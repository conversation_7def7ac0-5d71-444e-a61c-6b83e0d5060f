{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 71, "index": 71}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 72}, "end": {"line": 2, "column": 36, "index": 108}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "../../TouchEventType", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 109}, "end": {"line": 3, "column": 54, "index": 163}}], "key": "gRi5aG9FyEb9/VJwo00LvbUmVMU=", "exportNames": ["*"]}}, {"name": "../handlersRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 277}, "end": {"line": 9, "column": 73, "index": 350}}], "key": "ko+kACdA3Mfi5BxdyOfWCSrdI5A=", "exportNames": ["*"]}}, {"name": "./gestureStateManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 392}, "end": {"line": 14, "column": 31, "index": 482}}], "key": "43AIY0kxTL+QZqg6gpeYS2tWh3Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.onGestureHandlerEvent = onGestureHandlerEvent;\n  exports.startListening = startListening;\n  exports.stopListening = stopListening;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _State = require(_dependencyMap[1], \"../../State\");\n  var _TouchEventType = require(_dependencyMap[2], \"../../TouchEventType\");\n  var _handlersRegistry = require(_dependencyMap[3], \"../handlersRegistry\");\n  var _gestureStateManager = require(_dependencyMap[4], \"./gestureStateManager\");\n  var gestureHandlerEventSubscription = null;\n  var gestureHandlerStateChangeEventSubscription = null;\n  var gestureStateManagers = new Map();\n  var lastUpdateEvent = [];\n  function isStateChangeEvent(event) {\n    // @ts-ignore oldState doesn't exist on GestureTouchEvent and that's the point\n    return event.oldState != null;\n  }\n  function isTouchEvent(event) {\n    return event.eventType != null;\n  }\n  function onGestureHandlerEvent(event) {\n    var handler = (0, _handlersRegistry.findHandler)(event.handlerTag);\n    if (handler) {\n      if (isStateChangeEvent(event)) {\n        if (event.oldState === _State.State.UNDETERMINED && event.state === _State.State.BEGAN) {\n          handler.handlers.onBegin?.(event);\n        } else if ((event.oldState === _State.State.BEGAN || event.oldState === _State.State.UNDETERMINED) && event.state === _State.State.ACTIVE) {\n          handler.handlers.onStart?.(event);\n          lastUpdateEvent[handler.handlers.handlerTag] = event;\n        } else if (event.oldState !== event.state && event.state === _State.State.END) {\n          if (event.oldState === _State.State.ACTIVE) {\n            handler.handlers.onEnd?.(event, true);\n          }\n          handler.handlers.onFinalize?.(event, true);\n          lastUpdateEvent[handler.handlers.handlerTag] = undefined;\n        } else if ((event.state === _State.State.FAILED || event.state === _State.State.CANCELLED) && event.oldState !== event.state) {\n          if (event.oldState === _State.State.ACTIVE) {\n            handler.handlers.onEnd?.(event, false);\n          }\n          handler.handlers.onFinalize?.(event, false);\n          gestureStateManagers.delete(event.handlerTag);\n          lastUpdateEvent[handler.handlers.handlerTag] = undefined;\n        }\n      } else if (isTouchEvent(event)) {\n        if (!gestureStateManagers.has(event.handlerTag)) {\n          gestureStateManagers.set(event.handlerTag, _gestureStateManager.GestureStateManager.create(event.handlerTag));\n        }\n\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        var manager = gestureStateManagers.get(event.handlerTag);\n        switch (event.eventType) {\n          case _TouchEventType.TouchEventType.TOUCHES_DOWN:\n            handler.handlers?.onTouchesDown?.(event, manager);\n            break;\n          case _TouchEventType.TouchEventType.TOUCHES_MOVE:\n            handler.handlers?.onTouchesMove?.(event, manager);\n            break;\n          case _TouchEventType.TouchEventType.TOUCHES_UP:\n            handler.handlers?.onTouchesUp?.(event, manager);\n            break;\n          case _TouchEventType.TouchEventType.TOUCHES_CANCELLED:\n            handler.handlers?.onTouchesCancelled?.(event, manager);\n            break;\n        }\n      } else {\n        handler.handlers.onUpdate?.(event);\n        if (handler.handlers.onChange && handler.handlers.changeEventCalculator) {\n          handler.handlers.onChange?.(handler.handlers.changeEventCalculator?.(event, lastUpdateEvent[handler.handlers.handlerTag]));\n          lastUpdateEvent[handler.handlers.handlerTag] = event;\n        }\n      }\n    } else {\n      var oldHandler = (0, _handlersRegistry.findOldGestureHandler)(event.handlerTag);\n      if (oldHandler) {\n        var nativeEvent = {\n          nativeEvent: event\n        };\n        if (isStateChangeEvent(event)) {\n          oldHandler.onGestureStateChange(nativeEvent);\n        } else {\n          oldHandler.onGestureEvent(nativeEvent);\n        }\n        return;\n      }\n    }\n  }\n  function startListening() {\n    stopListening();\n    gestureHandlerEventSubscription = _reactNative.DeviceEventEmitter.addListener('onGestureHandlerEvent', onGestureHandlerEvent);\n    gestureHandlerStateChangeEventSubscription = _reactNative.DeviceEventEmitter.addListener('onGestureHandlerStateChange', onGestureHandlerEvent);\n  }\n  function stopListening() {\n    if (gestureHandlerEventSubscription) {\n      gestureHandlerEventSubscription.remove();\n      gestureHandlerEventSubscription = null;\n    }\n    if (gestureHandlerStateChangeEventSubscription) {\n      gestureHandlerStateChangeEventSubscription.remove();\n      gestureHandlerStateChangeEventSubscription = null;\n    }\n  }\n});", "lineCount": 105, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_reactNative"], [8, 18, 1, 0], [8, 21, 1, 0, "require"], [8, 28, 1, 0], [8, 29, 1, 0, "_dependencyMap"], [8, 43, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_State"], [9, 12, 2, 0], [9, 15, 2, 0, "require"], [9, 22, 2, 0], [9, 23, 2, 0, "_dependencyMap"], [9, 37, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_TouchEventType"], [10, 21, 3, 0], [10, 24, 3, 0, "require"], [10, 31, 3, 0], [10, 32, 3, 0, "_dependencyMap"], [10, 46, 3, 0], [11, 2, 9, 0], [11, 6, 9, 0, "_handlersRegistry"], [11, 23, 9, 0], [11, 26, 9, 0, "require"], [11, 33, 9, 0], [11, 34, 9, 0, "_dependencyMap"], [11, 48, 9, 0], [12, 2, 11, 0], [12, 6, 11, 0, "_gestureStateManager"], [12, 26, 11, 0], [12, 29, 11, 0, "require"], [12, 36, 11, 0], [12, 37, 11, 0, "_dependencyMap"], [12, 51, 11, 0], [13, 2, 16, 0], [13, 6, 16, 4, "gestureHandlerEventSubscription"], [13, 37, 16, 63], [13, 40, 16, 66], [13, 44, 16, 70], [14, 2, 17, 0], [14, 6, 17, 4, "gestureHandlerStateChangeEventSubscription"], [14, 48, 17, 74], [14, 51, 18, 2], [14, 55, 18, 6], [15, 2, 20, 0], [15, 6, 20, 6, "gestureStateManagers"], [15, 26, 20, 64], [15, 29, 20, 67], [15, 33, 20, 71, "Map"], [15, 36, 20, 74], [15, 37, 23, 2], [15, 38, 23, 3], [16, 2, 25, 0], [16, 6, 25, 6, "lastUpdateEvent"], [16, 21, 25, 57], [16, 24, 25, 60], [16, 26, 25, 62], [17, 2, 27, 0], [17, 11, 27, 9, "isStateChangeEvent"], [17, 29, 27, 27, "isStateChangeEvent"], [17, 30, 28, 2, "event"], [17, 35, 28, 73], [17, 37, 29, 36], [18, 4, 30, 2], [19, 4, 31, 2], [19, 11, 31, 9, "event"], [19, 16, 31, 14], [19, 17, 31, 15, "oldState"], [19, 25, 31, 23], [19, 29, 31, 27], [19, 33, 31, 31], [20, 2, 32, 0], [21, 2, 34, 0], [21, 11, 34, 9, "isTouchEvent"], [21, 23, 34, 21, "isTouchEvent"], [21, 24, 35, 2, "event"], [21, 29, 35, 73], [21, 31, 36, 30], [22, 4, 37, 2], [22, 11, 37, 9, "event"], [22, 16, 37, 14], [22, 17, 37, 15, "eventType"], [22, 26, 37, 24], [22, 30, 37, 28], [22, 34, 37, 32], [23, 2, 38, 0], [24, 2, 40, 7], [24, 11, 40, 16, "onGestureHandlerEvent"], [24, 32, 40, 37, "onGestureHandlerEvent"], [24, 33, 41, 2, "event"], [24, 38, 41, 73], [24, 40, 42, 2], [25, 4, 43, 2], [25, 8, 43, 8, "handler"], [25, 15, 43, 15], [25, 18, 43, 18], [25, 22, 43, 18, "<PERSON><PERSON><PERSON><PERSON>"], [25, 51, 43, 29], [25, 53, 43, 30, "event"], [25, 58, 43, 35], [25, 59, 43, 36, "handlerTag"], [25, 69, 43, 46], [25, 70, 45, 3], [26, 4, 47, 2], [26, 8, 47, 6, "handler"], [26, 15, 47, 13], [26, 17, 47, 15], [27, 6, 48, 4], [27, 10, 48, 8, "isStateChangeEvent"], [27, 28, 48, 26], [27, 29, 48, 27, "event"], [27, 34, 48, 32], [27, 35, 48, 33], [27, 37, 48, 35], [28, 8, 49, 6], [28, 12, 50, 8, "event"], [28, 17, 50, 13], [28, 18, 50, 14, "oldState"], [28, 26, 50, 22], [28, 31, 50, 27, "State"], [28, 43, 50, 32], [28, 44, 50, 33, "UNDETERMINED"], [28, 56, 50, 45], [28, 60, 51, 8, "event"], [28, 65, 51, 13], [28, 66, 51, 14, "state"], [28, 71, 51, 19], [28, 76, 51, 24, "State"], [28, 88, 51, 29], [28, 89, 51, 30, "BEGAN"], [28, 94, 51, 35], [28, 96, 52, 8], [29, 10, 53, 8, "handler"], [29, 17, 53, 15], [29, 18, 53, 16, "handlers"], [29, 26, 53, 24], [29, 27, 53, 25, "onBegin"], [29, 34, 53, 32], [29, 37, 53, 35, "event"], [29, 42, 53, 40], [29, 43, 53, 41], [30, 8, 54, 6], [30, 9, 54, 7], [30, 15, 54, 13], [30, 19, 55, 8], [30, 20, 55, 9, "event"], [30, 25, 55, 14], [30, 26, 55, 15, "oldState"], [30, 34, 55, 23], [30, 39, 55, 28, "State"], [30, 51, 55, 33], [30, 52, 55, 34, "BEGAN"], [30, 57, 55, 39], [30, 61, 56, 10, "event"], [30, 66, 56, 15], [30, 67, 56, 16, "oldState"], [30, 75, 56, 24], [30, 80, 56, 29, "State"], [30, 92, 56, 34], [30, 93, 56, 35, "UNDETERMINED"], [30, 105, 56, 47], [30, 110, 57, 8, "event"], [30, 115, 57, 13], [30, 116, 57, 14, "state"], [30, 121, 57, 19], [30, 126, 57, 24, "State"], [30, 138, 57, 29], [30, 139, 57, 30, "ACTIVE"], [30, 145, 57, 36], [30, 147, 58, 8], [31, 10, 59, 8, "handler"], [31, 17, 59, 15], [31, 18, 59, 16, "handlers"], [31, 26, 59, 24], [31, 27, 59, 25, "onStart"], [31, 34, 59, 32], [31, 37, 59, 35, "event"], [31, 42, 59, 40], [31, 43, 59, 41], [32, 10, 60, 8, "lastUpdateEvent"], [32, 25, 60, 23], [32, 26, 60, 24, "handler"], [32, 33, 60, 31], [32, 34, 60, 32, "handlers"], [32, 42, 60, 40], [32, 43, 60, 41, "handlerTag"], [32, 53, 60, 51], [32, 54, 60, 52], [32, 57, 60, 55, "event"], [32, 62, 60, 60], [33, 8, 61, 6], [33, 9, 61, 7], [33, 15, 61, 13], [33, 19, 61, 17, "event"], [33, 24, 61, 22], [33, 25, 61, 23, "oldState"], [33, 33, 61, 31], [33, 38, 61, 36, "event"], [33, 43, 61, 41], [33, 44, 61, 42, "state"], [33, 49, 61, 47], [33, 53, 61, 51, "event"], [33, 58, 61, 56], [33, 59, 61, 57, "state"], [33, 64, 61, 62], [33, 69, 61, 67, "State"], [33, 81, 61, 72], [33, 82, 61, 73, "END"], [33, 85, 61, 76], [33, 87, 61, 78], [34, 10, 62, 8], [34, 14, 62, 12, "event"], [34, 19, 62, 17], [34, 20, 62, 18, "oldState"], [34, 28, 62, 26], [34, 33, 62, 31, "State"], [34, 45, 62, 36], [34, 46, 62, 37, "ACTIVE"], [34, 52, 62, 43], [34, 54, 62, 45], [35, 12, 63, 10, "handler"], [35, 19, 63, 17], [35, 20, 63, 18, "handlers"], [35, 28, 63, 26], [35, 29, 63, 27, "onEnd"], [35, 34, 63, 32], [35, 37, 63, 35, "event"], [35, 42, 63, 40], [35, 44, 63, 42], [35, 48, 63, 46], [35, 49, 63, 47], [36, 10, 64, 8], [37, 10, 65, 8, "handler"], [37, 17, 65, 15], [37, 18, 65, 16, "handlers"], [37, 26, 65, 24], [37, 27, 65, 25, "onFinalize"], [37, 37, 65, 35], [37, 40, 65, 38, "event"], [37, 45, 65, 43], [37, 47, 65, 45], [37, 51, 65, 49], [37, 52, 65, 50], [38, 10, 66, 8, "lastUpdateEvent"], [38, 25, 66, 23], [38, 26, 66, 24, "handler"], [38, 33, 66, 31], [38, 34, 66, 32, "handlers"], [38, 42, 66, 40], [38, 43, 66, 41, "handlerTag"], [38, 53, 66, 51], [38, 54, 66, 52], [38, 57, 66, 55, "undefined"], [38, 66, 66, 64], [39, 8, 67, 6], [39, 9, 67, 7], [39, 15, 67, 13], [39, 19, 68, 8], [39, 20, 68, 9, "event"], [39, 25, 68, 14], [39, 26, 68, 15, "state"], [39, 31, 68, 20], [39, 36, 68, 25, "State"], [39, 48, 68, 30], [39, 49, 68, 31, "FAILED"], [39, 55, 68, 37], [39, 59, 68, 41, "event"], [39, 64, 68, 46], [39, 65, 68, 47, "state"], [39, 70, 68, 52], [39, 75, 68, 57, "State"], [39, 87, 68, 62], [39, 88, 68, 63, "CANCELLED"], [39, 97, 68, 72], [39, 102, 69, 8, "event"], [39, 107, 69, 13], [39, 108, 69, 14, "oldState"], [39, 116, 69, 22], [39, 121, 69, 27, "event"], [39, 126, 69, 32], [39, 127, 69, 33, "state"], [39, 132, 69, 38], [39, 134, 70, 8], [40, 10, 71, 8], [40, 14, 71, 12, "event"], [40, 19, 71, 17], [40, 20, 71, 18, "oldState"], [40, 28, 71, 26], [40, 33, 71, 31, "State"], [40, 45, 71, 36], [40, 46, 71, 37, "ACTIVE"], [40, 52, 71, 43], [40, 54, 71, 45], [41, 12, 72, 10, "handler"], [41, 19, 72, 17], [41, 20, 72, 18, "handlers"], [41, 28, 72, 26], [41, 29, 72, 27, "onEnd"], [41, 34, 72, 32], [41, 37, 72, 35, "event"], [41, 42, 72, 40], [41, 44, 72, 42], [41, 49, 72, 47], [41, 50, 72, 48], [42, 10, 73, 8], [43, 10, 74, 8, "handler"], [43, 17, 74, 15], [43, 18, 74, 16, "handlers"], [43, 26, 74, 24], [43, 27, 74, 25, "onFinalize"], [43, 37, 74, 35], [43, 40, 74, 38, "event"], [43, 45, 74, 43], [43, 47, 74, 45], [43, 52, 74, 50], [43, 53, 74, 51], [44, 10, 75, 8, "gestureStateManagers"], [44, 30, 75, 28], [44, 31, 75, 29, "delete"], [44, 37, 75, 35], [44, 38, 75, 36, "event"], [44, 43, 75, 41], [44, 44, 75, 42, "handlerTag"], [44, 54, 75, 52], [44, 55, 75, 53], [45, 10, 76, 8, "lastUpdateEvent"], [45, 25, 76, 23], [45, 26, 76, 24, "handler"], [45, 33, 76, 31], [45, 34, 76, 32, "handlers"], [45, 42, 76, 40], [45, 43, 76, 41, "handlerTag"], [45, 53, 76, 51], [45, 54, 76, 52], [45, 57, 76, 55, "undefined"], [45, 66, 76, 64], [46, 8, 77, 6], [47, 6, 78, 4], [47, 7, 78, 5], [47, 13, 78, 11], [47, 17, 78, 15, "isTouchEvent"], [47, 29, 78, 27], [47, 30, 78, 28, "event"], [47, 35, 78, 33], [47, 36, 78, 34], [47, 38, 78, 36], [48, 8, 79, 6], [48, 12, 79, 10], [48, 13, 79, 11, "gestureStateManagers"], [48, 33, 79, 31], [48, 34, 79, 32, "has"], [48, 37, 79, 35], [48, 38, 79, 36, "event"], [48, 43, 79, 41], [48, 44, 79, 42, "handlerTag"], [48, 54, 79, 52], [48, 55, 79, 53], [48, 57, 79, 55], [49, 10, 80, 8, "gestureStateManagers"], [49, 30, 80, 28], [49, 31, 80, 29, "set"], [49, 34, 80, 32], [49, 35, 81, 10, "event"], [49, 40, 81, 15], [49, 41, 81, 16, "handlerTag"], [49, 51, 81, 26], [49, 53, 82, 10, "GestureStateManager"], [49, 93, 82, 29], [49, 94, 82, 30, "create"], [49, 100, 82, 36], [49, 101, 82, 37, "event"], [49, 106, 82, 42], [49, 107, 82, 43, "handlerTag"], [49, 117, 82, 53], [49, 118, 83, 8], [49, 119, 83, 9], [50, 8, 84, 6], [52, 8, 86, 6], [53, 8, 87, 6], [53, 12, 87, 12, "manager"], [53, 19, 87, 19], [53, 22, 87, 22, "gestureStateManagers"], [53, 42, 87, 42], [53, 43, 87, 43, "get"], [53, 46, 87, 46], [53, 47, 87, 47, "event"], [53, 52, 87, 52], [53, 53, 87, 53, "handlerTag"], [53, 63, 87, 63], [53, 64, 87, 65], [54, 8, 89, 6], [54, 16, 89, 14, "event"], [54, 21, 89, 19], [54, 22, 89, 20, "eventType"], [54, 31, 89, 29], [55, 10, 90, 8], [55, 15, 90, 13, "TouchEventType"], [55, 45, 90, 27], [55, 46, 90, 28, "TOUCHES_DOWN"], [55, 58, 90, 40], [56, 12, 91, 10, "handler"], [56, 19, 91, 17], [56, 20, 91, 18, "handlers"], [56, 28, 91, 26], [56, 30, 91, 28, "onTouchesDown"], [56, 43, 91, 41], [56, 46, 91, 44, "event"], [56, 51, 91, 49], [56, 53, 91, 51, "manager"], [56, 60, 91, 58], [56, 61, 91, 59], [57, 12, 92, 10], [58, 10, 93, 8], [58, 15, 93, 13, "TouchEventType"], [58, 45, 93, 27], [58, 46, 93, 28, "TOUCHES_MOVE"], [58, 58, 93, 40], [59, 12, 94, 10, "handler"], [59, 19, 94, 17], [59, 20, 94, 18, "handlers"], [59, 28, 94, 26], [59, 30, 94, 28, "onTouchesMove"], [59, 43, 94, 41], [59, 46, 94, 44, "event"], [59, 51, 94, 49], [59, 53, 94, 51, "manager"], [59, 60, 94, 58], [59, 61, 94, 59], [60, 12, 95, 10], [61, 10, 96, 8], [61, 15, 96, 13, "TouchEventType"], [61, 45, 96, 27], [61, 46, 96, 28, "TOUCHES_UP"], [61, 56, 96, 38], [62, 12, 97, 10, "handler"], [62, 19, 97, 17], [62, 20, 97, 18, "handlers"], [62, 28, 97, 26], [62, 30, 97, 28, "onTouchesUp"], [62, 41, 97, 39], [62, 44, 97, 42, "event"], [62, 49, 97, 47], [62, 51, 97, 49, "manager"], [62, 58, 97, 56], [62, 59, 97, 57], [63, 12, 98, 10], [64, 10, 99, 8], [64, 15, 99, 13, "TouchEventType"], [64, 45, 99, 27], [64, 46, 99, 28, "TOUCHES_CANCELLED"], [64, 63, 99, 45], [65, 12, 100, 10, "handler"], [65, 19, 100, 17], [65, 20, 100, 18, "handlers"], [65, 28, 100, 26], [65, 30, 100, 28, "onTouchesCancelled"], [65, 48, 100, 46], [65, 51, 100, 49, "event"], [65, 56, 100, 54], [65, 58, 100, 56, "manager"], [65, 65, 100, 63], [65, 66, 100, 64], [66, 12, 101, 10], [67, 8, 102, 6], [68, 6, 103, 4], [68, 7, 103, 5], [68, 13, 103, 11], [69, 8, 104, 6, "handler"], [69, 15, 104, 13], [69, 16, 104, 14, "handlers"], [69, 24, 104, 22], [69, 25, 104, 23, "onUpdate"], [69, 33, 104, 31], [69, 36, 104, 34, "event"], [69, 41, 104, 39], [69, 42, 104, 40], [70, 8, 106, 6], [70, 12, 106, 10, "handler"], [70, 19, 106, 17], [70, 20, 106, 18, "handlers"], [70, 28, 106, 26], [70, 29, 106, 27, "onChange"], [70, 37, 106, 35], [70, 41, 106, 39, "handler"], [70, 48, 106, 46], [70, 49, 106, 47, "handlers"], [70, 57, 106, 55], [70, 58, 106, 56, "changeEventCalculator"], [70, 79, 106, 77], [70, 81, 106, 79], [71, 10, 107, 8, "handler"], [71, 17, 107, 15], [71, 18, 107, 16, "handlers"], [71, 26, 107, 24], [71, 27, 107, 25, "onChange"], [71, 35, 107, 33], [71, 38, 108, 10, "handler"], [71, 45, 108, 17], [71, 46, 108, 18, "handlers"], [71, 54, 108, 26], [71, 55, 108, 27, "changeEventCalculator"], [71, 76, 108, 48], [71, 79, 109, 12, "event"], [71, 84, 109, 17], [71, 86, 110, 12, "lastUpdateEvent"], [71, 101, 110, 27], [71, 102, 110, 28, "handler"], [71, 109, 110, 35], [71, 110, 110, 36, "handlers"], [71, 118, 110, 44], [71, 119, 110, 45, "handlerTag"], [71, 129, 110, 55], [71, 130, 111, 10], [71, 131, 112, 8], [71, 132, 112, 9], [72, 10, 114, 8, "lastUpdateEvent"], [72, 25, 114, 23], [72, 26, 114, 24, "handler"], [72, 33, 114, 31], [72, 34, 114, 32, "handlers"], [72, 42, 114, 40], [72, 43, 114, 41, "handlerTag"], [72, 53, 114, 51], [72, 54, 114, 52], [72, 57, 114, 55, "event"], [72, 62, 114, 60], [73, 8, 115, 6], [74, 6, 116, 4], [75, 4, 117, 2], [75, 5, 117, 3], [75, 11, 117, 9], [76, 6, 118, 4], [76, 10, 118, 10, "<PERSON><PERSON><PERSON><PERSON>"], [76, 20, 118, 20], [76, 23, 118, 23], [76, 27, 118, 23, "findOldGestureHandler"], [76, 66, 118, 44], [76, 68, 118, 45, "event"], [76, 73, 118, 50], [76, 74, 118, 51, "handlerTag"], [76, 84, 118, 61], [76, 85, 118, 62], [77, 6, 119, 4], [77, 10, 119, 8, "<PERSON><PERSON><PERSON><PERSON>"], [77, 20, 119, 18], [77, 22, 119, 20], [78, 8, 120, 6], [78, 12, 120, 12, "nativeEvent"], [78, 23, 120, 23], [78, 26, 120, 26], [79, 10, 120, 28, "nativeEvent"], [79, 21, 120, 39], [79, 23, 120, 41, "event"], [80, 8, 120, 47], [80, 9, 120, 48], [81, 8, 121, 6], [81, 12, 121, 10, "isStateChangeEvent"], [81, 30, 121, 28], [81, 31, 121, 29, "event"], [81, 36, 121, 34], [81, 37, 121, 35], [81, 39, 121, 37], [82, 10, 122, 8, "<PERSON><PERSON><PERSON><PERSON>"], [82, 20, 122, 18], [82, 21, 122, 19, "onGestureStateChange"], [82, 41, 122, 39], [82, 42, 122, 40, "nativeEvent"], [82, 53, 122, 51], [82, 54, 122, 52], [83, 8, 123, 6], [83, 9, 123, 7], [83, 15, 123, 13], [84, 10, 124, 8, "<PERSON><PERSON><PERSON><PERSON>"], [84, 20, 124, 18], [84, 21, 124, 19, "onGestureEvent"], [84, 35, 124, 33], [84, 36, 124, 34, "nativeEvent"], [84, 47, 124, 45], [84, 48, 124, 46], [85, 8, 125, 6], [86, 8, 126, 6], [87, 6, 127, 4], [88, 4, 128, 2], [89, 2, 129, 0], [90, 2, 131, 7], [90, 11, 131, 16, "startListening"], [90, 25, 131, 30, "startListening"], [90, 26, 131, 30], [90, 28, 131, 33], [91, 4, 132, 2, "stopListening"], [91, 17, 132, 15], [91, 18, 132, 16], [91, 19, 132, 17], [92, 4, 134, 2, "gestureHandlerEventSubscription"], [92, 35, 134, 33], [92, 38, 134, 36, "DeviceEventEmitter"], [92, 69, 134, 54], [92, 70, 134, 55, "addListener"], [92, 81, 134, 66], [92, 82, 135, 4], [92, 105, 135, 27], [92, 107, 136, 4, "onGestureHandlerEvent"], [92, 128, 137, 2], [92, 129, 137, 3], [93, 4, 139, 2, "gestureHandlerStateChangeEventSubscription"], [93, 46, 139, 44], [93, 49, 139, 47, "DeviceEventEmitter"], [93, 80, 139, 65], [93, 81, 139, 66, "addListener"], [93, 92, 139, 77], [93, 93, 140, 4], [93, 122, 140, 33], [93, 124, 141, 4, "onGestureHandlerEvent"], [93, 145, 142, 2], [93, 146, 142, 3], [94, 2, 143, 0], [95, 2, 145, 7], [95, 11, 145, 16, "stopListening"], [95, 24, 145, 29, "stopListening"], [95, 25, 145, 29], [95, 27, 145, 32], [96, 4, 146, 2], [96, 8, 146, 6, "gestureHandlerEventSubscription"], [96, 39, 146, 37], [96, 41, 146, 39], [97, 6, 147, 4, "gestureHandlerEventSubscription"], [97, 37, 147, 35], [97, 38, 147, 36, "remove"], [97, 44, 147, 42], [97, 45, 147, 43], [97, 46, 147, 44], [98, 6, 148, 4, "gestureHandlerEventSubscription"], [98, 37, 148, 35], [98, 40, 148, 38], [98, 44, 148, 42], [99, 4, 149, 2], [100, 4, 151, 2], [100, 8, 151, 6, "gestureHandlerStateChangeEventSubscription"], [100, 50, 151, 48], [100, 52, 151, 50], [101, 6, 152, 4, "gestureHandlerStateChangeEventSubscription"], [101, 48, 152, 46], [101, 49, 152, 47, "remove"], [101, 55, 152, 53], [101, 56, 152, 54], [101, 57, 152, 55], [102, 6, 153, 4, "gestureHandlerStateChangeEventSubscription"], [102, 48, 153, 46], [102, 51, 153, 49], [102, 55, 153, 53], [103, 4, 154, 2], [104, 2, 155, 0], [105, 0, 155, 1], [105, 3]], "functionMap": {"names": ["<global>", "isStateChangeEvent", "isTouchEvent", "onGestureHandlerEvent", "startListening", "stopListening"], "mappings": "AAA;AC0B;CDK;AEE;CFI;OGE;CHyF;OIE;CJY;OKE;CLU"}}, "type": "js/module"}]}