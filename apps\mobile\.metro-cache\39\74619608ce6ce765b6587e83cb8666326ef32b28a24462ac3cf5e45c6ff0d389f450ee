{"dependencies": [{"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 209}, "end": {"line": 10, "column": 35, "index": 244}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 245}, "end": {"line": 16, "column": 28, "index": 342}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.dispatchCommand = void 0;\n  var _logger = require(_dependencyMap[0], \"../logger\");\n  var _PlatformChecker = require(_dependencyMap[1], \"../PlatformChecker\");\n  /**\n   * Lets you synchronously call a command of a native component.\n   *\n   * @param animatedRef - An [animated\n   *   ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef#returns)\n   *   connected to the component you'd want to call the command on.\n   * @param commandName - The name of the command to dispatch (e.g. `\"focus\"` or\n   *   `\"scrollToEnd\"`).\n   * @param args - An optional array of arguments for the command.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/dispatchCommand\n   */\n  var dispatchCommand;\n  var _worklet_13960213792158_init_data = {\n    code: \"function dispatchCommandFabric_dispatchCommandTs1(animatedRef,commandName,args=[]){if(!_WORKLET){return;}const shadowNodeWrapper=animatedRef();global._dispatchCommandFabric(shadowNodeWrapper,commandName,args);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\platformFunctions\\\\dispatchCommand.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"dispatchCommandFabric_dispatchCommandTs1\\\",\\\"animatedRef\\\",\\\"commandName\\\",\\\"args\\\",\\\"_WORKLET\\\",\\\"shadowNodeWrapper\\\",\\\"global\\\",\\\"_dispatchCommandFabric\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/platformFunctions/dispatchCommand.ts\\\"],\\\"mappings\\\":\\\"AAoCA,SAAAA,wCAEEA,CAAAC,WACA,CAAoBC,WACpB,CAAAC,IAAA,KAEA,GAAI,CAACC,QAAQ,CAAE,CACb,OACF,CAEA,KAAM,CAAAC,iBAAiB,CAAGJ,WAAW,CAAC,CAAsB,CAC5DK,MAAM,CAACC,sBAAsB,CAAEF,iBAAiB,CAAEH,WAAW,CAAEC,IAAI,CAAC,CACtE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var dispatchCommandFabric = function () {\n    var _e = [new global.Error(), 1, -27];\n    var dispatchCommandFabric = function (animatedRef, commandName) {\n      var args = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n      if (!_WORKLET) {\n        return;\n      }\n      var shadowNodeWrapper = animatedRef();\n      global._dispatchCommandFabric(shadowNodeWrapper, commandName, args);\n    };\n    dispatchCommandFabric.__closure = {};\n    dispatchCommandFabric.__workletHash = 13960213792158;\n    dispatchCommandFabric.__initData = _worklet_13960213792158_init_data;\n    dispatchCommandFabric.__stackDetails = _e;\n    return dispatchCommandFabric;\n  }();\n  var _worklet_12364817925533_init_data = {\n    code: \"function dispatchCommandPaper_dispatchCommandTs2(animatedRef,commandName,args=[]){if(!_WORKLET){return;}const viewTag=animatedRef();global._dispatchCommandPaper(viewTag,commandName,args);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\platformFunctions\\\\dispatchCommand.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"dispatchCommandPaper_dispatchCommandTs2\\\",\\\"animatedRef\\\",\\\"commandName\\\",\\\"args\\\",\\\"_WORKLET\\\",\\\"viewTag\\\",\\\"global\\\",\\\"_dispatchCommandPaper\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/platformFunctions/dispatchCommand.ts\\\"],\\\"mappings\\\":\\\"AAkDA,SAAAA,uCAEEA,CAAAC,WACA,CAAoBC,WACpB,CAAAC,IAAA,KAEA,GAAI,CAACC,QAAQ,CAAE,CACb,OACF,CAEA,KAAM,CAAAC,OAAO,CAAGJ,WAAW,CAAC,CAAW,CACvCK,MAAM,CAACC,qBAAqB,CAAEF,OAAO,CAAEH,WAAW,CAAEC,IAAI,CAAC,CAC3D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var dispatchCommandPaper = function () {\n    var _e = [new global.Error(), 1, -27];\n    var dispatchCommandPaper = function (animatedRef, commandName) {\n      var args = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n      if (!_WORKLET) {\n        return;\n      }\n      var viewTag = animatedRef();\n      global._dispatchCommandPaper(viewTag, commandName, args);\n    };\n    dispatchCommandPaper.__closure = {};\n    dispatchCommandPaper.__workletHash = 12364817925533;\n    dispatchCommandPaper.__initData = _worklet_12364817925533_init_data;\n    dispatchCommandPaper.__stackDetails = _e;\n    return dispatchCommandPaper;\n  }();\n  function dispatchCommandJest() {\n    _logger.logger.warn('dispatchCommand() is not supported with Jest.');\n  }\n  function dispatchCommandChromeDebugger() {\n    _logger.logger.warn('dispatchCommand() is not supported with Chrome Debugger.');\n  }\n  function dispatchCommandDefault() {\n    _logger.logger.warn('dispatchCommand() is not supported on this configuration.');\n  }\n  if (!(0, _PlatformChecker.shouldBeUseWeb)()) {\n    // Those assertions are actually correct since on Native platforms `AnimatedRef` is\n    // mapped as a different function in `shareableMappingCache` and\n    // TypeScript is not able to infer that.\n    if ((0, _PlatformChecker.isFabric)()) {\n      exports.dispatchCommand = dispatchCommand = dispatchCommandFabric;\n    } else {\n      exports.dispatchCommand = dispatchCommand = dispatchCommandPaper;\n    }\n  } else if ((0, _PlatformChecker.isJest)()) {\n    exports.dispatchCommand = dispatchCommand = dispatchCommandJest;\n  } else if ((0, _PlatformChecker.isChromeDebugger)()) {\n    exports.dispatchCommand = dispatchCommand = dispatchCommandChromeDebugger;\n  } else {\n    exports.dispatchCommand = dispatchCommand = dispatchCommandDefault;\n  }\n});", "lineCount": 91, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "dispatchCommand"], [7, 25, 1, 13], [8, 2, 10, 0], [8, 6, 10, 0, "_logger"], [8, 13, 10, 0], [8, 16, 10, 0, "require"], [8, 23, 10, 0], [8, 24, 10, 0, "_dependencyMap"], [8, 38, 10, 0], [9, 2, 11, 0], [9, 6, 11, 0, "_PlatformChecker"], [9, 22, 11, 0], [9, 25, 11, 0, "require"], [9, 32, 11, 0], [9, 33, 11, 0, "_dependencyMap"], [9, 47, 11, 0], [10, 2, 24, 0], [11, 0, 25, 0], [12, 0, 26, 0], [13, 0, 27, 0], [14, 0, 28, 0], [15, 0, 29, 0], [16, 0, 30, 0], [17, 0, 31, 0], [18, 0, 32, 0], [19, 0, 33, 0], [20, 0, 34, 0], [21, 2, 35, 7], [21, 6, 35, 11, "dispatchCommand"], [21, 21, 35, 43], [22, 2, 35, 44], [22, 6, 35, 44, "_worklet_13960213792158_init_data"], [22, 39, 35, 44], [23, 4, 35, 44, "code"], [23, 8, 35, 44], [24, 4, 35, 44, "location"], [24, 12, 35, 44], [25, 4, 35, 44, "sourceMap"], [25, 13, 35, 44], [26, 4, 35, 44, "version"], [26, 11, 35, 44], [27, 2, 35, 44], [28, 2, 35, 44], [28, 6, 35, 44, "dispatchCommandFabric"], [28, 27, 35, 44], [28, 30, 37, 0], [29, 4, 37, 0], [29, 8, 37, 0, "_e"], [29, 10, 37, 0], [29, 18, 37, 0, "global"], [29, 24, 37, 0], [29, 25, 37, 0, "Error"], [29, 30, 37, 0], [30, 4, 37, 0], [30, 8, 37, 0, "dispatchCommandFabric"], [30, 29, 37, 0], [30, 41, 37, 0, "dispatchCommandFabric"], [30, 42, 38, 2, "animatedRef"], [30, 53, 38, 48], [30, 55, 39, 2, "commandName"], [30, 66, 39, 21], [30, 68, 41, 2], [31, 6, 41, 2], [31, 10, 40, 2, "args"], [31, 14, 40, 22], [31, 17, 40, 22, "arguments"], [31, 26, 40, 22], [31, 27, 40, 22, "length"], [31, 33, 40, 22], [31, 41, 40, 22, "arguments"], [31, 50, 40, 22], [31, 58, 40, 22, "undefined"], [31, 67, 40, 22], [31, 70, 40, 22, "arguments"], [31, 79, 40, 22], [31, 85, 40, 25], [31, 87, 40, 27], [32, 6, 43, 2], [32, 10, 43, 6], [32, 11, 43, 7, "_WORKLET"], [32, 19, 43, 15], [32, 21, 43, 17], [33, 8, 44, 4], [34, 6, 45, 2], [35, 6, 47, 2], [35, 10, 47, 8, "shadowNodeWrapper"], [35, 27, 47, 25], [35, 30, 47, 28, "animatedRef"], [35, 41, 47, 39], [35, 42, 47, 40], [35, 43, 47, 62], [36, 6, 48, 2, "global"], [36, 12, 48, 8], [36, 13, 48, 9, "_dispatchCommandFabric"], [36, 35, 48, 31], [36, 36, 48, 33, "shadowNodeWrapper"], [36, 53, 48, 50], [36, 55, 48, 52, "commandName"], [36, 66, 48, 63], [36, 68, 48, 65, "args"], [36, 72, 48, 69], [36, 73, 48, 70], [37, 4, 49, 0], [37, 5, 49, 1], [38, 4, 49, 1, "dispatchCommandFabric"], [38, 25, 49, 1], [38, 26, 49, 1, "__closure"], [38, 35, 49, 1], [39, 4, 49, 1, "dispatchCommandFabric"], [39, 25, 49, 1], [39, 26, 49, 1, "__workletHash"], [39, 39, 49, 1], [40, 4, 49, 1, "dispatchCommandFabric"], [40, 25, 49, 1], [40, 26, 49, 1, "__initData"], [40, 36, 49, 1], [40, 39, 49, 1, "_worklet_13960213792158_init_data"], [40, 72, 49, 1], [41, 4, 49, 1, "dispatchCommandFabric"], [41, 25, 49, 1], [41, 26, 49, 1, "__stackDetails"], [41, 40, 49, 1], [41, 43, 49, 1, "_e"], [41, 45, 49, 1], [42, 4, 49, 1], [42, 11, 49, 1, "dispatchCommandFabric"], [42, 32, 49, 1], [43, 2, 49, 1], [43, 3, 37, 0], [44, 2, 37, 0], [44, 6, 37, 0, "_worklet_12364817925533_init_data"], [44, 39, 37, 0], [45, 4, 37, 0, "code"], [45, 8, 37, 0], [46, 4, 37, 0, "location"], [46, 12, 37, 0], [47, 4, 37, 0, "sourceMap"], [47, 13, 37, 0], [48, 4, 37, 0, "version"], [48, 11, 37, 0], [49, 2, 37, 0], [50, 2, 37, 0], [50, 6, 37, 0, "dispatchCommandPaper"], [50, 26, 37, 0], [50, 29, 51, 0], [51, 4, 51, 0], [51, 8, 51, 0, "_e"], [51, 10, 51, 0], [51, 18, 51, 0, "global"], [51, 24, 51, 0], [51, 25, 51, 0, "Error"], [51, 30, 51, 0], [52, 4, 51, 0], [52, 8, 51, 0, "dispatchCommandPaper"], [52, 28, 51, 0], [52, 40, 51, 0, "dispatchCommandPaper"], [52, 41, 52, 2, "animatedRef"], [52, 52, 52, 48], [52, 54, 53, 2, "commandName"], [52, 65, 53, 21], [52, 67, 55, 2], [53, 6, 55, 2], [53, 10, 54, 2, "args"], [53, 14, 54, 22], [53, 17, 54, 22, "arguments"], [53, 26, 54, 22], [53, 27, 54, 22, "length"], [53, 33, 54, 22], [53, 41, 54, 22, "arguments"], [53, 50, 54, 22], [53, 58, 54, 22, "undefined"], [53, 67, 54, 22], [53, 70, 54, 22, "arguments"], [53, 79, 54, 22], [53, 85, 54, 25], [53, 87, 54, 27], [54, 6, 57, 2], [54, 10, 57, 6], [54, 11, 57, 7, "_WORKLET"], [54, 19, 57, 15], [54, 21, 57, 17], [55, 8, 58, 4], [56, 6, 59, 2], [57, 6, 61, 2], [57, 10, 61, 8, "viewTag"], [57, 17, 61, 15], [57, 20, 61, 18, "animatedRef"], [57, 31, 61, 29], [57, 32, 61, 30], [57, 33, 61, 41], [58, 6, 62, 2, "global"], [58, 12, 62, 8], [58, 13, 62, 9, "_dispatchCommandPaper"], [58, 34, 62, 30], [58, 35, 62, 32, "viewTag"], [58, 42, 62, 39], [58, 44, 62, 41, "commandName"], [58, 55, 62, 52], [58, 57, 62, 54, "args"], [58, 61, 62, 58], [58, 62, 62, 59], [59, 4, 63, 0], [59, 5, 63, 1], [60, 4, 63, 1, "dispatchCommandPaper"], [60, 24, 63, 1], [60, 25, 63, 1, "__closure"], [60, 34, 63, 1], [61, 4, 63, 1, "dispatchCommandPaper"], [61, 24, 63, 1], [61, 25, 63, 1, "__workletHash"], [61, 38, 63, 1], [62, 4, 63, 1, "dispatchCommandPaper"], [62, 24, 63, 1], [62, 25, 63, 1, "__initData"], [62, 35, 63, 1], [62, 38, 63, 1, "_worklet_12364817925533_init_data"], [62, 71, 63, 1], [63, 4, 63, 1, "dispatchCommandPaper"], [63, 24, 63, 1], [63, 25, 63, 1, "__stackDetails"], [63, 39, 63, 1], [63, 42, 63, 1, "_e"], [63, 44, 63, 1], [64, 4, 63, 1], [64, 11, 63, 1, "dispatchCommandPaper"], [64, 31, 63, 1], [65, 2, 63, 1], [65, 3, 51, 0], [66, 2, 65, 0], [66, 11, 65, 9, "dispatchCommandJest"], [66, 30, 65, 28, "dispatchCommandJest"], [66, 31, 65, 28], [66, 33, 65, 31], [67, 4, 66, 2, "logger"], [67, 18, 66, 8], [67, 19, 66, 9, "warn"], [67, 23, 66, 13], [67, 24, 66, 14], [67, 71, 66, 61], [67, 72, 66, 62], [68, 2, 67, 0], [69, 2, 69, 0], [69, 11, 69, 9, "dispatchCommandChromeDebugger"], [69, 40, 69, 38, "dispatchCommandChromeDebugger"], [69, 41, 69, 38], [69, 43, 69, 41], [70, 4, 70, 2, "logger"], [70, 18, 70, 8], [70, 19, 70, 9, "warn"], [70, 23, 70, 13], [70, 24, 70, 14], [70, 82, 70, 72], [70, 83, 70, 73], [71, 2, 71, 0], [72, 2, 73, 0], [72, 11, 73, 9, "dispatchCommandDefault"], [72, 33, 73, 31, "dispatchCommandDefault"], [72, 34, 73, 31], [72, 36, 73, 34], [73, 4, 74, 2, "logger"], [73, 18, 74, 8], [73, 19, 74, 9, "warn"], [73, 23, 74, 13], [73, 24, 74, 14], [73, 83, 74, 73], [73, 84, 74, 74], [74, 2, 75, 0], [75, 2, 77, 0], [75, 6, 77, 4], [75, 7, 77, 5], [75, 11, 77, 5, "shouldBeUseWeb"], [75, 42, 77, 19], [75, 44, 77, 20], [75, 45, 77, 21], [75, 47, 77, 23], [76, 4, 78, 2], [77, 4, 79, 2], [78, 4, 80, 2], [79, 4, 81, 2], [79, 8, 81, 6], [79, 12, 81, 6, "isF<PERSON><PERSON>"], [79, 37, 81, 14], [79, 39, 81, 15], [79, 40, 81, 16], [79, 42, 81, 18], [80, 6, 82, 4, "exports"], [80, 13, 82, 4], [80, 14, 82, 4, "dispatchCommand"], [80, 29, 82, 4], [80, 32, 82, 4, "dispatchCommand"], [80, 47, 82, 19], [80, 50, 82, 22, "dispatchCommandFabric"], [80, 71, 82, 73], [81, 4, 83, 2], [81, 5, 83, 3], [81, 11, 83, 9], [82, 6, 84, 4, "exports"], [82, 13, 84, 4], [82, 14, 84, 4, "dispatchCommand"], [82, 29, 84, 4], [82, 32, 84, 4, "dispatchCommand"], [82, 47, 84, 19], [82, 50, 84, 22, "dispatchCommandPaper"], [82, 70, 84, 72], [83, 4, 85, 2], [84, 2, 86, 0], [84, 3, 86, 1], [84, 9, 86, 7], [84, 13, 86, 11], [84, 17, 86, 11, "isJest"], [84, 40, 86, 17], [84, 42, 86, 18], [84, 43, 86, 19], [84, 45, 86, 21], [85, 4, 87, 2, "exports"], [85, 11, 87, 2], [85, 12, 87, 2, "dispatchCommand"], [85, 27, 87, 2], [85, 30, 87, 2, "dispatchCommand"], [85, 45, 87, 17], [85, 48, 87, 20, "dispatchCommandJest"], [85, 67, 87, 39], [86, 2, 88, 0], [86, 3, 88, 1], [86, 9, 88, 7], [86, 13, 88, 11], [86, 17, 88, 11, "isChromeDebugger"], [86, 50, 88, 27], [86, 52, 88, 28], [86, 53, 88, 29], [86, 55, 88, 31], [87, 4, 89, 2, "exports"], [87, 11, 89, 2], [87, 12, 89, 2, "dispatchCommand"], [87, 27, 89, 2], [87, 30, 89, 2, "dispatchCommand"], [87, 45, 89, 17], [87, 48, 89, 20, "dispatchCommandChromeDebugger"], [87, 77, 89, 49], [88, 2, 90, 0], [88, 3, 90, 1], [88, 9, 90, 7], [89, 4, 91, 2, "exports"], [89, 11, 91, 2], [89, 12, 91, 2, "dispatchCommand"], [89, 27, 91, 2], [89, 30, 91, 2, "dispatchCommand"], [89, 45, 91, 17], [89, 48, 91, 20, "dispatchCommandDefault"], [89, 70, 91, 42], [90, 2, 92, 0], [91, 0, 92, 1], [91, 3]], "functionMap": {"names": ["<global>", "dispatchCommandFabric", "dispatchCommandPaper", "dispatchCommandJest", "dispatchCommandChromeDebugger", "dispatchCommandDefault"], "mappings": "AAA;ACoC;CDY;AEE;CFY;AGE;CHE;AIE;CJE;AKE;CLE"}}, "type": "js/module"}]}