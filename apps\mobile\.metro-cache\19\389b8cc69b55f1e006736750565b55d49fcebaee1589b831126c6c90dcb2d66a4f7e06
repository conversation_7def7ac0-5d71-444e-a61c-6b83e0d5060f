{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 22, "column": 0, "index": 629}, "end": {"line": 22, "column": 77, "index": 706}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 22, "column": 0, "index": 629}, "end": {"line": 22, "column": 77, "index": 706}}, {"start": {"line": 22, "column": 0, "index": 629}, "end": {"line": 22, "column": 77, "index": 706}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var nativeComponentName = 'RNGestureHandlerButton';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNGestureHandlerButton\",\n    validAttributes: {\n      exclusive: true,\n      foreground: true,\n      borderless: true,\n      enabled: true,\n      rippleColor: {\n        process: require(_dependencyMap[3], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      rippleRadius: true,\n      touchSoundDisabled: true,\n      borderWidth: true,\n      borderColor: {\n        process: require(_dependencyMap[3], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      borderStyle: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 30, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 22, 0], [8, 6, 22, 0, "NativeComponentRegistry"], [8, 29, 22, 77], [8, 32, 22, 0, "require"], [8, 39, 22, 77], [8, 40, 22, 77, "_dependencyMap"], [8, 54, 22, 77], [8, 123, 22, 76], [8, 124, 22, 77], [9, 2, 22, 0], [9, 6, 22, 0, "nativeComponentName"], [9, 25, 22, 77], [9, 28, 22, 0], [9, 52, 22, 77], [10, 2, 22, 0], [10, 6, 22, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 22, 77], [10, 31, 22, 77, "exports"], [10, 38, 22, 77], [10, 39, 22, 77, "__INTERNAL_VIEW_CONFIG"], [10, 61, 22, 77], [10, 64, 22, 0], [11, 4, 22, 0, "uiViewClassName"], [11, 19, 22, 77], [11, 21, 22, 0], [11, 45, 22, 77], [12, 4, 22, 0, "validAttributes"], [12, 19, 22, 77], [12, 21, 22, 0], [13, 6, 22, 0, "exclusive"], [13, 15, 22, 77], [13, 17, 22, 0], [13, 21, 22, 77], [14, 6, 22, 0, "foreground"], [14, 16, 22, 77], [14, 18, 22, 0], [14, 22, 22, 77], [15, 6, 22, 0, "borderless"], [15, 16, 22, 77], [15, 18, 22, 0], [15, 22, 22, 77], [16, 6, 22, 0, "enabled"], [16, 13, 22, 77], [16, 15, 22, 0], [16, 19, 22, 77], [17, 6, 22, 0, "rippleColor"], [17, 17, 22, 77], [17, 19, 22, 0], [18, 8, 22, 0, "process"], [18, 15, 22, 77], [18, 17, 22, 0, "require"], [18, 24, 22, 77], [18, 25, 22, 77, "_dependencyMap"], [18, 39, 22, 77], [18, 92, 22, 76], [18, 93, 22, 77], [18, 94, 22, 0, "default"], [19, 6, 22, 76], [19, 7, 22, 77], [20, 6, 22, 0, "rippleRadius"], [20, 18, 22, 77], [20, 20, 22, 0], [20, 24, 22, 77], [21, 6, 22, 0, "touchSoundDisabled"], [21, 24, 22, 77], [21, 26, 22, 0], [21, 30, 22, 77], [22, 6, 22, 0, "borderWidth"], [22, 17, 22, 77], [22, 19, 22, 0], [22, 23, 22, 77], [23, 6, 22, 0, "borderColor"], [23, 17, 22, 77], [23, 19, 22, 0], [24, 8, 22, 0, "process"], [24, 15, 22, 77], [24, 17, 22, 0, "require"], [24, 24, 22, 77], [24, 25, 22, 77, "_dependencyMap"], [24, 39, 22, 77], [24, 92, 22, 76], [24, 93, 22, 77], [24, 94, 22, 0, "default"], [25, 6, 22, 76], [25, 7, 22, 77], [26, 6, 22, 0, "borderStyle"], [26, 17, 22, 77], [26, 19, 22, 0], [27, 4, 22, 76], [28, 2, 22, 76], [28, 3, 22, 77], [29, 2, 22, 77], [29, 6, 22, 77, "_default"], [29, 14, 22, 77], [29, 17, 22, 77, "exports"], [29, 24, 22, 77], [29, 25, 22, 77, "default"], [29, 32, 22, 77], [29, 35, 22, 0, "NativeComponentRegistry"], [29, 58, 22, 77], [29, 59, 22, 0, "get"], [29, 62, 22, 77], [29, 63, 22, 0, "nativeComponentName"], [29, 82, 22, 77], [29, 84, 22, 0], [29, 90, 22, 0, "__INTERNAL_VIEW_CONFIG"], [29, 112, 22, 76], [29, 113, 22, 77], [30, 0, 22, 77], [30, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}