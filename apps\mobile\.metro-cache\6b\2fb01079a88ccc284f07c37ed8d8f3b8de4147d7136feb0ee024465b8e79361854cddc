{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /* eslint-disable reanimated/use-reanimated-error */\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.registerReanimatedError = exports.ReanimatedError = void 0;\n  exports.registerWorkletStackDetails = registerWorkletStackDetails;\n  exports.reportFatalErrorOnJS = reportFatalErrorOnJS;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _worklet_2636258972826_init_data = {\n    code: \"function ReanimatedError_errorsTs1(message){const prefix='[Reanimated]';const errorInstance=new Error(message?prefix+\\\" \\\"+message:prefix);errorInstance.name='ReanimatedError';return errorInstance;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\errors.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"ReanimatedError_errorsTs1\\\",\\\"message\\\",\\\"prefix\\\",\\\"errorInstance\\\",\\\"Error\\\",\\\"name\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/errors.ts\\\"],\\\"mappings\\\":\\\"AAaE,SAAAA,yBAAyCA,CAAEC,OAAA,EAEzC,KAAM,CAAAC,MAAM,CAAG,cAAc,CAC7B,KAAM,CAAAC,aAAa,CAAG,GAAI,CAAAC,KAAK,CAACH,OAAO,CAAMC,MAAM,KAAID,OAAO,CAAKC,MAAM,CAAC,CAC1EC,aAAa,CAACE,IAAI,CAAG,iBAAiB,CACtC,MAAO,CAAAF,aAAa,CACtB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  // signed type\n\n  var ReanimatedErrorConstructor = exports.ReanimatedError = function () {\n    var _e = [new global.Error(), 1, -27];\n    var ReanimatedError = function (message) {\n      var prefix = '[Reanimated]';\n      var errorInstance = new Error(message ? `${prefix} ${message}` : prefix);\n      errorInstance.name = 'ReanimatedError';\n      return errorInstance;\n    };\n    ReanimatedError.__closure = {};\n    ReanimatedError.__workletHash = 2636258972826;\n    ReanimatedError.__initData = _worklet_2636258972826_init_data;\n    ReanimatedError.__stackDetails = _e;\n    return ReanimatedError;\n  }();\n  /**\n   * Registers `ReanimatedError` in global scope. Use it only for Worklet\n   * runtimes.\n   */\n  var _worklet_17492640473659_init_data = {\n    code: \"function registerReanimatedError_errorsTs2(){const{ReanimatedErrorConstructor}=this.__closure;if(!_WORKLET){throw new Error('[Reanimated] registerReanimatedError() must be called on Worklet runtime');}global.ReanimatedError=ReanimatedErrorConstructor;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\errors.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"registerReanimatedError_errorsTs2\\\",\\\"ReanimatedErrorConstructor\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"Error\\\",\\\"global\\\",\\\"ReanimatedError\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/errors.ts\\\"],\\\"mappings\\\":\\\"AA2BO,SAAAA,iCAAmCA,CAAA,QAAAC,0BAAA,OAAAC,SAAA,CAExC,GAAI,CAACC,QAAQ,CAAE,CACb,KAAM,IAAI,CAAAC,KAAK,CACb,0EACF,CAAC,CACH,CACCC,MAAM,CAA6BC,eAAe,CACjDL,0BAA0B,CAC9B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var registerReanimatedError = exports.registerReanimatedError = function () {\n    var _e = [new global.Error(), -2, -27];\n    var registerReanimatedError = function () {\n      if (!_WORKLET) {\n        throw new Error('[Reanimated] registerReanimatedError() must be called on Worklet runtime');\n      }\n      global.ReanimatedError = ReanimatedErrorConstructor;\n    };\n    registerReanimatedError.__closure = {\n      ReanimatedErrorConstructor\n    };\n    registerReanimatedError.__workletHash = 17492640473659;\n    registerReanimatedError.__initData = _worklet_17492640473659_init_data;\n    registerReanimatedError.__stackDetails = _e;\n    return registerReanimatedError;\n  }();\n  var _workletStackDetails = new Map();\n  function registerWorkletStackDetails(hash, stackDetails) {\n    _workletStackDetails.set(hash, stackDetails);\n  }\n  function getBundleOffset(error) {\n    var frame = error.stack?.split('\\n')?.[0];\n    if (frame) {\n      var parsedFrame = /@([^@]+):(\\d+):(\\d+)/.exec(frame);\n      if (parsedFrame) {\n        var _parsedFrame = (0, _slicedToArray2.default)(parsedFrame, 4),\n          file = _parsedFrame[1],\n          line = _parsedFrame[2],\n          col = _parsedFrame[3];\n        return [file, Number(line), Number(col)];\n      }\n    }\n    return ['unknown', 0, 0];\n  }\n  function processStack(stack) {\n    var workletStackEntries = stack.match(/worklet_(\\d+):(\\d+):(\\d+)/g);\n    var result = stack;\n    workletStackEntries?.forEach(match => {\n      var _match$split$map = match.split(/:|_/).map(Number),\n        _match$split$map2 = (0, _slicedToArray2.default)(_match$split$map, 4),\n        hash = _match$split$map2[1],\n        origLine = _match$split$map2[2],\n        origCol = _match$split$map2[3];\n      var errorDetails = _workletStackDetails.get(hash);\n      if (!errorDetails) {\n        return;\n      }\n      var _errorDetails = (0, _slicedToArray2.default)(errorDetails, 3),\n        error = _errorDetails[0],\n        lineOffset = _errorDetails[1],\n        colOffset = _errorDetails[2];\n      var _getBundleOffset = getBundleOffset(error),\n        _getBundleOffset2 = (0, _slicedToArray2.default)(_getBundleOffset, 3),\n        bundleFile = _getBundleOffset2[0],\n        bundleLine = _getBundleOffset2[1],\n        bundleCol = _getBundleOffset2[2];\n      var line = origLine + bundleLine + lineOffset;\n      var col = origCol + bundleCol + colOffset;\n      result = result.replace(match, `${bundleFile}:${line}:${col}`);\n    });\n    return result;\n  }\n  function reportFatalErrorOnJS(_ref) {\n    var message = _ref.message,\n      stack = _ref.stack;\n    var error = new Error();\n    error.message = message;\n    error.stack = stack ? processStack(stack) : undefined;\n    error.name = 'ReanimatedError';\n    // @ts-ignore React Native's ErrorUtils implementation extends the Error type with jsEngine field\n    error.jsEngine = 'reanimated';\n    // @ts-ignore the reportFatalError method is an internal method of ErrorUtils not exposed in the type definitions\n    global.ErrorUtils.reportFatalError(error);\n  }\n});", "lineCount": 119, "map": [[2, 2, 1, 0], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13], [5, 6, 2, 13, "_interopRequireDefault"], [5, 28, 2, 13], [5, 31, 2, 13, "require"], [5, 38, 2, 13], [5, 39, 2, 13, "_dependencyMap"], [5, 53, 2, 13], [6, 2, 2, 13, "Object"], [6, 8, 2, 13], [6, 9, 2, 13, "defineProperty"], [6, 23, 2, 13], [6, 24, 2, 13, "exports"], [6, 31, 2, 13], [7, 4, 2, 13, "value"], [7, 9, 2, 13], [8, 2, 2, 13], [9, 2, 2, 13, "exports"], [9, 9, 2, 13], [9, 10, 2, 13, "registerReanimatedError"], [9, 33, 2, 13], [9, 36, 2, 13, "exports"], [9, 43, 2, 13], [9, 44, 2, 13, "ReanimatedError"], [9, 59, 2, 13], [10, 2, 2, 13, "exports"], [10, 9, 2, 13], [10, 10, 2, 13, "registerWorkletStackDetails"], [10, 37, 2, 13], [10, 40, 2, 13, "registerWorkletStackDetails"], [10, 67, 2, 13], [11, 2, 2, 13, "exports"], [11, 9, 2, 13], [11, 10, 2, 13, "reportFatalErrorOnJS"], [11, 30, 2, 13], [11, 33, 2, 13, "reportFatalErrorOnJS"], [11, 53, 2, 13], [12, 2, 2, 13], [12, 6, 2, 13, "_slicedToArray2"], [12, 21, 2, 13], [12, 24, 2, 13, "_interopRequireDefault"], [12, 46, 2, 13], [12, 47, 2, 13, "require"], [12, 54, 2, 13], [12, 55, 2, 13, "_dependencyMap"], [12, 69, 2, 13], [13, 2, 2, 13], [13, 6, 2, 13, "_worklet_2636258972826_init_data"], [13, 38, 2, 13], [14, 4, 2, 13, "code"], [14, 8, 2, 13], [15, 4, 2, 13, "location"], [15, 12, 2, 13], [16, 4, 2, 13, "sourceMap"], [16, 13, 2, 13], [17, 4, 2, 13, "version"], [17, 11, 2, 13], [18, 2, 2, 13], [19, 2, 5, 50], [21, 2, 13, 0], [21, 6, 13, 6, "ReanimatedErrorConstructor"], [21, 32, 13, 60], [21, 35, 13, 60, "exports"], [21, 42, 13, 60], [21, 43, 13, 60, "ReanimatedError"], [21, 58, 13, 60], [21, 61, 14, 2], [22, 4, 14, 2], [22, 8, 14, 2, "_e"], [22, 10, 14, 2], [22, 18, 14, 2, "global"], [22, 24, 14, 2], [22, 25, 14, 2, "Error"], [22, 30, 14, 2], [23, 4, 14, 2], [23, 8, 14, 2, "ReanimatedError"], [23, 23, 14, 2], [23, 35, 14, 2, "ReanimatedError"], [23, 36, 14, 27, "message"], [23, 43, 14, 43], [23, 45, 14, 45], [24, 6, 16, 4], [24, 10, 16, 10, "prefix"], [24, 16, 16, 16], [24, 19, 16, 19], [24, 33, 16, 33], [25, 6, 17, 4], [25, 10, 17, 10, "errorInstance"], [25, 23, 17, 23], [25, 26, 17, 26], [25, 30, 17, 30, "Error"], [25, 35, 17, 35], [25, 36, 17, 36, "message"], [25, 43, 17, 43], [25, 46, 17, 46], [25, 49, 17, 49, "prefix"], [25, 55, 17, 55], [25, 59, 17, 59, "message"], [25, 66, 17, 66], [25, 68, 17, 68], [25, 71, 17, 71, "prefix"], [25, 77, 17, 77], [25, 78, 17, 78], [26, 6, 18, 4, "errorInstance"], [26, 19, 18, 17], [26, 20, 18, 18, "name"], [26, 24, 18, 22], [26, 27, 18, 25], [26, 44, 18, 42], [27, 6, 19, 4], [27, 13, 19, 11, "errorInstance"], [27, 26, 19, 24], [28, 4, 20, 2], [28, 5, 20, 3], [29, 4, 20, 3, "ReanimatedError"], [29, 19, 20, 3], [29, 20, 20, 3, "__closure"], [29, 29, 20, 3], [30, 4, 20, 3, "ReanimatedError"], [30, 19, 20, 3], [30, 20, 20, 3, "__workletHash"], [30, 33, 20, 3], [31, 4, 20, 3, "ReanimatedError"], [31, 19, 20, 3], [31, 20, 20, 3, "__initData"], [31, 30, 20, 3], [31, 33, 20, 3, "_worklet_2636258972826_init_data"], [31, 65, 20, 3], [32, 4, 20, 3, "ReanimatedError"], [32, 19, 20, 3], [32, 20, 20, 3, "__stackDetails"], [32, 34, 20, 3], [32, 37, 20, 3, "_e"], [32, 39, 20, 3], [33, 4, 20, 3], [33, 11, 20, 3, "ReanimatedError"], [33, 26, 20, 3], [34, 2, 20, 3], [34, 3, 14, 2], [34, 5, 20, 33], [35, 2, 24, 0], [36, 0, 25, 0], [37, 0, 26, 0], [38, 0, 27, 0], [39, 2, 24, 0], [39, 6, 24, 0, "_worklet_17492640473659_init_data"], [39, 39, 24, 0], [40, 4, 24, 0, "code"], [40, 8, 24, 0], [41, 4, 24, 0, "location"], [41, 12, 24, 0], [42, 4, 24, 0, "sourceMap"], [42, 13, 24, 0], [43, 4, 24, 0, "version"], [43, 11, 24, 0], [44, 2, 24, 0], [45, 2, 24, 0], [45, 6, 24, 0, "registerReanimatedError"], [45, 29, 24, 0], [45, 32, 24, 0, "exports"], [45, 39, 24, 0], [45, 40, 24, 0, "registerReanimatedError"], [45, 63, 24, 0], [45, 66, 28, 7], [46, 4, 28, 7], [46, 8, 28, 7, "_e"], [46, 10, 28, 7], [46, 18, 28, 7, "global"], [46, 24, 28, 7], [46, 25, 28, 7, "Error"], [46, 30, 28, 7], [47, 4, 28, 7], [47, 8, 28, 7, "registerReanimatedError"], [47, 31, 28, 7], [47, 43, 28, 7, "registerReanimatedError"], [47, 44, 28, 7], [47, 46, 28, 42], [48, 6, 30, 2], [48, 10, 30, 6], [48, 11, 30, 7, "_WORKLET"], [48, 19, 30, 15], [48, 21, 30, 17], [49, 8, 31, 4], [49, 14, 31, 10], [49, 18, 31, 14, "Error"], [49, 23, 31, 19], [49, 24, 32, 6], [49, 98, 33, 4], [49, 99, 33, 5], [50, 6, 34, 2], [51, 6, 35, 3, "global"], [51, 12, 35, 9], [51, 13, 35, 38, "ReanimatedError"], [51, 28, 35, 53], [51, 31, 36, 4, "ReanimatedErrorConstructor"], [51, 57, 36, 30], [52, 4, 37, 0], [52, 5, 37, 1], [53, 4, 37, 1, "registerReanimatedError"], [53, 27, 37, 1], [53, 28, 37, 1, "__closure"], [53, 37, 37, 1], [54, 6, 37, 1, "ReanimatedErrorConstructor"], [55, 4, 37, 1], [56, 4, 37, 1, "registerReanimatedError"], [56, 27, 37, 1], [56, 28, 37, 1, "__workletHash"], [56, 41, 37, 1], [57, 4, 37, 1, "registerReanimatedError"], [57, 27, 37, 1], [57, 28, 37, 1, "__initData"], [57, 38, 37, 1], [57, 41, 37, 1, "_worklet_17492640473659_init_data"], [57, 74, 37, 1], [58, 4, 37, 1, "registerReanimatedError"], [58, 27, 37, 1], [58, 28, 37, 1, "__stackDetails"], [58, 42, 37, 1], [58, 45, 37, 1, "_e"], [58, 47, 37, 1], [59, 4, 37, 1], [59, 11, 37, 1, "registerReanimatedError"], [59, 34, 37, 1], [60, 2, 37, 1], [60, 3, 28, 7], [61, 2, 39, 0], [61, 6, 39, 6, "_workletStackDetails"], [61, 26, 39, 26], [61, 29, 39, 29], [61, 33, 39, 33, "Map"], [61, 36, 39, 36], [61, 37, 39, 66], [61, 38, 39, 67], [62, 2, 41, 7], [62, 11, 41, 16, "registerWorkletStackDetails"], [62, 38, 41, 43, "registerWorkletStackDetails"], [62, 39, 42, 2, "hash"], [62, 43, 42, 14], [62, 45, 43, 2, "stackDetails"], [62, 57, 43, 35], [62, 59, 44, 2], [63, 4, 45, 2, "_workletStackDetails"], [63, 24, 45, 22], [63, 25, 45, 23, "set"], [63, 28, 45, 26], [63, 29, 45, 27, "hash"], [63, 33, 45, 31], [63, 35, 45, 33, "stackDetails"], [63, 47, 45, 45], [63, 48, 45, 46], [64, 2, 46, 0], [65, 2, 48, 0], [65, 11, 48, 9, "getBundleOffset"], [65, 26, 48, 24, "getBundleOffset"], [65, 27, 48, 25, "error"], [65, 32, 48, 37], [65, 34, 48, 65], [66, 4, 49, 2], [66, 8, 49, 8, "frame"], [66, 13, 49, 13], [66, 16, 49, 16, "error"], [66, 21, 49, 21], [66, 22, 49, 22, "stack"], [66, 27, 49, 27], [66, 29, 49, 29, "split"], [66, 34, 49, 34], [66, 35, 49, 35], [66, 39, 49, 39], [66, 40, 49, 40], [66, 43, 49, 43], [66, 44, 49, 44], [66, 45, 49, 45], [67, 4, 50, 2], [67, 8, 50, 6, "frame"], [67, 13, 50, 11], [67, 15, 50, 13], [68, 6, 51, 4], [68, 10, 51, 10, "parsedFrame"], [68, 21, 51, 21], [68, 24, 51, 24], [68, 46, 51, 46], [68, 47, 51, 47, "exec"], [68, 51, 51, 51], [68, 52, 51, 52, "frame"], [68, 57, 51, 57], [68, 58, 51, 58], [69, 6, 52, 4], [69, 10, 52, 8, "parsedFrame"], [69, 21, 52, 19], [69, 23, 52, 21], [70, 8, 53, 6], [70, 12, 53, 6, "_parsedFrame"], [70, 24, 53, 6], [70, 31, 53, 6, "_slicedToArray2"], [70, 46, 53, 6], [70, 47, 53, 6, "default"], [70, 54, 53, 6], [70, 56, 53, 34, "parsedFrame"], [70, 67, 53, 45], [71, 10, 53, 15, "file"], [71, 14, 53, 19], [71, 17, 53, 19, "_parsedFrame"], [71, 29, 53, 19], [72, 10, 53, 21, "line"], [72, 14, 53, 25], [72, 17, 53, 25, "_parsedFrame"], [72, 29, 53, 25], [73, 10, 53, 27, "col"], [73, 13, 53, 30], [73, 16, 53, 30, "_parsedFrame"], [73, 28, 53, 30], [74, 8, 54, 6], [74, 15, 54, 13], [74, 16, 54, 14, "file"], [74, 20, 54, 18], [74, 22, 54, 20, "Number"], [74, 28, 54, 26], [74, 29, 54, 27, "line"], [74, 33, 54, 31], [74, 34, 54, 32], [74, 36, 54, 34, "Number"], [74, 42, 54, 40], [74, 43, 54, 41, "col"], [74, 46, 54, 44], [74, 47, 54, 45], [74, 48, 54, 46], [75, 6, 55, 4], [76, 4, 56, 2], [77, 4, 57, 2], [77, 11, 57, 9], [77, 12, 57, 10], [77, 21, 57, 19], [77, 23, 57, 21], [77, 24, 57, 22], [77, 26, 57, 24], [77, 27, 57, 25], [77, 28, 57, 26], [78, 2, 58, 0], [79, 2, 60, 0], [79, 11, 60, 9, "processStack"], [79, 23, 60, 21, "processStack"], [79, 24, 60, 22, "stack"], [79, 29, 60, 35], [79, 31, 60, 45], [80, 4, 61, 2], [80, 8, 61, 8, "workletStackEntries"], [80, 27, 61, 27], [80, 30, 61, 30, "stack"], [80, 35, 61, 35], [80, 36, 61, 36, "match"], [80, 41, 61, 41], [80, 42, 61, 42], [80, 70, 61, 70], [80, 71, 61, 71], [81, 4, 62, 2], [81, 8, 62, 6, "result"], [81, 14, 62, 12], [81, 17, 62, 15, "stack"], [81, 22, 62, 20], [82, 4, 63, 2, "workletStackEntries"], [82, 23, 63, 21], [82, 25, 63, 23, "for<PERSON>ach"], [82, 32, 63, 30], [82, 33, 63, 32, "match"], [82, 38, 63, 37], [82, 42, 63, 42], [83, 6, 64, 4], [83, 10, 64, 4, "_match$split$map"], [83, 26, 64, 4], [83, 29, 64, 40, "match"], [83, 34, 64, 45], [83, 35, 64, 46, "split"], [83, 40, 64, 51], [83, 41, 64, 52], [83, 46, 64, 57], [83, 47, 64, 58], [83, 48, 64, 59, "map"], [83, 51, 64, 62], [83, 52, 64, 63, "Number"], [83, 58, 64, 69], [83, 59, 64, 70], [84, 8, 64, 70, "_match$split$map2"], [84, 25, 64, 70], [84, 32, 64, 70, "_slicedToArray2"], [84, 47, 64, 70], [84, 48, 64, 70, "default"], [84, 55, 64, 70], [84, 57, 64, 70, "_match$split$map"], [84, 73, 64, 70], [85, 8, 64, 13, "hash"], [85, 12, 64, 17], [85, 15, 64, 17, "_match$split$map2"], [85, 32, 64, 17], [86, 8, 64, 19, "origLine"], [86, 16, 64, 27], [86, 19, 64, 27, "_match$split$map2"], [86, 36, 64, 27], [87, 8, 64, 29, "origCol"], [87, 15, 64, 36], [87, 18, 64, 36, "_match$split$map2"], [87, 35, 64, 36], [88, 6, 65, 4], [88, 10, 65, 10, "errorDetails"], [88, 22, 65, 22], [88, 25, 65, 25, "_workletStackDetails"], [88, 45, 65, 45], [88, 46, 65, 46, "get"], [88, 49, 65, 49], [88, 50, 65, 50, "hash"], [88, 54, 65, 54], [88, 55, 65, 55], [89, 6, 66, 4], [89, 10, 66, 8], [89, 11, 66, 9, "errorDetails"], [89, 23, 66, 21], [89, 25, 66, 23], [90, 8, 67, 6], [91, 6, 68, 4], [92, 6, 69, 4], [92, 10, 69, 4, "_errorDetails"], [92, 23, 69, 4], [92, 30, 69, 4, "_slicedToArray2"], [92, 45, 69, 4], [92, 46, 69, 4, "default"], [92, 53, 69, 4], [92, 55, 69, 43, "errorDetails"], [92, 67, 69, 55], [93, 8, 69, 11, "error"], [93, 13, 69, 16], [93, 16, 69, 16, "_errorDetails"], [93, 29, 69, 16], [94, 8, 69, 18, "lineOffset"], [94, 18, 69, 28], [94, 21, 69, 28, "_errorDetails"], [94, 34, 69, 28], [95, 8, 69, 30, "colOffset"], [95, 17, 69, 39], [95, 20, 69, 39, "_errorDetails"], [95, 33, 69, 39], [96, 6, 70, 4], [96, 10, 70, 4, "_getBundleOffset"], [96, 26, 70, 4], [96, 29, 70, 48, "getBundleOffset"], [96, 44, 70, 63], [96, 45, 70, 64, "error"], [96, 50, 70, 69], [96, 51, 70, 70], [97, 8, 70, 70, "_getBundleOffset2"], [97, 25, 70, 70], [97, 32, 70, 70, "_slicedToArray2"], [97, 47, 70, 70], [97, 48, 70, 70, "default"], [97, 55, 70, 70], [97, 57, 70, 70, "_getBundleOffset"], [97, 73, 70, 70], [98, 8, 70, 11, "bundleFile"], [98, 18, 70, 21], [98, 21, 70, 21, "_getBundleOffset2"], [98, 38, 70, 21], [99, 8, 70, 23, "bundleLine"], [99, 18, 70, 33], [99, 21, 70, 33, "_getBundleOffset2"], [99, 38, 70, 33], [100, 8, 70, 35, "bundleCol"], [100, 17, 70, 44], [100, 20, 70, 44, "_getBundleOffset2"], [100, 37, 70, 44], [101, 6, 71, 4], [101, 10, 71, 10, "line"], [101, 14, 71, 14], [101, 17, 71, 17, "origLine"], [101, 25, 71, 25], [101, 28, 71, 28, "bundleLine"], [101, 38, 71, 38], [101, 41, 71, 41, "lineOffset"], [101, 51, 71, 51], [102, 6, 72, 4], [102, 10, 72, 10, "col"], [102, 13, 72, 13], [102, 16, 72, 16, "origCol"], [102, 23, 72, 23], [102, 26, 72, 26, "bundleCol"], [102, 35, 72, 35], [102, 38, 72, 38, "colOffset"], [102, 47, 72, 47], [103, 6, 74, 4, "result"], [103, 12, 74, 10], [103, 15, 74, 13, "result"], [103, 21, 74, 19], [103, 22, 74, 20, "replace"], [103, 29, 74, 27], [103, 30, 74, 28, "match"], [103, 35, 74, 33], [103, 37, 74, 35], [103, 40, 74, 38, "bundleFile"], [103, 50, 74, 48], [103, 54, 74, 52, "line"], [103, 58, 74, 56], [103, 62, 74, 60, "col"], [103, 65, 74, 63], [103, 67, 74, 65], [103, 68, 74, 66], [104, 4, 75, 2], [104, 5, 75, 3], [104, 6, 75, 4], [105, 4, 76, 2], [105, 11, 76, 9, "result"], [105, 17, 76, 15], [106, 2, 77, 0], [107, 2, 79, 7], [107, 11, 79, 16, "reportFatalErrorOnJS"], [107, 31, 79, 36, "reportFatalErrorOnJS"], [107, 32, 79, 36, "_ref"], [107, 36, 79, 36], [107, 38, 85, 3], [108, 4, 85, 3], [108, 8, 80, 2, "message"], [108, 15, 80, 9], [108, 18, 80, 9, "_ref"], [108, 22, 80, 9], [108, 23, 80, 2, "message"], [108, 30, 80, 9], [109, 6, 81, 2, "stack"], [109, 11, 81, 7], [109, 14, 81, 7, "_ref"], [109, 18, 81, 7], [109, 19, 81, 2, "stack"], [109, 24, 81, 7], [110, 4, 86, 2], [110, 8, 86, 8, "error"], [110, 13, 86, 13], [110, 16, 86, 16], [110, 20, 86, 20, "Error"], [110, 25, 86, 25], [110, 26, 86, 26], [110, 27, 86, 27], [111, 4, 87, 2, "error"], [111, 9, 87, 7], [111, 10, 87, 8, "message"], [111, 17, 87, 15], [111, 20, 87, 18, "message"], [111, 27, 87, 25], [112, 4, 88, 2, "error"], [112, 9, 88, 7], [112, 10, 88, 8, "stack"], [112, 15, 88, 13], [112, 18, 88, 16, "stack"], [112, 23, 88, 21], [112, 26, 88, 24, "processStack"], [112, 38, 88, 36], [112, 39, 88, 37, "stack"], [112, 44, 88, 42], [112, 45, 88, 43], [112, 48, 88, 46, "undefined"], [112, 57, 88, 55], [113, 4, 89, 2, "error"], [113, 9, 89, 7], [113, 10, 89, 8, "name"], [113, 14, 89, 12], [113, 17, 89, 15], [113, 34, 89, 32], [114, 4, 90, 2], [115, 4, 91, 2, "error"], [115, 9, 91, 7], [115, 10, 91, 8, "jsEngine"], [115, 18, 91, 16], [115, 21, 91, 19], [115, 33, 91, 31], [116, 4, 92, 2], [117, 4, 93, 2, "global"], [117, 10, 93, 8], [117, 11, 93, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [117, 21, 93, 19], [117, 22, 93, 20, "reportFatalError"], [117, 38, 93, 36], [117, 39, 93, 37, "error"], [117, 44, 93, 42], [117, 45, 93, 43], [118, 2, 94, 0], [119, 0, 94, 1], [119, 3]], "functionMap": {"names": ["<global>", "ReanimatedError", "registerReanimatedError", "registerWorkletStackDetails", "getBundleOffset", "processStack", "workletStackEntries.forEach$argument_0", "reportFatalErrorOnJS"], "mappings": "AAA;ECa;GDM;OEQ;CFS;OGI;CHK;AIE;CJU;AKE;+BCG;GDY;CLE;OOE;CPe"}}, "type": "js/module"}]}