{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.version = void 0;\n  var version = exports.version = {\n    major: 0,\n    minor: 79,\n    patch: 4,\n    prerelease: null\n  };\n});", "lineCount": 12, "map": [[6, 2, 11, 7], [6, 6, 11, 13, "version"], [6, 13, 16, 2], [6, 16, 16, 2, "exports"], [6, 23, 16, 2], [6, 24, 16, 2, "version"], [6, 31, 16, 2], [6, 34, 16, 5], [7, 4, 17, 2, "major"], [7, 9, 17, 7], [7, 11, 17, 9], [7, 12, 17, 10], [8, 4, 18, 2, "minor"], [8, 9, 18, 7], [8, 11, 18, 9], [8, 13, 18, 11], [9, 4, 19, 2, "patch"], [9, 9, 19, 7], [9, 11, 19, 9], [9, 12, 19, 10], [10, 4, 20, 2, "prerelease"], [10, 14, 20, 12], [10, 16, 20, 14], [11, 2, 21, 0], [11, 3, 21, 1], [12, 0, 21, 2], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}