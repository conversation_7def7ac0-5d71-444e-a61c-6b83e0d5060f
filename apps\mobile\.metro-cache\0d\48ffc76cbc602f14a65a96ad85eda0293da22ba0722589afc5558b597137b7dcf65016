{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-freeze", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 27}, "end": {"line": 2, "column": 38, "index": 65}}], "key": "Efe2ZZW2JdfPDT7ApPzpVwSLPQs=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[2], \"react\"));\n  var _reactFreeze = require(_dependencyMap[3], \"react-freeze\");\n  var _jsxDevRuntime = require(_dependencyMap[4], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-screens\\\\src\\\\components\\\\helpers\\\\DelayedFreeze.tsx\";\n  // This component allows one more render before freezing the screen.\n  // Allows activityState to reach the native side and useIsFocused to work correctly.\n  function DelayedFreeze(_ref) {\n    var freeze = _ref.freeze,\n      children = _ref.children;\n    // flag used for determining whether freeze should be enabled\n    var _React$useState = _react.default.useState(false),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      freezeState = _React$useState2[0],\n      setFreezeState = _React$useState2[1];\n    _react.default.useEffect(() => {\n      var id = setImmediate(() => {\n        setFreezeState(freeze);\n      });\n      return () => {\n        clearImmediate(id);\n      };\n    }, [freeze]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactFreeze.Freeze, {\n      freeze: freeze ? freezeState : false,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 10\n    }, this);\n  }\n  var _default = exports.default = DelayedFreeze;\n});", "lineCount": 40, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireDefault"], [8, 37, 1, 0], [8, 38, 1, 0, "require"], [8, 45, 1, 0], [8, 46, 1, 0, "_dependencyMap"], [8, 60, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactFreeze"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 2, 38], [10, 6, 2, 38, "_jsxDevRuntime"], [10, 20, 2, 38], [10, 23, 2, 38, "require"], [10, 30, 2, 38], [10, 31, 2, 38, "_dependencyMap"], [10, 45, 2, 38], [11, 2, 2, 38], [11, 6, 2, 38, "_jsxFileName"], [11, 18, 2, 38], [12, 2, 9, 0], [13, 2, 10, 0], [14, 2, 11, 0], [14, 11, 11, 9, "DelayedFreeze"], [14, 24, 11, 22, "DelayedFreeze"], [14, 25, 11, 22, "_ref"], [14, 29, 11, 22], [14, 31, 11, 65], [15, 4, 11, 65], [15, 8, 11, 25, "freeze"], [15, 14, 11, 31], [15, 17, 11, 31, "_ref"], [15, 21, 11, 31], [15, 22, 11, 25, "freeze"], [15, 28, 11, 31], [16, 6, 11, 33, "children"], [16, 14, 11, 41], [16, 17, 11, 41, "_ref"], [16, 21, 11, 41], [16, 22, 11, 33, "children"], [16, 30, 11, 41], [17, 4, 12, 2], [18, 4, 13, 2], [18, 8, 13, 2, "_React$useState"], [18, 23, 13, 2], [18, 26, 13, 40, "React"], [18, 40, 13, 45], [18, 41, 13, 46, "useState"], [18, 49, 13, 54], [18, 50, 13, 55], [18, 55, 13, 60], [18, 56, 13, 61], [19, 6, 13, 61, "_React$useState2"], [19, 22, 13, 61], [19, 29, 13, 61, "_slicedToArray2"], [19, 44, 13, 61], [19, 45, 13, 61, "default"], [19, 52, 13, 61], [19, 54, 13, 61, "_React$useState"], [19, 69, 13, 61], [20, 6, 13, 9, "freezeState"], [20, 17, 13, 20], [20, 20, 13, 20, "_React$useState2"], [20, 36, 13, 20], [21, 6, 13, 22, "setFreezeState"], [21, 20, 13, 36], [21, 23, 13, 36, "_React$useState2"], [21, 39, 13, 36], [22, 4, 15, 2, "React"], [22, 18, 15, 7], [22, 19, 15, 8, "useEffect"], [22, 28, 15, 17], [22, 29, 15, 18], [22, 35, 15, 24], [23, 6, 16, 4], [23, 10, 16, 10, "id"], [23, 12, 16, 12], [23, 15, 16, 15, "setImmediate"], [23, 27, 16, 27], [23, 28, 16, 28], [23, 34, 16, 34], [24, 8, 17, 6, "setFreezeState"], [24, 22, 17, 20], [24, 23, 17, 21, "freeze"], [24, 29, 17, 27], [24, 30, 17, 28], [25, 6, 18, 4], [25, 7, 18, 5], [25, 8, 18, 6], [26, 6, 19, 4], [26, 13, 19, 11], [26, 19, 19, 17], [27, 8, 20, 6, "clearImmediate"], [27, 22, 20, 20], [27, 23, 20, 21, "id"], [27, 25, 20, 23], [27, 26, 20, 24], [28, 6, 21, 4], [28, 7, 21, 5], [29, 4, 22, 2], [29, 5, 22, 3], [29, 7, 22, 5], [29, 8, 22, 6, "freeze"], [29, 14, 22, 12], [29, 15, 22, 13], [29, 16, 22, 14], [30, 4, 24, 2], [30, 24, 24, 9], [30, 28, 24, 9, "_jsxDevRuntime"], [30, 42, 24, 9], [30, 43, 24, 9, "jsxDEV"], [30, 49, 24, 9], [30, 51, 24, 10, "_reactFreeze"], [30, 63, 24, 10], [30, 64, 24, 10, "Freeze"], [30, 70, 24, 16], [31, 6, 24, 17, "freeze"], [31, 12, 24, 23], [31, 14, 24, 25, "freeze"], [31, 20, 24, 31], [31, 23, 24, 34, "freezeState"], [31, 34, 24, 45], [31, 37, 24, 48], [31, 42, 24, 54], [32, 6, 24, 54, "children"], [32, 14, 24, 54], [32, 16, 24, 56, "children"], [33, 4, 24, 64], [34, 6, 24, 64, "fileName"], [34, 14, 24, 64], [34, 16, 24, 64, "_jsxFileName"], [34, 28, 24, 64], [35, 6, 24, 64, "lineNumber"], [35, 16, 24, 64], [36, 6, 24, 64, "columnNumber"], [36, 18, 24, 64], [37, 4, 24, 64], [37, 11, 24, 73], [37, 12, 24, 74], [38, 2, 25, 0], [39, 2, 25, 1], [39, 6, 25, 1, "_default"], [39, 14, 25, 1], [39, 17, 25, 1, "exports"], [39, 24, 25, 1], [39, 25, 25, 1, "default"], [39, 32, 25, 1], [39, 35, 27, 15, "DelayedFreeze"], [39, 48, 27, 28], [40, 0, 27, 28], [40, 3]], "functionMap": {"names": ["<global>", "DelayedFreeze", "React.useEffect$argument_0", "setImmediate$argument_0", "<anonymous>"], "mappings": "AAA;ACU;kBCI;4BCC;KDE;WEC;KFE;GDC;CDG"}}, "type": "js/module"}]}