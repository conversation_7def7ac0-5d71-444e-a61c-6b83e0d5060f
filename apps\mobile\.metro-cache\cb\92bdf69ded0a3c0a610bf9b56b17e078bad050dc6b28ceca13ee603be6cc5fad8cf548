{"dependencies": [{"name": "../commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 122}, "end": {"line": 4, "column": 51, "index": 173}}], "key": "dQSfS57Pf/C96+Vvd1rktbJJov4=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 174}, "end": {"line": 5, "column": 52, "index": 226}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /* eslint-disable reanimated/use-reanimated-error */\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getValueUnpackerCode = getValueUnpackerCode;\n  var _commonTypes = require(_dependencyMap[0], \"../commonTypes\");\n  var _PlatformChecker = require(_dependencyMap[1], \"../PlatformChecker\");\n  var _worklet_7469257692856_init_data = {\n    code: \"function valueUnpacker_valueUnpackerTs1(objectToUnpack,category,remoteFunctionName){let workletsCache=global.__workletsCache;let handleCache=global.__handleCache;if(workletsCache===undefined){workletsCache=global.__workletsCache=new Map();handleCache=global.__handleCache=new WeakMap();}const workletHash=objectToUnpack.__workletHash;if(workletHash!==undefined){let workletFun=workletsCache.get(workletHash);if(workletFun===undefined){const initData=objectToUnpack.__initData;if(global.evalWithSourceMap){workletFun=global.evalWithSourceMap('('+initData.code+'\\\\n)',initData.location,initData.sourceMap);}else if(global.evalWithSourceUrl){workletFun=global.evalWithSourceUrl('('+initData.code+'\\\\n)',\\\"worklet_\\\"+workletHash);}else{workletFun=eval('('+initData.code+'\\\\n)');}workletsCache.set(workletHash,workletFun);}const functionInstance=workletFun.bind(objectToUnpack);objectToUnpack._recur=functionInstance;return functionInstance;}else if(objectToUnpack.__init!==undefined){let value=handleCache.get(objectToUnpack);if(value===undefined){value=objectToUnpack.__init();handleCache.set(objectToUnpack,value);}return value;}else if(category==='RemoteFunction'){const fun=function(){const label=remoteFunctionName?\\\"function `\\\"+remoteFunctionName+\\\"`\\\":'anonymous function';throw new Error(\\\"[Reanimated] Tried to synchronously call a non-worklet \\\"+label+\\\" on the UI thread.\\\\nSee https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#tried-to-synchronously-call-a-non-worklet-function-on-the-ui-thread for more details.\\\");};fun.__remoteFunction=objectToUnpack;return fun;}else{throw new Error(\\\"[Reanimated] Data type in category \\\\\\\"\\\"+category+\\\"\\\\\\\" not recognized by value unpacker: \\\\\\\"\\\"+_toString(objectToUnpack)+\\\"\\\\\\\".\\\");}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\worklets\\\\valueUnpacker.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"valueUnpacker_valueUnpackerTs1\\\",\\\"objectToUnpack\\\",\\\"category\\\",\\\"remoteFunctionName\\\",\\\"workletsCache\\\",\\\"global\\\",\\\"__workletsCache\\\",\\\"handleCache\\\",\\\"__handleCache\\\",\\\"undefined\\\",\\\"Map\\\",\\\"WeakMap\\\",\\\"workletHash\\\",\\\"__workletHash\\\",\\\"workletFun\\\",\\\"get\\\",\\\"initData\\\",\\\"__initData\\\",\\\"evalWithSourceMap\\\",\\\"code\\\",\\\"location\\\",\\\"sourceMap\\\",\\\"evalWithSourceUrl\\\",\\\"eval\\\",\\\"set\\\",\\\"functionInstance\\\",\\\"bind\\\",\\\"_recur\\\",\\\"__init\\\",\\\"value\\\",\\\"fun\\\",\\\"label\\\",\\\"Error\\\",\\\"__remoteFunction\\\",\\\"_toString\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/worklets/valueUnpacker.ts\\\"],\\\"mappings\\\":\\\"AAMA,SAAAA,8BACqBA,CACnBC,cACA,CAAAC,QAAA,CAAAC,kBACK,EAEL,GAAI,CAAAC,aAAa,CAAGC,MAAM,CAACC,eAAe,CAC1C,GAAI,CAAAC,WAAW,CAAGF,MAAM,CAACG,aAAa,CACtC,GAAIJ,aAAa,GAAKK,SAAS,CAAE,CAE/BL,aAAa,CAAGC,MAAM,CAACC,eAAe,CAAG,GAAI,CAAAI,GAAG,CAAC,CAAC,CAClDH,WAAW,CAAGF,MAAM,CAACG,aAAa,CAAG,GAAI,CAAAG,OAAO,CAAC,CAAC,CACpD,CACA,KAAM,CAAAC,WAAW,CAAGX,cAAc,CAACY,aAAa,CAChD,GAAID,WAAW,GAAKH,SAAS,CAAE,CAC7B,GAAI,CAAAK,UAAU,CAAGV,aAAa,CAACW,GAAG,CAACH,WAAW,CAAC,CAC/C,GAAIE,UAAU,GAAKL,SAAS,CAAE,CAC5B,KAAM,CAAAO,QAAQ,CAAGf,cAAc,CAACgB,UAAU,CAC1C,GAAIZ,MAAM,CAACa,iBAAiB,CAAE,CAK5BJ,UAAU,CAAGT,MAAM,CAACa,iBAAiB,CACnC,GAAG,CAAGF,QAAQ,CAACG,IAAI,CAAG,KAAK,CAC3BH,QAAQ,CAACI,QAAQ,CACjBJ,QAAQ,CAACK,SACX,CAA4B,CAC9B,CAAC,IAAM,IAAIhB,MAAM,CAACiB,iBAAiB,CAAE,CAKnCR,UAAU,CAAGT,MAAM,CAACiB,iBAAiB,CACnC,GAAG,CAAGN,QAAQ,CAACG,IAAI,CAAG,KAAK,YAChBP,WACb,CAA4B,CAC9B,CAAC,IAAM,CAGLE,UAAU,CAAGS,IAAI,CAAC,GAAG,CAAGP,QAAQ,CAACG,IAAI,CAAG,KAAK,CAErC,CACV,CACAf,aAAa,CAACoB,GAAG,CAACZ,WAAW,CAAEE,UAAU,CAAC,CAC5C,CACA,KAAM,CAAAW,gBAAgB,CAAGX,UAAU,CAACY,IAAI,CAACzB,cAAc,CAAC,CACxDA,cAAc,CAAC0B,MAAM,CAAGF,gBAAgB,CACxC,MAAO,CAAAA,gBAAgB,CACzB,CAAC,IAAM,IAAIxB,cAAc,CAAC2B,MAAM,GAAKnB,SAAS,CAAE,CAC9C,GAAI,CAAAoB,KAAK,CAAGtB,WAAW,CAACQ,GAAG,CAACd,cAAc,CAAC,CAC3C,GAAI4B,KAAK,GAAKpB,SAAS,CAAE,CACvBoB,KAAK,CAAG5B,cAAc,CAAC2B,MAAM,CAAC,CAAC,CAC/BrB,WAAW,CAACiB,GAAG,CAACvB,cAAc,CAAE4B,KAAK,CAAC,CACxC,CACA,MAAO,CAAAA,KAAK,CACd,CAAC,IAAM,IAAI3B,QAAQ,GAAK,gBAAgB,CAAE,CACxC,KAAM,CAAA4B,GAAG,CAAG,QAAAA,CAAA,CAAM,CAChB,KAAM,CAAAC,KAAK,CAAG5B,kBAAkB,cACdA,kBAAkB,KAChC,oBAAoB,CACxB,KAAM,IAAI,CAAA6B,KAAK,2DAA2DD,KAAK,+LACoF,CAAC,CACtK,CAAC,CACDD,GAAG,CAACG,gBAAgB,CAAGhC,cAAc,CACrC,MAAO,CAAA6B,GAAG,CACZ,CAAC,IAAM,CACL,KAAM,IAAI,CAAAE,KAAK,yCAC0B9B,QAAQ,2CAAwCgC,SAAS,CAC9FjC,cACF,CAAC,MACH,CAAC,CACH,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var valueUnpacker = function () {\n    var _e = [new global.Error(), 1, -27];\n    var valueUnpacker = function (objectToUnpack, category, remoteFunctionName) {\n      var workletsCache = global.__workletsCache;\n      var handleCache = global.__handleCache;\n      if (workletsCache === undefined) {\n        // init\n        workletsCache = global.__workletsCache = new Map();\n        handleCache = global.__handleCache = new WeakMap();\n      }\n      var workletHash = objectToUnpack.__workletHash;\n      if (workletHash !== undefined) {\n        var workletFun = workletsCache.get(workletHash);\n        if (workletFun === undefined) {\n          var initData = objectToUnpack.__initData;\n          if (global.evalWithSourceMap) {\n            // if the runtime (hermes only for now) supports loading source maps\n            // we want to use the proper filename for the location as it guarantees\n            // that debugger understands and loads the source code of the file where\n            // the worklet is defined.\n            workletFun = global.evalWithSourceMap('(' + initData.code + '\\n)', initData.location, initData.sourceMap);\n          } else if (global.evalWithSourceUrl) {\n            // if the runtime doesn't support loading source maps, in dev mode we\n            // can pass source url when evaluating the worklet. Now, instead of using\n            // the actual file location we use worklet hash, as it the allows us to\n            // properly symbolicate traces (see errors.ts for details)\n            workletFun = global.evalWithSourceUrl('(' + initData.code + '\\n)', `worklet_${workletHash}`);\n          } else {\n            // in release we use the regular eval to save on JSI calls\n            // eslint-disable-next-line no-eval\n            workletFun = eval('(' + initData.code + '\\n)');\n          }\n          workletsCache.set(workletHash, workletFun);\n        }\n        var functionInstance = workletFun.bind(objectToUnpack);\n        objectToUnpack._recur = functionInstance;\n        return functionInstance;\n      } else if (objectToUnpack.__init !== undefined) {\n        var value = handleCache.get(objectToUnpack);\n        if (value === undefined) {\n          value = objectToUnpack.__init();\n          handleCache.set(objectToUnpack, value);\n        }\n        return value;\n      } else if (category === 'RemoteFunction') {\n        var fun = () => {\n          var label = remoteFunctionName ? `function \\`${remoteFunctionName}\\`` : 'anonymous function';\n          throw new Error(`[Reanimated] Tried to synchronously call a non-worklet ${label} on the UI thread.\nSee https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#tried-to-synchronously-call-a-non-worklet-function-on-the-ui-thread for more details.`);\n        };\n        fun.__remoteFunction = objectToUnpack;\n        return fun;\n      } else {\n        throw new Error(`[Reanimated] Data type in category \"${category}\" not recognized by value unpacker: \"${_toString(objectToUnpack)}\".`);\n      }\n    };\n    valueUnpacker.__closure = {};\n    valueUnpacker.__workletHash = 7469257692856;\n    valueUnpacker.__initData = _worklet_7469257692856_init_data;\n    valueUnpacker.__stackDetails = _e;\n    return valueUnpacker;\n  }();\n  var _worklet_************_init_data = {\n    code: \"function valueUnpackerTs2(){}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\worklets\\\\valueUnpacker.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"valueUnpackerTs2\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/worklets/valueUnpacker.ts\\\"],\\\"mappings\\\":\\\"AAuFuB,SAAAA,gBAErBA,CAAA\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  if (__DEV__ && !(0, _PlatformChecker.shouldBeUseWeb)()) {\n    var testWorklet = function () {\n      var _e = [new global.Error(), 1, -27];\n      var valueUnpackerTs2 = function () {};\n      valueUnpackerTs2.__closure = {};\n      valueUnpackerTs2.__workletHash = ************;\n      valueUnpackerTs2.__initData = _worklet_************_init_data;\n      valueUnpackerTs2.__stackDetails = _e;\n      return valueUnpackerTs2;\n    }();\n    if (!(0, _commonTypes.isWorkletFunction)(testWorklet)) {\n      throw new Error(`[Reanimated] Failed to create a worklet. See https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#failed-to-create-a-worklet for more details.`);\n    }\n    if (!(0, _commonTypes.isWorkletFunction)(valueUnpacker)) {\n      throw new Error('[Reanimated] `valueUnpacker` is not a worklet');\n    }\n    var closure = valueUnpacker.__closure;\n    if (closure === undefined) {\n      throw new Error('[Reanimated] `valueUnpacker` closure is undefined');\n    }\n    if (Object.keys(closure).length !== 0) {\n      throw new Error('[Reanimated] `valueUnpacker` must have empty closure');\n    }\n  }\n  function getValueUnpackerCode() {\n    return valueUnpacker.__initData.code;\n  }\n});", "lineCount": 112, "map": [[2, 2, 1, 0], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13, "Object"], [5, 8, 2, 13], [5, 9, 2, 13, "defineProperty"], [5, 23, 2, 13], [5, 24, 2, 13, "exports"], [5, 31, 2, 13], [6, 4, 2, 13, "value"], [6, 9, 2, 13], [7, 2, 2, 13], [8, 2, 2, 13, "exports"], [8, 9, 2, 13], [8, 10, 2, 13, "getValueUnpackerCode"], [8, 30, 2, 13], [8, 33, 2, 13, "getValueUnpackerCode"], [8, 53, 2, 13], [9, 2, 4, 0], [9, 6, 4, 0, "_commonTypes"], [9, 18, 4, 0], [9, 21, 4, 0, "require"], [9, 28, 4, 0], [9, 29, 4, 0, "_dependencyMap"], [9, 43, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_PlatformChecker"], [10, 22, 5, 0], [10, 25, 5, 0, "require"], [10, 32, 5, 0], [10, 33, 5, 0, "_dependencyMap"], [10, 47, 5, 0], [11, 2, 5, 52], [11, 6, 5, 52, "_worklet_7469257692856_init_data"], [11, 38, 5, 52], [12, 4, 5, 52, "code"], [12, 8, 5, 52], [13, 4, 5, 52, "location"], [13, 12, 5, 52], [14, 4, 5, 52, "sourceMap"], [14, 13, 5, 52], [15, 4, 5, 52, "version"], [15, 11, 5, 52], [16, 2, 5, 52], [17, 2, 5, 52], [17, 6, 5, 52, "valueUnpacker"], [17, 19, 5, 52], [17, 22, 7, 0], [18, 4, 7, 0], [18, 8, 7, 0, "_e"], [18, 10, 7, 0], [18, 18, 7, 0, "global"], [18, 24, 7, 0], [18, 25, 7, 0, "Error"], [18, 30, 7, 0], [19, 4, 7, 0], [19, 8, 7, 0, "valueUnpacker"], [19, 21, 7, 0], [19, 33, 7, 0, "valueUnpacker"], [19, 34, 8, 2, "objectToUnpack"], [19, 48, 8, 21], [19, 50, 9, 2, "category"], [19, 58, 9, 19], [19, 60, 10, 2, "remoteFunctionName"], [19, 78, 10, 29], [19, 80, 11, 7], [20, 6, 13, 2], [20, 10, 13, 6, "workletsCache"], [20, 23, 13, 19], [20, 26, 13, 22, "global"], [20, 32, 13, 28], [20, 33, 13, 29, "__workletsCache"], [20, 48, 13, 44], [21, 6, 14, 2], [21, 10, 14, 6, "handleCache"], [21, 21, 14, 17], [21, 24, 14, 20, "global"], [21, 30, 14, 26], [21, 31, 14, 27, "__handleCache"], [21, 44, 14, 40], [22, 6, 15, 2], [22, 10, 15, 6, "workletsCache"], [22, 23, 15, 19], [22, 28, 15, 24, "undefined"], [22, 37, 15, 33], [22, 39, 15, 35], [23, 8, 16, 4], [24, 8, 17, 4, "workletsCache"], [24, 21, 17, 17], [24, 24, 17, 20, "global"], [24, 30, 17, 26], [24, 31, 17, 27, "__workletsCache"], [24, 46, 17, 42], [24, 49, 17, 45], [24, 53, 17, 49, "Map"], [24, 56, 17, 52], [24, 57, 17, 53], [24, 58, 17, 54], [25, 8, 18, 4, "handleCache"], [25, 19, 18, 15], [25, 22, 18, 18, "global"], [25, 28, 18, 24], [25, 29, 18, 25, "__handleCache"], [25, 42, 18, 38], [25, 45, 18, 41], [25, 49, 18, 45, "WeakMap"], [25, 56, 18, 52], [25, 57, 18, 53], [25, 58, 18, 54], [26, 6, 19, 2], [27, 6, 20, 2], [27, 10, 20, 8, "workletHash"], [27, 21, 20, 19], [27, 24, 20, 22, "objectToUnpack"], [27, 38, 20, 36], [27, 39, 20, 37, "__workletHash"], [27, 52, 20, 50], [28, 6, 21, 2], [28, 10, 21, 6, "workletHash"], [28, 21, 21, 17], [28, 26, 21, 22, "undefined"], [28, 35, 21, 31], [28, 37, 21, 33], [29, 8, 22, 4], [29, 12, 22, 8, "workletFun"], [29, 22, 22, 18], [29, 25, 22, 21, "workletsCache"], [29, 38, 22, 34], [29, 39, 22, 35, "get"], [29, 42, 22, 38], [29, 43, 22, 39, "workletHash"], [29, 54, 22, 50], [29, 55, 22, 51], [30, 8, 23, 4], [30, 12, 23, 8, "workletFun"], [30, 22, 23, 18], [30, 27, 23, 23, "undefined"], [30, 36, 23, 32], [30, 38, 23, 34], [31, 10, 24, 6], [31, 14, 24, 12, "initData"], [31, 22, 24, 20], [31, 25, 24, 23, "objectToUnpack"], [31, 39, 24, 37], [31, 40, 24, 38, "__initData"], [31, 50, 24, 48], [32, 10, 25, 6], [32, 14, 25, 10, "global"], [32, 20, 25, 16], [32, 21, 25, 17, "evalWithSourceMap"], [32, 38, 25, 34], [32, 40, 25, 36], [33, 12, 26, 8], [34, 12, 27, 8], [35, 12, 28, 8], [36, 12, 29, 8], [37, 12, 30, 8, "workletFun"], [37, 22, 30, 18], [37, 25, 30, 21, "global"], [37, 31, 30, 27], [37, 32, 30, 28, "evalWithSourceMap"], [37, 49, 30, 45], [37, 50, 31, 10], [37, 53, 31, 13], [37, 56, 31, 16, "initData"], [37, 64, 31, 24], [37, 65, 31, 25, "code"], [37, 69, 31, 29], [37, 72, 31, 32], [37, 77, 31, 37], [37, 79, 32, 10, "initData"], [37, 87, 32, 18], [37, 88, 32, 19, "location"], [37, 96, 32, 27], [37, 98, 33, 10, "initData"], [37, 106, 33, 18], [37, 107, 33, 19, "sourceMap"], [37, 116, 34, 8], [37, 117, 34, 36], [38, 10, 35, 6], [38, 11, 35, 7], [38, 17, 35, 13], [38, 21, 35, 17, "global"], [38, 27, 35, 23], [38, 28, 35, 24, "evalWithSourceUrl"], [38, 45, 35, 41], [38, 47, 35, 43], [39, 12, 36, 8], [40, 12, 37, 8], [41, 12, 38, 8], [42, 12, 39, 8], [43, 12, 40, 8, "workletFun"], [43, 22, 40, 18], [43, 25, 40, 21, "global"], [43, 31, 40, 27], [43, 32, 40, 28, "evalWithSourceUrl"], [43, 49, 40, 45], [43, 50, 41, 10], [43, 53, 41, 13], [43, 56, 41, 16, "initData"], [43, 64, 41, 24], [43, 65, 41, 25, "code"], [43, 69, 41, 29], [43, 72, 41, 32], [43, 77, 41, 37], [43, 79, 42, 10], [43, 90, 42, 21, "workletHash"], [43, 101, 42, 32], [43, 103, 43, 8], [43, 104, 43, 36], [44, 10, 44, 6], [44, 11, 44, 7], [44, 17, 44, 13], [45, 12, 45, 8], [46, 12, 46, 8], [47, 12, 47, 8, "workletFun"], [47, 22, 47, 18], [47, 25, 47, 21, "eval"], [47, 29, 47, 25], [47, 30, 47, 26], [47, 33, 47, 29], [47, 36, 47, 32, "initData"], [47, 44, 47, 40], [47, 45, 47, 41, "code"], [47, 49, 47, 45], [47, 52, 47, 48], [47, 57, 47, 53], [47, 58, 49, 16], [48, 10, 50, 6], [49, 10, 51, 6, "workletsCache"], [49, 23, 51, 19], [49, 24, 51, 20, "set"], [49, 27, 51, 23], [49, 28, 51, 24, "workletHash"], [49, 39, 51, 35], [49, 41, 51, 37, "workletFun"], [49, 51, 51, 47], [49, 52, 51, 48], [50, 8, 52, 4], [51, 8, 53, 4], [51, 12, 53, 10, "functionInstance"], [51, 28, 53, 26], [51, 31, 53, 29, "workletFun"], [51, 41, 53, 39], [51, 42, 53, 40, "bind"], [51, 46, 53, 44], [51, 47, 53, 45, "objectToUnpack"], [51, 61, 53, 59], [51, 62, 53, 60], [52, 8, 54, 4, "objectToUnpack"], [52, 22, 54, 18], [52, 23, 54, 19, "_recur"], [52, 29, 54, 25], [52, 32, 54, 28, "functionInstance"], [52, 48, 54, 44], [53, 8, 55, 4], [53, 15, 55, 11, "functionInstance"], [53, 31, 55, 27], [54, 6, 56, 2], [54, 7, 56, 3], [54, 13, 56, 9], [54, 17, 56, 13, "objectToUnpack"], [54, 31, 56, 27], [54, 32, 56, 28, "__init"], [54, 38, 56, 34], [54, 43, 56, 39, "undefined"], [54, 52, 56, 48], [54, 54, 56, 50], [55, 8, 57, 4], [55, 12, 57, 8, "value"], [55, 17, 57, 13], [55, 20, 57, 16, "handleCache"], [55, 31, 57, 27], [55, 32, 57, 28, "get"], [55, 35, 57, 31], [55, 36, 57, 32, "objectToUnpack"], [55, 50, 57, 46], [55, 51, 57, 47], [56, 8, 58, 4], [56, 12, 58, 8, "value"], [56, 17, 58, 13], [56, 22, 58, 18, "undefined"], [56, 31, 58, 27], [56, 33, 58, 29], [57, 10, 59, 6, "value"], [57, 15, 59, 11], [57, 18, 59, 14, "objectToUnpack"], [57, 32, 59, 28], [57, 33, 59, 29, "__init"], [57, 39, 59, 35], [57, 40, 59, 36], [57, 41, 59, 37], [58, 10, 60, 6, "handleCache"], [58, 21, 60, 17], [58, 22, 60, 18, "set"], [58, 25, 60, 21], [58, 26, 60, 22, "objectToUnpack"], [58, 40, 60, 36], [58, 42, 60, 38, "value"], [58, 47, 60, 43], [58, 48, 60, 44], [59, 8, 61, 4], [60, 8, 62, 4], [60, 15, 62, 11, "value"], [60, 20, 62, 16], [61, 6, 63, 2], [61, 7, 63, 3], [61, 13, 63, 9], [61, 17, 63, 13, "category"], [61, 25, 63, 21], [61, 30, 63, 26], [61, 46, 63, 42], [61, 48, 63, 44], [62, 8, 64, 4], [62, 12, 64, 10, "fun"], [62, 15, 64, 13], [62, 18, 64, 16, "fun"], [62, 19, 64, 16], [62, 24, 64, 22], [63, 10, 65, 6], [63, 14, 65, 12, "label"], [63, 19, 65, 17], [63, 22, 65, 20, "remoteFunctionName"], [63, 40, 65, 38], [63, 43, 66, 10], [63, 57, 66, 24, "remoteFunctionName"], [63, 75, 66, 42], [63, 79, 66, 46], [63, 82, 67, 10], [63, 102, 67, 30], [64, 10, 68, 6], [64, 16, 68, 12], [64, 20, 68, 16, "Error"], [64, 25, 68, 21], [64, 26, 68, 22], [64, 84, 68, 80, "label"], [64, 89, 68, 85], [65, 0, 69, 0], [65, 169, 69, 169], [65, 170, 69, 170], [66, 8, 70, 4], [66, 9, 70, 5], [67, 8, 71, 4, "fun"], [67, 11, 71, 7], [67, 12, 71, 8, "__remoteFunction"], [67, 28, 71, 24], [67, 31, 71, 27, "objectToUnpack"], [67, 45, 71, 41], [68, 8, 72, 4], [68, 15, 72, 11, "fun"], [68, 18, 72, 14], [69, 6, 73, 2], [69, 7, 73, 3], [69, 13, 73, 9], [70, 8, 74, 4], [70, 14, 74, 10], [70, 18, 74, 14, "Error"], [70, 23, 74, 19], [70, 24, 75, 6], [70, 63, 75, 45, "category"], [70, 71, 75, 53], [70, 111, 75, 93, "_toString"], [70, 120, 75, 102], [70, 121, 76, 8, "objectToUnpack"], [70, 135, 77, 6], [70, 136, 77, 7], [70, 140, 78, 4], [70, 141, 78, 5], [71, 6, 79, 2], [72, 4, 80, 0], [72, 5, 80, 1], [73, 4, 80, 1, "valueUnpacker"], [73, 17, 80, 1], [73, 18, 80, 1, "__closure"], [73, 27, 80, 1], [74, 4, 80, 1, "valueUnpacker"], [74, 17, 80, 1], [74, 18, 80, 1, "__workletHash"], [74, 31, 80, 1], [75, 4, 80, 1, "valueUnpacker"], [75, 17, 80, 1], [75, 18, 80, 1, "__initData"], [75, 28, 80, 1], [75, 31, 80, 1, "_worklet_7469257692856_init_data"], [75, 63, 80, 1], [76, 4, 80, 1, "valueUnpacker"], [76, 17, 80, 1], [76, 18, 80, 1, "__stackDetails"], [76, 32, 80, 1], [76, 35, 80, 1, "_e"], [76, 37, 80, 1], [77, 4, 80, 1], [77, 11, 80, 1, "valueUnpacker"], [77, 24, 80, 1], [78, 2, 80, 1], [78, 3, 7, 0], [79, 2, 7, 0], [79, 6, 7, 0, "_worklet_************_init_data"], [79, 37, 7, 0], [80, 4, 7, 0, "code"], [80, 8, 7, 0], [81, 4, 7, 0, "location"], [81, 12, 7, 0], [82, 4, 7, 0, "sourceMap"], [82, 13, 7, 0], [83, 4, 7, 0, "version"], [83, 11, 7, 0], [84, 2, 7, 0], [85, 2, 87, 0], [85, 6, 87, 4, "__DEV__"], [85, 13, 87, 11], [85, 17, 87, 15], [85, 18, 87, 16], [85, 22, 87, 16, "shouldBeUseWeb"], [85, 53, 87, 30], [85, 55, 87, 31], [85, 56, 87, 32], [85, 58, 87, 34], [86, 4, 88, 2], [86, 8, 88, 8, "testWorklet"], [86, 19, 88, 19], [86, 22, 88, 23], [87, 6, 88, 23], [87, 10, 88, 23, "_e"], [87, 12, 88, 23], [87, 20, 88, 23, "global"], [87, 26, 88, 23], [87, 27, 88, 23, "Error"], [87, 32, 88, 23], [88, 6, 88, 23], [88, 10, 88, 23, "valueUnpackerTs2"], [88, 26, 88, 23], [88, 38, 88, 23, "valueUnpackerTs2"], [88, 39, 88, 23], [88, 41, 88, 29], [88, 42, 90, 2], [88, 43, 90, 3], [89, 6, 90, 3, "valueUnpackerTs2"], [89, 22, 90, 3], [89, 23, 90, 3, "__closure"], [89, 32, 90, 3], [90, 6, 90, 3, "valueUnpackerTs2"], [90, 22, 90, 3], [90, 23, 90, 3, "__workletHash"], [90, 36, 90, 3], [91, 6, 90, 3, "valueUnpackerTs2"], [91, 22, 90, 3], [91, 23, 90, 3, "__initData"], [91, 33, 90, 3], [91, 36, 90, 3, "_worklet_************_init_data"], [91, 67, 90, 3], [92, 6, 90, 3, "valueUnpackerTs2"], [92, 22, 90, 3], [92, 23, 90, 3, "__stackDetails"], [92, 37, 90, 3], [92, 40, 90, 3, "_e"], [92, 42, 90, 3], [93, 6, 90, 3], [93, 13, 90, 3, "valueUnpackerTs2"], [93, 29, 90, 3], [94, 4, 90, 3], [94, 5, 88, 23], [94, 7, 90, 33], [95, 4, 91, 2], [95, 8, 91, 6], [95, 9, 91, 7], [95, 13, 91, 7, "isWorkletFunction"], [95, 43, 91, 24], [95, 45, 91, 25, "testWorklet"], [95, 56, 91, 36], [95, 57, 91, 37], [95, 59, 91, 39], [96, 6, 92, 4], [96, 12, 92, 10], [96, 16, 92, 14, "Error"], [96, 21, 92, 19], [96, 22, 93, 6], [96, 192, 94, 4], [96, 193, 94, 5], [97, 4, 95, 2], [98, 4, 96, 2], [98, 8, 96, 6], [98, 9, 96, 7], [98, 13, 96, 7, "isWorkletFunction"], [98, 43, 96, 24], [98, 45, 96, 25, "valueUnpacker"], [98, 58, 96, 38], [98, 59, 96, 39], [98, 61, 96, 41], [99, 6, 97, 4], [99, 12, 97, 10], [99, 16, 97, 14, "Error"], [99, 21, 97, 19], [99, 22, 97, 20], [99, 69, 97, 67], [99, 70, 97, 68], [100, 4, 98, 2], [101, 4, 99, 2], [101, 8, 99, 8, "closure"], [101, 15, 99, 15], [101, 18, 99, 19, "valueUnpacker"], [101, 31, 99, 32], [101, 32, 99, 51, "__closure"], [101, 41, 99, 60], [102, 4, 100, 2], [102, 8, 100, 6, "closure"], [102, 15, 100, 13], [102, 20, 100, 18, "undefined"], [102, 29, 100, 27], [102, 31, 100, 29], [103, 6, 101, 4], [103, 12, 101, 10], [103, 16, 101, 14, "Error"], [103, 21, 101, 19], [103, 22, 101, 20], [103, 73, 101, 71], [103, 74, 101, 72], [104, 4, 102, 2], [105, 4, 103, 2], [105, 8, 103, 6, "Object"], [105, 14, 103, 12], [105, 15, 103, 13, "keys"], [105, 19, 103, 17], [105, 20, 103, 18, "closure"], [105, 27, 103, 25], [105, 28, 103, 26], [105, 29, 103, 27, "length"], [105, 35, 103, 33], [105, 40, 103, 38], [105, 41, 103, 39], [105, 43, 103, 41], [106, 6, 104, 4], [106, 12, 104, 10], [106, 16, 104, 14, "Error"], [106, 21, 104, 19], [106, 22, 104, 20], [106, 76, 104, 74], [106, 77, 104, 75], [107, 4, 105, 2], [108, 2, 106, 0], [109, 2, 108, 7], [109, 11, 108, 16, "getValueUnpackerCode"], [109, 31, 108, 36, "getValueUnpackerCode"], [109, 32, 108, 36], [109, 34, 108, 39], [110, 4, 109, 2], [110, 11, 109, 10, "valueUnpacker"], [110, 24, 109, 23], [110, 25, 109, 42, "__initData"], [110, 35, 109, 52], [110, 36, 109, 53, "code"], [110, 40, 109, 57], [111, 2, 110, 0], [112, 0, 110, 1], [112, 3]], "functionMap": {"names": ["<global>", "valueUnpacker", "fun", "<anonymous>", "getValueUnpackerCode"], "mappings": "AAA;ACM;gBCyD;KDM;CDU;uBGQ;GHE;OIkB;CJE"}}, "type": "js/module"}]}