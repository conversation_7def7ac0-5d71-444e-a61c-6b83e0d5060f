{"dependencies": [{"name": "../EventTiming", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 54}}], "key": "ozpEK0R1tQnIxI7SXrFFB0Ay5TA=", "exportNames": ["*"]}}, {"name": "../LongTasks", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 55}}], "key": "yFk/mONAnKJSSzqZF1Oq1vD/abY=", "exportNames": ["*"]}}, {"name": "../PerformanceEntry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 53}}], "key": "IcA5aInmWtoWpgioVezhpZlWH+E=", "exportNames": ["*"]}}, {"name": "../UserTiming", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 66}}], "key": "eyVb/MK83JwDDHeCbDLudFxk868=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.RawPerformanceEntryTypeValues = void 0;\n  exports.performanceEntryTypeToRaw = performanceEntryTypeToRaw;\n  exports.rawToPerformanceEntry = rawToPerformanceEntry;\n  exports.rawToPerformanceEntryType = rawToPerformanceEntryType;\n  var _EventTiming = require(_dependencyMap[0], \"../EventTiming\");\n  var _LongTasks = require(_dependencyMap[1], \"../LongTasks\");\n  var _PerformanceEntry = require(_dependencyMap[2], \"../PerformanceEntry\");\n  var _UserTiming = require(_dependencyMap[3], \"../UserTiming\");\n  var RawPerformanceEntryTypeValues = exports.RawPerformanceEntryTypeValues = {\n    MARK: 1,\n    MEASURE: 2,\n    EVENT: 3,\n    LONGTASK: 4\n  };\n  function rawToPerformanceEntry(entry) {\n    if (entry.entryType === RawPerformanceEntryTypeValues.EVENT) {\n      return new _EventTiming.PerformanceEventTiming({\n        name: entry.name,\n        startTime: entry.startTime,\n        duration: entry.duration,\n        processingStart: entry.processingStart,\n        processingEnd: entry.processingEnd,\n        interactionId: entry.interactionId\n      });\n    } else if (entry.entryType === RawPerformanceEntryTypeValues.LONGTASK) {\n      return new _LongTasks.PerformanceLongTaskTiming({\n        name: entry.name,\n        entryType: rawToPerformanceEntryType(entry.entryType),\n        startTime: entry.startTime,\n        duration: entry.duration\n      });\n    } else if (entry.entryType === RawPerformanceEntryTypeValues.MARK) {\n      return new _UserTiming.PerformanceMark(entry.name, {\n        startTime: entry.startTime\n      });\n    } else if (entry.entryType === RawPerformanceEntryTypeValues.MEASURE) {\n      return new _UserTiming.PerformanceMeasure(entry.name, {\n        startTime: entry.startTime,\n        duration: entry.duration\n      });\n    } else {\n      return new _PerformanceEntry.PerformanceEntry({\n        name: entry.name,\n        entryType: rawToPerformanceEntryType(entry.entryType),\n        startTime: entry.startTime,\n        duration: entry.duration\n      });\n    }\n  }\n  function rawToPerformanceEntryType(type) {\n    switch (type) {\n      case RawPerformanceEntryTypeValues.MARK:\n        return 'mark';\n      case RawPerformanceEntryTypeValues.MEASURE:\n        return 'measure';\n      case RawPerformanceEntryTypeValues.EVENT:\n        return 'event';\n      case RawPerformanceEntryTypeValues.LONGTASK:\n        return 'longtask';\n      default:\n        throw new TypeError(`rawToPerformanceEntryType: unexpected performance entry type received: ${type}`);\n    }\n  }\n  function performanceEntryTypeToRaw(type) {\n    switch (type) {\n      case 'mark':\n        return RawPerformanceEntryTypeValues.MARK;\n      case 'measure':\n        return RawPerformanceEntryTypeValues.MEASURE;\n      case 'event':\n        return RawPerformanceEntryTypeValues.EVENT;\n      case 'longtask':\n        return RawPerformanceEntryTypeValues.LONGTASK;\n      default:\n        type;\n        throw new TypeError(`performanceEntryTypeToRaw: unexpected performance entry type received: ${type}`);\n    }\n  }\n});", "lineCount": 83, "map": [[9, 2, 17, 0], [9, 6, 17, 0, "_EventTiming"], [9, 18, 17, 0], [9, 21, 17, 0, "require"], [9, 28, 17, 0], [9, 29, 17, 0, "_dependencyMap"], [9, 43, 17, 0], [10, 2, 18, 0], [10, 6, 18, 0, "_LongTasks"], [10, 16, 18, 0], [10, 19, 18, 0, "require"], [10, 26, 18, 0], [10, 27, 18, 0, "_dependencyMap"], [10, 41, 18, 0], [11, 2, 19, 0], [11, 6, 19, 0, "_PerformanceEntry"], [11, 23, 19, 0], [11, 26, 19, 0, "require"], [11, 33, 19, 0], [11, 34, 19, 0, "_dependencyMap"], [11, 48, 19, 0], [12, 2, 20, 0], [12, 6, 20, 0, "_UserTiming"], [12, 17, 20, 0], [12, 20, 20, 0, "require"], [12, 27, 20, 0], [12, 28, 20, 0, "_dependencyMap"], [12, 42, 20, 0], [13, 2, 22, 7], [13, 6, 22, 13, "RawPerformanceEntryTypeValues"], [13, 35, 22, 42], [13, 38, 22, 42, "exports"], [13, 45, 22, 42], [13, 46, 22, 42, "RawPerformanceEntryTypeValues"], [13, 75, 22, 42], [13, 78, 22, 45], [14, 4, 23, 2, "MARK"], [14, 8, 23, 6], [14, 10, 23, 8], [14, 11, 23, 9], [15, 4, 24, 2, "MEASURE"], [15, 11, 24, 9], [15, 13, 24, 11], [15, 14, 24, 12], [16, 4, 25, 2, "EVENT"], [16, 9, 25, 7], [16, 11, 25, 9], [16, 12, 25, 10], [17, 4, 26, 2, "LONGTASK"], [17, 12, 26, 10], [17, 14, 26, 12], [18, 2, 27, 0], [18, 3, 27, 1], [19, 2, 29, 7], [19, 11, 29, 16, "rawToPerformanceEntry"], [19, 32, 29, 37, "rawToPerformanceEntry"], [19, 33, 30, 2, "entry"], [19, 38, 30, 28], [19, 40, 31, 20], [20, 4, 32, 2], [20, 8, 32, 6, "entry"], [20, 13, 32, 11], [20, 14, 32, 12, "entryType"], [20, 23, 32, 21], [20, 28, 32, 26, "RawPerformanceEntryTypeValues"], [20, 57, 32, 55], [20, 58, 32, 56, "EVENT"], [20, 63, 32, 61], [20, 65, 32, 63], [21, 6, 33, 4], [21, 13, 33, 11], [21, 17, 33, 15, "PerformanceEventTiming"], [21, 52, 33, 37], [21, 53, 33, 38], [22, 8, 34, 6, "name"], [22, 12, 34, 10], [22, 14, 34, 12, "entry"], [22, 19, 34, 17], [22, 20, 34, 18, "name"], [22, 24, 34, 22], [23, 8, 35, 6, "startTime"], [23, 17, 35, 15], [23, 19, 35, 17, "entry"], [23, 24, 35, 22], [23, 25, 35, 23, "startTime"], [23, 34, 35, 32], [24, 8, 36, 6, "duration"], [24, 16, 36, 14], [24, 18, 36, 16, "entry"], [24, 23, 36, 21], [24, 24, 36, 22, "duration"], [24, 32, 36, 30], [25, 8, 37, 6, "processingStart"], [25, 23, 37, 21], [25, 25, 37, 23, "entry"], [25, 30, 37, 28], [25, 31, 37, 29, "processingStart"], [25, 46, 37, 44], [26, 8, 38, 6, "processingEnd"], [26, 21, 38, 19], [26, 23, 38, 21, "entry"], [26, 28, 38, 26], [26, 29, 38, 27, "processingEnd"], [26, 42, 38, 40], [27, 8, 39, 6, "interactionId"], [27, 21, 39, 19], [27, 23, 39, 21, "entry"], [27, 28, 39, 26], [27, 29, 39, 27, "interactionId"], [28, 6, 40, 4], [28, 7, 40, 5], [28, 8, 40, 6], [29, 4, 41, 2], [29, 5, 41, 3], [29, 11, 41, 9], [29, 15, 41, 13, "entry"], [29, 20, 41, 18], [29, 21, 41, 19, "entryType"], [29, 30, 41, 28], [29, 35, 41, 33, "RawPerformanceEntryTypeValues"], [29, 64, 41, 62], [29, 65, 41, 63, "LONGTASK"], [29, 73, 41, 71], [29, 75, 41, 73], [30, 6, 42, 4], [30, 13, 42, 11], [30, 17, 42, 15, "PerformanceLongTaskTiming"], [30, 53, 42, 40], [30, 54, 42, 41], [31, 8, 43, 6, "name"], [31, 12, 43, 10], [31, 14, 43, 12, "entry"], [31, 19, 43, 17], [31, 20, 43, 18, "name"], [31, 24, 43, 22], [32, 8, 44, 6, "entryType"], [32, 17, 44, 15], [32, 19, 44, 17, "rawToPerformanceEntryType"], [32, 44, 44, 42], [32, 45, 44, 43, "entry"], [32, 50, 44, 48], [32, 51, 44, 49, "entryType"], [32, 60, 44, 58], [32, 61, 44, 59], [33, 8, 45, 6, "startTime"], [33, 17, 45, 15], [33, 19, 45, 17, "entry"], [33, 24, 45, 22], [33, 25, 45, 23, "startTime"], [33, 34, 45, 32], [34, 8, 46, 6, "duration"], [34, 16, 46, 14], [34, 18, 46, 16, "entry"], [34, 23, 46, 21], [34, 24, 46, 22, "duration"], [35, 6, 47, 4], [35, 7, 47, 5], [35, 8, 47, 6], [36, 4, 48, 2], [36, 5, 48, 3], [36, 11, 48, 9], [36, 15, 48, 13, "entry"], [36, 20, 48, 18], [36, 21, 48, 19, "entryType"], [36, 30, 48, 28], [36, 35, 48, 33, "RawPerformanceEntryTypeValues"], [36, 64, 48, 62], [36, 65, 48, 63, "MARK"], [36, 69, 48, 67], [36, 71, 48, 69], [37, 6, 49, 4], [37, 13, 49, 11], [37, 17, 49, 15, "PerformanceMark"], [37, 44, 49, 30], [37, 45, 49, 31, "entry"], [37, 50, 49, 36], [37, 51, 49, 37, "name"], [37, 55, 49, 41], [37, 57, 49, 43], [38, 8, 50, 6, "startTime"], [38, 17, 50, 15], [38, 19, 50, 17, "entry"], [38, 24, 50, 22], [38, 25, 50, 23, "startTime"], [39, 6, 51, 4], [39, 7, 51, 5], [39, 8, 51, 6], [40, 4, 52, 2], [40, 5, 52, 3], [40, 11, 52, 9], [40, 15, 52, 13, "entry"], [40, 20, 52, 18], [40, 21, 52, 19, "entryType"], [40, 30, 52, 28], [40, 35, 52, 33, "RawPerformanceEntryTypeValues"], [40, 64, 52, 62], [40, 65, 52, 63, "MEASURE"], [40, 72, 52, 70], [40, 74, 52, 72], [41, 6, 53, 4], [41, 13, 53, 11], [41, 17, 53, 15, "PerformanceMeasure"], [41, 47, 53, 33], [41, 48, 53, 34, "entry"], [41, 53, 53, 39], [41, 54, 53, 40, "name"], [41, 58, 53, 44], [41, 60, 53, 46], [42, 8, 54, 6, "startTime"], [42, 17, 54, 15], [42, 19, 54, 17, "entry"], [42, 24, 54, 22], [42, 25, 54, 23, "startTime"], [42, 34, 54, 32], [43, 8, 55, 6, "duration"], [43, 16, 55, 14], [43, 18, 55, 16, "entry"], [43, 23, 55, 21], [43, 24, 55, 22, "duration"], [44, 6, 56, 4], [44, 7, 56, 5], [44, 8, 56, 6], [45, 4, 57, 2], [45, 5, 57, 3], [45, 11, 57, 9], [46, 6, 58, 4], [46, 13, 58, 11], [46, 17, 58, 15, "PerformanceEntry"], [46, 51, 58, 31], [46, 52, 58, 32], [47, 8, 59, 6, "name"], [47, 12, 59, 10], [47, 14, 59, 12, "entry"], [47, 19, 59, 17], [47, 20, 59, 18, "name"], [47, 24, 59, 22], [48, 8, 60, 6, "entryType"], [48, 17, 60, 15], [48, 19, 60, 17, "rawToPerformanceEntryType"], [48, 44, 60, 42], [48, 45, 60, 43, "entry"], [48, 50, 60, 48], [48, 51, 60, 49, "entryType"], [48, 60, 60, 58], [48, 61, 60, 59], [49, 8, 61, 6, "startTime"], [49, 17, 61, 15], [49, 19, 61, 17, "entry"], [49, 24, 61, 22], [49, 25, 61, 23, "startTime"], [49, 34, 61, 32], [50, 8, 62, 6, "duration"], [50, 16, 62, 14], [50, 18, 62, 16, "entry"], [50, 23, 62, 21], [50, 24, 62, 22, "duration"], [51, 6, 63, 4], [51, 7, 63, 5], [51, 8, 63, 6], [52, 4, 64, 2], [53, 2, 65, 0], [54, 2, 67, 7], [54, 11, 67, 16, "rawToPerformanceEntryType"], [54, 36, 67, 41, "rawToPerformanceEntryType"], [54, 37, 68, 2, "type"], [54, 41, 68, 31], [54, 43, 69, 24], [55, 4, 70, 2], [55, 12, 70, 10, "type"], [55, 16, 70, 14], [56, 6, 71, 4], [56, 11, 71, 9, "RawPerformanceEntryTypeValues"], [56, 40, 71, 38], [56, 41, 71, 39, "MARK"], [56, 45, 71, 43], [57, 8, 72, 6], [57, 15, 72, 13], [57, 21, 72, 19], [58, 6, 73, 4], [58, 11, 73, 9, "RawPerformanceEntryTypeValues"], [58, 40, 73, 38], [58, 41, 73, 39, "MEASURE"], [58, 48, 73, 46], [59, 8, 74, 6], [59, 15, 74, 13], [59, 24, 74, 22], [60, 6, 75, 4], [60, 11, 75, 9, "RawPerformanceEntryTypeValues"], [60, 40, 75, 38], [60, 41, 75, 39, "EVENT"], [60, 46, 75, 44], [61, 8, 76, 6], [61, 15, 76, 13], [61, 22, 76, 20], [62, 6, 77, 4], [62, 11, 77, 9, "RawPerformanceEntryTypeValues"], [62, 40, 77, 38], [62, 41, 77, 39, "LONGTASK"], [62, 49, 77, 47], [63, 8, 78, 6], [63, 15, 78, 13], [63, 25, 78, 23], [64, 6, 79, 4], [65, 8, 80, 6], [65, 14, 80, 12], [65, 18, 80, 16, "TypeError"], [65, 27, 80, 25], [65, 28, 81, 8], [65, 102, 81, 82, "type"], [65, 106, 81, 86], [65, 108, 82, 6], [65, 109, 82, 7], [66, 4, 83, 2], [67, 2, 84, 0], [68, 2, 86, 7], [68, 11, 86, 16, "performanceEntryTypeToRaw"], [68, 36, 86, 41, "performanceEntryTypeToRaw"], [68, 37, 87, 2, "type"], [68, 41, 87, 28], [68, 43, 88, 27], [69, 4, 89, 2], [69, 12, 89, 10, "type"], [69, 16, 89, 14], [70, 6, 90, 4], [70, 11, 90, 9], [70, 17, 90, 15], [71, 8, 91, 6], [71, 15, 91, 13, "RawPerformanceEntryTypeValues"], [71, 44, 91, 42], [71, 45, 91, 43, "MARK"], [71, 49, 91, 47], [72, 6, 92, 4], [72, 11, 92, 9], [72, 20, 92, 18], [73, 8, 93, 6], [73, 15, 93, 13, "RawPerformanceEntryTypeValues"], [73, 44, 93, 42], [73, 45, 93, 43, "MEASURE"], [73, 52, 93, 50], [74, 6, 94, 4], [74, 11, 94, 9], [74, 18, 94, 16], [75, 8, 95, 6], [75, 15, 95, 13, "RawPerformanceEntryTypeValues"], [75, 44, 95, 42], [75, 45, 95, 43, "EVENT"], [75, 50, 95, 48], [76, 6, 96, 4], [76, 11, 96, 9], [76, 21, 96, 19], [77, 8, 97, 6], [77, 15, 97, 13, "RawPerformanceEntryTypeValues"], [77, 44, 97, 42], [77, 45, 97, 43, "LONGTASK"], [77, 53, 97, 51], [78, 6, 98, 4], [79, 8, 100, 7, "type"], [79, 12, 100, 11], [80, 8, 101, 6], [80, 14, 101, 12], [80, 18, 101, 16, "TypeError"], [80, 27, 101, 25], [80, 28, 102, 8], [80, 102, 102, 82, "type"], [80, 106, 102, 86], [80, 108, 103, 6], [80, 109, 103, 7], [81, 4, 104, 2], [82, 2, 105, 0], [83, 0, 105, 1], [83, 3]], "functionMap": {"names": ["<global>", "rawToPerformanceEntry", "rawToPerformanceEntryType", "performanceEntryTypeToRaw"], "mappings": "AAA;OC4B;CDoC;OEE;CFiB;OGE"}}, "type": "js/module"}]}