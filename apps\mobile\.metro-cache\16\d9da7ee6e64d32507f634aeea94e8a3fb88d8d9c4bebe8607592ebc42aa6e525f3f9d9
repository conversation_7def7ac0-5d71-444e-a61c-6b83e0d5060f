{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 72, "column": 16, "index": 2464}, "end": {"line": 72, "column": 32, "index": 2480}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * @license React\n   * use-sync-external-store-shim.native.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  \"use strict\";\n\n  \"production\" !== process.env.NODE_ENV && function () {\n    function is(x, y) {\n      return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      didWarnOld18Alpha || void 0 === React.startTransition || (didWarnOld18Alpha = !0, console.error(\"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) || (console.error(\"The result of getSnapshot should be cached to avoid an infinite loop\"), didWarnUncachedGetSnapshot = !0);\n      }\n      cachedValue = useState({\n        inst: {\n          value: value,\n          getSnapshot: getSnapshot\n        }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(function () {\n        inst.value = value;\n        inst.getSnapshot = getSnapshot;\n        checkIfSnapshotChanged(inst) && forceUpdate({\n          inst: inst\n        });\n      }, [subscribe, value, getSnapshot]);\n      useEffect(function () {\n        checkIfSnapshotChanged(inst) && forceUpdate({\n          inst: inst\n        });\n        return subscribe(function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({\n            inst: inst\n          });\n        });\n      }, [subscribe]);\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(_dependencyMap[0], \"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1;\n    exports.useSyncExternalStore = void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : useSyncExternalStore$1;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  }();\n});", "lineCount": 75, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 12, 0], [14, 14, 12, 12], [14, 19, 12, 17, "process"], [14, 26, 12, 24], [14, 27, 12, 25, "env"], [14, 30, 12, 28], [14, 31, 12, 29, "NODE_ENV"], [14, 39, 12, 37], [14, 43, 13, 3], [14, 55, 13, 15], [15, 4, 14, 4], [15, 13, 14, 13, "is"], [15, 15, 14, 15, "is"], [15, 16, 14, 16, "x"], [15, 17, 14, 17], [15, 19, 14, 19, "y"], [15, 20, 14, 20], [15, 22, 14, 22], [16, 6, 15, 6], [16, 13, 15, 14, "x"], [16, 14, 15, 15], [16, 19, 15, 20, "y"], [16, 20, 15, 21], [16, 25, 15, 26], [16, 26, 15, 27], [16, 31, 15, 32, "x"], [16, 32, 15, 33], [16, 36, 15, 37], [16, 37, 15, 38], [16, 40, 15, 41, "x"], [16, 41, 15, 42], [16, 46, 15, 47], [16, 47, 15, 48], [16, 50, 15, 51, "y"], [16, 51, 15, 52], [16, 52, 15, 53], [16, 56, 15, 59, "x"], [16, 57, 15, 60], [16, 62, 15, 65, "x"], [16, 63, 15, 66], [16, 67, 15, 70, "y"], [16, 68, 15, 71], [16, 73, 15, 76, "y"], [16, 74, 15, 78], [17, 4, 16, 4], [18, 4, 17, 4], [18, 13, 17, 13, "useSyncExternalStore$1"], [18, 35, 17, 35, "useSyncExternalStore$1"], [18, 36, 17, 36, "subscribe"], [18, 45, 17, 45], [18, 47, 17, 47, "getSnapshot"], [18, 58, 17, 58], [18, 60, 17, 60], [19, 6, 18, 6, "didWarnOld18Alpha"], [19, 23, 18, 23], [19, 27, 19, 8], [19, 32, 19, 13], [19, 33, 19, 14], [19, 38, 19, 19, "React"], [19, 43, 19, 24], [19, 44, 19, 25, "startTransition"], [19, 59, 19, 40], [19, 64, 20, 10, "didWarnOld18Alpha"], [19, 81, 20, 27], [19, 84, 20, 30], [19, 85, 20, 31], [19, 86, 20, 32], [19, 88, 21, 8, "console"], [19, 95, 21, 15], [19, 96, 21, 16, "error"], [19, 101, 21, 21], [19, 102, 22, 10], [19, 294, 23, 8], [19, 295, 23, 9], [19, 296, 23, 10], [20, 6, 24, 6], [20, 10, 24, 10, "value"], [20, 15, 24, 15], [20, 18, 24, 18, "getSnapshot"], [20, 29, 24, 29], [20, 30, 24, 30], [20, 31, 24, 31], [21, 6, 25, 6], [21, 10, 25, 10], [21, 11, 25, 11, "didWarnUncachedGetSnapshot"], [21, 37, 25, 37], [21, 39, 25, 39], [22, 8, 26, 8], [22, 12, 26, 12, "cachedValue"], [22, 23, 26, 23], [22, 26, 26, 26, "getSnapshot"], [22, 37, 26, 37], [22, 38, 26, 38], [22, 39, 26, 39], [23, 8, 27, 8, "objectIs"], [23, 16, 27, 16], [23, 17, 27, 17, "value"], [23, 22, 27, 22], [23, 24, 27, 24, "cachedValue"], [23, 35, 27, 35], [23, 36, 27, 36], [23, 41, 28, 11, "console"], [23, 48, 28, 18], [23, 49, 28, 19, "error"], [23, 54, 28, 24], [23, 55, 29, 12], [23, 125, 30, 10], [23, 126, 30, 11], [23, 128, 31, 11, "didWarnUncachedGetSnapshot"], [23, 154, 31, 37], [23, 157, 31, 40], [23, 158, 31, 41], [23, 159, 31, 43], [23, 160, 31, 44], [24, 6, 32, 6], [25, 6, 33, 6, "cachedValue"], [25, 17, 33, 17], [25, 20, 33, 20, "useState"], [25, 28, 33, 28], [25, 29, 33, 29], [26, 8, 34, 8, "inst"], [26, 12, 34, 12], [26, 14, 34, 14], [27, 10, 34, 16, "value"], [27, 15, 34, 21], [27, 17, 34, 23, "value"], [27, 22, 34, 28], [28, 10, 34, 30, "getSnapshot"], [28, 21, 34, 41], [28, 23, 34, 43, "getSnapshot"], [29, 8, 34, 55], [30, 6, 35, 6], [30, 7, 35, 7], [30, 8, 35, 8], [31, 6, 36, 6], [31, 10, 36, 10, "inst"], [31, 14, 36, 14], [31, 17, 36, 17, "cachedValue"], [31, 28, 36, 28], [31, 29, 36, 29], [31, 30, 36, 30], [31, 31, 36, 31], [31, 32, 36, 32, "inst"], [31, 36, 36, 36], [32, 8, 37, 8, "forceUpdate"], [32, 19, 37, 19], [32, 22, 37, 22, "cachedValue"], [32, 33, 37, 33], [32, 34, 37, 34], [32, 35, 37, 35], [32, 36, 37, 36], [33, 6, 38, 6, "useLayoutEffect"], [33, 21, 38, 21], [33, 22, 39, 8], [33, 34, 39, 20], [34, 8, 40, 10, "inst"], [34, 12, 40, 14], [34, 13, 40, 15, "value"], [34, 18, 40, 20], [34, 21, 40, 23, "value"], [34, 26, 40, 28], [35, 8, 41, 10, "inst"], [35, 12, 41, 14], [35, 13, 41, 15, "getSnapshot"], [35, 24, 41, 26], [35, 27, 41, 29, "getSnapshot"], [35, 38, 41, 40], [36, 8, 42, 10, "checkIfSnapshotChanged"], [36, 30, 42, 32], [36, 31, 42, 33, "inst"], [36, 35, 42, 37], [36, 36, 42, 38], [36, 40, 42, 42, "forceUpdate"], [36, 51, 42, 53], [36, 52, 42, 54], [37, 10, 42, 56, "inst"], [37, 14, 42, 60], [37, 16, 42, 62, "inst"], [38, 8, 42, 67], [38, 9, 42, 68], [38, 10, 42, 69], [39, 6, 43, 8], [39, 7, 43, 9], [39, 9, 44, 8], [39, 10, 44, 9, "subscribe"], [39, 19, 44, 18], [39, 21, 44, 20, "value"], [39, 26, 44, 25], [39, 28, 44, 27, "getSnapshot"], [39, 39, 44, 38], [39, 40, 45, 6], [39, 41, 45, 7], [40, 6, 46, 6, "useEffect"], [40, 15, 46, 15], [40, 16, 47, 8], [40, 28, 47, 20], [41, 8, 48, 10, "checkIfSnapshotChanged"], [41, 30, 48, 32], [41, 31, 48, 33, "inst"], [41, 35, 48, 37], [41, 36, 48, 38], [41, 40, 48, 42, "forceUpdate"], [41, 51, 48, 53], [41, 52, 48, 54], [42, 10, 48, 56, "inst"], [42, 14, 48, 60], [42, 16, 48, 62, "inst"], [43, 8, 48, 67], [43, 9, 48, 68], [43, 10, 48, 69], [44, 8, 49, 10], [44, 15, 49, 17, "subscribe"], [44, 24, 49, 26], [44, 25, 49, 27], [44, 37, 49, 39], [45, 10, 50, 12, "checkIfSnapshotChanged"], [45, 32, 50, 34], [45, 33, 50, 35, "inst"], [45, 37, 50, 39], [45, 38, 50, 40], [45, 42, 50, 44, "forceUpdate"], [45, 53, 50, 55], [45, 54, 50, 56], [46, 12, 50, 58, "inst"], [46, 16, 50, 62], [46, 18, 50, 64, "inst"], [47, 10, 50, 69], [47, 11, 50, 70], [47, 12, 50, 71], [48, 8, 51, 10], [48, 9, 51, 11], [48, 10, 51, 12], [49, 6, 52, 8], [49, 7, 52, 9], [49, 9, 53, 8], [49, 10, 53, 9, "subscribe"], [49, 19, 53, 18], [49, 20, 54, 6], [49, 21, 54, 7], [50, 6, 55, 6, "useDebugValue"], [50, 19, 55, 19], [50, 20, 55, 20, "value"], [50, 25, 55, 25], [50, 26, 55, 26], [51, 6, 56, 6], [51, 13, 56, 13, "value"], [51, 18, 56, 18], [52, 4, 57, 4], [53, 4, 58, 4], [53, 13, 58, 13, "checkIfSnapshotChanged"], [53, 35, 58, 35, "checkIfSnapshotChanged"], [53, 36, 58, 36, "inst"], [53, 40, 58, 40], [53, 42, 58, 42], [54, 6, 59, 6], [54, 10, 59, 10, "latestGetSnapshot"], [54, 27, 59, 27], [54, 30, 59, 30, "inst"], [54, 34, 59, 34], [54, 35, 59, 35, "getSnapshot"], [54, 46, 59, 46], [55, 6, 60, 6, "inst"], [55, 10, 60, 10], [55, 13, 60, 13, "inst"], [55, 17, 60, 17], [55, 18, 60, 18, "value"], [55, 23, 60, 23], [56, 6, 61, 6], [56, 10, 61, 10], [57, 8, 62, 8], [57, 12, 62, 12, "nextValue"], [57, 21, 62, 21], [57, 24, 62, 24, "latestGetSnapshot"], [57, 41, 62, 41], [57, 42, 62, 42], [57, 43, 62, 43], [58, 8, 63, 8], [58, 15, 63, 15], [58, 16, 63, 16, "objectIs"], [58, 24, 63, 24], [58, 25, 63, 25, "inst"], [58, 29, 63, 29], [58, 31, 63, 31, "nextValue"], [58, 40, 63, 40], [58, 41, 63, 41], [59, 6, 64, 6], [59, 7, 64, 7], [59, 8, 64, 8], [59, 15, 64, 15, "error"], [59, 20, 64, 20], [59, 22, 64, 22], [60, 8, 65, 8], [60, 15, 65, 15], [60, 16, 65, 16], [60, 17, 65, 17], [61, 6, 66, 6], [62, 4, 67, 4], [63, 4, 68, 4], [63, 15, 68, 15], [63, 20, 68, 20], [63, 27, 68, 27, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [63, 57, 68, 57], [63, 61, 69, 6], [63, 71, 69, 16], [63, 76, 70, 8], [63, 83, 70, 15, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [63, 113, 70, 45], [63, 114, 70, 46, "registerInternalModuleStart"], [63, 141, 70, 73], [63, 145, 71, 6, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [63, 175, 71, 36], [63, 176, 71, 37, "registerInternalModuleStart"], [63, 203, 71, 64], [63, 204, 71, 65, "Error"], [63, 209, 71, 70], [63, 210, 71, 71], [63, 211, 71, 72], [63, 212, 71, 73], [64, 4, 72, 4], [64, 8, 72, 8, "React"], [64, 13, 72, 13], [64, 16, 72, 16, "require"], [64, 23, 72, 23], [64, 24, 72, 23, "_dependencyMap"], [64, 38, 72, 23], [64, 50, 72, 31], [64, 51, 72, 32], [65, 6, 73, 6, "objectIs"], [65, 14, 73, 14], [65, 17, 73, 17], [65, 27, 73, 27], [65, 32, 73, 32], [65, 39, 73, 39, "Object"], [65, 45, 73, 45], [65, 46, 73, 46, "is"], [65, 48, 73, 48], [65, 51, 73, 51, "Object"], [65, 57, 73, 57], [65, 58, 73, 58, "is"], [65, 60, 73, 60], [65, 63, 73, 63, "is"], [65, 65, 73, 65], [66, 6, 74, 6, "useState"], [66, 14, 74, 14], [66, 17, 74, 17, "React"], [66, 22, 74, 22], [66, 23, 74, 23, "useState"], [66, 31, 74, 31], [67, 6, 75, 6, "useEffect"], [67, 15, 75, 15], [67, 18, 75, 18, "React"], [67, 23, 75, 23], [67, 24, 75, 24, "useEffect"], [67, 33, 75, 33], [68, 6, 76, 6, "useLayoutEffect"], [68, 21, 76, 21], [68, 24, 76, 24, "React"], [68, 29, 76, 29], [68, 30, 76, 30, "useLayoutEffect"], [68, 45, 76, 45], [69, 6, 77, 6, "useDebugValue"], [69, 19, 77, 19], [69, 22, 77, 22, "React"], [69, 27, 77, 27], [69, 28, 77, 28, "useDebugValue"], [69, 41, 77, 41], [70, 6, 78, 6, "didWarnOld18Alpha"], [70, 23, 78, 23], [70, 26, 78, 26], [70, 27, 78, 27], [70, 28, 78, 28], [71, 6, 79, 6, "didWarnUncachedGetSnapshot"], [71, 32, 79, 32], [71, 35, 79, 35], [71, 36, 79, 36], [71, 37, 79, 37], [72, 4, 80, 4, "exports"], [72, 11, 80, 11], [72, 12, 80, 12, "useSyncExternalStore"], [72, 32, 80, 32], [72, 35, 81, 6], [72, 40, 81, 11], [72, 41, 81, 12], [72, 46, 81, 17, "React"], [72, 51, 81, 22], [72, 52, 81, 23, "useSyncExternalStore"], [72, 72, 81, 43], [72, 75, 82, 10, "React"], [72, 80, 82, 15], [72, 81, 82, 16, "useSyncExternalStore"], [72, 101, 82, 36], [72, 104, 83, 10, "useSyncExternalStore$1"], [72, 126, 83, 32], [73, 4, 84, 4], [73, 15, 84, 15], [73, 20, 84, 20], [73, 27, 84, 27, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [73, 57, 84, 57], [73, 61, 85, 6], [73, 71, 85, 16], [73, 76, 86, 8], [73, 83, 86, 15, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [73, 113, 86, 45], [73, 114, 86, 46, "registerInternalModuleStop"], [73, 140, 86, 72], [73, 144, 87, 6, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [73, 174, 87, 36], [73, 175, 87, 37, "registerInternalModuleStop"], [73, 201, 87, 63], [73, 202, 87, 64, "Error"], [73, 207, 87, 69], [73, 208, 87, 70], [73, 209, 87, 71], [73, 210, 87, 72], [74, 2, 88, 2], [74, 3, 88, 3], [74, 4, 88, 5], [74, 5, 88, 6], [75, 0, 88, 7], [75, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "is", "useSyncExternalStore$1", "useLayoutEffect$argument_0", "useEffect$argument_0", "subscribe$argument_0", "checkIfSnapshotChanged"], "mappings": "AAA;GCY;ICC;KDE;IEC;QCsB;SDI;QEI;2BCE;WDE;SFC;KFK;IMC;KNS;GDqB"}}, "type": "js/module"}]}