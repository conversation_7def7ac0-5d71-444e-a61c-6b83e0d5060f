{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 113, "index": 128}}], "key": "Wm75LgE4xYscVWo0KoLFlflJQCo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 129}, "end": {"line": 4, "column": 31, "index": 160}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationContainer.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 161}, "end": {"line": 5, "column": 63, "index": 224}}], "key": "LB6hXm0TqYVluaSOGRsC2pfPato=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 225}, "end": {"line": 6, "column": 48, "index": 273}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createStaticNavigation = createStaticNavigation;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _core = require(_dependencyMap[2], \"@react-navigation/core\");\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _NavigationContainer = require(_dependencyMap[4], \"./NavigationContainer.js\");\n  var _jsxRuntime = require(_dependencyMap[5], \"react/jsx-runtime\");\n  var _excluded = [\"linking\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Create a navigation component from a static navigation config.\n   * The returned component is a wrapper around `NavigationContainer`.\n   *\n   * @param tree Static navigation config.\n   * @returns Navigation component to use in your app.\n   */\n  function createStaticNavigation(tree) {\n    var Component = (0, _core.createComponentForStaticNavigation)(tree, 'RootNavigator');\n    function Navigation(_ref, ref) {\n      var linking = _ref.linking,\n        rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n      var linkingConfig = React.useMemo(() => {\n        var screens = (0, _core.createPathConfigForStaticNavigation)(tree, {\n          initialRouteName: linking?.config?.initialRouteName\n        }, linking?.enabled === 'auto');\n        if (!screens) return;\n        return {\n          path: linking?.config?.path,\n          initialRouteName: linking?.config?.initialRouteName,\n          screens\n        };\n      }, [linking?.enabled, linking?.config?.path, linking?.config?.initialRouteName]);\n      var memoizedLinking = React.useMemo(() => {\n        if (!linking) {\n          return undefined;\n        }\n        var enabled = typeof linking.enabled === 'boolean' ? linking.enabled : linkingConfig?.screens != null;\n        return {\n          ...linking,\n          enabled,\n          config: linkingConfig\n        };\n      }, [linking, linkingConfig]);\n      if (linking?.enabled === true && linkingConfig?.screens == null) {\n        throw new Error('Linking is enabled but no linking configuration was found for the screens.\\n\\n' + 'To solve this:\\n' + \"- Specify a 'linking' property for the screens you want to link to.\\n\" + \"- Or set 'linking.enabled' to 'auto' to generate paths automatically.\\n\\n\" + 'See usage guide: https://reactnavigation.org/docs/static-configuration#linking');\n      }\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationContainer.NavigationContainer, {\n        ...rest,\n        ref: ref,\n        linking: memoizedLinking,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(Component, {})\n      });\n    }\n    return /*#__PURE__*/React.forwardRef(Navigation);\n  }\n});", "lineCount": 62, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "createStaticNavigation"], [8, 32, 1, 13], [8, 35, 1, 13, "createStaticNavigation"], [8, 57, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_core"], [10, 11, 3, 0], [10, 14, 3, 0, "require"], [10, 21, 3, 0], [10, 22, 3, 0, "_dependencyMap"], [10, 36, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "React"], [11, 11, 4, 0], [11, 14, 4, 0, "_interopRequireWildcard"], [11, 37, 4, 0], [11, 38, 4, 0, "require"], [11, 45, 4, 0], [11, 46, 4, 0, "_dependencyMap"], [11, 60, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_NavigationContainer"], [12, 26, 5, 0], [12, 29, 5, 0, "require"], [12, 36, 5, 0], [12, 37, 5, 0, "_dependencyMap"], [12, 51, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_jsxRuntime"], [13, 17, 6, 0], [13, 20, 6, 0, "require"], [13, 27, 6, 0], [13, 28, 6, 0, "_dependencyMap"], [13, 42, 6, 0], [14, 2, 6, 48], [14, 6, 6, 48, "_excluded"], [14, 15, 6, 48], [15, 2, 6, 48], [15, 11, 6, 48, "_interopRequireWildcard"], [15, 35, 6, 48, "e"], [15, 36, 6, 48], [15, 38, 6, 48, "t"], [15, 39, 6, 48], [15, 68, 6, 48, "WeakMap"], [15, 75, 6, 48], [15, 81, 6, 48, "r"], [15, 82, 6, 48], [15, 89, 6, 48, "WeakMap"], [15, 96, 6, 48], [15, 100, 6, 48, "n"], [15, 101, 6, 48], [15, 108, 6, 48, "WeakMap"], [15, 115, 6, 48], [15, 127, 6, 48, "_interopRequireWildcard"], [15, 150, 6, 48], [15, 162, 6, 48, "_interopRequireWildcard"], [15, 163, 6, 48, "e"], [15, 164, 6, 48], [15, 166, 6, 48, "t"], [15, 167, 6, 48], [15, 176, 6, 48, "t"], [15, 177, 6, 48], [15, 181, 6, 48, "e"], [15, 182, 6, 48], [15, 186, 6, 48, "e"], [15, 187, 6, 48], [15, 188, 6, 48, "__esModule"], [15, 198, 6, 48], [15, 207, 6, 48, "e"], [15, 208, 6, 48], [15, 214, 6, 48, "o"], [15, 215, 6, 48], [15, 217, 6, 48, "i"], [15, 218, 6, 48], [15, 220, 6, 48, "f"], [15, 221, 6, 48], [15, 226, 6, 48, "__proto__"], [15, 235, 6, 48], [15, 243, 6, 48, "default"], [15, 250, 6, 48], [15, 252, 6, 48, "e"], [15, 253, 6, 48], [15, 270, 6, 48, "e"], [15, 271, 6, 48], [15, 294, 6, 48, "e"], [15, 295, 6, 48], [15, 320, 6, 48, "e"], [15, 321, 6, 48], [15, 330, 6, 48, "f"], [15, 331, 6, 48], [15, 337, 6, 48, "o"], [15, 338, 6, 48], [15, 341, 6, 48, "t"], [15, 342, 6, 48], [15, 345, 6, 48, "n"], [15, 346, 6, 48], [15, 349, 6, 48, "r"], [15, 350, 6, 48], [15, 358, 6, 48, "o"], [15, 359, 6, 48], [15, 360, 6, 48, "has"], [15, 363, 6, 48], [15, 364, 6, 48, "e"], [15, 365, 6, 48], [15, 375, 6, 48, "o"], [15, 376, 6, 48], [15, 377, 6, 48, "get"], [15, 380, 6, 48], [15, 381, 6, 48, "e"], [15, 382, 6, 48], [15, 385, 6, 48, "o"], [15, 386, 6, 48], [15, 387, 6, 48, "set"], [15, 390, 6, 48], [15, 391, 6, 48, "e"], [15, 392, 6, 48], [15, 394, 6, 48, "f"], [15, 395, 6, 48], [15, 409, 6, 48, "_t"], [15, 411, 6, 48], [15, 415, 6, 48, "e"], [15, 416, 6, 48], [15, 432, 6, 48, "_t"], [15, 434, 6, 48], [15, 441, 6, 48, "hasOwnProperty"], [15, 455, 6, 48], [15, 456, 6, 48, "call"], [15, 460, 6, 48], [15, 461, 6, 48, "e"], [15, 462, 6, 48], [15, 464, 6, 48, "_t"], [15, 466, 6, 48], [15, 473, 6, 48, "i"], [15, 474, 6, 48], [15, 478, 6, 48, "o"], [15, 479, 6, 48], [15, 482, 6, 48, "Object"], [15, 488, 6, 48], [15, 489, 6, 48, "defineProperty"], [15, 503, 6, 48], [15, 508, 6, 48, "Object"], [15, 514, 6, 48], [15, 515, 6, 48, "getOwnPropertyDescriptor"], [15, 539, 6, 48], [15, 540, 6, 48, "e"], [15, 541, 6, 48], [15, 543, 6, 48, "_t"], [15, 545, 6, 48], [15, 552, 6, 48, "i"], [15, 553, 6, 48], [15, 554, 6, 48, "get"], [15, 557, 6, 48], [15, 561, 6, 48, "i"], [15, 562, 6, 48], [15, 563, 6, 48, "set"], [15, 566, 6, 48], [15, 570, 6, 48, "o"], [15, 571, 6, 48], [15, 572, 6, 48, "f"], [15, 573, 6, 48], [15, 575, 6, 48, "_t"], [15, 577, 6, 48], [15, 579, 6, 48, "i"], [15, 580, 6, 48], [15, 584, 6, 48, "f"], [15, 585, 6, 48], [15, 586, 6, 48, "_t"], [15, 588, 6, 48], [15, 592, 6, 48, "e"], [15, 593, 6, 48], [15, 594, 6, 48, "_t"], [15, 596, 6, 48], [15, 607, 6, 48, "f"], [15, 608, 6, 48], [15, 613, 6, 48, "e"], [15, 614, 6, 48], [15, 616, 6, 48, "t"], [15, 617, 6, 48], [16, 2, 7, 0], [17, 0, 8, 0], [18, 0, 9, 0], [19, 0, 10, 0], [20, 0, 11, 0], [21, 0, 12, 0], [22, 0, 13, 0], [23, 2, 14, 7], [23, 11, 14, 16, "createStaticNavigation"], [23, 33, 14, 38, "createStaticNavigation"], [23, 34, 14, 39, "tree"], [23, 38, 14, 43], [23, 40, 14, 45], [24, 4, 15, 2], [24, 8, 15, 8, "Component"], [24, 17, 15, 17], [24, 20, 15, 20], [24, 24, 15, 20, "createComponentForStaticNavigation"], [24, 64, 15, 54], [24, 66, 15, 55, "tree"], [24, 70, 15, 59], [24, 72, 15, 61], [24, 87, 15, 76], [24, 88, 15, 77], [25, 4, 16, 2], [25, 13, 16, 11, "Navigation"], [25, 23, 16, 21, "Navigation"], [25, 24, 16, 21, "_ref"], [25, 28, 16, 21], [25, 30, 19, 5, "ref"], [25, 33, 19, 8], [25, 35, 19, 10], [26, 6, 19, 10], [26, 10, 17, 4, "linking"], [26, 17, 17, 11], [26, 20, 17, 11, "_ref"], [26, 24, 17, 11], [26, 25, 17, 4, "linking"], [26, 32, 17, 11], [27, 8, 18, 7, "rest"], [27, 12, 18, 11], [27, 19, 18, 11, "_objectWithoutProperties2"], [27, 44, 18, 11], [27, 45, 18, 11, "default"], [27, 52, 18, 11], [27, 54, 18, 11, "_ref"], [27, 58, 18, 11], [27, 60, 18, 11, "_excluded"], [27, 69, 18, 11], [28, 6, 20, 4], [28, 10, 20, 10, "linkingConfig"], [28, 23, 20, 23], [28, 26, 20, 26, "React"], [28, 31, 20, 31], [28, 32, 20, 32, "useMemo"], [28, 39, 20, 39], [28, 40, 20, 40], [28, 46, 20, 46], [29, 8, 21, 6], [29, 12, 21, 12, "screens"], [29, 19, 21, 19], [29, 22, 21, 22], [29, 26, 21, 22, "createPathConfigForStaticNavigation"], [29, 67, 21, 57], [29, 69, 21, 58, "tree"], [29, 73, 21, 62], [29, 75, 21, 64], [30, 10, 22, 8, "initialRouteName"], [30, 26, 22, 24], [30, 28, 22, 26, "linking"], [30, 35, 22, 33], [30, 37, 22, 35, "config"], [30, 43, 22, 41], [30, 45, 22, 43, "initialRouteName"], [31, 8, 23, 6], [31, 9, 23, 7], [31, 11, 23, 9, "linking"], [31, 18, 23, 16], [31, 20, 23, 18, "enabled"], [31, 27, 23, 25], [31, 32, 23, 30], [31, 38, 23, 36], [31, 39, 23, 37], [32, 8, 24, 6], [32, 12, 24, 10], [32, 13, 24, 11, "screens"], [32, 20, 24, 18], [32, 22, 24, 20], [33, 8, 25, 6], [33, 15, 25, 13], [34, 10, 26, 8, "path"], [34, 14, 26, 12], [34, 16, 26, 14, "linking"], [34, 23, 26, 21], [34, 25, 26, 23, "config"], [34, 31, 26, 29], [34, 33, 26, 31, "path"], [34, 37, 26, 35], [35, 10, 27, 8, "initialRouteName"], [35, 26, 27, 24], [35, 28, 27, 26, "linking"], [35, 35, 27, 33], [35, 37, 27, 35, "config"], [35, 43, 27, 41], [35, 45, 27, 43, "initialRouteName"], [35, 61, 27, 59], [36, 10, 28, 8, "screens"], [37, 8, 29, 6], [37, 9, 29, 7], [38, 6, 30, 4], [38, 7, 30, 5], [38, 9, 30, 7], [38, 10, 30, 8, "linking"], [38, 17, 30, 15], [38, 19, 30, 17, "enabled"], [38, 26, 30, 24], [38, 28, 30, 26, "linking"], [38, 35, 30, 33], [38, 37, 30, 35, "config"], [38, 43, 30, 41], [38, 45, 30, 43, "path"], [38, 49, 30, 47], [38, 51, 30, 49, "linking"], [38, 58, 30, 56], [38, 60, 30, 58, "config"], [38, 66, 30, 64], [38, 68, 30, 66, "initialRouteName"], [38, 84, 30, 82], [38, 85, 30, 83], [38, 86, 30, 84], [39, 6, 31, 4], [39, 10, 31, 10, "memoizedLinking"], [39, 25, 31, 25], [39, 28, 31, 28, "React"], [39, 33, 31, 33], [39, 34, 31, 34, "useMemo"], [39, 41, 31, 41], [39, 42, 31, 42], [39, 48, 31, 48], [40, 8, 32, 6], [40, 12, 32, 10], [40, 13, 32, 11, "linking"], [40, 20, 32, 18], [40, 22, 32, 20], [41, 10, 33, 8], [41, 17, 33, 15, "undefined"], [41, 26, 33, 24], [42, 8, 34, 6], [43, 8, 35, 6], [43, 12, 35, 12, "enabled"], [43, 19, 35, 19], [43, 22, 35, 22], [43, 29, 35, 29, "linking"], [43, 36, 35, 36], [43, 37, 35, 37, "enabled"], [43, 44, 35, 44], [43, 49, 35, 49], [43, 58, 35, 58], [43, 61, 35, 61, "linking"], [43, 68, 35, 68], [43, 69, 35, 69, "enabled"], [43, 76, 35, 76], [43, 79, 35, 79, "linkingConfig"], [43, 92, 35, 92], [43, 94, 35, 94, "screens"], [43, 101, 35, 101], [43, 105, 35, 105], [43, 109, 35, 109], [44, 8, 36, 6], [44, 15, 36, 13], [45, 10, 37, 8], [45, 13, 37, 11, "linking"], [45, 20, 37, 18], [46, 10, 38, 8, "enabled"], [46, 17, 38, 15], [47, 10, 39, 8, "config"], [47, 16, 39, 14], [47, 18, 39, 16, "linkingConfig"], [48, 8, 40, 6], [48, 9, 40, 7], [49, 6, 41, 4], [49, 7, 41, 5], [49, 9, 41, 7], [49, 10, 41, 8, "linking"], [49, 17, 41, 15], [49, 19, 41, 17, "linkingConfig"], [49, 32, 41, 30], [49, 33, 41, 31], [49, 34, 41, 32], [50, 6, 42, 4], [50, 10, 42, 8, "linking"], [50, 17, 42, 15], [50, 19, 42, 17, "enabled"], [50, 26, 42, 24], [50, 31, 42, 29], [50, 35, 42, 33], [50, 39, 42, 37, "linkingConfig"], [50, 52, 42, 50], [50, 54, 42, 52, "screens"], [50, 61, 42, 59], [50, 65, 42, 63], [50, 69, 42, 67], [50, 71, 42, 69], [51, 8, 43, 6], [51, 14, 43, 12], [51, 18, 43, 16, "Error"], [51, 23, 43, 21], [51, 24, 43, 22], [51, 104, 43, 102], [51, 107, 43, 105], [51, 125, 43, 123], [51, 128, 43, 126], [51, 199, 43, 197], [51, 202, 43, 200], [51, 277, 43, 275], [51, 280, 43, 278], [51, 360, 43, 358], [51, 361, 43, 359], [52, 6, 44, 4], [53, 6, 45, 4], [53, 13, 45, 11], [53, 26, 45, 24], [53, 30, 45, 24, "_jsx"], [53, 45, 45, 28], [53, 47, 45, 29, "NavigationContainer"], [53, 87, 45, 48], [53, 89, 45, 50], [54, 8, 46, 6], [54, 11, 46, 9, "rest"], [54, 15, 46, 13], [55, 8, 47, 6, "ref"], [55, 11, 47, 9], [55, 13, 47, 11, "ref"], [55, 16, 47, 14], [56, 8, 48, 6, "linking"], [56, 15, 48, 13], [56, 17, 48, 15, "memoizedLinking"], [56, 32, 48, 30], [57, 8, 49, 6, "children"], [57, 16, 49, 14], [57, 18, 49, 16], [57, 31, 49, 29], [57, 35, 49, 29, "_jsx"], [57, 50, 49, 33], [57, 52, 49, 34, "Component"], [57, 61, 49, 43], [57, 63, 49, 45], [57, 64, 49, 46], [57, 65, 49, 47], [58, 6, 50, 4], [58, 7, 50, 5], [58, 8, 50, 6], [59, 4, 51, 2], [60, 4, 52, 2], [60, 11, 52, 9], [60, 24, 52, 22, "React"], [60, 29, 52, 27], [60, 30, 52, 28, "forwardRef"], [60, 40, 52, 38], [60, 41, 52, 39, "Navigation"], [60, 51, 52, 49], [60, 52, 52, 50], [61, 2, 53, 0], [62, 0, 53, 1], [62, 3]], "functionMap": {"names": ["<global>", "createStaticNavigation", "Navigation", "React.useMemo$argument_0"], "mappings": "AAA;OCa;ECE;wCCI;KDU;0CCC;KDU;GDU;CDE"}}, "type": "js/module"}]}