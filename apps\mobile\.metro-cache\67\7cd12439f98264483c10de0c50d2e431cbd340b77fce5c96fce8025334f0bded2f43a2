{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  /**\n   * Empty component used for specifying route configuration.\n   */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Screen = Screen;\n  function Screen(_) {\n    /* istanbul ignore next */\n    return null;\n  }\n});", "lineCount": 15, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 2, 3, 0, "Object"], [7, 8, 3, 0], [7, 9, 3, 0, "defineProperty"], [7, 23, 3, 0], [7, 24, 3, 0, "exports"], [7, 31, 3, 0], [8, 4, 3, 0, "value"], [8, 9, 3, 0], [9, 2, 3, 0], [10, 2, 3, 0, "exports"], [10, 9, 3, 0], [10, 10, 3, 0, "Screen"], [10, 16, 3, 0], [10, 19, 3, 0, "Screen"], [10, 25, 3, 0], [11, 2, 6, 7], [11, 11, 6, 16, "Screen"], [11, 17, 6, 22, "Screen"], [11, 18, 6, 23, "_"], [11, 19, 6, 24], [11, 21, 6, 26], [12, 4, 7, 2], [13, 4, 8, 2], [13, 11, 8, 9], [13, 15, 8, 13], [14, 2, 9, 0], [15, 0, 9, 1], [15, 3]], "functionMap": {"names": ["<global>", "Screen"], "mappings": "AAA;OCK;CDG"}}, "type": "js/module"}]}