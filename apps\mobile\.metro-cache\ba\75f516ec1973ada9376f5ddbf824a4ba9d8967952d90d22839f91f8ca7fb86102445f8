{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 68, "index": 68}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PanGesture = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _gesture = require(_dependencyMap[7], \"./gesture\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var _worklet_425682043203_init_data = {\n    code: \"function changeEventCalculator_panGestureTs1(current,previous){let changePayload;if(previous===undefined){changePayload={changeX:current.translationX,changeY:current.translationY};}else{changePayload={changeX:current.translationX-previous.translationX,changeY:current.translationY-previous.translationY};}return{...current,...changePayload};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\panGesture.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"changeEventCalculator_panGestureTs1\\\",\\\"current\\\",\\\"previous\\\",\\\"changePayload\\\",\\\"undefined\\\",\\\"changeX\\\",\\\"translationX\\\",\\\"changeY\\\",\\\"translationY\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/panGesture.ts\\\"],\\\"mappings\\\":\\\"AAUA,SAAAA,mCAEEA,CAAAC,OACA,CAAAC,QAAA,EAEA,GAAI,CAAAC,aAA2C,CAC/C,GAAID,QAAQ,GAAKE,SAAS,CAAE,CAC1BD,aAAa,CAAG,CACdE,OAAO,CAAEJ,OAAO,CAACK,YAAY,CAC7BC,OAAO,CAAEN,OAAO,CAACO,YACnB,CAAC,CACH,CAAC,IAAM,CACLL,aAAa,CAAG,CACdE,OAAO,CAAEJ,OAAO,CAACK,YAAY,CAAGJ,QAAQ,CAACI,YAAY,CACrDC,OAAO,CAAEN,OAAO,CAACO,YAAY,CAAGN,QAAQ,CAACM,YAC3C,CAAC,CACH,CAEA,MAAO,CAAE,GAAGP,OAAO,CAAE,GAAGE,aAAc,CAAC,CACzC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var changeEventCalculator = function () {\n    var _e = [new global.Error(), 1, -27];\n    var changeEventCalculator = function (current, previous) {\n      var changePayload;\n      if (previous === undefined) {\n        changePayload = {\n          changeX: current.translationX,\n          changeY: current.translationY\n        };\n      } else {\n        changePayload = {\n          changeX: current.translationX - previous.translationX,\n          changeY: current.translationY - previous.translationY\n        };\n      }\n      return {\n        ...current,\n        ...changePayload\n      };\n    };\n    changeEventCalculator.__closure = {};\n    changeEventCalculator.__workletHash = 425682043203;\n    changeEventCalculator.__initData = _worklet_425682043203_init_data;\n    changeEventCalculator.__stackDetails = _e;\n    return changeEventCalculator;\n  }();\n  var PanGesture = exports.PanGesture = /*#__PURE__*/function (_ContinousBaseGesture) {\n    function PanGesture() {\n      var _this;\n      (0, _classCallCheck2.default)(this, PanGesture);\n      _this = _callSuper(this, PanGesture);\n      _this.config = {};\n      _this.handlerName = 'PanGestureHandler';\n      return _this;\n    }\n\n    /**\n     * Range along Y axis (in points) where fingers travels without activation of gesture.\n     * @param offset\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#activeoffsetyvalue-number--number\n     */\n    (0, _inherits2.default)(PanGesture, _ContinousBaseGesture);\n    return (0, _createClass2.default)(PanGesture, [{\n      key: \"activeOffsetY\",\n      value: function activeOffsetY(offset) {\n        if (Array.isArray(offset)) {\n          this.config.activeOffsetYStart = offset[0];\n          this.config.activeOffsetYEnd = offset[1];\n        } else if (offset < 0) {\n          this.config.activeOffsetYStart = offset;\n        } else {\n          this.config.activeOffsetYEnd = offset;\n        }\n        return this;\n      }\n\n      /**\n       * Range along X axis (in points) where fingers travels without activation of gesture.\n       * @param offset\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#activeoffsetxvalue-number--number\n       */\n    }, {\n      key: \"activeOffsetX\",\n      value: function activeOffsetX(offset) {\n        if (Array.isArray(offset)) {\n          this.config.activeOffsetXStart = offset[0];\n          this.config.activeOffsetXEnd = offset[1];\n        } else if (offset < 0) {\n          this.config.activeOffsetXStart = offset;\n        } else {\n          this.config.activeOffsetXEnd = offset;\n        }\n        return this;\n      }\n\n      /**\n       * When the finger moves outside this range (in points) along Y axis and gesture hasn't yet activated it will fail recognizing the gesture.\n       * @param offset\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#failoffsetyvalue-number--number\n       */\n    }, {\n      key: \"failOffsetY\",\n      value: function failOffsetY(offset) {\n        if (Array.isArray(offset)) {\n          this.config.failOffsetYStart = offset[0];\n          this.config.failOffsetYEnd = offset[1];\n        } else if (offset < 0) {\n          this.config.failOffsetYStart = offset;\n        } else {\n          this.config.failOffsetYEnd = offset;\n        }\n        return this;\n      }\n\n      /**\n       * When the finger moves outside this range (in points) along X axis and gesture hasn't yet activated it will fail recognizing the gesture.\n       * @param offset\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#failoffsetxvalue-number--number\n       */\n    }, {\n      key: \"failOffsetX\",\n      value: function failOffsetX(offset) {\n        if (Array.isArray(offset)) {\n          this.config.failOffsetXStart = offset[0];\n          this.config.failOffsetXEnd = offset[1];\n        } else if (offset < 0) {\n          this.config.failOffsetXStart = offset;\n        } else {\n          this.config.failOffsetXEnd = offset;\n        }\n        return this;\n      }\n\n      /**\n       * A number of fingers that is required to be placed before gesture can activate. Should be a higher or equal to 0 integer.\n       * @param minPointers\n       */\n    }, {\n      key: \"minPointers\",\n      value: function minPointers(_minPointers) {\n        this.config.minPointers = _minPointers;\n        return this;\n      }\n\n      /**\n       * When the given number of fingers is placed on the screen and gesture hasn't yet activated it will fail recognizing the gesture.\n       * Should be a higher or equal to 0 integer.\n       * @param maxPointers\n       */\n    }, {\n      key: \"maxPointers\",\n      value: function maxPointers(_maxPointers) {\n        this.config.maxPointers = _maxPointers;\n        return this;\n      }\n\n      /**\n       * Minimum distance the finger (or multiple finger) need to travel before the gesture activates.\n       * Expressed in points.\n       * @param distance\n       */\n    }, {\n      key: \"minDistance\",\n      value: function minDistance(distance) {\n        this.config.minDist = distance;\n        return this;\n      }\n\n      /**\n       * Minimum velocity the finger has to reach in order to activate handler.\n       * @param velocity\n       */\n    }, {\n      key: \"minVelocity\",\n      value: function minVelocity(velocity) {\n        this.config.minVelocity = velocity;\n        return this;\n      }\n\n      /**\n       * Minimum velocity along X axis the finger has to reach in order to activate handler.\n       * @param velocity\n       */\n    }, {\n      key: \"minVelocityX\",\n      value: function minVelocityX(velocity) {\n        this.config.minVelocityX = velocity;\n        return this;\n      }\n\n      /**\n       * Minimum velocity along Y axis the finger has to reach in order to activate handler.\n       * @param velocity\n       */\n    }, {\n      key: \"minVelocityY\",\n      value: function minVelocityY(velocity) {\n        this.config.minVelocityY = velocity;\n        return this;\n      }\n\n      /**\n       * #### Android only\n       * Android, by default, will calculate translation values based on the position of the leading pointer (the first one that was placed on the screen).\n       * This modifier allows that behavior to be changed to the one that is default on iOS - the averaged position of all active pointers will be used to calculate the translation values.\n       * @param value\n       */\n    }, {\n      key: \"averageTouches\",\n      value: function averageTouches(value) {\n        this.config.avgTouches = value;\n        return this;\n      }\n\n      /**\n       * #### iOS only\n       * Enables two-finger gestures on supported devices, for example iPads with trackpads.\n       * @param value\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture/#enabletrackpadtwofingergesturevalue-boolean-ios-only\n       */\n    }, {\n      key: \"enableTrackpadTwoFingerGesture\",\n      value: function enableTrackpadTwoFingerGesture(value) {\n        this.config.enableTrackpadTwoFingerGesture = value;\n        return this;\n      }\n\n      /**\n       * Duration in milliseconds of the LongPress gesture before Pan is allowed to activate.\n       * @param duration\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture/#activateafterlongpressduration-number\n       */\n    }, {\n      key: \"activateAfterLongPress\",\n      value: function activateAfterLongPress(duration) {\n        this.config.activateAfterLongPress = duration;\n        return this;\n      }\n    }, {\n      key: \"onChange\",\n      value: function onChange(callback) {\n        // @ts-ignore TS being overprotective, PanGestureHandlerEventPayload is Record\n        this.handlers.changeEventCalculator = changeEventCalculator;\n        return _superPropGet(PanGesture, \"onChange\", this, 3)([callback]);\n      }\n    }]);\n  }(_gesture.ContinousBaseGesture);\n});", "lineCount": 250, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_gesture"], [13, 14, 1, 0], [13, 17, 1, 0, "require"], [13, 24, 1, 0], [13, 25, 1, 0, "_dependencyMap"], [13, 39, 1, 0], [14, 2, 1, 68], [14, 11, 1, 68, "_callSuper"], [14, 22, 1, 68, "t"], [14, 23, 1, 68], [14, 25, 1, 68, "o"], [14, 26, 1, 68], [14, 28, 1, 68, "e"], [14, 29, 1, 68], [14, 40, 1, 68, "o"], [14, 41, 1, 68], [14, 48, 1, 68, "_getPrototypeOf2"], [14, 64, 1, 68], [14, 65, 1, 68, "default"], [14, 72, 1, 68], [14, 74, 1, 68, "o"], [14, 75, 1, 68], [14, 82, 1, 68, "_possibleConstructorReturn2"], [14, 109, 1, 68], [14, 110, 1, 68, "default"], [14, 117, 1, 68], [14, 119, 1, 68, "t"], [14, 120, 1, 68], [14, 122, 1, 68, "_isNativeReflectConstruct"], [14, 147, 1, 68], [14, 152, 1, 68, "Reflect"], [14, 159, 1, 68], [14, 160, 1, 68, "construct"], [14, 169, 1, 68], [14, 170, 1, 68, "o"], [14, 171, 1, 68], [14, 173, 1, 68, "e"], [14, 174, 1, 68], [14, 186, 1, 68, "_getPrototypeOf2"], [14, 202, 1, 68], [14, 203, 1, 68, "default"], [14, 210, 1, 68], [14, 212, 1, 68, "t"], [14, 213, 1, 68], [14, 215, 1, 68, "constructor"], [14, 226, 1, 68], [14, 230, 1, 68, "o"], [14, 231, 1, 68], [14, 232, 1, 68, "apply"], [14, 237, 1, 68], [14, 238, 1, 68, "t"], [14, 239, 1, 68], [14, 241, 1, 68, "e"], [14, 242, 1, 68], [15, 2, 1, 68], [15, 11, 1, 68, "_isNativeReflectConstruct"], [15, 37, 1, 68], [15, 51, 1, 68, "t"], [15, 52, 1, 68], [15, 56, 1, 68, "Boolean"], [15, 63, 1, 68], [15, 64, 1, 68, "prototype"], [15, 73, 1, 68], [15, 74, 1, 68, "valueOf"], [15, 81, 1, 68], [15, 82, 1, 68, "call"], [15, 86, 1, 68], [15, 87, 1, 68, "Reflect"], [15, 94, 1, 68], [15, 95, 1, 68, "construct"], [15, 104, 1, 68], [15, 105, 1, 68, "Boolean"], [15, 112, 1, 68], [15, 145, 1, 68, "t"], [15, 146, 1, 68], [15, 159, 1, 68, "_isNativeReflectConstruct"], [15, 184, 1, 68], [15, 196, 1, 68, "_isNativeReflectConstruct"], [15, 197, 1, 68], [15, 210, 1, 68, "t"], [15, 211, 1, 68], [16, 2, 1, 68], [16, 11, 1, 68, "_superPropGet"], [16, 25, 1, 68, "t"], [16, 26, 1, 68], [16, 28, 1, 68, "o"], [16, 29, 1, 68], [16, 31, 1, 68, "e"], [16, 32, 1, 68], [16, 34, 1, 68, "r"], [16, 35, 1, 68], [16, 43, 1, 68, "p"], [16, 44, 1, 68], [16, 51, 1, 68, "_get2"], [16, 56, 1, 68], [16, 57, 1, 68, "default"], [16, 64, 1, 68], [16, 70, 1, 68, "_getPrototypeOf2"], [16, 86, 1, 68], [16, 87, 1, 68, "default"], [16, 94, 1, 68], [16, 100, 1, 68, "r"], [16, 101, 1, 68], [16, 104, 1, 68, "t"], [16, 105, 1, 68], [16, 106, 1, 68, "prototype"], [16, 115, 1, 68], [16, 118, 1, 68, "t"], [16, 119, 1, 68], [16, 122, 1, 68, "o"], [16, 123, 1, 68], [16, 125, 1, 68, "e"], [16, 126, 1, 68], [16, 140, 1, 68, "r"], [16, 141, 1, 68], [16, 166, 1, 68, "p"], [16, 167, 1, 68], [16, 180, 1, 68, "t"], [16, 181, 1, 68], [16, 192, 1, 68, "p"], [16, 193, 1, 68], [16, 194, 1, 68, "apply"], [16, 199, 1, 68], [16, 200, 1, 68, "e"], [16, 201, 1, 68], [16, 203, 1, 68, "t"], [16, 204, 1, 68], [16, 211, 1, 68, "p"], [16, 212, 1, 68], [17, 2, 1, 68], [17, 6, 1, 68, "_worklet_425682043203_init_data"], [17, 37, 1, 68], [18, 4, 1, 68, "code"], [18, 8, 1, 68], [19, 4, 1, 68, "location"], [19, 12, 1, 68], [20, 4, 1, 68, "sourceMap"], [20, 13, 1, 68], [21, 4, 1, 68, "version"], [21, 11, 1, 68], [22, 2, 1, 68], [23, 2, 1, 68], [23, 6, 1, 68, "changeEventCalculator"], [23, 27, 1, 68], [23, 30, 11, 0], [24, 4, 11, 0], [24, 8, 11, 0, "_e"], [24, 10, 11, 0], [24, 18, 11, 0, "global"], [24, 24, 11, 0], [24, 25, 11, 0, "Error"], [24, 30, 11, 0], [25, 4, 11, 0], [25, 8, 11, 0, "changeEventCalculator"], [25, 29, 11, 0], [25, 41, 11, 0, "changeEventCalculator"], [25, 42, 12, 2, "current"], [25, 49, 12, 60], [25, 51, 13, 2, "previous"], [25, 59, 13, 62], [25, 61, 14, 2], [26, 6, 16, 2], [26, 10, 16, 6, "changePayload"], [26, 23, 16, 49], [27, 6, 17, 2], [27, 10, 17, 6, "previous"], [27, 18, 17, 14], [27, 23, 17, 19, "undefined"], [27, 32, 17, 28], [27, 34, 17, 30], [28, 8, 18, 4, "changePayload"], [28, 21, 18, 17], [28, 24, 18, 20], [29, 10, 19, 6, "changeX"], [29, 17, 19, 13], [29, 19, 19, 15, "current"], [29, 26, 19, 22], [29, 27, 19, 23, "translationX"], [29, 39, 19, 35], [30, 10, 20, 6, "changeY"], [30, 17, 20, 13], [30, 19, 20, 15, "current"], [30, 26, 20, 22], [30, 27, 20, 23, "translationY"], [31, 8, 21, 4], [31, 9, 21, 5], [32, 6, 22, 2], [32, 7, 22, 3], [32, 13, 22, 9], [33, 8, 23, 4, "changePayload"], [33, 21, 23, 17], [33, 24, 23, 20], [34, 10, 24, 6, "changeX"], [34, 17, 24, 13], [34, 19, 24, 15, "current"], [34, 26, 24, 22], [34, 27, 24, 23, "translationX"], [34, 39, 24, 35], [34, 42, 24, 38, "previous"], [34, 50, 24, 46], [34, 51, 24, 47, "translationX"], [34, 63, 24, 59], [35, 10, 25, 6, "changeY"], [35, 17, 25, 13], [35, 19, 25, 15, "current"], [35, 26, 25, 22], [35, 27, 25, 23, "translationY"], [35, 39, 25, 35], [35, 42, 25, 38, "previous"], [35, 50, 25, 46], [35, 51, 25, 47, "translationY"], [36, 8, 26, 4], [36, 9, 26, 5], [37, 6, 27, 2], [38, 6, 29, 2], [38, 13, 29, 9], [39, 8, 29, 11], [39, 11, 29, 14, "current"], [39, 18, 29, 21], [40, 8, 29, 23], [40, 11, 29, 26, "changePayload"], [41, 6, 29, 40], [41, 7, 29, 41], [42, 4, 30, 0], [42, 5, 30, 1], [43, 4, 30, 1, "changeEventCalculator"], [43, 25, 30, 1], [43, 26, 30, 1, "__closure"], [43, 35, 30, 1], [44, 4, 30, 1, "changeEventCalculator"], [44, 25, 30, 1], [44, 26, 30, 1, "__workletHash"], [44, 39, 30, 1], [45, 4, 30, 1, "changeEventCalculator"], [45, 25, 30, 1], [45, 26, 30, 1, "__initData"], [45, 36, 30, 1], [45, 39, 30, 1, "_worklet_425682043203_init_data"], [45, 70, 30, 1], [46, 4, 30, 1, "changeEventCalculator"], [46, 25, 30, 1], [46, 26, 30, 1, "__stackDetails"], [46, 40, 30, 1], [46, 43, 30, 1, "_e"], [46, 45, 30, 1], [47, 4, 30, 1], [47, 11, 30, 1, "changeEventCalculator"], [47, 32, 30, 1], [48, 2, 30, 1], [48, 3, 11, 0], [49, 2, 11, 0], [49, 6, 32, 13, "PanGesture"], [49, 16, 32, 23], [49, 19, 32, 23, "exports"], [49, 26, 32, 23], [49, 27, 32, 23, "PanGesture"], [49, 37, 32, 23], [49, 63, 32, 23, "_ContinousBaseGesture"], [49, 84, 32, 23], [50, 4, 38, 2], [50, 13, 38, 2, "PanGesture"], [50, 24, 38, 2], [50, 26, 38, 16], [51, 6, 38, 16], [51, 10, 38, 16, "_this"], [51, 15, 38, 16], [52, 6, 38, 16], [52, 10, 38, 16, "_classCallCheck2"], [52, 26, 38, 16], [52, 27, 38, 16, "default"], [52, 34, 38, 16], [52, 42, 38, 16, "PanGesture"], [52, 52, 38, 16], [53, 6, 39, 4, "_this"], [53, 11, 39, 4], [53, 14, 39, 4, "_callSuper"], [53, 24, 39, 4], [53, 31, 39, 4, "PanGesture"], [53, 41, 39, 4], [54, 6, 39, 12, "_this"], [54, 11, 39, 12], [54, 12, 36, 9, "config"], [54, 18, 36, 15], [54, 21, 36, 56], [54, 22, 36, 57], [54, 23, 36, 58], [55, 6, 41, 4, "_this"], [55, 11, 41, 4], [55, 12, 41, 9, "handler<PERSON>ame"], [55, 23, 41, 20], [55, 26, 41, 23], [55, 45, 41, 42], [56, 6, 41, 43], [56, 13, 41, 43, "_this"], [56, 18, 41, 43], [57, 4, 42, 2], [59, 4, 44, 2], [60, 0, 45, 0], [61, 0, 46, 0], [62, 0, 47, 0], [63, 0, 48, 0], [64, 4, 44, 2], [64, 8, 44, 2, "_inherits2"], [64, 18, 44, 2], [64, 19, 44, 2, "default"], [64, 26, 44, 2], [64, 28, 44, 2, "PanGesture"], [64, 38, 44, 2], [64, 40, 44, 2, "_ContinousBaseGesture"], [64, 61, 44, 2], [65, 4, 44, 2], [65, 15, 44, 2, "_createClass2"], [65, 28, 44, 2], [65, 29, 44, 2, "default"], [65, 36, 44, 2], [65, 38, 44, 2, "PanGesture"], [65, 48, 44, 2], [66, 6, 44, 2, "key"], [66, 9, 44, 2], [67, 6, 44, 2, "value"], [67, 11, 44, 2], [67, 13, 49, 2], [67, 22, 49, 2, "activeOffsetY"], [67, 35, 49, 15, "activeOffsetY"], [67, 36, 50, 4, "offset"], [67, 42, 50, 75], [67, 44, 51, 4], [68, 8, 52, 4], [68, 12, 52, 8, "Array"], [68, 17, 52, 13], [68, 18, 52, 14, "isArray"], [68, 25, 52, 21], [68, 26, 52, 22, "offset"], [68, 32, 52, 28], [68, 33, 52, 29], [68, 35, 52, 31], [69, 10, 53, 6], [69, 14, 53, 10], [69, 15, 53, 11, "config"], [69, 21, 53, 17], [69, 22, 53, 18, "activeOffsetYStart"], [69, 40, 53, 36], [69, 43, 53, 39, "offset"], [69, 49, 53, 45], [69, 50, 53, 46], [69, 51, 53, 47], [69, 52, 53, 48], [70, 10, 54, 6], [70, 14, 54, 10], [70, 15, 54, 11, "config"], [70, 21, 54, 17], [70, 22, 54, 18, "activeOffsetYEnd"], [70, 38, 54, 34], [70, 41, 54, 37, "offset"], [70, 47, 54, 43], [70, 48, 54, 44], [70, 49, 54, 45], [70, 50, 54, 46], [71, 8, 55, 4], [71, 9, 55, 5], [71, 15, 55, 11], [71, 19, 55, 15, "offset"], [71, 25, 55, 21], [71, 28, 55, 24], [71, 29, 55, 25], [71, 31, 55, 27], [72, 10, 56, 6], [72, 14, 56, 10], [72, 15, 56, 11, "config"], [72, 21, 56, 17], [72, 22, 56, 18, "activeOffsetYStart"], [72, 40, 56, 36], [72, 43, 56, 39, "offset"], [72, 49, 56, 45], [73, 8, 57, 4], [73, 9, 57, 5], [73, 15, 57, 11], [74, 10, 58, 6], [74, 14, 58, 10], [74, 15, 58, 11, "config"], [74, 21, 58, 17], [74, 22, 58, 18, "activeOffsetYEnd"], [74, 38, 58, 34], [74, 41, 58, 37, "offset"], [74, 47, 58, 43], [75, 8, 59, 4], [76, 8, 60, 4], [76, 15, 60, 11], [76, 19, 60, 15], [77, 6, 61, 2], [79, 6, 63, 2], [80, 0, 64, 0], [81, 0, 65, 0], [82, 0, 66, 0], [83, 0, 67, 0], [84, 4, 63, 2], [85, 6, 63, 2, "key"], [85, 9, 63, 2], [86, 6, 63, 2, "value"], [86, 11, 63, 2], [86, 13, 68, 2], [86, 22, 68, 2, "activeOffsetX"], [86, 35, 68, 15, "activeOffsetX"], [86, 36, 69, 4, "offset"], [86, 42, 69, 75], [86, 44, 70, 4], [87, 8, 71, 4], [87, 12, 71, 8, "Array"], [87, 17, 71, 13], [87, 18, 71, 14, "isArray"], [87, 25, 71, 21], [87, 26, 71, 22, "offset"], [87, 32, 71, 28], [87, 33, 71, 29], [87, 35, 71, 31], [88, 10, 72, 6], [88, 14, 72, 10], [88, 15, 72, 11, "config"], [88, 21, 72, 17], [88, 22, 72, 18, "activeOffsetXStart"], [88, 40, 72, 36], [88, 43, 72, 39, "offset"], [88, 49, 72, 45], [88, 50, 72, 46], [88, 51, 72, 47], [88, 52, 72, 48], [89, 10, 73, 6], [89, 14, 73, 10], [89, 15, 73, 11, "config"], [89, 21, 73, 17], [89, 22, 73, 18, "activeOffsetXEnd"], [89, 38, 73, 34], [89, 41, 73, 37, "offset"], [89, 47, 73, 43], [89, 48, 73, 44], [89, 49, 73, 45], [89, 50, 73, 46], [90, 8, 74, 4], [90, 9, 74, 5], [90, 15, 74, 11], [90, 19, 74, 15, "offset"], [90, 25, 74, 21], [90, 28, 74, 24], [90, 29, 74, 25], [90, 31, 74, 27], [91, 10, 75, 6], [91, 14, 75, 10], [91, 15, 75, 11, "config"], [91, 21, 75, 17], [91, 22, 75, 18, "activeOffsetXStart"], [91, 40, 75, 36], [91, 43, 75, 39, "offset"], [91, 49, 75, 45], [92, 8, 76, 4], [92, 9, 76, 5], [92, 15, 76, 11], [93, 10, 77, 6], [93, 14, 77, 10], [93, 15, 77, 11, "config"], [93, 21, 77, 17], [93, 22, 77, 18, "activeOffsetXEnd"], [93, 38, 77, 34], [93, 41, 77, 37, "offset"], [93, 47, 77, 43], [94, 8, 78, 4], [95, 8, 79, 4], [95, 15, 79, 11], [95, 19, 79, 15], [96, 6, 80, 2], [98, 6, 82, 2], [99, 0, 83, 0], [100, 0, 84, 0], [101, 0, 85, 0], [102, 0, 86, 0], [103, 4, 82, 2], [104, 6, 82, 2, "key"], [104, 9, 82, 2], [105, 6, 82, 2, "value"], [105, 11, 82, 2], [105, 13, 87, 2], [105, 22, 87, 2, "failOffsetY"], [105, 33, 87, 13, "failOffsetY"], [105, 34, 88, 4, "offset"], [105, 40, 88, 71], [105, 42, 89, 4], [106, 8, 90, 4], [106, 12, 90, 8, "Array"], [106, 17, 90, 13], [106, 18, 90, 14, "isArray"], [106, 25, 90, 21], [106, 26, 90, 22, "offset"], [106, 32, 90, 28], [106, 33, 90, 29], [106, 35, 90, 31], [107, 10, 91, 6], [107, 14, 91, 10], [107, 15, 91, 11, "config"], [107, 21, 91, 17], [107, 22, 91, 18, "failOffsetYStart"], [107, 38, 91, 34], [107, 41, 91, 37, "offset"], [107, 47, 91, 43], [107, 48, 91, 44], [107, 49, 91, 45], [107, 50, 91, 46], [108, 10, 92, 6], [108, 14, 92, 10], [108, 15, 92, 11, "config"], [108, 21, 92, 17], [108, 22, 92, 18, "failOffsetYEnd"], [108, 36, 92, 32], [108, 39, 92, 35, "offset"], [108, 45, 92, 41], [108, 46, 92, 42], [108, 47, 92, 43], [108, 48, 92, 44], [109, 8, 93, 4], [109, 9, 93, 5], [109, 15, 93, 11], [109, 19, 93, 15, "offset"], [109, 25, 93, 21], [109, 28, 93, 24], [109, 29, 93, 25], [109, 31, 93, 27], [110, 10, 94, 6], [110, 14, 94, 10], [110, 15, 94, 11, "config"], [110, 21, 94, 17], [110, 22, 94, 18, "failOffsetYStart"], [110, 38, 94, 34], [110, 41, 94, 37, "offset"], [110, 47, 94, 43], [111, 8, 95, 4], [111, 9, 95, 5], [111, 15, 95, 11], [112, 10, 96, 6], [112, 14, 96, 10], [112, 15, 96, 11, "config"], [112, 21, 96, 17], [112, 22, 96, 18, "failOffsetYEnd"], [112, 36, 96, 32], [112, 39, 96, 35, "offset"], [112, 45, 96, 41], [113, 8, 97, 4], [114, 8, 98, 4], [114, 15, 98, 11], [114, 19, 98, 15], [115, 6, 99, 2], [117, 6, 101, 2], [118, 0, 102, 0], [119, 0, 103, 0], [120, 0, 104, 0], [121, 0, 105, 0], [122, 4, 101, 2], [123, 6, 101, 2, "key"], [123, 9, 101, 2], [124, 6, 101, 2, "value"], [124, 11, 101, 2], [124, 13, 106, 2], [124, 22, 106, 2, "failOffsetX"], [124, 33, 106, 13, "failOffsetX"], [124, 34, 107, 4, "offset"], [124, 40, 107, 71], [124, 42, 108, 4], [125, 8, 109, 4], [125, 12, 109, 8, "Array"], [125, 17, 109, 13], [125, 18, 109, 14, "isArray"], [125, 25, 109, 21], [125, 26, 109, 22, "offset"], [125, 32, 109, 28], [125, 33, 109, 29], [125, 35, 109, 31], [126, 10, 110, 6], [126, 14, 110, 10], [126, 15, 110, 11, "config"], [126, 21, 110, 17], [126, 22, 110, 18, "failOffsetXStart"], [126, 38, 110, 34], [126, 41, 110, 37, "offset"], [126, 47, 110, 43], [126, 48, 110, 44], [126, 49, 110, 45], [126, 50, 110, 46], [127, 10, 111, 6], [127, 14, 111, 10], [127, 15, 111, 11, "config"], [127, 21, 111, 17], [127, 22, 111, 18, "failOffsetXEnd"], [127, 36, 111, 32], [127, 39, 111, 35, "offset"], [127, 45, 111, 41], [127, 46, 111, 42], [127, 47, 111, 43], [127, 48, 111, 44], [128, 8, 112, 4], [128, 9, 112, 5], [128, 15, 112, 11], [128, 19, 112, 15, "offset"], [128, 25, 112, 21], [128, 28, 112, 24], [128, 29, 112, 25], [128, 31, 112, 27], [129, 10, 113, 6], [129, 14, 113, 10], [129, 15, 113, 11, "config"], [129, 21, 113, 17], [129, 22, 113, 18, "failOffsetXStart"], [129, 38, 113, 34], [129, 41, 113, 37, "offset"], [129, 47, 113, 43], [130, 8, 114, 4], [130, 9, 114, 5], [130, 15, 114, 11], [131, 10, 115, 6], [131, 14, 115, 10], [131, 15, 115, 11, "config"], [131, 21, 115, 17], [131, 22, 115, 18, "failOffsetXEnd"], [131, 36, 115, 32], [131, 39, 115, 35, "offset"], [131, 45, 115, 41], [132, 8, 116, 4], [133, 8, 117, 4], [133, 15, 117, 11], [133, 19, 117, 15], [134, 6, 118, 2], [136, 6, 120, 2], [137, 0, 121, 0], [138, 0, 122, 0], [139, 0, 123, 0], [140, 4, 120, 2], [141, 6, 120, 2, "key"], [141, 9, 120, 2], [142, 6, 120, 2, "value"], [142, 11, 120, 2], [142, 13, 124, 2], [142, 22, 124, 2, "minPointers"], [142, 33, 124, 13, "minPointers"], [142, 34, 124, 14, "minPointers"], [142, 46, 124, 33], [142, 48, 124, 35], [143, 8, 125, 4], [143, 12, 125, 8], [143, 13, 125, 9, "config"], [143, 19, 125, 15], [143, 20, 125, 16, "minPointers"], [143, 31, 125, 27], [143, 34, 125, 30, "minPointers"], [143, 46, 125, 41], [144, 8, 126, 4], [144, 15, 126, 11], [144, 19, 126, 15], [145, 6, 127, 2], [147, 6, 129, 2], [148, 0, 130, 0], [149, 0, 131, 0], [150, 0, 132, 0], [151, 0, 133, 0], [152, 4, 129, 2], [153, 6, 129, 2, "key"], [153, 9, 129, 2], [154, 6, 129, 2, "value"], [154, 11, 129, 2], [154, 13, 134, 2], [154, 22, 134, 2, "maxPointers"], [154, 33, 134, 13, "maxPointers"], [154, 34, 134, 14, "maxPointers"], [154, 46, 134, 33], [154, 48, 134, 35], [155, 8, 135, 4], [155, 12, 135, 8], [155, 13, 135, 9, "config"], [155, 19, 135, 15], [155, 20, 135, 16, "maxPointers"], [155, 31, 135, 27], [155, 34, 135, 30, "maxPointers"], [155, 46, 135, 41], [156, 8, 136, 4], [156, 15, 136, 11], [156, 19, 136, 15], [157, 6, 137, 2], [159, 6, 139, 2], [160, 0, 140, 0], [161, 0, 141, 0], [162, 0, 142, 0], [163, 0, 143, 0], [164, 4, 139, 2], [165, 6, 139, 2, "key"], [165, 9, 139, 2], [166, 6, 139, 2, "value"], [166, 11, 139, 2], [166, 13, 144, 2], [166, 22, 144, 2, "minDistance"], [166, 33, 144, 13, "minDistance"], [166, 34, 144, 14, "distance"], [166, 42, 144, 30], [166, 44, 144, 32], [167, 8, 145, 4], [167, 12, 145, 8], [167, 13, 145, 9, "config"], [167, 19, 145, 15], [167, 20, 145, 16, "minDist"], [167, 27, 145, 23], [167, 30, 145, 26, "distance"], [167, 38, 145, 34], [168, 8, 146, 4], [168, 15, 146, 11], [168, 19, 146, 15], [169, 6, 147, 2], [171, 6, 149, 2], [172, 0, 150, 0], [173, 0, 151, 0], [174, 0, 152, 0], [175, 4, 149, 2], [176, 6, 149, 2, "key"], [176, 9, 149, 2], [177, 6, 149, 2, "value"], [177, 11, 149, 2], [177, 13, 153, 2], [177, 22, 153, 2, "minVelocity"], [177, 33, 153, 13, "minVelocity"], [177, 34, 153, 14, "velocity"], [177, 42, 153, 30], [177, 44, 153, 32], [178, 8, 154, 4], [178, 12, 154, 8], [178, 13, 154, 9, "config"], [178, 19, 154, 15], [178, 20, 154, 16, "minVelocity"], [178, 31, 154, 27], [178, 34, 154, 30, "velocity"], [178, 42, 154, 38], [179, 8, 155, 4], [179, 15, 155, 11], [179, 19, 155, 15], [180, 6, 156, 2], [182, 6, 158, 2], [183, 0, 159, 0], [184, 0, 160, 0], [185, 0, 161, 0], [186, 4, 158, 2], [187, 6, 158, 2, "key"], [187, 9, 158, 2], [188, 6, 158, 2, "value"], [188, 11, 158, 2], [188, 13, 162, 2], [188, 22, 162, 2, "minVelocityX"], [188, 34, 162, 14, "minVelocityX"], [188, 35, 162, 15, "velocity"], [188, 43, 162, 31], [188, 45, 162, 33], [189, 8, 163, 4], [189, 12, 163, 8], [189, 13, 163, 9, "config"], [189, 19, 163, 15], [189, 20, 163, 16, "minVelocityX"], [189, 32, 163, 28], [189, 35, 163, 31, "velocity"], [189, 43, 163, 39], [190, 8, 164, 4], [190, 15, 164, 11], [190, 19, 164, 15], [191, 6, 165, 2], [193, 6, 167, 2], [194, 0, 168, 0], [195, 0, 169, 0], [196, 0, 170, 0], [197, 4, 167, 2], [198, 6, 167, 2, "key"], [198, 9, 167, 2], [199, 6, 167, 2, "value"], [199, 11, 167, 2], [199, 13, 171, 2], [199, 22, 171, 2, "minVelocityY"], [199, 34, 171, 14, "minVelocityY"], [199, 35, 171, 15, "velocity"], [199, 43, 171, 31], [199, 45, 171, 33], [200, 8, 172, 4], [200, 12, 172, 8], [200, 13, 172, 9, "config"], [200, 19, 172, 15], [200, 20, 172, 16, "minVelocityY"], [200, 32, 172, 28], [200, 35, 172, 31, "velocity"], [200, 43, 172, 39], [201, 8, 173, 4], [201, 15, 173, 11], [201, 19, 173, 15], [202, 6, 174, 2], [204, 6, 176, 2], [205, 0, 177, 0], [206, 0, 178, 0], [207, 0, 179, 0], [208, 0, 180, 0], [209, 0, 181, 0], [210, 4, 176, 2], [211, 6, 176, 2, "key"], [211, 9, 176, 2], [212, 6, 176, 2, "value"], [212, 11, 176, 2], [212, 13, 182, 2], [212, 22, 182, 2, "averageTouches"], [212, 36, 182, 16, "averageTouches"], [212, 37, 182, 17, "value"], [212, 42, 182, 31], [212, 44, 182, 33], [213, 8, 183, 4], [213, 12, 183, 8], [213, 13, 183, 9, "config"], [213, 19, 183, 15], [213, 20, 183, 16, "avgTouches"], [213, 30, 183, 26], [213, 33, 183, 29, "value"], [213, 38, 183, 34], [214, 8, 184, 4], [214, 15, 184, 11], [214, 19, 184, 15], [215, 6, 185, 2], [217, 6, 187, 2], [218, 0, 188, 0], [219, 0, 189, 0], [220, 0, 190, 0], [221, 0, 191, 0], [222, 0, 192, 0], [223, 4, 187, 2], [224, 6, 187, 2, "key"], [224, 9, 187, 2], [225, 6, 187, 2, "value"], [225, 11, 187, 2], [225, 13, 193, 2], [225, 22, 193, 2, "enableTrackpadTwoFingerGesture"], [225, 52, 193, 32, "enableTrackpadTwoFingerGesture"], [225, 53, 193, 33, "value"], [225, 58, 193, 47], [225, 60, 193, 49], [226, 8, 194, 4], [226, 12, 194, 8], [226, 13, 194, 9, "config"], [226, 19, 194, 15], [226, 20, 194, 16, "enableTrackpadTwoFingerGesture"], [226, 50, 194, 46], [226, 53, 194, 49, "value"], [226, 58, 194, 54], [227, 8, 195, 4], [227, 15, 195, 11], [227, 19, 195, 15], [228, 6, 196, 2], [230, 6, 198, 2], [231, 0, 199, 0], [232, 0, 200, 0], [233, 0, 201, 0], [234, 0, 202, 0], [235, 4, 198, 2], [236, 6, 198, 2, "key"], [236, 9, 198, 2], [237, 6, 198, 2, "value"], [237, 11, 198, 2], [237, 13, 203, 2], [237, 22, 203, 2, "activateAfterLongPress"], [237, 44, 203, 24, "activateAfterLongPress"], [237, 45, 203, 25, "duration"], [237, 53, 203, 41], [237, 55, 203, 43], [238, 8, 204, 4], [238, 12, 204, 8], [238, 13, 204, 9, "config"], [238, 19, 204, 15], [238, 20, 204, 16, "activateAfterLongPress"], [238, 42, 204, 38], [238, 45, 204, 41, "duration"], [238, 53, 204, 49], [239, 8, 205, 4], [239, 15, 205, 11], [239, 19, 205, 15], [240, 6, 206, 2], [241, 4, 206, 3], [242, 6, 206, 3, "key"], [242, 9, 206, 3], [243, 6, 206, 3, "value"], [243, 11, 206, 3], [243, 13, 208, 2], [243, 22, 208, 2, "onChange"], [243, 30, 208, 10, "onChange"], [243, 31, 209, 4, "callback"], [243, 39, 213, 13], [243, 41, 214, 4], [244, 8, 215, 4], [245, 8, 216, 4], [245, 12, 216, 8], [245, 13, 216, 9, "handlers"], [245, 21, 216, 17], [245, 22, 216, 18, "changeEventCalculator"], [245, 43, 216, 39], [245, 46, 216, 42, "changeEventCalculator"], [245, 67, 216, 63], [246, 8, 217, 4], [246, 15, 217, 4, "_superPropGet"], [246, 28, 217, 4], [246, 29, 217, 4, "PanGesture"], [246, 39, 217, 4], [246, 63, 217, 26, "callback"], [246, 71, 217, 34], [247, 6, 218, 2], [248, 4, 218, 3], [249, 2, 218, 3], [249, 4, 32, 32, "ContinousBaseGesture"], [249, 33, 32, 52], [250, 0, 32, 52], [250, 3]], "functionMap": {"names": ["<global>", "changeEventCalculator", "PanGesture", "PanGesture#constructor", "PanGesture#activeOffsetY", "PanGesture#activeOffsetX", "PanGesture#failOffsetY", "PanGesture#failOffsetX", "PanGesture#minPointers", "PanGesture#maxPointers", "PanGesture#minDistance", "PanGesture#minVelocity", "PanGesture#minVelocityX", "PanGesture#minVelocityY", "PanGesture#averageTouches", "PanGesture#enableTrackpadTwoFingerGesture", "PanGesture#activateAfterLongPress", "PanGesture#onChange"], "mappings": "AAA;ACU;CDmB;OEE;ECM;GDI;EEO;GFY;EGO;GHY;EIO;GJY;EKO;GLY;EMM;GNG;EOO;GPG;EQO;GRG;ESM;GTG;EUM;GVG;EWM;GXG;EYQ;GZG;EaQ;GbG;EcO;GdG;EeE;GfU;CFC"}}, "type": "js/module"}]}