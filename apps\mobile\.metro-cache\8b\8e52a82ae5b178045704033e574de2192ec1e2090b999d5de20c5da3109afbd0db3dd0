{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./createIconSet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 46}, "end": {"line": 2, "column": 44, "index": 90}}], "key": "PQt9ucTb+ABlKWjDhj7L4XHxOIA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = createMultiStyleIconSet;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var _createIconSet = _interopRequireDefault(require(_dependencyMap[7], \"./createIconSet\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function createMultiStyleIconSet(styles) {\n    var optionsInput = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var styleNames = Object.keys(styles);\n    if (styleNames.length === 0) {\n      throw new Error('You need to add at least one style');\n    }\n    var options = {\n      defaultStyle: styleNames[0],\n      fallbackFamily: _unused => styleNames[0],\n      glyphValidator: (_unused, __unused) => true,\n      ...optionsInput\n    };\n    var iconSets = styleNames.reduce((acc, name) => {\n      var style = styles[name];\n      acc[name] = (0, _createIconSet.default)(style.glyphMap || {}, style.fontFamily || '', style.fontFile || '', style.fontStyle || {});\n      return acc;\n    }, {});\n    function styleFromProps(props) {\n      return Object.keys(props).reduce((result, propName) => styleNames.indexOf(propName) !== -1 && props[propName] === true ? propName : result, options.defaultStyle);\n    }\n    function getIconSetForProps(props) {\n      var name = props.name;\n      var style = styleFromProps(props);\n      if (options.glyphValidator(name, style)) return iconSets[style];\n      var family = options.fallbackFamily(name);\n      if (styleNames.indexOf(family) === -1) {\n        return options.defaultStyle;\n      }\n      return iconSets[family];\n    }\n    function selectIconClass(iconSet, iconClass) {\n      return iconClass.length > 0 ? iconSet[iconClass] : iconSet;\n    }\n    function reduceProps(props) {\n      return Object.keys(props).reduce((acc, prop) => {\n        if (styleNames.indexOf(prop) === -1) {\n          acc[prop] = props[prop];\n        }\n        return acc;\n      }, {});\n    }\n    function getStyledIconSet(style) {\n      var name = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n      if (styleNames.indexOf(style) === -1) {\n        return iconSets[options.defaultStyle];\n      }\n      return !name ? iconSets[styleFromProps({\n        [style]: true\n      })] : getIconSetForProps({\n        name,\n        [style]: true\n      });\n    }\n    function getFontFamily() {\n      var style = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : options.defaultStyle;\n      return getStyledIconSet(style).getFontFamily();\n    }\n    function getRawGlyphMap() {\n      var style = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : options.defaultStyle;\n      return getStyledIconSet(style).getRawGlyphMap();\n    }\n    function hasIcon(name) {\n      var style = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : options.defaultStyle;\n      return options.glyphValidator(name, style);\n    }\n    function createStyledIconClass() {\n      var selectClass = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n      var IconClass = /*#__PURE__*/function (_PureComponent) {\n        function IconClass() {\n          (0, _classCallCheck2.default)(this, IconClass);\n          return _callSuper(this, IconClass, arguments);\n        }\n        (0, _inherits2.default)(IconClass, _PureComponent);\n        return (0, _createClass2.default)(IconClass, [{\n          key: \"render\",\n          value: function render() {\n            var selectedIconSet = getIconSetForProps(this.props);\n            var SelectedIconClass = selectIconClass(selectedIconSet, selectClass);\n            var props = reduceProps(this.props);\n            return /*#__PURE__*/_react.default.createElement(SelectedIconClass, props);\n          }\n        }]);\n      }(_react.PureComponent);\n      IconClass.defaultProps = styleNames.reduce((acc, name) => {\n        acc[name] = false;\n        return acc;\n      }, {});\n      IconClass.font = Object.values(styles).reduce((acc, style) => {\n        acc[style.fontFamily] = style.fontFile;\n        return acc;\n      }, {});\n      IconClass.StyledIconSet = getStyledIconSet;\n      IconClass.getFontFamily = getFontFamily;\n      IconClass.getRawGlyphMap = getRawGlyphMap;\n      IconClass.hasIcon = hasIcon;\n      return IconClass;\n    }\n    var Icon = createStyledIconClass();\n    Icon.Button = createStyledIconClass('Button');\n    return Icon;\n  }\n});", "lineCount": 118, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_react"], [12, 12, 1, 0], [12, 15, 1, 0, "_interopRequireWildcard"], [12, 38, 1, 0], [12, 39, 1, 0, "require"], [12, 46, 1, 0], [12, 47, 1, 0, "_dependencyMap"], [12, 61, 1, 0], [13, 2, 2, 0], [13, 6, 2, 0, "_createIconSet"], [13, 20, 2, 0], [13, 23, 2, 0, "_interopRequireDefault"], [13, 45, 2, 0], [13, 46, 2, 0, "require"], [13, 53, 2, 0], [13, 54, 2, 0, "_dependencyMap"], [13, 68, 2, 0], [14, 2, 2, 44], [14, 11, 2, 44, "_interopRequireWildcard"], [14, 35, 2, 44, "e"], [14, 36, 2, 44], [14, 38, 2, 44, "t"], [14, 39, 2, 44], [14, 68, 2, 44, "WeakMap"], [14, 75, 2, 44], [14, 81, 2, 44, "r"], [14, 82, 2, 44], [14, 89, 2, 44, "WeakMap"], [14, 96, 2, 44], [14, 100, 2, 44, "n"], [14, 101, 2, 44], [14, 108, 2, 44, "WeakMap"], [14, 115, 2, 44], [14, 127, 2, 44, "_interopRequireWildcard"], [14, 150, 2, 44], [14, 162, 2, 44, "_interopRequireWildcard"], [14, 163, 2, 44, "e"], [14, 164, 2, 44], [14, 166, 2, 44, "t"], [14, 167, 2, 44], [14, 176, 2, 44, "t"], [14, 177, 2, 44], [14, 181, 2, 44, "e"], [14, 182, 2, 44], [14, 186, 2, 44, "e"], [14, 187, 2, 44], [14, 188, 2, 44, "__esModule"], [14, 198, 2, 44], [14, 207, 2, 44, "e"], [14, 208, 2, 44], [14, 214, 2, 44, "o"], [14, 215, 2, 44], [14, 217, 2, 44, "i"], [14, 218, 2, 44], [14, 220, 2, 44, "f"], [14, 221, 2, 44], [14, 226, 2, 44, "__proto__"], [14, 235, 2, 44], [14, 243, 2, 44, "default"], [14, 250, 2, 44], [14, 252, 2, 44, "e"], [14, 253, 2, 44], [14, 270, 2, 44, "e"], [14, 271, 2, 44], [14, 294, 2, 44, "e"], [14, 295, 2, 44], [14, 320, 2, 44, "e"], [14, 321, 2, 44], [14, 330, 2, 44, "f"], [14, 331, 2, 44], [14, 337, 2, 44, "o"], [14, 338, 2, 44], [14, 341, 2, 44, "t"], [14, 342, 2, 44], [14, 345, 2, 44, "n"], [14, 346, 2, 44], [14, 349, 2, 44, "r"], [14, 350, 2, 44], [14, 358, 2, 44, "o"], [14, 359, 2, 44], [14, 360, 2, 44, "has"], [14, 363, 2, 44], [14, 364, 2, 44, "e"], [14, 365, 2, 44], [14, 375, 2, 44, "o"], [14, 376, 2, 44], [14, 377, 2, 44, "get"], [14, 380, 2, 44], [14, 381, 2, 44, "e"], [14, 382, 2, 44], [14, 385, 2, 44, "o"], [14, 386, 2, 44], [14, 387, 2, 44, "set"], [14, 390, 2, 44], [14, 391, 2, 44, "e"], [14, 392, 2, 44], [14, 394, 2, 44, "f"], [14, 395, 2, 44], [14, 409, 2, 44, "_t"], [14, 411, 2, 44], [14, 415, 2, 44, "e"], [14, 416, 2, 44], [14, 432, 2, 44, "_t"], [14, 434, 2, 44], [14, 441, 2, 44, "hasOwnProperty"], [14, 455, 2, 44], [14, 456, 2, 44, "call"], [14, 460, 2, 44], [14, 461, 2, 44, "e"], [14, 462, 2, 44], [14, 464, 2, 44, "_t"], [14, 466, 2, 44], [14, 473, 2, 44, "i"], [14, 474, 2, 44], [14, 478, 2, 44, "o"], [14, 479, 2, 44], [14, 482, 2, 44, "Object"], [14, 488, 2, 44], [14, 489, 2, 44, "defineProperty"], [14, 503, 2, 44], [14, 508, 2, 44, "Object"], [14, 514, 2, 44], [14, 515, 2, 44, "getOwnPropertyDescriptor"], [14, 539, 2, 44], [14, 540, 2, 44, "e"], [14, 541, 2, 44], [14, 543, 2, 44, "_t"], [14, 545, 2, 44], [14, 552, 2, 44, "i"], [14, 553, 2, 44], [14, 554, 2, 44, "get"], [14, 557, 2, 44], [14, 561, 2, 44, "i"], [14, 562, 2, 44], [14, 563, 2, 44, "set"], [14, 566, 2, 44], [14, 570, 2, 44, "o"], [14, 571, 2, 44], [14, 572, 2, 44, "f"], [14, 573, 2, 44], [14, 575, 2, 44, "_t"], [14, 577, 2, 44], [14, 579, 2, 44, "i"], [14, 580, 2, 44], [14, 584, 2, 44, "f"], [14, 585, 2, 44], [14, 586, 2, 44, "_t"], [14, 588, 2, 44], [14, 592, 2, 44, "e"], [14, 593, 2, 44], [14, 594, 2, 44, "_t"], [14, 596, 2, 44], [14, 607, 2, 44, "f"], [14, 608, 2, 44], [14, 613, 2, 44, "e"], [14, 614, 2, 44], [14, 616, 2, 44, "t"], [14, 617, 2, 44], [15, 2, 2, 44], [15, 11, 2, 44, "_callSuper"], [15, 22, 2, 44, "t"], [15, 23, 2, 44], [15, 25, 2, 44, "o"], [15, 26, 2, 44], [15, 28, 2, 44, "e"], [15, 29, 2, 44], [15, 40, 2, 44, "o"], [15, 41, 2, 44], [15, 48, 2, 44, "_getPrototypeOf2"], [15, 64, 2, 44], [15, 65, 2, 44, "default"], [15, 72, 2, 44], [15, 74, 2, 44, "o"], [15, 75, 2, 44], [15, 82, 2, 44, "_possibleConstructorReturn2"], [15, 109, 2, 44], [15, 110, 2, 44, "default"], [15, 117, 2, 44], [15, 119, 2, 44, "t"], [15, 120, 2, 44], [15, 122, 2, 44, "_isNativeReflectConstruct"], [15, 147, 2, 44], [15, 152, 2, 44, "Reflect"], [15, 159, 2, 44], [15, 160, 2, 44, "construct"], [15, 169, 2, 44], [15, 170, 2, 44, "o"], [15, 171, 2, 44], [15, 173, 2, 44, "e"], [15, 174, 2, 44], [15, 186, 2, 44, "_getPrototypeOf2"], [15, 202, 2, 44], [15, 203, 2, 44, "default"], [15, 210, 2, 44], [15, 212, 2, 44, "t"], [15, 213, 2, 44], [15, 215, 2, 44, "constructor"], [15, 226, 2, 44], [15, 230, 2, 44, "o"], [15, 231, 2, 44], [15, 232, 2, 44, "apply"], [15, 237, 2, 44], [15, 238, 2, 44, "t"], [15, 239, 2, 44], [15, 241, 2, 44, "e"], [15, 242, 2, 44], [16, 2, 2, 44], [16, 11, 2, 44, "_isNativeReflectConstruct"], [16, 37, 2, 44], [16, 51, 2, 44, "t"], [16, 52, 2, 44], [16, 56, 2, 44, "Boolean"], [16, 63, 2, 44], [16, 64, 2, 44, "prototype"], [16, 73, 2, 44], [16, 74, 2, 44, "valueOf"], [16, 81, 2, 44], [16, 82, 2, 44, "call"], [16, 86, 2, 44], [16, 87, 2, 44, "Reflect"], [16, 94, 2, 44], [16, 95, 2, 44, "construct"], [16, 104, 2, 44], [16, 105, 2, 44, "Boolean"], [16, 112, 2, 44], [16, 145, 2, 44, "t"], [16, 146, 2, 44], [16, 159, 2, 44, "_isNativeReflectConstruct"], [16, 184, 2, 44], [16, 196, 2, 44, "_isNativeReflectConstruct"], [16, 197, 2, 44], [16, 210, 2, 44, "t"], [16, 211, 2, 44], [17, 2, 3, 15], [17, 11, 3, 24, "createMultiStyleIconSet"], [17, 34, 3, 47, "createMultiStyleIconSet"], [17, 35, 3, 48, "styles"], [17, 41, 3, 54], [17, 43, 3, 75], [18, 4, 3, 75], [18, 8, 3, 56, "optionsInput"], [18, 20, 3, 68], [18, 23, 3, 68, "arguments"], [18, 32, 3, 68], [18, 33, 3, 68, "length"], [18, 39, 3, 68], [18, 47, 3, 68, "arguments"], [18, 56, 3, 68], [18, 64, 3, 68, "undefined"], [18, 73, 3, 68], [18, 76, 3, 68, "arguments"], [18, 85, 3, 68], [18, 91, 3, 71], [18, 92, 3, 72], [18, 93, 3, 73], [19, 4, 4, 4], [19, 8, 4, 10, "styleNames"], [19, 18, 4, 20], [19, 21, 4, 23, "Object"], [19, 27, 4, 29], [19, 28, 4, 30, "keys"], [19, 32, 4, 34], [19, 33, 4, 35, "styles"], [19, 39, 4, 41], [19, 40, 4, 42], [20, 4, 5, 4], [20, 8, 5, 8, "styleNames"], [20, 18, 5, 18], [20, 19, 5, 19, "length"], [20, 25, 5, 25], [20, 30, 5, 30], [20, 31, 5, 31], [20, 33, 5, 33], [21, 6, 6, 8], [21, 12, 6, 14], [21, 16, 6, 18, "Error"], [21, 21, 6, 23], [21, 22, 6, 24], [21, 58, 6, 60], [21, 59, 6, 61], [22, 4, 7, 4], [23, 4, 8, 4], [23, 8, 8, 10, "options"], [23, 15, 8, 17], [23, 18, 8, 20], [24, 6, 9, 8, "defaultStyle"], [24, 18, 9, 20], [24, 20, 9, 22, "styleNames"], [24, 30, 9, 32], [24, 31, 9, 33], [24, 32, 9, 34], [24, 33, 9, 35], [25, 6, 10, 8, "fallbackFamily"], [25, 20, 10, 22], [25, 22, 10, 25, "_unused"], [25, 29, 10, 32], [25, 33, 10, 37, "styleNames"], [25, 43, 10, 47], [25, 44, 10, 48], [25, 45, 10, 49], [25, 46, 10, 50], [26, 6, 11, 8, "glyphValidator"], [26, 20, 11, 22], [26, 22, 11, 24, "glyphValidator"], [26, 23, 11, 25, "_unused"], [26, 30, 11, 32], [26, 32, 11, 34, "__unused"], [26, 40, 11, 42], [26, 45, 11, 47], [26, 49, 11, 51], [27, 6, 12, 8], [27, 9, 12, 11, "optionsInput"], [28, 4, 13, 4], [28, 5, 13, 5], [29, 4, 14, 4], [29, 8, 14, 10, "iconSets"], [29, 16, 14, 18], [29, 19, 14, 21, "styleNames"], [29, 29, 14, 31], [29, 30, 14, 32, "reduce"], [29, 36, 14, 38], [29, 37, 14, 39], [29, 38, 14, 40, "acc"], [29, 41, 14, 43], [29, 43, 14, 45, "name"], [29, 47, 14, 49], [29, 52, 14, 54], [30, 6, 15, 8], [30, 10, 15, 14, "style"], [30, 15, 15, 19], [30, 18, 15, 22, "styles"], [30, 24, 15, 28], [30, 25, 15, 29, "name"], [30, 29, 15, 33], [30, 30, 15, 34], [31, 6, 16, 8, "acc"], [31, 9, 16, 11], [31, 10, 16, 12, "name"], [31, 14, 16, 16], [31, 15, 16, 17], [31, 18, 16, 20], [31, 22, 16, 20, "createIconSet"], [31, 44, 16, 33], [31, 46, 16, 34, "style"], [31, 51, 16, 39], [31, 52, 16, 40, "glyphMap"], [31, 60, 16, 48], [31, 64, 16, 52], [31, 65, 16, 53], [31, 66, 16, 54], [31, 68, 16, 56, "style"], [31, 73, 16, 61], [31, 74, 16, 62, "fontFamily"], [31, 84, 16, 72], [31, 88, 16, 76], [31, 90, 16, 78], [31, 92, 16, 80, "style"], [31, 97, 16, 85], [31, 98, 16, 86, "fontFile"], [31, 106, 16, 94], [31, 110, 16, 98], [31, 112, 16, 100], [31, 114, 16, 102, "style"], [31, 119, 16, 107], [31, 120, 16, 108, "fontStyle"], [31, 129, 16, 117], [31, 133, 16, 121], [31, 134, 16, 122], [31, 135, 16, 123], [31, 136, 16, 124], [32, 6, 17, 8], [32, 13, 17, 15, "acc"], [32, 16, 17, 18], [33, 4, 18, 4], [33, 5, 18, 5], [33, 7, 18, 7], [33, 8, 18, 8], [33, 9, 18, 9], [33, 10, 18, 10], [34, 4, 19, 4], [34, 13, 19, 13, "styleFromProps"], [34, 27, 19, 27, "styleFromProps"], [34, 28, 19, 28, "props"], [34, 33, 19, 33], [34, 35, 19, 35], [35, 6, 20, 8], [35, 13, 20, 15, "Object"], [35, 19, 20, 21], [35, 20, 20, 22, "keys"], [35, 24, 20, 26], [35, 25, 20, 27, "props"], [35, 30, 20, 32], [35, 31, 20, 33], [35, 32, 20, 34, "reduce"], [35, 38, 20, 40], [35, 39, 20, 41], [35, 40, 20, 42, "result"], [35, 46, 20, 48], [35, 48, 20, 50, "propName"], [35, 56, 20, 58], [35, 61, 20, 63, "styleNames"], [35, 71, 20, 73], [35, 72, 20, 74, "indexOf"], [35, 79, 20, 81], [35, 80, 20, 82, "propName"], [35, 88, 20, 90], [35, 89, 20, 91], [35, 94, 20, 96], [35, 95, 20, 97], [35, 96, 20, 98], [35, 100, 20, 102, "props"], [35, 105, 20, 107], [35, 106, 20, 108, "propName"], [35, 114, 20, 116], [35, 115, 20, 117], [35, 120, 20, 122], [35, 124, 20, 126], [35, 127, 20, 129, "propName"], [35, 135, 20, 137], [35, 138, 20, 140, "result"], [35, 144, 20, 146], [35, 146, 20, 148, "options"], [35, 153, 20, 155], [35, 154, 20, 156, "defaultStyle"], [35, 166, 20, 168], [35, 167, 20, 169], [36, 4, 21, 4], [37, 4, 22, 4], [37, 13, 22, 13, "getIconSetForProps"], [37, 31, 22, 31, "getIconSetForProps"], [37, 32, 22, 32, "props"], [37, 37, 22, 37], [37, 39, 22, 39], [38, 6, 23, 8], [38, 10, 23, 16, "name"], [38, 14, 23, 20], [38, 17, 23, 25, "props"], [38, 22, 23, 30], [38, 23, 23, 16, "name"], [38, 27, 23, 20], [39, 6, 24, 8], [39, 10, 24, 14, "style"], [39, 15, 24, 19], [39, 18, 24, 22, "styleFromProps"], [39, 32, 24, 36], [39, 33, 24, 37, "props"], [39, 38, 24, 42], [39, 39, 24, 43], [40, 6, 25, 8], [40, 10, 25, 12, "options"], [40, 17, 25, 19], [40, 18, 25, 20, "glyphValidator"], [40, 32, 25, 34], [40, 33, 25, 35, "name"], [40, 37, 25, 39], [40, 39, 25, 41, "style"], [40, 44, 25, 46], [40, 45, 25, 47], [40, 47, 26, 12], [40, 54, 26, 19, "iconSets"], [40, 62, 26, 27], [40, 63, 26, 28, "style"], [40, 68, 26, 33], [40, 69, 26, 34], [41, 6, 27, 8], [41, 10, 27, 14, "family"], [41, 16, 27, 20], [41, 19, 27, 23, "options"], [41, 26, 27, 30], [41, 27, 27, 31, "fallbackFamily"], [41, 41, 27, 45], [41, 42, 27, 46, "name"], [41, 46, 27, 50], [41, 47, 27, 51], [42, 6, 28, 8], [42, 10, 28, 12, "styleNames"], [42, 20, 28, 22], [42, 21, 28, 23, "indexOf"], [42, 28, 28, 30], [42, 29, 28, 31, "family"], [42, 35, 28, 37], [42, 36, 28, 38], [42, 41, 28, 43], [42, 42, 28, 44], [42, 43, 28, 45], [42, 45, 28, 47], [43, 8, 29, 12], [43, 15, 29, 19, "options"], [43, 22, 29, 26], [43, 23, 29, 27, "defaultStyle"], [43, 35, 29, 39], [44, 6, 30, 8], [45, 6, 31, 8], [45, 13, 31, 15, "iconSets"], [45, 21, 31, 23], [45, 22, 31, 24, "family"], [45, 28, 31, 30], [45, 29, 31, 31], [46, 4, 32, 4], [47, 4, 33, 4], [47, 13, 33, 13, "selectIconClass"], [47, 28, 33, 28, "selectIconClass"], [47, 29, 33, 29, "iconSet"], [47, 36, 33, 36], [47, 38, 33, 38, "iconClass"], [47, 47, 33, 47], [47, 49, 33, 49], [48, 6, 34, 8], [48, 13, 34, 15, "iconClass"], [48, 22, 34, 24], [48, 23, 34, 25, "length"], [48, 29, 34, 31], [48, 32, 34, 34], [48, 33, 34, 35], [48, 36, 34, 38, "iconSet"], [48, 43, 34, 45], [48, 44, 34, 46, "iconClass"], [48, 53, 34, 55], [48, 54, 34, 56], [48, 57, 34, 59, "iconSet"], [48, 64, 34, 66], [49, 4, 35, 4], [50, 4, 36, 4], [50, 13, 36, 13, "reduceProps"], [50, 24, 36, 24, "reduceProps"], [50, 25, 36, 25, "props"], [50, 30, 36, 30], [50, 32, 36, 32], [51, 6, 37, 8], [51, 13, 37, 15, "Object"], [51, 19, 37, 21], [51, 20, 37, 22, "keys"], [51, 24, 37, 26], [51, 25, 37, 27, "props"], [51, 30, 37, 32], [51, 31, 37, 33], [51, 32, 37, 34, "reduce"], [51, 38, 37, 40], [51, 39, 37, 41], [51, 40, 37, 42, "acc"], [51, 43, 37, 45], [51, 45, 37, 47, "prop"], [51, 49, 37, 51], [51, 54, 37, 56], [52, 8, 38, 12], [52, 12, 38, 16, "styleNames"], [52, 22, 38, 26], [52, 23, 38, 27, "indexOf"], [52, 30, 38, 34], [52, 31, 38, 35, "prop"], [52, 35, 38, 39], [52, 36, 38, 40], [52, 41, 38, 45], [52, 42, 38, 46], [52, 43, 38, 47], [52, 45, 38, 49], [53, 10, 39, 16, "acc"], [53, 13, 39, 19], [53, 14, 39, 20, "prop"], [53, 18, 39, 24], [53, 19, 39, 25], [53, 22, 39, 28, "props"], [53, 27, 39, 33], [53, 28, 39, 34, "prop"], [53, 32, 39, 38], [53, 33, 39, 39], [54, 8, 40, 12], [55, 8, 41, 12], [55, 15, 41, 19, "acc"], [55, 18, 41, 22], [56, 6, 42, 8], [56, 7, 42, 9], [56, 9, 42, 11], [56, 10, 42, 12], [56, 11, 42, 13], [56, 12, 42, 14], [57, 4, 43, 4], [58, 4, 44, 4], [58, 13, 44, 13, "getStyledIconSet"], [58, 29, 44, 29, "getStyledIconSet"], [58, 30, 44, 30, "style"], [58, 35, 44, 35], [58, 37, 44, 48], [59, 6, 44, 48], [59, 10, 44, 37, "name"], [59, 14, 44, 41], [59, 17, 44, 41, "arguments"], [59, 26, 44, 41], [59, 27, 44, 41, "length"], [59, 33, 44, 41], [59, 41, 44, 41, "arguments"], [59, 50, 44, 41], [59, 58, 44, 41, "undefined"], [59, 67, 44, 41], [59, 70, 44, 41, "arguments"], [59, 79, 44, 41], [59, 85, 44, 44], [59, 87, 44, 46], [60, 6, 45, 8], [60, 10, 45, 12, "styleNames"], [60, 20, 45, 22], [60, 21, 45, 23, "indexOf"], [60, 28, 45, 30], [60, 29, 45, 31, "style"], [60, 34, 45, 36], [60, 35, 45, 37], [60, 40, 45, 42], [60, 41, 45, 43], [60, 42, 45, 44], [60, 44, 45, 46], [61, 8, 46, 12], [61, 15, 46, 19, "iconSets"], [61, 23, 46, 27], [61, 24, 46, 28, "options"], [61, 31, 46, 35], [61, 32, 46, 36, "defaultStyle"], [61, 44, 46, 48], [61, 45, 46, 49], [62, 6, 47, 8], [63, 6, 48, 8], [63, 13, 48, 15], [63, 14, 48, 16, "name"], [63, 18, 48, 20], [63, 21, 49, 14, "iconSets"], [63, 29, 49, 22], [63, 30, 49, 23, "styleFromProps"], [63, 44, 49, 37], [63, 45, 49, 38], [64, 8, 49, 40], [64, 9, 49, 41, "style"], [64, 14, 49, 46], [64, 17, 49, 49], [65, 6, 49, 54], [65, 7, 49, 55], [65, 8, 49, 56], [65, 9, 49, 57], [65, 12, 50, 14, "getIconSetForProps"], [65, 30, 50, 32], [65, 31, 50, 33], [66, 8, 50, 35, "name"], [66, 12, 50, 39], [67, 8, 50, 41], [67, 9, 50, 42, "style"], [67, 14, 50, 47], [67, 17, 50, 50], [68, 6, 50, 55], [68, 7, 50, 56], [68, 8, 50, 57], [69, 4, 51, 4], [70, 4, 52, 4], [70, 13, 52, 13, "getFontFamily"], [70, 26, 52, 26, "getFontFamily"], [70, 27, 52, 26], [70, 29, 52, 57], [71, 6, 52, 57], [71, 10, 52, 27, "style"], [71, 15, 52, 32], [71, 18, 52, 32, "arguments"], [71, 27, 52, 32], [71, 28, 52, 32, "length"], [71, 34, 52, 32], [71, 42, 52, 32, "arguments"], [71, 51, 52, 32], [71, 59, 52, 32, "undefined"], [71, 68, 52, 32], [71, 71, 52, 32, "arguments"], [71, 80, 52, 32], [71, 86, 52, 35, "options"], [71, 93, 52, 42], [71, 94, 52, 43, "defaultStyle"], [71, 106, 52, 55], [72, 6, 53, 8], [72, 13, 53, 15, "getStyledIconSet"], [72, 29, 53, 31], [72, 30, 53, 32, "style"], [72, 35, 53, 37], [72, 36, 53, 38], [72, 37, 53, 39, "getFontFamily"], [72, 50, 53, 52], [72, 51, 53, 53], [72, 52, 53, 54], [73, 4, 54, 4], [74, 4, 55, 4], [74, 13, 55, 13, "getRawGlyphMap"], [74, 27, 55, 27, "getRawGlyphMap"], [74, 28, 55, 27], [74, 30, 55, 58], [75, 6, 55, 58], [75, 10, 55, 28, "style"], [75, 15, 55, 33], [75, 18, 55, 33, "arguments"], [75, 27, 55, 33], [75, 28, 55, 33, "length"], [75, 34, 55, 33], [75, 42, 55, 33, "arguments"], [75, 51, 55, 33], [75, 59, 55, 33, "undefined"], [75, 68, 55, 33], [75, 71, 55, 33, "arguments"], [75, 80, 55, 33], [75, 86, 55, 36, "options"], [75, 93, 55, 43], [75, 94, 55, 44, "defaultStyle"], [75, 106, 55, 56], [76, 6, 56, 8], [76, 13, 56, 15, "getStyledIconSet"], [76, 29, 56, 31], [76, 30, 56, 32, "style"], [76, 35, 56, 37], [76, 36, 56, 38], [76, 37, 56, 39, "getRawGlyphMap"], [76, 51, 56, 53], [76, 52, 56, 54], [76, 53, 56, 55], [77, 4, 57, 4], [78, 4, 58, 4], [78, 13, 58, 13, "hasIcon"], [78, 20, 58, 20, "hasIcon"], [78, 21, 58, 21, "name"], [78, 25, 58, 25], [78, 27, 58, 57], [79, 6, 58, 57], [79, 10, 58, 27, "style"], [79, 15, 58, 32], [79, 18, 58, 32, "arguments"], [79, 27, 58, 32], [79, 28, 58, 32, "length"], [79, 34, 58, 32], [79, 42, 58, 32, "arguments"], [79, 51, 58, 32], [79, 59, 58, 32, "undefined"], [79, 68, 58, 32], [79, 71, 58, 32, "arguments"], [79, 80, 58, 32], [79, 86, 58, 35, "options"], [79, 93, 58, 42], [79, 94, 58, 43, "defaultStyle"], [79, 106, 58, 55], [80, 6, 59, 8], [80, 13, 59, 15, "options"], [80, 20, 59, 22], [80, 21, 59, 23, "glyphValidator"], [80, 35, 59, 37], [80, 36, 59, 38, "name"], [80, 40, 59, 42], [80, 42, 59, 44, "style"], [80, 47, 59, 49], [80, 48, 59, 50], [81, 4, 60, 4], [82, 4, 61, 4], [82, 13, 61, 13, "createStyledIconClass"], [82, 34, 61, 34, "createStyledIconClass"], [82, 35, 61, 34], [82, 37, 61, 53], [83, 6, 61, 53], [83, 10, 61, 35, "selectClass"], [83, 21, 61, 46], [83, 24, 61, 46, "arguments"], [83, 33, 61, 46], [83, 34, 61, 46, "length"], [83, 40, 61, 46], [83, 48, 61, 46, "arguments"], [83, 57, 61, 46], [83, 65, 61, 46, "undefined"], [83, 74, 61, 46], [83, 77, 61, 46, "arguments"], [83, 86, 61, 46], [83, 92, 61, 49], [83, 94, 61, 51], [84, 6, 61, 51], [84, 10, 62, 14, "IconClass"], [84, 19, 62, 23], [84, 45, 62, 23, "_PureComponent"], [84, 59, 62, 23], [85, 8, 62, 23], [85, 17, 62, 23, "IconClass"], [85, 27, 62, 23], [86, 10, 62, 23], [86, 14, 62, 23, "_classCallCheck2"], [86, 30, 62, 23], [86, 31, 62, 23, "default"], [86, 38, 62, 23], [86, 46, 62, 23, "IconClass"], [86, 55, 62, 23], [87, 10, 62, 23], [87, 17, 62, 23, "_callSuper"], [87, 27, 62, 23], [87, 34, 62, 23, "IconClass"], [87, 43, 62, 23], [87, 45, 62, 23, "arguments"], [87, 54, 62, 23], [88, 8, 62, 23], [89, 8, 62, 23], [89, 12, 62, 23, "_inherits2"], [89, 22, 62, 23], [89, 23, 62, 23, "default"], [89, 30, 62, 23], [89, 32, 62, 23, "IconClass"], [89, 41, 62, 23], [89, 43, 62, 23, "_PureComponent"], [89, 57, 62, 23], [90, 8, 62, 23], [90, 19, 62, 23, "_createClass2"], [90, 32, 62, 23], [90, 33, 62, 23, "default"], [90, 40, 62, 23], [90, 42, 62, 23, "IconClass"], [90, 51, 62, 23], [91, 10, 62, 23, "key"], [91, 13, 62, 23], [92, 10, 62, 23, "value"], [92, 15, 62, 23], [92, 17, 76, 12], [92, 26, 76, 12, "render"], [92, 32, 76, 18, "render"], [92, 33, 76, 18], [92, 35, 76, 21], [93, 12, 77, 16], [93, 16, 77, 22, "selectedIconSet"], [93, 31, 77, 37], [93, 34, 77, 40, "getIconSetForProps"], [93, 52, 77, 58], [93, 53, 77, 59], [93, 57, 77, 63], [93, 58, 77, 64, "props"], [93, 63, 77, 69], [93, 64, 77, 70], [94, 12, 78, 16], [94, 16, 78, 22, "SelectedIconClass"], [94, 33, 78, 39], [94, 36, 78, 42, "selectIconClass"], [94, 51, 78, 57], [94, 52, 78, 58, "selectedIconSet"], [94, 67, 78, 73], [94, 69, 78, 75, "selectClass"], [94, 80, 78, 86], [94, 81, 78, 87], [95, 12, 79, 16], [95, 16, 79, 22, "props"], [95, 21, 79, 27], [95, 24, 79, 30, "reduceProps"], [95, 35, 79, 41], [95, 36, 79, 42], [95, 40, 79, 46], [95, 41, 79, 47, "props"], [95, 46, 79, 52], [95, 47, 79, 53], [96, 12, 80, 16], [96, 32, 80, 23, "React"], [96, 46, 80, 28], [96, 47, 80, 29, "createElement"], [96, 60, 80, 42], [96, 61, 80, 43, "SelectedIconClass"], [96, 78, 80, 60], [96, 80, 80, 62, "props"], [96, 85, 80, 67], [96, 86, 80, 68], [97, 10, 81, 12], [98, 8, 81, 13], [99, 6, 81, 13], [99, 8, 62, 32, "PureComponent"], [99, 28, 62, 45], [100, 6, 62, 14, "IconClass"], [100, 15, 62, 23], [100, 16, 63, 19, "defaultProps"], [100, 28, 63, 31], [100, 31, 63, 34, "styleNames"], [100, 41, 63, 44], [100, 42, 63, 45, "reduce"], [100, 48, 63, 51], [100, 49, 63, 52], [100, 50, 63, 53, "acc"], [100, 53, 63, 56], [100, 55, 63, 58, "name"], [100, 59, 63, 62], [100, 64, 63, 67], [101, 8, 64, 16, "acc"], [101, 11, 64, 19], [101, 12, 64, 20, "name"], [101, 16, 64, 24], [101, 17, 64, 25], [101, 20, 64, 28], [101, 25, 64, 33], [102, 8, 65, 16], [102, 15, 65, 23, "acc"], [102, 18, 65, 26], [103, 6, 66, 12], [103, 7, 66, 13], [103, 9, 66, 15], [103, 10, 66, 16], [103, 11, 66, 17], [103, 12, 66, 18], [104, 6, 62, 14, "IconClass"], [104, 15, 62, 23], [104, 16, 67, 19, "font"], [104, 20, 67, 23], [104, 23, 67, 26, "Object"], [104, 29, 67, 32], [104, 30, 67, 33, "values"], [104, 36, 67, 39], [104, 37, 67, 40, "styles"], [104, 43, 67, 46], [104, 44, 67, 47], [104, 45, 67, 48, "reduce"], [104, 51, 67, 54], [104, 52, 67, 55], [104, 53, 67, 56, "acc"], [104, 56, 67, 59], [104, 58, 67, 61, "style"], [104, 63, 67, 66], [104, 68, 67, 71], [105, 8, 68, 16, "acc"], [105, 11, 68, 19], [105, 12, 68, 20, "style"], [105, 17, 68, 25], [105, 18, 68, 26, "fontFamily"], [105, 28, 68, 36], [105, 29, 68, 37], [105, 32, 68, 40, "style"], [105, 37, 68, 45], [105, 38, 68, 46, "fontFile"], [105, 46, 68, 54], [106, 8, 69, 16], [106, 15, 69, 23, "acc"], [106, 18, 69, 26], [107, 6, 70, 12], [107, 7, 70, 13], [107, 9, 70, 15], [107, 10, 70, 16], [107, 11, 70, 17], [107, 12, 70, 18], [108, 6, 62, 14, "IconClass"], [108, 15, 62, 23], [108, 16, 72, 19, "StyledIconSet"], [108, 29, 72, 32], [108, 32, 72, 35, "getStyledIconSet"], [108, 48, 72, 51], [109, 6, 62, 14, "IconClass"], [109, 15, 62, 23], [109, 16, 73, 19, "getFontFamily"], [109, 29, 73, 32], [109, 32, 73, 35, "getFontFamily"], [109, 45, 73, 48], [110, 6, 62, 14, "IconClass"], [110, 15, 62, 23], [110, 16, 74, 19, "getRawGlyphMap"], [110, 30, 74, 33], [110, 33, 74, 36, "getRawGlyphMap"], [110, 47, 74, 50], [111, 6, 62, 14, "IconClass"], [111, 15, 62, 23], [111, 16, 75, 19, "hasIcon"], [111, 23, 75, 26], [111, 26, 75, 29, "hasIcon"], [111, 33, 75, 36], [112, 6, 83, 8], [112, 13, 83, 15, "IconClass"], [112, 22, 83, 24], [113, 4, 84, 4], [114, 4, 85, 4], [114, 8, 85, 10, "Icon"], [114, 12, 85, 14], [114, 15, 85, 17, "createStyledIconClass"], [114, 36, 85, 38], [114, 37, 85, 39], [114, 38, 85, 40], [115, 4, 86, 4, "Icon"], [115, 8, 86, 8], [115, 9, 86, 9, "<PERSON><PERSON>"], [115, 15, 86, 15], [115, 18, 86, 18, "createStyledIconClass"], [115, 39, 86, 39], [115, 40, 86, 40], [115, 48, 86, 48], [115, 49, 86, 49], [116, 4, 87, 4], [116, 11, 87, 11, "Icon"], [116, 15, 87, 15], [117, 2, 88, 0], [118, 0, 88, 1], [118, 3]], "functionMap": {"names": ["<global>", "createMultiStyleIconSet", "options.fallbackFamily", "options.glyphValidator", "styleNames.reduce$argument_0", "styleFromProps", "Object.keys.reduce$argument_0", "getIconSetForProps", "selectIconClass", "reduceProps", "getStyledIconSet", "getFontFamily", "getRawGlyphMap", "hasIcon", "createStyledIconClass", "IconClass", "Object.values.reduce$argument_0", "IconClass#render"], "mappings": "AAA;eCE;wBCO,0BD;wBEC,2BF;uCGG;KHI;IIC;yCCC,yGD;KJC;IMC;KNU;IOC;KPE;IQC;yCHC;SGK;KRC;ISC;KTO;IUC;KVE;IWC;KXE;IYC;KZE;IaC;QCC;oDXC;aWG;uDCC;aDG;YEM;aFK;SDC;KbE;CDI"}}, "type": "js/module"}]}