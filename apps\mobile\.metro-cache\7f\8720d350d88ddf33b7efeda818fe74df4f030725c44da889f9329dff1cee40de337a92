{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 85}, "end": {"line": 2, "column": 44, "index": 129}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 130}, "end": {"line": 6, "column": 32, "index": 225}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.flingHandlerName = exports.flingGestureHandlerProps = exports.FlingGestureHandler = void 0;\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[1], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[2], \"./gestureHandlerCommon\");\n  var flingGestureHandlerProps = exports.flingGestureHandlerProps = ['numberOfPointers', 'direction'];\n\n  /**\n   * @deprecated FlingGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Fling()` instead.\n   */\n\n  var flingHandlerName = exports.flingHandlerName = 'FlingGestureHandler';\n\n  /**\n   * @deprecated FlingGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Fling()` instead.\n   */\n\n  /**\n   * @deprecated FlingGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Fling()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n  var FlingGestureHandler = exports.FlingGestureHandler = (0, _createHandler.default)({\n    name: flingHandlerName,\n    allowedProps: [..._gestureHandlerCommon.baseGestureHandlerProps, ...flingGestureHandlerProps],\n    config: {}\n  });\n});", "lineCount": 30, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_createHandler"], [7, 20, 2, 0], [7, 23, 2, 0, "_interopRequireDefault"], [7, 45, 2, 0], [7, 46, 2, 0, "require"], [7, 53, 2, 0], [7, 54, 2, 0, "_dependencyMap"], [7, 68, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 27, 3, 0], [8, 30, 3, 0, "require"], [8, 37, 3, 0], [8, 38, 3, 0, "_dependencyMap"], [8, 52, 3, 0], [9, 2, 8, 7], [9, 6, 8, 13, "flingGestureHandlerProps"], [9, 30, 8, 37], [9, 33, 8, 37, "exports"], [9, 40, 8, 37], [9, 41, 8, 37, "flingGestureHandlerProps"], [9, 65, 8, 37], [9, 68, 8, 40], [9, 69, 9, 2], [9, 87, 9, 20], [9, 89, 10, 2], [9, 100, 10, 13], [9, 101, 11, 10], [11, 2, 36, 0], [12, 0, 37, 0], [13, 0, 38, 0], [15, 2, 43, 7], [15, 6, 43, 13, "flingHandlerName"], [15, 22, 43, 29], [15, 25, 43, 29, "exports"], [15, 32, 43, 29], [15, 33, 43, 29, "flingHandlerName"], [15, 49, 43, 29], [15, 52, 43, 32], [15, 73, 43, 53], [17, 2, 45, 0], [18, 0, 46, 0], [19, 0, 47, 0], [21, 2, 50, 0], [22, 0, 51, 0], [23, 0, 52, 0], [24, 2, 53, 0], [25, 2, 54, 7], [25, 6, 54, 13, "FlingGestureHandler"], [25, 25, 54, 32], [25, 28, 54, 32, "exports"], [25, 35, 54, 32], [25, 36, 54, 32, "FlingGestureHandler"], [25, 55, 54, 32], [25, 58, 54, 35], [25, 62, 54, 35, "createHandler"], [25, 84, 54, 48], [25, 86, 57, 2], [26, 4, 58, 2, "name"], [26, 8, 58, 6], [26, 10, 58, 8, "flingHandlerName"], [26, 26, 58, 24], [27, 4, 59, 2, "allowedProps"], [27, 16, 59, 14], [27, 18, 59, 16], [27, 19, 60, 4], [27, 22, 60, 7, "baseGestureHandlerProps"], [27, 67, 60, 30], [27, 69, 61, 4], [27, 72, 61, 7, "flingGestureHandlerProps"], [27, 96, 61, 31], [27, 97, 62, 12], [28, 4, 63, 2, "config"], [28, 10, 63, 8], [28, 12, 63, 10], [28, 13, 63, 11], [29, 2, 64, 0], [29, 3, 64, 1], [29, 4, 64, 2], [30, 0, 64, 3], [30, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}