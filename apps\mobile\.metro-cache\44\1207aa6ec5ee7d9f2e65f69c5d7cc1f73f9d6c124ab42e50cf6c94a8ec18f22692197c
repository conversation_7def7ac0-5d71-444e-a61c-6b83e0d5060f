{"dependencies": [{"name": "./getDevServer", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 46}}], "key": "XTiwJdMhtKrpiN8WycYhMCkLJSM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = openFileInEditor;\n  var getDevServer = require(_dependencyMap[0], \"./getDevServer\").default;\n  function openFileInEditor(file, lineNumber) {\n    fetch(getDevServer().url + 'open-stack-frame', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        file,\n        lineNumber\n      })\n    });\n  }\n});", "lineCount": 21, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [7, 20, 11, 13, "openFileInEditor"], [7, 36, 11, 13], [8, 2, 13, 0], [8, 6, 13, 6, "getDevServer"], [8, 18, 13, 18], [8, 21, 13, 21, "require"], [8, 28, 13, 28], [8, 29, 13, 28, "_dependencyMap"], [8, 43, 13, 28], [8, 64, 13, 45], [8, 65, 13, 46], [8, 66, 13, 47, "default"], [8, 73, 13, 54], [9, 2, 15, 15], [9, 11, 15, 24, "openFileInEditor"], [9, 27, 15, 40, "openFileInEditor"], [9, 28, 15, 41, "file"], [9, 32, 15, 53], [9, 34, 15, 55, "lineNumber"], [9, 44, 15, 73], [9, 46, 15, 75], [10, 4, 17, 2, "fetch"], [10, 9, 17, 7], [10, 10, 17, 8, "getDevServer"], [10, 22, 17, 20], [10, 23, 17, 21], [10, 24, 17, 22], [10, 25, 17, 23, "url"], [10, 28, 17, 26], [10, 31, 17, 29], [10, 49, 17, 47], [10, 51, 17, 49], [11, 6, 18, 4, "method"], [11, 12, 18, 10], [11, 14, 18, 12], [11, 20, 18, 18], [12, 6, 19, 4, "headers"], [12, 13, 19, 11], [12, 15, 19, 13], [13, 8, 20, 6], [13, 22, 20, 20], [13, 24, 20, 22], [14, 6, 21, 4], [14, 7, 21, 5], [15, 6, 22, 4, "body"], [15, 10, 22, 8], [15, 12, 22, 10, "JSON"], [15, 16, 22, 14], [15, 17, 22, 15, "stringify"], [15, 26, 22, 24], [15, 27, 22, 25], [16, 8, 22, 26, "file"], [16, 12, 22, 30], [17, 8, 22, 32, "lineNumber"], [18, 6, 22, 42], [18, 7, 22, 43], [19, 4, 23, 2], [19, 5, 23, 3], [19, 6, 23, 4], [20, 2, 24, 0], [21, 0, 24, 1], [21, 3]], "functionMap": {"names": ["<global>", "openFileInEditor"], "mappings": "AAA;eCc"}}, "type": "js/module"}]}