{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "nativewind", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "cmUKQSXJyC7fmRcHKtmYzlG9LzY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 42}, "end": {"line": 10, "column": 22, "index": 166}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 168}, "end": {"line": 11, "column": 62, "index": 230}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "../components/FooterNavigation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 336}, "end": {"line": 14, "column": 62, "index": 398}}], "key": "i0wsjvqzeC9AyK9fcghZiqZFu6Y=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = HomeScreen;\n  var _nativewind = require(_dependencyMap[1], \"nativewind\");\n  var _react = _interopRequireDefault(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[4], \"react-native-safe-area-context\");\n  var _FooterNavigation = _interopRequireDefault(require(_dependencyMap[5], \"../components/FooterNavigation\"));\n  var _jsxDevRuntime = require(_dependencyMap[6], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\apps\\\\mobile\\\\src\\\\screens\\\\HomeScreen.tsx\";\n  function HomeScreen(_ref) {\n    var navigation = _ref.navigation;\n    console.log('HomeScreen rendering...');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: {\n        flex: 1,\n        backgroundColor: '#f9fafb'\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f3a823'\n        },\n        edges: ['top']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: {\n          flex: 1,\n          backgroundColor: '#f9fafb'\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            paddingHorizontal: 20,\n            paddingTop: 10,\n            paddingBottom: 20\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 28,\n              fontWeight: 'bold',\n              color: '#111827'\n            },\n            children: \"Welcome to Tap2Go\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 16,\n              color: '#6b7280',\n              marginTop: 4\n            },\n            children: \"Your food delivery app\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.ScrollView, {\n          style: {\n            flex: 1,\n            paddingHorizontal: 20\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              backgroundColor: 'white',\n              borderRadius: 12,\n              padding: 20,\n              marginBottom: 20,\n              alignItems: 'center',\n              shadowColor: '#000',\n              shadowOffset: {\n                width: 0,\n                height: 1\n              },\n              shadowOpacity: 0.1,\n              shadowRadius: 3,\n              elevation: 2\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 24,\n                fontWeight: 'bold',\n                color: '#111827',\n                marginBottom: 12,\n                textAlign: 'center'\n              },\n              children: \"\\uD83D\\uDE80 Tap2Go Mobile App\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                textAlign: 'center',\n                marginBottom: 20,\n                lineHeight: 24\n              },\n              children: \"App is loading successfully! Navigate using the footer below.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              style: {\n                backgroundColor: '#f3a823',\n                paddingHorizontal: 24,\n                paddingVertical: 12,\n                borderRadius: 8\n              },\n              onPress: () => {\n                console.log('Test button pressed!');\n                navigation.navigate('Cart');\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                style: {\n                  color: 'white',\n                  fontSize: 16,\n                  fontWeight: '600'\n                },\n                children: \"Go to Cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              backgroundColor: 'white',\n              borderRadius: 12,\n              padding: 20,\n              marginBottom: 20,\n              shadowColor: '#000',\n              shadowOffset: {\n                width: 0,\n                height: 1\n              },\n              shadowOpacity: 0.1,\n              shadowRadius: 3,\n              elevation: 2\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 18,\n                fontWeight: '600',\n                color: '#111827',\n                marginBottom: 12\n              },\n              children: \"Features Available:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                marginBottom: 8,\n                lineHeight: 24\n              },\n              children: \"\\u2022 Browse restaurants and menus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                marginBottom: 8,\n                lineHeight: 24\n              },\n              children: \"\\u2022 Add items to cart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                marginBottom: 8,\n                lineHeight: 24\n              },\n              children: \"\\u2022 Track your orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                marginBottom: 8,\n                lineHeight: 24\n              },\n              children: \"\\u2022 Search for food\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                marginBottom: 8,\n                lineHeight: 24\n              },\n              children: \"\\u2022 Manage your account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              height: 100\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_FooterNavigation.default, {\n        navigation: navigation,\n        activeScreen: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f9fafb'\n        },\n        edges: ['bottom']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 5\n    }, this);\n  }\n  _c = HomeScreen;\n  _nativewind.NativeWindStyleSheet.create({\n    styles: {\n      \"flex\": {\n        \"display\": \"flex\"\n      },\n      \"elevation\": {\n        \"elevation\": 3\n      }\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"HomeScreen\");\n});", "lineCount": 294, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireDefault"], [8, 37, 1, 0], [8, 38, 1, 0, "require"], [8, 45, 1, 0], [8, 46, 1, 0, "_dependencyMap"], [8, 60, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 11, 0], [10, 6, 11, 0, "_reactNativeSafeAreaContext"], [10, 33, 11, 0], [10, 36, 11, 0, "require"], [10, 43, 11, 0], [10, 44, 11, 0, "_dependencyMap"], [10, 58, 11, 0], [11, 2, 14, 0], [11, 6, 14, 0, "_FooterNavigation"], [11, 23, 14, 0], [11, 26, 14, 0, "_interopRequireDefault"], [11, 48, 14, 0], [11, 49, 14, 0, "require"], [11, 56, 14, 0], [11, 57, 14, 0, "_dependencyMap"], [11, 71, 14, 0], [12, 2, 14, 62], [12, 6, 14, 62, "_jsxDevRuntime"], [12, 20, 14, 62], [12, 23, 14, 62, "require"], [12, 30, 14, 62], [12, 31, 14, 62, "_dependencyMap"], [12, 45, 14, 62], [13, 2, 14, 62], [13, 6, 14, 62, "_jsxFileName"], [13, 18, 14, 62], [14, 2, 16, 15], [14, 11, 16, 24, "HomeScreen"], [14, 21, 16, 34, "HomeScreen"], [14, 22, 16, 34, "_ref"], [14, 26, 16, 34], [14, 28, 16, 56], [15, 4, 16, 56], [15, 8, 16, 37, "navigation"], [15, 18, 16, 47], [15, 21, 16, 47, "_ref"], [15, 25, 16, 47], [15, 26, 16, 37, "navigation"], [15, 36, 16, 47], [16, 4, 17, 2, "console"], [16, 11, 17, 9], [16, 12, 17, 10, "log"], [16, 15, 17, 13], [16, 16, 17, 14], [16, 41, 17, 39], [16, 42, 17, 40], [17, 4, 19, 2], [17, 24, 20, 4], [17, 28, 20, 4, "_jsxDevRuntime"], [17, 42, 20, 4], [17, 43, 20, 4, "jsxDEV"], [17, 49, 20, 4], [17, 51, 20, 5, "_reactNative"], [17, 63, 20, 5], [17, 64, 20, 5, "View"], [17, 68, 20, 9], [18, 6, 20, 10, "style"], [18, 11, 20, 15], [18, 13, 20, 17], [19, 8, 20, 19, "flex"], [19, 12, 20, 23], [19, 14, 20, 25], [19, 15, 20, 26], [20, 8, 20, 28, "backgroundColor"], [20, 23, 20, 43], [20, 25, 20, 45], [21, 6, 20, 55], [21, 7, 20, 57], [22, 6, 20, 57, "children"], [22, 14, 20, 57], [22, 30, 22, 6], [22, 34, 22, 6, "_jsxDevRuntime"], [22, 48, 22, 6], [22, 49, 22, 6, "jsxDEV"], [22, 55, 22, 6], [22, 57, 22, 7, "_reactNativeSafeAreaContext"], [22, 84, 22, 7], [22, 85, 22, 7, "SafeAreaView"], [22, 97, 22, 19], [23, 8, 22, 20, "style"], [23, 13, 22, 25], [23, 15, 22, 27], [24, 10, 22, 29, "backgroundColor"], [24, 25, 22, 44], [24, 27, 22, 46], [25, 8, 22, 56], [25, 9, 22, 58], [26, 8, 22, 59, "edges"], [26, 13, 22, 64], [26, 15, 22, 66], [26, 16, 22, 67], [26, 21, 22, 72], [27, 6, 22, 74], [28, 8, 22, 74, "fileName"], [28, 16, 22, 74], [28, 18, 22, 74, "_jsxFileName"], [28, 30, 22, 74], [29, 8, 22, 74, "lineNumber"], [29, 18, 22, 74], [30, 8, 22, 74, "columnNumber"], [30, 20, 22, 74], [31, 6, 22, 74], [31, 13, 22, 76], [31, 14, 22, 77], [31, 29, 25, 6], [31, 33, 25, 6, "_jsxDevRuntime"], [31, 47, 25, 6], [31, 48, 25, 6, "jsxDEV"], [31, 54, 25, 6], [31, 56, 25, 7, "_reactNative"], [31, 68, 25, 7], [31, 69, 25, 7, "View"], [31, 73, 25, 11], [32, 8, 25, 12, "style"], [32, 13, 25, 17], [32, 15, 25, 19], [33, 10, 25, 21, "flex"], [33, 14, 25, 25], [33, 16, 25, 27], [33, 17, 25, 28], [34, 10, 25, 30, "backgroundColor"], [34, 25, 25, 45], [34, 27, 25, 47], [35, 8, 25, 57], [35, 9, 25, 59], [36, 8, 25, 59, "children"], [36, 16, 25, 59], [36, 32, 27, 8], [36, 36, 27, 8, "_jsxDevRuntime"], [36, 50, 27, 8], [36, 51, 27, 8, "jsxDEV"], [36, 57, 27, 8], [36, 59, 27, 9, "_reactNative"], [36, 71, 27, 9], [36, 72, 27, 9, "View"], [36, 76, 27, 13], [37, 10, 27, 14, "style"], [37, 15, 27, 19], [37, 17, 27, 21], [38, 12, 28, 10, "paddingHorizontal"], [38, 29, 28, 27], [38, 31, 28, 29], [38, 33, 28, 31], [39, 12, 29, 10, "paddingTop"], [39, 22, 29, 20], [39, 24, 29, 22], [39, 26, 29, 24], [40, 12, 30, 10, "paddingBottom"], [40, 25, 30, 23], [40, 27, 30, 25], [41, 10, 31, 8], [41, 11, 31, 10], [42, 10, 31, 10, "children"], [42, 18, 31, 10], [42, 34, 32, 10], [42, 38, 32, 10, "_jsxDevRuntime"], [42, 52, 32, 10], [42, 53, 32, 10, "jsxDEV"], [42, 59, 32, 10], [42, 61, 32, 11, "_reactNative"], [42, 73, 32, 11], [42, 74, 32, 11, "Text"], [42, 78, 32, 15], [43, 12, 32, 16, "style"], [43, 17, 32, 21], [43, 19, 32, 23], [44, 14, 32, 25, "fontSize"], [44, 22, 32, 33], [44, 24, 32, 35], [44, 26, 32, 37], [45, 14, 32, 39, "fontWeight"], [45, 24, 32, 49], [45, 26, 32, 51], [45, 32, 32, 57], [46, 14, 32, 59, "color"], [46, 19, 32, 64], [46, 21, 32, 66], [47, 12, 32, 76], [47, 13, 32, 78], [48, 12, 32, 78, "children"], [48, 20, 32, 78], [48, 22, 32, 79], [49, 10, 34, 10], [50, 12, 34, 10, "fileName"], [50, 20, 34, 10], [50, 22, 34, 10, "_jsxFileName"], [50, 34, 34, 10], [51, 12, 34, 10, "lineNumber"], [51, 22, 34, 10], [52, 12, 34, 10, "columnNumber"], [52, 24, 34, 10], [53, 10, 34, 10], [53, 17, 34, 16], [53, 18, 34, 17], [53, 33, 35, 10], [53, 37, 35, 10, "_jsxDevRuntime"], [53, 51, 35, 10], [53, 52, 35, 10, "jsxDEV"], [53, 58, 35, 10], [53, 60, 35, 11, "_reactNative"], [53, 72, 35, 11], [53, 73, 35, 11, "Text"], [53, 77, 35, 15], [54, 12, 35, 16, "style"], [54, 17, 35, 21], [54, 19, 35, 23], [55, 14, 35, 25, "fontSize"], [55, 22, 35, 33], [55, 24, 35, 35], [55, 26, 35, 37], [56, 14, 35, 39, "color"], [56, 19, 35, 44], [56, 21, 35, 46], [56, 30, 35, 55], [57, 14, 35, 57, "marginTop"], [57, 23, 35, 66], [57, 25, 35, 68], [58, 12, 35, 70], [58, 13, 35, 72], [59, 12, 35, 72, "children"], [59, 20, 35, 72], [59, 22, 35, 73], [60, 10, 37, 10], [61, 12, 37, 10, "fileName"], [61, 20, 37, 10], [61, 22, 37, 10, "_jsxFileName"], [61, 34, 37, 10], [62, 12, 37, 10, "lineNumber"], [62, 22, 37, 10], [63, 12, 37, 10, "columnNumber"], [63, 24, 37, 10], [64, 10, 37, 10], [64, 17, 37, 16], [64, 18, 37, 17], [65, 8, 37, 17], [66, 10, 37, 17, "fileName"], [66, 18, 37, 17], [66, 20, 37, 17, "_jsxFileName"], [66, 32, 37, 17], [67, 10, 37, 17, "lineNumber"], [67, 20, 37, 17], [68, 10, 37, 17, "columnNumber"], [68, 22, 37, 17], [69, 8, 37, 17], [69, 15, 38, 14], [69, 16, 38, 15], [69, 31, 40, 8], [69, 35, 40, 8, "_jsxDevRuntime"], [69, 49, 40, 8], [69, 50, 40, 8, "jsxDEV"], [69, 56, 40, 8], [69, 58, 40, 9, "_reactNative"], [69, 70, 40, 9], [69, 71, 40, 9, "ScrollView"], [69, 81, 40, 19], [70, 10, 40, 20, "style"], [70, 15, 40, 25], [70, 17, 40, 27], [71, 12, 40, 29, "flex"], [71, 16, 40, 33], [71, 18, 40, 35], [71, 19, 40, 36], [72, 12, 40, 38, "paddingHorizontal"], [72, 29, 40, 55], [72, 31, 40, 57], [73, 10, 40, 60], [73, 11, 40, 62], [74, 10, 40, 62, "children"], [74, 18, 40, 62], [74, 34, 41, 10], [74, 38, 41, 10, "_jsxDevRuntime"], [74, 52, 41, 10], [74, 53, 41, 10, "jsxDEV"], [74, 59, 41, 10], [74, 61, 41, 11, "_reactNative"], [74, 73, 41, 11], [74, 74, 41, 11, "View"], [74, 78, 41, 15], [75, 12, 41, 16, "style"], [75, 17, 41, 21], [75, 19, 41, 23], [76, 14, 42, 12, "backgroundColor"], [76, 29, 42, 27], [76, 31, 42, 29], [76, 38, 42, 36], [77, 14, 43, 12, "borderRadius"], [77, 26, 43, 24], [77, 28, 43, 26], [77, 30, 43, 28], [78, 14, 44, 12, "padding"], [78, 21, 44, 19], [78, 23, 44, 21], [78, 25, 44, 23], [79, 14, 45, 12, "marginBottom"], [79, 26, 45, 24], [79, 28, 45, 26], [79, 30, 45, 28], [80, 14, 46, 12, "alignItems"], [80, 24, 46, 22], [80, 26, 46, 24], [80, 34, 46, 32], [81, 14, 47, 12, "shadowColor"], [81, 25, 47, 23], [81, 27, 47, 25], [81, 33, 47, 31], [82, 14, 48, 12, "shadowOffset"], [82, 26, 48, 24], [82, 28, 48, 26], [83, 16, 48, 28, "width"], [83, 21, 48, 33], [83, 23, 48, 35], [83, 24, 48, 36], [84, 16, 48, 38, "height"], [84, 22, 48, 44], [84, 24, 48, 46], [85, 14, 48, 48], [85, 15, 48, 49], [86, 14, 49, 12, "shadowOpacity"], [86, 27, 49, 25], [86, 29, 49, 27], [86, 32, 49, 30], [87, 14, 50, 12, "shadowRadius"], [87, 26, 50, 24], [87, 28, 50, 26], [87, 29, 50, 27], [88, 14, 51, 12, "elevation"], [88, 23, 51, 21], [88, 25, 51, 23], [89, 12, 52, 10], [89, 13, 52, 12], [90, 12, 52, 12, "children"], [90, 20, 52, 12], [90, 36, 53, 12], [90, 40, 53, 12, "_jsxDevRuntime"], [90, 54, 53, 12], [90, 55, 53, 12, "jsxDEV"], [90, 61, 53, 12], [90, 63, 53, 13, "_reactNative"], [90, 75, 53, 13], [90, 76, 53, 13, "Text"], [90, 80, 53, 17], [91, 14, 53, 18, "style"], [91, 19, 53, 23], [91, 21, 53, 25], [92, 16, 54, 14, "fontSize"], [92, 24, 54, 22], [92, 26, 54, 24], [92, 28, 54, 26], [93, 16, 55, 14, "fontWeight"], [93, 26, 55, 24], [93, 28, 55, 26], [93, 34, 55, 32], [94, 16, 56, 14, "color"], [94, 21, 56, 19], [94, 23, 56, 21], [94, 32, 56, 30], [95, 16, 57, 14, "marginBottom"], [95, 28, 57, 26], [95, 30, 57, 28], [95, 32, 57, 30], [96, 16, 58, 14, "textAlign"], [96, 25, 58, 23], [96, 27, 58, 25], [97, 14, 59, 12], [97, 15, 59, 14], [98, 14, 59, 14, "children"], [98, 22, 59, 14], [98, 24, 59, 15], [99, 12, 61, 12], [100, 14, 61, 12, "fileName"], [100, 22, 61, 12], [100, 24, 61, 12, "_jsxFileName"], [100, 36, 61, 12], [101, 14, 61, 12, "lineNumber"], [101, 24, 61, 12], [102, 14, 61, 12, "columnNumber"], [102, 26, 61, 12], [103, 12, 61, 12], [103, 19, 61, 18], [103, 20, 61, 19], [103, 35, 62, 12], [103, 39, 62, 12, "_jsxDevRuntime"], [103, 53, 62, 12], [103, 54, 62, 12, "jsxDEV"], [103, 60, 62, 12], [103, 62, 62, 13, "_reactNative"], [103, 74, 62, 13], [103, 75, 62, 13, "Text"], [103, 79, 62, 17], [104, 14, 62, 18, "style"], [104, 19, 62, 23], [104, 21, 62, 25], [105, 16, 63, 14, "fontSize"], [105, 24, 63, 22], [105, 26, 63, 24], [105, 28, 63, 26], [106, 16, 64, 14, "color"], [106, 21, 64, 19], [106, 23, 64, 21], [106, 32, 64, 30], [107, 16, 65, 14, "textAlign"], [107, 25, 65, 23], [107, 27, 65, 25], [107, 35, 65, 33], [108, 16, 66, 14, "marginBottom"], [108, 28, 66, 26], [108, 30, 66, 28], [108, 32, 66, 30], [109, 16, 67, 14, "lineHeight"], [109, 26, 67, 24], [109, 28, 67, 26], [110, 14, 68, 12], [110, 15, 68, 14], [111, 14, 68, 14, "children"], [111, 22, 68, 14], [111, 24, 68, 15], [112, 12, 70, 12], [113, 14, 70, 12, "fileName"], [113, 22, 70, 12], [113, 24, 70, 12, "_jsxFileName"], [113, 36, 70, 12], [114, 14, 70, 12, "lineNumber"], [114, 24, 70, 12], [115, 14, 70, 12, "columnNumber"], [115, 26, 70, 12], [116, 12, 70, 12], [116, 19, 70, 18], [116, 20, 70, 19], [116, 35, 71, 12], [116, 39, 71, 12, "_jsxDevRuntime"], [116, 53, 71, 12], [116, 54, 71, 12, "jsxDEV"], [116, 60, 71, 12], [116, 62, 71, 13, "_reactNative"], [116, 74, 71, 13], [116, 75, 71, 13, "TouchableOpacity"], [116, 91, 71, 29], [117, 14, 72, 14, "style"], [117, 19, 72, 19], [117, 21, 72, 21], [118, 16, 73, 16, "backgroundColor"], [118, 31, 73, 31], [118, 33, 73, 33], [118, 42, 73, 42], [119, 16, 74, 16, "paddingHorizontal"], [119, 33, 74, 33], [119, 35, 74, 35], [119, 37, 74, 37], [120, 16, 75, 16, "paddingVertical"], [120, 31, 75, 31], [120, 33, 75, 33], [120, 35, 75, 35], [121, 16, 76, 16, "borderRadius"], [121, 28, 76, 28], [121, 30, 76, 30], [122, 14, 77, 14], [122, 15, 77, 16], [123, 14, 78, 14, "onPress"], [123, 21, 78, 21], [123, 23, 78, 23, "onPress"], [123, 24, 78, 23], [123, 29, 78, 29], [124, 16, 79, 16, "console"], [124, 23, 79, 23], [124, 24, 79, 24, "log"], [124, 27, 79, 27], [124, 28, 79, 28], [124, 50, 79, 50], [124, 51, 79, 51], [125, 16, 80, 16, "navigation"], [125, 26, 80, 26], [125, 27, 80, 27, "navigate"], [125, 35, 80, 35], [125, 36, 80, 36], [125, 42, 80, 42], [125, 43, 80, 43], [126, 14, 81, 14], [126, 15, 81, 16], [127, 14, 81, 16, "children"], [127, 22, 81, 16], [127, 37, 83, 14], [127, 41, 83, 14, "_jsxDevRuntime"], [127, 55, 83, 14], [127, 56, 83, 14, "jsxDEV"], [127, 62, 83, 14], [127, 64, 83, 15, "_reactNative"], [127, 76, 83, 15], [127, 77, 83, 15, "Text"], [127, 81, 83, 19], [128, 16, 83, 20, "style"], [128, 21, 83, 25], [128, 23, 83, 27], [129, 18, 84, 16, "color"], [129, 23, 84, 21], [129, 25, 84, 23], [129, 32, 84, 30], [130, 18, 85, 16, "fontSize"], [130, 26, 85, 24], [130, 28, 85, 26], [130, 30, 85, 28], [131, 18, 86, 16, "fontWeight"], [131, 28, 86, 26], [131, 30, 86, 28], [132, 16, 87, 14], [132, 17, 87, 16], [133, 16, 87, 16, "children"], [133, 24, 87, 16], [133, 26, 87, 17], [134, 14, 89, 14], [135, 16, 89, 14, "fileName"], [135, 24, 89, 14], [135, 26, 89, 14, "_jsxFileName"], [135, 38, 89, 14], [136, 16, 89, 14, "lineNumber"], [136, 26, 89, 14], [137, 16, 89, 14, "columnNumber"], [137, 28, 89, 14], [138, 14, 89, 14], [138, 21, 89, 20], [139, 12, 89, 21], [140, 14, 89, 21, "fileName"], [140, 22, 89, 21], [140, 24, 89, 21, "_jsxFileName"], [140, 36, 89, 21], [141, 14, 89, 21, "lineNumber"], [141, 24, 89, 21], [142, 14, 89, 21, "columnNumber"], [142, 26, 89, 21], [143, 12, 89, 21], [143, 19, 90, 30], [143, 20, 90, 31], [144, 10, 90, 31], [145, 12, 90, 31, "fileName"], [145, 20, 90, 31], [145, 22, 90, 31, "_jsxFileName"], [145, 34, 90, 31], [146, 12, 90, 31, "lineNumber"], [146, 22, 90, 31], [147, 12, 90, 31, "columnNumber"], [147, 24, 90, 31], [148, 10, 90, 31], [148, 17, 91, 16], [148, 18, 91, 17], [148, 33, 93, 10], [148, 37, 93, 10, "_jsxDevRuntime"], [148, 51, 93, 10], [148, 52, 93, 10, "jsxDEV"], [148, 58, 93, 10], [148, 60, 93, 11, "_reactNative"], [148, 72, 93, 11], [148, 73, 93, 11, "View"], [148, 77, 93, 15], [149, 12, 93, 16, "style"], [149, 17, 93, 21], [149, 19, 93, 23], [150, 14, 94, 12, "backgroundColor"], [150, 29, 94, 27], [150, 31, 94, 29], [150, 38, 94, 36], [151, 14, 95, 12, "borderRadius"], [151, 26, 95, 24], [151, 28, 95, 26], [151, 30, 95, 28], [152, 14, 96, 12, "padding"], [152, 21, 96, 19], [152, 23, 96, 21], [152, 25, 96, 23], [153, 14, 97, 12, "marginBottom"], [153, 26, 97, 24], [153, 28, 97, 26], [153, 30, 97, 28], [154, 14, 98, 12, "shadowColor"], [154, 25, 98, 23], [154, 27, 98, 25], [154, 33, 98, 31], [155, 14, 99, 12, "shadowOffset"], [155, 26, 99, 24], [155, 28, 99, 26], [156, 16, 99, 28, "width"], [156, 21, 99, 33], [156, 23, 99, 35], [156, 24, 99, 36], [157, 16, 99, 38, "height"], [157, 22, 99, 44], [157, 24, 99, 46], [158, 14, 99, 48], [158, 15, 99, 49], [159, 14, 100, 12, "shadowOpacity"], [159, 27, 100, 25], [159, 29, 100, 27], [159, 32, 100, 30], [160, 14, 101, 12, "shadowRadius"], [160, 26, 101, 24], [160, 28, 101, 26], [160, 29, 101, 27], [161, 14, 102, 12, "elevation"], [161, 23, 102, 21], [161, 25, 102, 23], [162, 12, 103, 10], [162, 13, 103, 12], [163, 12, 103, 12, "children"], [163, 20, 103, 12], [163, 36, 104, 12], [163, 40, 104, 12, "_jsxDevRuntime"], [163, 54, 104, 12], [163, 55, 104, 12, "jsxDEV"], [163, 61, 104, 12], [163, 63, 104, 13, "_reactNative"], [163, 75, 104, 13], [163, 76, 104, 13, "Text"], [163, 80, 104, 17], [164, 14, 104, 18, "style"], [164, 19, 104, 23], [164, 21, 104, 25], [165, 16, 105, 14, "fontSize"], [165, 24, 105, 22], [165, 26, 105, 24], [165, 28, 105, 26], [166, 16, 106, 14, "fontWeight"], [166, 26, 106, 24], [166, 28, 106, 26], [166, 33, 106, 31], [167, 16, 107, 14, "color"], [167, 21, 107, 19], [167, 23, 107, 21], [167, 32, 107, 30], [168, 16, 108, 14, "marginBottom"], [168, 28, 108, 26], [168, 30, 108, 28], [169, 14, 109, 12], [169, 15, 109, 14], [170, 14, 109, 14, "children"], [170, 22, 109, 14], [170, 24, 109, 15], [171, 12, 111, 12], [172, 14, 111, 12, "fileName"], [172, 22, 111, 12], [172, 24, 111, 12, "_jsxFileName"], [172, 36, 111, 12], [173, 14, 111, 12, "lineNumber"], [173, 24, 111, 12], [174, 14, 111, 12, "columnNumber"], [174, 26, 111, 12], [175, 12, 111, 12], [175, 19, 111, 18], [175, 20, 111, 19], [175, 35, 112, 12], [175, 39, 112, 12, "_jsxDevRuntime"], [175, 53, 112, 12], [175, 54, 112, 12, "jsxDEV"], [175, 60, 112, 12], [175, 62, 112, 13, "_reactNative"], [175, 74, 112, 13], [175, 75, 112, 13, "Text"], [175, 79, 112, 17], [176, 14, 112, 18, "style"], [176, 19, 112, 23], [176, 21, 112, 25], [177, 16, 112, 27, "fontSize"], [177, 24, 112, 35], [177, 26, 112, 37], [177, 28, 112, 39], [178, 16, 112, 41, "color"], [178, 21, 112, 46], [178, 23, 112, 48], [178, 32, 112, 57], [179, 16, 112, 59, "marginBottom"], [179, 28, 112, 71], [179, 30, 112, 73], [179, 31, 112, 74], [180, 16, 112, 76, "lineHeight"], [180, 26, 112, 86], [180, 28, 112, 88], [181, 14, 112, 91], [181, 15, 112, 93], [182, 14, 112, 93, "children"], [182, 22, 112, 93], [182, 24, 112, 94], [183, 12, 114, 12], [184, 14, 114, 12, "fileName"], [184, 22, 114, 12], [184, 24, 114, 12, "_jsxFileName"], [184, 36, 114, 12], [185, 14, 114, 12, "lineNumber"], [185, 24, 114, 12], [186, 14, 114, 12, "columnNumber"], [186, 26, 114, 12], [187, 12, 114, 12], [187, 19, 114, 18], [187, 20, 114, 19], [187, 35, 115, 12], [187, 39, 115, 12, "_jsxDevRuntime"], [187, 53, 115, 12], [187, 54, 115, 12, "jsxDEV"], [187, 60, 115, 12], [187, 62, 115, 13, "_reactNative"], [187, 74, 115, 13], [187, 75, 115, 13, "Text"], [187, 79, 115, 17], [188, 14, 115, 18, "style"], [188, 19, 115, 23], [188, 21, 115, 25], [189, 16, 115, 27, "fontSize"], [189, 24, 115, 35], [189, 26, 115, 37], [189, 28, 115, 39], [190, 16, 115, 41, "color"], [190, 21, 115, 46], [190, 23, 115, 48], [190, 32, 115, 57], [191, 16, 115, 59, "marginBottom"], [191, 28, 115, 71], [191, 30, 115, 73], [191, 31, 115, 74], [192, 16, 115, 76, "lineHeight"], [192, 26, 115, 86], [192, 28, 115, 88], [193, 14, 115, 91], [193, 15, 115, 93], [194, 14, 115, 93, "children"], [194, 22, 115, 93], [194, 24, 115, 94], [195, 12, 117, 12], [196, 14, 117, 12, "fileName"], [196, 22, 117, 12], [196, 24, 117, 12, "_jsxFileName"], [196, 36, 117, 12], [197, 14, 117, 12, "lineNumber"], [197, 24, 117, 12], [198, 14, 117, 12, "columnNumber"], [198, 26, 117, 12], [199, 12, 117, 12], [199, 19, 117, 18], [199, 20, 117, 19], [199, 35, 118, 12], [199, 39, 118, 12, "_jsxDevRuntime"], [199, 53, 118, 12], [199, 54, 118, 12, "jsxDEV"], [199, 60, 118, 12], [199, 62, 118, 13, "_reactNative"], [199, 74, 118, 13], [199, 75, 118, 13, "Text"], [199, 79, 118, 17], [200, 14, 118, 18, "style"], [200, 19, 118, 23], [200, 21, 118, 25], [201, 16, 118, 27, "fontSize"], [201, 24, 118, 35], [201, 26, 118, 37], [201, 28, 118, 39], [202, 16, 118, 41, "color"], [202, 21, 118, 46], [202, 23, 118, 48], [202, 32, 118, 57], [203, 16, 118, 59, "marginBottom"], [203, 28, 118, 71], [203, 30, 118, 73], [203, 31, 118, 74], [204, 16, 118, 76, "lineHeight"], [204, 26, 118, 86], [204, 28, 118, 88], [205, 14, 118, 91], [205, 15, 118, 93], [206, 14, 118, 93, "children"], [206, 22, 118, 93], [206, 24, 118, 94], [207, 12, 120, 12], [208, 14, 120, 12, "fileName"], [208, 22, 120, 12], [208, 24, 120, 12, "_jsxFileName"], [208, 36, 120, 12], [209, 14, 120, 12, "lineNumber"], [209, 24, 120, 12], [210, 14, 120, 12, "columnNumber"], [210, 26, 120, 12], [211, 12, 120, 12], [211, 19, 120, 18], [211, 20, 120, 19], [211, 35, 121, 12], [211, 39, 121, 12, "_jsxDevRuntime"], [211, 53, 121, 12], [211, 54, 121, 12, "jsxDEV"], [211, 60, 121, 12], [211, 62, 121, 13, "_reactNative"], [211, 74, 121, 13], [211, 75, 121, 13, "Text"], [211, 79, 121, 17], [212, 14, 121, 18, "style"], [212, 19, 121, 23], [212, 21, 121, 25], [213, 16, 121, 27, "fontSize"], [213, 24, 121, 35], [213, 26, 121, 37], [213, 28, 121, 39], [214, 16, 121, 41, "color"], [214, 21, 121, 46], [214, 23, 121, 48], [214, 32, 121, 57], [215, 16, 121, 59, "marginBottom"], [215, 28, 121, 71], [215, 30, 121, 73], [215, 31, 121, 74], [216, 16, 121, 76, "lineHeight"], [216, 26, 121, 86], [216, 28, 121, 88], [217, 14, 121, 91], [217, 15, 121, 93], [218, 14, 121, 93, "children"], [218, 22, 121, 93], [218, 24, 121, 94], [219, 12, 123, 12], [220, 14, 123, 12, "fileName"], [220, 22, 123, 12], [220, 24, 123, 12, "_jsxFileName"], [220, 36, 123, 12], [221, 14, 123, 12, "lineNumber"], [221, 24, 123, 12], [222, 14, 123, 12, "columnNumber"], [222, 26, 123, 12], [223, 12, 123, 12], [223, 19, 123, 18], [223, 20, 123, 19], [223, 35, 124, 12], [223, 39, 124, 12, "_jsxDevRuntime"], [223, 53, 124, 12], [223, 54, 124, 12, "jsxDEV"], [223, 60, 124, 12], [223, 62, 124, 13, "_reactNative"], [223, 74, 124, 13], [223, 75, 124, 13, "Text"], [223, 79, 124, 17], [224, 14, 124, 18, "style"], [224, 19, 124, 23], [224, 21, 124, 25], [225, 16, 124, 27, "fontSize"], [225, 24, 124, 35], [225, 26, 124, 37], [225, 28, 124, 39], [226, 16, 124, 41, "color"], [226, 21, 124, 46], [226, 23, 124, 48], [226, 32, 124, 57], [227, 16, 124, 59, "marginBottom"], [227, 28, 124, 71], [227, 30, 124, 73], [227, 31, 124, 74], [228, 16, 124, 76, "lineHeight"], [228, 26, 124, 86], [228, 28, 124, 88], [229, 14, 124, 91], [229, 15, 124, 93], [230, 14, 124, 93, "children"], [230, 22, 124, 93], [230, 24, 124, 94], [231, 12, 126, 12], [232, 14, 126, 12, "fileName"], [232, 22, 126, 12], [232, 24, 126, 12, "_jsxFileName"], [232, 36, 126, 12], [233, 14, 126, 12, "lineNumber"], [233, 24, 126, 12], [234, 14, 126, 12, "columnNumber"], [234, 26, 126, 12], [235, 12, 126, 12], [235, 19, 126, 18], [235, 20, 126, 19], [236, 10, 126, 19], [237, 12, 126, 19, "fileName"], [237, 20, 126, 19], [237, 22, 126, 19, "_jsxFileName"], [237, 34, 126, 19], [238, 12, 126, 19, "lineNumber"], [238, 22, 126, 19], [239, 12, 126, 19, "columnNumber"], [239, 24, 126, 19], [240, 10, 126, 19], [240, 17, 127, 16], [240, 18, 127, 17], [240, 33, 129, 10], [240, 37, 129, 10, "_jsxDevRuntime"], [240, 51, 129, 10], [240, 52, 129, 10, "jsxDEV"], [240, 58, 129, 10], [240, 60, 129, 11, "_reactNative"], [240, 72, 129, 11], [240, 73, 129, 11, "View"], [240, 77, 129, 15], [241, 12, 129, 16, "style"], [241, 17, 129, 21], [241, 19, 129, 23], [242, 14, 129, 25, "height"], [242, 20, 129, 31], [242, 22, 129, 33], [243, 12, 129, 37], [244, 10, 129, 39], [245, 12, 129, 39, "fileName"], [245, 20, 129, 39], [245, 22, 129, 39, "_jsxFileName"], [245, 34, 129, 39], [246, 12, 129, 39, "lineNumber"], [246, 22, 129, 39], [247, 12, 129, 39, "columnNumber"], [247, 24, 129, 39], [248, 10, 129, 39], [248, 17, 129, 41], [248, 18, 129, 42], [249, 8, 129, 42], [250, 10, 129, 42, "fileName"], [250, 18, 129, 42], [250, 20, 129, 42, "_jsxFileName"], [250, 32, 129, 42], [251, 10, 129, 42, "lineNumber"], [251, 20, 129, 42], [252, 10, 129, 42, "columnNumber"], [252, 22, 129, 42], [253, 8, 129, 42], [253, 15, 130, 20], [253, 16, 130, 21], [254, 6, 130, 21], [255, 8, 130, 21, "fileName"], [255, 16, 130, 21], [255, 18, 130, 21, "_jsxFileName"], [255, 30, 130, 21], [256, 8, 130, 21, "lineNumber"], [256, 18, 130, 21], [257, 8, 130, 21, "columnNumber"], [257, 20, 130, 21], [258, 6, 130, 21], [258, 13, 131, 12], [258, 14, 131, 13], [258, 29, 134, 6], [258, 33, 134, 6, "_jsxDevRuntime"], [258, 47, 134, 6], [258, 48, 134, 6, "jsxDEV"], [258, 54, 134, 6], [258, 56, 134, 7, "_FooterNavigation"], [258, 73, 134, 7], [258, 74, 134, 7, "default"], [258, 81, 134, 23], [259, 8, 134, 24, "navigation"], [259, 18, 134, 34], [259, 20, 134, 36, "navigation"], [259, 30, 134, 47], [260, 8, 134, 48, "activeScreen"], [260, 20, 134, 60], [260, 22, 134, 61], [261, 6, 134, 67], [262, 8, 134, 67, "fileName"], [262, 16, 134, 67], [262, 18, 134, 67, "_jsxFileName"], [262, 30, 134, 67], [263, 8, 134, 67, "lineNumber"], [263, 18, 134, 67], [264, 8, 134, 67, "columnNumber"], [264, 20, 134, 67], [265, 6, 134, 67], [265, 13, 134, 69], [265, 14, 134, 70], [265, 29, 137, 6], [265, 33, 137, 6, "_jsxDevRuntime"], [265, 47, 137, 6], [265, 48, 137, 6, "jsxDEV"], [265, 54, 137, 6], [265, 56, 137, 7, "_reactNativeSafeAreaContext"], [265, 83, 137, 7], [265, 84, 137, 7, "SafeAreaView"], [265, 96, 137, 19], [266, 8, 137, 20, "style"], [266, 13, 137, 25], [266, 15, 137, 27], [267, 10, 137, 29, "backgroundColor"], [267, 25, 137, 44], [267, 27, 137, 46], [268, 8, 137, 56], [268, 9, 137, 58], [269, 8, 137, 59, "edges"], [269, 13, 137, 64], [269, 15, 137, 66], [269, 16, 137, 67], [269, 24, 137, 75], [270, 6, 137, 77], [271, 8, 137, 77, "fileName"], [271, 16, 137, 77], [271, 18, 137, 77, "_jsxFileName"], [271, 30, 137, 77], [272, 8, 137, 77, "lineNumber"], [272, 18, 137, 77], [273, 8, 137, 77, "columnNumber"], [273, 20, 137, 77], [274, 6, 137, 77], [274, 13, 137, 79], [274, 14, 137, 80], [275, 4, 137, 80], [276, 6, 137, 80, "fileName"], [276, 14, 137, 80], [276, 16, 137, 80, "_jsxFileName"], [276, 28, 137, 80], [277, 6, 137, 80, "lineNumber"], [277, 16, 137, 80], [278, 6, 137, 80, "columnNumber"], [278, 18, 137, 80], [279, 4, 137, 80], [279, 11, 138, 10], [279, 12, 138, 11], [280, 2, 140, 0], [281, 2, 140, 1, "_c"], [281, 4, 140, 1], [281, 7, 16, 24, "HomeScreen"], [281, 17, 16, 34], [282, 2, 16, 34, "_nativewind"], [282, 13, 16, 34], [282, 14, 16, 34, "NativeWindStyleSheet"], [282, 34, 16, 34], [282, 35, 16, 34, "create"], [282, 41, 16, 34], [283, 4, 16, 34, "styles"], [283, 10, 16, 34], [284, 6, 16, 34], [285, 8, 16, 34], [286, 6, 16, 34], [287, 6, 16, 34], [288, 8, 16, 34], [289, 6, 16, 34], [290, 4, 16, 34], [291, 2, 16, 34], [292, 2, 16, 34], [292, 6, 16, 34, "_c"], [292, 8, 16, 34], [293, 2, 16, 34, "$RefreshReg$"], [293, 14, 16, 34], [293, 15, 16, 34, "_c"], [293, 17, 16, 34], [294, 0, 16, 34], [294, 3]], "functionMap": {"names": ["<global>", "HomeScreen", "TouchableOpacity.props.onPress"], "mappings": "AAA;eCe;uBC8D;eDG;CD2D"}}, "type": "js/module"}]}