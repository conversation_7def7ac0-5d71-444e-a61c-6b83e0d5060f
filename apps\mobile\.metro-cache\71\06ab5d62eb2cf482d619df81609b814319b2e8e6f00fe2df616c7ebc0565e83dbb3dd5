{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "../utils/ArrayLikeUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 60}}], "key": "qXu86f61IYr2LrrP/ULyK3HO2P0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createDOMRectList = createDOMRectList;\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _ArrayLikeUtils = require(_dependencyMap[5], \"../utils/ArrayLikeUtils\");\n  var _length = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"length\");\n  var DOMRectList = exports.default = /*#__PURE__*/function () {\n    function DOMRectList(elements) {\n      (0, _classCallCheck2.default)(this, DOMRectList);\n      Object.defineProperty(this, _length, {\n        writable: true,\n        value: void 0\n      });\n      for (var i = 0; i < elements.length; i++) {\n        Object.defineProperty(this, i, {\n          value: elements[i],\n          enumerable: true,\n          configurable: false,\n          writable: false\n        });\n      }\n      (0, _classPrivateFieldLooseBase2.default)(this, _length)[_length] = elements.length;\n    }\n    return (0, _createClass2.default)(DOMRectList, [{\n      key: \"length\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _length)[_length];\n      }\n    }, {\n      key: \"item\",\n      value: function item(index) {\n        if (index < 0 || index >= (0, _classPrivateFieldLooseBase2.default)(this, _length)[_length]) {\n          return null;\n        }\n        var arrayLike = this;\n        return arrayLike[index];\n      }\n    }, {\n      key: Symbol.iterator,\n      value: function () {\n        return (0, _ArrayLikeUtils.createValueIterator)(this);\n      }\n    }]);\n  }();\n  function createDOMRectList(elements) {\n    return new DOMRectList(elements);\n  }\n});", "lineCount": 55, "map": [[12, 2, 16, 0], [12, 6, 16, 0, "_ArrayLikeUtils"], [12, 21, 16, 0], [12, 24, 16, 0, "require"], [12, 31, 16, 0], [12, 32, 16, 0, "_dependencyMap"], [12, 46, 16, 0], [13, 2, 16, 60], [13, 6, 16, 60, "_length"], [13, 13, 16, 60], [13, 33, 16, 60, "_classPrivateFieldLooseKey2"], [13, 60, 16, 60], [13, 61, 16, 60, "default"], [13, 68, 16, 60], [14, 2, 16, 60], [14, 6, 22, 21, "DOMRectList"], [14, 17, 22, 32], [14, 20, 22, 32, "exports"], [14, 27, 22, 32], [14, 28, 22, 32, "default"], [14, 35, 22, 32], [15, 4, 31, 2], [15, 13, 31, 2, "DOMRectList"], [15, 25, 31, 14, "elements"], [15, 33, 31, 55], [15, 35, 31, 57], [16, 6, 31, 57], [16, 10, 31, 57, "_classCallCheck2"], [16, 26, 31, 57], [16, 27, 31, 57, "default"], [16, 34, 31, 57], [16, 42, 31, 57, "DOMRectList"], [16, 53, 31, 57], [17, 6, 31, 57, "Object"], [17, 12, 31, 57], [17, 13, 31, 57, "defineProperty"], [17, 27, 31, 57], [17, 34, 31, 57, "_length"], [17, 41, 31, 57], [18, 8, 31, 57, "writable"], [18, 16, 31, 57], [19, 8, 31, 57, "value"], [19, 13, 31, 57], [20, 6, 31, 57], [21, 6, 32, 4], [21, 11, 32, 9], [21, 15, 32, 13, "i"], [21, 16, 32, 14], [21, 19, 32, 17], [21, 20, 32, 18], [21, 22, 32, 20, "i"], [21, 23, 32, 21], [21, 26, 32, 24, "elements"], [21, 34, 32, 32], [21, 35, 32, 33, "length"], [21, 41, 32, 39], [21, 43, 32, 41, "i"], [21, 44, 32, 42], [21, 46, 32, 44], [21, 48, 32, 46], [22, 8, 33, 6, "Object"], [22, 14, 33, 12], [22, 15, 33, 13, "defineProperty"], [22, 29, 33, 27], [22, 30, 33, 28], [22, 34, 33, 32], [22, 36, 33, 34, "i"], [22, 37, 33, 35], [22, 39, 33, 37], [23, 10, 34, 8, "value"], [23, 15, 34, 13], [23, 17, 34, 15, "elements"], [23, 25, 34, 23], [23, 26, 34, 24, "i"], [23, 27, 34, 25], [23, 28, 34, 26], [24, 10, 35, 8, "enumerable"], [24, 20, 35, 18], [24, 22, 35, 20], [24, 26, 35, 24], [25, 10, 36, 8, "configurable"], [25, 22, 36, 20], [25, 24, 36, 22], [25, 29, 36, 27], [26, 10, 37, 8, "writable"], [26, 18, 37, 16], [26, 20, 37, 18], [27, 8, 38, 6], [27, 9, 38, 7], [27, 10, 38, 8], [28, 6, 39, 4], [29, 6, 41, 4], [29, 10, 41, 4, "_classPrivateFieldLooseBase2"], [29, 38, 41, 4], [29, 39, 41, 4, "default"], [29, 46, 41, 4], [29, 52, 41, 8], [29, 54, 41, 8, "_length"], [29, 61, 41, 8], [29, 63, 41, 8, "_length"], [29, 70, 41, 8], [29, 74, 41, 19, "elements"], [29, 82, 41, 27], [29, 83, 41, 28, "length"], [29, 89, 41, 34], [30, 4, 42, 2], [31, 4, 42, 3], [31, 15, 42, 3, "_createClass2"], [31, 28, 42, 3], [31, 29, 42, 3, "default"], [31, 36, 42, 3], [31, 38, 42, 3, "DOMRectList"], [31, 49, 42, 3], [32, 6, 42, 3, "key"], [32, 9, 42, 3], [33, 6, 42, 3, "get"], [33, 9, 42, 3], [33, 11, 44, 2], [33, 20, 44, 2, "get"], [33, 21, 44, 2], [33, 23, 44, 23], [34, 8, 45, 4], [34, 19, 45, 4, "_classPrivateFieldLooseBase2"], [34, 47, 45, 4], [34, 48, 45, 4, "default"], [34, 55, 45, 4], [34, 57, 45, 11], [34, 61, 45, 15], [34, 63, 45, 15, "_length"], [34, 70, 45, 15], [34, 72, 45, 15, "_length"], [34, 79, 45, 15], [35, 6, 46, 2], [36, 4, 46, 3], [37, 6, 46, 3, "key"], [37, 9, 46, 3], [38, 6, 46, 3, "value"], [38, 11, 46, 3], [38, 13, 48, 2], [38, 22, 48, 2, "item"], [38, 26, 48, 6, "item"], [38, 27, 48, 7, "index"], [38, 32, 48, 20], [38, 34, 48, 46], [39, 8, 49, 4], [39, 12, 49, 8, "index"], [39, 17, 49, 13], [39, 20, 49, 16], [39, 21, 49, 17], [39, 25, 49, 21, "index"], [39, 30, 49, 26], [39, 38, 49, 26, "_classPrivateFieldLooseBase2"], [39, 66, 49, 26], [39, 67, 49, 26, "default"], [39, 74, 49, 26], [39, 76, 49, 30], [39, 80, 49, 34], [39, 82, 49, 34, "_length"], [39, 89, 49, 34], [39, 91, 49, 34, "_length"], [39, 98, 49, 34], [39, 99, 49, 42], [39, 101, 49, 44], [40, 10, 50, 6], [40, 17, 50, 13], [40, 21, 50, 17], [41, 8, 51, 4], [42, 8, 56, 4], [42, 12, 56, 10, "arrayLike"], [42, 21, 56, 47], [42, 24, 56, 50], [42, 28, 56, 54], [43, 8, 57, 4], [43, 15, 57, 11, "arrayLike"], [43, 24, 57, 20], [43, 25, 57, 21, "index"], [43, 30, 57, 26], [43, 31, 57, 27], [44, 6, 58, 2], [45, 4, 58, 3], [46, 6, 58, 3, "key"], [46, 9, 58, 3], [46, 11, 61, 3, "Symbol"], [46, 17, 61, 9], [46, 18, 61, 10, "iterator"], [46, 26, 61, 18], [47, 6, 61, 18, "value"], [47, 11, 61, 18], [47, 13, 61, 2], [47, 22, 61, 2, "value"], [47, 23, 61, 2], [47, 25, 61, 49], [48, 8, 62, 4], [48, 15, 62, 11], [48, 19, 62, 11, "createValueIterator"], [48, 54, 62, 30], [48, 56, 62, 31], [48, 60, 62, 35], [48, 61, 62, 36], [49, 6, 63, 2], [50, 4, 63, 3], [51, 2, 63, 3], [52, 2, 72, 7], [52, 11, 72, 16, "createDOMRectList"], [52, 28, 72, 33, "createDOMRectList"], [52, 29, 73, 2, "elements"], [52, 37, 73, 43], [52, 39, 74, 15], [53, 4, 75, 2], [53, 11, 75, 9], [53, 15, 75, 13, "DOMRectList"], [53, 26, 75, 24], [53, 27, 75, 25, "elements"], [53, 35, 75, 33], [53, 36, 75, 34], [54, 2, 76, 0], [55, 0, 76, 1], [55, 3]], "functionMap": {"names": ["<global>", "DOMRectList", "constructor", "get__length", "item", "@@iterator", "createDOMRectList"], "mappings": "AAA;eCqB;ECS;GDW;EEE;GFE;EGE;GHU;EIG;GJE;CDC;OMQ"}}, "type": "js/module"}]}