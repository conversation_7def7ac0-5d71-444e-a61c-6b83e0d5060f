{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.applyRelationProp = applyRelationProp;\n  function applyRelationProp(gesture, relationPropName, relationProp) {\n    if (!relationProp) {\n      return;\n    }\n    if (Array.isArray(relationProp)) {\n      gesture[relationPropName](...relationProp);\n    } else {\n      gesture[relationPropName](relationProp);\n    }\n  }\n});", "lineCount": 16, "map": [[6, 2, 12, 7], [6, 11, 12, 16, "applyRelationProp"], [6, 28, 12, 33, "applyRelationProp"], [6, 29, 13, 2, "gesture"], [6, 36, 13, 27], [6, 38, 14, 2, "relationPropName"], [6, 54, 14, 36], [6, 56, 15, 2, "relationProp"], [6, 68, 15, 32], [6, 70, 16, 2], [7, 4, 17, 2], [7, 8, 17, 6], [7, 9, 17, 7, "relationProp"], [7, 21, 17, 19], [7, 23, 17, 21], [8, 6, 18, 4], [9, 4, 19, 2], [10, 4, 21, 2], [10, 8, 21, 6, "Array"], [10, 13, 21, 11], [10, 14, 21, 12, "isArray"], [10, 21, 21, 19], [10, 22, 21, 20, "relationProp"], [10, 34, 21, 32], [10, 35, 21, 33], [10, 37, 21, 35], [11, 6, 22, 4, "gesture"], [11, 13, 22, 11], [11, 14, 22, 12, "relationPropName"], [11, 30, 22, 28], [11, 31, 22, 29], [11, 32, 22, 30], [11, 35, 22, 33, "relationProp"], [11, 47, 22, 45], [11, 48, 22, 46], [12, 4, 23, 2], [12, 5, 23, 3], [12, 11, 23, 9], [13, 6, 24, 4, "gesture"], [13, 13, 24, 11], [13, 14, 24, 12, "relationPropName"], [13, 30, 24, 28], [13, 31, 24, 29], [13, 32, 24, 30, "relationProp"], [13, 44, 24, 42], [13, 45, 24, 43], [14, 4, 25, 2], [15, 2, 26, 0], [16, 0, 26, 1], [16, 3]], "functionMap": {"names": ["<global>", "applyRelationProp"], "mappings": "AAA;OCW;CDc"}}, "type": "js/module"}]}