{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 68}, "end": {"line": 4, "column": 31, "index": 99}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 100}, "end": {"line": 5, "column": 40, "index": 140}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 141}, "end": {"line": 6, "column": 48, "index": 189}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Background = Background;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _native = require(_dependencyMap[2], \"@react-navigation/native\");\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var _jsxRuntime = require(_dependencyMap[5], \"react/jsx-runtime\");\n  var _excluded = [\"style\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function Background(_ref) {\n    var style = _ref.style,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors;\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {\n      ...rest,\n      style: [{\n        flex: 1,\n        backgroundColor: colors.background\n      }, style]\n    });\n  }\n});", "lineCount": 29, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Background"], [8, 20, 1, 13], [8, 23, 1, 13, "Background"], [8, 33, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_native"], [10, 13, 3, 0], [10, 16, 3, 0, "require"], [10, 23, 3, 0], [10, 24, 3, 0, "_dependencyMap"], [10, 38, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "React"], [11, 11, 4, 0], [11, 14, 4, 0, "_interopRequireWildcard"], [11, 37, 4, 0], [11, 38, 4, 0, "require"], [11, 45, 4, 0], [11, 46, 4, 0, "_dependencyMap"], [11, 60, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_reactNative"], [12, 18, 5, 0], [12, 21, 5, 0, "require"], [12, 28, 5, 0], [12, 29, 5, 0, "_dependencyMap"], [12, 43, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_jsxRuntime"], [13, 17, 6, 0], [13, 20, 6, 0, "require"], [13, 27, 6, 0], [13, 28, 6, 0, "_dependencyMap"], [13, 42, 6, 0], [14, 2, 6, 48], [14, 6, 6, 48, "_excluded"], [14, 15, 6, 48], [15, 2, 6, 48], [15, 11, 6, 48, "_interopRequireWildcard"], [15, 35, 6, 48, "e"], [15, 36, 6, 48], [15, 38, 6, 48, "t"], [15, 39, 6, 48], [15, 68, 6, 48, "WeakMap"], [15, 75, 6, 48], [15, 81, 6, 48, "r"], [15, 82, 6, 48], [15, 89, 6, 48, "WeakMap"], [15, 96, 6, 48], [15, 100, 6, 48, "n"], [15, 101, 6, 48], [15, 108, 6, 48, "WeakMap"], [15, 115, 6, 48], [15, 127, 6, 48, "_interopRequireWildcard"], [15, 150, 6, 48], [15, 162, 6, 48, "_interopRequireWildcard"], [15, 163, 6, 48, "e"], [15, 164, 6, 48], [15, 166, 6, 48, "t"], [15, 167, 6, 48], [15, 176, 6, 48, "t"], [15, 177, 6, 48], [15, 181, 6, 48, "e"], [15, 182, 6, 48], [15, 186, 6, 48, "e"], [15, 187, 6, 48], [15, 188, 6, 48, "__esModule"], [15, 198, 6, 48], [15, 207, 6, 48, "e"], [15, 208, 6, 48], [15, 214, 6, 48, "o"], [15, 215, 6, 48], [15, 217, 6, 48, "i"], [15, 218, 6, 48], [15, 220, 6, 48, "f"], [15, 221, 6, 48], [15, 226, 6, 48, "__proto__"], [15, 235, 6, 48], [15, 243, 6, 48, "default"], [15, 250, 6, 48], [15, 252, 6, 48, "e"], [15, 253, 6, 48], [15, 270, 6, 48, "e"], [15, 271, 6, 48], [15, 294, 6, 48, "e"], [15, 295, 6, 48], [15, 320, 6, 48, "e"], [15, 321, 6, 48], [15, 330, 6, 48, "f"], [15, 331, 6, 48], [15, 337, 6, 48, "o"], [15, 338, 6, 48], [15, 341, 6, 48, "t"], [15, 342, 6, 48], [15, 345, 6, 48, "n"], [15, 346, 6, 48], [15, 349, 6, 48, "r"], [15, 350, 6, 48], [15, 358, 6, 48, "o"], [15, 359, 6, 48], [15, 360, 6, 48, "has"], [15, 363, 6, 48], [15, 364, 6, 48, "e"], [15, 365, 6, 48], [15, 375, 6, 48, "o"], [15, 376, 6, 48], [15, 377, 6, 48, "get"], [15, 380, 6, 48], [15, 381, 6, 48, "e"], [15, 382, 6, 48], [15, 385, 6, 48, "o"], [15, 386, 6, 48], [15, 387, 6, 48, "set"], [15, 390, 6, 48], [15, 391, 6, 48, "e"], [15, 392, 6, 48], [15, 394, 6, 48, "f"], [15, 395, 6, 48], [15, 409, 6, 48, "_t"], [15, 411, 6, 48], [15, 415, 6, 48, "e"], [15, 416, 6, 48], [15, 432, 6, 48, "_t"], [15, 434, 6, 48], [15, 441, 6, 48, "hasOwnProperty"], [15, 455, 6, 48], [15, 456, 6, 48, "call"], [15, 460, 6, 48], [15, 461, 6, 48, "e"], [15, 462, 6, 48], [15, 464, 6, 48, "_t"], [15, 466, 6, 48], [15, 473, 6, 48, "i"], [15, 474, 6, 48], [15, 478, 6, 48, "o"], [15, 479, 6, 48], [15, 482, 6, 48, "Object"], [15, 488, 6, 48], [15, 489, 6, 48, "defineProperty"], [15, 503, 6, 48], [15, 508, 6, 48, "Object"], [15, 514, 6, 48], [15, 515, 6, 48, "getOwnPropertyDescriptor"], [15, 539, 6, 48], [15, 540, 6, 48, "e"], [15, 541, 6, 48], [15, 543, 6, 48, "_t"], [15, 545, 6, 48], [15, 552, 6, 48, "i"], [15, 553, 6, 48], [15, 554, 6, 48, "get"], [15, 557, 6, 48], [15, 561, 6, 48, "i"], [15, 562, 6, 48], [15, 563, 6, 48, "set"], [15, 566, 6, 48], [15, 570, 6, 48, "o"], [15, 571, 6, 48], [15, 572, 6, 48, "f"], [15, 573, 6, 48], [15, 575, 6, 48, "_t"], [15, 577, 6, 48], [15, 579, 6, 48, "i"], [15, 580, 6, 48], [15, 584, 6, 48, "f"], [15, 585, 6, 48], [15, 586, 6, 48, "_t"], [15, 588, 6, 48], [15, 592, 6, 48, "e"], [15, 593, 6, 48], [15, 594, 6, 48, "_t"], [15, 596, 6, 48], [15, 607, 6, 48, "f"], [15, 608, 6, 48], [15, 613, 6, 48, "e"], [15, 614, 6, 48], [15, 616, 6, 48, "t"], [15, 617, 6, 48], [16, 2, 7, 7], [16, 11, 7, 16, "Background"], [16, 21, 7, 26, "Background"], [16, 22, 7, 26, "_ref"], [16, 26, 7, 26], [16, 28, 10, 3], [17, 4, 10, 3], [17, 8, 8, 2, "style"], [17, 13, 8, 7], [17, 16, 8, 7, "_ref"], [17, 20, 8, 7], [17, 21, 8, 2, "style"], [17, 26, 8, 7], [18, 6, 9, 5, "rest"], [18, 10, 9, 9], [18, 17, 9, 9, "_objectWithoutProperties2"], [18, 42, 9, 9], [18, 43, 9, 9, "default"], [18, 50, 9, 9], [18, 52, 9, 9, "_ref"], [18, 56, 9, 9], [18, 58, 9, 9, "_excluded"], [18, 67, 9, 9], [19, 4, 11, 2], [19, 8, 11, 2, "_useTheme"], [19, 17, 11, 2], [19, 20, 13, 6], [19, 24, 13, 6, "useTheme"], [19, 40, 13, 14], [19, 42, 13, 15], [19, 43, 13, 16], [20, 6, 12, 4, "colors"], [20, 12, 12, 10], [20, 15, 12, 10, "_useTheme"], [20, 24, 12, 10], [20, 25, 12, 4, "colors"], [20, 31, 12, 10], [21, 4, 14, 2], [21, 11, 14, 9], [21, 24, 14, 22], [21, 28, 14, 22, "_jsx"], [21, 43, 14, 26], [21, 45, 14, 27, "Animated"], [21, 66, 14, 35], [21, 67, 14, 36, "View"], [21, 71, 14, 40], [21, 73, 14, 42], [22, 6, 15, 4], [22, 9, 15, 7, "rest"], [22, 13, 15, 11], [23, 6, 16, 4, "style"], [23, 11, 16, 9], [23, 13, 16, 11], [23, 14, 16, 12], [24, 8, 17, 6, "flex"], [24, 12, 17, 10], [24, 14, 17, 12], [24, 15, 17, 13], [25, 8, 18, 6, "backgroundColor"], [25, 23, 18, 21], [25, 25, 18, 23, "colors"], [25, 31, 18, 29], [25, 32, 18, 30, "background"], [26, 6, 19, 4], [26, 7, 19, 5], [26, 9, 19, 7, "style"], [26, 14, 19, 12], [27, 4, 20, 2], [27, 5, 20, 3], [27, 6, 20, 4], [28, 2, 21, 0], [29, 0, 21, 1], [29, 3]], "functionMap": {"names": ["<global>", "Background"], "mappings": "AAA;OCM;CDc"}}, "type": "js/module"}]}