{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./Animation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 36}}], "key": "IpfQWRKePvRqyvs8Q+vGFqyieVw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _Animation2 = _interopRequireDefault(require(_dependencyMap[7], \"./Animation\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var DecayAnimation = exports.default = /*#__PURE__*/function (_Animation) {\n    function DecayAnimation(config) {\n      var _this;\n      (0, _classCallCheck2.default)(this, DecayAnimation);\n      _this = _callSuper(this, DecayAnimation, [config]);\n      _this._deceleration = config.deceleration ?? 0.998;\n      _this._velocity = config.velocity;\n      _this._platformConfig = config.platformConfig;\n      return _this;\n    }\n    (0, _inherits2.default)(DecayAnimation, _Animation);\n    return (0, _createClass2.default)(DecayAnimation, [{\n      key: \"__getNativeAnimationConfig\",\n      value: function __getNativeAnimationConfig() {\n        return {\n          type: 'decay',\n          deceleration: this._deceleration,\n          velocity: this._velocity,\n          iterations: this.__iterations,\n          platformConfig: this._platformConfig,\n          debugID: this.__getDebugID()\n        };\n      }\n    }, {\n      key: \"start\",\n      value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n        _superPropGet(DecayAnimation, \"start\", this, 3)([fromValue, onUpdate, onEnd, previousAnimation, animatedValue]);\n        this._lastValue = fromValue;\n        this._fromValue = fromValue;\n        this._onUpdate = onUpdate;\n        this._startTime = Date.now();\n        var useNativeDriver = this.__startAnimationIfNative(animatedValue);\n        if (!useNativeDriver) {\n          this._animationFrame = requestAnimationFrame(() => this.onUpdate());\n        }\n      }\n    }, {\n      key: \"onUpdate\",\n      value: function onUpdate() {\n        var now = Date.now();\n        var value = this._fromValue + this._velocity / (1 - this._deceleration) * (1 - Math.exp(-(1 - this._deceleration) * (now - this._startTime)));\n        this._onUpdate(value);\n        if (Math.abs(this._lastValue - value) < 0.1) {\n          this.__notifyAnimationEnd({\n            finished: true\n          });\n          return;\n        }\n        this._lastValue = value;\n        if (this.__active) {\n          this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n        }\n      }\n    }, {\n      key: \"stop\",\n      value: function stop() {\n        _superPropGet(DecayAnimation, \"stop\", this, 3)([]);\n        if (this._animationFrame != null) {\n          global.cancelAnimationFrame(this._animationFrame);\n        }\n        this.__notifyAnimationEnd({\n          finished: false\n        });\n      }\n    }]);\n  }(_Animation2.default);\n});", "lineCount": 83, "map": [[13, 2, 15, 0], [13, 6, 15, 0, "_Animation2"], [13, 17, 15, 0], [13, 20, 15, 0, "_interopRequireDefault"], [13, 42, 15, 0], [13, 43, 15, 0, "require"], [13, 50, 15, 0], [13, 51, 15, 0, "_dependencyMap"], [13, 65, 15, 0], [14, 2, 15, 36], [14, 11, 15, 36, "_callSuper"], [14, 22, 15, 36, "t"], [14, 23, 15, 36], [14, 25, 15, 36, "o"], [14, 26, 15, 36], [14, 28, 15, 36, "e"], [14, 29, 15, 36], [14, 40, 15, 36, "o"], [14, 41, 15, 36], [14, 48, 15, 36, "_getPrototypeOf2"], [14, 64, 15, 36], [14, 65, 15, 36, "default"], [14, 72, 15, 36], [14, 74, 15, 36, "o"], [14, 75, 15, 36], [14, 82, 15, 36, "_possibleConstructorReturn2"], [14, 109, 15, 36], [14, 110, 15, 36, "default"], [14, 117, 15, 36], [14, 119, 15, 36, "t"], [14, 120, 15, 36], [14, 122, 15, 36, "_isNativeReflectConstruct"], [14, 147, 15, 36], [14, 152, 15, 36, "Reflect"], [14, 159, 15, 36], [14, 160, 15, 36, "construct"], [14, 169, 15, 36], [14, 170, 15, 36, "o"], [14, 171, 15, 36], [14, 173, 15, 36, "e"], [14, 174, 15, 36], [14, 186, 15, 36, "_getPrototypeOf2"], [14, 202, 15, 36], [14, 203, 15, 36, "default"], [14, 210, 15, 36], [14, 212, 15, 36, "t"], [14, 213, 15, 36], [14, 215, 15, 36, "constructor"], [14, 226, 15, 36], [14, 230, 15, 36, "o"], [14, 231, 15, 36], [14, 232, 15, 36, "apply"], [14, 237, 15, 36], [14, 238, 15, 36, "t"], [14, 239, 15, 36], [14, 241, 15, 36, "e"], [14, 242, 15, 36], [15, 2, 15, 36], [15, 11, 15, 36, "_isNativeReflectConstruct"], [15, 37, 15, 36], [15, 51, 15, 36, "t"], [15, 52, 15, 36], [15, 56, 15, 36, "Boolean"], [15, 63, 15, 36], [15, 64, 15, 36, "prototype"], [15, 73, 15, 36], [15, 74, 15, 36, "valueOf"], [15, 81, 15, 36], [15, 82, 15, 36, "call"], [15, 86, 15, 36], [15, 87, 15, 36, "Reflect"], [15, 94, 15, 36], [15, 95, 15, 36, "construct"], [15, 104, 15, 36], [15, 105, 15, 36, "Boolean"], [15, 112, 15, 36], [15, 145, 15, 36, "t"], [15, 146, 15, 36], [15, 159, 15, 36, "_isNativeReflectConstruct"], [15, 184, 15, 36], [15, 196, 15, 36, "_isNativeReflectConstruct"], [15, 197, 15, 36], [15, 210, 15, 36, "t"], [15, 211, 15, 36], [16, 2, 15, 36], [16, 11, 15, 36, "_superPropGet"], [16, 25, 15, 36, "t"], [16, 26, 15, 36], [16, 28, 15, 36, "o"], [16, 29, 15, 36], [16, 31, 15, 36, "e"], [16, 32, 15, 36], [16, 34, 15, 36, "r"], [16, 35, 15, 36], [16, 43, 15, 36, "p"], [16, 44, 15, 36], [16, 51, 15, 36, "_get2"], [16, 56, 15, 36], [16, 57, 15, 36, "default"], [16, 64, 15, 36], [16, 70, 15, 36, "_getPrototypeOf2"], [16, 86, 15, 36], [16, 87, 15, 36, "default"], [16, 94, 15, 36], [16, 100, 15, 36, "r"], [16, 101, 15, 36], [16, 104, 15, 36, "t"], [16, 105, 15, 36], [16, 106, 15, 36, "prototype"], [16, 115, 15, 36], [16, 118, 15, 36, "t"], [16, 119, 15, 36], [16, 122, 15, 36, "o"], [16, 123, 15, 36], [16, 125, 15, 36, "e"], [16, 126, 15, 36], [16, 140, 15, 36, "r"], [16, 141, 15, 36], [16, 166, 15, 36, "p"], [16, 167, 15, 36], [16, 180, 15, 36, "t"], [16, 181, 15, 36], [16, 192, 15, 36, "p"], [16, 193, 15, 36], [16, 194, 15, 36, "apply"], [16, 199, 15, 36], [16, 200, 15, 36, "e"], [16, 201, 15, 36], [16, 203, 15, 36, "t"], [16, 204, 15, 36], [16, 211, 15, 36, "p"], [16, 212, 15, 36], [17, 2, 15, 36], [17, 6, 37, 21, "DecayAnimation"], [17, 20, 37, 35], [17, 23, 37, 35, "exports"], [17, 30, 37, 35], [17, 31, 37, 35, "default"], [17, 38, 37, 35], [17, 64, 37, 35, "_Animation"], [17, 74, 37, 35], [18, 4, 47, 2], [18, 13, 47, 2, "DecayAnimation"], [18, 28, 47, 14, "config"], [18, 34, 47, 48], [18, 36, 47, 50], [19, 6, 47, 50], [19, 10, 47, 50, "_this"], [19, 15, 47, 50], [20, 6, 47, 50], [20, 10, 47, 50, "_classCallCheck2"], [20, 26, 47, 50], [20, 27, 47, 50, "default"], [20, 34, 47, 50], [20, 42, 47, 50, "DecayAnimation"], [20, 56, 47, 50], [21, 6, 48, 4, "_this"], [21, 11, 48, 4], [21, 14, 48, 4, "_callSuper"], [21, 24, 48, 4], [21, 31, 48, 4, "DecayAnimation"], [21, 45, 48, 4], [21, 48, 48, 10, "config"], [21, 54, 48, 16], [22, 6, 50, 4, "_this"], [22, 11, 50, 4], [22, 12, 50, 9, "_deceleration"], [22, 25, 50, 22], [22, 28, 50, 25, "config"], [22, 34, 50, 31], [22, 35, 50, 32, "deceleration"], [22, 47, 50, 44], [22, 51, 50, 48], [22, 56, 50, 53], [23, 6, 51, 4, "_this"], [23, 11, 51, 4], [23, 12, 51, 9, "_velocity"], [23, 21, 51, 18], [23, 24, 51, 21, "config"], [23, 30, 51, 27], [23, 31, 51, 28, "velocity"], [23, 39, 51, 36], [24, 6, 52, 4, "_this"], [24, 11, 52, 4], [24, 12, 52, 9, "_platformConfig"], [24, 27, 52, 24], [24, 30, 52, 27, "config"], [24, 36, 52, 33], [24, 37, 52, 34, "platformConfig"], [24, 51, 52, 48], [25, 6, 52, 49], [25, 13, 52, 49, "_this"], [25, 18, 52, 49], [26, 4, 53, 2], [27, 4, 53, 3], [27, 8, 53, 3, "_inherits2"], [27, 18, 53, 3], [27, 19, 53, 3, "default"], [27, 26, 53, 3], [27, 28, 53, 3, "DecayAnimation"], [27, 42, 53, 3], [27, 44, 53, 3, "_Animation"], [27, 54, 53, 3], [28, 4, 53, 3], [28, 15, 53, 3, "_createClass2"], [28, 28, 53, 3], [28, 29, 53, 3, "default"], [28, 36, 53, 3], [28, 38, 53, 3, "DecayAnimation"], [28, 52, 53, 3], [29, 6, 53, 3, "key"], [29, 9, 53, 3], [30, 6, 53, 3, "value"], [30, 11, 53, 3], [30, 13, 55, 2], [30, 22, 55, 2, "__getNativeAnimationConfig"], [30, 48, 55, 28, "__getNativeAnimationConfig"], [30, 49, 55, 28], [30, 51, 62, 5], [31, 8, 63, 4], [31, 15, 63, 11], [32, 10, 64, 6, "type"], [32, 14, 64, 10], [32, 16, 64, 12], [32, 23, 64, 19], [33, 10, 65, 6, "deceleration"], [33, 22, 65, 18], [33, 24, 65, 20], [33, 28, 65, 24], [33, 29, 65, 25, "_deceleration"], [33, 42, 65, 38], [34, 10, 66, 6, "velocity"], [34, 18, 66, 14], [34, 20, 66, 16], [34, 24, 66, 20], [34, 25, 66, 21, "_velocity"], [34, 34, 66, 30], [35, 10, 67, 6, "iterations"], [35, 20, 67, 16], [35, 22, 67, 18], [35, 26, 67, 22], [35, 27, 67, 23, "__iterations"], [35, 39, 67, 35], [36, 10, 68, 6, "platformConfig"], [36, 24, 68, 20], [36, 26, 68, 22], [36, 30, 68, 26], [36, 31, 68, 27, "_platformConfig"], [36, 46, 68, 42], [37, 10, 69, 6, "debugID"], [37, 17, 69, 13], [37, 19, 69, 15], [37, 23, 69, 19], [37, 24, 69, 20, "__getDebugID"], [37, 36, 69, 32], [37, 37, 69, 33], [38, 8, 70, 4], [38, 9, 70, 5], [39, 6, 71, 2], [40, 4, 71, 3], [41, 6, 71, 3, "key"], [41, 9, 71, 3], [42, 6, 71, 3, "value"], [42, 11, 71, 3], [42, 13, 73, 2], [42, 22, 73, 2, "start"], [42, 27, 73, 7, "start"], [42, 28, 74, 4, "fromValue"], [42, 37, 74, 21], [42, 39, 75, 4, "onUpdate"], [42, 47, 75, 37], [42, 49, 76, 4, "onEnd"], [42, 54, 76, 23], [42, 56, 77, 4, "previousAnimation"], [42, 73, 77, 33], [42, 75, 78, 4, "animatedValue"], [42, 88, 78, 32], [42, 90, 79, 10], [43, 8, 80, 4, "_superPropGet"], [43, 21, 80, 4], [43, 22, 80, 4, "DecayAnimation"], [43, 36, 80, 4], [43, 57, 80, 16, "fromValue"], [43, 66, 80, 25], [43, 68, 80, 27, "onUpdate"], [43, 76, 80, 35], [43, 78, 80, 37, "onEnd"], [43, 83, 80, 42], [43, 85, 80, 44, "previousAnimation"], [43, 102, 80, 61], [43, 104, 80, 63, "animatedValue"], [43, 117, 80, 76], [44, 8, 82, 4], [44, 12, 82, 8], [44, 13, 82, 9, "_lastValue"], [44, 23, 82, 19], [44, 26, 82, 22, "fromValue"], [44, 35, 82, 31], [45, 8, 83, 4], [45, 12, 83, 8], [45, 13, 83, 9, "_fromValue"], [45, 23, 83, 19], [45, 26, 83, 22, "fromValue"], [45, 35, 83, 31], [46, 8, 84, 4], [46, 12, 84, 8], [46, 13, 84, 9, "_onUpdate"], [46, 22, 84, 18], [46, 25, 84, 21, "onUpdate"], [46, 33, 84, 29], [47, 8, 85, 4], [47, 12, 85, 8], [47, 13, 85, 9, "_startTime"], [47, 23, 85, 19], [47, 26, 85, 22, "Date"], [47, 30, 85, 26], [47, 31, 85, 27, "now"], [47, 34, 85, 30], [47, 35, 85, 31], [47, 36, 85, 32], [48, 8, 87, 4], [48, 12, 87, 10, "useNativeDriver"], [48, 27, 87, 25], [48, 30, 87, 28], [48, 34, 87, 32], [48, 35, 87, 33, "__startAnimationIfNative"], [48, 59, 87, 57], [48, 60, 87, 58, "animatedValue"], [48, 73, 87, 71], [48, 74, 87, 72], [49, 8, 88, 4], [49, 12, 88, 8], [49, 13, 88, 9, "useNativeDriver"], [49, 28, 88, 24], [49, 30, 88, 26], [50, 10, 89, 6], [50, 14, 89, 10], [50, 15, 89, 11, "_animationFrame"], [50, 30, 89, 26], [50, 33, 89, 29, "requestAnimationFrame"], [50, 54, 89, 50], [50, 55, 89, 51], [50, 61, 89, 57], [50, 65, 89, 61], [50, 66, 89, 62, "onUpdate"], [50, 74, 89, 70], [50, 75, 89, 71], [50, 76, 89, 72], [50, 77, 89, 73], [51, 8, 90, 4], [52, 6, 91, 2], [53, 4, 91, 3], [54, 6, 91, 3, "key"], [54, 9, 91, 3], [55, 6, 91, 3, "value"], [55, 11, 91, 3], [55, 13, 93, 2], [55, 22, 93, 2, "onUpdate"], [55, 30, 93, 10, "onUpdate"], [55, 31, 93, 10], [55, 33, 93, 19], [56, 8, 94, 4], [56, 12, 94, 10, "now"], [56, 15, 94, 13], [56, 18, 94, 16, "Date"], [56, 22, 94, 20], [56, 23, 94, 21, "now"], [56, 26, 94, 24], [56, 27, 94, 25], [56, 28, 94, 26], [57, 8, 96, 4], [57, 12, 96, 10, "value"], [57, 17, 96, 15], [57, 20, 97, 6], [57, 24, 97, 10], [57, 25, 97, 11, "_fromValue"], [57, 35, 97, 21], [57, 38, 98, 7], [57, 42, 98, 11], [57, 43, 98, 12, "_velocity"], [57, 52, 98, 21], [57, 56, 98, 25], [57, 57, 98, 26], [57, 60, 98, 29], [57, 64, 98, 33], [57, 65, 98, 34, "_deceleration"], [57, 78, 98, 47], [57, 79, 98, 48], [57, 83, 99, 9], [57, 84, 99, 10], [57, 87, 99, 13, "Math"], [57, 91, 99, 17], [57, 92, 99, 18, "exp"], [57, 95, 99, 21], [57, 96, 99, 22], [57, 98, 99, 24], [57, 99, 99, 25], [57, 102, 99, 28], [57, 106, 99, 32], [57, 107, 99, 33, "_deceleration"], [57, 120, 99, 46], [57, 121, 99, 47], [57, 125, 99, 51, "now"], [57, 128, 99, 54], [57, 131, 99, 57], [57, 135, 99, 61], [57, 136, 99, 62, "_startTime"], [57, 146, 99, 72], [57, 147, 99, 73], [57, 148, 99, 74], [57, 149, 99, 75], [58, 8, 101, 4], [58, 12, 101, 8], [58, 13, 101, 9, "_onUpdate"], [58, 22, 101, 18], [58, 23, 101, 19, "value"], [58, 28, 101, 24], [58, 29, 101, 25], [59, 8, 103, 4], [59, 12, 103, 8, "Math"], [59, 16, 103, 12], [59, 17, 103, 13, "abs"], [59, 20, 103, 16], [59, 21, 103, 17], [59, 25, 103, 21], [59, 26, 103, 22, "_lastValue"], [59, 36, 103, 32], [59, 39, 103, 35, "value"], [59, 44, 103, 40], [59, 45, 103, 41], [59, 48, 103, 44], [59, 51, 103, 47], [59, 53, 103, 49], [60, 10, 104, 6], [60, 14, 104, 10], [60, 15, 104, 11, "__notifyAnimationEnd"], [60, 35, 104, 31], [60, 36, 104, 32], [61, 12, 104, 33, "finished"], [61, 20, 104, 41], [61, 22, 104, 43], [62, 10, 104, 47], [62, 11, 104, 48], [62, 12, 104, 49], [63, 10, 105, 6], [64, 8, 106, 4], [65, 8, 108, 4], [65, 12, 108, 8], [65, 13, 108, 9, "_lastValue"], [65, 23, 108, 19], [65, 26, 108, 22, "value"], [65, 31, 108, 27], [66, 8, 109, 4], [66, 12, 109, 8], [66, 16, 109, 12], [66, 17, 109, 13, "__active"], [66, 25, 109, 21], [66, 27, 109, 23], [67, 10, 111, 6], [67, 14, 111, 10], [67, 15, 111, 11, "_animationFrame"], [67, 30, 111, 26], [67, 33, 111, 29, "requestAnimationFrame"], [67, 54, 111, 50], [67, 55, 111, 51], [67, 59, 111, 55], [67, 60, 111, 56, "onUpdate"], [67, 68, 111, 64], [67, 69, 111, 65, "bind"], [67, 73, 111, 69], [67, 74, 111, 70], [67, 78, 111, 74], [67, 79, 111, 75], [67, 80, 111, 76], [68, 8, 112, 4], [69, 6, 113, 2], [70, 4, 113, 3], [71, 6, 113, 3, "key"], [71, 9, 113, 3], [72, 6, 113, 3, "value"], [72, 11, 113, 3], [72, 13, 115, 2], [72, 22, 115, 2, "stop"], [72, 26, 115, 6, "stop"], [72, 27, 115, 6], [72, 29, 115, 15], [73, 8, 116, 4, "_superPropGet"], [73, 21, 116, 4], [73, 22, 116, 4, "DecayAnimation"], [73, 36, 116, 4], [74, 8, 117, 4], [74, 12, 117, 8], [74, 16, 117, 12], [74, 17, 117, 13, "_animationFrame"], [74, 32, 117, 28], [74, 36, 117, 32], [74, 40, 117, 36], [74, 42, 117, 38], [75, 10, 118, 6, "global"], [75, 16, 118, 12], [75, 17, 118, 13, "cancelAnimationFrame"], [75, 37, 118, 33], [75, 38, 118, 34], [75, 42, 118, 38], [75, 43, 118, 39, "_animationFrame"], [75, 58, 118, 54], [75, 59, 118, 55], [76, 8, 119, 4], [77, 8, 120, 4], [77, 12, 120, 8], [77, 13, 120, 9, "__notifyAnimationEnd"], [77, 33, 120, 29], [77, 34, 120, 30], [78, 10, 120, 31, "finished"], [78, 18, 120, 39], [78, 20, 120, 41], [79, 8, 120, 46], [79, 9, 120, 47], [79, 10, 120, 48], [80, 6, 121, 2], [81, 4, 121, 3], [82, 2, 121, 3], [82, 4, 37, 44, "Animation"], [82, 23, 37, 53], [83, 0, 37, 53], [83, 3]], "functionMap": {"names": ["<global>", "DecayAnimation", "constructor", "__getNativeAnimationConfig", "start", "requestAnimationFrame$argument_0", "onUpdate", "stop"], "mappings": "AAA;eCoC;ECU;GDM;EEE;GFgB;EGE;mDCgB,qBD;GHE;EKE;GLoB;EME;GNM"}}, "type": "js/module"}]}