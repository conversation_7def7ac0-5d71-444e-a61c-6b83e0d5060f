{"dependencies": [{"name": "../../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 47, "index": 61}}], "key": "eT202ujluoOcHDbauyWnF/muvbc=", "exportNames": ["*"]}}, {"name": "../../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 62}, "end": {"line": 3, "column": 38, "index": 100}}], "key": "BAUnomHCaPEo8SwbXzlKtt9pd/8=", "exportNames": ["*"]}}, {"name": "../../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 101}, "end": {"line": 4, "column": 58, "index": 159}}], "key": "pPfOdxbh9mtPdO2EBvl67ARfj+c=", "exportNames": ["*"]}}, {"name": "./componentStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 243}, "end": {"line": 6, "column": 65, "index": 308}}], "key": "fOjZFTm/VXjk+R+fwki3Uvzs2Zo=", "exportNames": ["*"]}}, {"name": "./config", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 357}, "end": {"line": 8, "column": 38, "index": 395}}], "key": "apL7GyCxHQJfXSypmIMW0g+q+wo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.addHTMLMutationObserver = addHTMLMutationObserver;\n  exports.areDOMRectsEqual = areDOMRectsEqual;\n  exports.configureWebLayoutAnimations = configureWebLayoutAnimations;\n  exports.insertWebAnimation = insertWebAnimation;\n  exports.scheduleAnimationCleanup = scheduleAnimationCleanup;\n  var _errors = require(_dependencyMap[0], \"../../errors\");\n  var _logger = require(_dependencyMap[1], \"../../logger\");\n  var _PlatformChecker = require(_dependencyMap[2], \"../../PlatformChecker\");\n  var _componentStyle = require(_dependencyMap[3], \"./componentStyle\");\n  var _config = require(_dependencyMap[4], \"./config\");\n  var PREDEFINED_WEB_ANIMATIONS_ID = 'ReanimatedPredefinedWebAnimationsStyle';\n  var CUSTOM_WEB_ANIMATIONS_ID = 'ReanimatedCustomWebAnimationsStyle';\n\n  // Since we cannot remove keyframe from DOM by its name, we have to store its id\n  var animationNameToIndex = new Map();\n  var animationNameList = [];\n  var isObserverSet = false;\n\n  /**\n   * Creates `HTMLStyleElement`, inserts it into DOM and then inserts CSS rules\n   * into the stylesheet. If style element already exists, nothing happens.\n   */\n  function configureWebLayoutAnimations() {\n    if (!(0, _PlatformChecker.isWindowAvailable)() ||\n    // Without this check SSR crashes because document is undefined (NextExample on CI)\n    document.getElementById(PREDEFINED_WEB_ANIMATIONS_ID) !== null) {\n      return;\n    }\n    var predefinedAnimationsStyleTag = document.createElement('style');\n    predefinedAnimationsStyleTag.id = PREDEFINED_WEB_ANIMATIONS_ID;\n    predefinedAnimationsStyleTag.onload = () => {\n      if (!predefinedAnimationsStyleTag.sheet) {\n        _logger.logger.error('Failed to create layout animations stylesheet.');\n        return;\n      }\n      for (var animationName in _config.Animations) {\n        predefinedAnimationsStyleTag.sheet.insertRule(_config.Animations[animationName].style);\n      }\n    };\n    var customAnimationsStyleTag = document.createElement('style');\n    customAnimationsStyleTag.id = CUSTOM_WEB_ANIMATIONS_ID;\n    document.head.appendChild(predefinedAnimationsStyleTag);\n    document.head.appendChild(customAnimationsStyleTag);\n  }\n  function insertWebAnimation(animationName, keyframe) {\n    // Without this check SSR crashes because document is undefined (NextExample on CI)\n    if (!(0, _PlatformChecker.isWindowAvailable)()) {\n      return;\n    }\n    var styleTag = document.getElementById(CUSTOM_WEB_ANIMATIONS_ID);\n    if (!styleTag.sheet) {\n      _logger.logger.error('Failed to create layout animations stylesheet.');\n      return;\n    }\n    styleTag.sheet.insertRule(keyframe, 0);\n    animationNameList.unshift(animationName);\n    animationNameToIndex.set(animationName, 0);\n    for (var i = 1; i < animationNameList.length; ++i) {\n      var nextAnimationName = animationNameList[i];\n      var nextAnimationIndex = animationNameToIndex.get(nextAnimationName);\n      if (nextAnimationIndex === undefined) {\n        throw new _errors.ReanimatedError('Failed to obtain animation index.');\n      }\n      animationNameToIndex.set(animationNameList[i], nextAnimationIndex + 1);\n    }\n  }\n  function removeWebAnimation(animationName, animationRemoveCallback) {\n    // Without this check SSR crashes because document is undefined (NextExample on CI)\n    if (!(0, _PlatformChecker.isWindowAvailable)()) {\n      return;\n    }\n    var styleTag = document.getElementById(CUSTOM_WEB_ANIMATIONS_ID);\n    var currentAnimationIndex = animationNameToIndex.get(animationName);\n    if (currentAnimationIndex === undefined) {\n      throw new _errors.ReanimatedError('Failed to obtain animation index.');\n    }\n    animationRemoveCallback();\n    styleTag.sheet?.deleteRule(currentAnimationIndex);\n    animationNameList.splice(currentAnimationIndex, 1);\n    animationNameToIndex.delete(animationName);\n    for (var i = currentAnimationIndex; i < animationNameList.length; ++i) {\n      var nextAnimationName = animationNameList[i];\n      var nextAnimationIndex = animationNameToIndex.get(nextAnimationName);\n      if (nextAnimationIndex === undefined) {\n        throw new _errors.ReanimatedError('Failed to obtain animation index.');\n      }\n      animationNameToIndex.set(animationNameList[i], nextAnimationIndex - 1);\n    }\n  }\n  var timeoutScale = 1.25; // We use this value to enlarge timeout duration. It can prove useful if animation lags.\n  var frameDurationMs = 16; // Just an approximation.\n  var minimumFrames = 10;\n  function scheduleAnimationCleanup(animationName, animationDuration, animationRemoveCallback) {\n    // If duration is very short, we want to keep remove delay to at least 10 frames\n    // In our case it is exactly 160/1099 s, which is approximately 0.15s\n    var timeoutValue = Math.max(animationDuration * timeoutScale * 1000, animationDuration + frameDurationMs * minimumFrames);\n    setTimeout(() => removeWebAnimation(animationName, animationRemoveCallback), timeoutValue);\n  }\n  function reattachElementToAncestor(child, parent) {\n    var childSnapshot = _componentStyle.snapshots.get(child);\n    if (!childSnapshot) {\n      _logger.logger.error('Failed to obtain snapshot.');\n      return;\n    }\n\n    // We use that so we don't end up in infinite loop\n    child.removedAfterAnimation = true;\n    parent.appendChild(child);\n    (0, _componentStyle.setElementPosition)(child, childSnapshot);\n    var originalOnAnimationEnd = child.onanimationend;\n    child.onanimationend = function (event) {\n      parent.removeChild(child);\n\n      // Given that this function overrides onAnimationEnd, it won't be null\n      originalOnAnimationEnd?.call(this, event);\n    };\n  }\n  function findDescendantWithExitingAnimation(node, root) {\n    // Node could be something else than HTMLElement, for example TextNode (treated as plain text, not as HTML object),\n    // therefore it won't have children prop and calling Array.from(node.children) will cause error.\n    if (!(node instanceof HTMLElement)) {\n      return;\n    }\n    if (node.reanimatedDummy && node.removedAfterAnimation === undefined) {\n      reattachElementToAncestor(node, root);\n    }\n    var children = Array.from(node.children);\n    for (var i = 0; i < children.length; ++i) {\n      findDescendantWithExitingAnimation(children[i], root);\n    }\n  }\n  function checkIfScreenWasChanged(mutationTarget) {\n    var reactFiberKey = '__reactFiber';\n    for (var _key of Object.keys(mutationTarget)) {\n      if (_key.startsWith('__reactFiber')) {\n        reactFiberKey = _key;\n        break;\n      }\n    }\n    return mutationTarget[reactFiberKey]?.child?.memoizedProps?.navigation !== undefined;\n  }\n  function addHTMLMutationObserver() {\n    if (isObserverSet || !(0, _PlatformChecker.isWindowAvailable)()) {\n      return;\n    }\n    isObserverSet = true;\n    var observer = new MutationObserver(mutationsList => {\n      var rootMutation = mutationsList[mutationsList.length - 1];\n      if (checkIfScreenWasChanged(rootMutation.target)) {\n        return;\n      }\n      for (var i = 0; i < rootMutation.removedNodes.length; ++i) {\n        findDescendantWithExitingAnimation(rootMutation.removedNodes[i], rootMutation.target);\n      }\n    });\n    observer.observe(document.body, {\n      childList: true,\n      subtree: true\n    });\n  }\n  function areDOMRectsEqual(r1, r2) {\n    // There are 4 more fields, but checking these should suffice\n    return r1.x === r2.x && r1.y === r2.y && r1.width === r2.width && r1.height === r2.height;\n  }\n});", "lineCount": 171, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "addHTMLMutationObserver"], [7, 33, 1, 13], [7, 36, 1, 13, "addHTMLMutationObserver"], [7, 59, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "areDOMRectsEqual"], [8, 26, 1, 13], [8, 29, 1, 13, "areDOMRectsEqual"], [8, 45, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "configureWebLayoutAnimations"], [9, 38, 1, 13], [9, 41, 1, 13, "configureWebLayoutAnimations"], [9, 69, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "insertWebAnimation"], [10, 28, 1, 13], [10, 31, 1, 13, "insertWebAnimation"], [10, 49, 1, 13], [11, 2, 1, 13, "exports"], [11, 9, 1, 13], [11, 10, 1, 13, "scheduleAnimationCleanup"], [11, 34, 1, 13], [11, 37, 1, 13, "scheduleAnimationCleanup"], [11, 61, 1, 13], [12, 2, 2, 0], [12, 6, 2, 0, "_errors"], [12, 13, 2, 0], [12, 16, 2, 0, "require"], [12, 23, 2, 0], [12, 24, 2, 0, "_dependencyMap"], [12, 38, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_logger"], [13, 13, 3, 0], [13, 16, 3, 0, "require"], [13, 23, 3, 0], [13, 24, 3, 0, "_dependencyMap"], [13, 38, 3, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_PlatformChecker"], [14, 22, 4, 0], [14, 25, 4, 0, "require"], [14, 32, 4, 0], [14, 33, 4, 0, "_dependencyMap"], [14, 47, 4, 0], [15, 2, 6, 0], [15, 6, 6, 0, "_componentStyle"], [15, 21, 6, 0], [15, 24, 6, 0, "require"], [15, 31, 6, 0], [15, 32, 6, 0, "_dependencyMap"], [15, 46, 6, 0], [16, 2, 8, 0], [16, 6, 8, 0, "_config"], [16, 13, 8, 0], [16, 16, 8, 0, "require"], [16, 23, 8, 0], [16, 24, 8, 0, "_dependencyMap"], [16, 38, 8, 0], [17, 2, 10, 0], [17, 6, 10, 6, "PREDEFINED_WEB_ANIMATIONS_ID"], [17, 34, 10, 34], [17, 37, 10, 37], [17, 77, 10, 77], [18, 2, 11, 0], [18, 6, 11, 6, "CUSTOM_WEB_ANIMATIONS_ID"], [18, 30, 11, 30], [18, 33, 11, 33], [18, 69, 11, 69], [20, 2, 13, 0], [21, 2, 14, 0], [21, 6, 14, 6, "animationNameToIndex"], [21, 26, 14, 26], [21, 29, 14, 29], [21, 33, 14, 33, "Map"], [21, 36, 14, 36], [21, 37, 14, 53], [21, 38, 14, 54], [22, 2, 15, 0], [22, 6, 15, 6, "animationNameList"], [22, 23, 15, 33], [22, 26, 15, 36], [22, 28, 15, 38], [23, 2, 17, 0], [23, 6, 17, 4, "isObserverSet"], [23, 19, 17, 17], [23, 22, 17, 20], [23, 27, 17, 25], [25, 2, 19, 0], [26, 0, 20, 0], [27, 0, 21, 0], [28, 0, 22, 0], [29, 2, 23, 7], [29, 11, 23, 16, "configureWebLayoutAnimations"], [29, 39, 23, 44, "configureWebLayoutAnimations"], [29, 40, 23, 44], [29, 42, 23, 47], [30, 4, 24, 2], [30, 8, 25, 4], [30, 9, 25, 5], [30, 13, 25, 5, "isWindowAvailable"], [30, 47, 25, 22], [30, 49, 25, 23], [30, 50, 25, 24], [31, 4, 25, 28], [32, 4, 26, 4, "document"], [32, 12, 26, 12], [32, 13, 26, 13, "getElementById"], [32, 27, 26, 27], [32, 28, 26, 28, "PREDEFINED_WEB_ANIMATIONS_ID"], [32, 56, 26, 56], [32, 57, 26, 57], [32, 62, 26, 62], [32, 66, 26, 66], [32, 68, 27, 4], [33, 6, 28, 4], [34, 4, 29, 2], [35, 4, 31, 2], [35, 8, 31, 8, "predefinedAnimationsStyleTag"], [35, 36, 31, 36], [35, 39, 31, 39, "document"], [35, 47, 31, 47], [35, 48, 31, 48, "createElement"], [35, 61, 31, 61], [35, 62, 31, 62], [35, 69, 31, 69], [35, 70, 31, 70], [36, 4, 32, 2, "predefinedAnimationsStyleTag"], [36, 32, 32, 30], [36, 33, 32, 31, "id"], [36, 35, 32, 33], [36, 38, 32, 36, "PREDEFINED_WEB_ANIMATIONS_ID"], [36, 66, 32, 64], [37, 4, 34, 2, "predefinedAnimationsStyleTag"], [37, 32, 34, 30], [37, 33, 34, 31, "onload"], [37, 39, 34, 37], [37, 42, 34, 40], [37, 48, 34, 46], [38, 6, 35, 4], [38, 10, 35, 8], [38, 11, 35, 9, "predefinedAnimationsStyleTag"], [38, 39, 35, 37], [38, 40, 35, 38, "sheet"], [38, 45, 35, 43], [38, 47, 35, 45], [39, 8, 36, 6, "logger"], [39, 22, 36, 12], [39, 23, 36, 13, "error"], [39, 28, 36, 18], [39, 29, 36, 19], [39, 77, 36, 67], [39, 78, 36, 68], [40, 8, 37, 6], [41, 6, 38, 4], [42, 6, 40, 4], [42, 11, 40, 9], [42, 15, 40, 15, "animationName"], [42, 28, 40, 28], [42, 32, 40, 32, "Animations"], [42, 50, 40, 42], [42, 52, 40, 44], [43, 8, 41, 6, "predefinedAnimationsStyleTag"], [43, 36, 41, 34], [43, 37, 41, 35, "sheet"], [43, 42, 41, 40], [43, 43, 41, 41, "insertRule"], [43, 53, 41, 51], [43, 54, 42, 8, "Animations"], [43, 72, 42, 18], [43, 73, 42, 19, "animationName"], [43, 86, 42, 32], [43, 87, 42, 51], [43, 88, 42, 52, "style"], [43, 93, 43, 6], [43, 94, 43, 7], [44, 6, 44, 4], [45, 4, 45, 2], [45, 5, 45, 3], [46, 4, 47, 2], [46, 8, 47, 8, "customAnimationsStyleTag"], [46, 32, 47, 32], [46, 35, 47, 35, "document"], [46, 43, 47, 43], [46, 44, 47, 44, "createElement"], [46, 57, 47, 57], [46, 58, 47, 58], [46, 65, 47, 65], [46, 66, 47, 66], [47, 4, 48, 2, "customAnimationsStyleTag"], [47, 28, 48, 26], [47, 29, 48, 27, "id"], [47, 31, 48, 29], [47, 34, 48, 32, "CUSTOM_WEB_ANIMATIONS_ID"], [47, 58, 48, 56], [48, 4, 50, 2, "document"], [48, 12, 50, 10], [48, 13, 50, 11, "head"], [48, 17, 50, 15], [48, 18, 50, 16, "append<PERSON><PERSON><PERSON>"], [48, 29, 50, 27], [48, 30, 50, 28, "predefinedAnimationsStyleTag"], [48, 58, 50, 56], [48, 59, 50, 57], [49, 4, 51, 2, "document"], [49, 12, 51, 10], [49, 13, 51, 11, "head"], [49, 17, 51, 15], [49, 18, 51, 16, "append<PERSON><PERSON><PERSON>"], [49, 29, 51, 27], [49, 30, 51, 28, "customAnimationsStyleTag"], [49, 54, 51, 52], [49, 55, 51, 53], [50, 2, 52, 0], [51, 2, 54, 7], [51, 11, 54, 16, "insertWebAnimation"], [51, 29, 54, 34, "insertWebAnimation"], [51, 30, 54, 35, "animationName"], [51, 43, 54, 56], [51, 45, 54, 58, "keyframe"], [51, 53, 54, 74], [51, 55, 54, 76], [52, 4, 55, 2], [53, 4, 56, 2], [53, 8, 56, 6], [53, 9, 56, 7], [53, 13, 56, 7, "isWindowAvailable"], [53, 47, 56, 24], [53, 49, 56, 25], [53, 50, 56, 26], [53, 52, 56, 28], [54, 6, 57, 4], [55, 4, 58, 2], [56, 4, 60, 2], [56, 8, 60, 8, "styleTag"], [56, 16, 60, 16], [56, 19, 60, 19, "document"], [56, 27, 60, 27], [56, 28, 60, 28, "getElementById"], [56, 42, 60, 42], [56, 43, 61, 4, "CUSTOM_WEB_ANIMATIONS_ID"], [56, 67, 62, 2], [56, 68, 62, 23], [57, 4, 64, 2], [57, 8, 64, 6], [57, 9, 64, 7, "styleTag"], [57, 17, 64, 15], [57, 18, 64, 16, "sheet"], [57, 23, 64, 21], [57, 25, 64, 23], [58, 6, 65, 4, "logger"], [58, 20, 65, 10], [58, 21, 65, 11, "error"], [58, 26, 65, 16], [58, 27, 65, 17], [58, 75, 65, 65], [58, 76, 65, 66], [59, 6, 66, 4], [60, 4, 67, 2], [61, 4, 69, 2, "styleTag"], [61, 12, 69, 10], [61, 13, 69, 11, "sheet"], [61, 18, 69, 16], [61, 19, 69, 17, "insertRule"], [61, 29, 69, 27], [61, 30, 69, 28, "keyframe"], [61, 38, 69, 36], [61, 40, 69, 38], [61, 41, 69, 39], [61, 42, 69, 40], [62, 4, 70, 2, "animationNameList"], [62, 21, 70, 19], [62, 22, 70, 20, "unshift"], [62, 29, 70, 27], [62, 30, 70, 28, "animationName"], [62, 43, 70, 41], [62, 44, 70, 42], [63, 4, 71, 2, "animationNameToIndex"], [63, 24, 71, 22], [63, 25, 71, 23, "set"], [63, 28, 71, 26], [63, 29, 71, 27, "animationName"], [63, 42, 71, 40], [63, 44, 71, 42], [63, 45, 71, 43], [63, 46, 71, 44], [64, 4, 73, 2], [64, 9, 73, 7], [64, 13, 73, 11, "i"], [64, 14, 73, 12], [64, 17, 73, 15], [64, 18, 73, 16], [64, 20, 73, 18, "i"], [64, 21, 73, 19], [64, 24, 73, 22, "animationNameList"], [64, 41, 73, 39], [64, 42, 73, 40, "length"], [64, 48, 73, 46], [64, 50, 73, 48], [64, 52, 73, 50, "i"], [64, 53, 73, 51], [64, 55, 73, 53], [65, 6, 74, 4], [65, 10, 74, 10, "nextAnimationName"], [65, 27, 74, 27], [65, 30, 74, 30, "animationNameList"], [65, 47, 74, 47], [65, 48, 74, 48, "i"], [65, 49, 74, 49], [65, 50, 74, 50], [66, 6, 75, 4], [66, 10, 75, 10, "nextAnimationIndex"], [66, 28, 75, 28], [66, 31, 75, 31, "animationNameToIndex"], [66, 51, 75, 51], [66, 52, 75, 52, "get"], [66, 55, 75, 55], [66, 56, 75, 56, "nextAnimationName"], [66, 73, 75, 73], [66, 74, 75, 74], [67, 6, 77, 4], [67, 10, 77, 8, "nextAnimationIndex"], [67, 28, 77, 26], [67, 33, 77, 31, "undefined"], [67, 42, 77, 40], [67, 44, 77, 42], [68, 8, 78, 6], [68, 14, 78, 12], [68, 18, 78, 16, "ReanimatedError"], [68, 41, 78, 31], [68, 42, 78, 32], [68, 77, 78, 67], [68, 78, 78, 68], [69, 6, 79, 4], [70, 6, 81, 4, "animationNameToIndex"], [70, 26, 81, 24], [70, 27, 81, 25, "set"], [70, 30, 81, 28], [70, 31, 81, 29, "animationNameList"], [70, 48, 81, 46], [70, 49, 81, 47, "i"], [70, 50, 81, 48], [70, 51, 81, 49], [70, 53, 81, 51, "nextAnimationIndex"], [70, 71, 81, 69], [70, 74, 81, 72], [70, 75, 81, 73], [70, 76, 81, 74], [71, 4, 82, 2], [72, 2, 83, 0], [73, 2, 85, 0], [73, 11, 85, 9, "removeWebAnimation"], [73, 29, 85, 27, "removeWebAnimation"], [73, 30, 86, 2, "animationName"], [73, 43, 86, 23], [73, 45, 87, 2, "animationRemoveCallback"], [73, 68, 87, 37], [73, 70, 88, 2], [74, 4, 89, 2], [75, 4, 90, 2], [75, 8, 90, 6], [75, 9, 90, 7], [75, 13, 90, 7, "isWindowAvailable"], [75, 47, 90, 24], [75, 49, 90, 25], [75, 50, 90, 26], [75, 52, 90, 28], [76, 6, 91, 4], [77, 4, 92, 2], [78, 4, 94, 2], [78, 8, 94, 8, "styleTag"], [78, 16, 94, 16], [78, 19, 94, 19, "document"], [78, 27, 94, 27], [78, 28, 94, 28, "getElementById"], [78, 42, 94, 42], [78, 43, 95, 4, "CUSTOM_WEB_ANIMATIONS_ID"], [78, 67, 96, 2], [78, 68, 96, 23], [79, 4, 98, 2], [79, 8, 98, 8, "currentAnimationIndex"], [79, 29, 98, 29], [79, 32, 98, 32, "animationNameToIndex"], [79, 52, 98, 52], [79, 53, 98, 53, "get"], [79, 56, 98, 56], [79, 57, 98, 57, "animationName"], [79, 70, 98, 70], [79, 71, 98, 71], [80, 4, 100, 2], [80, 8, 100, 6, "currentAnimationIndex"], [80, 29, 100, 27], [80, 34, 100, 32, "undefined"], [80, 43, 100, 41], [80, 45, 100, 43], [81, 6, 101, 4], [81, 12, 101, 10], [81, 16, 101, 14, "ReanimatedError"], [81, 39, 101, 29], [81, 40, 101, 30], [81, 75, 101, 65], [81, 76, 101, 66], [82, 4, 102, 2], [83, 4, 104, 2, "animationRemoveCallback"], [83, 27, 104, 25], [83, 28, 104, 26], [83, 29, 104, 27], [84, 4, 106, 2, "styleTag"], [84, 12, 106, 10], [84, 13, 106, 11, "sheet"], [84, 18, 106, 16], [84, 20, 106, 18, "deleteRule"], [84, 30, 106, 28], [84, 31, 106, 29, "currentAnimationIndex"], [84, 52, 106, 50], [84, 53, 106, 51], [85, 4, 108, 2, "animationNameList"], [85, 21, 108, 19], [85, 22, 108, 20, "splice"], [85, 28, 108, 26], [85, 29, 108, 27, "currentAnimationIndex"], [85, 50, 108, 48], [85, 52, 108, 50], [85, 53, 108, 51], [85, 54, 108, 52], [86, 4, 109, 2, "animationNameToIndex"], [86, 24, 109, 22], [86, 25, 109, 23, "delete"], [86, 31, 109, 29], [86, 32, 109, 30, "animationName"], [86, 45, 109, 43], [86, 46, 109, 44], [87, 4, 111, 2], [87, 9, 111, 7], [87, 13, 111, 11, "i"], [87, 14, 111, 12], [87, 17, 111, 15, "currentAnimationIndex"], [87, 38, 111, 36], [87, 40, 111, 38, "i"], [87, 41, 111, 39], [87, 44, 111, 42, "animationNameList"], [87, 61, 111, 59], [87, 62, 111, 60, "length"], [87, 68, 111, 66], [87, 70, 111, 68], [87, 72, 111, 70, "i"], [87, 73, 111, 71], [87, 75, 111, 73], [88, 6, 112, 4], [88, 10, 112, 10, "nextAnimationName"], [88, 27, 112, 27], [88, 30, 112, 30, "animationNameList"], [88, 47, 112, 47], [88, 48, 112, 48, "i"], [88, 49, 112, 49], [88, 50, 112, 50], [89, 6, 113, 4], [89, 10, 113, 10, "nextAnimationIndex"], [89, 28, 113, 28], [89, 31, 113, 31, "animationNameToIndex"], [89, 51, 113, 51], [89, 52, 113, 52, "get"], [89, 55, 113, 55], [89, 56, 113, 56, "nextAnimationName"], [89, 73, 113, 73], [89, 74, 113, 74], [90, 6, 115, 4], [90, 10, 115, 8, "nextAnimationIndex"], [90, 28, 115, 26], [90, 33, 115, 31, "undefined"], [90, 42, 115, 40], [90, 44, 115, 42], [91, 8, 116, 6], [91, 14, 116, 12], [91, 18, 116, 16, "ReanimatedError"], [91, 41, 116, 31], [91, 42, 116, 32], [91, 77, 116, 67], [91, 78, 116, 68], [92, 6, 117, 4], [93, 6, 119, 4, "animationNameToIndex"], [93, 26, 119, 24], [93, 27, 119, 25, "set"], [93, 30, 119, 28], [93, 31, 119, 29, "animationNameList"], [93, 48, 119, 46], [93, 49, 119, 47, "i"], [93, 50, 119, 48], [93, 51, 119, 49], [93, 53, 119, 51, "nextAnimationIndex"], [93, 71, 119, 69], [93, 74, 119, 72], [93, 75, 119, 73], [93, 76, 119, 74], [94, 4, 120, 2], [95, 2, 121, 0], [96, 2, 123, 0], [96, 6, 123, 6, "timeoutScale"], [96, 18, 123, 18], [96, 21, 123, 21], [96, 25, 123, 25], [96, 26, 123, 26], [96, 27, 123, 27], [97, 2, 124, 0], [97, 6, 124, 6, "frameDurationMs"], [97, 21, 124, 21], [97, 24, 124, 24], [97, 26, 124, 26], [97, 27, 124, 27], [97, 28, 124, 28], [98, 2, 125, 0], [98, 6, 125, 6, "minimumFrames"], [98, 19, 125, 19], [98, 22, 125, 22], [98, 24, 125, 24], [99, 2, 127, 7], [99, 11, 127, 16, "scheduleAnimationCleanup"], [99, 35, 127, 40, "scheduleAnimationCleanup"], [99, 36, 128, 2, "animationName"], [99, 49, 128, 23], [99, 51, 129, 2, "animationDuration"], [99, 68, 129, 27], [99, 70, 130, 2, "animationRemoveCallback"], [99, 93, 130, 37], [99, 95, 131, 2], [100, 4, 132, 2], [101, 4, 133, 2], [102, 4, 134, 2], [102, 8, 134, 8, "timeoutValue"], [102, 20, 134, 20], [102, 23, 134, 23, "Math"], [102, 27, 134, 27], [102, 28, 134, 28, "max"], [102, 31, 134, 31], [102, 32, 135, 4, "animationDuration"], [102, 49, 135, 21], [102, 52, 135, 24, "timeoutScale"], [102, 64, 135, 36], [102, 67, 135, 39], [102, 71, 135, 43], [102, 73, 136, 4, "animationDuration"], [102, 90, 136, 21], [102, 93, 136, 24, "frameDurationMs"], [102, 108, 136, 39], [102, 111, 136, 42, "minimumFrames"], [102, 124, 137, 2], [102, 125, 137, 3], [103, 4, 139, 2, "setTimeout"], [103, 14, 139, 12], [103, 15, 140, 4], [103, 21, 140, 10, "removeWebAnimation"], [103, 39, 140, 28], [103, 40, 140, 29, "animationName"], [103, 53, 140, 42], [103, 55, 140, 44, "animationRemoveCallback"], [103, 78, 140, 67], [103, 79, 140, 68], [103, 81, 141, 4, "timeoutValue"], [103, 93, 142, 2], [103, 94, 142, 3], [104, 2, 143, 0], [105, 2, 145, 0], [105, 11, 145, 9, "reattachElementToAncestor"], [105, 36, 145, 34, "reattachElementToAncestor"], [105, 37, 145, 35, "child"], [105, 42, 145, 63], [105, 44, 145, 65, "parent"], [105, 50, 145, 77], [105, 52, 145, 79], [106, 4, 146, 2], [106, 8, 146, 8, "childSnapshot"], [106, 21, 146, 21], [106, 24, 146, 24, "snapshots"], [106, 49, 146, 33], [106, 50, 146, 34, "get"], [106, 53, 146, 37], [106, 54, 146, 38, "child"], [106, 59, 146, 43], [106, 60, 146, 44], [107, 4, 148, 2], [107, 8, 148, 6], [107, 9, 148, 7, "childSnapshot"], [107, 22, 148, 20], [107, 24, 148, 22], [108, 6, 149, 4, "logger"], [108, 20, 149, 10], [108, 21, 149, 11, "error"], [108, 26, 149, 16], [108, 27, 149, 17], [108, 55, 149, 45], [108, 56, 149, 46], [109, 6, 150, 4], [110, 4, 151, 2], [112, 4, 153, 2], [113, 4, 154, 2, "child"], [113, 9, 154, 7], [113, 10, 154, 8, "removedAfterAnimation"], [113, 31, 154, 29], [113, 34, 154, 32], [113, 38, 154, 36], [114, 4, 155, 2, "parent"], [114, 10, 155, 8], [114, 11, 155, 9, "append<PERSON><PERSON><PERSON>"], [114, 22, 155, 20], [114, 23, 155, 21, "child"], [114, 28, 155, 26], [114, 29, 155, 27], [115, 4, 157, 2], [115, 8, 157, 2, "setElementPosition"], [115, 42, 157, 20], [115, 44, 157, 21, "child"], [115, 49, 157, 26], [115, 51, 157, 28, "childSnapshot"], [115, 64, 157, 41], [115, 65, 157, 42], [116, 4, 159, 2], [116, 8, 159, 8, "originalOnAnimationEnd"], [116, 30, 159, 30], [116, 33, 159, 33, "child"], [116, 38, 159, 38], [116, 39, 159, 39, "onanimationend"], [116, 53, 159, 53], [117, 4, 161, 2, "child"], [117, 9, 161, 7], [117, 10, 161, 8, "onanimationend"], [117, 24, 161, 22], [117, 27, 161, 25], [117, 37, 161, 35, "event"], [117, 42, 161, 56], [117, 44, 161, 58], [118, 6, 162, 4, "parent"], [118, 12, 162, 10], [118, 13, 162, 11, "<PERSON><PERSON><PERSON><PERSON>"], [118, 24, 162, 22], [118, 25, 162, 23, "child"], [118, 30, 162, 28], [118, 31, 162, 29], [120, 6, 164, 4], [121, 6, 165, 4, "originalOnAnimationEnd"], [121, 28, 165, 26], [121, 30, 165, 28, "call"], [121, 34, 165, 32], [121, 35, 165, 33], [121, 39, 165, 37], [121, 41, 165, 39, "event"], [121, 46, 165, 44], [121, 47, 165, 45], [122, 4, 166, 2], [122, 5, 166, 3], [123, 2, 167, 0], [124, 2, 169, 0], [124, 11, 169, 9, "findDescendantWithExitingAnimation"], [124, 45, 169, 43, "findDescendantWithExitingAnimation"], [124, 46, 170, 2, "node"], [124, 50, 170, 29], [124, 52, 171, 2, "root"], [124, 56, 171, 12], [124, 58, 172, 2], [125, 4, 173, 2], [126, 4, 174, 2], [127, 4, 175, 2], [127, 8, 175, 6], [127, 10, 175, 8, "node"], [127, 14, 175, 12], [127, 26, 175, 24, "HTMLElement"], [127, 37, 175, 35], [127, 38, 175, 36], [127, 40, 175, 38], [128, 6, 176, 4], [129, 4, 177, 2], [130, 4, 179, 2], [130, 8, 179, 6, "node"], [130, 12, 179, 10], [130, 13, 179, 11, "reanimatedDummy"], [130, 28, 179, 26], [130, 32, 179, 30, "node"], [130, 36, 179, 34], [130, 37, 179, 35, "removedAfterAnimation"], [130, 58, 179, 56], [130, 63, 179, 61, "undefined"], [130, 72, 179, 70], [130, 74, 179, 72], [131, 6, 180, 4, "reattachElementToAncestor"], [131, 31, 180, 29], [131, 32, 180, 30, "node"], [131, 36, 180, 34], [131, 38, 180, 36, "root"], [131, 42, 180, 40], [131, 43, 180, 41], [132, 4, 181, 2], [133, 4, 183, 2], [133, 8, 183, 8, "children"], [133, 16, 183, 16], [133, 19, 183, 19, "Array"], [133, 24, 183, 24], [133, 25, 183, 25, "from"], [133, 29, 183, 29], [133, 30, 183, 30, "node"], [133, 34, 183, 34], [133, 35, 183, 35, "children"], [133, 43, 183, 43], [133, 44, 183, 44], [134, 4, 185, 2], [134, 9, 185, 7], [134, 13, 185, 11, "i"], [134, 14, 185, 12], [134, 17, 185, 15], [134, 18, 185, 16], [134, 20, 185, 18, "i"], [134, 21, 185, 19], [134, 24, 185, 22, "children"], [134, 32, 185, 30], [134, 33, 185, 31, "length"], [134, 39, 185, 37], [134, 41, 185, 39], [134, 43, 185, 41, "i"], [134, 44, 185, 42], [134, 46, 185, 44], [135, 6, 186, 4, "findDescendantWithExitingAnimation"], [135, 40, 186, 38], [135, 41, 187, 6, "children"], [135, 49, 187, 14], [135, 50, 187, 15, "i"], [135, 51, 187, 16], [135, 52, 187, 17], [135, 54, 188, 6, "root"], [135, 58, 189, 4], [135, 59, 189, 5], [136, 4, 190, 2], [137, 2, 191, 0], [138, 2, 209, 0], [138, 11, 209, 9, "checkIfScreenWasChanged"], [138, 34, 209, 32, "checkIfScreenWasChanged"], [138, 35, 210, 2, "<PERSON><PERSON><PERSON><PERSON>"], [138, 49, 210, 60], [138, 51, 211, 2], [139, 4, 212, 2], [139, 8, 212, 6, "reactFiberKey"], [139, 21, 212, 33], [139, 24, 212, 36], [139, 38, 212, 50], [140, 4, 214, 2], [140, 9, 214, 7], [140, 13, 214, 13, "key"], [140, 17, 214, 16], [140, 21, 214, 20, "Object"], [140, 27, 214, 26], [140, 28, 214, 27, "keys"], [140, 32, 214, 31], [140, 33, 214, 32, "<PERSON><PERSON><PERSON><PERSON>"], [140, 47, 214, 46], [140, 48, 214, 47], [140, 50, 214, 49], [141, 6, 215, 4], [141, 10, 215, 8, "key"], [141, 14, 215, 11], [141, 15, 215, 12, "startsWith"], [141, 25, 215, 22], [141, 26, 215, 23], [141, 40, 215, 37], [141, 41, 215, 38], [141, 43, 215, 40], [142, 8, 216, 6, "reactFiberKey"], [142, 21, 216, 19], [142, 24, 216, 22, "key"], [142, 28, 216, 41], [143, 8, 217, 6], [144, 6, 218, 4], [145, 4, 219, 2], [146, 4, 221, 2], [146, 11, 222, 4, "<PERSON><PERSON><PERSON><PERSON>"], [146, 25, 222, 18], [146, 26, 222, 19, "reactFiberKey"], [146, 39, 222, 32], [146, 40, 222, 33], [146, 42, 222, 35, "child"], [146, 47, 222, 40], [146, 49, 222, 42, "memoizedProps"], [146, 62, 222, 55], [146, 64, 222, 57, "navigation"], [146, 74, 222, 67], [146, 79, 223, 4, "undefined"], [146, 88, 223, 13], [147, 2, 225, 0], [148, 2, 227, 7], [148, 11, 227, 16, "addHTMLMutationObserver"], [148, 34, 227, 39, "addHTMLMutationObserver"], [148, 35, 227, 39], [148, 37, 227, 42], [149, 4, 228, 2], [149, 8, 228, 6, "isObserverSet"], [149, 21, 228, 19], [149, 25, 228, 23], [149, 26, 228, 24], [149, 30, 228, 24, "isWindowAvailable"], [149, 64, 228, 41], [149, 66, 228, 42], [149, 67, 228, 43], [149, 69, 228, 45], [150, 6, 229, 4], [151, 4, 230, 2], [152, 4, 232, 2, "isObserverSet"], [152, 17, 232, 15], [152, 20, 232, 18], [152, 24, 232, 22], [153, 4, 234, 2], [153, 8, 234, 8, "observer"], [153, 16, 234, 16], [153, 19, 234, 19], [153, 23, 234, 23, "MutationObserver"], [153, 39, 234, 39], [153, 40, 234, 41, "mutationsList"], [153, 53, 234, 54], [153, 57, 234, 59], [154, 6, 235, 4], [154, 10, 235, 10, "rootMutation"], [154, 22, 235, 22], [154, 25, 235, 25, "mutationsList"], [154, 38, 235, 38], [154, 39, 235, 39, "mutationsList"], [154, 52, 235, 52], [154, 53, 235, 53, "length"], [154, 59, 235, 59], [154, 62, 235, 62], [154, 63, 235, 63], [154, 64, 235, 64], [155, 6, 237, 4], [155, 10, 238, 6, "checkIfScreenWasChanged"], [155, 33, 238, 29], [155, 34, 239, 8, "rootMutation"], [155, 46, 239, 20], [155, 47, 239, 21, "target"], [155, 53, 240, 6], [155, 54, 240, 7], [155, 56, 241, 6], [156, 8, 242, 6], [157, 6, 243, 4], [158, 6, 245, 4], [158, 11, 245, 9], [158, 15, 245, 13, "i"], [158, 16, 245, 14], [158, 19, 245, 17], [158, 20, 245, 18], [158, 22, 245, 20, "i"], [158, 23, 245, 21], [158, 26, 245, 24, "rootMutation"], [158, 38, 245, 36], [158, 39, 245, 37, "removedNodes"], [158, 51, 245, 49], [158, 52, 245, 50, "length"], [158, 58, 245, 56], [158, 60, 245, 58], [158, 62, 245, 60, "i"], [158, 63, 245, 61], [158, 65, 245, 63], [159, 8, 246, 6, "findDescendantWithExitingAnimation"], [159, 42, 246, 40], [159, 43, 247, 8, "rootMutation"], [159, 55, 247, 20], [159, 56, 247, 21, "removedNodes"], [159, 68, 247, 33], [159, 69, 247, 34, "i"], [159, 70, 247, 35], [159, 71, 247, 36], [159, 73, 248, 8, "rootMutation"], [159, 85, 248, 20], [159, 86, 248, 21, "target"], [159, 92, 249, 6], [159, 93, 249, 7], [160, 6, 250, 4], [161, 4, 251, 2], [161, 5, 251, 3], [161, 6, 251, 4], [162, 4, 253, 2, "observer"], [162, 12, 253, 10], [162, 13, 253, 11, "observe"], [162, 20, 253, 18], [162, 21, 253, 19, "document"], [162, 29, 253, 27], [162, 30, 253, 28, "body"], [162, 34, 253, 32], [162, 36, 253, 34], [163, 6, 253, 36, "childList"], [163, 15, 253, 45], [163, 17, 253, 47], [163, 21, 253, 51], [164, 6, 253, 53, "subtree"], [164, 13, 253, 60], [164, 15, 253, 62], [165, 4, 253, 67], [165, 5, 253, 68], [165, 6, 253, 69], [166, 2, 254, 0], [167, 2, 256, 7], [167, 11, 256, 16, "areDOMRectsEqual"], [167, 27, 256, 32, "areDOMRectsEqual"], [167, 28, 256, 33, "r1"], [167, 30, 256, 44], [167, 32, 256, 46, "r2"], [167, 34, 256, 57], [167, 36, 256, 59], [168, 4, 257, 2], [169, 4, 258, 2], [169, 11, 259, 4, "r1"], [169, 13, 259, 6], [169, 14, 259, 7, "x"], [169, 15, 259, 8], [169, 20, 259, 13, "r2"], [169, 22, 259, 15], [169, 23, 259, 16, "x"], [169, 24, 259, 17], [169, 28, 260, 4, "r1"], [169, 30, 260, 6], [169, 31, 260, 7, "y"], [169, 32, 260, 8], [169, 37, 260, 13, "r2"], [169, 39, 260, 15], [169, 40, 260, 16, "y"], [169, 41, 260, 17], [169, 45, 261, 4, "r1"], [169, 47, 261, 6], [169, 48, 261, 7, "width"], [169, 53, 261, 12], [169, 58, 261, 17, "r2"], [169, 60, 261, 19], [169, 61, 261, 20, "width"], [169, 66, 261, 25], [169, 70, 262, 4, "r1"], [169, 72, 262, 6], [169, 73, 262, 7, "height"], [169, 79, 262, 13], [169, 84, 262, 18, "r2"], [169, 86, 262, 20], [169, 87, 262, 21, "height"], [169, 93, 262, 27], [170, 2, 264, 0], [171, 0, 264, 1], [171, 3]], "functionMap": {"names": ["<global>", "configureWebLayoutAnimations", "predefinedAnimationsStyleTag.onload", "insertWebAnimation", "removeWebAnimation", "scheduleAnimationCleanup", "setTimeout$argument_0", "reattachElementToAncestor", "child.onanimationend", "findDescendantWithExitingAnimation", "checkIfScreenWasChanged", "addHTMLMutationObserver", "MutationObserver$argument_0", "areDOMRectsEqual"], "mappings": "AAA;OCsB;wCCW;GDW;CDO;OGE;CH6B;AIE;CJoC;OKM;ICa,gED;CLG;AOE;yBCgB;GDK;CPC;ASE;CTsB;AUkB;CVgB;OWE;wCCO;GDiB;CXG;OaE;CbQ"}}, "type": "js/module"}]}