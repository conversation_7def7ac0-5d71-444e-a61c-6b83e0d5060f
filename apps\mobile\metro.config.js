const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Find the project and workspace directories
const projectRoot = __dirname;
const monorepoRoot = path.resolve(projectRoot, '../..');

const config = getDefaultConfig(projectRoot);

// ENTERPRISE OPTIMIZATION: Only watch specific directories to prevent timeout
// Instead of watching entire monorepo, only watch what's actually needed
const specificPackages = {
  '@tap2go/config': path.resolve(monorepoRoot, 'packages/config'),
  // Add other packages as needed
};

// 1. OPTIMIZED: Watch only project root and specific packages (not entire monorepo)
config.watchFolders = [
  projectRoot,
  ...Object.values(specificPackages)
];

// 2. Let Metro know where to resolve packages and in what order
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(monorepoRoot, 'node_modules'),
];

// 3. Add specific packages as extraNodeModules to avoid symlink issues
config.resolver.extraNodeModules = specificPackages;

// 3. Force Metro to resolve (sub)dependencies only from the `nodeModulesPaths`
config.resolver.disableHierarchicalLookup = true;

// 4. Basic configuration without NativeWind (fallback)
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// 5. ENTERPRISE PERFORMANCE OPTIMIZATIONS
// Increase timeout for large monorepos
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// 6. Windows-specific optimizations for file watching
if (process.platform === 'win32') {
  // Reduce file watching overhead on Windows - only watch mobile app
  config.watchFolders = [projectRoot];

  // Add timeout configuration for Windows
  config.server = {
    ...config.server,
    enhanceMiddleware: (middleware) => {
      return (req, res, next) => {
        // Set longer timeout for Windows file system
        req.setTimeout(60000); // 60 seconds
        res.setTimeout(60000);
        return middleware(req, res, next);
      };
    },
  };
}

// 7. Cache optimization for enterprise development
try {
  const FileStore = require('metro-cache/src/stores/FileStore');
  config.cacheStores = [
    new FileStore({
      root: path.join(projectRoot, '.metro-cache'),
    }),
  ];
} catch (error) {
  // Fallback if FileStore is not available or has issues
  console.log('⚠️ Using default Metro cache configuration');
}

console.log('📝 Using ENTERPRISE-OPTIMIZED Metro config (NativeWind disabled)');
console.log(`🔍 Watching folders: ${config.watchFolders.length} directories`);
console.log(`🖥️  Platform: ${process.platform}`);

module.exports = config;
