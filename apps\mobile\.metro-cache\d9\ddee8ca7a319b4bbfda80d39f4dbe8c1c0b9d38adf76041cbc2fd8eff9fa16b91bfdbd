{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 70, "index": 102}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./NativeSafeAreaProvider", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 103}, "end": {"line": 3, "column": 66, "index": 169}}], "key": "xo9+eb0JNXKlNmjofEL2NnBIgvg=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SafeAreaInsetsContext = exports.SafeAreaFrameContext = exports.SafeAreaContext = exports.SafeAreaConsumer = void 0;\n  exports.SafeAreaProvider = SafeAreaProvider;\n  exports.useSafeArea = useSafeArea;\n  exports.useSafeAreaFrame = useSafeAreaFrame;\n  exports.useSafeAreaInsets = useSafeAreaInsets;\n  exports.withSafeAreaInsets = withSafeAreaInsets;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var _NativeSafeAreaProvider = require(_dependencyMap[5], \"./NativeSafeAreaProvider\");\n  var _jsxDevRuntime = require(_dependencyMap[6], \"react/jsx-dev-runtime\");\n  var _excluded = [\"children\", \"initialMetrics\", \"initialSafeAreaInsets\", \"style\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-safe-area-context\\\\src\\\\SafeAreaContext.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var isDev = process.env.NODE_ENV !== 'production';\n  var SafeAreaInsetsContext = exports.SafeAreaInsetsContext = /*#__PURE__*/React.createContext(null);\n  if (isDev) {\n    SafeAreaInsetsContext.displayName = 'SafeAreaInsetsContext';\n  }\n  var SafeAreaFrameContext = exports.SafeAreaFrameContext = /*#__PURE__*/React.createContext(null);\n  if (isDev) {\n    SafeAreaFrameContext.displayName = 'SafeAreaFrameContext';\n  }\n  function SafeAreaProvider(_ref) {\n    var children = _ref.children,\n      initialMetrics = _ref.initialMetrics,\n      initialSafeAreaInsets = _ref.initialSafeAreaInsets,\n      style = _ref.style,\n      others = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var parentInsets = useParentSafeAreaInsets();\n    var parentFrame = useParentSafeAreaFrame();\n    var _React$useState = React.useState(initialMetrics?.insets ?? initialSafeAreaInsets ?? parentInsets ?? null),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      insets = _React$useState2[0],\n      setInsets = _React$useState2[1];\n    var _React$useState3 = React.useState(initialMetrics?.frame ?? parentFrame ?? {\n        // Backwards compat so we render anyway if we don't have frame.\n        x: 0,\n        y: 0,\n        width: _reactNative.Dimensions.get('window').width,\n        height: _reactNative.Dimensions.get('window').height\n      }),\n      _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),\n      frame = _React$useState4[0],\n      setFrame = _React$useState4[1];\n    var onInsetsChange = React.useCallback(event => {\n      var _event$nativeEvent = event.nativeEvent,\n        nextFrame = _event$nativeEvent.frame,\n        nextInsets = _event$nativeEvent.insets;\n      setFrame(curFrame => {\n        if (\n        // Backwards compat with old native code that won't send frame.\n        nextFrame && (nextFrame.height !== curFrame.height || nextFrame.width !== curFrame.width || nextFrame.x !== curFrame.x || nextFrame.y !== curFrame.y)) {\n          return nextFrame;\n        } else {\n          return curFrame;\n        }\n      });\n      setInsets(curInsets => {\n        if (!curInsets || nextInsets.bottom !== curInsets.bottom || nextInsets.left !== curInsets.left || nextInsets.right !== curInsets.right || nextInsets.top !== curInsets.top) {\n          return nextInsets;\n        } else {\n          return curInsets;\n        }\n      });\n    }, []);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_NativeSafeAreaProvider.NativeSafeAreaProvider, {\n      style: [styles.fill, style],\n      onInsetsChange: onInsetsChange,\n      ...others,\n      children: insets != null ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(SafeAreaFrameContext.Provider, {\n        value: frame,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(SafeAreaInsetsContext.Provider, {\n          value: insets,\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this) : null\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _reactNative.StyleSheet.create({\n    fill: {\n      flex: 1\n    }\n  });\n  function useParentSafeAreaInsets() {\n    return React.useContext(SafeAreaInsetsContext);\n  }\n  function useParentSafeAreaFrame() {\n    return React.useContext(SafeAreaFrameContext);\n  }\n  var NO_INSETS_ERROR = 'No safe area value available. Make sure you are rendering `<SafeAreaProvider>` at the top of your app.';\n  function useSafeAreaInsets() {\n    var insets = React.useContext(SafeAreaInsetsContext);\n    if (insets == null) {\n      throw new Error(NO_INSETS_ERROR);\n    }\n    return insets;\n  }\n  function useSafeAreaFrame() {\n    var frame = React.useContext(SafeAreaFrameContext);\n    if (frame == null) {\n      throw new Error(NO_INSETS_ERROR);\n    }\n    return frame;\n  }\n  function withSafeAreaInsets(WrappedComponent) {\n    return /*#__PURE__*/React.forwardRef((props, ref) => {\n      var insets = useSafeAreaInsets();\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(WrappedComponent, {\n        ...props,\n        insets: insets,\n        ref: ref\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 12\n      }, this);\n    });\n  }\n\n  /**\n   * @deprecated\n   */\n  function useSafeArea() {\n    return useSafeAreaInsets();\n  }\n\n  /**\n   * @deprecated\n   */\n  var SafeAreaConsumer = exports.SafeAreaConsumer = SafeAreaInsetsContext.Consumer;\n\n  /**\n   * @deprecated\n   */\n  var SafeAreaContext = exports.SafeAreaContext = SafeAreaInsetsContext;\n});", "lineCount": 155, "map": [[14, 2, 1, 0], [14, 6, 1, 0, "React"], [14, 11, 1, 0], [14, 14, 1, 0, "_interopRequireWildcard"], [14, 37, 1, 0], [14, 38, 1, 0, "require"], [14, 45, 1, 0], [14, 46, 1, 0, "_dependencyMap"], [14, 60, 1, 0], [15, 2, 2, 0], [15, 6, 2, 0, "_reactNative"], [15, 18, 2, 0], [15, 21, 2, 0, "require"], [15, 28, 2, 0], [15, 29, 2, 0, "_dependencyMap"], [15, 43, 2, 0], [16, 2, 3, 0], [16, 6, 3, 0, "_NativeSafeAreaProvider"], [16, 29, 3, 0], [16, 32, 3, 0, "require"], [16, 39, 3, 0], [16, 40, 3, 0, "_dependencyMap"], [16, 54, 3, 0], [17, 2, 3, 66], [17, 6, 3, 66, "_jsxDevRuntime"], [17, 20, 3, 66], [17, 23, 3, 66, "require"], [17, 30, 3, 66], [17, 31, 3, 66, "_dependencyMap"], [17, 45, 3, 66], [18, 2, 3, 66], [18, 6, 3, 66, "_excluded"], [18, 15, 3, 66], [19, 2, 3, 66], [19, 6, 3, 66, "_jsxFileName"], [19, 18, 3, 66], [20, 2, 3, 66], [20, 11, 3, 66, "_interopRequireWildcard"], [20, 35, 3, 66, "e"], [20, 36, 3, 66], [20, 38, 3, 66, "t"], [20, 39, 3, 66], [20, 68, 3, 66, "WeakMap"], [20, 75, 3, 66], [20, 81, 3, 66, "r"], [20, 82, 3, 66], [20, 89, 3, 66, "WeakMap"], [20, 96, 3, 66], [20, 100, 3, 66, "n"], [20, 101, 3, 66], [20, 108, 3, 66, "WeakMap"], [20, 115, 3, 66], [20, 127, 3, 66, "_interopRequireWildcard"], [20, 150, 3, 66], [20, 162, 3, 66, "_interopRequireWildcard"], [20, 163, 3, 66, "e"], [20, 164, 3, 66], [20, 166, 3, 66, "t"], [20, 167, 3, 66], [20, 176, 3, 66, "t"], [20, 177, 3, 66], [20, 181, 3, 66, "e"], [20, 182, 3, 66], [20, 186, 3, 66, "e"], [20, 187, 3, 66], [20, 188, 3, 66, "__esModule"], [20, 198, 3, 66], [20, 207, 3, 66, "e"], [20, 208, 3, 66], [20, 214, 3, 66, "o"], [20, 215, 3, 66], [20, 217, 3, 66, "i"], [20, 218, 3, 66], [20, 220, 3, 66, "f"], [20, 221, 3, 66], [20, 226, 3, 66, "__proto__"], [20, 235, 3, 66], [20, 243, 3, 66, "default"], [20, 250, 3, 66], [20, 252, 3, 66, "e"], [20, 253, 3, 66], [20, 270, 3, 66, "e"], [20, 271, 3, 66], [20, 294, 3, 66, "e"], [20, 295, 3, 66], [20, 320, 3, 66, "e"], [20, 321, 3, 66], [20, 330, 3, 66, "f"], [20, 331, 3, 66], [20, 337, 3, 66, "o"], [20, 338, 3, 66], [20, 341, 3, 66, "t"], [20, 342, 3, 66], [20, 345, 3, 66, "n"], [20, 346, 3, 66], [20, 349, 3, 66, "r"], [20, 350, 3, 66], [20, 358, 3, 66, "o"], [20, 359, 3, 66], [20, 360, 3, 66, "has"], [20, 363, 3, 66], [20, 364, 3, 66, "e"], [20, 365, 3, 66], [20, 375, 3, 66, "o"], [20, 376, 3, 66], [20, 377, 3, 66, "get"], [20, 380, 3, 66], [20, 381, 3, 66, "e"], [20, 382, 3, 66], [20, 385, 3, 66, "o"], [20, 386, 3, 66], [20, 387, 3, 66, "set"], [20, 390, 3, 66], [20, 391, 3, 66, "e"], [20, 392, 3, 66], [20, 394, 3, 66, "f"], [20, 395, 3, 66], [20, 409, 3, 66, "_t"], [20, 411, 3, 66], [20, 415, 3, 66, "e"], [20, 416, 3, 66], [20, 432, 3, 66, "_t"], [20, 434, 3, 66], [20, 441, 3, 66, "hasOwnProperty"], [20, 455, 3, 66], [20, 456, 3, 66, "call"], [20, 460, 3, 66], [20, 461, 3, 66, "e"], [20, 462, 3, 66], [20, 464, 3, 66, "_t"], [20, 466, 3, 66], [20, 473, 3, 66, "i"], [20, 474, 3, 66], [20, 478, 3, 66, "o"], [20, 479, 3, 66], [20, 482, 3, 66, "Object"], [20, 488, 3, 66], [20, 489, 3, 66, "defineProperty"], [20, 503, 3, 66], [20, 508, 3, 66, "Object"], [20, 514, 3, 66], [20, 515, 3, 66, "getOwnPropertyDescriptor"], [20, 539, 3, 66], [20, 540, 3, 66, "e"], [20, 541, 3, 66], [20, 543, 3, 66, "_t"], [20, 545, 3, 66], [20, 552, 3, 66, "i"], [20, 553, 3, 66], [20, 554, 3, 66, "get"], [20, 557, 3, 66], [20, 561, 3, 66, "i"], [20, 562, 3, 66], [20, 563, 3, 66, "set"], [20, 566, 3, 66], [20, 570, 3, 66, "o"], [20, 571, 3, 66], [20, 572, 3, 66, "f"], [20, 573, 3, 66], [20, 575, 3, 66, "_t"], [20, 577, 3, 66], [20, 579, 3, 66, "i"], [20, 580, 3, 66], [20, 584, 3, 66, "f"], [20, 585, 3, 66], [20, 586, 3, 66, "_t"], [20, 588, 3, 66], [20, 592, 3, 66, "e"], [20, 593, 3, 66], [20, 594, 3, 66, "_t"], [20, 596, 3, 66], [20, 607, 3, 66, "f"], [20, 608, 3, 66], [20, 613, 3, 66, "e"], [20, 614, 3, 66], [20, 616, 3, 66, "t"], [20, 617, 3, 66], [21, 2, 11, 0], [21, 6, 11, 6, "isDev"], [21, 11, 11, 11], [21, 14, 11, 14, "process"], [21, 21, 11, 21], [21, 22, 11, 22, "env"], [21, 25, 11, 25], [21, 26, 11, 26, "NODE_ENV"], [21, 34, 11, 34], [21, 39, 11, 39], [21, 51, 11, 51], [22, 2, 13, 7], [22, 6, 13, 13, "SafeAreaInsetsContext"], [22, 27, 13, 34], [22, 30, 13, 34, "exports"], [22, 37, 13, 34], [22, 38, 13, 34, "SafeAreaInsetsContext"], [22, 59, 13, 34], [22, 75, 13, 37, "React"], [22, 80, 13, 42], [22, 81, 13, 43, "createContext"], [22, 94, 13, 56], [22, 95, 14, 2], [22, 99, 15, 0], [22, 100, 15, 1], [23, 2, 16, 0], [23, 6, 16, 4, "isDev"], [23, 11, 16, 9], [23, 13, 16, 11], [24, 4, 17, 2, "SafeAreaInsetsContext"], [24, 25, 17, 23], [24, 26, 17, 24, "displayName"], [24, 37, 17, 35], [24, 40, 17, 38], [24, 63, 17, 61], [25, 2, 18, 0], [26, 2, 20, 7], [26, 6, 20, 13, "SafeAreaFrameContext"], [26, 26, 20, 33], [26, 29, 20, 33, "exports"], [26, 36, 20, 33], [26, 37, 20, 33, "SafeAreaFrameContext"], [26, 57, 20, 33], [26, 73, 20, 36, "React"], [26, 78, 20, 41], [26, 79, 20, 42, "createContext"], [26, 92, 20, 55], [26, 93, 20, 69], [26, 97, 20, 73], [26, 98, 20, 74], [27, 2, 21, 0], [27, 6, 21, 4, "isDev"], [27, 11, 21, 9], [27, 13, 21, 11], [28, 4, 22, 2, "SafeAreaFrameContext"], [28, 24, 22, 22], [28, 25, 22, 23, "displayName"], [28, 36, 22, 34], [28, 39, 22, 37], [28, 61, 22, 59], [29, 2, 23, 0], [30, 2, 34, 7], [30, 11, 34, 16, "SafeAreaProvider"], [30, 27, 34, 32, "SafeAreaProvider"], [30, 28, 34, 32, "_ref"], [30, 32, 34, 32], [30, 34, 40, 26], [31, 4, 40, 26], [31, 8, 35, 2, "children"], [31, 16, 35, 10], [31, 19, 35, 10, "_ref"], [31, 23, 35, 10], [31, 24, 35, 2, "children"], [31, 32, 35, 10], [32, 6, 36, 2, "initialMetrics"], [32, 20, 36, 16], [32, 23, 36, 16, "_ref"], [32, 27, 36, 16], [32, 28, 36, 2, "initialMetrics"], [32, 42, 36, 16], [33, 6, 37, 2, "initialSafeAreaInsets"], [33, 27, 37, 23], [33, 30, 37, 23, "_ref"], [33, 34, 37, 23], [33, 35, 37, 2, "initialSafeAreaInsets"], [33, 56, 37, 23], [34, 6, 38, 2, "style"], [34, 11, 38, 7], [34, 14, 38, 7, "_ref"], [34, 18, 38, 7], [34, 19, 38, 2, "style"], [34, 24, 38, 7], [35, 6, 39, 5, "others"], [35, 12, 39, 11], [35, 19, 39, 11, "_objectWithoutProperties2"], [35, 44, 39, 11], [35, 45, 39, 11, "default"], [35, 52, 39, 11], [35, 54, 39, 11, "_ref"], [35, 58, 39, 11], [35, 60, 39, 11, "_excluded"], [35, 69, 39, 11], [36, 4, 41, 2], [36, 8, 41, 8, "parentInsets"], [36, 20, 41, 20], [36, 23, 41, 23, "useParentSafeAreaInsets"], [36, 46, 41, 46], [36, 47, 41, 47], [36, 48, 41, 48], [37, 4, 42, 2], [37, 8, 42, 8, "parentFrame"], [37, 19, 42, 19], [37, 22, 42, 22, "useParentSafeAreaFrame"], [37, 44, 42, 44], [37, 45, 42, 45], [37, 46, 42, 46], [38, 4, 43, 2], [38, 8, 43, 2, "_React$useState"], [38, 23, 43, 2], [38, 26, 43, 30, "React"], [38, 31, 43, 35], [38, 32, 43, 36, "useState"], [38, 40, 43, 44], [38, 41, 44, 4, "initialMetrics"], [38, 55, 44, 18], [38, 57, 44, 20, "insets"], [38, 63, 44, 26], [38, 67, 44, 30, "initialSafeAreaInsets"], [38, 88, 44, 51], [38, 92, 44, 55, "parentInsets"], [38, 104, 44, 67], [38, 108, 44, 71], [38, 112, 45, 2], [38, 113, 45, 3], [39, 6, 45, 3, "_React$useState2"], [39, 22, 45, 3], [39, 29, 45, 3, "_slicedToArray2"], [39, 44, 45, 3], [39, 45, 45, 3, "default"], [39, 52, 45, 3], [39, 54, 45, 3, "_React$useState"], [39, 69, 45, 3], [40, 6, 43, 9, "insets"], [40, 12, 43, 15], [40, 15, 43, 15, "_React$useState2"], [40, 31, 43, 15], [41, 6, 43, 17, "setInsets"], [41, 15, 43, 26], [41, 18, 43, 26, "_React$useState2"], [41, 34, 43, 26], [42, 4, 46, 2], [42, 8, 46, 2, "_React$useState3"], [42, 24, 46, 2], [42, 27, 46, 28, "React"], [42, 32, 46, 33], [42, 33, 46, 34, "useState"], [42, 41, 46, 42], [42, 42, 47, 4, "initialMetrics"], [42, 56, 47, 18], [42, 58, 47, 20, "frame"], [42, 63, 47, 25], [42, 67, 48, 6, "parentFrame"], [42, 78, 48, 17], [42, 82, 48, 21], [43, 8, 49, 8], [44, 8, 50, 8, "x"], [44, 9, 50, 9], [44, 11, 50, 11], [44, 12, 50, 12], [45, 8, 51, 8, "y"], [45, 9, 51, 9], [45, 11, 51, 11], [45, 12, 51, 12], [46, 8, 52, 8, "width"], [46, 13, 52, 13], [46, 15, 52, 15, "Dimensions"], [46, 38, 52, 25], [46, 39, 52, 26, "get"], [46, 42, 52, 29], [46, 43, 52, 30], [46, 51, 52, 38], [46, 52, 52, 39], [46, 53, 52, 40, "width"], [46, 58, 52, 45], [47, 8, 53, 8, "height"], [47, 14, 53, 14], [47, 16, 53, 16, "Dimensions"], [47, 39, 53, 26], [47, 40, 53, 27, "get"], [47, 43, 53, 30], [47, 44, 53, 31], [47, 52, 53, 39], [47, 53, 53, 40], [47, 54, 53, 41, "height"], [48, 6, 54, 6], [48, 7, 55, 2], [48, 8, 55, 3], [49, 6, 55, 3, "_React$useState4"], [49, 22, 55, 3], [49, 29, 55, 3, "_slicedToArray2"], [49, 44, 55, 3], [49, 45, 55, 3, "default"], [49, 52, 55, 3], [49, 54, 55, 3, "_React$useState3"], [49, 70, 55, 3], [50, 6, 46, 9, "frame"], [50, 11, 46, 14], [50, 14, 46, 14, "_React$useState4"], [50, 30, 46, 14], [51, 6, 46, 16, "set<PERSON>rame"], [51, 14, 46, 24], [51, 17, 46, 24, "_React$useState4"], [51, 33, 46, 24], [52, 4, 56, 2], [52, 8, 56, 8, "onInsetsChange"], [52, 22, 56, 22], [52, 25, 56, 25, "React"], [52, 30, 56, 30], [52, 31, 56, 31, "useCallback"], [52, 42, 56, 42], [52, 43, 56, 44, "event"], [52, 48, 56, 68], [52, 52, 56, 73], [53, 6, 57, 4], [53, 10, 57, 4, "_event$nativeEvent"], [53, 28, 57, 4], [53, 31, 59, 8, "event"], [53, 36, 59, 13], [53, 37, 58, 6, "nativeEvent"], [53, 48, 58, 17], [54, 8, 58, 28, "next<PERSON><PERSON><PERSON>"], [54, 17, 58, 37], [54, 20, 58, 37, "_event$nativeEvent"], [54, 38, 58, 37], [54, 39, 58, 21, "frame"], [54, 44, 58, 26], [55, 8, 58, 47, "nextInsets"], [55, 18, 58, 57], [55, 21, 58, 57, "_event$nativeEvent"], [55, 39, 58, 57], [55, 40, 58, 39, "insets"], [55, 46, 58, 45], [56, 6, 61, 4, "set<PERSON>rame"], [56, 14, 61, 12], [56, 15, 61, 14, "curFrame"], [56, 23, 61, 22], [56, 27, 61, 27], [57, 8, 62, 6], [58, 8, 63, 8], [59, 8, 64, 8, "next<PERSON><PERSON><PERSON>"], [59, 17, 64, 17], [59, 22, 65, 9, "next<PERSON><PERSON><PERSON>"], [59, 31, 65, 18], [59, 32, 65, 19, "height"], [59, 38, 65, 25], [59, 43, 65, 30, "curFrame"], [59, 51, 65, 38], [59, 52, 65, 39, "height"], [59, 58, 65, 45], [59, 62, 66, 10, "next<PERSON><PERSON><PERSON>"], [59, 71, 66, 19], [59, 72, 66, 20, "width"], [59, 77, 66, 25], [59, 82, 66, 30, "curFrame"], [59, 90, 66, 38], [59, 91, 66, 39, "width"], [59, 96, 66, 44], [59, 100, 67, 10, "next<PERSON><PERSON><PERSON>"], [59, 109, 67, 19], [59, 110, 67, 20, "x"], [59, 111, 67, 21], [59, 116, 67, 26, "curFrame"], [59, 124, 67, 34], [59, 125, 67, 35, "x"], [59, 126, 67, 36], [59, 130, 68, 10, "next<PERSON><PERSON><PERSON>"], [59, 139, 68, 19], [59, 140, 68, 20, "y"], [59, 141, 68, 21], [59, 146, 68, 26, "curFrame"], [59, 154, 68, 34], [59, 155, 68, 35, "y"], [59, 156, 68, 36], [59, 157, 68, 37], [59, 159, 69, 8], [60, 10, 70, 8], [60, 17, 70, 15, "next<PERSON><PERSON><PERSON>"], [60, 26, 70, 24], [61, 8, 71, 6], [61, 9, 71, 7], [61, 15, 71, 13], [62, 10, 72, 8], [62, 17, 72, 15, "curFrame"], [62, 25, 72, 23], [63, 8, 73, 6], [64, 6, 74, 4], [64, 7, 74, 5], [64, 8, 74, 6], [65, 6, 76, 4, "setInsets"], [65, 15, 76, 13], [65, 16, 76, 15, "curInsets"], [65, 25, 76, 24], [65, 29, 76, 29], [66, 8, 77, 6], [66, 12, 78, 8], [66, 13, 78, 9, "curInsets"], [66, 22, 78, 18], [66, 26, 79, 8, "nextInsets"], [66, 36, 79, 18], [66, 37, 79, 19, "bottom"], [66, 43, 79, 25], [66, 48, 79, 30, "curInsets"], [66, 57, 79, 39], [66, 58, 79, 40, "bottom"], [66, 64, 79, 46], [66, 68, 80, 8, "nextInsets"], [66, 78, 80, 18], [66, 79, 80, 19, "left"], [66, 83, 80, 23], [66, 88, 80, 28, "curInsets"], [66, 97, 80, 37], [66, 98, 80, 38, "left"], [66, 102, 80, 42], [66, 106, 81, 8, "nextInsets"], [66, 116, 81, 18], [66, 117, 81, 19, "right"], [66, 122, 81, 24], [66, 127, 81, 29, "curInsets"], [66, 136, 81, 38], [66, 137, 81, 39, "right"], [66, 142, 81, 44], [66, 146, 82, 8, "nextInsets"], [66, 156, 82, 18], [66, 157, 82, 19, "top"], [66, 160, 82, 22], [66, 165, 82, 27, "curInsets"], [66, 174, 82, 36], [66, 175, 82, 37, "top"], [66, 178, 82, 40], [66, 180, 83, 8], [67, 10, 84, 8], [67, 17, 84, 15, "nextInsets"], [67, 27, 84, 25], [68, 8, 85, 6], [68, 9, 85, 7], [68, 15, 85, 13], [69, 10, 86, 8], [69, 17, 86, 15, "curInsets"], [69, 26, 86, 24], [70, 8, 87, 6], [71, 6, 88, 4], [71, 7, 88, 5], [71, 8, 88, 6], [72, 4, 89, 2], [72, 5, 89, 3], [72, 7, 89, 5], [72, 9, 89, 7], [72, 10, 89, 8], [73, 4, 91, 2], [73, 24, 92, 4], [73, 28, 92, 4, "_jsxDevRuntime"], [73, 42, 92, 4], [73, 43, 92, 4, "jsxDEV"], [73, 49, 92, 4], [73, 51, 92, 5, "_NativeSafeAreaProvider"], [73, 74, 92, 5], [73, 75, 92, 5, "NativeSafeAreaProvider"], [73, 97, 92, 27], [74, 6, 93, 6, "style"], [74, 11, 93, 11], [74, 13, 93, 13], [74, 14, 93, 14, "styles"], [74, 20, 93, 20], [74, 21, 93, 21, "fill"], [74, 25, 93, 25], [74, 27, 93, 27, "style"], [74, 32, 93, 32], [74, 33, 93, 34], [75, 6, 94, 6, "onInsetsChange"], [75, 20, 94, 20], [75, 22, 94, 22, "onInsetsChange"], [75, 36, 94, 37], [76, 6, 94, 37], [76, 9, 95, 10, "others"], [76, 15, 95, 16], [77, 6, 95, 16, "children"], [77, 14, 95, 16], [77, 16, 97, 7, "insets"], [77, 22, 97, 13], [77, 26, 97, 17], [77, 30, 97, 21], [77, 46, 98, 8], [77, 50, 98, 8, "_jsxDevRuntime"], [77, 64, 98, 8], [77, 65, 98, 8, "jsxDEV"], [77, 71, 98, 8], [77, 73, 98, 9, "SafeAreaFrameContext"], [77, 93, 98, 29], [77, 94, 98, 30, "Provider"], [77, 102, 98, 38], [78, 8, 98, 39, "value"], [78, 13, 98, 44], [78, 15, 98, 46, "frame"], [78, 20, 98, 52], [79, 8, 98, 52, "children"], [79, 16, 98, 52], [79, 31, 99, 10], [79, 35, 99, 10, "_jsxDevRuntime"], [79, 49, 99, 10], [79, 50, 99, 10, "jsxDEV"], [79, 56, 99, 10], [79, 58, 99, 11, "SafeAreaInsetsContext"], [79, 79, 99, 32], [79, 80, 99, 33, "Provider"], [79, 88, 99, 41], [80, 10, 99, 42, "value"], [80, 15, 99, 47], [80, 17, 99, 49, "insets"], [80, 23, 99, 56], [81, 10, 99, 56, "children"], [81, 18, 99, 56], [81, 20, 100, 13, "children"], [82, 8, 100, 21], [83, 10, 100, 21, "fileName"], [83, 18, 100, 21], [83, 20, 100, 21, "_jsxFileName"], [83, 32, 100, 21], [84, 10, 100, 21, "lineNumber"], [84, 20, 100, 21], [85, 10, 100, 21, "columnNumber"], [85, 22, 100, 21], [86, 8, 100, 21], [86, 15, 101, 42], [87, 6, 101, 43], [88, 8, 101, 43, "fileName"], [88, 16, 101, 43], [88, 18, 101, 43, "_jsxFileName"], [88, 30, 101, 43], [89, 8, 101, 43, "lineNumber"], [89, 18, 101, 43], [90, 8, 101, 43, "columnNumber"], [90, 20, 101, 43], [91, 6, 101, 43], [91, 13, 102, 39], [91, 14, 102, 40], [91, 17, 103, 10], [92, 4, 103, 14], [93, 6, 103, 14, "fileName"], [93, 14, 103, 14], [93, 16, 103, 14, "_jsxFileName"], [93, 28, 103, 14], [94, 6, 103, 14, "lineNumber"], [94, 16, 103, 14], [95, 6, 103, 14, "columnNumber"], [95, 18, 103, 14], [96, 4, 103, 14], [96, 11, 104, 28], [96, 12, 104, 29], [97, 2, 106, 0], [98, 2, 108, 0], [98, 6, 108, 6, "styles"], [98, 12, 108, 12], [98, 15, 108, 15, "StyleSheet"], [98, 38, 108, 25], [98, 39, 108, 26, "create"], [98, 45, 108, 32], [98, 46, 108, 33], [99, 4, 109, 2, "fill"], [99, 8, 109, 6], [99, 10, 109, 8], [100, 6, 109, 10, "flex"], [100, 10, 109, 14], [100, 12, 109, 16], [101, 4, 109, 18], [102, 2, 110, 0], [102, 3, 110, 1], [102, 4, 110, 2], [103, 2, 112, 0], [103, 11, 112, 9, "useParentSafeAreaInsets"], [103, 34, 112, 32, "useParentSafeAreaInsets"], [103, 35, 112, 32], [103, 37, 112, 54], [104, 4, 113, 2], [104, 11, 113, 9, "React"], [104, 16, 113, 14], [104, 17, 113, 15, "useContext"], [104, 27, 113, 25], [104, 28, 113, 26, "SafeAreaInsetsContext"], [104, 49, 113, 47], [104, 50, 113, 48], [105, 2, 114, 0], [106, 2, 116, 0], [106, 11, 116, 9, "useParentSafeAreaFrame"], [106, 33, 116, 31, "useParentSafeAreaFrame"], [106, 34, 116, 31], [106, 36, 116, 47], [107, 4, 117, 2], [107, 11, 117, 9, "React"], [107, 16, 117, 14], [107, 17, 117, 15, "useContext"], [107, 27, 117, 25], [107, 28, 117, 26, "SafeAreaFrameContext"], [107, 48, 117, 46], [107, 49, 117, 47], [108, 2, 118, 0], [109, 2, 120, 0], [109, 6, 120, 6, "NO_INSETS_ERROR"], [109, 21, 120, 21], [109, 24, 121, 2], [109, 128, 121, 106], [110, 2, 123, 7], [110, 11, 123, 16, "useSafeAreaInsets"], [110, 28, 123, 33, "useSafeAreaInsets"], [110, 29, 123, 33], [110, 31, 123, 48], [111, 4, 124, 2], [111, 8, 124, 8, "insets"], [111, 14, 124, 14], [111, 17, 124, 17, "React"], [111, 22, 124, 22], [111, 23, 124, 23, "useContext"], [111, 33, 124, 33], [111, 34, 124, 34, "SafeAreaInsetsContext"], [111, 55, 124, 55], [111, 56, 124, 56], [112, 4, 125, 2], [112, 8, 125, 6, "insets"], [112, 14, 125, 12], [112, 18, 125, 16], [112, 22, 125, 20], [112, 24, 125, 22], [113, 6, 126, 4], [113, 12, 126, 10], [113, 16, 126, 14, "Error"], [113, 21, 126, 19], [113, 22, 126, 20, "NO_INSETS_ERROR"], [113, 37, 126, 35], [113, 38, 126, 36], [114, 4, 127, 2], [115, 4, 128, 2], [115, 11, 128, 9, "insets"], [115, 17, 128, 15], [116, 2, 129, 0], [117, 2, 131, 7], [117, 11, 131, 16, "useSafeAreaFrame"], [117, 27, 131, 32, "useSafeAreaFrame"], [117, 28, 131, 32], [117, 30, 131, 41], [118, 4, 132, 2], [118, 8, 132, 8, "frame"], [118, 13, 132, 13], [118, 16, 132, 16, "React"], [118, 21, 132, 21], [118, 22, 132, 22, "useContext"], [118, 32, 132, 32], [118, 33, 132, 33, "SafeAreaFrameContext"], [118, 53, 132, 53], [118, 54, 132, 54], [119, 4, 133, 2], [119, 8, 133, 6, "frame"], [119, 13, 133, 11], [119, 17, 133, 15], [119, 21, 133, 19], [119, 23, 133, 21], [120, 6, 134, 4], [120, 12, 134, 10], [120, 16, 134, 14, "Error"], [120, 21, 134, 19], [120, 22, 134, 20, "NO_INSETS_ERROR"], [120, 37, 134, 35], [120, 38, 134, 36], [121, 4, 135, 2], [122, 4, 136, 2], [122, 11, 136, 9, "frame"], [122, 16, 136, 14], [123, 2, 137, 0], [124, 2, 143, 7], [124, 11, 143, 16, "withSafeAreaInsets"], [124, 29, 143, 34, "withSafeAreaInsets"], [124, 30, 144, 2, "WrappedComponent"], [124, 46, 146, 3], [124, 48, 149, 2], [125, 4, 150, 2], [125, 24, 150, 9, "React"], [125, 29, 150, 14], [125, 30, 150, 15, "forwardRef"], [125, 40, 150, 25], [125, 41, 150, 38], [125, 42, 150, 39, "props"], [125, 47, 150, 44], [125, 49, 150, 46, "ref"], [125, 52, 150, 49], [125, 57, 150, 54], [126, 6, 151, 4], [126, 10, 151, 10, "insets"], [126, 16, 151, 16], [126, 19, 151, 19, "useSafeAreaInsets"], [126, 36, 151, 36], [126, 37, 151, 37], [126, 38, 151, 38], [127, 6, 152, 4], [127, 26, 152, 11], [127, 30, 152, 11, "_jsxDevRuntime"], [127, 44, 152, 11], [127, 45, 152, 11, "jsxDEV"], [127, 51, 152, 11], [127, 53, 152, 12, "WrappedComponent"], [127, 69, 152, 28], [128, 8, 152, 28], [128, 11, 152, 33, "props"], [128, 16, 152, 38], [129, 8, 152, 40, "insets"], [129, 14, 152, 46], [129, 16, 152, 48, "insets"], [129, 22, 152, 55], [130, 8, 152, 56, "ref"], [130, 11, 152, 59], [130, 13, 152, 61, "ref"], [131, 6, 152, 65], [132, 8, 152, 65, "fileName"], [132, 16, 152, 65], [132, 18, 152, 65, "_jsxFileName"], [132, 30, 152, 65], [133, 8, 152, 65, "lineNumber"], [133, 18, 152, 65], [134, 8, 152, 65, "columnNumber"], [134, 20, 152, 65], [135, 6, 152, 65], [135, 13, 152, 67], [135, 14, 152, 68], [136, 4, 153, 2], [136, 5, 153, 3], [136, 6, 153, 4], [137, 2, 154, 0], [139, 2, 156, 0], [140, 0, 157, 0], [141, 0, 158, 0], [142, 2, 159, 7], [142, 11, 159, 16, "useSafeArea"], [142, 22, 159, 27, "useSafeArea"], [142, 23, 159, 27], [142, 25, 159, 42], [143, 4, 160, 2], [143, 11, 160, 9, "useSafeAreaInsets"], [143, 28, 160, 26], [143, 29, 160, 27], [143, 30, 160, 28], [144, 2, 161, 0], [146, 2, 163, 0], [147, 0, 164, 0], [148, 0, 165, 0], [149, 2, 166, 7], [149, 6, 166, 13, "SafeAreaConsumer"], [149, 22, 166, 29], [149, 25, 166, 29, "exports"], [149, 32, 166, 29], [149, 33, 166, 29, "SafeAreaConsumer"], [149, 49, 166, 29], [149, 52, 166, 32, "SafeAreaInsetsContext"], [149, 73, 166, 53], [149, 74, 166, 54, "Consumer"], [149, 82, 166, 62], [151, 2, 168, 0], [152, 0, 169, 0], [153, 0, 170, 0], [154, 2, 171, 7], [154, 6, 171, 13, "SafeAreaContext"], [154, 21, 171, 28], [154, 24, 171, 28, "exports"], [154, 31, 171, 28], [154, 32, 171, 28, "SafeAreaContext"], [154, 47, 171, 28], [154, 50, 171, 31, "SafeAreaInsetsContext"], [154, 71, 171, 52], [155, 0, 171, 53], [155, 3]], "functionMap": {"names": ["<global>", "SafeAreaProvider", "onInsetsChange", "setFrame$argument_0", "setInsets$argument_0", "useParentSafeAreaInsets", "useParentSafeAreaFrame", "useSafeAreaInsets", "useSafeAreaFrame", "withSafeAreaInsets", "React.forwardRef$argument_0", "useSafeArea"], "mappings": "AAA;OCiC;2CCsB;aCK;KDa;cEE;KFY;GDC;CDiB;AKM;CLE;AME;CNE;OOK;CPM;OQE;CRM;OSM;sCCO;GDG;CTC;OWK;CXE"}}, "type": "js/module"}]}