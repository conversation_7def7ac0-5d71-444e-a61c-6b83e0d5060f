{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 48, "index": 95}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useComponent = useComponent;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[1], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var NavigationContent = _ref => {\n    var render = _ref.render,\n      children = _ref.children;\n    return render(children);\n  };\n  function useComponent(render) {\n    var renderRef = React.useRef(render);\n\n    // Normally refs shouldn't be mutated in render\n    // But we return a component which will be rendered\n    // So it's just for immediate consumption\n    renderRef.current = render;\n    React.useEffect(() => {\n      renderRef.current = null;\n    });\n    return React.useRef(_ref2 => {\n      var children = _ref2.children;\n      var render = renderRef.current;\n      if (render === null) {\n        throw new Error('The returned component must be rendered in the same render phase as the hook.');\n      }\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(NavigationContent, {\n        render: render,\n        children: children\n      });\n    }).current;\n  }\n});", "lineCount": 38, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useComponent"], [7, 22, 1, 13], [7, 25, 1, 13, "useComponent"], [7, 37, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_jsxRuntime"], [9, 17, 4, 0], [9, 20, 4, 0, "require"], [9, 27, 4, 0], [9, 28, 4, 0, "_dependencyMap"], [9, 42, 4, 0], [10, 2, 4, 48], [10, 11, 4, 48, "_interopRequireWildcard"], [10, 35, 4, 48, "e"], [10, 36, 4, 48], [10, 38, 4, 48, "t"], [10, 39, 4, 48], [10, 68, 4, 48, "WeakMap"], [10, 75, 4, 48], [10, 81, 4, 48, "r"], [10, 82, 4, 48], [10, 89, 4, 48, "WeakMap"], [10, 96, 4, 48], [10, 100, 4, 48, "n"], [10, 101, 4, 48], [10, 108, 4, 48, "WeakMap"], [10, 115, 4, 48], [10, 127, 4, 48, "_interopRequireWildcard"], [10, 150, 4, 48], [10, 162, 4, 48, "_interopRequireWildcard"], [10, 163, 4, 48, "e"], [10, 164, 4, 48], [10, 166, 4, 48, "t"], [10, 167, 4, 48], [10, 176, 4, 48, "t"], [10, 177, 4, 48], [10, 181, 4, 48, "e"], [10, 182, 4, 48], [10, 186, 4, 48, "e"], [10, 187, 4, 48], [10, 188, 4, 48, "__esModule"], [10, 198, 4, 48], [10, 207, 4, 48, "e"], [10, 208, 4, 48], [10, 214, 4, 48, "o"], [10, 215, 4, 48], [10, 217, 4, 48, "i"], [10, 218, 4, 48], [10, 220, 4, 48, "f"], [10, 221, 4, 48], [10, 226, 4, 48, "__proto__"], [10, 235, 4, 48], [10, 243, 4, 48, "default"], [10, 250, 4, 48], [10, 252, 4, 48, "e"], [10, 253, 4, 48], [10, 270, 4, 48, "e"], [10, 271, 4, 48], [10, 294, 4, 48, "e"], [10, 295, 4, 48], [10, 320, 4, 48, "e"], [10, 321, 4, 48], [10, 330, 4, 48, "f"], [10, 331, 4, 48], [10, 337, 4, 48, "o"], [10, 338, 4, 48], [10, 341, 4, 48, "t"], [10, 342, 4, 48], [10, 345, 4, 48, "n"], [10, 346, 4, 48], [10, 349, 4, 48, "r"], [10, 350, 4, 48], [10, 358, 4, 48, "o"], [10, 359, 4, 48], [10, 360, 4, 48, "has"], [10, 363, 4, 48], [10, 364, 4, 48, "e"], [10, 365, 4, 48], [10, 375, 4, 48, "o"], [10, 376, 4, 48], [10, 377, 4, 48, "get"], [10, 380, 4, 48], [10, 381, 4, 48, "e"], [10, 382, 4, 48], [10, 385, 4, 48, "o"], [10, 386, 4, 48], [10, 387, 4, 48, "set"], [10, 390, 4, 48], [10, 391, 4, 48, "e"], [10, 392, 4, 48], [10, 394, 4, 48, "f"], [10, 395, 4, 48], [10, 409, 4, 48, "_t"], [10, 411, 4, 48], [10, 415, 4, 48, "e"], [10, 416, 4, 48], [10, 432, 4, 48, "_t"], [10, 434, 4, 48], [10, 441, 4, 48, "hasOwnProperty"], [10, 455, 4, 48], [10, 456, 4, 48, "call"], [10, 460, 4, 48], [10, 461, 4, 48, "e"], [10, 462, 4, 48], [10, 464, 4, 48, "_t"], [10, 466, 4, 48], [10, 473, 4, 48, "i"], [10, 474, 4, 48], [10, 478, 4, 48, "o"], [10, 479, 4, 48], [10, 482, 4, 48, "Object"], [10, 488, 4, 48], [10, 489, 4, 48, "defineProperty"], [10, 503, 4, 48], [10, 508, 4, 48, "Object"], [10, 514, 4, 48], [10, 515, 4, 48, "getOwnPropertyDescriptor"], [10, 539, 4, 48], [10, 540, 4, 48, "e"], [10, 541, 4, 48], [10, 543, 4, 48, "_t"], [10, 545, 4, 48], [10, 552, 4, 48, "i"], [10, 553, 4, 48], [10, 554, 4, 48, "get"], [10, 557, 4, 48], [10, 561, 4, 48, "i"], [10, 562, 4, 48], [10, 563, 4, 48, "set"], [10, 566, 4, 48], [10, 570, 4, 48, "o"], [10, 571, 4, 48], [10, 572, 4, 48, "f"], [10, 573, 4, 48], [10, 575, 4, 48, "_t"], [10, 577, 4, 48], [10, 579, 4, 48, "i"], [10, 580, 4, 48], [10, 584, 4, 48, "f"], [10, 585, 4, 48], [10, 586, 4, 48, "_t"], [10, 588, 4, 48], [10, 592, 4, 48, "e"], [10, 593, 4, 48], [10, 594, 4, 48, "_t"], [10, 596, 4, 48], [10, 607, 4, 48, "f"], [10, 608, 4, 48], [10, 613, 4, 48, "e"], [10, 614, 4, 48], [10, 616, 4, 48, "t"], [10, 617, 4, 48], [11, 2, 5, 0], [11, 6, 5, 6, "NavigationContent"], [11, 23, 5, 23], [11, 26, 5, 26, "_ref"], [11, 30, 5, 26], [11, 34, 8, 6], [12, 4, 8, 6], [12, 8, 6, 2, "render"], [12, 14, 6, 8], [12, 17, 6, 8, "_ref"], [12, 21, 6, 8], [12, 22, 6, 2, "render"], [12, 28, 6, 8], [13, 6, 7, 2, "children"], [13, 14, 7, 10], [13, 17, 7, 10, "_ref"], [13, 21, 7, 10], [13, 22, 7, 2, "children"], [13, 30, 7, 10], [14, 4, 9, 2], [14, 11, 9, 9, "render"], [14, 17, 9, 15], [14, 18, 9, 16, "children"], [14, 26, 9, 24], [14, 27, 9, 25], [15, 2, 10, 0], [15, 3, 10, 1], [16, 2, 11, 7], [16, 11, 11, 16, "useComponent"], [16, 23, 11, 28, "useComponent"], [16, 24, 11, 29, "render"], [16, 30, 11, 35], [16, 32, 11, 37], [17, 4, 12, 2], [17, 8, 12, 8, "renderRef"], [17, 17, 12, 17], [17, 20, 12, 20, "React"], [17, 25, 12, 25], [17, 26, 12, 26, "useRef"], [17, 32, 12, 32], [17, 33, 12, 33, "render"], [17, 39, 12, 39], [17, 40, 12, 40], [19, 4, 14, 2], [20, 4, 15, 2], [21, 4, 16, 2], [22, 4, 17, 2, "renderRef"], [22, 13, 17, 11], [22, 14, 17, 12, "current"], [22, 21, 17, 19], [22, 24, 17, 22, "render"], [22, 30, 17, 28], [23, 4, 18, 2, "React"], [23, 9, 18, 7], [23, 10, 18, 8, "useEffect"], [23, 19, 18, 17], [23, 20, 18, 18], [23, 26, 18, 24], [24, 6, 19, 4, "renderRef"], [24, 15, 19, 13], [24, 16, 19, 14, "current"], [24, 23, 19, 21], [24, 26, 19, 24], [24, 30, 19, 28], [25, 4, 20, 2], [25, 5, 20, 3], [25, 6, 20, 4], [26, 4, 21, 2], [26, 11, 21, 9, "React"], [26, 16, 21, 14], [26, 17, 21, 15, "useRef"], [26, 23, 21, 21], [26, 24, 21, 22, "_ref2"], [26, 29, 21, 22], [26, 33, 23, 8], [27, 6, 23, 8], [27, 10, 22, 4, "children"], [27, 18, 22, 12], [27, 21, 22, 12, "_ref2"], [27, 26, 22, 12], [27, 27, 22, 4, "children"], [27, 35, 22, 12], [28, 6, 24, 4], [28, 10, 24, 10, "render"], [28, 16, 24, 16], [28, 19, 24, 19, "renderRef"], [28, 28, 24, 28], [28, 29, 24, 29, "current"], [28, 36, 24, 36], [29, 6, 25, 4], [29, 10, 25, 8, "render"], [29, 16, 25, 14], [29, 21, 25, 19], [29, 25, 25, 23], [29, 27, 25, 25], [30, 8, 26, 6], [30, 14, 26, 12], [30, 18, 26, 16, "Error"], [30, 23, 26, 21], [30, 24, 26, 22], [30, 103, 26, 101], [30, 104, 26, 102], [31, 6, 27, 4], [32, 6, 28, 4], [32, 13, 28, 11], [32, 26, 28, 24], [32, 30, 28, 24, "_jsx"], [32, 45, 28, 28], [32, 47, 28, 29, "NavigationContent"], [32, 64, 28, 46], [32, 66, 28, 48], [33, 8, 29, 6, "render"], [33, 14, 29, 12], [33, 16, 29, 14, "render"], [33, 22, 29, 20], [34, 8, 30, 6, "children"], [34, 16, 30, 14], [34, 18, 30, 16, "children"], [35, 6, 31, 4], [35, 7, 31, 5], [35, 8, 31, 6], [36, 4, 32, 2], [36, 5, 32, 3], [36, 6, 32, 4], [36, 7, 32, 5, "current"], [36, 14, 32, 12], [37, 2, 33, 0], [38, 0, 33, 1], [38, 3]], "functionMap": {"names": ["<global>", "NavigationContent", "useComponent", "React.useEffect$argument_0", "React.useRef$argument_0"], "mappings": "AAA;0BCI;CDK;OEC;kBCO;GDE;sBEC;GFW;CFC"}}, "type": "js/module"}]}