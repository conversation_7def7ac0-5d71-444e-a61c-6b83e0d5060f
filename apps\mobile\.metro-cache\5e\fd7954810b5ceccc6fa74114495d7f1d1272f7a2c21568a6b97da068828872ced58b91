{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./oklab", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 200}, "end": {"line": 8, "column": 28, "index": 228}}], "key": "9AeE7AVE9W4KojMzOpRPrdpx+pM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  /*\n   * The vast majority of the code exported by this module is a direct copy of the code from\n   * the culori package (see https://culorijs.org/), which deserves full credit for it.\n   */\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _oklab = _interopRequireDefault(require(_dependencyMap[1], \"./oklab\"));\n  var _default = exports.default = {\n    oklab: _oklab.default\n  };\n});", "lineCount": 17, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_interopRequireDefault"], [8, 28, 3, 0], [8, 31, 3, 0, "require"], [8, 38, 3, 0], [8, 39, 3, 0, "_dependencyMap"], [8, 53, 3, 0], [9, 2, 3, 0, "Object"], [9, 8, 3, 0], [9, 9, 3, 0, "defineProperty"], [9, 23, 3, 0], [9, 24, 3, 0, "exports"], [9, 31, 3, 0], [10, 4, 3, 0, "value"], [10, 9, 3, 0], [11, 2, 3, 0], [12, 2, 3, 0, "exports"], [12, 9, 3, 0], [12, 10, 3, 0, "default"], [12, 17, 3, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_oklab"], [13, 12, 8, 0], [13, 15, 8, 0, "_interopRequireDefault"], [13, 37, 8, 0], [13, 38, 8, 0, "require"], [13, 45, 8, 0], [13, 46, 8, 0, "_dependencyMap"], [13, 60, 8, 0], [14, 2, 8, 28], [14, 6, 8, 28, "_default"], [14, 14, 8, 28], [14, 17, 8, 28, "exports"], [14, 24, 8, 28], [14, 25, 8, 28, "default"], [14, 32, 8, 28], [14, 35, 10, 15], [15, 4, 11, 2, "oklab"], [15, 9, 11, 7], [15, 11, 11, 2, "oklab"], [16, 2, 12, 0], [16, 3, 12, 1], [17, 0, 12, 1], [17, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}