{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 83}, "end": {"line": 2, "column": 44, "index": 127}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 128}, "end": {"line": 6, "column": 32, "index": 223}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.tapHandlerName = exports.tapGestureHandlerProps = exports.TapGestureHandler = void 0;\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[1], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[2], \"./gestureHandlerCommon\");\n  var tapGestureHandlerProps = exports.tapGestureHandlerProps = ['maxDurationMs', 'maxDelayMs', 'numberOfTaps', 'maxDeltaX', 'maxDeltaY', 'maxDist', 'minPointers'];\n\n  /**\n   * @deprecated TapGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Tap()` instead.\n   */\n\n  var tapHandlerName = exports.tapHandlerName = 'TapGestureHandler';\n\n  /**\n   * @deprecated TapGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Tap()` instead.\n   */\n\n  /**\n   * @deprecated TapGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Tap()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n  var TapGestureHandler = exports.TapGestureHandler = (0, _createHandler.default)({\n    name: tapHandlerName,\n    allowedProps: [..._gestureHandlerCommon.baseGestureHandlerProps, ...tapGestureHandlerProps],\n    config: {\n      shouldCancelWhenOutside: true\n    }\n  });\n});", "lineCount": 32, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_createHandler"], [7, 20, 2, 0], [7, 23, 2, 0, "_interopRequireDefault"], [7, 45, 2, 0], [7, 46, 2, 0, "require"], [7, 53, 2, 0], [7, 54, 2, 0, "_dependencyMap"], [7, 68, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 27, 3, 0], [8, 30, 3, 0, "require"], [8, 37, 3, 0], [8, 38, 3, 0, "_dependencyMap"], [8, 52, 3, 0], [9, 2, 8, 7], [9, 6, 8, 13, "tapGestureHandlerProps"], [9, 28, 8, 35], [9, 31, 8, 35, "exports"], [9, 38, 8, 35], [9, 39, 8, 35, "tapGestureHandlerProps"], [9, 61, 8, 35], [9, 64, 8, 38], [9, 65, 9, 2], [9, 80, 9, 17], [9, 82, 10, 2], [9, 94, 10, 14], [9, 96, 11, 2], [9, 110, 11, 16], [9, 112, 12, 2], [9, 123, 12, 13], [9, 125, 13, 2], [9, 136, 13, 13], [9, 138, 14, 2], [9, 147, 14, 11], [9, 149, 15, 2], [9, 162, 15, 15], [9, 163, 16, 10], [11, 2, 69, 0], [12, 0, 70, 0], [13, 0, 71, 0], [15, 2, 76, 7], [15, 6, 76, 13, "tapHandlerName"], [15, 20, 76, 27], [15, 23, 76, 27, "exports"], [15, 30, 76, 27], [15, 31, 76, 27, "tapHandlerName"], [15, 45, 76, 27], [15, 48, 76, 30], [15, 67, 76, 49], [17, 2, 78, 0], [18, 0, 79, 0], [19, 0, 80, 0], [21, 2, 83, 0], [22, 0, 84, 0], [23, 0, 85, 0], [24, 2, 86, 0], [25, 2, 87, 7], [25, 6, 87, 13, "TapGestureHandler"], [25, 23, 87, 30], [25, 26, 87, 30, "exports"], [25, 33, 87, 30], [25, 34, 87, 30, "TapGestureHandler"], [25, 51, 87, 30], [25, 54, 87, 33], [25, 58, 87, 33, "createHandler"], [25, 80, 87, 46], [25, 82, 90, 2], [26, 4, 91, 2, "name"], [26, 8, 91, 6], [26, 10, 91, 8, "tapHandlerName"], [26, 24, 91, 22], [27, 4, 92, 2, "allowedProps"], [27, 16, 92, 14], [27, 18, 92, 16], [27, 19, 93, 4], [27, 22, 93, 7, "baseGestureHandlerProps"], [27, 67, 93, 30], [27, 69, 94, 4], [27, 72, 94, 7, "tapGestureHandlerProps"], [27, 94, 94, 29], [27, 95, 95, 12], [28, 4, 96, 2, "config"], [28, 10, 96, 8], [28, 12, 96, 10], [29, 6, 97, 4, "shouldCancelWhenOutside"], [29, 29, 97, 27], [29, 31, 97, 29], [30, 4, 98, 2], [31, 2, 99, 0], [31, 3, 99, 1], [31, 4, 99, 2], [32, 0, 99, 3], [32, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}