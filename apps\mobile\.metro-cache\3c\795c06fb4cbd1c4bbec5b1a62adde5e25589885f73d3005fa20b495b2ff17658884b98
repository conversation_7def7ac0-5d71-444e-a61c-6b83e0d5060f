{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 53, "index": 53}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.compatibilityFlags = void 0;\n  exports.executeNativeBackPress = executeNativeBackPress;\n  exports.isSearchBarAvailableForCurrentPlatform = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var isSearchBarAvailableForCurrentPlatform = exports.isSearchBarAvailableForCurrentPlatform = ['ios', 'android'].includes(_reactNative.Platform.OS);\n  function executeNativeBackPress() {\n    // This function invokes the native back press event\n    _reactNative.BackHandler.exitApp();\n    return true;\n  }\n\n  /**\n   * Exposes information useful for downstream navigation library implementers,\n   * so they can keep reasonable backward compatibility, if desired.\n   *\n   * We don't mean for this object to only grow in number of fields, however at the same time\n   * we won't be very hasty to reduce it. Expect gradual changes.\n   */\n  var compatibilityFlags = exports.compatibilityFlags = {\n    /**\n     * Because of a bug introduced in https://github.com/software-mansion/react-native-screens/pull/1646\n     * react-native-screens v3.21 changed how header's backTitle handles whitespace strings in https://github.com/software-mansion/react-native-screens/pull/1726\n     * To allow for backwards compatibility in @react-navigation/native-stack we need a way to check if this version or newer is used.\n     * See https://github.com/react-navigation/react-navigation/pull/11423 for more context.\n     */\n    isNewBackTitleImplementation: true,\n    /**\n     * With version 4.0.0 the header implementation has been changed. To allow for backward compat\n     * with native-stack@v6 we want to expose a way to check whether the new implementation\n     * is in use or not.\n     *\n     * See:\n     * * https://github.com/software-mansion/react-native-screens/pull/2325\n     * * https://github.com/react-navigation/react-navigation/pull/12125\n     */\n    usesHeaderFlexboxImplementation: true\n  };\n});", "lineCount": 42, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_reactNative"], [8, 18, 1, 0], [8, 21, 1, 0, "require"], [8, 28, 1, 0], [8, 29, 1, 0, "_dependencyMap"], [8, 43, 1, 0], [9, 2, 3, 7], [9, 6, 3, 13, "isSearchBarAvailableForCurrentPlatform"], [9, 44, 3, 51], [9, 47, 3, 51, "exports"], [9, 54, 3, 51], [9, 55, 3, 51, "isSearchBarAvailableForCurrentPlatform"], [9, 93, 3, 51], [9, 96, 3, 54], [9, 97, 4, 2], [9, 102, 4, 7], [9, 104, 5, 2], [9, 113, 5, 11], [9, 114, 6, 1], [9, 115, 6, 2, "includes"], [9, 123, 6, 10], [9, 124, 6, 11, "Platform"], [9, 145, 6, 19], [9, 146, 6, 20, "OS"], [9, 148, 6, 22], [9, 149, 6, 23], [10, 2, 8, 7], [10, 11, 8, 16, "executeNativeBackPress"], [10, 33, 8, 38, "executeNativeBackPress"], [10, 34, 8, 38], [10, 36, 8, 41], [11, 4, 9, 2], [12, 4, 10, 2, "<PERSON><PERSON><PERSON><PERSON>"], [12, 28, 10, 13], [12, 29, 10, 14, "exitApp"], [12, 36, 10, 21], [12, 37, 10, 22], [12, 38, 10, 23], [13, 4, 11, 2], [13, 11, 11, 9], [13, 15, 11, 13], [14, 2, 12, 0], [16, 2, 14, 0], [17, 0, 15, 0], [18, 0, 16, 0], [19, 0, 17, 0], [20, 0, 18, 0], [21, 0, 19, 0], [22, 0, 20, 0], [23, 2, 21, 7], [23, 6, 21, 13, "compatibilityFlags"], [23, 24, 21, 31], [23, 27, 21, 31, "exports"], [23, 34, 21, 31], [23, 35, 21, 31, "compatibilityFlags"], [23, 53, 21, 31], [23, 56, 21, 34], [24, 4, 22, 2], [25, 0, 23, 0], [26, 0, 24, 0], [27, 0, 25, 0], [28, 0, 26, 0], [29, 0, 27, 0], [30, 4, 28, 2, "isNewBackTitleImplementation"], [30, 32, 28, 30], [30, 34, 28, 32], [30, 38, 28, 36], [31, 4, 30, 2], [32, 0, 31, 0], [33, 0, 32, 0], [34, 0, 33, 0], [35, 0, 34, 0], [36, 0, 35, 0], [37, 0, 36, 0], [38, 0, 37, 0], [39, 0, 38, 0], [40, 4, 39, 2, "usesHeaderFlexboxImplementation"], [40, 35, 39, 33], [40, 37, 39, 35], [41, 2, 40, 0], [41, 3, 40, 1], [42, 0, 40, 2], [42, 3]], "functionMap": {"names": ["<global>", "executeNativeBackPress"], "mappings": "AAA;OCO;CDI"}}, "type": "js/module"}]}