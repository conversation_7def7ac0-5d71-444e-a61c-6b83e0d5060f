{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _startTime = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"startTime\");\n  var _endTime = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"endTime\");\n  var _initializeRuntimeStart = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"initializeRuntimeStart\");\n  var _initializeRuntimeEnd = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"initializeRuntimeEnd\");\n  var _executeJavaScriptBundleEntryPointStart = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"executeJavaScriptBundleEntryPointStart\");\n  var _executeJavaScriptBundleEntryPointEnd = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"executeJavaScriptBundleEntryPointEnd\");\n  var ReactNativeStartupTiming = exports.default = /*#__PURE__*/function () {\n    function ReactNativeStartupTiming(startUpTiming) {\n      (0, _classCallCheck2.default)(this, ReactNativeStartupTiming);\n      Object.defineProperty(this, _startTime, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _endTime, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _initializeRuntimeStart, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _initializeRuntimeEnd, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _executeJavaScriptBundleEntryPointStart, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _executeJavaScriptBundleEntryPointEnd, {\n        writable: true,\n        value: void 0\n      });\n      if (startUpTiming != null) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _startTime)[_startTime] = startUpTiming.startTime;\n        (0, _classPrivateFieldLooseBase2.default)(this, _endTime)[_endTime] = startUpTiming.endTime;\n        (0, _classPrivateFieldLooseBase2.default)(this, _initializeRuntimeStart)[_initializeRuntimeStart] = startUpTiming.initializeRuntimeStart;\n        (0, _classPrivateFieldLooseBase2.default)(this, _initializeRuntimeEnd)[_initializeRuntimeEnd] = startUpTiming.initializeRuntimeEnd;\n        (0, _classPrivateFieldLooseBase2.default)(this, _executeJavaScriptBundleEntryPointStart)[_executeJavaScriptBundleEntryPointStart] = startUpTiming.executeJavaScriptBundleEntryPointStart;\n        (0, _classPrivateFieldLooseBase2.default)(this, _executeJavaScriptBundleEntryPointEnd)[_executeJavaScriptBundleEntryPointEnd] = startUpTiming.executeJavaScriptBundleEntryPointEnd;\n      }\n    }\n    return (0, _createClass2.default)(ReactNativeStartupTiming, [{\n      key: \"startTime\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _startTime)[_startTime];\n      }\n    }, {\n      key: \"endTime\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _endTime)[_endTime];\n      }\n    }, {\n      key: \"initializeRuntimeStart\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _initializeRuntimeStart)[_initializeRuntimeStart];\n      }\n    }, {\n      key: \"initializeRuntimeEnd\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _initializeRuntimeEnd)[_initializeRuntimeEnd];\n      }\n    }, {\n      key: \"executeJavaScriptBundleEntryPointStart\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _executeJavaScriptBundleEntryPointStart)[_executeJavaScriptBundleEntryPointStart];\n      }\n    }, {\n      key: \"executeJavaScriptBundleEntryPointEnd\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _executeJavaScriptBundleEntryPointEnd)[_executeJavaScriptBundleEntryPointEnd];\n      }\n    }]);\n  }();\n});", "lineCount": 85, "map": [[17, 6, 25, 21, "ReactNativeStartupTiming"], [17, 30, 25, 45], [17, 33, 25, 45, "exports"], [17, 40, 25, 45], [17, 41, 25, 45, "default"], [17, 48, 25, 45], [18, 4, 37, 2], [18, 13, 37, 2, "ReactNativeStartupTiming"], [18, 38, 37, 14, "startUpTiming"], [18, 51, 37, 58], [18, 53, 37, 60], [19, 6, 37, 60], [19, 10, 37, 60, "_classCallCheck2"], [19, 26, 37, 60], [19, 27, 37, 60, "default"], [19, 34, 37, 60], [19, 42, 37, 60, "ReactNativeStartupTiming"], [19, 66, 37, 60], [20, 6, 37, 60, "Object"], [20, 12, 37, 60], [20, 13, 37, 60, "defineProperty"], [20, 27, 37, 60], [20, 34, 37, 60, "_startTime"], [20, 44, 37, 60], [21, 8, 37, 60, "writable"], [21, 16, 37, 60], [22, 8, 37, 60, "value"], [22, 13, 37, 60], [23, 6, 37, 60], [24, 6, 37, 60, "Object"], [24, 12, 37, 60], [24, 13, 37, 60, "defineProperty"], [24, 27, 37, 60], [24, 34, 37, 60, "_endTime"], [24, 42, 37, 60], [25, 8, 37, 60, "writable"], [25, 16, 37, 60], [26, 8, 37, 60, "value"], [26, 13, 37, 60], [27, 6, 37, 60], [28, 6, 37, 60, "Object"], [28, 12, 37, 60], [28, 13, 37, 60, "defineProperty"], [28, 27, 37, 60], [28, 34, 37, 60, "_initializeRuntimeStart"], [28, 57, 37, 60], [29, 8, 37, 60, "writable"], [29, 16, 37, 60], [30, 8, 37, 60, "value"], [30, 13, 37, 60], [31, 6, 37, 60], [32, 6, 37, 60, "Object"], [32, 12, 37, 60], [32, 13, 37, 60, "defineProperty"], [32, 27, 37, 60], [32, 34, 37, 60, "_initializeRuntimeEnd"], [32, 55, 37, 60], [33, 8, 37, 60, "writable"], [33, 16, 37, 60], [34, 8, 37, 60, "value"], [34, 13, 37, 60], [35, 6, 37, 60], [36, 6, 37, 60, "Object"], [36, 12, 37, 60], [36, 13, 37, 60, "defineProperty"], [36, 27, 37, 60], [36, 34, 37, 60, "_executeJavaScriptBundleEntryPointStart"], [36, 73, 37, 60], [37, 8, 37, 60, "writable"], [37, 16, 37, 60], [38, 8, 37, 60, "value"], [38, 13, 37, 60], [39, 6, 37, 60], [40, 6, 37, 60, "Object"], [40, 12, 37, 60], [40, 13, 37, 60, "defineProperty"], [40, 27, 37, 60], [40, 34, 37, 60, "_executeJavaScriptBundleEntryPointEnd"], [40, 71, 37, 60], [41, 8, 37, 60, "writable"], [41, 16, 37, 60], [42, 8, 37, 60, "value"], [42, 13, 37, 60], [43, 6, 37, 60], [44, 6, 38, 4], [44, 10, 38, 8, "startUpTiming"], [44, 23, 38, 21], [44, 27, 38, 25], [44, 31, 38, 29], [44, 33, 38, 31], [45, 8, 39, 6], [45, 12, 39, 6, "_classPrivateFieldLooseBase2"], [45, 40, 39, 6], [45, 41, 39, 6, "default"], [45, 48, 39, 6], [45, 54, 39, 10], [45, 56, 39, 10, "_startTime"], [45, 66, 39, 10], [45, 68, 39, 10, "_startTime"], [45, 78, 39, 10], [45, 82, 39, 24, "startUpTiming"], [45, 95, 39, 37], [45, 96, 39, 38, "startTime"], [45, 105, 39, 47], [46, 8, 40, 6], [46, 12, 40, 6, "_classPrivateFieldLooseBase2"], [46, 40, 40, 6], [46, 41, 40, 6, "default"], [46, 48, 40, 6], [46, 54, 40, 10], [46, 56, 40, 10, "_endTime"], [46, 64, 40, 10], [46, 66, 40, 10, "_endTime"], [46, 74, 40, 10], [46, 78, 40, 22, "startUpTiming"], [46, 91, 40, 35], [46, 92, 40, 36, "endTime"], [46, 99, 40, 43], [47, 8, 41, 6], [47, 12, 41, 6, "_classPrivateFieldLooseBase2"], [47, 40, 41, 6], [47, 41, 41, 6, "default"], [47, 48, 41, 6], [47, 54, 41, 10], [47, 56, 41, 10, "_initializeRuntimeStart"], [47, 79, 41, 10], [47, 81, 41, 10, "_initializeRuntimeStart"], [47, 104, 41, 10], [47, 108, 41, 37, "startUpTiming"], [47, 121, 41, 50], [47, 122, 41, 51, "initializeRuntimeStart"], [47, 144, 41, 73], [48, 8, 42, 6], [48, 12, 42, 6, "_classPrivateFieldLooseBase2"], [48, 40, 42, 6], [48, 41, 42, 6, "default"], [48, 48, 42, 6], [48, 54, 42, 10], [48, 56, 42, 10, "_initializeRuntimeEnd"], [48, 77, 42, 10], [48, 79, 42, 10, "_initializeRuntimeEnd"], [48, 100, 42, 10], [48, 104, 42, 35, "startUpTiming"], [48, 117, 42, 48], [48, 118, 42, 49, "initializeRuntimeEnd"], [48, 138, 42, 69], [49, 8, 43, 6], [49, 12, 43, 6, "_classPrivateFieldLooseBase2"], [49, 40, 43, 6], [49, 41, 43, 6, "default"], [49, 48, 43, 6], [49, 54, 43, 10], [49, 56, 43, 10, "_executeJavaScriptBundleEntryPointStart"], [49, 95, 43, 10], [49, 97, 43, 10, "_executeJavaScriptBundleEntryPointStart"], [49, 136, 43, 10], [49, 140, 44, 8, "startUpTiming"], [49, 153, 44, 21], [49, 154, 44, 22, "executeJavaScriptBundleEntryPointStart"], [49, 192, 44, 60], [50, 8, 45, 6], [50, 12, 45, 6, "_classPrivateFieldLooseBase2"], [50, 40, 45, 6], [50, 41, 45, 6, "default"], [50, 48, 45, 6], [50, 54, 45, 10], [50, 56, 45, 10, "_executeJavaScriptBundleEntryPointEnd"], [50, 93, 45, 10], [50, 95, 45, 10, "_executeJavaScriptBundleEntryPointEnd"], [50, 132, 45, 10], [50, 136, 46, 8, "startUpTiming"], [50, 149, 46, 21], [50, 150, 46, 22, "executeJavaScriptBundleEntryPointEnd"], [50, 186, 46, 58], [51, 6, 47, 4], [52, 4, 48, 2], [53, 4, 48, 3], [53, 15, 48, 3, "_createClass2"], [53, 28, 48, 3], [53, 29, 48, 3, "default"], [53, 36, 48, 3], [53, 38, 48, 3, "ReactNativeStartupTiming"], [53, 62, 48, 3], [54, 6, 48, 3, "key"], [54, 9, 48, 3], [55, 6, 48, 3, "get"], [55, 9, 48, 3], [55, 11, 53, 2], [55, 20, 53, 2, "get"], [55, 21, 53, 2], [55, 23, 53, 27], [56, 8, 54, 4], [56, 19, 54, 4, "_classPrivateFieldLooseBase2"], [56, 47, 54, 4], [56, 48, 54, 4, "default"], [56, 55, 54, 4], [56, 57, 54, 11], [56, 61, 54, 15], [56, 63, 54, 15, "_startTime"], [56, 73, 54, 15], [56, 75, 54, 15, "_startTime"], [56, 85, 54, 15], [57, 6, 55, 2], [58, 4, 55, 3], [59, 6, 55, 3, "key"], [59, 9, 55, 3], [60, 6, 55, 3, "get"], [60, 9, 55, 3], [60, 11, 60, 2], [60, 20, 60, 2, "get"], [60, 21, 60, 2], [60, 23, 60, 25], [61, 8, 61, 4], [61, 19, 61, 4, "_classPrivateFieldLooseBase2"], [61, 47, 61, 4], [61, 48, 61, 4, "default"], [61, 55, 61, 4], [61, 57, 61, 11], [61, 61, 61, 15], [61, 63, 61, 15, "_endTime"], [61, 71, 61, 15], [61, 73, 61, 15, "_endTime"], [61, 81, 61, 15], [62, 6, 62, 2], [63, 4, 62, 3], [64, 6, 62, 3, "key"], [64, 9, 62, 3], [65, 6, 62, 3, "get"], [65, 9, 62, 3], [65, 11, 67, 2], [65, 20, 67, 2, "get"], [65, 21, 67, 2], [65, 23, 67, 40], [66, 8, 68, 4], [66, 19, 68, 4, "_classPrivateFieldLooseBase2"], [66, 47, 68, 4], [66, 48, 68, 4, "default"], [66, 55, 68, 4], [66, 57, 68, 11], [66, 61, 68, 15], [66, 63, 68, 15, "_initializeRuntimeStart"], [66, 86, 68, 15], [66, 88, 68, 15, "_initializeRuntimeStart"], [66, 111, 68, 15], [67, 6, 69, 2], [68, 4, 69, 3], [69, 6, 69, 3, "key"], [69, 9, 69, 3], [70, 6, 69, 3, "get"], [70, 9, 69, 3], [70, 11, 74, 2], [70, 20, 74, 2, "get"], [70, 21, 74, 2], [70, 23, 74, 38], [71, 8, 75, 4], [71, 19, 75, 4, "_classPrivateFieldLooseBase2"], [71, 47, 75, 4], [71, 48, 75, 4, "default"], [71, 55, 75, 4], [71, 57, 75, 11], [71, 61, 75, 15], [71, 63, 75, 15, "_initializeRuntimeEnd"], [71, 84, 75, 15], [71, 86, 75, 15, "_initializeRuntimeEnd"], [71, 107, 75, 15], [72, 6, 76, 2], [73, 4, 76, 3], [74, 6, 76, 3, "key"], [74, 9, 76, 3], [75, 6, 76, 3, "get"], [75, 9, 76, 3], [75, 11, 81, 2], [75, 20, 81, 2, "get"], [75, 21, 81, 2], [75, 23, 81, 56], [76, 8, 82, 4], [76, 19, 82, 4, "_classPrivateFieldLooseBase2"], [76, 47, 82, 4], [76, 48, 82, 4, "default"], [76, 55, 82, 4], [76, 57, 82, 11], [76, 61, 82, 15], [76, 63, 82, 15, "_executeJavaScriptBundleEntryPointStart"], [76, 102, 82, 15], [76, 104, 82, 15, "_executeJavaScriptBundleEntryPointStart"], [76, 143, 82, 15], [77, 6, 83, 2], [78, 4, 83, 3], [79, 6, 83, 3, "key"], [79, 9, 83, 3], [80, 6, 83, 3, "get"], [80, 9, 83, 3], [80, 11, 88, 2], [80, 20, 88, 2, "get"], [80, 21, 88, 2], [80, 23, 88, 54], [81, 8, 89, 4], [81, 19, 89, 4, "_classPrivateFieldLooseBase2"], [81, 47, 89, 4], [81, 48, 89, 4, "default"], [81, 55, 89, 4], [81, 57, 89, 11], [81, 61, 89, 15], [81, 63, 89, 15, "_executeJavaScriptBundleEntryPointEnd"], [81, 100, 89, 15], [81, 102, 89, 15, "_executeJavaScriptBundleEntryPointEnd"], [81, 139, 89, 15], [82, 6, 90, 2], [83, 4, 90, 3], [84, 2, 90, 3], [85, 0, 90, 3], [85, 3]], "functionMap": {"names": ["<global>", "ReactNativeStartupTiming", "constructor", "get__startTime", "get__endTime", "get__initializeRuntimeStart", "get__initializeRuntimeEnd", "get__executeJavaScriptBundleEntryPointStart", "get__executeJavaScriptBundleEntryPointEnd"], "mappings": "AAA;eCwB;ECY;GDW;EEK;GFE;EGK;GHE;EIK;GJE;EKK;GLE;EMK;GNE;EOK;GPE"}}, "type": "js/module"}]}