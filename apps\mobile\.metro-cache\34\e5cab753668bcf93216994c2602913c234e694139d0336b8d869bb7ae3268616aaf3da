{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../handlersRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 60}, "end": {"line": 2, "column": 57, "index": 117}}], "key": "Q8MtNj8/mrt1iN8Kay94o881ERE=", "exportNames": ["*"]}}, {"name": "../../../RNGestureHandlerModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 118}, "end": {"line": 3, "column": 69, "index": 187}}], "key": "2BYIjnTRSFId8SRJ7sJFxLD1BD4=", "exportNames": ["*"]}}, {"name": "../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 188}, "end": {"line": 4, "column": 68, "index": 256}}], "key": "ByXat9lt9duIJLDmSeH0V+tRq1s=", "exportNames": ["*"]}}, {"name": "../../../ghQueueMicrotask", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 314}, "end": {"line": 6, "column": 61, "index": 375}}], "key": "6QYiO8x9sAoDBctSRJ19A8sqUNk=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 424}, "end": {"line": 12, "column": 17, "index": 530}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.updateHandlers = updateHandlers;\n  var _handlersRegistry = require(_dependencyMap[1], \"../../handlersRegistry\");\n  var _RNGestureHandlerModule = _interopRequireDefault(require(_dependencyMap[2], \"../../../RNGestureHandlerModule\"));\n  var _utils = require(_dependencyMap[3], \"../../utils\");\n  var _ghQueueMicrotask = require(_dependencyMap[4], \"../../../ghQueueMicrotask\");\n  var _utils2 = require(_dependencyMap[5], \"./utils\");\n  function updateHandlers(preparedGesture, gestureConfig, newGestures) {\n    gestureConfig.prepare();\n    for (var i = 0; i < newGestures.length; i++) {\n      var handler = preparedGesture.attachedGestures[i];\n      (0, _utils2.checkGestureCallbacksForWorklets)(handler);\n\n      // Only update handlerTag when it's actually different, it may be the same\n      // if gesture config object is wrapped with useMemo\n      if (newGestures[i].handlerTag !== handler.handlerTag) {\n        newGestures[i].handlerTag = handler.handlerTag;\n        newGestures[i].handlers.handlerTag = handler.handlerTag;\n      }\n    }\n\n    // Store attached gestures to avoid crash when gestures changed after queueing micro task\n    var attachedGestures = preparedGesture.attachedGestures;\n\n    // Use queueMicrotask to extract handlerTags, because when it's ran, all refs should be updated\n    // and handlerTags in BaseGesture references should be updated in the loop above (we need to wait\n    // in case of external relations)\n    (0, _ghQueueMicrotask.ghQueueMicrotask)(() => {\n      if (!preparedGesture.isMounted) {\n        return;\n      }\n\n      // Stop if attached gestures changed after queueing micro task\n      if (attachedGestures !== preparedGesture.attachedGestures) {\n        return;\n      }\n\n      // If amount of gesture configs changes, we need to update the callbacks in shared value\n      var shouldUpdateSharedValueIfUsed = attachedGestures.length !== newGestures.length;\n      for (var _i = 0; _i < newGestures.length; _i++) {\n        var _handler = attachedGestures[_i];\n\n        // If the gestureId is different (gesture isn't wrapped with useMemo or its dependencies changed),\n        // we need to update the shared value, assuming the gesture runs on UI thread or the thread changed\n        if (_handler.handlers.gestureId !== newGestures[_i].handlers.gestureId && (newGestures[_i].shouldUseReanimated || _handler.shouldUseReanimated)) {\n          shouldUpdateSharedValueIfUsed = true;\n        }\n        _handler.config = newGestures[_i].config;\n        _handler.handlers = newGestures[_i].handlers;\n        _RNGestureHandlerModule.default.updateGestureHandler(_handler.handlerTag, (0, _utils.filterConfig)(_handler.config, _utils2.ALLOWED_PROPS, (0, _utils2.extractGestureRelations)(_handler)));\n        (0, _handlersRegistry.registerHandler)(_handler.handlerTag, _handler, _handler.config.testId);\n      }\n      if (preparedGesture.animatedHandlers && shouldUpdateSharedValueIfUsed) {\n        var newHandlersValue = attachedGestures.filter(g => g.shouldUseReanimated) // Ignore gestures that shouldn't run on UI\n        .map(g => g.handlers);\n        preparedGesture.animatedHandlers.value = newHandlersValue;\n      }\n      (0, _utils.scheduleFlushOperations)();\n    });\n  }\n});", "lineCount": 65, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_handlersRegistry"], [7, 23, 2, 0], [7, 26, 2, 0, "require"], [7, 33, 2, 0], [7, 34, 2, 0, "_dependencyMap"], [7, 48, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_RNGestureHandlerModule"], [8, 29, 3, 0], [8, 32, 3, 0, "_interopRequireDefault"], [8, 54, 3, 0], [8, 55, 3, 0, "require"], [8, 62, 3, 0], [8, 63, 3, 0, "_dependencyMap"], [8, 77, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_utils"], [9, 12, 4, 0], [9, 15, 4, 0, "require"], [9, 22, 4, 0], [9, 23, 4, 0, "_dependencyMap"], [9, 37, 4, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_ghQueueMicrotask"], [10, 23, 6, 0], [10, 26, 6, 0, "require"], [10, 33, 6, 0], [10, 34, 6, 0, "_dependencyMap"], [10, 48, 6, 0], [11, 2, 8, 0], [11, 6, 8, 0, "_utils2"], [11, 13, 8, 0], [11, 16, 8, 0, "require"], [11, 23, 8, 0], [11, 24, 8, 0, "_dependencyMap"], [11, 38, 8, 0], [12, 2, 14, 7], [12, 11, 14, 16, "updateHandlers"], [12, 25, 14, 30, "updateHandlers"], [12, 26, 15, 2, "preparedGesture"], [12, 41, 15, 39], [12, 43, 16, 2, "gestureConfig"], [12, 56, 16, 46], [12, 58, 17, 2, "newGestures"], [12, 69, 17, 28], [12, 71, 18, 2], [13, 4, 19, 2, "gestureConfig"], [13, 17, 19, 15], [13, 18, 19, 16, "prepare"], [13, 25, 19, 23], [13, 26, 19, 24], [13, 27, 19, 25], [14, 4, 21, 2], [14, 9, 21, 7], [14, 13, 21, 11, "i"], [14, 14, 21, 12], [14, 17, 21, 15], [14, 18, 21, 16], [14, 20, 21, 18, "i"], [14, 21, 21, 19], [14, 24, 21, 22, "newGestures"], [14, 35, 21, 33], [14, 36, 21, 34, "length"], [14, 42, 21, 40], [14, 44, 21, 42, "i"], [14, 45, 21, 43], [14, 47, 21, 45], [14, 49, 21, 47], [15, 6, 22, 4], [15, 10, 22, 10, "handler"], [15, 17, 22, 17], [15, 20, 22, 20, "preparedGesture"], [15, 35, 22, 35], [15, 36, 22, 36, "attachedGestures"], [15, 52, 22, 52], [15, 53, 22, 53, "i"], [15, 54, 22, 54], [15, 55, 22, 55], [16, 6, 23, 4], [16, 10, 23, 4, "checkGestureCallbacksForWorklets"], [16, 50, 23, 36], [16, 52, 23, 37, "handler"], [16, 59, 23, 44], [16, 60, 23, 45], [18, 6, 25, 4], [19, 6, 26, 4], [20, 6, 27, 4], [20, 10, 27, 8, "newGestures"], [20, 21, 27, 19], [20, 22, 27, 20, "i"], [20, 23, 27, 21], [20, 24, 27, 22], [20, 25, 27, 23, "handlerTag"], [20, 35, 27, 33], [20, 40, 27, 38, "handler"], [20, 47, 27, 45], [20, 48, 27, 46, "handlerTag"], [20, 58, 27, 56], [20, 60, 27, 58], [21, 8, 28, 6, "newGestures"], [21, 19, 28, 17], [21, 20, 28, 18, "i"], [21, 21, 28, 19], [21, 22, 28, 20], [21, 23, 28, 21, "handlerTag"], [21, 33, 28, 31], [21, 36, 28, 34, "handler"], [21, 43, 28, 41], [21, 44, 28, 42, "handlerTag"], [21, 54, 28, 52], [22, 8, 29, 6, "newGestures"], [22, 19, 29, 17], [22, 20, 29, 18, "i"], [22, 21, 29, 19], [22, 22, 29, 20], [22, 23, 29, 21, "handlers"], [22, 31, 29, 29], [22, 32, 29, 30, "handlerTag"], [22, 42, 29, 40], [22, 45, 29, 43, "handler"], [22, 52, 29, 50], [22, 53, 29, 51, "handlerTag"], [22, 63, 29, 61], [23, 6, 30, 4], [24, 4, 31, 2], [26, 4, 33, 2], [27, 4, 34, 2], [27, 8, 34, 8, "attachedGestures"], [27, 24, 34, 24], [27, 27, 34, 27, "preparedGesture"], [27, 42, 34, 42], [27, 43, 34, 43, "attachedGestures"], [27, 59, 34, 59], [29, 4, 36, 2], [30, 4, 37, 2], [31, 4, 38, 2], [32, 4, 39, 2], [32, 8, 39, 2, "ghQueueMicrotask"], [32, 42, 39, 18], [32, 44, 39, 19], [32, 50, 39, 25], [33, 6, 40, 4], [33, 10, 40, 8], [33, 11, 40, 9, "preparedGesture"], [33, 26, 40, 24], [33, 27, 40, 25, "isMounted"], [33, 36, 40, 34], [33, 38, 40, 36], [34, 8, 41, 6], [35, 6, 42, 4], [37, 6, 44, 4], [38, 6, 45, 4], [38, 10, 45, 8, "attachedGestures"], [38, 26, 45, 24], [38, 31, 45, 29, "preparedGesture"], [38, 46, 45, 44], [38, 47, 45, 45, "attachedGestures"], [38, 63, 45, 61], [38, 65, 45, 63], [39, 8, 46, 6], [40, 6, 47, 4], [42, 6, 49, 4], [43, 6, 50, 4], [43, 10, 50, 8, "shouldUpdateSharedValueIfUsed"], [43, 39, 50, 37], [43, 42, 51, 6, "attachedGestures"], [43, 58, 51, 22], [43, 59, 51, 23, "length"], [43, 65, 51, 29], [43, 70, 51, 34, "newGestures"], [43, 81, 51, 45], [43, 82, 51, 46, "length"], [43, 88, 51, 52], [44, 6, 53, 4], [44, 11, 53, 9], [44, 15, 53, 13, "i"], [44, 17, 53, 14], [44, 20, 53, 17], [44, 21, 53, 18], [44, 23, 53, 20, "i"], [44, 25, 53, 21], [44, 28, 53, 24, "newGestures"], [44, 39, 53, 35], [44, 40, 53, 36, "length"], [44, 46, 53, 42], [44, 48, 53, 44, "i"], [44, 50, 53, 45], [44, 52, 53, 47], [44, 54, 53, 49], [45, 8, 54, 6], [45, 12, 54, 12, "handler"], [45, 20, 54, 19], [45, 23, 54, 22, "attachedGestures"], [45, 39, 54, 38], [45, 40, 54, 39, "i"], [45, 42, 54, 40], [45, 43, 54, 41], [47, 8, 56, 6], [48, 8, 57, 6], [49, 8, 58, 6], [49, 12, 59, 8, "handler"], [49, 20, 59, 15], [49, 21, 59, 16, "handlers"], [49, 29, 59, 24], [49, 30, 59, 25, "gestureId"], [49, 39, 59, 34], [49, 44, 59, 39, "newGestures"], [49, 55, 59, 50], [49, 56, 59, 51, "i"], [49, 58, 59, 52], [49, 59, 59, 53], [49, 60, 59, 54, "handlers"], [49, 68, 59, 62], [49, 69, 59, 63, "gestureId"], [49, 78, 59, 72], [49, 83, 60, 9, "newGestures"], [49, 94, 60, 20], [49, 95, 60, 21, "i"], [49, 97, 60, 22], [49, 98, 60, 23], [49, 99, 60, 24, "shouldUseReanimated"], [49, 118, 60, 43], [49, 122, 60, 47, "handler"], [49, 130, 60, 54], [49, 131, 60, 55, "shouldUseReanimated"], [49, 150, 60, 74], [49, 151, 60, 75], [49, 153, 61, 8], [50, 10, 62, 8, "shouldUpdateSharedValueIfUsed"], [50, 39, 62, 37], [50, 42, 62, 40], [50, 46, 62, 44], [51, 8, 63, 6], [52, 8, 65, 6, "handler"], [52, 16, 65, 13], [52, 17, 65, 14, "config"], [52, 23, 65, 20], [52, 26, 65, 23, "newGestures"], [52, 37, 65, 34], [52, 38, 65, 35, "i"], [52, 40, 65, 36], [52, 41, 65, 37], [52, 42, 65, 38, "config"], [52, 48, 65, 44], [53, 8, 66, 6, "handler"], [53, 16, 66, 13], [53, 17, 66, 14, "handlers"], [53, 25, 66, 22], [53, 28, 66, 25, "newGestures"], [53, 39, 66, 36], [53, 40, 66, 37, "i"], [53, 42, 66, 38], [53, 43, 66, 39], [53, 44, 66, 40, "handlers"], [53, 52, 66, 48], [54, 8, 68, 6, "RNGestureHandlerModule"], [54, 39, 68, 28], [54, 40, 68, 29, "updateGestureHandler"], [54, 60, 68, 49], [54, 61, 69, 8, "handler"], [54, 69, 69, 15], [54, 70, 69, 16, "handlerTag"], [54, 80, 69, 26], [54, 82, 70, 8], [54, 86, 70, 8, "filterConfig"], [54, 105, 70, 20], [54, 107, 71, 10, "handler"], [54, 115, 71, 17], [54, 116, 71, 18, "config"], [54, 122, 71, 24], [54, 124, 72, 10, "ALLOWED_PROPS"], [54, 145, 72, 23], [54, 147, 73, 10], [54, 151, 73, 10, "extractGestureRelations"], [54, 182, 73, 33], [54, 184, 73, 34, "handler"], [54, 192, 73, 41], [54, 193, 74, 8], [54, 194, 75, 6], [54, 195, 75, 7], [55, 8, 77, 6], [55, 12, 77, 6, "registerHandler"], [55, 45, 77, 21], [55, 47, 77, 22, "handler"], [55, 55, 77, 29], [55, 56, 77, 30, "handlerTag"], [55, 66, 77, 40], [55, 68, 77, 42, "handler"], [55, 76, 77, 49], [55, 78, 77, 51, "handler"], [55, 86, 77, 58], [55, 87, 77, 59, "config"], [55, 93, 77, 65], [55, 94, 77, 66, "testId"], [55, 100, 77, 72], [55, 101, 77, 73], [56, 6, 78, 4], [57, 6, 80, 4], [57, 10, 80, 8, "preparedGesture"], [57, 25, 80, 23], [57, 26, 80, 24, "animatedHandlers"], [57, 42, 80, 40], [57, 46, 80, 44, "shouldUpdateSharedValueIfUsed"], [57, 75, 80, 73], [57, 77, 80, 75], [58, 8, 81, 6], [58, 12, 81, 12, "newHandlersValue"], [58, 28, 81, 28], [58, 31, 81, 31, "attachedGestures"], [58, 47, 81, 47], [58, 48, 82, 9, "filter"], [58, 54, 82, 15], [58, 55, 82, 17, "g"], [58, 56, 82, 18], [58, 60, 82, 23, "g"], [58, 61, 82, 24], [58, 62, 82, 25, "shouldUseReanimated"], [58, 81, 82, 44], [58, 82, 82, 45], [58, 83, 82, 46], [59, 8, 82, 46], [59, 9, 83, 9, "map"], [59, 12, 83, 12], [59, 13, 83, 14, "g"], [59, 14, 83, 15], [59, 18, 83, 20, "g"], [59, 19, 83, 21], [59, 20, 83, 22, "handlers"], [59, 28, 83, 30], [59, 29, 85, 9], [60, 8, 87, 6, "preparedGesture"], [60, 23, 87, 21], [60, 24, 87, 22, "animatedHandlers"], [60, 40, 87, 38], [60, 41, 87, 39, "value"], [60, 46, 87, 44], [60, 49, 87, 47, "newHandlersValue"], [60, 65, 87, 63], [61, 6, 88, 4], [62, 6, 90, 4], [62, 10, 90, 4, "scheduleFlushOperations"], [62, 40, 90, 27], [62, 42, 90, 28], [62, 43, 90, 29], [63, 4, 91, 2], [63, 5, 91, 3], [63, 6, 91, 4], [64, 2, 92, 0], [65, 0, 92, 1], [65, 3]], "functionMap": {"names": ["<global>", "updateHandlers", "ghQueueMicrotask$argument_0", "attachedGestures.filter$argument_0", "attachedGestures.filter.map$argument_0"], "mappings": "AAA;OCa;mBCyB;gBC2C,4BD;aEC,iBF;GDQ;CDC"}}, "type": "js/module"}]}