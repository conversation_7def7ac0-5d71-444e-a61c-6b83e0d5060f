{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = parseHermesStack;\n  var RE_FRAME = /^ {4}at (.+?)(?: \\((native)\\)?| \\((address at )?(.*?):(\\d+):(\\d+)\\))$/;\n  var RE_SKIPPED = /^ {4}... skipping (\\d+) frames$/;\n  var RE_COMPONENT_NO_STACK = /^ {4}at .*$/;\n  function isInternalBytecodeSourceUrl(sourceUrl) {\n    return sourceUrl === 'InternalBytecode.js';\n  }\n  function parseLine(line) {\n    var asFrame = line.match(RE_FRAME);\n    if (asFrame) {\n      return {\n        type: 'FRAME',\n        functionName: asFrame[1],\n        location: asFrame[2] === 'native' ? {\n          type: 'NATIVE'\n        } : asFrame[3] === 'address at ' ? isInternalBytecodeSourceUrl(asFrame[4]) ? {\n          type: 'INTERNAL_BYTECODE',\n          sourceUrl: asFrame[4],\n          line1Based: Number.parseInt(asFrame[5], 10),\n          virtualOffset0Based: Number.parseInt(asFrame[6], 10)\n        } : {\n          type: 'BYTECODE',\n          sourceUrl: asFrame[4],\n          line1Based: Number.parseInt(asFrame[5], 10),\n          virtualOffset0Based: Number.parseInt(asFrame[6], 10)\n        } : {\n          type: 'SOURCE',\n          sourceUrl: asFrame[4],\n          line1Based: Number.parseInt(asFrame[5], 10),\n          column1Based: Number.parseInt(asFrame[6], 10)\n        }\n      };\n    }\n    var asSkipped = line.match(RE_SKIPPED);\n    if (asSkipped) {\n      return {\n        type: 'SKIPPED',\n        count: Number.parseInt(asSkipped[1], 10)\n      };\n    }\n  }\n  function parseHermesStack(stack) {\n    var lines = stack.split(/\\n/);\n    var entries = [];\n    var lastMessageLine = -1;\n    for (var i = 0; i < lines.length; ++i) {\n      var line = lines[i];\n      if (!line) {\n        continue;\n      }\n      var entry = parseLine(line);\n      if (entry) {\n        entries.push(entry);\n        continue;\n      }\n      if (RE_COMPONENT_NO_STACK.test(line)) {\n        continue;\n      }\n      lastMessageLine = i;\n      entries = [];\n    }\n    var message = lines.slice(0, lastMessageLine + 1).join('\\n');\n    return {\n      message,\n      entries\n    };\n  }\n});", "lineCount": 74, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [7, 20, 11, 13, "parseHermesStack"], [7, 36, 11, 13], [8, 2, 69, 0], [8, 6, 69, 6, "RE_FRAME"], [8, 14, 69, 14], [8, 17, 70, 2], [8, 88, 70, 73], [9, 2, 74, 0], [9, 6, 74, 6, "RE_SKIPPED"], [9, 16, 74, 16], [9, 19, 74, 19], [9, 52, 74, 52], [10, 2, 75, 0], [10, 6, 75, 6, "RE_COMPONENT_NO_STACK"], [10, 27, 75, 27], [10, 30, 75, 30], [10, 43, 75, 43], [11, 2, 77, 0], [11, 11, 77, 9, "isInternalBytecodeSourceUrl"], [11, 38, 77, 36, "isInternalBytecodeSourceUrl"], [11, 39, 77, 37, "sourceUrl"], [11, 48, 77, 54], [11, 50, 77, 65], [12, 4, 79, 2], [12, 11, 79, 9, "sourceUrl"], [12, 20, 79, 18], [12, 25, 79, 23], [12, 46, 79, 44], [13, 2, 80, 0], [14, 2, 82, 0], [14, 11, 82, 9, "parseLine"], [14, 20, 82, 18, "parseLine"], [14, 21, 82, 19, "line"], [14, 25, 82, 31], [14, 27, 82, 52], [15, 4, 83, 2], [15, 8, 83, 8, "as<PERSON><PERSON><PERSON>"], [15, 15, 83, 15], [15, 18, 83, 18, "line"], [15, 22, 83, 22], [15, 23, 83, 23, "match"], [15, 28, 83, 28], [15, 29, 83, 29, "RE_FRAME"], [15, 37, 83, 37], [15, 38, 83, 38], [16, 4, 84, 2], [16, 8, 84, 6, "as<PERSON><PERSON><PERSON>"], [16, 15, 84, 13], [16, 17, 84, 15], [17, 6, 85, 4], [17, 13, 85, 11], [18, 8, 86, 6, "type"], [18, 12, 86, 10], [18, 14, 86, 12], [18, 21, 86, 19], [19, 8, 87, 6, "functionName"], [19, 20, 87, 18], [19, 22, 87, 20, "as<PERSON><PERSON><PERSON>"], [19, 29, 87, 27], [19, 30, 87, 28], [19, 31, 87, 29], [19, 32, 87, 30], [20, 8, 88, 6, "location"], [20, 16, 88, 14], [20, 18, 89, 8, "as<PERSON><PERSON><PERSON>"], [20, 25, 89, 15], [20, 26, 89, 16], [20, 27, 89, 17], [20, 28, 89, 18], [20, 33, 89, 23], [20, 41, 89, 31], [20, 44, 90, 12], [21, 10, 90, 13, "type"], [21, 14, 90, 17], [21, 16, 90, 19], [22, 8, 90, 27], [22, 9, 90, 28], [22, 12, 91, 12, "as<PERSON><PERSON><PERSON>"], [22, 19, 91, 19], [22, 20, 91, 20], [22, 21, 91, 21], [22, 22, 91, 22], [22, 27, 91, 27], [22, 40, 91, 40], [22, 43, 92, 14, "isInternalBytecodeSourceUrl"], [22, 70, 92, 41], [22, 71, 92, 42, "as<PERSON><PERSON><PERSON>"], [22, 78, 92, 49], [22, 79, 92, 50], [22, 80, 92, 51], [22, 81, 92, 52], [22, 82, 92, 53], [22, 85, 93, 16], [23, 10, 94, 18, "type"], [23, 14, 94, 22], [23, 16, 94, 24], [23, 35, 94, 43], [24, 10, 95, 18, "sourceUrl"], [24, 19, 95, 27], [24, 21, 95, 29, "as<PERSON><PERSON><PERSON>"], [24, 28, 95, 36], [24, 29, 95, 37], [24, 30, 95, 38], [24, 31, 95, 39], [25, 10, 96, 18, "line1Based"], [25, 20, 96, 28], [25, 22, 96, 30, "Number"], [25, 28, 96, 36], [25, 29, 96, 37, "parseInt"], [25, 37, 96, 45], [25, 38, 96, 46, "as<PERSON><PERSON><PERSON>"], [25, 45, 96, 53], [25, 46, 96, 54], [25, 47, 96, 55], [25, 48, 96, 56], [25, 50, 96, 58], [25, 52, 96, 60], [25, 53, 96, 61], [26, 10, 97, 18, "virtualOffset0Based"], [26, 29, 97, 37], [26, 31, 97, 39, "Number"], [26, 37, 97, 45], [26, 38, 97, 46, "parseInt"], [26, 46, 97, 54], [26, 47, 97, 55, "as<PERSON><PERSON><PERSON>"], [26, 54, 97, 62], [26, 55, 97, 63], [26, 56, 97, 64], [26, 57, 97, 65], [26, 59, 97, 67], [26, 61, 97, 69], [27, 8, 98, 16], [27, 9, 98, 17], [27, 12, 99, 16], [28, 10, 100, 18, "type"], [28, 14, 100, 22], [28, 16, 100, 24], [28, 26, 100, 34], [29, 10, 101, 18, "sourceUrl"], [29, 19, 101, 27], [29, 21, 101, 29, "as<PERSON><PERSON><PERSON>"], [29, 28, 101, 36], [29, 29, 101, 37], [29, 30, 101, 38], [29, 31, 101, 39], [30, 10, 102, 18, "line1Based"], [30, 20, 102, 28], [30, 22, 102, 30, "Number"], [30, 28, 102, 36], [30, 29, 102, 37, "parseInt"], [30, 37, 102, 45], [30, 38, 102, 46, "as<PERSON><PERSON><PERSON>"], [30, 45, 102, 53], [30, 46, 102, 54], [30, 47, 102, 55], [30, 48, 102, 56], [30, 50, 102, 58], [30, 52, 102, 60], [30, 53, 102, 61], [31, 10, 103, 18, "virtualOffset0Based"], [31, 29, 103, 37], [31, 31, 103, 39, "Number"], [31, 37, 103, 45], [31, 38, 103, 46, "parseInt"], [31, 46, 103, 54], [31, 47, 103, 55, "as<PERSON><PERSON><PERSON>"], [31, 54, 103, 62], [31, 55, 103, 63], [31, 56, 103, 64], [31, 57, 103, 65], [31, 59, 103, 67], [31, 61, 103, 69], [32, 8, 104, 16], [32, 9, 104, 17], [32, 12, 105, 14], [33, 10, 106, 16, "type"], [33, 14, 106, 20], [33, 16, 106, 22], [33, 24, 106, 30], [34, 10, 107, 16, "sourceUrl"], [34, 19, 107, 25], [34, 21, 107, 27, "as<PERSON><PERSON><PERSON>"], [34, 28, 107, 34], [34, 29, 107, 35], [34, 30, 107, 36], [34, 31, 107, 37], [35, 10, 108, 16, "line1Based"], [35, 20, 108, 26], [35, 22, 108, 28, "Number"], [35, 28, 108, 34], [35, 29, 108, 35, "parseInt"], [35, 37, 108, 43], [35, 38, 108, 44, "as<PERSON><PERSON><PERSON>"], [35, 45, 108, 51], [35, 46, 108, 52], [35, 47, 108, 53], [35, 48, 108, 54], [35, 50, 108, 56], [35, 52, 108, 58], [35, 53, 108, 59], [36, 10, 109, 16, "column1Based"], [36, 22, 109, 28], [36, 24, 109, 30, "Number"], [36, 30, 109, 36], [36, 31, 109, 37, "parseInt"], [36, 39, 109, 45], [36, 40, 109, 46, "as<PERSON><PERSON><PERSON>"], [36, 47, 109, 53], [36, 48, 109, 54], [36, 49, 109, 55], [36, 50, 109, 56], [36, 52, 109, 58], [36, 54, 109, 60], [37, 8, 110, 14], [38, 6, 111, 4], [38, 7, 111, 5], [39, 4, 112, 2], [40, 4, 113, 2], [40, 8, 113, 8, "asSkipped"], [40, 17, 113, 17], [40, 20, 113, 20, "line"], [40, 24, 113, 24], [40, 25, 113, 25, "match"], [40, 30, 113, 30], [40, 31, 113, 31, "RE_SKIPPED"], [40, 41, 113, 41], [40, 42, 113, 42], [41, 4, 114, 2], [41, 8, 114, 6, "asSkipped"], [41, 17, 114, 15], [41, 19, 114, 17], [42, 6, 115, 4], [42, 13, 115, 11], [43, 8, 116, 6, "type"], [43, 12, 116, 10], [43, 14, 116, 12], [43, 23, 116, 21], [44, 8, 117, 6, "count"], [44, 13, 117, 11], [44, 15, 117, 13, "Number"], [44, 21, 117, 19], [44, 22, 117, 20, "parseInt"], [44, 30, 117, 28], [44, 31, 117, 29, "asSkipped"], [44, 40, 117, 38], [44, 41, 117, 39], [44, 42, 117, 40], [44, 43, 117, 41], [44, 45, 117, 43], [44, 47, 117, 45], [45, 6, 118, 4], [45, 7, 118, 5], [46, 4, 119, 2], [47, 2, 120, 0], [48, 2, 122, 15], [48, 11, 122, 24, "parseHermesStack"], [48, 27, 122, 40, "parseHermesStack"], [48, 28, 122, 41, "stack"], [48, 33, 122, 54], [48, 35, 122, 75], [49, 4, 123, 2], [49, 8, 123, 8, "lines"], [49, 13, 123, 13], [49, 16, 123, 16, "stack"], [49, 21, 123, 21], [49, 22, 123, 22, "split"], [49, 27, 123, 27], [49, 28, 123, 28], [49, 32, 123, 32], [49, 33, 123, 33], [50, 4, 124, 2], [50, 8, 124, 6, "entries"], [50, 15, 124, 69], [50, 18, 124, 72], [50, 20, 124, 74], [51, 4, 125, 2], [51, 8, 125, 6, "lastMessageLine"], [51, 23, 125, 21], [51, 26, 125, 24], [51, 27, 125, 25], [51, 28, 125, 26], [52, 4, 126, 2], [52, 9, 126, 7], [52, 13, 126, 11, "i"], [52, 14, 126, 12], [52, 17, 126, 15], [52, 18, 126, 16], [52, 20, 126, 18, "i"], [52, 21, 126, 19], [52, 24, 126, 22, "lines"], [52, 29, 126, 27], [52, 30, 126, 28, "length"], [52, 36, 126, 34], [52, 38, 126, 36], [52, 40, 126, 38, "i"], [52, 41, 126, 39], [52, 43, 126, 41], [53, 6, 127, 4], [53, 10, 127, 10, "line"], [53, 14, 127, 14], [53, 17, 127, 17, "lines"], [53, 22, 127, 22], [53, 23, 127, 23, "i"], [53, 24, 127, 24], [53, 25, 127, 25], [54, 6, 128, 4], [54, 10, 128, 8], [54, 11, 128, 9, "line"], [54, 15, 128, 13], [54, 17, 128, 15], [55, 8, 129, 6], [56, 6, 130, 4], [57, 6, 131, 4], [57, 10, 131, 10, "entry"], [57, 15, 131, 15], [57, 18, 131, 18, "parseLine"], [57, 27, 131, 27], [57, 28, 131, 28, "line"], [57, 32, 131, 32], [57, 33, 131, 33], [58, 6, 132, 4], [58, 10, 132, 8, "entry"], [58, 15, 132, 13], [58, 17, 132, 15], [59, 8, 133, 6, "entries"], [59, 15, 133, 13], [59, 16, 133, 14, "push"], [59, 20, 133, 18], [59, 21, 133, 19, "entry"], [59, 26, 133, 24], [59, 27, 133, 25], [60, 8, 134, 6], [61, 6, 135, 4], [62, 6, 136, 4], [62, 10, 136, 8, "RE_COMPONENT_NO_STACK"], [62, 31, 136, 29], [62, 32, 136, 30, "test"], [62, 36, 136, 34], [62, 37, 136, 35, "line"], [62, 41, 136, 39], [62, 42, 136, 40], [62, 44, 136, 42], [63, 8, 139, 6], [64, 6, 140, 4], [65, 6, 142, 4, "lastMessageLine"], [65, 21, 142, 19], [65, 24, 142, 22, "i"], [65, 25, 142, 23], [66, 6, 143, 4, "entries"], [66, 13, 143, 11], [66, 16, 143, 14], [66, 18, 143, 16], [67, 4, 144, 2], [68, 4, 145, 2], [68, 8, 145, 8, "message"], [68, 15, 145, 15], [68, 18, 145, 18, "lines"], [68, 23, 145, 23], [68, 24, 145, 24, "slice"], [68, 29, 145, 29], [68, 30, 145, 30], [68, 31, 145, 31], [68, 33, 145, 33, "lastMessageLine"], [68, 48, 145, 48], [68, 51, 145, 51], [68, 52, 145, 52], [68, 53, 145, 53], [68, 54, 145, 54, "join"], [68, 58, 145, 58], [68, 59, 145, 59], [68, 63, 145, 63], [68, 64, 145, 64], [69, 4, 146, 2], [69, 11, 146, 9], [70, 6, 146, 10, "message"], [70, 13, 146, 17], [71, 6, 146, 19, "entries"], [72, 4, 146, 26], [72, 5, 146, 27], [73, 2, 147, 0], [74, 0, 147, 1], [74, 3]], "functionMap": {"names": ["<global>", "isInternalBytecodeSourceUrl", "parseLine", "parseHermesStack"], "mappings": "AAA;AC4E;CDG;AEE;CFsC;eGE"}}, "type": "js/module"}]}