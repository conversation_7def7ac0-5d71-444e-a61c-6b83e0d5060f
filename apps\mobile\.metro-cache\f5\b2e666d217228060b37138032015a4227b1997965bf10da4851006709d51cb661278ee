{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./Pressable", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 89}, "end": {"line": 5, "column": 38, "index": 127}}], "key": "dBAxwTctyU15QMaM5u+aadP3liE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"default\", {\n    enumerable: true,\n    get: function () {\n      return _Pressable.default;\n    }\n  });\n  var _Pressable = _interopRequireDefault(require(_dependencyMap[1], \"./Pressable\"));\n});", "lineCount": 13, "map": [[12, 2, 5, 0], [12, 6, 5, 0, "_Pressable"], [12, 16, 5, 0], [12, 19, 5, 0, "_interopRequireDefault"], [12, 41, 5, 0], [12, 42, 5, 0, "require"], [12, 49, 5, 0], [12, 50, 5, 0, "_dependencyMap"], [12, 64, 5, 0], [13, 0, 5, 38], [13, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}