{"dependencies": [{"name": "./useEvent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 111}, "end": {"line": 7, "column": 38, "index": 149}}], "key": "agcKO4KjKVVd8qmhkCqgPk8SZT0=", "exportNames": ["*"]}}, {"name": "./useHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 150}, "end": {"line": 8, "column": 42, "index": 192}}], "key": "4fwTVy9JjjGj2GzFTCIyp4pa48c=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedGestureHandler = useAnimatedGestureHandler;\n  var _useEvent = require(_dependencyMap[0], \"./useEvent\");\n  var _useHandler2 = require(_dependencyMap[1], \"./useHandler\");\n  var EVENT_TYPE = {\n    UNDETERMINED: 0,\n    FAILED: 1,\n    BEGAN: 2,\n    CANCELLED: 3,\n    ACTIVE: 4,\n    END: 5\n  };\n\n  // This type comes from React Native Gesture Handler\n  // import type { PanGestureHandlerGestureEvent as DefaultEvent } from 'react-native-gesture-handler';\n  var _worklet_251895770429_init_data = {\n    code: \"function useAnimatedGestureHandlerTs1(e){const{useWeb,EVENT_TYPE,handlers,context}=this.__closure;const event=useWeb?e.nativeEvent:e;if(event.state===EVENT_TYPE.BEGAN&&handlers.onStart){handlers.onStart(event,context);}if(event.state===EVENT_TYPE.ACTIVE&&handlers.onActive){handlers.onActive(event,context);}if(event.oldState===EVENT_TYPE.ACTIVE&&event.state===EVENT_TYPE.END&&handlers.onEnd){handlers.onEnd(event,context);}if(event.oldState===EVENT_TYPE.BEGAN&&event.state===EVENT_TYPE.FAILED&&handlers.onFail){handlers.onFail(event,context);}if(event.oldState===EVENT_TYPE.ACTIVE&&event.state===EVENT_TYPE.CANCELLED&&handlers.onCancel){handlers.onCancel(event,context);}if((event.oldState===EVENT_TYPE.BEGAN||event.oldState===EVENT_TYPE.ACTIVE)&&event.state!==EVENT_TYPE.BEGAN&&event.state!==EVENT_TYPE.ACTIVE&&handlers.onFinish){handlers.onFinish(event,context,event.state===EVENT_TYPE.CANCELLED||event.state===EVENT_TYPE.FAILED);}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\hook\\\\useAnimatedGestureHandler.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useAnimatedGestureHandlerTs1\\\",\\\"e\\\",\\\"useWeb\\\",\\\"EVENT_TYPE\\\",\\\"handlers\\\",\\\"context\\\",\\\"__closure\\\",\\\"event\\\",\\\"nativeEvent\\\",\\\"state\\\",\\\"BEGAN\\\",\\\"onStart\\\",\\\"ACTIVE\\\",\\\"onActive\\\",\\\"oldState\\\",\\\"END\\\",\\\"onEnd\\\",\\\"FAILED\\\",\\\"onFail\\\",\\\"CANCELLED\\\",\\\"onCancel\\\",\\\"onFinish\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/hook/useAnimatedGestureHandler.ts\\\"],\\\"mappings\\\":\\\"AA0FkB,QAAC,CAAAA,4BAAwBA,CAAAC,CAAA,QAAAC,MAAA,CAAAC,UAAA,CAAAC,QAAA,CAAAC,OAAA,OAAAC,SAAA,CAEvC,KAAM,CAAAC,KAAK,CAAGL,MAAM,CAIdD,CAAC,CAAWO,WAAW,CACxBP,CAA4B,CAEjC,GAAIM,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACO,KAAK,EAAIN,QAAQ,CAACO,OAAO,CAAE,CACxDP,QAAQ,CAACO,OAAO,CAACJ,KAAK,CAAEF,OAAO,CAAC,CAClC,CACA,GAAIE,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACS,MAAM,EAAIR,QAAQ,CAACS,QAAQ,CAAE,CAC1DT,QAAQ,CAACS,QAAQ,CAACN,KAAK,CAAEF,OAAO,CAAC,CACnC,CACA,GACEE,KAAK,CAACO,QAAQ,GAAKX,UAAU,CAACS,MAAM,EACpCL,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACY,GAAG,EAC9BX,QAAQ,CAACY,KAAK,CACd,CACAZ,QAAQ,CAACY,KAAK,CAACT,KAAK,CAAEF,OAAO,CAAC,CAChC,CACA,GACEE,KAAK,CAACO,QAAQ,GAAKX,UAAU,CAACO,KAAK,EACnCH,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACc,MAAM,EACjCb,QAAQ,CAACc,MAAM,CACf,CACAd,QAAQ,CAACc,MAAM,CAACX,KAAK,CAAEF,OAAO,CAAC,CACjC,CACA,GACEE,KAAK,CAACO,QAAQ,GAAKX,UAAU,CAACS,MAAM,EACpCL,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACgB,SAAS,EACpCf,QAAQ,CAACgB,QAAQ,CACjB,CACAhB,QAAQ,CAACgB,QAAQ,CAACb,KAAK,CAAEF,OAAO,CAAC,CACnC,CACA,GACE,CAACE,KAAK,CAACO,QAAQ,GAAKX,UAAU,CAACO,KAAK,EAClCH,KAAK,CAACO,QAAQ,GAAKX,UAAU,CAACS,MAAM,GACtCL,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACO,KAAK,EAChCH,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACS,MAAM,EACjCR,QAAQ,CAACiB,QAAQ,CACjB,CACAjB,QAAQ,CAACiB,QAAQ,CACfd,KAAK,CACLF,OAAO,CACPE,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACgB,SAAS,EAClCZ,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACc,MAC/B,CAAC,CACH,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  /**\n   * @deprecated UseAnimatedGestureHandler is an old API which is no longer\n   *   supported.\n   *\n   *   Please check\n   *   https://docs.swmansion.com/react-native-gesture-handler/docs/guides/upgrading-to-2/\n   *   for information about how to migrate to `react-native-gesture-handler` v2\n   */\n  function useAnimatedGestureHandler(handlers, dependencies) {\n    var _useHandler = (0, _useHandler2.useHandler)(handlers, dependencies),\n      context = _useHandler.context,\n      doDependenciesDiffer = _useHandler.doDependenciesDiffer,\n      useWeb = _useHandler.useWeb;\n    var handler = function () {\n      var _e = [new global.Error(), -5, -27];\n      var useAnimatedGestureHandlerTs1 = function (e) {\n        var event = useWeb ?\n        // On Web we get events straight from React Native and they don't have\n        // the `eventName` field there. To simplify the types here we just\n        // cast it as the field was available.\n        e.nativeEvent : e;\n        if (event.state === EVENT_TYPE.BEGAN && handlers.onStart) {\n          handlers.onStart(event, context);\n        }\n        if (event.state === EVENT_TYPE.ACTIVE && handlers.onActive) {\n          handlers.onActive(event, context);\n        }\n        if (event.oldState === EVENT_TYPE.ACTIVE && event.state === EVENT_TYPE.END && handlers.onEnd) {\n          handlers.onEnd(event, context);\n        }\n        if (event.oldState === EVENT_TYPE.BEGAN && event.state === EVENT_TYPE.FAILED && handlers.onFail) {\n          handlers.onFail(event, context);\n        }\n        if (event.oldState === EVENT_TYPE.ACTIVE && event.state === EVENT_TYPE.CANCELLED && handlers.onCancel) {\n          handlers.onCancel(event, context);\n        }\n        if ((event.oldState === EVENT_TYPE.BEGAN || event.oldState === EVENT_TYPE.ACTIVE) && event.state !== EVENT_TYPE.BEGAN && event.state !== EVENT_TYPE.ACTIVE && handlers.onFinish) {\n          handlers.onFinish(event, context, event.state === EVENT_TYPE.CANCELLED || event.state === EVENT_TYPE.FAILED);\n        }\n      };\n      useAnimatedGestureHandlerTs1.__closure = {\n        useWeb,\n        EVENT_TYPE,\n        handlers,\n        context\n      };\n      useAnimatedGestureHandlerTs1.__workletHash = 251895770429;\n      useAnimatedGestureHandlerTs1.__initData = _worklet_251895770429_init_data;\n      useAnimatedGestureHandlerTs1.__stackDetails = _e;\n      return useAnimatedGestureHandlerTs1;\n    }();\n    if (useWeb) {\n      return handler;\n    }\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return (0, _useEvent.useEvent)(handler, ['onGestureHandlerStateChange', 'onGestureHandlerEvent'], doDependenciesDiffer\n    // This is not correct but we want to make GH think it receives a function.\n    );\n  }\n});", "lineCount": 87, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedGestureHandler"], [7, 35, 1, 13], [7, 38, 1, 13, "useAnimatedGestureHandler"], [7, 63, 1, 13], [8, 2, 7, 0], [8, 6, 7, 0, "_useEvent"], [8, 15, 7, 0], [8, 18, 7, 0, "require"], [8, 25, 7, 0], [8, 26, 7, 0, "_dependencyMap"], [8, 40, 7, 0], [9, 2, 8, 0], [9, 6, 8, 0, "_useHandler2"], [9, 18, 8, 0], [9, 21, 8, 0, "require"], [9, 28, 8, 0], [9, 29, 8, 0, "_dependencyMap"], [9, 43, 8, 0], [10, 2, 10, 0], [10, 6, 10, 6, "EVENT_TYPE"], [10, 16, 10, 16], [10, 19, 10, 19], [11, 4, 11, 2, "UNDETERMINED"], [11, 16, 11, 14], [11, 18, 11, 16], [11, 19, 11, 17], [12, 4, 12, 2, "FAILED"], [12, 10, 12, 8], [12, 12, 12, 10], [12, 13, 12, 11], [13, 4, 13, 2, "BEGAN"], [13, 9, 13, 7], [13, 11, 13, 9], [13, 12, 13, 10], [14, 4, 14, 2, "CANCELLED"], [14, 13, 14, 11], [14, 15, 14, 13], [14, 16, 14, 14], [15, 4, 15, 2, "ACTIVE"], [15, 10, 15, 8], [15, 12, 15, 10], [15, 13, 15, 11], [16, 4, 16, 2, "END"], [16, 7, 16, 5], [16, 9, 16, 7], [17, 2, 17, 0], [17, 3, 17, 10], [19, 2, 21, 0], [20, 2, 22, 0], [21, 2, 22, 0], [21, 6, 22, 0, "_worklet_251895770429_init_data"], [21, 37, 22, 0], [22, 4, 22, 0, "code"], [22, 8, 22, 0], [23, 4, 22, 0, "location"], [23, 12, 22, 0], [24, 4, 22, 0, "sourceMap"], [24, 13, 22, 0], [25, 4, 22, 0, "version"], [25, 11, 22, 0], [26, 2, 22, 0], [27, 2, 72, 0], [28, 0, 73, 0], [29, 0, 74, 0], [30, 0, 75, 0], [31, 0, 76, 0], [32, 0, 77, 0], [33, 0, 78, 0], [34, 0, 79, 0], [35, 2, 80, 7], [35, 11, 80, 16, "useAnimatedGestureHandler"], [35, 36, 80, 41, "useAnimatedGestureHandler"], [35, 37, 84, 2, "handlers"], [35, 45, 84, 43], [35, 47, 84, 45, "dependencies"], [35, 59, 84, 74], [35, 61, 84, 76], [36, 4, 87, 2], [36, 8, 87, 2, "_use<PERSON><PERSON>ler"], [36, 19, 87, 2], [36, 22, 87, 52], [36, 26, 87, 52, "useHandler"], [36, 49, 87, 62], [36, 51, 88, 4, "handlers"], [36, 59, 88, 12], [36, 61, 89, 4, "dependencies"], [36, 73, 90, 2], [36, 74, 90, 3], [37, 6, 87, 10, "context"], [37, 13, 87, 17], [37, 16, 87, 17, "_use<PERSON><PERSON>ler"], [37, 27, 87, 17], [37, 28, 87, 10, "context"], [37, 35, 87, 17], [38, 6, 87, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [38, 26, 87, 39], [38, 29, 87, 39, "_use<PERSON><PERSON>ler"], [38, 40, 87, 39], [38, 41, 87, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [38, 61, 87, 39], [39, 6, 87, 41, "useWeb"], [39, 12, 87, 47], [39, 15, 87, 47, "_use<PERSON><PERSON>ler"], [39, 26, 87, 47], [39, 27, 87, 41, "useWeb"], [39, 33, 87, 47], [40, 4, 91, 2], [40, 8, 91, 8, "handler"], [40, 15, 91, 15], [40, 18, 91, 18], [41, 6, 91, 18], [41, 10, 91, 18, "_e"], [41, 12, 91, 18], [41, 20, 91, 18, "global"], [41, 26, 91, 18], [41, 27, 91, 18, "Error"], [41, 32, 91, 18], [42, 6, 91, 18], [42, 10, 91, 18, "useAnimatedGestureHandlerTs1"], [42, 38, 91, 18], [42, 50, 91, 18, "useAnimatedGestureHandlerTs1"], [42, 51, 91, 19, "e"], [42, 52, 91, 38], [42, 54, 91, 43], [43, 8, 93, 4], [43, 12, 93, 10, "event"], [43, 17, 93, 15], [43, 20, 93, 18, "useWeb"], [43, 26, 93, 24], [44, 8, 94, 8], [45, 8, 95, 8], [46, 8, 96, 8], [47, 8, 97, 10, "e"], [47, 9, 97, 11], [47, 10, 97, 22, "nativeEvent"], [47, 21, 97, 33], [47, 24, 98, 9, "e"], [47, 25, 98, 37], [48, 8, 100, 4], [48, 12, 100, 8, "event"], [48, 17, 100, 13], [48, 18, 100, 14, "state"], [48, 23, 100, 19], [48, 28, 100, 24, "EVENT_TYPE"], [48, 38, 100, 34], [48, 39, 100, 35, "BEGAN"], [48, 44, 100, 40], [48, 48, 100, 44, "handlers"], [48, 56, 100, 52], [48, 57, 100, 53, "onStart"], [48, 64, 100, 60], [48, 66, 100, 62], [49, 10, 101, 6, "handlers"], [49, 18, 101, 14], [49, 19, 101, 15, "onStart"], [49, 26, 101, 22], [49, 27, 101, 23, "event"], [49, 32, 101, 28], [49, 34, 101, 30, "context"], [49, 41, 101, 37], [49, 42, 101, 38], [50, 8, 102, 4], [51, 8, 103, 4], [51, 12, 103, 8, "event"], [51, 17, 103, 13], [51, 18, 103, 14, "state"], [51, 23, 103, 19], [51, 28, 103, 24, "EVENT_TYPE"], [51, 38, 103, 34], [51, 39, 103, 35, "ACTIVE"], [51, 45, 103, 41], [51, 49, 103, 45, "handlers"], [51, 57, 103, 53], [51, 58, 103, 54, "onActive"], [51, 66, 103, 62], [51, 68, 103, 64], [52, 10, 104, 6, "handlers"], [52, 18, 104, 14], [52, 19, 104, 15, "onActive"], [52, 27, 104, 23], [52, 28, 104, 24, "event"], [52, 33, 104, 29], [52, 35, 104, 31, "context"], [52, 42, 104, 38], [52, 43, 104, 39], [53, 8, 105, 4], [54, 8, 106, 4], [54, 12, 107, 6, "event"], [54, 17, 107, 11], [54, 18, 107, 12, "oldState"], [54, 26, 107, 20], [54, 31, 107, 25, "EVENT_TYPE"], [54, 41, 107, 35], [54, 42, 107, 36, "ACTIVE"], [54, 48, 107, 42], [54, 52, 108, 6, "event"], [54, 57, 108, 11], [54, 58, 108, 12, "state"], [54, 63, 108, 17], [54, 68, 108, 22, "EVENT_TYPE"], [54, 78, 108, 32], [54, 79, 108, 33, "END"], [54, 82, 108, 36], [54, 86, 109, 6, "handlers"], [54, 94, 109, 14], [54, 95, 109, 15, "onEnd"], [54, 100, 109, 20], [54, 102, 110, 6], [55, 10, 111, 6, "handlers"], [55, 18, 111, 14], [55, 19, 111, 15, "onEnd"], [55, 24, 111, 20], [55, 25, 111, 21, "event"], [55, 30, 111, 26], [55, 32, 111, 28, "context"], [55, 39, 111, 35], [55, 40, 111, 36], [56, 8, 112, 4], [57, 8, 113, 4], [57, 12, 114, 6, "event"], [57, 17, 114, 11], [57, 18, 114, 12, "oldState"], [57, 26, 114, 20], [57, 31, 114, 25, "EVENT_TYPE"], [57, 41, 114, 35], [57, 42, 114, 36, "BEGAN"], [57, 47, 114, 41], [57, 51, 115, 6, "event"], [57, 56, 115, 11], [57, 57, 115, 12, "state"], [57, 62, 115, 17], [57, 67, 115, 22, "EVENT_TYPE"], [57, 77, 115, 32], [57, 78, 115, 33, "FAILED"], [57, 84, 115, 39], [57, 88, 116, 6, "handlers"], [57, 96, 116, 14], [57, 97, 116, 15, "onFail"], [57, 103, 116, 21], [57, 105, 117, 6], [58, 10, 118, 6, "handlers"], [58, 18, 118, 14], [58, 19, 118, 15, "onFail"], [58, 25, 118, 21], [58, 26, 118, 22, "event"], [58, 31, 118, 27], [58, 33, 118, 29, "context"], [58, 40, 118, 36], [58, 41, 118, 37], [59, 8, 119, 4], [60, 8, 120, 4], [60, 12, 121, 6, "event"], [60, 17, 121, 11], [60, 18, 121, 12, "oldState"], [60, 26, 121, 20], [60, 31, 121, 25, "EVENT_TYPE"], [60, 41, 121, 35], [60, 42, 121, 36, "ACTIVE"], [60, 48, 121, 42], [60, 52, 122, 6, "event"], [60, 57, 122, 11], [60, 58, 122, 12, "state"], [60, 63, 122, 17], [60, 68, 122, 22, "EVENT_TYPE"], [60, 78, 122, 32], [60, 79, 122, 33, "CANCELLED"], [60, 88, 122, 42], [60, 92, 123, 6, "handlers"], [60, 100, 123, 14], [60, 101, 123, 15, "onCancel"], [60, 109, 123, 23], [60, 111, 124, 6], [61, 10, 125, 6, "handlers"], [61, 18, 125, 14], [61, 19, 125, 15, "onCancel"], [61, 27, 125, 23], [61, 28, 125, 24, "event"], [61, 33, 125, 29], [61, 35, 125, 31, "context"], [61, 42, 125, 38], [61, 43, 125, 39], [62, 8, 126, 4], [63, 8, 127, 4], [63, 12, 128, 6], [63, 13, 128, 7, "event"], [63, 18, 128, 12], [63, 19, 128, 13, "oldState"], [63, 27, 128, 21], [63, 32, 128, 26, "EVENT_TYPE"], [63, 42, 128, 36], [63, 43, 128, 37, "BEGAN"], [63, 48, 128, 42], [63, 52, 129, 8, "event"], [63, 57, 129, 13], [63, 58, 129, 14, "oldState"], [63, 66, 129, 22], [63, 71, 129, 27, "EVENT_TYPE"], [63, 81, 129, 37], [63, 82, 129, 38, "ACTIVE"], [63, 88, 129, 44], [63, 93, 130, 6, "event"], [63, 98, 130, 11], [63, 99, 130, 12, "state"], [63, 104, 130, 17], [63, 109, 130, 22, "EVENT_TYPE"], [63, 119, 130, 32], [63, 120, 130, 33, "BEGAN"], [63, 125, 130, 38], [63, 129, 131, 6, "event"], [63, 134, 131, 11], [63, 135, 131, 12, "state"], [63, 140, 131, 17], [63, 145, 131, 22, "EVENT_TYPE"], [63, 155, 131, 32], [63, 156, 131, 33, "ACTIVE"], [63, 162, 131, 39], [63, 166, 132, 6, "handlers"], [63, 174, 132, 14], [63, 175, 132, 15, "onFinish"], [63, 183, 132, 23], [63, 185, 133, 6], [64, 10, 134, 6, "handlers"], [64, 18, 134, 14], [64, 19, 134, 15, "onFinish"], [64, 27, 134, 23], [64, 28, 135, 8, "event"], [64, 33, 135, 13], [64, 35, 136, 8, "context"], [64, 42, 136, 15], [64, 44, 137, 8, "event"], [64, 49, 137, 13], [64, 50, 137, 14, "state"], [64, 55, 137, 19], [64, 60, 137, 24, "EVENT_TYPE"], [64, 70, 137, 34], [64, 71, 137, 35, "CANCELLED"], [64, 80, 137, 44], [64, 84, 138, 10, "event"], [64, 89, 138, 15], [64, 90, 138, 16, "state"], [64, 95, 138, 21], [64, 100, 138, 26, "EVENT_TYPE"], [64, 110, 138, 36], [64, 111, 138, 37, "FAILED"], [64, 117, 139, 6], [64, 118, 139, 7], [65, 8, 140, 4], [66, 6, 141, 2], [66, 7, 141, 3], [67, 6, 141, 3, "useAnimatedGestureHandlerTs1"], [67, 34, 141, 3], [67, 35, 141, 3, "__closure"], [67, 44, 141, 3], [68, 8, 141, 3, "useWeb"], [68, 14, 141, 3], [69, 8, 141, 3, "EVENT_TYPE"], [69, 18, 141, 3], [70, 8, 141, 3, "handlers"], [70, 16, 141, 3], [71, 8, 141, 3, "context"], [72, 6, 141, 3], [73, 6, 141, 3, "useAnimatedGestureHandlerTs1"], [73, 34, 141, 3], [73, 35, 141, 3, "__workletHash"], [73, 48, 141, 3], [74, 6, 141, 3, "useAnimatedGestureHandlerTs1"], [74, 34, 141, 3], [74, 35, 141, 3, "__initData"], [74, 45, 141, 3], [74, 48, 141, 3, "_worklet_251895770429_init_data"], [74, 79, 141, 3], [75, 6, 141, 3, "useAnimatedGestureHandlerTs1"], [75, 34, 141, 3], [75, 35, 141, 3, "__stackDetails"], [75, 49, 141, 3], [75, 52, 141, 3, "_e"], [75, 54, 141, 3], [76, 6, 141, 3], [76, 13, 141, 3, "useAnimatedGestureHandlerTs1"], [76, 41, 141, 3], [77, 4, 141, 3], [77, 5, 91, 18], [77, 7, 141, 3], [78, 4, 143, 2], [78, 8, 143, 6, "useWeb"], [78, 14, 143, 12], [78, 16, 143, 14], [79, 6, 144, 4], [79, 13, 144, 11, "handler"], [79, 20, 144, 18], [80, 4, 145, 2], [82, 4, 147, 2], [83, 4, 148, 2], [83, 11, 148, 9], [83, 15, 148, 9, "useEvent"], [83, 33, 148, 17], [83, 35, 149, 4, "handler"], [83, 42, 149, 11], [83, 44, 150, 4], [83, 45, 150, 5], [83, 74, 150, 34], [83, 76, 150, 36], [83, 99, 150, 59], [83, 100, 150, 60], [83, 102, 151, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [84, 4, 152, 4], [85, 4, 153, 2], [85, 5, 153, 3], [86, 2, 154, 0], [87, 0, 154, 1], [87, 3]], "functionMap": {"names": ["<global>", "useAnimatedGestureHandler", "handler"], "mappings": "AAA;OC+E;kBCW;GDkD;CDa"}}, "type": "js/module"}]}