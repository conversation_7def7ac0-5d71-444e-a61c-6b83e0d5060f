{"dependencies": [{"name": "./reanimated<PERSON><PERSON>per", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 49, "index": 49}}], "key": "mbESMMm9nDr2Pm9c9N9QWtHIGDg=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 50}, "end": {"line": 2, "column": 36, "index": 86}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 87}, "end": {"line": 3, "column": 41, "index": 128}}], "key": "ByXat9lt9duIJLDmSeH0V+tRq1s=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.GestureStateManager = void 0;\n  var _reanimatedWrapper = require(_dependencyMap[0], \"./reanimatedWrapper\");\n  var _State = require(_dependencyMap[1], \"../../State\");\n  var _utils = require(_dependencyMap[2], \"../../utils\");\n  var warningMessage = (0, _utils.tagMessage)('react-native-reanimated is required in order to use synchronous state management');\n\n  // Check if reanimated module is available, but look for useSharedValue as conditional\n  // require of reanimated can sometimes return content of `utils.ts` file (?)\n  var REANIMATED_AVAILABLE = _reanimatedWrapper.Reanimated?.useSharedValue !== undefined;\n  var setGestureState = _reanimatedWrapper.Reanimated?.setGestureState;\n  var _worklet_11050783681467_init_data = {\n    code: \"function create_gestureStateManagerTs1(handlerTag){const{REANIMATED_AVAILABLE,setGestureState,State,warningMessage}=this.__closure;return{begin:function(){'worklet';if(REANIMATED_AVAILABLE){setGestureState(handlerTag,State.BEGAN);}else{console.warn(warningMessage);}},activate:function(){'worklet';if(REANIMATED_AVAILABLE){setGestureState(handlerTag,State.ACTIVE);}else{console.warn(warningMessage);}},fail:function(){'worklet';if(REANIMATED_AVAILABLE){setGestureState(handlerTag,State.FAILED);}else{console.warn(warningMessage);}},end:function(){'worklet';if(REANIMATED_AVAILABLE){setGestureState(handlerTag,State.END);}else{console.warn(warningMessage);}}};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\gestureStateManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"create_gestureStateManagerTs1\\\",\\\"handlerTag\\\",\\\"REANIMATED_AVAILABLE\\\",\\\"setGestureState\\\",\\\"State\\\",\\\"warningMessage\\\",\\\"__closure\\\",\\\"begin\\\",\\\"BEGAN\\\",\\\"console\\\",\\\"warn\\\",\\\"activate\\\",\\\"ACTIVE\\\",\\\"fail\\\",\\\"FAILED\\\",\\\"end\\\",\\\"END\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/gestureStateManager.ts\\\"],\\\"mappings\\\":\\\"AAoBA,SAAAA,6BAA6DA,CAAAC,UAAA,QAAAC,oBAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAC,cAAA,OAAAC,SAAA,CAE3D,MAAO,CACLC,KAAK,CAAE,QAAAA,CAAA,CAAM,CACX,SAAS,CACT,GAAIL,oBAAoB,CAAE,CAGxBC,eAAe,CAAEF,UAAU,CAAEG,KAAK,CAACI,KAAK,CAAC,CAC3C,CAAC,IAAM,CACLC,OAAO,CAACC,IAAI,CAACL,cAAc,CAAC,CAC9B,CACF,CAAC,CAEDM,QAAQ,CAAE,QAAAA,CAAA,CAAM,CACd,SAAS,CACT,GAAIT,oBAAoB,CAAE,CAGxBC,eAAe,CAAEF,UAAU,CAAEG,KAAK,CAACQ,MAAM,CAAC,CAC5C,CAAC,IAAM,CACLH,OAAO,CAACC,IAAI,CAACL,cAAc,CAAC,CAC9B,CACF,CAAC,CAEDQ,IAAI,CAAE,QAAAA,CAAA,CAAM,CACV,SAAS,CACT,GAAIX,oBAAoB,CAAE,CAGxBC,eAAe,CAAEF,UAAU,CAAEG,KAAK,CAACU,MAAM,CAAC,CAC5C,CAAC,IAAM,CACLL,OAAO,CAACC,IAAI,CAACL,cAAc,CAAC,CAC9B,CACF,CAAC,CAEDU,GAAG,CAAE,QAAAA,CAAA,CAAM,CACT,SAAS,CACT,GAAIb,oBAAoB,CAAE,CAGxBC,eAAe,CAAEF,UAAU,CAAEG,KAAK,CAACY,GAAG,CAAC,CACzC,CAAC,IAAM,CACLP,OAAO,CAACC,IAAI,CAACL,cAAc,CAAC,CAC9B,CACF,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_442906586055_init_data = {\n    code: \"function gestureStateManagerTs2(){const{REANIMATED_AVAILABLE,setGestureState,handlerTag,State,warningMessage}=this.__closure;if(REANIMATED_AVAILABLE){setGestureState(handlerTag,State.BEGAN);}else{console.warn(warningMessage);}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\gestureStateManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"gestureStateManagerTs2\\\",\\\"REANIMATED_AVAILABLE\\\",\\\"setGestureState\\\",\\\"handlerTag\\\",\\\"State\\\",\\\"warningMessage\\\",\\\"__closure\\\",\\\"BEGAN\\\",\\\"console\\\",\\\"warn\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/gestureStateManager.ts\\\"],\\\"mappings\\\":\\\"AAuBW,SAAAA,sBAAMA,CAAA,QAAAC,oBAAA,CAAAC,eAAA,CAAAC,UAAA,CAAAC,KAAA,CAAAC,cAAA,OAAAC,SAAA,CAEX,GAAIL,oBAAoB,CAAE,CAGxBC,eAAe,CAAEC,UAAU,CAAEC,KAAK,CAACG,KAAK,CAAC,CAC3C,CAAC,IAAM,CACLC,OAAO,CAACC,IAAI,CAACJ,cAAc,CAAC,CAC9B,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_1170647813701_init_data = {\n    code: \"function gestureStateManagerTs3(){const{REANIMATED_AVAILABLE,setGestureState,handlerTag,State,warningMessage}=this.__closure;if(REANIMATED_AVAILABLE){setGestureState(handlerTag,State.ACTIVE);}else{console.warn(warningMessage);}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\gestureStateManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"gestureStateManagerTs3\\\",\\\"REANIMATED_AVAILABLE\\\",\\\"setGestureState\\\",\\\"handlerTag\\\",\\\"State\\\",\\\"warningMessage\\\",\\\"__closure\\\",\\\"ACTIVE\\\",\\\"console\\\",\\\"warn\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/gestureStateManager.ts\\\"],\\\"mappings\\\":\\\"AAkCc,SAAAA,sBAAMA,CAAA,QAAAC,oBAAA,CAAAC,eAAA,CAAAC,UAAA,CAAAC,KAAA,CAAAC,cAAA,OAAAC,SAAA,CAEd,GAAIL,oBAAoB,CAAE,CAGxBC,eAAe,CAAEC,UAAU,CAAEC,KAAK,CAACG,MAAM,CAAC,CAC5C,CAAC,IAAM,CACLC,OAAO,CAACC,IAAI,CAACJ,cAAc,CAAC,CAC9B,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_16847768283629_init_data = {\n    code: \"function gestureStateManagerTs4(){const{REANIMATED_AVAILABLE,setGestureState,handlerTag,State,warningMessage}=this.__closure;if(REANIMATED_AVAILABLE){setGestureState(handlerTag,State.FAILED);}else{console.warn(warningMessage);}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\gestureStateManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"gestureStateManagerTs4\\\",\\\"REANIMATED_AVAILABLE\\\",\\\"setGestureState\\\",\\\"handlerTag\\\",\\\"State\\\",\\\"warningMessage\\\",\\\"__closure\\\",\\\"FAILED\\\",\\\"console\\\",\\\"warn\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/gestureStateManager.ts\\\"],\\\"mappings\\\":\\\"AA6CU,SAAAA,sBAAMA,CAAA,QAAAC,oBAAA,CAAAC,eAAA,CAAAC,UAAA,CAAAC,KAAA,CAAAC,cAAA,OAAAC,SAAA,CAEV,GAAIL,oBAAoB,CAAE,CAGxBC,eAAe,CAAEC,UAAU,CAAEC,KAAK,CAACG,MAAM,CAAC,CAC5C,CAAC,IAAM,CACLC,OAAO,CAACC,IAAI,CAACJ,cAAc,CAAC,CAC9B,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_3049493669888_init_data = {\n    code: \"function gestureStateManagerTs5(){const{REANIMATED_AVAILABLE,setGestureState,handlerTag,State,warningMessage}=this.__closure;if(REANIMATED_AVAILABLE){setGestureState(handlerTag,State.END);}else{console.warn(warningMessage);}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\gestureStateManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"gestureStateManagerTs5\\\",\\\"REANIMATED_AVAILABLE\\\",\\\"setGestureState\\\",\\\"handlerTag\\\",\\\"State\\\",\\\"warningMessage\\\",\\\"__closure\\\",\\\"END\\\",\\\"console\\\",\\\"warn\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/gestureStateManager.ts\\\"],\\\"mappings\\\":\\\"AAwDS,SAAAA,sBAAMA,CAAA,QAAAC,oBAAA,CAAAC,eAAA,CAAAC,UAAA,CAAAC,KAAA,CAAAC,cAAA,OAAAC,SAAA,CAET,GAAIL,oBAAoB,CAAE,CAGxBC,eAAe,CAAEC,UAAU,CAAEC,KAAK,CAACG,GAAG,CAAC,CACzC,CAAC,IAAM,CACLC,OAAO,CAACC,IAAI,CAACJ,cAAc,CAAC,CAC9B,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var create = function () {\n    var _e = [new global.Error(), -5, -27];\n    var create = function (handlerTag) {\n      return {\n        begin: function () {\n          var _e = [new global.Error(), -6, -27];\n          var gestureStateManagerTs2 = function () {\n            if (REANIMATED_AVAILABLE) {\n              // When Reanimated is available, setGestureState should be defined\n              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n              setGestureState(handlerTag, _State.State.BEGAN);\n            } else {\n              console.warn(warningMessage);\n            }\n          };\n          gestureStateManagerTs2.__closure = {\n            REANIMATED_AVAILABLE,\n            setGestureState,\n            handlerTag,\n            State: _State.State,\n            warningMessage\n          };\n          gestureStateManagerTs2.__workletHash = 442906586055;\n          gestureStateManagerTs2.__initData = _worklet_442906586055_init_data;\n          gestureStateManagerTs2.__stackDetails = _e;\n          return gestureStateManagerTs2;\n        }(),\n        activate: function () {\n          var _e = [new global.Error(), -6, -27];\n          var gestureStateManagerTs3 = function () {\n            if (REANIMATED_AVAILABLE) {\n              // When Reanimated is available, setGestureState should be defined\n              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n              setGestureState(handlerTag, _State.State.ACTIVE);\n            } else {\n              console.warn(warningMessage);\n            }\n          };\n          gestureStateManagerTs3.__closure = {\n            REANIMATED_AVAILABLE,\n            setGestureState,\n            handlerTag,\n            State: _State.State,\n            warningMessage\n          };\n          gestureStateManagerTs3.__workletHash = 1170647813701;\n          gestureStateManagerTs3.__initData = _worklet_1170647813701_init_data;\n          gestureStateManagerTs3.__stackDetails = _e;\n          return gestureStateManagerTs3;\n        }(),\n        fail: function () {\n          var _e = [new global.Error(), -6, -27];\n          var gestureStateManagerTs4 = function () {\n            if (REANIMATED_AVAILABLE) {\n              // When Reanimated is available, setGestureState should be defined\n              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n              setGestureState(handlerTag, _State.State.FAILED);\n            } else {\n              console.warn(warningMessage);\n            }\n          };\n          gestureStateManagerTs4.__closure = {\n            REANIMATED_AVAILABLE,\n            setGestureState,\n            handlerTag,\n            State: _State.State,\n            warningMessage\n          };\n          gestureStateManagerTs4.__workletHash = 16847768283629;\n          gestureStateManagerTs4.__initData = _worklet_16847768283629_init_data;\n          gestureStateManagerTs4.__stackDetails = _e;\n          return gestureStateManagerTs4;\n        }(),\n        end: function () {\n          var _e = [new global.Error(), -6, -27];\n          var gestureStateManagerTs5 = function () {\n            if (REANIMATED_AVAILABLE) {\n              // When Reanimated is available, setGestureState should be defined\n              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n              setGestureState(handlerTag, _State.State.END);\n            } else {\n              console.warn(warningMessage);\n            }\n          };\n          gestureStateManagerTs5.__closure = {\n            REANIMATED_AVAILABLE,\n            setGestureState,\n            handlerTag,\n            State: _State.State,\n            warningMessage\n          };\n          gestureStateManagerTs5.__workletHash = 3049493669888;\n          gestureStateManagerTs5.__initData = _worklet_3049493669888_init_data;\n          gestureStateManagerTs5.__stackDetails = _e;\n          return gestureStateManagerTs5;\n        }()\n      };\n    };\n    create.__closure = {\n      REANIMATED_AVAILABLE,\n      setGestureState,\n      State: _State.State,\n      warningMessage\n    };\n    create.__workletHash = 11050783681467;\n    create.__initData = _worklet_11050783681467_init_data;\n    create.__stackDetails = _e;\n    return create;\n  }();\n  var GestureStateManager = exports.GestureStateManager = {\n    create\n  };\n});", "lineCount": 157, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_reanimated<PERSON><PERSON>per"], [6, 24, 1, 0], [6, 27, 1, 0, "require"], [6, 34, 1, 0], [6, 35, 1, 0, "_dependencyMap"], [6, 49, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_State"], [7, 12, 2, 0], [7, 15, 2, 0, "require"], [7, 22, 2, 0], [7, 23, 2, 0, "_dependencyMap"], [7, 37, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_utils"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 12, 0], [9, 6, 12, 6, "warningMessage"], [9, 20, 12, 20], [9, 23, 12, 23], [9, 27, 12, 23, "tagMessage"], [9, 44, 12, 33], [9, 46, 13, 2], [9, 128, 14, 0], [9, 129, 14, 1], [11, 2, 16, 0], [12, 2, 17, 0], [13, 2, 18, 0], [13, 6, 18, 6, "REANIMATED_AVAILABLE"], [13, 26, 18, 26], [13, 29, 18, 29, "Reanimated"], [13, 58, 18, 39], [13, 60, 18, 41, "useSharedValue"], [13, 74, 18, 55], [13, 79, 18, 60, "undefined"], [13, 88, 18, 69], [14, 2, 19, 0], [14, 6, 19, 6, "setGestureState"], [14, 21, 19, 21], [14, 24, 19, 24, "Reanimated"], [14, 53, 19, 34], [14, 55, 19, 36, "setGestureState"], [14, 70, 19, 51], [15, 2, 19, 52], [15, 6, 19, 52, "_worklet_11050783681467_init_data"], [15, 39, 19, 52], [16, 4, 19, 52, "code"], [16, 8, 19, 52], [17, 4, 19, 52, "location"], [17, 12, 19, 52], [18, 4, 19, 52, "sourceMap"], [18, 13, 19, 52], [19, 4, 19, 52, "version"], [19, 11, 19, 52], [20, 2, 19, 52], [21, 2, 19, 52], [21, 6, 19, 52, "_worklet_442906586055_init_data"], [21, 37, 19, 52], [22, 4, 19, 52, "code"], [22, 8, 19, 52], [23, 4, 19, 52, "location"], [23, 12, 19, 52], [24, 4, 19, 52, "sourceMap"], [24, 13, 19, 52], [25, 4, 19, 52, "version"], [25, 11, 19, 52], [26, 2, 19, 52], [27, 2, 19, 52], [27, 6, 19, 52, "_worklet_1170647813701_init_data"], [27, 38, 19, 52], [28, 4, 19, 52, "code"], [28, 8, 19, 52], [29, 4, 19, 52, "location"], [29, 12, 19, 52], [30, 4, 19, 52, "sourceMap"], [30, 13, 19, 52], [31, 4, 19, 52, "version"], [31, 11, 19, 52], [32, 2, 19, 52], [33, 2, 19, 52], [33, 6, 19, 52, "_worklet_16847768283629_init_data"], [33, 39, 19, 52], [34, 4, 19, 52, "code"], [34, 8, 19, 52], [35, 4, 19, 52, "location"], [35, 12, 19, 52], [36, 4, 19, 52, "sourceMap"], [36, 13, 19, 52], [37, 4, 19, 52, "version"], [37, 11, 19, 52], [38, 2, 19, 52], [39, 2, 19, 52], [39, 6, 19, 52, "_worklet_3049493669888_init_data"], [39, 38, 19, 52], [40, 4, 19, 52, "code"], [40, 8, 19, 52], [41, 4, 19, 52, "location"], [41, 12, 19, 52], [42, 4, 19, 52, "sourceMap"], [42, 13, 19, 52], [43, 4, 19, 52, "version"], [43, 11, 19, 52], [44, 2, 19, 52], [45, 2, 19, 52], [45, 6, 19, 52, "create"], [45, 12, 19, 52], [45, 15, 21, 0], [46, 4, 21, 0], [46, 8, 21, 0, "_e"], [46, 10, 21, 0], [46, 18, 21, 0, "global"], [46, 24, 21, 0], [46, 25, 21, 0, "Error"], [46, 30, 21, 0], [47, 4, 21, 0], [47, 8, 21, 0, "create"], [47, 14, 21, 0], [47, 26, 21, 0, "create"], [47, 27, 21, 16, "handlerTag"], [47, 37, 21, 34], [47, 39, 21, 61], [48, 6, 23, 2], [48, 13, 23, 9], [49, 8, 24, 4, "begin"], [49, 13, 24, 9], [49, 15, 24, 11], [50, 10, 24, 11], [50, 14, 24, 11, "_e"], [50, 16, 24, 11], [50, 24, 24, 11, "global"], [50, 30, 24, 11], [50, 31, 24, 11, "Error"], [50, 36, 24, 11], [51, 10, 24, 11], [51, 14, 24, 11, "gestureStateManagerTs2"], [51, 36, 24, 11], [51, 48, 24, 11, "gestureStateManagerTs2"], [51, 49, 24, 11], [51, 51, 24, 17], [52, 12, 26, 6], [52, 16, 26, 10, "REANIMATED_AVAILABLE"], [52, 36, 26, 30], [52, 38, 26, 32], [53, 14, 27, 8], [54, 14, 28, 8], [55, 14, 29, 8, "setGestureState"], [55, 29, 29, 23], [55, 30, 29, 25, "handlerTag"], [55, 40, 29, 35], [55, 42, 29, 37, "State"], [55, 54, 29, 42], [55, 55, 29, 43, "BEGAN"], [55, 60, 29, 48], [55, 61, 29, 49], [56, 12, 30, 6], [56, 13, 30, 7], [56, 19, 30, 13], [57, 14, 31, 8, "console"], [57, 21, 31, 15], [57, 22, 31, 16, "warn"], [57, 26, 31, 20], [57, 27, 31, 21, "warningMessage"], [57, 41, 31, 35], [57, 42, 31, 36], [58, 12, 32, 6], [59, 10, 33, 4], [59, 11, 33, 5], [60, 10, 33, 5, "gestureStateManagerTs2"], [60, 32, 33, 5], [60, 33, 33, 5, "__closure"], [60, 42, 33, 5], [61, 12, 33, 5, "REANIMATED_AVAILABLE"], [61, 32, 33, 5], [62, 12, 33, 5, "setGestureState"], [62, 27, 33, 5], [63, 12, 33, 5, "handlerTag"], [63, 22, 33, 5], [64, 12, 33, 5, "State"], [64, 17, 33, 5], [64, 19, 29, 37, "State"], [64, 31, 29, 42], [65, 12, 29, 42, "warningMessage"], [66, 10, 29, 42], [67, 10, 29, 42, "gestureStateManagerTs2"], [67, 32, 29, 42], [67, 33, 29, 42, "__workletHash"], [67, 46, 29, 42], [68, 10, 29, 42, "gestureStateManagerTs2"], [68, 32, 29, 42], [68, 33, 29, 42, "__initData"], [68, 43, 29, 42], [68, 46, 29, 42, "_worklet_442906586055_init_data"], [68, 77, 29, 42], [69, 10, 29, 42, "gestureStateManagerTs2"], [69, 32, 29, 42], [69, 33, 29, 42, "__stackDetails"], [69, 47, 29, 42], [69, 50, 29, 42, "_e"], [69, 52, 29, 42], [70, 10, 29, 42], [70, 17, 29, 42, "gestureStateManagerTs2"], [70, 39, 29, 42], [71, 8, 29, 42], [71, 9, 24, 11], [71, 11, 33, 5], [72, 8, 35, 4, "activate"], [72, 16, 35, 12], [72, 18, 35, 14], [73, 10, 35, 14], [73, 14, 35, 14, "_e"], [73, 16, 35, 14], [73, 24, 35, 14, "global"], [73, 30, 35, 14], [73, 31, 35, 14, "Error"], [73, 36, 35, 14], [74, 10, 35, 14], [74, 14, 35, 14, "gestureStateManagerTs3"], [74, 36, 35, 14], [74, 48, 35, 14, "gestureStateManagerTs3"], [74, 49, 35, 14], [74, 51, 35, 20], [75, 12, 37, 6], [75, 16, 37, 10, "REANIMATED_AVAILABLE"], [75, 36, 37, 30], [75, 38, 37, 32], [76, 14, 38, 8], [77, 14, 39, 8], [78, 14, 40, 8, "setGestureState"], [78, 29, 40, 23], [78, 30, 40, 25, "handlerTag"], [78, 40, 40, 35], [78, 42, 40, 37, "State"], [78, 54, 40, 42], [78, 55, 40, 43, "ACTIVE"], [78, 61, 40, 49], [78, 62, 40, 50], [79, 12, 41, 6], [79, 13, 41, 7], [79, 19, 41, 13], [80, 14, 42, 8, "console"], [80, 21, 42, 15], [80, 22, 42, 16, "warn"], [80, 26, 42, 20], [80, 27, 42, 21, "warningMessage"], [80, 41, 42, 35], [80, 42, 42, 36], [81, 12, 43, 6], [82, 10, 44, 4], [82, 11, 44, 5], [83, 10, 44, 5, "gestureStateManagerTs3"], [83, 32, 44, 5], [83, 33, 44, 5, "__closure"], [83, 42, 44, 5], [84, 12, 44, 5, "REANIMATED_AVAILABLE"], [84, 32, 44, 5], [85, 12, 44, 5, "setGestureState"], [85, 27, 44, 5], [86, 12, 44, 5, "handlerTag"], [86, 22, 44, 5], [87, 12, 44, 5, "State"], [87, 17, 44, 5], [87, 19, 40, 37, "State"], [87, 31, 40, 42], [88, 12, 40, 42, "warningMessage"], [89, 10, 40, 42], [90, 10, 40, 42, "gestureStateManagerTs3"], [90, 32, 40, 42], [90, 33, 40, 42, "__workletHash"], [90, 46, 40, 42], [91, 10, 40, 42, "gestureStateManagerTs3"], [91, 32, 40, 42], [91, 33, 40, 42, "__initData"], [91, 43, 40, 42], [91, 46, 40, 42, "_worklet_1170647813701_init_data"], [91, 78, 40, 42], [92, 10, 40, 42, "gestureStateManagerTs3"], [92, 32, 40, 42], [92, 33, 40, 42, "__stackDetails"], [92, 47, 40, 42], [92, 50, 40, 42, "_e"], [92, 52, 40, 42], [93, 10, 40, 42], [93, 17, 40, 42, "gestureStateManagerTs3"], [93, 39, 40, 42], [94, 8, 40, 42], [94, 9, 35, 14], [94, 11, 44, 5], [95, 8, 46, 4, "fail"], [95, 12, 46, 8], [95, 14, 46, 10], [96, 10, 46, 10], [96, 14, 46, 10, "_e"], [96, 16, 46, 10], [96, 24, 46, 10, "global"], [96, 30, 46, 10], [96, 31, 46, 10, "Error"], [96, 36, 46, 10], [97, 10, 46, 10], [97, 14, 46, 10, "gestureStateManagerTs4"], [97, 36, 46, 10], [97, 48, 46, 10, "gestureStateManagerTs4"], [97, 49, 46, 10], [97, 51, 46, 16], [98, 12, 48, 6], [98, 16, 48, 10, "REANIMATED_AVAILABLE"], [98, 36, 48, 30], [98, 38, 48, 32], [99, 14, 49, 8], [100, 14, 50, 8], [101, 14, 51, 8, "setGestureState"], [101, 29, 51, 23], [101, 30, 51, 25, "handlerTag"], [101, 40, 51, 35], [101, 42, 51, 37, "State"], [101, 54, 51, 42], [101, 55, 51, 43, "FAILED"], [101, 61, 51, 49], [101, 62, 51, 50], [102, 12, 52, 6], [102, 13, 52, 7], [102, 19, 52, 13], [103, 14, 53, 8, "console"], [103, 21, 53, 15], [103, 22, 53, 16, "warn"], [103, 26, 53, 20], [103, 27, 53, 21, "warningMessage"], [103, 41, 53, 35], [103, 42, 53, 36], [104, 12, 54, 6], [105, 10, 55, 4], [105, 11, 55, 5], [106, 10, 55, 5, "gestureStateManagerTs4"], [106, 32, 55, 5], [106, 33, 55, 5, "__closure"], [106, 42, 55, 5], [107, 12, 55, 5, "REANIMATED_AVAILABLE"], [107, 32, 55, 5], [108, 12, 55, 5, "setGestureState"], [108, 27, 55, 5], [109, 12, 55, 5, "handlerTag"], [109, 22, 55, 5], [110, 12, 55, 5, "State"], [110, 17, 55, 5], [110, 19, 51, 37, "State"], [110, 31, 51, 42], [111, 12, 51, 42, "warningMessage"], [112, 10, 51, 42], [113, 10, 51, 42, "gestureStateManagerTs4"], [113, 32, 51, 42], [113, 33, 51, 42, "__workletHash"], [113, 46, 51, 42], [114, 10, 51, 42, "gestureStateManagerTs4"], [114, 32, 51, 42], [114, 33, 51, 42, "__initData"], [114, 43, 51, 42], [114, 46, 51, 42, "_worklet_16847768283629_init_data"], [114, 79, 51, 42], [115, 10, 51, 42, "gestureStateManagerTs4"], [115, 32, 51, 42], [115, 33, 51, 42, "__stackDetails"], [115, 47, 51, 42], [115, 50, 51, 42, "_e"], [115, 52, 51, 42], [116, 10, 51, 42], [116, 17, 51, 42, "gestureStateManagerTs4"], [116, 39, 51, 42], [117, 8, 51, 42], [117, 9, 46, 10], [117, 11, 55, 5], [118, 8, 57, 4, "end"], [118, 11, 57, 7], [118, 13, 57, 9], [119, 10, 57, 9], [119, 14, 57, 9, "_e"], [119, 16, 57, 9], [119, 24, 57, 9, "global"], [119, 30, 57, 9], [119, 31, 57, 9, "Error"], [119, 36, 57, 9], [120, 10, 57, 9], [120, 14, 57, 9, "gestureStateManagerTs5"], [120, 36, 57, 9], [120, 48, 57, 9, "gestureStateManagerTs5"], [120, 49, 57, 9], [120, 51, 57, 15], [121, 12, 59, 6], [121, 16, 59, 10, "REANIMATED_AVAILABLE"], [121, 36, 59, 30], [121, 38, 59, 32], [122, 14, 60, 8], [123, 14, 61, 8], [124, 14, 62, 8, "setGestureState"], [124, 29, 62, 23], [124, 30, 62, 25, "handlerTag"], [124, 40, 62, 35], [124, 42, 62, 37, "State"], [124, 54, 62, 42], [124, 55, 62, 43, "END"], [124, 58, 62, 46], [124, 59, 62, 47], [125, 12, 63, 6], [125, 13, 63, 7], [125, 19, 63, 13], [126, 14, 64, 8, "console"], [126, 21, 64, 15], [126, 22, 64, 16, "warn"], [126, 26, 64, 20], [126, 27, 64, 21, "warningMessage"], [126, 41, 64, 35], [126, 42, 64, 36], [127, 12, 65, 6], [128, 10, 66, 4], [128, 11, 66, 5], [129, 10, 66, 5, "gestureStateManagerTs5"], [129, 32, 66, 5], [129, 33, 66, 5, "__closure"], [129, 42, 66, 5], [130, 12, 66, 5, "REANIMATED_AVAILABLE"], [130, 32, 66, 5], [131, 12, 66, 5, "setGestureState"], [131, 27, 66, 5], [132, 12, 66, 5, "handlerTag"], [132, 22, 66, 5], [133, 12, 66, 5, "State"], [133, 17, 66, 5], [133, 19, 62, 37, "State"], [133, 31, 62, 42], [134, 12, 62, 42, "warningMessage"], [135, 10, 62, 42], [136, 10, 62, 42, "gestureStateManagerTs5"], [136, 32, 62, 42], [136, 33, 62, 42, "__workletHash"], [136, 46, 62, 42], [137, 10, 62, 42, "gestureStateManagerTs5"], [137, 32, 62, 42], [137, 33, 62, 42, "__initData"], [137, 43, 62, 42], [137, 46, 62, 42, "_worklet_3049493669888_init_data"], [137, 78, 62, 42], [138, 10, 62, 42, "gestureStateManagerTs5"], [138, 32, 62, 42], [138, 33, 62, 42, "__stackDetails"], [138, 47, 62, 42], [138, 50, 62, 42, "_e"], [138, 52, 62, 42], [139, 10, 62, 42], [139, 17, 62, 42, "gestureStateManagerTs5"], [139, 39, 62, 42], [140, 8, 62, 42], [140, 9, 57, 9], [141, 6, 67, 2], [141, 7, 67, 3], [142, 4, 68, 0], [142, 5, 68, 1], [143, 4, 68, 1, "create"], [143, 10, 68, 1], [143, 11, 68, 1, "__closure"], [143, 20, 68, 1], [144, 6, 68, 1, "REANIMATED_AVAILABLE"], [144, 26, 68, 1], [145, 6, 68, 1, "setGestureState"], [145, 21, 68, 1], [146, 6, 68, 1, "State"], [146, 11, 68, 1], [146, 13, 29, 37, "State"], [146, 25, 29, 42], [147, 6, 29, 42, "warningMessage"], [148, 4, 29, 42], [149, 4, 29, 42, "create"], [149, 10, 29, 42], [149, 11, 29, 42, "__workletHash"], [149, 24, 29, 42], [150, 4, 29, 42, "create"], [150, 10, 29, 42], [150, 11, 29, 42, "__initData"], [150, 21, 29, 42], [150, 24, 29, 42, "_worklet_11050783681467_init_data"], [150, 57, 29, 42], [151, 4, 29, 42, "create"], [151, 10, 29, 42], [151, 11, 29, 42, "__stackDetails"], [151, 25, 29, 42], [151, 28, 29, 42, "_e"], [151, 30, 29, 42], [152, 4, 29, 42], [152, 11, 29, 42, "create"], [152, 17, 29, 42], [153, 2, 29, 42], [153, 3, 21, 0], [154, 2, 70, 7], [154, 6, 70, 13, "GestureStateManager"], [154, 25, 70, 32], [154, 28, 70, 32, "exports"], [154, 35, 70, 32], [154, 36, 70, 32, "GestureStateManager"], [154, 55, 70, 32], [154, 58, 70, 35], [155, 4, 71, 2, "create"], [156, 2, 72, 0], [156, 3, 72, 1], [157, 0, 72, 2], [157, 3]], "functionMap": {"names": ["<global>", "create", "begin", "activate", "fail", "end"], "mappings": "AAA;ACoB;WCG;KDS;cEE;KFS;UGE;KHS;SIE;KJS;CDE"}}, "type": "js/module"}]}