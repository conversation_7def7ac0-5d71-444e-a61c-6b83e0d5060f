{"dependencies": [{"name": "../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 55, "index": 55}}], "key": "ByXat9lt9duIJLDmSeH0V+tRq1s=", "exportNames": ["*"]}}, {"name": "../../../mountRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 56}, "end": {"line": 2, "column": 55, "index": 111}}], "key": "ZDu7aL2iuT3Od7iyX13y9sY9XZQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 160}, "end": {"line": 4, "column": 34, "index": 194}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useMountReactions = useMountReactions;\n  var _utils = require(_dependencyMap[0], \"../../utils\");\n  var _mountRegistry = require(_dependencyMap[1], \"../../../mountRegistry\");\n  var _react = require(_dependencyMap[2], \"react\");\n  function shouldUpdateDetector(relation, gesture) {\n    if (relation === undefined) {\n      return false;\n    }\n    for (var tag of (0, _utils.transformIntoHandlerTags)(relation)) {\n      if (tag === gesture.handlerTag) {\n        return true;\n      }\n    }\n    return false;\n  }\n  function useMountReactions(updateDetector, state) {\n    (0, _react.useEffect)(() => {\n      return _mountRegistry.MountRegistry.addMountListener(gesture => {\n        // At this point the ref in the gesture config should be updated, so we can check if one of the gestures\n        // set in a relation with the gesture got mounted. If so, we need to update the detector to propagate\n        // the changes to the native side.\n        for (var attachedGesture of state.attachedGestures) {\n          var blocksHandlers = attachedGesture.config.blocksHandlers;\n          var requireToFail = attachedGesture.config.requireToFail;\n          var simultaneousWith = attachedGesture.config.simultaneousWith;\n          if (shouldUpdateDetector(blocksHandlers, gesture) || shouldUpdateDetector(requireToFail, gesture) || shouldUpdateDetector(simultaneousWith, gesture)) {\n            updateDetector();\n\n            // We can safely return here, if any other gestures should be updated, they will be by the above call\n            return;\n          }\n        }\n      });\n    }, [updateDetector, state]);\n  }\n});", "lineCount": 40, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_utils"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_mountRegistry"], [7, 20, 2, 0], [7, 23, 2, 0, "require"], [7, 30, 2, 0], [7, 31, 2, 0, "_dependencyMap"], [7, 45, 2, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_react"], [8, 12, 4, 0], [8, 15, 4, 0, "require"], [8, 22, 4, 0], [8, 23, 4, 0, "_dependencyMap"], [8, 37, 4, 0], [9, 2, 7, 0], [9, 11, 7, 9, "shouldUpdateDetector"], [9, 31, 7, 29, "shouldUpdateDetector"], [9, 32, 8, 2, "relation"], [9, 40, 8, 36], [9, 42, 9, 2, "gesture"], [9, 49, 9, 33], [9, 51, 10, 2], [10, 4, 11, 2], [10, 8, 11, 6, "relation"], [10, 16, 11, 14], [10, 21, 11, 19, "undefined"], [10, 30, 11, 28], [10, 32, 11, 30], [11, 6, 12, 4], [11, 13, 12, 11], [11, 18, 12, 16], [12, 4, 13, 2], [13, 4, 15, 2], [13, 9, 15, 7], [13, 13, 15, 13, "tag"], [13, 16, 15, 16], [13, 20, 15, 20], [13, 24, 15, 20, "transformIntoHandlerTags"], [13, 55, 15, 44], [13, 57, 15, 45, "relation"], [13, 65, 15, 53], [13, 66, 15, 54], [13, 68, 15, 56], [14, 6, 16, 4], [14, 10, 16, 8, "tag"], [14, 13, 16, 11], [14, 18, 16, 16, "gesture"], [14, 25, 16, 23], [14, 26, 16, 24, "handlerTag"], [14, 36, 16, 34], [14, 38, 16, 36], [15, 8, 17, 6], [15, 15, 17, 13], [15, 19, 17, 17], [16, 6, 18, 4], [17, 4, 19, 2], [18, 4, 21, 2], [18, 11, 21, 9], [18, 16, 21, 14], [19, 2, 22, 0], [20, 2, 24, 7], [20, 11, 24, 16, "useMountReactions"], [20, 28, 24, 33, "useMountReactions"], [20, 29, 25, 2, "updateDetector"], [20, 43, 25, 28], [20, 45, 26, 2, "state"], [20, 50, 26, 29], [20, 52, 27, 2], [21, 4, 28, 2], [21, 8, 28, 2, "useEffect"], [21, 24, 28, 11], [21, 26, 28, 12], [21, 32, 28, 18], [22, 6, 29, 4], [22, 13, 29, 11, "MountRegistry"], [22, 41, 29, 24], [22, 42, 29, 25, "addMountListener"], [22, 58, 29, 41], [22, 59, 29, 43, "gesture"], [22, 66, 29, 50], [22, 70, 29, 55], [23, 8, 30, 6], [24, 8, 31, 6], [25, 8, 32, 6], [26, 8, 33, 6], [26, 13, 33, 11], [26, 17, 33, 17, "attachedGesture"], [26, 32, 33, 32], [26, 36, 33, 36, "state"], [26, 41, 33, 41], [26, 42, 33, 42, "attachedGestures"], [26, 58, 33, 58], [26, 60, 33, 60], [27, 10, 34, 8], [27, 14, 34, 14, "blocksHandlers"], [27, 28, 34, 28], [27, 31, 34, 31, "attachedGesture"], [27, 46, 34, 46], [27, 47, 34, 47, "config"], [27, 53, 34, 53], [27, 54, 34, 54, "blocksHandlers"], [27, 68, 34, 68], [28, 10, 35, 8], [28, 14, 35, 14, "requireToFail"], [28, 27, 35, 27], [28, 30, 35, 30, "attachedGesture"], [28, 45, 35, 45], [28, 46, 35, 46, "config"], [28, 52, 35, 52], [28, 53, 35, 53, "requireToFail"], [28, 66, 35, 66], [29, 10, 36, 8], [29, 14, 36, 14, "simultaneousWith"], [29, 30, 36, 30], [29, 33, 36, 33, "attachedGesture"], [29, 48, 36, 48], [29, 49, 36, 49, "config"], [29, 55, 36, 55], [29, 56, 36, 56, "simultaneousWith"], [29, 72, 36, 72], [30, 10, 38, 8], [30, 14, 39, 10, "shouldUpdateDetector"], [30, 34, 39, 30], [30, 35, 39, 31, "blocksHandlers"], [30, 49, 39, 45], [30, 51, 39, 47, "gesture"], [30, 58, 39, 54], [30, 59, 39, 55], [30, 63, 40, 10, "shouldUpdateDetector"], [30, 83, 40, 30], [30, 84, 40, 31, "requireToFail"], [30, 97, 40, 44], [30, 99, 40, 46, "gesture"], [30, 106, 40, 53], [30, 107, 40, 54], [30, 111, 41, 10, "shouldUpdateDetector"], [30, 131, 41, 30], [30, 132, 41, 31, "simultaneousWith"], [30, 148, 41, 47], [30, 150, 41, 49, "gesture"], [30, 157, 41, 56], [30, 158, 41, 57], [30, 160, 42, 10], [31, 12, 43, 10, "updateDetector"], [31, 26, 43, 24], [31, 27, 43, 25], [31, 28, 43, 26], [33, 12, 45, 10], [34, 12, 46, 10], [35, 10, 47, 8], [36, 8, 48, 6], [37, 6, 49, 4], [37, 7, 49, 5], [37, 8, 49, 6], [38, 4, 50, 2], [38, 5, 50, 3], [38, 7, 50, 5], [38, 8, 50, 6, "updateDetector"], [38, 22, 50, 20], [38, 24, 50, 22, "state"], [38, 29, 50, 27], [38, 30, 50, 28], [38, 31, 50, 29], [39, 2, 51, 0], [40, 0, 51, 1], [40, 3]], "functionMap": {"names": ["<global>", "shouldUpdateDetector", "useMountReactions", "useEffect$argument_0", "MountRegistry.addMountListener$argument_0"], "mappings": "AAA;ACM;CDe;OEE;YCI;0CCC;KDoB;GDC;CFC"}}, "type": "js/module"}]}