{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Renderer/shims/ReactNative", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 77}, "end": {"line": 3, "column": 90, "index": 167}}], "key": "9HB9IIFOy7Q1h9amV0y0V6GxhGw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"RNRenderer\", {\n    enumerable: true,\n    get: function () {\n      return _ReactNative.default;\n    }\n  });\n  var _ReactNative = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Renderer/shims/ReactNative\"));\n});", "lineCount": 13, "map": [[12, 2, 3, 0], [12, 6, 3, 0, "_ReactNative"], [12, 18, 3, 0], [12, 21, 3, 0, "_interopRequireDefault"], [12, 43, 3, 0], [12, 44, 3, 0, "require"], [12, 51, 3, 0], [12, 52, 3, 0, "_dependencyMap"], [12, 66, 3, 0], [13, 0, 3, 90], [13, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}