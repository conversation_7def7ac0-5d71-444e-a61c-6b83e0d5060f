{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 16, "index": 130}, "end": {"line": 4, "column": 32, "index": 146}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-is", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 19, "index": 167}, "end": {"line": 5, "column": 38, "index": 186}}], "key": "hoZupclAije+HbYquo78nDVN6Z8=", "exportNames": ["*"]}}, {"name": "../utils/selector", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 19, "index": 207}, "end": {"line": 6, "column": 47, "index": 235}}], "key": "7YgKqtu3BoMZhI8fW1WyoV3whVo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withStyledChildren = void 0;\n  var react_1 = require(_dependencyMap[0], \"react\");\n  var react_is_1 = require(_dependencyMap[1], \"react-is\");\n  var selector_1 = require(_dependencyMap[2], \"../utils/selector\");\n  function withStyledChildren(_ref) {\n    var componentChildren = _ref.componentChildren,\n      componentState = _ref.componentState,\n      mask = _ref.mask,\n      store = _ref.store,\n      stylesArray = _ref.stylesArray;\n    var isParent = (0, selector_1.matchesMask)(mask, selector_1.PARENT);\n    if (!stylesArray.childClassNames && !isParent) {\n      return componentChildren;\n    }\n    var children = (0, react_is_1.isFragment)(componentChildren) ?\n    // This probably needs to be recursive\n    componentChildren.props.children : componentChildren;\n    return react_1.Children.toArray(children).filter(Boolean).map((child, index) => {\n      if (!(0, react_1.isValidElement)(child)) {\n        return child;\n      }\n      var style = store.getChildStyles(stylesArray, {\n        nthChild: index + 1,\n        parentHover: componentState.hover,\n        parentFocus: componentState.focus,\n        parentActive: componentState.active\n      });\n      if (!style || style.length === 0) {\n        return child;\n      }\n      return child.props.style ? (0, react_1.cloneElement)(child, {\n        style: [child.props.style, style]\n      }) : (0, react_1.cloneElement)(child, {\n        style\n      });\n    });\n  }\n  exports.withStyledChildren = withStyledChildren;\n});", "lineCount": 45, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [7, 28, 3, 26], [7, 31, 3, 29], [7, 36, 3, 34], [7, 37, 3, 35], [8, 2, 4, 0], [8, 6, 4, 6, "react_1"], [8, 13, 4, 13], [8, 16, 4, 16, "require"], [8, 23, 4, 23], [8, 24, 4, 23, "_dependencyMap"], [8, 38, 4, 23], [8, 50, 4, 31], [8, 51, 4, 32], [9, 2, 5, 0], [9, 6, 5, 6, "react_is_1"], [9, 16, 5, 16], [9, 19, 5, 19, "require"], [9, 26, 5, 26], [9, 27, 5, 26, "_dependencyMap"], [9, 41, 5, 26], [9, 56, 5, 37], [9, 57, 5, 38], [10, 2, 6, 0], [10, 6, 6, 6, "selector_1"], [10, 16, 6, 16], [10, 19, 6, 19, "require"], [10, 26, 6, 26], [10, 27, 6, 26, "_dependencyMap"], [10, 41, 6, 26], [10, 65, 6, 46], [10, 66, 6, 47], [11, 2, 7, 0], [11, 11, 7, 9, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 29, 7, 27, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 30, 7, 27, "_ref"], [11, 34, 7, 27], [11, 36, 7, 94], [12, 4, 7, 94], [12, 8, 7, 30, "componentChildren"], [12, 25, 7, 47], [12, 28, 7, 47, "_ref"], [12, 32, 7, 47], [12, 33, 7, 30, "componentChildren"], [12, 50, 7, 47], [13, 6, 7, 49, "componentState"], [13, 20, 7, 63], [13, 23, 7, 63, "_ref"], [13, 27, 7, 63], [13, 28, 7, 49, "componentState"], [13, 42, 7, 63], [14, 6, 7, 65, "mask"], [14, 10, 7, 69], [14, 13, 7, 69, "_ref"], [14, 17, 7, 69], [14, 18, 7, 65, "mask"], [14, 22, 7, 69], [15, 6, 7, 71, "store"], [15, 11, 7, 76], [15, 14, 7, 76, "_ref"], [15, 18, 7, 76], [15, 19, 7, 71, "store"], [15, 24, 7, 76], [16, 6, 7, 78, "stylesArray"], [16, 17, 7, 89], [16, 20, 7, 89, "_ref"], [16, 24, 7, 89], [16, 25, 7, 78, "stylesArray"], [16, 36, 7, 89], [17, 4, 8, 4], [17, 8, 8, 10, "isParent"], [17, 16, 8, 18], [17, 19, 8, 21], [17, 20, 8, 22], [17, 21, 8, 23], [17, 23, 8, 25, "selector_1"], [17, 33, 8, 35], [17, 34, 8, 36, "matchesMask"], [17, 45, 8, 47], [17, 47, 8, 49, "mask"], [17, 51, 8, 53], [17, 53, 8, 55, "selector_1"], [17, 63, 8, 65], [17, 64, 8, 66, "PARENT"], [17, 70, 8, 72], [17, 71, 8, 73], [18, 4, 9, 4], [18, 8, 9, 8], [18, 9, 9, 9, "stylesArray"], [18, 20, 9, 20], [18, 21, 9, 21, "childClassNames"], [18, 36, 9, 36], [18, 40, 9, 40], [18, 41, 9, 41, "isParent"], [18, 49, 9, 49], [18, 51, 9, 51], [19, 6, 10, 8], [19, 13, 10, 15, "componentChildren"], [19, 30, 10, 32], [20, 4, 11, 4], [21, 4, 12, 4], [21, 8, 12, 10, "children"], [21, 16, 12, 18], [21, 19, 12, 21], [21, 20, 12, 22], [21, 21, 12, 23], [21, 23, 12, 25, "react_is_1"], [21, 33, 12, 35], [21, 34, 12, 36, "isFragment"], [21, 44, 12, 46], [21, 46, 12, 48, "componentChildren"], [21, 63, 12, 65], [21, 64, 12, 66], [22, 4, 13, 10], [23, 4, 14, 12, "componentChildren"], [23, 21, 14, 29], [23, 22, 14, 30, "props"], [23, 27, 14, 35], [23, 28, 14, 36, "children"], [23, 36, 14, 44], [23, 39, 15, 10, "componentChildren"], [23, 56, 15, 27], [24, 4, 16, 4], [24, 11, 16, 11, "react_1"], [24, 18, 16, 18], [24, 19, 16, 19, "Children"], [24, 27, 16, 27], [24, 28, 16, 28, "toArray"], [24, 35, 16, 35], [24, 36, 16, 36, "children"], [24, 44, 16, 44], [24, 45, 16, 45], [24, 46, 17, 9, "filter"], [24, 52, 17, 15], [24, 53, 17, 16, "Boolean"], [24, 60, 17, 23], [24, 61, 17, 24], [24, 62, 18, 9, "map"], [24, 65, 18, 12], [24, 66, 18, 13], [24, 67, 18, 14, "child"], [24, 72, 18, 19], [24, 74, 18, 21, "index"], [24, 79, 18, 26], [24, 84, 18, 31], [25, 6, 19, 8], [25, 10, 19, 12], [25, 11, 19, 13], [25, 12, 19, 14], [25, 13, 19, 15], [25, 15, 19, 17, "react_1"], [25, 22, 19, 24], [25, 23, 19, 25, "isValidElement"], [25, 37, 19, 39], [25, 39, 19, 41, "child"], [25, 44, 19, 46], [25, 45, 19, 47], [25, 47, 19, 49], [26, 8, 20, 12], [26, 15, 20, 19, "child"], [26, 20, 20, 24], [27, 6, 21, 8], [28, 6, 22, 8], [28, 10, 22, 14, "style"], [28, 15, 22, 19], [28, 18, 22, 22, "store"], [28, 23, 22, 27], [28, 24, 22, 28, "get<PERSON><PERSON>d<PERSON><PERSON>les"], [28, 38, 22, 42], [28, 39, 22, 43, "stylesArray"], [28, 50, 22, 54], [28, 52, 22, 56], [29, 8, 23, 12, "nthChild"], [29, 16, 23, 20], [29, 18, 23, 22, "index"], [29, 23, 23, 27], [29, 26, 23, 30], [29, 27, 23, 31], [30, 8, 24, 12, "parentHover"], [30, 19, 24, 23], [30, 21, 24, 25, "componentState"], [30, 35, 24, 39], [30, 36, 24, 40, "hover"], [30, 41, 24, 45], [31, 8, 25, 12, "parentFocus"], [31, 19, 25, 23], [31, 21, 25, 25, "componentState"], [31, 35, 25, 39], [31, 36, 25, 40, "focus"], [31, 41, 25, 45], [32, 8, 26, 12, "parentActive"], [32, 20, 26, 24], [32, 22, 26, 26, "componentState"], [32, 36, 26, 40], [32, 37, 26, 41, "active"], [33, 6, 27, 8], [33, 7, 27, 9], [33, 8, 27, 10], [34, 6, 28, 8], [34, 10, 28, 12], [34, 11, 28, 13, "style"], [34, 16, 28, 18], [34, 20, 28, 22, "style"], [34, 25, 28, 27], [34, 26, 28, 28, "length"], [34, 32, 28, 34], [34, 37, 28, 39], [34, 38, 28, 40], [34, 40, 28, 42], [35, 8, 29, 12], [35, 15, 29, 19, "child"], [35, 20, 29, 24], [36, 6, 30, 8], [37, 6, 31, 8], [37, 13, 31, 15, "child"], [37, 18, 31, 20], [37, 19, 31, 21, "props"], [37, 24, 31, 26], [37, 25, 31, 27, "style"], [37, 30, 31, 32], [37, 33, 32, 14], [37, 34, 32, 15], [37, 35, 32, 16], [37, 37, 32, 18, "react_1"], [37, 44, 32, 25], [37, 45, 32, 26, "cloneElement"], [37, 57, 32, 38], [37, 59, 32, 40, "child"], [37, 64, 32, 45], [37, 66, 32, 47], [38, 8, 32, 49, "style"], [38, 13, 32, 54], [38, 15, 32, 56], [38, 16, 32, 57, "child"], [38, 21, 32, 62], [38, 22, 32, 63, "props"], [38, 27, 32, 68], [38, 28, 32, 69, "style"], [38, 33, 32, 74], [38, 35, 32, 76, "style"], [38, 40, 32, 81], [39, 6, 32, 83], [39, 7, 32, 84], [39, 8, 32, 85], [39, 11, 33, 14], [39, 12, 33, 15], [39, 13, 33, 16], [39, 15, 33, 18, "react_1"], [39, 22, 33, 25], [39, 23, 33, 26, "cloneElement"], [39, 35, 33, 38], [39, 37, 33, 40, "child"], [39, 42, 33, 45], [39, 44, 33, 47], [40, 8, 33, 49, "style"], [41, 6, 33, 55], [41, 7, 33, 56], [41, 8, 33, 57], [42, 4, 34, 4], [42, 5, 34, 5], [42, 6, 34, 6], [43, 2, 35, 0], [44, 2, 36, 0, "exports"], [44, 9, 36, 7], [44, 10, 36, 8, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [44, 28, 36, 26], [44, 31, 36, 29, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [44, 49, 36, 47], [45, 0, 36, 48], [45, 3]], "functionMap": {"names": ["<global>", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "react_1.Children.toArray.filter.map$argument_0"], "mappings": "AAA;ACM;aCW;KDgB;CDC"}}, "type": "js/module"}]}