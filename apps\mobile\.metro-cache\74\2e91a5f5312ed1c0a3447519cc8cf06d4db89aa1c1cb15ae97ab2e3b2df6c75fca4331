{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isDOMAvailable = exports.isAsyncDebugging = exports.canUseViewport = exports.canUseEventListeners = void 0;\n  // In standard node environments there is no DOM API\n  var isDOMAvailable = exports.isDOMAvailable = false;\n  var canUseEventListeners = exports.canUseEventListeners = false;\n  var canUseViewport = exports.canUseViewport = false;\n  var isAsyncDebugging = exports.isAsyncDebugging = false;\n  if (__DEV__) {\n    // These native globals are injected by native React runtimes and not standard browsers\n    // we can use them to determine if the JS is being executed in Chrome.\n    exports.isAsyncDebugging = isAsyncDebugging = !global.nativeExtensions && !global.nativeCallSyncHook && !global.RN$Bridgeless;\n  }\n});", "lineCount": 16, "map": [[6, 2, 3, 0], [7, 2, 4, 7], [7, 6, 4, 13, "isDOMAvailable"], [7, 20, 4, 27], [7, 23, 4, 27, "exports"], [7, 30, 4, 27], [7, 31, 4, 27, "isDOMAvailable"], [7, 45, 4, 27], [7, 48, 4, 30], [7, 53, 4, 35], [8, 2, 5, 7], [8, 6, 5, 13, "canUseEventListeners"], [8, 26, 5, 33], [8, 29, 5, 33, "exports"], [8, 36, 5, 33], [8, 37, 5, 33, "canUseEventListeners"], [8, 57, 5, 33], [8, 60, 5, 36], [8, 65, 5, 41], [9, 2, 6, 7], [9, 6, 6, 13, "canUseViewport"], [9, 20, 6, 27], [9, 23, 6, 27, "exports"], [9, 30, 6, 27], [9, 31, 6, 27, "canUseViewport"], [9, 45, 6, 27], [9, 48, 6, 30], [9, 53, 6, 35], [10, 2, 8, 7], [10, 6, 8, 11, "isAsyncDebugging"], [10, 22, 8, 36], [10, 25, 8, 36, "exports"], [10, 32, 8, 36], [10, 33, 8, 36, "isAsyncDebugging"], [10, 49, 8, 36], [10, 52, 8, 39], [10, 57, 8, 44], [11, 2, 10, 0], [11, 6, 10, 4, "__DEV__"], [11, 13, 10, 11], [11, 15, 10, 13], [12, 4, 11, 2], [13, 4, 12, 2], [14, 4, 13, 2, "exports"], [14, 11, 13, 2], [14, 12, 13, 2, "isAsyncDebugging"], [14, 28, 13, 2], [14, 31, 13, 2, "isAsyncDebugging"], [14, 47, 13, 18], [14, 50, 14, 4], [14, 51, 14, 5, "global"], [14, 57, 14, 11], [14, 58, 14, 12, "nativeExtensions"], [14, 74, 14, 28], [14, 78, 14, 32], [14, 79, 14, 33, "global"], [14, 85, 14, 39], [14, 86, 14, 40, "nativeCallSyncHook"], [14, 104, 14, 58], [14, 108, 14, 62], [14, 109, 14, 63, "global"], [14, 115, 14, 69], [14, 116, 14, 70, "RN$Bridgeless"], [14, 129, 14, 83], [15, 2, 15, 0], [16, 0, 15, 1], [16, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}