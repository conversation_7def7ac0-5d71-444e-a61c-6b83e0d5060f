{"dependencies": [{"name": "../../src/private/featureflags/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 98}}], "key": "fdTx5edELD8GYD7vaakWfKKte1Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.stringifyValidationResult = stringifyValidationResult;\n  exports.validate = validate;\n  var ReactNativeFeatureFlags = _interopRequireWildcard(require(_dependencyMap[0], \"../../src/private/featureflags/ReactNativeFeatureFlags\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function validate(name, nativeViewConfig, staticViewConfig) {\n    var differences = [];\n    accumulateDifferences(differences, [], {\n      bubblingEventTypes: nativeViewConfig.bubblingEventTypes,\n      directEventTypes: nativeViewConfig.directEventTypes,\n      uiViewClassName: nativeViewConfig.uiViewClassName,\n      validAttributes: nativeViewConfig.validAttributes\n    }, {\n      bubblingEventTypes: staticViewConfig.bubblingEventTypes,\n      directEventTypes: staticViewConfig.directEventTypes,\n      uiViewClassName: staticViewConfig.uiViewClassName,\n      validAttributes: staticViewConfig.validAttributes\n    });\n    if (differences.length === 0) {\n      return {\n        type: 'valid'\n      };\n    }\n    return {\n      type: 'invalid',\n      differences\n    };\n  }\n  function stringifyValidationResult(name, validationResult) {\n    var differences = validationResult.differences;\n    return [`StaticViewConfigValidator: Invalid static view config for '${name}'.`, '', ...differences.map(difference => {\n      var type = difference.type,\n        path = difference.path;\n      switch (type) {\n        case 'missing':\n          return `- '${path.join('.')}' is missing.`;\n        case 'unequal':\n          return `- '${path.join('.')}' is the wrong value.`;\n      }\n    }), ''].join('\\n');\n  }\n  function accumulateDifferences(differences, path, nativeObject, staticObject) {\n    for (var nativeKey in nativeObject) {\n      var nativeValue = nativeObject[nativeKey];\n      if (!staticObject.hasOwnProperty(nativeKey)) {\n        differences.push({\n          path: [...path, nativeKey],\n          type: 'missing',\n          nativeValue\n        });\n        continue;\n      }\n      var staticValue = staticObject[nativeKey];\n      var nativeValueIfObject = ifObject(nativeValue);\n      if (nativeValueIfObject != null) {\n        var staticValueIfObject = ifObject(staticValue);\n        if (staticValueIfObject != null) {\n          path.push(nativeKey);\n          accumulateDifferences(differences, path, nativeValueIfObject, staticValueIfObject);\n          path.pop();\n          continue;\n        }\n      }\n      if (nativeValue !== staticValue && !ReactNativeFeatureFlags.enableNativeCSSParsing()) {\n        differences.push({\n          path: [...path, nativeKey],\n          type: 'unequal',\n          nativeValue,\n          staticValue\n        });\n      }\n    }\n  }\n  function ifObject(value) {\n    return typeof value === 'object' && !Array.isArray(value) ? value : null;\n  }\n});", "lineCount": 80, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "ReactNativeFeatureFlags"], [7, 29, 11, 0], [7, 32, 11, 0, "_interopRequireWildcard"], [7, 55, 11, 0], [7, 56, 11, 0, "require"], [7, 63, 11, 0], [7, 64, 11, 0, "_dependencyMap"], [7, 78, 11, 0], [8, 2, 11, 98], [8, 11, 11, 98, "_interopRequireWildcard"], [8, 35, 11, 98, "e"], [8, 36, 11, 98], [8, 38, 11, 98, "t"], [8, 39, 11, 98], [8, 68, 11, 98, "WeakMap"], [8, 75, 11, 98], [8, 81, 11, 98, "r"], [8, 82, 11, 98], [8, 89, 11, 98, "WeakMap"], [8, 96, 11, 98], [8, 100, 11, 98, "n"], [8, 101, 11, 98], [8, 108, 11, 98, "WeakMap"], [8, 115, 11, 98], [8, 127, 11, 98, "_interopRequireWildcard"], [8, 150, 11, 98], [8, 162, 11, 98, "_interopRequireWildcard"], [8, 163, 11, 98, "e"], [8, 164, 11, 98], [8, 166, 11, 98, "t"], [8, 167, 11, 98], [8, 176, 11, 98, "t"], [8, 177, 11, 98], [8, 181, 11, 98, "e"], [8, 182, 11, 98], [8, 186, 11, 98, "e"], [8, 187, 11, 98], [8, 188, 11, 98, "__esModule"], [8, 198, 11, 98], [8, 207, 11, 98, "e"], [8, 208, 11, 98], [8, 214, 11, 98, "o"], [8, 215, 11, 98], [8, 217, 11, 98, "i"], [8, 218, 11, 98], [8, 220, 11, 98, "f"], [8, 221, 11, 98], [8, 226, 11, 98, "__proto__"], [8, 235, 11, 98], [8, 243, 11, 98, "default"], [8, 250, 11, 98], [8, 252, 11, 98, "e"], [8, 253, 11, 98], [8, 270, 11, 98, "e"], [8, 271, 11, 98], [8, 294, 11, 98, "e"], [8, 295, 11, 98], [8, 320, 11, 98, "e"], [8, 321, 11, 98], [8, 330, 11, 98, "f"], [8, 331, 11, 98], [8, 337, 11, 98, "o"], [8, 338, 11, 98], [8, 341, 11, 98, "t"], [8, 342, 11, 98], [8, 345, 11, 98, "n"], [8, 346, 11, 98], [8, 349, 11, 98, "r"], [8, 350, 11, 98], [8, 358, 11, 98, "o"], [8, 359, 11, 98], [8, 360, 11, 98, "has"], [8, 363, 11, 98], [8, 364, 11, 98, "e"], [8, 365, 11, 98], [8, 375, 11, 98, "o"], [8, 376, 11, 98], [8, 377, 11, 98, "get"], [8, 380, 11, 98], [8, 381, 11, 98, "e"], [8, 382, 11, 98], [8, 385, 11, 98, "o"], [8, 386, 11, 98], [8, 387, 11, 98, "set"], [8, 390, 11, 98], [8, 391, 11, 98, "e"], [8, 392, 11, 98], [8, 394, 11, 98, "f"], [8, 395, 11, 98], [8, 409, 11, 98, "_t"], [8, 411, 11, 98], [8, 415, 11, 98, "e"], [8, 416, 11, 98], [8, 432, 11, 98, "_t"], [8, 434, 11, 98], [8, 441, 11, 98, "hasOwnProperty"], [8, 455, 11, 98], [8, 456, 11, 98, "call"], [8, 460, 11, 98], [8, 461, 11, 98, "e"], [8, 462, 11, 98], [8, 464, 11, 98, "_t"], [8, 466, 11, 98], [8, 473, 11, 98, "i"], [8, 474, 11, 98], [8, 478, 11, 98, "o"], [8, 479, 11, 98], [8, 482, 11, 98, "Object"], [8, 488, 11, 98], [8, 489, 11, 98, "defineProperty"], [8, 503, 11, 98], [8, 508, 11, 98, "Object"], [8, 514, 11, 98], [8, 515, 11, 98, "getOwnPropertyDescriptor"], [8, 539, 11, 98], [8, 540, 11, 98, "e"], [8, 541, 11, 98], [8, 543, 11, 98, "_t"], [8, 545, 11, 98], [8, 552, 11, 98, "i"], [8, 553, 11, 98], [8, 554, 11, 98, "get"], [8, 557, 11, 98], [8, 561, 11, 98, "i"], [8, 562, 11, 98], [8, 563, 11, 98, "set"], [8, 566, 11, 98], [8, 570, 11, 98, "o"], [8, 571, 11, 98], [8, 572, 11, 98, "f"], [8, 573, 11, 98], [8, 575, 11, 98, "_t"], [8, 577, 11, 98], [8, 579, 11, 98, "i"], [8, 580, 11, 98], [8, 584, 11, 98, "f"], [8, 585, 11, 98], [8, 586, 11, 98, "_t"], [8, 588, 11, 98], [8, 592, 11, 98, "e"], [8, 593, 11, 98], [8, 594, 11, 98, "_t"], [8, 596, 11, 98], [8, 607, 11, 98, "f"], [8, 608, 11, 98], [8, 613, 11, 98, "e"], [8, 614, 11, 98], [8, 616, 11, 98, "t"], [8, 617, 11, 98], [9, 2, 40, 7], [9, 11, 40, 16, "validate"], [9, 19, 40, 24, "validate"], [9, 20, 41, 2, "name"], [9, 24, 41, 14], [9, 26, 42, 2, "nativeViewConfig"], [9, 42, 42, 30], [9, 44, 43, 2, "staticViewConfig"], [9, 60, 43, 30], [9, 62, 44, 20], [10, 4, 45, 2], [10, 8, 45, 8, "differences"], [10, 19, 45, 38], [10, 22, 45, 41], [10, 24, 45, 43], [11, 4, 46, 2, "accumulateDifferences"], [11, 25, 46, 23], [11, 26, 47, 4, "differences"], [11, 37, 47, 15], [11, 39, 48, 4], [11, 41, 48, 6], [11, 43, 49, 4], [12, 6, 50, 6, "bubblingEventTypes"], [12, 24, 50, 24], [12, 26, 50, 26, "nativeViewConfig"], [12, 42, 50, 42], [12, 43, 50, 43, "bubblingEventTypes"], [12, 61, 50, 61], [13, 6, 51, 6, "directEventTypes"], [13, 22, 51, 22], [13, 24, 51, 24, "nativeViewConfig"], [13, 40, 51, 40], [13, 41, 51, 41, "directEventTypes"], [13, 57, 51, 57], [14, 6, 52, 6, "uiViewClassName"], [14, 21, 52, 21], [14, 23, 52, 23, "nativeViewConfig"], [14, 39, 52, 39], [14, 40, 52, 40, "uiViewClassName"], [14, 55, 52, 55], [15, 6, 53, 6, "validAttributes"], [15, 21, 53, 21], [15, 23, 53, 23, "nativeViewConfig"], [15, 39, 53, 39], [15, 40, 53, 40, "validAttributes"], [16, 4, 54, 4], [16, 5, 54, 5], [16, 7, 55, 4], [17, 6, 56, 6, "bubblingEventTypes"], [17, 24, 56, 24], [17, 26, 56, 26, "staticViewConfig"], [17, 42, 56, 42], [17, 43, 56, 43, "bubblingEventTypes"], [17, 61, 56, 61], [18, 6, 57, 6, "directEventTypes"], [18, 22, 57, 22], [18, 24, 57, 24, "staticViewConfig"], [18, 40, 57, 40], [18, 41, 57, 41, "directEventTypes"], [18, 57, 57, 57], [19, 6, 58, 6, "uiViewClassName"], [19, 21, 58, 21], [19, 23, 58, 23, "staticViewConfig"], [19, 39, 58, 39], [19, 40, 58, 40, "uiViewClassName"], [19, 55, 58, 55], [20, 6, 59, 6, "validAttributes"], [20, 21, 59, 21], [20, 23, 59, 23, "staticViewConfig"], [20, 39, 59, 39], [20, 40, 59, 40, "validAttributes"], [21, 4, 60, 4], [21, 5, 61, 2], [21, 6, 61, 3], [22, 4, 63, 2], [22, 8, 63, 6, "differences"], [22, 19, 63, 17], [22, 20, 63, 18, "length"], [22, 26, 63, 24], [22, 31, 63, 29], [22, 32, 63, 30], [22, 34, 63, 32], [23, 6, 64, 4], [23, 13, 64, 11], [24, 8, 64, 12, "type"], [24, 12, 64, 16], [24, 14, 64, 18], [25, 6, 64, 25], [25, 7, 64, 26], [26, 4, 65, 2], [27, 4, 67, 2], [27, 11, 67, 9], [28, 6, 68, 4, "type"], [28, 10, 68, 8], [28, 12, 68, 10], [28, 21, 68, 19], [29, 6, 69, 4, "differences"], [30, 4, 70, 2], [30, 5, 70, 3], [31, 2, 71, 0], [32, 2, 73, 7], [32, 11, 73, 16, "stringifyValidationResult"], [32, 36, 73, 41, "stringifyValidationResult"], [32, 37, 74, 2, "name"], [32, 41, 74, 14], [32, 43, 75, 2, "validationResult"], [32, 59, 75, 33], [32, 61, 76, 10], [33, 4, 77, 2], [33, 8, 77, 9, "differences"], [33, 19, 77, 20], [33, 22, 77, 24, "validationResult"], [33, 38, 77, 40], [33, 39, 77, 9, "differences"], [33, 50, 77, 20], [34, 4, 78, 2], [34, 11, 78, 9], [34, 12, 79, 4], [34, 74, 79, 66, "name"], [34, 78, 79, 70], [34, 82, 79, 74], [34, 84, 80, 4], [34, 86, 80, 6], [34, 88, 81, 4], [34, 91, 81, 7, "differences"], [34, 102, 81, 18], [34, 103, 81, 19, "map"], [34, 106, 81, 22], [34, 107, 81, 23, "difference"], [34, 117, 81, 33], [34, 121, 81, 37], [35, 6, 82, 6], [35, 10, 82, 13, "type"], [35, 14, 82, 17], [35, 17, 82, 27, "difference"], [35, 27, 82, 37], [35, 28, 82, 13, "type"], [35, 32, 82, 17], [36, 8, 82, 19, "path"], [36, 12, 82, 23], [36, 15, 82, 27, "difference"], [36, 25, 82, 37], [36, 26, 82, 19, "path"], [36, 30, 82, 23], [37, 6, 83, 6], [37, 14, 83, 14, "type"], [37, 18, 83, 18], [38, 8, 84, 8], [38, 13, 84, 13], [38, 22, 84, 22], [39, 10, 85, 10], [39, 17, 85, 17], [39, 23, 85, 23, "path"], [39, 27, 85, 27], [39, 28, 85, 28, "join"], [39, 32, 85, 32], [39, 33, 85, 33], [39, 36, 85, 36], [39, 37, 85, 37], [39, 52, 85, 52], [40, 8, 86, 8], [40, 13, 86, 13], [40, 22, 86, 22], [41, 10, 87, 10], [41, 17, 87, 17], [41, 23, 87, 23, "path"], [41, 27, 87, 27], [41, 28, 87, 28, "join"], [41, 32, 87, 32], [41, 33, 87, 33], [41, 36, 87, 36], [41, 37, 87, 37], [41, 60, 87, 60], [42, 6, 88, 6], [43, 4, 89, 4], [43, 5, 89, 5], [43, 6, 89, 6], [43, 8, 90, 4], [43, 10, 90, 6], [43, 11, 91, 3], [43, 12, 91, 4, "join"], [43, 16, 91, 8], [43, 17, 91, 9], [43, 21, 91, 13], [43, 22, 91, 14], [44, 2, 92, 0], [45, 2, 94, 0], [45, 11, 94, 9, "accumulateDifferences"], [45, 32, 94, 30, "accumulateDifferences"], [45, 33, 95, 2, "differences"], [45, 44, 95, 32], [45, 46, 96, 2, "path"], [45, 50, 96, 21], [45, 52, 97, 2, "nativeObject"], [45, 64, 97, 21], [45, 66, 98, 2, "staticObject"], [45, 78, 98, 21], [45, 80, 99, 8], [46, 4, 100, 2], [46, 9, 100, 7], [46, 13, 100, 13, "<PERSON><PERSON><PERSON>"], [46, 22, 100, 22], [46, 26, 100, 26, "nativeObject"], [46, 38, 100, 38], [46, 40, 100, 40], [47, 6, 102, 4], [47, 10, 102, 10, "nativeValue"], [47, 21, 102, 21], [47, 24, 102, 24, "nativeObject"], [47, 36, 102, 36], [47, 37, 102, 37, "<PERSON><PERSON><PERSON>"], [47, 46, 102, 46], [47, 47, 102, 47], [48, 6, 104, 4], [48, 10, 104, 8], [48, 11, 104, 9, "staticObject"], [48, 23, 104, 21], [48, 24, 104, 22, "hasOwnProperty"], [48, 38, 104, 36], [48, 39, 104, 37, "<PERSON><PERSON><PERSON>"], [48, 48, 104, 46], [48, 49, 104, 47], [48, 51, 104, 49], [49, 8, 105, 6, "differences"], [49, 19, 105, 17], [49, 20, 105, 18, "push"], [49, 24, 105, 22], [49, 25, 105, 23], [50, 10, 106, 8, "path"], [50, 14, 106, 12], [50, 16, 106, 14], [50, 17, 106, 15], [50, 20, 106, 18, "path"], [50, 24, 106, 22], [50, 26, 106, 24, "<PERSON><PERSON><PERSON>"], [50, 35, 106, 33], [50, 36, 106, 34], [51, 10, 107, 8, "type"], [51, 14, 107, 12], [51, 16, 107, 14], [51, 25, 107, 23], [52, 10, 108, 8, "nativeValue"], [53, 8, 109, 6], [53, 9, 109, 7], [53, 10, 109, 8], [54, 8, 110, 6], [55, 6, 111, 4], [56, 6, 114, 4], [56, 10, 114, 10, "staticValue"], [56, 21, 114, 21], [56, 24, 114, 24, "staticObject"], [56, 36, 114, 36], [56, 37, 114, 37, "<PERSON><PERSON><PERSON>"], [56, 46, 114, 46], [56, 47, 114, 47], [57, 6, 116, 4], [57, 10, 116, 10, "nativeValueIfObject"], [57, 29, 116, 29], [57, 32, 116, 32, "ifObject"], [57, 40, 116, 40], [57, 41, 116, 41, "nativeValue"], [57, 52, 116, 52], [57, 53, 116, 53], [58, 6, 117, 4], [58, 10, 117, 8, "nativeValueIfObject"], [58, 29, 117, 27], [58, 33, 117, 31], [58, 37, 117, 35], [58, 39, 117, 37], [59, 8, 118, 6], [59, 12, 118, 12, "staticValueIfObject"], [59, 31, 118, 31], [59, 34, 118, 34, "ifObject"], [59, 42, 118, 42], [59, 43, 118, 43, "staticValue"], [59, 54, 118, 54], [59, 55, 118, 55], [60, 8, 119, 6], [60, 12, 119, 10, "staticValueIfObject"], [60, 31, 119, 29], [60, 35, 119, 33], [60, 39, 119, 37], [60, 41, 119, 39], [61, 10, 120, 8, "path"], [61, 14, 120, 12], [61, 15, 120, 13, "push"], [61, 19, 120, 17], [61, 20, 120, 18, "<PERSON><PERSON><PERSON>"], [61, 29, 120, 27], [61, 30, 120, 28], [62, 10, 121, 8, "accumulateDifferences"], [62, 31, 121, 29], [62, 32, 122, 10, "differences"], [62, 43, 122, 21], [62, 45, 123, 10, "path"], [62, 49, 123, 14], [62, 51, 124, 10, "nativeValueIfObject"], [62, 70, 124, 29], [62, 72, 125, 10, "staticValueIfObject"], [62, 91, 126, 8], [62, 92, 126, 9], [63, 10, 127, 8, "path"], [63, 14, 127, 12], [63, 15, 127, 13, "pop"], [63, 18, 127, 16], [63, 19, 127, 17], [63, 20, 127, 18], [64, 10, 128, 8], [65, 8, 129, 6], [66, 6, 130, 4], [67, 6, 132, 4], [67, 10, 133, 6, "nativeValue"], [67, 21, 133, 17], [67, 26, 133, 22, "staticValue"], [67, 37, 133, 33], [67, 41, 134, 6], [67, 42, 134, 7, "ReactNativeFeatureFlags"], [67, 65, 134, 30], [67, 66, 134, 31, "enableNativeCSSParsing"], [67, 88, 134, 53], [67, 89, 134, 54], [67, 90, 134, 55], [67, 92, 135, 6], [68, 8, 136, 6, "differences"], [68, 19, 136, 17], [68, 20, 136, 18, "push"], [68, 24, 136, 22], [68, 25, 136, 23], [69, 10, 137, 8, "path"], [69, 14, 137, 12], [69, 16, 137, 14], [69, 17, 137, 15], [69, 20, 137, 18, "path"], [69, 24, 137, 22], [69, 26, 137, 24, "<PERSON><PERSON><PERSON>"], [69, 35, 137, 33], [69, 36, 137, 34], [70, 10, 138, 8, "type"], [70, 14, 138, 12], [70, 16, 138, 14], [70, 25, 138, 23], [71, 10, 139, 8, "nativeValue"], [71, 21, 139, 19], [72, 10, 140, 8, "staticValue"], [73, 8, 141, 6], [73, 9, 141, 7], [73, 10, 141, 8], [74, 6, 142, 4], [75, 4, 143, 2], [76, 2, 144, 0], [77, 2, 146, 0], [77, 11, 146, 9, "ifObject"], [77, 19, 146, 17, "ifObject"], [77, 20, 146, 18, "value"], [77, 25, 146, 30], [77, 27, 146, 40], [78, 4, 147, 2], [78, 11, 147, 9], [78, 18, 147, 16, "value"], [78, 23, 147, 21], [78, 28, 147, 26], [78, 36, 147, 34], [78, 40, 147, 38], [78, 41, 147, 39, "Array"], [78, 46, 147, 44], [78, 47, 147, 45, "isArray"], [78, 54, 147, 52], [78, 55, 147, 53, "value"], [78, 60, 147, 58], [78, 61, 147, 59], [78, 64, 147, 62, "value"], [78, 69, 147, 67], [78, 72, 147, 70], [78, 76, 147, 74], [79, 2, 148, 0], [80, 0, 148, 1], [80, 3]], "functionMap": {"names": ["<global>", "validate", "stringifyValidationResult", "differences.map$argument_0", "accumulateDifferences", "ifObject"], "mappings": "AAA;OCuC;CD+B;OEE;uBCQ;KDQ;CFG;AIE;CJkD;AKE"}}, "type": "js/module"}]}