{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../../Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 92}}], "key": "jzP+LUi0+8ZCeIUw7GN35c9PLT4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 25, "column": 32}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"../../../../Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var nativeComponentName = 'RCTSafeAreaView';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RCTSafeAreaView\",\n    validAttributes: {}\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 15, "map": [[7, 2, 14, 0], [7, 6, 14, 0, "_codegenNativeComponent"], [7, 29, 14, 0], [7, 32, 14, 0, "_interopRequireDefault"], [7, 54, 14, 0], [7, 55, 14, 0, "require"], [7, 62, 14, 0], [7, 63, 14, 0, "_dependencyMap"], [7, 77, 14, 0], [8, 2, 22, 0], [8, 6, 22, 0, "NativeComponentRegistry"], [8, 29, 25, 32], [8, 32, 22, 0, "require"], [8, 39, 25, 32], [8, 40, 25, 32, "_dependencyMap"], [8, 54, 25, 32], [8, 123, 25, 31], [8, 124, 25, 32], [9, 2, 22, 0], [9, 6, 22, 0, "nativeComponentName"], [9, 25, 25, 32], [9, 28, 22, 0], [9, 45, 25, 32], [10, 2, 22, 0], [10, 6, 22, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 25, 32], [10, 31, 25, 32, "exports"], [10, 38, 25, 32], [10, 39, 25, 32, "__INTERNAL_VIEW_CONFIG"], [10, 61, 25, 32], [10, 64, 22, 0], [11, 4, 22, 0, "uiViewClassName"], [11, 19, 25, 32], [11, 21, 22, 0], [11, 38, 25, 32], [12, 4, 22, 0, "validAttributes"], [12, 19, 25, 32], [12, 21, 22, 0], [12, 22, 25, 31], [13, 2, 25, 31], [13, 3, 25, 32], [14, 2, 25, 32], [14, 6, 25, 32, "_default"], [14, 14, 25, 32], [14, 17, 25, 32, "exports"], [14, 24, 25, 32], [14, 25, 25, 32, "default"], [14, 32, 25, 32], [14, 35, 22, 0, "NativeComponentRegistry"], [14, 58, 25, 32], [14, 59, 22, 0, "get"], [14, 62, 25, 32], [14, 63, 22, 0, "nativeComponentName"], [14, 82, 25, 32], [14, 84, 22, 0], [14, 90, 22, 0, "__INTERNAL_VIEW_CONFIG"], [14, 112, 25, 31], [14, 113, 25, 32], [15, 0, 25, 32], [15, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}