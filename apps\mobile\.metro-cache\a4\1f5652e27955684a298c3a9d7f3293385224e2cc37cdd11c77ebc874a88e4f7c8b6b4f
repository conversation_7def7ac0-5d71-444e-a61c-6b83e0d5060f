{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2F..%2F..%2Fnode_modules%2Freact-native%2FLibraries%2FLogBox%2FUI%2FLogBoxImages\",\n    \"width\": 28,\n    \"height\": 28,\n    \"scales\": [1],\n    \"hash\": \"369745d4a4a6fa62fa0ed495f89aa964\",\n    \"name\": \"close\",\n    \"type\": \"png\",\n    \"fileHashes\": [\"369745d4a4a6fa62fa0ed495f89aa964\"]\n  });\n});", "lineCount": 13, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 133, 1, 153], [5, 4, 1, 154], [5, 11, 1, 161], [5, 13, 1, 162], [5, 15, 1, 164], [6, 4, 1, 165], [6, 12, 1, 173], [6, 14, 1, 174], [6, 16, 1, 176], [7, 4, 1, 177], [7, 12, 1, 185], [7, 14, 1, 186], [7, 15, 1, 187], [7, 16, 1, 188], [7, 17, 1, 189], [8, 4, 1, 190], [8, 10, 1, 196], [8, 12, 1, 197], [8, 46, 1, 231], [9, 4, 1, 232], [9, 10, 1, 238], [9, 12, 1, 239], [9, 19, 1, 246], [10, 4, 1, 247], [10, 10, 1, 253], [10, 12, 1, 254], [10, 17, 1, 259], [11, 4, 1, 260], [11, 16, 1, 272], [11, 18, 1, 273], [11, 19, 1, 274], [11, 53, 1, 308], [12, 2, 1, 309], [12, 3, 1, 310], [13, 0, 1, 310], [13, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}