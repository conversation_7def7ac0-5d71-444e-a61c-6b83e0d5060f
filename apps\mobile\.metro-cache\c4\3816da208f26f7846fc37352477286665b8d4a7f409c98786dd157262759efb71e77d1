{"dependencies": [{"name": "../../src/private/specs_DEPRECATED/components/DebuggingOverlayNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 94}}], "key": "EbJcuHrjvGMIJi5sdAsichH2iQQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {};\n  exports.default = void 0;\n  var _DebuggingOverlayNativeComponent = _interopRequireWildcard(require(_dependencyMap[0], \"../../src/private/specs_DEPRECATED/components/DebuggingOverlayNativeComponent\"));\n  Object.keys(_DebuggingOverlayNativeComponent).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _DebuggingOverlayNativeComponent[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _DebuggingOverlayNativeComponent[key];\n      }\n    });\n  });\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var _default = exports.default = _DebuggingOverlayNativeComponent.default;\n});", "lineCount": 21, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_DebuggingOverlayNativeComponent"], [7, 38, 11, 0], [7, 41, 11, 0, "_interopRequireWildcard"], [7, 64, 11, 0], [7, 65, 11, 0, "require"], [7, 72, 11, 0], [7, 73, 11, 0, "_dependencyMap"], [7, 87, 11, 0], [8, 2, 11, 0, "Object"], [8, 8, 11, 0], [8, 9, 11, 0, "keys"], [8, 13, 11, 0], [8, 14, 11, 0, "_DebuggingOverlayNativeComponent"], [8, 46, 11, 0], [8, 48, 11, 0, "for<PERSON>ach"], [8, 55, 11, 0], [8, 66, 11, 0, "key"], [8, 69, 11, 0], [9, 4, 11, 0], [9, 8, 11, 0, "key"], [9, 11, 11, 0], [9, 29, 11, 0, "key"], [9, 32, 11, 0], [10, 4, 11, 0], [10, 8, 11, 0, "Object"], [10, 14, 11, 0], [10, 15, 11, 0, "prototype"], [10, 24, 11, 0], [10, 25, 11, 0, "hasOwnProperty"], [10, 39, 11, 0], [10, 40, 11, 0, "call"], [10, 44, 11, 0], [10, 45, 11, 0, "_exportNames"], [10, 57, 11, 0], [10, 59, 11, 0, "key"], [10, 62, 11, 0], [11, 4, 11, 0], [11, 8, 11, 0, "key"], [11, 11, 11, 0], [11, 15, 11, 0, "exports"], [11, 22, 11, 0], [11, 26, 11, 0, "exports"], [11, 33, 11, 0], [11, 34, 11, 0, "key"], [11, 37, 11, 0], [11, 43, 11, 0, "_DebuggingOverlayNativeComponent"], [11, 75, 11, 0], [11, 76, 11, 0, "key"], [11, 79, 11, 0], [12, 4, 11, 0, "Object"], [12, 10, 11, 0], [12, 11, 11, 0, "defineProperty"], [12, 25, 11, 0], [12, 26, 11, 0, "exports"], [12, 33, 11, 0], [12, 35, 11, 0, "key"], [12, 38, 11, 0], [13, 6, 11, 0, "enumerable"], [13, 16, 11, 0], [14, 6, 11, 0, "get"], [14, 9, 11, 0], [14, 20, 11, 0, "get"], [14, 21, 11, 0], [15, 8, 11, 0], [15, 15, 11, 0, "_DebuggingOverlayNativeComponent"], [15, 47, 11, 0], [15, 48, 11, 0, "key"], [15, 51, 11, 0], [16, 6, 11, 0], [17, 4, 11, 0], [18, 2, 11, 0], [19, 2, 11, 94], [19, 11, 11, 94, "_interopRequireWildcard"], [19, 35, 11, 94, "e"], [19, 36, 11, 94], [19, 38, 11, 94, "t"], [19, 39, 11, 94], [19, 68, 11, 94, "WeakMap"], [19, 75, 11, 94], [19, 81, 11, 94, "r"], [19, 82, 11, 94], [19, 89, 11, 94, "WeakMap"], [19, 96, 11, 94], [19, 100, 11, 94, "n"], [19, 101, 11, 94], [19, 108, 11, 94, "WeakMap"], [19, 115, 11, 94], [19, 127, 11, 94, "_interopRequireWildcard"], [19, 150, 11, 94], [19, 162, 11, 94, "_interopRequireWildcard"], [19, 163, 11, 94, "e"], [19, 164, 11, 94], [19, 166, 11, 94, "t"], [19, 167, 11, 94], [19, 176, 11, 94, "t"], [19, 177, 11, 94], [19, 181, 11, 94, "e"], [19, 182, 11, 94], [19, 186, 11, 94, "e"], [19, 187, 11, 94], [19, 188, 11, 94, "__esModule"], [19, 198, 11, 94], [19, 207, 11, 94, "e"], [19, 208, 11, 94], [19, 214, 11, 94, "o"], [19, 215, 11, 94], [19, 217, 11, 94, "i"], [19, 218, 11, 94], [19, 220, 11, 94, "f"], [19, 221, 11, 94], [19, 226, 11, 94, "__proto__"], [19, 235, 11, 94], [19, 243, 11, 94, "default"], [19, 250, 11, 94], [19, 252, 11, 94, "e"], [19, 253, 11, 94], [19, 270, 11, 94, "e"], [19, 271, 11, 94], [19, 294, 11, 94, "e"], [19, 295, 11, 94], [19, 320, 11, 94, "e"], [19, 321, 11, 94], [19, 330, 11, 94, "f"], [19, 331, 11, 94], [19, 337, 11, 94, "o"], [19, 338, 11, 94], [19, 341, 11, 94, "t"], [19, 342, 11, 94], [19, 345, 11, 94, "n"], [19, 346, 11, 94], [19, 349, 11, 94, "r"], [19, 350, 11, 94], [19, 358, 11, 94, "o"], [19, 359, 11, 94], [19, 360, 11, 94, "has"], [19, 363, 11, 94], [19, 364, 11, 94, "e"], [19, 365, 11, 94], [19, 375, 11, 94, "o"], [19, 376, 11, 94], [19, 377, 11, 94, "get"], [19, 380, 11, 94], [19, 381, 11, 94, "e"], [19, 382, 11, 94], [19, 385, 11, 94, "o"], [19, 386, 11, 94], [19, 387, 11, 94, "set"], [19, 390, 11, 94], [19, 391, 11, 94, "e"], [19, 392, 11, 94], [19, 394, 11, 94, "f"], [19, 395, 11, 94], [19, 409, 11, 94, "_t"], [19, 411, 11, 94], [19, 415, 11, 94, "e"], [19, 416, 11, 94], [19, 432, 11, 94, "_t"], [19, 434, 11, 94], [19, 441, 11, 94, "hasOwnProperty"], [19, 455, 11, 94], [19, 456, 11, 94, "call"], [19, 460, 11, 94], [19, 461, 11, 94, "e"], [19, 462, 11, 94], [19, 464, 11, 94, "_t"], [19, 466, 11, 94], [19, 473, 11, 94, "i"], [19, 474, 11, 94], [19, 478, 11, 94, "o"], [19, 479, 11, 94], [19, 482, 11, 94, "Object"], [19, 488, 11, 94], [19, 489, 11, 94, "defineProperty"], [19, 503, 11, 94], [19, 508, 11, 94, "Object"], [19, 514, 11, 94], [19, 515, 11, 94, "getOwnPropertyDescriptor"], [19, 539, 11, 94], [19, 540, 11, 94, "e"], [19, 541, 11, 94], [19, 543, 11, 94, "_t"], [19, 545, 11, 94], [19, 552, 11, 94, "i"], [19, 553, 11, 94], [19, 554, 11, 94, "get"], [19, 557, 11, 94], [19, 561, 11, 94, "i"], [19, 562, 11, 94], [19, 563, 11, 94, "set"], [19, 566, 11, 94], [19, 570, 11, 94, "o"], [19, 571, 11, 94], [19, 572, 11, 94, "f"], [19, 573, 11, 94], [19, 575, 11, 94, "_t"], [19, 577, 11, 94], [19, 579, 11, 94, "i"], [19, 580, 11, 94], [19, 584, 11, 94, "f"], [19, 585, 11, 94], [19, 586, 11, 94, "_t"], [19, 588, 11, 94], [19, 592, 11, 94, "e"], [19, 593, 11, 94], [19, 594, 11, 94, "_t"], [19, 596, 11, 94], [19, 607, 11, 94, "f"], [19, 608, 11, 94], [19, 613, 11, 94, "e"], [19, 614, 11, 94], [19, 616, 11, 94, "t"], [19, 617, 11, 94], [20, 2, 11, 94], [20, 6, 11, 94, "_default"], [20, 14, 11, 94], [20, 17, 11, 94, "exports"], [20, 24, 11, 94], [20, 25, 11, 94, "default"], [20, 32, 11, 94], [20, 35, 13, 15, "DebuggingOverlayNativeComponent"], [20, 75, 13, 46], [21, 0, 13, 46], [21, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}