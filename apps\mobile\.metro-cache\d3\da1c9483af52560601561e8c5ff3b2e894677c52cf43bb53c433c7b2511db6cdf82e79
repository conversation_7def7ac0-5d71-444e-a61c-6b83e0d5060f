{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 49, "index": 64}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 65}, "end": {"line": 4, "column": 59, "index": 124}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../ConfigHelper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 126}, "end": {"line": 6, "column": 60, "index": 186}}], "key": "G9xd6OWOTj9ITxVE1K601ohQjLg=", "exportNames": ["*"]}}, {"name": "../createAnimatedComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 187}, "end": {"line": 7, "column": 69, "index": 256}}], "key": "e2Y7i0GjZ0FYhc0zsmE7V0rtFCw=", "exportNames": ["*"]}}, {"name": "../hook", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 308}, "end": {"line": 9, "column": 77, "index": 385}}], "key": "bWwNpYfPBnLxO2zDvQJyVxnxHlk=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PerformanceMonitor = PerformanceMonitor;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _reactNative = require(_dependencyMap[1], \"react-native\");\n  var _ConfigHelper = require(_dependencyMap[2], \"../ConfigHelper\");\n  var _createAnimatedComponent = require(_dependencyMap[3], \"../createAnimatedComponent\");\n  var _hook = require(_dependencyMap[4], \"../hook\");\n  var _jsxDevRuntime = require(_dependencyMap[5], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\component\\\\PerformanceMonitor.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var _worklet_7025330993767_init_data = {\n    code: \"function createCircularDoublesBuffer_PerformanceMonitorTsx1(size){return{next:0,buffer:new Float32Array(size),size:size,count:0,push:function(value){const oldValue=this.buffer[this.next];const oldCount=this.count;this.buffer[this.next]=value;this.next=(this.next+1)%this.size;this.count=Math.min(this.size,this.count+1);return oldCount===this.size?oldValue:null;},front:function(){const notEmpty=this.count>0;if(notEmpty){const current=this.next-1;const index=current<0?this.size-1:current;return this.buffer[index];}return null;},back:function(){const notEmpty=this.count>0;return notEmpty?this.buffer[this.next]:null;}};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\component\\\\PerformanceMonitor.tsx\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"createCircularDoublesBuffer_PerformanceMonitorTsx1\\\",\\\"size\\\",\\\"next\\\",\\\"buffer\\\",\\\"Float32Array\\\",\\\"count\\\",\\\"push\\\",\\\"value\\\",\\\"oldValue\\\",\\\"oldCount\\\",\\\"Math\\\",\\\"min\\\",\\\"front\\\",\\\"notEmpty\\\",\\\"current\\\",\\\"index\\\",\\\"back\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/component/PerformanceMonitor.tsx\\\"],\\\"mappings\\\":\\\"AAWA,SAAAA,kDAAmDA,CAAAC,IAAA,EAGjD,MAAO,CACLC,IAAI,CAAE,CAAW,CACjBC,MAAM,CAAE,GAAI,CAAAC,YAAY,CAACH,IAAI,CAAC,CAC9BA,IAAI,CAAJA,IAAI,CACJI,KAAK,CAAE,CAAW,CAElBC,IAAI,SAAAA,CAACC,KAAa,CAAiB,CACjC,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACL,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,CACvC,KAAM,CAAAO,QAAQ,CAAG,IAAI,CAACJ,KAAK,CAC3B,IAAI,CAACF,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,CAAGK,KAAK,CAE9B,IAAI,CAACL,IAAI,CAAG,CAAC,IAAI,CAACA,IAAI,CAAG,CAAC,EAAI,IAAI,CAACD,IAAI,CACvC,IAAI,CAACI,KAAK,CAAGK,IAAI,CAACC,GAAG,CAAC,IAAI,CAACV,IAAI,CAAE,IAAI,CAACI,KAAK,CAAG,CAAC,CAAC,CAChD,MAAO,CAAAI,QAAQ,GAAK,IAAI,CAACR,IAAI,CAAGO,QAAQ,CAAG,IAAI,CACjD,CAAC,CAEDI,KAAK,SAAAA,CAAA,CAAkB,CACrB,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACR,KAAK,CAAG,CAAC,CAC/B,GAAIQ,QAAQ,CAAE,CACZ,KAAM,CAAAC,OAAO,CAAG,IAAI,CAACZ,IAAI,CAAG,CAAC,CAC7B,KAAM,CAAAa,KAAK,CAAGD,OAAO,CAAG,CAAC,CAAG,IAAI,CAACb,IAAI,CAAG,CAAC,CAAGa,OAAO,CACnD,MAAO,KAAI,CAACX,MAAM,CAACY,KAAK,CAAC,CAC3B,CACA,MAAO,KAAI,CACb,CAAC,CAEDC,IAAI,SAAAA,CAAA,CAAkB,CACpB,KAAM,CAAAH,QAAQ,CAAG,IAAI,CAACR,KAAK,CAAG,CAAC,CAC/B,MAAO,CAAAQ,QAAQ,CAAG,IAAI,CAACV,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,CAAG,IAAI,CACjD,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var createCircularDoublesBuffer = function () {\n    var _e = [new global.Error(), 1, -27];\n    var createCircularDoublesBuffer = function (size) {\n      return {\n        next: 0,\n        buffer: new Float32Array(size),\n        size,\n        count: 0,\n        push(value) {\n          var oldValue = this.buffer[this.next];\n          var oldCount = this.count;\n          this.buffer[this.next] = value;\n          this.next = (this.next + 1) % this.size;\n          this.count = Math.min(this.size, this.count + 1);\n          return oldCount === this.size ? oldValue : null;\n        },\n        front() {\n          var notEmpty = this.count > 0;\n          if (notEmpty) {\n            var current = this.next - 1;\n            var index = current < 0 ? this.size - 1 : current;\n            return this.buffer[index];\n          }\n          return null;\n        },\n        back() {\n          var notEmpty = this.count > 0;\n          return notEmpty ? this.buffer[this.next] : null;\n        }\n      };\n    };\n    createCircularDoublesBuffer.__closure = {};\n    createCircularDoublesBuffer.__workletHash = 7025330993767;\n    createCircularDoublesBuffer.__initData = _worklet_7025330993767_init_data;\n    createCircularDoublesBuffer.__stackDetails = _e;\n    return createCircularDoublesBuffer;\n  }();\n  var DEFAULT_BUFFER_SIZE = 20;\n  (0, _ConfigHelper.addWhitelistedNativeProps)({\n    text: true\n  });\n  var AnimatedTextInput = (0, _createAnimatedComponent.createAnimatedComponent)(_reactNative.TextInput);\n  function loopAnimationFrame(fn) {\n    var lastTime = 0;\n    function loop() {\n      requestAnimationFrame(time => {\n        if (lastTime > 0) {\n          fn(lastTime, time);\n        }\n        lastTime = time;\n        requestAnimationFrame(loop);\n      });\n    }\n    loop();\n  }\n  var _worklet_4328451430280_init_data = {\n    code: \"function getFps_PerformanceMonitorTsx2(renderTimeInMs){return 1000/renderTimeInMs;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\component\\\\PerformanceMonitor.tsx\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getFps_PerformanceMonitorTsx2\\\",\\\"renderTimeInMs\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/component/PerformanceMonitor.tsx\\\"],\\\"mappings\\\":\\\"AAmEA,SAAAA,6BAAgDA,CAAAC,cAAA,EAE9C,MAAO,KAAI,CAAGA,cAAc,CAC9B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var getFps = function () {\n    var _e = [new global.Error(), 1, -27];\n    var getFps = function (renderTimeInMs) {\n      return 1000 / renderTimeInMs;\n    };\n    getFps.__closure = {};\n    getFps.__workletHash = 4328451430280;\n    getFps.__initData = _worklet_4328451430280_init_data;\n    getFps.__stackDetails = _e;\n    return getFps;\n  }();\n  var _worklet_3765204747727_init_data = {\n    code: \"function completeBufferRoutine_PerformanceMonitorTsx3(buffer,timestamp){const{getFps}=this.__closure;var _buffer$push;timestamp=Math.round(timestamp);const droppedTimestamp=(_buffer$push=buffer.push(timestamp))!==null&&_buffer$push!==void 0?_buffer$push:timestamp;const measuredRangeDuration=timestamp-droppedTimestamp;return getFps(measuredRangeDuration/buffer.count);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\component\\\\PerformanceMonitor.tsx\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"completeBufferRoutine_PerformanceMonitorTsx3\\\",\\\"buffer\\\",\\\"timestamp\\\",\\\"getFps\\\",\\\"__closure\\\",\\\"_buffer$push\\\",\\\"Math\\\",\\\"round\\\",\\\"droppedTimestamp\\\",\\\"push\\\",\\\"measuredRangeDuration\\\",\\\"count\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/component/PerformanceMonitor.tsx\\\"],\\\"mappings\\\":\\\"AAwEA,SAAAA,4CAGUA,CAAAC,MAAA,CAAAC,SAAA,QAAAC,MAAA,OAAAC,SAAA,KAAAC,YAAA,CAERH,SAAS,CAAGI,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC,CAEjC,KAAM,CAAAM,gBAAgB,EAAAH,YAAA,CAAGJ,MAAM,CAACQ,IAAI,CAACP,SAAS,CAAC,UAAAG,YAAA,UAAAA,YAAA,CAAIH,SAAS,CAE5D,KAAM,CAAAQ,qBAAqB,CAAGR,SAAS,CAAGM,gBAAgB,CAE1D,MAAO,CAAAL,MAAM,CAACO,qBAAqB,CAAGT,MAAM,CAACU,KAAK,CAAC,CACrD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var completeBufferRoutine = function () {\n    var _e = [new global.Error(), -2, -27];\n    var completeBufferRoutine = function (buffer, timestamp) {\n      timestamp = Math.round(timestamp);\n      var droppedTimestamp = buffer.push(timestamp) ?? timestamp;\n      var measuredRangeDuration = timestamp - droppedTimestamp;\n      return getFps(measuredRangeDuration / buffer.count);\n    };\n    completeBufferRoutine.__closure = {\n      getFps\n    };\n    completeBufferRoutine.__workletHash = 3765204747727;\n    completeBufferRoutine.__initData = _worklet_3765204747727_init_data;\n    completeBufferRoutine.__stackDetails = _e;\n    return completeBufferRoutine;\n  }();\n  var _worklet_6153571000774_init_data = {\n    code: \"function PerformanceMonitorTsx4(){const{jsFps}=this.__closure;var _jsFps$value;const text='JS: '+((_jsFps$value=jsFps.value)!==null&&_jsFps$value!==void 0?_jsFps$value:'N/A')+' ';return{text:text,defaultValue:text};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\component\\\\PerformanceMonitor.tsx\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"PerformanceMonitorTsx4\\\",\\\"jsFps\\\",\\\"__closure\\\",\\\"_jsFps$value\\\",\\\"text\\\",\\\"value\\\",\\\"defaultValue\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/component/PerformanceMonitor.tsx\\\"],\\\"mappings\\\":\\\"AA4GyC,SAAAA,sBAAMA,CAAA,QAAAC,KAAA,OAAAC,SAAA,KAAAC,YAAA,CAC3C,KAAM,CAAAC,IAAI,CAAG,MAAM,GAAAD,YAAA,CAAIF,KAAK,CAACI,KAAK,UAAAF,YAAA,UAAAA,YAAA,CAAI,KAAK,CAAC,CAAG,GAAG,CAClD,MAAO,CAAEC,IAAI,CAAJA,IAAI,CAAEE,YAAY,CAAEF,IAAK,CAAC,CACrC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function JsPerformance(_ref) {\n    var smoothingFrames = _ref.smoothingFrames;\n    var jsFps = (0, _hook.useSharedValue)(null);\n    var totalRenderTime = (0, _hook.useSharedValue)(0);\n    var circularBuffer = (0, _react.useRef)(createCircularDoublesBuffer(smoothingFrames));\n    (0, _react.useEffect)(() => {\n      loopAnimationFrame((_, timestamp) => {\n        timestamp = Math.round(timestamp);\n        var currentFps = completeBufferRoutine(circularBuffer.current, timestamp);\n\n        // JS fps have to be measured every 2nd frame,\n        // thus 2x multiplication has to occur here\n        jsFps.value = (currentFps * 2).toFixed(0);\n      });\n    }, [jsFps, totalRenderTime]);\n    var animatedProps = (0, _hook.useAnimatedProps)(function () {\n      var _e = [new global.Error(), -2, -27];\n      var PerformanceMonitorTsx4 = function () {\n        var text = 'JS: ' + (jsFps.value ?? 'N/A') + ' ';\n        return {\n          text,\n          defaultValue: text\n        };\n      };\n      PerformanceMonitorTsx4.__closure = {\n        jsFps\n      };\n      PerformanceMonitorTsx4.__workletHash = 6153571000774;\n      PerformanceMonitorTsx4.__initData = _worklet_6153571000774_init_data;\n      PerformanceMonitorTsx4.__stackDetails = _e;\n      return PerformanceMonitorTsx4;\n    }());\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: styles.container,\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(AnimatedTextInput, {\n        style: styles.text,\n        animatedProps: animatedProps,\n        editable: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 5\n    }, this);\n  }\n  var _worklet_6804949272378_init_data = {\n    code: \"function PerformanceMonitorTsx5({timestamp:timestamp}){const{circularBuffer,createCircularDoublesBuffer,smoothingFrames,completeBufferRoutine,uiFps}=this.__closure;if(circularBuffer.value===null){circularBuffer.value=createCircularDoublesBuffer(smoothingFrames);}timestamp=Math.round(timestamp);const currentFps=completeBufferRoutine(circularBuffer.value,timestamp);uiFps.value=currentFps.toFixed(0);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\component\\\\PerformanceMonitor.tsx\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"PerformanceMonitorTsx5\\\",\\\"timestamp\\\",\\\"circularBuffer\\\",\\\"createCircularDoublesBuffer\\\",\\\"smoothingFrames\\\",\\\"completeBufferRoutine\\\",\\\"uiFps\\\",\\\"__closure\\\",\\\"value\\\",\\\"Math\\\",\\\"round\\\",\\\"currentFps\\\",\\\"toFixed\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/component/PerformanceMonitor.tsx\\\"],\\\"mappings\\\":\\\"AAgImB,QAAC,CAAAA,uBAAA,CAAEC,SAAA,CAAAA,SAAa,CAAS,CAAK,OAAAC,cAAA,CAAAC,2BAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,KAAA,OAAAC,SAAA,CAC7C,GAAIL,cAAc,CAACM,KAAK,GAAK,IAAI,CAAE,CACjCN,cAAc,CAACM,KAAK,CAAGL,2BAA2B,CAACC,eAAe,CAAC,CACrE,CAEAH,SAAS,CAAGQ,IAAI,CAACC,KAAK,CAACT,SAAS,CAAC,CAEjC,KAAM,CAAAU,UAAU,CAAGN,qBAAqB,CAACH,cAAc,CAACM,KAAK,CAAEP,SAAS,CAAC,CAEzEK,KAAK,CAACE,KAAK,CAAGG,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,CACrC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_12791881627073_init_data = {\n    code: \"function PerformanceMonitorTsx6(){const{uiFps}=this.__closure;var _uiFps$value;const text='UI: '+((_uiFps$value=uiFps.value)!==null&&_uiFps$value!==void 0?_uiFps$value:'N/A')+' ';return{text:text,defaultValue:text};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\component\\\\PerformanceMonitor.tsx\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"PerformanceMonitorTsx6\\\",\\\"uiFps\\\",\\\"__closure\\\",\\\"_uiFps$value\\\",\\\"text\\\",\\\"value\\\",\\\"defaultValue\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/component/PerformanceMonitor.tsx\\\"],\\\"mappings\\\":\\\"AA4IyC,SAAAA,sBAAMA,CAAA,QAAAC,KAAA,OAAAC,SAAA,KAAAC,YAAA,CAC3C,KAAM,CAAAC,IAAI,CAAG,MAAM,GAAAD,YAAA,CAAIF,KAAK,CAACI,KAAK,UAAAF,YAAA,UAAAA,YAAA,CAAI,KAAK,CAAC,CAAG,GAAG,CAClD,MAAO,CAAEC,IAAI,CAAJA,IAAI,CAAEE,YAAY,CAAEF,IAAK,CAAC,CACrC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function UiPerformance(_ref2) {\n    var smoothingFrames = _ref2.smoothingFrames;\n    var uiFps = (0, _hook.useSharedValue)(null);\n    var circularBuffer = (0, _hook.useSharedValue)(null);\n    (0, _hook.useFrameCallback)(function () {\n      var _e = [new global.Error(), -6, -27];\n      var PerformanceMonitorTsx5 = function (_ref3) {\n        var timestamp = _ref3.timestamp;\n        if (circularBuffer.value === null) {\n          circularBuffer.value = createCircularDoublesBuffer(smoothingFrames);\n        }\n        timestamp = Math.round(timestamp);\n        var currentFps = completeBufferRoutine(circularBuffer.value, timestamp);\n        uiFps.value = currentFps.toFixed(0);\n      };\n      PerformanceMonitorTsx5.__closure = {\n        circularBuffer,\n        createCircularDoublesBuffer,\n        smoothingFrames,\n        completeBufferRoutine,\n        uiFps\n      };\n      PerformanceMonitorTsx5.__workletHash = 6804949272378;\n      PerformanceMonitorTsx5.__initData = _worklet_6804949272378_init_data;\n      PerformanceMonitorTsx5.__stackDetails = _e;\n      return PerformanceMonitorTsx5;\n    }());\n    var animatedProps = (0, _hook.useAnimatedProps)(function () {\n      var _e = [new global.Error(), -2, -27];\n      var PerformanceMonitorTsx6 = function () {\n        var text = 'UI: ' + (uiFps.value ?? 'N/A') + ' ';\n        return {\n          text,\n          defaultValue: text\n        };\n      };\n      PerformanceMonitorTsx6.__closure = {\n        uiFps\n      };\n      PerformanceMonitorTsx6.__workletHash = 12791881627073;\n      PerformanceMonitorTsx6.__initData = _worklet_12791881627073_init_data;\n      PerformanceMonitorTsx6.__stackDetails = _e;\n      return PerformanceMonitorTsx6;\n    }());\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: styles.container,\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(AnimatedTextInput, {\n        style: styles.text,\n        animatedProps: animatedProps,\n        editable: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 5\n    }, this);\n  }\n  /**\n   * A component that lets you measure fps values on JS and UI threads on both the\n   * Paper and Fabric architectures.\n   *\n   * @param smoothingFrames - Determines amount of saved frames which will be used\n   *   for fps value smoothing.\n   */\n  function PerformanceMonitor(_ref4) {\n    var _ref4$smoothingFrames = _ref4.smoothingFrames,\n      smoothingFrames = _ref4$smoothingFrames === void 0 ? DEFAULT_BUFFER_SIZE : _ref4$smoothingFrames;\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: styles.monitor,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(JsPerformance, {\n        smoothingFrames: smoothingFrames\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(UiPerformance, {\n        smoothingFrames: smoothingFrames\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _reactNative.StyleSheet.create({\n    monitor: {\n      flexDirection: 'row',\n      position: 'absolute',\n      backgroundColor: '#0006',\n      zIndex: 1000\n    },\n    header: {\n      fontSize: 14,\n      color: '#ffff',\n      paddingHorizontal: 5\n    },\n    text: {\n      fontSize: 13,\n      fontVariant: ['tabular-nums'],\n      color: '#ffff',\n      fontFamily: 'monospace',\n      paddingHorizontal: 3\n    },\n    container: {\n      alignItems: 'center',\n      justifyContent: 'center',\n      flexDirection: 'row',\n      flexWrap: 'wrap'\n    }\n  });\n});", "lineCount": 301, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "PerformanceMonitor"], [7, 28, 1, 13], [7, 31, 1, 13, "PerformanceMonitor"], [7, 49, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "_interopRequireWildcard"], [8, 38, 3, 0], [8, 39, 3, 0, "require"], [8, 46, 3, 0], [8, 47, 3, 0, "_dependencyMap"], [8, 61, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_reactNative"], [9, 18, 4, 0], [9, 21, 4, 0, "require"], [9, 28, 4, 0], [9, 29, 4, 0, "_dependencyMap"], [9, 43, 4, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_ConfigHelper"], [10, 19, 6, 0], [10, 22, 6, 0, "require"], [10, 29, 6, 0], [10, 30, 6, 0, "_dependencyMap"], [10, 44, 6, 0], [11, 2, 7, 0], [11, 6, 7, 0, "_createAnimatedComponent"], [11, 30, 7, 0], [11, 33, 7, 0, "require"], [11, 40, 7, 0], [11, 41, 7, 0, "_dependencyMap"], [11, 55, 7, 0], [12, 2, 9, 0], [12, 6, 9, 0, "_hook"], [12, 11, 9, 0], [12, 14, 9, 0, "require"], [12, 21, 9, 0], [12, 22, 9, 0, "_dependencyMap"], [12, 36, 9, 0], [13, 2, 9, 77], [13, 6, 9, 77, "_jsxDevRuntime"], [13, 20, 9, 77], [13, 23, 9, 77, "require"], [13, 30, 9, 77], [13, 31, 9, 77, "_dependencyMap"], [13, 45, 9, 77], [14, 2, 9, 77], [14, 6, 9, 77, "_jsxFileName"], [14, 18, 9, 77], [15, 2, 9, 77], [15, 11, 9, 77, "_interopRequireWildcard"], [15, 35, 9, 77, "e"], [15, 36, 9, 77], [15, 38, 9, 77, "t"], [15, 39, 9, 77], [15, 68, 9, 77, "WeakMap"], [15, 75, 9, 77], [15, 81, 9, 77, "r"], [15, 82, 9, 77], [15, 89, 9, 77, "WeakMap"], [15, 96, 9, 77], [15, 100, 9, 77, "n"], [15, 101, 9, 77], [15, 108, 9, 77, "WeakMap"], [15, 115, 9, 77], [15, 127, 9, 77, "_interopRequireWildcard"], [15, 150, 9, 77], [15, 162, 9, 77, "_interopRequireWildcard"], [15, 163, 9, 77, "e"], [15, 164, 9, 77], [15, 166, 9, 77, "t"], [15, 167, 9, 77], [15, 176, 9, 77, "t"], [15, 177, 9, 77], [15, 181, 9, 77, "e"], [15, 182, 9, 77], [15, 186, 9, 77, "e"], [15, 187, 9, 77], [15, 188, 9, 77, "__esModule"], [15, 198, 9, 77], [15, 207, 9, 77, "e"], [15, 208, 9, 77], [15, 214, 9, 77, "o"], [15, 215, 9, 77], [15, 217, 9, 77, "i"], [15, 218, 9, 77], [15, 220, 9, 77, "f"], [15, 221, 9, 77], [15, 226, 9, 77, "__proto__"], [15, 235, 9, 77], [15, 243, 9, 77, "default"], [15, 250, 9, 77], [15, 252, 9, 77, "e"], [15, 253, 9, 77], [15, 270, 9, 77, "e"], [15, 271, 9, 77], [15, 294, 9, 77, "e"], [15, 295, 9, 77], [15, 320, 9, 77, "e"], [15, 321, 9, 77], [15, 330, 9, 77, "f"], [15, 331, 9, 77], [15, 337, 9, 77, "o"], [15, 338, 9, 77], [15, 341, 9, 77, "t"], [15, 342, 9, 77], [15, 345, 9, 77, "n"], [15, 346, 9, 77], [15, 349, 9, 77, "r"], [15, 350, 9, 77], [15, 358, 9, 77, "o"], [15, 359, 9, 77], [15, 360, 9, 77, "has"], [15, 363, 9, 77], [15, 364, 9, 77, "e"], [15, 365, 9, 77], [15, 375, 9, 77, "o"], [15, 376, 9, 77], [15, 377, 9, 77, "get"], [15, 380, 9, 77], [15, 381, 9, 77, "e"], [15, 382, 9, 77], [15, 385, 9, 77, "o"], [15, 386, 9, 77], [15, 387, 9, 77, "set"], [15, 390, 9, 77], [15, 391, 9, 77, "e"], [15, 392, 9, 77], [15, 394, 9, 77, "f"], [15, 395, 9, 77], [15, 409, 9, 77, "_t"], [15, 411, 9, 77], [15, 415, 9, 77, "e"], [15, 416, 9, 77], [15, 432, 9, 77, "_t"], [15, 434, 9, 77], [15, 441, 9, 77, "hasOwnProperty"], [15, 455, 9, 77], [15, 456, 9, 77, "call"], [15, 460, 9, 77], [15, 461, 9, 77, "e"], [15, 462, 9, 77], [15, 464, 9, 77, "_t"], [15, 466, 9, 77], [15, 473, 9, 77, "i"], [15, 474, 9, 77], [15, 478, 9, 77, "o"], [15, 479, 9, 77], [15, 482, 9, 77, "Object"], [15, 488, 9, 77], [15, 489, 9, 77, "defineProperty"], [15, 503, 9, 77], [15, 508, 9, 77, "Object"], [15, 514, 9, 77], [15, 515, 9, 77, "getOwnPropertyDescriptor"], [15, 539, 9, 77], [15, 540, 9, 77, "e"], [15, 541, 9, 77], [15, 543, 9, 77, "_t"], [15, 545, 9, 77], [15, 552, 9, 77, "i"], [15, 553, 9, 77], [15, 554, 9, 77, "get"], [15, 557, 9, 77], [15, 561, 9, 77, "i"], [15, 562, 9, 77], [15, 563, 9, 77, "set"], [15, 566, 9, 77], [15, 570, 9, 77, "o"], [15, 571, 9, 77], [15, 572, 9, 77, "f"], [15, 573, 9, 77], [15, 575, 9, 77, "_t"], [15, 577, 9, 77], [15, 579, 9, 77, "i"], [15, 580, 9, 77], [15, 584, 9, 77, "f"], [15, 585, 9, 77], [15, 586, 9, 77, "_t"], [15, 588, 9, 77], [15, 592, 9, 77, "e"], [15, 593, 9, 77], [15, 594, 9, 77, "_t"], [15, 596, 9, 77], [15, 607, 9, 77, "f"], [15, 608, 9, 77], [15, 613, 9, 77, "e"], [15, 614, 9, 77], [15, 616, 9, 77, "t"], [15, 617, 9, 77], [16, 2, 9, 77], [16, 6, 9, 77, "_worklet_7025330993767_init_data"], [16, 38, 9, 77], [17, 4, 9, 77, "code"], [17, 8, 9, 77], [18, 4, 9, 77, "location"], [18, 12, 9, 77], [19, 4, 9, 77, "sourceMap"], [19, 13, 9, 77], [20, 4, 9, 77, "version"], [20, 11, 9, 77], [21, 2, 9, 77], [22, 2, 9, 77], [22, 6, 9, 77, "createCircularDoublesBuffer"], [22, 33, 9, 77], [22, 36, 12, 0], [23, 4, 12, 0], [23, 8, 12, 0, "_e"], [23, 10, 12, 0], [23, 18, 12, 0, "global"], [23, 24, 12, 0], [23, 25, 12, 0, "Error"], [23, 30, 12, 0], [24, 4, 12, 0], [24, 8, 12, 0, "createCircularDoublesBuffer"], [24, 35, 12, 0], [24, 47, 12, 0, "createCircularDoublesBuffer"], [24, 48, 12, 37, "size"], [24, 52, 12, 49], [24, 54, 12, 51], [25, 6, 15, 2], [25, 13, 15, 9], [26, 8, 16, 4, "next"], [26, 12, 16, 8], [26, 14, 16, 10], [26, 15, 16, 21], [27, 8, 17, 4, "buffer"], [27, 14, 17, 10], [27, 16, 17, 12], [27, 20, 17, 16, "Float32Array"], [27, 32, 17, 28], [27, 33, 17, 29, "size"], [27, 37, 17, 33], [27, 38, 17, 34], [28, 8, 18, 4, "size"], [28, 12, 18, 8], [29, 8, 19, 4, "count"], [29, 13, 19, 9], [29, 15, 19, 11], [29, 16, 19, 22], [30, 8, 21, 4, "push"], [30, 12, 21, 8, "push"], [30, 13, 21, 9, "value"], [30, 18, 21, 22], [30, 20, 21, 39], [31, 10, 22, 6], [31, 14, 22, 12, "oldValue"], [31, 22, 22, 20], [31, 25, 22, 23], [31, 29, 22, 27], [31, 30, 22, 28, "buffer"], [31, 36, 22, 34], [31, 37, 22, 35], [31, 41, 22, 39], [31, 42, 22, 40, "next"], [31, 46, 22, 44], [31, 47, 22, 45], [32, 10, 23, 6], [32, 14, 23, 12, "oldCount"], [32, 22, 23, 20], [32, 25, 23, 23], [32, 29, 23, 27], [32, 30, 23, 28, "count"], [32, 35, 23, 33], [33, 10, 24, 6], [33, 14, 24, 10], [33, 15, 24, 11, "buffer"], [33, 21, 24, 17], [33, 22, 24, 18], [33, 26, 24, 22], [33, 27, 24, 23, "next"], [33, 31, 24, 27], [33, 32, 24, 28], [33, 35, 24, 31, "value"], [33, 40, 24, 36], [34, 10, 26, 6], [34, 14, 26, 10], [34, 15, 26, 11, "next"], [34, 19, 26, 15], [34, 22, 26, 18], [34, 23, 26, 19], [34, 27, 26, 23], [34, 28, 26, 24, "next"], [34, 32, 26, 28], [34, 35, 26, 31], [34, 36, 26, 32], [34, 40, 26, 36], [34, 44, 26, 40], [34, 45, 26, 41, "size"], [34, 49, 26, 45], [35, 10, 27, 6], [35, 14, 27, 10], [35, 15, 27, 11, "count"], [35, 20, 27, 16], [35, 23, 27, 19, "Math"], [35, 27, 27, 23], [35, 28, 27, 24, "min"], [35, 31, 27, 27], [35, 32, 27, 28], [35, 36, 27, 32], [35, 37, 27, 33, "size"], [35, 41, 27, 37], [35, 43, 27, 39], [35, 47, 27, 43], [35, 48, 27, 44, "count"], [35, 53, 27, 49], [35, 56, 27, 52], [35, 57, 27, 53], [35, 58, 27, 54], [36, 10, 28, 6], [36, 17, 28, 13, "oldCount"], [36, 25, 28, 21], [36, 30, 28, 26], [36, 34, 28, 30], [36, 35, 28, 31, "size"], [36, 39, 28, 35], [36, 42, 28, 38, "oldValue"], [36, 50, 28, 46], [36, 53, 28, 49], [36, 57, 28, 53], [37, 8, 29, 4], [37, 9, 29, 5], [38, 8, 31, 4, "front"], [38, 13, 31, 9, "front"], [38, 14, 31, 9], [38, 16, 31, 27], [39, 10, 32, 6], [39, 14, 32, 12, "notEmpty"], [39, 22, 32, 20], [39, 25, 32, 23], [39, 29, 32, 27], [39, 30, 32, 28, "count"], [39, 35, 32, 33], [39, 38, 32, 36], [39, 39, 32, 37], [40, 10, 33, 6], [40, 14, 33, 10, "notEmpty"], [40, 22, 33, 18], [40, 24, 33, 20], [41, 12, 34, 8], [41, 16, 34, 14, "current"], [41, 23, 34, 21], [41, 26, 34, 24], [41, 30, 34, 28], [41, 31, 34, 29, "next"], [41, 35, 34, 33], [41, 38, 34, 36], [41, 39, 34, 37], [42, 12, 35, 8], [42, 16, 35, 14, "index"], [42, 21, 35, 19], [42, 24, 35, 22, "current"], [42, 31, 35, 29], [42, 34, 35, 32], [42, 35, 35, 33], [42, 38, 35, 36], [42, 42, 35, 40], [42, 43, 35, 41, "size"], [42, 47, 35, 45], [42, 50, 35, 48], [42, 51, 35, 49], [42, 54, 35, 52, "current"], [42, 61, 35, 59], [43, 12, 36, 8], [43, 19, 36, 15], [43, 23, 36, 19], [43, 24, 36, 20, "buffer"], [43, 30, 36, 26], [43, 31, 36, 27, "index"], [43, 36, 36, 32], [43, 37, 36, 33], [44, 10, 37, 6], [45, 10, 38, 6], [45, 17, 38, 13], [45, 21, 38, 17], [46, 8, 39, 4], [46, 9, 39, 5], [47, 8, 41, 4, "back"], [47, 12, 41, 8, "back"], [47, 13, 41, 8], [47, 15, 41, 26], [48, 10, 42, 6], [48, 14, 42, 12, "notEmpty"], [48, 22, 42, 20], [48, 25, 42, 23], [48, 29, 42, 27], [48, 30, 42, 28, "count"], [48, 35, 42, 33], [48, 38, 42, 36], [48, 39, 42, 37], [49, 10, 43, 6], [49, 17, 43, 13, "notEmpty"], [49, 25, 43, 21], [49, 28, 43, 24], [49, 32, 43, 28], [49, 33, 43, 29, "buffer"], [49, 39, 43, 35], [49, 40, 43, 36], [49, 44, 43, 40], [49, 45, 43, 41, "next"], [49, 49, 43, 45], [49, 50, 43, 46], [49, 53, 43, 49], [49, 57, 43, 53], [50, 8, 44, 4], [51, 6, 45, 2], [51, 7, 45, 3], [52, 4, 46, 0], [52, 5, 46, 1], [53, 4, 46, 1, "createCircularDoublesBuffer"], [53, 31, 46, 1], [53, 32, 46, 1, "__closure"], [53, 41, 46, 1], [54, 4, 46, 1, "createCircularDoublesBuffer"], [54, 31, 46, 1], [54, 32, 46, 1, "__workletHash"], [54, 45, 46, 1], [55, 4, 46, 1, "createCircularDoublesBuffer"], [55, 31, 46, 1], [55, 32, 46, 1, "__initData"], [55, 42, 46, 1], [55, 45, 46, 1, "_worklet_7025330993767_init_data"], [55, 77, 46, 1], [56, 4, 46, 1, "createCircularDoublesBuffer"], [56, 31, 46, 1], [56, 32, 46, 1, "__stackDetails"], [56, 46, 46, 1], [56, 49, 46, 1, "_e"], [56, 51, 46, 1], [57, 4, 46, 1], [57, 11, 46, 1, "createCircularDoublesBuffer"], [57, 38, 46, 1], [58, 2, 46, 1], [58, 3, 12, 0], [59, 2, 48, 0], [59, 6, 48, 6, "DEFAULT_BUFFER_SIZE"], [59, 25, 48, 25], [59, 28, 48, 28], [59, 30, 48, 30], [60, 2, 49, 0], [60, 6, 49, 0, "addWhitelistedNativeProps"], [60, 45, 49, 25], [60, 47, 49, 26], [61, 4, 49, 28, "text"], [61, 8, 49, 32], [61, 10, 49, 34], [62, 2, 49, 39], [62, 3, 49, 40], [62, 4, 49, 41], [63, 2, 50, 0], [63, 6, 50, 6, "AnimatedTextInput"], [63, 23, 50, 23], [63, 26, 50, 26], [63, 30, 50, 26, "createAnimatedComponent"], [63, 78, 50, 49], [63, 80, 50, 50, "TextInput"], [63, 102, 50, 59], [63, 103, 50, 60], [64, 2, 52, 0], [64, 11, 52, 9, "loopAnimationFrame"], [64, 29, 52, 27, "loopAnimationFrame"], [64, 30, 52, 28, "fn"], [64, 32, 52, 72], [64, 34, 52, 74], [65, 4, 53, 2], [65, 8, 53, 6, "lastTime"], [65, 16, 53, 14], [65, 19, 53, 17], [65, 20, 53, 18], [66, 4, 55, 2], [66, 13, 55, 11, "loop"], [66, 17, 55, 15, "loop"], [66, 18, 55, 15], [66, 20, 55, 18], [67, 6, 56, 4, "requestAnimationFrame"], [67, 27, 56, 25], [67, 28, 56, 27, "time"], [67, 32, 56, 31], [67, 36, 56, 36], [68, 8, 57, 6], [68, 12, 57, 10, "lastTime"], [68, 20, 57, 18], [68, 23, 57, 21], [68, 24, 57, 22], [68, 26, 57, 24], [69, 10, 58, 8, "fn"], [69, 12, 58, 10], [69, 13, 58, 11, "lastTime"], [69, 21, 58, 19], [69, 23, 58, 21, "time"], [69, 27, 58, 25], [69, 28, 58, 26], [70, 8, 59, 6], [71, 8, 60, 6, "lastTime"], [71, 16, 60, 14], [71, 19, 60, 17, "time"], [71, 23, 60, 21], [72, 8, 61, 6, "requestAnimationFrame"], [72, 29, 61, 27], [72, 30, 61, 28, "loop"], [72, 34, 61, 32], [72, 35, 61, 33], [73, 6, 62, 4], [73, 7, 62, 5], [73, 8, 62, 6], [74, 4, 63, 2], [75, 4, 65, 2, "loop"], [75, 8, 65, 6], [75, 9, 65, 7], [75, 10, 65, 8], [76, 2, 66, 0], [77, 2, 66, 1], [77, 6, 66, 1, "_worklet_4328451430280_init_data"], [77, 38, 66, 1], [78, 4, 66, 1, "code"], [78, 8, 66, 1], [79, 4, 66, 1, "location"], [79, 12, 66, 1], [80, 4, 66, 1, "sourceMap"], [80, 13, 66, 1], [81, 4, 66, 1, "version"], [81, 11, 66, 1], [82, 2, 66, 1], [83, 2, 66, 1], [83, 6, 66, 1, "getFps"], [83, 12, 66, 1], [83, 15, 68, 0], [84, 4, 68, 0], [84, 8, 68, 0, "_e"], [84, 10, 68, 0], [84, 18, 68, 0, "global"], [84, 24, 68, 0], [84, 25, 68, 0, "Error"], [84, 30, 68, 0], [85, 4, 68, 0], [85, 8, 68, 0, "getFps"], [85, 14, 68, 0], [85, 26, 68, 0, "getFps"], [85, 27, 68, 16, "renderTimeInMs"], [85, 41, 68, 38], [85, 43, 68, 48], [86, 6, 70, 2], [86, 13, 70, 9], [86, 17, 70, 13], [86, 20, 70, 16, "renderTimeInMs"], [86, 34, 70, 30], [87, 4, 71, 0], [87, 5, 71, 1], [88, 4, 71, 1, "getFps"], [88, 10, 71, 1], [88, 11, 71, 1, "__closure"], [88, 20, 71, 1], [89, 4, 71, 1, "getFps"], [89, 10, 71, 1], [89, 11, 71, 1, "__workletHash"], [89, 24, 71, 1], [90, 4, 71, 1, "getFps"], [90, 10, 71, 1], [90, 11, 71, 1, "__initData"], [90, 21, 71, 1], [90, 24, 71, 1, "_worklet_4328451430280_init_data"], [90, 56, 71, 1], [91, 4, 71, 1, "getFps"], [91, 10, 71, 1], [91, 11, 71, 1, "__stackDetails"], [91, 25, 71, 1], [91, 28, 71, 1, "_e"], [91, 30, 71, 1], [92, 4, 71, 1], [92, 11, 71, 1, "getFps"], [92, 17, 71, 1], [93, 2, 71, 1], [93, 3, 68, 0], [94, 2, 68, 0], [94, 6, 68, 0, "_worklet_3765204747727_init_data"], [94, 38, 68, 0], [95, 4, 68, 0, "code"], [95, 8, 68, 0], [96, 4, 68, 0, "location"], [96, 12, 68, 0], [97, 4, 68, 0, "sourceMap"], [97, 13, 68, 0], [98, 4, 68, 0, "version"], [98, 11, 68, 0], [99, 2, 68, 0], [100, 2, 68, 0], [100, 6, 68, 0, "completeBufferRoutine"], [100, 27, 68, 0], [100, 30, 73, 0], [101, 4, 73, 0], [101, 8, 73, 0, "_e"], [101, 10, 73, 0], [101, 18, 73, 0, "global"], [101, 24, 73, 0], [101, 25, 73, 0, "Error"], [101, 30, 73, 0], [102, 4, 73, 0], [102, 8, 73, 0, "completeBufferRoutine"], [102, 29, 73, 0], [102, 41, 73, 0, "completeBufferRoutine"], [102, 42, 74, 2, "buffer"], [102, 48, 74, 24], [102, 50, 75, 2, "timestamp"], [102, 59, 75, 19], [102, 61, 76, 10], [103, 6, 78, 2, "timestamp"], [103, 15, 78, 11], [103, 18, 78, 14, "Math"], [103, 22, 78, 18], [103, 23, 78, 19, "round"], [103, 28, 78, 24], [103, 29, 78, 25, "timestamp"], [103, 38, 78, 34], [103, 39, 78, 35], [104, 6, 80, 2], [104, 10, 80, 8, "droppedTimestamp"], [104, 26, 80, 24], [104, 29, 80, 27, "buffer"], [104, 35, 80, 33], [104, 36, 80, 34, "push"], [104, 40, 80, 38], [104, 41, 80, 39, "timestamp"], [104, 50, 80, 48], [104, 51, 80, 49], [104, 55, 80, 53, "timestamp"], [104, 64, 80, 62], [105, 6, 82, 2], [105, 10, 82, 8, "measuredRangeDuration"], [105, 31, 82, 29], [105, 34, 82, 32, "timestamp"], [105, 43, 82, 41], [105, 46, 82, 44, "droppedTimestamp"], [105, 62, 82, 60], [106, 6, 84, 2], [106, 13, 84, 9, "getFps"], [106, 19, 84, 15], [106, 20, 84, 16, "measuredRangeDuration"], [106, 41, 84, 37], [106, 44, 84, 40, "buffer"], [106, 50, 84, 46], [106, 51, 84, 47, "count"], [106, 56, 84, 52], [106, 57, 84, 53], [107, 4, 85, 0], [107, 5, 85, 1], [108, 4, 85, 1, "completeBufferRoutine"], [108, 25, 85, 1], [108, 26, 85, 1, "__closure"], [108, 35, 85, 1], [109, 6, 85, 1, "getFps"], [110, 4, 85, 1], [111, 4, 85, 1, "completeBufferRoutine"], [111, 25, 85, 1], [111, 26, 85, 1, "__workletHash"], [111, 39, 85, 1], [112, 4, 85, 1, "completeBufferRoutine"], [112, 25, 85, 1], [112, 26, 85, 1, "__initData"], [112, 36, 85, 1], [112, 39, 85, 1, "_worklet_3765204747727_init_data"], [112, 71, 85, 1], [113, 4, 85, 1, "completeBufferRoutine"], [113, 25, 85, 1], [113, 26, 85, 1, "__stackDetails"], [113, 40, 85, 1], [113, 43, 85, 1, "_e"], [113, 45, 85, 1], [114, 4, 85, 1], [114, 11, 85, 1, "completeBufferRoutine"], [114, 32, 85, 1], [115, 2, 85, 1], [115, 3, 73, 0], [116, 2, 73, 0], [116, 6, 73, 0, "_worklet_6153571000774_init_data"], [116, 38, 73, 0], [117, 4, 73, 0, "code"], [117, 8, 73, 0], [118, 4, 73, 0, "location"], [118, 12, 73, 0], [119, 4, 73, 0, "sourceMap"], [119, 13, 73, 0], [120, 4, 73, 0, "version"], [120, 11, 73, 0], [121, 2, 73, 0], [122, 2, 87, 0], [122, 11, 87, 9, "JsPerformance"], [122, 24, 87, 22, "JsPerformance"], [122, 25, 87, 22, "_ref"], [122, 29, 87, 22], [122, 31, 87, 73], [123, 4, 87, 73], [123, 8, 87, 25, "smoothingFrames"], [123, 23, 87, 40], [123, 26, 87, 40, "_ref"], [123, 30, 87, 40], [123, 31, 87, 25, "smoothingFrames"], [123, 46, 87, 40], [124, 4, 88, 2], [124, 8, 88, 8, "jsFps"], [124, 13, 88, 13], [124, 16, 88, 16], [124, 20, 88, 16, "useSharedValue"], [124, 40, 88, 30], [124, 42, 88, 46], [124, 46, 88, 50], [124, 47, 88, 51], [125, 4, 89, 2], [125, 8, 89, 8, "totalRenderTime"], [125, 23, 89, 23], [125, 26, 89, 26], [125, 30, 89, 26, "useSharedValue"], [125, 50, 89, 40], [125, 52, 89, 41], [125, 53, 89, 42], [125, 54, 89, 43], [126, 4, 90, 2], [126, 8, 90, 8, "circular<PERSON>uffer"], [126, 22, 90, 22], [126, 25, 90, 25], [126, 29, 90, 25, "useRef"], [126, 42, 90, 31], [126, 44, 91, 4, "createCircularDoublesBuffer"], [126, 71, 91, 31], [126, 72, 91, 32, "smoothingFrames"], [126, 87, 91, 47], [126, 88, 92, 2], [126, 89, 92, 3], [127, 4, 94, 2], [127, 8, 94, 2, "useEffect"], [127, 24, 94, 11], [127, 26, 94, 12], [127, 32, 94, 18], [128, 6, 95, 4, "loopAnimationFrame"], [128, 24, 95, 22], [128, 25, 95, 23], [128, 26, 95, 24, "_"], [128, 27, 95, 25], [128, 29, 95, 27, "timestamp"], [128, 38, 95, 36], [128, 43, 95, 41], [129, 8, 96, 6, "timestamp"], [129, 17, 96, 15], [129, 20, 96, 18, "Math"], [129, 24, 96, 22], [129, 25, 96, 23, "round"], [129, 30, 96, 28], [129, 31, 96, 29, "timestamp"], [129, 40, 96, 38], [129, 41, 96, 39], [130, 8, 98, 6], [130, 12, 98, 12, "currentFps"], [130, 22, 98, 22], [130, 25, 98, 25, "completeBufferRoutine"], [130, 46, 98, 46], [130, 47, 99, 8, "circular<PERSON>uffer"], [130, 61, 99, 22], [130, 62, 99, 23, "current"], [130, 69, 99, 30], [130, 71, 100, 8, "timestamp"], [130, 80, 101, 6], [130, 81, 101, 7], [132, 8, 103, 6], [133, 8, 104, 6], [134, 8, 105, 6, "jsFps"], [134, 13, 105, 11], [134, 14, 105, 12, "value"], [134, 19, 105, 17], [134, 22, 105, 20], [134, 23, 105, 21, "currentFps"], [134, 33, 105, 31], [134, 36, 105, 34], [134, 37, 105, 35], [134, 39, 105, 37, "toFixed"], [134, 46, 105, 44], [134, 47, 105, 45], [134, 48, 105, 46], [134, 49, 105, 47], [135, 6, 106, 4], [135, 7, 106, 5], [135, 8, 106, 6], [136, 4, 107, 2], [136, 5, 107, 3], [136, 7, 107, 5], [136, 8, 107, 6, "jsFps"], [136, 13, 107, 11], [136, 15, 107, 13, "totalRenderTime"], [136, 30, 107, 28], [136, 31, 107, 29], [136, 32, 107, 30], [137, 4, 109, 2], [137, 8, 109, 8, "animatedProps"], [137, 21, 109, 21], [137, 24, 109, 24], [137, 28, 109, 24, "useAnimatedProps"], [137, 50, 109, 40], [137, 52, 109, 41], [138, 6, 109, 41], [138, 10, 109, 41, "_e"], [138, 12, 109, 41], [138, 20, 109, 41, "global"], [138, 26, 109, 41], [138, 27, 109, 41, "Error"], [138, 32, 109, 41], [139, 6, 109, 41], [139, 10, 109, 41, "PerformanceMonitorTsx4"], [139, 32, 109, 41], [139, 44, 109, 41, "PerformanceMonitorTsx4"], [139, 45, 109, 41], [139, 47, 109, 47], [140, 8, 110, 4], [140, 12, 110, 10, "text"], [140, 16, 110, 14], [140, 19, 110, 17], [140, 25, 110, 23], [140, 29, 110, 27, "jsFps"], [140, 34, 110, 32], [140, 35, 110, 33, "value"], [140, 40, 110, 38], [140, 44, 110, 42], [140, 49, 110, 47], [140, 50, 110, 48], [140, 53, 110, 51], [140, 56, 110, 54], [141, 8, 111, 4], [141, 15, 111, 11], [142, 10, 111, 13, "text"], [142, 14, 111, 17], [143, 10, 111, 19, "defaultValue"], [143, 22, 111, 31], [143, 24, 111, 33, "text"], [144, 8, 111, 38], [144, 9, 111, 39], [145, 6, 112, 2], [145, 7, 112, 3], [146, 6, 112, 3, "PerformanceMonitorTsx4"], [146, 28, 112, 3], [146, 29, 112, 3, "__closure"], [146, 38, 112, 3], [147, 8, 112, 3, "jsFps"], [148, 6, 112, 3], [149, 6, 112, 3, "PerformanceMonitorTsx4"], [149, 28, 112, 3], [149, 29, 112, 3, "__workletHash"], [149, 42, 112, 3], [150, 6, 112, 3, "PerformanceMonitorTsx4"], [150, 28, 112, 3], [150, 29, 112, 3, "__initData"], [150, 39, 112, 3], [150, 42, 112, 3, "_worklet_6153571000774_init_data"], [150, 74, 112, 3], [151, 6, 112, 3, "PerformanceMonitorTsx4"], [151, 28, 112, 3], [151, 29, 112, 3, "__stackDetails"], [151, 43, 112, 3], [151, 46, 112, 3, "_e"], [151, 48, 112, 3], [152, 6, 112, 3], [152, 13, 112, 3, "PerformanceMonitorTsx4"], [152, 35, 112, 3], [153, 4, 112, 3], [153, 5, 109, 41], [153, 7, 112, 3], [153, 8, 112, 4], [154, 4, 114, 2], [154, 24, 115, 4], [154, 28, 115, 4, "_jsxDevRuntime"], [154, 42, 115, 4], [154, 43, 115, 4, "jsxDEV"], [154, 49, 115, 4], [154, 51, 115, 5, "_reactNative"], [154, 63, 115, 5], [154, 64, 115, 5, "View"], [154, 68, 115, 9], [155, 6, 115, 10, "style"], [155, 11, 115, 15], [155, 13, 115, 17, "styles"], [155, 19, 115, 23], [155, 20, 115, 24, "container"], [155, 29, 115, 34], [156, 6, 115, 34, "children"], [156, 14, 115, 34], [156, 29, 116, 6], [156, 33, 116, 6, "_jsxDevRuntime"], [156, 47, 116, 6], [156, 48, 116, 6, "jsxDEV"], [156, 54, 116, 6], [156, 56, 116, 7, "AnimatedTextInput"], [156, 73, 116, 24], [157, 8, 117, 8, "style"], [157, 13, 117, 13], [157, 15, 117, 15, "styles"], [157, 21, 117, 21], [157, 22, 117, 22, "text"], [157, 26, 117, 27], [158, 8, 118, 8, "animatedProps"], [158, 21, 118, 21], [158, 23, 118, 23, "animatedProps"], [158, 36, 118, 37], [159, 8, 119, 8, "editable"], [159, 16, 119, 16], [159, 18, 119, 18], [160, 6, 119, 24], [161, 8, 119, 24, "fileName"], [161, 16, 119, 24], [161, 18, 119, 24, "_jsxFileName"], [161, 30, 119, 24], [162, 8, 119, 24, "lineNumber"], [162, 18, 119, 24], [163, 8, 119, 24, "columnNumber"], [163, 20, 119, 24], [164, 6, 119, 24], [164, 13, 120, 7], [165, 4, 120, 8], [166, 6, 120, 8, "fileName"], [166, 14, 120, 8], [166, 16, 120, 8, "_jsxFileName"], [166, 28, 120, 8], [167, 6, 120, 8, "lineNumber"], [167, 16, 120, 8], [168, 6, 120, 8, "columnNumber"], [168, 18, 120, 8], [169, 4, 120, 8], [169, 11, 121, 10], [169, 12, 121, 11], [170, 2, 123, 0], [171, 2, 123, 1], [171, 6, 123, 1, "_worklet_6804949272378_init_data"], [171, 38, 123, 1], [172, 4, 123, 1, "code"], [172, 8, 123, 1], [173, 4, 123, 1, "location"], [173, 12, 123, 1], [174, 4, 123, 1, "sourceMap"], [174, 13, 123, 1], [175, 4, 123, 1, "version"], [175, 11, 123, 1], [176, 2, 123, 1], [177, 2, 123, 1], [177, 6, 123, 1, "_worklet_12791881627073_init_data"], [177, 39, 123, 1], [178, 4, 123, 1, "code"], [178, 8, 123, 1], [179, 4, 123, 1, "location"], [179, 12, 123, 1], [180, 4, 123, 1, "sourceMap"], [180, 13, 123, 1], [181, 4, 123, 1, "version"], [181, 11, 123, 1], [182, 2, 123, 1], [183, 2, 125, 0], [183, 11, 125, 9, "UiPerformance"], [183, 24, 125, 22, "UiPerformance"], [183, 25, 125, 22, "_ref2"], [183, 30, 125, 22], [183, 32, 125, 73], [184, 4, 125, 73], [184, 8, 125, 25, "smoothingFrames"], [184, 23, 125, 40], [184, 26, 125, 40, "_ref2"], [184, 31, 125, 40], [184, 32, 125, 25, "smoothingFrames"], [184, 47, 125, 40], [185, 4, 126, 2], [185, 8, 126, 8, "uiFps"], [185, 13, 126, 13], [185, 16, 126, 16], [185, 20, 126, 16, "useSharedValue"], [185, 40, 126, 30], [185, 42, 126, 46], [185, 46, 126, 50], [185, 47, 126, 51], [186, 4, 127, 2], [186, 8, 127, 8, "circular<PERSON>uffer"], [186, 22, 127, 22], [186, 25, 127, 25], [186, 29, 127, 25, "useSharedValue"], [186, 49, 127, 39], [186, 51, 127, 63], [186, 55, 127, 67], [186, 56, 127, 68], [187, 4, 129, 2], [187, 8, 129, 2, "useFrameCallback"], [187, 30, 129, 18], [187, 32, 129, 19], [188, 6, 129, 19], [188, 10, 129, 19, "_e"], [188, 12, 129, 19], [188, 20, 129, 19, "global"], [188, 26, 129, 19], [188, 27, 129, 19, "Error"], [188, 32, 129, 19], [189, 6, 129, 19], [189, 10, 129, 19, "PerformanceMonitorTsx5"], [189, 32, 129, 19], [189, 44, 129, 19, "PerformanceMonitorTsx5"], [189, 45, 129, 19, "_ref3"], [189, 50, 129, 19], [189, 52, 129, 49], [190, 8, 129, 49], [190, 12, 129, 22, "timestamp"], [190, 21, 129, 31], [190, 24, 129, 31, "_ref3"], [190, 29, 129, 31], [190, 30, 129, 22, "timestamp"], [190, 39, 129, 31], [191, 8, 130, 4], [191, 12, 130, 8, "circular<PERSON>uffer"], [191, 26, 130, 22], [191, 27, 130, 23, "value"], [191, 32, 130, 28], [191, 37, 130, 33], [191, 41, 130, 37], [191, 43, 130, 39], [192, 10, 131, 6, "circular<PERSON>uffer"], [192, 24, 131, 20], [192, 25, 131, 21, "value"], [192, 30, 131, 26], [192, 33, 131, 29, "createCircularDoublesBuffer"], [192, 60, 131, 56], [192, 61, 131, 57, "smoothingFrames"], [192, 76, 131, 72], [192, 77, 131, 73], [193, 8, 132, 4], [194, 8, 134, 4, "timestamp"], [194, 17, 134, 13], [194, 20, 134, 16, "Math"], [194, 24, 134, 20], [194, 25, 134, 21, "round"], [194, 30, 134, 26], [194, 31, 134, 27, "timestamp"], [194, 40, 134, 36], [194, 41, 134, 37], [195, 8, 136, 4], [195, 12, 136, 10, "currentFps"], [195, 22, 136, 20], [195, 25, 136, 23, "completeBufferRoutine"], [195, 46, 136, 44], [195, 47, 136, 45, "circular<PERSON>uffer"], [195, 61, 136, 59], [195, 62, 136, 60, "value"], [195, 67, 136, 65], [195, 69, 136, 67, "timestamp"], [195, 78, 136, 76], [195, 79, 136, 77], [196, 8, 138, 4, "uiFps"], [196, 13, 138, 9], [196, 14, 138, 10, "value"], [196, 19, 138, 15], [196, 22, 138, 18, "currentFps"], [196, 32, 138, 28], [196, 33, 138, 29, "toFixed"], [196, 40, 138, 36], [196, 41, 138, 37], [196, 42, 138, 38], [196, 43, 138, 39], [197, 6, 139, 2], [197, 7, 139, 3], [198, 6, 139, 3, "PerformanceMonitorTsx5"], [198, 28, 139, 3], [198, 29, 139, 3, "__closure"], [198, 38, 139, 3], [199, 8, 139, 3, "circular<PERSON>uffer"], [199, 22, 139, 3], [200, 8, 139, 3, "createCircularDoublesBuffer"], [200, 35, 139, 3], [201, 8, 139, 3, "smoothingFrames"], [201, 23, 139, 3], [202, 8, 139, 3, "completeBufferRoutine"], [202, 29, 139, 3], [203, 8, 139, 3, "uiFps"], [204, 6, 139, 3], [205, 6, 139, 3, "PerformanceMonitorTsx5"], [205, 28, 139, 3], [205, 29, 139, 3, "__workletHash"], [205, 42, 139, 3], [206, 6, 139, 3, "PerformanceMonitorTsx5"], [206, 28, 139, 3], [206, 29, 139, 3, "__initData"], [206, 39, 139, 3], [206, 42, 139, 3, "_worklet_6804949272378_init_data"], [206, 74, 139, 3], [207, 6, 139, 3, "PerformanceMonitorTsx5"], [207, 28, 139, 3], [207, 29, 139, 3, "__stackDetails"], [207, 43, 139, 3], [207, 46, 139, 3, "_e"], [207, 48, 139, 3], [208, 6, 139, 3], [208, 13, 139, 3, "PerformanceMonitorTsx5"], [208, 35, 139, 3], [209, 4, 139, 3], [209, 5, 129, 19], [209, 7, 139, 3], [209, 8, 139, 4], [210, 4, 141, 2], [210, 8, 141, 8, "animatedProps"], [210, 21, 141, 21], [210, 24, 141, 24], [210, 28, 141, 24, "useAnimatedProps"], [210, 50, 141, 40], [210, 52, 141, 41], [211, 6, 141, 41], [211, 10, 141, 41, "_e"], [211, 12, 141, 41], [211, 20, 141, 41, "global"], [211, 26, 141, 41], [211, 27, 141, 41, "Error"], [211, 32, 141, 41], [212, 6, 141, 41], [212, 10, 141, 41, "PerformanceMonitorTsx6"], [212, 32, 141, 41], [212, 44, 141, 41, "PerformanceMonitorTsx6"], [212, 45, 141, 41], [212, 47, 141, 47], [213, 8, 142, 4], [213, 12, 142, 10, "text"], [213, 16, 142, 14], [213, 19, 142, 17], [213, 25, 142, 23], [213, 29, 142, 27, "uiFps"], [213, 34, 142, 32], [213, 35, 142, 33, "value"], [213, 40, 142, 38], [213, 44, 142, 42], [213, 49, 142, 47], [213, 50, 142, 48], [213, 53, 142, 51], [213, 56, 142, 54], [214, 8, 143, 4], [214, 15, 143, 11], [215, 10, 143, 13, "text"], [215, 14, 143, 17], [216, 10, 143, 19, "defaultValue"], [216, 22, 143, 31], [216, 24, 143, 33, "text"], [217, 8, 143, 38], [217, 9, 143, 39], [218, 6, 144, 2], [218, 7, 144, 3], [219, 6, 144, 3, "PerformanceMonitorTsx6"], [219, 28, 144, 3], [219, 29, 144, 3, "__closure"], [219, 38, 144, 3], [220, 8, 144, 3, "uiFps"], [221, 6, 144, 3], [222, 6, 144, 3, "PerformanceMonitorTsx6"], [222, 28, 144, 3], [222, 29, 144, 3, "__workletHash"], [222, 42, 144, 3], [223, 6, 144, 3, "PerformanceMonitorTsx6"], [223, 28, 144, 3], [223, 29, 144, 3, "__initData"], [223, 39, 144, 3], [223, 42, 144, 3, "_worklet_12791881627073_init_data"], [223, 75, 144, 3], [224, 6, 144, 3, "PerformanceMonitorTsx6"], [224, 28, 144, 3], [224, 29, 144, 3, "__stackDetails"], [224, 43, 144, 3], [224, 46, 144, 3, "_e"], [224, 48, 144, 3], [225, 6, 144, 3], [225, 13, 144, 3, "PerformanceMonitorTsx6"], [225, 35, 144, 3], [226, 4, 144, 3], [226, 5, 141, 41], [226, 7, 144, 3], [226, 8, 144, 4], [227, 4, 146, 2], [227, 24, 147, 4], [227, 28, 147, 4, "_jsxDevRuntime"], [227, 42, 147, 4], [227, 43, 147, 4, "jsxDEV"], [227, 49, 147, 4], [227, 51, 147, 5, "_reactNative"], [227, 63, 147, 5], [227, 64, 147, 5, "View"], [227, 68, 147, 9], [228, 6, 147, 10, "style"], [228, 11, 147, 15], [228, 13, 147, 17, "styles"], [228, 19, 147, 23], [228, 20, 147, 24, "container"], [228, 29, 147, 34], [229, 6, 147, 34, "children"], [229, 14, 147, 34], [229, 29, 148, 6], [229, 33, 148, 6, "_jsxDevRuntime"], [229, 47, 148, 6], [229, 48, 148, 6, "jsxDEV"], [229, 54, 148, 6], [229, 56, 148, 7, "AnimatedTextInput"], [229, 73, 148, 24], [230, 8, 149, 8, "style"], [230, 13, 149, 13], [230, 15, 149, 15, "styles"], [230, 21, 149, 21], [230, 22, 149, 22, "text"], [230, 26, 149, 27], [231, 8, 150, 8, "animatedProps"], [231, 21, 150, 21], [231, 23, 150, 23, "animatedProps"], [231, 36, 150, 37], [232, 8, 151, 8, "editable"], [232, 16, 151, 16], [232, 18, 151, 18], [233, 6, 151, 24], [234, 8, 151, 24, "fileName"], [234, 16, 151, 24], [234, 18, 151, 24, "_jsxFileName"], [234, 30, 151, 24], [235, 8, 151, 24, "lineNumber"], [235, 18, 151, 24], [236, 8, 151, 24, "columnNumber"], [236, 20, 151, 24], [237, 6, 151, 24], [237, 13, 152, 7], [238, 4, 152, 8], [239, 6, 152, 8, "fileName"], [239, 14, 152, 8], [239, 16, 152, 8, "_jsxFileName"], [239, 28, 152, 8], [240, 6, 152, 8, "lineNumber"], [240, 16, 152, 8], [241, 6, 152, 8, "columnNumber"], [241, 18, 152, 8], [242, 4, 152, 8], [242, 11, 153, 10], [242, 12, 153, 11], [243, 2, 155, 0], [244, 2, 168, 0], [245, 0, 169, 0], [246, 0, 170, 0], [247, 0, 171, 0], [248, 0, 172, 0], [249, 0, 173, 0], [250, 0, 174, 0], [251, 2, 175, 7], [251, 11, 175, 16, "PerformanceMonitor"], [251, 29, 175, 34, "PerformanceMonitor"], [251, 30, 175, 34, "_ref4"], [251, 35, 175, 34], [251, 37, 177, 28], [252, 4, 177, 28], [252, 8, 177, 28, "_ref4$smoothingFrames"], [252, 29, 177, 28], [252, 32, 177, 28, "_ref4"], [252, 37, 177, 28], [252, 38, 176, 2, "smoothingFrames"], [252, 53, 176, 17], [253, 6, 176, 2, "smoothingFrames"], [253, 21, 176, 17], [253, 24, 176, 17, "_ref4$smoothingFrames"], [253, 45, 176, 17], [253, 59, 176, 20, "DEFAULT_BUFFER_SIZE"], [253, 78, 176, 39], [253, 81, 176, 39, "_ref4$smoothingFrames"], [253, 102, 176, 39], [254, 4, 178, 2], [254, 24, 179, 4], [254, 28, 179, 4, "_jsxDevRuntime"], [254, 42, 179, 4], [254, 43, 179, 4, "jsxDEV"], [254, 49, 179, 4], [254, 51, 179, 5, "_reactNative"], [254, 63, 179, 5], [254, 64, 179, 5, "View"], [254, 68, 179, 9], [255, 6, 179, 10, "style"], [255, 11, 179, 15], [255, 13, 179, 17, "styles"], [255, 19, 179, 23], [255, 20, 179, 24, "monitor"], [255, 27, 179, 32], [256, 6, 179, 32, "children"], [256, 14, 179, 32], [256, 30, 180, 6], [256, 34, 180, 6, "_jsxDevRuntime"], [256, 48, 180, 6], [256, 49, 180, 6, "jsxDEV"], [256, 55, 180, 6], [256, 57, 180, 7, "JsPerformance"], [256, 70, 180, 20], [257, 8, 180, 21, "smoothingFrames"], [257, 23, 180, 36], [257, 25, 180, 38, "smoothingFrames"], [258, 6, 180, 54], [259, 8, 180, 54, "fileName"], [259, 16, 180, 54], [259, 18, 180, 54, "_jsxFileName"], [259, 30, 180, 54], [260, 8, 180, 54, "lineNumber"], [260, 18, 180, 54], [261, 8, 180, 54, "columnNumber"], [261, 20, 180, 54], [262, 6, 180, 54], [262, 13, 180, 56], [262, 14, 180, 57], [262, 29, 181, 6], [262, 33, 181, 6, "_jsxDevRuntime"], [262, 47, 181, 6], [262, 48, 181, 6, "jsxDEV"], [262, 54, 181, 6], [262, 56, 181, 7, "UiPerformance"], [262, 69, 181, 20], [263, 8, 181, 21, "smoothingFrames"], [263, 23, 181, 36], [263, 25, 181, 38, "smoothingFrames"], [264, 6, 181, 54], [265, 8, 181, 54, "fileName"], [265, 16, 181, 54], [265, 18, 181, 54, "_jsxFileName"], [265, 30, 181, 54], [266, 8, 181, 54, "lineNumber"], [266, 18, 181, 54], [267, 8, 181, 54, "columnNumber"], [267, 20, 181, 54], [268, 6, 181, 54], [268, 13, 181, 56], [268, 14, 181, 57], [269, 4, 181, 57], [270, 6, 181, 57, "fileName"], [270, 14, 181, 57], [270, 16, 181, 57, "_jsxFileName"], [270, 28, 181, 57], [271, 6, 181, 57, "lineNumber"], [271, 16, 181, 57], [272, 6, 181, 57, "columnNumber"], [272, 18, 181, 57], [273, 4, 181, 57], [273, 11, 182, 10], [273, 12, 182, 11], [274, 2, 184, 0], [275, 2, 186, 0], [275, 6, 186, 6, "styles"], [275, 12, 186, 12], [275, 15, 186, 15, "StyleSheet"], [275, 38, 186, 25], [275, 39, 186, 26, "create"], [275, 45, 186, 32], [275, 46, 186, 33], [276, 4, 187, 2, "monitor"], [276, 11, 187, 9], [276, 13, 187, 11], [277, 6, 188, 4, "flexDirection"], [277, 19, 188, 17], [277, 21, 188, 19], [277, 26, 188, 24], [278, 6, 189, 4, "position"], [278, 14, 189, 12], [278, 16, 189, 14], [278, 26, 189, 24], [279, 6, 190, 4, "backgroundColor"], [279, 21, 190, 19], [279, 23, 190, 21], [279, 30, 190, 28], [280, 6, 191, 4, "zIndex"], [280, 12, 191, 10], [280, 14, 191, 12], [281, 4, 192, 2], [281, 5, 192, 3], [282, 4, 193, 2, "header"], [282, 10, 193, 8], [282, 12, 193, 10], [283, 6, 194, 4, "fontSize"], [283, 14, 194, 12], [283, 16, 194, 14], [283, 18, 194, 16], [284, 6, 195, 4, "color"], [284, 11, 195, 9], [284, 13, 195, 11], [284, 20, 195, 18], [285, 6, 196, 4, "paddingHorizontal"], [285, 23, 196, 21], [285, 25, 196, 23], [286, 4, 197, 2], [286, 5, 197, 3], [287, 4, 198, 2, "text"], [287, 8, 198, 6], [287, 10, 198, 8], [288, 6, 199, 4, "fontSize"], [288, 14, 199, 12], [288, 16, 199, 14], [288, 18, 199, 16], [289, 6, 200, 4, "fontVariant"], [289, 17, 200, 15], [289, 19, 200, 17], [289, 20, 200, 18], [289, 34, 200, 32], [289, 35, 200, 33], [290, 6, 201, 4, "color"], [290, 11, 201, 9], [290, 13, 201, 11], [290, 20, 201, 18], [291, 6, 202, 4, "fontFamily"], [291, 16, 202, 14], [291, 18, 202, 16], [291, 29, 202, 27], [292, 6, 203, 4, "paddingHorizontal"], [292, 23, 203, 21], [292, 25, 203, 23], [293, 4, 204, 2], [293, 5, 204, 3], [294, 4, 205, 2, "container"], [294, 13, 205, 11], [294, 15, 205, 13], [295, 6, 206, 4, "alignItems"], [295, 16, 206, 14], [295, 18, 206, 16], [295, 26, 206, 24], [296, 6, 207, 4, "justifyContent"], [296, 20, 207, 18], [296, 22, 207, 20], [296, 30, 207, 28], [297, 6, 208, 4, "flexDirection"], [297, 19, 208, 17], [297, 21, 208, 19], [297, 26, 208, 24], [298, 6, 209, 4, "flexWrap"], [298, 14, 209, 12], [298, 16, 209, 14], [299, 4, 210, 2], [300, 2, 211, 0], [300, 3, 211, 1], [300, 4, 211, 2], [301, 0, 211, 3], [301, 3]], "functionMap": {"names": ["<global>", "createCircularDoublesBuffer", "push", "front", "back", "loopAnimationFrame", "loop", "requestAnimationFrame$argument_0", "getFps", "completeBufferRoutine", "JsPerformance", "useEffect$argument_0", "loopAnimationFrame$argument_0", "useAnimatedProps$argument_0", "UiPerformance", "useFrameCallback$argument_0", "PerformanceMonitor"], "mappings": "AAA;ACW;ICS;KDQ;IEE;KFQ;IGE;KHG;CDE;AKM;ECG;0BCC;KDM;GDC;CLG;AQE;CRG;ASE;CTY;AUE;YCO;uBCC;KDW;GDC;yCGE;GHG;CVW;AcE;mBCI;GDU;yCDE;GCG;CdW;OgBoB;ChBS"}}, "type": "js/module"}]}