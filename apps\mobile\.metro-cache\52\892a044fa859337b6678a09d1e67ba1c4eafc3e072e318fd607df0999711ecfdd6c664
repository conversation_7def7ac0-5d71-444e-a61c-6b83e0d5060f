{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 42}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = useRefEffect;\n  var _react = require(_dependencyMap[0], \"react\");\n  function useRefEffect(effect) {\n    var cleanupRef = (0, _react.useRef)(undefined);\n    return (0, _react.useCallback)(instance => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (instance != null) {\n        cleanupRef.current = effect(instance);\n      }\n    }, [effect]);\n  }\n});", "lineCount": 19, "map": [[6, 2, 11, 0], [6, 6, 11, 0, "_react"], [6, 12, 11, 0], [6, 15, 11, 0, "require"], [6, 22, 11, 0], [6, 23, 11, 0, "_dependencyMap"], [6, 37, 11, 0], [7, 2, 29, 15], [7, 11, 29, 24, "useRefEffect"], [7, 23, 29, 36, "useRefEffect"], [7, 24, 30, 2, "effect"], [7, 30, 30, 42], [7, 32, 31, 33], [8, 4, 32, 2], [8, 8, 32, 8, "cleanupRef"], [8, 18, 32, 18], [8, 21, 32, 21], [8, 25, 32, 21, "useRef"], [8, 38, 32, 27], [8, 40, 32, 49, "undefined"], [8, 49, 32, 58], [8, 50, 32, 59], [9, 4, 33, 2], [9, 11, 33, 9], [9, 15, 33, 9, "useCallback"], [9, 33, 33, 20], [9, 35, 34, 5, "instance"], [9, 43, 34, 31], [9, 47, 34, 36], [10, 6, 35, 6], [10, 10, 35, 10, "cleanupRef"], [10, 20, 35, 20], [10, 21, 35, 21, "current"], [10, 28, 35, 28], [10, 30, 35, 30], [11, 8, 36, 8, "cleanupRef"], [11, 18, 36, 18], [11, 19, 36, 19, "current"], [11, 26, 36, 26], [11, 27, 36, 27], [11, 28, 36, 28], [12, 8, 37, 8, "cleanupRef"], [12, 18, 37, 18], [12, 19, 37, 19, "current"], [12, 26, 37, 26], [12, 29, 37, 29, "undefined"], [12, 38, 37, 38], [13, 6, 38, 6], [14, 6, 39, 6], [14, 10, 39, 10, "instance"], [14, 18, 39, 18], [14, 22, 39, 22], [14, 26, 39, 26], [14, 28, 39, 28], [15, 8, 40, 8, "cleanupRef"], [15, 18, 40, 18], [15, 19, 40, 19, "current"], [15, 26, 40, 26], [15, 29, 40, 29, "effect"], [15, 35, 40, 35], [15, 36, 40, 36, "instance"], [15, 44, 40, 44], [15, 45, 40, 45], [16, 6, 41, 6], [17, 4, 42, 4], [17, 5, 42, 5], [17, 7, 43, 4], [17, 8, 43, 5, "effect"], [17, 14, 43, 11], [17, 15, 44, 2], [17, 16, 44, 3], [18, 2, 45, 0], [19, 0, 45, 1], [19, 3]], "functionMap": {"names": ["<global>", "useRefEffect", "<anonymous>"], "mappings": "AAA;eC4B;ICK;KDQ"}}, "type": "js/module"}]}