{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./uuid", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 33, "index": 33}}], "key": "Q1VgbV74OaNDCDDYB3/dMtZbO1k=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"default\", {\n    enumerable: true,\n    get: function () {\n      return _uuid.default;\n    }\n  });\n  var _uuid = _interopRequireDefault(require(_dependencyMap[1], \"./uuid\"));\n});", "lineCount": 13, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_uuid"], [12, 11, 1, 0], [12, 14, 1, 0, "_interopRequireDefault"], [12, 36, 1, 0], [12, 37, 1, 0, "require"], [12, 44, 1, 0], [12, 45, 1, 0, "_dependencyMap"], [12, 59, 1, 0], [13, 0, 1, 33], [13, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}