{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 55, "index": 55}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _default = exports.default = _reactNative.NativeModules?.PlatformConstants ?? _reactNative.Platform.constants;\n});", "lineCount": 8, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_reactNative"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 1, 55], [7, 6, 1, 55, "_default"], [7, 14, 1, 55], [7, 17, 1, 55, "exports"], [7, 24, 1, 55], [7, 25, 1, 55, "default"], [7, 32, 1, 55], [7, 35, 7, 16, "NativeModules"], [7, 61, 7, 29], [7, 63, 7, 31, "PlatformConstants"], [7, 80, 7, 48], [7, 84, 8, 2, "Platform"], [7, 105, 8, 10], [7, 106, 8, 11, "constants"], [7, 115, 8, 20], [8, 0, 8, 20], [8, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}