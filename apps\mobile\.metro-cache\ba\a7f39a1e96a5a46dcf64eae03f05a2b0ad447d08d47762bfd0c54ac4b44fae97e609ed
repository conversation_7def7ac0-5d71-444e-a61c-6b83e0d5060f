{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 62}, "end": {"line": 2, "column": 49, "index": 111}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ManualGesture = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _gesture = require(_dependencyMap[7], \"./gesture\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var _worklet_15082129759219_init_data = {\n    code: \"function changeEventCalculator_manualGestureTs1(current,_previous){return current;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\manualGesture.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"changeEventCalculator_manualGestureTs1\\\",\\\"current\\\",\\\"_previous\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/manualGesture.ts\\\"],\\\"mappings\\\":\\\"AAGA,SAAAA,sCAEEA,CAAAC,OACA,CAAAC,SAAA,EAEA,MAAO,CAAAD,OAAO,CAChB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var changeEventCalculator = function () {\n    var _e = [new global.Error(), 1, -27];\n    var changeEventCalculator = function (current, _previous) {\n      return current;\n    };\n    changeEventCalculator.__closure = {};\n    changeEventCalculator.__workletHash = 15082129759219;\n    changeEventCalculator.__initData = _worklet_15082129759219_init_data;\n    changeEventCalculator.__stackDetails = _e;\n    return changeEventCalculator;\n  }();\n  var ManualGesture = exports.ManualGesture = /*#__PURE__*/function (_ContinousBaseGesture) {\n    function ManualGesture() {\n      var _this;\n      (0, _classCallCheck2.default)(this, ManualGesture);\n      _this = _callSuper(this, ManualGesture);\n      _this.handlerName = 'ManualGestureHandler';\n      return _this;\n    }\n    (0, _inherits2.default)(ManualGesture, _ContinousBaseGesture);\n    return (0, _createClass2.default)(ManualGesture, [{\n      key: \"onChange\",\n      value: function onChange(callback) {\n        // @ts-ignore TS being overprotective, Record<string, never> is Record\n        this.handlers.changeEventCalculator = changeEventCalculator;\n        return _superPropGet(ManualGesture, \"onChange\", this, 3)([callback]);\n      }\n    }]);\n  }(_gesture.ContinousBaseGesture);\n});", "lineCount": 52, "map": [[13, 2, 2, 0], [13, 6, 2, 0, "_gesture"], [13, 14, 2, 0], [13, 17, 2, 0, "require"], [13, 24, 2, 0], [13, 25, 2, 0, "_dependencyMap"], [13, 39, 2, 0], [14, 2, 2, 49], [14, 11, 2, 49, "_callSuper"], [14, 22, 2, 49, "t"], [14, 23, 2, 49], [14, 25, 2, 49, "o"], [14, 26, 2, 49], [14, 28, 2, 49, "e"], [14, 29, 2, 49], [14, 40, 2, 49, "o"], [14, 41, 2, 49], [14, 48, 2, 49, "_getPrototypeOf2"], [14, 64, 2, 49], [14, 65, 2, 49, "default"], [14, 72, 2, 49], [14, 74, 2, 49, "o"], [14, 75, 2, 49], [14, 82, 2, 49, "_possibleConstructorReturn2"], [14, 109, 2, 49], [14, 110, 2, 49, "default"], [14, 117, 2, 49], [14, 119, 2, 49, "t"], [14, 120, 2, 49], [14, 122, 2, 49, "_isNativeReflectConstruct"], [14, 147, 2, 49], [14, 152, 2, 49, "Reflect"], [14, 159, 2, 49], [14, 160, 2, 49, "construct"], [14, 169, 2, 49], [14, 170, 2, 49, "o"], [14, 171, 2, 49], [14, 173, 2, 49, "e"], [14, 174, 2, 49], [14, 186, 2, 49, "_getPrototypeOf2"], [14, 202, 2, 49], [14, 203, 2, 49, "default"], [14, 210, 2, 49], [14, 212, 2, 49, "t"], [14, 213, 2, 49], [14, 215, 2, 49, "constructor"], [14, 226, 2, 49], [14, 230, 2, 49, "o"], [14, 231, 2, 49], [14, 232, 2, 49, "apply"], [14, 237, 2, 49], [14, 238, 2, 49, "t"], [14, 239, 2, 49], [14, 241, 2, 49, "e"], [14, 242, 2, 49], [15, 2, 2, 49], [15, 11, 2, 49, "_isNativeReflectConstruct"], [15, 37, 2, 49], [15, 51, 2, 49, "t"], [15, 52, 2, 49], [15, 56, 2, 49, "Boolean"], [15, 63, 2, 49], [15, 64, 2, 49, "prototype"], [15, 73, 2, 49], [15, 74, 2, 49, "valueOf"], [15, 81, 2, 49], [15, 82, 2, 49, "call"], [15, 86, 2, 49], [15, 87, 2, 49, "Reflect"], [15, 94, 2, 49], [15, 95, 2, 49, "construct"], [15, 104, 2, 49], [15, 105, 2, 49, "Boolean"], [15, 112, 2, 49], [15, 145, 2, 49, "t"], [15, 146, 2, 49], [15, 159, 2, 49, "_isNativeReflectConstruct"], [15, 184, 2, 49], [15, 196, 2, 49, "_isNativeReflectConstruct"], [15, 197, 2, 49], [15, 210, 2, 49, "t"], [15, 211, 2, 49], [16, 2, 2, 49], [16, 11, 2, 49, "_superPropGet"], [16, 25, 2, 49, "t"], [16, 26, 2, 49], [16, 28, 2, 49, "o"], [16, 29, 2, 49], [16, 31, 2, 49, "e"], [16, 32, 2, 49], [16, 34, 2, 49, "r"], [16, 35, 2, 49], [16, 43, 2, 49, "p"], [16, 44, 2, 49], [16, 51, 2, 49, "_get2"], [16, 56, 2, 49], [16, 57, 2, 49, "default"], [16, 64, 2, 49], [16, 70, 2, 49, "_getPrototypeOf2"], [16, 86, 2, 49], [16, 87, 2, 49, "default"], [16, 94, 2, 49], [16, 100, 2, 49, "r"], [16, 101, 2, 49], [16, 104, 2, 49, "t"], [16, 105, 2, 49], [16, 106, 2, 49, "prototype"], [16, 115, 2, 49], [16, 118, 2, 49, "t"], [16, 119, 2, 49], [16, 122, 2, 49, "o"], [16, 123, 2, 49], [16, 125, 2, 49, "e"], [16, 126, 2, 49], [16, 140, 2, 49, "r"], [16, 141, 2, 49], [16, 166, 2, 49, "p"], [16, 167, 2, 49], [16, 180, 2, 49, "t"], [16, 181, 2, 49], [16, 192, 2, 49, "p"], [16, 193, 2, 49], [16, 194, 2, 49, "apply"], [16, 199, 2, 49], [16, 200, 2, 49, "e"], [16, 201, 2, 49], [16, 203, 2, 49, "t"], [16, 204, 2, 49], [16, 211, 2, 49, "p"], [16, 212, 2, 49], [17, 2, 2, 49], [17, 6, 2, 49, "_worklet_15082129759219_init_data"], [17, 39, 2, 49], [18, 4, 2, 49, "code"], [18, 8, 2, 49], [19, 4, 2, 49, "location"], [19, 12, 2, 49], [20, 4, 2, 49, "sourceMap"], [20, 13, 2, 49], [21, 4, 2, 49, "version"], [21, 11, 2, 49], [22, 2, 2, 49], [23, 2, 2, 49], [23, 6, 2, 49, "changeEventCalculator"], [23, 27, 2, 49], [23, 30, 4, 0], [24, 4, 4, 0], [24, 8, 4, 0, "_e"], [24, 10, 4, 0], [24, 18, 4, 0, "global"], [24, 24, 4, 0], [24, 25, 4, 0, "Error"], [24, 30, 4, 0], [25, 4, 4, 0], [25, 8, 4, 0, "changeEventCalculator"], [25, 29, 4, 0], [25, 41, 4, 0, "changeEventCalculator"], [25, 42, 5, 2, "current"], [25, 49, 5, 52], [25, 51, 6, 2, "_previous"], [25, 60, 6, 55], [25, 62, 7, 2], [26, 6, 9, 2], [26, 13, 9, 9, "current"], [26, 20, 9, 16], [27, 4, 10, 0], [27, 5, 10, 1], [28, 4, 10, 1, "changeEventCalculator"], [28, 25, 10, 1], [28, 26, 10, 1, "__closure"], [28, 35, 10, 1], [29, 4, 10, 1, "changeEventCalculator"], [29, 25, 10, 1], [29, 26, 10, 1, "__workletHash"], [29, 39, 10, 1], [30, 4, 10, 1, "changeEventCalculator"], [30, 25, 10, 1], [30, 26, 10, 1, "__initData"], [30, 36, 10, 1], [30, 39, 10, 1, "_worklet_15082129759219_init_data"], [30, 72, 10, 1], [31, 4, 10, 1, "changeEventCalculator"], [31, 25, 10, 1], [31, 26, 10, 1, "__stackDetails"], [31, 40, 10, 1], [31, 43, 10, 1, "_e"], [31, 45, 10, 1], [32, 4, 10, 1], [32, 11, 10, 1, "changeEventCalculator"], [32, 32, 10, 1], [33, 2, 10, 1], [33, 3, 4, 0], [34, 2, 4, 0], [34, 6, 12, 13, "ManualGesture"], [34, 19, 12, 26], [34, 22, 12, 26, "exports"], [34, 29, 12, 26], [34, 30, 12, 26, "ManualGesture"], [34, 43, 12, 26], [34, 69, 12, 26, "_ContinousBaseGesture"], [34, 90, 12, 26], [35, 4, 16, 2], [35, 13, 16, 2, "ManualGesture"], [35, 27, 16, 2], [35, 29, 16, 16], [36, 6, 16, 16], [36, 10, 16, 16, "_this"], [36, 15, 16, 16], [37, 6, 16, 16], [37, 10, 16, 16, "_classCallCheck2"], [37, 26, 16, 16], [37, 27, 16, 16, "default"], [37, 34, 16, 16], [37, 42, 16, 16, "ManualGesture"], [37, 55, 16, 16], [38, 6, 17, 4, "_this"], [38, 11, 17, 4], [38, 14, 17, 4, "_callSuper"], [38, 24, 17, 4], [38, 31, 17, 4, "ManualGesture"], [38, 44, 17, 4], [39, 6, 19, 4, "_this"], [39, 11, 19, 4], [39, 12, 19, 9, "handler<PERSON>ame"], [39, 23, 19, 20], [39, 26, 19, 23], [39, 48, 19, 45], [40, 6, 19, 46], [40, 13, 19, 46, "_this"], [40, 18, 19, 46], [41, 4, 20, 2], [42, 4, 20, 3], [42, 8, 20, 3, "_inherits2"], [42, 18, 20, 3], [42, 19, 20, 3, "default"], [42, 26, 20, 3], [42, 28, 20, 3, "ManualGesture"], [42, 41, 20, 3], [42, 43, 20, 3, "_ContinousBaseGesture"], [42, 64, 20, 3], [43, 4, 20, 3], [43, 15, 20, 3, "_createClass2"], [43, 28, 20, 3], [43, 29, 20, 3, "default"], [43, 36, 20, 3], [43, 38, 20, 3, "ManualGesture"], [43, 51, 20, 3], [44, 6, 20, 3, "key"], [44, 9, 20, 3], [45, 6, 20, 3, "value"], [45, 11, 20, 3], [45, 13, 22, 2], [45, 22, 22, 2, "onChange"], [45, 30, 22, 10, "onChange"], [45, 31, 23, 4, "callback"], [45, 39, 23, 72], [45, 41, 24, 4], [46, 8, 25, 4], [47, 8, 26, 4], [47, 12, 26, 8], [47, 13, 26, 9, "handlers"], [47, 21, 26, 17], [47, 22, 26, 18, "changeEventCalculator"], [47, 43, 26, 39], [47, 46, 26, 42, "changeEventCalculator"], [47, 67, 26, 63], [48, 8, 27, 4], [48, 15, 27, 4, "_superPropGet"], [48, 28, 27, 4], [48, 29, 27, 4, "ManualGesture"], [48, 42, 27, 4], [48, 66, 27, 26, "callback"], [48, 74, 27, 34], [49, 6, 28, 2], [50, 4, 28, 3], [51, 2, 28, 3], [51, 4, 12, 35, "ContinousBaseGesture"], [51, 33, 12, 55], [52, 0, 12, 55], [52, 3]], "functionMap": {"names": ["<global>", "changeEventCalculator", "ManualGesture", "ManualGesture#constructor", "ManualGesture#onChange"], "mappings": "AAA;ACG;CDM;OEE;ECI;GDI;EEE;GFM;CFC"}}, "type": "js/module"}]}