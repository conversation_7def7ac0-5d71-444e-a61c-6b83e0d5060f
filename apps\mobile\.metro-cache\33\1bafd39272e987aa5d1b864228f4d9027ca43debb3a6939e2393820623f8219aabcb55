{"dependencies": [{"name": "react-native/Libraries/Renderer/shims/ReactNativeViewConfigRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 43}, "end": {"line": 2, "column": 109, "index": 152}}], "key": "ws4cHz6x0kRb4bJ8vhU/pqmTiTw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"customDirectEventTypes\", {\n    enumerable: true,\n    get: function () {\n      return _ReactNativeViewConfigRegistry.customDirectEventTypes;\n    }\n  });\n  var _ReactNativeViewConfigRegistry = require(_dependencyMap[0], \"react-native/Libraries/Renderer/shims/ReactNativeViewConfigRegistry\");\n});", "lineCount": 12, "map": [[11, 2, 2, 0], [11, 6, 2, 0, "_ReactNativeViewConfigRegistry"], [11, 36, 2, 0], [11, 39, 2, 0, "require"], [11, 46, 2, 0], [11, 47, 2, 0, "_dependencyMap"], [11, 61, 2, 0], [12, 0, 2, 109], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}