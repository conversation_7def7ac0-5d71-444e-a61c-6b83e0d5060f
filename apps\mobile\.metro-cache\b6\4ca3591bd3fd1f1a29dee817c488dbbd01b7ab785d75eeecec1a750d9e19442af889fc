{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.shouldFallbackToLegacyNativeModule = shouldFallbackToLegacyNativeModule;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  function shouldFallbackToLegacyNativeModule() {\n    var expoConstants = _reactNative.NativeModules[\"NativeUnimoduleProxy\"]?.modulesConstants?.ExponentConstants;\n    if (expoConstants) {\n      /**\n       * In SDK <= 39, appOwnership is defined in managed apps but executionEnvironment is not.\n       * In bare React Native apps using expo-constants, appOwnership is never defined, so\n       * isLegacySdkVersion will be false in that context.\n       */\n      var isLegacySdkVersion = expoConstants.appOwnership && !expoConstants.executionEnvironment;\n\n      /**\n       * Expo managed apps don't include the @react-native-async-storage/async-storage\n       * native modules yet, but the API interface is the same, so we can use the version\n       * exported from React Native still.\n       *\n       * If in future releases (eg: @react-native-async-storage/async-storage >= 2.0.0) this\n       * will likely not be valid anymore, and the package will need to be included in the Expo SDK\n       * to continue to work.\n       */\n      if (isLegacySdkVersion || [\"storeClient\", \"standalone\"].includes(expoConstants.executionEnvironment)) {\n        return true;\n      }\n    }\n    return false;\n  }\n});", "lineCount": 32, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_reactNative"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 3, 7], [7, 11, 3, 16, "shouldFallbackToLegacyNativeModule"], [7, 45, 3, 50, "shouldFallbackToLegacyNativeModule"], [7, 46, 3, 50], [7, 48, 3, 62], [8, 4, 4, 2], [8, 8, 4, 8, "expoConstants"], [8, 21, 4, 21], [8, 24, 5, 4, "NativeModules"], [8, 50, 5, 17], [8, 51, 5, 18], [8, 73, 5, 40], [8, 74, 5, 41], [8, 76, 5, 43, "modulesConstants"], [8, 92, 5, 59], [8, 94, 5, 61, "ExponentConstants"], [8, 111, 5, 78], [9, 4, 7, 2], [9, 8, 7, 6, "expoConstants"], [9, 21, 7, 19], [9, 23, 7, 21], [10, 6, 8, 4], [11, 0, 9, 0], [12, 0, 10, 0], [13, 0, 11, 0], [14, 0, 12, 0], [15, 6, 13, 4], [15, 10, 13, 10, "isLegacySdkVersion"], [15, 28, 13, 28], [15, 31, 14, 6, "expoConstants"], [15, 44, 14, 19], [15, 45, 14, 20, "appOwnership"], [15, 57, 14, 32], [15, 61, 14, 36], [15, 62, 14, 37, "expoConstants"], [15, 75, 14, 50], [15, 76, 14, 51, "executionEnvironment"], [15, 96, 14, 71], [17, 6, 16, 4], [18, 0, 17, 0], [19, 0, 18, 0], [20, 0, 19, 0], [21, 0, 20, 0], [22, 0, 21, 0], [23, 0, 22, 0], [24, 0, 23, 0], [25, 0, 24, 0], [26, 6, 25, 4], [26, 10, 26, 6, "isLegacySdkVersion"], [26, 28, 26, 24], [26, 32, 27, 6], [26, 33, 27, 7], [26, 46, 27, 20], [26, 48, 27, 22], [26, 60, 27, 34], [26, 61, 27, 35], [26, 62, 27, 36, "includes"], [26, 70, 27, 44], [26, 71, 27, 45, "expoConstants"], [26, 84, 27, 58], [26, 85, 27, 59, "executionEnvironment"], [26, 105, 27, 79], [26, 106, 27, 80], [26, 108, 28, 6], [27, 8, 29, 6], [27, 15, 29, 13], [27, 19, 29, 17], [28, 6, 30, 4], [29, 4, 31, 2], [30, 4, 33, 2], [30, 11, 33, 9], [30, 16, 33, 14], [31, 2, 34, 0], [32, 0, 34, 1], [32, 3]], "functionMap": {"names": ["<global>", "shouldFallbackToLegacyNativeModule"], "mappings": "AAA;OCE;CD+B"}}, "type": "js/module"}]}