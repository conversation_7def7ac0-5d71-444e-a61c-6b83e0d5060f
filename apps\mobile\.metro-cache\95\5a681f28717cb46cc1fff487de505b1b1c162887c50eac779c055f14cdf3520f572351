{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./TouchableNativeFeedback", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 215}, "end": {"line": 4, "column": 79, "index": 294}}], "key": "GaEBL9RFpubFzxwnULrDduWfETw=", "exportNames": ["*"]}}, {"name": "./TouchableWithoutFeedback", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 295}, "end": {"line": 5, "column": 81, "index": 376}}], "key": "U5iWvGlDfzlJ6xEld4o3HiR9AWk=", "exportNames": ["*"]}}, {"name": "./TouchableOpacity", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 377}, "end": {"line": 6, "column": 65, "index": 442}}], "key": "pvRdBnGMqjulL9kenFSplZ6H5BU=", "exportNames": ["*"]}}, {"name": "./TouchableHighlight", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 443}, "end": {"line": 7, "column": 69, "index": 512}}], "key": "K1t+0LiMcHZOohK10gUIJXCyYkY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"TouchableHighlight\", {\n    enumerable: true,\n    get: function () {\n      return _TouchableHighlight.default;\n    }\n  });\n  Object.defineProperty(exports, \"TouchableNativeFeedback\", {\n    enumerable: true,\n    get: function () {\n      return _TouchableNativeFeedback.default;\n    }\n  });\n  Object.defineProperty(exports, \"TouchableOpacity\", {\n    enumerable: true,\n    get: function () {\n      return _TouchableOpacity.default;\n    }\n  });\n  Object.defineProperty(exports, \"TouchableWithoutFeedback\", {\n    enumerable: true,\n    get: function () {\n      return _TouchableWithoutFeedback.default;\n    }\n  });\n  var _TouchableNativeFeedback = _interopRequireDefault(require(_dependencyMap[1], \"./TouchableNativeFeedback\"));\n  var _TouchableWithoutFeedback = _interopRequireDefault(require(_dependencyMap[2], \"./TouchableWithoutFeedback\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[3], \"./TouchableOpacity\"));\n  var _TouchableHighlight = _interopRequireDefault(require(_dependencyMap[4], \"./TouchableHighlight\"));\n});", "lineCount": 34, "map": [[30, 2, 4, 0], [30, 6, 4, 0, "_TouchableNativeFeedback"], [30, 30, 4, 0], [30, 33, 4, 0, "_interopRequireDefault"], [30, 55, 4, 0], [30, 56, 4, 0, "require"], [30, 63, 4, 0], [30, 64, 4, 0, "_dependencyMap"], [30, 78, 4, 0], [31, 2, 5, 0], [31, 6, 5, 0, "_TouchableWithoutFeedback"], [31, 31, 5, 0], [31, 34, 5, 0, "_interopRequireDefault"], [31, 56, 5, 0], [31, 57, 5, 0, "require"], [31, 64, 5, 0], [31, 65, 5, 0, "_dependencyMap"], [31, 79, 5, 0], [32, 2, 6, 0], [32, 6, 6, 0, "_TouchableOpacity"], [32, 23, 6, 0], [32, 26, 6, 0, "_interopRequireDefault"], [32, 48, 6, 0], [32, 49, 6, 0, "require"], [32, 56, 6, 0], [32, 57, 6, 0, "_dependencyMap"], [32, 71, 6, 0], [33, 2, 7, 0], [33, 6, 7, 0, "_TouchableHighlight"], [33, 25, 7, 0], [33, 28, 7, 0, "_interopRequireDefault"], [33, 50, 7, 0], [33, 51, 7, 0, "require"], [33, 58, 7, 0], [33, 59, 7, 0, "_dependencyMap"], [33, 73, 7, 0], [34, 0, 7, 69], [34, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}