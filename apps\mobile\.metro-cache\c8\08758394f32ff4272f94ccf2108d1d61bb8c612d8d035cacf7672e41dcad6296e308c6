{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var DEV = process.env.NODE_ENV !== \"production\";\n  var warnings = new Set();\n  function warnOnce(condition) {\n    if (DEV && condition) {\n      for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        rest[_key - 1] = arguments[_key];\n      }\n      var key = rest.join(\" \");\n      if (warnings.has(key)) {\n        return;\n      }\n      warnings.add(key);\n      console.warn(...rest);\n    }\n  }\n  module.exports = warnOnce;\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 6, 1, 6, "DEV"], [2, 9, 1, 9], [2, 12, 1, 12, "process"], [2, 19, 1, 19], [2, 20, 1, 20, "env"], [2, 23, 1, 23], [2, 24, 1, 24, "NODE_ENV"], [2, 32, 1, 32], [2, 37, 1, 37], [2, 49, 1, 49], [3, 2, 3, 0], [3, 6, 3, 6, "warnings"], [3, 14, 3, 14], [3, 17, 3, 17], [3, 21, 3, 21, "Set"], [3, 24, 3, 24], [3, 25, 3, 25], [3, 26, 3, 26], [4, 2, 5, 0], [4, 11, 5, 9, "warnOnce"], [4, 19, 5, 17, "warnOnce"], [4, 20, 5, 18, "condition"], [4, 29, 5, 27], [4, 31, 5, 38], [5, 4, 6, 2], [5, 8, 6, 6, "DEV"], [5, 11, 6, 9], [5, 15, 6, 13, "condition"], [5, 24, 6, 22], [5, 26, 6, 24], [6, 6, 6, 24], [6, 15, 6, 24, "_len"], [6, 19, 6, 24], [6, 22, 6, 24, "arguments"], [6, 31, 6, 24], [6, 32, 6, 24, "length"], [6, 38, 6, 24], [6, 40, 5, 32, "rest"], [6, 44, 5, 36], [6, 51, 5, 36, "Array"], [6, 56, 5, 36], [6, 57, 5, 36, "_len"], [6, 61, 5, 36], [6, 68, 5, 36, "_len"], [6, 72, 5, 36], [6, 83, 5, 36, "_key"], [6, 87, 5, 36], [6, 93, 5, 36, "_key"], [6, 97, 5, 36], [6, 100, 5, 36, "_len"], [6, 104, 5, 36], [6, 106, 5, 36, "_key"], [6, 110, 5, 36], [7, 8, 5, 32, "rest"], [7, 12, 5, 36], [7, 13, 5, 36, "_key"], [7, 17, 5, 36], [7, 25, 5, 36, "arguments"], [7, 34, 5, 36], [7, 35, 5, 36, "_key"], [7, 39, 5, 36], [8, 6, 5, 36], [9, 6, 7, 4], [9, 10, 7, 10, "key"], [9, 13, 7, 13], [9, 16, 7, 16, "rest"], [9, 20, 7, 20], [9, 21, 7, 21, "join"], [9, 25, 7, 25], [9, 26, 7, 26], [9, 29, 7, 29], [9, 30, 7, 30], [10, 6, 9, 4], [10, 10, 9, 8, "warnings"], [10, 18, 9, 16], [10, 19, 9, 17, "has"], [10, 22, 9, 20], [10, 23, 9, 21, "key"], [10, 26, 9, 24], [10, 27, 9, 25], [10, 29, 9, 27], [11, 8, 10, 6], [12, 6, 11, 4], [13, 6, 13, 4, "warnings"], [13, 14, 13, 12], [13, 15, 13, 13, "add"], [13, 18, 13, 16], [13, 19, 13, 17, "key"], [13, 22, 13, 20], [13, 23, 13, 21], [14, 6, 14, 4, "console"], [14, 13, 14, 11], [14, 14, 14, 12, "warn"], [14, 18, 14, 16], [14, 19, 14, 17], [14, 22, 14, 20, "rest"], [14, 26, 14, 24], [14, 27, 14, 25], [15, 4, 15, 2], [16, 2, 16, 0], [17, 2, 18, 0, "module"], [17, 8, 18, 6], [17, 9, 18, 7, "exports"], [17, 16, 18, 14], [17, 19, 18, 17, "warnOnce"], [17, 27, 18, 25], [18, 0, 18, 26], [18, 3]], "functionMap": {"names": ["<global>", "warnOnce"], "mappings": "AAA;ACI;CDW"}}, "type": "js/module"}]}