{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.unregister = exports.register = exports.has = void 0;\n  var registry = new Map();\n  var register = id => {\n    var used = registry.get(id);\n    if (used != null) {\n      registry.set(id, used + 1);\n    } else {\n      registry.set(id, 1);\n    }\n  };\n  exports.register = register;\n  var unregister = id => {\n    var used = registry.get(id);\n    if (used != null) {\n      if (used <= 1) {\n        registry.delete(id);\n      } else {\n        registry.set(id, used - 1);\n      }\n    }\n  };\n  exports.unregister = unregister;\n  var has = id => {\n    return registry.get(id) || false;\n  };\n  exports.has = has;\n});", "lineCount": 31, "map": [[6, 2, 11, 0], [6, 6, 11, 6, "registry"], [6, 14, 11, 35], [6, 17, 11, 38], [6, 21, 11, 42, "Map"], [6, 24, 11, 45], [6, 25, 11, 46], [6, 26, 11, 47], [7, 2, 13, 7], [7, 6, 13, 13, "register"], [7, 14, 13, 21], [7, 17, 13, 25, "id"], [7, 19, 13, 35], [7, 23, 13, 40], [8, 4, 14, 2], [8, 8, 14, 8, "used"], [8, 12, 14, 12], [8, 15, 14, 15, "registry"], [8, 23, 14, 23], [8, 24, 14, 24, "get"], [8, 27, 14, 27], [8, 28, 14, 28, "id"], [8, 30, 14, 30], [8, 31, 14, 31], [9, 4, 16, 2], [9, 8, 16, 6, "used"], [9, 12, 16, 10], [9, 16, 16, 14], [9, 20, 16, 18], [9, 22, 16, 20], [10, 6, 17, 4, "registry"], [10, 14, 17, 12], [10, 15, 17, 13, "set"], [10, 18, 17, 16], [10, 19, 17, 17, "id"], [10, 21, 17, 19], [10, 23, 17, 21, "used"], [10, 27, 17, 25], [10, 30, 17, 28], [10, 31, 17, 29], [10, 32, 17, 30], [11, 4, 18, 2], [11, 5, 18, 3], [11, 11, 18, 9], [12, 6, 19, 4, "registry"], [12, 14, 19, 12], [12, 15, 19, 13, "set"], [12, 18, 19, 16], [12, 19, 19, 17, "id"], [12, 21, 19, 19], [12, 23, 19, 21], [12, 24, 19, 22], [12, 25, 19, 23], [13, 4, 20, 2], [14, 2, 21, 0], [14, 3, 21, 1], [15, 2, 21, 2, "exports"], [15, 9, 21, 2], [15, 10, 21, 2, "register"], [15, 18, 21, 2], [15, 21, 21, 2, "register"], [15, 29, 21, 2], [16, 2, 23, 7], [16, 6, 23, 13, "unregister"], [16, 16, 23, 23], [16, 19, 23, 27, "id"], [16, 21, 23, 37], [16, 25, 23, 42], [17, 4, 24, 2], [17, 8, 24, 8, "used"], [17, 12, 24, 12], [17, 15, 24, 15, "registry"], [17, 23, 24, 23], [17, 24, 24, 24, "get"], [17, 27, 24, 27], [17, 28, 24, 28, "id"], [17, 30, 24, 30], [17, 31, 24, 31], [18, 4, 26, 2], [18, 8, 26, 6, "used"], [18, 12, 26, 10], [18, 16, 26, 14], [18, 20, 26, 18], [18, 22, 26, 20], [19, 6, 27, 4], [19, 10, 27, 8, "used"], [19, 14, 27, 12], [19, 18, 27, 16], [19, 19, 27, 17], [19, 21, 27, 19], [20, 8, 28, 6, "registry"], [20, 16, 28, 14], [20, 17, 28, 15, "delete"], [20, 23, 28, 21], [20, 24, 28, 22, "id"], [20, 26, 28, 24], [20, 27, 28, 25], [21, 6, 29, 4], [21, 7, 29, 5], [21, 13, 29, 11], [22, 8, 30, 6, "registry"], [22, 16, 30, 14], [22, 17, 30, 15, "set"], [22, 20, 30, 18], [22, 21, 30, 19, "id"], [22, 23, 30, 21], [22, 25, 30, 23, "used"], [22, 29, 30, 27], [22, 32, 30, 30], [22, 33, 30, 31], [22, 34, 30, 32], [23, 6, 31, 4], [24, 4, 32, 2], [25, 2, 33, 0], [25, 3, 33, 1], [26, 2, 33, 2, "exports"], [26, 9, 33, 2], [26, 10, 33, 2, "unregister"], [26, 20, 33, 2], [26, 23, 33, 2, "unregister"], [26, 33, 33, 2], [27, 2, 35, 7], [27, 6, 35, 13, "has"], [27, 9, 35, 16], [27, 12, 35, 20, "id"], [27, 14, 35, 30], [27, 18, 35, 53], [28, 4, 36, 2], [28, 11, 36, 9, "registry"], [28, 19, 36, 17], [28, 20, 36, 18, "get"], [28, 23, 36, 21], [28, 24, 36, 22, "id"], [28, 26, 36, 24], [28, 27, 36, 25], [28, 31, 36, 29], [28, 36, 36, 34], [29, 2, 37, 0], [29, 3, 37, 1], [30, 2, 37, 2, "exports"], [30, 9, 37, 2], [30, 10, 37, 2, "has"], [30, 13, 37, 2], [30, 16, 37, 2, "has"], [30, 19, 37, 2], [31, 0, 37, 2], [31, 3]], "functionMap": {"names": ["<global>", "register", "unregister", "has"], "mappings": "AAA;wBCY;CDQ;0BEE;CFU;mBGE;CHE"}}, "type": "js/module"}]}