{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2F..%2F..%2Fnode_modules%2F%40react-navigation%2Felements%2Flib%2Fmodule%2Fassets\",\n    \"width\": 24,\n    \"height\": 24,\n    \"scales\": [1, 2, 3, 4],\n    \"hash\": \"940453dc5cbfaa96cf907b3aa7791ece\",\n    \"name\": \"search-icon\",\n    \"type\": \"png\",\n    \"fileHashes\": [\"4403c6117ec30c859bc95d70ce4a71d3\", \"069d99eb1fa6712c0b9034a58c6b57dd\", \"c3273c9e5321f20d1e42c2efae2578c4\", \"286d67d3f74808a60a78d3ebf1a5fb57\"]\n  });\n});", "lineCount": 13, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 134, 1, 154], [5, 4, 1, 155], [5, 11, 1, 162], [5, 13, 1, 163], [5, 15, 1, 165], [6, 4, 1, 166], [6, 12, 1, 174], [6, 14, 1, 175], [6, 16, 1, 177], [7, 4, 1, 178], [7, 12, 1, 186], [7, 14, 1, 187], [7, 15, 1, 188], [7, 16, 1, 189], [7, 18, 1, 190], [7, 19, 1, 191], [7, 21, 1, 192], [7, 22, 1, 193], [7, 24, 1, 194], [7, 25, 1, 195], [7, 26, 1, 196], [8, 4, 1, 197], [8, 10, 1, 203], [8, 12, 1, 204], [8, 46, 1, 238], [9, 4, 1, 239], [9, 10, 1, 245], [9, 12, 1, 246], [9, 25, 1, 259], [10, 4, 1, 260], [10, 10, 1, 266], [10, 12, 1, 267], [10, 17, 1, 272], [11, 4, 1, 273], [11, 16, 1, 285], [11, 18, 1, 286], [11, 19, 1, 287], [11, 53, 1, 321], [11, 55, 1, 322], [11, 89, 1, 356], [11, 91, 1, 357], [11, 125, 1, 391], [11, 127, 1, 392], [11, 161, 1, 426], [12, 2, 1, 427], [12, 3, 1, 428], [13, 0, 1, 428], [13, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}