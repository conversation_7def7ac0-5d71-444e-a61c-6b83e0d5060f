{"dependencies": [{"name": "../../../../Libraries/TurboModule/TurboModuleRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 93}}], "key": "H+9Pk6sLVUPsBv6YXnwcNYMfH5g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var TurboModuleRegistry = _interopRequireWildcard(require(_dependencyMap[0], \"../../../../Libraries/TurboModule/TurboModuleRegistry\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var NativeReactNativeFeatureFlags = TurboModuleRegistry.get('NativeReactNativeFeatureFlagsCxx');\n  var _default = exports.default = NativeReactNativeFeatureFlags;\n});", "lineCount": 10, "map": [[6, 2, 23, 0], [6, 6, 23, 0, "TurboModuleRegistry"], [6, 25, 23, 0], [6, 28, 23, 0, "_interopRequireWildcard"], [6, 51, 23, 0], [6, 52, 23, 0, "require"], [6, 59, 23, 0], [6, 60, 23, 0, "_dependencyMap"], [6, 74, 23, 0], [7, 2, 23, 93], [7, 11, 23, 93, "_interopRequireWildcard"], [7, 35, 23, 93, "e"], [7, 36, 23, 93], [7, 38, 23, 93, "t"], [7, 39, 23, 93], [7, 68, 23, 93, "WeakMap"], [7, 75, 23, 93], [7, 81, 23, 93, "r"], [7, 82, 23, 93], [7, 89, 23, 93, "WeakMap"], [7, 96, 23, 93], [7, 100, 23, 93, "n"], [7, 101, 23, 93], [7, 108, 23, 93, "WeakMap"], [7, 115, 23, 93], [7, 127, 23, 93, "_interopRequireWildcard"], [7, 150, 23, 93], [7, 162, 23, 93, "_interopRequireWildcard"], [7, 163, 23, 93, "e"], [7, 164, 23, 93], [7, 166, 23, 93, "t"], [7, 167, 23, 93], [7, 176, 23, 93, "t"], [7, 177, 23, 93], [7, 181, 23, 93, "e"], [7, 182, 23, 93], [7, 186, 23, 93, "e"], [7, 187, 23, 93], [7, 188, 23, 93, "__esModule"], [7, 198, 23, 93], [7, 207, 23, 93, "e"], [7, 208, 23, 93], [7, 214, 23, 93, "o"], [7, 215, 23, 93], [7, 217, 23, 93, "i"], [7, 218, 23, 93], [7, 220, 23, 93, "f"], [7, 221, 23, 93], [7, 226, 23, 93, "__proto__"], [7, 235, 23, 93], [7, 243, 23, 93, "default"], [7, 250, 23, 93], [7, 252, 23, 93, "e"], [7, 253, 23, 93], [7, 270, 23, 93, "e"], [7, 271, 23, 93], [7, 294, 23, 93, "e"], [7, 295, 23, 93], [7, 320, 23, 93, "e"], [7, 321, 23, 93], [7, 330, 23, 93, "f"], [7, 331, 23, 93], [7, 337, 23, 93, "o"], [7, 338, 23, 93], [7, 341, 23, 93, "t"], [7, 342, 23, 93], [7, 345, 23, 93, "n"], [7, 346, 23, 93], [7, 349, 23, 93, "r"], [7, 350, 23, 93], [7, 358, 23, 93, "o"], [7, 359, 23, 93], [7, 360, 23, 93, "has"], [7, 363, 23, 93], [7, 364, 23, 93, "e"], [7, 365, 23, 93], [7, 375, 23, 93, "o"], [7, 376, 23, 93], [7, 377, 23, 93, "get"], [7, 380, 23, 93], [7, 381, 23, 93, "e"], [7, 382, 23, 93], [7, 385, 23, 93, "o"], [7, 386, 23, 93], [7, 387, 23, 93, "set"], [7, 390, 23, 93], [7, 391, 23, 93, "e"], [7, 392, 23, 93], [7, 394, 23, 93, "f"], [7, 395, 23, 93], [7, 409, 23, 93, "_t"], [7, 411, 23, 93], [7, 415, 23, 93, "e"], [7, 416, 23, 93], [7, 432, 23, 93, "_t"], [7, 434, 23, 93], [7, 441, 23, 93, "hasOwnProperty"], [7, 455, 23, 93], [7, 456, 23, 93, "call"], [7, 460, 23, 93], [7, 461, 23, 93, "e"], [7, 462, 23, 93], [7, 464, 23, 93, "_t"], [7, 466, 23, 93], [7, 473, 23, 93, "i"], [7, 474, 23, 93], [7, 478, 23, 93, "o"], [7, 479, 23, 93], [7, 482, 23, 93, "Object"], [7, 488, 23, 93], [7, 489, 23, 93, "defineProperty"], [7, 503, 23, 93], [7, 508, 23, 93, "Object"], [7, 514, 23, 93], [7, 515, 23, 93, "getOwnPropertyDescriptor"], [7, 539, 23, 93], [7, 540, 23, 93, "e"], [7, 541, 23, 93], [7, 543, 23, 93, "_t"], [7, 545, 23, 93], [7, 552, 23, 93, "i"], [7, 553, 23, 93], [7, 554, 23, 93, "get"], [7, 557, 23, 93], [7, 561, 23, 93, "i"], [7, 562, 23, 93], [7, 563, 23, 93, "set"], [7, 566, 23, 93], [7, 570, 23, 93, "o"], [7, 571, 23, 93], [7, 572, 23, 93, "f"], [7, 573, 23, 93], [7, 575, 23, 93, "_t"], [7, 577, 23, 93], [7, 579, 23, 93, "i"], [7, 580, 23, 93], [7, 584, 23, 93, "f"], [7, 585, 23, 93], [7, 586, 23, 93, "_t"], [7, 588, 23, 93], [7, 592, 23, 93, "e"], [7, 593, 23, 93], [7, 594, 23, 93, "_t"], [7, 596, 23, 93], [7, 607, 23, 93, "f"], [7, 608, 23, 93], [7, 613, 23, 93, "e"], [7, 614, 23, 93], [7, 616, 23, 93, "t"], [7, 617, 23, 93], [8, 2, 73, 0], [8, 6, 73, 6, "NativeReactNativeFeatureFlags"], [8, 35, 73, 42], [8, 38, 73, 45, "TurboModuleRegistry"], [8, 57, 73, 64], [8, 58, 73, 65, "get"], [8, 61, 73, 68], [8, 62, 74, 2], [8, 96, 75, 0], [8, 97, 75, 1], [9, 2, 75, 2], [9, 6, 75, 2, "_default"], [9, 14, 75, 2], [9, 17, 75, 2, "exports"], [9, 24, 75, 2], [9, 25, 75, 2, "default"], [9, 32, 75, 2], [9, 35, 77, 15, "NativeReactNativeFeatureFlags"], [9, 64, 77, 44], [10, 0, 77, 44], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}