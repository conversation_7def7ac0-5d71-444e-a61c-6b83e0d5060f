{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.VirtualizedListCellContextProvider = VirtualizedListCellContextProvider;\n  exports.VirtualizedListContext = void 0;\n  exports.VirtualizedListContextProvider = VirtualizedListContextProvider;\n  exports.VirtualizedListContextResetter = VirtualizedListContextResetter;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var React = _react;\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\@react-native\\\\virtualized-lists\\\\Lists\\\\VirtualizedListContext.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var VirtualizedListContext = exports.VirtualizedListContext = /*#__PURE__*/React.createContext(null);\n  if (__DEV__) {\n    VirtualizedListContext.displayName = 'VirtualizedListContext';\n  }\n  function VirtualizedListContextResetter(_ref) {\n    var children = _ref.children;\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(VirtualizedListContext.Provider, {\n      value: null,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 5\n    }, this);\n  }\n  function VirtualizedListContextProvider(_ref2) {\n    var children = _ref2.children,\n      value = _ref2.value;\n    var context = (0, _react.useMemo)(() => ({\n      cellKey: null,\n      getScrollMetrics: value.getScrollMetrics,\n      horizontal: value.horizontal,\n      getOutermostParentListRef: value.getOutermostParentListRef,\n      registerAsNestedChild: value.registerAsNestedChild,\n      unregisterAsNestedChild: value.unregisterAsNestedChild\n    }), [value.getScrollMetrics, value.horizontal, value.getOutermostParentListRef, value.registerAsNestedChild, value.unregisterAsNestedChild]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(VirtualizedListContext.Provider, {\n      value: context,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 5\n    }, this);\n  }\n  function VirtualizedListCellContextProvider(_ref3) {\n    var cellKey = _ref3.cellKey,\n      children = _ref3.children;\n    var currContext = (0, _react.useContext)(VirtualizedListContext);\n    var context = (0, _react.useMemo)(() => currContext == null ? null : {\n      ...currContext,\n      cellKey\n    }, [currContext, cellKey]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(VirtualizedListContext.Provider, {\n      value: context,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 5\n    }, this);\n  }\n});", "lineCount": 66, "map": [[9, 2, 13, 0], [9, 6, 13, 0, "_react"], [9, 12, 13, 0], [9, 15, 13, 0, "_interopRequireWildcard"], [9, 38, 13, 0], [9, 39, 13, 0, "require"], [9, 46, 13, 0], [9, 47, 13, 0, "_dependencyMap"], [9, 61, 13, 0], [10, 2, 13, 31], [10, 6, 13, 31, "React"], [10, 11, 13, 31], [10, 14, 13, 31, "_react"], [10, 20, 13, 31], [11, 2, 13, 31], [11, 6, 13, 31, "_jsxDevRuntime"], [11, 20, 13, 31], [11, 23, 13, 31, "require"], [11, 30, 13, 31], [11, 31, 13, 31, "_dependencyMap"], [11, 45, 13, 31], [12, 2, 13, 31], [12, 6, 13, 31, "_jsxFileName"], [12, 18, 13, 31], [13, 2, 13, 31], [13, 11, 13, 31, "_interopRequireWildcard"], [13, 35, 13, 31, "e"], [13, 36, 13, 31], [13, 38, 13, 31, "t"], [13, 39, 13, 31], [13, 68, 13, 31, "WeakMap"], [13, 75, 13, 31], [13, 81, 13, 31, "r"], [13, 82, 13, 31], [13, 89, 13, 31, "WeakMap"], [13, 96, 13, 31], [13, 100, 13, 31, "n"], [13, 101, 13, 31], [13, 108, 13, 31, "WeakMap"], [13, 115, 13, 31], [13, 127, 13, 31, "_interopRequireWildcard"], [13, 150, 13, 31], [13, 162, 13, 31, "_interopRequireWildcard"], [13, 163, 13, 31, "e"], [13, 164, 13, 31], [13, 166, 13, 31, "t"], [13, 167, 13, 31], [13, 176, 13, 31, "t"], [13, 177, 13, 31], [13, 181, 13, 31, "e"], [13, 182, 13, 31], [13, 186, 13, 31, "e"], [13, 187, 13, 31], [13, 188, 13, 31, "__esModule"], [13, 198, 13, 31], [13, 207, 13, 31, "e"], [13, 208, 13, 31], [13, 214, 13, 31, "o"], [13, 215, 13, 31], [13, 217, 13, 31, "i"], [13, 218, 13, 31], [13, 220, 13, 31, "f"], [13, 221, 13, 31], [13, 226, 13, 31, "__proto__"], [13, 235, 13, 31], [13, 243, 13, 31, "default"], [13, 250, 13, 31], [13, 252, 13, 31, "e"], [13, 253, 13, 31], [13, 270, 13, 31, "e"], [13, 271, 13, 31], [13, 294, 13, 31, "e"], [13, 295, 13, 31], [13, 320, 13, 31, "e"], [13, 321, 13, 31], [13, 330, 13, 31, "f"], [13, 331, 13, 31], [13, 337, 13, 31, "o"], [13, 338, 13, 31], [13, 341, 13, 31, "t"], [13, 342, 13, 31], [13, 345, 13, 31, "n"], [13, 346, 13, 31], [13, 349, 13, 31, "r"], [13, 350, 13, 31], [13, 358, 13, 31, "o"], [13, 359, 13, 31], [13, 360, 13, 31, "has"], [13, 363, 13, 31], [13, 364, 13, 31, "e"], [13, 365, 13, 31], [13, 375, 13, 31, "o"], [13, 376, 13, 31], [13, 377, 13, 31, "get"], [13, 380, 13, 31], [13, 381, 13, 31, "e"], [13, 382, 13, 31], [13, 385, 13, 31, "o"], [13, 386, 13, 31], [13, 387, 13, 31, "set"], [13, 390, 13, 31], [13, 391, 13, 31, "e"], [13, 392, 13, 31], [13, 394, 13, 31, "f"], [13, 395, 13, 31], [13, 409, 13, 31, "_t"], [13, 411, 13, 31], [13, 415, 13, 31, "e"], [13, 416, 13, 31], [13, 432, 13, 31, "_t"], [13, 434, 13, 31], [13, 441, 13, 31, "hasOwnProperty"], [13, 455, 13, 31], [13, 456, 13, 31, "call"], [13, 460, 13, 31], [13, 461, 13, 31, "e"], [13, 462, 13, 31], [13, 464, 13, 31, "_t"], [13, 466, 13, 31], [13, 473, 13, 31, "i"], [13, 474, 13, 31], [13, 478, 13, 31, "o"], [13, 479, 13, 31], [13, 482, 13, 31, "Object"], [13, 488, 13, 31], [13, 489, 13, 31, "defineProperty"], [13, 503, 13, 31], [13, 508, 13, 31, "Object"], [13, 514, 13, 31], [13, 515, 13, 31, "getOwnPropertyDescriptor"], [13, 539, 13, 31], [13, 540, 13, 31, "e"], [13, 541, 13, 31], [13, 543, 13, 31, "_t"], [13, 545, 13, 31], [13, 552, 13, 31, "i"], [13, 553, 13, 31], [13, 554, 13, 31, "get"], [13, 557, 13, 31], [13, 561, 13, 31, "i"], [13, 562, 13, 31], [13, 563, 13, 31, "set"], [13, 566, 13, 31], [13, 570, 13, 31, "o"], [13, 571, 13, 31], [13, 572, 13, 31, "f"], [13, 573, 13, 31], [13, 575, 13, 31, "_t"], [13, 577, 13, 31], [13, 579, 13, 31, "i"], [13, 580, 13, 31], [13, 584, 13, 31, "f"], [13, 585, 13, 31], [13, 586, 13, 31, "_t"], [13, 588, 13, 31], [13, 592, 13, 31, "e"], [13, 593, 13, 31], [13, 594, 13, 31, "_t"], [13, 596, 13, 31], [13, 607, 13, 31, "f"], [13, 608, 13, 31], [13, 613, 13, 31, "e"], [13, 614, 13, 31], [13, 616, 13, 31, "t"], [13, 617, 13, 31], [14, 2, 37, 7], [14, 6, 37, 13, "VirtualizedListContext"], [14, 28, 37, 60], [14, 31, 37, 60, "exports"], [14, 38, 37, 60], [14, 39, 37, 60, "VirtualizedListContext"], [14, 61, 37, 60], [14, 77, 38, 2, "React"], [14, 82, 38, 7], [14, 83, 38, 8, "createContext"], [14, 96, 38, 21], [14, 97, 38, 22], [14, 101, 38, 26], [14, 102, 38, 27], [15, 2, 39, 0], [15, 6, 39, 4, "__DEV__"], [15, 13, 39, 11], [15, 15, 39, 13], [16, 4, 40, 2, "VirtualizedListContext"], [16, 26, 40, 24], [16, 27, 40, 25, "displayName"], [16, 38, 40, 36], [16, 41, 40, 39], [16, 65, 40, 63], [17, 2, 41, 0], [18, 2, 46, 7], [18, 11, 46, 16, "VirtualizedListContextResetter"], [18, 41, 46, 46, "VirtualizedListContextResetter"], [18, 42, 46, 46, "_ref"], [18, 46, 46, 46], [18, 48, 50, 15], [19, 4, 50, 15], [19, 8, 47, 2, "children"], [19, 16, 47, 10], [19, 19, 47, 10, "_ref"], [19, 23, 47, 10], [19, 24, 47, 2, "children"], [19, 32, 47, 10], [20, 4, 51, 2], [20, 24, 52, 4], [20, 28, 52, 4, "_jsxDevRuntime"], [20, 42, 52, 4], [20, 43, 52, 4, "jsxDEV"], [20, 49, 52, 4], [20, 51, 52, 5, "VirtualizedListContext"], [20, 73, 52, 27], [20, 74, 52, 28, "Provider"], [20, 82, 52, 36], [21, 6, 52, 37, "value"], [21, 11, 52, 42], [21, 13, 52, 44], [21, 17, 52, 49], [22, 6, 52, 49, "children"], [22, 14, 52, 49], [22, 16, 53, 7, "children"], [23, 4, 53, 15], [24, 6, 53, 15, "fileName"], [24, 14, 53, 15], [24, 16, 53, 15, "_jsxFileName"], [24, 28, 53, 15], [25, 6, 53, 15, "lineNumber"], [25, 16, 53, 15], [26, 6, 53, 15, "columnNumber"], [26, 18, 53, 15], [27, 4, 53, 15], [27, 11, 54, 37], [27, 12, 54, 38], [28, 2, 56, 0], [29, 2, 61, 7], [29, 11, 61, 16, "VirtualizedListContextProvider"], [29, 41, 61, 46, "VirtualizedListContextProvider"], [29, 42, 61, 46, "_ref2"], [29, 47, 61, 46], [29, 49, 67, 15], [30, 4, 67, 15], [30, 8, 62, 2, "children"], [30, 16, 62, 10], [30, 19, 62, 10, "_ref2"], [30, 24, 62, 10], [30, 25, 62, 2, "children"], [30, 33, 62, 10], [31, 6, 63, 2, "value"], [31, 11, 63, 7], [31, 14, 63, 7, "_ref2"], [31, 19, 63, 7], [31, 20, 63, 2, "value"], [31, 25, 63, 7], [32, 4, 69, 2], [32, 8, 69, 8, "context"], [32, 15, 69, 15], [32, 18, 69, 18], [32, 22, 69, 18, "useMemo"], [32, 36, 69, 25], [32, 38, 70, 4], [32, 45, 70, 11], [33, 6, 71, 6, "cellKey"], [33, 13, 71, 13], [33, 15, 71, 15], [33, 19, 71, 19], [34, 6, 72, 6, "getScrollMetrics"], [34, 22, 72, 22], [34, 24, 72, 24, "value"], [34, 29, 72, 29], [34, 30, 72, 30, "getScrollMetrics"], [34, 46, 72, 46], [35, 6, 73, 6, "horizontal"], [35, 16, 73, 16], [35, 18, 73, 18, "value"], [35, 23, 73, 23], [35, 24, 73, 24, "horizontal"], [35, 34, 73, 34], [36, 6, 74, 6, "getOutermostParentListRef"], [36, 31, 74, 31], [36, 33, 74, 33, "value"], [36, 38, 74, 38], [36, 39, 74, 39, "getOutermostParentListRef"], [36, 64, 74, 64], [37, 6, 75, 6, "registerAsNestedChild"], [37, 27, 75, 27], [37, 29, 75, 29, "value"], [37, 34, 75, 34], [37, 35, 75, 35, "registerAsNestedChild"], [37, 56, 75, 56], [38, 6, 76, 6, "unregisterAsNestedChild"], [38, 29, 76, 29], [38, 31, 76, 31, "value"], [38, 36, 76, 36], [38, 37, 76, 37, "unregisterAsNestedChild"], [39, 4, 77, 4], [39, 5, 77, 5], [39, 6, 77, 6], [39, 8, 78, 4], [39, 9, 79, 6, "value"], [39, 14, 79, 11], [39, 15, 79, 12, "getScrollMetrics"], [39, 31, 79, 28], [39, 33, 80, 6, "value"], [39, 38, 80, 11], [39, 39, 80, 12, "horizontal"], [39, 49, 80, 22], [39, 51, 81, 6, "value"], [39, 56, 81, 11], [39, 57, 81, 12, "getOutermostParentListRef"], [39, 82, 81, 37], [39, 84, 82, 6, "value"], [39, 89, 82, 11], [39, 90, 82, 12, "registerAsNestedChild"], [39, 111, 82, 33], [39, 113, 83, 6, "value"], [39, 118, 83, 11], [39, 119, 83, 12, "unregisterAsNestedChild"], [39, 142, 83, 35], [39, 143, 85, 2], [39, 144, 85, 3], [40, 4, 86, 2], [40, 24, 87, 4], [40, 28, 87, 4, "_jsxDevRuntime"], [40, 42, 87, 4], [40, 43, 87, 4, "jsxDEV"], [40, 49, 87, 4], [40, 51, 87, 5, "VirtualizedListContext"], [40, 73, 87, 27], [40, 74, 87, 28, "Provider"], [40, 82, 87, 36], [41, 6, 87, 37, "value"], [41, 11, 87, 42], [41, 13, 87, 44, "context"], [41, 20, 87, 52], [42, 6, 87, 52, "children"], [42, 14, 87, 52], [42, 16, 88, 7, "children"], [43, 4, 88, 15], [44, 6, 88, 15, "fileName"], [44, 14, 88, 15], [44, 16, 88, 15, "_jsxFileName"], [44, 28, 88, 15], [45, 6, 88, 15, "lineNumber"], [45, 16, 88, 15], [46, 6, 88, 15, "columnNumber"], [46, 18, 88, 15], [47, 4, 88, 15], [47, 11, 89, 37], [47, 12, 89, 38], [48, 2, 91, 0], [49, 2, 96, 7], [49, 11, 96, 16, "VirtualizedListCellContextProvider"], [49, 45, 96, 50, "VirtualizedListCellContextProvider"], [49, 46, 96, 50, "_ref3"], [49, 51, 96, 50], [49, 53, 102, 15], [50, 4, 102, 15], [50, 8, 97, 2, "cellKey"], [50, 15, 97, 9], [50, 18, 97, 9, "_ref3"], [50, 23, 97, 9], [50, 24, 97, 2, "cellKey"], [50, 31, 97, 9], [51, 6, 98, 2, "children"], [51, 14, 98, 10], [51, 17, 98, 10, "_ref3"], [51, 22, 98, 10], [51, 23, 98, 2, "children"], [51, 31, 98, 10], [52, 4, 104, 2], [52, 8, 104, 8, "currContext"], [52, 19, 104, 19], [52, 22, 104, 22], [52, 26, 104, 22, "useContext"], [52, 43, 104, 32], [52, 45, 104, 33, "VirtualizedListContext"], [52, 67, 104, 55], [52, 68, 104, 56], [53, 4, 105, 2], [53, 8, 105, 8, "context"], [53, 15, 105, 15], [53, 18, 105, 18], [53, 22, 105, 18, "useMemo"], [53, 36, 105, 25], [53, 38, 106, 4], [53, 44, 106, 11, "currContext"], [53, 55, 106, 22], [53, 59, 106, 26], [53, 63, 106, 30], [53, 66, 106, 33], [53, 70, 106, 37], [53, 73, 106, 40], [54, 6, 106, 41], [54, 9, 106, 44, "currContext"], [54, 20, 106, 55], [55, 6, 106, 57, "cellKey"], [56, 4, 106, 64], [56, 5, 106, 66], [56, 7, 107, 4], [56, 8, 107, 5, "currContext"], [56, 19, 107, 16], [56, 21, 107, 18, "cellKey"], [56, 28, 107, 25], [56, 29, 108, 2], [56, 30, 108, 3], [57, 4, 109, 2], [57, 24, 110, 4], [57, 28, 110, 4, "_jsxDevRuntime"], [57, 42, 110, 4], [57, 43, 110, 4, "jsxDEV"], [57, 49, 110, 4], [57, 51, 110, 5, "VirtualizedListContext"], [57, 73, 110, 27], [57, 74, 110, 28, "Provider"], [57, 82, 110, 36], [58, 6, 110, 37, "value"], [58, 11, 110, 42], [58, 13, 110, 44, "context"], [58, 20, 110, 52], [59, 6, 110, 52, "children"], [59, 14, 110, 52], [59, 16, 111, 7, "children"], [60, 4, 111, 15], [61, 6, 111, 15, "fileName"], [61, 14, 111, 15], [61, 16, 111, 15, "_jsxFileName"], [61, 28, 111, 15], [62, 6, 111, 15, "lineNumber"], [62, 16, 111, 15], [63, 6, 111, 15, "columnNumber"], [63, 18, 111, 15], [64, 4, 111, 15], [64, 11, 112, 37], [64, 12, 112, 38], [65, 2, 114, 0], [66, 0, 114, 1], [66, 3]], "functionMap": {"names": ["<global>", "VirtualizedListContextResetter", "VirtualizedListContextProvider", "useMemo$argument_0", "VirtualizedListCellContextProvider"], "mappings": "AAA;OC6C;CDU;OEK;ICS;MDO;CFc;OIK;IDU,8DC"}}, "type": "js/module"}]}