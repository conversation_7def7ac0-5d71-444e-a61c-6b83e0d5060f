{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 68}, "end": {"line": 4, "column": 62, "index": 130}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 131}, "end": {"line": 5, "column": 48, "index": 179}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderTitle = HeaderTitle;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _native = require(_dependencyMap[2], \"@react-navigation/native\");\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _jsxRuntime = require(_dependencyMap[4], \"react/jsx-runtime\");\n  var _excluded = [\"tintColor\", \"style\"];\n  function HeaderTitle(_ref) {\n    var tintColor = _ref.tintColor,\n      style = _ref.style,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors,\n      fonts = _useTheme.fonts;\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.Text, {\n      role: \"heading\",\n      \"aria-level\": \"1\",\n      numberOfLines: 1,\n      ...rest,\n      style: [{\n        color: tintColor === undefined ? colors.text : tintColor\n      }, _reactNative.Platform.select({\n        ios: fonts.bold,\n        default: fonts.medium\n      }), styles.title, style]\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    title: _reactNative.Platform.select({\n      ios: {\n        fontSize: 17\n      },\n      android: {\n        fontSize: 20\n      },\n      default: {\n        fontSize: 18\n      }\n    })\n  });\n});", "lineCount": 47, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 21, 1, 13], [8, 24, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 35, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_native"], [10, 13, 3, 0], [10, 16, 3, 0, "require"], [10, 23, 3, 0], [10, 24, 3, 0, "_dependencyMap"], [10, 38, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_reactNative"], [11, 18, 4, 0], [11, 21, 4, 0, "require"], [11, 28, 4, 0], [11, 29, 4, 0, "_dependencyMap"], [11, 43, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_jsxRuntime"], [12, 17, 5, 0], [12, 20, 5, 0, "require"], [12, 27, 5, 0], [12, 28, 5, 0, "_dependencyMap"], [12, 42, 5, 0], [13, 2, 5, 48], [13, 6, 5, 48, "_excluded"], [13, 15, 5, 48], [14, 2, 6, 7], [14, 11, 6, 16, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [14, 22, 6, 27, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [14, 23, 6, 27, "_ref"], [14, 27, 6, 27], [14, 29, 10, 3], [15, 4, 10, 3], [15, 8, 7, 2, "tintColor"], [15, 17, 7, 11], [15, 20, 7, 11, "_ref"], [15, 24, 7, 11], [15, 25, 7, 2, "tintColor"], [15, 34, 7, 11], [16, 6, 8, 2, "style"], [16, 11, 8, 7], [16, 14, 8, 7, "_ref"], [16, 18, 8, 7], [16, 19, 8, 2, "style"], [16, 24, 8, 7], [17, 6, 9, 5, "rest"], [17, 10, 9, 9], [17, 17, 9, 9, "_objectWithoutProperties2"], [17, 42, 9, 9], [17, 43, 9, 9, "default"], [17, 50, 9, 9], [17, 52, 9, 9, "_ref"], [17, 56, 9, 9], [17, 58, 9, 9, "_excluded"], [17, 67, 9, 9], [18, 4, 11, 2], [18, 8, 11, 2, "_useTheme"], [18, 17, 11, 2], [18, 20, 14, 6], [18, 24, 14, 6, "useTheme"], [18, 40, 14, 14], [18, 42, 14, 15], [18, 43, 14, 16], [19, 6, 12, 4, "colors"], [19, 12, 12, 10], [19, 15, 12, 10, "_useTheme"], [19, 24, 12, 10], [19, 25, 12, 4, "colors"], [19, 31, 12, 10], [20, 6, 13, 4, "fonts"], [20, 11, 13, 9], [20, 14, 13, 9, "_useTheme"], [20, 23, 13, 9], [20, 24, 13, 4, "fonts"], [20, 29, 13, 9], [21, 4, 15, 2], [21, 11, 15, 9], [21, 24, 15, 22], [21, 28, 15, 22, "_jsx"], [21, 43, 15, 26], [21, 45, 15, 27, "Animated"], [21, 66, 15, 35], [21, 67, 15, 36, "Text"], [21, 71, 15, 40], [21, 73, 15, 42], [22, 6, 16, 4, "role"], [22, 10, 16, 8], [22, 12, 16, 10], [22, 21, 16, 19], [23, 6, 17, 4], [23, 18, 17, 16], [23, 20, 17, 18], [23, 23, 17, 21], [24, 6, 18, 4, "numberOfLines"], [24, 19, 18, 17], [24, 21, 18, 19], [24, 22, 18, 20], [25, 6, 19, 4], [25, 9, 19, 7, "rest"], [25, 13, 19, 11], [26, 6, 20, 4, "style"], [26, 11, 20, 9], [26, 13, 20, 11], [26, 14, 20, 12], [27, 8, 21, 6, "color"], [27, 13, 21, 11], [27, 15, 21, 13, "tintColor"], [27, 24, 21, 22], [27, 29, 21, 27, "undefined"], [27, 38, 21, 36], [27, 41, 21, 39, "colors"], [27, 47, 21, 45], [27, 48, 21, 46, "text"], [27, 52, 21, 50], [27, 55, 21, 53, "tintColor"], [28, 6, 22, 4], [28, 7, 22, 5], [28, 9, 22, 7, "Platform"], [28, 30, 22, 15], [28, 31, 22, 16, "select"], [28, 37, 22, 22], [28, 38, 22, 23], [29, 8, 23, 6, "ios"], [29, 11, 23, 9], [29, 13, 23, 11, "fonts"], [29, 18, 23, 16], [29, 19, 23, 17, "bold"], [29, 23, 23, 21], [30, 8, 24, 6, "default"], [30, 15, 24, 13], [30, 17, 24, 15, "fonts"], [30, 22, 24, 20], [30, 23, 24, 21, "medium"], [31, 6, 25, 4], [31, 7, 25, 5], [31, 8, 25, 6], [31, 10, 25, 8, "styles"], [31, 16, 25, 14], [31, 17, 25, 15, "title"], [31, 22, 25, 20], [31, 24, 25, 22, "style"], [31, 29, 25, 27], [32, 4, 26, 2], [32, 5, 26, 3], [32, 6, 26, 4], [33, 2, 27, 0], [34, 2, 28, 0], [34, 6, 28, 6, "styles"], [34, 12, 28, 12], [34, 15, 28, 15, "StyleSheet"], [34, 38, 28, 25], [34, 39, 28, 26, "create"], [34, 45, 28, 32], [34, 46, 28, 33], [35, 4, 29, 2, "title"], [35, 9, 29, 7], [35, 11, 29, 9, "Platform"], [35, 32, 29, 17], [35, 33, 29, 18, "select"], [35, 39, 29, 24], [35, 40, 29, 25], [36, 6, 30, 4, "ios"], [36, 9, 30, 7], [36, 11, 30, 9], [37, 8, 31, 6, "fontSize"], [37, 16, 31, 14], [37, 18, 31, 16], [38, 6, 32, 4], [38, 7, 32, 5], [39, 6, 33, 4, "android"], [39, 13, 33, 11], [39, 15, 33, 13], [40, 8, 34, 6, "fontSize"], [40, 16, 34, 14], [40, 18, 34, 16], [41, 6, 35, 4], [41, 7, 35, 5], [42, 6, 36, 4, "default"], [42, 13, 36, 11], [42, 15, 36, 13], [43, 8, 37, 6, "fontSize"], [43, 16, 37, 14], [43, 18, 37, 16], [44, 6, 38, 4], [45, 4, 39, 2], [45, 5, 39, 3], [46, 2, 40, 0], [46, 3, 40, 1], [46, 4, 40, 2], [47, 0, 40, 3], [47, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;OCK;CDqB"}}, "type": "js/module"}]}