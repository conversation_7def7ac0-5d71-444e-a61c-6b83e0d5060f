{"dependencies": [{"name": "react-native/Libraries/Renderer/shims/ReactFabric", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 26, "index": 766}, "end": {"line": 15, "column": 86, "index": 826}}], "key": "4C8TqIjR7vX3v3JWvuJOu+XiEA8=", "exportNames": ["*"], "isOptional": true}}, {"name": "react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 35, "column": 8, "index": 1834}, "end": {"line": 35, "column": 105, "index": 1931}}], "key": "GHOwPKsHyqKPAI7qNK0GYb/HcIc=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getShadowNodeFromRef = getShadowNodeFromRef;\n  // Used by GestureDetector (unsupported on web at the moment) to check whether the\n  // attached view may get flattened on Fabric. This implementation causes errors\n  // on web due to the static resolution of `require` statements by webpack breaking\n  // the conditional importing. Solved by making .web file.\n  var findHostInstance_DEPRECATED;\n  var getInternalInstanceHandleFromPublicInstance;\n  function getShadowNodeFromRef(ref) {\n    // Load findHostInstance_DEPRECATED lazily because it may not be available before render\n    if (findHostInstance_DEPRECATED === undefined) {\n      try {\n        // eslint-disable-next-line @typescript-eslint/no-var-requires\n        var ReactFabric = require(_dependencyMap[0], \"react-native/Libraries/Renderer/shims/ReactFabric\");\n        // Since RN 0.77 ReactFabric exports findHostInstance_DEPRECATED in default object so we're trying to\n        // access it first, then fallback on named export\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        findHostInstance_DEPRECATED =\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        ReactFabric?.default?.findHostInstance_DEPRECATED ||\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        ReactFabric?.findHostInstance_DEPRECATED;\n      } catch (e) {\n        findHostInstance_DEPRECATED = _ref => null;\n      }\n    }\n\n    // Load findHostInstance_DEPRECATED lazily because it may not be available before render\n    if (getInternalInstanceHandleFromPublicInstance === undefined) {\n      try {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        getInternalInstanceHandleFromPublicInstance =\n        // eslint-disable-next-line @typescript-eslint/no-var-requires\n        require(_dependencyMap[1], \"react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance\").getInternalInstanceHandleFromPublicInstance ?? (\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-return\n        ref => ref._internalInstanceHandle);\n      } catch (e) {\n        getInternalInstanceHandleFromPublicInstance = ref =>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-return\n        ref._internalInstanceHandle;\n      }\n    }\n\n    // @ts-ignore Fabric\n    return getInternalInstanceHandleFromPublicInstance(findHostInstance_DEPRECATED(ref)).stateNode.node;\n  }\n});", "lineCount": 50, "map": [[6, 2, 1, 0], [7, 2, 2, 0], [8, 2, 3, 0], [9, 2, 4, 0], [10, 2, 5, 0], [10, 6, 5, 4, "findHostInstance_DEPRECATED"], [10, 33, 5, 55], [11, 2, 6, 0], [11, 6, 6, 4, "getInternalInstanceHandleFromPublicInstance"], [11, 49, 8, 1], [12, 2, 10, 7], [12, 11, 10, 16, "getShadowNodeFromRef"], [12, 31, 10, 36, "getShadowNodeFromRef"], [12, 32, 10, 37, "ref"], [12, 35, 10, 49], [12, 37, 10, 51], [13, 4, 11, 2], [14, 4, 12, 2], [14, 8, 12, 6, "findHostInstance_DEPRECATED"], [14, 35, 12, 33], [14, 40, 12, 38, "undefined"], [14, 49, 12, 47], [14, 51, 12, 49], [15, 6, 13, 4], [15, 10, 13, 8], [16, 8, 14, 6], [17, 8, 15, 6], [17, 12, 15, 12, "ReactFabric"], [17, 23, 15, 23], [17, 26, 15, 26, "require"], [17, 33, 15, 33], [17, 34, 15, 33, "_dependencyMap"], [17, 48, 15, 33], [17, 104, 15, 85], [17, 105, 15, 86], [18, 8, 16, 6], [19, 8, 17, 6], [20, 8, 18, 6], [21, 8, 19, 6, "findHostInstance_DEPRECATED"], [21, 35, 19, 33], [22, 8, 20, 8], [23, 8, 21, 8, "ReactFabric"], [23, 19, 21, 19], [23, 21, 21, 21, "default"], [23, 28, 21, 28], [23, 30, 21, 30, "findHostInstance_DEPRECATED"], [23, 57, 21, 57], [24, 8, 22, 8], [25, 8, 23, 8, "ReactFabric"], [25, 19, 23, 19], [25, 21, 23, 21, "findHostInstance_DEPRECATED"], [25, 48, 23, 48], [26, 6, 24, 4], [26, 7, 24, 5], [26, 8, 24, 6], [26, 15, 24, 13, "e"], [26, 16, 24, 14], [26, 18, 24, 16], [27, 8, 25, 6, "findHostInstance_DEPRECATED"], [27, 35, 25, 33], [27, 38, 25, 37, "_ref"], [27, 42, 25, 50], [27, 46, 25, 55], [27, 50, 25, 59], [28, 6, 26, 4], [29, 4, 27, 2], [31, 4, 29, 2], [32, 4, 30, 2], [32, 8, 30, 6, "getInternalInstanceHandleFromPublicInstance"], [32, 51, 30, 49], [32, 56, 30, 54, "undefined"], [32, 65, 30, 63], [32, 67, 30, 65], [33, 6, 31, 4], [33, 10, 31, 8], [34, 8, 32, 6], [35, 8, 33, 6, "getInternalInstanceHandleFromPublicInstance"], [35, 51, 33, 49], [36, 8, 34, 8], [37, 8, 35, 8, "require"], [37, 15, 35, 15], [37, 16, 35, 15, "_dependencyMap"], [37, 30, 35, 15], [37, 123, 35, 104], [37, 124, 35, 105], [37, 125, 36, 11, "getInternalInstanceHandleFromPublicInstance"], [37, 168, 36, 54], [38, 8, 37, 8], [39, 8, 38, 10, "ref"], [39, 11, 38, 18], [39, 15, 38, 23, "ref"], [39, 18, 38, 26], [39, 19, 38, 27, "_internalInstanceHandle"], [39, 42, 38, 50], [39, 43, 38, 51], [40, 6, 39, 4], [40, 7, 39, 5], [40, 8, 39, 6], [40, 15, 39, 13, "e"], [40, 16, 39, 14], [40, 18, 39, 16], [41, 8, 40, 6, "getInternalInstanceHandleFromPublicInstance"], [41, 51, 40, 49], [41, 54, 40, 53, "ref"], [41, 57, 40, 61], [42, 8, 41, 8], [43, 8, 42, 8, "ref"], [43, 11, 42, 11], [43, 12, 42, 12, "_internalInstanceHandle"], [43, 35, 42, 35], [44, 6, 43, 4], [45, 4, 44, 2], [47, 4, 46, 2], [48, 4, 47, 2], [48, 11, 47, 9, "getInternalInstanceHandleFromPublicInstance"], [48, 54, 47, 52], [48, 55, 48, 4, "findHostInstance_DEPRECATED"], [48, 82, 48, 31], [48, 83, 48, 32, "ref"], [48, 86, 48, 35], [48, 87, 49, 2], [48, 88, 49, 3], [48, 89, 49, 4, "stateNode"], [48, 98, 49, 13], [48, 99, 49, 14, "node"], [48, 103, 49, 18], [49, 2, 50, 0], [50, 0, 50, 1], [50, 3]], "functionMap": {"names": ["<global>", "getShadowNodeFromRef", "findHostInstance_DEPRECATED", "<anonymous>", "getInternalInstanceHandleFromPublicInstance"], "mappings": "AAA;OCS;oCCe,uBD;SEa,yCF;oDGE;mCHE;CDQ"}}, "type": "js/module"}]}