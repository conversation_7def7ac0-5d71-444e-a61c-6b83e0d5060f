{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./internals/NodeInternals", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 65}}], "key": "F4zamoEwysYHV6P3i/QfSQO3bPI=", "exportNames": ["*"]}}, {"name": "./internals/Traversal", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 56}}], "key": "BTn3T51wa079mxjQK/Vlj/3Q1Cc=", "exportNames": ["*"]}}, {"name": "./ReadOnlyNode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 42}}], "key": "OYDeHP1Dzx6fXOFHsRIU9CjY1bo=", "exportNames": ["*"]}}, {"name": "./specs/NativeDOM", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 42}}], "key": "9AthY4AxLdDxCbVd7pMFoUw/FmE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _NodeInternals = require(_dependencyMap[6], \"./internals/NodeInternals\");\n  var _Traversal = require(_dependencyMap[7], \"./internals/Traversal\");\n  var _ReadOnlyNode2 = _interopRequireDefault(require(_dependencyMap[8], \"./ReadOnlyNode\"));\n  var _NativeDOM = _interopRequireDefault(require(_dependencyMap[9], \"./specs/NativeDOM\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var ReadOnlyCharacterData = exports.default = /*#__PURE__*/function (_ReadOnlyNode) {\n    function ReadOnlyCharacterData() {\n      (0, _classCallCheck2.default)(this, ReadOnlyCharacterData);\n      return _callSuper(this, ReadOnlyCharacterData, arguments);\n    }\n    (0, _inherits2.default)(ReadOnlyCharacterData, _ReadOnlyNode);\n    return (0, _createClass2.default)(ReadOnlyCharacterData, [{\n      key: \"nextElementSibling\",\n      get: function () {\n        return (0, _Traversal.getElementSibling)(this, 'next');\n      }\n    }, {\n      key: \"previousElementSibling\",\n      get: function () {\n        return (0, _Traversal.getElementSibling)(this, 'previous');\n      }\n    }, {\n      key: \"data\",\n      get: function () {\n        var node = (0, _NodeInternals.getNativeTextReference)(this);\n        if (node != null) {\n          return _NativeDOM.default.getTextContent(node);\n        }\n        return '';\n      }\n    }, {\n      key: \"length\",\n      get: function () {\n        return this.data.length;\n      }\n    }, {\n      key: \"textContent\",\n      get: function () {\n        return this.data;\n      }\n    }, {\n      key: \"nodeValue\",\n      get: function () {\n        return this.data;\n      }\n    }, {\n      key: \"substringData\",\n      value: function substringData(offset, count) {\n        var data = this.data;\n        if (offset < 0) {\n          throw new TypeError(`Failed to execute 'substringData' on 'CharacterData': The offset ${offset} is negative.`);\n        }\n        if (offset > data.length) {\n          throw new TypeError(`Failed to execute 'substringData' on 'CharacterData': The offset ${offset} is greater than the node's length (${data.length}).`);\n        }\n        var adjustedCount = count < 0 || count > data.length ? data.length : count;\n        return data.slice(offset, offset + adjustedCount);\n      }\n    }]);\n  }(_ReadOnlyNode2.default);\n});", "lineCount": 73, "map": [[12, 2, 15, 0], [12, 6, 15, 0, "_NodeInternals"], [12, 20, 15, 0], [12, 23, 15, 0, "require"], [12, 30, 15, 0], [12, 31, 15, 0, "_dependencyMap"], [12, 45, 15, 0], [13, 2, 16, 0], [13, 6, 16, 0, "_Traversal"], [13, 16, 16, 0], [13, 19, 16, 0, "require"], [13, 26, 16, 0], [13, 27, 16, 0, "_dependencyMap"], [13, 41, 16, 0], [14, 2, 17, 0], [14, 6, 17, 0, "_ReadOnlyNode2"], [14, 20, 17, 0], [14, 23, 17, 0, "_interopRequireDefault"], [14, 45, 17, 0], [14, 46, 17, 0, "require"], [14, 53, 17, 0], [14, 54, 17, 0, "_dependencyMap"], [14, 68, 17, 0], [15, 2, 18, 0], [15, 6, 18, 0, "_NativeDOM"], [15, 16, 18, 0], [15, 19, 18, 0, "_interopRequireDefault"], [15, 41, 18, 0], [15, 42, 18, 0, "require"], [15, 49, 18, 0], [15, 50, 18, 0, "_dependencyMap"], [15, 64, 18, 0], [16, 2, 18, 42], [16, 11, 18, 42, "_callSuper"], [16, 22, 18, 42, "t"], [16, 23, 18, 42], [16, 25, 18, 42, "o"], [16, 26, 18, 42], [16, 28, 18, 42, "e"], [16, 29, 18, 42], [16, 40, 18, 42, "o"], [16, 41, 18, 42], [16, 48, 18, 42, "_getPrototypeOf2"], [16, 64, 18, 42], [16, 65, 18, 42, "default"], [16, 72, 18, 42], [16, 74, 18, 42, "o"], [16, 75, 18, 42], [16, 82, 18, 42, "_possibleConstructorReturn2"], [16, 109, 18, 42], [16, 110, 18, 42, "default"], [16, 117, 18, 42], [16, 119, 18, 42, "t"], [16, 120, 18, 42], [16, 122, 18, 42, "_isNativeReflectConstruct"], [16, 147, 18, 42], [16, 152, 18, 42, "Reflect"], [16, 159, 18, 42], [16, 160, 18, 42, "construct"], [16, 169, 18, 42], [16, 170, 18, 42, "o"], [16, 171, 18, 42], [16, 173, 18, 42, "e"], [16, 174, 18, 42], [16, 186, 18, 42, "_getPrototypeOf2"], [16, 202, 18, 42], [16, 203, 18, 42, "default"], [16, 210, 18, 42], [16, 212, 18, 42, "t"], [16, 213, 18, 42], [16, 215, 18, 42, "constructor"], [16, 226, 18, 42], [16, 230, 18, 42, "o"], [16, 231, 18, 42], [16, 232, 18, 42, "apply"], [16, 237, 18, 42], [16, 238, 18, 42, "t"], [16, 239, 18, 42], [16, 241, 18, 42, "e"], [16, 242, 18, 42], [17, 2, 18, 42], [17, 11, 18, 42, "_isNativeReflectConstruct"], [17, 37, 18, 42], [17, 51, 18, 42, "t"], [17, 52, 18, 42], [17, 56, 18, 42, "Boolean"], [17, 63, 18, 42], [17, 64, 18, 42, "prototype"], [17, 73, 18, 42], [17, 74, 18, 42, "valueOf"], [17, 81, 18, 42], [17, 82, 18, 42, "call"], [17, 86, 18, 42], [17, 87, 18, 42, "Reflect"], [17, 94, 18, 42], [17, 95, 18, 42, "construct"], [17, 104, 18, 42], [17, 105, 18, 42, "Boolean"], [17, 112, 18, 42], [17, 145, 18, 42, "t"], [17, 146, 18, 42], [17, 159, 18, 42, "_isNativeReflectConstruct"], [17, 184, 18, 42], [17, 196, 18, 42, "_isNativeReflectConstruct"], [17, 197, 18, 42], [17, 210, 18, 42, "t"], [17, 211, 18, 42], [18, 2, 18, 42], [18, 6, 20, 21, "ReadOnlyCharacterData"], [18, 27, 20, 42], [18, 30, 20, 42, "exports"], [18, 37, 20, 42], [18, 38, 20, 42, "default"], [18, 45, 20, 42], [18, 71, 20, 42, "_ReadOnlyNode"], [18, 84, 20, 42], [19, 4, 20, 42], [19, 13, 20, 42, "ReadOnlyCharacterData"], [19, 35, 20, 42], [20, 6, 20, 42], [20, 10, 20, 42, "_classCallCheck2"], [20, 26, 20, 42], [20, 27, 20, 42, "default"], [20, 34, 20, 42], [20, 42, 20, 42, "ReadOnlyCharacterData"], [20, 63, 20, 42], [21, 6, 20, 42], [21, 13, 20, 42, "_callSuper"], [21, 23, 20, 42], [21, 30, 20, 42, "ReadOnlyCharacterData"], [21, 51, 20, 42], [21, 53, 20, 42, "arguments"], [21, 62, 20, 42], [22, 4, 20, 42], [23, 4, 20, 42], [23, 8, 20, 42, "_inherits2"], [23, 18, 20, 42], [23, 19, 20, 42, "default"], [23, 26, 20, 42], [23, 28, 20, 42, "ReadOnlyCharacterData"], [23, 49, 20, 42], [23, 51, 20, 42, "_ReadOnlyNode"], [23, 64, 20, 42], [24, 4, 20, 42], [24, 15, 20, 42, "_createClass2"], [24, 28, 20, 42], [24, 29, 20, 42, "default"], [24, 36, 20, 42], [24, 38, 20, 42, "ReadOnlyCharacterData"], [24, 59, 20, 42], [25, 6, 20, 42, "key"], [25, 9, 20, 42], [26, 6, 20, 42, "get"], [26, 9, 20, 42], [26, 11, 21, 2], [26, 20, 21, 2, "get"], [26, 21, 21, 2], [26, 23, 21, 51], [27, 8, 22, 4], [27, 15, 22, 11], [27, 19, 22, 11, "getElementSibling"], [27, 47, 22, 28], [27, 49, 22, 29], [27, 53, 22, 33], [27, 55, 22, 35], [27, 61, 22, 41], [27, 62, 22, 42], [28, 6, 23, 2], [29, 4, 23, 3], [30, 6, 23, 3, "key"], [30, 9, 23, 3], [31, 6, 23, 3, "get"], [31, 9, 23, 3], [31, 11, 25, 2], [31, 20, 25, 2, "get"], [31, 21, 25, 2], [31, 23, 25, 55], [32, 8, 26, 4], [32, 15, 26, 11], [32, 19, 26, 11, "getElementSibling"], [32, 47, 26, 28], [32, 49, 26, 29], [32, 53, 26, 33], [32, 55, 26, 35], [32, 65, 26, 45], [32, 66, 26, 46], [33, 6, 27, 2], [34, 4, 27, 3], [35, 6, 27, 3, "key"], [35, 9, 27, 3], [36, 6, 27, 3, "get"], [36, 9, 27, 3], [36, 11, 29, 2], [36, 20, 29, 2, "get"], [36, 21, 29, 2], [36, 23, 29, 21], [37, 8, 30, 4], [37, 12, 30, 10, "node"], [37, 16, 30, 14], [37, 19, 30, 17], [37, 23, 30, 17, "getNativeTextReference"], [37, 60, 30, 39], [37, 62, 30, 40], [37, 66, 30, 44], [37, 67, 30, 45], [38, 8, 32, 4], [38, 12, 32, 8, "node"], [38, 16, 32, 12], [38, 20, 32, 16], [38, 24, 32, 20], [38, 26, 32, 22], [39, 10, 33, 6], [39, 17, 33, 13, "NativeDOM"], [39, 35, 33, 22], [39, 36, 33, 23, "getTextContent"], [39, 50, 33, 37], [39, 51, 33, 38, "node"], [39, 55, 33, 42], [39, 56, 33, 43], [40, 8, 34, 4], [41, 8, 36, 4], [41, 15, 36, 11], [41, 17, 36, 13], [42, 6, 37, 2], [43, 4, 37, 3], [44, 6, 37, 3, "key"], [44, 9, 37, 3], [45, 6, 37, 3, "get"], [45, 9, 37, 3], [45, 11, 39, 2], [45, 20, 39, 2, "get"], [45, 21, 39, 2], [45, 23, 39, 23], [46, 8, 40, 4], [46, 15, 40, 11], [46, 19, 40, 15], [46, 20, 40, 16, "data"], [46, 24, 40, 20], [46, 25, 40, 21, "length"], [46, 31, 40, 27], [47, 6, 41, 2], [48, 4, 41, 3], [49, 6, 41, 3, "key"], [49, 9, 41, 3], [50, 6, 41, 3, "get"], [50, 9, 41, 3], [50, 11, 46, 2], [50, 20, 46, 2, "get"], [50, 21, 46, 2], [50, 23, 46, 28], [51, 8, 47, 4], [51, 15, 47, 11], [51, 19, 47, 15], [51, 20, 47, 16, "data"], [51, 24, 47, 20], [52, 6, 48, 2], [53, 4, 48, 3], [54, 6, 48, 3, "key"], [54, 9, 48, 3], [55, 6, 48, 3, "get"], [55, 9, 48, 3], [55, 11, 53, 2], [55, 20, 53, 2, "get"], [55, 21, 53, 2], [55, 23, 53, 26], [56, 8, 54, 4], [56, 15, 54, 11], [56, 19, 54, 15], [56, 20, 54, 16, "data"], [56, 24, 54, 20], [57, 6, 55, 2], [58, 4, 55, 3], [59, 6, 55, 3, "key"], [59, 9, 55, 3], [60, 6, 55, 3, "value"], [60, 11, 55, 3], [60, 13, 57, 2], [60, 22, 57, 2, "substringData"], [60, 35, 57, 15, "substringData"], [60, 36, 57, 16, "offset"], [60, 42, 57, 30], [60, 44, 57, 32, "count"], [60, 49, 57, 45], [60, 51, 57, 55], [61, 8, 58, 4], [61, 12, 58, 10, "data"], [61, 16, 58, 14], [61, 19, 58, 17], [61, 23, 58, 21], [61, 24, 58, 22, "data"], [61, 28, 58, 26], [62, 8, 59, 4], [62, 12, 59, 8, "offset"], [62, 18, 59, 14], [62, 21, 59, 17], [62, 22, 59, 18], [62, 24, 59, 20], [63, 10, 60, 6], [63, 16, 60, 12], [63, 20, 60, 16, "TypeError"], [63, 29, 60, 25], [63, 30, 61, 8], [63, 98, 61, 76, "offset"], [63, 104, 61, 82], [63, 119, 62, 6], [63, 120, 62, 7], [64, 8, 63, 4], [65, 8, 64, 4], [65, 12, 64, 8, "offset"], [65, 18, 64, 14], [65, 21, 64, 17, "data"], [65, 25, 64, 21], [65, 26, 64, 22, "length"], [65, 32, 64, 28], [65, 34, 64, 30], [66, 10, 65, 6], [66, 16, 65, 12], [66, 20, 65, 16, "TypeError"], [66, 29, 65, 25], [66, 30, 66, 8], [66, 98, 66, 76, "offset"], [66, 104, 66, 82], [66, 143, 66, 121, "data"], [66, 147, 66, 125], [66, 148, 66, 126, "length"], [66, 154, 66, 132], [66, 158, 67, 6], [66, 159, 67, 7], [67, 8, 68, 4], [68, 8, 69, 4], [68, 12, 69, 8, "adjustedCount"], [68, 25, 69, 21], [68, 28, 69, 24, "count"], [68, 33, 69, 29], [68, 36, 69, 32], [68, 37, 69, 33], [68, 41, 69, 37, "count"], [68, 46, 69, 42], [68, 49, 69, 45, "data"], [68, 53, 69, 49], [68, 54, 69, 50, "length"], [68, 60, 69, 56], [68, 63, 69, 59, "data"], [68, 67, 69, 63], [68, 68, 69, 64, "length"], [68, 74, 69, 70], [68, 77, 69, 73, "count"], [68, 82, 69, 78], [69, 8, 70, 4], [69, 15, 70, 11, "data"], [69, 19, 70, 15], [69, 20, 70, 16, "slice"], [69, 25, 70, 21], [69, 26, 70, 22, "offset"], [69, 32, 70, 28], [69, 34, 70, 30, "offset"], [69, 40, 70, 36], [69, 43, 70, 39, "adjustedCount"], [69, 56, 70, 52], [69, 57, 70, 53], [70, 6, 71, 2], [71, 4, 71, 3], [72, 2, 71, 3], [72, 4, 20, 51, "ReadOnlyNode"], [72, 26, 20, 63], [73, 0, 20, 63], [73, 3]], "functionMap": {"names": ["<global>", "ReadOnlyCharacterData", "get__nextElementSibling", "get__previousElementSibling", "get__data", "get__length", "get__textContent", "get__nodeValue", "substringData"], "mappings": "AAA;eCmB;ECC;GDE;EEE;GFE;EGE;GHQ;EIE;GJE;EKK;GLE;EMK;GNE;EOE;GPc"}}, "type": "js/module"}]}