{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 73, "index": 120}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useFocusedListenersChildrenAdapter = useFocusedListenersChildrenAdapter;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _NavigationBuilderContext = require(_dependencyMap[1], \"./NavigationBuilderContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Hook for passing focus callback to children\n   */\n  function useFocusedListenersChildrenAdapter(_ref) {\n    var navigation = _ref.navigation,\n      focusedListeners = _ref.focusedListeners;\n    var _React$useContext = React.useContext(_NavigationBuilderContext.NavigationBuilderContext),\n      addListener = _React$useContext.addListener;\n    var listener = React.useCallback(callback => {\n      if (navigation.isFocused()) {\n        for (var _listener of focusedListeners) {\n          var _listener2 = _listener(callback),\n            handled = _listener2.handled,\n            result = _listener2.result;\n          if (handled) {\n            return {\n              handled,\n              result\n            };\n          }\n        }\n        return {\n          handled: true,\n          result: callback(navigation)\n        };\n      } else {\n        return {\n          handled: false,\n          result: null\n        };\n      }\n    }, [focusedListeners, navigation]);\n    React.useEffect(() => addListener?.('focus', listener), [addListener, listener]);\n  }\n});", "lineCount": 45, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useFocusedListenersChildrenAdapter"], [7, 44, 1, 13], [7, 47, 1, 13, "useFocusedListenersChildrenAdapter"], [7, 81, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_NavigationBuilderContext"], [9, 31, 4, 0], [9, 34, 4, 0, "require"], [9, 41, 4, 0], [9, 42, 4, 0, "_dependencyMap"], [9, 56, 4, 0], [10, 2, 4, 73], [10, 11, 4, 73, "_interopRequireWildcard"], [10, 35, 4, 73, "e"], [10, 36, 4, 73], [10, 38, 4, 73, "t"], [10, 39, 4, 73], [10, 68, 4, 73, "WeakMap"], [10, 75, 4, 73], [10, 81, 4, 73, "r"], [10, 82, 4, 73], [10, 89, 4, 73, "WeakMap"], [10, 96, 4, 73], [10, 100, 4, 73, "n"], [10, 101, 4, 73], [10, 108, 4, 73, "WeakMap"], [10, 115, 4, 73], [10, 127, 4, 73, "_interopRequireWildcard"], [10, 150, 4, 73], [10, 162, 4, 73, "_interopRequireWildcard"], [10, 163, 4, 73, "e"], [10, 164, 4, 73], [10, 166, 4, 73, "t"], [10, 167, 4, 73], [10, 176, 4, 73, "t"], [10, 177, 4, 73], [10, 181, 4, 73, "e"], [10, 182, 4, 73], [10, 186, 4, 73, "e"], [10, 187, 4, 73], [10, 188, 4, 73, "__esModule"], [10, 198, 4, 73], [10, 207, 4, 73, "e"], [10, 208, 4, 73], [10, 214, 4, 73, "o"], [10, 215, 4, 73], [10, 217, 4, 73, "i"], [10, 218, 4, 73], [10, 220, 4, 73, "f"], [10, 221, 4, 73], [10, 226, 4, 73, "__proto__"], [10, 235, 4, 73], [10, 243, 4, 73, "default"], [10, 250, 4, 73], [10, 252, 4, 73, "e"], [10, 253, 4, 73], [10, 270, 4, 73, "e"], [10, 271, 4, 73], [10, 294, 4, 73, "e"], [10, 295, 4, 73], [10, 320, 4, 73, "e"], [10, 321, 4, 73], [10, 330, 4, 73, "f"], [10, 331, 4, 73], [10, 337, 4, 73, "o"], [10, 338, 4, 73], [10, 341, 4, 73, "t"], [10, 342, 4, 73], [10, 345, 4, 73, "n"], [10, 346, 4, 73], [10, 349, 4, 73, "r"], [10, 350, 4, 73], [10, 358, 4, 73, "o"], [10, 359, 4, 73], [10, 360, 4, 73, "has"], [10, 363, 4, 73], [10, 364, 4, 73, "e"], [10, 365, 4, 73], [10, 375, 4, 73, "o"], [10, 376, 4, 73], [10, 377, 4, 73, "get"], [10, 380, 4, 73], [10, 381, 4, 73, "e"], [10, 382, 4, 73], [10, 385, 4, 73, "o"], [10, 386, 4, 73], [10, 387, 4, 73, "set"], [10, 390, 4, 73], [10, 391, 4, 73, "e"], [10, 392, 4, 73], [10, 394, 4, 73, "f"], [10, 395, 4, 73], [10, 409, 4, 73, "_t"], [10, 411, 4, 73], [10, 415, 4, 73, "e"], [10, 416, 4, 73], [10, 432, 4, 73, "_t"], [10, 434, 4, 73], [10, 441, 4, 73, "hasOwnProperty"], [10, 455, 4, 73], [10, 456, 4, 73, "call"], [10, 460, 4, 73], [10, 461, 4, 73, "e"], [10, 462, 4, 73], [10, 464, 4, 73, "_t"], [10, 466, 4, 73], [10, 473, 4, 73, "i"], [10, 474, 4, 73], [10, 478, 4, 73, "o"], [10, 479, 4, 73], [10, 482, 4, 73, "Object"], [10, 488, 4, 73], [10, 489, 4, 73, "defineProperty"], [10, 503, 4, 73], [10, 508, 4, 73, "Object"], [10, 514, 4, 73], [10, 515, 4, 73, "getOwnPropertyDescriptor"], [10, 539, 4, 73], [10, 540, 4, 73, "e"], [10, 541, 4, 73], [10, 543, 4, 73, "_t"], [10, 545, 4, 73], [10, 552, 4, 73, "i"], [10, 553, 4, 73], [10, 554, 4, 73, "get"], [10, 557, 4, 73], [10, 561, 4, 73, "i"], [10, 562, 4, 73], [10, 563, 4, 73, "set"], [10, 566, 4, 73], [10, 570, 4, 73, "o"], [10, 571, 4, 73], [10, 572, 4, 73, "f"], [10, 573, 4, 73], [10, 575, 4, 73, "_t"], [10, 577, 4, 73], [10, 579, 4, 73, "i"], [10, 580, 4, 73], [10, 584, 4, 73, "f"], [10, 585, 4, 73], [10, 586, 4, 73, "_t"], [10, 588, 4, 73], [10, 592, 4, 73, "e"], [10, 593, 4, 73], [10, 594, 4, 73, "_t"], [10, 596, 4, 73], [10, 607, 4, 73, "f"], [10, 608, 4, 73], [10, 613, 4, 73, "e"], [10, 614, 4, 73], [10, 616, 4, 73, "t"], [10, 617, 4, 73], [11, 2, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [14, 2, 8, 7], [14, 11, 8, 16, "useFocusedListenersChildrenAdapter"], [14, 45, 8, 50, "useFocusedListenersChildrenAdapter"], [14, 46, 8, 50, "_ref"], [14, 50, 8, 50], [14, 52, 11, 3], [15, 4, 11, 3], [15, 8, 9, 2, "navigation"], [15, 18, 9, 12], [15, 21, 9, 12, "_ref"], [15, 25, 9, 12], [15, 26, 9, 2, "navigation"], [15, 36, 9, 12], [16, 6, 10, 2, "focusedListeners"], [16, 22, 10, 18], [16, 25, 10, 18, "_ref"], [16, 29, 10, 18], [16, 30, 10, 2, "focusedListeners"], [16, 46, 10, 18], [17, 4, 12, 2], [17, 8, 12, 2, "_React$useContext"], [17, 25, 12, 2], [17, 28, 14, 6, "React"], [17, 33, 14, 11], [17, 34, 14, 12, "useContext"], [17, 44, 14, 22], [17, 45, 14, 23, "NavigationBuilderContext"], [17, 95, 14, 47], [17, 96, 14, 48], [18, 6, 13, 4, "addListener"], [18, 17, 13, 15], [18, 20, 13, 15, "_React$useContext"], [18, 37, 13, 15], [18, 38, 13, 4, "addListener"], [18, 49, 13, 15], [19, 4, 15, 2], [19, 8, 15, 8, "listener"], [19, 16, 15, 16], [19, 19, 15, 19, "React"], [19, 24, 15, 24], [19, 25, 15, 25, "useCallback"], [19, 36, 15, 36], [19, 37, 15, 37, "callback"], [19, 45, 15, 45], [19, 49, 15, 49], [20, 6, 16, 4], [20, 10, 16, 8, "navigation"], [20, 20, 16, 18], [20, 21, 16, 19, "isFocused"], [20, 30, 16, 28], [20, 31, 16, 29], [20, 32, 16, 30], [20, 34, 16, 32], [21, 8, 17, 6], [21, 13, 17, 11], [21, 17, 17, 17, "listener"], [21, 26, 17, 25], [21, 30, 17, 29, "focusedListeners"], [21, 46, 17, 45], [21, 48, 17, 47], [22, 10, 18, 8], [22, 14, 18, 8, "_listener2"], [22, 24, 18, 8], [22, 27, 21, 12, "listener"], [22, 36, 21, 20], [22, 37, 21, 21, "callback"], [22, 45, 21, 29], [22, 46, 21, 30], [23, 12, 19, 10, "handled"], [23, 19, 19, 17], [23, 22, 19, 17, "_listener2"], [23, 32, 19, 17], [23, 33, 19, 10, "handled"], [23, 40, 19, 17], [24, 12, 20, 10, "result"], [24, 18, 20, 16], [24, 21, 20, 16, "_listener2"], [24, 31, 20, 16], [24, 32, 20, 10, "result"], [24, 38, 20, 16], [25, 10, 22, 8], [25, 14, 22, 12, "handled"], [25, 21, 22, 19], [25, 23, 22, 21], [26, 12, 23, 10], [26, 19, 23, 17], [27, 14, 24, 12, "handled"], [27, 21, 24, 19], [28, 14, 25, 12, "result"], [29, 12, 26, 10], [29, 13, 26, 11], [30, 10, 27, 8], [31, 8, 28, 6], [32, 8, 29, 6], [32, 15, 29, 13], [33, 10, 30, 8, "handled"], [33, 17, 30, 15], [33, 19, 30, 17], [33, 23, 30, 21], [34, 10, 31, 8, "result"], [34, 16, 31, 14], [34, 18, 31, 16, "callback"], [34, 26, 31, 24], [34, 27, 31, 25, "navigation"], [34, 37, 31, 35], [35, 8, 32, 6], [35, 9, 32, 7], [36, 6, 33, 4], [36, 7, 33, 5], [36, 13, 33, 11], [37, 8, 34, 6], [37, 15, 34, 13], [38, 10, 35, 8, "handled"], [38, 17, 35, 15], [38, 19, 35, 17], [38, 24, 35, 22], [39, 10, 36, 8, "result"], [39, 16, 36, 14], [39, 18, 36, 16], [40, 8, 37, 6], [40, 9, 37, 7], [41, 6, 38, 4], [42, 4, 39, 2], [42, 5, 39, 3], [42, 7, 39, 5], [42, 8, 39, 6, "focusedListeners"], [42, 24, 39, 22], [42, 26, 39, 24, "navigation"], [42, 36, 39, 34], [42, 37, 39, 35], [42, 38, 39, 36], [43, 4, 40, 2, "React"], [43, 9, 40, 7], [43, 10, 40, 8, "useEffect"], [43, 19, 40, 17], [43, 20, 40, 18], [43, 26, 40, 24, "addListener"], [43, 37, 40, 35], [43, 40, 40, 38], [43, 47, 40, 45], [43, 49, 40, 47, "listener"], [43, 57, 40, 55], [43, 58, 40, 56], [43, 60, 40, 58], [43, 61, 40, 59, "addListener"], [43, 72, 40, 70], [43, 74, 40, 72, "listener"], [43, 82, 40, 80], [43, 83, 40, 81], [43, 84, 40, 82], [44, 2, 41, 0], [45, 0, 41, 1], [45, 3]], "functionMap": {"names": ["<global>", "useFocusedListenersChildrenAdapter", "listener", "React.useEffect$argument_0"], "mappings": "AAA;OCO;qCCO;GDwB;kBEC,sCF;CDC"}}, "type": "js/module"}]}