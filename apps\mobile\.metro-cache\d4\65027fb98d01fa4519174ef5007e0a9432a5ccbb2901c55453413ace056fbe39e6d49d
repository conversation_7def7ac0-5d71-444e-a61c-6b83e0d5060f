{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "../Blob/Blob", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 32}}], "key": "QJ9YUgVVo3YV103ML0FFHJuPYrA=", "exportNames": ["*"]}}, {"name": "../Blob/BlobManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 46}}], "key": "FtXaCM40L4ZvhVGzYeIEcCA7J8Y=", "exportNames": ["*"]}}, {"name": "../EventEmitter/NativeEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 68}}], "key": "Jk6GODPsD+OS/XTex7hK2MSfvW0=", "exportNames": ["*"]}}, {"name": "../Utilities/binaryToBase64", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 57}}], "key": "lSiEKNqE2DL2f1OV5nuMLekqR6M=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 45}}], "key": "WyqnBhspP5BAR0xvCwqfBv/v4uA=", "exportNames": ["*"]}}, {"name": "./NativeWebSocketModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 60}}], "key": "AGB2JKXOMxtGwGWQCZlW2uis6T8=", "exportNames": ["*"]}}, {"name": "./WebSocketEvent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 46}}], "key": "lLlIjRYaYNWXHJXjgQ+dMvkR6lQ=", "exportNames": ["*"]}}, {"name": "base64-js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 31}}], "key": "9arPc0KuVPvzcEfvnWXidnN1Ujk=", "exportNames": ["*"]}}, {"name": "event-target-shim", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 44}}], "key": "NbjKHRYGUQGwCXA5fondJGZijfU=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _Blob = _interopRequireDefault(require(_dependencyMap[7], \"../Blob/Blob\"));\n  var _BlobManager = _interopRequireDefault(require(_dependencyMap[8], \"../Blob/BlobManager\"));\n  var _NativeEventEmitter = _interopRequireDefault(require(_dependencyMap[9], \"../EventEmitter/NativeEventEmitter\"));\n  var _binaryToBase = _interopRequireDefault(require(_dependencyMap[10], \"../Utilities/binaryToBase64\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[11], \"../Utilities/Platform\"));\n  var _NativeWebSocketModule = _interopRequireDefault(require(_dependencyMap[12], \"./NativeWebSocketModule\"));\n  var _WebSocketEvent = _interopRequireDefault(require(_dependencyMap[13], \"./WebSocketEvent\"));\n  var _base64Js = _interopRequireDefault(require(_dependencyMap[14], \"base64-js\"));\n  var _eventTargetShim = _interopRequireDefault(require(_dependencyMap[15], \"event-target-shim\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[16], \"invariant\"));\n  var _excluded = [\"headers\"];\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var CONNECTING = 0;\n  var OPEN = 1;\n  var CLOSING = 2;\n  var CLOSED = 3;\n  var CLOSE_NORMAL = 1000;\n  var CLOSE_ABNORMAL = 1006;\n  var WEBSOCKET_EVENTS = ['close', 'error', 'message', 'open'];\n  var nextWebSocketId = 0;\n  var WebSocket = /*#__PURE__*/function (_ref) {\n    function WebSocket(url, protocols, options) {\n      var _this;\n      (0, _classCallCheck2.default)(this, WebSocket);\n      _this = _callSuper(this, WebSocket);\n      _this.CONNECTING = CONNECTING;\n      _this.OPEN = OPEN;\n      _this.CLOSING = CLOSING;\n      _this.CLOSED = CLOSED;\n      _this.readyState = CONNECTING;\n      _this.url = url;\n      if (typeof protocols === 'string') {\n        protocols = [protocols];\n      }\n      var _ref2 = options || {},\n        _ref2$headers = _ref2.headers,\n        headers = _ref2$headers === void 0 ? {} : _ref2$headers,\n        unrecognized = (0, _objectWithoutProperties2.default)(_ref2, _excluded);\n      if (unrecognized && typeof unrecognized.origin === 'string') {\n        console.warn('Specifying `origin` as a WebSocket connection option is deprecated. Include it under `headers` instead.');\n        headers.origin = unrecognized.origin;\n        delete unrecognized.origin;\n      }\n      if (Object.keys(unrecognized).length > 0) {\n        console.warn('Unrecognized WebSocket connection option(s) `' + Object.keys(unrecognized).join('`, `') + '`. ' + 'Did you mean to put these under `headers`?');\n      }\n      if (!Array.isArray(protocols)) {\n        protocols = null;\n      }\n      _this._eventEmitter = new _NativeEventEmitter.default(_Platform.default.OS !== 'ios' ? null : _NativeWebSocketModule.default);\n      _this._socketId = nextWebSocketId++;\n      _this._registerEvents();\n      _NativeWebSocketModule.default.connect(url, protocols, {\n        headers\n      }, _this._socketId);\n      return _this;\n    }\n    (0, _inherits2.default)(WebSocket, _ref);\n    return (0, _createClass2.default)(WebSocket, [{\n      key: \"binaryType\",\n      get: function () {\n        return this._binaryType;\n      },\n      set: function (binaryType) {\n        if (binaryType !== 'blob' && binaryType !== 'arraybuffer') {\n          throw new Error(\"binaryType must be either 'blob' or 'arraybuffer'\");\n        }\n        if (this._binaryType === 'blob' || binaryType === 'blob') {\n          (0, _invariant.default)(_BlobManager.default.isAvailable, 'Native module BlobModule is required for blob support');\n          if (binaryType === 'blob') {\n            _BlobManager.default.addWebSocketHandler(this._socketId);\n          } else {\n            _BlobManager.default.removeWebSocketHandler(this._socketId);\n          }\n        }\n        this._binaryType = binaryType;\n      }\n    }, {\n      key: \"close\",\n      value: function close(code, reason) {\n        if (this.readyState === this.CLOSING || this.readyState === this.CLOSED) {\n          return;\n        }\n        this.readyState = this.CLOSING;\n        this._close(code, reason);\n      }\n    }, {\n      key: \"send\",\n      value: function send(data) {\n        if (this.readyState === this.CONNECTING) {\n          throw new Error('INVALID_STATE_ERR');\n        }\n        if (data instanceof _Blob.default) {\n          (0, _invariant.default)(_BlobManager.default.isAvailable, 'Native module BlobModule is required for blob support');\n          _BlobManager.default.sendOverSocket(data, this._socketId);\n          return;\n        }\n        if (typeof data === 'string') {\n          _NativeWebSocketModule.default.send(data, this._socketId);\n          return;\n        }\n        if (data instanceof ArrayBuffer || ArrayBuffer.isView(data)) {\n          _NativeWebSocketModule.default.sendBinary((0, _binaryToBase.default)(data), this._socketId);\n          return;\n        }\n        throw new Error('Unsupported data type');\n      }\n    }, {\n      key: \"ping\",\n      value: function ping() {\n        if (this.readyState === this.CONNECTING) {\n          throw new Error('INVALID_STATE_ERR');\n        }\n        _NativeWebSocketModule.default.ping(this._socketId);\n      }\n    }, {\n      key: \"_close\",\n      value: function _close(code, reason) {\n        var statusCode = typeof code === 'number' ? code : CLOSE_NORMAL;\n        var closeReason = typeof reason === 'string' ? reason : '';\n        _NativeWebSocketModule.default.close(statusCode, closeReason, this._socketId);\n        if (_BlobManager.default.isAvailable && this._binaryType === 'blob') {\n          _BlobManager.default.removeWebSocketHandler(this._socketId);\n        }\n      }\n    }, {\n      key: \"_unregisterEvents\",\n      value: function _unregisterEvents() {\n        this._subscriptions.forEach(e => e.remove());\n        this._subscriptions = [];\n      }\n    }, {\n      key: \"_registerEvents\",\n      value: function _registerEvents() {\n        this._subscriptions = [this._eventEmitter.addListener('websocketMessage', ev => {\n          if (ev.id !== this._socketId) {\n            return;\n          }\n          var data = ev.data;\n          switch (ev.type) {\n            case 'binary':\n              data = _base64Js.default.toByteArray(ev.data).buffer;\n              break;\n            case 'blob':\n              data = _BlobManager.default.createFromOptions(ev.data);\n              break;\n          }\n          this.dispatchEvent(new _WebSocketEvent.default('message', {\n            data\n          }));\n        }), this._eventEmitter.addListener('websocketOpen', ev => {\n          if (ev.id !== this._socketId) {\n            return;\n          }\n          this.readyState = this.OPEN;\n          this.protocol = ev.protocol;\n          this.dispatchEvent(new _WebSocketEvent.default('open'));\n        }), this._eventEmitter.addListener('websocketClosed', ev => {\n          if (ev.id !== this._socketId) {\n            return;\n          }\n          this.readyState = this.CLOSED;\n          this.dispatchEvent(new _WebSocketEvent.default('close', {\n            code: ev.code,\n            reason: ev.reason\n          }));\n          this._unregisterEvents();\n          this.close();\n        }), this._eventEmitter.addListener('websocketFailed', ev => {\n          if (ev.id !== this._socketId) {\n            return;\n          }\n          this.readyState = this.CLOSED;\n          this.dispatchEvent(new _WebSocketEvent.default('error', {\n            message: ev.message\n          }));\n          this.dispatchEvent(new _WebSocketEvent.default('close', {\n            code: CLOSE_ABNORMAL,\n            reason: ev.message\n          }));\n          this._unregisterEvents();\n          this.close();\n        })];\n      }\n    }]);\n  }((0, _eventTargetShim.default)(...WEBSOCKET_EVENTS));\n  WebSocket.CONNECTING = CONNECTING;\n  WebSocket.OPEN = OPEN;\n  WebSocket.CLOSING = CLOSING;\n  WebSocket.CLOSED = CLOSED;\n  var _default = exports.default = WebSocket;\n});", "lineCount": 205, "map": [[13, 2, 14, 0], [13, 6, 14, 0, "_Blob"], [13, 11, 14, 0], [13, 14, 14, 0, "_interopRequireDefault"], [13, 36, 14, 0], [13, 37, 14, 0, "require"], [13, 44, 14, 0], [13, 45, 14, 0, "_dependencyMap"], [13, 59, 14, 0], [14, 2, 15, 0], [14, 6, 15, 0, "_BlobManager"], [14, 18, 15, 0], [14, 21, 15, 0, "_interopRequireDefault"], [14, 43, 15, 0], [14, 44, 15, 0, "require"], [14, 51, 15, 0], [14, 52, 15, 0, "_dependencyMap"], [14, 66, 15, 0], [15, 2, 16, 0], [15, 6, 16, 0, "_NativeEventEmitter"], [15, 25, 16, 0], [15, 28, 16, 0, "_interopRequireDefault"], [15, 50, 16, 0], [15, 51, 16, 0, "require"], [15, 58, 16, 0], [15, 59, 16, 0, "_dependencyMap"], [15, 73, 16, 0], [16, 2, 17, 0], [16, 6, 17, 0, "_binaryToBase"], [16, 19, 17, 0], [16, 22, 17, 0, "_interopRequireDefault"], [16, 44, 17, 0], [16, 45, 17, 0, "require"], [16, 52, 17, 0], [16, 53, 17, 0, "_dependencyMap"], [16, 67, 17, 0], [17, 2, 18, 0], [17, 6, 18, 0, "_Platform"], [17, 15, 18, 0], [17, 18, 18, 0, "_interopRequireDefault"], [17, 40, 18, 0], [17, 41, 18, 0, "require"], [17, 48, 18, 0], [17, 49, 18, 0, "_dependencyMap"], [17, 63, 18, 0], [18, 2, 19, 0], [18, 6, 19, 0, "_NativeWebSocketModule"], [18, 28, 19, 0], [18, 31, 19, 0, "_interopRequireDefault"], [18, 53, 19, 0], [18, 54, 19, 0, "require"], [18, 61, 19, 0], [18, 62, 19, 0, "_dependencyMap"], [18, 76, 19, 0], [19, 2, 20, 0], [19, 6, 20, 0, "_WebSocketEvent"], [19, 21, 20, 0], [19, 24, 20, 0, "_interopRequireDefault"], [19, 46, 20, 0], [19, 47, 20, 0, "require"], [19, 54, 20, 0], [19, 55, 20, 0, "_dependencyMap"], [19, 69, 20, 0], [20, 2, 21, 0], [20, 6, 21, 0, "_base64Js"], [20, 15, 21, 0], [20, 18, 21, 0, "_interopRequireDefault"], [20, 40, 21, 0], [20, 41, 21, 0, "require"], [20, 48, 21, 0], [20, 49, 21, 0, "_dependencyMap"], [20, 63, 21, 0], [21, 2, 22, 0], [21, 6, 22, 0, "_eventTar<PERSON><PERSON><PERSON>"], [21, 22, 22, 0], [21, 25, 22, 0, "_interopRequireDefault"], [21, 47, 22, 0], [21, 48, 22, 0, "require"], [21, 55, 22, 0], [21, 56, 22, 0, "_dependencyMap"], [21, 70, 22, 0], [22, 2, 23, 0], [22, 6, 23, 0, "_invariant"], [22, 16, 23, 0], [22, 19, 23, 0, "_interopRequireDefault"], [22, 41, 23, 0], [22, 42, 23, 0, "require"], [22, 49, 23, 0], [22, 50, 23, 0, "_dependencyMap"], [22, 64, 23, 0], [23, 2, 23, 34], [23, 6, 23, 34, "_excluded"], [23, 15, 23, 34], [24, 2, 23, 34], [24, 11, 23, 34, "_callSuper"], [24, 22, 23, 34, "t"], [24, 23, 23, 34], [24, 25, 23, 34, "o"], [24, 26, 23, 34], [24, 28, 23, 34, "e"], [24, 29, 23, 34], [24, 40, 23, 34, "o"], [24, 41, 23, 34], [24, 48, 23, 34, "_getPrototypeOf2"], [24, 64, 23, 34], [24, 65, 23, 34, "default"], [24, 72, 23, 34], [24, 74, 23, 34, "o"], [24, 75, 23, 34], [24, 82, 23, 34, "_possibleConstructorReturn2"], [24, 109, 23, 34], [24, 110, 23, 34, "default"], [24, 117, 23, 34], [24, 119, 23, 34, "t"], [24, 120, 23, 34], [24, 122, 23, 34, "_isNativeReflectConstruct"], [24, 147, 23, 34], [24, 152, 23, 34, "Reflect"], [24, 159, 23, 34], [24, 160, 23, 34, "construct"], [24, 169, 23, 34], [24, 170, 23, 34, "o"], [24, 171, 23, 34], [24, 173, 23, 34, "e"], [24, 174, 23, 34], [24, 186, 23, 34, "_getPrototypeOf2"], [24, 202, 23, 34], [24, 203, 23, 34, "default"], [24, 210, 23, 34], [24, 212, 23, 34, "t"], [24, 213, 23, 34], [24, 215, 23, 34, "constructor"], [24, 226, 23, 34], [24, 230, 23, 34, "o"], [24, 231, 23, 34], [24, 232, 23, 34, "apply"], [24, 237, 23, 34], [24, 238, 23, 34, "t"], [24, 239, 23, 34], [24, 241, 23, 34, "e"], [24, 242, 23, 34], [25, 2, 23, 34], [25, 11, 23, 34, "_isNativeReflectConstruct"], [25, 37, 23, 34], [25, 51, 23, 34, "t"], [25, 52, 23, 34], [25, 56, 23, 34, "Boolean"], [25, 63, 23, 34], [25, 64, 23, 34, "prototype"], [25, 73, 23, 34], [25, 74, 23, 34, "valueOf"], [25, 81, 23, 34], [25, 82, 23, 34, "call"], [25, 86, 23, 34], [25, 87, 23, 34, "Reflect"], [25, 94, 23, 34], [25, 95, 23, 34, "construct"], [25, 104, 23, 34], [25, 105, 23, 34, "Boolean"], [25, 112, 23, 34], [25, 145, 23, 34, "t"], [25, 146, 23, 34], [25, 159, 23, 34, "_isNativeReflectConstruct"], [25, 184, 23, 34], [25, 196, 23, 34, "_isNativeReflectConstruct"], [25, 197, 23, 34], [25, 210, 23, 34, "t"], [25, 211, 23, 34], [26, 2, 39, 0], [26, 6, 39, 6, "CONNECTING"], [26, 16, 39, 16], [26, 19, 39, 19], [26, 20, 39, 20], [27, 2, 40, 0], [27, 6, 40, 6, "OPEN"], [27, 10, 40, 10], [27, 13, 40, 13], [27, 14, 40, 14], [28, 2, 41, 0], [28, 6, 41, 6, "CLOSING"], [28, 13, 41, 13], [28, 16, 41, 16], [28, 17, 41, 17], [29, 2, 42, 0], [29, 6, 42, 6, "CLOSED"], [29, 12, 42, 12], [29, 15, 42, 15], [29, 16, 42, 16], [30, 2, 44, 0], [30, 6, 44, 6, "CLOSE_NORMAL"], [30, 18, 44, 18], [30, 21, 44, 21], [30, 25, 44, 25], [31, 2, 48, 0], [31, 6, 48, 6, "CLOSE_ABNORMAL"], [31, 20, 48, 20], [31, 23, 48, 23], [31, 27, 48, 27], [32, 2, 50, 0], [32, 6, 50, 6, "WEBSOCKET_EVENTS"], [32, 22, 50, 22], [32, 25, 50, 25], [32, 26, 50, 26], [32, 33, 50, 33], [32, 35, 50, 35], [32, 42, 50, 42], [32, 44, 50, 44], [32, 53, 50, 53], [32, 55, 50, 55], [32, 61, 50, 61], [32, 62, 50, 62], [33, 2, 52, 0], [33, 6, 52, 4, "nextWebSocketId"], [33, 21, 52, 19], [33, 24, 52, 22], [33, 25, 52, 23], [34, 2, 52, 24], [34, 6, 71, 6, "WebSocket"], [34, 15, 71, 15], [34, 41, 71, 15, "_ref"], [34, 45, 71, 15], [35, 4, 98, 2], [35, 13, 98, 2, "WebSocket"], [35, 23, 99, 4, "url"], [35, 26, 99, 15], [35, 28, 100, 4, "protocols"], [35, 37, 100, 39], [35, 39, 101, 4, "options"], [35, 46, 101, 53], [35, 48, 102, 4], [36, 6, 102, 4], [36, 10, 102, 4, "_this"], [36, 15, 102, 4], [37, 6, 102, 4], [37, 10, 102, 4, "_classCallCheck2"], [37, 26, 102, 4], [37, 27, 102, 4, "default"], [37, 34, 102, 4], [37, 42, 102, 4, "WebSocket"], [37, 51, 102, 4], [38, 6, 103, 4, "_this"], [38, 11, 103, 4], [38, 14, 103, 4, "_callSuper"], [38, 24, 103, 4], [38, 31, 103, 4, "WebSocket"], [38, 40, 103, 4], [39, 6, 103, 12, "_this"], [39, 11, 103, 12], [39, 12, 77, 2, "CONNECTING"], [39, 22, 77, 12], [39, 25, 77, 23, "CONNECTING"], [39, 35, 77, 33], [40, 6, 77, 33, "_this"], [40, 11, 77, 33], [40, 12, 78, 2, "OPEN"], [40, 16, 78, 6], [40, 19, 78, 17, "OPEN"], [40, 23, 78, 21], [41, 6, 78, 21, "_this"], [41, 11, 78, 21], [41, 12, 79, 2, "CLOSING"], [41, 19, 79, 9], [41, 22, 79, 20, "CLOSING"], [41, 29, 79, 27], [42, 6, 79, 27, "_this"], [42, 11, 79, 27], [42, 12, 80, 2, "CLOSED"], [42, 18, 80, 8], [42, 21, 80, 19, "CLOSED"], [42, 27, 80, 25], [43, 6, 80, 25, "_this"], [43, 11, 80, 25], [43, 12, 95, 2, "readyState"], [43, 22, 95, 12], [43, 25, 95, 23, "CONNECTING"], [43, 35, 95, 33], [44, 6, 104, 4, "_this"], [44, 11, 104, 4], [44, 12, 104, 9, "url"], [44, 15, 104, 12], [44, 18, 104, 15, "url"], [44, 21, 104, 18], [45, 6, 105, 4], [45, 10, 105, 8], [45, 17, 105, 15, "protocols"], [45, 26, 105, 24], [45, 31, 105, 29], [45, 39, 105, 37], [45, 41, 105, 39], [46, 8, 106, 6, "protocols"], [46, 17, 106, 15], [46, 20, 106, 18], [46, 21, 106, 19, "protocols"], [46, 30, 106, 28], [46, 31, 106, 29], [47, 6, 107, 4], [48, 6, 109, 4], [48, 10, 109, 4, "_ref2"], [48, 15, 109, 4], [48, 18, 109, 44, "options"], [48, 25, 109, 51], [48, 29, 109, 55], [48, 30, 109, 56], [48, 31, 109, 57], [49, 8, 109, 57, "_ref2$headers"], [49, 21, 109, 57], [49, 24, 109, 57, "_ref2"], [49, 29, 109, 57], [49, 30, 109, 11, "headers"], [49, 37, 109, 18], [50, 8, 109, 11, "headers"], [50, 15, 109, 18], [50, 18, 109, 18, "_ref2$headers"], [50, 31, 109, 18], [50, 45, 109, 21], [50, 46, 109, 22], [50, 47, 109, 23], [50, 50, 109, 23, "_ref2$headers"], [50, 63, 109, 23], [51, 8, 109, 28, "unrecognized"], [51, 20, 109, 40], [51, 27, 109, 40, "_objectWithoutProperties2"], [51, 52, 109, 40], [51, 53, 109, 40, "default"], [51, 60, 109, 40], [51, 62, 109, 40, "_ref2"], [51, 67, 109, 40], [51, 69, 109, 40, "_excluded"], [51, 78, 109, 40], [52, 6, 113, 4], [52, 10, 113, 8, "unrecognized"], [52, 22, 113, 20], [52, 26, 113, 24], [52, 33, 113, 31, "unrecognized"], [52, 45, 113, 43], [52, 46, 113, 44, "origin"], [52, 52, 113, 50], [52, 57, 113, 55], [52, 65, 113, 63], [52, 67, 113, 65], [53, 8, 114, 6, "console"], [53, 15, 114, 13], [53, 16, 114, 14, "warn"], [53, 20, 114, 18], [53, 21, 115, 8], [53, 126, 116, 6], [53, 127, 116, 7], [54, 8, 120, 6, "headers"], [54, 15, 120, 13], [54, 16, 120, 14, "origin"], [54, 22, 120, 20], [54, 25, 120, 23, "unrecognized"], [54, 37, 120, 35], [54, 38, 120, 36, "origin"], [54, 44, 120, 42], [55, 8, 124, 6], [55, 15, 124, 13, "unrecognized"], [55, 27, 124, 25], [55, 28, 124, 26, "origin"], [55, 34, 124, 32], [56, 6, 125, 4], [57, 6, 128, 4], [57, 10, 128, 8, "Object"], [57, 16, 128, 14], [57, 17, 128, 15, "keys"], [57, 21, 128, 19], [57, 22, 128, 20, "unrecognized"], [57, 34, 128, 32], [57, 35, 128, 33], [57, 36, 128, 34, "length"], [57, 42, 128, 40], [57, 45, 128, 43], [57, 46, 128, 44], [57, 48, 128, 46], [58, 8, 129, 6, "console"], [58, 15, 129, 13], [58, 16, 129, 14, "warn"], [58, 20, 129, 18], [58, 21, 130, 8], [58, 68, 130, 55], [58, 71, 131, 10, "Object"], [58, 77, 131, 16], [58, 78, 131, 17, "keys"], [58, 82, 131, 21], [58, 83, 131, 22, "unrecognized"], [58, 95, 131, 34], [58, 96, 131, 35], [58, 97, 131, 36, "join"], [58, 101, 131, 40], [58, 102, 131, 41], [58, 108, 131, 47], [58, 109, 131, 48], [58, 112, 132, 10], [58, 117, 132, 15], [58, 120, 133, 10], [58, 164, 134, 6], [58, 165, 134, 7], [59, 6, 135, 4], [60, 6, 137, 4], [60, 10, 137, 8], [60, 11, 137, 9, "Array"], [60, 16, 137, 14], [60, 17, 137, 15, "isArray"], [60, 24, 137, 22], [60, 25, 137, 23, "protocols"], [60, 34, 137, 32], [60, 35, 137, 33], [60, 37, 137, 35], [61, 8, 138, 6, "protocols"], [61, 17, 138, 15], [61, 20, 138, 18], [61, 24, 138, 22], [62, 6, 139, 4], [63, 6, 141, 4, "_this"], [63, 11, 141, 4], [63, 12, 141, 9, "_eventEmitter"], [63, 25, 141, 22], [63, 28, 141, 25], [63, 32, 141, 29, "NativeEventEmitter"], [63, 59, 141, 47], [63, 60, 144, 6, "Platform"], [63, 77, 144, 14], [63, 78, 144, 15, "OS"], [63, 80, 144, 17], [63, 85, 144, 22], [63, 90, 144, 27], [63, 93, 144, 30], [63, 97, 144, 34], [63, 100, 144, 37, "NativeWebSocketModule"], [63, 130, 145, 4], [63, 131, 145, 5], [64, 6, 146, 4, "_this"], [64, 11, 146, 4], [64, 12, 146, 9, "_socketId"], [64, 21, 146, 18], [64, 24, 146, 21, "nextWebSocketId"], [64, 39, 146, 36], [64, 41, 146, 38], [65, 6, 147, 4, "_this"], [65, 11, 147, 4], [65, 12, 147, 9, "_registerEvents"], [65, 27, 147, 24], [65, 28, 147, 25], [65, 29, 147, 26], [66, 6, 148, 4, "NativeWebSocketModule"], [66, 36, 148, 25], [66, 37, 148, 26, "connect"], [66, 44, 148, 33], [66, 45, 148, 34, "url"], [66, 48, 148, 37], [66, 50, 148, 39, "protocols"], [66, 59, 148, 48], [66, 61, 148, 50], [67, 8, 148, 51, "headers"], [68, 6, 148, 58], [68, 7, 148, 59], [68, 9, 148, 61, "_this"], [68, 14, 148, 61], [68, 15, 148, 66, "_socketId"], [68, 24, 148, 75], [68, 25, 148, 76], [69, 6, 148, 77], [69, 13, 148, 77, "_this"], [69, 18, 148, 77], [70, 4, 149, 2], [71, 4, 149, 3], [71, 8, 149, 3, "_inherits2"], [71, 18, 149, 3], [71, 19, 149, 3, "default"], [71, 26, 149, 3], [71, 28, 149, 3, "WebSocket"], [71, 37, 149, 3], [71, 39, 149, 3, "_ref"], [71, 43, 149, 3], [72, 4, 149, 3], [72, 15, 149, 3, "_createClass2"], [72, 28, 149, 3], [72, 29, 149, 3, "default"], [72, 36, 149, 3], [72, 38, 149, 3, "WebSocket"], [72, 47, 149, 3], [73, 6, 149, 3, "key"], [73, 9, 149, 3], [74, 6, 149, 3, "get"], [74, 9, 149, 3], [74, 11, 151, 2], [74, 20, 151, 2, "get"], [74, 21, 151, 2], [74, 23, 151, 32], [75, 8, 152, 4], [75, 15, 152, 11], [75, 19, 152, 15], [75, 20, 152, 16, "_binaryType"], [75, 31, 152, 27], [76, 6, 153, 2], [76, 7, 153, 3], [77, 6, 153, 3, "set"], [77, 9, 153, 3], [77, 11, 155, 2], [77, 20, 155, 2, "set"], [77, 21, 155, 17, "binaryType"], [77, 31, 155, 39], [77, 33, 155, 47], [78, 8, 156, 4], [78, 12, 156, 8, "binaryType"], [78, 22, 156, 18], [78, 27, 156, 23], [78, 33, 156, 29], [78, 37, 156, 33, "binaryType"], [78, 47, 156, 43], [78, 52, 156, 48], [78, 65, 156, 61], [78, 67, 156, 63], [79, 10, 157, 6], [79, 16, 157, 12], [79, 20, 157, 16, "Error"], [79, 25, 157, 21], [79, 26, 157, 22], [79, 77, 157, 73], [79, 78, 157, 74], [80, 8, 158, 4], [81, 8, 159, 4], [81, 12, 159, 8], [81, 16, 159, 12], [81, 17, 159, 13, "_binaryType"], [81, 28, 159, 24], [81, 33, 159, 29], [81, 39, 159, 35], [81, 43, 159, 39, "binaryType"], [81, 53, 159, 49], [81, 58, 159, 54], [81, 64, 159, 60], [81, 66, 159, 62], [82, 10, 160, 6], [82, 14, 160, 6, "invariant"], [82, 32, 160, 15], [82, 34, 161, 8, "BlobManager"], [82, 54, 161, 19], [82, 55, 161, 20, "isAvailable"], [82, 66, 161, 31], [82, 68, 162, 8], [82, 123, 163, 6], [82, 124, 163, 7], [83, 10, 164, 6], [83, 14, 164, 10, "binaryType"], [83, 24, 164, 20], [83, 29, 164, 25], [83, 35, 164, 31], [83, 37, 164, 33], [84, 12, 165, 8, "BlobManager"], [84, 32, 165, 19], [84, 33, 165, 20, "addWebSocketHandler"], [84, 52, 165, 39], [84, 53, 165, 40], [84, 57, 165, 44], [84, 58, 165, 45, "_socketId"], [84, 67, 165, 54], [84, 68, 165, 55], [85, 10, 166, 6], [85, 11, 166, 7], [85, 17, 166, 13], [86, 12, 167, 8, "BlobManager"], [86, 32, 167, 19], [86, 33, 167, 20, "removeWebSocketHandler"], [86, 55, 167, 42], [86, 56, 167, 43], [86, 60, 167, 47], [86, 61, 167, 48, "_socketId"], [86, 70, 167, 57], [86, 71, 167, 58], [87, 10, 168, 6], [88, 8, 169, 4], [89, 8, 170, 4], [89, 12, 170, 8], [89, 13, 170, 9, "_binaryType"], [89, 24, 170, 20], [89, 27, 170, 23, "binaryType"], [89, 37, 170, 33], [90, 6, 171, 2], [91, 4, 171, 3], [92, 6, 171, 3, "key"], [92, 9, 171, 3], [93, 6, 171, 3, "value"], [93, 11, 171, 3], [93, 13, 173, 2], [93, 22, 173, 2, "close"], [93, 27, 173, 7, "close"], [93, 28, 173, 8, "code"], [93, 32, 173, 21], [93, 34, 173, 23, "reason"], [93, 40, 173, 38], [93, 42, 173, 46], [94, 8, 174, 4], [94, 12, 174, 8], [94, 16, 174, 12], [94, 17, 174, 13, "readyState"], [94, 27, 174, 23], [94, 32, 174, 28], [94, 36, 174, 32], [94, 37, 174, 33, "CLOSING"], [94, 44, 174, 40], [94, 48, 174, 44], [94, 52, 174, 48], [94, 53, 174, 49, "readyState"], [94, 63, 174, 59], [94, 68, 174, 64], [94, 72, 174, 68], [94, 73, 174, 69, "CLOSED"], [94, 79, 174, 75], [94, 81, 174, 77], [95, 10, 175, 6], [96, 8, 176, 4], [97, 8, 178, 4], [97, 12, 178, 8], [97, 13, 178, 9, "readyState"], [97, 23, 178, 19], [97, 26, 178, 22], [97, 30, 178, 26], [97, 31, 178, 27, "CLOSING"], [97, 38, 178, 34], [98, 8, 179, 4], [98, 12, 179, 8], [98, 13, 179, 9, "_close"], [98, 19, 179, 15], [98, 20, 179, 16, "code"], [98, 24, 179, 20], [98, 26, 179, 22, "reason"], [98, 32, 179, 28], [98, 33, 179, 29], [99, 6, 180, 2], [100, 4, 180, 3], [101, 6, 180, 3, "key"], [101, 9, 180, 3], [102, 6, 180, 3, "value"], [102, 11, 180, 3], [102, 13, 182, 2], [102, 22, 182, 2, "send"], [102, 26, 182, 6, "send"], [102, 27, 182, 7, "data"], [102, 31, 182, 58], [102, 33, 182, 66], [103, 8, 183, 4], [103, 12, 183, 8], [103, 16, 183, 12], [103, 17, 183, 13, "readyState"], [103, 27, 183, 23], [103, 32, 183, 28], [103, 36, 183, 32], [103, 37, 183, 33, "CONNECTING"], [103, 47, 183, 43], [103, 49, 183, 45], [104, 10, 184, 6], [104, 16, 184, 12], [104, 20, 184, 16, "Error"], [104, 25, 184, 21], [104, 26, 184, 22], [104, 45, 184, 41], [104, 46, 184, 42], [105, 8, 185, 4], [106, 8, 187, 4], [106, 12, 187, 8, "data"], [106, 16, 187, 12], [106, 28, 187, 24, "Blob"], [106, 41, 187, 28], [106, 43, 187, 30], [107, 10, 188, 6], [107, 14, 188, 6, "invariant"], [107, 32, 188, 15], [107, 34, 189, 8, "BlobManager"], [107, 54, 189, 19], [107, 55, 189, 20, "isAvailable"], [107, 66, 189, 31], [107, 68, 190, 8], [107, 123, 191, 6], [107, 124, 191, 7], [108, 10, 192, 6, "BlobManager"], [108, 30, 192, 17], [108, 31, 192, 18, "sendOverSocket"], [108, 45, 192, 32], [108, 46, 192, 33, "data"], [108, 50, 192, 37], [108, 52, 192, 39], [108, 56, 192, 43], [108, 57, 192, 44, "_socketId"], [108, 66, 192, 53], [108, 67, 192, 54], [109, 10, 193, 6], [110, 8, 194, 4], [111, 8, 196, 4], [111, 12, 196, 8], [111, 19, 196, 15, "data"], [111, 23, 196, 19], [111, 28, 196, 24], [111, 36, 196, 32], [111, 38, 196, 34], [112, 10, 197, 6, "NativeWebSocketModule"], [112, 40, 197, 27], [112, 41, 197, 28, "send"], [112, 45, 197, 32], [112, 46, 197, 33, "data"], [112, 50, 197, 37], [112, 52, 197, 39], [112, 56, 197, 43], [112, 57, 197, 44, "_socketId"], [112, 66, 197, 53], [112, 67, 197, 54], [113, 10, 198, 6], [114, 8, 199, 4], [115, 8, 201, 4], [115, 12, 201, 8, "data"], [115, 16, 201, 12], [115, 28, 201, 24, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [115, 39, 201, 35], [115, 43, 201, 39, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [115, 54, 201, 50], [115, 55, 201, 51, "<PERSON><PERSON><PERSON><PERSON>"], [115, 61, 201, 57], [115, 62, 201, 58, "data"], [115, 66, 201, 62], [115, 67, 201, 63], [115, 69, 201, 65], [116, 10, 202, 6, "NativeWebSocketModule"], [116, 40, 202, 27], [116, 41, 202, 28, "sendBinary"], [116, 51, 202, 38], [116, 52, 202, 39], [116, 56, 202, 39, "binaryToBase64"], [116, 77, 202, 53], [116, 79, 202, 54, "data"], [116, 83, 202, 58], [116, 84, 202, 59], [116, 86, 202, 61], [116, 90, 202, 65], [116, 91, 202, 66, "_socketId"], [116, 100, 202, 75], [116, 101, 202, 76], [117, 10, 203, 6], [118, 8, 204, 4], [119, 8, 206, 4], [119, 14, 206, 10], [119, 18, 206, 14, "Error"], [119, 23, 206, 19], [119, 24, 206, 20], [119, 47, 206, 43], [119, 48, 206, 44], [120, 6, 207, 2], [121, 4, 207, 3], [122, 6, 207, 3, "key"], [122, 9, 207, 3], [123, 6, 207, 3, "value"], [123, 11, 207, 3], [123, 13, 209, 2], [123, 22, 209, 2, "ping"], [123, 26, 209, 6, "ping"], [123, 27, 209, 6], [123, 29, 209, 15], [124, 8, 210, 4], [124, 12, 210, 8], [124, 16, 210, 12], [124, 17, 210, 13, "readyState"], [124, 27, 210, 23], [124, 32, 210, 28], [124, 36, 210, 32], [124, 37, 210, 33, "CONNECTING"], [124, 47, 210, 43], [124, 49, 210, 45], [125, 10, 211, 6], [125, 16, 211, 12], [125, 20, 211, 16, "Error"], [125, 25, 211, 21], [125, 26, 211, 22], [125, 45, 211, 41], [125, 46, 211, 42], [126, 8, 212, 4], [127, 8, 214, 4, "NativeWebSocketModule"], [127, 38, 214, 25], [127, 39, 214, 26, "ping"], [127, 43, 214, 30], [127, 44, 214, 31], [127, 48, 214, 35], [127, 49, 214, 36, "_socketId"], [127, 58, 214, 45], [127, 59, 214, 46], [128, 6, 215, 2], [129, 4, 215, 3], [130, 6, 215, 3, "key"], [130, 9, 215, 3], [131, 6, 215, 3, "value"], [131, 11, 215, 3], [131, 13, 217, 2], [131, 22, 217, 2, "_close"], [131, 28, 217, 8, "_close"], [131, 29, 217, 9, "code"], [131, 33, 217, 22], [131, 35, 217, 24, "reason"], [131, 41, 217, 39], [131, 43, 217, 47], [132, 8, 219, 4], [132, 12, 219, 10, "statusCode"], [132, 22, 219, 20], [132, 25, 219, 23], [132, 32, 219, 30, "code"], [132, 36, 219, 34], [132, 41, 219, 39], [132, 49, 219, 47], [132, 52, 219, 50, "code"], [132, 56, 219, 54], [132, 59, 219, 57, "CLOSE_NORMAL"], [132, 71, 219, 69], [133, 8, 220, 4], [133, 12, 220, 10, "closeReason"], [133, 23, 220, 21], [133, 26, 220, 24], [133, 33, 220, 31, "reason"], [133, 39, 220, 37], [133, 44, 220, 42], [133, 52, 220, 50], [133, 55, 220, 53, "reason"], [133, 61, 220, 59], [133, 64, 220, 62], [133, 66, 220, 64], [134, 8, 221, 4, "NativeWebSocketModule"], [134, 38, 221, 25], [134, 39, 221, 26, "close"], [134, 44, 221, 31], [134, 45, 221, 32, "statusCode"], [134, 55, 221, 42], [134, 57, 221, 44, "closeReason"], [134, 68, 221, 55], [134, 70, 221, 57], [134, 74, 221, 61], [134, 75, 221, 62, "_socketId"], [134, 84, 221, 71], [134, 85, 221, 72], [135, 8, 223, 4], [135, 12, 223, 8, "BlobManager"], [135, 32, 223, 19], [135, 33, 223, 20, "isAvailable"], [135, 44, 223, 31], [135, 48, 223, 35], [135, 52, 223, 39], [135, 53, 223, 40, "_binaryType"], [135, 64, 223, 51], [135, 69, 223, 56], [135, 75, 223, 62], [135, 77, 223, 64], [136, 10, 224, 6, "BlobManager"], [136, 30, 224, 17], [136, 31, 224, 18, "removeWebSocketHandler"], [136, 53, 224, 40], [136, 54, 224, 41], [136, 58, 224, 45], [136, 59, 224, 46, "_socketId"], [136, 68, 224, 55], [136, 69, 224, 56], [137, 8, 225, 4], [138, 6, 226, 2], [139, 4, 226, 3], [140, 6, 226, 3, "key"], [140, 9, 226, 3], [141, 6, 226, 3, "value"], [141, 11, 226, 3], [141, 13, 228, 2], [141, 22, 228, 2, "_unregisterEvents"], [141, 39, 228, 19, "_unregisterEvents"], [141, 40, 228, 19], [141, 42, 228, 28], [142, 8, 229, 4], [142, 12, 229, 8], [142, 13, 229, 9, "_subscriptions"], [142, 27, 229, 23], [142, 28, 229, 24, "for<PERSON>ach"], [142, 35, 229, 31], [142, 36, 229, 32, "e"], [142, 37, 229, 33], [142, 41, 229, 37, "e"], [142, 42, 229, 38], [142, 43, 229, 39, "remove"], [142, 49, 229, 45], [142, 50, 229, 46], [142, 51, 229, 47], [142, 52, 229, 48], [143, 8, 230, 4], [143, 12, 230, 8], [143, 13, 230, 9, "_subscriptions"], [143, 27, 230, 23], [143, 30, 230, 26], [143, 32, 230, 28], [144, 6, 231, 2], [145, 4, 231, 3], [146, 6, 231, 3, "key"], [146, 9, 231, 3], [147, 6, 231, 3, "value"], [147, 11, 231, 3], [147, 13, 233, 2], [147, 22, 233, 2, "_registerEvents"], [147, 37, 233, 17, "_registerEvents"], [147, 38, 233, 17], [147, 40, 233, 26], [148, 8, 234, 4], [148, 12, 234, 8], [148, 13, 234, 9, "_subscriptions"], [148, 27, 234, 23], [148, 30, 234, 26], [148, 31, 235, 6], [148, 35, 235, 10], [148, 36, 235, 11, "_eventEmitter"], [148, 49, 235, 24], [148, 50, 235, 25, "addListener"], [148, 61, 235, 36], [148, 62, 235, 37], [148, 80, 235, 55], [148, 82, 235, 57, "ev"], [148, 84, 235, 59], [148, 88, 235, 63], [149, 10, 236, 8], [149, 14, 236, 12, "ev"], [149, 16, 236, 14], [149, 17, 236, 15, "id"], [149, 19, 236, 17], [149, 24, 236, 22], [149, 28, 236, 26], [149, 29, 236, 27, "_socketId"], [149, 38, 236, 36], [149, 40, 236, 38], [150, 12, 237, 10], [151, 10, 238, 8], [152, 10, 239, 8], [152, 14, 239, 12, "data"], [152, 18, 239, 56], [152, 21, 239, 59, "ev"], [152, 23, 239, 61], [152, 24, 239, 62, "data"], [152, 28, 239, 66], [153, 10, 240, 8], [153, 18, 240, 16, "ev"], [153, 20, 240, 18], [153, 21, 240, 19, "type"], [153, 25, 240, 23], [154, 12, 241, 10], [154, 17, 241, 15], [154, 25, 241, 23], [155, 14, 242, 12, "data"], [155, 18, 242, 16], [155, 21, 242, 19, "base64"], [155, 38, 242, 25], [155, 39, 242, 26, "toByteArray"], [155, 50, 242, 37], [155, 51, 242, 38, "ev"], [155, 53, 242, 40], [155, 54, 242, 41, "data"], [155, 58, 242, 45], [155, 59, 242, 46], [155, 60, 242, 47, "buffer"], [155, 66, 242, 53], [156, 14, 243, 12], [157, 12, 244, 10], [157, 17, 244, 15], [157, 23, 244, 21], [158, 14, 245, 12, "data"], [158, 18, 245, 16], [158, 21, 245, 19, "BlobManager"], [158, 41, 245, 30], [158, 42, 245, 31, "createFromOptions"], [158, 59, 245, 48], [158, 60, 245, 49, "ev"], [158, 62, 245, 51], [158, 63, 245, 52, "data"], [158, 67, 245, 56], [158, 68, 245, 57], [159, 14, 246, 12], [160, 10, 247, 8], [161, 10, 248, 8], [161, 14, 248, 12], [161, 15, 248, 13, "dispatchEvent"], [161, 28, 248, 26], [161, 29, 248, 27], [161, 33, 248, 31, "WebSocketEvent"], [161, 56, 248, 45], [161, 57, 248, 46], [161, 66, 248, 55], [161, 68, 248, 57], [162, 12, 248, 58, "data"], [163, 10, 248, 62], [163, 11, 248, 63], [163, 12, 248, 64], [163, 13, 248, 65], [164, 8, 249, 6], [164, 9, 249, 7], [164, 10, 249, 8], [164, 12, 250, 6], [164, 16, 250, 10], [164, 17, 250, 11, "_eventEmitter"], [164, 30, 250, 24], [164, 31, 250, 25, "addListener"], [164, 42, 250, 36], [164, 43, 250, 37], [164, 58, 250, 52], [164, 60, 250, 54, "ev"], [164, 62, 250, 56], [164, 66, 250, 60], [165, 10, 251, 8], [165, 14, 251, 12, "ev"], [165, 16, 251, 14], [165, 17, 251, 15, "id"], [165, 19, 251, 17], [165, 24, 251, 22], [165, 28, 251, 26], [165, 29, 251, 27, "_socketId"], [165, 38, 251, 36], [165, 40, 251, 38], [166, 12, 252, 10], [167, 10, 253, 8], [168, 10, 254, 8], [168, 14, 254, 12], [168, 15, 254, 13, "readyState"], [168, 25, 254, 23], [168, 28, 254, 26], [168, 32, 254, 30], [168, 33, 254, 31, "OPEN"], [168, 37, 254, 35], [169, 10, 255, 8], [169, 14, 255, 12], [169, 15, 255, 13, "protocol"], [169, 23, 255, 21], [169, 26, 255, 24, "ev"], [169, 28, 255, 26], [169, 29, 255, 27, "protocol"], [169, 37, 255, 35], [170, 10, 256, 8], [170, 14, 256, 12], [170, 15, 256, 13, "dispatchEvent"], [170, 28, 256, 26], [170, 29, 256, 27], [170, 33, 256, 31, "WebSocketEvent"], [170, 56, 256, 45], [170, 57, 256, 46], [170, 63, 256, 52], [170, 64, 256, 53], [170, 65, 256, 54], [171, 8, 257, 6], [171, 9, 257, 7], [171, 10, 257, 8], [171, 12, 258, 6], [171, 16, 258, 10], [171, 17, 258, 11, "_eventEmitter"], [171, 30, 258, 24], [171, 31, 258, 25, "addListener"], [171, 42, 258, 36], [171, 43, 258, 37], [171, 60, 258, 54], [171, 62, 258, 56, "ev"], [171, 64, 258, 58], [171, 68, 258, 62], [172, 10, 259, 8], [172, 14, 259, 12, "ev"], [172, 16, 259, 14], [172, 17, 259, 15, "id"], [172, 19, 259, 17], [172, 24, 259, 22], [172, 28, 259, 26], [172, 29, 259, 27, "_socketId"], [172, 38, 259, 36], [172, 40, 259, 38], [173, 12, 260, 10], [174, 10, 261, 8], [175, 10, 262, 8], [175, 14, 262, 12], [175, 15, 262, 13, "readyState"], [175, 25, 262, 23], [175, 28, 262, 26], [175, 32, 262, 30], [175, 33, 262, 31, "CLOSED"], [175, 39, 262, 37], [176, 10, 263, 8], [176, 14, 263, 12], [176, 15, 263, 13, "dispatchEvent"], [176, 28, 263, 26], [176, 29, 264, 10], [176, 33, 264, 14, "WebSocketEvent"], [176, 56, 264, 28], [176, 57, 264, 29], [176, 64, 264, 36], [176, 66, 264, 38], [177, 12, 265, 12, "code"], [177, 16, 265, 16], [177, 18, 265, 18, "ev"], [177, 20, 265, 20], [177, 21, 265, 21, "code"], [177, 25, 265, 25], [178, 12, 266, 12, "reason"], [178, 18, 266, 18], [178, 20, 266, 20, "ev"], [178, 22, 266, 22], [178, 23, 266, 23, "reason"], [179, 10, 268, 10], [179, 11, 268, 11], [179, 12, 269, 8], [179, 13, 269, 9], [180, 10, 270, 8], [180, 14, 270, 12], [180, 15, 270, 13, "_unregisterEvents"], [180, 32, 270, 30], [180, 33, 270, 31], [180, 34, 270, 32], [181, 10, 271, 8], [181, 14, 271, 12], [181, 15, 271, 13, "close"], [181, 20, 271, 18], [181, 21, 271, 19], [181, 22, 271, 20], [182, 8, 272, 6], [182, 9, 272, 7], [182, 10, 272, 8], [182, 12, 273, 6], [182, 16, 273, 10], [182, 17, 273, 11, "_eventEmitter"], [182, 30, 273, 24], [182, 31, 273, 25, "addListener"], [182, 42, 273, 36], [182, 43, 273, 37], [182, 60, 273, 54], [182, 62, 273, 56, "ev"], [182, 64, 273, 58], [182, 68, 273, 62], [183, 10, 274, 8], [183, 14, 274, 12, "ev"], [183, 16, 274, 14], [183, 17, 274, 15, "id"], [183, 19, 274, 17], [183, 24, 274, 22], [183, 28, 274, 26], [183, 29, 274, 27, "_socketId"], [183, 38, 274, 36], [183, 40, 274, 38], [184, 12, 275, 10], [185, 10, 276, 8], [186, 10, 277, 8], [186, 14, 277, 12], [186, 15, 277, 13, "readyState"], [186, 25, 277, 23], [186, 28, 277, 26], [186, 32, 277, 30], [186, 33, 277, 31, "CLOSED"], [186, 39, 277, 37], [187, 10, 278, 8], [187, 14, 278, 12], [187, 15, 278, 13, "dispatchEvent"], [187, 28, 278, 26], [187, 29, 279, 10], [187, 33, 279, 14, "WebSocketEvent"], [187, 56, 279, 28], [187, 57, 279, 29], [187, 64, 279, 36], [187, 66, 279, 38], [188, 12, 280, 12, "message"], [188, 19, 280, 19], [188, 21, 280, 21, "ev"], [188, 23, 280, 23], [188, 24, 280, 24, "message"], [189, 10, 281, 10], [189, 11, 281, 11], [189, 12, 282, 8], [189, 13, 282, 9], [190, 10, 283, 8], [190, 14, 283, 12], [190, 15, 283, 13, "dispatchEvent"], [190, 28, 283, 26], [190, 29, 284, 10], [190, 33, 284, 14, "WebSocketEvent"], [190, 56, 284, 28], [190, 57, 284, 29], [190, 64, 284, 36], [190, 66, 284, 38], [191, 12, 285, 12, "code"], [191, 16, 285, 16], [191, 18, 285, 18, "CLOSE_ABNORMAL"], [191, 32, 285, 32], [192, 12, 286, 12, "reason"], [192, 18, 286, 18], [192, 20, 286, 20, "ev"], [192, 22, 286, 22], [192, 23, 286, 23, "message"], [193, 10, 288, 10], [193, 11, 288, 11], [193, 12, 289, 8], [193, 13, 289, 9], [194, 10, 290, 8], [194, 14, 290, 12], [194, 15, 290, 13, "_unregisterEvents"], [194, 32, 290, 30], [194, 33, 290, 31], [194, 34, 290, 32], [195, 10, 291, 8], [195, 14, 291, 12], [195, 15, 291, 13, "close"], [195, 20, 291, 18], [195, 21, 291, 19], [195, 22, 291, 20], [196, 8, 292, 6], [196, 9, 292, 7], [196, 10, 292, 8], [196, 11, 293, 5], [197, 6, 294, 2], [198, 4, 294, 3], [199, 2, 294, 3], [199, 4, 71, 25], [199, 8, 71, 25, "EventTarget"], [199, 32, 71, 36], [199, 34, 71, 37], [199, 37, 71, 40, "WEBSOCKET_EVENTS"], [199, 53, 71, 56], [199, 54, 71, 57], [200, 2, 71, 6, "WebSocket"], [200, 11, 71, 15], [200, 12, 72, 9, "CONNECTING"], [200, 22, 72, 19], [200, 25, 72, 30, "CONNECTING"], [200, 35, 72, 40], [201, 2, 71, 6, "WebSocket"], [201, 11, 71, 15], [201, 12, 73, 9, "OPEN"], [201, 16, 73, 13], [201, 19, 73, 24, "OPEN"], [201, 23, 73, 28], [202, 2, 71, 6, "WebSocket"], [202, 11, 71, 15], [202, 12, 74, 9, "CLOSING"], [202, 19, 74, 16], [202, 22, 74, 27, "CLOSING"], [202, 29, 74, 34], [203, 2, 71, 6, "WebSocket"], [203, 11, 71, 15], [203, 12, 75, 9, "CLOSED"], [203, 18, 75, 15], [203, 21, 75, 26, "CLOSED"], [203, 27, 75, 32], [204, 2, 75, 32], [204, 6, 75, 32, "_default"], [204, 14, 75, 32], [204, 17, 75, 32, "exports"], [204, 24, 75, 32], [204, 25, 75, 32, "default"], [204, 32, 75, 32], [204, 35, 297, 15, "WebSocket"], [204, 44, 297, 24], [205, 0, 297, 24], [205, 3]], "functionMap": {"names": ["<global>", "WebSocket", "WebSocket#constructor", "WebSocket#get__binaryType", "WebSocket#set__binaryType", "WebSocket#close", "WebSocket#send", "WebSocket#ping", "WebSocket#_close", "WebSocket#_unregisterEvents", "_subscriptions.forEach$argument_0", "WebSocket#_registerEvents", "_eventEmitter.addListener$argument_1"], "mappings": "AAA;ACsE;EC2B;GDmD;EEE;GFE;EGE;GHgB;EIE;GJO;EKE;GLyB;EME;GNM;EOE;GPS;EQE;gCCC,eD;GRE;EUE;yDCE;ODc;sDCC;ODO;wDCC;ODc;wDCC;ODmB;GVE;CDC"}}, "type": "js/module"}]}