{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 67, "index": 82}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 83}, "end": {"line": 4, "column": 26, "index": 109}}], "key": "WMoKxUKO/GMHeED0pzSR/dc1v7c=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 110}, "end": {"line": 5, "column": 31, "index": 141}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 142}, "end": {"line": 6, "column": 68, "index": 210}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 211}, "end": {"line": 7, "column": 67, "index": 278}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "../assets/search-icon.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 279}, "end": {"line": 8, "column": 51, "index": 330}}], "key": "ai40rVaAzolPoKDrCo7kH+CIoHU=", "exportNames": ["*"]}}, {"name": "../useFrameSize.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 331}, "end": {"line": 9, "column": 50, "index": 381}}], "key": "7taeXB3nt1BpdUmcfwNb365E1ko=", "exportNames": ["*"]}}, {"name": "./getDefaultHeaderHeight.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 382}, "end": {"line": 10, "column": 69, "index": 451}}], "key": "XS+pdXfrnHFs4TugUxV9atNlm5I=", "exportNames": ["*"]}}, {"name": "./HeaderBackButton.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 452}, "end": {"line": 11, "column": 57, "index": 509}}], "key": "4bKq+NNcBdtVVEx2kV4uO9wAEtk=", "exportNames": ["*"]}}, {"name": "./HeaderBackground.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 510}, "end": {"line": 12, "column": 57, "index": 567}}], "key": "ye9cJnmaS+9XPtNY5wHFS+fPFuI=", "exportNames": ["*"]}}, {"name": "./HeaderButton.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 568}, "end": {"line": 13, "column": 49, "index": 617}}], "key": "5Mfp2bWqztZ2HFy80uJBbvbN6HA=", "exportNames": ["*"]}}, {"name": "./HeaderIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 618}, "end": {"line": 14, "column": 45, "index": 663}}], "key": "0JPASIZzwd0DulPaj/kDrorllj8=", "exportNames": ["*"]}}, {"name": "./HeaderSearchBar.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 664}, "end": {"line": 15, "column": 55, "index": 719}}], "key": "vzM0SWN0jKogpTdmzlNqPMFwJYo=", "exportNames": ["*"]}}, {"name": "./HeaderShownContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 720}, "end": {"line": 16, "column": 61, "index": 781}}], "key": "dJPbJxjIRPcLd4c1Az+chGzNyds=", "exportNames": ["*"]}}, {"name": "./HeaderTitle.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 782}, "end": {"line": 17, "column": 47, "index": 829}}], "key": "EVm/qUR3ximiZhC6CgHKasWhi6k=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 900}, "end": {"line": 20, "column": 86, "index": 986}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Header = Header;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/slicedToArray\"));\n  var _native = require(_dependencyMap[3], \"@react-navigation/native\");\n  var _color = _interopRequireDefault(require(_dependencyMap[4], \"color\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[5], \"react\"));\n  var _reactNative = require(_dependencyMap[6], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[7], \"react-native-safe-area-context\");\n  var _searchIcon = _interopRequireDefault(require(_dependencyMap[8], \"../assets/search-icon.png\"));\n  var _useFrameSize = require(_dependencyMap[9], \"../useFrameSize.js\");\n  var _getDefaultHeaderHeight = require(_dependencyMap[10], \"./getDefaultHeaderHeight.js\");\n  var _HeaderBackButton = require(_dependencyMap[11], \"./HeaderBackButton.js\");\n  var _HeaderBackground = require(_dependencyMap[12], \"./HeaderBackground.js\");\n  var _HeaderButton = require(_dependencyMap[13], \"./HeaderButton.js\");\n  var _HeaderIcon = require(_dependencyMap[14], \"./HeaderIcon.js\");\n  var _HeaderSearchBar = require(_dependencyMap[15], \"./HeaderSearchBar.js\");\n  var _HeaderShownContext = require(_dependencyMap[16], \"./HeaderShownContext.js\");\n  var _HeaderTitle = require(_dependencyMap[17], \"./HeaderTitle.js\");\n  var _jsxRuntime = require(_dependencyMap[18], \"react/jsx-runtime\");\n  var _excluded = [\"height\", \"minHeight\", \"maxHeight\", \"backgroundColor\", \"borderBottomColor\", \"borderBottomEndRadius\", \"borderBottomLeftRadius\", \"borderBottomRightRadius\", \"borderBottomStartRadius\", \"borderBottomWidth\", \"borderColor\", \"borderEndColor\", \"borderEndWidth\", \"borderLeftColor\", \"borderLeftWidth\", \"borderRadius\", \"borderRightColor\", \"borderRightWidth\", \"borderStartColor\", \"borderStartWidth\", \"borderStyle\", \"borderTopColor\", \"borderTopEndRadius\", \"borderTopLeftRadius\", \"borderTopRightRadius\", \"borderTopStartRadius\", \"borderTopWidth\", \"borderWidth\", \"boxShadow\", \"elevation\", \"shadowColor\", \"shadowOffset\", \"shadowOpacity\", \"shadowRadius\", \"opacity\", \"transform\"]; // Width of the screen in split layout on portrait mode on iPad Mini\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var IPAD_MINI_MEDIUM_WIDTH = 414;\n  var warnIfHeaderStylesDefined = styles => {\n    Object.keys(styles).forEach(styleProp => {\n      var value = styles[styleProp];\n      if (styleProp === 'position' && value === 'absolute') {\n        console.warn(\"position: 'absolute' is not supported on headerStyle. If you would like to render content under the header, use the 'headerTransparent' option.\");\n      } else if (value !== undefined) {\n        console.warn(`${styleProp} was given a value of ${value}, this has no effect on headerStyle.`);\n      }\n    });\n  };\n  function Header(props) {\n    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    var frame = (0, _useFrameSize.useFrameSize)(size => size, true);\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors;\n    var navigation = (0, _native.useNavigation)();\n    var isParentHeaderShown = React.useContext(_HeaderShownContext.HeaderShownContext);\n    var _React$useState = React.useState(false),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      searchBarVisible = _React$useState2[0],\n      setSearchBarVisible = _React$useState2[1];\n    var _React$useState3 = React.useState(undefined),\n      _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),\n      titleLayout = _React$useState4[0],\n      setTitleLayout = _React$useState4[1];\n    var onTitleLayout = e => {\n      var _e$nativeEvent$layout = e.nativeEvent.layout,\n        height = _e$nativeEvent$layout.height,\n        width = _e$nativeEvent$layout.width;\n      setTitleLayout(titleLayout => {\n        if (titleLayout && height === titleLayout.height && width === titleLayout.width) {\n          return titleLayout;\n        }\n        return {\n          height,\n          width\n        };\n      });\n    };\n    var _props$layout = props.layout,\n      layout = _props$layout === void 0 ? frame : _props$layout,\n      _props$modal = props.modal,\n      modal = _props$modal === void 0 ? false : _props$modal,\n      back = props.back,\n      title = props.title,\n      customTitle = props.headerTitle,\n      _props$headerTitleAli = props.headerTitleAlign,\n      headerTitleAlign = _props$headerTitleAli === void 0 ? _reactNative.Platform.OS === 'ios' ? 'center' : 'left' : _props$headerTitleAli,\n      _props$headerLeft = props.headerLeft,\n      headerLeft = _props$headerLeft === void 0 ? back ? props => /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderBackButton.HeaderBackButton, {\n        ...props\n      }) : undefined : _props$headerLeft,\n      headerSearchBarOptions = props.headerSearchBarOptions,\n      headerTransparent = props.headerTransparent,\n      headerTintColor = props.headerTintColor,\n      headerBackground = props.headerBackground,\n      headerRight = props.headerRight,\n      titleAllowFontScaling = props.headerTitleAllowFontScaling,\n      titleStyle = props.headerTitleStyle,\n      leftContainerStyle = props.headerLeftContainerStyle,\n      rightContainerStyle = props.headerRightContainerStyle,\n      titleContainerStyle = props.headerTitleContainerStyle,\n      _props$headerBackButt = props.headerBackButtonDisplayMode,\n      headerBackButtonDisplayMode = _props$headerBackButt === void 0 ? _reactNative.Platform.OS === 'ios' ? 'default' : 'minimal' : _props$headerBackButt,\n      headerBackTitleStyle = props.headerBackTitleStyle,\n      backgroundContainerStyle = props.headerBackgroundContainerStyle,\n      customHeaderStyle = props.headerStyle,\n      headerShadowVisible = props.headerShadowVisible,\n      headerPressColor = props.headerPressColor,\n      headerPressOpacity = props.headerPressOpacity,\n      _props$headerStatusBa = props.headerStatusBarHeight,\n      headerStatusBarHeight = _props$headerStatusBa === void 0 ? isParentHeaderShown ? 0 : insets.top : _props$headerStatusBa;\n    var defaultHeight = (0, _getDefaultHeaderHeight.getDefaultHeaderHeight)(layout, modal, headerStatusBarHeight);\n    var _StyleSheet$flatten = _reactNative.StyleSheet.flatten(customHeaderStyle || {}),\n      _StyleSheet$flatten$h = _StyleSheet$flatten.height,\n      height = _StyleSheet$flatten$h === void 0 ? defaultHeight : _StyleSheet$flatten$h,\n      minHeight = _StyleSheet$flatten.minHeight,\n      maxHeight = _StyleSheet$flatten.maxHeight,\n      backgroundColor = _StyleSheet$flatten.backgroundColor,\n      borderBottomColor = _StyleSheet$flatten.borderBottomColor,\n      borderBottomEndRadius = _StyleSheet$flatten.borderBottomEndRadius,\n      borderBottomLeftRadius = _StyleSheet$flatten.borderBottomLeftRadius,\n      borderBottomRightRadius = _StyleSheet$flatten.borderBottomRightRadius,\n      borderBottomStartRadius = _StyleSheet$flatten.borderBottomStartRadius,\n      borderBottomWidth = _StyleSheet$flatten.borderBottomWidth,\n      borderColor = _StyleSheet$flatten.borderColor,\n      borderEndColor = _StyleSheet$flatten.borderEndColor,\n      borderEndWidth = _StyleSheet$flatten.borderEndWidth,\n      borderLeftColor = _StyleSheet$flatten.borderLeftColor,\n      borderLeftWidth = _StyleSheet$flatten.borderLeftWidth,\n      borderRadius = _StyleSheet$flatten.borderRadius,\n      borderRightColor = _StyleSheet$flatten.borderRightColor,\n      borderRightWidth = _StyleSheet$flatten.borderRightWidth,\n      borderStartColor = _StyleSheet$flatten.borderStartColor,\n      borderStartWidth = _StyleSheet$flatten.borderStartWidth,\n      borderStyle = _StyleSheet$flatten.borderStyle,\n      borderTopColor = _StyleSheet$flatten.borderTopColor,\n      borderTopEndRadius = _StyleSheet$flatten.borderTopEndRadius,\n      borderTopLeftRadius = _StyleSheet$flatten.borderTopLeftRadius,\n      borderTopRightRadius = _StyleSheet$flatten.borderTopRightRadius,\n      borderTopStartRadius = _StyleSheet$flatten.borderTopStartRadius,\n      borderTopWidth = _StyleSheet$flatten.borderTopWidth,\n      borderWidth = _StyleSheet$flatten.borderWidth,\n      boxShadow = _StyleSheet$flatten.boxShadow,\n      elevation = _StyleSheet$flatten.elevation,\n      shadowColor = _StyleSheet$flatten.shadowColor,\n      shadowOffset = _StyleSheet$flatten.shadowOffset,\n      shadowOpacity = _StyleSheet$flatten.shadowOpacity,\n      shadowRadius = _StyleSheet$flatten.shadowRadius,\n      opacity = _StyleSheet$flatten.opacity,\n      transform = _StyleSheet$flatten.transform,\n      unsafeStyles = (0, _objectWithoutProperties2.default)(_StyleSheet$flatten, _excluded);\n    if (process.env.NODE_ENV !== 'production') {\n      warnIfHeaderStylesDefined(unsafeStyles);\n    }\n    var safeStyles = {\n      backgroundColor,\n      borderBottomColor,\n      borderBottomEndRadius,\n      borderBottomLeftRadius,\n      borderBottomRightRadius,\n      borderBottomStartRadius,\n      borderBottomWidth,\n      borderColor,\n      borderEndColor,\n      borderEndWidth,\n      borderLeftColor,\n      borderLeftWidth,\n      borderRadius,\n      borderRightColor,\n      borderRightWidth,\n      borderStartColor,\n      borderStartWidth,\n      borderStyle,\n      borderTopColor,\n      borderTopEndRadius,\n      borderTopLeftRadius,\n      borderTopRightRadius,\n      borderTopStartRadius,\n      borderTopWidth,\n      borderWidth,\n      boxShadow,\n      elevation,\n      shadowColor,\n      shadowOffset,\n      shadowOpacity,\n      shadowRadius,\n      opacity,\n      transform\n    };\n\n    // Setting a property to undefined triggers default style\n    // So we need to filter them out\n    // Users can use `null` instead\n    for (var styleProp in safeStyles) {\n      // @ts-expect-error: typescript wrongly complains that styleProp cannot be used to index safeStyles\n      if (safeStyles[styleProp] === undefined) {\n        // @ts-expect-error don't need to care about index signature for deletion\n        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n        delete safeStyles[styleProp];\n      }\n    }\n    var backgroundStyle = {\n      ...(headerTransparent && {\n        backgroundColor: 'transparent'\n      }),\n      ...((headerTransparent || headerShadowVisible === false) && {\n        borderBottomWidth: 0,\n        ..._reactNative.Platform.select({\n          android: {\n            elevation: 0\n          },\n          web: {\n            boxShadow: 'none'\n          },\n          default: {\n            shadowOpacity: 0\n          }\n        })\n      }),\n      ...safeStyles\n    };\n    var iconTintColor = headerTintColor ?? _reactNative.Platform.select({\n      ios: colors.primary,\n      default: colors.text\n    });\n    var leftButton = headerLeft ? headerLeft({\n      tintColor: iconTintColor,\n      pressColor: headerPressColor,\n      pressOpacity: headerPressOpacity,\n      displayMode: headerBackButtonDisplayMode,\n      titleLayout,\n      screenLayout: layout,\n      canGoBack: Boolean(back),\n      onPress: back ? navigation.goBack : undefined,\n      label: back?.title,\n      labelStyle: headerBackTitleStyle,\n      href: back?.href\n    }) : null;\n    var rightButton = headerRight ? headerRight({\n      tintColor: iconTintColor,\n      pressColor: headerPressColor,\n      pressOpacity: headerPressOpacity,\n      canGoBack: Boolean(back)\n    }) : null;\n    var headerTitle = typeof customTitle !== 'function' ? props => /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderTitle.HeaderTitle, {\n      ...props\n    }) : customTitle;\n    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.Animated.View, {\n      pointerEvents: \"box-none\",\n      style: [{\n        height,\n        minHeight,\n        maxHeight,\n        opacity,\n        transform\n      }],\n      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {\n        pointerEvents: \"box-none\",\n        style: [_reactNative.StyleSheet.absoluteFill, backgroundContainerStyle],\n        children: headerBackground ? headerBackground({\n          style: backgroundStyle\n        }) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderBackground.HeaderBackground, {\n          pointerEvents:\n          // Allow touch through the header when background color is transparent\n          headerTransparent && (backgroundStyle.backgroundColor === 'transparent' || (0, _color.default)(backgroundStyle.backgroundColor).alpha() === 0) ? 'none' : 'auto',\n          style: backgroundStyle\n        })\n      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n        pointerEvents: \"none\",\n        style: {\n          height: headerStatusBarHeight\n        }\n      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {\n        pointerEvents: \"box-none\",\n        style: [styles.content, _reactNative.Platform.OS === 'ios' && frame.width >= IPAD_MINI_MEDIUM_WIDTH ? styles.large : null],\n        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {\n          pointerEvents: \"box-none\",\n          style: [styles.start, !searchBarVisible && headerTitleAlign === 'center' && styles.expand, {\n            marginStart: insets.left\n          }, leftContainerStyle],\n          children: leftButton\n        }), _reactNative.Platform.OS === 'ios' || !searchBarVisible ? /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {\n            pointerEvents: \"box-none\",\n            style: [styles.title, {\n              // Avoid the title from going offscreen or overlapping buttons\n              maxWidth: headerTitleAlign === 'center' ? layout.width - ((leftButton ? headerBackButtonDisplayMode !== 'minimal' ? 80 : 32 : 16) + (rightButton || headerSearchBarOptions ? 16 : 0) + Math.max(insets.left, insets.right)) * 2 : layout.width - ((leftButton ? 52 : 16) + (rightButton || headerSearchBarOptions ? 52 : 16) + insets.left - insets.right)\n            }, headerTitleAlign === 'left' && leftButton ? {\n              marginStart: 4\n            } : {\n              marginHorizontal: 16\n            }, titleContainerStyle],\n            children: headerTitle({\n              children: title,\n              allowFontScaling: titleAllowFontScaling,\n              tintColor: headerTintColor,\n              onLayout: onTitleLayout,\n              style: titleStyle\n            })\n          }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.Animated.View, {\n            pointerEvents: \"box-none\",\n            style: [styles.end, styles.expand, {\n              marginEnd: insets.right\n            }, rightContainerStyle],\n            children: [rightButton, headerSearchBarOptions ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderButton.HeaderButton, {\n              tintColor: iconTintColor,\n              pressColor: headerPressColor,\n              pressOpacity: headerPressOpacity,\n              onPress: () => {\n                setSearchBarVisible(true);\n                headerSearchBarOptions?.onOpen?.();\n              },\n              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderIcon.HeaderIcon, {\n                source: _searchIcon.default,\n                tintColor: iconTintColor\n              })\n            }) : null]\n          })]\n        }) : null, _reactNative.Platform.OS === 'ios' || searchBarVisible ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderSearchBar.HeaderSearchBar, {\n          ...headerSearchBarOptions,\n          visible: searchBarVisible,\n          onClose: () => {\n            setSearchBarVisible(false);\n            headerSearchBarOptions?.onClose?.();\n          },\n          tintColor: headerTintColor,\n          style: [_reactNative.Platform.OS === 'ios' ? [_reactNative.StyleSheet.absoluteFill, {\n            paddingTop: headerStatusBarHeight ? 0 : 4\n          }, {\n            backgroundColor: backgroundColor ?? colors.card\n          }] : !leftButton && {\n            marginStart: 8\n          }]\n        }) : null]\n      })]\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    content: {\n      flex: 1,\n      flexDirection: 'row',\n      alignItems: 'stretch'\n    },\n    large: {\n      marginHorizontal: 5\n    },\n    title: {\n      justifyContent: 'center'\n    },\n    start: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      justifyContent: 'flex-start'\n    },\n    end: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      justifyContent: 'flex-end'\n    },\n    expand: {\n      flexGrow: 1,\n      flexBasis: 0\n    }\n  });\n});", "lineCount": 355, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Header"], [8, 16, 1, 13], [8, 19, 1, 13, "Header"], [8, 25, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_slicedToArray2"], [10, 21, 1, 13], [10, 24, 1, 13, "_interopRequireDefault"], [10, 46, 1, 13], [10, 47, 1, 13, "require"], [10, 54, 1, 13], [10, 55, 1, 13, "_dependencyMap"], [10, 69, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "_native"], [11, 13, 3, 0], [11, 16, 3, 0, "require"], [11, 23, 3, 0], [11, 24, 3, 0, "_dependencyMap"], [11, 38, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_color"], [12, 12, 4, 0], [12, 15, 4, 0, "_interopRequireDefault"], [12, 37, 4, 0], [12, 38, 4, 0, "require"], [12, 45, 4, 0], [12, 46, 4, 0, "_dependencyMap"], [12, 60, 4, 0], [13, 2, 5, 0], [13, 6, 5, 0, "React"], [13, 11, 5, 0], [13, 14, 5, 0, "_interopRequireWildcard"], [13, 37, 5, 0], [13, 38, 5, 0, "require"], [13, 45, 5, 0], [13, 46, 5, 0, "_dependencyMap"], [13, 60, 5, 0], [14, 2, 6, 0], [14, 6, 6, 0, "_reactNative"], [14, 18, 6, 0], [14, 21, 6, 0, "require"], [14, 28, 6, 0], [14, 29, 6, 0, "_dependencyMap"], [14, 43, 6, 0], [15, 2, 7, 0], [15, 6, 7, 0, "_reactNativeSafeAreaContext"], [15, 33, 7, 0], [15, 36, 7, 0, "require"], [15, 43, 7, 0], [15, 44, 7, 0, "_dependencyMap"], [15, 58, 7, 0], [16, 2, 8, 0], [16, 6, 8, 0, "_searchIcon"], [16, 17, 8, 0], [16, 20, 8, 0, "_interopRequireDefault"], [16, 42, 8, 0], [16, 43, 8, 0, "require"], [16, 50, 8, 0], [16, 51, 8, 0, "_dependencyMap"], [16, 65, 8, 0], [17, 2, 9, 0], [17, 6, 9, 0, "_useFrameSize"], [17, 19, 9, 0], [17, 22, 9, 0, "require"], [17, 29, 9, 0], [17, 30, 9, 0, "_dependencyMap"], [17, 44, 9, 0], [18, 2, 10, 0], [18, 6, 10, 0, "_getDefaultHeaderHeight"], [18, 29, 10, 0], [18, 32, 10, 0, "require"], [18, 39, 10, 0], [18, 40, 10, 0, "_dependencyMap"], [18, 54, 10, 0], [19, 2, 11, 0], [19, 6, 11, 0, "_HeaderBackButton"], [19, 23, 11, 0], [19, 26, 11, 0, "require"], [19, 33, 11, 0], [19, 34, 11, 0, "_dependencyMap"], [19, 48, 11, 0], [20, 2, 12, 0], [20, 6, 12, 0, "_HeaderBackground"], [20, 23, 12, 0], [20, 26, 12, 0, "require"], [20, 33, 12, 0], [20, 34, 12, 0, "_dependencyMap"], [20, 48, 12, 0], [21, 2, 13, 0], [21, 6, 13, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [21, 19, 13, 0], [21, 22, 13, 0, "require"], [21, 29, 13, 0], [21, 30, 13, 0, "_dependencyMap"], [21, 44, 13, 0], [22, 2, 14, 0], [22, 6, 14, 0, "_HeaderIcon"], [22, 17, 14, 0], [22, 20, 14, 0, "require"], [22, 27, 14, 0], [22, 28, 14, 0, "_dependencyMap"], [22, 42, 14, 0], [23, 2, 15, 0], [23, 6, 15, 0, "_HeaderSear<PERSON>B<PERSON>"], [23, 22, 15, 0], [23, 25, 15, 0, "require"], [23, 32, 15, 0], [23, 33, 15, 0, "_dependencyMap"], [23, 47, 15, 0], [24, 2, 16, 0], [24, 6, 16, 0, "_HeaderShownContext"], [24, 25, 16, 0], [24, 28, 16, 0, "require"], [24, 35, 16, 0], [24, 36, 16, 0, "_dependencyMap"], [24, 50, 16, 0], [25, 2, 17, 0], [25, 6, 17, 0, "_Header<PERSON>itle"], [25, 18, 17, 0], [25, 21, 17, 0, "require"], [25, 28, 17, 0], [25, 29, 17, 0, "_dependencyMap"], [25, 43, 17, 0], [26, 2, 20, 0], [26, 6, 20, 0, "_jsxRuntime"], [26, 17, 20, 0], [26, 20, 20, 0, "require"], [26, 27, 20, 0], [26, 28, 20, 0, "_dependencyMap"], [26, 42, 20, 0], [27, 2, 20, 86], [27, 6, 20, 86, "_excluded"], [27, 15, 20, 86], [27, 680, 19, 0], [28, 2, 19, 0], [28, 11, 19, 0, "_interopRequireWildcard"], [28, 35, 19, 0, "e"], [28, 36, 19, 0], [28, 38, 19, 0, "t"], [28, 39, 19, 0], [28, 68, 19, 0, "WeakMap"], [28, 75, 19, 0], [28, 81, 19, 0, "r"], [28, 82, 19, 0], [28, 89, 19, 0, "WeakMap"], [28, 96, 19, 0], [28, 100, 19, 0, "n"], [28, 101, 19, 0], [28, 108, 19, 0, "WeakMap"], [28, 115, 19, 0], [28, 127, 19, 0, "_interopRequireWildcard"], [28, 150, 19, 0], [28, 162, 19, 0, "_interopRequireWildcard"], [28, 163, 19, 0, "e"], [28, 164, 19, 0], [28, 166, 19, 0, "t"], [28, 167, 19, 0], [28, 176, 19, 0, "t"], [28, 177, 19, 0], [28, 181, 19, 0, "e"], [28, 182, 19, 0], [28, 186, 19, 0, "e"], [28, 187, 19, 0], [28, 188, 19, 0, "__esModule"], [28, 198, 19, 0], [28, 207, 19, 0, "e"], [28, 208, 19, 0], [28, 214, 19, 0, "o"], [28, 215, 19, 0], [28, 217, 19, 0, "i"], [28, 218, 19, 0], [28, 220, 19, 0, "f"], [28, 221, 19, 0], [28, 226, 19, 0, "__proto__"], [28, 235, 19, 0], [28, 243, 19, 0, "default"], [28, 250, 19, 0], [28, 252, 19, 0, "e"], [28, 253, 19, 0], [28, 270, 19, 0, "e"], [28, 271, 19, 0], [28, 294, 19, 0, "e"], [28, 295, 19, 0], [28, 320, 19, 0, "e"], [28, 321, 19, 0], [28, 330, 19, 0, "f"], [28, 331, 19, 0], [28, 337, 19, 0, "o"], [28, 338, 19, 0], [28, 341, 19, 0, "t"], [28, 342, 19, 0], [28, 345, 19, 0, "n"], [28, 346, 19, 0], [28, 349, 19, 0, "r"], [28, 350, 19, 0], [28, 358, 19, 0, "o"], [28, 359, 19, 0], [28, 360, 19, 0, "has"], [28, 363, 19, 0], [28, 364, 19, 0, "e"], [28, 365, 19, 0], [28, 375, 19, 0, "o"], [28, 376, 19, 0], [28, 377, 19, 0, "get"], [28, 380, 19, 0], [28, 381, 19, 0, "e"], [28, 382, 19, 0], [28, 385, 19, 0, "o"], [28, 386, 19, 0], [28, 387, 19, 0, "set"], [28, 390, 19, 0], [28, 391, 19, 0, "e"], [28, 392, 19, 0], [28, 394, 19, 0, "f"], [28, 395, 19, 0], [28, 409, 19, 0, "_t"], [28, 411, 19, 0], [28, 415, 19, 0, "e"], [28, 416, 19, 0], [28, 432, 19, 0, "_t"], [28, 434, 19, 0], [28, 441, 19, 0, "hasOwnProperty"], [28, 455, 19, 0], [28, 456, 19, 0, "call"], [28, 460, 19, 0], [28, 461, 19, 0, "e"], [28, 462, 19, 0], [28, 464, 19, 0, "_t"], [28, 466, 19, 0], [28, 473, 19, 0, "i"], [28, 474, 19, 0], [28, 478, 19, 0, "o"], [28, 479, 19, 0], [28, 482, 19, 0, "Object"], [28, 488, 19, 0], [28, 489, 19, 0, "defineProperty"], [28, 503, 19, 0], [28, 508, 19, 0, "Object"], [28, 514, 19, 0], [28, 515, 19, 0, "getOwnPropertyDescriptor"], [28, 539, 19, 0], [28, 540, 19, 0, "e"], [28, 541, 19, 0], [28, 543, 19, 0, "_t"], [28, 545, 19, 0], [28, 552, 19, 0, "i"], [28, 553, 19, 0], [28, 554, 19, 0, "get"], [28, 557, 19, 0], [28, 561, 19, 0, "i"], [28, 562, 19, 0], [28, 563, 19, 0, "set"], [28, 566, 19, 0], [28, 570, 19, 0, "o"], [28, 571, 19, 0], [28, 572, 19, 0, "f"], [28, 573, 19, 0], [28, 575, 19, 0, "_t"], [28, 577, 19, 0], [28, 579, 19, 0, "i"], [28, 580, 19, 0], [28, 584, 19, 0, "f"], [28, 585, 19, 0], [28, 586, 19, 0, "_t"], [28, 588, 19, 0], [28, 592, 19, 0, "e"], [28, 593, 19, 0], [28, 594, 19, 0, "_t"], [28, 596, 19, 0], [28, 607, 19, 0, "f"], [28, 608, 19, 0], [28, 613, 19, 0, "e"], [28, 614, 19, 0], [28, 616, 19, 0, "t"], [28, 617, 19, 0], [29, 2, 21, 0], [29, 6, 21, 6, "IPAD_MINI_MEDIUM_WIDTH"], [29, 28, 21, 28], [29, 31, 21, 31], [29, 34, 21, 34], [30, 2, 22, 0], [30, 6, 22, 6, "warnIfHeaderStylesDefined"], [30, 31, 22, 31], [30, 34, 22, 34, "styles"], [30, 40, 22, 40], [30, 44, 22, 44], [31, 4, 23, 2, "Object"], [31, 10, 23, 8], [31, 11, 23, 9, "keys"], [31, 15, 23, 13], [31, 16, 23, 14, "styles"], [31, 22, 23, 20], [31, 23, 23, 21], [31, 24, 23, 22, "for<PERSON>ach"], [31, 31, 23, 29], [31, 32, 23, 30, "styleProp"], [31, 41, 23, 39], [31, 45, 23, 43], [32, 6, 24, 4], [32, 10, 24, 10, "value"], [32, 15, 24, 15], [32, 18, 24, 18, "styles"], [32, 24, 24, 24], [32, 25, 24, 25, "styleProp"], [32, 34, 24, 34], [32, 35, 24, 35], [33, 6, 25, 4], [33, 10, 25, 8, "styleProp"], [33, 19, 25, 17], [33, 24, 25, 22], [33, 34, 25, 32], [33, 38, 25, 36, "value"], [33, 43, 25, 41], [33, 48, 25, 46], [33, 58, 25, 56], [33, 60, 25, 58], [34, 8, 26, 6, "console"], [34, 15, 26, 13], [34, 16, 26, 14, "warn"], [34, 20, 26, 18], [34, 21, 26, 19], [34, 166, 26, 164], [34, 167, 26, 165], [35, 6, 27, 4], [35, 7, 27, 5], [35, 13, 27, 11], [35, 17, 27, 15, "value"], [35, 22, 27, 20], [35, 27, 27, 25, "undefined"], [35, 36, 27, 34], [35, 38, 27, 36], [36, 8, 28, 6, "console"], [36, 15, 28, 13], [36, 16, 28, 14, "warn"], [36, 20, 28, 18], [36, 21, 28, 19], [36, 24, 28, 22, "styleProp"], [36, 33, 28, 31], [36, 58, 28, 56, "value"], [36, 63, 28, 61], [36, 101, 28, 99], [36, 102, 28, 100], [37, 6, 29, 4], [38, 4, 30, 2], [38, 5, 30, 3], [38, 6, 30, 4], [39, 2, 31, 0], [39, 3, 31, 1], [40, 2, 32, 7], [40, 11, 32, 16, "Header"], [40, 17, 32, 22, "Header"], [40, 18, 32, 23, "props"], [40, 23, 32, 28], [40, 25, 32, 30], [41, 4, 33, 2], [41, 8, 33, 8, "insets"], [41, 14, 33, 14], [41, 17, 33, 17], [41, 21, 33, 17, "useSafeAreaInsets"], [41, 66, 33, 34], [41, 68, 33, 35], [41, 69, 33, 36], [42, 4, 34, 2], [42, 8, 34, 8, "frame"], [42, 13, 34, 13], [42, 16, 34, 16], [42, 20, 34, 16, "useFrameSize"], [42, 46, 34, 28], [42, 48, 34, 29, "size"], [42, 52, 34, 33], [42, 56, 34, 37, "size"], [42, 60, 34, 41], [42, 62, 34, 43], [42, 66, 34, 47], [42, 67, 34, 48], [43, 4, 35, 2], [43, 8, 35, 2, "_useTheme"], [43, 17, 35, 2], [43, 20, 37, 6], [43, 24, 37, 6, "useTheme"], [43, 40, 37, 14], [43, 42, 37, 15], [43, 43, 37, 16], [44, 6, 36, 4, "colors"], [44, 12, 36, 10], [44, 15, 36, 10, "_useTheme"], [44, 24, 36, 10], [44, 25, 36, 4, "colors"], [44, 31, 36, 10], [45, 4, 38, 2], [45, 8, 38, 8, "navigation"], [45, 18, 38, 18], [45, 21, 38, 21], [45, 25, 38, 21, "useNavigation"], [45, 46, 38, 34], [45, 48, 38, 35], [45, 49, 38, 36], [46, 4, 39, 2], [46, 8, 39, 8, "isParentHeaderShown"], [46, 27, 39, 27], [46, 30, 39, 30, "React"], [46, 35, 39, 35], [46, 36, 39, 36, "useContext"], [46, 46, 39, 46], [46, 47, 39, 47, "HeaderShownContext"], [46, 85, 39, 65], [46, 86, 39, 66], [47, 4, 40, 2], [47, 8, 40, 2, "_React$useState"], [47, 23, 40, 2], [47, 26, 40, 50, "React"], [47, 31, 40, 55], [47, 32, 40, 56, "useState"], [47, 40, 40, 64], [47, 41, 40, 65], [47, 46, 40, 70], [47, 47, 40, 71], [48, 6, 40, 71, "_React$useState2"], [48, 22, 40, 71], [48, 29, 40, 71, "_slicedToArray2"], [48, 44, 40, 71], [48, 45, 40, 71, "default"], [48, 52, 40, 71], [48, 54, 40, 71, "_React$useState"], [48, 69, 40, 71], [49, 6, 40, 9, "searchBarVisible"], [49, 22, 40, 25], [49, 25, 40, 25, "_React$useState2"], [49, 41, 40, 25], [50, 6, 40, 27, "setSearchBarVisible"], [50, 25, 40, 46], [50, 28, 40, 46, "_React$useState2"], [50, 44, 40, 46], [51, 4, 41, 2], [51, 8, 41, 2, "_React$useState3"], [51, 24, 41, 2], [51, 27, 41, 40, "React"], [51, 32, 41, 45], [51, 33, 41, 46, "useState"], [51, 41, 41, 54], [51, 42, 41, 55, "undefined"], [51, 51, 41, 64], [51, 52, 41, 65], [52, 6, 41, 65, "_React$useState4"], [52, 22, 41, 65], [52, 29, 41, 65, "_slicedToArray2"], [52, 44, 41, 65], [52, 45, 41, 65, "default"], [52, 52, 41, 65], [52, 54, 41, 65, "_React$useState3"], [52, 70, 41, 65], [53, 6, 41, 9, "titleLayout"], [53, 17, 41, 20], [53, 20, 41, 20, "_React$useState4"], [53, 36, 41, 20], [54, 6, 41, 22, "setTitleLayout"], [54, 20, 41, 36], [54, 23, 41, 36, "_React$useState4"], [54, 39, 41, 36], [55, 4, 42, 2], [55, 8, 42, 8, "onTitleLayout"], [55, 21, 42, 21], [55, 24, 42, 24, "e"], [55, 25, 42, 25], [55, 29, 42, 29], [56, 6, 43, 4], [56, 10, 43, 4, "_e$nativeEvent$layout"], [56, 31, 43, 4], [56, 34, 46, 8, "e"], [56, 35, 46, 9], [56, 36, 46, 10, "nativeEvent"], [56, 47, 46, 21], [56, 48, 46, 22, "layout"], [56, 54, 46, 28], [57, 8, 44, 6, "height"], [57, 14, 44, 12], [57, 17, 44, 12, "_e$nativeEvent$layout"], [57, 38, 44, 12], [57, 39, 44, 6, "height"], [57, 45, 44, 12], [58, 8, 45, 6, "width"], [58, 13, 45, 11], [58, 16, 45, 11, "_e$nativeEvent$layout"], [58, 37, 45, 11], [58, 38, 45, 6, "width"], [58, 43, 45, 11], [59, 6, 47, 4, "setTitleLayout"], [59, 20, 47, 18], [59, 21, 47, 19, "titleLayout"], [59, 32, 47, 30], [59, 36, 47, 34], [60, 8, 48, 6], [60, 12, 48, 10, "titleLayout"], [60, 23, 48, 21], [60, 27, 48, 25, "height"], [60, 33, 48, 31], [60, 38, 48, 36, "titleLayout"], [60, 49, 48, 47], [60, 50, 48, 48, "height"], [60, 56, 48, 54], [60, 60, 48, 58, "width"], [60, 65, 48, 63], [60, 70, 48, 68, "titleLayout"], [60, 81, 48, 79], [60, 82, 48, 80, "width"], [60, 87, 48, 85], [60, 89, 48, 87], [61, 10, 49, 8], [61, 17, 49, 15, "titleLayout"], [61, 28, 49, 26], [62, 8, 50, 6], [63, 8, 51, 6], [63, 15, 51, 13], [64, 10, 52, 8, "height"], [64, 16, 52, 14], [65, 10, 53, 8, "width"], [66, 8, 54, 6], [66, 9, 54, 7], [67, 6, 55, 4], [67, 7, 55, 5], [67, 8, 55, 6], [68, 4, 56, 2], [68, 5, 56, 3], [69, 4, 57, 2], [69, 8, 57, 2, "_props$layout"], [69, 21, 57, 2], [69, 24, 85, 6, "props"], [69, 29, 85, 11], [69, 30, 58, 4, "layout"], [69, 36, 58, 10], [70, 6, 58, 4, "layout"], [70, 12, 58, 10], [70, 15, 58, 10, "_props$layout"], [70, 28, 58, 10], [70, 42, 58, 13, "frame"], [70, 47, 58, 18], [70, 50, 58, 18, "_props$layout"], [70, 63, 58, 18], [71, 6, 58, 18, "_props$modal"], [71, 18, 58, 18], [71, 21, 85, 6, "props"], [71, 26, 85, 11], [71, 27, 59, 4, "modal"], [71, 32, 59, 9], [72, 6, 59, 4, "modal"], [72, 11, 59, 9], [72, 14, 59, 9, "_props$modal"], [72, 26, 59, 9], [72, 40, 59, 12], [72, 45, 59, 17], [72, 48, 59, 17, "_props$modal"], [72, 60, 59, 17], [73, 6, 60, 4, "back"], [73, 10, 60, 8], [73, 13, 85, 6, "props"], [73, 18, 85, 11], [73, 19, 60, 4, "back"], [73, 23, 60, 8], [74, 6, 61, 4, "title"], [74, 11, 61, 9], [74, 14, 85, 6, "props"], [74, 19, 85, 11], [74, 20, 61, 4, "title"], [74, 25, 61, 9], [75, 6, 62, 17, "customTitle"], [75, 17, 62, 28], [75, 20, 85, 6, "props"], [75, 25, 85, 11], [75, 26, 62, 4, "headerTitle"], [75, 37, 62, 15], [76, 6, 62, 15, "_props$headerTitleAli"], [76, 27, 62, 15], [76, 30, 85, 6, "props"], [76, 35, 85, 11], [76, 36, 63, 4, "headerTitleAlign"], [76, 52, 63, 20], [77, 6, 63, 4, "headerTitleAlign"], [77, 22, 63, 20], [77, 25, 63, 20, "_props$headerTitleAli"], [77, 46, 63, 20], [77, 60, 63, 23, "Platform"], [77, 81, 63, 31], [77, 82, 63, 32, "OS"], [77, 84, 63, 34], [77, 89, 63, 39], [77, 94, 63, 44], [77, 97, 63, 47], [77, 105, 63, 55], [77, 108, 63, 58], [77, 114, 63, 64], [77, 117, 63, 64, "_props$headerTitleAli"], [77, 138, 63, 64], [78, 6, 63, 64, "_props$headerLeft"], [78, 23, 63, 64], [78, 26, 85, 6, "props"], [78, 31, 85, 11], [78, 32, 64, 4, "headerLeft"], [78, 42, 64, 14], [79, 6, 64, 4, "headerLeft"], [79, 16, 64, 14], [79, 19, 64, 14, "_props$headerLeft"], [79, 36, 64, 14], [79, 50, 64, 17, "back"], [79, 54, 64, 21], [79, 57, 64, 24, "props"], [79, 62, 64, 29], [79, 66, 64, 33], [79, 79, 64, 46], [79, 83, 64, 46, "_jsx"], [79, 98, 64, 50], [79, 100, 64, 51, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [79, 134, 64, 67], [79, 136, 64, 69], [80, 8, 65, 6], [80, 11, 65, 9, "props"], [81, 6, 66, 4], [81, 7, 66, 5], [81, 8, 66, 6], [81, 11, 66, 9, "undefined"], [81, 20, 66, 18], [81, 23, 66, 18, "_props$headerLeft"], [81, 40, 66, 18], [82, 6, 67, 4, "headerSearchBarOptions"], [82, 28, 67, 26], [82, 31, 85, 6, "props"], [82, 36, 85, 11], [82, 37, 67, 4, "headerSearchBarOptions"], [82, 59, 67, 26], [83, 6, 68, 4, "headerTransparent"], [83, 23, 68, 21], [83, 26, 85, 6, "props"], [83, 31, 85, 11], [83, 32, 68, 4, "headerTransparent"], [83, 49, 68, 21], [84, 6, 69, 4, "headerTintColor"], [84, 21, 69, 19], [84, 24, 85, 6, "props"], [84, 29, 85, 11], [84, 30, 69, 4, "headerTintColor"], [84, 45, 69, 19], [85, 6, 70, 4, "headerBackground"], [85, 22, 70, 20], [85, 25, 85, 6, "props"], [85, 30, 85, 11], [85, 31, 70, 4, "headerBackground"], [85, 47, 70, 20], [86, 6, 71, 4, "headerRight"], [86, 17, 71, 15], [86, 20, 85, 6, "props"], [86, 25, 85, 11], [86, 26, 71, 4, "headerRight"], [86, 37, 71, 15], [87, 6, 72, 33, "titleAllowFontScaling"], [87, 27, 72, 54], [87, 30, 85, 6, "props"], [87, 35, 85, 11], [87, 36, 72, 4, "headerTitleAllowFontScaling"], [87, 63, 72, 31], [88, 6, 73, 22, "titleStyle"], [88, 16, 73, 32], [88, 19, 85, 6, "props"], [88, 24, 85, 11], [88, 25, 73, 4, "headerTitleStyle"], [88, 41, 73, 20], [89, 6, 74, 30, "leftContainerStyle"], [89, 24, 74, 48], [89, 27, 85, 6, "props"], [89, 32, 85, 11], [89, 33, 74, 4, "headerLeftContainerStyle"], [89, 57, 74, 28], [90, 6, 75, 31, "rightContainerStyle"], [90, 25, 75, 50], [90, 28, 85, 6, "props"], [90, 33, 85, 11], [90, 34, 75, 4, "headerRightContainerStyle"], [90, 59, 75, 29], [91, 6, 76, 31, "titleContainerStyle"], [91, 25, 76, 50], [91, 28, 85, 6, "props"], [91, 33, 85, 11], [91, 34, 76, 4, "headerTitleContainerStyle"], [91, 59, 76, 29], [92, 6, 76, 29, "_props$headerBackButt"], [92, 27, 76, 29], [92, 30, 85, 6, "props"], [92, 35, 85, 11], [92, 36, 77, 4, "headerBackButtonDisplayMode"], [92, 63, 77, 31], [93, 6, 77, 4, "headerBackButtonDisplayMode"], [93, 33, 77, 31], [93, 36, 77, 31, "_props$headerBackButt"], [93, 57, 77, 31], [93, 71, 77, 34, "Platform"], [93, 92, 77, 42], [93, 93, 77, 43, "OS"], [93, 95, 77, 45], [93, 100, 77, 50], [93, 105, 77, 55], [93, 108, 77, 58], [93, 117, 77, 67], [93, 120, 77, 70], [93, 129, 77, 79], [93, 132, 77, 79, "_props$headerBackButt"], [93, 153, 77, 79], [94, 6, 78, 4, "headerBackTitleStyle"], [94, 26, 78, 24], [94, 29, 85, 6, "props"], [94, 34, 85, 11], [94, 35, 78, 4, "headerBackTitleStyle"], [94, 55, 78, 24], [95, 6, 79, 36, "backgroundContainerStyle"], [95, 30, 79, 60], [95, 33, 85, 6, "props"], [95, 38, 85, 11], [95, 39, 79, 4, "headerBackgroundContainerStyle"], [95, 69, 79, 34], [96, 6, 80, 17, "customHeaderStyle"], [96, 23, 80, 34], [96, 26, 85, 6, "props"], [96, 31, 85, 11], [96, 32, 80, 4, "headerStyle"], [96, 43, 80, 15], [97, 6, 81, 4, "headerShadowVisible"], [97, 25, 81, 23], [97, 28, 85, 6, "props"], [97, 33, 85, 11], [97, 34, 81, 4, "headerShadowVisible"], [97, 53, 81, 23], [98, 6, 82, 4, "headerPressColor"], [98, 22, 82, 20], [98, 25, 85, 6, "props"], [98, 30, 85, 11], [98, 31, 82, 4, "headerPressColor"], [98, 47, 82, 20], [99, 6, 83, 4, "headerPressOpacity"], [99, 24, 83, 22], [99, 27, 85, 6, "props"], [99, 32, 85, 11], [99, 33, 83, 4, "headerPressOpacity"], [99, 51, 83, 22], [100, 6, 83, 22, "_props$headerStatusBa"], [100, 27, 83, 22], [100, 30, 85, 6, "props"], [100, 35, 85, 11], [100, 36, 84, 4, "headerStatusBarHeight"], [100, 57, 84, 25], [101, 6, 84, 4, "headerStatusBarHeight"], [101, 27, 84, 25], [101, 30, 84, 25, "_props$headerStatusBa"], [101, 51, 84, 25], [101, 65, 84, 28, "isParentHeaderShown"], [101, 84, 84, 47], [101, 87, 84, 50], [101, 88, 84, 51], [101, 91, 84, 54, "insets"], [101, 97, 84, 60], [101, 98, 84, 61, "top"], [101, 101, 84, 64], [101, 104, 84, 64, "_props$headerStatusBa"], [101, 125, 84, 64], [102, 4, 86, 2], [102, 8, 86, 8, "defaultHeight"], [102, 21, 86, 21], [102, 24, 86, 24], [102, 28, 86, 24, "getDefaultHeaderHeight"], [102, 74, 86, 46], [102, 76, 86, 47, "layout"], [102, 82, 86, 53], [102, 84, 86, 55, "modal"], [102, 89, 86, 60], [102, 91, 86, 62, "headerStatusBarHeight"], [102, 112, 86, 83], [102, 113, 86, 84], [103, 4, 87, 2], [103, 8, 87, 2, "_StyleSheet$flatten"], [103, 27, 87, 2], [103, 30, 125, 6, "StyleSheet"], [103, 53, 125, 16], [103, 54, 125, 17, "flatten"], [103, 61, 125, 24], [103, 62, 125, 25, "customHeaderStyle"], [103, 79, 125, 42], [103, 83, 125, 46], [103, 84, 125, 47], [103, 85, 125, 48], [103, 86, 125, 49], [104, 6, 125, 49, "_StyleSheet$flatten$h"], [104, 27, 125, 49], [104, 30, 125, 49, "_StyleSheet$flatten"], [104, 49, 125, 49], [104, 50, 88, 4, "height"], [104, 56, 88, 10], [105, 6, 88, 4, "height"], [105, 12, 88, 10], [105, 15, 88, 10, "_StyleSheet$flatten$h"], [105, 36, 88, 10], [105, 50, 88, 13, "defaultHeight"], [105, 63, 88, 26], [105, 66, 88, 26, "_StyleSheet$flatten$h"], [105, 87, 88, 26], [106, 6, 89, 4, "minHeight"], [106, 15, 89, 13], [106, 18, 89, 13, "_StyleSheet$flatten"], [106, 37, 89, 13], [106, 38, 89, 4, "minHeight"], [106, 47, 89, 13], [107, 6, 90, 4, "maxHeight"], [107, 15, 90, 13], [107, 18, 90, 13, "_StyleSheet$flatten"], [107, 37, 90, 13], [107, 38, 90, 4, "maxHeight"], [107, 47, 90, 13], [108, 6, 91, 4, "backgroundColor"], [108, 21, 91, 19], [108, 24, 91, 19, "_StyleSheet$flatten"], [108, 43, 91, 19], [108, 44, 91, 4, "backgroundColor"], [108, 59, 91, 19], [109, 6, 92, 4, "borderBottomColor"], [109, 23, 92, 21], [109, 26, 92, 21, "_StyleSheet$flatten"], [109, 45, 92, 21], [109, 46, 92, 4, "borderBottomColor"], [109, 63, 92, 21], [110, 6, 93, 4, "borderBottomEndRadius"], [110, 27, 93, 25], [110, 30, 93, 25, "_StyleSheet$flatten"], [110, 49, 93, 25], [110, 50, 93, 4, "borderBottomEndRadius"], [110, 71, 93, 25], [111, 6, 94, 4, "borderBottomLeftRadius"], [111, 28, 94, 26], [111, 31, 94, 26, "_StyleSheet$flatten"], [111, 50, 94, 26], [111, 51, 94, 4, "borderBottomLeftRadius"], [111, 73, 94, 26], [112, 6, 95, 4, "borderBottomRightRadius"], [112, 29, 95, 27], [112, 32, 95, 27, "_StyleSheet$flatten"], [112, 51, 95, 27], [112, 52, 95, 4, "borderBottomRightRadius"], [112, 75, 95, 27], [113, 6, 96, 4, "borderBottomStartRadius"], [113, 29, 96, 27], [113, 32, 96, 27, "_StyleSheet$flatten"], [113, 51, 96, 27], [113, 52, 96, 4, "borderBottomStartRadius"], [113, 75, 96, 27], [114, 6, 97, 4, "borderBottomWidth"], [114, 23, 97, 21], [114, 26, 97, 21, "_StyleSheet$flatten"], [114, 45, 97, 21], [114, 46, 97, 4, "borderBottomWidth"], [114, 63, 97, 21], [115, 6, 98, 4, "borderColor"], [115, 17, 98, 15], [115, 20, 98, 15, "_StyleSheet$flatten"], [115, 39, 98, 15], [115, 40, 98, 4, "borderColor"], [115, 51, 98, 15], [116, 6, 99, 4, "borderEndColor"], [116, 20, 99, 18], [116, 23, 99, 18, "_StyleSheet$flatten"], [116, 42, 99, 18], [116, 43, 99, 4, "borderEndColor"], [116, 57, 99, 18], [117, 6, 100, 4, "borderEndWidth"], [117, 20, 100, 18], [117, 23, 100, 18, "_StyleSheet$flatten"], [117, 42, 100, 18], [117, 43, 100, 4, "borderEndWidth"], [117, 57, 100, 18], [118, 6, 101, 4, "borderLeftColor"], [118, 21, 101, 19], [118, 24, 101, 19, "_StyleSheet$flatten"], [118, 43, 101, 19], [118, 44, 101, 4, "borderLeftColor"], [118, 59, 101, 19], [119, 6, 102, 4, "borderLeftWidth"], [119, 21, 102, 19], [119, 24, 102, 19, "_StyleSheet$flatten"], [119, 43, 102, 19], [119, 44, 102, 4, "borderLeftWidth"], [119, 59, 102, 19], [120, 6, 103, 4, "borderRadius"], [120, 18, 103, 16], [120, 21, 103, 16, "_StyleSheet$flatten"], [120, 40, 103, 16], [120, 41, 103, 4, "borderRadius"], [120, 53, 103, 16], [121, 6, 104, 4, "borderRightColor"], [121, 22, 104, 20], [121, 25, 104, 20, "_StyleSheet$flatten"], [121, 44, 104, 20], [121, 45, 104, 4, "borderRightColor"], [121, 61, 104, 20], [122, 6, 105, 4, "borderRightWidth"], [122, 22, 105, 20], [122, 25, 105, 20, "_StyleSheet$flatten"], [122, 44, 105, 20], [122, 45, 105, 4, "borderRightWidth"], [122, 61, 105, 20], [123, 6, 106, 4, "borderStartColor"], [123, 22, 106, 20], [123, 25, 106, 20, "_StyleSheet$flatten"], [123, 44, 106, 20], [123, 45, 106, 4, "borderStartColor"], [123, 61, 106, 20], [124, 6, 107, 4, "borderStartWidth"], [124, 22, 107, 20], [124, 25, 107, 20, "_StyleSheet$flatten"], [124, 44, 107, 20], [124, 45, 107, 4, "borderStartWidth"], [124, 61, 107, 20], [125, 6, 108, 4, "borderStyle"], [125, 17, 108, 15], [125, 20, 108, 15, "_StyleSheet$flatten"], [125, 39, 108, 15], [125, 40, 108, 4, "borderStyle"], [125, 51, 108, 15], [126, 6, 109, 4, "borderTopColor"], [126, 20, 109, 18], [126, 23, 109, 18, "_StyleSheet$flatten"], [126, 42, 109, 18], [126, 43, 109, 4, "borderTopColor"], [126, 57, 109, 18], [127, 6, 110, 4, "borderTopEndRadius"], [127, 24, 110, 22], [127, 27, 110, 22, "_StyleSheet$flatten"], [127, 46, 110, 22], [127, 47, 110, 4, "borderTopEndRadius"], [127, 65, 110, 22], [128, 6, 111, 4, "borderTopLeftRadius"], [128, 25, 111, 23], [128, 28, 111, 23, "_StyleSheet$flatten"], [128, 47, 111, 23], [128, 48, 111, 4, "borderTopLeftRadius"], [128, 67, 111, 23], [129, 6, 112, 4, "borderTopRightRadius"], [129, 26, 112, 24], [129, 29, 112, 24, "_StyleSheet$flatten"], [129, 48, 112, 24], [129, 49, 112, 4, "borderTopRightRadius"], [129, 69, 112, 24], [130, 6, 113, 4, "borderTopStartRadius"], [130, 26, 113, 24], [130, 29, 113, 24, "_StyleSheet$flatten"], [130, 48, 113, 24], [130, 49, 113, 4, "borderTopStartRadius"], [130, 69, 113, 24], [131, 6, 114, 4, "borderTopWidth"], [131, 20, 114, 18], [131, 23, 114, 18, "_StyleSheet$flatten"], [131, 42, 114, 18], [131, 43, 114, 4, "borderTopWidth"], [131, 57, 114, 18], [132, 6, 115, 4, "borderWidth"], [132, 17, 115, 15], [132, 20, 115, 15, "_StyleSheet$flatten"], [132, 39, 115, 15], [132, 40, 115, 4, "borderWidth"], [132, 51, 115, 15], [133, 6, 116, 4, "boxShadow"], [133, 15, 116, 13], [133, 18, 116, 13, "_StyleSheet$flatten"], [133, 37, 116, 13], [133, 38, 116, 4, "boxShadow"], [133, 47, 116, 13], [134, 6, 117, 4, "elevation"], [134, 15, 117, 13], [134, 18, 117, 13, "_StyleSheet$flatten"], [134, 37, 117, 13], [134, 38, 117, 4, "elevation"], [134, 47, 117, 13], [135, 6, 118, 4, "shadowColor"], [135, 17, 118, 15], [135, 20, 118, 15, "_StyleSheet$flatten"], [135, 39, 118, 15], [135, 40, 118, 4, "shadowColor"], [135, 51, 118, 15], [136, 6, 119, 4, "shadowOffset"], [136, 18, 119, 16], [136, 21, 119, 16, "_StyleSheet$flatten"], [136, 40, 119, 16], [136, 41, 119, 4, "shadowOffset"], [136, 53, 119, 16], [137, 6, 120, 4, "shadowOpacity"], [137, 19, 120, 17], [137, 22, 120, 17, "_StyleSheet$flatten"], [137, 41, 120, 17], [137, 42, 120, 4, "shadowOpacity"], [137, 55, 120, 17], [138, 6, 121, 4, "shadowRadius"], [138, 18, 121, 16], [138, 21, 121, 16, "_StyleSheet$flatten"], [138, 40, 121, 16], [138, 41, 121, 4, "shadowRadius"], [138, 53, 121, 16], [139, 6, 122, 4, "opacity"], [139, 13, 122, 11], [139, 16, 122, 11, "_StyleSheet$flatten"], [139, 35, 122, 11], [139, 36, 122, 4, "opacity"], [139, 43, 122, 11], [140, 6, 123, 4, "transform"], [140, 15, 123, 13], [140, 18, 123, 13, "_StyleSheet$flatten"], [140, 37, 123, 13], [140, 38, 123, 4, "transform"], [140, 47, 123, 13], [141, 6, 124, 7, "unsafeStyles"], [141, 18, 124, 19], [141, 25, 124, 19, "_objectWithoutProperties2"], [141, 50, 124, 19], [141, 51, 124, 19, "default"], [141, 58, 124, 19], [141, 60, 124, 19, "_StyleSheet$flatten"], [141, 79, 124, 19], [141, 81, 124, 19, "_excluded"], [141, 90, 124, 19], [142, 4, 126, 2], [142, 8, 126, 6, "process"], [142, 15, 126, 13], [142, 16, 126, 14, "env"], [142, 19, 126, 17], [142, 20, 126, 18, "NODE_ENV"], [142, 28, 126, 26], [142, 33, 126, 31], [142, 45, 126, 43], [142, 47, 126, 45], [143, 6, 127, 4, "warnIfHeaderStylesDefined"], [143, 31, 127, 29], [143, 32, 127, 30, "unsafeStyles"], [143, 44, 127, 42], [143, 45, 127, 43], [144, 4, 128, 2], [145, 4, 129, 2], [145, 8, 129, 8, "safeStyles"], [145, 18, 129, 18], [145, 21, 129, 21], [146, 6, 130, 4, "backgroundColor"], [146, 21, 130, 19], [147, 6, 131, 4, "borderBottomColor"], [147, 23, 131, 21], [148, 6, 132, 4, "borderBottomEndRadius"], [148, 27, 132, 25], [149, 6, 133, 4, "borderBottomLeftRadius"], [149, 28, 133, 26], [150, 6, 134, 4, "borderBottomRightRadius"], [150, 29, 134, 27], [151, 6, 135, 4, "borderBottomStartRadius"], [151, 29, 135, 27], [152, 6, 136, 4, "borderBottomWidth"], [152, 23, 136, 21], [153, 6, 137, 4, "borderColor"], [153, 17, 137, 15], [154, 6, 138, 4, "borderEndColor"], [154, 20, 138, 18], [155, 6, 139, 4, "borderEndWidth"], [155, 20, 139, 18], [156, 6, 140, 4, "borderLeftColor"], [156, 21, 140, 19], [157, 6, 141, 4, "borderLeftWidth"], [157, 21, 141, 19], [158, 6, 142, 4, "borderRadius"], [158, 18, 142, 16], [159, 6, 143, 4, "borderRightColor"], [159, 22, 143, 20], [160, 6, 144, 4, "borderRightWidth"], [160, 22, 144, 20], [161, 6, 145, 4, "borderStartColor"], [161, 22, 145, 20], [162, 6, 146, 4, "borderStartWidth"], [162, 22, 146, 20], [163, 6, 147, 4, "borderStyle"], [163, 17, 147, 15], [164, 6, 148, 4, "borderTopColor"], [164, 20, 148, 18], [165, 6, 149, 4, "borderTopEndRadius"], [165, 24, 149, 22], [166, 6, 150, 4, "borderTopLeftRadius"], [166, 25, 150, 23], [167, 6, 151, 4, "borderTopRightRadius"], [167, 26, 151, 24], [168, 6, 152, 4, "borderTopStartRadius"], [168, 26, 152, 24], [169, 6, 153, 4, "borderTopWidth"], [169, 20, 153, 18], [170, 6, 154, 4, "borderWidth"], [170, 17, 154, 15], [171, 6, 155, 4, "boxShadow"], [171, 15, 155, 13], [172, 6, 156, 4, "elevation"], [172, 15, 156, 13], [173, 6, 157, 4, "shadowColor"], [173, 17, 157, 15], [174, 6, 158, 4, "shadowOffset"], [174, 18, 158, 16], [175, 6, 159, 4, "shadowOpacity"], [175, 19, 159, 17], [176, 6, 160, 4, "shadowRadius"], [176, 18, 160, 16], [177, 6, 161, 4, "opacity"], [177, 13, 161, 11], [178, 6, 162, 4, "transform"], [179, 4, 163, 2], [179, 5, 163, 3], [181, 4, 165, 2], [182, 4, 166, 2], [183, 4, 167, 2], [184, 4, 168, 2], [184, 9, 168, 7], [184, 13, 168, 13, "styleProp"], [184, 22, 168, 22], [184, 26, 168, 26, "safeStyles"], [184, 36, 168, 36], [184, 38, 168, 38], [185, 6, 169, 4], [186, 6, 170, 4], [186, 10, 170, 8, "safeStyles"], [186, 20, 170, 18], [186, 21, 170, 19, "styleProp"], [186, 30, 170, 28], [186, 31, 170, 29], [186, 36, 170, 34, "undefined"], [186, 45, 170, 43], [186, 47, 170, 45], [187, 8, 171, 6], [188, 8, 172, 6], [189, 8, 173, 6], [189, 15, 173, 13, "safeStyles"], [189, 25, 173, 23], [189, 26, 173, 24, "styleProp"], [189, 35, 173, 33], [189, 36, 173, 34], [190, 6, 174, 4], [191, 4, 175, 2], [192, 4, 176, 2], [192, 8, 176, 8, "backgroundStyle"], [192, 23, 176, 23], [192, 26, 176, 26], [193, 6, 177, 4], [193, 10, 177, 8, "headerTransparent"], [193, 27, 177, 25], [193, 31, 177, 29], [194, 8, 178, 6, "backgroundColor"], [194, 23, 178, 21], [194, 25, 178, 23], [195, 6, 179, 4], [195, 7, 179, 5], [195, 8, 179, 6], [196, 6, 180, 4], [196, 10, 180, 8], [196, 11, 180, 9, "headerTransparent"], [196, 28, 180, 26], [196, 32, 180, 30, "headerShadowVisible"], [196, 51, 180, 49], [196, 56, 180, 54], [196, 61, 180, 59], [196, 66, 180, 64], [197, 8, 181, 6, "borderBottomWidth"], [197, 25, 181, 23], [197, 27, 181, 25], [197, 28, 181, 26], [198, 8, 182, 6], [198, 11, 182, 9, "Platform"], [198, 32, 182, 17], [198, 33, 182, 18, "select"], [198, 39, 182, 24], [198, 40, 182, 25], [199, 10, 183, 8, "android"], [199, 17, 183, 15], [199, 19, 183, 17], [200, 12, 184, 10, "elevation"], [200, 21, 184, 19], [200, 23, 184, 21], [201, 10, 185, 8], [201, 11, 185, 9], [202, 10, 186, 8, "web"], [202, 13, 186, 11], [202, 15, 186, 13], [203, 12, 187, 10, "boxShadow"], [203, 21, 187, 19], [203, 23, 187, 21], [204, 10, 188, 8], [204, 11, 188, 9], [205, 10, 189, 8, "default"], [205, 17, 189, 15], [205, 19, 189, 17], [206, 12, 190, 10, "shadowOpacity"], [206, 25, 190, 23], [206, 27, 190, 25], [207, 10, 191, 8], [208, 8, 192, 6], [208, 9, 192, 7], [209, 6, 193, 4], [209, 7, 193, 5], [209, 8, 193, 6], [210, 6, 194, 4], [210, 9, 194, 7, "safeStyles"], [211, 4, 195, 2], [211, 5, 195, 3], [212, 4, 196, 2], [212, 8, 196, 8, "iconTintColor"], [212, 21, 196, 21], [212, 24, 196, 24, "headerTintColor"], [212, 39, 196, 39], [212, 43, 196, 43, "Platform"], [212, 64, 196, 51], [212, 65, 196, 52, "select"], [212, 71, 196, 58], [212, 72, 196, 59], [213, 6, 197, 4, "ios"], [213, 9, 197, 7], [213, 11, 197, 9, "colors"], [213, 17, 197, 15], [213, 18, 197, 16, "primary"], [213, 25, 197, 23], [214, 6, 198, 4, "default"], [214, 13, 198, 11], [214, 15, 198, 13, "colors"], [214, 21, 198, 19], [214, 22, 198, 20, "text"], [215, 4, 199, 2], [215, 5, 199, 3], [215, 6, 199, 4], [216, 4, 200, 2], [216, 8, 200, 8, "leftButton"], [216, 18, 200, 18], [216, 21, 200, 21, "headerLeft"], [216, 31, 200, 31], [216, 34, 200, 34, "headerLeft"], [216, 44, 200, 44], [216, 45, 200, 45], [217, 6, 201, 4, "tintColor"], [217, 15, 201, 13], [217, 17, 201, 15, "iconTintColor"], [217, 30, 201, 28], [218, 6, 202, 4, "pressColor"], [218, 16, 202, 14], [218, 18, 202, 16, "headerPressColor"], [218, 34, 202, 32], [219, 6, 203, 4, "pressOpacity"], [219, 18, 203, 16], [219, 20, 203, 18, "headerPressOpacity"], [219, 38, 203, 36], [220, 6, 204, 4, "displayMode"], [220, 17, 204, 15], [220, 19, 204, 17, "headerBackButtonDisplayMode"], [220, 46, 204, 44], [221, 6, 205, 4, "titleLayout"], [221, 17, 205, 15], [222, 6, 206, 4, "screenLayout"], [222, 18, 206, 16], [222, 20, 206, 18, "layout"], [222, 26, 206, 24], [223, 6, 207, 4, "canGoBack"], [223, 15, 207, 13], [223, 17, 207, 15, "Boolean"], [223, 24, 207, 22], [223, 25, 207, 23, "back"], [223, 29, 207, 27], [223, 30, 207, 28], [224, 6, 208, 4, "onPress"], [224, 13, 208, 11], [224, 15, 208, 13, "back"], [224, 19, 208, 17], [224, 22, 208, 20, "navigation"], [224, 32, 208, 30], [224, 33, 208, 31, "goBack"], [224, 39, 208, 37], [224, 42, 208, 40, "undefined"], [224, 51, 208, 49], [225, 6, 209, 4, "label"], [225, 11, 209, 9], [225, 13, 209, 11, "back"], [225, 17, 209, 15], [225, 19, 209, 17, "title"], [225, 24, 209, 22], [226, 6, 210, 4, "labelStyle"], [226, 16, 210, 14], [226, 18, 210, 16, "headerBackTitleStyle"], [226, 38, 210, 36], [227, 6, 211, 4, "href"], [227, 10, 211, 8], [227, 12, 211, 10, "back"], [227, 16, 211, 14], [227, 18, 211, 16, "href"], [228, 4, 212, 2], [228, 5, 212, 3], [228, 6, 212, 4], [228, 9, 212, 7], [228, 13, 212, 11], [229, 4, 213, 2], [229, 8, 213, 8, "rightB<PERSON>on"], [229, 19, 213, 19], [229, 22, 213, 22, "headerRight"], [229, 33, 213, 33], [229, 36, 213, 36, "headerRight"], [229, 47, 213, 47], [229, 48, 213, 48], [230, 6, 214, 4, "tintColor"], [230, 15, 214, 13], [230, 17, 214, 15, "iconTintColor"], [230, 30, 214, 28], [231, 6, 215, 4, "pressColor"], [231, 16, 215, 14], [231, 18, 215, 16, "headerPressColor"], [231, 34, 215, 32], [232, 6, 216, 4, "pressOpacity"], [232, 18, 216, 16], [232, 20, 216, 18, "headerPressOpacity"], [232, 38, 216, 36], [233, 6, 217, 4, "canGoBack"], [233, 15, 217, 13], [233, 17, 217, 15, "Boolean"], [233, 24, 217, 22], [233, 25, 217, 23, "back"], [233, 29, 217, 27], [234, 4, 218, 2], [234, 5, 218, 3], [234, 6, 218, 4], [234, 9, 218, 7], [234, 13, 218, 11], [235, 4, 219, 2], [235, 8, 219, 8, "headerTitle"], [235, 19, 219, 19], [235, 22, 219, 22], [235, 29, 219, 29, "customTitle"], [235, 40, 219, 40], [235, 45, 219, 45], [235, 55, 219, 55], [235, 58, 219, 58, "props"], [235, 63, 219, 63], [235, 67, 219, 67], [235, 80, 219, 80], [235, 84, 219, 80, "_jsx"], [235, 99, 219, 84], [235, 101, 219, 85, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [235, 125, 219, 96], [235, 127, 219, 98], [236, 6, 220, 4], [236, 9, 220, 7, "props"], [237, 4, 221, 2], [237, 5, 221, 3], [237, 6, 221, 4], [237, 9, 221, 7, "customTitle"], [237, 20, 221, 18], [238, 4, 222, 2], [238, 11, 222, 9], [238, 24, 222, 22], [238, 28, 222, 22, "_jsxs"], [238, 44, 222, 27], [238, 46, 222, 28, "Animated"], [238, 67, 222, 36], [238, 68, 222, 37, "View"], [238, 72, 222, 41], [238, 74, 222, 43], [239, 6, 223, 4, "pointerEvents"], [239, 19, 223, 17], [239, 21, 223, 19], [239, 31, 223, 29], [240, 6, 224, 4, "style"], [240, 11, 224, 9], [240, 13, 224, 11], [240, 14, 224, 12], [241, 8, 225, 6, "height"], [241, 14, 225, 12], [242, 8, 226, 6, "minHeight"], [242, 17, 226, 15], [243, 8, 227, 6, "maxHeight"], [243, 17, 227, 15], [244, 8, 228, 6, "opacity"], [244, 15, 228, 13], [245, 8, 229, 6, "transform"], [246, 6, 230, 4], [246, 7, 230, 5], [246, 8, 230, 6], [247, 6, 231, 4, "children"], [247, 14, 231, 12], [247, 16, 231, 14], [247, 17, 231, 15], [247, 30, 231, 28], [247, 34, 231, 28, "_jsx"], [247, 49, 231, 32], [247, 51, 231, 33, "Animated"], [247, 72, 231, 41], [247, 73, 231, 42, "View"], [247, 77, 231, 46], [247, 79, 231, 48], [248, 8, 232, 6, "pointerEvents"], [248, 21, 232, 19], [248, 23, 232, 21], [248, 33, 232, 31], [249, 8, 233, 6, "style"], [249, 13, 233, 11], [249, 15, 233, 13], [249, 16, 233, 14, "StyleSheet"], [249, 39, 233, 24], [249, 40, 233, 25, "absoluteFill"], [249, 52, 233, 37], [249, 54, 233, 39, "backgroundContainerStyle"], [249, 78, 233, 63], [249, 79, 233, 64], [250, 8, 234, 6, "children"], [250, 16, 234, 14], [250, 18, 234, 16, "headerBackground"], [250, 34, 234, 32], [250, 37, 234, 35, "headerBackground"], [250, 53, 234, 51], [250, 54, 234, 52], [251, 10, 235, 8, "style"], [251, 15, 235, 13], [251, 17, 235, 15, "backgroundStyle"], [252, 8, 236, 6], [252, 9, 236, 7], [252, 10, 236, 8], [252, 13, 236, 11], [252, 26, 236, 24], [252, 30, 236, 24, "_jsx"], [252, 45, 236, 28], [252, 47, 236, 29, "HeaderBackground"], [252, 81, 236, 45], [252, 83, 236, 47], [253, 10, 237, 8, "pointerEvents"], [253, 23, 237, 21], [254, 10, 238, 8], [255, 10, 239, 8, "headerTransparent"], [255, 27, 239, 25], [255, 32, 239, 30, "backgroundStyle"], [255, 47, 239, 45], [255, 48, 239, 46, "backgroundColor"], [255, 63, 239, 61], [255, 68, 239, 66], [255, 81, 239, 79], [255, 85, 239, 83], [255, 89, 239, 83, "Color"], [255, 103, 239, 88], [255, 105, 239, 89, "backgroundStyle"], [255, 120, 239, 104], [255, 121, 239, 105, "backgroundColor"], [255, 136, 239, 120], [255, 137, 239, 121], [255, 138, 239, 122, "alpha"], [255, 143, 239, 127], [255, 144, 239, 128], [255, 145, 239, 129], [255, 150, 239, 134], [255, 151, 239, 135], [255, 152, 239, 136], [255, 155, 239, 139], [255, 161, 239, 145], [255, 164, 239, 148], [255, 170, 239, 154], [256, 10, 240, 8, "style"], [256, 15, 240, 13], [256, 17, 240, 15, "backgroundStyle"], [257, 8, 241, 6], [257, 9, 241, 7], [258, 6, 242, 4], [258, 7, 242, 5], [258, 8, 242, 6], [258, 10, 242, 8], [258, 23, 242, 21], [258, 27, 242, 21, "_jsx"], [258, 42, 242, 25], [258, 44, 242, 26, "View"], [258, 61, 242, 30], [258, 63, 242, 32], [259, 8, 243, 6, "pointerEvents"], [259, 21, 243, 19], [259, 23, 243, 21], [259, 29, 243, 27], [260, 8, 244, 6, "style"], [260, 13, 244, 11], [260, 15, 244, 13], [261, 10, 245, 8, "height"], [261, 16, 245, 14], [261, 18, 245, 16, "headerStatusBarHeight"], [262, 8, 246, 6], [263, 6, 247, 4], [263, 7, 247, 5], [263, 8, 247, 6], [263, 10, 247, 8], [263, 23, 247, 21], [263, 27, 247, 21, "_jsxs"], [263, 43, 247, 26], [263, 45, 247, 27, "View"], [263, 62, 247, 31], [263, 64, 247, 33], [264, 8, 248, 6, "pointerEvents"], [264, 21, 248, 19], [264, 23, 248, 21], [264, 33, 248, 31], [265, 8, 249, 6, "style"], [265, 13, 249, 11], [265, 15, 249, 13], [265, 16, 249, 14, "styles"], [265, 22, 249, 20], [265, 23, 249, 21, "content"], [265, 30, 249, 28], [265, 32, 249, 30, "Platform"], [265, 53, 249, 38], [265, 54, 249, 39, "OS"], [265, 56, 249, 41], [265, 61, 249, 46], [265, 66, 249, 51], [265, 70, 249, 55, "frame"], [265, 75, 249, 60], [265, 76, 249, 61, "width"], [265, 81, 249, 66], [265, 85, 249, 70, "IPAD_MINI_MEDIUM_WIDTH"], [265, 107, 249, 92], [265, 110, 249, 95, "styles"], [265, 116, 249, 101], [265, 117, 249, 102, "large"], [265, 122, 249, 107], [265, 125, 249, 110], [265, 129, 249, 114], [265, 130, 249, 115], [266, 8, 250, 6, "children"], [266, 16, 250, 14], [266, 18, 250, 16], [266, 19, 250, 17], [266, 32, 250, 30], [266, 36, 250, 30, "_jsx"], [266, 51, 250, 34], [266, 53, 250, 35, "Animated"], [266, 74, 250, 43], [266, 75, 250, 44, "View"], [266, 79, 250, 48], [266, 81, 250, 50], [267, 10, 251, 8, "pointerEvents"], [267, 23, 251, 21], [267, 25, 251, 23], [267, 35, 251, 33], [268, 10, 252, 8, "style"], [268, 15, 252, 13], [268, 17, 252, 15], [268, 18, 252, 16, "styles"], [268, 24, 252, 22], [268, 25, 252, 23, "start"], [268, 30, 252, 28], [268, 32, 252, 30], [268, 33, 252, 31, "searchBarVisible"], [268, 49, 252, 47], [268, 53, 252, 51, "headerTitleAlign"], [268, 69, 252, 67], [268, 74, 252, 72], [268, 82, 252, 80], [268, 86, 252, 84, "styles"], [268, 92, 252, 90], [268, 93, 252, 91, "expand"], [268, 99, 252, 97], [268, 101, 252, 99], [269, 12, 253, 10, "marginStart"], [269, 23, 253, 21], [269, 25, 253, 23, "insets"], [269, 31, 253, 29], [269, 32, 253, 30, "left"], [270, 10, 254, 8], [270, 11, 254, 9], [270, 13, 254, 11, "leftContainerStyle"], [270, 31, 254, 29], [270, 32, 254, 30], [271, 10, 255, 8, "children"], [271, 18, 255, 16], [271, 20, 255, 18, "leftButton"], [272, 8, 256, 6], [272, 9, 256, 7], [272, 10, 256, 8], [272, 12, 256, 10, "Platform"], [272, 33, 256, 18], [272, 34, 256, 19, "OS"], [272, 36, 256, 21], [272, 41, 256, 26], [272, 46, 256, 31], [272, 50, 256, 35], [272, 51, 256, 36, "searchBarVisible"], [272, 67, 256, 52], [272, 70, 256, 55], [272, 83, 256, 68], [272, 87, 256, 68, "_jsxs"], [272, 103, 256, 73], [272, 105, 256, 74, "_Fragment"], [272, 125, 256, 83], [272, 127, 256, 85], [273, 10, 257, 8, "children"], [273, 18, 257, 16], [273, 20, 257, 18], [273, 21, 257, 19], [273, 34, 257, 32], [273, 38, 257, 32, "_jsx"], [273, 53, 257, 36], [273, 55, 257, 37, "Animated"], [273, 76, 257, 45], [273, 77, 257, 46, "View"], [273, 81, 257, 50], [273, 83, 257, 52], [274, 12, 258, 10, "pointerEvents"], [274, 25, 258, 23], [274, 27, 258, 25], [274, 37, 258, 35], [275, 12, 259, 10, "style"], [275, 17, 259, 15], [275, 19, 259, 17], [275, 20, 259, 18, "styles"], [275, 26, 259, 24], [275, 27, 259, 25, "title"], [275, 32, 259, 30], [275, 34, 259, 32], [276, 14, 260, 12], [277, 14, 261, 12, "max<PERSON><PERSON><PERSON>"], [277, 22, 261, 20], [277, 24, 261, 22, "headerTitleAlign"], [277, 40, 261, 38], [277, 45, 261, 43], [277, 53, 261, 51], [277, 56, 261, 54, "layout"], [277, 62, 261, 60], [277, 63, 261, 61, "width"], [277, 68, 261, 66], [277, 71, 261, 69], [277, 72, 261, 70], [277, 73, 261, 71, "leftButton"], [277, 83, 261, 81], [277, 86, 261, 84, "headerBackButtonDisplayMode"], [277, 113, 261, 111], [277, 118, 261, 116], [277, 127, 261, 125], [277, 130, 261, 128], [277, 132, 261, 130], [277, 135, 261, 133], [277, 137, 261, 135], [277, 140, 261, 138], [277, 142, 261, 140], [277, 147, 261, 145, "rightB<PERSON>on"], [277, 158, 261, 156], [277, 162, 261, 160, "headerSearchBarOptions"], [277, 184, 261, 182], [277, 187, 261, 185], [277, 189, 261, 187], [277, 192, 261, 190], [277, 193, 261, 191], [277, 194, 261, 192], [277, 197, 261, 195, "Math"], [277, 201, 261, 199], [277, 202, 261, 200, "max"], [277, 205, 261, 203], [277, 206, 261, 204, "insets"], [277, 212, 261, 210], [277, 213, 261, 211, "left"], [277, 217, 261, 215], [277, 219, 261, 217, "insets"], [277, 225, 261, 223], [277, 226, 261, 224, "right"], [277, 231, 261, 229], [277, 232, 261, 230], [277, 236, 261, 234], [277, 237, 261, 235], [277, 240, 261, 238, "layout"], [277, 246, 261, 244], [277, 247, 261, 245, "width"], [277, 252, 261, 250], [277, 256, 261, 254], [277, 257, 261, 255, "leftButton"], [277, 267, 261, 265], [277, 270, 261, 268], [277, 272, 261, 270], [277, 275, 261, 273], [277, 277, 261, 275], [277, 282, 261, 280, "rightB<PERSON>on"], [277, 293, 261, 291], [277, 297, 261, 295, "headerSearchBarOptions"], [277, 319, 261, 317], [277, 322, 261, 320], [277, 324, 261, 322], [277, 327, 261, 325], [277, 329, 261, 327], [277, 330, 261, 328], [277, 333, 261, 331, "insets"], [277, 339, 261, 337], [277, 340, 261, 338, "left"], [277, 344, 261, 342], [277, 347, 261, 345, "insets"], [277, 353, 261, 351], [277, 354, 261, 352, "right"], [277, 359, 261, 357], [278, 12, 262, 10], [278, 13, 262, 11], [278, 15, 262, 13, "headerTitleAlign"], [278, 31, 262, 29], [278, 36, 262, 34], [278, 42, 262, 40], [278, 46, 262, 44, "leftButton"], [278, 56, 262, 54], [278, 59, 262, 57], [279, 14, 263, 12, "marginStart"], [279, 25, 263, 23], [279, 27, 263, 25], [280, 12, 264, 10], [280, 13, 264, 11], [280, 16, 264, 14], [281, 14, 265, 12, "marginHorizontal"], [281, 30, 265, 28], [281, 32, 265, 30], [282, 12, 266, 10], [282, 13, 266, 11], [282, 15, 266, 13, "titleContainerStyle"], [282, 34, 266, 32], [282, 35, 266, 33], [283, 12, 267, 10, "children"], [283, 20, 267, 18], [283, 22, 267, 20, "headerTitle"], [283, 33, 267, 31], [283, 34, 267, 32], [284, 14, 268, 12, "children"], [284, 22, 268, 20], [284, 24, 268, 22, "title"], [284, 29, 268, 27], [285, 14, 269, 12, "allowFontScaling"], [285, 30, 269, 28], [285, 32, 269, 30, "titleAllowFontScaling"], [285, 53, 269, 51], [286, 14, 270, 12, "tintColor"], [286, 23, 270, 21], [286, 25, 270, 23, "headerTintColor"], [286, 40, 270, 38], [287, 14, 271, 12, "onLayout"], [287, 22, 271, 20], [287, 24, 271, 22, "onTitleLayout"], [287, 37, 271, 35], [288, 14, 272, 12, "style"], [288, 19, 272, 17], [288, 21, 272, 19, "titleStyle"], [289, 12, 273, 10], [289, 13, 273, 11], [290, 10, 274, 8], [290, 11, 274, 9], [290, 12, 274, 10], [290, 14, 274, 12], [290, 27, 274, 25], [290, 31, 274, 25, "_jsxs"], [290, 47, 274, 30], [290, 49, 274, 31, "Animated"], [290, 70, 274, 39], [290, 71, 274, 40, "View"], [290, 75, 274, 44], [290, 77, 274, 46], [291, 12, 275, 10, "pointerEvents"], [291, 25, 275, 23], [291, 27, 275, 25], [291, 37, 275, 35], [292, 12, 276, 10, "style"], [292, 17, 276, 15], [292, 19, 276, 17], [292, 20, 276, 18, "styles"], [292, 26, 276, 24], [292, 27, 276, 25, "end"], [292, 30, 276, 28], [292, 32, 276, 30, "styles"], [292, 38, 276, 36], [292, 39, 276, 37, "expand"], [292, 45, 276, 43], [292, 47, 276, 45], [293, 14, 277, 12, "marginEnd"], [293, 23, 277, 21], [293, 25, 277, 23, "insets"], [293, 31, 277, 29], [293, 32, 277, 30, "right"], [294, 12, 278, 10], [294, 13, 278, 11], [294, 15, 278, 13, "rightContainerStyle"], [294, 34, 278, 32], [294, 35, 278, 33], [295, 12, 279, 10, "children"], [295, 20, 279, 18], [295, 22, 279, 20], [295, 23, 279, 21, "rightB<PERSON>on"], [295, 34, 279, 32], [295, 36, 279, 34, "headerSearchBarOptions"], [295, 58, 279, 56], [295, 61, 279, 59], [295, 74, 279, 72], [295, 78, 279, 72, "_jsx"], [295, 93, 279, 76], [295, 95, 279, 77, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [295, 121, 279, 89], [295, 123, 279, 91], [296, 14, 280, 12, "tintColor"], [296, 23, 280, 21], [296, 25, 280, 23, "iconTintColor"], [296, 38, 280, 36], [297, 14, 281, 12, "pressColor"], [297, 24, 281, 22], [297, 26, 281, 24, "headerPressColor"], [297, 42, 281, 40], [298, 14, 282, 12, "pressOpacity"], [298, 26, 282, 24], [298, 28, 282, 26, "headerPressOpacity"], [298, 46, 282, 44], [299, 14, 283, 12, "onPress"], [299, 21, 283, 19], [299, 23, 283, 21, "onPress"], [299, 24, 283, 21], [299, 29, 283, 27], [300, 16, 284, 14, "setSearchBarVisible"], [300, 35, 284, 33], [300, 36, 284, 34], [300, 40, 284, 38], [300, 41, 284, 39], [301, 16, 285, 14, "headerSearchBarOptions"], [301, 38, 285, 36], [301, 40, 285, 38, "onOpen"], [301, 46, 285, 44], [301, 49, 285, 47], [301, 50, 285, 48], [302, 14, 286, 12], [302, 15, 286, 13], [303, 14, 287, 12, "children"], [303, 22, 287, 20], [303, 24, 287, 22], [303, 37, 287, 35], [303, 41, 287, 35, "_jsx"], [303, 56, 287, 39], [303, 58, 287, 40, "HeaderIcon"], [303, 80, 287, 50], [303, 82, 287, 52], [304, 16, 288, 14, "source"], [304, 22, 288, 20], [304, 24, 288, 22, "searchIcon"], [304, 43, 288, 32], [305, 16, 289, 14, "tintColor"], [305, 25, 289, 23], [305, 27, 289, 25, "iconTintColor"], [306, 14, 290, 12], [306, 15, 290, 13], [307, 12, 291, 10], [307, 13, 291, 11], [307, 14, 291, 12], [307, 17, 291, 15], [307, 21, 291, 19], [308, 10, 292, 8], [308, 11, 292, 9], [308, 12, 292, 10], [309, 8, 293, 6], [309, 9, 293, 7], [309, 10, 293, 8], [309, 13, 293, 11], [309, 17, 293, 15], [309, 19, 293, 17, "Platform"], [309, 40, 293, 25], [309, 41, 293, 26, "OS"], [309, 43, 293, 28], [309, 48, 293, 33], [309, 53, 293, 38], [309, 57, 293, 42, "searchBarVisible"], [309, 73, 293, 58], [309, 76, 293, 61], [309, 89, 293, 74], [309, 93, 293, 74, "_jsx"], [309, 108, 293, 78], [309, 110, 293, 79, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [309, 142, 293, 94], [309, 144, 293, 96], [310, 10, 294, 8], [310, 13, 294, 11, "headerSearchBarOptions"], [310, 35, 294, 33], [311, 10, 295, 8, "visible"], [311, 17, 295, 15], [311, 19, 295, 17, "searchBarVisible"], [311, 35, 295, 33], [312, 10, 296, 8, "onClose"], [312, 17, 296, 15], [312, 19, 296, 17, "onClose"], [312, 20, 296, 17], [312, 25, 296, 23], [313, 12, 297, 10, "setSearchBarVisible"], [313, 31, 297, 29], [313, 32, 297, 30], [313, 37, 297, 35], [313, 38, 297, 36], [314, 12, 298, 10, "headerSearchBarOptions"], [314, 34, 298, 32], [314, 36, 298, 34, "onClose"], [314, 43, 298, 41], [314, 46, 298, 44], [314, 47, 298, 45], [315, 10, 299, 8], [315, 11, 299, 9], [316, 10, 300, 8, "tintColor"], [316, 19, 300, 17], [316, 21, 300, 19, "headerTintColor"], [316, 36, 300, 34], [317, 10, 301, 8, "style"], [317, 15, 301, 13], [317, 17, 301, 15], [317, 18, 301, 16, "Platform"], [317, 39, 301, 24], [317, 40, 301, 25, "OS"], [317, 42, 301, 27], [317, 47, 301, 32], [317, 52, 301, 37], [317, 55, 301, 40], [317, 56, 301, 41, "StyleSheet"], [317, 79, 301, 51], [317, 80, 301, 52, "absoluteFill"], [317, 92, 301, 64], [317, 94, 301, 66], [318, 12, 302, 10, "paddingTop"], [318, 22, 302, 20], [318, 24, 302, 22, "headerStatusBarHeight"], [318, 45, 302, 43], [318, 48, 302, 46], [318, 49, 302, 47], [318, 52, 302, 50], [319, 10, 303, 8], [319, 11, 303, 9], [319, 13, 303, 11], [320, 12, 304, 10, "backgroundColor"], [320, 27, 304, 25], [320, 29, 304, 27, "backgroundColor"], [320, 44, 304, 42], [320, 48, 304, 46, "colors"], [320, 54, 304, 52], [320, 55, 304, 53, "card"], [321, 10, 305, 8], [321, 11, 305, 9], [321, 12, 305, 10], [321, 15, 305, 13], [321, 16, 305, 14, "leftButton"], [321, 26, 305, 24], [321, 30, 305, 28], [322, 12, 306, 10, "marginStart"], [322, 23, 306, 21], [322, 25, 306, 23], [323, 10, 307, 8], [323, 11, 307, 9], [324, 8, 308, 6], [324, 9, 308, 7], [324, 10, 308, 8], [324, 13, 308, 11], [324, 17, 308, 15], [325, 6, 309, 4], [325, 7, 309, 5], [325, 8, 309, 6], [326, 4, 310, 2], [326, 5, 310, 3], [326, 6, 310, 4], [327, 2, 311, 0], [328, 2, 312, 0], [328, 6, 312, 6, "styles"], [328, 12, 312, 12], [328, 15, 312, 15, "StyleSheet"], [328, 38, 312, 25], [328, 39, 312, 26, "create"], [328, 45, 312, 32], [328, 46, 312, 33], [329, 4, 313, 2, "content"], [329, 11, 313, 9], [329, 13, 313, 11], [330, 6, 314, 4, "flex"], [330, 10, 314, 8], [330, 12, 314, 10], [330, 13, 314, 11], [331, 6, 315, 4, "flexDirection"], [331, 19, 315, 17], [331, 21, 315, 19], [331, 26, 315, 24], [332, 6, 316, 4, "alignItems"], [332, 16, 316, 14], [332, 18, 316, 16], [333, 4, 317, 2], [333, 5, 317, 3], [334, 4, 318, 2, "large"], [334, 9, 318, 7], [334, 11, 318, 9], [335, 6, 319, 4, "marginHorizontal"], [335, 22, 319, 20], [335, 24, 319, 22], [336, 4, 320, 2], [336, 5, 320, 3], [337, 4, 321, 2, "title"], [337, 9, 321, 7], [337, 11, 321, 9], [338, 6, 322, 4, "justifyContent"], [338, 20, 322, 18], [338, 22, 322, 20], [339, 4, 323, 2], [339, 5, 323, 3], [340, 4, 324, 2, "start"], [340, 9, 324, 7], [340, 11, 324, 9], [341, 6, 325, 4, "flexDirection"], [341, 19, 325, 17], [341, 21, 325, 19], [341, 26, 325, 24], [342, 6, 326, 4, "alignItems"], [342, 16, 326, 14], [342, 18, 326, 16], [342, 26, 326, 24], [343, 6, 327, 4, "justifyContent"], [343, 20, 327, 18], [343, 22, 327, 20], [344, 4, 328, 2], [344, 5, 328, 3], [345, 4, 329, 2, "end"], [345, 7, 329, 5], [345, 9, 329, 7], [346, 6, 330, 4, "flexDirection"], [346, 19, 330, 17], [346, 21, 330, 19], [346, 26, 330, 24], [347, 6, 331, 4, "alignItems"], [347, 16, 331, 14], [347, 18, 331, 16], [347, 26, 331, 24], [348, 6, 332, 4, "justifyContent"], [348, 20, 332, 18], [348, 22, 332, 20], [349, 4, 333, 2], [349, 5, 333, 3], [350, 4, 334, 2, "expand"], [350, 10, 334, 8], [350, 12, 334, 10], [351, 6, 335, 4, "flexGrow"], [351, 14, 335, 12], [351, 16, 335, 14], [351, 17, 335, 15], [352, 6, 336, 4, "flexBasis"], [352, 15, 336, 13], [352, 17, 336, 15], [353, 4, 337, 2], [354, 2, 338, 0], [354, 3, 338, 1], [354, 4, 338, 2], [355, 0, 338, 3], [355, 3]], "functionMap": {"names": ["<global>", "warnIfHeaderStylesDefined", "Object.keys.forEach$argument_0", "Header", "useFrameSize$argument_0", "onTitleLayout", "setTitleLayout$argument_0", "<anonymous>", "_jsx$argument_1.onPress", "_jsx$argument_1.onClose"], "mappings": "AAA;kCCqB;8BCC;GDO;CDC;OGC;6BCE,YD;wBEQ;mBCK;KDQ;GFC;wBIQ;MJE;0DIyJ;IJE;qBK8D;aLG;iBMU;SNG;CHY"}}, "type": "js/module"}]}