{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "./commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 146}, "end": {"line": 10, "column": 43, "index": 189}}], "key": "+1Up2ERDMxkqzy1yjP2acBRtCSM=", "exportNames": ["*"]}}, {"name": "./mutables", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 190}, "end": {"line": 11, "column": 41, "index": 231}}], "key": "SiNpLdwfdd4YE0waycjIzPSn4ZU=", "exportNames": ["*"]}}, {"name": "./ReanimatedModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 232}, "end": {"line": 12, "column": 54, "index": 286}}], "key": "oecxEvQmWRmzTP60VuKAoww/f/4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _commonTypes = require(_dependencyMap[3], \"./commonTypes\");\n  var _mutables = require(_dependencyMap[4], \"./mutables\");\n  var _ReanimatedModule = require(_dependencyMap[5], \"./ReanimatedModule\");\n  function initSensorData(sensorType) {\n    if (sensorType === _commonTypes.SensorType.ROTATION) {\n      return (0, _mutables.makeMutable)({\n        qw: 0,\n        qx: 0,\n        qy: 0,\n        qz: 0,\n        yaw: 0,\n        pitch: 0,\n        roll: 0,\n        interfaceOrientation: 0\n      });\n    } else {\n      return (0, _mutables.makeMutable)({\n        x: 0,\n        y: 0,\n        z: 0,\n        interfaceOrientation: 0\n      });\n    }\n  }\n  var Sensor = exports.default = /*#__PURE__*/function () {\n    function Sensor(sensorType, config) {\n      (0, _classCallCheck2.default)(this, Sensor);\n      this.listenersNumber = 0;\n      this.sensorId = null;\n      this.sensorType = sensorType;\n      this.config = config;\n      this.data = initSensorData(sensorType);\n    }\n    return (0, _createClass2.default)(Sensor, [{\n      key: \"register\",\n      value: function register(eventHandler) {\n        var config = this.config;\n        var sensorType = this.sensorType;\n        this.sensorId = _ReanimatedModule.ReanimatedModule.registerSensor(sensorType, config.interval === 'auto' ? -1 : config.interval, config.iosReferenceFrame, eventHandler);\n        return this.sensorId !== -1;\n      }\n    }, {\n      key: \"isRunning\",\n      value: function isRunning() {\n        return this.sensorId !== -1 && this.sensorId !== null;\n      }\n    }, {\n      key: \"isAvailable\",\n      value: function isAvailable() {\n        return this.sensorId !== -1;\n      }\n    }, {\n      key: \"getSharedValue\",\n      value: function getSharedValue() {\n        return this.data;\n      }\n    }, {\n      key: \"unregister\",\n      value: function unregister() {\n        if (this.sensorId !== null && this.sensorId !== -1) {\n          _ReanimatedModule.ReanimatedModule.unregisterSensor(this.sensorId);\n        }\n        this.sensorId = null;\n      }\n    }]);\n  }();\n});", "lineCount": 77, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_classCallCheck2"], [9, 22, 1, 13], [9, 25, 1, 13, "_interopRequireDefault"], [9, 47, 1, 13], [9, 48, 1, 13, "require"], [9, 55, 1, 13], [9, 56, 1, 13, "_dependencyMap"], [9, 70, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_createClass2"], [10, 19, 1, 13], [10, 22, 1, 13, "_interopRequireDefault"], [10, 44, 1, 13], [10, 45, 1, 13, "require"], [10, 52, 1, 13], [10, 53, 1, 13, "_dependencyMap"], [10, 67, 1, 13], [11, 2, 10, 0], [11, 6, 10, 0, "_commonTypes"], [11, 18, 10, 0], [11, 21, 10, 0, "require"], [11, 28, 10, 0], [11, 29, 10, 0, "_dependencyMap"], [11, 43, 10, 0], [12, 2, 11, 0], [12, 6, 11, 0, "_mutables"], [12, 15, 11, 0], [12, 18, 11, 0, "require"], [12, 25, 11, 0], [12, 26, 11, 0, "_dependencyMap"], [12, 40, 11, 0], [13, 2, 12, 0], [13, 6, 12, 0, "_ReanimatedModule"], [13, 23, 12, 0], [13, 26, 12, 0, "require"], [13, 33, 12, 0], [13, 34, 12, 0, "_dependencyMap"], [13, 48, 12, 0], [14, 2, 14, 0], [14, 11, 14, 9, "initSensorData"], [14, 25, 14, 23, "initSensorData"], [14, 26, 15, 2, "sensorType"], [14, 36, 15, 24], [14, 38, 16, 40], [15, 4, 17, 2], [15, 8, 17, 6, "sensorType"], [15, 18, 17, 16], [15, 23, 17, 21, "SensorType"], [15, 46, 17, 31], [15, 47, 17, 32, "ROTATION"], [15, 55, 17, 40], [15, 57, 17, 42], [16, 6, 18, 4], [16, 13, 18, 11], [16, 17, 18, 11, "makeMutable"], [16, 38, 18, 22], [16, 40, 18, 48], [17, 8, 19, 6, "qw"], [17, 10, 19, 8], [17, 12, 19, 10], [17, 13, 19, 11], [18, 8, 20, 6, "qx"], [18, 10, 20, 8], [18, 12, 20, 10], [18, 13, 20, 11], [19, 8, 21, 6, "qy"], [19, 10, 21, 8], [19, 12, 21, 10], [19, 13, 21, 11], [20, 8, 22, 6, "qz"], [20, 10, 22, 8], [20, 12, 22, 10], [20, 13, 22, 11], [21, 8, 23, 6, "yaw"], [21, 11, 23, 9], [21, 13, 23, 11], [21, 14, 23, 12], [22, 8, 24, 6, "pitch"], [22, 13, 24, 11], [22, 15, 24, 13], [22, 16, 24, 14], [23, 8, 25, 6, "roll"], [23, 12, 25, 10], [23, 14, 25, 12], [23, 15, 25, 13], [24, 8, 26, 6, "interfaceOrientation"], [24, 28, 26, 26], [24, 30, 26, 28], [25, 6, 27, 4], [25, 7, 27, 5], [25, 8, 27, 6], [26, 4, 28, 2], [26, 5, 28, 3], [26, 11, 28, 9], [27, 6, 29, 4], [27, 13, 29, 11], [27, 17, 29, 11, "makeMutable"], [27, 38, 29, 22], [27, 40, 29, 48], [28, 8, 30, 6, "x"], [28, 9, 30, 7], [28, 11, 30, 9], [28, 12, 30, 10], [29, 8, 31, 6, "y"], [29, 9, 31, 7], [29, 11, 31, 9], [29, 12, 31, 10], [30, 8, 32, 6, "z"], [30, 9, 32, 7], [30, 11, 32, 9], [30, 12, 32, 10], [31, 8, 33, 6, "interfaceOrientation"], [31, 28, 33, 26], [31, 30, 33, 28], [32, 6, 34, 4], [32, 7, 34, 5], [32, 8, 34, 6], [33, 4, 35, 2], [34, 2, 36, 0], [35, 2, 36, 1], [35, 6, 38, 21, "Sensor"], [35, 12, 38, 27], [35, 15, 38, 27, "exports"], [35, 22, 38, 27], [35, 23, 38, 27, "default"], [35, 30, 38, 27], [36, 4, 45, 2], [36, 13, 45, 2, "Sensor"], [36, 20, 45, 14, "sensorType"], [36, 30, 45, 36], [36, 32, 45, 38, "config"], [36, 38, 45, 58], [36, 40, 45, 60], [37, 6, 45, 60], [37, 10, 45, 60, "_classCallCheck2"], [37, 26, 45, 60], [37, 27, 45, 60, "default"], [37, 34, 45, 60], [37, 42, 45, 60, "Sensor"], [37, 48, 45, 60], [38, 6, 45, 60], [38, 11, 39, 9, "listenersNumber"], [38, 26, 39, 24], [38, 29, 39, 27], [38, 30, 39, 28], [39, 6, 39, 28], [39, 11, 40, 10, "sensorId"], [39, 19, 40, 18], [39, 22, 40, 36], [39, 26, 40, 40], [40, 6, 46, 4], [40, 10, 46, 8], [40, 11, 46, 9, "sensorType"], [40, 21, 46, 19], [40, 24, 46, 22, "sensorType"], [40, 34, 46, 32], [41, 6, 47, 4], [41, 10, 47, 8], [41, 11, 47, 9, "config"], [41, 17, 47, 15], [41, 20, 47, 18, "config"], [41, 26, 47, 24], [42, 6, 48, 4], [42, 10, 48, 8], [42, 11, 48, 9, "data"], [42, 15, 48, 13], [42, 18, 48, 16, "initSensorData"], [42, 32, 48, 30], [42, 33, 48, 31, "sensorType"], [42, 43, 48, 41], [42, 44, 48, 42], [43, 4, 49, 2], [44, 4, 49, 3], [44, 15, 49, 3, "_createClass2"], [44, 28, 49, 3], [44, 29, 49, 3, "default"], [44, 36, 49, 3], [44, 38, 49, 3, "Sensor"], [44, 44, 49, 3], [45, 6, 49, 3, "key"], [45, 9, 49, 3], [46, 6, 49, 3, "value"], [46, 11, 49, 3], [46, 13, 51, 2], [46, 22, 51, 2, "register"], [46, 30, 51, 10, "register"], [46, 31, 52, 4, "<PERSON><PERSON><PERSON><PERSON>"], [46, 43, 52, 71], [46, 45, 53, 4], [47, 8, 54, 4], [47, 12, 54, 10, "config"], [47, 18, 54, 16], [47, 21, 54, 19], [47, 25, 54, 23], [47, 26, 54, 24, "config"], [47, 32, 54, 30], [48, 8, 55, 4], [48, 12, 55, 10, "sensorType"], [48, 22, 55, 20], [48, 25, 55, 23], [48, 29, 55, 27], [48, 30, 55, 28, "sensorType"], [48, 40, 55, 38], [49, 8, 56, 4], [49, 12, 56, 8], [49, 13, 56, 9, "sensorId"], [49, 21, 56, 17], [49, 24, 56, 20, "ReanimatedModule"], [49, 58, 56, 36], [49, 59, 56, 37, "registerSensor"], [49, 73, 56, 51], [49, 74, 57, 6, "sensorType"], [49, 84, 57, 16], [49, 86, 58, 6, "config"], [49, 92, 58, 12], [49, 93, 58, 13, "interval"], [49, 101, 58, 21], [49, 106, 58, 26], [49, 112, 58, 32], [49, 115, 58, 35], [49, 116, 58, 36], [49, 117, 58, 37], [49, 120, 58, 40, "config"], [49, 126, 58, 46], [49, 127, 58, 47, "interval"], [49, 135, 58, 55], [49, 137, 59, 6, "config"], [49, 143, 59, 12], [49, 144, 59, 13, "iosReferenceFrame"], [49, 161, 59, 30], [49, 163, 60, 6, "<PERSON><PERSON><PERSON><PERSON>"], [49, 175, 61, 4], [49, 176, 61, 5], [50, 8, 62, 4], [50, 15, 62, 11], [50, 19, 62, 15], [50, 20, 62, 16, "sensorId"], [50, 28, 62, 24], [50, 33, 62, 29], [50, 34, 62, 30], [50, 35, 62, 31], [51, 6, 63, 2], [52, 4, 63, 3], [53, 6, 63, 3, "key"], [53, 9, 63, 3], [54, 6, 63, 3, "value"], [54, 11, 63, 3], [54, 13, 65, 2], [54, 22, 65, 2, "isRunning"], [54, 31, 65, 11, "isRunning"], [54, 32, 65, 11], [54, 34, 65, 14], [55, 8, 66, 4], [55, 15, 66, 11], [55, 19, 66, 15], [55, 20, 66, 16, "sensorId"], [55, 28, 66, 24], [55, 33, 66, 29], [55, 34, 66, 30], [55, 35, 66, 31], [55, 39, 66, 35], [55, 43, 66, 39], [55, 44, 66, 40, "sensorId"], [55, 52, 66, 48], [55, 57, 66, 53], [55, 61, 66, 57], [56, 6, 67, 2], [57, 4, 67, 3], [58, 6, 67, 3, "key"], [58, 9, 67, 3], [59, 6, 67, 3, "value"], [59, 11, 67, 3], [59, 13, 69, 2], [59, 22, 69, 2, "isAvailable"], [59, 33, 69, 13, "isAvailable"], [59, 34, 69, 13], [59, 36, 69, 16], [60, 8, 70, 4], [60, 15, 70, 11], [60, 19, 70, 15], [60, 20, 70, 16, "sensorId"], [60, 28, 70, 24], [60, 33, 70, 29], [60, 34, 70, 30], [60, 35, 70, 31], [61, 6, 71, 2], [62, 4, 71, 3], [63, 6, 71, 3, "key"], [63, 9, 71, 3], [64, 6, 71, 3, "value"], [64, 11, 71, 3], [64, 13, 73, 2], [64, 22, 73, 2, "getSharedValue"], [64, 36, 73, 16, "getSharedValue"], [64, 37, 73, 16], [64, 39, 73, 19], [65, 8, 74, 4], [65, 15, 74, 11], [65, 19, 74, 15], [65, 20, 74, 16, "data"], [65, 24, 74, 20], [66, 6, 75, 2], [67, 4, 75, 3], [68, 6, 75, 3, "key"], [68, 9, 75, 3], [69, 6, 75, 3, "value"], [69, 11, 75, 3], [69, 13, 77, 2], [69, 22, 77, 2, "unregister"], [69, 32, 77, 12, "unregister"], [69, 33, 77, 12], [69, 35, 77, 15], [70, 8, 78, 4], [70, 12, 78, 8], [70, 16, 78, 12], [70, 17, 78, 13, "sensorId"], [70, 25, 78, 21], [70, 30, 78, 26], [70, 34, 78, 30], [70, 38, 78, 34], [70, 42, 78, 38], [70, 43, 78, 39, "sensorId"], [70, 51, 78, 47], [70, 56, 78, 52], [70, 57, 78, 53], [70, 58, 78, 54], [70, 60, 78, 56], [71, 10, 79, 6, "ReanimatedModule"], [71, 44, 79, 22], [71, 45, 79, 23, "unregisterSensor"], [71, 61, 79, 39], [71, 62, 79, 40], [71, 66, 79, 44], [71, 67, 79, 45, "sensorId"], [71, 75, 79, 53], [71, 76, 79, 54], [72, 8, 80, 4], [73, 8, 81, 4], [73, 12, 81, 8], [73, 13, 81, 9, "sensorId"], [73, 21, 81, 17], [73, 24, 81, 20], [73, 28, 81, 24], [74, 6, 82, 2], [75, 4, 82, 3], [76, 2, 82, 3], [77, 0, 82, 3], [77, 3]], "functionMap": {"names": ["<global>", "initSensorData", "Sensor", "constructor", "register", "isRunning", "isAvailable", "getSharedValue", "unregister"], "mappings": "AAA;ACa;CDsB;eEE;ECO;GDI;EEE;GFY;EGE;GHE;EIE;GJE;EKE;GLE;EME;GNK;CFC"}}, "type": "js/module"}]}