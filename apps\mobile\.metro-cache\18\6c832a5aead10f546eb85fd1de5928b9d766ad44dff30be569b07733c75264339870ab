{"dependencies": [{"name": "./errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 43, "index": 58}}], "key": "rEld05quROH+iA6QLT6kkvqJ/qc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.interpolate = exports.clamp = exports.Extrapolation = void 0;\n  var _errors = require(_dependencyMap[0], \"./errors\");\n  /**\n   * Extrapolation type.\n   *\n   * @param IDENTITY - Returns the provided value as is.\n   * @param CLAMP - Clamps the value to the edge of the output range.\n   * @param EXTEND - Predicts the values beyond the output range.\n   */\n  var Extrapolation = exports.Extrapolation = /*#__PURE__*/function (Extrapolation) {\n    Extrapolation[\"IDENTITY\"] = \"identity\";\n    Extrapolation[\"CLAMP\"] = \"clamp\";\n    Extrapolation[\"EXTEND\"] = \"extend\";\n    return Extrapolation;\n  }({});\n  /** Represents the possible values for extrapolation as a string. */\n  /** Allows to specify extrapolation for left and right edge of the interpolation. */\n  /** Configuration options for extrapolation. */\n  var _worklet_5413376973116_init_data = {\n    code: \"function getVal_interpolationTs1(type,coef,val,leftEdgeOutput,rightEdgeOutput,x){const{Extrapolation}=this.__closure;switch(type){case Extrapolation.IDENTITY:return x;case Extrapolation.CLAMP:if(coef*val<coef*leftEdgeOutput){return leftEdgeOutput;}return rightEdgeOutput;case Extrapolation.EXTEND:default:return val;}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolation.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getVal_interpolationTs1\\\",\\\"type\\\",\\\"coef\\\",\\\"val\\\",\\\"leftEdgeOutput\\\",\\\"rightEdgeOutput\\\",\\\"x\\\",\\\"Extrapolation\\\",\\\"__closure\\\",\\\"IDENTITY\\\",\\\"CLAMP\\\",\\\"EXTEND\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolation.ts\\\"],\\\"mappings\\\":\\\"AA6CA,SAAAA,uBAGaA,CAAAC,IACX,CAAAC,IAAA,CAAAC,GAAsB,CACtBC,cAAA,CAAuBC,eAEf,CAAAC,CAAA,QAAAC,aAAA,OAAAC,SAAA,CAGR,OAAQP,IAAI,EACV,IAAK,CAAAM,aAAa,CAACE,QAAQ,CACzB,MAAO,CAAAH,CAAC,CACV,IAAK,CAAAC,aAAa,CAACG,KAAK,CACtB,GAAIR,IAAI,CAAGC,GAAG,CAAGD,IAAI,CAAGE,cAAc,CAAE,CACtC,MAAO,CAAAA,cAAc,CACvB,CACA,MAAO,CAAAC,eAAe,CACxB,IAAK,CAAAE,aAAa,CAACI,MAAM,CACzB,QACE,MAAO,CAAAR,GAAG,CACd,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var getVal = function () {\n    var _e = [new global.Error(), -2, -27];\n    var getVal = function (type, coef, val, leftEdgeOutput, rightEdgeOutput, x) {\n      switch (type) {\n        case Extrapolation.IDENTITY:\n          return x;\n        case Extrapolation.CLAMP:\n          if (coef * val < coef * leftEdgeOutput) {\n            return leftEdgeOutput;\n          }\n          return rightEdgeOutput;\n        case Extrapolation.EXTEND:\n        default:\n          return val;\n      }\n    };\n    getVal.__closure = {\n      Extrapolation\n    };\n    getVal.__workletHash = 5413376973116;\n    getVal.__initData = _worklet_5413376973116_init_data;\n    getVal.__stackDetails = _e;\n    return getVal;\n  }();\n  var _worklet_14591500575420_init_data = {\n    code: \"function isExtrapolate_interpolationTs2(value){const{Extrapolation}=this.__closure;return value===Extrapolation.EXTEND||value===Extrapolation.CLAMP||value===Extrapolation.IDENTITY;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolation.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"isExtrapolate_interpolationTs2\\\",\\\"value\\\",\\\"Extrapolation\\\",\\\"__closure\\\",\\\"EXTEND\\\",\\\"CLAMP\\\",\\\"IDENTITY\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolation.ts\\\"],\\\"mappings\\\":\\\"AAqEA,SAAAA,8BAA8DA,CAAAC,KAAA,QAAAC,aAAA,OAAAC,SAAA,CAG5D,MAEE,CAAAF,KAAK,GAAKC,aAAa,CAACE,MAAM,EAC9BH,KAAK,GAAKC,aAAa,CAACG,KAAK,EAC7BJ,KAAK,GAAKC,aAAa,CAACI,QAAA,CAG5B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var isExtrapolate = function () {\n    var _e = [new global.Error(), -2, -27];\n    var isExtrapolate = function (value) {\n      return /* eslint-disable @typescript-eslint/no-unsafe-enum-comparison */value === Extrapolation.EXTEND || value === Extrapolation.CLAMP || value === Extrapolation.IDENTITY\n      /* eslint-enable @typescript-eslint/no-unsafe-enum-comparison */;\n    };\n    isExtrapolate.__closure = {\n      Extrapolation\n    };\n    isExtrapolate.__workletHash = 14591500575420;\n    isExtrapolate.__initData = _worklet_14591500575420_init_data;\n    isExtrapolate.__stackDetails = _e;\n    return isExtrapolate;\n  }(); // validates extrapolations type\n  // if type is correct, converts it to ExtrapolationConfig\n  var _worklet_14200579615035_init_data = {\n    code: \"function validateType_interpolationTs3(type){const{Extrapolation,isExtrapolate}=this.__closure;const extrapolationConfig={extrapolateLeft:Extrapolation.EXTEND,extrapolateRight:Extrapolation.EXTEND};if(!type){return extrapolationConfig;}if(typeof type==='string'){if(!isExtrapolate(type)){throw new ReanimatedError(\\\"Unsupported value for \\\\\\\"interpolate\\\\\\\" \\\\nSupported values: [\\\\\\\"extend\\\\\\\", \\\\\\\"clamp\\\\\\\", \\\\\\\"identity\\\\\\\", Extrapolatation.CLAMP, Extrapolatation.EXTEND, Extrapolatation.IDENTITY]\\\\n Valid example:\\\\n        interpolate(value, [inputRange], [outputRange], \\\\\\\"clamp\\\\\\\")\\\");}extrapolationConfig.extrapolateLeft=type;extrapolationConfig.extrapolateRight=type;return extrapolationConfig;}if(type.extrapolateLeft&&!isExtrapolate(type.extrapolateLeft)||type.extrapolateRight&&!isExtrapolate(type.extrapolateRight)){throw new ReanimatedError(\\\"Unsupported value for \\\\\\\"interpolate\\\\\\\" \\\\nSupported values: [\\\\\\\"extend\\\\\\\", \\\\\\\"clamp\\\\\\\", \\\\\\\"identity\\\\\\\", Extrapolatation.CLAMP, Extrapolatation.EXTEND, Extrapolatation.IDENTITY]\\\\n Valid example:\\\\n      interpolate(value, [inputRange], [outputRange], {\\\\n        extrapolateLeft: Extrapolation.CLAMP,\\\\n        extrapolateRight: Extrapolation.IDENTITY\\\\n      }})\\\");}Object.assign(extrapolationConfig,type);return extrapolationConfig;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolation.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"validateType_interpolationTs3\\\",\\\"type\\\",\\\"Extrapolation\\\",\\\"isExtrapolate\\\",\\\"__closure\\\",\\\"extrapolationConfig\\\",\\\"extrapolateLeft\\\",\\\"EXTEND\\\",\\\"extrapolateRight\\\",\\\"ReanimatedError\\\",\\\"Object\\\",\\\"assign\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolation.ts\\\"],\\\"mappings\\\":\\\"AAiFA,SAAAA,8BAAAC,IAAA,QAAAC,aAAA,CAAAC,aAAA,OAAAC,SAAA,CACA,MAAAC,mBAAA,EACAC,eAAS,CAAYJ,aAAuD,CAAAK,MAAA,CAE1EC,gBAAA,CAAAN,aAAA,CAAAK,MACA,E,GACE,CAAAN,IAAA,EACA,OAAAI,mBAAkB,CACpB,CAEA,GAAI,MAAO,CAAAJ,IAAA,aACT,IAAAE,aAAO,CAAAF,IAAA,EAAmB,CAC5B,UAAAQ,eAAA,kQAEA,CACEJ,mBAAmB,CAAAC,eAAO,CAAAL,IAAA,C,mBACd,CAAAO,gBAAe,CAAAP,IAAA,CAI3B,OAAAI,mBAAA,C,CAIF,GAAAJ,IAAA,CAAAK,eAAA,GAAAH,aAAA,CAAAF,IAAA,CAAAK,eAAA,GAAAL,IAAA,CAAAO,gBAAA,GAAAL,aAAA,CAAAF,IAAA,CAAAO,gBAAA,G,4XAEA,CACAE,MACG,CAAAC,MAAK,CAAAN,mBAAoB,CAAAJ,IAAA,E,MAG1B,CAAAI,mBAAU,C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var validateType = function () {\n    var _e = [new global.Error(), -3, -27];\n    var validateType = function (type) {\n      // initialize extrapolationConfig with default extrapolation\n      var extrapolationConfig = {\n        extrapolateLeft: Extrapolation.EXTEND,\n        extrapolateRight: Extrapolation.EXTEND\n      };\n      if (!type) {\n        return extrapolationConfig;\n      }\n      if (typeof type === 'string') {\n        if (!isExtrapolate(type)) {\n          throw new _errors.ReanimatedError(`Unsupported value for \"interpolate\" \\nSupported values: [\"extend\", \"clamp\", \"identity\", Extrapolatation.CLAMP, Extrapolatation.EXTEND, Extrapolatation.IDENTITY]\\n Valid example:\n        interpolate(value, [inputRange], [outputRange], \"clamp\")`);\n        }\n        extrapolationConfig.extrapolateLeft = type;\n        extrapolationConfig.extrapolateRight = type;\n        return extrapolationConfig;\n      }\n\n      // otherwise type is extrapolation config object\n      if (type.extrapolateLeft && !isExtrapolate(type.extrapolateLeft) || type.extrapolateRight && !isExtrapolate(type.extrapolateRight)) {\n        throw new _errors.ReanimatedError(`Unsupported value for \"interpolate\" \\nSupported values: [\"extend\", \"clamp\", \"identity\", Extrapolatation.CLAMP, Extrapolatation.EXTEND, Extrapolatation.IDENTITY]\\n Valid example:\n      interpolate(value, [inputRange], [outputRange], {\n        extrapolateLeft: Extrapolation.CLAMP,\n        extrapolateRight: Extrapolation.IDENTITY\n      }})`);\n      }\n      Object.assign(extrapolationConfig, type);\n      return extrapolationConfig;\n    };\n    validateType.__closure = {\n      Extrapolation,\n      isExtrapolate\n    };\n    validateType.__workletHash = 14200579615035;\n    validateType.__initData = _worklet_14200579615035_init_data;\n    validateType.__stackDetails = _e;\n    return validateType;\n  }();\n  var _worklet_9740645922780_init_data = {\n    code: \"function internalInterpolate_interpolationTs4(x,narrowedInput,extrapolationConfig){const{getVal}=this.__closure;const{leftEdgeInput:leftEdgeInput,rightEdgeInput:rightEdgeInput,leftEdgeOutput:leftEdgeOutput,rightEdgeOutput:rightEdgeOutput}=narrowedInput;if(rightEdgeInput-leftEdgeInput===0){return leftEdgeOutput;}const progress=(x-leftEdgeInput)/(rightEdgeInput-leftEdgeInput);const val=leftEdgeOutput+progress*(rightEdgeOutput-leftEdgeOutput);const coef=rightEdgeOutput>=leftEdgeOutput?1:-1;if(coef*val<coef*leftEdgeOutput){return getVal(extrapolationConfig.extrapolateLeft,coef,val,leftEdgeOutput,rightEdgeOutput,x);}else if(coef*val>coef*rightEdgeOutput){return getVal(extrapolationConfig.extrapolateRight,coef,val,leftEdgeOutput,rightEdgeOutput,x);}return val;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolation.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"internalInterpolate_interpolationTs4\\\",\\\"x\\\",\\\"narrowedInput\\\",\\\"extrapolationConfig\\\",\\\"getVal\\\",\\\"__closure\\\",\\\"leftEdgeInput\\\",\\\"rightEdgeInput\\\",\\\"leftEdgeOutput\\\",\\\"rightEdgeOutput\\\",\\\"progress\\\",\\\"val\\\",\\\"coef\\\",\\\"extrapolateLeft\\\",\\\"extrapolateRight\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolation.ts\\\"],\\\"mappings\\\":\\\"AA6HA,SAAAA,oCAEEA,CAAyCC,CAAA,CACzCC,aAAA,CAAAC,mBACA,QAAAC,MAAA,OAAAC,SAAA,CAEA,KAAM,CAAEC,aAAa,CAAbA,aAAa,CAAEC,cAAc,CAAdA,cAAc,CAAEC,cAAc,CAAdA,cAAc,CAAEC,eAAA,CAAAA,eAAgB,CAAC,CACtEP,aAAa,CACf,GAAIK,cAAc,CAAGD,aAAa,GAAK,CAAC,CAAE,CACxC,MAAO,CAAAE,cAAc,CACvB,CACA,KAAM,CAAAE,QAAQ,CAAG,CAACT,CAAC,CAAGK,aAAa,GAAKC,cAAc,CAAGD,aAAa,CAAC,CACvE,KAAM,CAAAK,GAAG,CAAGH,cAAc,CAAGE,QAAQ,EAAID,eAAe,CAAGD,cAAc,CAAC,CAC1E,KAAM,CAAAI,IAAI,CAAGH,eAAe,EAAID,cAAc,CAAG,CAAC,CAAG,CAAC,CAAC,CAEvD,GAAII,IAAI,CAAGD,GAAG,CAAGC,IAAI,CAAGJ,cAAc,CAAE,CACtC,MAAO,CAAAJ,MAAM,CACXD,mBAAmB,CAACU,eAAe,CACnCD,IAAI,CACJD,GAAG,CACHH,cAAc,CACdC,eAAe,CACfR,CACF,CAAC,CACH,CAAC,IAAM,IAAIW,IAAI,CAAGD,GAAG,CAAGC,IAAI,CAAGH,eAAe,CAAE,CAC9C,MAAO,CAAAL,MAAM,CACXD,mBAAmB,CAACW,gBAAgB,CACpCF,IAAI,CACJD,GAAG,CACHH,cAAc,CACdC,eAAe,CACfR,CACF,CAAC,CACH,CAEA,MAAO,CAAAU,GAAG,CACZ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var internalInterpolate = function () {\n    var _e = [new global.Error(), -2, -27];\n    var internalInterpolate = function (x, narrowedInput, extrapolationConfig) {\n      var leftEdgeInput = narrowedInput.leftEdgeInput,\n        rightEdgeInput = narrowedInput.rightEdgeInput,\n        leftEdgeOutput = narrowedInput.leftEdgeOutput,\n        rightEdgeOutput = narrowedInput.rightEdgeOutput;\n      if (rightEdgeInput - leftEdgeInput === 0) {\n        return leftEdgeOutput;\n      }\n      var progress = (x - leftEdgeInput) / (rightEdgeInput - leftEdgeInput);\n      var val = leftEdgeOutput + progress * (rightEdgeOutput - leftEdgeOutput);\n      var coef = rightEdgeOutput >= leftEdgeOutput ? 1 : -1;\n      if (coef * val < coef * leftEdgeOutput) {\n        return getVal(extrapolationConfig.extrapolateLeft, coef, val, leftEdgeOutput, rightEdgeOutput, x);\n      } else if (coef * val > coef * rightEdgeOutput) {\n        return getVal(extrapolationConfig.extrapolateRight, coef, val, leftEdgeOutput, rightEdgeOutput, x);\n      }\n      return val;\n    };\n    internalInterpolate.__closure = {\n      getVal\n    };\n    internalInterpolate.__workletHash = 9740645922780;\n    internalInterpolate.__initData = _worklet_9740645922780_init_data;\n    internalInterpolate.__stackDetails = _e;\n    return internalInterpolate;\n  }();\n  /**\n   * Lets you map a value from one range to another using linear interpolation.\n   *\n   * @param value - A number from the `input` range that is going to be mapped to\n   *   the `output` range.\n   * @param inputRange - An array of numbers specifying the input range of the\n   *   interpolation.\n   * @param outputRange - An array of numbers specifying the output range of the\n   *   interpolation.\n   * @param extrapolate - Determines what happens when the `value` goes beyond the\n   *   `input` range. Defaults to `Extrapolation.EXTEND` -\n   *   {@link ExtrapolationType}.\n   * @returns A mapped value within the output range.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/utilities/interpolate\n   */\n  var _worklet_10311999127907_init_data = {\n    code: \"function interpolate_interpolationTs5(x,inputRange,outputRange,type){const{validateType,internalInterpolate}=this.__closure;if(inputRange.length<2||outputRange.length<2){throw new ReanimatedError('Interpolation input and output ranges should contain at least two values.');}const extrapolationConfig=validateType(type);const length=inputRange.length;const narrowedInput={leftEdgeInput:inputRange[0],rightEdgeInput:inputRange[1],leftEdgeOutput:outputRange[0],rightEdgeOutput:outputRange[1]};if(length>2){if(x>inputRange[length-1]){narrowedInput.leftEdgeInput=inputRange[length-2];narrowedInput.rightEdgeInput=inputRange[length-1];narrowedInput.leftEdgeOutput=outputRange[length-2];narrowedInput.rightEdgeOutput=outputRange[length-1];}else{for(let i=1;i<length;++i){if(x<=inputRange[i]){narrowedInput.leftEdgeInput=inputRange[i-1];narrowedInput.rightEdgeInput=inputRange[i];narrowedInput.leftEdgeOutput=outputRange[i-1];narrowedInput.rightEdgeOutput=outputRange[i];break;}}}}return internalInterpolate(x,narrowedInput,extrapolationConfig);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolation.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolate_interpolationTs5\\\",\\\"x\\\",\\\"inputRange\\\",\\\"outputRange\\\",\\\"type\\\",\\\"validateType\\\",\\\"internalInterpolate\\\",\\\"__closure\\\",\\\"length\\\",\\\"ReanimatedError\\\",\\\"extrapolationConfig\\\",\\\"narrowedInput\\\",\\\"leftEdgeInput\\\",\\\"rightEdgeInput\\\",\\\"leftEdgeOutput\\\",\\\"rightEdgeOutput\\\",\\\"i\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolation.ts\\\"],\\\"mappings\\\":\\\"AAkLO,SAAAA,4BAGLA,CAAAC,CAAA,CAAAC,UACA,CAAAC,WACQ,CAAAC,IAAA,QAAAC,YAAA,CAAAC,mBAAA,OAAAC,SAAA,CAER,GAAIL,UAAU,CAACM,MAAM,CAAG,CAAC,EAAIL,WAAW,CAACK,MAAM,CAAG,CAAC,CAAE,CACnD,KAAM,IAAI,CAAAC,eAAe,CACvB,2EACF,CAAC,CACH,CAEA,KAAM,CAAAC,mBAAmB,CAAGL,YAAY,CAACD,IAAI,CAAC,CAC9C,KAAM,CAAAI,MAAM,CAAGN,UAAU,CAACM,MAAM,CAChC,KAAM,CAAAG,aAAyC,CAAG,CAChDC,aAAa,CAAEV,UAAU,CAAC,CAAC,CAAC,CAC5BW,cAAc,CAAEX,UAAU,CAAC,CAAC,CAAC,CAC7BY,cAAc,CAAEX,WAAW,CAAC,CAAC,CAAC,CAC9BY,eAAe,CAAEZ,WAAW,CAAC,CAAC,CAChC,CAAC,CACD,GAAIK,MAAM,CAAG,CAAC,CAAE,CACd,GAAIP,CAAC,CAAGC,UAAU,CAACM,MAAM,CAAG,CAAC,CAAC,CAAE,CAC9BG,aAAa,CAACC,aAAa,CAAGV,UAAU,CAACM,MAAM,CAAG,CAAC,CAAC,CACpDG,aAAa,CAACE,cAAc,CAAGX,UAAU,CAACM,MAAM,CAAG,CAAC,CAAC,CACrDG,aAAa,CAACG,cAAc,CAAGX,WAAW,CAACK,MAAM,CAAG,CAAC,CAAC,CACtDG,aAAa,CAACI,eAAe,CAAGZ,WAAW,CAACK,MAAM,CAAG,CAAC,CAAC,CACzD,CAAC,IAAM,CACL,IAAK,GAAI,CAAAQ,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGR,MAAM,CAAE,EAAEQ,CAAC,CAAE,CAC/B,GAAIf,CAAC,EAAIC,UAAU,CAACc,CAAC,CAAC,CAAE,CACtBL,aAAa,CAACC,aAAa,CAAGV,UAAU,CAACc,CAAC,CAAG,CAAC,CAAC,CAC/CL,aAAa,CAACE,cAAc,CAAGX,UAAU,CAACc,CAAC,CAAC,CAC5CL,aAAa,CAACG,cAAc,CAAGX,WAAW,CAACa,CAAC,CAAG,CAAC,CAAC,CACjDL,aAAa,CAACI,eAAe,CAAGZ,WAAW,CAACa,CAAC,CAAC,CAC9C,MACF,CACF,CACF,CACF,CAEA,MAAO,CAAAV,mBAAmB,CAACL,CAAC,CAAEU,aAAa,CAAED,mBAAmB,CAAC,CACnE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var interpolate = exports.interpolate = function () {\n    var _e = [new global.Error(), -3, -27];\n    var interpolate = function (x, inputRange, outputRange, type) {\n      if (inputRange.length < 2 || outputRange.length < 2) {\n        throw new _errors.ReanimatedError('Interpolation input and output ranges should contain at least two values.');\n      }\n      var extrapolationConfig = validateType(type);\n      var length = inputRange.length;\n      var narrowedInput = {\n        leftEdgeInput: inputRange[0],\n        rightEdgeInput: inputRange[1],\n        leftEdgeOutput: outputRange[0],\n        rightEdgeOutput: outputRange[1]\n      };\n      if (length > 2) {\n        if (x > inputRange[length - 1]) {\n          narrowedInput.leftEdgeInput = inputRange[length - 2];\n          narrowedInput.rightEdgeInput = inputRange[length - 1];\n          narrowedInput.leftEdgeOutput = outputRange[length - 2];\n          narrowedInput.rightEdgeOutput = outputRange[length - 1];\n        } else {\n          for (var i = 1; i < length; ++i) {\n            if (x <= inputRange[i]) {\n              narrowedInput.leftEdgeInput = inputRange[i - 1];\n              narrowedInput.rightEdgeInput = inputRange[i];\n              narrowedInput.leftEdgeOutput = outputRange[i - 1];\n              narrowedInput.rightEdgeOutput = outputRange[i];\n              break;\n            }\n          }\n        }\n      }\n      return internalInterpolate(x, narrowedInput, extrapolationConfig);\n    };\n    interpolate.__closure = {\n      validateType,\n      internalInterpolate\n    };\n    interpolate.__workletHash = 10311999127907;\n    interpolate.__initData = _worklet_10311999127907_init_data;\n    interpolate.__stackDetails = _e;\n    return interpolate;\n  }();\n  /**\n   * Lets you limit a value within a specified range.\n   *\n   * @param value - A number that will be returned as long as the provided value\n   *   is in range between `min` and `max`.\n   * @param min - A number which will be returned when provided `value` is lower\n   *   than `min`.\n   * @param max - A number which will be returned when provided `value` is higher\n   *   than `max`.\n   * @returns A number between min and max bounds.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/utilities/clamp/\n   */\n  var _worklet_8339713177498_init_data = {\n    code: \"function clamp_interpolationTs6(value,min,max){return Math.min(Math.max(value,min),max);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolation.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"clamp_interpolationTs6\\\",\\\"value\\\",\\\"min\\\",\\\"max\\\",\\\"Math\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolation.ts\\\"],\\\"mappings\\\":\\\"AAyOO,SAAAA,sBAAsDA,CAAAC,KAAE,CAAAC,GAAA,CAAAC,GAAA,EAE7D,MAAO,CAAAC,IAAI,CAACF,GAAG,CAACE,IAAI,CAACD,GAAG,CAACF,KAAK,CAAEC,GAAG,CAAC,CAAEC,GAAG,CAAC,CAC5C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var clamp = exports.clamp = function () {\n    var _e = [new global.Error(), 1, -27];\n    var clamp = function (value, min, max) {\n      return Math.min(Math.max(value, min), max);\n    };\n    clamp.__closure = {};\n    clamp.__workletHash = 8339713177498;\n    clamp.__initData = _worklet_8339713177498_init_data;\n    clamp.__stackDetails = _e;\n    return clamp;\n  }();\n});", "lineCount": 250, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "interpolate"], [7, 21, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [7, 32, 1, 13, "clamp"], [7, 37, 1, 13], [7, 40, 1, 13, "exports"], [7, 47, 1, 13], [7, 48, 1, 13, "Extrapolation"], [7, 61, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_errors"], [8, 13, 3, 0], [8, 16, 3, 0, "require"], [8, 23, 3, 0], [8, 24, 3, 0, "_dependencyMap"], [8, 38, 3, 0], [9, 2, 5, 0], [10, 0, 6, 0], [11, 0, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 2, 5, 0], [16, 6, 12, 12, "Extrapolation"], [16, 19, 12, 25], [16, 22, 12, 25, "exports"], [16, 29, 12, 25], [16, 30, 12, 25, "Extrapolation"], [16, 43, 12, 25], [16, 69, 12, 12, "Extrapolation"], [16, 82, 12, 25], [17, 4, 12, 12, "Extrapolation"], [17, 17, 12, 25], [18, 4, 12, 12, "Extrapolation"], [18, 17, 12, 25], [19, 4, 12, 12, "Extrapolation"], [19, 17, 12, 25], [20, 4, 12, 25], [20, 11, 12, 12, "Extrapolation"], [20, 24, 12, 25], [21, 2, 12, 25], [22, 2, 18, 0], [23, 2, 28, 0], [24, 2, 39, 0], [25, 2, 39, 0], [25, 6, 39, 0, "_worklet_5413376973116_init_data"], [25, 38, 39, 0], [26, 4, 39, 0, "code"], [26, 8, 39, 0], [27, 4, 39, 0, "location"], [27, 12, 39, 0], [28, 4, 39, 0, "sourceMap"], [28, 13, 39, 0], [29, 4, 39, 0, "version"], [29, 11, 39, 0], [30, 2, 39, 0], [31, 2, 39, 0], [31, 6, 39, 0, "getVal"], [31, 12, 39, 0], [31, 15, 46, 0], [32, 4, 46, 0], [32, 8, 46, 0, "_e"], [32, 10, 46, 0], [32, 18, 46, 0, "global"], [32, 24, 46, 0], [32, 25, 46, 0, "Error"], [32, 30, 46, 0], [33, 4, 46, 0], [33, 8, 46, 0, "getVal"], [33, 14, 46, 0], [33, 26, 46, 0, "getVal"], [33, 27, 47, 2, "type"], [33, 31, 47, 21], [33, 33, 48, 2, "coef"], [33, 37, 48, 14], [33, 39, 49, 2, "val"], [33, 42, 49, 13], [33, 44, 50, 2, "leftEdgeOutput"], [33, 58, 50, 24], [33, 60, 51, 2, "rightEdgeOutput"], [33, 75, 51, 25], [33, 77, 52, 2, "x"], [33, 78, 52, 11], [33, 80, 53, 10], [34, 6, 56, 2], [34, 14, 56, 10, "type"], [34, 18, 56, 14], [35, 8, 57, 4], [35, 13, 57, 9, "Extrapolation"], [35, 26, 57, 22], [35, 27, 57, 23, "IDENTITY"], [35, 35, 57, 31], [36, 10, 58, 6], [36, 17, 58, 13, "x"], [36, 18, 58, 14], [37, 8, 59, 4], [37, 13, 59, 9, "Extrapolation"], [37, 26, 59, 22], [37, 27, 59, 23, "CLAMP"], [37, 32, 59, 28], [38, 10, 60, 6], [38, 14, 60, 10, "coef"], [38, 18, 60, 14], [38, 21, 60, 17, "val"], [38, 24, 60, 20], [38, 27, 60, 23, "coef"], [38, 31, 60, 27], [38, 34, 60, 30, "leftEdgeOutput"], [38, 48, 60, 44], [38, 50, 60, 46], [39, 12, 61, 8], [39, 19, 61, 15, "leftEdgeOutput"], [39, 33, 61, 29], [40, 10, 62, 6], [41, 10, 63, 6], [41, 17, 63, 13, "rightEdgeOutput"], [41, 32, 63, 28], [42, 8, 64, 4], [42, 13, 64, 9, "Extrapolation"], [42, 26, 64, 22], [42, 27, 64, 23, "EXTEND"], [42, 33, 64, 29], [43, 8, 65, 4], [44, 10, 66, 6], [44, 17, 66, 13, "val"], [44, 20, 66, 16], [45, 6, 67, 2], [46, 4, 68, 0], [46, 5, 68, 1], [47, 4, 68, 1, "getVal"], [47, 10, 68, 1], [47, 11, 68, 1, "__closure"], [47, 20, 68, 1], [48, 6, 68, 1, "Extrapolation"], [49, 4, 68, 1], [50, 4, 68, 1, "getVal"], [50, 10, 68, 1], [50, 11, 68, 1, "__workletHash"], [50, 24, 68, 1], [51, 4, 68, 1, "getVal"], [51, 10, 68, 1], [51, 11, 68, 1, "__initData"], [51, 21, 68, 1], [51, 24, 68, 1, "_worklet_5413376973116_init_data"], [51, 56, 68, 1], [52, 4, 68, 1, "getVal"], [52, 10, 68, 1], [52, 11, 68, 1, "__stackDetails"], [52, 25, 68, 1], [52, 28, 68, 1, "_e"], [52, 30, 68, 1], [53, 4, 68, 1], [53, 11, 68, 1, "getVal"], [53, 17, 68, 1], [54, 2, 68, 1], [54, 3, 46, 0], [55, 2, 46, 0], [55, 6, 46, 0, "_worklet_14591500575420_init_data"], [55, 39, 46, 0], [56, 4, 46, 0, "code"], [56, 8, 46, 0], [57, 4, 46, 0, "location"], [57, 12, 46, 0], [58, 4, 46, 0, "sourceMap"], [58, 13, 46, 0], [59, 4, 46, 0, "version"], [59, 11, 46, 0], [60, 2, 46, 0], [61, 2, 46, 0], [61, 6, 46, 0, "isExtrapolate"], [61, 19, 46, 0], [61, 22, 70, 0], [62, 4, 70, 0], [62, 8, 70, 0, "_e"], [62, 10, 70, 0], [62, 18, 70, 0, "global"], [62, 24, 70, 0], [62, 25, 70, 0, "Error"], [62, 30, 70, 0], [63, 4, 70, 0], [63, 8, 70, 0, "isExtrapolate"], [63, 21, 70, 0], [63, 33, 70, 0, "isExtrapolate"], [63, 34, 70, 23, "value"], [63, 39, 70, 36], [63, 41, 70, 62], [64, 6, 73, 2], [64, 13, 74, 4], [64, 78, 75, 4, "value"], [64, 83, 75, 9], [64, 88, 75, 14, "Extrapolation"], [64, 101, 75, 27], [64, 102, 75, 28, "EXTEND"], [64, 108, 75, 34], [64, 112, 76, 4, "value"], [64, 117, 76, 9], [64, 122, 76, 14, "Extrapolation"], [64, 135, 76, 27], [64, 136, 76, 28, "CLAMP"], [64, 141, 76, 33], [64, 145, 77, 4, "value"], [64, 150, 77, 9], [64, 155, 77, 14, "Extrapolation"], [64, 168, 77, 27], [64, 169, 77, 28, "IDENTITY"], [65, 6, 78, 4], [66, 4, 80, 0], [66, 5, 80, 1], [67, 4, 80, 1, "isExtrapolate"], [67, 17, 80, 1], [67, 18, 80, 1, "__closure"], [67, 27, 80, 1], [68, 6, 80, 1, "Extrapolation"], [69, 4, 80, 1], [70, 4, 80, 1, "isExtrapolate"], [70, 17, 80, 1], [70, 18, 80, 1, "__workletHash"], [70, 31, 80, 1], [71, 4, 80, 1, "isExtrapolate"], [71, 17, 80, 1], [71, 18, 80, 1, "__initData"], [71, 28, 80, 1], [71, 31, 80, 1, "_worklet_14591500575420_init_data"], [71, 64, 80, 1], [72, 4, 80, 1, "isExtrapolate"], [72, 17, 80, 1], [72, 18, 80, 1, "__stackDetails"], [72, 32, 80, 1], [72, 35, 80, 1, "_e"], [72, 37, 80, 1], [73, 4, 80, 1], [73, 11, 80, 1, "isExtrapolate"], [73, 24, 80, 1], [74, 2, 80, 1], [74, 3, 70, 0], [74, 7, 82, 0], [75, 2, 83, 0], [76, 2, 83, 0], [76, 6, 83, 0, "_worklet_14200579615035_init_data"], [76, 39, 83, 0], [77, 4, 83, 0, "code"], [77, 8, 83, 0], [78, 4, 83, 0, "location"], [78, 12, 83, 0], [79, 4, 83, 0, "sourceMap"], [79, 13, 83, 0], [80, 4, 83, 0, "version"], [80, 11, 83, 0], [81, 2, 83, 0], [82, 2, 83, 0], [82, 6, 83, 0, "validateType"], [82, 18, 83, 0], [82, 21, 84, 0], [83, 4, 84, 0], [83, 8, 84, 0, "_e"], [83, 10, 84, 0], [83, 18, 84, 0, "global"], [83, 24, 84, 0], [83, 25, 84, 0, "Error"], [83, 30, 84, 0], [84, 4, 84, 0], [84, 8, 84, 0, "validateType"], [84, 20, 84, 0], [84, 32, 84, 0, "validateType"], [84, 33, 84, 22, "type"], [84, 37, 84, 45], [84, 39, 84, 76], [85, 6, 86, 2], [86, 6, 87, 2], [86, 10, 87, 8, "extrapolationConfig"], [86, 29, 87, 56], [86, 32, 87, 59], [87, 8, 88, 4, "extrapolateLeft"], [87, 23, 88, 19], [87, 25, 88, 21, "Extrapolation"], [87, 38, 88, 34], [87, 39, 88, 35, "EXTEND"], [87, 45, 88, 41], [88, 8, 89, 4, "extrapolateRight"], [88, 24, 89, 20], [88, 26, 89, 22, "Extrapolation"], [88, 39, 89, 35], [88, 40, 89, 36, "EXTEND"], [89, 6, 90, 2], [89, 7, 90, 3], [90, 6, 92, 2], [90, 10, 92, 6], [90, 11, 92, 7, "type"], [90, 15, 92, 11], [90, 17, 92, 13], [91, 8, 93, 4], [91, 15, 93, 11, "extrapolationConfig"], [91, 34, 93, 30], [92, 6, 94, 2], [93, 6, 96, 2], [93, 10, 96, 6], [93, 17, 96, 13, "type"], [93, 21, 96, 17], [93, 26, 96, 22], [93, 34, 96, 30], [93, 36, 96, 32], [94, 8, 97, 4], [94, 12, 97, 8], [94, 13, 97, 9, "isExtrapolate"], [94, 26, 97, 22], [94, 27, 97, 23, "type"], [94, 31, 97, 27], [94, 32, 97, 28], [94, 34, 97, 30], [95, 10, 98, 6], [95, 16, 98, 12], [95, 20, 98, 16, "ReanimatedError"], [95, 43, 98, 31], [95, 44, 99, 8], [96, 0, 100, 0], [96, 65, 101, 6], [96, 66, 101, 7], [97, 8, 102, 4], [98, 8, 103, 4, "extrapolationConfig"], [98, 27, 103, 23], [98, 28, 103, 24, "extrapolateLeft"], [98, 43, 103, 39], [98, 46, 103, 42, "type"], [98, 50, 103, 46], [99, 8, 104, 4, "extrapolationConfig"], [99, 27, 104, 23], [99, 28, 104, 24, "extrapolateRight"], [99, 44, 104, 40], [99, 47, 104, 43, "type"], [99, 51, 104, 47], [100, 8, 105, 4], [100, 15, 105, 11, "extrapolationConfig"], [100, 34, 105, 30], [101, 6, 106, 2], [103, 6, 108, 2], [104, 6, 109, 2], [104, 10, 110, 5, "type"], [104, 14, 110, 9], [104, 15, 110, 10, "extrapolateLeft"], [104, 30, 110, 25], [104, 34, 110, 29], [104, 35, 110, 30, "isExtrapolate"], [104, 48, 110, 43], [104, 49, 110, 44, "type"], [104, 53, 110, 48], [104, 54, 110, 49, "extrapolateLeft"], [104, 69, 110, 64], [104, 70, 110, 65], [104, 74, 111, 5, "type"], [104, 78, 111, 9], [104, 79, 111, 10, "extrapolateRight"], [104, 95, 111, 26], [104, 99, 111, 30], [104, 100, 111, 31, "isExtrapolate"], [104, 113, 111, 44], [104, 114, 111, 45, "type"], [104, 118, 111, 49], [104, 119, 111, 50, "extrapolateRight"], [104, 135, 111, 66], [104, 136, 111, 68], [104, 138, 112, 4], [105, 8, 113, 4], [105, 14, 113, 10], [105, 18, 113, 14, "ReanimatedError"], [105, 41, 113, 29], [105, 42, 114, 6], [106, 0, 115, 0], [107, 0, 116, 0], [108, 0, 117, 0], [109, 0, 118, 0], [109, 10, 119, 4], [109, 11, 119, 5], [110, 6, 120, 2], [111, 6, 122, 2, "Object"], [111, 12, 122, 8], [111, 13, 122, 9, "assign"], [111, 19, 122, 15], [111, 20, 122, 16, "extrapolationConfig"], [111, 39, 122, 35], [111, 41, 122, 37, "type"], [111, 45, 122, 41], [111, 46, 122, 42], [112, 6, 123, 2], [112, 13, 123, 9, "extrapolationConfig"], [112, 32, 123, 28], [113, 4, 124, 0], [113, 5, 124, 1], [114, 4, 124, 1, "validateType"], [114, 16, 124, 1], [114, 17, 124, 1, "__closure"], [114, 26, 124, 1], [115, 6, 124, 1, "Extrapolation"], [115, 19, 124, 1], [116, 6, 124, 1, "isExtrapolate"], [117, 4, 124, 1], [118, 4, 124, 1, "validateType"], [118, 16, 124, 1], [118, 17, 124, 1, "__workletHash"], [118, 30, 124, 1], [119, 4, 124, 1, "validateType"], [119, 16, 124, 1], [119, 17, 124, 1, "__initData"], [119, 27, 124, 1], [119, 30, 124, 1, "_worklet_14200579615035_init_data"], [119, 63, 124, 1], [120, 4, 124, 1, "validateType"], [120, 16, 124, 1], [120, 17, 124, 1, "__stackDetails"], [120, 31, 124, 1], [120, 34, 124, 1, "_e"], [120, 36, 124, 1], [121, 4, 124, 1], [121, 11, 124, 1, "validateType"], [121, 23, 124, 1], [122, 2, 124, 1], [122, 3, 84, 0], [123, 2, 84, 0], [123, 6, 84, 0, "_worklet_9740645922780_init_data"], [123, 38, 84, 0], [124, 4, 84, 0, "code"], [124, 8, 84, 0], [125, 4, 84, 0, "location"], [125, 12, 84, 0], [126, 4, 84, 0, "sourceMap"], [126, 13, 84, 0], [127, 4, 84, 0, "version"], [127, 11, 84, 0], [128, 2, 84, 0], [129, 2, 84, 0], [129, 6, 84, 0, "internalInterpolate"], [129, 25, 84, 0], [129, 28, 126, 0], [130, 4, 126, 0], [130, 8, 126, 0, "_e"], [130, 10, 126, 0], [130, 18, 126, 0, "global"], [130, 24, 126, 0], [130, 25, 126, 0, "Error"], [130, 30, 126, 0], [131, 4, 126, 0], [131, 8, 126, 0, "internalInterpolate"], [131, 27, 126, 0], [131, 39, 126, 0, "internalInterpolate"], [131, 40, 127, 2, "x"], [131, 41, 127, 11], [131, 43, 128, 2, "narrowedInput"], [131, 56, 128, 43], [131, 58, 129, 2, "extrapolationConfig"], [131, 77, 129, 50], [131, 79, 130, 2], [132, 6, 132, 2], [132, 10, 132, 10, "leftEdgeInput"], [132, 23, 132, 23], [132, 26, 133, 4, "narrowedInput"], [132, 39, 133, 17], [132, 40, 132, 10, "leftEdgeInput"], [132, 53, 132, 23], [133, 8, 132, 25, "rightEdgeInput"], [133, 22, 132, 39], [133, 25, 133, 4, "narrowedInput"], [133, 38, 133, 17], [133, 39, 132, 25, "rightEdgeInput"], [133, 53, 132, 39], [134, 8, 132, 41, "leftEdgeOutput"], [134, 22, 132, 55], [134, 25, 133, 4, "narrowedInput"], [134, 38, 133, 17], [134, 39, 132, 41, "leftEdgeOutput"], [134, 53, 132, 55], [135, 8, 132, 57, "rightEdgeOutput"], [135, 23, 132, 72], [135, 26, 133, 4, "narrowedInput"], [135, 39, 133, 17], [135, 40, 132, 57, "rightEdgeOutput"], [135, 55, 132, 72], [136, 6, 134, 2], [136, 10, 134, 6, "rightEdgeInput"], [136, 24, 134, 20], [136, 27, 134, 23, "leftEdgeInput"], [136, 40, 134, 36], [136, 45, 134, 41], [136, 46, 134, 42], [136, 48, 134, 44], [137, 8, 135, 4], [137, 15, 135, 11, "leftEdgeOutput"], [137, 29, 135, 25], [138, 6, 136, 2], [139, 6, 137, 2], [139, 10, 137, 8, "progress"], [139, 18, 137, 16], [139, 21, 137, 19], [139, 22, 137, 20, "x"], [139, 23, 137, 21], [139, 26, 137, 24, "leftEdgeInput"], [139, 39, 137, 37], [139, 44, 137, 42, "rightEdgeInput"], [139, 58, 137, 56], [139, 61, 137, 59, "leftEdgeInput"], [139, 74, 137, 72], [139, 75, 137, 73], [140, 6, 138, 2], [140, 10, 138, 8, "val"], [140, 13, 138, 11], [140, 16, 138, 14, "leftEdgeOutput"], [140, 30, 138, 28], [140, 33, 138, 31, "progress"], [140, 41, 138, 39], [140, 45, 138, 43, "rightEdgeOutput"], [140, 60, 138, 58], [140, 63, 138, 61, "leftEdgeOutput"], [140, 77, 138, 75], [140, 78, 138, 76], [141, 6, 139, 2], [141, 10, 139, 8, "coef"], [141, 14, 139, 12], [141, 17, 139, 15, "rightEdgeOutput"], [141, 32, 139, 30], [141, 36, 139, 34, "leftEdgeOutput"], [141, 50, 139, 48], [141, 53, 139, 51], [141, 54, 139, 52], [141, 57, 139, 55], [141, 58, 139, 56], [141, 59, 139, 57], [142, 6, 141, 2], [142, 10, 141, 6, "coef"], [142, 14, 141, 10], [142, 17, 141, 13, "val"], [142, 20, 141, 16], [142, 23, 141, 19, "coef"], [142, 27, 141, 23], [142, 30, 141, 26, "leftEdgeOutput"], [142, 44, 141, 40], [142, 46, 141, 42], [143, 8, 142, 4], [143, 15, 142, 11, "getVal"], [143, 21, 142, 17], [143, 22, 143, 6, "extrapolationConfig"], [143, 41, 143, 25], [143, 42, 143, 26, "extrapolateLeft"], [143, 57, 143, 41], [143, 59, 144, 6, "coef"], [143, 63, 144, 10], [143, 65, 145, 6, "val"], [143, 68, 145, 9], [143, 70, 146, 6, "leftEdgeOutput"], [143, 84, 146, 20], [143, 86, 147, 6, "rightEdgeOutput"], [143, 101, 147, 21], [143, 103, 148, 6, "x"], [143, 104, 149, 4], [143, 105, 149, 5], [144, 6, 150, 2], [144, 7, 150, 3], [144, 13, 150, 9], [144, 17, 150, 13, "coef"], [144, 21, 150, 17], [144, 24, 150, 20, "val"], [144, 27, 150, 23], [144, 30, 150, 26, "coef"], [144, 34, 150, 30], [144, 37, 150, 33, "rightEdgeOutput"], [144, 52, 150, 48], [144, 54, 150, 50], [145, 8, 151, 4], [145, 15, 151, 11, "getVal"], [145, 21, 151, 17], [145, 22, 152, 6, "extrapolationConfig"], [145, 41, 152, 25], [145, 42, 152, 26, "extrapolateRight"], [145, 58, 152, 42], [145, 60, 153, 6, "coef"], [145, 64, 153, 10], [145, 66, 154, 6, "val"], [145, 69, 154, 9], [145, 71, 155, 6, "leftEdgeOutput"], [145, 85, 155, 20], [145, 87, 156, 6, "rightEdgeOutput"], [145, 102, 156, 21], [145, 104, 157, 6, "x"], [145, 105, 158, 4], [145, 106, 158, 5], [146, 6, 159, 2], [147, 6, 161, 2], [147, 13, 161, 9, "val"], [147, 16, 161, 12], [148, 4, 162, 0], [148, 5, 162, 1], [149, 4, 162, 1, "internalInterpolate"], [149, 23, 162, 1], [149, 24, 162, 1, "__closure"], [149, 33, 162, 1], [150, 6, 162, 1, "getVal"], [151, 4, 162, 1], [152, 4, 162, 1, "internalInterpolate"], [152, 23, 162, 1], [152, 24, 162, 1, "__workletHash"], [152, 37, 162, 1], [153, 4, 162, 1, "internalInterpolate"], [153, 23, 162, 1], [153, 24, 162, 1, "__initData"], [153, 34, 162, 1], [153, 37, 162, 1, "_worklet_9740645922780_init_data"], [153, 69, 162, 1], [154, 4, 162, 1, "internalInterpolate"], [154, 23, 162, 1], [154, 24, 162, 1, "__stackDetails"], [154, 38, 162, 1], [154, 41, 162, 1, "_e"], [154, 43, 162, 1], [155, 4, 162, 1], [155, 11, 162, 1, "internalInterpolate"], [155, 30, 162, 1], [156, 2, 162, 1], [156, 3, 126, 0], [157, 2, 164, 0], [158, 0, 165, 0], [159, 0, 166, 0], [160, 0, 167, 0], [161, 0, 168, 0], [162, 0, 169, 0], [163, 0, 170, 0], [164, 0, 171, 0], [165, 0, 172, 0], [166, 0, 173, 0], [167, 0, 174, 0], [168, 0, 175, 0], [169, 0, 176, 0], [170, 0, 177, 0], [171, 0, 178, 0], [172, 2, 164, 0], [172, 6, 164, 0, "_worklet_10311999127907_init_data"], [172, 39, 164, 0], [173, 4, 164, 0, "code"], [173, 8, 164, 0], [174, 4, 164, 0, "location"], [174, 12, 164, 0], [175, 4, 164, 0, "sourceMap"], [175, 13, 164, 0], [176, 4, 164, 0, "version"], [176, 11, 164, 0], [177, 2, 164, 0], [178, 2, 164, 0], [178, 6, 164, 0, "interpolate"], [178, 17, 164, 0], [178, 20, 164, 0, "exports"], [178, 27, 164, 0], [178, 28, 164, 0, "interpolate"], [178, 39, 164, 0], [178, 42, 179, 7], [179, 4, 179, 7], [179, 8, 179, 7, "_e"], [179, 10, 179, 7], [179, 18, 179, 7, "global"], [179, 24, 179, 7], [179, 25, 179, 7, "Error"], [179, 30, 179, 7], [180, 4, 179, 7], [180, 8, 179, 7, "interpolate"], [180, 19, 179, 7], [180, 31, 179, 7, "interpolate"], [180, 32, 180, 2, "x"], [180, 33, 180, 11], [180, 35, 181, 2, "inputRange"], [180, 45, 181, 31], [180, 47, 182, 2, "outputRange"], [180, 58, 182, 32], [180, 60, 183, 2, "type"], [180, 64, 183, 26], [180, 66, 184, 10], [181, 6, 186, 2], [181, 10, 186, 6, "inputRange"], [181, 20, 186, 16], [181, 21, 186, 17, "length"], [181, 27, 186, 23], [181, 30, 186, 26], [181, 31, 186, 27], [181, 35, 186, 31, "outputRange"], [181, 46, 186, 42], [181, 47, 186, 43, "length"], [181, 53, 186, 49], [181, 56, 186, 52], [181, 57, 186, 53], [181, 59, 186, 55], [182, 8, 187, 4], [182, 14, 187, 10], [182, 18, 187, 14, "ReanimatedError"], [182, 41, 187, 29], [182, 42, 188, 6], [182, 117, 189, 4], [182, 118, 189, 5], [183, 6, 190, 2], [184, 6, 192, 2], [184, 10, 192, 8, "extrapolationConfig"], [184, 29, 192, 27], [184, 32, 192, 30, "validateType"], [184, 44, 192, 42], [184, 45, 192, 43, "type"], [184, 49, 192, 47], [184, 50, 192, 48], [185, 6, 193, 2], [185, 10, 193, 8, "length"], [185, 16, 193, 14], [185, 19, 193, 17, "inputRange"], [185, 29, 193, 27], [185, 30, 193, 28, "length"], [185, 36, 193, 34], [186, 6, 194, 2], [186, 10, 194, 8, "narrowedInput"], [186, 23, 194, 49], [186, 26, 194, 52], [187, 8, 195, 4, "leftEdgeInput"], [187, 21, 195, 17], [187, 23, 195, 19, "inputRange"], [187, 33, 195, 29], [187, 34, 195, 30], [187, 35, 195, 31], [187, 36, 195, 32], [188, 8, 196, 4, "rightEdgeInput"], [188, 22, 196, 18], [188, 24, 196, 20, "inputRange"], [188, 34, 196, 30], [188, 35, 196, 31], [188, 36, 196, 32], [188, 37, 196, 33], [189, 8, 197, 4, "leftEdgeOutput"], [189, 22, 197, 18], [189, 24, 197, 20, "outputRange"], [189, 35, 197, 31], [189, 36, 197, 32], [189, 37, 197, 33], [189, 38, 197, 34], [190, 8, 198, 4, "rightEdgeOutput"], [190, 23, 198, 19], [190, 25, 198, 21, "outputRange"], [190, 36, 198, 32], [190, 37, 198, 33], [190, 38, 198, 34], [191, 6, 199, 2], [191, 7, 199, 3], [192, 6, 200, 2], [192, 10, 200, 6, "length"], [192, 16, 200, 12], [192, 19, 200, 15], [192, 20, 200, 16], [192, 22, 200, 18], [193, 8, 201, 4], [193, 12, 201, 8, "x"], [193, 13, 201, 9], [193, 16, 201, 12, "inputRange"], [193, 26, 201, 22], [193, 27, 201, 23, "length"], [193, 33, 201, 29], [193, 36, 201, 32], [193, 37, 201, 33], [193, 38, 201, 34], [193, 40, 201, 36], [194, 10, 202, 6, "narrowedInput"], [194, 23, 202, 19], [194, 24, 202, 20, "leftEdgeInput"], [194, 37, 202, 33], [194, 40, 202, 36, "inputRange"], [194, 50, 202, 46], [194, 51, 202, 47, "length"], [194, 57, 202, 53], [194, 60, 202, 56], [194, 61, 202, 57], [194, 62, 202, 58], [195, 10, 203, 6, "narrowedInput"], [195, 23, 203, 19], [195, 24, 203, 20, "rightEdgeInput"], [195, 38, 203, 34], [195, 41, 203, 37, "inputRange"], [195, 51, 203, 47], [195, 52, 203, 48, "length"], [195, 58, 203, 54], [195, 61, 203, 57], [195, 62, 203, 58], [195, 63, 203, 59], [196, 10, 204, 6, "narrowedInput"], [196, 23, 204, 19], [196, 24, 204, 20, "leftEdgeOutput"], [196, 38, 204, 34], [196, 41, 204, 37, "outputRange"], [196, 52, 204, 48], [196, 53, 204, 49, "length"], [196, 59, 204, 55], [196, 62, 204, 58], [196, 63, 204, 59], [196, 64, 204, 60], [197, 10, 205, 6, "narrowedInput"], [197, 23, 205, 19], [197, 24, 205, 20, "rightEdgeOutput"], [197, 39, 205, 35], [197, 42, 205, 38, "outputRange"], [197, 53, 205, 49], [197, 54, 205, 50, "length"], [197, 60, 205, 56], [197, 63, 205, 59], [197, 64, 205, 60], [197, 65, 205, 61], [198, 8, 206, 4], [198, 9, 206, 5], [198, 15, 206, 11], [199, 10, 207, 6], [199, 15, 207, 11], [199, 19, 207, 15, "i"], [199, 20, 207, 16], [199, 23, 207, 19], [199, 24, 207, 20], [199, 26, 207, 22, "i"], [199, 27, 207, 23], [199, 30, 207, 26, "length"], [199, 36, 207, 32], [199, 38, 207, 34], [199, 40, 207, 36, "i"], [199, 41, 207, 37], [199, 43, 207, 39], [200, 12, 208, 8], [200, 16, 208, 12, "x"], [200, 17, 208, 13], [200, 21, 208, 17, "inputRange"], [200, 31, 208, 27], [200, 32, 208, 28, "i"], [200, 33, 208, 29], [200, 34, 208, 30], [200, 36, 208, 32], [201, 14, 209, 10, "narrowedInput"], [201, 27, 209, 23], [201, 28, 209, 24, "leftEdgeInput"], [201, 41, 209, 37], [201, 44, 209, 40, "inputRange"], [201, 54, 209, 50], [201, 55, 209, 51, "i"], [201, 56, 209, 52], [201, 59, 209, 55], [201, 60, 209, 56], [201, 61, 209, 57], [202, 14, 210, 10, "narrowedInput"], [202, 27, 210, 23], [202, 28, 210, 24, "rightEdgeInput"], [202, 42, 210, 38], [202, 45, 210, 41, "inputRange"], [202, 55, 210, 51], [202, 56, 210, 52, "i"], [202, 57, 210, 53], [202, 58, 210, 54], [203, 14, 211, 10, "narrowedInput"], [203, 27, 211, 23], [203, 28, 211, 24, "leftEdgeOutput"], [203, 42, 211, 38], [203, 45, 211, 41, "outputRange"], [203, 56, 211, 52], [203, 57, 211, 53, "i"], [203, 58, 211, 54], [203, 61, 211, 57], [203, 62, 211, 58], [203, 63, 211, 59], [204, 14, 212, 10, "narrowedInput"], [204, 27, 212, 23], [204, 28, 212, 24, "rightEdgeOutput"], [204, 43, 212, 39], [204, 46, 212, 42, "outputRange"], [204, 57, 212, 53], [204, 58, 212, 54, "i"], [204, 59, 212, 55], [204, 60, 212, 56], [205, 14, 213, 10], [206, 12, 214, 8], [207, 10, 215, 6], [208, 8, 216, 4], [209, 6, 217, 2], [210, 6, 219, 2], [210, 13, 219, 9, "internalInterpolate"], [210, 32, 219, 28], [210, 33, 219, 29, "x"], [210, 34, 219, 30], [210, 36, 219, 32, "narrowedInput"], [210, 49, 219, 45], [210, 51, 219, 47, "extrapolationConfig"], [210, 70, 219, 66], [210, 71, 219, 67], [211, 4, 220, 0], [211, 5, 220, 1], [212, 4, 220, 1, "interpolate"], [212, 15, 220, 1], [212, 16, 220, 1, "__closure"], [212, 25, 220, 1], [213, 6, 220, 1, "validateType"], [213, 18, 220, 1], [214, 6, 220, 1, "internalInterpolate"], [215, 4, 220, 1], [216, 4, 220, 1, "interpolate"], [216, 15, 220, 1], [216, 16, 220, 1, "__workletHash"], [216, 29, 220, 1], [217, 4, 220, 1, "interpolate"], [217, 15, 220, 1], [217, 16, 220, 1, "__initData"], [217, 26, 220, 1], [217, 29, 220, 1, "_worklet_10311999127907_init_data"], [217, 62, 220, 1], [218, 4, 220, 1, "interpolate"], [218, 15, 220, 1], [218, 16, 220, 1, "__stackDetails"], [218, 30, 220, 1], [218, 33, 220, 1, "_e"], [218, 35, 220, 1], [219, 4, 220, 1], [219, 11, 220, 1, "interpolate"], [219, 22, 220, 1], [220, 2, 220, 1], [220, 3, 179, 7], [221, 2, 222, 0], [222, 0, 223, 0], [223, 0, 224, 0], [224, 0, 225, 0], [225, 0, 226, 0], [226, 0, 227, 0], [227, 0, 228, 0], [228, 0, 229, 0], [229, 0, 230, 0], [230, 0, 231, 0], [231, 0, 232, 0], [232, 0, 233, 0], [233, 2, 222, 0], [233, 6, 222, 0, "_worklet_8339713177498_init_data"], [233, 38, 222, 0], [234, 4, 222, 0, "code"], [234, 8, 222, 0], [235, 4, 222, 0, "location"], [235, 12, 222, 0], [236, 4, 222, 0, "sourceMap"], [236, 13, 222, 0], [237, 4, 222, 0, "version"], [237, 11, 222, 0], [238, 2, 222, 0], [239, 2, 222, 0], [239, 6, 222, 0, "clamp"], [239, 11, 222, 0], [239, 14, 222, 0, "exports"], [239, 21, 222, 0], [239, 22, 222, 0, "clamp"], [239, 27, 222, 0], [239, 30, 234, 7], [240, 4, 234, 7], [240, 8, 234, 7, "_e"], [240, 10, 234, 7], [240, 18, 234, 7, "global"], [240, 24, 234, 7], [240, 25, 234, 7, "Error"], [240, 30, 234, 7], [241, 4, 234, 7], [241, 8, 234, 7, "clamp"], [241, 13, 234, 7], [241, 25, 234, 7, "clamp"], [241, 26, 234, 22, "value"], [241, 31, 234, 35], [241, 33, 234, 37, "min"], [241, 36, 234, 48], [241, 38, 234, 50, "max"], [241, 41, 234, 61], [241, 43, 234, 63], [242, 6, 236, 2], [242, 13, 236, 9, "Math"], [242, 17, 236, 13], [242, 18, 236, 14, "min"], [242, 21, 236, 17], [242, 22, 236, 18, "Math"], [242, 26, 236, 22], [242, 27, 236, 23, "max"], [242, 30, 236, 26], [242, 31, 236, 27, "value"], [242, 36, 236, 32], [242, 38, 236, 34, "min"], [242, 41, 236, 37], [242, 42, 236, 38], [242, 44, 236, 40, "max"], [242, 47, 236, 43], [242, 48, 236, 44], [243, 4, 237, 0], [243, 5, 237, 1], [244, 4, 237, 1, "clamp"], [244, 9, 237, 1], [244, 10, 237, 1, "__closure"], [244, 19, 237, 1], [245, 4, 237, 1, "clamp"], [245, 9, 237, 1], [245, 10, 237, 1, "__workletHash"], [245, 23, 237, 1], [246, 4, 237, 1, "clamp"], [246, 9, 237, 1], [246, 10, 237, 1, "__initData"], [246, 20, 237, 1], [246, 23, 237, 1, "_worklet_8339713177498_init_data"], [246, 55, 237, 1], [247, 4, 237, 1, "clamp"], [247, 9, 237, 1], [247, 10, 237, 1, "__stackDetails"], [247, 24, 237, 1], [247, 27, 237, 1, "_e"], [247, 29, 237, 1], [248, 4, 237, 1], [248, 11, 237, 1, "clamp"], [248, 16, 237, 1], [249, 2, 237, 1], [249, 3, 234, 7], [250, 0, 234, 7], [250, 3]], "functionMap": {"names": ["<global>", "getVal", "isExtrapolate", "validateType", "internalInterpolate", "interpolate", "clamp"], "mappings": "AAA;AC6C;CDsB;AEE;CFU;AGI;CHwC;AIE;CJoC;OKiB;CLyC;OMc;CNG"}}, "type": "js/module"}]}