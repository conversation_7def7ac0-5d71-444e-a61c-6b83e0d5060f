{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "./getDevServer", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 21}, "end": {"line": 15, "column": 46}}], "key": "XTiwJdMhtKrpiN8WycYhMCkLJSM=", "exportNames": ["*"]}}, {"name": "../../Network/fetch", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 42, "column": 32}, "end": {"line": 42, "column": 62}}], "key": "2GCAUMNZr18TN3s7hIFStgyp8GQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = symbolicateStackTrace;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var getDevServer = require(_dependencyMap[2], \"./getDevServer\").default;\n  function symbolicateStackTrace(_x, _x2) {\n    return _symbolicateStackTrace.apply(this, arguments);\n  }\n  function _symbolicateStackTrace() {\n    _symbolicateStackTrace = (0, _asyncToGenerator2.default)(function* (stack, extraData) {\n      var devServer = getDevServer();\n      if (!devServer.bundleLoadedFromServer) {\n        throw new Error('Bundle was not loaded from Metro.');\n      }\n      var fetch = global.fetch ?? require(_dependencyMap[3], \"../../Network/fetch\").fetch;\n      var response = yield fetch(devServer.url + 'symbolicate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          stack,\n          extraData\n        })\n      });\n      return yield response.json();\n    });\n    return _symbolicateStackTrace.apply(this, arguments);\n  }\n});", "lineCount": 35, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [8, 20, 11, 13, "symbolicateStackTrace"], [8, 41, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_asyncToGenerator2"], [9, 24, 11, 13], [9, 27, 11, 13, "_interopRequireDefault"], [9, 49, 11, 13], [9, 50, 11, 13, "require"], [9, 57, 11, 13], [9, 58, 11, 13, "_dependencyMap"], [9, 72, 11, 13], [10, 2, 15, 0], [10, 6, 15, 6, "getDevServer"], [10, 18, 15, 18], [10, 21, 15, 21, "require"], [10, 28, 15, 28], [10, 29, 15, 28, "_dependencyMap"], [10, 43, 15, 28], [10, 64, 15, 45], [10, 65, 15, 46], [10, 66, 15, 47, "default"], [10, 73, 15, 54], [11, 2, 15, 55], [11, 11, 32, 30, "symbolicateStackTrace"], [11, 32, 32, 51, "symbolicateStackTrace"], [11, 33, 32, 51, "_x"], [11, 35, 32, 51], [11, 37, 32, 51, "_x2"], [11, 40, 32, 51], [12, 4, 32, 51], [12, 11, 32, 51, "_symbolicateStackTrace"], [12, 33, 32, 51], [12, 34, 32, 51, "apply"], [12, 39, 32, 51], [12, 46, 32, 51, "arguments"], [12, 55, 32, 51], [13, 2, 32, 51], [14, 2, 32, 51], [14, 11, 32, 51, "_symbolicateStackTrace"], [14, 34, 32, 51], [15, 4, 32, 51, "_symbolicateStackTrace"], [15, 26, 32, 51], [15, 33, 32, 51, "_asyncToGenerator2"], [15, 51, 32, 51], [15, 52, 32, 51, "default"], [15, 59, 32, 51], [15, 61, 32, 15], [15, 72, 33, 2, "stack"], [15, 77, 33, 26], [15, 79, 34, 2, "extraData"], [15, 88, 34, 19], [15, 90, 35, 35], [16, 6, 36, 2], [16, 10, 36, 8, "devServer"], [16, 19, 36, 17], [16, 22, 36, 20, "getDevServer"], [16, 34, 36, 32], [16, 35, 36, 33], [16, 36, 36, 34], [17, 6, 37, 2], [17, 10, 37, 6], [17, 11, 37, 7, "devServer"], [17, 20, 37, 16], [17, 21, 37, 17, "bundleLoadedFromServer"], [17, 43, 37, 39], [17, 45, 37, 41], [18, 8, 38, 4], [18, 14, 38, 10], [18, 18, 38, 14, "Error"], [18, 23, 38, 19], [18, 24, 38, 20], [18, 59, 38, 55], [18, 60, 38, 56], [19, 6, 39, 2], [20, 6, 42, 2], [20, 10, 42, 8, "fetch"], [20, 15, 42, 13], [20, 18, 42, 16, "global"], [20, 24, 42, 22], [20, 25, 42, 23, "fetch"], [20, 30, 42, 28], [20, 34, 42, 32, "require"], [20, 41, 42, 39], [20, 42, 42, 39, "_dependencyMap"], [20, 56, 42, 39], [20, 82, 42, 61], [20, 83, 42, 62], [20, 84, 42, 63, "fetch"], [20, 89, 42, 68], [21, 6, 43, 2], [21, 10, 43, 8, "response"], [21, 18, 43, 16], [21, 27, 43, 25, "fetch"], [21, 32, 43, 30], [21, 33, 43, 31, "devServer"], [21, 42, 43, 40], [21, 43, 43, 41, "url"], [21, 46, 43, 44], [21, 49, 43, 47], [21, 62, 43, 60], [21, 64, 43, 62], [22, 8, 44, 4, "method"], [22, 14, 44, 10], [22, 16, 44, 12], [22, 22, 44, 18], [23, 8, 45, 4, "headers"], [23, 15, 45, 11], [23, 17, 45, 13], [24, 10, 46, 6], [24, 24, 46, 20], [24, 26, 46, 22], [25, 8, 47, 4], [25, 9, 47, 5], [26, 8, 48, 4, "body"], [26, 12, 48, 8], [26, 14, 48, 10, "JSON"], [26, 18, 48, 14], [26, 19, 48, 15, "stringify"], [26, 28, 48, 24], [26, 29, 48, 25], [27, 10, 48, 26, "stack"], [27, 15, 48, 31], [28, 10, 48, 33, "extraData"], [29, 8, 48, 42], [29, 9, 48, 43], [30, 6, 49, 2], [30, 7, 49, 3], [30, 8, 49, 4], [31, 6, 50, 2], [31, 19, 50, 15, "response"], [31, 27, 50, 23], [31, 28, 50, 24, "json"], [31, 32, 50, 28], [31, 33, 50, 29], [31, 34, 50, 30], [32, 4, 51, 0], [32, 5, 51, 1], [33, 4, 51, 1], [33, 11, 51, 1, "_symbolicateStackTrace"], [33, 33, 51, 1], [33, 34, 51, 1, "apply"], [33, 39, 51, 1], [33, 46, 51, 1, "arguments"], [33, 55, 51, 1], [34, 2, 51, 1], [35, 0, 51, 1], [35, 3]], "functionMap": {"names": ["<global>", "symbolicateStackTrace"], "mappings": "AAA;eC+B"}}, "type": "js/module"}]}