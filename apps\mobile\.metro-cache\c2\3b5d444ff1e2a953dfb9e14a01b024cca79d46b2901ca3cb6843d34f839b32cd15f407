{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../fabric/ScreenFooterNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 69}, "end": {"line": 3, "column": 80, "index": 149}}], "key": "N8ALY+5gyzzDdLkR0o0Xbph4rp4=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.FooterComponent = FooterComponent;\n  exports.default = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _ScreenFooterNativeComponent = _interopRequireDefault(require(_dependencyMap[2], \"../fabric/ScreenFooterNativeComponent\"));\n  var _jsxDevRuntime = require(_dependencyMap[3], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-screens\\\\src\\\\components\\\\ScreenFooter.tsx\";\n  /**\n   * Unstable API\n   */\n  function ScreenFooter(props) {\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScreenFooterNativeComponent.default, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 10\n    }, this);\n  }\n  function FooterComponent(_ref) {\n    var children = _ref.children;\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ScreenFooter, {\n      collapsable: false,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 10\n    }, this);\n  }\n  var _default = exports.default = ScreenFooter;\n});", "lineCount": 36, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireDefault"], [8, 37, 1, 0], [8, 38, 1, 0, "require"], [8, 45, 1, 0], [8, 46, 1, 0, "_dependencyMap"], [8, 60, 1, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_ScreenFooterNativeComponent"], [9, 34, 3, 0], [9, 37, 3, 0, "_interopRequireDefault"], [9, 59, 3, 0], [9, 60, 3, 0, "require"], [9, 67, 3, 0], [9, 68, 3, 0, "_dependencyMap"], [9, 82, 3, 0], [10, 2, 3, 80], [10, 6, 3, 80, "_jsxDevRuntime"], [10, 20, 3, 80], [10, 23, 3, 80, "require"], [10, 30, 3, 80], [10, 31, 3, 80, "_dependencyMap"], [10, 45, 3, 80], [11, 2, 3, 80], [11, 6, 3, 80, "_jsxFileName"], [11, 18, 3, 80], [12, 2, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 2, 8, 0], [15, 11, 8, 9, "ScreenFooter"], [15, 23, 8, 21, "ScreenFooter"], [15, 24, 8, 22, "props"], [15, 29, 8, 38], [15, 31, 8, 40], [16, 4, 9, 2], [16, 24, 9, 9], [16, 28, 9, 9, "_jsxDevRuntime"], [16, 42, 9, 9], [16, 43, 9, 9, "jsxDEV"], [16, 49, 9, 9], [16, 51, 9, 10, "_ScreenFooterNativeComponent"], [16, 79, 9, 10], [16, 80, 9, 10, "default"], [16, 87, 9, 37], [17, 6, 9, 37], [17, 9, 9, 42, "props"], [18, 4, 9, 47], [19, 6, 9, 47, "fileName"], [19, 14, 9, 47], [19, 16, 9, 47, "_jsxFileName"], [19, 28, 9, 47], [20, 6, 9, 47, "lineNumber"], [20, 16, 9, 47], [21, 6, 9, 47, "columnNumber"], [21, 18, 9, 47], [22, 4, 9, 47], [22, 11, 9, 50], [22, 12, 9, 51], [23, 2, 10, 0], [24, 2, 16, 7], [24, 11, 16, 16, "FooterComponent"], [24, 26, 16, 31, "FooterComponent"], [24, 27, 16, 31, "_ref"], [24, 31, 16, 31], [24, 33, 16, 59], [25, 4, 16, 59], [25, 8, 16, 34, "children"], [25, 16, 16, 42], [25, 19, 16, 42, "_ref"], [25, 23, 16, 42], [25, 24, 16, 34, "children"], [25, 32, 16, 42], [26, 4, 17, 2], [26, 24, 17, 9], [26, 28, 17, 9, "_jsxDevRuntime"], [26, 42, 17, 9], [26, 43, 17, 9, "jsxDEV"], [26, 49, 17, 9], [26, 51, 17, 10, "ScreenFooter"], [26, 63, 17, 22], [27, 6, 17, 23, "collapsable"], [27, 17, 17, 34], [27, 19, 17, 36], [27, 24, 17, 42], [28, 6, 17, 42, "children"], [28, 14, 17, 42], [28, 16, 17, 44, "children"], [29, 4, 17, 52], [30, 6, 17, 52, "fileName"], [30, 14, 17, 52], [30, 16, 17, 52, "_jsxFileName"], [30, 28, 17, 52], [31, 6, 17, 52, "lineNumber"], [31, 16, 17, 52], [32, 6, 17, 52, "columnNumber"], [32, 18, 17, 52], [33, 4, 17, 52], [33, 11, 17, 67], [33, 12, 17, 68], [34, 2, 18, 0], [35, 2, 18, 1], [35, 6, 18, 1, "_default"], [35, 14, 18, 1], [35, 17, 18, 1, "exports"], [35, 24, 18, 1], [35, 25, 18, 1, "default"], [35, 32, 18, 1], [35, 35, 20, 15, "ScreenFooter"], [35, 47, 20, 27], [36, 0, 20, 27], [36, 3]], "functionMap": {"names": ["<global>", "ScreenFooter", "FooterComponent"], "mappings": "AAA;ACO;CDE;OEM;CFE"}}, "type": "js/module"}]}