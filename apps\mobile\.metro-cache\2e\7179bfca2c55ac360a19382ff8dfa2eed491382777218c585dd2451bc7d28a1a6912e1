{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./BaseViewConfig", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 46}}], "key": "Jrlhp/w+forbk4/Z/JIbTDWeyqU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _BaseViewConfig = _interopRequireDefault(require(_dependencyMap[1], \"./BaseViewConfig\"));\n  var PlatformBaseViewConfig = _BaseViewConfig.default;\n  var _default = exports.default = PlatformBaseViewConfig;\n});", "lineCount": 10, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_BaseViewConfig"], [7, 21, 13, 0], [7, 24, 13, 0, "_interopRequireDefault"], [7, 46, 13, 0], [7, 47, 13, 0, "require"], [7, 54, 13, 0], [7, 55, 13, 0, "_dependencyMap"], [7, 69, 13, 0], [8, 2, 20, 0], [8, 6, 20, 6, "PlatformBaseViewConfig"], [8, 28, 20, 58], [8, 31, 20, 61, "BaseViewConfig"], [8, 54, 20, 75], [9, 2, 20, 76], [9, 6, 20, 76, "_default"], [9, 14, 20, 76], [9, 17, 20, 76, "exports"], [9, 24, 20, 76], [9, 25, 20, 76, "default"], [9, 32, 20, 76], [9, 35, 25, 15, "PlatformBaseViewConfig"], [9, 57, 25, 37], [10, 0, 25, 37], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}