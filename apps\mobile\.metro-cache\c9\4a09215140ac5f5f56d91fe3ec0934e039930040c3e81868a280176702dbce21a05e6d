{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PointerType = void 0;\n  var PointerType = exports.PointerType = /*#__PURE__*/function (PointerType) {\n    PointerType[PointerType[\"TOUCH\"] = 0] = \"TOUCH\";\n    PointerType[PointerType[\"STYLUS\"] = 1] = \"STYLUS\";\n    PointerType[PointerType[\"MOUSE\"] = 2] = \"MOUSE\";\n    PointerType[PointerType[\"KEY\"] = 3] = \"KEY\";\n    PointerType[PointerType[\"OTHER\"] = 4] = \"OTHER\";\n    return PointerType;\n  }({});\n});", "lineCount": 14, "map": [[6, 6, 1, 12, "PointerType"], [6, 17, 1, 23], [6, 20, 1, 23, "exports"], [6, 27, 1, 23], [6, 28, 1, 23, "PointerType"], [6, 39, 1, 23], [6, 65, 1, 12, "PointerType"], [6, 76, 1, 23], [7, 4, 1, 12, "PointerType"], [7, 15, 1, 23], [7, 16, 1, 12, "PointerType"], [7, 27, 1, 23], [8, 4, 1, 12, "PointerType"], [8, 15, 1, 23], [8, 16, 1, 12, "PointerType"], [8, 27, 1, 23], [9, 4, 1, 12, "PointerType"], [9, 15, 1, 23], [9, 16, 1, 12, "PointerType"], [9, 27, 1, 23], [10, 4, 1, 12, "PointerType"], [10, 15, 1, 23], [10, 16, 1, 12, "PointerType"], [10, 27, 1, 23], [11, 4, 1, 12, "PointerType"], [11, 15, 1, 23], [11, 16, 1, 12, "PointerType"], [11, 27, 1, 23], [12, 4, 1, 23], [12, 11, 1, 12, "PointerType"], [12, 22, 1, 23], [13, 2, 1, 23], [14, 0, 1, 23], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}