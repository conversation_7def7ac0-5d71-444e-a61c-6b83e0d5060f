{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 85}, "end": {"line": 2, "column": 44, "index": 129}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 130}, "end": {"line": 6, "column": 32, "index": 225}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.nativeViewProps = exports.nativeViewHandlerName = exports.nativeViewGestureHandlerProps = exports.NativeViewGestureHandler = void 0;\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[1], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[2], \"./gestureHandlerCommon\");\n  var nativeViewGestureHandlerProps = exports.nativeViewGestureHandlerProps = ['shouldActivateOnStart', 'disallowInterruption'];\n\n  /**\n   * @deprecated NativeViewGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Native()` instead.\n   */\n\n  var nativeViewProps = exports.nativeViewProps = [..._gestureHandlerCommon.baseGestureHandlerProps, ...nativeViewGestureHandlerProps];\n  var nativeViewHandlerName = exports.nativeViewHandlerName = 'NativeViewGestureHandler';\n\n  /**\n   * @deprecated NativeViewGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Native()` instead.\n   */\n\n  /**\n   * @deprecated NativeViewGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Native()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n  var NativeViewGestureHandler = exports.NativeViewGestureHandler = (0, _createHandler.default)({\n    name: nativeViewHandlerName,\n    allowedProps: nativeViewProps,\n    config: {}\n  });\n});", "lineCount": 31, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_createHandler"], [7, 20, 2, 0], [7, 23, 2, 0, "_interopRequireDefault"], [7, 45, 2, 0], [7, 46, 2, 0, "require"], [7, 53, 2, 0], [7, 54, 2, 0, "_dependencyMap"], [7, 68, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 27, 3, 0], [8, 30, 3, 0, "require"], [8, 37, 3, 0], [8, 38, 3, 0, "_dependencyMap"], [8, 52, 3, 0], [9, 2, 8, 7], [9, 6, 8, 13, "nativeViewGestureHandlerProps"], [9, 35, 8, 42], [9, 38, 8, 42, "exports"], [9, 45, 8, 42], [9, 46, 8, 42, "nativeViewGestureHandlerProps"], [9, 75, 8, 42], [9, 78, 8, 45], [9, 79, 9, 2], [9, 102, 9, 25], [9, 104, 10, 2], [9, 126, 10, 24], [9, 127, 11, 10], [11, 2, 29, 0], [12, 0, 30, 0], [13, 0, 31, 0], [15, 2, 36, 7], [15, 6, 36, 13, "nativeViewProps"], [15, 21, 36, 28], [15, 24, 36, 28, "exports"], [15, 31, 36, 28], [15, 32, 36, 28, "nativeViewProps"], [15, 47, 36, 28], [15, 50, 36, 31], [15, 51, 37, 2], [15, 54, 37, 5, "baseGestureHandlerProps"], [15, 99, 37, 28], [15, 101, 38, 2], [15, 104, 38, 5, "nativeViewGestureHandlerProps"], [15, 133, 38, 34], [15, 134, 39, 10], [16, 2, 41, 7], [16, 6, 41, 13, "nativeViewHandlerName"], [16, 27, 41, 34], [16, 30, 41, 34, "exports"], [16, 37, 41, 34], [16, 38, 41, 34, "nativeViewHandlerName"], [16, 59, 41, 34], [16, 62, 41, 37], [16, 88, 41, 63], [18, 2, 43, 0], [19, 0, 44, 0], [20, 0, 45, 0], [22, 2, 48, 0], [23, 0, 49, 0], [24, 0, 50, 0], [25, 2, 51, 0], [26, 2, 52, 7], [26, 6, 52, 13, "NativeViewGestureHandler"], [26, 30, 52, 37], [26, 33, 52, 37, "exports"], [26, 40, 52, 37], [26, 41, 52, 37, "NativeViewGestureHandler"], [26, 65, 52, 37], [26, 68, 52, 40], [26, 72, 52, 40, "createHandler"], [26, 94, 52, 53], [26, 96, 55, 2], [27, 4, 56, 2, "name"], [27, 8, 56, 6], [27, 10, 56, 8, "nativeViewHandlerName"], [27, 31, 56, 29], [28, 4, 57, 2, "allowedProps"], [28, 16, 57, 14], [28, 18, 57, 16, "nativeViewProps"], [28, 33, 57, 31], [29, 4, 58, 2, "config"], [29, 10, 58, 8], [29, 12, 58, 10], [29, 13, 58, 11], [30, 2, 59, 0], [30, 3, 59, 1], [30, 4, 59, 2], [31, 0, 59, 3], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}