{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var token = '%[a-f0-9]{2}';\n  var singleMatcher = new RegExp('(' + token + ')|([^%]+?)', 'gi');\n  var multiMatcher = new RegExp('(' + token + ')+', 'gi');\n  function decodeComponents(components, split) {\n    try {\n      // Try to decode the entire string first\n      return [decodeURIComponent(components.join(''))];\n    } catch (err) {\n      // Do nothing\n    }\n    if (components.length === 1) {\n      return components;\n    }\n    split = split || 1;\n\n    // Split the array in 2 parts\n    var left = components.slice(0, split);\n    var right = components.slice(split);\n    return Array.prototype.concat.call([], decodeComponents(left), decodeComponents(right));\n  }\n  function decode(input) {\n    try {\n      return decodeURIComponent(input);\n    } catch (err) {\n      var tokens = input.match(singleMatcher) || [];\n      for (var i = 1; i < tokens.length; i++) {\n        input = decodeComponents(tokens, i).join('');\n        tokens = input.match(singleMatcher) || [];\n      }\n      return input;\n    }\n  }\n  function customDecodeURIComponent(input) {\n    // Keep track of all the replacements and prefill the map with the `BOM`\n    var replaceMap = {\n      '%FE%FF': '\\uFFFD\\uFFFD',\n      '%FF%FE': '\\uFFFD\\uFFFD'\n    };\n    var match = multiMatcher.exec(input);\n    while (match) {\n      try {\n        // Decode as big chunks as possible\n        replaceMap[match[0]] = decodeURIComponent(match[0]);\n      } catch (err) {\n        var result = decode(match[0]);\n        if (result !== match[0]) {\n          replaceMap[match[0]] = result;\n        }\n      }\n      match = multiMatcher.exec(input);\n    }\n\n    // Add `%C2` at the end of the map to make sure it does not replace the combinator before everything else\n    replaceMap['%C2'] = '\\uFFFD';\n    var entries = Object.keys(replaceMap);\n    for (var i = 0; i < entries.length; i++) {\n      // Replace all decoded components\n      var key = entries[i];\n      input = input.replace(new RegExp(key, 'g'), replaceMap[key]);\n    }\n    return input;\n  }\n  module.exports = function (encodedURI) {\n    if (typeof encodedURI !== 'string') {\n      throw new TypeError('Expected `encodedURI` to be of type `string`, got `' + typeof encodedURI + '`');\n    }\n    try {\n      encodedURI = encodedURI.replace(/\\+/g, ' ');\n\n      // Try the built in decoder first\n      return decodeURIComponent(encodedURI);\n    } catch (err) {\n      // Fallback to a more advanced decoder\n      return customDecodeURIComponent(encodedURI);\n    }\n  };\n});", "lineCount": 80, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "token"], [4, 11, 2, 9], [4, 14, 2, 12], [4, 28, 2, 26], [5, 2, 3, 0], [5, 6, 3, 4, "singleMatcher"], [5, 19, 3, 17], [5, 22, 3, 20], [5, 26, 3, 24, "RegExp"], [5, 32, 3, 30], [5, 33, 3, 31], [5, 36, 3, 34], [5, 39, 3, 37, "token"], [5, 44, 3, 42], [5, 47, 3, 45], [5, 59, 3, 57], [5, 61, 3, 59], [5, 65, 3, 63], [5, 66, 3, 64], [6, 2, 4, 0], [6, 6, 4, 4, "multiMatcher"], [6, 18, 4, 16], [6, 21, 4, 19], [6, 25, 4, 23, "RegExp"], [6, 31, 4, 29], [6, 32, 4, 30], [6, 35, 4, 33], [6, 38, 4, 36, "token"], [6, 43, 4, 41], [6, 46, 4, 44], [6, 50, 4, 48], [6, 52, 4, 50], [6, 56, 4, 54], [6, 57, 4, 55], [7, 2, 6, 0], [7, 11, 6, 9, "decodeComponents"], [7, 27, 6, 25, "decodeComponents"], [7, 28, 6, 26, "components"], [7, 38, 6, 36], [7, 40, 6, 38, "split"], [7, 45, 6, 43], [7, 47, 6, 45], [8, 4, 7, 1], [8, 8, 7, 5], [9, 6, 8, 2], [10, 6, 9, 2], [10, 13, 9, 9], [10, 14, 9, 10, "decodeURIComponent"], [10, 32, 9, 28], [10, 33, 9, 29, "components"], [10, 43, 9, 39], [10, 44, 9, 40, "join"], [10, 48, 9, 44], [10, 49, 9, 45], [10, 51, 9, 47], [10, 52, 9, 48], [10, 53, 9, 49], [10, 54, 9, 50], [11, 4, 10, 1], [11, 5, 10, 2], [11, 6, 10, 3], [11, 13, 10, 10, "err"], [11, 16, 10, 13], [11, 18, 10, 15], [12, 6, 11, 2], [13, 4, 11, 2], [14, 4, 14, 1], [14, 8, 14, 5, "components"], [14, 18, 14, 15], [14, 19, 14, 16, "length"], [14, 25, 14, 22], [14, 30, 14, 27], [14, 31, 14, 28], [14, 33, 14, 30], [15, 6, 15, 2], [15, 13, 15, 9, "components"], [15, 23, 15, 19], [16, 4, 16, 1], [17, 4, 18, 1, "split"], [17, 9, 18, 6], [17, 12, 18, 9, "split"], [17, 17, 18, 14], [17, 21, 18, 18], [17, 22, 18, 19], [19, 4, 20, 1], [20, 4, 21, 1], [20, 8, 21, 5, "left"], [20, 12, 21, 9], [20, 15, 21, 12, "components"], [20, 25, 21, 22], [20, 26, 21, 23, "slice"], [20, 31, 21, 28], [20, 32, 21, 29], [20, 33, 21, 30], [20, 35, 21, 32, "split"], [20, 40, 21, 37], [20, 41, 21, 38], [21, 4, 22, 1], [21, 8, 22, 5, "right"], [21, 13, 22, 10], [21, 16, 22, 13, "components"], [21, 26, 22, 23], [21, 27, 22, 24, "slice"], [21, 32, 22, 29], [21, 33, 22, 30, "split"], [21, 38, 22, 35], [21, 39, 22, 36], [22, 4, 24, 1], [22, 11, 24, 8, "Array"], [22, 16, 24, 13], [22, 17, 24, 14, "prototype"], [22, 26, 24, 23], [22, 27, 24, 24, "concat"], [22, 33, 24, 30], [22, 34, 24, 31, "call"], [22, 38, 24, 35], [22, 39, 24, 36], [22, 41, 24, 38], [22, 43, 24, 40, "decodeComponents"], [22, 59, 24, 56], [22, 60, 24, 57, "left"], [22, 64, 24, 61], [22, 65, 24, 62], [22, 67, 24, 64, "decodeComponents"], [22, 83, 24, 80], [22, 84, 24, 81, "right"], [22, 89, 24, 86], [22, 90, 24, 87], [22, 91, 24, 88], [23, 2, 25, 0], [24, 2, 27, 0], [24, 11, 27, 9, "decode"], [24, 17, 27, 15, "decode"], [24, 18, 27, 16, "input"], [24, 23, 27, 21], [24, 25, 27, 23], [25, 4, 28, 1], [25, 8, 28, 5], [26, 6, 29, 2], [26, 13, 29, 9, "decodeURIComponent"], [26, 31, 29, 27], [26, 32, 29, 28, "input"], [26, 37, 29, 33], [26, 38, 29, 34], [27, 4, 30, 1], [27, 5, 30, 2], [27, 6, 30, 3], [27, 13, 30, 10, "err"], [27, 16, 30, 13], [27, 18, 30, 15], [28, 6, 31, 2], [28, 10, 31, 6, "tokens"], [28, 16, 31, 12], [28, 19, 31, 15, "input"], [28, 24, 31, 20], [28, 25, 31, 21, "match"], [28, 30, 31, 26], [28, 31, 31, 27, "singleMatcher"], [28, 44, 31, 40], [28, 45, 31, 41], [28, 49, 31, 45], [28, 51, 31, 47], [29, 6, 33, 2], [29, 11, 33, 7], [29, 15, 33, 11, "i"], [29, 16, 33, 12], [29, 19, 33, 15], [29, 20, 33, 16], [29, 22, 33, 18, "i"], [29, 23, 33, 19], [29, 26, 33, 22, "tokens"], [29, 32, 33, 28], [29, 33, 33, 29, "length"], [29, 39, 33, 35], [29, 41, 33, 37, "i"], [29, 42, 33, 38], [29, 44, 33, 40], [29, 46, 33, 42], [30, 8, 34, 3, "input"], [30, 13, 34, 8], [30, 16, 34, 11, "decodeComponents"], [30, 32, 34, 27], [30, 33, 34, 28, "tokens"], [30, 39, 34, 34], [30, 41, 34, 36, "i"], [30, 42, 34, 37], [30, 43, 34, 38], [30, 44, 34, 39, "join"], [30, 48, 34, 43], [30, 49, 34, 44], [30, 51, 34, 46], [30, 52, 34, 47], [31, 8, 36, 3, "tokens"], [31, 14, 36, 9], [31, 17, 36, 12, "input"], [31, 22, 36, 17], [31, 23, 36, 18, "match"], [31, 28, 36, 23], [31, 29, 36, 24, "singleMatcher"], [31, 42, 36, 37], [31, 43, 36, 38], [31, 47, 36, 42], [31, 49, 36, 44], [32, 6, 37, 2], [33, 6, 39, 2], [33, 13, 39, 9, "input"], [33, 18, 39, 14], [34, 4, 40, 1], [35, 2, 41, 0], [36, 2, 43, 0], [36, 11, 43, 9, "customDecodeURIComponent"], [36, 35, 43, 33, "customDecodeURIComponent"], [36, 36, 43, 34, "input"], [36, 41, 43, 39], [36, 43, 43, 41], [37, 4, 44, 1], [38, 4, 45, 1], [38, 8, 45, 5, "replaceMap"], [38, 18, 45, 15], [38, 21, 45, 18], [39, 6, 46, 2], [39, 14, 46, 10], [39, 16, 46, 12], [39, 30, 46, 26], [40, 6, 47, 2], [40, 14, 47, 10], [40, 16, 47, 12], [41, 4, 48, 1], [41, 5, 48, 2], [42, 4, 50, 1], [42, 8, 50, 5, "match"], [42, 13, 50, 10], [42, 16, 50, 13, "multiMatcher"], [42, 28, 50, 25], [42, 29, 50, 26, "exec"], [42, 33, 50, 30], [42, 34, 50, 31, "input"], [42, 39, 50, 36], [42, 40, 50, 37], [43, 4, 51, 1], [43, 11, 51, 8, "match"], [43, 16, 51, 13], [43, 18, 51, 15], [44, 6, 52, 2], [44, 10, 52, 6], [45, 8, 53, 3], [46, 8, 54, 3, "replaceMap"], [46, 18, 54, 13], [46, 19, 54, 14, "match"], [46, 24, 54, 19], [46, 25, 54, 20], [46, 26, 54, 21], [46, 27, 54, 22], [46, 28, 54, 23], [46, 31, 54, 26, "decodeURIComponent"], [46, 49, 54, 44], [46, 50, 54, 45, "match"], [46, 55, 54, 50], [46, 56, 54, 51], [46, 57, 54, 52], [46, 58, 54, 53], [46, 59, 54, 54], [47, 6, 55, 2], [47, 7, 55, 3], [47, 8, 55, 4], [47, 15, 55, 11, "err"], [47, 18, 55, 14], [47, 20, 55, 16], [48, 8, 56, 3], [48, 12, 56, 7, "result"], [48, 18, 56, 13], [48, 21, 56, 16, "decode"], [48, 27, 56, 22], [48, 28, 56, 23, "match"], [48, 33, 56, 28], [48, 34, 56, 29], [48, 35, 56, 30], [48, 36, 56, 31], [48, 37, 56, 32], [49, 8, 58, 3], [49, 12, 58, 7, "result"], [49, 18, 58, 13], [49, 23, 58, 18, "match"], [49, 28, 58, 23], [49, 29, 58, 24], [49, 30, 58, 25], [49, 31, 58, 26], [49, 33, 58, 28], [50, 10, 59, 4, "replaceMap"], [50, 20, 59, 14], [50, 21, 59, 15, "match"], [50, 26, 59, 20], [50, 27, 59, 21], [50, 28, 59, 22], [50, 29, 59, 23], [50, 30, 59, 24], [50, 33, 59, 27, "result"], [50, 39, 59, 33], [51, 8, 60, 3], [52, 6, 61, 2], [53, 6, 63, 2, "match"], [53, 11, 63, 7], [53, 14, 63, 10, "multiMatcher"], [53, 26, 63, 22], [53, 27, 63, 23, "exec"], [53, 31, 63, 27], [53, 32, 63, 28, "input"], [53, 37, 63, 33], [53, 38, 63, 34], [54, 4, 64, 1], [56, 4, 66, 1], [57, 4, 67, 1, "replaceMap"], [57, 14, 67, 11], [57, 15, 67, 12], [57, 20, 67, 17], [57, 21, 67, 18], [57, 24, 67, 21], [57, 32, 67, 29], [58, 4, 69, 1], [58, 8, 69, 5, "entries"], [58, 15, 69, 12], [58, 18, 69, 15, "Object"], [58, 24, 69, 21], [58, 25, 69, 22, "keys"], [58, 29, 69, 26], [58, 30, 69, 27, "replaceMap"], [58, 40, 69, 37], [58, 41, 69, 38], [59, 4, 71, 1], [59, 9, 71, 6], [59, 13, 71, 10, "i"], [59, 14, 71, 11], [59, 17, 71, 14], [59, 18, 71, 15], [59, 20, 71, 17, "i"], [59, 21, 71, 18], [59, 24, 71, 21, "entries"], [59, 31, 71, 28], [59, 32, 71, 29, "length"], [59, 38, 71, 35], [59, 40, 71, 37, "i"], [59, 41, 71, 38], [59, 43, 71, 40], [59, 45, 71, 42], [60, 6, 72, 2], [61, 6, 73, 2], [61, 10, 73, 6, "key"], [61, 13, 73, 9], [61, 16, 73, 12, "entries"], [61, 23, 73, 19], [61, 24, 73, 20, "i"], [61, 25, 73, 21], [61, 26, 73, 22], [62, 6, 74, 2, "input"], [62, 11, 74, 7], [62, 14, 74, 10, "input"], [62, 19, 74, 15], [62, 20, 74, 16, "replace"], [62, 27, 74, 23], [62, 28, 74, 24], [62, 32, 74, 28, "RegExp"], [62, 38, 74, 34], [62, 39, 74, 35, "key"], [62, 42, 74, 38], [62, 44, 74, 40], [62, 47, 74, 43], [62, 48, 74, 44], [62, 50, 74, 46, "replaceMap"], [62, 60, 74, 56], [62, 61, 74, 57, "key"], [62, 64, 74, 60], [62, 65, 74, 61], [62, 66, 74, 62], [63, 4, 75, 1], [64, 4, 77, 1], [64, 11, 77, 8, "input"], [64, 16, 77, 13], [65, 2, 78, 0], [66, 2, 80, 0, "module"], [66, 8, 80, 6], [66, 9, 80, 7, "exports"], [66, 16, 80, 14], [66, 19, 80, 17], [66, 29, 80, 27, "encodedURI"], [66, 39, 80, 37], [66, 41, 80, 39], [67, 4, 81, 1], [67, 8, 81, 5], [67, 15, 81, 12, "encodedURI"], [67, 25, 81, 22], [67, 30, 81, 27], [67, 38, 81, 35], [67, 40, 81, 37], [68, 6, 82, 2], [68, 12, 82, 8], [68, 16, 82, 12, "TypeError"], [68, 25, 82, 21], [68, 26, 82, 22], [68, 79, 82, 75], [68, 82, 82, 78], [68, 89, 82, 85, "encodedURI"], [68, 99, 82, 95], [68, 102, 82, 98], [68, 105, 82, 101], [68, 106, 82, 102], [69, 4, 83, 1], [70, 4, 85, 1], [70, 8, 85, 5], [71, 6, 86, 2, "encodedURI"], [71, 16, 86, 12], [71, 19, 86, 15, "encodedURI"], [71, 29, 86, 25], [71, 30, 86, 26, "replace"], [71, 37, 86, 33], [71, 38, 86, 34], [71, 43, 86, 39], [71, 45, 86, 41], [71, 48, 86, 44], [71, 49, 86, 45], [73, 6, 88, 2], [74, 6, 89, 2], [74, 13, 89, 9, "decodeURIComponent"], [74, 31, 89, 27], [74, 32, 89, 28, "encodedURI"], [74, 42, 89, 38], [74, 43, 89, 39], [75, 4, 90, 1], [75, 5, 90, 2], [75, 6, 90, 3], [75, 13, 90, 10, "err"], [75, 16, 90, 13], [75, 18, 90, 15], [76, 6, 91, 2], [77, 6, 92, 2], [77, 13, 92, 9, "customDecodeURIComponent"], [77, 37, 92, 33], [77, 38, 92, 34, "encodedURI"], [77, 48, 92, 44], [77, 49, 92, 45], [78, 4, 93, 1], [79, 2, 94, 0], [79, 3, 94, 1], [80, 0, 94, 2], [80, 3]], "functionMap": {"names": ["<global>", "decodeComponents", "decode", "customDecodeURIComponent", "module.exports"], "mappings": "AAA;ACK;CDmB;AEE;CFc;AGE;CHmC;iBIE;CJc"}}, "type": "js/module"}]}