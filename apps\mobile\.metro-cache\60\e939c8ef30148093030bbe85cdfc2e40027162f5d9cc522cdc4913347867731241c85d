{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 41}, "end": {"line": 2, "column": 37, "index": 78}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.enableExperimentalWebImplementation = enableExperimentalWebImplementation;\n  exports.enableLegacyWebImplementation = enableLegacyWebImplementation;\n  exports.isNewWebImplementationEnabled = isNewWebImplementationEnabled;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _utils = require(_dependencyMap[1], \"./utils\");\n  var useNewWebImplementation = true;\n  var getWasCalled = false;\n\n  /**\n   * @deprecated new web implementation is enabled by default. This function will be removed in Gesture Handler 3\n   */\n  function enableExperimentalWebImplementation() {\n    var _shouldEnable = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n    // NO-OP since the new implementation is now the default\n    console.warn((0, _utils.tagMessage)('New web implementation is enabled by default. This function will be removed in Gesture Handler 3.'));\n  }\n\n  /**\n   * @deprecated legacy implementation is no longer supported. This function will be removed in Gesture Handler 3\n   */\n  function enableLegacyWebImplementation() {\n    var shouldUseLegacyImplementation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n    console.warn((0, _utils.tagMessage)('Legacy web implementation is deprecated. This function will be removed in Gesture Handler 3.'));\n    if (_reactNative.Platform.OS !== 'web' || useNewWebImplementation === !shouldUseLegacyImplementation) {\n      return;\n    }\n    if (getWasCalled) {\n      console.error('Some parts of this application have already started using the new gesture handler implementation. No changes will be applied. You can try enabling legacy implementation earlier.');\n      return;\n    }\n    useNewWebImplementation = !shouldUseLegacyImplementation;\n  }\n  function isNewWebImplementationEnabled() {\n    getWasCalled = true;\n    return useNewWebImplementation;\n  }\n});", "lineCount": 41, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_reactNative"], [8, 18, 1, 0], [8, 21, 1, 0, "require"], [8, 28, 1, 0], [8, 29, 1, 0, "_dependencyMap"], [8, 43, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_utils"], [9, 12, 2, 0], [9, 15, 2, 0, "require"], [9, 22, 2, 0], [9, 23, 2, 0, "_dependencyMap"], [9, 37, 2, 0], [10, 2, 4, 0], [10, 6, 4, 4, "useNewWebImplementation"], [10, 29, 4, 27], [10, 32, 4, 30], [10, 36, 4, 34], [11, 2, 5, 0], [11, 6, 5, 4, "getWasCalled"], [11, 18, 5, 16], [11, 21, 5, 19], [11, 26, 5, 24], [13, 2, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 2, 10, 7], [16, 11, 10, 16, "enableExperimentalWebImplementation"], [16, 46, 10, 51, "enableExperimentalWebImplementation"], [16, 47, 10, 51], [16, 49, 12, 8], [17, 4, 12, 8], [17, 8, 11, 2, "_shouldEnable"], [17, 21, 11, 15], [17, 24, 11, 15, "arguments"], [17, 33, 11, 15], [17, 34, 11, 15, "length"], [17, 40, 11, 15], [17, 48, 11, 15, "arguments"], [17, 57, 11, 15], [17, 65, 11, 15, "undefined"], [17, 74, 11, 15], [17, 77, 11, 15, "arguments"], [17, 86, 11, 15], [17, 92, 11, 18], [17, 96, 11, 22], [18, 4, 13, 2], [19, 4, 14, 2, "console"], [19, 11, 14, 9], [19, 12, 14, 10, "warn"], [19, 16, 14, 14], [19, 17, 15, 4], [19, 21, 15, 4, "tagMessage"], [19, 38, 15, 14], [19, 40, 16, 6], [19, 139, 17, 4], [19, 140, 18, 2], [19, 141, 18, 3], [20, 2, 19, 0], [22, 2, 21, 0], [23, 0, 22, 0], [24, 0, 23, 0], [25, 2, 24, 7], [25, 11, 24, 16, "enableLegacyWebImplementation"], [25, 40, 24, 45, "enableLegacyWebImplementation"], [25, 41, 24, 45], [25, 43, 26, 8], [26, 4, 26, 8], [26, 8, 25, 2, "shouldUseLegacyImplementation"], [26, 37, 25, 31], [26, 40, 25, 31, "arguments"], [26, 49, 25, 31], [26, 50, 25, 31, "length"], [26, 56, 25, 31], [26, 64, 25, 31, "arguments"], [26, 73, 25, 31], [26, 81, 25, 31, "undefined"], [26, 90, 25, 31], [26, 93, 25, 31, "arguments"], [26, 102, 25, 31], [26, 108, 25, 34], [26, 112, 25, 38], [27, 4, 27, 2, "console"], [27, 11, 27, 9], [27, 12, 27, 10, "warn"], [27, 16, 27, 14], [27, 17, 28, 4], [27, 21, 28, 4, "tagMessage"], [27, 38, 28, 14], [27, 40, 29, 6], [27, 134, 30, 4], [27, 135, 31, 2], [27, 136, 31, 3], [28, 4, 33, 2], [28, 8, 34, 4, "Platform"], [28, 29, 34, 12], [28, 30, 34, 13, "OS"], [28, 32, 34, 15], [28, 37, 34, 20], [28, 42, 34, 25], [28, 46, 35, 4, "useNewWebImplementation"], [28, 69, 35, 27], [28, 74, 35, 32], [28, 75, 35, 33, "shouldUseLegacyImplementation"], [28, 104, 35, 62], [28, 106, 36, 4], [29, 6, 37, 4], [30, 4, 38, 2], [31, 4, 40, 2], [31, 8, 40, 6, "getWasCalled"], [31, 20, 40, 18], [31, 22, 40, 20], [32, 6, 41, 4, "console"], [32, 13, 41, 11], [32, 14, 41, 12, "error"], [32, 19, 41, 17], [32, 20, 42, 6], [32, 199, 43, 4], [32, 200, 43, 5], [33, 6, 44, 4], [34, 4, 45, 2], [35, 4, 47, 2, "useNewWebImplementation"], [35, 27, 47, 25], [35, 30, 47, 28], [35, 31, 47, 29, "shouldUseLegacyImplementation"], [35, 60, 47, 58], [36, 2, 48, 0], [37, 2, 50, 7], [37, 11, 50, 16, "isNewWebImplementationEnabled"], [37, 40, 50, 45, "isNewWebImplementationEnabled"], [37, 41, 50, 45], [37, 43, 50, 57], [38, 4, 51, 2, "getWasCalled"], [38, 16, 51, 14], [38, 19, 51, 17], [38, 23, 51, 21], [39, 4, 52, 2], [39, 11, 52, 9, "useNewWebImplementation"], [39, 34, 52, 32], [40, 2, 53, 0], [41, 0, 53, 1], [41, 3]], "functionMap": {"names": ["<global>", "enableExperimentalWebImplementation", "enableLegacyWebImplementation", "isNewWebImplementationEnabled"], "mappings": "AAA;OCS;CDS;OEK;CFwB;OGE;CHG"}}, "type": "js/module"}]}