{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 12, "index": 299}, "end": {"line": 12, "column": 28, "index": 315}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "use-sync-external-store/shim", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 9, "index": 326}, "end": {"line": 13, "column": 48, "index": 365}}], "key": "aKGLQ73LwOEJPCL0G86fBp+zBKQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * @license React\n   * use-sync-external-store-shim/with-selector.production.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  \"use strict\";\n\n  var React = require(_dependencyMap[0], \"react\"),\n    shim = require(_dependencyMap[1], \"use-sync-external-store/shim\");\n  function is(x, y) {\n    return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n  }\n  var objectIs = \"function\" === typeof Object.is ? Object.is : is,\n    useSyncExternalStore = shim.useSyncExternalStore,\n    useRef = React.useRef,\n    useEffect = React.useEffect,\n    useMemo = React.useMemo,\n    useDebugValue = React.useDebugValue;\n  exports.useSyncExternalStoreWithSelector = function (subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n    var instRef = useRef(null);\n    if (null === instRef.current) {\n      var inst = {\n        hasValue: !1,\n        value: null\n      };\n      instRef.current = inst;\n    } else inst = instRef.current;\n    instRef = useMemo(function () {\n      function memoizedSelector(nextSnapshot) {\n        if (!hasMemo) {\n          hasMemo = !0;\n          memoizedSnapshot = nextSnapshot;\n          nextSnapshot = selector(nextSnapshot);\n          if (void 0 !== isEqual && inst.hasValue) {\n            var currentSelection = inst.value;\n            if (isEqual(currentSelection, nextSnapshot)) return memoizedSelection = currentSelection;\n          }\n          return memoizedSelection = nextSnapshot;\n        }\n        currentSelection = memoizedSelection;\n        if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;\n        var nextSelection = selector(nextSnapshot);\n        if (void 0 !== isEqual && isEqual(currentSelection, nextSelection)) return memoizedSnapshot = nextSnapshot, currentSelection;\n        memoizedSnapshot = nextSnapshot;\n        return memoizedSelection = nextSelection;\n      }\n      var hasMemo = !1,\n        memoizedSnapshot,\n        memoizedSelection,\n        maybeGetServerSnapshot = void 0 === getServerSnapshot ? null : getServerSnapshot;\n      return [function () {\n        return memoizedSelector(getSnapshot());\n      }, null === maybeGetServerSnapshot ? void 0 : function () {\n        return memoizedSelector(maybeGetServerSnapshot());\n      }];\n    }, [getSnapshot, getServerSnapshot, selector, isEqual]);\n    var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n    useEffect(function () {\n      inst.hasValue = !0;\n      inst.value = value;\n    }, [value]);\n    useDebugValue(value);\n    return value;\n  };\n});", "lineCount": 71, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 12, 0], [14, 6, 12, 4, "React"], [14, 11, 12, 9], [14, 14, 12, 12, "require"], [14, 21, 12, 19], [14, 22, 12, 19, "_dependencyMap"], [14, 36, 12, 19], [14, 48, 12, 27], [14, 49, 12, 28], [15, 4, 13, 2, "shim"], [15, 8, 13, 6], [15, 11, 13, 9, "require"], [15, 18, 13, 16], [15, 19, 13, 16, "_dependencyMap"], [15, 33, 13, 16], [15, 68, 13, 47], [15, 69, 13, 48], [16, 2, 14, 0], [16, 11, 14, 9, "is"], [16, 13, 14, 11, "is"], [16, 14, 14, 12, "x"], [16, 15, 14, 13], [16, 17, 14, 15, "y"], [16, 18, 14, 16], [16, 20, 14, 18], [17, 4, 15, 2], [17, 11, 15, 10, "x"], [17, 12, 15, 11], [17, 17, 15, 16, "y"], [17, 18, 15, 17], [17, 23, 15, 22], [17, 24, 15, 23], [17, 29, 15, 28, "x"], [17, 30, 15, 29], [17, 34, 15, 33], [17, 35, 15, 34], [17, 38, 15, 37, "x"], [17, 39, 15, 38], [17, 44, 15, 43], [17, 45, 15, 44], [17, 48, 15, 47, "y"], [17, 49, 15, 48], [17, 50, 15, 49], [17, 54, 15, 55, "x"], [17, 55, 15, 56], [17, 60, 15, 61, "x"], [17, 61, 15, 62], [17, 65, 15, 66, "y"], [17, 66, 15, 67], [17, 71, 15, 72, "y"], [17, 72, 15, 74], [18, 2, 16, 0], [19, 2, 17, 0], [19, 6, 17, 4, "objectIs"], [19, 14, 17, 12], [19, 17, 17, 15], [19, 27, 17, 25], [19, 32, 17, 30], [19, 39, 17, 37, "Object"], [19, 45, 17, 43], [19, 46, 17, 44, "is"], [19, 48, 17, 46], [19, 51, 17, 49, "Object"], [19, 57, 17, 55], [19, 58, 17, 56, "is"], [19, 60, 17, 58], [19, 63, 17, 61, "is"], [19, 65, 17, 63], [20, 4, 18, 2, "useSyncExternalStore"], [20, 24, 18, 22], [20, 27, 18, 25, "shim"], [20, 31, 18, 29], [20, 32, 18, 30, "useSyncExternalStore"], [20, 52, 18, 50], [21, 4, 19, 2, "useRef"], [21, 10, 19, 8], [21, 13, 19, 11, "React"], [21, 18, 19, 16], [21, 19, 19, 17, "useRef"], [21, 25, 19, 23], [22, 4, 20, 2, "useEffect"], [22, 13, 20, 11], [22, 16, 20, 14, "React"], [22, 21, 20, 19], [22, 22, 20, 20, "useEffect"], [22, 31, 20, 29], [23, 4, 21, 2, "useMemo"], [23, 11, 21, 9], [23, 14, 21, 12, "React"], [23, 19, 21, 17], [23, 20, 21, 18, "useMemo"], [23, 27, 21, 25], [24, 4, 22, 2, "useDebugValue"], [24, 17, 22, 15], [24, 20, 22, 18, "React"], [24, 25, 22, 23], [24, 26, 22, 24, "useDebugValue"], [24, 39, 22, 37], [25, 2, 23, 0, "exports"], [25, 9, 23, 7], [25, 10, 23, 8, "useSyncExternalStoreWithSelector"], [25, 42, 23, 40], [25, 45, 23, 43], [25, 55, 24, 2, "subscribe"], [25, 64, 24, 11], [25, 66, 25, 2, "getSnapshot"], [25, 77, 25, 13], [25, 79, 26, 2, "getServerSnapshot"], [25, 96, 26, 19], [25, 98, 27, 2, "selector"], [25, 106, 27, 10], [25, 108, 28, 2, "isEqual"], [25, 115, 28, 9], [25, 117, 29, 2], [26, 4, 30, 2], [26, 8, 30, 6, "instRef"], [26, 15, 30, 13], [26, 18, 30, 16, "useRef"], [26, 24, 30, 22], [26, 25, 30, 23], [26, 29, 30, 27], [26, 30, 30, 28], [27, 4, 31, 2], [27, 8, 31, 6], [27, 12, 31, 10], [27, 17, 31, 15, "instRef"], [27, 24, 31, 22], [27, 25, 31, 23, "current"], [27, 32, 31, 30], [27, 34, 31, 32], [28, 6, 32, 4], [28, 10, 32, 8, "inst"], [28, 14, 32, 12], [28, 17, 32, 15], [29, 8, 32, 17, "hasValue"], [29, 16, 32, 25], [29, 18, 32, 27], [29, 19, 32, 28], [29, 20, 32, 29], [30, 8, 32, 31, "value"], [30, 13, 32, 36], [30, 15, 32, 38], [31, 6, 32, 43], [31, 7, 32, 44], [32, 6, 33, 4, "instRef"], [32, 13, 33, 11], [32, 14, 33, 12, "current"], [32, 21, 33, 19], [32, 24, 33, 22, "inst"], [32, 28, 33, 26], [33, 4, 34, 2], [33, 5, 34, 3], [33, 11, 34, 9, "inst"], [33, 15, 34, 13], [33, 18, 34, 16, "instRef"], [33, 25, 34, 23], [33, 26, 34, 24, "current"], [33, 33, 34, 31], [34, 4, 35, 2, "instRef"], [34, 11, 35, 9], [34, 14, 35, 12, "useMemo"], [34, 21, 35, 19], [34, 22, 36, 4], [34, 34, 36, 16], [35, 6, 37, 6], [35, 15, 37, 15, "memoizedSelector"], [35, 31, 37, 31, "memoizedSelector"], [35, 32, 37, 32, "nextSnapshot"], [35, 44, 37, 44], [35, 46, 37, 46], [36, 8, 38, 8], [36, 12, 38, 12], [36, 13, 38, 13, "hasMemo"], [36, 20, 38, 20], [36, 22, 38, 22], [37, 10, 39, 10, "hasMemo"], [37, 17, 39, 17], [37, 20, 39, 20], [37, 21, 39, 21], [37, 22, 39, 22], [38, 10, 40, 10, "memoizedSnapshot"], [38, 26, 40, 26], [38, 29, 40, 29, "nextSnapshot"], [38, 41, 40, 41], [39, 10, 41, 10, "nextSnapshot"], [39, 22, 41, 22], [39, 25, 41, 25, "selector"], [39, 33, 41, 33], [39, 34, 41, 34, "nextSnapshot"], [39, 46, 41, 46], [39, 47, 41, 47], [40, 10, 42, 10], [40, 14, 42, 14], [40, 19, 42, 19], [40, 20, 42, 20], [40, 25, 42, 25, "isEqual"], [40, 32, 42, 32], [40, 36, 42, 36, "inst"], [40, 40, 42, 40], [40, 41, 42, 41, "hasValue"], [40, 49, 42, 49], [40, 51, 42, 51], [41, 12, 43, 12], [41, 16, 43, 16, "currentSelection"], [41, 32, 43, 32], [41, 35, 43, 35, "inst"], [41, 39, 43, 39], [41, 40, 43, 40, "value"], [41, 45, 43, 45], [42, 12, 44, 12], [42, 16, 44, 16, "isEqual"], [42, 23, 44, 23], [42, 24, 44, 24, "currentSelection"], [42, 40, 44, 40], [42, 42, 44, 42, "nextSnapshot"], [42, 54, 44, 54], [42, 55, 44, 55], [42, 57, 45, 14], [42, 64, 45, 22, "memoizedSelection"], [42, 81, 45, 39], [42, 84, 45, 42, "currentSelection"], [42, 100, 45, 58], [43, 10, 46, 10], [44, 10, 47, 10], [44, 17, 47, 18, "memoizedSelection"], [44, 34, 47, 35], [44, 37, 47, 38, "nextSnapshot"], [44, 49, 47, 50], [45, 8, 48, 8], [46, 8, 49, 8, "currentSelection"], [46, 24, 49, 24], [46, 27, 49, 27, "memoizedSelection"], [46, 44, 49, 44], [47, 8, 50, 8], [47, 12, 50, 12, "objectIs"], [47, 20, 50, 20], [47, 21, 50, 21, "memoizedSnapshot"], [47, 37, 50, 37], [47, 39, 50, 39, "nextSnapshot"], [47, 51, 50, 51], [47, 52, 50, 52], [47, 54, 50, 54], [47, 61, 50, 61, "currentSelection"], [47, 77, 50, 77], [48, 8, 51, 8], [48, 12, 51, 12, "nextSelection"], [48, 25, 51, 25], [48, 28, 51, 28, "selector"], [48, 36, 51, 36], [48, 37, 51, 37, "nextSnapshot"], [48, 49, 51, 49], [48, 50, 51, 50], [49, 8, 52, 8], [49, 12, 52, 12], [49, 17, 52, 17], [49, 18, 52, 18], [49, 23, 52, 23, "isEqual"], [49, 30, 52, 30], [49, 34, 52, 34, "isEqual"], [49, 41, 52, 41], [49, 42, 52, 42, "currentSelection"], [49, 58, 52, 58], [49, 60, 52, 60, "nextSelection"], [49, 73, 52, 73], [49, 74, 52, 74], [49, 76, 53, 10], [49, 83, 53, 18, "memoizedSnapshot"], [49, 99, 53, 34], [49, 102, 53, 37, "nextSnapshot"], [49, 114, 53, 49], [49, 116, 53, 52, "currentSelection"], [49, 132, 53, 68], [50, 8, 54, 8, "memoizedSnapshot"], [50, 24, 54, 24], [50, 27, 54, 27, "nextSnapshot"], [50, 39, 54, 39], [51, 8, 55, 8], [51, 15, 55, 16, "memoizedSelection"], [51, 32, 55, 33], [51, 35, 55, 36, "nextSelection"], [51, 48, 55, 49], [52, 6, 56, 6], [53, 6, 57, 6], [53, 10, 57, 10, "hasMemo"], [53, 17, 57, 17], [53, 20, 57, 20], [53, 21, 57, 21], [53, 22, 57, 22], [54, 8, 58, 8, "memoizedSnapshot"], [54, 24, 58, 24], [55, 8, 59, 8, "memoizedSelection"], [55, 25, 59, 25], [56, 8, 60, 8, "maybeGetServerSnapshot"], [56, 30, 60, 30], [56, 33, 61, 10], [56, 38, 61, 15], [56, 39, 61, 16], [56, 44, 61, 21, "getServerSnapshot"], [56, 61, 61, 38], [56, 64, 61, 41], [56, 68, 61, 45], [56, 71, 61, 48, "getServerSnapshot"], [56, 88, 61, 65], [57, 6, 62, 6], [57, 13, 62, 13], [57, 14, 63, 8], [57, 26, 63, 20], [58, 8, 64, 10], [58, 15, 64, 17, "memoizedSelector"], [58, 31, 64, 33], [58, 32, 64, 34, "getSnapshot"], [58, 43, 64, 45], [58, 44, 64, 46], [58, 45, 64, 47], [58, 46, 64, 48], [59, 6, 65, 8], [59, 7, 65, 9], [59, 9, 66, 8], [59, 13, 66, 12], [59, 18, 66, 17, "maybeGetServerSnapshot"], [59, 40, 66, 39], [59, 43, 67, 12], [59, 48, 67, 17], [59, 49, 67, 18], [59, 52, 68, 12], [59, 64, 68, 24], [60, 8, 69, 14], [60, 15, 69, 21, "memoizedSelector"], [60, 31, 69, 37], [60, 32, 69, 38, "maybeGetServerSnapshot"], [60, 54, 69, 60], [60, 55, 69, 61], [60, 56, 69, 62], [60, 57, 69, 63], [61, 6, 70, 12], [61, 7, 70, 13], [61, 8, 71, 7], [62, 4, 72, 4], [62, 5, 72, 5], [62, 7, 73, 4], [62, 8, 73, 5, "getSnapshot"], [62, 19, 73, 16], [62, 21, 73, 18, "getServerSnapshot"], [62, 38, 73, 35], [62, 40, 73, 37, "selector"], [62, 48, 73, 45], [62, 50, 73, 47, "isEqual"], [62, 57, 73, 54], [62, 58, 74, 2], [62, 59, 74, 3], [63, 4, 75, 2], [63, 8, 75, 6, "value"], [63, 13, 75, 11], [63, 16, 75, 14, "useSyncExternalStore"], [63, 36, 75, 34], [63, 37, 75, 35, "subscribe"], [63, 46, 75, 44], [63, 48, 75, 46, "instRef"], [63, 55, 75, 53], [63, 56, 75, 54], [63, 57, 75, 55], [63, 58, 75, 56], [63, 60, 75, 58, "instRef"], [63, 67, 75, 65], [63, 68, 75, 66], [63, 69, 75, 67], [63, 70, 75, 68], [63, 71, 75, 69], [64, 4, 76, 2, "useEffect"], [64, 13, 76, 11], [64, 14, 77, 4], [64, 26, 77, 16], [65, 6, 78, 6, "inst"], [65, 10, 78, 10], [65, 11, 78, 11, "hasValue"], [65, 19, 78, 19], [65, 22, 78, 22], [65, 23, 78, 23], [65, 24, 78, 24], [66, 6, 79, 6, "inst"], [66, 10, 79, 10], [66, 11, 79, 11, "value"], [66, 16, 79, 16], [66, 19, 79, 19, "value"], [66, 24, 79, 24], [67, 4, 80, 4], [67, 5, 80, 5], [67, 7, 81, 4], [67, 8, 81, 5, "value"], [67, 13, 81, 10], [67, 14, 82, 2], [67, 15, 82, 3], [68, 4, 83, 2, "useDebugValue"], [68, 17, 83, 15], [68, 18, 83, 16, "value"], [68, 23, 83, 21], [68, 24, 83, 22], [69, 4, 84, 2], [69, 11, 84, 9, "value"], [69, 16, 84, 14], [70, 2, 85, 0], [70, 3, 85, 1], [71, 0, 85, 2], [71, 3]], "functionMap": {"names": ["<global>", "is", "exports.useSyncExternalStoreWithSelector", "useMemo$argument_0", "memoizedSelector", "<anonymous>", "useEffect$argument_0"], "mappings": "AAA;ACa;CDE;2CEO;ICa;MCC;ODmB;QEO;SFE;YEG;aFE;KDE;IIK;KJG;CFK"}}, "type": "js/module"}]}