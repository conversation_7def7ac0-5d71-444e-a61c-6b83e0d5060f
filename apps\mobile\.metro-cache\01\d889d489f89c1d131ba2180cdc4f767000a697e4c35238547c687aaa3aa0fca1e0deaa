{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.hasNativeConstructor = hasNativeConstructor;\n  exports.isNativeFunction = isNativeFunction;\n  function isNativeFunction(f) {\n    return typeof f === 'function' && f.toString().indexOf('[native code]') > -1;\n  }\n  function hasNativeConstructor(o, expectedName) {\n    var con = Object.getPrototypeOf(o).constructor;\n    return con.name === expectedName && isNativeFunction(con);\n  }\n});", "lineCount": 14, "map": [[7, 2, 18, 7], [7, 11, 18, 16, "isNativeFunction"], [7, 27, 18, 32, "isNativeFunction"], [7, 28, 18, 33, "f"], [7, 29, 18, 44], [7, 31, 18, 55], [8, 4, 19, 2], [8, 11, 19, 9], [8, 18, 19, 16, "f"], [8, 19, 19, 17], [8, 24, 19, 22], [8, 34, 19, 32], [8, 38, 19, 36, "f"], [8, 39, 19, 37], [8, 40, 19, 38, "toString"], [8, 48, 19, 46], [8, 49, 19, 47], [8, 50, 19, 48], [8, 51, 19, 49, "indexOf"], [8, 58, 19, 56], [8, 59, 19, 57], [8, 74, 19, 72], [8, 75, 19, 73], [8, 78, 19, 76], [8, 79, 19, 77], [8, 80, 19, 78], [9, 2, 20, 0], [10, 2, 26, 7], [10, 11, 26, 16, "hasNativeConstructor"], [10, 31, 26, 36, "hasNativeConstructor"], [10, 32, 26, 37, "o"], [10, 33, 26, 46], [10, 35, 26, 48, "expectedName"], [10, 47, 26, 68], [10, 49, 26, 79], [11, 4, 27, 2], [11, 8, 27, 8, "con"], [11, 11, 27, 11], [11, 14, 27, 14, "Object"], [11, 20, 27, 20], [11, 21, 27, 21, "getPrototypeOf"], [11, 35, 27, 35], [11, 36, 27, 36, "o"], [11, 37, 27, 37], [11, 38, 27, 38], [11, 39, 27, 39, "constructor"], [11, 50, 27, 50], [12, 4, 28, 2], [12, 11, 28, 9, "con"], [12, 14, 28, 12], [12, 15, 28, 13, "name"], [12, 19, 28, 17], [12, 24, 28, 22, "expectedName"], [12, 36, 28, 34], [12, 40, 28, 38, "isNativeFunction"], [12, 56, 28, 54], [12, 57, 28, 55, "con"], [12, 60, 28, 58], [12, 61, 28, 59], [13, 2, 29, 0], [14, 0, 29, 1], [14, 3]], "functionMap": {"names": ["<global>", "isNativeFunction", "hasNativeConstructor"], "mappings": "AAA;OCiB;CDE;OEM"}}, "type": "js/module"}]}