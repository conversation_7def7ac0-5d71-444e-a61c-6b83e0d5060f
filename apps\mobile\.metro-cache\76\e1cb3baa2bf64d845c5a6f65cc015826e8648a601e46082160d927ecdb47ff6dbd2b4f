{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "./commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 68}, "end": {"line": 3, "column": 50, "index": 118}}], "key": "+1Up2ERDMxkqzy1yjP2acBRtCSM=", "exportNames": ["*"]}}, {"name": "./errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 119}, "end": {"line": 4, "column": 43, "index": 162}}], "key": "rEld05quROH+iA6QLT6kkvqJ/qc=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 163}, "end": {"line": 5, "column": 59, "index": 222}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}, {"name": "./ReanimatedModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 223}, "end": {"line": 6, "column": 54, "index": 277}}], "key": "oecxEvQmWRmzTP60VuKAoww/f/4=", "exportNames": ["*"]}}, {"name": "./shareables", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 278}, "end": {"line": 10, "column": 22, "index": 375}}], "key": "V8GJV/2wCfEKa73+4dIdiUi/ZbE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.callMicrotasks = void 0;\n  exports.executeOnUIRuntimeSync = executeOnUIRuntimeSync;\n  exports.setupMicrotasks = exports.runOnUIImmediately = exports.runOnUI = exports.runOnJS = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _commonTypes = require(_dependencyMap[2], \"./commonTypes\");\n  var _errors = require(_dependencyMap[3], \"./errors\");\n  var _PlatformChecker = require(_dependencyMap[4], \"./PlatformChecker\");\n  var _ReanimatedModule = require(_dependencyMap[5], \"./ReanimatedModule\");\n  var _shareables = require(_dependencyMap[6], \"./shareables\");\n  var IS_JEST = (0, _PlatformChecker.isJest)();\n  var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n\n  /** An array of [worklet, args] pairs. */\n  var _runOnUIQueue = [];\n  var _worklet_3377922715333_init_data = {\n    code: \"function setupMicrotasks_threadsTs1(){let microtasksQueue=[];let isExecutingMicrotasksQueue=false;global.queueMicrotask=function(callback){microtasksQueue.push(callback);};global.__callMicrotasks=function(){if(isExecutingMicrotasksQueue){return;}try{isExecutingMicrotasksQueue=true;for(let index=0;index<microtasksQueue.length;index+=1){microtasksQueue[index]();}microtasksQueue=[];global._maybeFlushUIUpdatesQueue();}finally{isExecutingMicrotasksQueue=false;}};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\threads.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setupMicrotasks_threadsTs1\\\",\\\"microtasksQueue\\\",\\\"isExecutingMicrotasksQueue\\\",\\\"global\\\",\\\"queueMicrotask\\\",\\\"callback\\\",\\\"push\\\",\\\"__callMicrotasks\\\",\\\"index\\\",\\\"length\\\",\\\"_maybeFlushUIUpdatesQueue\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/threads.ts\\\"],\\\"mappings\\\":\\\"AAiBO,SAAAA,0BAA2BA,CAAA,EAGhC,GAAI,CAAAC,eAAkC,CAAG,EAAE,CAC3C,GAAI,CAAAC,0BAA0B,CAAG,KAAK,CACtCC,MAAM,CAACC,cAAc,CAAG,SAACC,QAAoB,CAAK,CAChDJ,eAAe,CAACK,IAAI,CAACD,QAAQ,CAAC,CAChC,CAAC,CAEDF,MAAM,CAACI,gBAAgB,CAAG,UAAM,CAC9B,GAAIL,0BAA0B,CAAE,CAC9B,OACF,CACA,GAAI,CACFA,0BAA0B,CAAG,IAAI,CACjC,IAAK,GAAI,CAAAM,KAAK,CAAG,CAAC,CAAEA,KAAK,CAAGP,eAAe,CAACQ,MAAM,CAAED,KAAK,EAAI,CAAC,CAAE,CAE9DP,eAAe,CAACO,KAAK,CAAC,CAAC,CAAC,CAC1B,CACAP,eAAe,CAAG,EAAE,CACpBE,MAAM,CAACO,yBAAyB,CAAC,CAAC,CACpC,CAAC,OAAS,CACRR,0BAA0B,CAAG,KAAK,CACpC,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var setupMicrotasks = exports.setupMicrotasks = function () {\n    var _e = [new global.Error(), 1, -27];\n    var setupMicrotasks = function () {\n      var microtasksQueue = [];\n      var isExecutingMicrotasksQueue = false;\n      global.queueMicrotask = callback => {\n        microtasksQueue.push(callback);\n      };\n      global.__callMicrotasks = () => {\n        if (isExecutingMicrotasksQueue) {\n          return;\n        }\n        try {\n          isExecutingMicrotasksQueue = true;\n          for (var index = 0; index < microtasksQueue.length; index += 1) {\n            // we use classic 'for' loop because the size of the currentTasks array may change while executing some of the callbacks due to queueMicrotask calls\n            microtasksQueue[index]();\n          }\n          microtasksQueue = [];\n          global._maybeFlushUIUpdatesQueue();\n        } finally {\n          isExecutingMicrotasksQueue = false;\n        }\n      };\n    };\n    setupMicrotasks.__closure = {};\n    setupMicrotasks.__workletHash = 3377922715333;\n    setupMicrotasks.__initData = _worklet_3377922715333_init_data;\n    setupMicrotasks.__stackDetails = _e;\n    return setupMicrotasks;\n  }();\n  var _worklet_15627757384281_init_data = {\n    code: \"function callMicrotasksOnUIThread_threadsTs2(){global.__callMicrotasks();}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\threads.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"callMicrotasksOnUIThread_threadsTs2\\\",\\\"global\\\",\\\"__callMicrotasks\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/threads.ts\\\"],\\\"mappings\\\":\\\"AA4CA,SAAAA,mCAAoCA,CAAA,EAElCC,MAAM,CAACC,gBAAgB,CAAC,CAAC,CAC3B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var callMicrotasksOnUIThread = function () {\n    var _e = [new global.Error(), 1, -27];\n    var callMicrotasksOnUIThread = function () {\n      global.__callMicrotasks();\n    };\n    callMicrotasksOnUIThread.__closure = {};\n    callMicrotasksOnUIThread.__workletHash = 15627757384281;\n    callMicrotasksOnUIThread.__initData = _worklet_15627757384281_init_data;\n    callMicrotasksOnUIThread.__stackDetails = _e;\n    return callMicrotasksOnUIThread;\n  }();\n  var callMicrotasks = exports.callMicrotasks = SHOULD_BE_USE_WEB ? () => {\n    // on web flushing is a noop as immediates are handled by the browser\n  } : callMicrotasksOnUIThread;\n\n  /**\n   * Lets you asynchronously run\n   * [workletized](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#to-workletize)\n   * functions on the [UI\n   * thread](https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI).\n   *\n   * This method does not schedule the work immediately but instead waits for\n   * other worklets to be scheduled within the same JS loop. It uses\n   * queueMicrotask to schedule all the worklets at once making sure they will run\n   * within the same frame boundaries on the UI thread.\n   *\n   * @param fun - A reference to a function you want to execute on the [UI\n   *   thread](https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI)\n   *   from the [JavaScript\n   *   thread](https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI).\n   * @returns A function that accepts arguments for the function passed as the\n   *   first argument.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI\n   */\n  // @ts-expect-error This overload is correct since it's what user sees in his code\n  // before it's transformed by Reanimated Babel plugin.\n  var _worklet_3681290945923_init_data = {\n    code: \"function runOnUI_threadsTs3(worklet){const{__DEV__,SHOULD_BE_USE_WEB,isWorkletFunction,IS_JEST,ReanimatedModule,makeShareableCloneRecursive,callMicrotasks}=this.__closure;if(__DEV__&&!SHOULD_BE_USE_WEB&&_WORKLET){throw new ReanimatedError('`runOnUI` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.');}if(__DEV__&&!SHOULD_BE_USE_WEB&&!isWorkletFunction(worklet)){throw new ReanimatedError('`runOnUI` can only be used with worklets.');}return function(...args){if(IS_JEST){ReanimatedModule.scheduleOnUI(makeShareableCloneRecursive(function(){'worklet';worklet(...args);}));return;}if(__DEV__){makeShareableCloneRecursive(worklet);makeShareableCloneRecursive(args);}_runOnUIQueue.push([worklet,args]);if(_runOnUIQueue.length===1){queueMicrotask(function(){const queue=_runOnUIQueue;_runOnUIQueue=[];ReanimatedModule.scheduleOnUI(makeShareableCloneRecursive(function(){'worklet';queue.forEach(function([worklet,args]){worklet(...args);});callMicrotasks();}));});}};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\threads.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"runOnUI_threadsTs3\\\",\\\"worklet\\\",\\\"__DEV__\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"isWorkletFunction\\\",\\\"IS_JEST\\\",\\\"ReanimatedModule\\\",\\\"makeShareableCloneRecursive\\\",\\\"callMicrotasks\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"ReanimatedError\\\",\\\"args\\\",\\\"scheduleOnUI\\\",\\\"_runOnUIQueue\\\",\\\"push\\\",\\\"length\\\",\\\"queueMicrotask\\\",\\\"queue\\\",\\\"forEach\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/threads.ts\\\"],\\\"mappings\\\":\\\"AAgFO,SAAAA,kBAEoBA,CAAAC,OAAA,QAAAC,OAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,OAAA,CAAAC,gBAAA,CAAAC,2BAAA,CAAAC,cAAA,OAAAC,SAAA,CAEzB,GAAIP,OAAO,EAAI,CAACC,iBAAiB,EAAIO,QAAQ,CAAE,CAC7C,KAAM,IAAI,CAAAC,eAAe,CACvB,kJACF,CAAC,CACH,CACA,GAAIT,OAAO,EAAI,CAACC,iBAAiB,EAAI,CAACC,iBAAiB,CAACH,OAAO,CAAC,CAAE,CAChE,KAAM,IAAI,CAAAU,eAAe,CAAC,2CAA2C,CAAC,CACxE,CACA,MAAO,UAAC,GAAGC,IAAI,CAAK,CAClB,GAAIP,OAAO,CAAE,CAUXC,gBAAgB,CAACO,YAAY,CAC3BN,2BAA2B,CAAC,UAAM,CAChC,SAAS,CACTN,OAAO,CAAC,GAAGW,IAAI,CAAC,CAClB,CAAC,CACH,CAAC,CACD,OACF,CACA,GAAIV,OAAO,CAAE,CAMXK,2BAA2B,CAACN,OAAO,CAAC,CACpCM,2BAA2B,CAACK,IAAI,CAAC,CACnC,CACAE,aAAa,CAACC,IAAI,CAAC,CAACd,OAAO,CAAqBW,IAAI,CAAC,CAAC,CACtD,GAAIE,aAAa,CAACE,MAAM,GAAK,CAAC,CAAE,CAC9BC,cAAc,CAAC,UAAM,CACnB,KAAM,CAAAC,KAAK,CAAGJ,aAAa,CAC3BA,aAAa,CAAG,EAAE,CAClBR,gBAAgB,CAACO,YAAY,CAC3BN,2BAA2B,CAAC,UAAM,CAChC,SAAS,CAETW,KAAK,CAACC,OAAO,CAAC,SAAC,CAAClB,OAAO,CAAEW,IAAI,CAAC,CAAK,CACjCX,OAAO,CAAC,GAAGW,IAAI,CAAC,CAClB,CAAC,CAAC,CACFJ,cAAc,CAAC,CAAC,CAClB,CAAC,CACH,CAAC,CACH,CAAC,CAAC,CACJ,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_8194144187976_init_data = {\n    code: \"function threadsTs4(){const{worklet,args}=this.__closure;worklet(...args);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\threads.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"threadsTs4\\\",\\\"worklet\\\",\\\"args\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/threads.ts\\\"],\\\"mappings\\\":\\\"AAwGoC,SAAAA,UAAMA,CAAA,QAAAC,OAAA,CAAAC,IAAA,OAAAC,SAAA,CAEhCF,OAAO,CAAC,GAAGC,IAAI,CAAC,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_3662355562716_init_data = {\n    code: \"function threadsTs5(){const{queue,callMicrotasks}=this.__closure;queue.forEach(function([worklet,args]){worklet(...args);});callMicrotasks();}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\threads.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"threadsTs5\\\",\\\"queue\\\",\\\"callMicrotasks\\\",\\\"__closure\\\",\\\"forEach\\\",\\\"worklet\\\",\\\"args\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/threads.ts\\\"],\\\"mappings\\\":\\\"AA8HsC,SAAAA,UAAMA,CAAA,QAAAC,KAAA,CAAAC,cAAA,OAAAC,SAAA,CAGhCF,KAAK,CAACG,OAAO,CAAC,SAAC,CAACC,OAAO,CAAEC,IAAI,CAAC,CAAK,CACjCD,OAAO,CAAC,GAAGC,IAAI,CAAC,CAClB,CAAC,CAAC,CACFJ,cAAc,CAAC,CAAC,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var runOnUI = exports.runOnUI = function () {\n    var _e = [new global.Error(), -8, -27];\n    var runOnUI = function (worklet) {\n      if (__DEV__ && !SHOULD_BE_USE_WEB && _WORKLET) {\n        throw new _errors.ReanimatedError('`runOnUI` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.');\n      }\n      if (__DEV__ && !SHOULD_BE_USE_WEB && !(0, _commonTypes.isWorkletFunction)(worklet)) {\n        throw new _errors.ReanimatedError('`runOnUI` can only be used with worklets.');\n      }\n      return function () {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (IS_JEST) {\n          // Mocking time in Jest is tricky as both requestAnimationFrame and queueMicrotask\n          // callbacks run on the same queue and can be interleaved. There is no way\n          // to flush particular queue in Jest and the only control over mocked timers\n          // is by using jest.advanceTimersByTime() method which advances all types\n          // of timers including immediate and animation callbacks. Ideally we'd like\n          // to have some way here to schedule work along with React updates, but\n          // that's not possible, and hence in Jest environment instead of using scheduling\n          // mechanism we just schedule the work ommiting the queue. This is ok for the\n          // uses that we currently have but may not be ok for future tests that we write.\n          _ReanimatedModule.ReanimatedModule.scheduleOnUI((0, _shareables.makeShareableCloneRecursive)(function () {\n            var _e = [new global.Error(), -3, -27];\n            var threadsTs4 = function () {\n              worklet(...args);\n            };\n            threadsTs4.__closure = {\n              worklet,\n              args\n            };\n            threadsTs4.__workletHash = 8194144187976;\n            threadsTs4.__initData = _worklet_8194144187976_init_data;\n            threadsTs4.__stackDetails = _e;\n            return threadsTs4;\n          }()));\n          return;\n        }\n        if (__DEV__) {\n          // in DEV mode we call shareable conversion here because in case the object\n          // can't be converted, we will get a meaningful stack-trace as opposed to the\n          // situation when conversion is only done via microtask queue. This does not\n          // make the app particularily less efficient as converted objects are cached\n          // and for a given worklet the conversion only happens once.\n          (0, _shareables.makeShareableCloneRecursive)(worklet);\n          (0, _shareables.makeShareableCloneRecursive)(args);\n        }\n        _runOnUIQueue.push([worklet, args]);\n        if (_runOnUIQueue.length === 1) {\n          queueMicrotask(() => {\n            var queue = _runOnUIQueue;\n            _runOnUIQueue = [];\n            _ReanimatedModule.ReanimatedModule.scheduleOnUI((0, _shareables.makeShareableCloneRecursive)(function () {\n              var _e = [new global.Error(), -3, -27];\n              var threadsTs5 = function () {\n                // eslint-disable-next-line @typescript-eslint/no-shadow\n                queue.forEach(_ref => {\n                  var _ref2 = (0, _slicedToArray2.default)(_ref, 2),\n                    worklet = _ref2[0],\n                    args = _ref2[1];\n                  worklet(...args);\n                });\n                callMicrotasks();\n              };\n              threadsTs5.__closure = {\n                queue,\n                callMicrotasks\n              };\n              threadsTs5.__workletHash = 3662355562716;\n              threadsTs5.__initData = _worklet_3662355562716_init_data;\n              threadsTs5.__stackDetails = _e;\n              return threadsTs5;\n            }()));\n          });\n        }\n      };\n    };\n    runOnUI.__closure = {\n      __DEV__,\n      SHOULD_BE_USE_WEB,\n      isWorkletFunction: _commonTypes.isWorkletFunction,\n      IS_JEST,\n      ReanimatedModule: _ReanimatedModule.ReanimatedModule,\n      makeShareableCloneRecursive: _shareables.makeShareableCloneRecursive,\n      callMicrotasks\n    };\n    runOnUI.__workletHash = 3681290945923;\n    runOnUI.__initData = _worklet_3681290945923_init_data;\n    runOnUI.__stackDetails = _e;\n    return runOnUI;\n  }(); // @ts-expect-error Check `executeOnUIRuntimeSync` overload above.\n  var _worklet_17019095552526_init_data = {\n    code: \"function threadsTs6(){const{worklet,args,makeShareableCloneOnUIRecursive}=this.__closure;const result=worklet(...args);return makeShareableCloneOnUIRecursive(result);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\threads.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"threadsTs6\\\",\\\"worklet\\\",\\\"args\\\",\\\"makeShareableCloneOnUIRecursive\\\",\\\"__closure\\\",\\\"result\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/threads.ts\\\"],\\\"mappings\\\":\\\"AAsJkC,SAAAA,UAAMA,CAAA,QAAAC,OAAA,CAAAC,IAAA,CAAAC,+BAAA,OAAAC,SAAA,CAEhC,KAAM,CAAAC,MAAM,CAAGJ,OAAO,CAAC,GAAGC,IAAI,CAAC,CAC/B,MAAO,CAAAC,+BAA+B,CAACE,MAAM,CAAC,CAChD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function executeOnUIRuntimeSync(worklet) {\n    return function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      return _ReanimatedModule.ReanimatedModule.executeOnUIRuntimeSync((0, _shareables.makeShareableCloneRecursive)(function () {\n        var _e = [new global.Error(), -4, -27];\n        var threadsTs6 = function () {\n          var result = worklet(...args);\n          return (0, _shareables.makeShareableCloneOnUIRecursive)(result);\n        };\n        threadsTs6.__closure = {\n          worklet,\n          args,\n          makeShareableCloneOnUIRecursive: _shareables.makeShareableCloneOnUIRecursive\n        };\n        threadsTs6.__workletHash = 17019095552526;\n        threadsTs6.__initData = _worklet_17019095552526_init_data;\n        threadsTs6.__stackDetails = _e;\n        return threadsTs6;\n      }()));\n    };\n  }\n\n  // @ts-expect-error Check `runOnUI` overload above.\n  var _worklet_7163126537089_init_data = {\n    code: \"function runOnUIImmediately_threadsTs7(worklet){const{__DEV__,SHOULD_BE_USE_WEB,isWorkletFunction,ReanimatedModule,makeShareableCloneRecursive}=this.__closure;if(__DEV__&&!SHOULD_BE_USE_WEB&&_WORKLET){throw new ReanimatedError('`runOnUIImmediately` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.');}if(__DEV__&&!SHOULD_BE_USE_WEB&&!isWorkletFunction(worklet)){throw new ReanimatedError('`runOnUIImmediately` can only be used with worklets.');}return function(...args){ReanimatedModule.scheduleOnUI(makeShareableCloneRecursive(function(){'worklet';worklet(...args);}));};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\threads.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"runOnUIImmediately_threadsTs7\\\",\\\"worklet\\\",\\\"__DEV__\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"isWorkletFunction\\\",\\\"ReanimatedModule\\\",\\\"makeShareableCloneRecursive\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"ReanimatedError\\\",\\\"args\\\",\\\"scheduleOnUI\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/threads.ts\\\"],\\\"mappings\\\":\\\"AAoKO,SAAAA,6BAEoBA,CAAAC,OAAA,QAAAC,OAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAC,2BAAA,OAAAC,SAAA,CAEzB,GAAIL,OAAO,EAAI,CAACC,iBAAiB,EAAIK,QAAQ,CAAE,CAC7C,KAAM,IAAI,CAAAC,eAAe,CACvB,6JACF,CAAC,CACH,CACA,GAAIP,OAAO,EAAI,CAACC,iBAAiB,EAAI,CAACC,iBAAiB,CAACH,OAAO,CAAC,CAAE,CAChE,KAAM,IAAI,CAAAQ,eAAe,CACvB,sDACF,CAAC,CACH,CACA,MAAO,UAAC,GAAGC,IAAI,CAAK,CAClBL,gBAAgB,CAACM,YAAY,CAC3BL,2BAA2B,CAAC,UAAM,CAChC,SAAS,CACTL,OAAO,CAAC,GAAGS,IAAI,CAAC,CAClB,CAAC,CACH,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_12333479226436_init_data = {\n    code: \"function threadsTs8(){const{worklet,args}=this.__closure;worklet(...args);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\threads.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"threadsTs8\\\",\\\"worklet\\\",\\\"args\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/threads.ts\\\"],\\\"mappings\\\":\\\"AAoLkC,SAAAA,UAAMA,CAAA,QAAAC,OAAA,CAAAC,IAAA,OAAAC,SAAA,CAEhCF,OAAO,CAAC,GAAGC,IAAI,CAAC,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  /** Schedule a worklet to execute on the UI runtime skipping batching mechanism. */\n  var runOnUIImmediately = exports.runOnUIImmediately = function () {\n    var _e = [new global.Error(), -6, -27];\n    var runOnUIImmediately = function (worklet) {\n      if (__DEV__ && !SHOULD_BE_USE_WEB && _WORKLET) {\n        throw new _errors.ReanimatedError('`runOnUIImmediately` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.');\n      }\n      if (__DEV__ && !SHOULD_BE_USE_WEB && !(0, _commonTypes.isWorkletFunction)(worklet)) {\n        throw new _errors.ReanimatedError('`runOnUIImmediately` can only be used with worklets.');\n      }\n      return function () {\n        for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n          args[_key3] = arguments[_key3];\n        }\n        _ReanimatedModule.ReanimatedModule.scheduleOnUI((0, _shareables.makeShareableCloneRecursive)(function () {\n          var _e = [new global.Error(), -3, -27];\n          var threadsTs8 = function () {\n            worklet(...args);\n          };\n          threadsTs8.__closure = {\n            worklet,\n            args\n          };\n          threadsTs8.__workletHash = 12333479226436;\n          threadsTs8.__initData = _worklet_12333479226436_init_data;\n          threadsTs8.__stackDetails = _e;\n          return threadsTs8;\n        }()));\n      };\n    };\n    runOnUIImmediately.__closure = {\n      __DEV__,\n      SHOULD_BE_USE_WEB,\n      isWorkletFunction: _commonTypes.isWorkletFunction,\n      ReanimatedModule: _ReanimatedModule.ReanimatedModule,\n      makeShareableCloneRecursive: _shareables.makeShareableCloneRecursive\n    };\n    runOnUIImmediately.__workletHash = 7163126537089;\n    runOnUIImmediately.__initData = _worklet_7163126537089_init_data;\n    runOnUIImmediately.__stackDetails = _e;\n    return runOnUIImmediately;\n  }();\n  function runWorkletOnJS(worklet) {\n    for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n      args[_key4 - 1] = arguments[_key4];\n    }\n    // remote function that calls a worklet synchronously on the JS runtime\n    worklet(...args);\n  }\n\n  /**\n   * Lets you asynchronously run\n   * non-[workletized](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#to-workletize)\n   * functions that couldn't otherwise run on the [UI\n   * thread](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#ui-thread).\n   * This applies to most external libraries as they don't have their functions\n   * marked with \"worklet\"; directive.\n   *\n   * @param fun - A reference to a function you want to execute on the JavaScript\n   *   thread from the UI thread.\n   * @returns A function that accepts arguments for the function passed as the\n   *   first argument.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnJS\n   */\n  var _worklet_6458650219678_init_data = {\n    code: \"function runOnJS_threadsTs9(fun){const runOnJS_threadsTs9=this._recur;const{SHOULD_BE_USE_WEB,isWorkletFunction,runWorkletOnJS,makeShareableCloneOnUIRecursive}=this.__closure;if(SHOULD_BE_USE_WEB||!_WORKLET){return function(...args){return queueMicrotask(args.length?function(){return fun(...args);}:fun);};}if(isWorkletFunction(fun)){return function(...args){return runOnJS_threadsTs9(runWorkletOnJS)(fun,...args);};}if(fun.__remoteFunction){fun=fun.__remoteFunction;}const scheduleOnJS=typeof fun==='function'?global._scheduleHostFunctionOnJS:global._scheduleRemoteFunctionOnJS;return function(...args){scheduleOnJS(fun,args.length>0?makeShareableCloneOnUIRecursive(args):undefined);};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\threads.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"runOnJS_threadsTs9\\\",\\\"fun\\\",\\\"_recur\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"isWorkletFunction\\\",\\\"runWorkletOnJS\\\",\\\"makeShareableCloneOnUIRecursive\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"args\\\",\\\"queueMicrotask\\\",\\\"length\\\",\\\"__remoteFunction\\\",\\\"scheduleOnJS\\\",\\\"global\\\",\\\"_scheduleHostFunctionOnJS\\\",\\\"_scheduleRemoteFunctionOnJS\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/threads.ts\\\"],\\\"mappings\\\":\\\"AA8NO,SAAAA,kBAKoBA,CAAAC,GAAA,QAAAD,kBAAA,MAAAE,MAAA,OAAAC,iBAAA,CAAAC,iBAAA,CAAAC,cAAA,CAAAC,+BAAA,OAAAC,SAAA,CAGzB,GAAIJ,iBAAiB,EAAI,CAACK,QAAQ,CAAE,CAElC,MAAO,UAAC,GAAGC,IAAI,QACb,CAAAC,cAAc,CACZD,IAAI,CAACE,MAAM,CACP,iBAAO,CAAAV,GAAG,CAAoC,GAAGQ,IAAI,CAAC,GACrDR,GACP,CAAC,GACL,CACA,GAAIG,iBAAiB,CAAoBH,GAAG,CAAC,CAAE,CAI7C,MAAO,UAAC,GAAGQ,IAAI,QACb,CAAAT,kBAAQ,CAAAK,cAEH,EAAAJ,GACJ,IAAAQ,IAAA,IACL,CACA,GAAKR,GAAG,CAAkBW,gBAAgB,CAAE,CAK1CX,GAAG,CAAIA,GAAG,CAAkBW,gBAAgB,CAC9C,CAEA,KAAM,CAAAC,YAAY,CAChB,MAAO,CAAAZ,GAAG,GAAK,UAAU,CACrBa,MAAM,CAACC,yBAAyB,CAChCD,MAAM,CAACE,2BAA2B,CAExC,MAAO,UAAC,GAAGP,IAAI,CAAK,CAClBI,YAAY,CACVZ,GAAG,CAGHQ,IAAI,CAACE,MAAM,CAAG,CAAC,CAEVL,+BAA+B,CAACG,IAAI,CAAC,CACtCQ,SACN,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var runOnJS = exports.runOnJS = function () {\n    var _e = [new global.Error(), -5, -27];\n    var runOnJS = function (fun) {\n      if (SHOULD_BE_USE_WEB || !_WORKLET) {\n        // if we are already on the JS thread, we just schedule the worklet on the JS queue\n        return function () {\n          for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n            args[_key5] = arguments[_key5];\n          }\n          return queueMicrotask(args.length ? () => fun(...args) : fun);\n        };\n      }\n      if ((0, _commonTypes.isWorkletFunction)(fun)) {\n        // If `fun` is a worklet, we schedule a call of a remote function `runWorkletOnJS`\n        // and pass the worklet as a first argument followed by original arguments.\n\n        return function () {\n          for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n            args[_key6] = arguments[_key6];\n          }\n          return runOnJS(runWorkletOnJS)(fun, ...args);\n        };\n      }\n      if (fun.__remoteFunction) {\n        // In development mode the function provided as `fun` throws an error message\n        // such that when someone accidentally calls it directly on the UI runtime, they\n        // see that they should use `runOnJS` instead. To facilitate that we put the\n        // reference to the original remote function in the `__remoteFunction` property.\n        fun = fun.__remoteFunction;\n      }\n      var scheduleOnJS = typeof fun === 'function' ? global._scheduleHostFunctionOnJS : global._scheduleRemoteFunctionOnJS;\n      return function () {\n        for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n          args[_key7] = arguments[_key7];\n        }\n        scheduleOnJS(fun, args.length > 0 ?\n        // TODO TYPESCRIPT this cast is terrible but will be fixed\n        (0, _shareables.makeShareableCloneOnUIRecursive)(args) : undefined);\n      };\n    };\n    runOnJS.__closure = {\n      SHOULD_BE_USE_WEB,\n      isWorkletFunction: _commonTypes.isWorkletFunction,\n      runWorkletOnJS,\n      makeShareableCloneOnUIRecursive: _shareables.makeShareableCloneOnUIRecursive\n    };\n    runOnJS.__workletHash = 6458650219678;\n    runOnJS.__initData = _worklet_6458650219678_init_data;\n    runOnJS.__stackDetails = _e;\n    return runOnJS;\n  }();\n});", "lineCount": 375, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "callMicrotasks"], [8, 24, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "executeOnUIRuntimeSync"], [9, 32, 1, 13], [9, 35, 1, 13, "executeOnUIRuntimeSync"], [9, 57, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "setupMicrotasks"], [10, 25, 1, 13], [10, 28, 1, 13, "exports"], [10, 35, 1, 13], [10, 36, 1, 13, "runOnUIImmediately"], [10, 54, 1, 13], [10, 57, 1, 13, "exports"], [10, 64, 1, 13], [10, 65, 1, 13, "runOnUI"], [10, 72, 1, 13], [10, 75, 1, 13, "exports"], [10, 82, 1, 13], [10, 83, 1, 13, "runOnJS"], [10, 90, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_slicedToArray2"], [11, 21, 1, 13], [11, 24, 1, 13, "_interopRequireDefault"], [11, 46, 1, 13], [11, 47, 1, 13, "require"], [11, 54, 1, 13], [11, 55, 1, 13, "_dependencyMap"], [11, 69, 1, 13], [12, 2, 3, 0], [12, 6, 3, 0, "_commonTypes"], [12, 18, 3, 0], [12, 21, 3, 0, "require"], [12, 28, 3, 0], [12, 29, 3, 0, "_dependencyMap"], [12, 43, 3, 0], [13, 2, 4, 0], [13, 6, 4, 0, "_errors"], [13, 13, 4, 0], [13, 16, 4, 0, "require"], [13, 23, 4, 0], [13, 24, 4, 0, "_dependencyMap"], [13, 38, 4, 0], [14, 2, 5, 0], [14, 6, 5, 0, "_PlatformChecker"], [14, 22, 5, 0], [14, 25, 5, 0, "require"], [14, 32, 5, 0], [14, 33, 5, 0, "_dependencyMap"], [14, 47, 5, 0], [15, 2, 6, 0], [15, 6, 6, 0, "_ReanimatedModule"], [15, 23, 6, 0], [15, 26, 6, 0, "require"], [15, 33, 6, 0], [15, 34, 6, 0, "_dependencyMap"], [15, 48, 6, 0], [16, 2, 7, 0], [16, 6, 7, 0, "_shareables"], [16, 17, 7, 0], [16, 20, 7, 0, "require"], [16, 27, 7, 0], [16, 28, 7, 0, "_dependencyMap"], [16, 42, 7, 0], [17, 2, 12, 0], [17, 6, 12, 6, "IS_JEST"], [17, 13, 12, 13], [17, 16, 12, 16], [17, 20, 12, 16, "isJest"], [17, 43, 12, 22], [17, 45, 12, 23], [17, 46, 12, 24], [18, 2, 13, 0], [18, 6, 13, 6, "SHOULD_BE_USE_WEB"], [18, 23, 13, 23], [18, 26, 13, 26], [18, 30, 13, 26, "shouldBeUseWeb"], [18, 61, 13, 40], [18, 63, 13, 41], [18, 64, 13, 42], [20, 2, 15, 0], [21, 2, 16, 0], [21, 6, 16, 4, "_runOnUIQueue"], [21, 19, 16, 74], [21, 22, 16, 77], [21, 24, 16, 79], [22, 2, 16, 80], [22, 6, 16, 80, "_worklet_3377922715333_init_data"], [22, 38, 16, 80], [23, 4, 16, 80, "code"], [23, 8, 16, 80], [24, 4, 16, 80, "location"], [24, 12, 16, 80], [25, 4, 16, 80, "sourceMap"], [25, 13, 16, 80], [26, 4, 16, 80, "version"], [26, 11, 16, 80], [27, 2, 16, 80], [28, 2, 16, 80], [28, 6, 16, 80, "setupMicrotasks"], [28, 21, 16, 80], [28, 24, 16, 80, "exports"], [28, 31, 16, 80], [28, 32, 16, 80, "setupMicrotasks"], [28, 47, 16, 80], [28, 50, 18, 7], [29, 4, 18, 7], [29, 8, 18, 7, "_e"], [29, 10, 18, 7], [29, 18, 18, 7, "global"], [29, 24, 18, 7], [29, 25, 18, 7, "Error"], [29, 30, 18, 7], [30, 4, 18, 7], [30, 8, 18, 7, "setupMicrotasks"], [30, 23, 18, 7], [30, 35, 18, 7, "setupMicrotasks"], [30, 36, 18, 7], [30, 38, 18, 34], [31, 6, 21, 2], [31, 10, 21, 6, "microtasksQueue"], [31, 25, 21, 40], [31, 28, 21, 43], [31, 30, 21, 45], [32, 6, 22, 2], [32, 10, 22, 6, "isExecutingMicrotasksQueue"], [32, 36, 22, 32], [32, 39, 22, 35], [32, 44, 22, 40], [33, 6, 23, 2, "global"], [33, 12, 23, 8], [33, 13, 23, 9, "queueMicrotask"], [33, 27, 23, 23], [33, 30, 23, 27, "callback"], [33, 38, 23, 47], [33, 42, 23, 52], [34, 8, 24, 4, "microtasksQueue"], [34, 23, 24, 19], [34, 24, 24, 20, "push"], [34, 28, 24, 24], [34, 29, 24, 25, "callback"], [34, 37, 24, 33], [34, 38, 24, 34], [35, 6, 25, 2], [35, 7, 25, 3], [36, 6, 27, 2, "global"], [36, 12, 27, 8], [36, 13, 27, 9, "__callMicrotasks"], [36, 29, 27, 25], [36, 32, 27, 28], [36, 38, 27, 34], [37, 8, 28, 4], [37, 12, 28, 8, "isExecutingMicrotasksQueue"], [37, 38, 28, 34], [37, 40, 28, 36], [38, 10, 29, 6], [39, 8, 30, 4], [40, 8, 31, 4], [40, 12, 31, 8], [41, 10, 32, 6, "isExecutingMicrotasksQueue"], [41, 36, 32, 32], [41, 39, 32, 35], [41, 43, 32, 39], [42, 10, 33, 6], [42, 15, 33, 11], [42, 19, 33, 15, "index"], [42, 24, 33, 20], [42, 27, 33, 23], [42, 28, 33, 24], [42, 30, 33, 26, "index"], [42, 35, 33, 31], [42, 38, 33, 34, "microtasksQueue"], [42, 53, 33, 49], [42, 54, 33, 50, "length"], [42, 60, 33, 56], [42, 62, 33, 58, "index"], [42, 67, 33, 63], [42, 71, 33, 67], [42, 72, 33, 68], [42, 74, 33, 70], [43, 12, 34, 8], [44, 12, 35, 8, "microtasksQueue"], [44, 27, 35, 23], [44, 28, 35, 24, "index"], [44, 33, 35, 29], [44, 34, 35, 30], [44, 35, 35, 31], [44, 36, 35, 32], [45, 10, 36, 6], [46, 10, 37, 6, "microtasksQueue"], [46, 25, 37, 21], [46, 28, 37, 24], [46, 30, 37, 26], [47, 10, 38, 6, "global"], [47, 16, 38, 12], [47, 17, 38, 13, "_maybeFlushUIUpdatesQueue"], [47, 42, 38, 38], [47, 43, 38, 39], [47, 44, 38, 40], [48, 8, 39, 4], [48, 9, 39, 5], [48, 18, 39, 14], [49, 10, 40, 6, "isExecutingMicrotasksQueue"], [49, 36, 40, 32], [49, 39, 40, 35], [49, 44, 40, 40], [50, 8, 41, 4], [51, 6, 42, 2], [51, 7, 42, 3], [52, 4, 43, 0], [52, 5, 43, 1], [53, 4, 43, 1, "setupMicrotasks"], [53, 19, 43, 1], [53, 20, 43, 1, "__closure"], [53, 29, 43, 1], [54, 4, 43, 1, "setupMicrotasks"], [54, 19, 43, 1], [54, 20, 43, 1, "__workletHash"], [54, 33, 43, 1], [55, 4, 43, 1, "setupMicrotasks"], [55, 19, 43, 1], [55, 20, 43, 1, "__initData"], [55, 30, 43, 1], [55, 33, 43, 1, "_worklet_3377922715333_init_data"], [55, 65, 43, 1], [56, 4, 43, 1, "setupMicrotasks"], [56, 19, 43, 1], [56, 20, 43, 1, "__stackDetails"], [56, 34, 43, 1], [56, 37, 43, 1, "_e"], [56, 39, 43, 1], [57, 4, 43, 1], [57, 11, 43, 1, "setupMicrotasks"], [57, 26, 43, 1], [58, 2, 43, 1], [58, 3, 18, 7], [59, 2, 18, 7], [59, 6, 18, 7, "_worklet_15627757384281_init_data"], [59, 39, 18, 7], [60, 4, 18, 7, "code"], [60, 8, 18, 7], [61, 4, 18, 7, "location"], [61, 12, 18, 7], [62, 4, 18, 7, "sourceMap"], [62, 13, 18, 7], [63, 4, 18, 7, "version"], [63, 11, 18, 7], [64, 2, 18, 7], [65, 2, 18, 7], [65, 6, 18, 7, "callMicrotasksOnUIThread"], [65, 30, 18, 7], [65, 33, 45, 0], [66, 4, 45, 0], [66, 8, 45, 0, "_e"], [66, 10, 45, 0], [66, 18, 45, 0, "global"], [66, 24, 45, 0], [66, 25, 45, 0, "Error"], [66, 30, 45, 0], [67, 4, 45, 0], [67, 8, 45, 0, "callMicrotasksOnUIThread"], [67, 32, 45, 0], [67, 44, 45, 0, "callMicrotasksOnUIThread"], [67, 45, 45, 0], [67, 47, 45, 36], [68, 6, 47, 2, "global"], [68, 12, 47, 8], [68, 13, 47, 9, "__callMicrotasks"], [68, 29, 47, 25], [68, 30, 47, 26], [68, 31, 47, 27], [69, 4, 48, 0], [69, 5, 48, 1], [70, 4, 48, 1, "callMicrotasksOnUIThread"], [70, 28, 48, 1], [70, 29, 48, 1, "__closure"], [70, 38, 48, 1], [71, 4, 48, 1, "callMicrotasksOnUIThread"], [71, 28, 48, 1], [71, 29, 48, 1, "__workletHash"], [71, 42, 48, 1], [72, 4, 48, 1, "callMicrotasksOnUIThread"], [72, 28, 48, 1], [72, 29, 48, 1, "__initData"], [72, 39, 48, 1], [72, 42, 48, 1, "_worklet_15627757384281_init_data"], [72, 75, 48, 1], [73, 4, 48, 1, "callMicrotasksOnUIThread"], [73, 28, 48, 1], [73, 29, 48, 1, "__stackDetails"], [73, 43, 48, 1], [73, 46, 48, 1, "_e"], [73, 48, 48, 1], [74, 4, 48, 1], [74, 11, 48, 1, "callMicrotasksOnUIThread"], [74, 35, 48, 1], [75, 2, 48, 1], [75, 3, 45, 0], [76, 2, 50, 7], [76, 6, 50, 13, "callMicrotasks"], [76, 20, 50, 27], [76, 23, 50, 27, "exports"], [76, 30, 50, 27], [76, 31, 50, 27, "callMicrotasks"], [76, 45, 50, 27], [76, 48, 50, 30, "SHOULD_BE_USE_WEB"], [76, 65, 50, 47], [76, 68, 51, 4], [76, 74, 51, 10], [77, 4, 52, 6], [78, 2, 52, 6], [78, 3, 53, 5], [78, 6, 54, 4, "callMicrotasksOnUIThread"], [78, 30, 54, 28], [80, 2, 56, 0], [81, 0, 57, 0], [82, 0, 58, 0], [83, 0, 59, 0], [84, 0, 60, 0], [85, 0, 61, 0], [86, 0, 62, 0], [87, 0, 63, 0], [88, 0, 64, 0], [89, 0, 65, 0], [90, 0, 66, 0], [91, 0, 67, 0], [92, 0, 68, 0], [93, 0, 69, 0], [94, 0, 70, 0], [95, 0, 71, 0], [96, 0, 72, 0], [97, 0, 73, 0], [98, 0, 74, 0], [99, 2, 75, 0], [100, 2, 76, 0], [101, 2, 76, 0], [101, 6, 76, 0, "_worklet_3681290945923_init_data"], [101, 38, 76, 0], [102, 4, 76, 0, "code"], [102, 8, 76, 0], [103, 4, 76, 0, "location"], [103, 12, 76, 0], [104, 4, 76, 0, "sourceMap"], [104, 13, 76, 0], [105, 4, 76, 0, "version"], [105, 11, 76, 0], [106, 2, 76, 0], [107, 2, 76, 0], [107, 6, 76, 0, "_worklet_8194144187976_init_data"], [107, 38, 76, 0], [108, 4, 76, 0, "code"], [108, 8, 76, 0], [109, 4, 76, 0, "location"], [109, 12, 76, 0], [110, 4, 76, 0, "sourceMap"], [110, 13, 76, 0], [111, 4, 76, 0, "version"], [111, 11, 76, 0], [112, 2, 76, 0], [113, 2, 76, 0], [113, 6, 76, 0, "_worklet_3662355562716_init_data"], [113, 38, 76, 0], [114, 4, 76, 0, "code"], [114, 8, 76, 0], [115, 4, 76, 0, "location"], [115, 12, 76, 0], [116, 4, 76, 0, "sourceMap"], [116, 13, 76, 0], [117, 4, 76, 0, "version"], [117, 11, 76, 0], [118, 2, 76, 0], [119, 2, 76, 0], [119, 6, 76, 0, "runOnUI"], [119, 13, 76, 0], [119, 16, 76, 0, "exports"], [119, 23, 76, 0], [119, 24, 76, 0, "runOnUI"], [119, 31, 76, 0], [119, 34, 81, 7], [120, 4, 81, 7], [120, 8, 81, 7, "_e"], [120, 10, 81, 7], [120, 18, 81, 7, "global"], [120, 24, 81, 7], [120, 25, 81, 7, "Error"], [120, 30, 81, 7], [121, 4, 81, 7], [121, 8, 81, 7, "runOnUI"], [121, 15, 81, 7], [121, 27, 81, 7, "runOnUI"], [121, 28, 82, 2, "worklet"], [121, 35, 82, 45], [121, 37, 83, 27], [122, 6, 85, 2], [122, 10, 85, 6, "__DEV__"], [122, 17, 85, 13], [122, 21, 85, 17], [122, 22, 85, 18, "SHOULD_BE_USE_WEB"], [122, 39, 85, 35], [122, 43, 85, 39, "_WORKLET"], [122, 51, 85, 47], [122, 53, 85, 49], [123, 8, 86, 4], [123, 14, 86, 10], [123, 18, 86, 14, "ReanimatedError"], [123, 41, 86, 29], [123, 42, 87, 6], [123, 188, 88, 4], [123, 189, 88, 5], [124, 6, 89, 2], [125, 6, 90, 2], [125, 10, 90, 6, "__DEV__"], [125, 17, 90, 13], [125, 21, 90, 17], [125, 22, 90, 18, "SHOULD_BE_USE_WEB"], [125, 39, 90, 35], [125, 43, 90, 39], [125, 44, 90, 40], [125, 48, 90, 40, "isWorkletFunction"], [125, 78, 90, 57], [125, 80, 90, 58, "worklet"], [125, 87, 90, 65], [125, 88, 90, 66], [125, 90, 90, 68], [126, 8, 91, 4], [126, 14, 91, 10], [126, 18, 91, 14, "ReanimatedError"], [126, 41, 91, 29], [126, 42, 91, 30], [126, 85, 91, 73], [126, 86, 91, 74], [127, 6, 92, 2], [128, 6, 93, 2], [128, 13, 93, 9], [128, 25, 93, 22], [129, 8, 93, 22], [129, 17, 93, 22, "_len"], [129, 21, 93, 22], [129, 24, 93, 22, "arguments"], [129, 33, 93, 22], [129, 34, 93, 22, "length"], [129, 40, 93, 22], [129, 42, 93, 13, "args"], [129, 46, 93, 17], [129, 53, 93, 17, "Array"], [129, 58, 93, 17], [129, 59, 93, 17, "_len"], [129, 63, 93, 17], [129, 66, 93, 17, "_key"], [129, 70, 93, 17], [129, 76, 93, 17, "_key"], [129, 80, 93, 17], [129, 83, 93, 17, "_len"], [129, 87, 93, 17], [129, 89, 93, 17, "_key"], [129, 93, 93, 17], [130, 10, 93, 13, "args"], [130, 14, 93, 17], [130, 15, 93, 17, "_key"], [130, 19, 93, 17], [130, 23, 93, 17, "arguments"], [130, 32, 93, 17], [130, 33, 93, 17, "_key"], [130, 37, 93, 17], [131, 8, 93, 17], [132, 8, 94, 4], [132, 12, 94, 8, "IS_JEST"], [132, 19, 94, 15], [132, 21, 94, 17], [133, 10, 95, 6], [134, 10, 96, 6], [135, 10, 97, 6], [136, 10, 98, 6], [137, 10, 99, 6], [138, 10, 100, 6], [139, 10, 101, 6], [140, 10, 102, 6], [141, 10, 103, 6], [142, 10, 104, 6, "ReanimatedModule"], [142, 44, 104, 22], [142, 45, 104, 23, "scheduleOnUI"], [142, 57, 104, 35], [142, 58, 105, 8], [142, 62, 105, 8, "makeShareableCloneRecursive"], [142, 101, 105, 35], [142, 103, 105, 36], [143, 12, 105, 36], [143, 16, 105, 36, "_e"], [143, 18, 105, 36], [143, 26, 105, 36, "global"], [143, 32, 105, 36], [143, 33, 105, 36, "Error"], [143, 38, 105, 36], [144, 12, 105, 36], [144, 16, 105, 36, "threadsTs4"], [144, 26, 105, 36], [144, 38, 105, 36, "threadsTs4"], [144, 39, 105, 36], [144, 41, 105, 42], [145, 14, 107, 10, "worklet"], [145, 21, 107, 17], [145, 22, 107, 18], [145, 25, 107, 21, "args"], [145, 29, 107, 25], [145, 30, 107, 26], [146, 12, 108, 8], [146, 13, 108, 9], [147, 12, 108, 9, "threadsTs4"], [147, 22, 108, 9], [147, 23, 108, 9, "__closure"], [147, 32, 108, 9], [148, 14, 108, 9, "worklet"], [148, 21, 108, 9], [149, 14, 108, 9, "args"], [150, 12, 108, 9], [151, 12, 108, 9, "threadsTs4"], [151, 22, 108, 9], [151, 23, 108, 9, "__workletHash"], [151, 36, 108, 9], [152, 12, 108, 9, "threadsTs4"], [152, 22, 108, 9], [152, 23, 108, 9, "__initData"], [152, 33, 108, 9], [152, 36, 108, 9, "_worklet_8194144187976_init_data"], [152, 68, 108, 9], [153, 12, 108, 9, "threadsTs4"], [153, 22, 108, 9], [153, 23, 108, 9, "__stackDetails"], [153, 37, 108, 9], [153, 40, 108, 9, "_e"], [153, 42, 108, 9], [154, 12, 108, 9], [154, 19, 108, 9, "threadsTs4"], [154, 29, 108, 9], [155, 10, 108, 9], [155, 11, 105, 36], [155, 13, 108, 9], [155, 14, 109, 6], [155, 15, 109, 7], [156, 10, 110, 6], [157, 8, 111, 4], [158, 8, 112, 4], [158, 12, 112, 8, "__DEV__"], [158, 19, 112, 15], [158, 21, 112, 17], [159, 10, 113, 6], [160, 10, 114, 6], [161, 10, 115, 6], [162, 10, 116, 6], [163, 10, 117, 6], [164, 10, 118, 6], [164, 14, 118, 6, "makeShareableCloneRecursive"], [164, 53, 118, 33], [164, 55, 118, 34, "worklet"], [164, 62, 118, 41], [164, 63, 118, 42], [165, 10, 119, 6], [165, 14, 119, 6, "makeShareableCloneRecursive"], [165, 53, 119, 33], [165, 55, 119, 34, "args"], [165, 59, 119, 38], [165, 60, 119, 39], [166, 8, 120, 4], [167, 8, 121, 4, "_runOnUIQueue"], [167, 21, 121, 17], [167, 22, 121, 18, "push"], [167, 26, 121, 22], [167, 27, 121, 23], [167, 28, 121, 24, "worklet"], [167, 35, 121, 31], [167, 37, 121, 52, "args"], [167, 41, 121, 56], [167, 42, 121, 57], [167, 43, 121, 58], [168, 8, 122, 4], [168, 12, 122, 8, "_runOnUIQueue"], [168, 25, 122, 21], [168, 26, 122, 22, "length"], [168, 32, 122, 28], [168, 37, 122, 33], [168, 38, 122, 34], [168, 40, 122, 36], [169, 10, 123, 6, "queueMicrotask"], [169, 24, 123, 20], [169, 25, 123, 21], [169, 31, 123, 27], [170, 12, 124, 8], [170, 16, 124, 14, "queue"], [170, 21, 124, 19], [170, 24, 124, 22, "_runOnUIQueue"], [170, 37, 124, 35], [171, 12, 125, 8, "_runOnUIQueue"], [171, 25, 125, 21], [171, 28, 125, 24], [171, 30, 125, 26], [172, 12, 126, 8, "ReanimatedModule"], [172, 46, 126, 24], [172, 47, 126, 25, "scheduleOnUI"], [172, 59, 126, 37], [172, 60, 127, 10], [172, 64, 127, 10, "makeShareableCloneRecursive"], [172, 103, 127, 37], [172, 105, 127, 38], [173, 14, 127, 38], [173, 18, 127, 38, "_e"], [173, 20, 127, 38], [173, 28, 127, 38, "global"], [173, 34, 127, 38], [173, 35, 127, 38, "Error"], [173, 40, 127, 38], [174, 14, 127, 38], [174, 18, 127, 38, "threadsTs5"], [174, 28, 127, 38], [174, 40, 127, 38, "threadsTs5"], [174, 41, 127, 38], [174, 43, 127, 44], [175, 16, 129, 12], [176, 16, 130, 12, "queue"], [176, 21, 130, 17], [176, 22, 130, 18, "for<PERSON>ach"], [176, 29, 130, 25], [176, 30, 130, 26, "_ref"], [176, 34, 130, 26], [176, 38, 130, 47], [177, 18, 130, 47], [177, 22, 130, 47, "_ref2"], [177, 27, 130, 47], [177, 34, 130, 47, "_slicedToArray2"], [177, 49, 130, 47], [177, 50, 130, 47, "default"], [177, 57, 130, 47], [177, 59, 130, 47, "_ref"], [177, 63, 130, 47], [178, 20, 130, 28, "worklet"], [178, 27, 130, 35], [178, 30, 130, 35, "_ref2"], [178, 35, 130, 35], [179, 20, 130, 37, "args"], [179, 24, 130, 41], [179, 27, 130, 41, "_ref2"], [179, 32, 130, 41], [180, 18, 131, 14, "worklet"], [180, 25, 131, 21], [180, 26, 131, 22], [180, 29, 131, 25, "args"], [180, 33, 131, 29], [180, 34, 131, 30], [181, 16, 132, 12], [181, 17, 132, 13], [181, 18, 132, 14], [182, 16, 133, 12, "callMicrotasks"], [182, 30, 133, 26], [182, 31, 133, 27], [182, 32, 133, 28], [183, 14, 134, 10], [183, 15, 134, 11], [184, 14, 134, 11, "threadsTs5"], [184, 24, 134, 11], [184, 25, 134, 11, "__closure"], [184, 34, 134, 11], [185, 16, 134, 11, "queue"], [185, 21, 134, 11], [186, 16, 134, 11, "callMicrotasks"], [187, 14, 134, 11], [188, 14, 134, 11, "threadsTs5"], [188, 24, 134, 11], [188, 25, 134, 11, "__workletHash"], [188, 38, 134, 11], [189, 14, 134, 11, "threadsTs5"], [189, 24, 134, 11], [189, 25, 134, 11, "__initData"], [189, 35, 134, 11], [189, 38, 134, 11, "_worklet_3662355562716_init_data"], [189, 70, 134, 11], [190, 14, 134, 11, "threadsTs5"], [190, 24, 134, 11], [190, 25, 134, 11, "__stackDetails"], [190, 39, 134, 11], [190, 42, 134, 11, "_e"], [190, 44, 134, 11], [191, 14, 134, 11], [191, 21, 134, 11, "threadsTs5"], [191, 31, 134, 11], [192, 12, 134, 11], [192, 13, 127, 38], [192, 15, 134, 11], [192, 16, 135, 8], [192, 17, 135, 9], [193, 10, 136, 6], [193, 11, 136, 7], [193, 12, 136, 8], [194, 8, 137, 4], [195, 6, 138, 2], [195, 7, 138, 3], [196, 4, 139, 0], [196, 5, 139, 1], [197, 4, 139, 1, "runOnUI"], [197, 11, 139, 1], [197, 12, 139, 1, "__closure"], [197, 21, 139, 1], [198, 6, 139, 1, "__DEV__"], [198, 13, 139, 1], [199, 6, 139, 1, "SHOULD_BE_USE_WEB"], [199, 23, 139, 1], [200, 6, 139, 1, "isWorkletFunction"], [200, 23, 139, 1], [200, 25, 90, 40, "isWorkletFunction"], [200, 55, 90, 57], [201, 6, 90, 57, "IS_JEST"], [201, 13, 90, 57], [202, 6, 90, 57, "ReanimatedModule"], [202, 22, 90, 57], [202, 24, 104, 6, "ReanimatedModule"], [202, 58, 104, 22], [203, 6, 104, 22, "makeShareableCloneRecursive"], [203, 33, 104, 22], [203, 35, 105, 8, "makeShareableCloneRecursive"], [203, 74, 105, 35], [204, 6, 105, 35, "callMicrotasks"], [205, 4, 105, 35], [206, 4, 105, 35, "runOnUI"], [206, 11, 105, 35], [206, 12, 105, 35, "__workletHash"], [206, 25, 105, 35], [207, 4, 105, 35, "runOnUI"], [207, 11, 105, 35], [207, 12, 105, 35, "__initData"], [207, 22, 105, 35], [207, 25, 105, 35, "_worklet_3681290945923_init_data"], [207, 57, 105, 35], [208, 4, 105, 35, "runOnUI"], [208, 11, 105, 35], [208, 12, 105, 35, "__stackDetails"], [208, 26, 105, 35], [208, 29, 105, 35, "_e"], [208, 31, 105, 35], [209, 4, 105, 35], [209, 11, 105, 35, "runOnUI"], [209, 18, 105, 35], [210, 2, 105, 35], [210, 3, 81, 7], [210, 7, 141, 0], [211, 2, 141, 0], [211, 6, 141, 0, "_worklet_17019095552526_init_data"], [211, 39, 141, 0], [212, 4, 141, 0, "code"], [212, 8, 141, 0], [213, 4, 141, 0, "location"], [213, 12, 141, 0], [214, 4, 141, 0, "sourceMap"], [214, 13, 141, 0], [215, 4, 141, 0, "version"], [215, 11, 141, 0], [216, 2, 141, 0], [217, 2, 146, 7], [217, 11, 146, 16, "executeOnUIRuntimeSync"], [217, 33, 146, 38, "executeOnUIRuntimeSync"], [217, 34, 147, 2, "worklet"], [217, 41, 147, 45], [217, 43, 148, 34], [218, 4, 149, 2], [218, 11, 149, 9], [218, 23, 149, 22], [219, 6, 149, 22], [219, 15, 149, 22, "_len2"], [219, 20, 149, 22], [219, 23, 149, 22, "arguments"], [219, 32, 149, 22], [219, 33, 149, 22, "length"], [219, 39, 149, 22], [219, 41, 149, 13, "args"], [219, 45, 149, 17], [219, 52, 149, 17, "Array"], [219, 57, 149, 17], [219, 58, 149, 17, "_len2"], [219, 63, 149, 17], [219, 66, 149, 17, "_key2"], [219, 71, 149, 17], [219, 77, 149, 17, "_key2"], [219, 82, 149, 17], [219, 85, 149, 17, "_len2"], [219, 90, 149, 17], [219, 92, 149, 17, "_key2"], [219, 97, 149, 17], [220, 8, 149, 13, "args"], [220, 12, 149, 17], [220, 13, 149, 17, "_key2"], [220, 18, 149, 17], [220, 22, 149, 17, "arguments"], [220, 31, 149, 17], [220, 32, 149, 17, "_key2"], [220, 37, 149, 17], [221, 6, 149, 17], [222, 6, 150, 4], [222, 13, 150, 11, "ReanimatedModule"], [222, 47, 150, 27], [222, 48, 150, 28, "executeOnUIRuntimeSync"], [222, 70, 150, 50], [222, 71, 151, 6], [222, 75, 151, 6, "makeShareableCloneRecursive"], [222, 114, 151, 33], [222, 116, 151, 34], [223, 8, 151, 34], [223, 12, 151, 34, "_e"], [223, 14, 151, 34], [223, 22, 151, 34, "global"], [223, 28, 151, 34], [223, 29, 151, 34, "Error"], [223, 34, 151, 34], [224, 8, 151, 34], [224, 12, 151, 34, "threadsTs6"], [224, 22, 151, 34], [224, 34, 151, 34, "threadsTs6"], [224, 35, 151, 34], [224, 37, 151, 40], [225, 10, 153, 8], [225, 14, 153, 14, "result"], [225, 20, 153, 20], [225, 23, 153, 23, "worklet"], [225, 30, 153, 30], [225, 31, 153, 31], [225, 34, 153, 34, "args"], [225, 38, 153, 38], [225, 39, 153, 39], [226, 10, 154, 8], [226, 17, 154, 15], [226, 21, 154, 15, "makeShareableCloneOnUIRecursive"], [226, 64, 154, 46], [226, 66, 154, 47, "result"], [226, 72, 154, 53], [226, 73, 154, 54], [227, 8, 155, 6], [227, 9, 155, 7], [228, 8, 155, 7, "threadsTs6"], [228, 18, 155, 7], [228, 19, 155, 7, "__closure"], [228, 28, 155, 7], [229, 10, 155, 7, "worklet"], [229, 17, 155, 7], [230, 10, 155, 7, "args"], [230, 14, 155, 7], [231, 10, 155, 7, "makeShareableCloneOnUIRecursive"], [231, 41, 155, 7], [231, 43, 154, 15, "makeShareableCloneOnUIRecursive"], [232, 8, 154, 46], [233, 8, 154, 46, "threadsTs6"], [233, 18, 154, 46], [233, 19, 154, 46, "__workletHash"], [233, 32, 154, 46], [234, 8, 154, 46, "threadsTs6"], [234, 18, 154, 46], [234, 19, 154, 46, "__initData"], [234, 29, 154, 46], [234, 32, 154, 46, "_worklet_17019095552526_init_data"], [234, 65, 154, 46], [235, 8, 154, 46, "threadsTs6"], [235, 18, 154, 46], [235, 19, 154, 46, "__stackDetails"], [235, 33, 154, 46], [235, 36, 154, 46, "_e"], [235, 38, 154, 46], [236, 8, 154, 46], [236, 15, 154, 46, "threadsTs6"], [236, 25, 154, 46], [237, 6, 154, 46], [237, 7, 151, 34], [237, 9, 155, 7], [237, 10, 156, 4], [237, 11, 156, 5], [238, 4, 157, 2], [238, 5, 157, 3], [239, 2, 158, 0], [241, 2, 160, 0], [242, 2, 160, 0], [242, 6, 160, 0, "_worklet_7163126537089_init_data"], [242, 38, 160, 0], [243, 4, 160, 0, "code"], [243, 8, 160, 0], [244, 4, 160, 0, "location"], [244, 12, 160, 0], [245, 4, 160, 0, "sourceMap"], [245, 13, 160, 0], [246, 4, 160, 0, "version"], [246, 11, 160, 0], [247, 2, 160, 0], [248, 2, 160, 0], [248, 6, 160, 0, "_worklet_12333479226436_init_data"], [248, 39, 160, 0], [249, 4, 160, 0, "code"], [249, 8, 160, 0], [250, 4, 160, 0, "location"], [250, 12, 160, 0], [251, 4, 160, 0, "sourceMap"], [251, 13, 160, 0], [252, 4, 160, 0, "version"], [252, 11, 160, 0], [253, 2, 160, 0], [254, 2, 164, 0], [255, 2, 164, 0], [255, 6, 164, 0, "runOnUIImmediately"], [255, 24, 164, 0], [255, 27, 164, 0, "exports"], [255, 34, 164, 0], [255, 35, 164, 0, "runOnUIImmediately"], [255, 53, 164, 0], [255, 56, 165, 7], [256, 4, 165, 7], [256, 8, 165, 7, "_e"], [256, 10, 165, 7], [256, 18, 165, 7, "global"], [256, 24, 165, 7], [256, 25, 165, 7, "Error"], [256, 30, 165, 7], [257, 4, 165, 7], [257, 8, 165, 7, "runOnUIImmediately"], [257, 26, 165, 7], [257, 38, 165, 7, "runOnUIImmediately"], [257, 39, 166, 2, "worklet"], [257, 46, 166, 45], [257, 48, 167, 27], [258, 6, 169, 2], [258, 10, 169, 6, "__DEV__"], [258, 17, 169, 13], [258, 21, 169, 17], [258, 22, 169, 18, "SHOULD_BE_USE_WEB"], [258, 39, 169, 35], [258, 43, 169, 39, "_WORKLET"], [258, 51, 169, 47], [258, 53, 169, 49], [259, 8, 170, 4], [259, 14, 170, 10], [259, 18, 170, 14, "ReanimatedError"], [259, 41, 170, 29], [259, 42, 171, 6], [259, 199, 172, 4], [259, 200, 172, 5], [260, 6, 173, 2], [261, 6, 174, 2], [261, 10, 174, 6, "__DEV__"], [261, 17, 174, 13], [261, 21, 174, 17], [261, 22, 174, 18, "SHOULD_BE_USE_WEB"], [261, 39, 174, 35], [261, 43, 174, 39], [261, 44, 174, 40], [261, 48, 174, 40, "isWorkletFunction"], [261, 78, 174, 57], [261, 80, 174, 58, "worklet"], [261, 87, 174, 65], [261, 88, 174, 66], [261, 90, 174, 68], [262, 8, 175, 4], [262, 14, 175, 10], [262, 18, 175, 14, "ReanimatedError"], [262, 41, 175, 29], [262, 42, 176, 6], [262, 96, 177, 4], [262, 97, 177, 5], [263, 6, 178, 2], [264, 6, 179, 2], [264, 13, 179, 9], [264, 25, 179, 22], [265, 8, 179, 22], [265, 17, 179, 22, "_len3"], [265, 22, 179, 22], [265, 25, 179, 22, "arguments"], [265, 34, 179, 22], [265, 35, 179, 22, "length"], [265, 41, 179, 22], [265, 43, 179, 13, "args"], [265, 47, 179, 17], [265, 54, 179, 17, "Array"], [265, 59, 179, 17], [265, 60, 179, 17, "_len3"], [265, 65, 179, 17], [265, 68, 179, 17, "_key3"], [265, 73, 179, 17], [265, 79, 179, 17, "_key3"], [265, 84, 179, 17], [265, 87, 179, 17, "_len3"], [265, 92, 179, 17], [265, 94, 179, 17, "_key3"], [265, 99, 179, 17], [266, 10, 179, 13, "args"], [266, 14, 179, 17], [266, 15, 179, 17, "_key3"], [266, 20, 179, 17], [266, 24, 179, 17, "arguments"], [266, 33, 179, 17], [266, 34, 179, 17, "_key3"], [266, 39, 179, 17], [267, 8, 179, 17], [268, 8, 180, 4, "ReanimatedModule"], [268, 42, 180, 20], [268, 43, 180, 21, "scheduleOnUI"], [268, 55, 180, 33], [268, 56, 181, 6], [268, 60, 181, 6, "makeShareableCloneRecursive"], [268, 99, 181, 33], [268, 101, 181, 34], [269, 10, 181, 34], [269, 14, 181, 34, "_e"], [269, 16, 181, 34], [269, 24, 181, 34, "global"], [269, 30, 181, 34], [269, 31, 181, 34, "Error"], [269, 36, 181, 34], [270, 10, 181, 34], [270, 14, 181, 34, "threadsTs8"], [270, 24, 181, 34], [270, 36, 181, 34, "threadsTs8"], [270, 37, 181, 34], [270, 39, 181, 40], [271, 12, 183, 8, "worklet"], [271, 19, 183, 15], [271, 20, 183, 16], [271, 23, 183, 19, "args"], [271, 27, 183, 23], [271, 28, 183, 24], [272, 10, 184, 6], [272, 11, 184, 7], [273, 10, 184, 7, "threadsTs8"], [273, 20, 184, 7], [273, 21, 184, 7, "__closure"], [273, 30, 184, 7], [274, 12, 184, 7, "worklet"], [274, 19, 184, 7], [275, 12, 184, 7, "args"], [276, 10, 184, 7], [277, 10, 184, 7, "threadsTs8"], [277, 20, 184, 7], [277, 21, 184, 7, "__workletHash"], [277, 34, 184, 7], [278, 10, 184, 7, "threadsTs8"], [278, 20, 184, 7], [278, 21, 184, 7, "__initData"], [278, 31, 184, 7], [278, 34, 184, 7, "_worklet_12333479226436_init_data"], [278, 67, 184, 7], [279, 10, 184, 7, "threadsTs8"], [279, 20, 184, 7], [279, 21, 184, 7, "__stackDetails"], [279, 35, 184, 7], [279, 38, 184, 7, "_e"], [279, 40, 184, 7], [280, 10, 184, 7], [280, 17, 184, 7, "threadsTs8"], [280, 27, 184, 7], [281, 8, 184, 7], [281, 9, 181, 34], [281, 11, 184, 7], [281, 12, 185, 4], [281, 13, 185, 5], [282, 6, 186, 2], [282, 7, 186, 3], [283, 4, 187, 0], [283, 5, 187, 1], [284, 4, 187, 1, "runOnUIImmediately"], [284, 22, 187, 1], [284, 23, 187, 1, "__closure"], [284, 32, 187, 1], [285, 6, 187, 1, "__DEV__"], [285, 13, 187, 1], [286, 6, 187, 1, "SHOULD_BE_USE_WEB"], [286, 23, 187, 1], [287, 6, 187, 1, "isWorkletFunction"], [287, 23, 187, 1], [287, 25, 174, 40, "isWorkletFunction"], [287, 55, 174, 57], [288, 6, 174, 57, "ReanimatedModule"], [288, 22, 174, 57], [288, 24, 180, 4, "ReanimatedModule"], [288, 58, 180, 20], [289, 6, 180, 20, "makeShareableCloneRecursive"], [289, 33, 180, 20], [289, 35, 181, 6, "makeShareableCloneRecursive"], [290, 4, 181, 33], [291, 4, 181, 33, "runOnUIImmediately"], [291, 22, 181, 33], [291, 23, 181, 33, "__workletHash"], [291, 36, 181, 33], [292, 4, 181, 33, "runOnUIImmediately"], [292, 22, 181, 33], [292, 23, 181, 33, "__initData"], [292, 33, 181, 33], [292, 36, 181, 33, "_worklet_7163126537089_init_data"], [292, 68, 181, 33], [293, 4, 181, 33, "runOnUIImmediately"], [293, 22, 181, 33], [293, 23, 181, 33, "__stackDetails"], [293, 37, 181, 33], [293, 40, 181, 33, "_e"], [293, 42, 181, 33], [294, 4, 181, 33], [294, 11, 181, 33, "runOnUIImmediately"], [294, 29, 181, 33], [295, 2, 181, 33], [295, 3, 165, 7], [296, 2, 201, 0], [296, 11, 201, 9, "runWorkletOnJS"], [296, 25, 201, 23, "runWorkletOnJS"], [296, 26, 202, 2, "worklet"], [296, 33, 202, 45], [296, 35, 204, 8], [297, 4, 204, 8], [297, 13, 204, 8, "_len4"], [297, 18, 204, 8], [297, 21, 204, 8, "arguments"], [297, 30, 204, 8], [297, 31, 204, 8, "length"], [297, 37, 204, 8], [297, 39, 203, 5, "args"], [297, 43, 203, 9], [297, 50, 203, 9, "Array"], [297, 55, 203, 9], [297, 56, 203, 9, "_len4"], [297, 61, 203, 9], [297, 68, 203, 9, "_len4"], [297, 73, 203, 9], [297, 84, 203, 9, "_key4"], [297, 89, 203, 9], [297, 95, 203, 9, "_key4"], [297, 100, 203, 9], [297, 103, 203, 9, "_len4"], [297, 108, 203, 9], [297, 110, 203, 9, "_key4"], [297, 115, 203, 9], [298, 6, 203, 5, "args"], [298, 10, 203, 9], [298, 11, 203, 9, "_key4"], [298, 16, 203, 9], [298, 24, 203, 9, "arguments"], [298, 33, 203, 9], [298, 34, 203, 9, "_key4"], [298, 39, 203, 9], [299, 4, 203, 9], [300, 4, 205, 2], [301, 4, 206, 2, "worklet"], [301, 11, 206, 9], [301, 12, 206, 10], [301, 15, 206, 13, "args"], [301, 19, 206, 17], [301, 20, 206, 18], [302, 2, 207, 0], [304, 2, 209, 0], [305, 0, 210, 0], [306, 0, 211, 0], [307, 0, 212, 0], [308, 0, 213, 0], [309, 0, 214, 0], [310, 0, 215, 0], [311, 0, 216, 0], [312, 0, 217, 0], [313, 0, 218, 0], [314, 0, 219, 0], [315, 0, 220, 0], [316, 0, 221, 0], [317, 0, 222, 0], [318, 2, 209, 0], [318, 6, 209, 0, "_worklet_6458650219678_init_data"], [318, 38, 209, 0], [319, 4, 209, 0, "code"], [319, 8, 209, 0], [320, 4, 209, 0, "location"], [320, 12, 209, 0], [321, 4, 209, 0, "sourceMap"], [321, 13, 209, 0], [322, 4, 209, 0, "version"], [322, 11, 209, 0], [323, 2, 209, 0], [324, 2, 209, 0], [324, 6, 209, 0, "runOnJS"], [324, 13, 209, 0], [324, 16, 209, 0, "exports"], [324, 23, 209, 0], [324, 24, 209, 0, "runOnJS"], [324, 31, 209, 0], [324, 34, 223, 7], [325, 4, 223, 7], [325, 8, 223, 7, "_e"], [325, 10, 223, 7], [325, 18, 223, 7, "global"], [325, 24, 223, 7], [325, 25, 223, 7, "Error"], [325, 30, 223, 7], [326, 4, 223, 7], [326, 8, 223, 7, "runOnJS"], [326, 15, 223, 7], [326, 27, 223, 7, "runOnJS"], [326, 28, 224, 2, "fun"], [326, 31, 227, 40], [326, 33, 228, 27], [327, 6, 231, 2], [327, 10, 231, 6, "SHOULD_BE_USE_WEB"], [327, 27, 231, 23], [327, 31, 231, 27], [327, 32, 231, 28, "_WORKLET"], [327, 40, 231, 36], [327, 42, 231, 38], [328, 8, 232, 4], [329, 8, 233, 4], [329, 15, 233, 11], [330, 10, 233, 11], [330, 19, 233, 11, "_len5"], [330, 24, 233, 11], [330, 27, 233, 11, "arguments"], [330, 36, 233, 11], [330, 37, 233, 11, "length"], [330, 43, 233, 11], [330, 45, 233, 15, "args"], [330, 49, 233, 19], [330, 56, 233, 19, "Array"], [330, 61, 233, 19], [330, 62, 233, 19, "_len5"], [330, 67, 233, 19], [330, 70, 233, 19, "_key5"], [330, 75, 233, 19], [330, 81, 233, 19, "_key5"], [330, 86, 233, 19], [330, 89, 233, 19, "_len5"], [330, 94, 233, 19], [330, 96, 233, 19, "_key5"], [330, 101, 233, 19], [331, 12, 233, 15, "args"], [331, 16, 233, 19], [331, 17, 233, 19, "_key5"], [331, 22, 233, 19], [331, 26, 233, 19, "arguments"], [331, 35, 233, 19], [331, 36, 233, 19, "_key5"], [331, 41, 233, 19], [332, 10, 233, 19], [333, 10, 233, 19], [333, 17, 234, 6, "queueMicrotask"], [333, 31, 234, 20], [333, 32, 235, 8, "args"], [333, 36, 235, 12], [333, 37, 235, 13, "length"], [333, 43, 235, 19], [333, 46, 236, 12], [333, 52, 236, 19, "fun"], [333, 55, 236, 22], [333, 56, 236, 58], [333, 59, 236, 61, "args"], [333, 63, 236, 65], [333, 64, 236, 66], [333, 67, 237, 13, "fun"], [333, 70, 238, 6], [333, 71, 238, 7], [334, 8, 238, 7], [335, 6, 239, 2], [336, 6, 240, 2], [336, 10, 240, 6], [336, 14, 240, 6, "isWorkletFunction"], [336, 44, 240, 23], [336, 46, 240, 43, "fun"], [336, 49, 240, 46], [336, 50, 240, 47], [336, 52, 240, 49], [337, 8, 241, 4], [338, 8, 242, 4], [340, 8, 244, 4], [340, 15, 244, 11], [341, 10, 244, 11], [341, 19, 244, 11, "_len6"], [341, 24, 244, 11], [341, 27, 244, 11, "arguments"], [341, 36, 244, 11], [341, 37, 244, 11, "length"], [341, 43, 244, 11], [341, 45, 244, 15, "args"], [341, 49, 244, 19], [341, 56, 244, 19, "Array"], [341, 61, 244, 19], [341, 62, 244, 19, "_len6"], [341, 67, 244, 19], [341, 70, 244, 19, "_key6"], [341, 75, 244, 19], [341, 81, 244, 19, "_key6"], [341, 86, 244, 19], [341, 89, 244, 19, "_len6"], [341, 94, 244, 19], [341, 96, 244, 19, "_key6"], [341, 101, 244, 19], [342, 12, 244, 15, "args"], [342, 16, 244, 19], [342, 17, 244, 19, "_key6"], [342, 22, 244, 19], [342, 26, 244, 19, "arguments"], [342, 35, 244, 19], [342, 36, 244, 19, "_key6"], [342, 41, 244, 19], [343, 10, 244, 19], [344, 10, 244, 19], [344, 17, 245, 6, "runOnJS"], [344, 24, 245, 13], [344, 25, 245, 14, "runWorkletOnJS"], [344, 39, 245, 47], [344, 40, 245, 48], [344, 41, 246, 8, "fun"], [344, 44, 246, 11], [344, 46, 247, 8], [344, 49, 247, 11, "args"], [344, 53, 248, 6], [344, 54, 248, 7], [345, 8, 248, 7], [346, 6, 249, 2], [347, 6, 250, 2], [347, 10, 250, 7, "fun"], [347, 13, 250, 10], [347, 14, 250, 28, "__remoteFunction"], [347, 30, 250, 44], [347, 32, 250, 46], [348, 8, 251, 4], [349, 8, 252, 4], [350, 8, 253, 4], [351, 8, 254, 4], [352, 8, 255, 4, "fun"], [352, 11, 255, 7], [352, 14, 255, 11, "fun"], [352, 17, 255, 14], [352, 18, 255, 32, "__remoteFunction"], [352, 34, 255, 48], [353, 6, 256, 2], [354, 6, 258, 2], [354, 10, 258, 8, "scheduleOnJS"], [354, 22, 258, 20], [354, 25, 259, 4], [354, 32, 259, 11, "fun"], [354, 35, 259, 14], [354, 40, 259, 19], [354, 50, 259, 29], [354, 53, 260, 8, "global"], [354, 59, 260, 14], [354, 60, 260, 15, "_scheduleHostFunctionOnJS"], [354, 85, 260, 40], [354, 88, 261, 8, "global"], [354, 94, 261, 14], [354, 95, 261, 15, "_scheduleRemoteFunctionOnJS"], [354, 122, 261, 42], [355, 6, 263, 2], [355, 13, 263, 9], [355, 25, 263, 22], [356, 8, 263, 22], [356, 17, 263, 22, "_len7"], [356, 22, 263, 22], [356, 25, 263, 22, "arguments"], [356, 34, 263, 22], [356, 35, 263, 22, "length"], [356, 41, 263, 22], [356, 43, 263, 13, "args"], [356, 47, 263, 17], [356, 54, 263, 17, "Array"], [356, 59, 263, 17], [356, 60, 263, 17, "_len7"], [356, 65, 263, 17], [356, 68, 263, 17, "_key7"], [356, 73, 263, 17], [356, 79, 263, 17, "_key7"], [356, 84, 263, 17], [356, 87, 263, 17, "_len7"], [356, 92, 263, 17], [356, 94, 263, 17, "_key7"], [356, 99, 263, 17], [357, 10, 263, 13, "args"], [357, 14, 263, 17], [357, 15, 263, 17, "_key7"], [357, 20, 263, 17], [357, 24, 263, 17, "arguments"], [357, 33, 263, 17], [357, 34, 263, 17, "_key7"], [357, 39, 263, 17], [358, 8, 263, 17], [359, 8, 264, 4, "scheduleOnJS"], [359, 20, 264, 16], [359, 21, 265, 6, "fun"], [359, 24, 265, 9], [359, 26, 268, 6, "args"], [359, 30, 268, 10], [359, 31, 268, 11, "length"], [359, 37, 268, 17], [359, 40, 268, 20], [359, 41, 268, 21], [360, 8, 269, 10], [361, 8, 270, 11], [361, 12, 270, 11, "makeShareableCloneOnUIRecursive"], [361, 55, 270, 42], [361, 57, 270, 43, "args"], [361, 61, 270, 47], [361, 62, 270, 48], [361, 65, 271, 10, "undefined"], [361, 74, 272, 4], [361, 75, 272, 5], [362, 6, 273, 2], [362, 7, 273, 3], [363, 4, 274, 0], [363, 5, 274, 1], [364, 4, 274, 1, "runOnJS"], [364, 11, 274, 1], [364, 12, 274, 1, "__closure"], [364, 21, 274, 1], [365, 6, 274, 1, "SHOULD_BE_USE_WEB"], [365, 23, 274, 1], [366, 6, 274, 1, "isWorkletFunction"], [366, 23, 274, 1], [366, 25, 240, 6, "isWorkletFunction"], [366, 55, 240, 23], [367, 6, 240, 23, "runWorkletOnJS"], [367, 20, 240, 23], [368, 6, 240, 23, "makeShareableCloneOnUIRecursive"], [368, 37, 240, 23], [368, 39, 270, 11, "makeShareableCloneOnUIRecursive"], [369, 4, 270, 42], [370, 4, 270, 42, "runOnJS"], [370, 11, 270, 42], [370, 12, 270, 42, "__workletHash"], [370, 25, 270, 42], [371, 4, 270, 42, "runOnJS"], [371, 11, 270, 42], [371, 12, 270, 42, "__initData"], [371, 22, 270, 42], [371, 25, 270, 42, "_worklet_6458650219678_init_data"], [371, 57, 270, 42], [372, 4, 270, 42, "runOnJS"], [372, 11, 270, 42], [372, 12, 270, 42, "__stackDetails"], [372, 26, 270, 42], [372, 29, 270, 42, "_e"], [372, 31, 270, 42], [373, 4, 270, 42], [373, 11, 270, 42, "runOnJS"], [373, 18, 270, 42], [374, 2, 270, 42], [374, 3, 223, 7], [375, 0, 223, 7], [375, 3]], "functionMap": {"names": ["<global>", "setupMicrotasks", "global.queueMicrotask", "global.__callMicrotasks", "callMicrotasksOnUIThread", "<anonymous>", "runOnUI", "makeShareableCloneRecursive$argument_0", "queueMicrotask$argument_0", "queue.forEach$argument_0", "executeOnUIRuntimeSync", "runOnUIImmediately", "runWorkletOnJS", "runOnJS"], "mappings": "AAA;OCiB;0BCK;GDE;4BEE;GFe;CDC;AIE;CJG;IKG;KLE;OM4B;SDY;oCEY;SFG;qBGe;sCDI;0BEG;aFE;WCE;OHE;GCE;CNC;OUO;SLG;kCEE;OFI;GKE;CVC;OWO;SNc;kCEE;OFG;GME;CXC;AYc;CZM;OagB;WRU;OQK;WRM;OQI;SRe;GQU;CbC"}}, "type": "js/module"}]}