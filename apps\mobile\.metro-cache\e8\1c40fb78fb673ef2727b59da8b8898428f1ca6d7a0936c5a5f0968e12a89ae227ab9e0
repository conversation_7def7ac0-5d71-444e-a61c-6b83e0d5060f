{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 51, "index": 51}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isEdgeToEdge = exports.controlEdgeToEdgeValues = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  // src/index.android.ts\n  var warnings = /* @__PURE__ */new Set();\n  var isEdgeToEdge = () => _reactNative.TurboModuleRegistry.get(\"RNEdgeToEdge\") != null;\n  exports.isEdgeToEdge = isEdgeToEdge;\n  var controlEdgeToEdgeValues = values => {\n    if (__DEV__ && isEdgeToEdge()) {\n      var entries = Object.entries(values).filter(_ref => {\n        var _ref2 = (0, _slicedToArray2.default)(_ref, 2),\n          value = _ref2[1];\n        return typeof value !== \"undefined\";\n      });\n      var stableKey = entries.join(\" \");\n      if (entries.length < 1 || warnings.has(stableKey)) {\n        return;\n      }\n      warnings.add(stableKey);\n      var isPlural = entries.length > 1;\n      var lastIndex = entries.length - 1;\n      var list = entries.reduce((acc, _ref3, index) => {\n        var _ref4 = (0, _slicedToArray2.default)(_ref3, 1),\n          name = _ref4[0];\n        return index === 0 ? name : acc + (index === lastIndex ? \" and \" : \", \") + name;\n      }, \"\");\n      console.warn(`${list} ${isPlural ? \"values are\" : \"value is\"} ignored when using react-native-edge-to-edge`);\n    }\n  };\n  exports.controlEdgeToEdgeValues = controlEdgeToEdgeValues;\n});", "lineCount": 36, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_reactNative"], [8, 18, 1, 0], [8, 21, 1, 0, "require"], [8, 28, 1, 0], [8, 29, 1, 0, "_dependencyMap"], [8, 43, 1, 0], [9, 2, 3, 0], [10, 2, 4, 0], [10, 6, 4, 4, "warnings"], [10, 14, 4, 12], [10, 17, 4, 15], [10, 32, 4, 31], [10, 36, 4, 35, "Set"], [10, 39, 4, 38], [10, 40, 4, 39], [10, 41, 4, 40], [11, 2, 5, 0], [11, 6, 5, 4, "isEdgeToEdge"], [11, 18, 5, 16], [11, 21, 5, 19, "isEdgeToEdge"], [11, 22, 5, 19], [11, 27, 5, 25, "TurboModuleRegistry"], [11, 59, 5, 44], [11, 60, 5, 45, "get"], [11, 63, 5, 48], [11, 64, 5, 49], [11, 78, 5, 63], [11, 79, 5, 64], [11, 83, 5, 68], [11, 87, 5, 72], [12, 2, 5, 73, "exports"], [12, 9, 5, 73], [12, 10, 5, 73, "isEdgeToEdge"], [12, 22, 5, 73], [12, 25, 5, 73, "isEdgeToEdge"], [12, 37, 5, 73], [13, 2, 6, 0], [13, 6, 6, 4, "controlEdgeToEdgeValues"], [13, 29, 6, 27], [13, 32, 6, 31, "values"], [13, 38, 6, 37], [13, 42, 6, 42], [14, 4, 7, 2], [14, 8, 7, 6, "__DEV__"], [14, 15, 7, 13], [14, 19, 7, 17, "isEdgeToEdge"], [14, 31, 7, 29], [14, 32, 7, 30], [14, 33, 7, 31], [14, 35, 7, 33], [15, 6, 8, 4], [15, 10, 8, 10, "entries"], [15, 17, 8, 17], [15, 20, 8, 20, "Object"], [15, 26, 8, 26], [15, 27, 8, 27, "entries"], [15, 34, 8, 34], [15, 35, 8, 35, "values"], [15, 41, 8, 41], [15, 42, 8, 42], [15, 43, 8, 43, "filter"], [15, 49, 8, 49], [15, 50, 9, 6, "_ref"], [15, 54, 9, 6], [16, 8, 9, 6], [16, 12, 9, 6, "_ref2"], [16, 17, 9, 6], [16, 24, 9, 6, "_slicedToArray2"], [16, 39, 9, 6], [16, 40, 9, 6, "default"], [16, 47, 9, 6], [16, 49, 9, 6, "_ref"], [16, 53, 9, 6], [17, 10, 9, 10, "value"], [17, 15, 9, 15], [17, 18, 9, 15, "_ref2"], [17, 23, 9, 15], [18, 8, 9, 15], [18, 15, 9, 21], [18, 22, 9, 28, "value"], [18, 27, 9, 33], [18, 32, 9, 38], [18, 43, 9, 49], [19, 6, 9, 49], [19, 7, 10, 4], [19, 8, 10, 5], [20, 6, 11, 4], [20, 10, 11, 10, "<PERSON><PERSON><PERSON>"], [20, 19, 11, 19], [20, 22, 11, 22, "entries"], [20, 29, 11, 29], [20, 30, 11, 30, "join"], [20, 34, 11, 34], [20, 35, 11, 35], [20, 38, 11, 38], [20, 39, 11, 39], [21, 6, 12, 4], [21, 10, 12, 8, "entries"], [21, 17, 12, 15], [21, 18, 12, 16, "length"], [21, 24, 12, 22], [21, 27, 12, 25], [21, 28, 12, 26], [21, 32, 12, 30, "warnings"], [21, 40, 12, 38], [21, 41, 12, 39, "has"], [21, 44, 12, 42], [21, 45, 12, 43, "<PERSON><PERSON><PERSON>"], [21, 54, 12, 52], [21, 55, 12, 53], [21, 57, 12, 55], [22, 8, 13, 6], [23, 6, 14, 4], [24, 6, 15, 4, "warnings"], [24, 14, 15, 12], [24, 15, 15, 13, "add"], [24, 18, 15, 16], [24, 19, 15, 17, "<PERSON><PERSON><PERSON>"], [24, 28, 15, 26], [24, 29, 15, 27], [25, 6, 16, 4], [25, 10, 16, 10, "isPlural"], [25, 18, 16, 18], [25, 21, 16, 21, "entries"], [25, 28, 16, 28], [25, 29, 16, 29, "length"], [25, 35, 16, 35], [25, 38, 16, 38], [25, 39, 16, 39], [26, 6, 17, 4], [26, 10, 17, 10, "lastIndex"], [26, 19, 17, 19], [26, 22, 17, 22, "entries"], [26, 29, 17, 29], [26, 30, 17, 30, "length"], [26, 36, 17, 36], [26, 39, 17, 39], [26, 40, 17, 40], [27, 6, 18, 4], [27, 10, 18, 10, "list"], [27, 14, 18, 14], [27, 17, 18, 17, "entries"], [27, 24, 18, 24], [27, 25, 18, 25, "reduce"], [27, 31, 18, 31], [27, 32, 19, 6], [27, 33, 19, 7, "acc"], [27, 36, 19, 10], [27, 38, 19, 10, "_ref3"], [27, 43, 19, 10], [27, 45, 19, 20, "index"], [27, 50, 19, 25], [28, 8, 19, 25], [28, 12, 19, 25, "_ref4"], [28, 17, 19, 25], [28, 24, 19, 25, "_slicedToArray2"], [28, 39, 19, 25], [28, 40, 19, 25, "default"], [28, 47, 19, 25], [28, 49, 19, 25, "_ref3"], [28, 54, 19, 25], [29, 10, 19, 13, "name"], [29, 14, 19, 17], [29, 17, 19, 17, "_ref4"], [29, 22, 19, 17], [30, 8, 19, 17], [30, 15, 19, 30, "index"], [30, 20, 19, 35], [30, 25, 19, 40], [30, 26, 19, 41], [30, 29, 19, 44, "name"], [30, 33, 19, 48], [30, 36, 19, 51, "acc"], [30, 39, 19, 54], [30, 43, 19, 58, "index"], [30, 48, 19, 63], [30, 53, 19, 68, "lastIndex"], [30, 62, 19, 77], [30, 65, 19, 80], [30, 72, 19, 87], [30, 75, 19, 90], [30, 79, 19, 94], [30, 80, 19, 95], [30, 83, 19, 98, "name"], [30, 87, 19, 102], [31, 6, 19, 102], [31, 9, 20, 6], [31, 11, 21, 4], [31, 12, 21, 5], [32, 6, 22, 4, "console"], [32, 13, 22, 11], [32, 14, 22, 12, "warn"], [32, 18, 22, 16], [32, 19, 23, 6], [32, 22, 23, 9, "list"], [32, 26, 23, 13], [32, 30, 23, 17, "isPlural"], [32, 38, 23, 25], [32, 41, 23, 28], [32, 53, 23, 40], [32, 56, 23, 43], [32, 66, 23, 53], [32, 113, 24, 4], [32, 114, 24, 5], [33, 4, 25, 2], [34, 2, 26, 0], [34, 3, 26, 1], [35, 2, 26, 2, "exports"], [35, 9, 26, 2], [35, 10, 26, 2, "controlEdgeToEdgeValues"], [35, 33, 26, 2], [35, 36, 26, 2, "controlEdgeToEdgeValues"], [35, 59, 26, 2], [36, 0, 26, 2], [36, 3]], "functionMap": {"names": ["<global>", "isEdgeToEdge", "controlEdgeToEdgeValues", "Object.entries.filter$argument_0", "entries.reduce$argument_0"], "mappings": "AAA;mBCI,qDD;8BEC;MCG,2CD;MEU,gGF;CFO"}}, "type": "js/module"}]}