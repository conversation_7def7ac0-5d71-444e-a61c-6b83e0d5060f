{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/wrapNativeSuper", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "imgnTtXT+OlBfDxpawXO7znTT9E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.CodedError = void 0;\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/createClass\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _wrapNativeSuper2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/wrapNativeSuper\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  /**\n   * A general error class that should be used for all errors in Expo modules.\n   * Guarantees a `code` field that can be used to differentiate between different\n   * types of errors without further subclassing Error.\n   */\n  var CodedError = exports.CodedError = /*#__PURE__*/function (_Error) {\n    function CodedError(code, message) {\n      var _this;\n      (0, _classCallCheck2.default)(this, CodedError);\n      _this = _callSuper(this, CodedError, [message]);\n      _this.code = code;\n      return _this;\n    }\n    (0, _inherits2.default)(CodedError, _Error);\n    return (0, _createClass2.default)(CodedError);\n  }(/*#__PURE__*/(0, _wrapNativeSuper2.default)(Error));\n});", "lineCount": 31, "map": [[15, 2, 1, 0], [16, 0, 2, 0], [17, 0, 3, 0], [18, 0, 4, 0], [19, 0, 5, 0], [20, 2, 1, 0], [20, 6, 6, 13, "CodedError"], [20, 16, 6, 23], [20, 19, 6, 23, "exports"], [20, 26, 6, 23], [20, 27, 6, 23, "CodedError"], [20, 37, 6, 23], [20, 63, 6, 23, "_Error"], [20, 69, 6, 23], [21, 4, 10, 2], [21, 13, 10, 2, "CodedError"], [21, 24, 10, 14, "code"], [21, 28, 10, 26], [21, 30, 10, 28, "message"], [21, 37, 10, 43], [21, 39, 10, 45], [22, 6, 10, 45], [22, 10, 10, 45, "_this"], [22, 15, 10, 45], [23, 6, 10, 45], [23, 10, 10, 45, "_classCallCheck2"], [23, 26, 10, 45], [23, 27, 10, 45, "default"], [23, 34, 10, 45], [23, 42, 10, 45, "CodedError"], [23, 52, 10, 45], [24, 6, 11, 4, "_this"], [24, 11, 11, 4], [24, 14, 11, 4, "_callSuper"], [24, 24, 11, 4], [24, 31, 11, 4, "CodedError"], [24, 41, 11, 4], [24, 44, 11, 10, "message"], [24, 51, 11, 17], [25, 6, 12, 4, "_this"], [25, 11, 12, 4], [25, 12, 12, 9, "code"], [25, 16, 12, 13], [25, 19, 12, 16, "code"], [25, 23, 12, 20], [26, 6, 12, 21], [26, 13, 12, 21, "_this"], [26, 18, 12, 21], [27, 4, 13, 2], [28, 4, 13, 3], [28, 8, 13, 3, "_inherits2"], [28, 18, 13, 3], [28, 19, 13, 3, "default"], [28, 26, 13, 3], [28, 28, 13, 3, "CodedError"], [28, 38, 13, 3], [28, 40, 13, 3, "_Error"], [28, 46, 13, 3], [29, 4, 13, 3], [29, 15, 13, 3, "_createClass2"], [29, 28, 13, 3], [29, 29, 13, 3, "default"], [29, 36, 13, 3], [29, 38, 13, 3, "CodedError"], [29, 48, 13, 3], [30, 2, 13, 3], [30, 21, 13, 3, "_wrapNativeSuper2"], [30, 38, 13, 3], [30, 39, 13, 3, "default"], [30, 46, 13, 3], [30, 48, 6, 32, "Error"], [30, 53, 6, 37], [31, 0, 6, 37], [31, 3]], "functionMap": {"names": ["<global>", "CodedError", "constructor"], "mappings": "AAA;OCK;ECI;GDG;CDC"}}, "type": "js/module"}]}