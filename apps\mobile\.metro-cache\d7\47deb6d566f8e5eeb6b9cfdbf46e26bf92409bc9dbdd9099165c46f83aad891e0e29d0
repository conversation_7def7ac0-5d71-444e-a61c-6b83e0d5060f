{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "../../utils/ArrayLikeUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 63}}], "key": "UNCoikk/SuoXbwGrCmksIC85q/Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createHTMLCollection = createHTMLCollection;\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _ArrayLikeUtils = require(_dependencyMap[5], \"../../utils/ArrayLikeUtils\");\n  var _length = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"length\");\n  var HTMLCollection = exports.default = /*#__PURE__*/function () {\n    function HTMLCollection(elements) {\n      (0, _classCallCheck2.default)(this, HTMLCollection);\n      Object.defineProperty(this, _length, {\n        writable: true,\n        value: void 0\n      });\n      for (var i = 0; i < elements.length; i++) {\n        Object.defineProperty(this, i, {\n          value: elements[i],\n          enumerable: true,\n          configurable: false,\n          writable: false\n        });\n      }\n      (0, _classPrivateFieldLooseBase2.default)(this, _length)[_length] = elements.length;\n    }\n    return (0, _createClass2.default)(HTMLCollection, [{\n      key: \"length\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _length)[_length];\n      }\n    }, {\n      key: \"item\",\n      value: function item(index) {\n        if (index < 0 || index >= (0, _classPrivateFieldLooseBase2.default)(this, _length)[_length]) {\n          return null;\n        }\n        var arrayLike = this;\n        return arrayLike[index];\n      }\n    }, {\n      key: \"namedItem\",\n      value: function namedItem(name) {\n        return null;\n      }\n    }, {\n      key: Symbol.iterator,\n      value: function () {\n        return (0, _ArrayLikeUtils.createValueIterator)(this);\n      }\n    }]);\n  }();\n  function createHTMLCollection(elements) {\n    return new HTMLCollection(elements);\n  }\n});", "lineCount": 60, "map": [[12, 2, 15, 0], [12, 6, 15, 0, "_ArrayLikeUtils"], [12, 21, 15, 0], [12, 24, 15, 0, "require"], [12, 31, 15, 0], [12, 32, 15, 0, "_dependencyMap"], [12, 46, 15, 0], [13, 2, 15, 63], [13, 6, 15, 63, "_length"], [13, 13, 15, 63], [13, 33, 15, 63, "_classPrivateFieldLooseKey2"], [13, 60, 15, 63], [13, 61, 15, 63, "default"], [13, 68, 15, 63], [14, 2, 15, 63], [14, 6, 21, 21, "HTMLCollection"], [14, 20, 21, 35], [14, 23, 21, 35, "exports"], [14, 30, 21, 35], [14, 31, 21, 35, "default"], [14, 38, 21, 35], [15, 4, 30, 2], [15, 13, 30, 2, "HTMLCollection"], [15, 28, 30, 14, "elements"], [15, 36, 30, 41], [15, 38, 30, 43], [16, 6, 30, 43], [16, 10, 30, 43, "_classCallCheck2"], [16, 26, 30, 43], [16, 27, 30, 43, "default"], [16, 34, 30, 43], [16, 42, 30, 43, "HTMLCollection"], [16, 56, 30, 43], [17, 6, 30, 43, "Object"], [17, 12, 30, 43], [17, 13, 30, 43, "defineProperty"], [17, 27, 30, 43], [17, 34, 30, 43, "_length"], [17, 41, 30, 43], [18, 8, 30, 43, "writable"], [18, 16, 30, 43], [19, 8, 30, 43, "value"], [19, 13, 30, 43], [20, 6, 30, 43], [21, 6, 31, 4], [21, 11, 31, 9], [21, 15, 31, 13, "i"], [21, 16, 31, 14], [21, 19, 31, 17], [21, 20, 31, 18], [21, 22, 31, 20, "i"], [21, 23, 31, 21], [21, 26, 31, 24, "elements"], [21, 34, 31, 32], [21, 35, 31, 33, "length"], [21, 41, 31, 39], [21, 43, 31, 41, "i"], [21, 44, 31, 42], [21, 46, 31, 44], [21, 48, 31, 46], [22, 8, 32, 6, "Object"], [22, 14, 32, 12], [22, 15, 32, 13, "defineProperty"], [22, 29, 32, 27], [22, 30, 32, 28], [22, 34, 32, 32], [22, 36, 32, 34, "i"], [22, 37, 32, 35], [22, 39, 32, 37], [23, 10, 33, 8, "value"], [23, 15, 33, 13], [23, 17, 33, 15, "elements"], [23, 25, 33, 23], [23, 26, 33, 24, "i"], [23, 27, 33, 25], [23, 28, 33, 26], [24, 10, 34, 8, "enumerable"], [24, 20, 34, 18], [24, 22, 34, 20], [24, 26, 34, 24], [25, 10, 35, 8, "configurable"], [25, 22, 35, 20], [25, 24, 35, 22], [25, 29, 35, 27], [26, 10, 36, 8, "writable"], [26, 18, 36, 16], [26, 20, 36, 18], [27, 8, 37, 6], [27, 9, 37, 7], [27, 10, 37, 8], [28, 6, 38, 4], [29, 6, 40, 4], [29, 10, 40, 4, "_classPrivateFieldLooseBase2"], [29, 38, 40, 4], [29, 39, 40, 4, "default"], [29, 46, 40, 4], [29, 52, 40, 8], [29, 54, 40, 8, "_length"], [29, 61, 40, 8], [29, 63, 40, 8, "_length"], [29, 70, 40, 8], [29, 74, 40, 19, "elements"], [29, 82, 40, 27], [29, 83, 40, 28, "length"], [29, 89, 40, 34], [30, 4, 41, 2], [31, 4, 41, 3], [31, 15, 41, 3, "_createClass2"], [31, 28, 41, 3], [31, 29, 41, 3, "default"], [31, 36, 41, 3], [31, 38, 41, 3, "HTMLCollection"], [31, 52, 41, 3], [32, 6, 41, 3, "key"], [32, 9, 41, 3], [33, 6, 41, 3, "get"], [33, 9, 41, 3], [33, 11, 43, 2], [33, 20, 43, 2, "get"], [33, 21, 43, 2], [33, 23, 43, 23], [34, 8, 44, 4], [34, 19, 44, 4, "_classPrivateFieldLooseBase2"], [34, 47, 44, 4], [34, 48, 44, 4, "default"], [34, 55, 44, 4], [34, 57, 44, 11], [34, 61, 44, 15], [34, 63, 44, 15, "_length"], [34, 70, 44, 15], [34, 72, 44, 15, "_length"], [34, 79, 44, 15], [35, 6, 45, 2], [36, 4, 45, 3], [37, 6, 45, 3, "key"], [37, 9, 45, 3], [38, 6, 45, 3, "value"], [38, 11, 45, 3], [38, 13, 47, 2], [38, 22, 47, 2, "item"], [38, 26, 47, 6, "item"], [38, 27, 47, 7, "index"], [38, 32, 47, 20], [38, 34, 47, 32], [39, 8, 48, 4], [39, 12, 48, 8, "index"], [39, 17, 48, 13], [39, 20, 48, 16], [39, 21, 48, 17], [39, 25, 48, 21, "index"], [39, 30, 48, 26], [39, 38, 48, 26, "_classPrivateFieldLooseBase2"], [39, 66, 48, 26], [39, 67, 48, 26, "default"], [39, 74, 48, 26], [39, 76, 48, 30], [39, 80, 48, 34], [39, 82, 48, 34, "_length"], [39, 89, 48, 34], [39, 91, 48, 34, "_length"], [39, 98, 48, 34], [39, 99, 48, 42], [39, 101, 48, 44], [40, 10, 49, 6], [40, 17, 49, 13], [40, 21, 49, 17], [41, 8, 50, 4], [42, 8, 55, 4], [42, 12, 55, 10, "arrayLike"], [42, 21, 55, 33], [42, 24, 55, 36], [42, 28, 55, 40], [43, 8, 56, 4], [43, 15, 56, 11, "arrayLike"], [43, 24, 56, 20], [43, 25, 56, 21, "index"], [43, 30, 56, 26], [43, 31, 56, 27], [44, 6, 57, 2], [45, 4, 57, 3], [46, 6, 57, 3, "key"], [46, 9, 57, 3], [47, 6, 57, 3, "value"], [47, 11, 57, 3], [47, 13, 62, 2], [47, 22, 62, 2, "namedItem"], [47, 31, 62, 11, "namedItem"], [47, 32, 62, 12, "name"], [47, 36, 62, 24], [47, 38, 62, 36], [48, 8, 63, 4], [48, 15, 63, 11], [48, 19, 63, 15], [49, 6, 64, 2], [50, 4, 64, 3], [51, 6, 64, 3, "key"], [51, 9, 64, 3], [51, 11, 67, 3, "Symbol"], [51, 17, 67, 9], [51, 18, 67, 10, "iterator"], [51, 26, 67, 18], [52, 6, 67, 18, "value"], [52, 11, 67, 18], [52, 13, 67, 2], [52, 22, 67, 2, "value"], [52, 23, 67, 2], [52, 25, 67, 35], [53, 8, 68, 4], [53, 15, 68, 11], [53, 19, 68, 11, "createValueIterator"], [53, 54, 68, 30], [53, 56, 68, 31], [53, 60, 68, 35], [53, 61, 68, 36], [54, 6, 69, 2], [55, 4, 69, 3], [56, 2, 69, 3], [57, 2, 78, 7], [57, 11, 78, 16, "createHTMLCollection"], [57, 31, 78, 36, "createHTMLCollection"], [57, 32, 79, 2, "elements"], [57, 40, 79, 29], [57, 42, 80, 21], [58, 4, 81, 2], [58, 11, 81, 9], [58, 15, 81, 13, "HTMLCollection"], [58, 29, 81, 27], [58, 30, 81, 28, "elements"], [58, 38, 81, 36], [58, 39, 81, 37], [59, 2, 82, 0], [60, 0, 82, 1], [60, 3]], "functionMap": {"names": ["<global>", "HTMLCollection", "constructor", "get__length", "item", "namedItem", "@@iterator", "createHTMLCollection"], "mappings": "AAA;eCoB;ECS;GDW;EEE;GFE;EGE;GHU;EIK;GJE;EKG;GLE;CDC;OOQ"}}, "type": "js/module"}]}