{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 51, "index": 66}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.enableFreeze = enableFreeze;\n  exports.enableScreens = enableScreens;\n  exports.freezeEnabled = freezeEnabled;\n  exports.isNativePlatformSupported = void 0;\n  exports.screensEnabled = screensEnabled;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var isNativePlatformSupported = exports.isNativePlatformSupported = _reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android' || _reactNative.Platform.OS === 'windows';\n  var ENABLE_SCREENS = isNativePlatformSupported;\n  function enableScreens() {\n    var shouldEnableScreens = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n    ENABLE_SCREENS = shouldEnableScreens;\n    if (!isNativePlatformSupported) {\n      return;\n    }\n    if (ENABLE_SCREENS && !_reactNative.UIManager.getViewManagerConfig('RNSScreen')) {\n      console.error(`Screen native module hasn't been linked. Please check the react-native-screens README for more details`);\n    }\n  }\n  var ENABLE_FREEZE = false;\n  function enableFreeze() {\n    var shouldEnableReactFreeze = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n    if (!isNativePlatformSupported) {\n      return;\n    }\n    ENABLE_FREEZE = shouldEnableReactFreeze;\n  }\n  function screensEnabled() {\n    return ENABLE_SCREENS;\n  }\n  function freezeEnabled() {\n    return ENABLE_FREEZE;\n  }\n});", "lineCount": 39, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "enableFreeze"], [7, 22, 1, 13], [7, 25, 1, 13, "enableFreeze"], [7, 37, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "enableScreens"], [8, 23, 1, 13], [8, 26, 1, 13, "enableScreens"], [8, 39, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "freezeEnabled"], [9, 23, 1, 13], [9, 26, 1, 13, "freezeEnabled"], [9, 39, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "isNativePlatformSupported"], [10, 35, 1, 13], [11, 2, 1, 13, "exports"], [11, 9, 1, 13], [11, 10, 1, 13, "screensEnabled"], [11, 24, 1, 13], [11, 27, 1, 13, "screensEnabled"], [11, 41, 1, 13], [12, 2, 3, 0], [12, 6, 3, 0, "_reactNative"], [12, 18, 3, 0], [12, 21, 3, 0, "require"], [12, 28, 3, 0], [12, 29, 3, 0, "_dependencyMap"], [12, 43, 3, 0], [13, 2, 5, 7], [13, 6, 5, 13, "isNativePlatformSupported"], [13, 31, 5, 38], [13, 34, 5, 38, "exports"], [13, 41, 5, 38], [13, 42, 5, 38, "isNativePlatformSupported"], [13, 67, 5, 38], [13, 70, 6, 2, "Platform"], [13, 91, 6, 10], [13, 92, 6, 11, "OS"], [13, 94, 6, 13], [13, 99, 6, 18], [13, 104, 6, 23], [13, 108, 7, 2, "Platform"], [13, 129, 7, 10], [13, 130, 7, 11, "OS"], [13, 132, 7, 13], [13, 137, 7, 18], [13, 146, 7, 27], [13, 150, 8, 2, "Platform"], [13, 171, 8, 10], [13, 172, 8, 11, "OS"], [13, 174, 8, 13], [13, 179, 8, 18], [13, 188, 8, 27], [14, 2, 10, 0], [14, 6, 10, 4, "ENABLE_SCREENS"], [14, 20, 10, 18], [14, 23, 10, 21, "isNativePlatformSupported"], [14, 48, 10, 46], [15, 2, 12, 7], [15, 11, 12, 16, "enableScreens"], [15, 24, 12, 29, "enableScreens"], [15, 25, 12, 29], [15, 27, 12, 58], [16, 4, 12, 58], [16, 8, 12, 30, "shouldEnableScreens"], [16, 27, 12, 49], [16, 30, 12, 49, "arguments"], [16, 39, 12, 49], [16, 40, 12, 49, "length"], [16, 46, 12, 49], [16, 54, 12, 49, "arguments"], [16, 63, 12, 49], [16, 71, 12, 49, "undefined"], [16, 80, 12, 49], [16, 83, 12, 49, "arguments"], [16, 92, 12, 49], [16, 98, 12, 52], [16, 102, 12, 56], [17, 4, 13, 2, "ENABLE_SCREENS"], [17, 18, 13, 16], [17, 21, 13, 19, "shouldEnableScreens"], [17, 40, 13, 38], [18, 4, 15, 2], [18, 8, 15, 6], [18, 9, 15, 7, "isNativePlatformSupported"], [18, 34, 15, 32], [18, 36, 15, 34], [19, 6, 16, 4], [20, 4, 17, 2], [21, 4, 19, 2], [21, 8, 19, 6, "ENABLE_SCREENS"], [21, 22, 19, 20], [21, 26, 19, 24], [21, 27, 19, 25, "UIManager"], [21, 49, 19, 34], [21, 50, 19, 35, "getViewManagerConfig"], [21, 70, 19, 55], [21, 71, 19, 56], [21, 82, 19, 67], [21, 83, 19, 68], [21, 85, 19, 70], [22, 6, 20, 4, "console"], [22, 13, 20, 11], [22, 14, 20, 12, "error"], [22, 19, 20, 17], [22, 20, 21, 6], [22, 124, 22, 4], [22, 125, 22, 5], [23, 4, 23, 2], [24, 2, 24, 0], [25, 2, 26, 0], [25, 6, 26, 4, "ENABLE_FREEZE"], [25, 19, 26, 17], [25, 22, 26, 20], [25, 27, 26, 25], [26, 2, 28, 7], [26, 11, 28, 16, "enableFreeze"], [26, 23, 28, 28, "enableFreeze"], [26, 24, 28, 28], [26, 26, 28, 61], [27, 4, 28, 61], [27, 8, 28, 29, "shouldEnableReactFreeze"], [27, 31, 28, 52], [27, 34, 28, 52, "arguments"], [27, 43, 28, 52], [27, 44, 28, 52, "length"], [27, 50, 28, 52], [27, 58, 28, 52, "arguments"], [27, 67, 28, 52], [27, 75, 28, 52, "undefined"], [27, 84, 28, 52], [27, 87, 28, 52, "arguments"], [27, 96, 28, 52], [27, 102, 28, 55], [27, 106, 28, 59], [28, 4, 29, 2], [28, 8, 29, 6], [28, 9, 29, 7, "isNativePlatformSupported"], [28, 34, 29, 32], [28, 36, 29, 34], [29, 6, 30, 4], [30, 4, 31, 2], [31, 4, 33, 2, "ENABLE_FREEZE"], [31, 17, 33, 15], [31, 20, 33, 18, "shouldEnableReactFreeze"], [31, 43, 33, 41], [32, 2, 34, 0], [33, 2, 36, 7], [33, 11, 36, 16, "screensEnabled"], [33, 25, 36, 30, "screensEnabled"], [33, 26, 36, 30], [33, 28, 36, 33], [34, 4, 37, 2], [34, 11, 37, 9, "ENABLE_SCREENS"], [34, 25, 37, 23], [35, 2, 38, 0], [36, 2, 40, 7], [36, 11, 40, 16, "freezeEnabled"], [36, 24, 40, 29, "freezeEnabled"], [36, 25, 40, 29], [36, 27, 40, 32], [37, 4, 41, 2], [37, 11, 41, 9, "ENABLE_FREEZE"], [37, 24, 41, 22], [38, 2, 42, 0], [39, 0, 42, 1], [39, 3]], "functionMap": {"names": ["<global>", "enableScreens", "enableFreeze", "screensEnabled", "freezeEnabled"], "mappings": "AAA;OCW;CDY;OEI;CFM;OGE;CHE;OIE;CJE"}}, "type": "js/module"}]}