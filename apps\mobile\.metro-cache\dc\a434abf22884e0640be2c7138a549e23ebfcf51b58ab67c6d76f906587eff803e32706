{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 61}, "end": {"line": 2, "column": 52, "index": 113}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useEvent = useEvent;\n  exports.useEventListener = useEventListener;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = require(_dependencyMap[2], \"react\");\n  /**\n   * Type helper that infers the event name from the emitter's events map.\n   */\n\n  /**\n   * Type helper that infers the event listener from the emitter's events map.\n   */\n\n  /**\n   * Type helper that infers the first parameter of the event listener.\n   */\n\n  /**\n   * React hook that listens to events emitted by the given object. The returned value is an event parameter\n   * that gets updated whenever a new event is dispatched.\n   * @param eventEmitter An object that emits events. For example, a native module or shared object or an instance of [`EventEmitter`](#eventemitter).\n   * @param eventName Name of the event to listen to.\n   * @param initialValue An event parameter to use until the event is called for the first time.\n   * @returns A parameter of the event listener.\n   * @example\n   * ```tsx\n   * import { useEvent } from 'expo';\n   * import { VideoPlayer } from 'expo-video';\n   *\n   * export function PlayerStatus({ videoPlayer }: { videoPlayer: VideoPlayer }) {\n   *   const { status } = useEvent(videoPlayer, 'statusChange', { status: videoPlayer.status });\n   *\n   *   return <Text>{`Player status: ${status}`}</Text>;\n   * }\n   * ```\n   */\n  function useEvent(eventEmitter, eventName) {\n    var initialValue = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var _useState = (0, _react.useState)(initialValue),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      event = _useState2[0],\n      setEvent = _useState2[1];\n    var listener = event => setEvent(event);\n    useEventListener(eventEmitter, eventName, listener);\n    return event;\n  }\n\n  /**\n   * React hook that listens to events emitted by the given object and calls the listener function whenever a new event is dispatched.\n   * The event listener is automatically added during the first render and removed when the component unmounts.\n   * @param eventEmitter An object that emits events. For example, a native module or shared object or an instance of [`EventEmitter`](#eventemitter).\n   * @param eventName Name of the event to listen to.\n   * @param listener A function to call when the event is dispatched.\n   * @example\n   * ```tsx\n   * import { useEventListener } from 'expo';\n   * import { useVideoPlayer, VideoView } from 'expo-video';\n   *\n   * export function VideoPlayerView() {\n   *   const player = useVideoPlayer(videoSource);\n   *\n   *   useEventListener(player, 'playingChange', ({ isPlaying }) => {\n   *     console.log('Player is playing:', isPlaying);\n   *   });\n   *\n   *   return <VideoView player={player} />;\n   * }\n   * ```\n   */\n  function useEventListener(eventEmitter, eventName, listener) {\n    // Always use the most recent version of the listener inside the effect,\n    // without memoization so the listeners don't have to be swapped with every render.\n    var listenerRef = (0, _react.useRef)(listener);\n    listenerRef.current = listener;\n    (0, _react.useEffect)(() => {\n      var callback = function () {\n        return listenerRef.current(...arguments);\n      };\n      var subscription = eventEmitter.addListener(eventName, callback);\n      return () => subscription.remove();\n    }, [eventEmitter, eventName, listenerRef]);\n  }\n});", "lineCount": 87, "map": [[9, 2, 2, 0], [9, 6, 2, 0, "_react"], [9, 12, 2, 0], [9, 15, 2, 0, "require"], [9, 22, 2, 0], [9, 23, 2, 0, "_dependencyMap"], [9, 37, 2, 0], [10, 2, 6, 0], [11, 0, 7, 0], [12, 0, 8, 0], [14, 2, 14, 0], [15, 0, 15, 0], [16, 0, 16, 0], [18, 2, 22, 0], [19, 0, 23, 0], [20, 0, 24, 0], [22, 2, 32, 0], [23, 0, 33, 0], [24, 0, 34, 0], [25, 0, 35, 0], [26, 0, 36, 0], [27, 0, 37, 0], [28, 0, 38, 0], [29, 0, 39, 0], [30, 0, 40, 0], [31, 0, 41, 0], [32, 0, 42, 0], [33, 0, 43, 0], [34, 0, 44, 0], [35, 0, 45, 0], [36, 0, 46, 0], [37, 0, 47, 0], [38, 0, 48, 0], [39, 0, 49, 0], [40, 0, 50, 0], [41, 2, 51, 7], [41, 11, 51, 16, "useEvent"], [41, 19, 51, 24, "useEvent"], [41, 20, 57, 2, "eventEmitter"], [41, 32, 57, 40], [41, 34, 58, 2, "eventName"], [41, 43, 58, 23], [41, 45, 60, 54], [42, 4, 60, 54], [42, 8, 59, 2, "initialValue"], [42, 20, 59, 36], [42, 23, 59, 36, "arguments"], [42, 32, 59, 36], [42, 33, 59, 36, "length"], [42, 39, 59, 36], [42, 47, 59, 36, "arguments"], [42, 56, 59, 36], [42, 64, 59, 36, "undefined"], [42, 73, 59, 36], [42, 76, 59, 36, "arguments"], [42, 85, 59, 36], [42, 91, 59, 39], [42, 95, 59, 43], [43, 4, 61, 2], [43, 8, 61, 2, "_useState"], [43, 17, 61, 2], [43, 20, 61, 28], [43, 24, 61, 28, "useState"], [43, 39, 61, 36], [43, 41, 62, 4, "initialValue"], [43, 53, 63, 2], [43, 54, 63, 3], [44, 6, 63, 3, "_useState2"], [44, 16, 63, 3], [44, 23, 63, 3, "_slicedToArray2"], [44, 38, 63, 3], [44, 39, 63, 3, "default"], [44, 46, 63, 3], [44, 48, 63, 3, "_useState"], [44, 57, 63, 3], [45, 6, 61, 9, "event"], [45, 11, 61, 14], [45, 14, 61, 14, "_useState2"], [45, 24, 61, 14], [46, 6, 61, 16, "setEvent"], [46, 14, 61, 24], [46, 17, 61, 24, "_useState2"], [46, 27, 61, 24], [47, 4, 64, 2], [47, 8, 64, 8, "listener"], [47, 16, 64, 16], [47, 19, 64, 20, "event"], [47, 24, 64, 56], [47, 28, 64, 61, "setEvent"], [47, 36, 64, 69], [47, 37, 64, 70, "event"], [47, 42, 64, 75], [47, 43, 64, 76], [48, 4, 66, 2, "useEventListener"], [48, 20, 66, 18], [48, 21, 66, 19, "eventEmitter"], [48, 33, 66, 31], [48, 35, 66, 33, "eventName"], [48, 44, 66, 42], [48, 46, 66, 44, "listener"], [48, 54, 66, 98], [48, 55, 66, 99], [49, 4, 68, 2], [49, 11, 68, 9, "event"], [49, 16, 68, 14], [50, 2, 69, 0], [52, 2, 71, 0], [53, 0, 72, 0], [54, 0, 73, 0], [55, 0, 74, 0], [56, 0, 75, 0], [57, 0, 76, 0], [58, 0, 77, 0], [59, 0, 78, 0], [60, 0, 79, 0], [61, 0, 80, 0], [62, 0, 81, 0], [63, 0, 82, 0], [64, 0, 83, 0], [65, 0, 84, 0], [66, 0, 85, 0], [67, 0, 86, 0], [68, 0, 87, 0], [69, 0, 88, 0], [70, 0, 89, 0], [71, 0, 90, 0], [72, 0, 91, 0], [73, 0, 92, 0], [74, 2, 93, 7], [74, 11, 93, 16, "useEventListener"], [74, 27, 93, 32, "useEventListener"], [74, 28, 97, 2, "eventEmitter"], [74, 40, 97, 40], [74, 42, 97, 42, "eventName"], [74, 51, 97, 63], [74, 53, 97, 65, "listener"], [74, 61, 97, 89], [74, 63, 97, 97], [75, 4, 98, 2], [76, 4, 99, 2], [77, 4, 100, 2], [77, 8, 100, 8, "listenerRef"], [77, 19, 100, 19], [77, 22, 100, 22], [77, 26, 100, 22, "useRef"], [77, 39, 100, 28], [77, 41, 100, 45, "listener"], [77, 49, 100, 53], [77, 50, 100, 54], [78, 4, 101, 2, "listenerRef"], [78, 15, 101, 13], [78, 16, 101, 14, "current"], [78, 23, 101, 21], [78, 26, 101, 24, "listener"], [78, 34, 101, 32], [79, 4, 103, 2], [79, 8, 103, 2, "useEffect"], [79, 24, 103, 11], [79, 26, 103, 12], [79, 32, 103, 18], [80, 6, 104, 4], [80, 10, 104, 10, "callback"], [80, 18, 104, 18], [80, 21, 104, 21], [80, 30, 104, 21, "callback"], [80, 31, 104, 21], [81, 8, 104, 21], [81, 15, 104, 41, "listenerRef"], [81, 26, 104, 52], [81, 27, 104, 53, "current"], [81, 34, 104, 60], [81, 35, 104, 61], [81, 38, 104, 61, "arguments"], [81, 47, 104, 68], [81, 48, 104, 69], [82, 6, 104, 69], [83, 6, 105, 4], [83, 10, 105, 10, "subscription"], [83, 22, 105, 22], [83, 25, 105, 25, "eventEmitter"], [83, 37, 105, 37], [83, 38, 105, 38, "addListener"], [83, 49, 105, 49], [83, 50, 106, 6, "eventName"], [83, 59, 106, 15], [83, 61, 107, 6, "callback"], [83, 69, 108, 4], [83, 70, 108, 5], [84, 6, 110, 4], [84, 13, 110, 11], [84, 19, 110, 17, "subscription"], [84, 31, 110, 29], [84, 32, 110, 30, "remove"], [84, 38, 110, 36], [84, 39, 110, 37], [84, 40, 110, 38], [85, 4, 111, 2], [85, 5, 111, 3], [85, 7, 111, 5], [85, 8, 111, 6, "eventEmitter"], [85, 20, 111, 18], [85, 22, 111, 20, "eventName"], [85, 31, 111, 29], [85, 33, 111, 31, "listenerRef"], [85, 44, 111, 42], [85, 45, 111, 43], [85, 46, 111, 44], [86, 2, 112, 0], [87, 0, 112, 1], [87, 3]], "functionMap": {"names": ["<global>", "useEvent", "listener", "useEventListener", "useEffect$argument_0", "callback", "<anonymous>"], "mappings": "AAA;OCkD;mBCa,yDD;CDK;OGwB;YCU;qBCC,gDD;WEM,2BF;GDC;CHC"}}, "type": "js/module"}]}