{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "../../Components/TextInput/TextInputState", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 71}}], "key": "ZvzCkbkBq73a65QYi57u7rMx5l0=", "exportNames": ["*"]}}, {"name": "../../ReactNative/RendererProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 82}}], "key": "j84sSm9ab86FoVeNurFPMT0HcEQ=", "exportNames": ["*"]}}, {"name": "../FabricUIManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 54}}], "key": "l2+5h+qW5bLsH2Lgxbyo10tXaR0=", "exportNames": ["*"]}}, {"name": "./ReactNativeAttributePayload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 53}}], "key": "7MKpffwOOuP3/mt5XU0ESbB7e30=", "exportNames": ["*"]}}, {"name": "./warnForStyleProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 52}}], "key": "QYVGyPVNwS72YdeCkBxH9blrfk8=", "exportNames": ["*"]}}, {"name": "nullthrows", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 36}}], "key": "epufkdgpKN0G543QKwfSBBl0bWM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _TextInputState = _interopRequireDefault(require(_dependencyMap[3], \"../../Components/TextInput/TextInputState\"));\n  var _RendererProxy = require(_dependencyMap[4], \"../../ReactNative/RendererProxy\");\n  var _FabricUIManager = require(_dependencyMap[5], \"../FabricUIManager\");\n  var _ReactNativeAttributePayload = require(_dependencyMap[6], \"./ReactNativeAttributePayload\");\n  var _warnForStyleProps = _interopRequireDefault(require(_dependencyMap[7], \"./warnForStyleProps\"));\n  var _nullthrows2 = _interopRequireDefault(require(_dependencyMap[8], \"nullthrows\"));\n  var _nullthrows = (0, _nullthrows2.default)((0, _FabricUIManager.getFabricUIManager)()),\n    fabricMeasure = _nullthrows.measure,\n    fabricMeasureInWindow = _nullthrows.measureInWindow,\n    fabricMeasureLayout = _nullthrows.measureLayout,\n    fabricGetBoundingClientRect = _nullthrows.getBoundingClientRect,\n    _setNativeProps = _nullthrows.setNativeProps;\n  var noop = () => {};\n  var ReactFabricHostComponent = exports.default = /*#__PURE__*/function () {\n    function ReactFabricHostComponent(tag, viewConfig, internalInstanceHandle) {\n      (0, _classCallCheck2.default)(this, ReactFabricHostComponent);\n      this.__nativeTag = tag;\n      this._viewConfig = viewConfig;\n      this.__internalInstanceHandle = internalInstanceHandle;\n    }\n    return (0, _createClass2.default)(ReactFabricHostComponent, [{\n      key: \"blur\",\n      value: function blur() {\n        _TextInputState.default.blurTextInput(this);\n      }\n    }, {\n      key: \"focus\",\n      value: function focus() {\n        _TextInputState.default.focusTextInput(this);\n      }\n    }, {\n      key: \"measure\",\n      value: function measure(callback) {\n        var node = (0, _RendererProxy.getNodeFromInternalInstanceHandle)(this.__internalInstanceHandle);\n        if (node != null) {\n          fabricMeasure(node, callback);\n        }\n      }\n    }, {\n      key: \"measureInWindow\",\n      value: function measureInWindow(callback) {\n        var node = (0, _RendererProxy.getNodeFromInternalInstanceHandle)(this.__internalInstanceHandle);\n        if (node != null) {\n          fabricMeasureInWindow(node, callback);\n        }\n      }\n    }, {\n      key: \"measureLayout\",\n      value: function measureLayout(relativeToNativeNode, onSuccess, onFail) {\n        if (typeof relativeToNativeNode === 'number' || !(relativeToNativeNode instanceof ReactFabricHostComponent)) {\n          if (__DEV__) {\n            console.error('Warning: ref.measureLayout must be called with a ref to a native component.');\n          }\n          return;\n        }\n        var toStateNode = (0, _RendererProxy.getNodeFromInternalInstanceHandle)(this.__internalInstanceHandle);\n        var fromStateNode = (0, _RendererProxy.getNodeFromInternalInstanceHandle)(relativeToNativeNode.__internalInstanceHandle);\n        if (toStateNode != null && fromStateNode != null) {\n          fabricMeasureLayout(toStateNode, fromStateNode, onFail != null ? onFail : noop, onSuccess != null ? onSuccess : noop);\n        }\n      }\n    }, {\n      key: \"unstable_getBoundingClientRect\",\n      value: function unstable_getBoundingClientRect() {\n        var node = (0, _RendererProxy.getNodeFromInternalInstanceHandle)(this.__internalInstanceHandle);\n        if (node != null) {\n          var rect = fabricGetBoundingClientRect(node, true);\n          if (rect) {\n            return new DOMRect(rect[0], rect[1], rect[2], rect[3]);\n          }\n        }\n        return new DOMRect(0, 0, 0, 0);\n      }\n    }, {\n      key: \"setNativeProps\",\n      value: function setNativeProps(nativeProps) {\n        if (__DEV__) {\n          (0, _warnForStyleProps.default)(nativeProps, this._viewConfig.validAttributes);\n        }\n        var updatePayload = (0, _ReactNativeAttributePayload.create)(nativeProps, this._viewConfig.validAttributes);\n        var node = (0, _RendererProxy.getNodeFromInternalInstanceHandle)(this.__internalInstanceHandle);\n        if (node != null && updatePayload != null) {\n          _setNativeProps(node, updatePayload);\n        }\n      }\n    }]);\n  }();\n});", "lineCount": 96, "map": [[9, 2, 23, 0], [9, 6, 23, 0, "_TextInputState"], [9, 21, 23, 0], [9, 24, 23, 0, "_interopRequireDefault"], [9, 46, 23, 0], [9, 47, 23, 0, "require"], [9, 54, 23, 0], [9, 55, 23, 0, "_dependencyMap"], [9, 69, 23, 0], [10, 2, 24, 0], [10, 6, 24, 0, "_RendererProxy"], [10, 20, 24, 0], [10, 23, 24, 0, "require"], [10, 30, 24, 0], [10, 31, 24, 0, "_dependencyMap"], [10, 45, 24, 0], [11, 2, 25, 0], [11, 6, 25, 0, "_FabricUIManager"], [11, 22, 25, 0], [11, 25, 25, 0, "require"], [11, 32, 25, 0], [11, 33, 25, 0, "_dependencyMap"], [11, 47, 25, 0], [12, 2, 26, 0], [12, 6, 26, 0, "_ReactNativeAttributePayload"], [12, 34, 26, 0], [12, 37, 26, 0, "require"], [12, 44, 26, 0], [12, 45, 26, 0, "_dependencyMap"], [12, 59, 26, 0], [13, 2, 27, 0], [13, 6, 27, 0, "_warnForStyleProps"], [13, 24, 27, 0], [13, 27, 27, 0, "_interopRequireDefault"], [13, 49, 27, 0], [13, 50, 27, 0, "require"], [13, 57, 27, 0], [13, 58, 27, 0, "_dependencyMap"], [13, 72, 27, 0], [14, 2, 28, 0], [14, 6, 28, 0, "_nullthrows2"], [14, 18, 28, 0], [14, 21, 28, 0, "_interopRequireDefault"], [14, 43, 28, 0], [14, 44, 28, 0, "require"], [14, 51, 28, 0], [14, 52, 28, 0, "_dependencyMap"], [14, 66, 28, 0], [15, 2, 30, 0], [15, 6, 30, 0, "_nullthrows"], [15, 17, 30, 0], [15, 20, 36, 4], [15, 24, 36, 4, "nullthrows"], [15, 44, 36, 14], [15, 46, 36, 15], [15, 50, 36, 15, "getFabricUIManager"], [15, 85, 36, 33], [15, 87, 36, 34], [15, 88, 36, 35], [15, 89, 36, 36], [16, 4, 31, 11, "fabricMeasure"], [16, 17, 31, 24], [16, 20, 31, 24, "_nullthrows"], [16, 31, 31, 24], [16, 32, 31, 2, "measure"], [16, 39, 31, 9], [17, 4, 32, 19, "fabricMeasureInWindow"], [17, 25, 32, 40], [17, 28, 32, 40, "_nullthrows"], [17, 39, 32, 40], [17, 40, 32, 2, "measureInWindow"], [17, 55, 32, 17], [18, 4, 33, 17, "fabricMeasureLayout"], [18, 23, 33, 36], [18, 26, 33, 36, "_nullthrows"], [18, 37, 33, 36], [18, 38, 33, 2, "measureLayout"], [18, 51, 33, 15], [19, 4, 34, 25, "fabricGetBoundingClientRect"], [19, 31, 34, 52], [19, 34, 34, 52, "_nullthrows"], [19, 45, 34, 52], [19, 46, 34, 2, "getBoundingClientRect"], [19, 67, 34, 23], [20, 4, 35, 2, "setNativeProps"], [20, 19, 35, 16], [20, 22, 35, 16, "_nullthrows"], [20, 33, 35, 16], [20, 34, 35, 2, "setNativeProps"], [20, 48, 35, 16], [21, 2, 38, 0], [21, 6, 38, 6, "noop"], [21, 10, 38, 10], [21, 13, 38, 13, "noop"], [21, 14, 38, 13], [21, 19, 38, 19], [21, 20, 38, 20], [21, 21, 38, 21], [22, 2, 38, 22], [22, 6, 43, 21, "ReactFabricHostComponent"], [22, 30, 43, 45], [22, 33, 43, 45, "exports"], [22, 40, 43, 45], [22, 41, 43, 45, "default"], [22, 48, 43, 45], [23, 4, 52, 2], [23, 13, 52, 2, "ReactFabricHostComponent"], [23, 38, 53, 4, "tag"], [23, 41, 53, 15], [23, 43, 54, 4, "viewConfig"], [23, 53, 54, 26], [23, 55, 55, 4, "internalInstanceHandle"], [23, 77, 55, 50], [23, 79, 56, 4], [24, 6, 56, 4], [24, 10, 56, 4, "_classCallCheck2"], [24, 26, 56, 4], [24, 27, 56, 4, "default"], [24, 34, 56, 4], [24, 42, 56, 4, "ReactFabricHostComponent"], [24, 66, 56, 4], [25, 6, 57, 4], [25, 10, 57, 8], [25, 11, 57, 9, "__nativeTag"], [25, 22, 57, 20], [25, 25, 57, 23, "tag"], [25, 28, 57, 26], [26, 6, 58, 4], [26, 10, 58, 8], [26, 11, 58, 9, "_viewConfig"], [26, 22, 58, 20], [26, 25, 58, 23, "viewConfig"], [26, 35, 58, 33], [27, 6, 59, 4], [27, 10, 59, 8], [27, 11, 59, 9, "__internalInstanceHandle"], [27, 35, 59, 33], [27, 38, 59, 36, "internalInstanceHandle"], [27, 60, 59, 58], [28, 4, 60, 2], [29, 4, 60, 3], [29, 15, 60, 3, "_createClass2"], [29, 28, 60, 3], [29, 29, 60, 3, "default"], [29, 36, 60, 3], [29, 38, 60, 3, "ReactFabricHostComponent"], [29, 62, 60, 3], [30, 6, 60, 3, "key"], [30, 9, 60, 3], [31, 6, 60, 3, "value"], [31, 11, 60, 3], [31, 13, 62, 2], [31, 22, 62, 2, "blur"], [31, 26, 62, 6, "blur"], [31, 27, 62, 6], [31, 29, 62, 9], [32, 8, 63, 4, "TextInputState"], [32, 31, 63, 18], [32, 32, 63, 19, "blurTextInput"], [32, 45, 63, 32], [32, 46, 63, 33], [32, 50, 63, 37], [32, 51, 63, 38], [33, 6, 64, 2], [34, 4, 64, 3], [35, 6, 64, 3, "key"], [35, 9, 64, 3], [36, 6, 64, 3, "value"], [36, 11, 64, 3], [36, 13, 66, 2], [36, 22, 66, 2, "focus"], [36, 27, 66, 7, "focus"], [36, 28, 66, 7], [36, 30, 66, 10], [37, 8, 67, 4, "TextInputState"], [37, 31, 67, 18], [37, 32, 67, 19, "focusTextInput"], [37, 46, 67, 33], [37, 47, 67, 34], [37, 51, 67, 38], [37, 52, 67, 39], [38, 6, 68, 2], [39, 4, 68, 3], [40, 6, 68, 3, "key"], [40, 9, 68, 3], [41, 6, 68, 3, "value"], [41, 11, 68, 3], [41, 13, 70, 2], [41, 22, 70, 2, "measure"], [41, 29, 70, 9, "measure"], [41, 30, 70, 10, "callback"], [41, 38, 70, 44], [41, 40, 70, 46], [42, 8, 71, 4], [42, 12, 71, 10, "node"], [42, 16, 71, 14], [42, 19, 71, 17], [42, 23, 71, 17, "getNodeFromInternalInstanceHandle"], [42, 71, 71, 50], [42, 73, 72, 6], [42, 77, 72, 10], [42, 78, 72, 11, "__internalInstanceHandle"], [42, 102, 73, 4], [42, 103, 73, 5], [43, 8, 74, 4], [43, 12, 74, 8, "node"], [43, 16, 74, 12], [43, 20, 74, 16], [43, 24, 74, 20], [43, 26, 74, 22], [44, 10, 75, 6, "fabricMeasure"], [44, 23, 75, 19], [44, 24, 75, 20, "node"], [44, 28, 75, 24], [44, 30, 75, 26, "callback"], [44, 38, 75, 34], [44, 39, 75, 35], [45, 8, 76, 4], [46, 6, 77, 2], [47, 4, 77, 3], [48, 6, 77, 3, "key"], [48, 9, 77, 3], [49, 6, 77, 3, "value"], [49, 11, 77, 3], [49, 13, 79, 2], [49, 22, 79, 2, "measureInWindow"], [49, 37, 79, 17, "measureInWindow"], [49, 38, 79, 18, "callback"], [49, 46, 79, 60], [49, 48, 79, 62], [50, 8, 80, 4], [50, 12, 80, 10, "node"], [50, 16, 80, 14], [50, 19, 80, 17], [50, 23, 80, 17, "getNodeFromInternalInstanceHandle"], [50, 71, 80, 50], [50, 73, 81, 6], [50, 77, 81, 10], [50, 78, 81, 11, "__internalInstanceHandle"], [50, 102, 82, 4], [50, 103, 82, 5], [51, 8, 83, 4], [51, 12, 83, 8, "node"], [51, 16, 83, 12], [51, 20, 83, 16], [51, 24, 83, 20], [51, 26, 83, 22], [52, 10, 84, 6, "fabricMeasureInWindow"], [52, 31, 84, 27], [52, 32, 84, 28, "node"], [52, 36, 84, 32], [52, 38, 84, 34, "callback"], [52, 46, 84, 42], [52, 47, 84, 43], [53, 8, 85, 4], [54, 6, 86, 2], [55, 4, 86, 3], [56, 6, 86, 3, "key"], [56, 9, 86, 3], [57, 6, 86, 3, "value"], [57, 11, 86, 3], [57, 13, 88, 2], [57, 22, 88, 2, "measureLayout"], [57, 35, 88, 15, "measureLayout"], [57, 36, 89, 4, "relativeToNativeNode"], [57, 56, 89, 47], [57, 58, 90, 4, "onSuccess"], [57, 67, 90, 45], [57, 69, 91, 4, "onFail"], [57, 75, 91, 23], [57, 77, 92, 4], [58, 8, 93, 4], [58, 12, 94, 6], [58, 19, 94, 13, "relativeToNativeNode"], [58, 39, 94, 33], [58, 44, 94, 38], [58, 52, 94, 46], [58, 56, 95, 6], [58, 58, 95, 8, "relativeToNativeNode"], [58, 78, 95, 28], [58, 90, 95, 40, "ReactFabricHostComponent"], [58, 114, 95, 64], [58, 115, 95, 65], [58, 117, 96, 6], [59, 10, 97, 6], [59, 14, 97, 10, "__DEV__"], [59, 21, 97, 17], [59, 23, 97, 19], [60, 12, 98, 8, "console"], [60, 19, 98, 15], [60, 20, 98, 16, "error"], [60, 25, 98, 21], [60, 26, 99, 10], [60, 103, 100, 8], [60, 104, 100, 9], [61, 10, 101, 6], [62, 10, 103, 6], [63, 8, 104, 4], [64, 8, 106, 4], [64, 12, 106, 10, "toStateNode"], [64, 23, 106, 21], [64, 26, 106, 24], [64, 30, 106, 24, "getNodeFromInternalInstanceHandle"], [64, 78, 106, 57], [64, 80, 107, 6], [64, 84, 107, 10], [64, 85, 107, 11, "__internalInstanceHandle"], [64, 109, 108, 4], [64, 110, 108, 5], [65, 8, 109, 4], [65, 12, 109, 10, "fromStateNode"], [65, 25, 109, 23], [65, 28, 109, 26], [65, 32, 109, 26, "getNodeFromInternalInstanceHandle"], [65, 80, 109, 59], [65, 82, 110, 6, "relativeToNativeNode"], [65, 102, 110, 26], [65, 103, 110, 27, "__internalInstanceHandle"], [65, 127, 111, 4], [65, 128, 111, 5], [66, 8, 113, 4], [66, 12, 113, 8, "toStateNode"], [66, 23, 113, 19], [66, 27, 113, 23], [66, 31, 113, 27], [66, 35, 113, 31, "fromStateNode"], [66, 48, 113, 44], [66, 52, 113, 48], [66, 56, 113, 52], [66, 58, 113, 54], [67, 10, 114, 6, "fabricMeasureLayout"], [67, 29, 114, 25], [67, 30, 115, 8, "toStateNode"], [67, 41, 115, 19], [67, 43, 116, 8, "fromStateNode"], [67, 56, 116, 21], [67, 58, 117, 8, "onFail"], [67, 64, 117, 14], [67, 68, 117, 18], [67, 72, 117, 22], [67, 75, 117, 25, "onFail"], [67, 81, 117, 31], [67, 84, 117, 34, "noop"], [67, 88, 117, 38], [67, 90, 118, 8, "onSuccess"], [67, 99, 118, 17], [67, 103, 118, 21], [67, 107, 118, 25], [67, 110, 118, 28, "onSuccess"], [67, 119, 118, 37], [67, 122, 118, 40, "noop"], [67, 126, 119, 6], [67, 127, 119, 7], [68, 8, 120, 4], [69, 6, 121, 2], [70, 4, 121, 3], [71, 6, 121, 3, "key"], [71, 9, 121, 3], [72, 6, 121, 3, "value"], [72, 11, 121, 3], [72, 13, 123, 2], [72, 22, 123, 2, "unstable_getBoundingClientRect"], [72, 52, 123, 32, "unstable_getBoundingClientRect"], [72, 53, 123, 32], [72, 55, 123, 44], [73, 8, 124, 4], [73, 12, 124, 10, "node"], [73, 16, 124, 14], [73, 19, 124, 17], [73, 23, 124, 17, "getNodeFromInternalInstanceHandle"], [73, 71, 124, 50], [73, 73, 125, 6], [73, 77, 125, 10], [73, 78, 125, 11, "__internalInstanceHandle"], [73, 102, 126, 4], [73, 103, 126, 5], [74, 8, 127, 4], [74, 12, 127, 8, "node"], [74, 16, 127, 12], [74, 20, 127, 16], [74, 24, 127, 20], [74, 26, 127, 22], [75, 10, 128, 6], [75, 14, 128, 12, "rect"], [75, 18, 128, 16], [75, 21, 128, 19, "fabricGetBoundingClientRect"], [75, 48, 128, 46], [75, 49, 128, 47, "node"], [75, 53, 128, 51], [75, 55, 128, 53], [75, 59, 128, 57], [75, 60, 128, 58], [76, 10, 130, 6], [76, 14, 130, 10, "rect"], [76, 18, 130, 14], [76, 20, 130, 16], [77, 12, 131, 8], [77, 19, 131, 15], [77, 23, 131, 19, "DOMRect"], [77, 30, 131, 26], [77, 31, 131, 27, "rect"], [77, 35, 131, 31], [77, 36, 131, 32], [77, 37, 131, 33], [77, 38, 131, 34], [77, 40, 131, 36, "rect"], [77, 44, 131, 40], [77, 45, 131, 41], [77, 46, 131, 42], [77, 47, 131, 43], [77, 49, 131, 45, "rect"], [77, 53, 131, 49], [77, 54, 131, 50], [77, 55, 131, 51], [77, 56, 131, 52], [77, 58, 131, 54, "rect"], [77, 62, 131, 58], [77, 63, 131, 59], [77, 64, 131, 60], [77, 65, 131, 61], [77, 66, 131, 62], [78, 10, 132, 6], [79, 8, 133, 4], [80, 8, 136, 4], [80, 15, 136, 11], [80, 19, 136, 15, "DOMRect"], [80, 26, 136, 22], [80, 27, 136, 23], [80, 28, 136, 24], [80, 30, 136, 26], [80, 31, 136, 27], [80, 33, 136, 29], [80, 34, 136, 30], [80, 36, 136, 32], [80, 37, 136, 33], [80, 38, 136, 34], [81, 6, 137, 2], [82, 4, 137, 3], [83, 6, 137, 3, "key"], [83, 9, 137, 3], [84, 6, 137, 3, "value"], [84, 11, 137, 3], [84, 13, 139, 2], [84, 22, 139, 2, "setNativeProps"], [84, 36, 139, 16, "setNativeProps"], [84, 37, 139, 17, "nativeProps"], [84, 48, 139, 35], [84, 50, 139, 43], [85, 8, 140, 4], [85, 12, 140, 8, "__DEV__"], [85, 19, 140, 15], [85, 21, 140, 17], [86, 10, 141, 6], [86, 14, 141, 6, "warnForStyleProps"], [86, 40, 141, 23], [86, 42, 141, 24, "nativeProps"], [86, 53, 141, 35], [86, 55, 141, 37], [86, 59, 141, 41], [86, 60, 141, 42, "_viewConfig"], [86, 71, 141, 53], [86, 72, 141, 54, "validAttributes"], [86, 87, 141, 69], [86, 88, 141, 70], [87, 8, 142, 4], [88, 8, 143, 4], [88, 12, 143, 10, "updatePayload"], [88, 25, 143, 23], [88, 28, 143, 26], [88, 32, 143, 26, "create"], [88, 67, 143, 32], [88, 69, 143, 33, "nativeProps"], [88, 80, 143, 44], [88, 82, 143, 46], [88, 86, 143, 50], [88, 87, 143, 51, "_viewConfig"], [88, 98, 143, 62], [88, 99, 143, 63, "validAttributes"], [88, 114, 143, 78], [88, 115, 143, 79], [89, 8, 145, 4], [89, 12, 145, 10, "node"], [89, 16, 145, 14], [89, 19, 145, 17], [89, 23, 145, 17, "getNodeFromInternalInstanceHandle"], [89, 71, 145, 50], [89, 73, 146, 6], [89, 77, 146, 10], [89, 78, 146, 11, "__internalInstanceHandle"], [89, 102, 147, 4], [89, 103, 147, 5], [90, 8, 148, 4], [90, 12, 148, 8, "node"], [90, 16, 148, 12], [90, 20, 148, 16], [90, 24, 148, 20], [90, 28, 148, 24, "updatePayload"], [90, 41, 148, 37], [90, 45, 148, 41], [90, 49, 148, 45], [90, 51, 148, 47], [91, 10, 149, 6, "setNativeProps"], [91, 25, 149, 20], [91, 26, 149, 21, "node"], [91, 30, 149, 25], [91, 32, 149, 27, "updatePayload"], [91, 45, 149, 40], [91, 46, 149, 41], [92, 8, 150, 4], [93, 6, 151, 2], [94, 4, 151, 3], [95, 2, 151, 3], [96, 0, 151, 3], [96, 3]], "functionMap": {"names": ["<global>", "noop", "ReactFabricHostComponent", "constructor", "blur", "focus", "measure", "measureInWindow", "measureLayout", "unstable_getBoundingClientRect", "setNativeProps"], "mappings": "AAA;aCqC,QD;eEK;ECS;GDQ;EEE;GFE;EGE;GHE;EIE;GJO;EKE;GLO;EME;GNiC;EOE;GPc;EQE;GRY"}}, "type": "js/module"}]}