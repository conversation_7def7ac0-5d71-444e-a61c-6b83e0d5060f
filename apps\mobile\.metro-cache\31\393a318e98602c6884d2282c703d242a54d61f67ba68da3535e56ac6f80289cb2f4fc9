{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./EventPolyfill", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 44}}], "key": "E7Go7GBJO//bHe0KX5wmT8ygiAY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/createClass\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _EventPolyfill2 = _interopRequireDefault(require(_dependencyMap[6], \"./EventPolyfill\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var CustomEvent = /*#__PURE__*/function (_EventPolyfill) {\n    function CustomEvent(typeArg, options) {\n      var _this;\n      (0, _classCallCheck2.default)(this, CustomEvent);\n      var bubbles = options.bubbles,\n        cancelable = options.cancelable,\n        composed = options.composed;\n      _this = _callSuper(this, CustomEvent, [typeArg, {\n        bubbles,\n        cancelable,\n        composed\n      }]);\n      _this.detail = options.detail;\n      return _this;\n    }\n    (0, _inherits2.default)(CustomEvent, _EventPolyfill);\n    return (0, _createClass2.default)(CustomEvent);\n  }(_EventPolyfill2.default);\n  var _default = exports.default = CustomEvent;\n});", "lineCount": 34, "map": [[12, 2, 12, 0], [12, 6, 12, 0, "_EventPolyfill2"], [12, 21, 12, 0], [12, 24, 12, 0, "_interopRequireDefault"], [12, 46, 12, 0], [12, 47, 12, 0, "require"], [12, 54, 12, 0], [12, 55, 12, 0, "_dependencyMap"], [12, 69, 12, 0], [13, 2, 12, 44], [13, 11, 12, 44, "_callSuper"], [13, 22, 12, 44, "t"], [13, 23, 12, 44], [13, 25, 12, 44, "o"], [13, 26, 12, 44], [13, 28, 12, 44, "e"], [13, 29, 12, 44], [13, 40, 12, 44, "o"], [13, 41, 12, 44], [13, 48, 12, 44, "_getPrototypeOf2"], [13, 64, 12, 44], [13, 65, 12, 44, "default"], [13, 72, 12, 44], [13, 74, 12, 44, "o"], [13, 75, 12, 44], [13, 82, 12, 44, "_possibleConstructorReturn2"], [13, 109, 12, 44], [13, 110, 12, 44, "default"], [13, 117, 12, 44], [13, 119, 12, 44, "t"], [13, 120, 12, 44], [13, 122, 12, 44, "_isNativeReflectConstruct"], [13, 147, 12, 44], [13, 152, 12, 44, "Reflect"], [13, 159, 12, 44], [13, 160, 12, 44, "construct"], [13, 169, 12, 44], [13, 170, 12, 44, "o"], [13, 171, 12, 44], [13, 173, 12, 44, "e"], [13, 174, 12, 44], [13, 186, 12, 44, "_getPrototypeOf2"], [13, 202, 12, 44], [13, 203, 12, 44, "default"], [13, 210, 12, 44], [13, 212, 12, 44, "t"], [13, 213, 12, 44], [13, 215, 12, 44, "constructor"], [13, 226, 12, 44], [13, 230, 12, 44, "o"], [13, 231, 12, 44], [13, 232, 12, 44, "apply"], [13, 237, 12, 44], [13, 238, 12, 44, "t"], [13, 239, 12, 44], [13, 241, 12, 44, "e"], [13, 242, 12, 44], [14, 2, 12, 44], [14, 11, 12, 44, "_isNativeReflectConstruct"], [14, 37, 12, 44], [14, 51, 12, 44, "t"], [14, 52, 12, 44], [14, 56, 12, 44, "Boolean"], [14, 63, 12, 44], [14, 64, 12, 44, "prototype"], [14, 73, 12, 44], [14, 74, 12, 44, "valueOf"], [14, 81, 12, 44], [14, 82, 12, 44, "call"], [14, 86, 12, 44], [14, 87, 12, 44, "Reflect"], [14, 94, 12, 44], [14, 95, 12, 44, "construct"], [14, 104, 12, 44], [14, 105, 12, 44, "Boolean"], [14, 112, 12, 44], [14, 145, 12, 44, "t"], [14, 146, 12, 44], [14, 159, 12, 44, "_isNativeReflectConstruct"], [14, 184, 12, 44], [14, 196, 12, 44, "_isNativeReflectConstruct"], [14, 197, 12, 44], [14, 210, 12, 44, "t"], [14, 211, 12, 44], [15, 2, 12, 44], [15, 6, 21, 6, "CustomEvent"], [15, 17, 21, 17], [15, 43, 21, 17, "_EventPolyfill"], [15, 57, 21, 17], [16, 4, 24, 2], [16, 13, 24, 2, "CustomEvent"], [16, 25, 24, 14, "typeArg"], [16, 32, 24, 29], [16, 34, 24, 31, "options"], [16, 41, 24, 59], [16, 43, 24, 61], [17, 6, 24, 61], [17, 10, 24, 61, "_this"], [17, 15, 24, 61], [18, 6, 24, 61], [18, 10, 24, 61, "_classCallCheck2"], [18, 26, 24, 61], [18, 27, 24, 61, "default"], [18, 34, 24, 61], [18, 42, 24, 61, "CustomEvent"], [18, 53, 24, 61], [19, 6, 25, 4], [19, 10, 25, 11, "bubbles"], [19, 17, 25, 18], [19, 20, 25, 44, "options"], [19, 27, 25, 51], [19, 28, 25, 11, "bubbles"], [19, 35, 25, 18], [20, 8, 25, 20, "cancelable"], [20, 18, 25, 30], [20, 21, 25, 44, "options"], [20, 28, 25, 51], [20, 29, 25, 20, "cancelable"], [20, 39, 25, 30], [21, 8, 25, 32, "composed"], [21, 16, 25, 40], [21, 19, 25, 44, "options"], [21, 26, 25, 51], [21, 27, 25, 32, "composed"], [21, 35, 25, 40], [22, 6, 26, 4, "_this"], [22, 11, 26, 4], [22, 14, 26, 4, "_callSuper"], [22, 24, 26, 4], [22, 31, 26, 4, "CustomEvent"], [22, 42, 26, 4], [22, 45, 26, 10, "typeArg"], [22, 52, 26, 17], [22, 54, 26, 19], [23, 8, 26, 20, "bubbles"], [23, 15, 26, 27], [24, 8, 26, 29, "cancelable"], [24, 18, 26, 39], [25, 8, 26, 41, "composed"], [26, 6, 26, 49], [26, 7, 26, 50], [27, 6, 28, 4, "_this"], [27, 11, 28, 4], [27, 12, 28, 9, "detail"], [27, 18, 28, 15], [27, 21, 28, 18, "options"], [27, 28, 28, 25], [27, 29, 28, 26, "detail"], [27, 35, 28, 32], [28, 6, 28, 33], [28, 13, 28, 33, "_this"], [28, 18, 28, 33], [29, 4, 29, 2], [30, 4, 29, 3], [30, 8, 29, 3, "_inherits2"], [30, 18, 29, 3], [30, 19, 29, 3, "default"], [30, 26, 29, 3], [30, 28, 29, 3, "CustomEvent"], [30, 39, 29, 3], [30, 41, 29, 3, "_EventPolyfill"], [30, 55, 29, 3], [31, 4, 29, 3], [31, 15, 29, 3, "_createClass2"], [31, 28, 29, 3], [31, 29, 29, 3, "default"], [31, 36, 29, 3], [31, 38, 29, 3, "CustomEvent"], [31, 49, 29, 3], [32, 2, 29, 3], [32, 4, 21, 26, "EventPolyfill"], [32, 27, 21, 39], [33, 2, 21, 39], [33, 6, 21, 39, "_default"], [33, 14, 21, 39], [33, 17, 21, 39, "exports"], [33, 24, 21, 39], [33, 25, 21, 39, "default"], [33, 32, 21, 39], [33, 35, 32, 15, "CustomEvent"], [33, 46, 32, 26], [34, 0, 32, 26], [34, 3]], "functionMap": {"names": ["<global>", "CustomEvent", "constructor"], "mappings": "AAA;ACoB;ECG;GDK;CDC"}}, "type": "js/module"}]}