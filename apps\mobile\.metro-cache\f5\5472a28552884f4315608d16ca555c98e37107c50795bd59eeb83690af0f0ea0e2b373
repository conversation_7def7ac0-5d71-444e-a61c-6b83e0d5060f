{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "./ListMetricsAggregator", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 60}}], "key": "+LsZXRWRdDHi7URH4twavK8owRs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/createClass\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _ListMetricsAggregator = _interopRequireDefault(require(_dependencyMap[3], \"./ListMetricsAggregator\"));\n  var Info = /*#__PURE__*/(0, _createClass2.default)(function Info() {\n    (0, _classCallCheck2.default)(this, Info);\n    this.any_blank_count = 0;\n    this.any_blank_ms = 0;\n    this.any_blank_speed_sum = 0;\n    this.mostly_blank_count = 0;\n    this.mostly_blank_ms = 0;\n    this.pixels_blank = 0;\n    this.pixels_sampled = 0;\n    this.pixels_scrolled = 0;\n    this.total_time_spent = 0;\n    this.sample_count = 0;\n  });\n  var DEBUG = false;\n  var _listeners = [];\n  var _minSampleCount = 10;\n  var _sampleRate = DEBUG ? 1 : null;\n  var FillRateHelper = /*#__PURE__*/function () {\n    function FillRateHelper(listMetrics) {\n      (0, _classCallCheck2.default)(this, FillRateHelper);\n      this._anyBlankStartTime = null;\n      this._enabled = false;\n      this._info = new Info();\n      this._mostlyBlankStartTime = null;\n      this._samplesStartTime = null;\n      this._listMetrics = listMetrics;\n      this._enabled = (_sampleRate || 0) > Math.random();\n      this._resetData();\n    }\n    return (0, _createClass2.default)(FillRateHelper, [{\n      key: \"activate\",\n      value: function activate() {\n        if (this._enabled && this._samplesStartTime == null) {\n          DEBUG && console.debug('FillRateHelper: activate');\n          this._samplesStartTime = global.performance.now();\n        }\n      }\n    }, {\n      key: \"deactivateAndFlush\",\n      value: function deactivateAndFlush() {\n        if (!this._enabled) {\n          return;\n        }\n        var start = this._samplesStartTime;\n        if (start == null) {\n          DEBUG && console.debug('FillRateHelper: bail on deactivate with no start time');\n          return;\n        }\n        if (this._info.sample_count < _minSampleCount) {\n          this._resetData();\n          return;\n        }\n        var total_time_spent = global.performance.now() - start;\n        var info = {\n          ...this._info,\n          total_time_spent\n        };\n        if (DEBUG) {\n          var derived = {\n            avg_blankness: this._info.pixels_blank / this._info.pixels_sampled,\n            avg_speed: this._info.pixels_scrolled / (total_time_spent / 1000),\n            avg_speed_when_any_blank: this._info.any_blank_speed_sum / this._info.any_blank_count,\n            any_blank_per_min: this._info.any_blank_count / (total_time_spent / 1000 / 60),\n            any_blank_time_frac: this._info.any_blank_ms / total_time_spent,\n            mostly_blank_per_min: this._info.mostly_blank_count / (total_time_spent / 1000 / 60),\n            mostly_blank_time_frac: this._info.mostly_blank_ms / total_time_spent\n          };\n          for (var key in derived) {\n            derived[key] = Math.round(1000 * derived[key]) / 1000;\n          }\n          console.debug('FillRateHelper deactivateAndFlush: ', {\n            derived,\n            info\n          });\n        }\n        _listeners.forEach(listener => listener(info));\n        this._resetData();\n      }\n    }, {\n      key: \"computeBlankness\",\n      value: function computeBlankness(props, cellsAroundViewport, scrollMetrics) {\n        if (!this._enabled || props.getItemCount(props.data) === 0 || cellsAroundViewport.last < cellsAroundViewport.first || this._samplesStartTime == null) {\n          return 0;\n        }\n        var dOffset = scrollMetrics.dOffset,\n          offset = scrollMetrics.offset,\n          velocity = scrollMetrics.velocity,\n          visibleLength = scrollMetrics.visibleLength;\n        this._info.sample_count++;\n        this._info.pixels_sampled += Math.round(visibleLength);\n        this._info.pixels_scrolled += Math.round(Math.abs(dOffset));\n        var scrollSpeed = Math.round(Math.abs(velocity) * 1000);\n        var now = global.performance.now();\n        if (this._anyBlankStartTime != null) {\n          this._info.any_blank_ms += now - this._anyBlankStartTime;\n        }\n        this._anyBlankStartTime = null;\n        if (this._mostlyBlankStartTime != null) {\n          this._info.mostly_blank_ms += now - this._mostlyBlankStartTime;\n        }\n        this._mostlyBlankStartTime = null;\n        var blankTop = 0;\n        var first = cellsAroundViewport.first;\n        var firstFrame = this._listMetrics.getCellMetrics(first, props);\n        while (first <= cellsAroundViewport.last && (!firstFrame || !firstFrame.isMounted)) {\n          firstFrame = this._listMetrics.getCellMetrics(first, props);\n          first++;\n        }\n        if (firstFrame && first > 0) {\n          blankTop = Math.min(visibleLength, Math.max(0, firstFrame.offset - offset));\n        }\n        var blankBottom = 0;\n        var last = cellsAroundViewport.last;\n        var lastFrame = this._listMetrics.getCellMetrics(last, props);\n        while (last >= cellsAroundViewport.first && (!lastFrame || !lastFrame.isMounted)) {\n          lastFrame = this._listMetrics.getCellMetrics(last, props);\n          last--;\n        }\n        if (lastFrame && last < props.getItemCount(props.data) - 1) {\n          var bottomEdge = lastFrame.offset + lastFrame.length;\n          blankBottom = Math.min(visibleLength, Math.max(0, offset + visibleLength - bottomEdge));\n        }\n        var pixels_blank = Math.round(blankTop + blankBottom);\n        var blankness = pixels_blank / visibleLength;\n        if (blankness > 0) {\n          this._anyBlankStartTime = now;\n          this._info.any_blank_speed_sum += scrollSpeed;\n          this._info.any_blank_count++;\n          this._info.pixels_blank += pixels_blank;\n          if (blankness > 0.5) {\n            this._mostlyBlankStartTime = now;\n            this._info.mostly_blank_count++;\n          }\n        } else if (scrollSpeed < 0.01 || Math.abs(dOffset) < 1) {\n          this.deactivateAndFlush();\n        }\n        return blankness;\n      }\n    }, {\n      key: \"enabled\",\n      value: function enabled() {\n        return this._enabled;\n      }\n    }, {\n      key: \"_resetData\",\n      value: function _resetData() {\n        this._anyBlankStartTime = null;\n        this._info = new Info();\n        this._mostlyBlankStartTime = null;\n        this._samplesStartTime = null;\n      }\n    }], [{\n      key: \"addListener\",\n      value: function addListener(callback) {\n        if (_sampleRate === null) {\n          console.warn('Call `FillRateHelper.setSampleRate` before `addListener`.');\n        }\n        _listeners.push(callback);\n        return {\n          remove: () => {\n            _listeners = _listeners.filter(listener => callback !== listener);\n          }\n        };\n      }\n    }, {\n      key: \"setSampleRate\",\n      value: function setSampleRate(sampleRate) {\n        _sampleRate = sampleRate;\n      }\n    }, {\n      key: \"setMinSampleCount\",\n      value: function setMinSampleCount(minSampleCount) {\n        _minSampleCount = minSampleCount;\n      }\n    }]);\n  }();\n  var _default = exports.default = FillRateHelper;\n});", "lineCount": 189, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_createClass2"], [9, 19, 11, 13], [9, 22, 11, 13, "_interopRequireDefault"], [9, 44, 11, 13], [9, 45, 11, 13, "require"], [9, 52, 11, 13], [9, 53, 11, 13, "_dependencyMap"], [9, 67, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_classCallCheck2"], [10, 22, 11, 13], [10, 25, 11, 13, "_interopRequireDefault"], [10, 47, 11, 13], [10, 48, 11, 13, "require"], [10, 55, 11, 13], [10, 56, 11, 13, "_dependencyMap"], [10, 70, 11, 13], [11, 2, 15, 0], [11, 6, 15, 0, "_ListMetricsAggregator"], [11, 28, 15, 0], [11, 31, 15, 0, "_interopRequireDefault"], [11, 53, 15, 0], [11, 54, 15, 0, "require"], [11, 61, 15, 0], [11, 62, 15, 0, "_dependencyMap"], [11, 76, 15, 0], [12, 2, 15, 60], [12, 6, 19, 6, "Info"], [12, 10, 19, 10], [12, 30, 19, 10, "_createClass2"], [12, 43, 19, 10], [12, 44, 19, 10, "default"], [12, 51, 19, 10], [12, 62, 19, 10, "Info"], [12, 67, 19, 10], [13, 4, 19, 10], [13, 8, 19, 10, "_classCallCheck2"], [13, 24, 19, 10], [13, 25, 19, 10, "default"], [13, 32, 19, 10], [13, 40, 19, 10, "Info"], [13, 44, 19, 10], [14, 4, 19, 10], [14, 9, 20, 2, "any_blank_count"], [14, 24, 20, 17], [14, 27, 20, 28], [14, 28, 20, 29], [15, 4, 20, 29], [15, 9, 21, 2, "any_blank_ms"], [15, 21, 21, 14], [15, 24, 21, 25], [15, 25, 21, 26], [16, 4, 21, 26], [16, 9, 22, 2, "any_blank_speed_sum"], [16, 28, 22, 21], [16, 31, 22, 32], [16, 32, 22, 33], [17, 4, 22, 33], [17, 9, 23, 2, "mostly_blank_count"], [17, 27, 23, 20], [17, 30, 23, 31], [17, 31, 23, 32], [18, 4, 23, 32], [18, 9, 24, 2, "mostly_blank_ms"], [18, 24, 24, 17], [18, 27, 24, 28], [18, 28, 24, 29], [19, 4, 24, 29], [19, 9, 25, 2, "pixels_blank"], [19, 21, 25, 14], [19, 24, 25, 25], [19, 25, 25, 26], [20, 4, 25, 26], [20, 9, 26, 2, "pixels_sampled"], [20, 23, 26, 16], [20, 26, 26, 27], [20, 27, 26, 28], [21, 4, 26, 28], [21, 9, 27, 2, "pixels_scrolled"], [21, 24, 27, 17], [21, 27, 27, 28], [21, 28, 27, 29], [22, 4, 27, 29], [22, 9, 28, 2, "total_time_spent"], [22, 25, 28, 18], [22, 28, 28, 29], [22, 29, 28, 30], [23, 4, 28, 30], [23, 9, 29, 2, "sample_count"], [23, 21, 29, 14], [23, 24, 29, 25], [23, 25, 29, 26], [24, 2, 29, 26], [25, 2, 32, 0], [25, 6, 32, 6, "DEBUG"], [25, 11, 32, 11], [25, 14, 32, 14], [25, 19, 32, 19], [26, 2, 34, 0], [26, 6, 34, 4, "_listeners"], [26, 16, 34, 37], [26, 19, 34, 40], [26, 21, 34, 42], [27, 2, 35, 0], [27, 6, 35, 4, "_minSampleCount"], [27, 21, 35, 19], [27, 24, 35, 22], [27, 26, 35, 24], [28, 2, 36, 0], [28, 6, 36, 4, "_sampleRate"], [28, 17, 36, 15], [28, 20, 36, 18, "DEBUG"], [28, 25, 36, 23], [28, 28, 36, 26], [28, 29, 36, 27], [28, 32, 36, 30], [28, 36, 36, 34], [29, 2, 36, 35], [29, 6, 46, 6, "FillRateHelper"], [29, 20, 46, 20], [30, 4, 77, 2], [30, 13, 77, 2, "FillRateHelper"], [30, 28, 77, 14, "listMetrics"], [30, 39, 77, 48], [30, 41, 77, 50], [31, 6, 77, 50], [31, 10, 77, 50, "_classCallCheck2"], [31, 26, 77, 50], [31, 27, 77, 50, "default"], [31, 34, 77, 50], [31, 42, 77, 50, "FillRateHelper"], [31, 56, 77, 50], [32, 6, 77, 50], [32, 11, 47, 2, "_anyBlankStartTime"], [32, 29, 47, 20], [32, 32, 47, 32], [32, 36, 47, 36], [33, 6, 47, 36], [33, 11, 48, 2, "_enabled"], [33, 19, 48, 10], [33, 22, 48, 13], [33, 27, 48, 18], [34, 6, 48, 18], [34, 11, 50, 2, "_info"], [34, 16, 50, 7], [34, 19, 50, 16], [34, 23, 50, 20, "Info"], [34, 27, 50, 24], [34, 28, 50, 25], [34, 29, 50, 26], [35, 6, 50, 26], [35, 11, 51, 2, "_mostlyBlankStartTime"], [35, 32, 51, 23], [35, 35, 51, 35], [35, 39, 51, 39], [36, 6, 51, 39], [36, 11, 52, 2, "_samplesStartTime"], [36, 28, 52, 19], [36, 31, 52, 31], [36, 35, 52, 35], [37, 6, 78, 4], [37, 10, 78, 8], [37, 11, 78, 9, "_listMetrics"], [37, 23, 78, 21], [37, 26, 78, 24, "listMetrics"], [37, 37, 78, 35], [38, 6, 79, 4], [38, 10, 79, 8], [38, 11, 79, 9, "_enabled"], [38, 19, 79, 17], [38, 22, 79, 20], [38, 23, 79, 21, "_sampleRate"], [38, 34, 79, 32], [38, 38, 79, 36], [38, 39, 79, 37], [38, 43, 79, 41, "Math"], [38, 47, 79, 45], [38, 48, 79, 46, "random"], [38, 54, 79, 52], [38, 55, 79, 53], [38, 56, 79, 54], [39, 6, 80, 4], [39, 10, 80, 8], [39, 11, 80, 9, "_resetData"], [39, 21, 80, 19], [39, 22, 80, 20], [39, 23, 80, 21], [40, 4, 81, 2], [41, 4, 81, 3], [41, 15, 81, 3, "_createClass2"], [41, 28, 81, 3], [41, 29, 81, 3, "default"], [41, 36, 81, 3], [41, 38, 81, 3, "FillRateHelper"], [41, 52, 81, 3], [42, 6, 81, 3, "key"], [42, 9, 81, 3], [43, 6, 81, 3, "value"], [43, 11, 81, 3], [43, 13, 83, 2], [43, 22, 83, 2, "activate"], [43, 30, 83, 10, "activate"], [43, 31, 83, 10], [43, 33, 83, 13], [44, 8, 84, 4], [44, 12, 84, 8], [44, 16, 84, 12], [44, 17, 84, 13, "_enabled"], [44, 25, 84, 21], [44, 29, 84, 25], [44, 33, 84, 29], [44, 34, 84, 30, "_samplesStartTime"], [44, 51, 84, 47], [44, 55, 84, 51], [44, 59, 84, 55], [44, 61, 84, 57], [45, 10, 85, 6, "DEBUG"], [45, 15, 85, 11], [45, 19, 85, 15, "console"], [45, 26, 85, 22], [45, 27, 85, 23, "debug"], [45, 32, 85, 28], [45, 33, 85, 29], [45, 59, 85, 55], [45, 60, 85, 56], [46, 10, 86, 6], [46, 14, 86, 10], [46, 15, 86, 11, "_samplesStartTime"], [46, 32, 86, 28], [46, 35, 86, 31, "global"], [46, 41, 86, 37], [46, 42, 86, 38, "performance"], [46, 53, 86, 49], [46, 54, 86, 50, "now"], [46, 57, 86, 53], [46, 58, 86, 54], [46, 59, 86, 55], [47, 8, 87, 4], [48, 6, 88, 2], [49, 4, 88, 3], [50, 6, 88, 3, "key"], [50, 9, 88, 3], [51, 6, 88, 3, "value"], [51, 11, 88, 3], [51, 13, 90, 2], [51, 22, 90, 2, "deactivateAndFlush"], [51, 40, 90, 20, "deactivateAndFlush"], [51, 41, 90, 20], [51, 43, 90, 23], [52, 8, 91, 4], [52, 12, 91, 8], [52, 13, 91, 9], [52, 17, 91, 13], [52, 18, 91, 14, "_enabled"], [52, 26, 91, 22], [52, 28, 91, 24], [53, 10, 92, 6], [54, 8, 93, 4], [55, 8, 94, 4], [55, 12, 94, 10, "start"], [55, 17, 94, 15], [55, 20, 94, 18], [55, 24, 94, 22], [55, 25, 94, 23, "_samplesStartTime"], [55, 42, 94, 40], [56, 8, 95, 4], [56, 12, 95, 8, "start"], [56, 17, 95, 13], [56, 21, 95, 17], [56, 25, 95, 21], [56, 27, 95, 23], [57, 10, 96, 6, "DEBUG"], [57, 15, 96, 11], [57, 19, 97, 8, "console"], [57, 26, 97, 15], [57, 27, 97, 16, "debug"], [57, 32, 97, 21], [57, 33, 97, 22], [57, 88, 97, 77], [57, 89, 97, 78], [58, 10, 98, 6], [59, 8, 99, 4], [60, 8, 100, 4], [60, 12, 100, 8], [60, 16, 100, 12], [60, 17, 100, 13, "_info"], [60, 22, 100, 18], [60, 23, 100, 19, "sample_count"], [60, 35, 100, 31], [60, 38, 100, 34, "_minSampleCount"], [60, 53, 100, 49], [60, 55, 100, 51], [61, 10, 102, 6], [61, 14, 102, 10], [61, 15, 102, 11, "_resetData"], [61, 25, 102, 21], [61, 26, 102, 22], [61, 27, 102, 23], [62, 10, 103, 6], [63, 8, 104, 4], [64, 8, 105, 4], [64, 12, 105, 10, "total_time_spent"], [64, 28, 105, 26], [64, 31, 105, 29, "global"], [64, 37, 105, 35], [64, 38, 105, 36, "performance"], [64, 49, 105, 47], [64, 50, 105, 48, "now"], [64, 53, 105, 51], [64, 54, 105, 52], [64, 55, 105, 53], [64, 58, 105, 56, "start"], [64, 63, 105, 61], [65, 8, 106, 4], [65, 12, 106, 10, "info"], [65, 16, 106, 19], [65, 19, 106, 22], [66, 10, 107, 6], [66, 13, 107, 9], [66, 17, 107, 13], [66, 18, 107, 14, "_info"], [66, 23, 107, 19], [67, 10, 108, 6, "total_time_spent"], [68, 8, 109, 4], [68, 9, 109, 5], [69, 8, 110, 4], [69, 12, 110, 8, "DEBUG"], [69, 17, 110, 13], [69, 19, 110, 15], [70, 10, 111, 6], [70, 14, 111, 12, "derived"], [70, 21, 111, 19], [70, 24, 111, 22], [71, 12, 112, 8, "avg_blankness"], [71, 25, 112, 21], [71, 27, 112, 23], [71, 31, 112, 27], [71, 32, 112, 28, "_info"], [71, 37, 112, 33], [71, 38, 112, 34, "pixels_blank"], [71, 50, 112, 46], [71, 53, 112, 49], [71, 57, 112, 53], [71, 58, 112, 54, "_info"], [71, 63, 112, 59], [71, 64, 112, 60, "pixels_sampled"], [71, 78, 112, 74], [72, 12, 113, 8, "avg_speed"], [72, 21, 113, 17], [72, 23, 113, 19], [72, 27, 113, 23], [72, 28, 113, 24, "_info"], [72, 33, 113, 29], [72, 34, 113, 30, "pixels_scrolled"], [72, 49, 113, 45], [72, 53, 113, 49, "total_time_spent"], [72, 69, 113, 65], [72, 72, 113, 68], [72, 76, 113, 72], [72, 77, 113, 73], [73, 12, 114, 8, "avg_speed_when_any_blank"], [73, 36, 114, 32], [73, 38, 115, 10], [73, 42, 115, 14], [73, 43, 115, 15, "_info"], [73, 48, 115, 20], [73, 49, 115, 21, "any_blank_speed_sum"], [73, 68, 115, 40], [73, 71, 115, 43], [73, 75, 115, 47], [73, 76, 115, 48, "_info"], [73, 81, 115, 53], [73, 82, 115, 54, "any_blank_count"], [73, 97, 115, 69], [74, 12, 116, 8, "any_blank_per_min"], [74, 29, 116, 25], [74, 31, 117, 10], [74, 35, 117, 14], [74, 36, 117, 15, "_info"], [74, 41, 117, 20], [74, 42, 117, 21, "any_blank_count"], [74, 57, 117, 36], [74, 61, 117, 40, "total_time_spent"], [74, 77, 117, 56], [74, 80, 117, 59], [74, 84, 117, 63], [74, 87, 117, 66], [74, 89, 117, 68], [74, 90, 117, 69], [75, 12, 118, 8, "any_blank_time_frac"], [75, 31, 118, 27], [75, 33, 118, 29], [75, 37, 118, 33], [75, 38, 118, 34, "_info"], [75, 43, 118, 39], [75, 44, 118, 40, "any_blank_ms"], [75, 56, 118, 52], [75, 59, 118, 55, "total_time_spent"], [75, 75, 118, 71], [76, 12, 119, 8, "mostly_blank_per_min"], [76, 32, 119, 28], [76, 34, 120, 10], [76, 38, 120, 14], [76, 39, 120, 15, "_info"], [76, 44, 120, 20], [76, 45, 120, 21, "mostly_blank_count"], [76, 63, 120, 39], [76, 67, 120, 43, "total_time_spent"], [76, 83, 120, 59], [76, 86, 120, 62], [76, 90, 120, 66], [76, 93, 120, 69], [76, 95, 120, 71], [76, 96, 120, 72], [77, 12, 121, 8, "mostly_blank_time_frac"], [77, 34, 121, 30], [77, 36, 121, 32], [77, 40, 121, 36], [77, 41, 121, 37, "_info"], [77, 46, 121, 42], [77, 47, 121, 43, "mostly_blank_ms"], [77, 62, 121, 58], [77, 65, 121, 61, "total_time_spent"], [78, 10, 122, 6], [78, 11, 122, 7], [79, 10, 123, 6], [79, 15, 123, 11], [79, 19, 123, 17, "key"], [79, 22, 123, 20], [79, 26, 123, 24, "derived"], [79, 33, 123, 31], [79, 35, 123, 33], [80, 12, 126, 8, "derived"], [80, 19, 126, 15], [80, 20, 126, 16, "key"], [80, 23, 126, 19], [80, 24, 126, 20], [80, 27, 126, 23, "Math"], [80, 31, 126, 27], [80, 32, 126, 28, "round"], [80, 37, 126, 33], [80, 38, 126, 34], [80, 42, 126, 38], [80, 45, 126, 41, "derived"], [80, 52, 126, 48], [80, 53, 126, 49, "key"], [80, 56, 126, 52], [80, 57, 126, 53], [80, 58, 126, 54], [80, 61, 126, 57], [80, 65, 126, 61], [81, 10, 127, 6], [82, 10, 128, 6, "console"], [82, 17, 128, 13], [82, 18, 128, 14, "debug"], [82, 23, 128, 19], [82, 24, 128, 20], [82, 61, 128, 57], [82, 63, 128, 59], [83, 12, 128, 60, "derived"], [83, 19, 128, 67], [84, 12, 128, 69, "info"], [85, 10, 128, 73], [85, 11, 128, 74], [85, 12, 128, 75], [86, 8, 129, 4], [87, 8, 130, 4, "_listeners"], [87, 18, 130, 14], [87, 19, 130, 15, "for<PERSON>ach"], [87, 26, 130, 22], [87, 27, 130, 23, "listener"], [87, 35, 130, 31], [87, 39, 130, 35, "listener"], [87, 47, 130, 43], [87, 48, 130, 44, "info"], [87, 52, 130, 48], [87, 53, 130, 49], [87, 54, 130, 50], [88, 8, 131, 4], [88, 12, 131, 8], [88, 13, 131, 9, "_resetData"], [88, 23, 131, 19], [88, 24, 131, 20], [88, 25, 131, 21], [89, 6, 132, 2], [90, 4, 132, 3], [91, 6, 132, 3, "key"], [91, 9, 132, 3], [92, 6, 132, 3, "value"], [92, 11, 132, 3], [92, 13, 134, 2], [92, 22, 134, 2, "computeBlankness"], [92, 38, 134, 18, "computeBlankness"], [92, 39, 135, 4, "props"], [92, 44, 139, 5], [92, 46, 140, 4, "cellsAroundViewport"], [92, 65, 144, 5], [92, 67, 145, 4, "scrollMetrics"], [92, 80, 151, 5], [92, 82, 152, 12], [93, 8, 153, 4], [93, 12, 154, 6], [93, 13, 154, 7], [93, 17, 154, 11], [93, 18, 154, 12, "_enabled"], [93, 26, 154, 20], [93, 30, 155, 6, "props"], [93, 35, 155, 11], [93, 36, 155, 12, "getItemCount"], [93, 48, 155, 24], [93, 49, 155, 25, "props"], [93, 54, 155, 30], [93, 55, 155, 31, "data"], [93, 59, 155, 35], [93, 60, 155, 36], [93, 65, 155, 41], [93, 66, 155, 42], [93, 70, 156, 6, "cellsAroundViewport"], [93, 89, 156, 25], [93, 90, 156, 26, "last"], [93, 94, 156, 30], [93, 97, 156, 33, "cellsAroundViewport"], [93, 116, 156, 52], [93, 117, 156, 53, "first"], [93, 122, 156, 58], [93, 126, 157, 6], [93, 130, 157, 10], [93, 131, 157, 11, "_samplesStartTime"], [93, 148, 157, 28], [93, 152, 157, 32], [93, 156, 157, 36], [93, 158, 158, 6], [94, 10, 159, 6], [94, 17, 159, 13], [94, 18, 159, 14], [95, 8, 160, 4], [96, 8, 161, 4], [96, 12, 161, 11, "dOffset"], [96, 19, 161, 18], [96, 22, 161, 55, "scrollMetrics"], [96, 35, 161, 68], [96, 36, 161, 11, "dOffset"], [96, 43, 161, 18], [97, 10, 161, 20, "offset"], [97, 16, 161, 26], [97, 19, 161, 55, "scrollMetrics"], [97, 32, 161, 68], [97, 33, 161, 20, "offset"], [97, 39, 161, 26], [98, 10, 161, 28, "velocity"], [98, 18, 161, 36], [98, 21, 161, 55, "scrollMetrics"], [98, 34, 161, 68], [98, 35, 161, 28, "velocity"], [98, 43, 161, 36], [99, 10, 161, 38, "<PERSON><PERSON><PERSON><PERSON>"], [99, 23, 161, 51], [99, 26, 161, 55, "scrollMetrics"], [99, 39, 161, 68], [99, 40, 161, 38, "<PERSON><PERSON><PERSON><PERSON>"], [99, 53, 161, 51], [100, 8, 165, 4], [100, 12, 165, 8], [100, 13, 165, 9, "_info"], [100, 18, 165, 14], [100, 19, 165, 15, "sample_count"], [100, 31, 165, 27], [100, 33, 165, 29], [101, 8, 166, 4], [101, 12, 166, 8], [101, 13, 166, 9, "_info"], [101, 18, 166, 14], [101, 19, 166, 15, "pixels_sampled"], [101, 33, 166, 29], [101, 37, 166, 33, "Math"], [101, 41, 166, 37], [101, 42, 166, 38, "round"], [101, 47, 166, 43], [101, 48, 166, 44, "<PERSON><PERSON><PERSON><PERSON>"], [101, 61, 166, 57], [101, 62, 166, 58], [102, 8, 167, 4], [102, 12, 167, 8], [102, 13, 167, 9, "_info"], [102, 18, 167, 14], [102, 19, 167, 15, "pixels_scrolled"], [102, 34, 167, 30], [102, 38, 167, 34, "Math"], [102, 42, 167, 38], [102, 43, 167, 39, "round"], [102, 48, 167, 44], [102, 49, 167, 45, "Math"], [102, 53, 167, 49], [102, 54, 167, 50, "abs"], [102, 57, 167, 53], [102, 58, 167, 54, "dOffset"], [102, 65, 167, 61], [102, 66, 167, 62], [102, 67, 167, 63], [103, 8, 168, 4], [103, 12, 168, 10, "scrollSpeed"], [103, 23, 168, 21], [103, 26, 168, 24, "Math"], [103, 30, 168, 28], [103, 31, 168, 29, "round"], [103, 36, 168, 34], [103, 37, 168, 35, "Math"], [103, 41, 168, 39], [103, 42, 168, 40, "abs"], [103, 45, 168, 43], [103, 46, 168, 44, "velocity"], [103, 54, 168, 52], [103, 55, 168, 53], [103, 58, 168, 56], [103, 62, 168, 60], [103, 63, 168, 61], [104, 8, 171, 4], [104, 12, 171, 10, "now"], [104, 15, 171, 13], [104, 18, 171, 16, "global"], [104, 24, 171, 22], [104, 25, 171, 23, "performance"], [104, 36, 171, 34], [104, 37, 171, 35, "now"], [104, 40, 171, 38], [104, 41, 171, 39], [104, 42, 171, 40], [105, 8, 172, 4], [105, 12, 172, 8], [105, 16, 172, 12], [105, 17, 172, 13, "_anyBlankStartTime"], [105, 35, 172, 31], [105, 39, 172, 35], [105, 43, 172, 39], [105, 45, 172, 41], [106, 10, 173, 6], [106, 14, 173, 10], [106, 15, 173, 11, "_info"], [106, 20, 173, 16], [106, 21, 173, 17, "any_blank_ms"], [106, 33, 173, 29], [106, 37, 173, 33, "now"], [106, 40, 173, 36], [106, 43, 173, 39], [106, 47, 173, 43], [106, 48, 173, 44, "_anyBlankStartTime"], [106, 66, 173, 62], [107, 8, 174, 4], [108, 8, 175, 4], [108, 12, 175, 8], [108, 13, 175, 9, "_anyBlankStartTime"], [108, 31, 175, 27], [108, 34, 175, 30], [108, 38, 175, 34], [109, 8, 176, 4], [109, 12, 176, 8], [109, 16, 176, 12], [109, 17, 176, 13, "_mostlyBlankStartTime"], [109, 38, 176, 34], [109, 42, 176, 38], [109, 46, 176, 42], [109, 48, 176, 44], [110, 10, 177, 6], [110, 14, 177, 10], [110, 15, 177, 11, "_info"], [110, 20, 177, 16], [110, 21, 177, 17, "mostly_blank_ms"], [110, 36, 177, 32], [110, 40, 177, 36, "now"], [110, 43, 177, 39], [110, 46, 177, 42], [110, 50, 177, 46], [110, 51, 177, 47, "_mostlyBlankStartTime"], [110, 72, 177, 68], [111, 8, 178, 4], [112, 8, 179, 4], [112, 12, 179, 8], [112, 13, 179, 9, "_mostlyBlankStartTime"], [112, 34, 179, 30], [112, 37, 179, 33], [112, 41, 179, 37], [113, 8, 181, 4], [113, 12, 181, 8, "blankTop"], [113, 20, 181, 16], [113, 23, 181, 19], [113, 24, 181, 20], [114, 8, 182, 4], [114, 12, 182, 8, "first"], [114, 17, 182, 13], [114, 20, 182, 16, "cellsAroundViewport"], [114, 39, 182, 35], [114, 40, 182, 36, "first"], [114, 45, 182, 41], [115, 8, 183, 4], [115, 12, 183, 8, "firstFrame"], [115, 22, 183, 18], [115, 25, 183, 21], [115, 29, 183, 25], [115, 30, 183, 26, "_listMetrics"], [115, 42, 183, 38], [115, 43, 183, 39, "getCellMetrics"], [115, 57, 183, 53], [115, 58, 183, 54, "first"], [115, 63, 183, 59], [115, 65, 183, 61, "props"], [115, 70, 183, 66], [115, 71, 183, 67], [116, 8, 184, 4], [116, 15, 185, 6, "first"], [116, 20, 185, 11], [116, 24, 185, 15, "cellsAroundViewport"], [116, 43, 185, 34], [116, 44, 185, 35, "last"], [116, 48, 185, 39], [116, 53, 186, 7], [116, 54, 186, 8, "firstFrame"], [116, 64, 186, 18], [116, 68, 186, 22], [116, 69, 186, 23, "firstFrame"], [116, 79, 186, 33], [116, 80, 186, 34, "isMounted"], [116, 89, 186, 43], [116, 90, 186, 44], [116, 92, 187, 6], [117, 10, 188, 6, "firstFrame"], [117, 20, 188, 16], [117, 23, 188, 19], [117, 27, 188, 23], [117, 28, 188, 24, "_listMetrics"], [117, 40, 188, 36], [117, 41, 188, 37, "getCellMetrics"], [117, 55, 188, 51], [117, 56, 188, 52, "first"], [117, 61, 188, 57], [117, 63, 188, 59, "props"], [117, 68, 188, 64], [117, 69, 188, 65], [118, 10, 189, 6, "first"], [118, 15, 189, 11], [118, 17, 189, 13], [119, 8, 190, 4], [120, 8, 193, 4], [120, 12, 193, 8, "firstFrame"], [120, 22, 193, 18], [120, 26, 193, 22, "first"], [120, 31, 193, 27], [120, 34, 193, 30], [120, 35, 193, 31], [120, 37, 193, 33], [121, 10, 194, 6, "blankTop"], [121, 18, 194, 14], [121, 21, 194, 17, "Math"], [121, 25, 194, 21], [121, 26, 194, 22, "min"], [121, 29, 194, 25], [121, 30, 195, 8, "<PERSON><PERSON><PERSON><PERSON>"], [121, 43, 195, 21], [121, 45, 196, 8, "Math"], [121, 49, 196, 12], [121, 50, 196, 13, "max"], [121, 53, 196, 16], [121, 54, 196, 17], [121, 55, 196, 18], [121, 57, 196, 20, "firstFrame"], [121, 67, 196, 30], [121, 68, 196, 31, "offset"], [121, 74, 196, 37], [121, 77, 196, 40, "offset"], [121, 83, 196, 46], [121, 84, 197, 6], [121, 85, 197, 7], [122, 8, 198, 4], [123, 8, 199, 4], [123, 12, 199, 8, "blankBottom"], [123, 23, 199, 19], [123, 26, 199, 22], [123, 27, 199, 23], [124, 8, 200, 4], [124, 12, 200, 8, "last"], [124, 16, 200, 12], [124, 19, 200, 15, "cellsAroundViewport"], [124, 38, 200, 34], [124, 39, 200, 35, "last"], [124, 43, 200, 39], [125, 8, 201, 4], [125, 12, 201, 8, "<PERSON><PERSON><PERSON><PERSON>"], [125, 21, 201, 17], [125, 24, 201, 20], [125, 28, 201, 24], [125, 29, 201, 25, "_listMetrics"], [125, 41, 201, 37], [125, 42, 201, 38, "getCellMetrics"], [125, 56, 201, 52], [125, 57, 201, 53, "last"], [125, 61, 201, 57], [125, 63, 201, 59, "props"], [125, 68, 201, 64], [125, 69, 201, 65], [126, 8, 202, 4], [126, 15, 203, 6, "last"], [126, 19, 203, 10], [126, 23, 203, 14, "cellsAroundViewport"], [126, 42, 203, 33], [126, 43, 203, 34, "first"], [126, 48, 203, 39], [126, 53, 204, 7], [126, 54, 204, 8, "<PERSON><PERSON><PERSON><PERSON>"], [126, 63, 204, 17], [126, 67, 204, 21], [126, 68, 204, 22, "<PERSON><PERSON><PERSON><PERSON>"], [126, 77, 204, 31], [126, 78, 204, 32, "isMounted"], [126, 87, 204, 41], [126, 88, 204, 42], [126, 90, 205, 6], [127, 10, 206, 6, "<PERSON><PERSON><PERSON><PERSON>"], [127, 19, 206, 15], [127, 22, 206, 18], [127, 26, 206, 22], [127, 27, 206, 23, "_listMetrics"], [127, 39, 206, 35], [127, 40, 206, 36, "getCellMetrics"], [127, 54, 206, 50], [127, 55, 206, 51, "last"], [127, 59, 206, 55], [127, 61, 206, 57, "props"], [127, 66, 206, 62], [127, 67, 206, 63], [128, 10, 207, 6, "last"], [128, 14, 207, 10], [128, 16, 207, 12], [129, 8, 208, 4], [130, 8, 211, 4], [130, 12, 211, 8, "<PERSON><PERSON><PERSON><PERSON>"], [130, 21, 211, 17], [130, 25, 211, 21, "last"], [130, 29, 211, 25], [130, 32, 211, 28, "props"], [130, 37, 211, 33], [130, 38, 211, 34, "getItemCount"], [130, 50, 211, 46], [130, 51, 211, 47, "props"], [130, 56, 211, 52], [130, 57, 211, 53, "data"], [130, 61, 211, 57], [130, 62, 211, 58], [130, 65, 211, 61], [130, 66, 211, 62], [130, 68, 211, 64], [131, 10, 212, 6], [131, 14, 212, 12, "bottomEdge"], [131, 24, 212, 22], [131, 27, 212, 25, "<PERSON><PERSON><PERSON><PERSON>"], [131, 36, 212, 34], [131, 37, 212, 35, "offset"], [131, 43, 212, 41], [131, 46, 212, 44, "<PERSON><PERSON><PERSON><PERSON>"], [131, 55, 212, 53], [131, 56, 212, 54, "length"], [131, 62, 212, 60], [132, 10, 213, 6, "blankBottom"], [132, 21, 213, 17], [132, 24, 213, 20, "Math"], [132, 28, 213, 24], [132, 29, 213, 25, "min"], [132, 32, 213, 28], [132, 33, 214, 8, "<PERSON><PERSON><PERSON><PERSON>"], [132, 46, 214, 21], [132, 48, 215, 8, "Math"], [132, 52, 215, 12], [132, 53, 215, 13, "max"], [132, 56, 215, 16], [132, 57, 215, 17], [132, 58, 215, 18], [132, 60, 215, 20, "offset"], [132, 66, 215, 26], [132, 69, 215, 29, "<PERSON><PERSON><PERSON><PERSON>"], [132, 82, 215, 42], [132, 85, 215, 45, "bottomEdge"], [132, 95, 215, 55], [132, 96, 216, 6], [132, 97, 216, 7], [133, 8, 217, 4], [134, 8, 218, 4], [134, 12, 218, 10, "pixels_blank"], [134, 24, 218, 22], [134, 27, 218, 25, "Math"], [134, 31, 218, 29], [134, 32, 218, 30, "round"], [134, 37, 218, 35], [134, 38, 218, 36, "blankTop"], [134, 46, 218, 44], [134, 49, 218, 47, "blankBottom"], [134, 60, 218, 58], [134, 61, 218, 59], [135, 8, 219, 4], [135, 12, 219, 10, "blankness"], [135, 21, 219, 19], [135, 24, 219, 22, "pixels_blank"], [135, 36, 219, 34], [135, 39, 219, 37, "<PERSON><PERSON><PERSON><PERSON>"], [135, 52, 219, 50], [136, 8, 220, 4], [136, 12, 220, 8, "blankness"], [136, 21, 220, 17], [136, 24, 220, 20], [136, 25, 220, 21], [136, 27, 220, 23], [137, 10, 221, 6], [137, 14, 221, 10], [137, 15, 221, 11, "_anyBlankStartTime"], [137, 33, 221, 29], [137, 36, 221, 32, "now"], [137, 39, 221, 35], [138, 10, 222, 6], [138, 14, 222, 10], [138, 15, 222, 11, "_info"], [138, 20, 222, 16], [138, 21, 222, 17, "any_blank_speed_sum"], [138, 40, 222, 36], [138, 44, 222, 40, "scrollSpeed"], [138, 55, 222, 51], [139, 10, 223, 6], [139, 14, 223, 10], [139, 15, 223, 11, "_info"], [139, 20, 223, 16], [139, 21, 223, 17, "any_blank_count"], [139, 36, 223, 32], [139, 38, 223, 34], [140, 10, 224, 6], [140, 14, 224, 10], [140, 15, 224, 11, "_info"], [140, 20, 224, 16], [140, 21, 224, 17, "pixels_blank"], [140, 33, 224, 29], [140, 37, 224, 33, "pixels_blank"], [140, 49, 224, 45], [141, 10, 225, 6], [141, 14, 225, 10, "blankness"], [141, 23, 225, 19], [141, 26, 225, 22], [141, 29, 225, 25], [141, 31, 225, 27], [142, 12, 226, 8], [142, 16, 226, 12], [142, 17, 226, 13, "_mostlyBlankStartTime"], [142, 38, 226, 34], [142, 41, 226, 37, "now"], [142, 44, 226, 40], [143, 12, 227, 8], [143, 16, 227, 12], [143, 17, 227, 13, "_info"], [143, 22, 227, 18], [143, 23, 227, 19, "mostly_blank_count"], [143, 41, 227, 37], [143, 43, 227, 39], [144, 10, 228, 6], [145, 8, 229, 4], [145, 9, 229, 5], [145, 15, 229, 11], [145, 19, 229, 15, "scrollSpeed"], [145, 30, 229, 26], [145, 33, 229, 29], [145, 37, 229, 33], [145, 41, 229, 37, "Math"], [145, 45, 229, 41], [145, 46, 229, 42, "abs"], [145, 49, 229, 45], [145, 50, 229, 46, "dOffset"], [145, 57, 229, 53], [145, 58, 229, 54], [145, 61, 229, 57], [145, 62, 229, 58], [145, 64, 229, 60], [146, 10, 230, 6], [146, 14, 230, 10], [146, 15, 230, 11, "deactivateAndFlush"], [146, 33, 230, 29], [146, 34, 230, 30], [146, 35, 230, 31], [147, 8, 231, 4], [148, 8, 232, 4], [148, 15, 232, 11, "blankness"], [148, 24, 232, 20], [149, 6, 233, 2], [150, 4, 233, 3], [151, 6, 233, 3, "key"], [151, 9, 233, 3], [152, 6, 233, 3, "value"], [152, 11, 233, 3], [152, 13, 235, 2], [152, 22, 235, 2, "enabled"], [152, 29, 235, 9, "enabled"], [152, 30, 235, 9], [152, 32, 235, 21], [153, 8, 236, 4], [153, 15, 236, 11], [153, 19, 236, 15], [153, 20, 236, 16, "_enabled"], [153, 28, 236, 24], [154, 6, 237, 2], [155, 4, 237, 3], [156, 6, 237, 3, "key"], [156, 9, 237, 3], [157, 6, 237, 3, "value"], [157, 11, 237, 3], [157, 13, 239, 2], [157, 22, 239, 2, "_resetData"], [157, 32, 239, 12, "_resetData"], [157, 33, 239, 12], [157, 35, 239, 15], [158, 8, 240, 4], [158, 12, 240, 8], [158, 13, 240, 9, "_anyBlankStartTime"], [158, 31, 240, 27], [158, 34, 240, 30], [158, 38, 240, 34], [159, 8, 241, 4], [159, 12, 241, 8], [159, 13, 241, 9, "_info"], [159, 18, 241, 14], [159, 21, 241, 17], [159, 25, 241, 21, "Info"], [159, 29, 241, 25], [159, 30, 241, 26], [159, 31, 241, 27], [160, 8, 242, 4], [160, 12, 242, 8], [160, 13, 242, 9, "_mostlyBlankStartTime"], [160, 34, 242, 30], [160, 37, 242, 33], [160, 41, 242, 37], [161, 8, 243, 4], [161, 12, 243, 8], [161, 13, 243, 9, "_samplesStartTime"], [161, 30, 243, 26], [161, 33, 243, 29], [161, 37, 243, 33], [162, 6, 244, 2], [163, 4, 244, 3], [164, 6, 244, 3, "key"], [164, 9, 244, 3], [165, 6, 244, 3, "value"], [165, 11, 244, 3], [165, 13, 54, 2], [165, 22, 54, 9, "addListener"], [165, 33, 54, 20, "addListener"], [165, 34, 54, 21, "callback"], [165, 42, 54, 51], [165, 44, 57, 4], [166, 8, 58, 4], [166, 12, 58, 8, "_sampleRate"], [166, 23, 58, 19], [166, 28, 58, 24], [166, 32, 58, 28], [166, 34, 58, 30], [167, 10, 59, 6, "console"], [167, 17, 59, 13], [167, 18, 59, 14, "warn"], [167, 22, 59, 18], [167, 23, 59, 19], [167, 82, 59, 78], [167, 83, 59, 79], [168, 8, 60, 4], [169, 8, 61, 4, "_listeners"], [169, 18, 61, 14], [169, 19, 61, 15, "push"], [169, 23, 61, 19], [169, 24, 61, 20, "callback"], [169, 32, 61, 28], [169, 33, 61, 29], [170, 8, 62, 4], [170, 15, 62, 11], [171, 10, 63, 6, "remove"], [171, 16, 63, 12], [171, 18, 63, 14, "remove"], [171, 19, 63, 14], [171, 24, 63, 20], [172, 12, 64, 8, "_listeners"], [172, 22, 64, 18], [172, 25, 64, 21, "_listeners"], [172, 35, 64, 31], [172, 36, 64, 32, "filter"], [172, 42, 64, 38], [172, 43, 64, 39, "listener"], [172, 51, 64, 47], [172, 55, 64, 51, "callback"], [172, 63, 64, 59], [172, 68, 64, 64, "listener"], [172, 76, 64, 72], [172, 77, 64, 73], [173, 10, 65, 6], [174, 8, 66, 4], [174, 9, 66, 5], [175, 6, 67, 2], [176, 4, 67, 3], [177, 6, 67, 3, "key"], [177, 9, 67, 3], [178, 6, 67, 3, "value"], [178, 11, 67, 3], [178, 13, 69, 2], [178, 22, 69, 9, "setSampleRate"], [178, 35, 69, 22, "setSampleRate"], [178, 36, 69, 23, "sampleRate"], [178, 46, 69, 41], [178, 48, 69, 43], [179, 8, 70, 4, "_sampleRate"], [179, 19, 70, 15], [179, 22, 70, 18, "sampleRate"], [179, 32, 70, 28], [180, 6, 71, 2], [181, 4, 71, 3], [182, 6, 71, 3, "key"], [182, 9, 71, 3], [183, 6, 71, 3, "value"], [183, 11, 71, 3], [183, 13, 73, 2], [183, 22, 73, 9, "setMinSampleCount"], [183, 39, 73, 26, "setMinSampleCount"], [183, 40, 73, 27, "minSampleCount"], [183, 54, 73, 49], [183, 56, 73, 51], [184, 8, 74, 4, "_minSampleCount"], [184, 23, 74, 19], [184, 26, 74, 22, "minSampleCount"], [184, 40, 74, 36], [185, 6, 75, 2], [186, 4, 75, 3], [187, 2, 75, 3], [188, 2, 75, 3], [188, 6, 75, 3, "_default"], [188, 14, 75, 3], [188, 17, 75, 3, "exports"], [188, 24, 75, 3], [188, 25, 75, 3, "default"], [188, 32, 75, 3], [188, 35, 247, 15, "FillRateHelper"], [188, 49, 247, 29], [189, 0, 247, 29], [189, 3]], "functionMap": {"names": ["<global>", "Info", "FillRateHelper", "addListener", "remove", "_listeners.filter$argument_0", "setSampleRate", "setMinSampleCount", "constructor", "activate", "deactivateAndFlush", "_listeners.forEach$argument_0", "computeBlankness", "enabled", "_resetData"], "mappings": "AAA;ACkB;CDW;AEgB;ECQ;cCS;uCCC,iCD;ODC;GDE;EIE;GJE;EKE;GLE;EME;GNI;EOE;GPK;EQE;uBCwC,0BD;GRE;EUE;GVmG;EWE;GXE;EYE;GZK;CFC"}}, "type": "js/module"}]}