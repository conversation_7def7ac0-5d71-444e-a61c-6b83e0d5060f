{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../Core/Devtools/getDevServer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 57}}], "key": "D2dUkTmlpJpD00plKbWupMRoK8Y=", "exportNames": ["*"]}}, {"name": "../LogBox/LogBox", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 38}}], "key": "cHTNqe+nKOCmxfE+25FBR4MZxGc=", "exportNames": ["*"]}}, {"name": "../NativeModules/specs/NativeRedBox", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 63}}], "key": "oMZcmEAYLZpvh2Rq/1cim7vVinE=", "exportNames": ["*"]}}, {"name": "./DevSettings", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 20}, "end": {"line": 17, "column": 44}}], "key": "W7J879sUreeTtz/gmMORntiZ6Nc=", "exportNames": ["*"]}}, {"name": "./Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 17}, "end": {"line": 18, "column": 38}}], "key": "KMdNMOjL2UR7UgOfvU1uDXsNqTU=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 18}, "end": {"line": 19, "column": 38}}], "key": "oQpL0Es3H146KnQH9ygFeHrzVP4=", "exportNames": ["*"]}}, {"name": "metro-runtime/src/modules/HMRClient", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 20, "column": 23}, "end": {"line": 20, "column": 69}}], "key": "7qn5CJ9sE1FJeWCZmlYKVwEkkqk=", "exportNames": ["*"]}}, {"name": "pretty-format", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 21}, "end": {"line": 21, "column": 45}}], "key": "olWyfE7OhSnDcSpkF2r8idaXSNU=", "exportNames": ["*"]}}, {"name": "./DevLoadingView", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 71, "column": 27}, "end": {"line": 71, "column": 54}}, {"start": {"line": 159, "column": 27}, "end": {"line": 159, "column": 54}}], "key": "4gPLe4J8V9oplo3L2tl7ZwxSZLg=", "exportNames": ["*"]}}, {"name": "../Core/NativeExceptionsManager", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 333, "column": 6}, "end": {"line": 333, "column": 48}}], "key": "5IPHk3lQjAUZ4d6HzCiC1IIvnzc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _getDevServer2 = _interopRequireDefault(require(_dependencyMap[2], \"../Core/Devtools/getDevServer\"));\n  var _LogBox = _interopRequireDefault(require(_dependencyMap[3], \"../LogBox/LogBox\"));\n  var _NativeRedBox = _interopRequireDefault(require(_dependencyMap[4], \"../NativeModules/specs/NativeRedBox\"));\n  var DevSettings = require(_dependencyMap[5], \"./DevSettings\").default;\n  var Platform = require(_dependencyMap[6], \"./Platform\").default;\n  var invariant = require(_dependencyMap[7], \"invariant\");\n  var MetroHMRClient = require(_dependencyMap[8], \"metro-runtime/src/modules/HMRClient\");\n  var prettyFormat = require(_dependencyMap[9], \"pretty-format\");\n  var pendingEntryPoints = [];\n  var hmrClient = null;\n  var hmrUnavailableReason = null;\n  var currentCompileErrorMessage = null;\n  var didConnect = false;\n  var pendingLogs = [];\n  var HMRClient = {\n    enable() {\n      if (hmrUnavailableReason !== null) {\n        throw new Error(hmrUnavailableReason);\n      }\n      invariant(hmrClient, 'Expected HMRClient.setup() call at startup.');\n      var DevLoadingView = require(_dependencyMap[10], \"./DevLoadingView\").default;\n      hmrClient.send(JSON.stringify({\n        type: 'log-opt-in'\n      }));\n      var hasUpdates = hmrClient.hasPendingUpdates();\n      if (hasUpdates) {\n        DevLoadingView.showMessage('Refreshing...', 'refresh');\n      }\n      try {\n        hmrClient.enable();\n      } finally {\n        if (hasUpdates) {\n          DevLoadingView.hide();\n        }\n      }\n      showCompileError();\n    },\n    disable() {\n      invariant(hmrClient, 'Expected HMRClient.setup() call at startup.');\n      hmrClient.disable();\n    },\n    registerBundle(requestUrl) {\n      invariant(hmrClient, 'Expected HMRClient.setup() call at startup.');\n      pendingEntryPoints.push(requestUrl);\n      registerBundleEntryPoints(hmrClient);\n    },\n    log(level, data) {\n      if (!hmrClient) {\n        pendingLogs.push([level, data]);\n        if (pendingLogs.length > 100) {\n          pendingLogs.shift();\n        }\n        return;\n      }\n      try {\n        hmrClient.send(JSON.stringify({\n          type: 'log',\n          level,\n          mode: global.RN$Bridgeless === true ? 'NOBRIDGE' : 'BRIDGE',\n          data: data.map(item => typeof item === 'string' ? item : prettyFormat.format(item, {\n            escapeString: true,\n            highlight: true,\n            maxDepth: 3,\n            min: true,\n            plugins: [prettyFormat.plugins.ReactElement]\n          }))\n        }));\n      } catch (error) {}\n    },\n    setup(platform, bundleEntry, host, port, isEnabled) {\n      var scheme = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 'http';\n      invariant(platform, 'Missing required parameter `platform`');\n      invariant(bundleEntry, 'Missing required parameter `bundleEntry`');\n      invariant(host, 'Missing required parameter `host`');\n      invariant(!hmrClient, 'Cannot initialize hmrClient twice');\n      var DevLoadingView = require(_dependencyMap[10], \"./DevLoadingView\").default;\n      var serverHost = port !== null && port !== '' ? `${host}:${port}` : host;\n      var serverScheme = scheme;\n      var client = new MetroHMRClient(`${serverScheme}://${serverHost}/hot`);\n      hmrClient = client;\n      var _getDevServer = (0, _getDevServer2.default)(),\n        fullBundleUrl = _getDevServer.fullBundleUrl;\n      pendingEntryPoints.push(fullBundleUrl ?? `${serverScheme}://${serverHost}/hot?bundleEntry=${bundleEntry}&platform=${platform}`);\n      client.on('connection-error', e => {\n        var error = `Cannot connect to Metro.\n\nTry the following to fix the issue:\n- Ensure that Metro is running and available on the same network`;\n        if (Platform.OS === 'ios') {\n          error += `\n- Ensure that the Metro URL is correctly set in AppDelegate`;\n        } else {\n          error += `\n- Ensure that your device/emulator is connected to your machine and has USB debugging enabled - run 'adb devices' to see a list of connected devices\n- If you're on a physical device connected to the same machine, run 'adb reverse tcp:8081 tcp:8081' to forward requests from your device\n- If your device is on the same Wi-Fi network, set 'Debug server host & port for device' in 'Dev settings' to your machine's IP address and the port of the local dev server - e.g. ********:8081`;\n        }\n        error += `\n\nURL: ${host}:${port}\n\nError: ${e.message}`;\n        setHMRUnavailableReason(error);\n      });\n      client.on('update-start', _ref => {\n        var isInitialUpdate = _ref.isInitialUpdate;\n        currentCompileErrorMessage = null;\n        didConnect = true;\n        if (client.isEnabled() && !isInitialUpdate) {\n          DevLoadingView.showMessage('Refreshing...', 'refresh');\n        }\n      });\n      client.on('update', _ref2 => {\n        var isInitialUpdate = _ref2.isInitialUpdate;\n        if (client.isEnabled() && !isInitialUpdate) {\n          dismissRedbox();\n          _LogBox.default.clearAllLogs();\n        }\n      });\n      client.on('update-done', () => {\n        DevLoadingView.hide();\n      });\n      client.on('error', data => {\n        DevLoadingView.hide();\n        if (data.type === 'GraphNotFoundError') {\n          client.close();\n          setHMRUnavailableReason('Metro has restarted since the last edit. Reload to reconnect.');\n        } else if (data.type === 'RevisionNotFoundError') {\n          client.close();\n          setHMRUnavailableReason('Metro and the client are out of sync. Reload to reconnect.');\n        } else {\n          currentCompileErrorMessage = `${data.type} ${data.message}`;\n          if (client.isEnabled()) {\n            showCompileError();\n          }\n        }\n      });\n      client.on('close', closeEvent => {\n        DevLoadingView.hide();\n        var isNormalOrUnsetCloseReason = closeEvent == null || closeEvent.code === 1000 || closeEvent.code === 1005 || closeEvent.code == null;\n        setHMRUnavailableReason(`${isNormalOrUnsetCloseReason ? 'Disconnected from Metro.' : `Disconnected from Metro (${closeEvent.code}: \"${closeEvent.reason}\").`}\n\nTo reconnect:\n- Ensure that Metro is running and available on the same network\n- Reload this app (will trigger further help if Metro cannot be connected to)\n      `);\n      });\n      if (isEnabled) {\n        HMRClient.enable();\n      } else {\n        HMRClient.disable();\n      }\n      registerBundleEntryPoints(hmrClient);\n      flushEarlyLogs(hmrClient);\n    }\n  };\n  function setHMRUnavailableReason(reason) {\n    invariant(hmrClient, 'Expected HMRClient.setup() call at startup.');\n    if (hmrUnavailableReason !== null) {\n      return;\n    }\n    hmrUnavailableReason = reason;\n    if (hmrClient.isEnabled() && didConnect) {\n      console.warn(reason);\n    }\n  }\n  function registerBundleEntryPoints(client) {\n    if (hmrUnavailableReason != null) {\n      DevSettings.reload('Bundle Splitting – Metro disconnected');\n      return;\n    }\n    if (pendingEntryPoints.length > 0) {\n      client.send(JSON.stringify({\n        type: 'register-entrypoints',\n        entryPoints: pendingEntryPoints\n      }));\n      pendingEntryPoints.length = 0;\n    }\n  }\n  function flushEarlyLogs(client) {\n    try {\n      pendingLogs.forEach(_ref3 => {\n        var _ref4 = (0, _slicedToArray2.default)(_ref3, 2),\n          level = _ref4[0],\n          data = _ref4[1];\n        HMRClient.log(level, data);\n      });\n    } finally {\n      pendingLogs.length = 0;\n    }\n  }\n  function dismissRedbox() {\n    if (Platform.OS === 'ios' && _NativeRedBox.default != null && _NativeRedBox.default.dismiss != null) {\n      _NativeRedBox.default.dismiss();\n    } else {\n      var NativeExceptionsManager = require(_dependencyMap[11], \"../Core/NativeExceptionsManager\").default;\n      NativeExceptionsManager && NativeExceptionsManager.dismissRedbox && NativeExceptionsManager.dismissRedbox();\n    }\n  }\n  function showCompileError() {\n    if (currentCompileErrorMessage === null) {\n      return;\n    }\n    dismissRedbox();\n    var message = currentCompileErrorMessage;\n    currentCompileErrorMessage = null;\n    var error = new Error(message);\n    error.preventSymbolication = true;\n    throw error;\n  }\n  var _default = exports.default = HMRClient;\n});", "lineCount": 219, "map": [[8, 2, 13, 0], [8, 6, 13, 0, "_getDevServer2"], [8, 20, 13, 0], [8, 23, 13, 0, "_interopRequireDefault"], [8, 45, 13, 0], [8, 46, 13, 0, "require"], [8, 53, 13, 0], [8, 54, 13, 0, "_dependencyMap"], [8, 68, 13, 0], [9, 2, 14, 0], [9, 6, 14, 0, "_LogBox"], [9, 13, 14, 0], [9, 16, 14, 0, "_interopRequireDefault"], [9, 38, 14, 0], [9, 39, 14, 0, "require"], [9, 46, 14, 0], [9, 47, 14, 0, "_dependencyMap"], [9, 61, 14, 0], [10, 2, 15, 0], [10, 6, 15, 0, "_NativeRedBox"], [10, 19, 15, 0], [10, 22, 15, 0, "_interopRequireDefault"], [10, 44, 15, 0], [10, 45, 15, 0, "require"], [10, 52, 15, 0], [10, 53, 15, 0, "_dependencyMap"], [10, 67, 15, 0], [11, 2, 17, 0], [11, 6, 17, 6, "DevSettings"], [11, 17, 17, 17], [11, 20, 17, 20, "require"], [11, 27, 17, 27], [11, 28, 17, 27, "_dependencyMap"], [11, 42, 17, 27], [11, 62, 17, 43], [11, 63, 17, 44], [11, 64, 17, 45, "default"], [11, 71, 17, 52], [12, 2, 18, 0], [12, 6, 18, 6, "Platform"], [12, 14, 18, 14], [12, 17, 18, 17, "require"], [12, 24, 18, 24], [12, 25, 18, 24, "_dependencyMap"], [12, 39, 18, 24], [12, 56, 18, 37], [12, 57, 18, 38], [12, 58, 18, 39, "default"], [12, 65, 18, 46], [13, 2, 19, 0], [13, 6, 19, 6, "invariant"], [13, 15, 19, 15], [13, 18, 19, 18, "require"], [13, 25, 19, 25], [13, 26, 19, 25, "_dependencyMap"], [13, 40, 19, 25], [13, 56, 19, 37], [13, 57, 19, 38], [14, 2, 20, 0], [14, 6, 20, 6, "MetroHMRClient"], [14, 20, 20, 20], [14, 23, 20, 23, "require"], [14, 30, 20, 30], [14, 31, 20, 30, "_dependencyMap"], [14, 45, 20, 30], [14, 87, 20, 68], [14, 88, 20, 69], [15, 2, 21, 0], [15, 6, 21, 6, "prettyFormat"], [15, 18, 21, 18], [15, 21, 21, 21, "require"], [15, 28, 21, 28], [15, 29, 21, 28, "_dependencyMap"], [15, 43, 21, 28], [15, 63, 21, 44], [15, 64, 21, 45], [16, 2, 23, 0], [16, 6, 23, 6, "pendingEntryPoints"], [16, 24, 23, 24], [16, 27, 23, 27], [16, 29, 23, 29], [17, 2, 24, 0], [17, 6, 24, 4, "hmrClient"], [17, 15, 24, 13], [17, 18, 24, 16], [17, 22, 24, 20], [18, 2, 25, 0], [18, 6, 25, 4, "hmrUnavailableReason"], [18, 26, 25, 39], [18, 29, 25, 42], [18, 33, 25, 46], [19, 2, 26, 0], [19, 6, 26, 4, "currentCompileErrorMessage"], [19, 32, 26, 45], [19, 35, 26, 48], [19, 39, 26, 52], [20, 2, 27, 0], [20, 6, 27, 4, "didConnect"], [20, 16, 27, 23], [20, 19, 27, 26], [20, 24, 27, 31], [21, 2, 28, 0], [21, 6, 28, 4, "pendingLogs"], [21, 17, 28, 57], [21, 20, 28, 60], [21, 22, 28, 62], [22, 2, 60, 0], [22, 6, 60, 6, "HMRClient"], [22, 15, 60, 41], [22, 18, 60, 44], [23, 4, 61, 2, "enable"], [23, 10, 61, 8, "enable"], [23, 11, 61, 8], [23, 13, 61, 11], [24, 6, 62, 4], [24, 10, 62, 8, "hmrUnavailableReason"], [24, 30, 62, 28], [24, 35, 62, 33], [24, 39, 62, 37], [24, 41, 62, 39], [25, 8, 67, 6], [25, 14, 67, 12], [25, 18, 67, 16, "Error"], [25, 23, 67, 21], [25, 24, 67, 22, "hmrUnavailableReason"], [25, 44, 67, 42], [25, 45, 67, 43], [26, 6, 68, 4], [27, 6, 70, 4, "invariant"], [27, 15, 70, 13], [27, 16, 70, 14, "hmrClient"], [27, 25, 70, 23], [27, 27, 70, 25], [27, 72, 70, 70], [27, 73, 70, 71], [28, 6, 71, 4], [28, 10, 71, 10, "DevLoadingView"], [28, 24, 71, 24], [28, 27, 71, 27, "require"], [28, 34, 71, 34], [28, 35, 71, 34, "_dependencyMap"], [28, 49, 71, 34], [28, 73, 71, 53], [28, 74, 71, 54], [28, 75, 71, 55, "default"], [28, 82, 71, 62], [29, 6, 75, 4, "hmrClient"], [29, 15, 75, 13], [29, 16, 75, 14, "send"], [29, 20, 75, 18], [29, 21, 75, 19, "JSON"], [29, 25, 75, 23], [29, 26, 75, 24, "stringify"], [29, 35, 75, 33], [29, 36, 75, 34], [30, 8, 75, 35, "type"], [30, 12, 75, 39], [30, 14, 75, 41], [31, 6, 75, 53], [31, 7, 75, 54], [31, 8, 75, 55], [31, 9, 75, 56], [32, 6, 79, 4], [32, 10, 79, 10, "hasUpdates"], [32, 20, 79, 20], [32, 23, 79, 23, "hmrClient"], [32, 32, 79, 32], [32, 33, 79, 33, "hasPendingUpdates"], [32, 50, 79, 50], [32, 51, 79, 51], [32, 52, 79, 52], [33, 6, 81, 4], [33, 10, 81, 8, "hasUpdates"], [33, 20, 81, 18], [33, 22, 81, 20], [34, 8, 82, 6, "DevLoadingView"], [34, 22, 82, 20], [34, 23, 82, 21, "showMessage"], [34, 34, 82, 32], [34, 35, 82, 33], [34, 50, 82, 48], [34, 52, 82, 50], [34, 61, 82, 59], [34, 62, 82, 60], [35, 6, 83, 4], [36, 6, 84, 4], [36, 10, 84, 8], [37, 8, 85, 6, "hmrClient"], [37, 17, 85, 15], [37, 18, 85, 16, "enable"], [37, 24, 85, 22], [37, 25, 85, 23], [37, 26, 85, 24], [38, 6, 86, 4], [38, 7, 86, 5], [38, 16, 86, 14], [39, 8, 87, 6], [39, 12, 87, 10, "hasUpdates"], [39, 22, 87, 20], [39, 24, 87, 22], [40, 10, 88, 8, "DevLoadingView"], [40, 24, 88, 22], [40, 25, 88, 23, "hide"], [40, 29, 88, 27], [40, 30, 88, 28], [40, 31, 88, 29], [41, 8, 89, 6], [42, 6, 90, 4], [43, 6, 94, 4, "showCompileError"], [43, 22, 94, 20], [43, 23, 94, 21], [43, 24, 94, 22], [44, 4, 95, 2], [44, 5, 95, 3], [45, 4, 97, 2, "disable"], [45, 11, 97, 9, "disable"], [45, 12, 97, 9], [45, 14, 97, 12], [46, 6, 98, 4, "invariant"], [46, 15, 98, 13], [46, 16, 98, 14, "hmrClient"], [46, 25, 98, 23], [46, 27, 98, 25], [46, 72, 98, 70], [46, 73, 98, 71], [47, 6, 99, 4, "hmrClient"], [47, 15, 99, 13], [47, 16, 99, 14, "disable"], [47, 23, 99, 21], [47, 24, 99, 22], [47, 25, 99, 23], [48, 4, 100, 2], [48, 5, 100, 3], [49, 4, 102, 2, "registerBundle"], [49, 18, 102, 16, "registerBundle"], [49, 19, 102, 17, "requestUrl"], [49, 29, 102, 35], [49, 31, 102, 37], [50, 6, 103, 4, "invariant"], [50, 15, 103, 13], [50, 16, 103, 14, "hmrClient"], [50, 25, 103, 23], [50, 27, 103, 25], [50, 72, 103, 70], [50, 73, 103, 71], [51, 6, 104, 4, "pendingEntryPoints"], [51, 24, 104, 22], [51, 25, 104, 23, "push"], [51, 29, 104, 27], [51, 30, 104, 28, "requestUrl"], [51, 40, 104, 38], [51, 41, 104, 39], [52, 6, 105, 4, "registerBundleEntryPoints"], [52, 31, 105, 29], [52, 32, 105, 30, "hmrClient"], [52, 41, 105, 39], [52, 42, 105, 40], [53, 4, 106, 2], [53, 5, 106, 3], [54, 4, 108, 2, "log"], [54, 7, 108, 5, "log"], [54, 8, 108, 6, "level"], [54, 13, 108, 21], [54, 15, 108, 23, "data"], [54, 19, 108, 50], [54, 21, 108, 52], [55, 6, 109, 4], [55, 10, 109, 8], [55, 11, 109, 9, "hmrClient"], [55, 20, 109, 18], [55, 22, 109, 20], [56, 8, 112, 6, "pendingLogs"], [56, 19, 112, 17], [56, 20, 112, 18, "push"], [56, 24, 112, 22], [56, 25, 112, 23], [56, 26, 112, 24, "level"], [56, 31, 112, 29], [56, 33, 112, 31, "data"], [56, 37, 112, 35], [56, 38, 112, 36], [56, 39, 112, 37], [57, 8, 113, 6], [57, 12, 113, 10, "pendingLogs"], [57, 23, 113, 21], [57, 24, 113, 22, "length"], [57, 30, 113, 28], [57, 33, 113, 31], [57, 36, 113, 34], [57, 38, 113, 36], [58, 10, 114, 8, "pendingLogs"], [58, 21, 114, 19], [58, 22, 114, 20, "shift"], [58, 27, 114, 25], [58, 28, 114, 26], [58, 29, 114, 27], [59, 8, 115, 6], [60, 8, 116, 6], [61, 6, 117, 4], [62, 6, 118, 4], [62, 10, 118, 8], [63, 8, 119, 6, "hmrClient"], [63, 17, 119, 15], [63, 18, 119, 16, "send"], [63, 22, 119, 20], [63, 23, 120, 8, "JSON"], [63, 27, 120, 12], [63, 28, 120, 13, "stringify"], [63, 37, 120, 22], [63, 38, 120, 23], [64, 10, 121, 10, "type"], [64, 14, 121, 14], [64, 16, 121, 16], [64, 21, 121, 21], [65, 10, 122, 10, "level"], [65, 15, 122, 15], [66, 10, 123, 10, "mode"], [66, 14, 123, 14], [66, 16, 123, 16, "global"], [66, 22, 123, 22], [66, 23, 123, 23, "RN$Bridgeless"], [66, 36, 123, 36], [66, 41, 123, 41], [66, 45, 123, 45], [66, 48, 123, 48], [66, 58, 123, 58], [66, 61, 123, 61], [66, 69, 123, 69], [67, 10, 124, 10, "data"], [67, 14, 124, 14], [67, 16, 124, 16, "data"], [67, 20, 124, 20], [67, 21, 124, 21, "map"], [67, 24, 124, 24], [67, 25, 124, 25, "item"], [67, 29, 124, 29], [67, 33, 125, 12], [67, 40, 125, 19, "item"], [67, 44, 125, 23], [67, 49, 125, 28], [67, 57, 125, 36], [67, 60, 126, 16, "item"], [67, 64, 126, 20], [67, 67, 127, 16, "prettyFormat"], [67, 79, 127, 28], [67, 80, 127, 29, "format"], [67, 86, 127, 35], [67, 87, 127, 36, "item"], [67, 91, 127, 40], [67, 93, 127, 42], [68, 12, 128, 18, "escapeString"], [68, 24, 128, 30], [68, 26, 128, 32], [68, 30, 128, 36], [69, 12, 129, 18, "highlight"], [69, 21, 129, 27], [69, 23, 129, 29], [69, 27, 129, 33], [70, 12, 130, 18, "max<PERSON><PERSON><PERSON>"], [70, 20, 130, 26], [70, 22, 130, 28], [70, 23, 130, 29], [71, 12, 131, 18, "min"], [71, 15, 131, 21], [71, 17, 131, 23], [71, 21, 131, 27], [72, 12, 132, 18, "plugins"], [72, 19, 132, 25], [72, 21, 132, 27], [72, 22, 132, 28, "prettyFormat"], [72, 34, 132, 40], [72, 35, 132, 41, "plugins"], [72, 42, 132, 48], [72, 43, 132, 49, "ReactElement"], [72, 55, 132, 61], [73, 10, 133, 16], [73, 11, 133, 17], [73, 12, 134, 10], [74, 8, 135, 8], [74, 9, 135, 9], [74, 10, 136, 6], [74, 11, 136, 7], [75, 6, 137, 4], [75, 7, 137, 5], [75, 8, 137, 6], [75, 15, 137, 13, "error"], [75, 20, 137, 18], [75, 22, 137, 20], [75, 23, 140, 4], [76, 4, 141, 2], [76, 5, 141, 3], [77, 4, 145, 2, "setup"], [77, 9, 145, 7, "setup"], [77, 10, 146, 4, "platform"], [77, 18, 146, 20], [77, 20, 147, 4, "bundleEntry"], [77, 31, 147, 23], [77, 33, 148, 4, "host"], [77, 37, 148, 16], [77, 39, 149, 4, "port"], [77, 43, 149, 25], [77, 45, 150, 4, "isEnabled"], [77, 54, 150, 22], [77, 56, 152, 4], [78, 6, 152, 4], [78, 10, 151, 4, "scheme"], [78, 16, 151, 19], [78, 19, 151, 19, "arguments"], [78, 28, 151, 19], [78, 29, 151, 19, "length"], [78, 35, 151, 19], [78, 43, 151, 19, "arguments"], [78, 52, 151, 19], [78, 60, 151, 19, "undefined"], [78, 69, 151, 19], [78, 72, 151, 19, "arguments"], [78, 81, 151, 19], [78, 87, 151, 22], [78, 93, 151, 28], [79, 6, 153, 4, "invariant"], [79, 15, 153, 13], [79, 16, 153, 14, "platform"], [79, 24, 153, 22], [79, 26, 153, 24], [79, 65, 153, 63], [79, 66, 153, 64], [80, 6, 154, 4, "invariant"], [80, 15, 154, 13], [80, 16, 154, 14, "bundleEntry"], [80, 27, 154, 25], [80, 29, 154, 27], [80, 71, 154, 69], [80, 72, 154, 70], [81, 6, 155, 4, "invariant"], [81, 15, 155, 13], [81, 16, 155, 14, "host"], [81, 20, 155, 18], [81, 22, 155, 20], [81, 57, 155, 55], [81, 58, 155, 56], [82, 6, 156, 4, "invariant"], [82, 15, 156, 13], [82, 16, 156, 14], [82, 17, 156, 15, "hmrClient"], [82, 26, 156, 24], [82, 28, 156, 26], [82, 63, 156, 61], [82, 64, 156, 62], [83, 6, 159, 4], [83, 10, 159, 10, "DevLoadingView"], [83, 24, 159, 24], [83, 27, 159, 27, "require"], [83, 34, 159, 34], [83, 35, 159, 34, "_dependencyMap"], [83, 49, 159, 34], [83, 73, 159, 53], [83, 74, 159, 54], [83, 75, 159, 55, "default"], [83, 82, 159, 62], [84, 6, 161, 4], [84, 10, 161, 10, "serverHost"], [84, 20, 161, 20], [84, 23, 161, 23, "port"], [84, 27, 161, 27], [84, 32, 161, 32], [84, 36, 161, 36], [84, 40, 161, 40, "port"], [84, 44, 161, 44], [84, 49, 161, 49], [84, 51, 161, 51], [84, 54, 161, 54], [84, 57, 161, 57, "host"], [84, 61, 161, 61], [84, 65, 161, 65, "port"], [84, 69, 161, 69], [84, 71, 161, 71], [84, 74, 161, 74, "host"], [84, 78, 161, 78], [85, 6, 163, 4], [85, 10, 163, 10, "serverScheme"], [85, 22, 163, 22], [85, 25, 163, 25, "scheme"], [85, 31, 163, 31], [86, 6, 165, 4], [86, 10, 165, 10, "client"], [86, 16, 165, 16], [86, 19, 165, 19], [86, 23, 165, 23, "MetroHMRClient"], [86, 37, 165, 37], [86, 38, 165, 38], [86, 41, 165, 41, "serverScheme"], [86, 53, 165, 53], [86, 59, 165, 59, "serverHost"], [86, 69, 165, 69], [86, 75, 165, 75], [86, 76, 165, 76], [87, 6, 167, 4, "hmrClient"], [87, 15, 167, 13], [87, 18, 167, 16, "client"], [87, 24, 167, 22], [88, 6, 169, 4], [88, 10, 169, 4, "_getDevServer"], [88, 23, 169, 4], [88, 26, 169, 28], [88, 30, 169, 28, "getDevServer"], [88, 52, 169, 40], [88, 54, 169, 41], [88, 55, 169, 42], [89, 8, 169, 11, "fullBundleUrl"], [89, 21, 169, 24], [89, 24, 169, 24, "_getDevServer"], [89, 37, 169, 24], [89, 38, 169, 11, "fullBundleUrl"], [89, 51, 169, 24], [90, 6, 170, 4, "pendingEntryPoints"], [90, 24, 170, 22], [90, 25, 170, 23, "push"], [90, 29, 170, 27], [90, 30, 174, 6, "fullBundleUrl"], [90, 43, 174, 19], [90, 47, 175, 8], [90, 50, 175, 11, "serverScheme"], [90, 62, 175, 23], [90, 68, 175, 29, "serverHost"], [90, 78, 175, 39], [90, 98, 175, 59, "bundleEntry"], [90, 109, 175, 70], [90, 122, 175, 83, "platform"], [90, 130, 175, 91], [90, 132, 176, 4], [90, 133, 176, 5], [91, 6, 178, 4, "client"], [91, 12, 178, 10], [91, 13, 178, 11, "on"], [91, 15, 178, 13], [91, 16, 178, 14], [91, 34, 178, 32], [91, 36, 178, 34, "e"], [91, 37, 178, 35], [91, 41, 178, 39], [92, 8, 179, 6], [92, 12, 179, 10, "error"], [92, 17, 179, 15], [92, 20, 179, 18], [93, 0, 180, 0], [94, 0, 181, 0], [95, 0, 182, 0], [95, 65, 182, 65], [96, 8, 184, 6], [96, 12, 184, 10, "Platform"], [96, 20, 184, 18], [96, 21, 184, 19, "OS"], [96, 23, 184, 21], [96, 28, 184, 26], [96, 33, 184, 31], [96, 35, 184, 33], [97, 10, 185, 8, "error"], [97, 15, 185, 13], [97, 19, 185, 17], [98, 0, 186, 0], [98, 60, 186, 60], [99, 8, 187, 6], [99, 9, 187, 7], [99, 15, 187, 13], [100, 10, 188, 8, "error"], [100, 15, 188, 13], [100, 19, 188, 17], [101, 0, 189, 0], [102, 0, 190, 0], [103, 0, 191, 0], [103, 194, 191, 194], [104, 8, 192, 6], [105, 8, 194, 6, "error"], [105, 13, 194, 11], [105, 17, 194, 15], [106, 0, 195, 0], [107, 0, 196, 0], [107, 7, 196, 7, "host"], [107, 11, 196, 11], [107, 15, 196, 15, "port"], [107, 19, 196, 19], [108, 0, 197, 0], [109, 0, 198, 0], [109, 9, 198, 9, "e"], [109, 10, 198, 10], [109, 11, 198, 11, "message"], [109, 18, 198, 18], [109, 20, 198, 20], [110, 8, 200, 6, "setHMRUnavailableReason"], [110, 31, 200, 29], [110, 32, 200, 30, "error"], [110, 37, 200, 35], [110, 38, 200, 36], [111, 6, 201, 4], [111, 7, 201, 5], [111, 8, 201, 6], [112, 6, 203, 4, "client"], [112, 12, 203, 10], [112, 13, 203, 11, "on"], [112, 15, 203, 13], [112, 16, 203, 14], [112, 30, 203, 28], [112, 32, 203, 30, "_ref"], [112, 36, 203, 30], [112, 40, 203, 53], [113, 8, 203, 53], [113, 12, 203, 32, "isInitialUpdate"], [113, 27, 203, 47], [113, 30, 203, 47, "_ref"], [113, 34, 203, 47], [113, 35, 203, 32, "isInitialUpdate"], [113, 50, 203, 47], [114, 8, 204, 6, "currentCompileErrorMessage"], [114, 34, 204, 32], [114, 37, 204, 35], [114, 41, 204, 39], [115, 8, 205, 6, "didConnect"], [115, 18, 205, 16], [115, 21, 205, 19], [115, 25, 205, 23], [116, 8, 207, 6], [116, 12, 207, 10, "client"], [116, 18, 207, 16], [116, 19, 207, 17, "isEnabled"], [116, 28, 207, 26], [116, 29, 207, 27], [116, 30, 207, 28], [116, 34, 207, 32], [116, 35, 207, 33, "isInitialUpdate"], [116, 50, 207, 48], [116, 52, 207, 50], [117, 10, 208, 8, "DevLoadingView"], [117, 24, 208, 22], [117, 25, 208, 23, "showMessage"], [117, 36, 208, 34], [117, 37, 208, 35], [117, 52, 208, 50], [117, 54, 208, 52], [117, 63, 208, 61], [117, 64, 208, 62], [118, 8, 209, 6], [119, 6, 210, 4], [119, 7, 210, 5], [119, 8, 210, 6], [120, 6, 212, 4, "client"], [120, 12, 212, 10], [120, 13, 212, 11, "on"], [120, 15, 212, 13], [120, 16, 212, 14], [120, 24, 212, 22], [120, 26, 212, 24, "_ref2"], [120, 31, 212, 24], [120, 35, 212, 47], [121, 8, 212, 47], [121, 12, 212, 26, "isInitialUpdate"], [121, 27, 212, 41], [121, 30, 212, 41, "_ref2"], [121, 35, 212, 41], [121, 36, 212, 26, "isInitialUpdate"], [121, 51, 212, 41], [122, 8, 213, 6], [122, 12, 213, 10, "client"], [122, 18, 213, 16], [122, 19, 213, 17, "isEnabled"], [122, 28, 213, 26], [122, 29, 213, 27], [122, 30, 213, 28], [122, 34, 213, 32], [122, 35, 213, 33, "isInitialUpdate"], [122, 50, 213, 48], [122, 52, 213, 50], [123, 10, 214, 8, "dismissRedbox"], [123, 23, 214, 21], [123, 24, 214, 22], [123, 25, 214, 23], [124, 10, 215, 8, "LogBox"], [124, 25, 215, 14], [124, 26, 215, 15, "clearAllLogs"], [124, 38, 215, 27], [124, 39, 215, 28], [124, 40, 215, 29], [125, 8, 216, 6], [126, 6, 217, 4], [126, 7, 217, 5], [126, 8, 217, 6], [127, 6, 219, 4, "client"], [127, 12, 219, 10], [127, 13, 219, 11, "on"], [127, 15, 219, 13], [127, 16, 219, 14], [127, 29, 219, 27], [127, 31, 219, 29], [127, 37, 219, 35], [128, 8, 220, 6, "DevLoadingView"], [128, 22, 220, 20], [128, 23, 220, 21, "hide"], [128, 27, 220, 25], [128, 28, 220, 26], [128, 29, 220, 27], [129, 6, 221, 4], [129, 7, 221, 5], [129, 8, 221, 6], [130, 6, 223, 4, "client"], [130, 12, 223, 10], [130, 13, 223, 11, "on"], [130, 15, 223, 13], [130, 16, 223, 14], [130, 23, 223, 21], [130, 25, 223, 23, "data"], [130, 29, 223, 27], [130, 33, 223, 31], [131, 8, 224, 6, "DevLoadingView"], [131, 22, 224, 20], [131, 23, 224, 21, "hide"], [131, 27, 224, 25], [131, 28, 224, 26], [131, 29, 224, 27], [132, 8, 226, 6], [132, 12, 226, 10, "data"], [132, 16, 226, 14], [132, 17, 226, 15, "type"], [132, 21, 226, 19], [132, 26, 226, 24], [132, 46, 226, 44], [132, 48, 226, 46], [133, 10, 227, 8, "client"], [133, 16, 227, 14], [133, 17, 227, 15, "close"], [133, 22, 227, 20], [133, 23, 227, 21], [133, 24, 227, 22], [134, 10, 228, 8, "setHMRUnavailableReason"], [134, 33, 228, 31], [134, 34, 229, 10], [134, 97, 230, 8], [134, 98, 230, 9], [135, 8, 231, 6], [135, 9, 231, 7], [135, 15, 231, 13], [135, 19, 231, 17, "data"], [135, 23, 231, 21], [135, 24, 231, 22, "type"], [135, 28, 231, 26], [135, 33, 231, 31], [135, 56, 231, 54], [135, 58, 231, 56], [136, 10, 232, 8, "client"], [136, 16, 232, 14], [136, 17, 232, 15, "close"], [136, 22, 232, 20], [136, 23, 232, 21], [136, 24, 232, 22], [137, 10, 233, 8, "setHMRUnavailableReason"], [137, 33, 233, 31], [137, 34, 234, 10], [137, 94, 235, 8], [137, 95, 235, 9], [138, 8, 236, 6], [138, 9, 236, 7], [138, 15, 236, 13], [139, 10, 237, 8, "currentCompileErrorMessage"], [139, 36, 237, 34], [139, 39, 237, 37], [139, 42, 237, 40, "data"], [139, 46, 237, 44], [139, 47, 237, 45, "type"], [139, 51, 237, 49], [139, 55, 237, 53, "data"], [139, 59, 237, 57], [139, 60, 237, 58, "message"], [139, 67, 237, 65], [139, 69, 237, 67], [140, 10, 238, 8], [140, 14, 238, 12, "client"], [140, 20, 238, 18], [140, 21, 238, 19, "isEnabled"], [140, 30, 238, 28], [140, 31, 238, 29], [140, 32, 238, 30], [140, 34, 238, 32], [141, 12, 239, 10, "showCompileError"], [141, 28, 239, 26], [141, 29, 239, 27], [141, 30, 239, 28], [142, 10, 240, 8], [143, 8, 241, 6], [144, 6, 242, 4], [144, 7, 242, 5], [144, 8, 242, 6], [145, 6, 244, 4, "client"], [145, 12, 244, 10], [145, 13, 244, 11, "on"], [145, 15, 244, 13], [145, 16, 244, 14], [145, 23, 244, 21], [145, 25, 244, 23, "closeEvent"], [145, 35, 244, 33], [145, 39, 244, 37], [146, 8, 245, 6, "DevLoadingView"], [146, 22, 245, 20], [146, 23, 245, 21, "hide"], [146, 27, 245, 25], [146, 28, 245, 26], [146, 29, 245, 27], [147, 8, 249, 6], [147, 12, 249, 12, "isNormalOrUnsetCloseReason"], [147, 38, 249, 38], [147, 41, 250, 8, "closeEvent"], [147, 51, 250, 18], [147, 55, 250, 22], [147, 59, 250, 26], [147, 63, 251, 8, "closeEvent"], [147, 73, 251, 18], [147, 74, 251, 19, "code"], [147, 78, 251, 23], [147, 83, 251, 28], [147, 87, 251, 32], [147, 91, 252, 8, "closeEvent"], [147, 101, 252, 18], [147, 102, 252, 19, "code"], [147, 106, 252, 23], [147, 111, 252, 28], [147, 115, 252, 32], [147, 119, 253, 8, "closeEvent"], [147, 129, 253, 18], [147, 130, 253, 19, "code"], [147, 134, 253, 23], [147, 138, 253, 27], [147, 142, 253, 31], [148, 8, 255, 6, "setHMRUnavailableReason"], [148, 31, 255, 29], [148, 32, 256, 8], [148, 35, 257, 10, "isNormalOrUnsetCloseReason"], [148, 61, 257, 36], [148, 64, 258, 14], [148, 90, 258, 40], [148, 93, 259, 14], [148, 121, 259, 42, "closeEvent"], [148, 131, 259, 52], [148, 132, 259, 53, "code"], [148, 136, 259, 57], [148, 142, 259, 63, "closeEvent"], [148, 152, 259, 73], [148, 153, 259, 74, "reason"], [148, 159, 259, 80], [148, 164, 259, 85], [149, 0, 260, 0], [150, 0, 261, 0], [151, 0, 262, 0], [152, 0, 263, 0], [153, 0, 264, 0], [153, 7, 266, 6], [153, 8, 266, 7], [154, 6, 267, 4], [154, 7, 267, 5], [154, 8, 267, 6], [155, 6, 269, 4], [155, 10, 269, 8, "isEnabled"], [155, 19, 269, 17], [155, 21, 269, 19], [156, 8, 270, 6, "HMRClient"], [156, 17, 270, 15], [156, 18, 270, 16, "enable"], [156, 24, 270, 22], [156, 25, 270, 23], [156, 26, 270, 24], [157, 6, 271, 4], [157, 7, 271, 5], [157, 13, 271, 11], [158, 8, 272, 6, "HMRClient"], [158, 17, 272, 15], [158, 18, 272, 16, "disable"], [158, 25, 272, 23], [158, 26, 272, 24], [158, 27, 272, 25], [159, 6, 273, 4], [160, 6, 275, 4, "registerBundleEntryPoints"], [160, 31, 275, 29], [160, 32, 275, 30, "hmrClient"], [160, 41, 275, 39], [160, 42, 275, 40], [161, 6, 276, 4, "flush<PERSON>arly<PERSON><PERSON>s"], [161, 20, 276, 18], [161, 21, 276, 19, "hmrClient"], [161, 30, 276, 28], [161, 31, 276, 29], [162, 4, 277, 2], [163, 2, 278, 0], [163, 3, 278, 1], [164, 2, 280, 0], [164, 11, 280, 9, "setHMRUnavailableReason"], [164, 34, 280, 32, "setHMRUnavailableReason"], [164, 35, 280, 33, "reason"], [164, 41, 280, 47], [164, 43, 280, 49], [165, 4, 281, 2, "invariant"], [165, 13, 281, 11], [165, 14, 281, 12, "hmrClient"], [165, 23, 281, 21], [165, 25, 281, 23], [165, 70, 281, 68], [165, 71, 281, 69], [166, 4, 282, 2], [166, 8, 282, 6, "hmrUnavailableReason"], [166, 28, 282, 26], [166, 33, 282, 31], [166, 37, 282, 35], [166, 39, 282, 37], [167, 6, 284, 4], [168, 4, 285, 2], [169, 4, 286, 2, "hmrUnavailableReason"], [169, 24, 286, 22], [169, 27, 286, 25, "reason"], [169, 33, 286, 31], [170, 4, 291, 2], [170, 8, 291, 6, "hmrClient"], [170, 17, 291, 15], [170, 18, 291, 16, "isEnabled"], [170, 27, 291, 25], [170, 28, 291, 26], [170, 29, 291, 27], [170, 33, 291, 31, "didConnect"], [170, 43, 291, 41], [170, 45, 291, 43], [171, 6, 292, 4, "console"], [171, 13, 292, 11], [171, 14, 292, 12, "warn"], [171, 18, 292, 16], [171, 19, 292, 17, "reason"], [171, 25, 292, 23], [171, 26, 292, 24], [172, 4, 294, 2], [173, 2, 295, 0], [174, 2, 297, 0], [174, 11, 297, 9, "registerBundleEntryPoints"], [174, 36, 297, 34, "registerBundleEntryPoints"], [174, 37, 297, 35, "client"], [174, 43, 297, 57], [174, 45, 297, 59], [175, 4, 298, 2], [175, 8, 298, 6, "hmrUnavailableReason"], [175, 28, 298, 26], [175, 32, 298, 30], [175, 36, 298, 34], [175, 38, 298, 36], [176, 6, 299, 4, "DevSettings"], [176, 17, 299, 15], [176, 18, 299, 16, "reload"], [176, 24, 299, 22], [176, 25, 299, 23], [176, 64, 299, 62], [176, 65, 299, 63], [177, 6, 300, 4], [178, 4, 301, 2], [179, 4, 303, 2], [179, 8, 303, 6, "pendingEntryPoints"], [179, 26, 303, 24], [179, 27, 303, 25, "length"], [179, 33, 303, 31], [179, 36, 303, 34], [179, 37, 303, 35], [179, 39, 303, 37], [180, 6, 304, 4, "client"], [180, 12, 304, 10], [180, 13, 304, 11, "send"], [180, 17, 304, 15], [180, 18, 305, 6, "JSON"], [180, 22, 305, 10], [180, 23, 305, 11, "stringify"], [180, 32, 305, 20], [180, 33, 305, 21], [181, 8, 306, 8, "type"], [181, 12, 306, 12], [181, 14, 306, 14], [181, 36, 306, 36], [182, 8, 307, 8, "entryPoints"], [182, 19, 307, 19], [182, 21, 307, 21, "pendingEntryPoints"], [183, 6, 308, 6], [183, 7, 308, 7], [183, 8, 309, 4], [183, 9, 309, 5], [184, 6, 310, 4, "pendingEntryPoints"], [184, 24, 310, 22], [184, 25, 310, 23, "length"], [184, 31, 310, 29], [184, 34, 310, 32], [184, 35, 310, 33], [185, 4, 311, 2], [186, 2, 312, 0], [187, 2, 314, 0], [187, 11, 314, 9, "flush<PERSON>arly<PERSON><PERSON>s"], [187, 25, 314, 23, "flush<PERSON>arly<PERSON><PERSON>s"], [187, 26, 314, 24, "client"], [187, 32, 314, 46], [187, 34, 314, 48], [188, 4, 315, 2], [188, 8, 315, 6], [189, 6, 316, 4, "pendingLogs"], [189, 17, 316, 15], [189, 18, 316, 16, "for<PERSON>ach"], [189, 25, 316, 23], [189, 26, 316, 24, "_ref3"], [189, 31, 316, 24], [189, 35, 316, 43], [190, 8, 316, 43], [190, 12, 316, 43, "_ref4"], [190, 17, 316, 43], [190, 24, 316, 43, "_slicedToArray2"], [190, 39, 316, 43], [190, 40, 316, 43, "default"], [190, 47, 316, 43], [190, 49, 316, 43, "_ref3"], [190, 54, 316, 43], [191, 10, 316, 26, "level"], [191, 15, 316, 31], [191, 18, 316, 31, "_ref4"], [191, 23, 316, 31], [192, 10, 316, 33, "data"], [192, 14, 316, 37], [192, 17, 316, 37, "_ref4"], [192, 22, 316, 37], [193, 8, 317, 6, "HMRClient"], [193, 17, 317, 15], [193, 18, 317, 16, "log"], [193, 21, 317, 19], [193, 22, 317, 20, "level"], [193, 27, 317, 25], [193, 29, 317, 27, "data"], [193, 33, 317, 31], [193, 34, 317, 32], [194, 6, 318, 4], [194, 7, 318, 5], [194, 8, 318, 6], [195, 4, 319, 2], [195, 5, 319, 3], [195, 14, 319, 12], [196, 6, 320, 4, "pendingLogs"], [196, 17, 320, 15], [196, 18, 320, 16, "length"], [196, 24, 320, 22], [196, 27, 320, 25], [196, 28, 320, 26], [197, 4, 321, 2], [198, 2, 322, 0], [199, 2, 324, 0], [199, 11, 324, 9, "dismissRedbox"], [199, 24, 324, 22, "dismissRedbox"], [199, 25, 324, 22], [199, 27, 324, 25], [200, 4, 325, 2], [200, 8, 326, 4, "Platform"], [200, 16, 326, 12], [200, 17, 326, 13, "OS"], [200, 19, 326, 15], [200, 24, 326, 20], [200, 29, 326, 25], [200, 33, 327, 4, "NativeRedBox"], [200, 54, 327, 16], [200, 58, 327, 20], [200, 62, 327, 24], [200, 66, 328, 4, "NativeRedBox"], [200, 87, 328, 16], [200, 88, 328, 17, "dismiss"], [200, 95, 328, 24], [200, 99, 328, 28], [200, 103, 328, 32], [200, 105, 329, 4], [201, 6, 330, 4, "NativeRedBox"], [201, 27, 330, 16], [201, 28, 330, 17, "dismiss"], [201, 35, 330, 24], [201, 36, 330, 25], [201, 37, 330, 26], [202, 4, 331, 2], [202, 5, 331, 3], [202, 11, 331, 9], [203, 6, 332, 4], [203, 10, 332, 10, "NativeExceptionsManager"], [203, 33, 332, 33], [203, 36, 333, 6, "require"], [203, 43, 333, 13], [203, 44, 333, 13, "_dependencyMap"], [203, 58, 333, 13], [203, 97, 333, 47], [203, 98, 333, 48], [203, 99, 333, 49, "default"], [203, 106, 333, 56], [204, 6, 334, 4, "NativeExceptionsManager"], [204, 29, 334, 27], [204, 33, 335, 6, "NativeExceptionsManager"], [204, 56, 335, 29], [204, 57, 335, 30, "dismissRedbox"], [204, 70, 335, 43], [204, 74, 336, 6, "NativeExceptionsManager"], [204, 97, 336, 29], [204, 98, 336, 30, "dismissRedbox"], [204, 111, 336, 43], [204, 112, 336, 44], [204, 113, 336, 45], [205, 4, 337, 2], [206, 2, 338, 0], [207, 2, 340, 0], [207, 11, 340, 9, "showCompileError"], [207, 27, 340, 25, "showCompileError"], [207, 28, 340, 25], [207, 30, 340, 28], [208, 4, 341, 2], [208, 8, 341, 6, "currentCompileErrorMessage"], [208, 34, 341, 32], [208, 39, 341, 37], [208, 43, 341, 41], [208, 45, 341, 43], [209, 6, 342, 4], [210, 4, 343, 2], [211, 4, 347, 2, "dismissRedbox"], [211, 17, 347, 15], [211, 18, 347, 16], [211, 19, 347, 17], [212, 4, 349, 2], [212, 8, 349, 8, "message"], [212, 15, 349, 15], [212, 18, 349, 18, "currentCompileErrorMessage"], [212, 44, 349, 44], [213, 4, 350, 2, "currentCompileErrorMessage"], [213, 30, 350, 28], [213, 33, 350, 31], [213, 37, 350, 35], [214, 4, 355, 2], [214, 8, 355, 8, "error"], [214, 13, 355, 28], [214, 16, 355, 31], [214, 20, 355, 35, "Error"], [214, 25, 355, 40], [214, 26, 355, 41, "message"], [214, 33, 355, 48], [214, 34, 355, 49], [215, 4, 358, 2, "error"], [215, 9, 358, 7], [215, 10, 358, 8, "preventSymbolication"], [215, 30, 358, 28], [215, 33, 358, 31], [215, 37, 358, 35], [216, 4, 359, 2], [216, 10, 359, 8, "error"], [216, 15, 359, 13], [217, 2, 360, 0], [218, 2, 360, 1], [218, 6, 360, 1, "_default"], [218, 14, 360, 1], [218, 17, 360, 1, "exports"], [218, 24, 360, 1], [218, 25, 360, 1, "default"], [218, 32, 360, 1], [218, 35, 362, 15, "HMRClient"], [218, 44, 362, 24], [219, 0, 362, 24], [219, 3]], "functionMap": {"names": ["<global>", "enable", "disable", "registerBundle", "log", "data.map$argument_0", "setup", "client.on$argument_1", "setHMRUnavailableReason", "registerBundleEntryPoints", "flush<PERSON>arly<PERSON><PERSON>s", "pendingLogs.forEach$argument_0", "dismissRedbox", "showCompileError"], "mappings": "AAA;EC4D;GDkC;EEE;GFG;EGE;GHI;EIE;yBCgB;kBDS;GJQ;EMI;kCCiC;KDuB;8BCE;KDO;wBCE;KDK;6BCE;KDE;uBCE;KDmB;uBCE;KDuB;GNU;AQG;CRe;ASE;CTe;AUE;wBCE;KDE;CVI;AYE;CZc;AaE;CboB"}}, "type": "js/module"}]}