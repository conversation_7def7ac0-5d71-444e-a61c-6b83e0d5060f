{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var UIManagerProperties = ['clearJSResponder', 'configureNextLayoutAnimation', 'createView', 'dispatchViewManagerCommand', 'findSubviewIn', 'getConstantsForViewManager', 'getDefaultEventTypes', 'manageChildren', 'measure', 'measureInWindow', 'measureLayout', 'measureLayoutRelativeToParent', 'removeRootView', 'sendAccessibilityEvent', 'setChildren', 'setJSResponder', 'setLayoutAnimationEnabledExperimental', 'updateView', 'viewIsDescendantOf', 'LazyViewManagersEnabled', 'ViewManagerNames', 'StyleConstants', 'AccessibilityEventTypes', 'UIView', 'getViewManagerConfig', 'hasViewManagerConfig', 'blur', 'focus', 'genericBubblingEventTypes', 'genericDirectEventTypes', 'lazilyLoadView'];\n  var _default = exports.default = UIManagerProperties;\n});", "lineCount": 10, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 29, 0], [8, 6, 29, 6, "UIManagerProperties"], [8, 25, 29, 49], [8, 28, 29, 52], [8, 29, 30, 2], [8, 47, 30, 20], [8, 49, 31, 2], [8, 79, 31, 32], [8, 81, 32, 2], [8, 93, 32, 14], [8, 95, 33, 2], [8, 123, 33, 30], [8, 125, 34, 2], [8, 140, 34, 17], [8, 142, 35, 2], [8, 170, 35, 30], [8, 172, 36, 2], [8, 194, 36, 24], [8, 196, 37, 2], [8, 212, 37, 18], [8, 214, 38, 2], [8, 223, 38, 11], [8, 225, 39, 2], [8, 242, 39, 19], [8, 244, 40, 2], [8, 259, 40, 17], [8, 261, 41, 2], [8, 292, 41, 33], [8, 294, 42, 2], [8, 310, 42, 18], [8, 312, 43, 2], [8, 336, 43, 26], [8, 338, 44, 2], [8, 351, 44, 15], [8, 353, 45, 2], [8, 369, 45, 18], [8, 371, 46, 2], [8, 410, 46, 41], [8, 412, 47, 2], [8, 424, 47, 14], [8, 426, 48, 2], [8, 446, 48, 22], [8, 448, 49, 2], [8, 473, 49, 27], [8, 475, 50, 2], [8, 493, 50, 20], [8, 495, 51, 2], [8, 511, 51, 18], [8, 513, 52, 2], [8, 538, 52, 27], [8, 540, 53, 2], [8, 548, 53, 10], [8, 550, 54, 2], [8, 572, 54, 24], [8, 574, 55, 2], [8, 596, 55, 24], [8, 598, 56, 2], [8, 604, 56, 8], [8, 606, 57, 2], [8, 613, 57, 9], [8, 615, 58, 2], [8, 642, 58, 29], [8, 644, 59, 2], [8, 669, 59, 27], [8, 671, 60, 2], [8, 687, 60, 18], [8, 688, 61, 1], [9, 2, 61, 2], [9, 6, 61, 2, "_default"], [9, 14, 61, 2], [9, 17, 61, 2, "exports"], [9, 24, 61, 2], [9, 25, 61, 2, "default"], [9, 32, 61, 2], [9, 35, 63, 15, "UIManagerProperties"], [9, 54, 63, 34], [10, 0, 63, 34], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}