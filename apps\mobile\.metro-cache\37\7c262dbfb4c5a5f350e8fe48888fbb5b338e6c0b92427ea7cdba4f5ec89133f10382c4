{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 51, "index": 98}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useKeyboardManager = useKeyboardManager;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _reactNative = require(_dependencyMap[1], \"react-native\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function useKeyboardManager(isEnabled) {\n    // Numeric id of the previously focused text input\n    // When a gesture didn't change the tab, we can restore the focused input with this\n    var previouslyFocusedTextInputRef = React.useRef(undefined);\n    var startTimestampRef = React.useRef(0);\n    var keyboardTimeoutRef = React.useRef(undefined);\n    var clearKeyboardTimeout = React.useCallback(() => {\n      if (keyboardTimeoutRef.current !== undefined) {\n        clearTimeout(keyboardTimeoutRef.current);\n        keyboardTimeoutRef.current = undefined;\n      }\n    }, []);\n    var onPageChangeStart = React.useCallback(() => {\n      if (!isEnabled()) {\n        return;\n      }\n      clearKeyboardTimeout();\n      var input = _reactNative.TextInput.State.currentlyFocusedInput();\n\n      // When a page change begins, blur the currently focused input\n      input?.blur();\n\n      // Store the id of this input so we can refocus it if change was cancelled\n      previouslyFocusedTextInputRef.current = input;\n\n      // Store timestamp for touch start\n      startTimestampRef.current = Date.now();\n    }, [clearKeyboardTimeout, isEnabled]);\n    var onPageChangeConfirm = React.useCallback(force => {\n      if (!isEnabled()) {\n        return;\n      }\n      clearKeyboardTimeout();\n      if (force) {\n        // Always dismiss input, even if we don't have a ref to it\n        // We might not have the ref if onPageChangeStart was never called\n        // This can happen if page change was not from a gesture\n        _reactNative.Keyboard.dismiss();\n      } else {\n        var input = previouslyFocusedTextInputRef.current;\n\n        // Dismiss the keyboard only if an input was a focused before\n        // This makes sure we don't dismiss input on going back and focusing an input\n        input?.blur();\n      }\n\n      // Cleanup the ID on successful page change\n      previouslyFocusedTextInputRef.current = undefined;\n    }, [clearKeyboardTimeout, isEnabled]);\n    var onPageChangeCancel = React.useCallback(() => {\n      if (!isEnabled()) {\n        return;\n      }\n      clearKeyboardTimeout();\n\n      // The page didn't change, we should restore the focus of text input\n      var input = previouslyFocusedTextInputRef.current;\n      if (input) {\n        // If the interaction was super short we should make sure keyboard won't hide again.\n\n        // Too fast input refocus will result only in keyboard flashing on screen and hiding right away.\n        // During first ~100ms keyboard will be dismissed no matter what,\n        // so we have to make sure it won't interrupt input refocus logic.\n        // That's why when the interaction is shorter than 100ms we add delay so it won't hide once again.\n        // Subtracting timestamps makes us sure the delay is executed only when needed.\n        if (Date.now() - startTimestampRef.current < 100) {\n          keyboardTimeoutRef.current = setTimeout(() => {\n            input?.focus();\n            previouslyFocusedTextInputRef.current = undefined;\n          }, 100);\n        } else {\n          input?.focus();\n          previouslyFocusedTextInputRef.current = undefined;\n        }\n      }\n    }, [clearKeyboardTimeout, isEnabled]);\n    React.useEffect(() => {\n      return () => clearKeyboardTimeout();\n    }, [clearKeyboardTimeout]);\n    return {\n      onPageChangeStart,\n      onPageChangeConfirm,\n      onPageChangeCancel\n    };\n  }\n});", "lineCount": 96, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useKeyboardManager"], [7, 28, 1, 13], [7, 31, 1, 13, "useKeyboardManager"], [7, 49, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_reactNative"], [9, 18, 4, 0], [9, 21, 4, 0, "require"], [9, 28, 4, 0], [9, 29, 4, 0, "_dependencyMap"], [9, 43, 4, 0], [10, 2, 4, 51], [10, 11, 4, 51, "_interopRequireWildcard"], [10, 35, 4, 51, "e"], [10, 36, 4, 51], [10, 38, 4, 51, "t"], [10, 39, 4, 51], [10, 68, 4, 51, "WeakMap"], [10, 75, 4, 51], [10, 81, 4, 51, "r"], [10, 82, 4, 51], [10, 89, 4, 51, "WeakMap"], [10, 96, 4, 51], [10, 100, 4, 51, "n"], [10, 101, 4, 51], [10, 108, 4, 51, "WeakMap"], [10, 115, 4, 51], [10, 127, 4, 51, "_interopRequireWildcard"], [10, 150, 4, 51], [10, 162, 4, 51, "_interopRequireWildcard"], [10, 163, 4, 51, "e"], [10, 164, 4, 51], [10, 166, 4, 51, "t"], [10, 167, 4, 51], [10, 176, 4, 51, "t"], [10, 177, 4, 51], [10, 181, 4, 51, "e"], [10, 182, 4, 51], [10, 186, 4, 51, "e"], [10, 187, 4, 51], [10, 188, 4, 51, "__esModule"], [10, 198, 4, 51], [10, 207, 4, 51, "e"], [10, 208, 4, 51], [10, 214, 4, 51, "o"], [10, 215, 4, 51], [10, 217, 4, 51, "i"], [10, 218, 4, 51], [10, 220, 4, 51, "f"], [10, 221, 4, 51], [10, 226, 4, 51, "__proto__"], [10, 235, 4, 51], [10, 243, 4, 51, "default"], [10, 250, 4, 51], [10, 252, 4, 51, "e"], [10, 253, 4, 51], [10, 270, 4, 51, "e"], [10, 271, 4, 51], [10, 294, 4, 51, "e"], [10, 295, 4, 51], [10, 320, 4, 51, "e"], [10, 321, 4, 51], [10, 330, 4, 51, "f"], [10, 331, 4, 51], [10, 337, 4, 51, "o"], [10, 338, 4, 51], [10, 341, 4, 51, "t"], [10, 342, 4, 51], [10, 345, 4, 51, "n"], [10, 346, 4, 51], [10, 349, 4, 51, "r"], [10, 350, 4, 51], [10, 358, 4, 51, "o"], [10, 359, 4, 51], [10, 360, 4, 51, "has"], [10, 363, 4, 51], [10, 364, 4, 51, "e"], [10, 365, 4, 51], [10, 375, 4, 51, "o"], [10, 376, 4, 51], [10, 377, 4, 51, "get"], [10, 380, 4, 51], [10, 381, 4, 51, "e"], [10, 382, 4, 51], [10, 385, 4, 51, "o"], [10, 386, 4, 51], [10, 387, 4, 51, "set"], [10, 390, 4, 51], [10, 391, 4, 51, "e"], [10, 392, 4, 51], [10, 394, 4, 51, "f"], [10, 395, 4, 51], [10, 409, 4, 51, "_t"], [10, 411, 4, 51], [10, 415, 4, 51, "e"], [10, 416, 4, 51], [10, 432, 4, 51, "_t"], [10, 434, 4, 51], [10, 441, 4, 51, "hasOwnProperty"], [10, 455, 4, 51], [10, 456, 4, 51, "call"], [10, 460, 4, 51], [10, 461, 4, 51, "e"], [10, 462, 4, 51], [10, 464, 4, 51, "_t"], [10, 466, 4, 51], [10, 473, 4, 51, "i"], [10, 474, 4, 51], [10, 478, 4, 51, "o"], [10, 479, 4, 51], [10, 482, 4, 51, "Object"], [10, 488, 4, 51], [10, 489, 4, 51, "defineProperty"], [10, 503, 4, 51], [10, 508, 4, 51, "Object"], [10, 514, 4, 51], [10, 515, 4, 51, "getOwnPropertyDescriptor"], [10, 539, 4, 51], [10, 540, 4, 51, "e"], [10, 541, 4, 51], [10, 543, 4, 51, "_t"], [10, 545, 4, 51], [10, 552, 4, 51, "i"], [10, 553, 4, 51], [10, 554, 4, 51, "get"], [10, 557, 4, 51], [10, 561, 4, 51, "i"], [10, 562, 4, 51], [10, 563, 4, 51, "set"], [10, 566, 4, 51], [10, 570, 4, 51, "o"], [10, 571, 4, 51], [10, 572, 4, 51, "f"], [10, 573, 4, 51], [10, 575, 4, 51, "_t"], [10, 577, 4, 51], [10, 579, 4, 51, "i"], [10, 580, 4, 51], [10, 584, 4, 51, "f"], [10, 585, 4, 51], [10, 586, 4, 51, "_t"], [10, 588, 4, 51], [10, 592, 4, 51, "e"], [10, 593, 4, 51], [10, 594, 4, 51, "_t"], [10, 596, 4, 51], [10, 607, 4, 51, "f"], [10, 608, 4, 51], [10, 613, 4, 51, "e"], [10, 614, 4, 51], [10, 616, 4, 51, "t"], [10, 617, 4, 51], [11, 2, 5, 7], [11, 11, 5, 16, "useKeyboardManager"], [11, 29, 5, 34, "useKeyboardManager"], [11, 30, 5, 35, "isEnabled"], [11, 39, 5, 44], [11, 41, 5, 46], [12, 4, 6, 2], [13, 4, 7, 2], [14, 4, 8, 2], [14, 8, 8, 8, "previouslyFocusedTextInputRef"], [14, 37, 8, 37], [14, 40, 8, 40, "React"], [14, 45, 8, 45], [14, 46, 8, 46, "useRef"], [14, 52, 8, 52], [14, 53, 8, 53, "undefined"], [14, 62, 8, 62], [14, 63, 8, 63], [15, 4, 9, 2], [15, 8, 9, 8, "startTimestampRef"], [15, 25, 9, 25], [15, 28, 9, 28, "React"], [15, 33, 9, 33], [15, 34, 9, 34, "useRef"], [15, 40, 9, 40], [15, 41, 9, 41], [15, 42, 9, 42], [15, 43, 9, 43], [16, 4, 10, 2], [16, 8, 10, 8, "keyboardTimeoutRef"], [16, 26, 10, 26], [16, 29, 10, 29, "React"], [16, 34, 10, 34], [16, 35, 10, 35, "useRef"], [16, 41, 10, 41], [16, 42, 10, 42, "undefined"], [16, 51, 10, 51], [16, 52, 10, 52], [17, 4, 11, 2], [17, 8, 11, 8, "clearKeyboardTimeout"], [17, 28, 11, 28], [17, 31, 11, 31, "React"], [17, 36, 11, 36], [17, 37, 11, 37, "useCallback"], [17, 48, 11, 48], [17, 49, 11, 49], [17, 55, 11, 55], [18, 6, 12, 4], [18, 10, 12, 8, "keyboardTimeoutRef"], [18, 28, 12, 26], [18, 29, 12, 27, "current"], [18, 36, 12, 34], [18, 41, 12, 39, "undefined"], [18, 50, 12, 48], [18, 52, 12, 50], [19, 8, 13, 6, "clearTimeout"], [19, 20, 13, 18], [19, 21, 13, 19, "keyboardTimeoutRef"], [19, 39, 13, 37], [19, 40, 13, 38, "current"], [19, 47, 13, 45], [19, 48, 13, 46], [20, 8, 14, 6, "keyboardTimeoutRef"], [20, 26, 14, 24], [20, 27, 14, 25, "current"], [20, 34, 14, 32], [20, 37, 14, 35, "undefined"], [20, 46, 14, 44], [21, 6, 15, 4], [22, 4, 16, 2], [22, 5, 16, 3], [22, 7, 16, 5], [22, 9, 16, 7], [22, 10, 16, 8], [23, 4, 17, 2], [23, 8, 17, 8, "onPageChangeStart"], [23, 25, 17, 25], [23, 28, 17, 28, "React"], [23, 33, 17, 33], [23, 34, 17, 34, "useCallback"], [23, 45, 17, 45], [23, 46, 17, 46], [23, 52, 17, 52], [24, 6, 18, 4], [24, 10, 18, 8], [24, 11, 18, 9, "isEnabled"], [24, 20, 18, 18], [24, 21, 18, 19], [24, 22, 18, 20], [24, 24, 18, 22], [25, 8, 19, 6], [26, 6, 20, 4], [27, 6, 21, 4, "clearKeyboardTimeout"], [27, 26, 21, 24], [27, 27, 21, 25], [27, 28, 21, 26], [28, 6, 22, 4], [28, 10, 22, 10, "input"], [28, 15, 22, 15], [28, 18, 22, 18, "TextInput"], [28, 40, 22, 27], [28, 41, 22, 28, "State"], [28, 46, 22, 33], [28, 47, 22, 34, "currentlyFocusedInput"], [28, 68, 22, 55], [28, 69, 22, 56], [28, 70, 22, 57], [30, 6, 24, 4], [31, 6, 25, 4, "input"], [31, 11, 25, 9], [31, 13, 25, 11, "blur"], [31, 17, 25, 15], [31, 18, 25, 16], [31, 19, 25, 17], [33, 6, 27, 4], [34, 6, 28, 4, "previouslyFocusedTextInputRef"], [34, 35, 28, 33], [34, 36, 28, 34, "current"], [34, 43, 28, 41], [34, 46, 28, 44, "input"], [34, 51, 28, 49], [36, 6, 30, 4], [37, 6, 31, 4, "startTimestampRef"], [37, 23, 31, 21], [37, 24, 31, 22, "current"], [37, 31, 31, 29], [37, 34, 31, 32, "Date"], [37, 38, 31, 36], [37, 39, 31, 37, "now"], [37, 42, 31, 40], [37, 43, 31, 41], [37, 44, 31, 42], [38, 4, 32, 2], [38, 5, 32, 3], [38, 7, 32, 5], [38, 8, 32, 6, "clearKeyboardTimeout"], [38, 28, 32, 26], [38, 30, 32, 28, "isEnabled"], [38, 39, 32, 37], [38, 40, 32, 38], [38, 41, 32, 39], [39, 4, 33, 2], [39, 8, 33, 8, "onPageChangeConfirm"], [39, 27, 33, 27], [39, 30, 33, 30, "React"], [39, 35, 33, 35], [39, 36, 33, 36, "useCallback"], [39, 47, 33, 47], [39, 48, 33, 48, "force"], [39, 53, 33, 53], [39, 57, 33, 57], [40, 6, 34, 4], [40, 10, 34, 8], [40, 11, 34, 9, "isEnabled"], [40, 20, 34, 18], [40, 21, 34, 19], [40, 22, 34, 20], [40, 24, 34, 22], [41, 8, 35, 6], [42, 6, 36, 4], [43, 6, 37, 4, "clearKeyboardTimeout"], [43, 26, 37, 24], [43, 27, 37, 25], [43, 28, 37, 26], [44, 6, 38, 4], [44, 10, 38, 8, "force"], [44, 15, 38, 13], [44, 17, 38, 15], [45, 8, 39, 6], [46, 8, 40, 6], [47, 8, 41, 6], [48, 8, 42, 6, "Keyboard"], [48, 29, 42, 14], [48, 30, 42, 15, "dismiss"], [48, 37, 42, 22], [48, 38, 42, 23], [48, 39, 42, 24], [49, 6, 43, 4], [49, 7, 43, 5], [49, 13, 43, 11], [50, 8, 44, 6], [50, 12, 44, 12, "input"], [50, 17, 44, 17], [50, 20, 44, 20, "previouslyFocusedTextInputRef"], [50, 49, 44, 49], [50, 50, 44, 50, "current"], [50, 57, 44, 57], [52, 8, 46, 6], [53, 8, 47, 6], [54, 8, 48, 6, "input"], [54, 13, 48, 11], [54, 15, 48, 13, "blur"], [54, 19, 48, 17], [54, 20, 48, 18], [54, 21, 48, 19], [55, 6, 49, 4], [57, 6, 51, 4], [58, 6, 52, 4, "previouslyFocusedTextInputRef"], [58, 35, 52, 33], [58, 36, 52, 34, "current"], [58, 43, 52, 41], [58, 46, 52, 44, "undefined"], [58, 55, 52, 53], [59, 4, 53, 2], [59, 5, 53, 3], [59, 7, 53, 5], [59, 8, 53, 6, "clearKeyboardTimeout"], [59, 28, 53, 26], [59, 30, 53, 28, "isEnabled"], [59, 39, 53, 37], [59, 40, 53, 38], [59, 41, 53, 39], [60, 4, 54, 2], [60, 8, 54, 8, "onPageChangeCancel"], [60, 26, 54, 26], [60, 29, 54, 29, "React"], [60, 34, 54, 34], [60, 35, 54, 35, "useCallback"], [60, 46, 54, 46], [60, 47, 54, 47], [60, 53, 54, 53], [61, 6, 55, 4], [61, 10, 55, 8], [61, 11, 55, 9, "isEnabled"], [61, 20, 55, 18], [61, 21, 55, 19], [61, 22, 55, 20], [61, 24, 55, 22], [62, 8, 56, 6], [63, 6, 57, 4], [64, 6, 58, 4, "clearKeyboardTimeout"], [64, 26, 58, 24], [64, 27, 58, 25], [64, 28, 58, 26], [66, 6, 60, 4], [67, 6, 61, 4], [67, 10, 61, 10, "input"], [67, 15, 61, 15], [67, 18, 61, 18, "previouslyFocusedTextInputRef"], [67, 47, 61, 47], [67, 48, 61, 48, "current"], [67, 55, 61, 55], [68, 6, 62, 4], [68, 10, 62, 8, "input"], [68, 15, 62, 13], [68, 17, 62, 15], [69, 8, 63, 6], [71, 8, 65, 6], [72, 8, 66, 6], [73, 8, 67, 6], [74, 8, 68, 6], [75, 8, 69, 6], [76, 8, 70, 6], [76, 12, 70, 10, "Date"], [76, 16, 70, 14], [76, 17, 70, 15, "now"], [76, 20, 70, 18], [76, 21, 70, 19], [76, 22, 70, 20], [76, 25, 70, 23, "startTimestampRef"], [76, 42, 70, 40], [76, 43, 70, 41, "current"], [76, 50, 70, 48], [76, 53, 70, 51], [76, 56, 70, 54], [76, 58, 70, 56], [77, 10, 71, 8, "keyboardTimeoutRef"], [77, 28, 71, 26], [77, 29, 71, 27, "current"], [77, 36, 71, 34], [77, 39, 71, 37, "setTimeout"], [77, 49, 71, 47], [77, 50, 71, 48], [77, 56, 71, 54], [78, 12, 72, 10, "input"], [78, 17, 72, 15], [78, 19, 72, 17, "focus"], [78, 24, 72, 22], [78, 25, 72, 23], [78, 26, 72, 24], [79, 12, 73, 10, "previouslyFocusedTextInputRef"], [79, 41, 73, 39], [79, 42, 73, 40, "current"], [79, 49, 73, 47], [79, 52, 73, 50, "undefined"], [79, 61, 73, 59], [80, 10, 74, 8], [80, 11, 74, 9], [80, 13, 74, 11], [80, 16, 74, 14], [80, 17, 74, 15], [81, 8, 75, 6], [81, 9, 75, 7], [81, 15, 75, 13], [82, 10, 76, 8, "input"], [82, 15, 76, 13], [82, 17, 76, 15, "focus"], [82, 22, 76, 20], [82, 23, 76, 21], [82, 24, 76, 22], [83, 10, 77, 8, "previouslyFocusedTextInputRef"], [83, 39, 77, 37], [83, 40, 77, 38, "current"], [83, 47, 77, 45], [83, 50, 77, 48, "undefined"], [83, 59, 77, 57], [84, 8, 78, 6], [85, 6, 79, 4], [86, 4, 80, 2], [86, 5, 80, 3], [86, 7, 80, 5], [86, 8, 80, 6, "clearKeyboardTimeout"], [86, 28, 80, 26], [86, 30, 80, 28, "isEnabled"], [86, 39, 80, 37], [86, 40, 80, 38], [86, 41, 80, 39], [87, 4, 81, 2, "React"], [87, 9, 81, 7], [87, 10, 81, 8, "useEffect"], [87, 19, 81, 17], [87, 20, 81, 18], [87, 26, 81, 24], [88, 6, 82, 4], [88, 13, 82, 11], [88, 19, 82, 17, "clearKeyboardTimeout"], [88, 39, 82, 37], [88, 40, 82, 38], [88, 41, 82, 39], [89, 4, 83, 2], [89, 5, 83, 3], [89, 7, 83, 5], [89, 8, 83, 6, "clearKeyboardTimeout"], [89, 28, 83, 26], [89, 29, 83, 27], [89, 30, 83, 28], [90, 4, 84, 2], [90, 11, 84, 9], [91, 6, 85, 4, "onPageChangeStart"], [91, 23, 85, 21], [92, 6, 86, 4, "onPageChangeConfirm"], [92, 25, 86, 23], [93, 6, 87, 4, "onPageChangeCancel"], [94, 4, 88, 2], [94, 5, 88, 3], [95, 2, 89, 0], [96, 0, 89, 1], [96, 3]], "functionMap": {"names": ["<global>", "useKeyboardManager", "clearKeyboardTimeout", "onPageChangeStart", "onPageChangeConfirm", "onPageChangeCancel", "setTimeout$argument_0", "React.useEffect$argument_0", "<anonymous>"], "mappings": "AAA;OCI;iDCM;GDK;8CEC;GFe;gDGC;GHoB;+CIC;gDCiB;SDG;GJM;kBMC;WCC,4BD;GNC;CDM"}}, "type": "js/module"}]}