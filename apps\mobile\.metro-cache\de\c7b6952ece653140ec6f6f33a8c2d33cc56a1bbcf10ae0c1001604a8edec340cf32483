{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var HMRClientProdShim = {\n    setup() {},\n    enable() {\n      console.error('Fast Refresh is disabled in JavaScript bundles built in production mode. ' + 'Did you forget to run Metro?');\n    },\n    disable() {},\n    registerBundle() {},\n    log() {}\n  };\n  var _default = exports.default = HMRClientProdShim;\n});", "lineCount": 18, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 17, 0], [8, 6, 17, 6, "HMRClientProdShim"], [8, 23, 17, 49], [8, 26, 17, 52], [9, 4, 18, 2, "setup"], [9, 9, 18, 7, "setup"], [9, 10, 18, 7], [9, 12, 18, 10], [9, 13, 18, 11], [9, 14, 18, 12], [10, 4, 19, 2, "enable"], [10, 10, 19, 8, "enable"], [10, 11, 19, 8], [10, 13, 19, 11], [11, 6, 20, 4, "console"], [11, 13, 20, 11], [11, 14, 20, 12, "error"], [11, 19, 20, 17], [11, 20, 21, 6], [11, 95, 21, 81], [11, 98, 22, 8], [11, 128, 23, 4], [11, 129, 23, 5], [12, 4, 24, 2], [12, 5, 24, 3], [13, 4, 25, 2, "disable"], [13, 11, 25, 9, "disable"], [13, 12, 25, 9], [13, 14, 25, 12], [13, 15, 25, 13], [13, 16, 25, 14], [14, 4, 26, 2, "registerBundle"], [14, 18, 26, 16, "registerBundle"], [14, 19, 26, 16], [14, 21, 26, 19], [14, 22, 26, 20], [14, 23, 26, 21], [15, 4, 27, 2, "log"], [15, 7, 27, 5, "log"], [15, 8, 27, 5], [15, 10, 27, 8], [15, 11, 27, 9], [16, 2, 28, 0], [16, 3, 28, 1], [17, 2, 28, 2], [17, 6, 28, 2, "_default"], [17, 14, 28, 2], [17, 17, 28, 2, "exports"], [17, 24, 28, 2], [17, 25, 28, 2, "default"], [17, 32, 28, 2], [17, 35, 30, 15, "HMRClientProdShim"], [17, 52, 30, 32], [18, 0, 30, 32], [18, 3]], "functionMap": {"names": ["<global>", "setup", "enable", "disable", "registerBundle", "log"], "mappings": "AAA;ECiB,UD;EEC;GFK;EGC,YH;EIC,mBJ;EKC,QL"}}, "type": "js/module"}]}