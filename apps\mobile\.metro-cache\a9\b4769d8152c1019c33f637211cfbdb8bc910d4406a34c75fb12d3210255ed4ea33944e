{"dependencies": [{"name": "./setPrototypeOf.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 21, "index": 21}, "end": {"line": 1, "column": 51, "index": 51}}], "key": "SqKbVhpPIT7m6Gx70N8rif/ceME=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var setPrototypeOf = require(_dependencyMap[0], \"./setPrototypeOf.js\");\n  function _inherits(t, e) {\n    if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n    t.prototype = Object.create(e && e.prototype, {\n      constructor: {\n        value: t,\n        writable: !0,\n        configurable: !0\n      }\n    }), Object.defineProperty(t, \"prototype\", {\n      writable: !1\n    }), e && setPrototypeOf(t, e);\n  }\n  module.exports = _inherits, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 16, "map": [[2, 2, 1, 0], [2, 6, 1, 4, "setPrototypeOf"], [2, 20, 1, 18], [2, 23, 1, 21, "require"], [2, 30, 1, 28], [2, 31, 1, 28, "_dependencyMap"], [2, 45, 1, 28], [2, 71, 1, 50], [2, 72, 1, 51], [3, 2, 2, 0], [3, 11, 2, 9, "_inherits"], [3, 20, 2, 18, "_inherits"], [3, 21, 2, 19, "t"], [3, 22, 2, 20], [3, 24, 2, 22, "e"], [3, 25, 2, 23], [3, 27, 2, 25], [4, 4, 3, 2], [4, 8, 3, 6], [4, 18, 3, 16], [4, 22, 3, 20], [4, 29, 3, 27, "e"], [4, 30, 3, 28], [4, 34, 3, 32], [4, 38, 3, 36], [4, 43, 3, 41, "e"], [4, 44, 3, 42], [4, 46, 3, 44], [4, 52, 3, 50], [4, 56, 3, 54, "TypeError"], [4, 65, 3, 63], [4, 66, 3, 64], [4, 118, 3, 116], [4, 119, 3, 117], [5, 4, 4, 2, "t"], [5, 5, 4, 3], [5, 6, 4, 4, "prototype"], [5, 15, 4, 13], [5, 18, 4, 16, "Object"], [5, 24, 4, 22], [5, 25, 4, 23, "create"], [5, 31, 4, 29], [5, 32, 4, 30, "e"], [5, 33, 4, 31], [5, 37, 4, 35, "e"], [5, 38, 4, 36], [5, 39, 4, 37, "prototype"], [5, 48, 4, 46], [5, 50, 4, 48], [6, 6, 5, 4, "constructor"], [6, 17, 5, 15], [6, 19, 5, 17], [7, 8, 6, 6, "value"], [7, 13, 6, 11], [7, 15, 6, 13, "t"], [7, 16, 6, 14], [8, 8, 7, 6, "writable"], [8, 16, 7, 14], [8, 18, 7, 16], [8, 19, 7, 17], [8, 20, 7, 18], [9, 8, 8, 6, "configurable"], [9, 20, 8, 18], [9, 22, 8, 20], [9, 23, 8, 21], [10, 6, 9, 4], [11, 4, 10, 2], [11, 5, 10, 3], [11, 6, 10, 4], [11, 8, 10, 6, "Object"], [11, 14, 10, 12], [11, 15, 10, 13, "defineProperty"], [11, 29, 10, 27], [11, 30, 10, 28, "t"], [11, 31, 10, 29], [11, 33, 10, 31], [11, 44, 10, 42], [11, 46, 10, 44], [12, 6, 11, 4, "writable"], [12, 14, 11, 12], [12, 16, 11, 14], [12, 17, 11, 15], [13, 4, 12, 2], [13, 5, 12, 3], [13, 6, 12, 4], [13, 8, 12, 6, "e"], [13, 9, 12, 7], [13, 13, 12, 11, "setPrototypeOf"], [13, 27, 12, 25], [13, 28, 12, 26, "t"], [13, 29, 12, 27], [13, 31, 12, 29, "e"], [13, 32, 12, 30], [13, 33, 12, 31], [14, 2, 13, 0], [15, 2, 14, 0, "module"], [15, 8, 14, 6], [15, 9, 14, 7, "exports"], [15, 16, 14, 14], [15, 19, 14, 17, "_inherits"], [15, 28, 14, 26], [15, 30, 14, 28, "module"], [15, 36, 14, 34], [15, 37, 14, 35, "exports"], [15, 44, 14, 42], [15, 45, 14, 43, "__esModule"], [15, 55, 14, 53], [15, 58, 14, 56], [15, 62, 14, 60], [15, 64, 14, 62, "module"], [15, 70, 14, 68], [15, 71, 14, 69, "exports"], [15, 78, 14, 76], [15, 79, 14, 77], [15, 88, 14, 86], [15, 89, 14, 87], [15, 92, 14, 90, "module"], [15, 98, 14, 96], [15, 99, 14, 97, "exports"], [15, 106, 14, 104], [16, 0, 14, 105], [16, 3]], "functionMap": {"names": ["<global>", "_inherits"], "mappings": "AAA;ACC;CDW"}}, "type": "js/module"}]}