{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 28}, "end": {"line": 9, "column": 22, "index": 135}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 137}, "end": {"line": 10, "column": 62, "index": 199}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo/vector-icons", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 201}, "end": {"line": 11, "column": 46, "index": 247}}], "key": "ow7vkrqkIckRjlSi/+MhMmRYtUE=", "exportNames": ["*"]}}, {"name": "../components/FooterNavigation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 249}, "end": {"line": 12, "column": 62, "index": 311}}], "key": "i0wsjvqzeC9AyK9fcghZiqZFu6Y=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = AccountScreen;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[4], \"react-native-safe-area-context\");\n  var _vectorIcons = require(_dependencyMap[5], \"@expo/vector-icons\");\n  var _FooterNavigation = _interopRequireDefault(require(_dependencyMap[6], \"../components/FooterNavigation\"));\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\apps\\\\mobile\\\\src\\\\screens\\\\AccountScreen.tsx\",\n    _s = $RefreshSig$();\n  function AccountScreen(_ref) {\n    _s();\n    var navigation = _ref.navigation;\n    var _React$useState = _react.default.useState(true),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      notificationsEnabled = _React$useState2[0],\n      setNotificationsEnabled = _React$useState2[1];\n    var _React$useState3 = _react.default.useState(true),\n      _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),\n      locationEnabled = _React$useState4[0],\n      setLocationEnabled = _React$useState4[1];\n    var menuItems = [{\n      section: 'Account',\n      items: [{\n        icon: 'person-outline',\n        title: 'Edit Profile',\n        subtitle: 'Update your personal information'\n      }, {\n        icon: 'location-outline',\n        title: 'Addresses',\n        subtitle: 'Manage delivery addresses'\n      }, {\n        icon: 'card-outline',\n        title: 'Payment Methods',\n        subtitle: 'Add or edit payment options'\n      }]\n    }, {\n      section: 'Orders',\n      items: [{\n        icon: 'receipt-outline',\n        title: 'Order History',\n        subtitle: 'View your past orders'\n      }, {\n        icon: 'heart-outline',\n        title: 'Favorites',\n        subtitle: 'Your favorite restaurants and dishes'\n      }, {\n        icon: 'star-outline',\n        title: 'Reviews',\n        subtitle: 'Rate and review your orders'\n      }]\n    }, {\n      section: 'Support',\n      items: [{\n        icon: 'help-circle-outline',\n        title: 'Help Center',\n        subtitle: 'Get answers to common questions'\n      }, {\n        icon: 'chatbubble-outline',\n        title: 'Contact Support',\n        subtitle: 'Chat with our support team'\n      }, {\n        icon: 'document-text-outline',\n        title: 'Terms & Privacy',\n        subtitle: 'Read our policies'\n      }]\n    }];\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: {\n        flex: 1,\n        backgroundColor: '#f9fafb'\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f3a823'\n        },\n        edges: ['top']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: {\n          flex: 1,\n          backgroundColor: '#f9fafb'\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            paddingHorizontal: 16,\n            paddingVertical: 12,\n            backgroundColor: '#fff',\n            borderBottomWidth: 1,\n            borderBottomColor: '#e5e7eb'\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 24,\n                fontWeight: 'bold',\n                color: '#111827'\n              },\n              children: \"Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 11\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                name: \"settings-outline\",\n                size: 24,\n                color: \"#6b7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 13\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.ScrollView, {\n          style: {\n            flex: 1\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              backgroundColor: '#fff',\n              margin: 16,\n              borderRadius: 16,\n              padding: 20,\n              shadowColor: '#000',\n              shadowOffset: {\n                width: 0,\n                height: 2\n              },\n              shadowOpacity: 0.1,\n              shadowRadius: 4,\n              elevation: 3\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: {\n                flexDirection: 'row',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Image, {\n                source: {\n                  uri: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400'\n                },\n                style: {\n                  width: 80,\n                  height: 80,\n                  borderRadius: 40,\n                  marginRight: 16\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 13\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    fontSize: 20,\n                    fontWeight: 'bold',\n                    color: '#111827',\n                    marginBottom: 4\n                  },\n                  children: \"John Doe\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    color: '#6b7280',\n                    fontSize: 14,\n                    marginBottom: 8\n                  },\n                  children: \"<EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                  style: {\n                    flexDirection: 'row',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                    name: \"star\",\n                    size: 16,\n                    color: \"#fbbf24\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 101,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                    style: {\n                      marginLeft: 4,\n                      color: '#6b7280',\n                      fontSize: 14\n                    },\n                    children: \"4.9 \\u2022 127 orders\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 13\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n                style: {\n                  backgroundColor: '#f97316',\n                  paddingHorizontal: 16,\n                  paddingVertical: 8,\n                  borderRadius: 20\n                },\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    color: '#fff',\n                    fontWeight: '600',\n                    fontSize: 14\n                  },\n                  children: \"Gold\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 9\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              backgroundColor: '#fff',\n              marginHorizontal: 16,\n              marginBottom: 16,\n              borderRadius: 16,\n              padding: 20,\n              shadowColor: '#000',\n              shadowOffset: {\n                width: 0,\n                height: 2\n              },\n              shadowOpacity: 0.1,\n              shadowRadius: 4,\n              elevation: 3\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                fontWeight: 'bold',\n                color: '#111827',\n                marginBottom: 16\n              },\n              children: \"Your Stats\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 11\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: {\n                flexDirection: 'row',\n                justifyContent: 'space-around'\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: {\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    fontSize: 24,\n                    fontWeight: 'bold',\n                    color: '#f97316'\n                  },\n                  children: \"127\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    color: '#6b7280',\n                    fontSize: 12\n                  },\n                  children: \"Orders\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 13\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: {\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    fontSize: 24,\n                    fontWeight: 'bold',\n                    color: '#10b981'\n                  },\n                  children: \"$2,340\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    color: '#6b7280',\n                    fontSize: 12\n                  },\n                  children: \"Saved\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 13\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: {\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    fontSize: 24,\n                    fontWeight: 'bold',\n                    color: '#8b5cf6'\n                  },\n                  children: \"23\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    color: '#6b7280',\n                    fontSize: 12\n                  },\n                  children: \"Reviews\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 9\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              backgroundColor: '#fff',\n              marginHorizontal: 16,\n              marginBottom: 16,\n              borderRadius: 16,\n              padding: 20,\n              shadowColor: '#000',\n              shadowOffset: {\n                width: 0,\n                height: 2\n              },\n              shadowOpacity: 0.1,\n              shadowRadius: 4,\n              elevation: 3\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                fontWeight: 'bold',\n                color: '#111827',\n                marginBottom: 16\n              },\n              children: \"Preferences\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 11\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: {\n                flexDirection: 'row',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                  name: \"notifications-outline\",\n                  size: 20,\n                  color: \"#6b7280\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    marginLeft: 12,\n                    fontSize: 16,\n                    color: '#111827'\n                  },\n                  children: \"Push Notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 13\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Switch, {\n                value: notificationsEnabled,\n                onValueChange: setNotificationsEnabled,\n                trackColor: {\n                  false: '#e5e7eb',\n                  true: '#f97316'\n                },\n                thumbColor: notificationsEnabled ? '#fff' : '#f4f3f4'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 11\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: {\n                flexDirection: 'row',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                  name: \"location-outline\",\n                  size: 20,\n                  color: \"#6b7280\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    marginLeft: 12,\n                    fontSize: 16,\n                    color: '#111827'\n                  },\n                  children: \"Location Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 13\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Switch, {\n                value: locationEnabled,\n                onValueChange: setLocationEnabled,\n                trackColor: {\n                  false: '#e5e7eb',\n                  true: '#f97316'\n                },\n                thumbColor: locationEnabled ? '#fff' : '#f4f3f4'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 9\n          }, this), menuItems.map((section, sectionIndex) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              backgroundColor: '#fff',\n              marginHorizontal: 16,\n              marginBottom: 16,\n              borderRadius: 16,\n              shadowColor: '#000',\n              shadowOffset: {\n                width: 0,\n                height: 2\n              },\n              shadowOpacity: 0.1,\n              shadowRadius: 4,\n              elevation: 3\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                fontWeight: 'bold',\n                color: '#111827',\n                padding: 20,\n                paddingBottom: 12\n              },\n              children: section.section\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 13\n            }, this), section.items.map((item, itemIndex) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              style: {\n                flexDirection: 'row',\n                alignItems: 'center',\n                paddingHorizontal: 20,\n                paddingVertical: 16,\n                borderTopWidth: itemIndex > 0 ? 1 : 0,\n                borderTopColor: '#f3f4f6'\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                name: item.icon,\n                size: 20,\n                color: \"#6b7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: {\n                  flex: 1,\n                  marginLeft: 12\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    fontSize: 16,\n                    color: '#111827',\n                    marginBottom: 2\n                  },\n                  children: item.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    fontSize: 14,\n                    color: '#6b7280'\n                  },\n                  children: item.subtitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                name: \"chevron-forward\",\n                size: 16,\n                color: \"#9ca3af\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, itemIndex, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this))]\n          }, sectionIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 11\n          }, this)), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n            style: {\n              backgroundColor: '#fff',\n              marginHorizontal: 16,\n              marginBottom: 32,\n              borderRadius: 16,\n              padding: 20,\n              shadowColor: '#000',\n              shadowOffset: {\n                width: 0,\n                height: 2\n              },\n              shadowOpacity: 0.1,\n              shadowRadius: 4,\n              elevation: 3,\n              alignItems: 'center'\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#ef4444',\n                fontWeight: '600'\n              },\n              children: \"Sign Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 9\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              height: 100\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_FooterNavigation.default, {\n        navigation: navigation,\n        activeScreen: \"Account\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f9fafb'\n        },\n        edges: ['bottom']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 5\n    }, this);\n  }\n  _s(AccountScreen, \"XgJ7BRJ3649eEM9xlvUUSN6rdwc=\");\n  _c = AccountScreen;\n  var _c;\n  $RefreshReg$(_c, \"AccountScreen\");\n});", "lineCount": 716, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireDefault"], [8, 37, 1, 0], [8, 38, 1, 0, "require"], [8, 45, 1, 0], [8, 46, 1, 0, "_dependencyMap"], [8, 60, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 10, 0], [10, 6, 10, 0, "_reactNativeSafeAreaContext"], [10, 33, 10, 0], [10, 36, 10, 0, "require"], [10, 43, 10, 0], [10, 44, 10, 0, "_dependencyMap"], [10, 58, 10, 0], [11, 2, 11, 0], [11, 6, 11, 0, "_vectorIcons"], [11, 18, 11, 0], [11, 21, 11, 0, "require"], [11, 28, 11, 0], [11, 29, 11, 0, "_dependencyMap"], [11, 43, 11, 0], [12, 2, 12, 0], [12, 6, 12, 0, "_FooterNavigation"], [12, 23, 12, 0], [12, 26, 12, 0, "_interopRequireDefault"], [12, 48, 12, 0], [12, 49, 12, 0, "require"], [12, 56, 12, 0], [12, 57, 12, 0, "_dependencyMap"], [12, 71, 12, 0], [13, 2, 12, 62], [13, 6, 12, 62, "_jsxDevRuntime"], [13, 20, 12, 62], [13, 23, 12, 62, "require"], [13, 30, 12, 62], [13, 31, 12, 62, "_dependencyMap"], [13, 45, 12, 62], [14, 2, 12, 62], [14, 6, 12, 62, "_jsxFileName"], [14, 18, 12, 62], [15, 4, 12, 62, "_s"], [15, 6, 12, 62], [15, 9, 12, 62, "$RefreshSig$"], [15, 21, 12, 62], [16, 2, 14, 15], [16, 11, 14, 24, "AccountScreen"], [16, 24, 14, 37, "AccountScreen"], [16, 25, 14, 37, "_ref"], [16, 29, 14, 37], [16, 31, 14, 59], [17, 4, 14, 59, "_s"], [17, 6, 14, 59], [18, 4, 14, 59], [18, 8, 14, 40, "navigation"], [18, 18, 14, 50], [18, 21, 14, 50, "_ref"], [18, 25, 14, 50], [18, 26, 14, 40, "navigation"], [18, 36, 14, 50], [19, 4, 15, 2], [19, 8, 15, 2, "_React$useState"], [19, 23, 15, 2], [19, 26, 15, 58, "React"], [19, 40, 15, 63], [19, 41, 15, 64, "useState"], [19, 49, 15, 72], [19, 50, 15, 73], [19, 54, 15, 77], [19, 55, 15, 78], [20, 6, 15, 78, "_React$useState2"], [20, 22, 15, 78], [20, 29, 15, 78, "_slicedToArray2"], [20, 44, 15, 78], [20, 45, 15, 78, "default"], [20, 52, 15, 78], [20, 54, 15, 78, "_React$useState"], [20, 69, 15, 78], [21, 6, 15, 9, "notificationsEnabled"], [21, 26, 15, 29], [21, 29, 15, 29, "_React$useState2"], [21, 45, 15, 29], [22, 6, 15, 31, "setNotificationsEnabled"], [22, 29, 15, 54], [22, 32, 15, 54, "_React$useState2"], [22, 48, 15, 54], [23, 4, 16, 2], [23, 8, 16, 2, "_React$useState3"], [23, 24, 16, 2], [23, 27, 16, 48, "React"], [23, 41, 16, 53], [23, 42, 16, 54, "useState"], [23, 50, 16, 62], [23, 51, 16, 63], [23, 55, 16, 67], [23, 56, 16, 68], [24, 6, 16, 68, "_React$useState4"], [24, 22, 16, 68], [24, 29, 16, 68, "_slicedToArray2"], [24, 44, 16, 68], [24, 45, 16, 68, "default"], [24, 52, 16, 68], [24, 54, 16, 68, "_React$useState3"], [24, 70, 16, 68], [25, 6, 16, 9, "locationEnabled"], [25, 21, 16, 24], [25, 24, 16, 24, "_React$useState4"], [25, 40, 16, 24], [26, 6, 16, 26, "setLocationEnabled"], [26, 24, 16, 44], [26, 27, 16, 44, "_React$useState4"], [26, 43, 16, 44], [27, 4, 18, 2], [27, 8, 18, 8, "menuItems"], [27, 17, 18, 17], [27, 20, 18, 20], [27, 21, 19, 4], [28, 6, 20, 6, "section"], [28, 13, 20, 13], [28, 15, 20, 15], [28, 24, 20, 24], [29, 6, 21, 6, "items"], [29, 11, 21, 11], [29, 13, 21, 13], [29, 14, 22, 8], [30, 8, 22, 10, "icon"], [30, 12, 22, 14], [30, 14, 22, 16], [30, 30, 22, 32], [31, 8, 22, 34, "title"], [31, 13, 22, 39], [31, 15, 22, 41], [31, 29, 22, 55], [32, 8, 22, 57, "subtitle"], [32, 16, 22, 65], [32, 18, 22, 67], [33, 6, 22, 102], [33, 7, 22, 103], [33, 9, 23, 8], [34, 8, 23, 10, "icon"], [34, 12, 23, 14], [34, 14, 23, 16], [34, 32, 23, 34], [35, 8, 23, 36, "title"], [35, 13, 23, 41], [35, 15, 23, 43], [35, 26, 23, 54], [36, 8, 23, 56, "subtitle"], [36, 16, 23, 64], [36, 18, 23, 66], [37, 6, 23, 94], [37, 7, 23, 95], [37, 9, 24, 8], [38, 8, 24, 10, "icon"], [38, 12, 24, 14], [38, 14, 24, 16], [38, 28, 24, 30], [39, 8, 24, 32, "title"], [39, 13, 24, 37], [39, 15, 24, 39], [39, 32, 24, 56], [40, 8, 24, 58, "subtitle"], [40, 16, 24, 66], [40, 18, 24, 68], [41, 6, 24, 98], [41, 7, 24, 99], [42, 4, 26, 4], [42, 5, 26, 5], [42, 7, 27, 4], [43, 6, 28, 6, "section"], [43, 13, 28, 13], [43, 15, 28, 15], [43, 23, 28, 23], [44, 6, 29, 6, "items"], [44, 11, 29, 11], [44, 13, 29, 13], [44, 14, 30, 8], [45, 8, 30, 10, "icon"], [45, 12, 30, 14], [45, 14, 30, 16], [45, 31, 30, 33], [46, 8, 30, 35, "title"], [46, 13, 30, 40], [46, 15, 30, 42], [46, 30, 30, 57], [47, 8, 30, 59, "subtitle"], [47, 16, 30, 67], [47, 18, 30, 69], [48, 6, 30, 93], [48, 7, 30, 94], [48, 9, 31, 8], [49, 8, 31, 10, "icon"], [49, 12, 31, 14], [49, 14, 31, 16], [49, 29, 31, 31], [50, 8, 31, 33, "title"], [50, 13, 31, 38], [50, 15, 31, 40], [50, 26, 31, 51], [51, 8, 31, 53, "subtitle"], [51, 16, 31, 61], [51, 18, 31, 63], [52, 6, 31, 102], [52, 7, 31, 103], [52, 9, 32, 8], [53, 8, 32, 10, "icon"], [53, 12, 32, 14], [53, 14, 32, 16], [53, 28, 32, 30], [54, 8, 32, 32, "title"], [54, 13, 32, 37], [54, 15, 32, 39], [54, 24, 32, 48], [55, 8, 32, 50, "subtitle"], [55, 16, 32, 58], [55, 18, 32, 60], [56, 6, 32, 90], [56, 7, 32, 91], [57, 4, 34, 4], [57, 5, 34, 5], [57, 7, 35, 4], [58, 6, 36, 6, "section"], [58, 13, 36, 13], [58, 15, 36, 15], [58, 24, 36, 24], [59, 6, 37, 6, "items"], [59, 11, 37, 11], [59, 13, 37, 13], [59, 14, 38, 8], [60, 8, 38, 10, "icon"], [60, 12, 38, 14], [60, 14, 38, 16], [60, 35, 38, 37], [61, 8, 38, 39, "title"], [61, 13, 38, 44], [61, 15, 38, 46], [61, 28, 38, 59], [62, 8, 38, 61, "subtitle"], [62, 16, 38, 69], [62, 18, 38, 71], [63, 6, 38, 105], [63, 7, 38, 106], [63, 9, 39, 8], [64, 8, 39, 10, "icon"], [64, 12, 39, 14], [64, 14, 39, 16], [64, 34, 39, 36], [65, 8, 39, 38, "title"], [65, 13, 39, 43], [65, 15, 39, 45], [65, 32, 39, 62], [66, 8, 39, 64, "subtitle"], [66, 16, 39, 72], [66, 18, 39, 74], [67, 6, 39, 103], [67, 7, 39, 104], [67, 9, 40, 8], [68, 8, 40, 10, "icon"], [68, 12, 40, 14], [68, 14, 40, 16], [68, 37, 40, 39], [69, 8, 40, 41, "title"], [69, 13, 40, 46], [69, 15, 40, 48], [69, 32, 40, 65], [70, 8, 40, 67, "subtitle"], [70, 16, 40, 75], [70, 18, 40, 77], [71, 6, 40, 97], [71, 7, 40, 98], [72, 4, 42, 4], [72, 5, 42, 5], [72, 6, 43, 3], [73, 4, 45, 2], [73, 24, 46, 4], [73, 28, 46, 4, "_jsxDevRuntime"], [73, 42, 46, 4], [73, 43, 46, 4, "jsxDEV"], [73, 49, 46, 4], [73, 51, 46, 5, "_reactNative"], [73, 63, 46, 5], [73, 64, 46, 5, "View"], [73, 68, 46, 9], [74, 6, 46, 10, "style"], [74, 11, 46, 15], [74, 13, 46, 17], [75, 8, 46, 19, "flex"], [75, 12, 46, 23], [75, 14, 46, 25], [75, 15, 46, 26], [76, 8, 46, 28, "backgroundColor"], [76, 23, 46, 43], [76, 25, 46, 45], [77, 6, 46, 55], [77, 7, 46, 57], [78, 6, 46, 57, "children"], [78, 14, 46, 57], [78, 30, 48, 6], [78, 34, 48, 6, "_jsxDevRuntime"], [78, 48, 48, 6], [78, 49, 48, 6, "jsxDEV"], [78, 55, 48, 6], [78, 57, 48, 7, "_reactNativeSafeAreaContext"], [78, 84, 48, 7], [78, 85, 48, 7, "SafeAreaView"], [78, 97, 48, 19], [79, 8, 48, 20, "style"], [79, 13, 48, 25], [79, 15, 48, 27], [80, 10, 48, 29, "backgroundColor"], [80, 25, 48, 44], [80, 27, 48, 46], [81, 8, 48, 56], [81, 9, 48, 58], [82, 8, 48, 59, "edges"], [82, 13, 48, 64], [82, 15, 48, 66], [82, 16, 48, 67], [82, 21, 48, 72], [83, 6, 48, 74], [84, 8, 48, 74, "fileName"], [84, 16, 48, 74], [84, 18, 48, 74, "_jsxFileName"], [84, 30, 48, 74], [85, 8, 48, 74, "lineNumber"], [85, 18, 48, 74], [86, 8, 48, 74, "columnNumber"], [86, 20, 48, 74], [87, 6, 48, 74], [87, 13, 48, 76], [87, 14, 48, 77], [87, 29, 51, 6], [87, 33, 51, 6, "_jsxDevRuntime"], [87, 47, 51, 6], [87, 48, 51, 6, "jsxDEV"], [87, 54, 51, 6], [87, 56, 51, 7, "_reactNative"], [87, 68, 51, 7], [87, 69, 51, 7, "View"], [87, 73, 51, 11], [88, 8, 51, 12, "style"], [88, 13, 51, 17], [88, 15, 51, 19], [89, 10, 51, 21, "flex"], [89, 14, 51, 25], [89, 16, 51, 27], [89, 17, 51, 28], [90, 10, 51, 30, "backgroundColor"], [90, 25, 51, 45], [90, 27, 51, 47], [91, 8, 51, 57], [91, 9, 51, 59], [92, 8, 51, 59, "children"], [92, 16, 51, 59], [92, 32, 53, 8], [92, 36, 53, 8, "_jsxDevRuntime"], [92, 50, 53, 8], [92, 51, 53, 8, "jsxDEV"], [92, 57, 53, 8], [92, 59, 53, 9, "_reactNative"], [92, 71, 53, 9], [92, 72, 53, 9, "View"], [92, 76, 53, 13], [93, 10, 53, 14, "style"], [93, 15, 53, 19], [93, 17, 53, 21], [94, 12, 54, 10, "paddingHorizontal"], [94, 29, 54, 27], [94, 31, 54, 29], [94, 33, 54, 31], [95, 12, 55, 10, "paddingVertical"], [95, 27, 55, 25], [95, 29, 55, 27], [95, 31, 55, 29], [96, 12, 56, 10, "backgroundColor"], [96, 27, 56, 25], [96, 29, 56, 27], [96, 35, 56, 33], [97, 12, 57, 10, "borderBottomWidth"], [97, 29, 57, 27], [97, 31, 57, 29], [97, 32, 57, 30], [98, 12, 58, 10, "borderBottomColor"], [98, 29, 58, 27], [98, 31, 58, 29], [99, 10, 59, 8], [99, 11, 59, 10], [100, 10, 59, 10, "children"], [100, 18, 59, 10], [100, 33, 60, 8], [100, 37, 60, 8, "_jsxDevRuntime"], [100, 51, 60, 8], [100, 52, 60, 8, "jsxDEV"], [100, 58, 60, 8], [100, 60, 60, 9, "_reactNative"], [100, 72, 60, 9], [100, 73, 60, 9, "View"], [100, 77, 60, 13], [101, 12, 60, 14, "style"], [101, 17, 60, 19], [101, 19, 60, 21], [102, 14, 60, 23, "flexDirection"], [102, 27, 60, 36], [102, 29, 60, 38], [102, 34, 60, 43], [103, 14, 60, 45, "justifyContent"], [103, 28, 60, 59], [103, 30, 60, 61], [103, 45, 60, 76], [104, 14, 60, 78, "alignItems"], [104, 24, 60, 88], [104, 26, 60, 90], [105, 12, 60, 99], [105, 13, 60, 101], [106, 12, 60, 101, "children"], [106, 20, 60, 101], [106, 36, 61, 10], [106, 40, 61, 10, "_jsxDevRuntime"], [106, 54, 61, 10], [106, 55, 61, 10, "jsxDEV"], [106, 61, 61, 10], [106, 63, 61, 11, "_reactNative"], [106, 75, 61, 11], [106, 76, 61, 11, "Text"], [106, 80, 61, 15], [107, 14, 61, 16, "style"], [107, 19, 61, 21], [107, 21, 61, 23], [108, 16, 61, 25, "fontSize"], [108, 24, 61, 33], [108, 26, 61, 35], [108, 28, 61, 37], [109, 16, 61, 39, "fontWeight"], [109, 26, 61, 49], [109, 28, 61, 51], [109, 34, 61, 57], [110, 16, 61, 59, "color"], [110, 21, 61, 64], [110, 23, 61, 66], [111, 14, 61, 76], [111, 15, 61, 78], [112, 14, 61, 78, "children"], [112, 22, 61, 78], [112, 24, 61, 79], [113, 12, 63, 10], [114, 14, 63, 10, "fileName"], [114, 22, 63, 10], [114, 24, 63, 10, "_jsxFileName"], [114, 36, 63, 10], [115, 14, 63, 10, "lineNumber"], [115, 24, 63, 10], [116, 14, 63, 10, "columnNumber"], [116, 26, 63, 10], [117, 12, 63, 10], [117, 19, 63, 16], [117, 20, 63, 17], [117, 35, 64, 10], [117, 39, 64, 10, "_jsxDevRuntime"], [117, 53, 64, 10], [117, 54, 64, 10, "jsxDEV"], [117, 60, 64, 10], [117, 62, 64, 11, "_reactNative"], [117, 74, 64, 11], [117, 75, 64, 11, "TouchableOpacity"], [117, 91, 64, 27], [118, 14, 64, 27, "children"], [118, 22, 64, 27], [118, 37, 65, 12], [118, 41, 65, 12, "_jsxDevRuntime"], [118, 55, 65, 12], [118, 56, 65, 12, "jsxDEV"], [118, 62, 65, 12], [118, 64, 65, 13, "_vectorIcons"], [118, 76, 65, 13], [118, 77, 65, 13, "Ionicons"], [118, 85, 65, 21], [119, 16, 65, 22, "name"], [119, 20, 65, 26], [119, 22, 65, 27], [119, 40, 65, 45], [120, 16, 65, 46, "size"], [120, 20, 65, 50], [120, 22, 65, 52], [120, 24, 65, 55], [121, 16, 65, 56, "color"], [121, 21, 65, 61], [121, 23, 65, 62], [122, 14, 65, 71], [123, 16, 65, 71, "fileName"], [123, 24, 65, 71], [123, 26, 65, 71, "_jsxFileName"], [123, 38, 65, 71], [124, 16, 65, 71, "lineNumber"], [124, 26, 65, 71], [125, 16, 65, 71, "columnNumber"], [125, 28, 65, 71], [126, 14, 65, 71], [126, 21, 65, 73], [127, 12, 65, 74], [128, 14, 65, 74, "fileName"], [128, 22, 65, 74], [128, 24, 65, 74, "_jsxFileName"], [128, 36, 65, 74], [129, 14, 65, 74, "lineNumber"], [129, 24, 65, 74], [130, 14, 65, 74, "columnNumber"], [130, 26, 65, 74], [131, 12, 65, 74], [131, 19, 66, 28], [131, 20, 66, 29], [132, 10, 66, 29], [133, 12, 66, 29, "fileName"], [133, 20, 66, 29], [133, 22, 66, 29, "_jsxFileName"], [133, 34, 66, 29], [134, 12, 66, 29, "lineNumber"], [134, 22, 66, 29], [135, 12, 66, 29, "columnNumber"], [135, 24, 66, 29], [136, 10, 66, 29], [136, 17, 67, 14], [137, 8, 67, 15], [138, 10, 67, 15, "fileName"], [138, 18, 67, 15], [138, 20, 67, 15, "_jsxFileName"], [138, 32, 67, 15], [139, 10, 67, 15, "lineNumber"], [139, 20, 67, 15], [140, 10, 67, 15, "columnNumber"], [140, 22, 67, 15], [141, 8, 67, 15], [141, 15, 68, 12], [141, 16, 68, 13], [141, 31, 70, 6], [141, 35, 70, 6, "_jsxDevRuntime"], [141, 49, 70, 6], [141, 50, 70, 6, "jsxDEV"], [141, 56, 70, 6], [141, 58, 70, 7, "_reactNative"], [141, 70, 70, 7], [141, 71, 70, 7, "ScrollView"], [141, 81, 70, 17], [142, 10, 70, 18, "style"], [142, 15, 70, 23], [142, 17, 70, 25], [143, 12, 70, 27, "flex"], [143, 16, 70, 31], [143, 18, 70, 33], [144, 10, 70, 35], [144, 11, 70, 37], [145, 10, 70, 37, "children"], [145, 18, 70, 37], [145, 34, 72, 8], [145, 38, 72, 8, "_jsxDevRuntime"], [145, 52, 72, 8], [145, 53, 72, 8, "jsxDEV"], [145, 59, 72, 8], [145, 61, 72, 9, "_reactNative"], [145, 73, 72, 9], [145, 74, 72, 9, "View"], [145, 78, 72, 13], [146, 12, 72, 14, "style"], [146, 17, 72, 19], [146, 19, 72, 21], [147, 14, 73, 10, "backgroundColor"], [147, 29, 73, 25], [147, 31, 73, 27], [147, 37, 73, 33], [148, 14, 74, 10, "margin"], [148, 20, 74, 16], [148, 22, 74, 18], [148, 24, 74, 20], [149, 14, 75, 10, "borderRadius"], [149, 26, 75, 22], [149, 28, 75, 24], [149, 30, 75, 26], [150, 14, 76, 10, "padding"], [150, 21, 76, 17], [150, 23, 76, 19], [150, 25, 76, 21], [151, 14, 77, 10, "shadowColor"], [151, 25, 77, 21], [151, 27, 77, 23], [151, 33, 77, 29], [152, 14, 78, 10, "shadowOffset"], [152, 26, 78, 22], [152, 28, 78, 24], [153, 16, 78, 26, "width"], [153, 21, 78, 31], [153, 23, 78, 33], [153, 24, 78, 34], [154, 16, 78, 36, "height"], [154, 22, 78, 42], [154, 24, 78, 44], [155, 14, 78, 46], [155, 15, 78, 47], [156, 14, 79, 10, "shadowOpacity"], [156, 27, 79, 23], [156, 29, 79, 25], [156, 32, 79, 28], [157, 14, 80, 10, "shadowRadius"], [157, 26, 80, 22], [157, 28, 80, 24], [157, 29, 80, 25], [158, 14, 81, 10, "elevation"], [158, 23, 81, 19], [158, 25, 81, 21], [159, 12, 82, 8], [159, 13, 82, 10], [160, 12, 82, 10, "children"], [160, 20, 82, 10], [160, 35, 83, 10], [160, 39, 83, 10, "_jsxDevRuntime"], [160, 53, 83, 10], [160, 54, 83, 10, "jsxDEV"], [160, 60, 83, 10], [160, 62, 83, 11, "_reactNative"], [160, 74, 83, 11], [160, 75, 83, 11, "View"], [160, 79, 83, 15], [161, 14, 83, 16, "style"], [161, 19, 83, 21], [161, 21, 83, 23], [162, 16, 83, 25, "flexDirection"], [162, 29, 83, 38], [162, 31, 83, 40], [162, 36, 83, 45], [163, 16, 83, 47, "alignItems"], [163, 26, 83, 57], [163, 28, 83, 59], [164, 14, 83, 68], [164, 15, 83, 70], [165, 14, 83, 70, "children"], [165, 22, 83, 70], [165, 38, 84, 12], [165, 42, 84, 12, "_jsxDevRuntime"], [165, 56, 84, 12], [165, 57, 84, 12, "jsxDEV"], [165, 63, 84, 12], [165, 65, 84, 13, "_reactNative"], [165, 77, 84, 13], [165, 78, 84, 13, "Image"], [165, 83, 84, 18], [166, 16, 85, 14, "source"], [166, 22, 85, 20], [166, 24, 85, 22], [167, 18, 85, 24, "uri"], [167, 21, 85, 27], [167, 23, 85, 29], [168, 16, 85, 98], [168, 17, 85, 100], [169, 16, 86, 14, "style"], [169, 21, 86, 19], [169, 23, 86, 21], [170, 18, 87, 16, "width"], [170, 23, 87, 21], [170, 25, 87, 23], [170, 27, 87, 25], [171, 18, 88, 16, "height"], [171, 24, 88, 22], [171, 26, 88, 24], [171, 28, 88, 26], [172, 18, 89, 16, "borderRadius"], [172, 30, 89, 28], [172, 32, 89, 30], [172, 34, 89, 32], [173, 18, 90, 16, "marginRight"], [173, 29, 90, 27], [173, 31, 90, 29], [174, 16, 91, 14], [175, 14, 91, 16], [176, 16, 91, 16, "fileName"], [176, 24, 91, 16], [176, 26, 91, 16, "_jsxFileName"], [176, 38, 91, 16], [177, 16, 91, 16, "lineNumber"], [177, 26, 91, 16], [178, 16, 91, 16, "columnNumber"], [178, 28, 91, 16], [179, 14, 91, 16], [179, 21, 92, 13], [179, 22, 92, 14], [179, 37, 93, 12], [179, 41, 93, 12, "_jsxDevRuntime"], [179, 55, 93, 12], [179, 56, 93, 12, "jsxDEV"], [179, 62, 93, 12], [179, 64, 93, 13, "_reactNative"], [179, 76, 93, 13], [179, 77, 93, 13, "View"], [179, 81, 93, 17], [180, 16, 93, 18, "style"], [180, 21, 93, 23], [180, 23, 93, 25], [181, 18, 93, 27, "flex"], [181, 22, 93, 31], [181, 24, 93, 33], [182, 16, 93, 35], [182, 17, 93, 37], [183, 16, 93, 37, "children"], [183, 24, 93, 37], [183, 40, 94, 14], [183, 44, 94, 14, "_jsxDevRuntime"], [183, 58, 94, 14], [183, 59, 94, 14, "jsxDEV"], [183, 65, 94, 14], [183, 67, 94, 15, "_reactNative"], [183, 79, 94, 15], [183, 80, 94, 15, "Text"], [183, 84, 94, 19], [184, 18, 94, 20, "style"], [184, 23, 94, 25], [184, 25, 94, 27], [185, 20, 94, 29, "fontSize"], [185, 28, 94, 37], [185, 30, 94, 39], [185, 32, 94, 41], [186, 20, 94, 43, "fontWeight"], [186, 30, 94, 53], [186, 32, 94, 55], [186, 38, 94, 61], [187, 20, 94, 63, "color"], [187, 25, 94, 68], [187, 27, 94, 70], [187, 36, 94, 79], [188, 20, 94, 81, "marginBottom"], [188, 32, 94, 93], [188, 34, 94, 95], [189, 18, 94, 97], [189, 19, 94, 99], [190, 18, 94, 99, "children"], [190, 26, 94, 99], [190, 28, 94, 100], [191, 16, 96, 14], [192, 18, 96, 14, "fileName"], [192, 26, 96, 14], [192, 28, 96, 14, "_jsxFileName"], [192, 40, 96, 14], [193, 18, 96, 14, "lineNumber"], [193, 28, 96, 14], [194, 18, 96, 14, "columnNumber"], [194, 30, 96, 14], [195, 16, 96, 14], [195, 23, 96, 20], [195, 24, 96, 21], [195, 39, 97, 14], [195, 43, 97, 14, "_jsxDevRuntime"], [195, 57, 97, 14], [195, 58, 97, 14, "jsxDEV"], [195, 64, 97, 14], [195, 66, 97, 15, "_reactNative"], [195, 78, 97, 15], [195, 79, 97, 15, "Text"], [195, 83, 97, 19], [196, 18, 97, 20, "style"], [196, 23, 97, 25], [196, 25, 97, 27], [197, 20, 97, 29, "color"], [197, 25, 97, 34], [197, 27, 97, 36], [197, 36, 97, 45], [198, 20, 97, 47, "fontSize"], [198, 28, 97, 55], [198, 30, 97, 57], [198, 32, 97, 59], [199, 20, 97, 61, "marginBottom"], [199, 32, 97, 73], [199, 34, 97, 75], [200, 18, 97, 77], [200, 19, 97, 79], [201, 18, 97, 79, "children"], [201, 26, 97, 79], [201, 28, 97, 80], [202, 16, 99, 14], [203, 18, 99, 14, "fileName"], [203, 26, 99, 14], [203, 28, 99, 14, "_jsxFileName"], [203, 40, 99, 14], [204, 18, 99, 14, "lineNumber"], [204, 28, 99, 14], [205, 18, 99, 14, "columnNumber"], [205, 30, 99, 14], [206, 16, 99, 14], [206, 23, 99, 20], [206, 24, 99, 21], [206, 39, 100, 14], [206, 43, 100, 14, "_jsxDevRuntime"], [206, 57, 100, 14], [206, 58, 100, 14, "jsxDEV"], [206, 64, 100, 14], [206, 66, 100, 15, "_reactNative"], [206, 78, 100, 15], [206, 79, 100, 15, "View"], [206, 83, 100, 19], [207, 18, 100, 20, "style"], [207, 23, 100, 25], [207, 25, 100, 27], [208, 20, 100, 29, "flexDirection"], [208, 33, 100, 42], [208, 35, 100, 44], [208, 40, 100, 49], [209, 20, 100, 51, "alignItems"], [209, 30, 100, 61], [209, 32, 100, 63], [210, 18, 100, 72], [210, 19, 100, 74], [211, 18, 100, 74, "children"], [211, 26, 100, 74], [211, 42, 101, 16], [211, 46, 101, 16, "_jsxDevRuntime"], [211, 60, 101, 16], [211, 61, 101, 16, "jsxDEV"], [211, 67, 101, 16], [211, 69, 101, 17, "_vectorIcons"], [211, 81, 101, 17], [211, 82, 101, 17, "Ionicons"], [211, 90, 101, 25], [212, 20, 101, 26, "name"], [212, 24, 101, 30], [212, 26, 101, 31], [212, 32, 101, 37], [213, 20, 101, 38, "size"], [213, 24, 101, 42], [213, 26, 101, 44], [213, 28, 101, 47], [214, 20, 101, 48, "color"], [214, 25, 101, 53], [214, 27, 101, 54], [215, 18, 101, 63], [216, 20, 101, 63, "fileName"], [216, 28, 101, 63], [216, 30, 101, 63, "_jsxFileName"], [216, 42, 101, 63], [217, 20, 101, 63, "lineNumber"], [217, 30, 101, 63], [218, 20, 101, 63, "columnNumber"], [218, 32, 101, 63], [219, 18, 101, 63], [219, 25, 101, 65], [219, 26, 101, 66], [219, 41, 102, 16], [219, 45, 102, 16, "_jsxDevRuntime"], [219, 59, 102, 16], [219, 60, 102, 16, "jsxDEV"], [219, 66, 102, 16], [219, 68, 102, 17, "_reactNative"], [219, 80, 102, 17], [219, 81, 102, 17, "Text"], [219, 85, 102, 21], [220, 20, 102, 22, "style"], [220, 25, 102, 27], [220, 27, 102, 29], [221, 22, 102, 31, "marginLeft"], [221, 32, 102, 41], [221, 34, 102, 43], [221, 35, 102, 44], [222, 22, 102, 46, "color"], [222, 27, 102, 51], [222, 29, 102, 53], [222, 38, 102, 62], [223, 22, 102, 64, "fontSize"], [223, 30, 102, 72], [223, 32, 102, 74], [224, 20, 102, 77], [224, 21, 102, 79], [225, 20, 102, 79, "children"], [225, 28, 102, 79], [225, 30, 102, 80], [226, 18, 104, 16], [227, 20, 104, 16, "fileName"], [227, 28, 104, 16], [227, 30, 104, 16, "_jsxFileName"], [227, 42, 104, 16], [228, 20, 104, 16, "lineNumber"], [228, 30, 104, 16], [229, 20, 104, 16, "columnNumber"], [229, 32, 104, 16], [230, 18, 104, 16], [230, 25, 104, 22], [230, 26, 104, 23], [231, 16, 104, 23], [232, 18, 104, 23, "fileName"], [232, 26, 104, 23], [232, 28, 104, 23, "_jsxFileName"], [232, 40, 104, 23], [233, 18, 104, 23, "lineNumber"], [233, 28, 104, 23], [234, 18, 104, 23, "columnNumber"], [234, 30, 104, 23], [235, 16, 104, 23], [235, 23, 105, 20], [235, 24, 105, 21], [236, 14, 105, 21], [237, 16, 105, 21, "fileName"], [237, 24, 105, 21], [237, 26, 105, 21, "_jsxFileName"], [237, 38, 105, 21], [238, 16, 105, 21, "lineNumber"], [238, 26, 105, 21], [239, 16, 105, 21, "columnNumber"], [239, 28, 105, 21], [240, 14, 105, 21], [240, 21, 106, 18], [240, 22, 106, 19], [240, 37, 107, 12], [240, 41, 107, 12, "_jsxDevRuntime"], [240, 55, 107, 12], [240, 56, 107, 12, "jsxDEV"], [240, 62, 107, 12], [240, 64, 107, 13, "_reactNative"], [240, 76, 107, 13], [240, 77, 107, 13, "TouchableOpacity"], [240, 93, 107, 29], [241, 16, 107, 30, "style"], [241, 21, 107, 35], [241, 23, 107, 37], [242, 18, 108, 14, "backgroundColor"], [242, 33, 108, 29], [242, 35, 108, 31], [242, 44, 108, 40], [243, 18, 109, 14, "paddingHorizontal"], [243, 35, 109, 31], [243, 37, 109, 33], [243, 39, 109, 35], [244, 18, 110, 14, "paddingVertical"], [244, 33, 110, 29], [244, 35, 110, 31], [244, 36, 110, 32], [245, 18, 111, 14, "borderRadius"], [245, 30, 111, 26], [245, 32, 111, 28], [246, 16, 112, 12], [246, 17, 112, 14], [247, 16, 112, 14, "children"], [247, 24, 112, 14], [247, 39, 113, 14], [247, 43, 113, 14, "_jsxDevRuntime"], [247, 57, 113, 14], [247, 58, 113, 14, "jsxDEV"], [247, 64, 113, 14], [247, 66, 113, 15, "_reactNative"], [247, 78, 113, 15], [247, 79, 113, 15, "Text"], [247, 83, 113, 19], [248, 18, 113, 20, "style"], [248, 23, 113, 25], [248, 25, 113, 27], [249, 20, 113, 29, "color"], [249, 25, 113, 34], [249, 27, 113, 36], [249, 33, 113, 42], [250, 20, 113, 44, "fontWeight"], [250, 30, 113, 54], [250, 32, 113, 56], [250, 37, 113, 61], [251, 20, 113, 63, "fontSize"], [251, 28, 113, 71], [251, 30, 113, 73], [252, 18, 113, 76], [252, 19, 113, 78], [253, 18, 113, 78, "children"], [253, 26, 113, 78], [253, 28, 113, 79], [254, 16, 115, 14], [255, 18, 115, 14, "fileName"], [255, 26, 115, 14], [255, 28, 115, 14, "_jsxFileName"], [255, 40, 115, 14], [256, 18, 115, 14, "lineNumber"], [256, 28, 115, 14], [257, 18, 115, 14, "columnNumber"], [257, 30, 115, 14], [258, 16, 115, 14], [258, 23, 115, 20], [259, 14, 115, 21], [260, 16, 115, 21, "fileName"], [260, 24, 115, 21], [260, 26, 115, 21, "_jsxFileName"], [260, 38, 115, 21], [261, 16, 115, 21, "lineNumber"], [261, 26, 115, 21], [262, 16, 115, 21, "columnNumber"], [262, 28, 115, 21], [263, 14, 115, 21], [263, 21, 116, 30], [263, 22, 116, 31], [264, 12, 116, 31], [265, 14, 116, 31, "fileName"], [265, 22, 116, 31], [265, 24, 116, 31, "_jsxFileName"], [265, 36, 116, 31], [266, 14, 116, 31, "lineNumber"], [266, 24, 116, 31], [267, 14, 116, 31, "columnNumber"], [267, 26, 116, 31], [268, 12, 116, 31], [268, 19, 117, 16], [269, 10, 117, 17], [270, 12, 117, 17, "fileName"], [270, 20, 117, 17], [270, 22, 117, 17, "_jsxFileName"], [270, 34, 117, 17], [271, 12, 117, 17, "lineNumber"], [271, 22, 117, 17], [272, 12, 117, 17, "columnNumber"], [272, 24, 117, 17], [273, 10, 117, 17], [273, 17, 118, 14], [273, 18, 118, 15], [273, 33, 121, 8], [273, 37, 121, 8, "_jsxDevRuntime"], [273, 51, 121, 8], [273, 52, 121, 8, "jsxDEV"], [273, 58, 121, 8], [273, 60, 121, 9, "_reactNative"], [273, 72, 121, 9], [273, 73, 121, 9, "View"], [273, 77, 121, 13], [274, 12, 121, 14, "style"], [274, 17, 121, 19], [274, 19, 121, 21], [275, 14, 122, 10, "backgroundColor"], [275, 29, 122, 25], [275, 31, 122, 27], [275, 37, 122, 33], [276, 14, 123, 10, "marginHorizontal"], [276, 30, 123, 26], [276, 32, 123, 28], [276, 34, 123, 30], [277, 14, 124, 10, "marginBottom"], [277, 26, 124, 22], [277, 28, 124, 24], [277, 30, 124, 26], [278, 14, 125, 10, "borderRadius"], [278, 26, 125, 22], [278, 28, 125, 24], [278, 30, 125, 26], [279, 14, 126, 10, "padding"], [279, 21, 126, 17], [279, 23, 126, 19], [279, 25, 126, 21], [280, 14, 127, 10, "shadowColor"], [280, 25, 127, 21], [280, 27, 127, 23], [280, 33, 127, 29], [281, 14, 128, 10, "shadowOffset"], [281, 26, 128, 22], [281, 28, 128, 24], [282, 16, 128, 26, "width"], [282, 21, 128, 31], [282, 23, 128, 33], [282, 24, 128, 34], [283, 16, 128, 36, "height"], [283, 22, 128, 42], [283, 24, 128, 44], [284, 14, 128, 46], [284, 15, 128, 47], [285, 14, 129, 10, "shadowOpacity"], [285, 27, 129, 23], [285, 29, 129, 25], [285, 32, 129, 28], [286, 14, 130, 10, "shadowRadius"], [286, 26, 130, 22], [286, 28, 130, 24], [286, 29, 130, 25], [287, 14, 131, 10, "elevation"], [287, 23, 131, 19], [287, 25, 131, 21], [288, 12, 132, 8], [288, 13, 132, 10], [289, 12, 132, 10, "children"], [289, 20, 132, 10], [289, 36, 133, 10], [289, 40, 133, 10, "_jsxDevRuntime"], [289, 54, 133, 10], [289, 55, 133, 10, "jsxDEV"], [289, 61, 133, 10], [289, 63, 133, 11, "_reactNative"], [289, 75, 133, 11], [289, 76, 133, 11, "Text"], [289, 80, 133, 15], [290, 14, 133, 16, "style"], [290, 19, 133, 21], [290, 21, 133, 23], [291, 16, 133, 25, "fontSize"], [291, 24, 133, 33], [291, 26, 133, 35], [291, 28, 133, 37], [292, 16, 133, 39, "fontWeight"], [292, 26, 133, 49], [292, 28, 133, 51], [292, 34, 133, 57], [293, 16, 133, 59, "color"], [293, 21, 133, 64], [293, 23, 133, 66], [293, 32, 133, 75], [294, 16, 133, 77, "marginBottom"], [294, 28, 133, 89], [294, 30, 133, 91], [295, 14, 133, 94], [295, 15, 133, 96], [296, 14, 133, 96, "children"], [296, 22, 133, 96], [296, 24, 133, 97], [297, 12, 135, 10], [298, 14, 135, 10, "fileName"], [298, 22, 135, 10], [298, 24, 135, 10, "_jsxFileName"], [298, 36, 135, 10], [299, 14, 135, 10, "lineNumber"], [299, 24, 135, 10], [300, 14, 135, 10, "columnNumber"], [300, 26, 135, 10], [301, 12, 135, 10], [301, 19, 135, 16], [301, 20, 135, 17], [301, 35, 136, 10], [301, 39, 136, 10, "_jsxDevRuntime"], [301, 53, 136, 10], [301, 54, 136, 10, "jsxDEV"], [301, 60, 136, 10], [301, 62, 136, 11, "_reactNative"], [301, 74, 136, 11], [301, 75, 136, 11, "View"], [301, 79, 136, 15], [302, 14, 136, 16, "style"], [302, 19, 136, 21], [302, 21, 136, 23], [303, 16, 136, 25, "flexDirection"], [303, 29, 136, 38], [303, 31, 136, 40], [303, 36, 136, 45], [304, 16, 136, 47, "justifyContent"], [304, 30, 136, 61], [304, 32, 136, 63], [305, 14, 136, 78], [305, 15, 136, 80], [306, 14, 136, 80, "children"], [306, 22, 136, 80], [306, 38, 137, 12], [306, 42, 137, 12, "_jsxDevRuntime"], [306, 56, 137, 12], [306, 57, 137, 12, "jsxDEV"], [306, 63, 137, 12], [306, 65, 137, 13, "_reactNative"], [306, 77, 137, 13], [306, 78, 137, 13, "View"], [306, 82, 137, 17], [307, 16, 137, 18, "style"], [307, 21, 137, 23], [307, 23, 137, 25], [308, 18, 137, 27, "alignItems"], [308, 28, 137, 37], [308, 30, 137, 39], [309, 16, 137, 48], [309, 17, 137, 50], [310, 16, 137, 50, "children"], [310, 24, 137, 50], [310, 40, 138, 14], [310, 44, 138, 14, "_jsxDevRuntime"], [310, 58, 138, 14], [310, 59, 138, 14, "jsxDEV"], [310, 65, 138, 14], [310, 67, 138, 15, "_reactNative"], [310, 79, 138, 15], [310, 80, 138, 15, "Text"], [310, 84, 138, 19], [311, 18, 138, 20, "style"], [311, 23, 138, 25], [311, 25, 138, 27], [312, 20, 138, 29, "fontSize"], [312, 28, 138, 37], [312, 30, 138, 39], [312, 32, 138, 41], [313, 20, 138, 43, "fontWeight"], [313, 30, 138, 53], [313, 32, 138, 55], [313, 38, 138, 61], [314, 20, 138, 63, "color"], [314, 25, 138, 68], [314, 27, 138, 70], [315, 18, 138, 80], [315, 19, 138, 82], [316, 18, 138, 82, "children"], [316, 26, 138, 82], [316, 28, 138, 83], [317, 16, 138, 86], [318, 18, 138, 86, "fileName"], [318, 26, 138, 86], [318, 28, 138, 86, "_jsxFileName"], [318, 40, 138, 86], [319, 18, 138, 86, "lineNumber"], [319, 28, 138, 86], [320, 18, 138, 86, "columnNumber"], [320, 30, 138, 86], [321, 16, 138, 86], [321, 23, 138, 92], [321, 24, 138, 93], [321, 39, 139, 14], [321, 43, 139, 14, "_jsxDevRuntime"], [321, 57, 139, 14], [321, 58, 139, 14, "jsxDEV"], [321, 64, 139, 14], [321, 66, 139, 15, "_reactNative"], [321, 78, 139, 15], [321, 79, 139, 15, "Text"], [321, 83, 139, 19], [322, 18, 139, 20, "style"], [322, 23, 139, 25], [322, 25, 139, 27], [323, 20, 139, 29, "color"], [323, 25, 139, 34], [323, 27, 139, 36], [323, 36, 139, 45], [324, 20, 139, 47, "fontSize"], [324, 28, 139, 55], [324, 30, 139, 57], [325, 18, 139, 60], [325, 19, 139, 62], [326, 18, 139, 62, "children"], [326, 26, 139, 62], [326, 28, 139, 63], [327, 16, 139, 69], [328, 18, 139, 69, "fileName"], [328, 26, 139, 69], [328, 28, 139, 69, "_jsxFileName"], [328, 40, 139, 69], [329, 18, 139, 69, "lineNumber"], [329, 28, 139, 69], [330, 18, 139, 69, "columnNumber"], [330, 30, 139, 69], [331, 16, 139, 69], [331, 23, 139, 75], [331, 24, 139, 76], [332, 14, 139, 76], [333, 16, 139, 76, "fileName"], [333, 24, 139, 76], [333, 26, 139, 76, "_jsxFileName"], [333, 38, 139, 76], [334, 16, 139, 76, "lineNumber"], [334, 26, 139, 76], [335, 16, 139, 76, "columnNumber"], [335, 28, 139, 76], [336, 14, 139, 76], [336, 21, 140, 18], [336, 22, 140, 19], [336, 37, 141, 12], [336, 41, 141, 12, "_jsxDevRuntime"], [336, 55, 141, 12], [336, 56, 141, 12, "jsxDEV"], [336, 62, 141, 12], [336, 64, 141, 13, "_reactNative"], [336, 76, 141, 13], [336, 77, 141, 13, "View"], [336, 81, 141, 17], [337, 16, 141, 18, "style"], [337, 21, 141, 23], [337, 23, 141, 25], [338, 18, 141, 27, "alignItems"], [338, 28, 141, 37], [338, 30, 141, 39], [339, 16, 141, 48], [339, 17, 141, 50], [340, 16, 141, 50, "children"], [340, 24, 141, 50], [340, 40, 142, 14], [340, 44, 142, 14, "_jsxDevRuntime"], [340, 58, 142, 14], [340, 59, 142, 14, "jsxDEV"], [340, 65, 142, 14], [340, 67, 142, 15, "_reactNative"], [340, 79, 142, 15], [340, 80, 142, 15, "Text"], [340, 84, 142, 19], [341, 18, 142, 20, "style"], [341, 23, 142, 25], [341, 25, 142, 27], [342, 20, 142, 29, "fontSize"], [342, 28, 142, 37], [342, 30, 142, 39], [342, 32, 142, 41], [343, 20, 142, 43, "fontWeight"], [343, 30, 142, 53], [343, 32, 142, 55], [343, 38, 142, 61], [344, 20, 142, 63, "color"], [344, 25, 142, 68], [344, 27, 142, 70], [345, 18, 142, 80], [345, 19, 142, 82], [346, 18, 142, 82, "children"], [346, 26, 142, 82], [346, 28, 142, 83], [347, 16, 142, 89], [348, 18, 142, 89, "fileName"], [348, 26, 142, 89], [348, 28, 142, 89, "_jsxFileName"], [348, 40, 142, 89], [349, 18, 142, 89, "lineNumber"], [349, 28, 142, 89], [350, 18, 142, 89, "columnNumber"], [350, 30, 142, 89], [351, 16, 142, 89], [351, 23, 142, 95], [351, 24, 142, 96], [351, 39, 143, 14], [351, 43, 143, 14, "_jsxDevRuntime"], [351, 57, 143, 14], [351, 58, 143, 14, "jsxDEV"], [351, 64, 143, 14], [351, 66, 143, 15, "_reactNative"], [351, 78, 143, 15], [351, 79, 143, 15, "Text"], [351, 83, 143, 19], [352, 18, 143, 20, "style"], [352, 23, 143, 25], [352, 25, 143, 27], [353, 20, 143, 29, "color"], [353, 25, 143, 34], [353, 27, 143, 36], [353, 36, 143, 45], [354, 20, 143, 47, "fontSize"], [354, 28, 143, 55], [354, 30, 143, 57], [355, 18, 143, 60], [355, 19, 143, 62], [356, 18, 143, 62, "children"], [356, 26, 143, 62], [356, 28, 143, 63], [357, 16, 143, 68], [358, 18, 143, 68, "fileName"], [358, 26, 143, 68], [358, 28, 143, 68, "_jsxFileName"], [358, 40, 143, 68], [359, 18, 143, 68, "lineNumber"], [359, 28, 143, 68], [360, 18, 143, 68, "columnNumber"], [360, 30, 143, 68], [361, 16, 143, 68], [361, 23, 143, 74], [361, 24, 143, 75], [362, 14, 143, 75], [363, 16, 143, 75, "fileName"], [363, 24, 143, 75], [363, 26, 143, 75, "_jsxFileName"], [363, 38, 143, 75], [364, 16, 143, 75, "lineNumber"], [364, 26, 143, 75], [365, 16, 143, 75, "columnNumber"], [365, 28, 143, 75], [366, 14, 143, 75], [366, 21, 144, 18], [366, 22, 144, 19], [366, 37, 145, 12], [366, 41, 145, 12, "_jsxDevRuntime"], [366, 55, 145, 12], [366, 56, 145, 12, "jsxDEV"], [366, 62, 145, 12], [366, 64, 145, 13, "_reactNative"], [366, 76, 145, 13], [366, 77, 145, 13, "View"], [366, 81, 145, 17], [367, 16, 145, 18, "style"], [367, 21, 145, 23], [367, 23, 145, 25], [368, 18, 145, 27, "alignItems"], [368, 28, 145, 37], [368, 30, 145, 39], [369, 16, 145, 48], [369, 17, 145, 50], [370, 16, 145, 50, "children"], [370, 24, 145, 50], [370, 40, 146, 14], [370, 44, 146, 14, "_jsxDevRuntime"], [370, 58, 146, 14], [370, 59, 146, 14, "jsxDEV"], [370, 65, 146, 14], [370, 67, 146, 15, "_reactNative"], [370, 79, 146, 15], [370, 80, 146, 15, "Text"], [370, 84, 146, 19], [371, 18, 146, 20, "style"], [371, 23, 146, 25], [371, 25, 146, 27], [372, 20, 146, 29, "fontSize"], [372, 28, 146, 37], [372, 30, 146, 39], [372, 32, 146, 41], [373, 20, 146, 43, "fontWeight"], [373, 30, 146, 53], [373, 32, 146, 55], [373, 38, 146, 61], [374, 20, 146, 63, "color"], [374, 25, 146, 68], [374, 27, 146, 70], [375, 18, 146, 80], [375, 19, 146, 82], [376, 18, 146, 82, "children"], [376, 26, 146, 82], [376, 28, 146, 83], [377, 16, 146, 85], [378, 18, 146, 85, "fileName"], [378, 26, 146, 85], [378, 28, 146, 85, "_jsxFileName"], [378, 40, 146, 85], [379, 18, 146, 85, "lineNumber"], [379, 28, 146, 85], [380, 18, 146, 85, "columnNumber"], [380, 30, 146, 85], [381, 16, 146, 85], [381, 23, 146, 91], [381, 24, 146, 92], [381, 39, 147, 14], [381, 43, 147, 14, "_jsxDevRuntime"], [381, 57, 147, 14], [381, 58, 147, 14, "jsxDEV"], [381, 64, 147, 14], [381, 66, 147, 15, "_reactNative"], [381, 78, 147, 15], [381, 79, 147, 15, "Text"], [381, 83, 147, 19], [382, 18, 147, 20, "style"], [382, 23, 147, 25], [382, 25, 147, 27], [383, 20, 147, 29, "color"], [383, 25, 147, 34], [383, 27, 147, 36], [383, 36, 147, 45], [384, 20, 147, 47, "fontSize"], [384, 28, 147, 55], [384, 30, 147, 57], [385, 18, 147, 60], [385, 19, 147, 62], [386, 18, 147, 62, "children"], [386, 26, 147, 62], [386, 28, 147, 63], [387, 16, 147, 70], [388, 18, 147, 70, "fileName"], [388, 26, 147, 70], [388, 28, 147, 70, "_jsxFileName"], [388, 40, 147, 70], [389, 18, 147, 70, "lineNumber"], [389, 28, 147, 70], [390, 18, 147, 70, "columnNumber"], [390, 30, 147, 70], [391, 16, 147, 70], [391, 23, 147, 76], [391, 24, 147, 77], [392, 14, 147, 77], [393, 16, 147, 77, "fileName"], [393, 24, 147, 77], [393, 26, 147, 77, "_jsxFileName"], [393, 38, 147, 77], [394, 16, 147, 77, "lineNumber"], [394, 26, 147, 77], [395, 16, 147, 77, "columnNumber"], [395, 28, 147, 77], [396, 14, 147, 77], [396, 21, 148, 18], [396, 22, 148, 19], [397, 12, 148, 19], [398, 14, 148, 19, "fileName"], [398, 22, 148, 19], [398, 24, 148, 19, "_jsxFileName"], [398, 36, 148, 19], [399, 14, 148, 19, "lineNumber"], [399, 24, 148, 19], [400, 14, 148, 19, "columnNumber"], [400, 26, 148, 19], [401, 12, 148, 19], [401, 19, 149, 16], [401, 20, 149, 17], [402, 10, 149, 17], [403, 12, 149, 17, "fileName"], [403, 20, 149, 17], [403, 22, 149, 17, "_jsxFileName"], [403, 34, 149, 17], [404, 12, 149, 17, "lineNumber"], [404, 22, 149, 17], [405, 12, 149, 17, "columnNumber"], [405, 24, 149, 17], [406, 10, 149, 17], [406, 17, 150, 14], [406, 18, 150, 15], [406, 33, 153, 8], [406, 37, 153, 8, "_jsxDevRuntime"], [406, 51, 153, 8], [406, 52, 153, 8, "jsxDEV"], [406, 58, 153, 8], [406, 60, 153, 9, "_reactNative"], [406, 72, 153, 9], [406, 73, 153, 9, "View"], [406, 77, 153, 13], [407, 12, 153, 14, "style"], [407, 17, 153, 19], [407, 19, 153, 21], [408, 14, 154, 10, "backgroundColor"], [408, 29, 154, 25], [408, 31, 154, 27], [408, 37, 154, 33], [409, 14, 155, 10, "marginHorizontal"], [409, 30, 155, 26], [409, 32, 155, 28], [409, 34, 155, 30], [410, 14, 156, 10, "marginBottom"], [410, 26, 156, 22], [410, 28, 156, 24], [410, 30, 156, 26], [411, 14, 157, 10, "borderRadius"], [411, 26, 157, 22], [411, 28, 157, 24], [411, 30, 157, 26], [412, 14, 158, 10, "padding"], [412, 21, 158, 17], [412, 23, 158, 19], [412, 25, 158, 21], [413, 14, 159, 10, "shadowColor"], [413, 25, 159, 21], [413, 27, 159, 23], [413, 33, 159, 29], [414, 14, 160, 10, "shadowOffset"], [414, 26, 160, 22], [414, 28, 160, 24], [415, 16, 160, 26, "width"], [415, 21, 160, 31], [415, 23, 160, 33], [415, 24, 160, 34], [416, 16, 160, 36, "height"], [416, 22, 160, 42], [416, 24, 160, 44], [417, 14, 160, 46], [417, 15, 160, 47], [418, 14, 161, 10, "shadowOpacity"], [418, 27, 161, 23], [418, 29, 161, 25], [418, 32, 161, 28], [419, 14, 162, 10, "shadowRadius"], [419, 26, 162, 22], [419, 28, 162, 24], [419, 29, 162, 25], [420, 14, 163, 10, "elevation"], [420, 23, 163, 19], [420, 25, 163, 21], [421, 12, 164, 8], [421, 13, 164, 10], [422, 12, 164, 10, "children"], [422, 20, 164, 10], [422, 36, 165, 10], [422, 40, 165, 10, "_jsxDevRuntime"], [422, 54, 165, 10], [422, 55, 165, 10, "jsxDEV"], [422, 61, 165, 10], [422, 63, 165, 11, "_reactNative"], [422, 75, 165, 11], [422, 76, 165, 11, "Text"], [422, 80, 165, 15], [423, 14, 165, 16, "style"], [423, 19, 165, 21], [423, 21, 165, 23], [424, 16, 165, 25, "fontSize"], [424, 24, 165, 33], [424, 26, 165, 35], [424, 28, 165, 37], [425, 16, 165, 39, "fontWeight"], [425, 26, 165, 49], [425, 28, 165, 51], [425, 34, 165, 57], [426, 16, 165, 59, "color"], [426, 21, 165, 64], [426, 23, 165, 66], [426, 32, 165, 75], [427, 16, 165, 77, "marginBottom"], [427, 28, 165, 89], [427, 30, 165, 91], [428, 14, 165, 94], [428, 15, 165, 96], [429, 14, 165, 96, "children"], [429, 22, 165, 96], [429, 24, 165, 97], [430, 12, 167, 10], [431, 14, 167, 10, "fileName"], [431, 22, 167, 10], [431, 24, 167, 10, "_jsxFileName"], [431, 36, 167, 10], [432, 14, 167, 10, "lineNumber"], [432, 24, 167, 10], [433, 14, 167, 10, "columnNumber"], [433, 26, 167, 10], [434, 12, 167, 10], [434, 19, 167, 16], [434, 20, 167, 17], [434, 35, 169, 10], [434, 39, 169, 10, "_jsxDevRuntime"], [434, 53, 169, 10], [434, 54, 169, 10, "jsxDEV"], [434, 60, 169, 10], [434, 62, 169, 11, "_reactNative"], [434, 74, 169, 11], [434, 75, 169, 11, "View"], [434, 79, 169, 15], [435, 14, 169, 16, "style"], [435, 19, 169, 21], [435, 21, 169, 23], [436, 16, 169, 25, "flexDirection"], [436, 29, 169, 38], [436, 31, 169, 40], [436, 36, 169, 45], [437, 16, 169, 47, "justifyContent"], [437, 30, 169, 61], [437, 32, 169, 63], [437, 47, 169, 78], [438, 16, 169, 80, "alignItems"], [438, 26, 169, 90], [438, 28, 169, 92], [438, 36, 169, 100], [439, 16, 169, 102, "marginBottom"], [439, 28, 169, 114], [439, 30, 169, 116], [440, 14, 169, 119], [440, 15, 169, 121], [441, 14, 169, 121, "children"], [441, 22, 169, 121], [441, 38, 170, 12], [441, 42, 170, 12, "_jsxDevRuntime"], [441, 56, 170, 12], [441, 57, 170, 12, "jsxDEV"], [441, 63, 170, 12], [441, 65, 170, 13, "_reactNative"], [441, 77, 170, 13], [441, 78, 170, 13, "View"], [441, 82, 170, 17], [442, 16, 170, 18, "style"], [442, 21, 170, 23], [442, 23, 170, 25], [443, 18, 170, 27, "flexDirection"], [443, 31, 170, 40], [443, 33, 170, 42], [443, 38, 170, 47], [444, 18, 170, 49, "alignItems"], [444, 28, 170, 59], [444, 30, 170, 61], [445, 16, 170, 70], [445, 17, 170, 72], [446, 16, 170, 72, "children"], [446, 24, 170, 72], [446, 40, 171, 14], [446, 44, 171, 14, "_jsxDevRuntime"], [446, 58, 171, 14], [446, 59, 171, 14, "jsxDEV"], [446, 65, 171, 14], [446, 67, 171, 15, "_vectorIcons"], [446, 79, 171, 15], [446, 80, 171, 15, "Ionicons"], [446, 88, 171, 23], [447, 18, 171, 24, "name"], [447, 22, 171, 28], [447, 24, 171, 29], [447, 47, 171, 52], [448, 18, 171, 53, "size"], [448, 22, 171, 57], [448, 24, 171, 59], [448, 26, 171, 62], [449, 18, 171, 63, "color"], [449, 23, 171, 68], [449, 25, 171, 69], [450, 16, 171, 78], [451, 18, 171, 78, "fileName"], [451, 26, 171, 78], [451, 28, 171, 78, "_jsxFileName"], [451, 40, 171, 78], [452, 18, 171, 78, "lineNumber"], [452, 28, 171, 78], [453, 18, 171, 78, "columnNumber"], [453, 30, 171, 78], [454, 16, 171, 78], [454, 23, 171, 80], [454, 24, 171, 81], [454, 39, 172, 14], [454, 43, 172, 14, "_jsxDevRuntime"], [454, 57, 172, 14], [454, 58, 172, 14, "jsxDEV"], [454, 64, 172, 14], [454, 66, 172, 15, "_reactNative"], [454, 78, 172, 15], [454, 79, 172, 15, "Text"], [454, 83, 172, 19], [455, 18, 172, 20, "style"], [455, 23, 172, 25], [455, 25, 172, 27], [456, 20, 172, 29, "marginLeft"], [456, 30, 172, 39], [456, 32, 172, 41], [456, 34, 172, 43], [457, 20, 172, 45, "fontSize"], [457, 28, 172, 53], [457, 30, 172, 55], [457, 32, 172, 57], [458, 20, 172, 59, "color"], [458, 25, 172, 64], [458, 27, 172, 66], [459, 18, 172, 76], [459, 19, 172, 78], [460, 18, 172, 78, "children"], [460, 26, 172, 78], [460, 28, 172, 79], [461, 16, 174, 14], [462, 18, 174, 14, "fileName"], [462, 26, 174, 14], [462, 28, 174, 14, "_jsxFileName"], [462, 40, 174, 14], [463, 18, 174, 14, "lineNumber"], [463, 28, 174, 14], [464, 18, 174, 14, "columnNumber"], [464, 30, 174, 14], [465, 16, 174, 14], [465, 23, 174, 20], [465, 24, 174, 21], [466, 14, 174, 21], [467, 16, 174, 21, "fileName"], [467, 24, 174, 21], [467, 26, 174, 21, "_jsxFileName"], [467, 38, 174, 21], [468, 16, 174, 21, "lineNumber"], [468, 26, 174, 21], [469, 16, 174, 21, "columnNumber"], [469, 28, 174, 21], [470, 14, 174, 21], [470, 21, 175, 18], [470, 22, 175, 19], [470, 37, 176, 12], [470, 41, 176, 12, "_jsxDevRuntime"], [470, 55, 176, 12], [470, 56, 176, 12, "jsxDEV"], [470, 62, 176, 12], [470, 64, 176, 13, "_reactNative"], [470, 76, 176, 13], [470, 77, 176, 13, "Switch"], [470, 83, 176, 19], [471, 16, 177, 14, "value"], [471, 21, 177, 19], [471, 23, 177, 21, "notificationsEnabled"], [471, 43, 177, 42], [472, 16, 178, 14, "onValueChange"], [472, 29, 178, 27], [472, 31, 178, 29, "setNotificationsEnabled"], [472, 54, 178, 53], [473, 16, 179, 14, "trackColor"], [473, 26, 179, 24], [473, 28, 179, 26], [474, 18, 179, 28, "false"], [474, 23, 179, 33], [474, 25, 179, 35], [474, 34, 179, 44], [475, 18, 179, 46, "true"], [475, 22, 179, 50], [475, 24, 179, 52], [476, 16, 179, 62], [476, 17, 179, 64], [477, 16, 180, 14, "thumbColor"], [477, 26, 180, 24], [477, 28, 180, 26, "notificationsEnabled"], [477, 48, 180, 46], [477, 51, 180, 49], [477, 57, 180, 55], [477, 60, 180, 58], [478, 14, 180, 68], [479, 16, 180, 68, "fileName"], [479, 24, 180, 68], [479, 26, 180, 68, "_jsxFileName"], [479, 38, 180, 68], [480, 16, 180, 68, "lineNumber"], [480, 26, 180, 68], [481, 16, 180, 68, "columnNumber"], [481, 28, 180, 68], [482, 14, 180, 68], [482, 21, 181, 13], [482, 22, 181, 14], [483, 12, 181, 14], [484, 14, 181, 14, "fileName"], [484, 22, 181, 14], [484, 24, 181, 14, "_jsxFileName"], [484, 36, 181, 14], [485, 14, 181, 14, "lineNumber"], [485, 24, 181, 14], [486, 14, 181, 14, "columnNumber"], [486, 26, 181, 14], [487, 12, 181, 14], [487, 19, 182, 16], [487, 20, 182, 17], [487, 35, 184, 10], [487, 39, 184, 10, "_jsxDevRuntime"], [487, 53, 184, 10], [487, 54, 184, 10, "jsxDEV"], [487, 60, 184, 10], [487, 62, 184, 11, "_reactNative"], [487, 74, 184, 11], [487, 75, 184, 11, "View"], [487, 79, 184, 15], [488, 14, 184, 16, "style"], [488, 19, 184, 21], [488, 21, 184, 23], [489, 16, 184, 25, "flexDirection"], [489, 29, 184, 38], [489, 31, 184, 40], [489, 36, 184, 45], [490, 16, 184, 47, "justifyContent"], [490, 30, 184, 61], [490, 32, 184, 63], [490, 47, 184, 78], [491, 16, 184, 80, "alignItems"], [491, 26, 184, 90], [491, 28, 184, 92], [492, 14, 184, 101], [492, 15, 184, 103], [493, 14, 184, 103, "children"], [493, 22, 184, 103], [493, 38, 185, 12], [493, 42, 185, 12, "_jsxDevRuntime"], [493, 56, 185, 12], [493, 57, 185, 12, "jsxDEV"], [493, 63, 185, 12], [493, 65, 185, 13, "_reactNative"], [493, 77, 185, 13], [493, 78, 185, 13, "View"], [493, 82, 185, 17], [494, 16, 185, 18, "style"], [494, 21, 185, 23], [494, 23, 185, 25], [495, 18, 185, 27, "flexDirection"], [495, 31, 185, 40], [495, 33, 185, 42], [495, 38, 185, 47], [496, 18, 185, 49, "alignItems"], [496, 28, 185, 59], [496, 30, 185, 61], [497, 16, 185, 70], [497, 17, 185, 72], [498, 16, 185, 72, "children"], [498, 24, 185, 72], [498, 40, 186, 14], [498, 44, 186, 14, "_jsxDevRuntime"], [498, 58, 186, 14], [498, 59, 186, 14, "jsxDEV"], [498, 65, 186, 14], [498, 67, 186, 15, "_vectorIcons"], [498, 79, 186, 15], [498, 80, 186, 15, "Ionicons"], [498, 88, 186, 23], [499, 18, 186, 24, "name"], [499, 22, 186, 28], [499, 24, 186, 29], [499, 42, 186, 47], [500, 18, 186, 48, "size"], [500, 22, 186, 52], [500, 24, 186, 54], [500, 26, 186, 57], [501, 18, 186, 58, "color"], [501, 23, 186, 63], [501, 25, 186, 64], [502, 16, 186, 73], [503, 18, 186, 73, "fileName"], [503, 26, 186, 73], [503, 28, 186, 73, "_jsxFileName"], [503, 40, 186, 73], [504, 18, 186, 73, "lineNumber"], [504, 28, 186, 73], [505, 18, 186, 73, "columnNumber"], [505, 30, 186, 73], [506, 16, 186, 73], [506, 23, 186, 75], [506, 24, 186, 76], [506, 39, 187, 14], [506, 43, 187, 14, "_jsxDevRuntime"], [506, 57, 187, 14], [506, 58, 187, 14, "jsxDEV"], [506, 64, 187, 14], [506, 66, 187, 15, "_reactNative"], [506, 78, 187, 15], [506, 79, 187, 15, "Text"], [506, 83, 187, 19], [507, 18, 187, 20, "style"], [507, 23, 187, 25], [507, 25, 187, 27], [508, 20, 187, 29, "marginLeft"], [508, 30, 187, 39], [508, 32, 187, 41], [508, 34, 187, 43], [509, 20, 187, 45, "fontSize"], [509, 28, 187, 53], [509, 30, 187, 55], [509, 32, 187, 57], [510, 20, 187, 59, "color"], [510, 25, 187, 64], [510, 27, 187, 66], [511, 18, 187, 76], [511, 19, 187, 78], [512, 18, 187, 78, "children"], [512, 26, 187, 78], [512, 28, 187, 79], [513, 16, 189, 14], [514, 18, 189, 14, "fileName"], [514, 26, 189, 14], [514, 28, 189, 14, "_jsxFileName"], [514, 40, 189, 14], [515, 18, 189, 14, "lineNumber"], [515, 28, 189, 14], [516, 18, 189, 14, "columnNumber"], [516, 30, 189, 14], [517, 16, 189, 14], [517, 23, 189, 20], [517, 24, 189, 21], [518, 14, 189, 21], [519, 16, 189, 21, "fileName"], [519, 24, 189, 21], [519, 26, 189, 21, "_jsxFileName"], [519, 38, 189, 21], [520, 16, 189, 21, "lineNumber"], [520, 26, 189, 21], [521, 16, 189, 21, "columnNumber"], [521, 28, 189, 21], [522, 14, 189, 21], [522, 21, 190, 18], [522, 22, 190, 19], [522, 37, 191, 12], [522, 41, 191, 12, "_jsxDevRuntime"], [522, 55, 191, 12], [522, 56, 191, 12, "jsxDEV"], [522, 62, 191, 12], [522, 64, 191, 13, "_reactNative"], [522, 76, 191, 13], [522, 77, 191, 13, "Switch"], [522, 83, 191, 19], [523, 16, 192, 14, "value"], [523, 21, 192, 19], [523, 23, 192, 21, "locationEnabled"], [523, 38, 192, 37], [524, 16, 193, 14, "onValueChange"], [524, 29, 193, 27], [524, 31, 193, 29, "setLocationEnabled"], [524, 49, 193, 48], [525, 16, 194, 14, "trackColor"], [525, 26, 194, 24], [525, 28, 194, 26], [526, 18, 194, 28, "false"], [526, 23, 194, 33], [526, 25, 194, 35], [526, 34, 194, 44], [527, 18, 194, 46, "true"], [527, 22, 194, 50], [527, 24, 194, 52], [528, 16, 194, 62], [528, 17, 194, 64], [529, 16, 195, 14, "thumbColor"], [529, 26, 195, 24], [529, 28, 195, 26, "locationEnabled"], [529, 43, 195, 41], [529, 46, 195, 44], [529, 52, 195, 50], [529, 55, 195, 53], [530, 14, 195, 63], [531, 16, 195, 63, "fileName"], [531, 24, 195, 63], [531, 26, 195, 63, "_jsxFileName"], [531, 38, 195, 63], [532, 16, 195, 63, "lineNumber"], [532, 26, 195, 63], [533, 16, 195, 63, "columnNumber"], [533, 28, 195, 63], [534, 14, 195, 63], [534, 21, 196, 13], [534, 22, 196, 14], [535, 12, 196, 14], [536, 14, 196, 14, "fileName"], [536, 22, 196, 14], [536, 24, 196, 14, "_jsxFileName"], [536, 36, 196, 14], [537, 14, 196, 14, "lineNumber"], [537, 24, 196, 14], [538, 14, 196, 14, "columnNumber"], [538, 26, 196, 14], [539, 12, 196, 14], [539, 19, 197, 16], [539, 20, 197, 17], [540, 10, 197, 17], [541, 12, 197, 17, "fileName"], [541, 20, 197, 17], [541, 22, 197, 17, "_jsxFileName"], [541, 34, 197, 17], [542, 12, 197, 17, "lineNumber"], [542, 22, 197, 17], [543, 12, 197, 17, "columnNumber"], [543, 24, 197, 17], [544, 10, 197, 17], [544, 17, 198, 14], [544, 18, 198, 15], [544, 20, 201, 9, "menuItems"], [544, 29, 201, 18], [544, 30, 201, 19, "map"], [544, 33, 201, 22], [544, 34, 201, 23], [544, 35, 201, 24, "section"], [544, 42, 201, 31], [544, 44, 201, 33, "sectionIndex"], [544, 56, 201, 45], [544, 74, 202, 10], [544, 78, 202, 10, "_jsxDevRuntime"], [544, 92, 202, 10], [544, 93, 202, 10, "jsxDEV"], [544, 99, 202, 10], [544, 101, 202, 11, "_reactNative"], [544, 113, 202, 11], [544, 114, 202, 11, "View"], [544, 118, 202, 15], [545, 12, 204, 12, "style"], [545, 17, 204, 17], [545, 19, 204, 19], [546, 14, 205, 14, "backgroundColor"], [546, 29, 205, 29], [546, 31, 205, 31], [546, 37, 205, 37], [547, 14, 206, 14, "marginHorizontal"], [547, 30, 206, 30], [547, 32, 206, 32], [547, 34, 206, 34], [548, 14, 207, 14, "marginBottom"], [548, 26, 207, 26], [548, 28, 207, 28], [548, 30, 207, 30], [549, 14, 208, 14, "borderRadius"], [549, 26, 208, 26], [549, 28, 208, 28], [549, 30, 208, 30], [550, 14, 209, 14, "shadowColor"], [550, 25, 209, 25], [550, 27, 209, 27], [550, 33, 209, 33], [551, 14, 210, 14, "shadowOffset"], [551, 26, 210, 26], [551, 28, 210, 28], [552, 16, 210, 30, "width"], [552, 21, 210, 35], [552, 23, 210, 37], [552, 24, 210, 38], [553, 16, 210, 40, "height"], [553, 22, 210, 46], [553, 24, 210, 48], [554, 14, 210, 50], [554, 15, 210, 51], [555, 14, 211, 14, "shadowOpacity"], [555, 27, 211, 27], [555, 29, 211, 29], [555, 32, 211, 32], [556, 14, 212, 14, "shadowRadius"], [556, 26, 212, 26], [556, 28, 212, 28], [556, 29, 212, 29], [557, 14, 213, 14, "elevation"], [557, 23, 213, 23], [557, 25, 213, 25], [558, 12, 214, 12], [558, 13, 214, 14], [559, 12, 214, 14, "children"], [559, 20, 214, 14], [559, 36, 216, 12], [559, 40, 216, 12, "_jsxDevRuntime"], [559, 54, 216, 12], [559, 55, 216, 12, "jsxDEV"], [559, 61, 216, 12], [559, 63, 216, 13, "_reactNative"], [559, 75, 216, 13], [559, 76, 216, 13, "Text"], [559, 80, 216, 17], [560, 14, 216, 18, "style"], [560, 19, 216, 23], [560, 21, 216, 25], [561, 16, 217, 14, "fontSize"], [561, 24, 217, 22], [561, 26, 217, 24], [561, 28, 217, 26], [562, 16, 218, 14, "fontWeight"], [562, 26, 218, 24], [562, 28, 218, 26], [562, 34, 218, 32], [563, 16, 219, 14, "color"], [563, 21, 219, 19], [563, 23, 219, 21], [563, 32, 219, 30], [564, 16, 220, 14, "padding"], [564, 23, 220, 21], [564, 25, 220, 23], [564, 27, 220, 25], [565, 16, 221, 14, "paddingBottom"], [565, 29, 221, 27], [565, 31, 221, 29], [566, 14, 222, 12], [566, 15, 222, 14], [567, 14, 222, 14, "children"], [567, 22, 222, 14], [567, 24, 223, 15, "section"], [567, 31, 223, 22], [567, 32, 223, 23, "section"], [568, 12, 223, 30], [569, 14, 223, 30, "fileName"], [569, 22, 223, 30], [569, 24, 223, 30, "_jsxFileName"], [569, 36, 223, 30], [570, 14, 223, 30, "lineNumber"], [570, 24, 223, 30], [571, 14, 223, 30, "columnNumber"], [571, 26, 223, 30], [572, 12, 223, 30], [572, 19, 224, 18], [572, 20, 224, 19], [572, 22, 226, 13, "section"], [572, 29, 226, 20], [572, 30, 226, 21, "items"], [572, 35, 226, 26], [572, 36, 226, 27, "map"], [572, 39, 226, 30], [572, 40, 226, 31], [572, 41, 226, 32, "item"], [572, 45, 226, 36], [572, 47, 226, 38, "itemIndex"], [572, 56, 226, 47], [572, 74, 227, 14], [572, 78, 227, 14, "_jsxDevRuntime"], [572, 92, 227, 14], [572, 93, 227, 14, "jsxDEV"], [572, 99, 227, 14], [572, 101, 227, 15, "_reactNative"], [572, 113, 227, 15], [572, 114, 227, 15, "TouchableOpacity"], [572, 130, 227, 31], [573, 14, 229, 16, "style"], [573, 19, 229, 21], [573, 21, 229, 23], [574, 16, 230, 18, "flexDirection"], [574, 29, 230, 31], [574, 31, 230, 33], [574, 36, 230, 38], [575, 16, 231, 18, "alignItems"], [575, 26, 231, 28], [575, 28, 231, 30], [575, 36, 231, 38], [576, 16, 232, 18, "paddingHorizontal"], [576, 33, 232, 35], [576, 35, 232, 37], [576, 37, 232, 39], [577, 16, 233, 18, "paddingVertical"], [577, 31, 233, 33], [577, 33, 233, 35], [577, 35, 233, 37], [578, 16, 234, 18, "borderTopWidth"], [578, 30, 234, 32], [578, 32, 234, 34, "itemIndex"], [578, 41, 234, 43], [578, 44, 234, 46], [578, 45, 234, 47], [578, 48, 234, 50], [578, 49, 234, 51], [578, 52, 234, 54], [578, 53, 234, 55], [579, 16, 235, 18, "borderTopColor"], [579, 30, 235, 32], [579, 32, 235, 34], [580, 14, 236, 16], [580, 15, 236, 18], [581, 14, 236, 18, "children"], [581, 22, 236, 18], [581, 38, 238, 16], [581, 42, 238, 16, "_jsxDevRuntime"], [581, 56, 238, 16], [581, 57, 238, 16, "jsxDEV"], [581, 63, 238, 16], [581, 65, 238, 17, "_vectorIcons"], [581, 77, 238, 17], [581, 78, 238, 17, "Ionicons"], [581, 86, 238, 25], [582, 16, 238, 26, "name"], [582, 20, 238, 30], [582, 22, 238, 32, "item"], [582, 26, 238, 36], [582, 27, 238, 37, "icon"], [582, 31, 238, 49], [583, 16, 238, 50, "size"], [583, 20, 238, 54], [583, 22, 238, 56], [583, 24, 238, 59], [584, 16, 238, 60, "color"], [584, 21, 238, 65], [584, 23, 238, 66], [585, 14, 238, 75], [586, 16, 238, 75, "fileName"], [586, 24, 238, 75], [586, 26, 238, 75, "_jsxFileName"], [586, 38, 238, 75], [587, 16, 238, 75, "lineNumber"], [587, 26, 238, 75], [588, 16, 238, 75, "columnNumber"], [588, 28, 238, 75], [589, 14, 238, 75], [589, 21, 238, 77], [589, 22, 238, 78], [589, 37, 239, 16], [589, 41, 239, 16, "_jsxDevRuntime"], [589, 55, 239, 16], [589, 56, 239, 16, "jsxDEV"], [589, 62, 239, 16], [589, 64, 239, 17, "_reactNative"], [589, 76, 239, 17], [589, 77, 239, 17, "View"], [589, 81, 239, 21], [590, 16, 239, 22, "style"], [590, 21, 239, 27], [590, 23, 239, 29], [591, 18, 239, 31, "flex"], [591, 22, 239, 35], [591, 24, 239, 37], [591, 25, 239, 38], [592, 18, 239, 40, "marginLeft"], [592, 28, 239, 50], [592, 30, 239, 52], [593, 16, 239, 55], [593, 17, 239, 57], [594, 16, 239, 57, "children"], [594, 24, 239, 57], [594, 40, 240, 18], [594, 44, 240, 18, "_jsxDevRuntime"], [594, 58, 240, 18], [594, 59, 240, 18, "jsxDEV"], [594, 65, 240, 18], [594, 67, 240, 19, "_reactNative"], [594, 79, 240, 19], [594, 80, 240, 19, "Text"], [594, 84, 240, 23], [595, 18, 240, 24, "style"], [595, 23, 240, 29], [595, 25, 240, 31], [596, 20, 240, 33, "fontSize"], [596, 28, 240, 41], [596, 30, 240, 43], [596, 32, 240, 45], [597, 20, 240, 47, "color"], [597, 25, 240, 52], [597, 27, 240, 54], [597, 36, 240, 63], [598, 20, 240, 65, "marginBottom"], [598, 32, 240, 77], [598, 34, 240, 79], [599, 18, 240, 81], [599, 19, 240, 83], [600, 18, 240, 83, "children"], [600, 26, 240, 83], [600, 28, 241, 21, "item"], [600, 32, 241, 25], [600, 33, 241, 26, "title"], [601, 16, 241, 31], [602, 18, 241, 31, "fileName"], [602, 26, 241, 31], [602, 28, 241, 31, "_jsxFileName"], [602, 40, 241, 31], [603, 18, 241, 31, "lineNumber"], [603, 28, 241, 31], [604, 18, 241, 31, "columnNumber"], [604, 30, 241, 31], [605, 16, 241, 31], [605, 23, 242, 24], [605, 24, 242, 25], [605, 39, 243, 18], [605, 43, 243, 18, "_jsxDevRuntime"], [605, 57, 243, 18], [605, 58, 243, 18, "jsxDEV"], [605, 64, 243, 18], [605, 66, 243, 19, "_reactNative"], [605, 78, 243, 19], [605, 79, 243, 19, "Text"], [605, 83, 243, 23], [606, 18, 243, 24, "style"], [606, 23, 243, 29], [606, 25, 243, 31], [607, 20, 243, 33, "fontSize"], [607, 28, 243, 41], [607, 30, 243, 43], [607, 32, 243, 45], [608, 20, 243, 47, "color"], [608, 25, 243, 52], [608, 27, 243, 54], [609, 18, 243, 64], [609, 19, 243, 66], [610, 18, 243, 66, "children"], [610, 26, 243, 66], [610, 28, 244, 21, "item"], [610, 32, 244, 25], [610, 33, 244, 26, "subtitle"], [611, 16, 244, 34], [612, 18, 244, 34, "fileName"], [612, 26, 244, 34], [612, 28, 244, 34, "_jsxFileName"], [612, 40, 244, 34], [613, 18, 244, 34, "lineNumber"], [613, 28, 244, 34], [614, 18, 244, 34, "columnNumber"], [614, 30, 244, 34], [615, 16, 244, 34], [615, 23, 245, 24], [615, 24, 245, 25], [616, 14, 245, 25], [617, 16, 245, 25, "fileName"], [617, 24, 245, 25], [617, 26, 245, 25, "_jsxFileName"], [617, 38, 245, 25], [618, 16, 245, 25, "lineNumber"], [618, 26, 245, 25], [619, 16, 245, 25, "columnNumber"], [619, 28, 245, 25], [620, 14, 245, 25], [620, 21, 246, 22], [620, 22, 246, 23], [620, 37, 247, 16], [620, 41, 247, 16, "_jsxDevRuntime"], [620, 55, 247, 16], [620, 56, 247, 16, "jsxDEV"], [620, 62, 247, 16], [620, 64, 247, 17, "_vectorIcons"], [620, 76, 247, 17], [620, 77, 247, 17, "Ionicons"], [620, 85, 247, 25], [621, 16, 247, 26, "name"], [621, 20, 247, 30], [621, 22, 247, 31], [621, 39, 247, 48], [622, 16, 247, 49, "size"], [622, 20, 247, 53], [622, 22, 247, 55], [622, 24, 247, 58], [623, 16, 247, 59, "color"], [623, 21, 247, 64], [623, 23, 247, 65], [624, 14, 247, 74], [625, 16, 247, 74, "fileName"], [625, 24, 247, 74], [625, 26, 247, 74, "_jsxFileName"], [625, 38, 247, 74], [626, 16, 247, 74, "lineNumber"], [626, 26, 247, 74], [627, 16, 247, 74, "columnNumber"], [627, 28, 247, 74], [628, 14, 247, 74], [628, 21, 247, 76], [628, 22, 247, 77], [629, 12, 247, 77], [629, 15, 228, 21, "itemIndex"], [629, 24, 228, 30], [630, 14, 228, 30, "fileName"], [630, 22, 228, 30], [630, 24, 228, 30, "_jsxFileName"], [630, 36, 228, 30], [631, 14, 228, 30, "lineNumber"], [631, 24, 228, 30], [632, 14, 228, 30, "columnNumber"], [632, 26, 228, 30], [633, 12, 228, 30], [633, 19, 248, 32], [633, 20, 249, 13], [633, 21, 249, 14], [634, 10, 249, 14], [634, 13, 203, 17, "sectionIndex"], [634, 25, 203, 29], [635, 12, 203, 29, "fileName"], [635, 20, 203, 29], [635, 22, 203, 29, "_jsxFileName"], [635, 34, 203, 29], [636, 12, 203, 29, "lineNumber"], [636, 22, 203, 29], [637, 12, 203, 29, "columnNumber"], [637, 24, 203, 29], [638, 10, 203, 29], [638, 17, 250, 16], [638, 18, 251, 9], [638, 19, 251, 10], [638, 34, 254, 8], [638, 38, 254, 8, "_jsxDevRuntime"], [638, 52, 254, 8], [638, 53, 254, 8, "jsxDEV"], [638, 59, 254, 8], [638, 61, 254, 9, "_reactNative"], [638, 73, 254, 9], [638, 74, 254, 9, "TouchableOpacity"], [638, 90, 254, 25], [639, 12, 254, 26, "style"], [639, 17, 254, 31], [639, 19, 254, 33], [640, 14, 255, 10, "backgroundColor"], [640, 29, 255, 25], [640, 31, 255, 27], [640, 37, 255, 33], [641, 14, 256, 10, "marginHorizontal"], [641, 30, 256, 26], [641, 32, 256, 28], [641, 34, 256, 30], [642, 14, 257, 10, "marginBottom"], [642, 26, 257, 22], [642, 28, 257, 24], [642, 30, 257, 26], [643, 14, 258, 10, "borderRadius"], [643, 26, 258, 22], [643, 28, 258, 24], [643, 30, 258, 26], [644, 14, 259, 10, "padding"], [644, 21, 259, 17], [644, 23, 259, 19], [644, 25, 259, 21], [645, 14, 260, 10, "shadowColor"], [645, 25, 260, 21], [645, 27, 260, 23], [645, 33, 260, 29], [646, 14, 261, 10, "shadowOffset"], [646, 26, 261, 22], [646, 28, 261, 24], [647, 16, 261, 26, "width"], [647, 21, 261, 31], [647, 23, 261, 33], [647, 24, 261, 34], [648, 16, 261, 36, "height"], [648, 22, 261, 42], [648, 24, 261, 44], [649, 14, 261, 46], [649, 15, 261, 47], [650, 14, 262, 10, "shadowOpacity"], [650, 27, 262, 23], [650, 29, 262, 25], [650, 32, 262, 28], [651, 14, 263, 10, "shadowRadius"], [651, 26, 263, 22], [651, 28, 263, 24], [651, 29, 263, 25], [652, 14, 264, 10, "elevation"], [652, 23, 264, 19], [652, 25, 264, 21], [652, 26, 264, 22], [653, 14, 265, 10, "alignItems"], [653, 24, 265, 20], [653, 26, 265, 22], [654, 12, 266, 8], [654, 13, 266, 10], [655, 12, 266, 10, "children"], [655, 20, 266, 10], [655, 35, 267, 10], [655, 39, 267, 10, "_jsxDevRuntime"], [655, 53, 267, 10], [655, 54, 267, 10, "jsxDEV"], [655, 60, 267, 10], [655, 62, 267, 11, "_reactNative"], [655, 74, 267, 11], [655, 75, 267, 11, "Text"], [655, 79, 267, 15], [656, 14, 267, 16, "style"], [656, 19, 267, 21], [656, 21, 267, 23], [657, 16, 267, 25, "fontSize"], [657, 24, 267, 33], [657, 26, 267, 35], [657, 28, 267, 37], [658, 16, 267, 39, "color"], [658, 21, 267, 44], [658, 23, 267, 46], [658, 32, 267, 55], [659, 16, 267, 57, "fontWeight"], [659, 26, 267, 67], [659, 28, 267, 69], [660, 14, 267, 75], [660, 15, 267, 77], [661, 14, 267, 77, "children"], [661, 22, 267, 77], [661, 24, 267, 78], [662, 12, 269, 10], [663, 14, 269, 10, "fileName"], [663, 22, 269, 10], [663, 24, 269, 10, "_jsxFileName"], [663, 36, 269, 10], [664, 14, 269, 10, "lineNumber"], [664, 24, 269, 10], [665, 14, 269, 10, "columnNumber"], [665, 26, 269, 10], [666, 12, 269, 10], [666, 19, 269, 16], [667, 10, 269, 17], [668, 12, 269, 17, "fileName"], [668, 20, 269, 17], [668, 22, 269, 17, "_jsxFileName"], [668, 34, 269, 17], [669, 12, 269, 17, "lineNumber"], [669, 22, 269, 17], [670, 12, 269, 17, "columnNumber"], [670, 24, 269, 17], [671, 10, 269, 17], [671, 17, 270, 26], [671, 18, 270, 27], [671, 33, 272, 8], [671, 37, 272, 8, "_jsxDevRuntime"], [671, 51, 272, 8], [671, 52, 272, 8, "jsxDEV"], [671, 58, 272, 8], [671, 60, 272, 9, "_reactNative"], [671, 72, 272, 9], [671, 73, 272, 9, "View"], [671, 77, 272, 13], [672, 12, 272, 14, "style"], [672, 17, 272, 19], [672, 19, 272, 21], [673, 14, 272, 23, "height"], [673, 20, 272, 29], [673, 22, 272, 31], [674, 12, 272, 35], [675, 10, 272, 37], [676, 12, 272, 37, "fileName"], [676, 20, 272, 37], [676, 22, 272, 37, "_jsxFileName"], [676, 34, 272, 37], [677, 12, 272, 37, "lineNumber"], [677, 22, 272, 37], [678, 12, 272, 37, "columnNumber"], [678, 24, 272, 37], [679, 10, 272, 37], [679, 17, 272, 39], [679, 18, 272, 40], [680, 8, 272, 40], [681, 10, 272, 40, "fileName"], [681, 18, 272, 40], [681, 20, 272, 40, "_jsxFileName"], [681, 32, 272, 40], [682, 10, 272, 40, "lineNumber"], [682, 20, 272, 40], [683, 10, 272, 40, "columnNumber"], [683, 22, 272, 40], [684, 8, 272, 40], [684, 15, 273, 20], [684, 16, 273, 21], [685, 6, 273, 21], [686, 8, 273, 21, "fileName"], [686, 16, 273, 21], [686, 18, 273, 21, "_jsxFileName"], [686, 30, 273, 21], [687, 8, 273, 21, "lineNumber"], [687, 18, 273, 21], [688, 8, 273, 21, "columnNumber"], [688, 20, 273, 21], [689, 6, 273, 21], [689, 13, 274, 12], [689, 14, 274, 13], [689, 29, 277, 6], [689, 33, 277, 6, "_jsxDevRuntime"], [689, 47, 277, 6], [689, 48, 277, 6, "jsxDEV"], [689, 54, 277, 6], [689, 56, 277, 7, "_FooterNavigation"], [689, 73, 277, 7], [689, 74, 277, 7, "default"], [689, 81, 277, 23], [690, 8, 277, 24, "navigation"], [690, 18, 277, 34], [690, 20, 277, 36, "navigation"], [690, 30, 277, 47], [691, 8, 277, 48, "activeScreen"], [691, 20, 277, 60], [691, 22, 277, 61], [692, 6, 277, 70], [693, 8, 277, 70, "fileName"], [693, 16, 277, 70], [693, 18, 277, 70, "_jsxFileName"], [693, 30, 277, 70], [694, 8, 277, 70, "lineNumber"], [694, 18, 277, 70], [695, 8, 277, 70, "columnNumber"], [695, 20, 277, 70], [696, 6, 277, 70], [696, 13, 277, 72], [696, 14, 277, 73], [696, 29, 280, 6], [696, 33, 280, 6, "_jsxDevRuntime"], [696, 47, 280, 6], [696, 48, 280, 6, "jsxDEV"], [696, 54, 280, 6], [696, 56, 280, 7, "_reactNativeSafeAreaContext"], [696, 83, 280, 7], [696, 84, 280, 7, "SafeAreaView"], [696, 96, 280, 19], [697, 8, 280, 20, "style"], [697, 13, 280, 25], [697, 15, 280, 27], [698, 10, 280, 29, "backgroundColor"], [698, 25, 280, 44], [698, 27, 280, 46], [699, 8, 280, 56], [699, 9, 280, 58], [700, 8, 280, 59, "edges"], [700, 13, 280, 64], [700, 15, 280, 66], [700, 16, 280, 67], [700, 24, 280, 75], [701, 6, 280, 77], [702, 8, 280, 77, "fileName"], [702, 16, 280, 77], [702, 18, 280, 77, "_jsxFileName"], [702, 30, 280, 77], [703, 8, 280, 77, "lineNumber"], [703, 18, 280, 77], [704, 8, 280, 77, "columnNumber"], [704, 20, 280, 77], [705, 6, 280, 77], [705, 13, 280, 79], [705, 14, 280, 80], [706, 4, 280, 80], [707, 6, 280, 80, "fileName"], [707, 14, 280, 80], [707, 16, 280, 80, "_jsxFileName"], [707, 28, 280, 80], [708, 6, 280, 80, "lineNumber"], [708, 16, 280, 80], [709, 6, 280, 80, "columnNumber"], [709, 18, 280, 80], [710, 4, 280, 80], [710, 11, 281, 10], [710, 12, 281, 11], [711, 2, 283, 0], [712, 2, 283, 1, "_s"], [712, 4, 283, 1], [712, 5, 14, 24, "AccountScreen"], [712, 18, 14, 37], [713, 2, 14, 37, "_c"], [713, 4, 14, 37], [713, 7, 14, 24, "AccountScreen"], [713, 20, 14, 37], [714, 2, 14, 37], [714, 6, 14, 37, "_c"], [714, 8, 14, 37], [715, 2, 14, 37, "$RefreshReg$"], [715, 14, 14, 37], [715, 15, 14, 37, "_c"], [715, 17, 14, 37], [716, 0, 14, 37], [716, 3]], "functionMap": {"names": ["<global>", "AccountScreen", "menuItems.map$argument_0", "section.items.map$argument_0"], "mappings": "AAA;eCa;uBC2L;+BCyB;aDuB;SDE;CDgC"}}, "type": "js/module"}]}