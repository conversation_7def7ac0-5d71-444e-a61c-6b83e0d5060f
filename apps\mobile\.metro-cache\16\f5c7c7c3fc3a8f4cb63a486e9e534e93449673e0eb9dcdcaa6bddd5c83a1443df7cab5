{"dependencies": [{"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 152}, "end": {"line": 9, "column": 35, "index": 187}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 188}, "end": {"line": 15, "column": 28, "index": 285}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "./dispatchCommand", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 286}, "end": {"line": 16, "column": 52, "index": 338}}], "key": "ox+VJbjgxK+JO2iVuZgRrFKJTho=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.scrollTo = void 0;\n  var _logger = require(_dependencyMap[0], \"../logger\");\n  var _PlatformChecker = require(_dependencyMap[1], \"../PlatformChecker\");\n  var _dispatchCommand = require(_dependencyMap[2], \"./dispatchCommand\");\n  /**\n   * Lets you synchronously scroll to a given position of a `ScrollView`.\n   *\n   * @param animatedRef - An [animated\n   *   ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef)\n   *   attached to an `Animated.ScrollView` component.\n   * @param x - The x position you want to scroll to.\n   * @param y - The y position you want to scroll to.\n   * @param animated - Whether the scrolling should be smooth or instant.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/scroll/scrollTo\n   */\n  var scrollTo;\n  var _worklet_16532235539756_init_data = {\n    code: \"function scrollToFabric_scrollToTs1(animatedRef,x,y,animated){const{dispatchCommand}=this.__closure;dispatchCommand(animatedRef,'scrollTo',[x,y,animated]);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\platformFunctions\\\\scrollTo.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"scrollToFabric_scrollToTs1\\\",\\\"animatedRef\\\",\\\"x\\\",\\\"y\\\",\\\"animated\\\",\\\"dispatchCommand\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/platformFunctions/scrollTo.ts\\\"],\\\"mappings\\\":\\\"AAqCA,SAAAA,0BACEA,CAA8CC,WAG9C,CAAAC,CAAA,CAAiBC,CAAA,CACjBC,QAAA,QAAAC,eAAA,OAAAC,SAAA,CAEAD,eAAe,CAEbJ,WAAW,CACX,UAAU,CACV,CAACC,CAAC,CAAEC,CAAC,CAAEC,QAAQ,CACjB,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var scrollToFabric = function () {\n    var _e = [new global.Error(), -2, -27];\n    var scrollToFabric = function (animatedRef, x, y, animated) {\n      (0, _dispatchCommand.dispatchCommand)(\n      // This assertion is needed to comply to `dispatchCommand` interface\n      animatedRef, 'scrollTo', [x, y, animated]);\n    };\n    scrollToFabric.__closure = {\n      dispatchCommand: _dispatchCommand.dispatchCommand\n    };\n    scrollToFabric.__workletHash = 16532235539756;\n    scrollToFabric.__initData = _worklet_16532235539756_init_data;\n    scrollToFabric.__stackDetails = _e;\n    return scrollToFabric;\n  }();\n  var _worklet_4709209822663_init_data = {\n    code: \"function scrollToPaper_scrollToTs2(animatedRef,x,y,animated){if(!_WORKLET){return;}const viewTag=animatedRef();global._scrollToPaper(viewTag,x,y,animated);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\platformFunctions\\\\scrollTo.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"scrollToPaper_scrollToTs2\\\",\\\"animatedRef\\\",\\\"x\\\",\\\"y\\\",\\\"animated\\\",\\\"_WORKLET\\\",\\\"viewTag\\\",\\\"global\\\",\\\"_scrollToPaper\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/platformFunctions/scrollTo.ts\\\"],\\\"mappings\\\":\\\"AAoDA,SAAAA,yBACEA,CAA8CC,WAG9C,CAAAC,CAAA,CAAiBC,CAAA,CACjBC,QAAA,EAEA,GAAI,CAACC,QAAQ,CAAE,CACb,OACF,CAEA,KAAM,CAAAC,OAAO,CAAGL,WAAW,CAAC,CAAW,CACvCM,MAAM,CAACC,cAAc,CAAEF,OAAO,CAAEJ,CAAC,CAAEC,CAAC,CAAEC,QAAQ,CAAC,CACjD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var scrollToPaper = function () {\n    var _e = [new global.Error(), 1, -27];\n    var scrollToPaper = function (animatedRef, x, y, animated) {\n      if (!_WORKLET) {\n        return;\n      }\n      var viewTag = animatedRef();\n      global._scrollToPaper(viewTag, x, y, animated);\n    };\n    scrollToPaper.__closure = {};\n    scrollToPaper.__workletHash = 4709209822663;\n    scrollToPaper.__initData = _worklet_4709209822663_init_data;\n    scrollToPaper.__stackDetails = _e;\n    return scrollToPaper;\n  }();\n  function scrollToJest() {\n    _logger.logger.warn('scrollTo() is not supported with Jest.');\n  }\n  function scrollToChromeDebugger() {\n    _logger.logger.warn('scrollTo() is not supported with Chrome Debugger.');\n  }\n  function scrollToDefault() {\n    _logger.logger.warn('scrollTo() is not supported on this configuration.');\n  }\n  if (!(0, _PlatformChecker.shouldBeUseWeb)()) {\n    // Those assertions are actually correct since on Native platforms `AnimatedRef` is\n    // mapped as a different function in `shareableMappingCache` and\n    // TypeScript is not able to infer that.\n    if ((0, _PlatformChecker.isFabric)()) {\n      exports.scrollTo = scrollTo = scrollToFabric;\n    } else {\n      exports.scrollTo = scrollTo = scrollToPaper;\n    }\n  } else if ((0, _PlatformChecker.isJest)()) {\n    exports.scrollTo = scrollTo = scrollToJest;\n  } else if ((0, _PlatformChecker.isChromeDebugger)()) {\n    exports.scrollTo = scrollTo = scrollToChromeDebugger;\n  } else {\n    exports.scrollTo = scrollTo = scrollToDefault;\n  }\n});", "lineCount": 90, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "scrollTo"], [7, 18, 1, 13], [8, 2, 9, 0], [8, 6, 9, 0, "_logger"], [8, 13, 9, 0], [8, 16, 9, 0, "require"], [8, 23, 9, 0], [8, 24, 9, 0, "_dependencyMap"], [8, 38, 9, 0], [9, 2, 10, 0], [9, 6, 10, 0, "_PlatformChecker"], [9, 22, 10, 0], [9, 25, 10, 0, "require"], [9, 32, 10, 0], [9, 33, 10, 0, "_dependencyMap"], [9, 47, 10, 0], [10, 2, 16, 0], [10, 6, 16, 0, "_dispatchCommand"], [10, 22, 16, 0], [10, 25, 16, 0, "require"], [10, 32, 16, 0], [10, 33, 16, 0, "_dependencyMap"], [10, 47, 16, 0], [11, 2, 25, 0], [12, 0, 26, 0], [13, 0, 27, 0], [14, 0, 28, 0], [15, 0, 29, 0], [16, 0, 30, 0], [17, 0, 31, 0], [18, 0, 32, 0], [19, 0, 33, 0], [20, 0, 34, 0], [21, 0, 35, 0], [22, 2, 36, 7], [22, 6, 36, 11, "scrollTo"], [22, 14, 36, 29], [23, 2, 36, 30], [23, 6, 36, 30, "_worklet_16532235539756_init_data"], [23, 39, 36, 30], [24, 4, 36, 30, "code"], [24, 8, 36, 30], [25, 4, 36, 30, "location"], [25, 12, 36, 30], [26, 4, 36, 30, "sourceMap"], [26, 13, 36, 30], [27, 4, 36, 30, "version"], [27, 11, 36, 30], [28, 2, 36, 30], [29, 2, 36, 30], [29, 6, 36, 30, "scrollToFabric"], [29, 20, 36, 30], [29, 23, 38, 0], [30, 4, 38, 0], [30, 8, 38, 0, "_e"], [30, 10, 38, 0], [30, 18, 38, 0, "global"], [30, 24, 38, 0], [30, 25, 38, 0, "Error"], [30, 30, 38, 0], [31, 4, 38, 0], [31, 8, 38, 0, "scrollToFabric"], [31, 22, 38, 0], [31, 34, 38, 0, "scrollToFabric"], [31, 35, 39, 2, "animatedRef"], [31, 46, 39, 48], [31, 48, 40, 2, "x"], [31, 49, 40, 11], [31, 51, 41, 2, "y"], [31, 52, 41, 11], [31, 54, 42, 2, "animated"], [31, 62, 42, 19], [31, 64, 43, 2], [32, 6, 45, 2], [32, 10, 45, 2, "dispatchCommand"], [32, 42, 45, 17], [33, 6, 46, 4], [34, 6, 47, 4, "animatedRef"], [34, 17, 47, 15], [34, 19, 48, 4], [34, 29, 48, 14], [34, 31, 49, 4], [34, 32, 49, 5, "x"], [34, 33, 49, 6], [34, 35, 49, 8, "y"], [34, 36, 49, 9], [34, 38, 49, 11, "animated"], [34, 46, 49, 19], [34, 47, 50, 2], [34, 48, 50, 3], [35, 4, 51, 0], [35, 5, 51, 1], [36, 4, 51, 1, "scrollToFabric"], [36, 18, 51, 1], [36, 19, 51, 1, "__closure"], [36, 28, 51, 1], [37, 6, 51, 1, "dispatchCommand"], [37, 21, 51, 1], [37, 23, 45, 2, "dispatchCommand"], [38, 4, 45, 17], [39, 4, 45, 17, "scrollToFabric"], [39, 18, 45, 17], [39, 19, 45, 17, "__workletHash"], [39, 32, 45, 17], [40, 4, 45, 17, "scrollToFabric"], [40, 18, 45, 17], [40, 19, 45, 17, "__initData"], [40, 29, 45, 17], [40, 32, 45, 17, "_worklet_16532235539756_init_data"], [40, 65, 45, 17], [41, 4, 45, 17, "scrollToFabric"], [41, 18, 45, 17], [41, 19, 45, 17, "__stackDetails"], [41, 33, 45, 17], [41, 36, 45, 17, "_e"], [41, 38, 45, 17], [42, 4, 45, 17], [42, 11, 45, 17, "scrollToFabric"], [42, 25, 45, 17], [43, 2, 45, 17], [43, 3, 38, 0], [44, 2, 38, 0], [44, 6, 38, 0, "_worklet_4709209822663_init_data"], [44, 38, 38, 0], [45, 4, 38, 0, "code"], [45, 8, 38, 0], [46, 4, 38, 0, "location"], [46, 12, 38, 0], [47, 4, 38, 0, "sourceMap"], [47, 13, 38, 0], [48, 4, 38, 0, "version"], [48, 11, 38, 0], [49, 2, 38, 0], [50, 2, 38, 0], [50, 6, 38, 0, "scrollToPaper"], [50, 19, 38, 0], [50, 22, 53, 0], [51, 4, 53, 0], [51, 8, 53, 0, "_e"], [51, 10, 53, 0], [51, 18, 53, 0, "global"], [51, 24, 53, 0], [51, 25, 53, 0, "Error"], [51, 30, 53, 0], [52, 4, 53, 0], [52, 8, 53, 0, "scrollToPaper"], [52, 21, 53, 0], [52, 33, 53, 0, "scrollToPaper"], [52, 34, 54, 2, "animatedRef"], [52, 45, 54, 48], [52, 47, 55, 2, "x"], [52, 48, 55, 11], [52, 50, 56, 2, "y"], [52, 51, 56, 11], [52, 53, 57, 2, "animated"], [52, 61, 57, 19], [52, 63, 58, 2], [53, 6, 60, 2], [53, 10, 60, 6], [53, 11, 60, 7, "_WORKLET"], [53, 19, 60, 15], [53, 21, 60, 17], [54, 8, 61, 4], [55, 6, 62, 2], [56, 6, 64, 2], [56, 10, 64, 8, "viewTag"], [56, 17, 64, 15], [56, 20, 64, 18, "animatedRef"], [56, 31, 64, 29], [56, 32, 64, 30], [56, 33, 64, 41], [57, 6, 65, 2, "global"], [57, 12, 65, 8], [57, 13, 65, 9, "_scrollToPaper"], [57, 27, 65, 23], [57, 28, 65, 25, "viewTag"], [57, 35, 65, 32], [57, 37, 65, 34, "x"], [57, 38, 65, 35], [57, 40, 65, 37, "y"], [57, 41, 65, 38], [57, 43, 65, 40, "animated"], [57, 51, 65, 48], [57, 52, 65, 49], [58, 4, 66, 0], [58, 5, 66, 1], [59, 4, 66, 1, "scrollToPaper"], [59, 17, 66, 1], [59, 18, 66, 1, "__closure"], [59, 27, 66, 1], [60, 4, 66, 1, "scrollToPaper"], [60, 17, 66, 1], [60, 18, 66, 1, "__workletHash"], [60, 31, 66, 1], [61, 4, 66, 1, "scrollToPaper"], [61, 17, 66, 1], [61, 18, 66, 1, "__initData"], [61, 28, 66, 1], [61, 31, 66, 1, "_worklet_4709209822663_init_data"], [61, 63, 66, 1], [62, 4, 66, 1, "scrollToPaper"], [62, 17, 66, 1], [62, 18, 66, 1, "__stackDetails"], [62, 32, 66, 1], [62, 35, 66, 1, "_e"], [62, 37, 66, 1], [63, 4, 66, 1], [63, 11, 66, 1, "scrollToPaper"], [63, 24, 66, 1], [64, 2, 66, 1], [64, 3, 53, 0], [65, 2, 68, 0], [65, 11, 68, 9, "scrollToJest"], [65, 23, 68, 21, "scrollToJest"], [65, 24, 68, 21], [65, 26, 68, 24], [66, 4, 69, 2, "logger"], [66, 18, 69, 8], [66, 19, 69, 9, "warn"], [66, 23, 69, 13], [66, 24, 69, 14], [66, 64, 69, 54], [66, 65, 69, 55], [67, 2, 70, 0], [68, 2, 72, 0], [68, 11, 72, 9, "scrollToChromeDebugger"], [68, 33, 72, 31, "scrollToChromeDebugger"], [68, 34, 72, 31], [68, 36, 72, 34], [69, 4, 73, 2, "logger"], [69, 18, 73, 8], [69, 19, 73, 9, "warn"], [69, 23, 73, 13], [69, 24, 73, 14], [69, 75, 73, 65], [69, 76, 73, 66], [70, 2, 74, 0], [71, 2, 76, 0], [71, 11, 76, 9, "scrollToDefault"], [71, 26, 76, 24, "scrollToDefault"], [71, 27, 76, 24], [71, 29, 76, 27], [72, 4, 77, 2, "logger"], [72, 18, 77, 8], [72, 19, 77, 9, "warn"], [72, 23, 77, 13], [72, 24, 77, 14], [72, 76, 77, 66], [72, 77, 77, 67], [73, 2, 78, 0], [74, 2, 80, 0], [74, 6, 80, 4], [74, 7, 80, 5], [74, 11, 80, 5, "shouldBeUseWeb"], [74, 42, 80, 19], [74, 44, 80, 20], [74, 45, 80, 21], [74, 47, 80, 23], [75, 4, 81, 2], [76, 4, 82, 2], [77, 4, 83, 2], [78, 4, 84, 2], [78, 8, 84, 6], [78, 12, 84, 6, "isF<PERSON><PERSON>"], [78, 37, 84, 14], [78, 39, 84, 15], [78, 40, 84, 16], [78, 42, 84, 18], [79, 6, 85, 4, "exports"], [79, 13, 85, 4], [79, 14, 85, 4, "scrollTo"], [79, 22, 85, 4], [79, 25, 85, 4, "scrollTo"], [79, 33, 85, 12], [79, 36, 85, 15, "scrollToFabric"], [79, 50, 85, 52], [80, 4, 86, 2], [80, 5, 86, 3], [80, 11, 86, 9], [81, 6, 87, 4, "exports"], [81, 13, 87, 4], [81, 14, 87, 4, "scrollTo"], [81, 22, 87, 4], [81, 25, 87, 4, "scrollTo"], [81, 33, 87, 12], [81, 36, 87, 15, "scrollToPaper"], [81, 49, 87, 51], [82, 4, 88, 2], [83, 2, 89, 0], [83, 3, 89, 1], [83, 9, 89, 7], [83, 13, 89, 11], [83, 17, 89, 11, "isJest"], [83, 40, 89, 17], [83, 42, 89, 18], [83, 43, 89, 19], [83, 45, 89, 21], [84, 4, 90, 2, "exports"], [84, 11, 90, 2], [84, 12, 90, 2, "scrollTo"], [84, 20, 90, 2], [84, 23, 90, 2, "scrollTo"], [84, 31, 90, 10], [84, 34, 90, 13, "scrollToJest"], [84, 46, 90, 25], [85, 2, 91, 0], [85, 3, 91, 1], [85, 9, 91, 7], [85, 13, 91, 11], [85, 17, 91, 11, "isChromeDebugger"], [85, 50, 91, 27], [85, 52, 91, 28], [85, 53, 91, 29], [85, 55, 91, 31], [86, 4, 92, 2, "exports"], [86, 11, 92, 2], [86, 12, 92, 2, "scrollTo"], [86, 20, 92, 2], [86, 23, 92, 2, "scrollTo"], [86, 31, 92, 10], [86, 34, 92, 13, "scrollToChromeDebugger"], [86, 56, 92, 35], [87, 2, 93, 0], [87, 3, 93, 1], [87, 9, 93, 7], [88, 4, 94, 2, "exports"], [88, 11, 94, 2], [88, 12, 94, 2, "scrollTo"], [88, 20, 94, 2], [88, 23, 94, 2, "scrollTo"], [88, 31, 94, 10], [88, 34, 94, 13, "scrollToDefault"], [88, 49, 94, 28], [89, 2, 95, 0], [90, 0, 95, 1], [90, 3]], "functionMap": {"names": ["<global>", "scrollToFabric", "scrollToPaper", "scrollToJest", "scrollToChromeDebugger", "scrollToDefault"], "mappings": "AAA;ACqC;CDa;AEE;CFa;AGE;CHE;AIE;CJE;AKE;CLE"}}, "type": "js/module"}]}