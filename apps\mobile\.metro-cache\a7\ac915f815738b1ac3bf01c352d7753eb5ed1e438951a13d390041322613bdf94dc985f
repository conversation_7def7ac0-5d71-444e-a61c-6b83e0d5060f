{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "PWvtvXU7MaET6Yd1Gn8oQOXJQ8A=", "exportNames": ["*"]}}, {"name": "./App", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 49}, "end": {"line": 3, "column": 24, "index": 73}}], "key": "ast/ITGQj4FCx1KDroUEzNlgRhM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  var _expo = require(_dependencyMap[1], \"expo\");\n  var _App = _interopRequireDefault(require(_dependencyMap[2], \"./App\"));\n  // registerRootComponent calls AppRegistry.registerComponent('main', () => App);\n  // It also ensures that whether you load the app in Expo Go or in a native build,\n  // the environment is set up appropriately\n  (0, _expo.registerRootComponent)(_App.default);\n});", "lineCount": 9, "map": [[3, 2, 1, 0], [3, 6, 1, 0, "_expo"], [3, 11, 1, 0], [3, 14, 1, 0, "require"], [3, 21, 1, 0], [3, 22, 1, 0, "_dependencyMap"], [3, 36, 1, 0], [4, 2, 3, 0], [4, 6, 3, 0, "_App"], [4, 10, 3, 0], [4, 13, 3, 0, "_interopRequireDefault"], [4, 35, 3, 0], [4, 36, 3, 0, "require"], [4, 43, 3, 0], [4, 44, 3, 0, "_dependencyMap"], [4, 58, 3, 0], [5, 2, 5, 0], [6, 2, 6, 0], [7, 2, 7, 0], [8, 2, 8, 0], [8, 6, 8, 0, "registerRootComponent"], [8, 33, 8, 21], [8, 35, 8, 22, "App"], [8, 47, 8, 25], [8, 48, 8, 26], [9, 0, 8, 27], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}