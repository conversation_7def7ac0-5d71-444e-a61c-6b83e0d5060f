{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 262}, "end": {"line": 6, "column": 45, "index": 307}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  // Copyright © 2024 650 Industries.\n  // NOTE: Forcing this to be a client boundary so the errors are a bit clearer. In the future we can\n  // make this a shim on the server by ignoring the globals that are missing in React Server contexts (Node.js).\n  'use client';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var LegacyNativeProxy = _reactNative.NativeModules?.NativeUnimoduleProxy;\n  // Fixes `cannot find name 'global'.` in tests\n  // @ts-ignore\n  var ExpoNativeProxy = global.expo?.modules?.NativeModulesProxy;\n  var modulesConstantsKey = 'modulesConstants';\n  var exportedMethodsKey = 'exportedMethods';\n\n  /**\n   * @deprecated `NativeModulesProxy` is deprecated and might be removed in the future releases.\n   * Use `requireNativeModule` or `requireOptionalNativeModule` instead.\n   */\n  var NativeModulesProxy = {};\n  if (LegacyNativeProxy) {\n    // use JSI proxy if available, fallback to legacy RN proxy\n    var NativeProxy = ExpoNativeProxy ?? LegacyNativeProxy;\n    Object.keys(NativeProxy[exportedMethodsKey]).forEach(moduleName => {\n      // copy constants\n      NativeModulesProxy[moduleName] = NativeProxy[modulesConstantsKey][moduleName] || {};\n\n      // copy methods\n      // TODO(@kitten): Annotate `NativeProxy` with abstract types to avoid implicit `any`\n      NativeProxy[exportedMethodsKey][moduleName].forEach(methodInfo => {\n        NativeModulesProxy[moduleName][methodInfo.name] = function () {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          // Use the new proxy to call methods on legacy modules, if possible.\n          if (ExpoNativeProxy?.callMethod) {\n            return ExpoNativeProxy.callMethod(moduleName, methodInfo.name, args);\n          }\n\n          // Otherwise fall back to the legacy proxy.\n          // This is deprecated and might be removed in SDK47 or later.\n          var key = methodInfo.key,\n            argumentsCount = methodInfo.argumentsCount;\n          if (argumentsCount !== args.length) {\n            return Promise.reject(new Error(`Native method ${moduleName}.${methodInfo.name} expects ${argumentsCount} ${argumentsCount === 1 ? 'argument' : 'arguments'} but received ${args.length}`));\n          }\n          return LegacyNativeProxy.callMethod(moduleName, key, args);\n        };\n      });\n\n      // These are called by EventEmitter (which is a wrapper for NativeEventEmitter)\n      // only on iOS, and they use iOS-specific native module, EXReactNativeEventEmitter.\n      //\n      // On Android only {start,stop}Observing are called on the native module\n      // and these should be exported as Expo methods.\n      //\n      // Before the RN 65, addListener/removeListeners weren't called on Android. However, it no longer stays true.\n      // See https://github.com/facebook/react-native/commit/f5502fbda9fe271ff6e1d0da773a3a8ee206a453.\n      // That's why, we check if the `EXReactNativeEventEmitter` exists and only if yes, we use it in the listener implementation.\n      // Otherwise, those methods are NOOP.\n      if (_reactNative.NativeModules.EXReactNativeEventEmitter) {\n        NativeModulesProxy[moduleName].addListener = function () {\n          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n            args[_key2] = arguments[_key2];\n          }\n          return _reactNative.NativeModules.EXReactNativeEventEmitter.addProxiedListener(moduleName, ...args);\n        };\n        NativeModulesProxy[moduleName].removeListeners = function () {\n          for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n            args[_key3] = arguments[_key3];\n          }\n          return _reactNative.NativeModules.EXReactNativeEventEmitter.removeProxiedListeners(moduleName, ...args);\n        };\n      } else {\n        // Fixes on Android:\n        // WARN  `new NativeEventEmitter()` was called with a non-null argument without the required `addListener` method.\n        // WARN  `new NativeEventEmitter()` was called with a non-null argument without the required `removeListeners` method.\n        NativeModulesProxy[moduleName].addListener = () => {};\n        NativeModulesProxy[moduleName].removeListeners = () => {};\n      }\n    });\n  } else {\n    console.warn(`The \"EXNativeModulesProxy\" native module is not exported through NativeModules; verify that expo-modules-core's native code is linked properly`);\n  }\n  var _default = exports.default = NativeModulesProxy;\n});", "lineCount": 89, "map": [[2, 2, 1, 0], [3, 2, 2, 0], [4, 2, 3, 0], [5, 2, 4, 0], [5, 14, 4, 12], [7, 2, 4, 13, "Object"], [7, 8, 4, 13], [7, 9, 4, 13, "defineProperty"], [7, 23, 4, 13], [7, 24, 4, 13, "exports"], [7, 31, 4, 13], [8, 4, 4, 13, "value"], [8, 9, 4, 13], [9, 2, 4, 13], [10, 2, 4, 13, "exports"], [10, 9, 4, 13], [10, 10, 4, 13, "default"], [10, 17, 4, 13], [11, 2, 6, 0], [11, 6, 6, 0, "_reactNative"], [11, 18, 6, 0], [11, 21, 6, 0, "require"], [11, 28, 6, 0], [11, 29, 6, 0, "_dependencyMap"], [11, 43, 6, 0], [12, 2, 10, 0], [12, 6, 10, 6, "LegacyNativeProxy"], [12, 23, 10, 23], [12, 26, 10, 26, "NativeModules"], [12, 52, 10, 39], [12, 54, 10, 41, "NativeUnimoduleProxy"], [12, 74, 10, 61], [13, 2, 11, 0], [14, 2, 12, 0], [15, 2, 13, 0], [15, 6, 13, 6, "ExpoNativeProxy"], [15, 21, 13, 21], [15, 24, 13, 24, "global"], [15, 30, 13, 30], [15, 31, 13, 31, "expo"], [15, 35, 13, 35], [15, 37, 13, 37, "modules"], [15, 44, 13, 44], [15, 46, 13, 46, "NativeModulesProxy"], [15, 64, 13, 64], [16, 2, 15, 0], [16, 6, 15, 6, "modulesConstantsKey"], [16, 25, 15, 25], [16, 28, 15, 28], [16, 46, 15, 46], [17, 2, 16, 0], [17, 6, 16, 6, "exportedMethodsKey"], [17, 24, 16, 24], [17, 27, 16, 27], [17, 44, 16, 44], [19, 2, 18, 0], [20, 0, 19, 0], [21, 0, 20, 0], [22, 0, 21, 0], [23, 2, 22, 0], [23, 6, 22, 6, "NativeModulesProxy"], [23, 24, 22, 59], [23, 27, 22, 62], [23, 28, 22, 63], [23, 29, 22, 64], [24, 2, 24, 0], [24, 6, 24, 4, "LegacyNativeProxy"], [24, 23, 24, 21], [24, 25, 24, 23], [25, 4, 25, 2], [26, 4, 26, 2], [26, 8, 26, 8, "NativeProxy"], [26, 19, 26, 19], [26, 22, 26, 22, "ExpoNativeProxy"], [26, 37, 26, 37], [26, 41, 26, 41, "LegacyNativeProxy"], [26, 58, 26, 58], [27, 4, 28, 2, "Object"], [27, 10, 28, 8], [27, 11, 28, 9, "keys"], [27, 15, 28, 13], [27, 16, 28, 14, "NativeProxy"], [27, 27, 28, 25], [27, 28, 28, 26, "exportedMethodsKey"], [27, 46, 28, 44], [27, 47, 28, 45], [27, 48, 28, 46], [27, 49, 28, 47, "for<PERSON>ach"], [27, 56, 28, 54], [27, 57, 28, 56, "moduleName"], [27, 67, 28, 66], [27, 71, 28, 71], [28, 6, 29, 4], [29, 6, 30, 4, "NativeModulesProxy"], [29, 24, 30, 22], [29, 25, 30, 23, "moduleName"], [29, 35, 30, 33], [29, 36, 30, 34], [29, 39, 30, 37, "NativeProxy"], [29, 50, 30, 48], [29, 51, 30, 49, "modulesConstantsKey"], [29, 70, 30, 68], [29, 71, 30, 69], [29, 72, 30, 70, "moduleName"], [29, 82, 30, 80], [29, 83, 30, 81], [29, 87, 30, 85], [29, 88, 30, 86], [29, 89, 30, 87], [31, 6, 32, 4], [32, 6, 33, 4], [33, 6, 34, 4, "NativeProxy"], [33, 17, 34, 15], [33, 18, 34, 16, "exportedMethodsKey"], [33, 36, 34, 34], [33, 37, 34, 35], [33, 38, 34, 36, "moduleName"], [33, 48, 34, 46], [33, 49, 34, 47], [33, 50, 34, 48, "for<PERSON>ach"], [33, 57, 34, 55], [33, 58, 34, 57, "methodInfo"], [33, 68, 34, 72], [33, 72, 34, 77], [34, 8, 35, 6, "NativeModulesProxy"], [34, 26, 35, 24], [34, 27, 35, 25, "moduleName"], [34, 37, 35, 35], [34, 38, 35, 36], [34, 39, 35, 37, "methodInfo"], [34, 49, 35, 47], [34, 50, 35, 48, "name"], [34, 54, 35, 52], [34, 55, 35, 53], [34, 58, 35, 56], [34, 70, 35, 94], [35, 10, 35, 94], [35, 19, 35, 94, "_len"], [35, 23, 35, 94], [35, 26, 35, 94, "arguments"], [35, 35, 35, 94], [35, 36, 35, 94, "length"], [35, 42, 35, 94], [35, 44, 35, 60, "args"], [35, 48, 35, 64], [35, 55, 35, 64, "Array"], [35, 60, 35, 64], [35, 61, 35, 64, "_len"], [35, 65, 35, 64], [35, 68, 35, 64, "_key"], [35, 72, 35, 64], [35, 78, 35, 64, "_key"], [35, 82, 35, 64], [35, 85, 35, 64, "_len"], [35, 89, 35, 64], [35, 91, 35, 64, "_key"], [35, 95, 35, 64], [36, 12, 35, 60, "args"], [36, 16, 35, 64], [36, 17, 35, 64, "_key"], [36, 21, 35, 64], [36, 25, 35, 64, "arguments"], [36, 34, 35, 64], [36, 35, 35, 64, "_key"], [36, 39, 35, 64], [37, 10, 35, 64], [38, 10, 36, 8], [39, 10, 37, 8], [39, 14, 37, 12, "ExpoNativeProxy"], [39, 29, 37, 27], [39, 31, 37, 29, "callMethod"], [39, 41, 37, 39], [39, 43, 37, 41], [40, 12, 38, 10], [40, 19, 38, 17, "ExpoNativeProxy"], [40, 34, 38, 32], [40, 35, 38, 33, "callMethod"], [40, 45, 38, 43], [40, 46, 38, 44, "moduleName"], [40, 56, 38, 54], [40, 58, 38, 56, "methodInfo"], [40, 68, 38, 66], [40, 69, 38, 67, "name"], [40, 73, 38, 71], [40, 75, 38, 73, "args"], [40, 79, 38, 77], [40, 80, 38, 78], [41, 10, 39, 8], [43, 10, 41, 8], [44, 10, 42, 8], [45, 10, 43, 8], [45, 14, 43, 16, "key"], [45, 17, 43, 19], [45, 20, 43, 40, "methodInfo"], [45, 30, 43, 50], [45, 31, 43, 16, "key"], [45, 34, 43, 19], [46, 12, 43, 21, "argumentsCount"], [46, 26, 43, 35], [46, 29, 43, 40, "methodInfo"], [46, 39, 43, 50], [46, 40, 43, 21, "argumentsCount"], [46, 54, 43, 35], [47, 10, 44, 8], [47, 14, 44, 12, "argumentsCount"], [47, 28, 44, 26], [47, 33, 44, 31, "args"], [47, 37, 44, 35], [47, 38, 44, 36, "length"], [47, 44, 44, 42], [47, 46, 44, 44], [48, 12, 45, 10], [48, 19, 45, 17, "Promise"], [48, 26, 45, 24], [48, 27, 45, 25, "reject"], [48, 33, 45, 31], [48, 34, 46, 12], [48, 38, 46, 16, "Error"], [48, 43, 46, 21], [48, 44, 47, 14], [48, 61, 47, 31, "moduleName"], [48, 71, 47, 41], [48, 75, 47, 45, "methodInfo"], [48, 85, 47, 55], [48, 86, 47, 56, "name"], [48, 90, 47, 60], [48, 102, 47, 72, "argumentsCount"], [48, 116, 47, 86], [48, 120, 48, 16, "argumentsCount"], [48, 134, 48, 30], [48, 139, 48, 35], [48, 140, 48, 36], [48, 143, 48, 39], [48, 153, 48, 49], [48, 156, 48, 52], [48, 167, 48, 63], [48, 184, 49, 31, "args"], [48, 188, 49, 35], [48, 189, 49, 36, "length"], [48, 195, 49, 42], [48, 197, 50, 12], [48, 198, 51, 10], [48, 199, 51, 11], [49, 10, 52, 8], [50, 10, 53, 8], [50, 17, 53, 15, "LegacyNativeProxy"], [50, 34, 53, 32], [50, 35, 53, 33, "callMethod"], [50, 45, 53, 43], [50, 46, 53, 44, "moduleName"], [50, 56, 53, 54], [50, 58, 53, 56, "key"], [50, 61, 53, 59], [50, 63, 53, 61, "args"], [50, 67, 53, 65], [50, 68, 53, 66], [51, 8, 54, 6], [51, 9, 54, 7], [52, 6, 55, 4], [52, 7, 55, 5], [52, 8, 55, 6], [54, 6, 57, 4], [55, 6, 58, 4], [56, 6, 59, 4], [57, 6, 60, 4], [58, 6, 61, 4], [59, 6, 62, 4], [60, 6, 63, 4], [61, 6, 64, 4], [62, 6, 65, 4], [63, 6, 66, 4], [64, 6, 67, 4], [64, 10, 67, 8, "NativeModules"], [64, 36, 67, 21], [64, 37, 67, 22, "EXReactNativeEventEmitter"], [64, 62, 67, 47], [64, 64, 67, 49], [65, 8, 68, 6, "NativeModulesProxy"], [65, 26, 68, 24], [65, 27, 68, 25, "moduleName"], [65, 37, 68, 35], [65, 38, 68, 36], [65, 39, 68, 37, "addListener"], [65, 50, 68, 48], [65, 53, 68, 51], [66, 10, 68, 51], [66, 19, 68, 51, "_len2"], [66, 24, 68, 51], [66, 27, 68, 51, "arguments"], [66, 36, 68, 51], [66, 37, 68, 51, "length"], [66, 43, 68, 51], [66, 45, 68, 55, "args"], [66, 49, 68, 59], [66, 56, 68, 59, "Array"], [66, 61, 68, 59], [66, 62, 68, 59, "_len2"], [66, 67, 68, 59], [66, 70, 68, 59, "_key2"], [66, 75, 68, 59], [66, 81, 68, 59, "_key2"], [66, 86, 68, 59], [66, 89, 68, 59, "_len2"], [66, 94, 68, 59], [66, 96, 68, 59, "_key2"], [66, 101, 68, 59], [67, 12, 68, 55, "args"], [67, 16, 68, 59], [67, 17, 68, 59, "_key2"], [67, 22, 68, 59], [67, 26, 68, 59, "arguments"], [67, 35, 68, 59], [67, 36, 68, 59, "_key2"], [67, 41, 68, 59], [68, 10, 68, 59], [69, 10, 68, 59], [69, 17, 69, 8, "NativeModules"], [69, 43, 69, 21], [69, 44, 69, 22, "EXReactNativeEventEmitter"], [69, 69, 69, 47], [69, 70, 69, 48, "addProxiedListener"], [69, 88, 69, 66], [69, 89, 69, 67, "moduleName"], [69, 99, 69, 77], [69, 101, 69, 79], [69, 104, 69, 82, "args"], [69, 108, 69, 86], [69, 109, 69, 87], [70, 8, 69, 87], [71, 8, 70, 6, "NativeModulesProxy"], [71, 26, 70, 24], [71, 27, 70, 25, "moduleName"], [71, 37, 70, 35], [71, 38, 70, 36], [71, 39, 70, 37, "removeListeners"], [71, 54, 70, 52], [71, 57, 70, 55], [72, 10, 70, 55], [72, 19, 70, 55, "_len3"], [72, 24, 70, 55], [72, 27, 70, 55, "arguments"], [72, 36, 70, 55], [72, 37, 70, 55, "length"], [72, 43, 70, 55], [72, 45, 70, 59, "args"], [72, 49, 70, 63], [72, 56, 70, 63, "Array"], [72, 61, 70, 63], [72, 62, 70, 63, "_len3"], [72, 67, 70, 63], [72, 70, 70, 63, "_key3"], [72, 75, 70, 63], [72, 81, 70, 63, "_key3"], [72, 86, 70, 63], [72, 89, 70, 63, "_len3"], [72, 94, 70, 63], [72, 96, 70, 63, "_key3"], [72, 101, 70, 63], [73, 12, 70, 59, "args"], [73, 16, 70, 63], [73, 17, 70, 63, "_key3"], [73, 22, 70, 63], [73, 26, 70, 63, "arguments"], [73, 35, 70, 63], [73, 36, 70, 63, "_key3"], [73, 41, 70, 63], [74, 10, 70, 63], [75, 10, 70, 63], [75, 17, 71, 8, "NativeModules"], [75, 43, 71, 21], [75, 44, 71, 22, "EXReactNativeEventEmitter"], [75, 69, 71, 47], [75, 70, 71, 48, "removeProxiedListeners"], [75, 92, 71, 70], [75, 93, 71, 71, "moduleName"], [75, 103, 71, 81], [75, 105, 71, 83], [75, 108, 71, 86, "args"], [75, 112, 71, 90], [75, 113, 71, 91], [76, 8, 71, 91], [77, 6, 72, 4], [77, 7, 72, 5], [77, 13, 72, 11], [78, 8, 73, 6], [79, 8, 74, 6], [80, 8, 75, 6], [81, 8, 76, 6, "NativeModulesProxy"], [81, 26, 76, 24], [81, 27, 76, 25, "moduleName"], [81, 37, 76, 35], [81, 38, 76, 36], [81, 39, 76, 37, "addListener"], [81, 50, 76, 48], [81, 53, 76, 51], [81, 59, 76, 57], [81, 60, 76, 58], [81, 61, 76, 59], [82, 8, 77, 6, "NativeModulesProxy"], [82, 26, 77, 24], [82, 27, 77, 25, "moduleName"], [82, 37, 77, 35], [82, 38, 77, 36], [82, 39, 77, 37, "removeListeners"], [82, 54, 77, 52], [82, 57, 77, 55], [82, 63, 77, 61], [82, 64, 77, 62], [82, 65, 77, 63], [83, 6, 78, 4], [84, 4, 79, 2], [84, 5, 79, 3], [84, 6, 79, 4], [85, 2, 80, 0], [85, 3, 80, 1], [85, 9, 80, 7], [86, 4, 81, 2, "console"], [86, 11, 81, 9], [86, 12, 81, 10, "warn"], [86, 16, 81, 14], [86, 17, 82, 4], [86, 161, 83, 2], [86, 162, 83, 3], [87, 2, 84, 0], [88, 2, 84, 1], [88, 6, 84, 1, "_default"], [88, 14, 84, 1], [88, 17, 84, 1, "exports"], [88, 24, 84, 1], [88, 25, 84, 1, "default"], [88, 32, 84, 1], [88, 35, 86, 15, "NativeModulesProxy"], [88, 53, 86, 33], [89, 0, 86, 33], [89, 3]], "functionMap": {"names": ["<global>", "Object.keys.forEach$argument_0", "NativeProxy.exportedMethodsKey.moduleName.forEach$argument_0", "moduleName.methodInfo.name", "moduleName.addListener", "moduleName.removeListeners"], "mappings": "AAA;uDC2B;wDCM;wDCC;ODmB;KDC;mDGa;uFHC;uDIC;2FJC;mDGK,QH;uDIC,QJ;GDE"}}, "type": "js/module"}]}