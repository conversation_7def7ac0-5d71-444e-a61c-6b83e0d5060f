{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function infoLog() {\n    return console.log(...arguments);\n  }\n  var _default = exports.default = infoLog;\n});", "lineCount": 12, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 16, 0], [8, 11, 16, 9, "infoLog"], [8, 18, 16, 16, "infoLog"], [8, 19, 16, 16], [8, 21, 16, 46], [9, 4, 17, 2], [9, 11, 17, 9, "console"], [9, 18, 17, 16], [9, 19, 17, 17, "log"], [9, 22, 17, 20], [9, 23, 17, 21], [9, 26, 17, 21, "arguments"], [9, 35, 17, 28], [9, 36, 17, 29], [10, 2, 18, 0], [11, 2, 18, 1], [11, 6, 18, 1, "_default"], [11, 14, 18, 1], [11, 17, 18, 1, "exports"], [11, 24, 18, 1], [11, 25, 18, 1, "default"], [11, 32, 18, 1], [11, 35, 20, 15, "infoLog"], [11, 42, 20, 22], [12, 0, 20, 22], [12, 3]], "functionMap": {"names": ["<global>", "infoLog"], "mappings": "AAA;ACe;CDE"}}, "type": "js/module"}]}