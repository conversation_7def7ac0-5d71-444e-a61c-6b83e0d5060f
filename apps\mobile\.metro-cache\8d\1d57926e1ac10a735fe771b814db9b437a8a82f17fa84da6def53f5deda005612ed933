{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 16, "index": 706}, "end": {"line": 21, "column": 32, "index": 722}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "use-sync-external-store/shim", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 22, "column": 13, "index": 737}, "end": {"line": 22, "column": 52, "index": 776}}], "key": "aKGLQ73LwOEJPCL0G86fBp+zBKQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * @license React\n   * use-sync-external-store-shim/with-selector.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  \"use strict\";\n\n  \"production\" !== process.env.NODE_ENV && function () {\n    function is(x, y) {\n      return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(_dependencyMap[0], \"react\"),\n      shim = require(_dependencyMap[1], \"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = {\n          hasValue: !1,\n          value: null\n        };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(function () {\n        function memoizedSelector(nextSnapshot) {\n          if (!hasMemo) {\n            hasMemo = !0;\n            memoizedSnapshot = nextSnapshot;\n            nextSnapshot = selector(nextSnapshot);\n            if (void 0 !== isEqual && inst.hasValue) {\n              var currentSelection = inst.value;\n              if (isEqual(currentSelection, nextSnapshot)) return memoizedSelection = currentSelection;\n            }\n            return memoizedSelection = nextSnapshot;\n          }\n          currentSelection = memoizedSelection;\n          if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;\n          var nextSelection = selector(nextSnapshot);\n          if (void 0 !== isEqual && isEqual(currentSelection, nextSelection)) return memoizedSnapshot = nextSnapshot, currentSelection;\n          memoizedSnapshot = nextSnapshot;\n          return memoizedSelection = nextSelection;\n        }\n        var hasMemo = !1,\n          memoizedSnapshot,\n          memoizedSelection,\n          maybeGetServerSnapshot = void 0 === getServerSnapshot ? null : getServerSnapshot;\n        return [function () {\n          return memoizedSelector(getSnapshot());\n        }, null === maybeGetServerSnapshot ? void 0 : function () {\n          return memoizedSelector(maybeGetServerSnapshot());\n        }];\n      }, [getSnapshot, getServerSnapshot, selector, isEqual]);\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(function () {\n        inst.hasValue = !0;\n        inst.value = value;\n      }, [value]);\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  }();\n});", "lineCount": 75, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 12, 0], [14, 14, 12, 12], [14, 19, 12, 17, "process"], [14, 26, 12, 24], [14, 27, 12, 25, "env"], [14, 30, 12, 28], [14, 31, 12, 29, "NODE_ENV"], [14, 39, 12, 37], [14, 43, 13, 3], [14, 55, 13, 15], [15, 4, 14, 4], [15, 13, 14, 13, "is"], [15, 15, 14, 15, "is"], [15, 16, 14, 16, "x"], [15, 17, 14, 17], [15, 19, 14, 19, "y"], [15, 20, 14, 20], [15, 22, 14, 22], [16, 6, 15, 6], [16, 13, 15, 14, "x"], [16, 14, 15, 15], [16, 19, 15, 20, "y"], [16, 20, 15, 21], [16, 25, 15, 26], [16, 26, 15, 27], [16, 31, 15, 32, "x"], [16, 32, 15, 33], [16, 36, 15, 37], [16, 37, 15, 38], [16, 40, 15, 41, "x"], [16, 41, 15, 42], [16, 46, 15, 47], [16, 47, 15, 48], [16, 50, 15, 51, "y"], [16, 51, 15, 52], [16, 52, 15, 53], [16, 56, 15, 59, "x"], [16, 57, 15, 60], [16, 62, 15, 65, "x"], [16, 63, 15, 66], [16, 67, 15, 70, "y"], [16, 68, 15, 71], [16, 73, 15, 76, "y"], [16, 74, 15, 78], [17, 4, 16, 4], [18, 4, 17, 4], [18, 15, 17, 15], [18, 20, 17, 20], [18, 27, 17, 27, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [18, 57, 17, 57], [18, 61, 18, 6], [18, 71, 18, 16], [18, 76, 19, 8], [18, 83, 19, 15, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [18, 113, 19, 45], [18, 114, 19, 46, "registerInternalModuleStart"], [18, 141, 19, 73], [18, 145, 20, 6, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [18, 175, 20, 36], [18, 176, 20, 37, "registerInternalModuleStart"], [18, 203, 20, 64], [18, 204, 20, 65, "Error"], [18, 209, 20, 70], [18, 210, 20, 71], [18, 211, 20, 72], [18, 212, 20, 73], [19, 4, 21, 4], [19, 8, 21, 8, "React"], [19, 13, 21, 13], [19, 16, 21, 16, "require"], [19, 23, 21, 23], [19, 24, 21, 23, "_dependencyMap"], [19, 38, 21, 23], [19, 50, 21, 31], [19, 51, 21, 32], [20, 6, 22, 6, "shim"], [20, 10, 22, 10], [20, 13, 22, 13, "require"], [20, 20, 22, 20], [20, 21, 22, 20, "_dependencyMap"], [20, 35, 22, 20], [20, 70, 22, 51], [20, 71, 22, 52], [21, 6, 23, 6, "objectIs"], [21, 14, 23, 14], [21, 17, 23, 17], [21, 27, 23, 27], [21, 32, 23, 32], [21, 39, 23, 39, "Object"], [21, 45, 23, 45], [21, 46, 23, 46, "is"], [21, 48, 23, 48], [21, 51, 23, 51, "Object"], [21, 57, 23, 57], [21, 58, 23, 58, "is"], [21, 60, 23, 60], [21, 63, 23, 63, "is"], [21, 65, 23, 65], [22, 6, 24, 6, "useSyncExternalStore"], [22, 26, 24, 26], [22, 29, 24, 29, "shim"], [22, 33, 24, 33], [22, 34, 24, 34, "useSyncExternalStore"], [22, 54, 24, 54], [23, 6, 25, 6, "useRef"], [23, 12, 25, 12], [23, 15, 25, 15, "React"], [23, 20, 25, 20], [23, 21, 25, 21, "useRef"], [23, 27, 25, 27], [24, 6, 26, 6, "useEffect"], [24, 15, 26, 15], [24, 18, 26, 18, "React"], [24, 23, 26, 23], [24, 24, 26, 24, "useEffect"], [24, 33, 26, 33], [25, 6, 27, 6, "useMemo"], [25, 13, 27, 13], [25, 16, 27, 16, "React"], [25, 21, 27, 21], [25, 22, 27, 22, "useMemo"], [25, 29, 27, 29], [26, 6, 28, 6, "useDebugValue"], [26, 19, 28, 19], [26, 22, 28, 22, "React"], [26, 27, 28, 27], [26, 28, 28, 28, "useDebugValue"], [26, 41, 28, 41], [27, 4, 29, 4, "exports"], [27, 11, 29, 11], [27, 12, 29, 12, "useSyncExternalStoreWithSelector"], [27, 44, 29, 44], [27, 47, 29, 47], [27, 57, 30, 6, "subscribe"], [27, 66, 30, 15], [27, 68, 31, 6, "getSnapshot"], [27, 79, 31, 17], [27, 81, 32, 6, "getServerSnapshot"], [27, 98, 32, 23], [27, 100, 33, 6, "selector"], [27, 108, 33, 14], [27, 110, 34, 6, "isEqual"], [27, 117, 34, 13], [27, 119, 35, 6], [28, 6, 36, 6], [28, 10, 36, 10, "instRef"], [28, 17, 36, 17], [28, 20, 36, 20, "useRef"], [28, 26, 36, 26], [28, 27, 36, 27], [28, 31, 36, 31], [28, 32, 36, 32], [29, 6, 37, 6], [29, 10, 37, 10], [29, 14, 37, 14], [29, 19, 37, 19, "instRef"], [29, 26, 37, 26], [29, 27, 37, 27, "current"], [29, 34, 37, 34], [29, 36, 37, 36], [30, 8, 38, 8], [30, 12, 38, 12, "inst"], [30, 16, 38, 16], [30, 19, 38, 19], [31, 10, 38, 21, "hasValue"], [31, 18, 38, 29], [31, 20, 38, 31], [31, 21, 38, 32], [31, 22, 38, 33], [32, 10, 38, 35, "value"], [32, 15, 38, 40], [32, 17, 38, 42], [33, 8, 38, 47], [33, 9, 38, 48], [34, 8, 39, 8, "instRef"], [34, 15, 39, 15], [34, 16, 39, 16, "current"], [34, 23, 39, 23], [34, 26, 39, 26, "inst"], [34, 30, 39, 30], [35, 6, 40, 6], [35, 7, 40, 7], [35, 13, 40, 13, "inst"], [35, 17, 40, 17], [35, 20, 40, 20, "instRef"], [35, 27, 40, 27], [35, 28, 40, 28, "current"], [35, 35, 40, 35], [36, 6, 41, 6, "instRef"], [36, 13, 41, 13], [36, 16, 41, 16, "useMemo"], [36, 23, 41, 23], [36, 24, 42, 8], [36, 36, 42, 20], [37, 8, 43, 10], [37, 17, 43, 19, "memoizedSelector"], [37, 33, 43, 35, "memoizedSelector"], [37, 34, 43, 36, "nextSnapshot"], [37, 46, 43, 48], [37, 48, 43, 50], [38, 10, 44, 12], [38, 14, 44, 16], [38, 15, 44, 17, "hasMemo"], [38, 22, 44, 24], [38, 24, 44, 26], [39, 12, 45, 14, "hasMemo"], [39, 19, 45, 21], [39, 22, 45, 24], [39, 23, 45, 25], [39, 24, 45, 26], [40, 12, 46, 14, "memoizedSnapshot"], [40, 28, 46, 30], [40, 31, 46, 33, "nextSnapshot"], [40, 43, 46, 45], [41, 12, 47, 14, "nextSnapshot"], [41, 24, 47, 26], [41, 27, 47, 29, "selector"], [41, 35, 47, 37], [41, 36, 47, 38, "nextSnapshot"], [41, 48, 47, 50], [41, 49, 47, 51], [42, 12, 48, 14], [42, 16, 48, 18], [42, 21, 48, 23], [42, 22, 48, 24], [42, 27, 48, 29, "isEqual"], [42, 34, 48, 36], [42, 38, 48, 40, "inst"], [42, 42, 48, 44], [42, 43, 48, 45, "hasValue"], [42, 51, 48, 53], [42, 53, 48, 55], [43, 14, 49, 16], [43, 18, 49, 20, "currentSelection"], [43, 34, 49, 36], [43, 37, 49, 39, "inst"], [43, 41, 49, 43], [43, 42, 49, 44, "value"], [43, 47, 49, 49], [44, 14, 50, 16], [44, 18, 50, 20, "isEqual"], [44, 25, 50, 27], [44, 26, 50, 28, "currentSelection"], [44, 42, 50, 44], [44, 44, 50, 46, "nextSnapshot"], [44, 56, 50, 58], [44, 57, 50, 59], [44, 59, 51, 18], [44, 66, 51, 26, "memoizedSelection"], [44, 83, 51, 43], [44, 86, 51, 46, "currentSelection"], [44, 102, 51, 62], [45, 12, 52, 14], [46, 12, 53, 14], [46, 19, 53, 22, "memoizedSelection"], [46, 36, 53, 39], [46, 39, 53, 42, "nextSnapshot"], [46, 51, 53, 54], [47, 10, 54, 12], [48, 10, 55, 12, "currentSelection"], [48, 26, 55, 28], [48, 29, 55, 31, "memoizedSelection"], [48, 46, 55, 48], [49, 10, 56, 12], [49, 14, 56, 16, "objectIs"], [49, 22, 56, 24], [49, 23, 56, 25, "memoizedSnapshot"], [49, 39, 56, 41], [49, 41, 56, 43, "nextSnapshot"], [49, 53, 56, 55], [49, 54, 56, 56], [49, 56, 57, 14], [49, 63, 57, 21, "currentSelection"], [49, 79, 57, 37], [50, 10, 58, 12], [50, 14, 58, 16, "nextSelection"], [50, 27, 58, 29], [50, 30, 58, 32, "selector"], [50, 38, 58, 40], [50, 39, 58, 41, "nextSnapshot"], [50, 51, 58, 53], [50, 52, 58, 54], [51, 10, 59, 12], [51, 14, 59, 16], [51, 19, 59, 21], [51, 20, 59, 22], [51, 25, 59, 27, "isEqual"], [51, 32, 59, 34], [51, 36, 59, 38, "isEqual"], [51, 43, 59, 45], [51, 44, 59, 46, "currentSelection"], [51, 60, 59, 62], [51, 62, 59, 64, "nextSelection"], [51, 75, 59, 77], [51, 76, 59, 78], [51, 78, 60, 14], [51, 85, 60, 22, "memoizedSnapshot"], [51, 101, 60, 38], [51, 104, 60, 41, "nextSnapshot"], [51, 116, 60, 53], [51, 118, 60, 56, "currentSelection"], [51, 134, 60, 72], [52, 10, 61, 12, "memoizedSnapshot"], [52, 26, 61, 28], [52, 29, 61, 31, "nextSnapshot"], [52, 41, 61, 43], [53, 10, 62, 12], [53, 17, 62, 20, "memoizedSelection"], [53, 34, 62, 37], [53, 37, 62, 40, "nextSelection"], [53, 50, 62, 53], [54, 8, 63, 10], [55, 8, 64, 10], [55, 12, 64, 14, "hasMemo"], [55, 19, 64, 21], [55, 22, 64, 24], [55, 23, 64, 25], [55, 24, 64, 26], [56, 10, 65, 12, "memoizedSnapshot"], [56, 26, 65, 28], [57, 10, 66, 12, "memoizedSelection"], [57, 27, 66, 29], [58, 10, 67, 12, "maybeGetServerSnapshot"], [58, 32, 67, 34], [58, 35, 68, 14], [58, 40, 68, 19], [58, 41, 68, 20], [58, 46, 68, 25, "getServerSnapshot"], [58, 63, 68, 42], [58, 66, 68, 45], [58, 70, 68, 49], [58, 73, 68, 52, "getServerSnapshot"], [58, 90, 68, 69], [59, 8, 69, 10], [59, 15, 69, 17], [59, 16, 70, 12], [59, 28, 70, 24], [60, 10, 71, 14], [60, 17, 71, 21, "memoizedSelector"], [60, 33, 71, 37], [60, 34, 71, 38, "getSnapshot"], [60, 45, 71, 49], [60, 46, 71, 50], [60, 47, 71, 51], [60, 48, 71, 52], [61, 8, 72, 12], [61, 9, 72, 13], [61, 11, 73, 12], [61, 15, 73, 16], [61, 20, 73, 21, "maybeGetServerSnapshot"], [61, 42, 73, 43], [61, 45, 74, 16], [61, 50, 74, 21], [61, 51, 74, 22], [61, 54, 75, 16], [61, 66, 75, 28], [62, 10, 76, 18], [62, 17, 76, 25, "memoizedSelector"], [62, 33, 76, 41], [62, 34, 76, 42, "maybeGetServerSnapshot"], [62, 56, 76, 64], [62, 57, 76, 65], [62, 58, 76, 66], [62, 59, 76, 67], [63, 8, 77, 16], [63, 9, 77, 17], [63, 10, 78, 11], [64, 6, 79, 8], [64, 7, 79, 9], [64, 9, 80, 8], [64, 10, 80, 9, "getSnapshot"], [64, 21, 80, 20], [64, 23, 80, 22, "getServerSnapshot"], [64, 40, 80, 39], [64, 42, 80, 41, "selector"], [64, 50, 80, 49], [64, 52, 80, 51, "isEqual"], [64, 59, 80, 58], [64, 60, 81, 6], [64, 61, 81, 7], [65, 6, 82, 6], [65, 10, 82, 10, "value"], [65, 15, 82, 15], [65, 18, 82, 18, "useSyncExternalStore"], [65, 38, 82, 38], [65, 39, 82, 39, "subscribe"], [65, 48, 82, 48], [65, 50, 82, 50, "instRef"], [65, 57, 82, 57], [65, 58, 82, 58], [65, 59, 82, 59], [65, 60, 82, 60], [65, 62, 82, 62, "instRef"], [65, 69, 82, 69], [65, 70, 82, 70], [65, 71, 82, 71], [65, 72, 82, 72], [65, 73, 82, 73], [66, 6, 83, 6, "useEffect"], [66, 15, 83, 15], [66, 16, 84, 8], [66, 28, 84, 20], [67, 8, 85, 10, "inst"], [67, 12, 85, 14], [67, 13, 85, 15, "hasValue"], [67, 21, 85, 23], [67, 24, 85, 26], [67, 25, 85, 27], [67, 26, 85, 28], [68, 8, 86, 10, "inst"], [68, 12, 86, 14], [68, 13, 86, 15, "value"], [68, 18, 86, 20], [68, 21, 86, 23, "value"], [68, 26, 86, 28], [69, 6, 87, 8], [69, 7, 87, 9], [69, 9, 88, 8], [69, 10, 88, 9, "value"], [69, 15, 88, 14], [69, 16, 89, 6], [69, 17, 89, 7], [70, 6, 90, 6, "useDebugValue"], [70, 19, 90, 19], [70, 20, 90, 20, "value"], [70, 25, 90, 25], [70, 26, 90, 26], [71, 6, 91, 6], [71, 13, 91, 13, "value"], [71, 18, 91, 18], [72, 4, 92, 4], [72, 5, 92, 5], [73, 4, 93, 4], [73, 15, 93, 15], [73, 20, 93, 20], [73, 27, 93, 27, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [73, 57, 93, 57], [73, 61, 94, 6], [73, 71, 94, 16], [73, 76, 95, 8], [73, 83, 95, 15, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [73, 113, 95, 45], [73, 114, 95, 46, "registerInternalModuleStop"], [73, 140, 95, 72], [73, 144, 96, 6, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [73, 174, 96, 36], [73, 175, 96, 37, "registerInternalModuleStop"], [73, 201, 96, 63], [73, 202, 96, 64, "Error"], [73, 207, 96, 69], [73, 208, 96, 70], [73, 209, 96, 71], [73, 210, 96, 72], [74, 2, 97, 2], [74, 3, 97, 3], [74, 4, 97, 5], [74, 5, 97, 6], [75, 0, 97, 7], [75, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "is", "exports.useSyncExternalStoreWithSelector", "useMemo$argument_0", "memoizedSelector", "useEffect$argument_0"], "mappings": "AAA;GCY;ICC;KDE;+CEa;QCa;UCC;WDoB;YHO;aGE;gBHG;iBGE;SDE;QGK;SHG;KFK;GDK"}}, "type": "js/module"}]}