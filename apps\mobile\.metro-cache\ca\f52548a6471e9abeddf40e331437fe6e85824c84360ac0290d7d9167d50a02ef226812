{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createIconSet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "PQt9ucTb+ABlKWjDhj7L4XHxOIA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = _default;\n  var _createIconSet = _interopRequireDefault(require(_dependencyMap[1], \"./createIconSet\"));\n  function _default(config, expoFontName, expoAssetId) {\n    var glyphMap = {};\n    config.icons.forEach(icon => {\n      icon.properties.name.split(/\\s*,\\s*/g).forEach(name => {\n        glyphMap[name] = icon.properties.code;\n      });\n    });\n    var fontFamily = expoFontName || config.preferences.fontPref.metadata.fontFamily;\n    return (0, _createIconSet.default)(glyphMap, fontFamily, expoAssetId || `${fontFamily}.ttf`);\n  }\n});", "lineCount": 18, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_createIconSet"], [7, 20, 1, 0], [7, 23, 1, 0, "_interopRequireDefault"], [7, 45, 1, 0], [7, 46, 1, 0, "require"], [7, 53, 1, 0], [7, 54, 1, 0, "_dependencyMap"], [7, 68, 1, 0], [8, 2, 2, 15], [8, 11, 2, 15, "_default"], [8, 20, 2, 25, "config"], [8, 26, 2, 31], [8, 28, 2, 33, "expoFontName"], [8, 40, 2, 45], [8, 42, 2, 47, "expoAssetId"], [8, 53, 2, 58], [8, 55, 2, 60], [9, 4, 3, 4], [9, 8, 3, 10, "glyphMap"], [9, 16, 3, 18], [9, 19, 3, 21], [9, 20, 3, 22], [9, 21, 3, 23], [10, 4, 4, 4, "config"], [10, 10, 4, 10], [10, 11, 4, 11, "icons"], [10, 16, 4, 16], [10, 17, 4, 17, "for<PERSON>ach"], [10, 24, 4, 24], [10, 25, 4, 26, "icon"], [10, 29, 4, 30], [10, 33, 4, 35], [11, 6, 5, 8, "icon"], [11, 10, 5, 12], [11, 11, 5, 13, "properties"], [11, 21, 5, 23], [11, 22, 5, 24, "name"], [11, 26, 5, 28], [11, 27, 5, 29, "split"], [11, 32, 5, 34], [11, 33, 5, 35], [11, 43, 5, 45], [11, 44, 5, 46], [11, 45, 5, 47, "for<PERSON>ach"], [11, 52, 5, 54], [11, 53, 5, 56, "name"], [11, 57, 5, 60], [11, 61, 5, 65], [12, 8, 6, 12, "glyphMap"], [12, 16, 6, 20], [12, 17, 6, 21, "name"], [12, 21, 6, 25], [12, 22, 6, 26], [12, 25, 6, 29, "icon"], [12, 29, 6, 33], [12, 30, 6, 34, "properties"], [12, 40, 6, 44], [12, 41, 6, 45, "code"], [12, 45, 6, 49], [13, 6, 7, 8], [13, 7, 7, 9], [13, 8, 7, 10], [14, 4, 8, 4], [14, 5, 8, 5], [14, 6, 8, 6], [15, 4, 9, 4], [15, 8, 9, 10, "fontFamily"], [15, 18, 9, 20], [15, 21, 9, 23, "expoFontName"], [15, 33, 9, 35], [15, 37, 9, 39, "config"], [15, 43, 9, 45], [15, 44, 9, 46, "preferences"], [15, 55, 9, 57], [15, 56, 9, 58, "fontPref"], [15, 64, 9, 66], [15, 65, 9, 67, "metadata"], [15, 73, 9, 75], [15, 74, 9, 76, "fontFamily"], [15, 84, 9, 86], [16, 4, 10, 4], [16, 11, 10, 11], [16, 15, 10, 11, "createIconSet"], [16, 37, 10, 24], [16, 39, 10, 25, "glyphMap"], [16, 47, 10, 33], [16, 49, 10, 35, "fontFamily"], [16, 59, 10, 45], [16, 61, 10, 47, "expoAssetId"], [16, 72, 10, 58], [16, 76, 10, 62], [16, 79, 10, 65, "fontFamily"], [16, 89, 10, 75], [16, 95, 10, 81], [16, 96, 10, 82], [17, 2, 11, 0], [18, 0, 11, 1], [18, 3]], "functionMap": {"names": ["<global>", "default", "config.icons.forEach$argument_0", "icon.properties.name.split.forEach$argument_0"], "mappings": "AAA;eCC;yBCE;uDCC;SDE;KDC;CDG"}}, "type": "js/module"}]}