{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 83}, "end": {"line": 2, "column": 44, "index": 127}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 128}, "end": {"line": 6, "column": 32, "index": 223}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PanGestureHandler = void 0;\n  exports.managePanProps = managePanProps;\n  exports.panHandlerName = exports.panGestureHandlerProps = exports.panGestureHandlerCustomNativeProps = void 0;\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[1], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[2], \"./gestureHandlerCommon\");\n  var panGestureHandlerProps = exports.panGestureHandlerProps = ['activeOffsetY', 'activeOffsetX', 'failOffsetY', 'failOffsetX', 'minDist', 'minVelocity', 'minVelocityX', 'minVelocityY', 'minPointers', 'maxPointers', 'avgTouches', 'enableTrackpadTwoFingerGesture', 'activateAfterLongPress'];\n  var panGestureHandlerCustomNativeProps = exports.panGestureHandlerCustomNativeProps = ['activeOffsetYStart', 'activeOffsetYEnd', 'activeOffsetXStart', 'activeOffsetXEnd', 'failOffsetYStart', 'failOffsetYEnd', 'failOffsetXStart', 'failOffsetXEnd'];\n\n  /**\n   * @deprecated PanGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pan()` instead.\n   */\n\n  var panHandlerName = exports.panHandlerName = 'PanGestureHandler';\n\n  /**\n   * @deprecated PanGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pan()` instead.\n   */\n\n  /**\n   * @deprecated PanGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pan()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n  var PanGestureHandler = exports.PanGestureHandler = (0, _createHandler.default)({\n    name: panHandlerName,\n    allowedProps: [..._gestureHandlerCommon.baseGestureHandlerProps, ...panGestureHandlerProps],\n    config: {},\n    transformProps: managePanProps,\n    customNativeProps: panGestureHandlerCustomNativeProps\n  });\n  function validatePanGestureHandlerProps(props) {\n    if (Array.isArray(props.activeOffsetX) && (props.activeOffsetX[0] > 0 || props.activeOffsetX[1] < 0)) {\n      throw new Error(`First element of activeOffsetX should be negative, a the second one should be positive`);\n    }\n    if (Array.isArray(props.activeOffsetY) && (props.activeOffsetY[0] > 0 || props.activeOffsetY[1] < 0)) {\n      throw new Error(`First element of activeOffsetY should be negative, a the second one should be positive`);\n    }\n    if (Array.isArray(props.failOffsetX) && (props.failOffsetX[0] > 0 || props.failOffsetX[1] < 0)) {\n      throw new Error(`First element of failOffsetX should be negative, a the second one should be positive`);\n    }\n    if (Array.isArray(props.failOffsetY) && (props.failOffsetY[0] > 0 || props.failOffsetY[1] < 0)) {\n      throw new Error(`First element of failOffsetY should be negative, a the second one should be positive`);\n    }\n    if (props.minDist && (props.failOffsetX || props.failOffsetY)) {\n      throw new Error(`It is not supported to use minDist with failOffsetX or failOffsetY, use activeOffsetX and activeOffsetY instead`);\n    }\n    if (props.minDist && (props.activeOffsetX || props.activeOffsetY)) {\n      throw new Error(`It is not supported to use minDist with activeOffsetX or activeOffsetY`);\n    }\n  }\n  function transformPanGestureHandlerProps(props) {\n    var res = {\n      ...props\n    };\n    if (props.activeOffsetX !== undefined) {\n      delete res.activeOffsetX;\n      if (Array.isArray(props.activeOffsetX)) {\n        res.activeOffsetXStart = props.activeOffsetX[0];\n        res.activeOffsetXEnd = props.activeOffsetX[1];\n      } else if (props.activeOffsetX < 0) {\n        res.activeOffsetXStart = props.activeOffsetX;\n      } else {\n        res.activeOffsetXEnd = props.activeOffsetX;\n      }\n    }\n    if (props.activeOffsetY !== undefined) {\n      delete res.activeOffsetY;\n      if (Array.isArray(props.activeOffsetY)) {\n        res.activeOffsetYStart = props.activeOffsetY[0];\n        res.activeOffsetYEnd = props.activeOffsetY[1];\n      } else if (props.activeOffsetY < 0) {\n        res.activeOffsetYStart = props.activeOffsetY;\n      } else {\n        res.activeOffsetYEnd = props.activeOffsetY;\n      }\n    }\n    if (props.failOffsetX !== undefined) {\n      delete res.failOffsetX;\n      if (Array.isArray(props.failOffsetX)) {\n        res.failOffsetXStart = props.failOffsetX[0];\n        res.failOffsetXEnd = props.failOffsetX[1];\n      } else if (props.failOffsetX < 0) {\n        res.failOffsetXStart = props.failOffsetX;\n      } else {\n        res.failOffsetXEnd = props.failOffsetX;\n      }\n    }\n    if (props.failOffsetY !== undefined) {\n      delete res.failOffsetY;\n      if (Array.isArray(props.failOffsetY)) {\n        res.failOffsetYStart = props.failOffsetY[0];\n        res.failOffsetYEnd = props.failOffsetY[1];\n      } else if (props.failOffsetY < 0) {\n        res.failOffsetYStart = props.failOffsetY;\n      } else {\n        res.failOffsetYEnd = props.failOffsetY;\n      }\n    }\n    return res;\n  }\n  function managePanProps(props) {\n    if (__DEV__) {\n      validatePanGestureHandlerProps(props);\n    }\n    return transformPanGestureHandlerProps(props);\n  }\n});", "lineCount": 111, "map": [[9, 2, 2, 0], [9, 6, 2, 0, "_createHandler"], [9, 20, 2, 0], [9, 23, 2, 0, "_interopRequireDefault"], [9, 45, 2, 0], [9, 46, 2, 0, "require"], [9, 53, 2, 0], [9, 54, 2, 0, "_dependencyMap"], [9, 68, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 27, 3, 0], [10, 30, 3, 0, "require"], [10, 37, 3, 0], [10, 38, 3, 0, "_dependencyMap"], [10, 52, 3, 0], [11, 2, 8, 7], [11, 6, 8, 13, "panGestureHandlerProps"], [11, 28, 8, 35], [11, 31, 8, 35, "exports"], [11, 38, 8, 35], [11, 39, 8, 35, "panGestureHandlerProps"], [11, 61, 8, 35], [11, 64, 8, 38], [11, 65, 9, 2], [11, 80, 9, 17], [11, 82, 10, 2], [11, 97, 10, 17], [11, 99, 11, 2], [11, 112, 11, 15], [11, 114, 12, 2], [11, 127, 12, 15], [11, 129, 13, 2], [11, 138, 13, 11], [11, 140, 14, 2], [11, 153, 14, 15], [11, 155, 15, 2], [11, 169, 15, 16], [11, 171, 16, 2], [11, 185, 16, 16], [11, 187, 17, 2], [11, 200, 17, 15], [11, 202, 18, 2], [11, 215, 18, 15], [11, 217, 19, 2], [11, 229, 19, 14], [11, 231, 20, 2], [11, 263, 20, 34], [11, 265, 21, 2], [11, 289, 21, 26], [11, 290, 22, 10], [12, 2, 24, 7], [12, 6, 24, 13, "panGestureHandlerCustomNativeProps"], [12, 40, 24, 47], [12, 43, 24, 47, "exports"], [12, 50, 24, 47], [12, 51, 24, 47, "panGestureHandlerCustomNativeProps"], [12, 85, 24, 47], [12, 88, 24, 50], [12, 89, 25, 2], [12, 109, 25, 22], [12, 111, 26, 2], [12, 129, 26, 20], [12, 131, 27, 2], [12, 151, 27, 22], [12, 153, 28, 2], [12, 171, 28, 20], [12, 173, 29, 2], [12, 191, 29, 20], [12, 193, 30, 2], [12, 209, 30, 18], [12, 211, 31, 2], [12, 229, 31, 20], [12, 231, 32, 2], [12, 247, 32, 18], [12, 248, 33, 10], [14, 2, 85, 0], [15, 0, 86, 0], [16, 0, 87, 0], [18, 2, 136, 7], [18, 6, 136, 13, "panHandlerName"], [18, 20, 136, 27], [18, 23, 136, 27, "exports"], [18, 30, 136, 27], [18, 31, 136, 27, "panHandlerName"], [18, 45, 136, 27], [18, 48, 136, 30], [18, 67, 136, 49], [20, 2, 138, 0], [21, 0, 139, 0], [22, 0, 140, 0], [24, 2, 143, 0], [25, 0, 144, 0], [26, 0, 145, 0], [27, 2, 146, 0], [28, 2, 147, 7], [28, 6, 147, 13, "PanGestureHandler"], [28, 23, 147, 30], [28, 26, 147, 30, "exports"], [28, 33, 147, 30], [28, 34, 147, 30, "PanGestureHandler"], [28, 51, 147, 30], [28, 54, 147, 33], [28, 58, 147, 33, "createHandler"], [28, 80, 147, 46], [28, 82, 150, 2], [29, 4, 151, 2, "name"], [29, 8, 151, 6], [29, 10, 151, 8, "panHandlerName"], [29, 24, 151, 22], [30, 4, 152, 2, "allowedProps"], [30, 16, 152, 14], [30, 18, 152, 16], [30, 19, 153, 4], [30, 22, 153, 7, "baseGestureHandlerProps"], [30, 67, 153, 30], [30, 69, 154, 4], [30, 72, 154, 7, "panGestureHandlerProps"], [30, 94, 154, 29], [30, 95, 155, 12], [31, 4, 156, 2, "config"], [31, 10, 156, 8], [31, 12, 156, 10], [31, 13, 156, 11], [31, 14, 156, 12], [32, 4, 157, 2, "transformProps"], [32, 18, 157, 16], [32, 20, 157, 18, "managePanProps"], [32, 34, 157, 32], [33, 4, 158, 2, "customNativeProps"], [33, 21, 158, 19], [33, 23, 158, 21, "panGestureHandlerCustomNativeProps"], [34, 2, 159, 0], [34, 3, 159, 1], [34, 4, 159, 2], [35, 2, 161, 0], [35, 11, 161, 9, "validatePanGestureHandlerProps"], [35, 41, 161, 39, "validatePanGestureHandlerProps"], [35, 42, 161, 40, "props"], [35, 47, 161, 69], [35, 49, 161, 71], [36, 4, 162, 2], [36, 8, 163, 4, "Array"], [36, 13, 163, 9], [36, 14, 163, 10, "isArray"], [36, 21, 163, 17], [36, 22, 163, 18, "props"], [36, 27, 163, 23], [36, 28, 163, 24, "activeOffsetX"], [36, 41, 163, 37], [36, 42, 163, 38], [36, 47, 164, 5, "props"], [36, 52, 164, 10], [36, 53, 164, 11, "activeOffsetX"], [36, 66, 164, 24], [36, 67, 164, 25], [36, 68, 164, 26], [36, 69, 164, 27], [36, 72, 164, 30], [36, 73, 164, 31], [36, 77, 164, 35, "props"], [36, 82, 164, 40], [36, 83, 164, 41, "activeOffsetX"], [36, 96, 164, 54], [36, 97, 164, 55], [36, 98, 164, 56], [36, 99, 164, 57], [36, 102, 164, 60], [36, 103, 164, 61], [36, 104, 164, 62], [36, 106, 165, 4], [37, 6, 166, 4], [37, 12, 166, 10], [37, 16, 166, 14, "Error"], [37, 21, 166, 19], [37, 22, 167, 6], [37, 110, 168, 4], [37, 111, 168, 5], [38, 4, 169, 2], [39, 4, 171, 2], [39, 8, 172, 4, "Array"], [39, 13, 172, 9], [39, 14, 172, 10, "isArray"], [39, 21, 172, 17], [39, 22, 172, 18, "props"], [39, 27, 172, 23], [39, 28, 172, 24, "activeOffsetY"], [39, 41, 172, 37], [39, 42, 172, 38], [39, 47, 173, 5, "props"], [39, 52, 173, 10], [39, 53, 173, 11, "activeOffsetY"], [39, 66, 173, 24], [39, 67, 173, 25], [39, 68, 173, 26], [39, 69, 173, 27], [39, 72, 173, 30], [39, 73, 173, 31], [39, 77, 173, 35, "props"], [39, 82, 173, 40], [39, 83, 173, 41, "activeOffsetY"], [39, 96, 173, 54], [39, 97, 173, 55], [39, 98, 173, 56], [39, 99, 173, 57], [39, 102, 173, 60], [39, 103, 173, 61], [39, 104, 173, 62], [39, 106, 174, 4], [40, 6, 175, 4], [40, 12, 175, 10], [40, 16, 175, 14, "Error"], [40, 21, 175, 19], [40, 22, 176, 6], [40, 110, 177, 4], [40, 111, 177, 5], [41, 4, 178, 2], [42, 4, 180, 2], [42, 8, 181, 4, "Array"], [42, 13, 181, 9], [42, 14, 181, 10, "isArray"], [42, 21, 181, 17], [42, 22, 181, 18, "props"], [42, 27, 181, 23], [42, 28, 181, 24, "failOffsetX"], [42, 39, 181, 35], [42, 40, 181, 36], [42, 45, 182, 5, "props"], [42, 50, 182, 10], [42, 51, 182, 11, "failOffsetX"], [42, 62, 182, 22], [42, 63, 182, 23], [42, 64, 182, 24], [42, 65, 182, 25], [42, 68, 182, 28], [42, 69, 182, 29], [42, 73, 182, 33, "props"], [42, 78, 182, 38], [42, 79, 182, 39, "failOffsetX"], [42, 90, 182, 50], [42, 91, 182, 51], [42, 92, 182, 52], [42, 93, 182, 53], [42, 96, 182, 56], [42, 97, 182, 57], [42, 98, 182, 58], [42, 100, 183, 4], [43, 6, 184, 4], [43, 12, 184, 10], [43, 16, 184, 14, "Error"], [43, 21, 184, 19], [43, 22, 185, 6], [43, 108, 186, 4], [43, 109, 186, 5], [44, 4, 187, 2], [45, 4, 189, 2], [45, 8, 190, 4, "Array"], [45, 13, 190, 9], [45, 14, 190, 10, "isArray"], [45, 21, 190, 17], [45, 22, 190, 18, "props"], [45, 27, 190, 23], [45, 28, 190, 24, "failOffsetY"], [45, 39, 190, 35], [45, 40, 190, 36], [45, 45, 191, 5, "props"], [45, 50, 191, 10], [45, 51, 191, 11, "failOffsetY"], [45, 62, 191, 22], [45, 63, 191, 23], [45, 64, 191, 24], [45, 65, 191, 25], [45, 68, 191, 28], [45, 69, 191, 29], [45, 73, 191, 33, "props"], [45, 78, 191, 38], [45, 79, 191, 39, "failOffsetY"], [45, 90, 191, 50], [45, 91, 191, 51], [45, 92, 191, 52], [45, 93, 191, 53], [45, 96, 191, 56], [45, 97, 191, 57], [45, 98, 191, 58], [45, 100, 192, 4], [46, 6, 193, 4], [46, 12, 193, 10], [46, 16, 193, 14, "Error"], [46, 21, 193, 19], [46, 22, 194, 6], [46, 108, 195, 4], [46, 109, 195, 5], [47, 4, 196, 2], [48, 4, 198, 2], [48, 8, 198, 6, "props"], [48, 13, 198, 11], [48, 14, 198, 12, "minDist"], [48, 21, 198, 19], [48, 26, 198, 24, "props"], [48, 31, 198, 29], [48, 32, 198, 30, "failOffsetX"], [48, 43, 198, 41], [48, 47, 198, 45, "props"], [48, 52, 198, 50], [48, 53, 198, 51, "failOffsetY"], [48, 64, 198, 62], [48, 65, 198, 63], [48, 67, 198, 65], [49, 6, 199, 4], [49, 12, 199, 10], [49, 16, 199, 14, "Error"], [49, 21, 199, 19], [49, 22, 200, 6], [49, 135, 201, 4], [49, 136, 201, 5], [50, 4, 202, 2], [51, 4, 204, 2], [51, 8, 204, 6, "props"], [51, 13, 204, 11], [51, 14, 204, 12, "minDist"], [51, 21, 204, 19], [51, 26, 204, 24, "props"], [51, 31, 204, 29], [51, 32, 204, 30, "activeOffsetX"], [51, 45, 204, 43], [51, 49, 204, 47, "props"], [51, 54, 204, 52], [51, 55, 204, 53, "activeOffsetY"], [51, 68, 204, 66], [51, 69, 204, 67], [51, 71, 204, 69], [52, 6, 205, 4], [52, 12, 205, 10], [52, 16, 205, 14, "Error"], [52, 21, 205, 19], [52, 22, 206, 6], [52, 94, 207, 4], [52, 95, 207, 5], [53, 4, 208, 2], [54, 2, 209, 0], [55, 2, 211, 0], [55, 11, 211, 9, "transformPanGestureHandlerProps"], [55, 42, 211, 40, "transformPanGestureHandlerProps"], [55, 43, 211, 41, "props"], [55, 48, 211, 70], [55, 50, 211, 72], [56, 4, 224, 2], [56, 8, 224, 8, "res"], [56, 11, 224, 43], [56, 14, 224, 46], [57, 6, 224, 48], [57, 9, 224, 51, "props"], [58, 4, 224, 57], [58, 5, 224, 58], [59, 4, 226, 2], [59, 8, 226, 6, "props"], [59, 13, 226, 11], [59, 14, 226, 12, "activeOffsetX"], [59, 27, 226, 25], [59, 32, 226, 30, "undefined"], [59, 41, 226, 39], [59, 43, 226, 41], [60, 6, 227, 4], [60, 13, 227, 11, "res"], [60, 16, 227, 14], [60, 17, 227, 15, "activeOffsetX"], [60, 30, 227, 28], [61, 6, 228, 4], [61, 10, 228, 8, "Array"], [61, 15, 228, 13], [61, 16, 228, 14, "isArray"], [61, 23, 228, 21], [61, 24, 228, 22, "props"], [61, 29, 228, 27], [61, 30, 228, 28, "activeOffsetX"], [61, 43, 228, 41], [61, 44, 228, 42], [61, 46, 228, 44], [62, 8, 229, 6, "res"], [62, 11, 229, 9], [62, 12, 229, 10, "activeOffsetXStart"], [62, 30, 229, 28], [62, 33, 229, 31, "props"], [62, 38, 229, 36], [62, 39, 229, 37, "activeOffsetX"], [62, 52, 229, 50], [62, 53, 229, 51], [62, 54, 229, 52], [62, 55, 229, 53], [63, 8, 230, 6, "res"], [63, 11, 230, 9], [63, 12, 230, 10, "activeOffsetXEnd"], [63, 28, 230, 26], [63, 31, 230, 29, "props"], [63, 36, 230, 34], [63, 37, 230, 35, "activeOffsetX"], [63, 50, 230, 48], [63, 51, 230, 49], [63, 52, 230, 50], [63, 53, 230, 51], [64, 6, 231, 4], [64, 7, 231, 5], [64, 13, 231, 11], [64, 17, 231, 15, "props"], [64, 22, 231, 20], [64, 23, 231, 21, "activeOffsetX"], [64, 36, 231, 34], [64, 39, 231, 37], [64, 40, 231, 38], [64, 42, 231, 40], [65, 8, 232, 6, "res"], [65, 11, 232, 9], [65, 12, 232, 10, "activeOffsetXStart"], [65, 30, 232, 28], [65, 33, 232, 31, "props"], [65, 38, 232, 36], [65, 39, 232, 37, "activeOffsetX"], [65, 52, 232, 50], [66, 6, 233, 4], [66, 7, 233, 5], [66, 13, 233, 11], [67, 8, 234, 6, "res"], [67, 11, 234, 9], [67, 12, 234, 10, "activeOffsetXEnd"], [67, 28, 234, 26], [67, 31, 234, 29, "props"], [67, 36, 234, 34], [67, 37, 234, 35, "activeOffsetX"], [67, 50, 234, 48], [68, 6, 235, 4], [69, 4, 236, 2], [70, 4, 238, 2], [70, 8, 238, 6, "props"], [70, 13, 238, 11], [70, 14, 238, 12, "activeOffsetY"], [70, 27, 238, 25], [70, 32, 238, 30, "undefined"], [70, 41, 238, 39], [70, 43, 238, 41], [71, 6, 239, 4], [71, 13, 239, 11, "res"], [71, 16, 239, 14], [71, 17, 239, 15, "activeOffsetY"], [71, 30, 239, 28], [72, 6, 240, 4], [72, 10, 240, 8, "Array"], [72, 15, 240, 13], [72, 16, 240, 14, "isArray"], [72, 23, 240, 21], [72, 24, 240, 22, "props"], [72, 29, 240, 27], [72, 30, 240, 28, "activeOffsetY"], [72, 43, 240, 41], [72, 44, 240, 42], [72, 46, 240, 44], [73, 8, 241, 6, "res"], [73, 11, 241, 9], [73, 12, 241, 10, "activeOffsetYStart"], [73, 30, 241, 28], [73, 33, 241, 31, "props"], [73, 38, 241, 36], [73, 39, 241, 37, "activeOffsetY"], [73, 52, 241, 50], [73, 53, 241, 51], [73, 54, 241, 52], [73, 55, 241, 53], [74, 8, 242, 6, "res"], [74, 11, 242, 9], [74, 12, 242, 10, "activeOffsetYEnd"], [74, 28, 242, 26], [74, 31, 242, 29, "props"], [74, 36, 242, 34], [74, 37, 242, 35, "activeOffsetY"], [74, 50, 242, 48], [74, 51, 242, 49], [74, 52, 242, 50], [74, 53, 242, 51], [75, 6, 243, 4], [75, 7, 243, 5], [75, 13, 243, 11], [75, 17, 243, 15, "props"], [75, 22, 243, 20], [75, 23, 243, 21, "activeOffsetY"], [75, 36, 243, 34], [75, 39, 243, 37], [75, 40, 243, 38], [75, 42, 243, 40], [76, 8, 244, 6, "res"], [76, 11, 244, 9], [76, 12, 244, 10, "activeOffsetYStart"], [76, 30, 244, 28], [76, 33, 244, 31, "props"], [76, 38, 244, 36], [76, 39, 244, 37, "activeOffsetY"], [76, 52, 244, 50], [77, 6, 245, 4], [77, 7, 245, 5], [77, 13, 245, 11], [78, 8, 246, 6, "res"], [78, 11, 246, 9], [78, 12, 246, 10, "activeOffsetYEnd"], [78, 28, 246, 26], [78, 31, 246, 29, "props"], [78, 36, 246, 34], [78, 37, 246, 35, "activeOffsetY"], [78, 50, 246, 48], [79, 6, 247, 4], [80, 4, 248, 2], [81, 4, 250, 2], [81, 8, 250, 6, "props"], [81, 13, 250, 11], [81, 14, 250, 12, "failOffsetX"], [81, 25, 250, 23], [81, 30, 250, 28, "undefined"], [81, 39, 250, 37], [81, 41, 250, 39], [82, 6, 251, 4], [82, 13, 251, 11, "res"], [82, 16, 251, 14], [82, 17, 251, 15, "failOffsetX"], [82, 28, 251, 26], [83, 6, 252, 4], [83, 10, 252, 8, "Array"], [83, 15, 252, 13], [83, 16, 252, 14, "isArray"], [83, 23, 252, 21], [83, 24, 252, 22, "props"], [83, 29, 252, 27], [83, 30, 252, 28, "failOffsetX"], [83, 41, 252, 39], [83, 42, 252, 40], [83, 44, 252, 42], [84, 8, 253, 6, "res"], [84, 11, 253, 9], [84, 12, 253, 10, "failOffsetXStart"], [84, 28, 253, 26], [84, 31, 253, 29, "props"], [84, 36, 253, 34], [84, 37, 253, 35, "failOffsetX"], [84, 48, 253, 46], [84, 49, 253, 47], [84, 50, 253, 48], [84, 51, 253, 49], [85, 8, 254, 6, "res"], [85, 11, 254, 9], [85, 12, 254, 10, "failOffsetXEnd"], [85, 26, 254, 24], [85, 29, 254, 27, "props"], [85, 34, 254, 32], [85, 35, 254, 33, "failOffsetX"], [85, 46, 254, 44], [85, 47, 254, 45], [85, 48, 254, 46], [85, 49, 254, 47], [86, 6, 255, 4], [86, 7, 255, 5], [86, 13, 255, 11], [86, 17, 255, 15, "props"], [86, 22, 255, 20], [86, 23, 255, 21, "failOffsetX"], [86, 34, 255, 32], [86, 37, 255, 35], [86, 38, 255, 36], [86, 40, 255, 38], [87, 8, 256, 6, "res"], [87, 11, 256, 9], [87, 12, 256, 10, "failOffsetXStart"], [87, 28, 256, 26], [87, 31, 256, 29, "props"], [87, 36, 256, 34], [87, 37, 256, 35, "failOffsetX"], [87, 48, 256, 46], [88, 6, 257, 4], [88, 7, 257, 5], [88, 13, 257, 11], [89, 8, 258, 6, "res"], [89, 11, 258, 9], [89, 12, 258, 10, "failOffsetXEnd"], [89, 26, 258, 24], [89, 29, 258, 27, "props"], [89, 34, 258, 32], [89, 35, 258, 33, "failOffsetX"], [89, 46, 258, 44], [90, 6, 259, 4], [91, 4, 260, 2], [92, 4, 262, 2], [92, 8, 262, 6, "props"], [92, 13, 262, 11], [92, 14, 262, 12, "failOffsetY"], [92, 25, 262, 23], [92, 30, 262, 28, "undefined"], [92, 39, 262, 37], [92, 41, 262, 39], [93, 6, 263, 4], [93, 13, 263, 11, "res"], [93, 16, 263, 14], [93, 17, 263, 15, "failOffsetY"], [93, 28, 263, 26], [94, 6, 264, 4], [94, 10, 264, 8, "Array"], [94, 15, 264, 13], [94, 16, 264, 14, "isArray"], [94, 23, 264, 21], [94, 24, 264, 22, "props"], [94, 29, 264, 27], [94, 30, 264, 28, "failOffsetY"], [94, 41, 264, 39], [94, 42, 264, 40], [94, 44, 264, 42], [95, 8, 265, 6, "res"], [95, 11, 265, 9], [95, 12, 265, 10, "failOffsetYStart"], [95, 28, 265, 26], [95, 31, 265, 29, "props"], [95, 36, 265, 34], [95, 37, 265, 35, "failOffsetY"], [95, 48, 265, 46], [95, 49, 265, 47], [95, 50, 265, 48], [95, 51, 265, 49], [96, 8, 266, 6, "res"], [96, 11, 266, 9], [96, 12, 266, 10, "failOffsetYEnd"], [96, 26, 266, 24], [96, 29, 266, 27, "props"], [96, 34, 266, 32], [96, 35, 266, 33, "failOffsetY"], [96, 46, 266, 44], [96, 47, 266, 45], [96, 48, 266, 46], [96, 49, 266, 47], [97, 6, 267, 4], [97, 7, 267, 5], [97, 13, 267, 11], [97, 17, 267, 15, "props"], [97, 22, 267, 20], [97, 23, 267, 21, "failOffsetY"], [97, 34, 267, 32], [97, 37, 267, 35], [97, 38, 267, 36], [97, 40, 267, 38], [98, 8, 268, 6, "res"], [98, 11, 268, 9], [98, 12, 268, 10, "failOffsetYStart"], [98, 28, 268, 26], [98, 31, 268, 29, "props"], [98, 36, 268, 34], [98, 37, 268, 35, "failOffsetY"], [98, 48, 268, 46], [99, 6, 269, 4], [99, 7, 269, 5], [99, 13, 269, 11], [100, 8, 270, 6, "res"], [100, 11, 270, 9], [100, 12, 270, 10, "failOffsetYEnd"], [100, 26, 270, 24], [100, 29, 270, 27, "props"], [100, 34, 270, 32], [100, 35, 270, 33, "failOffsetY"], [100, 46, 270, 44], [101, 6, 271, 4], [102, 4, 272, 2], [103, 4, 274, 2], [103, 11, 274, 9, "res"], [103, 14, 274, 12], [104, 2, 275, 0], [105, 2, 277, 7], [105, 11, 277, 16, "managePanProps"], [105, 25, 277, 30, "managePanProps"], [105, 26, 277, 31, "props"], [105, 31, 277, 60], [105, 33, 277, 62], [106, 4, 278, 2], [106, 8, 278, 6, "__DEV__"], [106, 15, 278, 13], [106, 17, 278, 15], [107, 6, 279, 4, "validatePanGestureHandlerProps"], [107, 36, 279, 34], [107, 37, 279, 35, "props"], [107, 42, 279, 40], [107, 43, 279, 41], [108, 4, 280, 2], [109, 4, 281, 2], [109, 11, 281, 9, "transformPanGestureHandlerProps"], [109, 42, 281, 40], [109, 43, 281, 41, "props"], [109, 48, 281, 46], [109, 49, 281, 47], [110, 2, 282, 0], [111, 0, 282, 1], [111, 3]], "functionMap": {"names": ["<global>", "validatePanGestureHandlerProps", "transformPanGestureHandlerProps", "managePanProps"], "mappings": "AAA;ACgK;CDgD;AEE;CFgE;OGE;CHK"}}, "type": "js/module"}]}