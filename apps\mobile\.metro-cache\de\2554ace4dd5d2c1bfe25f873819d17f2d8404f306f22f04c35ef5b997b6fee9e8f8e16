{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.TapGesture = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _gesture = require(_dependencyMap[6], \"./gesture\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var TapGesture = exports.TapGesture = /*#__PURE__*/function (_BaseGesture) {\n    function TapGesture() {\n      var _this;\n      (0, _classCallCheck2.default)(this, TapGesture);\n      _this = _callSuper(this, TapGesture);\n      _this.config = {};\n      _this.handlerName = 'TapGestureHandler';\n      _this.shouldCancelWhenOutside(true);\n      return _this;\n    }\n\n    /**\n     * Minimum number of pointers (fingers) required to be placed before the gesture activates.\n     * Should be a positive integer. The default value is 1.\n     * @param minPointers\n     */\n    (0, _inherits2.default)(TapGesture, _BaseGesture);\n    return (0, _createClass2.default)(TapGesture, [{\n      key: \"minPointers\",\n      value: function minPointers(_minPointers) {\n        this.config.minPointers = _minPointers;\n        return this;\n      }\n\n      /**\n       * Number of tap gestures required to activate the gesture.\n       * The default value is 1.\n       * @param count\n       */\n    }, {\n      key: \"numberOfTaps\",\n      value: function numberOfTaps(count) {\n        this.config.numberOfTaps = count;\n        return this;\n      }\n\n      /**\n       * Maximum distance, expressed in points, that defines how far the finger is allowed to travel during a tap gesture.\n       * @param maxDist\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/tap-gesture#maxdistancevalue-number\n       */\n    }, {\n      key: \"maxDistance\",\n      value: function maxDistance(maxDist) {\n        this.config.maxDist = maxDist;\n        return this;\n      }\n\n      /**\n       * Maximum time, expressed in milliseconds, that defines how fast a finger must be released after a touch.\n       * The default value is 500.\n       * @param duration\n       */\n    }, {\n      key: \"maxDuration\",\n      value: function maxDuration(duration) {\n        this.config.maxDurationMs = duration;\n        return this;\n      }\n\n      /**\n       * Maximum time, expressed in milliseconds, that can pass before the next tap — if many taps are required.\n       * The default value is 500.\n       * @param delay\n       */\n    }, {\n      key: \"maxDelay\",\n      value: function maxDelay(delay) {\n        this.config.maxDelayMs = delay;\n        return this;\n      }\n\n      /**\n       * Maximum distance, expressed in points, that defines how far the finger is allowed to travel along the X axis during a tap gesture.\n       * @param delta\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/tap-gesture#maxdeltaxvalue-number\n       */\n    }, {\n      key: \"maxDeltaX\",\n      value: function maxDeltaX(delta) {\n        this.config.maxDeltaX = delta;\n        return this;\n      }\n\n      /**\n       * Maximum distance, expressed in points, that defines how far the finger is allowed to travel along the Y axis during a tap gesture.\n       * @param delta\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/tap-gesture#maxdeltayvalue-number\n       */\n    }, {\n      key: \"maxDeltaY\",\n      value: function maxDeltaY(delta) {\n        this.config.maxDeltaY = delta;\n        return this;\n      }\n    }]);\n  }(_gesture.BaseGesture);\n});", "lineCount": 112, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_gesture"], [12, 14, 1, 0], [12, 17, 1, 0, "require"], [12, 24, 1, 0], [12, 25, 1, 0, "_dependencyMap"], [12, 39, 1, 0], [13, 2, 1, 59], [13, 11, 1, 59, "_callSuper"], [13, 22, 1, 59, "t"], [13, 23, 1, 59], [13, 25, 1, 59, "o"], [13, 26, 1, 59], [13, 28, 1, 59, "e"], [13, 29, 1, 59], [13, 40, 1, 59, "o"], [13, 41, 1, 59], [13, 48, 1, 59, "_getPrototypeOf2"], [13, 64, 1, 59], [13, 65, 1, 59, "default"], [13, 72, 1, 59], [13, 74, 1, 59, "o"], [13, 75, 1, 59], [13, 82, 1, 59, "_possibleConstructorReturn2"], [13, 109, 1, 59], [13, 110, 1, 59, "default"], [13, 117, 1, 59], [13, 119, 1, 59, "t"], [13, 120, 1, 59], [13, 122, 1, 59, "_isNativeReflectConstruct"], [13, 147, 1, 59], [13, 152, 1, 59, "Reflect"], [13, 159, 1, 59], [13, 160, 1, 59, "construct"], [13, 169, 1, 59], [13, 170, 1, 59, "o"], [13, 171, 1, 59], [13, 173, 1, 59, "e"], [13, 174, 1, 59], [13, 186, 1, 59, "_getPrototypeOf2"], [13, 202, 1, 59], [13, 203, 1, 59, "default"], [13, 210, 1, 59], [13, 212, 1, 59, "t"], [13, 213, 1, 59], [13, 215, 1, 59, "constructor"], [13, 226, 1, 59], [13, 230, 1, 59, "o"], [13, 231, 1, 59], [13, 232, 1, 59, "apply"], [13, 237, 1, 59], [13, 238, 1, 59, "t"], [13, 239, 1, 59], [13, 241, 1, 59, "e"], [13, 242, 1, 59], [14, 2, 1, 59], [14, 11, 1, 59, "_isNativeReflectConstruct"], [14, 37, 1, 59], [14, 51, 1, 59, "t"], [14, 52, 1, 59], [14, 56, 1, 59, "Boolean"], [14, 63, 1, 59], [14, 64, 1, 59, "prototype"], [14, 73, 1, 59], [14, 74, 1, 59, "valueOf"], [14, 81, 1, 59], [14, 82, 1, 59, "call"], [14, 86, 1, 59], [14, 87, 1, 59, "Reflect"], [14, 94, 1, 59], [14, 95, 1, 59, "construct"], [14, 104, 1, 59], [14, 105, 1, 59, "Boolean"], [14, 112, 1, 59], [14, 145, 1, 59, "t"], [14, 146, 1, 59], [14, 159, 1, 59, "_isNativeReflectConstruct"], [14, 184, 1, 59], [14, 196, 1, 59, "_isNativeReflectConstruct"], [14, 197, 1, 59], [14, 210, 1, 59, "t"], [14, 211, 1, 59], [15, 2, 1, 59], [15, 6, 5, 13, "TapGesture"], [15, 16, 5, 23], [15, 19, 5, 23, "exports"], [15, 26, 5, 23], [15, 27, 5, 23, "TapGesture"], [15, 37, 5, 23], [15, 63, 5, 23, "_BaseGesture"], [15, 75, 5, 23], [16, 4, 8, 2], [16, 13, 8, 2, "TapGesture"], [16, 24, 8, 2], [16, 26, 8, 16], [17, 6, 8, 16], [17, 10, 8, 16, "_this"], [17, 15, 8, 16], [18, 6, 8, 16], [18, 10, 8, 16, "_classCallCheck2"], [18, 26, 8, 16], [18, 27, 8, 16, "default"], [18, 34, 8, 16], [18, 42, 8, 16, "TapGesture"], [18, 52, 8, 16], [19, 6, 9, 4, "_this"], [19, 11, 9, 4], [19, 14, 9, 4, "_callSuper"], [19, 24, 9, 4], [19, 31, 9, 4, "TapGesture"], [19, 41, 9, 4], [20, 6, 9, 12, "_this"], [20, 11, 9, 12], [20, 12, 6, 9, "config"], [20, 18, 6, 15], [20, 21, 6, 56], [20, 22, 6, 57], [20, 23, 6, 58], [21, 6, 11, 4, "_this"], [21, 11, 11, 4], [21, 12, 11, 9, "handler<PERSON>ame"], [21, 23, 11, 20], [21, 26, 11, 23], [21, 45, 11, 42], [22, 6, 12, 4, "_this"], [22, 11, 12, 4], [22, 12, 12, 9, "shouldCancelWhenOutside"], [22, 35, 12, 32], [22, 36, 12, 33], [22, 40, 12, 37], [22, 41, 12, 38], [23, 6, 12, 39], [23, 13, 12, 39, "_this"], [23, 18, 12, 39], [24, 4, 13, 2], [26, 4, 15, 2], [27, 0, 16, 0], [28, 0, 17, 0], [29, 0, 18, 0], [30, 0, 19, 0], [31, 4, 15, 2], [31, 8, 15, 2, "_inherits2"], [31, 18, 15, 2], [31, 19, 15, 2, "default"], [31, 26, 15, 2], [31, 28, 15, 2, "TapGesture"], [31, 38, 15, 2], [31, 40, 15, 2, "_BaseGesture"], [31, 52, 15, 2], [32, 4, 15, 2], [32, 15, 15, 2, "_createClass2"], [32, 28, 15, 2], [32, 29, 15, 2, "default"], [32, 36, 15, 2], [32, 38, 15, 2, "TapGesture"], [32, 48, 15, 2], [33, 6, 15, 2, "key"], [33, 9, 15, 2], [34, 6, 15, 2, "value"], [34, 11, 15, 2], [34, 13, 20, 2], [34, 22, 20, 2, "minPointers"], [34, 33, 20, 13, "minPointers"], [34, 34, 20, 14, "minPointers"], [34, 46, 20, 33], [34, 48, 20, 35], [35, 8, 21, 4], [35, 12, 21, 8], [35, 13, 21, 9, "config"], [35, 19, 21, 15], [35, 20, 21, 16, "minPointers"], [35, 31, 21, 27], [35, 34, 21, 30, "minPointers"], [35, 46, 21, 41], [36, 8, 22, 4], [36, 15, 22, 11], [36, 19, 22, 15], [37, 6, 23, 2], [39, 6, 25, 2], [40, 0, 26, 0], [41, 0, 27, 0], [42, 0, 28, 0], [43, 0, 29, 0], [44, 4, 25, 2], [45, 6, 25, 2, "key"], [45, 9, 25, 2], [46, 6, 25, 2, "value"], [46, 11, 25, 2], [46, 13, 30, 2], [46, 22, 30, 2, "numberOfTaps"], [46, 34, 30, 14, "numberOfTaps"], [46, 35, 30, 15, "count"], [46, 40, 30, 28], [46, 42, 30, 30], [47, 8, 31, 4], [47, 12, 31, 8], [47, 13, 31, 9, "config"], [47, 19, 31, 15], [47, 20, 31, 16, "numberOfTaps"], [47, 32, 31, 28], [47, 35, 31, 31, "count"], [47, 40, 31, 36], [48, 8, 32, 4], [48, 15, 32, 11], [48, 19, 32, 15], [49, 6, 33, 2], [51, 6, 35, 2], [52, 0, 36, 0], [53, 0, 37, 0], [54, 0, 38, 0], [55, 0, 39, 0], [56, 4, 35, 2], [57, 6, 35, 2, "key"], [57, 9, 35, 2], [58, 6, 35, 2, "value"], [58, 11, 35, 2], [58, 13, 40, 2], [58, 22, 40, 2, "maxDistance"], [58, 33, 40, 13, "maxDistance"], [58, 34, 40, 14, "maxDist"], [58, 41, 40, 29], [58, 43, 40, 31], [59, 8, 41, 4], [59, 12, 41, 8], [59, 13, 41, 9, "config"], [59, 19, 41, 15], [59, 20, 41, 16, "maxDist"], [59, 27, 41, 23], [59, 30, 41, 26, "maxDist"], [59, 37, 41, 33], [60, 8, 42, 4], [60, 15, 42, 11], [60, 19, 42, 15], [61, 6, 43, 2], [63, 6, 45, 2], [64, 0, 46, 0], [65, 0, 47, 0], [66, 0, 48, 0], [67, 0, 49, 0], [68, 4, 45, 2], [69, 6, 45, 2, "key"], [69, 9, 45, 2], [70, 6, 45, 2, "value"], [70, 11, 45, 2], [70, 13, 50, 2], [70, 22, 50, 2, "maxDuration"], [70, 33, 50, 13, "maxDuration"], [70, 34, 50, 14, "duration"], [70, 42, 50, 30], [70, 44, 50, 32], [71, 8, 51, 4], [71, 12, 51, 8], [71, 13, 51, 9, "config"], [71, 19, 51, 15], [71, 20, 51, 16, "maxDurationMs"], [71, 33, 51, 29], [71, 36, 51, 32, "duration"], [71, 44, 51, 40], [72, 8, 52, 4], [72, 15, 52, 11], [72, 19, 52, 15], [73, 6, 53, 2], [75, 6, 55, 2], [76, 0, 56, 0], [77, 0, 57, 0], [78, 0, 58, 0], [79, 0, 59, 0], [80, 4, 55, 2], [81, 6, 55, 2, "key"], [81, 9, 55, 2], [82, 6, 55, 2, "value"], [82, 11, 55, 2], [82, 13, 60, 2], [82, 22, 60, 2, "max<PERSON><PERSON><PERSON>"], [82, 30, 60, 10, "max<PERSON><PERSON><PERSON>"], [82, 31, 60, 11, "delay"], [82, 36, 60, 24], [82, 38, 60, 26], [83, 8, 61, 4], [83, 12, 61, 8], [83, 13, 61, 9, "config"], [83, 19, 61, 15], [83, 20, 61, 16, "max<PERSON>elay<PERSON>"], [83, 30, 61, 26], [83, 33, 61, 29, "delay"], [83, 38, 61, 34], [84, 8, 62, 4], [84, 15, 62, 11], [84, 19, 62, 15], [85, 6, 63, 2], [87, 6, 65, 2], [88, 0, 66, 0], [89, 0, 67, 0], [90, 0, 68, 0], [91, 0, 69, 0], [92, 4, 65, 2], [93, 6, 65, 2, "key"], [93, 9, 65, 2], [94, 6, 65, 2, "value"], [94, 11, 65, 2], [94, 13, 70, 2], [94, 22, 70, 2, "maxDeltaX"], [94, 31, 70, 11, "maxDeltaX"], [94, 32, 70, 12, "delta"], [94, 37, 70, 25], [94, 39, 70, 27], [95, 8, 71, 4], [95, 12, 71, 8], [95, 13, 71, 9, "config"], [95, 19, 71, 15], [95, 20, 71, 16, "maxDeltaX"], [95, 29, 71, 25], [95, 32, 71, 28, "delta"], [95, 37, 71, 33], [96, 8, 72, 4], [96, 15, 72, 11], [96, 19, 72, 15], [97, 6, 73, 2], [99, 6, 75, 2], [100, 0, 76, 0], [101, 0, 77, 0], [102, 0, 78, 0], [103, 0, 79, 0], [104, 4, 75, 2], [105, 6, 75, 2, "key"], [105, 9, 75, 2], [106, 6, 75, 2, "value"], [106, 11, 75, 2], [106, 13, 80, 2], [106, 22, 80, 2, "maxDeltaY"], [106, 31, 80, 11, "maxDeltaY"], [106, 32, 80, 12, "delta"], [106, 37, 80, 25], [106, 39, 80, 27], [107, 8, 81, 4], [107, 12, 81, 8], [107, 13, 81, 9, "config"], [107, 19, 81, 15], [107, 20, 81, 16, "maxDeltaY"], [107, 29, 81, 25], [107, 32, 81, 28, "delta"], [107, 37, 81, 33], [108, 8, 82, 4], [108, 15, 82, 11], [108, 19, 82, 15], [109, 6, 83, 2], [110, 4, 83, 3], [111, 2, 83, 3], [111, 4, 5, 32, "BaseGesture"], [111, 24, 5, 43], [112, 0, 5, 43], [112, 3]], "functionMap": {"names": ["<global>", "TapGesture", "TapGesture#constructor", "TapGesture#minPointers", "TapGesture#numberOfTaps", "TapGesture#maxDistance", "TapGesture#maxDuration", "TapGesture#maxDelay", "TapGesture#maxDeltaX", "TapGesture#maxDeltaY"], "mappings": "AAA;OCI;ECG;GDK;EEO;GFG;EGO;GHG;EIO;GJG;EKO;GLG;EMO;GNG;EOO;GPG;EQO;GRG;CDC"}}, "type": "js/module"}]}