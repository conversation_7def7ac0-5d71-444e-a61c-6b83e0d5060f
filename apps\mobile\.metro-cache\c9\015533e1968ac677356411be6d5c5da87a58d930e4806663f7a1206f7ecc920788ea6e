{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 28}, "end": {"line": 2, "column": 84, "index": 112}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 114}, "end": {"line": 3, "column": 62, "index": 176}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "../contexts/CartContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 178}, "end": {"line": 4, "column": 50, "index": 228}}], "key": "LZOU3Ibi1S4IYnUbOnGaF02X5Zw=", "exportNames": ["*"]}}, {"name": "../components/FooterNavigation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 230}, "end": {"line": 5, "column": 62, "index": 292}}], "key": "i0wsjvqzeC9AyK9fcghZiqZFu6Y=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = CartScreen;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[3], \"react-native-safe-area-context\");\n  var _CartContext = require(_dependencyMap[4], \"../contexts/CartContext\");\n  var _FooterNavigation = _interopRequireDefault(require(_dependencyMap[5], \"../components/FooterNavigation\"));\n  var _jsxDevRuntime = require(_dependencyMap[6], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\apps\\\\mobile\\\\src\\\\screens\\\\CartScreen.tsx\",\n    _s = $RefreshSig$();\n  function CartScreen(_ref) {\n    _s();\n    var navigation = _ref.navigation;\n    var _useCart = (0, _CartContext.useCart)(),\n      cart = _useCart.cart,\n      getCartItemCount = _useCart.getCartItemCount,\n      addToCart = _useCart.addToCart,\n      clearCart = _useCart.clearCart;\n    var itemCount = getCartItemCount();\n\n    // Demo function to add a test item\n    var addTestItem = () => {\n      var testItem = {\n        id: `test-${Date.now()}`,\n        name: 'Test Burger',\n        description: 'A delicious test burger',\n        price: 12.99,\n        image: 'https://example.com/burger.jpg',\n        restaurantId: 'test-restaurant',\n        category: 'Burgers',\n        available: true\n      };\n      addToCart(testItem, 1);\n    };\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f3a823'\n        },\n        edges: ['top']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: styles.header,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n          style: styles.title,\n          children: \"Your Cart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), cart && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n          onPress: clearCart,\n          style: styles.clearButton,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: styles.clearButtonText,\n            children: \"Clear Cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: {\n          flex: 1,\n          backgroundColor: '#f9fafb'\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.ScrollView, {\n          style: styles.content,\n          showsVerticalScrollIndicator: false,\n          children: cart && cart.items.length > 0 ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: styles.itemCount,\n              children: [itemCount, \" item\", itemCount !== 1 ? 's' : '', \" in cart\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 13\n            }, this), cart.items.map(item => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: styles.cartItem,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: styles.itemInfo,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: styles.itemName,\n                  children: item.menuItem.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: styles.itemDescription,\n                  children: item.menuItem.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: styles.itemPrice,\n                  children: [\"$\", item.menuItem.price.toFixed(2), \" x \", item.quantity]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                style: styles.itemTotal,\n                children: [\"$\", item.totalPrice.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: styles.summary,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: styles.summaryRow,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: styles.summaryLabel,\n                  children: \"Subtotal:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: styles.summaryValue,\n                  children: [\"$\", cart.subtotal.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: styles.summaryRow,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: styles.summaryLabel,\n                  children: \"Delivery Fee:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: styles.summaryValue,\n                  children: [\"$\", cart.deliveryFee.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: styles.summaryRow,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: styles.summaryLabel,\n                  children: \"Tax:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 74,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: styles.summaryValue,\n                  children: [\"$\", cart.tax.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: [styles.summaryRow, styles.totalRow],\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: styles.totalLabel,\n                  children: \"Total:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: styles.totalValue,\n                  children: [\"$\", cart.total.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 11\n          }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: styles.emptyState,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: styles.emptyTitle,\n              children: \"Your cart is empty\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: styles.emptySubtitle,\n              children: \"Add some delicious items to get started!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              onPress: addTestItem,\n              style: styles.testButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                style: styles.testButtonText,\n                children: \"Add Test Item\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_FooterNavigation.default, {\n        navigation: navigation,\n        activeScreen: \"Cart\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f9fafb'\n        },\n        edges: ['bottom']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 5\n    }, this);\n  }\n  _s(CartScreen, \"fCXBgeRDQHPZOhQ8qrJ3hwgwLYU=\", false, function () {\n    return [_CartContext.useCart];\n  });\n  _c = CartScreen;\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#f9fafb'\n    },\n    header: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      paddingHorizontal: 20,\n      paddingTop: 10,\n      paddingBottom: 20\n    },\n    title: {\n      fontSize: 28,\n      fontWeight: 'bold',\n      color: '#111827'\n    },\n    clearButton: {\n      paddingHorizontal: 12,\n      paddingVertical: 6,\n      backgroundColor: '#ef4444',\n      borderRadius: 6\n    },\n    clearButtonText: {\n      color: 'white',\n      fontSize: 14,\n      fontWeight: '600'\n    },\n    content: {\n      flex: 1,\n      paddingHorizontal: 20\n    },\n    itemCount: {\n      fontSize: 16,\n      color: '#6b7280',\n      marginBottom: 16\n    },\n    cartItem: {\n      backgroundColor: 'white',\n      borderRadius: 12,\n      padding: 16,\n      marginBottom: 12,\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      shadowColor: '#000',\n      shadowOffset: {\n        width: 0,\n        height: 1\n      },\n      shadowOpacity: 0.1,\n      shadowRadius: 3,\n      elevation: 2\n    },\n    itemInfo: {\n      flex: 1\n    },\n    itemName: {\n      fontSize: 16,\n      fontWeight: '600',\n      color: '#111827',\n      marginBottom: 4\n    },\n    itemDescription: {\n      fontSize: 14,\n      color: '#6b7280',\n      marginBottom: 4\n    },\n    itemPrice: {\n      fontSize: 14,\n      color: '#9ca3af'\n    },\n    itemTotal: {\n      fontSize: 16,\n      fontWeight: '600',\n      color: '#111827'\n    },\n    summary: {\n      backgroundColor: 'white',\n      borderRadius: 12,\n      padding: 16,\n      marginTop: 20,\n      marginBottom: 20,\n      shadowColor: '#000',\n      shadowOffset: {\n        width: 0,\n        height: 1\n      },\n      shadowOpacity: 0.1,\n      shadowRadius: 3,\n      elevation: 2\n    },\n    summaryRow: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      marginBottom: 8\n    },\n    summaryLabel: {\n      fontSize: 16,\n      color: '#6b7280'\n    },\n    summaryValue: {\n      fontSize: 16,\n      color: '#111827'\n    },\n    totalRow: {\n      borderTopWidth: 1,\n      borderTopColor: '#e5e7eb',\n      paddingTop: 8,\n      marginTop: 8,\n      marginBottom: 0\n    },\n    totalLabel: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827'\n    },\n    totalValue: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#f3a823'\n    },\n    emptyState: {\n      alignItems: 'center',\n      paddingTop: 60\n    },\n    emptyTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#374151',\n      marginBottom: 8\n    },\n    emptySubtitle: {\n      fontSize: 16,\n      color: '#6b7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    testButton: {\n      backgroundColor: '#f3a823',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8\n    },\n    testButtonText: {\n      color: 'white',\n      fontSize: 16,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"CartScreen\");\n});", "lineCount": 459, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_reactNative"], [8, 18, 2, 0], [8, 21, 2, 0, "require"], [8, 28, 2, 0], [8, 29, 2, 0, "_dependencyMap"], [8, 43, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_reactNativeSafeAreaContext"], [9, 33, 3, 0], [9, 36, 3, 0, "require"], [9, 43, 3, 0], [9, 44, 3, 0, "_dependencyMap"], [9, 58, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_CartContext"], [10, 18, 4, 0], [10, 21, 4, 0, "require"], [10, 28, 4, 0], [10, 29, 4, 0, "_dependencyMap"], [10, 43, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_FooterNavigation"], [11, 23, 5, 0], [11, 26, 5, 0, "_interopRequireDefault"], [11, 48, 5, 0], [11, 49, 5, 0, "require"], [11, 56, 5, 0], [11, 57, 5, 0, "_dependencyMap"], [11, 71, 5, 0], [12, 2, 5, 62], [12, 6, 5, 62, "_jsxDevRuntime"], [12, 20, 5, 62], [12, 23, 5, 62, "require"], [12, 30, 5, 62], [12, 31, 5, 62, "_dependencyMap"], [12, 45, 5, 62], [13, 2, 5, 62], [13, 6, 5, 62, "_jsxFileName"], [13, 18, 5, 62], [14, 4, 5, 62, "_s"], [14, 6, 5, 62], [14, 9, 5, 62, "$RefreshSig$"], [14, 21, 5, 62], [15, 2, 7, 15], [15, 11, 7, 24, "CartScreen"], [15, 21, 7, 34, "CartScreen"], [15, 22, 7, 34, "_ref"], [15, 26, 7, 34], [15, 28, 7, 56], [16, 4, 7, 56, "_s"], [16, 6, 7, 56], [17, 4, 7, 56], [17, 8, 7, 37, "navigation"], [17, 18, 7, 47], [17, 21, 7, 47, "_ref"], [17, 25, 7, 47], [17, 26, 7, 37, "navigation"], [17, 36, 7, 47], [18, 4, 8, 2], [18, 8, 8, 2, "_useCart"], [18, 16, 8, 2], [18, 19, 8, 59], [18, 23, 8, 59, "useCart"], [18, 43, 8, 66], [18, 45, 8, 67], [18, 46, 8, 68], [19, 6, 8, 10, "cart"], [19, 10, 8, 14], [19, 13, 8, 14, "_useCart"], [19, 21, 8, 14], [19, 22, 8, 10, "cart"], [19, 26, 8, 14], [20, 6, 8, 16, "getCartItemCount"], [20, 22, 8, 32], [20, 25, 8, 32, "_useCart"], [20, 33, 8, 32], [20, 34, 8, 16, "getCartItemCount"], [20, 50, 8, 32], [21, 6, 8, 34, "addToCart"], [21, 15, 8, 43], [21, 18, 8, 43, "_useCart"], [21, 26, 8, 43], [21, 27, 8, 34, "addToCart"], [21, 36, 8, 43], [22, 6, 8, 45, "clearCart"], [22, 15, 8, 54], [22, 18, 8, 54, "_useCart"], [22, 26, 8, 54], [22, 27, 8, 45, "clearCart"], [22, 36, 8, 54], [23, 4, 9, 2], [23, 8, 9, 8, "itemCount"], [23, 17, 9, 17], [23, 20, 9, 20, "getCartItemCount"], [23, 36, 9, 36], [23, 37, 9, 37], [23, 38, 9, 38], [25, 4, 11, 2], [26, 4, 12, 2], [26, 8, 12, 8, "addTestItem"], [26, 19, 12, 19], [26, 22, 12, 22, "addTestItem"], [26, 23, 12, 22], [26, 28, 12, 28], [27, 6, 13, 4], [27, 10, 13, 10, "testItem"], [27, 18, 13, 18], [27, 21, 13, 21], [28, 8, 14, 6, "id"], [28, 10, 14, 8], [28, 12, 14, 10], [28, 20, 14, 18, "Date"], [28, 24, 14, 22], [28, 25, 14, 23, "now"], [28, 28, 14, 26], [28, 29, 14, 27], [28, 30, 14, 28], [28, 32, 14, 30], [29, 8, 15, 6, "name"], [29, 12, 15, 10], [29, 14, 15, 12], [29, 27, 15, 25], [30, 8, 16, 6, "description"], [30, 19, 16, 17], [30, 21, 16, 19], [30, 46, 16, 44], [31, 8, 17, 6, "price"], [31, 13, 17, 11], [31, 15, 17, 13], [31, 20, 17, 18], [32, 8, 18, 6, "image"], [32, 13, 18, 11], [32, 15, 18, 13], [32, 47, 18, 45], [33, 8, 19, 6, "restaurantId"], [33, 20, 19, 18], [33, 22, 19, 20], [33, 39, 19, 37], [34, 8, 20, 6, "category"], [34, 16, 20, 14], [34, 18, 20, 16], [34, 27, 20, 25], [35, 8, 21, 6, "available"], [35, 17, 21, 15], [35, 19, 21, 17], [36, 6, 22, 4], [36, 7, 22, 5], [37, 6, 23, 4, "addToCart"], [37, 15, 23, 13], [37, 16, 23, 14, "testItem"], [37, 24, 23, 22], [37, 26, 23, 24], [37, 27, 23, 25], [37, 28, 23, 26], [38, 4, 24, 2], [38, 5, 24, 3], [39, 4, 26, 2], [39, 24, 27, 4], [39, 28, 27, 4, "_jsxDevRuntime"], [39, 42, 27, 4], [39, 43, 27, 4, "jsxDEV"], [39, 49, 27, 4], [39, 51, 27, 5, "_reactNative"], [39, 63, 27, 5], [39, 64, 27, 5, "View"], [39, 68, 27, 9], [40, 6, 27, 10, "style"], [40, 11, 27, 15], [40, 13, 27, 17, "styles"], [40, 19, 27, 23], [40, 20, 27, 24, "container"], [40, 29, 27, 34], [41, 6, 27, 34, "children"], [41, 14, 27, 34], [41, 30, 29, 6], [41, 34, 29, 6, "_jsxDevRuntime"], [41, 48, 29, 6], [41, 49, 29, 6, "jsxDEV"], [41, 55, 29, 6], [41, 57, 29, 7, "_reactNativeSafeAreaContext"], [41, 84, 29, 7], [41, 85, 29, 7, "SafeAreaView"], [41, 97, 29, 19], [42, 8, 29, 20, "style"], [42, 13, 29, 25], [42, 15, 29, 27], [43, 10, 29, 29, "backgroundColor"], [43, 25, 29, 44], [43, 27, 29, 46], [44, 8, 29, 56], [44, 9, 29, 58], [45, 8, 29, 59, "edges"], [45, 13, 29, 64], [45, 15, 29, 66], [45, 16, 29, 67], [45, 21, 29, 72], [46, 6, 29, 74], [47, 8, 29, 74, "fileName"], [47, 16, 29, 74], [47, 18, 29, 74, "_jsxFileName"], [47, 30, 29, 74], [48, 8, 29, 74, "lineNumber"], [48, 18, 29, 74], [49, 8, 29, 74, "columnNumber"], [49, 20, 29, 74], [50, 6, 29, 74], [50, 13, 29, 76], [50, 14, 29, 77], [50, 29, 31, 6], [50, 33, 31, 6, "_jsxDevRuntime"], [50, 47, 31, 6], [50, 48, 31, 6, "jsxDEV"], [50, 54, 31, 6], [50, 56, 31, 7, "_reactNative"], [50, 68, 31, 7], [50, 69, 31, 7, "View"], [50, 73, 31, 11], [51, 8, 31, 12, "style"], [51, 13, 31, 17], [51, 15, 31, 19, "styles"], [51, 21, 31, 25], [51, 22, 31, 26, "header"], [51, 28, 31, 33], [52, 8, 31, 33, "children"], [52, 16, 31, 33], [52, 32, 32, 10], [52, 36, 32, 10, "_jsxDevRuntime"], [52, 50, 32, 10], [52, 51, 32, 10, "jsxDEV"], [52, 57, 32, 10], [52, 59, 32, 11, "_reactNative"], [52, 71, 32, 11], [52, 72, 32, 11, "Text"], [52, 76, 32, 15], [53, 10, 32, 16, "style"], [53, 15, 32, 21], [53, 17, 32, 23, "styles"], [53, 23, 32, 29], [53, 24, 32, 30, "title"], [53, 29, 32, 36], [54, 10, 32, 36, "children"], [54, 18, 32, 36], [54, 20, 32, 37], [55, 8, 32, 46], [56, 10, 32, 46, "fileName"], [56, 18, 32, 46], [56, 20, 32, 46, "_jsxFileName"], [56, 32, 32, 46], [57, 10, 32, 46, "lineNumber"], [57, 20, 32, 46], [58, 10, 32, 46, "columnNumber"], [58, 22, 32, 46], [59, 8, 32, 46], [59, 15, 32, 52], [59, 16, 32, 53], [59, 18, 33, 11, "cart"], [59, 22, 33, 15], [59, 39, 34, 12], [59, 43, 34, 12, "_jsxDevRuntime"], [59, 57, 34, 12], [59, 58, 34, 12, "jsxDEV"], [59, 64, 34, 12], [59, 66, 34, 13, "_reactNative"], [59, 78, 34, 13], [59, 79, 34, 13, "TouchableOpacity"], [59, 95, 34, 29], [60, 10, 34, 30, "onPress"], [60, 17, 34, 37], [60, 19, 34, 39, "clearCart"], [60, 28, 34, 49], [61, 10, 34, 50, "style"], [61, 15, 34, 55], [61, 17, 34, 57, "styles"], [61, 23, 34, 63], [61, 24, 34, 64, "clearButton"], [61, 35, 34, 76], [62, 10, 34, 76, "children"], [62, 18, 34, 76], [62, 33, 35, 14], [62, 37, 35, 14, "_jsxDevRuntime"], [62, 51, 35, 14], [62, 52, 35, 14, "jsxDEV"], [62, 58, 35, 14], [62, 60, 35, 15, "_reactNative"], [62, 72, 35, 15], [62, 73, 35, 15, "Text"], [62, 77, 35, 19], [63, 12, 35, 20, "style"], [63, 17, 35, 25], [63, 19, 35, 27, "styles"], [63, 25, 35, 33], [63, 26, 35, 34, "clearButtonText"], [63, 41, 35, 50], [64, 12, 35, 50, "children"], [64, 20, 35, 50], [64, 22, 35, 51], [65, 10, 35, 61], [66, 12, 35, 61, "fileName"], [66, 20, 35, 61], [66, 22, 35, 61, "_jsxFileName"], [66, 34, 35, 61], [67, 12, 35, 61, "lineNumber"], [67, 22, 35, 61], [68, 12, 35, 61, "columnNumber"], [68, 24, 35, 61], [69, 10, 35, 61], [69, 17, 35, 67], [70, 8, 35, 68], [71, 10, 35, 68, "fileName"], [71, 18, 35, 68], [71, 20, 35, 68, "_jsxFileName"], [71, 32, 35, 68], [72, 10, 35, 68, "lineNumber"], [72, 20, 35, 68], [73, 10, 35, 68, "columnNumber"], [73, 22, 35, 68], [74, 8, 35, 68], [74, 15, 36, 30], [74, 16, 37, 11], [75, 6, 37, 11], [76, 8, 37, 11, "fileName"], [76, 16, 37, 11], [76, 18, 37, 11, "_jsxFileName"], [76, 30, 37, 11], [77, 8, 37, 11, "lineNumber"], [77, 18, 37, 11], [78, 8, 37, 11, "columnNumber"], [78, 20, 37, 11], [79, 6, 37, 11], [79, 13, 38, 14], [79, 14, 38, 15], [79, 29, 41, 6], [79, 33, 41, 6, "_jsxDevRuntime"], [79, 47, 41, 6], [79, 48, 41, 6, "jsxDEV"], [79, 54, 41, 6], [79, 56, 41, 7, "_reactNative"], [79, 68, 41, 7], [79, 69, 41, 7, "View"], [79, 73, 41, 11], [80, 8, 41, 12, "style"], [80, 13, 41, 17], [80, 15, 41, 19], [81, 10, 41, 21, "flex"], [81, 14, 41, 25], [81, 16, 41, 27], [81, 17, 41, 28], [82, 10, 41, 30, "backgroundColor"], [82, 25, 41, 45], [82, 27, 41, 47], [83, 8, 41, 57], [83, 9, 41, 59], [84, 8, 41, 59, "children"], [84, 16, 41, 59], [84, 31, 42, 8], [84, 35, 42, 8, "_jsxDevRuntime"], [84, 49, 42, 8], [84, 50, 42, 8, "jsxDEV"], [84, 56, 42, 8], [84, 58, 42, 9, "_reactNative"], [84, 70, 42, 9], [84, 71, 42, 9, "ScrollView"], [84, 81, 42, 19], [85, 10, 42, 20, "style"], [85, 15, 42, 25], [85, 17, 42, 27, "styles"], [85, 23, 42, 33], [85, 24, 42, 34, "content"], [85, 31, 42, 42], [86, 10, 42, 43, "showsVerticalScrollIndicator"], [86, 38, 42, 71], [86, 40, 42, 73], [86, 45, 42, 79], [87, 10, 42, 79, "children"], [87, 18, 42, 79], [87, 20, 43, 9, "cart"], [87, 24, 43, 13], [87, 28, 43, 17, "cart"], [87, 32, 43, 21], [87, 33, 43, 22, "items"], [87, 38, 43, 27], [87, 39, 43, 28, "length"], [87, 45, 43, 34], [87, 48, 43, 37], [87, 49, 43, 38], [87, 65, 44, 10], [87, 69, 44, 10, "_jsxDevRuntime"], [87, 83, 44, 10], [87, 84, 44, 10, "jsxDEV"], [87, 90, 44, 10], [87, 92, 44, 11, "_reactNative"], [87, 104, 44, 11], [87, 105, 44, 11, "View"], [87, 109, 44, 15], [88, 12, 44, 15, "children"], [88, 20, 44, 15], [88, 36, 45, 12], [88, 40, 45, 12, "_jsxDevRuntime"], [88, 54, 45, 12], [88, 55, 45, 12, "jsxDEV"], [88, 61, 45, 12], [88, 63, 45, 13, "_reactNative"], [88, 75, 45, 13], [88, 76, 45, 13, "Text"], [88, 80, 45, 17], [89, 14, 45, 18, "style"], [89, 19, 45, 23], [89, 21, 45, 25, "styles"], [89, 27, 45, 31], [89, 28, 45, 32, "itemCount"], [89, 37, 45, 42], [90, 14, 45, 42, "children"], [90, 22, 45, 42], [90, 25, 46, 15, "itemCount"], [90, 34, 46, 24], [90, 36, 46, 25], [90, 43, 46, 30], [90, 45, 46, 31, "itemCount"], [90, 54, 46, 40], [90, 59, 46, 45], [90, 60, 46, 46], [90, 63, 46, 49], [90, 66, 46, 52], [90, 69, 46, 55], [90, 71, 46, 57], [90, 73, 46, 58], [90, 83, 47, 12], [91, 12, 47, 12], [92, 14, 47, 12, "fileName"], [92, 22, 47, 12], [92, 24, 47, 12, "_jsxFileName"], [92, 36, 47, 12], [93, 14, 47, 12, "lineNumber"], [93, 24, 47, 12], [94, 14, 47, 12, "columnNumber"], [94, 26, 47, 12], [95, 12, 47, 12], [95, 19, 47, 18], [95, 20, 47, 19], [95, 22, 49, 13, "cart"], [95, 26, 49, 17], [95, 27, 49, 18, "items"], [95, 32, 49, 23], [95, 33, 49, 24, "map"], [95, 36, 49, 27], [95, 37, 49, 29, "item"], [95, 41, 49, 33], [95, 58, 50, 14], [95, 62, 50, 14, "_jsxDevRuntime"], [95, 76, 50, 14], [95, 77, 50, 14, "jsxDEV"], [95, 83, 50, 14], [95, 85, 50, 15, "_reactNative"], [95, 97, 50, 15], [95, 98, 50, 15, "View"], [95, 102, 50, 19], [96, 14, 50, 34, "style"], [96, 19, 50, 39], [96, 21, 50, 41, "styles"], [96, 27, 50, 47], [96, 28, 50, 48, "cartItem"], [96, 36, 50, 57], [97, 14, 50, 57, "children"], [97, 22, 50, 57], [97, 38, 51, 16], [97, 42, 51, 16, "_jsxDevRuntime"], [97, 56, 51, 16], [97, 57, 51, 16, "jsxDEV"], [97, 63, 51, 16], [97, 65, 51, 17, "_reactNative"], [97, 77, 51, 17], [97, 78, 51, 17, "View"], [97, 82, 51, 21], [98, 16, 51, 22, "style"], [98, 21, 51, 27], [98, 23, 51, 29, "styles"], [98, 29, 51, 35], [98, 30, 51, 36, "itemInfo"], [98, 38, 51, 45], [99, 16, 51, 45, "children"], [99, 24, 51, 45], [99, 40, 52, 18], [99, 44, 52, 18, "_jsxDevRuntime"], [99, 58, 52, 18], [99, 59, 52, 18, "jsxDEV"], [99, 65, 52, 18], [99, 67, 52, 19, "_reactNative"], [99, 79, 52, 19], [99, 80, 52, 19, "Text"], [99, 84, 52, 23], [100, 18, 52, 24, "style"], [100, 23, 52, 29], [100, 25, 52, 31, "styles"], [100, 31, 52, 37], [100, 32, 52, 38, "itemName"], [100, 40, 52, 47], [101, 18, 52, 47, "children"], [101, 26, 52, 47], [101, 28, 52, 49, "item"], [101, 32, 52, 53], [101, 33, 52, 54, "menuItem"], [101, 41, 52, 62], [101, 42, 52, 63, "name"], [102, 16, 52, 67], [103, 18, 52, 67, "fileName"], [103, 26, 52, 67], [103, 28, 52, 67, "_jsxFileName"], [103, 40, 52, 67], [104, 18, 52, 67, "lineNumber"], [104, 28, 52, 67], [105, 18, 52, 67, "columnNumber"], [105, 30, 52, 67], [106, 16, 52, 67], [106, 23, 52, 74], [106, 24, 52, 75], [106, 39, 53, 18], [106, 43, 53, 18, "_jsxDevRuntime"], [106, 57, 53, 18], [106, 58, 53, 18, "jsxDEV"], [106, 64, 53, 18], [106, 66, 53, 19, "_reactNative"], [106, 78, 53, 19], [106, 79, 53, 19, "Text"], [106, 83, 53, 23], [107, 18, 53, 24, "style"], [107, 23, 53, 29], [107, 25, 53, 31, "styles"], [107, 31, 53, 37], [107, 32, 53, 38, "itemDescription"], [107, 47, 53, 54], [108, 18, 53, 54, "children"], [108, 26, 53, 54], [108, 28, 53, 56, "item"], [108, 32, 53, 60], [108, 33, 53, 61, "menuItem"], [108, 41, 53, 69], [108, 42, 53, 70, "description"], [109, 16, 53, 81], [110, 18, 53, 81, "fileName"], [110, 26, 53, 81], [110, 28, 53, 81, "_jsxFileName"], [110, 40, 53, 81], [111, 18, 53, 81, "lineNumber"], [111, 28, 53, 81], [112, 18, 53, 81, "columnNumber"], [112, 30, 53, 81], [113, 16, 53, 81], [113, 23, 53, 88], [113, 24, 53, 89], [113, 39, 54, 18], [113, 43, 54, 18, "_jsxDevRuntime"], [113, 57, 54, 18], [113, 58, 54, 18, "jsxDEV"], [113, 64, 54, 18], [113, 66, 54, 19, "_reactNative"], [113, 78, 54, 19], [113, 79, 54, 19, "Text"], [113, 83, 54, 23], [114, 18, 54, 24, "style"], [114, 23, 54, 29], [114, 25, 54, 31, "styles"], [114, 31, 54, 37], [114, 32, 54, 38, "itemPrice"], [114, 41, 54, 48], [115, 18, 54, 48, "children"], [115, 26, 54, 48], [115, 29, 54, 49], [115, 32, 55, 21], [115, 34, 55, 22, "item"], [115, 38, 55, 26], [115, 39, 55, 27, "menuItem"], [115, 47, 55, 35], [115, 48, 55, 36, "price"], [115, 53, 55, 41], [115, 54, 55, 42, "toFixed"], [115, 61, 55, 49], [115, 62, 55, 50], [115, 63, 55, 51], [115, 64, 55, 52], [115, 66, 55, 53], [115, 71, 55, 56], [115, 73, 55, 57, "item"], [115, 77, 55, 61], [115, 78, 55, 62, "quantity"], [115, 86, 55, 70], [116, 16, 55, 70], [117, 18, 55, 70, "fileName"], [117, 26, 55, 70], [117, 28, 55, 70, "_jsxFileName"], [117, 40, 55, 70], [118, 18, 55, 70, "lineNumber"], [118, 28, 55, 70], [119, 18, 55, 70, "columnNumber"], [119, 30, 55, 70], [120, 16, 55, 70], [120, 23, 56, 24], [120, 24, 56, 25], [121, 14, 56, 25], [122, 16, 56, 25, "fileName"], [122, 24, 56, 25], [122, 26, 56, 25, "_jsxFileName"], [122, 38, 56, 25], [123, 16, 56, 25, "lineNumber"], [123, 26, 56, 25], [124, 16, 56, 25, "columnNumber"], [124, 28, 56, 25], [125, 14, 56, 25], [125, 21, 57, 22], [125, 22, 57, 23], [125, 37, 58, 16], [125, 41, 58, 16, "_jsxDevRuntime"], [125, 55, 58, 16], [125, 56, 58, 16, "jsxDEV"], [125, 62, 58, 16], [125, 64, 58, 17, "_reactNative"], [125, 76, 58, 17], [125, 77, 58, 17, "Text"], [125, 81, 58, 21], [126, 16, 58, 22, "style"], [126, 21, 58, 27], [126, 23, 58, 29, "styles"], [126, 29, 58, 35], [126, 30, 58, 36, "itemTotal"], [126, 39, 58, 46], [127, 16, 58, 46, "children"], [127, 24, 58, 46], [127, 27, 58, 47], [127, 30, 59, 19], [127, 32, 59, 20, "item"], [127, 36, 59, 24], [127, 37, 59, 25, "totalPrice"], [127, 47, 59, 35], [127, 48, 59, 36, "toFixed"], [127, 55, 59, 43], [127, 56, 59, 44], [127, 57, 59, 45], [127, 58, 59, 46], [128, 14, 59, 46], [129, 16, 59, 46, "fileName"], [129, 24, 59, 46], [129, 26, 59, 46, "_jsxFileName"], [129, 38, 59, 46], [130, 16, 59, 46, "lineNumber"], [130, 26, 59, 46], [131, 16, 59, 46, "columnNumber"], [131, 28, 59, 46], [132, 14, 59, 46], [132, 21, 60, 22], [132, 22, 60, 23], [133, 12, 60, 23], [133, 15, 50, 25, "item"], [133, 19, 50, 29], [133, 20, 50, 30, "id"], [133, 22, 50, 32], [134, 14, 50, 32, "fileName"], [134, 22, 50, 32], [134, 24, 50, 32, "_jsxFileName"], [134, 36, 50, 32], [135, 14, 50, 32, "lineNumber"], [135, 24, 50, 32], [136, 14, 50, 32, "columnNumber"], [136, 26, 50, 32], [137, 12, 50, 32], [137, 19, 61, 20], [137, 20, 62, 13], [137, 21, 62, 14], [137, 36, 64, 12], [137, 40, 64, 12, "_jsxDevRuntime"], [137, 54, 64, 12], [137, 55, 64, 12, "jsxDEV"], [137, 61, 64, 12], [137, 63, 64, 13, "_reactNative"], [137, 75, 64, 13], [137, 76, 64, 13, "View"], [137, 80, 64, 17], [138, 14, 64, 18, "style"], [138, 19, 64, 23], [138, 21, 64, 25, "styles"], [138, 27, 64, 31], [138, 28, 64, 32, "summary"], [138, 35, 64, 40], [139, 14, 64, 40, "children"], [139, 22, 64, 40], [139, 38, 65, 14], [139, 42, 65, 14, "_jsxDevRuntime"], [139, 56, 65, 14], [139, 57, 65, 14, "jsxDEV"], [139, 63, 65, 14], [139, 65, 65, 15, "_reactNative"], [139, 77, 65, 15], [139, 78, 65, 15, "View"], [139, 82, 65, 19], [140, 16, 65, 20, "style"], [140, 21, 65, 25], [140, 23, 65, 27, "styles"], [140, 29, 65, 33], [140, 30, 65, 34, "summaryRow"], [140, 40, 65, 45], [141, 16, 65, 45, "children"], [141, 24, 65, 45], [141, 40, 66, 16], [141, 44, 66, 16, "_jsxDevRuntime"], [141, 58, 66, 16], [141, 59, 66, 16, "jsxDEV"], [141, 65, 66, 16], [141, 67, 66, 17, "_reactNative"], [141, 79, 66, 17], [141, 80, 66, 17, "Text"], [141, 84, 66, 21], [142, 18, 66, 22, "style"], [142, 23, 66, 27], [142, 25, 66, 29, "styles"], [142, 31, 66, 35], [142, 32, 66, 36, "summaryLabel"], [142, 44, 66, 49], [143, 18, 66, 49, "children"], [143, 26, 66, 49], [143, 28, 66, 50], [144, 16, 66, 59], [145, 18, 66, 59, "fileName"], [145, 26, 66, 59], [145, 28, 66, 59, "_jsxFileName"], [145, 40, 66, 59], [146, 18, 66, 59, "lineNumber"], [146, 28, 66, 59], [147, 18, 66, 59, "columnNumber"], [147, 30, 66, 59], [148, 16, 66, 59], [148, 23, 66, 65], [148, 24, 66, 66], [148, 39, 67, 16], [148, 43, 67, 16, "_jsxDevRuntime"], [148, 57, 67, 16], [148, 58, 67, 16, "jsxDEV"], [148, 64, 67, 16], [148, 66, 67, 17, "_reactNative"], [148, 78, 67, 17], [148, 79, 67, 17, "Text"], [148, 83, 67, 21], [149, 18, 67, 22, "style"], [149, 23, 67, 27], [149, 25, 67, 29, "styles"], [149, 31, 67, 35], [149, 32, 67, 36, "summaryValue"], [149, 44, 67, 49], [150, 18, 67, 49, "children"], [150, 26, 67, 49], [150, 29, 67, 50], [150, 32, 67, 51], [150, 34, 67, 52, "cart"], [150, 38, 67, 56], [150, 39, 67, 57, "subtotal"], [150, 47, 67, 65], [150, 48, 67, 66, "toFixed"], [150, 55, 67, 73], [150, 56, 67, 74], [150, 57, 67, 75], [150, 58, 67, 76], [151, 16, 67, 76], [152, 18, 67, 76, "fileName"], [152, 26, 67, 76], [152, 28, 67, 76, "_jsxFileName"], [152, 40, 67, 76], [153, 18, 67, 76, "lineNumber"], [153, 28, 67, 76], [154, 18, 67, 76, "columnNumber"], [154, 30, 67, 76], [155, 16, 67, 76], [155, 23, 67, 83], [155, 24, 67, 84], [156, 14, 67, 84], [157, 16, 67, 84, "fileName"], [157, 24, 67, 84], [157, 26, 67, 84, "_jsxFileName"], [157, 38, 67, 84], [158, 16, 67, 84, "lineNumber"], [158, 26, 67, 84], [159, 16, 67, 84, "columnNumber"], [159, 28, 67, 84], [160, 14, 67, 84], [160, 21, 68, 20], [160, 22, 68, 21], [160, 37, 69, 14], [160, 41, 69, 14, "_jsxDevRuntime"], [160, 55, 69, 14], [160, 56, 69, 14, "jsxDEV"], [160, 62, 69, 14], [160, 64, 69, 15, "_reactNative"], [160, 76, 69, 15], [160, 77, 69, 15, "View"], [160, 81, 69, 19], [161, 16, 69, 20, "style"], [161, 21, 69, 25], [161, 23, 69, 27, "styles"], [161, 29, 69, 33], [161, 30, 69, 34, "summaryRow"], [161, 40, 69, 45], [162, 16, 69, 45, "children"], [162, 24, 69, 45], [162, 40, 70, 16], [162, 44, 70, 16, "_jsxDevRuntime"], [162, 58, 70, 16], [162, 59, 70, 16, "jsxDEV"], [162, 65, 70, 16], [162, 67, 70, 17, "_reactNative"], [162, 79, 70, 17], [162, 80, 70, 17, "Text"], [162, 84, 70, 21], [163, 18, 70, 22, "style"], [163, 23, 70, 27], [163, 25, 70, 29, "styles"], [163, 31, 70, 35], [163, 32, 70, 36, "summaryLabel"], [163, 44, 70, 49], [164, 18, 70, 49, "children"], [164, 26, 70, 49], [164, 28, 70, 50], [165, 16, 70, 63], [166, 18, 70, 63, "fileName"], [166, 26, 70, 63], [166, 28, 70, 63, "_jsxFileName"], [166, 40, 70, 63], [167, 18, 70, 63, "lineNumber"], [167, 28, 70, 63], [168, 18, 70, 63, "columnNumber"], [168, 30, 70, 63], [169, 16, 70, 63], [169, 23, 70, 69], [169, 24, 70, 70], [169, 39, 71, 16], [169, 43, 71, 16, "_jsxDevRuntime"], [169, 57, 71, 16], [169, 58, 71, 16, "jsxDEV"], [169, 64, 71, 16], [169, 66, 71, 17, "_reactNative"], [169, 78, 71, 17], [169, 79, 71, 17, "Text"], [169, 83, 71, 21], [170, 18, 71, 22, "style"], [170, 23, 71, 27], [170, 25, 71, 29, "styles"], [170, 31, 71, 35], [170, 32, 71, 36, "summaryValue"], [170, 44, 71, 49], [171, 18, 71, 49, "children"], [171, 26, 71, 49], [171, 29, 71, 50], [171, 32, 71, 51], [171, 34, 71, 52, "cart"], [171, 38, 71, 56], [171, 39, 71, 57, "deliveryFee"], [171, 50, 71, 68], [171, 51, 71, 69, "toFixed"], [171, 58, 71, 76], [171, 59, 71, 77], [171, 60, 71, 78], [171, 61, 71, 79], [172, 16, 71, 79], [173, 18, 71, 79, "fileName"], [173, 26, 71, 79], [173, 28, 71, 79, "_jsxFileName"], [173, 40, 71, 79], [174, 18, 71, 79, "lineNumber"], [174, 28, 71, 79], [175, 18, 71, 79, "columnNumber"], [175, 30, 71, 79], [176, 16, 71, 79], [176, 23, 71, 86], [176, 24, 71, 87], [177, 14, 71, 87], [178, 16, 71, 87, "fileName"], [178, 24, 71, 87], [178, 26, 71, 87, "_jsxFileName"], [178, 38, 71, 87], [179, 16, 71, 87, "lineNumber"], [179, 26, 71, 87], [180, 16, 71, 87, "columnNumber"], [180, 28, 71, 87], [181, 14, 71, 87], [181, 21, 72, 20], [181, 22, 72, 21], [181, 37, 73, 14], [181, 41, 73, 14, "_jsxDevRuntime"], [181, 55, 73, 14], [181, 56, 73, 14, "jsxDEV"], [181, 62, 73, 14], [181, 64, 73, 15, "_reactNative"], [181, 76, 73, 15], [181, 77, 73, 15, "View"], [181, 81, 73, 19], [182, 16, 73, 20, "style"], [182, 21, 73, 25], [182, 23, 73, 27, "styles"], [182, 29, 73, 33], [182, 30, 73, 34, "summaryRow"], [182, 40, 73, 45], [183, 16, 73, 45, "children"], [183, 24, 73, 45], [183, 40, 74, 16], [183, 44, 74, 16, "_jsxDevRuntime"], [183, 58, 74, 16], [183, 59, 74, 16, "jsxDEV"], [183, 65, 74, 16], [183, 67, 74, 17, "_reactNative"], [183, 79, 74, 17], [183, 80, 74, 17, "Text"], [183, 84, 74, 21], [184, 18, 74, 22, "style"], [184, 23, 74, 27], [184, 25, 74, 29, "styles"], [184, 31, 74, 35], [184, 32, 74, 36, "summaryLabel"], [184, 44, 74, 49], [185, 18, 74, 49, "children"], [185, 26, 74, 49], [185, 28, 74, 50], [186, 16, 74, 54], [187, 18, 74, 54, "fileName"], [187, 26, 74, 54], [187, 28, 74, 54, "_jsxFileName"], [187, 40, 74, 54], [188, 18, 74, 54, "lineNumber"], [188, 28, 74, 54], [189, 18, 74, 54, "columnNumber"], [189, 30, 74, 54], [190, 16, 74, 54], [190, 23, 74, 60], [190, 24, 74, 61], [190, 39, 75, 16], [190, 43, 75, 16, "_jsxDevRuntime"], [190, 57, 75, 16], [190, 58, 75, 16, "jsxDEV"], [190, 64, 75, 16], [190, 66, 75, 17, "_reactNative"], [190, 78, 75, 17], [190, 79, 75, 17, "Text"], [190, 83, 75, 21], [191, 18, 75, 22, "style"], [191, 23, 75, 27], [191, 25, 75, 29, "styles"], [191, 31, 75, 35], [191, 32, 75, 36, "summaryValue"], [191, 44, 75, 49], [192, 18, 75, 49, "children"], [192, 26, 75, 49], [192, 29, 75, 50], [192, 32, 75, 51], [192, 34, 75, 52, "cart"], [192, 38, 75, 56], [192, 39, 75, 57, "tax"], [192, 42, 75, 60], [192, 43, 75, 61, "toFixed"], [192, 50, 75, 68], [192, 51, 75, 69], [192, 52, 75, 70], [192, 53, 75, 71], [193, 16, 75, 71], [194, 18, 75, 71, "fileName"], [194, 26, 75, 71], [194, 28, 75, 71, "_jsxFileName"], [194, 40, 75, 71], [195, 18, 75, 71, "lineNumber"], [195, 28, 75, 71], [196, 18, 75, 71, "columnNumber"], [196, 30, 75, 71], [197, 16, 75, 71], [197, 23, 75, 78], [197, 24, 75, 79], [198, 14, 75, 79], [199, 16, 75, 79, "fileName"], [199, 24, 75, 79], [199, 26, 75, 79, "_jsxFileName"], [199, 38, 75, 79], [200, 16, 75, 79, "lineNumber"], [200, 26, 75, 79], [201, 16, 75, 79, "columnNumber"], [201, 28, 75, 79], [202, 14, 75, 79], [202, 21, 76, 20], [202, 22, 76, 21], [202, 37, 77, 14], [202, 41, 77, 14, "_jsxDevRuntime"], [202, 55, 77, 14], [202, 56, 77, 14, "jsxDEV"], [202, 62, 77, 14], [202, 64, 77, 15, "_reactNative"], [202, 76, 77, 15], [202, 77, 77, 15, "View"], [202, 81, 77, 19], [203, 16, 77, 20, "style"], [203, 21, 77, 25], [203, 23, 77, 27], [203, 24, 77, 28, "styles"], [203, 30, 77, 34], [203, 31, 77, 35, "summaryRow"], [203, 41, 77, 45], [203, 43, 77, 47, "styles"], [203, 49, 77, 53], [203, 50, 77, 54, "totalRow"], [203, 58, 77, 62], [203, 59, 77, 64], [204, 16, 77, 64, "children"], [204, 24, 77, 64], [204, 40, 78, 16], [204, 44, 78, 16, "_jsxDevRuntime"], [204, 58, 78, 16], [204, 59, 78, 16, "jsxDEV"], [204, 65, 78, 16], [204, 67, 78, 17, "_reactNative"], [204, 79, 78, 17], [204, 80, 78, 17, "Text"], [204, 84, 78, 21], [205, 18, 78, 22, "style"], [205, 23, 78, 27], [205, 25, 78, 29, "styles"], [205, 31, 78, 35], [205, 32, 78, 36, "totalLabel"], [205, 42, 78, 47], [206, 18, 78, 47, "children"], [206, 26, 78, 47], [206, 28, 78, 48], [207, 16, 78, 54], [208, 18, 78, 54, "fileName"], [208, 26, 78, 54], [208, 28, 78, 54, "_jsxFileName"], [208, 40, 78, 54], [209, 18, 78, 54, "lineNumber"], [209, 28, 78, 54], [210, 18, 78, 54, "columnNumber"], [210, 30, 78, 54], [211, 16, 78, 54], [211, 23, 78, 60], [211, 24, 78, 61], [211, 39, 79, 16], [211, 43, 79, 16, "_jsxDevRuntime"], [211, 57, 79, 16], [211, 58, 79, 16, "jsxDEV"], [211, 64, 79, 16], [211, 66, 79, 17, "_reactNative"], [211, 78, 79, 17], [211, 79, 79, 17, "Text"], [211, 83, 79, 21], [212, 18, 79, 22, "style"], [212, 23, 79, 27], [212, 25, 79, 29, "styles"], [212, 31, 79, 35], [212, 32, 79, 36, "totalValue"], [212, 42, 79, 47], [213, 18, 79, 47, "children"], [213, 26, 79, 47], [213, 29, 79, 48], [213, 32, 79, 49], [213, 34, 79, 50, "cart"], [213, 38, 79, 54], [213, 39, 79, 55, "total"], [213, 44, 79, 60], [213, 45, 79, 61, "toFixed"], [213, 52, 79, 68], [213, 53, 79, 69], [213, 54, 79, 70], [213, 55, 79, 71], [214, 16, 79, 71], [215, 18, 79, 71, "fileName"], [215, 26, 79, 71], [215, 28, 79, 71, "_jsxFileName"], [215, 40, 79, 71], [216, 18, 79, 71, "lineNumber"], [216, 28, 79, 71], [217, 18, 79, 71, "columnNumber"], [217, 30, 79, 71], [218, 16, 79, 71], [218, 23, 79, 78], [218, 24, 79, 79], [219, 14, 79, 79], [220, 16, 79, 79, "fileName"], [220, 24, 79, 79], [220, 26, 79, 79, "_jsxFileName"], [220, 38, 79, 79], [221, 16, 79, 79, "lineNumber"], [221, 26, 79, 79], [222, 16, 79, 79, "columnNumber"], [222, 28, 79, 79], [223, 14, 79, 79], [223, 21, 80, 20], [223, 22, 80, 21], [224, 12, 80, 21], [225, 14, 80, 21, "fileName"], [225, 22, 80, 21], [225, 24, 80, 21, "_jsxFileName"], [225, 36, 80, 21], [226, 14, 80, 21, "lineNumber"], [226, 24, 80, 21], [227, 14, 80, 21, "columnNumber"], [227, 26, 80, 21], [228, 12, 80, 21], [228, 19, 81, 18], [228, 20, 81, 19], [229, 10, 81, 19], [230, 12, 81, 19, "fileName"], [230, 20, 81, 19], [230, 22, 81, 19, "_jsxFileName"], [230, 34, 81, 19], [231, 12, 81, 19, "lineNumber"], [231, 22, 81, 19], [232, 12, 81, 19, "columnNumber"], [232, 24, 81, 19], [233, 10, 81, 19], [233, 17, 82, 16], [233, 18, 82, 17], [233, 34, 84, 10], [233, 38, 84, 10, "_jsxDevRuntime"], [233, 52, 84, 10], [233, 53, 84, 10, "jsxDEV"], [233, 59, 84, 10], [233, 61, 84, 11, "_reactNative"], [233, 73, 84, 11], [233, 74, 84, 11, "View"], [233, 78, 84, 15], [234, 12, 84, 16, "style"], [234, 17, 84, 21], [234, 19, 84, 23, "styles"], [234, 25, 84, 29], [234, 26, 84, 30, "emptyState"], [234, 36, 84, 41], [235, 12, 84, 41, "children"], [235, 20, 84, 41], [235, 36, 85, 12], [235, 40, 85, 12, "_jsxDevRuntime"], [235, 54, 85, 12], [235, 55, 85, 12, "jsxDEV"], [235, 61, 85, 12], [235, 63, 85, 13, "_reactNative"], [235, 75, 85, 13], [235, 76, 85, 13, "Text"], [235, 80, 85, 17], [236, 14, 85, 18, "style"], [236, 19, 85, 23], [236, 21, 85, 25, "styles"], [236, 27, 85, 31], [236, 28, 85, 32, "emptyTitle"], [236, 38, 85, 43], [237, 14, 85, 43, "children"], [237, 22, 85, 43], [237, 24, 85, 44], [238, 12, 85, 62], [239, 14, 85, 62, "fileName"], [239, 22, 85, 62], [239, 24, 85, 62, "_jsxFileName"], [239, 36, 85, 62], [240, 14, 85, 62, "lineNumber"], [240, 24, 85, 62], [241, 14, 85, 62, "columnNumber"], [241, 26, 85, 62], [242, 12, 85, 62], [242, 19, 85, 68], [242, 20, 85, 69], [242, 35, 86, 12], [242, 39, 86, 12, "_jsxDevRuntime"], [242, 53, 86, 12], [242, 54, 86, 12, "jsxDEV"], [242, 60, 86, 12], [242, 62, 86, 13, "_reactNative"], [242, 74, 86, 13], [242, 75, 86, 13, "Text"], [242, 79, 86, 17], [243, 14, 86, 18, "style"], [243, 19, 86, 23], [243, 21, 86, 25, "styles"], [243, 27, 86, 31], [243, 28, 86, 32, "emptySubtitle"], [243, 41, 86, 46], [244, 14, 86, 46, "children"], [244, 22, 86, 46], [244, 24, 86, 47], [245, 12, 88, 12], [246, 14, 88, 12, "fileName"], [246, 22, 88, 12], [246, 24, 88, 12, "_jsxFileName"], [246, 36, 88, 12], [247, 14, 88, 12, "lineNumber"], [247, 24, 88, 12], [248, 14, 88, 12, "columnNumber"], [248, 26, 88, 12], [249, 12, 88, 12], [249, 19, 88, 18], [249, 20, 88, 19], [249, 35, 89, 12], [249, 39, 89, 12, "_jsxDevRuntime"], [249, 53, 89, 12], [249, 54, 89, 12, "jsxDEV"], [249, 60, 89, 12], [249, 62, 89, 13, "_reactNative"], [249, 74, 89, 13], [249, 75, 89, 13, "TouchableOpacity"], [249, 91, 89, 29], [250, 14, 89, 30, "onPress"], [250, 21, 89, 37], [250, 23, 89, 39, "addTestItem"], [250, 34, 89, 51], [251, 14, 89, 52, "style"], [251, 19, 89, 57], [251, 21, 89, 59, "styles"], [251, 27, 89, 65], [251, 28, 89, 66, "testButton"], [251, 38, 89, 77], [252, 14, 89, 77, "children"], [252, 22, 89, 77], [252, 37, 90, 14], [252, 41, 90, 14, "_jsxDevRuntime"], [252, 55, 90, 14], [252, 56, 90, 14, "jsxDEV"], [252, 62, 90, 14], [252, 64, 90, 15, "_reactNative"], [252, 76, 90, 15], [252, 77, 90, 15, "Text"], [252, 81, 90, 19], [253, 16, 90, 20, "style"], [253, 21, 90, 25], [253, 23, 90, 27, "styles"], [253, 29, 90, 33], [253, 30, 90, 34, "testButtonText"], [253, 44, 90, 49], [254, 16, 90, 49, "children"], [254, 24, 90, 49], [254, 26, 90, 50], [255, 14, 90, 63], [256, 16, 90, 63, "fileName"], [256, 24, 90, 63], [256, 26, 90, 63, "_jsxFileName"], [256, 38, 90, 63], [257, 16, 90, 63, "lineNumber"], [257, 26, 90, 63], [258, 16, 90, 63, "columnNumber"], [258, 28, 90, 63], [259, 14, 90, 63], [259, 21, 90, 69], [260, 12, 90, 70], [261, 14, 90, 70, "fileName"], [261, 22, 90, 70], [261, 24, 90, 70, "_jsxFileName"], [261, 36, 90, 70], [262, 14, 90, 70, "lineNumber"], [262, 24, 90, 70], [263, 14, 90, 70, "columnNumber"], [263, 26, 90, 70], [264, 12, 90, 70], [264, 19, 91, 30], [264, 20, 91, 31], [265, 10, 91, 31], [266, 12, 91, 31, "fileName"], [266, 20, 91, 31], [266, 22, 91, 31, "_jsxFileName"], [266, 34, 91, 31], [267, 12, 91, 31, "lineNumber"], [267, 22, 91, 31], [268, 12, 91, 31, "columnNumber"], [268, 24, 91, 31], [269, 10, 91, 31], [269, 17, 92, 16], [270, 8, 93, 9], [271, 10, 93, 9, "fileName"], [271, 18, 93, 9], [271, 20, 93, 9, "_jsxFileName"], [271, 32, 93, 9], [272, 10, 93, 9, "lineNumber"], [272, 20, 93, 9], [273, 10, 93, 9, "columnNumber"], [273, 22, 93, 9], [274, 8, 93, 9], [274, 15, 94, 20], [275, 6, 94, 21], [276, 8, 94, 21, "fileName"], [276, 16, 94, 21], [276, 18, 94, 21, "_jsxFileName"], [276, 30, 94, 21], [277, 8, 94, 21, "lineNumber"], [277, 18, 94, 21], [278, 8, 94, 21, "columnNumber"], [278, 20, 94, 21], [279, 6, 94, 21], [279, 13, 95, 12], [279, 14, 95, 13], [279, 29, 98, 6], [279, 33, 98, 6, "_jsxDevRuntime"], [279, 47, 98, 6], [279, 48, 98, 6, "jsxDEV"], [279, 54, 98, 6], [279, 56, 98, 7, "_FooterNavigation"], [279, 73, 98, 7], [279, 74, 98, 7, "default"], [279, 81, 98, 23], [280, 8, 98, 24, "navigation"], [280, 18, 98, 34], [280, 20, 98, 36, "navigation"], [280, 30, 98, 47], [281, 8, 98, 48, "activeScreen"], [281, 20, 98, 60], [281, 22, 98, 61], [282, 6, 98, 67], [283, 8, 98, 67, "fileName"], [283, 16, 98, 67], [283, 18, 98, 67, "_jsxFileName"], [283, 30, 98, 67], [284, 8, 98, 67, "lineNumber"], [284, 18, 98, 67], [285, 8, 98, 67, "columnNumber"], [285, 20, 98, 67], [286, 6, 98, 67], [286, 13, 98, 69], [286, 14, 98, 70], [286, 29, 101, 6], [286, 33, 101, 6, "_jsxDevRuntime"], [286, 47, 101, 6], [286, 48, 101, 6, "jsxDEV"], [286, 54, 101, 6], [286, 56, 101, 7, "_reactNativeSafeAreaContext"], [286, 83, 101, 7], [286, 84, 101, 7, "SafeAreaView"], [286, 96, 101, 19], [287, 8, 101, 20, "style"], [287, 13, 101, 25], [287, 15, 101, 27], [288, 10, 101, 29, "backgroundColor"], [288, 25, 101, 44], [288, 27, 101, 46], [289, 8, 101, 56], [289, 9, 101, 58], [290, 8, 101, 59, "edges"], [290, 13, 101, 64], [290, 15, 101, 66], [290, 16, 101, 67], [290, 24, 101, 75], [291, 6, 101, 77], [292, 8, 101, 77, "fileName"], [292, 16, 101, 77], [292, 18, 101, 77, "_jsxFileName"], [292, 30, 101, 77], [293, 8, 101, 77, "lineNumber"], [293, 18, 101, 77], [294, 8, 101, 77, "columnNumber"], [294, 20, 101, 77], [295, 6, 101, 77], [295, 13, 101, 79], [295, 14, 101, 80], [296, 4, 101, 80], [297, 6, 101, 80, "fileName"], [297, 14, 101, 80], [297, 16, 101, 80, "_jsxFileName"], [297, 28, 101, 80], [298, 6, 101, 80, "lineNumber"], [298, 16, 101, 80], [299, 6, 101, 80, "columnNumber"], [299, 18, 101, 80], [300, 4, 101, 80], [300, 11, 102, 10], [300, 12, 102, 11], [301, 2, 104, 0], [302, 2, 104, 1, "_s"], [302, 4, 104, 1], [302, 5, 7, 24, "CartScreen"], [302, 15, 7, 34], [303, 4, 7, 34], [303, 12, 8, 59, "useCart"], [303, 32, 8, 66], [304, 2, 8, 66], [305, 2, 8, 66, "_c"], [305, 4, 8, 66], [305, 7, 7, 24, "CartScreen"], [305, 17, 7, 34], [306, 2, 106, 0], [306, 6, 106, 6, "styles"], [306, 12, 106, 12], [306, 15, 106, 15, "StyleSheet"], [306, 38, 106, 25], [306, 39, 106, 26, "create"], [306, 45, 106, 32], [306, 46, 106, 33], [307, 4, 107, 2, "container"], [307, 13, 107, 11], [307, 15, 107, 13], [308, 6, 108, 4, "flex"], [308, 10, 108, 8], [308, 12, 108, 10], [308, 13, 108, 11], [309, 6, 109, 4, "backgroundColor"], [309, 21, 109, 19], [309, 23, 109, 21], [310, 4, 110, 2], [310, 5, 110, 3], [311, 4, 111, 2, "header"], [311, 10, 111, 8], [311, 12, 111, 10], [312, 6, 112, 4, "flexDirection"], [312, 19, 112, 17], [312, 21, 112, 19], [312, 26, 112, 24], [313, 6, 113, 4, "justifyContent"], [313, 20, 113, 18], [313, 22, 113, 20], [313, 37, 113, 35], [314, 6, 114, 4, "alignItems"], [314, 16, 114, 14], [314, 18, 114, 16], [314, 26, 114, 24], [315, 6, 115, 4, "paddingHorizontal"], [315, 23, 115, 21], [315, 25, 115, 23], [315, 27, 115, 25], [316, 6, 116, 4, "paddingTop"], [316, 16, 116, 14], [316, 18, 116, 16], [316, 20, 116, 18], [317, 6, 117, 4, "paddingBottom"], [317, 19, 117, 17], [317, 21, 117, 19], [318, 4, 118, 2], [318, 5, 118, 3], [319, 4, 119, 2, "title"], [319, 9, 119, 7], [319, 11, 119, 9], [320, 6, 120, 4, "fontSize"], [320, 14, 120, 12], [320, 16, 120, 14], [320, 18, 120, 16], [321, 6, 121, 4, "fontWeight"], [321, 16, 121, 14], [321, 18, 121, 16], [321, 24, 121, 22], [322, 6, 122, 4, "color"], [322, 11, 122, 9], [322, 13, 122, 11], [323, 4, 123, 2], [323, 5, 123, 3], [324, 4, 124, 2, "clearButton"], [324, 15, 124, 13], [324, 17, 124, 15], [325, 6, 125, 4, "paddingHorizontal"], [325, 23, 125, 21], [325, 25, 125, 23], [325, 27, 125, 25], [326, 6, 126, 4, "paddingVertical"], [326, 21, 126, 19], [326, 23, 126, 21], [326, 24, 126, 22], [327, 6, 127, 4, "backgroundColor"], [327, 21, 127, 19], [327, 23, 127, 21], [327, 32, 127, 30], [328, 6, 128, 4, "borderRadius"], [328, 18, 128, 16], [328, 20, 128, 18], [329, 4, 129, 2], [329, 5, 129, 3], [330, 4, 130, 2, "clearButtonText"], [330, 19, 130, 17], [330, 21, 130, 19], [331, 6, 131, 4, "color"], [331, 11, 131, 9], [331, 13, 131, 11], [331, 20, 131, 18], [332, 6, 132, 4, "fontSize"], [332, 14, 132, 12], [332, 16, 132, 14], [332, 18, 132, 16], [333, 6, 133, 4, "fontWeight"], [333, 16, 133, 14], [333, 18, 133, 16], [334, 4, 134, 2], [334, 5, 134, 3], [335, 4, 135, 2, "content"], [335, 11, 135, 9], [335, 13, 135, 11], [336, 6, 136, 4, "flex"], [336, 10, 136, 8], [336, 12, 136, 10], [336, 13, 136, 11], [337, 6, 137, 4, "paddingHorizontal"], [337, 23, 137, 21], [337, 25, 137, 23], [338, 4, 138, 2], [338, 5, 138, 3], [339, 4, 139, 2, "itemCount"], [339, 13, 139, 11], [339, 15, 139, 13], [340, 6, 140, 4, "fontSize"], [340, 14, 140, 12], [340, 16, 140, 14], [340, 18, 140, 16], [341, 6, 141, 4, "color"], [341, 11, 141, 9], [341, 13, 141, 11], [341, 22, 141, 20], [342, 6, 142, 4, "marginBottom"], [342, 18, 142, 16], [342, 20, 142, 18], [343, 4, 143, 2], [343, 5, 143, 3], [344, 4, 144, 2, "cartItem"], [344, 12, 144, 10], [344, 14, 144, 12], [345, 6, 145, 4, "backgroundColor"], [345, 21, 145, 19], [345, 23, 145, 21], [345, 30, 145, 28], [346, 6, 146, 4, "borderRadius"], [346, 18, 146, 16], [346, 20, 146, 18], [346, 22, 146, 20], [347, 6, 147, 4, "padding"], [347, 13, 147, 11], [347, 15, 147, 13], [347, 17, 147, 15], [348, 6, 148, 4, "marginBottom"], [348, 18, 148, 16], [348, 20, 148, 18], [348, 22, 148, 20], [349, 6, 149, 4, "flexDirection"], [349, 19, 149, 17], [349, 21, 149, 19], [349, 26, 149, 24], [350, 6, 150, 4, "justifyContent"], [350, 20, 150, 18], [350, 22, 150, 20], [350, 37, 150, 35], [351, 6, 151, 4, "alignItems"], [351, 16, 151, 14], [351, 18, 151, 16], [351, 26, 151, 24], [352, 6, 152, 4, "shadowColor"], [352, 17, 152, 15], [352, 19, 152, 17], [352, 25, 152, 23], [353, 6, 153, 4, "shadowOffset"], [353, 18, 153, 16], [353, 20, 153, 18], [354, 8, 153, 20, "width"], [354, 13, 153, 25], [354, 15, 153, 27], [354, 16, 153, 28], [355, 8, 153, 30, "height"], [355, 14, 153, 36], [355, 16, 153, 38], [356, 6, 153, 40], [356, 7, 153, 41], [357, 6, 154, 4, "shadowOpacity"], [357, 19, 154, 17], [357, 21, 154, 19], [357, 24, 154, 22], [358, 6, 155, 4, "shadowRadius"], [358, 18, 155, 16], [358, 20, 155, 18], [358, 21, 155, 19], [359, 6, 156, 4, "elevation"], [359, 15, 156, 13], [359, 17, 156, 15], [360, 4, 157, 2], [360, 5, 157, 3], [361, 4, 158, 2, "itemInfo"], [361, 12, 158, 10], [361, 14, 158, 12], [362, 6, 159, 4, "flex"], [362, 10, 159, 8], [362, 12, 159, 10], [363, 4, 160, 2], [363, 5, 160, 3], [364, 4, 161, 2, "itemName"], [364, 12, 161, 10], [364, 14, 161, 12], [365, 6, 162, 4, "fontSize"], [365, 14, 162, 12], [365, 16, 162, 14], [365, 18, 162, 16], [366, 6, 163, 4, "fontWeight"], [366, 16, 163, 14], [366, 18, 163, 16], [366, 23, 163, 21], [367, 6, 164, 4, "color"], [367, 11, 164, 9], [367, 13, 164, 11], [367, 22, 164, 20], [368, 6, 165, 4, "marginBottom"], [368, 18, 165, 16], [368, 20, 165, 18], [369, 4, 166, 2], [369, 5, 166, 3], [370, 4, 167, 2, "itemDescription"], [370, 19, 167, 17], [370, 21, 167, 19], [371, 6, 168, 4, "fontSize"], [371, 14, 168, 12], [371, 16, 168, 14], [371, 18, 168, 16], [372, 6, 169, 4, "color"], [372, 11, 169, 9], [372, 13, 169, 11], [372, 22, 169, 20], [373, 6, 170, 4, "marginBottom"], [373, 18, 170, 16], [373, 20, 170, 18], [374, 4, 171, 2], [374, 5, 171, 3], [375, 4, 172, 2, "itemPrice"], [375, 13, 172, 11], [375, 15, 172, 13], [376, 6, 173, 4, "fontSize"], [376, 14, 173, 12], [376, 16, 173, 14], [376, 18, 173, 16], [377, 6, 174, 4, "color"], [377, 11, 174, 9], [377, 13, 174, 11], [378, 4, 175, 2], [378, 5, 175, 3], [379, 4, 176, 2, "itemTotal"], [379, 13, 176, 11], [379, 15, 176, 13], [380, 6, 177, 4, "fontSize"], [380, 14, 177, 12], [380, 16, 177, 14], [380, 18, 177, 16], [381, 6, 178, 4, "fontWeight"], [381, 16, 178, 14], [381, 18, 178, 16], [381, 23, 178, 21], [382, 6, 179, 4, "color"], [382, 11, 179, 9], [382, 13, 179, 11], [383, 4, 180, 2], [383, 5, 180, 3], [384, 4, 181, 2, "summary"], [384, 11, 181, 9], [384, 13, 181, 11], [385, 6, 182, 4, "backgroundColor"], [385, 21, 182, 19], [385, 23, 182, 21], [385, 30, 182, 28], [386, 6, 183, 4, "borderRadius"], [386, 18, 183, 16], [386, 20, 183, 18], [386, 22, 183, 20], [387, 6, 184, 4, "padding"], [387, 13, 184, 11], [387, 15, 184, 13], [387, 17, 184, 15], [388, 6, 185, 4, "marginTop"], [388, 15, 185, 13], [388, 17, 185, 15], [388, 19, 185, 17], [389, 6, 186, 4, "marginBottom"], [389, 18, 186, 16], [389, 20, 186, 18], [389, 22, 186, 20], [390, 6, 187, 4, "shadowColor"], [390, 17, 187, 15], [390, 19, 187, 17], [390, 25, 187, 23], [391, 6, 188, 4, "shadowOffset"], [391, 18, 188, 16], [391, 20, 188, 18], [392, 8, 188, 20, "width"], [392, 13, 188, 25], [392, 15, 188, 27], [392, 16, 188, 28], [393, 8, 188, 30, "height"], [393, 14, 188, 36], [393, 16, 188, 38], [394, 6, 188, 40], [394, 7, 188, 41], [395, 6, 189, 4, "shadowOpacity"], [395, 19, 189, 17], [395, 21, 189, 19], [395, 24, 189, 22], [396, 6, 190, 4, "shadowRadius"], [396, 18, 190, 16], [396, 20, 190, 18], [396, 21, 190, 19], [397, 6, 191, 4, "elevation"], [397, 15, 191, 13], [397, 17, 191, 15], [398, 4, 192, 2], [398, 5, 192, 3], [399, 4, 193, 2, "summaryRow"], [399, 14, 193, 12], [399, 16, 193, 14], [400, 6, 194, 4, "flexDirection"], [400, 19, 194, 17], [400, 21, 194, 19], [400, 26, 194, 24], [401, 6, 195, 4, "justifyContent"], [401, 20, 195, 18], [401, 22, 195, 20], [401, 37, 195, 35], [402, 6, 196, 4, "marginBottom"], [402, 18, 196, 16], [402, 20, 196, 18], [403, 4, 197, 2], [403, 5, 197, 3], [404, 4, 198, 2, "summaryLabel"], [404, 16, 198, 14], [404, 18, 198, 16], [405, 6, 199, 4, "fontSize"], [405, 14, 199, 12], [405, 16, 199, 14], [405, 18, 199, 16], [406, 6, 200, 4, "color"], [406, 11, 200, 9], [406, 13, 200, 11], [407, 4, 201, 2], [407, 5, 201, 3], [408, 4, 202, 2, "summaryValue"], [408, 16, 202, 14], [408, 18, 202, 16], [409, 6, 203, 4, "fontSize"], [409, 14, 203, 12], [409, 16, 203, 14], [409, 18, 203, 16], [410, 6, 204, 4, "color"], [410, 11, 204, 9], [410, 13, 204, 11], [411, 4, 205, 2], [411, 5, 205, 3], [412, 4, 206, 2, "totalRow"], [412, 12, 206, 10], [412, 14, 206, 12], [413, 6, 207, 4, "borderTopWidth"], [413, 20, 207, 18], [413, 22, 207, 20], [413, 23, 207, 21], [414, 6, 208, 4, "borderTopColor"], [414, 20, 208, 18], [414, 22, 208, 20], [414, 31, 208, 29], [415, 6, 209, 4, "paddingTop"], [415, 16, 209, 14], [415, 18, 209, 16], [415, 19, 209, 17], [416, 6, 210, 4, "marginTop"], [416, 15, 210, 13], [416, 17, 210, 15], [416, 18, 210, 16], [417, 6, 211, 4, "marginBottom"], [417, 18, 211, 16], [417, 20, 211, 18], [418, 4, 212, 2], [418, 5, 212, 3], [419, 4, 213, 2, "totalLabel"], [419, 14, 213, 12], [419, 16, 213, 14], [420, 6, 214, 4, "fontSize"], [420, 14, 214, 12], [420, 16, 214, 14], [420, 18, 214, 16], [421, 6, 215, 4, "fontWeight"], [421, 16, 215, 14], [421, 18, 215, 16], [421, 23, 215, 21], [422, 6, 216, 4, "color"], [422, 11, 216, 9], [422, 13, 216, 11], [423, 4, 217, 2], [423, 5, 217, 3], [424, 4, 218, 2, "totalValue"], [424, 14, 218, 12], [424, 16, 218, 14], [425, 6, 219, 4, "fontSize"], [425, 14, 219, 12], [425, 16, 219, 14], [425, 18, 219, 16], [426, 6, 220, 4, "fontWeight"], [426, 16, 220, 14], [426, 18, 220, 16], [426, 23, 220, 21], [427, 6, 221, 4, "color"], [427, 11, 221, 9], [427, 13, 221, 11], [428, 4, 222, 2], [428, 5, 222, 3], [429, 4, 223, 2, "emptyState"], [429, 14, 223, 12], [429, 16, 223, 14], [430, 6, 224, 4, "alignItems"], [430, 16, 224, 14], [430, 18, 224, 16], [430, 26, 224, 24], [431, 6, 225, 4, "paddingTop"], [431, 16, 225, 14], [431, 18, 225, 16], [432, 4, 226, 2], [432, 5, 226, 3], [433, 4, 227, 2, "emptyTitle"], [433, 14, 227, 12], [433, 16, 227, 14], [434, 6, 228, 4, "fontSize"], [434, 14, 228, 12], [434, 16, 228, 14], [434, 18, 228, 16], [435, 6, 229, 4, "fontWeight"], [435, 16, 229, 14], [435, 18, 229, 16], [435, 23, 229, 21], [436, 6, 230, 4, "color"], [436, 11, 230, 9], [436, 13, 230, 11], [436, 22, 230, 20], [437, 6, 231, 4, "marginBottom"], [437, 18, 231, 16], [437, 20, 231, 18], [438, 4, 232, 2], [438, 5, 232, 3], [439, 4, 233, 2, "emptySubtitle"], [439, 17, 233, 15], [439, 19, 233, 17], [440, 6, 234, 4, "fontSize"], [440, 14, 234, 12], [440, 16, 234, 14], [440, 18, 234, 16], [441, 6, 235, 4, "color"], [441, 11, 235, 9], [441, 13, 235, 11], [441, 22, 235, 20], [442, 6, 236, 4, "textAlign"], [442, 15, 236, 13], [442, 17, 236, 15], [442, 25, 236, 23], [443, 6, 237, 4, "marginBottom"], [443, 18, 237, 16], [443, 20, 237, 18], [444, 4, 238, 2], [444, 5, 238, 3], [445, 4, 239, 2, "testButton"], [445, 14, 239, 12], [445, 16, 239, 14], [446, 6, 240, 4, "backgroundColor"], [446, 21, 240, 19], [446, 23, 240, 21], [446, 32, 240, 30], [447, 6, 241, 4, "paddingHorizontal"], [447, 23, 241, 21], [447, 25, 241, 23], [447, 27, 241, 25], [448, 6, 242, 4, "paddingVertical"], [448, 21, 242, 19], [448, 23, 242, 21], [448, 25, 242, 23], [449, 6, 243, 4, "borderRadius"], [449, 18, 243, 16], [449, 20, 243, 18], [450, 4, 244, 2], [450, 5, 244, 3], [451, 4, 245, 2, "testButtonText"], [451, 18, 245, 16], [451, 20, 245, 18], [452, 6, 246, 4, "color"], [452, 11, 246, 9], [452, 13, 246, 11], [452, 20, 246, 18], [453, 6, 247, 4, "fontSize"], [453, 14, 247, 12], [453, 16, 247, 14], [453, 18, 247, 16], [454, 6, 248, 4, "fontWeight"], [454, 16, 248, 14], [454, 18, 248, 16], [455, 4, 249, 2], [456, 2, 250, 0], [456, 3, 250, 1], [456, 4, 250, 2], [457, 2, 250, 3], [457, 6, 250, 3, "_c"], [457, 8, 250, 3], [458, 2, 250, 3, "$RefreshReg$"], [458, 14, 250, 3], [458, 15, 250, 3, "_c"], [458, 17, 250, 3], [459, 0, 250, 3], [459, 3]], "functionMap": {"names": ["<global>", "CartScreen", "addTestItem", "cart.items.map$argument_0"], "mappings": "AAA;eCM;sBCK;GDY;4BEyB;aFa;CD0C"}}, "type": "js/module"}]}