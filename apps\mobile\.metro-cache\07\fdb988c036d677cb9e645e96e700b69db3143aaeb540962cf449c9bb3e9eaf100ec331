{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 40, "index": 55}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.conditional = conditional;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var add = _reactNative.Animated.add,\n    multiply = _reactNative.Animated.multiply;\n\n  /**\n   * Use an Animated Node based on a condition. Similar to Reanimated's `cond`.\n   *\n   * @param condition Animated Node representing the condition, must be 0 or 1, 1 means `true`, 0 means `false`\n   * @param main Animated Node to use if the condition is `true`\n   * @param fallback Animated Node to use if the condition is `false`\n   */\n  function conditional(condition, main, fallback) {\n    // To implement this behavior, we multiply the main node with the condition.\n    // So if condition is 0, result will be 0, and if condition is 1, result will be main node.\n    // Then we multiple reverse of the condition (0 if condition is 1) with the fallback.\n    // So if condition is 0, result will be fallback node, and if condition is 1, result will be 0,\n    // This way, one of them will always be 0, and other one will be the value we need.\n    // In the end we add them both together, 0 + value we need = value we need\n    return add(multiply(condition, main), multiply(condition.interpolate({\n      inputRange: [0, 1],\n      outputRange: [1, 0]\n    }), fallback));\n  }\n});", "lineCount": 31, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "conditional"], [7, 21, 1, 13], [7, 24, 1, 13, "conditional"], [7, 35, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_reactNative"], [8, 18, 3, 0], [8, 21, 3, 0, "require"], [8, 28, 3, 0], [8, 29, 3, 0, "_dependencyMap"], [8, 43, 3, 0], [9, 2, 4, 0], [9, 6, 5, 2, "add"], [9, 9, 5, 5], [9, 12, 7, 4, "Animated"], [9, 33, 7, 12], [9, 34, 5, 2, "add"], [9, 37, 5, 5], [10, 4, 6, 2, "multiply"], [10, 12, 6, 10], [10, 15, 7, 4, "Animated"], [10, 36, 7, 12], [10, 37, 6, 2, "multiply"], [10, 45, 6, 10], [12, 2, 9, 0], [13, 0, 10, 0], [14, 0, 11, 0], [15, 0, 12, 0], [16, 0, 13, 0], [17, 0, 14, 0], [18, 0, 15, 0], [19, 2, 16, 7], [19, 11, 16, 16, "conditional"], [19, 22, 16, 27, "conditional"], [19, 23, 16, 28, "condition"], [19, 32, 16, 37], [19, 34, 16, 39, "main"], [19, 38, 16, 43], [19, 40, 16, 45, "fallback"], [19, 48, 16, 53], [19, 50, 16, 55], [20, 4, 17, 2], [21, 4, 18, 2], [22, 4, 19, 2], [23, 4, 20, 2], [24, 4, 21, 2], [25, 4, 22, 2], [26, 4, 23, 2], [26, 11, 23, 9, "add"], [26, 14, 23, 12], [26, 15, 23, 13, "multiply"], [26, 23, 23, 21], [26, 24, 23, 22, "condition"], [26, 33, 23, 31], [26, 35, 23, 33, "main"], [26, 39, 23, 37], [26, 40, 23, 38], [26, 42, 23, 40, "multiply"], [26, 50, 23, 48], [26, 51, 23, 49, "condition"], [26, 60, 23, 58], [26, 61, 23, 59, "interpolate"], [26, 72, 23, 70], [26, 73, 23, 71], [27, 6, 24, 4, "inputRange"], [27, 16, 24, 14], [27, 18, 24, 16], [27, 19, 24, 17], [27, 20, 24, 18], [27, 22, 24, 20], [27, 23, 24, 21], [27, 24, 24, 22], [28, 6, 25, 4, "outputRange"], [28, 17, 25, 15], [28, 19, 25, 17], [28, 20, 25, 18], [28, 21, 25, 19], [28, 23, 25, 21], [28, 24, 25, 22], [29, 4, 26, 2], [29, 5, 26, 3], [29, 6, 26, 4], [29, 8, 26, 6, "fallback"], [29, 16, 26, 14], [29, 17, 26, 15], [29, 18, 26, 16], [30, 2, 27, 0], [31, 0, 27, 1], [31, 3]], "functionMap": {"names": ["<global>", "conditional"], "mappings": "AAA;OCe;CDW"}}, "type": "js/module"}]}