{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./AnimatedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 44}}], "key": "MXjn1CQaLNtMiiooxlb5qObVfR0=", "exportNames": ["*"]}}, {"name": "./AnimatedWithChildren", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 58}}], "key": "IUkIH5MYbr+OqFsp9MMa/cV/D0g=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _AnimatedValue = _interopRequireDefault(require(_dependencyMap[7], \"./AnimatedValue\"));\n  var _AnimatedWithChildren2 = _interopRequireDefault(require(_dependencyMap[8], \"./AnimatedWithChildren\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[9], \"invariant\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var _uniqueId = 1;\n  var AnimatedValueXY = exports.default = /*#__PURE__*/function (_AnimatedWithChildren) {\n    function AnimatedValueXY(valueIn, config) {\n      var _this;\n      (0, _classCallCheck2.default)(this, AnimatedValueXY);\n      _this = _callSuper(this, AnimatedValueXY, [config]);\n      var value = valueIn || {\n        x: 0,\n        y: 0\n      };\n      if (typeof value.x === 'number' && typeof value.y === 'number') {\n        _this.x = new _AnimatedValue.default(value.x);\n        _this.y = new _AnimatedValue.default(value.y);\n      } else {\n        (0, _invariant.default)(value.x instanceof _AnimatedValue.default && value.y instanceof _AnimatedValue.default, 'AnimatedValueXY must be initialized with an object of numbers or ' + 'AnimatedValues.');\n        _this.x = value.x;\n        _this.y = value.y;\n      }\n      _this._listeners = {};\n      if (config && config.useNativeDriver) {\n        _this.__makeNative();\n      }\n      return _this;\n    }\n    (0, _inherits2.default)(AnimatedValueXY, _AnimatedWithChildren);\n    return (0, _createClass2.default)(AnimatedValueXY, [{\n      key: \"setValue\",\n      value: function setValue(value) {\n        this.x.setValue(value.x);\n        this.y.setValue(value.y);\n      }\n    }, {\n      key: \"setOffset\",\n      value: function setOffset(offset) {\n        this.x.setOffset(offset.x);\n        this.y.setOffset(offset.y);\n      }\n    }, {\n      key: \"flattenOffset\",\n      value: function flattenOffset() {\n        this.x.flattenOffset();\n        this.y.flattenOffset();\n      }\n    }, {\n      key: \"extractOffset\",\n      value: function extractOffset() {\n        this.x.extractOffset();\n        this.y.extractOffset();\n      }\n    }, {\n      key: \"__getValue\",\n      value: function __getValue() {\n        return {\n          x: this.x.__getValue(),\n          y: this.y.__getValue()\n        };\n      }\n    }, {\n      key: \"resetAnimation\",\n      value: function resetAnimation(callback) {\n        this.x.resetAnimation();\n        this.y.resetAnimation();\n        callback && callback(this.__getValue());\n      }\n    }, {\n      key: \"stopAnimation\",\n      value: function stopAnimation(callback) {\n        this.x.stopAnimation();\n        this.y.stopAnimation();\n        callback && callback(this.__getValue());\n      }\n    }, {\n      key: \"addListener\",\n      value: function addListener(callback) {\n        var id = String(_uniqueId++);\n        var jointCallback = _ref => {\n          var number = _ref.value;\n          callback(this.__getValue());\n        };\n        this._listeners[id] = {\n          x: this.x.addListener(jointCallback),\n          y: this.y.addListener(jointCallback)\n        };\n        return id;\n      }\n    }, {\n      key: \"removeListener\",\n      value: function removeListener(id) {\n        this.x.removeListener(this._listeners[id].x);\n        this.y.removeListener(this._listeners[id].y);\n        delete this._listeners[id];\n      }\n    }, {\n      key: \"removeAllListeners\",\n      value: function removeAllListeners() {\n        this.x.removeAllListeners();\n        this.y.removeAllListeners();\n        this._listeners = {};\n      }\n    }, {\n      key: \"getLayout\",\n      value: function getLayout() {\n        return {\n          left: this.x,\n          top: this.y\n        };\n      }\n    }, {\n      key: \"getTranslateTransform\",\n      value: function getTranslateTransform() {\n        return [{\n          translateX: this.x\n        }, {\n          translateY: this.y\n        }];\n      }\n    }, {\n      key: \"__attach\",\n      value: function __attach() {\n        this.x.__addChild(this);\n        this.y.__addChild(this);\n        _superPropGet(AnimatedValueXY, \"__attach\", this, 3)([]);\n      }\n    }, {\n      key: \"__detach\",\n      value: function __detach() {\n        this.x.__removeChild(this);\n        this.y.__removeChild(this);\n        _superPropGet(AnimatedValueXY, \"__detach\", this, 3)([]);\n      }\n    }, {\n      key: \"__makeNative\",\n      value: function __makeNative(platformConfig) {\n        this.x.__makeNative(platformConfig);\n        this.y.__makeNative(platformConfig);\n        _superPropGet(AnimatedValueXY, \"__makeNative\", this, 3)([platformConfig]);\n      }\n    }]);\n  }(_AnimatedWithChildren2.default);\n});", "lineCount": 160, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_classCallCheck2"], [9, 22, 11, 13], [9, 25, 11, 13, "_interopRequireDefault"], [9, 47, 11, 13], [9, 48, 11, 13, "require"], [9, 55, 11, 13], [9, 56, 11, 13, "_dependencyMap"], [9, 70, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_createClass2"], [10, 19, 11, 13], [10, 22, 11, 13, "_interopRequireDefault"], [10, 44, 11, 13], [10, 45, 11, 13, "require"], [10, 52, 11, 13], [10, 53, 11, 13, "_dependencyMap"], [10, 67, 11, 13], [11, 2, 11, 13], [11, 6, 11, 13, "_possibleConstructorReturn2"], [11, 33, 11, 13], [11, 36, 11, 13, "_interopRequireDefault"], [11, 58, 11, 13], [11, 59, 11, 13, "require"], [11, 66, 11, 13], [11, 67, 11, 13, "_dependencyMap"], [11, 81, 11, 13], [12, 2, 11, 13], [12, 6, 11, 13, "_getPrototypeOf2"], [12, 22, 11, 13], [12, 25, 11, 13, "_interopRequireDefault"], [12, 47, 11, 13], [12, 48, 11, 13, "require"], [12, 55, 11, 13], [12, 56, 11, 13, "_dependencyMap"], [12, 70, 11, 13], [13, 2, 11, 13], [13, 6, 11, 13, "_get2"], [13, 11, 11, 13], [13, 14, 11, 13, "_interopRequireDefault"], [13, 36, 11, 13], [13, 37, 11, 13, "require"], [13, 44, 11, 13], [13, 45, 11, 13, "_dependencyMap"], [13, 59, 11, 13], [14, 2, 11, 13], [14, 6, 11, 13, "_inherits2"], [14, 16, 11, 13], [14, 19, 11, 13, "_interopRequireDefault"], [14, 41, 11, 13], [14, 42, 11, 13, "require"], [14, 49, 11, 13], [14, 50, 11, 13, "_dependencyMap"], [14, 64, 11, 13], [15, 2, 16, 0], [15, 6, 16, 0, "_AnimatedValue"], [15, 20, 16, 0], [15, 23, 16, 0, "_interopRequireDefault"], [15, 45, 16, 0], [15, 46, 16, 0, "require"], [15, 53, 16, 0], [15, 54, 16, 0, "_dependencyMap"], [15, 68, 16, 0], [16, 2, 17, 0], [16, 6, 17, 0, "_AnimatedWithChildren2"], [16, 28, 17, 0], [16, 31, 17, 0, "_interopRequireDefault"], [16, 53, 17, 0], [16, 54, 17, 0, "require"], [16, 61, 17, 0], [16, 62, 17, 0, "_dependencyMap"], [16, 76, 17, 0], [17, 2, 18, 0], [17, 6, 18, 0, "_invariant"], [17, 16, 18, 0], [17, 19, 18, 0, "_interopRequireDefault"], [17, 41, 18, 0], [17, 42, 18, 0, "require"], [17, 49, 18, 0], [17, 50, 18, 0, "_dependencyMap"], [17, 64, 18, 0], [18, 2, 18, 34], [18, 11, 18, 34, "_callSuper"], [18, 22, 18, 34, "t"], [18, 23, 18, 34], [18, 25, 18, 34, "o"], [18, 26, 18, 34], [18, 28, 18, 34, "e"], [18, 29, 18, 34], [18, 40, 18, 34, "o"], [18, 41, 18, 34], [18, 48, 18, 34, "_getPrototypeOf2"], [18, 64, 18, 34], [18, 65, 18, 34, "default"], [18, 72, 18, 34], [18, 74, 18, 34, "o"], [18, 75, 18, 34], [18, 82, 18, 34, "_possibleConstructorReturn2"], [18, 109, 18, 34], [18, 110, 18, 34, "default"], [18, 117, 18, 34], [18, 119, 18, 34, "t"], [18, 120, 18, 34], [18, 122, 18, 34, "_isNativeReflectConstruct"], [18, 147, 18, 34], [18, 152, 18, 34, "Reflect"], [18, 159, 18, 34], [18, 160, 18, 34, "construct"], [18, 169, 18, 34], [18, 170, 18, 34, "o"], [18, 171, 18, 34], [18, 173, 18, 34, "e"], [18, 174, 18, 34], [18, 186, 18, 34, "_getPrototypeOf2"], [18, 202, 18, 34], [18, 203, 18, 34, "default"], [18, 210, 18, 34], [18, 212, 18, 34, "t"], [18, 213, 18, 34], [18, 215, 18, 34, "constructor"], [18, 226, 18, 34], [18, 230, 18, 34, "o"], [18, 231, 18, 34], [18, 232, 18, 34, "apply"], [18, 237, 18, 34], [18, 238, 18, 34, "t"], [18, 239, 18, 34], [18, 241, 18, 34, "e"], [18, 242, 18, 34], [19, 2, 18, 34], [19, 11, 18, 34, "_isNativeReflectConstruct"], [19, 37, 18, 34], [19, 51, 18, 34, "t"], [19, 52, 18, 34], [19, 56, 18, 34, "Boolean"], [19, 63, 18, 34], [19, 64, 18, 34, "prototype"], [19, 73, 18, 34], [19, 74, 18, 34, "valueOf"], [19, 81, 18, 34], [19, 82, 18, 34, "call"], [19, 86, 18, 34], [19, 87, 18, 34, "Reflect"], [19, 94, 18, 34], [19, 95, 18, 34, "construct"], [19, 104, 18, 34], [19, 105, 18, 34, "Boolean"], [19, 112, 18, 34], [19, 145, 18, 34, "t"], [19, 146, 18, 34], [19, 159, 18, 34, "_isNativeReflectConstruct"], [19, 184, 18, 34], [19, 196, 18, 34, "_isNativeReflectConstruct"], [19, 197, 18, 34], [19, 210, 18, 34, "t"], [19, 211, 18, 34], [20, 2, 18, 34], [20, 11, 18, 34, "_superPropGet"], [20, 25, 18, 34, "t"], [20, 26, 18, 34], [20, 28, 18, 34, "o"], [20, 29, 18, 34], [20, 31, 18, 34, "e"], [20, 32, 18, 34], [20, 34, 18, 34, "r"], [20, 35, 18, 34], [20, 43, 18, 34, "p"], [20, 44, 18, 34], [20, 51, 18, 34, "_get2"], [20, 56, 18, 34], [20, 57, 18, 34, "default"], [20, 64, 18, 34], [20, 70, 18, 34, "_getPrototypeOf2"], [20, 86, 18, 34], [20, 87, 18, 34, "default"], [20, 94, 18, 34], [20, 100, 18, 34, "r"], [20, 101, 18, 34], [20, 104, 18, 34, "t"], [20, 105, 18, 34], [20, 106, 18, 34, "prototype"], [20, 115, 18, 34], [20, 118, 18, 34, "t"], [20, 119, 18, 34], [20, 122, 18, 34, "o"], [20, 123, 18, 34], [20, 125, 18, 34, "e"], [20, 126, 18, 34], [20, 140, 18, 34, "r"], [20, 141, 18, 34], [20, 166, 18, 34, "p"], [20, 167, 18, 34], [20, 180, 18, 34, "t"], [20, 181, 18, 34], [20, 192, 18, 34, "p"], [20, 193, 18, 34], [20, 194, 18, 34, "apply"], [20, 199, 18, 34], [20, 200, 18, 34, "e"], [20, 201, 18, 34], [20, 203, 18, 34, "t"], [20, 204, 18, 34], [20, 211, 18, 34, "p"], [20, 212, 18, 34], [21, 2, 26, 0], [21, 6, 26, 4, "_uniqueId"], [21, 15, 26, 13], [21, 18, 26, 16], [21, 19, 26, 17], [22, 2, 26, 18], [22, 6, 34, 21, "AnimatedValueXY"], [22, 21, 34, 36], [22, 24, 34, 36, "exports"], [22, 31, 34, 36], [22, 32, 34, 36, "default"], [22, 39, 34, 36], [22, 65, 34, 36, "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>"], [22, 86, 34, 36], [23, 4, 46, 2], [23, 13, 46, 2, "AnimatedValueXY"], [23, 29, 47, 4, "valueIn"], [23, 36, 51, 5], [23, 38, 52, 4, "config"], [23, 44, 52, 35], [23, 46, 53, 4], [24, 6, 53, 4], [24, 10, 53, 4, "_this"], [24, 15, 53, 4], [25, 6, 53, 4], [25, 10, 53, 4, "_classCallCheck2"], [25, 26, 53, 4], [25, 27, 53, 4, "default"], [25, 34, 53, 4], [25, 42, 53, 4, "AnimatedValueXY"], [25, 57, 53, 4], [26, 6, 54, 4, "_this"], [26, 11, 54, 4], [26, 14, 54, 4, "_callSuper"], [26, 24, 54, 4], [26, 31, 54, 4, "AnimatedValueXY"], [26, 46, 54, 4], [26, 49, 54, 10, "config"], [26, 55, 54, 16], [27, 6, 55, 4], [27, 10, 55, 10, "value"], [27, 15, 55, 20], [27, 18, 55, 23, "valueIn"], [27, 25, 55, 30], [27, 29, 55, 34], [28, 8, 55, 35, "x"], [28, 9, 55, 36], [28, 11, 55, 38], [28, 12, 55, 39], [29, 8, 55, 41, "y"], [29, 9, 55, 42], [29, 11, 55, 44], [30, 6, 55, 45], [30, 7, 55, 46], [31, 6, 56, 4], [31, 10, 56, 8], [31, 17, 56, 15, "value"], [31, 22, 56, 20], [31, 23, 56, 21, "x"], [31, 24, 56, 22], [31, 29, 56, 27], [31, 37, 56, 35], [31, 41, 56, 39], [31, 48, 56, 46, "value"], [31, 53, 56, 51], [31, 54, 56, 52, "y"], [31, 55, 56, 53], [31, 60, 56, 58], [31, 68, 56, 66], [31, 70, 56, 68], [32, 8, 57, 6, "_this"], [32, 13, 57, 6], [32, 14, 57, 11, "x"], [32, 15, 57, 12], [32, 18, 57, 15], [32, 22, 57, 19, "AnimatedValue"], [32, 44, 57, 32], [32, 45, 57, 33, "value"], [32, 50, 57, 38], [32, 51, 57, 39, "x"], [32, 52, 57, 40], [32, 53, 57, 41], [33, 8, 58, 6, "_this"], [33, 13, 58, 6], [33, 14, 58, 11, "y"], [33, 15, 58, 12], [33, 18, 58, 15], [33, 22, 58, 19, "AnimatedValue"], [33, 44, 58, 32], [33, 45, 58, 33, "value"], [33, 50, 58, 38], [33, 51, 58, 39, "y"], [33, 52, 58, 40], [33, 53, 58, 41], [34, 6, 59, 4], [34, 7, 59, 5], [34, 13, 59, 11], [35, 8, 60, 6], [35, 12, 60, 6, "invariant"], [35, 30, 60, 15], [35, 32, 61, 8, "value"], [35, 37, 61, 13], [35, 38, 61, 14, "x"], [35, 39, 61, 15], [35, 51, 61, 27, "AnimatedValue"], [35, 73, 61, 40], [35, 77, 61, 44, "value"], [35, 82, 61, 49], [35, 83, 61, 50, "y"], [35, 84, 61, 51], [35, 96, 61, 63, "AnimatedValue"], [35, 118, 61, 76], [35, 120, 62, 8], [35, 187, 62, 75], [35, 190, 63, 10], [35, 207, 64, 6], [35, 208, 64, 7], [36, 8, 65, 6, "_this"], [36, 13, 65, 6], [36, 14, 65, 11, "x"], [36, 15, 65, 12], [36, 18, 65, 15, "value"], [36, 23, 65, 20], [36, 24, 65, 21, "x"], [36, 25, 65, 22], [37, 8, 66, 6, "_this"], [37, 13, 66, 6], [37, 14, 66, 11, "y"], [37, 15, 66, 12], [37, 18, 66, 15, "value"], [37, 23, 66, 20], [37, 24, 66, 21, "y"], [37, 25, 66, 22], [38, 6, 67, 4], [39, 6, 68, 4, "_this"], [39, 11, 68, 4], [39, 12, 68, 9, "_listeners"], [39, 22, 68, 19], [39, 25, 68, 22], [39, 26, 68, 23], [39, 27, 68, 24], [40, 6, 69, 4], [40, 10, 69, 8, "config"], [40, 16, 69, 14], [40, 20, 69, 18, "config"], [40, 26, 69, 24], [40, 27, 69, 25, "useNativeDriver"], [40, 42, 69, 40], [40, 44, 69, 42], [41, 8, 70, 6, "_this"], [41, 13, 70, 6], [41, 14, 70, 11, "__makeNative"], [41, 26, 70, 23], [41, 27, 70, 24], [41, 28, 70, 25], [42, 6, 71, 4], [43, 6, 71, 5], [43, 13, 71, 5, "_this"], [43, 18, 71, 5], [44, 4, 72, 2], [45, 4, 72, 3], [45, 8, 72, 3, "_inherits2"], [45, 18, 72, 3], [45, 19, 72, 3, "default"], [45, 26, 72, 3], [45, 28, 72, 3, "AnimatedValueXY"], [45, 43, 72, 3], [45, 45, 72, 3, "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>"], [45, 66, 72, 3], [46, 4, 72, 3], [46, 15, 72, 3, "_createClass2"], [46, 28, 72, 3], [46, 29, 72, 3, "default"], [46, 36, 72, 3], [46, 38, 72, 3, "AnimatedValueXY"], [46, 53, 72, 3], [47, 6, 72, 3, "key"], [47, 9, 72, 3], [48, 6, 72, 3, "value"], [48, 11, 72, 3], [48, 13, 80, 2], [48, 22, 80, 2, "setValue"], [48, 30, 80, 10, "setValue"], [48, 31, 80, 11, "value"], [48, 36, 80, 45], [48, 38, 80, 47], [49, 8, 81, 4], [49, 12, 81, 8], [49, 13, 81, 9, "x"], [49, 14, 81, 10], [49, 15, 81, 11, "setValue"], [49, 23, 81, 19], [49, 24, 81, 20, "value"], [49, 29, 81, 25], [49, 30, 81, 26, "x"], [49, 31, 81, 27], [49, 32, 81, 28], [50, 8, 82, 4], [50, 12, 82, 8], [50, 13, 82, 9, "y"], [50, 14, 82, 10], [50, 15, 82, 11, "setValue"], [50, 23, 82, 19], [50, 24, 82, 20, "value"], [50, 29, 82, 25], [50, 30, 82, 26, "y"], [50, 31, 82, 27], [50, 32, 82, 28], [51, 6, 83, 2], [52, 4, 83, 3], [53, 6, 83, 3, "key"], [53, 9, 83, 3], [54, 6, 83, 3, "value"], [54, 11, 83, 3], [54, 13, 92, 2], [54, 22, 92, 2, "setOffset"], [54, 31, 92, 11, "setOffset"], [54, 32, 92, 12, "offset"], [54, 38, 92, 47], [54, 40, 92, 49], [55, 8, 93, 4], [55, 12, 93, 8], [55, 13, 93, 9, "x"], [55, 14, 93, 10], [55, 15, 93, 11, "setOffset"], [55, 24, 93, 20], [55, 25, 93, 21, "offset"], [55, 31, 93, 27], [55, 32, 93, 28, "x"], [55, 33, 93, 29], [55, 34, 93, 30], [56, 8, 94, 4], [56, 12, 94, 8], [56, 13, 94, 9, "y"], [56, 14, 94, 10], [56, 15, 94, 11, "setOffset"], [56, 24, 94, 20], [56, 25, 94, 21, "offset"], [56, 31, 94, 27], [56, 32, 94, 28, "y"], [56, 33, 94, 29], [56, 34, 94, 30], [57, 6, 95, 2], [58, 4, 95, 3], [59, 6, 95, 3, "key"], [59, 9, 95, 3], [60, 6, 95, 3, "value"], [60, 11, 95, 3], [60, 13, 103, 2], [60, 22, 103, 2, "flattenOffset"], [60, 35, 103, 15, "flattenOffset"], [60, 36, 103, 15], [60, 38, 103, 24], [61, 8, 104, 4], [61, 12, 104, 8], [61, 13, 104, 9, "x"], [61, 14, 104, 10], [61, 15, 104, 11, "flattenOffset"], [61, 28, 104, 24], [61, 29, 104, 25], [61, 30, 104, 26], [62, 8, 105, 4], [62, 12, 105, 8], [62, 13, 105, 9, "y"], [62, 14, 105, 10], [62, 15, 105, 11, "flattenOffset"], [62, 28, 105, 24], [62, 29, 105, 25], [62, 30, 105, 26], [63, 6, 106, 2], [64, 4, 106, 3], [65, 6, 106, 3, "key"], [65, 9, 106, 3], [66, 6, 106, 3, "value"], [66, 11, 106, 3], [66, 13, 114, 2], [66, 22, 114, 2, "extractOffset"], [66, 35, 114, 15, "extractOffset"], [66, 36, 114, 15], [66, 38, 114, 24], [67, 8, 115, 4], [67, 12, 115, 8], [67, 13, 115, 9, "x"], [67, 14, 115, 10], [67, 15, 115, 11, "extractOffset"], [67, 28, 115, 24], [67, 29, 115, 25], [67, 30, 115, 26], [68, 8, 116, 4], [68, 12, 116, 8], [68, 13, 116, 9, "y"], [68, 14, 116, 10], [68, 15, 116, 11, "extractOffset"], [68, 28, 116, 24], [68, 29, 116, 25], [68, 30, 116, 26], [69, 6, 117, 2], [70, 4, 117, 3], [71, 6, 117, 3, "key"], [71, 9, 117, 3], [72, 6, 117, 3, "value"], [72, 11, 117, 3], [72, 13, 119, 2], [72, 22, 119, 2, "__getValue"], [72, 32, 119, 12, "__getValue"], [72, 33, 119, 12], [72, 35, 123, 4], [73, 8, 124, 4], [73, 15, 124, 11], [74, 10, 125, 6, "x"], [74, 11, 125, 7], [74, 13, 125, 9], [74, 17, 125, 13], [74, 18, 125, 14, "x"], [74, 19, 125, 15], [74, 20, 125, 16, "__getValue"], [74, 30, 125, 26], [74, 31, 125, 27], [74, 32, 125, 28], [75, 10, 126, 6, "y"], [75, 11, 126, 7], [75, 13, 126, 9], [75, 17, 126, 13], [75, 18, 126, 14, "y"], [75, 19, 126, 15], [75, 20, 126, 16, "__getValue"], [75, 30, 126, 26], [75, 31, 126, 27], [76, 8, 127, 4], [76, 9, 127, 5], [77, 6, 128, 2], [78, 4, 128, 3], [79, 6, 128, 3, "key"], [79, 9, 128, 3], [80, 6, 128, 3, "value"], [80, 11, 128, 3], [80, 13, 135, 2], [80, 22, 135, 2, "resetAnimation"], [80, 36, 135, 16, "resetAnimation"], [80, 37, 136, 4, "callback"], [80, 45, 136, 59], [80, 47, 137, 10], [81, 8, 138, 4], [81, 12, 138, 8], [81, 13, 138, 9, "x"], [81, 14, 138, 10], [81, 15, 138, 11, "resetAnimation"], [81, 29, 138, 25], [81, 30, 138, 26], [81, 31, 138, 27], [82, 8, 139, 4], [82, 12, 139, 8], [82, 13, 139, 9, "y"], [82, 14, 139, 10], [82, 15, 139, 11, "resetAnimation"], [82, 29, 139, 25], [82, 30, 139, 26], [82, 31, 139, 27], [83, 8, 140, 4, "callback"], [83, 16, 140, 12], [83, 20, 140, 16, "callback"], [83, 28, 140, 24], [83, 29, 140, 25], [83, 33, 140, 29], [83, 34, 140, 30, "__getValue"], [83, 44, 140, 40], [83, 45, 140, 41], [83, 46, 140, 42], [83, 47, 140, 43], [84, 6, 141, 2], [85, 4, 141, 3], [86, 6, 141, 3, "key"], [86, 9, 141, 3], [87, 6, 141, 3, "value"], [87, 11, 141, 3], [87, 13, 150, 2], [87, 22, 150, 2, "stopAnimation"], [87, 35, 150, 15, "stopAnimation"], [87, 36, 150, 16, "callback"], [87, 44, 150, 71], [87, 46, 150, 79], [88, 8, 151, 4], [88, 12, 151, 8], [88, 13, 151, 9, "x"], [88, 14, 151, 10], [88, 15, 151, 11, "stopAnimation"], [88, 28, 151, 24], [88, 29, 151, 25], [88, 30, 151, 26], [89, 8, 152, 4], [89, 12, 152, 8], [89, 13, 152, 9, "y"], [89, 14, 152, 10], [89, 15, 152, 11, "stopAnimation"], [89, 28, 152, 24], [89, 29, 152, 25], [89, 30, 152, 26], [90, 8, 153, 4, "callback"], [90, 16, 153, 12], [90, 20, 153, 16, "callback"], [90, 28, 153, 24], [90, 29, 153, 25], [90, 33, 153, 29], [90, 34, 153, 30, "__getValue"], [90, 44, 153, 40], [90, 45, 153, 41], [90, 46, 153, 42], [90, 47, 153, 43], [91, 6, 154, 2], [92, 4, 154, 3], [93, 6, 154, 3, "key"], [93, 9, 154, 3], [94, 6, 154, 3, "value"], [94, 11, 154, 3], [94, 13, 165, 2], [94, 22, 165, 2, "addListener"], [94, 33, 165, 13, "addListener"], [94, 34, 165, 14, "callback"], [94, 42, 165, 47], [94, 44, 165, 57], [95, 8, 166, 4], [95, 12, 166, 10, "id"], [95, 14, 166, 12], [95, 17, 166, 15, "String"], [95, 23, 166, 21], [95, 24, 166, 22, "_uniqueId"], [95, 33, 166, 31], [95, 35, 166, 33], [95, 36, 166, 34], [96, 8, 167, 4], [96, 12, 167, 10, "jointCallback"], [96, 25, 167, 23], [96, 28, 167, 26, "_ref"], [96, 32, 167, 26], [96, 36, 167, 52], [97, 10, 167, 52], [97, 14, 167, 35, "number"], [97, 20, 167, 41], [97, 23, 167, 41, "_ref"], [97, 27, 167, 41], [97, 28, 167, 28, "value"], [97, 33, 167, 33], [98, 10, 168, 6, "callback"], [98, 18, 168, 14], [98, 19, 168, 15], [98, 23, 168, 19], [98, 24, 168, 20, "__getValue"], [98, 34, 168, 30], [98, 35, 168, 31], [98, 36, 168, 32], [98, 37, 168, 33], [99, 8, 169, 4], [99, 9, 169, 5], [100, 8, 170, 4], [100, 12, 170, 8], [100, 13, 170, 9, "_listeners"], [100, 23, 170, 19], [100, 24, 170, 20, "id"], [100, 26, 170, 22], [100, 27, 170, 23], [100, 30, 170, 26], [101, 10, 171, 6, "x"], [101, 11, 171, 7], [101, 13, 171, 9], [101, 17, 171, 13], [101, 18, 171, 14, "x"], [101, 19, 171, 15], [101, 20, 171, 16, "addListener"], [101, 31, 171, 27], [101, 32, 171, 28, "jointCallback"], [101, 45, 171, 41], [101, 46, 171, 42], [102, 10, 172, 6, "y"], [102, 11, 172, 7], [102, 13, 172, 9], [102, 17, 172, 13], [102, 18, 172, 14, "y"], [102, 19, 172, 15], [102, 20, 172, 16, "addListener"], [102, 31, 172, 27], [102, 32, 172, 28, "jointCallback"], [102, 45, 172, 41], [103, 8, 173, 4], [103, 9, 173, 5], [104, 8, 174, 4], [104, 15, 174, 11, "id"], [104, 17, 174, 13], [105, 6, 175, 2], [106, 4, 175, 3], [107, 6, 175, 3, "key"], [107, 9, 175, 3], [108, 6, 175, 3, "value"], [108, 11, 175, 3], [108, 13, 183, 2], [108, 22, 183, 2, "removeListener"], [108, 36, 183, 16, "removeListener"], [108, 37, 183, 17, "id"], [108, 39, 183, 27], [108, 41, 183, 35], [109, 8, 184, 4], [109, 12, 184, 8], [109, 13, 184, 9, "x"], [109, 14, 184, 10], [109, 15, 184, 11, "removeListener"], [109, 29, 184, 25], [109, 30, 184, 26], [109, 34, 184, 30], [109, 35, 184, 31, "_listeners"], [109, 45, 184, 41], [109, 46, 184, 42, "id"], [109, 48, 184, 44], [109, 49, 184, 45], [109, 50, 184, 46, "x"], [109, 51, 184, 47], [109, 52, 184, 48], [110, 8, 185, 4], [110, 12, 185, 8], [110, 13, 185, 9, "y"], [110, 14, 185, 10], [110, 15, 185, 11, "removeListener"], [110, 29, 185, 25], [110, 30, 185, 26], [110, 34, 185, 30], [110, 35, 185, 31, "_listeners"], [110, 45, 185, 41], [110, 46, 185, 42, "id"], [110, 48, 185, 44], [110, 49, 185, 45], [110, 50, 185, 46, "y"], [110, 51, 185, 47], [110, 52, 185, 48], [111, 8, 186, 4], [111, 15, 186, 11], [111, 19, 186, 15], [111, 20, 186, 16, "_listeners"], [111, 30, 186, 26], [111, 31, 186, 27, "id"], [111, 33, 186, 29], [111, 34, 186, 30], [112, 6, 187, 2], [113, 4, 187, 3], [114, 6, 187, 3, "key"], [114, 9, 187, 3], [115, 6, 187, 3, "value"], [115, 11, 187, 3], [115, 13, 194, 2], [115, 22, 194, 2, "removeAllListeners"], [115, 40, 194, 20, "removeAllListeners"], [115, 41, 194, 20], [115, 43, 194, 29], [116, 8, 195, 4], [116, 12, 195, 8], [116, 13, 195, 9, "x"], [116, 14, 195, 10], [116, 15, 195, 11, "removeAllListeners"], [116, 33, 195, 29], [116, 34, 195, 30], [116, 35, 195, 31], [117, 8, 196, 4], [117, 12, 196, 8], [117, 13, 196, 9, "y"], [117, 14, 196, 10], [117, 15, 196, 11, "removeAllListeners"], [117, 33, 196, 29], [117, 34, 196, 30], [117, 35, 196, 31], [118, 8, 197, 4], [118, 12, 197, 8], [118, 13, 197, 9, "_listeners"], [118, 23, 197, 19], [118, 26, 197, 22], [118, 27, 197, 23], [118, 28, 197, 24], [119, 6, 198, 2], [120, 4, 198, 3], [121, 6, 198, 3, "key"], [121, 9, 198, 3], [122, 6, 198, 3, "value"], [122, 11, 198, 3], [122, 13, 205, 2], [122, 22, 205, 2, "getLayout"], [122, 31, 205, 11, "getLayout"], [122, 32, 205, 11], [122, 34, 205, 51], [123, 8, 206, 4], [123, 15, 206, 11], [124, 10, 207, 6, "left"], [124, 14, 207, 10], [124, 16, 207, 12], [124, 20, 207, 16], [124, 21, 207, 17, "x"], [124, 22, 207, 18], [125, 10, 208, 6, "top"], [125, 13, 208, 9], [125, 15, 208, 11], [125, 19, 208, 15], [125, 20, 208, 16, "y"], [126, 8, 209, 4], [126, 9, 209, 5], [127, 6, 210, 2], [128, 4, 210, 3], [129, 6, 210, 3, "key"], [129, 9, 210, 3], [130, 6, 210, 3, "value"], [130, 11, 210, 3], [130, 13, 217, 2], [130, 22, 217, 2, "getTranslateTransform"], [130, 43, 217, 23, "getTranslateTransform"], [130, 44, 217, 23], [130, 46, 217, 70], [131, 8, 218, 4], [131, 15, 218, 11], [131, 16, 218, 12], [132, 10, 218, 13, "translateX"], [132, 20, 218, 23], [132, 22, 218, 25], [132, 26, 218, 29], [132, 27, 218, 30, "x"], [133, 8, 218, 31], [133, 9, 218, 32], [133, 11, 218, 34], [134, 10, 218, 35, "translateY"], [134, 20, 218, 45], [134, 22, 218, 47], [134, 26, 218, 51], [134, 27, 218, 52, "y"], [135, 8, 218, 53], [135, 9, 218, 54], [135, 10, 218, 55], [136, 6, 219, 2], [137, 4, 219, 3], [138, 6, 219, 3, "key"], [138, 9, 219, 3], [139, 6, 219, 3, "value"], [139, 11, 219, 3], [139, 13, 221, 2], [139, 22, 221, 2, "__attach"], [139, 30, 221, 10, "__attach"], [139, 31, 221, 10], [139, 33, 221, 19], [140, 8, 222, 4], [140, 12, 222, 8], [140, 13, 222, 9, "x"], [140, 14, 222, 10], [140, 15, 222, 11, "__add<PERSON><PERSON>d"], [140, 25, 222, 21], [140, 26, 222, 22], [140, 30, 222, 26], [140, 31, 222, 27], [141, 8, 223, 4], [141, 12, 223, 8], [141, 13, 223, 9, "y"], [141, 14, 223, 10], [141, 15, 223, 11, "__add<PERSON><PERSON>d"], [141, 25, 223, 21], [141, 26, 223, 22], [141, 30, 223, 26], [141, 31, 223, 27], [142, 8, 224, 4, "_superPropGet"], [142, 21, 224, 4], [142, 22, 224, 4, "AnimatedValueXY"], [142, 37, 224, 4], [143, 6, 225, 2], [144, 4, 225, 3], [145, 6, 225, 3, "key"], [145, 9, 225, 3], [146, 6, 225, 3, "value"], [146, 11, 225, 3], [146, 13, 227, 2], [146, 22, 227, 2, "__detach"], [146, 30, 227, 10, "__detach"], [146, 31, 227, 10], [146, 33, 227, 19], [147, 8, 228, 4], [147, 12, 228, 8], [147, 13, 228, 9, "x"], [147, 14, 228, 10], [147, 15, 228, 11, "__remove<PERSON><PERSON>d"], [147, 28, 228, 24], [147, 29, 228, 25], [147, 33, 228, 29], [147, 34, 228, 30], [148, 8, 229, 4], [148, 12, 229, 8], [148, 13, 229, 9, "y"], [148, 14, 229, 10], [148, 15, 229, 11, "__remove<PERSON><PERSON>d"], [148, 28, 229, 24], [148, 29, 229, 25], [148, 33, 229, 29], [148, 34, 229, 30], [149, 8, 230, 4, "_superPropGet"], [149, 21, 230, 4], [149, 22, 230, 4, "AnimatedValueXY"], [149, 37, 230, 4], [150, 6, 231, 2], [151, 4, 231, 3], [152, 6, 231, 3, "key"], [152, 9, 231, 3], [153, 6, 231, 3, "value"], [153, 11, 231, 3], [153, 13, 233, 2], [153, 22, 233, 2, "__makeNative"], [153, 34, 233, 14, "__makeNative"], [153, 35, 233, 15, "platformConfig"], [153, 49, 233, 46], [153, 51, 233, 48], [154, 8, 234, 4], [154, 12, 234, 8], [154, 13, 234, 9, "x"], [154, 14, 234, 10], [154, 15, 234, 11, "__makeNative"], [154, 27, 234, 23], [154, 28, 234, 24, "platformConfig"], [154, 42, 234, 38], [154, 43, 234, 39], [155, 8, 235, 4], [155, 12, 235, 8], [155, 13, 235, 9, "y"], [155, 14, 235, 10], [155, 15, 235, 11, "__makeNative"], [155, 27, 235, 23], [155, 28, 235, 24, "platformConfig"], [155, 42, 235, 38], [155, 43, 235, 39], [156, 8, 236, 4, "_superPropGet"], [156, 21, 236, 4], [156, 22, 236, 4, "AnimatedValueXY"], [156, 37, 236, 4], [156, 65, 236, 23, "platformConfig"], [156, 79, 236, 37], [157, 6, 237, 2], [158, 4, 237, 3], [159, 2, 237, 3], [159, 4, 34, 45, "AnimatedWithChildren"], [159, 34, 34, 65], [160, 0, 34, 65], [160, 3]], "functionMap": {"names": ["<global>", "AnimatedValueXY", "constructor", "setValue", "setOffset", "flattenOffset", "extractOffset", "__getValue", "resetAnimation", "stopAnimation", "addListener", "jointCallback", "removeListener", "removeAllListeners", "getLayout", "getTranslateTransform", "__attach", "__detach", "__makeNative"], "mappings": "AAA;eCiC;ECY;GD0B;EEQ;GFG;EGS;GHG;EIQ;GJG;EKQ;GLG;EME;GNS;EOO;GPM;EQS;GRI;ESW;0BCE;KDE;GTM;EWQ;GXI;EYO;GZI;EaO;GbK;EcO;GdE;EeE;GfI;EgBE;GhBI;EiBE;GjBI"}}, "type": "js/module"}]}