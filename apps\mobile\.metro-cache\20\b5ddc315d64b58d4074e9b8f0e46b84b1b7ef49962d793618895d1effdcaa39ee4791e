{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  function _isNativeReflectConstruct() {\n    try {\n      var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n    } catch (t) {}\n    return (module.exports = _isNativeReflectConstruct = function _isNativeReflectConstruct() {\n      return !!t;\n    }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n  }\n  module.exports = _isNativeReflectConstruct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 11, "map": [[2, 2, 1, 0], [2, 11, 1, 9, "_isNativeReflectConstruct"], [2, 36, 1, 34, "_isNativeReflectConstruct"], [2, 37, 1, 34], [2, 39, 1, 37], [3, 4, 2, 2], [3, 8, 2, 6], [4, 6, 3, 4], [4, 10, 3, 8, "t"], [4, 11, 3, 9], [4, 14, 3, 12], [4, 15, 3, 13, "Boolean"], [4, 22, 3, 20], [4, 23, 3, 21, "prototype"], [4, 32, 3, 30], [4, 33, 3, 31, "valueOf"], [4, 40, 3, 38], [4, 41, 3, 39, "call"], [4, 45, 3, 43], [4, 46, 3, 44, "Reflect"], [4, 53, 3, 51], [4, 54, 3, 52, "construct"], [4, 63, 3, 61], [4, 64, 3, 62, "Boolean"], [4, 71, 3, 69], [4, 73, 3, 71], [4, 75, 3, 73], [4, 77, 3, 75], [4, 89, 3, 87], [4, 90, 3, 88], [4, 91, 3, 89], [4, 92, 3, 90], [4, 93, 3, 91], [5, 4, 4, 2], [5, 5, 4, 3], [5, 6, 4, 4], [5, 13, 4, 11, "t"], [5, 14, 4, 12], [5, 16, 4, 14], [5, 17, 4, 15], [6, 4, 5, 2], [6, 11, 5, 9], [6, 12, 5, 10, "module"], [6, 18, 5, 16], [6, 19, 5, 17, "exports"], [6, 26, 5, 24], [6, 29, 5, 27, "_isNativeReflectConstruct"], [6, 54, 5, 52], [6, 57, 5, 55], [6, 66, 5, 64, "_isNativeReflectConstruct"], [6, 91, 5, 89, "_isNativeReflectConstruct"], [6, 92, 5, 89], [6, 94, 5, 92], [7, 6, 6, 4], [7, 13, 6, 11], [7, 14, 6, 12], [7, 15, 6, 13, "t"], [7, 16, 6, 14], [8, 4, 7, 2], [8, 5, 7, 3], [8, 7, 7, 5, "module"], [8, 13, 7, 11], [8, 14, 7, 12, "exports"], [8, 21, 7, 19], [8, 22, 7, 20, "__esModule"], [8, 32, 7, 30], [8, 35, 7, 33], [8, 39, 7, 37], [8, 41, 7, 39, "module"], [8, 47, 7, 45], [8, 48, 7, 46, "exports"], [8, 55, 7, 53], [8, 56, 7, 54], [8, 65, 7, 63], [8, 66, 7, 64], [8, 69, 7, 67, "module"], [8, 75, 7, 73], [8, 76, 7, 74, "exports"], [8, 83, 7, 81], [8, 85, 7, 83], [8, 86, 7, 84], [9, 2, 8, 0], [10, 2, 9, 0, "module"], [10, 8, 9, 6], [10, 9, 9, 7, "exports"], [10, 16, 9, 14], [10, 19, 9, 17, "_isNativeReflectConstruct"], [10, 44, 9, 42], [10, 46, 9, 44, "module"], [10, 52, 9, 50], [10, 53, 9, 51, "exports"], [10, 60, 9, 58], [10, 61, 9, 59, "__esModule"], [10, 71, 9, 69], [10, 74, 9, 72], [10, 78, 9, 76], [10, 80, 9, 78, "module"], [10, 86, 9, 84], [10, 87, 9, 85, "exports"], [10, 94, 9, 92], [10, 95, 9, 93], [10, 104, 9, 102], [10, 105, 9, 103], [10, 108, 9, 106, "module"], [10, 114, 9, 112], [10, 115, 9, 113, "exports"], [10, 122, 9, 120], [11, 0, 9, 121], [11, 3]], "functionMap": {"names": ["_isNativeReflectConstruct", "Reflect.construct$argument_2", "<global>"], "mappings": "AAA;2ECE,cD;CEK"}}, "type": "js/module"}]}