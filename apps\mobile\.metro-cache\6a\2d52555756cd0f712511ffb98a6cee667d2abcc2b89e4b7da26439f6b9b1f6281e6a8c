{"dependencies": [{"name": "./ExceptionsManager", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 28}, "end": {"line": 18, "column": 58}}], "key": "73HzAkjU5Cscn5BS81m8tPC03ag=", "exportNames": ["*"]}}, {"name": "../vendor/core/ErrorUtils", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 32, "column": 23}, "end": {"line": 32, "column": 59}}], "key": "TcgsBidg33sQ2IYaB//RN7g4/7c=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  if (global.RN$useAlwaysAvailableJSErrorHandling !== true) {\n    var ExceptionsManager = require(_dependencyMap[0], \"./ExceptionsManager\").default;\n    ExceptionsManager.installConsoleErrorReporter();\n    if (!global.__fbDisableExceptionsManager) {\n      var handleError = (e, isFatal) => {\n        try {\n          ExceptionsManager.handleException(e, isFatal);\n        } catch (ee) {\n          console.log('Failed to print error: ', ee.message);\n          throw e;\n        }\n      };\n      var ErrorUtils = require(_dependencyMap[1], \"../vendor/core/ErrorUtils\").default;\n      ErrorUtils.setGlobalHandler(handleError);\n    }\n  }\n});", "lineCount": 20, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 13, 0], [4, 6, 13, 4, "global"], [4, 12, 13, 10], [4, 13, 13, 11, "RN$useAlwaysAvailableJSErrorHandling"], [4, 49, 13, 47], [4, 54, 13, 52], [4, 58, 13, 56], [4, 60, 13, 58], [5, 4, 18, 2], [5, 8, 18, 8, "ExceptionsManager"], [5, 25, 18, 25], [5, 28, 18, 28, "require"], [5, 35, 18, 35], [5, 36, 18, 35, "_dependencyMap"], [5, 50, 18, 35], [5, 76, 18, 57], [5, 77, 18, 58], [5, 78, 18, 59, "default"], [5, 85, 18, 66], [6, 4, 19, 2, "ExceptionsManager"], [6, 21, 19, 19], [6, 22, 19, 20, "installConsoleErrorReporter"], [6, 49, 19, 47], [6, 50, 19, 48], [6, 51, 19, 49], [7, 4, 22, 2], [7, 8, 22, 6], [7, 9, 22, 7, "global"], [7, 15, 22, 13], [7, 16, 22, 14, "__fbDisableExceptionsManager"], [7, 44, 22, 42], [7, 46, 22, 44], [8, 6, 23, 4], [8, 10, 23, 10, "handleError"], [8, 21, 23, 21], [8, 24, 23, 24, "handleError"], [8, 25, 23, 25, "e"], [8, 26, 23, 33], [8, 28, 23, 35, "isFatal"], [8, 35, 23, 51], [8, 40, 23, 56], [9, 8, 24, 6], [9, 12, 24, 10], [10, 10, 25, 8, "ExceptionsManager"], [10, 27, 25, 25], [10, 28, 25, 26, "handleException"], [10, 43, 25, 41], [10, 44, 25, 42, "e"], [10, 45, 25, 43], [10, 47, 25, 45, "isFatal"], [10, 54, 25, 52], [10, 55, 25, 53], [11, 8, 26, 6], [11, 9, 26, 7], [11, 10, 26, 8], [11, 17, 26, 15, "ee"], [11, 19, 26, 17], [11, 21, 26, 19], [12, 10, 27, 8, "console"], [12, 17, 27, 15], [12, 18, 27, 16, "log"], [12, 21, 27, 19], [12, 22, 27, 20], [12, 47, 27, 45], [12, 49, 27, 47, "ee"], [12, 51, 27, 49], [12, 52, 27, 50, "message"], [12, 59, 27, 57], [12, 60, 27, 58], [13, 10, 28, 8], [13, 16, 28, 14, "e"], [13, 17, 28, 15], [14, 8, 29, 6], [15, 6, 30, 4], [15, 7, 30, 5], [16, 6, 32, 4], [16, 10, 32, 10, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 20, 32, 20], [16, 23, 32, 23, "require"], [16, 30, 32, 30], [16, 31, 32, 30, "_dependencyMap"], [16, 45, 32, 30], [16, 77, 32, 58], [16, 78, 32, 59], [16, 79, 32, 60, "default"], [16, 86, 32, 67], [17, 6, 33, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [17, 16, 33, 14], [17, 17, 33, 15, "setGlobalHandler"], [17, 33, 33, 31], [17, 34, 33, 32, "handleError"], [17, 45, 33, 43], [17, 46, 33, 44], [18, 4, 34, 2], [19, 2, 35, 0], [20, 0, 35, 1], [20, 3]], "functionMap": {"names": ["<global>", "handleError"], "mappings": "AAA;wBCsB;KDO"}}, "type": "js/module"}]}