{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = createIconSourceCache;\n  var TYPE_VALUE = 'value';\n  var TYPE_ERROR = 'error';\n  function createIconSourceCache() {\n    var cache = new Map();\n    var setValue = (key, value) => cache.set(key, {\n      type: TYPE_VALUE,\n      data: value\n    });\n    var setError = (key, error) => cache.set(key, {\n      type: TYPE_ERROR,\n      data: error\n    });\n    var has = key => cache.has(key);\n    var get = key => {\n      if (!cache.has(key)) {\n        return undefined;\n      }\n      var _cache$get = cache.get(key),\n        type = _cache$get.type,\n        data = _cache$get.data;\n      if (type === TYPE_ERROR) {\n        throw data;\n      }\n      return data;\n    };\n    return {\n      setValue,\n      setError,\n      has,\n      get\n    };\n  }\n});", "lineCount": 38, "map": [[6, 2, 1, 0], [6, 6, 1, 6, "TYPE_VALUE"], [6, 16, 1, 16], [6, 19, 1, 19], [6, 26, 1, 26], [7, 2, 2, 0], [7, 6, 2, 6, "TYPE_ERROR"], [7, 16, 2, 16], [7, 19, 2, 19], [7, 26, 2, 26], [8, 2, 4, 15], [8, 11, 4, 24, "createIconSourceCache"], [8, 32, 4, 45, "createIconSourceCache"], [8, 33, 4, 45], [8, 35, 4, 48], [9, 4, 5, 2], [9, 8, 5, 8, "cache"], [9, 13, 5, 13], [9, 16, 5, 16], [9, 20, 5, 20, "Map"], [9, 23, 5, 23], [9, 24, 5, 24], [9, 25, 5, 25], [10, 4, 7, 2], [10, 8, 7, 8, "setValue"], [10, 16, 7, 16], [10, 19, 7, 19, "setValue"], [10, 20, 7, 20, "key"], [10, 23, 7, 23], [10, 25, 7, 25, "value"], [10, 30, 7, 30], [10, 35, 8, 4, "cache"], [10, 40, 8, 9], [10, 41, 8, 10, "set"], [10, 44, 8, 13], [10, 45, 8, 14, "key"], [10, 48, 8, 17], [10, 50, 8, 19], [11, 6, 8, 21, "type"], [11, 10, 8, 25], [11, 12, 8, 27, "TYPE_VALUE"], [11, 22, 8, 37], [12, 6, 8, 39, "data"], [12, 10, 8, 43], [12, 12, 8, 45, "value"], [13, 4, 8, 51], [13, 5, 8, 52], [13, 6, 8, 53], [14, 4, 10, 2], [14, 8, 10, 8, "setError"], [14, 16, 10, 16], [14, 19, 10, 19, "setError"], [14, 20, 10, 20, "key"], [14, 23, 10, 23], [14, 25, 10, 25, "error"], [14, 30, 10, 30], [14, 35, 11, 4, "cache"], [14, 40, 11, 9], [14, 41, 11, 10, "set"], [14, 44, 11, 13], [14, 45, 11, 14, "key"], [14, 48, 11, 17], [14, 50, 11, 19], [15, 6, 11, 21, "type"], [15, 10, 11, 25], [15, 12, 11, 27, "TYPE_ERROR"], [15, 22, 11, 37], [16, 6, 11, 39, "data"], [16, 10, 11, 43], [16, 12, 11, 45, "error"], [17, 4, 11, 51], [17, 5, 11, 52], [17, 6, 11, 53], [18, 4, 13, 2], [18, 8, 13, 8, "has"], [18, 11, 13, 11], [18, 14, 13, 14, "key"], [18, 17, 13, 17], [18, 21, 13, 21, "cache"], [18, 26, 13, 26], [18, 27, 13, 27, "has"], [18, 30, 13, 30], [18, 31, 13, 31, "key"], [18, 34, 13, 34], [18, 35, 13, 35], [19, 4, 15, 2], [19, 8, 15, 8, "get"], [19, 11, 15, 11], [19, 14, 15, 14, "key"], [19, 17, 15, 17], [19, 21, 15, 21], [20, 6, 16, 4], [20, 10, 16, 8], [20, 11, 16, 9, "cache"], [20, 16, 16, 14], [20, 17, 16, 15, "has"], [20, 20, 16, 18], [20, 21, 16, 19, "key"], [20, 24, 16, 22], [20, 25, 16, 23], [20, 27, 16, 25], [21, 8, 17, 6], [21, 15, 17, 13, "undefined"], [21, 24, 17, 22], [22, 6, 18, 4], [23, 6, 19, 4], [23, 10, 19, 4, "_cache$get"], [23, 20, 19, 4], [23, 23, 19, 27, "cache"], [23, 28, 19, 32], [23, 29, 19, 33, "get"], [23, 32, 19, 36], [23, 33, 19, 37, "key"], [23, 36, 19, 40], [23, 37, 19, 41], [24, 8, 19, 12, "type"], [24, 12, 19, 16], [24, 15, 19, 16, "_cache$get"], [24, 25, 19, 16], [24, 26, 19, 12, "type"], [24, 30, 19, 16], [25, 8, 19, 18, "data"], [25, 12, 19, 22], [25, 15, 19, 22, "_cache$get"], [25, 25, 19, 22], [25, 26, 19, 18, "data"], [25, 30, 19, 22], [26, 6, 20, 4], [26, 10, 20, 8, "type"], [26, 14, 20, 12], [26, 19, 20, 17, "TYPE_ERROR"], [26, 29, 20, 27], [26, 31, 20, 29], [27, 8, 21, 6], [27, 14, 21, 12, "data"], [27, 18, 21, 16], [28, 6, 22, 4], [29, 6, 23, 4], [29, 13, 23, 11, "data"], [29, 17, 23, 15], [30, 4, 24, 2], [30, 5, 24, 3], [31, 4, 26, 2], [31, 11, 26, 9], [32, 6, 26, 11, "setValue"], [32, 14, 26, 19], [33, 6, 26, 21, "setError"], [33, 14, 26, 29], [34, 6, 26, 31, "has"], [34, 9, 26, 34], [35, 6, 26, 36, "get"], [36, 4, 26, 40], [36, 5, 26, 41], [37, 2, 27, 0], [38, 0, 27, 1], [38, 3]], "functionMap": {"names": ["<global>", "createIconSourceCache", "setValue", "setError", "has", "get"], "mappings": "AAA;eCG;mBCG;qDDC;mBEE;qDFC;cGE,qBH;cIE;GJS;CDG"}}, "type": "js/module"}]}