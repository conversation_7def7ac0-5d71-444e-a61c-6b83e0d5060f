{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getEventHandlerAttribute = getEventHandlerAttribute;\n  exports.setEventHandlerAttribute = setEventHandlerAttribute;\n  var EVENT_HANDLER_CONTENT_ATTRIBUTE_MAP_KEY = Symbol('eventHandlerAttributeMap');\n  function getEventHandlerAttributeMap(target) {\n    return target[EVENT_HANDLER_CONTENT_ATTRIBUTE_MAP_KEY];\n  }\n  function setEventHandlerAttributeMap(target, map) {\n    target[EVENT_HANDLER_CONTENT_ATTRIBUTE_MAP_KEY] = map;\n  }\n  function getEventHandlerAttribute(target, type) {\n    var listener = getEventHandlerAttributeMap(target)?.get(type);\n    return listener != null ? listener.handleEvent : null;\n  }\n  function setEventHandlerAttribute(target, type, callback) {\n    var map = getEventHandlerAttributeMap(target);\n    if (map != null) {\n      var currentListener = map.get(type);\n      if (currentListener) {\n        target.removeEventListener(type, currentListener);\n        map.delete(type);\n      }\n    }\n    if (callback != null && (typeof callback === 'function' || typeof callback === 'object')) {\n      var listener = {\n        handleEvent: callback\n      };\n      try {\n        target.addEventListener(type, listener);\n        if (map == null) {\n          map = new Map();\n          setEventHandlerAttributeMap(target, map);\n        }\n        map.set(type, listener);\n      } catch (e) {}\n    }\n    if (map != null && map.size === 0) {\n      setEventHandlerAttributeMap(target, null);\n    }\n  }\n});", "lineCount": 44, "map": [[7, 2, 51, 0], [7, 6, 51, 6, "EVENT_HANDLER_CONTENT_ATTRIBUTE_MAP_KEY"], [7, 45, 51, 45], [7, 48, 51, 48, "Symbol"], [7, 54, 51, 54], [7, 55, 52, 2], [7, 81, 53, 0], [7, 82, 53, 1], [8, 2, 55, 0], [8, 11, 55, 9, "getEventHandlerAttributeMap"], [8, 38, 55, 36, "getEventHandlerAttributeMap"], [8, 39, 56, 2, "target"], [8, 45, 56, 21], [8, 47, 57, 29], [9, 4, 59, 2], [9, 11, 59, 9, "target"], [9, 17, 59, 15], [9, 18, 59, 16, "EVENT_HANDLER_CONTENT_ATTRIBUTE_MAP_KEY"], [9, 57, 59, 55], [9, 58, 59, 56], [10, 2, 60, 0], [11, 2, 62, 0], [11, 11, 62, 9, "setEventHandlerAttributeMap"], [11, 38, 62, 36, "setEventHandlerAttributeMap"], [11, 39, 63, 2, "target"], [11, 45, 63, 21], [11, 47, 64, 2, "map"], [11, 50, 64, 32], [11, 52, 65, 2], [12, 4, 67, 2, "target"], [12, 10, 67, 8], [12, 11, 67, 9, "EVENT_HANDLER_CONTENT_ATTRIBUTE_MAP_KEY"], [12, 50, 67, 48], [12, 51, 67, 49], [12, 54, 67, 52, "map"], [12, 57, 67, 55], [13, 2, 68, 0], [14, 2, 76, 7], [14, 11, 76, 16, "getEventHandlerAttribute"], [14, 35, 76, 40, "getEventHandlerAttribute"], [14, 36, 77, 2, "target"], [14, 42, 77, 21], [14, 44, 78, 2, "type"], [14, 48, 78, 14], [14, 50, 79, 24], [15, 4, 80, 2], [15, 8, 80, 8, "listener"], [15, 16, 80, 16], [15, 19, 80, 19, "getEventHandlerAttributeMap"], [15, 46, 80, 46], [15, 47, 80, 47, "target"], [15, 53, 80, 53], [15, 54, 80, 54], [15, 56, 80, 56, "get"], [15, 59, 80, 59], [15, 60, 80, 60, "type"], [15, 64, 80, 64], [15, 65, 80, 65], [16, 4, 81, 2], [16, 11, 81, 9, "listener"], [16, 19, 81, 17], [16, 23, 81, 21], [16, 27, 81, 25], [16, 30, 81, 28, "listener"], [16, 38, 81, 36], [16, 39, 81, 37, "handleEvent"], [16, 50, 81, 48], [16, 53, 81, 51], [16, 57, 81, 55], [17, 2, 82, 0], [18, 2, 90, 7], [18, 11, 90, 16, "setEventHandlerAttribute"], [18, 35, 90, 40, "setEventHandlerAttribute"], [18, 36, 91, 2, "target"], [18, 42, 91, 21], [18, 44, 92, 2, "type"], [18, 48, 92, 14], [18, 50, 93, 2, "callback"], [18, 58, 93, 26], [18, 60, 94, 8], [19, 4, 95, 2], [19, 8, 95, 6, "map"], [19, 11, 95, 9], [19, 14, 95, 12, "getEventHandlerAttributeMap"], [19, 41, 95, 39], [19, 42, 95, 40, "target"], [19, 48, 95, 46], [19, 49, 95, 47], [20, 4, 96, 2], [20, 8, 96, 6, "map"], [20, 11, 96, 9], [20, 15, 96, 13], [20, 19, 96, 17], [20, 21, 96, 19], [21, 6, 97, 4], [21, 10, 97, 10, "currentListener"], [21, 25, 97, 25], [21, 28, 97, 28, "map"], [21, 31, 97, 31], [21, 32, 97, 32, "get"], [21, 35, 97, 35], [21, 36, 97, 36, "type"], [21, 40, 97, 40], [21, 41, 97, 41], [22, 6, 98, 4], [22, 10, 98, 8, "currentListener"], [22, 25, 98, 23], [22, 27, 98, 25], [23, 8, 99, 6, "target"], [23, 14, 99, 12], [23, 15, 99, 13, "removeEventListener"], [23, 34, 99, 32], [23, 35, 99, 33, "type"], [23, 39, 99, 37], [23, 41, 99, 39, "currentListener"], [23, 56, 99, 54], [23, 57, 99, 55], [24, 8, 100, 6, "map"], [24, 11, 100, 9], [24, 12, 100, 10, "delete"], [24, 18, 100, 16], [24, 19, 100, 17, "type"], [24, 23, 100, 21], [24, 24, 100, 22], [25, 6, 101, 4], [26, 4, 102, 2], [27, 4, 104, 2], [27, 8, 105, 4, "callback"], [27, 16, 105, 12], [27, 20, 105, 16], [27, 24, 105, 20], [27, 29, 106, 5], [27, 36, 106, 12, "callback"], [27, 44, 106, 20], [27, 49, 106, 25], [27, 59, 106, 35], [27, 63, 106, 39], [27, 70, 106, 46, "callback"], [27, 78, 106, 54], [27, 83, 106, 59], [27, 91, 106, 67], [27, 92, 106, 68], [27, 94, 107, 4], [28, 6, 110, 4], [28, 10, 110, 10, "listener"], [28, 18, 110, 18], [28, 21, 110, 21], [29, 8, 111, 6, "handleEvent"], [29, 19, 111, 17], [29, 21, 111, 19, "callback"], [30, 6, 112, 4], [30, 7, 112, 5], [31, 6, 114, 4], [31, 10, 114, 8], [32, 8, 115, 6, "target"], [32, 14, 115, 12], [32, 15, 115, 13, "addEventListener"], [32, 31, 115, 29], [32, 32, 115, 30, "type"], [32, 36, 115, 34], [32, 38, 115, 36, "listener"], [32, 46, 115, 44], [32, 47, 115, 45], [33, 8, 117, 6], [33, 12, 117, 10, "map"], [33, 15, 117, 13], [33, 19, 117, 17], [33, 23, 117, 21], [33, 25, 117, 23], [34, 10, 118, 8, "map"], [34, 13, 118, 11], [34, 16, 118, 14], [34, 20, 118, 18, "Map"], [34, 23, 118, 21], [34, 24, 118, 22], [34, 25, 118, 23], [35, 10, 119, 8, "setEventHandlerAttributeMap"], [35, 37, 119, 35], [35, 38, 119, 36, "target"], [35, 44, 119, 42], [35, 46, 119, 44, "map"], [35, 49, 119, 47], [35, 50, 119, 48], [36, 8, 120, 6], [37, 8, 121, 6, "map"], [37, 11, 121, 9], [37, 12, 121, 10, "set"], [37, 15, 121, 13], [37, 16, 121, 14, "type"], [37, 20, 121, 18], [37, 22, 121, 20, "listener"], [37, 30, 121, 28], [37, 31, 121, 29], [38, 6, 122, 4], [38, 7, 122, 5], [38, 8, 122, 6], [38, 15, 122, 13, "e"], [38, 16, 122, 14], [38, 18, 122, 16], [38, 19, 124, 4], [39, 4, 125, 2], [40, 4, 127, 2], [40, 8, 127, 6, "map"], [40, 11, 127, 9], [40, 15, 127, 13], [40, 19, 127, 17], [40, 23, 127, 21, "map"], [40, 26, 127, 24], [40, 27, 127, 25, "size"], [40, 31, 127, 29], [40, 36, 127, 34], [40, 37, 127, 35], [40, 39, 127, 37], [41, 6, 128, 4, "setEventHandlerAttributeMap"], [41, 33, 128, 31], [41, 34, 128, 32, "target"], [41, 40, 128, 38], [41, 42, 128, 40], [41, 46, 128, 44], [41, 47, 128, 45], [42, 4, 129, 2], [43, 2, 130, 0], [44, 0, 130, 1], [44, 3]], "functionMap": {"names": ["<global>", "getEventHandlerAttributeMap", "setEventHandlerAttributeMap", "getEventHandlerAttribute", "setEventHandlerAttribute"], "mappings": "AAA;ACsD;CDK;AEE;CFM;OGQ;CHM;OIQ"}}, "type": "js/module"}]}