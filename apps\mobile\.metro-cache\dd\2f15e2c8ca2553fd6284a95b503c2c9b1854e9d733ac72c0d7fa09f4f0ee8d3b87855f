{"dependencies": [{"name": "../Colors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 115}, "end": {"line": 11, "column": 19, "index": 241}}], "key": "Y2vNB3FL9La5/kx04BGVY2eun0w=", "exportNames": ["*"]}}, {"name": "../commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 403}, "end": {"line": 21, "column": 65, "index": 468}}], "key": "dQSfS57Pf/C96+Vvd1rktbJJov4=", "exportNames": ["*"]}}, {"name": "../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 525}, "end": {"line": 23, "column": 44, "index": 569}}], "key": "ioSJ9iLOtXMo2uBjbVE14/NC9RQ=", "exportNames": ["*"]}}, {"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 570}, "end": {"line": 24, "column": 35, "index": 605}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 606}, "end": {"line": 25, "column": 52, "index": 658}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "../ReducedMotion", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 659}, "end": {"line": 26, "column": 56, "index": 715}}], "key": "KOT/pgeLpi4gIRD7QyPlcDltLOw=", "exportNames": ["*"]}}, {"name": "../threads", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0, "index": 716}, "end": {"line": 27, "column": 37, "index": 753}}], "key": "K1yKq+VUoHdgwBY7Fz9TrE1h5uU=", "exportNames": ["*"]}}, {"name": "./transformationMatrix/matrixUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 33, "column": 0, "index": 930}, "end": {"line": 42, "column": 44, "index": 1147}}], "key": "A/njr6R2JYcFLcvcPWxqCTXWp/8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /* eslint-disable @typescript-eslint/no-shadow */\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getReduceMotionFromConfig = exports.getReduceMotionForAnimation = exports.defineAnimation = exports.cancelAnimation = exports.assertEasingIsWorklet = void 0;\n  exports.initialUpdaterRun = initialUpdaterRun;\n  exports.recognizePrefixSuffix = exports.isValidLayoutAnimationProp = void 0;\n  var _Colors = require(_dependencyMap[0], \"../Colors\");\n  var _commonTypes = require(_dependencyMap[1], \"../commonTypes\");\n  var _errors = require(_dependencyMap[2], \"../errors\");\n  var _logger = require(_dependencyMap[3], \"../logger\");\n  var _PlatformChecker = require(_dependencyMap[4], \"../PlatformChecker\");\n  var _ReducedMotion = require(_dependencyMap[5], \"../ReducedMotion\");\n  var _threads = require(_dependencyMap[6], \"../threads\");\n  var _matrixUtils = require(_dependencyMap[7], \"./transformationMatrix/matrixUtils\");\n  var IN_STYLE_UPDATER = false;\n  var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n  var LAYOUT_ANIMATION_SUPPORTED_PROPS = {\n    originX: true,\n    originY: true,\n    width: true,\n    height: true,\n    borderRadius: true,\n    globalOriginX: true,\n    globalOriginY: true,\n    opacity: true,\n    transform: true\n  };\n  var _worklet_16718747144056_init_data = {\n    code: \"function isValidLayoutAnimationProp_utilTs1(prop){const{LAYOUT_ANIMATION_SUPPORTED_PROPS}=this.__closure;return prop in LAYOUT_ANIMATION_SUPPORTED_PROPS;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"isValidLayoutAnimationProp_utilTs1\\\",\\\"prop\\\",\\\"LAYOUT_ANIMATION_SUPPORTED_PROPS\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AA4DO,SAAAA,kCAAkDA,CAAAC,IAAA,QAAAC,gCAAA,OAAAC,SAAA,CAEvD,MAAQ,CAAAF,IAAI,GAA4B,CAAAC,gCAAgC,CAC1E\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var isValidLayoutAnimationProp = exports.isValidLayoutAnimationProp = function () {\n    var _e = [new global.Error(), -2, -27];\n    var isValidLayoutAnimationProp = function (prop) {\n      return prop in LAYOUT_ANIMATION_SUPPORTED_PROPS;\n    };\n    isValidLayoutAnimationProp.__closure = {\n      LAYOUT_ANIMATION_SUPPORTED_PROPS\n    };\n    isValidLayoutAnimationProp.__workletHash = 16718747144056;\n    isValidLayoutAnimationProp.__initData = _worklet_16718747144056_init_data;\n    isValidLayoutAnimationProp.__stackDetails = _e;\n    return isValidLayoutAnimationProp;\n  }();\n  if (__DEV__ && _ReducedMotion.ReducedMotionManager.jsValue) {\n    _logger.logger.warn(`Reduced motion setting is enabled on this device. This warning is visible only in the development mode. Some animations will be disabled by default. You can override the behavior for individual animations, see https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#reduced-motion-setting-is-enabled-on-this-device.`);\n  }\n  var _worklet_11513914120603_init_data = {\n    code: \"function assertEasingIsWorklet_utilTs2(easing){const{SHOULD_BE_USE_WEB,isWorkletFunction}=this.__closure;if(_WORKLET){return;}if(SHOULD_BE_USE_WEB){return;}if(easing!==null&&easing!==void 0&&easing.factory){return;}if(!isWorkletFunction(easing)){throw new ReanimatedError('The easing function is not a worklet. Please make sure you import `Easing` from react-native-reanimated.');}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"assertEasingIsWorklet_utilTs2\\\",\\\"easing\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"isWorkletFunction\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"factory\\\",\\\"ReanimatedError\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AAuEO,SAAAA,6BAENA,CAAAC,MAAO,QAAAC,iBAAA,CAAAC,iBAAA,OAAAC,SAAA,CAEN,GAAIC,QAAQ,CAAE,CAGZ,OACF,CACA,GAAIH,iBAAiB,CAAE,CAErB,OACF,CAEA,GAAID,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEK,OAAO,CAAE,CACnB,OACF,CAEA,GAAI,CAACH,iBAAiB,CAACF,MAAM,CAAC,CAAE,CAC9B,KAAM,IAAI,CAAAM,eAAe,CACvB,0GACF,CAAC,CACH,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var assertEasingIsWorklet = exports.assertEasingIsWorklet = function () {\n    var _e = [new global.Error(), -3, -27];\n    var assertEasingIsWorklet = function (easing) {\n      if (_WORKLET) {\n        // If this is called on UI (for example from gesture handler with worklets), we don't get easing,\n        // but its bound copy, which is not a worklet. We don't want to throw any error then.\n        return;\n      }\n      if (SHOULD_BE_USE_WEB) {\n        // It is possible to run reanimated on web without plugin, so let's skip this check on web\n        return;\n      }\n      // @ts-ignore typescript wants us to use `in` instead, which doesn't work with host objects\n      if (easing?.factory) {\n        return;\n      }\n      if (!(0, _commonTypes.isWorkletFunction)(easing)) {\n        throw new _errors.ReanimatedError('The easing function is not a worklet. Please make sure you import `Easing` from react-native-reanimated.');\n      }\n    };\n    assertEasingIsWorklet.__closure = {\n      SHOULD_BE_USE_WEB,\n      isWorkletFunction: _commonTypes.isWorkletFunction\n    };\n    assertEasingIsWorklet.__workletHash = 11513914120603;\n    assertEasingIsWorklet.__initData = _worklet_11513914120603_init_data;\n    assertEasingIsWorklet.__stackDetails = _e;\n    return assertEasingIsWorklet;\n  }();\n  function initialUpdaterRun(updater) {\n    IN_STYLE_UPDATER = true;\n    var result = updater();\n    IN_STYLE_UPDATER = false;\n    return result;\n  }\n  var _worklet_10505711737785_init_data = {\n    code: \"function recognizePrefixSuffix_utilTs3(value){if(typeof value==='string'){var _match$;const match=value.match(/([A-Za-z]*)(-?\\\\d*\\\\.?\\\\d*)([eE][-+]?[0-9]+)?([A-Za-z%]*)/);if(!match){throw new ReanimatedError(\\\"Couldn't parse animation value.\\\");}const prefix=match[1];const suffix=match[4];const number=match[2]+((_match$=match[3])!==null&&_match$!==void 0?_match$:'');return{prefix:prefix,suffix:suffix,strippedValue:parseFloat(number)};}else{return{strippedValue:value};}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"recognizePrefixSuffix_utilTs3\\\",\\\"value\\\",\\\"_match$\\\",\\\"match\\\",\\\"ReanimatedError\\\",\\\"prefix\\\",\\\"suffix\\\",\\\"number\\\",\\\"strippedValue\\\",\\\"parseFloat\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AA6GO,SAAAA,6BAENA,CAAyBC,KAAA,EAExB,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,KAAAC,OAAA,CAC7B,KAAM,CAAAC,KAAK,CAAGF,KAAK,CAACE,KAAK,CACvB,wDACF,CAAC,CACD,GAAI,CAACA,KAAK,CAAE,CACV,KAAM,IAAI,CAAAC,eAAe,CAAC,iCAAiC,CAAC,CAC9D,CACA,KAAM,CAAAC,MAAM,CAAGF,KAAK,CAAC,CAAC,CAAC,CACvB,KAAM,CAAAG,MAAM,CAAGH,KAAK,CAAC,CAAC,CAAC,CAEvB,KAAM,CAAAI,MAAM,CAAGJ,KAAK,CAAC,CAAC,CAAC,GAAAD,OAAA,CAAIC,KAAK,CAAC,CAAC,CAAC,UAAAD,OAAA,UAAAA,OAAA,CAAI,EAAE,CAAC,CAC1C,MAAO,CAAEG,MAAM,CAANA,MAAM,CAAEC,MAAM,CAANA,MAAM,CAAEE,aAAa,CAAEC,UAAU,CAACF,MAAM,CAAE,CAAC,CAC9D,CAAC,IAAM,CACL,MAAO,CAAEC,aAAa,CAAEP,KAAM,CAAC,CACjC,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var recognizePrefixSuffix = exports.recognizePrefixSuffix = function () {\n    var _e = [new global.Error(), 1, -27];\n    var recognizePrefixSuffix = function (value) {\n      if (typeof value === 'string') {\n        var match = value.match(/([A-Za-z]*)(-?\\d*\\.?\\d*)([eE][-+]?[0-9]+)?([A-Za-z%]*)/);\n        if (!match) {\n          throw new _errors.ReanimatedError(\"Couldn't parse animation value.\");\n        }\n        var prefix = match[1];\n        var suffix = match[4];\n        // number with scientific notation\n        var number = match[2] + (match[3] ?? '');\n        return {\n          prefix,\n          suffix,\n          strippedValue: parseFloat(number)\n        };\n      } else {\n        return {\n          strippedValue: value\n        };\n      }\n    };\n    recognizePrefixSuffix.__closure = {};\n    recognizePrefixSuffix.__workletHash = 10505711737785;\n    recognizePrefixSuffix.__initData = _worklet_10505711737785_init_data;\n    recognizePrefixSuffix.__stackDetails = _e;\n    return recognizePrefixSuffix;\n  }();\n  /**\n   * Returns whether the motion should be reduced for a specified config. By\n   * default returns the system setting.\n   */\n  var isReduceMotionOnUI = _ReducedMotion.ReducedMotionManager.uiValue;\n  var _worklet_7714090217876_init_data = {\n    code: \"function getReduceMotionFromConfig_utilTs4(config){const{ReduceMotion,isReduceMotionOnUI}=this.__closure;return!config||config===ReduceMotion.System?isReduceMotionOnUI.value:config===ReduceMotion.Always;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getReduceMotionFromConfig_utilTs4\\\",\\\"config\\\",\\\"ReduceMotion\\\",\\\"isReduceMotionOnUI\\\",\\\"__closure\\\",\\\"System\\\",\\\"value\\\",\\\"Always\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AAuIO,SAAAA,iCAAwDA,CAAAC,MAAE,QAAAC,YAAA,CAAAC,kBAAA,OAAAC,SAAA,CAE/D,MAAO,CAACH,MAAM,EAAIA,MAAM,GAAKC,YAAY,CAACG,MAAM,CAC5CF,kBAAkB,CAACG,KAAK,CACxBL,MAAM,GAAKC,YAAY,CAACK,MAAM,CACpC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var getReduceMotionFromConfig = exports.getReduceMotionFromConfig = function () {\n    var _e = [new global.Error(), -3, -27];\n    var getReduceMotionFromConfig = function (config) {\n      return !config || config === _commonTypes.ReduceMotion.System ? isReduceMotionOnUI.value : config === _commonTypes.ReduceMotion.Always;\n    };\n    getReduceMotionFromConfig.__closure = {\n      ReduceMotion: _commonTypes.ReduceMotion,\n      isReduceMotionOnUI\n    };\n    getReduceMotionFromConfig.__workletHash = 7714090217876;\n    getReduceMotionFromConfig.__initData = _worklet_7714090217876_init_data;\n    getReduceMotionFromConfig.__stackDetails = _e;\n    return getReduceMotionFromConfig;\n  }();\n  /**\n   * Returns the value that should be assigned to `animation.reduceMotion` for a\n   * given config. If the config is not defined, `undefined` is returned.\n   */\n  var _worklet_16606661392394_init_data = {\n    code: \"function getReduceMotionForAnimation_utilTs5(config){const{getReduceMotionFromConfig}=this.__closure;if(!config){return undefined;}return getReduceMotionFromConfig(config);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getReduceMotionForAnimation_utilTs5\\\",\\\"config\\\",\\\"getReduceMotionFromConfig\\\",\\\"__closure\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AAkJO,SAAAA,mCAA0DA,CAAAC,MAAE,QAAAC,yBAAA,OAAAC,SAAA,CAIjE,GAAI,CAACF,MAAM,CAAE,CACX,MAAO,CAAAG,SAAS,CAClB,CAEA,MAAO,CAAAF,yBAAyB,CAACD,MAAM,CAAC,CAC1C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var getReduceMotionForAnimation = exports.getReduceMotionForAnimation = function () {\n    var _e = [new global.Error(), -2, -27];\n    var getReduceMotionForAnimation = function (config) {\n      // if the config is not defined, we want `reduceMotion` to be undefined,\n      // so the parent animation knows if it should overwrite it\n      if (!config) {\n        return undefined;\n      }\n      return getReduceMotionFromConfig(config);\n    };\n    getReduceMotionForAnimation.__closure = {\n      getReduceMotionFromConfig\n    };\n    getReduceMotionForAnimation.__workletHash = 16606661392394;\n    getReduceMotionForAnimation.__initData = _worklet_16606661392394_init_data;\n    getReduceMotionForAnimation.__stackDetails = _e;\n    return getReduceMotionForAnimation;\n  }();\n  var _worklet_3999595183904_init_data = {\n    code: \"function applyProgressToMatrix_utilTs6(progress,a,b){const{addMatrices,scaleMatrix,subtractMatrices}=this.__closure;return addMatrices(a,scaleMatrix(subtractMatrices(b,a),progress));}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"applyProgressToMatrix_utilTs6\\\",\\\"progress\\\",\\\"a\\\",\\\"b\\\",\\\"addMatrices\\\",\\\"scaleMatrix\\\",\\\"subtractMatrices\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AA6JA,SAAAA,6BACEA,CAAAC,QAEe,CACfC,CAAA,CAAAC,CAAA,QAAAC,WAAA,CAAAC,WAAA,CAAAC,gBAAA,OAAAC,SAAA,CAEA,MAAO,CAAAH,WAAW,CAACF,CAAC,CAAEG,WAAW,CAACC,gBAAgB,CAACH,CAAC,CAAED,CAAC,CAAC,CAAED,QAAQ,CAAC,CAAC,CACtE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var applyProgressToMatrix = function () {\n    var _e = [new global.Error(), -4, -27];\n    var applyProgressToMatrix = function (progress, a, b) {\n      return (0, _matrixUtils.addMatrices)(a, (0, _matrixUtils.scaleMatrix)((0, _matrixUtils.subtractMatrices)(b, a), progress));\n    };\n    applyProgressToMatrix.__closure = {\n      addMatrices: _matrixUtils.addMatrices,\n      scaleMatrix: _matrixUtils.scaleMatrix,\n      subtractMatrices: _matrixUtils.subtractMatrices\n    };\n    applyProgressToMatrix.__workletHash = 3999595183904;\n    applyProgressToMatrix.__initData = _worklet_3999595183904_init_data;\n    applyProgressToMatrix.__stackDetails = _e;\n    return applyProgressToMatrix;\n  }();\n  var _worklet_15372800802213_init_data = {\n    code: \"function applyProgressToNumber_utilTs7(progress,a,b){return a+progress*(b-a);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"applyProgressToNumber_utilTs7\\\",\\\"progress\\\",\\\"a\\\",\\\"b\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AAsKA,SAAAA,6BAA+BA,CAAAC,QAAsC,CAAEC,CAAA,CAAAC,CAAA,EAErE,MAAO,CAAAD,CAAC,CAAGD,QAAQ,EAAIE,CAAC,CAAGD,CAAC,CAAC,CAC/B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var applyProgressToNumber = function () {\n    var _e = [new global.Error(), 1, -27];\n    var applyProgressToNumber = function (progress, a, b) {\n      return a + progress * (b - a);\n    };\n    applyProgressToNumber.__closure = {};\n    applyProgressToNumber.__workletHash = 15372800802213;\n    applyProgressToNumber.__initData = _worklet_15372800802213_init_data;\n    applyProgressToNumber.__stackDetails = _e;\n    return applyProgressToNumber;\n  }();\n  var _worklet_1842068436516_init_data = {\n    code: \"function decorateAnimation_utilTs8(animation){const{getReduceMotionFromConfig,recognizePrefixSuffix,isColor,toLinearSpace,convertToRGBA,clampRGBA,rgbaArrayToRGBAColor,toGammaSpace,decomposeMatrixIntoMatricesAndAngles,applyProgressToMatrix,applyProgressToNumber,getRotationMatrix,multiplyMatrices,flatten,isAffineMatrixFlat}=this.__closure;const baseOnStart=animation.onStart;const baseOnFrame=animation.onFrame;if(animation.isHigherOrder){animation.onStart=function(animation,value,timestamp,previousAnimation){if(animation.reduceMotion===undefined){animation.reduceMotion=getReduceMotionFromConfig();}return baseOnStart(animation,value,timestamp,previousAnimation);};return;}const animationCopy=Object.assign({},animation);delete animationCopy.callback;const prefNumberSuffOnStart=function(animation,value,timestamp,previousAnimation){var _animation$__prefix,_animation$__suffix;const{prefix:prefix,suffix:suffix,strippedValue:strippedValue}=recognizePrefixSuffix(value);animation.__prefix=prefix;animation.__suffix=suffix;animation.strippedCurrent=strippedValue;const{strippedValue:strippedToValue}=recognizePrefixSuffix(animation.toValue);animation.current=strippedValue;animation.startValue=strippedValue;animation.toValue=strippedToValue;if(previousAnimation&&previousAnimation!==animation){const{prefix:paPrefix,suffix:paSuffix,strippedValue:paStrippedValue}=recognizePrefixSuffix(previousAnimation.current);previousAnimation.current=paStrippedValue;previousAnimation.__prefix=paPrefix;previousAnimation.__suffix=paSuffix;}baseOnStart(animation,strippedValue,timestamp,previousAnimation);animation.current=((_animation$__prefix=animation.__prefix)!==null&&_animation$__prefix!==void 0?_animation$__prefix:'')+animation.current+((_animation$__suffix=animation.__suffix)!==null&&_animation$__suffix!==void 0?_animation$__suffix:'');if(previousAnimation&&previousAnimation!==animation){var _previousAnimation$__,_previousAnimation$__2;previousAnimation.current=((_previousAnimation$__=previousAnimation.__prefix)!==null&&_previousAnimation$__!==void 0?_previousAnimation$__:'')+previousAnimation.current+((_previousAnimation$__2=previousAnimation.__suffix)!==null&&_previousAnimation$__2!==void 0?_previousAnimation$__2:'');}};const prefNumberSuffOnFrame=function(animation,timestamp){var _animation$__prefix2,_animation$__suffix2;animation.current=animation.strippedCurrent;const res=baseOnFrame(animation,timestamp);animation.strippedCurrent=animation.current;animation.current=((_animation$__prefix2=animation.__prefix)!==null&&_animation$__prefix2!==void 0?_animation$__prefix2:'')+animation.current+((_animation$__suffix2=animation.__suffix)!==null&&_animation$__suffix2!==void 0?_animation$__suffix2:'');return res;};const tab=['R','G','B','A'];const colorOnStart=function(animation,value,timestamp,previousAnimation){let RGBAValue;let RGBACurrent;let RGBAToValue;const res=[];if(isColor(value)){RGBACurrent=toLinearSpace(convertToRGBA(animation.current));RGBAValue=toLinearSpace(convertToRGBA(value));if(animation.toValue){RGBAToValue=toLinearSpace(convertToRGBA(animation.toValue));}}tab.forEach(function(i,index){animation[i]=Object.assign({},animationCopy);animation[i].current=RGBACurrent[index];animation[i].toValue=RGBAToValue?RGBAToValue[index]:undefined;animation[i].onStart(animation[i],RGBAValue[index],timestamp,previousAnimation?previousAnimation[i]:undefined);res.push(animation[i].current);});clampRGBA(res);animation.current=rgbaArrayToRGBAColor(toGammaSpace(res));};const colorOnFrame=function(animation,timestamp){const RGBACurrent=toLinearSpace(convertToRGBA(animation.current));const res=[];let finished=true;tab.forEach(function(i,index){animation[i].current=RGBACurrent[index];const result=animation[i].onFrame(animation[i],timestamp);finished=finished&&result;res.push(animation[i].current);});clampRGBA(res);animation.current=rgbaArrayToRGBAColor(toGammaSpace(res));return finished;};const transformationMatrixOnStart=function(animation,value,timestamp,previousAnimation){const toValue=animation.toValue;animation.startMatrices=decomposeMatrixIntoMatricesAndAngles(value);animation.stopMatrices=decomposeMatrixIntoMatricesAndAngles(toValue);animation[0]=Object.assign({},animationCopy);animation[0].current=0;animation[0].toValue=100;animation[0].onStart(animation[0],0,timestamp,previousAnimation?previousAnimation[0]:undefined);animation.current=value;};const transformationMatrixOnFrame=function(animation,timestamp){let finished=true;const result=animation[0].onFrame(animation[0],timestamp);finished=finished&&result;const progress=animation[0].current/100;const transforms=['translationMatrix','scaleMatrix','skewMatrix'];const mappedTransforms=[];transforms.forEach(function(key,_){return mappedTransforms.push(applyProgressToMatrix(progress,animation.startMatrices[key],animation.stopMatrices[key]));});const[currentTranslation,currentScale,skewMatrix]=mappedTransforms;const rotations=['x','y','z'];const mappedRotations=[];rotations.forEach(function(key,_){const angle=applyProgressToNumber(progress,animation.startMatrices['r'+key],animation.stopMatrices['r'+key]);mappedRotations.push(getRotationMatrix(angle,key));});const[rotationMatrixX,rotationMatrixY,rotationMatrixZ]=mappedRotations;const rotationMatrix=multiplyMatrices(rotationMatrixX,multiplyMatrices(rotationMatrixY,rotationMatrixZ));const updated=flatten(multiplyMatrices(multiplyMatrices(currentScale,multiplyMatrices(skewMatrix,rotationMatrix)),currentTranslation));animation.current=updated;return finished;};const arrayOnStart=function(animation,value,timestamp,previousAnimation){value.forEach(function(v,i){animation[i]=Object.assign({},animationCopy);animation[i].current=v;animation[i].toValue=animation.toValue[i];animation[i].onStart(animation[i],v,timestamp,previousAnimation?previousAnimation[i]:undefined);});animation.current=[...value];};const arrayOnFrame=function(animation,timestamp){let finished=true;animation.current.forEach(function(_,i){const result=animation[i].onFrame(animation[i],timestamp);finished=finished&&result;animation.current[i]=animation[i].current;});return finished;};const objectOnStart=function(animation,value,timestamp,previousAnimation){for(const key in value){animation[key]=Object.assign({},animationCopy);animation[key].onStart=animation.onStart;animation[key].current=value[key];animation[key].toValue=animation.toValue[key];animation[key].onStart(animation[key],value[key],timestamp,previousAnimation?previousAnimation[key]:undefined);}animation.current=value;};const objectOnFrame=function(animation,timestamp){let finished=true;const newObject={};for(const key in animation.current){const result=animation[key].onFrame(animation[key],timestamp);finished=finished&&result;newObject[key]=animation[key].current;}animation.current=newObject;return finished;};animation.onStart=function(animation,value,timestamp,previousAnimation){if(animation.reduceMotion===undefined){animation.reduceMotion=getReduceMotionFromConfig();}if(animation.reduceMotion){if(animation.toValue!==undefined){animation.current=animation.toValue;}else{baseOnStart(animation,value,timestamp,previousAnimation);}animation.startTime=0;animation.onFrame=function(){return true;};return;}if(isColor(value)){colorOnStart(animation,value,timestamp,previousAnimation);animation.onFrame=colorOnFrame;return;}else if(isAffineMatrixFlat(value)){transformationMatrixOnStart(animation,value,timestamp,previousAnimation);animation.onFrame=transformationMatrixOnFrame;return;}else if(Array.isArray(value)){arrayOnStart(animation,value,timestamp,previousAnimation);animation.onFrame=arrayOnFrame;return;}else if(typeof value==='string'){prefNumberSuffOnStart(animation,value,timestamp,previousAnimation);animation.onFrame=prefNumberSuffOnFrame;return;}else if(typeof value==='object'&&value!==null){objectOnStart(animation,value,timestamp,previousAnimation);animation.onFrame=objectOnFrame;return;}baseOnStart(animation,value,timestamp,previousAnimation);};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"decorateAnimation_utilTs8\\\",\\\"animation\\\",\\\"getReduceMotionFromConfig\\\",\\\"recognizePrefixSuffix\\\",\\\"isColor\\\",\\\"toLinearSpace\\\",\\\"convertToRGBA\\\",\\\"clampRGBA\\\",\\\"rgbaArrayToRGBAColor\\\",\\\"toGammaSpace\\\",\\\"decomposeMatrixIntoMatricesAndAngles\\\",\\\"applyProgressToMatrix\\\",\\\"applyProgressToNumber\\\",\\\"getRotationMatrix\\\",\\\"multiplyMatrices\\\",\\\"flatten\\\",\\\"isAffineMatrixFlat\\\",\\\"__closure\\\",\\\"baseOnStart\\\",\\\"onStart\\\",\\\"baseOnFrame\\\",\\\"onFrame\\\",\\\"isHigherOrder\\\",\\\"value\\\",\\\"timestamp\\\",\\\"previousAnimation\\\",\\\"reduceMotion\\\",\\\"undefined\\\",\\\"animationCopy\\\",\\\"Object\\\",\\\"assign\\\",\\\"callback\\\",\\\"prefNumberSuffOnStart\\\",\\\"_animation$__prefix\\\",\\\"_animation$__suffix\\\",\\\"prefix\\\",\\\"suffix\\\",\\\"strippedValue\\\",\\\"__prefix\\\",\\\"__suffix\\\",\\\"strippedCurrent\\\",\\\"strippedToValue\\\",\\\"toValue\\\",\\\"current\\\",\\\"startValue\\\",\\\"paPrefix\\\",\\\"paSuffix\\\",\\\"paStrippedValue\\\",\\\"_previousAnimation$__\\\",\\\"_previousAnimation$__2\\\",\\\"prefNumberSuffOnFrame\\\",\\\"_animation$__prefix2\\\",\\\"_animation$__suffix2\\\",\\\"res\\\",\\\"tab\\\",\\\"colorOnStart\\\",\\\"RGBAValue\\\",\\\"RGBACurrent\\\",\\\"RGBAToValue\\\",\\\"forEach\\\",\\\"i\\\",\\\"index\\\",\\\"push\\\",\\\"colorOnFrame\\\",\\\"finished\\\",\\\"result\\\",\\\"transformationMatrixOnStart\\\",\\\"startMatrices\\\",\\\"stopMatrices\\\",\\\"transformationMatrixOnFrame\\\",\\\"progress\\\",\\\"transforms\\\",\\\"mappedTransforms\\\",\\\"key\\\",\\\"_\\\",\\\"currentTranslation\\\",\\\"currentScale\\\",\\\"skewMatrix\\\",\\\"rotations\\\",\\\"mappedRotations\\\",\\\"angle\\\",\\\"rotationMatrixX\\\",\\\"rotationMatrixY\\\",\\\"rotationMatrixZ\\\",\\\"rotationMatrix\\\",\\\"updated\\\",\\\"arrayOnStart\\\",\\\"v\\\",\\\"arrayOnFrame\\\",\\\"objectOnStart\\\",\\\"objectOnFrame\\\",\\\"newObject\\\",\\\"startTime\\\",\\\"Array\\\",\\\"isArray\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AA2KA,SAAAA,yBACEA,CAAAC,SACM,QAAAC,yBAAA,CAAAC,qBAAA,CAAAC,OAAA,CAAAC,aAAA,CAAAC,aAAA,CAAAC,SAAA,CAAAC,oBAAA,CAAAC,YAAA,CAAAC,oCAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAC,OAAA,CAAAC,kBAAA,OAAAC,SAAA,CAEN,KAAM,CAAAC,WAAW,CAAIjB,SAAS,CAAgCkB,OAAO,CACrE,KAAM,CAAAC,WAAW,CAAInB,SAAS,CAAgCoB,OAAO,CAErE,GAAKpB,SAAS,CAA0BqB,aAAa,CAAE,CACrDrB,SAAS,CAACkB,OAAO,CAAG,SAClBlB,SAAqC,CACrCsB,KAAa,CACbC,SAAoB,CACpBC,iBAA6C,CAC1C,CACH,GAAIxB,SAAS,CAACyB,YAAY,GAAKC,SAAS,CAAE,CACxC1B,SAAS,CAACyB,YAAY,CAAGxB,yBAAyB,CAAC,CAAC,CACtD,CACA,MAAO,CAAAgB,WAAW,CAACjB,SAAS,CAAEsB,KAAK,CAAEC,SAAS,CAAEC,iBAAiB,CAAC,CACpE,CAAC,CACD,OACF,CAEA,KAAM,CAAAG,aAAa,CAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAE7B,SAAS,CAAC,CAClD,MAAO,CAAA2B,aAAa,CAACG,QAAQ,CAE7B,KAAM,CAAAC,qBAAqB,CAAG,QAAAA,CAC5B/B,SAAqC,CACrCsB,KAAsB,CACtBC,SAAiB,CACjBC,iBAA6C,CAC1C,KAAAQ,mBAAA,CAAAC,mBAAA,CAEH,KAAM,CAAEC,MAAM,CAANA,MAAM,CAAEC,MAAM,CAANA,MAAM,CAAEC,aAAA,CAAAA,aAAc,CAAC,CAAGlC,qBAAqB,CAACoB,KAAK,CAAC,CACtEtB,SAAS,CAACqC,QAAQ,CAAGH,MAAM,CAC3BlC,SAAS,CAACsC,QAAQ,CAAGH,MAAM,CAC3BnC,SAAS,CAACuC,eAAe,CAAGH,aAAa,CACzC,KAAM,CAAEA,aAAa,CAAEI,eAAgB,CAAC,CAAGtC,qBAAqB,CAC9DF,SAAS,CAACyC,OACZ,CAAC,CACDzC,SAAS,CAAC0C,OAAO,CAAGN,aAAa,CACjCpC,SAAS,CAAC2C,UAAU,CAAGP,aAAa,CACpCpC,SAAS,CAACyC,OAAO,CAAGD,eAAe,CACnC,GAAIhB,iBAAiB,EAAIA,iBAAiB,GAAKxB,SAAS,CAAE,CACxD,KAAM,CACJkC,MAAM,CAAEU,QAAQ,CAChBT,MAAM,CAAEU,QAAQ,CAChBT,aAAa,CAAEU,eACjB,CAAC,CAAG5C,qBAAqB,CAACsB,iBAAiB,CAACkB,OAA0B,CAAC,CACvElB,iBAAiB,CAACkB,OAAO,CAAGI,eAAe,CAC3CtB,iBAAiB,CAACa,QAAQ,CAAGO,QAAQ,CACrCpB,iBAAiB,CAACc,QAAQ,CAAGO,QAAQ,CACvC,CAEA5B,WAAW,CAACjB,SAAS,CAAEoC,aAAa,CAAEb,SAAS,CAAEC,iBAAiB,CAAC,CAEnExB,SAAS,CAAC0C,OAAO,CACf,EAAAV,mBAAA,CAAChC,SAAS,CAACqC,QAAQ,UAAAL,mBAAA,UAAAA,mBAAA,CAAI,EAAE,EACzBhC,SAAS,CAAC0C,OAAO,GAAAT,mBAAA,CAChBjC,SAAS,CAACsC,QAAQ,UAAAL,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CAE5B,GAAIT,iBAAiB,EAAIA,iBAAiB,GAAKxB,SAAS,CAAE,KAAA+C,qBAAA,CAAAC,sBAAA,CACxDxB,iBAAiB,CAACkB,OAAO,CACvB,EAAAK,qBAAA,CAACvB,iBAAiB,CAACa,QAAQ,UAAAU,qBAAA,UAAAA,qBAAA,CAAI,EAAE,EAGjCvB,iBAAiB,CAACkB,OAAO,GAAAM,sBAAA,CACxBxB,iBAAiB,CAACc,QAAQ,UAAAU,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CACtC,CACF,CAAC,CACD,KAAM,CAAAC,qBAAqB,CAAG,QAAAA,CAC5BjD,SAAqC,CACrCuB,SAAiB,CACd,KAAA2B,oBAAA,CAAAC,oBAAA,CACHnD,SAAS,CAAC0C,OAAO,CAAG1C,SAAS,CAACuC,eAAe,CAC7C,KAAM,CAAAa,GAAG,CAAGjC,WAAW,CAACnB,SAAS,CAAEuB,SAAS,CAAC,CAC7CvB,SAAS,CAACuC,eAAe,CAAGvC,SAAS,CAAC0C,OAAO,CAC7C1C,SAAS,CAAC0C,OAAO,CACf,EAAAQ,oBAAA,CAAClD,SAAS,CAACqC,QAAQ,UAAAa,oBAAA,UAAAA,oBAAA,CAAI,EAAE,EACzBlD,SAAS,CAAC0C,OAAO,GAAAS,oBAAA,CAChBnD,SAAS,CAACsC,QAAQ,UAAAa,oBAAA,UAAAA,oBAAA,CAAI,EAAE,CAAC,CAC5B,MAAO,CAAAC,GAAG,CACZ,CAAC,CAED,KAAM,CAAAC,GAAG,CAAG,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAChC,KAAM,CAAAC,YAAY,CAAG,QAAAA,CACnBtD,SAAqC,CACrCsB,KAAsB,CACtBC,SAAoB,CACpBC,iBAA6C,CACpC,CACT,GAAI,CAAA+B,SAA2B,CAC/B,GAAI,CAAAC,WAA6B,CACjC,GAAI,CAAAC,WAA6B,CACjC,KAAM,CAAAL,GAAkB,CAAG,EAAE,CAC7B,GAAIjD,OAAO,CAACmB,KAAK,CAAC,CAAE,CAClBkC,WAAW,CAAGpD,aAAa,CAACC,aAAa,CAACL,SAAS,CAAC0C,OAAO,CAAC,CAAC,CAC7Da,SAAS,CAAGnD,aAAa,CAACC,aAAa,CAACiB,KAAK,CAAC,CAAC,CAC/C,GAAItB,SAAS,CAACyC,OAAO,CAAE,CACrBgB,WAAW,CAAGrD,aAAa,CAACC,aAAa,CAACL,SAAS,CAACyC,OAAO,CAAC,CAAC,CAC/D,CACF,CACAY,GAAG,CAACK,OAAO,CAAC,SAACC,CAAC,CAAEC,KAAK,CAAK,CACxB5D,SAAS,CAAC2D,CAAC,CAAC,CAAG/B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAEF,aAAa,CAAC,CAC/C3B,SAAS,CAAC2D,CAAC,CAAC,CAACjB,OAAO,CAAGc,WAAW,CAACI,KAAK,CAAC,CACzC5D,SAAS,CAAC2D,CAAC,CAAC,CAAClB,OAAO,CAAGgB,WAAW,CAAGA,WAAW,CAACG,KAAK,CAAC,CAAGlC,SAAS,CACnE1B,SAAS,CAAC2D,CAAC,CAAC,CAACzC,OAAO,CAClBlB,SAAS,CAAC2D,CAAC,CAAC,CACZJ,SAAS,CAACK,KAAK,CAAC,CAChBrC,SAAS,CACTC,iBAAiB,CAAGA,iBAAiB,CAACmC,CAAC,CAAC,CAAGjC,SAC7C,CAAC,CACD0B,GAAG,CAACS,IAAI,CAAC7D,SAAS,CAAC2D,CAAC,CAAC,CAACjB,OAAO,CAAC,CAChC,CAAC,CAAC,CAGFpC,SAAS,CAAC8C,GAAuB,CAAC,CAElCpD,SAAS,CAAC0C,OAAO,CAAGnC,oBAAoB,CACtCC,YAAY,CAAC4C,GAAuB,CACtC,CAAC,CACH,CAAC,CAED,KAAM,CAAAU,YAAY,CAAG,QAAAA,CACnB9D,SAAqC,CACrCuB,SAAoB,CACR,CACZ,KAAM,CAAAiC,WAAW,CAAGpD,aAAa,CAACC,aAAa,CAACL,SAAS,CAAC0C,OAAO,CAAC,CAAC,CACnE,KAAM,CAAAU,GAAkB,CAAG,EAAE,CAC7B,GAAI,CAAAW,QAAQ,CAAG,IAAI,CACnBV,GAAG,CAACK,OAAO,CAAC,SAACC,CAAC,CAAEC,KAAK,CAAK,CACxB5D,SAAS,CAAC2D,CAAC,CAAC,CAACjB,OAAO,CAAGc,WAAW,CAACI,KAAK,CAAC,CACzC,KAAM,CAAAI,MAAM,CAAGhE,SAAS,CAAC2D,CAAC,CAAC,CAACvC,OAAO,CAACpB,SAAS,CAAC2D,CAAC,CAAC,CAAEpC,SAAS,CAAC,CAE5DwC,QAAQ,CAAGA,QAAQ,EAAIC,MAAM,CAC7BZ,GAAG,CAACS,IAAI,CAAC7D,SAAS,CAAC2D,CAAC,CAAC,CAACjB,OAAO,CAAC,CAChC,CAAC,CAAC,CAGFpC,SAAS,CAAC8C,GAAuB,CAAC,CAElCpD,SAAS,CAAC0C,OAAO,CAAGnC,oBAAoB,CACtCC,YAAY,CAAC4C,GAAuB,CACtC,CAAC,CACD,MAAO,CAAAW,QAAQ,CACjB,CAAC,CAED,KAAM,CAAAE,2BAA2B,CAAG,QAAAA,CAClCjE,SAAqC,CACrCsB,KAAuB,CACvBC,SAAoB,CACpBC,iBAA6C,CACpC,CACT,KAAM,CAAAiB,OAAO,CAAGzC,SAAS,CAACyC,OAA2B,CAErDzC,SAAS,CAACkE,aAAa,CAAGzD,oCAAoC,CAACa,KAAK,CAAC,CACrEtB,SAAS,CAACmE,YAAY,CAAG1D,oCAAoC,CAACgC,OAAO,CAAC,CAMtEzC,SAAS,CAAC,CAAC,CAAC,CAAG4B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAEF,aAAa,CAAC,CAC/C3B,SAAS,CAAC,CAAC,CAAC,CAAC0C,OAAO,CAAG,CAAC,CACxB1C,SAAS,CAAC,CAAC,CAAC,CAACyC,OAAO,CAAG,GAAG,CAC1BzC,SAAS,CAAC,CAAC,CAAC,CAACkB,OAAO,CAClBlB,SAAS,CAAC,CAAC,CAAC,CACZ,CAAC,CACDuB,SAAS,CACTC,iBAAiB,CAAGA,iBAAiB,CAAC,CAAC,CAAC,CAAGE,SAC7C,CAAC,CAED1B,SAAS,CAAC0C,OAAO,CAAGpB,KAAK,CAC3B,CAAC,CAED,KAAM,CAAA8C,2BAA2B,CAAG,QAAAA,CAClCpE,SAAqC,CACrCuB,SAAoB,CACR,CACZ,GAAI,CAAAwC,QAAQ,CAAG,IAAI,CACnB,KAAM,CAAAC,MAAM,CAAGhE,SAAS,CAAC,CAAC,CAAC,CAACoB,OAAO,CAACpB,SAAS,CAAC,CAAC,CAAC,CAAEuB,SAAS,CAAC,CAE5DwC,QAAQ,CAAGA,QAAQ,EAAIC,MAAM,CAE7B,KAAM,CAAAK,QAAQ,CAAGrE,SAAS,CAAC,CAAC,CAAC,CAAC0C,OAAO,CAAG,GAAG,CAE3C,KAAM,CAAA4B,UAAU,CAAG,CAAC,mBAAmB,CAAE,aAAa,CAAE,YAAY,CAAC,CACrE,KAAM,CAAAC,gBAAqC,CAAG,EAAE,CAEhDD,UAAU,CAACZ,OAAO,CAAC,SAACc,GAAG,CAAEC,CAAC,QACxB,CAAAF,gBAAgB,CAACV,IAAI,CACnBnD,qBAAqB,CACnB2D,QAAQ,CACRrE,SAAS,CAACkE,aAAa,CAACM,GAAG,CAAC,CAC5BxE,SAAS,CAACmE,YAAY,CAACK,GAAG,CAC5B,CACF,CACF,GAAC,CAED,KAAM,CAACE,kBAAkB,CAAEC,YAAY,CAAEC,UAAU,CAAC,CAAGL,gBAAgB,CAEvE,KAAM,CAAAM,SAAiC,CAAG,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CACzD,KAAM,CAAAC,eAAoC,CAAG,EAAE,CAE/CD,SAAS,CAACnB,OAAO,CAAC,SAACc,GAAG,CAAEC,CAAC,CAAK,CAC5B,KAAM,CAAAM,KAAK,CAAGpE,qBAAqB,CACjC0D,QAAQ,CACRrE,SAAS,CAACkE,aAAa,CAAC,GAAG,CAAGM,GAAG,CAAC,CAClCxE,SAAS,CAACmE,YAAY,CAAC,GAAG,CAAGK,GAAG,CAClC,CAAC,CACDM,eAAe,CAACjB,IAAI,CAACjD,iBAAiB,CAACmE,KAAK,CAAEP,GAAG,CAAC,CAAC,CACrD,CAAC,CAAC,CAEF,KAAM,CAACQ,eAAe,CAAEC,eAAe,CAAEC,eAAe,CAAC,CAAGJ,eAAe,CAE3E,KAAM,CAAAK,cAAc,CAAGtE,gBAAgB,CACrCmE,eAAe,CACfnE,gBAAgB,CAACoE,eAAe,CAAEC,eAAe,CACnD,CAAC,CAED,KAAM,CAAAE,OAAO,CAAGtE,OAAO,CACrBD,gBAAgB,CACdA,gBAAgB,CACd8D,YAAY,CACZ9D,gBAAgB,CAAC+D,UAAU,CAAEO,cAAc,CAC7C,CAAC,CACDT,kBACF,CACF,CAAC,CAED1E,SAAS,CAAC0C,OAAO,CAAG0C,OAAO,CAE3B,MAAO,CAAArB,QAAQ,CACjB,CAAC,CAED,KAAM,CAAAsB,YAAY,CAAG,QAAAA,CACnBrF,SAAqC,CACrCsB,KAAoB,CACpBC,SAAoB,CACpBC,iBAA6C,CACpC,CACTF,KAAK,CAACoC,OAAO,CAAC,SAAC4B,CAAC,CAAE3B,CAAC,CAAK,CACtB3D,SAAS,CAAC2D,CAAC,CAAC,CAAG/B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAEF,aAAa,CAAC,CAC/C3B,SAAS,CAAC2D,CAAC,CAAC,CAACjB,OAAO,CAAG4C,CAAC,CACxBtF,SAAS,CAAC2D,CAAC,CAAC,CAAClB,OAAO,CAAIzC,SAAS,CAACyC,OAAO,CAAmBkB,CAAC,CAAC,CAC9D3D,SAAS,CAAC2D,CAAC,CAAC,CAACzC,OAAO,CAClBlB,SAAS,CAAC2D,CAAC,CAAC,CACZ2B,CAAC,CACD/D,SAAS,CACTC,iBAAiB,CAAGA,iBAAiB,CAACmC,CAAC,CAAC,CAAGjC,SAC7C,CAAC,CACH,CAAC,CAAC,CACF1B,SAAS,CAAC0C,OAAO,CAAG,CAAC,GAAGpB,KAAK,CAAC,CAChC,CAAC,CAED,KAAM,CAAAiE,YAAY,CAAG,QAAAA,CACnBvF,SAAqC,CACrCuB,SAAoB,CACR,CACZ,GAAI,CAAAwC,QAAQ,CAAG,IAAI,CAClB/D,SAAS,CAAC0C,OAAO,CAAmBgB,OAAO,CAAC,SAACe,CAAC,CAAEd,CAAC,CAAK,CACrD,KAAM,CAAAK,MAAM,CAAGhE,SAAS,CAAC2D,CAAC,CAAC,CAACvC,OAAO,CAACpB,SAAS,CAAC2D,CAAC,CAAC,CAAEpC,SAAS,CAAC,CAE5DwC,QAAQ,CAAGA,QAAQ,EAAIC,MAAM,CAC5BhE,SAAS,CAAC0C,OAAO,CAAmBiB,CAAC,CAAC,CAAG3D,SAAS,CAAC2D,CAAC,CAAC,CAACjB,OAAO,CAChE,CAAC,CAAC,CAEF,MAAO,CAAAqB,QAAQ,CACjB,CAAC,CAED,KAAM,CAAAyB,aAAa,CAAG,QAAAA,CACpBxF,SAAqC,CACrCsB,KAA4B,CAC5BC,SAAoB,CACpBC,iBAA6C,CACpC,CACT,IAAK,KAAM,CAAAgD,GAAG,GAAI,CAAAlD,KAAK,CAAE,CACvBtB,SAAS,CAACwE,GAAG,CAAC,CAAG5C,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAEF,aAAa,CAAC,CACjD3B,SAAS,CAACwE,GAAG,CAAC,CAACtD,OAAO,CAAGlB,SAAS,CAACkB,OAAO,CAE1ClB,SAAS,CAACwE,GAAG,CAAC,CAAC9B,OAAO,CAAGpB,KAAK,CAACkD,GAAG,CAAC,CACnCxE,SAAS,CAACwE,GAAG,CAAC,CAAC/B,OAAO,CAAIzC,SAAS,CAACyC,OAAO,CACzC+B,GAAG,CACJ,CACDxE,SAAS,CAACwE,GAAG,CAAC,CAACtD,OAAO,CACpBlB,SAAS,CAACwE,GAAG,CAAC,CACdlD,KAAK,CAACkD,GAAG,CAAC,CACVjD,SAAS,CACTC,iBAAiB,CAAGA,iBAAiB,CAACgD,GAAG,CAAC,CAAG9C,SAC/C,CAAC,CACH,CACA1B,SAAS,CAAC0C,OAAO,CAAGpB,KAAK,CAC3B,CAAC,CAED,KAAM,CAAAmE,aAAa,CAAG,QAAAA,CACpBzF,SAAqC,CACrCuB,SAAoB,CACR,CACZ,GAAI,CAAAwC,QAAQ,CAAG,IAAI,CACnB,KAAM,CAAA2B,SAAgC,CAAG,CAAC,CAAC,CAC3C,IAAK,KAAM,CAAAlB,GAAG,GAAI,CAAAxE,SAAS,CAAC0C,OAAO,CAA2B,CAC5D,KAAM,CAAAsB,MAAM,CAAGhE,SAAS,CAACwE,GAAG,CAAC,CAACpD,OAAO,CAACpB,SAAS,CAACwE,GAAG,CAAC,CAAEjD,SAAS,CAAC,CAEhEwC,QAAQ,CAAGA,QAAQ,EAAIC,MAAM,CAC7B0B,SAAS,CAAClB,GAAG,CAAC,CAAGxE,SAAS,CAACwE,GAAG,CAAC,CAAC9B,OAAO,CACzC,CACA1C,SAAS,CAAC0C,OAAO,CAAGgD,SAAS,CAC7B,MAAO,CAAA3B,QAAQ,CACjB,CAAC,CAED/D,SAAS,CAACkB,OAAO,CAAG,SAClBlB,SAAqC,CACrCsB,KAAa,CACbC,SAAoB,CACpBC,iBAA6C,CAC1C,CACH,GAAIxB,SAAS,CAACyB,YAAY,GAAKC,SAAS,CAAE,CACxC1B,SAAS,CAACyB,YAAY,CAAGxB,yBAAyB,CAAC,CAAC,CACtD,CACA,GAAID,SAAS,CAACyB,YAAY,CAAE,CAC1B,GAAIzB,SAAS,CAACyC,OAAO,GAAKf,SAAS,CAAE,CACnC1B,SAAS,CAAC0C,OAAO,CAAG1C,SAAS,CAACyC,OAAO,CACvC,CAAC,IAAM,CAELxB,WAAW,CAACjB,SAAS,CAAEsB,KAAK,CAAEC,SAAS,CAAEC,iBAAiB,CAAC,CAC7D,CACAxB,SAAS,CAAC2F,SAAS,CAAG,CAAC,CACvB3F,SAAS,CAACoB,OAAO,CAAG,iBAAM,KAAI,GAC9B,OACF,CACA,GAAIjB,OAAO,CAACmB,KAAK,CAAC,CAAE,CAClBgC,YAAY,CAACtD,SAAS,CAAEsB,KAAK,CAAEC,SAAS,CAAEC,iBAAiB,CAAC,CAC5DxB,SAAS,CAACoB,OAAO,CAAG0C,YAAY,CAChC,OACF,CAAC,IAAM,IAAI/C,kBAAkB,CAACO,KAAK,CAAC,CAAE,CACpC2C,2BAA2B,CACzBjE,SAAS,CACTsB,KAAK,CACLC,SAAS,CACTC,iBACF,CAAC,CACDxB,SAAS,CAACoB,OAAO,CAAGgD,2BAA2B,CAC/C,OACF,CAAC,IAAM,IAAIwB,KAAK,CAACC,OAAO,CAACvE,KAAK,CAAC,CAAE,CAC/B+D,YAAY,CAACrF,SAAS,CAAEsB,KAAK,CAAEC,SAAS,CAAEC,iBAAiB,CAAC,CAC5DxB,SAAS,CAACoB,OAAO,CAAGmE,YAAY,CAChC,OACF,CAAC,IAAM,IAAI,MAAO,CAAAjE,KAAK,GAAK,QAAQ,CAAE,CACpCS,qBAAqB,CAAC/B,SAAS,CAAEsB,KAAK,CAAEC,SAAS,CAAEC,iBAAiB,CAAC,CACrExB,SAAS,CAACoB,OAAO,CAAG6B,qBAAqB,CACzC,OACF,CAAC,IAAM,IAAI,MAAO,CAAA3B,KAAK,GAAK,QAAQ,EAAIA,KAAK,GAAK,IAAI,CAAE,CACtDkE,aAAa,CAACxF,SAAS,CAAEsB,KAAK,CAAEC,SAAS,CAAEC,iBAAiB,CAAC,CAC7DxB,SAAS,CAACoB,OAAO,CAAGqE,aAAa,CACjC,OACF,CACAxE,WAAW,CAACjB,SAAS,CAAEsB,KAAK,CAAEC,SAAS,CAAEC,iBAAiB,CAAC,CAC7D,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var decorateAnimation = function () {\n    var _e = [new global.Error(), -16, -27];\n    var decorateAnimation = function (animation) {\n      var baseOnStart = animation.onStart;\n      var baseOnFrame = animation.onFrame;\n      if (animation.isHigherOrder) {\n        animation.onStart = (animation, value, timestamp, previousAnimation) => {\n          if (animation.reduceMotion === undefined) {\n            animation.reduceMotion = getReduceMotionFromConfig();\n          }\n          return baseOnStart(animation, value, timestamp, previousAnimation);\n        };\n        return;\n      }\n      var animationCopy = Object.assign({}, animation);\n      delete animationCopy.callback;\n      var prefNumberSuffOnStart = (animation, value, timestamp, previousAnimation) => {\n        // recognize prefix, suffix, and updates stripped value on animation start\n        var _recognizePrefixSuffi = recognizePrefixSuffix(value),\n          prefix = _recognizePrefixSuffi.prefix,\n          suffix = _recognizePrefixSuffi.suffix,\n          strippedValue = _recognizePrefixSuffi.strippedValue;\n        animation.__prefix = prefix;\n        animation.__suffix = suffix;\n        animation.strippedCurrent = strippedValue;\n        var _recognizePrefixSuffi2 = recognizePrefixSuffix(animation.toValue),\n          strippedToValue = _recognizePrefixSuffi2.strippedValue;\n        animation.current = strippedValue;\n        animation.startValue = strippedValue;\n        animation.toValue = strippedToValue;\n        if (previousAnimation && previousAnimation !== animation) {\n          var _recognizePrefixSuffi3 = recognizePrefixSuffix(previousAnimation.current),\n            paPrefix = _recognizePrefixSuffi3.prefix,\n            paSuffix = _recognizePrefixSuffi3.suffix,\n            paStrippedValue = _recognizePrefixSuffi3.strippedValue;\n          previousAnimation.current = paStrippedValue;\n          previousAnimation.__prefix = paPrefix;\n          previousAnimation.__suffix = paSuffix;\n        }\n        baseOnStart(animation, strippedValue, timestamp, previousAnimation);\n        animation.current = (animation.__prefix ?? '') + animation.current + (animation.__suffix ?? '');\n        if (previousAnimation && previousAnimation !== animation) {\n          previousAnimation.current = (previousAnimation.__prefix ?? '') +\n          // FIXME\n          // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n          previousAnimation.current + (previousAnimation.__suffix ?? '');\n        }\n      };\n      var prefNumberSuffOnFrame = (animation, timestamp) => {\n        animation.current = animation.strippedCurrent;\n        var res = baseOnFrame(animation, timestamp);\n        animation.strippedCurrent = animation.current;\n        animation.current = (animation.__prefix ?? '') + animation.current + (animation.__suffix ?? '');\n        return res;\n      };\n      var tab = ['R', 'G', 'B', 'A'];\n      var colorOnStart = (animation, value, timestamp, previousAnimation) => {\n        var RGBAValue;\n        var RGBACurrent;\n        var RGBAToValue;\n        var res = [];\n        if ((0, _Colors.isColor)(value)) {\n          RGBACurrent = (0, _Colors.toLinearSpace)((0, _Colors.convertToRGBA)(animation.current));\n          RGBAValue = (0, _Colors.toLinearSpace)((0, _Colors.convertToRGBA)(value));\n          if (animation.toValue) {\n            RGBAToValue = (0, _Colors.toLinearSpace)((0, _Colors.convertToRGBA)(animation.toValue));\n          }\n        }\n        tab.forEach((i, index) => {\n          animation[i] = Object.assign({}, animationCopy);\n          animation[i].current = RGBACurrent[index];\n          animation[i].toValue = RGBAToValue ? RGBAToValue[index] : undefined;\n          animation[i].onStart(animation[i], RGBAValue[index], timestamp, previousAnimation ? previousAnimation[i] : undefined);\n          res.push(animation[i].current);\n        });\n\n        // We need to clamp the res values to make sure they are in the correct RGBA range\n        (0, _Colors.clampRGBA)(res);\n        animation.current = (0, _Colors.rgbaArrayToRGBAColor)((0, _Colors.toGammaSpace)(res));\n      };\n      var colorOnFrame = (animation, timestamp) => {\n        var RGBACurrent = (0, _Colors.toLinearSpace)((0, _Colors.convertToRGBA)(animation.current));\n        var res = [];\n        var finished = true;\n        tab.forEach((i, index) => {\n          animation[i].current = RGBACurrent[index];\n          var result = animation[i].onFrame(animation[i], timestamp);\n          // We really need to assign this value to result, instead of passing it directly - otherwise once \"finished\" is false, onFrame won't be called\n          finished = finished && result;\n          res.push(animation[i].current);\n        });\n\n        // We need to clamp the res values to make sure they are in the correct RGBA range\n        (0, _Colors.clampRGBA)(res);\n        animation.current = (0, _Colors.rgbaArrayToRGBAColor)((0, _Colors.toGammaSpace)(res));\n        return finished;\n      };\n      var transformationMatrixOnStart = (animation, value, timestamp, previousAnimation) => {\n        var toValue = animation.toValue;\n        animation.startMatrices = (0, _matrixUtils.decomposeMatrixIntoMatricesAndAngles)(value);\n        animation.stopMatrices = (0, _matrixUtils.decomposeMatrixIntoMatricesAndAngles)(toValue);\n\n        // We create an animation copy to animate single value between 0 and 100\n        // We set limits from 0 to 100 (instead of 0-1) to make spring look good\n        // with default thresholds.\n\n        animation[0] = Object.assign({}, animationCopy);\n        animation[0].current = 0;\n        animation[0].toValue = 100;\n        animation[0].onStart(animation[0], 0, timestamp, previousAnimation ? previousAnimation[0] : undefined);\n        animation.current = value;\n      };\n      var transformationMatrixOnFrame = (animation, timestamp) => {\n        var finished = true;\n        var result = animation[0].onFrame(animation[0], timestamp);\n        // We really need to assign this value to result, instead of passing it directly - otherwise once \"finished\" is false, onFrame won't be called\n        finished = finished && result;\n        var progress = animation[0].current / 100;\n        var transforms = ['translationMatrix', 'scaleMatrix', 'skewMatrix'];\n        var mappedTransforms = [];\n        transforms.forEach((key, _) => mappedTransforms.push(applyProgressToMatrix(progress, animation.startMatrices[key], animation.stopMatrices[key])));\n        var currentTranslation = mappedTransforms[0],\n          currentScale = mappedTransforms[1],\n          skewMatrix = mappedTransforms[2];\n        var rotations = ['x', 'y', 'z'];\n        var mappedRotations = [];\n        rotations.forEach((key, _) => {\n          var angle = applyProgressToNumber(progress, animation.startMatrices['r' + key], animation.stopMatrices['r' + key]);\n          mappedRotations.push((0, _matrixUtils.getRotationMatrix)(angle, key));\n        });\n        var rotationMatrixX = mappedRotations[0],\n          rotationMatrixY = mappedRotations[1],\n          rotationMatrixZ = mappedRotations[2];\n        var rotationMatrix = (0, _matrixUtils.multiplyMatrices)(rotationMatrixX, (0, _matrixUtils.multiplyMatrices)(rotationMatrixY, rotationMatrixZ));\n        var updated = (0, _matrixUtils.flatten)((0, _matrixUtils.multiplyMatrices)((0, _matrixUtils.multiplyMatrices)(currentScale, (0, _matrixUtils.multiplyMatrices)(skewMatrix, rotationMatrix)), currentTranslation));\n        animation.current = updated;\n        return finished;\n      };\n      var arrayOnStart = (animation, value, timestamp, previousAnimation) => {\n        value.forEach((v, i) => {\n          animation[i] = Object.assign({}, animationCopy);\n          animation[i].current = v;\n          animation[i].toValue = animation.toValue[i];\n          animation[i].onStart(animation[i], v, timestamp, previousAnimation ? previousAnimation[i] : undefined);\n        });\n        animation.current = [...value];\n      };\n      var arrayOnFrame = (animation, timestamp) => {\n        var finished = true;\n        animation.current.forEach((_, i) => {\n          var result = animation[i].onFrame(animation[i], timestamp);\n          // We really need to assign this value to result, instead of passing it directly - otherwise once \"finished\" is false, onFrame won't be called\n          finished = finished && result;\n          animation.current[i] = animation[i].current;\n        });\n        return finished;\n      };\n      var objectOnStart = (animation, value, timestamp, previousAnimation) => {\n        for (var key in value) {\n          animation[key] = Object.assign({}, animationCopy);\n          animation[key].onStart = animation.onStart;\n          animation[key].current = value[key];\n          animation[key].toValue = animation.toValue[key];\n          animation[key].onStart(animation[key], value[key], timestamp, previousAnimation ? previousAnimation[key] : undefined);\n        }\n        animation.current = value;\n      };\n      var objectOnFrame = (animation, timestamp) => {\n        var finished = true;\n        var newObject = {};\n        for (var key in animation.current) {\n          var result = animation[key].onFrame(animation[key], timestamp);\n          // We really need to assign this value to result, instead of passing it directly - otherwise once \"finished\" is false, onFrame won't be called\n          finished = finished && result;\n          newObject[key] = animation[key].current;\n        }\n        animation.current = newObject;\n        return finished;\n      };\n      animation.onStart = (animation, value, timestamp, previousAnimation) => {\n        if (animation.reduceMotion === undefined) {\n          animation.reduceMotion = getReduceMotionFromConfig();\n        }\n        if (animation.reduceMotion) {\n          if (animation.toValue !== undefined) {\n            animation.current = animation.toValue;\n          } else {\n            // if there is no `toValue`, then the base function is responsible for setting the current value\n            baseOnStart(animation, value, timestamp, previousAnimation);\n          }\n          animation.startTime = 0;\n          animation.onFrame = () => true;\n          return;\n        }\n        if ((0, _Colors.isColor)(value)) {\n          colorOnStart(animation, value, timestamp, previousAnimation);\n          animation.onFrame = colorOnFrame;\n          return;\n        } else if ((0, _matrixUtils.isAffineMatrixFlat)(value)) {\n          transformationMatrixOnStart(animation, value, timestamp, previousAnimation);\n          animation.onFrame = transformationMatrixOnFrame;\n          return;\n        } else if (Array.isArray(value)) {\n          arrayOnStart(animation, value, timestamp, previousAnimation);\n          animation.onFrame = arrayOnFrame;\n          return;\n        } else if (typeof value === 'string') {\n          prefNumberSuffOnStart(animation, value, timestamp, previousAnimation);\n          animation.onFrame = prefNumberSuffOnFrame;\n          return;\n        } else if (typeof value === 'object' && value !== null) {\n          objectOnStart(animation, value, timestamp, previousAnimation);\n          animation.onFrame = objectOnFrame;\n          return;\n        }\n        baseOnStart(animation, value, timestamp, previousAnimation);\n      };\n    };\n    decorateAnimation.__closure = {\n      getReduceMotionFromConfig,\n      recognizePrefixSuffix,\n      isColor: _Colors.isColor,\n      toLinearSpace: _Colors.toLinearSpace,\n      convertToRGBA: _Colors.convertToRGBA,\n      clampRGBA: _Colors.clampRGBA,\n      rgbaArrayToRGBAColor: _Colors.rgbaArrayToRGBAColor,\n      toGammaSpace: _Colors.toGammaSpace,\n      decomposeMatrixIntoMatricesAndAngles: _matrixUtils.decomposeMatrixIntoMatricesAndAngles,\n      applyProgressToMatrix,\n      applyProgressToNumber,\n      getRotationMatrix: _matrixUtils.getRotationMatrix,\n      multiplyMatrices: _matrixUtils.multiplyMatrices,\n      flatten: _matrixUtils.flatten,\n      isAffineMatrixFlat: _matrixUtils.isAffineMatrixFlat\n    };\n    decorateAnimation.__workletHash = 1842068436516;\n    decorateAnimation.__initData = _worklet_1842068436516_init_data;\n    decorateAnimation.__stackDetails = _e;\n    return decorateAnimation;\n  }();\n  var _worklet_8207093411086_init_data = {\n    code: \"function defineAnimation_utilTs9(starting,factory){const{IN_STYLE_UPDATER,decorateAnimation,SHOULD_BE_USE_WEB}=this.__closure;if(IN_STYLE_UPDATER){return starting;}const create=function(){'worklet';const animation=factory();decorateAnimation(animation);return animation;};if(_WORKLET||SHOULD_BE_USE_WEB){return create();}create.__isAnimationDefinition=true;return create;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"defineAnimation_utilTs9\\\",\\\"starting\\\",\\\"factory\\\",\\\"IN_STYLE_UPDATER\\\",\\\"decorateAnimation\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"__closure\\\",\\\"create\\\",\\\"animation\\\",\\\"_WORKLET\\\",\\\"__isAnimationDefinition\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AAyhBO,SAAAA,uBAGLA,CAAAC,QAAuC,CAAgBC,OAAK,QAAAC,gBAAA,CAAAC,iBAAA,CAAAC,iBAAA,OAAAC,SAAA,CAE5D,GAAIH,gBAAgB,CAAE,CACpB,MAAO,CAAAF,QAAQ,CACjB,CACA,KAAM,CAAAM,MAAM,CAAG,QAAAA,CAAA,CAAM,CACnB,SAAS,CACT,KAAM,CAAAC,SAAS,CAAGN,OAAO,CAAC,CAAC,CAC3BE,iBAAiB,CAAII,SAAyB,CAAC,CAC/C,MAAO,CAAAA,SAAS,CAClB,CAAC,CAED,GAAIC,QAAQ,EAAIJ,iBAAiB,CAAE,CACjC,MAAO,CAAAE,MAAM,CAAC,CAAC,CACjB,CACAA,MAAM,CAACG,uBAAuB,CAAG,IAAI,CAGrC,MAAO,CAAAH,MAAM,CACf\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_10854716983407_init_data = {\n    code: \"function utilTs10(){const{factory,decorateAnimation}=this.__closure;const animation=factory();decorateAnimation(animation);return animation;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"utilTs10\\\",\\\"factory\\\",\\\"decorateAnimation\\\",\\\"__closure\\\",\\\"animation\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AAiiBiB,SAAAA,QAAMA,CAAA,QAAAC,OAAA,CAAAC,iBAAA,OAAAC,SAAA,CAEnB,KAAM,CAAAC,SAAS,CAAGH,OAAO,CAAC,CAAC,CAC3BC,iBAAiB,CAAIE,SAAyB,CAAC,CAC/C,MAAO,CAAAA,SAAS,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var defineAnimation = exports.defineAnimation = function () {\n    var _e = [new global.Error(), -4, -27];\n    var defineAnimation = function (starting, factory) {\n      if (IN_STYLE_UPDATER) {\n        return starting;\n      }\n      var create = function () {\n        var _e = [new global.Error(), -3, -27];\n        var utilTs10 = function () {\n          var animation = factory();\n          decorateAnimation(animation);\n          return animation;\n        };\n        utilTs10.__closure = {\n          factory,\n          decorateAnimation\n        };\n        utilTs10.__workletHash = 10854716983407;\n        utilTs10.__initData = _worklet_10854716983407_init_data;\n        utilTs10.__stackDetails = _e;\n        return utilTs10;\n      }();\n      if (_WORKLET || SHOULD_BE_USE_WEB) {\n        return create();\n      }\n      create.__isAnimationDefinition = true;\n\n      // @ts-expect-error it's fine\n      return create;\n    };\n    defineAnimation.__closure = {\n      IN_STYLE_UPDATER,\n      decorateAnimation,\n      SHOULD_BE_USE_WEB\n    };\n    defineAnimation.__workletHash = 8207093411086;\n    defineAnimation.__initData = _worklet_8207093411086_init_data;\n    defineAnimation.__stackDetails = _e;\n    return defineAnimation;\n  }();\n  var _worklet_6696318663002_init_data = {\n    code: \"function cancelAnimationNative_utilTs11(sharedValue){const{runOnUI}=this.__closure;if(_WORKLET){sharedValue.value=sharedValue.value;}else{runOnUI(function(){'worklet';sharedValue.value=sharedValue.value;})();}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"cancelAnimationNative_utilTs11\\\",\\\"sharedValue\\\",\\\"runOnUI\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AAijBA,SAAAA,8BAAuCA,CAAAC,WAAwC,QAAAC,OAAA,OAAAC,SAAA,CAG7E,GAAIC,QAAQ,CAAE,CACZH,WAAW,CAACI,KAAK,CAAGJ,WAAW,CAACI,KAAK,CACvC,CAAC,IAAM,CACLH,OAAO,CAAC,UAAM,CACZ,SAAS,CACTD,WAAW,CAACI,KAAK,CAAGJ,WAAW,CAACI,KAAK,CACvC,CAAC,CAAC,CAAC,CAAC,CACN,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_1589476284506_init_data = {\n    code: \"function utilTs12(){const{sharedValue}=this.__closure;sharedValue.value=sharedValue.value;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\util.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"utilTs12\\\",\\\"sharedValue\\\",\\\"__closure\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/util.ts\\\"],\\\"mappings\\\":\\\"AAujBY,SAAAA,QAAMA,CAAA,QAAAC,WAAA,OAAAC,SAAA,CAEZD,WAAW,CAACE,KAAK,CAAGF,WAAW,CAACE,KAAK,CACvC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var cancelAnimationNative = function () {\n    var _e = [new global.Error(), -2, -27];\n    var cancelAnimationNative = function (sharedValue) {\n      // setting the current value cancels the animation if one is currently running\n      if (_WORKLET) {\n        sharedValue.value = sharedValue.value; // eslint-disable-line no-self-assign\n      } else {\n        (0, _threads.runOnUI)(function () {\n          var _e = [new global.Error(), -2, -27];\n          var utilTs12 = function () {\n            sharedValue.value = sharedValue.value; // eslint-disable-line no-self-assign\n          };\n          utilTs12.__closure = {\n            sharedValue\n          };\n          utilTs12.__workletHash = 1589476284506;\n          utilTs12.__initData = _worklet_1589476284506_init_data;\n          utilTs12.__stackDetails = _e;\n          return utilTs12;\n        }())();\n      }\n    };\n    cancelAnimationNative.__closure = {\n      runOnUI: _threads.runOnUI\n    };\n    cancelAnimationNative.__workletHash = 6696318663002;\n    cancelAnimationNative.__initData = _worklet_6696318663002_init_data;\n    cancelAnimationNative.__stackDetails = _e;\n    return cancelAnimationNative;\n  }();\n  function cancelAnimationWeb(sharedValue) {\n    // setting the current value cancels the animation if one is currently running\n    sharedValue.value = sharedValue.value; // eslint-disable-line no-self-assign\n  }\n\n  /**\n   * Lets you cancel a running animation paired to a shared value. The\n   * cancellation is asynchronous.\n   *\n   * @param sharedValue - The shared value of a running animation that you want to\n   *   cancel.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/core/cancelAnimation\n   */\n  var cancelAnimation = exports.cancelAnimation = SHOULD_BE_USE_WEB ? cancelAnimationWeb : cancelAnimationNative;\n});", "lineCount": 575, "map": [[2, 2, 1, 0], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13, "Object"], [5, 8, 2, 13], [5, 9, 2, 13, "defineProperty"], [5, 23, 2, 13], [5, 24, 2, 13, "exports"], [5, 31, 2, 13], [6, 4, 2, 13, "value"], [6, 9, 2, 13], [7, 2, 2, 13], [8, 2, 2, 13, "exports"], [8, 9, 2, 13], [8, 10, 2, 13, "getReduceMotionFromConfig"], [8, 35, 2, 13], [8, 38, 2, 13, "exports"], [8, 45, 2, 13], [8, 46, 2, 13, "getReduceMotionForAnimation"], [8, 73, 2, 13], [8, 76, 2, 13, "exports"], [8, 83, 2, 13], [8, 84, 2, 13, "defineAnimation"], [8, 99, 2, 13], [8, 102, 2, 13, "exports"], [8, 109, 2, 13], [8, 110, 2, 13, "cancelAnimation"], [8, 125, 2, 13], [8, 128, 2, 13, "exports"], [8, 135, 2, 13], [8, 136, 2, 13, "assertEasingIsWorklet"], [8, 157, 2, 13], [9, 2, 2, 13, "exports"], [9, 9, 2, 13], [9, 10, 2, 13, "initialUpdaterRun"], [9, 27, 2, 13], [9, 30, 2, 13, "initialUpdaterRun"], [9, 47, 2, 13], [10, 2, 2, 13, "exports"], [10, 9, 2, 13], [10, 10, 2, 13, "recognizePrefixSuffix"], [10, 31, 2, 13], [10, 34, 2, 13, "exports"], [10, 41, 2, 13], [10, 42, 2, 13, "isValidLayoutAnimationProp"], [10, 68, 2, 13], [11, 2, 4, 0], [11, 6, 4, 0, "_Colors"], [11, 13, 4, 0], [11, 16, 4, 0, "require"], [11, 23, 4, 0], [11, 24, 4, 0, "_dependencyMap"], [11, 38, 4, 0], [12, 2, 21, 0], [12, 6, 21, 0, "_commonTypes"], [12, 18, 21, 0], [12, 21, 21, 0, "require"], [12, 28, 21, 0], [12, 29, 21, 0, "_dependencyMap"], [12, 43, 21, 0], [13, 2, 23, 0], [13, 6, 23, 0, "_errors"], [13, 13, 23, 0], [13, 16, 23, 0, "require"], [13, 23, 23, 0], [13, 24, 23, 0, "_dependencyMap"], [13, 38, 23, 0], [14, 2, 24, 0], [14, 6, 24, 0, "_logger"], [14, 13, 24, 0], [14, 16, 24, 0, "require"], [14, 23, 24, 0], [14, 24, 24, 0, "_dependencyMap"], [14, 38, 24, 0], [15, 2, 25, 0], [15, 6, 25, 0, "_PlatformChecker"], [15, 22, 25, 0], [15, 25, 25, 0, "require"], [15, 32, 25, 0], [15, 33, 25, 0, "_dependencyMap"], [15, 47, 25, 0], [16, 2, 26, 0], [16, 6, 26, 0, "_ReducedMotion"], [16, 20, 26, 0], [16, 23, 26, 0, "require"], [16, 30, 26, 0], [16, 31, 26, 0, "_dependencyMap"], [16, 45, 26, 0], [17, 2, 27, 0], [17, 6, 27, 0, "_threads"], [17, 14, 27, 0], [17, 17, 27, 0, "require"], [17, 24, 27, 0], [17, 25, 27, 0, "_dependencyMap"], [17, 39, 27, 0], [18, 2, 33, 0], [18, 6, 33, 0, "_matrixUtils"], [18, 18, 33, 0], [18, 21, 33, 0, "require"], [18, 28, 33, 0], [18, 29, 33, 0, "_dependencyMap"], [18, 43, 33, 0], [19, 2, 44, 0], [19, 6, 44, 4, "IN_STYLE_UPDATER"], [19, 22, 44, 20], [19, 25, 44, 23], [19, 30, 44, 28], [20, 2, 45, 0], [20, 6, 45, 6, "SHOULD_BE_USE_WEB"], [20, 23, 45, 23], [20, 26, 45, 26], [20, 30, 45, 26, "shouldBeUseWeb"], [20, 61, 45, 40], [20, 63, 45, 41], [20, 64, 45, 42], [21, 2, 47, 0], [21, 6, 47, 6, "LAYOUT_ANIMATION_SUPPORTED_PROPS"], [21, 38, 47, 38], [21, 41, 47, 41], [22, 4, 48, 2, "originX"], [22, 11, 48, 9], [22, 13, 48, 11], [22, 17, 48, 15], [23, 4, 49, 2, "originY"], [23, 11, 49, 9], [23, 13, 49, 11], [23, 17, 49, 15], [24, 4, 50, 2, "width"], [24, 9, 50, 7], [24, 11, 50, 9], [24, 15, 50, 13], [25, 4, 51, 2, "height"], [25, 10, 51, 8], [25, 12, 51, 10], [25, 16, 51, 14], [26, 4, 52, 2, "borderRadius"], [26, 16, 52, 14], [26, 18, 52, 16], [26, 22, 52, 20], [27, 4, 53, 2, "globalOriginX"], [27, 17, 53, 15], [27, 19, 53, 17], [27, 23, 53, 21], [28, 4, 54, 2, "globalOriginY"], [28, 17, 54, 15], [28, 19, 54, 17], [28, 23, 54, 21], [29, 4, 55, 2, "opacity"], [29, 11, 55, 9], [29, 13, 55, 11], [29, 17, 55, 15], [30, 4, 56, 2, "transform"], [30, 13, 56, 11], [30, 15, 56, 13], [31, 2, 57, 0], [31, 3, 57, 1], [32, 2, 57, 2], [32, 6, 57, 2, "_worklet_16718747144056_init_data"], [32, 39, 57, 2], [33, 4, 57, 2, "code"], [33, 8, 57, 2], [34, 4, 57, 2, "location"], [34, 12, 57, 2], [35, 4, 57, 2, "sourceMap"], [35, 13, 57, 2], [36, 4, 57, 2, "version"], [36, 11, 57, 2], [37, 2, 57, 2], [38, 2, 57, 2], [38, 6, 57, 2, "isValidLayoutAnimationProp"], [38, 32, 57, 2], [38, 35, 57, 2, "exports"], [38, 42, 57, 2], [38, 43, 57, 2, "isValidLayoutAnimationProp"], [38, 69, 57, 2], [38, 72, 61, 7], [39, 4, 61, 7], [39, 8, 61, 7, "_e"], [39, 10, 61, 7], [39, 18, 61, 7, "global"], [39, 24, 61, 7], [39, 25, 61, 7, "Error"], [39, 30, 61, 7], [40, 4, 61, 7], [40, 8, 61, 7, "isValidLayoutAnimationProp"], [40, 34, 61, 7], [40, 46, 61, 7, "isValidLayoutAnimationProp"], [40, 47, 61, 43, "prop"], [40, 51, 61, 55], [40, 53, 61, 57], [41, 6, 63, 2], [41, 13, 63, 10, "prop"], [41, 17, 63, 14], [41, 21, 63, 42, "LAYOUT_ANIMATION_SUPPORTED_PROPS"], [41, 53, 63, 74], [42, 4, 64, 0], [42, 5, 64, 1], [43, 4, 64, 1, "isValidLayoutAnimationProp"], [43, 30, 64, 1], [43, 31, 64, 1, "__closure"], [43, 40, 64, 1], [44, 6, 64, 1, "LAYOUT_ANIMATION_SUPPORTED_PROPS"], [45, 4, 64, 1], [46, 4, 64, 1, "isValidLayoutAnimationProp"], [46, 30, 64, 1], [46, 31, 64, 1, "__workletHash"], [46, 44, 64, 1], [47, 4, 64, 1, "isValidLayoutAnimationProp"], [47, 30, 64, 1], [47, 31, 64, 1, "__initData"], [47, 41, 64, 1], [47, 44, 64, 1, "_worklet_16718747144056_init_data"], [47, 77, 64, 1], [48, 4, 64, 1, "isValidLayoutAnimationProp"], [48, 30, 64, 1], [48, 31, 64, 1, "__stackDetails"], [48, 45, 64, 1], [48, 48, 64, 1, "_e"], [48, 50, 64, 1], [49, 4, 64, 1], [49, 11, 64, 1, "isValidLayoutAnimationProp"], [49, 37, 64, 1], [50, 2, 64, 1], [50, 3, 61, 7], [51, 2, 66, 0], [51, 6, 66, 4, "__DEV__"], [51, 13, 66, 11], [51, 17, 66, 15, "ReducedMotionManager"], [51, 52, 66, 35], [51, 53, 66, 36, "jsValue"], [51, 60, 66, 43], [51, 62, 66, 45], [52, 4, 67, 2, "logger"], [52, 18, 67, 8], [52, 19, 67, 9, "warn"], [52, 23, 67, 13], [52, 24, 68, 4], [52, 364, 69, 2], [52, 365, 69, 3], [53, 2, 70, 0], [54, 2, 70, 1], [54, 6, 70, 1, "_worklet_11513914120603_init_data"], [54, 39, 70, 1], [55, 4, 70, 1, "code"], [55, 8, 70, 1], [56, 4, 70, 1, "location"], [56, 12, 70, 1], [57, 4, 70, 1, "sourceMap"], [57, 13, 70, 1], [58, 4, 70, 1, "version"], [58, 11, 70, 1], [59, 2, 70, 1], [60, 2, 70, 1], [60, 6, 70, 1, "assertEasingIsWorklet"], [60, 27, 70, 1], [60, 30, 70, 1, "exports"], [60, 37, 70, 1], [60, 38, 70, 1, "assertEasingIsWorklet"], [60, 59, 70, 1], [60, 62, 72, 7], [61, 4, 72, 7], [61, 8, 72, 7, "_e"], [61, 10, 72, 7], [61, 18, 72, 7, "global"], [61, 24, 72, 7], [61, 25, 72, 7, "Error"], [61, 30, 72, 7], [62, 4, 72, 7], [62, 8, 72, 7, "assertEasingIsWorklet"], [62, 29, 72, 7], [62, 41, 72, 7, "assertEasingIsWorklet"], [62, 42, 73, 2, "easing"], [62, 48, 73, 48], [62, 50, 74, 8], [63, 6, 76, 2], [63, 10, 76, 6, "_WORKLET"], [63, 18, 76, 14], [63, 20, 76, 16], [64, 8, 77, 4], [65, 8, 78, 4], [66, 8, 79, 4], [67, 6, 80, 2], [68, 6, 81, 2], [68, 10, 81, 6, "SHOULD_BE_USE_WEB"], [68, 27, 81, 23], [68, 29, 81, 25], [69, 8, 82, 4], [70, 8, 83, 4], [71, 6, 84, 2], [72, 6, 85, 2], [73, 6, 86, 2], [73, 10, 86, 6, "easing"], [73, 16, 86, 12], [73, 18, 86, 14, "factory"], [73, 25, 86, 21], [73, 27, 86, 23], [74, 8, 87, 4], [75, 6, 88, 2], [76, 6, 90, 2], [76, 10, 90, 6], [76, 11, 90, 7], [76, 15, 90, 7, "isWorkletFunction"], [76, 45, 90, 24], [76, 47, 90, 25, "easing"], [76, 53, 90, 31], [76, 54, 90, 32], [76, 56, 90, 34], [77, 8, 91, 4], [77, 14, 91, 10], [77, 18, 91, 14, "ReanimatedError"], [77, 41, 91, 29], [77, 42, 92, 6], [77, 148, 93, 4], [77, 149, 93, 5], [78, 6, 94, 2], [79, 4, 95, 0], [79, 5, 95, 1], [80, 4, 95, 1, "assertEasingIsWorklet"], [80, 25, 95, 1], [80, 26, 95, 1, "__closure"], [80, 35, 95, 1], [81, 6, 95, 1, "SHOULD_BE_USE_WEB"], [81, 23, 95, 1], [82, 6, 95, 1, "isWorkletFunction"], [82, 23, 95, 1], [82, 25, 90, 7, "isWorkletFunction"], [83, 4, 90, 24], [84, 4, 90, 24, "assertEasingIsWorklet"], [84, 25, 90, 24], [84, 26, 90, 24, "__workletHash"], [84, 39, 90, 24], [85, 4, 90, 24, "assertEasingIsWorklet"], [85, 25, 90, 24], [85, 26, 90, 24, "__initData"], [85, 36, 90, 24], [85, 39, 90, 24, "_worklet_11513914120603_init_data"], [85, 72, 90, 24], [86, 4, 90, 24, "assertEasingIsWorklet"], [86, 25, 90, 24], [86, 26, 90, 24, "__stackDetails"], [86, 40, 90, 24], [86, 43, 90, 24, "_e"], [86, 45, 90, 24], [87, 4, 90, 24], [87, 11, 90, 24, "assertEasingIsWorklet"], [87, 32, 90, 24], [88, 2, 90, 24], [88, 3, 72, 7], [89, 2, 97, 7], [89, 11, 97, 16, "initialUpdaterRun"], [89, 28, 97, 33, "initialUpdaterRun"], [89, 29, 97, 37, "updater"], [89, 36, 97, 53], [89, 38, 97, 55], [90, 4, 98, 2, "IN_STYLE_UPDATER"], [90, 20, 98, 18], [90, 23, 98, 21], [90, 27, 98, 25], [91, 4, 99, 2], [91, 8, 99, 8, "result"], [91, 14, 99, 14], [91, 17, 99, 17, "updater"], [91, 24, 99, 24], [91, 25, 99, 25], [91, 26, 99, 26], [92, 4, 100, 2, "IN_STYLE_UPDATER"], [92, 20, 100, 18], [92, 23, 100, 21], [92, 28, 100, 26], [93, 4, 101, 2], [93, 11, 101, 9, "result"], [93, 17, 101, 15], [94, 2, 102, 0], [95, 2, 102, 1], [95, 6, 102, 1, "_worklet_10505711737785_init_data"], [95, 39, 102, 1], [96, 4, 102, 1, "code"], [96, 8, 102, 1], [97, 4, 102, 1, "location"], [97, 12, 102, 1], [98, 4, 102, 1, "sourceMap"], [98, 13, 102, 1], [99, 4, 102, 1, "version"], [99, 11, 102, 1], [100, 2, 102, 1], [101, 2, 102, 1], [101, 6, 102, 1, "recognizePrefixSuffix"], [101, 27, 102, 1], [101, 30, 102, 1, "exports"], [101, 37, 102, 1], [101, 38, 102, 1, "recognizePrefixSuffix"], [101, 59, 102, 1], [101, 62, 110, 7], [102, 4, 110, 7], [102, 8, 110, 7, "_e"], [102, 10, 110, 7], [102, 18, 110, 7, "global"], [102, 24, 110, 7], [102, 25, 110, 7, "Error"], [102, 30, 110, 7], [103, 4, 110, 7], [103, 8, 110, 7, "recognizePrefixSuffix"], [103, 29, 110, 7], [103, 41, 110, 7, "recognizePrefixSuffix"], [103, 42, 111, 2, "value"], [103, 47, 111, 24], [103, 49, 112, 26], [104, 6, 114, 2], [104, 10, 114, 6], [104, 17, 114, 13, "value"], [104, 22, 114, 18], [104, 27, 114, 23], [104, 35, 114, 31], [104, 37, 114, 33], [105, 8, 115, 4], [105, 12, 115, 10, "match"], [105, 17, 115, 15], [105, 20, 115, 18, "value"], [105, 25, 115, 23], [105, 26, 115, 24, "match"], [105, 31, 115, 29], [105, 32, 116, 6], [105, 88, 117, 4], [105, 89, 117, 5], [106, 8, 118, 4], [106, 12, 118, 8], [106, 13, 118, 9, "match"], [106, 18, 118, 14], [106, 20, 118, 16], [107, 10, 119, 6], [107, 16, 119, 12], [107, 20, 119, 16, "ReanimatedError"], [107, 43, 119, 31], [107, 44, 119, 32], [107, 77, 119, 65], [107, 78, 119, 66], [108, 8, 120, 4], [109, 8, 121, 4], [109, 12, 121, 10, "prefix"], [109, 18, 121, 16], [109, 21, 121, 19, "match"], [109, 26, 121, 24], [109, 27, 121, 25], [109, 28, 121, 26], [109, 29, 121, 27], [110, 8, 122, 4], [110, 12, 122, 10, "suffix"], [110, 18, 122, 16], [110, 21, 122, 19, "match"], [110, 26, 122, 24], [110, 27, 122, 25], [110, 28, 122, 26], [110, 29, 122, 27], [111, 8, 123, 4], [112, 8, 124, 4], [112, 12, 124, 10, "number"], [112, 18, 124, 16], [112, 21, 124, 19, "match"], [112, 26, 124, 24], [112, 27, 124, 25], [112, 28, 124, 26], [112, 29, 124, 27], [112, 33, 124, 31, "match"], [112, 38, 124, 36], [112, 39, 124, 37], [112, 40, 124, 38], [112, 41, 124, 39], [112, 45, 124, 43], [112, 47, 124, 45], [112, 48, 124, 46], [113, 8, 125, 4], [113, 15, 125, 11], [114, 10, 125, 13, "prefix"], [114, 16, 125, 19], [115, 10, 125, 21, "suffix"], [115, 16, 125, 27], [116, 10, 125, 29, "strippedValue"], [116, 23, 125, 42], [116, 25, 125, 44, "parseFloat"], [116, 35, 125, 54], [116, 36, 125, 55, "number"], [116, 42, 125, 61], [117, 8, 125, 63], [117, 9, 125, 64], [118, 6, 126, 2], [118, 7, 126, 3], [118, 13, 126, 9], [119, 8, 127, 4], [119, 15, 127, 11], [120, 10, 127, 13, "strippedValue"], [120, 23, 127, 26], [120, 25, 127, 28, "value"], [121, 8, 127, 34], [121, 9, 127, 35], [122, 6, 128, 2], [123, 4, 129, 0], [123, 5, 129, 1], [124, 4, 129, 1, "recognizePrefixSuffix"], [124, 25, 129, 1], [124, 26, 129, 1, "__closure"], [124, 35, 129, 1], [125, 4, 129, 1, "recognizePrefixSuffix"], [125, 25, 129, 1], [125, 26, 129, 1, "__workletHash"], [125, 39, 129, 1], [126, 4, 129, 1, "recognizePrefixSuffix"], [126, 25, 129, 1], [126, 26, 129, 1, "__initData"], [126, 36, 129, 1], [126, 39, 129, 1, "_worklet_10505711737785_init_data"], [126, 72, 129, 1], [127, 4, 129, 1, "recognizePrefixSuffix"], [127, 25, 129, 1], [127, 26, 129, 1, "__stackDetails"], [127, 40, 129, 1], [127, 43, 129, 1, "_e"], [127, 45, 129, 1], [128, 4, 129, 1], [128, 11, 129, 1, "recognizePrefixSuffix"], [128, 32, 129, 1], [129, 2, 129, 1], [129, 3, 110, 7], [130, 2, 131, 0], [131, 0, 132, 0], [132, 0, 133, 0], [133, 0, 134, 0], [134, 2, 135, 0], [134, 6, 135, 6, "isReduceMotionOnUI"], [134, 24, 135, 24], [134, 27, 135, 27, "ReducedMotionManager"], [134, 62, 135, 47], [134, 63, 135, 48, "uiValue"], [134, 70, 135, 55], [135, 2, 135, 56], [135, 6, 135, 56, "_worklet_7714090217876_init_data"], [135, 38, 135, 56], [136, 4, 135, 56, "code"], [136, 8, 135, 56], [137, 4, 135, 56, "location"], [137, 12, 135, 56], [138, 4, 135, 56, "sourceMap"], [138, 13, 135, 56], [139, 4, 135, 56, "version"], [139, 11, 135, 56], [140, 2, 135, 56], [141, 2, 135, 56], [141, 6, 135, 56, "getReduceMotionFromConfig"], [141, 31, 135, 56], [141, 34, 135, 56, "exports"], [141, 41, 135, 56], [141, 42, 135, 56, "getReduceMotionFromConfig"], [141, 67, 135, 56], [141, 70, 136, 7], [142, 4, 136, 7], [142, 8, 136, 7, "_e"], [142, 10, 136, 7], [142, 18, 136, 7, "global"], [142, 24, 136, 7], [142, 25, 136, 7, "Error"], [142, 30, 136, 7], [143, 4, 136, 7], [143, 8, 136, 7, "getReduceMotionFromConfig"], [143, 33, 136, 7], [143, 45, 136, 7, "getReduceMotionFromConfig"], [143, 46, 136, 42, "config"], [143, 52, 136, 63], [143, 54, 136, 65], [144, 6, 138, 2], [144, 13, 138, 9], [144, 14, 138, 10, "config"], [144, 20, 138, 16], [144, 24, 138, 20, "config"], [144, 30, 138, 26], [144, 35, 138, 31, "ReduceMotion"], [144, 60, 138, 43], [144, 61, 138, 44, "System"], [144, 67, 138, 50], [144, 70, 139, 6, "isReduceMotionOnUI"], [144, 88, 139, 24], [144, 89, 139, 25, "value"], [144, 94, 139, 30], [144, 97, 140, 6, "config"], [144, 103, 140, 12], [144, 108, 140, 17, "ReduceMotion"], [144, 133, 140, 29], [144, 134, 140, 30, "Always"], [144, 140, 140, 36], [145, 4, 141, 0], [145, 5, 141, 1], [146, 4, 141, 1, "getReduceMotionFromConfig"], [146, 29, 141, 1], [146, 30, 141, 1, "__closure"], [146, 39, 141, 1], [147, 6, 141, 1, "ReduceMotion"], [147, 18, 141, 1], [147, 20, 136, 51, "ReduceMotion"], [147, 45, 136, 63], [148, 6, 136, 63, "isReduceMotionOnUI"], [149, 4, 136, 63], [150, 4, 136, 63, "getReduceMotionFromConfig"], [150, 29, 136, 63], [150, 30, 136, 63, "__workletHash"], [150, 43, 136, 63], [151, 4, 136, 63, "getReduceMotionFromConfig"], [151, 29, 136, 63], [151, 30, 136, 63, "__initData"], [151, 40, 136, 63], [151, 43, 136, 63, "_worklet_7714090217876_init_data"], [151, 75, 136, 63], [152, 4, 136, 63, "getReduceMotionFromConfig"], [152, 29, 136, 63], [152, 30, 136, 63, "__stackDetails"], [152, 44, 136, 63], [152, 47, 136, 63, "_e"], [152, 49, 136, 63], [153, 4, 136, 63], [153, 11, 136, 63, "getReduceMotionFromConfig"], [153, 36, 136, 63], [154, 2, 136, 63], [154, 3, 136, 7], [155, 2, 143, 0], [156, 0, 144, 0], [157, 0, 145, 0], [158, 0, 146, 0], [159, 2, 143, 0], [159, 6, 143, 0, "_worklet_16606661392394_init_data"], [159, 39, 143, 0], [160, 4, 143, 0, "code"], [160, 8, 143, 0], [161, 4, 143, 0, "location"], [161, 12, 143, 0], [162, 4, 143, 0, "sourceMap"], [162, 13, 143, 0], [163, 4, 143, 0, "version"], [163, 11, 143, 0], [164, 2, 143, 0], [165, 2, 143, 0], [165, 6, 143, 0, "getReduceMotionForAnimation"], [165, 33, 143, 0], [165, 36, 143, 0, "exports"], [165, 43, 143, 0], [165, 44, 143, 0, "getReduceMotionForAnimation"], [165, 71, 143, 0], [165, 74, 147, 7], [166, 4, 147, 7], [166, 8, 147, 7, "_e"], [166, 10, 147, 7], [166, 18, 147, 7, "global"], [166, 24, 147, 7], [166, 25, 147, 7, "Error"], [166, 30, 147, 7], [167, 4, 147, 7], [167, 8, 147, 7, "getReduceMotionForAnimation"], [167, 35, 147, 7], [167, 47, 147, 7, "getReduceMotionForAnimation"], [167, 48, 147, 44, "config"], [167, 54, 147, 65], [167, 56, 147, 67], [168, 6, 149, 2], [169, 6, 150, 2], [170, 6, 151, 2], [170, 10, 151, 6], [170, 11, 151, 7, "config"], [170, 17, 151, 13], [170, 19, 151, 15], [171, 8, 152, 4], [171, 15, 152, 11, "undefined"], [171, 24, 152, 20], [172, 6, 153, 2], [173, 6, 155, 2], [173, 13, 155, 9, "getReduceMotionFromConfig"], [173, 38, 155, 34], [173, 39, 155, 35, "config"], [173, 45, 155, 41], [173, 46, 155, 42], [174, 4, 156, 0], [174, 5, 156, 1], [175, 4, 156, 1, "getReduceMotionForAnimation"], [175, 31, 156, 1], [175, 32, 156, 1, "__closure"], [175, 41, 156, 1], [176, 6, 156, 1, "getReduceMotionFromConfig"], [177, 4, 156, 1], [178, 4, 156, 1, "getReduceMotionForAnimation"], [178, 31, 156, 1], [178, 32, 156, 1, "__workletHash"], [178, 45, 156, 1], [179, 4, 156, 1, "getReduceMotionForAnimation"], [179, 31, 156, 1], [179, 32, 156, 1, "__initData"], [179, 42, 156, 1], [179, 45, 156, 1, "_worklet_16606661392394_init_data"], [179, 78, 156, 1], [180, 4, 156, 1, "getReduceMotionForAnimation"], [180, 31, 156, 1], [180, 32, 156, 1, "__stackDetails"], [180, 46, 156, 1], [180, 49, 156, 1, "_e"], [180, 51, 156, 1], [181, 4, 156, 1], [181, 11, 156, 1, "getReduceMotionForAnimation"], [181, 38, 156, 1], [182, 2, 156, 1], [182, 3, 147, 7], [183, 2, 147, 7], [183, 6, 147, 7, "_worklet_3999595183904_init_data"], [183, 38, 147, 7], [184, 4, 147, 7, "code"], [184, 8, 147, 7], [185, 4, 147, 7, "location"], [185, 12, 147, 7], [186, 4, 147, 7, "sourceMap"], [186, 13, 147, 7], [187, 4, 147, 7, "version"], [187, 11, 147, 7], [188, 2, 147, 7], [189, 2, 147, 7], [189, 6, 147, 7, "applyProgressToMatrix"], [189, 27, 147, 7], [189, 30, 158, 0], [190, 4, 158, 0], [190, 8, 158, 0, "_e"], [190, 10, 158, 0], [190, 18, 158, 0, "global"], [190, 24, 158, 0], [190, 25, 158, 0, "Error"], [190, 30, 158, 0], [191, 4, 158, 0], [191, 8, 158, 0, "applyProgressToMatrix"], [191, 29, 158, 0], [191, 41, 158, 0, "applyProgressToMatrix"], [191, 42, 159, 2, "progress"], [191, 50, 159, 18], [191, 52, 160, 2, "a"], [191, 53, 160, 17], [191, 55, 161, 2, "b"], [191, 56, 161, 17], [191, 58, 162, 2], [192, 6, 164, 2], [192, 13, 164, 9], [192, 17, 164, 9, "addMatrices"], [192, 41, 164, 20], [192, 43, 164, 21, "a"], [192, 44, 164, 22], [192, 46, 164, 24], [192, 50, 164, 24, "scaleMatrix"], [192, 74, 164, 35], [192, 76, 164, 36], [192, 80, 164, 36, "subtractMatrices"], [192, 109, 164, 52], [192, 111, 164, 53, "b"], [192, 112, 164, 54], [192, 114, 164, 56, "a"], [192, 115, 164, 57], [192, 116, 164, 58], [192, 118, 164, 60, "progress"], [192, 126, 164, 68], [192, 127, 164, 69], [192, 128, 164, 70], [193, 4, 165, 0], [193, 5, 165, 1], [194, 4, 165, 1, "applyProgressToMatrix"], [194, 25, 165, 1], [194, 26, 165, 1, "__closure"], [194, 35, 165, 1], [195, 6, 165, 1, "addMatrices"], [195, 17, 165, 1], [195, 19, 164, 9, "addMatrices"], [195, 43, 164, 20], [196, 6, 164, 20, "scaleMatrix"], [196, 17, 164, 20], [196, 19, 164, 24, "scaleMatrix"], [196, 43, 164, 35], [197, 6, 164, 35, "subtractMatrices"], [197, 22, 164, 35], [197, 24, 164, 36, "subtractMatrices"], [198, 4, 164, 52], [199, 4, 164, 52, "applyProgressToMatrix"], [199, 25, 164, 52], [199, 26, 164, 52, "__workletHash"], [199, 39, 164, 52], [200, 4, 164, 52, "applyProgressToMatrix"], [200, 25, 164, 52], [200, 26, 164, 52, "__initData"], [200, 36, 164, 52], [200, 39, 164, 52, "_worklet_3999595183904_init_data"], [200, 71, 164, 52], [201, 4, 164, 52, "applyProgressToMatrix"], [201, 25, 164, 52], [201, 26, 164, 52, "__stackDetails"], [201, 40, 164, 52], [201, 43, 164, 52, "_e"], [201, 45, 164, 52], [202, 4, 164, 52], [202, 11, 164, 52, "applyProgressToMatrix"], [202, 32, 164, 52], [203, 2, 164, 52], [203, 3, 158, 0], [204, 2, 158, 0], [204, 6, 158, 0, "_worklet_15372800802213_init_data"], [204, 39, 158, 0], [205, 4, 158, 0, "code"], [205, 8, 158, 0], [206, 4, 158, 0, "location"], [206, 12, 158, 0], [207, 4, 158, 0, "sourceMap"], [207, 13, 158, 0], [208, 4, 158, 0, "version"], [208, 11, 158, 0], [209, 2, 158, 0], [210, 2, 158, 0], [210, 6, 158, 0, "applyProgressToNumber"], [210, 27, 158, 0], [210, 30, 167, 0], [211, 4, 167, 0], [211, 8, 167, 0, "_e"], [211, 10, 167, 0], [211, 18, 167, 0, "global"], [211, 24, 167, 0], [211, 25, 167, 0, "Error"], [211, 30, 167, 0], [212, 4, 167, 0], [212, 8, 167, 0, "applyProgressToNumber"], [212, 29, 167, 0], [212, 41, 167, 0, "applyProgressToNumber"], [212, 42, 167, 31, "progress"], [212, 50, 167, 47], [212, 52, 167, 49, "a"], [212, 53, 167, 58], [212, 55, 167, 60, "b"], [212, 56, 167, 69], [212, 58, 167, 71], [213, 6, 169, 2], [213, 13, 169, 9, "a"], [213, 14, 169, 10], [213, 17, 169, 13, "progress"], [213, 25, 169, 21], [213, 29, 169, 25, "b"], [213, 30, 169, 26], [213, 33, 169, 29, "a"], [213, 34, 169, 30], [213, 35, 169, 31], [214, 4, 170, 0], [214, 5, 170, 1], [215, 4, 170, 1, "applyProgressToNumber"], [215, 25, 170, 1], [215, 26, 170, 1, "__closure"], [215, 35, 170, 1], [216, 4, 170, 1, "applyProgressToNumber"], [216, 25, 170, 1], [216, 26, 170, 1, "__workletHash"], [216, 39, 170, 1], [217, 4, 170, 1, "applyProgressToNumber"], [217, 25, 170, 1], [217, 26, 170, 1, "__initData"], [217, 36, 170, 1], [217, 39, 170, 1, "_worklet_15372800802213_init_data"], [217, 72, 170, 1], [218, 4, 170, 1, "applyProgressToNumber"], [218, 25, 170, 1], [218, 26, 170, 1, "__stackDetails"], [218, 40, 170, 1], [218, 43, 170, 1, "_e"], [218, 45, 170, 1], [219, 4, 170, 1], [219, 11, 170, 1, "applyProgressToNumber"], [219, 32, 170, 1], [220, 2, 170, 1], [220, 3, 167, 0], [221, 2, 167, 0], [221, 6, 167, 0, "_worklet_1842068436516_init_data"], [221, 38, 167, 0], [222, 4, 167, 0, "code"], [222, 8, 167, 0], [223, 4, 167, 0, "location"], [223, 12, 167, 0], [224, 4, 167, 0, "sourceMap"], [224, 13, 167, 0], [225, 4, 167, 0, "version"], [225, 11, 167, 0], [226, 2, 167, 0], [227, 2, 167, 0], [227, 6, 167, 0, "decorateAnimation"], [227, 23, 167, 0], [227, 26, 172, 0], [228, 4, 172, 0], [228, 8, 172, 0, "_e"], [228, 10, 172, 0], [228, 18, 172, 0, "global"], [228, 24, 172, 0], [228, 25, 172, 0, "Error"], [228, 30, 172, 0], [229, 4, 172, 0], [229, 8, 172, 0, "decorateAnimation"], [229, 25, 172, 0], [229, 37, 172, 0, "decorateAnimation"], [229, 38, 173, 2, "animation"], [229, 47, 173, 14], [229, 49, 174, 8], [230, 6, 176, 2], [230, 10, 176, 8, "baseOnStart"], [230, 21, 176, 19], [230, 24, 176, 23, "animation"], [230, 33, 176, 32], [230, 34, 176, 64, "onStart"], [230, 41, 176, 71], [231, 6, 177, 2], [231, 10, 177, 8, "baseOnFrame"], [231, 21, 177, 19], [231, 24, 177, 23, "animation"], [231, 33, 177, 32], [231, 34, 177, 64, "onFrame"], [231, 41, 177, 71], [232, 6, 179, 2], [232, 10, 179, 7, "animation"], [232, 19, 179, 16], [232, 20, 179, 42, "isHigherOrder"], [232, 33, 179, 55], [232, 35, 179, 57], [233, 8, 180, 4, "animation"], [233, 17, 180, 13], [233, 18, 180, 14, "onStart"], [233, 25, 180, 21], [233, 28, 180, 24], [233, 29, 181, 6, "animation"], [233, 38, 181, 43], [233, 40, 182, 6, "value"], [233, 45, 182, 19], [233, 47, 183, 6, "timestamp"], [233, 56, 183, 26], [233, 58, 184, 6, "previousAnimation"], [233, 75, 184, 51], [233, 80, 185, 9], [234, 10, 186, 6], [234, 14, 186, 10, "animation"], [234, 23, 186, 19], [234, 24, 186, 20, "reduceMotion"], [234, 36, 186, 32], [234, 41, 186, 37, "undefined"], [234, 50, 186, 46], [234, 52, 186, 48], [235, 12, 187, 8, "animation"], [235, 21, 187, 17], [235, 22, 187, 18, "reduceMotion"], [235, 34, 187, 30], [235, 37, 187, 33, "getReduceMotionFromConfig"], [235, 62, 187, 58], [235, 63, 187, 59], [235, 64, 187, 60], [236, 10, 188, 6], [237, 10, 189, 6], [237, 17, 189, 13, "baseOnStart"], [237, 28, 189, 24], [237, 29, 189, 25, "animation"], [237, 38, 189, 34], [237, 40, 189, 36, "value"], [237, 45, 189, 41], [237, 47, 189, 43, "timestamp"], [237, 56, 189, 52], [237, 58, 189, 54, "previousAnimation"], [237, 75, 189, 71], [237, 76, 189, 72], [238, 8, 190, 4], [238, 9, 190, 5], [239, 8, 191, 4], [240, 6, 192, 2], [241, 6, 194, 2], [241, 10, 194, 8, "animationCopy"], [241, 23, 194, 21], [241, 26, 194, 24, "Object"], [241, 32, 194, 30], [241, 33, 194, 31, "assign"], [241, 39, 194, 37], [241, 40, 194, 38], [241, 41, 194, 39], [241, 42, 194, 40], [241, 44, 194, 42, "animation"], [241, 53, 194, 51], [241, 54, 194, 52], [242, 6, 195, 2], [242, 13, 195, 9, "animationCopy"], [242, 26, 195, 22], [242, 27, 195, 23, "callback"], [242, 35, 195, 31], [243, 6, 197, 2], [243, 10, 197, 8, "prefNumberSuffOnStart"], [243, 31, 197, 29], [243, 34, 197, 32, "prefNumberSuffOnStart"], [243, 35, 198, 4, "animation"], [243, 44, 198, 41], [243, 46, 199, 4, "value"], [243, 51, 199, 26], [243, 53, 200, 4, "timestamp"], [243, 62, 200, 21], [243, 64, 201, 4, "previousAnimation"], [243, 81, 201, 49], [243, 86, 202, 7], [244, 8, 203, 4], [245, 8, 204, 4], [245, 12, 204, 4, "_recognizePrefixSuffi"], [245, 33, 204, 4], [245, 36, 204, 46, "recognizePrefixSuffix"], [245, 57, 204, 67], [245, 58, 204, 68, "value"], [245, 63, 204, 73], [245, 64, 204, 74], [246, 10, 204, 12, "prefix"], [246, 16, 204, 18], [246, 19, 204, 18, "_recognizePrefixSuffi"], [246, 40, 204, 18], [246, 41, 204, 12, "prefix"], [246, 47, 204, 18], [247, 10, 204, 20, "suffix"], [247, 16, 204, 26], [247, 19, 204, 26, "_recognizePrefixSuffi"], [247, 40, 204, 26], [247, 41, 204, 20, "suffix"], [247, 47, 204, 26], [248, 10, 204, 28, "strippedValue"], [248, 23, 204, 41], [248, 26, 204, 41, "_recognizePrefixSuffi"], [248, 47, 204, 41], [248, 48, 204, 28, "strippedValue"], [248, 61, 204, 41], [249, 8, 205, 4, "animation"], [249, 17, 205, 13], [249, 18, 205, 14, "__prefix"], [249, 26, 205, 22], [249, 29, 205, 25, "prefix"], [249, 35, 205, 31], [250, 8, 206, 4, "animation"], [250, 17, 206, 13], [250, 18, 206, 14, "__suffix"], [250, 26, 206, 22], [250, 29, 206, 25, "suffix"], [250, 35, 206, 31], [251, 8, 207, 4, "animation"], [251, 17, 207, 13], [251, 18, 207, 14, "strippedCurrent"], [251, 33, 207, 29], [251, 36, 207, 32, "strippedValue"], [251, 49, 207, 45], [252, 8, 208, 4], [252, 12, 208, 4, "_recognizePrefixSuffi2"], [252, 34, 208, 4], [252, 37, 208, 47, "recognizePrefixSuffix"], [252, 58, 208, 68], [252, 59, 209, 6, "animation"], [252, 68, 209, 15], [252, 69, 209, 16, "toValue"], [252, 76, 210, 4], [252, 77, 210, 5], [253, 10, 208, 27, "strippedToValue"], [253, 25, 208, 42], [253, 28, 208, 42, "_recognizePrefixSuffi2"], [253, 50, 208, 42], [253, 51, 208, 12, "strippedValue"], [253, 64, 208, 25], [254, 8, 211, 4, "animation"], [254, 17, 211, 13], [254, 18, 211, 14, "current"], [254, 25, 211, 21], [254, 28, 211, 24, "strippedValue"], [254, 41, 211, 37], [255, 8, 212, 4, "animation"], [255, 17, 212, 13], [255, 18, 212, 14, "startValue"], [255, 28, 212, 24], [255, 31, 212, 27, "strippedValue"], [255, 44, 212, 40], [256, 8, 213, 4, "animation"], [256, 17, 213, 13], [256, 18, 213, 14, "toValue"], [256, 25, 213, 21], [256, 28, 213, 24, "strippedToValue"], [256, 43, 213, 39], [257, 8, 214, 4], [257, 12, 214, 8, "previousAnimation"], [257, 29, 214, 25], [257, 33, 214, 29, "previousAnimation"], [257, 50, 214, 46], [257, 55, 214, 51, "animation"], [257, 64, 214, 60], [257, 66, 214, 62], [258, 10, 215, 6], [258, 14, 215, 6, "_recognizePrefixSuffi3"], [258, 36, 215, 6], [258, 39, 219, 10, "recognizePrefixSuffix"], [258, 60, 219, 31], [258, 61, 219, 32, "previousAnimation"], [258, 78, 219, 49], [258, 79, 219, 50, "current"], [258, 86, 219, 76], [258, 87, 219, 77], [259, 12, 216, 16, "paPrefix"], [259, 20, 216, 24], [259, 23, 216, 24, "_recognizePrefixSuffi3"], [259, 45, 216, 24], [259, 46, 216, 8, "prefix"], [259, 52, 216, 14], [260, 12, 217, 16, "paSuffix"], [260, 20, 217, 24], [260, 23, 217, 24, "_recognizePrefixSuffi3"], [260, 45, 217, 24], [260, 46, 217, 8, "suffix"], [260, 52, 217, 14], [261, 12, 218, 23, "paStrippedValue"], [261, 27, 218, 38], [261, 30, 218, 38, "_recognizePrefixSuffi3"], [261, 52, 218, 38], [261, 53, 218, 8, "strippedValue"], [261, 66, 218, 21], [262, 10, 220, 6, "previousAnimation"], [262, 27, 220, 23], [262, 28, 220, 24, "current"], [262, 35, 220, 31], [262, 38, 220, 34, "paStrippedValue"], [262, 53, 220, 49], [263, 10, 221, 6, "previousAnimation"], [263, 27, 221, 23], [263, 28, 221, 24, "__prefix"], [263, 36, 221, 32], [263, 39, 221, 35, "paPrefix"], [263, 47, 221, 43], [264, 10, 222, 6, "previousAnimation"], [264, 27, 222, 23], [264, 28, 222, 24, "__suffix"], [264, 36, 222, 32], [264, 39, 222, 35, "paSuffix"], [264, 47, 222, 43], [265, 8, 223, 4], [266, 8, 225, 4, "baseOnStart"], [266, 19, 225, 15], [266, 20, 225, 16, "animation"], [266, 29, 225, 25], [266, 31, 225, 27, "strippedValue"], [266, 44, 225, 40], [266, 46, 225, 42, "timestamp"], [266, 55, 225, 51], [266, 57, 225, 53, "previousAnimation"], [266, 74, 225, 70], [266, 75, 225, 71], [267, 8, 227, 4, "animation"], [267, 17, 227, 13], [267, 18, 227, 14, "current"], [267, 25, 227, 21], [267, 28, 228, 6], [267, 29, 228, 7, "animation"], [267, 38, 228, 16], [267, 39, 228, 17, "__prefix"], [267, 47, 228, 25], [267, 51, 228, 29], [267, 53, 228, 31], [267, 57, 229, 6, "animation"], [267, 66, 229, 15], [267, 67, 229, 16, "current"], [267, 74, 229, 23], [267, 78, 230, 7, "animation"], [267, 87, 230, 16], [267, 88, 230, 17, "__suffix"], [267, 96, 230, 25], [267, 100, 230, 29], [267, 102, 230, 31], [267, 103, 230, 32], [268, 8, 232, 4], [268, 12, 232, 8, "previousAnimation"], [268, 29, 232, 25], [268, 33, 232, 29, "previousAnimation"], [268, 50, 232, 46], [268, 55, 232, 51, "animation"], [268, 64, 232, 60], [268, 66, 232, 62], [269, 10, 233, 6, "previousAnimation"], [269, 27, 233, 23], [269, 28, 233, 24, "current"], [269, 35, 233, 31], [269, 38, 234, 8], [269, 39, 234, 9, "previousAnimation"], [269, 56, 234, 26], [269, 57, 234, 27, "__prefix"], [269, 65, 234, 35], [269, 69, 234, 39], [269, 71, 234, 41], [270, 10, 235, 8], [271, 10, 236, 8], [272, 10, 237, 8, "previousAnimation"], [272, 27, 237, 25], [272, 28, 237, 26, "current"], [272, 35, 237, 33], [272, 39, 238, 9, "previousAnimation"], [272, 56, 238, 26], [272, 57, 238, 27, "__suffix"], [272, 65, 238, 35], [272, 69, 238, 39], [272, 71, 238, 41], [272, 72, 238, 42], [273, 8, 239, 4], [274, 6, 240, 2], [274, 7, 240, 3], [275, 6, 241, 2], [275, 10, 241, 8, "prefNumberSuffOnFrame"], [275, 31, 241, 29], [275, 34, 241, 32, "prefNumberSuffOnFrame"], [275, 35, 242, 4, "animation"], [275, 44, 242, 41], [275, 46, 243, 4, "timestamp"], [275, 55, 243, 21], [275, 60, 244, 7], [276, 8, 245, 4, "animation"], [276, 17, 245, 13], [276, 18, 245, 14, "current"], [276, 25, 245, 21], [276, 28, 245, 24, "animation"], [276, 37, 245, 33], [276, 38, 245, 34, "strippedCurrent"], [276, 53, 245, 49], [277, 8, 246, 4], [277, 12, 246, 10, "res"], [277, 15, 246, 13], [277, 18, 246, 16, "baseOnFrame"], [277, 29, 246, 27], [277, 30, 246, 28, "animation"], [277, 39, 246, 37], [277, 41, 246, 39, "timestamp"], [277, 50, 246, 48], [277, 51, 246, 49], [278, 8, 247, 4, "animation"], [278, 17, 247, 13], [278, 18, 247, 14, "strippedCurrent"], [278, 33, 247, 29], [278, 36, 247, 32, "animation"], [278, 45, 247, 41], [278, 46, 247, 42, "current"], [278, 53, 247, 49], [279, 8, 248, 4, "animation"], [279, 17, 248, 13], [279, 18, 248, 14, "current"], [279, 25, 248, 21], [279, 28, 249, 6], [279, 29, 249, 7, "animation"], [279, 38, 249, 16], [279, 39, 249, 17, "__prefix"], [279, 47, 249, 25], [279, 51, 249, 29], [279, 53, 249, 31], [279, 57, 250, 6, "animation"], [279, 66, 250, 15], [279, 67, 250, 16, "current"], [279, 74, 250, 23], [279, 78, 251, 7, "animation"], [279, 87, 251, 16], [279, 88, 251, 17, "__suffix"], [279, 96, 251, 25], [279, 100, 251, 29], [279, 102, 251, 31], [279, 103, 251, 32], [280, 8, 252, 4], [280, 15, 252, 11, "res"], [280, 18, 252, 14], [281, 6, 253, 2], [281, 7, 253, 3], [282, 6, 255, 2], [282, 10, 255, 8, "tab"], [282, 13, 255, 11], [282, 16, 255, 14], [282, 17, 255, 15], [282, 20, 255, 18], [282, 22, 255, 20], [282, 25, 255, 23], [282, 27, 255, 25], [282, 30, 255, 28], [282, 32, 255, 30], [282, 35, 255, 33], [282, 36, 255, 34], [283, 6, 256, 2], [283, 10, 256, 8, "colorOnStart"], [283, 22, 256, 20], [283, 25, 256, 23, "colorOnStart"], [283, 26, 257, 4, "animation"], [283, 35, 257, 41], [283, 37, 258, 4, "value"], [283, 42, 258, 26], [283, 44, 259, 4, "timestamp"], [283, 53, 259, 24], [283, 55, 260, 4, "previousAnimation"], [283, 72, 260, 49], [283, 77, 261, 13], [284, 8, 262, 4], [284, 12, 262, 8, "RGBAValue"], [284, 21, 262, 35], [285, 8, 263, 4], [285, 12, 263, 8, "RGBACurrent"], [285, 23, 263, 37], [286, 8, 264, 4], [286, 12, 264, 8, "RGBAToValue"], [286, 23, 264, 37], [287, 8, 265, 4], [287, 12, 265, 10, "res"], [287, 15, 265, 28], [287, 18, 265, 31], [287, 20, 265, 33], [288, 8, 266, 4], [288, 12, 266, 8], [288, 16, 266, 8, "isColor"], [288, 31, 266, 15], [288, 33, 266, 16, "value"], [288, 38, 266, 21], [288, 39, 266, 22], [288, 41, 266, 24], [289, 10, 267, 6, "RGBACurrent"], [289, 21, 267, 17], [289, 24, 267, 20], [289, 28, 267, 20, "toLinearSpace"], [289, 49, 267, 33], [289, 51, 267, 34], [289, 55, 267, 34, "convertToRGBA"], [289, 76, 267, 47], [289, 78, 267, 48, "animation"], [289, 87, 267, 57], [289, 88, 267, 58, "current"], [289, 95, 267, 65], [289, 96, 267, 66], [289, 97, 267, 67], [290, 10, 268, 6, "RGBAValue"], [290, 19, 268, 15], [290, 22, 268, 18], [290, 26, 268, 18, "toLinearSpace"], [290, 47, 268, 31], [290, 49, 268, 32], [290, 53, 268, 32, "convertToRGBA"], [290, 74, 268, 45], [290, 76, 268, 46, "value"], [290, 81, 268, 51], [290, 82, 268, 52], [290, 83, 268, 53], [291, 10, 269, 6], [291, 14, 269, 10, "animation"], [291, 23, 269, 19], [291, 24, 269, 20, "toValue"], [291, 31, 269, 27], [291, 33, 269, 29], [292, 12, 270, 8, "RGBAToValue"], [292, 23, 270, 19], [292, 26, 270, 22], [292, 30, 270, 22, "toLinearSpace"], [292, 51, 270, 35], [292, 53, 270, 36], [292, 57, 270, 36, "convertToRGBA"], [292, 78, 270, 49], [292, 80, 270, 50, "animation"], [292, 89, 270, 59], [292, 90, 270, 60, "toValue"], [292, 97, 270, 67], [292, 98, 270, 68], [292, 99, 270, 69], [293, 10, 271, 6], [294, 8, 272, 4], [295, 8, 273, 4, "tab"], [295, 11, 273, 7], [295, 12, 273, 8, "for<PERSON>ach"], [295, 19, 273, 15], [295, 20, 273, 16], [295, 21, 273, 17, "i"], [295, 22, 273, 18], [295, 24, 273, 20, "index"], [295, 29, 273, 25], [295, 34, 273, 30], [296, 10, 274, 6, "animation"], [296, 19, 274, 15], [296, 20, 274, 16, "i"], [296, 21, 274, 17], [296, 22, 274, 18], [296, 25, 274, 21, "Object"], [296, 31, 274, 27], [296, 32, 274, 28, "assign"], [296, 38, 274, 34], [296, 39, 274, 35], [296, 40, 274, 36], [296, 41, 274, 37], [296, 43, 274, 39, "animationCopy"], [296, 56, 274, 52], [296, 57, 274, 53], [297, 10, 275, 6, "animation"], [297, 19, 275, 15], [297, 20, 275, 16, "i"], [297, 21, 275, 17], [297, 22, 275, 18], [297, 23, 275, 19, "current"], [297, 30, 275, 26], [297, 33, 275, 29, "RGBACurrent"], [297, 44, 275, 40], [297, 45, 275, 41, "index"], [297, 50, 275, 46], [297, 51, 275, 47], [298, 10, 276, 6, "animation"], [298, 19, 276, 15], [298, 20, 276, 16, "i"], [298, 21, 276, 17], [298, 22, 276, 18], [298, 23, 276, 19, "toValue"], [298, 30, 276, 26], [298, 33, 276, 29, "RGBAToValue"], [298, 44, 276, 40], [298, 47, 276, 43, "RGBAToValue"], [298, 58, 276, 54], [298, 59, 276, 55, "index"], [298, 64, 276, 60], [298, 65, 276, 61], [298, 68, 276, 64, "undefined"], [298, 77, 276, 73], [299, 10, 277, 6, "animation"], [299, 19, 277, 15], [299, 20, 277, 16, "i"], [299, 21, 277, 17], [299, 22, 277, 18], [299, 23, 277, 19, "onStart"], [299, 30, 277, 26], [299, 31, 278, 8, "animation"], [299, 40, 278, 17], [299, 41, 278, 18, "i"], [299, 42, 278, 19], [299, 43, 278, 20], [299, 45, 279, 8, "RGBAValue"], [299, 54, 279, 17], [299, 55, 279, 18, "index"], [299, 60, 279, 23], [299, 61, 279, 24], [299, 63, 280, 8, "timestamp"], [299, 72, 280, 17], [299, 74, 281, 8, "previousAnimation"], [299, 91, 281, 25], [299, 94, 281, 28, "previousAnimation"], [299, 111, 281, 45], [299, 112, 281, 46, "i"], [299, 113, 281, 47], [299, 114, 281, 48], [299, 117, 281, 51, "undefined"], [299, 126, 282, 6], [299, 127, 282, 7], [300, 10, 283, 6, "res"], [300, 13, 283, 9], [300, 14, 283, 10, "push"], [300, 18, 283, 14], [300, 19, 283, 15, "animation"], [300, 28, 283, 24], [300, 29, 283, 25, "i"], [300, 30, 283, 26], [300, 31, 283, 27], [300, 32, 283, 28, "current"], [300, 39, 283, 35], [300, 40, 283, 36], [301, 8, 284, 4], [301, 9, 284, 5], [301, 10, 284, 6], [303, 8, 286, 4], [304, 8, 287, 4], [304, 12, 287, 4, "clampRGBA"], [304, 29, 287, 13], [304, 31, 287, 14, "res"], [304, 34, 287, 37], [304, 35, 287, 38], [305, 8, 289, 4, "animation"], [305, 17, 289, 13], [305, 18, 289, 14, "current"], [305, 25, 289, 21], [305, 28, 289, 24], [305, 32, 289, 24, "rgbaArrayToRGBAColor"], [305, 60, 289, 44], [305, 62, 290, 6], [305, 66, 290, 6, "toGammaSpace"], [305, 86, 290, 18], [305, 88, 290, 19, "res"], [305, 91, 290, 42], [305, 92, 291, 4], [305, 93, 291, 5], [306, 6, 292, 2], [306, 7, 292, 3], [307, 6, 294, 2], [307, 10, 294, 8, "colorOnFrame"], [307, 22, 294, 20], [307, 25, 294, 23, "colorOnFrame"], [307, 26, 295, 4, "animation"], [307, 35, 295, 41], [307, 37, 296, 4, "timestamp"], [307, 46, 296, 24], [307, 51, 297, 16], [308, 8, 298, 4], [308, 12, 298, 10, "RGBACurrent"], [308, 23, 298, 21], [308, 26, 298, 24], [308, 30, 298, 24, "toLinearSpace"], [308, 51, 298, 37], [308, 53, 298, 38], [308, 57, 298, 38, "convertToRGBA"], [308, 78, 298, 51], [308, 80, 298, 52, "animation"], [308, 89, 298, 61], [308, 90, 298, 62, "current"], [308, 97, 298, 69], [308, 98, 298, 70], [308, 99, 298, 71], [309, 8, 299, 4], [309, 12, 299, 10, "res"], [309, 15, 299, 28], [309, 18, 299, 31], [309, 20, 299, 33], [310, 8, 300, 4], [310, 12, 300, 8, "finished"], [310, 20, 300, 16], [310, 23, 300, 19], [310, 27, 300, 23], [311, 8, 301, 4, "tab"], [311, 11, 301, 7], [311, 12, 301, 8, "for<PERSON>ach"], [311, 19, 301, 15], [311, 20, 301, 16], [311, 21, 301, 17, "i"], [311, 22, 301, 18], [311, 24, 301, 20, "index"], [311, 29, 301, 25], [311, 34, 301, 30], [312, 10, 302, 6, "animation"], [312, 19, 302, 15], [312, 20, 302, 16, "i"], [312, 21, 302, 17], [312, 22, 302, 18], [312, 23, 302, 19, "current"], [312, 30, 302, 26], [312, 33, 302, 29, "RGBACurrent"], [312, 44, 302, 40], [312, 45, 302, 41, "index"], [312, 50, 302, 46], [312, 51, 302, 47], [313, 10, 303, 6], [313, 14, 303, 12, "result"], [313, 20, 303, 18], [313, 23, 303, 21, "animation"], [313, 32, 303, 30], [313, 33, 303, 31, "i"], [313, 34, 303, 32], [313, 35, 303, 33], [313, 36, 303, 34, "onFrame"], [313, 43, 303, 41], [313, 44, 303, 42, "animation"], [313, 53, 303, 51], [313, 54, 303, 52, "i"], [313, 55, 303, 53], [313, 56, 303, 54], [313, 58, 303, 56, "timestamp"], [313, 67, 303, 65], [313, 68, 303, 66], [314, 10, 304, 6], [315, 10, 305, 6, "finished"], [315, 18, 305, 14], [315, 21, 305, 17, "finished"], [315, 29, 305, 25], [315, 33, 305, 29, "result"], [315, 39, 305, 35], [316, 10, 306, 6, "res"], [316, 13, 306, 9], [316, 14, 306, 10, "push"], [316, 18, 306, 14], [316, 19, 306, 15, "animation"], [316, 28, 306, 24], [316, 29, 306, 25, "i"], [316, 30, 306, 26], [316, 31, 306, 27], [316, 32, 306, 28, "current"], [316, 39, 306, 35], [316, 40, 306, 36], [317, 8, 307, 4], [317, 9, 307, 5], [317, 10, 307, 6], [319, 8, 309, 4], [320, 8, 310, 4], [320, 12, 310, 4, "clampRGBA"], [320, 29, 310, 13], [320, 31, 310, 14, "res"], [320, 34, 310, 37], [320, 35, 310, 38], [321, 8, 312, 4, "animation"], [321, 17, 312, 13], [321, 18, 312, 14, "current"], [321, 25, 312, 21], [321, 28, 312, 24], [321, 32, 312, 24, "rgbaArrayToRGBAColor"], [321, 60, 312, 44], [321, 62, 313, 6], [321, 66, 313, 6, "toGammaSpace"], [321, 86, 313, 18], [321, 88, 313, 19, "res"], [321, 91, 313, 42], [321, 92, 314, 4], [321, 93, 314, 5], [322, 8, 315, 4], [322, 15, 315, 11, "finished"], [322, 23, 315, 19], [323, 6, 316, 2], [323, 7, 316, 3], [324, 6, 318, 2], [324, 10, 318, 8, "transformationMatrixOnStart"], [324, 37, 318, 35], [324, 40, 318, 38, "transformationMatrixOnStart"], [324, 41, 319, 4, "animation"], [324, 50, 319, 41], [324, 52, 320, 4, "value"], [324, 57, 320, 27], [324, 59, 321, 4, "timestamp"], [324, 68, 321, 24], [324, 70, 322, 4, "previousAnimation"], [324, 87, 322, 49], [324, 92, 323, 13], [325, 8, 324, 4], [325, 12, 324, 10, "toValue"], [325, 19, 324, 17], [325, 22, 324, 20, "animation"], [325, 31, 324, 29], [325, 32, 324, 30, "toValue"], [325, 39, 324, 57], [326, 8, 326, 4, "animation"], [326, 17, 326, 13], [326, 18, 326, 14, "startMatrices"], [326, 31, 326, 27], [326, 34, 326, 30], [326, 38, 326, 30, "decomposeMatrixIntoMatricesAndAngles"], [326, 87, 326, 66], [326, 89, 326, 67, "value"], [326, 94, 326, 72], [326, 95, 326, 73], [327, 8, 327, 4, "animation"], [327, 17, 327, 13], [327, 18, 327, 14, "stopMatrices"], [327, 30, 327, 26], [327, 33, 327, 29], [327, 37, 327, 29, "decomposeMatrixIntoMatricesAndAngles"], [327, 86, 327, 65], [327, 88, 327, 66, "toValue"], [327, 95, 327, 73], [327, 96, 327, 74], [329, 8, 329, 4], [330, 8, 330, 4], [331, 8, 331, 4], [333, 8, 333, 4, "animation"], [333, 17, 333, 13], [333, 18, 333, 14], [333, 19, 333, 15], [333, 20, 333, 16], [333, 23, 333, 19, "Object"], [333, 29, 333, 25], [333, 30, 333, 26, "assign"], [333, 36, 333, 32], [333, 37, 333, 33], [333, 38, 333, 34], [333, 39, 333, 35], [333, 41, 333, 37, "animationCopy"], [333, 54, 333, 50], [333, 55, 333, 51], [334, 8, 334, 4, "animation"], [334, 17, 334, 13], [334, 18, 334, 14], [334, 19, 334, 15], [334, 20, 334, 16], [334, 21, 334, 17, "current"], [334, 28, 334, 24], [334, 31, 334, 27], [334, 32, 334, 28], [335, 8, 335, 4, "animation"], [335, 17, 335, 13], [335, 18, 335, 14], [335, 19, 335, 15], [335, 20, 335, 16], [335, 21, 335, 17, "toValue"], [335, 28, 335, 24], [335, 31, 335, 27], [335, 34, 335, 30], [336, 8, 336, 4, "animation"], [336, 17, 336, 13], [336, 18, 336, 14], [336, 19, 336, 15], [336, 20, 336, 16], [336, 21, 336, 17, "onStart"], [336, 28, 336, 24], [336, 29, 337, 6, "animation"], [336, 38, 337, 15], [336, 39, 337, 16], [336, 40, 337, 17], [336, 41, 337, 18], [336, 43, 338, 6], [336, 44, 338, 7], [336, 46, 339, 6, "timestamp"], [336, 55, 339, 15], [336, 57, 340, 6, "previousAnimation"], [336, 74, 340, 23], [336, 77, 340, 26, "previousAnimation"], [336, 94, 340, 43], [336, 95, 340, 44], [336, 96, 340, 45], [336, 97, 340, 46], [336, 100, 340, 49, "undefined"], [336, 109, 341, 4], [336, 110, 341, 5], [337, 8, 343, 4, "animation"], [337, 17, 343, 13], [337, 18, 343, 14, "current"], [337, 25, 343, 21], [337, 28, 343, 24, "value"], [337, 33, 343, 29], [338, 6, 344, 2], [338, 7, 344, 3], [339, 6, 346, 2], [339, 10, 346, 8, "transformationMatrixOnFrame"], [339, 37, 346, 35], [339, 40, 346, 38, "transformationMatrixOnFrame"], [339, 41, 347, 4, "animation"], [339, 50, 347, 41], [339, 52, 348, 4, "timestamp"], [339, 61, 348, 24], [339, 66, 349, 16], [340, 8, 350, 4], [340, 12, 350, 8, "finished"], [340, 20, 350, 16], [340, 23, 350, 19], [340, 27, 350, 23], [341, 8, 351, 4], [341, 12, 351, 10, "result"], [341, 18, 351, 16], [341, 21, 351, 19, "animation"], [341, 30, 351, 28], [341, 31, 351, 29], [341, 32, 351, 30], [341, 33, 351, 31], [341, 34, 351, 32, "onFrame"], [341, 41, 351, 39], [341, 42, 351, 40, "animation"], [341, 51, 351, 49], [341, 52, 351, 50], [341, 53, 351, 51], [341, 54, 351, 52], [341, 56, 351, 54, "timestamp"], [341, 65, 351, 63], [341, 66, 351, 64], [342, 8, 352, 4], [343, 8, 353, 4, "finished"], [343, 16, 353, 12], [343, 19, 353, 15, "finished"], [343, 27, 353, 23], [343, 31, 353, 27, "result"], [343, 37, 353, 33], [344, 8, 355, 4], [344, 12, 355, 10, "progress"], [344, 20, 355, 18], [344, 23, 355, 21, "animation"], [344, 32, 355, 30], [344, 33, 355, 31], [344, 34, 355, 32], [344, 35, 355, 33], [344, 36, 355, 34, "current"], [344, 43, 355, 41], [344, 46, 355, 44], [344, 49, 355, 47], [345, 8, 357, 4], [345, 12, 357, 10, "transforms"], [345, 22, 357, 20], [345, 25, 357, 23], [345, 26, 357, 24], [345, 45, 357, 43], [345, 47, 357, 45], [345, 60, 357, 58], [345, 62, 357, 60], [345, 74, 357, 72], [345, 75, 357, 73], [346, 8, 358, 4], [346, 12, 358, 10, "mappedTransforms"], [346, 28, 358, 47], [346, 31, 358, 50], [346, 33, 358, 52], [347, 8, 360, 4, "transforms"], [347, 18, 360, 14], [347, 19, 360, 15, "for<PERSON>ach"], [347, 26, 360, 22], [347, 27, 360, 23], [347, 28, 360, 24, "key"], [347, 31, 360, 27], [347, 33, 360, 29, "_"], [347, 34, 360, 30], [347, 39, 361, 6, "mappedTransforms"], [347, 55, 361, 22], [347, 56, 361, 23, "push"], [347, 60, 361, 27], [347, 61, 362, 8, "applyProgressToMatrix"], [347, 82, 362, 29], [347, 83, 363, 10, "progress"], [347, 91, 363, 18], [347, 93, 364, 10, "animation"], [347, 102, 364, 19], [347, 103, 364, 20, "startMatrices"], [347, 116, 364, 33], [347, 117, 364, 34, "key"], [347, 120, 364, 37], [347, 121, 364, 38], [347, 123, 365, 10, "animation"], [347, 132, 365, 19], [347, 133, 365, 20, "stopMatrices"], [347, 145, 365, 32], [347, 146, 365, 33, "key"], [347, 149, 365, 36], [347, 150, 366, 8], [347, 151, 367, 6], [347, 152, 368, 4], [347, 153, 368, 5], [348, 8, 370, 4], [348, 12, 370, 11, "currentTranslation"], [348, 30, 370, 29], [348, 33, 370, 59, "mappedTransforms"], [348, 49, 370, 75], [349, 10, 370, 31, "currentScale"], [349, 22, 370, 43], [349, 25, 370, 59, "mappedTransforms"], [349, 41, 370, 75], [350, 10, 370, 45, "skewMatrix"], [350, 20, 370, 55], [350, 23, 370, 59, "mappedTransforms"], [350, 39, 370, 75], [351, 8, 372, 4], [351, 12, 372, 10, "rotations"], [351, 21, 372, 43], [351, 24, 372, 46], [351, 25, 372, 47], [351, 28, 372, 50], [351, 30, 372, 52], [351, 33, 372, 55], [351, 35, 372, 57], [351, 38, 372, 60], [351, 39, 372, 61], [352, 8, 373, 4], [352, 12, 373, 10, "mappedRotations"], [352, 27, 373, 46], [352, 30, 373, 49], [352, 32, 373, 51], [353, 8, 375, 4, "rotations"], [353, 17, 375, 13], [353, 18, 375, 14, "for<PERSON>ach"], [353, 25, 375, 21], [353, 26, 375, 22], [353, 27, 375, 23, "key"], [353, 30, 375, 26], [353, 32, 375, 28, "_"], [353, 33, 375, 29], [353, 38, 375, 34], [354, 10, 376, 6], [354, 14, 376, 12, "angle"], [354, 19, 376, 17], [354, 22, 376, 20, "applyProgressToNumber"], [354, 43, 376, 41], [354, 44, 377, 8, "progress"], [354, 52, 377, 16], [354, 54, 378, 8, "animation"], [354, 63, 378, 17], [354, 64, 378, 18, "startMatrices"], [354, 77, 378, 31], [354, 78, 378, 32], [354, 81, 378, 35], [354, 84, 378, 38, "key"], [354, 87, 378, 41], [354, 88, 378, 42], [354, 90, 379, 8, "animation"], [354, 99, 379, 17], [354, 100, 379, 18, "stopMatrices"], [354, 112, 379, 30], [354, 113, 379, 31], [354, 116, 379, 34], [354, 119, 379, 37, "key"], [354, 122, 379, 40], [354, 123, 380, 6], [354, 124, 380, 7], [355, 10, 381, 6, "mappedRotations"], [355, 25, 381, 21], [355, 26, 381, 22, "push"], [355, 30, 381, 26], [355, 31, 381, 27], [355, 35, 381, 27, "getRotationMatrix"], [355, 65, 381, 44], [355, 67, 381, 45, "angle"], [355, 72, 381, 50], [355, 74, 381, 52, "key"], [355, 77, 381, 55], [355, 78, 381, 56], [355, 79, 381, 57], [356, 8, 382, 4], [356, 9, 382, 5], [356, 10, 382, 6], [357, 8, 384, 4], [357, 12, 384, 11, "rotationMatrixX"], [357, 27, 384, 26], [357, 30, 384, 64, "mappedRotations"], [357, 45, 384, 79], [358, 10, 384, 28, "rotationMatrixY"], [358, 25, 384, 43], [358, 28, 384, 64, "mappedRotations"], [358, 43, 384, 79], [359, 10, 384, 45, "rotationMatrixZ"], [359, 25, 384, 60], [359, 28, 384, 64, "mappedRotations"], [359, 43, 384, 79], [360, 8, 386, 4], [360, 12, 386, 10, "rotationMatrix"], [360, 26, 386, 24], [360, 29, 386, 27], [360, 33, 386, 27, "multiplyMatrices"], [360, 62, 386, 43], [360, 64, 387, 6, "rotationMatrixX"], [360, 79, 387, 21], [360, 81, 388, 6], [360, 85, 388, 6, "multiplyMatrices"], [360, 114, 388, 22], [360, 116, 388, 23, "rotationMatrixY"], [360, 131, 388, 38], [360, 133, 388, 40, "rotationMatrixZ"], [360, 148, 388, 55], [360, 149, 389, 4], [360, 150, 389, 5], [361, 8, 391, 4], [361, 12, 391, 10, "updated"], [361, 19, 391, 17], [361, 22, 391, 20], [361, 26, 391, 20, "flatten"], [361, 46, 391, 27], [361, 48, 392, 6], [361, 52, 392, 6, "multiplyMatrices"], [361, 81, 392, 22], [361, 83, 393, 8], [361, 87, 393, 8, "multiplyMatrices"], [361, 116, 393, 24], [361, 118, 394, 10, "currentScale"], [361, 130, 394, 22], [361, 132, 395, 10], [361, 136, 395, 10, "multiplyMatrices"], [361, 165, 395, 26], [361, 167, 395, 27, "skewMatrix"], [361, 177, 395, 37], [361, 179, 395, 39, "rotationMatrix"], [361, 193, 395, 53], [361, 194, 396, 8], [361, 195, 396, 9], [361, 197, 397, 8, "currentTranslation"], [361, 215, 398, 6], [361, 216, 399, 4], [361, 217, 399, 5], [362, 8, 401, 4, "animation"], [362, 17, 401, 13], [362, 18, 401, 14, "current"], [362, 25, 401, 21], [362, 28, 401, 24, "updated"], [362, 35, 401, 31], [363, 8, 403, 4], [363, 15, 403, 11, "finished"], [363, 23, 403, 19], [364, 6, 404, 2], [364, 7, 404, 3], [365, 6, 406, 2], [365, 10, 406, 8, "arrayOnStart"], [365, 22, 406, 20], [365, 25, 406, 23, "arrayOnStart"], [365, 26, 407, 4, "animation"], [365, 35, 407, 41], [365, 37, 408, 4, "value"], [365, 42, 408, 24], [365, 44, 409, 4, "timestamp"], [365, 53, 409, 24], [365, 55, 410, 4, "previousAnimation"], [365, 72, 410, 49], [365, 77, 411, 13], [366, 8, 412, 4, "value"], [366, 13, 412, 9], [366, 14, 412, 10, "for<PERSON>ach"], [366, 21, 412, 17], [366, 22, 412, 18], [366, 23, 412, 19, "v"], [366, 24, 412, 20], [366, 26, 412, 22, "i"], [366, 27, 412, 23], [366, 32, 412, 28], [367, 10, 413, 6, "animation"], [367, 19, 413, 15], [367, 20, 413, 16, "i"], [367, 21, 413, 17], [367, 22, 413, 18], [367, 25, 413, 21, "Object"], [367, 31, 413, 27], [367, 32, 413, 28, "assign"], [367, 38, 413, 34], [367, 39, 413, 35], [367, 40, 413, 36], [367, 41, 413, 37], [367, 43, 413, 39, "animationCopy"], [367, 56, 413, 52], [367, 57, 413, 53], [368, 10, 414, 6, "animation"], [368, 19, 414, 15], [368, 20, 414, 16, "i"], [368, 21, 414, 17], [368, 22, 414, 18], [368, 23, 414, 19, "current"], [368, 30, 414, 26], [368, 33, 414, 29, "v"], [368, 34, 414, 30], [369, 10, 415, 6, "animation"], [369, 19, 415, 15], [369, 20, 415, 16, "i"], [369, 21, 415, 17], [369, 22, 415, 18], [369, 23, 415, 19, "toValue"], [369, 30, 415, 26], [369, 33, 415, 30, "animation"], [369, 42, 415, 39], [369, 43, 415, 40, "toValue"], [369, 50, 415, 47], [369, 51, 415, 66, "i"], [369, 52, 415, 67], [369, 53, 415, 68], [370, 10, 416, 6, "animation"], [370, 19, 416, 15], [370, 20, 416, 16, "i"], [370, 21, 416, 17], [370, 22, 416, 18], [370, 23, 416, 19, "onStart"], [370, 30, 416, 26], [370, 31, 417, 8, "animation"], [370, 40, 417, 17], [370, 41, 417, 18, "i"], [370, 42, 417, 19], [370, 43, 417, 20], [370, 45, 418, 8, "v"], [370, 46, 418, 9], [370, 48, 419, 8, "timestamp"], [370, 57, 419, 17], [370, 59, 420, 8, "previousAnimation"], [370, 76, 420, 25], [370, 79, 420, 28, "previousAnimation"], [370, 96, 420, 45], [370, 97, 420, 46, "i"], [370, 98, 420, 47], [370, 99, 420, 48], [370, 102, 420, 51, "undefined"], [370, 111, 421, 6], [370, 112, 421, 7], [371, 8, 422, 4], [371, 9, 422, 5], [371, 10, 422, 6], [372, 8, 423, 4, "animation"], [372, 17, 423, 13], [372, 18, 423, 14, "current"], [372, 25, 423, 21], [372, 28, 423, 24], [372, 29, 423, 25], [372, 32, 423, 28, "value"], [372, 37, 423, 33], [372, 38, 423, 34], [373, 6, 424, 2], [373, 7, 424, 3], [374, 6, 426, 2], [374, 10, 426, 8, "arrayOnFrame"], [374, 22, 426, 20], [374, 25, 426, 23, "arrayOnFrame"], [374, 26, 427, 4, "animation"], [374, 35, 427, 41], [374, 37, 428, 4, "timestamp"], [374, 46, 428, 24], [374, 51, 429, 16], [375, 8, 430, 4], [375, 12, 430, 8, "finished"], [375, 20, 430, 16], [375, 23, 430, 19], [375, 27, 430, 23], [376, 8, 431, 5, "animation"], [376, 17, 431, 14], [376, 18, 431, 15, "current"], [376, 25, 431, 22], [376, 26, 431, 41, "for<PERSON>ach"], [376, 33, 431, 48], [376, 34, 431, 49], [376, 35, 431, 50, "_"], [376, 36, 431, 51], [376, 38, 431, 53, "i"], [376, 39, 431, 54], [376, 44, 431, 59], [377, 10, 432, 6], [377, 14, 432, 12, "result"], [377, 20, 432, 18], [377, 23, 432, 21, "animation"], [377, 32, 432, 30], [377, 33, 432, 31, "i"], [377, 34, 432, 32], [377, 35, 432, 33], [377, 36, 432, 34, "onFrame"], [377, 43, 432, 41], [377, 44, 432, 42, "animation"], [377, 53, 432, 51], [377, 54, 432, 52, "i"], [377, 55, 432, 53], [377, 56, 432, 54], [377, 58, 432, 56, "timestamp"], [377, 67, 432, 65], [377, 68, 432, 66], [378, 10, 433, 6], [379, 10, 434, 6, "finished"], [379, 18, 434, 14], [379, 21, 434, 17, "finished"], [379, 29, 434, 25], [379, 33, 434, 29, "result"], [379, 39, 434, 35], [380, 10, 435, 7, "animation"], [380, 19, 435, 16], [380, 20, 435, 17, "current"], [380, 27, 435, 24], [380, 28, 435, 43, "i"], [380, 29, 435, 44], [380, 30, 435, 45], [380, 33, 435, 48, "animation"], [380, 42, 435, 57], [380, 43, 435, 58, "i"], [380, 44, 435, 59], [380, 45, 435, 60], [380, 46, 435, 61, "current"], [380, 53, 435, 68], [381, 8, 436, 4], [381, 9, 436, 5], [381, 10, 436, 6], [382, 8, 438, 4], [382, 15, 438, 11, "finished"], [382, 23, 438, 19], [383, 6, 439, 2], [383, 7, 439, 3], [384, 6, 441, 2], [384, 10, 441, 8, "objectOnStart"], [384, 23, 441, 21], [384, 26, 441, 24, "objectOnStart"], [384, 27, 442, 4, "animation"], [384, 36, 442, 41], [384, 38, 443, 4, "value"], [384, 43, 443, 32], [384, 45, 444, 4, "timestamp"], [384, 54, 444, 24], [384, 56, 445, 4, "previousAnimation"], [384, 73, 445, 49], [384, 78, 446, 13], [385, 8, 447, 4], [385, 13, 447, 9], [385, 17, 447, 15, "key"], [385, 20, 447, 18], [385, 24, 447, 22, "value"], [385, 29, 447, 27], [385, 31, 447, 29], [386, 10, 448, 6, "animation"], [386, 19, 448, 15], [386, 20, 448, 16, "key"], [386, 23, 448, 19], [386, 24, 448, 20], [386, 27, 448, 23, "Object"], [386, 33, 448, 29], [386, 34, 448, 30, "assign"], [386, 40, 448, 36], [386, 41, 448, 37], [386, 42, 448, 38], [386, 43, 448, 39], [386, 45, 448, 41, "animationCopy"], [386, 58, 448, 54], [386, 59, 448, 55], [387, 10, 449, 6, "animation"], [387, 19, 449, 15], [387, 20, 449, 16, "key"], [387, 23, 449, 19], [387, 24, 449, 20], [387, 25, 449, 21, "onStart"], [387, 32, 449, 28], [387, 35, 449, 31, "animation"], [387, 44, 449, 40], [387, 45, 449, 41, "onStart"], [387, 52, 449, 48], [388, 10, 451, 6, "animation"], [388, 19, 451, 15], [388, 20, 451, 16, "key"], [388, 23, 451, 19], [388, 24, 451, 20], [388, 25, 451, 21, "current"], [388, 32, 451, 28], [388, 35, 451, 31, "value"], [388, 40, 451, 36], [388, 41, 451, 37, "key"], [388, 44, 451, 40], [388, 45, 451, 41], [389, 10, 452, 6, "animation"], [389, 19, 452, 15], [389, 20, 452, 16, "key"], [389, 23, 452, 19], [389, 24, 452, 20], [389, 25, 452, 21, "toValue"], [389, 32, 452, 28], [389, 35, 452, 32, "animation"], [389, 44, 452, 41], [389, 45, 452, 42, "toValue"], [389, 52, 452, 49], [389, 53, 453, 8, "key"], [389, 56, 453, 11], [389, 57, 454, 7], [390, 10, 455, 6, "animation"], [390, 19, 455, 15], [390, 20, 455, 16, "key"], [390, 23, 455, 19], [390, 24, 455, 20], [390, 25, 455, 21, "onStart"], [390, 32, 455, 28], [390, 33, 456, 8, "animation"], [390, 42, 456, 17], [390, 43, 456, 18, "key"], [390, 46, 456, 21], [390, 47, 456, 22], [390, 49, 457, 8, "value"], [390, 54, 457, 13], [390, 55, 457, 14, "key"], [390, 58, 457, 17], [390, 59, 457, 18], [390, 61, 458, 8, "timestamp"], [390, 70, 458, 17], [390, 72, 459, 8, "previousAnimation"], [390, 89, 459, 25], [390, 92, 459, 28, "previousAnimation"], [390, 109, 459, 45], [390, 110, 459, 46, "key"], [390, 113, 459, 49], [390, 114, 459, 50], [390, 117, 459, 53, "undefined"], [390, 126, 460, 6], [390, 127, 460, 7], [391, 8, 461, 4], [392, 8, 462, 4, "animation"], [392, 17, 462, 13], [392, 18, 462, 14, "current"], [392, 25, 462, 21], [392, 28, 462, 24, "value"], [392, 33, 462, 29], [393, 6, 463, 2], [393, 7, 463, 3], [394, 6, 465, 2], [394, 10, 465, 8, "objectOnFrame"], [394, 23, 465, 21], [394, 26, 465, 24, "objectOnFrame"], [394, 27, 466, 4, "animation"], [394, 36, 466, 41], [394, 38, 467, 4, "timestamp"], [394, 47, 467, 24], [394, 52, 468, 16], [395, 8, 469, 4], [395, 12, 469, 8, "finished"], [395, 20, 469, 16], [395, 23, 469, 19], [395, 27, 469, 23], [396, 8, 470, 4], [396, 12, 470, 10, "newObject"], [396, 21, 470, 42], [396, 24, 470, 45], [396, 25, 470, 46], [396, 26, 470, 47], [397, 8, 471, 4], [397, 13, 471, 9], [397, 17, 471, 15, "key"], [397, 20, 471, 18], [397, 24, 471, 22, "animation"], [397, 33, 471, 31], [397, 34, 471, 32, "current"], [397, 41, 471, 39], [397, 43, 471, 66], [398, 10, 472, 6], [398, 14, 472, 12, "result"], [398, 20, 472, 18], [398, 23, 472, 21, "animation"], [398, 32, 472, 30], [398, 33, 472, 31, "key"], [398, 36, 472, 34], [398, 37, 472, 35], [398, 38, 472, 36, "onFrame"], [398, 45, 472, 43], [398, 46, 472, 44, "animation"], [398, 55, 472, 53], [398, 56, 472, 54, "key"], [398, 59, 472, 57], [398, 60, 472, 58], [398, 62, 472, 60, "timestamp"], [398, 71, 472, 69], [398, 72, 472, 70], [399, 10, 473, 6], [400, 10, 474, 6, "finished"], [400, 18, 474, 14], [400, 21, 474, 17, "finished"], [400, 29, 474, 25], [400, 33, 474, 29, "result"], [400, 39, 474, 35], [401, 10, 475, 6, "newObject"], [401, 19, 475, 15], [401, 20, 475, 16, "key"], [401, 23, 475, 19], [401, 24, 475, 20], [401, 27, 475, 23, "animation"], [401, 36, 475, 32], [401, 37, 475, 33, "key"], [401, 40, 475, 36], [401, 41, 475, 37], [401, 42, 475, 38, "current"], [401, 49, 475, 45], [402, 8, 476, 4], [403, 8, 477, 4, "animation"], [403, 17, 477, 13], [403, 18, 477, 14, "current"], [403, 25, 477, 21], [403, 28, 477, 24, "newObject"], [403, 37, 477, 33], [404, 8, 478, 4], [404, 15, 478, 11, "finished"], [404, 23, 478, 19], [405, 6, 479, 2], [405, 7, 479, 3], [406, 6, 481, 2, "animation"], [406, 15, 481, 11], [406, 16, 481, 12, "onStart"], [406, 23, 481, 19], [406, 26, 481, 22], [406, 27, 482, 4, "animation"], [406, 36, 482, 41], [406, 38, 483, 4, "value"], [406, 43, 483, 17], [406, 45, 484, 4, "timestamp"], [406, 54, 484, 24], [406, 56, 485, 4, "previousAnimation"], [406, 73, 485, 49], [406, 78, 486, 7], [407, 8, 487, 4], [407, 12, 487, 8, "animation"], [407, 21, 487, 17], [407, 22, 487, 18, "reduceMotion"], [407, 34, 487, 30], [407, 39, 487, 35, "undefined"], [407, 48, 487, 44], [407, 50, 487, 46], [408, 10, 488, 6, "animation"], [408, 19, 488, 15], [408, 20, 488, 16, "reduceMotion"], [408, 32, 488, 28], [408, 35, 488, 31, "getReduceMotionFromConfig"], [408, 60, 488, 56], [408, 61, 488, 57], [408, 62, 488, 58], [409, 8, 489, 4], [410, 8, 490, 4], [410, 12, 490, 8, "animation"], [410, 21, 490, 17], [410, 22, 490, 18, "reduceMotion"], [410, 34, 490, 30], [410, 36, 490, 32], [411, 10, 491, 6], [411, 14, 491, 10, "animation"], [411, 23, 491, 19], [411, 24, 491, 20, "toValue"], [411, 31, 491, 27], [411, 36, 491, 32, "undefined"], [411, 45, 491, 41], [411, 47, 491, 43], [412, 12, 492, 8, "animation"], [412, 21, 492, 17], [412, 22, 492, 18, "current"], [412, 29, 492, 25], [412, 32, 492, 28, "animation"], [412, 41, 492, 37], [412, 42, 492, 38, "toValue"], [412, 49, 492, 45], [413, 10, 493, 6], [413, 11, 493, 7], [413, 17, 493, 13], [414, 12, 494, 8], [415, 12, 495, 8, "baseOnStart"], [415, 23, 495, 19], [415, 24, 495, 20, "animation"], [415, 33, 495, 29], [415, 35, 495, 31, "value"], [415, 40, 495, 36], [415, 42, 495, 38, "timestamp"], [415, 51, 495, 47], [415, 53, 495, 49, "previousAnimation"], [415, 70, 495, 66], [415, 71, 495, 67], [416, 10, 496, 6], [417, 10, 497, 6, "animation"], [417, 19, 497, 15], [417, 20, 497, 16, "startTime"], [417, 29, 497, 25], [417, 32, 497, 28], [417, 33, 497, 29], [418, 10, 498, 6, "animation"], [418, 19, 498, 15], [418, 20, 498, 16, "onFrame"], [418, 27, 498, 23], [418, 30, 498, 26], [418, 36, 498, 32], [418, 40, 498, 36], [419, 10, 499, 6], [420, 8, 500, 4], [421, 8, 501, 4], [421, 12, 501, 8], [421, 16, 501, 8, "isColor"], [421, 31, 501, 15], [421, 33, 501, 16, "value"], [421, 38, 501, 21], [421, 39, 501, 22], [421, 41, 501, 24], [422, 10, 502, 6, "colorOnStart"], [422, 22, 502, 18], [422, 23, 502, 19, "animation"], [422, 32, 502, 28], [422, 34, 502, 30, "value"], [422, 39, 502, 35], [422, 41, 502, 37, "timestamp"], [422, 50, 502, 46], [422, 52, 502, 48, "previousAnimation"], [422, 69, 502, 65], [422, 70, 502, 66], [423, 10, 503, 6, "animation"], [423, 19, 503, 15], [423, 20, 503, 16, "onFrame"], [423, 27, 503, 23], [423, 30, 503, 26, "colorOnFrame"], [423, 42, 503, 38], [424, 10, 504, 6], [425, 8, 505, 4], [425, 9, 505, 5], [425, 15, 505, 11], [425, 19, 505, 15], [425, 23, 505, 15, "isAffineMatrixFlat"], [425, 54, 505, 33], [425, 56, 505, 34, "value"], [425, 61, 505, 39], [425, 62, 505, 40], [425, 64, 505, 42], [426, 10, 506, 6, "transformationMatrixOnStart"], [426, 37, 506, 33], [426, 38, 507, 8, "animation"], [426, 47, 507, 17], [426, 49, 508, 8, "value"], [426, 54, 508, 13], [426, 56, 509, 8, "timestamp"], [426, 65, 509, 17], [426, 67, 510, 8, "previousAnimation"], [426, 84, 511, 6], [426, 85, 511, 7], [427, 10, 512, 6, "animation"], [427, 19, 512, 15], [427, 20, 512, 16, "onFrame"], [427, 27, 512, 23], [427, 30, 512, 26, "transformationMatrixOnFrame"], [427, 57, 512, 53], [428, 10, 513, 6], [429, 8, 514, 4], [429, 9, 514, 5], [429, 15, 514, 11], [429, 19, 514, 15, "Array"], [429, 24, 514, 20], [429, 25, 514, 21, "isArray"], [429, 32, 514, 28], [429, 33, 514, 29, "value"], [429, 38, 514, 34], [429, 39, 514, 35], [429, 41, 514, 37], [430, 10, 515, 6, "arrayOnStart"], [430, 22, 515, 18], [430, 23, 515, 19, "animation"], [430, 32, 515, 28], [430, 34, 515, 30, "value"], [430, 39, 515, 35], [430, 41, 515, 37, "timestamp"], [430, 50, 515, 46], [430, 52, 515, 48, "previousAnimation"], [430, 69, 515, 65], [430, 70, 515, 66], [431, 10, 516, 6, "animation"], [431, 19, 516, 15], [431, 20, 516, 16, "onFrame"], [431, 27, 516, 23], [431, 30, 516, 26, "arrayOnFrame"], [431, 42, 516, 38], [432, 10, 517, 6], [433, 8, 518, 4], [433, 9, 518, 5], [433, 15, 518, 11], [433, 19, 518, 15], [433, 26, 518, 22, "value"], [433, 31, 518, 27], [433, 36, 518, 32], [433, 44, 518, 40], [433, 46, 518, 42], [434, 10, 519, 6, "prefNumberSuffOnStart"], [434, 31, 519, 27], [434, 32, 519, 28, "animation"], [434, 41, 519, 37], [434, 43, 519, 39, "value"], [434, 48, 519, 44], [434, 50, 519, 46, "timestamp"], [434, 59, 519, 55], [434, 61, 519, 57, "previousAnimation"], [434, 78, 519, 74], [434, 79, 519, 75], [435, 10, 520, 6, "animation"], [435, 19, 520, 15], [435, 20, 520, 16, "onFrame"], [435, 27, 520, 23], [435, 30, 520, 26, "prefNumberSuffOnFrame"], [435, 51, 520, 47], [436, 10, 521, 6], [437, 8, 522, 4], [437, 9, 522, 5], [437, 15, 522, 11], [437, 19, 522, 15], [437, 26, 522, 22, "value"], [437, 31, 522, 27], [437, 36, 522, 32], [437, 44, 522, 40], [437, 48, 522, 44, "value"], [437, 53, 522, 49], [437, 58, 522, 54], [437, 62, 522, 58], [437, 64, 522, 60], [438, 10, 523, 6, "objectOnStart"], [438, 23, 523, 19], [438, 24, 523, 20, "animation"], [438, 33, 523, 29], [438, 35, 523, 31, "value"], [438, 40, 523, 36], [438, 42, 523, 38, "timestamp"], [438, 51, 523, 47], [438, 53, 523, 49, "previousAnimation"], [438, 70, 523, 66], [438, 71, 523, 67], [439, 10, 524, 6, "animation"], [439, 19, 524, 15], [439, 20, 524, 16, "onFrame"], [439, 27, 524, 23], [439, 30, 524, 26, "objectOnFrame"], [439, 43, 524, 39], [440, 10, 525, 6], [441, 8, 526, 4], [442, 8, 527, 4, "baseOnStart"], [442, 19, 527, 15], [442, 20, 527, 16, "animation"], [442, 29, 527, 25], [442, 31, 527, 27, "value"], [442, 36, 527, 32], [442, 38, 527, 34, "timestamp"], [442, 47, 527, 43], [442, 49, 527, 45, "previousAnimation"], [442, 66, 527, 62], [442, 67, 527, 63], [443, 6, 528, 2], [443, 7, 528, 3], [444, 4, 529, 0], [444, 5, 529, 1], [445, 4, 529, 1, "decorateAnimation"], [445, 21, 529, 1], [445, 22, 529, 1, "__closure"], [445, 31, 529, 1], [446, 6, 529, 1, "getReduceMotionFromConfig"], [446, 31, 529, 1], [447, 6, 529, 1, "recognizePrefixSuffix"], [447, 27, 529, 1], [448, 6, 529, 1, "isColor"], [448, 13, 529, 1], [448, 15, 266, 8, "isColor"], [448, 30, 266, 15], [449, 6, 266, 15, "toLinearSpace"], [449, 19, 266, 15], [449, 21, 267, 20, "toLinearSpace"], [449, 42, 267, 33], [450, 6, 267, 33, "convertToRGBA"], [450, 19, 267, 33], [450, 21, 267, 34, "convertToRGBA"], [450, 42, 267, 47], [451, 6, 267, 47, "clampRGBA"], [451, 15, 267, 47], [451, 17, 287, 4, "clampRGBA"], [451, 34, 287, 13], [452, 6, 287, 13, "rgbaArrayToRGBAColor"], [452, 26, 287, 13], [452, 28, 289, 24, "rgbaArrayToRGBAColor"], [452, 56, 289, 44], [453, 6, 289, 44, "toGammaSpace"], [453, 18, 289, 44], [453, 20, 290, 6, "toGammaSpace"], [453, 40, 290, 18], [454, 6, 290, 18, "decomposeMatrixIntoMatricesAndAngles"], [454, 42, 290, 18], [454, 44, 326, 30, "decomposeMatrixIntoMatricesAndAngles"], [454, 93, 326, 66], [455, 6, 326, 66, "applyProgressToMatrix"], [455, 27, 326, 66], [456, 6, 326, 66, "applyProgressToNumber"], [456, 27, 326, 66], [457, 6, 326, 66, "getRotationMatrix"], [457, 23, 326, 66], [457, 25, 381, 27, "getRotationMatrix"], [457, 55, 381, 44], [458, 6, 381, 44, "multiplyMatrices"], [458, 22, 381, 44], [458, 24, 386, 27, "multiplyMatrices"], [458, 53, 386, 43], [459, 6, 386, 43, "flatten"], [459, 13, 386, 43], [459, 15, 391, 20, "flatten"], [459, 35, 391, 27], [460, 6, 391, 27, "isAffineMatrixFlat"], [460, 24, 391, 27], [460, 26, 505, 15, "isAffineMatrixFlat"], [461, 4, 505, 33], [462, 4, 505, 33, "decorateAnimation"], [462, 21, 505, 33], [462, 22, 505, 33, "__workletHash"], [462, 35, 505, 33], [463, 4, 505, 33, "decorateAnimation"], [463, 21, 505, 33], [463, 22, 505, 33, "__initData"], [463, 32, 505, 33], [463, 35, 505, 33, "_worklet_1842068436516_init_data"], [463, 67, 505, 33], [464, 4, 505, 33, "decorateAnimation"], [464, 21, 505, 33], [464, 22, 505, 33, "__stackDetails"], [464, 36, 505, 33], [464, 39, 505, 33, "_e"], [464, 41, 505, 33], [465, 4, 505, 33], [465, 11, 505, 33, "decorateAnimation"], [465, 28, 505, 33], [466, 2, 505, 33], [466, 3, 172, 0], [467, 2, 172, 0], [467, 6, 172, 0, "_worklet_8207093411086_init_data"], [467, 38, 172, 0], [468, 4, 172, 0, "code"], [468, 8, 172, 0], [469, 4, 172, 0, "location"], [469, 12, 172, 0], [470, 4, 172, 0, "sourceMap"], [470, 13, 172, 0], [471, 4, 172, 0, "version"], [471, 11, 172, 0], [472, 2, 172, 0], [473, 2, 172, 0], [473, 6, 172, 0, "_worklet_10854716983407_init_data"], [473, 39, 172, 0], [474, 4, 172, 0, "code"], [474, 8, 172, 0], [475, 4, 172, 0, "location"], [475, 12, 172, 0], [476, 4, 172, 0, "sourceMap"], [476, 13, 172, 0], [477, 4, 172, 0, "version"], [477, 11, 172, 0], [478, 2, 172, 0], [479, 2, 172, 0], [479, 6, 172, 0, "defineAnimation"], [479, 21, 172, 0], [479, 24, 172, 0, "exports"], [479, 31, 172, 0], [479, 32, 172, 0, "defineAnimation"], [479, 47, 172, 0], [479, 50, 538, 7], [480, 4, 538, 7], [480, 8, 538, 7, "_e"], [480, 10, 538, 7], [480, 18, 538, 7, "global"], [480, 24, 538, 7], [480, 25, 538, 7, "Error"], [480, 30, 538, 7], [481, 4, 538, 7], [481, 8, 538, 7, "defineAnimation"], [481, 23, 538, 7], [481, 35, 538, 7, "defineAnimation"], [481, 36, 541, 2, "starting"], [481, 44, 541, 39], [481, 46, 541, 41, "factory"], [481, 53, 541, 57], [481, 55, 541, 62], [482, 6, 543, 2], [482, 10, 543, 6, "IN_STYLE_UPDATER"], [482, 26, 543, 22], [482, 28, 543, 24], [483, 8, 544, 4], [483, 15, 544, 11, "starting"], [483, 23, 544, 19], [484, 6, 545, 2], [485, 6, 546, 2], [485, 10, 546, 8, "create"], [485, 16, 546, 14], [485, 19, 546, 17], [486, 8, 546, 17], [486, 12, 546, 17, "_e"], [486, 14, 546, 17], [486, 22, 546, 17, "global"], [486, 28, 546, 17], [486, 29, 546, 17, "Error"], [486, 34, 546, 17], [487, 8, 546, 17], [487, 12, 546, 17, "utilTs10"], [487, 20, 546, 17], [487, 32, 546, 17, "utilTs10"], [487, 33, 546, 17], [487, 35, 546, 23], [488, 10, 548, 4], [488, 14, 548, 10, "animation"], [488, 23, 548, 19], [488, 26, 548, 22, "factory"], [488, 33, 548, 29], [488, 34, 548, 30], [488, 35, 548, 31], [489, 10, 549, 4, "decorateAnimation"], [489, 27, 549, 21], [489, 28, 549, 25, "animation"], [489, 37, 549, 50], [489, 38, 549, 51], [490, 10, 550, 4], [490, 17, 550, 11, "animation"], [490, 26, 550, 20], [491, 8, 551, 2], [491, 9, 551, 3], [492, 8, 551, 3, "utilTs10"], [492, 16, 551, 3], [492, 17, 551, 3, "__closure"], [492, 26, 551, 3], [493, 10, 551, 3, "factory"], [493, 17, 551, 3], [494, 10, 551, 3, "decorateAnimation"], [495, 8, 551, 3], [496, 8, 551, 3, "utilTs10"], [496, 16, 551, 3], [496, 17, 551, 3, "__workletHash"], [496, 30, 551, 3], [497, 8, 551, 3, "utilTs10"], [497, 16, 551, 3], [497, 17, 551, 3, "__initData"], [497, 27, 551, 3], [497, 30, 551, 3, "_worklet_10854716983407_init_data"], [497, 63, 551, 3], [498, 8, 551, 3, "utilTs10"], [498, 16, 551, 3], [498, 17, 551, 3, "__stackDetails"], [498, 31, 551, 3], [498, 34, 551, 3, "_e"], [498, 36, 551, 3], [499, 8, 551, 3], [499, 15, 551, 3, "utilTs10"], [499, 23, 551, 3], [500, 6, 551, 3], [500, 7, 546, 17], [500, 9, 551, 3], [501, 6, 553, 2], [501, 10, 553, 6, "_WORKLET"], [501, 18, 553, 14], [501, 22, 553, 18, "SHOULD_BE_USE_WEB"], [501, 39, 553, 35], [501, 41, 553, 37], [502, 8, 554, 4], [502, 15, 554, 11, "create"], [502, 21, 554, 17], [502, 22, 554, 18], [502, 23, 554, 19], [503, 6, 555, 2], [504, 6, 556, 2, "create"], [504, 12, 556, 8], [504, 13, 556, 9, "__isAnimationDefinition"], [504, 36, 556, 32], [504, 39, 556, 35], [504, 43, 556, 39], [506, 6, 558, 2], [507, 6, 559, 2], [507, 13, 559, 9, "create"], [507, 19, 559, 15], [508, 4, 560, 0], [508, 5, 560, 1], [509, 4, 560, 1, "defineAnimation"], [509, 19, 560, 1], [509, 20, 560, 1, "__closure"], [509, 29, 560, 1], [510, 6, 560, 1, "IN_STYLE_UPDATER"], [510, 22, 560, 1], [511, 6, 560, 1, "decorateAnimation"], [511, 23, 560, 1], [512, 6, 560, 1, "SHOULD_BE_USE_WEB"], [513, 4, 560, 1], [514, 4, 560, 1, "defineAnimation"], [514, 19, 560, 1], [514, 20, 560, 1, "__workletHash"], [514, 33, 560, 1], [515, 4, 560, 1, "defineAnimation"], [515, 19, 560, 1], [515, 20, 560, 1, "__initData"], [515, 30, 560, 1], [515, 33, 560, 1, "_worklet_8207093411086_init_data"], [515, 65, 560, 1], [516, 4, 560, 1, "defineAnimation"], [516, 19, 560, 1], [516, 20, 560, 1, "__stackDetails"], [516, 34, 560, 1], [516, 37, 560, 1, "_e"], [516, 39, 560, 1], [517, 4, 560, 1], [517, 11, 560, 1, "defineAnimation"], [517, 26, 560, 1], [518, 2, 560, 1], [518, 3, 538, 7], [519, 2, 538, 7], [519, 6, 538, 7, "_worklet_6696318663002_init_data"], [519, 38, 538, 7], [520, 4, 538, 7, "code"], [520, 8, 538, 7], [521, 4, 538, 7, "location"], [521, 12, 538, 7], [522, 4, 538, 7, "sourceMap"], [522, 13, 538, 7], [523, 4, 538, 7, "version"], [523, 11, 538, 7], [524, 2, 538, 7], [525, 2, 538, 7], [525, 6, 538, 7, "_worklet_1589476284506_init_data"], [525, 38, 538, 7], [526, 4, 538, 7, "code"], [526, 8, 538, 7], [527, 4, 538, 7, "location"], [527, 12, 538, 7], [528, 4, 538, 7, "sourceMap"], [528, 13, 538, 7], [529, 4, 538, 7, "version"], [529, 11, 538, 7], [530, 2, 538, 7], [531, 2, 538, 7], [531, 6, 538, 7, "cancelAnimationNative"], [531, 27, 538, 7], [531, 30, 562, 0], [532, 4, 562, 0], [532, 8, 562, 0, "_e"], [532, 10, 562, 0], [532, 18, 562, 0, "global"], [532, 24, 562, 0], [532, 25, 562, 0, "Error"], [532, 30, 562, 0], [533, 4, 562, 0], [533, 8, 562, 0, "cancelAnimationNative"], [533, 29, 562, 0], [533, 41, 562, 0, "cancelAnimationNative"], [533, 42, 562, 39, "sharedValue"], [533, 53, 562, 71], [533, 55, 562, 79], [534, 6, 564, 2], [535, 6, 565, 2], [535, 10, 565, 6, "_WORKLET"], [535, 18, 565, 14], [535, 20, 565, 16], [536, 8, 566, 4, "sharedValue"], [536, 19, 566, 15], [536, 20, 566, 16, "value"], [536, 25, 566, 21], [536, 28, 566, 24, "sharedValue"], [536, 39, 566, 35], [536, 40, 566, 36, "value"], [536, 45, 566, 41], [536, 46, 566, 42], [536, 47, 566, 43], [537, 6, 567, 2], [537, 7, 567, 3], [537, 13, 567, 9], [538, 8, 568, 4], [538, 12, 568, 4, "runOnUI"], [538, 28, 568, 11], [538, 30, 568, 12], [539, 10, 568, 12], [539, 14, 568, 12, "_e"], [539, 16, 568, 12], [539, 24, 568, 12, "global"], [539, 30, 568, 12], [539, 31, 568, 12, "Error"], [539, 36, 568, 12], [540, 10, 568, 12], [540, 14, 568, 12, "utilTs12"], [540, 22, 568, 12], [540, 34, 568, 12, "utilTs12"], [540, 35, 568, 12], [540, 37, 568, 18], [541, 12, 570, 6, "sharedValue"], [541, 23, 570, 17], [541, 24, 570, 18, "value"], [541, 29, 570, 23], [541, 32, 570, 26, "sharedValue"], [541, 43, 570, 37], [541, 44, 570, 38, "value"], [541, 49, 570, 43], [541, 50, 570, 44], [541, 51, 570, 45], [542, 10, 571, 4], [542, 11, 571, 5], [543, 10, 571, 5, "utilTs12"], [543, 18, 571, 5], [543, 19, 571, 5, "__closure"], [543, 28, 571, 5], [544, 12, 571, 5, "sharedValue"], [545, 10, 571, 5], [546, 10, 571, 5, "utilTs12"], [546, 18, 571, 5], [546, 19, 571, 5, "__workletHash"], [546, 32, 571, 5], [547, 10, 571, 5, "utilTs12"], [547, 18, 571, 5], [547, 19, 571, 5, "__initData"], [547, 29, 571, 5], [547, 32, 571, 5, "_worklet_1589476284506_init_data"], [547, 64, 571, 5], [548, 10, 571, 5, "utilTs12"], [548, 18, 571, 5], [548, 19, 571, 5, "__stackDetails"], [548, 33, 571, 5], [548, 36, 571, 5, "_e"], [548, 38, 571, 5], [549, 10, 571, 5], [549, 17, 571, 5, "utilTs12"], [549, 25, 571, 5], [550, 8, 571, 5], [550, 9, 568, 12], [550, 11, 571, 5], [550, 12, 571, 6], [550, 13, 571, 7], [550, 14, 571, 8], [551, 6, 572, 2], [552, 4, 573, 0], [552, 5, 573, 1], [553, 4, 573, 1, "cancelAnimationNative"], [553, 25, 573, 1], [553, 26, 573, 1, "__closure"], [553, 35, 573, 1], [554, 6, 573, 1, "runOnUI"], [554, 13, 573, 1], [554, 15, 568, 4, "runOnUI"], [555, 4, 568, 11], [556, 4, 568, 11, "cancelAnimationNative"], [556, 25, 568, 11], [556, 26, 568, 11, "__workletHash"], [556, 39, 568, 11], [557, 4, 568, 11, "cancelAnimationNative"], [557, 25, 568, 11], [557, 26, 568, 11, "__initData"], [557, 36, 568, 11], [557, 39, 568, 11, "_worklet_6696318663002_init_data"], [557, 71, 568, 11], [558, 4, 568, 11, "cancelAnimationNative"], [558, 25, 568, 11], [558, 26, 568, 11, "__stackDetails"], [558, 40, 568, 11], [558, 43, 568, 11, "_e"], [558, 45, 568, 11], [559, 4, 568, 11], [559, 11, 568, 11, "cancelAnimationNative"], [559, 32, 568, 11], [560, 2, 568, 11], [560, 3, 562, 0], [561, 2, 575, 0], [561, 11, 575, 9, "cancelAnimationWeb"], [561, 29, 575, 27, "cancelAnimationWeb"], [561, 30, 575, 36, "sharedValue"], [561, 41, 575, 68], [561, 43, 575, 76], [562, 4, 576, 2], [563, 4, 577, 2, "sharedValue"], [563, 15, 577, 13], [563, 16, 577, 14, "value"], [563, 21, 577, 19], [563, 24, 577, 22, "sharedValue"], [563, 35, 577, 33], [563, 36, 577, 34, "value"], [563, 41, 577, 39], [563, 42, 577, 40], [563, 43, 577, 41], [564, 2, 578, 0], [566, 2, 580, 0], [567, 0, 581, 0], [568, 0, 582, 0], [569, 0, 583, 0], [570, 0, 584, 0], [571, 0, 585, 0], [572, 0, 586, 0], [573, 0, 587, 0], [574, 2, 588, 7], [574, 6, 588, 13, "cancelAnimation"], [574, 21, 588, 28], [574, 24, 588, 28, "exports"], [574, 31, 588, 28], [574, 32, 588, 28, "cancelAnimation"], [574, 47, 588, 28], [574, 50, 588, 31, "SHOULD_BE_USE_WEB"], [574, 67, 588, 48], [574, 70, 589, 4, "cancelAnimationWeb"], [574, 88, 589, 22], [574, 91, 590, 4, "cancelAnimationNative"], [574, 112, 590, 25], [575, 0, 590, 26], [575, 3]], "functionMap": {"names": ["<global>", "isValidLayoutAnimationProp", "assertEasingIsWorklet", "initialUpdaterRun", "recognizePrefixSuffix", "getReduceMotionFromConfig", "getReduceMotionForAnimation", "applyProgressToMatrix", "applyProgressToNumber", "decorateAnimation", "animation.onStart", "prefNumberSuffOnStart", "prefNumberSuffOnFrame", "colorOnStart", "tab.forEach$argument_0", "colorOnFrame", "transformationMatrixOnStart", "transformationMatrixOnFrame", "transforms.forEach$argument_0", "rotations.forEach$argument_0", "arrayOnStart", "value.forEach$argument_0", "arrayOnFrame", "forEach$argument_0", "objectOnStart", "objectOnFrame", "animation.onFrame", "defineAnimation", "create", "cancelAnimationNative", "runOnUI$argument_0", "cancelAnimationWeb"], "mappings": "AAA;OC4D;CDG;OEQ;CFuB;OGE;CHK;OIQ;CJmB;OKO;CLK;OMM;CNS;AOE;CPO;AQE;CRG;ASE;wBCQ;KDU;gCEO;GF2C;gCGC;GHY;uBIG;gBCiB;KDW;GJQ;uBME;gBDO;KCM;GNS;sCOE;GP0B;sCQE;uBCc;ODO;sBEQ;KFO;GRsB;uBWE;kBCM;KDU;GXE;uBaE;iDCK;KDK;GbG;wBeE;GfsB;wBgBE;GhBc;sBCE;0BgBiB,UhB;GD8B;CTC;O2BS;iBCQ;GDK;C3BS;A6BE;YCM;KDG;C7BE;A+BE;C/BG"}}, "type": "js/module"}]}