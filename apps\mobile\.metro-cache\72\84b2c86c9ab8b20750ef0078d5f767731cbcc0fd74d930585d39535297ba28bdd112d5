{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../../Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 92}}], "key": "jzP+LUi0+8ZCeIUw7GN35c9PLT4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 144, "column": 0}, "end": {"line": 147, "column": 32}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/ViewConfigIgnore", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 144, "column": 0}, "end": {"line": 147, "column": 32}}], "key": "IAMNY1s5722b4GYH12DgGSx1R70=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"../../../../Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var _require = require(_dependencyMap[3], \"react-native/Libraries/NativeComponent/ViewConfigIgnore\"),\n    ConditionallyIgnoredEventHandlers = _require.ConditionallyIgnoredEventHandlers;\n  var nativeComponentName = 'RCTModalHostView';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RCTModalHostView\",\n    directEventTypes: {\n      topRequestClose: {\n        registrationName: \"onRequestClose\"\n      },\n      topShow: {\n        registrationName: \"onShow\"\n      },\n      topDismiss: {\n        registrationName: \"onDismiss\"\n      },\n      topOrientationChange: {\n        registrationName: \"onOrientationChange\"\n      }\n    },\n    validAttributes: {\n      animationType: true,\n      presentationStyle: true,\n      transparent: true,\n      statusBarTranslucent: true,\n      navigationBarTranslucent: true,\n      hardwareAccelerated: true,\n      visible: true,\n      animated: true,\n      supportedOrientations: true,\n      identifier: true,\n      ...ConditionallyIgnoredEventHandlers({\n        onRequestClose: true,\n        onShow: true,\n        onDismiss: true,\n        onOrientationChange: true\n      })\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 48, "map": [[7, 2, 19, 0], [7, 6, 19, 0, "_codegenNativeComponent"], [7, 29, 19, 0], [7, 32, 19, 0, "_interopRequireDefault"], [7, 54, 19, 0], [7, 55, 19, 0, "require"], [7, 62, 19, 0], [7, 63, 19, 0, "_dependencyMap"], [7, 77, 19, 0], [8, 2, 144, 0], [8, 6, 144, 0, "NativeComponentRegistry"], [8, 29, 147, 32], [8, 32, 144, 0, "require"], [8, 39, 147, 32], [8, 40, 147, 32, "_dependencyMap"], [8, 54, 147, 32], [8, 123, 147, 31], [8, 124, 147, 32], [9, 2, 144, 0], [9, 6, 144, 0, "_require"], [9, 14, 144, 0], [9, 17, 144, 0, "require"], [9, 24, 147, 32], [9, 25, 147, 32, "_dependencyMap"], [9, 39, 147, 32], [9, 101, 147, 31], [9, 102, 147, 32], [10, 4, 144, 0, "ConditionallyIgnoredEventHandlers"], [10, 37, 147, 32], [10, 40, 147, 32, "_require"], [10, 48, 147, 32], [10, 49, 144, 0, "ConditionallyIgnoredEventHandlers"], [10, 82, 147, 32], [11, 2, 144, 0], [11, 6, 144, 0, "nativeComponentName"], [11, 25, 147, 32], [11, 28, 144, 0], [11, 46, 147, 32], [12, 2, 144, 0], [12, 6, 144, 0, "__INTERNAL_VIEW_CONFIG"], [12, 28, 147, 32], [12, 31, 147, 32, "exports"], [12, 38, 147, 32], [12, 39, 147, 32, "__INTERNAL_VIEW_CONFIG"], [12, 61, 147, 32], [12, 64, 144, 0], [13, 4, 144, 0, "uiViewClassName"], [13, 19, 147, 32], [13, 21, 144, 0], [13, 39, 147, 32], [14, 4, 144, 0, "directEventTypes"], [14, 20, 147, 32], [14, 22, 144, 0], [15, 6, 144, 0, "topRequestClose"], [15, 21, 147, 32], [15, 23, 144, 0], [16, 8, 144, 0, "registrationName"], [16, 24, 147, 32], [16, 26, 144, 0], [17, 6, 147, 31], [17, 7, 147, 32], [18, 6, 144, 0, "topShow"], [18, 13, 147, 32], [18, 15, 144, 0], [19, 8, 144, 0, "registrationName"], [19, 24, 147, 32], [19, 26, 144, 0], [20, 6, 147, 31], [20, 7, 147, 32], [21, 6, 144, 0, "topDismiss"], [21, 16, 147, 32], [21, 18, 144, 0], [22, 8, 144, 0, "registrationName"], [22, 24, 147, 32], [22, 26, 144, 0], [23, 6, 147, 31], [23, 7, 147, 32], [24, 6, 144, 0, "topOrientationChange"], [24, 26, 147, 32], [24, 28, 144, 0], [25, 8, 144, 0, "registrationName"], [25, 24, 147, 32], [25, 26, 144, 0], [26, 6, 147, 31], [27, 4, 147, 31], [27, 5, 147, 32], [28, 4, 144, 0, "validAttributes"], [28, 19, 147, 32], [28, 21, 144, 0], [29, 6, 144, 0, "animationType"], [29, 19, 147, 32], [29, 21, 144, 0], [29, 25, 147, 32], [30, 6, 144, 0, "presentationStyle"], [30, 23, 147, 32], [30, 25, 144, 0], [30, 29, 147, 32], [31, 6, 144, 0, "transparent"], [31, 17, 147, 32], [31, 19, 144, 0], [31, 23, 147, 32], [32, 6, 144, 0, "statusBarTranslucent"], [32, 26, 147, 32], [32, 28, 144, 0], [32, 32, 147, 32], [33, 6, 144, 0, "navigationBarTranslucent"], [33, 30, 147, 32], [33, 32, 144, 0], [33, 36, 147, 32], [34, 6, 144, 0, "hardwareAccelerated"], [34, 25, 147, 32], [34, 27, 144, 0], [34, 31, 147, 32], [35, 6, 144, 0, "visible"], [35, 13, 147, 32], [35, 15, 144, 0], [35, 19, 147, 32], [36, 6, 144, 0, "animated"], [36, 14, 147, 32], [36, 16, 144, 0], [36, 20, 147, 32], [37, 6, 144, 0, "supportedOrientations"], [37, 27, 147, 32], [37, 29, 144, 0], [37, 33, 147, 32], [38, 6, 144, 0, "identifier"], [38, 16, 147, 32], [38, 18, 144, 0], [38, 22, 147, 32], [39, 6, 144, 0], [39, 9, 144, 0, "ConditionallyIgnoredEventHandlers"], [39, 42, 147, 32], [39, 43, 144, 0], [40, 8, 144, 0, "onRequestClose"], [40, 22, 147, 32], [40, 24, 144, 0], [40, 28, 147, 32], [41, 8, 144, 0, "onShow"], [41, 14, 147, 32], [41, 16, 144, 0], [41, 20, 147, 32], [42, 8, 144, 0, "on<PERSON><PERSON><PERSON>"], [42, 17, 147, 32], [42, 19, 144, 0], [42, 23, 147, 32], [43, 8, 144, 0, "onOrientationChange"], [43, 27, 147, 32], [43, 29, 144, 0], [44, 6, 147, 31], [45, 4, 147, 31], [46, 2, 147, 31], [46, 3, 147, 32], [47, 2, 147, 32], [47, 6, 147, 32, "_default"], [47, 14, 147, 32], [47, 17, 147, 32, "exports"], [47, 24, 147, 32], [47, 25, 147, 32, "default"], [47, 32, 147, 32], [47, 35, 144, 0, "NativeComponentRegistry"], [47, 58, 147, 32], [47, 59, 144, 0, "get"], [47, 62, 147, 32], [47, 63, 144, 0, "nativeComponentName"], [47, 82, 147, 32], [47, 84, 144, 0], [47, 90, 144, 0, "__INTERNAL_VIEW_CONFIG"], [47, 112, 147, 31], [47, 113, 147, 32], [48, 0, 147, 32], [48, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}