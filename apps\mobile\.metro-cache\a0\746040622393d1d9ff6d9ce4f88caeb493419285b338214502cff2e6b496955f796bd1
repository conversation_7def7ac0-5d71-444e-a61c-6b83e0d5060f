{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 42}, "end": {"line": 8, "column": 22, "index": 138}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 140}, "end": {"line": 9, "column": 62, "index": 202}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo/vector-icons", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 204}, "end": {"line": 10, "column": 46, "index": 250}}], "key": "ow7vkrqkIckRjlSi/+MhMmRYtUE=", "exportNames": ["*"]}}, {"name": "../components/FooterNavigation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 252}, "end": {"line": 11, "column": 62, "index": 314}}], "key": "i0wsjvqzeC9AyK9fcghZiqZFu6Y=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = WishlistScreen;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[4], \"react-native-safe-area-context\");\n  var _vectorIcons = require(_dependencyMap[5], \"@expo/vector-icons\");\n  var _FooterNavigation = _interopRequireDefault(require(_dependencyMap[6], \"../components/FooterNavigation\"));\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\apps\\\\mobile\\\\src\\\\screens\\\\WishlistScreen.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function WishlistScreen(_ref) {\n    _s();\n    var navigation = _ref.navigation;\n    var _useState = (0, _react.useState)([{\n        id: '1',\n        name: 'Margherita Pizza',\n        restaurant: 'Pizza Corner',\n        price: 18.99,\n        image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',\n        rating: 4.6,\n        deliveryTime: '20-30 min',\n        description: 'Fresh tomatoes, mozzarella, basil'\n      }, {\n        id: '2',\n        name: 'Chicken Burger',\n        restaurant: 'Burger Palace',\n        price: 12.99,\n        image: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400',\n        rating: 4.8,\n        deliveryTime: '15-25 min',\n        description: 'Grilled chicken, lettuce, tomato, mayo'\n      }, {\n        id: '3',\n        name: 'Salmon Sushi Roll',\n        restaurant: 'Sushi Express',\n        price: 24.99,\n        image: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=400',\n        rating: 4.9,\n        deliveryTime: '25-35 min',\n        description: 'Fresh salmon, avocado, cucumber'\n      }, {\n        id: '4',\n        name: 'Caesar Salad',\n        restaurant: 'Green Bowl',\n        price: 9.99,\n        image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400',\n        rating: 4.7,\n        deliveryTime: '18-28 min',\n        description: 'Romaine lettuce, parmesan, croutons'\n      }]),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      wishlistItems = _useState2[0],\n      setWishlistItems = _useState2[1];\n    var removeFromWishlist = id => {\n      setWishlistItems(items => items.filter(item => item.id !== id));\n    };\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: {\n        flex: 1,\n        backgroundColor: '#f9fafb'\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f3a823'\n        },\n        edges: ['top']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: {\n          flex: 1,\n          backgroundColor: '#f9fafb'\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            flexDirection: 'row',\n            alignItems: 'center',\n            paddingHorizontal: 16,\n            paddingVertical: 12,\n            backgroundColor: '#fff',\n            borderBottomWidth: 1,\n            borderBottomColor: '#e5e7eb'\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n            onPress: () => navigation.goBack(),\n            style: {\n              padding: 8,\n              marginRight: 8\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n              name: \"arrow-back\",\n              size: 24,\n              color: \"#111827\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 9\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 24,\n              fontWeight: 'bold',\n              color: '#111827',\n              flex: 1\n            },\n            children: \"Wishlist\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 9\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              backgroundColor: '#f97316',\n              paddingHorizontal: 12,\n              paddingVertical: 6,\n              borderRadius: 16\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                color: '#fff',\n                fontWeight: '600',\n                fontSize: 14\n              },\n              children: [wishlistItems.length, \" items\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 9\n        }, this), wishlistItems.length === 0 ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            flex: 1,\n            justifyContent: 'center',\n            alignItems: 'center',\n            paddingHorizontal: 32\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              width: 120,\n              height: 120,\n              backgroundColor: '#f3f4f6',\n              borderRadius: 60,\n              alignItems: 'center',\n              justifyContent: 'center',\n              marginBottom: 24\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n              name: \"heart-outline\",\n              size: 60,\n              color: \"#9ca3af\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 24,\n              fontWeight: 'bold',\n              color: '#111827',\n              marginBottom: 8,\n              textAlign: 'center'\n            },\n            children: \"Your wishlist is empty\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              color: '#6b7280',\n              textAlign: 'center',\n              marginBottom: 32,\n              lineHeight: 20\n            },\n            children: \"Save your favorite dishes and restaurants to order them later!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n            onPress: () => navigation.navigate('Home'),\n            style: {\n              backgroundColor: '#f97316',\n              paddingHorizontal: 32,\n              paddingVertical: 16,\n              borderRadius: 12\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                color: '#fff',\n                fontWeight: '600',\n                fontSize: 16\n              },\n              children: \"Explore Restaurants\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 9\n        }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.ScrollView, {\n          style: {\n            flex: 1\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              padding: 16\n            },\n            children: wishlistItems.map(item => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: {\n                backgroundColor: '#fff',\n                borderRadius: 16,\n                marginBottom: 16,\n                shadowColor: '#000',\n                shadowOffset: {\n                  width: 0,\n                  height: 2\n                },\n                shadowOpacity: 0.1,\n                shadowRadius: 4,\n                elevation: 3\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Image, {\n                source: {\n                  uri: item.image\n                },\n                style: {\n                  width: '100%',\n                  height: 160,\n                  borderTopLeftRadius: 16,\n                  borderTopRightRadius: 16\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: {\n                  padding: 16\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                  style: {\n                    flexDirection: 'row',\n                    justifyContent: 'space-between',\n                    alignItems: 'flex-start',\n                    marginBottom: 8\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        fontSize: 18,\n                        fontWeight: 'bold',\n                        color: '#111827',\n                        marginBottom: 4\n                      },\n                      children: item.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        color: '#6b7280',\n                        fontSize: 14,\n                        marginBottom: 4\n                      },\n                      children: [\"from \", item.restaurant]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 164,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        color: '#9ca3af',\n                        fontSize: 14,\n                        marginBottom: 8\n                      },\n                      children: item.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 167,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                      style: {\n                        flexDirection: 'row',\n                        alignItems: 'center',\n                        marginBottom: 12\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                        name: \"star\",\n                        size: 16,\n                        color: \"#fbbf24\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 171,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                        style: {\n                          marginLeft: 4,\n                          color: '#6b7280',\n                          fontSize: 14\n                        },\n                        children: [item.rating, \" \\u2022 \", item.deliveryTime]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 172,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n                    onPress: () => removeFromWishlist(item.id),\n                    style: {\n                      padding: 8,\n                      backgroundColor: '#fef2f2',\n                      borderRadius: 8\n                    },\n                    children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                      name: \"heart\",\n                      size: 20,\n                      color: \"#ef4444\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                  style: {\n                    flexDirection: 'row',\n                    justifyContent: 'space-between',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                    style: {\n                      fontSize: 20,\n                      fontWeight: 'bold',\n                      color: '#f97316'\n                    },\n                    children: [\"$\", item.price.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n                    style: {\n                      backgroundColor: '#f97316',\n                      paddingHorizontal: 20,\n                      paddingVertical: 10,\n                      borderRadius: 8\n                    },\n                    children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        color: '#fff',\n                        fontWeight: '600',\n                        fontSize: 14\n                      },\n                      children: \"Add to Cart\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              height: 100\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_FooterNavigation.default, {\n        navigation: navigation,\n        activeScreen: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f9fafb'\n        },\n        edges: ['bottom']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 5\n    }, this);\n  }\n  _s(WishlistScreen, \"JAbhCYBLKdhf6NcmX+YEwyUF77Y=\");\n  _c = WishlistScreen;\n  var _c;\n  $RefreshReg$(_c, \"WishlistScreen\");\n});", "lineCount": 485, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireWildcard"], [8, 38, 1, 0], [8, 39, 1, 0, "require"], [8, 46, 1, 0], [8, 47, 1, 0, "_dependencyMap"], [8, 61, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 9, 0], [10, 6, 9, 0, "_reactNativeSafeAreaContext"], [10, 33, 9, 0], [10, 36, 9, 0, "require"], [10, 43, 9, 0], [10, 44, 9, 0, "_dependencyMap"], [10, 58, 9, 0], [11, 2, 10, 0], [11, 6, 10, 0, "_vectorIcons"], [11, 18, 10, 0], [11, 21, 10, 0, "require"], [11, 28, 10, 0], [11, 29, 10, 0, "_dependencyMap"], [11, 43, 10, 0], [12, 2, 11, 0], [12, 6, 11, 0, "_FooterNavigation"], [12, 23, 11, 0], [12, 26, 11, 0, "_interopRequireDefault"], [12, 48, 11, 0], [12, 49, 11, 0, "require"], [12, 56, 11, 0], [12, 57, 11, 0, "_dependencyMap"], [12, 71, 11, 0], [13, 2, 11, 62], [13, 6, 11, 62, "_jsxDevRuntime"], [13, 20, 11, 62], [13, 23, 11, 62, "require"], [13, 30, 11, 62], [13, 31, 11, 62, "_dependencyMap"], [13, 45, 11, 62], [14, 2, 11, 62], [14, 6, 11, 62, "_jsxFileName"], [14, 18, 11, 62], [15, 4, 11, 62, "_s"], [15, 6, 11, 62], [15, 9, 11, 62, "$RefreshSig$"], [15, 21, 11, 62], [16, 2, 11, 62], [16, 11, 11, 62, "_interopRequireWildcard"], [16, 35, 11, 62, "e"], [16, 36, 11, 62], [16, 38, 11, 62, "t"], [16, 39, 11, 62], [16, 68, 11, 62, "WeakMap"], [16, 75, 11, 62], [16, 81, 11, 62, "r"], [16, 82, 11, 62], [16, 89, 11, 62, "WeakMap"], [16, 96, 11, 62], [16, 100, 11, 62, "n"], [16, 101, 11, 62], [16, 108, 11, 62, "WeakMap"], [16, 115, 11, 62], [16, 127, 11, 62, "_interopRequireWildcard"], [16, 150, 11, 62], [16, 162, 11, 62, "_interopRequireWildcard"], [16, 163, 11, 62, "e"], [16, 164, 11, 62], [16, 166, 11, 62, "t"], [16, 167, 11, 62], [16, 176, 11, 62, "t"], [16, 177, 11, 62], [16, 181, 11, 62, "e"], [16, 182, 11, 62], [16, 186, 11, 62, "e"], [16, 187, 11, 62], [16, 188, 11, 62, "__esModule"], [16, 198, 11, 62], [16, 207, 11, 62, "e"], [16, 208, 11, 62], [16, 214, 11, 62, "o"], [16, 215, 11, 62], [16, 217, 11, 62, "i"], [16, 218, 11, 62], [16, 220, 11, 62, "f"], [16, 221, 11, 62], [16, 226, 11, 62, "__proto__"], [16, 235, 11, 62], [16, 243, 11, 62, "default"], [16, 250, 11, 62], [16, 252, 11, 62, "e"], [16, 253, 11, 62], [16, 270, 11, 62, "e"], [16, 271, 11, 62], [16, 294, 11, 62, "e"], [16, 295, 11, 62], [16, 320, 11, 62, "e"], [16, 321, 11, 62], [16, 330, 11, 62, "f"], [16, 331, 11, 62], [16, 337, 11, 62, "o"], [16, 338, 11, 62], [16, 341, 11, 62, "t"], [16, 342, 11, 62], [16, 345, 11, 62, "n"], [16, 346, 11, 62], [16, 349, 11, 62, "r"], [16, 350, 11, 62], [16, 358, 11, 62, "o"], [16, 359, 11, 62], [16, 360, 11, 62, "has"], [16, 363, 11, 62], [16, 364, 11, 62, "e"], [16, 365, 11, 62], [16, 375, 11, 62, "o"], [16, 376, 11, 62], [16, 377, 11, 62, "get"], [16, 380, 11, 62], [16, 381, 11, 62, "e"], [16, 382, 11, 62], [16, 385, 11, 62, "o"], [16, 386, 11, 62], [16, 387, 11, 62, "set"], [16, 390, 11, 62], [16, 391, 11, 62, "e"], [16, 392, 11, 62], [16, 394, 11, 62, "f"], [16, 395, 11, 62], [16, 409, 11, 62, "_t"], [16, 411, 11, 62], [16, 415, 11, 62, "e"], [16, 416, 11, 62], [16, 432, 11, 62, "_t"], [16, 434, 11, 62], [16, 441, 11, 62, "hasOwnProperty"], [16, 455, 11, 62], [16, 456, 11, 62, "call"], [16, 460, 11, 62], [16, 461, 11, 62, "e"], [16, 462, 11, 62], [16, 464, 11, 62, "_t"], [16, 466, 11, 62], [16, 473, 11, 62, "i"], [16, 474, 11, 62], [16, 478, 11, 62, "o"], [16, 479, 11, 62], [16, 482, 11, 62, "Object"], [16, 488, 11, 62], [16, 489, 11, 62, "defineProperty"], [16, 503, 11, 62], [16, 508, 11, 62, "Object"], [16, 514, 11, 62], [16, 515, 11, 62, "getOwnPropertyDescriptor"], [16, 539, 11, 62], [16, 540, 11, 62, "e"], [16, 541, 11, 62], [16, 543, 11, 62, "_t"], [16, 545, 11, 62], [16, 552, 11, 62, "i"], [16, 553, 11, 62], [16, 554, 11, 62, "get"], [16, 557, 11, 62], [16, 561, 11, 62, "i"], [16, 562, 11, 62], [16, 563, 11, 62, "set"], [16, 566, 11, 62], [16, 570, 11, 62, "o"], [16, 571, 11, 62], [16, 572, 11, 62, "f"], [16, 573, 11, 62], [16, 575, 11, 62, "_t"], [16, 577, 11, 62], [16, 579, 11, 62, "i"], [16, 580, 11, 62], [16, 584, 11, 62, "f"], [16, 585, 11, 62], [16, 586, 11, 62, "_t"], [16, 588, 11, 62], [16, 592, 11, 62, "e"], [16, 593, 11, 62], [16, 594, 11, 62, "_t"], [16, 596, 11, 62], [16, 607, 11, 62, "f"], [16, 608, 11, 62], [16, 613, 11, 62, "e"], [16, 614, 11, 62], [16, 616, 11, 62, "t"], [16, 617, 11, 62], [17, 2, 13, 15], [17, 11, 13, 24, "WishlistScreen"], [17, 25, 13, 38, "WishlistScreen"], [17, 26, 13, 38, "_ref"], [17, 30, 13, 38], [17, 32, 13, 60], [18, 4, 13, 60, "_s"], [18, 6, 13, 60], [19, 4, 13, 60], [19, 8, 13, 41, "navigation"], [19, 18, 13, 51], [19, 21, 13, 51, "_ref"], [19, 25, 13, 51], [19, 26, 13, 41, "navigation"], [19, 36, 13, 51], [20, 4, 14, 2], [20, 8, 14, 2, "_useState"], [20, 17, 14, 2], [20, 20, 14, 44], [20, 24, 14, 44, "useState"], [20, 39, 14, 52], [20, 41, 14, 53], [20, 42, 15, 4], [21, 8, 16, 6, "id"], [21, 10, 16, 8], [21, 12, 16, 10], [21, 15, 16, 13], [22, 8, 17, 6, "name"], [22, 12, 17, 10], [22, 14, 17, 12], [22, 32, 17, 30], [23, 8, 18, 6, "restaurant"], [23, 18, 18, 16], [23, 20, 18, 18], [23, 34, 18, 32], [24, 8, 19, 6, "price"], [24, 13, 19, 11], [24, 15, 19, 13], [24, 20, 19, 18], [25, 8, 20, 6, "image"], [25, 13, 20, 11], [25, 15, 20, 13], [25, 83, 20, 81], [26, 8, 21, 6, "rating"], [26, 14, 21, 12], [26, 16, 21, 14], [26, 19, 21, 17], [27, 8, 22, 6, "deliveryTime"], [27, 20, 22, 18], [27, 22, 22, 20], [27, 33, 22, 31], [28, 8, 23, 6, "description"], [28, 19, 23, 17], [28, 21, 23, 19], [29, 6, 24, 4], [29, 7, 24, 5], [29, 9, 25, 4], [30, 8, 26, 6, "id"], [30, 10, 26, 8], [30, 12, 26, 10], [30, 15, 26, 13], [31, 8, 27, 6, "name"], [31, 12, 27, 10], [31, 14, 27, 12], [31, 30, 27, 28], [32, 8, 28, 6, "restaurant"], [32, 18, 28, 16], [32, 20, 28, 18], [32, 35, 28, 33], [33, 8, 29, 6, "price"], [33, 13, 29, 11], [33, 15, 29, 13], [33, 20, 29, 18], [34, 8, 30, 6, "image"], [34, 13, 30, 11], [34, 15, 30, 13], [34, 83, 30, 81], [35, 8, 31, 6, "rating"], [35, 14, 31, 12], [35, 16, 31, 14], [35, 19, 31, 17], [36, 8, 32, 6, "deliveryTime"], [36, 20, 32, 18], [36, 22, 32, 20], [36, 33, 32, 31], [37, 8, 33, 6, "description"], [37, 19, 33, 17], [37, 21, 33, 19], [38, 6, 34, 4], [38, 7, 34, 5], [38, 9, 35, 4], [39, 8, 36, 6, "id"], [39, 10, 36, 8], [39, 12, 36, 10], [39, 15, 36, 13], [40, 8, 37, 6, "name"], [40, 12, 37, 10], [40, 14, 37, 12], [40, 33, 37, 31], [41, 8, 38, 6, "restaurant"], [41, 18, 38, 16], [41, 20, 38, 18], [41, 35, 38, 33], [42, 8, 39, 6, "price"], [42, 13, 39, 11], [42, 15, 39, 13], [42, 20, 39, 18], [43, 8, 40, 6, "image"], [43, 13, 40, 11], [43, 15, 40, 13], [43, 83, 40, 81], [44, 8, 41, 6, "rating"], [44, 14, 41, 12], [44, 16, 41, 14], [44, 19, 41, 17], [45, 8, 42, 6, "deliveryTime"], [45, 20, 42, 18], [45, 22, 42, 20], [45, 33, 42, 31], [46, 8, 43, 6, "description"], [46, 19, 43, 17], [46, 21, 43, 19], [47, 6, 44, 4], [47, 7, 44, 5], [47, 9, 45, 4], [48, 8, 46, 6, "id"], [48, 10, 46, 8], [48, 12, 46, 10], [48, 15, 46, 13], [49, 8, 47, 6, "name"], [49, 12, 47, 10], [49, 14, 47, 12], [49, 28, 47, 26], [50, 8, 48, 6, "restaurant"], [50, 18, 48, 16], [50, 20, 48, 18], [50, 32, 48, 30], [51, 8, 49, 6, "price"], [51, 13, 49, 11], [51, 15, 49, 13], [51, 19, 49, 17], [52, 8, 50, 6, "image"], [52, 13, 50, 11], [52, 15, 50, 13], [52, 83, 50, 81], [53, 8, 51, 6, "rating"], [53, 14, 51, 12], [53, 16, 51, 14], [53, 19, 51, 17], [54, 8, 52, 6, "deliveryTime"], [54, 20, 52, 18], [54, 22, 52, 20], [54, 33, 52, 31], [55, 8, 53, 6, "description"], [55, 19, 53, 17], [55, 21, 53, 19], [56, 6, 54, 4], [56, 7, 54, 5], [56, 8, 55, 3], [56, 9, 55, 4], [57, 6, 55, 4, "_useState2"], [57, 16, 55, 4], [57, 23, 55, 4, "_slicedToArray2"], [57, 38, 55, 4], [57, 39, 55, 4, "default"], [57, 46, 55, 4], [57, 48, 55, 4, "_useState"], [57, 57, 55, 4], [58, 6, 14, 9, "wishlistItems"], [58, 19, 14, 22], [58, 22, 14, 22, "_useState2"], [58, 32, 14, 22], [59, 6, 14, 24, "setWishlistItems"], [59, 22, 14, 40], [59, 25, 14, 40, "_useState2"], [59, 35, 14, 40], [60, 4, 57, 2], [60, 8, 57, 8, "removeFromWishlist"], [60, 26, 57, 26], [60, 29, 57, 30, "id"], [60, 31, 57, 40], [60, 35, 57, 45], [61, 6, 58, 4, "setWishlistItems"], [61, 22, 58, 20], [61, 23, 58, 21, "items"], [61, 28, 58, 26], [61, 32, 58, 30, "items"], [61, 37, 58, 35], [61, 38, 58, 36, "filter"], [61, 44, 58, 42], [61, 45, 58, 43, "item"], [61, 49, 58, 47], [61, 53, 58, 51, "item"], [61, 57, 58, 55], [61, 58, 58, 56, "id"], [61, 60, 58, 58], [61, 65, 58, 63, "id"], [61, 67, 58, 65], [61, 68, 58, 66], [61, 69, 58, 67], [62, 4, 59, 2], [62, 5, 59, 3], [63, 4, 61, 2], [63, 24, 62, 4], [63, 28, 62, 4, "_jsxDevRuntime"], [63, 42, 62, 4], [63, 43, 62, 4, "jsxDEV"], [63, 49, 62, 4], [63, 51, 62, 5, "_reactNative"], [63, 63, 62, 5], [63, 64, 62, 5, "View"], [63, 68, 62, 9], [64, 6, 62, 10, "style"], [64, 11, 62, 15], [64, 13, 62, 17], [65, 8, 62, 19, "flex"], [65, 12, 62, 23], [65, 14, 62, 25], [65, 15, 62, 26], [66, 8, 62, 28, "backgroundColor"], [66, 23, 62, 43], [66, 25, 62, 45], [67, 6, 62, 55], [67, 7, 62, 57], [68, 6, 62, 57, "children"], [68, 14, 62, 57], [68, 30, 64, 6], [68, 34, 64, 6, "_jsxDevRuntime"], [68, 48, 64, 6], [68, 49, 64, 6, "jsxDEV"], [68, 55, 64, 6], [68, 57, 64, 7, "_reactNativeSafeAreaContext"], [68, 84, 64, 7], [68, 85, 64, 7, "SafeAreaView"], [68, 97, 64, 19], [69, 8, 64, 20, "style"], [69, 13, 64, 25], [69, 15, 64, 27], [70, 10, 64, 29, "backgroundColor"], [70, 25, 64, 44], [70, 27, 64, 46], [71, 8, 64, 56], [71, 9, 64, 58], [72, 8, 64, 59, "edges"], [72, 13, 64, 64], [72, 15, 64, 66], [72, 16, 64, 67], [72, 21, 64, 72], [73, 6, 64, 74], [74, 8, 64, 74, "fileName"], [74, 16, 64, 74], [74, 18, 64, 74, "_jsxFileName"], [74, 30, 64, 74], [75, 8, 64, 74, "lineNumber"], [75, 18, 64, 74], [76, 8, 64, 74, "columnNumber"], [76, 20, 64, 74], [77, 6, 64, 74], [77, 13, 64, 76], [77, 14, 64, 77], [77, 29, 67, 6], [77, 33, 67, 6, "_jsxDevRuntime"], [77, 47, 67, 6], [77, 48, 67, 6, "jsxDEV"], [77, 54, 67, 6], [77, 56, 67, 7, "_reactNative"], [77, 68, 67, 7], [77, 69, 67, 7, "View"], [77, 73, 67, 11], [78, 8, 67, 12, "style"], [78, 13, 67, 17], [78, 15, 67, 19], [79, 10, 67, 21, "flex"], [79, 14, 67, 25], [79, 16, 67, 27], [79, 17, 67, 28], [80, 10, 67, 30, "backgroundColor"], [80, 25, 67, 45], [80, 27, 67, 47], [81, 8, 67, 57], [81, 9, 67, 59], [82, 8, 67, 59, "children"], [82, 16, 67, 59], [82, 32, 69, 8], [82, 36, 69, 8, "_jsxDevRuntime"], [82, 50, 69, 8], [82, 51, 69, 8, "jsxDEV"], [82, 57, 69, 8], [82, 59, 69, 9, "_reactNative"], [82, 71, 69, 9], [82, 72, 69, 9, "View"], [82, 76, 69, 13], [83, 10, 69, 14, "style"], [83, 15, 69, 19], [83, 17, 69, 21], [84, 12, 70, 10, "flexDirection"], [84, 25, 70, 23], [84, 27, 70, 25], [84, 32, 70, 30], [85, 12, 71, 10, "alignItems"], [85, 22, 71, 20], [85, 24, 71, 22], [85, 32, 71, 30], [86, 12, 72, 10, "paddingHorizontal"], [86, 29, 72, 27], [86, 31, 72, 29], [86, 33, 72, 31], [87, 12, 73, 10, "paddingVertical"], [87, 27, 73, 25], [87, 29, 73, 27], [87, 31, 73, 29], [88, 12, 74, 10, "backgroundColor"], [88, 27, 74, 25], [88, 29, 74, 27], [88, 35, 74, 33], [89, 12, 75, 10, "borderBottomWidth"], [89, 29, 75, 27], [89, 31, 75, 29], [89, 32, 75, 30], [90, 12, 76, 10, "borderBottomColor"], [90, 29, 76, 27], [90, 31, 76, 29], [91, 10, 77, 8], [91, 11, 77, 10], [92, 10, 77, 10, "children"], [92, 18, 77, 10], [92, 34, 78, 8], [92, 38, 78, 8, "_jsxDevRuntime"], [92, 52, 78, 8], [92, 53, 78, 8, "jsxDEV"], [92, 59, 78, 8], [92, 61, 78, 9, "_reactNative"], [92, 73, 78, 9], [92, 74, 78, 9, "TouchableOpacity"], [92, 90, 78, 25], [93, 12, 79, 10, "onPress"], [93, 19, 79, 17], [93, 21, 79, 19, "onPress"], [93, 22, 79, 19], [93, 27, 79, 25, "navigation"], [93, 37, 79, 35], [93, 38, 79, 36, "goBack"], [93, 44, 79, 42], [93, 45, 79, 43], [93, 46, 79, 45], [94, 12, 80, 10, "style"], [94, 17, 80, 15], [94, 19, 80, 17], [95, 14, 80, 19, "padding"], [95, 21, 80, 26], [95, 23, 80, 28], [95, 24, 80, 29], [96, 14, 80, 31, "marginRight"], [96, 25, 80, 42], [96, 27, 80, 44], [97, 12, 80, 46], [97, 13, 80, 48], [98, 12, 80, 48, "children"], [98, 20, 80, 48], [98, 35, 82, 10], [98, 39, 82, 10, "_jsxDevRuntime"], [98, 53, 82, 10], [98, 54, 82, 10, "jsxDEV"], [98, 60, 82, 10], [98, 62, 82, 11, "_vectorIcons"], [98, 74, 82, 11], [98, 75, 82, 11, "Ionicons"], [98, 83, 82, 19], [99, 14, 82, 20, "name"], [99, 18, 82, 24], [99, 20, 82, 25], [99, 32, 82, 37], [100, 14, 82, 38, "size"], [100, 18, 82, 42], [100, 20, 82, 44], [100, 22, 82, 47], [101, 14, 82, 48, "color"], [101, 19, 82, 53], [101, 21, 82, 54], [102, 12, 82, 63], [103, 14, 82, 63, "fileName"], [103, 22, 82, 63], [103, 24, 82, 63, "_jsxFileName"], [103, 36, 82, 63], [104, 14, 82, 63, "lineNumber"], [104, 24, 82, 63], [105, 14, 82, 63, "columnNumber"], [105, 26, 82, 63], [106, 12, 82, 63], [106, 19, 82, 65], [107, 10, 82, 66], [108, 12, 82, 66, "fileName"], [108, 20, 82, 66], [108, 22, 82, 66, "_jsxFileName"], [108, 34, 82, 66], [109, 12, 82, 66, "lineNumber"], [109, 22, 82, 66], [110, 12, 82, 66, "columnNumber"], [110, 24, 82, 66], [111, 10, 82, 66], [111, 17, 83, 26], [111, 18, 83, 27], [111, 33, 84, 8], [111, 37, 84, 8, "_jsxDevRuntime"], [111, 51, 84, 8], [111, 52, 84, 8, "jsxDEV"], [111, 58, 84, 8], [111, 60, 84, 9, "_reactNative"], [111, 72, 84, 9], [111, 73, 84, 9, "Text"], [111, 77, 84, 13], [112, 12, 84, 14, "style"], [112, 17, 84, 19], [112, 19, 84, 21], [113, 14, 84, 23, "fontSize"], [113, 22, 84, 31], [113, 24, 84, 33], [113, 26, 84, 35], [114, 14, 84, 37, "fontWeight"], [114, 24, 84, 47], [114, 26, 84, 49], [114, 32, 84, 55], [115, 14, 84, 57, "color"], [115, 19, 84, 62], [115, 21, 84, 64], [115, 30, 84, 73], [116, 14, 84, 75, "flex"], [116, 18, 84, 79], [116, 20, 84, 81], [117, 12, 84, 83], [117, 13, 84, 85], [118, 12, 84, 85, "children"], [118, 20, 84, 85], [118, 22, 84, 86], [119, 10, 86, 8], [120, 12, 86, 8, "fileName"], [120, 20, 86, 8], [120, 22, 86, 8, "_jsxFileName"], [120, 34, 86, 8], [121, 12, 86, 8, "lineNumber"], [121, 22, 86, 8], [122, 12, 86, 8, "columnNumber"], [122, 24, 86, 8], [123, 10, 86, 8], [123, 17, 86, 14], [123, 18, 86, 15], [123, 33, 87, 8], [123, 37, 87, 8, "_jsxDevRuntime"], [123, 51, 87, 8], [123, 52, 87, 8, "jsxDEV"], [123, 58, 87, 8], [123, 60, 87, 9, "_reactNative"], [123, 72, 87, 9], [123, 73, 87, 9, "View"], [123, 77, 87, 13], [124, 12, 87, 14, "style"], [124, 17, 87, 19], [124, 19, 87, 21], [125, 14, 88, 10, "backgroundColor"], [125, 29, 88, 25], [125, 31, 88, 27], [125, 40, 88, 36], [126, 14, 89, 10, "paddingHorizontal"], [126, 31, 89, 27], [126, 33, 89, 29], [126, 35, 89, 31], [127, 14, 90, 10, "paddingVertical"], [127, 29, 90, 25], [127, 31, 90, 27], [127, 32, 90, 28], [128, 14, 91, 10, "borderRadius"], [128, 26, 91, 22], [128, 28, 91, 24], [129, 12, 92, 8], [129, 13, 92, 10], [130, 12, 92, 10, "children"], [130, 20, 92, 10], [130, 35, 93, 10], [130, 39, 93, 10, "_jsxDevRuntime"], [130, 53, 93, 10], [130, 54, 93, 10, "jsxDEV"], [130, 60, 93, 10], [130, 62, 93, 11, "_reactNative"], [130, 74, 93, 11], [130, 75, 93, 11, "Text"], [130, 79, 93, 15], [131, 14, 93, 16, "style"], [131, 19, 93, 21], [131, 21, 93, 23], [132, 16, 93, 25, "color"], [132, 21, 93, 30], [132, 23, 93, 32], [132, 29, 93, 38], [133, 16, 93, 40, "fontWeight"], [133, 26, 93, 50], [133, 28, 93, 52], [133, 33, 93, 57], [134, 16, 93, 59, "fontSize"], [134, 24, 93, 67], [134, 26, 93, 69], [135, 14, 93, 72], [135, 15, 93, 74], [136, 14, 93, 74, "children"], [136, 22, 93, 74], [136, 25, 94, 13, "wishlistItems"], [136, 38, 94, 26], [136, 39, 94, 27, "length"], [136, 45, 94, 33], [136, 47, 94, 34], [136, 55, 95, 10], [137, 12, 95, 10], [138, 14, 95, 10, "fileName"], [138, 22, 95, 10], [138, 24, 95, 10, "_jsxFileName"], [138, 36, 95, 10], [139, 14, 95, 10, "lineNumber"], [139, 24, 95, 10], [140, 14, 95, 10, "columnNumber"], [140, 26, 95, 10], [141, 12, 95, 10], [141, 19, 95, 16], [142, 10, 95, 17], [143, 12, 95, 17, "fileName"], [143, 20, 95, 17], [143, 22, 95, 17, "_jsxFileName"], [143, 34, 95, 17], [144, 12, 95, 17, "lineNumber"], [144, 22, 95, 17], [145, 12, 95, 17, "columnNumber"], [145, 24, 95, 17], [146, 10, 95, 17], [146, 17, 96, 14], [146, 18, 96, 15], [147, 8, 96, 15], [148, 10, 96, 15, "fileName"], [148, 18, 96, 15], [148, 20, 96, 15, "_jsxFileName"], [148, 32, 96, 15], [149, 10, 96, 15, "lineNumber"], [149, 20, 96, 15], [150, 10, 96, 15, "columnNumber"], [150, 22, 96, 15], [151, 8, 96, 15], [151, 15, 97, 12], [151, 16, 97, 13], [151, 18, 99, 7, "wishlistItems"], [151, 31, 99, 20], [151, 32, 99, 21, "length"], [151, 38, 99, 27], [151, 43, 99, 32], [151, 44, 99, 33], [151, 60, 100, 8], [151, 64, 100, 8, "_jsxDevRuntime"], [151, 78, 100, 8], [151, 79, 100, 8, "jsxDEV"], [151, 85, 100, 8], [151, 87, 100, 9, "_reactNative"], [151, 99, 100, 9], [151, 100, 100, 9, "View"], [151, 104, 100, 13], [152, 10, 100, 14, "style"], [152, 15, 100, 19], [152, 17, 100, 21], [153, 12, 100, 23, "flex"], [153, 16, 100, 27], [153, 18, 100, 29], [153, 19, 100, 30], [154, 12, 100, 32, "justifyContent"], [154, 26, 100, 46], [154, 28, 100, 48], [154, 36, 100, 56], [155, 12, 100, 58, "alignItems"], [155, 22, 100, 68], [155, 24, 100, 70], [155, 32, 100, 78], [156, 12, 100, 80, "paddingHorizontal"], [156, 29, 100, 97], [156, 31, 100, 99], [157, 10, 100, 102], [157, 11, 100, 104], [158, 10, 100, 104, "children"], [158, 18, 100, 104], [158, 34, 101, 10], [158, 38, 101, 10, "_jsxDevRuntime"], [158, 52, 101, 10], [158, 53, 101, 10, "jsxDEV"], [158, 59, 101, 10], [158, 61, 101, 11, "_reactNative"], [158, 73, 101, 11], [158, 74, 101, 11, "View"], [158, 78, 101, 15], [159, 12, 101, 16, "style"], [159, 17, 101, 21], [159, 19, 101, 23], [160, 14, 102, 12, "width"], [160, 19, 102, 17], [160, 21, 102, 19], [160, 24, 102, 22], [161, 14, 103, 12, "height"], [161, 20, 103, 18], [161, 22, 103, 20], [161, 25, 103, 23], [162, 14, 104, 12, "backgroundColor"], [162, 29, 104, 27], [162, 31, 104, 29], [162, 40, 104, 38], [163, 14, 105, 12, "borderRadius"], [163, 26, 105, 24], [163, 28, 105, 26], [163, 30, 105, 28], [164, 14, 106, 12, "alignItems"], [164, 24, 106, 22], [164, 26, 106, 24], [164, 34, 106, 32], [165, 14, 107, 12, "justifyContent"], [165, 28, 107, 26], [165, 30, 107, 28], [165, 38, 107, 36], [166, 14, 108, 12, "marginBottom"], [166, 26, 108, 24], [166, 28, 108, 26], [167, 12, 109, 10], [167, 13, 109, 12], [168, 12, 109, 12, "children"], [168, 20, 109, 12], [168, 35, 110, 12], [168, 39, 110, 12, "_jsxDevRuntime"], [168, 53, 110, 12], [168, 54, 110, 12, "jsxDEV"], [168, 60, 110, 12], [168, 62, 110, 13, "_vectorIcons"], [168, 74, 110, 13], [168, 75, 110, 13, "Ionicons"], [168, 83, 110, 21], [169, 14, 110, 22, "name"], [169, 18, 110, 26], [169, 20, 110, 27], [169, 35, 110, 42], [170, 14, 110, 43, "size"], [170, 18, 110, 47], [170, 20, 110, 49], [170, 22, 110, 52], [171, 14, 110, 53, "color"], [171, 19, 110, 58], [171, 21, 110, 59], [172, 12, 110, 68], [173, 14, 110, 68, "fileName"], [173, 22, 110, 68], [173, 24, 110, 68, "_jsxFileName"], [173, 36, 110, 68], [174, 14, 110, 68, "lineNumber"], [174, 24, 110, 68], [175, 14, 110, 68, "columnNumber"], [175, 26, 110, 68], [176, 12, 110, 68], [176, 19, 110, 70], [177, 10, 110, 71], [178, 12, 110, 71, "fileName"], [178, 20, 110, 71], [178, 22, 110, 71, "_jsxFileName"], [178, 34, 110, 71], [179, 12, 110, 71, "lineNumber"], [179, 22, 110, 71], [180, 12, 110, 71, "columnNumber"], [180, 24, 110, 71], [181, 10, 110, 71], [181, 17, 111, 16], [181, 18, 111, 17], [181, 33, 112, 10], [181, 37, 112, 10, "_jsxDevRuntime"], [181, 51, 112, 10], [181, 52, 112, 10, "jsxDEV"], [181, 58, 112, 10], [181, 60, 112, 11, "_reactNative"], [181, 72, 112, 11], [181, 73, 112, 11, "Text"], [181, 77, 112, 15], [182, 12, 112, 16, "style"], [182, 17, 112, 21], [182, 19, 112, 23], [183, 14, 112, 25, "fontSize"], [183, 22, 112, 33], [183, 24, 112, 35], [183, 26, 112, 37], [184, 14, 112, 39, "fontWeight"], [184, 24, 112, 49], [184, 26, 112, 51], [184, 32, 112, 57], [185, 14, 112, 59, "color"], [185, 19, 112, 64], [185, 21, 112, 66], [185, 30, 112, 75], [186, 14, 112, 77, "marginBottom"], [186, 26, 112, 89], [186, 28, 112, 91], [186, 29, 112, 92], [187, 14, 112, 94, "textAlign"], [187, 23, 112, 103], [187, 25, 112, 105], [188, 12, 112, 114], [188, 13, 112, 116], [189, 12, 112, 116, "children"], [189, 20, 112, 116], [189, 22, 112, 117], [190, 10, 114, 10], [191, 12, 114, 10, "fileName"], [191, 20, 114, 10], [191, 22, 114, 10, "_jsxFileName"], [191, 34, 114, 10], [192, 12, 114, 10, "lineNumber"], [192, 22, 114, 10], [193, 12, 114, 10, "columnNumber"], [193, 24, 114, 10], [194, 10, 114, 10], [194, 17, 114, 16], [194, 18, 114, 17], [194, 33, 115, 10], [194, 37, 115, 10, "_jsxDevRuntime"], [194, 51, 115, 10], [194, 52, 115, 10, "jsxDEV"], [194, 58, 115, 10], [194, 60, 115, 11, "_reactNative"], [194, 72, 115, 11], [194, 73, 115, 11, "Text"], [194, 77, 115, 15], [195, 12, 115, 16, "style"], [195, 17, 115, 21], [195, 19, 115, 23], [196, 14, 115, 25, "color"], [196, 19, 115, 30], [196, 21, 115, 32], [196, 30, 115, 41], [197, 14, 115, 43, "textAlign"], [197, 23, 115, 52], [197, 25, 115, 54], [197, 33, 115, 62], [198, 14, 115, 64, "marginBottom"], [198, 26, 115, 76], [198, 28, 115, 78], [198, 30, 115, 80], [199, 14, 115, 82, "lineHeight"], [199, 24, 115, 92], [199, 26, 115, 94], [200, 12, 115, 97], [200, 13, 115, 99], [201, 12, 115, 99, "children"], [201, 20, 115, 99], [201, 22, 115, 100], [202, 10, 117, 10], [203, 12, 117, 10, "fileName"], [203, 20, 117, 10], [203, 22, 117, 10, "_jsxFileName"], [203, 34, 117, 10], [204, 12, 117, 10, "lineNumber"], [204, 22, 117, 10], [205, 12, 117, 10, "columnNumber"], [205, 24, 117, 10], [206, 10, 117, 10], [206, 17, 117, 16], [206, 18, 117, 17], [206, 33, 118, 10], [206, 37, 118, 10, "_jsxDevRuntime"], [206, 51, 118, 10], [206, 52, 118, 10, "jsxDEV"], [206, 58, 118, 10], [206, 60, 118, 11, "_reactNative"], [206, 72, 118, 11], [206, 73, 118, 11, "TouchableOpacity"], [206, 89, 118, 27], [207, 12, 119, 12, "onPress"], [207, 19, 119, 19], [207, 21, 119, 21, "onPress"], [207, 22, 119, 21], [207, 27, 119, 27, "navigation"], [207, 37, 119, 37], [207, 38, 119, 38, "navigate"], [207, 46, 119, 46], [207, 47, 119, 47], [207, 53, 119, 53], [207, 54, 119, 55], [208, 12, 120, 12, "style"], [208, 17, 120, 17], [208, 19, 120, 19], [209, 14, 121, 14, "backgroundColor"], [209, 29, 121, 29], [209, 31, 121, 31], [209, 40, 121, 40], [210, 14, 122, 14, "paddingHorizontal"], [210, 31, 122, 31], [210, 33, 122, 33], [210, 35, 122, 35], [211, 14, 123, 14, "paddingVertical"], [211, 29, 123, 29], [211, 31, 123, 31], [211, 33, 123, 33], [212, 14, 124, 14, "borderRadius"], [212, 26, 124, 26], [212, 28, 124, 28], [213, 12, 125, 12], [213, 13, 125, 14], [214, 12, 125, 14, "children"], [214, 20, 125, 14], [214, 35, 127, 12], [214, 39, 127, 12, "_jsxDevRuntime"], [214, 53, 127, 12], [214, 54, 127, 12, "jsxDEV"], [214, 60, 127, 12], [214, 62, 127, 13, "_reactNative"], [214, 74, 127, 13], [214, 75, 127, 13, "Text"], [214, 79, 127, 17], [215, 14, 127, 18, "style"], [215, 19, 127, 23], [215, 21, 127, 25], [216, 16, 127, 27, "color"], [216, 21, 127, 32], [216, 23, 127, 34], [216, 29, 127, 40], [217, 16, 127, 42, "fontWeight"], [217, 26, 127, 52], [217, 28, 127, 54], [217, 33, 127, 59], [218, 16, 127, 61, "fontSize"], [218, 24, 127, 69], [218, 26, 127, 71], [219, 14, 127, 74], [219, 15, 127, 76], [220, 14, 127, 76, "children"], [220, 22, 127, 76], [220, 24, 127, 77], [221, 12, 129, 12], [222, 14, 129, 12, "fileName"], [222, 22, 129, 12], [222, 24, 129, 12, "_jsxFileName"], [222, 36, 129, 12], [223, 14, 129, 12, "lineNumber"], [223, 24, 129, 12], [224, 14, 129, 12, "columnNumber"], [224, 26, 129, 12], [225, 12, 129, 12], [225, 19, 129, 18], [226, 10, 129, 19], [227, 12, 129, 19, "fileName"], [227, 20, 129, 19], [227, 22, 129, 19, "_jsxFileName"], [227, 34, 129, 19], [228, 12, 129, 19, "lineNumber"], [228, 22, 129, 19], [229, 12, 129, 19, "columnNumber"], [229, 24, 129, 19], [230, 10, 129, 19], [230, 17, 130, 28], [230, 18, 130, 29], [231, 8, 130, 29], [232, 10, 130, 29, "fileName"], [232, 18, 130, 29], [232, 20, 130, 29, "_jsxFileName"], [232, 32, 130, 29], [233, 10, 130, 29, "lineNumber"], [233, 20, 130, 29], [234, 10, 130, 29, "columnNumber"], [234, 22, 130, 29], [235, 8, 130, 29], [235, 15, 131, 14], [235, 16, 131, 15], [235, 32, 133, 8], [235, 36, 133, 8, "_jsxDevRuntime"], [235, 50, 133, 8], [235, 51, 133, 8, "jsxDEV"], [235, 57, 133, 8], [235, 59, 133, 9, "_reactNative"], [235, 71, 133, 9], [235, 72, 133, 9, "ScrollView"], [235, 82, 133, 19], [236, 10, 133, 20, "style"], [236, 15, 133, 25], [236, 17, 133, 27], [237, 12, 133, 29, "flex"], [237, 16, 133, 33], [237, 18, 133, 35], [238, 10, 133, 37], [238, 11, 133, 39], [239, 10, 133, 39, "children"], [239, 18, 133, 39], [239, 34, 134, 10], [239, 38, 134, 10, "_jsxDevRuntime"], [239, 52, 134, 10], [239, 53, 134, 10, "jsxDEV"], [239, 59, 134, 10], [239, 61, 134, 11, "_reactNative"], [239, 73, 134, 11], [239, 74, 134, 11, "View"], [239, 78, 134, 15], [240, 12, 134, 16, "style"], [240, 17, 134, 21], [240, 19, 134, 23], [241, 14, 134, 25, "padding"], [241, 21, 134, 32], [241, 23, 134, 34], [242, 12, 134, 37], [242, 13, 134, 39], [243, 12, 134, 39, "children"], [243, 20, 134, 39], [243, 22, 135, 13, "wishlistItems"], [243, 35, 135, 26], [243, 36, 135, 27, "map"], [243, 39, 135, 30], [243, 40, 135, 32, "item"], [243, 44, 135, 36], [243, 61, 136, 14], [243, 65, 136, 14, "_jsxDevRuntime"], [243, 79, 136, 14], [243, 80, 136, 14, "jsxDEV"], [243, 86, 136, 14], [243, 88, 136, 15, "_reactNative"], [243, 100, 136, 15], [243, 101, 136, 15, "View"], [243, 105, 136, 19], [244, 14, 138, 16, "style"], [244, 19, 138, 21], [244, 21, 138, 23], [245, 16, 139, 18, "backgroundColor"], [245, 31, 139, 33], [245, 33, 139, 35], [245, 39, 139, 41], [246, 16, 140, 18, "borderRadius"], [246, 28, 140, 30], [246, 30, 140, 32], [246, 32, 140, 34], [247, 16, 141, 18, "marginBottom"], [247, 28, 141, 30], [247, 30, 141, 32], [247, 32, 141, 34], [248, 16, 142, 18, "shadowColor"], [248, 27, 142, 29], [248, 29, 142, 31], [248, 35, 142, 37], [249, 16, 143, 18, "shadowOffset"], [249, 28, 143, 30], [249, 30, 143, 32], [250, 18, 143, 34, "width"], [250, 23, 143, 39], [250, 25, 143, 41], [250, 26, 143, 42], [251, 18, 143, 44, "height"], [251, 24, 143, 50], [251, 26, 143, 52], [252, 16, 143, 54], [252, 17, 143, 55], [253, 16, 144, 18, "shadowOpacity"], [253, 29, 144, 31], [253, 31, 144, 33], [253, 34, 144, 36], [254, 16, 145, 18, "shadowRadius"], [254, 28, 145, 30], [254, 30, 145, 32], [254, 31, 145, 33], [255, 16, 146, 18, "elevation"], [255, 25, 146, 27], [255, 27, 146, 29], [256, 14, 147, 16], [256, 15, 147, 18], [257, 14, 147, 18, "children"], [257, 22, 147, 18], [257, 38, 149, 16], [257, 42, 149, 16, "_jsxDevRuntime"], [257, 56, 149, 16], [257, 57, 149, 16, "jsxDEV"], [257, 63, 149, 16], [257, 65, 149, 17, "_reactNative"], [257, 77, 149, 17], [257, 78, 149, 17, "Image"], [257, 83, 149, 22], [258, 16, 150, 18, "source"], [258, 22, 150, 24], [258, 24, 150, 26], [259, 18, 150, 28, "uri"], [259, 21, 150, 31], [259, 23, 150, 33, "item"], [259, 27, 150, 37], [259, 28, 150, 38, "image"], [260, 16, 150, 44], [260, 17, 150, 46], [261, 16, 151, 18, "style"], [261, 21, 151, 23], [261, 23, 151, 25], [262, 18, 152, 20, "width"], [262, 23, 152, 25], [262, 25, 152, 27], [262, 31, 152, 33], [263, 18, 153, 20, "height"], [263, 24, 153, 26], [263, 26, 153, 28], [263, 29, 153, 31], [264, 18, 154, 20, "borderTopLeftRadius"], [264, 37, 154, 39], [264, 39, 154, 41], [264, 41, 154, 43], [265, 18, 155, 20, "borderTopRightRadius"], [265, 38, 155, 40], [265, 40, 155, 42], [266, 16, 156, 18], [267, 14, 156, 20], [268, 16, 156, 20, "fileName"], [268, 24, 156, 20], [268, 26, 156, 20, "_jsxFileName"], [268, 38, 156, 20], [269, 16, 156, 20, "lineNumber"], [269, 26, 156, 20], [270, 16, 156, 20, "columnNumber"], [270, 28, 156, 20], [271, 14, 156, 20], [271, 21, 157, 17], [271, 22, 157, 18], [271, 37, 158, 16], [271, 41, 158, 16, "_jsxDevRuntime"], [271, 55, 158, 16], [271, 56, 158, 16, "jsxDEV"], [271, 62, 158, 16], [271, 64, 158, 17, "_reactNative"], [271, 76, 158, 17], [271, 77, 158, 17, "View"], [271, 81, 158, 21], [272, 16, 158, 22, "style"], [272, 21, 158, 27], [272, 23, 158, 29], [273, 18, 158, 31, "padding"], [273, 25, 158, 38], [273, 27, 158, 40], [274, 16, 158, 43], [274, 17, 158, 45], [275, 16, 158, 45, "children"], [275, 24, 158, 45], [275, 40, 159, 18], [275, 44, 159, 18, "_jsxDevRuntime"], [275, 58, 159, 18], [275, 59, 159, 18, "jsxDEV"], [275, 65, 159, 18], [275, 67, 159, 19, "_reactNative"], [275, 79, 159, 19], [275, 80, 159, 19, "View"], [275, 84, 159, 23], [276, 18, 159, 24, "style"], [276, 23, 159, 29], [276, 25, 159, 31], [277, 20, 159, 33, "flexDirection"], [277, 33, 159, 46], [277, 35, 159, 48], [277, 40, 159, 53], [278, 20, 159, 55, "justifyContent"], [278, 34, 159, 69], [278, 36, 159, 71], [278, 51, 159, 86], [279, 20, 159, 88, "alignItems"], [279, 30, 159, 98], [279, 32, 159, 100], [279, 44, 159, 112], [280, 20, 159, 114, "marginBottom"], [280, 32, 159, 126], [280, 34, 159, 128], [281, 18, 159, 130], [281, 19, 159, 132], [282, 18, 159, 132, "children"], [282, 26, 159, 132], [282, 42, 160, 20], [282, 46, 160, 20, "_jsxDevRuntime"], [282, 60, 160, 20], [282, 61, 160, 20, "jsxDEV"], [282, 67, 160, 20], [282, 69, 160, 21, "_reactNative"], [282, 81, 160, 21], [282, 82, 160, 21, "View"], [282, 86, 160, 25], [283, 20, 160, 26, "style"], [283, 25, 160, 31], [283, 27, 160, 33], [284, 22, 160, 35, "flex"], [284, 26, 160, 39], [284, 28, 160, 41], [285, 20, 160, 43], [285, 21, 160, 45], [286, 20, 160, 45, "children"], [286, 28, 160, 45], [286, 44, 161, 22], [286, 48, 161, 22, "_jsxDevRuntime"], [286, 62, 161, 22], [286, 63, 161, 22, "jsxDEV"], [286, 69, 161, 22], [286, 71, 161, 23, "_reactNative"], [286, 83, 161, 23], [286, 84, 161, 23, "Text"], [286, 88, 161, 27], [287, 22, 161, 28, "style"], [287, 27, 161, 33], [287, 29, 161, 35], [288, 24, 161, 37, "fontSize"], [288, 32, 161, 45], [288, 34, 161, 47], [288, 36, 161, 49], [289, 24, 161, 51, "fontWeight"], [289, 34, 161, 61], [289, 36, 161, 63], [289, 42, 161, 69], [290, 24, 161, 71, "color"], [290, 29, 161, 76], [290, 31, 161, 78], [290, 40, 161, 87], [291, 24, 161, 89, "marginBottom"], [291, 36, 161, 101], [291, 38, 161, 103], [292, 22, 161, 105], [292, 23, 161, 107], [293, 22, 161, 107, "children"], [293, 30, 161, 107], [293, 32, 162, 25, "item"], [293, 36, 162, 29], [293, 37, 162, 30, "name"], [294, 20, 162, 34], [295, 22, 162, 34, "fileName"], [295, 30, 162, 34], [295, 32, 162, 34, "_jsxFileName"], [295, 44, 162, 34], [296, 22, 162, 34, "lineNumber"], [296, 32, 162, 34], [297, 22, 162, 34, "columnNumber"], [297, 34, 162, 34], [298, 20, 162, 34], [298, 27, 163, 28], [298, 28, 163, 29], [298, 43, 164, 22], [298, 47, 164, 22, "_jsxDevRuntime"], [298, 61, 164, 22], [298, 62, 164, 22, "jsxDEV"], [298, 68, 164, 22], [298, 70, 164, 23, "_reactNative"], [298, 82, 164, 23], [298, 83, 164, 23, "Text"], [298, 87, 164, 27], [299, 22, 164, 28, "style"], [299, 27, 164, 33], [299, 29, 164, 35], [300, 24, 164, 37, "color"], [300, 29, 164, 42], [300, 31, 164, 44], [300, 40, 164, 53], [301, 24, 164, 55, "fontSize"], [301, 32, 164, 63], [301, 34, 164, 65], [301, 36, 164, 67], [302, 24, 164, 69, "marginBottom"], [302, 36, 164, 81], [302, 38, 164, 83], [303, 22, 164, 85], [303, 23, 164, 87], [304, 22, 164, 87, "children"], [304, 30, 164, 87], [304, 33, 164, 88], [304, 40, 165, 29], [304, 42, 165, 30, "item"], [304, 46, 165, 34], [304, 47, 165, 35, "restaurant"], [304, 57, 165, 45], [305, 20, 165, 45], [306, 22, 165, 45, "fileName"], [306, 30, 165, 45], [306, 32, 165, 45, "_jsxFileName"], [306, 44, 165, 45], [307, 22, 165, 45, "lineNumber"], [307, 32, 165, 45], [308, 22, 165, 45, "columnNumber"], [308, 34, 165, 45], [309, 20, 165, 45], [309, 27, 166, 28], [309, 28, 166, 29], [309, 43, 167, 22], [309, 47, 167, 22, "_jsxDevRuntime"], [309, 61, 167, 22], [309, 62, 167, 22, "jsxDEV"], [309, 68, 167, 22], [309, 70, 167, 23, "_reactNative"], [309, 82, 167, 23], [309, 83, 167, 23, "Text"], [309, 87, 167, 27], [310, 22, 167, 28, "style"], [310, 27, 167, 33], [310, 29, 167, 35], [311, 24, 167, 37, "color"], [311, 29, 167, 42], [311, 31, 167, 44], [311, 40, 167, 53], [312, 24, 167, 55, "fontSize"], [312, 32, 167, 63], [312, 34, 167, 65], [312, 36, 167, 67], [313, 24, 167, 69, "marginBottom"], [313, 36, 167, 81], [313, 38, 167, 83], [314, 22, 167, 85], [314, 23, 167, 87], [315, 22, 167, 87, "children"], [315, 30, 167, 87], [315, 32, 168, 25, "item"], [315, 36, 168, 29], [315, 37, 168, 30, "description"], [316, 20, 168, 41], [317, 22, 168, 41, "fileName"], [317, 30, 168, 41], [317, 32, 168, 41, "_jsxFileName"], [317, 44, 168, 41], [318, 22, 168, 41, "lineNumber"], [318, 32, 168, 41], [319, 22, 168, 41, "columnNumber"], [319, 34, 168, 41], [320, 20, 168, 41], [320, 27, 169, 28], [320, 28, 169, 29], [320, 43, 170, 22], [320, 47, 170, 22, "_jsxDevRuntime"], [320, 61, 170, 22], [320, 62, 170, 22, "jsxDEV"], [320, 68, 170, 22], [320, 70, 170, 23, "_reactNative"], [320, 82, 170, 23], [320, 83, 170, 23, "View"], [320, 87, 170, 27], [321, 22, 170, 28, "style"], [321, 27, 170, 33], [321, 29, 170, 35], [322, 24, 170, 37, "flexDirection"], [322, 37, 170, 50], [322, 39, 170, 52], [322, 44, 170, 57], [323, 24, 170, 59, "alignItems"], [323, 34, 170, 69], [323, 36, 170, 71], [323, 44, 170, 79], [324, 24, 170, 81, "marginBottom"], [324, 36, 170, 93], [324, 38, 170, 95], [325, 22, 170, 98], [325, 23, 170, 100], [326, 22, 170, 100, "children"], [326, 30, 170, 100], [326, 46, 171, 24], [326, 50, 171, 24, "_jsxDevRuntime"], [326, 64, 171, 24], [326, 65, 171, 24, "jsxDEV"], [326, 71, 171, 24], [326, 73, 171, 25, "_vectorIcons"], [326, 85, 171, 25], [326, 86, 171, 25, "Ionicons"], [326, 94, 171, 33], [327, 24, 171, 34, "name"], [327, 28, 171, 38], [327, 30, 171, 39], [327, 36, 171, 45], [328, 24, 171, 46, "size"], [328, 28, 171, 50], [328, 30, 171, 52], [328, 32, 171, 55], [329, 24, 171, 56, "color"], [329, 29, 171, 61], [329, 31, 171, 62], [330, 22, 171, 71], [331, 24, 171, 71, "fileName"], [331, 32, 171, 71], [331, 34, 171, 71, "_jsxFileName"], [331, 46, 171, 71], [332, 24, 171, 71, "lineNumber"], [332, 34, 171, 71], [333, 24, 171, 71, "columnNumber"], [333, 36, 171, 71], [334, 22, 171, 71], [334, 29, 171, 73], [334, 30, 171, 74], [334, 45, 172, 24], [334, 49, 172, 24, "_jsxDevRuntime"], [334, 63, 172, 24], [334, 64, 172, 24, "jsxDEV"], [334, 70, 172, 24], [334, 72, 172, 25, "_reactNative"], [334, 84, 172, 25], [334, 85, 172, 25, "Text"], [334, 89, 172, 29], [335, 24, 172, 30, "style"], [335, 29, 172, 35], [335, 31, 172, 37], [336, 26, 172, 39, "marginLeft"], [336, 36, 172, 49], [336, 38, 172, 51], [336, 39, 172, 52], [337, 26, 172, 54, "color"], [337, 31, 172, 59], [337, 33, 172, 61], [337, 42, 172, 70], [338, 26, 172, 72, "fontSize"], [338, 34, 172, 80], [338, 36, 172, 82], [339, 24, 172, 85], [339, 25, 172, 87], [340, 24, 172, 87, "children"], [340, 32, 172, 87], [340, 35, 173, 27, "item"], [340, 39, 173, 31], [340, 40, 173, 32, "rating"], [340, 46, 173, 38], [340, 48, 173, 39], [340, 58, 173, 42], [340, 60, 173, 43, "item"], [340, 64, 173, 47], [340, 65, 173, 48, "deliveryTime"], [340, 77, 173, 60], [341, 22, 173, 60], [342, 24, 173, 60, "fileName"], [342, 32, 173, 60], [342, 34, 173, 60, "_jsxFileName"], [342, 46, 173, 60], [343, 24, 173, 60, "lineNumber"], [343, 34, 173, 60], [344, 24, 173, 60, "columnNumber"], [344, 36, 173, 60], [345, 22, 173, 60], [345, 29, 174, 30], [345, 30, 174, 31], [346, 20, 174, 31], [347, 22, 174, 31, "fileName"], [347, 30, 174, 31], [347, 32, 174, 31, "_jsxFileName"], [347, 44, 174, 31], [348, 22, 174, 31, "lineNumber"], [348, 32, 174, 31], [349, 22, 174, 31, "columnNumber"], [349, 34, 174, 31], [350, 20, 174, 31], [350, 27, 175, 28], [350, 28, 175, 29], [351, 18, 175, 29], [352, 20, 175, 29, "fileName"], [352, 28, 175, 29], [352, 30, 175, 29, "_jsxFileName"], [352, 42, 175, 29], [353, 20, 175, 29, "lineNumber"], [353, 30, 175, 29], [354, 20, 175, 29, "columnNumber"], [354, 32, 175, 29], [355, 18, 175, 29], [355, 25, 176, 26], [355, 26, 176, 27], [355, 41, 177, 20], [355, 45, 177, 20, "_jsxDevRuntime"], [355, 59, 177, 20], [355, 60, 177, 20, "jsxDEV"], [355, 66, 177, 20], [355, 68, 177, 21, "_reactNative"], [355, 80, 177, 21], [355, 81, 177, 21, "TouchableOpacity"], [355, 97, 177, 37], [356, 20, 178, 22, "onPress"], [356, 27, 178, 29], [356, 29, 178, 31, "onPress"], [356, 30, 178, 31], [356, 35, 178, 37, "removeFromWishlist"], [356, 53, 178, 55], [356, 54, 178, 56, "item"], [356, 58, 178, 60], [356, 59, 178, 61, "id"], [356, 61, 178, 63], [356, 62, 178, 65], [357, 20, 179, 22, "style"], [357, 25, 179, 27], [357, 27, 179, 29], [358, 22, 180, 24, "padding"], [358, 29, 180, 31], [358, 31, 180, 33], [358, 32, 180, 34], [359, 22, 181, 24, "backgroundColor"], [359, 37, 181, 39], [359, 39, 181, 41], [359, 48, 181, 50], [360, 22, 182, 24, "borderRadius"], [360, 34, 182, 36], [360, 36, 182, 38], [361, 20, 183, 22], [361, 21, 183, 24], [362, 20, 183, 24, "children"], [362, 28, 183, 24], [362, 43, 185, 22], [362, 47, 185, 22, "_jsxDevRuntime"], [362, 61, 185, 22], [362, 62, 185, 22, "jsxDEV"], [362, 68, 185, 22], [362, 70, 185, 23, "_vectorIcons"], [362, 82, 185, 23], [362, 83, 185, 23, "Ionicons"], [362, 91, 185, 31], [363, 22, 185, 32, "name"], [363, 26, 185, 36], [363, 28, 185, 37], [363, 35, 185, 44], [364, 22, 185, 45, "size"], [364, 26, 185, 49], [364, 28, 185, 51], [364, 30, 185, 54], [365, 22, 185, 55, "color"], [365, 27, 185, 60], [365, 29, 185, 61], [366, 20, 185, 70], [367, 22, 185, 70, "fileName"], [367, 30, 185, 70], [367, 32, 185, 70, "_jsxFileName"], [367, 44, 185, 70], [368, 22, 185, 70, "lineNumber"], [368, 32, 185, 70], [369, 22, 185, 70, "columnNumber"], [369, 34, 185, 70], [370, 20, 185, 70], [370, 27, 185, 72], [371, 18, 185, 73], [372, 20, 185, 73, "fileName"], [372, 28, 185, 73], [372, 30, 185, 73, "_jsxFileName"], [372, 42, 185, 73], [373, 20, 185, 73, "lineNumber"], [373, 30, 185, 73], [374, 20, 185, 73, "columnNumber"], [374, 32, 185, 73], [375, 18, 185, 73], [375, 25, 186, 38], [375, 26, 186, 39], [376, 16, 186, 39], [377, 18, 186, 39, "fileName"], [377, 26, 186, 39], [377, 28, 186, 39, "_jsxFileName"], [377, 40, 186, 39], [378, 18, 186, 39, "lineNumber"], [378, 28, 186, 39], [379, 18, 186, 39, "columnNumber"], [379, 30, 186, 39], [380, 16, 186, 39], [380, 23, 187, 24], [380, 24, 187, 25], [380, 39, 189, 18], [380, 43, 189, 18, "_jsxDevRuntime"], [380, 57, 189, 18], [380, 58, 189, 18, "jsxDEV"], [380, 64, 189, 18], [380, 66, 189, 19, "_reactNative"], [380, 78, 189, 19], [380, 79, 189, 19, "View"], [380, 83, 189, 23], [381, 18, 189, 24, "style"], [381, 23, 189, 29], [381, 25, 189, 31], [382, 20, 189, 33, "flexDirection"], [382, 33, 189, 46], [382, 35, 189, 48], [382, 40, 189, 53], [383, 20, 189, 55, "justifyContent"], [383, 34, 189, 69], [383, 36, 189, 71], [383, 51, 189, 86], [384, 20, 189, 88, "alignItems"], [384, 30, 189, 98], [384, 32, 189, 100], [385, 18, 189, 109], [385, 19, 189, 111], [386, 18, 189, 111, "children"], [386, 26, 189, 111], [386, 42, 190, 20], [386, 46, 190, 20, "_jsxDevRuntime"], [386, 60, 190, 20], [386, 61, 190, 20, "jsxDEV"], [386, 67, 190, 20], [386, 69, 190, 21, "_reactNative"], [386, 81, 190, 21], [386, 82, 190, 21, "Text"], [386, 86, 190, 25], [387, 20, 190, 26, "style"], [387, 25, 190, 31], [387, 27, 190, 33], [388, 22, 190, 35, "fontSize"], [388, 30, 190, 43], [388, 32, 190, 45], [388, 34, 190, 47], [389, 22, 190, 49, "fontWeight"], [389, 32, 190, 59], [389, 34, 190, 61], [389, 40, 190, 67], [390, 22, 190, 69, "color"], [390, 27, 190, 74], [390, 29, 190, 76], [391, 20, 190, 86], [391, 21, 190, 88], [392, 20, 190, 88, "children"], [392, 28, 190, 88], [392, 31, 190, 89], [392, 34, 191, 23], [392, 36, 191, 24, "item"], [392, 40, 191, 28], [392, 41, 191, 29, "price"], [392, 46, 191, 34], [392, 47, 191, 35, "toFixed"], [392, 54, 191, 42], [392, 55, 191, 43], [392, 56, 191, 44], [392, 57, 191, 45], [393, 18, 191, 45], [394, 20, 191, 45, "fileName"], [394, 28, 191, 45], [394, 30, 191, 45, "_jsxFileName"], [394, 42, 191, 45], [395, 20, 191, 45, "lineNumber"], [395, 30, 191, 45], [396, 20, 191, 45, "columnNumber"], [396, 32, 191, 45], [397, 18, 191, 45], [397, 25, 192, 26], [397, 26, 192, 27], [397, 41, 193, 20], [397, 45, 193, 20, "_jsxDevRuntime"], [397, 59, 193, 20], [397, 60, 193, 20, "jsxDEV"], [397, 66, 193, 20], [397, 68, 193, 21, "_reactNative"], [397, 80, 193, 21], [397, 81, 193, 21, "TouchableOpacity"], [397, 97, 193, 37], [398, 20, 193, 38, "style"], [398, 25, 193, 43], [398, 27, 193, 45], [399, 22, 194, 22, "backgroundColor"], [399, 37, 194, 37], [399, 39, 194, 39], [399, 48, 194, 48], [400, 22, 195, 22, "paddingHorizontal"], [400, 39, 195, 39], [400, 41, 195, 41], [400, 43, 195, 43], [401, 22, 196, 22, "paddingVertical"], [401, 37, 196, 37], [401, 39, 196, 39], [401, 41, 196, 41], [402, 22, 197, 22, "borderRadius"], [402, 34, 197, 34], [402, 36, 197, 36], [403, 20, 198, 20], [403, 21, 198, 22], [404, 20, 198, 22, "children"], [404, 28, 198, 22], [404, 43, 199, 22], [404, 47, 199, 22, "_jsxDevRuntime"], [404, 61, 199, 22], [404, 62, 199, 22, "jsxDEV"], [404, 68, 199, 22], [404, 70, 199, 23, "_reactNative"], [404, 82, 199, 23], [404, 83, 199, 23, "Text"], [404, 87, 199, 27], [405, 22, 199, 28, "style"], [405, 27, 199, 33], [405, 29, 199, 35], [406, 24, 199, 37, "color"], [406, 29, 199, 42], [406, 31, 199, 44], [406, 37, 199, 50], [407, 24, 199, 52, "fontWeight"], [407, 34, 199, 62], [407, 36, 199, 64], [407, 41, 199, 69], [408, 24, 199, 71, "fontSize"], [408, 32, 199, 79], [408, 34, 199, 81], [409, 22, 199, 84], [409, 23, 199, 86], [410, 22, 199, 86, "children"], [410, 30, 199, 86], [410, 32, 199, 87], [411, 20, 201, 22], [412, 22, 201, 22, "fileName"], [412, 30, 201, 22], [412, 32, 201, 22, "_jsxFileName"], [412, 44, 201, 22], [413, 22, 201, 22, "lineNumber"], [413, 32, 201, 22], [414, 22, 201, 22, "columnNumber"], [414, 34, 201, 22], [415, 20, 201, 22], [415, 27, 201, 28], [416, 18, 201, 29], [417, 20, 201, 29, "fileName"], [417, 28, 201, 29], [417, 30, 201, 29, "_jsxFileName"], [417, 42, 201, 29], [418, 20, 201, 29, "lineNumber"], [418, 30, 201, 29], [419, 20, 201, 29, "columnNumber"], [419, 32, 201, 29], [420, 18, 201, 29], [420, 25, 202, 38], [420, 26, 202, 39], [421, 16, 202, 39], [422, 18, 202, 39, "fileName"], [422, 26, 202, 39], [422, 28, 202, 39, "_jsxFileName"], [422, 40, 202, 39], [423, 18, 202, 39, "lineNumber"], [423, 28, 202, 39], [424, 18, 202, 39, "columnNumber"], [424, 30, 202, 39], [425, 16, 202, 39], [425, 23, 203, 24], [425, 24, 203, 25], [426, 14, 203, 25], [427, 16, 203, 25, "fileName"], [427, 24, 203, 25], [427, 26, 203, 25, "_jsxFileName"], [427, 38, 203, 25], [428, 16, 203, 25, "lineNumber"], [428, 26, 203, 25], [429, 16, 203, 25, "columnNumber"], [429, 28, 203, 25], [430, 14, 203, 25], [430, 21, 204, 22], [430, 22, 204, 23], [431, 12, 204, 23], [431, 15, 137, 21, "item"], [431, 19, 137, 25], [431, 20, 137, 26, "id"], [431, 22, 137, 28], [432, 14, 137, 28, "fileName"], [432, 22, 137, 28], [432, 24, 137, 28, "_jsxFileName"], [432, 36, 137, 28], [433, 14, 137, 28, "lineNumber"], [433, 24, 137, 28], [434, 14, 137, 28, "columnNumber"], [434, 26, 137, 28], [435, 12, 137, 28], [435, 19, 205, 20], [435, 20, 206, 13], [436, 10, 206, 14], [437, 12, 206, 14, "fileName"], [437, 20, 206, 14], [437, 22, 206, 14, "_jsxFileName"], [437, 34, 206, 14], [438, 12, 206, 14, "lineNumber"], [438, 22, 206, 14], [439, 12, 206, 14, "columnNumber"], [439, 24, 206, 14], [440, 10, 206, 14], [440, 17, 207, 16], [440, 18, 207, 17], [440, 33, 209, 10], [440, 37, 209, 10, "_jsxDevRuntime"], [440, 51, 209, 10], [440, 52, 209, 10, "jsxDEV"], [440, 58, 209, 10], [440, 60, 209, 11, "_reactNative"], [440, 72, 209, 11], [440, 73, 209, 11, "View"], [440, 77, 209, 15], [441, 12, 209, 16, "style"], [441, 17, 209, 21], [441, 19, 209, 23], [442, 14, 209, 25, "height"], [442, 20, 209, 31], [442, 22, 209, 33], [443, 12, 209, 37], [444, 10, 209, 39], [445, 12, 209, 39, "fileName"], [445, 20, 209, 39], [445, 22, 209, 39, "_jsxFileName"], [445, 34, 209, 39], [446, 12, 209, 39, "lineNumber"], [446, 22, 209, 39], [447, 12, 209, 39, "columnNumber"], [447, 24, 209, 39], [448, 10, 209, 39], [448, 17, 209, 41], [448, 18, 209, 42], [449, 8, 209, 42], [450, 10, 209, 42, "fileName"], [450, 18, 209, 42], [450, 20, 209, 42, "_jsxFileName"], [450, 32, 209, 42], [451, 10, 209, 42, "lineNumber"], [451, 20, 209, 42], [452, 10, 209, 42, "columnNumber"], [452, 22, 209, 42], [453, 8, 209, 42], [453, 15, 210, 22], [453, 16, 211, 9], [454, 6, 211, 9], [455, 8, 211, 9, "fileName"], [455, 16, 211, 9], [455, 18, 211, 9, "_jsxFileName"], [455, 30, 211, 9], [456, 8, 211, 9, "lineNumber"], [456, 18, 211, 9], [457, 8, 211, 9, "columnNumber"], [457, 20, 211, 9], [458, 6, 211, 9], [458, 13, 212, 12], [458, 14, 212, 13], [458, 29, 215, 6], [458, 33, 215, 6, "_jsxDevRuntime"], [458, 47, 215, 6], [458, 48, 215, 6, "jsxDEV"], [458, 54, 215, 6], [458, 56, 215, 7, "_FooterNavigation"], [458, 73, 215, 7], [458, 74, 215, 7, "default"], [458, 81, 215, 23], [459, 8, 215, 24, "navigation"], [459, 18, 215, 34], [459, 20, 215, 36, "navigation"], [459, 30, 215, 47], [460, 8, 215, 48, "activeScreen"], [460, 20, 215, 60], [460, 22, 215, 61], [461, 6, 215, 67], [462, 8, 215, 67, "fileName"], [462, 16, 215, 67], [462, 18, 215, 67, "_jsxFileName"], [462, 30, 215, 67], [463, 8, 215, 67, "lineNumber"], [463, 18, 215, 67], [464, 8, 215, 67, "columnNumber"], [464, 20, 215, 67], [465, 6, 215, 67], [465, 13, 215, 69], [465, 14, 215, 70], [465, 29, 218, 6], [465, 33, 218, 6, "_jsxDevRuntime"], [465, 47, 218, 6], [465, 48, 218, 6, "jsxDEV"], [465, 54, 218, 6], [465, 56, 218, 7, "_reactNativeSafeAreaContext"], [465, 83, 218, 7], [465, 84, 218, 7, "SafeAreaView"], [465, 96, 218, 19], [466, 8, 218, 20, "style"], [466, 13, 218, 25], [466, 15, 218, 27], [467, 10, 218, 29, "backgroundColor"], [467, 25, 218, 44], [467, 27, 218, 46], [468, 8, 218, 56], [468, 9, 218, 58], [469, 8, 218, 59, "edges"], [469, 13, 218, 64], [469, 15, 218, 66], [469, 16, 218, 67], [469, 24, 218, 75], [470, 6, 218, 77], [471, 8, 218, 77, "fileName"], [471, 16, 218, 77], [471, 18, 218, 77, "_jsxFileName"], [471, 30, 218, 77], [472, 8, 218, 77, "lineNumber"], [472, 18, 218, 77], [473, 8, 218, 77, "columnNumber"], [473, 20, 218, 77], [474, 6, 218, 77], [474, 13, 218, 79], [474, 14, 218, 80], [475, 4, 218, 80], [476, 6, 218, 80, "fileName"], [476, 14, 218, 80], [476, 16, 218, 80, "_jsxFileName"], [476, 28, 218, 80], [477, 6, 218, 80, "lineNumber"], [477, 16, 218, 80], [478, 6, 218, 80, "columnNumber"], [478, 18, 218, 80], [479, 4, 218, 80], [479, 11, 219, 10], [479, 12, 219, 11], [480, 2, 221, 0], [481, 2, 221, 1, "_s"], [481, 4, 221, 1], [481, 5, 13, 24, "WishlistScreen"], [481, 19, 13, 38], [482, 2, 13, 38, "_c"], [482, 4, 13, 38], [482, 7, 13, 24, "WishlistScreen"], [482, 21, 13, 38], [483, 2, 13, 38], [483, 6, 13, 38, "_c"], [483, 8, 13, 38], [484, 2, 13, 38, "$RefreshReg$"], [484, 14, 13, 38], [484, 15, 13, 38, "_c"], [484, 17, 13, 38], [485, 0, 13, 38], [485, 3]], "functionMap": {"names": ["<global>", "WishlistScreen", "removeFromWishlist", "setWishlistItems$argument_0", "items.filter$argument_0", "TouchableOpacity.props.onPress", "wishlistItems.map$argument_0"], "mappings": "AAA;eCY;6BC4C;qBCC,sBC,sBD,CD;GDC;mBIoB,yBJ;qBIwC,iCJ;+BKgB;+BD2C,iCC;aL4B;CDe"}}, "type": "js/module"}]}