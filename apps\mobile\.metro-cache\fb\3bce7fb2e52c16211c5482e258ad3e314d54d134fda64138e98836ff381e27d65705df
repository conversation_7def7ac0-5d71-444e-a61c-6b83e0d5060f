{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Libraries/ReactNative/requireNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 88}}], "key": "KBWCKirVm6qAKb3p7wadGMA7eE4=", "exportNames": ["*"]}}, {"name": "../ReactNative/UIManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 49}}], "key": "KRUgL9V6NH4fkC0TEE/DaBnYx0c=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _requireNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"../../Libraries/ReactNative/requireNativeComponent\"));\n  var _UIManager = _interopRequireDefault(require(_dependencyMap[2], \"../ReactNative/UIManager\"));\n  function codegenNativeComponent(componentName, options) {\n    if (global.RN$Bridgeless === true && __DEV__) {\n      console.warn(`Codegen didn't run for ${componentName}. This will be an error in the future. Make sure you are using @react-native/babel-preset when building your JavaScript code.`);\n    }\n    var componentNameInUse = options && options.paperComponentName != null ? options.paperComponentName : componentName;\n    if (options != null && options.paperComponentNameDeprecated != null) {\n      if (_UIManager.default.hasViewManagerConfig(componentName)) {\n        componentNameInUse = componentName;\n      } else if (options.paperComponentNameDeprecated != null && _UIManager.default.hasViewManagerConfig(options.paperComponentNameDeprecated)) {\n        componentNameInUse = options.paperComponentNameDeprecated;\n      } else {\n        throw new Error(`Failed to find native component for either ${componentName} or ${options.paperComponentNameDeprecated ?? '(unknown)'}`);\n      }\n    }\n    return (0, _requireNativeComponent.default)(componentNameInUse);\n  }\n  var _default = exports.default = codegenNativeComponent;\n});", "lineCount": 26, "map": [[7, 2, 15, 0], [7, 6, 15, 0, "_requireNativeComponent"], [7, 29, 15, 0], [7, 32, 15, 0, "_interopRequireDefault"], [7, 54, 15, 0], [7, 55, 15, 0, "require"], [7, 62, 15, 0], [7, 63, 15, 0, "_dependencyMap"], [7, 77, 15, 0], [8, 2, 16, 0], [8, 6, 16, 0, "_UIManager"], [8, 16, 16, 0], [8, 19, 16, 0, "_interopRequireDefault"], [8, 41, 16, 0], [8, 42, 16, 0, "require"], [8, 49, 16, 0], [8, 50, 16, 0, "_dependencyMap"], [8, 64, 16, 0], [9, 2, 34, 0], [9, 11, 34, 9, "codegenNativeComponent"], [9, 33, 34, 31, "codegenNativeComponent"], [9, 34, 35, 2, "componentName"], [9, 47, 35, 23], [9, 49, 36, 2, "options"], [9, 56, 36, 19], [9, 58, 37, 30], [10, 4, 38, 2], [10, 8, 38, 6, "global"], [10, 14, 38, 12], [10, 15, 38, 13, "RN$Bridgeless"], [10, 28, 38, 26], [10, 33, 38, 31], [10, 37, 38, 35], [10, 41, 38, 39, "__DEV__"], [10, 48, 38, 46], [10, 50, 38, 48], [11, 6, 39, 4, "console"], [11, 13, 39, 11], [11, 14, 39, 12, "warn"], [11, 18, 39, 16], [11, 19, 40, 6], [11, 45, 40, 32, "componentName"], [11, 58, 40, 45], [11, 185, 41, 4], [11, 186, 41, 5], [12, 4, 42, 2], [13, 4, 44, 2], [13, 8, 44, 6, "componentNameInUse"], [13, 26, 44, 24], [13, 29, 45, 4, "options"], [13, 36, 45, 11], [13, 40, 45, 15, "options"], [13, 47, 45, 22], [13, 48, 45, 23, "paperComponentName"], [13, 66, 45, 41], [13, 70, 45, 45], [13, 74, 45, 49], [13, 77, 46, 8, "options"], [13, 84, 46, 15], [13, 85, 46, 16, "paperComponentName"], [13, 103, 46, 34], [13, 106, 47, 8, "componentName"], [13, 119, 47, 21], [14, 4, 49, 2], [14, 8, 49, 6, "options"], [14, 15, 49, 13], [14, 19, 49, 17], [14, 23, 49, 21], [14, 27, 49, 25, "options"], [14, 34, 49, 32], [14, 35, 49, 33, "paperComponentNameDeprecated"], [14, 63, 49, 61], [14, 67, 49, 65], [14, 71, 49, 69], [14, 73, 49, 71], [15, 6, 50, 4], [15, 10, 50, 8, "UIManager"], [15, 28, 50, 17], [15, 29, 50, 18, "hasViewManagerConfig"], [15, 49, 50, 38], [15, 50, 50, 39, "componentName"], [15, 63, 50, 52], [15, 64, 50, 53], [15, 66, 50, 55], [16, 8, 51, 6, "componentNameInUse"], [16, 26, 51, 24], [16, 29, 51, 27, "componentName"], [16, 42, 51, 40], [17, 6, 52, 4], [17, 7, 52, 5], [17, 13, 52, 11], [17, 17, 53, 6, "options"], [17, 24, 53, 13], [17, 25, 53, 14, "paperComponentNameDeprecated"], [17, 53, 53, 42], [17, 57, 53, 46], [17, 61, 53, 50], [17, 65, 54, 6, "UIManager"], [17, 83, 54, 15], [17, 84, 54, 16, "hasViewManagerConfig"], [17, 104, 54, 36], [17, 105, 54, 37, "options"], [17, 112, 54, 44], [17, 113, 54, 45, "paperComponentNameDeprecated"], [17, 141, 54, 73], [17, 142, 54, 74], [17, 144, 55, 6], [18, 8, 57, 6, "componentNameInUse"], [18, 26, 57, 24], [18, 29, 57, 27, "options"], [18, 36, 57, 34], [18, 37, 57, 35, "paperComponentNameDeprecated"], [18, 65, 57, 63], [19, 6, 58, 4], [19, 7, 58, 5], [19, 13, 58, 11], [20, 8, 59, 6], [20, 14, 59, 12], [20, 18, 59, 16, "Error"], [20, 23, 59, 21], [20, 24, 60, 8], [20, 70, 60, 54, "componentName"], [20, 83, 60, 67], [20, 90, 61, 10, "options"], [20, 97, 61, 17], [20, 98, 61, 18, "paperComponentNameDeprecated"], [20, 126, 61, 46], [20, 130, 61, 50], [20, 141, 61, 61], [20, 143, 63, 6], [20, 144, 63, 7], [21, 6, 64, 4], [22, 4, 65, 2], [23, 4, 67, 2], [23, 11, 67, 10], [23, 15, 67, 10, "requireNativeComponent"], [23, 46, 67, 32], [23, 48, 69, 4, "componentNameInUse"], [23, 66, 70, 2], [23, 67, 70, 3], [24, 2, 71, 0], [25, 2, 71, 1], [25, 6, 71, 1, "_default"], [25, 14, 71, 1], [25, 17, 71, 1, "exports"], [25, 24, 71, 1], [25, 25, 71, 1, "default"], [25, 32, 71, 1], [25, 35, 73, 15, "codegenNativeComponent"], [25, 57, 73, 37], [26, 0, 73, 37], [26, 3]], "functionMap": {"names": ["<global>", "codegenNativeComponent"], "mappings": "AAA;ACiC;CDqC"}}, "type": "js/module"}]}