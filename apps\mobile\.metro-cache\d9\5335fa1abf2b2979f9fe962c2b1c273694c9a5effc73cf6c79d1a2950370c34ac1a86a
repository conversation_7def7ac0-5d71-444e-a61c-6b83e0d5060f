{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 85, "index": 100}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 101}, "end": {"line": 4, "column": 31, "index": 132}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 133}, "end": {"line": 5, "column": 48, "index": 181}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 182}, "end": {"line": 6, "column": 67, "index": 249}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "./Background.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 250}, "end": {"line": 7, "column": 45, "index": 295}}], "key": "rS5c0BsCtRGU5EOTkhE8gBPdgTE=", "exportNames": ["*"]}}, {"name": "./Header/getDefaultHeaderHeight.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 296}, "end": {"line": 8, "column": 76, "index": 372}}], "key": "3odPzSL37tOWjaOt2BJGt37Bg6o=", "exportNames": ["*"]}}, {"name": "./Header/HeaderHeightContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 373}, "end": {"line": 9, "column": 70, "index": 443}}], "key": "stZawU7KzLasMJlrjWF3s0um3fY=", "exportNames": ["*"]}}, {"name": "./Header/HeaderShownContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 444}, "end": {"line": 10, "column": 68, "index": 512}}], "key": "IveGqOWZUvFpozXTUMOpsU/p17I=", "exportNames": ["*"]}}, {"name": "./useFrameSize.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 513}, "end": {"line": 11, "column": 49, "index": 562}}], "key": "dRzp9Mme73SbFUGqz80tDHJoVo0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 563}, "end": {"line": 12, "column": 63, "index": 626}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Screen = Screen;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _native = require(_dependencyMap[2], \"@react-navigation/native\");\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[5], \"react-native-safe-area-context\");\n  var _Background = require(_dependencyMap[6], \"./Background.js\");\n  var _getDefaultHeaderHeight = require(_dependencyMap[7], \"./Header/getDefaultHeaderHeight.js\");\n  var _HeaderHeightContext = require(_dependencyMap[8], \"./Header/HeaderHeightContext.js\");\n  var _HeaderShownContext = require(_dependencyMap[9], \"./Header/HeaderShownContext.js\");\n  var _useFrameSize = require(_dependencyMap[10], \"./useFrameSize.js\");\n  var _jsxRuntime = require(_dependencyMap[11], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function Screen(props) {\n    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    var isParentHeaderShown = React.useContext(_HeaderShownContext.HeaderShownContext);\n    var parentHeaderHeight = React.useContext(_HeaderHeightContext.HeaderHeightContext);\n    var focused = props.focused,\n      _props$modal = props.modal,\n      modal = _props$modal === void 0 ? false : _props$modal,\n      header = props.header,\n      _props$headerShown = props.headerShown,\n      headerShown = _props$headerShown === void 0 ? true : _props$headerShown,\n      headerTransparent = props.headerTransparent,\n      _props$headerStatusBa = props.headerStatusBarHeight,\n      headerStatusBarHeight = _props$headerStatusBa === void 0 ? isParentHeaderShown ? 0 : insets.top : _props$headerStatusBa,\n      navigation = props.navigation,\n      route = props.route,\n      children = props.children,\n      style = props.style;\n    var defaultHeaderHeight = (0, _useFrameSize.useFrameSize)(size => (0, _getDefaultHeaderHeight.getDefaultHeaderHeight)(size, modal, headerStatusBarHeight));\n    var _React$useState = React.useState(defaultHeaderHeight),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      headerHeight = _React$useState2[0],\n      setHeaderHeight = _React$useState2[1];\n    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_Background.Background, {\n      \"aria-hidden\": !focused,\n      style: [styles.container, style]\n      // On Fabric we need to disable collapsing for the background to ensure\n      // that we won't render unnecessary views due to the view flattening.\n      ,\n\n      collapsable: false,\n      children: [headerShown ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_native.NavigationContext.Provider, {\n        value: navigation,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_native.NavigationRouteContext.Provider, {\n          value: route,\n          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n            pointerEvents: \"box-none\",\n            onLayout: e => {\n              var height = e.nativeEvent.layout.height;\n              setHeaderHeight(height);\n            },\n            style: [styles.header, headerTransparent ? styles.absolute : null],\n            children: header\n          })\n        })\n      }) : null, /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n        style: styles.content,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderShownContext.HeaderShownContext.Provider, {\n          value: isParentHeaderShown || headerShown !== false,\n          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderHeightContext.HeaderHeightContext.Provider, {\n            value: headerShown ? headerHeight : parentHeaderHeight ?? 0,\n            children: children\n          })\n        })\n      })]\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flex: 1\n    },\n    content: {\n      flex: 1\n    },\n    header: {\n      zIndex: 1\n    },\n    absolute: {\n      position: 'absolute',\n      top: 0,\n      start: 0,\n      end: 0\n    }\n  });\n});", "lineCount": 94, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Screen"], [8, 16, 1, 13], [8, 19, 1, 13, "Screen"], [8, 25, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_native"], [10, 13, 3, 0], [10, 16, 3, 0, "require"], [10, 23, 3, 0], [10, 24, 3, 0, "_dependencyMap"], [10, 38, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "React"], [11, 11, 4, 0], [11, 14, 4, 0, "_interopRequireWildcard"], [11, 37, 4, 0], [11, 38, 4, 0, "require"], [11, 45, 4, 0], [11, 46, 4, 0, "_dependencyMap"], [11, 60, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_reactNative"], [12, 18, 5, 0], [12, 21, 5, 0, "require"], [12, 28, 5, 0], [12, 29, 5, 0, "_dependencyMap"], [12, 43, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_reactNativeSafeAreaContext"], [13, 33, 6, 0], [13, 36, 6, 0, "require"], [13, 43, 6, 0], [13, 44, 6, 0, "_dependencyMap"], [13, 58, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_Background"], [14, 17, 7, 0], [14, 20, 7, 0, "require"], [14, 27, 7, 0], [14, 28, 7, 0, "_dependencyMap"], [14, 42, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_getDefaultHeaderHeight"], [15, 29, 8, 0], [15, 32, 8, 0, "require"], [15, 39, 8, 0], [15, 40, 8, 0, "_dependencyMap"], [15, 54, 8, 0], [16, 2, 9, 0], [16, 6, 9, 0, "_HeaderHeightContext"], [16, 26, 9, 0], [16, 29, 9, 0, "require"], [16, 36, 9, 0], [16, 37, 9, 0, "_dependencyMap"], [16, 51, 9, 0], [17, 2, 10, 0], [17, 6, 10, 0, "_HeaderShownContext"], [17, 25, 10, 0], [17, 28, 10, 0, "require"], [17, 35, 10, 0], [17, 36, 10, 0, "_dependencyMap"], [17, 50, 10, 0], [18, 2, 11, 0], [18, 6, 11, 0, "_useFrameSize"], [18, 19, 11, 0], [18, 22, 11, 0, "require"], [18, 29, 11, 0], [18, 30, 11, 0, "_dependencyMap"], [18, 44, 11, 0], [19, 2, 12, 0], [19, 6, 12, 0, "_jsxRuntime"], [19, 17, 12, 0], [19, 20, 12, 0, "require"], [19, 27, 12, 0], [19, 28, 12, 0, "_dependencyMap"], [19, 42, 12, 0], [20, 2, 12, 63], [20, 11, 12, 63, "_interopRequireWildcard"], [20, 35, 12, 63, "e"], [20, 36, 12, 63], [20, 38, 12, 63, "t"], [20, 39, 12, 63], [20, 68, 12, 63, "WeakMap"], [20, 75, 12, 63], [20, 81, 12, 63, "r"], [20, 82, 12, 63], [20, 89, 12, 63, "WeakMap"], [20, 96, 12, 63], [20, 100, 12, 63, "n"], [20, 101, 12, 63], [20, 108, 12, 63, "WeakMap"], [20, 115, 12, 63], [20, 127, 12, 63, "_interopRequireWildcard"], [20, 150, 12, 63], [20, 162, 12, 63, "_interopRequireWildcard"], [20, 163, 12, 63, "e"], [20, 164, 12, 63], [20, 166, 12, 63, "t"], [20, 167, 12, 63], [20, 176, 12, 63, "t"], [20, 177, 12, 63], [20, 181, 12, 63, "e"], [20, 182, 12, 63], [20, 186, 12, 63, "e"], [20, 187, 12, 63], [20, 188, 12, 63, "__esModule"], [20, 198, 12, 63], [20, 207, 12, 63, "e"], [20, 208, 12, 63], [20, 214, 12, 63, "o"], [20, 215, 12, 63], [20, 217, 12, 63, "i"], [20, 218, 12, 63], [20, 220, 12, 63, "f"], [20, 221, 12, 63], [20, 226, 12, 63, "__proto__"], [20, 235, 12, 63], [20, 243, 12, 63, "default"], [20, 250, 12, 63], [20, 252, 12, 63, "e"], [20, 253, 12, 63], [20, 270, 12, 63, "e"], [20, 271, 12, 63], [20, 294, 12, 63, "e"], [20, 295, 12, 63], [20, 320, 12, 63, "e"], [20, 321, 12, 63], [20, 330, 12, 63, "f"], [20, 331, 12, 63], [20, 337, 12, 63, "o"], [20, 338, 12, 63], [20, 341, 12, 63, "t"], [20, 342, 12, 63], [20, 345, 12, 63, "n"], [20, 346, 12, 63], [20, 349, 12, 63, "r"], [20, 350, 12, 63], [20, 358, 12, 63, "o"], [20, 359, 12, 63], [20, 360, 12, 63, "has"], [20, 363, 12, 63], [20, 364, 12, 63, "e"], [20, 365, 12, 63], [20, 375, 12, 63, "o"], [20, 376, 12, 63], [20, 377, 12, 63, "get"], [20, 380, 12, 63], [20, 381, 12, 63, "e"], [20, 382, 12, 63], [20, 385, 12, 63, "o"], [20, 386, 12, 63], [20, 387, 12, 63, "set"], [20, 390, 12, 63], [20, 391, 12, 63, "e"], [20, 392, 12, 63], [20, 394, 12, 63, "f"], [20, 395, 12, 63], [20, 409, 12, 63, "_t"], [20, 411, 12, 63], [20, 415, 12, 63, "e"], [20, 416, 12, 63], [20, 432, 12, 63, "_t"], [20, 434, 12, 63], [20, 441, 12, 63, "hasOwnProperty"], [20, 455, 12, 63], [20, 456, 12, 63, "call"], [20, 460, 12, 63], [20, 461, 12, 63, "e"], [20, 462, 12, 63], [20, 464, 12, 63, "_t"], [20, 466, 12, 63], [20, 473, 12, 63, "i"], [20, 474, 12, 63], [20, 478, 12, 63, "o"], [20, 479, 12, 63], [20, 482, 12, 63, "Object"], [20, 488, 12, 63], [20, 489, 12, 63, "defineProperty"], [20, 503, 12, 63], [20, 508, 12, 63, "Object"], [20, 514, 12, 63], [20, 515, 12, 63, "getOwnPropertyDescriptor"], [20, 539, 12, 63], [20, 540, 12, 63, "e"], [20, 541, 12, 63], [20, 543, 12, 63, "_t"], [20, 545, 12, 63], [20, 552, 12, 63, "i"], [20, 553, 12, 63], [20, 554, 12, 63, "get"], [20, 557, 12, 63], [20, 561, 12, 63, "i"], [20, 562, 12, 63], [20, 563, 12, 63, "set"], [20, 566, 12, 63], [20, 570, 12, 63, "o"], [20, 571, 12, 63], [20, 572, 12, 63, "f"], [20, 573, 12, 63], [20, 575, 12, 63, "_t"], [20, 577, 12, 63], [20, 579, 12, 63, "i"], [20, 580, 12, 63], [20, 584, 12, 63, "f"], [20, 585, 12, 63], [20, 586, 12, 63, "_t"], [20, 588, 12, 63], [20, 592, 12, 63, "e"], [20, 593, 12, 63], [20, 594, 12, 63, "_t"], [20, 596, 12, 63], [20, 607, 12, 63, "f"], [20, 608, 12, 63], [20, 613, 12, 63, "e"], [20, 614, 12, 63], [20, 616, 12, 63, "t"], [20, 617, 12, 63], [21, 2, 13, 7], [21, 11, 13, 16, "Screen"], [21, 17, 13, 22, "Screen"], [21, 18, 13, 23, "props"], [21, 23, 13, 28], [21, 25, 13, 30], [22, 4, 14, 2], [22, 8, 14, 8, "insets"], [22, 14, 14, 14], [22, 17, 14, 17], [22, 21, 14, 17, "useSafeAreaInsets"], [22, 66, 14, 34], [22, 68, 14, 35], [22, 69, 14, 36], [23, 4, 15, 2], [23, 8, 15, 8, "isParentHeaderShown"], [23, 27, 15, 27], [23, 30, 15, 30, "React"], [23, 35, 15, 35], [23, 36, 15, 36, "useContext"], [23, 46, 15, 46], [23, 47, 15, 47, "HeaderShownContext"], [23, 85, 15, 65], [23, 86, 15, 66], [24, 4, 16, 2], [24, 8, 16, 8, "parentHeaderHeight"], [24, 26, 16, 26], [24, 29, 16, 29, "React"], [24, 34, 16, 34], [24, 35, 16, 35, "useContext"], [24, 45, 16, 45], [24, 46, 16, 46, "HeaderHeightContext"], [24, 86, 16, 65], [24, 87, 16, 66], [25, 4, 17, 2], [25, 8, 18, 4, "focused"], [25, 15, 18, 11], [25, 18, 28, 6, "props"], [25, 23, 28, 11], [25, 24, 18, 4, "focused"], [25, 31, 18, 11], [26, 6, 18, 11, "_props$modal"], [26, 18, 18, 11], [26, 21, 28, 6, "props"], [26, 26, 28, 11], [26, 27, 19, 4, "modal"], [26, 32, 19, 9], [27, 6, 19, 4, "modal"], [27, 11, 19, 9], [27, 14, 19, 9, "_props$modal"], [27, 26, 19, 9], [27, 40, 19, 12], [27, 45, 19, 17], [27, 48, 19, 17, "_props$modal"], [27, 60, 19, 17], [28, 6, 20, 4, "header"], [28, 12, 20, 10], [28, 15, 28, 6, "props"], [28, 20, 28, 11], [28, 21, 20, 4, "header"], [28, 27, 20, 10], [29, 6, 20, 10, "_props$headerShown"], [29, 24, 20, 10], [29, 27, 28, 6, "props"], [29, 32, 28, 11], [29, 33, 21, 4, "headerShown"], [29, 44, 21, 15], [30, 6, 21, 4, "headerShown"], [30, 17, 21, 15], [30, 20, 21, 15, "_props$headerShown"], [30, 38, 21, 15], [30, 52, 21, 18], [30, 56, 21, 22], [30, 59, 21, 22, "_props$headerShown"], [30, 77, 21, 22], [31, 6, 22, 4, "headerTransparent"], [31, 23, 22, 21], [31, 26, 28, 6, "props"], [31, 31, 28, 11], [31, 32, 22, 4, "headerTransparent"], [31, 49, 22, 21], [32, 6, 22, 21, "_props$headerStatusBa"], [32, 27, 22, 21], [32, 30, 28, 6, "props"], [32, 35, 28, 11], [32, 36, 23, 4, "headerStatusBarHeight"], [32, 57, 23, 25], [33, 6, 23, 4, "headerStatusBarHeight"], [33, 27, 23, 25], [33, 30, 23, 25, "_props$headerStatusBa"], [33, 51, 23, 25], [33, 65, 23, 28, "isParentHeaderShown"], [33, 84, 23, 47], [33, 87, 23, 50], [33, 88, 23, 51], [33, 91, 23, 54, "insets"], [33, 97, 23, 60], [33, 98, 23, 61, "top"], [33, 101, 23, 64], [33, 104, 23, 64, "_props$headerStatusBa"], [33, 125, 23, 64], [34, 6, 24, 4, "navigation"], [34, 16, 24, 14], [34, 19, 28, 6, "props"], [34, 24, 28, 11], [34, 25, 24, 4, "navigation"], [34, 35, 24, 14], [35, 6, 25, 4, "route"], [35, 11, 25, 9], [35, 14, 28, 6, "props"], [35, 19, 28, 11], [35, 20, 25, 4, "route"], [35, 25, 25, 9], [36, 6, 26, 4, "children"], [36, 14, 26, 12], [36, 17, 28, 6, "props"], [36, 22, 28, 11], [36, 23, 26, 4, "children"], [36, 31, 26, 12], [37, 6, 27, 4, "style"], [37, 11, 27, 9], [37, 14, 28, 6, "props"], [37, 19, 28, 11], [37, 20, 27, 4, "style"], [37, 25, 27, 9], [38, 4, 29, 2], [38, 8, 29, 8, "defaultHeaderHeight"], [38, 27, 29, 27], [38, 30, 29, 30], [38, 34, 29, 30, "useFrameSize"], [38, 60, 29, 42], [38, 62, 29, 43, "size"], [38, 66, 29, 47], [38, 70, 29, 51], [38, 74, 29, 51, "getDefaultHeaderHeight"], [38, 120, 29, 73], [38, 122, 29, 74, "size"], [38, 126, 29, 78], [38, 128, 29, 80, "modal"], [38, 133, 29, 85], [38, 135, 29, 87, "headerStatusBarHeight"], [38, 156, 29, 108], [38, 157, 29, 109], [38, 158, 29, 110], [39, 4, 30, 2], [39, 8, 30, 2, "_React$useState"], [39, 23, 30, 2], [39, 26, 30, 42, "React"], [39, 31, 30, 47], [39, 32, 30, 48, "useState"], [39, 40, 30, 56], [39, 41, 30, 57, "defaultHeaderHeight"], [39, 60, 30, 76], [39, 61, 30, 77], [40, 6, 30, 77, "_React$useState2"], [40, 22, 30, 77], [40, 29, 30, 77, "_slicedToArray2"], [40, 44, 30, 77], [40, 45, 30, 77, "default"], [40, 52, 30, 77], [40, 54, 30, 77, "_React$useState"], [40, 69, 30, 77], [41, 6, 30, 9, "headerHeight"], [41, 18, 30, 21], [41, 21, 30, 21, "_React$useState2"], [41, 37, 30, 21], [42, 6, 30, 23, "setHeaderHeight"], [42, 21, 30, 38], [42, 24, 30, 38, "_React$useState2"], [42, 40, 30, 38], [43, 4, 31, 2], [43, 11, 31, 9], [43, 24, 31, 22], [43, 28, 31, 22, "_jsxs"], [43, 44, 31, 27], [43, 46, 31, 28, "Background"], [43, 68, 31, 38], [43, 70, 31, 40], [44, 6, 32, 4], [44, 19, 32, 17], [44, 21, 32, 19], [44, 22, 32, 20, "focused"], [44, 29, 32, 27], [45, 6, 33, 4, "style"], [45, 11, 33, 9], [45, 13, 33, 11], [45, 14, 33, 12, "styles"], [45, 20, 33, 18], [45, 21, 33, 19, "container"], [45, 30, 33, 28], [45, 32, 33, 30, "style"], [45, 37, 33, 35], [46, 6, 34, 4], [47, 6, 35, 4], [48, 6, 35, 4], [50, 6, 37, 4, "collapsable"], [50, 17, 37, 15], [50, 19, 37, 17], [50, 24, 37, 22], [51, 6, 38, 4, "children"], [51, 14, 38, 12], [51, 16, 38, 14], [51, 17, 38, 15, "headerShown"], [51, 28, 38, 26], [51, 31, 38, 29], [51, 44, 38, 42], [51, 48, 38, 42, "_jsx"], [51, 63, 38, 46], [51, 65, 38, 47, "NavigationContext"], [51, 90, 38, 64], [51, 91, 38, 65, "Provider"], [51, 99, 38, 73], [51, 101, 38, 75], [52, 8, 39, 6, "value"], [52, 13, 39, 11], [52, 15, 39, 13, "navigation"], [52, 25, 39, 23], [53, 8, 40, 6, "children"], [53, 16, 40, 14], [53, 18, 40, 16], [53, 31, 40, 29], [53, 35, 40, 29, "_jsx"], [53, 50, 40, 33], [53, 52, 40, 34, "NavigationRouteContext"], [53, 82, 40, 56], [53, 83, 40, 57, "Provider"], [53, 91, 40, 65], [53, 93, 40, 67], [54, 10, 41, 8, "value"], [54, 15, 41, 13], [54, 17, 41, 15, "route"], [54, 22, 41, 20], [55, 10, 42, 8, "children"], [55, 18, 42, 16], [55, 20, 42, 18], [55, 33, 42, 31], [55, 37, 42, 31, "_jsx"], [55, 52, 42, 35], [55, 54, 42, 36, "View"], [55, 71, 42, 40], [55, 73, 42, 42], [56, 12, 43, 10, "pointerEvents"], [56, 25, 43, 23], [56, 27, 43, 25], [56, 37, 43, 35], [57, 12, 44, 10, "onLayout"], [57, 20, 44, 18], [57, 22, 44, 20, "e"], [57, 23, 44, 21], [57, 27, 44, 25], [58, 14, 45, 12], [58, 18, 46, 14, "height"], [58, 24, 46, 20], [58, 27, 47, 16, "e"], [58, 28, 47, 17], [58, 29, 47, 18, "nativeEvent"], [58, 40, 47, 29], [58, 41, 47, 30, "layout"], [58, 47, 47, 36], [58, 48, 46, 14, "height"], [58, 54, 46, 20], [59, 14, 48, 12, "setHeaderHeight"], [59, 29, 48, 27], [59, 30, 48, 28, "height"], [59, 36, 48, 34], [59, 37, 48, 35], [60, 12, 49, 10], [60, 13, 49, 11], [61, 12, 50, 10, "style"], [61, 17, 50, 15], [61, 19, 50, 17], [61, 20, 50, 18, "styles"], [61, 26, 50, 24], [61, 27, 50, 25, "header"], [61, 33, 50, 31], [61, 35, 50, 33, "headerTransparent"], [61, 52, 50, 50], [61, 55, 50, 53, "styles"], [61, 61, 50, 59], [61, 62, 50, 60, "absolute"], [61, 70, 50, 68], [61, 73, 50, 71], [61, 77, 50, 75], [61, 78, 50, 76], [62, 12, 51, 10, "children"], [62, 20, 51, 18], [62, 22, 51, 20, "header"], [63, 10, 52, 8], [63, 11, 52, 9], [64, 8, 53, 6], [64, 9, 53, 7], [65, 6, 54, 4], [65, 7, 54, 5], [65, 8, 54, 6], [65, 11, 54, 9], [65, 15, 54, 13], [65, 17, 54, 15], [65, 30, 54, 28], [65, 34, 54, 28, "_jsx"], [65, 49, 54, 32], [65, 51, 54, 33, "View"], [65, 68, 54, 37], [65, 70, 54, 39], [66, 8, 55, 6, "style"], [66, 13, 55, 11], [66, 15, 55, 13, "styles"], [66, 21, 55, 19], [66, 22, 55, 20, "content"], [66, 29, 55, 27], [67, 8, 56, 6, "children"], [67, 16, 56, 14], [67, 18, 56, 16], [67, 31, 56, 29], [67, 35, 56, 29, "_jsx"], [67, 50, 56, 33], [67, 52, 56, 34, "HeaderShownContext"], [67, 90, 56, 52], [67, 91, 56, 53, "Provider"], [67, 99, 56, 61], [67, 101, 56, 63], [68, 10, 57, 8, "value"], [68, 15, 57, 13], [68, 17, 57, 15, "isParentHeaderShown"], [68, 36, 57, 34], [68, 40, 57, 38, "headerShown"], [68, 51, 57, 49], [68, 56, 57, 54], [68, 61, 57, 59], [69, 10, 58, 8, "children"], [69, 18, 58, 16], [69, 20, 58, 18], [69, 33, 58, 31], [69, 37, 58, 31, "_jsx"], [69, 52, 58, 35], [69, 54, 58, 36, "HeaderHeightContext"], [69, 94, 58, 55], [69, 95, 58, 56, "Provider"], [69, 103, 58, 64], [69, 105, 58, 66], [70, 12, 59, 10, "value"], [70, 17, 59, 15], [70, 19, 59, 17, "headerShown"], [70, 30, 59, 28], [70, 33, 59, 31, "headerHeight"], [70, 45, 59, 43], [70, 48, 59, 46, "parentHeaderHeight"], [70, 66, 59, 64], [70, 70, 59, 68], [70, 71, 59, 69], [71, 12, 60, 10, "children"], [71, 20, 60, 18], [71, 22, 60, 20, "children"], [72, 10, 61, 8], [72, 11, 61, 9], [73, 8, 62, 6], [73, 9, 62, 7], [74, 6, 63, 4], [74, 7, 63, 5], [74, 8, 63, 6], [75, 4, 64, 2], [75, 5, 64, 3], [75, 6, 64, 4], [76, 2, 65, 0], [77, 2, 66, 0], [77, 6, 66, 6, "styles"], [77, 12, 66, 12], [77, 15, 66, 15, "StyleSheet"], [77, 38, 66, 25], [77, 39, 66, 26, "create"], [77, 45, 66, 32], [77, 46, 66, 33], [78, 4, 67, 2, "container"], [78, 13, 67, 11], [78, 15, 67, 13], [79, 6, 68, 4, "flex"], [79, 10, 68, 8], [79, 12, 68, 10], [80, 4, 69, 2], [80, 5, 69, 3], [81, 4, 70, 2, "content"], [81, 11, 70, 9], [81, 13, 70, 11], [82, 6, 71, 4, "flex"], [82, 10, 71, 8], [82, 12, 71, 10], [83, 4, 72, 2], [83, 5, 72, 3], [84, 4, 73, 2, "header"], [84, 10, 73, 8], [84, 12, 73, 10], [85, 6, 74, 4, "zIndex"], [85, 12, 74, 10], [85, 14, 74, 12], [86, 4, 75, 2], [86, 5, 75, 3], [87, 4, 76, 2, "absolute"], [87, 12, 76, 10], [87, 14, 76, 12], [88, 6, 77, 4, "position"], [88, 14, 77, 12], [88, 16, 77, 14], [88, 26, 77, 24], [89, 6, 78, 4, "top"], [89, 9, 78, 7], [89, 11, 78, 9], [89, 12, 78, 10], [90, 6, 79, 4, "start"], [90, 11, 79, 9], [90, 13, 79, 11], [90, 14, 79, 12], [91, 6, 80, 4, "end"], [91, 9, 80, 7], [91, 11, 80, 9], [92, 4, 81, 2], [93, 2, 82, 0], [93, 3, 82, 1], [93, 4, 82, 2], [94, 0, 82, 3], [94, 3]], "functionMap": {"names": ["<global>", "Screen", "useFrameSize$argument_0", "_jsx$argument_1.onLayout"], "mappings": "AAA;OCY;2CCgB,kED;oBEe;WFK;CDgB"}}, "type": "js/module"}]}