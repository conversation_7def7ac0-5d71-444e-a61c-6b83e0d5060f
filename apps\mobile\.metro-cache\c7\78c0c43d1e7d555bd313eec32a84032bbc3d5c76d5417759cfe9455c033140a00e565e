{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 59}}], "key": "pb9N0Xpf+NPEwAXmL7T0XcgBMDo=", "exportNames": ["*"]}}, {"name": "../../../Libraries/ReactNative/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 93}}], "key": "uaEeDOwphMrjo4PQMRtEMx2ofc0=", "exportNames": ["*"]}}, {"name": "../../../Libraries/StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 66}}], "key": "Df3Frl1R7NQ+cYiuuV9KQAE/Y2Y=", "exportNames": ["*"]}}, {"name": "./ElementBox", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 38}}], "key": "dWcPHQMpieWViJso3N78yRXHPYE=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}, {"name": "./getInspectorDataForViewAtPoint", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 45}}], "key": "n4e/pQZqtVLHQ80AxR71Lb2FRkM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = ReactDevToolsOverlay;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../../../Libraries/Components/View/View\"));\n  var _ReactNativeFeatureFlags = _interopRequireDefault(require(_dependencyMap[3], \"../../../Libraries/ReactNative/ReactNativeFeatureFlags\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"../../../Libraries/StyleSheet/StyleSheet\"));\n  var _ElementBox = _interopRequireDefault(require(_dependencyMap[5], \"./ElementBox\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\src\\\\private\\\\inspector\\\\ReactDevToolsOverlay.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var getInspectorDataForViewAtPoint = require(_dependencyMap[8], \"./getInspectorDataForViewAtPoint\").default;\n  var useEffect = React.useEffect,\n    useState = React.useState,\n    useCallback = React.useCallback;\n  function ReactDevToolsOverlay(_ref) {\n    var inspectedViewRef = _ref.inspectedViewRef,\n      reactDevToolsAgent = _ref.reactDevToolsAgent;\n    var _useState = useState(null),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      inspected = _useState2[0],\n      setInspected = _useState2[1];\n    var _useState3 = useState(false),\n      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),\n      isInspecting = _useState4[0],\n      setIsInspecting = _useState4[1];\n    useEffect(() => {\n      function cleanup() {\n        reactDevToolsAgent.removeListener('shutdown', cleanup);\n        reactDevToolsAgent.removeListener('startInspectingNative', onStartInspectingNative);\n        reactDevToolsAgent.removeListener('stopInspectingNative', onStopInspectingNative);\n      }\n      function onStartInspectingNative() {\n        setIsInspecting(true);\n      }\n      function onStopInspectingNative() {\n        setIsInspecting(false);\n      }\n      reactDevToolsAgent.addListener('shutdown', cleanup);\n      reactDevToolsAgent.addListener('startInspectingNative', onStartInspectingNative);\n      reactDevToolsAgent.addListener('stopInspectingNative', onStopInspectingNative);\n      return cleanup;\n    }, [reactDevToolsAgent]);\n    var findViewForLocation = useCallback((x, y) => {\n      getInspectorDataForViewAtPoint(inspectedViewRef.current, x, y, viewData => {\n        var frame = viewData.frame,\n          closestPublicInstance = viewData.closestPublicInstance;\n        if (closestPublicInstance == null) {\n          return false;\n        }\n        reactDevToolsAgent.selectNode(closestPublicInstance);\n        setInspected({\n          frame\n        });\n        return true;\n      });\n    }, [inspectedViewRef, reactDevToolsAgent]);\n    var stopInspecting = useCallback(() => {\n      reactDevToolsAgent.stopInspectingNative(true);\n      setIsInspecting(false);\n      setInspected(null);\n    }, [reactDevToolsAgent]);\n    var onPointerMove = useCallback(e => {\n      findViewForLocation(e.nativeEvent.x, e.nativeEvent.y);\n    }, [findViewForLocation]);\n    var onResponderMove = useCallback(e => {\n      findViewForLocation(e.nativeEvent.touches[0].locationX, e.nativeEvent.touches[0].locationY);\n    }, [findViewForLocation]);\n    var shouldSetResponder = useCallback(e => {\n      onResponderMove(e);\n      return true;\n    }, [onResponderMove]);\n    var highlight = inspected ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ElementBox.default, {\n      frame: inspected.frame\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 33\n    }, this) : null;\n    if (isInspecting) {\n      var events = _ReactNativeFeatureFlags.default.shouldEmitW3CPointerEvents() ? {\n        onPointerMove,\n        onPointerDown: onPointerMove,\n        onPointerUp: stopInspecting\n      } : {\n        onStartShouldSetResponder: shouldSetResponder,\n        onResponderMove: onResponderMove,\n        onResponderRelease: stopInspecting\n      };\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        nativeID: \"devToolsInspectorOverlay\",\n        style: styles.inspector,\n        ...events,\n        children: highlight\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 7\n      }, this);\n    }\n    return highlight;\n  }\n  var styles = _StyleSheet.default.create({\n    inspector: {\n      backgroundColor: 'transparent',\n      position: 'absolute',\n      left: 0,\n      top: 0,\n      right: 0,\n      bottom: 0\n    }\n  });\n});", "lineCount": 117, "map": [[8, 2, 17, 0], [8, 6, 17, 0, "_View"], [8, 11, 17, 0], [8, 14, 17, 0, "_interopRequireDefault"], [8, 36, 17, 0], [8, 37, 17, 0, "require"], [8, 44, 17, 0], [8, 45, 17, 0, "_dependencyMap"], [8, 59, 17, 0], [9, 2, 18, 0], [9, 6, 18, 0, "_ReactNativeFeatureFlags"], [9, 30, 18, 0], [9, 33, 18, 0, "_interopRequireDefault"], [9, 55, 18, 0], [9, 56, 18, 0, "require"], [9, 63, 18, 0], [9, 64, 18, 0, "_dependencyMap"], [9, 78, 18, 0], [10, 2, 19, 0], [10, 6, 19, 0, "_StyleSheet"], [10, 17, 19, 0], [10, 20, 19, 0, "_interopRequireDefault"], [10, 42, 19, 0], [10, 43, 19, 0, "require"], [10, 50, 19, 0], [10, 51, 19, 0, "_dependencyMap"], [10, 65, 19, 0], [11, 2, 20, 0], [11, 6, 20, 0, "_ElementBox"], [11, 17, 20, 0], [11, 20, 20, 0, "_interopRequireDefault"], [11, 42, 20, 0], [11, 43, 20, 0, "require"], [11, 50, 20, 0], [11, 51, 20, 0, "_dependencyMap"], [11, 65, 20, 0], [12, 2, 21, 0], [12, 6, 21, 0, "React"], [12, 11, 21, 0], [12, 14, 21, 0, "_interopRequireWildcard"], [12, 37, 21, 0], [12, 38, 21, 0, "require"], [12, 45, 21, 0], [12, 46, 21, 0, "_dependencyMap"], [12, 60, 21, 0], [13, 2, 21, 31], [13, 6, 21, 31, "_jsxDevRuntime"], [13, 20, 21, 31], [13, 23, 21, 31, "require"], [13, 30, 21, 31], [13, 31, 21, 31, "_dependencyMap"], [13, 45, 21, 31], [14, 2, 21, 31], [14, 6, 21, 31, "_jsxFileName"], [14, 18, 21, 31], [15, 2, 21, 31], [15, 11, 21, 31, "_interopRequireWildcard"], [15, 35, 21, 31, "e"], [15, 36, 21, 31], [15, 38, 21, 31, "t"], [15, 39, 21, 31], [15, 68, 21, 31, "WeakMap"], [15, 75, 21, 31], [15, 81, 21, 31, "r"], [15, 82, 21, 31], [15, 89, 21, 31, "WeakMap"], [15, 96, 21, 31], [15, 100, 21, 31, "n"], [15, 101, 21, 31], [15, 108, 21, 31, "WeakMap"], [15, 115, 21, 31], [15, 127, 21, 31, "_interopRequireWildcard"], [15, 150, 21, 31], [15, 162, 21, 31, "_interopRequireWildcard"], [15, 163, 21, 31, "e"], [15, 164, 21, 31], [15, 166, 21, 31, "t"], [15, 167, 21, 31], [15, 176, 21, 31, "t"], [15, 177, 21, 31], [15, 181, 21, 31, "e"], [15, 182, 21, 31], [15, 186, 21, 31, "e"], [15, 187, 21, 31], [15, 188, 21, 31, "__esModule"], [15, 198, 21, 31], [15, 207, 21, 31, "e"], [15, 208, 21, 31], [15, 214, 21, 31, "o"], [15, 215, 21, 31], [15, 217, 21, 31, "i"], [15, 218, 21, 31], [15, 220, 21, 31, "f"], [15, 221, 21, 31], [15, 226, 21, 31, "__proto__"], [15, 235, 21, 31], [15, 243, 21, 31, "default"], [15, 250, 21, 31], [15, 252, 21, 31, "e"], [15, 253, 21, 31], [15, 270, 21, 31, "e"], [15, 271, 21, 31], [15, 294, 21, 31, "e"], [15, 295, 21, 31], [15, 320, 21, 31, "e"], [15, 321, 21, 31], [15, 330, 21, 31, "f"], [15, 331, 21, 31], [15, 337, 21, 31, "o"], [15, 338, 21, 31], [15, 341, 21, 31, "t"], [15, 342, 21, 31], [15, 345, 21, 31, "n"], [15, 346, 21, 31], [15, 349, 21, 31, "r"], [15, 350, 21, 31], [15, 358, 21, 31, "o"], [15, 359, 21, 31], [15, 360, 21, 31, "has"], [15, 363, 21, 31], [15, 364, 21, 31, "e"], [15, 365, 21, 31], [15, 375, 21, 31, "o"], [15, 376, 21, 31], [15, 377, 21, 31, "get"], [15, 380, 21, 31], [15, 381, 21, 31, "e"], [15, 382, 21, 31], [15, 385, 21, 31, "o"], [15, 386, 21, 31], [15, 387, 21, 31, "set"], [15, 390, 21, 31], [15, 391, 21, 31, "e"], [15, 392, 21, 31], [15, 394, 21, 31, "f"], [15, 395, 21, 31], [15, 409, 21, 31, "_t"], [15, 411, 21, 31], [15, 415, 21, 31, "e"], [15, 416, 21, 31], [15, 432, 21, 31, "_t"], [15, 434, 21, 31], [15, 441, 21, 31, "hasOwnProperty"], [15, 455, 21, 31], [15, 456, 21, 31, "call"], [15, 460, 21, 31], [15, 461, 21, 31, "e"], [15, 462, 21, 31], [15, 464, 21, 31, "_t"], [15, 466, 21, 31], [15, 473, 21, 31, "i"], [15, 474, 21, 31], [15, 478, 21, 31, "o"], [15, 479, 21, 31], [15, 482, 21, 31, "Object"], [15, 488, 21, 31], [15, 489, 21, 31, "defineProperty"], [15, 503, 21, 31], [15, 508, 21, 31, "Object"], [15, 514, 21, 31], [15, 515, 21, 31, "getOwnPropertyDescriptor"], [15, 539, 21, 31], [15, 540, 21, 31, "e"], [15, 541, 21, 31], [15, 543, 21, 31, "_t"], [15, 545, 21, 31], [15, 552, 21, 31, "i"], [15, 553, 21, 31], [15, 554, 21, 31, "get"], [15, 557, 21, 31], [15, 561, 21, 31, "i"], [15, 562, 21, 31], [15, 563, 21, 31, "set"], [15, 566, 21, 31], [15, 570, 21, 31, "o"], [15, 571, 21, 31], [15, 572, 21, 31, "f"], [15, 573, 21, 31], [15, 575, 21, 31, "_t"], [15, 577, 21, 31], [15, 579, 21, 31, "i"], [15, 580, 21, 31], [15, 584, 21, 31, "f"], [15, 585, 21, 31], [15, 586, 21, 31, "_t"], [15, 588, 21, 31], [15, 592, 21, 31, "e"], [15, 593, 21, 31], [15, 594, 21, 31, "_t"], [15, 596, 21, 31], [15, 607, 21, 31, "f"], [15, 608, 21, 31], [15, 613, 21, 31, "e"], [15, 614, 21, 31], [15, 616, 21, 31, "t"], [15, 617, 21, 31], [16, 2, 23, 0], [16, 6, 23, 6, "getInspectorDataForViewAtPoint"], [16, 36, 23, 36], [16, 39, 24, 2, "require"], [16, 46, 24, 9], [16, 47, 24, 9, "_dependencyMap"], [16, 61, 24, 9], [16, 100, 24, 44], [16, 101, 24, 45], [16, 102, 24, 46, "default"], [16, 109, 24, 53], [17, 2, 26, 0], [17, 6, 26, 7, "useEffect"], [17, 15, 26, 16], [17, 18, 26, 43, "React"], [17, 23, 26, 48], [17, 24, 26, 7, "useEffect"], [17, 33, 26, 16], [18, 4, 26, 18, "useState"], [18, 12, 26, 26], [18, 15, 26, 43, "React"], [18, 20, 26, 48], [18, 21, 26, 18, "useState"], [18, 29, 26, 26], [19, 4, 26, 28, "useCallback"], [19, 15, 26, 39], [19, 18, 26, 43, "React"], [19, 23, 26, 48], [19, 24, 26, 28, "useCallback"], [19, 35, 26, 39], [20, 2, 33, 15], [20, 11, 33, 24, "ReactDevToolsOverlay"], [20, 31, 33, 44, "ReactDevToolsOverlay"], [20, 32, 33, 44, "_ref"], [20, 36, 33, 44], [20, 38, 36, 22], [21, 4, 36, 22], [21, 8, 34, 2, "inspectedViewRef"], [21, 24, 34, 18], [21, 27, 34, 18, "_ref"], [21, 31, 34, 18], [21, 32, 34, 2, "inspectedViewRef"], [21, 48, 34, 18], [22, 6, 35, 2, "reactDevToolsAgent"], [22, 24, 35, 20], [22, 27, 35, 20, "_ref"], [22, 31, 35, 20], [22, 32, 35, 2, "reactDevToolsAgent"], [22, 50, 35, 20], [23, 4, 37, 2], [23, 8, 37, 2, "_useState"], [23, 17, 37, 2], [23, 20, 37, 36, "useState"], [23, 28, 37, 44], [23, 29, 37, 64], [23, 33, 37, 68], [23, 34, 37, 69], [24, 6, 37, 69, "_useState2"], [24, 16, 37, 69], [24, 23, 37, 69, "_slicedToArray2"], [24, 38, 37, 69], [24, 39, 37, 69, "default"], [24, 46, 37, 69], [24, 48, 37, 69, "_useState"], [24, 57, 37, 69], [25, 6, 37, 9, "inspected"], [25, 15, 37, 18], [25, 18, 37, 18, "_useState2"], [25, 28, 37, 18], [26, 6, 37, 20, "setInspected"], [26, 18, 37, 32], [26, 21, 37, 32, "_useState2"], [26, 31, 37, 32], [27, 4, 38, 2], [27, 8, 38, 2, "_useState3"], [27, 18, 38, 2], [27, 21, 38, 42, "useState"], [27, 29, 38, 50], [27, 30, 38, 51], [27, 35, 38, 56], [27, 36, 38, 57], [28, 6, 38, 57, "_useState4"], [28, 16, 38, 57], [28, 23, 38, 57, "_slicedToArray2"], [28, 38, 38, 57], [28, 39, 38, 57, "default"], [28, 46, 38, 57], [28, 48, 38, 57, "_useState3"], [28, 58, 38, 57], [29, 6, 38, 9, "isInspecting"], [29, 18, 38, 21], [29, 21, 38, 21, "_useState4"], [29, 31, 38, 21], [30, 6, 38, 23, "setIsInspecting"], [30, 21, 38, 38], [30, 24, 38, 38, "_useState4"], [30, 34, 38, 38], [31, 4, 40, 2, "useEffect"], [31, 13, 40, 11], [31, 14, 40, 12], [31, 20, 40, 18], [32, 6, 41, 4], [32, 15, 41, 13, "cleanup"], [32, 22, 41, 20, "cleanup"], [32, 23, 41, 20], [32, 25, 41, 23], [33, 8, 42, 6, "reactDevToolsAgent"], [33, 26, 42, 24], [33, 27, 42, 25, "removeListener"], [33, 41, 42, 39], [33, 42, 42, 40], [33, 52, 42, 50], [33, 54, 42, 52, "cleanup"], [33, 61, 42, 59], [33, 62, 42, 60], [34, 8, 43, 6, "reactDevToolsAgent"], [34, 26, 43, 24], [34, 27, 43, 25, "removeListener"], [34, 41, 43, 39], [34, 42, 44, 8], [34, 65, 44, 31], [34, 67, 45, 8, "onStartInspectingNative"], [34, 90, 46, 6], [34, 91, 46, 7], [35, 8, 47, 6, "reactDevToolsAgent"], [35, 26, 47, 24], [35, 27, 47, 25, "removeListener"], [35, 41, 47, 39], [35, 42, 48, 8], [35, 64, 48, 30], [35, 66, 49, 8, "onStopInspectingNative"], [35, 88, 50, 6], [35, 89, 50, 7], [36, 6, 51, 4], [37, 6, 53, 4], [37, 15, 53, 13, "onStartInspectingNative"], [37, 38, 53, 36, "onStartInspectingNative"], [37, 39, 53, 36], [37, 41, 53, 39], [38, 8, 54, 6, "setIsInspecting"], [38, 23, 54, 21], [38, 24, 54, 22], [38, 28, 54, 26], [38, 29, 54, 27], [39, 6, 55, 4], [40, 6, 57, 4], [40, 15, 57, 13, "onStopInspectingNative"], [40, 37, 57, 35, "onStopInspectingNative"], [40, 38, 57, 35], [40, 40, 57, 38], [41, 8, 58, 6, "setIsInspecting"], [41, 23, 58, 21], [41, 24, 58, 22], [41, 29, 58, 27], [41, 30, 58, 28], [42, 6, 59, 4], [43, 6, 61, 4, "reactDevToolsAgent"], [43, 24, 61, 22], [43, 25, 61, 23, "addListener"], [43, 36, 61, 34], [43, 37, 61, 35], [43, 47, 61, 45], [43, 49, 61, 47, "cleanup"], [43, 56, 61, 54], [43, 57, 61, 55], [44, 6, 62, 4, "reactDevToolsAgent"], [44, 24, 62, 22], [44, 25, 62, 23, "addListener"], [44, 36, 62, 34], [44, 37, 63, 6], [44, 60, 63, 29], [44, 62, 64, 6, "onStartInspectingNative"], [44, 85, 65, 4], [44, 86, 65, 5], [45, 6, 66, 4, "reactDevToolsAgent"], [45, 24, 66, 22], [45, 25, 66, 23, "addListener"], [45, 36, 66, 34], [45, 37, 67, 6], [45, 59, 67, 28], [45, 61, 68, 6, "onStopInspectingNative"], [45, 83, 69, 4], [45, 84, 69, 5], [46, 6, 71, 4], [46, 13, 71, 11, "cleanup"], [46, 20, 71, 18], [47, 4, 72, 2], [47, 5, 72, 3], [47, 7, 72, 5], [47, 8, 72, 6, "reactDevToolsAgent"], [47, 26, 72, 24], [47, 27, 72, 25], [47, 28, 72, 26], [48, 4, 74, 2], [48, 8, 74, 8, "findViewForLocation"], [48, 27, 74, 27], [48, 30, 74, 30, "useCallback"], [48, 41, 74, 41], [48, 42, 75, 4], [48, 43, 75, 5, "x"], [48, 44, 75, 14], [48, 46, 75, 16, "y"], [48, 47, 75, 25], [48, 52, 75, 30], [49, 6, 76, 6, "getInspectorDataForViewAtPoint"], [49, 36, 76, 36], [49, 37, 77, 8, "inspectedViewRef"], [49, 53, 77, 24], [49, 54, 77, 25, "current"], [49, 61, 77, 32], [49, 63, 78, 8, "x"], [49, 64, 78, 9], [49, 66, 79, 8, "y"], [49, 67, 79, 9], [49, 69, 80, 8, "viewData"], [49, 77, 80, 16], [49, 81, 80, 20], [50, 8, 81, 10], [50, 12, 81, 17, "frame"], [50, 17, 81, 22], [50, 20, 81, 49, "viewData"], [50, 28, 81, 57], [50, 29, 81, 17, "frame"], [50, 34, 81, 22], [51, 10, 81, 24, "closestPublicInstance"], [51, 31, 81, 45], [51, 34, 81, 49, "viewData"], [51, 42, 81, 57], [51, 43, 81, 24, "closestPublicInstance"], [51, 64, 81, 45], [52, 8, 83, 10], [52, 12, 83, 14, "closestPublicInstance"], [52, 33, 83, 35], [52, 37, 83, 39], [52, 41, 83, 43], [52, 43, 83, 45], [53, 10, 84, 12], [53, 17, 84, 19], [53, 22, 84, 24], [54, 8, 85, 10], [55, 8, 87, 10, "reactDevToolsAgent"], [55, 26, 87, 28], [55, 27, 87, 29, "selectNode"], [55, 37, 87, 39], [55, 38, 87, 40, "closestPublicInstance"], [55, 59, 87, 61], [55, 60, 87, 62], [56, 8, 88, 10, "setInspected"], [56, 20, 88, 22], [56, 21, 88, 23], [57, 10, 88, 24, "frame"], [58, 8, 88, 29], [58, 9, 88, 30], [58, 10, 88, 31], [59, 8, 89, 10], [59, 15, 89, 17], [59, 19, 89, 21], [60, 6, 90, 8], [60, 7, 91, 6], [60, 8, 91, 7], [61, 4, 92, 4], [61, 5, 92, 5], [61, 7, 93, 4], [61, 8, 93, 5, "inspectedViewRef"], [61, 24, 93, 21], [61, 26, 93, 23, "reactDevToolsAgent"], [61, 44, 93, 41], [61, 45, 94, 2], [61, 46, 94, 3], [62, 4, 96, 2], [62, 8, 96, 8, "stopInspecting"], [62, 22, 96, 22], [62, 25, 96, 25, "useCallback"], [62, 36, 96, 36], [62, 37, 96, 37], [62, 43, 96, 43], [63, 6, 97, 4, "reactDevToolsAgent"], [63, 24, 97, 22], [63, 25, 97, 23, "stopInspectingNative"], [63, 45, 97, 43], [63, 46, 97, 44], [63, 50, 97, 48], [63, 51, 97, 49], [64, 6, 98, 4, "setIsInspecting"], [64, 21, 98, 19], [64, 22, 98, 20], [64, 27, 98, 25], [64, 28, 98, 26], [65, 6, 99, 4, "setInspected"], [65, 18, 99, 16], [65, 19, 99, 17], [65, 23, 99, 21], [65, 24, 99, 22], [66, 4, 100, 2], [66, 5, 100, 3], [66, 7, 100, 5], [66, 8, 100, 6, "reactDevToolsAgent"], [66, 26, 100, 24], [66, 27, 100, 25], [66, 28, 100, 26], [67, 4, 102, 2], [67, 8, 102, 8, "onPointerMove"], [67, 21, 102, 21], [67, 24, 102, 24, "useCallback"], [67, 35, 102, 35], [67, 36, 103, 5, "e"], [67, 37, 103, 20], [67, 41, 103, 25], [68, 6, 104, 6, "findViewForLocation"], [68, 25, 104, 25], [68, 26, 104, 26, "e"], [68, 27, 104, 27], [68, 28, 104, 28, "nativeEvent"], [68, 39, 104, 39], [68, 40, 104, 40, "x"], [68, 41, 104, 41], [68, 43, 104, 43, "e"], [68, 44, 104, 44], [68, 45, 104, 45, "nativeEvent"], [68, 56, 104, 56], [68, 57, 104, 57, "y"], [68, 58, 104, 58], [68, 59, 104, 59], [69, 4, 105, 4], [69, 5, 105, 5], [69, 7, 106, 4], [69, 8, 106, 5, "findViewForLocation"], [69, 27, 106, 24], [69, 28, 107, 2], [69, 29, 107, 3], [70, 4, 109, 2], [70, 8, 109, 8, "onResponderMove"], [70, 23, 109, 23], [70, 26, 109, 26, "useCallback"], [70, 37, 109, 37], [70, 38, 110, 5, "e"], [70, 39, 110, 29], [70, 43, 110, 34], [71, 6, 111, 6, "findViewForLocation"], [71, 25, 111, 25], [71, 26, 112, 8, "e"], [71, 27, 112, 9], [71, 28, 112, 10, "nativeEvent"], [71, 39, 112, 21], [71, 40, 112, 22, "touches"], [71, 47, 112, 29], [71, 48, 112, 30], [71, 49, 112, 31], [71, 50, 112, 32], [71, 51, 112, 33, "locationX"], [71, 60, 112, 42], [71, 62, 113, 8, "e"], [71, 63, 113, 9], [71, 64, 113, 10, "nativeEvent"], [71, 75, 113, 21], [71, 76, 113, 22, "touches"], [71, 83, 113, 29], [71, 84, 113, 30], [71, 85, 113, 31], [71, 86, 113, 32], [71, 87, 113, 33, "locationY"], [71, 96, 114, 6], [71, 97, 114, 7], [72, 4, 115, 4], [72, 5, 115, 5], [72, 7, 116, 4], [72, 8, 116, 5, "findViewForLocation"], [72, 27, 116, 24], [72, 28, 117, 2], [72, 29, 117, 3], [73, 4, 119, 2], [73, 8, 119, 8, "shouldSetResponder"], [73, 26, 119, 26], [73, 29, 119, 29, "useCallback"], [73, 40, 119, 40], [73, 41, 120, 5, "e"], [73, 42, 120, 29], [73, 46, 120, 43], [74, 6, 121, 6, "onResponderMove"], [74, 21, 121, 21], [74, 22, 121, 22, "e"], [74, 23, 121, 23], [74, 24, 121, 24], [75, 6, 122, 6], [75, 13, 122, 13], [75, 17, 122, 17], [76, 4, 123, 4], [76, 5, 123, 5], [76, 7, 124, 4], [76, 8, 124, 5, "onResponderMove"], [76, 23, 124, 20], [76, 24, 125, 2], [76, 25, 125, 3], [77, 4, 127, 2], [77, 8, 127, 8, "highlight"], [77, 17, 127, 17], [77, 20, 127, 20, "inspected"], [77, 29, 127, 29], [77, 45, 127, 32], [77, 49, 127, 32, "_jsxDevRuntime"], [77, 63, 127, 32], [77, 64, 127, 32, "jsxDEV"], [77, 70, 127, 32], [77, 72, 127, 33, "_ElementBox"], [77, 83, 127, 33], [77, 84, 127, 33, "default"], [77, 91, 127, 43], [78, 6, 127, 44, "frame"], [78, 11, 127, 49], [78, 13, 127, 51, "inspected"], [78, 22, 127, 60], [78, 23, 127, 61, "frame"], [79, 4, 127, 67], [80, 6, 127, 67, "fileName"], [80, 14, 127, 67], [80, 16, 127, 67, "_jsxFileName"], [80, 28, 127, 67], [81, 6, 127, 67, "lineNumber"], [81, 16, 127, 67], [82, 6, 127, 67, "columnNumber"], [82, 18, 127, 67], [83, 4, 127, 67], [83, 11, 127, 69], [83, 12, 127, 70], [83, 15, 127, 73], [83, 19, 127, 77], [84, 4, 129, 2], [84, 8, 129, 6, "isInspecting"], [84, 20, 129, 18], [84, 22, 129, 20], [85, 6, 130, 4], [85, 10, 130, 10, "events"], [85, 16, 130, 16], [85, 19, 132, 6, "ReactNativeFeatureFlags"], [85, 51, 132, 29], [85, 52, 132, 30, "shouldEmitW3CPointerEvents"], [85, 78, 132, 56], [85, 79, 132, 57], [85, 80, 132, 58], [85, 83, 133, 10], [86, 8, 134, 12, "onPointerMove"], [86, 21, 134, 25], [87, 8, 135, 12, "onPointerDown"], [87, 21, 135, 25], [87, 23, 135, 27, "onPointerMove"], [87, 36, 135, 40], [88, 8, 136, 12, "onPointerUp"], [88, 19, 136, 23], [88, 21, 136, 25, "stopInspecting"], [89, 6, 137, 10], [89, 7, 137, 11], [89, 10, 138, 10], [90, 8, 139, 12, "onStartShouldSetResponder"], [90, 33, 139, 37], [90, 35, 139, 39, "shouldSetResponder"], [90, 53, 139, 57], [91, 8, 140, 12, "onResponderMove"], [91, 23, 140, 27], [91, 25, 140, 29, "onResponderMove"], [91, 40, 140, 44], [92, 8, 141, 12, "onResponderRelease"], [92, 26, 141, 30], [92, 28, 141, 32, "stopInspecting"], [93, 6, 142, 10], [93, 7, 142, 11], [94, 6, 144, 4], [94, 26, 145, 6], [94, 30, 145, 6, "_jsxDevRuntime"], [94, 44, 145, 6], [94, 45, 145, 6, "jsxDEV"], [94, 51, 145, 6], [94, 53, 145, 7, "_View"], [94, 58, 145, 7], [94, 59, 145, 7, "default"], [94, 66, 145, 11], [95, 8, 146, 8, "nativeID"], [95, 16, 146, 16], [95, 18, 146, 17], [95, 44, 146, 43], [96, 8, 147, 8, "style"], [96, 13, 147, 13], [96, 15, 147, 15, "styles"], [96, 21, 147, 21], [96, 22, 147, 22, "inspector"], [96, 31, 147, 32], [97, 8, 147, 32], [97, 11, 148, 12, "events"], [97, 17, 148, 18], [98, 8, 148, 18, "children"], [98, 16, 148, 18], [98, 18, 149, 9, "highlight"], [99, 6, 149, 18], [100, 8, 149, 18, "fileName"], [100, 16, 149, 18], [100, 18, 149, 18, "_jsxFileName"], [100, 30, 149, 18], [101, 8, 149, 18, "lineNumber"], [101, 18, 149, 18], [102, 8, 149, 18, "columnNumber"], [102, 20, 149, 18], [103, 6, 149, 18], [103, 13, 150, 12], [103, 14, 150, 13], [104, 4, 152, 2], [105, 4, 154, 2], [105, 11, 154, 9, "highlight"], [105, 20, 154, 18], [106, 2, 155, 0], [107, 2, 157, 0], [107, 6, 157, 6, "styles"], [107, 12, 157, 12], [107, 15, 157, 15, "StyleSheet"], [107, 34, 157, 25], [107, 35, 157, 26, "create"], [107, 41, 157, 32], [107, 42, 157, 33], [108, 4, 158, 2, "inspector"], [108, 13, 158, 11], [108, 15, 158, 13], [109, 6, 159, 4, "backgroundColor"], [109, 21, 159, 19], [109, 23, 159, 21], [109, 36, 159, 34], [110, 6, 160, 4, "position"], [110, 14, 160, 12], [110, 16, 160, 14], [110, 26, 160, 24], [111, 6, 161, 4, "left"], [111, 10, 161, 8], [111, 12, 161, 10], [111, 13, 161, 11], [112, 6, 162, 4, "top"], [112, 9, 162, 7], [112, 11, 162, 9], [112, 12, 162, 10], [113, 6, 163, 4, "right"], [113, 11, 163, 9], [113, 13, 163, 11], [113, 14, 163, 12], [114, 6, 164, 4, "bottom"], [114, 12, 164, 10], [114, 14, 164, 12], [115, 4, 165, 2], [116, 2, 166, 0], [116, 3, 166, 1], [116, 4, 166, 2], [117, 0, 166, 3], [117, 3]], "functionMap": {"names": ["<global>", "ReactDevToolsOverlay", "useEffect$argument_0", "cleanup", "onStartInspectingNative", "onStopInspectingNative", "findViewForLocation", "getInspectorDataForViewAtPoint$argument_3", "stopInspecting", "onPointerMove", "onResponderMove", "shouldSetResponder"], "mappings": "AAA;eCgC;YCO;ICC;KDU;IEE;KFE;IGE;KHE;GDa;IKG;QCK;SDU;KLE;qCOI;GPI;IQG;KRE;ISK;KTK;IUK;KVG;CDgC"}}, "type": "js/module"}]}