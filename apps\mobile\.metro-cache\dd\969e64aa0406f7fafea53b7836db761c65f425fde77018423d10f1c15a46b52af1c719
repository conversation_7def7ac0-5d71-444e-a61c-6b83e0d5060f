{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "../../dom/events/Event", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 43}}], "key": "DDe09b6G/xKMPeTxmtlFrrA0ORw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _Event2 = _interopRequireDefault(require(_dependencyMap[6], \"../../dom/events/Event\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var CloseEvent = exports.default = /*#__PURE__*/function (_Event) {\n    function CloseEvent(type, options) {\n      var _this;\n      (0, _classCallCheck2.default)(this, CloseEvent);\n      _this = _callSuper(this, CloseEvent, [type, options]);\n      _this._wasClean = Boolean(options?.wasClean);\n      _this._code = Number(options?.code) || 0;\n      _this._reason = options?.reason != null ? String(options.reason) : '';\n      return _this;\n    }\n    (0, _inherits2.default)(CloseEvent, _Event);\n    return (0, _createClass2.default)(CloseEvent, [{\n      key: \"wasClean\",\n      get: function () {\n        return this._wasClean;\n      }\n    }, {\n      key: \"code\",\n      get: function () {\n        return this._code;\n      }\n    }, {\n      key: \"reason\",\n      get: function () {\n        return this._reason;\n      }\n    }]);\n  }(_Event2.default);\n});", "lineCount": 43, "map": [[12, 2, 20, 0], [12, 6, 20, 0, "_Event2"], [12, 13, 20, 0], [12, 16, 20, 0, "_interopRequireDefault"], [12, 38, 20, 0], [12, 39, 20, 0, "require"], [12, 46, 20, 0], [12, 47, 20, 0, "_dependencyMap"], [12, 61, 20, 0], [13, 2, 20, 43], [13, 11, 20, 43, "_callSuper"], [13, 22, 20, 43, "t"], [13, 23, 20, 43], [13, 25, 20, 43, "o"], [13, 26, 20, 43], [13, 28, 20, 43, "e"], [13, 29, 20, 43], [13, 40, 20, 43, "o"], [13, 41, 20, 43], [13, 48, 20, 43, "_getPrototypeOf2"], [13, 64, 20, 43], [13, 65, 20, 43, "default"], [13, 72, 20, 43], [13, 74, 20, 43, "o"], [13, 75, 20, 43], [13, 82, 20, 43, "_possibleConstructorReturn2"], [13, 109, 20, 43], [13, 110, 20, 43, "default"], [13, 117, 20, 43], [13, 119, 20, 43, "t"], [13, 120, 20, 43], [13, 122, 20, 43, "_isNativeReflectConstruct"], [13, 147, 20, 43], [13, 152, 20, 43, "Reflect"], [13, 159, 20, 43], [13, 160, 20, 43, "construct"], [13, 169, 20, 43], [13, 170, 20, 43, "o"], [13, 171, 20, 43], [13, 173, 20, 43, "e"], [13, 174, 20, 43], [13, 186, 20, 43, "_getPrototypeOf2"], [13, 202, 20, 43], [13, 203, 20, 43, "default"], [13, 210, 20, 43], [13, 212, 20, 43, "t"], [13, 213, 20, 43], [13, 215, 20, 43, "constructor"], [13, 226, 20, 43], [13, 230, 20, 43, "o"], [13, 231, 20, 43], [13, 232, 20, 43, "apply"], [13, 237, 20, 43], [13, 238, 20, 43, "t"], [13, 239, 20, 43], [13, 241, 20, 43, "e"], [13, 242, 20, 43], [14, 2, 20, 43], [14, 11, 20, 43, "_isNativeReflectConstruct"], [14, 37, 20, 43], [14, 51, 20, 43, "t"], [14, 52, 20, 43], [14, 56, 20, 43, "Boolean"], [14, 63, 20, 43], [14, 64, 20, 43, "prototype"], [14, 73, 20, 43], [14, 74, 20, 43, "valueOf"], [14, 81, 20, 43], [14, 82, 20, 43, "call"], [14, 86, 20, 43], [14, 87, 20, 43, "Reflect"], [14, 94, 20, 43], [14, 95, 20, 43, "construct"], [14, 104, 20, 43], [14, 105, 20, 43, "Boolean"], [14, 112, 20, 43], [14, 145, 20, 43, "t"], [14, 146, 20, 43], [14, 159, 20, 43, "_isNativeReflectConstruct"], [14, 184, 20, 43], [14, 196, 20, 43, "_isNativeReflectConstruct"], [14, 197, 20, 43], [14, 210, 20, 43, "t"], [14, 211, 20, 43], [15, 2, 20, 43], [15, 6, 28, 21, "CloseEvent"], [15, 16, 28, 31], [15, 19, 28, 31, "exports"], [15, 26, 28, 31], [15, 27, 28, 31, "default"], [15, 34, 28, 31], [15, 60, 28, 31, "_Event"], [15, 66, 28, 31], [16, 4, 33, 2], [16, 13, 33, 2, "CloseEvent"], [16, 24, 33, 14, "type"], [16, 28, 33, 26], [16, 30, 33, 28, "options"], [16, 37, 33, 53], [16, 39, 33, 55], [17, 6, 33, 55], [17, 10, 33, 55, "_this"], [17, 15, 33, 55], [18, 6, 33, 55], [18, 10, 33, 55, "_classCallCheck2"], [18, 26, 33, 55], [18, 27, 33, 55, "default"], [18, 34, 33, 55], [18, 42, 33, 55, "CloseEvent"], [18, 52, 33, 55], [19, 6, 34, 4, "_this"], [19, 11, 34, 4], [19, 14, 34, 4, "_callSuper"], [19, 24, 34, 4], [19, 31, 34, 4, "CloseEvent"], [19, 41, 34, 4], [19, 44, 34, 10, "type"], [19, 48, 34, 14], [19, 50, 34, 16, "options"], [19, 57, 34, 23], [20, 6, 36, 4, "_this"], [20, 11, 36, 4], [20, 12, 36, 9, "_was<PERSON><PERSON>"], [20, 21, 36, 18], [20, 24, 36, 21, "Boolean"], [20, 31, 36, 28], [20, 32, 36, 29, "options"], [20, 39, 36, 36], [20, 41, 36, 38, "<PERSON><PERSON><PERSON>"], [20, 49, 36, 46], [20, 50, 36, 47], [21, 6, 37, 4, "_this"], [21, 11, 37, 4], [21, 12, 37, 9, "_code"], [21, 17, 37, 14], [21, 20, 37, 17, "Number"], [21, 26, 37, 23], [21, 27, 37, 24, "options"], [21, 34, 37, 31], [21, 36, 37, 33, "code"], [21, 40, 37, 37], [21, 41, 37, 38], [21, 45, 37, 42], [21, 46, 37, 43], [22, 6, 38, 4, "_this"], [22, 11, 38, 4], [22, 12, 38, 9, "_reason"], [22, 19, 38, 16], [22, 22, 38, 19, "options"], [22, 29, 38, 26], [22, 31, 38, 28, "reason"], [22, 37, 38, 34], [22, 41, 38, 38], [22, 45, 38, 42], [22, 48, 38, 45, "String"], [22, 54, 38, 51], [22, 55, 38, 52, "options"], [22, 62, 38, 59], [22, 63, 38, 60, "reason"], [22, 69, 38, 66], [22, 70, 38, 67], [22, 73, 38, 70], [22, 75, 38, 72], [23, 6, 38, 73], [23, 13, 38, 73, "_this"], [23, 18, 38, 73], [24, 4, 39, 2], [25, 4, 39, 3], [25, 8, 39, 3, "_inherits2"], [25, 18, 39, 3], [25, 19, 39, 3, "default"], [25, 26, 39, 3], [25, 28, 39, 3, "CloseEvent"], [25, 38, 39, 3], [25, 40, 39, 3, "_Event"], [25, 46, 39, 3], [26, 4, 39, 3], [26, 15, 39, 3, "_createClass2"], [26, 28, 39, 3], [26, 29, 39, 3, "default"], [26, 36, 39, 3], [26, 38, 39, 3, "CloseEvent"], [26, 48, 39, 3], [27, 6, 39, 3, "key"], [27, 9, 39, 3], [28, 6, 39, 3, "get"], [28, 9, 39, 3], [28, 11, 41, 2], [28, 20, 41, 2, "get"], [28, 21, 41, 2], [28, 23, 41, 26], [29, 8, 42, 4], [29, 15, 42, 11], [29, 19, 42, 15], [29, 20, 42, 16, "_was<PERSON><PERSON>"], [29, 29, 42, 25], [30, 6, 43, 2], [31, 4, 43, 3], [32, 6, 43, 3, "key"], [32, 9, 43, 3], [33, 6, 43, 3, "get"], [33, 9, 43, 3], [33, 11, 45, 2], [33, 20, 45, 2, "get"], [33, 21, 45, 2], [33, 23, 45, 21], [34, 8, 46, 4], [34, 15, 46, 11], [34, 19, 46, 15], [34, 20, 46, 16, "_code"], [34, 25, 46, 21], [35, 6, 47, 2], [36, 4, 47, 3], [37, 6, 47, 3, "key"], [37, 9, 47, 3], [38, 6, 47, 3, "get"], [38, 9, 47, 3], [38, 11, 49, 2], [38, 20, 49, 2, "get"], [38, 21, 49, 2], [38, 23, 49, 23], [39, 8, 50, 4], [39, 15, 50, 11], [39, 19, 50, 15], [39, 20, 50, 16, "_reason"], [39, 27, 50, 23], [40, 6, 51, 2], [41, 4, 51, 3], [42, 2, 51, 3], [42, 4, 28, 40, "Event"], [42, 19, 28, 45], [43, 0, 28, 45], [43, 3]], "functionMap": {"names": ["<global>", "CloseEvent", "constructor", "get__was<PERSON><PERSON>", "get__code", "get__reason"], "mappings": "AAA;eC2B;ECK;GDM;EEE;GFE;EGE;GHE;EIE;GJE"}}, "type": "js/module"}]}