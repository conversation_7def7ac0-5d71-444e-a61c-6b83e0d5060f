{"dependencies": [{"name": "../../../../Libraries/TurboModule/TurboModuleRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 93}}], "key": "H+9Pk6sLVUPsBv6YXnwcNYMfH5g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var TurboModuleRegistry = _interopRequireWildcard(require(_dependencyMap[0], \"../../../../Libraries/TurboModule/TurboModuleRegistry\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var NativeModule = TurboModuleRegistry.getEnforcing('StatusBarManager');\n  var constants = null;\n  var NativeStatusBarManager = {\n    getConstants() {\n      if (constants == null) {\n        constants = NativeModule.getConstants();\n      }\n      return constants;\n    },\n    getHeight(callback) {\n      NativeModule.getHeight(callback);\n    },\n    setNetworkActivityIndicatorVisible(visible) {\n      NativeModule.setNetworkActivityIndicatorVisible(visible);\n    },\n    addListener(eventType) {\n      NativeModule.addListener(eventType);\n    },\n    removeListeners(count) {\n      NativeModule.removeListeners(count);\n    },\n    setStyle(statusBarStyle, animated) {\n      NativeModule.setStyle(statusBarStyle, animated);\n    },\n    setHidden(hidden, withAnimation) {\n      NativeModule.setHidden(hidden, withAnimation);\n    }\n  };\n  var _default = exports.default = NativeStatusBarManager;\n});", "lineCount": 37, "map": [[6, 2, 13, 0], [6, 6, 13, 0, "TurboModuleRegistry"], [6, 25, 13, 0], [6, 28, 13, 0, "_interopRequireWildcard"], [6, 51, 13, 0], [6, 52, 13, 0, "require"], [6, 59, 13, 0], [6, 60, 13, 0, "_dependencyMap"], [6, 74, 13, 0], [7, 2, 13, 93], [7, 11, 13, 93, "_interopRequireWildcard"], [7, 35, 13, 93, "e"], [7, 36, 13, 93], [7, 38, 13, 93, "t"], [7, 39, 13, 93], [7, 68, 13, 93, "WeakMap"], [7, 75, 13, 93], [7, 81, 13, 93, "r"], [7, 82, 13, 93], [7, 89, 13, 93, "WeakMap"], [7, 96, 13, 93], [7, 100, 13, 93, "n"], [7, 101, 13, 93], [7, 108, 13, 93, "WeakMap"], [7, 115, 13, 93], [7, 127, 13, 93, "_interopRequireWildcard"], [7, 150, 13, 93], [7, 162, 13, 93, "_interopRequireWildcard"], [7, 163, 13, 93, "e"], [7, 164, 13, 93], [7, 166, 13, 93, "t"], [7, 167, 13, 93], [7, 176, 13, 93, "t"], [7, 177, 13, 93], [7, 181, 13, 93, "e"], [7, 182, 13, 93], [7, 186, 13, 93, "e"], [7, 187, 13, 93], [7, 188, 13, 93, "__esModule"], [7, 198, 13, 93], [7, 207, 13, 93, "e"], [7, 208, 13, 93], [7, 214, 13, 93, "o"], [7, 215, 13, 93], [7, 217, 13, 93, "i"], [7, 218, 13, 93], [7, 220, 13, 93, "f"], [7, 221, 13, 93], [7, 226, 13, 93, "__proto__"], [7, 235, 13, 93], [7, 243, 13, 93, "default"], [7, 250, 13, 93], [7, 252, 13, 93, "e"], [7, 253, 13, 93], [7, 270, 13, 93, "e"], [7, 271, 13, 93], [7, 294, 13, 93, "e"], [7, 295, 13, 93], [7, 320, 13, 93, "e"], [7, 321, 13, 93], [7, 330, 13, 93, "f"], [7, 331, 13, 93], [7, 337, 13, 93, "o"], [7, 338, 13, 93], [7, 341, 13, 93, "t"], [7, 342, 13, 93], [7, 345, 13, 93, "n"], [7, 346, 13, 93], [7, 349, 13, 93, "r"], [7, 350, 13, 93], [7, 358, 13, 93, "o"], [7, 359, 13, 93], [7, 360, 13, 93, "has"], [7, 363, 13, 93], [7, 364, 13, 93, "e"], [7, 365, 13, 93], [7, 375, 13, 93, "o"], [7, 376, 13, 93], [7, 377, 13, 93, "get"], [7, 380, 13, 93], [7, 381, 13, 93, "e"], [7, 382, 13, 93], [7, 385, 13, 93, "o"], [7, 386, 13, 93], [7, 387, 13, 93, "set"], [7, 390, 13, 93], [7, 391, 13, 93, "e"], [7, 392, 13, 93], [7, 394, 13, 93, "f"], [7, 395, 13, 93], [7, 409, 13, 93, "_t"], [7, 411, 13, 93], [7, 415, 13, 93, "e"], [7, 416, 13, 93], [7, 432, 13, 93, "_t"], [7, 434, 13, 93], [7, 441, 13, 93, "hasOwnProperty"], [7, 455, 13, 93], [7, 456, 13, 93, "call"], [7, 460, 13, 93], [7, 461, 13, 93, "e"], [7, 462, 13, 93], [7, 464, 13, 93, "_t"], [7, 466, 13, 93], [7, 473, 13, 93, "i"], [7, 474, 13, 93], [7, 478, 13, 93, "o"], [7, 479, 13, 93], [7, 482, 13, 93, "Object"], [7, 488, 13, 93], [7, 489, 13, 93, "defineProperty"], [7, 503, 13, 93], [7, 508, 13, 93, "Object"], [7, 514, 13, 93], [7, 515, 13, 93, "getOwnPropertyDescriptor"], [7, 539, 13, 93], [7, 540, 13, 93, "e"], [7, 541, 13, 93], [7, 543, 13, 93, "_t"], [7, 545, 13, 93], [7, 552, 13, 93, "i"], [7, 553, 13, 93], [7, 554, 13, 93, "get"], [7, 557, 13, 93], [7, 561, 13, 93, "i"], [7, 562, 13, 93], [7, 563, 13, 93, "set"], [7, 566, 13, 93], [7, 570, 13, 93, "o"], [7, 571, 13, 93], [7, 572, 13, 93, "f"], [7, 573, 13, 93], [7, 575, 13, 93, "_t"], [7, 577, 13, 93], [7, 579, 13, 93, "i"], [7, 580, 13, 93], [7, 584, 13, 93, "f"], [7, 585, 13, 93], [7, 586, 13, 93, "_t"], [7, 588, 13, 93], [7, 592, 13, 93, "e"], [7, 593, 13, 93], [7, 594, 13, 93, "_t"], [7, 596, 13, 93], [7, 607, 13, 93, "f"], [7, 608, 13, 93], [7, 613, 13, 93, "e"], [7, 614, 13, 93], [7, 616, 13, 93, "t"], [7, 617, 13, 93], [8, 2, 40, 0], [8, 6, 40, 6, "NativeModule"], [8, 18, 40, 18], [8, 21, 40, 21, "TurboModuleRegistry"], [8, 40, 40, 40], [8, 41, 40, 41, "getEnforcing"], [8, 53, 40, 53], [8, 54, 40, 60], [8, 72, 40, 78], [8, 73, 40, 79], [9, 2, 41, 0], [9, 6, 41, 4, "constants"], [9, 15, 41, 13], [9, 18, 41, 16], [9, 22, 41, 20], [10, 2, 43, 0], [10, 6, 43, 6, "NativeStatusBarManager"], [10, 28, 43, 28], [10, 31, 43, 31], [11, 4, 44, 2, "getConstants"], [11, 16, 44, 14, "getConstants"], [11, 17, 44, 14], [11, 19, 47, 4], [12, 6, 48, 4], [12, 10, 48, 8, "constants"], [12, 19, 48, 17], [12, 23, 48, 21], [12, 27, 48, 25], [12, 29, 48, 27], [13, 8, 49, 6, "constants"], [13, 17, 49, 15], [13, 20, 49, 18, "NativeModule"], [13, 32, 49, 30], [13, 33, 49, 31, "getConstants"], [13, 45, 49, 43], [13, 46, 49, 44], [13, 47, 49, 45], [14, 6, 50, 4], [15, 6, 51, 4], [15, 13, 51, 11, "constants"], [15, 22, 51, 20], [16, 4, 52, 2], [16, 5, 52, 3], [17, 4, 55, 2, "getHeight"], [17, 13, 55, 11, "getHeight"], [17, 14, 55, 12, "callback"], [17, 22, 55, 56], [17, 24, 55, 64], [18, 6, 56, 4, "NativeModule"], [18, 18, 56, 16], [18, 19, 56, 17, "getHeight"], [18, 28, 56, 26], [18, 29, 56, 27, "callback"], [18, 37, 56, 35], [18, 38, 56, 36], [19, 4, 57, 2], [19, 5, 57, 3], [20, 4, 59, 2, "setNetworkActivityIndicatorVisible"], [20, 38, 59, 36, "setNetworkActivityIndicatorVisible"], [20, 39, 59, 37, "visible"], [20, 46, 59, 53], [20, 48, 59, 61], [21, 6, 60, 4, "NativeModule"], [21, 18, 60, 16], [21, 19, 60, 17, "setNetworkActivityIndicatorVisible"], [21, 53, 60, 51], [21, 54, 60, 52, "visible"], [21, 61, 60, 59], [21, 62, 60, 60], [22, 4, 61, 2], [22, 5, 61, 3], [23, 4, 63, 2, "addListener"], [23, 15, 63, 13, "addListener"], [23, 16, 63, 14, "eventType"], [23, 25, 63, 31], [23, 27, 63, 39], [24, 6, 64, 4, "NativeModule"], [24, 18, 64, 16], [24, 19, 64, 17, "addListener"], [24, 30, 64, 28], [24, 31, 64, 29, "eventType"], [24, 40, 64, 38], [24, 41, 64, 39], [25, 4, 65, 2], [25, 5, 65, 3], [26, 4, 67, 2, "removeListeners"], [26, 19, 67, 17, "removeListeners"], [26, 20, 67, 18, "count"], [26, 25, 67, 31], [26, 27, 67, 39], [27, 6, 68, 4, "NativeModule"], [27, 18, 68, 16], [27, 19, 68, 17, "removeListeners"], [27, 34, 68, 32], [27, 35, 68, 33, "count"], [27, 40, 68, 38], [27, 41, 68, 39], [28, 4, 69, 2], [28, 5, 69, 3], [29, 4, 77, 2, "setStyle"], [29, 12, 77, 10, "setStyle"], [29, 13, 77, 11, "statusBarStyle"], [29, 27, 77, 35], [29, 29, 77, 37, "animated"], [29, 37, 77, 54], [29, 39, 77, 62], [30, 6, 78, 4, "NativeModule"], [30, 18, 78, 16], [30, 19, 78, 17, "setStyle"], [30, 27, 78, 25], [30, 28, 78, 26, "statusBarStyle"], [30, 42, 78, 40], [30, 44, 78, 42, "animated"], [30, 52, 78, 50], [30, 53, 78, 51], [31, 4, 79, 2], [31, 5, 79, 3], [32, 4, 84, 2, "setHidden"], [32, 13, 84, 11, "setHidden"], [32, 14, 84, 12, "hidden"], [32, 20, 84, 27], [32, 22, 84, 29, "withAnimation"], [32, 35, 84, 50], [32, 37, 84, 58], [33, 6, 85, 4, "NativeModule"], [33, 18, 85, 16], [33, 19, 85, 17, "setHidden"], [33, 28, 85, 26], [33, 29, 85, 27, "hidden"], [33, 35, 85, 33], [33, 37, 85, 35, "withAnimation"], [33, 50, 85, 48], [33, 51, 85, 49], [34, 4, 86, 2], [35, 2, 87, 0], [35, 3, 87, 1], [36, 2, 87, 2], [36, 6, 87, 2, "_default"], [36, 14, 87, 2], [36, 17, 87, 2, "exports"], [36, 24, 87, 2], [36, 25, 87, 2, "default"], [36, 32, 87, 2], [36, 35, 89, 15, "NativeStatusBarManager"], [36, 57, 89, 37], [37, 0, 89, 37], [37, 3]], "functionMap": {"names": ["<global>", "NativeStatusBarManager.getConstants", "NativeStatusBarManager.getHeight", "NativeStatusBarManager.setNetworkActivityIndicatorVisible", "NativeStatusBarManager.addListener", "NativeStatusBarManager.removeListeners", "NativeStatusBarManager.setStyle", "NativeStatusBarManager.setHidden"], "mappings": "AAA;EC2C;GDQ;EEG;GFE;EGE;GHE;EIE;GJE;EKE;GLE;EMQ;GNE;EOK;GPE"}}, "type": "js/module"}]}