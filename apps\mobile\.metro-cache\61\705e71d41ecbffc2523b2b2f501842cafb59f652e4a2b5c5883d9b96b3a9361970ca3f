{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 48, "index": 95}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.EnsureSingleNavigator = EnsureSingleNavigator;\n  exports.SingleNavigatorContext = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[1], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var MULTIPLE_NAVIGATOR_ERROR = `Another navigator is already registered for this container. You likely have multiple navigators under a single \"NavigationContainer\" or \"Screen\". Make sure each navigator is under a separate \"Screen\" container. See https://reactnavigation.org/docs/nesting-navigators for a guide on nesting.`;\n  var SingleNavigatorContext = exports.SingleNavigatorContext = /*#__PURE__*/React.createContext(undefined);\n\n  /**\n   * Component which ensures that there's only one navigator nested under it.\n   */\n  function EnsureSingleNavigator(_ref) {\n    var children = _ref.children;\n    var navigatorKeyRef = React.useRef(undefined);\n    var value = React.useMemo(() => ({\n      register(key) {\n        var currentKey = navigatorKeyRef.current;\n        if (currentKey !== undefined && key !== currentKey) {\n          throw new Error(MULTIPLE_NAVIGATOR_ERROR);\n        }\n        navigatorKeyRef.current = key;\n      },\n      unregister(key) {\n        var currentKey = navigatorKeyRef.current;\n        if (key !== currentKey) {\n          return;\n        }\n        navigatorKeyRef.current = undefined;\n      }\n    }), []);\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(SingleNavigatorContext.Provider, {\n      value: value,\n      children: children\n    });\n  }\n});", "lineCount": 42, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "EnsureSingleNavigator"], [7, 31, 1, 13], [7, 34, 1, 13, "EnsureSingleNavigator"], [7, 55, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "SingleNavigatorContext"], [8, 32, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "React"], [9, 11, 3, 0], [9, 14, 3, 0, "_interopRequireWildcard"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_jsxRuntime"], [10, 17, 4, 0], [10, 20, 4, 0, "require"], [10, 27, 4, 0], [10, 28, 4, 0, "_dependencyMap"], [10, 42, 4, 0], [11, 2, 4, 48], [11, 11, 4, 48, "_interopRequireWildcard"], [11, 35, 4, 48, "e"], [11, 36, 4, 48], [11, 38, 4, 48, "t"], [11, 39, 4, 48], [11, 68, 4, 48, "WeakMap"], [11, 75, 4, 48], [11, 81, 4, 48, "r"], [11, 82, 4, 48], [11, 89, 4, 48, "WeakMap"], [11, 96, 4, 48], [11, 100, 4, 48, "n"], [11, 101, 4, 48], [11, 108, 4, 48, "WeakMap"], [11, 115, 4, 48], [11, 127, 4, 48, "_interopRequireWildcard"], [11, 150, 4, 48], [11, 162, 4, 48, "_interopRequireWildcard"], [11, 163, 4, 48, "e"], [11, 164, 4, 48], [11, 166, 4, 48, "t"], [11, 167, 4, 48], [11, 176, 4, 48, "t"], [11, 177, 4, 48], [11, 181, 4, 48, "e"], [11, 182, 4, 48], [11, 186, 4, 48, "e"], [11, 187, 4, 48], [11, 188, 4, 48, "__esModule"], [11, 198, 4, 48], [11, 207, 4, 48, "e"], [11, 208, 4, 48], [11, 214, 4, 48, "o"], [11, 215, 4, 48], [11, 217, 4, 48, "i"], [11, 218, 4, 48], [11, 220, 4, 48, "f"], [11, 221, 4, 48], [11, 226, 4, 48, "__proto__"], [11, 235, 4, 48], [11, 243, 4, 48, "default"], [11, 250, 4, 48], [11, 252, 4, 48, "e"], [11, 253, 4, 48], [11, 270, 4, 48, "e"], [11, 271, 4, 48], [11, 294, 4, 48, "e"], [11, 295, 4, 48], [11, 320, 4, 48, "e"], [11, 321, 4, 48], [11, 330, 4, 48, "f"], [11, 331, 4, 48], [11, 337, 4, 48, "o"], [11, 338, 4, 48], [11, 341, 4, 48, "t"], [11, 342, 4, 48], [11, 345, 4, 48, "n"], [11, 346, 4, 48], [11, 349, 4, 48, "r"], [11, 350, 4, 48], [11, 358, 4, 48, "o"], [11, 359, 4, 48], [11, 360, 4, 48, "has"], [11, 363, 4, 48], [11, 364, 4, 48, "e"], [11, 365, 4, 48], [11, 375, 4, 48, "o"], [11, 376, 4, 48], [11, 377, 4, 48, "get"], [11, 380, 4, 48], [11, 381, 4, 48, "e"], [11, 382, 4, 48], [11, 385, 4, 48, "o"], [11, 386, 4, 48], [11, 387, 4, 48, "set"], [11, 390, 4, 48], [11, 391, 4, 48, "e"], [11, 392, 4, 48], [11, 394, 4, 48, "f"], [11, 395, 4, 48], [11, 409, 4, 48, "_t"], [11, 411, 4, 48], [11, 415, 4, 48, "e"], [11, 416, 4, 48], [11, 432, 4, 48, "_t"], [11, 434, 4, 48], [11, 441, 4, 48, "hasOwnProperty"], [11, 455, 4, 48], [11, 456, 4, 48, "call"], [11, 460, 4, 48], [11, 461, 4, 48, "e"], [11, 462, 4, 48], [11, 464, 4, 48, "_t"], [11, 466, 4, 48], [11, 473, 4, 48, "i"], [11, 474, 4, 48], [11, 478, 4, 48, "o"], [11, 479, 4, 48], [11, 482, 4, 48, "Object"], [11, 488, 4, 48], [11, 489, 4, 48, "defineProperty"], [11, 503, 4, 48], [11, 508, 4, 48, "Object"], [11, 514, 4, 48], [11, 515, 4, 48, "getOwnPropertyDescriptor"], [11, 539, 4, 48], [11, 540, 4, 48, "e"], [11, 541, 4, 48], [11, 543, 4, 48, "_t"], [11, 545, 4, 48], [11, 552, 4, 48, "i"], [11, 553, 4, 48], [11, 554, 4, 48, "get"], [11, 557, 4, 48], [11, 561, 4, 48, "i"], [11, 562, 4, 48], [11, 563, 4, 48, "set"], [11, 566, 4, 48], [11, 570, 4, 48, "o"], [11, 571, 4, 48], [11, 572, 4, 48, "f"], [11, 573, 4, 48], [11, 575, 4, 48, "_t"], [11, 577, 4, 48], [11, 579, 4, 48, "i"], [11, 580, 4, 48], [11, 584, 4, 48, "f"], [11, 585, 4, 48], [11, 586, 4, 48, "_t"], [11, 588, 4, 48], [11, 592, 4, 48, "e"], [11, 593, 4, 48], [11, 594, 4, 48, "_t"], [11, 596, 4, 48], [11, 607, 4, 48, "f"], [11, 608, 4, 48], [11, 613, 4, 48, "e"], [11, 614, 4, 48], [11, 616, 4, 48, "t"], [11, 617, 4, 48], [12, 2, 5, 0], [12, 6, 5, 6, "MULTIPLE_NAVIGATOR_ERROR"], [12, 30, 5, 30], [12, 33, 5, 33], [12, 325, 5, 325], [13, 2, 6, 7], [13, 6, 6, 13, "SingleNavigatorContext"], [13, 28, 6, 35], [13, 31, 6, 35, "exports"], [13, 38, 6, 35], [13, 39, 6, 35, "SingleNavigatorContext"], [13, 61, 6, 35], [13, 64, 6, 38], [13, 77, 6, 51, "React"], [13, 82, 6, 56], [13, 83, 6, 57, "createContext"], [13, 96, 6, 70], [13, 97, 6, 71, "undefined"], [13, 106, 6, 80], [13, 107, 6, 81], [15, 2, 8, 0], [16, 0, 9, 0], [17, 0, 10, 0], [18, 2, 11, 7], [18, 11, 11, 16, "EnsureSingleNavigator"], [18, 32, 11, 37, "EnsureSingleNavigator"], [18, 33, 11, 37, "_ref"], [18, 37, 11, 37], [18, 39, 13, 3], [19, 4, 13, 3], [19, 8, 12, 2, "children"], [19, 16, 12, 10], [19, 19, 12, 10, "_ref"], [19, 23, 12, 10], [19, 24, 12, 2, "children"], [19, 32, 12, 10], [20, 4, 14, 2], [20, 8, 14, 8, "navigator<PERSON><PERSON><PERSON><PERSON>"], [20, 23, 14, 23], [20, 26, 14, 26, "React"], [20, 31, 14, 31], [20, 32, 14, 32, "useRef"], [20, 38, 14, 38], [20, 39, 14, 39, "undefined"], [20, 48, 14, 48], [20, 49, 14, 49], [21, 4, 15, 2], [21, 8, 15, 8, "value"], [21, 13, 15, 13], [21, 16, 15, 16, "React"], [21, 21, 15, 21], [21, 22, 15, 22, "useMemo"], [21, 29, 15, 29], [21, 30, 15, 30], [21, 37, 15, 37], [22, 6, 16, 4, "register"], [22, 14, 16, 12, "register"], [22, 15, 16, 13, "key"], [22, 18, 16, 16], [22, 20, 16, 18], [23, 8, 17, 6], [23, 12, 17, 12, "current<PERSON><PERSON>"], [23, 22, 17, 22], [23, 25, 17, 25, "navigator<PERSON><PERSON><PERSON><PERSON>"], [23, 40, 17, 40], [23, 41, 17, 41, "current"], [23, 48, 17, 48], [24, 8, 18, 6], [24, 12, 18, 10, "current<PERSON><PERSON>"], [24, 22, 18, 20], [24, 27, 18, 25, "undefined"], [24, 36, 18, 34], [24, 40, 18, 38, "key"], [24, 43, 18, 41], [24, 48, 18, 46, "current<PERSON><PERSON>"], [24, 58, 18, 56], [24, 60, 18, 58], [25, 10, 19, 8], [25, 16, 19, 14], [25, 20, 19, 18, "Error"], [25, 25, 19, 23], [25, 26, 19, 24, "MULTIPLE_NAVIGATOR_ERROR"], [25, 50, 19, 48], [25, 51, 19, 49], [26, 8, 20, 6], [27, 8, 21, 6, "navigator<PERSON><PERSON><PERSON><PERSON>"], [27, 23, 21, 21], [27, 24, 21, 22, "current"], [27, 31, 21, 29], [27, 34, 21, 32, "key"], [27, 37, 21, 35], [28, 6, 22, 4], [28, 7, 22, 5], [29, 6, 23, 4, "unregister"], [29, 16, 23, 14, "unregister"], [29, 17, 23, 15, "key"], [29, 20, 23, 18], [29, 22, 23, 20], [30, 8, 24, 6], [30, 12, 24, 12, "current<PERSON><PERSON>"], [30, 22, 24, 22], [30, 25, 24, 25, "navigator<PERSON><PERSON><PERSON><PERSON>"], [30, 40, 24, 40], [30, 41, 24, 41, "current"], [30, 48, 24, 48], [31, 8, 25, 6], [31, 12, 25, 10, "key"], [31, 15, 25, 13], [31, 20, 25, 18, "current<PERSON><PERSON>"], [31, 30, 25, 28], [31, 32, 25, 30], [32, 10, 26, 8], [33, 8, 27, 6], [34, 8, 28, 6, "navigator<PERSON><PERSON><PERSON><PERSON>"], [34, 23, 28, 21], [34, 24, 28, 22, "current"], [34, 31, 28, 29], [34, 34, 28, 32, "undefined"], [34, 43, 28, 41], [35, 6, 29, 4], [36, 4, 30, 2], [36, 5, 30, 3], [36, 6, 30, 4], [36, 8, 30, 6], [36, 10, 30, 8], [36, 11, 30, 9], [37, 4, 31, 2], [37, 11, 31, 9], [37, 24, 31, 22], [37, 28, 31, 22, "_jsx"], [37, 43, 31, 26], [37, 45, 31, 27, "SingleNavigatorContext"], [37, 67, 31, 49], [37, 68, 31, 50, "Provider"], [37, 76, 31, 58], [37, 78, 31, 60], [38, 6, 32, 4, "value"], [38, 11, 32, 9], [38, 13, 32, 11, "value"], [38, 18, 32, 16], [39, 6, 33, 4, "children"], [39, 14, 33, 12], [39, 16, 33, 14, "children"], [40, 4, 34, 2], [40, 5, 34, 3], [40, 6, 34, 4], [41, 2, 35, 0], [42, 0, 35, 1], [42, 3]], "functionMap": {"names": ["<global>", "EnsureSingleNavigator", "React.useMemo$argument_0", "register", "unregister"], "mappings": "AAA;OCU;8BCI;ICC;KDM;IEC;KFM;IDC;CDK"}}, "type": "js/module"}]}