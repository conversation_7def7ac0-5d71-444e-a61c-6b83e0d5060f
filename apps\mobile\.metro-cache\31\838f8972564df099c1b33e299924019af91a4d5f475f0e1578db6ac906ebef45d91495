{"dependencies": [{"name": "react-native/Libraries/Pressability/PressabilityDebug", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 66}, "end": {"line": 2, "column": 94, "index": 160}}], "key": "PRekYgRuMxzpaxVA4mY84O1njiQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"PressabilityDebugView\", {\n    enumerable: true,\n    get: function () {\n      return _PressabilityDebug.PressabilityDebugView;\n    }\n  });\n  var _PressabilityDebug = require(_dependencyMap[0], \"react-native/Libraries/Pressability/PressabilityDebug\");\n});", "lineCount": 12, "map": [[11, 2, 2, 0], [11, 6, 2, 0, "_PressabilityDebug"], [11, 24, 2, 0], [11, 27, 2, 0, "require"], [11, 34, 2, 0], [11, 35, 2, 0, "_dependencyMap"], [11, 49, 2, 0], [12, 0, 2, 94], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}