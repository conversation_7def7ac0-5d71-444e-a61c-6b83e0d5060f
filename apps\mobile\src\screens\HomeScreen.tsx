import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import FooterNavigation from '../components/FooterNavigation';

export default function HomeScreen({ navigation }: any) {
  console.log('HomeScreen rendering...');

  return (
    <View style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Brand color status bar area only */}
      <SafeAreaView style={{ backgroundColor: '#f3a823' }} edges={['top']} />

      {/* Content area with light background */}
      <View style={{ flex: 1, backgroundColor: '#f9fafb' }}>
        {/* Header */}
        <View style={{
          paddingHorizontal: 20,
          paddingTop: 10,
          paddingBottom: 20,
        }}>
          <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#111827' }}>
            Welcome to Tap2Go
          </Text>
          <Text style={{ fontSize: 16, color: '#6b7280', marginTop: 4 }}>
            Your food delivery app
          </Text>
        </View>

        <ScrollView style={{ flex: 1, paddingHorizontal: 20 }}>
          <View style={{
            backgroundColor: 'white',
            borderRadius: 12,
            padding: 20,
            marginBottom: 20,
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 3,
            elevation: 2,
          }}>
            <Text style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: '#111827',
              marginBottom: 12,
              textAlign: 'center',
            }}>
              🚀 Tap2Go Mobile App
            </Text>
            <Text style={{
              fontSize: 16,
              color: '#6b7280',
              textAlign: 'center',
              marginBottom: 20,
              lineHeight: 24,
            }}>
              App is loading successfully! Navigate using the footer below.
            </Text>
            <TouchableOpacity
              style={{
                backgroundColor: '#f3a823',
                paddingHorizontal: 24,
                paddingVertical: 12,
                borderRadius: 8,
              }}
              onPress={() => {
                console.log('Test button pressed!');
                navigation.navigate('Cart');
              }}
            >
              <Text style={{
                color: 'white',
                fontSize: 16,
                fontWeight: '600',
              }}>
                Go to Cart
              </Text>
            </TouchableOpacity>
          </View>

          <View style={{
            backgroundColor: 'white',
            borderRadius: 12,
            padding: 20,
            marginBottom: 20,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 3,
            elevation: 2,
          }}>
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#111827',
              marginBottom: 12,
            }}>
              Features Available:
            </Text>
            <Text style={{ fontSize: 16, color: '#6b7280', marginBottom: 8, lineHeight: 24 }}>
              • Browse restaurants and menus
            </Text>
            <Text style={{ fontSize: 16, color: '#6b7280', marginBottom: 8, lineHeight: 24 }}>
              • Add items to cart
            </Text>
            <Text style={{ fontSize: 16, color: '#6b7280', marginBottom: 8, lineHeight: 24 }}>
              • Track your orders
            </Text>
            <Text style={{ fontSize: 16, color: '#6b7280', marginBottom: 8, lineHeight: 24 }}>
              • Search for food
            </Text>
            <Text style={{ fontSize: 16, color: '#6b7280', marginBottom: 8, lineHeight: 24 }}>
              • Manage your account
            </Text>
          </View>

          <View style={{ height: 100 }} />
        </ScrollView>
      </View>

      {/* Footer Navigation - positioned above bottom safe area */}
      <FooterNavigation navigation={navigation} activeScreen="Home" />

      {/* Bottom safe area with light background */}
      <SafeAreaView style={{ backgroundColor: '#f9fafb' }} edges={['bottom']} />
    </View>
  );
}


