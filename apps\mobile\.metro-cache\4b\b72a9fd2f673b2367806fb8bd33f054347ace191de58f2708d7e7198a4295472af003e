{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "4wo4OYT4MSo2InL8kiWmZxvepwE=", "exportNames": ["*"]}}, {"name": "../../../getShadowNodeFromRef", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 55}, "end": {"line": 2, "column": 69, "index": 124}}], "key": "84Qu5UH2d/L4S1+Pr1wW2nxbCNI=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 174}, "end": {"line": 5, "column": 43, "index": 217}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../../findNodeHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 218}, "end": {"line": 6, "column": 53, "index": 271}}], "key": "k+xfarWxri7fB3IShKFMK0oi5UQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useViewRefHandler = useViewRefHandler;\n  var _utils = require(_dependencyMap[1], \"../../../utils\");\n  var _getShadowNodeFromRef = require(_dependencyMap[2], \"../../../getShadowNodeFromRef\");\n  var _react = require(_dependencyMap[3], \"react\");\n  var _findNodeHandle = _interopRequireDefault(require(_dependencyMap[4], \"../../../findNodeHandle\"));\n  // Ref handler for the Wrap component attached under the GestureDetector.\n  // It's responsible for setting the viewRef on the state and triggering the reattaching of handlers\n  // if the view has changed.\n  function useViewRefHandler(state, updateAttachedGestures) {\n    var refHandler = (0, _react.useCallback)(ref => {\n      if (ref === null) {\n        return;\n      }\n      state.viewRef = ref;\n\n      // if it's the first render, also set the previousViewTag to prevent reattaching gestures when not needed\n      if (state.previousViewTag === -1) {\n        state.previousViewTag = (0, _findNodeHandle.default)(state.viewRef);\n      }\n\n      // Pass true as `skipConfigUpdate`. Here we only want to trigger the eventual reattaching of handlers\n      // in case the view has changed. If the view doesn't change, the update will be handled by detector.\n      if (!state.firstRender) {\n        updateAttachedGestures(true);\n      }\n      if (__DEV__ && (0, _utils.isFabric)() && global.isViewFlatteningDisabled) {\n        var node = (0, _getShadowNodeFromRef.getShadowNodeFromRef)(ref);\n        if (global.isViewFlatteningDisabled(node) === false) {\n          console.error((0, _utils.tagMessage)('GestureDetector has received a child that may get view-flattened. ' + '\\nTo prevent it from misbehaving you need to wrap the child with a `<View collapsable={false}>`.'));\n        }\n      }\n    }, [state, updateAttachedGestures]);\n    return refHandler;\n  }\n});", "lineCount": 40, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_utils"], [7, 12, 1, 0], [7, 15, 1, 0, "require"], [7, 22, 1, 0], [7, 23, 1, 0, "_dependencyMap"], [7, 37, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_getShadowNodeFromRef"], [8, 27, 2, 0], [8, 30, 2, 0, "require"], [8, 37, 2, 0], [8, 38, 2, 0, "_dependencyMap"], [8, 52, 2, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_react"], [9, 12, 5, 0], [9, 15, 5, 0, "require"], [9, 22, 5, 0], [9, 23, 5, 0, "_dependencyMap"], [9, 37, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_findNodeHandle"], [10, 21, 6, 0], [10, 24, 6, 0, "_interopRequireDefault"], [10, 46, 6, 0], [10, 47, 6, 0, "require"], [10, 54, 6, 0], [10, 55, 6, 0, "_dependencyMap"], [10, 69, 6, 0], [11, 2, 12, 0], [12, 2, 13, 0], [13, 2, 14, 0], [14, 2, 15, 7], [14, 11, 15, 16, "useViewRefHandler"], [14, 28, 15, 33, "useViewRefHandler"], [14, 29, 16, 2, "state"], [14, 34, 16, 29], [14, 36, 17, 2, "updateAttachedGestures"], [14, 58, 17, 62], [14, 60, 18, 2], [15, 4, 19, 2], [15, 8, 19, 8, "ref<PERSON><PERSON><PERSON>"], [15, 18, 19, 18], [15, 21, 19, 21], [15, 25, 19, 21, "useCallback"], [15, 43, 19, 32], [15, 45, 20, 5, "ref"], [15, 48, 20, 32], [15, 52, 20, 37], [16, 6, 21, 6], [16, 10, 21, 10, "ref"], [16, 13, 21, 13], [16, 18, 21, 18], [16, 22, 21, 22], [16, 24, 21, 24], [17, 8, 22, 8], [18, 6, 23, 6], [19, 6, 25, 6, "state"], [19, 11, 25, 11], [19, 12, 25, 12, "viewRef"], [19, 19, 25, 19], [19, 22, 25, 22, "ref"], [19, 25, 25, 25], [21, 6, 27, 6], [22, 6, 28, 6], [22, 10, 28, 10, "state"], [22, 15, 28, 15], [22, 16, 28, 16, "previousViewTag"], [22, 31, 28, 31], [22, 36, 28, 36], [22, 37, 28, 37], [22, 38, 28, 38], [22, 40, 28, 40], [23, 8, 29, 8, "state"], [23, 13, 29, 13], [23, 14, 29, 14, "previousViewTag"], [23, 29, 29, 29], [23, 32, 29, 32], [23, 36, 29, 32, "findNodeHandle"], [23, 59, 29, 46], [23, 61, 29, 47, "state"], [23, 66, 29, 52], [23, 67, 29, 53, "viewRef"], [23, 74, 29, 60], [23, 75, 29, 71], [24, 6, 30, 6], [26, 6, 32, 6], [27, 6, 33, 6], [28, 6, 34, 6], [28, 10, 34, 10], [28, 11, 34, 11, "state"], [28, 16, 34, 16], [28, 17, 34, 17, "firstRender"], [28, 28, 34, 28], [28, 30, 34, 30], [29, 8, 35, 8, "updateAttachedGestures"], [29, 30, 35, 30], [29, 31, 35, 31], [29, 35, 35, 35], [29, 36, 35, 36], [30, 6, 36, 6], [31, 6, 38, 6], [31, 10, 38, 10, "__DEV__"], [31, 17, 38, 17], [31, 21, 38, 21], [31, 25, 38, 21, "isF<PERSON><PERSON>"], [31, 40, 38, 29], [31, 42, 38, 30], [31, 43, 38, 31], [31, 47, 38, 35, "global"], [31, 53, 38, 41], [31, 54, 38, 42, "isViewFlatteningDisabled"], [31, 78, 38, 66], [31, 80, 38, 68], [32, 8, 39, 8], [32, 12, 39, 14, "node"], [32, 16, 39, 18], [32, 19, 39, 21], [32, 23, 39, 21, "getShadowNodeFromRef"], [32, 65, 39, 41], [32, 67, 39, 42, "ref"], [32, 70, 39, 45], [32, 71, 39, 46], [33, 8, 40, 8], [33, 12, 40, 12, "global"], [33, 18, 40, 18], [33, 19, 40, 19, "isViewFlatteningDisabled"], [33, 43, 40, 43], [33, 44, 40, 44, "node"], [33, 48, 40, 48], [33, 49, 40, 49], [33, 54, 40, 54], [33, 59, 40, 59], [33, 61, 40, 61], [34, 10, 41, 10, "console"], [34, 17, 41, 17], [34, 18, 41, 18, "error"], [34, 23, 41, 23], [34, 24, 42, 12], [34, 28, 42, 12, "tagMessage"], [34, 45, 42, 22], [34, 47, 43, 14], [34, 115, 43, 82], [34, 118, 44, 16], [34, 216, 45, 12], [34, 217, 46, 10], [34, 218, 46, 11], [35, 8, 47, 8], [36, 6, 48, 6], [37, 4, 49, 4], [37, 5, 49, 5], [37, 7, 50, 4], [37, 8, 50, 5, "state"], [37, 13, 50, 10], [37, 15, 50, 12, "updateAttachedGestures"], [37, 37, 50, 34], [37, 38, 51, 2], [37, 39, 51, 3], [38, 4, 53, 2], [38, 11, 53, 9, "ref<PERSON><PERSON><PERSON>"], [38, 21, 53, 19], [39, 2, 54, 0], [40, 0, 54, 1], [40, 3]], "functionMap": {"names": ["<global>", "useViewRefHandler", "ref<PERSON><PERSON><PERSON>"], "mappings": "AAA;OCc;ICK;KD6B;CDK"}}, "type": "js/module"}]}