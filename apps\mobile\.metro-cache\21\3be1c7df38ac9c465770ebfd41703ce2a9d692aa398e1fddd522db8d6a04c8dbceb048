{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "../../commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 180}, "end": {"line": 11, "column": 47, "index": 227}}], "key": "vhHMm+PKBSj2e9y550uvksCLTMU=", "exportNames": ["*"]}}, {"name": "../../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 228}, "end": {"line": 12, "column": 47, "index": 275}}], "key": "eT202ujluoOcHDbauyWnF/muvbc=", "exportNames": ["*"]}}, {"name": "../../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 276}, "end": {"line": 13, "column": 38, "index": 314}}], "key": "BAUnomHCaPEo8SwbXzlKtt9pd/8=", "exportNames": ["*"]}}, {"name": "../../mockedRequestAnimationFrame", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 315}, "end": {"line": 14, "column": 80, "index": 395}}], "key": "weVrOD1kUADg6lWV28y9xyA+EOs=", "exportNames": ["*"]}}, {"name": "../../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 396}, "end": {"line": 20, "column": 31, "index": 496}}], "key": "pPfOdxbh9mtPdO2EBvl67ARfj+c=", "exportNames": ["*"]}}, {"name": "../../worklets", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 551}, "end": {"line": 22, "column": 48, "index": 599}}], "key": "HXZOmk1Cxgt7TCWomkXO32a100I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Platform = void 0;\n  exports.createJSReanimatedModule = createJSReanimatedModule;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _commonTypes = require(_dependencyMap[5], \"../../commonTypes\");\n  var _errors = require(_dependencyMap[6], \"../../errors\");\n  var _logger = require(_dependencyMap[7], \"../../logger\");\n  var _mockedRequestAnimationFrame = require(_dependencyMap[8], \"../../mockedRequestAnimationFrame\");\n  var _PlatformChecker = require(_dependencyMap[9], \"../../PlatformChecker\");\n  var _worklets = require(_dependencyMap[10], \"../../worklets\");\n  function createJSReanimatedModule() {\n    return new JSReanimated();\n  }\n\n  // In Node.js environments (like when static rendering with Expo Router)\n  // requestAnimationFrame is unavailable, so we use our mock.\n  // It also has to be mocked for Jest purposes (see `initializeUIRuntime`).\n  var requestAnimationFrameImpl = (0, _PlatformChecker.isJest)() || !globalThis.requestAnimationFrame ? _mockedRequestAnimationFrame.mockedRequestAnimationFrame : globalThis.requestAnimationFrame;\n  var _workletsModule = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"workletsModule\");\n  var JSReanimated = /*#__PURE__*/function () {\n    function JSReanimated() {\n      (0, _classCallCheck2.default)(this, JSReanimated);\n      /**\n       * We keep the instance of `WorkletsModule` here to keep correct coupling of\n       * the modules and initialization order.\n       */\n      Object.defineProperty(this, _workletsModule, {\n        writable: true,\n        value: _worklets.WorkletsModule\n      });\n      this.nextSensorId = 0;\n      this.sensors = new Map();\n      this.platform = undefined;\n      this.getSensorCallback = (sensor, sensorType, eventHandler) => {\n        switch (sensorType) {\n          case _commonTypes.SensorType.ACCELEROMETER:\n          case _commonTypes.SensorType.GRAVITY:\n            return () => {\n              var x = sensor.x,\n                y = sensor.y,\n                z = sensor.z;\n\n              // Web Android sensors have a different coordinate system than iOS\n              if (this.platform === Platform.WEB_ANDROID) {\n                var _ref = [-x, -y, -z];\n                x = _ref[0];\n                y = _ref[1];\n                z = _ref[2];\n              }\n              // TODO TYPESCRIPT on web ShareableRef is the value itself so we call it directly\n              eventHandler({\n                x,\n                y,\n                z,\n                interfaceOrientation: 0\n              });\n            };\n          case _commonTypes.SensorType.GYROSCOPE:\n          case _commonTypes.SensorType.MAGNETIC_FIELD:\n            return () => {\n              var x = sensor.x,\n                y = sensor.y,\n                z = sensor.z;\n              // TODO TYPESCRIPT on web ShareableRef is the value itself so we call it directly\n              eventHandler({\n                x,\n                y,\n                z,\n                interfaceOrientation: 0\n              });\n            };\n          case _commonTypes.SensorType.ROTATION:\n            return () => {\n              var _sensor$quaternion = (0, _slicedToArray2.default)(sensor.quaternion, 4),\n                qw = _sensor$quaternion[0],\n                qx = _sensor$quaternion[1],\n                qy = _sensor$quaternion[2],\n                qz = _sensor$quaternion[3];\n\n              // Android sensors have a different coordinate system than iOS\n              if (this.platform === Platform.WEB_ANDROID) {\n                var _ref2 = [qz, -qy];\n                qy = _ref2[0];\n                qz = _ref2[1];\n              }\n\n              // reference: https://stackoverflow.com/questions/5782658/extracting-yaw-from-a-quaternion\n              var yaw = -Math.atan2(2.0 * (qy * qz + qw * qx), qw * qw - qx * qx - qy * qy + qz * qz);\n              var pitch = Math.sin(-2.0 * (qx * qz - qw * qy));\n              var roll = -Math.atan2(2.0 * (qx * qy + qw * qz), qw * qw + qx * qx - qy * qy - qz * qz);\n              // TODO TYPESCRIPT on web ShareableRef is the value itself so we call it directly\n              eventHandler({\n                qw,\n                qx,\n                qy,\n                qz,\n                yaw,\n                pitch,\n                roll,\n                interfaceOrientation: 0\n              });\n            };\n        }\n      };\n    }\n    return (0, _createClass2.default)(JSReanimated, [{\n      key: \"scheduleOnUI\",\n      value: function scheduleOnUI(worklet) {\n        // @ts-ignore web implementation has still not been updated after the rewrite, this will be addressed once the web implementation updates are ready\n        requestAnimationFrameImpl(worklet);\n      }\n    }, {\n      key: \"createWorkletRuntime\",\n      value: function createWorkletRuntime(_name, _initializer) {\n        throw new _errors.ReanimatedError('createWorkletRuntime is not available in JSReanimated.');\n      }\n    }, {\n      key: \"scheduleOnRuntime\",\n      value: function scheduleOnRuntime() {\n        throw new _errors.ReanimatedError('scheduleOnRuntime is not available in JSReanimated.');\n      }\n    }, {\n      key: \"registerEventHandler\",\n      value: function registerEventHandler(_eventHandler, _eventName, _emitterReactTag) {\n        throw new _errors.ReanimatedError('registerEventHandler is not available in JSReanimated.');\n      }\n    }, {\n      key: \"unregisterEventHandler\",\n      value: function unregisterEventHandler(_) {\n        throw new _errors.ReanimatedError('unregisterEventHandler is not available in JSReanimated.');\n      }\n    }, {\n      key: \"enableLayoutAnimations\",\n      value: function enableLayoutAnimations() {\n        if ((0, _PlatformChecker.isWeb)()) {\n          _logger.logger.warn('Layout Animations are not supported on web yet.');\n        } else if ((0, _PlatformChecker.isJest)()) {\n          _logger.logger.warn('Layout Animations are no-ops when using Jest.');\n        } else if ((0, _PlatformChecker.isChromeDebugger)()) {\n          _logger.logger.warn('Layout Animations are no-ops when using Chrome Debugger.');\n        } else {\n          _logger.logger.warn('Layout Animations are not supported on this configuration.');\n        }\n      }\n    }, {\n      key: \"configureLayoutAnimationBatch\",\n      value: function configureLayoutAnimationBatch() {\n        // no-op\n      }\n    }, {\n      key: \"setShouldAnimateExitingForTag\",\n      value: function setShouldAnimateExitingForTag() {\n        // no-op\n      }\n    }, {\n      key: \"registerSensor\",\n      value: function registerSensor(sensorType, interval, _iosReferenceFrame, eventHandler) {\n        if (!(0, _PlatformChecker.isWindowAvailable)()) {\n          // the window object is unavailable when building the server portion of a site that uses SSG\n          // this check is here to ensure that the server build won't fail\n          return -1;\n        }\n        if (this.platform === undefined) {\n          this.detectPlatform();\n        }\n        if (!(this.getSensorName(sensorType) in window)) {\n          // https://w3c.github.io/sensors/#secure-context\n          _logger.logger.warn('Sensor is not available.' + ((0, _PlatformChecker.isWeb)() && location.protocol !== 'https:' ? ' Make sure you use secure origin with `npx expo start --web --https`.' : '') + (this.platform === Platform.WEB_IOS ? ' For iOS web, you will also have to also grant permission in the browser: https://dev.to/li/how-to-requestpermission-for-devicemotion-and-deviceorientation-events-in-ios-13-46g2.' : ''));\n          return -1;\n        }\n        if (this.platform === undefined) {\n          this.detectPlatform();\n        }\n        var sensor = this.initializeSensor(sensorType, interval);\n        sensor.addEventListener('reading', this.getSensorCallback(sensor, sensorType, eventHandler));\n        sensor.start();\n        this.sensors.set(this.nextSensorId, sensor);\n        return this.nextSensorId++;\n      }\n    }, {\n      key: \"unregisterSensor\",\n      value: function unregisterSensor(id) {\n        var sensor = this.sensors.get(id);\n        if (sensor !== undefined) {\n          sensor.stop();\n          this.sensors.delete(id);\n        }\n      }\n    }, {\n      key: \"subscribeForKeyboardEvents\",\n      value: function subscribeForKeyboardEvents(_) {\n        if ((0, _PlatformChecker.isWeb)()) {\n          _logger.logger.warn('useAnimatedKeyboard is not available on web yet.');\n        } else if ((0, _PlatformChecker.isJest)()) {\n          _logger.logger.warn('useAnimatedKeyboard is not available when using Jest.');\n        } else if ((0, _PlatformChecker.isChromeDebugger)()) {\n          _logger.logger.warn('useAnimatedKeyboard is not available when using Chrome Debugger.');\n        } else {\n          _logger.logger.warn('useAnimatedKeyboard is not available on this configuration.');\n        }\n        return -1;\n      }\n    }, {\n      key: \"unsubscribeFromKeyboardEvents\",\n      value: function unsubscribeFromKeyboardEvents(_) {\n        // noop\n      }\n    }, {\n      key: \"initializeSensor\",\n      value: function initializeSensor(sensorType, interval) {\n        var config = interval <= 0 ? {\n          referenceFrame: 'device'\n        } : {\n          frequency: 1000 / interval\n        };\n        switch (sensorType) {\n          case _commonTypes.SensorType.ACCELEROMETER:\n            return new window.Accelerometer(config);\n          case _commonTypes.SensorType.GYROSCOPE:\n            return new window.Gyroscope(config);\n          case _commonTypes.SensorType.GRAVITY:\n            return new window.GravitySensor(config);\n          case _commonTypes.SensorType.MAGNETIC_FIELD:\n            return new window.Magnetometer(config);\n          case _commonTypes.SensorType.ROTATION:\n            return new window.AbsoluteOrientationSensor(config);\n        }\n      }\n    }, {\n      key: \"getSensorName\",\n      value: function getSensorName(sensorType) {\n        switch (sensorType) {\n          case _commonTypes.SensorType.ACCELEROMETER:\n            return 'Accelerometer';\n          case _commonTypes.SensorType.GRAVITY:\n            return 'GravitySensor';\n          case _commonTypes.SensorType.GYROSCOPE:\n            return 'Gyroscope';\n          case _commonTypes.SensorType.MAGNETIC_FIELD:\n            return 'Magnetometer';\n          case _commonTypes.SensorType.ROTATION:\n            return 'AbsoluteOrientationSensor';\n        }\n      }\n    }, {\n      key: \"detectPlatform\",\n      value: function detectPlatform() {\n        var userAgent = navigator.userAgent || navigator.vendor || window.opera;\n        if (userAgent === undefined) {\n          this.platform = Platform.UNKNOWN;\n        } else if (/iPad|iPhone|iPod/.test(userAgent)) {\n          this.platform = Platform.WEB_IOS;\n        } else if (/android/i.test(userAgent)) {\n          this.platform = Platform.WEB_ANDROID;\n        } else {\n          this.platform = Platform.WEB;\n        }\n      }\n    }, {\n      key: \"getViewProp\",\n      value: function getViewProp(_viewTag, _propName, _component, _callback) {\n        throw new _errors.ReanimatedError('getViewProp is not available in JSReanimated.');\n      }\n    }, {\n      key: \"configureProps\",\n      value: function configureProps() {\n        throw new _errors.ReanimatedError('configureProps is not available in JSReanimated.');\n      }\n    }, {\n      key: \"executeOnUIRuntimeSync\",\n      value: function executeOnUIRuntimeSync(_shareable) {\n        throw new _errors.ReanimatedError('`executeOnUIRuntimeSync` is not available in JSReanimated.');\n      }\n    }, {\n      key: \"markNodeAsRemovable\",\n      value: function markNodeAsRemovable(_shadowNodeWrapper) {\n        throw new _errors.ReanimatedError('markNodeAsRemovable is not available in JSReanimated.');\n      }\n    }, {\n      key: \"unmarkNodeAsRemovable\",\n      value: function unmarkNodeAsRemovable(_viewTag) {\n        throw new _errors.ReanimatedError('unmarkNodeAsRemovable is not available in JSReanimated.');\n      }\n    }]);\n  }(); // Lack of this export breaks TypeScript generation since\n  // an enum transpiles into JavaScript code.\n  // ts-prune-ignore-next\n  var Platform = exports.Platform = /*#__PURE__*/function (Platform) {\n    Platform[\"WEB_IOS\"] = \"web iOS\";\n    Platform[\"WEB_ANDROID\"] = \"web Android\";\n    Platform[\"WEB\"] = \"web\";\n    Platform[\"UNKNOWN\"] = \"unknown\";\n    return Platform;\n  }({});\n});", "lineCount": 304, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Platform"], [8, 18, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "createJSReanimatedModule"], [9, 34, 1, 13], [9, 37, 1, 13, "createJSReanimatedModule"], [9, 61, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_slicedToArray2"], [10, 21, 1, 13], [10, 24, 1, 13, "_interopRequireDefault"], [10, 46, 1, 13], [10, 47, 1, 13, "require"], [10, 54, 1, 13], [10, 55, 1, 13, "_dependencyMap"], [10, 69, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_classCallCheck2"], [11, 22, 1, 13], [11, 25, 1, 13, "_interopRequireDefault"], [11, 47, 1, 13], [11, 48, 1, 13, "require"], [11, 55, 1, 13], [11, 56, 1, 13, "_dependencyMap"], [11, 70, 1, 13], [12, 2, 1, 13], [12, 6, 1, 13, "_createClass2"], [12, 19, 1, 13], [12, 22, 1, 13, "_interopRequireDefault"], [12, 44, 1, 13], [12, 45, 1, 13, "require"], [12, 52, 1, 13], [12, 53, 1, 13, "_dependencyMap"], [12, 67, 1, 13], [13, 2, 1, 13], [13, 6, 1, 13, "_classPrivateFieldLooseKey2"], [13, 33, 1, 13], [13, 36, 1, 13, "_interopRequireDefault"], [13, 58, 1, 13], [13, 59, 1, 13, "require"], [13, 66, 1, 13], [13, 67, 1, 13, "_dependencyMap"], [13, 81, 1, 13], [14, 2, 11, 0], [14, 6, 11, 0, "_commonTypes"], [14, 18, 11, 0], [14, 21, 11, 0, "require"], [14, 28, 11, 0], [14, 29, 11, 0, "_dependencyMap"], [14, 43, 11, 0], [15, 2, 12, 0], [15, 6, 12, 0, "_errors"], [15, 13, 12, 0], [15, 16, 12, 0, "require"], [15, 23, 12, 0], [15, 24, 12, 0, "_dependencyMap"], [15, 38, 12, 0], [16, 2, 13, 0], [16, 6, 13, 0, "_logger"], [16, 13, 13, 0], [16, 16, 13, 0, "require"], [16, 23, 13, 0], [16, 24, 13, 0, "_dependencyMap"], [16, 38, 13, 0], [17, 2, 14, 0], [17, 6, 14, 0, "_mockedRequestAnimationFrame"], [17, 34, 14, 0], [17, 37, 14, 0, "require"], [17, 44, 14, 0], [17, 45, 14, 0, "_dependencyMap"], [17, 59, 14, 0], [18, 2, 15, 0], [18, 6, 15, 0, "_PlatformChecker"], [18, 22, 15, 0], [18, 25, 15, 0, "require"], [18, 32, 15, 0], [18, 33, 15, 0, "_dependencyMap"], [18, 47, 15, 0], [19, 2, 22, 0], [19, 6, 22, 0, "_worklets"], [19, 15, 22, 0], [19, 18, 22, 0, "require"], [19, 25, 22, 0], [19, 26, 22, 0, "_dependencyMap"], [19, 40, 22, 0], [20, 2, 25, 7], [20, 11, 25, 16, "createJSReanimatedModule"], [20, 35, 25, 40, "createJSReanimatedModule"], [20, 36, 25, 40], [20, 38, 25, 62], [21, 4, 26, 2], [21, 11, 26, 9], [21, 15, 26, 13, "JSReanimated"], [21, 27, 26, 25], [21, 28, 26, 26], [21, 29, 26, 27], [22, 2, 27, 0], [24, 2, 29, 0], [25, 2, 30, 0], [26, 2, 31, 0], [27, 2, 32, 0], [27, 6, 32, 6, "requestAnimationFrameImpl"], [27, 31, 32, 31], [27, 34, 33, 2], [27, 38, 33, 2, "isJest"], [27, 61, 33, 8], [27, 63, 33, 9], [27, 64, 33, 10], [27, 68, 33, 14], [27, 69, 33, 15, "globalThis"], [27, 79, 33, 25], [27, 80, 33, 26, "requestAnimationFrame"], [27, 101, 33, 47], [27, 104, 34, 6, "mockedRequestAnimationFrame"], [27, 160, 34, 33], [27, 163, 35, 6, "globalThis"], [27, 173, 35, 16], [27, 174, 35, 17, "requestAnimationFrame"], [27, 195, 35, 38], [28, 2, 35, 39], [28, 6, 35, 39, "_workletsModule"], [28, 21, 35, 39], [28, 41, 35, 39, "_classPrivateFieldLooseKey2"], [28, 68, 35, 39], [28, 69, 35, 39, "default"], [28, 76, 35, 39], [29, 2, 35, 39], [29, 6, 37, 6, "JSReanimated"], [29, 18, 37, 18], [30, 4, 37, 18], [30, 13, 37, 18, "JSReanimated"], [30, 26, 37, 18], [31, 6, 37, 18], [31, 10, 37, 18, "_classCallCheck2"], [31, 26, 37, 18], [31, 27, 37, 18, "default"], [31, 34, 37, 18], [31, 42, 37, 18, "JSReanimated"], [31, 54, 37, 18], [32, 6, 38, 2], [33, 0, 39, 0], [34, 0, 40, 0], [35, 0, 41, 0], [36, 6, 38, 2, "Object"], [36, 12, 38, 2], [36, 13, 38, 2, "defineProperty"], [36, 27, 38, 2], [36, 34, 38, 2, "_workletsModule"], [36, 49, 38, 2], [37, 8, 38, 2, "writable"], [37, 16, 38, 2], [38, 8, 38, 2, "value"], [38, 13, 38, 2], [38, 15, 42, 37, "WorkletsModule"], [39, 6, 42, 51], [40, 6, 42, 51], [40, 11, 43, 2, "nextSensorId"], [40, 23, 43, 14], [40, 26, 43, 17], [40, 27, 43, 18], [41, 6, 43, 18], [41, 11, 44, 2, "sensors"], [41, 18, 44, 9], [41, 21, 44, 12], [41, 25, 44, 16, "Map"], [41, 28, 44, 19], [41, 29, 44, 39], [41, 30, 44, 40], [42, 6, 44, 40], [42, 11, 45, 2, "platform"], [42, 19, 45, 10], [42, 22, 45, 24, "undefined"], [42, 31, 45, 33], [43, 6, 45, 33], [43, 11, 148, 2, "getSensorCallback"], [43, 28, 148, 19], [43, 31, 148, 22], [43, 32, 149, 4, "sensor"], [43, 38, 149, 21], [43, 40, 150, 4, "sensorType"], [43, 50, 150, 26], [43, 52, 151, 4, "<PERSON><PERSON><PERSON><PERSON>"], [43, 64, 151, 71], [43, 69, 152, 7], [44, 8, 153, 4], [44, 16, 153, 12, "sensorType"], [44, 26, 153, 22], [45, 10, 154, 6], [45, 15, 154, 11, "SensorType"], [45, 38, 154, 21], [45, 39, 154, 22, "ACCELEROMETER"], [45, 52, 154, 35], [46, 10, 155, 6], [46, 15, 155, 11, "SensorType"], [46, 38, 155, 21], [46, 39, 155, 22, "GRAVITY"], [46, 46, 155, 29], [47, 12, 156, 8], [47, 19, 156, 15], [47, 25, 156, 21], [48, 14, 157, 10], [48, 18, 157, 16, "x"], [48, 19, 157, 17], [48, 22, 157, 28, "sensor"], [48, 28, 157, 34], [48, 29, 157, 16, "x"], [48, 30, 157, 17], [49, 16, 157, 19, "y"], [49, 17, 157, 20], [49, 20, 157, 28, "sensor"], [49, 26, 157, 34], [49, 27, 157, 19, "y"], [49, 28, 157, 20], [50, 16, 157, 22, "z"], [50, 17, 157, 23], [50, 20, 157, 28, "sensor"], [50, 26, 157, 34], [50, 27, 157, 22, "z"], [50, 28, 157, 23], [52, 14, 159, 10], [53, 14, 160, 10], [53, 18, 160, 14], [53, 22, 160, 18], [53, 23, 160, 19, "platform"], [53, 31, 160, 27], [53, 36, 160, 32, "Platform"], [53, 44, 160, 40], [53, 45, 160, 41, "WEB_ANDROID"], [53, 56, 160, 52], [53, 58, 160, 54], [54, 16, 160, 54], [54, 20, 160, 54, "_ref"], [54, 24, 160, 54], [54, 27, 161, 24], [54, 28, 161, 25], [54, 29, 161, 26, "x"], [54, 30, 161, 27], [54, 32, 161, 29], [54, 33, 161, 30, "y"], [54, 34, 161, 31], [54, 36, 161, 33], [54, 37, 161, 34, "z"], [54, 38, 161, 35], [54, 39, 161, 36], [55, 16, 161, 13, "x"], [55, 17, 161, 14], [55, 20, 161, 14, "_ref"], [55, 24, 161, 14], [56, 16, 161, 16, "y"], [56, 17, 161, 17], [56, 20, 161, 17, "_ref"], [56, 24, 161, 17], [57, 16, 161, 19, "z"], [57, 17, 161, 20], [57, 20, 161, 20, "_ref"], [57, 24, 161, 20], [58, 14, 162, 10], [59, 14, 163, 10], [60, 14, 164, 11, "<PERSON><PERSON><PERSON><PERSON>"], [60, 26, 164, 23], [60, 27, 164, 32], [61, 16, 164, 34, "x"], [61, 17, 164, 35], [62, 16, 164, 37, "y"], [62, 17, 164, 38], [63, 16, 164, 40, "z"], [63, 17, 164, 41], [64, 16, 164, 43, "interfaceOrientation"], [64, 36, 164, 63], [64, 38, 164, 65], [65, 14, 164, 67], [65, 15, 164, 68], [65, 16, 164, 69], [66, 12, 165, 8], [66, 13, 165, 9], [67, 10, 166, 6], [67, 15, 166, 11, "SensorType"], [67, 38, 166, 21], [67, 39, 166, 22, "GYROSCOPE"], [67, 48, 166, 31], [68, 10, 167, 6], [68, 15, 167, 11, "SensorType"], [68, 38, 167, 21], [68, 39, 167, 22, "MAGNETIC_FIELD"], [68, 53, 167, 36], [69, 12, 168, 8], [69, 19, 168, 15], [69, 25, 168, 21], [70, 14, 169, 10], [70, 18, 169, 18, "x"], [70, 19, 169, 19], [70, 22, 169, 30, "sensor"], [70, 28, 169, 36], [70, 29, 169, 18, "x"], [70, 30, 169, 19], [71, 16, 169, 21, "y"], [71, 17, 169, 22], [71, 20, 169, 30, "sensor"], [71, 26, 169, 36], [71, 27, 169, 21, "y"], [71, 28, 169, 22], [72, 16, 169, 24, "z"], [72, 17, 169, 25], [72, 20, 169, 30, "sensor"], [72, 26, 169, 36], [72, 27, 169, 24, "z"], [72, 28, 169, 25], [73, 14, 170, 10], [74, 14, 171, 11, "<PERSON><PERSON><PERSON><PERSON>"], [74, 26, 171, 23], [74, 27, 171, 32], [75, 16, 171, 34, "x"], [75, 17, 171, 35], [76, 16, 171, 37, "y"], [76, 17, 171, 38], [77, 16, 171, 40, "z"], [77, 17, 171, 41], [78, 16, 171, 43, "interfaceOrientation"], [78, 36, 171, 63], [78, 38, 171, 65], [79, 14, 171, 67], [79, 15, 171, 68], [79, 16, 171, 69], [80, 12, 172, 8], [80, 13, 172, 9], [81, 10, 173, 6], [81, 15, 173, 11, "SensorType"], [81, 38, 173, 21], [81, 39, 173, 22, "ROTATION"], [81, 47, 173, 30], [82, 12, 174, 8], [82, 19, 174, 15], [82, 25, 174, 21], [83, 14, 175, 10], [83, 18, 175, 10, "_sensor$quaternion"], [83, 36, 175, 10], [83, 43, 175, 10, "_slicedToArray2"], [83, 58, 175, 10], [83, 59, 175, 10, "default"], [83, 66, 175, 10], [83, 68, 175, 33, "sensor"], [83, 74, 175, 39], [83, 75, 175, 40, "quaternion"], [83, 85, 175, 50], [84, 16, 175, 15, "qw"], [84, 18, 175, 17], [84, 21, 175, 17, "_sensor$quaternion"], [84, 39, 175, 17], [85, 16, 175, 19, "qx"], [85, 18, 175, 21], [85, 21, 175, 21, "_sensor$quaternion"], [85, 39, 175, 21], [86, 16, 175, 23, "qy"], [86, 18, 175, 25], [86, 21, 175, 25, "_sensor$quaternion"], [86, 39, 175, 25], [87, 16, 175, 27, "qz"], [87, 18, 175, 29], [87, 21, 175, 29, "_sensor$quaternion"], [87, 39, 175, 29], [89, 14, 177, 10], [90, 14, 178, 10], [90, 18, 178, 14], [90, 22, 178, 18], [90, 23, 178, 19, "platform"], [90, 31, 178, 27], [90, 36, 178, 32, "Platform"], [90, 44, 178, 40], [90, 45, 178, 41, "WEB_ANDROID"], [90, 56, 178, 52], [90, 58, 178, 54], [91, 16, 178, 54], [91, 20, 178, 54, "_ref2"], [91, 25, 178, 54], [91, 28, 179, 23], [91, 29, 179, 24, "qz"], [91, 31, 179, 26], [91, 33, 179, 28], [91, 34, 179, 29, "qy"], [91, 36, 179, 31], [91, 37, 179, 32], [92, 16, 179, 13, "qy"], [92, 18, 179, 15], [92, 21, 179, 15, "_ref2"], [92, 26, 179, 15], [93, 16, 179, 17, "qz"], [93, 18, 179, 19], [93, 21, 179, 19, "_ref2"], [93, 26, 179, 19], [94, 14, 180, 10], [96, 14, 182, 10], [97, 14, 183, 10], [97, 18, 183, 16, "yaw"], [97, 21, 183, 19], [97, 24, 183, 22], [97, 25, 183, 23, "Math"], [97, 29, 183, 27], [97, 30, 183, 28, "atan2"], [97, 35, 183, 33], [97, 36, 184, 12], [97, 39, 184, 15], [97, 43, 184, 19, "qy"], [97, 45, 184, 21], [97, 48, 184, 24, "qz"], [97, 50, 184, 26], [97, 53, 184, 29, "qw"], [97, 55, 184, 31], [97, 58, 184, 34, "qx"], [97, 60, 184, 36], [97, 61, 184, 37], [97, 63, 185, 12, "qw"], [97, 65, 185, 14], [97, 68, 185, 17, "qw"], [97, 70, 185, 19], [97, 73, 185, 22, "qx"], [97, 75, 185, 24], [97, 78, 185, 27, "qx"], [97, 80, 185, 29], [97, 83, 185, 32, "qy"], [97, 85, 185, 34], [97, 88, 185, 37, "qy"], [97, 90, 185, 39], [97, 93, 185, 42, "qz"], [97, 95, 185, 44], [97, 98, 185, 47, "qz"], [97, 100, 186, 10], [97, 101, 186, 11], [98, 14, 187, 10], [98, 18, 187, 16, "pitch"], [98, 23, 187, 21], [98, 26, 187, 24, "Math"], [98, 30, 187, 28], [98, 31, 187, 29, "sin"], [98, 34, 187, 32], [98, 35, 187, 33], [98, 36, 187, 34], [98, 39, 187, 37], [98, 43, 187, 41, "qx"], [98, 45, 187, 43], [98, 48, 187, 46, "qz"], [98, 50, 187, 48], [98, 53, 187, 51, "qw"], [98, 55, 187, 53], [98, 58, 187, 56, "qy"], [98, 60, 187, 58], [98, 61, 187, 59], [98, 62, 187, 60], [99, 14, 188, 10], [99, 18, 188, 16, "roll"], [99, 22, 188, 20], [99, 25, 188, 23], [99, 26, 188, 24, "Math"], [99, 30, 188, 28], [99, 31, 188, 29, "atan2"], [99, 36, 188, 34], [99, 37, 189, 12], [99, 40, 189, 15], [99, 44, 189, 19, "qx"], [99, 46, 189, 21], [99, 49, 189, 24, "qy"], [99, 51, 189, 26], [99, 54, 189, 29, "qw"], [99, 56, 189, 31], [99, 59, 189, 34, "qz"], [99, 61, 189, 36], [99, 62, 189, 37], [99, 64, 190, 12, "qw"], [99, 66, 190, 14], [99, 69, 190, 17, "qw"], [99, 71, 190, 19], [99, 74, 190, 22, "qx"], [99, 76, 190, 24], [99, 79, 190, 27, "qx"], [99, 81, 190, 29], [99, 84, 190, 32, "qy"], [99, 86, 190, 34], [99, 89, 190, 37, "qy"], [99, 91, 190, 39], [99, 94, 190, 42, "qz"], [99, 96, 190, 44], [99, 99, 190, 47, "qz"], [99, 101, 191, 10], [99, 102, 191, 11], [100, 14, 192, 10], [101, 14, 193, 11, "<PERSON><PERSON><PERSON><PERSON>"], [101, 26, 193, 23], [101, 27, 193, 32], [102, 16, 194, 12, "qw"], [102, 18, 194, 14], [103, 16, 195, 12, "qx"], [103, 18, 195, 14], [104, 16, 196, 12, "qy"], [104, 18, 196, 14], [105, 16, 197, 12, "qz"], [105, 18, 197, 14], [106, 16, 198, 12, "yaw"], [106, 19, 198, 15], [107, 16, 199, 12, "pitch"], [107, 21, 199, 17], [108, 16, 200, 12, "roll"], [108, 20, 200, 16], [109, 16, 201, 12, "interfaceOrientation"], [109, 36, 201, 32], [109, 38, 201, 34], [110, 14, 202, 10], [110, 15, 202, 11], [110, 16, 202, 12], [111, 12, 203, 8], [111, 13, 203, 9], [112, 8, 204, 4], [113, 6, 205, 2], [113, 7, 205, 3], [114, 4, 205, 3], [115, 4, 205, 3], [115, 15, 205, 3, "_createClass2"], [115, 28, 205, 3], [115, 29, 205, 3, "default"], [115, 36, 205, 3], [115, 38, 205, 3, "JSReanimated"], [115, 50, 205, 3], [116, 6, 205, 3, "key"], [116, 9, 205, 3], [117, 6, 205, 3, "value"], [117, 11, 205, 3], [117, 13, 47, 2], [117, 22, 47, 2, "scheduleOnUI"], [117, 34, 47, 14, "scheduleOnUI"], [117, 35, 47, 18, "worklet"], [117, 42, 47, 42], [117, 44, 47, 44], [118, 8, 48, 4], [119, 8, 49, 4, "requestAnimationFrameImpl"], [119, 33, 49, 29], [119, 34, 49, 30, "worklet"], [119, 41, 49, 37], [119, 42, 49, 38], [120, 6, 50, 2], [121, 4, 50, 3], [122, 6, 50, 3, "key"], [122, 9, 50, 3], [123, 6, 50, 3, "value"], [123, 11, 50, 3], [123, 13, 52, 2], [123, 22, 52, 2, "createWorkletRuntime"], [123, 42, 52, 22, "createWorkletRuntime"], [123, 43, 53, 4, "_name"], [123, 48, 53, 17], [123, 50, 54, 4, "_initializer"], [123, 62, 54, 42], [123, 64, 55, 20], [124, 8, 56, 4], [124, 14, 56, 10], [124, 18, 56, 14, "ReanimatedError"], [124, 41, 56, 29], [124, 42, 57, 6], [124, 98, 58, 4], [124, 99, 58, 5], [125, 6, 59, 2], [126, 4, 59, 3], [127, 6, 59, 3, "key"], [127, 9, 59, 3], [128, 6, 59, 3, "value"], [128, 11, 59, 3], [128, 13, 61, 2], [128, 22, 61, 2, "scheduleOnRuntime"], [128, 39, 61, 19, "scheduleOnRuntime"], [128, 40, 61, 19], [128, 42, 61, 22], [129, 8, 62, 4], [129, 14, 62, 10], [129, 18, 62, 14, "ReanimatedError"], [129, 41, 62, 29], [129, 42, 63, 6], [129, 95, 64, 4], [129, 96, 64, 5], [130, 6, 65, 2], [131, 4, 65, 3], [132, 6, 65, 3, "key"], [132, 9, 65, 3], [133, 6, 65, 3, "value"], [133, 11, 65, 3], [133, 13, 67, 2], [133, 22, 67, 2, "registerEventHandler"], [133, 42, 67, 22, "registerEventHandler"], [133, 43, 68, 4, "_event<PERSON><PERSON><PERSON>"], [133, 56, 68, 34], [133, 58, 69, 4, "_eventName"], [133, 68, 69, 22], [133, 70, 70, 4, "_emitterReactTag"], [133, 86, 70, 28], [133, 88, 71, 12], [134, 8, 72, 4], [134, 14, 72, 10], [134, 18, 72, 14, "ReanimatedError"], [134, 41, 72, 29], [134, 42, 73, 6], [134, 98, 74, 4], [134, 99, 74, 5], [135, 6, 75, 2], [136, 4, 75, 3], [137, 6, 75, 3, "key"], [137, 9, 75, 3], [138, 6, 75, 3, "value"], [138, 11, 75, 3], [138, 13, 77, 2], [138, 22, 77, 2, "unregisterEventHandler"], [138, 44, 77, 24, "unregisterEventHandler"], [138, 45, 77, 25, "_"], [138, 46, 77, 34], [138, 48, 77, 42], [139, 8, 78, 4], [139, 14, 78, 10], [139, 18, 78, 14, "ReanimatedError"], [139, 41, 78, 29], [139, 42, 79, 6], [139, 100, 80, 4], [139, 101, 80, 5], [140, 6, 81, 2], [141, 4, 81, 3], [142, 6, 81, 3, "key"], [142, 9, 81, 3], [143, 6, 81, 3, "value"], [143, 11, 81, 3], [143, 13, 83, 2], [143, 22, 83, 2, "enableLayoutAnimations"], [143, 44, 83, 24, "enableLayoutAnimations"], [143, 45, 83, 24], [143, 47, 83, 27], [144, 8, 84, 4], [144, 12, 84, 8], [144, 16, 84, 8, "isWeb"], [144, 38, 84, 13], [144, 40, 84, 14], [144, 41, 84, 15], [144, 43, 84, 17], [145, 10, 85, 6, "logger"], [145, 24, 85, 12], [145, 25, 85, 13, "warn"], [145, 29, 85, 17], [145, 30, 85, 18], [145, 79, 85, 67], [145, 80, 85, 68], [146, 8, 86, 4], [146, 9, 86, 5], [146, 15, 86, 11], [146, 19, 86, 15], [146, 23, 86, 15, "isJest"], [146, 46, 86, 21], [146, 48, 86, 22], [146, 49, 86, 23], [146, 51, 86, 25], [147, 10, 87, 6, "logger"], [147, 24, 87, 12], [147, 25, 87, 13, "warn"], [147, 29, 87, 17], [147, 30, 87, 18], [147, 77, 87, 65], [147, 78, 87, 66], [148, 8, 88, 4], [148, 9, 88, 5], [148, 15, 88, 11], [148, 19, 88, 15], [148, 23, 88, 15, "isChromeDebugger"], [148, 56, 88, 31], [148, 58, 88, 32], [148, 59, 88, 33], [148, 61, 88, 35], [149, 10, 89, 6, "logger"], [149, 24, 89, 12], [149, 25, 89, 13, "warn"], [149, 29, 89, 17], [149, 30, 89, 18], [149, 88, 89, 76], [149, 89, 89, 77], [150, 8, 90, 4], [150, 9, 90, 5], [150, 15, 90, 11], [151, 10, 91, 6, "logger"], [151, 24, 91, 12], [151, 25, 91, 13, "warn"], [151, 29, 91, 17], [151, 30, 91, 18], [151, 90, 91, 78], [151, 91, 91, 79], [152, 8, 92, 4], [153, 6, 93, 2], [154, 4, 93, 3], [155, 6, 93, 3, "key"], [155, 9, 93, 3], [156, 6, 93, 3, "value"], [156, 11, 93, 3], [156, 13, 95, 2], [156, 22, 95, 2, "configureLayoutAnimationBatch"], [156, 51, 95, 31, "configureLayoutAnimationBatch"], [156, 52, 95, 31], [156, 54, 95, 34], [157, 8, 96, 4], [158, 6, 96, 4], [159, 4, 97, 3], [160, 6, 97, 3, "key"], [160, 9, 97, 3], [161, 6, 97, 3, "value"], [161, 11, 97, 3], [161, 13, 99, 2], [161, 22, 99, 2, "setShouldAnimateExitingForTag"], [161, 51, 99, 31, "setShouldAnimateExitingForTag"], [161, 52, 99, 31], [161, 54, 99, 34], [162, 8, 100, 4], [163, 6, 100, 4], [164, 4, 101, 3], [165, 6, 101, 3, "key"], [165, 9, 101, 3], [166, 6, 101, 3, "value"], [166, 11, 101, 3], [166, 13, 103, 2], [166, 22, 103, 2, "registerSensor"], [166, 36, 103, 16, "registerSensor"], [166, 37, 104, 4, "sensorType"], [166, 47, 104, 26], [166, 49, 105, 4, "interval"], [166, 57, 105, 20], [166, 59, 106, 4, "_iosReferenceFrame"], [166, 77, 106, 30], [166, 79, 107, 4, "<PERSON><PERSON><PERSON><PERSON>"], [166, 91, 107, 71], [166, 93, 108, 12], [167, 8, 109, 4], [167, 12, 109, 8], [167, 13, 109, 9], [167, 17, 109, 9, "isWindowAvailable"], [167, 51, 109, 26], [167, 53, 109, 27], [167, 54, 109, 28], [167, 56, 109, 30], [168, 10, 110, 6], [169, 10, 111, 6], [170, 10, 112, 6], [170, 17, 112, 13], [170, 18, 112, 14], [170, 19, 112, 15], [171, 8, 113, 4], [172, 8, 115, 4], [172, 12, 115, 8], [172, 16, 115, 12], [172, 17, 115, 13, "platform"], [172, 25, 115, 21], [172, 30, 115, 26, "undefined"], [172, 39, 115, 35], [172, 41, 115, 37], [173, 10, 116, 6], [173, 14, 116, 10], [173, 15, 116, 11, "detectPlatform"], [173, 29, 116, 25], [173, 30, 116, 26], [173, 31, 116, 27], [174, 8, 117, 4], [175, 8, 119, 4], [175, 12, 119, 8], [175, 14, 119, 10], [175, 18, 119, 14], [175, 19, 119, 15, "getSensorName"], [175, 32, 119, 28], [175, 33, 119, 29, "sensorType"], [175, 43, 119, 39], [175, 44, 119, 40], [175, 48, 119, 44, "window"], [175, 54, 119, 50], [175, 55, 119, 51], [175, 57, 119, 53], [176, 10, 120, 6], [177, 10, 121, 6, "logger"], [177, 24, 121, 12], [177, 25, 121, 13, "warn"], [177, 29, 121, 17], [177, 30, 122, 8], [177, 56, 122, 34], [177, 60, 123, 11], [177, 64, 123, 11, "isWeb"], [177, 86, 123, 16], [177, 88, 123, 17], [177, 89, 123, 18], [177, 93, 123, 22, "location"], [177, 101, 123, 30], [177, 102, 123, 31, "protocol"], [177, 110, 123, 39], [177, 115, 123, 44], [177, 123, 123, 52], [177, 126, 124, 14], [177, 197, 124, 85], [177, 200, 125, 14], [177, 202, 125, 16], [177, 203, 125, 17], [177, 207, 126, 11], [177, 211, 126, 15], [177, 212, 126, 16, "platform"], [177, 220, 126, 24], [177, 225, 126, 29, "Platform"], [177, 233, 126, 37], [177, 234, 126, 38, "WEB_IOS"], [177, 241, 126, 45], [177, 244, 127, 14], [177, 424, 127, 194], [177, 427, 128, 14], [177, 429, 128, 16], [177, 430, 129, 6], [177, 431, 129, 7], [178, 10, 130, 6], [178, 17, 130, 13], [178, 18, 130, 14], [178, 19, 130, 15], [179, 8, 131, 4], [180, 8, 133, 4], [180, 12, 133, 8], [180, 16, 133, 12], [180, 17, 133, 13, "platform"], [180, 25, 133, 21], [180, 30, 133, 26, "undefined"], [180, 39, 133, 35], [180, 41, 133, 37], [181, 10, 134, 6], [181, 14, 134, 10], [181, 15, 134, 11, "detectPlatform"], [181, 29, 134, 25], [181, 30, 134, 26], [181, 31, 134, 27], [182, 8, 135, 4], [183, 8, 137, 4], [183, 12, 137, 10, "sensor"], [183, 18, 137, 27], [183, 21, 137, 30], [183, 25, 137, 34], [183, 26, 137, 35, "initializeSensor"], [183, 42, 137, 51], [183, 43, 137, 52, "sensorType"], [183, 53, 137, 62], [183, 55, 137, 64, "interval"], [183, 63, 137, 72], [183, 64, 137, 73], [184, 8, 138, 4, "sensor"], [184, 14, 138, 10], [184, 15, 138, 11, "addEventListener"], [184, 31, 138, 27], [184, 32, 139, 6], [184, 41, 139, 15], [184, 43, 140, 6], [184, 47, 140, 10], [184, 48, 140, 11, "getSensorCallback"], [184, 65, 140, 28], [184, 66, 140, 29, "sensor"], [184, 72, 140, 35], [184, 74, 140, 37, "sensorType"], [184, 84, 140, 47], [184, 86, 140, 49, "<PERSON><PERSON><PERSON><PERSON>"], [184, 98, 140, 61], [184, 99, 141, 4], [184, 100, 141, 5], [185, 8, 142, 4, "sensor"], [185, 14, 142, 10], [185, 15, 142, 11, "start"], [185, 20, 142, 16], [185, 21, 142, 17], [185, 22, 142, 18], [186, 8, 144, 4], [186, 12, 144, 8], [186, 13, 144, 9, "sensors"], [186, 20, 144, 16], [186, 21, 144, 17, "set"], [186, 24, 144, 20], [186, 25, 144, 21], [186, 29, 144, 25], [186, 30, 144, 26, "nextSensorId"], [186, 42, 144, 38], [186, 44, 144, 40, "sensor"], [186, 50, 144, 46], [186, 51, 144, 47], [187, 8, 145, 4], [187, 15, 145, 11], [187, 19, 145, 15], [187, 20, 145, 16, "nextSensorId"], [187, 32, 145, 28], [187, 34, 145, 30], [188, 6, 146, 2], [189, 4, 146, 3], [190, 6, 146, 3, "key"], [190, 9, 146, 3], [191, 6, 146, 3, "value"], [191, 11, 146, 3], [191, 13, 207, 2], [191, 22, 207, 2, "unregisterSensor"], [191, 38, 207, 18, "unregisterSensor"], [191, 39, 207, 19, "id"], [191, 41, 207, 29], [191, 43, 207, 37], [192, 8, 208, 4], [192, 12, 208, 10, "sensor"], [192, 18, 208, 39], [192, 21, 208, 42], [192, 25, 208, 46], [192, 26, 208, 47, "sensors"], [192, 33, 208, 54], [192, 34, 208, 55, "get"], [192, 37, 208, 58], [192, 38, 208, 59, "id"], [192, 40, 208, 61], [192, 41, 208, 62], [193, 8, 209, 4], [193, 12, 209, 8, "sensor"], [193, 18, 209, 14], [193, 23, 209, 19, "undefined"], [193, 32, 209, 28], [193, 34, 209, 30], [194, 10, 210, 6, "sensor"], [194, 16, 210, 12], [194, 17, 210, 13, "stop"], [194, 21, 210, 17], [194, 22, 210, 18], [194, 23, 210, 19], [195, 10, 211, 6], [195, 14, 211, 10], [195, 15, 211, 11, "sensors"], [195, 22, 211, 18], [195, 23, 211, 19, "delete"], [195, 29, 211, 25], [195, 30, 211, 26, "id"], [195, 32, 211, 28], [195, 33, 211, 29], [196, 8, 212, 4], [197, 6, 213, 2], [198, 4, 213, 3], [199, 6, 213, 3, "key"], [199, 9, 213, 3], [200, 6, 213, 3, "value"], [200, 11, 213, 3], [200, 13, 215, 2], [200, 22, 215, 2, "subscribeForKeyboardEvents"], [200, 48, 215, 28, "subscribeForKeyboardEvents"], [200, 49, 215, 29, "_"], [200, 50, 215, 61], [200, 52, 215, 71], [201, 8, 216, 4], [201, 12, 216, 8], [201, 16, 216, 8, "isWeb"], [201, 38, 216, 13], [201, 40, 216, 14], [201, 41, 216, 15], [201, 43, 216, 17], [202, 10, 217, 6, "logger"], [202, 24, 217, 12], [202, 25, 217, 13, "warn"], [202, 29, 217, 17], [202, 30, 217, 18], [202, 80, 217, 68], [202, 81, 217, 69], [203, 8, 218, 4], [203, 9, 218, 5], [203, 15, 218, 11], [203, 19, 218, 15], [203, 23, 218, 15, "isJest"], [203, 46, 218, 21], [203, 48, 218, 22], [203, 49, 218, 23], [203, 51, 218, 25], [204, 10, 219, 6, "logger"], [204, 24, 219, 12], [204, 25, 219, 13, "warn"], [204, 29, 219, 17], [204, 30, 219, 18], [204, 85, 219, 73], [204, 86, 219, 74], [205, 8, 220, 4], [205, 9, 220, 5], [205, 15, 220, 11], [205, 19, 220, 15], [205, 23, 220, 15, "isChromeDebugger"], [205, 56, 220, 31], [205, 58, 220, 32], [205, 59, 220, 33], [205, 61, 220, 35], [206, 10, 221, 6, "logger"], [206, 24, 221, 12], [206, 25, 221, 13, "warn"], [206, 29, 221, 17], [206, 30, 222, 8], [206, 96, 223, 6], [206, 97, 223, 7], [207, 8, 224, 4], [207, 9, 224, 5], [207, 15, 224, 11], [208, 10, 225, 6, "logger"], [208, 24, 225, 12], [208, 25, 225, 13, "warn"], [208, 29, 225, 17], [208, 30, 226, 8], [208, 91, 227, 6], [208, 92, 227, 7], [209, 8, 228, 4], [210, 8, 229, 4], [210, 15, 229, 11], [210, 16, 229, 12], [210, 17, 229, 13], [211, 6, 230, 2], [212, 4, 230, 3], [213, 6, 230, 3, "key"], [213, 9, 230, 3], [214, 6, 230, 3, "value"], [214, 11, 230, 3], [214, 13, 232, 2], [214, 22, 232, 2, "unsubscribeFromKeyboardEvents"], [214, 51, 232, 31, "unsubscribeFromKeyboardEvents"], [214, 52, 232, 32, "_"], [214, 53, 232, 41], [214, 55, 232, 49], [215, 8, 233, 4], [216, 6, 233, 4], [217, 4, 234, 3], [218, 6, 234, 3, "key"], [218, 9, 234, 3], [219, 6, 234, 3, "value"], [219, 11, 234, 3], [219, 13, 236, 2], [219, 22, 236, 2, "initializeSensor"], [219, 38, 236, 18, "initializeSensor"], [219, 39, 236, 19, "sensorType"], [219, 49, 236, 41], [219, 51, 236, 43, "interval"], [219, 59, 236, 59], [219, 61, 236, 72], [220, 8, 237, 4], [220, 12, 237, 10, "config"], [220, 18, 237, 16], [220, 21, 238, 6, "interval"], [220, 29, 238, 14], [220, 33, 238, 18], [220, 34, 238, 19], [220, 37, 239, 10], [221, 10, 239, 12, "referenceFrame"], [221, 24, 239, 26], [221, 26, 239, 28], [222, 8, 239, 37], [222, 9, 239, 38], [222, 12, 240, 10], [223, 10, 240, 12, "frequency"], [223, 19, 240, 21], [223, 21, 240, 23], [223, 25, 240, 27], [223, 28, 240, 30, "interval"], [224, 8, 240, 39], [224, 9, 240, 40], [225, 8, 241, 4], [225, 16, 241, 12, "sensorType"], [225, 26, 241, 22], [226, 10, 242, 6], [226, 15, 242, 11, "SensorType"], [226, 38, 242, 21], [226, 39, 242, 22, "ACCELEROMETER"], [226, 52, 242, 35], [227, 12, 243, 8], [227, 19, 243, 15], [227, 23, 243, 19, "window"], [227, 29, 243, 25], [227, 30, 243, 26, "Accelerometer"], [227, 43, 243, 39], [227, 44, 243, 40, "config"], [227, 50, 243, 46], [227, 51, 243, 47], [228, 10, 244, 6], [228, 15, 244, 11, "SensorType"], [228, 38, 244, 21], [228, 39, 244, 22, "GYROSCOPE"], [228, 48, 244, 31], [229, 12, 245, 8], [229, 19, 245, 15], [229, 23, 245, 19, "window"], [229, 29, 245, 25], [229, 30, 245, 26, "Gyroscope"], [229, 39, 245, 35], [229, 40, 245, 36, "config"], [229, 46, 245, 42], [229, 47, 245, 43], [230, 10, 246, 6], [230, 15, 246, 11, "SensorType"], [230, 38, 246, 21], [230, 39, 246, 22, "GRAVITY"], [230, 46, 246, 29], [231, 12, 247, 8], [231, 19, 247, 15], [231, 23, 247, 19, "window"], [231, 29, 247, 25], [231, 30, 247, 26, "GravitySensor"], [231, 43, 247, 39], [231, 44, 247, 40, "config"], [231, 50, 247, 46], [231, 51, 247, 47], [232, 10, 248, 6], [232, 15, 248, 11, "SensorType"], [232, 38, 248, 21], [232, 39, 248, 22, "MAGNETIC_FIELD"], [232, 53, 248, 36], [233, 12, 249, 8], [233, 19, 249, 15], [233, 23, 249, 19, "window"], [233, 29, 249, 25], [233, 30, 249, 26, "Magnetometer"], [233, 42, 249, 38], [233, 43, 249, 39, "config"], [233, 49, 249, 45], [233, 50, 249, 46], [234, 10, 250, 6], [234, 15, 250, 11, "SensorType"], [234, 38, 250, 21], [234, 39, 250, 22, "ROTATION"], [234, 47, 250, 30], [235, 12, 251, 8], [235, 19, 251, 15], [235, 23, 251, 19, "window"], [235, 29, 251, 25], [235, 30, 251, 26, "AbsoluteOrientationSensor"], [235, 55, 251, 51], [235, 56, 251, 52, "config"], [235, 62, 251, 58], [235, 63, 251, 59], [236, 8, 252, 4], [237, 6, 253, 2], [238, 4, 253, 3], [239, 6, 253, 3, "key"], [239, 9, 253, 3], [240, 6, 253, 3, "value"], [240, 11, 253, 3], [240, 13, 255, 2], [240, 22, 255, 2, "getSensorName"], [240, 35, 255, 15, "getSensorName"], [240, 36, 255, 16, "sensorType"], [240, 46, 255, 38], [240, 48, 255, 48], [241, 8, 256, 4], [241, 16, 256, 12, "sensorType"], [241, 26, 256, 22], [242, 10, 257, 6], [242, 15, 257, 11, "SensorType"], [242, 38, 257, 21], [242, 39, 257, 22, "ACCELEROMETER"], [242, 52, 257, 35], [243, 12, 258, 8], [243, 19, 258, 15], [243, 34, 258, 30], [244, 10, 259, 6], [244, 15, 259, 11, "SensorType"], [244, 38, 259, 21], [244, 39, 259, 22, "GRAVITY"], [244, 46, 259, 29], [245, 12, 260, 8], [245, 19, 260, 15], [245, 34, 260, 30], [246, 10, 261, 6], [246, 15, 261, 11, "SensorType"], [246, 38, 261, 21], [246, 39, 261, 22, "GYROSCOPE"], [246, 48, 261, 31], [247, 12, 262, 8], [247, 19, 262, 15], [247, 30, 262, 26], [248, 10, 263, 6], [248, 15, 263, 11, "SensorType"], [248, 38, 263, 21], [248, 39, 263, 22, "MAGNETIC_FIELD"], [248, 53, 263, 36], [249, 12, 264, 8], [249, 19, 264, 15], [249, 33, 264, 29], [250, 10, 265, 6], [250, 15, 265, 11, "SensorType"], [250, 38, 265, 21], [250, 39, 265, 22, "ROTATION"], [250, 47, 265, 30], [251, 12, 266, 8], [251, 19, 266, 15], [251, 46, 266, 42], [252, 8, 267, 4], [253, 6, 268, 2], [254, 4, 268, 3], [255, 6, 268, 3, "key"], [255, 9, 268, 3], [256, 6, 268, 3, "value"], [256, 11, 268, 3], [256, 13, 270, 2], [256, 22, 270, 2, "detectPlatform"], [256, 36, 270, 16, "detectPlatform"], [256, 37, 270, 16], [256, 39, 270, 19], [257, 8, 271, 4], [257, 12, 271, 10, "userAgent"], [257, 21, 271, 19], [257, 24, 271, 22, "navigator"], [257, 33, 271, 31], [257, 34, 271, 32, "userAgent"], [257, 43, 271, 41], [257, 47, 271, 45, "navigator"], [257, 56, 271, 54], [257, 57, 271, 55, "vendor"], [257, 63, 271, 61], [257, 67, 271, 65, "window"], [257, 73, 271, 71], [257, 74, 271, 72, "opera"], [257, 79, 271, 77], [258, 8, 272, 4], [258, 12, 272, 8, "userAgent"], [258, 21, 272, 17], [258, 26, 272, 22, "undefined"], [258, 35, 272, 31], [258, 37, 272, 33], [259, 10, 273, 6], [259, 14, 273, 10], [259, 15, 273, 11, "platform"], [259, 23, 273, 19], [259, 26, 273, 22, "Platform"], [259, 34, 273, 30], [259, 35, 273, 31, "UNKNOWN"], [259, 42, 273, 38], [260, 8, 274, 4], [260, 9, 274, 5], [260, 15, 274, 11], [260, 19, 274, 15], [260, 37, 274, 33], [260, 38, 274, 34, "test"], [260, 42, 274, 38], [260, 43, 274, 39, "userAgent"], [260, 52, 274, 48], [260, 53, 274, 49], [260, 55, 274, 51], [261, 10, 275, 6], [261, 14, 275, 10], [261, 15, 275, 11, "platform"], [261, 23, 275, 19], [261, 26, 275, 22, "Platform"], [261, 34, 275, 30], [261, 35, 275, 31, "WEB_IOS"], [261, 42, 275, 38], [262, 8, 276, 4], [262, 9, 276, 5], [262, 15, 276, 11], [262, 19, 276, 15], [262, 29, 276, 25], [262, 30, 276, 26, "test"], [262, 34, 276, 30], [262, 35, 276, 31, "userAgent"], [262, 44, 276, 40], [262, 45, 276, 41], [262, 47, 276, 43], [263, 10, 277, 6], [263, 14, 277, 10], [263, 15, 277, 11, "platform"], [263, 23, 277, 19], [263, 26, 277, 22, "Platform"], [263, 34, 277, 30], [263, 35, 277, 31, "WEB_ANDROID"], [263, 46, 277, 42], [264, 8, 278, 4], [264, 9, 278, 5], [264, 15, 278, 11], [265, 10, 279, 6], [265, 14, 279, 10], [265, 15, 279, 11, "platform"], [265, 23, 279, 19], [265, 26, 279, 22, "Platform"], [265, 34, 279, 30], [265, 35, 279, 31, "WEB"], [265, 38, 279, 34], [266, 8, 280, 4], [267, 6, 281, 2], [268, 4, 281, 3], [269, 6, 281, 3, "key"], [269, 9, 281, 3], [270, 6, 281, 3, "value"], [270, 11, 281, 3], [270, 13, 283, 2], [270, 22, 283, 2, "getViewProp"], [270, 33, 283, 13, "getViewProp"], [270, 34, 284, 4, "_viewTag"], [270, 42, 284, 20], [270, 44, 285, 4, "_propName"], [270, 53, 285, 21], [270, 55, 286, 4, "_component"], [270, 65, 286, 32], [270, 67, 287, 4, "_callback"], [270, 76, 287, 35], [270, 78, 288, 16], [271, 8, 289, 4], [271, 14, 289, 10], [271, 18, 289, 14, "ReanimatedError"], [271, 41, 289, 29], [271, 42, 289, 30], [271, 89, 289, 77], [271, 90, 289, 78], [272, 6, 290, 2], [273, 4, 290, 3], [274, 6, 290, 3, "key"], [274, 9, 290, 3], [275, 6, 290, 3, "value"], [275, 11, 290, 3], [275, 13, 292, 2], [275, 22, 292, 2, "configureProps"], [275, 36, 292, 16, "configureProps"], [275, 37, 292, 16], [275, 39, 292, 19], [276, 8, 293, 4], [276, 14, 293, 10], [276, 18, 293, 14, "ReanimatedError"], [276, 41, 293, 29], [276, 42, 294, 6], [276, 92, 295, 4], [276, 93, 295, 5], [277, 6, 296, 2], [278, 4, 296, 3], [279, 6, 296, 3, "key"], [279, 9, 296, 3], [280, 6, 296, 3, "value"], [280, 11, 296, 3], [280, 13, 298, 2], [280, 22, 298, 2, "executeOnUIRuntimeSync"], [280, 44, 298, 24, "executeOnUIRuntimeSync"], [280, 45, 298, 31, "_shareable"], [280, 55, 298, 58], [280, 57, 298, 63], [281, 8, 299, 4], [281, 14, 299, 10], [281, 18, 299, 14, "ReanimatedError"], [281, 41, 299, 29], [281, 42, 300, 6], [281, 102, 301, 4], [281, 103, 301, 5], [282, 6, 302, 2], [283, 4, 302, 3], [284, 6, 302, 3, "key"], [284, 9, 302, 3], [285, 6, 302, 3, "value"], [285, 11, 302, 3], [285, 13, 304, 2], [285, 22, 304, 2, "markNodeAsRemovable"], [285, 41, 304, 21, "markNodeAsRemovable"], [285, 42, 304, 22, "_shadowNodeWrapper"], [285, 60, 304, 59], [285, 62, 304, 67], [286, 8, 305, 4], [286, 14, 305, 10], [286, 18, 305, 14, "ReanimatedError"], [286, 41, 305, 29], [286, 42, 306, 6], [286, 97, 307, 4], [286, 98, 307, 5], [287, 6, 308, 2], [288, 4, 308, 3], [289, 6, 308, 3, "key"], [289, 9, 308, 3], [290, 6, 308, 3, "value"], [290, 11, 308, 3], [290, 13, 310, 2], [290, 22, 310, 2, "unmarkNodeAsRemovable"], [290, 43, 310, 23, "unmarkNodeAsRemovable"], [290, 44, 310, 24, "_viewTag"], [290, 52, 310, 40], [290, 54, 310, 48], [291, 8, 311, 4], [291, 14, 311, 10], [291, 18, 311, 14, "ReanimatedError"], [291, 41, 311, 29], [291, 42, 312, 6], [291, 99, 313, 4], [291, 100, 313, 5], [292, 6, 314, 2], [293, 4, 314, 3], [294, 2, 314, 3], [294, 7, 317, 0], [295, 2, 318, 0], [296, 2, 319, 0], [297, 2, 319, 0], [297, 6, 320, 12, "Platform"], [297, 14, 320, 20], [297, 17, 320, 20, "exports"], [297, 24, 320, 20], [297, 25, 320, 20, "Platform"], [297, 33, 320, 20], [297, 59, 320, 12, "Platform"], [297, 67, 320, 20], [298, 4, 320, 12, "Platform"], [298, 12, 320, 20], [299, 4, 320, 12, "Platform"], [299, 12, 320, 20], [300, 4, 320, 12, "Platform"], [300, 12, 320, 20], [301, 4, 320, 12, "Platform"], [301, 12, 320, 20], [302, 4, 320, 20], [302, 11, 320, 12, "Platform"], [302, 19, 320, 20], [303, 2, 320, 20], [304, 0, 320, 20], [304, 3]], "functionMap": {"names": ["<global>", "createJSReanimatedModule", "JSReanimated", "scheduleOnUI", "createWorkletRuntime", "scheduleOnRuntime", "registerEventHandler", "unregisterEventHandler", "enableLayoutAnimations", "configureLayoutAnimationBatch", "setShouldAnimateExitingForTag", "registerSensor", "getSensorCallback", "<anonymous>", "unregisterSensor", "subscribeForKeyboardEvents", "unsubscribeFromKeyboardEvents", "initializeSensor", "getSensorName", "detectPlatform", "getViewProp", "configureProps", "executeOnUIRuntimeSync", "markNodeAsRemovable", "unmarkNodeAsRemovable"], "mappings": "AAA;OCwB;CDE;AEU;ECU;GDG;EEE;GFO;EGE;GHI;EIE;GJQ;EKE;GLI;EME;GNU;EOE;GPE;EQE;GRE;ESE;GT2C;sBUE;eCQ;SDS;eCG;SDI;eCE;SD6B;GVE;EYE;GZM;EaE;Gbe;EcE;GdE;EeE;GfiB;EgBE;GhBa;EiBE;GjBW;EkBE;GlBO;EmBE;GnBI;EoBE;GpBI;EqBE;GrBI;EsBE;GtBI;CFC"}}, "type": "js/module"}]}