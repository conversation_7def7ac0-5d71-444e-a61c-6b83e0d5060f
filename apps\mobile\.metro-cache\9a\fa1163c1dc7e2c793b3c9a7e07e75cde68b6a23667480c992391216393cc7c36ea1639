{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  function _classCallCheck(a, n) {\n    if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n  }\n  module.exports = _classCallCheck, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 6, "map": [[2, 2, 1, 0], [2, 11, 1, 9, "_classCallCheck"], [2, 26, 1, 24, "_classCallCheck"], [2, 27, 1, 25, "a"], [2, 28, 1, 26], [2, 30, 1, 28, "n"], [2, 31, 1, 29], [2, 33, 1, 31], [3, 4, 2, 2], [3, 8, 2, 6], [3, 10, 2, 8, "a"], [3, 11, 2, 9], [3, 23, 2, 21, "n"], [3, 24, 2, 22], [3, 25, 2, 23], [3, 27, 2, 25], [3, 33, 2, 31], [3, 37, 2, 35, "TypeError"], [3, 46, 2, 44], [3, 47, 2, 45], [3, 82, 2, 80], [3, 83, 2, 81], [4, 2, 3, 0], [5, 2, 4, 0, "module"], [5, 8, 4, 6], [5, 9, 4, 7, "exports"], [5, 16, 4, 14], [5, 19, 4, 17, "_classCallCheck"], [5, 34, 4, 32], [5, 36, 4, 34, "module"], [5, 42, 4, 40], [5, 43, 4, 41, "exports"], [5, 50, 4, 48], [5, 51, 4, 49, "__esModule"], [5, 61, 4, 59], [5, 64, 4, 62], [5, 68, 4, 66], [5, 70, 4, 68, "module"], [5, 76, 4, 74], [5, 77, 4, 75, "exports"], [5, 84, 4, 82], [5, 85, 4, 83], [5, 94, 4, 92], [5, 95, 4, 93], [5, 98, 4, 96, "module"], [5, 104, 4, 102], [5, 105, 4, 103, "exports"], [5, 112, 4, 110], [6, 0, 4, 111], [6, 3]], "functionMap": {"names": ["_classCallCheck", "<global>"], "mappings": "AAA;CCE"}}, "type": "js/module"}]}