{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../src/private/animated/createAnimatedPropsHook", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 89}}], "key": "3eMtqah/1mRI7Xa1A9DF9DkKr5M=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createAnimatedPropsHook = _interopRequireDefault(require(_dependencyMap[1], \"../../src/private/animated/createAnimatedPropsHook\"));\n  var _default = exports.default = (0, _createAnimatedPropsHook.default)(null);\n});", "lineCount": 9, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_createAnimatedPropsHook"], [7, 30, 13, 0], [7, 33, 13, 0, "_interopRequireDefault"], [7, 55, 13, 0], [7, 56, 13, 0, "require"], [7, 63, 13, 0], [7, 64, 13, 0, "_dependencyMap"], [7, 78, 13, 0], [8, 2, 13, 89], [8, 6, 13, 89, "_default"], [8, 14, 13, 89], [8, 17, 13, 89, "exports"], [8, 24, 13, 89], [8, 25, 13, 89, "default"], [8, 32, 13, 89], [8, 35, 18, 15], [8, 39, 18, 15, "createAnimatedPropsHook"], [8, 71, 18, 38], [8, 73, 18, 39], [8, 77, 18, 43], [8, 78, 18, 44], [9, 0, 18, 44], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}