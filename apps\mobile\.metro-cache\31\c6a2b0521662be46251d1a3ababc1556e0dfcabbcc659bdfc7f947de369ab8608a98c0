{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.MountRegistry = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  // eslint-disable-next-line @typescript-eslint/no-extraneous-class\n  var MountRegistry = exports.MountRegistry = /*#__PURE__*/function () {\n    function MountRegistry() {\n      (0, _classCallCheck2.default)(this, MountRegistry);\n    }\n    return (0, _createClass2.default)(MountRegistry, null, [{\n      key: \"addMountListener\",\n      value: function addMountListener(listener) {\n        this.mountListeners.add(listener);\n        return () => {\n          this.mountListeners.delete(listener);\n        };\n      }\n    }, {\n      key: \"addUnmountListener\",\n      value: function addUnmountListener(listener) {\n        this.unmountListeners.add(listener);\n        return () => {\n          this.unmountListeners.delete(listener);\n        };\n      }\n    }, {\n      key: \"gestureHandlerWillMount\",\n      value: function gestureHandlerWillMount(handler) {\n        this.mountListeners.forEach(listener => listener(handler));\n      }\n    }, {\n      key: \"gestureHandlerWillUnmount\",\n      value: function gestureHandlerWillUnmount(handler) {\n        this.unmountListeners.forEach(listener => listener(handler));\n      }\n    }, {\n      key: \"gestureWillMount\",\n      value: function gestureWillMount(gesture) {\n        this.mountListeners.forEach(listener => listener(gesture));\n      }\n    }, {\n      key: \"gestureWillUnmount\",\n      value: function gestureWillUnmount(gesture) {\n        this.unmountListeners.forEach(listener => listener(gesture));\n      }\n    }]);\n  }();\n  MountRegistry.mountListeners = new Set();\n  MountRegistry.unmountListeners = new Set();\n});", "lineCount": 54, "map": [[9, 2, 11, 0], [10, 2, 11, 0], [10, 6, 12, 13, "MountRegistry"], [10, 19, 12, 26], [10, 22, 12, 26, "exports"], [10, 29, 12, 26], [10, 30, 12, 26, "MountRegistry"], [10, 43, 12, 26], [11, 4, 12, 26], [11, 13, 12, 26, "MountRegistry"], [11, 27, 12, 26], [12, 6, 12, 26], [12, 10, 12, 26, "_classCallCheck2"], [12, 26, 12, 26], [12, 27, 12, 26, "default"], [12, 34, 12, 26], [12, 42, 12, 26, "MountRegistry"], [12, 55, 12, 26], [13, 4, 12, 26], [14, 4, 12, 26], [14, 15, 12, 26, "_createClass2"], [14, 28, 12, 26], [14, 29, 12, 26, "default"], [14, 36, 12, 26], [14, 38, 12, 26, "MountRegistry"], [14, 51, 12, 26], [15, 6, 12, 26, "key"], [15, 9, 12, 26], [16, 6, 12, 26, "value"], [16, 11, 12, 26], [16, 13, 16, 2], [16, 22, 16, 9, "addMountListener"], [16, 38, 16, 25, "addMountListener"], [16, 39, 16, 26, "listener"], [16, 47, 16, 56], [16, 49, 16, 70], [17, 8, 17, 4], [17, 12, 17, 8], [17, 13, 17, 9, "mountListeners"], [17, 27, 17, 23], [17, 28, 17, 24, "add"], [17, 31, 17, 27], [17, 32, 17, 28, "listener"], [17, 40, 17, 36], [17, 41, 17, 37], [18, 8, 19, 4], [18, 15, 19, 11], [18, 21, 19, 17], [19, 10, 20, 6], [19, 14, 20, 10], [19, 15, 20, 11, "mountListeners"], [19, 29, 20, 25], [19, 30, 20, 26, "delete"], [19, 36, 20, 32], [19, 37, 20, 33, "listener"], [19, 45, 20, 41], [19, 46, 20, 42], [20, 8, 21, 4], [20, 9, 21, 5], [21, 6, 22, 2], [22, 4, 22, 3], [23, 6, 22, 3, "key"], [23, 9, 22, 3], [24, 6, 22, 3, "value"], [24, 11, 22, 3], [24, 13, 24, 2], [24, 22, 24, 9, "addUnmountListener"], [24, 40, 24, 27, "addUnmountListener"], [24, 41, 24, 28, "listener"], [24, 49, 24, 58], [24, 51, 24, 72], [25, 8, 25, 4], [25, 12, 25, 8], [25, 13, 25, 9, "unmountListeners"], [25, 29, 25, 25], [25, 30, 25, 26, "add"], [25, 33, 25, 29], [25, 34, 25, 30, "listener"], [25, 42, 25, 38], [25, 43, 25, 39], [26, 8, 27, 4], [26, 15, 27, 11], [26, 21, 27, 17], [27, 10, 28, 6], [27, 14, 28, 10], [27, 15, 28, 11, "unmountListeners"], [27, 31, 28, 27], [27, 32, 28, 28, "delete"], [27, 38, 28, 34], [27, 39, 28, 35, "listener"], [27, 47, 28, 43], [27, 48, 28, 44], [28, 8, 29, 4], [28, 9, 29, 5], [29, 6, 30, 2], [30, 4, 30, 3], [31, 6, 30, 3, "key"], [31, 9, 30, 3], [32, 6, 30, 3, "value"], [32, 11, 30, 3], [32, 13, 32, 2], [32, 22, 32, 9, "gestureHandlerWillMount"], [32, 45, 32, 32, "gestureHandlerWillMount"], [32, 46, 32, 33, "handler"], [32, 53, 32, 57], [32, 55, 32, 59], [33, 8, 33, 4], [33, 12, 33, 8], [33, 13, 33, 9, "mountListeners"], [33, 27, 33, 23], [33, 28, 33, 24, "for<PERSON>ach"], [33, 35, 33, 31], [33, 36, 33, 33, "listener"], [33, 44, 33, 41], [33, 48, 34, 6, "listener"], [33, 56, 34, 14], [33, 57, 34, 15, "handler"], [33, 64, 34, 54], [33, 65, 35, 4], [33, 66, 35, 5], [34, 6, 36, 2], [35, 4, 36, 3], [36, 6, 36, 3, "key"], [36, 9, 36, 3], [37, 6, 36, 3, "value"], [37, 11, 36, 3], [37, 13, 38, 2], [37, 22, 38, 9, "gestureHandlerWillUnmount"], [37, 47, 38, 34, "gestureHandlerWillUnmount"], [37, 48, 38, 35, "handler"], [37, 55, 38, 59], [37, 57, 38, 61], [38, 8, 39, 4], [38, 12, 39, 8], [38, 13, 39, 9, "unmountListeners"], [38, 29, 39, 25], [38, 30, 39, 26, "for<PERSON>ach"], [38, 37, 39, 33], [38, 38, 39, 35, "listener"], [38, 46, 39, 43], [38, 50, 40, 6, "listener"], [38, 58, 40, 14], [38, 59, 40, 15, "handler"], [38, 66, 40, 54], [38, 67, 41, 4], [38, 68, 41, 5], [39, 6, 42, 2], [40, 4, 42, 3], [41, 6, 42, 3, "key"], [41, 9, 42, 3], [42, 6, 42, 3, "value"], [42, 11, 42, 3], [42, 13, 44, 2], [42, 22, 44, 9, "gestureWillMount"], [42, 38, 44, 25, "gestureWillMount"], [42, 39, 44, 26, "gesture"], [42, 46, 44, 46], [42, 48, 44, 48], [43, 8, 45, 4], [43, 12, 45, 8], [43, 13, 45, 9, "mountListeners"], [43, 27, 45, 23], [43, 28, 45, 24, "for<PERSON>ach"], [43, 35, 45, 31], [43, 36, 45, 33, "listener"], [43, 44, 45, 41], [43, 48, 45, 46, "listener"], [43, 56, 45, 54], [43, 57, 45, 55, "gesture"], [43, 64, 45, 62], [43, 65, 45, 63], [43, 66, 45, 64], [44, 6, 46, 2], [45, 4, 46, 3], [46, 6, 46, 3, "key"], [46, 9, 46, 3], [47, 6, 46, 3, "value"], [47, 11, 46, 3], [47, 13, 48, 2], [47, 22, 48, 9, "gestureWillUnmount"], [47, 40, 48, 27, "gestureWillUnmount"], [47, 41, 48, 28, "gesture"], [47, 48, 48, 48], [47, 50, 48, 50], [48, 8, 49, 4], [48, 12, 49, 8], [48, 13, 49, 9, "unmountListeners"], [48, 29, 49, 25], [48, 30, 49, 26, "for<PERSON>ach"], [48, 37, 49, 33], [48, 38, 49, 35, "listener"], [48, 46, 49, 43], [48, 50, 49, 48, "listener"], [48, 58, 49, 56], [48, 59, 49, 57, "gesture"], [48, 66, 49, 64], [48, 67, 49, 65], [48, 68, 49, 66], [49, 6, 50, 2], [50, 4, 50, 3], [51, 2, 50, 3], [52, 2, 12, 13, "MountRegistry"], [52, 15, 12, 26], [52, 16, 13, 17, "mountListeners"], [52, 30, 13, 31], [52, 33, 13, 34], [52, 37, 13, 38, "Set"], [52, 40, 13, 41], [52, 41, 13, 64], [52, 42, 13, 65], [53, 2, 12, 13, "MountRegistry"], [53, 15, 12, 26], [53, 16, 14, 17, "unmountListeners"], [53, 32, 14, 33], [53, 35, 14, 36], [53, 39, 14, 40, "Set"], [53, 42, 14, 43], [53, 43, 14, 66], [53, 44, 14, 67], [54, 0, 14, 67], [54, 3]], "functionMap": {"names": ["<global>", "MountRegistry", "MountRegistry.addMountListener", "<anonymous>", "MountRegistry.addUnmountListener", "MountRegistry.gestureHandlerWillMount", "mountListeners.forEach$argument_0", "MountRegistry.gestureHandlerWillUnmount", "unmountListeners.forEach$argument_0", "MountRegistry.gestureWillMount", "MountRegistry.gestureWillUnmount"], "mappings": "AAA;OCW;ECI;WCG;KDE;GDC;EGE;WDG;KCE;GHC;EIE;gCCC;uDDC;GJE;EME;kCCC;uDDC;GNE;EQE;gCHC,+BG;GRC;ESE;kCFC,+BE;GTC;CDC"}}, "type": "js/module"}]}