{"dependencies": [{"name": "@react-navigation/routers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 58, "index": 73}}], "key": "TumjUqgKkj40CL5/as2VxzLfO54=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 74}, "end": {"line": 4, "column": 31, "index": 105}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 106}, "end": {"line": 5, "column": 59, "index": 165}}], "key": "RM0XoJ1uy5+hqq85ZlLNt6FYuco=", "exportNames": ["*"]}}, {"name": "./types.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 166}, "end": {"line": 6, "column": 47, "index": 213}}], "key": "yJvqu7zVoaSgx/LOxsKU/6eppkQ=", "exportNames": ["*"]}}, {"name": "./UnhandledActionContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 214}, "end": {"line": 7, "column": 69, "index": 283}}], "key": "hbxQFgxZ0nD1dniBnLKjFC5C/nw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useNavigationHelpers = useNavigationHelpers;\n  var _routers = require(_dependencyMap[0], \"@react-navigation/routers\");\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _NavigationContext = require(_dependencyMap[2], \"./NavigationContext.js\");\n  var _types = require(_dependencyMap[3], \"./types.js\");\n  var _UnhandledActionContext = require(_dependencyMap[4], \"./UnhandledActionContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  // This is to make TypeScript compiler happy\n  // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n  _types.PrivateValueStore;\n  /**\n   * Navigation object with helper methods to be used by a navigator.\n   * This object includes methods for common actions as well as methods the parent screen's navigation object.\n   */\n  function useNavigationHelpers(_ref) {\n    var navigatorId = _ref.id,\n      onAction = _ref.onAction,\n      getState = _ref.getState,\n      emitter = _ref.emitter,\n      router = _ref.router,\n      stateRef = _ref.stateRef;\n    var onUnhandledAction = React.useContext(_UnhandledActionContext.UnhandledActionContext);\n    var parentNavigationHelpers = React.useContext(_NavigationContext.NavigationContext);\n    return React.useMemo(() => {\n      var dispatch = op => {\n        var action = typeof op === 'function' ? op(getState()) : op;\n        var handled = onAction(action);\n        if (!handled) {\n          onUnhandledAction?.(action);\n        }\n      };\n      var actions = {\n        ...router.actionCreators,\n        ..._routers.CommonActions\n      };\n      var helpers = Object.keys(actions).reduce((acc, name) => {\n        // @ts-expect-error: name is a valid key, but TypeScript is dumb\n        acc[name] = function () {\n          return dispatch(actions[name](...arguments));\n        };\n        return acc;\n      }, {});\n      var navigationHelpers = {\n        ...parentNavigationHelpers,\n        ...helpers,\n        dispatch,\n        emit: emitter.emit,\n        isFocused: parentNavigationHelpers ? parentNavigationHelpers.isFocused : () => true,\n        canGoBack: () => {\n          var state = getState();\n          return router.getStateForAction(state, _routers.CommonActions.goBack(), {\n            routeNames: state.routeNames,\n            routeParamList: {},\n            routeGetIdList: {}\n          }) !== null || parentNavigationHelpers?.canGoBack() || false;\n        },\n        getId: () => navigatorId,\n        getParent: id => {\n          if (id !== undefined) {\n            var current = navigationHelpers;\n            while (current && id !== current.getId()) {\n              current = current.getParent();\n            }\n            return current;\n          }\n          return parentNavigationHelpers;\n        },\n        getState: () => {\n          // FIXME: Workaround for when the state is read during render\n          // By this time, we haven't committed the new state yet\n          // Without this `useSyncExternalStore` will keep reading the old state\n          // This may result in `useNavigationState` or `useIsFocused` returning wrong values\n          // Apart from `useSyncExternalStore`, `getState` should never be called during render\n          if (stateRef.current != null) {\n            return stateRef.current;\n          }\n          return getState();\n        }\n      };\n      return navigationHelpers;\n    }, [router, parentNavigationHelpers, emitter.emit, getState, onAction, onUnhandledAction, navigatorId, stateRef]);\n  }\n});", "lineCount": 89, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useNavigationHelpers"], [7, 30, 1, 13], [7, 33, 1, 13, "useNavigationHelpers"], [7, 53, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_routers"], [8, 14, 3, 0], [8, 17, 3, 0, "require"], [8, 24, 3, 0], [8, 25, 3, 0, "_dependencyMap"], [8, 39, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_NavigationContext"], [10, 24, 5, 0], [10, 27, 5, 0, "require"], [10, 34, 5, 0], [10, 35, 5, 0, "_dependencyMap"], [10, 49, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_types"], [11, 12, 6, 0], [11, 15, 6, 0, "require"], [11, 22, 6, 0], [11, 23, 6, 0, "_dependencyMap"], [11, 37, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_UnhandledActionContext"], [12, 29, 7, 0], [12, 32, 7, 0, "require"], [12, 39, 7, 0], [12, 40, 7, 0, "_dependencyMap"], [12, 54, 7, 0], [13, 2, 7, 69], [13, 11, 7, 69, "_interopRequireWildcard"], [13, 35, 7, 69, "e"], [13, 36, 7, 69], [13, 38, 7, 69, "t"], [13, 39, 7, 69], [13, 68, 7, 69, "WeakMap"], [13, 75, 7, 69], [13, 81, 7, 69, "r"], [13, 82, 7, 69], [13, 89, 7, 69, "WeakMap"], [13, 96, 7, 69], [13, 100, 7, 69, "n"], [13, 101, 7, 69], [13, 108, 7, 69, "WeakMap"], [13, 115, 7, 69], [13, 127, 7, 69, "_interopRequireWildcard"], [13, 150, 7, 69], [13, 162, 7, 69, "_interopRequireWildcard"], [13, 163, 7, 69, "e"], [13, 164, 7, 69], [13, 166, 7, 69, "t"], [13, 167, 7, 69], [13, 176, 7, 69, "t"], [13, 177, 7, 69], [13, 181, 7, 69, "e"], [13, 182, 7, 69], [13, 186, 7, 69, "e"], [13, 187, 7, 69], [13, 188, 7, 69, "__esModule"], [13, 198, 7, 69], [13, 207, 7, 69, "e"], [13, 208, 7, 69], [13, 214, 7, 69, "o"], [13, 215, 7, 69], [13, 217, 7, 69, "i"], [13, 218, 7, 69], [13, 220, 7, 69, "f"], [13, 221, 7, 69], [13, 226, 7, 69, "__proto__"], [13, 235, 7, 69], [13, 243, 7, 69, "default"], [13, 250, 7, 69], [13, 252, 7, 69, "e"], [13, 253, 7, 69], [13, 270, 7, 69, "e"], [13, 271, 7, 69], [13, 294, 7, 69, "e"], [13, 295, 7, 69], [13, 320, 7, 69, "e"], [13, 321, 7, 69], [13, 330, 7, 69, "f"], [13, 331, 7, 69], [13, 337, 7, 69, "o"], [13, 338, 7, 69], [13, 341, 7, 69, "t"], [13, 342, 7, 69], [13, 345, 7, 69, "n"], [13, 346, 7, 69], [13, 349, 7, 69, "r"], [13, 350, 7, 69], [13, 358, 7, 69, "o"], [13, 359, 7, 69], [13, 360, 7, 69, "has"], [13, 363, 7, 69], [13, 364, 7, 69, "e"], [13, 365, 7, 69], [13, 375, 7, 69, "o"], [13, 376, 7, 69], [13, 377, 7, 69, "get"], [13, 380, 7, 69], [13, 381, 7, 69, "e"], [13, 382, 7, 69], [13, 385, 7, 69, "o"], [13, 386, 7, 69], [13, 387, 7, 69, "set"], [13, 390, 7, 69], [13, 391, 7, 69, "e"], [13, 392, 7, 69], [13, 394, 7, 69, "f"], [13, 395, 7, 69], [13, 409, 7, 69, "_t"], [13, 411, 7, 69], [13, 415, 7, 69, "e"], [13, 416, 7, 69], [13, 432, 7, 69, "_t"], [13, 434, 7, 69], [13, 441, 7, 69, "hasOwnProperty"], [13, 455, 7, 69], [13, 456, 7, 69, "call"], [13, 460, 7, 69], [13, 461, 7, 69, "e"], [13, 462, 7, 69], [13, 464, 7, 69, "_t"], [13, 466, 7, 69], [13, 473, 7, 69, "i"], [13, 474, 7, 69], [13, 478, 7, 69, "o"], [13, 479, 7, 69], [13, 482, 7, 69, "Object"], [13, 488, 7, 69], [13, 489, 7, 69, "defineProperty"], [13, 503, 7, 69], [13, 508, 7, 69, "Object"], [13, 514, 7, 69], [13, 515, 7, 69, "getOwnPropertyDescriptor"], [13, 539, 7, 69], [13, 540, 7, 69, "e"], [13, 541, 7, 69], [13, 543, 7, 69, "_t"], [13, 545, 7, 69], [13, 552, 7, 69, "i"], [13, 553, 7, 69], [13, 554, 7, 69, "get"], [13, 557, 7, 69], [13, 561, 7, 69, "i"], [13, 562, 7, 69], [13, 563, 7, 69, "set"], [13, 566, 7, 69], [13, 570, 7, 69, "o"], [13, 571, 7, 69], [13, 572, 7, 69, "f"], [13, 573, 7, 69], [13, 575, 7, 69, "_t"], [13, 577, 7, 69], [13, 579, 7, 69, "i"], [13, 580, 7, 69], [13, 584, 7, 69, "f"], [13, 585, 7, 69], [13, 586, 7, 69, "_t"], [13, 588, 7, 69], [13, 592, 7, 69, "e"], [13, 593, 7, 69], [13, 594, 7, 69, "_t"], [13, 596, 7, 69], [13, 607, 7, 69, "f"], [13, 608, 7, 69], [13, 613, 7, 69, "e"], [13, 614, 7, 69], [13, 616, 7, 69, "t"], [13, 617, 7, 69], [14, 2, 8, 0], [15, 2, 9, 0], [16, 2, 10, 0, "PrivateValueStore"], [16, 26, 10, 17], [17, 2, 11, 0], [18, 0, 12, 0], [19, 0, 13, 0], [20, 0, 14, 0], [21, 2, 15, 7], [21, 11, 15, 16, "useNavigationHelpers"], [21, 31, 15, 36, "useNavigationHelpers"], [21, 32, 15, 36, "_ref"], [21, 36, 15, 36], [21, 38, 22, 3], [22, 4, 22, 3], [22, 8, 16, 6, "navigatorId"], [22, 19, 16, 17], [22, 22, 16, 17, "_ref"], [22, 26, 16, 17], [22, 27, 16, 2, "id"], [22, 29, 16, 4], [23, 6, 17, 2, "onAction"], [23, 14, 17, 10], [23, 17, 17, 10, "_ref"], [23, 21, 17, 10], [23, 22, 17, 2, "onAction"], [23, 30, 17, 10], [24, 6, 18, 2, "getState"], [24, 14, 18, 10], [24, 17, 18, 10, "_ref"], [24, 21, 18, 10], [24, 22, 18, 2, "getState"], [24, 30, 18, 10], [25, 6, 19, 2, "emitter"], [25, 13, 19, 9], [25, 16, 19, 9, "_ref"], [25, 20, 19, 9], [25, 21, 19, 2, "emitter"], [25, 28, 19, 9], [26, 6, 20, 2, "router"], [26, 12, 20, 8], [26, 15, 20, 8, "_ref"], [26, 19, 20, 8], [26, 20, 20, 2, "router"], [26, 26, 20, 8], [27, 6, 21, 2, "stateRef"], [27, 14, 21, 10], [27, 17, 21, 10, "_ref"], [27, 21, 21, 10], [27, 22, 21, 2, "stateRef"], [27, 30, 21, 10], [28, 4, 23, 2], [28, 8, 23, 8, "onUnhandledAction"], [28, 25, 23, 25], [28, 28, 23, 28, "React"], [28, 33, 23, 33], [28, 34, 23, 34, "useContext"], [28, 44, 23, 44], [28, 45, 23, 45, "UnhandledActionContext"], [28, 91, 23, 67], [28, 92, 23, 68], [29, 4, 24, 2], [29, 8, 24, 8, "parentNavigationHelpers"], [29, 31, 24, 31], [29, 34, 24, 34, "React"], [29, 39, 24, 39], [29, 40, 24, 40, "useContext"], [29, 50, 24, 50], [29, 51, 24, 51, "NavigationContext"], [29, 87, 24, 68], [29, 88, 24, 69], [30, 4, 25, 2], [30, 11, 25, 9, "React"], [30, 16, 25, 14], [30, 17, 25, 15, "useMemo"], [30, 24, 25, 22], [30, 25, 25, 23], [30, 31, 25, 29], [31, 6, 26, 4], [31, 10, 26, 10, "dispatch"], [31, 18, 26, 18], [31, 21, 26, 21, "op"], [31, 23, 26, 23], [31, 27, 26, 27], [32, 8, 27, 6], [32, 12, 27, 12, "action"], [32, 18, 27, 18], [32, 21, 27, 21], [32, 28, 27, 28, "op"], [32, 30, 27, 30], [32, 35, 27, 35], [32, 45, 27, 45], [32, 48, 27, 48, "op"], [32, 50, 27, 50], [32, 51, 27, 51, "getState"], [32, 59, 27, 59], [32, 60, 27, 60], [32, 61, 27, 61], [32, 62, 27, 62], [32, 65, 27, 65, "op"], [32, 67, 27, 67], [33, 8, 28, 6], [33, 12, 28, 12, "handled"], [33, 19, 28, 19], [33, 22, 28, 22, "onAction"], [33, 30, 28, 30], [33, 31, 28, 31, "action"], [33, 37, 28, 37], [33, 38, 28, 38], [34, 8, 29, 6], [34, 12, 29, 10], [34, 13, 29, 11, "handled"], [34, 20, 29, 18], [34, 22, 29, 20], [35, 10, 30, 8, "onUnhandledAction"], [35, 27, 30, 25], [35, 30, 30, 28, "action"], [35, 36, 30, 34], [35, 37, 30, 35], [36, 8, 31, 6], [37, 6, 32, 4], [37, 7, 32, 5], [38, 6, 33, 4], [38, 10, 33, 10, "actions"], [38, 17, 33, 17], [38, 20, 33, 20], [39, 8, 34, 6], [39, 11, 34, 9, "router"], [39, 17, 34, 15], [39, 18, 34, 16, "actionCreators"], [39, 32, 34, 30], [40, 8, 35, 6], [40, 11, 35, 9, "CommonActions"], [41, 6, 36, 4], [41, 7, 36, 5], [42, 6, 37, 4], [42, 10, 37, 10, "helpers"], [42, 17, 37, 17], [42, 20, 37, 20, "Object"], [42, 26, 37, 26], [42, 27, 37, 27, "keys"], [42, 31, 37, 31], [42, 32, 37, 32, "actions"], [42, 39, 37, 39], [42, 40, 37, 40], [42, 41, 37, 41, "reduce"], [42, 47, 37, 47], [42, 48, 37, 48], [42, 49, 37, 49, "acc"], [42, 52, 37, 52], [42, 54, 37, 54, "name"], [42, 58, 37, 58], [42, 63, 37, 63], [43, 8, 38, 6], [44, 8, 39, 6, "acc"], [44, 11, 39, 9], [44, 12, 39, 10, "name"], [44, 16, 39, 14], [44, 17, 39, 15], [44, 20, 39, 18], [45, 10, 39, 18], [45, 17, 39, 31, "dispatch"], [45, 25, 39, 39], [45, 26, 39, 40, "actions"], [45, 33, 39, 47], [45, 34, 39, 48, "name"], [45, 38, 39, 52], [45, 39, 39, 53], [45, 40, 39, 54], [45, 43, 39, 54, "arguments"], [45, 52, 39, 61], [45, 53, 39, 62], [45, 54, 39, 63], [46, 8, 39, 63], [47, 8, 40, 6], [47, 15, 40, 13, "acc"], [47, 18, 40, 16], [48, 6, 41, 4], [48, 7, 41, 5], [48, 9, 41, 7], [48, 10, 41, 8], [48, 11, 41, 9], [48, 12, 41, 10], [49, 6, 42, 4], [49, 10, 42, 10, "navigationHelpers"], [49, 27, 42, 27], [49, 30, 42, 30], [50, 8, 43, 6], [50, 11, 43, 9, "parentNavigationHelpers"], [50, 34, 43, 32], [51, 8, 44, 6], [51, 11, 44, 9, "helpers"], [51, 18, 44, 16], [52, 8, 45, 6, "dispatch"], [52, 16, 45, 14], [53, 8, 46, 6, "emit"], [53, 12, 46, 10], [53, 14, 46, 12, "emitter"], [53, 21, 46, 19], [53, 22, 46, 20, "emit"], [53, 26, 46, 24], [54, 8, 47, 6, "isFocused"], [54, 17, 47, 15], [54, 19, 47, 17, "parentNavigationHelpers"], [54, 42, 47, 40], [54, 45, 47, 43, "parentNavigationHelpers"], [54, 68, 47, 66], [54, 69, 47, 67, "isFocused"], [54, 78, 47, 76], [54, 81, 47, 79], [54, 87, 47, 85], [54, 91, 47, 89], [55, 8, 48, 6, "canGoBack"], [55, 17, 48, 15], [55, 19, 48, 17, "canGoBack"], [55, 20, 48, 17], [55, 25, 48, 23], [56, 10, 49, 8], [56, 14, 49, 14, "state"], [56, 19, 49, 19], [56, 22, 49, 22, "getState"], [56, 30, 49, 30], [56, 31, 49, 31], [56, 32, 49, 32], [57, 10, 50, 8], [57, 17, 50, 15, "router"], [57, 23, 50, 21], [57, 24, 50, 22, "getStateForAction"], [57, 41, 50, 39], [57, 42, 50, 40, "state"], [57, 47, 50, 45], [57, 49, 50, 47, "CommonActions"], [57, 71, 50, 60], [57, 72, 50, 61, "goBack"], [57, 78, 50, 67], [57, 79, 50, 68], [57, 80, 50, 69], [57, 82, 50, 71], [58, 12, 51, 10, "routeNames"], [58, 22, 51, 20], [58, 24, 51, 22, "state"], [58, 29, 51, 27], [58, 30, 51, 28, "routeNames"], [58, 40, 51, 38], [59, 12, 52, 10, "routeParamList"], [59, 26, 52, 24], [59, 28, 52, 26], [59, 29, 52, 27], [59, 30, 52, 28], [60, 12, 53, 10, "routeGetIdList"], [60, 26, 53, 24], [60, 28, 53, 26], [60, 29, 53, 27], [61, 10, 54, 8], [61, 11, 54, 9], [61, 12, 54, 10], [61, 17, 54, 15], [61, 21, 54, 19], [61, 25, 54, 23, "parentNavigationHelpers"], [61, 48, 54, 46], [61, 50, 54, 48, "canGoBack"], [61, 59, 54, 57], [61, 60, 54, 58], [61, 61, 54, 59], [61, 65, 54, 63], [61, 70, 54, 68], [62, 8, 55, 6], [62, 9, 55, 7], [63, 8, 56, 6, "getId"], [63, 13, 56, 11], [63, 15, 56, 13, "getId"], [63, 16, 56, 13], [63, 21, 56, 19, "navigatorId"], [63, 32, 56, 30], [64, 8, 57, 6, "getParent"], [64, 17, 57, 15], [64, 19, 57, 17, "id"], [64, 21, 57, 19], [64, 25, 57, 23], [65, 10, 58, 8], [65, 14, 58, 12, "id"], [65, 16, 58, 14], [65, 21, 58, 19, "undefined"], [65, 30, 58, 28], [65, 32, 58, 30], [66, 12, 59, 10], [66, 16, 59, 14, "current"], [66, 23, 59, 21], [66, 26, 59, 24, "navigationHelpers"], [66, 43, 59, 41], [67, 12, 60, 10], [67, 19, 60, 17, "current"], [67, 26, 60, 24], [67, 30, 60, 28, "id"], [67, 32, 60, 30], [67, 37, 60, 35, "current"], [67, 44, 60, 42], [67, 45, 60, 43, "getId"], [67, 50, 60, 48], [67, 51, 60, 49], [67, 52, 60, 50], [67, 54, 60, 52], [68, 14, 61, 12, "current"], [68, 21, 61, 19], [68, 24, 61, 22, "current"], [68, 31, 61, 29], [68, 32, 61, 30, "getParent"], [68, 41, 61, 39], [68, 42, 61, 40], [68, 43, 61, 41], [69, 12, 62, 10], [70, 12, 63, 10], [70, 19, 63, 17, "current"], [70, 26, 63, 24], [71, 10, 64, 8], [72, 10, 65, 8], [72, 17, 65, 15, "parentNavigationHelpers"], [72, 40, 65, 38], [73, 8, 66, 6], [73, 9, 66, 7], [74, 8, 67, 6, "getState"], [74, 16, 67, 14], [74, 18, 67, 16, "getState"], [74, 19, 67, 16], [74, 24, 67, 22], [75, 10, 68, 8], [76, 10, 69, 8], [77, 10, 70, 8], [78, 10, 71, 8], [79, 10, 72, 8], [80, 10, 73, 8], [80, 14, 73, 12, "stateRef"], [80, 22, 73, 20], [80, 23, 73, 21, "current"], [80, 30, 73, 28], [80, 34, 73, 32], [80, 38, 73, 36], [80, 40, 73, 38], [81, 12, 74, 10], [81, 19, 74, 17, "stateRef"], [81, 27, 74, 25], [81, 28, 74, 26, "current"], [81, 35, 74, 33], [82, 10, 75, 8], [83, 10, 76, 8], [83, 17, 76, 15, "getState"], [83, 25, 76, 23], [83, 26, 76, 24], [83, 27, 76, 25], [84, 8, 77, 6], [85, 6, 78, 4], [85, 7, 78, 5], [86, 6, 79, 4], [86, 13, 79, 11, "navigationHelpers"], [86, 30, 79, 28], [87, 4, 80, 2], [87, 5, 80, 3], [87, 7, 80, 5], [87, 8, 80, 6, "router"], [87, 14, 80, 12], [87, 16, 80, 14, "parentNavigationHelpers"], [87, 39, 80, 37], [87, 41, 80, 39, "emitter"], [87, 48, 80, 46], [87, 49, 80, 47, "emit"], [87, 53, 80, 51], [87, 55, 80, 53, "getState"], [87, 63, 80, 61], [87, 65, 80, 63, "onAction"], [87, 73, 80, 71], [87, 75, 80, 73, "onUnhandledAction"], [87, 92, 80, 90], [87, 94, 80, 92, "navigatorId"], [87, 105, 80, 103], [87, 107, 80, 105, "stateRef"], [87, 115, 80, 113], [87, 116, 80, 114], [87, 117, 80, 115], [88, 2, 81, 0], [89, 0, 81, 1], [89, 3]], "functionMap": {"names": ["<global>", "useNavigationHelpers", "React.useMemo$argument_0", "dispatch", "Object.keys.reduce$argument_0", "acc.name", "<anonymous>", "navigationHelpers.canGoBack", "navigationHelpers.getId", "navigationHelpers.getParent", "navigationHelpers.getState"], "mappings": "AAA;OCc;uBCU;qBCC;KDM;gDEK;kBCE,6CD;KFE;+EIM,UJ;iBKC;OLO;aMC,iBN;iBOC;OPS;gBQC;ORU;GDG;CDC"}}, "type": "js/module"}]}