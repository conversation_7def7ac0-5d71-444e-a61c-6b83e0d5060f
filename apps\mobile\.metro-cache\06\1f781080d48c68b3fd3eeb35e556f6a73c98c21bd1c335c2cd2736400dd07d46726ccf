{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-gesture-handler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 92, "index": 139}}], "key": "HJh61WuEUBv/Tn7fS9MiHoQcOfk=", "exportNames": ["*"]}}, {"name": "../utils/GestureHandlerRefContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 140}, "end": {"line": 5, "column": 80, "index": 220}}], "key": "kW3GIrr8T6TBGJdRuNotcAIVgdU=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 221}, "end": {"line": 6, "column": 48, "index": 269}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"GestureHandlerRootView\", {\n    enumerable: true,\n    get: function () {\n      return _reactNativeGestureHandler.GestureHandlerRootView;\n    }\n  });\n  Object.defineProperty(exports, \"GestureState\", {\n    enumerable: true,\n    get: function () {\n      return _reactNativeGestureHandler.State;\n    }\n  });\n  exports.PanGestureHandler = PanGestureHandler;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _reactNativeGestureHandler = require(_dependencyMap[1], \"react-native-gesture-handler\");\n  var _GestureHandlerRefContext = require(_dependencyMap[2], \"../utils/GestureHandlerRefContext.js\");\n  var _jsxRuntime = require(_dependencyMap[3], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function PanGestureHandler(props) {\n    var gestureRef = React.useRef(null);\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_GestureHandlerRefContext.GestureHandlerRefContext.Provider, {\n      value: gestureRef,\n      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeGestureHandler.PanGestureHandler, {\n        ...props,\n        ref: gestureRef\n      })\n    });\n  }\n});", "lineCount": 35, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "Object"], [7, 8, 1, 13], [7, 9, 1, 13, "defineProperty"], [7, 23, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [8, 4, 1, 13, "enumerable"], [8, 14, 1, 13], [9, 4, 1, 13, "get"], [9, 7, 1, 13], [9, 18, 1, 13, "get"], [9, 19, 1, 13], [10, 6, 1, 13], [10, 13, 1, 13, "_reactNativeGestureHandler"], [10, 39, 1, 13], [10, 40, 1, 13, "GestureHandlerRootView"], [10, 62, 1, 13], [11, 4, 1, 13], [12, 2, 1, 13], [13, 2, 1, 13, "Object"], [13, 8, 1, 13], [13, 9, 1, 13, "defineProperty"], [13, 23, 1, 13], [13, 24, 1, 13, "exports"], [13, 31, 1, 13], [14, 4, 1, 13, "enumerable"], [14, 14, 1, 13], [15, 4, 1, 13, "get"], [15, 7, 1, 13], [15, 18, 1, 13, "get"], [15, 19, 1, 13], [16, 6, 1, 13], [16, 13, 1, 13, "_reactNativeGestureHandler"], [16, 39, 1, 13], [16, 40, 1, 13, "State"], [16, 45, 1, 13], [17, 4, 1, 13], [18, 2, 1, 13], [19, 2, 1, 13, "exports"], [19, 9, 1, 13], [19, 10, 1, 13, "PanGestureHandler"], [19, 27, 1, 13], [19, 30, 1, 13, "PanGestureHandler"], [19, 47, 1, 13], [20, 2, 3, 0], [20, 6, 3, 0, "React"], [20, 11, 3, 0], [20, 14, 3, 0, "_interopRequireWildcard"], [20, 37, 3, 0], [20, 38, 3, 0, "require"], [20, 45, 3, 0], [20, 46, 3, 0, "_dependencyMap"], [20, 60, 3, 0], [21, 2, 4, 0], [21, 6, 4, 0, "_reactNativeGestureHandler"], [21, 32, 4, 0], [21, 35, 4, 0, "require"], [21, 42, 4, 0], [21, 43, 4, 0, "_dependencyMap"], [21, 57, 4, 0], [22, 2, 5, 0], [22, 6, 5, 0, "_GestureHandlerRefContext"], [22, 31, 5, 0], [22, 34, 5, 0, "require"], [22, 41, 5, 0], [22, 42, 5, 0, "_dependencyMap"], [22, 56, 5, 0], [23, 2, 6, 0], [23, 6, 6, 0, "_jsxRuntime"], [23, 17, 6, 0], [23, 20, 6, 0, "require"], [23, 27, 6, 0], [23, 28, 6, 0, "_dependencyMap"], [23, 42, 6, 0], [24, 2, 6, 48], [24, 11, 6, 48, "_interopRequireWildcard"], [24, 35, 6, 48, "e"], [24, 36, 6, 48], [24, 38, 6, 48, "t"], [24, 39, 6, 48], [24, 68, 6, 48, "WeakMap"], [24, 75, 6, 48], [24, 81, 6, 48, "r"], [24, 82, 6, 48], [24, 89, 6, 48, "WeakMap"], [24, 96, 6, 48], [24, 100, 6, 48, "n"], [24, 101, 6, 48], [24, 108, 6, 48, "WeakMap"], [24, 115, 6, 48], [24, 127, 6, 48, "_interopRequireWildcard"], [24, 150, 6, 48], [24, 162, 6, 48, "_interopRequireWildcard"], [24, 163, 6, 48, "e"], [24, 164, 6, 48], [24, 166, 6, 48, "t"], [24, 167, 6, 48], [24, 176, 6, 48, "t"], [24, 177, 6, 48], [24, 181, 6, 48, "e"], [24, 182, 6, 48], [24, 186, 6, 48, "e"], [24, 187, 6, 48], [24, 188, 6, 48, "__esModule"], [24, 198, 6, 48], [24, 207, 6, 48, "e"], [24, 208, 6, 48], [24, 214, 6, 48, "o"], [24, 215, 6, 48], [24, 217, 6, 48, "i"], [24, 218, 6, 48], [24, 220, 6, 48, "f"], [24, 221, 6, 48], [24, 226, 6, 48, "__proto__"], [24, 235, 6, 48], [24, 243, 6, 48, "default"], [24, 250, 6, 48], [24, 252, 6, 48, "e"], [24, 253, 6, 48], [24, 270, 6, 48, "e"], [24, 271, 6, 48], [24, 294, 6, 48, "e"], [24, 295, 6, 48], [24, 320, 6, 48, "e"], [24, 321, 6, 48], [24, 330, 6, 48, "f"], [24, 331, 6, 48], [24, 337, 6, 48, "o"], [24, 338, 6, 48], [24, 341, 6, 48, "t"], [24, 342, 6, 48], [24, 345, 6, 48, "n"], [24, 346, 6, 48], [24, 349, 6, 48, "r"], [24, 350, 6, 48], [24, 358, 6, 48, "o"], [24, 359, 6, 48], [24, 360, 6, 48, "has"], [24, 363, 6, 48], [24, 364, 6, 48, "e"], [24, 365, 6, 48], [24, 375, 6, 48, "o"], [24, 376, 6, 48], [24, 377, 6, 48, "get"], [24, 380, 6, 48], [24, 381, 6, 48, "e"], [24, 382, 6, 48], [24, 385, 6, 48, "o"], [24, 386, 6, 48], [24, 387, 6, 48, "set"], [24, 390, 6, 48], [24, 391, 6, 48, "e"], [24, 392, 6, 48], [24, 394, 6, 48, "f"], [24, 395, 6, 48], [24, 409, 6, 48, "_t"], [24, 411, 6, 48], [24, 415, 6, 48, "e"], [24, 416, 6, 48], [24, 432, 6, 48, "_t"], [24, 434, 6, 48], [24, 441, 6, 48, "hasOwnProperty"], [24, 455, 6, 48], [24, 456, 6, 48, "call"], [24, 460, 6, 48], [24, 461, 6, 48, "e"], [24, 462, 6, 48], [24, 464, 6, 48, "_t"], [24, 466, 6, 48], [24, 473, 6, 48, "i"], [24, 474, 6, 48], [24, 478, 6, 48, "o"], [24, 479, 6, 48], [24, 482, 6, 48, "Object"], [24, 488, 6, 48], [24, 489, 6, 48, "defineProperty"], [24, 503, 6, 48], [24, 508, 6, 48, "Object"], [24, 514, 6, 48], [24, 515, 6, 48, "getOwnPropertyDescriptor"], [24, 539, 6, 48], [24, 540, 6, 48, "e"], [24, 541, 6, 48], [24, 543, 6, 48, "_t"], [24, 545, 6, 48], [24, 552, 6, 48, "i"], [24, 553, 6, 48], [24, 554, 6, 48, "get"], [24, 557, 6, 48], [24, 561, 6, 48, "i"], [24, 562, 6, 48], [24, 563, 6, 48, "set"], [24, 566, 6, 48], [24, 570, 6, 48, "o"], [24, 571, 6, 48], [24, 572, 6, 48, "f"], [24, 573, 6, 48], [24, 575, 6, 48, "_t"], [24, 577, 6, 48], [24, 579, 6, 48, "i"], [24, 580, 6, 48], [24, 584, 6, 48, "f"], [24, 585, 6, 48], [24, 586, 6, 48, "_t"], [24, 588, 6, 48], [24, 592, 6, 48, "e"], [24, 593, 6, 48], [24, 594, 6, 48, "_t"], [24, 596, 6, 48], [24, 607, 6, 48, "f"], [24, 608, 6, 48], [24, 613, 6, 48, "e"], [24, 614, 6, 48], [24, 616, 6, 48, "t"], [24, 617, 6, 48], [25, 2, 7, 7], [25, 11, 7, 16, "PanGestureHandler"], [25, 28, 7, 33, "PanGestureHandler"], [25, 29, 7, 34, "props"], [25, 34, 7, 39], [25, 36, 7, 41], [26, 4, 8, 2], [26, 8, 8, 8, "gestureRef"], [26, 18, 8, 18], [26, 21, 8, 21, "React"], [26, 26, 8, 26], [26, 27, 8, 27, "useRef"], [26, 33, 8, 33], [26, 34, 8, 34], [26, 38, 8, 38], [26, 39, 8, 39], [27, 4, 9, 2], [27, 11, 9, 9], [27, 24, 9, 22], [27, 28, 9, 22, "_jsx"], [27, 43, 9, 26], [27, 45, 9, 27, "GestureHandlerRefContext"], [27, 95, 9, 51], [27, 96, 9, 52, "Provider"], [27, 104, 9, 60], [27, 106, 9, 62], [28, 6, 10, 4, "value"], [28, 11, 10, 9], [28, 13, 10, 11, "gestureRef"], [28, 23, 10, 21], [29, 6, 11, 4, "children"], [29, 14, 11, 12], [29, 16, 11, 14], [29, 29, 11, 27], [29, 33, 11, 27, "_jsx"], [29, 48, 11, 31], [29, 50, 11, 32, "PanGestureHandlerNative"], [29, 94, 11, 55], [29, 96, 11, 57], [30, 8, 12, 6], [30, 11, 12, 9, "props"], [30, 16, 12, 14], [31, 8, 13, 6, "ref"], [31, 11, 13, 9], [31, 13, 13, 11, "gestureRef"], [32, 6, 14, 4], [32, 7, 14, 5], [33, 4, 15, 2], [33, 5, 15, 3], [33, 6, 15, 4], [34, 2, 16, 0], [35, 0, 16, 1], [35, 3]], "functionMap": {"names": ["<global>", "PanGestureHandler"], "mappings": "AAA;OCM;CDS"}}, "type": "js/module"}]}