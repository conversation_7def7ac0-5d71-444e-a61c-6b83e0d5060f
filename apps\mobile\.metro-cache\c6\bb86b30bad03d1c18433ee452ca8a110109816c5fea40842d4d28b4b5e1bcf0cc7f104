{"dependencies": [{"name": "@react-navigation/elements", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 79, "index": 94}}], "key": "LmqW7jh+SpCzQZMkzh+Awcuawt0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 95}, "end": {"line": 4, "column": 101, "index": 196}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 197}, "end": {"line": 5, "column": 31, "index": 228}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 229}, "end": {"line": 6, "column": 58, "index": 287}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../../TransitionConfigs/HeaderStyleInterpolators.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 288}, "end": {"line": 7, "column": 126, "index": 414}}], "key": "dhCferIywPppJlbwacI0ej912S8=", "exportNames": ["*"]}}, {"name": "./Header.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 415}, "end": {"line": 8, "column": 37, "index": 452}}], "key": "tqEIxJqFQE8dJEQX6NpDtmaBjwE=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 453}, "end": {"line": 9, "column": 48, "index": 501}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderContainer = HeaderContainer;\n  var _elements = require(_dependencyMap[0], \"@react-navigation/elements\");\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _HeaderStyleInterpolators = require(_dependencyMap[4], \"../../TransitionConfigs/HeaderStyleInterpolators.js\");\n  var _Header = require(_dependencyMap[5], \"./Header.js\");\n  var _jsxRuntime = require(_dependencyMap[6], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function HeaderContainer(_ref) {\n    var mode = _ref.mode,\n      scenes = _ref.scenes,\n      layout = _ref.layout,\n      getPreviousScene = _ref.getPreviousScene,\n      getFocusedRoute = _ref.getFocusedRoute,\n      onContentHeightChange = _ref.onContentHeightChange,\n      style = _ref.style;\n    var focusedRoute = getFocusedRoute();\n    var parentHeaderBack = React.useContext(_elements.HeaderBackContext);\n    var _useLinkBuilder = (0, _native.useLinkBuilder)(),\n      buildHref = _useLinkBuilder.buildHref;\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {\n      pointerEvents: \"box-none\",\n      style: style,\n      children: scenes.slice(-3).map((scene, i, self) => {\n        if (mode === 'screen' && i !== self.length - 1 || !scene) {\n          return null;\n        }\n        var _scene$descriptor$opt = scene.descriptor.options,\n          header = _scene$descriptor$opt.header,\n          headerMode = _scene$descriptor$opt.headerMode,\n          _scene$descriptor$opt2 = _scene$descriptor$opt.headerShown,\n          headerShown = _scene$descriptor$opt2 === void 0 ? true : _scene$descriptor$opt2,\n          headerTransparent = _scene$descriptor$opt.headerTransparent,\n          headerStyleInterpolator = _scene$descriptor$opt.headerStyleInterpolator;\n        if (headerMode !== mode || !headerShown) {\n          return null;\n        }\n        var isFocused = focusedRoute.key === scene.descriptor.route.key;\n        var previousScene = getPreviousScene({\n          route: scene.descriptor.route\n        });\n        var headerBack = parentHeaderBack;\n        if (previousScene) {\n          var _previousScene$descri = previousScene.descriptor,\n            options = _previousScene$descri.options,\n            route = _previousScene$descri.route;\n          headerBack = previousScene ? {\n            title: (0, _elements.getHeaderTitle)(options, route.name),\n            href: buildHref(route.name, route.params)\n          } : parentHeaderBack;\n        }\n\n        // If the screen is next to a headerless screen, we need to make the header appear static\n        // This makes the header look like it's moving with the screen\n        var previousDescriptor = self[i - 1]?.descriptor;\n        var nextDescriptor = self[i + 1]?.descriptor;\n        var _ref2 = previousDescriptor?.options || {},\n          _ref2$headerShown = _ref2.headerShown,\n          previousHeaderShown = _ref2$headerShown === void 0 ? true : _ref2$headerShown,\n          previousHeaderMode = _ref2.headerMode;\n\n        // If any of the next screens don't have a header or header is part of the screen\n        // Then we need to move this header offscreen so that it doesn't cover it\n        var nextHeaderlessScene = self.slice(i + 1).find(scene => {\n          var _ref3 = scene?.descriptor.options || {},\n            _ref3$headerShown = _ref3.headerShown,\n            currentHeaderShown = _ref3$headerShown === void 0 ? true : _ref3$headerShown,\n            currentHeaderMode = _ref3.headerMode;\n          return currentHeaderShown === false || currentHeaderMode === 'screen';\n        });\n        var _ref4 = nextHeaderlessScene?.descriptor.options || {},\n          nextHeaderlessGestureDirection = _ref4.gestureDirection;\n        var isHeaderStatic = (previousHeaderShown === false || previousHeaderMode === 'screen') &&\n        // We still need to animate when coming back from next scene\n        // A hacky way to check this is if the next scene exists\n        !nextDescriptor || nextHeaderlessScene;\n        var props = {\n          layout,\n          back: headerBack,\n          progress: scene.progress,\n          options: scene.descriptor.options,\n          route: scene.descriptor.route,\n          navigation: scene.descriptor.navigation,\n          styleInterpolator: mode === 'float' ? isHeaderStatic ? nextHeaderlessGestureDirection === 'vertical' || nextHeaderlessGestureDirection === 'vertical-inverted' ? _HeaderStyleInterpolators.forSlideUp : nextHeaderlessGestureDirection === 'horizontal-inverted' ? _HeaderStyleInterpolators.forSlideRight : _HeaderStyleInterpolators.forSlideLeft : headerStyleInterpolator : _HeaderStyleInterpolators.forNoAnimation\n        };\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_native.NavigationContext.Provider, {\n          value: scene.descriptor.navigation,\n          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_native.NavigationRouteContext.Provider, {\n            value: scene.descriptor.route,\n            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n              onLayout: onContentHeightChange ? e => {\n                var height = e.nativeEvent.layout.height;\n                onContentHeightChange({\n                  route: scene.descriptor.route,\n                  height\n                });\n              } : undefined,\n              pointerEvents: isFocused ? 'box-none' : 'none',\n              \"aria-hidden\": !isFocused,\n              style:\n              // Avoid positioning the focused header absolutely\n              // Otherwise accessibility tools don't seem to be able to find it\n              mode === 'float' && !isFocused || headerTransparent ? styles.header : null,\n              children: header !== undefined ? header(props) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_Header.Header, {\n                ...props\n              })\n            })\n          })\n        }, scene.descriptor.route.key);\n      })\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    header: {\n      position: 'absolute',\n      top: 0,\n      start: 0,\n      end: 0\n    }\n  });\n});", "lineCount": 128, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [7, 25, 1, 13], [7, 28, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [7, 43, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_elements"], [8, 15, 3, 0], [8, 18, 3, 0, "require"], [8, 25, 3, 0], [8, 26, 3, 0, "_dependencyMap"], [8, 40, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_native"], [9, 13, 4, 0], [9, 16, 4, 0, "require"], [9, 23, 4, 0], [9, 24, 4, 0, "_dependencyMap"], [9, 38, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "React"], [10, 11, 5, 0], [10, 14, 5, 0, "_interopRequireWildcard"], [10, 37, 5, 0], [10, 38, 5, 0, "require"], [10, 45, 5, 0], [10, 46, 5, 0, "_dependencyMap"], [10, 60, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_reactNative"], [11, 18, 6, 0], [11, 21, 6, 0, "require"], [11, 28, 6, 0], [11, 29, 6, 0, "_dependencyMap"], [11, 43, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_HeaderStyleInterpolators"], [12, 31, 7, 0], [12, 34, 7, 0, "require"], [12, 41, 7, 0], [12, 42, 7, 0, "_dependencyMap"], [12, 56, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_Header"], [13, 13, 8, 0], [13, 16, 8, 0, "require"], [13, 23, 8, 0], [13, 24, 8, 0, "_dependencyMap"], [13, 38, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_jsxRuntime"], [14, 17, 9, 0], [14, 20, 9, 0, "require"], [14, 27, 9, 0], [14, 28, 9, 0, "_dependencyMap"], [14, 42, 9, 0], [15, 2, 9, 48], [15, 11, 9, 48, "_interopRequireWildcard"], [15, 35, 9, 48, "e"], [15, 36, 9, 48], [15, 38, 9, 48, "t"], [15, 39, 9, 48], [15, 68, 9, 48, "WeakMap"], [15, 75, 9, 48], [15, 81, 9, 48, "r"], [15, 82, 9, 48], [15, 89, 9, 48, "WeakMap"], [15, 96, 9, 48], [15, 100, 9, 48, "n"], [15, 101, 9, 48], [15, 108, 9, 48, "WeakMap"], [15, 115, 9, 48], [15, 127, 9, 48, "_interopRequireWildcard"], [15, 150, 9, 48], [15, 162, 9, 48, "_interopRequireWildcard"], [15, 163, 9, 48, "e"], [15, 164, 9, 48], [15, 166, 9, 48, "t"], [15, 167, 9, 48], [15, 176, 9, 48, "t"], [15, 177, 9, 48], [15, 181, 9, 48, "e"], [15, 182, 9, 48], [15, 186, 9, 48, "e"], [15, 187, 9, 48], [15, 188, 9, 48, "__esModule"], [15, 198, 9, 48], [15, 207, 9, 48, "e"], [15, 208, 9, 48], [15, 214, 9, 48, "o"], [15, 215, 9, 48], [15, 217, 9, 48, "i"], [15, 218, 9, 48], [15, 220, 9, 48, "f"], [15, 221, 9, 48], [15, 226, 9, 48, "__proto__"], [15, 235, 9, 48], [15, 243, 9, 48, "default"], [15, 250, 9, 48], [15, 252, 9, 48, "e"], [15, 253, 9, 48], [15, 270, 9, 48, "e"], [15, 271, 9, 48], [15, 294, 9, 48, "e"], [15, 295, 9, 48], [15, 320, 9, 48, "e"], [15, 321, 9, 48], [15, 330, 9, 48, "f"], [15, 331, 9, 48], [15, 337, 9, 48, "o"], [15, 338, 9, 48], [15, 341, 9, 48, "t"], [15, 342, 9, 48], [15, 345, 9, 48, "n"], [15, 346, 9, 48], [15, 349, 9, 48, "r"], [15, 350, 9, 48], [15, 358, 9, 48, "o"], [15, 359, 9, 48], [15, 360, 9, 48, "has"], [15, 363, 9, 48], [15, 364, 9, 48, "e"], [15, 365, 9, 48], [15, 375, 9, 48, "o"], [15, 376, 9, 48], [15, 377, 9, 48, "get"], [15, 380, 9, 48], [15, 381, 9, 48, "e"], [15, 382, 9, 48], [15, 385, 9, 48, "o"], [15, 386, 9, 48], [15, 387, 9, 48, "set"], [15, 390, 9, 48], [15, 391, 9, 48, "e"], [15, 392, 9, 48], [15, 394, 9, 48, "f"], [15, 395, 9, 48], [15, 409, 9, 48, "_t"], [15, 411, 9, 48], [15, 415, 9, 48, "e"], [15, 416, 9, 48], [15, 432, 9, 48, "_t"], [15, 434, 9, 48], [15, 441, 9, 48, "hasOwnProperty"], [15, 455, 9, 48], [15, 456, 9, 48, "call"], [15, 460, 9, 48], [15, 461, 9, 48, "e"], [15, 462, 9, 48], [15, 464, 9, 48, "_t"], [15, 466, 9, 48], [15, 473, 9, 48, "i"], [15, 474, 9, 48], [15, 478, 9, 48, "o"], [15, 479, 9, 48], [15, 482, 9, 48, "Object"], [15, 488, 9, 48], [15, 489, 9, 48, "defineProperty"], [15, 503, 9, 48], [15, 508, 9, 48, "Object"], [15, 514, 9, 48], [15, 515, 9, 48, "getOwnPropertyDescriptor"], [15, 539, 9, 48], [15, 540, 9, 48, "e"], [15, 541, 9, 48], [15, 543, 9, 48, "_t"], [15, 545, 9, 48], [15, 552, 9, 48, "i"], [15, 553, 9, 48], [15, 554, 9, 48, "get"], [15, 557, 9, 48], [15, 561, 9, 48, "i"], [15, 562, 9, 48], [15, 563, 9, 48, "set"], [15, 566, 9, 48], [15, 570, 9, 48, "o"], [15, 571, 9, 48], [15, 572, 9, 48, "f"], [15, 573, 9, 48], [15, 575, 9, 48, "_t"], [15, 577, 9, 48], [15, 579, 9, 48, "i"], [15, 580, 9, 48], [15, 584, 9, 48, "f"], [15, 585, 9, 48], [15, 586, 9, 48, "_t"], [15, 588, 9, 48], [15, 592, 9, 48, "e"], [15, 593, 9, 48], [15, 594, 9, 48, "_t"], [15, 596, 9, 48], [15, 607, 9, 48, "f"], [15, 608, 9, 48], [15, 613, 9, 48, "e"], [15, 614, 9, 48], [15, 616, 9, 48, "t"], [15, 617, 9, 48], [16, 2, 10, 7], [16, 11, 10, 16, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 26, 10, 31, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 27, 10, 31, "_ref"], [16, 31, 10, 31], [16, 33, 18, 3], [17, 4, 18, 3], [17, 8, 11, 2, "mode"], [17, 12, 11, 6], [17, 15, 11, 6, "_ref"], [17, 19, 11, 6], [17, 20, 11, 2, "mode"], [17, 24, 11, 6], [18, 6, 12, 2, "scenes"], [18, 12, 12, 8], [18, 15, 12, 8, "_ref"], [18, 19, 12, 8], [18, 20, 12, 2, "scenes"], [18, 26, 12, 8], [19, 6, 13, 2, "layout"], [19, 12, 13, 8], [19, 15, 13, 8, "_ref"], [19, 19, 13, 8], [19, 20, 13, 2, "layout"], [19, 26, 13, 8], [20, 6, 14, 2, "getPreviousScene"], [20, 22, 14, 18], [20, 25, 14, 18, "_ref"], [20, 29, 14, 18], [20, 30, 14, 2, "getPreviousScene"], [20, 46, 14, 18], [21, 6, 15, 2, "getFocusedRoute"], [21, 21, 15, 17], [21, 24, 15, 17, "_ref"], [21, 28, 15, 17], [21, 29, 15, 2, "getFocusedRoute"], [21, 44, 15, 17], [22, 6, 16, 2, "onContentHeightChange"], [22, 27, 16, 23], [22, 30, 16, 23, "_ref"], [22, 34, 16, 23], [22, 35, 16, 2, "onContentHeightChange"], [22, 56, 16, 23], [23, 6, 17, 2, "style"], [23, 11, 17, 7], [23, 14, 17, 7, "_ref"], [23, 18, 17, 7], [23, 19, 17, 2, "style"], [23, 24, 17, 7], [24, 4, 19, 2], [24, 8, 19, 8, "focusedRoute"], [24, 20, 19, 20], [24, 23, 19, 23, "getFocusedRoute"], [24, 38, 19, 38], [24, 39, 19, 39], [24, 40, 19, 40], [25, 4, 20, 2], [25, 8, 20, 8, "parentHeaderBack"], [25, 24, 20, 24], [25, 27, 20, 27, "React"], [25, 32, 20, 32], [25, 33, 20, 33, "useContext"], [25, 43, 20, 43], [25, 44, 20, 44, "HeaderBackContext"], [25, 71, 20, 61], [25, 72, 20, 62], [26, 4, 21, 2], [26, 8, 21, 2, "_useLinkBuilder"], [26, 23, 21, 2], [26, 26, 23, 6], [26, 30, 23, 6, "useLinkBuilder"], [26, 52, 23, 20], [26, 54, 23, 21], [26, 55, 23, 22], [27, 6, 22, 4, "buildHref"], [27, 15, 22, 13], [27, 18, 22, 13, "_useLinkBuilder"], [27, 33, 22, 13], [27, 34, 22, 4, "buildHref"], [27, 43, 22, 13], [28, 4, 24, 2], [28, 11, 24, 9], [28, 24, 24, 22], [28, 28, 24, 22, "_jsx"], [28, 43, 24, 26], [28, 45, 24, 27, "Animated"], [28, 66, 24, 35], [28, 67, 24, 36, "View"], [28, 71, 24, 40], [28, 73, 24, 42], [29, 6, 25, 4, "pointerEvents"], [29, 19, 25, 17], [29, 21, 25, 19], [29, 31, 25, 29], [30, 6, 26, 4, "style"], [30, 11, 26, 9], [30, 13, 26, 11, "style"], [30, 18, 26, 16], [31, 6, 27, 4, "children"], [31, 14, 27, 12], [31, 16, 27, 14, "scenes"], [31, 22, 27, 20], [31, 23, 27, 21, "slice"], [31, 28, 27, 26], [31, 29, 27, 27], [31, 30, 27, 28], [31, 31, 27, 29], [31, 32, 27, 30], [31, 33, 27, 31, "map"], [31, 36, 27, 34], [31, 37, 27, 35], [31, 38, 27, 36, "scene"], [31, 43, 27, 41], [31, 45, 27, 43, "i"], [31, 46, 27, 44], [31, 48, 27, 46, "self"], [31, 52, 27, 50], [31, 57, 27, 55], [32, 8, 28, 6], [32, 12, 28, 10, "mode"], [32, 16, 28, 14], [32, 21, 28, 19], [32, 29, 28, 27], [32, 33, 28, 31, "i"], [32, 34, 28, 32], [32, 39, 28, 37, "self"], [32, 43, 28, 41], [32, 44, 28, 42, "length"], [32, 50, 28, 48], [32, 53, 28, 51], [32, 54, 28, 52], [32, 58, 28, 56], [32, 59, 28, 57, "scene"], [32, 64, 28, 62], [32, 66, 28, 64], [33, 10, 29, 8], [33, 17, 29, 15], [33, 21, 29, 19], [34, 8, 30, 6], [35, 8, 31, 6], [35, 12, 31, 6, "_scene$descriptor$opt"], [35, 33, 31, 6], [35, 36, 37, 10, "scene"], [35, 41, 37, 15], [35, 42, 37, 16, "descriptor"], [35, 52, 37, 26], [35, 53, 37, 27, "options"], [35, 60, 37, 34], [36, 10, 32, 8, "header"], [36, 16, 32, 14], [36, 19, 32, 14, "_scene$descriptor$opt"], [36, 40, 32, 14], [36, 41, 32, 8, "header"], [36, 47, 32, 14], [37, 10, 33, 8, "headerMode"], [37, 20, 33, 18], [37, 23, 33, 18, "_scene$descriptor$opt"], [37, 44, 33, 18], [37, 45, 33, 8, "headerMode"], [37, 55, 33, 18], [38, 10, 33, 18, "_scene$descriptor$opt2"], [38, 32, 33, 18], [38, 35, 33, 18, "_scene$descriptor$opt"], [38, 56, 33, 18], [38, 57, 34, 8, "headerShown"], [38, 68, 34, 19], [39, 10, 34, 8, "headerShown"], [39, 21, 34, 19], [39, 24, 34, 19, "_scene$descriptor$opt2"], [39, 46, 34, 19], [39, 60, 34, 22], [39, 64, 34, 26], [39, 67, 34, 26, "_scene$descriptor$opt2"], [39, 89, 34, 26], [40, 10, 35, 8, "headerTransparent"], [40, 27, 35, 25], [40, 30, 35, 25, "_scene$descriptor$opt"], [40, 51, 35, 25], [40, 52, 35, 8, "headerTransparent"], [40, 69, 35, 25], [41, 10, 36, 8, "headerStyleInterpolator"], [41, 33, 36, 31], [41, 36, 36, 31, "_scene$descriptor$opt"], [41, 57, 36, 31], [41, 58, 36, 8, "headerStyleInterpolator"], [41, 81, 36, 31], [42, 8, 38, 6], [42, 12, 38, 10, "headerMode"], [42, 22, 38, 20], [42, 27, 38, 25, "mode"], [42, 31, 38, 29], [42, 35, 38, 33], [42, 36, 38, 34, "headerShown"], [42, 47, 38, 45], [42, 49, 38, 47], [43, 10, 39, 8], [43, 17, 39, 15], [43, 21, 39, 19], [44, 8, 40, 6], [45, 8, 41, 6], [45, 12, 41, 12, "isFocused"], [45, 21, 41, 21], [45, 24, 41, 24, "focusedRoute"], [45, 36, 41, 36], [45, 37, 41, 37, "key"], [45, 40, 41, 40], [45, 45, 41, 45, "scene"], [45, 50, 41, 50], [45, 51, 41, 51, "descriptor"], [45, 61, 41, 61], [45, 62, 41, 62, "route"], [45, 67, 41, 67], [45, 68, 41, 68, "key"], [45, 71, 41, 71], [46, 8, 42, 6], [46, 12, 42, 12, "previousScene"], [46, 25, 42, 25], [46, 28, 42, 28, "getPreviousScene"], [46, 44, 42, 44], [46, 45, 42, 45], [47, 10, 43, 8, "route"], [47, 15, 43, 13], [47, 17, 43, 15, "scene"], [47, 22, 43, 20], [47, 23, 43, 21, "descriptor"], [47, 33, 43, 31], [47, 34, 43, 32, "route"], [48, 8, 44, 6], [48, 9, 44, 7], [48, 10, 44, 8], [49, 8, 45, 6], [49, 12, 45, 10, "headerBack"], [49, 22, 45, 20], [49, 25, 45, 23, "parentHeaderBack"], [49, 41, 45, 39], [50, 8, 46, 6], [50, 12, 46, 10, "previousScene"], [50, 25, 46, 23], [50, 27, 46, 25], [51, 10, 47, 8], [51, 14, 47, 8, "_previousScene$descri"], [51, 35, 47, 8], [51, 38, 50, 12, "previousScene"], [51, 51, 50, 25], [51, 52, 50, 26, "descriptor"], [51, 62, 50, 36], [52, 12, 48, 10, "options"], [52, 19, 48, 17], [52, 22, 48, 17, "_previousScene$descri"], [52, 43, 48, 17], [52, 44, 48, 10, "options"], [52, 51, 48, 17], [53, 12, 49, 10, "route"], [53, 17, 49, 15], [53, 20, 49, 15, "_previousScene$descri"], [53, 41, 49, 15], [53, 42, 49, 10, "route"], [53, 47, 49, 15], [54, 10, 51, 8, "headerBack"], [54, 20, 51, 18], [54, 23, 51, 21, "previousScene"], [54, 36, 51, 34], [54, 39, 51, 37], [55, 12, 52, 10, "title"], [55, 17, 52, 15], [55, 19, 52, 17], [55, 23, 52, 17, "getHeaderTitle"], [55, 47, 52, 31], [55, 49, 52, 32, "options"], [55, 56, 52, 39], [55, 58, 52, 41, "route"], [55, 63, 52, 46], [55, 64, 52, 47, "name"], [55, 68, 52, 51], [55, 69, 52, 52], [56, 12, 53, 10, "href"], [56, 16, 53, 14], [56, 18, 53, 16, "buildHref"], [56, 27, 53, 25], [56, 28, 53, 26, "route"], [56, 33, 53, 31], [56, 34, 53, 32, "name"], [56, 38, 53, 36], [56, 40, 53, 38, "route"], [56, 45, 53, 43], [56, 46, 53, 44, "params"], [56, 52, 53, 50], [57, 10, 54, 8], [57, 11, 54, 9], [57, 14, 54, 12, "parentHeaderBack"], [57, 30, 54, 28], [58, 8, 55, 6], [60, 8, 57, 6], [61, 8, 58, 6], [62, 8, 59, 6], [62, 12, 59, 12, "previousDescriptor"], [62, 30, 59, 30], [62, 33, 59, 33, "self"], [62, 37, 59, 37], [62, 38, 59, 38, "i"], [62, 39, 59, 39], [62, 42, 59, 42], [62, 43, 59, 43], [62, 44, 59, 44], [62, 46, 59, 46, "descriptor"], [62, 56, 59, 56], [63, 8, 60, 6], [63, 12, 60, 12, "nextDescriptor"], [63, 26, 60, 26], [63, 29, 60, 29, "self"], [63, 33, 60, 33], [63, 34, 60, 34, "i"], [63, 35, 60, 35], [63, 38, 60, 38], [63, 39, 60, 39], [63, 40, 60, 40], [63, 42, 60, 42, "descriptor"], [63, 52, 60, 52], [64, 8, 61, 6], [64, 12, 61, 6, "_ref2"], [64, 17, 61, 6], [64, 20, 64, 10, "previousDescriptor"], [64, 38, 64, 28], [64, 40, 64, 30, "options"], [64, 47, 64, 37], [64, 51, 64, 41], [64, 52, 64, 42], [64, 53, 64, 43], [65, 10, 64, 43, "_ref2$headerShown"], [65, 27, 64, 43], [65, 30, 64, 43, "_ref2"], [65, 35, 64, 43], [65, 36, 62, 8, "headerShown"], [65, 47, 62, 19], [66, 10, 62, 21, "previousHeaderShown"], [66, 29, 62, 40], [66, 32, 62, 40, "_ref2$headerShown"], [66, 49, 62, 40], [66, 63, 62, 43], [66, 67, 62, 47], [66, 70, 62, 47, "_ref2$headerShown"], [66, 87, 62, 47], [67, 10, 63, 20, "previousHeaderMode"], [67, 28, 63, 38], [67, 31, 63, 38, "_ref2"], [67, 36, 63, 38], [67, 37, 63, 8, "headerMode"], [67, 47, 63, 18], [69, 8, 66, 6], [70, 8, 67, 6], [71, 8, 68, 6], [71, 12, 68, 12, "nextHeaderlessScene"], [71, 31, 68, 31], [71, 34, 68, 34, "self"], [71, 38, 68, 38], [71, 39, 68, 39, "slice"], [71, 44, 68, 44], [71, 45, 68, 45, "i"], [71, 46, 68, 46], [71, 49, 68, 49], [71, 50, 68, 50], [71, 51, 68, 51], [71, 52, 68, 52, "find"], [71, 56, 68, 56], [71, 57, 68, 57, "scene"], [71, 62, 68, 62], [71, 66, 68, 66], [72, 10, 69, 8], [72, 14, 69, 8, "_ref3"], [72, 19, 69, 8], [72, 22, 72, 12, "scene"], [72, 27, 72, 17], [72, 29, 72, 19, "descriptor"], [72, 39, 72, 29], [72, 40, 72, 30, "options"], [72, 47, 72, 37], [72, 51, 72, 41], [72, 52, 72, 42], [72, 53, 72, 43], [73, 12, 72, 43, "_ref3$headerShown"], [73, 29, 72, 43], [73, 32, 72, 43, "_ref3"], [73, 37, 72, 43], [73, 38, 70, 10, "headerShown"], [73, 49, 70, 21], [74, 12, 70, 23, "currentHeaderShown"], [74, 30, 70, 41], [74, 33, 70, 41, "_ref3$headerShown"], [74, 50, 70, 41], [74, 64, 70, 44], [74, 68, 70, 48], [74, 71, 70, 48, "_ref3$headerShown"], [74, 88, 70, 48], [75, 12, 71, 22, "currentHeaderMode"], [75, 29, 71, 39], [75, 32, 71, 39, "_ref3"], [75, 37, 71, 39], [75, 38, 71, 10, "headerMode"], [75, 48, 71, 20], [76, 10, 73, 8], [76, 17, 73, 15, "currentHeaderShown"], [76, 35, 73, 33], [76, 40, 73, 38], [76, 45, 73, 43], [76, 49, 73, 47, "currentHeaderMode"], [76, 66, 73, 64], [76, 71, 73, 69], [76, 79, 73, 77], [77, 8, 74, 6], [77, 9, 74, 7], [77, 10, 74, 8], [78, 8, 75, 6], [78, 12, 75, 6, "_ref4"], [78, 17, 75, 6], [78, 20, 77, 10, "nextHeaderlessScene"], [78, 39, 77, 29], [78, 41, 77, 31, "descriptor"], [78, 51, 77, 41], [78, 52, 77, 42, "options"], [78, 59, 77, 49], [78, 63, 77, 53], [78, 64, 77, 54], [78, 65, 77, 55], [79, 10, 76, 26, "nextHeaderlessGestureDirection"], [79, 40, 76, 56], [79, 43, 76, 56, "_ref4"], [79, 48, 76, 56], [79, 49, 76, 8, "gestureDirection"], [79, 65, 76, 24], [80, 8, 78, 6], [80, 12, 78, 12, "isHeaderStatic"], [80, 26, 78, 26], [80, 29, 78, 29], [80, 30, 78, 30, "previousHeaderShown"], [80, 49, 78, 49], [80, 54, 78, 54], [80, 59, 78, 59], [80, 63, 78, 63, "previousHeaderMode"], [80, 81, 78, 81], [80, 86, 78, 86], [80, 94, 78, 94], [81, 8, 79, 6], [82, 8, 80, 6], [83, 8, 81, 6], [83, 9, 81, 7, "nextDescriptor"], [83, 23, 81, 21], [83, 27, 81, 25, "nextHeaderlessScene"], [83, 46, 81, 44], [84, 8, 82, 6], [84, 12, 82, 12, "props"], [84, 17, 82, 17], [84, 20, 82, 20], [85, 10, 83, 8, "layout"], [85, 16, 83, 14], [86, 10, 84, 8, "back"], [86, 14, 84, 12], [86, 16, 84, 14, "headerBack"], [86, 26, 84, 24], [87, 10, 85, 8, "progress"], [87, 18, 85, 16], [87, 20, 85, 18, "scene"], [87, 25, 85, 23], [87, 26, 85, 24, "progress"], [87, 34, 85, 32], [88, 10, 86, 8, "options"], [88, 17, 86, 15], [88, 19, 86, 17, "scene"], [88, 24, 86, 22], [88, 25, 86, 23, "descriptor"], [88, 35, 86, 33], [88, 36, 86, 34, "options"], [88, 43, 86, 41], [89, 10, 87, 8, "route"], [89, 15, 87, 13], [89, 17, 87, 15, "scene"], [89, 22, 87, 20], [89, 23, 87, 21, "descriptor"], [89, 33, 87, 31], [89, 34, 87, 32, "route"], [89, 39, 87, 37], [90, 10, 88, 8, "navigation"], [90, 20, 88, 18], [90, 22, 88, 20, "scene"], [90, 27, 88, 25], [90, 28, 88, 26, "descriptor"], [90, 38, 88, 36], [90, 39, 88, 37, "navigation"], [90, 49, 88, 47], [91, 10, 89, 8, "styleInterpolator"], [91, 27, 89, 25], [91, 29, 89, 27, "mode"], [91, 33, 89, 31], [91, 38, 89, 36], [91, 45, 89, 43], [91, 48, 89, 46, "isHeaderStatic"], [91, 62, 89, 60], [91, 65, 89, 63, "nextHeaderlessGestureDirection"], [91, 95, 89, 93], [91, 100, 89, 98], [91, 110, 89, 108], [91, 114, 89, 112, "nextHeaderlessGestureDirection"], [91, 144, 89, 142], [91, 149, 89, 147], [91, 168, 89, 166], [91, 171, 89, 169, "forSlideUp"], [91, 207, 89, 179], [91, 210, 89, 182, "nextHeaderlessGestureDirection"], [91, 240, 89, 212], [91, 245, 89, 217], [91, 266, 89, 238], [91, 269, 89, 241, "forSlideRight"], [91, 308, 89, 254], [91, 311, 89, 257, "forSlideLeft"], [91, 349, 89, 269], [91, 352, 89, 272, "headerStyleInterpolator"], [91, 375, 89, 295], [91, 378, 89, 298, "forNoAnimation"], [92, 8, 90, 6], [92, 9, 90, 7], [93, 8, 91, 6], [93, 15, 91, 13], [93, 28, 91, 26], [93, 32, 91, 26, "_jsx"], [93, 47, 91, 30], [93, 49, 91, 31, "NavigationContext"], [93, 74, 91, 48], [93, 75, 91, 49, "Provider"], [93, 83, 91, 57], [93, 85, 91, 59], [94, 10, 92, 8, "value"], [94, 15, 92, 13], [94, 17, 92, 15, "scene"], [94, 22, 92, 20], [94, 23, 92, 21, "descriptor"], [94, 33, 92, 31], [94, 34, 92, 32, "navigation"], [94, 44, 92, 42], [95, 10, 93, 8, "children"], [95, 18, 93, 16], [95, 20, 93, 18], [95, 33, 93, 31], [95, 37, 93, 31, "_jsx"], [95, 52, 93, 35], [95, 54, 93, 36, "NavigationRouteContext"], [95, 84, 93, 58], [95, 85, 93, 59, "Provider"], [95, 93, 93, 67], [95, 95, 93, 69], [96, 12, 94, 10, "value"], [96, 17, 94, 15], [96, 19, 94, 17, "scene"], [96, 24, 94, 22], [96, 25, 94, 23, "descriptor"], [96, 35, 94, 33], [96, 36, 94, 34, "route"], [96, 41, 94, 39], [97, 12, 95, 10, "children"], [97, 20, 95, 18], [97, 22, 95, 20], [97, 35, 95, 33], [97, 39, 95, 33, "_jsx"], [97, 54, 95, 37], [97, 56, 95, 38, "View"], [97, 73, 95, 42], [97, 75, 95, 44], [98, 14, 96, 12, "onLayout"], [98, 22, 96, 20], [98, 24, 96, 22, "onContentHeightChange"], [98, 45, 96, 43], [98, 48, 96, 46, "e"], [98, 49, 96, 47], [98, 53, 96, 51], [99, 16, 97, 14], [99, 20, 98, 16, "height"], [99, 26, 98, 22], [99, 29, 99, 18, "e"], [99, 30, 99, 19], [99, 31, 99, 20, "nativeEvent"], [99, 42, 99, 31], [99, 43, 99, 32, "layout"], [99, 49, 99, 38], [99, 50, 98, 16, "height"], [99, 56, 98, 22], [100, 16, 100, 14, "onContentHeightChange"], [100, 37, 100, 35], [100, 38, 100, 36], [101, 18, 101, 16, "route"], [101, 23, 101, 21], [101, 25, 101, 23, "scene"], [101, 30, 101, 28], [101, 31, 101, 29, "descriptor"], [101, 41, 101, 39], [101, 42, 101, 40, "route"], [101, 47, 101, 45], [102, 18, 102, 16, "height"], [103, 16, 103, 14], [103, 17, 103, 15], [103, 18, 103, 16], [104, 14, 104, 12], [104, 15, 104, 13], [104, 18, 104, 16, "undefined"], [104, 27, 104, 25], [105, 14, 105, 12, "pointerEvents"], [105, 27, 105, 25], [105, 29, 105, 27, "isFocused"], [105, 38, 105, 36], [105, 41, 105, 39], [105, 51, 105, 49], [105, 54, 105, 52], [105, 60, 105, 58], [106, 14, 106, 12], [106, 27, 106, 25], [106, 29, 106, 27], [106, 30, 106, 28, "isFocused"], [106, 39, 106, 37], [107, 14, 107, 12, "style"], [107, 19, 107, 17], [108, 14, 108, 12], [109, 14, 109, 12], [110, 14, 110, 12, "mode"], [110, 18, 110, 16], [110, 23, 110, 21], [110, 30, 110, 28], [110, 34, 110, 32], [110, 35, 110, 33, "isFocused"], [110, 44, 110, 42], [110, 48, 110, 46, "headerTransparent"], [110, 65, 110, 63], [110, 68, 110, 66, "styles"], [110, 74, 110, 72], [110, 75, 110, 73, "header"], [110, 81, 110, 79], [110, 84, 110, 82], [110, 88, 110, 86], [111, 14, 111, 12, "children"], [111, 22, 111, 20], [111, 24, 111, 22, "header"], [111, 30, 111, 28], [111, 35, 111, 33, "undefined"], [111, 44, 111, 42], [111, 47, 111, 45, "header"], [111, 53, 111, 51], [111, 54, 111, 52, "props"], [111, 59, 111, 57], [111, 60, 111, 58], [111, 63, 111, 61], [111, 76, 111, 74], [111, 80, 111, 74, "_jsx"], [111, 95, 111, 78], [111, 97, 111, 79, "Header"], [111, 111, 111, 85], [111, 113, 111, 87], [112, 16, 112, 14], [112, 19, 112, 17, "props"], [113, 14, 113, 12], [113, 15, 113, 13], [114, 12, 114, 10], [114, 13, 114, 11], [115, 10, 115, 8], [115, 11, 115, 9], [116, 8, 116, 6], [116, 9, 116, 7], [116, 11, 116, 9, "scene"], [116, 16, 116, 14], [116, 17, 116, 15, "descriptor"], [116, 27, 116, 25], [116, 28, 116, 26, "route"], [116, 33, 116, 31], [116, 34, 116, 32, "key"], [116, 37, 116, 35], [116, 38, 116, 36], [117, 6, 117, 4], [117, 7, 117, 5], [118, 4, 118, 2], [118, 5, 118, 3], [118, 6, 118, 4], [119, 2, 119, 0], [120, 2, 120, 0], [120, 6, 120, 6, "styles"], [120, 12, 120, 12], [120, 15, 120, 15, "StyleSheet"], [120, 38, 120, 25], [120, 39, 120, 26, "create"], [120, 45, 120, 32], [120, 46, 120, 33], [121, 4, 121, 2, "header"], [121, 10, 121, 8], [121, 12, 121, 10], [122, 6, 122, 4, "position"], [122, 14, 122, 12], [122, 16, 122, 14], [122, 26, 122, 24], [123, 6, 123, 4, "top"], [123, 9, 123, 7], [123, 11, 123, 9], [123, 12, 123, 10], [124, 6, 124, 4, "start"], [124, 11, 124, 9], [124, 13, 124, 11], [124, 14, 124, 12], [125, 6, 125, 4, "end"], [125, 9, 125, 7], [125, 11, 125, 9], [126, 4, 126, 2], [127, 2, 127, 0], [127, 3, 127, 1], [127, 4, 127, 2], [128, 0, 127, 3], [128, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes.slice.map$argument_0", "self.slice.find$argument_0", "<anonymous>"], "mappings": "AAA;OCS;mCCiB;yDCyC;ODM;8CEsB;aFQ;KDa;CDE"}}, "type": "js/module"}]}