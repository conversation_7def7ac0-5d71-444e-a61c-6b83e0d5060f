{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 80}, "end": {"line": 2, "column": 44, "index": 124}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 125}, "end": {"line": 6, "column": 32, "index": 220}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.pinchHandlerName = exports.PinchGestureHandler = void 0;\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[1], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[2], \"./gestureHandlerCommon\");\n  /**\n   * @deprecated PinchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pinch()` instead.\n   */\n\n  var pinchHandlerName = exports.pinchHandlerName = 'PinchGestureHandler';\n\n  /**\n   * @deprecated PinchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pinch()` instead.\n   */\n\n  /**\n   * @deprecated PinchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pinch()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n  var PinchGestureHandler = exports.PinchGestureHandler = (0, _createHandler.default)({\n    name: pinchHandlerName,\n    allowedProps: _gestureHandlerCommon.baseGestureHandlerProps,\n    config: {}\n  });\n});", "lineCount": 28, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_createHandler"], [7, 20, 2, 0], [7, 23, 2, 0, "_interopRequireDefault"], [7, 45, 2, 0], [7, 46, 2, 0, "require"], [7, 53, 2, 0], [7, 54, 2, 0, "_dependencyMap"], [7, 68, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 27, 3, 0], [8, 30, 3, 0, "require"], [8, 37, 3, 0], [8, 38, 3, 0, "_dependencyMap"], [8, 52, 3, 0], [9, 2, 8, 0], [10, 0, 9, 0], [11, 0, 10, 0], [13, 2, 14, 7], [13, 6, 14, 13, "pinchHandlerName"], [13, 22, 14, 29], [13, 25, 14, 29, "exports"], [13, 32, 14, 29], [13, 33, 14, 29, "pinchHandlerName"], [13, 49, 14, 29], [13, 52, 14, 32], [13, 73, 14, 53], [15, 2, 16, 0], [16, 0, 17, 0], [17, 0, 18, 0], [19, 2, 21, 0], [20, 0, 22, 0], [21, 0, 23, 0], [22, 2, 24, 0], [23, 2, 25, 7], [23, 6, 25, 13, "PinchGestureHandler"], [23, 25, 25, 32], [23, 28, 25, 32, "exports"], [23, 35, 25, 32], [23, 36, 25, 32, "PinchGestureHandler"], [23, 55, 25, 32], [23, 58, 25, 35], [23, 62, 25, 35, "createHandler"], [23, 84, 25, 48], [23, 86, 28, 2], [24, 4, 29, 2, "name"], [24, 8, 29, 6], [24, 10, 29, 8, "pinchHandlerName"], [24, 26, 29, 24], [25, 4, 30, 2, "allowedProps"], [25, 16, 30, 14], [25, 18, 30, 16, "baseGestureHandlerProps"], [25, 63, 30, 39], [26, 4, 31, 2, "config"], [26, 10, 31, 8], [26, 12, 31, 10], [26, 13, 31, 11], [27, 2, 32, 0], [27, 3, 32, 1], [27, 4, 32, 2], [28, 0, 32, 3], [28, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}