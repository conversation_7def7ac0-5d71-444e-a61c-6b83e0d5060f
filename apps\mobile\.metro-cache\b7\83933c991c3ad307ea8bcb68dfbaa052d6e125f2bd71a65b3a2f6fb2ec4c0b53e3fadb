{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 59, "index": 106}}], "key": "RM0XoJ1uy5+hqq85ZlLNt6FYuco=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useFocusEvents = useFocusEvents;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _NavigationContext = require(_dependencyMap[1], \"./NavigationContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Hook to take care of emitting `focus` and `blur` events.\n   */\n  function useFocusEvents(_ref) {\n    var state = _ref.state,\n      emitter = _ref.emitter;\n    var navigation = React.useContext(_NavigationContext.NavigationContext);\n    var lastFocusedKeyRef = React.useRef(undefined);\n    var currentFocusedKey = state.routes[state.index].key;\n\n    // When the parent screen changes its focus state, we also need to change child's focus\n    // Coz the child screen can't be focused if the parent screen is out of focus\n    React.useEffect(() => navigation?.addListener('focus', () => {\n      lastFocusedKeyRef.current = currentFocusedKey;\n      emitter.emit({\n        type: 'focus',\n        target: currentFocusedKey\n      });\n    }), [currentFocusedKey, emitter, navigation]);\n    React.useEffect(() => navigation?.addListener('blur', () => {\n      lastFocusedKeyRef.current = undefined;\n      emitter.emit({\n        type: 'blur',\n        target: currentFocusedKey\n      });\n    }), [currentFocusedKey, emitter, navigation]);\n    React.useEffect(() => {\n      var lastFocusedKey = lastFocusedKeyRef.current;\n      lastFocusedKeyRef.current = currentFocusedKey;\n\n      // We wouldn't have `lastFocusedKey` on initial mount\n      // Fire focus event for the current route on mount if there's no parent navigator\n      if (lastFocusedKey === undefined && !navigation) {\n        emitter.emit({\n          type: 'focus',\n          target: currentFocusedKey\n        });\n      }\n\n      // We should only emit events when the focused key changed and navigator is focused\n      // When navigator is not focused, screens inside shouldn't receive focused status either\n      if (lastFocusedKey === currentFocusedKey || !(navigation ? navigation.isFocused() : true)) {\n        return;\n      }\n      if (lastFocusedKey === undefined) {\n        // Only fire events after initial mount\n        return;\n      }\n      emitter.emit({\n        type: 'blur',\n        target: lastFocusedKey\n      });\n      emitter.emit({\n        type: 'focus',\n        target: currentFocusedKey\n      });\n    }, [currentFocusedKey, emitter, navigation]);\n  }\n});", "lineCount": 69, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useFocusEvents"], [7, 24, 1, 13], [7, 27, 1, 13, "useFocusEvents"], [7, 41, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_NavigationContext"], [9, 24, 4, 0], [9, 27, 4, 0, "require"], [9, 34, 4, 0], [9, 35, 4, 0, "_dependencyMap"], [9, 49, 4, 0], [10, 2, 4, 59], [10, 11, 4, 59, "_interopRequireWildcard"], [10, 35, 4, 59, "e"], [10, 36, 4, 59], [10, 38, 4, 59, "t"], [10, 39, 4, 59], [10, 68, 4, 59, "WeakMap"], [10, 75, 4, 59], [10, 81, 4, 59, "r"], [10, 82, 4, 59], [10, 89, 4, 59, "WeakMap"], [10, 96, 4, 59], [10, 100, 4, 59, "n"], [10, 101, 4, 59], [10, 108, 4, 59, "WeakMap"], [10, 115, 4, 59], [10, 127, 4, 59, "_interopRequireWildcard"], [10, 150, 4, 59], [10, 162, 4, 59, "_interopRequireWildcard"], [10, 163, 4, 59, "e"], [10, 164, 4, 59], [10, 166, 4, 59, "t"], [10, 167, 4, 59], [10, 176, 4, 59, "t"], [10, 177, 4, 59], [10, 181, 4, 59, "e"], [10, 182, 4, 59], [10, 186, 4, 59, "e"], [10, 187, 4, 59], [10, 188, 4, 59, "__esModule"], [10, 198, 4, 59], [10, 207, 4, 59, "e"], [10, 208, 4, 59], [10, 214, 4, 59, "o"], [10, 215, 4, 59], [10, 217, 4, 59, "i"], [10, 218, 4, 59], [10, 220, 4, 59, "f"], [10, 221, 4, 59], [10, 226, 4, 59, "__proto__"], [10, 235, 4, 59], [10, 243, 4, 59, "default"], [10, 250, 4, 59], [10, 252, 4, 59, "e"], [10, 253, 4, 59], [10, 270, 4, 59, "e"], [10, 271, 4, 59], [10, 294, 4, 59, "e"], [10, 295, 4, 59], [10, 320, 4, 59, "e"], [10, 321, 4, 59], [10, 330, 4, 59, "f"], [10, 331, 4, 59], [10, 337, 4, 59, "o"], [10, 338, 4, 59], [10, 341, 4, 59, "t"], [10, 342, 4, 59], [10, 345, 4, 59, "n"], [10, 346, 4, 59], [10, 349, 4, 59, "r"], [10, 350, 4, 59], [10, 358, 4, 59, "o"], [10, 359, 4, 59], [10, 360, 4, 59, "has"], [10, 363, 4, 59], [10, 364, 4, 59, "e"], [10, 365, 4, 59], [10, 375, 4, 59, "o"], [10, 376, 4, 59], [10, 377, 4, 59, "get"], [10, 380, 4, 59], [10, 381, 4, 59, "e"], [10, 382, 4, 59], [10, 385, 4, 59, "o"], [10, 386, 4, 59], [10, 387, 4, 59, "set"], [10, 390, 4, 59], [10, 391, 4, 59, "e"], [10, 392, 4, 59], [10, 394, 4, 59, "f"], [10, 395, 4, 59], [10, 409, 4, 59, "_t"], [10, 411, 4, 59], [10, 415, 4, 59, "e"], [10, 416, 4, 59], [10, 432, 4, 59, "_t"], [10, 434, 4, 59], [10, 441, 4, 59, "hasOwnProperty"], [10, 455, 4, 59], [10, 456, 4, 59, "call"], [10, 460, 4, 59], [10, 461, 4, 59, "e"], [10, 462, 4, 59], [10, 464, 4, 59, "_t"], [10, 466, 4, 59], [10, 473, 4, 59, "i"], [10, 474, 4, 59], [10, 478, 4, 59, "o"], [10, 479, 4, 59], [10, 482, 4, 59, "Object"], [10, 488, 4, 59], [10, 489, 4, 59, "defineProperty"], [10, 503, 4, 59], [10, 508, 4, 59, "Object"], [10, 514, 4, 59], [10, 515, 4, 59, "getOwnPropertyDescriptor"], [10, 539, 4, 59], [10, 540, 4, 59, "e"], [10, 541, 4, 59], [10, 543, 4, 59, "_t"], [10, 545, 4, 59], [10, 552, 4, 59, "i"], [10, 553, 4, 59], [10, 554, 4, 59, "get"], [10, 557, 4, 59], [10, 561, 4, 59, "i"], [10, 562, 4, 59], [10, 563, 4, 59, "set"], [10, 566, 4, 59], [10, 570, 4, 59, "o"], [10, 571, 4, 59], [10, 572, 4, 59, "f"], [10, 573, 4, 59], [10, 575, 4, 59, "_t"], [10, 577, 4, 59], [10, 579, 4, 59, "i"], [10, 580, 4, 59], [10, 584, 4, 59, "f"], [10, 585, 4, 59], [10, 586, 4, 59, "_t"], [10, 588, 4, 59], [10, 592, 4, 59, "e"], [10, 593, 4, 59], [10, 594, 4, 59, "_t"], [10, 596, 4, 59], [10, 607, 4, 59, "f"], [10, 608, 4, 59], [10, 613, 4, 59, "e"], [10, 614, 4, 59], [10, 616, 4, 59, "t"], [10, 617, 4, 59], [11, 2, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [14, 2, 8, 7], [14, 11, 8, 16, "useFocusEvents"], [14, 25, 8, 30, "useFocusEvents"], [14, 26, 8, 30, "_ref"], [14, 30, 8, 30], [14, 32, 11, 3], [15, 4, 11, 3], [15, 8, 9, 2, "state"], [15, 13, 9, 7], [15, 16, 9, 7, "_ref"], [15, 20, 9, 7], [15, 21, 9, 2, "state"], [15, 26, 9, 7], [16, 6, 10, 2, "emitter"], [16, 13, 10, 9], [16, 16, 10, 9, "_ref"], [16, 20, 10, 9], [16, 21, 10, 2, "emitter"], [16, 28, 10, 9], [17, 4, 12, 2], [17, 8, 12, 8, "navigation"], [17, 18, 12, 18], [17, 21, 12, 21, "React"], [17, 26, 12, 26], [17, 27, 12, 27, "useContext"], [17, 37, 12, 37], [17, 38, 12, 38, "NavigationContext"], [17, 74, 12, 55], [17, 75, 12, 56], [18, 4, 13, 2], [18, 8, 13, 8, "lastFocusedKeyRef"], [18, 25, 13, 25], [18, 28, 13, 28, "React"], [18, 33, 13, 33], [18, 34, 13, 34, "useRef"], [18, 40, 13, 40], [18, 41, 13, 41, "undefined"], [18, 50, 13, 50], [18, 51, 13, 51], [19, 4, 14, 2], [19, 8, 14, 8, "currentFocusedKey"], [19, 25, 14, 25], [19, 28, 14, 28, "state"], [19, 33, 14, 33], [19, 34, 14, 34, "routes"], [19, 40, 14, 40], [19, 41, 14, 41, "state"], [19, 46, 14, 46], [19, 47, 14, 47, "index"], [19, 52, 14, 52], [19, 53, 14, 53], [19, 54, 14, 54, "key"], [19, 57, 14, 57], [21, 4, 16, 2], [22, 4, 17, 2], [23, 4, 18, 2, "React"], [23, 9, 18, 7], [23, 10, 18, 8, "useEffect"], [23, 19, 18, 17], [23, 20, 18, 18], [23, 26, 18, 24, "navigation"], [23, 36, 18, 34], [23, 38, 18, 36, "addListener"], [23, 49, 18, 47], [23, 50, 18, 48], [23, 57, 18, 55], [23, 59, 18, 57], [23, 65, 18, 63], [24, 6, 19, 4, "lastFocusedKeyRef"], [24, 23, 19, 21], [24, 24, 19, 22, "current"], [24, 31, 19, 29], [24, 34, 19, 32, "currentFocusedKey"], [24, 51, 19, 49], [25, 6, 20, 4, "emitter"], [25, 13, 20, 11], [25, 14, 20, 12, "emit"], [25, 18, 20, 16], [25, 19, 20, 17], [26, 8, 21, 6, "type"], [26, 12, 21, 10], [26, 14, 21, 12], [26, 21, 21, 19], [27, 8, 22, 6, "target"], [27, 14, 22, 12], [27, 16, 22, 14, "currentFocusedKey"], [28, 6, 23, 4], [28, 7, 23, 5], [28, 8, 23, 6], [29, 4, 24, 2], [29, 5, 24, 3], [29, 6, 24, 4], [29, 8, 24, 6], [29, 9, 24, 7, "currentFocusedKey"], [29, 26, 24, 24], [29, 28, 24, 26, "emitter"], [29, 35, 24, 33], [29, 37, 24, 35, "navigation"], [29, 47, 24, 45], [29, 48, 24, 46], [29, 49, 24, 47], [30, 4, 25, 2, "React"], [30, 9, 25, 7], [30, 10, 25, 8, "useEffect"], [30, 19, 25, 17], [30, 20, 25, 18], [30, 26, 25, 24, "navigation"], [30, 36, 25, 34], [30, 38, 25, 36, "addListener"], [30, 49, 25, 47], [30, 50, 25, 48], [30, 56, 25, 54], [30, 58, 25, 56], [30, 64, 25, 62], [31, 6, 26, 4, "lastFocusedKeyRef"], [31, 23, 26, 21], [31, 24, 26, 22, "current"], [31, 31, 26, 29], [31, 34, 26, 32, "undefined"], [31, 43, 26, 41], [32, 6, 27, 4, "emitter"], [32, 13, 27, 11], [32, 14, 27, 12, "emit"], [32, 18, 27, 16], [32, 19, 27, 17], [33, 8, 28, 6, "type"], [33, 12, 28, 10], [33, 14, 28, 12], [33, 20, 28, 18], [34, 8, 29, 6, "target"], [34, 14, 29, 12], [34, 16, 29, 14, "currentFocusedKey"], [35, 6, 30, 4], [35, 7, 30, 5], [35, 8, 30, 6], [36, 4, 31, 2], [36, 5, 31, 3], [36, 6, 31, 4], [36, 8, 31, 6], [36, 9, 31, 7, "currentFocusedKey"], [36, 26, 31, 24], [36, 28, 31, 26, "emitter"], [36, 35, 31, 33], [36, 37, 31, 35, "navigation"], [36, 47, 31, 45], [36, 48, 31, 46], [36, 49, 31, 47], [37, 4, 32, 2, "React"], [37, 9, 32, 7], [37, 10, 32, 8, "useEffect"], [37, 19, 32, 17], [37, 20, 32, 18], [37, 26, 32, 24], [38, 6, 33, 4], [38, 10, 33, 10, "lastFocused<PERSON>ey"], [38, 24, 33, 24], [38, 27, 33, 27, "lastFocusedKeyRef"], [38, 44, 33, 44], [38, 45, 33, 45, "current"], [38, 52, 33, 52], [39, 6, 34, 4, "lastFocusedKeyRef"], [39, 23, 34, 21], [39, 24, 34, 22, "current"], [39, 31, 34, 29], [39, 34, 34, 32, "currentFocusedKey"], [39, 51, 34, 49], [41, 6, 36, 4], [42, 6, 37, 4], [43, 6, 38, 4], [43, 10, 38, 8, "lastFocused<PERSON>ey"], [43, 24, 38, 22], [43, 29, 38, 27, "undefined"], [43, 38, 38, 36], [43, 42, 38, 40], [43, 43, 38, 41, "navigation"], [43, 53, 38, 51], [43, 55, 38, 53], [44, 8, 39, 6, "emitter"], [44, 15, 39, 13], [44, 16, 39, 14, "emit"], [44, 20, 39, 18], [44, 21, 39, 19], [45, 10, 40, 8, "type"], [45, 14, 40, 12], [45, 16, 40, 14], [45, 23, 40, 21], [46, 10, 41, 8, "target"], [46, 16, 41, 14], [46, 18, 41, 16, "currentFocusedKey"], [47, 8, 42, 6], [47, 9, 42, 7], [47, 10, 42, 8], [48, 6, 43, 4], [50, 6, 45, 4], [51, 6, 46, 4], [52, 6, 47, 4], [52, 10, 47, 8, "lastFocused<PERSON>ey"], [52, 24, 47, 22], [52, 29, 47, 27, "currentFocusedKey"], [52, 46, 47, 44], [52, 50, 47, 48], [52, 52, 47, 50, "navigation"], [52, 62, 47, 60], [52, 65, 47, 63, "navigation"], [52, 75, 47, 73], [52, 76, 47, 74, "isFocused"], [52, 85, 47, 83], [52, 86, 47, 84], [52, 87, 47, 85], [52, 90, 47, 88], [52, 94, 47, 92], [52, 95, 47, 93], [52, 97, 47, 95], [53, 8, 48, 6], [54, 6, 49, 4], [55, 6, 50, 4], [55, 10, 50, 8, "lastFocused<PERSON>ey"], [55, 24, 50, 22], [55, 29, 50, 27, "undefined"], [55, 38, 50, 36], [55, 40, 50, 38], [56, 8, 51, 6], [57, 8, 52, 6], [58, 6, 53, 4], [59, 6, 54, 4, "emitter"], [59, 13, 54, 11], [59, 14, 54, 12, "emit"], [59, 18, 54, 16], [59, 19, 54, 17], [60, 8, 55, 6, "type"], [60, 12, 55, 10], [60, 14, 55, 12], [60, 20, 55, 18], [61, 8, 56, 6, "target"], [61, 14, 56, 12], [61, 16, 56, 14, "lastFocused<PERSON>ey"], [62, 6, 57, 4], [62, 7, 57, 5], [62, 8, 57, 6], [63, 6, 58, 4, "emitter"], [63, 13, 58, 11], [63, 14, 58, 12, "emit"], [63, 18, 58, 16], [63, 19, 58, 17], [64, 8, 59, 6, "type"], [64, 12, 59, 10], [64, 14, 59, 12], [64, 21, 59, 19], [65, 8, 60, 6, "target"], [65, 14, 60, 12], [65, 16, 60, 14, "currentFocusedKey"], [66, 6, 61, 4], [66, 7, 61, 5], [66, 8, 61, 6], [67, 4, 62, 2], [67, 5, 62, 3], [67, 7, 62, 5], [67, 8, 62, 6, "currentFocusedKey"], [67, 25, 62, 23], [67, 27, 62, 25, "emitter"], [67, 34, 62, 32], [67, 36, 62, 34, "navigation"], [67, 46, 62, 44], [67, 47, 62, 45], [67, 48, 62, 46], [68, 2, 63, 0], [69, 0, 63, 1], [69, 3]], "functionMap": {"names": ["<global>", "useFocusEvents", "React.useEffect$argument_0", "navigation.addListener$argument_1"], "mappings": "AAA;OCO;kBCU,uCC;GDM,CD;kBCC,sCC;GDM,CD;kBCC;GD8B;CDC"}}, "type": "js/module"}]}