{"dependencies": [{"name": "../animationParser", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 71, "index": 85}}], "key": "NS2upIa4aHN1XdKmQKcusYkE9o0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PinwheelData = exports.Pinwheel = void 0;\n  var _animationParser = require(_dependencyMap[0], \"../animationParser\");\n  var DEFAULT_PINWHEEL_TIME = 0.3;\n  var PinwheelData = exports.PinwheelData = {\n    PinwheelIn: {\n      name: 'PinwheelIn',\n      style: {\n        0: {\n          transform: [{\n            rotate: '5rad',\n            scale: 0\n          }],\n          opacity: 0\n        },\n        100: {\n          transform: [{\n            rotate: '0deg',\n            scale: 1\n          }],\n          opacity: 1\n        }\n      },\n      duration: DEFAULT_PINWHEEL_TIME\n    },\n    PinwheelOut: {\n      name: 'PinwheelOut',\n      style: {\n        0: {\n          transform: [{\n            rotate: '0rad',\n            scale: 1\n          }],\n          opacity: 1\n        },\n        100: {\n          transform: [{\n            rotate: '5rad',\n            scale: 0\n          }],\n          opacity: 0\n        }\n      },\n      duration: DEFAULT_PINWHEEL_TIME\n    }\n  };\n  var Pinwheel = exports.Pinwheel = {\n    PinwheelIn: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(PinwheelData.PinwheelIn),\n      duration: PinwheelData.PinwheelIn.duration\n    },\n    PinwheelOut: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(PinwheelData.PinwheelOut),\n      duration: PinwheelData.PinwheelOut.duration\n    }\n  };\n});", "lineCount": 62, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "PinwheelData"], [7, 22, 1, 13], [7, 25, 1, 13, "exports"], [7, 32, 1, 13], [7, 33, 1, 13, "Pinwheel"], [7, 41, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_animation<PERSON><PERSON>er"], [8, 22, 2, 0], [8, 25, 2, 0, "require"], [8, 32, 2, 0], [8, 33, 2, 0, "_dependencyMap"], [8, 47, 2, 0], [9, 2, 4, 0], [9, 6, 4, 6, "DEFAULT_PINWHEEL_TIME"], [9, 27, 4, 27], [9, 30, 4, 30], [9, 33, 4, 33], [10, 2, 6, 7], [10, 6, 6, 13, "PinwheelData"], [10, 18, 6, 25], [10, 21, 6, 25, "exports"], [10, 28, 6, 25], [10, 29, 6, 25, "PinwheelData"], [10, 41, 6, 25], [10, 44, 6, 28], [11, 4, 7, 2, "PinwheelIn"], [11, 14, 7, 12], [11, 16, 7, 14], [12, 6, 8, 4, "name"], [12, 10, 8, 8], [12, 12, 8, 10], [12, 24, 8, 22], [13, 6, 9, 4, "style"], [13, 11, 9, 9], [13, 13, 9, 11], [14, 8, 10, 6], [14, 9, 10, 7], [14, 11, 10, 9], [15, 10, 11, 8, "transform"], [15, 19, 11, 17], [15, 21, 11, 19], [15, 22, 11, 20], [16, 12, 11, 22, "rotate"], [16, 18, 11, 28], [16, 20, 11, 30], [16, 26, 11, 36], [17, 12, 11, 38, "scale"], [17, 17, 11, 43], [17, 19, 11, 45], [18, 10, 11, 47], [18, 11, 11, 48], [18, 12, 11, 49], [19, 10, 12, 8, "opacity"], [19, 17, 12, 15], [19, 19, 12, 17], [20, 8, 13, 6], [20, 9, 13, 7], [21, 8, 15, 6], [21, 11, 15, 9], [21, 13, 15, 11], [22, 10, 16, 8, "transform"], [22, 19, 16, 17], [22, 21, 16, 19], [22, 22, 16, 20], [23, 12, 16, 22, "rotate"], [23, 18, 16, 28], [23, 20, 16, 30], [23, 26, 16, 36], [24, 12, 16, 38, "scale"], [24, 17, 16, 43], [24, 19, 16, 45], [25, 10, 16, 47], [25, 11, 16, 48], [25, 12, 16, 49], [26, 10, 17, 8, "opacity"], [26, 17, 17, 15], [26, 19, 17, 17], [27, 8, 18, 6], [28, 6, 19, 4], [28, 7, 19, 5], [29, 6, 20, 4, "duration"], [29, 14, 20, 12], [29, 16, 20, 14, "DEFAULT_PINWHEEL_TIME"], [30, 4, 21, 2], [30, 5, 21, 3], [31, 4, 23, 2, "PinwheelOut"], [31, 15, 23, 13], [31, 17, 23, 15], [32, 6, 24, 4, "name"], [32, 10, 24, 8], [32, 12, 24, 10], [32, 25, 24, 23], [33, 6, 25, 4, "style"], [33, 11, 25, 9], [33, 13, 25, 11], [34, 8, 26, 6], [34, 9, 26, 7], [34, 11, 26, 9], [35, 10, 27, 8, "transform"], [35, 19, 27, 17], [35, 21, 27, 19], [35, 22, 27, 20], [36, 12, 27, 22, "rotate"], [36, 18, 27, 28], [36, 20, 27, 30], [36, 26, 27, 36], [37, 12, 27, 38, "scale"], [37, 17, 27, 43], [37, 19, 27, 45], [38, 10, 27, 47], [38, 11, 27, 48], [38, 12, 27, 49], [39, 10, 28, 8, "opacity"], [39, 17, 28, 15], [39, 19, 28, 17], [40, 8, 29, 6], [40, 9, 29, 7], [41, 8, 30, 6], [41, 11, 30, 9], [41, 13, 30, 11], [42, 10, 31, 8, "transform"], [42, 19, 31, 17], [42, 21, 31, 19], [42, 22, 31, 20], [43, 12, 31, 22, "rotate"], [43, 18, 31, 28], [43, 20, 31, 30], [43, 26, 31, 36], [44, 12, 31, 38, "scale"], [44, 17, 31, 43], [44, 19, 31, 45], [45, 10, 31, 47], [45, 11, 31, 48], [45, 12, 31, 49], [46, 10, 32, 8, "opacity"], [46, 17, 32, 15], [46, 19, 32, 17], [47, 8, 33, 6], [48, 6, 34, 4], [48, 7, 34, 5], [49, 6, 35, 4, "duration"], [49, 14, 35, 12], [49, 16, 35, 14, "DEFAULT_PINWHEEL_TIME"], [50, 4, 36, 2], [51, 2, 37, 0], [51, 3, 37, 1], [52, 2, 39, 7], [52, 6, 39, 13, "Pinwheel"], [52, 14, 39, 21], [52, 17, 39, 21, "exports"], [52, 24, 39, 21], [52, 25, 39, 21, "Pinwheel"], [52, 33, 39, 21], [52, 36, 39, 24], [53, 4, 40, 2, "PinwheelIn"], [53, 14, 40, 12], [53, 16, 40, 14], [54, 6, 41, 4, "style"], [54, 11, 41, 9], [54, 13, 41, 11], [54, 17, 41, 11, "convertAnimationObjectToKeyframes"], [54, 67, 41, 44], [54, 69, 41, 45, "PinwheelData"], [54, 81, 41, 57], [54, 82, 41, 58, "PinwheelIn"], [54, 92, 41, 68], [54, 93, 41, 69], [55, 6, 42, 4, "duration"], [55, 14, 42, 12], [55, 16, 42, 14, "PinwheelData"], [55, 28, 42, 26], [55, 29, 42, 27, "PinwheelIn"], [55, 39, 42, 37], [55, 40, 42, 38, "duration"], [56, 4, 43, 2], [56, 5, 43, 3], [57, 4, 44, 2, "PinwheelOut"], [57, 15, 44, 13], [57, 17, 44, 15], [58, 6, 45, 4, "style"], [58, 11, 45, 9], [58, 13, 45, 11], [58, 17, 45, 11, "convertAnimationObjectToKeyframes"], [58, 67, 45, 44], [58, 69, 45, 45, "PinwheelData"], [58, 81, 45, 57], [58, 82, 45, 58, "PinwheelOut"], [58, 93, 45, 69], [58, 94, 45, 70], [59, 6, 46, 4, "duration"], [59, 14, 46, 12], [59, 16, 46, 14, "PinwheelData"], [59, 28, 46, 26], [59, 29, 46, 27, "PinwheelOut"], [59, 40, 46, 38], [59, 41, 46, 39, "duration"], [60, 4, 47, 2], [61, 2, 48, 0], [61, 3, 48, 1], [62, 0, 48, 2], [62, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}