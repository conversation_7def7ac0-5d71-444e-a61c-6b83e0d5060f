{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Components/View/View", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 13}, "end": {"line": 17, "column": 63}}], "key": "H/3fvmiHyIdASS62Hfb3a4a54KU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[2], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\src\\\\private\\\\inspector\\\\BorderBox.js\";\n  var View = require(_dependencyMap[3], \"../../../Libraries/Components/View/View\").default;\n  function BorderBox(_ref) {\n    var children = _ref.children,\n      box = _ref.box,\n      style = _ref.style;\n    if (!box) {\n      return children;\n    }\n    var borderStyle = {\n      borderTopWidth: box.top,\n      borderBottomWidth: box.bottom,\n      borderLeftWidth: box.left,\n      borderRightWidth: box.right\n    };\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(View, {\n      style: [borderStyle, style],\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 10\n    }, this);\n  }\n  var _default = exports.default = BorderBox;\n});", "lineCount": 36, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 15, 0], [9, 6, 15, 0, "_react"], [9, 12, 15, 0], [9, 15, 15, 0, "_interopRequireDefault"], [9, 37, 15, 0], [9, 38, 15, 0, "require"], [9, 45, 15, 0], [9, 46, 15, 0, "_dependencyMap"], [9, 60, 15, 0], [10, 2, 15, 26], [10, 6, 15, 26, "_jsxDevRuntime"], [10, 20, 15, 26], [10, 23, 15, 26, "require"], [10, 30, 15, 26], [10, 31, 15, 26, "_dependencyMap"], [10, 45, 15, 26], [11, 2, 15, 26], [11, 6, 15, 26, "_jsxFileName"], [11, 18, 15, 26], [12, 2, 17, 0], [12, 6, 17, 6, "View"], [12, 10, 17, 10], [12, 13, 17, 13, "require"], [12, 20, 17, 20], [12, 21, 17, 20, "_dependencyMap"], [12, 35, 17, 20], [12, 81, 17, 62], [12, 82, 17, 63], [12, 83, 17, 64, "default"], [12, 90, 17, 71], [13, 2, 31, 0], [13, 11, 31, 9, "BorderBox"], [13, 20, 31, 18, "BorderBox"], [13, 21, 31, 18, "_ref"], [13, 25, 31, 18], [13, 27, 31, 62], [14, 4, 31, 62], [14, 8, 31, 20, "children"], [14, 16, 31, 28], [14, 19, 31, 28, "_ref"], [14, 23, 31, 28], [14, 24, 31, 20, "children"], [14, 32, 31, 28], [15, 6, 31, 30, "box"], [15, 9, 31, 33], [15, 12, 31, 33, "_ref"], [15, 16, 31, 33], [15, 17, 31, 30, "box"], [15, 20, 31, 33], [16, 6, 31, 35, "style"], [16, 11, 31, 40], [16, 14, 31, 40, "_ref"], [16, 18, 31, 40], [16, 19, 31, 35, "style"], [16, 24, 31, 40], [17, 4, 32, 2], [17, 8, 32, 6], [17, 9, 32, 7, "box"], [17, 12, 32, 10], [17, 14, 32, 12], [18, 6, 33, 4], [18, 13, 33, 11, "children"], [18, 21, 33, 19], [19, 4, 34, 2], [20, 4, 35, 2], [20, 8, 35, 8, "borderStyle"], [20, 19, 35, 19], [20, 22, 35, 22], [21, 6, 36, 4, "borderTopWidth"], [21, 20, 36, 18], [21, 22, 36, 20, "box"], [21, 25, 36, 23], [21, 26, 36, 24, "top"], [21, 29, 36, 27], [22, 6, 37, 4, "borderBottomWidth"], [22, 23, 37, 21], [22, 25, 37, 23, "box"], [22, 28, 37, 26], [22, 29, 37, 27, "bottom"], [22, 35, 37, 33], [23, 6, 38, 4, "borderLeftWidth"], [23, 21, 38, 19], [23, 23, 38, 21, "box"], [23, 26, 38, 24], [23, 27, 38, 25, "left"], [23, 31, 38, 29], [24, 6, 39, 4, "borderRightWidth"], [24, 22, 39, 20], [24, 24, 39, 22, "box"], [24, 27, 39, 25], [24, 28, 39, 26, "right"], [25, 4, 40, 2], [25, 5, 40, 3], [26, 4, 41, 2], [26, 24, 41, 9], [26, 28, 41, 9, "_jsxDevRuntime"], [26, 42, 41, 9], [26, 43, 41, 9, "jsxDEV"], [26, 49, 41, 9], [26, 51, 41, 10, "View"], [26, 55, 41, 14], [27, 6, 41, 15, "style"], [27, 11, 41, 20], [27, 13, 41, 22], [27, 14, 41, 23, "borderStyle"], [27, 25, 41, 34], [27, 27, 41, 36, "style"], [27, 32, 41, 41], [27, 33, 41, 43], [28, 6, 41, 43, "children"], [28, 14, 41, 43], [28, 16, 41, 45, "children"], [29, 4, 41, 53], [30, 6, 41, 53, "fileName"], [30, 14, 41, 53], [30, 16, 41, 53, "_jsxFileName"], [30, 28, 41, 53], [31, 6, 41, 53, "lineNumber"], [31, 16, 41, 53], [32, 6, 41, 53, "columnNumber"], [32, 18, 41, 53], [33, 4, 41, 53], [33, 11, 41, 60], [33, 12, 41, 61], [34, 2, 42, 0], [35, 2, 42, 1], [35, 6, 42, 1, "_default"], [35, 14, 42, 1], [35, 17, 42, 1, "exports"], [35, 24, 42, 1], [35, 25, 42, 1, "default"], [35, 32, 42, 1], [35, 35, 44, 15, "BorderBox"], [35, 44, 44, 24], [36, 0, 44, 24], [36, 3]], "functionMap": {"names": ["<global>", "BorderBox"], "mappings": "AAA;AC8B;CDW"}}, "type": "js/module"}]}