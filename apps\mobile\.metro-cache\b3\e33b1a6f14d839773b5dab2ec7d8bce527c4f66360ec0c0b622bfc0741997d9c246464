{"dependencies": [{"name": "../collections", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 19, "index": 160}, "end": {"line": 7, "column": 44, "index": 185}}], "key": "EATVSGofyoN+/7kTXlSYWkNRByg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, '__esModule', {\n    value: true\n  });\n  exports.test = exports.serialize = exports.default = void 0;\n  var _collections = require(_dependencyMap[0], \"../collections\");\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  // SENTINEL constants are from https://github.com/facebook/immutable-js\n  var IS_ITERABLE_SENTINEL = '@@__IMMUTABLE_ITERABLE__@@';\n  var IS_LIST_SENTINEL = '@@__IMMUTABLE_LIST__@@';\n  var IS_KEYED_SENTINEL = '@@__IMMUTABLE_KEYED__@@';\n  var IS_MAP_SENTINEL = '@@__IMMUTABLE_MAP__@@';\n  var IS_ORDERED_SENTINEL = '@@__IMMUTABLE_ORDERED__@@';\n  var IS_RECORD_SENTINEL = '@@__IMMUTABLE_RECORD__@@'; // immutable v4\n  var IS_SEQ_SENTINEL = '@@__IMMUTABLE_SEQ__@@';\n  var IS_SET_SENTINEL = '@@__IMMUTABLE_SET__@@';\n  var IS_STACK_SENTINEL = '@@__IMMUTABLE_STACK__@@';\n  var getImmutableName = name => `Immutable.${name}`;\n  var printAsLeaf = name => `[${name}]`;\n  var SPACE = ' ';\n  var LAZY = '…'; // Seq is lazy if it calls a method like filter\n\n  var printImmutableEntries = (val, config, indentation, depth, refs, printer, type) => ++depth > config.maxDepth ? printAsLeaf(getImmutableName(type)) : `${getImmutableName(type) + SPACE}{${(0, _collections.printIteratorEntries)(val.entries(), config, indentation, depth, refs, printer)}}`;\n\n  // Record has an entries method because it is a collection in immutable v3.\n  // Return an iterator for Immutable Record from version v3 or v4.\n  function getRecordEntries(val) {\n    var i = 0;\n    return {\n      next() {\n        if (i < val._keys.length) {\n          var key = val._keys[i++];\n          return {\n            done: false,\n            value: [key, val.get(key)]\n          };\n        }\n        return {\n          done: true,\n          value: undefined\n        };\n      }\n    };\n  }\n  var printImmutableRecord = (val, config, indentation, depth, refs, printer) => {\n    // _name property is defined only for an Immutable Record instance\n    // which was constructed with a second optional descriptive name arg\n    var name = getImmutableName(val._name || 'Record');\n    return ++depth > config.maxDepth ? printAsLeaf(name) : `${name + SPACE}{${(0, _collections.printIteratorEntries)(getRecordEntries(val), config, indentation, depth, refs, printer)}}`;\n  };\n  var printImmutableSeq = (val, config, indentation, depth, refs, printer) => {\n    var name = getImmutableName('Seq');\n    if (++depth > config.maxDepth) {\n      return printAsLeaf(name);\n    }\n    if (val[IS_KEYED_SENTINEL]) {\n      return `${name + SPACE}{${\n      // from Immutable collection of entries or from ECMAScript object\n      val._iter || val._object ? (0, _collections.printIteratorEntries)(val.entries(), config, indentation, depth, refs, printer) : LAZY}}`;\n    }\n    return `${name + SPACE}[${val._iter ||\n    // from Immutable collection of values\n    val._array ||\n    // from ECMAScript array\n    val._collection ||\n    // from ECMAScript collection in immutable v4\n    val._iterable // from ECMAScript collection in immutable v3\n    ? (0, _collections.printIteratorValues)(val.values(), config, indentation, depth, refs, printer) : LAZY}]`;\n  };\n  var printImmutableValues = (val, config, indentation, depth, refs, printer, type) => ++depth > config.maxDepth ? printAsLeaf(getImmutableName(type)) : `${getImmutableName(type) + SPACE}[${(0, _collections.printIteratorValues)(val.values(), config, indentation, depth, refs, printer)}]`;\n  var serialize = (val, config, indentation, depth, refs, printer) => {\n    if (val[IS_MAP_SENTINEL]) {\n      return printImmutableEntries(val, config, indentation, depth, refs, printer, val[IS_ORDERED_SENTINEL] ? 'OrderedMap' : 'Map');\n    }\n    if (val[IS_LIST_SENTINEL]) {\n      return printImmutableValues(val, config, indentation, depth, refs, printer, 'List');\n    }\n    if (val[IS_SET_SENTINEL]) {\n      return printImmutableValues(val, config, indentation, depth, refs, printer, val[IS_ORDERED_SENTINEL] ? 'OrderedSet' : 'Set');\n    }\n    if (val[IS_STACK_SENTINEL]) {\n      return printImmutableValues(val, config, indentation, depth, refs, printer, 'Stack');\n    }\n    if (val[IS_SEQ_SENTINEL]) {\n      return printImmutableSeq(val, config, indentation, depth, refs, printer);\n    }\n\n    // For compatibility with immutable v3 and v4, let record be the default.\n    return printImmutableRecord(val, config, indentation, depth, refs, printer);\n  };\n\n  // Explicitly comparing sentinel properties to true avoids false positive\n  // when mock identity-obj-proxy returns the key as the value for any key.\n  exports.serialize = serialize;\n  var test = val => val && (val[IS_ITERABLE_SENTINEL] === true || val[IS_RECORD_SENTINEL] === true);\n  exports.test = test;\n  var plugin = {\n    serialize,\n    test\n  };\n  var _default = plugin;\n  exports.default = _default;\n});", "lineCount": 111, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8, "test"], [7, 14, 6, 12], [7, 17, 6, 15, "exports"], [7, 24, 6, 22], [7, 25, 6, 23, "serialize"], [7, 34, 6, 32], [7, 37, 6, 35, "exports"], [7, 44, 6, 42], [7, 45, 6, 43, "default"], [7, 52, 6, 50], [7, 55, 6, 53], [7, 60, 6, 58], [7, 61, 6, 59], [8, 2, 7, 0], [8, 6, 7, 4, "_collections"], [8, 18, 7, 16], [8, 21, 7, 19, "require"], [8, 28, 7, 26], [8, 29, 7, 26, "_dependencyMap"], [8, 43, 7, 26], [8, 64, 7, 43], [8, 65, 7, 44], [9, 2, 8, 0], [10, 0, 9, 0], [11, 0, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [16, 2, 15, 0], [17, 2, 16, 0], [17, 6, 16, 6, "IS_ITERABLE_SENTINEL"], [17, 26, 16, 26], [17, 29, 16, 29], [17, 57, 16, 57], [18, 2, 17, 0], [18, 6, 17, 6, "IS_LIST_SENTINEL"], [18, 22, 17, 22], [18, 25, 17, 25], [18, 49, 17, 49], [19, 2, 18, 0], [19, 6, 18, 6, "IS_KEYED_SENTINEL"], [19, 23, 18, 23], [19, 26, 18, 26], [19, 51, 18, 51], [20, 2, 19, 0], [20, 6, 19, 6, "IS_MAP_SENTINEL"], [20, 21, 19, 21], [20, 24, 19, 24], [20, 47, 19, 47], [21, 2, 20, 0], [21, 6, 20, 6, "IS_ORDERED_SENTINEL"], [21, 25, 20, 25], [21, 28, 20, 28], [21, 55, 20, 55], [22, 2, 21, 0], [22, 6, 21, 6, "IS_RECORD_SENTINEL"], [22, 24, 21, 24], [22, 27, 21, 27], [22, 53, 21, 53], [22, 54, 21, 54], [22, 55, 21, 55], [23, 2, 22, 0], [23, 6, 22, 6, "IS_SEQ_SENTINEL"], [23, 21, 22, 21], [23, 24, 22, 24], [23, 47, 22, 47], [24, 2, 23, 0], [24, 6, 23, 6, "IS_SET_SENTINEL"], [24, 21, 23, 21], [24, 24, 23, 24], [24, 47, 23, 47], [25, 2, 24, 0], [25, 6, 24, 6, "IS_STACK_SENTINEL"], [25, 23, 24, 23], [25, 26, 24, 26], [25, 51, 24, 51], [26, 2, 25, 0], [26, 6, 25, 6, "getImmutableName"], [26, 22, 25, 22], [26, 25, 25, 25, "name"], [26, 29, 25, 29], [26, 33, 25, 33], [26, 46, 25, 46, "name"], [26, 50, 25, 50], [26, 52, 25, 52], [27, 2, 26, 0], [27, 6, 26, 6, "printAsLeaf"], [27, 17, 26, 17], [27, 20, 26, 20, "name"], [27, 24, 26, 24], [27, 28, 26, 28], [27, 32, 26, 32, "name"], [27, 36, 26, 36], [27, 39, 26, 39], [28, 2, 27, 0], [28, 6, 27, 6, "SPACE"], [28, 11, 27, 11], [28, 14, 27, 14], [28, 17, 27, 17], [29, 2, 28, 0], [29, 6, 28, 6, "LAZY"], [29, 10, 28, 10], [29, 13, 28, 13], [29, 16, 28, 16], [29, 17, 28, 17], [29, 18, 28, 18], [31, 2, 30, 0], [31, 6, 30, 6, "printImmutableEntries"], [31, 27, 30, 27], [31, 30, 30, 30, "printImmutableEntries"], [31, 31, 31, 2, "val"], [31, 34, 31, 5], [31, 36, 32, 2, "config"], [31, 42, 32, 8], [31, 44, 33, 2, "indentation"], [31, 55, 33, 13], [31, 57, 34, 2, "depth"], [31, 62, 34, 7], [31, 64, 35, 2, "refs"], [31, 68, 35, 6], [31, 70, 36, 2, "printer"], [31, 77, 36, 9], [31, 79, 37, 2, "type"], [31, 83, 37, 6], [31, 88, 39, 2], [31, 90, 39, 4, "depth"], [31, 95, 39, 9], [31, 98, 39, 12, "config"], [31, 104, 39, 18], [31, 105, 39, 19, "max<PERSON><PERSON><PERSON>"], [31, 113, 39, 27], [31, 116, 40, 6, "printAsLeaf"], [31, 127, 40, 17], [31, 128, 40, 18, "getImmutableName"], [31, 144, 40, 34], [31, 145, 40, 35, "type"], [31, 149, 40, 39], [31, 150, 40, 40], [31, 151, 40, 41], [31, 154, 41, 6], [31, 157, 41, 9, "getImmutableName"], [31, 173, 41, 25], [31, 174, 41, 26, "type"], [31, 178, 41, 30], [31, 179, 41, 31], [31, 182, 41, 34, "SPACE"], [31, 187, 41, 39], [31, 191, 41, 43], [31, 192, 41, 44], [31, 193, 41, 45], [31, 195, 42, 6, "_collections"], [31, 207, 42, 18], [31, 208, 42, 19, "printIteratorEntries"], [31, 228, 42, 39], [31, 230, 43, 8, "val"], [31, 233, 43, 11], [31, 234, 43, 12, "entries"], [31, 241, 43, 19], [31, 242, 43, 20], [31, 243, 43, 21], [31, 245, 44, 8, "config"], [31, 251, 44, 14], [31, 253, 45, 8, "indentation"], [31, 264, 45, 19], [31, 266, 46, 8, "depth"], [31, 271, 46, 13], [31, 273, 47, 8, "refs"], [31, 277, 47, 12], [31, 279, 48, 8, "printer"], [31, 286, 49, 6], [31, 287, 49, 7], [31, 290, 49, 10], [33, 2, 51, 0], [34, 2, 52, 0], [35, 2, 53, 0], [35, 11, 53, 9, "getRecordEntries"], [35, 27, 53, 25, "getRecordEntries"], [35, 28, 53, 26, "val"], [35, 31, 53, 29], [35, 33, 53, 31], [36, 4, 54, 2], [36, 8, 54, 6, "i"], [36, 9, 54, 7], [36, 12, 54, 10], [36, 13, 54, 11], [37, 4, 55, 2], [37, 11, 55, 9], [38, 6, 56, 4, "next"], [38, 10, 56, 8, "next"], [38, 11, 56, 8], [38, 13, 56, 11], [39, 8, 57, 6], [39, 12, 57, 10, "i"], [39, 13, 57, 11], [39, 16, 57, 14, "val"], [39, 19, 57, 17], [39, 20, 57, 18, "_keys"], [39, 25, 57, 23], [39, 26, 57, 24, "length"], [39, 32, 57, 30], [39, 34, 57, 32], [40, 10, 58, 8], [40, 14, 58, 14, "key"], [40, 17, 58, 17], [40, 20, 58, 20, "val"], [40, 23, 58, 23], [40, 24, 58, 24, "_keys"], [40, 29, 58, 29], [40, 30, 58, 30, "i"], [40, 31, 58, 31], [40, 33, 58, 33], [40, 34, 58, 34], [41, 10, 59, 8], [41, 17, 59, 15], [42, 12, 60, 10, "done"], [42, 16, 60, 14], [42, 18, 60, 16], [42, 23, 60, 21], [43, 12, 61, 10, "value"], [43, 17, 61, 15], [43, 19, 61, 17], [43, 20, 61, 18, "key"], [43, 23, 61, 21], [43, 25, 61, 23, "val"], [43, 28, 61, 26], [43, 29, 61, 27, "get"], [43, 32, 61, 30], [43, 33, 61, 31, "key"], [43, 36, 61, 34], [43, 37, 61, 35], [44, 10, 62, 8], [44, 11, 62, 9], [45, 8, 63, 6], [46, 8, 64, 6], [46, 15, 64, 13], [47, 10, 65, 8, "done"], [47, 14, 65, 12], [47, 16, 65, 14], [47, 20, 65, 18], [48, 10, 66, 8, "value"], [48, 15, 66, 13], [48, 17, 66, 15, "undefined"], [49, 8, 67, 6], [49, 9, 67, 7], [50, 6, 68, 4], [51, 4, 69, 2], [51, 5, 69, 3], [52, 2, 70, 0], [53, 2, 71, 0], [53, 6, 71, 6, "printImmutableRecord"], [53, 26, 71, 26], [53, 29, 71, 29, "printImmutableRecord"], [53, 30, 72, 2, "val"], [53, 33, 72, 5], [53, 35, 73, 2, "config"], [53, 41, 73, 8], [53, 43, 74, 2, "indentation"], [53, 54, 74, 13], [53, 56, 75, 2, "depth"], [53, 61, 75, 7], [53, 63, 76, 2, "refs"], [53, 67, 76, 6], [53, 69, 77, 2, "printer"], [53, 76, 77, 9], [53, 81, 78, 5], [54, 4, 79, 2], [55, 4, 80, 2], [56, 4, 81, 2], [56, 8, 81, 8, "name"], [56, 12, 81, 12], [56, 15, 81, 15, "getImmutableName"], [56, 31, 81, 31], [56, 32, 81, 32, "val"], [56, 35, 81, 35], [56, 36, 81, 36, "_name"], [56, 41, 81, 41], [56, 45, 81, 45], [56, 53, 81, 53], [56, 54, 81, 54], [57, 4, 82, 2], [57, 11, 82, 9], [57, 13, 82, 11, "depth"], [57, 18, 82, 16], [57, 21, 82, 19, "config"], [57, 27, 82, 25], [57, 28, 82, 26, "max<PERSON><PERSON><PERSON>"], [57, 36, 82, 34], [57, 39, 83, 6, "printAsLeaf"], [57, 50, 83, 17], [57, 51, 83, 18, "name"], [57, 55, 83, 22], [57, 56, 83, 23], [57, 59, 84, 6], [57, 62, 84, 9, "name"], [57, 66, 84, 13], [57, 69, 84, 16, "SPACE"], [57, 74, 84, 21], [57, 78, 84, 25], [57, 79, 84, 26], [57, 80, 84, 27], [57, 82, 84, 29, "_collections"], [57, 94, 84, 41], [57, 95, 84, 42, "printIteratorEntries"], [57, 115, 84, 62], [57, 117, 85, 8, "getRecordEntries"], [57, 133, 85, 24], [57, 134, 85, 25, "val"], [57, 137, 85, 28], [57, 138, 85, 29], [57, 140, 86, 8, "config"], [57, 146, 86, 14], [57, 148, 87, 8, "indentation"], [57, 159, 87, 19], [57, 161, 88, 8, "depth"], [57, 166, 88, 13], [57, 168, 89, 8, "refs"], [57, 172, 89, 12], [57, 174, 90, 8, "printer"], [57, 181, 91, 6], [57, 182, 91, 7], [57, 185, 91, 10], [58, 2, 92, 0], [58, 3, 92, 1], [59, 2, 93, 0], [59, 6, 93, 6, "printImmutableSeq"], [59, 23, 93, 23], [59, 26, 93, 26, "printImmutableSeq"], [59, 27, 93, 27, "val"], [59, 30, 93, 30], [59, 32, 93, 32, "config"], [59, 38, 93, 38], [59, 40, 93, 40, "indentation"], [59, 51, 93, 51], [59, 53, 93, 53, "depth"], [59, 58, 93, 58], [59, 60, 93, 60, "refs"], [59, 64, 93, 64], [59, 66, 93, 66, "printer"], [59, 73, 93, 73], [59, 78, 93, 78], [60, 4, 94, 2], [60, 8, 94, 8, "name"], [60, 12, 94, 12], [60, 15, 94, 15, "getImmutableName"], [60, 31, 94, 31], [60, 32, 94, 32], [60, 37, 94, 37], [60, 38, 94, 38], [61, 4, 95, 2], [61, 8, 95, 6], [61, 10, 95, 8, "depth"], [61, 15, 95, 13], [61, 18, 95, 16, "config"], [61, 24, 95, 22], [61, 25, 95, 23, "max<PERSON><PERSON><PERSON>"], [61, 33, 95, 31], [61, 35, 95, 33], [62, 6, 96, 4], [62, 13, 96, 11, "printAsLeaf"], [62, 24, 96, 22], [62, 25, 96, 23, "name"], [62, 29, 96, 27], [62, 30, 96, 28], [63, 4, 97, 2], [64, 4, 98, 2], [64, 8, 98, 6, "val"], [64, 11, 98, 9], [64, 12, 98, 10, "IS_KEYED_SENTINEL"], [64, 29, 98, 27], [64, 30, 98, 28], [64, 32, 98, 30], [65, 6, 99, 4], [65, 13, 99, 11], [65, 16, 99, 14, "name"], [65, 20, 99, 18], [65, 23, 99, 21, "SPACE"], [65, 28, 99, 26], [66, 6, 100, 6], [67, 6, 101, 6, "val"], [67, 9, 101, 9], [67, 10, 101, 10, "_iter"], [67, 15, 101, 15], [67, 19, 101, 19, "val"], [67, 22, 101, 22], [67, 23, 101, 23, "_object"], [67, 30, 101, 30], [67, 33, 102, 10], [67, 34, 102, 11], [67, 35, 102, 12], [67, 37, 102, 14, "_collections"], [67, 49, 102, 26], [67, 50, 102, 27, "printIteratorEntries"], [67, 70, 102, 47], [67, 72, 103, 12, "val"], [67, 75, 103, 15], [67, 76, 103, 16, "entries"], [67, 83, 103, 23], [67, 84, 103, 24], [67, 85, 103, 25], [67, 87, 104, 12, "config"], [67, 93, 104, 18], [67, 95, 105, 12, "indentation"], [67, 106, 105, 23], [67, 108, 106, 12, "depth"], [67, 113, 106, 17], [67, 115, 107, 12, "refs"], [67, 119, 107, 16], [67, 121, 108, 12, "printer"], [67, 128, 109, 10], [67, 129, 109, 11], [67, 132, 110, 10, "LAZY"], [67, 136, 110, 14], [67, 139, 111, 7], [68, 4, 112, 2], [69, 4, 113, 2], [69, 11, 113, 9], [69, 14, 113, 12, "name"], [69, 18, 113, 16], [69, 21, 113, 19, "SPACE"], [69, 26, 113, 24], [69, 30, 114, 4, "val"], [69, 33, 114, 7], [69, 34, 114, 8, "_iter"], [69, 39, 114, 13], [70, 4, 115, 4], [71, 4, 116, 4, "val"], [71, 7, 116, 7], [71, 8, 116, 8, "_array"], [71, 14, 116, 14], [72, 4, 117, 4], [73, 4, 118, 4, "val"], [73, 7, 118, 7], [73, 8, 118, 8, "_collection"], [73, 19, 118, 19], [74, 4, 119, 4], [75, 4, 120, 4, "val"], [75, 7, 120, 7], [75, 8, 120, 8, "_iterable"], [75, 17, 120, 17], [75, 18, 120, 18], [76, 4, 120, 18], [76, 6, 121, 8], [76, 7, 121, 9], [76, 8, 121, 10], [76, 10, 121, 12, "_collections"], [76, 22, 121, 24], [76, 23, 121, 25, "printIteratorValues"], [76, 42, 121, 44], [76, 44, 122, 10, "val"], [76, 47, 122, 13], [76, 48, 122, 14, "values"], [76, 54, 122, 20], [76, 55, 122, 21], [76, 56, 122, 22], [76, 58, 123, 10, "config"], [76, 64, 123, 16], [76, 66, 124, 10, "indentation"], [76, 77, 124, 21], [76, 79, 125, 10, "depth"], [76, 84, 125, 15], [76, 86, 126, 10, "refs"], [76, 90, 126, 14], [76, 92, 127, 10, "printer"], [76, 99, 128, 8], [76, 100, 128, 9], [76, 103, 129, 8, "LAZY"], [76, 107, 129, 12], [76, 110, 130, 5], [77, 2, 131, 0], [77, 3, 131, 1], [78, 2, 132, 0], [78, 6, 132, 6, "printImmutableValues"], [78, 26, 132, 26], [78, 29, 132, 29, "printImmutableValues"], [78, 30, 133, 2, "val"], [78, 33, 133, 5], [78, 35, 134, 2, "config"], [78, 41, 134, 8], [78, 43, 135, 2, "indentation"], [78, 54, 135, 13], [78, 56, 136, 2, "depth"], [78, 61, 136, 7], [78, 63, 137, 2, "refs"], [78, 67, 137, 6], [78, 69, 138, 2, "printer"], [78, 76, 138, 9], [78, 78, 139, 2, "type"], [78, 82, 139, 6], [78, 87, 141, 2], [78, 89, 141, 4, "depth"], [78, 94, 141, 9], [78, 97, 141, 12, "config"], [78, 103, 141, 18], [78, 104, 141, 19, "max<PERSON><PERSON><PERSON>"], [78, 112, 141, 27], [78, 115, 142, 6, "printAsLeaf"], [78, 126, 142, 17], [78, 127, 142, 18, "getImmutableName"], [78, 143, 142, 34], [78, 144, 142, 35, "type"], [78, 148, 142, 39], [78, 149, 142, 40], [78, 150, 142, 41], [78, 153, 143, 6], [78, 156, 143, 9, "getImmutableName"], [78, 172, 143, 25], [78, 173, 143, 26, "type"], [78, 177, 143, 30], [78, 178, 143, 31], [78, 181, 143, 34, "SPACE"], [78, 186, 143, 39], [78, 190, 143, 43], [78, 191, 143, 44], [78, 192, 143, 45], [78, 194, 144, 6, "_collections"], [78, 206, 144, 18], [78, 207, 144, 19, "printIteratorValues"], [78, 226, 144, 38], [78, 228, 145, 8, "val"], [78, 231, 145, 11], [78, 232, 145, 12, "values"], [78, 238, 145, 18], [78, 239, 145, 19], [78, 240, 145, 20], [78, 242, 146, 8, "config"], [78, 248, 146, 14], [78, 250, 147, 8, "indentation"], [78, 261, 147, 19], [78, 263, 148, 8, "depth"], [78, 268, 148, 13], [78, 270, 149, 8, "refs"], [78, 274, 149, 12], [78, 276, 150, 8, "printer"], [78, 283, 151, 6], [78, 284, 151, 7], [78, 287, 151, 10], [79, 2, 152, 0], [79, 6, 152, 6, "serialize"], [79, 15, 152, 15], [79, 18, 152, 18, "serialize"], [79, 19, 152, 19, "val"], [79, 22, 152, 22], [79, 24, 152, 24, "config"], [79, 30, 152, 30], [79, 32, 152, 32, "indentation"], [79, 43, 152, 43], [79, 45, 152, 45, "depth"], [79, 50, 152, 50], [79, 52, 152, 52, "refs"], [79, 56, 152, 56], [79, 58, 152, 58, "printer"], [79, 65, 152, 65], [79, 70, 152, 70], [80, 4, 153, 2], [80, 8, 153, 6, "val"], [80, 11, 153, 9], [80, 12, 153, 10, "IS_MAP_SENTINEL"], [80, 27, 153, 25], [80, 28, 153, 26], [80, 30, 153, 28], [81, 6, 154, 4], [81, 13, 154, 11, "printImmutableEntries"], [81, 34, 154, 32], [81, 35, 155, 6, "val"], [81, 38, 155, 9], [81, 40, 156, 6, "config"], [81, 46, 156, 12], [81, 48, 157, 6, "indentation"], [81, 59, 157, 17], [81, 61, 158, 6, "depth"], [81, 66, 158, 11], [81, 68, 159, 6, "refs"], [81, 72, 159, 10], [81, 74, 160, 6, "printer"], [81, 81, 160, 13], [81, 83, 161, 6, "val"], [81, 86, 161, 9], [81, 87, 161, 10, "IS_ORDERED_SENTINEL"], [81, 106, 161, 29], [81, 107, 161, 30], [81, 110, 161, 33], [81, 122, 161, 45], [81, 125, 161, 48], [81, 130, 162, 4], [81, 131, 162, 5], [82, 4, 163, 2], [83, 4, 164, 2], [83, 8, 164, 6, "val"], [83, 11, 164, 9], [83, 12, 164, 10, "IS_LIST_SENTINEL"], [83, 28, 164, 26], [83, 29, 164, 27], [83, 31, 164, 29], [84, 6, 165, 4], [84, 13, 165, 11, "printImmutableValues"], [84, 33, 165, 31], [84, 34, 166, 6, "val"], [84, 37, 166, 9], [84, 39, 167, 6, "config"], [84, 45, 167, 12], [84, 47, 168, 6, "indentation"], [84, 58, 168, 17], [84, 60, 169, 6, "depth"], [84, 65, 169, 11], [84, 67, 170, 6, "refs"], [84, 71, 170, 10], [84, 73, 171, 6, "printer"], [84, 80, 171, 13], [84, 82, 172, 6], [84, 88, 173, 4], [84, 89, 173, 5], [85, 4, 174, 2], [86, 4, 175, 2], [86, 8, 175, 6, "val"], [86, 11, 175, 9], [86, 12, 175, 10, "IS_SET_SENTINEL"], [86, 27, 175, 25], [86, 28, 175, 26], [86, 30, 175, 28], [87, 6, 176, 4], [87, 13, 176, 11, "printImmutableValues"], [87, 33, 176, 31], [87, 34, 177, 6, "val"], [87, 37, 177, 9], [87, 39, 178, 6, "config"], [87, 45, 178, 12], [87, 47, 179, 6, "indentation"], [87, 58, 179, 17], [87, 60, 180, 6, "depth"], [87, 65, 180, 11], [87, 67, 181, 6, "refs"], [87, 71, 181, 10], [87, 73, 182, 6, "printer"], [87, 80, 182, 13], [87, 82, 183, 6, "val"], [87, 85, 183, 9], [87, 86, 183, 10, "IS_ORDERED_SENTINEL"], [87, 105, 183, 29], [87, 106, 183, 30], [87, 109, 183, 33], [87, 121, 183, 45], [87, 124, 183, 48], [87, 129, 184, 4], [87, 130, 184, 5], [88, 4, 185, 2], [89, 4, 186, 2], [89, 8, 186, 6, "val"], [89, 11, 186, 9], [89, 12, 186, 10, "IS_STACK_SENTINEL"], [89, 29, 186, 27], [89, 30, 186, 28], [89, 32, 186, 30], [90, 6, 187, 4], [90, 13, 187, 11, "printImmutableValues"], [90, 33, 187, 31], [90, 34, 188, 6, "val"], [90, 37, 188, 9], [90, 39, 189, 6, "config"], [90, 45, 189, 12], [90, 47, 190, 6, "indentation"], [90, 58, 190, 17], [90, 60, 191, 6, "depth"], [90, 65, 191, 11], [90, 67, 192, 6, "refs"], [90, 71, 192, 10], [90, 73, 193, 6, "printer"], [90, 80, 193, 13], [90, 82, 194, 6], [90, 89, 195, 4], [90, 90, 195, 5], [91, 4, 196, 2], [92, 4, 197, 2], [92, 8, 197, 6, "val"], [92, 11, 197, 9], [92, 12, 197, 10, "IS_SEQ_SENTINEL"], [92, 27, 197, 25], [92, 28, 197, 26], [92, 30, 197, 28], [93, 6, 198, 4], [93, 13, 198, 11, "printImmutableSeq"], [93, 30, 198, 28], [93, 31, 198, 29, "val"], [93, 34, 198, 32], [93, 36, 198, 34, "config"], [93, 42, 198, 40], [93, 44, 198, 42, "indentation"], [93, 55, 198, 53], [93, 57, 198, 55, "depth"], [93, 62, 198, 60], [93, 64, 198, 62, "refs"], [93, 68, 198, 66], [93, 70, 198, 68, "printer"], [93, 77, 198, 75], [93, 78, 198, 76], [94, 4, 199, 2], [96, 4, 201, 2], [97, 4, 202, 2], [97, 11, 202, 9, "printImmutableRecord"], [97, 31, 202, 29], [97, 32, 202, 30, "val"], [97, 35, 202, 33], [97, 37, 202, 35, "config"], [97, 43, 202, 41], [97, 45, 202, 43, "indentation"], [97, 56, 202, 54], [97, 58, 202, 56, "depth"], [97, 63, 202, 61], [97, 65, 202, 63, "refs"], [97, 69, 202, 67], [97, 71, 202, 69, "printer"], [97, 78, 202, 76], [97, 79, 202, 77], [98, 2, 203, 0], [98, 3, 203, 1], [100, 2, 205, 0], [101, 2, 206, 0], [102, 2, 207, 0, "exports"], [102, 9, 207, 7], [102, 10, 207, 8, "serialize"], [102, 19, 207, 17], [102, 22, 207, 20, "serialize"], [102, 31, 207, 29], [103, 2, 208, 0], [103, 6, 208, 6, "test"], [103, 10, 208, 10], [103, 13, 208, 13, "val"], [103, 16, 208, 16], [103, 20, 209, 2, "val"], [103, 23, 209, 5], [103, 28, 210, 3, "val"], [103, 31, 210, 6], [103, 32, 210, 7, "IS_ITERABLE_SENTINEL"], [103, 52, 210, 27], [103, 53, 210, 28], [103, 58, 210, 33], [103, 62, 210, 37], [103, 66, 210, 41, "val"], [103, 69, 210, 44], [103, 70, 210, 45, "IS_RECORD_SENTINEL"], [103, 88, 210, 63], [103, 89, 210, 64], [103, 94, 210, 69], [103, 98, 210, 73], [103, 99, 210, 74], [104, 2, 211, 0, "exports"], [104, 9, 211, 7], [104, 10, 211, 8, "test"], [104, 14, 211, 12], [104, 17, 211, 15, "test"], [104, 21, 211, 19], [105, 2, 212, 0], [105, 6, 212, 6, "plugin"], [105, 12, 212, 12], [105, 15, 212, 15], [106, 4, 213, 2, "serialize"], [106, 13, 213, 11], [107, 4, 214, 2, "test"], [108, 2, 215, 0], [108, 3, 215, 1], [109, 2, 216, 0], [109, 6, 216, 4, "_default"], [109, 14, 216, 12], [109, 17, 216, 15, "plugin"], [109, 23, 216, 21], [110, 2, 217, 0, "exports"], [110, 9, 217, 7], [110, 10, 217, 8, "default"], [110, 17, 217, 15], [110, 20, 217, 18, "_default"], [110, 28, 217, 26], [111, 0, 217, 27], [111, 3]], "functionMap": {"names": ["<global>", "getImmutableName", "printAsLeaf", "printImmutableEntries", "getRecordEntries", "next", "printImmutableRecord", "printImmutableSeq", "printImmutableValues", "serialize", "test"], "mappings": "AAA;yBCwB,2BD;oBEC,mBF;8BGI;UHmB;AII;ICG;KDY;CJE;6BMC;CNqB;0BOC;CPsC;6BQC;URmB;kBSC;CTmD;aUK;0EVE"}}, "type": "js/module"}]}