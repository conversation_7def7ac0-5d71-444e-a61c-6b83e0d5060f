{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.findLastIndex = findLastIndex;\n  function findLastIndex(array, callback) {\n    for (var i = array.length - 1; i >= 0; i--) {\n      if (callback(array[i])) {\n        return i;\n      }\n    }\n    return -1;\n  }\n});", "lineCount": 16, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "findLastIndex"], [7, 23, 1, 13], [7, 26, 1, 13, "findLastIndex"], [7, 39, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "findLastIndex"], [8, 24, 3, 29, "findLastIndex"], [8, 25, 3, 30, "array"], [8, 30, 3, 35], [8, 32, 3, 37, "callback"], [8, 40, 3, 45], [8, 42, 3, 47], [9, 4, 4, 2], [9, 9, 4, 7], [9, 13, 4, 11, "i"], [9, 14, 4, 12], [9, 17, 4, 15, "array"], [9, 22, 4, 20], [9, 23, 4, 21, "length"], [9, 29, 4, 27], [9, 32, 4, 30], [9, 33, 4, 31], [9, 35, 4, 33, "i"], [9, 36, 4, 34], [9, 40, 4, 38], [9, 41, 4, 39], [9, 43, 4, 41, "i"], [9, 44, 4, 42], [9, 46, 4, 44], [9, 48, 4, 46], [10, 6, 5, 4], [10, 10, 5, 8, "callback"], [10, 18, 5, 16], [10, 19, 5, 17, "array"], [10, 24, 5, 22], [10, 25, 5, 23, "i"], [10, 26, 5, 24], [10, 27, 5, 25], [10, 28, 5, 26], [10, 30, 5, 28], [11, 8, 6, 6], [11, 15, 6, 13, "i"], [11, 16, 6, 14], [12, 6, 7, 4], [13, 4, 8, 2], [14, 4, 9, 2], [14, 11, 9, 9], [14, 12, 9, 10], [14, 13, 9, 11], [15, 2, 10, 0], [16, 0, 10, 1], [16, 3]], "functionMap": {"names": ["<global>", "findLastIndex"], "mappings": "AAA;OCE;CDO"}}, "type": "js/module"}]}