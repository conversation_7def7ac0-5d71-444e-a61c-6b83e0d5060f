{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Directions = exports.DiagonalDirections = void 0;\n  var RIGHT = 1;\n  var LEFT = 2;\n  var UP = 4;\n  var DOWN = 8;\n\n  // Public interface\n  var Directions = exports.Directions = {\n    RIGHT: RIGHT,\n    LEFT: LEFT,\n    UP: UP,\n    DOWN: DOWN\n  };\n\n  // Internal interface\n  var DiagonalDirections = exports.DiagonalDirections = {\n    UP_RIGHT: UP | RIGHT,\n    DOWN_RIGHT: DOWN | RIGHT,\n    UP_LEFT: UP | LEFT,\n    DOWN_LEFT: DOWN | LEFT\n  };\n\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value\n\n  // eslint-disable-next-line @typescript-eslint/no-redeclare\n});", "lineCount": 30, "map": [[6, 2, 1, 0], [6, 6, 1, 6, "RIGHT"], [6, 11, 1, 11], [6, 14, 1, 14], [6, 15, 1, 15], [7, 2, 2, 0], [7, 6, 2, 6, "LEFT"], [7, 10, 2, 10], [7, 13, 2, 13], [7, 14, 2, 14], [8, 2, 3, 0], [8, 6, 3, 6, "UP"], [8, 8, 3, 8], [8, 11, 3, 11], [8, 12, 3, 12], [9, 2, 4, 0], [9, 6, 4, 6, "DOWN"], [9, 10, 4, 10], [9, 13, 4, 13], [9, 14, 4, 14], [11, 2, 6, 0], [12, 2, 7, 7], [12, 6, 7, 13, "Directions"], [12, 16, 7, 23], [12, 19, 7, 23, "exports"], [12, 26, 7, 23], [12, 27, 7, 23, "Directions"], [12, 37, 7, 23], [12, 40, 7, 26], [13, 4, 8, 2, "RIGHT"], [13, 9, 8, 7], [13, 11, 8, 9, "RIGHT"], [13, 16, 8, 14], [14, 4, 9, 2, "LEFT"], [14, 8, 9, 6], [14, 10, 9, 8, "LEFT"], [14, 14, 9, 12], [15, 4, 10, 2, "UP"], [15, 6, 10, 4], [15, 8, 10, 6, "UP"], [15, 10, 10, 8], [16, 4, 11, 2, "DOWN"], [16, 8, 11, 6], [16, 10, 11, 8, "DOWN"], [17, 2, 12, 0], [17, 3, 12, 10], [19, 2, 14, 0], [20, 2, 15, 7], [20, 6, 15, 13, "DiagonalDirections"], [20, 24, 15, 31], [20, 27, 15, 31, "exports"], [20, 34, 15, 31], [20, 35, 15, 31, "DiagonalDirections"], [20, 53, 15, 31], [20, 56, 15, 34], [21, 4, 16, 2, "UP_RIGHT"], [21, 12, 16, 10], [21, 14, 16, 12, "UP"], [21, 16, 16, 14], [21, 19, 16, 17, "RIGHT"], [21, 24, 16, 22], [22, 4, 17, 2, "DOWN_RIGHT"], [22, 14, 17, 12], [22, 16, 17, 14, "DOWN"], [22, 20, 17, 18], [22, 23, 17, 21, "RIGHT"], [22, 28, 17, 26], [23, 4, 18, 2, "UP_LEFT"], [23, 11, 18, 9], [23, 13, 18, 11, "UP"], [23, 15, 18, 13], [23, 18, 18, 16, "LEFT"], [23, 22, 18, 20], [24, 4, 19, 2, "DOWN_LEFT"], [24, 13, 19, 11], [24, 15, 19, 13, "DOWN"], [24, 19, 19, 17], [24, 22, 19, 20, "LEFT"], [25, 2, 20, 0], [25, 3, 20, 10], [27, 2, 22, 0], [29, 2, 24, 0], [30, 0, 24, 0], [30, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}