{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../Core/registerCallableModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 68}}], "key": "U8mBYKYrvfb4dzGw9nGcccGL2Ww=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _registerCallableModule = _interopRequireDefault(require(_dependencyMap[1], \"../Core/registerCallableModule\"));\n  var RCTEventEmitter = {\n    register(eventEmitter) {\n      (0, _registerCallableModule.default)('RCTEventEmitter', eventEmitter);\n    }\n  };\n  var _default = exports.default = RCTEventEmitter;\n});", "lineCount": 16, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 13, 0], [9, 6, 13, 0, "_registerCallableModule"], [9, 29, 13, 0], [9, 32, 13, 0, "_interopRequireDefault"], [9, 54, 13, 0], [9, 55, 13, 0, "require"], [9, 62, 13, 0], [9, 63, 13, 0, "_dependencyMap"], [9, 77, 13, 0], [10, 2, 15, 0], [10, 6, 15, 6, "RCTEventEmitter"], [10, 21, 15, 21], [10, 24, 15, 24], [11, 4, 16, 2, "register"], [11, 12, 16, 10, "register"], [11, 13, 16, 11, "eventEmitter"], [11, 25, 16, 28], [11, 27, 16, 30], [12, 6, 17, 4], [12, 10, 17, 4, "registerCallableModule"], [12, 41, 17, 26], [12, 43, 17, 27], [12, 60, 17, 44], [12, 62, 17, 46, "eventEmitter"], [12, 74, 17, 58], [12, 75, 17, 59], [13, 4, 18, 2], [14, 2, 19, 0], [14, 3, 19, 1], [15, 2, 19, 2], [15, 6, 19, 2, "_default"], [15, 14, 19, 2], [15, 17, 19, 2, "exports"], [15, 24, 19, 2], [15, 25, 19, 2, "default"], [15, 32, 19, 2], [15, 35, 21, 15, "RCTEventEmitter"], [15, 50, 21, 30], [16, 0, 21, 30], [16, 3]], "functionMap": {"names": ["<global>", "register"], "mappings": "AAA;ECe;GDE"}}, "type": "js/module"}]}