{"dependencies": [{"name": "../animation/styleAnimation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 65, "index": 79}}], "key": "EJbWbd3zgRMEW833TGJ0tnsQ/Qk=", "exportNames": ["*"]}}, {"name": "../commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 221}, "end": {"line": 9, "column": 53, "index": 274}}], "key": "dQSfS57Pf/C96+Vvd1rktbJJov4=", "exportNames": ["*"]}}, {"name": "../mutables", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 275}, "end": {"line": 10, "column": 44, "index": 319}}], "key": "IDRZr6dviDL7zsjT4xI3gPwtNYo=", "exportNames": ["*"]}}, {"name": "../threads", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 320}, "end": {"line": 11, "column": 48, "index": 368}}], "key": "K1yKq+VUoHdgwBY7Fz9TrE1h5uU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _styleAnimation = require(_dependencyMap[0], \"../animation/styleAnimation\");\n  var _commonTypes = require(_dependencyMap[1], \"../commonTypes\");\n  var _mutables = require(_dependencyMap[2], \"../mutables\");\n  var _threads = require(_dependencyMap[3], \"../threads\");\n  var TAG_OFFSET = 1e9;\n  var _worklet_10675557011176_init_data = {\n    code: \"function startObservingProgress_animationsManagerTs1(tag,sharedValue,animationType){const{LayoutAnimationType,TAG_OFFSET}=this.__closure;const isSharedTransition=animationType===LayoutAnimationType.SHARED_ELEMENT_TRANSITION;sharedValue.addListener(tag+TAG_OFFSET,function(){global._notifyAboutProgress(tag,sharedValue.value,isSharedTransition);});}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\layoutReanimation\\\\animationsManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"startObservingProgress_animationsManagerTs1\\\",\\\"tag\\\",\\\"sharedValue\\\",\\\"animationType\\\",\\\"LayoutAnimationType\\\",\\\"TAG_OFFSET\\\",\\\"__closure\\\",\\\"isSharedTransition\\\",\\\"SHARED_ELEMENT_TRANSITION\\\",\\\"addListener\\\",\\\"global\\\",\\\"_notifyAboutProgress\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/layoutReanimation/animationsManager.ts\\\"],\\\"mappings\\\":\\\"AAcA,SAAAA,2CAGEA,CAAAC,GAAA,CAAAC,WACM,CAAAC,aAAA,QAAAC,mBAAA,CAAAC,UAAA,OAAAC,SAAA,CAEN,KAAM,CAAAC,kBAAkB,CACtBJ,aAAa,GAAKC,mBAAmB,CAACI,yBAAyB,CACjEN,WAAW,CAACO,WAAW,CAACR,GAAG,CAAGI,UAAU,CAAE,UAAM,CAC9CK,MAAM,CAACC,oBAAoB,CAACV,GAAG,CAAEC,WAAW,CAACU,KAAK,CAAEL,kBAAkB,CAAC,CACzE,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var startObservingProgress = function () {\n    var _e = [new global.Error(), -3, -27];\n    var startObservingProgress = function (tag, sharedValue, animationType) {\n      var isSharedTransition = animationType === _commonTypes.LayoutAnimationType.SHARED_ELEMENT_TRANSITION;\n      sharedValue.addListener(tag + TAG_OFFSET, () => {\n        global._notifyAboutProgress(tag, sharedValue.value, isSharedTransition);\n      });\n    };\n    startObservingProgress.__closure = {\n      LayoutAnimationType: _commonTypes.LayoutAnimationType,\n      TAG_OFFSET\n    };\n    startObservingProgress.__workletHash = 10675557011176;\n    startObservingProgress.__initData = _worklet_10675557011176_init_data;\n    startObservingProgress.__stackDetails = _e;\n    return startObservingProgress;\n  }();\n  var _worklet_16958055875360_init_data = {\n    code: \"function stopObservingProgress_animationsManagerTs2(tag,sharedValue,removeView=false){const{TAG_OFFSET}=this.__closure;sharedValue.removeListener(tag+TAG_OFFSET);global._notifyAboutEnd(tag,removeView);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\layoutReanimation\\\\animationsManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"stopObservingProgress_animationsManagerTs2\\\",\\\"tag\\\",\\\"sharedValue\\\",\\\"removeView\\\",\\\"TAG_OFFSET\\\",\\\"__closure\\\",\\\"removeListener\\\",\\\"global\\\",\\\"_notifyAboutEnd\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/layoutReanimation/animationsManager.ts\\\"],\\\"mappings\\\":\\\"AA2BA,SAAAA,0CAGEA,CAAAC,GAAA,CAAAC,WACD,CAAOC,UAAA,cAAAC,UAAA,OAAAC,SAAA,CAENH,WAAW,CAACI,cAAc,CAACL,GAAG,CAAGG,UAAU,CAAC,CAC5CG,MAAM,CAACC,eAAe,CAACP,GAAG,CAAEE,UAAU,CAAC,CACzC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var stopObservingProgress = function () {\n    var _e = [new global.Error(), -2, -27];\n    var stopObservingProgress = function (tag, sharedValue) {\n      var removeView = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      sharedValue.removeListener(tag + TAG_OFFSET);\n      global._notifyAboutEnd(tag, removeView);\n    };\n    stopObservingProgress.__closure = {\n      TAG_OFFSET\n    };\n    stopObservingProgress.__workletHash = 16958055875360;\n    stopObservingProgress.__initData = _worklet_16958055875360_init_data;\n    stopObservingProgress.__stackDetails = _e;\n    return stopObservingProgress;\n  }();\n  var _worklet_5423312481392_init_data = {\n    code: \"function createLayoutAnimationManager_animationsManagerTs3(){const{LayoutAnimationType,makeMutableUI,stopObservingProgress,withStyleAnimation,startObservingProgress}=this.__closure;const currentAnimationForTag=new Map();const mutableValuesForTag=new Map();return{start:function(tag,type,yogaValues,config){if(type===LayoutAnimationType.SHARED_ELEMENT_TRANSITION_PROGRESS){global.ProgressTransitionRegister.onTransitionStart(tag,yogaValues);return;}const style=config(yogaValues);let currentAnimation=style.animations;const previousAnimation=currentAnimationForTag.get(tag);if(previousAnimation){currentAnimation={...previousAnimation,...style.animations};}currentAnimationForTag.set(tag,currentAnimation);let value=mutableValuesForTag.get(tag);if(value===undefined){value=makeMutableUI(style.initialValues);mutableValuesForTag.set(tag,value);}else{stopObservingProgress(tag,value);value._value=style.initialValues;}const animation=withStyleAnimation(currentAnimation);animation.callback=function(finished){if(finished){currentAnimationForTag.delete(tag);mutableValuesForTag.delete(tag);const shouldRemoveView=type===LayoutAnimationType.EXITING;stopObservingProgress(tag,value,shouldRemoveView);}style.callback&&style.callback(finished===undefined?false:finished);};startObservingProgress(tag,value,type);value.value=animation;},stop:function(tag){const value=mutableValuesForTag.get(tag);if(!value){return;}stopObservingProgress(tag,value);}};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\layoutReanimation\\\\animationsManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"createLayoutAnimationManager_animationsManagerTs3\\\",\\\"LayoutAnimationType\\\",\\\"makeMutableUI\\\",\\\"stopObservingProgress\\\",\\\"withStyleAnimation\\\",\\\"startObservingProgress\\\",\\\"__closure\\\",\\\"currentAnimationForTag\\\",\\\"Map\\\",\\\"mutableValuesForTag\\\",\\\"start\\\",\\\"tag\\\",\\\"type\\\",\\\"yogaValues\\\",\\\"config\\\",\\\"SHARED_ELEMENT_TRANSITION_PROGRESS\\\",\\\"global\\\",\\\"ProgressTransitionRegister\\\",\\\"onTransitionStart\\\",\\\"style\\\",\\\"currentAnimation\\\",\\\"animations\\\",\\\"previousAnimation\\\",\\\"get\\\",\\\"set\\\",\\\"value\\\",\\\"undefined\\\",\\\"initialValues\\\",\\\"_value\\\",\\\"animation\\\",\\\"callback\\\",\\\"finished\\\",\\\"delete\\\",\\\"shouldRemoveView\\\",\\\"EXITING\\\",\\\"stop\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/layoutReanimation/animationsManager.ts\\\"],\\\"mappings\\\":\\\"AAqCA,SAAAA,iDAGEA,CAAA,QAAAC,mBAAA,CAAAC,aAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,sBAAA,OAAAC,SAAA,CAEA,KAAM,CAAAC,sBAAsB,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACxC,KAAM,CAAAC,mBAAmB,CAAG,GAAI,CAAAD,GAAG,CAAC,CAAC,CAErC,MAAO,CACLE,KAAK,SAAAA,CACHC,GAAW,CACXC,IAAyB,CAKzBC,UAAqD,CACrDC,MAEoB,CACpB,CACA,GAAIF,IAAI,GAAKX,mBAAmB,CAACc,kCAAkC,CAAE,CACnEC,MAAM,CAACC,0BAA0B,CAACC,iBAAiB,CAACP,GAAG,CAAEE,UAAU,CAAC,CACpE,OACF,CAEA,KAAM,CAAAM,KAAK,CAAGL,MAAM,CAACD,UAAU,CAAC,CAChC,GAAI,CAAAO,gBAAgB,CAAGD,KAAK,CAACE,UAAU,CAIvC,KAAM,CAAAC,iBAAiB,CAAGf,sBAAsB,CAACgB,GAAG,CAACZ,GAAG,CAAC,CACzD,GAAIW,iBAAiB,CAAE,CACrBF,gBAAgB,CAAG,CAAE,GAAGE,iBAAiB,CAAE,GAAGH,KAAK,CAACE,UAAW,CAAC,CAClE,CACAd,sBAAsB,CAACiB,GAAG,CAACb,GAAG,CAAES,gBAAgB,CAAC,CAEjD,GAAI,CAAAK,KAAK,CAAGhB,mBAAmB,CAACc,GAAG,CAACZ,GAAG,CAAC,CACxC,GAAIc,KAAK,GAAKC,SAAS,CAAE,CACvBD,KAAK,CAAGvB,aAAa,CAACiB,KAAK,CAACQ,aAAa,CAAC,CAC1ClB,mBAAmB,CAACe,GAAG,CAACb,GAAG,CAAEc,KAAK,CAAC,CACrC,CAAC,IAAM,CACLtB,qBAAqB,CAACQ,GAAG,CAAEc,KAAK,CAAC,CACjCA,KAAK,CAACG,MAAM,CAAGT,KAAK,CAACQ,aAAa,CACpC,CAGA,KAAM,CAAAE,SAAS,CAAGzB,kBAAkB,CAACgB,gBAAgB,CAAC,CAEtDS,SAAS,CAACC,QAAQ,CAAG,SAACC,QAAkB,CAAK,CAC3C,GAAIA,QAAQ,CAAE,CACZxB,sBAAsB,CAACyB,MAAM,CAACrB,GAAG,CAAC,CAClCF,mBAAmB,CAACuB,MAAM,CAACrB,GAAG,CAAC,CAC/B,KAAM,CAAAsB,gBAAgB,CAAGrB,IAAI,GAAKX,mBAAmB,CAACiC,OAAO,CAC7D/B,qBAAqB,CAACQ,GAAG,CAAEc,KAAK,CAAEQ,gBAAgB,CAAC,CACrD,CACAd,KAAK,CAACW,QAAQ,EACZX,KAAK,CAACW,QAAQ,CAACC,QAAQ,GAAKL,SAAS,CAAG,KAAK,CAAGK,QAAQ,CAAC,CAC7D,CAAC,CAED1B,sBAAsB,CAACM,GAAG,CAAEc,KAAK,CAAEb,IAAI,CAAC,CACxCa,KAAK,CAACA,KAAK,CAAGI,SAAS,CACzB,CAAC,CACDM,IAAI,SAAAA,CAACxB,GAAW,CAAE,CAChB,KAAM,CAAAc,KAAK,CAAGhB,mBAAmB,CAACc,GAAG,CAACZ,GAAG,CAAC,CAC1C,GAAI,CAACc,KAAK,CAAE,CACV,OACF,CACAtB,qBAAqB,CAACQ,GAAG,CAAEc,KAAK,CAAC,CACnC,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var createLayoutAnimationManager = function () {\n    var _e = [new global.Error(), -6, -27];\n    var createLayoutAnimationManager = function () {\n      var currentAnimationForTag = new Map();\n      var mutableValuesForTag = new Map();\n      return {\n        start(tag, type,\n        /**\n         * CreateLayoutAnimationManager creates an animation manager for both\n         * Layout animations and Shared Transition Elements animations.\n         */\n        yogaValues, config) {\n          if (type === _commonTypes.LayoutAnimationType.SHARED_ELEMENT_TRANSITION_PROGRESS) {\n            global.ProgressTransitionRegister.onTransitionStart(tag, yogaValues);\n            return;\n          }\n          var style = config(yogaValues);\n          var currentAnimation = style.animations;\n\n          // When layout animation is requested, but a previous one is still running, we merge\n          // new layout animation targets into the ongoing animation\n          var previousAnimation = currentAnimationForTag.get(tag);\n          if (previousAnimation) {\n            currentAnimation = {\n              ...previousAnimation,\n              ...style.animations\n            };\n          }\n          currentAnimationForTag.set(tag, currentAnimation);\n          var value = mutableValuesForTag.get(tag);\n          if (value === undefined) {\n            value = (0, _mutables.makeMutableUI)(style.initialValues);\n            mutableValuesForTag.set(tag, value);\n          } else {\n            stopObservingProgress(tag, value);\n            value._value = style.initialValues;\n          }\n\n          // @ts-ignore The line below started failing because I added types to the method – don't have time to fix it right now\n          var animation = (0, _styleAnimation.withStyleAnimation)(currentAnimation);\n          animation.callback = finished => {\n            if (finished) {\n              currentAnimationForTag.delete(tag);\n              mutableValuesForTag.delete(tag);\n              var shouldRemoveView = type === _commonTypes.LayoutAnimationType.EXITING;\n              stopObservingProgress(tag, value, shouldRemoveView);\n            }\n            style.callback && style.callback(finished === undefined ? false : finished);\n          };\n          startObservingProgress(tag, value, type);\n          value.value = animation;\n        },\n        stop(tag) {\n          var value = mutableValuesForTag.get(tag);\n          if (!value) {\n            return;\n          }\n          stopObservingProgress(tag, value);\n        }\n      };\n    };\n    createLayoutAnimationManager.__closure = {\n      LayoutAnimationType: _commonTypes.LayoutAnimationType,\n      makeMutableUI: _mutables.makeMutableUI,\n      stopObservingProgress,\n      withStyleAnimation: _styleAnimation.withStyleAnimation,\n      startObservingProgress\n    };\n    createLayoutAnimationManager.__workletHash = 5423312481392;\n    createLayoutAnimationManager.__initData = _worklet_5423312481392_init_data;\n    createLayoutAnimationManager.__stackDetails = _e;\n    return createLayoutAnimationManager;\n  }();\n  var _worklet_6336697456501_init_data = {\n    code: \"function animationsManagerTs4(){const{createLayoutAnimationManager}=this.__closure;global.LayoutAnimationsManager=createLayoutAnimationManager();}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\layoutReanimation\\\\animationsManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"animationsManagerTs4\\\",\\\"createLayoutAnimationManager\\\",\\\"__closure\\\",\\\"global\\\",\\\"LayoutAnimationsManager\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/layoutReanimation/animationsManager.ts\\\"],\\\"mappings\\\":\\\"AA8GmB,SAAAA,oBAAMA,CAAA,QAAAC,4BAAA,OAAAC,SAAA,CAEvBC,MAAM,CAACC,uBAAuB,CAAGH,4BAA4B,CAAC,CAAC,CACjE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  (0, _threads.runOnUIImmediately)(function () {\n    var _e = [new global.Error(), -2, -27];\n    var animationsManagerTs4 = function () {\n      global.LayoutAnimationsManager = createLayoutAnimationManager();\n    };\n    animationsManagerTs4.__closure = {\n      createLayoutAnimationManager\n    };\n    animationsManagerTs4.__workletHash = 6336697456501;\n    animationsManagerTs4.__initData = _worklet_6336697456501_init_data;\n    animationsManagerTs4.__stackDetails = _e;\n    return animationsManagerTs4;\n  }())();\n});", "lineCount": 151, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 0, "_styleAnimation"], [4, 21, 2, 0], [4, 24, 2, 0, "require"], [4, 31, 2, 0], [4, 32, 2, 0, "_dependencyMap"], [4, 46, 2, 0], [5, 2, 9, 0], [5, 6, 9, 0, "_commonTypes"], [5, 18, 9, 0], [5, 21, 9, 0, "require"], [5, 28, 9, 0], [5, 29, 9, 0, "_dependencyMap"], [5, 43, 9, 0], [6, 2, 10, 0], [6, 6, 10, 0, "_mutables"], [6, 15, 10, 0], [6, 18, 10, 0, "require"], [6, 25, 10, 0], [6, 26, 10, 0, "_dependencyMap"], [6, 40, 10, 0], [7, 2, 11, 0], [7, 6, 11, 0, "_threads"], [7, 14, 11, 0], [7, 17, 11, 0, "require"], [7, 24, 11, 0], [7, 25, 11, 0, "_dependencyMap"], [7, 39, 11, 0], [8, 2, 13, 0], [8, 6, 13, 6, "TAG_OFFSET"], [8, 16, 13, 16], [8, 19, 13, 19], [8, 22, 13, 22], [9, 2, 13, 23], [9, 6, 13, 23, "_worklet_10675557011176_init_data"], [9, 39, 13, 23], [10, 4, 13, 23, "code"], [10, 8, 13, 23], [11, 4, 13, 23, "location"], [11, 12, 13, 23], [12, 4, 13, 23, "sourceMap"], [12, 13, 13, 23], [13, 4, 13, 23, "version"], [13, 11, 13, 23], [14, 2, 13, 23], [15, 2, 13, 23], [15, 6, 13, 23, "startObservingProgress"], [15, 28, 13, 23], [15, 31, 15, 0], [16, 4, 15, 0], [16, 8, 15, 0, "_e"], [16, 10, 15, 0], [16, 18, 15, 0, "global"], [16, 24, 15, 0], [16, 25, 15, 0, "Error"], [16, 30, 15, 0], [17, 4, 15, 0], [17, 8, 15, 0, "startObservingProgress"], [17, 30, 15, 0], [17, 42, 15, 0, "startObservingProgress"], [17, 43, 16, 2, "tag"], [17, 46, 16, 13], [17, 48, 17, 2, "sharedValue"], [17, 59, 17, 51], [17, 61, 18, 2, "animationType"], [17, 74, 18, 36], [17, 76, 19, 8], [18, 6, 21, 2], [18, 10, 21, 8, "isSharedTransition"], [18, 28, 21, 26], [18, 31, 22, 4, "animationType"], [18, 44, 22, 17], [18, 49, 22, 22, "LayoutAnimationType"], [18, 81, 22, 41], [18, 82, 22, 42, "SHARED_ELEMENT_TRANSITION"], [18, 107, 22, 67], [19, 6, 23, 2, "sharedValue"], [19, 17, 23, 13], [19, 18, 23, 14, "addListener"], [19, 29, 23, 25], [19, 30, 23, 26, "tag"], [19, 33, 23, 29], [19, 36, 23, 32, "TAG_OFFSET"], [19, 46, 23, 42], [19, 48, 23, 44], [19, 54, 23, 50], [20, 8, 24, 4, "global"], [20, 14, 24, 10], [20, 15, 24, 11, "_notifyAboutProgress"], [20, 35, 24, 31], [20, 36, 24, 32, "tag"], [20, 39, 24, 35], [20, 41, 24, 37, "sharedValue"], [20, 52, 24, 48], [20, 53, 24, 49, "value"], [20, 58, 24, 54], [20, 60, 24, 56, "isSharedTransition"], [20, 78, 24, 74], [20, 79, 24, 75], [21, 6, 25, 2], [21, 7, 25, 3], [21, 8, 25, 4], [22, 4, 26, 0], [22, 5, 26, 1], [23, 4, 26, 1, "startObservingProgress"], [23, 26, 26, 1], [23, 27, 26, 1, "__closure"], [23, 36, 26, 1], [24, 6, 26, 1, "LayoutAnimationType"], [24, 25, 26, 1], [24, 27, 18, 17, "LayoutAnimationType"], [24, 59, 18, 36], [25, 6, 18, 36, "TAG_OFFSET"], [26, 4, 18, 36], [27, 4, 18, 36, "startObservingProgress"], [27, 26, 18, 36], [27, 27, 18, 36, "__workletHash"], [27, 40, 18, 36], [28, 4, 18, 36, "startObservingProgress"], [28, 26, 18, 36], [28, 27, 18, 36, "__initData"], [28, 37, 18, 36], [28, 40, 18, 36, "_worklet_10675557011176_init_data"], [28, 73, 18, 36], [29, 4, 18, 36, "startObservingProgress"], [29, 26, 18, 36], [29, 27, 18, 36, "__stackDetails"], [29, 41, 18, 36], [29, 44, 18, 36, "_e"], [29, 46, 18, 36], [30, 4, 18, 36], [30, 11, 18, 36, "startObservingProgress"], [30, 33, 18, 36], [31, 2, 18, 36], [31, 3, 15, 0], [32, 2, 15, 0], [32, 6, 15, 0, "_worklet_16958055875360_init_data"], [32, 39, 15, 0], [33, 4, 15, 0, "code"], [33, 8, 15, 0], [34, 4, 15, 0, "location"], [34, 12, 15, 0], [35, 4, 15, 0, "sourceMap"], [35, 13, 15, 0], [36, 4, 15, 0, "version"], [36, 11, 15, 0], [37, 2, 15, 0], [38, 2, 15, 0], [38, 6, 15, 0, "stopObservingProgress"], [38, 27, 15, 0], [38, 30, 28, 0], [39, 4, 28, 0], [39, 8, 28, 0, "_e"], [39, 10, 28, 0], [39, 18, 28, 0, "global"], [39, 24, 28, 0], [39, 25, 28, 0, "Error"], [39, 30, 28, 0], [40, 4, 28, 0], [40, 8, 28, 0, "stopObservingProgress"], [40, 29, 28, 0], [40, 41, 28, 0, "stopObservingProgress"], [40, 42, 29, 2, "tag"], [40, 45, 29, 13], [40, 47, 30, 2, "sharedValue"], [40, 58, 30, 34], [40, 60, 32, 8], [41, 6, 32, 8], [41, 10, 31, 2, "<PERSON><PERSON><PERSON><PERSON>"], [41, 20, 31, 12], [41, 23, 31, 12, "arguments"], [41, 32, 31, 12], [41, 33, 31, 12, "length"], [41, 39, 31, 12], [41, 47, 31, 12, "arguments"], [41, 56, 31, 12], [41, 64, 31, 12, "undefined"], [41, 73, 31, 12], [41, 76, 31, 12, "arguments"], [41, 85, 31, 12], [41, 91, 31, 15], [41, 96, 31, 20], [42, 6, 34, 2, "sharedValue"], [42, 17, 34, 13], [42, 18, 34, 14, "removeListener"], [42, 32, 34, 28], [42, 33, 34, 29, "tag"], [42, 36, 34, 32], [42, 39, 34, 35, "TAG_OFFSET"], [42, 49, 34, 45], [42, 50, 34, 46], [43, 6, 35, 2, "global"], [43, 12, 35, 8], [43, 13, 35, 9, "_notifyAboutEnd"], [43, 28, 35, 24], [43, 29, 35, 25, "tag"], [43, 32, 35, 28], [43, 34, 35, 30, "<PERSON><PERSON><PERSON><PERSON>"], [43, 44, 35, 40], [43, 45, 35, 41], [44, 4, 36, 0], [44, 5, 36, 1], [45, 4, 36, 1, "stopObservingProgress"], [45, 25, 36, 1], [45, 26, 36, 1, "__closure"], [45, 35, 36, 1], [46, 6, 36, 1, "TAG_OFFSET"], [47, 4, 36, 1], [48, 4, 36, 1, "stopObservingProgress"], [48, 25, 36, 1], [48, 26, 36, 1, "__workletHash"], [48, 39, 36, 1], [49, 4, 36, 1, "stopObservingProgress"], [49, 25, 36, 1], [49, 26, 36, 1, "__initData"], [49, 36, 36, 1], [49, 39, 36, 1, "_worklet_16958055875360_init_data"], [49, 72, 36, 1], [50, 4, 36, 1, "stopObservingProgress"], [50, 25, 36, 1], [50, 26, 36, 1, "__stackDetails"], [50, 40, 36, 1], [50, 43, 36, 1, "_e"], [50, 45, 36, 1], [51, 4, 36, 1], [51, 11, 36, 1, "stopObservingProgress"], [51, 32, 36, 1], [52, 2, 36, 1], [52, 3, 28, 0], [53, 2, 28, 0], [53, 6, 28, 0, "_worklet_5423312481392_init_data"], [53, 38, 28, 0], [54, 4, 28, 0, "code"], [54, 8, 28, 0], [55, 4, 28, 0, "location"], [55, 12, 28, 0], [56, 4, 28, 0, "sourceMap"], [56, 13, 28, 0], [57, 4, 28, 0, "version"], [57, 11, 28, 0], [58, 2, 28, 0], [59, 2, 28, 0], [59, 6, 28, 0, "createLayoutAnimationManager"], [59, 34, 28, 0], [59, 37, 38, 0], [60, 4, 38, 0], [60, 8, 38, 0, "_e"], [60, 10, 38, 0], [60, 18, 38, 0, "global"], [60, 24, 38, 0], [60, 25, 38, 0, "Error"], [60, 30, 38, 0], [61, 4, 38, 0], [61, 8, 38, 0, "createLayoutAnimationManager"], [61, 36, 38, 0], [61, 48, 38, 0, "createLayoutAnimationManager"], [61, 49, 38, 0], [61, 51, 41, 2], [62, 6, 43, 2], [62, 10, 43, 8, "currentAnimationForTag"], [62, 32, 43, 30], [62, 35, 43, 33], [62, 39, 43, 37, "Map"], [62, 42, 43, 40], [62, 43, 43, 41], [62, 44, 43, 42], [63, 6, 44, 2], [63, 10, 44, 8, "mutableValuesForTag"], [63, 29, 44, 27], [63, 32, 44, 30], [63, 36, 44, 34, "Map"], [63, 39, 44, 37], [63, 40, 44, 38], [63, 41, 44, 39], [64, 6, 46, 2], [64, 13, 46, 9], [65, 8, 47, 4, "start"], [65, 13, 47, 9, "start"], [65, 14, 48, 6, "tag"], [65, 17, 48, 17], [65, 19, 49, 6, "type"], [65, 23, 49, 31], [66, 8, 50, 6], [67, 0, 51, 0], [68, 0, 52, 0], [69, 0, 53, 0], [70, 8, 54, 6, "yoga<PERSON><PERSON><PERSON>"], [70, 18, 54, 59], [70, 20, 55, 6, "config"], [70, 26, 57, 26], [70, 28, 58, 6], [71, 10, 59, 6], [71, 14, 59, 10, "type"], [71, 18, 59, 14], [71, 23, 59, 19, "LayoutAnimationType"], [71, 55, 59, 38], [71, 56, 59, 39, "SHARED_ELEMENT_TRANSITION_PROGRESS"], [71, 90, 59, 73], [71, 92, 59, 75], [72, 12, 60, 8, "global"], [72, 18, 60, 14], [72, 19, 60, 15, "ProgressTransitionRegister"], [72, 45, 60, 41], [72, 46, 60, 42, "onTransitionStart"], [72, 63, 60, 59], [72, 64, 60, 60, "tag"], [72, 67, 60, 63], [72, 69, 60, 65, "yoga<PERSON><PERSON><PERSON>"], [72, 79, 60, 75], [72, 80, 60, 76], [73, 12, 61, 8], [74, 10, 62, 6], [75, 10, 64, 6], [75, 14, 64, 12, "style"], [75, 19, 64, 17], [75, 22, 64, 20, "config"], [75, 28, 64, 26], [75, 29, 64, 27, "yoga<PERSON><PERSON><PERSON>"], [75, 39, 64, 37], [75, 40, 64, 38], [76, 10, 65, 6], [76, 14, 65, 10, "currentAnimation"], [76, 30, 65, 26], [76, 33, 65, 29, "style"], [76, 38, 65, 34], [76, 39, 65, 35, "animations"], [76, 49, 65, 45], [78, 10, 67, 6], [79, 10, 68, 6], [80, 10, 69, 6], [80, 14, 69, 12, "previousAnimation"], [80, 31, 69, 29], [80, 34, 69, 32, "currentAnimationForTag"], [80, 56, 69, 54], [80, 57, 69, 55, "get"], [80, 60, 69, 58], [80, 61, 69, 59, "tag"], [80, 64, 69, 62], [80, 65, 69, 63], [81, 10, 70, 6], [81, 14, 70, 10, "previousAnimation"], [81, 31, 70, 27], [81, 33, 70, 29], [82, 12, 71, 8, "currentAnimation"], [82, 28, 71, 24], [82, 31, 71, 27], [83, 14, 71, 29], [83, 17, 71, 32, "previousAnimation"], [83, 34, 71, 49], [84, 14, 71, 51], [84, 17, 71, 54, "style"], [84, 22, 71, 59], [84, 23, 71, 60, "animations"], [85, 12, 71, 71], [85, 13, 71, 72], [86, 10, 72, 6], [87, 10, 73, 6, "currentAnimationForTag"], [87, 32, 73, 28], [87, 33, 73, 29, "set"], [87, 36, 73, 32], [87, 37, 73, 33, "tag"], [87, 40, 73, 36], [87, 42, 73, 38, "currentAnimation"], [87, 58, 73, 54], [87, 59, 73, 55], [88, 10, 75, 6], [88, 14, 75, 10, "value"], [88, 19, 75, 15], [88, 22, 75, 18, "mutableValuesForTag"], [88, 41, 75, 37], [88, 42, 75, 38, "get"], [88, 45, 75, 41], [88, 46, 75, 42, "tag"], [88, 49, 75, 45], [88, 50, 75, 46], [89, 10, 76, 6], [89, 14, 76, 10, "value"], [89, 19, 76, 15], [89, 24, 76, 20, "undefined"], [89, 33, 76, 29], [89, 35, 76, 31], [90, 12, 77, 8, "value"], [90, 17, 77, 13], [90, 20, 77, 16], [90, 24, 77, 16, "makeMutableUI"], [90, 47, 77, 29], [90, 49, 77, 30, "style"], [90, 54, 77, 35], [90, 55, 77, 36, "initialValues"], [90, 68, 77, 49], [90, 69, 77, 50], [91, 12, 78, 8, "mutableValuesForTag"], [91, 31, 78, 27], [91, 32, 78, 28, "set"], [91, 35, 78, 31], [91, 36, 78, 32, "tag"], [91, 39, 78, 35], [91, 41, 78, 37, "value"], [91, 46, 78, 42], [91, 47, 78, 43], [92, 10, 79, 6], [92, 11, 79, 7], [92, 17, 79, 13], [93, 12, 80, 8, "stopObservingProgress"], [93, 33, 80, 29], [93, 34, 80, 30, "tag"], [93, 37, 80, 33], [93, 39, 80, 35, "value"], [93, 44, 80, 40], [93, 45, 80, 41], [94, 12, 81, 8, "value"], [94, 17, 81, 13], [94, 18, 81, 14, "_value"], [94, 24, 81, 20], [94, 27, 81, 23, "style"], [94, 32, 81, 28], [94, 33, 81, 29, "initialValues"], [94, 46, 81, 42], [95, 10, 82, 6], [97, 10, 84, 6], [98, 10, 85, 6], [98, 14, 85, 12, "animation"], [98, 23, 85, 21], [98, 26, 85, 24], [98, 30, 85, 24, "withStyleAnimation"], [98, 64, 85, 42], [98, 66, 85, 43, "currentAnimation"], [98, 82, 85, 59], [98, 83, 85, 60], [99, 10, 87, 6, "animation"], [99, 19, 87, 15], [99, 20, 87, 16, "callback"], [99, 28, 87, 24], [99, 31, 87, 28, "finished"], [99, 39, 87, 46], [99, 43, 87, 51], [100, 12, 88, 8], [100, 16, 88, 12, "finished"], [100, 24, 88, 20], [100, 26, 88, 22], [101, 14, 89, 10, "currentAnimationForTag"], [101, 36, 89, 32], [101, 37, 89, 33, "delete"], [101, 43, 89, 39], [101, 44, 89, 40, "tag"], [101, 47, 89, 43], [101, 48, 89, 44], [102, 14, 90, 10, "mutableValuesForTag"], [102, 33, 90, 29], [102, 34, 90, 30, "delete"], [102, 40, 90, 36], [102, 41, 90, 37, "tag"], [102, 44, 90, 40], [102, 45, 90, 41], [103, 14, 91, 10], [103, 18, 91, 16, "shouldRemoveView"], [103, 34, 91, 32], [103, 37, 91, 35, "type"], [103, 41, 91, 39], [103, 46, 91, 44, "LayoutAnimationType"], [103, 78, 91, 63], [103, 79, 91, 64, "EXITING"], [103, 86, 91, 71], [104, 14, 92, 10, "stopObservingProgress"], [104, 35, 92, 31], [104, 36, 92, 32, "tag"], [104, 39, 92, 35], [104, 41, 92, 37, "value"], [104, 46, 92, 42], [104, 48, 92, 44, "shouldRemoveView"], [104, 64, 92, 60], [104, 65, 92, 61], [105, 12, 93, 8], [106, 12, 94, 8, "style"], [106, 17, 94, 13], [106, 18, 94, 14, "callback"], [106, 26, 94, 22], [106, 30, 95, 10, "style"], [106, 35, 95, 15], [106, 36, 95, 16, "callback"], [106, 44, 95, 24], [106, 45, 95, 25, "finished"], [106, 53, 95, 33], [106, 58, 95, 38, "undefined"], [106, 67, 95, 47], [106, 70, 95, 50], [106, 75, 95, 55], [106, 78, 95, 58, "finished"], [106, 86, 95, 66], [106, 87, 95, 67], [107, 10, 96, 6], [107, 11, 96, 7], [108, 10, 98, 6, "startObservingProgress"], [108, 32, 98, 28], [108, 33, 98, 29, "tag"], [108, 36, 98, 32], [108, 38, 98, 34, "value"], [108, 43, 98, 39], [108, 45, 98, 41, "type"], [108, 49, 98, 45], [108, 50, 98, 46], [109, 10, 99, 6, "value"], [109, 15, 99, 11], [109, 16, 99, 12, "value"], [109, 21, 99, 17], [109, 24, 99, 20, "animation"], [109, 33, 99, 29], [110, 8, 100, 4], [110, 9, 100, 5], [111, 8, 101, 4, "stop"], [111, 12, 101, 8, "stop"], [111, 13, 101, 9, "tag"], [111, 16, 101, 20], [111, 18, 101, 22], [112, 10, 102, 6], [112, 14, 102, 12, "value"], [112, 19, 102, 17], [112, 22, 102, 20, "mutableValuesForTag"], [112, 41, 102, 39], [112, 42, 102, 40, "get"], [112, 45, 102, 43], [112, 46, 102, 44, "tag"], [112, 49, 102, 47], [112, 50, 102, 48], [113, 10, 103, 6], [113, 14, 103, 10], [113, 15, 103, 11, "value"], [113, 20, 103, 16], [113, 22, 103, 18], [114, 12, 104, 8], [115, 10, 105, 6], [116, 10, 106, 6, "stopObservingProgress"], [116, 31, 106, 27], [116, 32, 106, 28, "tag"], [116, 35, 106, 31], [116, 37, 106, 33, "value"], [116, 42, 106, 38], [116, 43, 106, 39], [117, 8, 107, 4], [118, 6, 108, 2], [118, 7, 108, 3], [119, 4, 109, 0], [119, 5, 109, 1], [120, 4, 109, 1, "createLayoutAnimationManager"], [120, 32, 109, 1], [120, 33, 109, 1, "__closure"], [120, 42, 109, 1], [121, 6, 109, 1, "LayoutAnimationType"], [121, 25, 109, 1], [121, 27, 49, 12, "LayoutAnimationType"], [121, 59, 49, 31], [122, 6, 49, 31, "makeMutableUI"], [122, 19, 49, 31], [122, 21, 77, 16, "makeMutableUI"], [122, 44, 77, 29], [123, 6, 77, 29, "stopObservingProgress"], [123, 27, 77, 29], [124, 6, 77, 29, "withStyleAnimation"], [124, 24, 77, 29], [124, 26, 85, 24, "withStyleAnimation"], [124, 60, 85, 42], [125, 6, 85, 42, "startObservingProgress"], [126, 4, 85, 42], [127, 4, 85, 42, "createLayoutAnimationManager"], [127, 32, 85, 42], [127, 33, 85, 42, "__workletHash"], [127, 46, 85, 42], [128, 4, 85, 42, "createLayoutAnimationManager"], [128, 32, 85, 42], [128, 33, 85, 42, "__initData"], [128, 43, 85, 42], [128, 46, 85, 42, "_worklet_5423312481392_init_data"], [128, 78, 85, 42], [129, 4, 85, 42, "createLayoutAnimationManager"], [129, 32, 85, 42], [129, 33, 85, 42, "__stackDetails"], [129, 47, 85, 42], [129, 50, 85, 42, "_e"], [129, 52, 85, 42], [130, 4, 85, 42], [130, 11, 85, 42, "createLayoutAnimationManager"], [130, 39, 85, 42], [131, 2, 85, 42], [131, 3, 38, 0], [132, 2, 38, 0], [132, 6, 38, 0, "_worklet_6336697456501_init_data"], [132, 38, 38, 0], [133, 4, 38, 0, "code"], [133, 8, 38, 0], [134, 4, 38, 0, "location"], [134, 12, 38, 0], [135, 4, 38, 0, "sourceMap"], [135, 13, 38, 0], [136, 4, 38, 0, "version"], [136, 11, 38, 0], [137, 2, 38, 0], [138, 2, 111, 0], [138, 6, 111, 0, "runOnUIImmediately"], [138, 33, 111, 18], [138, 35, 111, 19], [139, 4, 111, 19], [139, 8, 111, 19, "_e"], [139, 10, 111, 19], [139, 18, 111, 19, "global"], [139, 24, 111, 19], [139, 25, 111, 19, "Error"], [139, 30, 111, 19], [140, 4, 111, 19], [140, 8, 111, 19, "animationsManagerTs4"], [140, 28, 111, 19], [140, 40, 111, 19, "animationsManagerTs4"], [140, 41, 111, 19], [140, 43, 111, 25], [141, 6, 113, 2, "global"], [141, 12, 113, 8], [141, 13, 113, 9, "LayoutAnimationsManager"], [141, 36, 113, 32], [141, 39, 113, 35, "createLayoutAnimationManager"], [141, 67, 113, 63], [141, 68, 113, 64], [141, 69, 113, 65], [142, 4, 114, 0], [142, 5, 114, 1], [143, 4, 114, 1, "animationsManagerTs4"], [143, 24, 114, 1], [143, 25, 114, 1, "__closure"], [143, 34, 114, 1], [144, 6, 114, 1, "createLayoutAnimationManager"], [145, 4, 114, 1], [146, 4, 114, 1, "animationsManagerTs4"], [146, 24, 114, 1], [146, 25, 114, 1, "__workletHash"], [146, 38, 114, 1], [147, 4, 114, 1, "animationsManagerTs4"], [147, 24, 114, 1], [147, 25, 114, 1, "__initData"], [147, 35, 114, 1], [147, 38, 114, 1, "_worklet_6336697456501_init_data"], [147, 70, 114, 1], [148, 4, 114, 1, "animationsManagerTs4"], [148, 24, 114, 1], [148, 25, 114, 1, "__stackDetails"], [148, 39, 114, 1], [148, 42, 114, 1, "_e"], [148, 44, 114, 1], [149, 4, 114, 1], [149, 11, 114, 1, "animationsManagerTs4"], [149, 31, 114, 1], [150, 2, 114, 1], [150, 3, 111, 19], [150, 5, 114, 1], [150, 6, 114, 2], [150, 7, 114, 3], [150, 8, 114, 4], [151, 0, 114, 5], [151, 3]], "functionMap": {"names": ["<global>", "startObservingProgress", "sharedValue.addListener$argument_1", "stopObservingProgress", "createLayoutAnimationManager", "start", "animation.callback", "stop", "runOnUIImmediately$argument_0"], "mappings": "AAA;ACc;4CCQ;GDE;CDC;AGE;CHQ;AIE;ICS;2BCwC;ODS;KDI;IGC;KHM;CJE;mBQE;CRG"}}, "type": "js/module"}]}