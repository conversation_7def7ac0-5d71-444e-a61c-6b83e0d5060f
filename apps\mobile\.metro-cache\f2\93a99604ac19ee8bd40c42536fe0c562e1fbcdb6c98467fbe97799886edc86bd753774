{"dependencies": [{"name": "../Colors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 55}, "end": {"line": 4, "column": 49, "index": 104}}], "key": "Y2vNB3FL9La5/kx04BGVY2eun0w=", "exportNames": ["*"]}}, {"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 271}, "end": {"line": 11, "column": 35, "index": 306}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 307}, "end": {"line": 17, "column": 28, "index": 404}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.setNativeProps = void 0;\n  var _Colors = require(_dependencyMap[0], \"../Colors\");\n  var _logger = require(_dependencyMap[1], \"../logger\");\n  var _PlatformChecker = require(_dependencyMap[2], \"../PlatformChecker\");\n  /**\n   * Lets you imperatively update component properties. You should always reach\n   * for\n   * [useAnimatedStyle](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedStyle)\n   * and\n   * [useAnimatedProps](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedProps)\n   * first when animating styles or properties.\n   *\n   * @param animatedRef - An [animated\n   *   ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef#returns)\n   *   connected to the component you'd want to update.\n   * @param updates - An object with properties you want to update.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/setNativeProps\n   */\n  var setNativeProps;\n  var _worklet_994888571758_init_data = {\n    code: \"function setNativePropsFabric_setNativePropsTs1(animatedRef,updates){const{logger,processColorsInProps}=this.__closure;if(!_WORKLET){logger.warn('setNativeProps() can only be used on the UI runtime.');return;}const shadowNodeWrapper=animatedRef();processColorsInProps(updates);global._updatePropsFabric([{shadowNodeWrapper:shadowNodeWrapper,updates:updates}]);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\platformFunctions\\\\setNativeProps.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setNativePropsFabric_setNativePropsTs1\\\",\\\"animatedRef\\\",\\\"updates\\\",\\\"logger\\\",\\\"processColorsInProps\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"warn\\\",\\\"shadowNodeWrapper\\\",\\\"global\\\",\\\"_updatePropsFabric\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/platformFunctions/setNativeProps.ts\\\"],\\\"mappings\\\":\\\"AAsCA,SAAAA,sCAEEA,CAAAC,WACA,CAAAC,OAAA,QAAAC,MAAA,CAAAC,oBAAA,OAAAC,SAAA,CAEA,GAAI,CAACC,QAAQ,CAAE,CACbH,MAAM,CAACI,IAAI,CAAC,sDAAsD,CAAC,CACnE,OACF,CACA,KAAM,CAAAC,iBAAiB,CAAGP,WAAW,CAAC,CAAsB,CAC5DG,oBAAoB,CAACF,OAAO,CAAC,CAC7BO,MAAM,CAACC,kBAAkB,CAAE,CAAC,CAAEF,iBAAiB,CAAjBA,iBAAiB,CAAEN,OAAA,CAAAA,OAAQ,CAAC,CAAC,CAAC,CAC9D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var setNativePropsFabric = function () {\n    var _e = [new global.Error(), -3, -27];\n    var setNativePropsFabric = function (animatedRef, updates) {\n      if (!_WORKLET) {\n        _logger.logger.warn('setNativeProps() can only be used on the UI runtime.');\n        return;\n      }\n      var shadowNodeWrapper = animatedRef();\n      (0, _Colors.processColorsInProps)(updates);\n      global._updatePropsFabric([{\n        shadowNodeWrapper,\n        updates\n      }]);\n    };\n    setNativePropsFabric.__closure = {\n      logger: _logger.logger,\n      processColorsInProps: _Colors.processColorsInProps\n    };\n    setNativePropsFabric.__workletHash = 994888571758;\n    setNativePropsFabric.__initData = _worklet_994888571758_init_data;\n    setNativePropsFabric.__stackDetails = _e;\n    return setNativePropsFabric;\n  }();\n  var _worklet_14226845395319_init_data = {\n    code: \"function setNativePropsPaper_setNativePropsTs2(animatedRef,updates){const{logger,processColorsInProps}=this.__closure;if(!_WORKLET){logger.warn('setNativeProps() can only be used on the UI runtime.');return;}const tag=animatedRef();const name=animatedRef.viewName.value;processColorsInProps(updates);global._updatePropsPaper([{tag:tag,name:name,updates:updates}]);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\platformFunctions\\\\setNativeProps.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setNativePropsPaper_setNativePropsTs2\\\",\\\"animatedRef\\\",\\\"updates\\\",\\\"logger\\\",\\\"processColorsInProps\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"warn\\\",\\\"tag\\\",\\\"name\\\",\\\"viewName\\\",\\\"value\\\",\\\"global\\\",\\\"_updatePropsPaper\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/platformFunctions/setNativeProps.ts\\\"],\\\"mappings\\\":\\\"AAoDA,SAAAA,qCAEEA,CAAAC,WACA,CAAAC,OAAA,QAAAC,MAAA,CAAAC,oBAAA,OAAAC,SAAA,CAEA,GAAI,CAACC,QAAQ,CAAE,CACbH,MAAM,CAACI,IAAI,CAAC,sDAAsD,CAAC,CACnE,OACF,CACA,KAAM,CAAAC,GAAG,CAAGP,WAAW,CAAC,CAAW,CACnC,KAAM,CAAAQ,IAAI,CAAIR,WAAW,CAAqBS,QAAQ,CAACC,KAAK,CAC5DP,oBAAoB,CAACF,OAAO,CAAC,CAC7BU,MAAM,CAACC,iBAAiB,CAAE,CAAC,CAAEL,GAAG,CAAHA,GAAG,CAAEC,IAAI,CAAJA,IAAI,CAAEP,OAAA,CAAAA,OAAQ,CAAC,CAAC,CAAC,CACrD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var setNativePropsPaper = function () {\n    var _e = [new global.Error(), -3, -27];\n    var setNativePropsPaper = function (animatedRef, updates) {\n      if (!_WORKLET) {\n        _logger.logger.warn('setNativeProps() can only be used on the UI runtime.');\n        return;\n      }\n      var tag = animatedRef();\n      var name = animatedRef.viewName.value;\n      (0, _Colors.processColorsInProps)(updates);\n      global._updatePropsPaper([{\n        tag,\n        name,\n        updates\n      }]);\n    };\n    setNativePropsPaper.__closure = {\n      logger: _logger.logger,\n      processColorsInProps: _Colors.processColorsInProps\n    };\n    setNativePropsPaper.__workletHash = 14226845395319;\n    setNativePropsPaper.__initData = _worklet_14226845395319_init_data;\n    setNativePropsPaper.__stackDetails = _e;\n    return setNativePropsPaper;\n  }();\n  function setNativePropsJest() {\n    _logger.logger.warn('setNativeProps() is not supported with Jest.');\n  }\n  function setNativePropsChromeDebugger() {\n    _logger.logger.warn('setNativeProps() is not supported with Chrome Debugger.');\n  }\n  function setNativePropsDefault() {\n    _logger.logger.warn('setNativeProps() is not supported on this configuration.');\n  }\n  if (!(0, _PlatformChecker.shouldBeUseWeb)()) {\n    // Those assertions are actually correct since on Native platforms `AnimatedRef` is\n    // mapped as a different function in `shareableMappingCache` and\n    // TypeScript is not able to infer that.\n    if ((0, _PlatformChecker.isFabric)()) {\n      exports.setNativeProps = setNativeProps = setNativePropsFabric;\n    } else {\n      exports.setNativeProps = setNativeProps = setNativePropsPaper;\n    }\n  } else if ((0, _PlatformChecker.isJest)()) {\n    exports.setNativeProps = setNativeProps = setNativePropsJest;\n  } else if ((0, _PlatformChecker.isChromeDebugger)()) {\n    exports.setNativeProps = setNativeProps = setNativePropsChromeDebugger;\n  } else {\n    exports.setNativeProps = setNativeProps = setNativePropsDefault;\n  }\n});", "lineCount": 111, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "setNativeProps"], [7, 24, 1, 13], [8, 2, 4, 0], [8, 6, 4, 0, "_Colors"], [8, 13, 4, 0], [8, 16, 4, 0, "require"], [8, 23, 4, 0], [8, 24, 4, 0, "_dependencyMap"], [8, 38, 4, 0], [9, 2, 11, 0], [9, 6, 11, 0, "_logger"], [9, 13, 11, 0], [9, 16, 11, 0, "require"], [9, 23, 11, 0], [9, 24, 11, 0, "_dependencyMap"], [9, 38, 11, 0], [10, 2, 12, 0], [10, 6, 12, 0, "_PlatformChecker"], [10, 22, 12, 0], [10, 25, 12, 0, "require"], [10, 32, 12, 0], [10, 33, 12, 0, "_dependencyMap"], [10, 47, 12, 0], [11, 2, 23, 0], [12, 0, 24, 0], [13, 0, 25, 0], [14, 0, 26, 0], [15, 0, 27, 0], [16, 0, 28, 0], [17, 0, 29, 0], [18, 0, 30, 0], [19, 0, 31, 0], [20, 0, 32, 0], [21, 0, 33, 0], [22, 0, 34, 0], [23, 0, 35, 0], [24, 0, 36, 0], [25, 2, 37, 7], [25, 6, 37, 11, "setNativeProps"], [25, 20, 37, 41], [26, 2, 37, 42], [26, 6, 37, 42, "_worklet_994888571758_init_data"], [26, 37, 37, 42], [27, 4, 37, 42, "code"], [27, 8, 37, 42], [28, 4, 37, 42, "location"], [28, 12, 37, 42], [29, 4, 37, 42, "sourceMap"], [29, 13, 37, 42], [30, 4, 37, 42, "version"], [30, 11, 37, 42], [31, 2, 37, 42], [32, 2, 37, 42], [32, 6, 37, 42, "setNativePropsFabric"], [32, 26, 37, 42], [32, 29, 39, 0], [33, 4, 39, 0], [33, 8, 39, 0, "_e"], [33, 10, 39, 0], [33, 18, 39, 0, "global"], [33, 24, 39, 0], [33, 25, 39, 0, "Error"], [33, 30, 39, 0], [34, 4, 39, 0], [34, 8, 39, 0, "setNativePropsFabric"], [34, 28, 39, 0], [34, 40, 39, 0, "setNativePropsFabric"], [34, 41, 40, 2, "animatedRef"], [34, 52, 40, 48], [34, 54, 41, 2, "updates"], [34, 61, 41, 21], [34, 63, 42, 2], [35, 6, 44, 2], [35, 10, 44, 6], [35, 11, 44, 7, "_WORKLET"], [35, 19, 44, 15], [35, 21, 44, 17], [36, 8, 45, 4, "logger"], [36, 22, 45, 10], [36, 23, 45, 11, "warn"], [36, 27, 45, 15], [36, 28, 45, 16], [36, 82, 45, 70], [36, 83, 45, 71], [37, 8, 46, 4], [38, 6, 47, 2], [39, 6, 48, 2], [39, 10, 48, 8, "shadowNodeWrapper"], [39, 27, 48, 25], [39, 30, 48, 28, "animatedRef"], [39, 41, 48, 39], [39, 42, 48, 40], [39, 43, 48, 62], [40, 6, 49, 2], [40, 10, 49, 2, "processColorsInProps"], [40, 38, 49, 22], [40, 40, 49, 23, "updates"], [40, 47, 49, 30], [40, 48, 49, 31], [41, 6, 50, 2, "global"], [41, 12, 50, 8], [41, 13, 50, 9, "_updatePropsFabric"], [41, 31, 50, 27], [41, 32, 50, 29], [41, 33, 50, 30], [42, 8, 50, 32, "shadowNodeWrapper"], [42, 25, 50, 49], [43, 8, 50, 51, "updates"], [44, 6, 50, 59], [44, 7, 50, 60], [44, 8, 50, 61], [44, 9, 50, 62], [45, 4, 51, 0], [45, 5, 51, 1], [46, 4, 51, 1, "setNativePropsFabric"], [46, 24, 51, 1], [46, 25, 51, 1, "__closure"], [46, 34, 51, 1], [47, 6, 51, 1, "logger"], [47, 12, 51, 1], [47, 14, 45, 4, "logger"], [47, 28, 45, 10], [48, 6, 45, 10, "processColorsInProps"], [48, 26, 45, 10], [48, 28, 49, 2, "processColorsInProps"], [49, 4, 49, 22], [50, 4, 49, 22, "setNativePropsFabric"], [50, 24, 49, 22], [50, 25, 49, 22, "__workletHash"], [50, 38, 49, 22], [51, 4, 49, 22, "setNativePropsFabric"], [51, 24, 49, 22], [51, 25, 49, 22, "__initData"], [51, 35, 49, 22], [51, 38, 49, 22, "_worklet_994888571758_init_data"], [51, 69, 49, 22], [52, 4, 49, 22, "setNativePropsFabric"], [52, 24, 49, 22], [52, 25, 49, 22, "__stackDetails"], [52, 39, 49, 22], [52, 42, 49, 22, "_e"], [52, 44, 49, 22], [53, 4, 49, 22], [53, 11, 49, 22, "setNativePropsFabric"], [53, 31, 49, 22], [54, 2, 49, 22], [54, 3, 39, 0], [55, 2, 39, 0], [55, 6, 39, 0, "_worklet_14226845395319_init_data"], [55, 39, 39, 0], [56, 4, 39, 0, "code"], [56, 8, 39, 0], [57, 4, 39, 0, "location"], [57, 12, 39, 0], [58, 4, 39, 0, "sourceMap"], [58, 13, 39, 0], [59, 4, 39, 0, "version"], [59, 11, 39, 0], [60, 2, 39, 0], [61, 2, 39, 0], [61, 6, 39, 0, "setNativePropsPaper"], [61, 25, 39, 0], [61, 28, 53, 0], [62, 4, 53, 0], [62, 8, 53, 0, "_e"], [62, 10, 53, 0], [62, 18, 53, 0, "global"], [62, 24, 53, 0], [62, 25, 53, 0, "Error"], [62, 30, 53, 0], [63, 4, 53, 0], [63, 8, 53, 0, "setNativePropsPaper"], [63, 27, 53, 0], [63, 39, 53, 0, "setNativePropsPaper"], [63, 40, 54, 2, "animatedRef"], [63, 51, 54, 48], [63, 53, 55, 2, "updates"], [63, 60, 55, 21], [63, 62, 56, 2], [64, 6, 58, 2], [64, 10, 58, 6], [64, 11, 58, 7, "_WORKLET"], [64, 19, 58, 15], [64, 21, 58, 17], [65, 8, 59, 4, "logger"], [65, 22, 59, 10], [65, 23, 59, 11, "warn"], [65, 27, 59, 15], [65, 28, 59, 16], [65, 82, 59, 70], [65, 83, 59, 71], [66, 8, 60, 4], [67, 6, 61, 2], [68, 6, 62, 2], [68, 10, 62, 8, "tag"], [68, 13, 62, 11], [68, 16, 62, 14, "animatedRef"], [68, 27, 62, 25], [68, 28, 62, 26], [68, 29, 62, 37], [69, 6, 63, 2], [69, 10, 63, 8, "name"], [69, 14, 63, 12], [69, 17, 63, 16, "animatedRef"], [69, 28, 63, 27], [69, 29, 63, 48, "viewName"], [69, 37, 63, 56], [69, 38, 63, 57, "value"], [69, 43, 63, 62], [70, 6, 64, 2], [70, 10, 64, 2, "processColorsInProps"], [70, 38, 64, 22], [70, 40, 64, 23, "updates"], [70, 47, 64, 30], [70, 48, 64, 31], [71, 6, 65, 2, "global"], [71, 12, 65, 8], [71, 13, 65, 9, "_updatePropsPaper"], [71, 30, 65, 26], [71, 31, 65, 28], [71, 32, 65, 29], [72, 8, 65, 31, "tag"], [72, 11, 65, 34], [73, 8, 65, 36, "name"], [73, 12, 65, 40], [74, 8, 65, 42, "updates"], [75, 6, 65, 50], [75, 7, 65, 51], [75, 8, 65, 52], [75, 9, 65, 53], [76, 4, 66, 0], [76, 5, 66, 1], [77, 4, 66, 1, "setNativePropsPaper"], [77, 23, 66, 1], [77, 24, 66, 1, "__closure"], [77, 33, 66, 1], [78, 6, 66, 1, "logger"], [78, 12, 66, 1], [78, 14, 59, 4, "logger"], [78, 28, 59, 10], [79, 6, 59, 10, "processColorsInProps"], [79, 26, 59, 10], [79, 28, 64, 2, "processColorsInProps"], [80, 4, 64, 22], [81, 4, 64, 22, "setNativePropsPaper"], [81, 23, 64, 22], [81, 24, 64, 22, "__workletHash"], [81, 37, 64, 22], [82, 4, 64, 22, "setNativePropsPaper"], [82, 23, 64, 22], [82, 24, 64, 22, "__initData"], [82, 34, 64, 22], [82, 37, 64, 22, "_worklet_14226845395319_init_data"], [82, 70, 64, 22], [83, 4, 64, 22, "setNativePropsPaper"], [83, 23, 64, 22], [83, 24, 64, 22, "__stackDetails"], [83, 38, 64, 22], [83, 41, 64, 22, "_e"], [83, 43, 64, 22], [84, 4, 64, 22], [84, 11, 64, 22, "setNativePropsPaper"], [84, 30, 64, 22], [85, 2, 64, 22], [85, 3, 53, 0], [86, 2, 68, 0], [86, 11, 68, 9, "setNativePropsJest"], [86, 29, 68, 27, "setNativePropsJest"], [86, 30, 68, 27], [86, 32, 68, 30], [87, 4, 69, 2, "logger"], [87, 18, 69, 8], [87, 19, 69, 9, "warn"], [87, 23, 69, 13], [87, 24, 69, 14], [87, 70, 69, 60], [87, 71, 69, 61], [88, 2, 70, 0], [89, 2, 72, 0], [89, 11, 72, 9, "setNativePropsChromeDebugger"], [89, 39, 72, 37, "setNativePropsChromeDebugger"], [89, 40, 72, 37], [89, 42, 72, 40], [90, 4, 73, 2, "logger"], [90, 18, 73, 8], [90, 19, 73, 9, "warn"], [90, 23, 73, 13], [90, 24, 73, 14], [90, 81, 73, 71], [90, 82, 73, 72], [91, 2, 74, 0], [92, 2, 76, 0], [92, 11, 76, 9, "setNativePropsDefault"], [92, 32, 76, 30, "setNativePropsDefault"], [92, 33, 76, 30], [92, 35, 76, 33], [93, 4, 77, 2, "logger"], [93, 18, 77, 8], [93, 19, 77, 9, "warn"], [93, 23, 77, 13], [93, 24, 77, 14], [93, 82, 77, 72], [93, 83, 77, 73], [94, 2, 78, 0], [95, 2, 80, 0], [95, 6, 80, 4], [95, 7, 80, 5], [95, 11, 80, 5, "shouldBeUseWeb"], [95, 42, 80, 19], [95, 44, 80, 20], [95, 45, 80, 21], [95, 47, 80, 23], [96, 4, 81, 2], [97, 4, 82, 2], [98, 4, 83, 2], [99, 4, 84, 2], [99, 8, 84, 6], [99, 12, 84, 6, "isF<PERSON><PERSON>"], [99, 37, 84, 14], [99, 39, 84, 15], [99, 40, 84, 16], [99, 42, 84, 18], [100, 6, 85, 4, "exports"], [100, 13, 85, 4], [100, 14, 85, 4, "setNativeProps"], [100, 28, 85, 4], [100, 31, 85, 4, "setNativeProps"], [100, 45, 85, 18], [100, 48, 85, 21, "setNativePropsFabric"], [100, 68, 85, 70], [101, 4, 86, 2], [101, 5, 86, 3], [101, 11, 86, 9], [102, 6, 87, 4, "exports"], [102, 13, 87, 4], [102, 14, 87, 4, "setNativeProps"], [102, 28, 87, 4], [102, 31, 87, 4, "setNativeProps"], [102, 45, 87, 18], [102, 48, 87, 21, "setNativePropsPaper"], [102, 67, 87, 69], [103, 4, 88, 2], [104, 2, 89, 0], [104, 3, 89, 1], [104, 9, 89, 7], [104, 13, 89, 11], [104, 17, 89, 11, "isJest"], [104, 40, 89, 17], [104, 42, 89, 18], [104, 43, 89, 19], [104, 45, 89, 21], [105, 4, 90, 2, "exports"], [105, 11, 90, 2], [105, 12, 90, 2, "setNativeProps"], [105, 26, 90, 2], [105, 29, 90, 2, "setNativeProps"], [105, 43, 90, 16], [105, 46, 90, 19, "setNativePropsJest"], [105, 64, 90, 37], [106, 2, 91, 0], [106, 3, 91, 1], [106, 9, 91, 7], [106, 13, 91, 11], [106, 17, 91, 11, "isChromeDebugger"], [106, 50, 91, 27], [106, 52, 91, 28], [106, 53, 91, 29], [106, 55, 91, 31], [107, 4, 92, 2, "exports"], [107, 11, 92, 2], [107, 12, 92, 2, "setNativeProps"], [107, 26, 92, 2], [107, 29, 92, 2, "setNativeProps"], [107, 43, 92, 16], [107, 46, 92, 19, "setNativePropsChromeDebugger"], [107, 74, 92, 47], [108, 2, 93, 0], [108, 3, 93, 1], [108, 9, 93, 7], [109, 4, 94, 2, "exports"], [109, 11, 94, 2], [109, 12, 94, 2, "setNativeProps"], [109, 26, 94, 2], [109, 29, 94, 2, "setNativeProps"], [109, 43, 94, 16], [109, 46, 94, 19, "setNativePropsDefault"], [109, 67, 94, 40], [110, 2, 95, 0], [111, 0, 95, 1], [111, 3]], "functionMap": {"names": ["<global>", "setNativePropsFabric", "setNativePropsPaper", "setNativePropsJest", "setNativePropsChromeDebugger", "setNativePropsDefault"], "mappings": "AAA;ACsC;CDY;AEE;CFa;AGE;CHE;AIE;CJE;AKE;CLE"}}, "type": "js/module"}]}