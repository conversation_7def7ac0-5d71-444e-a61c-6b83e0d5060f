{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.UnhandledLinkingContext = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var MISSING_CONTEXT_ERROR = \"Couldn't find an UnhandledLinkingContext context.\";\n  var UnhandledLinkingContext = exports.UnhandledLinkingContext = /*#__PURE__*/React.createContext({\n    get lastUnhandledLink() {\n      throw new Error(MISSING_CONTEXT_ERROR);\n    },\n    get setLastUnhandledLink() {\n      throw new Error(MISSING_CONTEXT_ERROR);\n    }\n  });\n  UnhandledLinkingContext.displayName = 'UnhandledLinkingContext';\n});", "lineCount": 20, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "UnhandledLinkingContext"], [7, 33, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 3, 31], [9, 11, 3, 31, "_interopRequireWildcard"], [9, 35, 3, 31, "e"], [9, 36, 3, 31], [9, 38, 3, 31, "t"], [9, 39, 3, 31], [9, 68, 3, 31, "WeakMap"], [9, 75, 3, 31], [9, 81, 3, 31, "r"], [9, 82, 3, 31], [9, 89, 3, 31, "WeakMap"], [9, 96, 3, 31], [9, 100, 3, 31, "n"], [9, 101, 3, 31], [9, 108, 3, 31, "WeakMap"], [9, 115, 3, 31], [9, 127, 3, 31, "_interopRequireWildcard"], [9, 150, 3, 31], [9, 162, 3, 31, "_interopRequireWildcard"], [9, 163, 3, 31, "e"], [9, 164, 3, 31], [9, 166, 3, 31, "t"], [9, 167, 3, 31], [9, 176, 3, 31, "t"], [9, 177, 3, 31], [9, 181, 3, 31, "e"], [9, 182, 3, 31], [9, 186, 3, 31, "e"], [9, 187, 3, 31], [9, 188, 3, 31, "__esModule"], [9, 198, 3, 31], [9, 207, 3, 31, "e"], [9, 208, 3, 31], [9, 214, 3, 31, "o"], [9, 215, 3, 31], [9, 217, 3, 31, "i"], [9, 218, 3, 31], [9, 220, 3, 31, "f"], [9, 221, 3, 31], [9, 226, 3, 31, "__proto__"], [9, 235, 3, 31], [9, 243, 3, 31, "default"], [9, 250, 3, 31], [9, 252, 3, 31, "e"], [9, 253, 3, 31], [9, 270, 3, 31, "e"], [9, 271, 3, 31], [9, 294, 3, 31, "e"], [9, 295, 3, 31], [9, 320, 3, 31, "e"], [9, 321, 3, 31], [9, 330, 3, 31, "f"], [9, 331, 3, 31], [9, 337, 3, 31, "o"], [9, 338, 3, 31], [9, 341, 3, 31, "t"], [9, 342, 3, 31], [9, 345, 3, 31, "n"], [9, 346, 3, 31], [9, 349, 3, 31, "r"], [9, 350, 3, 31], [9, 358, 3, 31, "o"], [9, 359, 3, 31], [9, 360, 3, 31, "has"], [9, 363, 3, 31], [9, 364, 3, 31, "e"], [9, 365, 3, 31], [9, 375, 3, 31, "o"], [9, 376, 3, 31], [9, 377, 3, 31, "get"], [9, 380, 3, 31], [9, 381, 3, 31, "e"], [9, 382, 3, 31], [9, 385, 3, 31, "o"], [9, 386, 3, 31], [9, 387, 3, 31, "set"], [9, 390, 3, 31], [9, 391, 3, 31, "e"], [9, 392, 3, 31], [9, 394, 3, 31, "f"], [9, 395, 3, 31], [9, 409, 3, 31, "_t"], [9, 411, 3, 31], [9, 415, 3, 31, "e"], [9, 416, 3, 31], [9, 432, 3, 31, "_t"], [9, 434, 3, 31], [9, 441, 3, 31, "hasOwnProperty"], [9, 455, 3, 31], [9, 456, 3, 31, "call"], [9, 460, 3, 31], [9, 461, 3, 31, "e"], [9, 462, 3, 31], [9, 464, 3, 31, "_t"], [9, 466, 3, 31], [9, 473, 3, 31, "i"], [9, 474, 3, 31], [9, 478, 3, 31, "o"], [9, 479, 3, 31], [9, 482, 3, 31, "Object"], [9, 488, 3, 31], [9, 489, 3, 31, "defineProperty"], [9, 503, 3, 31], [9, 508, 3, 31, "Object"], [9, 514, 3, 31], [9, 515, 3, 31, "getOwnPropertyDescriptor"], [9, 539, 3, 31], [9, 540, 3, 31, "e"], [9, 541, 3, 31], [9, 543, 3, 31, "_t"], [9, 545, 3, 31], [9, 552, 3, 31, "i"], [9, 553, 3, 31], [9, 554, 3, 31, "get"], [9, 557, 3, 31], [9, 561, 3, 31, "i"], [9, 562, 3, 31], [9, 563, 3, 31, "set"], [9, 566, 3, 31], [9, 570, 3, 31, "o"], [9, 571, 3, 31], [9, 572, 3, 31, "f"], [9, 573, 3, 31], [9, 575, 3, 31, "_t"], [9, 577, 3, 31], [9, 579, 3, 31, "i"], [9, 580, 3, 31], [9, 584, 3, 31, "f"], [9, 585, 3, 31], [9, 586, 3, 31, "_t"], [9, 588, 3, 31], [9, 592, 3, 31, "e"], [9, 593, 3, 31], [9, 594, 3, 31, "_t"], [9, 596, 3, 31], [9, 607, 3, 31, "f"], [9, 608, 3, 31], [9, 613, 3, 31, "e"], [9, 614, 3, 31], [9, 616, 3, 31, "t"], [9, 617, 3, 31], [10, 2, 4, 0], [10, 6, 4, 6, "MISSING_CONTEXT_ERROR"], [10, 27, 4, 27], [10, 30, 4, 30], [10, 81, 4, 81], [11, 2, 5, 7], [11, 6, 5, 13, "UnhandledLinkingContext"], [11, 29, 5, 36], [11, 32, 5, 36, "exports"], [11, 39, 5, 36], [11, 40, 5, 36, "UnhandledLinkingContext"], [11, 63, 5, 36], [11, 66, 5, 39], [11, 79, 5, 52, "React"], [11, 84, 5, 57], [11, 85, 5, 58, "createContext"], [11, 98, 5, 71], [11, 99, 5, 72], [12, 4, 6, 2], [12, 8, 6, 6, "lastUnhandledLink"], [12, 25, 6, 23, "lastUnhandledLink"], [12, 26, 6, 23], [12, 28, 6, 26], [13, 6, 7, 4], [13, 12, 7, 10], [13, 16, 7, 14, "Error"], [13, 21, 7, 19], [13, 22, 7, 20, "MISSING_CONTEXT_ERROR"], [13, 43, 7, 41], [13, 44, 7, 42], [14, 4, 8, 2], [14, 5, 8, 3], [15, 4, 9, 2], [15, 8, 9, 6, "setLastUnhandledLink"], [15, 28, 9, 26, "setLastUnhandledLink"], [15, 29, 9, 26], [15, 31, 9, 29], [16, 6, 10, 4], [16, 12, 10, 10], [16, 16, 10, 14, "Error"], [16, 21, 10, 19], [16, 22, 10, 20, "MISSING_CONTEXT_ERROR"], [16, 43, 10, 41], [16, 44, 10, 42], [17, 4, 11, 2], [18, 2, 12, 0], [18, 3, 12, 1], [18, 4, 12, 2], [19, 2, 13, 0, "UnhandledLinkingContext"], [19, 25, 13, 23], [19, 26, 13, 24, "displayName"], [19, 37, 13, 35], [19, 40, 13, 38], [19, 65, 13, 63], [20, 0, 13, 64], [20, 3]], "functionMap": {"names": ["<global>", "React.createContext$argument_0.get__lastUnhandledLink", "React.createContext$argument_0.get__setLastUnhandledLink"], "mappings": "AAA;ECK;GDE;EEC;GFE"}}, "type": "js/module"}]}