{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var dummyPoint = {\n    x: undefined,\n    y: undefined\n  };\n  function pointsDiffer(one, two) {\n    one = one || dummyPoint;\n    two = two || dummyPoint;\n    return one !== two && (one.x !== two.x || one.y !== two.y);\n  }\n  var _default = exports.default = pointsDiffer;\n});", "lineCount": 18, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 19, 0], [8, 6, 19, 6, "dummyPoint"], [8, 16, 19, 16], [8, 19, 19, 19], [9, 4, 19, 20, "x"], [9, 5, 19, 21], [9, 7, 19, 23, "undefined"], [9, 16, 19, 32], [10, 4, 19, 34, "y"], [10, 5, 19, 35], [10, 7, 19, 37, "undefined"], [11, 2, 19, 46], [11, 3, 19, 47], [12, 2, 21, 0], [12, 11, 21, 9, "points<PERSON><PERSON><PERSON>"], [12, 23, 21, 21, "points<PERSON><PERSON><PERSON>"], [12, 24, 21, 22, "one"], [12, 27, 21, 33], [12, 29, 21, 35, "two"], [12, 32, 21, 46], [12, 34, 21, 57], [13, 4, 22, 2, "one"], [13, 7, 22, 5], [13, 10, 22, 8, "one"], [13, 13, 22, 11], [13, 17, 22, 15, "dummyPoint"], [13, 27, 22, 25], [14, 4, 23, 2, "two"], [14, 7, 23, 5], [14, 10, 23, 8, "two"], [14, 13, 23, 11], [14, 17, 23, 15, "dummyPoint"], [14, 27, 23, 25], [15, 4, 24, 2], [15, 11, 24, 9, "one"], [15, 14, 24, 12], [15, 19, 24, 17, "two"], [15, 22, 24, 20], [15, 27, 24, 25, "one"], [15, 30, 24, 28], [15, 31, 24, 29, "x"], [15, 32, 24, 30], [15, 37, 24, 35, "two"], [15, 40, 24, 38], [15, 41, 24, 39, "x"], [15, 42, 24, 40], [15, 46, 24, 44, "one"], [15, 49, 24, 47], [15, 50, 24, 48, "y"], [15, 51, 24, 49], [15, 56, 24, 54, "two"], [15, 59, 24, 57], [15, 60, 24, 58, "y"], [15, 61, 24, 59], [15, 62, 24, 60], [16, 2, 25, 0], [17, 2, 25, 1], [17, 6, 25, 1, "_default"], [17, 14, 25, 1], [17, 17, 25, 1, "exports"], [17, 24, 25, 1], [17, 25, 25, 1, "default"], [17, 32, 25, 1], [17, 35, 27, 15, "points<PERSON><PERSON><PERSON>"], [17, 47, 27, 27], [18, 0, 27, 27], [18, 3]], "functionMap": {"names": ["<global>", "points<PERSON><PERSON><PERSON>"], "mappings": "AAA;ACoB;CDI"}}, "type": "js/module"}]}