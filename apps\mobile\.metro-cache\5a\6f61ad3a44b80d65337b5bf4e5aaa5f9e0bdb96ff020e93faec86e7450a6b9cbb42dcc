{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "../../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 47, "index": 119}}], "key": "eT202ujluoOcHDbauyWnF/muvbc=", "exportNames": ["*"]}}, {"name": "../../specs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 120}, "end": {"line": 4, "column": 50, "index": 170}}], "key": "nJUmFHqb/g9LPxwGJGGJ7E1x+BU=", "exportNames": ["*"]}}, {"name": "../valueUnpacker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 171}, "end": {"line": 5, "column": 56, "index": 227}}], "key": "r0asaYVaqHPc/qSDP8K4TEwZTkU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createNativeWorkletsModule = createNativeWorkletsModule;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _errors = require(_dependencyMap[5], \"../../errors\");\n  var _specs = require(_dependencyMap[6], \"../../specs\");\n  var _valueUnpacker = require(_dependencyMap[7], \"../valueUnpacker\");\n  function createNativeWorkletsModule() {\n    return new NativeWorklets();\n  }\n  var _workletsModuleProxy = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"workletsModuleProxy\");\n  var NativeWorklets = /*#__PURE__*/function () {\n    function NativeWorklets() {\n      (0, _classCallCheck2.default)(this, NativeWorklets);\n      Object.defineProperty(this, _workletsModuleProxy, {\n        writable: true,\n        value: void 0\n      });\n      if (global.__workletsModuleProxy === undefined) {\n        var valueUnpackerCode = (0, _valueUnpacker.getValueUnpackerCode)();\n        _specs.WorkletsTurboModule?.installTurboModule(valueUnpackerCode);\n      }\n      if (global.__workletsModuleProxy === undefined) {\n        throw new _errors.ReanimatedError(`Native part of Reanimated doesn't seem to be initialized (Worklets).\nSee https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#native-part-of-reanimated-doesnt-seem-to-be-initialized for more details.`);\n      }\n      (0, _classPrivateFieldLooseBase2.default)(this, _workletsModuleProxy)[_workletsModuleProxy] = global.__workletsModuleProxy;\n    }\n    return (0, _createClass2.default)(NativeWorklets, [{\n      key: \"makeShareableClone\",\n      value: function makeShareableClone(value, shouldPersistRemote, nativeStateSource) {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _workletsModuleProxy)[_workletsModuleProxy].makeShareableClone(value, shouldPersistRemote, nativeStateSource);\n      }\n    }]);\n  }();\n});", "lineCount": 44, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "createNativeWorkletsModule"], [8, 36, 1, 13], [8, 39, 1, 13, "createNativeWorkletsModule"], [8, 65, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_classCallCheck2"], [9, 22, 1, 13], [9, 25, 1, 13, "_interopRequireDefault"], [9, 47, 1, 13], [9, 48, 1, 13, "require"], [9, 55, 1, 13], [9, 56, 1, 13, "_dependencyMap"], [9, 70, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_createClass2"], [10, 19, 1, 13], [10, 22, 1, 13, "_interopRequireDefault"], [10, 44, 1, 13], [10, 45, 1, 13, "require"], [10, 52, 1, 13], [10, 53, 1, 13, "_dependencyMap"], [10, 67, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_classPrivateFieldLooseBase2"], [11, 34, 1, 13], [11, 37, 1, 13, "_interopRequireDefault"], [11, 59, 1, 13], [11, 60, 1, 13, "require"], [11, 67, 1, 13], [11, 68, 1, 13, "_dependencyMap"], [11, 82, 1, 13], [12, 2, 1, 13], [12, 6, 1, 13, "_classPrivateFieldLooseKey2"], [12, 33, 1, 13], [12, 36, 1, 13, "_interopRequireDefault"], [12, 58, 1, 13], [12, 59, 1, 13, "require"], [12, 66, 1, 13], [12, 67, 1, 13, "_dependencyMap"], [12, 81, 1, 13], [13, 2, 3, 0], [13, 6, 3, 0, "_errors"], [13, 13, 3, 0], [13, 16, 3, 0, "require"], [13, 23, 3, 0], [13, 24, 3, 0, "_dependencyMap"], [13, 38, 3, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_specs"], [14, 12, 4, 0], [14, 15, 4, 0, "require"], [14, 22, 4, 0], [14, 23, 4, 0, "_dependencyMap"], [14, 37, 4, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_valueUnpacker"], [15, 20, 5, 0], [15, 23, 5, 0, "require"], [15, 30, 5, 0], [15, 31, 5, 0, "_dependencyMap"], [15, 45, 5, 0], [16, 2, 8, 7], [16, 11, 8, 16, "createNativeWorkletsModule"], [16, 37, 8, 42, "createNativeWorkletsModule"], [16, 38, 8, 42], [16, 40, 8, 62], [17, 4, 9, 2], [17, 11, 9, 9], [17, 15, 9, 13, "NativeWorklets"], [17, 29, 9, 27], [17, 30, 9, 28], [17, 31, 9, 29], [18, 2, 10, 0], [19, 2, 10, 1], [19, 6, 10, 1, "_workletsModuleProxy"], [19, 26, 10, 1], [19, 46, 10, 1, "_classPrivateFieldLooseKey2"], [19, 73, 10, 1], [19, 74, 10, 1, "default"], [19, 81, 10, 1], [20, 2, 10, 1], [20, 6, 12, 6, "NativeWorklets"], [20, 20, 12, 20], [21, 4, 15, 2], [21, 13, 15, 2, "NativeWorklets"], [21, 28, 15, 2], [21, 30, 15, 16], [22, 6, 15, 16], [22, 10, 15, 16, "_classCallCheck2"], [22, 26, 15, 16], [22, 27, 15, 16, "default"], [22, 34, 15, 16], [22, 42, 15, 16, "NativeWorklets"], [22, 56, 15, 16], [23, 6, 15, 16, "Object"], [23, 12, 15, 16], [23, 13, 15, 16, "defineProperty"], [23, 27, 15, 16], [23, 34, 15, 16, "_workletsModuleProxy"], [23, 54, 15, 16], [24, 8, 15, 16, "writable"], [24, 16, 15, 16], [25, 8, 15, 16, "value"], [25, 13, 15, 16], [26, 6, 15, 16], [27, 6, 16, 4], [27, 10, 16, 8, "global"], [27, 16, 16, 14], [27, 17, 16, 15, "__workletsModuleProxy"], [27, 38, 16, 36], [27, 43, 16, 41, "undefined"], [27, 52, 16, 50], [27, 54, 16, 52], [28, 8, 17, 6], [28, 12, 17, 12, "valueUnpackerCode"], [28, 29, 17, 29], [28, 32, 17, 32], [28, 36, 17, 32, "getValueUnpackerCode"], [28, 71, 17, 52], [28, 73, 17, 53], [28, 74, 17, 54], [29, 8, 18, 6, "WorkletsTurboModule"], [29, 34, 18, 25], [29, 36, 18, 27, "installTurboModule"], [29, 54, 18, 45], [29, 55, 18, 46, "valueUnpackerCode"], [29, 72, 18, 63], [29, 73, 18, 64], [30, 6, 19, 4], [31, 6, 20, 4], [31, 10, 20, 8, "global"], [31, 16, 20, 14], [31, 17, 20, 15, "__workletsModuleProxy"], [31, 38, 20, 36], [31, 43, 20, 41, "undefined"], [31, 52, 20, 50], [31, 54, 20, 52], [32, 8, 21, 6], [32, 14, 21, 12], [32, 18, 21, 16, "ReanimatedError"], [32, 41, 21, 31], [32, 42, 22, 8], [33, 0, 23, 0], [33, 157, 24, 6], [33, 158, 24, 7], [34, 6, 25, 4], [35, 6, 26, 4], [35, 10, 26, 4, "_classPrivateFieldLooseBase2"], [35, 38, 26, 4], [35, 39, 26, 4, "default"], [35, 46, 26, 4], [35, 52, 26, 8], [35, 54, 26, 8, "_workletsModuleProxy"], [35, 74, 26, 8], [35, 76, 26, 8, "_workletsModuleProxy"], [35, 96, 26, 8], [35, 100, 26, 32, "global"], [35, 106, 26, 38], [35, 107, 26, 39, "__workletsModuleProxy"], [35, 128, 26, 60], [36, 4, 27, 2], [37, 4, 27, 3], [37, 15, 27, 3, "_createClass2"], [37, 28, 27, 3], [37, 29, 27, 3, "default"], [37, 36, 27, 3], [37, 38, 27, 3, "NativeWorklets"], [37, 52, 27, 3], [38, 6, 27, 3, "key"], [38, 9, 27, 3], [39, 6, 27, 3, "value"], [39, 11, 27, 3], [39, 13, 29, 2], [39, 22, 29, 2, "makeShareableClone"], [39, 40, 29, 20, "makeShareableClone"], [39, 41, 30, 4, "value"], [39, 46, 30, 12], [39, 48, 31, 4, "shouldPersistRemote"], [39, 67, 31, 32], [39, 69, 32, 4, "nativeStateSource"], [39, 86, 32, 30], [39, 88, 33, 4], [40, 8, 34, 4], [40, 15, 34, 11], [40, 19, 34, 11, "_classPrivateFieldLooseBase2"], [40, 47, 34, 11], [40, 48, 34, 11, "default"], [40, 55, 34, 11], [40, 61, 34, 15], [40, 63, 34, 15, "_workletsModuleProxy"], [40, 83, 34, 15], [40, 85, 34, 15, "_workletsModuleProxy"], [40, 105, 34, 15], [40, 107, 34, 37, "makeShareableClone"], [40, 125, 34, 55], [40, 126, 35, 6, "value"], [40, 131, 35, 11], [40, 133, 36, 6, "shouldPersistRemote"], [40, 152, 36, 25], [40, 154, 37, 6, "nativeStateSource"], [40, 171, 38, 4], [40, 172, 38, 5], [41, 6, 39, 2], [42, 4, 39, 3], [43, 2, 39, 3], [44, 0, 39, 3], [44, 3]], "functionMap": {"names": ["<global>", "createNativeWorkletsModule", "NativeWorklets", "constructor", "makeShareableClone"], "mappings": "AAA;OCO;CDE;AEE;ECG;GDY;EEE;GFU;CFC"}}, "type": "js/module"}]}