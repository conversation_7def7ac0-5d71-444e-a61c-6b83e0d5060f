{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 42}, "end": {"line": 9, "column": 22, "index": 152}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 154}, "end": {"line": 10, "column": 62, "index": 216}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo/vector-icons", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 218}, "end": {"line": 11, "column": 46, "index": 264}}], "key": "ow7vkrqkIckRjlSi/+MhMmRYtUE=", "exportNames": ["*"]}}, {"name": "../components/FooterNavigation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 266}, "end": {"line": 12, "column": 62, "index": 328}}], "key": "i0wsjvqzeC9AyK9fcghZiqZFu6Y=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = OrdersScreen;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[4], \"react-native-safe-area-context\");\n  var _vectorIcons = require(_dependencyMap[5], \"@expo/vector-icons\");\n  var _FooterNavigation = _interopRequireDefault(require(_dependencyMap[6], \"../components/FooterNavigation\"));\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\apps\\\\mobile\\\\src\\\\screens\\\\OrdersScreen.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function OrdersScreen(_ref) {\n    _s();\n    var navigation = _ref.navigation;\n    var _useState = (0, _react.useState)(''),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      searchQuery = _useState2[0],\n      setSearchQuery = _useState2[1];\n    var _useState3 = (0, _react.useState)('All'),\n      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),\n      selectedFilter = _useState4[0],\n      setSelectedFilter = _useState4[1];\n    var filters = ['All', 'Active', 'Completed', 'Cancelled'];\n    var orders = [{\n      id: '1',\n      orderNumber: '#ORD-2024-001',\n      restaurant: 'Burger Palace',\n      items: ['2x Chicken Burger', '1x Fries', '1x Coke'],\n      total: 28.97,\n      status: 'Active',\n      orderTime: '2024-01-15 14:30',\n      estimatedDelivery: '15-25 min',\n      image: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400'\n    }, {\n      id: '2',\n      orderNumber: '#ORD-2024-002',\n      restaurant: 'Pizza Corner',\n      items: ['1x Margherita Pizza', '1x Garlic Bread'],\n      total: 22.98,\n      status: 'Completed',\n      orderTime: '2024-01-14 19:45',\n      deliveredTime: '2024-01-14 20:15',\n      image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400'\n    }, {\n      id: '3',\n      orderNumber: '#ORD-2024-003',\n      restaurant: 'Sushi Express',\n      items: ['1x Salmon Roll', '1x Tuna Sashimi', '1x Miso Soup'],\n      total: 35.50,\n      status: 'Completed',\n      orderTime: '2024-01-13 18:20',\n      deliveredTime: '2024-01-13 19:05',\n      image: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=400'\n    }, {\n      id: '4',\n      orderNumber: '#ORD-2024-004',\n      restaurant: 'Green Bowl',\n      items: ['1x Caesar Salad', '1x Smoothie'],\n      total: 15.99,\n      status: 'Cancelled',\n      orderTime: '2024-01-12 12:15',\n      cancelledTime: '2024-01-12 12:45',\n      image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400'\n    }];\n    var filteredOrders = orders.filter(order => {\n      var matchesSearch = order.restaurant.toLowerCase().includes(searchQuery.toLowerCase()) || order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase());\n      var matchesFilter = selectedFilter === 'All' || order.status === selectedFilter;\n      return matchesSearch && matchesFilter;\n    });\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: {\n        flex: 1,\n        backgroundColor: '#f9fafb'\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f3a823'\n        },\n        edges: ['top']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: {\n          flex: 1,\n          backgroundColor: '#f9fafb'\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            flexDirection: 'row',\n            alignItems: 'center',\n            paddingHorizontal: 16,\n            paddingVertical: 12,\n            backgroundColor: '#fff',\n            borderBottomWidth: 1,\n            borderBottomColor: '#e5e7eb'\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 24,\n              fontWeight: 'bold',\n              color: '#111827',\n              flex: 1\n            },\n            children: \"Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 9\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n            style: {\n              padding: 8\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n              name: \"filter-outline\",\n              size: 24,\n              color: \"#6b7280\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.ScrollView, {\n          style: {\n            flex: 1\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              padding: 16\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: {\n                flexDirection: 'row',\n                alignItems: 'center',\n                backgroundColor: '#fff',\n                borderRadius: 12,\n                paddingHorizontal: 16,\n                paddingVertical: 12,\n                shadowColor: '#000',\n                shadowOffset: {\n                  width: 0,\n                  height: 1\n                },\n                shadowOpacity: 0.05,\n                shadowRadius: 2,\n                elevation: 2\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                name: \"search-outline\",\n                size: 20,\n                color: \"#9ca3af\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 13\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TextInput, {\n                style: {\n                  flex: 1,\n                  marginLeft: 12,\n                  fontSize: 16,\n                  color: '#111827'\n                },\n                placeholder: \"Search orders...\",\n                placeholderTextColor: \"#9ca3af\",\n                value: searchQuery,\n                onChangeText: setSearchQuery\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 9\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.ScrollView, {\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            style: {\n              paddingLeft: 16,\n              marginBottom: 16\n            },\n            children: filters.map(filter => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              onPress: () => setSelectedFilter(filter),\n              style: {\n                backgroundColor: selectedFilter === filter ? '#f97316' : '#fff',\n                paddingHorizontal: 20,\n                paddingVertical: 10,\n                borderRadius: 20,\n                marginRight: 12,\n                borderWidth: 1,\n                borderColor: selectedFilter === filter ? '#f97316' : '#e5e7eb'\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                style: {\n                  color: selectedFilter === filter ? '#fff' : '#6b7280',\n                  fontWeight: '600',\n                  fontSize: 14\n                },\n                children: filter\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 15\n              }, this)\n            }, filter, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 13\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 9\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              paddingHorizontal: 16\n            },\n            children: filteredOrders.map(order => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              style: {\n                backgroundColor: '#fff',\n                borderRadius: 16,\n                marginBottom: 16,\n                shadowColor: '#000',\n                shadowOffset: {\n                  width: 0,\n                  height: 2\n                },\n                shadowOpacity: 0.1,\n                shadowRadius: 4,\n                elevation: 3\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: {\n                  padding: 16\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                  style: {\n                    flexDirection: 'row',\n                    justifyContent: 'space-between',\n                    alignItems: 'flex-start',\n                    marginBottom: 12\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        fontSize: 16,\n                        fontWeight: 'bold',\n                        color: '#111827',\n                        marginBottom: 4\n                      },\n                      children: order.orderNumber\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        fontSize: 14,\n                        color: '#6b7280',\n                        marginBottom: 4\n                      },\n                      children: [\"from \", order.restaurant]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 179,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        fontSize: 12,\n                        color: '#9ca3af'\n                      },\n                      children: order.orderTime\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                    style: {\n                      backgroundColor: order.status === 'Active' ? '#f3a823' : order.status === 'Completed' ? '#10b981' : '#ef4444',\n                      paddingHorizontal: 12,\n                      paddingVertical: 6,\n                      borderRadius: 16\n                    },\n                    children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        color: '#fff',\n                        fontSize: 12,\n                        fontWeight: '600'\n                      },\n                      children: order.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                  style: {\n                    flexDirection: 'row',\n                    alignItems: 'center',\n                    marginBottom: 12\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Image, {\n                    source: {\n                      uri: order.image\n                    },\n                    style: {\n                      width: 60,\n                      height: 60,\n                      borderRadius: 8,\n                      marginRight: 12\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                    style: {\n                      flex: 1\n                    },\n                    children: order.items.map((item, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        fontSize: 14,\n                        color: '#374151',\n                        marginBottom: 2\n                      },\n                      children: item\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 23\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                  style: {\n                    flexDirection: 'row',\n                    justifyContent: 'space-between',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                    style: {\n                      fontSize: 18,\n                      fontWeight: 'bold',\n                      color: '#111827'\n                    },\n                    children: [\"$\", order.total.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 19\n                  }, this), order.status === 'Active' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                    style: {\n                      fontSize: 12,\n                      color: '#f3a823',\n                      fontWeight: '600'\n                    },\n                    children: [\"Arriving in \", order.estimatedDelivery]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 21\n                  }, this), order.status === 'Completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n                    style: {\n                      backgroundColor: '#f3f4f6',\n                      paddingHorizontal: 12,\n                      paddingVertical: 6,\n                      borderRadius: 8\n                    },\n                    children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        color: '#374151',\n                        fontSize: 12,\n                        fontWeight: '600'\n                      },\n                      children: \"Reorder\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 15\n              }, this)\n            }, order.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 13\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 9\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              height: 100\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_FooterNavigation.default, {\n        navigation: navigation,\n        activeScreen: \"Orders\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f9fafb'\n        },\n        edges: ['bottom']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 5\n    }, this);\n  }\n  _s(OrdersScreen, \"hLfmzZ95P4Ia7Ei0fZvVgsZ1W10=\");\n  _c = OrdersScreen;\n  var _c;\n  $RefreshReg$(_c, \"OrdersScreen\");\n});", "lineCount": 495, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireWildcard"], [8, 38, 1, 0], [8, 39, 1, 0, "require"], [8, 46, 1, 0], [8, 47, 1, 0, "_dependencyMap"], [8, 61, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 10, 0], [10, 6, 10, 0, "_reactNativeSafeAreaContext"], [10, 33, 10, 0], [10, 36, 10, 0, "require"], [10, 43, 10, 0], [10, 44, 10, 0, "_dependencyMap"], [10, 58, 10, 0], [11, 2, 11, 0], [11, 6, 11, 0, "_vectorIcons"], [11, 18, 11, 0], [11, 21, 11, 0, "require"], [11, 28, 11, 0], [11, 29, 11, 0, "_dependencyMap"], [11, 43, 11, 0], [12, 2, 12, 0], [12, 6, 12, 0, "_FooterNavigation"], [12, 23, 12, 0], [12, 26, 12, 0, "_interopRequireDefault"], [12, 48, 12, 0], [12, 49, 12, 0, "require"], [12, 56, 12, 0], [12, 57, 12, 0, "_dependencyMap"], [12, 71, 12, 0], [13, 2, 12, 62], [13, 6, 12, 62, "_jsxDevRuntime"], [13, 20, 12, 62], [13, 23, 12, 62, "require"], [13, 30, 12, 62], [13, 31, 12, 62, "_dependencyMap"], [13, 45, 12, 62], [14, 2, 12, 62], [14, 6, 12, 62, "_jsxFileName"], [14, 18, 12, 62], [15, 4, 12, 62, "_s"], [15, 6, 12, 62], [15, 9, 12, 62, "$RefreshSig$"], [15, 21, 12, 62], [16, 2, 12, 62], [16, 11, 12, 62, "_interopRequireWildcard"], [16, 35, 12, 62, "e"], [16, 36, 12, 62], [16, 38, 12, 62, "t"], [16, 39, 12, 62], [16, 68, 12, 62, "WeakMap"], [16, 75, 12, 62], [16, 81, 12, 62, "r"], [16, 82, 12, 62], [16, 89, 12, 62, "WeakMap"], [16, 96, 12, 62], [16, 100, 12, 62, "n"], [16, 101, 12, 62], [16, 108, 12, 62, "WeakMap"], [16, 115, 12, 62], [16, 127, 12, 62, "_interopRequireWildcard"], [16, 150, 12, 62], [16, 162, 12, 62, "_interopRequireWildcard"], [16, 163, 12, 62, "e"], [16, 164, 12, 62], [16, 166, 12, 62, "t"], [16, 167, 12, 62], [16, 176, 12, 62, "t"], [16, 177, 12, 62], [16, 181, 12, 62, "e"], [16, 182, 12, 62], [16, 186, 12, 62, "e"], [16, 187, 12, 62], [16, 188, 12, 62, "__esModule"], [16, 198, 12, 62], [16, 207, 12, 62, "e"], [16, 208, 12, 62], [16, 214, 12, 62, "o"], [16, 215, 12, 62], [16, 217, 12, 62, "i"], [16, 218, 12, 62], [16, 220, 12, 62, "f"], [16, 221, 12, 62], [16, 226, 12, 62, "__proto__"], [16, 235, 12, 62], [16, 243, 12, 62, "default"], [16, 250, 12, 62], [16, 252, 12, 62, "e"], [16, 253, 12, 62], [16, 270, 12, 62, "e"], [16, 271, 12, 62], [16, 294, 12, 62, "e"], [16, 295, 12, 62], [16, 320, 12, 62, "e"], [16, 321, 12, 62], [16, 330, 12, 62, "f"], [16, 331, 12, 62], [16, 337, 12, 62, "o"], [16, 338, 12, 62], [16, 341, 12, 62, "t"], [16, 342, 12, 62], [16, 345, 12, 62, "n"], [16, 346, 12, 62], [16, 349, 12, 62, "r"], [16, 350, 12, 62], [16, 358, 12, 62, "o"], [16, 359, 12, 62], [16, 360, 12, 62, "has"], [16, 363, 12, 62], [16, 364, 12, 62, "e"], [16, 365, 12, 62], [16, 375, 12, 62, "o"], [16, 376, 12, 62], [16, 377, 12, 62, "get"], [16, 380, 12, 62], [16, 381, 12, 62, "e"], [16, 382, 12, 62], [16, 385, 12, 62, "o"], [16, 386, 12, 62], [16, 387, 12, 62, "set"], [16, 390, 12, 62], [16, 391, 12, 62, "e"], [16, 392, 12, 62], [16, 394, 12, 62, "f"], [16, 395, 12, 62], [16, 409, 12, 62, "_t"], [16, 411, 12, 62], [16, 415, 12, 62, "e"], [16, 416, 12, 62], [16, 432, 12, 62, "_t"], [16, 434, 12, 62], [16, 441, 12, 62, "hasOwnProperty"], [16, 455, 12, 62], [16, 456, 12, 62, "call"], [16, 460, 12, 62], [16, 461, 12, 62, "e"], [16, 462, 12, 62], [16, 464, 12, 62, "_t"], [16, 466, 12, 62], [16, 473, 12, 62, "i"], [16, 474, 12, 62], [16, 478, 12, 62, "o"], [16, 479, 12, 62], [16, 482, 12, 62, "Object"], [16, 488, 12, 62], [16, 489, 12, 62, "defineProperty"], [16, 503, 12, 62], [16, 508, 12, 62, "Object"], [16, 514, 12, 62], [16, 515, 12, 62, "getOwnPropertyDescriptor"], [16, 539, 12, 62], [16, 540, 12, 62, "e"], [16, 541, 12, 62], [16, 543, 12, 62, "_t"], [16, 545, 12, 62], [16, 552, 12, 62, "i"], [16, 553, 12, 62], [16, 554, 12, 62, "get"], [16, 557, 12, 62], [16, 561, 12, 62, "i"], [16, 562, 12, 62], [16, 563, 12, 62, "set"], [16, 566, 12, 62], [16, 570, 12, 62, "o"], [16, 571, 12, 62], [16, 572, 12, 62, "f"], [16, 573, 12, 62], [16, 575, 12, 62, "_t"], [16, 577, 12, 62], [16, 579, 12, 62, "i"], [16, 580, 12, 62], [16, 584, 12, 62, "f"], [16, 585, 12, 62], [16, 586, 12, 62, "_t"], [16, 588, 12, 62], [16, 592, 12, 62, "e"], [16, 593, 12, 62], [16, 594, 12, 62, "_t"], [16, 596, 12, 62], [16, 607, 12, 62, "f"], [16, 608, 12, 62], [16, 613, 12, 62, "e"], [16, 614, 12, 62], [16, 616, 12, 62, "t"], [16, 617, 12, 62], [17, 2, 14, 15], [17, 11, 14, 24, "OrdersScreen"], [17, 23, 14, 36, "OrdersScreen"], [17, 24, 14, 36, "_ref"], [17, 28, 14, 36], [17, 30, 14, 58], [18, 4, 14, 58, "_s"], [18, 6, 14, 58], [19, 4, 14, 58], [19, 8, 14, 39, "navigation"], [19, 18, 14, 49], [19, 21, 14, 49, "_ref"], [19, 25, 14, 49], [19, 26, 14, 39, "navigation"], [19, 36, 14, 49], [20, 4, 15, 2], [20, 8, 15, 2, "_useState"], [20, 17, 15, 2], [20, 20, 15, 40], [20, 24, 15, 40, "useState"], [20, 39, 15, 48], [20, 41, 15, 49], [20, 43, 15, 51], [20, 44, 15, 52], [21, 6, 15, 52, "_useState2"], [21, 16, 15, 52], [21, 23, 15, 52, "_slicedToArray2"], [21, 38, 15, 52], [21, 39, 15, 52, "default"], [21, 46, 15, 52], [21, 48, 15, 52, "_useState"], [21, 57, 15, 52], [22, 6, 15, 9, "searchQuery"], [22, 17, 15, 20], [22, 20, 15, 20, "_useState2"], [22, 30, 15, 20], [23, 6, 15, 22, "setSearch<PERSON>uery"], [23, 20, 15, 36], [23, 23, 15, 36, "_useState2"], [23, 33, 15, 36], [24, 4, 16, 2], [24, 8, 16, 2, "_useState3"], [24, 18, 16, 2], [24, 21, 16, 46], [24, 25, 16, 46, "useState"], [24, 40, 16, 54], [24, 42, 16, 55], [24, 47, 16, 60], [24, 48, 16, 61], [25, 6, 16, 61, "_useState4"], [25, 16, 16, 61], [25, 23, 16, 61, "_slicedToArray2"], [25, 38, 16, 61], [25, 39, 16, 61, "default"], [25, 46, 16, 61], [25, 48, 16, 61, "_useState3"], [25, 58, 16, 61], [26, 6, 16, 9, "<PERSON><PERSON><PERSON><PERSON>"], [26, 20, 16, 23], [26, 23, 16, 23, "_useState4"], [26, 33, 16, 23], [27, 6, 16, 25, "setSelectedFilter"], [27, 23, 16, 42], [27, 26, 16, 42, "_useState4"], [27, 36, 16, 42], [28, 4, 18, 2], [28, 8, 18, 8, "filters"], [28, 15, 18, 15], [28, 18, 18, 18], [28, 19, 18, 19], [28, 24, 18, 24], [28, 26, 18, 26], [28, 34, 18, 34], [28, 36, 18, 36], [28, 47, 18, 47], [28, 49, 18, 49], [28, 60, 18, 60], [28, 61, 18, 61], [29, 4, 20, 2], [29, 8, 20, 8, "orders"], [29, 14, 20, 14], [29, 17, 20, 17], [29, 18, 21, 4], [30, 6, 22, 6, "id"], [30, 8, 22, 8], [30, 10, 22, 10], [30, 13, 22, 13], [31, 6, 23, 6, "orderNumber"], [31, 17, 23, 17], [31, 19, 23, 19], [31, 34, 23, 34], [32, 6, 24, 6, "restaurant"], [32, 16, 24, 16], [32, 18, 24, 18], [32, 33, 24, 33], [33, 6, 25, 6, "items"], [33, 11, 25, 11], [33, 13, 25, 13], [33, 14, 25, 14], [33, 33, 25, 33], [33, 35, 25, 35], [33, 45, 25, 45], [33, 47, 25, 47], [33, 56, 25, 56], [33, 57, 25, 57], [34, 6, 26, 6, "total"], [34, 11, 26, 11], [34, 13, 26, 13], [34, 18, 26, 18], [35, 6, 27, 6, "status"], [35, 12, 27, 12], [35, 14, 27, 14], [35, 22, 27, 22], [36, 6, 28, 6, "orderTime"], [36, 15, 28, 15], [36, 17, 28, 17], [36, 35, 28, 35], [37, 6, 29, 6, "estimatedDelivery"], [37, 23, 29, 23], [37, 25, 29, 25], [37, 36, 29, 36], [38, 6, 30, 6, "image"], [38, 11, 30, 11], [38, 13, 30, 13], [39, 4, 31, 4], [39, 5, 31, 5], [39, 7, 32, 4], [40, 6, 33, 6, "id"], [40, 8, 33, 8], [40, 10, 33, 10], [40, 13, 33, 13], [41, 6, 34, 6, "orderNumber"], [41, 17, 34, 17], [41, 19, 34, 19], [41, 34, 34, 34], [42, 6, 35, 6, "restaurant"], [42, 16, 35, 16], [42, 18, 35, 18], [42, 32, 35, 32], [43, 6, 36, 6, "items"], [43, 11, 36, 11], [43, 13, 36, 13], [43, 14, 36, 14], [43, 35, 36, 35], [43, 37, 36, 37], [43, 54, 36, 54], [43, 55, 36, 55], [44, 6, 37, 6, "total"], [44, 11, 37, 11], [44, 13, 37, 13], [44, 18, 37, 18], [45, 6, 38, 6, "status"], [45, 12, 38, 12], [45, 14, 38, 14], [45, 25, 38, 25], [46, 6, 39, 6, "orderTime"], [46, 15, 39, 15], [46, 17, 39, 17], [46, 35, 39, 35], [47, 6, 40, 6, "deliveredTime"], [47, 19, 40, 19], [47, 21, 40, 21], [47, 39, 40, 39], [48, 6, 41, 6, "image"], [48, 11, 41, 11], [48, 13, 41, 13], [49, 4, 42, 4], [49, 5, 42, 5], [49, 7, 43, 4], [50, 6, 44, 6, "id"], [50, 8, 44, 8], [50, 10, 44, 10], [50, 13, 44, 13], [51, 6, 45, 6, "orderNumber"], [51, 17, 45, 17], [51, 19, 45, 19], [51, 34, 45, 34], [52, 6, 46, 6, "restaurant"], [52, 16, 46, 16], [52, 18, 46, 18], [52, 33, 46, 33], [53, 6, 47, 6, "items"], [53, 11, 47, 11], [53, 13, 47, 13], [53, 14, 47, 14], [53, 30, 47, 30], [53, 32, 47, 32], [53, 49, 47, 49], [53, 51, 47, 51], [53, 65, 47, 65], [53, 66, 47, 66], [54, 6, 48, 6, "total"], [54, 11, 48, 11], [54, 13, 48, 13], [54, 18, 48, 18], [55, 6, 49, 6, "status"], [55, 12, 49, 12], [55, 14, 49, 14], [55, 25, 49, 25], [56, 6, 50, 6, "orderTime"], [56, 15, 50, 15], [56, 17, 50, 17], [56, 35, 50, 35], [57, 6, 51, 6, "deliveredTime"], [57, 19, 51, 19], [57, 21, 51, 21], [57, 39, 51, 39], [58, 6, 52, 6, "image"], [58, 11, 52, 11], [58, 13, 52, 13], [59, 4, 53, 4], [59, 5, 53, 5], [59, 7, 54, 4], [60, 6, 55, 6, "id"], [60, 8, 55, 8], [60, 10, 55, 10], [60, 13, 55, 13], [61, 6, 56, 6, "orderNumber"], [61, 17, 56, 17], [61, 19, 56, 19], [61, 34, 56, 34], [62, 6, 57, 6, "restaurant"], [62, 16, 57, 16], [62, 18, 57, 18], [62, 30, 57, 30], [63, 6, 58, 6, "items"], [63, 11, 58, 11], [63, 13, 58, 13], [63, 14, 58, 14], [63, 31, 58, 31], [63, 33, 58, 33], [63, 46, 58, 46], [63, 47, 58, 47], [64, 6, 59, 6, "total"], [64, 11, 59, 11], [64, 13, 59, 13], [64, 18, 59, 18], [65, 6, 60, 6, "status"], [65, 12, 60, 12], [65, 14, 60, 14], [65, 25, 60, 25], [66, 6, 61, 6, "orderTime"], [66, 15, 61, 15], [66, 17, 61, 17], [66, 35, 61, 35], [67, 6, 62, 6, "cancelledTime"], [67, 19, 62, 19], [67, 21, 62, 21], [67, 39, 62, 39], [68, 6, 63, 6, "image"], [68, 11, 63, 11], [68, 13, 63, 13], [69, 4, 64, 4], [69, 5, 64, 5], [69, 6, 65, 3], [70, 4, 67, 2], [70, 8, 67, 8, "filteredOrders"], [70, 22, 67, 22], [70, 25, 67, 25, "orders"], [70, 31, 67, 31], [70, 32, 67, 32, "filter"], [70, 38, 67, 38], [70, 39, 67, 39, "order"], [70, 44, 67, 44], [70, 48, 67, 48], [71, 6, 68, 4], [71, 10, 68, 10, "matchesSearch"], [71, 23, 68, 23], [71, 26, 68, 26, "order"], [71, 31, 68, 31], [71, 32, 68, 32, "restaurant"], [71, 42, 68, 42], [71, 43, 68, 43, "toLowerCase"], [71, 54, 68, 54], [71, 55, 68, 55], [71, 56, 68, 56], [71, 57, 68, 57, "includes"], [71, 65, 68, 65], [71, 66, 68, 66, "searchQuery"], [71, 77, 68, 77], [71, 78, 68, 78, "toLowerCase"], [71, 89, 68, 89], [71, 90, 68, 90], [71, 91, 68, 91], [71, 92, 68, 92], [71, 96, 69, 25, "order"], [71, 101, 69, 30], [71, 102, 69, 31, "orderNumber"], [71, 113, 69, 42], [71, 114, 69, 43, "toLowerCase"], [71, 125, 69, 54], [71, 126, 69, 55], [71, 127, 69, 56], [71, 128, 69, 57, "includes"], [71, 136, 69, 65], [71, 137, 69, 66, "searchQuery"], [71, 148, 69, 77], [71, 149, 69, 78, "toLowerCase"], [71, 160, 69, 89], [71, 161, 69, 90], [71, 162, 69, 91], [71, 163, 69, 92], [72, 6, 70, 4], [72, 10, 70, 10, "matchesFilter"], [72, 23, 70, 23], [72, 26, 70, 26, "<PERSON><PERSON><PERSON><PERSON>"], [72, 40, 70, 40], [72, 45, 70, 45], [72, 50, 70, 50], [72, 54, 70, 54, "order"], [72, 59, 70, 59], [72, 60, 70, 60, "status"], [72, 66, 70, 66], [72, 71, 70, 71, "<PERSON><PERSON><PERSON><PERSON>"], [72, 85, 70, 85], [73, 6, 71, 4], [73, 13, 71, 11, "matchesSearch"], [73, 26, 71, 24], [73, 30, 71, 28, "matchesFilter"], [73, 43, 71, 41], [74, 4, 72, 2], [74, 5, 72, 3], [74, 6, 72, 4], [75, 4, 74, 2], [75, 24, 75, 4], [75, 28, 75, 4, "_jsxDevRuntime"], [75, 42, 75, 4], [75, 43, 75, 4, "jsxDEV"], [75, 49, 75, 4], [75, 51, 75, 5, "_reactNative"], [75, 63, 75, 5], [75, 64, 75, 5, "View"], [75, 68, 75, 9], [76, 6, 75, 10, "style"], [76, 11, 75, 15], [76, 13, 75, 17], [77, 8, 75, 19, "flex"], [77, 12, 75, 23], [77, 14, 75, 25], [77, 15, 75, 26], [78, 8, 75, 28, "backgroundColor"], [78, 23, 75, 43], [78, 25, 75, 45], [79, 6, 75, 55], [79, 7, 75, 57], [80, 6, 75, 57, "children"], [80, 14, 75, 57], [80, 30, 77, 6], [80, 34, 77, 6, "_jsxDevRuntime"], [80, 48, 77, 6], [80, 49, 77, 6, "jsxDEV"], [80, 55, 77, 6], [80, 57, 77, 7, "_reactNativeSafeAreaContext"], [80, 84, 77, 7], [80, 85, 77, 7, "SafeAreaView"], [80, 97, 77, 19], [81, 8, 77, 20, "style"], [81, 13, 77, 25], [81, 15, 77, 27], [82, 10, 77, 29, "backgroundColor"], [82, 25, 77, 44], [82, 27, 77, 46], [83, 8, 77, 56], [83, 9, 77, 58], [84, 8, 77, 59, "edges"], [84, 13, 77, 64], [84, 15, 77, 66], [84, 16, 77, 67], [84, 21, 77, 72], [85, 6, 77, 74], [86, 8, 77, 74, "fileName"], [86, 16, 77, 74], [86, 18, 77, 74, "_jsxFileName"], [86, 30, 77, 74], [87, 8, 77, 74, "lineNumber"], [87, 18, 77, 74], [88, 8, 77, 74, "columnNumber"], [88, 20, 77, 74], [89, 6, 77, 74], [89, 13, 77, 76], [89, 14, 77, 77], [89, 29, 80, 6], [89, 33, 80, 6, "_jsxDevRuntime"], [89, 47, 80, 6], [89, 48, 80, 6, "jsxDEV"], [89, 54, 80, 6], [89, 56, 80, 7, "_reactNative"], [89, 68, 80, 7], [89, 69, 80, 7, "View"], [89, 73, 80, 11], [90, 8, 80, 12, "style"], [90, 13, 80, 17], [90, 15, 80, 19], [91, 10, 80, 21, "flex"], [91, 14, 80, 25], [91, 16, 80, 27], [91, 17, 80, 28], [92, 10, 80, 30, "backgroundColor"], [92, 25, 80, 45], [92, 27, 80, 47], [93, 8, 80, 57], [93, 9, 80, 59], [94, 8, 80, 59, "children"], [94, 16, 80, 59], [94, 32, 82, 8], [94, 36, 82, 8, "_jsxDevRuntime"], [94, 50, 82, 8], [94, 51, 82, 8, "jsxDEV"], [94, 57, 82, 8], [94, 59, 82, 9, "_reactNative"], [94, 71, 82, 9], [94, 72, 82, 9, "View"], [94, 76, 82, 13], [95, 10, 82, 14, "style"], [95, 15, 82, 19], [95, 17, 82, 21], [96, 12, 83, 10, "flexDirection"], [96, 25, 83, 23], [96, 27, 83, 25], [96, 32, 83, 30], [97, 12, 84, 10, "alignItems"], [97, 22, 84, 20], [97, 24, 84, 22], [97, 32, 84, 30], [98, 12, 85, 10, "paddingHorizontal"], [98, 29, 85, 27], [98, 31, 85, 29], [98, 33, 85, 31], [99, 12, 86, 10, "paddingVertical"], [99, 27, 86, 25], [99, 29, 86, 27], [99, 31, 86, 29], [100, 12, 87, 10, "backgroundColor"], [100, 27, 87, 25], [100, 29, 87, 27], [100, 35, 87, 33], [101, 12, 88, 10, "borderBottomWidth"], [101, 29, 88, 27], [101, 31, 88, 29], [101, 32, 88, 30], [102, 12, 89, 10, "borderBottomColor"], [102, 29, 89, 27], [102, 31, 89, 29], [103, 10, 90, 8], [103, 11, 90, 10], [104, 10, 90, 10, "children"], [104, 18, 90, 10], [104, 34, 91, 8], [104, 38, 91, 8, "_jsxDevRuntime"], [104, 52, 91, 8], [104, 53, 91, 8, "jsxDEV"], [104, 59, 91, 8], [104, 61, 91, 9, "_reactNative"], [104, 73, 91, 9], [104, 74, 91, 9, "Text"], [104, 78, 91, 13], [105, 12, 91, 14, "style"], [105, 17, 91, 19], [105, 19, 91, 21], [106, 14, 91, 23, "fontSize"], [106, 22, 91, 31], [106, 24, 91, 33], [106, 26, 91, 35], [107, 14, 91, 37, "fontWeight"], [107, 24, 91, 47], [107, 26, 91, 49], [107, 32, 91, 55], [108, 14, 91, 57, "color"], [108, 19, 91, 62], [108, 21, 91, 64], [108, 30, 91, 73], [109, 14, 91, 75, "flex"], [109, 18, 91, 79], [109, 20, 91, 81], [110, 12, 91, 83], [110, 13, 91, 85], [111, 12, 91, 85, "children"], [111, 20, 91, 85], [111, 22, 91, 86], [112, 10, 93, 8], [113, 12, 93, 8, "fileName"], [113, 20, 93, 8], [113, 22, 93, 8, "_jsxFileName"], [113, 34, 93, 8], [114, 12, 93, 8, "lineNumber"], [114, 22, 93, 8], [115, 12, 93, 8, "columnNumber"], [115, 24, 93, 8], [116, 10, 93, 8], [116, 17, 93, 14], [116, 18, 93, 15], [116, 33, 94, 8], [116, 37, 94, 8, "_jsxDevRuntime"], [116, 51, 94, 8], [116, 52, 94, 8, "jsxDEV"], [116, 58, 94, 8], [116, 60, 94, 9, "_reactNative"], [116, 72, 94, 9], [116, 73, 94, 9, "TouchableOpacity"], [116, 89, 94, 25], [117, 12, 94, 26, "style"], [117, 17, 94, 31], [117, 19, 94, 33], [118, 14, 94, 35, "padding"], [118, 21, 94, 42], [118, 23, 94, 44], [119, 12, 94, 46], [119, 13, 94, 48], [120, 12, 94, 48, "children"], [120, 20, 94, 48], [120, 35, 95, 10], [120, 39, 95, 10, "_jsxDevRuntime"], [120, 53, 95, 10], [120, 54, 95, 10, "jsxDEV"], [120, 60, 95, 10], [120, 62, 95, 11, "_vectorIcons"], [120, 74, 95, 11], [120, 75, 95, 11, "Ionicons"], [120, 83, 95, 19], [121, 14, 95, 20, "name"], [121, 18, 95, 24], [121, 20, 95, 25], [121, 36, 95, 41], [122, 14, 95, 42, "size"], [122, 18, 95, 46], [122, 20, 95, 48], [122, 22, 95, 51], [123, 14, 95, 52, "color"], [123, 19, 95, 57], [123, 21, 95, 58], [124, 12, 95, 67], [125, 14, 95, 67, "fileName"], [125, 22, 95, 67], [125, 24, 95, 67, "_jsxFileName"], [125, 36, 95, 67], [126, 14, 95, 67, "lineNumber"], [126, 24, 95, 67], [127, 14, 95, 67, "columnNumber"], [127, 26, 95, 67], [128, 12, 95, 67], [128, 19, 95, 69], [129, 10, 95, 70], [130, 12, 95, 70, "fileName"], [130, 20, 95, 70], [130, 22, 95, 70, "_jsxFileName"], [130, 34, 95, 70], [131, 12, 95, 70, "lineNumber"], [131, 22, 95, 70], [132, 12, 95, 70, "columnNumber"], [132, 24, 95, 70], [133, 10, 95, 70], [133, 17, 96, 26], [133, 18, 96, 27], [134, 8, 96, 27], [135, 10, 96, 27, "fileName"], [135, 18, 96, 27], [135, 20, 96, 27, "_jsxFileName"], [135, 32, 96, 27], [136, 10, 96, 27, "lineNumber"], [136, 20, 96, 27], [137, 10, 96, 27, "columnNumber"], [137, 22, 96, 27], [138, 8, 96, 27], [138, 15, 97, 12], [138, 16, 97, 13], [138, 31, 99, 6], [138, 35, 99, 6, "_jsxDevRuntime"], [138, 49, 99, 6], [138, 50, 99, 6, "jsxDEV"], [138, 56, 99, 6], [138, 58, 99, 7, "_reactNative"], [138, 70, 99, 7], [138, 71, 99, 7, "ScrollView"], [138, 81, 99, 17], [139, 10, 99, 18, "style"], [139, 15, 99, 23], [139, 17, 99, 25], [140, 12, 99, 27, "flex"], [140, 16, 99, 31], [140, 18, 99, 33], [141, 10, 99, 35], [141, 11, 99, 37], [142, 10, 99, 37, "children"], [142, 18, 99, 37], [142, 34, 101, 8], [142, 38, 101, 8, "_jsxDevRuntime"], [142, 52, 101, 8], [142, 53, 101, 8, "jsxDEV"], [142, 59, 101, 8], [142, 61, 101, 9, "_reactNative"], [142, 73, 101, 9], [142, 74, 101, 9, "View"], [142, 78, 101, 13], [143, 12, 101, 14, "style"], [143, 17, 101, 19], [143, 19, 101, 21], [144, 14, 101, 23, "padding"], [144, 21, 101, 30], [144, 23, 101, 32], [145, 12, 101, 35], [145, 13, 101, 37], [146, 12, 101, 37, "children"], [146, 20, 101, 37], [146, 35, 102, 10], [146, 39, 102, 10, "_jsxDevRuntime"], [146, 53, 102, 10], [146, 54, 102, 10, "jsxDEV"], [146, 60, 102, 10], [146, 62, 102, 11, "_reactNative"], [146, 74, 102, 11], [146, 75, 102, 11, "View"], [146, 79, 102, 15], [147, 14, 102, 16, "style"], [147, 19, 102, 21], [147, 21, 102, 23], [148, 16, 103, 12, "flexDirection"], [148, 29, 103, 25], [148, 31, 103, 27], [148, 36, 103, 32], [149, 16, 104, 12, "alignItems"], [149, 26, 104, 22], [149, 28, 104, 24], [149, 36, 104, 32], [150, 16, 105, 12, "backgroundColor"], [150, 31, 105, 27], [150, 33, 105, 29], [150, 39, 105, 35], [151, 16, 106, 12, "borderRadius"], [151, 28, 106, 24], [151, 30, 106, 26], [151, 32, 106, 28], [152, 16, 107, 12, "paddingHorizontal"], [152, 33, 107, 29], [152, 35, 107, 31], [152, 37, 107, 33], [153, 16, 108, 12, "paddingVertical"], [153, 31, 108, 27], [153, 33, 108, 29], [153, 35, 108, 31], [154, 16, 109, 12, "shadowColor"], [154, 27, 109, 23], [154, 29, 109, 25], [154, 35, 109, 31], [155, 16, 110, 12, "shadowOffset"], [155, 28, 110, 24], [155, 30, 110, 26], [156, 18, 110, 28, "width"], [156, 23, 110, 33], [156, 25, 110, 35], [156, 26, 110, 36], [157, 18, 110, 38, "height"], [157, 24, 110, 44], [157, 26, 110, 46], [158, 16, 110, 48], [158, 17, 110, 49], [159, 16, 111, 12, "shadowOpacity"], [159, 29, 111, 25], [159, 31, 111, 27], [159, 35, 111, 31], [160, 16, 112, 12, "shadowRadius"], [160, 28, 112, 24], [160, 30, 112, 26], [160, 31, 112, 27], [161, 16, 113, 12, "elevation"], [161, 25, 113, 21], [161, 27, 113, 23], [162, 14, 114, 10], [162, 15, 114, 12], [163, 14, 114, 12, "children"], [163, 22, 114, 12], [163, 38, 115, 12], [163, 42, 115, 12, "_jsxDevRuntime"], [163, 56, 115, 12], [163, 57, 115, 12, "jsxDEV"], [163, 63, 115, 12], [163, 65, 115, 13, "_vectorIcons"], [163, 77, 115, 13], [163, 78, 115, 13, "Ionicons"], [163, 86, 115, 21], [164, 16, 115, 22, "name"], [164, 20, 115, 26], [164, 22, 115, 27], [164, 38, 115, 43], [165, 16, 115, 44, "size"], [165, 20, 115, 48], [165, 22, 115, 50], [165, 24, 115, 53], [166, 16, 115, 54, "color"], [166, 21, 115, 59], [166, 23, 115, 60], [167, 14, 115, 69], [168, 16, 115, 69, "fileName"], [168, 24, 115, 69], [168, 26, 115, 69, "_jsxFileName"], [168, 38, 115, 69], [169, 16, 115, 69, "lineNumber"], [169, 26, 115, 69], [170, 16, 115, 69, "columnNumber"], [170, 28, 115, 69], [171, 14, 115, 69], [171, 21, 115, 71], [171, 22, 115, 72], [171, 37, 116, 12], [171, 41, 116, 12, "_jsxDevRuntime"], [171, 55, 116, 12], [171, 56, 116, 12, "jsxDEV"], [171, 62, 116, 12], [171, 64, 116, 13, "_reactNative"], [171, 76, 116, 13], [171, 77, 116, 13, "TextInput"], [171, 86, 116, 22], [172, 16, 117, 14, "style"], [172, 21, 117, 19], [172, 23, 117, 21], [173, 18, 117, 23, "flex"], [173, 22, 117, 27], [173, 24, 117, 29], [173, 25, 117, 30], [174, 18, 117, 32, "marginLeft"], [174, 28, 117, 42], [174, 30, 117, 44], [174, 32, 117, 46], [175, 18, 117, 48, "fontSize"], [175, 26, 117, 56], [175, 28, 117, 58], [175, 30, 117, 60], [176, 18, 117, 62, "color"], [176, 23, 117, 67], [176, 25, 117, 69], [177, 16, 117, 79], [177, 17, 117, 81], [178, 16, 118, 14, "placeholder"], [178, 27, 118, 25], [178, 29, 118, 26], [178, 47, 118, 44], [179, 16, 119, 14, "placeholderTextColor"], [179, 36, 119, 34], [179, 38, 119, 35], [179, 47, 119, 44], [180, 16, 120, 14, "value"], [180, 21, 120, 19], [180, 23, 120, 21, "searchQuery"], [180, 34, 120, 33], [181, 16, 121, 14, "onChangeText"], [181, 28, 121, 26], [181, 30, 121, 28, "setSearch<PERSON>uery"], [182, 14, 121, 43], [183, 16, 121, 43, "fileName"], [183, 24, 121, 43], [183, 26, 121, 43, "_jsxFileName"], [183, 38, 121, 43], [184, 16, 121, 43, "lineNumber"], [184, 26, 121, 43], [185, 16, 121, 43, "columnNumber"], [185, 28, 121, 43], [186, 14, 121, 43], [186, 21, 122, 13], [186, 22, 122, 14], [187, 12, 122, 14], [188, 14, 122, 14, "fileName"], [188, 22, 122, 14], [188, 24, 122, 14, "_jsxFileName"], [188, 36, 122, 14], [189, 14, 122, 14, "lineNumber"], [189, 24, 122, 14], [190, 14, 122, 14, "columnNumber"], [190, 26, 122, 14], [191, 12, 122, 14], [191, 19, 123, 16], [192, 10, 123, 17], [193, 12, 123, 17, "fileName"], [193, 20, 123, 17], [193, 22, 123, 17, "_jsxFileName"], [193, 34, 123, 17], [194, 12, 123, 17, "lineNumber"], [194, 22, 123, 17], [195, 12, 123, 17, "columnNumber"], [195, 24, 123, 17], [196, 10, 123, 17], [196, 17, 124, 14], [196, 18, 124, 15], [196, 33, 127, 8], [196, 37, 127, 8, "_jsxDevRuntime"], [196, 51, 127, 8], [196, 52, 127, 8, "jsxDEV"], [196, 58, 127, 8], [196, 60, 127, 9, "_reactNative"], [196, 72, 127, 9], [196, 73, 127, 9, "ScrollView"], [196, 83, 127, 19], [197, 12, 128, 10, "horizontal"], [197, 22, 128, 20], [198, 12, 129, 10, "showsHorizontalScrollIndicator"], [198, 42, 129, 40], [198, 44, 129, 42], [198, 49, 129, 48], [199, 12, 130, 10, "style"], [199, 17, 130, 15], [199, 19, 130, 17], [200, 14, 130, 19, "paddingLeft"], [200, 25, 130, 30], [200, 27, 130, 32], [200, 29, 130, 34], [201, 14, 130, 36, "marginBottom"], [201, 26, 130, 48], [201, 28, 130, 50], [202, 12, 130, 53], [202, 13, 130, 55], [203, 12, 130, 55, "children"], [203, 20, 130, 55], [203, 22, 132, 11, "filters"], [203, 29, 132, 18], [203, 30, 132, 19, "map"], [203, 33, 132, 22], [203, 34, 132, 24, "filter"], [203, 40, 132, 30], [203, 57, 133, 12], [203, 61, 133, 12, "_jsxDevRuntime"], [203, 75, 133, 12], [203, 76, 133, 12, "jsxDEV"], [203, 82, 133, 12], [203, 84, 133, 13, "_reactNative"], [203, 96, 133, 13], [203, 97, 133, 13, "TouchableOpacity"], [203, 113, 133, 29], [204, 14, 135, 14, "onPress"], [204, 21, 135, 21], [204, 23, 135, 23, "onPress"], [204, 24, 135, 23], [204, 29, 135, 29, "setSelectedFilter"], [204, 46, 135, 46], [204, 47, 135, 47, "filter"], [204, 53, 135, 53], [204, 54, 135, 55], [205, 14, 136, 14, "style"], [205, 19, 136, 19], [205, 21, 136, 21], [206, 16, 137, 16, "backgroundColor"], [206, 31, 137, 31], [206, 33, 137, 33, "<PERSON><PERSON><PERSON><PERSON>"], [206, 47, 137, 47], [206, 52, 137, 52, "filter"], [206, 58, 137, 58], [206, 61, 137, 61], [206, 70, 137, 70], [206, 73, 137, 73], [206, 79, 137, 79], [207, 16, 138, 16, "paddingHorizontal"], [207, 33, 138, 33], [207, 35, 138, 35], [207, 37, 138, 37], [208, 16, 139, 16, "paddingVertical"], [208, 31, 139, 31], [208, 33, 139, 33], [208, 35, 139, 35], [209, 16, 140, 16, "borderRadius"], [209, 28, 140, 28], [209, 30, 140, 30], [209, 32, 140, 32], [210, 16, 141, 16, "marginRight"], [210, 27, 141, 27], [210, 29, 141, 29], [210, 31, 141, 31], [211, 16, 142, 16, "borderWidth"], [211, 27, 142, 27], [211, 29, 142, 29], [211, 30, 142, 30], [212, 16, 143, 16, "borderColor"], [212, 27, 143, 27], [212, 29, 143, 29, "<PERSON><PERSON><PERSON><PERSON>"], [212, 43, 143, 43], [212, 48, 143, 48, "filter"], [212, 54, 143, 54], [212, 57, 143, 57], [212, 66, 143, 66], [212, 69, 143, 69], [213, 14, 144, 14], [213, 15, 144, 16], [214, 14, 144, 16, "children"], [214, 22, 144, 16], [214, 37, 146, 14], [214, 41, 146, 14, "_jsxDevRuntime"], [214, 55, 146, 14], [214, 56, 146, 14, "jsxDEV"], [214, 62, 146, 14], [214, 64, 146, 15, "_reactNative"], [214, 76, 146, 15], [214, 77, 146, 15, "Text"], [214, 81, 146, 19], [215, 16, 146, 20, "style"], [215, 21, 146, 25], [215, 23, 146, 27], [216, 18, 147, 16, "color"], [216, 23, 147, 21], [216, 25, 147, 23, "<PERSON><PERSON><PERSON><PERSON>"], [216, 39, 147, 37], [216, 44, 147, 42, "filter"], [216, 50, 147, 48], [216, 53, 147, 51], [216, 59, 147, 57], [216, 62, 147, 60], [216, 71, 147, 69], [217, 18, 148, 16, "fontWeight"], [217, 28, 148, 26], [217, 30, 148, 28], [217, 35, 148, 33], [218, 18, 149, 16, "fontSize"], [218, 26, 149, 24], [218, 28, 149, 26], [219, 16, 150, 14], [219, 17, 150, 16], [220, 16, 150, 16, "children"], [220, 24, 150, 16], [220, 26, 151, 17, "filter"], [221, 14, 151, 23], [222, 16, 151, 23, "fileName"], [222, 24, 151, 23], [222, 26, 151, 23, "_jsxFileName"], [222, 38, 151, 23], [223, 16, 151, 23, "lineNumber"], [223, 26, 151, 23], [224, 16, 151, 23, "columnNumber"], [224, 28, 151, 23], [225, 14, 151, 23], [225, 21, 152, 20], [226, 12, 152, 21], [226, 15, 134, 19, "filter"], [226, 21, 134, 25], [227, 14, 134, 25, "fileName"], [227, 22, 134, 25], [227, 24, 134, 25, "_jsxFileName"], [227, 36, 134, 25], [228, 14, 134, 25, "lineNumber"], [228, 24, 134, 25], [229, 14, 134, 25, "columnNumber"], [229, 26, 134, 25], [230, 12, 134, 25], [230, 19, 153, 30], [230, 20, 154, 11], [231, 10, 154, 12], [232, 12, 154, 12, "fileName"], [232, 20, 154, 12], [232, 22, 154, 12, "_jsxFileName"], [232, 34, 154, 12], [233, 12, 154, 12, "lineNumber"], [233, 22, 154, 12], [234, 12, 154, 12, "columnNumber"], [234, 24, 154, 12], [235, 10, 154, 12], [235, 17, 155, 20], [235, 18, 155, 21], [235, 33, 158, 8], [235, 37, 158, 8, "_jsxDevRuntime"], [235, 51, 158, 8], [235, 52, 158, 8, "jsxDEV"], [235, 58, 158, 8], [235, 60, 158, 9, "_reactNative"], [235, 72, 158, 9], [235, 73, 158, 9, "View"], [235, 77, 158, 13], [236, 12, 158, 14, "style"], [236, 17, 158, 19], [236, 19, 158, 21], [237, 14, 158, 23, "paddingHorizontal"], [237, 31, 158, 40], [237, 33, 158, 42], [238, 12, 158, 45], [238, 13, 158, 47], [239, 12, 158, 47, "children"], [239, 20, 158, 47], [239, 22, 159, 11, "filteredOrders"], [239, 36, 159, 25], [239, 37, 159, 26, "map"], [239, 40, 159, 29], [239, 41, 159, 31, "order"], [239, 46, 159, 36], [239, 63, 160, 12], [239, 67, 160, 12, "_jsxDevRuntime"], [239, 81, 160, 12], [239, 82, 160, 12, "jsxDEV"], [239, 88, 160, 12], [239, 90, 160, 13, "_reactNative"], [239, 102, 160, 13], [239, 103, 160, 13, "TouchableOpacity"], [239, 119, 160, 29], [240, 14, 162, 14, "style"], [240, 19, 162, 19], [240, 21, 162, 21], [241, 16, 163, 16, "backgroundColor"], [241, 31, 163, 31], [241, 33, 163, 33], [241, 39, 163, 39], [242, 16, 164, 16, "borderRadius"], [242, 28, 164, 28], [242, 30, 164, 30], [242, 32, 164, 32], [243, 16, 165, 16, "marginBottom"], [243, 28, 165, 28], [243, 30, 165, 30], [243, 32, 165, 32], [244, 16, 166, 16, "shadowColor"], [244, 27, 166, 27], [244, 29, 166, 29], [244, 35, 166, 35], [245, 16, 167, 16, "shadowOffset"], [245, 28, 167, 28], [245, 30, 167, 30], [246, 18, 167, 32, "width"], [246, 23, 167, 37], [246, 25, 167, 39], [246, 26, 167, 40], [247, 18, 167, 42, "height"], [247, 24, 167, 48], [247, 26, 167, 50], [248, 16, 167, 52], [248, 17, 167, 53], [249, 16, 168, 16, "shadowOpacity"], [249, 29, 168, 29], [249, 31, 168, 31], [249, 34, 168, 34], [250, 16, 169, 16, "shadowRadius"], [250, 28, 169, 28], [250, 30, 169, 30], [250, 31, 169, 31], [251, 16, 170, 16, "elevation"], [251, 25, 170, 25], [251, 27, 170, 27], [252, 14, 171, 14], [252, 15, 171, 16], [253, 14, 171, 16, "children"], [253, 22, 171, 16], [253, 37, 173, 14], [253, 41, 173, 14, "_jsxDevRuntime"], [253, 55, 173, 14], [253, 56, 173, 14, "jsxDEV"], [253, 62, 173, 14], [253, 64, 173, 15, "_reactNative"], [253, 76, 173, 15], [253, 77, 173, 15, "View"], [253, 81, 173, 19], [254, 16, 173, 20, "style"], [254, 21, 173, 25], [254, 23, 173, 27], [255, 18, 173, 29, "padding"], [255, 25, 173, 36], [255, 27, 173, 38], [256, 16, 173, 41], [256, 17, 173, 43], [257, 16, 173, 43, "children"], [257, 24, 173, 43], [257, 40, 174, 16], [257, 44, 174, 16, "_jsxDevRuntime"], [257, 58, 174, 16], [257, 59, 174, 16, "jsxDEV"], [257, 65, 174, 16], [257, 67, 174, 17, "_reactNative"], [257, 79, 174, 17], [257, 80, 174, 17, "View"], [257, 84, 174, 21], [258, 18, 174, 22, "style"], [258, 23, 174, 27], [258, 25, 174, 29], [259, 20, 174, 31, "flexDirection"], [259, 33, 174, 44], [259, 35, 174, 46], [259, 40, 174, 51], [260, 20, 174, 53, "justifyContent"], [260, 34, 174, 67], [260, 36, 174, 69], [260, 51, 174, 84], [261, 20, 174, 86, "alignItems"], [261, 30, 174, 96], [261, 32, 174, 98], [261, 44, 174, 110], [262, 20, 174, 112, "marginBottom"], [262, 32, 174, 124], [262, 34, 174, 126], [263, 18, 174, 129], [263, 19, 174, 131], [264, 18, 174, 131, "children"], [264, 26, 174, 131], [264, 42, 175, 18], [264, 46, 175, 18, "_jsxDevRuntime"], [264, 60, 175, 18], [264, 61, 175, 18, "jsxDEV"], [264, 67, 175, 18], [264, 69, 175, 19, "_reactNative"], [264, 81, 175, 19], [264, 82, 175, 19, "View"], [264, 86, 175, 23], [265, 20, 175, 24, "style"], [265, 25, 175, 29], [265, 27, 175, 31], [266, 22, 175, 33, "flex"], [266, 26, 175, 37], [266, 28, 175, 39], [267, 20, 175, 41], [267, 21, 175, 43], [268, 20, 175, 43, "children"], [268, 28, 175, 43], [268, 44, 176, 20], [268, 48, 176, 20, "_jsxDevRuntime"], [268, 62, 176, 20], [268, 63, 176, 20, "jsxDEV"], [268, 69, 176, 20], [268, 71, 176, 21, "_reactNative"], [268, 83, 176, 21], [268, 84, 176, 21, "Text"], [268, 88, 176, 25], [269, 22, 176, 26, "style"], [269, 27, 176, 31], [269, 29, 176, 33], [270, 24, 176, 35, "fontSize"], [270, 32, 176, 43], [270, 34, 176, 45], [270, 36, 176, 47], [271, 24, 176, 49, "fontWeight"], [271, 34, 176, 59], [271, 36, 176, 61], [271, 42, 176, 67], [272, 24, 176, 69, "color"], [272, 29, 176, 74], [272, 31, 176, 76], [272, 40, 176, 85], [273, 24, 176, 87, "marginBottom"], [273, 36, 176, 99], [273, 38, 176, 101], [274, 22, 176, 103], [274, 23, 176, 105], [275, 22, 176, 105, "children"], [275, 30, 176, 105], [275, 32, 177, 23, "order"], [275, 37, 177, 28], [275, 38, 177, 29, "orderNumber"], [276, 20, 177, 40], [277, 22, 177, 40, "fileName"], [277, 30, 177, 40], [277, 32, 177, 40, "_jsxFileName"], [277, 44, 177, 40], [278, 22, 177, 40, "lineNumber"], [278, 32, 177, 40], [279, 22, 177, 40, "columnNumber"], [279, 34, 177, 40], [280, 20, 177, 40], [280, 27, 178, 26], [280, 28, 178, 27], [280, 43, 179, 20], [280, 47, 179, 20, "_jsxDevRuntime"], [280, 61, 179, 20], [280, 62, 179, 20, "jsxDEV"], [280, 68, 179, 20], [280, 70, 179, 21, "_reactNative"], [280, 82, 179, 21], [280, 83, 179, 21, "Text"], [280, 87, 179, 25], [281, 22, 179, 26, "style"], [281, 27, 179, 31], [281, 29, 179, 33], [282, 24, 179, 35, "fontSize"], [282, 32, 179, 43], [282, 34, 179, 45], [282, 36, 179, 47], [283, 24, 179, 49, "color"], [283, 29, 179, 54], [283, 31, 179, 56], [283, 40, 179, 65], [284, 24, 179, 67, "marginBottom"], [284, 36, 179, 79], [284, 38, 179, 81], [285, 22, 179, 83], [285, 23, 179, 85], [286, 22, 179, 85, "children"], [286, 30, 179, 85], [286, 33, 179, 86], [286, 40, 180, 27], [286, 42, 180, 28, "order"], [286, 47, 180, 33], [286, 48, 180, 34, "restaurant"], [286, 58, 180, 44], [287, 20, 180, 44], [288, 22, 180, 44, "fileName"], [288, 30, 180, 44], [288, 32, 180, 44, "_jsxFileName"], [288, 44, 180, 44], [289, 22, 180, 44, "lineNumber"], [289, 32, 180, 44], [290, 22, 180, 44, "columnNumber"], [290, 34, 180, 44], [291, 20, 180, 44], [291, 27, 181, 26], [291, 28, 181, 27], [291, 43, 182, 20], [291, 47, 182, 20, "_jsxDevRuntime"], [291, 61, 182, 20], [291, 62, 182, 20, "jsxDEV"], [291, 68, 182, 20], [291, 70, 182, 21, "_reactNative"], [291, 82, 182, 21], [291, 83, 182, 21, "Text"], [291, 87, 182, 25], [292, 22, 182, 26, "style"], [292, 27, 182, 31], [292, 29, 182, 33], [293, 24, 182, 35, "fontSize"], [293, 32, 182, 43], [293, 34, 182, 45], [293, 36, 182, 47], [294, 24, 182, 49, "color"], [294, 29, 182, 54], [294, 31, 182, 56], [295, 22, 182, 66], [295, 23, 182, 68], [296, 22, 182, 68, "children"], [296, 30, 182, 68], [296, 32, 183, 23, "order"], [296, 37, 183, 28], [296, 38, 183, 29, "orderTime"], [297, 20, 183, 38], [298, 22, 183, 38, "fileName"], [298, 30, 183, 38], [298, 32, 183, 38, "_jsxFileName"], [298, 44, 183, 38], [299, 22, 183, 38, "lineNumber"], [299, 32, 183, 38], [300, 22, 183, 38, "columnNumber"], [300, 34, 183, 38], [301, 20, 183, 38], [301, 27, 184, 26], [301, 28, 184, 27], [302, 18, 184, 27], [303, 20, 184, 27, "fileName"], [303, 28, 184, 27], [303, 30, 184, 27, "_jsxFileName"], [303, 42, 184, 27], [304, 20, 184, 27, "lineNumber"], [304, 30, 184, 27], [305, 20, 184, 27, "columnNumber"], [305, 32, 184, 27], [306, 18, 184, 27], [306, 25, 185, 24], [306, 26, 185, 25], [306, 41, 186, 18], [306, 45, 186, 18, "_jsxDevRuntime"], [306, 59, 186, 18], [306, 60, 186, 18, "jsxDEV"], [306, 66, 186, 18], [306, 68, 186, 19, "_reactNative"], [306, 80, 186, 19], [306, 81, 186, 19, "View"], [306, 85, 186, 23], [307, 20, 186, 24, "style"], [307, 25, 186, 29], [307, 27, 186, 31], [308, 22, 187, 20, "backgroundColor"], [308, 37, 187, 35], [308, 39, 188, 22, "order"], [308, 44, 188, 27], [308, 45, 188, 28, "status"], [308, 51, 188, 34], [308, 56, 188, 39], [308, 64, 188, 47], [308, 67, 188, 50], [308, 76, 188, 59], [308, 79, 189, 22, "order"], [308, 84, 189, 27], [308, 85, 189, 28, "status"], [308, 91, 189, 34], [308, 96, 189, 39], [308, 107, 189, 50], [308, 110, 189, 53], [308, 119, 189, 62], [308, 122, 189, 65], [308, 131, 189, 74], [309, 22, 190, 20, "paddingHorizontal"], [309, 39, 190, 37], [309, 41, 190, 39], [309, 43, 190, 41], [310, 22, 191, 20, "paddingVertical"], [310, 37, 191, 35], [310, 39, 191, 37], [310, 40, 191, 38], [311, 22, 192, 20, "borderRadius"], [311, 34, 192, 32], [311, 36, 192, 34], [312, 20, 193, 18], [312, 21, 193, 20], [313, 20, 193, 20, "children"], [313, 28, 193, 20], [313, 43, 194, 20], [313, 47, 194, 20, "_jsxDevRuntime"], [313, 61, 194, 20], [313, 62, 194, 20, "jsxDEV"], [313, 68, 194, 20], [313, 70, 194, 21, "_reactNative"], [313, 82, 194, 21], [313, 83, 194, 21, "Text"], [313, 87, 194, 25], [314, 22, 194, 26, "style"], [314, 27, 194, 31], [314, 29, 194, 33], [315, 24, 194, 35, "color"], [315, 29, 194, 40], [315, 31, 194, 42], [315, 37, 194, 48], [316, 24, 194, 50, "fontSize"], [316, 32, 194, 58], [316, 34, 194, 60], [316, 36, 194, 62], [317, 24, 194, 64, "fontWeight"], [317, 34, 194, 74], [317, 36, 194, 76], [318, 22, 194, 82], [318, 23, 194, 84], [319, 22, 194, 84, "children"], [319, 30, 194, 84], [319, 32, 195, 23, "order"], [319, 37, 195, 28], [319, 38, 195, 29, "status"], [320, 20, 195, 35], [321, 22, 195, 35, "fileName"], [321, 30, 195, 35], [321, 32, 195, 35, "_jsxFileName"], [321, 44, 195, 35], [322, 22, 195, 35, "lineNumber"], [322, 32, 195, 35], [323, 22, 195, 35, "columnNumber"], [323, 34, 195, 35], [324, 20, 195, 35], [324, 27, 196, 26], [325, 18, 196, 27], [326, 20, 196, 27, "fileName"], [326, 28, 196, 27], [326, 30, 196, 27, "_jsxFileName"], [326, 42, 196, 27], [327, 20, 196, 27, "lineNumber"], [327, 30, 196, 27], [328, 20, 196, 27, "columnNumber"], [328, 32, 196, 27], [329, 18, 196, 27], [329, 25, 197, 24], [329, 26, 197, 25], [330, 16, 197, 25], [331, 18, 197, 25, "fileName"], [331, 26, 197, 25], [331, 28, 197, 25, "_jsxFileName"], [331, 40, 197, 25], [332, 18, 197, 25, "lineNumber"], [332, 28, 197, 25], [333, 18, 197, 25, "columnNumber"], [333, 30, 197, 25], [334, 16, 197, 25], [334, 23, 198, 22], [334, 24, 198, 23], [334, 39, 200, 16], [334, 43, 200, 16, "_jsxDevRuntime"], [334, 57, 200, 16], [334, 58, 200, 16, "jsxDEV"], [334, 64, 200, 16], [334, 66, 200, 17, "_reactNative"], [334, 78, 200, 17], [334, 79, 200, 17, "View"], [334, 83, 200, 21], [335, 18, 200, 22, "style"], [335, 23, 200, 27], [335, 25, 200, 29], [336, 20, 200, 31, "flexDirection"], [336, 33, 200, 44], [336, 35, 200, 46], [336, 40, 200, 51], [337, 20, 200, 53, "alignItems"], [337, 30, 200, 63], [337, 32, 200, 65], [337, 40, 200, 73], [338, 20, 200, 75, "marginBottom"], [338, 32, 200, 87], [338, 34, 200, 89], [339, 18, 200, 92], [339, 19, 200, 94], [340, 18, 200, 94, "children"], [340, 26, 200, 94], [340, 42, 201, 18], [340, 46, 201, 18, "_jsxDevRuntime"], [340, 60, 201, 18], [340, 61, 201, 18, "jsxDEV"], [340, 67, 201, 18], [340, 69, 201, 19, "_reactNative"], [340, 81, 201, 19], [340, 82, 201, 19, "Image"], [340, 87, 201, 24], [341, 20, 202, 20, "source"], [341, 26, 202, 26], [341, 28, 202, 28], [342, 22, 202, 30, "uri"], [342, 25, 202, 33], [342, 27, 202, 35, "order"], [342, 32, 202, 40], [342, 33, 202, 41, "image"], [343, 20, 202, 47], [343, 21, 202, 49], [344, 20, 203, 20, "style"], [344, 25, 203, 25], [344, 27, 203, 27], [345, 22, 204, 22, "width"], [345, 27, 204, 27], [345, 29, 204, 29], [345, 31, 204, 31], [346, 22, 205, 22, "height"], [346, 28, 205, 28], [346, 30, 205, 30], [346, 32, 205, 32], [347, 22, 206, 22, "borderRadius"], [347, 34, 206, 34], [347, 36, 206, 36], [347, 37, 206, 37], [348, 22, 207, 22, "marginRight"], [348, 33, 207, 33], [348, 35, 207, 35], [349, 20, 208, 20], [350, 18, 208, 22], [351, 20, 208, 22, "fileName"], [351, 28, 208, 22], [351, 30, 208, 22, "_jsxFileName"], [351, 42, 208, 22], [352, 20, 208, 22, "lineNumber"], [352, 30, 208, 22], [353, 20, 208, 22, "columnNumber"], [353, 32, 208, 22], [354, 18, 208, 22], [354, 25, 209, 19], [354, 26, 209, 20], [354, 41, 210, 18], [354, 45, 210, 18, "_jsxDevRuntime"], [354, 59, 210, 18], [354, 60, 210, 18, "jsxDEV"], [354, 66, 210, 18], [354, 68, 210, 19, "_reactNative"], [354, 80, 210, 19], [354, 81, 210, 19, "View"], [354, 85, 210, 23], [355, 20, 210, 24, "style"], [355, 25, 210, 29], [355, 27, 210, 31], [356, 22, 210, 33, "flex"], [356, 26, 210, 37], [356, 28, 210, 39], [357, 20, 210, 41], [357, 21, 210, 43], [358, 20, 210, 43, "children"], [358, 28, 210, 43], [358, 30, 211, 21, "order"], [358, 35, 211, 26], [358, 36, 211, 27, "items"], [358, 41, 211, 32], [358, 42, 211, 33, "map"], [358, 45, 211, 36], [358, 46, 211, 37], [358, 47, 211, 38, "item"], [358, 51, 211, 42], [358, 53, 211, 44, "index"], [358, 58, 211, 49], [358, 76, 212, 22], [358, 80, 212, 22, "_jsxDevRuntime"], [358, 94, 212, 22], [358, 95, 212, 22, "jsxDEV"], [358, 101, 212, 22], [358, 103, 212, 23, "_reactNative"], [358, 115, 212, 23], [358, 116, 212, 23, "Text"], [358, 120, 212, 27], [359, 22, 212, 40, "style"], [359, 27, 212, 45], [359, 29, 212, 47], [360, 24, 212, 49, "fontSize"], [360, 32, 212, 57], [360, 34, 212, 59], [360, 36, 212, 61], [361, 24, 212, 63, "color"], [361, 29, 212, 68], [361, 31, 212, 70], [361, 40, 212, 79], [362, 24, 212, 81, "marginBottom"], [362, 36, 212, 93], [362, 38, 212, 95], [363, 22, 212, 97], [363, 23, 212, 99], [364, 22, 212, 99, "children"], [364, 30, 212, 99], [364, 32, 213, 25, "item"], [365, 20, 213, 29], [365, 23, 212, 33, "index"], [365, 28, 212, 38], [366, 22, 212, 38, "fileName"], [366, 30, 212, 38], [366, 32, 212, 38, "_jsxFileName"], [366, 44, 212, 38], [367, 22, 212, 38, "lineNumber"], [367, 32, 212, 38], [368, 22, 212, 38, "columnNumber"], [368, 34, 212, 38], [369, 20, 212, 38], [369, 27, 214, 28], [369, 28, 215, 21], [370, 18, 215, 22], [371, 20, 215, 22, "fileName"], [371, 28, 215, 22], [371, 30, 215, 22, "_jsxFileName"], [371, 42, 215, 22], [372, 20, 215, 22, "lineNumber"], [372, 30, 215, 22], [373, 20, 215, 22, "columnNumber"], [373, 32, 215, 22], [374, 18, 215, 22], [374, 25, 216, 24], [374, 26, 216, 25], [375, 16, 216, 25], [376, 18, 216, 25, "fileName"], [376, 26, 216, 25], [376, 28, 216, 25, "_jsxFileName"], [376, 40, 216, 25], [377, 18, 216, 25, "lineNumber"], [377, 28, 216, 25], [378, 18, 216, 25, "columnNumber"], [378, 30, 216, 25], [379, 16, 216, 25], [379, 23, 217, 22], [379, 24, 217, 23], [379, 39, 219, 16], [379, 43, 219, 16, "_jsxDevRuntime"], [379, 57, 219, 16], [379, 58, 219, 16, "jsxDEV"], [379, 64, 219, 16], [379, 66, 219, 17, "_reactNative"], [379, 78, 219, 17], [379, 79, 219, 17, "View"], [379, 83, 219, 21], [380, 18, 219, 22, "style"], [380, 23, 219, 27], [380, 25, 219, 29], [381, 20, 219, 31, "flexDirection"], [381, 33, 219, 44], [381, 35, 219, 46], [381, 40, 219, 51], [382, 20, 219, 53, "justifyContent"], [382, 34, 219, 67], [382, 36, 219, 69], [382, 51, 219, 84], [383, 20, 219, 86, "alignItems"], [383, 30, 219, 96], [383, 32, 219, 98], [384, 18, 219, 107], [384, 19, 219, 109], [385, 18, 219, 109, "children"], [385, 26, 219, 109], [385, 42, 220, 18], [385, 46, 220, 18, "_jsxDevRuntime"], [385, 60, 220, 18], [385, 61, 220, 18, "jsxDEV"], [385, 67, 220, 18], [385, 69, 220, 19, "_reactNative"], [385, 81, 220, 19], [385, 82, 220, 19, "Text"], [385, 86, 220, 23], [386, 20, 220, 24, "style"], [386, 25, 220, 29], [386, 27, 220, 31], [387, 22, 220, 33, "fontSize"], [387, 30, 220, 41], [387, 32, 220, 43], [387, 34, 220, 45], [388, 22, 220, 47, "fontWeight"], [388, 32, 220, 57], [388, 34, 220, 59], [388, 40, 220, 65], [389, 22, 220, 67, "color"], [389, 27, 220, 72], [389, 29, 220, 74], [390, 20, 220, 84], [390, 21, 220, 86], [391, 20, 220, 86, "children"], [391, 28, 220, 86], [391, 31, 220, 87], [391, 34, 221, 21], [391, 36, 221, 22, "order"], [391, 41, 221, 27], [391, 42, 221, 28, "total"], [391, 47, 221, 33], [391, 48, 221, 34, "toFixed"], [391, 55, 221, 41], [391, 56, 221, 42], [391, 57, 221, 43], [391, 58, 221, 44], [392, 18, 221, 44], [393, 20, 221, 44, "fileName"], [393, 28, 221, 44], [393, 30, 221, 44, "_jsxFileName"], [393, 42, 221, 44], [394, 20, 221, 44, "lineNumber"], [394, 30, 221, 44], [395, 20, 221, 44, "columnNumber"], [395, 32, 221, 44], [396, 18, 221, 44], [396, 25, 222, 24], [396, 26, 222, 25], [396, 28, 223, 19, "order"], [396, 33, 223, 24], [396, 34, 223, 25, "status"], [396, 40, 223, 31], [396, 45, 223, 36], [396, 53, 223, 44], [396, 70, 224, 20], [396, 74, 224, 20, "_jsxDevRuntime"], [396, 88, 224, 20], [396, 89, 224, 20, "jsxDEV"], [396, 95, 224, 20], [396, 97, 224, 21, "_reactNative"], [396, 109, 224, 21], [396, 110, 224, 21, "Text"], [396, 114, 224, 25], [397, 20, 224, 26, "style"], [397, 25, 224, 31], [397, 27, 224, 33], [398, 22, 224, 35, "fontSize"], [398, 30, 224, 43], [398, 32, 224, 45], [398, 34, 224, 47], [399, 22, 224, 49, "color"], [399, 27, 224, 54], [399, 29, 224, 56], [399, 38, 224, 65], [400, 22, 224, 67, "fontWeight"], [400, 32, 224, 77], [400, 34, 224, 79], [401, 20, 224, 85], [401, 21, 224, 87], [402, 20, 224, 87, "children"], [402, 28, 224, 87], [402, 31, 224, 88], [402, 45, 225, 34], [402, 47, 225, 35, "order"], [402, 52, 225, 40], [402, 53, 225, 41, "estimatedDelivery"], [402, 70, 225, 58], [403, 18, 225, 58], [404, 20, 225, 58, "fileName"], [404, 28, 225, 58], [404, 30, 225, 58, "_jsxFileName"], [404, 42, 225, 58], [405, 20, 225, 58, "lineNumber"], [405, 30, 225, 58], [406, 20, 225, 58, "columnNumber"], [406, 32, 225, 58], [407, 18, 225, 58], [407, 25, 226, 26], [407, 26, 227, 19], [407, 28, 228, 19, "order"], [407, 33, 228, 24], [407, 34, 228, 25, "status"], [407, 40, 228, 31], [407, 45, 228, 36], [407, 56, 228, 47], [407, 73, 229, 20], [407, 77, 229, 20, "_jsxDevRuntime"], [407, 91, 229, 20], [407, 92, 229, 20, "jsxDEV"], [407, 98, 229, 20], [407, 100, 229, 21, "_reactNative"], [407, 112, 229, 21], [407, 113, 229, 21, "TouchableOpacity"], [407, 129, 229, 37], [408, 20, 229, 38, "style"], [408, 25, 229, 43], [408, 27, 229, 45], [409, 22, 230, 22, "backgroundColor"], [409, 37, 230, 37], [409, 39, 230, 39], [409, 48, 230, 48], [410, 22, 231, 22, "paddingHorizontal"], [410, 39, 231, 39], [410, 41, 231, 41], [410, 43, 231, 43], [411, 22, 232, 22, "paddingVertical"], [411, 37, 232, 37], [411, 39, 232, 39], [411, 40, 232, 40], [412, 22, 233, 22, "borderRadius"], [412, 34, 233, 34], [412, 36, 233, 36], [413, 20, 234, 20], [413, 21, 234, 22], [414, 20, 234, 22, "children"], [414, 28, 234, 22], [414, 43, 235, 22], [414, 47, 235, 22, "_jsxDevRuntime"], [414, 61, 235, 22], [414, 62, 235, 22, "jsxDEV"], [414, 68, 235, 22], [414, 70, 235, 23, "_reactNative"], [414, 82, 235, 23], [414, 83, 235, 23, "Text"], [414, 87, 235, 27], [415, 22, 235, 28, "style"], [415, 27, 235, 33], [415, 29, 235, 35], [416, 24, 235, 37, "color"], [416, 29, 235, 42], [416, 31, 235, 44], [416, 40, 235, 53], [417, 24, 235, 55, "fontSize"], [417, 32, 235, 63], [417, 34, 235, 65], [417, 36, 235, 67], [418, 24, 235, 69, "fontWeight"], [418, 34, 235, 79], [418, 36, 235, 81], [419, 22, 235, 87], [419, 23, 235, 89], [420, 22, 235, 89, "children"], [420, 30, 235, 89], [420, 32, 235, 90], [421, 20, 237, 22], [422, 22, 237, 22, "fileName"], [422, 30, 237, 22], [422, 32, 237, 22, "_jsxFileName"], [422, 44, 237, 22], [423, 22, 237, 22, "lineNumber"], [423, 32, 237, 22], [424, 22, 237, 22, "columnNumber"], [424, 34, 237, 22], [425, 20, 237, 22], [425, 27, 237, 28], [426, 18, 237, 29], [427, 20, 237, 29, "fileName"], [427, 28, 237, 29], [427, 30, 237, 29, "_jsxFileName"], [427, 42, 237, 29], [428, 20, 237, 29, "lineNumber"], [428, 30, 237, 29], [429, 20, 237, 29, "columnNumber"], [429, 32, 237, 29], [430, 18, 237, 29], [430, 25, 238, 38], [430, 26, 239, 19], [431, 16, 239, 19], [432, 18, 239, 19, "fileName"], [432, 26, 239, 19], [432, 28, 239, 19, "_jsxFileName"], [432, 40, 239, 19], [433, 18, 239, 19, "lineNumber"], [433, 28, 239, 19], [434, 18, 239, 19, "columnNumber"], [434, 30, 239, 19], [435, 16, 239, 19], [435, 23, 240, 22], [435, 24, 240, 23], [436, 14, 240, 23], [437, 16, 240, 23, "fileName"], [437, 24, 240, 23], [437, 26, 240, 23, "_jsxFileName"], [437, 38, 240, 23], [438, 16, 240, 23, "lineNumber"], [438, 26, 240, 23], [439, 16, 240, 23, "columnNumber"], [439, 28, 240, 23], [440, 14, 240, 23], [440, 21, 241, 20], [441, 12, 241, 21], [441, 15, 161, 19, "order"], [441, 20, 161, 24], [441, 21, 161, 25, "id"], [441, 23, 161, 27], [442, 14, 161, 27, "fileName"], [442, 22, 161, 27], [442, 24, 161, 27, "_jsxFileName"], [442, 36, 161, 27], [443, 14, 161, 27, "lineNumber"], [443, 24, 161, 27], [444, 14, 161, 27, "columnNumber"], [444, 26, 161, 27], [445, 12, 161, 27], [445, 19, 242, 30], [445, 20, 243, 11], [446, 10, 243, 12], [447, 12, 243, 12, "fileName"], [447, 20, 243, 12], [447, 22, 243, 12, "_jsxFileName"], [447, 34, 243, 12], [448, 12, 243, 12, "lineNumber"], [448, 22, 243, 12], [449, 12, 243, 12, "columnNumber"], [449, 24, 243, 12], [450, 10, 243, 12], [450, 17, 244, 14], [450, 18, 244, 15], [450, 33, 246, 8], [450, 37, 246, 8, "_jsxDevRuntime"], [450, 51, 246, 8], [450, 52, 246, 8, "jsxDEV"], [450, 58, 246, 8], [450, 60, 246, 9, "_reactNative"], [450, 72, 246, 9], [450, 73, 246, 9, "View"], [450, 77, 246, 13], [451, 12, 246, 14, "style"], [451, 17, 246, 19], [451, 19, 246, 21], [452, 14, 246, 23, "height"], [452, 20, 246, 29], [452, 22, 246, 31], [453, 12, 246, 35], [454, 10, 246, 37], [455, 12, 246, 37, "fileName"], [455, 20, 246, 37], [455, 22, 246, 37, "_jsxFileName"], [455, 34, 246, 37], [456, 12, 246, 37, "lineNumber"], [456, 22, 246, 37], [457, 12, 246, 37, "columnNumber"], [457, 24, 246, 37], [458, 10, 246, 37], [458, 17, 246, 39], [458, 18, 246, 40], [459, 8, 246, 40], [460, 10, 246, 40, "fileName"], [460, 18, 246, 40], [460, 20, 246, 40, "_jsxFileName"], [460, 32, 246, 40], [461, 10, 246, 40, "lineNumber"], [461, 20, 246, 40], [462, 10, 246, 40, "columnNumber"], [462, 22, 246, 40], [463, 8, 246, 40], [463, 15, 247, 20], [463, 16, 247, 21], [464, 6, 247, 21], [465, 8, 247, 21, "fileName"], [465, 16, 247, 21], [465, 18, 247, 21, "_jsxFileName"], [465, 30, 247, 21], [466, 8, 247, 21, "lineNumber"], [466, 18, 247, 21], [467, 8, 247, 21, "columnNumber"], [467, 20, 247, 21], [468, 6, 247, 21], [468, 13, 248, 12], [468, 14, 248, 13], [468, 29, 251, 6], [468, 33, 251, 6, "_jsxDevRuntime"], [468, 47, 251, 6], [468, 48, 251, 6, "jsxDEV"], [468, 54, 251, 6], [468, 56, 251, 7, "_FooterNavigation"], [468, 73, 251, 7], [468, 74, 251, 7, "default"], [468, 81, 251, 23], [469, 8, 251, 24, "navigation"], [469, 18, 251, 34], [469, 20, 251, 36, "navigation"], [469, 30, 251, 47], [470, 8, 251, 48, "activeScreen"], [470, 20, 251, 60], [470, 22, 251, 61], [471, 6, 251, 69], [472, 8, 251, 69, "fileName"], [472, 16, 251, 69], [472, 18, 251, 69, "_jsxFileName"], [472, 30, 251, 69], [473, 8, 251, 69, "lineNumber"], [473, 18, 251, 69], [474, 8, 251, 69, "columnNumber"], [474, 20, 251, 69], [475, 6, 251, 69], [475, 13, 251, 71], [475, 14, 251, 72], [475, 29, 254, 6], [475, 33, 254, 6, "_jsxDevRuntime"], [475, 47, 254, 6], [475, 48, 254, 6, "jsxDEV"], [475, 54, 254, 6], [475, 56, 254, 7, "_reactNativeSafeAreaContext"], [475, 83, 254, 7], [475, 84, 254, 7, "SafeAreaView"], [475, 96, 254, 19], [476, 8, 254, 20, "style"], [476, 13, 254, 25], [476, 15, 254, 27], [477, 10, 254, 29, "backgroundColor"], [477, 25, 254, 44], [477, 27, 254, 46], [478, 8, 254, 56], [478, 9, 254, 58], [479, 8, 254, 59, "edges"], [479, 13, 254, 64], [479, 15, 254, 66], [479, 16, 254, 67], [479, 24, 254, 75], [480, 6, 254, 77], [481, 8, 254, 77, "fileName"], [481, 16, 254, 77], [481, 18, 254, 77, "_jsxFileName"], [481, 30, 254, 77], [482, 8, 254, 77, "lineNumber"], [482, 18, 254, 77], [483, 8, 254, 77, "columnNumber"], [483, 20, 254, 77], [484, 6, 254, 77], [484, 13, 254, 79], [484, 14, 254, 80], [485, 4, 254, 80], [486, 6, 254, 80, "fileName"], [486, 14, 254, 80], [486, 16, 254, 80, "_jsxFileName"], [486, 28, 254, 80], [487, 6, 254, 80, "lineNumber"], [487, 16, 254, 80], [488, 6, 254, 80, "columnNumber"], [488, 18, 254, 80], [489, 4, 254, 80], [489, 11, 255, 10], [489, 12, 255, 11], [490, 2, 257, 0], [491, 2, 257, 1, "_s"], [491, 4, 257, 1], [491, 5, 14, 24, "OrdersScreen"], [491, 17, 14, 36], [492, 2, 14, 36, "_c"], [492, 4, 14, 36], [492, 7, 14, 24, "OrdersScreen"], [492, 19, 14, 36], [493, 2, 14, 36], [493, 6, 14, 36, "_c"], [493, 8, 14, 36], [494, 2, 14, 36, "$RefreshReg$"], [494, 14, 14, 36], [494, 15, 14, 36, "_c"], [494, 17, 14, 36], [495, 0, 14, 36], [495, 3]], "functionMap": {"names": ["<global>", "OrdersScreen", "orders.filter$argument_0", "filters.map$argument_0", "TouchableOpacity.props.onPress", "filteredOrders.map$argument_0", "order.items.map$argument_0"], "mappings": "AAA;eCa;uCCqD;GDK;uBE4D;uBCG,+BD;WFmB;8BIK;qCCoD;qBDI;WJ4B;CDc"}}, "type": "js/module"}]}