{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _jsHeapSizeLimit = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"jsHeapSizeLimit\");\n  var _totalJSHeapSize = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"totalJSHeapSize\");\n  var _usedJSHeapSize = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"usedJSHeapSize\");\n  var MemoryInfo = exports.default = /*#__PURE__*/function () {\n    function MemoryInfo(memoryInfo) {\n      (0, _classCallCheck2.default)(this, MemoryInfo);\n      Object.defineProperty(this, _jsHeapSizeLimit, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _totalJSHeapSize, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _usedJSHeapSize, {\n        writable: true,\n        value: void 0\n      });\n      if (memoryInfo != null) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _jsHeapSizeLimit)[_jsHeapSizeLimit] = memoryInfo.jsHeapSizeLimit;\n        (0, _classPrivateFieldLooseBase2.default)(this, _totalJSHeapSize)[_totalJSHeapSize] = memoryInfo.totalJSHeapSize;\n        (0, _classPrivateFieldLooseBase2.default)(this, _usedJSHeapSize)[_usedJSHeapSize] = memoryInfo.usedJSHeapSize;\n      }\n    }\n    return (0, _createClass2.default)(MemoryInfo, [{\n      key: \"jsHeapSizeLimit\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _jsHeapSizeLimit)[_jsHeapSizeLimit];\n      }\n    }, {\n      key: \"totalJSHeapSize\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _totalJSHeapSize)[_totalJSHeapSize];\n      }\n    }, {\n      key: \"usedJSHeapSize\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _usedJSHeapSize)[_usedJSHeapSize];\n      }\n    }]);\n  }();\n});", "lineCount": 52, "map": [[14, 6, 21, 21, "MemoryInfo"], [14, 16, 21, 31], [14, 19, 21, 31, "exports"], [14, 26, 21, 31], [14, 27, 21, 31, "default"], [14, 34, 21, 31], [15, 4, 26, 2], [15, 13, 26, 2, "MemoryInfo"], [15, 24, 26, 14, "memoryInfo"], [15, 34, 26, 41], [15, 36, 26, 43], [16, 6, 26, 43], [16, 10, 26, 43, "_classCallCheck2"], [16, 26, 26, 43], [16, 27, 26, 43, "default"], [16, 34, 26, 43], [16, 42, 26, 43, "MemoryInfo"], [16, 52, 26, 43], [17, 6, 26, 43, "Object"], [17, 12, 26, 43], [17, 13, 26, 43, "defineProperty"], [17, 27, 26, 43], [17, 34, 26, 43, "_jsHeapSizeLimit"], [17, 50, 26, 43], [18, 8, 26, 43, "writable"], [18, 16, 26, 43], [19, 8, 26, 43, "value"], [19, 13, 26, 43], [20, 6, 26, 43], [21, 6, 26, 43, "Object"], [21, 12, 26, 43], [21, 13, 26, 43, "defineProperty"], [21, 27, 26, 43], [21, 34, 26, 43, "_totalJSHeapSize"], [21, 50, 26, 43], [22, 8, 26, 43, "writable"], [22, 16, 26, 43], [23, 8, 26, 43, "value"], [23, 13, 26, 43], [24, 6, 26, 43], [25, 6, 26, 43, "Object"], [25, 12, 26, 43], [25, 13, 26, 43, "defineProperty"], [25, 27, 26, 43], [25, 34, 26, 43, "_usedJSHeapSize"], [25, 49, 26, 43], [26, 8, 26, 43, "writable"], [26, 16, 26, 43], [27, 8, 26, 43, "value"], [27, 13, 26, 43], [28, 6, 26, 43], [29, 6, 27, 4], [29, 10, 27, 8, "memoryInfo"], [29, 20, 27, 18], [29, 24, 27, 22], [29, 28, 27, 26], [29, 30, 27, 28], [30, 8, 28, 6], [30, 12, 28, 6, "_classPrivateFieldLooseBase2"], [30, 40, 28, 6], [30, 41, 28, 6, "default"], [30, 48, 28, 6], [30, 54, 28, 10], [30, 56, 28, 10, "_jsHeapSizeLimit"], [30, 72, 28, 10], [30, 74, 28, 10, "_jsHeapSizeLimit"], [30, 90, 28, 10], [30, 94, 28, 30, "memoryInfo"], [30, 104, 28, 40], [30, 105, 28, 41, "jsHeapSizeLimit"], [30, 120, 28, 56], [31, 8, 29, 6], [31, 12, 29, 6, "_classPrivateFieldLooseBase2"], [31, 40, 29, 6], [31, 41, 29, 6, "default"], [31, 48, 29, 6], [31, 54, 29, 10], [31, 56, 29, 10, "_totalJSHeapSize"], [31, 72, 29, 10], [31, 74, 29, 10, "_totalJSHeapSize"], [31, 90, 29, 10], [31, 94, 29, 30, "memoryInfo"], [31, 104, 29, 40], [31, 105, 29, 41, "totalJSHeapSize"], [31, 120, 29, 56], [32, 8, 30, 6], [32, 12, 30, 6, "_classPrivateFieldLooseBase2"], [32, 40, 30, 6], [32, 41, 30, 6, "default"], [32, 48, 30, 6], [32, 54, 30, 10], [32, 56, 30, 10, "_usedJSHeapSize"], [32, 71, 30, 10], [32, 73, 30, 10, "_usedJSHeapSize"], [32, 88, 30, 10], [32, 92, 30, 29, "memoryInfo"], [32, 102, 30, 39], [32, 103, 30, 40, "usedJSHeapSize"], [32, 117, 30, 54], [33, 6, 31, 4], [34, 4, 32, 2], [35, 4, 32, 3], [35, 15, 32, 3, "_createClass2"], [35, 28, 32, 3], [35, 29, 32, 3, "default"], [35, 36, 32, 3], [35, 38, 32, 3, "MemoryInfo"], [35, 48, 32, 3], [36, 6, 32, 3, "key"], [36, 9, 32, 3], [37, 6, 32, 3, "get"], [37, 9, 32, 3], [37, 11, 37, 2], [37, 20, 37, 2, "get"], [37, 21, 37, 2], [37, 23, 37, 33], [38, 8, 38, 4], [38, 19, 38, 4, "_classPrivateFieldLooseBase2"], [38, 47, 38, 4], [38, 48, 38, 4, "default"], [38, 55, 38, 4], [38, 57, 38, 11], [38, 61, 38, 15], [38, 63, 38, 15, "_jsHeapSizeLimit"], [38, 79, 38, 15], [38, 81, 38, 15, "_jsHeapSizeLimit"], [38, 97, 38, 15], [39, 6, 39, 2], [40, 4, 39, 3], [41, 6, 39, 3, "key"], [41, 9, 39, 3], [42, 6, 39, 3, "get"], [42, 9, 39, 3], [42, 11, 44, 2], [42, 20, 44, 2, "get"], [42, 21, 44, 2], [42, 23, 44, 33], [43, 8, 45, 4], [43, 19, 45, 4, "_classPrivateFieldLooseBase2"], [43, 47, 45, 4], [43, 48, 45, 4, "default"], [43, 55, 45, 4], [43, 57, 45, 11], [43, 61, 45, 15], [43, 63, 45, 15, "_totalJSHeapSize"], [43, 79, 45, 15], [43, 81, 45, 15, "_totalJSHeapSize"], [43, 97, 45, 15], [44, 6, 46, 2], [45, 4, 46, 3], [46, 6, 46, 3, "key"], [46, 9, 46, 3], [47, 6, 46, 3, "get"], [47, 9, 46, 3], [47, 11, 51, 2], [47, 20, 51, 2, "get"], [47, 21, 51, 2], [47, 23, 51, 32], [48, 8, 52, 4], [48, 19, 52, 4, "_classPrivateFieldLooseBase2"], [48, 47, 52, 4], [48, 48, 52, 4, "default"], [48, 55, 52, 4], [48, 57, 52, 11], [48, 61, 52, 15], [48, 63, 52, 15, "_usedJSHeapSize"], [48, 78, 52, 15], [48, 80, 52, 15, "_usedJSHeapSize"], [48, 95, 52, 15], [49, 6, 53, 2], [50, 4, 53, 3], [51, 2, 53, 3], [52, 0, 53, 3], [52, 3]], "functionMap": {"names": ["<global>", "MemoryInfo", "constructor", "get__jsHeapSizeLimit", "get__totalJSHeapSize", "get__usedJSHeapSize"], "mappings": "AAA;eCoB;ECK;GDM;EEK;GFE;EGK;GHE;EIK;GJE"}}, "type": "js/module"}]}