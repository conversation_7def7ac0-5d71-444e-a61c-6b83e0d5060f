{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 56, "index": 56}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n  var m = typeof window === 'undefined' ?\n  // React server mock\n  {\n    getLoadedFonts() {\n      return [];\n    },\n    loadAsync() {}\n  } : (0, _expoModulesCore.requireNativeModule)('ExpoFontLoader');\n  var _default = exports.default = m;\n});", "lineCount": 16, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_expoModulesCore"], [6, 22, 1, 0], [6, 25, 1, 0, "require"], [6, 32, 1, 0], [6, 33, 1, 0, "_dependencyMap"], [6, 47, 1, 0], [7, 2, 2, 0], [7, 6, 2, 6, "m"], [7, 7, 2, 7], [7, 10, 2, 10], [7, 17, 2, 17, "window"], [7, 23, 2, 23], [7, 28, 2, 28], [7, 39, 2, 39], [8, 2, 3, 6], [9, 2, 4, 8], [10, 4, 5, 12, "getLoadedFonts"], [10, 18, 5, 26, "getLoadedFonts"], [10, 19, 5, 26], [10, 21, 5, 29], [11, 6, 6, 16], [11, 13, 6, 23], [11, 15, 6, 25], [12, 4, 7, 12], [12, 5, 7, 13], [13, 4, 8, 12, "loadAsync"], [13, 13, 8, 21, "loadAsync"], [13, 14, 8, 21], [13, 16, 8, 24], [13, 17, 8, 26], [14, 2, 9, 8], [14, 3, 9, 9], [14, 6, 10, 6], [14, 10, 10, 6, "requireNativeModule"], [14, 46, 10, 25], [14, 48, 10, 26], [14, 64, 10, 42], [14, 65, 10, 43], [15, 2, 10, 44], [15, 6, 10, 44, "_default"], [15, 14, 10, 44], [15, 17, 10, 44, "exports"], [15, 24, 10, 44], [15, 25, 10, 44, "default"], [15, 32, 10, 44], [15, 35, 11, 15, "m"], [15, 36, 11, 16], [16, 0, 11, 16], [16, 3]], "functionMap": {"names": ["<global>", "getLoadedFonts", "loadAsync"], "mappings": "AAA;YCI;aDE;YEC,eF"}}, "type": "js/module"}]}