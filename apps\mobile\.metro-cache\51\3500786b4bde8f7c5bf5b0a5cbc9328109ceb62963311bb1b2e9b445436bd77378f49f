{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Components/SafeAreaView/SafeAreaView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 70}}], "key": "3QVUNIaP8S9RRUnN6oy5HbhEEko=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "../../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 48}}], "key": "/m0HqCpVZ4yItbJJaw+YeR/qFWU=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorHeaderButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 72}}], "key": "PCvEzKBAfAlrDut3o/xhXaVJxRM=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}, {"name": "./LogBoxImages/chevron-left.png", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 62, "column": 17}, "end": {"line": 62, "column": 59}}], "key": "x8uI0JSerK/ln25O5Zgp145wBS4=", "exportNames": ["*"]}}, {"name": "./LogBoxImages/chevron-right.png", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 74, "column": 17}, "end": {"line": 74, "column": 60}}], "key": "AdN9bB7I7ffs3GLs4awYjctk/A0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = LogBoxInspectorHeader;\n  var _SafeAreaView = _interopRequireDefault(require(_dependencyMap[1], \"../../Components/SafeAreaView/SafeAreaView\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"../../Text/Text\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[5], \"../../Utilities/Platform\"));\n  var _LogBoxInspectorHeaderButton = _interopRequireDefault(require(_dependencyMap[6], \"./LogBoxInspectorHeaderButton\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[7], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[8], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[9], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxInspectorHeader.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var LogBoxInspectorHeaderSafeArea = _Platform.default.OS === 'android' ? _View.default : _SafeAreaView.default;\n  function LogBoxInspectorHeader(props) {\n    if (props.level === 'syntax') {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(LogBoxInspectorHeaderSafeArea, {\n        style: styles[props.level],\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.header,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.title,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.titleText,\n              id: \"logbox_header_title_text\",\n              children: \"Failed to compile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 7\n      }, this);\n    }\n    var prevIndex = props.selectedIndex - 1 < 0 ? props.total - 1 : props.selectedIndex - 1;\n    var nextIndex = props.selectedIndex + 1 > props.total - 1 ? 0 : props.selectedIndex + 1;\n    var titleText = `Log ${props.selectedIndex + 1} of ${props.total}`;\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(LogBoxInspectorHeaderSafeArea, {\n      style: styles[props.level],\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.header,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorHeaderButton.default, {\n          id: \"logbox_header_button_prev\",\n          disabled: props.total <= 1,\n          level: props.level,\n          image: require(_dependencyMap[10], \"./LogBoxImages/chevron-left.png\"),\n          onPress: () => props.onSelectIndex(prevIndex)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.title,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.titleText,\n            id: \"logbox_header_title_text\",\n            children: titleText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorHeaderButton.default, {\n          id: \"logbox_header_button_next\",\n          disabled: props.total <= 1,\n          level: props.level,\n          image: require(_dependencyMap[11], \"./LogBoxImages/chevron-right.png\"),\n          onPress: () => props.onSelectIndex(nextIndex)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _StyleSheet.default.create({\n    syntax: {\n      backgroundColor: LogBoxStyle.getFatalColor()\n    },\n    fatal: {\n      backgroundColor: LogBoxStyle.getFatalColor()\n    },\n    warn: {\n      backgroundColor: LogBoxStyle.getWarningColor()\n    },\n    error: {\n      backgroundColor: LogBoxStyle.getErrorColor()\n    },\n    header: {\n      flexDirection: 'row',\n      height: _Platform.default.select({\n        android: 48,\n        ios: 44\n      })\n    },\n    title: {\n      alignItems: 'center',\n      flex: 1,\n      justifyContent: 'center'\n    },\n    titleText: {\n      color: LogBoxStyle.getTextColor(),\n      fontSize: 16,\n      fontWeight: '600',\n      includeFontPadding: false,\n      lineHeight: 20\n    }\n  });\n});", "lineCount": 139, "map": [[7, 2, 14, 0], [7, 6, 14, 0, "_SafeAreaView"], [7, 19, 14, 0], [7, 22, 14, 0, "_interopRequireDefault"], [7, 44, 14, 0], [7, 45, 14, 0, "require"], [7, 52, 14, 0], [7, 53, 14, 0, "_dependencyMap"], [7, 67, 14, 0], [8, 2, 15, 0], [8, 6, 15, 0, "_View"], [8, 11, 15, 0], [8, 14, 15, 0, "_interopRequireDefault"], [8, 36, 15, 0], [8, 37, 15, 0, "require"], [8, 44, 15, 0], [8, 45, 15, 0, "_dependencyMap"], [8, 59, 15, 0], [9, 2, 16, 0], [9, 6, 16, 0, "_StyleSheet"], [9, 17, 16, 0], [9, 20, 16, 0, "_interopRequireDefault"], [9, 42, 16, 0], [9, 43, 16, 0, "require"], [9, 50, 16, 0], [9, 51, 16, 0, "_dependencyMap"], [9, 65, 16, 0], [10, 2, 17, 0], [10, 6, 17, 0, "_Text"], [10, 11, 17, 0], [10, 14, 17, 0, "_interopRequireDefault"], [10, 36, 17, 0], [10, 37, 17, 0, "require"], [10, 44, 17, 0], [10, 45, 17, 0, "_dependencyMap"], [10, 59, 17, 0], [11, 2, 18, 0], [11, 6, 18, 0, "_Platform"], [11, 15, 18, 0], [11, 18, 18, 0, "_interopRequireDefault"], [11, 40, 18, 0], [11, 41, 18, 0, "require"], [11, 48, 18, 0], [11, 49, 18, 0, "_dependencyMap"], [11, 63, 18, 0], [12, 2, 19, 0], [12, 6, 19, 0, "_LogBoxInspectorHeaderButton"], [12, 34, 19, 0], [12, 37, 19, 0, "_interopRequireDefault"], [12, 59, 19, 0], [12, 60, 19, 0, "require"], [12, 67, 19, 0], [12, 68, 19, 0, "_dependencyMap"], [12, 82, 19, 0], [13, 2, 20, 0], [13, 6, 20, 0, "LogBoxStyle"], [13, 17, 20, 0], [13, 20, 20, 0, "_interopRequireWildcard"], [13, 43, 20, 0], [13, 44, 20, 0, "require"], [13, 51, 20, 0], [13, 52, 20, 0, "_dependencyMap"], [13, 66, 20, 0], [14, 2, 21, 0], [14, 6, 21, 0, "React"], [14, 11, 21, 0], [14, 14, 21, 0, "_interopRequireWildcard"], [14, 37, 21, 0], [14, 38, 21, 0, "require"], [14, 45, 21, 0], [14, 46, 21, 0, "_dependencyMap"], [14, 60, 21, 0], [15, 2, 21, 31], [15, 6, 21, 31, "_jsxDevRuntime"], [15, 20, 21, 31], [15, 23, 21, 31, "require"], [15, 30, 21, 31], [15, 31, 21, 31, "_dependencyMap"], [15, 45, 21, 31], [16, 2, 21, 31], [16, 6, 21, 31, "_jsxFileName"], [16, 18, 21, 31], [17, 2, 21, 31], [17, 11, 21, 31, "_interopRequireWildcard"], [17, 35, 21, 31, "e"], [17, 36, 21, 31], [17, 38, 21, 31, "t"], [17, 39, 21, 31], [17, 68, 21, 31, "WeakMap"], [17, 75, 21, 31], [17, 81, 21, 31, "r"], [17, 82, 21, 31], [17, 89, 21, 31, "WeakMap"], [17, 96, 21, 31], [17, 100, 21, 31, "n"], [17, 101, 21, 31], [17, 108, 21, 31, "WeakMap"], [17, 115, 21, 31], [17, 127, 21, 31, "_interopRequireWildcard"], [17, 150, 21, 31], [17, 162, 21, 31, "_interopRequireWildcard"], [17, 163, 21, 31, "e"], [17, 164, 21, 31], [17, 166, 21, 31, "t"], [17, 167, 21, 31], [17, 176, 21, 31, "t"], [17, 177, 21, 31], [17, 181, 21, 31, "e"], [17, 182, 21, 31], [17, 186, 21, 31, "e"], [17, 187, 21, 31], [17, 188, 21, 31, "__esModule"], [17, 198, 21, 31], [17, 207, 21, 31, "e"], [17, 208, 21, 31], [17, 214, 21, 31, "o"], [17, 215, 21, 31], [17, 217, 21, 31, "i"], [17, 218, 21, 31], [17, 220, 21, 31, "f"], [17, 221, 21, 31], [17, 226, 21, 31, "__proto__"], [17, 235, 21, 31], [17, 243, 21, 31, "default"], [17, 250, 21, 31], [17, 252, 21, 31, "e"], [17, 253, 21, 31], [17, 270, 21, 31, "e"], [17, 271, 21, 31], [17, 294, 21, 31, "e"], [17, 295, 21, 31], [17, 320, 21, 31, "e"], [17, 321, 21, 31], [17, 330, 21, 31, "f"], [17, 331, 21, 31], [17, 337, 21, 31, "o"], [17, 338, 21, 31], [17, 341, 21, 31, "t"], [17, 342, 21, 31], [17, 345, 21, 31, "n"], [17, 346, 21, 31], [17, 349, 21, 31, "r"], [17, 350, 21, 31], [17, 358, 21, 31, "o"], [17, 359, 21, 31], [17, 360, 21, 31, "has"], [17, 363, 21, 31], [17, 364, 21, 31, "e"], [17, 365, 21, 31], [17, 375, 21, 31, "o"], [17, 376, 21, 31], [17, 377, 21, 31, "get"], [17, 380, 21, 31], [17, 381, 21, 31, "e"], [17, 382, 21, 31], [17, 385, 21, 31, "o"], [17, 386, 21, 31], [17, 387, 21, 31, "set"], [17, 390, 21, 31], [17, 391, 21, 31, "e"], [17, 392, 21, 31], [17, 394, 21, 31, "f"], [17, 395, 21, 31], [17, 409, 21, 31, "_t"], [17, 411, 21, 31], [17, 415, 21, 31, "e"], [17, 416, 21, 31], [17, 432, 21, 31, "_t"], [17, 434, 21, 31], [17, 441, 21, 31, "hasOwnProperty"], [17, 455, 21, 31], [17, 456, 21, 31, "call"], [17, 460, 21, 31], [17, 461, 21, 31, "e"], [17, 462, 21, 31], [17, 464, 21, 31, "_t"], [17, 466, 21, 31], [17, 473, 21, 31, "i"], [17, 474, 21, 31], [17, 478, 21, 31, "o"], [17, 479, 21, 31], [17, 482, 21, 31, "Object"], [17, 488, 21, 31], [17, 489, 21, 31, "defineProperty"], [17, 503, 21, 31], [17, 508, 21, 31, "Object"], [17, 514, 21, 31], [17, 515, 21, 31, "getOwnPropertyDescriptor"], [17, 539, 21, 31], [17, 540, 21, 31, "e"], [17, 541, 21, 31], [17, 543, 21, 31, "_t"], [17, 545, 21, 31], [17, 552, 21, 31, "i"], [17, 553, 21, 31], [17, 554, 21, 31, "get"], [17, 557, 21, 31], [17, 561, 21, 31, "i"], [17, 562, 21, 31], [17, 563, 21, 31, "set"], [17, 566, 21, 31], [17, 570, 21, 31, "o"], [17, 571, 21, 31], [17, 572, 21, 31, "f"], [17, 573, 21, 31], [17, 575, 21, 31, "_t"], [17, 577, 21, 31], [17, 579, 21, 31, "i"], [17, 580, 21, 31], [17, 584, 21, 31, "f"], [17, 585, 21, 31], [17, 586, 21, 31, "_t"], [17, 588, 21, 31], [17, 592, 21, 31, "e"], [17, 593, 21, 31], [17, 594, 21, 31, "_t"], [17, 596, 21, 31], [17, 607, 21, 31, "f"], [17, 608, 21, 31], [17, 613, 21, 31, "e"], [17, 614, 21, 31], [17, 616, 21, 31, "t"], [17, 617, 21, 31], [18, 2, 30, 0], [18, 6, 30, 6, "LogBoxInspectorHeaderSafeArea"], [18, 35, 30, 67], [18, 38, 31, 2, "Platform"], [18, 55, 31, 10], [18, 56, 31, 11, "OS"], [18, 58, 31, 13], [18, 63, 31, 18], [18, 72, 31, 27], [18, 75, 31, 30, "View"], [18, 88, 31, 34], [18, 91, 31, 37, "SafeAreaView"], [18, 112, 31, 49], [19, 2, 33, 15], [19, 11, 33, 24, "LogBoxInspectorHeader"], [19, 32, 33, 45, "LogBoxInspectorHeader"], [19, 33, 33, 46, "props"], [19, 38, 33, 58], [19, 40, 33, 72], [20, 4, 34, 2], [20, 8, 34, 6, "props"], [20, 13, 34, 11], [20, 14, 34, 12, "level"], [20, 19, 34, 17], [20, 24, 34, 22], [20, 32, 34, 30], [20, 34, 34, 32], [21, 6, 35, 4], [21, 26, 36, 6], [21, 30, 36, 6, "_jsxDevRuntime"], [21, 44, 36, 6], [21, 45, 36, 6, "jsxDEV"], [21, 51, 36, 6], [21, 53, 36, 7, "LogBoxInspectorHeaderSafeArea"], [21, 82, 36, 36], [22, 8, 36, 37, "style"], [22, 13, 36, 42], [22, 15, 36, 44, "styles"], [22, 21, 36, 50], [22, 22, 36, 51, "props"], [22, 27, 36, 56], [22, 28, 36, 57, "level"], [22, 33, 36, 62], [22, 34, 36, 64], [23, 8, 36, 64, "children"], [23, 16, 36, 64], [23, 31, 37, 8], [23, 35, 37, 8, "_jsxDevRuntime"], [23, 49, 37, 8], [23, 50, 37, 8, "jsxDEV"], [23, 56, 37, 8], [23, 58, 37, 9, "_View"], [23, 63, 37, 9], [23, 64, 37, 9, "default"], [23, 71, 37, 13], [24, 10, 37, 14, "style"], [24, 15, 37, 19], [24, 17, 37, 21, "styles"], [24, 23, 37, 27], [24, 24, 37, 28, "header"], [24, 30, 37, 35], [25, 10, 37, 35, "children"], [25, 18, 37, 35], [25, 33, 38, 10], [25, 37, 38, 10, "_jsxDevRuntime"], [25, 51, 38, 10], [25, 52, 38, 10, "jsxDEV"], [25, 58, 38, 10], [25, 60, 38, 11, "_View"], [25, 65, 38, 11], [25, 66, 38, 11, "default"], [25, 73, 38, 15], [26, 12, 38, 16, "style"], [26, 17, 38, 21], [26, 19, 38, 23, "styles"], [26, 25, 38, 29], [26, 26, 38, 30, "title"], [26, 31, 38, 36], [27, 12, 38, 36, "children"], [27, 20, 38, 36], [27, 35, 39, 12], [27, 39, 39, 12, "_jsxDevRuntime"], [27, 53, 39, 12], [27, 54, 39, 12, "jsxDEV"], [27, 60, 39, 12], [27, 62, 39, 13, "_Text"], [27, 67, 39, 13], [27, 68, 39, 13, "default"], [27, 75, 39, 17], [28, 14, 39, 18, "style"], [28, 19, 39, 23], [28, 21, 39, 25, "styles"], [28, 27, 39, 31], [28, 28, 39, 32, "titleText"], [28, 37, 39, 42], [29, 14, 39, 43, "id"], [29, 16, 39, 45], [29, 18, 39, 46], [29, 44, 39, 72], [30, 14, 39, 72, "children"], [30, 22, 39, 72], [30, 24, 39, 73], [31, 12, 41, 12], [32, 14, 41, 12, "fileName"], [32, 22, 41, 12], [32, 24, 41, 12, "_jsxFileName"], [32, 36, 41, 12], [33, 14, 41, 12, "lineNumber"], [33, 24, 41, 12], [34, 14, 41, 12, "columnNumber"], [34, 26, 41, 12], [35, 12, 41, 12], [35, 19, 41, 18], [36, 10, 41, 19], [37, 12, 41, 19, "fileName"], [37, 20, 41, 19], [37, 22, 41, 19, "_jsxFileName"], [37, 34, 41, 19], [38, 12, 41, 19, "lineNumber"], [38, 22, 41, 19], [39, 12, 41, 19, "columnNumber"], [39, 24, 41, 19], [40, 10, 41, 19], [40, 17, 42, 16], [41, 8, 42, 17], [42, 10, 42, 17, "fileName"], [42, 18, 42, 17], [42, 20, 42, 17, "_jsxFileName"], [42, 32, 42, 17], [43, 10, 42, 17, "lineNumber"], [43, 20, 42, 17], [44, 10, 42, 17, "columnNumber"], [44, 22, 42, 17], [45, 8, 42, 17], [45, 15, 43, 14], [46, 6, 43, 15], [47, 8, 43, 15, "fileName"], [47, 16, 43, 15], [47, 18, 43, 15, "_jsxFileName"], [47, 30, 43, 15], [48, 8, 43, 15, "lineNumber"], [48, 18, 43, 15], [49, 8, 43, 15, "columnNumber"], [49, 20, 43, 15], [50, 6, 43, 15], [50, 13, 44, 37], [50, 14, 44, 38], [51, 4, 46, 2], [52, 4, 48, 2], [52, 8, 48, 8, "prevIndex"], [52, 17, 48, 17], [52, 20, 49, 4, "props"], [52, 25, 49, 9], [52, 26, 49, 10, "selectedIndex"], [52, 39, 49, 23], [52, 42, 49, 26], [52, 43, 49, 27], [52, 46, 49, 30], [52, 47, 49, 31], [52, 50, 49, 34, "props"], [52, 55, 49, 39], [52, 56, 49, 40, "total"], [52, 61, 49, 45], [52, 64, 49, 48], [52, 65, 49, 49], [52, 68, 49, 52, "props"], [52, 73, 49, 57], [52, 74, 49, 58, "selectedIndex"], [52, 87, 49, 71], [52, 90, 49, 74], [52, 91, 49, 75], [53, 4, 50, 2], [53, 8, 50, 8, "nextIndex"], [53, 17, 50, 17], [53, 20, 51, 4, "props"], [53, 25, 51, 9], [53, 26, 51, 10, "selectedIndex"], [53, 39, 51, 23], [53, 42, 51, 26], [53, 43, 51, 27], [53, 46, 51, 30, "props"], [53, 51, 51, 35], [53, 52, 51, 36, "total"], [53, 57, 51, 41], [53, 60, 51, 44], [53, 61, 51, 45], [53, 64, 51, 48], [53, 65, 51, 49], [53, 68, 51, 52, "props"], [53, 73, 51, 57], [53, 74, 51, 58, "selectedIndex"], [53, 87, 51, 71], [53, 90, 51, 74], [53, 91, 51, 75], [54, 4, 53, 2], [54, 8, 53, 8, "titleText"], [54, 17, 53, 17], [54, 20, 53, 20], [54, 27, 53, 27, "props"], [54, 32, 53, 32], [54, 33, 53, 33, "selectedIndex"], [54, 46, 53, 46], [54, 49, 53, 49], [54, 50, 53, 50], [54, 57, 53, 57, "props"], [54, 62, 53, 62], [54, 63, 53, 63, "total"], [54, 68, 53, 68], [54, 70, 53, 70], [55, 4, 55, 2], [55, 24, 56, 4], [55, 28, 56, 4, "_jsxDevRuntime"], [55, 42, 56, 4], [55, 43, 56, 4, "jsxDEV"], [55, 49, 56, 4], [55, 51, 56, 5, "LogBoxInspectorHeaderSafeArea"], [55, 80, 56, 34], [56, 6, 56, 35, "style"], [56, 11, 56, 40], [56, 13, 56, 42, "styles"], [56, 19, 56, 48], [56, 20, 56, 49, "props"], [56, 25, 56, 54], [56, 26, 56, 55, "level"], [56, 31, 56, 60], [56, 32, 56, 62], [57, 6, 56, 62, "children"], [57, 14, 56, 62], [57, 29, 57, 6], [57, 33, 57, 6, "_jsxDevRuntime"], [57, 47, 57, 6], [57, 48, 57, 6, "jsxDEV"], [57, 54, 57, 6], [57, 56, 57, 7, "_View"], [57, 61, 57, 7], [57, 62, 57, 7, "default"], [57, 69, 57, 11], [58, 8, 57, 12, "style"], [58, 13, 57, 17], [58, 15, 57, 19, "styles"], [58, 21, 57, 25], [58, 22, 57, 26, "header"], [58, 28, 57, 33], [59, 8, 57, 33, "children"], [59, 16, 57, 33], [59, 32, 58, 8], [59, 36, 58, 8, "_jsxDevRuntime"], [59, 50, 58, 8], [59, 51, 58, 8, "jsxDEV"], [59, 57, 58, 8], [59, 59, 58, 9, "_LogBoxInspectorHeaderButton"], [59, 87, 58, 9], [59, 88, 58, 9, "default"], [59, 95, 58, 36], [60, 10, 59, 10, "id"], [60, 12, 59, 12], [60, 14, 59, 13], [60, 41, 59, 40], [61, 10, 60, 10, "disabled"], [61, 18, 60, 18], [61, 20, 60, 20, "props"], [61, 25, 60, 25], [61, 26, 60, 26, "total"], [61, 31, 60, 31], [61, 35, 60, 35], [61, 36, 60, 37], [62, 10, 61, 10, "level"], [62, 15, 61, 15], [62, 17, 61, 17, "props"], [62, 22, 61, 22], [62, 23, 61, 23, "level"], [62, 28, 61, 29], [63, 10, 62, 10, "image"], [63, 15, 62, 15], [63, 17, 62, 17, "require"], [63, 24, 62, 24], [63, 25, 62, 24, "_dependencyMap"], [63, 39, 62, 24], [63, 78, 62, 58], [63, 79, 62, 60], [64, 10, 63, 10, "onPress"], [64, 17, 63, 17], [64, 19, 63, 19, "onPress"], [64, 20, 63, 19], [64, 25, 63, 25, "props"], [64, 30, 63, 30], [64, 31, 63, 31, "onSelectIndex"], [64, 44, 63, 44], [64, 45, 63, 45, "prevIndex"], [64, 54, 63, 54], [65, 8, 63, 56], [66, 10, 63, 56, "fileName"], [66, 18, 63, 56], [66, 20, 63, 56, "_jsxFileName"], [66, 32, 63, 56], [67, 10, 63, 56, "lineNumber"], [67, 20, 63, 56], [68, 10, 63, 56, "columnNumber"], [68, 22, 63, 56], [69, 8, 63, 56], [69, 15, 64, 9], [69, 16, 64, 10], [69, 31, 65, 8], [69, 35, 65, 8, "_jsxDevRuntime"], [69, 49, 65, 8], [69, 50, 65, 8, "jsxDEV"], [69, 56, 65, 8], [69, 58, 65, 9, "_View"], [69, 63, 65, 9], [69, 64, 65, 9, "default"], [69, 71, 65, 13], [70, 10, 65, 14, "style"], [70, 15, 65, 19], [70, 17, 65, 21, "styles"], [70, 23, 65, 27], [70, 24, 65, 28, "title"], [70, 29, 65, 34], [71, 10, 65, 34, "children"], [71, 18, 65, 34], [71, 33, 66, 10], [71, 37, 66, 10, "_jsxDevRuntime"], [71, 51, 66, 10], [71, 52, 66, 10, "jsxDEV"], [71, 58, 66, 10], [71, 60, 66, 11, "_Text"], [71, 65, 66, 11], [71, 66, 66, 11, "default"], [71, 73, 66, 15], [72, 12, 66, 16, "style"], [72, 17, 66, 21], [72, 19, 66, 23, "styles"], [72, 25, 66, 29], [72, 26, 66, 30, "titleText"], [72, 35, 66, 40], [73, 12, 66, 41, "id"], [73, 14, 66, 43], [73, 16, 66, 44], [73, 42, 66, 70], [74, 12, 66, 70, "children"], [74, 20, 66, 70], [74, 22, 67, 13, "titleText"], [75, 10, 67, 22], [76, 12, 67, 22, "fileName"], [76, 20, 67, 22], [76, 22, 67, 22, "_jsxFileName"], [76, 34, 67, 22], [77, 12, 67, 22, "lineNumber"], [77, 22, 67, 22], [78, 12, 67, 22, "columnNumber"], [78, 24, 67, 22], [79, 10, 67, 22], [79, 17, 68, 16], [80, 8, 68, 17], [81, 10, 68, 17, "fileName"], [81, 18, 68, 17], [81, 20, 68, 17, "_jsxFileName"], [81, 32, 68, 17], [82, 10, 68, 17, "lineNumber"], [82, 20, 68, 17], [83, 10, 68, 17, "columnNumber"], [83, 22, 68, 17], [84, 8, 68, 17], [84, 15, 69, 14], [84, 16, 69, 15], [84, 31, 70, 8], [84, 35, 70, 8, "_jsxDevRuntime"], [84, 49, 70, 8], [84, 50, 70, 8, "jsxDEV"], [84, 56, 70, 8], [84, 58, 70, 9, "_LogBoxInspectorHeaderButton"], [84, 86, 70, 9], [84, 87, 70, 9, "default"], [84, 94, 70, 36], [85, 10, 71, 10, "id"], [85, 12, 71, 12], [85, 14, 71, 13], [85, 41, 71, 40], [86, 10, 72, 10, "disabled"], [86, 18, 72, 18], [86, 20, 72, 20, "props"], [86, 25, 72, 25], [86, 26, 72, 26, "total"], [86, 31, 72, 31], [86, 35, 72, 35], [86, 36, 72, 37], [87, 10, 73, 10, "level"], [87, 15, 73, 15], [87, 17, 73, 17, "props"], [87, 22, 73, 22], [87, 23, 73, 23, "level"], [87, 28, 73, 29], [88, 10, 74, 10, "image"], [88, 15, 74, 15], [88, 17, 74, 17, "require"], [88, 24, 74, 24], [88, 25, 74, 24, "_dependencyMap"], [88, 39, 74, 24], [88, 79, 74, 59], [88, 80, 74, 61], [89, 10, 75, 10, "onPress"], [89, 17, 75, 17], [89, 19, 75, 19, "onPress"], [89, 20, 75, 19], [89, 25, 75, 25, "props"], [89, 30, 75, 30], [89, 31, 75, 31, "onSelectIndex"], [89, 44, 75, 44], [89, 45, 75, 45, "nextIndex"], [89, 54, 75, 54], [90, 8, 75, 56], [91, 10, 75, 56, "fileName"], [91, 18, 75, 56], [91, 20, 75, 56, "_jsxFileName"], [91, 32, 75, 56], [92, 10, 75, 56, "lineNumber"], [92, 20, 75, 56], [93, 10, 75, 56, "columnNumber"], [93, 22, 75, 56], [94, 8, 75, 56], [94, 15, 76, 9], [94, 16, 76, 10], [95, 6, 76, 10], [96, 8, 76, 10, "fileName"], [96, 16, 76, 10], [96, 18, 76, 10, "_jsxFileName"], [96, 30, 76, 10], [97, 8, 76, 10, "lineNumber"], [97, 18, 76, 10], [98, 8, 76, 10, "columnNumber"], [98, 20, 76, 10], [99, 6, 76, 10], [99, 13, 77, 12], [100, 4, 77, 13], [101, 6, 77, 13, "fileName"], [101, 14, 77, 13], [101, 16, 77, 13, "_jsxFileName"], [101, 28, 77, 13], [102, 6, 77, 13, "lineNumber"], [102, 16, 77, 13], [103, 6, 77, 13, "columnNumber"], [103, 18, 77, 13], [104, 4, 77, 13], [104, 11, 78, 35], [104, 12, 78, 36], [105, 2, 80, 0], [106, 2, 82, 0], [106, 6, 82, 6, "styles"], [106, 12, 82, 12], [106, 15, 82, 15, "StyleSheet"], [106, 34, 82, 25], [106, 35, 82, 26, "create"], [106, 41, 82, 32], [106, 42, 82, 33], [107, 4, 83, 2, "syntax"], [107, 10, 83, 8], [107, 12, 83, 10], [108, 6, 84, 4, "backgroundColor"], [108, 21, 84, 19], [108, 23, 84, 21, "LogBoxStyle"], [108, 34, 84, 32], [108, 35, 84, 33, "getFatalColor"], [108, 48, 84, 46], [108, 49, 84, 47], [109, 4, 85, 2], [109, 5, 85, 3], [110, 4, 86, 2, "fatal"], [110, 9, 86, 7], [110, 11, 86, 9], [111, 6, 87, 4, "backgroundColor"], [111, 21, 87, 19], [111, 23, 87, 21, "LogBoxStyle"], [111, 34, 87, 32], [111, 35, 87, 33, "getFatalColor"], [111, 48, 87, 46], [111, 49, 87, 47], [112, 4, 88, 2], [112, 5, 88, 3], [113, 4, 89, 2, "warn"], [113, 8, 89, 6], [113, 10, 89, 8], [114, 6, 90, 4, "backgroundColor"], [114, 21, 90, 19], [114, 23, 90, 21, "LogBoxStyle"], [114, 34, 90, 32], [114, 35, 90, 33, "getWarningColor"], [114, 50, 90, 48], [114, 51, 90, 49], [115, 4, 91, 2], [115, 5, 91, 3], [116, 4, 92, 2, "error"], [116, 9, 92, 7], [116, 11, 92, 9], [117, 6, 93, 4, "backgroundColor"], [117, 21, 93, 19], [117, 23, 93, 21, "LogBoxStyle"], [117, 34, 93, 32], [117, 35, 93, 33, "getErrorColor"], [117, 48, 93, 46], [117, 49, 93, 47], [118, 4, 94, 2], [118, 5, 94, 3], [119, 4, 95, 2, "header"], [119, 10, 95, 8], [119, 12, 95, 10], [120, 6, 96, 4, "flexDirection"], [120, 19, 96, 17], [120, 21, 96, 19], [120, 26, 96, 24], [121, 6, 97, 4, "height"], [121, 12, 97, 10], [121, 14, 97, 12, "Platform"], [121, 31, 97, 20], [121, 32, 97, 21, "select"], [121, 38, 97, 27], [121, 39, 97, 28], [122, 8, 98, 6, "android"], [122, 15, 98, 13], [122, 17, 98, 15], [122, 19, 98, 17], [123, 8, 99, 6, "ios"], [123, 11, 99, 9], [123, 13, 99, 11], [124, 6, 100, 4], [124, 7, 100, 5], [125, 4, 101, 2], [125, 5, 101, 3], [126, 4, 102, 2, "title"], [126, 9, 102, 7], [126, 11, 102, 9], [127, 6, 103, 4, "alignItems"], [127, 16, 103, 14], [127, 18, 103, 16], [127, 26, 103, 24], [128, 6, 104, 4, "flex"], [128, 10, 104, 8], [128, 12, 104, 10], [128, 13, 104, 11], [129, 6, 105, 4, "justifyContent"], [129, 20, 105, 18], [129, 22, 105, 20], [130, 4, 106, 2], [130, 5, 106, 3], [131, 4, 107, 2, "titleText"], [131, 13, 107, 11], [131, 15, 107, 13], [132, 6, 108, 4, "color"], [132, 11, 108, 9], [132, 13, 108, 11, "LogBoxStyle"], [132, 24, 108, 22], [132, 25, 108, 23, "getTextColor"], [132, 37, 108, 35], [132, 38, 108, 36], [132, 39, 108, 37], [133, 6, 109, 4, "fontSize"], [133, 14, 109, 12], [133, 16, 109, 14], [133, 18, 109, 16], [134, 6, 110, 4, "fontWeight"], [134, 16, 110, 14], [134, 18, 110, 16], [134, 23, 110, 21], [135, 6, 111, 4, "includeFontPadding"], [135, 24, 111, 22], [135, 26, 111, 24], [135, 31, 111, 29], [136, 6, 112, 4, "lineHeight"], [136, 16, 112, 14], [136, 18, 112, 16], [137, 4, 113, 2], [138, 2, 114, 0], [138, 3, 114, 1], [138, 4, 114, 2], [139, 0, 114, 3], [139, 3]], "functionMap": {"names": ["<global>", "LogBoxInspectorHeader", "LogBoxInspectorHeaderButton.props.onPress"], "mappings": "AAA;eCgC;mBC8B,oCD;mBCY,oCD;CDK"}}, "type": "js/module"}]}