{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../NativeModules/specs/NativeSourceCode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 74}}], "key": "1linPxu6TBFXPeh9GC4tTgfiZQE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = getDevServer;\n  var _NativeSourceCode = _interopRequireDefault(require(_dependencyMap[1], \"../../NativeModules/specs/NativeSourceCode\"));\n  var _cachedDevServerURL;\n  var _cachedFullBundleURL;\n  var FALLBACK = 'http://localhost:8081/';\n  function getDevServer() {\n    if (_cachedDevServerURL === undefined) {\n      var scriptUrl = _NativeSourceCode.default.getConstants().scriptURL;\n      var match = scriptUrl.match(/^https?:\\/\\/.*?\\//);\n      _cachedDevServerURL = match ? match[0] : null;\n      _cachedFullBundleURL = match ? scriptUrl : null;\n    }\n    return {\n      url: _cachedDevServerURL ?? FALLBACK,\n      fullBundleUrl: _cachedFullBundleURL,\n      bundleLoadedFromServer: _cachedDevServerURL !== null\n    };\n  }\n});", "lineCount": 24, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeSourceCode"], [7, 23, 11, 0], [7, 26, 11, 0, "_interopRequireDefault"], [7, 48, 11, 0], [7, 49, 11, 0, "require"], [7, 56, 11, 0], [7, 57, 11, 0, "_dependencyMap"], [7, 71, 11, 0], [8, 2, 13, 0], [8, 6, 13, 4, "_cachedDevServerURL"], [8, 25, 13, 32], [9, 2, 14, 0], [9, 6, 14, 4, "_cachedFullBundleURL"], [9, 26, 14, 33], [10, 2, 15, 0], [10, 6, 15, 6, "FALLBACK"], [10, 14, 15, 14], [10, 17, 15, 17], [10, 41, 15, 41], [11, 2, 28, 15], [11, 11, 28, 24, "getDevServer"], [11, 23, 28, 36, "getDevServer"], [11, 24, 28, 36], [11, 26, 28, 54], [12, 4, 29, 2], [12, 8, 29, 6, "_cachedDevServerURL"], [12, 27, 29, 25], [12, 32, 29, 30, "undefined"], [12, 41, 29, 39], [12, 43, 29, 41], [13, 6, 30, 4], [13, 10, 30, 10, "scriptUrl"], [13, 19, 30, 19], [13, 22, 30, 22, "NativeSourceCode"], [13, 47, 30, 38], [13, 48, 30, 39, "getConstants"], [13, 60, 30, 51], [13, 61, 30, 52], [13, 62, 30, 53], [13, 63, 30, 54, "scriptURL"], [13, 72, 30, 63], [14, 6, 31, 4], [14, 10, 31, 10, "match"], [14, 15, 31, 15], [14, 18, 31, 18, "scriptUrl"], [14, 27, 31, 27], [14, 28, 31, 28, "match"], [14, 33, 31, 33], [14, 34, 31, 34], [14, 53, 31, 53], [14, 54, 31, 54], [15, 6, 32, 4, "_cachedDevServerURL"], [15, 25, 32, 23], [15, 28, 32, 26, "match"], [15, 33, 32, 31], [15, 36, 32, 34, "match"], [15, 41, 32, 39], [15, 42, 32, 40], [15, 43, 32, 41], [15, 44, 32, 42], [15, 47, 32, 45], [15, 51, 32, 49], [16, 6, 33, 4, "_cachedFullBundleURL"], [16, 26, 33, 24], [16, 29, 33, 27, "match"], [16, 34, 33, 32], [16, 37, 33, 35, "scriptUrl"], [16, 46, 33, 44], [16, 49, 33, 47], [16, 53, 33, 51], [17, 4, 34, 2], [18, 4, 36, 2], [18, 11, 36, 9], [19, 6, 37, 4, "url"], [19, 9, 37, 7], [19, 11, 37, 9, "_cachedDevServerURL"], [19, 30, 37, 28], [19, 34, 37, 32, "FALLBACK"], [19, 42, 37, 40], [20, 6, 38, 4, "fullBundleUrl"], [20, 19, 38, 17], [20, 21, 38, 19, "_cachedFullBundleURL"], [20, 41, 38, 39], [21, 6, 39, 4, "bundleLoadedFromServer"], [21, 28, 39, 26], [21, 30, 39, 28, "_cachedDevServerURL"], [21, 49, 39, 47], [21, 54, 39, 52], [22, 4, 40, 2], [22, 5, 40, 3], [23, 2, 41, 0], [24, 0, 41, 1], [24, 3]], "functionMap": {"names": ["<global>", "getDevServer"], "mappings": "AAA;eC2B"}}, "type": "js/module"}]}