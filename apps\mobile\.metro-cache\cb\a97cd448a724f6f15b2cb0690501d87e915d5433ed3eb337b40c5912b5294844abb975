{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../Core/Devtools/openFileInEditor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 68}}], "key": "3vH1y5vrRpr8TQTBUBKQt4U0X5I=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "../../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 48}}], "key": "/m0HqCpVZ4yItbJJaw+YeR/qFWU=", "exportNames": ["*"]}}, {"name": "./LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 42}}], "key": "M6ofQu070ZUTf+Oq+Zz+7FQEgjs=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorSection", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 62}}], "key": "psfwCNco8+nKb+3u3V4A+OhHW2E=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../../Components/View/View\"));\n  var _openFileInEditor = _interopRequireDefault(require(_dependencyMap[3], \"../../Core/Devtools/openFileInEditor\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[5], \"../../Text/Text\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[6], \"../../Utilities/Platform\"));\n  var _LogBoxButton = _interopRequireDefault(require(_dependencyMap[7], \"./LogBoxButton\"));\n  var _LogBoxInspectorSection = _interopRequireDefault(require(_dependencyMap[8], \"./LogBoxInspectorSection\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[9], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[10], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[11], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxInspectorReactFrames.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var BEFORE_SLASH_RE = /^(.*)[\\\\/]/;\n  function getPrettyFileName(path) {\n    var fileName = path.replace(BEFORE_SLASH_RE, '');\n    if (/^index\\./.test(fileName)) {\n      var match = path.match(BEFORE_SLASH_RE);\n      if (match) {\n        var pathBeforeSlash = match[1];\n        if (pathBeforeSlash) {\n          var folderName = pathBeforeSlash.replace(BEFORE_SLASH_RE, '');\n          fileName = folderName + '/​' + fileName;\n        }\n      }\n    }\n    return fileName;\n  }\n  function LogBoxInspectorReactFrames(props) {\n    var _React$useState = React.useState(true),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      collapsed = _React$useState2[0],\n      setCollapsed = _React$useState2[1];\n    if (props.log.getAvailableComponentStack() == null || props.log.getAvailableComponentStack().length < 1) {\n      return null;\n    }\n    function getStackList() {\n      if (collapsed) {\n        return props.log.getAvailableComponentStack().slice(0, 3);\n      } else {\n        return props.log.getAvailableComponentStack();\n      }\n    }\n    function getCollapseMessage() {\n      if (props.log.getAvailableComponentStack().length <= 3) {\n        return;\n      }\n      var count = props.log.getAvailableComponentStack().length - 3;\n      if (collapsed) {\n        return `See ${count} more components`;\n      } else {\n        return `Collapse ${count} components`;\n      }\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorSection.default, {\n      heading: \"Component Stack\",\n      children: [getStackList().map((frame, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: componentStyles.frameContainer,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxButton.default, {\n          backgroundColor: {\n            default: 'transparent',\n            pressed: LogBoxStyle.getBackgroundColor(1)\n          },\n          onPress: frame.fileName.startsWith('/') ? () => (0, _openFileInEditor.default)(frame.fileName, frame.location?.row ?? 1) : null,\n          style: componentStyles.frame,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: componentStyles.component,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              id: \"logbox_component_stack_frame_text\",\n              style: componentStyles.frameName,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: componentStyles.bracket,\n                children: '<'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), frame.content, /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: componentStyles.bracket,\n                children: ' />'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: componentStyles.frameLocation,\n            children: [getPrettyFileName(frame.fileName), frame.location ? `:${frame.location.row}` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: componentStyles.collapseContainer,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxButton.default, {\n          backgroundColor: {\n            default: 'transparent',\n            pressed: LogBoxStyle.getBackgroundColor(1)\n          },\n          onPress: () => setCollapsed(!collapsed),\n          style: componentStyles.collapseButton,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: componentStyles.collapse,\n            children: getCollapseMessage()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 5\n    }, this);\n  }\n  var componentStyles = _StyleSheet.default.create({\n    collapseContainer: {\n      marginLeft: 15,\n      flexDirection: 'row'\n    },\n    collapseButton: {\n      borderRadius: 5\n    },\n    collapse: {\n      color: LogBoxStyle.getTextColor(0.7),\n      fontSize: 12,\n      fontWeight: '300',\n      lineHeight: 20,\n      marginTop: 0,\n      paddingVertical: 5,\n      paddingHorizontal: 10\n    },\n    frameContainer: {\n      flexDirection: 'row',\n      paddingHorizontal: 15\n    },\n    frame: {\n      flex: 1,\n      paddingVertical: 4,\n      paddingHorizontal: 10,\n      borderRadius: 5\n    },\n    component: {\n      flexDirection: 'row',\n      paddingRight: 10\n    },\n    frameName: {\n      fontFamily: _Platform.default.select({\n        android: 'monospace',\n        ios: 'Menlo'\n      }),\n      color: LogBoxStyle.getTextColor(1),\n      fontSize: 14,\n      includeFontPadding: false,\n      lineHeight: 18\n    },\n    bracket: {\n      fontFamily: _Platform.default.select({\n        android: 'monospace',\n        ios: 'Menlo'\n      }),\n      color: LogBoxStyle.getTextColor(0.4),\n      fontSize: 14,\n      fontWeight: '500',\n      includeFontPadding: false,\n      lineHeight: 18\n    },\n    frameLocation: {\n      color: LogBoxStyle.getTextColor(0.7),\n      fontSize: 12,\n      fontWeight: '300',\n      includeFontPadding: false,\n      lineHeight: 16,\n      paddingLeft: 10\n    }\n  });\n  var _default = exports.default = LogBoxInspectorReactFrames;\n});", "lineCount": 213, "map": [[8, 2, 13, 0], [8, 6, 13, 0, "_View"], [8, 11, 13, 0], [8, 14, 13, 0, "_interopRequireDefault"], [8, 36, 13, 0], [8, 37, 13, 0, "require"], [8, 44, 13, 0], [8, 45, 13, 0, "_dependencyMap"], [8, 59, 13, 0], [9, 2, 14, 0], [9, 6, 14, 0, "_openFileInEditor"], [9, 23, 14, 0], [9, 26, 14, 0, "_interopRequireDefault"], [9, 48, 14, 0], [9, 49, 14, 0, "require"], [9, 56, 14, 0], [9, 57, 14, 0, "_dependencyMap"], [9, 71, 14, 0], [10, 2, 15, 0], [10, 6, 15, 0, "_StyleSheet"], [10, 17, 15, 0], [10, 20, 15, 0, "_interopRequireDefault"], [10, 42, 15, 0], [10, 43, 15, 0, "require"], [10, 50, 15, 0], [10, 51, 15, 0, "_dependencyMap"], [10, 65, 15, 0], [11, 2, 16, 0], [11, 6, 16, 0, "_Text"], [11, 11, 16, 0], [11, 14, 16, 0, "_interopRequireDefault"], [11, 36, 16, 0], [11, 37, 16, 0, "require"], [11, 44, 16, 0], [11, 45, 16, 0, "_dependencyMap"], [11, 59, 16, 0], [12, 2, 17, 0], [12, 6, 17, 0, "_Platform"], [12, 15, 17, 0], [12, 18, 17, 0, "_interopRequireDefault"], [12, 40, 17, 0], [12, 41, 17, 0, "require"], [12, 48, 17, 0], [12, 49, 17, 0, "_dependencyMap"], [12, 63, 17, 0], [13, 2, 18, 0], [13, 6, 18, 0, "_LogBoxButton"], [13, 19, 18, 0], [13, 22, 18, 0, "_interopRequireDefault"], [13, 44, 18, 0], [13, 45, 18, 0, "require"], [13, 52, 18, 0], [13, 53, 18, 0, "_dependencyMap"], [13, 67, 18, 0], [14, 2, 19, 0], [14, 6, 19, 0, "_LogBoxInspectorSection"], [14, 29, 19, 0], [14, 32, 19, 0, "_interopRequireDefault"], [14, 54, 19, 0], [14, 55, 19, 0, "require"], [14, 62, 19, 0], [14, 63, 19, 0, "_dependencyMap"], [14, 77, 19, 0], [15, 2, 20, 0], [15, 6, 20, 0, "LogBoxStyle"], [15, 17, 20, 0], [15, 20, 20, 0, "_interopRequireWildcard"], [15, 43, 20, 0], [15, 44, 20, 0, "require"], [15, 51, 20, 0], [15, 52, 20, 0, "_dependencyMap"], [15, 66, 20, 0], [16, 2, 21, 0], [16, 6, 21, 0, "React"], [16, 11, 21, 0], [16, 14, 21, 0, "_interopRequireWildcard"], [16, 37, 21, 0], [16, 38, 21, 0, "require"], [16, 45, 21, 0], [16, 46, 21, 0, "_dependencyMap"], [16, 60, 21, 0], [17, 2, 21, 31], [17, 6, 21, 31, "_jsxDevRuntime"], [17, 20, 21, 31], [17, 23, 21, 31, "require"], [17, 30, 21, 31], [17, 31, 21, 31, "_dependencyMap"], [17, 45, 21, 31], [18, 2, 21, 31], [18, 6, 21, 31, "_jsxFileName"], [18, 18, 21, 31], [19, 2, 21, 31], [19, 11, 21, 31, "_interopRequireWildcard"], [19, 35, 21, 31, "e"], [19, 36, 21, 31], [19, 38, 21, 31, "t"], [19, 39, 21, 31], [19, 68, 21, 31, "WeakMap"], [19, 75, 21, 31], [19, 81, 21, 31, "r"], [19, 82, 21, 31], [19, 89, 21, 31, "WeakMap"], [19, 96, 21, 31], [19, 100, 21, 31, "n"], [19, 101, 21, 31], [19, 108, 21, 31, "WeakMap"], [19, 115, 21, 31], [19, 127, 21, 31, "_interopRequireWildcard"], [19, 150, 21, 31], [19, 162, 21, 31, "_interopRequireWildcard"], [19, 163, 21, 31, "e"], [19, 164, 21, 31], [19, 166, 21, 31, "t"], [19, 167, 21, 31], [19, 176, 21, 31, "t"], [19, 177, 21, 31], [19, 181, 21, 31, "e"], [19, 182, 21, 31], [19, 186, 21, 31, "e"], [19, 187, 21, 31], [19, 188, 21, 31, "__esModule"], [19, 198, 21, 31], [19, 207, 21, 31, "e"], [19, 208, 21, 31], [19, 214, 21, 31, "o"], [19, 215, 21, 31], [19, 217, 21, 31, "i"], [19, 218, 21, 31], [19, 220, 21, 31, "f"], [19, 221, 21, 31], [19, 226, 21, 31, "__proto__"], [19, 235, 21, 31], [19, 243, 21, 31, "default"], [19, 250, 21, 31], [19, 252, 21, 31, "e"], [19, 253, 21, 31], [19, 270, 21, 31, "e"], [19, 271, 21, 31], [19, 294, 21, 31, "e"], [19, 295, 21, 31], [19, 320, 21, 31, "e"], [19, 321, 21, 31], [19, 330, 21, 31, "f"], [19, 331, 21, 31], [19, 337, 21, 31, "o"], [19, 338, 21, 31], [19, 341, 21, 31, "t"], [19, 342, 21, 31], [19, 345, 21, 31, "n"], [19, 346, 21, 31], [19, 349, 21, 31, "r"], [19, 350, 21, 31], [19, 358, 21, 31, "o"], [19, 359, 21, 31], [19, 360, 21, 31, "has"], [19, 363, 21, 31], [19, 364, 21, 31, "e"], [19, 365, 21, 31], [19, 375, 21, 31, "o"], [19, 376, 21, 31], [19, 377, 21, 31, "get"], [19, 380, 21, 31], [19, 381, 21, 31, "e"], [19, 382, 21, 31], [19, 385, 21, 31, "o"], [19, 386, 21, 31], [19, 387, 21, 31, "set"], [19, 390, 21, 31], [19, 391, 21, 31, "e"], [19, 392, 21, 31], [19, 394, 21, 31, "f"], [19, 395, 21, 31], [19, 409, 21, 31, "_t"], [19, 411, 21, 31], [19, 415, 21, 31, "e"], [19, 416, 21, 31], [19, 432, 21, 31, "_t"], [19, 434, 21, 31], [19, 441, 21, 31, "hasOwnProperty"], [19, 455, 21, 31], [19, 456, 21, 31, "call"], [19, 460, 21, 31], [19, 461, 21, 31, "e"], [19, 462, 21, 31], [19, 464, 21, 31, "_t"], [19, 466, 21, 31], [19, 473, 21, 31, "i"], [19, 474, 21, 31], [19, 478, 21, 31, "o"], [19, 479, 21, 31], [19, 482, 21, 31, "Object"], [19, 488, 21, 31], [19, 489, 21, 31, "defineProperty"], [19, 503, 21, 31], [19, 508, 21, 31, "Object"], [19, 514, 21, 31], [19, 515, 21, 31, "getOwnPropertyDescriptor"], [19, 539, 21, 31], [19, 540, 21, 31, "e"], [19, 541, 21, 31], [19, 543, 21, 31, "_t"], [19, 545, 21, 31], [19, 552, 21, 31, "i"], [19, 553, 21, 31], [19, 554, 21, 31, "get"], [19, 557, 21, 31], [19, 561, 21, 31, "i"], [19, 562, 21, 31], [19, 563, 21, 31, "set"], [19, 566, 21, 31], [19, 570, 21, 31, "o"], [19, 571, 21, 31], [19, 572, 21, 31, "f"], [19, 573, 21, 31], [19, 575, 21, 31, "_t"], [19, 577, 21, 31], [19, 579, 21, 31, "i"], [19, 580, 21, 31], [19, 584, 21, 31, "f"], [19, 585, 21, 31], [19, 586, 21, 31, "_t"], [19, 588, 21, 31], [19, 592, 21, 31, "e"], [19, 593, 21, 31], [19, 594, 21, 31, "_t"], [19, 596, 21, 31], [19, 607, 21, 31, "f"], [19, 608, 21, 31], [19, 613, 21, 31, "e"], [19, 614, 21, 31], [19, 616, 21, 31, "t"], [19, 617, 21, 31], [20, 2, 27, 0], [20, 6, 27, 6, "BEFORE_SLASH_RE"], [20, 21, 27, 21], [20, 24, 27, 24], [20, 36, 27, 36], [21, 2, 30, 0], [21, 11, 30, 9, "getPrettyFileName"], [21, 28, 30, 26, "getPrettyFileName"], [21, 29, 30, 27, "path"], [21, 33, 30, 39], [21, 35, 30, 41], [22, 4, 31, 2], [22, 8, 31, 6, "fileName"], [22, 16, 31, 14], [22, 19, 31, 17, "path"], [22, 23, 31, 21], [22, 24, 31, 22, "replace"], [22, 31, 31, 29], [22, 32, 31, 30, "BEFORE_SLASH_RE"], [22, 47, 31, 45], [22, 49, 31, 47], [22, 51, 31, 49], [22, 52, 31, 50], [23, 4, 35, 2], [23, 8, 35, 6], [23, 18, 35, 16], [23, 19, 35, 17, "test"], [23, 23, 35, 21], [23, 24, 35, 22, "fileName"], [23, 32, 35, 30], [23, 33, 35, 31], [23, 35, 35, 33], [24, 6, 36, 4], [24, 10, 36, 10, "match"], [24, 15, 36, 15], [24, 18, 36, 18, "path"], [24, 22, 36, 22], [24, 23, 36, 23, "match"], [24, 28, 36, 28], [24, 29, 36, 29, "BEFORE_SLASH_RE"], [24, 44, 36, 44], [24, 45, 36, 45], [25, 6, 37, 4], [25, 10, 37, 8, "match"], [25, 15, 37, 13], [25, 17, 37, 15], [26, 8, 38, 6], [26, 12, 38, 12, "pathBeforeSlash"], [26, 27, 38, 27], [26, 30, 38, 30, "match"], [26, 35, 38, 35], [26, 36, 38, 36], [26, 37, 38, 37], [26, 38, 38, 38], [27, 8, 39, 6], [27, 12, 39, 10, "pathBeforeSlash"], [27, 27, 39, 25], [27, 29, 39, 27], [28, 10, 40, 8], [28, 14, 40, 14, "folderName"], [28, 24, 40, 24], [28, 27, 40, 27, "pathBeforeSlash"], [28, 42, 40, 42], [28, 43, 40, 43, "replace"], [28, 50, 40, 50], [28, 51, 40, 51, "BEFORE_SLASH_RE"], [28, 66, 40, 66], [28, 68, 40, 68], [28, 70, 40, 70], [28, 71, 40, 71], [29, 10, 44, 8, "fileName"], [29, 18, 44, 16], [29, 21, 44, 19, "folderName"], [29, 31, 44, 29], [29, 34, 44, 32], [29, 38, 44, 36], [29, 41, 44, 39, "fileName"], [29, 49, 44, 47], [30, 8, 45, 6], [31, 6, 46, 4], [32, 4, 47, 2], [33, 4, 49, 2], [33, 11, 49, 9, "fileName"], [33, 19, 49, 17], [34, 2, 50, 0], [35, 2, 51, 0], [35, 11, 51, 9, "LogBoxInspectorReactFrames"], [35, 37, 51, 35, "LogBoxInspectorReactFrames"], [35, 38, 51, 36, "props"], [35, 43, 51, 48], [35, 45, 51, 62], [36, 4, 52, 2], [36, 8, 52, 2, "_React$useState"], [36, 23, 52, 2], [36, 26, 52, 36, "React"], [36, 31, 52, 41], [36, 32, 52, 42, "useState"], [36, 40, 52, 50], [36, 41, 52, 51], [36, 45, 52, 55], [36, 46, 52, 56], [37, 6, 52, 56, "_React$useState2"], [37, 22, 52, 56], [37, 29, 52, 56, "_slicedToArray2"], [37, 44, 52, 56], [37, 45, 52, 56, "default"], [37, 52, 52, 56], [37, 54, 52, 56, "_React$useState"], [37, 69, 52, 56], [38, 6, 52, 9, "collapsed"], [38, 15, 52, 18], [38, 18, 52, 18, "_React$useState2"], [38, 34, 52, 18], [39, 6, 52, 20, "setCollapsed"], [39, 18, 52, 32], [39, 21, 52, 32, "_React$useState2"], [39, 37, 52, 32], [40, 4, 53, 2], [40, 8, 54, 4, "props"], [40, 13, 54, 9], [40, 14, 54, 10, "log"], [40, 17, 54, 13], [40, 18, 54, 14, "getAvailableComponentStack"], [40, 44, 54, 40], [40, 45, 54, 41], [40, 46, 54, 42], [40, 50, 54, 46], [40, 54, 54, 50], [40, 58, 55, 4, "props"], [40, 63, 55, 9], [40, 64, 55, 10, "log"], [40, 67, 55, 13], [40, 68, 55, 14, "getAvailableComponentStack"], [40, 94, 55, 40], [40, 95, 55, 41], [40, 96, 55, 42], [40, 97, 55, 43, "length"], [40, 103, 55, 49], [40, 106, 55, 52], [40, 107, 55, 53], [40, 109, 56, 4], [41, 6, 57, 4], [41, 13, 57, 11], [41, 17, 57, 15], [42, 4, 58, 2], [43, 4, 60, 2], [43, 13, 60, 11, "getStackList"], [43, 25, 60, 23, "getStackList"], [43, 26, 60, 23], [43, 28, 60, 26], [44, 6, 61, 4], [44, 10, 61, 8, "collapsed"], [44, 19, 61, 17], [44, 21, 61, 19], [45, 8, 62, 6], [45, 15, 62, 13, "props"], [45, 20, 62, 18], [45, 21, 62, 19, "log"], [45, 24, 62, 22], [45, 25, 62, 23, "getAvailableComponentStack"], [45, 51, 62, 49], [45, 52, 62, 50], [45, 53, 62, 51], [45, 54, 62, 52, "slice"], [45, 59, 62, 57], [45, 60, 62, 58], [45, 61, 62, 59], [45, 63, 62, 61], [45, 64, 62, 62], [45, 65, 62, 63], [46, 6, 63, 4], [46, 7, 63, 5], [46, 13, 63, 11], [47, 8, 64, 6], [47, 15, 64, 13, "props"], [47, 20, 64, 18], [47, 21, 64, 19, "log"], [47, 24, 64, 22], [47, 25, 64, 23, "getAvailableComponentStack"], [47, 51, 64, 49], [47, 52, 64, 50], [47, 53, 64, 51], [48, 6, 65, 4], [49, 4, 66, 2], [50, 4, 68, 2], [50, 13, 68, 11, "getCollapseMessage"], [50, 31, 68, 29, "getCollapseMessage"], [50, 32, 68, 29], [50, 34, 68, 32], [51, 6, 69, 4], [51, 10, 69, 8, "props"], [51, 15, 69, 13], [51, 16, 69, 14, "log"], [51, 19, 69, 17], [51, 20, 69, 18, "getAvailableComponentStack"], [51, 46, 69, 44], [51, 47, 69, 45], [51, 48, 69, 46], [51, 49, 69, 47, "length"], [51, 55, 69, 53], [51, 59, 69, 57], [51, 60, 69, 58], [51, 62, 69, 60], [52, 8, 70, 6], [53, 6, 71, 4], [54, 6, 73, 4], [54, 10, 73, 10, "count"], [54, 15, 73, 15], [54, 18, 73, 18, "props"], [54, 23, 73, 23], [54, 24, 73, 24, "log"], [54, 27, 73, 27], [54, 28, 73, 28, "getAvailableComponentStack"], [54, 54, 73, 54], [54, 55, 73, 55], [54, 56, 73, 56], [54, 57, 73, 57, "length"], [54, 63, 73, 63], [54, 66, 73, 66], [54, 67, 73, 67], [55, 6, 74, 4], [55, 10, 74, 8, "collapsed"], [55, 19, 74, 17], [55, 21, 74, 19], [56, 8, 75, 6], [56, 15, 75, 13], [56, 22, 75, 20, "count"], [56, 27, 75, 25], [56, 45, 75, 43], [57, 6, 76, 4], [57, 7, 76, 5], [57, 13, 76, 11], [58, 8, 77, 6], [58, 15, 77, 13], [58, 27, 77, 25, "count"], [58, 32, 77, 30], [58, 45, 77, 43], [59, 6, 78, 4], [60, 4, 79, 2], [61, 4, 81, 2], [61, 24, 82, 4], [61, 28, 82, 4, "_jsxDevRuntime"], [61, 42, 82, 4], [61, 43, 82, 4, "jsxDEV"], [61, 49, 82, 4], [61, 51, 82, 5, "_LogBoxInspectorSection"], [61, 74, 82, 5], [61, 75, 82, 5, "default"], [61, 82, 82, 27], [62, 6, 82, 28, "heading"], [62, 13, 82, 35], [62, 15, 82, 36], [62, 32, 82, 53], [63, 6, 82, 53, "children"], [63, 14, 82, 53], [63, 17, 83, 7, "getStackList"], [63, 29, 83, 19], [63, 30, 83, 20], [63, 31, 83, 21], [63, 32, 83, 22, "map"], [63, 35, 83, 25], [63, 36, 83, 26], [63, 37, 83, 27, "frame"], [63, 42, 83, 32], [63, 44, 83, 34, "index"], [63, 49, 83, 39], [63, 67, 84, 8], [63, 71, 84, 8, "_jsxDevRuntime"], [63, 85, 84, 8], [63, 86, 84, 8, "jsxDEV"], [63, 92, 84, 8], [63, 94, 84, 9, "_View"], [63, 99, 84, 9], [63, 100, 84, 9, "default"], [63, 107, 84, 13], [64, 8, 87, 10, "style"], [64, 13, 87, 15], [64, 15, 87, 17, "componentStyles"], [64, 30, 87, 32], [64, 31, 87, 33, "frameContainer"], [64, 45, 87, 48], [65, 8, 87, 48, "children"], [65, 16, 87, 48], [65, 31, 88, 10], [65, 35, 88, 10, "_jsxDevRuntime"], [65, 49, 88, 10], [65, 50, 88, 10, "jsxDEV"], [65, 56, 88, 10], [65, 58, 88, 11, "_LogBoxButton"], [65, 71, 88, 11], [65, 72, 88, 11, "default"], [65, 79, 88, 23], [66, 10, 89, 12, "backgroundColor"], [66, 25, 89, 27], [66, 27, 89, 29], [67, 12, 90, 14, "default"], [67, 19, 90, 21], [67, 21, 90, 23], [67, 34, 90, 36], [68, 12, 91, 14, "pressed"], [68, 19, 91, 21], [68, 21, 91, 23, "LogBoxStyle"], [68, 32, 91, 34], [68, 33, 91, 35, "getBackgroundColor"], [68, 51, 91, 53], [68, 52, 91, 54], [68, 53, 91, 55], [69, 10, 92, 12], [69, 11, 92, 14], [70, 10, 93, 12, "onPress"], [70, 17, 93, 19], [70, 19, 97, 14, "frame"], [70, 24, 97, 19], [70, 25, 97, 20, "fileName"], [70, 33, 97, 28], [70, 34, 97, 29, "startsWith"], [70, 44, 97, 39], [70, 45, 97, 40], [70, 48, 97, 43], [70, 49, 97, 44], [70, 52, 98, 18], [70, 58, 99, 20], [70, 62, 99, 20, "openFileInEditor"], [70, 87, 99, 36], [70, 89, 99, 37, "frame"], [70, 94, 99, 42], [70, 95, 99, 43, "fileName"], [70, 103, 99, 51], [70, 105, 99, 53, "frame"], [70, 110, 99, 58], [70, 111, 99, 59, "location"], [70, 119, 99, 67], [70, 121, 99, 69, "row"], [70, 124, 99, 72], [70, 128, 99, 76], [70, 129, 99, 77], [70, 130, 99, 78], [70, 133, 100, 18], [70, 137, 101, 13], [71, 10, 102, 12, "style"], [71, 15, 102, 17], [71, 17, 102, 19, "componentStyles"], [71, 32, 102, 34], [71, 33, 102, 35, "frame"], [71, 38, 102, 41], [72, 10, 102, 41, "children"], [72, 18, 102, 41], [72, 34, 103, 12], [72, 38, 103, 12, "_jsxDevRuntime"], [72, 52, 103, 12], [72, 53, 103, 12, "jsxDEV"], [72, 59, 103, 12], [72, 61, 103, 13, "_View"], [72, 66, 103, 13], [72, 67, 103, 13, "default"], [72, 74, 103, 17], [73, 12, 103, 18, "style"], [73, 17, 103, 23], [73, 19, 103, 25, "componentStyles"], [73, 34, 103, 40], [73, 35, 103, 41, "component"], [73, 44, 103, 51], [74, 12, 103, 51, "children"], [74, 20, 103, 51], [74, 35, 104, 14], [74, 39, 104, 14, "_jsxDevRuntime"], [74, 53, 104, 14], [74, 54, 104, 14, "jsxDEV"], [74, 60, 104, 14], [74, 62, 104, 15, "_Text"], [74, 67, 104, 15], [74, 68, 104, 15, "default"], [74, 75, 104, 19], [75, 14, 105, 16, "id"], [75, 16, 105, 18], [75, 18, 105, 19], [75, 53, 105, 54], [76, 14, 106, 16, "style"], [76, 19, 106, 21], [76, 21, 106, 23, "componentStyles"], [76, 36, 106, 38], [76, 37, 106, 39, "frameName"], [76, 46, 106, 49], [77, 14, 106, 49, "children"], [77, 22, 106, 49], [77, 38, 107, 16], [77, 42, 107, 16, "_jsxDevRuntime"], [77, 56, 107, 16], [77, 57, 107, 16, "jsxDEV"], [77, 63, 107, 16], [77, 65, 107, 17, "_Text"], [77, 70, 107, 17], [77, 71, 107, 17, "default"], [77, 78, 107, 21], [78, 16, 107, 22, "style"], [78, 21, 107, 27], [78, 23, 107, 29, "componentStyles"], [78, 38, 107, 44], [78, 39, 107, 45, "bracket"], [78, 46, 107, 53], [79, 16, 107, 53, "children"], [79, 24, 107, 53], [79, 26, 107, 55], [80, 14, 107, 58], [81, 16, 107, 58, "fileName"], [81, 24, 107, 58], [81, 26, 107, 58, "_jsxFileName"], [81, 38, 107, 58], [82, 16, 107, 58, "lineNumber"], [82, 26, 107, 58], [83, 16, 107, 58, "columnNumber"], [83, 28, 107, 58], [84, 14, 107, 58], [84, 21, 107, 65], [84, 22, 107, 66], [84, 24, 108, 17, "frame"], [84, 29, 108, 22], [84, 30, 108, 23, "content"], [84, 37, 108, 30], [84, 52, 109, 16], [84, 56, 109, 16, "_jsxDevRuntime"], [84, 70, 109, 16], [84, 71, 109, 16, "jsxDEV"], [84, 77, 109, 16], [84, 79, 109, 17, "_Text"], [84, 84, 109, 17], [84, 85, 109, 17, "default"], [84, 92, 109, 21], [85, 16, 109, 22, "style"], [85, 21, 109, 27], [85, 23, 109, 29, "componentStyles"], [85, 38, 109, 44], [85, 39, 109, 45, "bracket"], [85, 46, 109, 53], [86, 16, 109, 53, "children"], [86, 24, 109, 53], [86, 26, 109, 55], [87, 14, 109, 60], [88, 16, 109, 60, "fileName"], [88, 24, 109, 60], [88, 26, 109, 60, "_jsxFileName"], [88, 38, 109, 60], [89, 16, 109, 60, "lineNumber"], [89, 26, 109, 60], [90, 16, 109, 60, "columnNumber"], [90, 28, 109, 60], [91, 14, 109, 60], [91, 21, 109, 67], [91, 22, 109, 68], [92, 12, 109, 68], [93, 14, 109, 68, "fileName"], [93, 22, 109, 68], [93, 24, 109, 68, "_jsxFileName"], [93, 36, 109, 68], [94, 14, 109, 68, "lineNumber"], [94, 24, 109, 68], [95, 14, 109, 68, "columnNumber"], [95, 26, 109, 68], [96, 12, 109, 68], [96, 19, 110, 20], [97, 10, 110, 21], [98, 12, 110, 21, "fileName"], [98, 20, 110, 21], [98, 22, 110, 21, "_jsxFileName"], [98, 34, 110, 21], [99, 12, 110, 21, "lineNumber"], [99, 22, 110, 21], [100, 12, 110, 21, "columnNumber"], [100, 24, 110, 21], [101, 10, 110, 21], [101, 17, 111, 18], [101, 18, 111, 19], [101, 33, 112, 12], [101, 37, 112, 12, "_jsxDevRuntime"], [101, 51, 112, 12], [101, 52, 112, 12, "jsxDEV"], [101, 58, 112, 12], [101, 60, 112, 13, "_Text"], [101, 65, 112, 13], [101, 66, 112, 13, "default"], [101, 73, 112, 17], [102, 12, 112, 18, "style"], [102, 17, 112, 23], [102, 19, 112, 25, "componentStyles"], [102, 34, 112, 40], [102, 35, 112, 41, "frameLocation"], [102, 48, 112, 55], [103, 12, 112, 55, "children"], [103, 20, 112, 55], [103, 23, 113, 15, "getPrettyFileName"], [103, 40, 113, 32], [103, 41, 113, 33, "frame"], [103, 46, 113, 38], [103, 47, 113, 39, "fileName"], [103, 55, 113, 47], [103, 56, 113, 48], [103, 58, 114, 15, "frame"], [103, 63, 114, 20], [103, 64, 114, 21, "location"], [103, 72, 114, 29], [103, 75, 114, 32], [103, 79, 114, 36, "frame"], [103, 84, 114, 41], [103, 85, 114, 42, "location"], [103, 93, 114, 50], [103, 94, 114, 51, "row"], [103, 97, 114, 54], [103, 99, 114, 56], [103, 102, 114, 59], [103, 104, 114, 61], [104, 10, 114, 61], [105, 12, 114, 61, "fileName"], [105, 20, 114, 61], [105, 22, 114, 61, "_jsxFileName"], [105, 34, 114, 61], [106, 12, 114, 61, "lineNumber"], [106, 22, 114, 61], [107, 12, 114, 61, "columnNumber"], [107, 24, 114, 61], [108, 10, 114, 61], [108, 17, 115, 18], [108, 18, 115, 19], [109, 8, 115, 19], [110, 10, 115, 19, "fileName"], [110, 18, 115, 19], [110, 20, 115, 19, "_jsxFileName"], [110, 32, 115, 19], [111, 10, 115, 19, "lineNumber"], [111, 20, 115, 19], [112, 10, 115, 19, "columnNumber"], [112, 22, 115, 19], [113, 8, 115, 19], [113, 15, 116, 24], [114, 6, 116, 25], [114, 9, 86, 15, "index"], [114, 14, 86, 20], [115, 8, 86, 20, "fileName"], [115, 16, 86, 20], [115, 18, 86, 20, "_jsxFileName"], [115, 30, 86, 20], [116, 8, 86, 20, "lineNumber"], [116, 18, 86, 20], [117, 8, 86, 20, "columnNumber"], [117, 20, 86, 20], [118, 6, 86, 20], [118, 13, 117, 14], [118, 14, 118, 7], [118, 15, 118, 8], [118, 30, 119, 6], [118, 34, 119, 6, "_jsxDevRuntime"], [118, 48, 119, 6], [118, 49, 119, 6, "jsxDEV"], [118, 55, 119, 6], [118, 57, 119, 7, "_View"], [118, 62, 119, 7], [118, 63, 119, 7, "default"], [118, 70, 119, 11], [119, 8, 119, 12, "style"], [119, 13, 119, 17], [119, 15, 119, 19, "componentStyles"], [119, 30, 119, 34], [119, 31, 119, 35, "collapseContainer"], [119, 48, 119, 53], [120, 8, 119, 53, "children"], [120, 16, 119, 53], [120, 31, 120, 8], [120, 35, 120, 8, "_jsxDevRuntime"], [120, 49, 120, 8], [120, 50, 120, 8, "jsxDEV"], [120, 56, 120, 8], [120, 58, 120, 9, "_LogBoxButton"], [120, 71, 120, 9], [120, 72, 120, 9, "default"], [120, 79, 120, 21], [121, 10, 121, 10, "backgroundColor"], [121, 25, 121, 25], [121, 27, 121, 27], [122, 12, 122, 12, "default"], [122, 19, 122, 19], [122, 21, 122, 21], [122, 34, 122, 34], [123, 12, 123, 12, "pressed"], [123, 19, 123, 19], [123, 21, 123, 21, "LogBoxStyle"], [123, 32, 123, 32], [123, 33, 123, 33, "getBackgroundColor"], [123, 51, 123, 51], [123, 52, 123, 52], [123, 53, 123, 53], [124, 10, 124, 10], [124, 11, 124, 12], [125, 10, 125, 10, "onPress"], [125, 17, 125, 17], [125, 19, 125, 19, "onPress"], [125, 20, 125, 19], [125, 25, 125, 25, "setCollapsed"], [125, 37, 125, 37], [125, 38, 125, 38], [125, 39, 125, 39, "collapsed"], [125, 48, 125, 48], [125, 49, 125, 50], [126, 10, 126, 10, "style"], [126, 15, 126, 15], [126, 17, 126, 17, "componentStyles"], [126, 32, 126, 32], [126, 33, 126, 33, "collapseButton"], [126, 47, 126, 48], [127, 10, 126, 48, "children"], [127, 18, 126, 48], [127, 33, 127, 10], [127, 37, 127, 10, "_jsxDevRuntime"], [127, 51, 127, 10], [127, 52, 127, 10, "jsxDEV"], [127, 58, 127, 10], [127, 60, 127, 11, "_Text"], [127, 65, 127, 11], [127, 66, 127, 11, "default"], [127, 73, 127, 15], [128, 12, 127, 16, "style"], [128, 17, 127, 21], [128, 19, 127, 23, "componentStyles"], [128, 34, 127, 38], [128, 35, 127, 39, "collapse"], [128, 43, 127, 48], [129, 12, 127, 48, "children"], [129, 20, 127, 48], [129, 22, 127, 50, "getCollapseMessage"], [129, 40, 127, 68], [129, 41, 127, 69], [130, 10, 127, 70], [131, 12, 127, 70, "fileName"], [131, 20, 127, 70], [131, 22, 127, 70, "_jsxFileName"], [131, 34, 127, 70], [132, 12, 127, 70, "lineNumber"], [132, 22, 127, 70], [133, 12, 127, 70, "columnNumber"], [133, 24, 127, 70], [134, 10, 127, 70], [134, 17, 127, 77], [135, 8, 127, 78], [136, 10, 127, 78, "fileName"], [136, 18, 127, 78], [136, 20, 127, 78, "_jsxFileName"], [136, 32, 127, 78], [137, 10, 127, 78, "lineNumber"], [137, 20, 127, 78], [138, 10, 127, 78, "columnNumber"], [138, 22, 127, 78], [139, 8, 127, 78], [139, 15, 128, 22], [140, 6, 128, 23], [141, 8, 128, 23, "fileName"], [141, 16, 128, 23], [141, 18, 128, 23, "_jsxFileName"], [141, 30, 128, 23], [142, 8, 128, 23, "lineNumber"], [142, 18, 128, 23], [143, 8, 128, 23, "columnNumber"], [143, 20, 128, 23], [144, 6, 128, 23], [144, 13, 129, 12], [144, 14, 129, 13], [145, 4, 129, 13], [146, 6, 129, 13, "fileName"], [146, 14, 129, 13], [146, 16, 129, 13, "_jsxFileName"], [146, 28, 129, 13], [147, 6, 129, 13, "lineNumber"], [147, 16, 129, 13], [148, 6, 129, 13, "columnNumber"], [148, 18, 129, 13], [149, 4, 129, 13], [149, 11, 130, 28], [149, 12, 130, 29], [150, 2, 132, 0], [151, 2, 134, 0], [151, 6, 134, 6, "componentStyles"], [151, 21, 134, 21], [151, 24, 134, 24, "StyleSheet"], [151, 43, 134, 34], [151, 44, 134, 35, "create"], [151, 50, 134, 41], [151, 51, 134, 42], [152, 4, 135, 2, "collapseContainer"], [152, 21, 135, 19], [152, 23, 135, 21], [153, 6, 136, 4, "marginLeft"], [153, 16, 136, 14], [153, 18, 136, 16], [153, 20, 136, 18], [154, 6, 137, 4, "flexDirection"], [154, 19, 137, 17], [154, 21, 137, 19], [155, 4, 138, 2], [155, 5, 138, 3], [156, 4, 139, 2, "collapseButton"], [156, 18, 139, 16], [156, 20, 139, 18], [157, 6, 140, 4, "borderRadius"], [157, 18, 140, 16], [157, 20, 140, 18], [158, 4, 141, 2], [158, 5, 141, 3], [159, 4, 142, 2, "collapse"], [159, 12, 142, 10], [159, 14, 142, 12], [160, 6, 143, 4, "color"], [160, 11, 143, 9], [160, 13, 143, 11, "LogBoxStyle"], [160, 24, 143, 22], [160, 25, 143, 23, "getTextColor"], [160, 37, 143, 35], [160, 38, 143, 36], [160, 41, 143, 39], [160, 42, 143, 40], [161, 6, 144, 4, "fontSize"], [161, 14, 144, 12], [161, 16, 144, 14], [161, 18, 144, 16], [162, 6, 145, 4, "fontWeight"], [162, 16, 145, 14], [162, 18, 145, 16], [162, 23, 145, 21], [163, 6, 146, 4, "lineHeight"], [163, 16, 146, 14], [163, 18, 146, 16], [163, 20, 146, 18], [164, 6, 147, 4, "marginTop"], [164, 15, 147, 13], [164, 17, 147, 15], [164, 18, 147, 16], [165, 6, 148, 4, "paddingVertical"], [165, 21, 148, 19], [165, 23, 148, 21], [165, 24, 148, 22], [166, 6, 149, 4, "paddingHorizontal"], [166, 23, 149, 21], [166, 25, 149, 23], [167, 4, 150, 2], [167, 5, 150, 3], [168, 4, 151, 2, "frameContainer"], [168, 18, 151, 16], [168, 20, 151, 18], [169, 6, 152, 4, "flexDirection"], [169, 19, 152, 17], [169, 21, 152, 19], [169, 26, 152, 24], [170, 6, 153, 4, "paddingHorizontal"], [170, 23, 153, 21], [170, 25, 153, 23], [171, 4, 154, 2], [171, 5, 154, 3], [172, 4, 155, 2, "frame"], [172, 9, 155, 7], [172, 11, 155, 9], [173, 6, 156, 4, "flex"], [173, 10, 156, 8], [173, 12, 156, 10], [173, 13, 156, 11], [174, 6, 157, 4, "paddingVertical"], [174, 21, 157, 19], [174, 23, 157, 21], [174, 24, 157, 22], [175, 6, 158, 4, "paddingHorizontal"], [175, 23, 158, 21], [175, 25, 158, 23], [175, 27, 158, 25], [176, 6, 159, 4, "borderRadius"], [176, 18, 159, 16], [176, 20, 159, 18], [177, 4, 160, 2], [177, 5, 160, 3], [178, 4, 161, 2, "component"], [178, 13, 161, 11], [178, 15, 161, 13], [179, 6, 162, 4, "flexDirection"], [179, 19, 162, 17], [179, 21, 162, 19], [179, 26, 162, 24], [180, 6, 163, 4, "paddingRight"], [180, 18, 163, 16], [180, 20, 163, 18], [181, 4, 164, 2], [181, 5, 164, 3], [182, 4, 165, 2, "frameName"], [182, 13, 165, 11], [182, 15, 165, 13], [183, 6, 166, 4, "fontFamily"], [183, 16, 166, 14], [183, 18, 166, 16, "Platform"], [183, 35, 166, 24], [183, 36, 166, 25, "select"], [183, 42, 166, 31], [183, 43, 166, 32], [184, 8, 166, 33, "android"], [184, 15, 166, 40], [184, 17, 166, 42], [184, 28, 166, 53], [185, 8, 166, 55, "ios"], [185, 11, 166, 58], [185, 13, 166, 60], [186, 6, 166, 67], [186, 7, 166, 68], [186, 8, 166, 69], [187, 6, 167, 4, "color"], [187, 11, 167, 9], [187, 13, 167, 11, "LogBoxStyle"], [187, 24, 167, 22], [187, 25, 167, 23, "getTextColor"], [187, 37, 167, 35], [187, 38, 167, 36], [187, 39, 167, 37], [187, 40, 167, 38], [188, 6, 168, 4, "fontSize"], [188, 14, 168, 12], [188, 16, 168, 14], [188, 18, 168, 16], [189, 6, 169, 4, "includeFontPadding"], [189, 24, 169, 22], [189, 26, 169, 24], [189, 31, 169, 29], [190, 6, 170, 4, "lineHeight"], [190, 16, 170, 14], [190, 18, 170, 16], [191, 4, 171, 2], [191, 5, 171, 3], [192, 4, 172, 2, "bracket"], [192, 11, 172, 9], [192, 13, 172, 11], [193, 6, 173, 4, "fontFamily"], [193, 16, 173, 14], [193, 18, 173, 16, "Platform"], [193, 35, 173, 24], [193, 36, 173, 25, "select"], [193, 42, 173, 31], [193, 43, 173, 32], [194, 8, 173, 33, "android"], [194, 15, 173, 40], [194, 17, 173, 42], [194, 28, 173, 53], [195, 8, 173, 55, "ios"], [195, 11, 173, 58], [195, 13, 173, 60], [196, 6, 173, 67], [196, 7, 173, 68], [196, 8, 173, 69], [197, 6, 174, 4, "color"], [197, 11, 174, 9], [197, 13, 174, 11, "LogBoxStyle"], [197, 24, 174, 22], [197, 25, 174, 23, "getTextColor"], [197, 37, 174, 35], [197, 38, 174, 36], [197, 41, 174, 39], [197, 42, 174, 40], [198, 6, 175, 4, "fontSize"], [198, 14, 175, 12], [198, 16, 175, 14], [198, 18, 175, 16], [199, 6, 176, 4, "fontWeight"], [199, 16, 176, 14], [199, 18, 176, 16], [199, 23, 176, 21], [200, 6, 177, 4, "includeFontPadding"], [200, 24, 177, 22], [200, 26, 177, 24], [200, 31, 177, 29], [201, 6, 178, 4, "lineHeight"], [201, 16, 178, 14], [201, 18, 178, 16], [202, 4, 179, 2], [202, 5, 179, 3], [203, 4, 180, 2, "frameLocation"], [203, 17, 180, 15], [203, 19, 180, 17], [204, 6, 181, 4, "color"], [204, 11, 181, 9], [204, 13, 181, 11, "LogBoxStyle"], [204, 24, 181, 22], [204, 25, 181, 23, "getTextColor"], [204, 37, 181, 35], [204, 38, 181, 36], [204, 41, 181, 39], [204, 42, 181, 40], [205, 6, 182, 4, "fontSize"], [205, 14, 182, 12], [205, 16, 182, 14], [205, 18, 182, 16], [206, 6, 183, 4, "fontWeight"], [206, 16, 183, 14], [206, 18, 183, 16], [206, 23, 183, 21], [207, 6, 184, 4, "includeFontPadding"], [207, 24, 184, 22], [207, 26, 184, 24], [207, 31, 184, 29], [208, 6, 185, 4, "lineHeight"], [208, 16, 185, 14], [208, 18, 185, 16], [208, 20, 185, 18], [209, 6, 186, 4, "paddingLeft"], [209, 17, 186, 15], [209, 19, 186, 17], [210, 4, 187, 2], [211, 2, 188, 0], [211, 3, 188, 1], [211, 4, 188, 2], [212, 2, 188, 3], [212, 6, 188, 3, "_default"], [212, 14, 188, 3], [212, 17, 188, 3, "exports"], [212, 24, 188, 3], [212, 25, 188, 3, "default"], [212, 32, 188, 3], [212, 35, 190, 15, "LogBoxInspectorReactFrames"], [212, 61, 190, 41], [213, 0, 190, 41], [213, 3]], "functionMap": {"names": ["<global>", "getPrettyFileName", "LogBoxInspectorReactFrames", "getStackList", "getCollapseMessage", "getStackList.map$argument_0", "<anonymous>", "LogBoxButton.props.onPress"], "mappings": "AAA;AC6B;CDoB;AEC;ECS;GDM;EEE;GFW;0BGI;kBCe;8EDC;OHmB;mBKO,8BL;CFO"}}, "type": "js/module"}]}