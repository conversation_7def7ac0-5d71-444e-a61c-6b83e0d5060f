{"dependencies": [{"name": "../Expo.fx", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 20, "index": 20}}], "key": "FvsXvN3bPiq/mbLa3/fohyj0yKA=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 66}, "end": {"line": 4, "column": 53, "index": 119}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./withDevTools", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 44, "column": 29, "index": 1721}, "end": {"line": 44, "column": 54, "index": 1746}}], "key": "eswx2chYZRS2ha0EDZHVoDJfYVM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = registerRootComponent;\n  require(_dependencyMap[0], \"../Expo.fx\");\n  var _reactNative = require(_dependencyMap[1], \"react-native\");\n  // @needsAudit\n  /**\n   * Sets the initial React component to render natively in the app's root React Native view on Android, iOS, tvOS and the web.\n   *\n   * This method does the following:\n   * - Invokes React Native's `AppRegistry.registerComponent`.\n   * - Invokes React Native web's `AppRegistry.runApplication` on web to render to the root `index.html` file.\n   * - Polyfills the `process.nextTick` function globally.\n   *\n   * This method also adds the following dev-only features that are removed in production bundles.\n   * - Adds the Fast Refresh and bundle splitting indicator to the app.\n   * - Asserts if the `expo-updates` package is misconfigured.\n   * - Asserts if `react-native` is not aliased to `react-native-web` when running in the browser.\n   * @param component The React component class that renders the rest of your app.\n   * @see For information on how to setup `registerRootComponent` in an existing (bare) React Native app, see [Common questions](#rootregistercomponent-setup-for-existing-react-native-projects) below.\n   */\n  function registerRootComponent(component) {\n    var qualifiedComponent = component;\n    if (process.env.NODE_ENV !== 'production') {\n      var _ref = require(_dependencyMap[2], \"./withDevTools\"),\n        withDevTools = _ref.withDevTools;\n      qualifiedComponent = withDevTools(component);\n    }\n    _reactNative.AppRegistry.registerComponent('main', () => qualifiedComponent);\n    // Skip querying the DOM if we're in a Node.js environment.\n    if (_reactNative.Platform.OS === 'web' && typeof window !== 'undefined') {\n      var rootTag = document.getElementById('root');\n      if (process.env.NODE_ENV !== 'production') {\n        if (!rootTag) {\n          throw new Error('Required HTML element with id \"root\" was not found in the document HTML.');\n        }\n      }\n      _reactNative.AppRegistry.runApplication('main', {\n        rootTag,\n        // Injected by SSR HTML tags.\n        hydrate: globalThis.__EXPO_ROUTER_HYDRATE__\n      });\n    }\n  }\n});", "lineCount": 47, "map": [[6, 2, 1, 0, "require"], [6, 9, 1, 0], [6, 10, 1, 0, "_dependencyMap"], [6, 24, 1, 0], [7, 2, 4, 0], [7, 6, 4, 0, "_reactNative"], [7, 18, 4, 0], [7, 21, 4, 0, "require"], [7, 28, 4, 0], [7, 29, 4, 0, "_dependencyMap"], [7, 43, 4, 0], [8, 2, 22, 0], [9, 2, 23, 0], [10, 0, 24, 0], [11, 0, 25, 0], [12, 0, 26, 0], [13, 0, 27, 0], [14, 0, 28, 0], [15, 0, 29, 0], [16, 0, 30, 0], [17, 0, 31, 0], [18, 0, 32, 0], [19, 0, 33, 0], [20, 0, 34, 0], [21, 0, 35, 0], [22, 0, 36, 0], [23, 0, 37, 0], [24, 2, 38, 15], [24, 11, 38, 24, "registerRootComponent"], [24, 32, 38, 45, "registerRootComponent"], [24, 33, 39, 2, "component"], [24, 42, 39, 29], [24, 44, 40, 8], [25, 4, 41, 2], [25, 8, 41, 6, "qualifiedComponent"], [25, 26, 41, 24], [25, 29, 41, 27, "component"], [25, 38, 41, 36], [26, 4, 43, 2], [26, 8, 43, 6, "process"], [26, 15, 43, 13], [26, 16, 43, 14, "env"], [26, 19, 43, 17], [26, 20, 43, 18, "NODE_ENV"], [26, 28, 43, 26], [26, 33, 43, 31], [26, 45, 43, 43], [26, 47, 43, 45], [27, 6, 44, 4], [27, 10, 44, 4, "_ref"], [27, 14, 44, 4], [27, 17, 44, 29, "require"], [27, 24, 44, 36], [27, 25, 44, 36, "_dependencyMap"], [27, 39, 44, 36], [27, 60, 44, 53], [27, 61, 44, 54], [28, 8, 44, 12, "withDevTools"], [28, 20, 44, 24], [28, 23, 44, 24, "_ref"], [28, 27, 44, 24], [28, 28, 44, 12, "withDevTools"], [28, 40, 44, 24], [29, 6, 45, 4, "qualifiedComponent"], [29, 24, 45, 22], [29, 27, 45, 25, "withDevTools"], [29, 39, 45, 37], [29, 40, 45, 38, "component"], [29, 49, 45, 47], [29, 50, 45, 48], [30, 4, 46, 2], [31, 4, 48, 2, "AppRegistry"], [31, 28, 48, 13], [31, 29, 48, 14, "registerComponent"], [31, 46, 48, 31], [31, 47, 48, 32], [31, 53, 48, 38], [31, 55, 48, 40], [31, 61, 48, 46, "qualifiedComponent"], [31, 79, 48, 64], [31, 80, 48, 65], [32, 4, 49, 2], [33, 4, 50, 2], [33, 8, 50, 6, "Platform"], [33, 29, 50, 14], [33, 30, 50, 15, "OS"], [33, 32, 50, 17], [33, 37, 50, 22], [33, 42, 50, 27], [33, 46, 50, 31], [33, 53, 50, 38, "window"], [33, 59, 50, 44], [33, 64, 50, 49], [33, 75, 50, 60], [33, 77, 50, 62], [34, 6, 51, 4], [34, 10, 51, 10, "rootTag"], [34, 17, 51, 17], [34, 20, 51, 20, "document"], [34, 28, 51, 28], [34, 29, 51, 29, "getElementById"], [34, 43, 51, 43], [34, 44, 51, 44], [34, 50, 51, 50], [34, 51, 51, 51], [35, 6, 52, 4], [35, 10, 52, 8, "process"], [35, 17, 52, 15], [35, 18, 52, 16, "env"], [35, 21, 52, 19], [35, 22, 52, 20, "NODE_ENV"], [35, 30, 52, 28], [35, 35, 52, 33], [35, 47, 52, 45], [35, 49, 52, 47], [36, 8, 53, 6], [36, 12, 53, 10], [36, 13, 53, 11, "rootTag"], [36, 20, 53, 18], [36, 22, 53, 20], [37, 10, 54, 8], [37, 16, 54, 14], [37, 20, 54, 18, "Error"], [37, 25, 54, 23], [37, 26, 54, 24], [37, 100, 54, 98], [37, 101, 54, 99], [38, 8, 55, 6], [39, 6, 56, 4], [40, 6, 58, 4, "AppRegistry"], [40, 30, 58, 15], [40, 31, 58, 16, "runApplication"], [40, 45, 58, 30], [40, 46, 58, 31], [40, 52, 58, 37], [40, 54, 58, 39], [41, 8, 59, 6, "rootTag"], [41, 15, 59, 13], [42, 8, 60, 6], [43, 8, 61, 6, "hydrate"], [43, 15, 61, 13], [43, 17, 61, 15, "globalThis"], [43, 27, 61, 25], [43, 28, 61, 26, "__EXPO_ROUTER_HYDRATE__"], [44, 6, 62, 4], [44, 7, 62, 5], [44, 8, 62, 6], [45, 4, 63, 2], [46, 2, 64, 0], [47, 0, 64, 1], [47, 3]], "functionMap": {"names": ["<global>", "registerRootComponent", "AppRegistry.registerComponent$argument_1"], "mappings": "AAA;eCqC;wCCU,wBD;CDgB"}}, "type": "js/module"}]}