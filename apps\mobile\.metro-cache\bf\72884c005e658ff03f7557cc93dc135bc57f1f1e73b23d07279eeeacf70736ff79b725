{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 64}, "end": {"line": 4, "column": 51, "index": 115}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _default = exports.default = _reactNative.TurboModuleRegistry.get('RNSModule');\n});", "lineCount": 10, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "default"], [7, 17, 1, 13], [8, 2, 4, 0], [8, 6, 4, 0, "_reactNative"], [8, 18, 4, 0], [8, 21, 4, 0, "require"], [8, 28, 4, 0], [8, 29, 4, 0, "_dependencyMap"], [8, 43, 4, 0], [9, 2, 4, 51], [9, 6, 4, 51, "_default"], [9, 14, 4, 51], [9, 17, 4, 51, "exports"], [9, 24, 4, 51], [9, 25, 4, 51, "default"], [9, 32, 4, 51], [9, 35, 8, 15, "TurboModuleRegistry"], [9, 67, 8, 34], [9, 68, 8, 35, "get"], [9, 71, 8, 38], [9, 72, 8, 45], [9, 83, 8, 56], [9, 84, 8, 57], [10, 0, 8, 57], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}