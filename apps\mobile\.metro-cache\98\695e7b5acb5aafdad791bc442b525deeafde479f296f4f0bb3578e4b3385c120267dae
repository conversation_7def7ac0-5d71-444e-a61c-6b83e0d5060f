{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 26, "index": 41}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 99}, "end": {"line": 12, "column": 22, "index": 200}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../fabric/ScreenStackHeaderConfigNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 223}, "end": {"line": 15, "column": 102, "index": 325}}], "key": "eJaAV5nmTqDyDdgzeNwmpmhAY5Y=", "exportNames": ["*"]}}, {"name": "../fabric/ScreenStackHeaderSubviewNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 326}, "end": {"line": 18, "column": 59, "index": 494}}], "key": "KROksKVJkHZNvKoDrBjZimnGEnI=", "exportNames": ["*"]}}, {"name": "./helpers/edge-to-edge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 495}, "end": {"line": 19, "column": 54, "index": 549}}], "key": "mL3LRc1/0hIqBAJosobpJZ1fPWw=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ScreenStackHeaderSubview = exports.ScreenStackHeaderSearchBarView = exports.ScreenStackHeaderRightView = exports.ScreenStackHeaderLeftView = exports.ScreenStackHeaderConfig = exports.ScreenStackHeaderCenterView = exports.ScreenStackHeaderBackButtonImage = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _ScreenStackHeaderConfigNativeComponent = _interopRequireDefault(require(_dependencyMap[4], \"../fabric/ScreenStackHeaderConfigNativeComponent\"));\n  var _ScreenStackHeaderSubviewNativeComponent = _interopRequireDefault(require(_dependencyMap[5], \"../fabric/ScreenStackHeaderSubviewNativeComponent\"));\n  var _edgeToEdge = require(_dependencyMap[6], \"./helpers/edge-to-edge\");\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _excluded = [\"style\"],\n    _excluded2 = [\"style\"],\n    _excluded3 = [\"style\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-screens\\\\src\\\\components\\\\ScreenStackHeaderConfig.tsx\"; // Native components\n  var ScreenStackHeaderSubview = exports.ScreenStackHeaderSubview = _ScreenStackHeaderSubviewNativeComponent.default;\n  var ScreenStackHeaderConfig = exports.ScreenStackHeaderConfig = /*#__PURE__*/_react.default.forwardRef((props, ref) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScreenStackHeaderConfigNativeComponent.default, {\n    ...props,\n    ref: ref,\n    topInsetEnabled: _edgeToEdge.EDGE_TO_EDGE ? true : props.topInsetEnabled,\n    style: styles.headerConfig,\n    pointerEvents: \"box-none\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 3\n  }, this));\n  ScreenStackHeaderConfig.displayName = 'ScreenStackHeaderConfig';\n  var ScreenStackHeaderBackButtonImage = props => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ScreenStackHeaderSubview, {\n    type: \"back\",\n    style: styles.headerSubview,\n    children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Image, {\n      resizeMode: \"center\",\n      fadeDuration: 0,\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 3\n  }, this);\n  exports.ScreenStackHeaderBackButtonImage = ScreenStackHeaderBackButtonImage;\n  var ScreenStackHeaderRightView = props => {\n    var style = props.style,\n      rest = (0, _objectWithoutProperties2.default)(props, _excluded);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ScreenStackHeaderSubview, {\n      ...rest,\n      type: \"right\",\n      style: [styles.headerSubview, style]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 5\n    }, this);\n  };\n  exports.ScreenStackHeaderRightView = ScreenStackHeaderRightView;\n  var ScreenStackHeaderLeftView = props => {\n    var style = props.style,\n      rest = (0, _objectWithoutProperties2.default)(props, _excluded2);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ScreenStackHeaderSubview, {\n      ...rest,\n      type: \"left\",\n      style: [styles.headerSubview, style]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 5\n    }, this);\n  };\n  exports.ScreenStackHeaderLeftView = ScreenStackHeaderLeftView;\n  var ScreenStackHeaderCenterView = props => {\n    var style = props.style,\n      rest = (0, _objectWithoutProperties2.default)(props, _excluded3);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ScreenStackHeaderSubview, {\n      ...rest,\n      type: \"center\",\n      style: [styles.headerSubviewCenter, style]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 5\n    }, this);\n  };\n  exports.ScreenStackHeaderCenterView = ScreenStackHeaderCenterView;\n  var ScreenStackHeaderSearchBarView = props => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ScreenStackHeaderSubview, {\n    ...props,\n    type: \"searchBar\",\n    style: styles.headerSubview\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 3\n  }, this);\n  exports.ScreenStackHeaderSearchBarView = ScreenStackHeaderSearchBarView;\n  var styles = _reactNative.StyleSheet.create({\n    headerSubview: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    headerSubviewCenter: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      justifyContent: 'center',\n      flexShrink: 1\n    },\n    headerConfig: {\n      position: 'absolute',\n      width: '100%',\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      // We only want to center align the subviews on iOS.\n      // See https://github.com/software-mansion/react-native-screens/pull/2456\n      alignItems: _reactNative.Platform.OS === 'ios' ? 'center' : undefined\n    }\n  });\n});", "lineCount": 125, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "ScreenStackHeaderSubview"], [8, 34, 1, 13], [8, 37, 1, 13, "exports"], [8, 44, 1, 13], [8, 45, 1, 13, "ScreenStackHeaderSearchBarView"], [8, 75, 1, 13], [8, 78, 1, 13, "exports"], [8, 85, 1, 13], [8, 86, 1, 13, "ScreenStackHeaderRightView"], [8, 112, 1, 13], [8, 115, 1, 13, "exports"], [8, 122, 1, 13], [8, 123, 1, 13, "ScreenStackHeaderLeftView"], [8, 148, 1, 13], [8, 151, 1, 13, "exports"], [8, 158, 1, 13], [8, 159, 1, 13, "ScreenStackHeaderConfig"], [8, 182, 1, 13], [8, 185, 1, 13, "exports"], [8, 192, 1, 13], [8, 193, 1, 13, "ScreenStackHeaderCenterView"], [8, 220, 1, 13], [8, 223, 1, 13, "exports"], [8, 230, 1, 13], [8, 231, 1, 13, "ScreenStackHeaderBackButtonImage"], [8, 263, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_react"], [10, 12, 3, 0], [10, 15, 3, 0, "_interopRequireDefault"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_reactNative"], [11, 18, 5, 0], [11, 21, 5, 0, "require"], [11, 28, 5, 0], [11, 29, 5, 0, "_dependencyMap"], [11, 43, 5, 0], [12, 2, 15, 0], [12, 6, 15, 0, "_ScreenStackHeaderConfigNativeComponent"], [12, 45, 15, 0], [12, 48, 15, 0, "_interopRequireDefault"], [12, 70, 15, 0], [12, 71, 15, 0, "require"], [12, 78, 15, 0], [12, 79, 15, 0, "_dependencyMap"], [12, 93, 15, 0], [13, 2, 16, 0], [13, 6, 16, 0, "_ScreenStackHeaderSubviewNativeComponent"], [13, 46, 16, 0], [13, 49, 16, 0, "_interopRequireDefault"], [13, 71, 16, 0], [13, 72, 16, 0, "require"], [13, 79, 16, 0], [13, 80, 16, 0, "_dependencyMap"], [13, 94, 16, 0], [14, 2, 19, 0], [14, 6, 19, 0, "_edgeToEdge"], [14, 17, 19, 0], [14, 20, 19, 0, "require"], [14, 27, 19, 0], [14, 28, 19, 0, "_dependencyMap"], [14, 42, 19, 0], [15, 2, 19, 54], [15, 6, 19, 54, "_jsxDevRuntime"], [15, 20, 19, 54], [15, 23, 19, 54, "require"], [15, 30, 19, 54], [15, 31, 19, 54, "_dependencyMap"], [15, 45, 19, 54], [16, 2, 19, 54], [16, 6, 19, 54, "_excluded"], [16, 15, 19, 54], [17, 4, 19, 54, "_excluded2"], [17, 14, 19, 54], [18, 4, 19, 54, "_excluded3"], [18, 14, 19, 54], [19, 2, 19, 54], [19, 6, 19, 54, "_jsxFileName"], [19, 18, 19, 54], [19, 139, 14, 0], [20, 2, 21, 7], [20, 6, 21, 13, "ScreenStackHeaderSubview"], [20, 30, 21, 95], [20, 33, 21, 95, "exports"], [20, 40, 21, 95], [20, 41, 21, 95, "ScreenStackHeaderSubview"], [20, 65, 21, 95], [20, 68, 22, 2, "ScreenStackHeaderSubviewNativeComponent"], [20, 116, 22, 41], [21, 2, 24, 7], [21, 6, 24, 13, "ScreenStackHeaderConfig"], [21, 29, 24, 36], [21, 32, 24, 36, "exports"], [21, 39, 24, 36], [21, 40, 24, 36, "ScreenStackHeaderConfig"], [21, 63, 24, 36], [21, 79, 24, 39, "React"], [21, 93, 24, 44], [21, 94, 24, 45, "forwardRef"], [21, 104, 24, 55], [21, 105, 27, 2], [21, 106, 27, 3, "props"], [21, 111, 27, 8], [21, 113, 27, 10, "ref"], [21, 116, 27, 13], [21, 134, 28, 2], [21, 138, 28, 2, "_jsxDevRuntime"], [21, 152, 28, 2], [21, 153, 28, 2, "jsxDEV"], [21, 159, 28, 2], [21, 161, 28, 3, "_ScreenStackHeaderConfigNativeComponent"], [21, 200, 28, 3], [21, 201, 28, 3, "default"], [21, 208, 28, 41], [22, 4, 28, 41], [22, 7, 29, 8, "props"], [22, 12, 29, 13], [23, 4, 30, 4, "ref"], [23, 7, 30, 7], [23, 9, 30, 9, "ref"], [23, 12, 30, 13], [24, 4, 31, 4, "topInsetEnabled"], [24, 19, 31, 19], [24, 21, 31, 21, "EDGE_TO_EDGE"], [24, 45, 31, 33], [24, 48, 31, 36], [24, 52, 31, 40], [24, 55, 31, 43, "props"], [24, 60, 31, 48], [24, 61, 31, 49, "topInsetEnabled"], [24, 76, 31, 65], [25, 4, 32, 4, "style"], [25, 9, 32, 9], [25, 11, 32, 11, "styles"], [25, 17, 32, 17], [25, 18, 32, 18, "headerConfig"], [25, 30, 32, 31], [26, 4, 33, 4, "pointerEvents"], [26, 17, 33, 17], [26, 19, 33, 18], [27, 2, 33, 28], [28, 4, 33, 28, "fileName"], [28, 12, 33, 28], [28, 14, 33, 28, "_jsxFileName"], [28, 26, 33, 28], [29, 4, 33, 28, "lineNumber"], [29, 14, 33, 28], [30, 4, 33, 28, "columnNumber"], [30, 16, 33, 28], [31, 2, 33, 28], [31, 9, 34, 3], [31, 10, 35, 1], [31, 11, 35, 2], [32, 2, 37, 0, "ScreenStackHeaderConfig"], [32, 25, 37, 23], [32, 26, 37, 24, "displayName"], [32, 37, 37, 35], [32, 40, 37, 38], [32, 65, 37, 63], [33, 2, 39, 7], [33, 6, 39, 13, "ScreenStackHeaderBackButtonImage"], [33, 38, 39, 45], [33, 41, 40, 2, "props"], [33, 46, 40, 19], [33, 63, 42, 2], [33, 67, 42, 2, "_jsxDevRuntime"], [33, 81, 42, 2], [33, 82, 42, 2, "jsxDEV"], [33, 88, 42, 2], [33, 90, 42, 3, "ScreenStackHeaderSubview"], [33, 114, 42, 27], [34, 4, 42, 28, "type"], [34, 8, 42, 32], [34, 10, 42, 33], [34, 16, 42, 39], [35, 4, 42, 40, "style"], [35, 9, 42, 45], [35, 11, 42, 47, "styles"], [35, 17, 42, 53], [35, 18, 42, 54, "headerSubview"], [35, 31, 42, 68], [36, 4, 42, 68, "children"], [36, 12, 42, 68], [36, 27, 43, 4], [36, 31, 43, 4, "_jsxDevRuntime"], [36, 45, 43, 4], [36, 46, 43, 4, "jsxDEV"], [36, 52, 43, 4], [36, 54, 43, 5, "_reactNative"], [36, 66, 43, 5], [36, 67, 43, 5, "Image"], [36, 72, 43, 10], [37, 6, 43, 11, "resizeMode"], [37, 16, 43, 21], [37, 18, 43, 22], [37, 26, 43, 30], [38, 6, 43, 31, "fadeDuration"], [38, 18, 43, 43], [38, 20, 43, 45], [38, 21, 43, 47], [39, 6, 43, 47], [39, 9, 43, 52, "props"], [40, 4, 43, 57], [41, 6, 43, 57, "fileName"], [41, 14, 43, 57], [41, 16, 43, 57, "_jsxFileName"], [41, 28, 43, 57], [42, 6, 43, 57, "lineNumber"], [42, 16, 43, 57], [43, 6, 43, 57, "columnNumber"], [43, 18, 43, 57], [44, 4, 43, 57], [44, 11, 43, 60], [45, 2, 43, 61], [46, 4, 43, 61, "fileName"], [46, 12, 43, 61], [46, 14, 43, 61, "_jsxFileName"], [46, 26, 43, 61], [47, 4, 43, 61, "lineNumber"], [47, 14, 43, 61], [48, 4, 43, 61, "columnNumber"], [48, 16, 43, 61], [49, 2, 43, 61], [49, 9, 44, 28], [49, 10, 45, 1], [50, 2, 45, 2, "exports"], [50, 9, 45, 2], [50, 10, 45, 2, "ScreenStackHeaderBackButtonImage"], [50, 42, 45, 2], [50, 45, 45, 2, "ScreenStackHeaderBackButtonImage"], [50, 77, 45, 2], [51, 2, 47, 7], [51, 6, 47, 13, "ScreenStackHeaderRightView"], [51, 32, 47, 39], [51, 35, 47, 43, "props"], [51, 40, 47, 59], [51, 44, 47, 77], [52, 4, 48, 2], [52, 8, 48, 10, "style"], [52, 13, 48, 15], [52, 16, 48, 29, "props"], [52, 21, 48, 34], [52, 22, 48, 10, "style"], [52, 27, 48, 15], [53, 6, 48, 20, "rest"], [53, 10, 48, 24], [53, 17, 48, 24, "_objectWithoutProperties2"], [53, 42, 48, 24], [53, 43, 48, 24, "default"], [53, 50, 48, 24], [53, 52, 48, 29, "props"], [53, 57, 48, 34], [53, 59, 48, 34, "_excluded"], [53, 68, 48, 34], [54, 4, 50, 2], [54, 24, 51, 4], [54, 28, 51, 4, "_jsxDevRuntime"], [54, 42, 51, 4], [54, 43, 51, 4, "jsxDEV"], [54, 49, 51, 4], [54, 51, 51, 5, "ScreenStackHeaderSubview"], [54, 75, 51, 29], [55, 6, 51, 29], [55, 9, 52, 10, "rest"], [55, 13, 52, 14], [56, 6, 53, 6, "type"], [56, 10, 53, 10], [56, 12, 53, 11], [56, 19, 53, 18], [57, 6, 54, 6, "style"], [57, 11, 54, 11], [57, 13, 54, 13], [57, 14, 54, 14, "styles"], [57, 20, 54, 20], [57, 21, 54, 21, "headerSubview"], [57, 34, 54, 34], [57, 36, 54, 36, "style"], [57, 41, 54, 41], [58, 4, 54, 43], [59, 6, 54, 43, "fileName"], [59, 14, 54, 43], [59, 16, 54, 43, "_jsxFileName"], [59, 28, 54, 43], [60, 6, 54, 43, "lineNumber"], [60, 16, 54, 43], [61, 6, 54, 43, "columnNumber"], [61, 18, 54, 43], [62, 4, 54, 43], [62, 11, 55, 5], [62, 12, 55, 6], [63, 2, 57, 0], [63, 3, 57, 1], [64, 2, 57, 2, "exports"], [64, 9, 57, 2], [64, 10, 57, 2, "ScreenStackHeaderRightView"], [64, 36, 57, 2], [64, 39, 57, 2, "ScreenStackHeaderRightView"], [64, 65, 57, 2], [65, 2, 59, 7], [65, 6, 59, 13, "ScreenStackHeaderLeftView"], [65, 31, 59, 38], [65, 34, 59, 42, "props"], [65, 39, 59, 58], [65, 43, 59, 76], [66, 4, 60, 2], [66, 8, 60, 10, "style"], [66, 13, 60, 15], [66, 16, 60, 29, "props"], [66, 21, 60, 34], [66, 22, 60, 10, "style"], [66, 27, 60, 15], [67, 6, 60, 20, "rest"], [67, 10, 60, 24], [67, 17, 60, 24, "_objectWithoutProperties2"], [67, 42, 60, 24], [67, 43, 60, 24, "default"], [67, 50, 60, 24], [67, 52, 60, 29, "props"], [67, 57, 60, 34], [67, 59, 60, 34, "_excluded2"], [67, 69, 60, 34], [68, 4, 62, 2], [68, 24, 63, 4], [68, 28, 63, 4, "_jsxDevRuntime"], [68, 42, 63, 4], [68, 43, 63, 4, "jsxDEV"], [68, 49, 63, 4], [68, 51, 63, 5, "ScreenStackHeaderSubview"], [68, 75, 63, 29], [69, 6, 63, 29], [69, 9, 64, 10, "rest"], [69, 13, 64, 14], [70, 6, 65, 6, "type"], [70, 10, 65, 10], [70, 12, 65, 11], [70, 18, 65, 17], [71, 6, 66, 6, "style"], [71, 11, 66, 11], [71, 13, 66, 13], [71, 14, 66, 14, "styles"], [71, 20, 66, 20], [71, 21, 66, 21, "headerSubview"], [71, 34, 66, 34], [71, 36, 66, 36, "style"], [71, 41, 66, 41], [72, 4, 66, 43], [73, 6, 66, 43, "fileName"], [73, 14, 66, 43], [73, 16, 66, 43, "_jsxFileName"], [73, 28, 66, 43], [74, 6, 66, 43, "lineNumber"], [74, 16, 66, 43], [75, 6, 66, 43, "columnNumber"], [75, 18, 66, 43], [76, 4, 66, 43], [76, 11, 67, 5], [76, 12, 67, 6], [77, 2, 69, 0], [77, 3, 69, 1], [78, 2, 69, 2, "exports"], [78, 9, 69, 2], [78, 10, 69, 2, "ScreenStackHeaderLeftView"], [78, 35, 69, 2], [78, 38, 69, 2, "ScreenStackHeaderLeftView"], [78, 63, 69, 2], [79, 2, 71, 7], [79, 6, 71, 13, "ScreenStackHeaderCenterView"], [79, 33, 71, 40], [79, 36, 71, 44, "props"], [79, 41, 71, 60], [79, 45, 71, 78], [80, 4, 72, 2], [80, 8, 72, 10, "style"], [80, 13, 72, 15], [80, 16, 72, 29, "props"], [80, 21, 72, 34], [80, 22, 72, 10, "style"], [80, 27, 72, 15], [81, 6, 72, 20, "rest"], [81, 10, 72, 24], [81, 17, 72, 24, "_objectWithoutProperties2"], [81, 42, 72, 24], [81, 43, 72, 24, "default"], [81, 50, 72, 24], [81, 52, 72, 29, "props"], [81, 57, 72, 34], [81, 59, 72, 34, "_excluded3"], [81, 69, 72, 34], [82, 4, 74, 2], [82, 24, 75, 4], [82, 28, 75, 4, "_jsxDevRuntime"], [82, 42, 75, 4], [82, 43, 75, 4, "jsxDEV"], [82, 49, 75, 4], [82, 51, 75, 5, "ScreenStackHeaderSubview"], [82, 75, 75, 29], [83, 6, 75, 29], [83, 9, 76, 10, "rest"], [83, 13, 76, 14], [84, 6, 77, 6, "type"], [84, 10, 77, 10], [84, 12, 77, 11], [84, 20, 77, 19], [85, 6, 78, 6, "style"], [85, 11, 78, 11], [85, 13, 78, 13], [85, 14, 78, 14, "styles"], [85, 20, 78, 20], [85, 21, 78, 21, "headerSubviewCenter"], [85, 40, 78, 40], [85, 42, 78, 42, "style"], [85, 47, 78, 47], [86, 4, 78, 49], [87, 6, 78, 49, "fileName"], [87, 14, 78, 49], [87, 16, 78, 49, "_jsxFileName"], [87, 28, 78, 49], [88, 6, 78, 49, "lineNumber"], [88, 16, 78, 49], [89, 6, 78, 49, "columnNumber"], [89, 18, 78, 49], [90, 4, 78, 49], [90, 11, 79, 5], [90, 12, 79, 6], [91, 2, 81, 0], [91, 3, 81, 1], [92, 2, 81, 2, "exports"], [92, 9, 81, 2], [92, 10, 81, 2, "ScreenStackHeaderCenterView"], [92, 37, 81, 2], [92, 40, 81, 2, "ScreenStackHeaderCenterView"], [92, 67, 81, 2], [93, 2, 83, 7], [93, 6, 83, 13, "ScreenStackHeaderSearchBarView"], [93, 36, 83, 43], [93, 39, 84, 2, "props"], [93, 44, 84, 18], [93, 61, 86, 2], [93, 65, 86, 2, "_jsxDevRuntime"], [93, 79, 86, 2], [93, 80, 86, 2, "jsxDEV"], [93, 86, 86, 2], [93, 88, 86, 3, "ScreenStackHeaderSubview"], [93, 112, 86, 27], [94, 4, 86, 27], [94, 7, 87, 8, "props"], [94, 12, 87, 13], [95, 4, 88, 4, "type"], [95, 8, 88, 8], [95, 10, 88, 9], [95, 21, 88, 20], [96, 4, 89, 4, "style"], [96, 9, 89, 9], [96, 11, 89, 11, "styles"], [96, 17, 89, 17], [96, 18, 89, 18, "headerSubview"], [97, 2, 89, 32], [98, 4, 89, 32, "fileName"], [98, 12, 89, 32], [98, 14, 89, 32, "_jsxFileName"], [98, 26, 89, 32], [99, 4, 89, 32, "lineNumber"], [99, 14, 89, 32], [100, 4, 89, 32, "columnNumber"], [100, 16, 89, 32], [101, 2, 89, 32], [101, 9, 90, 3], [101, 10, 91, 1], [102, 2, 91, 2, "exports"], [102, 9, 91, 2], [102, 10, 91, 2, "ScreenStackHeaderSearchBarView"], [102, 40, 91, 2], [102, 43, 91, 2, "ScreenStackHeaderSearchBarView"], [102, 73, 91, 2], [103, 2, 93, 0], [103, 6, 93, 6, "styles"], [103, 12, 93, 12], [103, 15, 93, 15, "StyleSheet"], [103, 38, 93, 25], [103, 39, 93, 26, "create"], [103, 45, 93, 32], [103, 46, 93, 33], [104, 4, 94, 2, "headerSubview"], [104, 17, 94, 15], [104, 19, 94, 17], [105, 6, 95, 4, "flexDirection"], [105, 19, 95, 17], [105, 21, 95, 19], [105, 26, 95, 24], [106, 6, 96, 4, "alignItems"], [106, 16, 96, 14], [106, 18, 96, 16], [106, 26, 96, 24], [107, 6, 97, 4, "justifyContent"], [107, 20, 97, 18], [107, 22, 97, 20], [108, 4, 98, 2], [108, 5, 98, 3], [109, 4, 99, 2, "headerSubviewCenter"], [109, 23, 99, 21], [109, 25, 99, 23], [110, 6, 100, 4, "flexDirection"], [110, 19, 100, 17], [110, 21, 100, 19], [110, 26, 100, 24], [111, 6, 101, 4, "alignItems"], [111, 16, 101, 14], [111, 18, 101, 16], [111, 26, 101, 24], [112, 6, 102, 4, "justifyContent"], [112, 20, 102, 18], [112, 22, 102, 20], [112, 30, 102, 28], [113, 6, 103, 4, "flexShrink"], [113, 16, 103, 14], [113, 18, 103, 16], [114, 4, 104, 2], [114, 5, 104, 3], [115, 4, 105, 2, "headerConfig"], [115, 16, 105, 14], [115, 18, 105, 16], [116, 6, 106, 4, "position"], [116, 14, 106, 12], [116, 16, 106, 14], [116, 26, 106, 24], [117, 6, 107, 4, "width"], [117, 11, 107, 9], [117, 13, 107, 11], [117, 19, 107, 17], [118, 6, 108, 4, "flexDirection"], [118, 19, 108, 17], [118, 21, 108, 19], [118, 26, 108, 24], [119, 6, 109, 4, "justifyContent"], [119, 20, 109, 18], [119, 22, 109, 20], [119, 37, 109, 35], [120, 6, 110, 4], [121, 6, 111, 4], [122, 6, 112, 4, "alignItems"], [122, 16, 112, 14], [122, 18, 112, 16, "Platform"], [122, 39, 112, 24], [122, 40, 112, 25, "OS"], [122, 42, 112, 27], [122, 47, 112, 32], [122, 52, 112, 37], [122, 55, 112, 40], [122, 63, 112, 48], [122, 66, 112, 51, "undefined"], [123, 4, 113, 2], [124, 2, 114, 0], [124, 3, 114, 1], [124, 4, 114, 2], [125, 0, 114, 3], [125, 3]], "functionMap": {"names": ["<global>", "React.forwardRef$argument_0", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView"], "mappings": "AAA;EC0B;CDQ;gDEI;CFM;0CGE;CHU;yCIE;CJU;2CKE;CLU;8CME;CNQ"}}, "type": "js/module"}]}