{"dependencies": [{"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "./use-tailwind", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 23, "index": 134}, "end": {"line": 4, "column": 48, "index": 159}}], "key": "IE9zRKq7W5s9Imr4veeB1jC5iMU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _slicedToArray = require(_dependencyMap[0], \"@babel/runtime/helpers/slicedToArray\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withStyledProps = void 0;\n  var use_tailwind_1 = require(_dependencyMap[1], \"./use-tailwind\");\n  function withStyledProps(_ref) {\n    var propsToTransform = _ref.propsToTransform,\n      componentProps = _ref.componentProps,\n      classProps = _ref.classProps,\n      preprocessed = _ref.preprocessed,\n      className = _ref.className;\n    var styledProps = {};\n    var mask = 0;\n    if (classProps) {\n      if (preprocessed) {\n        for (var prop of classProps) {\n          styledProps[prop] = undefined;\n          className += ` ${componentProps[prop]}`;\n        }\n      } else {\n        for (var _prop of classProps) {\n          var style = (0, use_tailwind_1.useTailwind)({\n            className: componentProps[_prop],\n            flatten: true\n          });\n          if (style.mask) {\n            mask |= style.mask;\n          }\n          Object.assign(styledProps, {\n            [_prop]: undefined\n          }, style[0]);\n        }\n      }\n    }\n    if (propsToTransform && !preprocessed) {\n      for (var _ref2 of Object.entries(propsToTransform)) {\n        var _ref3 = _slicedToArray(_ref2, 2);\n        var _prop2 = _ref3[0];\n        var styleKey = _ref3[1];\n        var styleArray = (0, use_tailwind_1.useTailwind)({\n          className: componentProps[_prop2],\n          flatten: styleKey !== true\n        });\n        if (styleArray.length === 0) {\n          continue;\n        }\n        if (styleArray.mask) {\n          mask |= styleArray.mask;\n        }\n        if (typeof styleKey === \"boolean\") {\n          styledProps[_prop2] = styleArray;\n        } else {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          styledProps[_prop2] = styleArray[0][styleKey];\n        }\n      }\n    }\n    return {\n      styledProps,\n      mask,\n      className\n    };\n  }\n  exports.withStyledProps = withStyledProps;\n});", "lineCount": 69, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_slicedToArray"], [4, 20, 1, 13], [4, 23, 1, 13, "require"], [4, 30, 1, 13], [4, 31, 1, 13, "_dependencyMap"], [4, 45, 1, 13], [5, 2, 2, 0, "Object"], [5, 8, 2, 6], [5, 9, 2, 7, "defineProperty"], [5, 23, 2, 21], [5, 24, 2, 22, "exports"], [5, 31, 2, 29], [5, 33, 2, 31], [5, 45, 2, 43], [5, 47, 2, 45], [6, 4, 2, 47, "value"], [6, 9, 2, 52], [6, 11, 2, 54], [7, 2, 2, 59], [7, 3, 2, 60], [7, 4, 2, 61], [8, 2, 3, 0, "exports"], [8, 9, 3, 7], [8, 10, 3, 8, "withStyledProps"], [8, 25, 3, 23], [8, 28, 3, 26], [8, 33, 3, 31], [8, 34, 3, 32], [9, 2, 4, 0], [9, 6, 4, 6, "use_tailwind_1"], [9, 20, 4, 20], [9, 23, 4, 23, "require"], [9, 30, 4, 30], [9, 31, 4, 30, "_dependencyMap"], [9, 45, 4, 30], [9, 66, 4, 47], [9, 67, 4, 48], [10, 2, 5, 0], [10, 11, 5, 9, "withStyledProps"], [10, 26, 5, 24, "withStyledProps"], [10, 27, 5, 24, "_ref"], [10, 31, 5, 24], [10, 33, 5, 101], [11, 4, 5, 101], [11, 8, 5, 27, "propsToTransform"], [11, 24, 5, 43], [11, 27, 5, 43, "_ref"], [11, 31, 5, 43], [11, 32, 5, 27, "propsToTransform"], [11, 48, 5, 43], [12, 6, 5, 45, "componentProps"], [12, 20, 5, 59], [12, 23, 5, 59, "_ref"], [12, 27, 5, 59], [12, 28, 5, 45, "componentProps"], [12, 42, 5, 59], [13, 6, 5, 61, "classProps"], [13, 16, 5, 71], [13, 19, 5, 71, "_ref"], [13, 23, 5, 71], [13, 24, 5, 61, "classProps"], [13, 34, 5, 71], [14, 6, 5, 73, "preprocessed"], [14, 18, 5, 85], [14, 21, 5, 85, "_ref"], [14, 25, 5, 85], [14, 26, 5, 73, "preprocessed"], [14, 38, 5, 85], [15, 6, 5, 87, "className"], [15, 15, 5, 96], [15, 18, 5, 96, "_ref"], [15, 22, 5, 96], [15, 23, 5, 87, "className"], [15, 32, 5, 96], [16, 4, 6, 4], [16, 8, 6, 10, "styledProps"], [16, 19, 6, 21], [16, 22, 6, 24], [16, 23, 6, 25], [16, 24, 6, 26], [17, 4, 7, 4], [17, 8, 7, 8, "mask"], [17, 12, 7, 12], [17, 15, 7, 15], [17, 16, 7, 16], [18, 4, 8, 4], [18, 8, 8, 8, "classProps"], [18, 18, 8, 18], [18, 20, 8, 20], [19, 6, 9, 8], [19, 10, 9, 12, "preprocessed"], [19, 22, 9, 24], [19, 24, 9, 26], [20, 8, 10, 12], [20, 13, 10, 17], [20, 17, 10, 23, "prop"], [20, 21, 10, 27], [20, 25, 10, 31, "classProps"], [20, 35, 10, 41], [20, 37, 10, 43], [21, 10, 11, 16, "styledProps"], [21, 21, 11, 27], [21, 22, 11, 28, "prop"], [21, 26, 11, 32], [21, 27, 11, 33], [21, 30, 11, 36, "undefined"], [21, 39, 11, 45], [22, 10, 12, 16, "className"], [22, 19, 12, 25], [22, 23, 12, 29], [22, 27, 12, 33, "componentProps"], [22, 41, 12, 47], [22, 42, 12, 48, "prop"], [22, 46, 12, 52], [22, 47, 12, 53], [22, 49, 12, 55], [23, 8, 13, 12], [24, 6, 14, 8], [24, 7, 14, 9], [24, 13, 15, 13], [25, 8, 16, 12], [25, 13, 16, 17], [25, 17, 16, 23, "prop"], [25, 22, 16, 27], [25, 26, 16, 31, "classProps"], [25, 36, 16, 41], [25, 38, 16, 43], [26, 10, 17, 16], [26, 14, 17, 22, "style"], [26, 19, 17, 27], [26, 22, 17, 30], [26, 23, 17, 31], [26, 24, 17, 32], [26, 26, 17, 34, "use_tailwind_1"], [26, 40, 17, 48], [26, 41, 17, 49, "useTailwind"], [26, 52, 17, 60], [26, 54, 17, 62], [27, 12, 18, 20, "className"], [27, 21, 18, 29], [27, 23, 18, 31, "componentProps"], [27, 37, 18, 45], [27, 38, 18, 46, "prop"], [27, 43, 18, 50], [27, 44, 18, 51], [28, 12, 19, 20, "flatten"], [28, 19, 19, 27], [28, 21, 19, 29], [29, 10, 20, 16], [29, 11, 20, 17], [29, 12, 20, 18], [30, 10, 21, 16], [30, 14, 21, 20, "style"], [30, 19, 21, 25], [30, 20, 21, 26, "mask"], [30, 24, 21, 30], [30, 26, 21, 32], [31, 12, 22, 20, "mask"], [31, 16, 22, 24], [31, 20, 22, 28, "style"], [31, 25, 22, 33], [31, 26, 22, 34, "mask"], [31, 30, 22, 38], [32, 10, 23, 16], [33, 10, 24, 16, "Object"], [33, 16, 24, 22], [33, 17, 24, 23, "assign"], [33, 23, 24, 29], [33, 24, 24, 30, "styledProps"], [33, 35, 24, 41], [33, 37, 24, 43], [34, 12, 24, 45], [34, 13, 24, 46, "prop"], [34, 18, 24, 50], [34, 21, 24, 53, "undefined"], [35, 10, 24, 63], [35, 11, 24, 64], [35, 13, 24, 66, "style"], [35, 18, 24, 71], [35, 19, 24, 72], [35, 20, 24, 73], [35, 21, 24, 74], [35, 22, 24, 75], [36, 8, 25, 12], [37, 6, 26, 8], [38, 4, 27, 4], [39, 4, 28, 4], [39, 8, 28, 8, "propsToTransform"], [39, 24, 28, 24], [39, 28, 28, 28], [39, 29, 28, 29, "preprocessed"], [39, 41, 28, 41], [39, 43, 28, 43], [40, 6, 29, 8], [40, 15, 29, 8, "_ref2"], [40, 20, 29, 8], [40, 24, 29, 39, "Object"], [40, 30, 29, 45], [40, 31, 29, 46, "entries"], [40, 38, 29, 53], [40, 39, 29, 54, "propsToTransform"], [40, 55, 29, 70], [40, 56, 29, 71], [40, 58, 29, 73], [41, 8, 29, 73], [41, 12, 29, 73, "_ref3"], [41, 17, 29, 73], [41, 20, 29, 73, "_slicedToArray"], [41, 34, 29, 73], [41, 35, 29, 73, "_ref2"], [41, 40, 29, 73], [42, 8, 29, 73], [42, 12, 29, 20, "prop"], [42, 18, 29, 24], [42, 21, 29, 24, "_ref3"], [42, 26, 29, 24], [43, 8, 29, 24], [43, 12, 29, 26, "styleKey"], [43, 20, 29, 34], [43, 23, 29, 34, "_ref3"], [43, 28, 29, 34], [44, 8, 30, 12], [44, 12, 30, 18, "styleArray"], [44, 22, 30, 28], [44, 25, 30, 31], [44, 26, 30, 32], [44, 27, 30, 33], [44, 29, 30, 35, "use_tailwind_1"], [44, 43, 30, 49], [44, 44, 30, 50, "useTailwind"], [44, 55, 30, 61], [44, 57, 30, 63], [45, 10, 31, 16, "className"], [45, 19, 31, 25], [45, 21, 31, 27, "componentProps"], [45, 35, 31, 41], [45, 36, 31, 42, "prop"], [45, 42, 31, 46], [45, 43, 31, 47], [46, 10, 32, 16, "flatten"], [46, 17, 32, 23], [46, 19, 32, 25, "styleKey"], [46, 27, 32, 33], [46, 32, 32, 38], [47, 8, 33, 12], [47, 9, 33, 13], [47, 10, 33, 14], [48, 8, 34, 12], [48, 12, 34, 16, "styleArray"], [48, 22, 34, 26], [48, 23, 34, 27, "length"], [48, 29, 34, 33], [48, 34, 34, 38], [48, 35, 34, 39], [48, 37, 34, 41], [49, 10, 35, 16], [50, 8, 36, 12], [51, 8, 37, 12], [51, 12, 37, 16, "styleArray"], [51, 22, 37, 26], [51, 23, 37, 27, "mask"], [51, 27, 37, 31], [51, 29, 37, 33], [52, 10, 38, 16, "mask"], [52, 14, 38, 20], [52, 18, 38, 24, "styleArray"], [52, 28, 38, 34], [52, 29, 38, 35, "mask"], [52, 33, 38, 39], [53, 8, 39, 12], [54, 8, 40, 12], [54, 12, 40, 16], [54, 19, 40, 23, "styleKey"], [54, 27, 40, 31], [54, 32, 40, 36], [54, 41, 40, 45], [54, 43, 40, 47], [55, 10, 41, 16, "styledProps"], [55, 21, 41, 27], [55, 22, 41, 28, "prop"], [55, 28, 41, 32], [55, 29, 41, 33], [55, 32, 41, 36, "styleArray"], [55, 42, 41, 46], [56, 8, 42, 12], [56, 9, 42, 13], [56, 15, 43, 17], [57, 10, 44, 16], [58, 10, 45, 16, "styledProps"], [58, 21, 45, 27], [58, 22, 45, 28, "prop"], [58, 28, 45, 32], [58, 29, 45, 33], [58, 32, 45, 36, "styleArray"], [58, 42, 45, 46], [58, 43, 45, 47], [58, 44, 45, 48], [58, 45, 45, 49], [58, 46, 45, 50, "styleKey"], [58, 54, 45, 58], [58, 55, 45, 59], [59, 8, 46, 12], [60, 6, 47, 8], [61, 4, 48, 4], [62, 4, 49, 4], [62, 11, 49, 11], [63, 6, 49, 13, "styledProps"], [63, 17, 49, 24], [64, 6, 49, 26, "mask"], [64, 10, 49, 30], [65, 6, 49, 32, "className"], [66, 4, 49, 42], [66, 5, 49, 43], [67, 2, 50, 0], [68, 2, 51, 0, "exports"], [68, 9, 51, 7], [68, 10, 51, 8, "withStyledProps"], [68, 25, 51, 23], [68, 28, 51, 26, "withStyledProps"], [68, 43, 51, 41], [69, 0, 51, 42], [69, 3]], "functionMap": {"names": ["<global>", "withStyledProps"], "mappings": "AAA;ACI;CD6C"}}, "type": "js/module"}]}