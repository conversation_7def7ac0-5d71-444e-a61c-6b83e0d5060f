{"dependencies": [{"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "./vendor/eventemitter3", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 3, "column": 21, "index": 36}, "end": {"line": 3, "column": 54, "index": 69}}], "key": "jJGG4W3dGzj/PN2Gsc+NKMZI37g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _classCallCheck = require(_dependencyMap[0], \"@babel/runtime/helpers/classCallCheck\");\n  var _createClass = require(_dependencyMap[1], \"@babel/runtime/helpers/createClass\");\n  var _possibleConstructorReturn = require(_dependencyMap[2], \"@babel/runtime/helpers/possibleConstructorReturn\");\n  var _getPrototypeOf = require(_dependencyMap[3], \"@babel/runtime/helpers/getPrototypeOf\");\n  var _inherits = require(_dependencyMap[4], \"@babel/runtime/helpers/inherits\");\n  var _slicedToArray = require(_dependencyMap[5], \"@babel/runtime/helpers/slicedToArray\");\n  function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var EventEmitter = require(_dependencyMap[6], \"./vendor/eventemitter3\");\n  var inject = _ref => {\n    var _ref$module = _slicedToArray(_ref.module, 2),\n      id = _ref$module[0],\n      code = _ref$module[1],\n      sourceURL = _ref.sourceURL;\n    if (global.globalEvalWithSourceUrl) {\n      global.globalEvalWithSourceUrl(code, sourceURL);\n    } else {\n      eval(code);\n    }\n  };\n  var injectUpdate = update => {\n    update.added.forEach(inject);\n    update.modified.forEach(inject);\n  };\n  var HMRClient = /*#__PURE__*/function (_EventEmitter) {\n    function HMRClient(url) {\n      var _this;\n      _classCallCheck(this, HMRClient);\n      _this = _callSuper(this, HMRClient);\n      _this._isEnabled = false;\n      _this._pendingUpdate = null;\n      _this._queue = [];\n      _this._state = \"opening\";\n      _this._ws = new global.WebSocket(url);\n      _this._ws.onopen = () => {\n        _this._state = \"open\";\n        _this.emit(\"open\");\n        _this._flushQueue();\n      };\n      _this._ws.onerror = error => {\n        _this.emit(\"connection-error\", error);\n      };\n      _this._ws.onclose = closeEvent => {\n        _this._state = \"closed\";\n        _this.emit(\"close\", closeEvent);\n      };\n      _this._ws.onmessage = message => {\n        var data = JSON.parse(String(message.data));\n        switch (data.type) {\n          case \"bundle-registered\":\n            _this.emit(\"bundle-registered\");\n            break;\n          case \"update-start\":\n            _this.emit(\"update-start\", data.body);\n            break;\n          case \"update\":\n            _this.emit(\"update\", data.body);\n            break;\n          case \"update-done\":\n            _this.emit(\"update-done\");\n            break;\n          case \"error\":\n            _this.emit(\"error\", data.body);\n            break;\n          default:\n            _this.emit(\"error\", {\n              type: \"unknown-message\",\n              message: data\n            });\n        }\n      };\n      _this.on(\"update\", update => {\n        if (_this._isEnabled) {\n          injectUpdate(update);\n        } else if (_this._pendingUpdate == null) {\n          _this._pendingUpdate = update;\n        } else {\n          _this._pendingUpdate = mergeUpdates(_this._pendingUpdate, update);\n        }\n      });\n      return _this;\n    }\n    _inherits(HMRClient, _EventEmitter);\n    return _createClass(HMRClient, [{\n      key: \"close\",\n      value: function close() {\n        this._ws.close();\n      }\n    }, {\n      key: \"send\",\n      value: function send(message) {\n        switch (this._state) {\n          case \"opening\":\n            this._queue.push(message);\n            break;\n          case \"open\":\n            this._ws.send(message);\n            break;\n          case \"closed\":\n            break;\n          default:\n            throw new Error(\"[WebSocketHMRClient] Unknown state: \" + this._state);\n        }\n      }\n    }, {\n      key: \"_flushQueue\",\n      value: function _flushQueue() {\n        this._queue.forEach(message => this.send(message));\n        this._queue.length = 0;\n      }\n    }, {\n      key: \"enable\",\n      value: function enable() {\n        this._isEnabled = true;\n        var update = this._pendingUpdate;\n        this._pendingUpdate = null;\n        if (update != null) {\n          injectUpdate(update);\n        }\n      }\n    }, {\n      key: \"disable\",\n      value: function disable() {\n        this._isEnabled = false;\n      }\n    }, {\n      key: \"isEnabled\",\n      value: function isEnabled() {\n        return this._isEnabled;\n      }\n    }, {\n      key: \"hasPendingUpdates\",\n      value: function hasPendingUpdates() {\n        return this._pendingUpdate != null;\n      }\n    }]);\n  }(EventEmitter);\n  function mergeUpdates(base, next) {\n    var addedIDs = new Set();\n    var deletedIDs = new Set();\n    var moduleMap = new Map();\n    applyUpdateLocally(base);\n    applyUpdateLocally(next);\n    function applyUpdateLocally(update) {\n      update.deleted.forEach(id => {\n        if (addedIDs.has(id)) {\n          addedIDs.delete(id);\n        } else {\n          deletedIDs.add(id);\n        }\n        moduleMap.delete(id);\n      });\n      update.added.forEach(item => {\n        var id = item.module[0];\n        if (deletedIDs.has(id)) {\n          deletedIDs.delete(id);\n        } else {\n          addedIDs.add(id);\n        }\n        moduleMap.set(id, item);\n      });\n      update.modified.forEach(item => {\n        var id = item.module[0];\n        moduleMap.set(id, item);\n      });\n    }\n    var result = {\n      isInitialUpdate: next.isInitialUpdate,\n      revisionId: next.revisionId,\n      added: [],\n      modified: [],\n      deleted: []\n    };\n    deletedIDs.forEach(id => {\n      result.deleted.push(id);\n    });\n    moduleMap.forEach((item, id) => {\n      if (deletedIDs.has(id)) {\n        return;\n      }\n      if (addedIDs.has(id)) {\n        result.added.push(item);\n      } else {\n        result.modified.push(item);\n      }\n    });\n    return result;\n  }\n  module.exports = HMRClient;\n});", "lineCount": 193, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_classCallCheck"], [4, 21, 1, 13], [4, 24, 1, 13, "require"], [4, 31, 1, 13], [4, 32, 1, 13, "_dependencyMap"], [4, 46, 1, 13], [5, 2, 1, 13], [5, 6, 1, 13, "_createClass"], [5, 18, 1, 13], [5, 21, 1, 13, "require"], [5, 28, 1, 13], [5, 29, 1, 13, "_dependencyMap"], [5, 43, 1, 13], [6, 2, 1, 13], [6, 6, 1, 13, "_possibleConstructorReturn"], [6, 32, 1, 13], [6, 35, 1, 13, "require"], [6, 42, 1, 13], [6, 43, 1, 13, "_dependencyMap"], [6, 57, 1, 13], [7, 2, 1, 13], [7, 6, 1, 13, "_getPrototypeOf"], [7, 21, 1, 13], [7, 24, 1, 13, "require"], [7, 31, 1, 13], [7, 32, 1, 13, "_dependencyMap"], [7, 46, 1, 13], [8, 2, 1, 13], [8, 6, 1, 13, "_inherits"], [8, 15, 1, 13], [8, 18, 1, 13, "require"], [8, 25, 1, 13], [8, 26, 1, 13, "_dependencyMap"], [8, 40, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray"], [9, 20, 1, 13], [9, 23, 1, 13, "require"], [9, 30, 1, 13], [9, 31, 1, 13, "_dependencyMap"], [9, 45, 1, 13], [10, 2, 1, 13], [10, 11, 1, 13, "_callSuper"], [10, 22, 1, 13, "t"], [10, 23, 1, 13], [10, 25, 1, 13, "o"], [10, 26, 1, 13], [10, 28, 1, 13, "e"], [10, 29, 1, 13], [10, 40, 1, 13, "o"], [10, 41, 1, 13], [10, 44, 1, 13, "_getPrototypeOf"], [10, 59, 1, 13], [10, 60, 1, 13, "o"], [10, 61, 1, 13], [10, 64, 1, 13, "_possibleConstructorReturn"], [10, 90, 1, 13], [10, 91, 1, 13, "t"], [10, 92, 1, 13], [10, 94, 1, 13, "_isNativeReflectConstruct"], [10, 119, 1, 13], [10, 124, 1, 13, "Reflect"], [10, 131, 1, 13], [10, 132, 1, 13, "construct"], [10, 141, 1, 13], [10, 142, 1, 13, "o"], [10, 143, 1, 13], [10, 145, 1, 13, "e"], [10, 146, 1, 13], [10, 154, 1, 13, "_getPrototypeOf"], [10, 169, 1, 13], [10, 170, 1, 13, "t"], [10, 171, 1, 13], [10, 173, 1, 13, "constructor"], [10, 184, 1, 13], [10, 188, 1, 13, "o"], [10, 189, 1, 13], [10, 190, 1, 13, "apply"], [10, 195, 1, 13], [10, 196, 1, 13, "t"], [10, 197, 1, 13], [10, 199, 1, 13, "e"], [10, 200, 1, 13], [11, 2, 1, 13], [11, 11, 1, 13, "_isNativeReflectConstruct"], [11, 37, 1, 13], [11, 51, 1, 13, "t"], [11, 52, 1, 13], [11, 56, 1, 13, "Boolean"], [11, 63, 1, 13], [11, 64, 1, 13, "prototype"], [11, 73, 1, 13], [11, 74, 1, 13, "valueOf"], [11, 81, 1, 13], [11, 82, 1, 13, "call"], [11, 86, 1, 13], [11, 87, 1, 13, "Reflect"], [11, 94, 1, 13], [11, 95, 1, 13, "construct"], [11, 104, 1, 13], [11, 105, 1, 13, "Boolean"], [11, 112, 1, 13], [11, 145, 1, 13, "t"], [11, 146, 1, 13], [11, 159, 1, 13, "_isNativeReflectConstruct"], [11, 184, 1, 13], [11, 196, 1, 13, "_isNativeReflectConstruct"], [11, 197, 1, 13], [11, 210, 1, 13, "t"], [11, 211, 1, 13], [12, 2, 3, 0], [12, 6, 3, 6, "EventEmitter"], [12, 18, 3, 18], [12, 21, 3, 21, "require"], [12, 28, 3, 28], [12, 29, 3, 28, "_dependencyMap"], [12, 43, 3, 28], [12, 72, 3, 53], [12, 73, 3, 54], [13, 2, 4, 0], [13, 6, 4, 6, "inject"], [13, 12, 4, 12], [13, 15, 4, 15, "_ref"], [13, 19, 4, 15], [13, 23, 4, 54], [14, 4, 4, 54], [14, 8, 4, 54, "_ref$module"], [14, 19, 4, 54], [14, 22, 4, 54, "_slicedToArray"], [14, 36, 4, 54], [14, 37, 4, 54, "_ref"], [14, 41, 4, 54], [14, 42, 4, 18, "module"], [14, 48, 4, 24], [15, 6, 4, 27, "id"], [15, 8, 4, 29], [15, 11, 4, 29, "_ref$module"], [15, 22, 4, 29], [16, 6, 4, 31, "code"], [16, 10, 4, 35], [16, 13, 4, 35, "_ref$module"], [16, 24, 4, 35], [17, 6, 4, 38, "sourceURL"], [17, 15, 4, 47], [17, 18, 4, 47, "_ref"], [17, 22, 4, 47], [17, 23, 4, 38, "sourceURL"], [17, 32, 4, 47], [18, 4, 5, 2], [18, 8, 5, 6, "global"], [18, 14, 5, 12], [18, 15, 5, 13, "globalEvalWithSourceUrl"], [18, 38, 5, 36], [18, 40, 5, 38], [19, 6, 6, 4, "global"], [19, 12, 6, 10], [19, 13, 6, 11, "globalEvalWithSourceUrl"], [19, 36, 6, 34], [19, 37, 6, 35, "code"], [19, 41, 6, 39], [19, 43, 6, 41, "sourceURL"], [19, 52, 6, 50], [19, 53, 6, 51], [20, 4, 7, 2], [20, 5, 7, 3], [20, 11, 7, 9], [21, 6, 8, 4, "eval"], [21, 10, 8, 8], [21, 11, 8, 9, "code"], [21, 15, 8, 13], [21, 16, 8, 14], [22, 4, 9, 2], [23, 2, 10, 0], [23, 3, 10, 1], [24, 2, 11, 0], [24, 6, 11, 6, "injectUpdate"], [24, 18, 11, 18], [24, 21, 11, 22, "update"], [24, 27, 11, 28], [24, 31, 11, 33], [25, 4, 12, 2, "update"], [25, 10, 12, 8], [25, 11, 12, 9, "added"], [25, 16, 12, 14], [25, 17, 12, 15, "for<PERSON>ach"], [25, 24, 12, 22], [25, 25, 12, 23, "inject"], [25, 31, 12, 29], [25, 32, 12, 30], [26, 4, 13, 2, "update"], [26, 10, 13, 8], [26, 11, 13, 9, "modified"], [26, 19, 13, 17], [26, 20, 13, 18, "for<PERSON>ach"], [26, 27, 13, 25], [26, 28, 13, 26, "inject"], [26, 34, 13, 32], [26, 35, 13, 33], [27, 2, 14, 0], [27, 3, 14, 1], [28, 2, 14, 2], [28, 6, 15, 6, "HMRClient"], [28, 15, 15, 15], [28, 41, 15, 15, "_EventEmitter"], [28, 54, 15, 15], [29, 4, 20, 2], [29, 13, 20, 2, "HMRClient"], [29, 23, 20, 14, "url"], [29, 26, 20, 17], [29, 28, 20, 19], [30, 6, 20, 19], [30, 10, 20, 19, "_this"], [30, 15, 20, 19], [31, 6, 20, 19, "_classCallCheck"], [31, 21, 20, 19], [31, 28, 20, 19, "HMRClient"], [31, 37, 20, 19], [32, 6, 21, 4, "_this"], [32, 11, 21, 4], [32, 14, 21, 4, "_callSuper"], [32, 24, 21, 4], [32, 31, 21, 4, "HMRClient"], [32, 40, 21, 4], [33, 6, 21, 12, "_this"], [33, 11, 21, 12], [33, 12, 16, 2, "_isEnabled"], [33, 22, 16, 12], [33, 25, 16, 15], [33, 30, 16, 20], [34, 6, 16, 20, "_this"], [34, 11, 16, 20], [34, 12, 17, 2, "_pendingUpdate"], [34, 26, 17, 16], [34, 29, 17, 19], [34, 33, 17, 23], [35, 6, 17, 23, "_this"], [35, 11, 17, 23], [35, 12, 18, 2, "_queue"], [35, 18, 18, 8], [35, 21, 18, 11], [35, 23, 18, 13], [36, 6, 18, 13, "_this"], [36, 11, 18, 13], [36, 12, 19, 2, "_state"], [36, 18, 19, 8], [36, 21, 19, 11], [36, 30, 19, 20], [37, 6, 22, 4, "_this"], [37, 11, 22, 4], [37, 12, 22, 9, "_ws"], [37, 15, 22, 12], [37, 18, 22, 15], [37, 22, 22, 19, "global"], [37, 28, 22, 25], [37, 29, 22, 26, "WebSocket"], [37, 38, 22, 35], [37, 39, 22, 36, "url"], [37, 42, 22, 39], [37, 43, 22, 40], [38, 6, 23, 4, "_this"], [38, 11, 23, 4], [38, 12, 23, 9, "_ws"], [38, 15, 23, 12], [38, 16, 23, 13, "onopen"], [38, 22, 23, 19], [38, 25, 23, 22], [38, 31, 23, 28], [39, 8, 24, 6, "_this"], [39, 13, 24, 6], [39, 14, 24, 11, "_state"], [39, 20, 24, 17], [39, 23, 24, 20], [39, 29, 24, 26], [40, 8, 25, 6, "_this"], [40, 13, 25, 6], [40, 14, 25, 11, "emit"], [40, 18, 25, 15], [40, 19, 25, 16], [40, 25, 25, 22], [40, 26, 25, 23], [41, 8, 26, 6, "_this"], [41, 13, 26, 6], [41, 14, 26, 11, "_flushQueue"], [41, 25, 26, 22], [41, 26, 26, 23], [41, 27, 26, 24], [42, 6, 27, 4], [42, 7, 27, 5], [43, 6, 28, 4, "_this"], [43, 11, 28, 4], [43, 12, 28, 9, "_ws"], [43, 15, 28, 12], [43, 16, 28, 13, "onerror"], [43, 23, 28, 20], [43, 26, 28, 24, "error"], [43, 31, 28, 29], [43, 35, 28, 34], [44, 8, 29, 6, "_this"], [44, 13, 29, 6], [44, 14, 29, 11, "emit"], [44, 18, 29, 15], [44, 19, 29, 16], [44, 37, 29, 34], [44, 39, 29, 36, "error"], [44, 44, 29, 41], [44, 45, 29, 42], [45, 6, 30, 4], [45, 7, 30, 5], [46, 6, 31, 4, "_this"], [46, 11, 31, 4], [46, 12, 31, 9, "_ws"], [46, 15, 31, 12], [46, 16, 31, 13, "onclose"], [46, 23, 31, 20], [46, 26, 31, 24, "closeEvent"], [46, 36, 31, 34], [46, 40, 31, 39], [47, 8, 32, 6, "_this"], [47, 13, 32, 6], [47, 14, 32, 11, "_state"], [47, 20, 32, 17], [47, 23, 32, 20], [47, 31, 32, 28], [48, 8, 33, 6, "_this"], [48, 13, 33, 6], [48, 14, 33, 11, "emit"], [48, 18, 33, 15], [48, 19, 33, 16], [48, 26, 33, 23], [48, 28, 33, 25, "closeEvent"], [48, 38, 33, 35], [48, 39, 33, 36], [49, 6, 34, 4], [49, 7, 34, 5], [50, 6, 35, 4, "_this"], [50, 11, 35, 4], [50, 12, 35, 9, "_ws"], [50, 15, 35, 12], [50, 16, 35, 13, "onmessage"], [50, 25, 35, 22], [50, 28, 35, 26, "message"], [50, 35, 35, 33], [50, 39, 35, 38], [51, 8, 36, 6], [51, 12, 36, 12, "data"], [51, 16, 36, 16], [51, 19, 36, 19, "JSON"], [51, 23, 36, 23], [51, 24, 36, 24, "parse"], [51, 29, 36, 29], [51, 30, 36, 30, "String"], [51, 36, 36, 36], [51, 37, 36, 37, "message"], [51, 44, 36, 44], [51, 45, 36, 45, "data"], [51, 49, 36, 49], [51, 50, 36, 50], [51, 51, 36, 51], [52, 8, 37, 6], [52, 16, 37, 14, "data"], [52, 20, 37, 18], [52, 21, 37, 19, "type"], [52, 25, 37, 23], [53, 10, 38, 8], [53, 15, 38, 13], [53, 34, 38, 32], [54, 12, 39, 10, "_this"], [54, 17, 39, 10], [54, 18, 39, 15, "emit"], [54, 22, 39, 19], [54, 23, 39, 20], [54, 42, 39, 39], [54, 43, 39, 40], [55, 12, 40, 10], [56, 10, 41, 8], [56, 15, 41, 13], [56, 29, 41, 27], [57, 12, 42, 10, "_this"], [57, 17, 42, 10], [57, 18, 42, 15, "emit"], [57, 22, 42, 19], [57, 23, 42, 20], [57, 37, 42, 34], [57, 39, 42, 36, "data"], [57, 43, 42, 40], [57, 44, 42, 41, "body"], [57, 48, 42, 45], [57, 49, 42, 46], [58, 12, 43, 10], [59, 10, 44, 8], [59, 15, 44, 13], [59, 23, 44, 21], [60, 12, 45, 10, "_this"], [60, 17, 45, 10], [60, 18, 45, 15, "emit"], [60, 22, 45, 19], [60, 23, 45, 20], [60, 31, 45, 28], [60, 33, 45, 30, "data"], [60, 37, 45, 34], [60, 38, 45, 35, "body"], [60, 42, 45, 39], [60, 43, 45, 40], [61, 12, 46, 10], [62, 10, 47, 8], [62, 15, 47, 13], [62, 28, 47, 26], [63, 12, 48, 10, "_this"], [63, 17, 48, 10], [63, 18, 48, 15, "emit"], [63, 22, 48, 19], [63, 23, 48, 20], [63, 36, 48, 33], [63, 37, 48, 34], [64, 12, 49, 10], [65, 10, 50, 8], [65, 15, 50, 13], [65, 22, 50, 20], [66, 12, 51, 10, "_this"], [66, 17, 51, 10], [66, 18, 51, 15, "emit"], [66, 22, 51, 19], [66, 23, 51, 20], [66, 30, 51, 27], [66, 32, 51, 29, "data"], [66, 36, 51, 33], [66, 37, 51, 34, "body"], [66, 41, 51, 38], [66, 42, 51, 39], [67, 12, 52, 10], [68, 10, 53, 8], [69, 12, 54, 10, "_this"], [69, 17, 54, 10], [69, 18, 54, 15, "emit"], [69, 22, 54, 19], [69, 23, 54, 20], [69, 30, 54, 27], [69, 32, 54, 29], [70, 14, 55, 12, "type"], [70, 18, 55, 16], [70, 20, 55, 18], [70, 37, 55, 35], [71, 14, 56, 12, "message"], [71, 21, 56, 19], [71, 23, 56, 21, "data"], [72, 12, 57, 10], [72, 13, 57, 11], [72, 14, 57, 12], [73, 8, 58, 6], [74, 6, 59, 4], [74, 7, 59, 5], [75, 6, 60, 4, "_this"], [75, 11, 60, 4], [75, 12, 60, 9, "on"], [75, 14, 60, 11], [75, 15, 60, 12], [75, 23, 60, 20], [75, 25, 60, 23, "update"], [75, 31, 60, 29], [75, 35, 60, 34], [76, 8, 61, 6], [76, 12, 61, 10, "_this"], [76, 17, 61, 10], [76, 18, 61, 15, "_isEnabled"], [76, 28, 61, 25], [76, 30, 61, 27], [77, 10, 62, 8, "injectUpdate"], [77, 22, 62, 20], [77, 23, 62, 21, "update"], [77, 29, 62, 27], [77, 30, 62, 28], [78, 8, 63, 6], [78, 9, 63, 7], [78, 15, 63, 13], [78, 19, 63, 17, "_this"], [78, 24, 63, 17], [78, 25, 63, 22, "_pendingUpdate"], [78, 39, 63, 36], [78, 43, 63, 40], [78, 47, 63, 44], [78, 49, 63, 46], [79, 10, 64, 8, "_this"], [79, 15, 64, 8], [79, 16, 64, 13, "_pendingUpdate"], [79, 30, 64, 27], [79, 33, 64, 30, "update"], [79, 39, 64, 36], [80, 8, 65, 6], [80, 9, 65, 7], [80, 15, 65, 13], [81, 10, 66, 8, "_this"], [81, 15, 66, 8], [81, 16, 66, 13, "_pendingUpdate"], [81, 30, 66, 27], [81, 33, 66, 30, "mergeUpdates"], [81, 45, 66, 42], [81, 46, 66, 43, "_this"], [81, 51, 66, 43], [81, 52, 66, 48, "_pendingUpdate"], [81, 66, 66, 62], [81, 68, 66, 64, "update"], [81, 74, 66, 70], [81, 75, 66, 71], [82, 8, 67, 6], [83, 6, 68, 4], [83, 7, 68, 5], [83, 8, 68, 6], [84, 6, 68, 7], [84, 13, 68, 7, "_this"], [84, 18, 68, 7], [85, 4, 69, 2], [86, 4, 69, 3, "_inherits"], [86, 13, 69, 3], [86, 14, 69, 3, "HMRClient"], [86, 23, 69, 3], [86, 25, 69, 3, "_EventEmitter"], [86, 38, 69, 3], [87, 4, 69, 3], [87, 11, 69, 3, "_createClass"], [87, 23, 69, 3], [87, 24, 69, 3, "HMRClient"], [87, 33, 69, 3], [88, 6, 69, 3, "key"], [88, 9, 69, 3], [89, 6, 69, 3, "value"], [89, 11, 69, 3], [89, 13, 70, 2], [89, 22, 70, 2, "close"], [89, 27, 70, 7, "close"], [89, 28, 70, 7], [89, 30, 70, 10], [90, 8, 71, 4], [90, 12, 71, 8], [90, 13, 71, 9, "_ws"], [90, 16, 71, 12], [90, 17, 71, 13, "close"], [90, 22, 71, 18], [90, 23, 71, 19], [90, 24, 71, 20], [91, 6, 72, 2], [92, 4, 72, 3], [93, 6, 72, 3, "key"], [93, 9, 72, 3], [94, 6, 72, 3, "value"], [94, 11, 72, 3], [94, 13, 73, 2], [94, 22, 73, 2, "send"], [94, 26, 73, 6, "send"], [94, 27, 73, 7, "message"], [94, 34, 73, 14], [94, 36, 73, 16], [95, 8, 74, 4], [95, 16, 74, 12], [95, 20, 74, 16], [95, 21, 74, 17, "_state"], [95, 27, 74, 23], [96, 10, 75, 6], [96, 15, 75, 11], [96, 24, 75, 20], [97, 12, 76, 8], [97, 16, 76, 12], [97, 17, 76, 13, "_queue"], [97, 23, 76, 19], [97, 24, 76, 20, "push"], [97, 28, 76, 24], [97, 29, 76, 25, "message"], [97, 36, 76, 32], [97, 37, 76, 33], [98, 12, 77, 8], [99, 10, 78, 6], [99, 15, 78, 11], [99, 21, 78, 17], [100, 12, 79, 8], [100, 16, 79, 12], [100, 17, 79, 13, "_ws"], [100, 20, 79, 16], [100, 21, 79, 17, "send"], [100, 25, 79, 21], [100, 26, 79, 22, "message"], [100, 33, 79, 29], [100, 34, 79, 30], [101, 12, 80, 8], [102, 10, 81, 6], [102, 15, 81, 11], [102, 23, 81, 19], [103, 12, 82, 8], [104, 10, 83, 6], [105, 12, 84, 8], [105, 18, 84, 14], [105, 22, 84, 18, "Error"], [105, 27, 84, 23], [105, 28, 84, 24], [105, 66, 84, 62], [105, 69, 84, 65], [105, 73, 84, 69], [105, 74, 84, 70, "_state"], [105, 80, 84, 76], [105, 81, 84, 77], [106, 8, 85, 4], [107, 6, 86, 2], [108, 4, 86, 3], [109, 6, 86, 3, "key"], [109, 9, 86, 3], [110, 6, 86, 3, "value"], [110, 11, 86, 3], [110, 13, 87, 2], [110, 22, 87, 2, "_flushQueue"], [110, 33, 87, 13, "_flushQueue"], [110, 34, 87, 13], [110, 36, 87, 16], [111, 8, 88, 4], [111, 12, 88, 8], [111, 13, 88, 9, "_queue"], [111, 19, 88, 15], [111, 20, 88, 16, "for<PERSON>ach"], [111, 27, 88, 23], [111, 28, 88, 25, "message"], [111, 35, 88, 32], [111, 39, 88, 37], [111, 43, 88, 41], [111, 44, 88, 42, "send"], [111, 48, 88, 46], [111, 49, 88, 47, "message"], [111, 56, 88, 54], [111, 57, 88, 55], [111, 58, 88, 56], [112, 8, 89, 4], [112, 12, 89, 8], [112, 13, 89, 9, "_queue"], [112, 19, 89, 15], [112, 20, 89, 16, "length"], [112, 26, 89, 22], [112, 29, 89, 25], [112, 30, 89, 26], [113, 6, 90, 2], [114, 4, 90, 3], [115, 6, 90, 3, "key"], [115, 9, 90, 3], [116, 6, 90, 3, "value"], [116, 11, 90, 3], [116, 13, 91, 2], [116, 22, 91, 2, "enable"], [116, 28, 91, 8, "enable"], [116, 29, 91, 8], [116, 31, 91, 11], [117, 8, 92, 4], [117, 12, 92, 8], [117, 13, 92, 9, "_isEnabled"], [117, 23, 92, 19], [117, 26, 92, 22], [117, 30, 92, 26], [118, 8, 93, 4], [118, 12, 93, 10, "update"], [118, 18, 93, 16], [118, 21, 93, 19], [118, 25, 93, 23], [118, 26, 93, 24, "_pendingUpdate"], [118, 40, 93, 38], [119, 8, 94, 4], [119, 12, 94, 8], [119, 13, 94, 9, "_pendingUpdate"], [119, 27, 94, 23], [119, 30, 94, 26], [119, 34, 94, 30], [120, 8, 95, 4], [120, 12, 95, 8, "update"], [120, 18, 95, 14], [120, 22, 95, 18], [120, 26, 95, 22], [120, 28, 95, 24], [121, 10, 96, 6, "injectUpdate"], [121, 22, 96, 18], [121, 23, 96, 19, "update"], [121, 29, 96, 25], [121, 30, 96, 26], [122, 8, 97, 4], [123, 6, 98, 2], [124, 4, 98, 3], [125, 6, 98, 3, "key"], [125, 9, 98, 3], [126, 6, 98, 3, "value"], [126, 11, 98, 3], [126, 13, 99, 2], [126, 22, 99, 2, "disable"], [126, 29, 99, 9, "disable"], [126, 30, 99, 9], [126, 32, 99, 12], [127, 8, 100, 4], [127, 12, 100, 8], [127, 13, 100, 9, "_isEnabled"], [127, 23, 100, 19], [127, 26, 100, 22], [127, 31, 100, 27], [128, 6, 101, 2], [129, 4, 101, 3], [130, 6, 101, 3, "key"], [130, 9, 101, 3], [131, 6, 101, 3, "value"], [131, 11, 101, 3], [131, 13, 102, 2], [131, 22, 102, 2, "isEnabled"], [131, 31, 102, 11, "isEnabled"], [131, 32, 102, 11], [131, 34, 102, 14], [132, 8, 103, 4], [132, 15, 103, 11], [132, 19, 103, 15], [132, 20, 103, 16, "_isEnabled"], [132, 30, 103, 26], [133, 6, 104, 2], [134, 4, 104, 3], [135, 6, 104, 3, "key"], [135, 9, 104, 3], [136, 6, 104, 3, "value"], [136, 11, 104, 3], [136, 13, 105, 2], [136, 22, 105, 2, "hasPendingUpdates"], [136, 39, 105, 19, "hasPendingUpdates"], [136, 40, 105, 19], [136, 42, 105, 22], [137, 8, 106, 4], [137, 15, 106, 11], [137, 19, 106, 15], [137, 20, 106, 16, "_pendingUpdate"], [137, 34, 106, 30], [137, 38, 106, 34], [137, 42, 106, 38], [138, 6, 107, 2], [139, 4, 107, 3], [140, 2, 107, 3], [140, 4, 15, 24, "EventEmitter"], [140, 16, 15, 36], [141, 2, 109, 0], [141, 11, 109, 9, "mergeUpdates"], [141, 23, 109, 21, "mergeUpdates"], [141, 24, 109, 22, "base"], [141, 28, 109, 26], [141, 30, 109, 28, "next"], [141, 34, 109, 32], [141, 36, 109, 34], [142, 4, 110, 2], [142, 8, 110, 8, "addedIDs"], [142, 16, 110, 16], [142, 19, 110, 19], [142, 23, 110, 23, "Set"], [142, 26, 110, 26], [142, 27, 110, 27], [142, 28, 110, 28], [143, 4, 111, 2], [143, 8, 111, 8, "deletedIDs"], [143, 18, 111, 18], [143, 21, 111, 21], [143, 25, 111, 25, "Set"], [143, 28, 111, 28], [143, 29, 111, 29], [143, 30, 111, 30], [144, 4, 112, 2], [144, 8, 112, 8, "moduleMap"], [144, 17, 112, 17], [144, 20, 112, 20], [144, 24, 112, 24, "Map"], [144, 27, 112, 27], [144, 28, 112, 28], [144, 29, 112, 29], [145, 4, 113, 2, "applyUpdateLocally"], [145, 22, 113, 20], [145, 23, 113, 21, "base"], [145, 27, 113, 25], [145, 28, 113, 26], [146, 4, 114, 2, "applyUpdateLocally"], [146, 22, 114, 20], [146, 23, 114, 21, "next"], [146, 27, 114, 25], [146, 28, 114, 26], [147, 4, 115, 2], [147, 13, 115, 11, "applyUpdateLocally"], [147, 31, 115, 29, "applyUpdateLocally"], [147, 32, 115, 30, "update"], [147, 38, 115, 36], [147, 40, 115, 38], [148, 6, 116, 4, "update"], [148, 12, 116, 10], [148, 13, 116, 11, "deleted"], [148, 20, 116, 18], [148, 21, 116, 19, "for<PERSON>ach"], [148, 28, 116, 26], [148, 29, 116, 28, "id"], [148, 31, 116, 30], [148, 35, 116, 35], [149, 8, 117, 6], [149, 12, 117, 10, "addedIDs"], [149, 20, 117, 18], [149, 21, 117, 19, "has"], [149, 24, 117, 22], [149, 25, 117, 23, "id"], [149, 27, 117, 25], [149, 28, 117, 26], [149, 30, 117, 28], [150, 10, 118, 8, "addedIDs"], [150, 18, 118, 16], [150, 19, 118, 17, "delete"], [150, 25, 118, 23], [150, 26, 118, 24, "id"], [150, 28, 118, 26], [150, 29, 118, 27], [151, 8, 119, 6], [151, 9, 119, 7], [151, 15, 119, 13], [152, 10, 120, 8, "deletedIDs"], [152, 20, 120, 18], [152, 21, 120, 19, "add"], [152, 24, 120, 22], [152, 25, 120, 23, "id"], [152, 27, 120, 25], [152, 28, 120, 26], [153, 8, 121, 6], [154, 8, 122, 6, "moduleMap"], [154, 17, 122, 15], [154, 18, 122, 16, "delete"], [154, 24, 122, 22], [154, 25, 122, 23, "id"], [154, 27, 122, 25], [154, 28, 122, 26], [155, 6, 123, 4], [155, 7, 123, 5], [155, 8, 123, 6], [156, 6, 124, 4, "update"], [156, 12, 124, 10], [156, 13, 124, 11, "added"], [156, 18, 124, 16], [156, 19, 124, 17, "for<PERSON>ach"], [156, 26, 124, 24], [156, 27, 124, 26, "item"], [156, 31, 124, 30], [156, 35, 124, 35], [157, 8, 125, 6], [157, 12, 125, 12, "id"], [157, 14, 125, 14], [157, 17, 125, 17, "item"], [157, 21, 125, 21], [157, 22, 125, 22, "module"], [157, 28, 125, 28], [157, 29, 125, 29], [157, 30, 125, 30], [157, 31, 125, 31], [158, 8, 126, 6], [158, 12, 126, 10, "deletedIDs"], [158, 22, 126, 20], [158, 23, 126, 21, "has"], [158, 26, 126, 24], [158, 27, 126, 25, "id"], [158, 29, 126, 27], [158, 30, 126, 28], [158, 32, 126, 30], [159, 10, 127, 8, "deletedIDs"], [159, 20, 127, 18], [159, 21, 127, 19, "delete"], [159, 27, 127, 25], [159, 28, 127, 26, "id"], [159, 30, 127, 28], [159, 31, 127, 29], [160, 8, 128, 6], [160, 9, 128, 7], [160, 15, 128, 13], [161, 10, 129, 8, "addedIDs"], [161, 18, 129, 16], [161, 19, 129, 17, "add"], [161, 22, 129, 20], [161, 23, 129, 21, "id"], [161, 25, 129, 23], [161, 26, 129, 24], [162, 8, 130, 6], [163, 8, 131, 6, "moduleMap"], [163, 17, 131, 15], [163, 18, 131, 16, "set"], [163, 21, 131, 19], [163, 22, 131, 20, "id"], [163, 24, 131, 22], [163, 26, 131, 24, "item"], [163, 30, 131, 28], [163, 31, 131, 29], [164, 6, 132, 4], [164, 7, 132, 5], [164, 8, 132, 6], [165, 6, 133, 4, "update"], [165, 12, 133, 10], [165, 13, 133, 11, "modified"], [165, 21, 133, 19], [165, 22, 133, 20, "for<PERSON>ach"], [165, 29, 133, 27], [165, 30, 133, 29, "item"], [165, 34, 133, 33], [165, 38, 133, 38], [166, 8, 134, 6], [166, 12, 134, 12, "id"], [166, 14, 134, 14], [166, 17, 134, 17, "item"], [166, 21, 134, 21], [166, 22, 134, 22, "module"], [166, 28, 134, 28], [166, 29, 134, 29], [166, 30, 134, 30], [166, 31, 134, 31], [167, 8, 135, 6, "moduleMap"], [167, 17, 135, 15], [167, 18, 135, 16, "set"], [167, 21, 135, 19], [167, 22, 135, 20, "id"], [167, 24, 135, 22], [167, 26, 135, 24, "item"], [167, 30, 135, 28], [167, 31, 135, 29], [168, 6, 136, 4], [168, 7, 136, 5], [168, 8, 136, 6], [169, 4, 137, 2], [170, 4, 138, 2], [170, 8, 138, 8, "result"], [170, 14, 138, 14], [170, 17, 138, 17], [171, 6, 139, 4, "isInitialUpdate"], [171, 21, 139, 19], [171, 23, 139, 21, "next"], [171, 27, 139, 25], [171, 28, 139, 26, "isInitialUpdate"], [171, 43, 139, 41], [172, 6, 140, 4, "revisionId"], [172, 16, 140, 14], [172, 18, 140, 16, "next"], [172, 22, 140, 20], [172, 23, 140, 21, "revisionId"], [172, 33, 140, 31], [173, 6, 141, 4, "added"], [173, 11, 141, 9], [173, 13, 141, 11], [173, 15, 141, 13], [174, 6, 142, 4, "modified"], [174, 14, 142, 12], [174, 16, 142, 14], [174, 18, 142, 16], [175, 6, 143, 4, "deleted"], [175, 13, 143, 11], [175, 15, 143, 13], [176, 4, 144, 2], [176, 5, 144, 3], [177, 4, 145, 2, "deletedIDs"], [177, 14, 145, 12], [177, 15, 145, 13, "for<PERSON>ach"], [177, 22, 145, 20], [177, 23, 145, 22, "id"], [177, 25, 145, 24], [177, 29, 145, 29], [178, 6, 146, 4, "result"], [178, 12, 146, 10], [178, 13, 146, 11, "deleted"], [178, 20, 146, 18], [178, 21, 146, 19, "push"], [178, 25, 146, 23], [178, 26, 146, 24, "id"], [178, 28, 146, 26], [178, 29, 146, 27], [179, 4, 147, 2], [179, 5, 147, 3], [179, 6, 147, 4], [180, 4, 148, 2, "moduleMap"], [180, 13, 148, 11], [180, 14, 148, 12, "for<PERSON>ach"], [180, 21, 148, 19], [180, 22, 148, 20], [180, 23, 148, 21, "item"], [180, 27, 148, 25], [180, 29, 148, 27, "id"], [180, 31, 148, 29], [180, 36, 148, 34], [181, 6, 149, 4], [181, 10, 149, 8, "deletedIDs"], [181, 20, 149, 18], [181, 21, 149, 19, "has"], [181, 24, 149, 22], [181, 25, 149, 23, "id"], [181, 27, 149, 25], [181, 28, 149, 26], [181, 30, 149, 28], [182, 8, 150, 6], [183, 6, 151, 4], [184, 6, 152, 4], [184, 10, 152, 8, "addedIDs"], [184, 18, 152, 16], [184, 19, 152, 17, "has"], [184, 22, 152, 20], [184, 23, 152, 21, "id"], [184, 25, 152, 23], [184, 26, 152, 24], [184, 28, 152, 26], [185, 8, 153, 6, "result"], [185, 14, 153, 12], [185, 15, 153, 13, "added"], [185, 20, 153, 18], [185, 21, 153, 19, "push"], [185, 25, 153, 23], [185, 26, 153, 24, "item"], [185, 30, 153, 28], [185, 31, 153, 29], [186, 6, 154, 4], [186, 7, 154, 5], [186, 13, 154, 11], [187, 8, 155, 6, "result"], [187, 14, 155, 12], [187, 15, 155, 13, "modified"], [187, 23, 155, 21], [187, 24, 155, 22, "push"], [187, 28, 155, 26], [187, 29, 155, 27, "item"], [187, 33, 155, 31], [187, 34, 155, 32], [188, 6, 156, 4], [189, 4, 157, 2], [189, 5, 157, 3], [189, 6, 157, 4], [190, 4, 158, 2], [190, 11, 158, 9, "result"], [190, 17, 158, 15], [191, 2, 159, 0], [192, 2, 160, 0, "module"], [192, 8, 160, 6], [192, 9, 160, 7, "exports"], [192, 16, 160, 14], [192, 19, 160, 17, "HMRClient"], [192, 28, 160, 26], [193, 0, 160, 27], [193, 3]], "functionMap": {"names": ["<global>", "inject", "injectUpdate", "HMRClient", "constructor", "_ws.onopen", "_ws.onerror", "_ws.onclose", "_ws.onmessage", "on$argument_1", "close", "send", "_flushQueue", "_queue.forEach$argument_0", "enable", "disable", "isEnabled", "hasPendingUpdates", "mergeUpdates", "applyUpdateLocally", "update.deleted.forEach$argument_0", "update.added.forEach$argument_0", "update.modified.forEach$argument_0", "deletedIDs.forEach$argument_0", "moduleMap.forEach$argument_0"], "mappings": "AAA;eCG;CDM;qBEC;CFG;AGC;ECK;sBCG;KDI;uBEC;KFE;uBGC;KHG;yBIC;KJwB;sBKC;KLQ;GDC;EOC;GPE;EQC;GRa;ESC;wBCC,+BD;GTE;EWC;GXO;EYC;GZE;EaC;GbE;EcC;GdE;CHC;AkBC;ECM;2BCC;KDO;yBEC;KFQ;4BGC;KHG;GDC;qBKQ;GLE;oBMC;GNS;ClBE"}}, "type": "js/module"}]}