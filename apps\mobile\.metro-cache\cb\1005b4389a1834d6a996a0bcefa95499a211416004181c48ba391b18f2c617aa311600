{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getModalRouteKeys = void 0;\n  var getModalRouteKeys = (routes, descriptors) => routes.reduce((acc, route) => {\n    var _ref = descriptors[route.key]?.options ?? {},\n      presentation = _ref.presentation;\n    if (acc.length && !presentation || presentation === 'modal' || presentation === 'transparentModal') {\n      acc.push(route.key);\n    }\n    return acc;\n  }, []);\n  exports.getModalRouteKeys = getModalRouteKeys;\n});", "lineCount": 17, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getModalRouteKeys"], [7, 27, 1, 13], [8, 2, 3, 7], [8, 6, 3, 13, "getModalRouteKeys"], [8, 23, 3, 30], [8, 26, 3, 33, "getModalRouteKeys"], [8, 27, 3, 34, "routes"], [8, 33, 3, 40], [8, 35, 3, 42, "descriptors"], [8, 46, 3, 53], [8, 51, 3, 58, "routes"], [8, 57, 3, 64], [8, 58, 3, 65, "reduce"], [8, 64, 3, 71], [8, 65, 3, 72], [8, 66, 3, 73, "acc"], [8, 69, 3, 76], [8, 71, 3, 78, "route"], [8, 76, 3, 83], [8, 81, 3, 88], [9, 4, 4, 2], [9, 8, 4, 2, "_ref"], [9, 12, 4, 2], [9, 15, 6, 6, "descriptors"], [9, 26, 6, 17], [9, 27, 6, 18, "route"], [9, 32, 6, 23], [9, 33, 6, 24, "key"], [9, 36, 6, 27], [9, 37, 6, 28], [9, 39, 6, 30, "options"], [9, 46, 6, 37], [9, 50, 6, 41], [9, 51, 6, 42], [9, 52, 6, 43], [10, 6, 5, 4, "presentation"], [10, 18, 5, 16], [10, 21, 5, 16, "_ref"], [10, 25, 5, 16], [10, 26, 5, 4, "presentation"], [10, 38, 5, 16], [11, 4, 7, 2], [11, 8, 7, 6, "acc"], [11, 11, 7, 9], [11, 12, 7, 10, "length"], [11, 18, 7, 16], [11, 22, 7, 20], [11, 23, 7, 21, "presentation"], [11, 35, 7, 33], [11, 39, 7, 37, "presentation"], [11, 51, 7, 49], [11, 56, 7, 54], [11, 63, 7, 61], [11, 67, 7, 65, "presentation"], [11, 79, 7, 77], [11, 84, 7, 82], [11, 102, 7, 100], [11, 104, 7, 102], [12, 6, 8, 4, "acc"], [12, 9, 8, 7], [12, 10, 8, 8, "push"], [12, 14, 8, 12], [12, 15, 8, 13, "route"], [12, 20, 8, 18], [12, 21, 8, 19, "key"], [12, 24, 8, 22], [12, 25, 8, 23], [13, 4, 9, 2], [14, 4, 10, 2], [14, 11, 10, 9, "acc"], [14, 14, 10, 12], [15, 2, 11, 0], [15, 3, 11, 1], [15, 5, 11, 3], [15, 7, 11, 5], [15, 8, 11, 6], [16, 2, 11, 7, "exports"], [16, 9, 11, 7], [16, 10, 11, 7, "getModalRouteKeys"], [16, 27, 11, 7], [16, 30, 11, 7, "getModalRouteKeys"], [16, 47, 11, 7], [17, 0, 11, 7], [17, 3]], "functionMap": {"names": ["<global>", "getModalRouteKeys", "routes.reduce$argument_0"], "mappings": "AAA;iCCE,uCC;CDQ,KD"}}, "type": "js/module"}]}