{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.needsToReattach = needsToReattach;\n  // Checks whether the gesture should be reattached to the view, this will happen when:\n  // - The number of gestures in the preparedGesture is different than the number of gestures in the gesture\n  // - The handlerName is different in any of the gestures\n  // - At least one of the gestures changed the thread it runs on\n  function needsToReattach(preparedGesture, newGestures) {\n    if (newGestures.length !== preparedGesture.attachedGestures.length) {\n      return true;\n    }\n    for (var i = 0; i < newGestures.length; i++) {\n      if (newGestures[i].handlerName !== preparedGesture.attachedGestures[i].handlerName || newGestures[i].shouldUseReanimated !== preparedGesture.attachedGestures[i].shouldUseReanimated) {\n        return true;\n      }\n    }\n    return false;\n  }\n});", "lineCount": 21, "map": [[6, 2, 4, 0], [7, 2, 5, 0], [8, 2, 6, 0], [9, 2, 7, 0], [10, 2, 8, 7], [10, 11, 8, 16, "needsToReattach"], [10, 26, 8, 31, "needsToReattach"], [10, 27, 9, 2, "preparedGesture"], [10, 42, 9, 39], [10, 44, 10, 2, "newGestures"], [10, 55, 10, 28], [10, 57, 11, 2], [11, 4, 12, 2], [11, 8, 12, 6, "newGestures"], [11, 19, 12, 17], [11, 20, 12, 18, "length"], [11, 26, 12, 24], [11, 31, 12, 29, "preparedGesture"], [11, 46, 12, 44], [11, 47, 12, 45, "attachedGestures"], [11, 63, 12, 61], [11, 64, 12, 62, "length"], [11, 70, 12, 68], [11, 72, 12, 70], [12, 6, 13, 4], [12, 13, 13, 11], [12, 17, 13, 15], [13, 4, 14, 2], [14, 4, 15, 2], [14, 9, 15, 7], [14, 13, 15, 11, "i"], [14, 14, 15, 12], [14, 17, 15, 15], [14, 18, 15, 16], [14, 20, 15, 18, "i"], [14, 21, 15, 19], [14, 24, 15, 22, "newGestures"], [14, 35, 15, 33], [14, 36, 15, 34, "length"], [14, 42, 15, 40], [14, 44, 15, 42, "i"], [14, 45, 15, 43], [14, 47, 15, 45], [14, 49, 15, 47], [15, 6, 16, 4], [15, 10, 17, 6, "newGestures"], [15, 21, 17, 17], [15, 22, 17, 18, "i"], [15, 23, 17, 19], [15, 24, 17, 20], [15, 25, 17, 21, "handler<PERSON>ame"], [15, 36, 17, 32], [15, 41, 18, 8, "preparedGesture"], [15, 56, 18, 23], [15, 57, 18, 24, "attachedGestures"], [15, 73, 18, 40], [15, 74, 18, 41, "i"], [15, 75, 18, 42], [15, 76, 18, 43], [15, 77, 18, 44, "handler<PERSON>ame"], [15, 88, 18, 55], [15, 92, 19, 6, "newGestures"], [15, 103, 19, 17], [15, 104, 19, 18, "i"], [15, 105, 19, 19], [15, 106, 19, 20], [15, 107, 19, 21, "shouldUseReanimated"], [15, 126, 19, 40], [15, 131, 20, 8, "preparedGesture"], [15, 146, 20, 23], [15, 147, 20, 24, "attachedGestures"], [15, 163, 20, 40], [15, 164, 20, 41, "i"], [15, 165, 20, 42], [15, 166, 20, 43], [15, 167, 20, 44, "shouldUseReanimated"], [15, 186, 20, 63], [15, 188, 21, 6], [16, 8, 22, 6], [16, 15, 22, 13], [16, 19, 22, 17], [17, 6, 23, 4], [18, 4, 24, 2], [19, 4, 26, 2], [19, 11, 26, 9], [19, 16, 26, 14], [20, 2, 27, 0], [21, 0, 27, 1], [21, 3]], "functionMap": {"names": ["<global>", "needsToReattach"], "mappings": "AAA;OCO;CDmB"}}, "type": "js/module"}]}