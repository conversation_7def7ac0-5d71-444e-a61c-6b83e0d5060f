{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 45}}], "key": "WyqnBhspP5BAR0xvCwqfBv/v4uA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _Platform = _interopRequireDefault(require(_dependencyMap[1], \"../Utilities/Platform\"));\n  function shouldUseTurboAnimatedModule() {\n    return _Platform.default.OS === 'ios' && global.RN$Bridgeless === true;\n  }\n  var _default = exports.default = shouldUseTurboAnimatedModule;\n});", "lineCount": 12, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_Platform"], [7, 15, 11, 0], [7, 18, 11, 0, "_interopRequireDefault"], [7, 40, 11, 0], [7, 41, 11, 0, "require"], [7, 48, 11, 0], [7, 49, 11, 0, "_dependencyMap"], [7, 63, 11, 0], [8, 2, 13, 0], [8, 11, 13, 9, "shouldUseTurboAnimatedModule"], [8, 39, 13, 37, "shouldUseTurboAnimatedModule"], [8, 40, 13, 37], [8, 42, 13, 49], [9, 4, 14, 2], [9, 11, 14, 9, "Platform"], [9, 28, 14, 17], [9, 29, 14, 18, "OS"], [9, 31, 14, 20], [9, 36, 14, 25], [9, 41, 14, 30], [9, 45, 14, 34, "global"], [9, 51, 14, 40], [9, 52, 14, 41, "RN$Bridgeless"], [9, 65, 14, 54], [9, 70, 14, 59], [9, 74, 14, 63], [10, 2, 15, 0], [11, 2, 15, 1], [11, 6, 15, 1, "_default"], [11, 14, 15, 1], [11, 17, 15, 1, "exports"], [11, 24, 15, 1], [11, 25, 15, 1, "default"], [11, 32, 15, 1], [11, 35, 17, 15, "shouldUseTurboAnimatedModule"], [11, 63, 17, 43], [12, 0, 17, 43], [12, 3]], "functionMap": {"names": ["<global>", "shouldUseTurboAnimatedModule"], "mappings": "AAA;ACY;CDE"}}, "type": "js/module"}]}