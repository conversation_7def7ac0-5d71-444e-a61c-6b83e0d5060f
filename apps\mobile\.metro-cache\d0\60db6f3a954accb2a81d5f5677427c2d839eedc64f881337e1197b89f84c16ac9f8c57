{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  // Returns \"Type(value) is Object\" in ES terminology.\n  function isObject(value) {\n    return typeof value === \"object\" && value !== null || typeof value === \"function\";\n  }\n  function hasOwn(obj, prop) {\n    return Object.prototype.hasOwnProperty.call(obj, prop);\n  }\n  var wrapperSymbol = Symbol(\"wrapper\");\n  var implSymbol = Symbol(\"impl\");\n  var sameObjectCaches = Symbol(\"SameObject caches\");\n  var ctorRegistrySymbol = Symbol.for(\"[webidl2js]  constructor registry\");\n  function getSameObject(wrapper, prop, creator) {\n    if (!wrapper[sameObjectCaches]) {\n      wrapper[sameObjectCaches] = Object.create(null);\n    }\n    if (prop in wrapper[sameObjectCaches]) {\n      return wrapper[sameObjectCaches][prop];\n    }\n    wrapper[sameObjectCaches][prop] = creator();\n    return wrapper[sameObjectCaches][prop];\n  }\n  function wrapperForImpl(impl) {\n    return impl ? impl[wrapperSymbol] : null;\n  }\n  function implForWrapper(wrapper) {\n    return wrapper ? wrapper[implSymbol] : null;\n  }\n  function tryWrapperForImpl(impl) {\n    var wrapper = wrapperForImpl(impl);\n    return wrapper ? wrapper : impl;\n  }\n  function tryImplForWrapper(wrapper) {\n    var impl = implForWrapper(wrapper);\n    return impl ? impl : wrapper;\n  }\n  var iterInternalSymbol = Symbol(\"internal\");\n  var IteratorPrototype = Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]()));\n  function isArrayIndexPropName(P) {\n    if (typeof P !== \"string\") {\n      return false;\n    }\n    var i = P >>> 0;\n    if (i === Math.pow(2, 32) - 1) {\n      return false;\n    }\n    var s = `${i}`;\n    if (P !== s) {\n      return false;\n    }\n    return true;\n  }\n  var byteLengthGetter = Object.getOwnPropertyDescriptor(ArrayBuffer.prototype, \"byteLength\").get;\n  function isArrayBuffer(value) {\n    try {\n      byteLengthGetter.call(value);\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n  var supportsPropertyIndex = Symbol(\"supports property index\");\n  var supportedPropertyIndices = Symbol(\"supported property indices\");\n  var supportsPropertyName = Symbol(\"supports property name\");\n  var supportedPropertyNames = Symbol(\"supported property names\");\n  var indexedGet = Symbol(\"indexed property get\");\n  var indexedSetNew = Symbol(\"indexed property set new\");\n  var indexedSetExisting = Symbol(\"indexed property set existing\");\n  var namedGet = Symbol(\"named property get\");\n  var namedSetNew = Symbol(\"named property set new\");\n  var namedSetExisting = Symbol(\"named property set existing\");\n  var namedDelete = Symbol(\"named property delete\");\n  module.exports = exports = {\n    isObject,\n    hasOwn,\n    wrapperSymbol,\n    implSymbol,\n    getSameObject,\n    ctorRegistrySymbol,\n    wrapperForImpl,\n    implForWrapper,\n    tryWrapperForImpl,\n    tryImplForWrapper,\n    iterInternalSymbol,\n    IteratorPrototype,\n    isArrayBuffer,\n    isArrayIndexPropName,\n    supportsPropertyIndex,\n    supportedPropertyIndices,\n    supportsPropertyName,\n    supportedPropertyNames,\n    indexedGet,\n    indexedSetNew,\n    indexedSetExisting,\n    namedGet,\n    namedSetNew,\n    namedSetExisting,\n    namedDelete\n  };\n});", "lineCount": 102, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 4, 0], [5, 11, 4, 9, "isObject"], [5, 19, 4, 17, "isObject"], [5, 20, 4, 18, "value"], [5, 25, 4, 23], [5, 27, 4, 25], [6, 4, 5, 2], [6, 11, 5, 9], [6, 18, 5, 16, "value"], [6, 23, 5, 21], [6, 28, 5, 26], [6, 36, 5, 34], [6, 40, 5, 38, "value"], [6, 45, 5, 43], [6, 50, 5, 48], [6, 54, 5, 52], [6, 58, 5, 56], [6, 65, 5, 63, "value"], [6, 70, 5, 68], [6, 75, 5, 73], [6, 85, 5, 83], [7, 2, 6, 0], [8, 2, 8, 0], [8, 11, 8, 9, "hasOwn"], [8, 17, 8, 15, "hasOwn"], [8, 18, 8, 16, "obj"], [8, 21, 8, 19], [8, 23, 8, 21, "prop"], [8, 27, 8, 25], [8, 29, 8, 27], [9, 4, 9, 2], [9, 11, 9, 9, "Object"], [9, 17, 9, 15], [9, 18, 9, 16, "prototype"], [9, 27, 9, 25], [9, 28, 9, 26, "hasOwnProperty"], [9, 42, 9, 40], [9, 43, 9, 41, "call"], [9, 47, 9, 45], [9, 48, 9, 46, "obj"], [9, 51, 9, 49], [9, 53, 9, 51, "prop"], [9, 57, 9, 55], [9, 58, 9, 56], [10, 2, 10, 0], [11, 2, 12, 0], [11, 6, 12, 6, "wrapperSymbol"], [11, 19, 12, 19], [11, 22, 12, 22, "Symbol"], [11, 28, 12, 28], [11, 29, 12, 29], [11, 38, 12, 38], [11, 39, 12, 39], [12, 2, 13, 0], [12, 6, 13, 6, "implSymbol"], [12, 16, 13, 16], [12, 19, 13, 19, "Symbol"], [12, 25, 13, 25], [12, 26, 13, 26], [12, 32, 13, 32], [12, 33, 13, 33], [13, 2, 14, 0], [13, 6, 14, 6, "sameObjectCaches"], [13, 22, 14, 22], [13, 25, 14, 25, "Symbol"], [13, 31, 14, 31], [13, 32, 14, 32], [13, 51, 14, 51], [13, 52, 14, 52], [14, 2, 15, 0], [14, 6, 15, 6, "ctorRegistrySymbol"], [14, 24, 15, 24], [14, 27, 15, 27, "Symbol"], [14, 33, 15, 33], [14, 34, 15, 34, "for"], [14, 37, 15, 37], [14, 38, 15, 38], [14, 73, 15, 73], [14, 74, 15, 74], [15, 2, 17, 0], [15, 11, 17, 9, "getSameObject"], [15, 24, 17, 22, "getSameObject"], [15, 25, 17, 23, "wrapper"], [15, 32, 17, 30], [15, 34, 17, 32, "prop"], [15, 38, 17, 36], [15, 40, 17, 38, "creator"], [15, 47, 17, 45], [15, 49, 17, 47], [16, 4, 18, 2], [16, 8, 18, 6], [16, 9, 18, 7, "wrapper"], [16, 16, 18, 14], [16, 17, 18, 15, "sameObjectCaches"], [16, 33, 18, 31], [16, 34, 18, 32], [16, 36, 18, 34], [17, 6, 19, 4, "wrapper"], [17, 13, 19, 11], [17, 14, 19, 12, "sameObjectCaches"], [17, 30, 19, 28], [17, 31, 19, 29], [17, 34, 19, 32, "Object"], [17, 40, 19, 38], [17, 41, 19, 39, "create"], [17, 47, 19, 45], [17, 48, 19, 46], [17, 52, 19, 50], [17, 53, 19, 51], [18, 4, 20, 2], [19, 4, 22, 2], [19, 8, 22, 6, "prop"], [19, 12, 22, 10], [19, 16, 22, 14, "wrapper"], [19, 23, 22, 21], [19, 24, 22, 22, "sameObjectCaches"], [19, 40, 22, 38], [19, 41, 22, 39], [19, 43, 22, 41], [20, 6, 23, 4], [20, 13, 23, 11, "wrapper"], [20, 20, 23, 18], [20, 21, 23, 19, "sameObjectCaches"], [20, 37, 23, 35], [20, 38, 23, 36], [20, 39, 23, 37, "prop"], [20, 43, 23, 41], [20, 44, 23, 42], [21, 4, 24, 2], [22, 4, 26, 2, "wrapper"], [22, 11, 26, 9], [22, 12, 26, 10, "sameObjectCaches"], [22, 28, 26, 26], [22, 29, 26, 27], [22, 30, 26, 28, "prop"], [22, 34, 26, 32], [22, 35, 26, 33], [22, 38, 26, 36, "creator"], [22, 45, 26, 43], [22, 46, 26, 44], [22, 47, 26, 45], [23, 4, 27, 2], [23, 11, 27, 9, "wrapper"], [23, 18, 27, 16], [23, 19, 27, 17, "sameObjectCaches"], [23, 35, 27, 33], [23, 36, 27, 34], [23, 37, 27, 35, "prop"], [23, 41, 27, 39], [23, 42, 27, 40], [24, 2, 28, 0], [25, 2, 30, 0], [25, 11, 30, 9, "wrapperForImpl"], [25, 25, 30, 23, "wrapperForImpl"], [25, 26, 30, 24, "impl"], [25, 30, 30, 28], [25, 32, 30, 30], [26, 4, 31, 2], [26, 11, 31, 9, "impl"], [26, 15, 31, 13], [26, 18, 31, 16, "impl"], [26, 22, 31, 20], [26, 23, 31, 21, "wrapperSymbol"], [26, 36, 31, 34], [26, 37, 31, 35], [26, 40, 31, 38], [26, 44, 31, 42], [27, 2, 32, 0], [28, 2, 34, 0], [28, 11, 34, 9, "implForWrapper"], [28, 25, 34, 23, "implForWrapper"], [28, 26, 34, 24, "wrapper"], [28, 33, 34, 31], [28, 35, 34, 33], [29, 4, 35, 2], [29, 11, 35, 9, "wrapper"], [29, 18, 35, 16], [29, 21, 35, 19, "wrapper"], [29, 28, 35, 26], [29, 29, 35, 27, "implSymbol"], [29, 39, 35, 37], [29, 40, 35, 38], [29, 43, 35, 41], [29, 47, 35, 45], [30, 2, 36, 0], [31, 2, 38, 0], [31, 11, 38, 9, "tryWrapperForImpl"], [31, 28, 38, 26, "tryWrapperForImpl"], [31, 29, 38, 27, "impl"], [31, 33, 38, 31], [31, 35, 38, 33], [32, 4, 39, 2], [32, 8, 39, 8, "wrapper"], [32, 15, 39, 15], [32, 18, 39, 18, "wrapperForImpl"], [32, 32, 39, 32], [32, 33, 39, 33, "impl"], [32, 37, 39, 37], [32, 38, 39, 38], [33, 4, 40, 2], [33, 11, 40, 9, "wrapper"], [33, 18, 40, 16], [33, 21, 40, 19, "wrapper"], [33, 28, 40, 26], [33, 31, 40, 29, "impl"], [33, 35, 40, 33], [34, 2, 41, 0], [35, 2, 43, 0], [35, 11, 43, 9, "tryImplForWrapper"], [35, 28, 43, 26, "tryImplForWrapper"], [35, 29, 43, 27, "wrapper"], [35, 36, 43, 34], [35, 38, 43, 36], [36, 4, 44, 2], [36, 8, 44, 8, "impl"], [36, 12, 44, 12], [36, 15, 44, 15, "implForWrapper"], [36, 29, 44, 29], [36, 30, 44, 30, "wrapper"], [36, 37, 44, 37], [36, 38, 44, 38], [37, 4, 45, 2], [37, 11, 45, 9, "impl"], [37, 15, 45, 13], [37, 18, 45, 16, "impl"], [37, 22, 45, 20], [37, 25, 45, 23, "wrapper"], [37, 32, 45, 30], [38, 2, 46, 0], [39, 2, 48, 0], [39, 6, 48, 6, "iterInternalSymbol"], [39, 24, 48, 24], [39, 27, 48, 27, "Symbol"], [39, 33, 48, 33], [39, 34, 48, 34], [39, 44, 48, 44], [39, 45, 48, 45], [40, 2, 49, 0], [40, 6, 49, 6, "IteratorPrototype"], [40, 23, 49, 23], [40, 26, 49, 26, "Object"], [40, 32, 49, 32], [40, 33, 49, 33, "getPrototypeOf"], [40, 47, 49, 47], [40, 48, 49, 48, "Object"], [40, 54, 49, 54], [40, 55, 49, 55, "getPrototypeOf"], [40, 69, 49, 69], [40, 70, 49, 70], [40, 72, 49, 72], [40, 73, 49, 73, "Symbol"], [40, 79, 49, 79], [40, 80, 49, 80, "iterator"], [40, 88, 49, 88], [40, 89, 49, 89], [40, 90, 49, 90], [40, 91, 49, 91], [40, 92, 49, 92], [40, 93, 49, 93], [41, 2, 51, 0], [41, 11, 51, 9, "isArrayIndexPropName"], [41, 31, 51, 29, "isArrayIndexPropName"], [41, 32, 51, 30, "P"], [41, 33, 51, 31], [41, 35, 51, 33], [42, 4, 52, 2], [42, 8, 52, 6], [42, 15, 52, 13, "P"], [42, 16, 52, 14], [42, 21, 52, 19], [42, 29, 52, 27], [42, 31, 52, 29], [43, 6, 53, 4], [43, 13, 53, 11], [43, 18, 53, 16], [44, 4, 54, 2], [45, 4, 55, 2], [45, 8, 55, 8, "i"], [45, 9, 55, 9], [45, 12, 55, 12, "P"], [45, 13, 55, 13], [45, 18, 55, 18], [45, 19, 55, 19], [46, 4, 56, 2], [46, 8, 56, 6, "i"], [46, 9, 56, 7], [46, 14, 56, 12, "Math"], [46, 18, 56, 16], [46, 19, 56, 17, "pow"], [46, 22, 56, 20], [46, 23, 56, 21], [46, 24, 56, 22], [46, 26, 56, 24], [46, 28, 56, 26], [46, 29, 56, 27], [46, 32, 56, 30], [46, 33, 56, 31], [46, 35, 56, 33], [47, 6, 57, 4], [47, 13, 57, 11], [47, 18, 57, 16], [48, 4, 58, 2], [49, 4, 59, 2], [49, 8, 59, 8, "s"], [49, 9, 59, 9], [49, 12, 59, 12], [49, 15, 59, 15, "i"], [49, 16, 59, 16], [49, 18, 59, 18], [50, 4, 60, 2], [50, 8, 60, 6, "P"], [50, 9, 60, 7], [50, 14, 60, 12, "s"], [50, 15, 60, 13], [50, 17, 60, 15], [51, 6, 61, 4], [51, 13, 61, 11], [51, 18, 61, 16], [52, 4, 62, 2], [53, 4, 63, 2], [53, 11, 63, 9], [53, 15, 63, 13], [54, 2, 64, 0], [55, 2, 66, 0], [55, 6, 66, 6, "byteLengthGetter"], [55, 22, 66, 22], [55, 25, 67, 4, "Object"], [55, 31, 67, 10], [55, 32, 67, 11, "getOwnPropertyDescriptor"], [55, 56, 67, 35], [55, 57, 67, 36, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [55, 68, 67, 47], [55, 69, 67, 48, "prototype"], [55, 78, 67, 57], [55, 80, 67, 59], [55, 92, 67, 71], [55, 93, 67, 72], [55, 94, 67, 73, "get"], [55, 97, 67, 76], [56, 2, 68, 0], [56, 11, 68, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [56, 24, 68, 22, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [56, 25, 68, 23, "value"], [56, 30, 68, 28], [56, 32, 68, 30], [57, 4, 69, 2], [57, 8, 69, 6], [58, 6, 70, 4, "byteLengthGetter"], [58, 22, 70, 20], [58, 23, 70, 21, "call"], [58, 27, 70, 25], [58, 28, 70, 26, "value"], [58, 33, 70, 31], [58, 34, 70, 32], [59, 6, 71, 4], [59, 13, 71, 11], [59, 17, 71, 15], [60, 4, 72, 2], [60, 5, 72, 3], [60, 6, 72, 4], [60, 13, 72, 11, "e"], [60, 14, 72, 12], [60, 16, 72, 14], [61, 6, 73, 4], [61, 13, 73, 11], [61, 18, 73, 16], [62, 4, 74, 2], [63, 2, 75, 0], [64, 2, 77, 0], [64, 6, 77, 6, "supportsPropertyIndex"], [64, 27, 77, 27], [64, 30, 77, 30, "Symbol"], [64, 36, 77, 36], [64, 37, 77, 37], [64, 62, 77, 62], [64, 63, 77, 63], [65, 2, 78, 0], [65, 6, 78, 6, "supportedPropertyIndices"], [65, 30, 78, 30], [65, 33, 78, 33, "Symbol"], [65, 39, 78, 39], [65, 40, 78, 40], [65, 68, 78, 68], [65, 69, 78, 69], [66, 2, 79, 0], [66, 6, 79, 6, "supportsPropertyName"], [66, 26, 79, 26], [66, 29, 79, 29, "Symbol"], [66, 35, 79, 35], [66, 36, 79, 36], [66, 60, 79, 60], [66, 61, 79, 61], [67, 2, 80, 0], [67, 6, 80, 6, "supportedPropertyNames"], [67, 28, 80, 28], [67, 31, 80, 31, "Symbol"], [67, 37, 80, 37], [67, 38, 80, 38], [67, 64, 80, 64], [67, 65, 80, 65], [68, 2, 81, 0], [68, 6, 81, 6, "indexedGet"], [68, 16, 81, 16], [68, 19, 81, 19, "Symbol"], [68, 25, 81, 25], [68, 26, 81, 26], [68, 48, 81, 48], [68, 49, 81, 49], [69, 2, 82, 0], [69, 6, 82, 6, "indexedSetNew"], [69, 19, 82, 19], [69, 22, 82, 22, "Symbol"], [69, 28, 82, 28], [69, 29, 82, 29], [69, 55, 82, 55], [69, 56, 82, 56], [70, 2, 83, 0], [70, 6, 83, 6, "indexedSetExisting"], [70, 24, 83, 24], [70, 27, 83, 27, "Symbol"], [70, 33, 83, 33], [70, 34, 83, 34], [70, 65, 83, 65], [70, 66, 83, 66], [71, 2, 84, 0], [71, 6, 84, 6, "namedGet"], [71, 14, 84, 14], [71, 17, 84, 17, "Symbol"], [71, 23, 84, 23], [71, 24, 84, 24], [71, 44, 84, 44], [71, 45, 84, 45], [72, 2, 85, 0], [72, 6, 85, 6, "namedSetNew"], [72, 17, 85, 17], [72, 20, 85, 20, "Symbol"], [72, 26, 85, 26], [72, 27, 85, 27], [72, 51, 85, 51], [72, 52, 85, 52], [73, 2, 86, 0], [73, 6, 86, 6, "namedSetExisting"], [73, 22, 86, 22], [73, 25, 86, 25, "Symbol"], [73, 31, 86, 31], [73, 32, 86, 32], [73, 61, 86, 61], [73, 62, 86, 62], [74, 2, 87, 0], [74, 6, 87, 6, "namedDelete"], [74, 17, 87, 17], [74, 20, 87, 20, "Symbol"], [74, 26, 87, 26], [74, 27, 87, 27], [74, 50, 87, 50], [74, 51, 87, 51], [75, 2, 89, 0, "module"], [75, 8, 89, 6], [75, 9, 89, 7, "exports"], [75, 16, 89, 14], [75, 19, 89, 17, "exports"], [75, 26, 89, 24], [75, 29, 89, 27], [76, 4, 90, 2, "isObject"], [76, 12, 90, 10], [77, 4, 91, 2, "hasOwn"], [77, 10, 91, 8], [78, 4, 92, 2, "wrapperSymbol"], [78, 17, 92, 15], [79, 4, 93, 2, "implSymbol"], [79, 14, 93, 12], [80, 4, 94, 2, "getSameObject"], [80, 17, 94, 15], [81, 4, 95, 2, "ctorRegistrySymbol"], [81, 22, 95, 20], [82, 4, 96, 2, "wrapperForImpl"], [82, 18, 96, 16], [83, 4, 97, 2, "implForWrapper"], [83, 18, 97, 16], [84, 4, 98, 2, "tryWrapperForImpl"], [84, 21, 98, 19], [85, 4, 99, 2, "tryImplForWrapper"], [85, 21, 99, 19], [86, 4, 100, 2, "iterInternalSymbol"], [86, 22, 100, 20], [87, 4, 101, 2, "IteratorPrototype"], [87, 21, 101, 19], [88, 4, 102, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [88, 17, 102, 15], [89, 4, 103, 2, "isArrayIndexPropName"], [89, 24, 103, 22], [90, 4, 104, 2, "supportsPropertyIndex"], [90, 25, 104, 23], [91, 4, 105, 2, "supportedPropertyIndices"], [91, 28, 105, 26], [92, 4, 106, 2, "supportsPropertyName"], [92, 24, 106, 22], [93, 4, 107, 2, "supportedPropertyNames"], [93, 26, 107, 24], [94, 4, 108, 2, "indexedGet"], [94, 14, 108, 12], [95, 4, 109, 2, "indexedSetNew"], [95, 17, 109, 15], [96, 4, 110, 2, "indexedSetExisting"], [96, 22, 110, 20], [97, 4, 111, 2, "namedGet"], [97, 12, 111, 10], [98, 4, 112, 2, "namedSetNew"], [98, 15, 112, 13], [99, 4, 113, 2, "namedSetExisting"], [99, 20, 113, 18], [100, 4, 114, 2, "namedDelete"], [101, 2, 115, 0], [101, 3, 115, 1], [102, 0, 115, 2], [102, 3]], "functionMap": {"names": ["<global>", "isObject", "hasOwn", "getSameObject", "wrapperForImpl", "implForWrapper", "tryWrapperForImpl", "tryImplForWrapper", "isArrayIndexPropName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;ACG;CDE;AEE;CFE;AGO;CHW;AIE;CJE;AKE;CLE;AME;CNG;AOE;CPG;AQK;CRa;ASI;CTO"}}, "type": "js/module"}]}