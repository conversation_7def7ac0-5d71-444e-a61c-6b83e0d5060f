{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 3, "column": 23, "index": 100}, "end": {"line": 3, "column": 46, "index": 123}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var react_native_1 = require(_dependencyMap[0], \"react-native\");\n  function vw(value) {\n    var parsed = typeof value === \"number\" ? value : Number.parseFloat(value);\n    return react_native_1.Dimensions.get(\"window\").width * (parsed / 100);\n  }\n  exports.default = vw;\n});", "lineCount": 13, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0], [7, 6, 3, 6, "react_native_1"], [7, 20, 3, 20], [7, 23, 3, 23, "require"], [7, 30, 3, 30], [7, 31, 3, 30, "_dependencyMap"], [7, 45, 3, 30], [7, 64, 3, 45], [7, 65, 3, 46], [8, 2, 4, 0], [8, 11, 4, 9, "vw"], [8, 13, 4, 11, "vw"], [8, 14, 4, 12, "value"], [8, 19, 4, 17], [8, 21, 4, 19], [9, 4, 5, 4], [9, 8, 5, 10, "parsed"], [9, 14, 5, 16], [9, 17, 5, 19], [9, 24, 5, 26, "value"], [9, 29, 5, 31], [9, 34, 5, 36], [9, 42, 5, 44], [9, 45, 5, 47, "value"], [9, 50, 5, 52], [9, 53, 5, 55, "Number"], [9, 59, 5, 61], [9, 60, 5, 62, "parseFloat"], [9, 70, 5, 72], [9, 71, 5, 73, "value"], [9, 76, 5, 78], [9, 77, 5, 79], [10, 4, 6, 4], [10, 11, 6, 11, "react_native_1"], [10, 25, 6, 25], [10, 26, 6, 26, "Dimensions"], [10, 36, 6, 36], [10, 37, 6, 37, "get"], [10, 40, 6, 40], [10, 41, 6, 41], [10, 49, 6, 49], [10, 50, 6, 50], [10, 51, 6, 51, "width"], [10, 56, 6, 56], [10, 60, 6, 60, "parsed"], [10, 66, 6, 66], [10, 69, 6, 69], [10, 72, 6, 72], [10, 73, 6, 73], [11, 2, 7, 0], [12, 2, 8, 0, "exports"], [12, 9, 8, 7], [12, 10, 8, 8, "default"], [12, 17, 8, 15], [12, 20, 8, 18, "vw"], [12, 22, 8, 20], [13, 0, 8, 21], [13, 3]], "functionMap": {"names": ["<global>", "vw"], "mappings": "AAA;ACG;CDG"}}, "type": "js/module"}]}