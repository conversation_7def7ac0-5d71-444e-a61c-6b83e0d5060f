{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.TARGET_KEY = exports.STOP_PROPAGATION_FLAG_KEY = exports.STOP_IMMEDIATE_PROPAGATION_FLAG_KEY = exports.IS_TRUSTED_KEY = exports.IN_PASSIVE_LISTENER_FLAG_KEY = exports.EVENT_PHASE_KEY = exports.CURRENT_TARGET_KEY = exports.COMPOSED_PATH_KEY = void 0;\n  exports.getComposedPath = getComposedPath;\n  exports.getCurrentTarget = getCurrentTarget;\n  exports.getEventPhase = getEventPhase;\n  exports.getInPassiveListenerFlag = getInPassiveListenerFlag;\n  exports.getIsTrusted = getIsTrusted;\n  exports.getStopImmediatePropagationFlag = getStopImmediatePropagationFlag;\n  exports.getStopPropagationFlag = getStopPropagationFlag;\n  exports.getTarget = getTarget;\n  exports.setComposedPath = setComposedPath;\n  exports.setCurrentTarget = setCurrentTarget;\n  exports.setEventPhase = setEventPhase;\n  exports.setInPassiveListenerFlag = setInPassiveListenerFlag;\n  exports.setIsTrusted = setIsTrusted;\n  exports.setStopImmediatePropagationFlag = setStopImmediatePropagationFlag;\n  exports.setStopPropagationFlag = setStopPropagationFlag;\n  exports.setTarget = setTarget;\n  var COMPOSED_PATH_KEY = exports.COMPOSED_PATH_KEY = Symbol('composedPath');\n  var CURRENT_TARGET_KEY = exports.CURRENT_TARGET_KEY = Symbol('currentTarget');\n  var EVENT_PHASE_KEY = exports.EVENT_PHASE_KEY = Symbol('eventPhase');\n  var IN_PASSIVE_LISTENER_FLAG_KEY = exports.IN_PASSIVE_LISTENER_FLAG_KEY = Symbol('inPassiveListenerFlag');\n  var IS_TRUSTED_KEY = exports.IS_TRUSTED_KEY = Symbol('isTrusted');\n  var STOP_IMMEDIATE_PROPAGATION_FLAG_KEY = exports.STOP_IMMEDIATE_PROPAGATION_FLAG_KEY = Symbol('stopPropagationFlag');\n  var STOP_PROPAGATION_FLAG_KEY = exports.STOP_PROPAGATION_FLAG_KEY = Symbol('stopPropagationFlag');\n  var TARGET_KEY = exports.TARGET_KEY = Symbol('target');\n  function getCurrentTarget(event) {\n    return event[CURRENT_TARGET_KEY];\n  }\n  function setCurrentTarget(event, currentTarget) {\n    event[CURRENT_TARGET_KEY] = currentTarget;\n  }\n  function getComposedPath(event) {\n    return event[COMPOSED_PATH_KEY];\n  }\n  function setComposedPath(event, composedPath) {\n    event[COMPOSED_PATH_KEY] = composedPath;\n  }\n  function getEventPhase(event) {\n    return event[EVENT_PHASE_KEY];\n  }\n  function setEventPhase(event, eventPhase) {\n    event[EVENT_PHASE_KEY] = eventPhase;\n  }\n  function getInPassiveListenerFlag(event) {\n    return event[IN_PASSIVE_LISTENER_FLAG_KEY];\n  }\n  function setInPassiveListenerFlag(event, value) {\n    event[IN_PASSIVE_LISTENER_FLAG_KEY] = value;\n  }\n  function getIsTrusted(event) {\n    return event[IS_TRUSTED_KEY];\n  }\n  function setIsTrusted(event, isTrusted) {\n    event[IS_TRUSTED_KEY] = isTrusted;\n  }\n  function getStopImmediatePropagationFlag(event) {\n    return event[STOP_IMMEDIATE_PROPAGATION_FLAG_KEY];\n  }\n  function setStopImmediatePropagationFlag(event, value) {\n    event[STOP_IMMEDIATE_PROPAGATION_FLAG_KEY] = value;\n  }\n  function getStopPropagationFlag(event) {\n    return event[STOP_PROPAGATION_FLAG_KEY];\n  }\n  function setStopPropagationFlag(event, value) {\n    event[STOP_PROPAGATION_FLAG_KEY] = value;\n  }\n  function getTarget(event) {\n    return event[TARGET_KEY];\n  }\n  function setTarget(event, target) {\n    event[TARGET_KEY] = target;\n  }\n});", "lineCount": 78, "map": [[22, 2, 20, 7], [22, 6, 20, 13, "COMPOSED_PATH_KEY"], [22, 23, 20, 38], [22, 26, 20, 38, "exports"], [22, 33, 20, 38], [22, 34, 20, 38, "COMPOSED_PATH_KEY"], [22, 51, 20, 38], [22, 54, 20, 41, "Symbol"], [22, 60, 20, 47], [22, 61, 20, 48], [22, 75, 20, 62], [22, 76, 20, 63], [23, 2, 21, 7], [23, 6, 21, 13, "CURRENT_TARGET_KEY"], [23, 24, 21, 39], [23, 27, 21, 39, "exports"], [23, 34, 21, 39], [23, 35, 21, 39, "CURRENT_TARGET_KEY"], [23, 53, 21, 39], [23, 56, 21, 42, "Symbol"], [23, 62, 21, 48], [23, 63, 21, 49], [23, 78, 21, 64], [23, 79, 21, 65], [24, 2, 22, 7], [24, 6, 22, 13, "EVENT_PHASE_KEY"], [24, 21, 22, 36], [24, 24, 22, 36, "exports"], [24, 31, 22, 36], [24, 32, 22, 36, "EVENT_PHASE_KEY"], [24, 47, 22, 36], [24, 50, 22, 39, "Symbol"], [24, 56, 22, 45], [24, 57, 22, 46], [24, 69, 22, 58], [24, 70, 22, 59], [25, 2, 23, 7], [25, 6, 23, 13, "IN_PASSIVE_LISTENER_FLAG_KEY"], [25, 34, 23, 49], [25, 37, 23, 49, "exports"], [25, 44, 23, 49], [25, 45, 23, 49, "IN_PASSIVE_LISTENER_FLAG_KEY"], [25, 73, 23, 49], [25, 76, 23, 52, "Symbol"], [25, 82, 23, 58], [25, 83, 24, 2], [25, 106, 25, 0], [25, 107, 25, 1], [26, 2, 26, 7], [26, 6, 26, 13, "IS_TRUSTED_KEY"], [26, 20, 26, 35], [26, 23, 26, 35, "exports"], [26, 30, 26, 35], [26, 31, 26, 35, "IS_TRUSTED_KEY"], [26, 45, 26, 35], [26, 48, 26, 38, "Symbol"], [26, 54, 26, 44], [26, 55, 26, 45], [26, 66, 26, 56], [26, 67, 26, 57], [27, 2, 27, 7], [27, 6, 27, 13, "STOP_IMMEDIATE_PROPAGATION_FLAG_KEY"], [27, 41, 27, 56], [27, 44, 27, 56, "exports"], [27, 51, 27, 56], [27, 52, 27, 56, "STOP_IMMEDIATE_PROPAGATION_FLAG_KEY"], [27, 87, 27, 56], [27, 90, 27, 59, "Symbol"], [27, 96, 27, 65], [27, 97, 28, 2], [27, 118, 29, 0], [27, 119, 29, 1], [28, 2, 30, 7], [28, 6, 30, 13, "STOP_PROPAGATION_FLAG_KEY"], [28, 31, 30, 46], [28, 34, 30, 46, "exports"], [28, 41, 30, 46], [28, 42, 30, 46, "STOP_PROPAGATION_FLAG_KEY"], [28, 67, 30, 46], [28, 70, 30, 49, "Symbol"], [28, 76, 30, 55], [28, 77, 30, 56], [28, 98, 30, 77], [28, 99, 30, 78], [29, 2, 31, 7], [29, 6, 31, 13, "TARGET_KEY"], [29, 16, 31, 31], [29, 19, 31, 31, "exports"], [29, 26, 31, 31], [29, 27, 31, 31, "TARGET_KEY"], [29, 37, 31, 31], [29, 40, 31, 34, "Symbol"], [29, 46, 31, 40], [29, 47, 31, 41], [29, 55, 31, 49], [29, 56, 31, 50], [30, 2, 33, 7], [30, 11, 33, 16, "getC<PERSON>rentTarget"], [30, 27, 33, 32, "getC<PERSON>rentTarget"], [30, 28, 33, 33, "event"], [30, 33, 33, 45], [30, 35, 33, 67], [31, 4, 35, 2], [31, 11, 35, 9, "event"], [31, 16, 35, 14], [31, 17, 35, 15, "CURRENT_TARGET_KEY"], [31, 35, 35, 33], [31, 36, 35, 34], [32, 2, 36, 0], [33, 2, 38, 7], [33, 11, 38, 16, "set<PERSON><PERSON><PERSON><PERSON>arget"], [33, 27, 38, 32, "set<PERSON><PERSON><PERSON><PERSON>arget"], [33, 28, 39, 2, "event"], [33, 33, 39, 14], [33, 35, 40, 2, "currentTarget"], [33, 48, 40, 35], [33, 50, 41, 8], [34, 4, 43, 2, "event"], [34, 9, 43, 7], [34, 10, 43, 8, "CURRENT_TARGET_KEY"], [34, 28, 43, 26], [34, 29, 43, 27], [34, 32, 43, 30, "currentTarget"], [34, 45, 43, 43], [35, 2, 44, 0], [36, 2, 46, 7], [36, 11, 46, 16, "getComposedPath"], [36, 26, 46, 31, "getComposedPath"], [36, 27, 46, 32, "event"], [36, 32, 46, 44], [36, 34, 46, 75], [37, 4, 48, 2], [37, 11, 48, 9, "event"], [37, 16, 48, 14], [37, 17, 48, 15, "COMPOSED_PATH_KEY"], [37, 34, 48, 32], [37, 35, 48, 33], [38, 2, 49, 0], [39, 2, 51, 7], [39, 11, 51, 16, "setComposedPath"], [39, 26, 51, 31, "setComposedPath"], [39, 27, 52, 2, "event"], [39, 32, 52, 14], [39, 34, 53, 2, "<PERSON><PERSON><PERSON>"], [39, 46, 53, 43], [39, 48, 54, 8], [40, 4, 56, 2, "event"], [40, 9, 56, 7], [40, 10, 56, 8, "COMPOSED_PATH_KEY"], [40, 27, 56, 25], [40, 28, 56, 26], [40, 31, 56, 29, "<PERSON><PERSON><PERSON>"], [40, 43, 56, 41], [41, 2, 57, 0], [42, 2, 59, 7], [42, 11, 59, 16, "getEventPhase"], [42, 24, 59, 29, "getEventPhase"], [42, 25, 59, 30, "event"], [42, 30, 59, 42], [42, 32, 59, 56], [43, 4, 61, 2], [43, 11, 61, 9, "event"], [43, 16, 61, 14], [43, 17, 61, 15, "EVENT_PHASE_KEY"], [43, 32, 61, 30], [43, 33, 61, 31], [44, 2, 62, 0], [45, 2, 64, 7], [45, 11, 64, 16, "setEventPhase"], [45, 24, 64, 29, "setEventPhase"], [45, 25, 64, 30, "event"], [45, 30, 64, 42], [45, 32, 64, 44, "eventPhase"], [45, 42, 64, 66], [45, 44, 64, 74], [46, 4, 66, 2, "event"], [46, 9, 66, 7], [46, 10, 66, 8, "EVENT_PHASE_KEY"], [46, 25, 66, 23], [46, 26, 66, 24], [46, 29, 66, 27, "eventPhase"], [46, 39, 66, 37], [47, 2, 67, 0], [48, 2, 69, 7], [48, 11, 69, 16, "getInPassiveListenerFlag"], [48, 35, 69, 40, "getInPassiveListenerFlag"], [48, 36, 69, 41, "event"], [48, 41, 69, 53], [48, 43, 69, 64], [49, 4, 71, 2], [49, 11, 71, 9, "event"], [49, 16, 71, 14], [49, 17, 71, 15, "IN_PASSIVE_LISTENER_FLAG_KEY"], [49, 45, 71, 43], [49, 46, 71, 44], [50, 2, 72, 0], [51, 2, 74, 7], [51, 11, 74, 16, "setInPassiveListenerFlag"], [51, 35, 74, 40, "setInPassiveListenerFlag"], [51, 36, 74, 41, "event"], [51, 41, 74, 53], [51, 43, 74, 55, "value"], [51, 48, 74, 69], [51, 50, 74, 77], [52, 4, 76, 2, "event"], [52, 9, 76, 7], [52, 10, 76, 8, "IN_PASSIVE_LISTENER_FLAG_KEY"], [52, 38, 76, 36], [52, 39, 76, 37], [52, 42, 76, 40, "value"], [52, 47, 76, 45], [53, 2, 77, 0], [54, 2, 79, 7], [54, 11, 79, 16, "getIsTrusted"], [54, 23, 79, 28, "getIsTrusted"], [54, 24, 79, 29, "event"], [54, 29, 79, 41], [54, 31, 79, 52], [55, 4, 81, 2], [55, 11, 81, 9, "event"], [55, 16, 81, 14], [55, 17, 81, 15, "IS_TRUSTED_KEY"], [55, 31, 81, 29], [55, 32, 81, 30], [56, 2, 82, 0], [57, 2, 84, 7], [57, 11, 84, 16, "setIsTrusted"], [57, 23, 84, 28, "setIsTrusted"], [57, 24, 84, 29, "event"], [57, 29, 84, 41], [57, 31, 84, 43, "isTrusted"], [57, 40, 84, 61], [57, 42, 84, 69], [58, 4, 86, 2, "event"], [58, 9, 86, 7], [58, 10, 86, 8, "IS_TRUSTED_KEY"], [58, 24, 86, 22], [58, 25, 86, 23], [58, 28, 86, 26, "isTrusted"], [58, 37, 86, 35], [59, 2, 87, 0], [60, 2, 89, 7], [60, 11, 89, 16, "getStopImmediatePropagationFlag"], [60, 42, 89, 47, "getStopImmediatePropagationFlag"], [60, 43, 89, 48, "event"], [60, 48, 89, 60], [60, 50, 89, 71], [61, 4, 91, 2], [61, 11, 91, 9, "event"], [61, 16, 91, 14], [61, 17, 91, 15, "STOP_IMMEDIATE_PROPAGATION_FLAG_KEY"], [61, 52, 91, 50], [61, 53, 91, 51], [62, 2, 92, 0], [63, 2, 94, 7], [63, 11, 94, 16, "setStopImmediatePropagationFlag"], [63, 42, 94, 47, "setStopImmediatePropagationFlag"], [63, 43, 95, 2, "event"], [63, 48, 95, 14], [63, 50, 96, 2, "value"], [63, 55, 96, 16], [63, 57, 97, 8], [64, 4, 99, 2, "event"], [64, 9, 99, 7], [64, 10, 99, 8, "STOP_IMMEDIATE_PROPAGATION_FLAG_KEY"], [64, 45, 99, 43], [64, 46, 99, 44], [64, 49, 99, 47, "value"], [64, 54, 99, 52], [65, 2, 100, 0], [66, 2, 102, 7], [66, 11, 102, 16, "getStopPropagationFlag"], [66, 33, 102, 38, "getStopPropagationFlag"], [66, 34, 102, 39, "event"], [66, 39, 102, 51], [66, 41, 102, 62], [67, 4, 104, 2], [67, 11, 104, 9, "event"], [67, 16, 104, 14], [67, 17, 104, 15, "STOP_PROPAGATION_FLAG_KEY"], [67, 42, 104, 40], [67, 43, 104, 41], [68, 2, 105, 0], [69, 2, 107, 7], [69, 11, 107, 16, "setStopPropagationFlag"], [69, 33, 107, 38, "setStopPropagationFlag"], [69, 34, 107, 39, "event"], [69, 39, 107, 51], [69, 41, 107, 53, "value"], [69, 46, 107, 67], [69, 48, 107, 75], [70, 4, 109, 2, "event"], [70, 9, 109, 7], [70, 10, 109, 8, "STOP_PROPAGATION_FLAG_KEY"], [70, 35, 109, 33], [70, 36, 109, 34], [70, 39, 109, 37, "value"], [70, 44, 109, 42], [71, 2, 110, 0], [72, 2, 112, 7], [72, 11, 112, 16, "get<PERSON><PERSON><PERSON>"], [72, 20, 112, 25, "get<PERSON><PERSON><PERSON>"], [72, 21, 112, 26, "event"], [72, 26, 112, 38], [72, 28, 112, 60], [73, 4, 114, 2], [73, 11, 114, 9, "event"], [73, 16, 114, 14], [73, 17, 114, 15, "TARGET_KEY"], [73, 27, 114, 25], [73, 28, 114, 26], [74, 2, 115, 0], [75, 2, 117, 7], [75, 11, 117, 16, "<PERSON><PERSON><PERSON><PERSON>"], [75, 20, 117, 25, "<PERSON><PERSON><PERSON><PERSON>"], [75, 21, 117, 26, "event"], [75, 26, 117, 38], [75, 28, 117, 40, "target"], [75, 34, 117, 66], [75, 36, 117, 74], [76, 4, 119, 2, "event"], [76, 9, 119, 7], [76, 10, 119, 8, "TARGET_KEY"], [76, 20, 119, 18], [76, 21, 119, 19], [76, 24, 119, 22, "target"], [76, 30, 119, 28], [77, 2, 120, 0], [78, 0, 120, 1], [78, 3]], "functionMap": {"names": ["<global>", "getC<PERSON>rentTarget", "set<PERSON><PERSON><PERSON><PERSON>arget", "getComposedPath", "setComposedPath", "getEventPhase", "setEventPhase", "getInPassiveListenerFlag", "setInPassiveListenerFlag", "getIsTrusted", "setIsTrusted", "getStopImmediatePropagationFlag", "setStopImmediatePropagationFlag", "getStopPropagationFlag", "setStopPropagationFlag", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;OCgC;CDG;OEE;CFM;OGE;CHG;OIE;CJM;OKE;CLG;OME;CNG;OOE;CPG;OQE;CRG;OSE;CTG;OUE;CVG;OWE;CXG;OYE;CZM;OaE;CbG;OcE;CdG;OeE;CfG;OgBE"}}, "type": "js/module"}]}