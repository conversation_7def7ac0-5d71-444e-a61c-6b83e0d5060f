{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Components/View/View", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 13}, "end": {"line": 18, "column": 63}}], "key": "H/3fvmiHyIdASS62Hfb3a4a54KU=", "exportNames": ["*"]}}, {"name": "../../../Libraries/StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 19}, "end": {"line": 19, "column": 70}}], "key": "Nurrv5y9ebtgGhUjBt0E/GEpaGk=", "exportNames": ["*"]}}, {"name": "./ElementBox", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 20, "column": 19}, "end": {"line": 20, "column": 42}}], "key": "g8YZehp3q+UEa0fRQTevNMs9xdU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[2], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\src\\\\private\\\\inspector\\\\InspectorOverlay.js\";\n  var View = require(_dependencyMap[3], \"../../../Libraries/Components/View/View\").default;\n  var StyleSheet = require(_dependencyMap[4], \"../../../Libraries/StyleSheet/StyleSheet\").default;\n  var ElementBox = require(_dependencyMap[5], \"./ElementBox\").default;\n  function InspectorOverlay(_ref) {\n    var inspected = _ref.inspected,\n      onTouchPoint = _ref.onTouchPoint;\n    var findViewForTouchEvent = e => {\n      var _e$nativeEvent$touche = e.nativeEvent.touches[0],\n        locationX = _e$nativeEvent$touche.locationX,\n        locationY = _e$nativeEvent$touche.locationY;\n      onTouchPoint(locationX, locationY);\n    };\n    var handleStartShouldSetResponder = e => {\n      findViewForTouchEvent(e);\n      return true;\n    };\n    var content = null;\n    if (inspected) {\n      content = /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ElementBox, {\n        frame: inspected.frame,\n        style: inspected.style\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 15\n      }, this);\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(View, {\n      onStartShouldSetResponder: handleStartShouldSetResponder,\n      onResponderMove: findViewForTouchEvent,\n      nativeID: \"inspectorOverlay\",\n      style: styles.inspector,\n      children: content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = StyleSheet.create({\n    inspector: {\n      backgroundColor: 'transparent',\n      position: 'absolute',\n      left: 0,\n      top: 0,\n      right: 0,\n      bottom: 0\n    }\n  });\n  var _default = exports.default = InspectorOverlay;\n});", "lineCount": 62, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 16, 0], [9, 6, 16, 0, "_react"], [9, 12, 16, 0], [9, 15, 16, 0, "_interopRequireDefault"], [9, 37, 16, 0], [9, 38, 16, 0, "require"], [9, 45, 16, 0], [9, 46, 16, 0, "_dependencyMap"], [9, 60, 16, 0], [10, 2, 16, 26], [10, 6, 16, 26, "_jsxDevRuntime"], [10, 20, 16, 26], [10, 23, 16, 26, "require"], [10, 30, 16, 26], [10, 31, 16, 26, "_dependencyMap"], [10, 45, 16, 26], [11, 2, 16, 26], [11, 6, 16, 26, "_jsxFileName"], [11, 18, 16, 26], [12, 2, 18, 0], [12, 6, 18, 6, "View"], [12, 10, 18, 10], [12, 13, 18, 13, "require"], [12, 20, 18, 20], [12, 21, 18, 20, "_dependencyMap"], [12, 35, 18, 20], [12, 81, 18, 62], [12, 82, 18, 63], [12, 83, 18, 64, "default"], [12, 90, 18, 71], [13, 2, 19, 0], [13, 6, 19, 6, "StyleSheet"], [13, 16, 19, 16], [13, 19, 19, 19, "require"], [13, 26, 19, 26], [13, 27, 19, 26, "_dependencyMap"], [13, 41, 19, 26], [13, 88, 19, 69], [13, 89, 19, 70], [13, 90, 19, 71, "default"], [13, 97, 19, 78], [14, 2, 20, 0], [14, 6, 20, 6, "ElementBox"], [14, 16, 20, 16], [14, 19, 20, 19, "require"], [14, 26, 20, 26], [14, 27, 20, 26, "_dependencyMap"], [14, 41, 20, 26], [14, 60, 20, 41], [14, 61, 20, 42], [14, 62, 20, 43, "default"], [14, 69, 20, 50], [15, 2, 27, 0], [15, 11, 27, 9, "<PERSON><PERSON><PERSON><PERSON>"], [15, 27, 27, 25, "<PERSON><PERSON><PERSON><PERSON>"], [15, 28, 27, 25, "_ref"], [15, 32, 27, 25], [15, 34, 27, 72], [16, 4, 27, 72], [16, 8, 27, 27, "inspected"], [16, 17, 27, 36], [16, 20, 27, 36, "_ref"], [16, 24, 27, 36], [16, 25, 27, 27, "inspected"], [16, 34, 27, 36], [17, 6, 27, 38, "onTouchPoint"], [17, 18, 27, 50], [17, 21, 27, 50, "_ref"], [17, 25, 27, 50], [17, 26, 27, 38, "onTouchPoint"], [17, 38, 27, 50], [18, 4, 28, 2], [18, 8, 28, 8, "findViewForTouchEvent"], [18, 29, 28, 29], [18, 32, 28, 33, "e"], [18, 33, 28, 57], [18, 37, 28, 62], [19, 6, 29, 4], [19, 10, 29, 4, "_e$nativeEvent$touche"], [19, 31, 29, 4], [19, 34, 29, 35, "e"], [19, 35, 29, 36], [19, 36, 29, 37, "nativeEvent"], [19, 47, 29, 48], [19, 48, 29, 49, "touches"], [19, 55, 29, 56], [19, 56, 29, 57], [19, 57, 29, 58], [19, 58, 29, 59], [20, 8, 29, 11, "locationX"], [20, 17, 29, 20], [20, 20, 29, 20, "_e$nativeEvent$touche"], [20, 41, 29, 20], [20, 42, 29, 11, "locationX"], [20, 51, 29, 20], [21, 8, 29, 22, "locationY"], [21, 17, 29, 31], [21, 20, 29, 31, "_e$nativeEvent$touche"], [21, 41, 29, 31], [21, 42, 29, 22, "locationY"], [21, 51, 29, 31], [22, 6, 31, 4, "onTouchPoint"], [22, 18, 31, 16], [22, 19, 31, 17, "locationX"], [22, 28, 31, 26], [22, 30, 31, 28, "locationY"], [22, 39, 31, 37], [22, 40, 31, 38], [23, 4, 32, 2], [23, 5, 32, 3], [24, 4, 34, 2], [24, 8, 34, 8, "handleStartShouldSetResponder"], [24, 37, 34, 37], [24, 40, 34, 41, "e"], [24, 41, 34, 65], [24, 45, 34, 79], [25, 6, 35, 4, "findViewForTouchEvent"], [25, 27, 35, 25], [25, 28, 35, 26, "e"], [25, 29, 35, 27], [25, 30, 35, 28], [26, 6, 36, 4], [26, 13, 36, 11], [26, 17, 36, 15], [27, 4, 37, 2], [27, 5, 37, 3], [28, 4, 39, 2], [28, 8, 39, 6, "content"], [28, 15, 39, 13], [28, 18, 39, 16], [28, 22, 39, 20], [29, 4, 40, 2], [29, 8, 40, 6, "inspected"], [29, 17, 40, 15], [29, 19, 40, 17], [30, 6, 41, 4, "content"], [30, 13, 41, 11], [30, 29, 41, 14], [30, 33, 41, 14, "_jsxDevRuntime"], [30, 47, 41, 14], [30, 48, 41, 14, "jsxDEV"], [30, 54, 41, 14], [30, 56, 41, 15, "ElementBox"], [30, 66, 41, 25], [31, 8, 41, 26, "frame"], [31, 13, 41, 31], [31, 15, 41, 33, "inspected"], [31, 24, 41, 42], [31, 25, 41, 43, "frame"], [31, 30, 41, 49], [32, 8, 41, 50, "style"], [32, 13, 41, 55], [32, 15, 41, 57, "inspected"], [32, 24, 41, 66], [32, 25, 41, 67, "style"], [33, 6, 41, 73], [34, 8, 41, 73, "fileName"], [34, 16, 41, 73], [34, 18, 41, 73, "_jsxFileName"], [34, 30, 41, 73], [35, 8, 41, 73, "lineNumber"], [35, 18, 41, 73], [36, 8, 41, 73, "columnNumber"], [36, 20, 41, 73], [37, 6, 41, 73], [37, 13, 41, 75], [37, 14, 41, 76], [38, 4, 42, 2], [39, 4, 44, 2], [39, 24, 45, 4], [39, 28, 45, 4, "_jsxDevRuntime"], [39, 42, 45, 4], [39, 43, 45, 4, "jsxDEV"], [39, 49, 45, 4], [39, 51, 45, 5, "View"], [39, 55, 45, 9], [40, 6, 46, 6, "onStartShouldSetResponder"], [40, 31, 46, 31], [40, 33, 46, 33, "handleStartShouldSetResponder"], [40, 62, 46, 63], [41, 6, 47, 6, "onResponderMove"], [41, 21, 47, 21], [41, 23, 47, 23, "findViewForTouchEvent"], [41, 44, 47, 45], [42, 6, 48, 6, "nativeID"], [42, 14, 48, 14], [42, 16, 48, 15], [42, 34, 48, 33], [43, 6, 49, 6, "style"], [43, 11, 49, 11], [43, 13, 49, 13, "styles"], [43, 19, 49, 19], [43, 20, 49, 20, "inspector"], [43, 29, 49, 30], [44, 6, 49, 30, "children"], [44, 14, 49, 30], [44, 16, 50, 7, "content"], [45, 4, 50, 14], [46, 6, 50, 14, "fileName"], [46, 14, 50, 14], [46, 16, 50, 14, "_jsxFileName"], [46, 28, 50, 14], [47, 6, 50, 14, "lineNumber"], [47, 16, 50, 14], [48, 6, 50, 14, "columnNumber"], [48, 18, 50, 14], [49, 4, 50, 14], [49, 11, 51, 10], [49, 12, 51, 11], [50, 2, 53, 0], [51, 2, 55, 0], [51, 6, 55, 6, "styles"], [51, 12, 55, 12], [51, 15, 55, 15, "StyleSheet"], [51, 25, 55, 25], [51, 26, 55, 26, "create"], [51, 32, 55, 32], [51, 33, 55, 33], [52, 4, 56, 2, "inspector"], [52, 13, 56, 11], [52, 15, 56, 13], [53, 6, 57, 4, "backgroundColor"], [53, 21, 57, 19], [53, 23, 57, 21], [53, 36, 57, 34], [54, 6, 58, 4, "position"], [54, 14, 58, 12], [54, 16, 58, 14], [54, 26, 58, 24], [55, 6, 59, 4, "left"], [55, 10, 59, 8], [55, 12, 59, 10], [55, 13, 59, 11], [56, 6, 60, 4, "top"], [56, 9, 60, 7], [56, 11, 60, 9], [56, 12, 60, 10], [57, 6, 61, 4, "right"], [57, 11, 61, 9], [57, 13, 61, 11], [57, 14, 61, 12], [58, 6, 62, 4, "bottom"], [58, 12, 62, 10], [58, 14, 62, 12], [59, 4, 63, 2], [60, 2, 64, 0], [60, 3, 64, 1], [60, 4, 64, 2], [61, 2, 64, 3], [61, 6, 64, 3, "_default"], [61, 14, 64, 3], [61, 17, 64, 3, "exports"], [61, 24, 64, 3], [61, 25, 64, 3, "default"], [61, 32, 64, 3], [61, 35, 66, 15, "<PERSON><PERSON><PERSON><PERSON>"], [61, 51, 66, 31], [62, 0, 66, 31], [62, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON>", "findViewForTouchEvent", "handleStartShouldSetResponder"], "mappings": "AAA;AC0B;gCCC;GDI;wCEE;GFG;CDgB"}}, "type": "js/module"}]}