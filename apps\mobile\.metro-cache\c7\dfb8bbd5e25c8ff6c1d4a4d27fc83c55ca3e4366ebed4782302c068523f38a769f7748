{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "nativewind", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "cmUKQSXJyC7fmRcHKtmYzlG9LzY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 42}, "end": {"line": 10, "column": 22, "index": 166}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 168}, "end": {"line": 11, "column": 62, "index": 230}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo/vector-icons", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 232}, "end": {"line": 12, "column": 46, "index": 278}}], "key": "ow7vkrqkIckRjlSi/+MhMmRYtUE=", "exportNames": ["*"]}}, {"name": "../components/MobileHeader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 280}, "end": {"line": 13, "column": 54, "index": 334}}], "key": "BQ/TXH6JzdXsH7iz7EzLq7Zpaks=", "exportNames": ["*"]}}, {"name": "../components/FooterNavigation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 336}, "end": {"line": 14, "column": 62, "index": 398}}], "key": "i0wsjvqzeC9AyK9fcghZiqZFu6Y=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = HomeScreen;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _nativewind = require(_dependencyMap[2], \"nativewind\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[5], \"react-native-safe-area-context\");\n  var _vectorIcons = require(_dependencyMap[6], \"@expo/vector-icons\");\n  var _MobileHeader = _interopRequireDefault(require(_dependencyMap[7], \"../components/MobileHeader\"));\n  var _FooterNavigation = _interopRequireDefault(require(_dependencyMap[8], \"../components/FooterNavigation\"));\n  var _jsxDevRuntime = require(_dependencyMap[9], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\apps\\\\mobile\\\\src\\\\screens\\\\HomeScreen.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var _Dimensions$get = _reactNative.Dimensions.get('window'),\n    width = _Dimensions$get.width;\n\n  // Mock data for UberEats-style content\n  var categories = [{\n    id: '1',\n    name: 'Fast Food',\n    icon: '🍔',\n    color: '#FF6B6B'\n  }, {\n    id: '2',\n    name: 'Pizza',\n    icon: '🍕',\n    color: '#4ECDC4'\n  }, {\n    id: '3',\n    name: 'Asian',\n    icon: '🍜',\n    color: '#45B7D1'\n  }, {\n    id: '4',\n    name: 'Desserts',\n    icon: '🍰',\n    color: '#96CEB4'\n  }, {\n    id: '5',\n    name: 'Coffee',\n    icon: '☕',\n    color: '#FFEAA7'\n  }, {\n    id: '6',\n    name: 'Healthy',\n    icon: '🥗',\n    color: '#DDA0DD'\n  }];\n  var featuredRestaurants = [{\n    id: '1',\n    name: 'McDonald\\'s',\n    image: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=200&fit=crop',\n    rating: 4.5,\n    deliveryTime: '15-25 min',\n    deliveryFee: '₱29',\n    tags: ['Fast Food', 'Burgers'],\n    promo: '20% OFF'\n  }, {\n    id: '2',\n    name: 'Jollibee',\n    image: 'https://images.unsplash.com/photo-1586190848861-99aa4a171e90?w=400&h=200&fit=crop',\n    rating: 4.7,\n    deliveryTime: '20-30 min',\n    deliveryFee: '₱35',\n    tags: ['Fast Food', 'Filipino'],\n    promo: 'Free Delivery'\n  }, {\n    id: '3',\n    name: 'Pizza Hut',\n    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=200&fit=crop',\n    rating: 4.3,\n    deliveryTime: '25-35 min',\n    deliveryFee: '₱45',\n    tags: ['Pizza', 'Italian'],\n    promo: 'Buy 1 Take 1'\n  }];\n  var promoOffers = [{\n    id: '1',\n    title: 'Flash Sale!',\n    subtitle: 'Up to 50% OFF on selected restaurants',\n    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=150&fit=crop',\n    color: '#FF6B6B'\n  }, {\n    id: '2',\n    title: 'Free Delivery',\n    subtitle: 'No delivery fee for orders above ₱500',\n    image: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=150&fit=crop',\n    color: '#4ECDC4'\n  }];\n  function HomeScreen(_ref) {\n    _s();\n    var navigation = _ref.navigation;\n    var _useState = (0, _react.useState)(''),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      searchQuery = _useState2[0],\n      setSearchQuery = _useState2[1];\n    var handleNotificationPress = () => {\n      navigation.navigate('Notifications');\n    };\n    var handleWishlistPress = () => {\n      navigation.navigate('Wishlist');\n    };\n    var renderCategory = _ref2 => {\n      var item = _ref2.item;\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n        className: \"items-center mr-4\",\n        component: _reactNative.TouchableOpacity,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n          className: \"w-16 h-16 rounded-full items-center justify-center mb-2\",\n          style: {\n            backgroundColor: item.color + '20'\n          },\n          component: _reactNative.View,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            className: \"text-2xl\",\n            component: _reactNative.Text,\n            children: item.icon\n          }, void 0, false, void 0, this)\n        }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n          className: \"text-xs text-gray-600 text-center font-medium\",\n          component: _reactNative.Text,\n          children: item.name\n        }, void 0, false, void 0, this)]\n      }, void 0, true, void 0, this);\n    };\n    var renderRestaurant = _ref3 => {\n      var item = _ref3.item;\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n        className: \"bg-white rounded-xl mr-4 shadow-sm\",\n        style: {\n          width: width * 0.75\n        },\n        component: _reactNative.TouchableOpacity,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n          className: \"relative\",\n          component: _reactNative.View,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            source: {\n              uri: item.image\n            },\n            className: \"w-full h-32 rounded-t-xl\",\n            resizeMode: \"cover\",\n            component: _reactNative.Image\n          }, void 0, false, void 0, this), item.promo && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            className: \"absolute top-2 left-2 bg-red-500 px-2 py-1 rounded\",\n            component: _reactNative.View,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"text-white text-xs font-bold\",\n              component: _reactNative.Text,\n              children: item.promo\n            }, void 0, false, void 0, this)\n          }, void 0, false, void 0, this)]\n        }, void 0, true, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n          className: \"p-3\",\n          component: _reactNative.View,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            className: \"font-bold text-gray-900 text-base mb-1\",\n            component: _reactNative.Text,\n            children: item.name\n          }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            className: \"flex-row items-center mb-2\",\n            component: _reactNative.View,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n              name: \"star\",\n              size: 14,\n              color: \"#FFD700\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 11\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"text-sm text-gray-600 ml-1\",\n              component: _reactNative.Text,\n              children: item.rating\n            }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"text-sm text-gray-400 mx-2\",\n              component: _reactNative.Text,\n              children: \"\\u2022\"\n            }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"text-sm text-gray-600\",\n              component: _reactNative.Text,\n              children: item.deliveryTime\n            }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"text-sm text-gray-400 mx-2\",\n              component: _reactNative.Text,\n              children: \"\\u2022\"\n            }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"text-sm text-gray-600\",\n              component: _reactNative.Text,\n              children: item.deliveryFee\n            }, void 0, false, void 0, this)]\n          }, void 0, true, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            className: \"flex-row flex-wrap\",\n            component: _reactNative.View,\n            children: item.tags.map((tag, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"text-xs text-gray-500 mr-2\",\n              component: _reactNative.Text,\n              children: tag\n            }, index, false, void 0, this))\n          }, void 0, false, void 0, this)]\n        }, void 0, true, void 0, this)]\n      }, void 0, true, void 0, this);\n    };\n    var renderPromo = _ref4 => {\n      var item = _ref4.item;\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n        className: \"rounded-xl mr-4 overflow-hidden\",\n        style: {\n          width: width * 0.8\n        },\n        component: _reactNative.TouchableOpacity,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n          className: \"relative\",\n          component: _reactNative.View,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            source: {\n              uri: item.image\n            },\n            className: \"w-full h-24\",\n            resizeMode: \"cover\",\n            component: _reactNative.Image\n          }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            className: \"absolute inset-0 justify-center px-4\",\n            style: {\n              backgroundColor: item.color + '90'\n            },\n            component: _reactNative.View,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"text-white font-bold text-lg\",\n              component: _reactNative.Text,\n              children: item.title\n            }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"text-white text-sm opacity-90\",\n              component: _reactNative.Text,\n              children: item.subtitle\n            }, void 0, false, void 0, this)]\n          }, void 0, true, void 0, this)]\n        }, void 0, true, void 0, this)\n      }, void 0, false, void 0, this);\n    };\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n      className: \"flex-1 bg-gray-50\",\n      component: _reactNative.View,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_MobileHeader.default, {\n        searchQuery: searchQuery,\n        onSearchChange: setSearchQuery,\n        onNotificationPress: handleNotificationPress,\n        onWishlistPress: handleWishlistPress\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n        className: \"flex-1\",\n        showsVerticalScrollIndicator: false,\n        component: _reactNative.ScrollView,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n          className: \"py-4\",\n          component: _reactNative.View,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.FlatList, {\n            data: promoOffers,\n            renderItem: renderPromo,\n            keyExtractor: item => item.id,\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              paddingHorizontal: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n          className: \"py-4\",\n          component: _reactNative.View,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            className: \"text-lg font-bold text-gray-900 px-4 mb-3\",\n            component: _reactNative.Text,\n            children: \"Categories\"\n          }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.FlatList, {\n            data: categories,\n            renderItem: renderCategory,\n            keyExtractor: item => item.id,\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              paddingHorizontal: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n          className: \"py-4\",\n          component: _reactNative.View,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            className: \"flex-row justify-between items-center px-4 mb-3\",\n            component: _reactNative.View,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"text-lg font-bold text-gray-900\",\n              component: _reactNative.Text,\n              children: \"Featured Restaurants\"\n            }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n                className: \"text-orange-500 font-medium\",\n                component: _reactNative.Text,\n                children: \"See all\"\n              }, void 0, false, void 0, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.FlatList, {\n            data: featuredRestaurants,\n            renderItem: renderRestaurant,\n            keyExtractor: item => item.id,\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              paddingHorizontal: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n          className: \"px-4 py-4\",\n          component: _reactNative.View,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            className: \"text-lg font-bold text-gray-900 mb-3\",\n            component: _reactNative.Text,\n            children: \"Quick Actions\"\n          }, void 0, false, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n            className: \"flex-row justify-between\",\n            component: _reactNative.View,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"bg-white rounded-xl p-4 items-center flex-1 mr-2 shadow-sm\",\n              onPress: () => navigation.navigate('Orders'),\n              component: _reactNative.TouchableOpacity,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                name: \"receipt-outline\",\n                size: 24,\n                color: \"#f3a823\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n                className: \"text-gray-900 font-medium mt-2\",\n                component: _reactNative.Text,\n                children: \"My Orders\"\n              }, void 0, false, void 0, this)]\n            }, void 0, true, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"bg-white rounded-xl p-4 items-center flex-1 mx-1 shadow-sm\",\n              onPress: () => navigation.navigate('Wishlist'),\n              component: _reactNative.TouchableOpacity,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                name: \"heart-outline\",\n                size: 24,\n                color: \"#f3a823\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n                className: \"text-gray-900 font-medium mt-2\",\n                component: _reactNative.Text,\n                children: \"Favorites\"\n              }, void 0, false, void 0, this)]\n            }, void 0, true, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n              className: \"bg-white rounded-xl p-4 items-center flex-1 ml-2 shadow-sm\",\n              onPress: () => navigation.navigate('Account'),\n              component: _reactNative.TouchableOpacity,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                name: \"person-outline\",\n                size: 24,\n                color: \"#f3a823\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n                className: \"text-gray-900 font-medium mt-2\",\n                component: _reactNative.Text,\n                children: \"Account\"\n              }, void 0, false, void 0, this)]\n            }, void 0, true, void 0, this)]\n          }, void 0, true, void 0, this)]\n        }, void 0, true, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_nativewind.StyledComponent, {\n          className: \"h-20\",\n          component: _reactNative.View\n        }, void 0, false, void 0, this)]\n      }, void 0, true, void 0, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_FooterNavigation.default, {\n        navigation: navigation,\n        activeScreen: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f9fafb'\n        },\n        edges: ['bottom']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, void 0, this);\n  }\n  _s(HomeScreen, \"uixqA8hxOTN7LqZPWxVzG2fnyhQ=\");\n  _c = HomeScreen;\n  _nativewind.NativeWindStyleSheet.create({\n    styles: {\n      \"shadow-sm@0\": {\n        \"elevation\": 1.5,\n        \"shadowColor\": \"rgba(0, 0, 0, 0.1)\"\n      },\n      \"shadow-sm@1\": {\n        \"shadowOffset\": {\n          \"width\": 0,\n          \"height\": 1\n        },\n        \"shadowRadius\": 2,\n        \"shadowColor\": \"rgba(0, 0, 0, 0.1)\",\n        \"shadowOpacity\": 1\n      },\n      \"shadow-sm@2\": {\n        \"shadowOffset\": {\n          \"width\": 0,\n          \"height\": 1\n        },\n        \"shadowRadius\": 2,\n        \"shadowColor\": \"rgba(0, 0, 0, 0.1)\",\n        \"shadowOpacity\": 1\n      },\n      \"absolute\": {\n        \"position\": \"absolute\"\n      },\n      \"relative\": {\n        \"position\": \"relative\"\n      },\n      \"left-2\": {\n        \"left\": 8\n      },\n      \"top-2\": {\n        \"top\": 8\n      },\n      \"mx-1\": {\n        \"marginLeft\": 4,\n        \"marginRight\": 4\n      },\n      \"mx-2\": {\n        \"marginLeft\": 8,\n        \"marginRight\": 8\n      },\n      \"mb-1\": {\n        \"marginBottom\": 4\n      },\n      \"mb-2\": {\n        \"marginBottom\": 8\n      },\n      \"mb-3\": {\n        \"marginBottom\": 12\n      },\n      \"ml-1\": {\n        \"marginLeft\": 4\n      },\n      \"ml-2\": {\n        \"marginLeft\": 8\n      },\n      \"mr-2\": {\n        \"marginRight\": 8\n      },\n      \"mr-4\": {\n        \"marginRight\": 16\n      },\n      \"mt-2\": {\n        \"marginTop\": 8\n      },\n      \"flex\": {\n        \"display\": \"flex\"\n      },\n      \"h-16\": {\n        \"height\": 64\n      },\n      \"h-20\": {\n        \"height\": 80\n      },\n      \"h-24\": {\n        \"height\": 96\n      },\n      \"h-32\": {\n        \"height\": 128\n      },\n      \"w-16\": {\n        \"width\": 64\n      },\n      \"w-full\": {\n        \"width\": \"100%\"\n      },\n      \"flex-1\": {\n        \"flexGrow\": 1,\n        \"flexShrink\": 1,\n        \"flexBasis\": \"0%\"\n      },\n      \"flex-row\": {\n        \"flexDirection\": \"row\"\n      },\n      \"flex-wrap\": {\n        \"flexWrap\": \"wrap\"\n      },\n      \"items-center\": {\n        \"alignItems\": \"center\"\n      },\n      \"justify-center\": {\n        \"justifyContent\": \"center\"\n      },\n      \"justify-between\": {\n        \"justifyContent\": \"space-between\"\n      },\n      \"overflow-hidden\": {\n        \"overflow\": \"hidden\"\n      },\n      \"rounded\": {\n        \"borderTopLeftRadius\": 4,\n        \"borderTopRightRadius\": 4,\n        \"borderBottomRightRadius\": 4,\n        \"borderBottomLeftRadius\": 4\n      },\n      \"rounded-full\": {\n        \"borderTopLeftRadius\": 9999,\n        \"borderTopRightRadius\": 9999,\n        \"borderBottomRightRadius\": 9999,\n        \"borderBottomLeftRadius\": 9999\n      },\n      \"rounded-xl\": {\n        \"borderTopLeftRadius\": 12,\n        \"borderTopRightRadius\": 12,\n        \"borderBottomRightRadius\": 12,\n        \"borderBottomLeftRadius\": 12\n      },\n      \"rounded-t-xl\": {\n        \"borderTopLeftRadius\": 12,\n        \"borderTopRightRadius\": 12\n      },\n      \"bg-gray-50\": {\n        \"backgroundColor\": \"#f9fafb\"\n      },\n      \"bg-red-500\": {\n        \"backgroundColor\": \"#ef4444\"\n      },\n      \"bg-white\": {\n        \"backgroundColor\": \"#fff\"\n      },\n      \"p-3\": {\n        \"paddingTop\": 12,\n        \"paddingRight\": 12,\n        \"paddingBottom\": 12,\n        \"paddingLeft\": 12\n      },\n      \"p-4\": {\n        \"paddingTop\": 16,\n        \"paddingRight\": 16,\n        \"paddingBottom\": 16,\n        \"paddingLeft\": 16\n      },\n      \"px-2\": {\n        \"paddingLeft\": 8,\n        \"paddingRight\": 8\n      },\n      \"px-4\": {\n        \"paddingLeft\": 16,\n        \"paddingRight\": 16\n      },\n      \"py-1\": {\n        \"paddingTop\": 4,\n        \"paddingBottom\": 4\n      },\n      \"py-4\": {\n        \"paddingTop\": 16,\n        \"paddingBottom\": 16\n      },\n      \"text-center\": {\n        \"textAlign\": \"center\"\n      },\n      \"font-bold\": {\n        \"fontWeight\": \"700\"\n      },\n      \"font-medium\": {\n        \"fontWeight\": \"500\"\n      },\n      \"text-gray-400\": {\n        \"color\": \"#9ca3af\"\n      },\n      \"text-gray-500\": {\n        \"color\": \"#6b7280\"\n      },\n      \"text-gray-600\": {\n        \"color\": \"#4b5563\"\n      },\n      \"text-gray-900\": {\n        \"color\": \"#111827\"\n      },\n      \"text-orange-500\": {\n        \"color\": \"#f97316\"\n      },\n      \"text-white\": {\n        \"color\": \"#fff\"\n      },\n      \"opacity-90\": {\n        \"opacity\": 0.9\n      },\n      \"text-2xl\": {\n        \"fontSize\": 24,\n        \"lineHeight\": 32\n      },\n      \"text-base\": {\n        \"fontSize\": 16,\n        \"lineHeight\": 24\n      },\n      \"text-lg\": {\n        \"fontSize\": 18,\n        \"lineHeight\": 28\n      },\n      \"text-sm\": {\n        \"fontSize\": 14,\n        \"lineHeight\": 20\n      },\n      \"text-xs\": {\n        \"fontSize\": 12,\n        \"lineHeight\": 16\n      },\n      \"elevation\": {\n        \"elevation\": 3\n      }\n    },\n    atRules: {\n      \"shadow-sm\": [[[\"media\", \"android\"]], [[\"media\", \"ios\"]], [[\"media\", \"web\"]]]\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"HomeScreen\");\n});", "lineCount": 654, "map": [[9, 2, 1, 0], [9, 6, 1, 0, "_react"], [9, 12, 1, 0], [9, 15, 1, 0, "_interopRequireWildcard"], [9, 38, 1, 0], [9, 39, 1, 0, "require"], [9, 46, 1, 0], [9, 47, 1, 0, "_dependencyMap"], [9, 61, 1, 0], [10, 2, 2, 0], [10, 6, 2, 0, "_reactNative"], [10, 18, 2, 0], [10, 21, 2, 0, "require"], [10, 28, 2, 0], [10, 29, 2, 0, "_dependencyMap"], [10, 43, 2, 0], [11, 2, 11, 0], [11, 6, 11, 0, "_reactNativeSafeAreaContext"], [11, 33, 11, 0], [11, 36, 11, 0, "require"], [11, 43, 11, 0], [11, 44, 11, 0, "_dependencyMap"], [11, 58, 11, 0], [12, 2, 12, 0], [12, 6, 12, 0, "_vectorIcons"], [12, 18, 12, 0], [12, 21, 12, 0, "require"], [12, 28, 12, 0], [12, 29, 12, 0, "_dependencyMap"], [12, 43, 12, 0], [13, 2, 13, 0], [13, 6, 13, 0, "_MobileHeader"], [13, 19, 13, 0], [13, 22, 13, 0, "_interopRequireDefault"], [13, 44, 13, 0], [13, 45, 13, 0, "require"], [13, 52, 13, 0], [13, 53, 13, 0, "_dependencyMap"], [13, 67, 13, 0], [14, 2, 14, 0], [14, 6, 14, 0, "_FooterNavigation"], [14, 23, 14, 0], [14, 26, 14, 0, "_interopRequireDefault"], [14, 48, 14, 0], [14, 49, 14, 0, "require"], [14, 56, 14, 0], [14, 57, 14, 0, "_dependencyMap"], [14, 71, 14, 0], [15, 2, 14, 62], [15, 6, 14, 62, "_jsxDevRuntime"], [15, 20, 14, 62], [15, 23, 14, 62, "require"], [15, 30, 14, 62], [15, 31, 14, 62, "_dependencyMap"], [15, 45, 14, 62], [16, 2, 14, 62], [16, 6, 14, 62, "_jsxFileName"], [16, 18, 14, 62], [17, 4, 14, 62, "_s"], [17, 6, 14, 62], [17, 9, 14, 62, "$RefreshSig$"], [17, 21, 14, 62], [18, 2, 14, 62], [18, 11, 14, 62, "_interopRequireWildcard"], [18, 35, 14, 62, "e"], [18, 36, 14, 62], [18, 38, 14, 62, "t"], [18, 39, 14, 62], [18, 68, 14, 62, "WeakMap"], [18, 75, 14, 62], [18, 81, 14, 62, "r"], [18, 82, 14, 62], [18, 89, 14, 62, "WeakMap"], [18, 96, 14, 62], [18, 100, 14, 62, "n"], [18, 101, 14, 62], [18, 108, 14, 62, "WeakMap"], [18, 115, 14, 62], [18, 127, 14, 62, "_interopRequireWildcard"], [18, 150, 14, 62], [18, 162, 14, 62, "_interopRequireWildcard"], [18, 163, 14, 62, "e"], [18, 164, 14, 62], [18, 166, 14, 62, "t"], [18, 167, 14, 62], [18, 176, 14, 62, "t"], [18, 177, 14, 62], [18, 181, 14, 62, "e"], [18, 182, 14, 62], [18, 186, 14, 62, "e"], [18, 187, 14, 62], [18, 188, 14, 62, "__esModule"], [18, 198, 14, 62], [18, 207, 14, 62, "e"], [18, 208, 14, 62], [18, 214, 14, 62, "o"], [18, 215, 14, 62], [18, 217, 14, 62, "i"], [18, 218, 14, 62], [18, 220, 14, 62, "f"], [18, 221, 14, 62], [18, 226, 14, 62, "__proto__"], [18, 235, 14, 62], [18, 243, 14, 62, "default"], [18, 250, 14, 62], [18, 252, 14, 62, "e"], [18, 253, 14, 62], [18, 270, 14, 62, "e"], [18, 271, 14, 62], [18, 294, 14, 62, "e"], [18, 295, 14, 62], [18, 320, 14, 62, "e"], [18, 321, 14, 62], [18, 330, 14, 62, "f"], [18, 331, 14, 62], [18, 337, 14, 62, "o"], [18, 338, 14, 62], [18, 341, 14, 62, "t"], [18, 342, 14, 62], [18, 345, 14, 62, "n"], [18, 346, 14, 62], [18, 349, 14, 62, "r"], [18, 350, 14, 62], [18, 358, 14, 62, "o"], [18, 359, 14, 62], [18, 360, 14, 62, "has"], [18, 363, 14, 62], [18, 364, 14, 62, "e"], [18, 365, 14, 62], [18, 375, 14, 62, "o"], [18, 376, 14, 62], [18, 377, 14, 62, "get"], [18, 380, 14, 62], [18, 381, 14, 62, "e"], [18, 382, 14, 62], [18, 385, 14, 62, "o"], [18, 386, 14, 62], [18, 387, 14, 62, "set"], [18, 390, 14, 62], [18, 391, 14, 62, "e"], [18, 392, 14, 62], [18, 394, 14, 62, "f"], [18, 395, 14, 62], [18, 409, 14, 62, "_t"], [18, 411, 14, 62], [18, 415, 14, 62, "e"], [18, 416, 14, 62], [18, 432, 14, 62, "_t"], [18, 434, 14, 62], [18, 441, 14, 62, "hasOwnProperty"], [18, 455, 14, 62], [18, 456, 14, 62, "call"], [18, 460, 14, 62], [18, 461, 14, 62, "e"], [18, 462, 14, 62], [18, 464, 14, 62, "_t"], [18, 466, 14, 62], [18, 473, 14, 62, "i"], [18, 474, 14, 62], [18, 478, 14, 62, "o"], [18, 479, 14, 62], [18, 482, 14, 62, "Object"], [18, 488, 14, 62], [18, 489, 14, 62, "defineProperty"], [18, 503, 14, 62], [18, 508, 14, 62, "Object"], [18, 514, 14, 62], [18, 515, 14, 62, "getOwnPropertyDescriptor"], [18, 539, 14, 62], [18, 540, 14, 62, "e"], [18, 541, 14, 62], [18, 543, 14, 62, "_t"], [18, 545, 14, 62], [18, 552, 14, 62, "i"], [18, 553, 14, 62], [18, 554, 14, 62, "get"], [18, 557, 14, 62], [18, 561, 14, 62, "i"], [18, 562, 14, 62], [18, 563, 14, 62, "set"], [18, 566, 14, 62], [18, 570, 14, 62, "o"], [18, 571, 14, 62], [18, 572, 14, 62, "f"], [18, 573, 14, 62], [18, 575, 14, 62, "_t"], [18, 577, 14, 62], [18, 579, 14, 62, "i"], [18, 580, 14, 62], [18, 584, 14, 62, "f"], [18, 585, 14, 62], [18, 586, 14, 62, "_t"], [18, 588, 14, 62], [18, 592, 14, 62, "e"], [18, 593, 14, 62], [18, 594, 14, 62, "_t"], [18, 596, 14, 62], [18, 607, 14, 62, "f"], [18, 608, 14, 62], [18, 613, 14, 62, "e"], [18, 614, 14, 62], [18, 616, 14, 62, "t"], [18, 617, 14, 62], [19, 2, 16, 0], [19, 6, 16, 0, "_Dimensions$get"], [19, 21, 16, 0], [19, 24, 16, 18, "Dimensions"], [19, 47, 16, 28], [19, 48, 16, 29, "get"], [19, 51, 16, 32], [19, 52, 16, 33], [19, 60, 16, 41], [19, 61, 16, 42], [20, 4, 16, 8, "width"], [20, 9, 16, 13], [20, 12, 16, 13, "_Dimensions$get"], [20, 27, 16, 13], [20, 28, 16, 8, "width"], [20, 33, 16, 13], [22, 2, 18, 0], [23, 2, 19, 0], [23, 6, 19, 6, "categories"], [23, 16, 19, 16], [23, 19, 19, 19], [23, 20, 20, 2], [24, 4, 20, 4, "id"], [24, 6, 20, 6], [24, 8, 20, 8], [24, 11, 20, 11], [25, 4, 20, 13, "name"], [25, 8, 20, 17], [25, 10, 20, 19], [25, 21, 20, 30], [26, 4, 20, 32, "icon"], [26, 8, 20, 36], [26, 10, 20, 38], [26, 14, 20, 42], [27, 4, 20, 44, "color"], [27, 9, 20, 49], [27, 11, 20, 51], [28, 2, 20, 61], [28, 3, 20, 62], [28, 5, 21, 2], [29, 4, 21, 4, "id"], [29, 6, 21, 6], [29, 8, 21, 8], [29, 11, 21, 11], [30, 4, 21, 13, "name"], [30, 8, 21, 17], [30, 10, 21, 19], [30, 17, 21, 26], [31, 4, 21, 28, "icon"], [31, 8, 21, 32], [31, 10, 21, 34], [31, 14, 21, 38], [32, 4, 21, 40, "color"], [32, 9, 21, 45], [32, 11, 21, 47], [33, 2, 21, 57], [33, 3, 21, 58], [33, 5, 22, 2], [34, 4, 22, 4, "id"], [34, 6, 22, 6], [34, 8, 22, 8], [34, 11, 22, 11], [35, 4, 22, 13, "name"], [35, 8, 22, 17], [35, 10, 22, 19], [35, 17, 22, 26], [36, 4, 22, 28, "icon"], [36, 8, 22, 32], [36, 10, 22, 34], [36, 14, 22, 38], [37, 4, 22, 40, "color"], [37, 9, 22, 45], [37, 11, 22, 47], [38, 2, 22, 57], [38, 3, 22, 58], [38, 5, 23, 2], [39, 4, 23, 4, "id"], [39, 6, 23, 6], [39, 8, 23, 8], [39, 11, 23, 11], [40, 4, 23, 13, "name"], [40, 8, 23, 17], [40, 10, 23, 19], [40, 20, 23, 29], [41, 4, 23, 31, "icon"], [41, 8, 23, 35], [41, 10, 23, 37], [41, 14, 23, 41], [42, 4, 23, 43, "color"], [42, 9, 23, 48], [42, 11, 23, 50], [43, 2, 23, 60], [43, 3, 23, 61], [43, 5, 24, 2], [44, 4, 24, 4, "id"], [44, 6, 24, 6], [44, 8, 24, 8], [44, 11, 24, 11], [45, 4, 24, 13, "name"], [45, 8, 24, 17], [45, 10, 24, 19], [45, 18, 24, 27], [46, 4, 24, 29, "icon"], [46, 8, 24, 33], [46, 10, 24, 35], [46, 13, 24, 38], [47, 4, 24, 40, "color"], [47, 9, 24, 45], [47, 11, 24, 47], [48, 2, 24, 57], [48, 3, 24, 58], [48, 5, 25, 2], [49, 4, 25, 4, "id"], [49, 6, 25, 6], [49, 8, 25, 8], [49, 11, 25, 11], [50, 4, 25, 13, "name"], [50, 8, 25, 17], [50, 10, 25, 19], [50, 19, 25, 28], [51, 4, 25, 30, "icon"], [51, 8, 25, 34], [51, 10, 25, 36], [51, 14, 25, 40], [52, 4, 25, 42, "color"], [52, 9, 25, 47], [52, 11, 25, 49], [53, 2, 25, 59], [53, 3, 25, 60], [53, 4, 26, 1], [54, 2, 28, 0], [54, 6, 28, 6, "featuredRestaurants"], [54, 25, 28, 25], [54, 28, 28, 28], [54, 29, 29, 2], [55, 4, 30, 4, "id"], [55, 6, 30, 6], [55, 8, 30, 8], [55, 11, 30, 11], [56, 4, 31, 4, "name"], [56, 8, 31, 8], [56, 10, 31, 10], [56, 23, 31, 23], [57, 4, 32, 4, "image"], [57, 9, 32, 9], [57, 11, 32, 11], [57, 94, 32, 94], [58, 4, 33, 4, "rating"], [58, 10, 33, 10], [58, 12, 33, 12], [58, 15, 33, 15], [59, 4, 34, 4, "deliveryTime"], [59, 16, 34, 16], [59, 18, 34, 18], [59, 29, 34, 29], [60, 4, 35, 4, "deliveryFee"], [60, 15, 35, 15], [60, 17, 35, 17], [60, 22, 35, 22], [61, 4, 36, 4, "tags"], [61, 8, 36, 8], [61, 10, 36, 10], [61, 11, 36, 11], [61, 22, 36, 22], [61, 24, 36, 24], [61, 33, 36, 33], [61, 34, 36, 34], [62, 4, 37, 4, "promo"], [62, 9, 37, 9], [62, 11, 37, 11], [63, 2, 38, 2], [63, 3, 38, 3], [63, 5, 39, 2], [64, 4, 40, 4, "id"], [64, 6, 40, 6], [64, 8, 40, 8], [64, 11, 40, 11], [65, 4, 41, 4, "name"], [65, 8, 41, 8], [65, 10, 41, 10], [65, 20, 41, 20], [66, 4, 42, 4, "image"], [66, 9, 42, 9], [66, 11, 42, 11], [66, 94, 42, 94], [67, 4, 43, 4, "rating"], [67, 10, 43, 10], [67, 12, 43, 12], [67, 15, 43, 15], [68, 4, 44, 4, "deliveryTime"], [68, 16, 44, 16], [68, 18, 44, 18], [68, 29, 44, 29], [69, 4, 45, 4, "deliveryFee"], [69, 15, 45, 15], [69, 17, 45, 17], [69, 22, 45, 22], [70, 4, 46, 4, "tags"], [70, 8, 46, 8], [70, 10, 46, 10], [70, 11, 46, 11], [70, 22, 46, 22], [70, 24, 46, 24], [70, 34, 46, 34], [70, 35, 46, 35], [71, 4, 47, 4, "promo"], [71, 9, 47, 9], [71, 11, 47, 11], [72, 2, 48, 2], [72, 3, 48, 3], [72, 5, 49, 2], [73, 4, 50, 4, "id"], [73, 6, 50, 6], [73, 8, 50, 8], [73, 11, 50, 11], [74, 4, 51, 4, "name"], [74, 8, 51, 8], [74, 10, 51, 10], [74, 21, 51, 21], [75, 4, 52, 4, "image"], [75, 9, 52, 9], [75, 11, 52, 11], [75, 94, 52, 94], [76, 4, 53, 4, "rating"], [76, 10, 53, 10], [76, 12, 53, 12], [76, 15, 53, 15], [77, 4, 54, 4, "deliveryTime"], [77, 16, 54, 16], [77, 18, 54, 18], [77, 29, 54, 29], [78, 4, 55, 4, "deliveryFee"], [78, 15, 55, 15], [78, 17, 55, 17], [78, 22, 55, 22], [79, 4, 56, 4, "tags"], [79, 8, 56, 8], [79, 10, 56, 10], [79, 11, 56, 11], [79, 18, 56, 18], [79, 20, 56, 20], [79, 29, 56, 29], [79, 30, 56, 30], [80, 4, 57, 4, "promo"], [80, 9, 57, 9], [80, 11, 57, 11], [81, 2, 58, 2], [81, 3, 58, 3], [81, 4, 59, 1], [82, 2, 61, 0], [82, 6, 61, 6, "promoOffers"], [82, 17, 61, 17], [82, 20, 61, 20], [82, 21, 62, 2], [83, 4, 63, 4, "id"], [83, 6, 63, 6], [83, 8, 63, 8], [83, 11, 63, 11], [84, 4, 64, 4, "title"], [84, 9, 64, 9], [84, 11, 64, 11], [84, 24, 64, 24], [85, 4, 65, 4, "subtitle"], [85, 12, 65, 12], [85, 14, 65, 14], [85, 53, 65, 53], [86, 4, 66, 4, "image"], [86, 9, 66, 9], [86, 11, 66, 11], [86, 94, 66, 94], [87, 4, 67, 4, "color"], [87, 9, 67, 9], [87, 11, 67, 11], [88, 2, 68, 2], [88, 3, 68, 3], [88, 5, 69, 2], [89, 4, 70, 4, "id"], [89, 6, 70, 6], [89, 8, 70, 8], [89, 11, 70, 11], [90, 4, 71, 4, "title"], [90, 9, 71, 9], [90, 11, 71, 11], [90, 26, 71, 26], [91, 4, 72, 4, "subtitle"], [91, 12, 72, 12], [91, 14, 72, 14], [91, 53, 72, 53], [92, 4, 73, 4, "image"], [92, 9, 73, 9], [92, 11, 73, 11], [92, 94, 73, 94], [93, 4, 74, 4, "color"], [93, 9, 74, 9], [93, 11, 74, 11], [94, 2, 75, 2], [94, 3, 75, 3], [94, 4, 76, 1], [95, 2, 78, 15], [95, 11, 78, 24, "HomeScreen"], [95, 21, 78, 34, "HomeScreen"], [95, 22, 78, 34, "_ref"], [95, 26, 78, 34], [95, 28, 78, 56], [96, 4, 78, 56, "_s"], [96, 6, 78, 56], [97, 4, 78, 56], [97, 8, 78, 37, "navigation"], [97, 18, 78, 47], [97, 21, 78, 47, "_ref"], [97, 25, 78, 47], [97, 26, 78, 37, "navigation"], [97, 36, 78, 47], [98, 4, 79, 2], [98, 8, 79, 2, "_useState"], [98, 17, 79, 2], [98, 20, 79, 40], [98, 24, 79, 40, "useState"], [98, 39, 79, 48], [98, 41, 79, 49], [98, 43, 79, 51], [98, 44, 79, 52], [99, 6, 79, 52, "_useState2"], [99, 16, 79, 52], [99, 23, 79, 52, "_slicedToArray2"], [99, 38, 79, 52], [99, 39, 79, 52, "default"], [99, 46, 79, 52], [99, 48, 79, 52, "_useState"], [99, 57, 79, 52], [100, 6, 79, 9, "searchQuery"], [100, 17, 79, 20], [100, 20, 79, 20, "_useState2"], [100, 30, 79, 20], [101, 6, 79, 22, "setSearch<PERSON>uery"], [101, 20, 79, 36], [101, 23, 79, 36, "_useState2"], [101, 33, 79, 36], [102, 4, 81, 2], [102, 8, 81, 8, "handleNotificationPress"], [102, 31, 81, 31], [102, 34, 81, 34, "handleNotificationPress"], [102, 35, 81, 34], [102, 40, 81, 40], [103, 6, 82, 4, "navigation"], [103, 16, 82, 14], [103, 17, 82, 15, "navigate"], [103, 25, 82, 23], [103, 26, 82, 24], [103, 41, 82, 39], [103, 42, 82, 40], [104, 4, 83, 2], [104, 5, 83, 3], [105, 4, 85, 2], [105, 8, 85, 8, "handleWishlistPress"], [105, 27, 85, 27], [105, 30, 85, 30, "handleWishlistPress"], [105, 31, 85, 30], [105, 36, 85, 36], [106, 6, 86, 4, "navigation"], [106, 16, 86, 14], [106, 17, 86, 15, "navigate"], [106, 25, 86, 23], [106, 26, 86, 24], [106, 36, 86, 34], [106, 37, 86, 35], [107, 4, 87, 2], [107, 5, 87, 3], [108, 4, 89, 2], [108, 8, 89, 8, "renderCategory"], [108, 22, 89, 22], [108, 25, 89, 25, "_ref2"], [108, 30, 89, 25], [109, 6, 89, 25], [109, 10, 89, 28, "item"], [109, 14, 89, 32], [109, 17, 89, 32, "_ref2"], [109, 22, 89, 32], [109, 23, 89, 28, "item"], [109, 27, 89, 32], [110, 6, 89, 32], [110, 30, 89, 32, "_jsxDevRuntime"], [110, 44, 89, 32], [110, 45, 89, 32, "jsxDEV"], [110, 51, 89, 32], [110, 53, 89, 32, "_nativewind"], [110, 64, 89, 32], [110, 65, 89, 32, "StyledComponent"], [110, 80, 89, 32], [111, 8, 90, 22, "className"], [111, 17, 90, 31], [111, 19, 90, 32], [111, 38, 90, 51], [112, 8, 90, 51, "component"], [112, 17, 90, 51], [112, 19, 90, 51, "_reactNative"], [112, 31, 90, 51], [112, 32, 90, 51, "TouchableOpacity"], [112, 48, 90, 51], [113, 8, 90, 51, "children"], [113, 16, 90, 51], [113, 36, 90, 51, "_jsxDevRuntime"], [113, 50, 90, 51], [113, 51, 90, 51, "jsxDEV"], [113, 57, 90, 51], [113, 59, 90, 51, "_nativewind"], [113, 70, 90, 51], [113, 71, 90, 51, "StyledComponent"], [113, 86, 90, 51], [114, 10, 92, 8, "className"], [114, 19, 92, 17], [114, 21, 92, 18], [114, 78, 92, 75], [115, 10, 93, 8, "style"], [115, 15, 93, 13], [115, 17, 93, 15], [116, 12, 93, 17, "backgroundColor"], [116, 27, 93, 32], [116, 29, 93, 34, "item"], [116, 33, 93, 38], [116, 34, 93, 39, "color"], [116, 39, 93, 44], [116, 42, 93, 47], [117, 10, 93, 52], [117, 11, 93, 54], [118, 10, 93, 54, "component"], [118, 19, 93, 54], [118, 21, 93, 54, "_reactNative"], [118, 33, 93, 54], [118, 34, 93, 54, "View"], [118, 38, 93, 54], [119, 10, 93, 54, "children"], [119, 18, 93, 54], [119, 37, 93, 54, "_jsxDevRuntime"], [119, 51, 93, 54], [119, 52, 93, 54, "jsxDEV"], [119, 58, 93, 54], [119, 60, 93, 54, "_nativewind"], [119, 71, 93, 54], [119, 72, 93, 54, "StyledComponent"], [119, 87, 93, 54], [120, 12, 95, 14, "className"], [120, 21, 95, 23], [120, 23, 95, 24], [120, 33, 95, 34], [121, 12, 95, 34, "component"], [121, 21, 95, 34], [121, 23, 95, 34, "_reactNative"], [121, 35, 95, 34], [121, 36, 95, 34, "Text"], [121, 40, 95, 34], [122, 12, 95, 34, "children"], [122, 20, 95, 34], [122, 22, 95, 36, "item"], [122, 26, 95, 40], [122, 27, 95, 41, "icon"], [123, 10, 95, 45], [124, 8, 95, 45], [124, 58, 95, 45, "_jsxDevRuntime"], [124, 72, 95, 45], [124, 73, 95, 45, "jsxDEV"], [124, 79, 95, 45], [124, 81, 95, 45, "_nativewind"], [124, 92, 95, 45], [124, 93, 95, 45, "StyledComponent"], [124, 108, 95, 45], [125, 10, 97, 12, "className"], [125, 19, 97, 21], [125, 21, 97, 22], [125, 68, 97, 69], [126, 10, 97, 69, "component"], [126, 19, 97, 69], [126, 21, 97, 69, "_reactNative"], [126, 33, 97, 69], [126, 34, 97, 69, "Text"], [126, 38, 97, 69], [127, 10, 97, 69, "children"], [127, 18, 97, 69], [127, 20, 97, 71, "item"], [127, 24, 97, 75], [127, 25, 97, 76, "name"], [128, 8, 97, 80], [129, 6, 97, 80], [130, 4, 97, 80], [130, 5, 99, 3], [131, 4, 101, 2], [131, 8, 101, 8, "renderRestaurant"], [131, 24, 101, 24], [131, 27, 101, 27, "_ref3"], [131, 32, 101, 27], [132, 6, 101, 27], [132, 10, 101, 30, "item"], [132, 14, 101, 34], [132, 17, 101, 34, "_ref3"], [132, 22, 101, 34], [132, 23, 101, 30, "item"], [132, 27, 101, 34], [133, 6, 101, 34], [133, 30, 101, 34, "_jsxDevRuntime"], [133, 44, 101, 34], [133, 45, 101, 34, "jsxDEV"], [133, 51, 101, 34], [133, 53, 101, 34, "_nativewind"], [133, 64, 101, 34], [133, 65, 101, 34, "StyledComponent"], [133, 80, 101, 34], [134, 8, 102, 22, "className"], [134, 17, 102, 31], [134, 19, 102, 32], [134, 55, 102, 68], [135, 8, 102, 69, "style"], [135, 13, 102, 74], [135, 15, 102, 76], [136, 10, 102, 78, "width"], [136, 15, 102, 83], [136, 17, 102, 85, "width"], [136, 22, 102, 90], [136, 25, 102, 93], [137, 8, 102, 98], [137, 9, 102, 100], [138, 8, 102, 100, "component"], [138, 17, 102, 100], [138, 19, 102, 100, "_reactNative"], [138, 31, 102, 100], [138, 32, 102, 100, "TouchableOpacity"], [138, 48, 102, 100], [139, 8, 102, 100, "children"], [139, 16, 102, 100], [139, 36, 102, 100, "_jsxDevRuntime"], [139, 50, 102, 100], [139, 51, 102, 100, "jsxDEV"], [139, 57, 102, 100], [139, 59, 102, 100, "_nativewind"], [139, 70, 102, 100], [139, 71, 102, 100, "StyledComponent"], [139, 86, 102, 100], [140, 10, 103, 12, "className"], [140, 19, 103, 21], [140, 21, 103, 22], [140, 31, 103, 32], [141, 10, 103, 32, "component"], [141, 19, 103, 32], [141, 21, 103, 32, "_reactNative"], [141, 33, 103, 32], [141, 34, 103, 32, "View"], [141, 38, 103, 32], [142, 10, 103, 32, "children"], [142, 18, 103, 32], [142, 38, 103, 32, "_jsxDevRuntime"], [142, 52, 103, 32], [142, 53, 103, 32, "jsxDEV"], [142, 59, 103, 32], [142, 61, 103, 32, "_nativewind"], [142, 72, 103, 32], [142, 73, 103, 32, "StyledComponent"], [142, 88, 103, 32], [143, 12, 105, 10, "source"], [143, 18, 105, 16], [143, 20, 105, 18], [144, 14, 105, 20, "uri"], [144, 17, 105, 23], [144, 19, 105, 25, "item"], [144, 23, 105, 29], [144, 24, 105, 30, "image"], [145, 12, 105, 36], [145, 13, 105, 38], [146, 12, 106, 10, "className"], [146, 21, 106, 19], [146, 23, 106, 20], [146, 49, 106, 46], [147, 12, 107, 10, "resizeMode"], [147, 22, 107, 20], [147, 24, 107, 21], [147, 31, 107, 28], [148, 12, 107, 28, "component"], [148, 21, 107, 28], [148, 23, 107, 28, "_reactNative"], [148, 35, 107, 28], [148, 36, 107, 28, "Image"], [149, 10, 107, 28], [149, 43, 109, 9, "item"], [149, 47, 109, 13], [149, 48, 109, 14, "promo"], [149, 53, 109, 19], [149, 74, 109, 19, "_jsxDevRuntime"], [149, 88, 109, 19], [149, 89, 109, 19, "jsxDEV"], [149, 95, 109, 19], [149, 97, 109, 19, "_nativewind"], [149, 108, 109, 19], [149, 109, 109, 19, "StyledComponent"], [149, 124, 109, 19], [150, 12, 110, 16, "className"], [150, 21, 110, 25], [150, 23, 110, 26], [150, 75, 110, 78], [151, 12, 110, 78, "component"], [151, 21, 110, 78], [151, 23, 110, 78, "_reactNative"], [151, 35, 110, 78], [151, 36, 110, 78, "View"], [151, 40, 110, 78], [152, 12, 110, 78, "children"], [152, 20, 110, 78], [152, 39, 110, 78, "_jsxDevRuntime"], [152, 53, 110, 78], [152, 54, 110, 78, "jsxDEV"], [152, 60, 110, 78], [152, 62, 110, 78, "_nativewind"], [152, 73, 110, 78], [152, 74, 110, 78, "StyledComponent"], [152, 89, 110, 78], [153, 14, 111, 18, "className"], [153, 23, 111, 27], [153, 25, 111, 28], [153, 55, 111, 58], [154, 14, 111, 58, "component"], [154, 23, 111, 58], [154, 25, 111, 58, "_reactNative"], [154, 37, 111, 58], [154, 38, 111, 58, "Text"], [154, 42, 111, 58], [155, 14, 111, 58, "children"], [155, 22, 111, 58], [155, 24, 111, 60, "item"], [155, 28, 111, 64], [155, 29, 111, 65, "promo"], [156, 12, 111, 70], [157, 10, 111, 70], [157, 41, 113, 9], [158, 8, 113, 9], [158, 57, 113, 9, "_jsxDevRuntime"], [158, 71, 113, 9], [158, 72, 113, 9, "jsxDEV"], [158, 78, 113, 9], [158, 80, 113, 9, "_nativewind"], [158, 91, 113, 9], [158, 92, 113, 9, "StyledComponent"], [158, 107, 113, 9], [159, 10, 115, 12, "className"], [159, 19, 115, 21], [159, 21, 115, 22], [159, 26, 115, 27], [160, 10, 115, 27, "component"], [160, 19, 115, 27], [160, 21, 115, 27, "_reactNative"], [160, 33, 115, 27], [160, 34, 115, 27, "View"], [160, 38, 115, 27], [161, 10, 115, 27, "children"], [161, 18, 115, 27], [161, 38, 115, 27, "_jsxDevRuntime"], [161, 52, 115, 27], [161, 53, 115, 27, "jsxDEV"], [161, 59, 115, 27], [161, 61, 115, 27, "_nativewind"], [161, 72, 115, 27], [161, 73, 115, 27, "StyledComponent"], [161, 88, 115, 27], [162, 12, 116, 14, "className"], [162, 21, 116, 23], [162, 23, 116, 24], [162, 63, 116, 64], [163, 12, 116, 64, "component"], [163, 21, 116, 64], [163, 23, 116, 64, "_reactNative"], [163, 35, 116, 64], [163, 36, 116, 64, "Text"], [163, 40, 116, 64], [164, 12, 116, 64, "children"], [164, 20, 116, 64], [164, 22, 116, 66, "item"], [164, 26, 116, 70], [164, 27, 116, 71, "name"], [165, 10, 116, 75], [165, 60, 116, 75, "_jsxDevRuntime"], [165, 74, 116, 75], [165, 75, 116, 75, "jsxDEV"], [165, 81, 116, 75], [165, 83, 116, 75, "_nativewind"], [165, 94, 116, 75], [165, 95, 116, 75, "StyledComponent"], [165, 110, 116, 75], [166, 12, 117, 14, "className"], [166, 21, 117, 23], [166, 23, 117, 24], [166, 51, 117, 52], [167, 12, 117, 52, "component"], [167, 21, 117, 52], [167, 23, 117, 52, "_reactNative"], [167, 35, 117, 52], [167, 36, 117, 52, "View"], [167, 40, 117, 52], [168, 12, 117, 52, "children"], [168, 20, 117, 52], [168, 36, 118, 10], [168, 40, 118, 10, "_jsxDevRuntime"], [168, 54, 118, 10], [168, 55, 118, 10, "jsxDEV"], [168, 61, 118, 10], [168, 63, 118, 11, "_vectorIcons"], [168, 75, 118, 11], [168, 76, 118, 11, "Ionicons"], [168, 84, 118, 19], [169, 14, 118, 20, "name"], [169, 18, 118, 24], [169, 20, 118, 25], [169, 26, 118, 31], [170, 14, 118, 32, "size"], [170, 18, 118, 36], [170, 20, 118, 38], [170, 22, 118, 41], [171, 14, 118, 42, "color"], [171, 19, 118, 47], [171, 21, 118, 48], [172, 12, 118, 57], [173, 14, 118, 57, "fileName"], [173, 22, 118, 57], [173, 24, 118, 57, "_jsxFileName"], [173, 36, 118, 57], [174, 14, 118, 57, "lineNumber"], [174, 24, 118, 57], [175, 14, 118, 57, "columnNumber"], [175, 26, 118, 57], [176, 12, 118, 57], [176, 19, 118, 59], [176, 20, 118, 60], [176, 39, 118, 60, "_jsxDevRuntime"], [176, 53, 118, 60], [176, 54, 118, 60, "jsxDEV"], [176, 60, 118, 60], [176, 62, 118, 60, "_nativewind"], [176, 73, 118, 60], [176, 74, 118, 60, "StyledComponent"], [176, 89, 118, 60], [177, 14, 119, 16, "className"], [177, 23, 119, 25], [177, 25, 119, 26], [177, 53, 119, 54], [178, 14, 119, 54, "component"], [178, 23, 119, 54], [178, 25, 119, 54, "_reactNative"], [178, 37, 119, 54], [178, 38, 119, 54, "Text"], [178, 42, 119, 54], [179, 14, 119, 54, "children"], [179, 22, 119, 54], [179, 24, 119, 56, "item"], [179, 28, 119, 60], [179, 29, 119, 61, "rating"], [180, 12, 119, 67], [180, 62, 119, 67, "_jsxDevRuntime"], [180, 76, 119, 67], [180, 77, 119, 67, "jsxDEV"], [180, 83, 119, 67], [180, 85, 119, 67, "_nativewind"], [180, 96, 119, 67], [180, 97, 119, 67, "StyledComponent"], [180, 112, 119, 67], [181, 14, 120, 16, "className"], [181, 23, 120, 25], [181, 25, 120, 26], [181, 53, 120, 54], [182, 14, 120, 54, "component"], [182, 23, 120, 54], [182, 25, 120, 54, "_reactNative"], [182, 37, 120, 54], [182, 38, 120, 54, "Text"], [182, 42, 120, 54], [183, 14, 120, 54, "children"], [183, 22, 120, 54], [183, 24, 120, 55], [184, 12, 120, 56], [184, 62, 120, 56, "_jsxDevRuntime"], [184, 76, 120, 56], [184, 77, 120, 56, "jsxDEV"], [184, 83, 120, 56], [184, 85, 120, 56, "_nativewind"], [184, 96, 120, 56], [184, 97, 120, 56, "StyledComponent"], [184, 112, 120, 56], [185, 14, 121, 16, "className"], [185, 23, 121, 25], [185, 25, 121, 26], [185, 48, 121, 49], [186, 14, 121, 49, "component"], [186, 23, 121, 49], [186, 25, 121, 49, "_reactNative"], [186, 37, 121, 49], [186, 38, 121, 49, "Text"], [186, 42, 121, 49], [187, 14, 121, 49, "children"], [187, 22, 121, 49], [187, 24, 121, 51, "item"], [187, 28, 121, 55], [187, 29, 121, 56, "deliveryTime"], [188, 12, 121, 68], [188, 62, 121, 68, "_jsxDevRuntime"], [188, 76, 121, 68], [188, 77, 121, 68, "jsxDEV"], [188, 83, 121, 68], [188, 85, 121, 68, "_nativewind"], [188, 96, 121, 68], [188, 97, 121, 68, "StyledComponent"], [188, 112, 121, 68], [189, 14, 122, 16, "className"], [189, 23, 122, 25], [189, 25, 122, 26], [189, 53, 122, 54], [190, 14, 122, 54, "component"], [190, 23, 122, 54], [190, 25, 122, 54, "_reactNative"], [190, 37, 122, 54], [190, 38, 122, 54, "Text"], [190, 42, 122, 54], [191, 14, 122, 54, "children"], [191, 22, 122, 54], [191, 24, 122, 55], [192, 12, 122, 56], [192, 62, 122, 56, "_jsxDevRuntime"], [192, 76, 122, 56], [192, 77, 122, 56, "jsxDEV"], [192, 83, 122, 56], [192, 85, 122, 56, "_nativewind"], [192, 96, 122, 56], [192, 97, 122, 56, "StyledComponent"], [192, 112, 122, 56], [193, 14, 123, 16, "className"], [193, 23, 123, 25], [193, 25, 123, 26], [193, 48, 123, 49], [194, 14, 123, 49, "component"], [194, 23, 123, 49], [194, 25, 123, 49, "_reactNative"], [194, 37, 123, 49], [194, 38, 123, 49, "Text"], [194, 42, 123, 49], [195, 14, 123, 49, "children"], [195, 22, 123, 49], [195, 24, 123, 51, "item"], [195, 28, 123, 55], [195, 29, 123, 56, "deliveryFee"], [196, 12, 123, 67], [197, 10, 123, 67], [197, 59, 123, 67, "_jsxDevRuntime"], [197, 73, 123, 67], [197, 74, 123, 67, "jsxDEV"], [197, 80, 123, 67], [197, 82, 123, 67, "_nativewind"], [197, 93, 123, 67], [197, 94, 123, 67, "StyledComponent"], [197, 109, 123, 67], [198, 12, 125, 14, "className"], [198, 21, 125, 23], [198, 23, 125, 24], [198, 43, 125, 44], [199, 12, 125, 44, "component"], [199, 21, 125, 44], [199, 23, 125, 44, "_reactNative"], [199, 35, 125, 44], [199, 36, 125, 44, "View"], [199, 40, 125, 44], [200, 12, 125, 44, "children"], [200, 20, 125, 44], [200, 22, 126, 11, "item"], [200, 26, 126, 15], [200, 27, 126, 16, "tags"], [200, 31, 126, 20], [200, 32, 126, 21, "map"], [200, 35, 126, 24], [200, 36, 126, 25], [200, 37, 126, 26, "tag"], [200, 40, 126, 37], [200, 42, 126, 39, "index"], [200, 47, 126, 52], [200, 69, 126, 52, "_jsxDevRuntime"], [200, 83, 126, 52], [200, 84, 126, 52, "jsxDEV"], [200, 90, 126, 52], [200, 92, 126, 52, "_nativewind"], [200, 103, 126, 52], [200, 104, 126, 52, "StyledComponent"], [200, 119, 126, 52], [201, 14, 127, 30, "className"], [201, 23, 127, 39], [201, 25, 127, 40], [201, 53, 127, 68], [202, 14, 127, 68, "component"], [202, 23, 127, 68], [202, 25, 127, 68, "_reactNative"], [202, 37, 127, 68], [202, 38, 127, 68, "Text"], [202, 42, 127, 68], [203, 14, 127, 68, "children"], [203, 22, 127, 68], [203, 24, 128, 15, "tag"], [204, 12, 128, 18], [204, 15, 127, 23, "index"], [204, 20, 127, 28], [204, 42, 130, 11], [205, 10, 130, 12], [206, 8, 130, 12], [207, 6, 130, 12], [208, 4, 130, 12], [208, 5, 134, 3], [209, 4, 136, 2], [209, 8, 136, 8, "renderPromo"], [209, 19, 136, 19], [209, 22, 136, 22, "_ref4"], [209, 27, 136, 22], [210, 6, 136, 22], [210, 10, 136, 25, "item"], [210, 14, 136, 29], [210, 17, 136, 29, "_ref4"], [210, 22, 136, 29], [210, 23, 136, 25, "item"], [210, 27, 136, 29], [211, 6, 136, 29], [211, 30, 136, 29, "_jsxDevRuntime"], [211, 44, 136, 29], [211, 45, 136, 29, "jsxDEV"], [211, 51, 136, 29], [211, 53, 136, 29, "_nativewind"], [211, 64, 136, 29], [211, 65, 136, 29, "StyledComponent"], [211, 80, 136, 29], [212, 8, 138, 6, "className"], [212, 17, 138, 15], [212, 19, 138, 16], [212, 52, 138, 49], [213, 8, 139, 6, "style"], [213, 13, 139, 11], [213, 15, 139, 13], [214, 10, 139, 15, "width"], [214, 15, 139, 20], [214, 17, 139, 22, "width"], [214, 22, 139, 27], [214, 25, 139, 30], [215, 8, 139, 34], [215, 9, 139, 36], [216, 8, 139, 36, "component"], [216, 17, 139, 36], [216, 19, 139, 36, "_reactNative"], [216, 31, 139, 36], [216, 32, 139, 36, "TouchableOpacity"], [216, 48, 139, 36], [217, 8, 139, 36, "children"], [217, 16, 139, 36], [217, 35, 139, 36, "_jsxDevRuntime"], [217, 49, 139, 36], [217, 50, 139, 36, "jsxDEV"], [217, 56, 139, 36], [217, 58, 139, 36, "_nativewind"], [217, 69, 139, 36], [217, 70, 139, 36, "StyledComponent"], [217, 85, 139, 36], [218, 10, 141, 12, "className"], [218, 19, 141, 21], [218, 21, 141, 22], [218, 31, 141, 32], [219, 10, 141, 32, "component"], [219, 19, 141, 32], [219, 21, 141, 32, "_reactNative"], [219, 33, 141, 32], [219, 34, 141, 32, "View"], [219, 38, 141, 32], [220, 10, 141, 32, "children"], [220, 18, 141, 32], [220, 38, 141, 32, "_jsxDevRuntime"], [220, 52, 141, 32], [220, 53, 141, 32, "jsxDEV"], [220, 59, 141, 32], [220, 61, 141, 32, "_nativewind"], [220, 72, 141, 32], [220, 73, 141, 32, "StyledComponent"], [220, 88, 141, 32], [221, 12, 143, 10, "source"], [221, 18, 143, 16], [221, 20, 143, 18], [222, 14, 143, 20, "uri"], [222, 17, 143, 23], [222, 19, 143, 25, "item"], [222, 23, 143, 29], [222, 24, 143, 30, "image"], [223, 12, 143, 36], [223, 13, 143, 38], [224, 12, 144, 10, "className"], [224, 21, 144, 19], [224, 23, 144, 20], [224, 36, 144, 33], [225, 12, 145, 10, "resizeMode"], [225, 22, 145, 20], [225, 24, 145, 21], [225, 31, 145, 28], [226, 12, 145, 28, "component"], [226, 21, 145, 28], [226, 23, 145, 28, "_reactNative"], [226, 35, 145, 28], [226, 36, 145, 28, "Image"], [227, 10, 145, 28], [227, 60, 145, 28, "_jsxDevRuntime"], [227, 74, 145, 28], [227, 75, 145, 28, "jsxDEV"], [227, 81, 145, 28], [227, 83, 145, 28, "_nativewind"], [227, 94, 145, 28], [227, 95, 145, 28, "StyledComponent"], [227, 110, 145, 28], [228, 12, 148, 10, "className"], [228, 21, 148, 19], [228, 23, 148, 20], [228, 61, 148, 58], [229, 12, 149, 10, "style"], [229, 17, 149, 15], [229, 19, 149, 17], [230, 14, 149, 19, "backgroundColor"], [230, 29, 149, 34], [230, 31, 149, 36, "item"], [230, 35, 149, 40], [230, 36, 149, 41, "color"], [230, 41, 149, 46], [230, 44, 149, 49], [231, 12, 149, 54], [231, 13, 149, 56], [232, 12, 149, 56, "component"], [232, 21, 149, 56], [232, 23, 149, 56, "_reactNative"], [232, 35, 149, 56], [232, 36, 149, 56, "View"], [232, 40, 149, 56], [233, 12, 149, 56, "children"], [233, 20, 149, 56], [233, 40, 149, 56, "_jsxDevRuntime"], [233, 54, 149, 56], [233, 55, 149, 56, "jsxDEV"], [233, 61, 149, 56], [233, 63, 149, 56, "_nativewind"], [233, 74, 149, 56], [233, 75, 149, 56, "StyledComponent"], [233, 90, 149, 56], [234, 14, 151, 16, "className"], [234, 23, 151, 25], [234, 25, 151, 26], [234, 55, 151, 56], [235, 14, 151, 56, "component"], [235, 23, 151, 56], [235, 25, 151, 56, "_reactNative"], [235, 37, 151, 56], [235, 38, 151, 56, "Text"], [235, 42, 151, 56], [236, 14, 151, 56, "children"], [236, 22, 151, 56], [236, 24, 151, 58, "item"], [236, 28, 151, 62], [236, 29, 151, 63, "title"], [237, 12, 151, 68], [237, 62, 151, 68, "_jsxDevRuntime"], [237, 76, 151, 68], [237, 77, 151, 68, "jsxDEV"], [237, 83, 151, 68], [237, 85, 151, 68, "_nativewind"], [237, 96, 151, 68], [237, 97, 151, 68, "StyledComponent"], [237, 112, 151, 68], [238, 14, 152, 16, "className"], [238, 23, 152, 25], [238, 25, 152, 26], [238, 56, 152, 57], [239, 14, 152, 57, "component"], [239, 23, 152, 57], [239, 25, 152, 57, "_reactNative"], [239, 37, 152, 57], [239, 38, 152, 57, "Text"], [239, 42, 152, 57], [240, 14, 152, 57, "children"], [240, 22, 152, 57], [240, 24, 152, 59, "item"], [240, 28, 152, 63], [240, 29, 152, 64, "subtitle"], [241, 12, 152, 72], [242, 10, 152, 72], [243, 8, 152, 72], [244, 6, 152, 72], [245, 4, 152, 72], [245, 5, 156, 3], [246, 4, 158, 2], [246, 28, 158, 2, "_jsxDevRuntime"], [246, 42, 158, 2], [246, 43, 158, 2, "jsxDEV"], [246, 49, 158, 2], [246, 51, 158, 2, "_nativewind"], [246, 62, 158, 2], [246, 63, 158, 2, "StyledComponent"], [246, 78, 158, 2], [247, 6, 159, 10, "className"], [247, 15, 159, 19], [247, 17, 159, 20], [247, 36, 159, 39], [248, 6, 159, 39, "component"], [248, 15, 159, 39], [248, 17, 159, 39, "_reactNative"], [248, 29, 159, 39], [248, 30, 159, 39, "View"], [248, 34, 159, 39], [249, 6, 159, 39, "children"], [249, 14, 159, 39], [249, 30, 161, 6], [249, 34, 161, 6, "_jsxDevRuntime"], [249, 48, 161, 6], [249, 49, 161, 6, "jsxDEV"], [249, 55, 161, 6], [249, 57, 161, 7, "_MobileHeader"], [249, 70, 161, 7], [249, 71, 161, 7, "default"], [249, 78, 161, 19], [250, 8, 162, 8, "searchQuery"], [250, 19, 162, 19], [250, 21, 162, 21, "searchQuery"], [250, 32, 162, 33], [251, 8, 163, 8, "onSearchChange"], [251, 22, 163, 22], [251, 24, 163, 24, "setSearch<PERSON>uery"], [251, 38, 163, 39], [252, 8, 164, 8, "onNotificationPress"], [252, 27, 164, 27], [252, 29, 164, 29, "handleNotificationPress"], [252, 52, 164, 53], [253, 8, 165, 8, "onWishlistPress"], [253, 23, 165, 23], [253, 25, 165, 25, "handleWishlistPress"], [254, 6, 165, 45], [255, 8, 165, 45, "fileName"], [255, 16, 165, 45], [255, 18, 165, 45, "_jsxFileName"], [255, 30, 165, 45], [256, 8, 165, 45, "lineNumber"], [256, 18, 165, 45], [257, 8, 165, 45, "columnNumber"], [257, 20, 165, 45], [258, 6, 165, 45], [258, 13, 166, 7], [258, 14, 166, 8], [258, 33, 166, 8, "_jsxDevRuntime"], [258, 47, 166, 8], [258, 48, 166, 8, "jsxDEV"], [258, 54, 166, 8], [258, 56, 166, 8, "_nativewind"], [258, 67, 166, 8], [258, 68, 166, 8, "StyledComponent"], [258, 83, 166, 8], [259, 8, 168, 18, "className"], [259, 17, 168, 27], [259, 19, 168, 28], [259, 27, 168, 36], [260, 8, 168, 37, "showsVerticalScrollIndicator"], [260, 36, 168, 65], [260, 38, 168, 67], [260, 43, 168, 73], [261, 8, 168, 73, "component"], [261, 17, 168, 73], [261, 19, 168, 73, "_reactNative"], [261, 31, 168, 73], [261, 32, 168, 73, "ScrollView"], [261, 42, 168, 73], [262, 8, 168, 73, "children"], [262, 16, 168, 73], [262, 36, 168, 73, "_jsxDevRuntime"], [262, 50, 168, 73], [262, 51, 168, 73, "jsxDEV"], [262, 57, 168, 73], [262, 59, 168, 73, "_nativewind"], [262, 70, 168, 73], [262, 71, 168, 73, "StyledComponent"], [262, 86, 168, 73], [263, 10, 170, 14, "className"], [263, 19, 170, 23], [263, 21, 170, 24], [263, 27, 170, 30], [264, 10, 170, 30, "component"], [264, 19, 170, 30], [264, 21, 170, 30, "_reactNative"], [264, 33, 170, 30], [264, 34, 170, 30, "View"], [264, 38, 170, 30], [265, 10, 170, 30, "children"], [265, 18, 170, 30], [265, 33, 171, 10], [265, 37, 171, 10, "_jsxDevRuntime"], [265, 51, 171, 10], [265, 52, 171, 10, "jsxDEV"], [265, 58, 171, 10], [265, 60, 171, 11, "_reactNative"], [265, 72, 171, 11], [265, 73, 171, 11, "FlatList"], [265, 81, 171, 19], [266, 12, 172, 12, "data"], [266, 16, 172, 16], [266, 18, 172, 18, "promoOffers"], [266, 29, 172, 30], [267, 12, 173, 12, "renderItem"], [267, 22, 173, 22], [267, 24, 173, 24, "renderPromo"], [267, 35, 173, 36], [268, 12, 174, 12, "keyExtractor"], [268, 24, 174, 24], [268, 26, 174, 27, "item"], [268, 30, 174, 31], [268, 34, 174, 36, "item"], [268, 38, 174, 40], [268, 39, 174, 41, "id"], [268, 41, 174, 44], [269, 12, 175, 12, "horizontal"], [269, 22, 175, 22], [270, 12, 176, 12, "showsHorizontalScrollIndicator"], [270, 42, 176, 42], [270, 44, 176, 44], [270, 49, 176, 50], [271, 12, 177, 12, "contentContainerStyle"], [271, 33, 177, 33], [271, 35, 177, 35], [272, 14, 177, 37, "paddingHorizontal"], [272, 31, 177, 54], [272, 33, 177, 56], [273, 12, 177, 59], [274, 10, 177, 61], [275, 12, 177, 61, "fileName"], [275, 20, 177, 61], [275, 22, 177, 61, "_jsxFileName"], [275, 34, 177, 61], [276, 12, 177, 61, "lineNumber"], [276, 22, 177, 61], [277, 12, 177, 61, "columnNumber"], [277, 24, 177, 61], [278, 10, 177, 61], [278, 17, 178, 11], [279, 8, 178, 12], [279, 58, 178, 12, "_jsxDevRuntime"], [279, 72, 178, 12], [279, 73, 178, 12, "jsxDEV"], [279, 79, 178, 12], [279, 81, 178, 12, "_nativewind"], [279, 92, 178, 12], [279, 93, 178, 12, "StyledComponent"], [279, 108, 178, 12], [280, 10, 182, 14, "className"], [280, 19, 182, 23], [280, 21, 182, 24], [280, 27, 182, 30], [281, 10, 182, 30, "component"], [281, 19, 182, 30], [281, 21, 182, 30, "_reactNative"], [281, 33, 182, 30], [281, 34, 182, 30, "View"], [281, 38, 182, 30], [282, 10, 182, 30, "children"], [282, 18, 182, 30], [282, 38, 182, 30, "_jsxDevRuntime"], [282, 52, 182, 30], [282, 53, 182, 30, "jsxDEV"], [282, 59, 182, 30], [282, 61, 182, 30, "_nativewind"], [282, 72, 182, 30], [282, 73, 182, 30, "StyledComponent"], [282, 88, 182, 30], [283, 12, 183, 16, "className"], [283, 21, 183, 25], [283, 23, 183, 26], [283, 66, 183, 69], [284, 12, 183, 69, "component"], [284, 21, 183, 69], [284, 23, 183, 69, "_reactNative"], [284, 35, 183, 69], [284, 36, 183, 69, "Text"], [284, 40, 183, 69], [285, 12, 183, 69, "children"], [285, 20, 183, 69], [285, 22, 183, 70], [286, 10, 183, 80], [286, 56, 184, 10], [286, 60, 184, 10, "_jsxDevRuntime"], [286, 74, 184, 10], [286, 75, 184, 10, "jsxDEV"], [286, 81, 184, 10], [286, 83, 184, 11, "_reactNative"], [286, 95, 184, 11], [286, 96, 184, 11, "FlatList"], [286, 104, 184, 19], [287, 12, 185, 12, "data"], [287, 16, 185, 16], [287, 18, 185, 18, "categories"], [287, 28, 185, 29], [288, 12, 186, 12, "renderItem"], [288, 22, 186, 22], [288, 24, 186, 24, "renderCategory"], [288, 38, 186, 39], [289, 12, 187, 12, "keyExtractor"], [289, 24, 187, 24], [289, 26, 187, 27, "item"], [289, 30, 187, 31], [289, 34, 187, 36, "item"], [289, 38, 187, 40], [289, 39, 187, 41, "id"], [289, 41, 187, 44], [290, 12, 188, 12, "horizontal"], [290, 22, 188, 22], [291, 12, 189, 12, "showsHorizontalScrollIndicator"], [291, 42, 189, 42], [291, 44, 189, 44], [291, 49, 189, 50], [292, 12, 190, 12, "contentContainerStyle"], [292, 33, 190, 33], [292, 35, 190, 35], [293, 14, 190, 37, "paddingHorizontal"], [293, 31, 190, 54], [293, 33, 190, 56], [294, 12, 190, 59], [295, 10, 190, 61], [296, 12, 190, 61, "fileName"], [296, 20, 190, 61], [296, 22, 190, 61, "_jsxFileName"], [296, 34, 190, 61], [297, 12, 190, 61, "lineNumber"], [297, 22, 190, 61], [298, 12, 190, 61, "columnNumber"], [298, 24, 190, 61], [299, 10, 190, 61], [299, 17, 191, 11], [299, 18, 191, 12], [300, 8, 191, 12], [300, 57, 191, 12, "_jsxDevRuntime"], [300, 71, 191, 12], [300, 72, 191, 12, "jsxDEV"], [300, 78, 191, 12], [300, 80, 191, 12, "_nativewind"], [300, 91, 191, 12], [300, 92, 191, 12, "StyledComponent"], [300, 107, 191, 12], [301, 10, 195, 14, "className"], [301, 19, 195, 23], [301, 21, 195, 24], [301, 27, 195, 30], [302, 10, 195, 30, "component"], [302, 19, 195, 30], [302, 21, 195, 30, "_reactNative"], [302, 33, 195, 30], [302, 34, 195, 30, "View"], [302, 38, 195, 30], [303, 10, 195, 30, "children"], [303, 18, 195, 30], [303, 38, 195, 30, "_jsxDevRuntime"], [303, 52, 195, 30], [303, 53, 195, 30, "jsxDEV"], [303, 59, 195, 30], [303, 61, 195, 30, "_nativewind"], [303, 72, 195, 30], [303, 73, 195, 30, "StyledComponent"], [303, 88, 195, 30], [304, 12, 196, 16, "className"], [304, 21, 196, 25], [304, 23, 196, 26], [304, 72, 196, 75], [305, 12, 196, 75, "component"], [305, 21, 196, 75], [305, 23, 196, 75, "_reactNative"], [305, 35, 196, 75], [305, 36, 196, 75, "View"], [305, 40, 196, 75], [306, 12, 196, 75, "children"], [306, 20, 196, 75], [306, 40, 196, 75, "_jsxDevRuntime"], [306, 54, 196, 75], [306, 55, 196, 75, "jsxDEV"], [306, 61, 196, 75], [306, 63, 196, 75, "_nativewind"], [306, 74, 196, 75], [306, 75, 196, 75, "StyledComponent"], [306, 90, 196, 75], [307, 14, 197, 18, "className"], [307, 23, 197, 27], [307, 25, 197, 28], [307, 58, 197, 61], [308, 14, 197, 61, "component"], [308, 23, 197, 61], [308, 25, 197, 61, "_reactNative"], [308, 37, 197, 61], [308, 38, 197, 61, "Text"], [308, 42, 197, 61], [309, 14, 197, 61, "children"], [309, 22, 197, 61], [309, 24, 197, 62], [310, 12, 197, 82], [310, 58, 198, 12], [310, 62, 198, 12, "_jsxDevRuntime"], [310, 76, 198, 12], [310, 77, 198, 12, "jsxDEV"], [310, 83, 198, 12], [310, 85, 198, 13, "_reactNative"], [310, 97, 198, 13], [310, 98, 198, 13, "TouchableOpacity"], [310, 114, 198, 29], [311, 14, 198, 29, "children"], [311, 22, 198, 29], [311, 41, 198, 29, "_jsxDevRuntime"], [311, 55, 198, 29], [311, 56, 198, 29, "jsxDEV"], [311, 62, 198, 29], [311, 64, 198, 29, "_nativewind"], [311, 75, 198, 29], [311, 76, 198, 29, "StyledComponent"], [311, 91, 198, 29], [312, 16, 199, 20, "className"], [312, 25, 199, 29], [312, 27, 199, 30], [312, 56, 199, 59], [313, 16, 199, 59, "component"], [313, 25, 199, 59], [313, 27, 199, 59, "_reactNative"], [313, 39, 199, 59], [313, 40, 199, 59, "Text"], [313, 44, 199, 59], [314, 16, 199, 59, "children"], [314, 24, 199, 59], [314, 26, 199, 60], [315, 14, 199, 67], [316, 12, 199, 67], [317, 14, 199, 67, "fileName"], [317, 22, 199, 67], [317, 24, 199, 67, "_jsxFileName"], [317, 36, 199, 67], [318, 14, 199, 67, "lineNumber"], [318, 24, 199, 67], [319, 14, 199, 67, "columnNumber"], [319, 26, 199, 67], [320, 12, 199, 67], [320, 19, 200, 30], [320, 20, 200, 31], [321, 10, 200, 31], [321, 55, 202, 10], [321, 59, 202, 10, "_jsxDevRuntime"], [321, 73, 202, 10], [321, 74, 202, 10, "jsxDEV"], [321, 80, 202, 10], [321, 82, 202, 11, "_reactNative"], [321, 94, 202, 11], [321, 95, 202, 11, "FlatList"], [321, 103, 202, 19], [322, 12, 203, 12, "data"], [322, 16, 203, 16], [322, 18, 203, 18, "featuredRestaurants"], [322, 37, 203, 38], [323, 12, 204, 12, "renderItem"], [323, 22, 204, 22], [323, 24, 204, 24, "renderRestaurant"], [323, 40, 204, 41], [324, 12, 205, 12, "keyExtractor"], [324, 24, 205, 24], [324, 26, 205, 27, "item"], [324, 30, 205, 31], [324, 34, 205, 36, "item"], [324, 38, 205, 40], [324, 39, 205, 41, "id"], [324, 41, 205, 44], [325, 12, 206, 12, "horizontal"], [325, 22, 206, 22], [326, 12, 207, 12, "showsHorizontalScrollIndicator"], [326, 42, 207, 42], [326, 44, 207, 44], [326, 49, 207, 50], [327, 12, 208, 12, "contentContainerStyle"], [327, 33, 208, 33], [327, 35, 208, 35], [328, 14, 208, 37, "paddingHorizontal"], [328, 31, 208, 54], [328, 33, 208, 56], [329, 12, 208, 59], [330, 10, 208, 61], [331, 12, 208, 61, "fileName"], [331, 20, 208, 61], [331, 22, 208, 61, "_jsxFileName"], [331, 34, 208, 61], [332, 12, 208, 61, "lineNumber"], [332, 22, 208, 61], [333, 12, 208, 61, "columnNumber"], [333, 24, 208, 61], [334, 10, 208, 61], [334, 17, 209, 11], [334, 18, 209, 12], [335, 8, 209, 12], [335, 57, 209, 12, "_jsxDevRuntime"], [335, 71, 209, 12], [335, 72, 209, 12, "jsxDEV"], [335, 78, 209, 12], [335, 80, 209, 12, "_nativewind"], [335, 91, 209, 12], [335, 92, 209, 12, "StyledComponent"], [335, 107, 209, 12], [336, 10, 213, 14, "className"], [336, 19, 213, 23], [336, 21, 213, 24], [336, 32, 213, 35], [337, 10, 213, 35, "component"], [337, 19, 213, 35], [337, 21, 213, 35, "_reactNative"], [337, 33, 213, 35], [337, 34, 213, 35, "View"], [337, 38, 213, 35], [338, 10, 213, 35, "children"], [338, 18, 213, 35], [338, 38, 213, 35, "_jsxDevRuntime"], [338, 52, 213, 35], [338, 53, 213, 35, "jsxDEV"], [338, 59, 213, 35], [338, 61, 213, 35, "_nativewind"], [338, 72, 213, 35], [338, 73, 213, 35, "StyledComponent"], [338, 88, 213, 35], [339, 12, 214, 16, "className"], [339, 21, 214, 25], [339, 23, 214, 26], [339, 61, 214, 64], [340, 12, 214, 64, "component"], [340, 21, 214, 64], [340, 23, 214, 64, "_reactNative"], [340, 35, 214, 64], [340, 36, 214, 64, "Text"], [340, 40, 214, 64], [341, 12, 214, 64, "children"], [341, 20, 214, 64], [341, 22, 214, 65], [342, 10, 214, 78], [342, 60, 214, 78, "_jsxDevRuntime"], [342, 74, 214, 78], [342, 75, 214, 78, "jsxDEV"], [342, 81, 214, 78], [342, 83, 214, 78, "_nativewind"], [342, 94, 214, 78], [342, 95, 214, 78, "StyledComponent"], [342, 110, 214, 78], [343, 12, 215, 16, "className"], [343, 21, 215, 25], [343, 23, 215, 26], [343, 49, 215, 52], [344, 12, 215, 52, "component"], [344, 21, 215, 52], [344, 23, 215, 52, "_reactNative"], [344, 35, 215, 52], [344, 36, 215, 52, "View"], [344, 40, 215, 52], [345, 12, 215, 52, "children"], [345, 20, 215, 52], [345, 40, 215, 52, "_jsxDevRuntime"], [345, 54, 215, 52], [345, 55, 215, 52, "jsxDEV"], [345, 61, 215, 52], [345, 63, 215, 52, "_nativewind"], [345, 74, 215, 52], [345, 75, 215, 52, "StyledComponent"], [345, 90, 215, 52], [346, 14, 217, 14, "className"], [346, 23, 217, 23], [346, 25, 217, 24], [346, 85, 217, 84], [347, 14, 218, 14, "onPress"], [347, 21, 218, 21], [347, 23, 218, 23, "onPress"], [347, 24, 218, 23], [347, 29, 218, 29, "navigation"], [347, 39, 218, 39], [347, 40, 218, 40, "navigate"], [347, 48, 218, 48], [347, 49, 218, 49], [347, 57, 218, 57], [347, 58, 218, 59], [348, 14, 218, 59, "component"], [348, 23, 218, 59], [348, 25, 218, 59, "_reactNative"], [348, 37, 218, 59], [348, 38, 218, 59, "TouchableOpacity"], [348, 54, 218, 59], [349, 14, 218, 59, "children"], [349, 22, 218, 59], [349, 38, 220, 14], [349, 42, 220, 14, "_jsxDevRuntime"], [349, 56, 220, 14], [349, 57, 220, 14, "jsxDEV"], [349, 63, 220, 14], [349, 65, 220, 15, "_vectorIcons"], [349, 77, 220, 15], [349, 78, 220, 15, "Ionicons"], [349, 86, 220, 23], [350, 16, 220, 24, "name"], [350, 20, 220, 28], [350, 22, 220, 29], [350, 39, 220, 46], [351, 16, 220, 47, "size"], [351, 20, 220, 51], [351, 22, 220, 53], [351, 24, 220, 56], [352, 16, 220, 57, "color"], [352, 21, 220, 62], [352, 23, 220, 63], [353, 14, 220, 72], [354, 16, 220, 72, "fileName"], [354, 24, 220, 72], [354, 26, 220, 72, "_jsxFileName"], [354, 38, 220, 72], [355, 16, 220, 72, "lineNumber"], [355, 26, 220, 72], [356, 16, 220, 72, "columnNumber"], [356, 28, 220, 72], [357, 14, 220, 72], [357, 21, 220, 74], [357, 22, 220, 75], [357, 41, 220, 75, "_jsxDevRuntime"], [357, 55, 220, 75], [357, 56, 220, 75, "jsxDEV"], [357, 62, 220, 75], [357, 64, 220, 75, "_nativewind"], [357, 75, 220, 75], [357, 76, 220, 75, "StyledComponent"], [357, 91, 220, 75], [358, 16, 221, 20, "className"], [358, 25, 221, 29], [358, 27, 221, 30], [358, 59, 221, 62], [359, 16, 221, 62, "component"], [359, 25, 221, 62], [359, 27, 221, 62, "_reactNative"], [359, 39, 221, 62], [359, 40, 221, 62, "Text"], [359, 44, 221, 62], [360, 16, 221, 62, "children"], [360, 24, 221, 62], [360, 26, 221, 63], [361, 14, 221, 72], [362, 12, 221, 72], [362, 61, 221, 72, "_jsxDevRuntime"], [362, 75, 221, 72], [362, 76, 221, 72, "jsxDEV"], [362, 82, 221, 72], [362, 84, 221, 72, "_nativewind"], [362, 95, 221, 72], [362, 96, 221, 72, "StyledComponent"], [362, 111, 221, 72], [363, 14, 224, 14, "className"], [363, 23, 224, 23], [363, 25, 224, 24], [363, 85, 224, 84], [364, 14, 225, 14, "onPress"], [364, 21, 225, 21], [364, 23, 225, 23, "onPress"], [364, 24, 225, 23], [364, 29, 225, 29, "navigation"], [364, 39, 225, 39], [364, 40, 225, 40, "navigate"], [364, 48, 225, 48], [364, 49, 225, 49], [364, 59, 225, 59], [364, 60, 225, 61], [365, 14, 225, 61, "component"], [365, 23, 225, 61], [365, 25, 225, 61, "_reactNative"], [365, 37, 225, 61], [365, 38, 225, 61, "TouchableOpacity"], [365, 54, 225, 61], [366, 14, 225, 61, "children"], [366, 22, 225, 61], [366, 38, 227, 14], [366, 42, 227, 14, "_jsxDevRuntime"], [366, 56, 227, 14], [366, 57, 227, 14, "jsxDEV"], [366, 63, 227, 14], [366, 65, 227, 15, "_vectorIcons"], [366, 77, 227, 15], [366, 78, 227, 15, "Ionicons"], [366, 86, 227, 23], [367, 16, 227, 24, "name"], [367, 20, 227, 28], [367, 22, 227, 29], [367, 37, 227, 44], [368, 16, 227, 45, "size"], [368, 20, 227, 49], [368, 22, 227, 51], [368, 24, 227, 54], [369, 16, 227, 55, "color"], [369, 21, 227, 60], [369, 23, 227, 61], [370, 14, 227, 70], [371, 16, 227, 70, "fileName"], [371, 24, 227, 70], [371, 26, 227, 70, "_jsxFileName"], [371, 38, 227, 70], [372, 16, 227, 70, "lineNumber"], [372, 26, 227, 70], [373, 16, 227, 70, "columnNumber"], [373, 28, 227, 70], [374, 14, 227, 70], [374, 21, 227, 72], [374, 22, 227, 73], [374, 41, 227, 73, "_jsxDevRuntime"], [374, 55, 227, 73], [374, 56, 227, 73, "jsxDEV"], [374, 62, 227, 73], [374, 64, 227, 73, "_nativewind"], [374, 75, 227, 73], [374, 76, 227, 73, "StyledComponent"], [374, 91, 227, 73], [375, 16, 228, 20, "className"], [375, 25, 228, 29], [375, 27, 228, 30], [375, 59, 228, 62], [376, 16, 228, 62, "component"], [376, 25, 228, 62], [376, 27, 228, 62, "_reactNative"], [376, 39, 228, 62], [376, 40, 228, 62, "Text"], [376, 44, 228, 62], [377, 16, 228, 62, "children"], [377, 24, 228, 62], [377, 26, 228, 63], [378, 14, 228, 72], [379, 12, 228, 72], [379, 61, 228, 72, "_jsxDevRuntime"], [379, 75, 228, 72], [379, 76, 228, 72, "jsxDEV"], [379, 82, 228, 72], [379, 84, 228, 72, "_nativewind"], [379, 95, 228, 72], [379, 96, 228, 72, "StyledComponent"], [379, 111, 228, 72], [380, 14, 231, 14, "className"], [380, 23, 231, 23], [380, 25, 231, 24], [380, 85, 231, 84], [381, 14, 232, 14, "onPress"], [381, 21, 232, 21], [381, 23, 232, 23, "onPress"], [381, 24, 232, 23], [381, 29, 232, 29, "navigation"], [381, 39, 232, 39], [381, 40, 232, 40, "navigate"], [381, 48, 232, 48], [381, 49, 232, 49], [381, 58, 232, 58], [381, 59, 232, 60], [382, 14, 232, 60, "component"], [382, 23, 232, 60], [382, 25, 232, 60, "_reactNative"], [382, 37, 232, 60], [382, 38, 232, 60, "TouchableOpacity"], [382, 54, 232, 60], [383, 14, 232, 60, "children"], [383, 22, 232, 60], [383, 38, 234, 14], [383, 42, 234, 14, "_jsxDevRuntime"], [383, 56, 234, 14], [383, 57, 234, 14, "jsxDEV"], [383, 63, 234, 14], [383, 65, 234, 15, "_vectorIcons"], [383, 77, 234, 15], [383, 78, 234, 15, "Ionicons"], [383, 86, 234, 23], [384, 16, 234, 24, "name"], [384, 20, 234, 28], [384, 22, 234, 29], [384, 38, 234, 45], [385, 16, 234, 46, "size"], [385, 20, 234, 50], [385, 22, 234, 52], [385, 24, 234, 55], [386, 16, 234, 56, "color"], [386, 21, 234, 61], [386, 23, 234, 62], [387, 14, 234, 71], [388, 16, 234, 71, "fileName"], [388, 24, 234, 71], [388, 26, 234, 71, "_jsxFileName"], [388, 38, 234, 71], [389, 16, 234, 71, "lineNumber"], [389, 26, 234, 71], [390, 16, 234, 71, "columnNumber"], [390, 28, 234, 71], [391, 14, 234, 71], [391, 21, 234, 73], [391, 22, 234, 74], [391, 41, 234, 74, "_jsxDevRuntime"], [391, 55, 234, 74], [391, 56, 234, 74, "jsxDEV"], [391, 62, 234, 74], [391, 64, 234, 74, "_nativewind"], [391, 75, 234, 74], [391, 76, 234, 74, "StyledComponent"], [391, 91, 234, 74], [392, 16, 235, 20, "className"], [392, 25, 235, 29], [392, 27, 235, 30], [392, 59, 235, 62], [393, 16, 235, 62, "component"], [393, 25, 235, 62], [393, 27, 235, 62, "_reactNative"], [393, 39, 235, 62], [393, 40, 235, 62, "Text"], [393, 44, 235, 62], [394, 16, 235, 62, "children"], [394, 24, 235, 62], [394, 26, 235, 63], [395, 14, 235, 70], [396, 12, 235, 70], [397, 10, 235, 70], [398, 8, 235, 70], [398, 57, 235, 70, "_jsxDevRuntime"], [398, 71, 235, 70], [398, 72, 235, 70, "jsxDEV"], [398, 78, 235, 70], [398, 80, 235, 70, "_nativewind"], [398, 91, 235, 70], [398, 92, 235, 70, "StyledComponent"], [398, 107, 235, 70], [399, 10, 241, 14, "className"], [399, 19, 241, 23], [399, 21, 241, 24], [399, 27, 241, 30], [400, 10, 241, 30, "component"], [400, 19, 241, 30], [400, 21, 241, 30, "_reactNative"], [400, 33, 241, 30], [400, 34, 241, 30, "View"], [401, 8, 241, 30], [402, 6, 241, 30], [402, 51, 245, 6], [402, 55, 245, 6, "_jsxDevRuntime"], [402, 69, 245, 6], [402, 70, 245, 6, "jsxDEV"], [402, 76, 245, 6], [402, 78, 245, 7, "_FooterNavigation"], [402, 95, 245, 7], [402, 96, 245, 7, "default"], [402, 103, 245, 23], [403, 8, 245, 24, "navigation"], [403, 18, 245, 34], [403, 20, 245, 36, "navigation"], [403, 30, 245, 47], [404, 8, 245, 48, "activeScreen"], [404, 20, 245, 60], [404, 22, 245, 61], [405, 6, 245, 67], [406, 8, 245, 67, "fileName"], [406, 16, 245, 67], [406, 18, 245, 67, "_jsxFileName"], [406, 30, 245, 67], [407, 8, 245, 67, "lineNumber"], [407, 18, 245, 67], [408, 8, 245, 67, "columnNumber"], [408, 20, 245, 67], [409, 6, 245, 67], [409, 13, 245, 69], [409, 14, 245, 70], [409, 29, 248, 6], [409, 33, 248, 6, "_jsxDevRuntime"], [409, 47, 248, 6], [409, 48, 248, 6, "jsxDEV"], [409, 54, 248, 6], [409, 56, 248, 7, "_reactNativeSafeAreaContext"], [409, 83, 248, 7], [409, 84, 248, 7, "SafeAreaView"], [409, 96, 248, 19], [410, 8, 248, 20, "style"], [410, 13, 248, 25], [410, 15, 248, 27], [411, 10, 248, 29, "backgroundColor"], [411, 25, 248, 44], [411, 27, 248, 46], [412, 8, 248, 56], [412, 9, 248, 58], [413, 8, 248, 59, "edges"], [413, 13, 248, 64], [413, 15, 248, 66], [413, 16, 248, 67], [413, 24, 248, 75], [414, 6, 248, 77], [415, 8, 248, 77, "fileName"], [415, 16, 248, 77], [415, 18, 248, 77, "_jsxFileName"], [415, 30, 248, 77], [416, 8, 248, 77, "lineNumber"], [416, 18, 248, 77], [417, 8, 248, 77, "columnNumber"], [417, 20, 248, 77], [418, 6, 248, 77], [418, 13, 248, 79], [418, 14, 248, 80], [419, 4, 248, 80], [420, 2, 251, 0], [421, 2, 251, 1, "_s"], [421, 4, 251, 1], [421, 5, 78, 24, "HomeScreen"], [421, 15, 78, 34], [422, 2, 78, 34, "_c"], [422, 4, 78, 34], [422, 7, 78, 24, "HomeScreen"], [422, 17, 78, 34], [423, 2, 78, 34, "_nativewind"], [423, 13, 78, 34], [423, 14, 78, 34, "NativeWindStyleSheet"], [423, 34, 78, 34], [423, 35, 78, 34, "create"], [423, 41, 78, 34], [424, 4, 78, 34, "styles"], [424, 10, 78, 34], [425, 6, 78, 34], [426, 8, 78, 34], [427, 8, 78, 34], [428, 6, 78, 34], [429, 6, 78, 34], [430, 8, 78, 34], [431, 10, 78, 34], [432, 10, 78, 34], [433, 8, 78, 34], [434, 8, 78, 34], [435, 8, 78, 34], [436, 8, 78, 34], [437, 6, 78, 34], [438, 6, 78, 34], [439, 8, 78, 34], [440, 10, 78, 34], [441, 10, 78, 34], [442, 8, 78, 34], [443, 8, 78, 34], [444, 8, 78, 34], [445, 8, 78, 34], [446, 6, 78, 34], [447, 6, 78, 34], [448, 8, 78, 34], [449, 6, 78, 34], [450, 6, 78, 34], [451, 8, 78, 34], [452, 6, 78, 34], [453, 6, 78, 34], [454, 8, 78, 34], [455, 6, 78, 34], [456, 6, 78, 34], [457, 8, 78, 34], [458, 6, 78, 34], [459, 6, 78, 34], [460, 8, 78, 34], [461, 8, 78, 34], [462, 6, 78, 34], [463, 6, 78, 34], [464, 8, 78, 34], [465, 8, 78, 34], [466, 6, 78, 34], [467, 6, 78, 34], [468, 8, 78, 34], [469, 6, 78, 34], [470, 6, 78, 34], [471, 8, 78, 34], [472, 6, 78, 34], [473, 6, 78, 34], [474, 8, 78, 34], [475, 6, 78, 34], [476, 6, 78, 34], [477, 8, 78, 34], [478, 6, 78, 34], [479, 6, 78, 34], [480, 8, 78, 34], [481, 6, 78, 34], [482, 6, 78, 34], [483, 8, 78, 34], [484, 6, 78, 34], [485, 6, 78, 34], [486, 8, 78, 34], [487, 6, 78, 34], [488, 6, 78, 34], [489, 8, 78, 34], [490, 6, 78, 34], [491, 6, 78, 34], [492, 8, 78, 34], [493, 6, 78, 34], [494, 6, 78, 34], [495, 8, 78, 34], [496, 6, 78, 34], [497, 6, 78, 34], [498, 8, 78, 34], [499, 6, 78, 34], [500, 6, 78, 34], [501, 8, 78, 34], [502, 6, 78, 34], [503, 6, 78, 34], [504, 8, 78, 34], [505, 6, 78, 34], [506, 6, 78, 34], [507, 8, 78, 34], [508, 6, 78, 34], [509, 6, 78, 34], [510, 8, 78, 34], [511, 6, 78, 34], [512, 6, 78, 34], [513, 8, 78, 34], [514, 8, 78, 34], [515, 8, 78, 34], [516, 6, 78, 34], [517, 6, 78, 34], [518, 8, 78, 34], [519, 6, 78, 34], [520, 6, 78, 34], [521, 8, 78, 34], [522, 6, 78, 34], [523, 6, 78, 34], [524, 8, 78, 34], [525, 6, 78, 34], [526, 6, 78, 34], [527, 8, 78, 34], [528, 6, 78, 34], [529, 6, 78, 34], [530, 8, 78, 34], [531, 6, 78, 34], [532, 6, 78, 34], [533, 8, 78, 34], [534, 6, 78, 34], [535, 6, 78, 34], [536, 8, 78, 34], [537, 8, 78, 34], [538, 8, 78, 34], [539, 8, 78, 34], [540, 6, 78, 34], [541, 6, 78, 34], [542, 8, 78, 34], [543, 8, 78, 34], [544, 8, 78, 34], [545, 8, 78, 34], [546, 6, 78, 34], [547, 6, 78, 34], [548, 8, 78, 34], [549, 8, 78, 34], [550, 8, 78, 34], [551, 8, 78, 34], [552, 6, 78, 34], [553, 6, 78, 34], [554, 8, 78, 34], [555, 8, 78, 34], [556, 6, 78, 34], [557, 6, 78, 34], [558, 8, 78, 34], [559, 6, 78, 34], [560, 6, 78, 34], [561, 8, 78, 34], [562, 6, 78, 34], [563, 6, 78, 34], [564, 8, 78, 34], [565, 6, 78, 34], [566, 6, 78, 34], [567, 8, 78, 34], [568, 8, 78, 34], [569, 8, 78, 34], [570, 8, 78, 34], [571, 6, 78, 34], [572, 6, 78, 34], [573, 8, 78, 34], [574, 8, 78, 34], [575, 8, 78, 34], [576, 8, 78, 34], [577, 6, 78, 34], [578, 6, 78, 34], [579, 8, 78, 34], [580, 8, 78, 34], [581, 6, 78, 34], [582, 6, 78, 34], [583, 8, 78, 34], [584, 8, 78, 34], [585, 6, 78, 34], [586, 6, 78, 34], [587, 8, 78, 34], [588, 8, 78, 34], [589, 6, 78, 34], [590, 6, 78, 34], [591, 8, 78, 34], [592, 8, 78, 34], [593, 6, 78, 34], [594, 6, 78, 34], [595, 8, 78, 34], [596, 6, 78, 34], [597, 6, 78, 34], [598, 8, 78, 34], [599, 6, 78, 34], [600, 6, 78, 34], [601, 8, 78, 34], [602, 6, 78, 34], [603, 6, 78, 34], [604, 8, 78, 34], [605, 6, 78, 34], [606, 6, 78, 34], [607, 8, 78, 34], [608, 6, 78, 34], [609, 6, 78, 34], [610, 8, 78, 34], [611, 6, 78, 34], [612, 6, 78, 34], [613, 8, 78, 34], [614, 6, 78, 34], [615, 6, 78, 34], [616, 8, 78, 34], [617, 6, 78, 34], [618, 6, 78, 34], [619, 8, 78, 34], [620, 6, 78, 34], [621, 6, 78, 34], [622, 8, 78, 34], [623, 6, 78, 34], [624, 6, 78, 34], [625, 8, 78, 34], [626, 8, 78, 34], [627, 6, 78, 34], [628, 6, 78, 34], [629, 8, 78, 34], [630, 8, 78, 34], [631, 6, 78, 34], [632, 6, 78, 34], [633, 8, 78, 34], [634, 8, 78, 34], [635, 6, 78, 34], [636, 6, 78, 34], [637, 8, 78, 34], [638, 8, 78, 34], [639, 6, 78, 34], [640, 6, 78, 34], [641, 8, 78, 34], [642, 8, 78, 34], [643, 6, 78, 34], [644, 6, 78, 34], [645, 8, 78, 34], [646, 6, 78, 34], [647, 4, 78, 34], [648, 4, 78, 34, "atRules"], [648, 11, 78, 34], [649, 6, 78, 34], [650, 4, 78, 34], [651, 2, 78, 34], [652, 2, 78, 34], [652, 6, 78, 34, "_c"], [652, 8, 78, 34], [653, 2, 78, 34, "$RefreshReg$"], [653, 14, 78, 34], [653, 15, 78, 34, "_c"], [653, 17, 78, 34], [654, 0, 78, 34], [654, 3]], "functionMap": {"names": ["<global>", "HomeScreen", "handleNotificationPress", "handleWishlistPress", "renderCategory", "renderRestaurant", "item.tags.map$argument_0", "renderPromo", "FlatList.props.keyExtractor", "TouchableOpacity.props.onPress"], "mappings": "AAA;eC6E;kCCG;GDE;8BEE;GFE;yBGE;GHU;2BIE;yBCyB;WDI;GJI;sBME;GNoB;0BOkB,iBP;0BOa,iBP;0BOkB,iBP;uBQa,mCR;uBQO,qCR;uBQO,oCR;CDmB"}}, "type": "js/module"}]}