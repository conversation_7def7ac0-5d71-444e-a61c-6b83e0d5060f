{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 46}, "end": {"line": 2, "column": 74, "index": 120}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./object-utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 121}, "end": {"line": 3, "column": 44, "index": 165}}], "key": "0cv5dvb10zWeCI3DlVEWSegHvJY=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = createIconButtonComponent;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[7], \"react\"));\n  var _reactNative = require(_dependencyMap[8], \"react-native\");\n  var _objectUtils = require(_dependencyMap[9], \"./object-utils\");\n  var _jsxDevRuntime = require(_dependencyMap[10], \"react/jsx-dev-runtime\");\n  var _excluded = [\"style\", \"iconStyle\", \"children\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\@expo\\\\vector-icons\\\\build\\\\vendor\\\\react-native-vector-icons\\\\lib\\\\icon-button.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flexDirection: 'row',\n      justifyContent: 'flex-start',\n      alignItems: 'center',\n      padding: 8\n    },\n    touchable: {\n      overflow: 'hidden'\n    },\n    icon: {\n      marginRight: 10\n    },\n    text: {\n      fontWeight: '600',\n      backgroundColor: 'transparent'\n    }\n  });\n  var IOS7_BLUE = '#007AFF';\n  var TEXT_PROP_NAMES = ['ellipsizeMode', 'numberOfLines', 'textBreakStrategy', 'selectable', 'suppressHighlighting', 'allowFontScaling', 'adjustsFontSizeToFit', 'minimumFontScale'];\n  var TOUCHABLE_PROP_NAMES = ['accessible', 'accessibilityLabel', 'accessibilityHint', 'accessibilityComponentType', 'accessibilityRole', 'accessibilityStates', 'accessibilityTraits', 'onFocus', 'onBlur', 'disabled', 'onPress', 'onPressIn', 'onPressOut', 'onLayout', 'onLongPress', 'nativeID', 'testID', 'delayPressIn', 'delayPressOut', 'delayLongPress', 'activeOpacity', 'underlayColor', 'selectionColor', 'onShowUnderlay', 'onHideUnderlay', 'hasTVPreferredFocus', 'tvParallaxProperties'];\n  function createIconButtonComponent(Icon) {\n    var _IconButton;\n    return _IconButton = /*#__PURE__*/function (_PureComponent) {\n      function IconButton() {\n        (0, _classCallCheck2.default)(this, IconButton);\n        return _callSuper(this, IconButton, arguments);\n      }\n      (0, _inherits2.default)(IconButton, _PureComponent);\n      return (0, _createClass2.default)(IconButton, [{\n        key: \"render\",\n        value: function render() {\n          var _this$props = this.props,\n            style = _this$props.style,\n            iconStyle = _this$props.iconStyle,\n            children = _this$props.children,\n            restProps = (0, _objectWithoutProperties2.default)(_this$props, _excluded);\n          var iconProps = (0, _objectUtils.pick)(restProps, TEXT_PROP_NAMES, 'style', 'name', 'size', 'color');\n          var touchableProps = (0, _objectUtils.pick)(restProps, TOUCHABLE_PROP_NAMES);\n          var props = (0, _objectUtils.omit)(restProps, Object.keys(iconProps), Object.keys(touchableProps), 'iconStyle', 'borderRadius', 'backgroundColor');\n          iconProps.style = iconStyle ? [styles.icon, iconStyle] : styles.icon;\n          var colorStyle = (0, _objectUtils.pick)(this.props, 'color');\n          var blockStyle = (0, _objectUtils.pick)(this.props, 'backgroundColor', 'borderRadius');\n          return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableHighlight, {\n            style: [styles.touchable, blockStyle],\n            ...touchableProps,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: [styles.container, blockStyle, style],\n              ...props,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Icon, {\n                ...iconProps\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 13\n              }, this), typeof children === 'string' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                style: [styles.text, colorStyle],\n                selectable: false,\n                children: children\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 15\n              }, this) : children]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 9\n          }, this);\n        }\n      }]);\n    }(_react.PureComponent), _IconButton.defaultProps = {\n      backgroundColor: IOS7_BLUE,\n      borderRadius: 5,\n      color: 'white',\n      size: 20\n    }, _IconButton;\n  }\n});", "lineCount": 105, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_react"], [13, 12, 1, 0], [13, 15, 1, 0, "_interopRequireWildcard"], [13, 38, 1, 0], [13, 39, 1, 0, "require"], [13, 46, 1, 0], [13, 47, 1, 0, "_dependencyMap"], [13, 61, 1, 0], [14, 2, 2, 0], [14, 6, 2, 0, "_reactNative"], [14, 18, 2, 0], [14, 21, 2, 0, "require"], [14, 28, 2, 0], [14, 29, 2, 0, "_dependencyMap"], [14, 43, 2, 0], [15, 2, 3, 0], [15, 6, 3, 0, "_objectUtils"], [15, 18, 3, 0], [15, 21, 3, 0, "require"], [15, 28, 3, 0], [15, 29, 3, 0, "_dependencyMap"], [15, 43, 3, 0], [16, 2, 3, 44], [16, 6, 3, 44, "_jsxDevRuntime"], [16, 20, 3, 44], [16, 23, 3, 44, "require"], [16, 30, 3, 44], [16, 31, 3, 44, "_dependencyMap"], [16, 45, 3, 44], [17, 2, 3, 44], [17, 6, 3, 44, "_excluded"], [17, 15, 3, 44], [18, 2, 3, 44], [18, 6, 3, 44, "_jsxFileName"], [18, 18, 3, 44], [19, 2, 3, 44], [19, 11, 3, 44, "_interopRequireWildcard"], [19, 35, 3, 44, "e"], [19, 36, 3, 44], [19, 38, 3, 44, "t"], [19, 39, 3, 44], [19, 68, 3, 44, "WeakMap"], [19, 75, 3, 44], [19, 81, 3, 44, "r"], [19, 82, 3, 44], [19, 89, 3, 44, "WeakMap"], [19, 96, 3, 44], [19, 100, 3, 44, "n"], [19, 101, 3, 44], [19, 108, 3, 44, "WeakMap"], [19, 115, 3, 44], [19, 127, 3, 44, "_interopRequireWildcard"], [19, 150, 3, 44], [19, 162, 3, 44, "_interopRequireWildcard"], [19, 163, 3, 44, "e"], [19, 164, 3, 44], [19, 166, 3, 44, "t"], [19, 167, 3, 44], [19, 176, 3, 44, "t"], [19, 177, 3, 44], [19, 181, 3, 44, "e"], [19, 182, 3, 44], [19, 186, 3, 44, "e"], [19, 187, 3, 44], [19, 188, 3, 44, "__esModule"], [19, 198, 3, 44], [19, 207, 3, 44, "e"], [19, 208, 3, 44], [19, 214, 3, 44, "o"], [19, 215, 3, 44], [19, 217, 3, 44, "i"], [19, 218, 3, 44], [19, 220, 3, 44, "f"], [19, 221, 3, 44], [19, 226, 3, 44, "__proto__"], [19, 235, 3, 44], [19, 243, 3, 44, "default"], [19, 250, 3, 44], [19, 252, 3, 44, "e"], [19, 253, 3, 44], [19, 270, 3, 44, "e"], [19, 271, 3, 44], [19, 294, 3, 44, "e"], [19, 295, 3, 44], [19, 320, 3, 44, "e"], [19, 321, 3, 44], [19, 330, 3, 44, "f"], [19, 331, 3, 44], [19, 337, 3, 44, "o"], [19, 338, 3, 44], [19, 341, 3, 44, "t"], [19, 342, 3, 44], [19, 345, 3, 44, "n"], [19, 346, 3, 44], [19, 349, 3, 44, "r"], [19, 350, 3, 44], [19, 358, 3, 44, "o"], [19, 359, 3, 44], [19, 360, 3, 44, "has"], [19, 363, 3, 44], [19, 364, 3, 44, "e"], [19, 365, 3, 44], [19, 375, 3, 44, "o"], [19, 376, 3, 44], [19, 377, 3, 44, "get"], [19, 380, 3, 44], [19, 381, 3, 44, "e"], [19, 382, 3, 44], [19, 385, 3, 44, "o"], [19, 386, 3, 44], [19, 387, 3, 44, "set"], [19, 390, 3, 44], [19, 391, 3, 44, "e"], [19, 392, 3, 44], [19, 394, 3, 44, "f"], [19, 395, 3, 44], [19, 409, 3, 44, "_t"], [19, 411, 3, 44], [19, 415, 3, 44, "e"], [19, 416, 3, 44], [19, 432, 3, 44, "_t"], [19, 434, 3, 44], [19, 441, 3, 44, "hasOwnProperty"], [19, 455, 3, 44], [19, 456, 3, 44, "call"], [19, 460, 3, 44], [19, 461, 3, 44, "e"], [19, 462, 3, 44], [19, 464, 3, 44, "_t"], [19, 466, 3, 44], [19, 473, 3, 44, "i"], [19, 474, 3, 44], [19, 478, 3, 44, "o"], [19, 479, 3, 44], [19, 482, 3, 44, "Object"], [19, 488, 3, 44], [19, 489, 3, 44, "defineProperty"], [19, 503, 3, 44], [19, 508, 3, 44, "Object"], [19, 514, 3, 44], [19, 515, 3, 44, "getOwnPropertyDescriptor"], [19, 539, 3, 44], [19, 540, 3, 44, "e"], [19, 541, 3, 44], [19, 543, 3, 44, "_t"], [19, 545, 3, 44], [19, 552, 3, 44, "i"], [19, 553, 3, 44], [19, 554, 3, 44, "get"], [19, 557, 3, 44], [19, 561, 3, 44, "i"], [19, 562, 3, 44], [19, 563, 3, 44, "set"], [19, 566, 3, 44], [19, 570, 3, 44, "o"], [19, 571, 3, 44], [19, 572, 3, 44, "f"], [19, 573, 3, 44], [19, 575, 3, 44, "_t"], [19, 577, 3, 44], [19, 579, 3, 44, "i"], [19, 580, 3, 44], [19, 584, 3, 44, "f"], [19, 585, 3, 44], [19, 586, 3, 44, "_t"], [19, 588, 3, 44], [19, 592, 3, 44, "e"], [19, 593, 3, 44], [19, 594, 3, 44, "_t"], [19, 596, 3, 44], [19, 607, 3, 44, "f"], [19, 608, 3, 44], [19, 613, 3, 44, "e"], [19, 614, 3, 44], [19, 616, 3, 44, "t"], [19, 617, 3, 44], [20, 2, 3, 44], [20, 11, 3, 44, "_callSuper"], [20, 22, 3, 44, "t"], [20, 23, 3, 44], [20, 25, 3, 44, "o"], [20, 26, 3, 44], [20, 28, 3, 44, "e"], [20, 29, 3, 44], [20, 40, 3, 44, "o"], [20, 41, 3, 44], [20, 48, 3, 44, "_getPrototypeOf2"], [20, 64, 3, 44], [20, 65, 3, 44, "default"], [20, 72, 3, 44], [20, 74, 3, 44, "o"], [20, 75, 3, 44], [20, 82, 3, 44, "_possibleConstructorReturn2"], [20, 109, 3, 44], [20, 110, 3, 44, "default"], [20, 117, 3, 44], [20, 119, 3, 44, "t"], [20, 120, 3, 44], [20, 122, 3, 44, "_isNativeReflectConstruct"], [20, 147, 3, 44], [20, 152, 3, 44, "Reflect"], [20, 159, 3, 44], [20, 160, 3, 44, "construct"], [20, 169, 3, 44], [20, 170, 3, 44, "o"], [20, 171, 3, 44], [20, 173, 3, 44, "e"], [20, 174, 3, 44], [20, 186, 3, 44, "_getPrototypeOf2"], [20, 202, 3, 44], [20, 203, 3, 44, "default"], [20, 210, 3, 44], [20, 212, 3, 44, "t"], [20, 213, 3, 44], [20, 215, 3, 44, "constructor"], [20, 226, 3, 44], [20, 230, 3, 44, "o"], [20, 231, 3, 44], [20, 232, 3, 44, "apply"], [20, 237, 3, 44], [20, 238, 3, 44, "t"], [20, 239, 3, 44], [20, 241, 3, 44, "e"], [20, 242, 3, 44], [21, 2, 3, 44], [21, 11, 3, 44, "_isNativeReflectConstruct"], [21, 37, 3, 44], [21, 51, 3, 44, "t"], [21, 52, 3, 44], [21, 56, 3, 44, "Boolean"], [21, 63, 3, 44], [21, 64, 3, 44, "prototype"], [21, 73, 3, 44], [21, 74, 3, 44, "valueOf"], [21, 81, 3, 44], [21, 82, 3, 44, "call"], [21, 86, 3, 44], [21, 87, 3, 44, "Reflect"], [21, 94, 3, 44], [21, 95, 3, 44, "construct"], [21, 104, 3, 44], [21, 105, 3, 44, "Boolean"], [21, 112, 3, 44], [21, 145, 3, 44, "t"], [21, 146, 3, 44], [21, 159, 3, 44, "_isNativeReflectConstruct"], [21, 184, 3, 44], [21, 196, 3, 44, "_isNativeReflectConstruct"], [21, 197, 3, 44], [21, 210, 3, 44, "t"], [21, 211, 3, 44], [22, 2, 5, 0], [22, 6, 5, 6, "styles"], [22, 12, 5, 12], [22, 15, 5, 15, "StyleSheet"], [22, 38, 5, 25], [22, 39, 5, 26, "create"], [22, 45, 5, 32], [22, 46, 5, 33], [23, 4, 6, 2, "container"], [23, 13, 6, 11], [23, 15, 6, 13], [24, 6, 7, 4, "flexDirection"], [24, 19, 7, 17], [24, 21, 7, 19], [24, 26, 7, 24], [25, 6, 8, 4, "justifyContent"], [25, 20, 8, 18], [25, 22, 8, 20], [25, 34, 8, 32], [26, 6, 9, 4, "alignItems"], [26, 16, 9, 14], [26, 18, 9, 16], [26, 26, 9, 24], [27, 6, 10, 4, "padding"], [27, 13, 10, 11], [27, 15, 10, 13], [28, 4, 11, 2], [28, 5, 11, 3], [29, 4, 12, 2, "touchable"], [29, 13, 12, 11], [29, 15, 12, 13], [30, 6, 13, 4, "overflow"], [30, 14, 13, 12], [30, 16, 13, 14], [31, 4, 14, 2], [31, 5, 14, 3], [32, 4, 15, 2, "icon"], [32, 8, 15, 6], [32, 10, 15, 8], [33, 6, 16, 4, "marginRight"], [33, 17, 16, 15], [33, 19, 16, 17], [34, 4, 17, 2], [34, 5, 17, 3], [35, 4, 18, 2, "text"], [35, 8, 18, 6], [35, 10, 18, 8], [36, 6, 19, 4, "fontWeight"], [36, 16, 19, 14], [36, 18, 19, 16], [36, 23, 19, 21], [37, 6, 20, 4, "backgroundColor"], [37, 21, 20, 19], [37, 23, 20, 21], [38, 4, 21, 2], [39, 2, 22, 0], [39, 3, 22, 1], [39, 4, 22, 2], [40, 2, 24, 0], [40, 6, 24, 6, "IOS7_BLUE"], [40, 15, 24, 15], [40, 18, 24, 18], [40, 27, 24, 27], [41, 2, 26, 0], [41, 6, 26, 6, "TEXT_PROP_NAMES"], [41, 21, 26, 21], [41, 24, 26, 24], [41, 25, 27, 2], [41, 40, 27, 17], [41, 42, 28, 2], [41, 57, 28, 17], [41, 59, 29, 2], [41, 78, 29, 21], [41, 80, 30, 2], [41, 92, 30, 14], [41, 94, 31, 2], [41, 116, 31, 24], [41, 118, 32, 2], [41, 136, 32, 20], [41, 138, 33, 2], [41, 160, 33, 24], [41, 162, 34, 2], [41, 180, 34, 20], [41, 181, 35, 1], [42, 2, 37, 0], [42, 6, 37, 6, "TOUCHABLE_PROP_NAMES"], [42, 26, 37, 26], [42, 29, 37, 29], [42, 30, 38, 2], [42, 42, 38, 14], [42, 44, 39, 2], [42, 64, 39, 22], [42, 66, 40, 2], [42, 85, 40, 21], [42, 87, 41, 2], [42, 115, 41, 30], [42, 117, 42, 2], [42, 136, 42, 21], [42, 138, 43, 2], [42, 159, 43, 23], [42, 161, 44, 2], [42, 182, 44, 23], [42, 184, 45, 2], [42, 193, 45, 11], [42, 195, 46, 2], [42, 203, 46, 10], [42, 205, 47, 2], [42, 215, 47, 12], [42, 217, 48, 2], [42, 226, 48, 11], [42, 228, 49, 2], [42, 239, 49, 13], [42, 241, 50, 2], [42, 253, 50, 14], [42, 255, 51, 2], [42, 265, 51, 12], [42, 267, 52, 2], [42, 280, 52, 15], [42, 282, 53, 2], [42, 292, 53, 12], [42, 294, 54, 2], [42, 302, 54, 10], [42, 304, 55, 2], [42, 318, 55, 16], [42, 320, 56, 2], [42, 335, 56, 17], [42, 337, 57, 2], [42, 353, 57, 18], [42, 355, 58, 2], [42, 370, 58, 17], [42, 372, 59, 2], [42, 387, 59, 17], [42, 389, 60, 2], [42, 405, 60, 18], [42, 407, 61, 2], [42, 423, 61, 18], [42, 425, 62, 2], [42, 441, 62, 18], [42, 443, 63, 2], [42, 464, 63, 23], [42, 466, 64, 2], [42, 488, 64, 24], [42, 489, 65, 1], [43, 2, 67, 15], [43, 11, 67, 24, "createIconButtonComponent"], [43, 36, 67, 49, "createIconButtonComponent"], [43, 37, 67, 50, "Icon"], [43, 41, 67, 54], [43, 43, 67, 56], [44, 4, 67, 56], [44, 8, 67, 56, "_IconButton"], [44, 19, 67, 56], [45, 4, 68, 2], [45, 11, 68, 2, "_IconButton"], [45, 22, 68, 2], [45, 48, 68, 2, "_PureComponent"], [45, 62, 68, 2], [46, 6, 68, 2], [46, 15, 68, 2, "IconButton"], [46, 26, 68, 2], [47, 8, 68, 2], [47, 12, 68, 2, "_classCallCheck2"], [47, 28, 68, 2], [47, 29, 68, 2, "default"], [47, 36, 68, 2], [47, 44, 68, 2, "IconButton"], [47, 54, 68, 2], [48, 8, 68, 2], [48, 15, 68, 2, "_callSuper"], [48, 25, 68, 2], [48, 32, 68, 2, "IconButton"], [48, 42, 68, 2], [48, 44, 68, 2, "arguments"], [48, 53, 68, 2], [49, 6, 68, 2], [50, 6, 68, 2], [50, 10, 68, 2, "_inherits2"], [50, 20, 68, 2], [50, 21, 68, 2, "default"], [50, 28, 68, 2], [50, 30, 68, 2, "IconButton"], [50, 40, 68, 2], [50, 42, 68, 2, "_PureComponent"], [50, 56, 68, 2], [51, 6, 68, 2], [51, 17, 68, 2, "_createClass2"], [51, 30, 68, 2], [51, 31, 68, 2, "default"], [51, 38, 68, 2], [51, 40, 68, 2, "IconButton"], [51, 50, 68, 2], [52, 8, 68, 2, "key"], [52, 11, 68, 2], [53, 8, 68, 2, "value"], [53, 13, 68, 2], [53, 15, 78, 4], [53, 24, 78, 4, "render"], [53, 30, 78, 10, "render"], [53, 31, 78, 10], [53, 33, 78, 13], [54, 10, 79, 6], [54, 14, 79, 6, "_this$props"], [54, 25, 79, 6], [54, 28, 79, 59], [54, 32, 79, 63], [54, 33, 79, 64, "props"], [54, 38, 79, 69], [55, 12, 79, 14, "style"], [55, 17, 79, 19], [55, 20, 79, 19, "_this$props"], [55, 31, 79, 19], [55, 32, 79, 14, "style"], [55, 37, 79, 19], [56, 12, 79, 21, "iconStyle"], [56, 21, 79, 30], [56, 24, 79, 30, "_this$props"], [56, 35, 79, 30], [56, 36, 79, 21, "iconStyle"], [56, 45, 79, 30], [57, 12, 79, 32, "children"], [57, 20, 79, 40], [57, 23, 79, 40, "_this$props"], [57, 34, 79, 40], [57, 35, 79, 32, "children"], [57, 43, 79, 40], [58, 12, 79, 45, "restProps"], [58, 21, 79, 54], [58, 28, 79, 54, "_objectWithoutProperties2"], [58, 53, 79, 54], [58, 54, 79, 54, "default"], [58, 61, 79, 54], [58, 63, 79, 54, "_this$props"], [58, 74, 79, 54], [58, 76, 79, 54, "_excluded"], [58, 85, 79, 54], [59, 10, 81, 6], [59, 14, 81, 12, "iconProps"], [59, 23, 81, 21], [59, 26, 81, 24], [59, 30, 81, 24, "pick"], [59, 47, 81, 28], [59, 49, 82, 8, "restProps"], [59, 58, 82, 17], [59, 60, 83, 8, "TEXT_PROP_NAMES"], [59, 75, 83, 23], [59, 77, 84, 8], [59, 84, 84, 15], [59, 86, 85, 8], [59, 92, 85, 14], [59, 94, 86, 8], [59, 100, 86, 14], [59, 102, 87, 8], [59, 109, 88, 6], [59, 110, 88, 7], [60, 10, 89, 6], [60, 14, 89, 12, "touchableProps"], [60, 28, 89, 26], [60, 31, 89, 29], [60, 35, 89, 29, "pick"], [60, 52, 89, 33], [60, 54, 89, 34, "restProps"], [60, 63, 89, 43], [60, 65, 89, 45, "TOUCHABLE_PROP_NAMES"], [60, 85, 89, 65], [60, 86, 89, 66], [61, 10, 90, 6], [61, 14, 90, 12, "props"], [61, 19, 90, 17], [61, 22, 90, 20], [61, 26, 90, 20, "omit"], [61, 43, 90, 24], [61, 45, 91, 8, "restProps"], [61, 54, 91, 17], [61, 56, 92, 8, "Object"], [61, 62, 92, 14], [61, 63, 92, 15, "keys"], [61, 67, 92, 19], [61, 68, 92, 20, "iconProps"], [61, 77, 92, 29], [61, 78, 92, 30], [61, 80, 93, 8, "Object"], [61, 86, 93, 14], [61, 87, 93, 15, "keys"], [61, 91, 93, 19], [61, 92, 93, 20, "touchableProps"], [61, 106, 93, 34], [61, 107, 93, 35], [61, 109, 94, 8], [61, 120, 94, 19], [61, 122, 95, 8], [61, 136, 95, 22], [61, 138, 96, 8], [61, 155, 97, 6], [61, 156, 97, 7], [62, 10, 98, 6, "iconProps"], [62, 19, 98, 15], [62, 20, 98, 16, "style"], [62, 25, 98, 21], [62, 28, 98, 24, "iconStyle"], [62, 37, 98, 33], [62, 40, 98, 36], [62, 41, 98, 37, "styles"], [62, 47, 98, 43], [62, 48, 98, 44, "icon"], [62, 52, 98, 48], [62, 54, 98, 50, "iconStyle"], [62, 63, 98, 59], [62, 64, 98, 60], [62, 67, 98, 63, "styles"], [62, 73, 98, 69], [62, 74, 98, 70, "icon"], [62, 78, 98, 74], [63, 10, 100, 6], [63, 14, 100, 12, "colorStyle"], [63, 24, 100, 22], [63, 27, 100, 25], [63, 31, 100, 25, "pick"], [63, 48, 100, 29], [63, 50, 100, 30], [63, 54, 100, 34], [63, 55, 100, 35, "props"], [63, 60, 100, 40], [63, 62, 100, 42], [63, 69, 100, 49], [63, 70, 100, 50], [64, 10, 101, 6], [64, 14, 101, 12, "blockStyle"], [64, 24, 101, 22], [64, 27, 101, 25], [64, 31, 101, 25, "pick"], [64, 48, 101, 29], [64, 50, 101, 30], [64, 54, 101, 34], [64, 55, 101, 35, "props"], [64, 60, 101, 40], [64, 62, 101, 42], [64, 79, 101, 59], [64, 81, 101, 61], [64, 95, 101, 75], [64, 96, 101, 76], [65, 10, 103, 6], [65, 30, 104, 8], [65, 34, 104, 8, "_jsxDevRuntime"], [65, 48, 104, 8], [65, 49, 104, 8, "jsxDEV"], [65, 55, 104, 8], [65, 57, 104, 9, "_reactNative"], [65, 69, 104, 9], [65, 70, 104, 9, "TouchableHighlight"], [65, 88, 104, 27], [66, 12, 105, 10, "style"], [66, 17, 105, 15], [66, 19, 105, 17], [66, 20, 105, 18, "styles"], [66, 26, 105, 24], [66, 27, 105, 25, "touchable"], [66, 36, 105, 34], [66, 38, 105, 36, "blockStyle"], [66, 48, 105, 46], [66, 49, 105, 48], [67, 12, 105, 48], [67, 15, 106, 14, "touchableProps"], [67, 29, 106, 28], [68, 12, 106, 28, "children"], [68, 20, 106, 28], [68, 35, 108, 10], [68, 39, 108, 10, "_jsxDevRuntime"], [68, 53, 108, 10], [68, 54, 108, 10, "jsxDEV"], [68, 60, 108, 10], [68, 62, 108, 11, "_reactNative"], [68, 74, 108, 11], [68, 75, 108, 11, "View"], [68, 79, 108, 15], [69, 14, 108, 16, "style"], [69, 19, 108, 21], [69, 21, 108, 23], [69, 22, 108, 24, "styles"], [69, 28, 108, 30], [69, 29, 108, 31, "container"], [69, 38, 108, 40], [69, 40, 108, 42, "blockStyle"], [69, 50, 108, 52], [69, 52, 108, 54, "style"], [69, 57, 108, 59], [69, 58, 108, 61], [70, 14, 108, 61], [70, 17, 108, 66, "props"], [70, 22, 108, 71], [71, 14, 108, 71, "children"], [71, 22, 108, 71], [71, 38, 109, 12], [71, 42, 109, 12, "_jsxDevRuntime"], [71, 56, 109, 12], [71, 57, 109, 12, "jsxDEV"], [71, 63, 109, 12], [71, 65, 109, 13, "Icon"], [71, 69, 109, 17], [72, 16, 109, 17], [72, 19, 109, 22, "iconProps"], [73, 14, 109, 31], [74, 16, 109, 31, "fileName"], [74, 24, 109, 31], [74, 26, 109, 31, "_jsxFileName"], [74, 38, 109, 31], [75, 16, 109, 31, "lineNumber"], [75, 26, 109, 31], [76, 16, 109, 31, "columnNumber"], [76, 28, 109, 31], [77, 14, 109, 31], [77, 21, 109, 34], [77, 22, 109, 35], [77, 24, 110, 13], [77, 31, 110, 20, "children"], [77, 39, 110, 28], [77, 44, 110, 33], [77, 52, 110, 41], [77, 68, 111, 14], [77, 72, 111, 14, "_jsxDevRuntime"], [77, 86, 111, 14], [77, 87, 111, 14, "jsxDEV"], [77, 93, 111, 14], [77, 95, 111, 15, "_reactNative"], [77, 107, 111, 15], [77, 108, 111, 15, "Text"], [77, 112, 111, 19], [78, 16, 111, 20, "style"], [78, 21, 111, 25], [78, 23, 111, 27], [78, 24, 111, 28, "styles"], [78, 30, 111, 34], [78, 31, 111, 35, "text"], [78, 35, 111, 39], [78, 37, 111, 41, "colorStyle"], [78, 47, 111, 51], [78, 48, 111, 53], [79, 16, 111, 54, "selectable"], [79, 26, 111, 64], [79, 28, 111, 66], [79, 33, 111, 72], [80, 16, 111, 72, "children"], [80, 24, 111, 72], [80, 26, 112, 17, "children"], [81, 14, 112, 25], [82, 16, 112, 25, "fileName"], [82, 24, 112, 25], [82, 26, 112, 25, "_jsxFileName"], [82, 38, 112, 25], [83, 16, 112, 25, "lineNumber"], [83, 26, 112, 25], [84, 16, 112, 25, "columnNumber"], [84, 28, 112, 25], [85, 14, 112, 25], [85, 21, 113, 20], [85, 22, 113, 21], [85, 25, 115, 14, "children"], [85, 33, 116, 13], [86, 12, 116, 13], [87, 14, 116, 13, "fileName"], [87, 22, 116, 13], [87, 24, 116, 13, "_jsxFileName"], [87, 36, 116, 13], [88, 14, 116, 13, "lineNumber"], [88, 24, 116, 13], [89, 14, 116, 13, "columnNumber"], [89, 26, 116, 13], [90, 12, 116, 13], [90, 19, 117, 16], [91, 10, 117, 17], [92, 12, 117, 17, "fileName"], [92, 20, 117, 17], [92, 22, 117, 17, "_jsxFileName"], [92, 34, 117, 17], [93, 12, 117, 17, "lineNumber"], [93, 22, 117, 17], [94, 12, 117, 17, "columnNumber"], [94, 24, 117, 17], [95, 10, 117, 17], [95, 17, 118, 28], [95, 18, 118, 29], [96, 8, 120, 4], [97, 6, 120, 5], [98, 4, 120, 5], [98, 6, 68, 34, "PureComponent"], [98, 26, 68, 47], [98, 29, 68, 47, "_IconButton"], [98, 40, 68, 47], [98, 41, 71, 11, "defaultProps"], [98, 53, 71, 23], [98, 56, 71, 26], [99, 6, 72, 6, "backgroundColor"], [99, 21, 72, 21], [99, 23, 72, 23, "IOS7_BLUE"], [99, 32, 72, 32], [100, 6, 73, 6, "borderRadius"], [100, 18, 73, 18], [100, 20, 73, 20], [100, 21, 73, 21], [101, 6, 74, 6, "color"], [101, 11, 74, 11], [101, 13, 74, 13], [101, 20, 74, 20], [102, 6, 75, 6, "size"], [102, 10, 75, 10], [102, 12, 75, 12], [103, 4, 76, 4], [103, 5, 76, 5], [103, 7, 76, 5, "_IconButton"], [103, 18, 76, 5], [104, 2, 122, 0], [105, 0, 122, 1], [105, 3]], "functionMap": {"names": ["<global>", "createIconButtonComponent", "IconButton", "IconButton#render"], "mappings": "AAA;eCkE;SCC;ICU;KD0C;GDC;CDC"}}, "type": "js/module"}]}