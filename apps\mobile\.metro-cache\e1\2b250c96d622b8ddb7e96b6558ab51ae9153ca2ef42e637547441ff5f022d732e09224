{"dependencies": [{"name": "../Utilities/PolyfillFunctions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 25}, "end": {"line": 13, "column": 66}}], "key": "HYclPKQCLeyfRj4pG+IgrzgyEZ8=", "exportNames": ["*"]}}, {"name": "../Network/XMLHttpRequest", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 23, "column": 8}, "end": {"line": 23, "column": 44}}], "key": "qguKgdItzKoGXllQYf2Bx1WNEaQ=", "exportNames": ["*"]}}, {"name": "../Network/FormData", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 25, "column": 33}, "end": {"line": 25, "column": 63}}], "key": "MmSSPDOtR5szb+lznOBaFLDrpJk=", "exportNames": ["*"]}}, {"name": "../Network/fetch", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 27, "column": 30}, "end": {"line": 27, "column": 57}}, {"start": {"line": 28, "column": 32}, "end": {"line": 28, "column": 59}}, {"start": {"line": 29, "column": 32}, "end": {"line": 29, "column": 59}}, {"start": {"line": 30, "column": 33}, "end": {"line": 30, "column": 60}}], "key": "1bUFTuzS+ntgJy2/zXqu4/G7YEA=", "exportNames": ["*"]}}, {"name": "../WebSocket/WebSocket", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 31, "column": 34}, "end": {"line": 31, "column": 67}}], "key": "YzL+ui6xotWaSVWZqsgWhTyGQQQ=", "exportNames": ["*"]}}, {"name": "../Blob/Blob", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 32, "column": 29}, "end": {"line": 32, "column": 52}}], "key": "CnOreM5x+el0dt4mgJPb+JN2GZg=", "exportNames": ["*"]}}, {"name": "../Blob/File", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 33, "column": 29}, "end": {"line": 33, "column": 52}}], "key": "tvknEwop+8VRuH6BvZys2nS95hI=", "exportNames": ["*"]}}, {"name": "../Blob/FileReader", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 34, "column": 35}, "end": {"line": 34, "column": 64}}], "key": "2xGTnldACl1WSb68fgSNTiszFRA=", "exportNames": ["*"]}}, {"name": "../Blob/URL", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 35, "column": 28}, "end": {"line": 35, "column": 50}}, {"start": {"line": 36, "column": 40}, "end": {"line": 36, "column": 62}}], "key": "kOsWiythE+sKE0IzthwlHA5jdME=", "exportNames": ["*"]}}, {"name": "abort-controller/dist/abort-controller", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": 57}}, {"start": {"line": 43, "column": 8}, "end": {"line": 43, "column": 57}}], "key": "Qvdk+dBOR9ppe2zaHOp7jShqUIw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _require = require(_dependencyMap[0], \"../Utilities/PolyfillFunctions\"),\n    polyfillGlobal = _require.polyfillGlobal;\n  polyfillGlobal('XMLHttpRequest', () => require(_dependencyMap[1], \"../Network/XMLHttpRequest\").default);\n  polyfillGlobal('FormData', () => require(_dependencyMap[2], \"../Network/FormData\").default);\n  polyfillGlobal('fetch', () => require(_dependencyMap[3], \"../Network/fetch\").fetch);\n  polyfillGlobal('Headers', () => require(_dependencyMap[3], \"../Network/fetch\").Headers);\n  polyfillGlobal('Request', () => require(_dependencyMap[3], \"../Network/fetch\").Request);\n  polyfillGlobal('Response', () => require(_dependencyMap[3], \"../Network/fetch\").Response);\n  polyfillGlobal('WebSocket', () => require(_dependencyMap[4], \"../WebSocket/WebSocket\").default);\n  polyfillGlobal('Blob', () => require(_dependencyMap[5], \"../Blob/Blob\").default);\n  polyfillGlobal('File', () => require(_dependencyMap[6], \"../Blob/File\").default);\n  polyfillGlobal('FileReader', () => require(_dependencyMap[7], \"../Blob/FileReader\").default);\n  polyfillGlobal('URL', () => require(_dependencyMap[8], \"../Blob/URL\").URL);\n  polyfillGlobal('URLSearchParams', () => require(_dependencyMap[8], \"../Blob/URL\").URLSearchParams);\n  polyfillGlobal('AbortController', () => require(_dependencyMap[9], \"abort-controller/dist/abort-controller\").AbortController);\n  polyfillGlobal('AbortSignal', () => require(_dependencyMap[9], \"abort-controller/dist/abort-controller\").AbortSignal);\n});", "lineCount": 20, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 13, 0], [4, 6, 13, 0, "_require"], [4, 14, 13, 0], [4, 17, 13, 25, "require"], [4, 24, 13, 32], [4, 25, 13, 32, "_dependencyMap"], [4, 39, 13, 32], [4, 76, 13, 65], [4, 77, 13, 66], [5, 4, 13, 7, "polyfillGlobal"], [5, 18, 13, 21], [5, 21, 13, 21, "_require"], [5, 29, 13, 21], [5, 30, 13, 7, "polyfillGlobal"], [5, 44, 13, 21], [6, 2, 21, 0, "polyfillGlobal"], [6, 16, 21, 14], [6, 17, 22, 2], [6, 33, 22, 18], [6, 35, 23, 2], [6, 41, 23, 8, "require"], [6, 48, 23, 15], [6, 49, 23, 15, "_dependencyMap"], [6, 63, 23, 15], [6, 95, 23, 43], [6, 96, 23, 44], [6, 97, 23, 45, "default"], [6, 104, 24, 0], [6, 105, 24, 1], [7, 2, 25, 0, "polyfillGlobal"], [7, 16, 25, 14], [7, 17, 25, 15], [7, 27, 25, 25], [7, 29, 25, 27], [7, 35, 25, 33, "require"], [7, 42, 25, 40], [7, 43, 25, 40, "_dependencyMap"], [7, 57, 25, 40], [7, 83, 25, 62], [7, 84, 25, 63], [7, 85, 25, 64, "default"], [7, 92, 25, 71], [7, 93, 25, 72], [8, 2, 27, 0, "polyfillGlobal"], [8, 16, 27, 14], [8, 17, 27, 15], [8, 24, 27, 22], [8, 26, 27, 24], [8, 32, 27, 30, "require"], [8, 39, 27, 37], [8, 40, 27, 37, "_dependencyMap"], [8, 54, 27, 37], [8, 77, 27, 56], [8, 78, 27, 57], [8, 79, 27, 58, "fetch"], [8, 84, 27, 63], [8, 85, 27, 64], [9, 2, 28, 0, "polyfillGlobal"], [9, 16, 28, 14], [9, 17, 28, 15], [9, 26, 28, 24], [9, 28, 28, 26], [9, 34, 28, 32, "require"], [9, 41, 28, 39], [9, 42, 28, 39, "_dependencyMap"], [9, 56, 28, 39], [9, 79, 28, 58], [9, 80, 28, 59], [9, 81, 28, 60, "Headers"], [9, 88, 28, 67], [9, 89, 28, 68], [10, 2, 29, 0, "polyfillGlobal"], [10, 16, 29, 14], [10, 17, 29, 15], [10, 26, 29, 24], [10, 28, 29, 26], [10, 34, 29, 32, "require"], [10, 41, 29, 39], [10, 42, 29, 39, "_dependencyMap"], [10, 56, 29, 39], [10, 79, 29, 58], [10, 80, 29, 59], [10, 81, 29, 60, "Request"], [10, 88, 29, 67], [10, 89, 29, 68], [11, 2, 30, 0, "polyfillGlobal"], [11, 16, 30, 14], [11, 17, 30, 15], [11, 27, 30, 25], [11, 29, 30, 27], [11, 35, 30, 33, "require"], [11, 42, 30, 40], [11, 43, 30, 40, "_dependencyMap"], [11, 57, 30, 40], [11, 80, 30, 59], [11, 81, 30, 60], [11, 82, 30, 61, "Response"], [11, 90, 30, 69], [11, 91, 30, 70], [12, 2, 31, 0, "polyfillGlobal"], [12, 16, 31, 14], [12, 17, 31, 15], [12, 28, 31, 26], [12, 30, 31, 28], [12, 36, 31, 34, "require"], [12, 43, 31, 41], [12, 44, 31, 41, "_dependencyMap"], [12, 58, 31, 41], [12, 87, 31, 66], [12, 88, 31, 67], [12, 89, 31, 68, "default"], [12, 96, 31, 75], [12, 97, 31, 76], [13, 2, 32, 0, "polyfillGlobal"], [13, 16, 32, 14], [13, 17, 32, 15], [13, 23, 32, 21], [13, 25, 32, 23], [13, 31, 32, 29, "require"], [13, 38, 32, 36], [13, 39, 32, 36, "_dependencyMap"], [13, 53, 32, 36], [13, 72, 32, 51], [13, 73, 32, 52], [13, 74, 32, 53, "default"], [13, 81, 32, 60], [13, 82, 32, 61], [14, 2, 33, 0, "polyfillGlobal"], [14, 16, 33, 14], [14, 17, 33, 15], [14, 23, 33, 21], [14, 25, 33, 23], [14, 31, 33, 29, "require"], [14, 38, 33, 36], [14, 39, 33, 36, "_dependencyMap"], [14, 53, 33, 36], [14, 72, 33, 51], [14, 73, 33, 52], [14, 74, 33, 53, "default"], [14, 81, 33, 60], [14, 82, 33, 61], [15, 2, 34, 0, "polyfillGlobal"], [15, 16, 34, 14], [15, 17, 34, 15], [15, 29, 34, 27], [15, 31, 34, 29], [15, 37, 34, 35, "require"], [15, 44, 34, 42], [15, 45, 34, 42, "_dependencyMap"], [15, 59, 34, 42], [15, 84, 34, 63], [15, 85, 34, 64], [15, 86, 34, 65, "default"], [15, 93, 34, 72], [15, 94, 34, 73], [16, 2, 35, 0, "polyfillGlobal"], [16, 16, 35, 14], [16, 17, 35, 15], [16, 22, 35, 20], [16, 24, 35, 22], [16, 30, 35, 28, "require"], [16, 37, 35, 35], [16, 38, 35, 35, "_dependencyMap"], [16, 52, 35, 35], [16, 70, 35, 49], [16, 71, 35, 50], [16, 72, 35, 51, "URL"], [16, 75, 35, 54], [16, 76, 35, 55], [17, 2, 36, 0, "polyfillGlobal"], [17, 16, 36, 14], [17, 17, 36, 15], [17, 34, 36, 32], [17, 36, 36, 34], [17, 42, 36, 40, "require"], [17, 49, 36, 47], [17, 50, 36, 47, "_dependencyMap"], [17, 64, 36, 47], [17, 82, 36, 61], [17, 83, 36, 62], [17, 84, 36, 63, "URLSearchParams"], [17, 99, 36, 78], [17, 100, 36, 79], [18, 2, 37, 0, "polyfillGlobal"], [18, 16, 37, 14], [18, 17, 38, 2], [18, 34, 38, 19], [18, 36, 39, 2], [18, 42, 39, 8, "require"], [18, 49, 39, 15], [18, 50, 39, 15, "_dependencyMap"], [18, 64, 39, 15], [18, 109, 39, 56], [18, 110, 39, 57], [18, 111, 39, 58, "AbortController"], [18, 126, 40, 0], [18, 127, 40, 1], [19, 2, 41, 0, "polyfillGlobal"], [19, 16, 41, 14], [19, 17, 42, 2], [19, 30, 42, 15], [19, 32, 43, 2], [19, 38, 43, 8, "require"], [19, 45, 43, 15], [19, 46, 43, 15, "_dependencyMap"], [19, 60, 43, 15], [19, 105, 43, 56], [19, 106, 43, 57], [19, 107, 43, 58, "AbortSignal"], [19, 118, 44, 0], [19, 119, 44, 1], [20, 0, 44, 2], [20, 3]], "functionMap": {"names": ["<global>", "polyfillGlobal$argument_1"], "mappings": "AAA;ECsB,kDD;2BCE,4CD;wBCE,uCD;0BCC,yCD;0BCC,yCD;2BCC,0CD;4BCC,+CD;uBCC,qCD;uBCC,qCD;6BCC,2CD;sBCC,gCD;kCCC,4CD;ECG,uED;ECI,mED"}}, "type": "js/module"}]}