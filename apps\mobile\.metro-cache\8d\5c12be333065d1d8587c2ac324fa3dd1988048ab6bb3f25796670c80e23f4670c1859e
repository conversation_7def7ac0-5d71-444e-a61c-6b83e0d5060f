{"dependencies": [{"name": "./GestureHandlerNative.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 42, "index": 57}}], "key": "Kc9EC95O7nptsPQATey1UnbXV8E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _GestureHandlerNative = require(_dependencyMap[0], \"./GestureHandlerNative.js\");\n  Object.keys(_GestureHandlerNative).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _GestureHandlerNative[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _GestureHandlerNative[key];\n      }\n    });\n  });\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 3, 0], [7, 6, 3, 0, "_GestureHandlerNative"], [7, 27, 3, 0], [7, 30, 3, 0, "require"], [7, 37, 3, 0], [7, 38, 3, 0, "_dependencyMap"], [7, 52, 3, 0], [8, 2, 3, 0, "Object"], [8, 8, 3, 0], [8, 9, 3, 0, "keys"], [8, 13, 3, 0], [8, 14, 3, 0, "_GestureHandlerNative"], [8, 35, 3, 0], [8, 37, 3, 0, "for<PERSON>ach"], [8, 44, 3, 0], [8, 55, 3, 0, "key"], [8, 58, 3, 0], [9, 4, 3, 0], [9, 8, 3, 0, "key"], [9, 11, 3, 0], [9, 29, 3, 0, "key"], [9, 32, 3, 0], [10, 4, 3, 0], [10, 8, 3, 0, "key"], [10, 11, 3, 0], [10, 15, 3, 0, "exports"], [10, 22, 3, 0], [10, 26, 3, 0, "exports"], [10, 33, 3, 0], [10, 34, 3, 0, "key"], [10, 37, 3, 0], [10, 43, 3, 0, "_GestureHandlerNative"], [10, 64, 3, 0], [10, 65, 3, 0, "key"], [10, 68, 3, 0], [11, 4, 3, 0, "Object"], [11, 10, 3, 0], [11, 11, 3, 0, "defineProperty"], [11, 25, 3, 0], [11, 26, 3, 0, "exports"], [11, 33, 3, 0], [11, 35, 3, 0, "key"], [11, 38, 3, 0], [12, 6, 3, 0, "enumerable"], [12, 16, 3, 0], [13, 6, 3, 0, "get"], [13, 9, 3, 0], [13, 20, 3, 0, "get"], [13, 21, 3, 0], [14, 8, 3, 0], [14, 15, 3, 0, "_GestureHandlerNative"], [14, 36, 3, 0], [14, 37, 3, 0, "key"], [14, 40, 3, 0], [15, 6, 3, 0], [16, 4, 3, 0], [17, 2, 3, 0], [18, 0, 3, 42], [18, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}