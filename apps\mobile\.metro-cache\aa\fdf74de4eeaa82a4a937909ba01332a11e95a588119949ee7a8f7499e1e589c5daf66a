{"dependencies": [{"name": "../collections", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 19, "index": 160}, "end": {"line": 7, "column": 44, "index": 185}}], "key": "EATVSGofyoN+/7kTXlSYWkNRByg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, '__esModule', {\n    value: true\n  });\n  exports.test = exports.serialize = exports.default = void 0;\n  var _collections = require(_dependencyMap[0], \"../collections\");\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  var SPACE = ' ';\n  var OBJECT_NAMES = ['DOMStringMap', 'NamedNodeMap'];\n  var ARRAY_REGEXP = /^(HTML\\w*Collection|NodeList)$/;\n  var testName = name => OBJECT_NAMES.indexOf(name) !== -1 || ARRAY_REGEXP.test(name);\n  var test = val => val && val.constructor && !!val.constructor.name && testName(val.constructor.name);\n  exports.test = test;\n  var isNamedNodeMap = collection => collection.constructor.name === 'NamedNodeMap';\n  var serialize = (collection, config, indentation, depth, refs, printer) => {\n    var name = collection.constructor.name;\n    if (++depth > config.maxDepth) {\n      return `[${name}]`;\n    }\n    return (config.min ? '' : name + SPACE) + (OBJECT_NAMES.indexOf(name) !== -1 ? `{${(0, _collections.printObjectProperties)(isNamedNodeMap(collection) ? Array.from(collection).reduce((props, attribute) => {\n      props[attribute.name] = attribute.value;\n      return props;\n    }, {}) : {\n      ...collection\n    }, config, indentation, depth, refs, printer)}}` : `[${(0, _collections.printListItems)(Array.from(collection), config, indentation, depth, refs, printer)}]`);\n  };\n  exports.serialize = serialize;\n  var plugin = {\n    serialize,\n    test\n  };\n  var _default = plugin;\n  exports.default = _default;\n});", "lineCount": 42, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8, "test"], [7, 14, 6, 12], [7, 17, 6, 15, "exports"], [7, 24, 6, 22], [7, 25, 6, 23, "serialize"], [7, 34, 6, 32], [7, 37, 6, 35, "exports"], [7, 44, 6, 42], [7, 45, 6, 43, "default"], [7, 52, 6, 50], [7, 55, 6, 53], [7, 60, 6, 58], [7, 61, 6, 59], [8, 2, 7, 0], [8, 6, 7, 4, "_collections"], [8, 18, 7, 16], [8, 21, 7, 19, "require"], [8, 28, 7, 26], [8, 29, 7, 26, "_dependencyMap"], [8, 43, 7, 26], [8, 64, 7, 43], [8, 65, 7, 44], [9, 2, 8, 0], [10, 0, 9, 0], [11, 0, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [16, 2, 15, 0], [16, 6, 15, 6, "SPACE"], [16, 11, 15, 11], [16, 14, 15, 14], [16, 17, 15, 17], [17, 2, 16, 0], [17, 6, 16, 6, "OBJECT_NAMES"], [17, 18, 16, 18], [17, 21, 16, 21], [17, 22, 16, 22], [17, 36, 16, 36], [17, 38, 16, 38], [17, 52, 16, 52], [17, 53, 16, 53], [18, 2, 17, 0], [18, 6, 17, 6, "ARRAY_REGEXP"], [18, 18, 17, 18], [18, 21, 17, 21], [18, 53, 17, 53], [19, 2, 18, 0], [19, 6, 18, 6, "testName"], [19, 14, 18, 14], [19, 17, 18, 17, "name"], [19, 21, 18, 21], [19, 25, 19, 2, "OBJECT_NAMES"], [19, 37, 19, 14], [19, 38, 19, 15, "indexOf"], [19, 45, 19, 22], [19, 46, 19, 23, "name"], [19, 50, 19, 27], [19, 51, 19, 28], [19, 56, 19, 33], [19, 57, 19, 34], [19, 58, 19, 35], [19, 62, 19, 39, "ARRAY_REGEXP"], [19, 74, 19, 51], [19, 75, 19, 52, "test"], [19, 79, 19, 56], [19, 80, 19, 57, "name"], [19, 84, 19, 61], [19, 85, 19, 62], [20, 2, 20, 0], [20, 6, 20, 6, "test"], [20, 10, 20, 10], [20, 13, 20, 13, "val"], [20, 16, 20, 16], [20, 20, 21, 2, "val"], [20, 23, 21, 5], [20, 27, 22, 2, "val"], [20, 30, 22, 5], [20, 31, 22, 6, "constructor"], [20, 42, 22, 17], [20, 46, 23, 2], [20, 47, 23, 3], [20, 48, 23, 4, "val"], [20, 51, 23, 7], [20, 52, 23, 8, "constructor"], [20, 63, 23, 19], [20, 64, 23, 20, "name"], [20, 68, 23, 24], [20, 72, 24, 2, "testName"], [20, 80, 24, 10], [20, 81, 24, 11, "val"], [20, 84, 24, 14], [20, 85, 24, 15, "constructor"], [20, 96, 24, 26], [20, 97, 24, 27, "name"], [20, 101, 24, 31], [20, 102, 24, 32], [21, 2, 25, 0, "exports"], [21, 9, 25, 7], [21, 10, 25, 8, "test"], [21, 14, 25, 12], [21, 17, 25, 15, "test"], [21, 21, 25, 19], [22, 2, 26, 0], [22, 6, 26, 6, "isNamedNodeMap"], [22, 20, 26, 20], [22, 23, 26, 23, "collection"], [22, 33, 26, 33], [22, 37, 27, 2, "collection"], [22, 47, 27, 12], [22, 48, 27, 13, "constructor"], [22, 59, 27, 24], [22, 60, 27, 25, "name"], [22, 64, 27, 29], [22, 69, 27, 34], [22, 83, 27, 48], [23, 2, 28, 0], [23, 6, 28, 6, "serialize"], [23, 15, 28, 15], [23, 18, 28, 18, "serialize"], [23, 19, 28, 19, "collection"], [23, 29, 28, 29], [23, 31, 28, 31, "config"], [23, 37, 28, 37], [23, 39, 28, 39, "indentation"], [23, 50, 28, 50], [23, 52, 28, 52, "depth"], [23, 57, 28, 57], [23, 59, 28, 59, "refs"], [23, 63, 28, 63], [23, 65, 28, 65, "printer"], [23, 72, 28, 72], [23, 77, 28, 77], [24, 4, 29, 2], [24, 8, 29, 8, "name"], [24, 12, 29, 12], [24, 15, 29, 15, "collection"], [24, 25, 29, 25], [24, 26, 29, 26, "constructor"], [24, 37, 29, 37], [24, 38, 29, 38, "name"], [24, 42, 29, 42], [25, 4, 30, 2], [25, 8, 30, 6], [25, 10, 30, 8, "depth"], [25, 15, 30, 13], [25, 18, 30, 16, "config"], [25, 24, 30, 22], [25, 25, 30, 23, "max<PERSON><PERSON><PERSON>"], [25, 33, 30, 31], [25, 35, 30, 33], [26, 6, 31, 4], [26, 13, 31, 11], [26, 17, 31, 15, "name"], [26, 21, 31, 19], [26, 24, 31, 22], [27, 4, 32, 2], [28, 4, 33, 2], [28, 11, 34, 4], [28, 12, 34, 5, "config"], [28, 18, 34, 11], [28, 19, 34, 12, "min"], [28, 22, 34, 15], [28, 25, 34, 18], [28, 27, 34, 20], [28, 30, 34, 23, "name"], [28, 34, 34, 27], [28, 37, 34, 30, "SPACE"], [28, 42, 34, 35], [28, 47, 35, 5, "OBJECT_NAMES"], [28, 59, 35, 17], [28, 60, 35, 18, "indexOf"], [28, 67, 35, 25], [28, 68, 35, 26, "name"], [28, 72, 35, 30], [28, 73, 35, 31], [28, 78, 35, 36], [28, 79, 35, 37], [28, 80, 35, 38], [28, 83, 36, 8], [28, 87, 36, 12], [28, 88, 36, 13], [28, 89, 36, 14], [28, 91, 36, 16, "_collections"], [28, 103, 36, 28], [28, 104, 36, 29, "printObjectProperties"], [28, 125, 36, 50], [28, 127, 37, 10, "isNamedNodeMap"], [28, 141, 37, 24], [28, 142, 37, 25, "collection"], [28, 152, 37, 35], [28, 153, 37, 36], [28, 156, 38, 14, "Array"], [28, 161, 38, 19], [28, 162, 38, 20, "from"], [28, 166, 38, 24], [28, 167, 38, 25, "collection"], [28, 177, 38, 35], [28, 178, 38, 36], [28, 179, 38, 37, "reduce"], [28, 185, 38, 43], [28, 186, 38, 44], [28, 187, 38, 45, "props"], [28, 192, 38, 50], [28, 194, 38, 52, "attribute"], [28, 203, 38, 61], [28, 208, 38, 66], [29, 6, 39, 16, "props"], [29, 11, 39, 21], [29, 12, 39, 22, "attribute"], [29, 21, 39, 31], [29, 22, 39, 32, "name"], [29, 26, 39, 36], [29, 27, 39, 37], [29, 30, 39, 40, "attribute"], [29, 39, 39, 49], [29, 40, 39, 50, "value"], [29, 45, 39, 55], [30, 6, 40, 16], [30, 13, 40, 23, "props"], [30, 18, 40, 28], [31, 4, 41, 14], [31, 5, 41, 15], [31, 7, 41, 17], [31, 8, 41, 18], [31, 9, 41, 19], [31, 10, 41, 20], [31, 13, 42, 14], [32, 6, 43, 16], [32, 9, 43, 19, "collection"], [33, 4, 44, 14], [33, 5, 44, 15], [33, 7, 45, 10, "config"], [33, 13, 45, 16], [33, 15, 46, 10, "indentation"], [33, 26, 46, 21], [33, 28, 47, 10, "depth"], [33, 33, 47, 15], [33, 35, 48, 10, "refs"], [33, 39, 48, 14], [33, 41, 49, 10, "printer"], [33, 48, 50, 8], [33, 49, 50, 9], [33, 52, 50, 12], [33, 55, 51, 8], [33, 59, 51, 12], [33, 60, 51, 13], [33, 61, 51, 14], [33, 63, 51, 16, "_collections"], [33, 75, 51, 28], [33, 76, 51, 29, "printListItems"], [33, 90, 51, 43], [33, 92, 52, 10, "Array"], [33, 97, 52, 15], [33, 98, 52, 16, "from"], [33, 102, 52, 20], [33, 103, 52, 21, "collection"], [33, 113, 52, 31], [33, 114, 52, 32], [33, 116, 53, 10, "config"], [33, 122, 53, 16], [33, 124, 54, 10, "indentation"], [33, 135, 54, 21], [33, 137, 55, 10, "depth"], [33, 142, 55, 15], [33, 144, 56, 10, "refs"], [33, 148, 56, 14], [33, 150, 57, 10, "printer"], [33, 157, 58, 8], [33, 158, 58, 9], [33, 161, 58, 12], [33, 162, 58, 13], [34, 2, 60, 0], [34, 3, 60, 1], [35, 2, 61, 0, "exports"], [35, 9, 61, 7], [35, 10, 61, 8, "serialize"], [35, 19, 61, 17], [35, 22, 61, 20, "serialize"], [35, 31, 61, 29], [36, 2, 62, 0], [36, 6, 62, 6, "plugin"], [36, 12, 62, 12], [36, 15, 62, 15], [37, 4, 63, 2, "serialize"], [37, 13, 63, 11], [38, 4, 64, 2, "test"], [39, 2, 65, 0], [39, 3, 65, 1], [40, 2, 66, 0], [40, 6, 66, 4, "_default"], [40, 14, 66, 12], [40, 17, 66, 15, "plugin"], [40, 23, 66, 21], [41, 2, 67, 0, "exports"], [41, 9, 67, 7], [41, 10, 67, 8, "default"], [41, 17, 67, 15], [41, 20, 67, 18, "_default"], [41, 28, 67, 26], [42, 0, 67, 27], [42, 3]], "functionMap": {"names": ["<global>", "testName", "test", "isNamedNodeMap", "serialize", "Array.from.reduce$argument_0"], "mappings": "AAA;iBCiB;8DDC;aEC;gCFI;uBGE;gDHC;kBIC;4CCU;eDG;CJmB"}}, "type": "js/module"}]}