{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 46}, "end": {"line": 2, "column": 85, "index": 131}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./ensure-native-module-available", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 133}, "end": {"line": 4, "column": 75, "index": 208}}], "key": "6W6A5XnIcFSn+7HndgYv9PL1F1w=", "exportNames": ["*"]}}, {"name": "./create-icon-source-cache", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 209}, "end": {"line": 5, "column": 63, "index": 272}}], "key": "BjsgdD/s5/oZqTiDGr60rkQTiUk=", "exportNames": ["*"]}}, {"name": "./icon-button", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 273}, "end": {"line": 6, "column": 54, "index": 327}}], "key": "zWPD0YJDk0oh7bz2YfMkM4yULEg=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.NativeIconAPI = exports.DEFAULT_ICON_SIZE = exports.DEFAULT_ICON_COLOR = void 0;\n  exports.default = createIconSet;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[7], \"@babel/runtime/helpers/inherits\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[8], \"react\"));\n  var _reactNative = require(_dependencyMap[9], \"react-native\");\n  var _ensureNativeModuleAvailable = _interopRequireDefault(require(_dependencyMap[10], \"./ensure-native-module-available\"));\n  var _createIconSourceCache = _interopRequireDefault(require(_dependencyMap[11], \"./create-icon-source-cache\"));\n  var _iconButton = _interopRequireDefault(require(_dependencyMap[12], \"./icon-button\"));\n  var _jsxDevRuntime = require(_dependencyMap[13], \"react/jsx-dev-runtime\");\n  var _excluded = [\"name\", \"size\", \"color\", \"style\", \"children\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\@expo\\\\vector-icons\\\\build\\\\vendor\\\\react-native-vector-icons\\\\lib\\\\create-icon-set.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var NativeIconAPI = exports.NativeIconAPI = _reactNative.NativeModules.RNVectorIconsManager || _reactNative.NativeModules.RNVectorIconsModule;\n  var DEFAULT_ICON_SIZE = exports.DEFAULT_ICON_SIZE = 12;\n  var DEFAULT_ICON_COLOR = exports.DEFAULT_ICON_COLOR = 'black';\n  function createIconSet(glyphMap, fontFamily, fontFile, fontStyle) {\n    // Android doesn't care about actual fontFamily name, it will only look in fonts folder.\n    var fontBasename = fontFile ? fontFile.replace(/\\.(otf|ttf)$/, '') : fontFamily;\n    var fontReference = _reactNative.Platform.select({\n      windows: `/Assets/${fontFile}#${fontFamily}`,\n      android: fontBasename,\n      web: fontBasename,\n      default: fontFamily\n    });\n    var Icon = /*#__PURE__*/function (_PureComponent) {\n      function Icon() {\n        var _this;\n        (0, _classCallCheck2.default)(this, Icon);\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        _this = _callSuper(this, Icon, [...args]);\n        _this.root = null;\n        return _this;\n      }\n      (0, _inherits2.default)(Icon, _PureComponent);\n      return (0, _createClass2.default)(Icon, [{\n        key: \"render\",\n        value: function render() {\n          var _this$props = this.props,\n            name = _this$props.name,\n            size = _this$props.size,\n            color = _this$props.color,\n            style = _this$props.style,\n            children = _this$props.children,\n            props = (0, _objectWithoutProperties2.default)(_this$props, _excluded);\n          var glyph = name ? glyphMap[name] || '?' : '';\n          if (typeof glyph === 'number') {\n            glyph = String.fromCodePoint(glyph);\n          }\n          var styleDefaults = {\n            fontSize: size,\n            color\n          };\n          var styleOverrides = {\n            fontFamily: fontReference,\n            fontWeight: 'normal',\n            fontStyle: 'normal'\n          };\n          props.style = [styleDefaults, style, styleOverrides, fontStyle || {}];\n          return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            selectable: false,\n            ...props,\n            children: [glyph, children]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 9\n          }, this);\n        }\n      }]);\n    }(_react.PureComponent);\n    Icon.defaultProps = {\n      size: DEFAULT_ICON_SIZE,\n      allowFontScaling: false\n    };\n    var imageSourceCache = (0, _createIconSourceCache.default)();\n    function resolveGlyph(name) {\n      var glyph = glyphMap[name] || '?';\n      if (typeof glyph === 'number') {\n        return String.fromCodePoint(glyph);\n      }\n      return glyph;\n    }\n    function getImageSourceSync(name) {\n      var size = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DEFAULT_ICON_SIZE;\n      var color = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : DEFAULT_ICON_COLOR;\n      (0, _ensureNativeModuleAvailable.default)();\n      var glyph = resolveGlyph(name);\n      var processedColor = (0, _reactNative.processColor)(color);\n      var cacheKey = `${glyph}:${size}:${processedColor}`;\n      if (imageSourceCache.has(cacheKey)) {\n        return imageSourceCache.get(cacheKey);\n      }\n      try {\n        var imagePath = NativeIconAPI.getImageForFontSync(fontReference, glyph, size, processedColor);\n        var value = {\n          uri: imagePath,\n          scale: _reactNative.PixelRatio.get()\n        };\n        imageSourceCache.setValue(cacheKey, value);\n        return value;\n      } catch (error) {\n        imageSourceCache.setError(cacheKey, error);\n        throw error;\n      }\n    }\n    function getImageSource(_x) {\n      return _getImageSource.apply(this, arguments);\n    }\n    function _getImageSource() {\n      _getImageSource = (0, _asyncToGenerator2.default)(function* (name) {\n        var size = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DEFAULT_ICON_SIZE;\n        var color = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : DEFAULT_ICON_COLOR;\n        (0, _ensureNativeModuleAvailable.default)();\n        var glyph = resolveGlyph(name);\n        var processedColor = (0, _reactNative.processColor)(color);\n        var cacheKey = `${glyph}:${size}:${processedColor}`;\n        if (imageSourceCache.has(cacheKey)) {\n          return imageSourceCache.get(cacheKey);\n        }\n        try {\n          var imagePath = yield NativeIconAPI.getImageForFont(fontReference, glyph, size, processedColor);\n          var value = {\n            uri: imagePath,\n            scale: _reactNative.PixelRatio.get()\n          };\n          imageSourceCache.setValue(cacheKey, value);\n          return value;\n        } catch (error) {\n          imageSourceCache.setError(cacheKey, error);\n          throw error;\n        }\n      });\n      return _getImageSource.apply(this, arguments);\n    }\n    function loadFont() {\n      return _loadFont.apply(this, arguments);\n    }\n    function _loadFont() {\n      _loadFont = (0, _asyncToGenerator2.default)(function* () {\n        var file = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : fontFile;\n        if (_reactNative.Platform.OS === 'ios') {\n          (0, _ensureNativeModuleAvailable.default)();\n          if (!file) {\n            throw new Error('Unable to load font, because no file was specified. ');\n          }\n          yield NativeIconAPI.loadFontWithFileName(...file.split('.'));\n        }\n      });\n      return _loadFont.apply(this, arguments);\n    }\n    function hasIcon(name) {\n      return Object.prototype.hasOwnProperty.call(glyphMap, name);\n    }\n    function getRawGlyphMap() {\n      return glyphMap;\n    }\n    function getFontFamily() {\n      return fontReference;\n    }\n    Icon.Button = (0, _iconButton.default)(Icon);\n    Icon.getImageSource = getImageSource;\n    Icon.getImageSourceSync = getImageSourceSync;\n    Icon.loadFont = loadFont;\n    Icon.hasIcon = hasIcon;\n    Icon.getRawGlyphMap = getRawGlyphMap;\n    Icon.getFontFamily = getFontFamily;\n    return Icon;\n  }\n});", "lineCount": 184, "map": [[15, 2, 1, 0], [15, 6, 1, 0, "_react"], [15, 12, 1, 0], [15, 15, 1, 0, "_interopRequireWildcard"], [15, 38, 1, 0], [15, 39, 1, 0, "require"], [15, 46, 1, 0], [15, 47, 1, 0, "_dependencyMap"], [15, 61, 1, 0], [16, 2, 2, 0], [16, 6, 2, 0, "_reactNative"], [16, 18, 2, 0], [16, 21, 2, 0, "require"], [16, 28, 2, 0], [16, 29, 2, 0, "_dependencyMap"], [16, 43, 2, 0], [17, 2, 4, 0], [17, 6, 4, 0, "_ensureNativeModuleAvailable"], [17, 34, 4, 0], [17, 37, 4, 0, "_interopRequireDefault"], [17, 59, 4, 0], [17, 60, 4, 0, "require"], [17, 67, 4, 0], [17, 68, 4, 0, "_dependencyMap"], [17, 82, 4, 0], [18, 2, 5, 0], [18, 6, 5, 0, "_createIconSourceCache"], [18, 28, 5, 0], [18, 31, 5, 0, "_interopRequireDefault"], [18, 53, 5, 0], [18, 54, 5, 0, "require"], [18, 61, 5, 0], [18, 62, 5, 0, "_dependencyMap"], [18, 76, 5, 0], [19, 2, 6, 0], [19, 6, 6, 0, "_iconButton"], [19, 17, 6, 0], [19, 20, 6, 0, "_interopRequireDefault"], [19, 42, 6, 0], [19, 43, 6, 0, "require"], [19, 50, 6, 0], [19, 51, 6, 0, "_dependencyMap"], [19, 65, 6, 0], [20, 2, 6, 54], [20, 6, 6, 54, "_jsxDevRuntime"], [20, 20, 6, 54], [20, 23, 6, 54, "require"], [20, 30, 6, 54], [20, 31, 6, 54, "_dependencyMap"], [20, 45, 6, 54], [21, 2, 6, 54], [21, 6, 6, 54, "_excluded"], [21, 15, 6, 54], [22, 2, 6, 54], [22, 6, 6, 54, "_jsxFileName"], [22, 18, 6, 54], [23, 2, 6, 54], [23, 11, 6, 54, "_interopRequireWildcard"], [23, 35, 6, 54, "e"], [23, 36, 6, 54], [23, 38, 6, 54, "t"], [23, 39, 6, 54], [23, 68, 6, 54, "WeakMap"], [23, 75, 6, 54], [23, 81, 6, 54, "r"], [23, 82, 6, 54], [23, 89, 6, 54, "WeakMap"], [23, 96, 6, 54], [23, 100, 6, 54, "n"], [23, 101, 6, 54], [23, 108, 6, 54, "WeakMap"], [23, 115, 6, 54], [23, 127, 6, 54, "_interopRequireWildcard"], [23, 150, 6, 54], [23, 162, 6, 54, "_interopRequireWildcard"], [23, 163, 6, 54, "e"], [23, 164, 6, 54], [23, 166, 6, 54, "t"], [23, 167, 6, 54], [23, 176, 6, 54, "t"], [23, 177, 6, 54], [23, 181, 6, 54, "e"], [23, 182, 6, 54], [23, 186, 6, 54, "e"], [23, 187, 6, 54], [23, 188, 6, 54, "__esModule"], [23, 198, 6, 54], [23, 207, 6, 54, "e"], [23, 208, 6, 54], [23, 214, 6, 54, "o"], [23, 215, 6, 54], [23, 217, 6, 54, "i"], [23, 218, 6, 54], [23, 220, 6, 54, "f"], [23, 221, 6, 54], [23, 226, 6, 54, "__proto__"], [23, 235, 6, 54], [23, 243, 6, 54, "default"], [23, 250, 6, 54], [23, 252, 6, 54, "e"], [23, 253, 6, 54], [23, 270, 6, 54, "e"], [23, 271, 6, 54], [23, 294, 6, 54, "e"], [23, 295, 6, 54], [23, 320, 6, 54, "e"], [23, 321, 6, 54], [23, 330, 6, 54, "f"], [23, 331, 6, 54], [23, 337, 6, 54, "o"], [23, 338, 6, 54], [23, 341, 6, 54, "t"], [23, 342, 6, 54], [23, 345, 6, 54, "n"], [23, 346, 6, 54], [23, 349, 6, 54, "r"], [23, 350, 6, 54], [23, 358, 6, 54, "o"], [23, 359, 6, 54], [23, 360, 6, 54, "has"], [23, 363, 6, 54], [23, 364, 6, 54, "e"], [23, 365, 6, 54], [23, 375, 6, 54, "o"], [23, 376, 6, 54], [23, 377, 6, 54, "get"], [23, 380, 6, 54], [23, 381, 6, 54, "e"], [23, 382, 6, 54], [23, 385, 6, 54, "o"], [23, 386, 6, 54], [23, 387, 6, 54, "set"], [23, 390, 6, 54], [23, 391, 6, 54, "e"], [23, 392, 6, 54], [23, 394, 6, 54, "f"], [23, 395, 6, 54], [23, 409, 6, 54, "_t"], [23, 411, 6, 54], [23, 415, 6, 54, "e"], [23, 416, 6, 54], [23, 432, 6, 54, "_t"], [23, 434, 6, 54], [23, 441, 6, 54, "hasOwnProperty"], [23, 455, 6, 54], [23, 456, 6, 54, "call"], [23, 460, 6, 54], [23, 461, 6, 54, "e"], [23, 462, 6, 54], [23, 464, 6, 54, "_t"], [23, 466, 6, 54], [23, 473, 6, 54, "i"], [23, 474, 6, 54], [23, 478, 6, 54, "o"], [23, 479, 6, 54], [23, 482, 6, 54, "Object"], [23, 488, 6, 54], [23, 489, 6, 54, "defineProperty"], [23, 503, 6, 54], [23, 508, 6, 54, "Object"], [23, 514, 6, 54], [23, 515, 6, 54, "getOwnPropertyDescriptor"], [23, 539, 6, 54], [23, 540, 6, 54, "e"], [23, 541, 6, 54], [23, 543, 6, 54, "_t"], [23, 545, 6, 54], [23, 552, 6, 54, "i"], [23, 553, 6, 54], [23, 554, 6, 54, "get"], [23, 557, 6, 54], [23, 561, 6, 54, "i"], [23, 562, 6, 54], [23, 563, 6, 54, "set"], [23, 566, 6, 54], [23, 570, 6, 54, "o"], [23, 571, 6, 54], [23, 572, 6, 54, "f"], [23, 573, 6, 54], [23, 575, 6, 54, "_t"], [23, 577, 6, 54], [23, 579, 6, 54, "i"], [23, 580, 6, 54], [23, 584, 6, 54, "f"], [23, 585, 6, 54], [23, 586, 6, 54, "_t"], [23, 588, 6, 54], [23, 592, 6, 54, "e"], [23, 593, 6, 54], [23, 594, 6, 54, "_t"], [23, 596, 6, 54], [23, 607, 6, 54, "f"], [23, 608, 6, 54], [23, 613, 6, 54, "e"], [23, 614, 6, 54], [23, 616, 6, 54, "t"], [23, 617, 6, 54], [24, 2, 6, 54], [24, 11, 6, 54, "_callSuper"], [24, 22, 6, 54, "t"], [24, 23, 6, 54], [24, 25, 6, 54, "o"], [24, 26, 6, 54], [24, 28, 6, 54, "e"], [24, 29, 6, 54], [24, 40, 6, 54, "o"], [24, 41, 6, 54], [24, 48, 6, 54, "_getPrototypeOf2"], [24, 64, 6, 54], [24, 65, 6, 54, "default"], [24, 72, 6, 54], [24, 74, 6, 54, "o"], [24, 75, 6, 54], [24, 82, 6, 54, "_possibleConstructorReturn2"], [24, 109, 6, 54], [24, 110, 6, 54, "default"], [24, 117, 6, 54], [24, 119, 6, 54, "t"], [24, 120, 6, 54], [24, 122, 6, 54, "_isNativeReflectConstruct"], [24, 147, 6, 54], [24, 152, 6, 54, "Reflect"], [24, 159, 6, 54], [24, 160, 6, 54, "construct"], [24, 169, 6, 54], [24, 170, 6, 54, "o"], [24, 171, 6, 54], [24, 173, 6, 54, "e"], [24, 174, 6, 54], [24, 186, 6, 54, "_getPrototypeOf2"], [24, 202, 6, 54], [24, 203, 6, 54, "default"], [24, 210, 6, 54], [24, 212, 6, 54, "t"], [24, 213, 6, 54], [24, 215, 6, 54, "constructor"], [24, 226, 6, 54], [24, 230, 6, 54, "o"], [24, 231, 6, 54], [24, 232, 6, 54, "apply"], [24, 237, 6, 54], [24, 238, 6, 54, "t"], [24, 239, 6, 54], [24, 241, 6, 54, "e"], [24, 242, 6, 54], [25, 2, 6, 54], [25, 11, 6, 54, "_isNativeReflectConstruct"], [25, 37, 6, 54], [25, 51, 6, 54, "t"], [25, 52, 6, 54], [25, 56, 6, 54, "Boolean"], [25, 63, 6, 54], [25, 64, 6, 54, "prototype"], [25, 73, 6, 54], [25, 74, 6, 54, "valueOf"], [25, 81, 6, 54], [25, 82, 6, 54, "call"], [25, 86, 6, 54], [25, 87, 6, 54, "Reflect"], [25, 94, 6, 54], [25, 95, 6, 54, "construct"], [25, 104, 6, 54], [25, 105, 6, 54, "Boolean"], [25, 112, 6, 54], [25, 145, 6, 54, "t"], [25, 146, 6, 54], [25, 159, 6, 54, "_isNativeReflectConstruct"], [25, 184, 6, 54], [25, 196, 6, 54, "_isNativeReflectConstruct"], [25, 197, 6, 54], [25, 210, 6, 54, "t"], [25, 211, 6, 54], [26, 2, 8, 7], [26, 6, 8, 13, "NativeIconAPI"], [26, 19, 8, 26], [26, 22, 8, 26, "exports"], [26, 29, 8, 26], [26, 30, 8, 26, "NativeIconAPI"], [26, 43, 8, 26], [26, 46, 9, 2, "NativeModules"], [26, 72, 9, 15], [26, 73, 9, 16, "RNVectorIconsManager"], [26, 93, 9, 36], [26, 97, 9, 40, "NativeModules"], [26, 123, 9, 53], [26, 124, 9, 54, "RNVectorIconsModule"], [26, 143, 9, 73], [27, 2, 11, 7], [27, 6, 11, 13, "DEFAULT_ICON_SIZE"], [27, 23, 11, 30], [27, 26, 11, 30, "exports"], [27, 33, 11, 30], [27, 34, 11, 30, "DEFAULT_ICON_SIZE"], [27, 51, 11, 30], [27, 54, 11, 33], [27, 56, 11, 35], [28, 2, 12, 7], [28, 6, 12, 13, "DEFAULT_ICON_COLOR"], [28, 24, 12, 31], [28, 27, 12, 31, "exports"], [28, 34, 12, 31], [28, 35, 12, 31, "DEFAULT_ICON_COLOR"], [28, 53, 12, 31], [28, 56, 12, 34], [28, 63, 12, 41], [29, 2, 14, 15], [29, 11, 14, 24, "createIconSet"], [29, 24, 14, 37, "createIconSet"], [29, 25, 15, 2, "glyphMap"], [29, 33, 15, 10], [29, 35, 16, 2, "fontFamily"], [29, 45, 16, 12], [29, 47, 17, 2, "fontFile"], [29, 55, 17, 10], [29, 57, 18, 2, "fontStyle"], [29, 66, 18, 11], [29, 68, 19, 2], [30, 4, 20, 2], [31, 4, 21, 2], [31, 8, 21, 8, "fontBasename"], [31, 20, 21, 20], [31, 23, 21, 23, "fontFile"], [31, 31, 21, 31], [31, 34, 22, 6, "fontFile"], [31, 42, 22, 14], [31, 43, 22, 15, "replace"], [31, 50, 22, 22], [31, 51, 22, 23], [31, 65, 22, 37], [31, 67, 22, 39], [31, 69, 22, 41], [31, 70, 22, 42], [31, 73, 23, 6, "fontFamily"], [31, 83, 23, 16], [32, 4, 25, 2], [32, 8, 25, 8, "fontReference"], [32, 21, 25, 21], [32, 24, 25, 24, "Platform"], [32, 45, 25, 32], [32, 46, 25, 33, "select"], [32, 52, 25, 39], [32, 53, 25, 40], [33, 6, 26, 4, "windows"], [33, 13, 26, 11], [33, 15, 26, 13], [33, 26, 26, 24, "fontFile"], [33, 34, 26, 32], [33, 38, 26, 36, "fontFamily"], [33, 48, 26, 46], [33, 50, 26, 48], [34, 6, 27, 4, "android"], [34, 13, 27, 11], [34, 15, 27, 13, "fontBasename"], [34, 27, 27, 25], [35, 6, 28, 4, "web"], [35, 9, 28, 7], [35, 11, 28, 9, "fontBasename"], [35, 23, 28, 21], [36, 6, 29, 4, "default"], [36, 13, 29, 11], [36, 15, 29, 13, "fontFamily"], [37, 4, 30, 2], [37, 5, 30, 3], [37, 6, 30, 4], [38, 4, 30, 5], [38, 8, 32, 8, "Icon"], [38, 12, 32, 12], [38, 38, 32, 12, "_PureComponent"], [38, 52, 32, 12], [39, 6, 32, 12], [39, 15, 32, 12, "Icon"], [39, 20, 32, 12], [40, 8, 32, 12], [40, 12, 32, 12, "_this"], [40, 17, 32, 12], [41, 8, 32, 12], [41, 12, 32, 12, "_classCallCheck2"], [41, 28, 32, 12], [41, 29, 32, 12, "default"], [41, 36, 32, 12], [41, 44, 32, 12, "Icon"], [41, 48, 32, 12], [42, 8, 32, 12], [42, 17, 32, 12, "_len"], [42, 21, 32, 12], [42, 24, 32, 12, "arguments"], [42, 33, 32, 12], [42, 34, 32, 12, "length"], [42, 40, 32, 12], [42, 42, 32, 12, "args"], [42, 46, 32, 12], [42, 53, 32, 12, "Array"], [42, 58, 32, 12], [42, 59, 32, 12, "_len"], [42, 63, 32, 12], [42, 66, 32, 12, "_key"], [42, 70, 32, 12], [42, 76, 32, 12, "_key"], [42, 80, 32, 12], [42, 83, 32, 12, "_len"], [42, 87, 32, 12], [42, 89, 32, 12, "_key"], [42, 93, 32, 12], [43, 10, 32, 12, "args"], [43, 14, 32, 12], [43, 15, 32, 12, "_key"], [43, 19, 32, 12], [43, 23, 32, 12, "arguments"], [43, 32, 32, 12], [43, 33, 32, 12, "_key"], [43, 37, 32, 12], [44, 8, 32, 12], [45, 8, 32, 12, "_this"], [45, 13, 32, 12], [45, 16, 32, 12, "_callSuper"], [45, 26, 32, 12], [45, 33, 32, 12, "Icon"], [45, 37, 32, 12], [45, 43, 32, 12, "args"], [45, 47, 32, 12], [46, 8, 32, 12, "_this"], [46, 13, 32, 12], [46, 14, 33, 4, "root"], [46, 18, 33, 8], [46, 21, 33, 11], [46, 25, 33, 15], [47, 8, 33, 15], [47, 15, 33, 15, "_this"], [47, 20, 33, 15], [48, 6, 33, 15], [49, 6, 33, 15], [49, 10, 33, 15, "_inherits2"], [49, 20, 33, 15], [49, 21, 33, 15, "default"], [49, 28, 33, 15], [49, 30, 33, 15, "Icon"], [49, 34, 33, 15], [49, 36, 33, 15, "_PureComponent"], [49, 50, 33, 15], [50, 6, 33, 15], [50, 17, 33, 15, "_createClass2"], [50, 30, 33, 15], [50, 31, 33, 15, "default"], [50, 38, 33, 15], [50, 40, 33, 15, "Icon"], [50, 44, 33, 15], [51, 8, 33, 15, "key"], [51, 11, 33, 15], [52, 8, 33, 15, "value"], [52, 13, 33, 15], [52, 15, 40, 4], [52, 24, 40, 4, "render"], [52, 30, 40, 10, "render"], [52, 31, 40, 10], [52, 33, 40, 13], [53, 10, 41, 6], [53, 14, 41, 6, "_this$props"], [53, 25, 41, 6], [53, 28, 41, 63], [53, 32, 41, 67], [53, 33, 41, 68, "props"], [53, 38, 41, 73], [54, 12, 41, 14, "name"], [54, 16, 41, 18], [54, 19, 41, 18, "_this$props"], [54, 30, 41, 18], [54, 31, 41, 14, "name"], [54, 35, 41, 18], [55, 12, 41, 20, "size"], [55, 16, 41, 24], [55, 19, 41, 24, "_this$props"], [55, 30, 41, 24], [55, 31, 41, 20, "size"], [55, 35, 41, 24], [56, 12, 41, 26, "color"], [56, 17, 41, 31], [56, 20, 41, 31, "_this$props"], [56, 31, 41, 31], [56, 32, 41, 26, "color"], [56, 37, 41, 31], [57, 12, 41, 33, "style"], [57, 17, 41, 38], [57, 20, 41, 38, "_this$props"], [57, 31, 41, 38], [57, 32, 41, 33, "style"], [57, 37, 41, 38], [58, 12, 41, 40, "children"], [58, 20, 41, 48], [58, 23, 41, 48, "_this$props"], [58, 34, 41, 48], [58, 35, 41, 40, "children"], [58, 43, 41, 48], [59, 12, 41, 53, "props"], [59, 17, 41, 58], [59, 24, 41, 58, "_objectWithoutProperties2"], [59, 49, 41, 58], [59, 50, 41, 58, "default"], [59, 57, 41, 58], [59, 59, 41, 58, "_this$props"], [59, 70, 41, 58], [59, 72, 41, 58, "_excluded"], [59, 81, 41, 58], [60, 10, 43, 6], [60, 14, 43, 10, "glyph"], [60, 19, 43, 15], [60, 22, 43, 18, "name"], [60, 26, 43, 22], [60, 29, 43, 25, "glyphMap"], [60, 37, 43, 33], [60, 38, 43, 34, "name"], [60, 42, 43, 38], [60, 43, 43, 39], [60, 47, 43, 43], [60, 50, 43, 46], [60, 53, 43, 49], [60, 55, 43, 51], [61, 10, 44, 6], [61, 14, 44, 10], [61, 21, 44, 17, "glyph"], [61, 26, 44, 22], [61, 31, 44, 27], [61, 39, 44, 35], [61, 41, 44, 37], [62, 12, 45, 8, "glyph"], [62, 17, 45, 13], [62, 20, 45, 16, "String"], [62, 26, 45, 22], [62, 27, 45, 23, "fromCodePoint"], [62, 40, 45, 36], [62, 41, 45, 37, "glyph"], [62, 46, 45, 42], [62, 47, 45, 43], [63, 10, 46, 6], [64, 10, 48, 6], [64, 14, 48, 12, "styleDefaults"], [64, 27, 48, 25], [64, 30, 48, 28], [65, 12, 49, 8, "fontSize"], [65, 20, 49, 16], [65, 22, 49, 18, "size"], [65, 26, 49, 22], [66, 12, 50, 8, "color"], [67, 10, 51, 6], [67, 11, 51, 7], [68, 10, 53, 6], [68, 14, 53, 12, "styleOverrides"], [68, 28, 53, 26], [68, 31, 53, 29], [69, 12, 54, 8, "fontFamily"], [69, 22, 54, 18], [69, 24, 54, 20, "fontReference"], [69, 37, 54, 33], [70, 12, 55, 8, "fontWeight"], [70, 22, 55, 18], [70, 24, 55, 20], [70, 32, 55, 28], [71, 12, 56, 8, "fontStyle"], [71, 21, 56, 17], [71, 23, 56, 19], [72, 10, 57, 6], [72, 11, 57, 7], [73, 10, 59, 6, "props"], [73, 15, 59, 11], [73, 16, 59, 12, "style"], [73, 21, 59, 17], [73, 24, 59, 20], [73, 25, 59, 21, "styleDefaults"], [73, 38, 59, 34], [73, 40, 59, 36, "style"], [73, 45, 59, 41], [73, 47, 59, 43, "styleOverrides"], [73, 61, 59, 57], [73, 63, 59, 59, "fontStyle"], [73, 72, 59, 68], [73, 76, 59, 72], [73, 77, 59, 73], [73, 78, 59, 74], [73, 79, 59, 75], [74, 10, 61, 6], [74, 30, 62, 8], [74, 34, 62, 8, "_jsxDevRuntime"], [74, 48, 62, 8], [74, 49, 62, 8, "jsxDEV"], [74, 55, 62, 8], [74, 57, 62, 9, "_reactNative"], [74, 69, 62, 9], [74, 70, 62, 9, "Text"], [74, 74, 62, 13], [75, 12, 62, 14, "selectable"], [75, 22, 62, 24], [75, 24, 62, 26], [75, 29, 62, 32], [76, 12, 62, 32], [76, 15, 62, 37, "props"], [76, 20, 62, 42], [77, 12, 62, 42, "children"], [77, 20, 62, 42], [77, 23, 63, 11, "glyph"], [77, 28, 63, 16], [77, 30, 64, 11, "children"], [77, 38, 64, 19], [78, 10, 64, 19], [79, 12, 64, 19, "fileName"], [79, 20, 64, 19], [79, 22, 64, 19, "_jsxFileName"], [79, 34, 64, 19], [80, 12, 64, 19, "lineNumber"], [80, 22, 64, 19], [81, 12, 64, 19, "columnNumber"], [81, 24, 64, 19], [82, 10, 64, 19], [82, 17, 65, 14], [82, 18, 65, 15], [83, 8, 67, 4], [84, 6, 67, 5], [85, 4, 67, 5], [85, 6, 32, 21, "PureComponent"], [85, 26, 32, 34], [86, 4, 32, 8, "Icon"], [86, 8, 32, 12], [86, 9, 35, 11, "defaultProps"], [86, 21, 35, 23], [86, 24, 35, 26], [87, 6, 36, 6, "size"], [87, 10, 36, 10], [87, 12, 36, 12, "DEFAULT_ICON_SIZE"], [87, 29, 36, 29], [88, 6, 37, 6, "allowFontScaling"], [88, 22, 37, 22], [88, 24, 37, 24], [89, 4, 38, 4], [89, 5, 38, 5], [90, 4, 70, 2], [90, 8, 70, 8, "imageSourceCache"], [90, 24, 70, 24], [90, 27, 70, 27], [90, 31, 70, 27, "createIconSourceCache"], [90, 61, 70, 48], [90, 63, 70, 49], [90, 64, 70, 50], [91, 4, 72, 2], [91, 13, 72, 11, "resolveGlyph"], [91, 25, 72, 23, "resolveGlyph"], [91, 26, 72, 24, "name"], [91, 30, 72, 28], [91, 32, 72, 30], [92, 6, 73, 4], [92, 10, 73, 10, "glyph"], [92, 15, 73, 15], [92, 18, 73, 18, "glyphMap"], [92, 26, 73, 26], [92, 27, 73, 27, "name"], [92, 31, 73, 31], [92, 32, 73, 32], [92, 36, 73, 36], [92, 39, 73, 39], [93, 6, 74, 4], [93, 10, 74, 8], [93, 17, 74, 15, "glyph"], [93, 22, 74, 20], [93, 27, 74, 25], [93, 35, 74, 33], [93, 37, 74, 35], [94, 8, 75, 6], [94, 15, 75, 13, "String"], [94, 21, 75, 19], [94, 22, 75, 20, "fromCodePoint"], [94, 35, 75, 33], [94, 36, 75, 34, "glyph"], [94, 41, 75, 39], [94, 42, 75, 40], [95, 6, 76, 4], [96, 6, 77, 4], [96, 13, 77, 11, "glyph"], [96, 18, 77, 16], [97, 4, 78, 2], [98, 4, 80, 2], [98, 13, 80, 11, "getImageSourceSync"], [98, 31, 80, 29, "getImageSourceSync"], [98, 32, 81, 4, "name"], [98, 36, 81, 8], [98, 38, 84, 4], [99, 6, 84, 4], [99, 10, 82, 4, "size"], [99, 14, 82, 8], [99, 17, 82, 8, "arguments"], [99, 26, 82, 8], [99, 27, 82, 8, "length"], [99, 33, 82, 8], [99, 41, 82, 8, "arguments"], [99, 50, 82, 8], [99, 58, 82, 8, "undefined"], [99, 67, 82, 8], [99, 70, 82, 8, "arguments"], [99, 79, 82, 8], [99, 85, 82, 11, "DEFAULT_ICON_SIZE"], [99, 102, 82, 28], [100, 6, 82, 28], [100, 10, 83, 4, "color"], [100, 15, 83, 9], [100, 18, 83, 9, "arguments"], [100, 27, 83, 9], [100, 28, 83, 9, "length"], [100, 34, 83, 9], [100, 42, 83, 9, "arguments"], [100, 51, 83, 9], [100, 59, 83, 9, "undefined"], [100, 68, 83, 9], [100, 71, 83, 9, "arguments"], [100, 80, 83, 9], [100, 86, 83, 12, "DEFAULT_ICON_COLOR"], [100, 104, 83, 30], [101, 6, 85, 4], [101, 10, 85, 4, "ensureNativeModuleAvailable"], [101, 46, 85, 31], [101, 48, 85, 32], [101, 49, 85, 33], [102, 6, 87, 4], [102, 10, 87, 10, "glyph"], [102, 15, 87, 15], [102, 18, 87, 18, "resolveGlyph"], [102, 30, 87, 30], [102, 31, 87, 31, "name"], [102, 35, 87, 35], [102, 36, 87, 36], [103, 6, 88, 4], [103, 10, 88, 10, "processedColor"], [103, 24, 88, 24], [103, 27, 88, 27], [103, 31, 88, 27, "processColor"], [103, 56, 88, 39], [103, 58, 88, 40, "color"], [103, 63, 88, 45], [103, 64, 88, 46], [104, 6, 89, 4], [104, 10, 89, 10, "cache<PERSON>ey"], [104, 18, 89, 18], [104, 21, 89, 21], [104, 24, 89, 24, "glyph"], [104, 29, 89, 29], [104, 33, 89, 33, "size"], [104, 37, 89, 37], [104, 41, 89, 41, "processedColor"], [104, 55, 89, 55], [104, 57, 89, 57], [105, 6, 91, 4], [105, 10, 91, 8, "imageSourceCache"], [105, 26, 91, 24], [105, 27, 91, 25, "has"], [105, 30, 91, 28], [105, 31, 91, 29, "cache<PERSON>ey"], [105, 39, 91, 37], [105, 40, 91, 38], [105, 42, 91, 40], [106, 8, 92, 6], [106, 15, 92, 13, "imageSourceCache"], [106, 31, 92, 29], [106, 32, 92, 30, "get"], [106, 35, 92, 33], [106, 36, 92, 34, "cache<PERSON>ey"], [106, 44, 92, 42], [106, 45, 92, 43], [107, 6, 93, 4], [108, 6, 94, 4], [108, 10, 94, 8], [109, 8, 95, 6], [109, 12, 95, 12, "imagePath"], [109, 21, 95, 21], [109, 24, 95, 24, "NativeIconAPI"], [109, 37, 95, 37], [109, 38, 95, 38, "getImageForFontSync"], [109, 57, 95, 57], [109, 58, 96, 8, "fontReference"], [109, 71, 96, 21], [109, 73, 97, 8, "glyph"], [109, 78, 97, 13], [109, 80, 98, 8, "size"], [109, 84, 98, 12], [109, 86, 99, 8, "processedColor"], [109, 100, 100, 6], [109, 101, 100, 7], [110, 8, 101, 6], [110, 12, 101, 12, "value"], [110, 17, 101, 17], [110, 20, 101, 20], [111, 10, 101, 22, "uri"], [111, 13, 101, 25], [111, 15, 101, 27, "imagePath"], [111, 24, 101, 36], [112, 10, 101, 38, "scale"], [112, 15, 101, 43], [112, 17, 101, 45, "PixelRatio"], [112, 40, 101, 55], [112, 41, 101, 56, "get"], [112, 44, 101, 59], [112, 45, 101, 60], [113, 8, 101, 62], [113, 9, 101, 63], [114, 8, 102, 6, "imageSourceCache"], [114, 24, 102, 22], [114, 25, 102, 23, "setValue"], [114, 33, 102, 31], [114, 34, 102, 32, "cache<PERSON>ey"], [114, 42, 102, 40], [114, 44, 102, 42, "value"], [114, 49, 102, 47], [114, 50, 102, 48], [115, 8, 103, 6], [115, 15, 103, 13, "value"], [115, 20, 103, 18], [116, 6, 104, 4], [116, 7, 104, 5], [116, 8, 104, 6], [116, 15, 104, 13, "error"], [116, 20, 104, 18], [116, 22, 104, 20], [117, 8, 105, 6, "imageSourceCache"], [117, 24, 105, 22], [117, 25, 105, 23, "setError"], [117, 33, 105, 31], [117, 34, 105, 32, "cache<PERSON>ey"], [117, 42, 105, 40], [117, 44, 105, 42, "error"], [117, 49, 105, 47], [117, 50, 105, 48], [118, 8, 106, 6], [118, 14, 106, 12, "error"], [118, 19, 106, 17], [119, 6, 107, 4], [120, 4, 108, 2], [121, 4, 108, 3], [121, 13, 110, 17, "getImageSource"], [121, 27, 110, 31, "getImageSource"], [121, 28, 110, 31, "_x"], [121, 30, 110, 31], [122, 6, 110, 31], [122, 13, 110, 31, "_getImageSource"], [122, 28, 110, 31], [122, 29, 110, 31, "apply"], [122, 34, 110, 31], [122, 41, 110, 31, "arguments"], [122, 50, 110, 31], [123, 4, 110, 31], [124, 4, 110, 31], [124, 13, 110, 31, "_getImageSource"], [124, 29, 110, 31], [125, 6, 110, 31, "_getImageSource"], [125, 21, 110, 31], [125, 28, 110, 31, "_asyncToGenerator2"], [125, 46, 110, 31], [125, 47, 110, 31, "default"], [125, 54, 110, 31], [125, 56, 110, 2], [125, 67, 111, 4, "name"], [125, 71, 111, 8], [125, 73, 114, 4], [126, 8, 114, 4], [126, 12, 112, 4, "size"], [126, 16, 112, 8], [126, 19, 112, 8, "arguments"], [126, 28, 112, 8], [126, 29, 112, 8, "length"], [126, 35, 112, 8], [126, 43, 112, 8, "arguments"], [126, 52, 112, 8], [126, 60, 112, 8, "undefined"], [126, 69, 112, 8], [126, 72, 112, 8, "arguments"], [126, 81, 112, 8], [126, 87, 112, 11, "DEFAULT_ICON_SIZE"], [126, 104, 112, 28], [127, 8, 112, 28], [127, 12, 113, 4, "color"], [127, 17, 113, 9], [127, 20, 113, 9, "arguments"], [127, 29, 113, 9], [127, 30, 113, 9, "length"], [127, 36, 113, 9], [127, 44, 113, 9, "arguments"], [127, 53, 113, 9], [127, 61, 113, 9, "undefined"], [127, 70, 113, 9], [127, 73, 113, 9, "arguments"], [127, 82, 113, 9], [127, 88, 113, 12, "DEFAULT_ICON_COLOR"], [127, 106, 113, 30], [128, 8, 115, 4], [128, 12, 115, 4, "ensureNativeModuleAvailable"], [128, 48, 115, 31], [128, 50, 115, 32], [128, 51, 115, 33], [129, 8, 117, 4], [129, 12, 117, 10, "glyph"], [129, 17, 117, 15], [129, 20, 117, 18, "resolveGlyph"], [129, 32, 117, 30], [129, 33, 117, 31, "name"], [129, 37, 117, 35], [129, 38, 117, 36], [130, 8, 118, 4], [130, 12, 118, 10, "processedColor"], [130, 26, 118, 24], [130, 29, 118, 27], [130, 33, 118, 27, "processColor"], [130, 58, 118, 39], [130, 60, 118, 40, "color"], [130, 65, 118, 45], [130, 66, 118, 46], [131, 8, 119, 4], [131, 12, 119, 10, "cache<PERSON>ey"], [131, 20, 119, 18], [131, 23, 119, 21], [131, 26, 119, 24, "glyph"], [131, 31, 119, 29], [131, 35, 119, 33, "size"], [131, 39, 119, 37], [131, 43, 119, 41, "processedColor"], [131, 57, 119, 55], [131, 59, 119, 57], [132, 8, 121, 4], [132, 12, 121, 8, "imageSourceCache"], [132, 28, 121, 24], [132, 29, 121, 25, "has"], [132, 32, 121, 28], [132, 33, 121, 29, "cache<PERSON>ey"], [132, 41, 121, 37], [132, 42, 121, 38], [132, 44, 121, 40], [133, 10, 122, 6], [133, 17, 122, 13, "imageSourceCache"], [133, 33, 122, 29], [133, 34, 122, 30, "get"], [133, 37, 122, 33], [133, 38, 122, 34, "cache<PERSON>ey"], [133, 46, 122, 42], [133, 47, 122, 43], [134, 8, 123, 4], [135, 8, 124, 4], [135, 12, 124, 8], [136, 10, 125, 6], [136, 14, 125, 12, "imagePath"], [136, 23, 125, 21], [136, 32, 125, 30, "NativeIconAPI"], [136, 45, 125, 43], [136, 46, 125, 44, "getImageForFont"], [136, 61, 125, 59], [136, 62, 126, 8, "fontReference"], [136, 75, 126, 21], [136, 77, 127, 8, "glyph"], [136, 82, 127, 13], [136, 84, 128, 8, "size"], [136, 88, 128, 12], [136, 90, 129, 8, "processedColor"], [136, 104, 130, 6], [136, 105, 130, 7], [137, 10, 131, 6], [137, 14, 131, 12, "value"], [137, 19, 131, 17], [137, 22, 131, 20], [138, 12, 131, 22, "uri"], [138, 15, 131, 25], [138, 17, 131, 27, "imagePath"], [138, 26, 131, 36], [139, 12, 131, 38, "scale"], [139, 17, 131, 43], [139, 19, 131, 45, "PixelRatio"], [139, 42, 131, 55], [139, 43, 131, 56, "get"], [139, 46, 131, 59], [139, 47, 131, 60], [140, 10, 131, 62], [140, 11, 131, 63], [141, 10, 132, 6, "imageSourceCache"], [141, 26, 132, 22], [141, 27, 132, 23, "setValue"], [141, 35, 132, 31], [141, 36, 132, 32, "cache<PERSON>ey"], [141, 44, 132, 40], [141, 46, 132, 42, "value"], [141, 51, 132, 47], [141, 52, 132, 48], [142, 10, 133, 6], [142, 17, 133, 13, "value"], [142, 22, 133, 18], [143, 8, 134, 4], [143, 9, 134, 5], [143, 10, 134, 6], [143, 17, 134, 13, "error"], [143, 22, 134, 18], [143, 24, 134, 20], [144, 10, 135, 6, "imageSourceCache"], [144, 26, 135, 22], [144, 27, 135, 23, "setError"], [144, 35, 135, 31], [144, 36, 135, 32, "cache<PERSON>ey"], [144, 44, 135, 40], [144, 46, 135, 42, "error"], [144, 51, 135, 47], [144, 52, 135, 48], [145, 10, 136, 6], [145, 16, 136, 12, "error"], [145, 21, 136, 17], [146, 8, 137, 4], [147, 6, 138, 2], [147, 7, 138, 3], [148, 6, 138, 3], [148, 13, 138, 3, "_getImageSource"], [148, 28, 138, 3], [148, 29, 138, 3, "apply"], [148, 34, 138, 3], [148, 41, 138, 3, "arguments"], [148, 50, 138, 3], [149, 4, 138, 3], [150, 4, 138, 3], [150, 13, 140, 17, "loadFont"], [150, 21, 140, 25, "loadFont"], [150, 22, 140, 25], [151, 6, 140, 25], [151, 13, 140, 25, "_loadFont"], [151, 22, 140, 25], [151, 23, 140, 25, "apply"], [151, 28, 140, 25], [151, 35, 140, 25, "arguments"], [151, 44, 140, 25], [152, 4, 140, 25], [153, 4, 140, 25], [153, 13, 140, 25, "_loadFont"], [153, 23, 140, 25], [154, 6, 140, 25, "_loadFont"], [154, 15, 140, 25], [154, 22, 140, 25, "_asyncToGenerator2"], [154, 40, 140, 25], [154, 41, 140, 25, "default"], [154, 48, 140, 25], [154, 50, 140, 2], [154, 63, 140, 43], [155, 8, 140, 43], [155, 12, 140, 26, "file"], [155, 16, 140, 30], [155, 19, 140, 30, "arguments"], [155, 28, 140, 30], [155, 29, 140, 30, "length"], [155, 35, 140, 30], [155, 43, 140, 30, "arguments"], [155, 52, 140, 30], [155, 60, 140, 30, "undefined"], [155, 69, 140, 30], [155, 72, 140, 30, "arguments"], [155, 81, 140, 30], [155, 87, 140, 33, "fontFile"], [155, 95, 140, 41], [156, 8, 141, 4], [156, 12, 141, 8, "Platform"], [156, 33, 141, 16], [156, 34, 141, 17, "OS"], [156, 36, 141, 19], [156, 41, 141, 24], [156, 46, 141, 29], [156, 48, 141, 31], [157, 10, 142, 6], [157, 14, 142, 6, "ensureNativeModuleAvailable"], [157, 50, 142, 33], [157, 52, 142, 34], [157, 53, 142, 35], [158, 10, 143, 6], [158, 14, 143, 10], [158, 15, 143, 11, "file"], [158, 19, 143, 15], [158, 21, 143, 17], [159, 12, 144, 8], [159, 18, 144, 14], [159, 22, 144, 18, "Error"], [159, 27, 144, 23], [159, 28, 144, 24], [159, 82, 144, 78], [159, 83, 144, 79], [160, 10, 145, 6], [161, 10, 146, 6], [161, 16, 146, 12, "NativeIconAPI"], [161, 29, 146, 25], [161, 30, 146, 26, "loadFontWithFileName"], [161, 50, 146, 46], [161, 51, 146, 47], [161, 54, 146, 50, "file"], [161, 58, 146, 54], [161, 59, 146, 55, "split"], [161, 64, 146, 60], [161, 65, 146, 61], [161, 68, 146, 64], [161, 69, 146, 65], [161, 70, 146, 66], [162, 8, 147, 4], [163, 6, 148, 2], [163, 7, 148, 3], [164, 6, 148, 3], [164, 13, 148, 3, "_loadFont"], [164, 22, 148, 3], [164, 23, 148, 3, "apply"], [164, 28, 148, 3], [164, 35, 148, 3, "arguments"], [164, 44, 148, 3], [165, 4, 148, 3], [166, 4, 150, 2], [166, 13, 150, 11, "hasIcon"], [166, 20, 150, 18, "hasIcon"], [166, 21, 150, 19, "name"], [166, 25, 150, 23], [166, 27, 150, 25], [167, 6, 151, 4], [167, 13, 151, 11, "Object"], [167, 19, 151, 17], [167, 20, 151, 18, "prototype"], [167, 29, 151, 27], [167, 30, 151, 28, "hasOwnProperty"], [167, 44, 151, 42], [167, 45, 151, 43, "call"], [167, 49, 151, 47], [167, 50, 151, 48, "glyphMap"], [167, 58, 151, 56], [167, 60, 151, 58, "name"], [167, 64, 151, 62], [167, 65, 151, 63], [168, 4, 152, 2], [169, 4, 154, 2], [169, 13, 154, 11, "getRawGlyphMap"], [169, 27, 154, 25, "getRawGlyphMap"], [169, 28, 154, 25], [169, 30, 154, 28], [170, 6, 155, 4], [170, 13, 155, 11, "glyphMap"], [170, 21, 155, 19], [171, 4, 156, 2], [172, 4, 158, 2], [172, 13, 158, 11, "getFontFamily"], [172, 26, 158, 24, "getFontFamily"], [172, 27, 158, 24], [172, 29, 158, 27], [173, 6, 159, 4], [173, 13, 159, 11, "fontReference"], [173, 26, 159, 24], [174, 4, 160, 2], [175, 4, 162, 2, "Icon"], [175, 8, 162, 6], [175, 9, 162, 7, "<PERSON><PERSON>"], [175, 15, 162, 13], [175, 18, 162, 16], [175, 22, 162, 16, "createIconButtonComponent"], [175, 41, 162, 41], [175, 43, 162, 42, "Icon"], [175, 47, 162, 46], [175, 48, 162, 47], [176, 4, 163, 2, "Icon"], [176, 8, 163, 6], [176, 9, 163, 7, "getImageSource"], [176, 23, 163, 21], [176, 26, 163, 24, "getImageSource"], [176, 40, 163, 38], [177, 4, 164, 2, "Icon"], [177, 8, 164, 6], [177, 9, 164, 7, "getImageSourceSync"], [177, 27, 164, 25], [177, 30, 164, 28, "getImageSourceSync"], [177, 48, 164, 46], [178, 4, 165, 2, "Icon"], [178, 8, 165, 6], [178, 9, 165, 7, "loadFont"], [178, 17, 165, 15], [178, 20, 165, 18, "loadFont"], [178, 28, 165, 26], [179, 4, 166, 2, "Icon"], [179, 8, 166, 6], [179, 9, 166, 7, "hasIcon"], [179, 16, 166, 14], [179, 19, 166, 17, "hasIcon"], [179, 26, 166, 24], [180, 4, 167, 2, "Icon"], [180, 8, 167, 6], [180, 9, 167, 7, "getRawGlyphMap"], [180, 23, 167, 21], [180, 26, 167, 24, "getRawGlyphMap"], [180, 40, 167, 38], [181, 4, 168, 2, "Icon"], [181, 8, 168, 6], [181, 9, 168, 7, "getFontFamily"], [181, 22, 168, 20], [181, 25, 168, 23, "getFontFamily"], [181, 38, 168, 36], [182, 4, 170, 2], [182, 11, 170, 9, "Icon"], [182, 15, 170, 13], [183, 2, 171, 0], [184, 0, 171, 1], [184, 3]], "functionMap": {"names": ["<global>", "createIconSet", "Icon", "Icon#render", "resolveGlyph", "getImageSourceSync", "getImageSource", "loadFont", "hasIcon", "getRawGlyphMap", "getFontFamily"], "mappings": "AAA;eCa;ECkB;ICQ;KD2B;GDC;EGI;GHM;EIE;GJ4B;EKE;GL4B;EME;GNQ;EOE;GPE;EQE;GRE;ESE;GTE;CDW"}}, "type": "js/module"}]}