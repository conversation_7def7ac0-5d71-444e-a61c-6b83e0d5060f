{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 156, "index": 171}}], "key": "Wm75LgE4xYscVWo0KoLFlflJQCo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 172}, "end": {"line": 4, "column": 31, "index": 203}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 204}, "end": {"line": 5, "column": 43, "index": 247}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "use-latest-callback", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 248}, "end": {"line": 6, "column": 52, "index": 300}}], "key": "2ER/r3Agt+5SFwaFR8HXg24Rpu4=", "exportNames": ["*"]}}, {"name": "./LinkingContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 301}, "end": {"line": 7, "column": 53, "index": 354}}], "key": "r/0Yvi+HouDAqn4vN4m4I6AMfEI=", "exportNames": ["*"]}}, {"name": "./LocaleDirContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 355}, "end": {"line": 8, "column": 57, "index": 412}}], "key": "JhefGuX6ok+3UUDM4KPL7UjhyjI=", "exportNames": ["*"]}}, {"name": "./theming/DefaultTheme.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 413}, "end": {"line": 9, "column": 57, "index": 470}}], "key": "kkwFOcbvVeXoVn3++UTGCa2rZaw=", "exportNames": ["*"]}}, {"name": "./UnhandledLinkingContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 471}, "end": {"line": 10, "column": 71, "index": 542}}], "key": "4bj4SmNbRtH5ACzulwdy6l1c5bQ=", "exportNames": ["*"]}}, {"name": "./useBackButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 543}, "end": {"line": 11, "column": 48, "index": 591}}], "key": "6AuAHDn5tDI/cM8vnoiQ8MvFC+A=", "exportNames": ["*"]}}, {"name": "./useDocumentTitle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 592}, "end": {"line": 12, "column": 54, "index": 646}}], "key": "Sde5qNzOTEFCaFuQn9lFtNZvwUc=", "exportNames": ["*"]}}, {"name": "./useLinking", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 647}, "end": {"line": 13, "column": 42, "index": 689}}], "key": "6DpfPB6RfLSnPu0pwnvv4tRfW94=", "exportNames": ["*"]}}, {"name": "./useThenable.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 690}, "end": {"line": 14, "column": 47, "index": 737}}], "key": "uVz/3cd7diisUOFjz1tYN4bnBsE=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 738}, "end": {"line": 15, "column": 48, "index": 786}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.NavigationContainer = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _core = require(_dependencyMap[3], \"@react-navigation/core\");\n  var React = _interopRequireWildcard(require(_dependencyMap[4], \"react\"));\n  var _reactNative = require(_dependencyMap[5], \"react-native\");\n  var _useLatestCallback = _interopRequireDefault(require(_dependencyMap[6], \"use-latest-callback\"));\n  var _LinkingContext = require(_dependencyMap[7], \"./LinkingContext.js\");\n  var _LocaleDirContext = require(_dependencyMap[8], \"./LocaleDirContext.js\");\n  var _DefaultTheme = require(_dependencyMap[9], \"./theming/DefaultTheme.js\");\n  var _UnhandledLinkingContext = require(_dependencyMap[10], \"./UnhandledLinkingContext.js\");\n  var _useBackButton = require(_dependencyMap[11], \"./useBackButton\");\n  var _useDocumentTitle = require(_dependencyMap[12], \"./useDocumentTitle\");\n  var _useLinking2 = require(_dependencyMap[13], \"./useLinking\");\n  var _useThenable3 = require(_dependencyMap[14], \"./useThenable.js\");\n  var _jsxRuntime = require(_dependencyMap[15], \"react/jsx-runtime\");\n  var _excluded = [\"direction\", \"theme\", \"linking\", \"fallback\", \"documentTitle\", \"onReady\", \"onStateChange\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  globalThis.REACT_NAVIGATION_DEVTOOLS = new WeakMap();\n  /**\n   * Container component which holds the navigation state designed for React Native apps.\n   * This should be rendered at the root wrapping the whole app.\n   *\n   * @param props.initialState Initial state object for the navigation tree. When deep link handling is enabled, this will override deep links when specified. Make sure that you don't specify an `initialState` when there's a deep link (`Linking.getInitialURL()`).\n   * @param props.onReady Callback which is called after the navigation tree mounts.\n   * @param props.onStateChange Callback which is called with the latest navigation state when it changes.\n   * @param props.onUnhandledAction Callback which is called when an action is not handled.\n   * @param props.direction Text direction of the components. Defaults to `'ltr'`.\n   * @param props.theme Theme object for the UI elements.\n   * @param props.linking Options for deep linking. Deep link handling is enabled when this prop is provided, unless `linking.enabled` is `false`.\n   * @param props.fallback Fallback component to render until we have finished getting initial state when linking is enabled. Defaults to `null`.\n   * @param props.documentTitle Options to configure the document title on Web. Updating document title is handled by default unless `documentTitle.enabled` is `false`.\n   * @param props.children Child elements to render the content.\n   * @param props.ref Ref object which refers to the navigation object containing helper methods.\n   */\n  function NavigationContainerInner(_ref, ref) {\n    var _ref$direction = _ref.direction,\n      direction = _ref$direction === void 0 ? _reactNative.I18nManager.getConstants().isRTL ? 'rtl' : 'ltr' : _ref$direction,\n      _ref$theme = _ref.theme,\n      theme = _ref$theme === void 0 ? _DefaultTheme.DefaultTheme : _ref$theme,\n      linking = _ref.linking,\n      _ref$fallback = _ref.fallback,\n      fallback = _ref$fallback === void 0 ? null : _ref$fallback,\n      documentTitle = _ref.documentTitle,\n      onReady = _ref.onReady,\n      onStateChange = _ref.onStateChange,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var isLinkingEnabled = linking ? linking.enabled !== false : false;\n    if (linking?.config) {\n      (0, _core.validatePathConfig)(linking.config);\n    }\n    var refContainer = React.useRef(null);\n    (0, _useBackButton.useBackButton)(refContainer);\n    (0, _useDocumentTitle.useDocumentTitle)(refContainer, documentTitle);\n    var _React$useState = React.useState(),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      lastUnhandledLink = _React$useState2[0],\n      setLastUnhandledLink = _React$useState2[1];\n    var _useLinking = (0, _useLinking2.useLinking)(refContainer, {\n        enabled: isLinkingEnabled,\n        prefixes: [],\n        ...linking\n      }, setLastUnhandledLink),\n      getInitialState = _useLinking.getInitialState;\n    var linkingContext = React.useMemo(() => ({\n      options: linking\n    }), [linking]);\n    var unhandledLinkingContext = React.useMemo(() => ({\n      lastUnhandledLink,\n      setLastUnhandledLink\n    }), [lastUnhandledLink, setLastUnhandledLink]);\n    var onReadyForLinkingHandling = (0, _useLatestCallback.default)(() => {\n      // If the screen path matches lastUnhandledLink, we do not track it\n      var path = refContainer.current?.getCurrentRoute()?.path;\n      setLastUnhandledLink(previousLastUnhandledLink => {\n        if (previousLastUnhandledLink === path) {\n          return undefined;\n        }\n        return previousLastUnhandledLink;\n      });\n      onReady?.();\n    });\n    var onStateChangeForLinkingHandling = (0, _useLatestCallback.default)(state => {\n      // If the screen path matches lastUnhandledLink, we do not track it\n      var path = refContainer.current?.getCurrentRoute()?.path;\n      setLastUnhandledLink(previousLastUnhandledLink => {\n        if (previousLastUnhandledLink === path) {\n          return undefined;\n        }\n        return previousLastUnhandledLink;\n      });\n      onStateChange?.(state);\n    });\n    // Add additional linking related info to the ref\n    // This will be used by the devtools\n    React.useEffect(() => {\n      if (refContainer.current) {\n        REACT_NAVIGATION_DEVTOOLS.set(refContainer.current, {\n          get linking() {\n            return {\n              ...linking,\n              enabled: isLinkingEnabled,\n              prefixes: linking?.prefixes ?? [],\n              getStateFromPath: linking?.getStateFromPath ?? _core.getStateFromPath,\n              getPathFromState: linking?.getPathFromState ?? _core.getPathFromState,\n              getActionFromState: linking?.getActionFromState ?? _core.getActionFromState\n            };\n          }\n        });\n      }\n    });\n    var _useThenable = (0, _useThenable3.useThenable)(getInitialState),\n      _useThenable2 = (0, _slicedToArray2.default)(_useThenable, 2),\n      isResolved = _useThenable2[0],\n      initialState = _useThenable2[1];\n\n    // FIXME\n    // @ts-expect-error not sure why this is not working\n    React.useImperativeHandle(ref, () => refContainer.current);\n    var isLinkingReady = rest.initialState != null || !isLinkingEnabled || isResolved;\n    if (!isLinkingReady) {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_LocaleDirContext.LocaleDirContext.Provider, {\n        value: direction,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_core.ThemeProvider, {\n          value: theme,\n          children: fallback\n        })\n      });\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_LocaleDirContext.LocaleDirContext.Provider, {\n      value: direction,\n      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_UnhandledLinkingContext.UnhandledLinkingContext.Provider, {\n        value: unhandledLinkingContext,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_LinkingContext.LinkingContext.Provider, {\n          value: linkingContext,\n          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_core.BaseNavigationContainer, {\n            ...rest,\n            theme: theme,\n            onReady: onReadyForLinkingHandling,\n            onStateChange: onStateChangeForLinkingHandling,\n            initialState: rest.initialState == null ? initialState : rest.initialState,\n            ref: refContainer\n          })\n        })\n      })\n    });\n  }\n  var NavigationContainer = exports.NavigationContainer = /*#__PURE__*/React.forwardRef(NavigationContainerInner);\n});", "lineCount": 156, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "NavigationContainer"], [8, 29, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_objectWithoutProperties2"], [10, 31, 1, 13], [10, 34, 1, 13, "_interopRequireDefault"], [10, 56, 1, 13], [10, 57, 1, 13, "require"], [10, 64, 1, 13], [10, 65, 1, 13, "_dependencyMap"], [10, 79, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "_core"], [11, 11, 3, 0], [11, 14, 3, 0, "require"], [11, 21, 3, 0], [11, 22, 3, 0, "_dependencyMap"], [11, 36, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "React"], [12, 11, 4, 0], [12, 14, 4, 0, "_interopRequireWildcard"], [12, 37, 4, 0], [12, 38, 4, 0, "require"], [12, 45, 4, 0], [12, 46, 4, 0, "_dependencyMap"], [12, 60, 4, 0], [13, 2, 5, 0], [13, 6, 5, 0, "_reactNative"], [13, 18, 5, 0], [13, 21, 5, 0, "require"], [13, 28, 5, 0], [13, 29, 5, 0, "_dependencyMap"], [13, 43, 5, 0], [14, 2, 6, 0], [14, 6, 6, 0, "_useLatestCallback"], [14, 24, 6, 0], [14, 27, 6, 0, "_interopRequireDefault"], [14, 49, 6, 0], [14, 50, 6, 0, "require"], [14, 57, 6, 0], [14, 58, 6, 0, "_dependencyMap"], [14, 72, 6, 0], [15, 2, 7, 0], [15, 6, 7, 0, "_LinkingContext"], [15, 21, 7, 0], [15, 24, 7, 0, "require"], [15, 31, 7, 0], [15, 32, 7, 0, "_dependencyMap"], [15, 46, 7, 0], [16, 2, 8, 0], [16, 6, 8, 0, "_LocaleDirContext"], [16, 23, 8, 0], [16, 26, 8, 0, "require"], [16, 33, 8, 0], [16, 34, 8, 0, "_dependencyMap"], [16, 48, 8, 0], [17, 2, 9, 0], [17, 6, 9, 0, "_DefaultTheme"], [17, 19, 9, 0], [17, 22, 9, 0, "require"], [17, 29, 9, 0], [17, 30, 9, 0, "_dependencyMap"], [17, 44, 9, 0], [18, 2, 10, 0], [18, 6, 10, 0, "_UnhandledLinkingContext"], [18, 30, 10, 0], [18, 33, 10, 0, "require"], [18, 40, 10, 0], [18, 41, 10, 0, "_dependencyMap"], [18, 55, 10, 0], [19, 2, 11, 0], [19, 6, 11, 0, "_useBackButton"], [19, 20, 11, 0], [19, 23, 11, 0, "require"], [19, 30, 11, 0], [19, 31, 11, 0, "_dependencyMap"], [19, 45, 11, 0], [20, 2, 12, 0], [20, 6, 12, 0, "_useDocumentTitle"], [20, 23, 12, 0], [20, 26, 12, 0, "require"], [20, 33, 12, 0], [20, 34, 12, 0, "_dependencyMap"], [20, 48, 12, 0], [21, 2, 13, 0], [21, 6, 13, 0, "_useLinking2"], [21, 18, 13, 0], [21, 21, 13, 0, "require"], [21, 28, 13, 0], [21, 29, 13, 0, "_dependencyMap"], [21, 43, 13, 0], [22, 2, 14, 0], [22, 6, 14, 0, "_useThenable3"], [22, 19, 14, 0], [22, 22, 14, 0, "require"], [22, 29, 14, 0], [22, 30, 14, 0, "_dependencyMap"], [22, 44, 14, 0], [23, 2, 15, 0], [23, 6, 15, 0, "_jsxRuntime"], [23, 17, 15, 0], [23, 20, 15, 0, "require"], [23, 27, 15, 0], [23, 28, 15, 0, "_dependencyMap"], [23, 42, 15, 0], [24, 2, 15, 48], [24, 6, 15, 48, "_excluded"], [24, 15, 15, 48], [25, 2, 15, 48], [25, 11, 15, 48, "_interopRequireWildcard"], [25, 35, 15, 48, "e"], [25, 36, 15, 48], [25, 38, 15, 48, "t"], [25, 39, 15, 48], [25, 68, 15, 48, "WeakMap"], [25, 75, 15, 48], [25, 81, 15, 48, "r"], [25, 82, 15, 48], [25, 89, 15, 48, "WeakMap"], [25, 96, 15, 48], [25, 100, 15, 48, "n"], [25, 101, 15, 48], [25, 108, 15, 48, "WeakMap"], [25, 115, 15, 48], [25, 127, 15, 48, "_interopRequireWildcard"], [25, 150, 15, 48], [25, 162, 15, 48, "_interopRequireWildcard"], [25, 163, 15, 48, "e"], [25, 164, 15, 48], [25, 166, 15, 48, "t"], [25, 167, 15, 48], [25, 176, 15, 48, "t"], [25, 177, 15, 48], [25, 181, 15, 48, "e"], [25, 182, 15, 48], [25, 186, 15, 48, "e"], [25, 187, 15, 48], [25, 188, 15, 48, "__esModule"], [25, 198, 15, 48], [25, 207, 15, 48, "e"], [25, 208, 15, 48], [25, 214, 15, 48, "o"], [25, 215, 15, 48], [25, 217, 15, 48, "i"], [25, 218, 15, 48], [25, 220, 15, 48, "f"], [25, 221, 15, 48], [25, 226, 15, 48, "__proto__"], [25, 235, 15, 48], [25, 243, 15, 48, "default"], [25, 250, 15, 48], [25, 252, 15, 48, "e"], [25, 253, 15, 48], [25, 270, 15, 48, "e"], [25, 271, 15, 48], [25, 294, 15, 48, "e"], [25, 295, 15, 48], [25, 320, 15, 48, "e"], [25, 321, 15, 48], [25, 330, 15, 48, "f"], [25, 331, 15, 48], [25, 337, 15, 48, "o"], [25, 338, 15, 48], [25, 341, 15, 48, "t"], [25, 342, 15, 48], [25, 345, 15, 48, "n"], [25, 346, 15, 48], [25, 349, 15, 48, "r"], [25, 350, 15, 48], [25, 358, 15, 48, "o"], [25, 359, 15, 48], [25, 360, 15, 48, "has"], [25, 363, 15, 48], [25, 364, 15, 48, "e"], [25, 365, 15, 48], [25, 375, 15, 48, "o"], [25, 376, 15, 48], [25, 377, 15, 48, "get"], [25, 380, 15, 48], [25, 381, 15, 48, "e"], [25, 382, 15, 48], [25, 385, 15, 48, "o"], [25, 386, 15, 48], [25, 387, 15, 48, "set"], [25, 390, 15, 48], [25, 391, 15, 48, "e"], [25, 392, 15, 48], [25, 394, 15, 48, "f"], [25, 395, 15, 48], [25, 409, 15, 48, "_t"], [25, 411, 15, 48], [25, 415, 15, 48, "e"], [25, 416, 15, 48], [25, 432, 15, 48, "_t"], [25, 434, 15, 48], [25, 441, 15, 48, "hasOwnProperty"], [25, 455, 15, 48], [25, 456, 15, 48, "call"], [25, 460, 15, 48], [25, 461, 15, 48, "e"], [25, 462, 15, 48], [25, 464, 15, 48, "_t"], [25, 466, 15, 48], [25, 473, 15, 48, "i"], [25, 474, 15, 48], [25, 478, 15, 48, "o"], [25, 479, 15, 48], [25, 482, 15, 48, "Object"], [25, 488, 15, 48], [25, 489, 15, 48, "defineProperty"], [25, 503, 15, 48], [25, 508, 15, 48, "Object"], [25, 514, 15, 48], [25, 515, 15, 48, "getOwnPropertyDescriptor"], [25, 539, 15, 48], [25, 540, 15, 48, "e"], [25, 541, 15, 48], [25, 543, 15, 48, "_t"], [25, 545, 15, 48], [25, 552, 15, 48, "i"], [25, 553, 15, 48], [25, 554, 15, 48, "get"], [25, 557, 15, 48], [25, 561, 15, 48, "i"], [25, 562, 15, 48], [25, 563, 15, 48, "set"], [25, 566, 15, 48], [25, 570, 15, 48, "o"], [25, 571, 15, 48], [25, 572, 15, 48, "f"], [25, 573, 15, 48], [25, 575, 15, 48, "_t"], [25, 577, 15, 48], [25, 579, 15, 48, "i"], [25, 580, 15, 48], [25, 584, 15, 48, "f"], [25, 585, 15, 48], [25, 586, 15, 48, "_t"], [25, 588, 15, 48], [25, 592, 15, 48, "e"], [25, 593, 15, 48], [25, 594, 15, 48, "_t"], [25, 596, 15, 48], [25, 607, 15, 48, "f"], [25, 608, 15, 48], [25, 613, 15, 48, "e"], [25, 614, 15, 48], [25, 616, 15, 48, "t"], [25, 617, 15, 48], [26, 2, 16, 0, "globalThis"], [26, 12, 16, 10], [26, 13, 16, 11, "REACT_NAVIGATION_DEVTOOLS"], [26, 38, 16, 36], [26, 41, 16, 39], [26, 45, 16, 43, "WeakMap"], [26, 52, 16, 50], [26, 53, 16, 51], [26, 54, 16, 52], [27, 2, 17, 0], [28, 0, 18, 0], [29, 0, 19, 0], [30, 0, 20, 0], [31, 0, 21, 0], [32, 0, 22, 0], [33, 0, 23, 0], [34, 0, 24, 0], [35, 0, 25, 0], [36, 0, 26, 0], [37, 0, 27, 0], [38, 0, 28, 0], [39, 0, 29, 0], [40, 0, 30, 0], [41, 0, 31, 0], [42, 0, 32, 0], [43, 2, 33, 0], [43, 11, 33, 9, "NavigationContainerInner"], [43, 35, 33, 33, "NavigationContainerInner"], [43, 36, 33, 33, "_ref"], [43, 40, 33, 33], [43, 42, 42, 3, "ref"], [43, 45, 42, 6], [43, 47, 42, 8], [44, 4, 42, 8], [44, 8, 42, 8, "_ref$direction"], [44, 22, 42, 8], [44, 25, 42, 8, "_ref"], [44, 29, 42, 8], [44, 30, 34, 2, "direction"], [44, 39, 34, 11], [45, 6, 34, 2, "direction"], [45, 15, 34, 11], [45, 18, 34, 11, "_ref$direction"], [45, 32, 34, 11], [45, 46, 34, 14, "I18nManager"], [45, 70, 34, 25], [45, 71, 34, 26, "getConstants"], [45, 83, 34, 38], [45, 84, 34, 39], [45, 85, 34, 40], [45, 86, 34, 41, "isRTL"], [45, 91, 34, 46], [45, 94, 34, 49], [45, 99, 34, 54], [45, 102, 34, 57], [45, 107, 34, 62], [45, 110, 34, 62, "_ref$direction"], [45, 124, 34, 62], [46, 6, 34, 62, "_ref$theme"], [46, 16, 34, 62], [46, 19, 34, 62, "_ref"], [46, 23, 34, 62], [46, 24, 35, 2, "theme"], [46, 29, 35, 7], [47, 6, 35, 2, "theme"], [47, 11, 35, 7], [47, 14, 35, 7, "_ref$theme"], [47, 24, 35, 7], [47, 38, 35, 10, "DefaultTheme"], [47, 64, 35, 22], [47, 67, 35, 22, "_ref$theme"], [47, 77, 35, 22], [48, 6, 36, 2, "linking"], [48, 13, 36, 9], [48, 16, 36, 9, "_ref"], [48, 20, 36, 9], [48, 21, 36, 2, "linking"], [48, 28, 36, 9], [49, 6, 36, 9, "_ref$fallback"], [49, 19, 36, 9], [49, 22, 36, 9, "_ref"], [49, 26, 36, 9], [49, 27, 37, 2, "fallback"], [49, 35, 37, 10], [50, 6, 37, 2, "fallback"], [50, 14, 37, 10], [50, 17, 37, 10, "_ref$fallback"], [50, 30, 37, 10], [50, 44, 37, 13], [50, 48, 37, 17], [50, 51, 37, 17, "_ref$fallback"], [50, 64, 37, 17], [51, 6, 38, 2, "documentTitle"], [51, 19, 38, 15], [51, 22, 38, 15, "_ref"], [51, 26, 38, 15], [51, 27, 38, 2, "documentTitle"], [51, 40, 38, 15], [52, 6, 39, 2, "onReady"], [52, 13, 39, 9], [52, 16, 39, 9, "_ref"], [52, 20, 39, 9], [52, 21, 39, 2, "onReady"], [52, 28, 39, 9], [53, 6, 40, 2, "onStateChange"], [53, 19, 40, 15], [53, 22, 40, 15, "_ref"], [53, 26, 40, 15], [53, 27, 40, 2, "onStateChange"], [53, 40, 40, 15], [54, 6, 41, 5, "rest"], [54, 10, 41, 9], [54, 17, 41, 9, "_objectWithoutProperties2"], [54, 42, 41, 9], [54, 43, 41, 9, "default"], [54, 50, 41, 9], [54, 52, 41, 9, "_ref"], [54, 56, 41, 9], [54, 58, 41, 9, "_excluded"], [54, 67, 41, 9], [55, 4, 43, 2], [55, 8, 43, 8, "isLinkingEnabled"], [55, 24, 43, 24], [55, 27, 43, 27, "linking"], [55, 34, 43, 34], [55, 37, 43, 37, "linking"], [55, 44, 43, 44], [55, 45, 43, 45, "enabled"], [55, 52, 43, 52], [55, 57, 43, 57], [55, 62, 43, 62], [55, 65, 43, 65], [55, 70, 43, 70], [56, 4, 44, 2], [56, 8, 44, 6, "linking"], [56, 15, 44, 13], [56, 17, 44, 15, "config"], [56, 23, 44, 21], [56, 25, 44, 23], [57, 6, 45, 4], [57, 10, 45, 4, "validatePathConfig"], [57, 34, 45, 22], [57, 36, 45, 23, "linking"], [57, 43, 45, 30], [57, 44, 45, 31, "config"], [57, 50, 45, 37], [57, 51, 45, 38], [58, 4, 46, 2], [59, 4, 47, 2], [59, 8, 47, 8, "ref<PERSON><PERSON><PERSON>"], [59, 20, 47, 20], [59, 23, 47, 23, "React"], [59, 28, 47, 28], [59, 29, 47, 29, "useRef"], [59, 35, 47, 35], [59, 36, 47, 36], [59, 40, 47, 40], [59, 41, 47, 41], [60, 4, 48, 2], [60, 8, 48, 2, "useBackButton"], [60, 36, 48, 15], [60, 38, 48, 16, "ref<PERSON><PERSON><PERSON>"], [60, 50, 48, 28], [60, 51, 48, 29], [61, 4, 49, 2], [61, 8, 49, 2, "useDocumentTitle"], [61, 42, 49, 18], [61, 44, 49, 19, "ref<PERSON><PERSON><PERSON>"], [61, 56, 49, 31], [61, 58, 49, 33, "documentTitle"], [61, 71, 49, 46], [61, 72, 49, 47], [62, 4, 50, 2], [62, 8, 50, 2, "_React$useState"], [62, 23, 50, 2], [62, 26, 50, 52, "React"], [62, 31, 50, 57], [62, 32, 50, 58, "useState"], [62, 40, 50, 66], [62, 41, 50, 67], [62, 42, 50, 68], [63, 6, 50, 68, "_React$useState2"], [63, 22, 50, 68], [63, 29, 50, 68, "_slicedToArray2"], [63, 44, 50, 68], [63, 45, 50, 68, "default"], [63, 52, 50, 68], [63, 54, 50, 68, "_React$useState"], [63, 69, 50, 68], [64, 6, 50, 9, "lastUnhandledLink"], [64, 23, 50, 26], [64, 26, 50, 26, "_React$useState2"], [64, 42, 50, 26], [65, 6, 50, 28, "setLastUnhandledLink"], [65, 26, 50, 48], [65, 29, 50, 48, "_React$useState2"], [65, 45, 50, 48], [66, 4, 51, 2], [66, 8, 51, 2, "_useLinking"], [66, 19, 51, 2], [66, 22, 53, 6], [66, 26, 53, 6, "useLinking"], [66, 49, 53, 16], [66, 51, 53, 17, "ref<PERSON><PERSON><PERSON>"], [66, 63, 53, 29], [66, 65, 53, 31], [67, 8, 54, 4, "enabled"], [67, 15, 54, 11], [67, 17, 54, 13, "isLinkingEnabled"], [67, 33, 54, 29], [68, 8, 55, 4, "prefixes"], [68, 16, 55, 12], [68, 18, 55, 14], [68, 20, 55, 16], [69, 8, 56, 4], [69, 11, 56, 7, "linking"], [70, 6, 57, 2], [70, 7, 57, 3], [70, 9, 57, 5, "setLastUnhandledLink"], [70, 29, 57, 25], [70, 30, 57, 26], [71, 6, 52, 4, "getInitialState"], [71, 21, 52, 19], [71, 24, 52, 19, "_useLinking"], [71, 35, 52, 19], [71, 36, 52, 4, "getInitialState"], [71, 51, 52, 19], [72, 4, 58, 2], [72, 8, 58, 8, "linkingContext"], [72, 22, 58, 22], [72, 25, 58, 25, "React"], [72, 30, 58, 30], [72, 31, 58, 31, "useMemo"], [72, 38, 58, 38], [72, 39, 58, 39], [72, 46, 58, 46], [73, 6, 59, 4, "options"], [73, 13, 59, 11], [73, 15, 59, 13, "linking"], [74, 4, 60, 2], [74, 5, 60, 3], [74, 6, 60, 4], [74, 8, 60, 6], [74, 9, 60, 7, "linking"], [74, 16, 60, 14], [74, 17, 60, 15], [74, 18, 60, 16], [75, 4, 61, 2], [75, 8, 61, 8, "unhandledLinkingContext"], [75, 31, 61, 31], [75, 34, 61, 34, "React"], [75, 39, 61, 39], [75, 40, 61, 40, "useMemo"], [75, 47, 61, 47], [75, 48, 61, 48], [75, 55, 61, 55], [76, 6, 62, 4, "lastUnhandledLink"], [76, 23, 62, 21], [77, 6, 63, 4, "setLastUnhandledLink"], [78, 4, 64, 2], [78, 5, 64, 3], [78, 6, 64, 4], [78, 8, 64, 6], [78, 9, 64, 7, "lastUnhandledLink"], [78, 26, 64, 24], [78, 28, 64, 26, "setLastUnhandledLink"], [78, 48, 64, 46], [78, 49, 64, 47], [78, 50, 64, 48], [79, 4, 65, 2], [79, 8, 65, 8, "onReadyForLinkingHandling"], [79, 33, 65, 33], [79, 36, 65, 36], [79, 40, 65, 36, "useLatestCallback"], [79, 66, 65, 53], [79, 68, 65, 54], [79, 74, 65, 60], [80, 6, 66, 4], [81, 6, 67, 4], [81, 10, 67, 10, "path"], [81, 14, 67, 14], [81, 17, 67, 17, "ref<PERSON><PERSON><PERSON>"], [81, 29, 67, 29], [81, 30, 67, 30, "current"], [81, 37, 67, 37], [81, 39, 67, 39, "getCurrentRoute"], [81, 54, 67, 54], [81, 55, 67, 55], [81, 56, 67, 56], [81, 58, 67, 58, "path"], [81, 62, 67, 62], [82, 6, 68, 4, "setLastUnhandledLink"], [82, 26, 68, 24], [82, 27, 68, 25, "previousLastUnhandledLink"], [82, 52, 68, 50], [82, 56, 68, 54], [83, 8, 69, 6], [83, 12, 69, 10, "previousLastUnhandledLink"], [83, 37, 69, 35], [83, 42, 69, 40, "path"], [83, 46, 69, 44], [83, 48, 69, 46], [84, 10, 70, 8], [84, 17, 70, 15, "undefined"], [84, 26, 70, 24], [85, 8, 71, 6], [86, 8, 72, 6], [86, 15, 72, 13, "previousLastUnhandledLink"], [86, 40, 72, 38], [87, 6, 73, 4], [87, 7, 73, 5], [87, 8, 73, 6], [88, 6, 74, 4, "onReady"], [88, 13, 74, 11], [88, 16, 74, 14], [88, 17, 74, 15], [89, 4, 75, 2], [89, 5, 75, 3], [89, 6, 75, 4], [90, 4, 76, 2], [90, 8, 76, 8, "onStateChangeForLinkingHandling"], [90, 39, 76, 39], [90, 42, 76, 42], [90, 46, 76, 42, "useLatestCallback"], [90, 72, 76, 59], [90, 74, 76, 60, "state"], [90, 79, 76, 65], [90, 83, 76, 69], [91, 6, 77, 4], [92, 6, 78, 4], [92, 10, 78, 10, "path"], [92, 14, 78, 14], [92, 17, 78, 17, "ref<PERSON><PERSON><PERSON>"], [92, 29, 78, 29], [92, 30, 78, 30, "current"], [92, 37, 78, 37], [92, 39, 78, 39, "getCurrentRoute"], [92, 54, 78, 54], [92, 55, 78, 55], [92, 56, 78, 56], [92, 58, 78, 58, "path"], [92, 62, 78, 62], [93, 6, 79, 4, "setLastUnhandledLink"], [93, 26, 79, 24], [93, 27, 79, 25, "previousLastUnhandledLink"], [93, 52, 79, 50], [93, 56, 79, 54], [94, 8, 80, 6], [94, 12, 80, 10, "previousLastUnhandledLink"], [94, 37, 80, 35], [94, 42, 80, 40, "path"], [94, 46, 80, 44], [94, 48, 80, 46], [95, 10, 81, 8], [95, 17, 81, 15, "undefined"], [95, 26, 81, 24], [96, 8, 82, 6], [97, 8, 83, 6], [97, 15, 83, 13, "previousLastUnhandledLink"], [97, 40, 83, 38], [98, 6, 84, 4], [98, 7, 84, 5], [98, 8, 84, 6], [99, 6, 85, 4, "onStateChange"], [99, 19, 85, 17], [99, 22, 85, 20, "state"], [99, 27, 85, 25], [99, 28, 85, 26], [100, 4, 86, 2], [100, 5, 86, 3], [100, 6, 86, 4], [101, 4, 87, 2], [102, 4, 88, 2], [103, 4, 89, 2, "React"], [103, 9, 89, 7], [103, 10, 89, 8, "useEffect"], [103, 19, 89, 17], [103, 20, 89, 18], [103, 26, 89, 24], [104, 6, 90, 4], [104, 10, 90, 8, "ref<PERSON><PERSON><PERSON>"], [104, 22, 90, 20], [104, 23, 90, 21, "current"], [104, 30, 90, 28], [104, 32, 90, 30], [105, 8, 91, 6, "REACT_NAVIGATION_DEVTOOLS"], [105, 33, 91, 31], [105, 34, 91, 32, "set"], [105, 37, 91, 35], [105, 38, 91, 36, "ref<PERSON><PERSON><PERSON>"], [105, 50, 91, 48], [105, 51, 91, 49, "current"], [105, 58, 91, 56], [105, 60, 91, 58], [106, 10, 92, 8], [106, 14, 92, 12, "linking"], [106, 21, 92, 19, "linking"], [106, 22, 92, 19], [106, 24, 92, 22], [107, 12, 93, 10], [107, 19, 93, 17], [108, 14, 94, 12], [108, 17, 94, 15, "linking"], [108, 24, 94, 22], [109, 14, 95, 12, "enabled"], [109, 21, 95, 19], [109, 23, 95, 21, "isLinkingEnabled"], [109, 39, 95, 37], [110, 14, 96, 12, "prefixes"], [110, 22, 96, 20], [110, 24, 96, 22, "linking"], [110, 31, 96, 29], [110, 33, 96, 31, "prefixes"], [110, 41, 96, 39], [110, 45, 96, 43], [110, 47, 96, 45], [111, 14, 97, 12, "getStateFromPath"], [111, 30, 97, 28], [111, 32, 97, 30, "linking"], [111, 39, 97, 37], [111, 41, 97, 39, "getStateFromPath"], [111, 57, 97, 55], [111, 61, 97, 59, "getStateFromPath"], [111, 83, 97, 75], [112, 14, 98, 12, "getPathFromState"], [112, 30, 98, 28], [112, 32, 98, 30, "linking"], [112, 39, 98, 37], [112, 41, 98, 39, "getPathFromState"], [112, 57, 98, 55], [112, 61, 98, 59, "getPathFromState"], [112, 83, 98, 75], [113, 14, 99, 12, "getActionFromState"], [113, 32, 99, 30], [113, 34, 99, 32, "linking"], [113, 41, 99, 39], [113, 43, 99, 41, "getActionFromState"], [113, 61, 99, 59], [113, 65, 99, 63, "getActionFromState"], [114, 12, 100, 10], [114, 13, 100, 11], [115, 10, 101, 8], [116, 8, 102, 6], [116, 9, 102, 7], [116, 10, 102, 8], [117, 6, 103, 4], [118, 4, 104, 2], [118, 5, 104, 3], [118, 6, 104, 4], [119, 4, 105, 2], [119, 8, 105, 2, "_useThenable"], [119, 20, 105, 2], [119, 23, 105, 37], [119, 27, 105, 37, "useThenable"], [119, 52, 105, 48], [119, 54, 105, 49, "getInitialState"], [119, 69, 105, 64], [119, 70, 105, 65], [120, 6, 105, 65, "_useThenable2"], [120, 19, 105, 65], [120, 26, 105, 65, "_slicedToArray2"], [120, 41, 105, 65], [120, 42, 105, 65, "default"], [120, 49, 105, 65], [120, 51, 105, 65, "_useThenable"], [120, 63, 105, 65], [121, 6, 105, 9, "isResolved"], [121, 16, 105, 19], [121, 19, 105, 19, "_useThenable2"], [121, 32, 105, 19], [122, 6, 105, 21, "initialState"], [122, 18, 105, 33], [122, 21, 105, 33, "_useThenable2"], [122, 34, 105, 33], [124, 4, 107, 2], [125, 4, 108, 2], [126, 4, 109, 2, "React"], [126, 9, 109, 7], [126, 10, 109, 8, "useImperativeHandle"], [126, 29, 109, 27], [126, 30, 109, 28, "ref"], [126, 33, 109, 31], [126, 35, 109, 33], [126, 41, 109, 39, "ref<PERSON><PERSON><PERSON>"], [126, 53, 109, 51], [126, 54, 109, 52, "current"], [126, 61, 109, 59], [126, 62, 109, 60], [127, 4, 110, 2], [127, 8, 110, 8, "isLinkingReady"], [127, 22, 110, 22], [127, 25, 110, 25, "rest"], [127, 29, 110, 29], [127, 30, 110, 30, "initialState"], [127, 42, 110, 42], [127, 46, 110, 46], [127, 50, 110, 50], [127, 54, 110, 54], [127, 55, 110, 55, "isLinkingEnabled"], [127, 71, 110, 71], [127, 75, 110, 75, "isResolved"], [127, 85, 110, 85], [128, 4, 111, 2], [128, 8, 111, 6], [128, 9, 111, 7, "isLinkingReady"], [128, 23, 111, 21], [128, 25, 111, 23], [129, 6, 112, 4], [129, 13, 112, 11], [129, 26, 112, 24], [129, 30, 112, 24, "_jsx"], [129, 45, 112, 28], [129, 47, 112, 29, "LocaleDirContext"], [129, 81, 112, 45], [129, 82, 112, 46, "Provider"], [129, 90, 112, 54], [129, 92, 112, 56], [130, 8, 113, 6, "value"], [130, 13, 113, 11], [130, 15, 113, 13, "direction"], [130, 24, 113, 22], [131, 8, 114, 6, "children"], [131, 16, 114, 14], [131, 18, 114, 16], [131, 31, 114, 29], [131, 35, 114, 29, "_jsx"], [131, 50, 114, 33], [131, 52, 114, 34, "ThemeProvider"], [131, 71, 114, 47], [131, 73, 114, 49], [132, 10, 115, 8, "value"], [132, 15, 115, 13], [132, 17, 115, 15, "theme"], [132, 22, 115, 20], [133, 10, 116, 8, "children"], [133, 18, 116, 16], [133, 20, 116, 18, "fallback"], [134, 8, 117, 6], [134, 9, 117, 7], [135, 6, 118, 4], [135, 7, 118, 5], [135, 8, 118, 6], [136, 4, 119, 2], [137, 4, 120, 2], [137, 11, 120, 9], [137, 24, 120, 22], [137, 28, 120, 22, "_jsx"], [137, 43, 120, 26], [137, 45, 120, 27, "LocaleDirContext"], [137, 79, 120, 43], [137, 80, 120, 44, "Provider"], [137, 88, 120, 52], [137, 90, 120, 54], [138, 6, 121, 4, "value"], [138, 11, 121, 9], [138, 13, 121, 11, "direction"], [138, 22, 121, 20], [139, 6, 122, 4, "children"], [139, 14, 122, 12], [139, 16, 122, 14], [139, 29, 122, 27], [139, 33, 122, 27, "_jsx"], [139, 48, 122, 31], [139, 50, 122, 32, "UnhandledLinkingContext"], [139, 98, 122, 55], [139, 99, 122, 56, "Provider"], [139, 107, 122, 64], [139, 109, 122, 66], [140, 8, 123, 6, "value"], [140, 13, 123, 11], [140, 15, 123, 13, "unhandledLinkingContext"], [140, 38, 123, 36], [141, 8, 124, 6, "children"], [141, 16, 124, 14], [141, 18, 124, 16], [141, 31, 124, 29], [141, 35, 124, 29, "_jsx"], [141, 50, 124, 33], [141, 52, 124, 34, "LinkingContext"], [141, 82, 124, 48], [141, 83, 124, 49, "Provider"], [141, 91, 124, 57], [141, 93, 124, 59], [142, 10, 125, 8, "value"], [142, 15, 125, 13], [142, 17, 125, 15, "linkingContext"], [142, 31, 125, 29], [143, 10, 126, 8, "children"], [143, 18, 126, 16], [143, 20, 126, 18], [143, 33, 126, 31], [143, 37, 126, 31, "_jsx"], [143, 52, 126, 35], [143, 54, 126, 36, "BaseNavigationContainer"], [143, 83, 126, 59], [143, 85, 126, 61], [144, 12, 127, 10], [144, 15, 127, 13, "rest"], [144, 19, 127, 17], [145, 12, 128, 10, "theme"], [145, 17, 128, 15], [145, 19, 128, 17, "theme"], [145, 24, 128, 22], [146, 12, 129, 10, "onReady"], [146, 19, 129, 17], [146, 21, 129, 19, "onReadyForLinkingHandling"], [146, 46, 129, 44], [147, 12, 130, 10, "onStateChange"], [147, 25, 130, 23], [147, 27, 130, 25, "onStateChangeForLinkingHandling"], [147, 58, 130, 56], [148, 12, 131, 10, "initialState"], [148, 24, 131, 22], [148, 26, 131, 24, "rest"], [148, 30, 131, 28], [148, 31, 131, 29, "initialState"], [148, 43, 131, 41], [148, 47, 131, 45], [148, 51, 131, 49], [148, 54, 131, 52, "initialState"], [148, 66, 131, 64], [148, 69, 131, 67, "rest"], [148, 73, 131, 71], [148, 74, 131, 72, "initialState"], [148, 86, 131, 84], [149, 12, 132, 10, "ref"], [149, 15, 132, 13], [149, 17, 132, 15, "ref<PERSON><PERSON><PERSON>"], [150, 10, 133, 8], [150, 11, 133, 9], [151, 8, 134, 6], [151, 9, 134, 7], [152, 6, 135, 4], [152, 7, 135, 5], [153, 4, 136, 2], [153, 5, 136, 3], [153, 6, 136, 4], [154, 2, 137, 0], [155, 2, 138, 7], [155, 6, 138, 13, "NavigationContainer"], [155, 25, 138, 32], [155, 28, 138, 32, "exports"], [155, 35, 138, 32], [155, 36, 138, 32, "NavigationContainer"], [155, 55, 138, 32], [155, 58, 138, 35], [155, 71, 138, 48, "React"], [155, 76, 138, 53], [155, 77, 138, 54, "forwardRef"], [155, 87, 138, 64], [155, 88, 138, 65, "NavigationContainerInner"], [155, 112, 138, 89], [155, 113, 138, 90], [156, 0, 138, 91], [156, 3]], "functionMap": {"names": ["<global>", "NavigationContainerInner", "React.useMemo$argument_0", "useLatestCallback$argument_0", "setLastUnhandledLink$argument_0", "React.useEffect$argument_0", "REACT_NAVIGATION_DEVTOOLS.set$argument_1.get__linking", "React.useImperativeHandle$argument_1"], "mappings": "AAA;ACgC;uCCyB;IDE;gDCC;IDG;sDEC;yBCG;KDK;GFE;4DEC;yBCG;KDK;GFE;kBIG;QCG;SDS;GJG;iCMK,0BN;CD4B"}}, "type": "js/module"}]}