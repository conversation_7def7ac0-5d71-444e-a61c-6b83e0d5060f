{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 42, "index": 56}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../animation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 58}, "end": {"line": 4, "column": 49, "index": 107}}], "key": "a6n75g9KQy+KnMEjz15YzADQ7Hw=", "exportNames": ["*"]}}, {"name": "../core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 176}, "end": {"line": 6, "column": 63, "index": 239}}], "key": "OSA8xsmyvVLjxZOJ/QFvle2ua2I=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 240}, "end": {"line": 7, "column": 52, "index": 292}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDerivedValue = useDerivedValue;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _animation = require(_dependencyMap[1], \"../animation\");\n  var _core = require(_dependencyMap[2], \"../core\");\n  var _PlatformChecker = require(_dependencyMap[3], \"../PlatformChecker\");\n  /**\n   * Lets you create new shared values based on existing ones while keeping them\n   * reactive.\n   *\n   * @param updater - A function called whenever at least one of the shared values\n   *   or state used in the function body changes.\n   * @param dependencies - An optional array of dependencies. Only relevant when\n   *   using Reanimated without the Babel plugin on the Web.\n   * @returns A new readonly shared value based on a value returned from the\n   *   updater function\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useDerivedValue\n   */\n  // @ts-expect-error This overload is required by our API.\n  var _worklet_7329842932899_init_data = {\n    code: \"function useDerivedValueTs1(){const{sharedValue,updater}=this.__closure;sharedValue.value=updater();}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\hook\\\\useDerivedValue.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useDerivedValueTs1\\\",\\\"sharedValue\\\",\\\"updater\\\",\\\"__closure\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/hook/useDerivedValue.ts\\\"],\\\"mappings\\\":\\\"AAgEgB,SAAAA,kBAAMA,CAAA,QAAAC,WAAA,CAAAC,OAAA,OAAAC,SAAA,CAEhBF,WAAW,CAACG,KAAK,CAAGF,OAAO,CAAC,CAAC,CAC/B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function useDerivedValue(updater, dependencies) {\n    var initRef = (0, _react.useRef)(null);\n    var inputs = Object.values(updater.__closure ?? {});\n    if ((0, _PlatformChecker.shouldBeUseWeb)()) {\n      if (!inputs.length && dependencies?.length) {\n        // let web work without a Babel/SWC plugin\n        inputs = dependencies;\n      }\n    }\n\n    // build dependencies\n    if (dependencies === undefined) {\n      dependencies = [...inputs, updater.__workletHash];\n    } else {\n      dependencies.push(updater.__workletHash);\n    }\n    if (initRef.current === null) {\n      initRef.current = (0, _core.makeMutable)((0, _animation.initialUpdaterRun)(updater));\n    }\n    var sharedValue = initRef.current;\n    (0, _react.useEffect)(() => {\n      var fun = function () {\n        var _e = [new global.Error(), -3, -27];\n        var useDerivedValueTs1 = function () {\n          sharedValue.value = updater();\n        };\n        useDerivedValueTs1.__closure = {\n          sharedValue,\n          updater\n        };\n        useDerivedValueTs1.__workletHash = 7329842932899;\n        useDerivedValueTs1.__initData = _worklet_7329842932899_init_data;\n        useDerivedValueTs1.__stackDetails = _e;\n        return useDerivedValueTs1;\n      }();\n      var mapperId = (0, _core.startMapper)(fun, inputs, [sharedValue]);\n      return () => {\n        (0, _core.stopMapper)(mapperId);\n      };\n    }, dependencies);\n    return sharedValue;\n  }\n});", "lineCount": 73, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useDerivedValue"], [7, 25, 1, 13], [7, 28, 1, 13, "useDerivedValue"], [7, 43, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_animation"], [9, 16, 4, 0], [9, 19, 4, 0, "require"], [9, 26, 4, 0], [9, 27, 4, 0, "_dependencyMap"], [9, 41, 4, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_core"], [10, 11, 6, 0], [10, 14, 6, 0, "require"], [10, 21, 6, 0], [10, 22, 6, 0, "_dependencyMap"], [10, 36, 6, 0], [11, 2, 7, 0], [11, 6, 7, 0, "_PlatformChecker"], [11, 22, 7, 0], [11, 25, 7, 0, "require"], [11, 32, 7, 0], [11, 33, 7, 0, "_dependencyMap"], [11, 47, 7, 0], [12, 2, 20, 0], [13, 0, 21, 0], [14, 0, 22, 0], [15, 0, 23, 0], [16, 0, 24, 0], [17, 0, 25, 0], [18, 0, 26, 0], [19, 0, 27, 0], [20, 0, 28, 0], [21, 0, 29, 0], [22, 0, 30, 0], [23, 0, 31, 0], [24, 2, 32, 0], [25, 2, 32, 0], [25, 6, 32, 0, "_worklet_7329842932899_init_data"], [25, 38, 32, 0], [26, 4, 32, 0, "code"], [26, 8, 32, 0], [27, 4, 32, 0, "location"], [27, 12, 32, 0], [28, 4, 32, 0, "sourceMap"], [28, 13, 32, 0], [29, 4, 32, 0, "version"], [29, 11, 32, 0], [30, 2, 32, 0], [31, 2, 38, 7], [31, 11, 38, 16, "useDerivedValue"], [31, 26, 38, 31, "useDerivedValue"], [31, 27, 39, 2, "updater"], [31, 34, 39, 37], [31, 36, 40, 2, "dependencies"], [31, 48, 40, 31], [31, 50, 41, 23], [32, 4, 42, 2], [32, 8, 42, 8, "initRef"], [32, 15, 42, 15], [32, 18, 42, 18], [32, 22, 42, 18, "useRef"], [32, 35, 42, 24], [32, 37, 42, 52], [32, 41, 42, 56], [32, 42, 42, 57], [33, 4, 43, 2], [33, 8, 43, 6, "inputs"], [33, 14, 43, 12], [33, 17, 43, 15, "Object"], [33, 23, 43, 21], [33, 24, 43, 22, "values"], [33, 30, 43, 28], [33, 31, 43, 29, "updater"], [33, 38, 43, 36], [33, 39, 43, 37, "__closure"], [33, 48, 43, 46], [33, 52, 43, 50], [33, 53, 43, 51], [33, 54, 43, 52], [33, 55, 43, 53], [34, 4, 44, 2], [34, 8, 44, 6], [34, 12, 44, 6, "shouldBeUseWeb"], [34, 43, 44, 20], [34, 45, 44, 21], [34, 46, 44, 22], [34, 48, 44, 24], [35, 6, 45, 4], [35, 10, 45, 8], [35, 11, 45, 9, "inputs"], [35, 17, 45, 15], [35, 18, 45, 16, "length"], [35, 24, 45, 22], [35, 28, 45, 26, "dependencies"], [35, 40, 45, 38], [35, 42, 45, 40, "length"], [35, 48, 45, 46], [35, 50, 45, 48], [36, 8, 46, 6], [37, 8, 47, 6, "inputs"], [37, 14, 47, 12], [37, 17, 47, 15, "dependencies"], [37, 29, 47, 27], [38, 6, 48, 4], [39, 4, 49, 2], [41, 4, 51, 2], [42, 4, 52, 2], [42, 8, 52, 6, "dependencies"], [42, 20, 52, 18], [42, 25, 52, 23, "undefined"], [42, 34, 52, 32], [42, 36, 52, 34], [43, 6, 53, 4, "dependencies"], [43, 18, 53, 16], [43, 21, 53, 19], [43, 22, 53, 20], [43, 25, 53, 23, "inputs"], [43, 31, 53, 29], [43, 33, 53, 31, "updater"], [43, 40, 53, 38], [43, 41, 53, 39, "__workletHash"], [43, 54, 53, 52], [43, 55, 53, 53], [44, 4, 54, 2], [44, 5, 54, 3], [44, 11, 54, 9], [45, 6, 55, 4, "dependencies"], [45, 18, 55, 16], [45, 19, 55, 17, "push"], [45, 23, 55, 21], [45, 24, 55, 22, "updater"], [45, 31, 55, 29], [45, 32, 55, 30, "__workletHash"], [45, 45, 55, 43], [45, 46, 55, 44], [46, 4, 56, 2], [47, 4, 58, 2], [47, 8, 58, 6, "initRef"], [47, 15, 58, 13], [47, 16, 58, 14, "current"], [47, 23, 58, 21], [47, 28, 58, 26], [47, 32, 58, 30], [47, 34, 58, 32], [48, 6, 59, 4, "initRef"], [48, 13, 59, 11], [48, 14, 59, 12, "current"], [48, 21, 59, 19], [48, 24, 59, 22], [48, 28, 59, 22, "makeMutable"], [48, 45, 59, 33], [48, 47, 59, 34], [48, 51, 59, 34, "initialUpdaterRun"], [48, 79, 59, 51], [48, 81, 59, 52, "updater"], [48, 88, 59, 59], [48, 89, 59, 60], [48, 90, 59, 61], [49, 4, 60, 2], [50, 4, 62, 2], [50, 8, 62, 8, "sharedValue"], [50, 19, 62, 39], [50, 22, 62, 42, "initRef"], [50, 29, 62, 49], [50, 30, 62, 50, "current"], [50, 37, 62, 57], [51, 4, 64, 2], [51, 8, 64, 2, "useEffect"], [51, 24, 64, 11], [51, 26, 64, 12], [51, 32, 64, 18], [52, 6, 65, 4], [52, 10, 65, 10, "fun"], [52, 13, 65, 13], [52, 16, 65, 16], [53, 8, 65, 16], [53, 12, 65, 16, "_e"], [53, 14, 65, 16], [53, 22, 65, 16, "global"], [53, 28, 65, 16], [53, 29, 65, 16, "Error"], [53, 34, 65, 16], [54, 8, 65, 16], [54, 12, 65, 16, "useDerivedValueTs1"], [54, 30, 65, 16], [54, 42, 65, 16, "useDerivedValueTs1"], [54, 43, 65, 16], [54, 45, 65, 22], [55, 10, 67, 6, "sharedValue"], [55, 21, 67, 17], [55, 22, 67, 18, "value"], [55, 27, 67, 23], [55, 30, 67, 26, "updater"], [55, 37, 67, 33], [55, 38, 67, 34], [55, 39, 67, 35], [56, 8, 68, 4], [56, 9, 68, 5], [57, 8, 68, 5, "useDerivedValueTs1"], [57, 26, 68, 5], [57, 27, 68, 5, "__closure"], [57, 36, 68, 5], [58, 10, 68, 5, "sharedValue"], [58, 21, 68, 5], [59, 10, 68, 5, "updater"], [60, 8, 68, 5], [61, 8, 68, 5, "useDerivedValueTs1"], [61, 26, 68, 5], [61, 27, 68, 5, "__workletHash"], [61, 40, 68, 5], [62, 8, 68, 5, "useDerivedValueTs1"], [62, 26, 68, 5], [62, 27, 68, 5, "__initData"], [62, 37, 68, 5], [62, 40, 68, 5, "_worklet_7329842932899_init_data"], [62, 72, 68, 5], [63, 8, 68, 5, "useDerivedValueTs1"], [63, 26, 68, 5], [63, 27, 68, 5, "__stackDetails"], [63, 41, 68, 5], [63, 44, 68, 5, "_e"], [63, 46, 68, 5], [64, 8, 68, 5], [64, 15, 68, 5, "useDerivedValueTs1"], [64, 33, 68, 5], [65, 6, 68, 5], [65, 7, 65, 16], [65, 9, 68, 5], [66, 6, 69, 4], [66, 10, 69, 10, "mapperId"], [66, 18, 69, 18], [66, 21, 69, 21], [66, 25, 69, 21, "startMapper"], [66, 42, 69, 32], [66, 44, 69, 33, "fun"], [66, 47, 69, 36], [66, 49, 69, 38, "inputs"], [66, 55, 69, 44], [66, 57, 69, 46], [66, 58, 70, 6, "sharedValue"], [66, 69, 70, 17], [66, 70, 71, 5], [66, 71, 71, 6], [67, 6, 72, 4], [67, 13, 72, 11], [67, 19, 72, 17], [68, 8, 73, 6], [68, 12, 73, 6, "stopMapper"], [68, 28, 73, 16], [68, 30, 73, 17, "mapperId"], [68, 38, 73, 25], [68, 39, 73, 26], [69, 6, 74, 4], [69, 7, 74, 5], [70, 4, 75, 2], [70, 5, 75, 3], [70, 7, 75, 5, "dependencies"], [70, 19, 75, 17], [70, 20, 75, 18], [71, 4, 77, 2], [71, 11, 77, 9, "sharedValue"], [71, 22, 77, 20], [72, 2, 78, 0], [73, 0, 78, 1], [73, 3]], "functionMap": {"names": ["<global>", "useDerivedValue", "useEffect$argument_0", "fun", "<anonymous>"], "mappings": "AAA;OCqC;YC0B;gBCC;KDG;WEI;KFE;GDC;CDG"}}, "type": "js/module"}]}