{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 49, "index": 49}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.RNSScreensRefContext = exports.GHContext = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[2], \"react/jsx-dev-runtime\");\n  var GHContext = exports.GHContext = /*#__PURE__*/_react.default.createContext(props => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n    children: props.children\n  }, void 0, false));\n  var RNSScreensRefContext = exports.RNSScreensRefContext = /*#__PURE__*/_react.default.createContext(null);\n});", "lineCount": 13, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 1, 49], [8, 6, 1, 49, "_jsxDevRuntime"], [8, 20, 1, 49], [8, 23, 1, 49, "require"], [8, 30, 1, 49], [8, 31, 1, 49, "_dependencyMap"], [8, 45, 1, 49], [9, 2, 4, 7], [9, 6, 4, 13, "GHContext"], [9, 15, 4, 22], [9, 18, 4, 22, "exports"], [9, 25, 4, 22], [9, 26, 4, 22, "GHContext"], [9, 35, 4, 22], [9, 51, 4, 25, "React"], [9, 65, 4, 30], [9, 66, 4, 31, "createContext"], [9, 79, 4, 44], [9, 80, 5, 3, "props"], [9, 85, 5, 49], [9, 102, 5, 54], [9, 106, 5, 54, "_jsxDevRuntime"], [9, 120, 5, 54], [9, 121, 5, 54, "jsxDEV"], [9, 127, 5, 54], [9, 129, 5, 54, "_jsxDevRuntime"], [9, 143, 5, 54], [9, 144, 5, 54, "Fragment"], [9, 152, 5, 54], [10, 4, 5, 54, "children"], [10, 12, 5, 54], [10, 14, 5, 57, "props"], [10, 19, 5, 62], [10, 20, 5, 63, "children"], [11, 2, 5, 71], [11, 18, 5, 74], [11, 19, 6, 0], [11, 20, 6, 1], [12, 2, 8, 7], [12, 6, 8, 13, "RNSScreensRefContext"], [12, 26, 8, 33], [12, 29, 8, 33, "exports"], [12, 36, 8, 33], [12, 37, 8, 33, "RNSScreensRefContext"], [12, 57, 8, 33], [12, 73, 9, 2, "React"], [12, 87, 9, 7], [12, 88, 9, 8, "createContext"], [12, 101, 9, 21], [12, 102, 9, 72], [12, 106, 9, 76], [12, 107, 9, 77], [13, 0, 9, 78], [13, 3]], "functionMap": {"names": ["<global>", "React.createContext$argument_0"], "mappings": "AAA;ECI,yED"}}, "type": "js/module"}]}