{"dependencies": [{"name": "../../../commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 59, "index": 73}}], "key": "TwVv6Lmo2X59rY13ef3nPCsFe9I=", "exportNames": ["*"]}}, {"name": "../Easing.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 322}, "end": {"line": 7, "column": 48, "index": 370}}], "key": "o8Otr+7xxI91ztdMofacNf+fWlw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.CurvedTransition = CurvedTransition;\n  exports.prepareCurvedTransition = prepareCurvedTransition;\n  var _commonTypes = require(_dependencyMap[0], \"../../../commonTypes\");\n  var _Easing = require(_dependencyMap[1], \"../Easing.web\");\n  function resetStyle(component) {\n    component.style.animationName = ''; // This line prevents unwanted entering animation\n    component.style.position = 'absolute';\n    component.style.top = '0px';\n    component.style.left = '0px';\n    component.style.margin = '0px';\n    component.style.width = '100%';\n    component.style.height = '100%';\n  }\n  function showChildren(parent, childrenDisplayProperty, shouldShow) {\n    for (var i = 0; i < parent.children.length; ++i) {\n      var child = parent.children[i];\n      if (shouldShow) {\n        child.style.display = childrenDisplayProperty.get(child);\n      } else {\n        childrenDisplayProperty.set(child, child.style.display);\n        child.style.display = 'none';\n      }\n    }\n  }\n  function prepareParent(element, dummy, animationConfig, transitionData) {\n    // Adjust configs for `CurvedTransition` and create config object for dummy\n    animationConfig.easing = (0, _Easing.getEasingByName)(transitionData.easingX);\n    var childrenDisplayProperty = new Map();\n    showChildren(element, childrenDisplayProperty, false);\n    var originalBackgroundColor = element.style.backgroundColor;\n    element.style.backgroundColor = 'transparent';\n    var onFinalize = () => {\n      if (element.contains(dummy)) {\n        element.removeChild(dummy);\n      }\n      showChildren(element, childrenDisplayProperty, true);\n      element.style.backgroundColor = originalBackgroundColor;\n    };\n    var animationCancelCallback = () => {\n      onFinalize();\n      element.removeEventListener('animationcancel', animationCancelCallback);\n    };\n    var animationEndCallback = () => {\n      onFinalize();\n      element.removeEventListener('animationend', animationEndCallback);\n    };\n    element.addEventListener('animationend', animationEndCallback);\n    element.addEventListener('animationcancel', animationCancelCallback);\n    element.appendChild(dummy);\n  }\n  function prepareDummy(element, animationConfig, transitionData, dummyTransitionKeyframeName) {\n    var dummyAnimationConfig = {\n      animationName: dummyTransitionKeyframeName,\n      animationType: _commonTypes.LayoutAnimationType.LAYOUT,\n      duration: animationConfig.duration,\n      delay: animationConfig.delay,\n      easing: (0, _Easing.getEasingByName)(transitionData.easingY),\n      callback: null,\n      reversed: false\n    };\n    var dummy = element.cloneNode(true);\n    resetStyle(dummy);\n    return {\n      dummy,\n      dummyAnimationConfig\n    };\n  }\n  function prepareCurvedTransition(element, animationConfig, transitionData, dummyTransitionKeyframeName) {\n    var _prepareDummy = prepareDummy(element, animationConfig, transitionData, dummyTransitionKeyframeName),\n      dummy = _prepareDummy.dummy,\n      dummyAnimationConfig = _prepareDummy.dummyAnimationConfig;\n    prepareParent(element, dummy, animationConfig, transitionData);\n    return {\n      dummy,\n      dummyAnimationConfig\n    };\n  }\n  function CurvedTransition(keyframeXName, keyframeYName, transitionData) {\n    var keyframeXObj = {\n      name: keyframeXName,\n      style: {\n        0: {\n          transform: [{\n            translateX: `${transitionData.translateX}px`,\n            scale: `${transitionData.scaleX},${transitionData.scaleY}`\n          }]\n        }\n      },\n      duration: 300\n    };\n    var keyframeYObj = {\n      name: keyframeYName,\n      style: {\n        0: {\n          transform: [{\n            translateY: `${transitionData.translateY}px`,\n            scale: `${transitionData.scaleX},${transitionData.scaleY}`\n          }]\n        }\n      },\n      duration: 300\n    };\n    return {\n      firstKeyframeObj: keyframeXObj,\n      secondKeyframeObj: keyframeYObj\n    };\n  }\n});", "lineCount": 114, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "CurvedTransition"], [7, 26, 1, 13], [7, 29, 1, 13, "CurvedTransition"], [7, 45, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "prepareCurvedTransition"], [8, 33, 1, 13], [8, 36, 1, 13, "prepareCurvedTransition"], [8, 59, 1, 13], [9, 2, 2, 0], [9, 6, 2, 0, "_commonTypes"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 7, 0], [10, 6, 7, 0, "_Easing"], [10, 13, 7, 0], [10, 16, 7, 0, "require"], [10, 23, 7, 0], [10, 24, 7, 0, "_dependencyMap"], [10, 38, 7, 0], [11, 2, 9, 0], [11, 11, 9, 9, "resetStyle"], [11, 21, 9, 19, "resetStyle"], [11, 22, 9, 20, "component"], [11, 31, 9, 42], [11, 33, 9, 44], [12, 4, 10, 2, "component"], [12, 13, 10, 11], [12, 14, 10, 12, "style"], [12, 19, 10, 17], [12, 20, 10, 18, "animationName"], [12, 33, 10, 31], [12, 36, 10, 34], [12, 38, 10, 36], [12, 39, 10, 37], [12, 40, 10, 38], [13, 4, 11, 2, "component"], [13, 13, 11, 11], [13, 14, 11, 12, "style"], [13, 19, 11, 17], [13, 20, 11, 18, "position"], [13, 28, 11, 26], [13, 31, 11, 29], [13, 41, 11, 39], [14, 4, 12, 2, "component"], [14, 13, 12, 11], [14, 14, 12, 12, "style"], [14, 19, 12, 17], [14, 20, 12, 18, "top"], [14, 23, 12, 21], [14, 26, 12, 24], [14, 31, 12, 29], [15, 4, 13, 2, "component"], [15, 13, 13, 11], [15, 14, 13, 12, "style"], [15, 19, 13, 17], [15, 20, 13, 18, "left"], [15, 24, 13, 22], [15, 27, 13, 25], [15, 32, 13, 30], [16, 4, 14, 2, "component"], [16, 13, 14, 11], [16, 14, 14, 12, "style"], [16, 19, 14, 17], [16, 20, 14, 18, "margin"], [16, 26, 14, 24], [16, 29, 14, 27], [16, 34, 14, 32], [17, 4, 15, 2, "component"], [17, 13, 15, 11], [17, 14, 15, 12, "style"], [17, 19, 15, 17], [17, 20, 15, 18, "width"], [17, 25, 15, 23], [17, 28, 15, 26], [17, 34, 15, 32], [18, 4, 16, 2, "component"], [18, 13, 16, 11], [18, 14, 16, 12, "style"], [18, 19, 16, 17], [18, 20, 16, 18, "height"], [18, 26, 16, 24], [18, 29, 16, 27], [18, 35, 16, 33], [19, 2, 17, 0], [20, 2, 19, 0], [20, 11, 19, 9, "showChildren"], [20, 23, 19, 21, "showChildren"], [20, 24, 20, 2, "parent"], [20, 30, 20, 21], [20, 32, 21, 2, "childrenDisplayProperty"], [20, 55, 21, 51], [20, 57, 22, 2, "shouldShow"], [20, 67, 22, 21], [20, 69, 23, 2], [21, 4, 24, 2], [21, 9, 24, 7], [21, 13, 24, 11, "i"], [21, 14, 24, 12], [21, 17, 24, 15], [21, 18, 24, 16], [21, 20, 24, 18, "i"], [21, 21, 24, 19], [21, 24, 24, 22, "parent"], [21, 30, 24, 28], [21, 31, 24, 29, "children"], [21, 39, 24, 37], [21, 40, 24, 38, "length"], [21, 46, 24, 44], [21, 48, 24, 46], [21, 50, 24, 48, "i"], [21, 51, 24, 49], [21, 53, 24, 51], [22, 6, 25, 4], [22, 10, 25, 10, "child"], [22, 15, 25, 15], [22, 18, 25, 18, "parent"], [22, 24, 25, 24], [22, 25, 25, 25, "children"], [22, 33, 25, 33], [22, 34, 25, 34, "i"], [22, 35, 25, 35], [22, 36, 25, 51], [23, 6, 27, 4], [23, 10, 27, 8, "shouldShow"], [23, 20, 27, 18], [23, 22, 27, 20], [24, 8, 28, 6, "child"], [24, 13, 28, 11], [24, 14, 28, 12, "style"], [24, 19, 28, 17], [24, 20, 28, 18, "display"], [24, 27, 28, 25], [24, 30, 28, 28, "childrenDisplayProperty"], [24, 53, 28, 51], [24, 54, 28, 52, "get"], [24, 57, 28, 55], [24, 58, 28, 56, "child"], [24, 63, 28, 61], [24, 64, 28, 63], [25, 6, 29, 4], [25, 7, 29, 5], [25, 13, 29, 11], [26, 8, 30, 6, "childrenDisplayProperty"], [26, 31, 30, 29], [26, 32, 30, 30, "set"], [26, 35, 30, 33], [26, 36, 30, 34, "child"], [26, 41, 30, 39], [26, 43, 30, 41, "child"], [26, 48, 30, 46], [26, 49, 30, 47, "style"], [26, 54, 30, 52], [26, 55, 30, 53, "display"], [26, 62, 30, 60], [26, 63, 30, 61], [27, 8, 31, 6, "child"], [27, 13, 31, 11], [27, 14, 31, 12, "style"], [27, 19, 31, 17], [27, 20, 31, 18, "display"], [27, 27, 31, 25], [27, 30, 31, 28], [27, 36, 31, 34], [28, 6, 32, 4], [29, 4, 33, 2], [30, 2, 34, 0], [31, 2, 36, 0], [31, 11, 36, 9, "prepareParent"], [31, 24, 36, 22, "prepareParent"], [31, 25, 37, 2, "element"], [31, 32, 37, 32], [31, 34, 38, 2, "dummy"], [31, 39, 38, 30], [31, 41, 39, 2, "animationConfig"], [31, 56, 39, 34], [31, 58, 40, 2, "transitionData"], [31, 72, 40, 32], [31, 74, 41, 2], [32, 4, 42, 2], [33, 4, 43, 2, "animationConfig"], [33, 19, 43, 17], [33, 20, 43, 18, "easing"], [33, 26, 43, 24], [33, 29, 43, 27], [33, 33, 43, 27, "getEasingByName"], [33, 56, 43, 42], [33, 58, 44, 4, "transitionData"], [33, 72, 44, 18], [33, 73, 44, 19, "easingX"], [33, 80, 45, 2], [33, 81, 45, 3], [34, 4, 47, 2], [34, 8, 47, 8, "childrenDisplayProperty"], [34, 31, 47, 31], [34, 34, 47, 34], [34, 38, 47, 38, "Map"], [34, 41, 47, 41], [34, 42, 47, 63], [34, 43, 47, 64], [35, 4, 48, 2, "showChildren"], [35, 16, 48, 14], [35, 17, 48, 15, "element"], [35, 24, 48, 22], [35, 26, 48, 24, "childrenDisplayProperty"], [35, 49, 48, 47], [35, 51, 48, 49], [35, 56, 48, 54], [35, 57, 48, 55], [36, 4, 50, 2], [36, 8, 50, 8, "originalBackgroundColor"], [36, 31, 50, 31], [36, 34, 50, 34, "element"], [36, 41, 50, 41], [36, 42, 50, 42, "style"], [36, 47, 50, 47], [36, 48, 50, 48, "backgroundColor"], [36, 63, 50, 63], [37, 4, 51, 2, "element"], [37, 11, 51, 9], [37, 12, 51, 10, "style"], [37, 17, 51, 15], [37, 18, 51, 16, "backgroundColor"], [37, 33, 51, 31], [37, 36, 51, 34], [37, 49, 51, 47], [38, 4, 53, 2], [38, 8, 53, 8, "onFinalize"], [38, 18, 53, 18], [38, 21, 53, 21, "onFinalize"], [38, 22, 53, 21], [38, 27, 53, 27], [39, 6, 54, 4], [39, 10, 54, 8, "element"], [39, 17, 54, 15], [39, 18, 54, 16, "contains"], [39, 26, 54, 24], [39, 27, 54, 25, "dummy"], [39, 32, 54, 30], [39, 33, 54, 31], [39, 35, 54, 33], [40, 8, 55, 6, "element"], [40, 15, 55, 13], [40, 16, 55, 14, "<PERSON><PERSON><PERSON><PERSON>"], [40, 27, 55, 25], [40, 28, 55, 26, "dummy"], [40, 33, 55, 31], [40, 34, 55, 32], [41, 6, 56, 4], [42, 6, 58, 4, "showChildren"], [42, 18, 58, 16], [42, 19, 58, 17, "element"], [42, 26, 58, 24], [42, 28, 58, 26, "childrenDisplayProperty"], [42, 51, 58, 49], [42, 53, 58, 51], [42, 57, 58, 55], [42, 58, 58, 56], [43, 6, 60, 4, "element"], [43, 13, 60, 11], [43, 14, 60, 12, "style"], [43, 19, 60, 17], [43, 20, 60, 18, "backgroundColor"], [43, 35, 60, 33], [43, 38, 60, 36, "originalBackgroundColor"], [43, 61, 60, 59], [44, 4, 61, 2], [44, 5, 61, 3], [45, 4, 63, 2], [45, 8, 63, 8, "animationCancelCallback"], [45, 31, 63, 31], [45, 34, 63, 34, "animationCancelCallback"], [45, 35, 63, 34], [45, 40, 63, 40], [46, 6, 64, 4, "onFinalize"], [46, 16, 64, 14], [46, 17, 64, 15], [46, 18, 64, 16], [47, 6, 65, 4, "element"], [47, 13, 65, 11], [47, 14, 65, 12, "removeEventListener"], [47, 33, 65, 31], [47, 34, 65, 32], [47, 51, 65, 49], [47, 53, 65, 51, "animationCancelCallback"], [47, 76, 65, 74], [47, 77, 65, 75], [48, 4, 66, 2], [48, 5, 66, 3], [49, 4, 68, 2], [49, 8, 68, 8, "animationEndCallback"], [49, 28, 68, 28], [49, 31, 68, 31, "animationEndCallback"], [49, 32, 68, 31], [49, 37, 68, 37], [50, 6, 69, 4, "onFinalize"], [50, 16, 69, 14], [50, 17, 69, 15], [50, 18, 69, 16], [51, 6, 70, 4, "element"], [51, 13, 70, 11], [51, 14, 70, 12, "removeEventListener"], [51, 33, 70, 31], [51, 34, 70, 32], [51, 48, 70, 46], [51, 50, 70, 48, "animationEndCallback"], [51, 70, 70, 68], [51, 71, 70, 69], [52, 4, 71, 2], [52, 5, 71, 3], [53, 4, 73, 2, "element"], [53, 11, 73, 9], [53, 12, 73, 10, "addEventListener"], [53, 28, 73, 26], [53, 29, 73, 27], [53, 43, 73, 41], [53, 45, 73, 43, "animationEndCallback"], [53, 65, 73, 63], [53, 66, 73, 64], [54, 4, 74, 2, "element"], [54, 11, 74, 9], [54, 12, 74, 10, "addEventListener"], [54, 28, 74, 26], [54, 29, 74, 27], [54, 46, 74, 44], [54, 48, 74, 46, "animationCancelCallback"], [54, 71, 74, 69], [54, 72, 74, 70], [55, 4, 76, 2, "element"], [55, 11, 76, 9], [55, 12, 76, 10, "append<PERSON><PERSON><PERSON>"], [55, 23, 76, 21], [55, 24, 76, 22, "dummy"], [55, 29, 76, 27], [55, 30, 76, 28], [56, 2, 77, 0], [57, 2, 79, 0], [57, 11, 79, 9, "prepareDummy"], [57, 23, 79, 21, "prepareDummy"], [57, 24, 80, 2, "element"], [57, 31, 80, 32], [57, 33, 81, 2, "animationConfig"], [57, 48, 81, 34], [57, 50, 82, 2, "transitionData"], [57, 64, 82, 32], [57, 66, 83, 2, "dummyTransitionKeyframeName"], [57, 93, 83, 37], [57, 95, 84, 2], [58, 4, 85, 2], [58, 8, 85, 8, "dummyAnimationConfig"], [58, 28, 85, 45], [58, 31, 85, 48], [59, 6, 86, 4, "animationName"], [59, 19, 86, 17], [59, 21, 86, 19, "dummyTransitionKeyframeName"], [59, 48, 86, 46], [60, 6, 87, 4, "animationType"], [60, 19, 87, 17], [60, 21, 87, 19, "LayoutAnimationType"], [60, 53, 87, 38], [60, 54, 87, 39, "LAYOUT"], [60, 60, 87, 45], [61, 6, 88, 4, "duration"], [61, 14, 88, 12], [61, 16, 88, 14, "animationConfig"], [61, 31, 88, 29], [61, 32, 88, 30, "duration"], [61, 40, 88, 38], [62, 6, 89, 4, "delay"], [62, 11, 89, 9], [62, 13, 89, 11, "animationConfig"], [62, 28, 89, 26], [62, 29, 89, 27, "delay"], [62, 34, 89, 32], [63, 6, 90, 4, "easing"], [63, 12, 90, 10], [63, 14, 90, 12], [63, 18, 90, 12, "getEasingByName"], [63, 41, 90, 27], [63, 43, 90, 28, "transitionData"], [63, 57, 90, 42], [63, 58, 90, 43, "easingY"], [63, 65, 90, 69], [63, 66, 90, 70], [64, 6, 91, 4, "callback"], [64, 14, 91, 12], [64, 16, 91, 14], [64, 20, 91, 18], [65, 6, 92, 4, "reversed"], [65, 14, 92, 12], [65, 16, 92, 14], [66, 4, 93, 2], [66, 5, 93, 3], [67, 4, 95, 2], [67, 8, 95, 8, "dummy"], [67, 13, 95, 13], [67, 16, 95, 16, "element"], [67, 23, 95, 23], [67, 24, 95, 24, "cloneNode"], [67, 33, 95, 33], [67, 34, 95, 34], [67, 38, 95, 38], [67, 39, 95, 64], [68, 4, 96, 2, "resetStyle"], [68, 14, 96, 12], [68, 15, 96, 13, "dummy"], [68, 20, 96, 18], [68, 21, 96, 19], [69, 4, 98, 2], [69, 11, 98, 9], [70, 6, 98, 11, "dummy"], [70, 11, 98, 16], [71, 6, 98, 18, "dummyAnimationConfig"], [72, 4, 98, 39], [72, 5, 98, 40], [73, 2, 99, 0], [74, 2, 101, 7], [74, 11, 101, 16, "prepareCurvedTransition"], [74, 34, 101, 39, "prepareCurvedTransition"], [74, 35, 102, 2, "element"], [74, 42, 102, 32], [74, 44, 103, 2, "animationConfig"], [74, 59, 103, 34], [74, 61, 104, 2, "transitionData"], [74, 75, 104, 32], [74, 77, 105, 2, "dummyTransitionKeyframeName"], [74, 104, 105, 37], [74, 106, 106, 2], [75, 4, 107, 2], [75, 8, 107, 2, "_prepareDummy"], [75, 21, 107, 2], [75, 24, 107, 42, "prepareDummy"], [75, 36, 107, 54], [75, 37, 108, 4, "element"], [75, 44, 108, 11], [75, 46, 109, 4, "animationConfig"], [75, 61, 109, 19], [75, 63, 110, 4, "transitionData"], [75, 77, 110, 18], [75, 79, 111, 4, "dummyTransitionKeyframeName"], [75, 106, 112, 2], [75, 107, 112, 3], [76, 6, 107, 10, "dummy"], [76, 11, 107, 15], [76, 14, 107, 15, "_prepareDummy"], [76, 27, 107, 15], [76, 28, 107, 10, "dummy"], [76, 33, 107, 15], [77, 6, 107, 17, "dummyAnimationConfig"], [77, 26, 107, 37], [77, 29, 107, 37, "_prepareDummy"], [77, 42, 107, 37], [77, 43, 107, 17, "dummyAnimationConfig"], [77, 63, 107, 37], [78, 4, 114, 2, "prepareParent"], [78, 17, 114, 15], [78, 18, 114, 16, "element"], [78, 25, 114, 23], [78, 27, 114, 25, "dummy"], [78, 32, 114, 30], [78, 34, 114, 32, "animationConfig"], [78, 49, 114, 47], [78, 51, 114, 49, "transitionData"], [78, 65, 114, 63], [78, 66, 114, 64], [79, 4, 116, 2], [79, 11, 116, 9], [80, 6, 116, 11, "dummy"], [80, 11, 116, 16], [81, 6, 116, 18, "dummyAnimationConfig"], [82, 4, 116, 39], [82, 5, 116, 40], [83, 2, 117, 0], [84, 2, 119, 7], [84, 11, 119, 16, "CurvedTransition"], [84, 27, 119, 32, "CurvedTransition"], [84, 28, 120, 2, "keyframeXName"], [84, 41, 120, 23], [84, 43, 121, 2, "keyframeYName"], [84, 56, 121, 23], [84, 58, 122, 2, "transitionData"], [84, 72, 122, 32], [84, 74, 123, 2], [85, 4, 124, 2], [85, 8, 124, 8, "keyframeXObj"], [85, 20, 124, 20], [85, 23, 124, 23], [86, 6, 125, 4, "name"], [86, 10, 125, 8], [86, 12, 125, 10, "keyframeXName"], [86, 25, 125, 23], [87, 6, 126, 4, "style"], [87, 11, 126, 9], [87, 13, 126, 11], [88, 8, 127, 6], [88, 9, 127, 7], [88, 11, 127, 9], [89, 10, 128, 8, "transform"], [89, 19, 128, 17], [89, 21, 128, 19], [89, 22, 129, 10], [90, 12, 130, 12, "translateX"], [90, 22, 130, 22], [90, 24, 130, 24], [90, 27, 130, 27, "transitionData"], [90, 41, 130, 41], [90, 42, 130, 42, "translateX"], [90, 52, 130, 52], [90, 56, 130, 56], [91, 12, 131, 12, "scale"], [91, 17, 131, 17], [91, 19, 131, 19], [91, 22, 131, 22, "transitionData"], [91, 36, 131, 36], [91, 37, 131, 37, "scaleX"], [91, 43, 131, 43], [91, 47, 131, 47, "transitionData"], [91, 61, 131, 61], [91, 62, 131, 62, "scaleY"], [91, 68, 131, 68], [92, 10, 132, 10], [92, 11, 132, 11], [93, 8, 134, 6], [94, 6, 135, 4], [94, 7, 135, 5], [95, 6, 136, 4, "duration"], [95, 14, 136, 12], [95, 16, 136, 14], [96, 4, 137, 2], [96, 5, 137, 3], [97, 4, 139, 2], [97, 8, 139, 8, "keyframeYObj"], [97, 20, 139, 20], [97, 23, 139, 23], [98, 6, 140, 4, "name"], [98, 10, 140, 8], [98, 12, 140, 10, "keyframeYName"], [98, 25, 140, 23], [99, 6, 141, 4, "style"], [99, 11, 141, 9], [99, 13, 141, 11], [100, 8, 142, 6], [100, 9, 142, 7], [100, 11, 142, 9], [101, 10, 143, 8, "transform"], [101, 19, 143, 17], [101, 21, 143, 19], [101, 22, 144, 10], [102, 12, 145, 12, "translateY"], [102, 22, 145, 22], [102, 24, 145, 24], [102, 27, 145, 27, "transitionData"], [102, 41, 145, 41], [102, 42, 145, 42, "translateY"], [102, 52, 145, 52], [102, 56, 145, 56], [103, 12, 146, 12, "scale"], [103, 17, 146, 17], [103, 19, 146, 19], [103, 22, 146, 22, "transitionData"], [103, 36, 146, 36], [103, 37, 146, 37, "scaleX"], [103, 43, 146, 43], [103, 47, 146, 47, "transitionData"], [103, 61, 146, 61], [103, 62, 146, 62, "scaleY"], [103, 68, 146, 68], [104, 10, 147, 10], [104, 11, 147, 11], [105, 8, 149, 6], [106, 6, 150, 4], [106, 7, 150, 5], [107, 6, 151, 4, "duration"], [107, 14, 151, 12], [107, 16, 151, 14], [108, 4, 152, 2], [108, 5, 152, 3], [109, 4, 154, 2], [109, 11, 154, 9], [110, 6, 155, 4, "firstKeyframeObj"], [110, 22, 155, 20], [110, 24, 155, 22, "keyframeXObj"], [110, 36, 155, 34], [111, 6, 156, 4, "secondKeyframeObj"], [111, 23, 156, 21], [111, 25, 156, 23, "keyframeYObj"], [112, 4, 157, 2], [112, 5, 157, 3], [113, 2, 158, 0], [114, 0, 158, 1], [114, 3]], "functionMap": {"names": ["<global>", "resetStyle", "showChildren", "prepareParent", "onFinalize", "animationCancelCallback", "animationEndCallback", "prepareDummy", "prepareCurvedTransition", "CurvedTransition"], "mappings": "AAA;ACQ;CDQ;AEE;CFe;AGE;qBCiB;GDQ;kCEE;GFG;+BGE;GHG;CHM;AOE;CPoB;OQE;CRgB;OSE;CTuC"}}, "type": "js/module"}]}