{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 36, "index": 50}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useWorkletCallback = useWorkletCallback;\n  var _react = require(_dependencyMap[0], \"react\");\n  /** @deprecated Use React.useCallback instead */\n  function useWorkletCallback(worklet, deps) {\n    return (0, _react.useCallback)(worklet, deps ?? []);\n  }\n});", "lineCount": 13, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useWorkletCallback"], [7, 28, 1, 13], [7, 31, 1, 13, "useWorkletCallback"], [7, 49, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 6, 0], [10, 2, 7, 7], [10, 11, 7, 16, "useWorkletCallback"], [10, 29, 7, 34, "useWorkletCallback"], [10, 30, 8, 2, "worklet"], [10, 37, 8, 41], [10, 39, 9, 2, "deps"], [10, 43, 9, 23], [10, 45, 10, 2], [11, 4, 11, 2], [11, 11, 11, 9], [11, 15, 11, 9, "useCallback"], [11, 33, 11, 20], [11, 35, 11, 21, "worklet"], [11, 42, 11, 28], [11, 44, 11, 30, "deps"], [11, 48, 11, 34], [11, 52, 11, 38], [11, 54, 11, 40], [11, 55, 11, 41], [12, 2, 12, 0], [13, 0, 12, 1], [13, 3]], "functionMap": {"names": ["<global>", "useWorkletCallback"], "mappings": "AAA;OCM;CDK"}}, "type": "js/module"}]}