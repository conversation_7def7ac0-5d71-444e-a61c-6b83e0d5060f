{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./winter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 84}, "end": {"line": 2, "column": 18, "index": 102}}], "key": "mkkKJkNfPl3SGB5Bx4OGQ/3JW8Y=", "exportNames": ["*"]}}, {"name": "expo-asset", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 103}, "end": {"line": 3, "column": 20, "index": 123}}], "key": "ZXJFWHziJpBZf3W7vl00wXf6fd8=", "exportNames": ["*"]}}, {"name": "expo/virtual/rsc", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 124}, "end": {"line": 4, "column": 26, "index": 150}}], "key": "Njna7k+CMzQedLgoLi0KGtlTdvM=", "exportNames": ["*"]}}, {"name": "expo-constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 152}, "end": {"line": 6, "column": 39, "index": 191}}], "key": "pPv5KzfRT0rL6NCr7G9k0o4d1W8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 192}, "end": {"line": 7, "column": 76, "index": 268}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./environment/ExpoGo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 270}, "end": {"line": 9, "column": 57, "index": 327}}], "key": "F+MwpkEnpJkgvAycBfX5pcdeFTU=", "exportNames": ["*"]}}, {"name": "./errors/AppEntryNotFound", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 328}, "end": {"line": 10, "column": 61, "index": 389}}], "key": "uxC/Eigd8br/RpahWlhds1Je7vE=", "exportNames": ["*"]}}, {"name": "./errors/ExpoErrorManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 390}, "end": {"line": 11, "column": 63, "index": 453}}], "key": "esKyY/+Md43XSEpLGnyGTCXAU2g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  require(_dependencyMap[1], \"./winter\");\n  require(_dependencyMap[2], \"expo-asset\");\n  require(_dependencyMap[3], \"expo/virtual/rsc\");\n  var _expoConstants = _interopRequireDefault(require(_dependencyMap[4], \"expo-constants\"));\n  var _reactNative = require(_dependencyMap[5], \"react-native\");\n  var _ExpoGo = require(_dependencyMap[6], \"./environment/ExpoGo\");\n  var _AppEntryNotFound = require(_dependencyMap[7], \"./errors/AppEntryNotFound\");\n  var _ExpoErrorManager = require(_dependencyMap[8], \"./errors/ExpoErrorManager\");\n  // load expo-asset immediately to set a custom `source` transformer in React Native\n\n  if ((0, _ExpoGo.isRunningInExpoGo)()) {\n    // set up some improvements to commonly logged error messages stemming from react-native\n    var globalHandler = ErrorUtils.getGlobalHandler();\n    ErrorUtils.setGlobalHandler((0, _ExpoErrorManager.createErrorHandler)(globalHandler));\n  }\n\n  // Warn if the New Architecture is not explicitly enabled in the app config and we are running in Expo Go.\n  // This could be problematic because you will be developing your app with the New Architecture enabled and\n  // but your builds will have the New Architecture disabled.\n  if (__DEV__ && (0, _ExpoGo.isRunningInExpoGo)() && process.env.NODE_ENV === 'development') {\n    ['android', 'ios'].forEach(platform => {\n      var newArchPlatformConfig = _expoConstants.default.expoConfig?.[platform]?.newArchEnabled;\n      var newArchRootConfig = _expoConstants.default.expoConfig?.newArchEnabled;\n      var isNewArchExplicitlyDisabled = newArchPlatformConfig === false || newArchPlatformConfig === undefined && newArchRootConfig === false;\n      if (_reactNative.Platform.OS === platform && isNewArchExplicitlyDisabled) {\n        // Wrap it in rAF to show the warning after the React Native DevTools message\n        requestAnimationFrame(() => {\n          console.warn(`🚨 React Native's New Architecture is always enabled in Expo Go, but it is explicitly disabled in your project's app config. This may lead to unexpected behavior when creating a production or development build. Remove \"newArchEnabled\": false from your app.json.\\nLearn more: https://docs.expo.dev/guides/new-architecture/`);\n        });\n      }\n    });\n  }\n\n  // Disable the \"Open debugger to view warnings\" React Native DevTools warning in\n  // Expo Go and expo-dev-client, because launching the debugger from there will not\n  // get the correct JS target.\n  var IS_RUNNING_IN_DEV_CLIENT = !!_reactNative.NativeModules.EXDevLauncher;\n  if (__DEV__ && _reactNative.LogBox?.ignoreLogs && ((0, _ExpoGo.isRunningInExpoGo)() || IS_RUNNING_IN_DEV_CLIENT)) {\n    _reactNative.LogBox.ignoreLogs([/Open debugger to view warnings/]);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // Register a default component and expect `registerRootComponent` to be called later and update it.\n    _reactNative.AppRegistry.registerComponent('main', () => _AppEntryNotFound.AppEntryNotFound);\n  }\n});", "lineCount": 47, "map": [[3, 2, 2, 0, "require"], [3, 9, 2, 0], [3, 10, 2, 0, "_dependencyMap"], [3, 24, 2, 0], [4, 2, 3, 0, "require"], [4, 9, 3, 0], [4, 10, 3, 0, "_dependencyMap"], [4, 24, 3, 0], [5, 2, 4, 0, "require"], [5, 9, 4, 0], [5, 10, 4, 0, "_dependencyMap"], [5, 24, 4, 0], [6, 2, 6, 0], [6, 6, 6, 0, "_expoConstants"], [6, 20, 6, 0], [6, 23, 6, 0, "_interopRequireDefault"], [6, 45, 6, 0], [6, 46, 6, 0, "require"], [6, 53, 6, 0], [6, 54, 6, 0, "_dependencyMap"], [6, 68, 6, 0], [7, 2, 7, 0], [7, 6, 7, 0, "_reactNative"], [7, 18, 7, 0], [7, 21, 7, 0, "require"], [7, 28, 7, 0], [7, 29, 7, 0, "_dependencyMap"], [7, 43, 7, 0], [8, 2, 9, 0], [8, 6, 9, 0, "_ExpoGo"], [8, 13, 9, 0], [8, 16, 9, 0, "require"], [8, 23, 9, 0], [8, 24, 9, 0, "_dependencyMap"], [8, 38, 9, 0], [9, 2, 10, 0], [9, 6, 10, 0, "_AppEntryNotFound"], [9, 23, 10, 0], [9, 26, 10, 0, "require"], [9, 33, 10, 0], [9, 34, 10, 0, "_dependencyMap"], [9, 48, 10, 0], [10, 2, 11, 0], [10, 6, 11, 0, "_ExpoErrorManager"], [10, 23, 11, 0], [10, 26, 11, 0, "require"], [10, 33, 11, 0], [10, 34, 11, 0, "_dependencyMap"], [10, 48, 11, 0], [11, 2, 1, 0], [13, 2, 13, 0], [13, 6, 13, 4], [13, 10, 13, 4, "isRunningInExpoGo"], [13, 35, 13, 21], [13, 37, 13, 22], [13, 38, 13, 23], [13, 40, 13, 25], [14, 4, 14, 2], [15, 4, 15, 2], [15, 8, 15, 8, "globalHandler"], [15, 21, 15, 21], [15, 24, 15, 24, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 34, 15, 34], [15, 35, 15, 35, "getGlobalHandler"], [15, 51, 15, 51], [15, 52, 15, 52], [15, 53, 15, 53], [16, 4, 16, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 14, 16, 12], [16, 15, 16, 13, "setGlobalHandler"], [16, 31, 16, 29], [16, 32, 16, 30], [16, 36, 16, 30, "createErrorHandler"], [16, 72, 16, 48], [16, 74, 16, 49, "globalHandler"], [16, 87, 16, 62], [16, 88, 16, 63], [16, 89, 16, 64], [17, 2, 17, 0], [19, 2, 19, 0], [20, 2, 20, 0], [21, 2, 21, 0], [22, 2, 22, 0], [22, 6, 22, 4, "__DEV__"], [22, 13, 22, 11], [22, 17, 22, 15], [22, 21, 22, 15, "isRunningInExpoGo"], [22, 46, 22, 32], [22, 48, 22, 33], [22, 49, 22, 34], [22, 53, 22, 38, "process"], [22, 60, 22, 45], [22, 61, 22, 46, "env"], [22, 64, 22, 49], [22, 65, 22, 50, "NODE_ENV"], [22, 73, 22, 58], [22, 78, 22, 63], [22, 91, 22, 76], [22, 93, 22, 78], [23, 4, 23, 3], [23, 5, 23, 4], [23, 14, 23, 13], [23, 16, 23, 15], [23, 21, 23, 20], [23, 22, 23, 21], [23, 23, 23, 32, "for<PERSON>ach"], [23, 30, 23, 39], [23, 31, 23, 41, "platform"], [23, 39, 23, 49], [23, 43, 23, 54], [24, 6, 24, 4], [24, 10, 24, 10, "newArchPlatformConfig"], [24, 31, 24, 31], [24, 34, 24, 34, "Constants"], [24, 56, 24, 43], [24, 57, 24, 44, "expoConfig"], [24, 67, 24, 54], [24, 70, 24, 57, "platform"], [24, 78, 24, 65], [24, 79, 24, 66], [24, 81, 24, 68, "newArchEnabled"], [24, 95, 24, 82], [25, 6, 25, 4], [25, 10, 25, 10, "newArchRootConfig"], [25, 27, 25, 27], [25, 30, 25, 30, "Constants"], [25, 52, 25, 39], [25, 53, 25, 40, "expoConfig"], [25, 63, 25, 50], [25, 65, 25, 52, "newArchEnabled"], [25, 79, 25, 66], [26, 6, 27, 4], [26, 10, 27, 10, "isNewArchExplicitlyDisabled"], [26, 37, 27, 37], [26, 40, 28, 6, "newArchPlatformConfig"], [26, 61, 28, 27], [26, 66, 28, 32], [26, 71, 28, 37], [26, 75, 29, 7, "newArchPlatformConfig"], [26, 96, 29, 28], [26, 101, 29, 33, "undefined"], [26, 110, 29, 42], [26, 114, 29, 46, "newArchRootConfig"], [26, 131, 29, 63], [26, 136, 29, 68], [26, 141, 29, 74], [27, 6, 31, 4], [27, 10, 31, 8, "Platform"], [27, 31, 31, 16], [27, 32, 31, 17, "OS"], [27, 34, 31, 19], [27, 39, 31, 24, "platform"], [27, 47, 31, 32], [27, 51, 31, 36, "isNewArchExplicitlyDisabled"], [27, 78, 31, 63], [27, 80, 31, 65], [28, 8, 32, 6], [29, 8, 33, 6, "requestAnimationFrame"], [29, 29, 33, 27], [29, 30, 33, 28], [29, 36, 33, 34], [30, 10, 34, 8, "console"], [30, 17, 34, 15], [30, 18, 34, 16, "warn"], [30, 22, 34, 20], [30, 23, 35, 10], [30, 346, 36, 8], [30, 347, 36, 9], [31, 8, 37, 6], [31, 9, 37, 7], [31, 10, 37, 8], [32, 6, 38, 4], [33, 4, 39, 2], [33, 5, 39, 3], [33, 6, 39, 4], [34, 2, 40, 0], [36, 2, 42, 0], [37, 2, 43, 0], [38, 2, 44, 0], [39, 2, 45, 0], [39, 6, 45, 6, "IS_RUNNING_IN_DEV_CLIENT"], [39, 30, 45, 30], [39, 33, 45, 33], [39, 34, 45, 34], [39, 35, 45, 35, "NativeModules"], [39, 61, 45, 48], [39, 62, 45, 49, "EXDevLauncher"], [39, 75, 45, 62], [40, 2, 46, 0], [40, 6, 46, 4, "__DEV__"], [40, 13, 46, 11], [40, 17, 46, 15, "LogBox"], [40, 36, 46, 21], [40, 38, 46, 23, "ignoreLogs"], [40, 48, 46, 33], [40, 53, 46, 38], [40, 57, 46, 38, "isRunningInExpoGo"], [40, 82, 46, 55], [40, 84, 46, 56], [40, 85, 46, 57], [40, 89, 46, 61, "IS_RUNNING_IN_DEV_CLIENT"], [40, 113, 46, 85], [40, 114, 46, 86], [40, 116, 46, 88], [41, 4, 47, 2, "LogBox"], [41, 23, 47, 8], [41, 24, 47, 9, "ignoreLogs"], [41, 34, 47, 19], [41, 35, 47, 20], [41, 36, 47, 21], [41, 68, 47, 53], [41, 69, 47, 54], [41, 70, 47, 55], [42, 2, 48, 0], [43, 2, 50, 0], [43, 6, 50, 4, "process"], [43, 13, 50, 11], [43, 14, 50, 12, "env"], [43, 17, 50, 15], [43, 18, 50, 16, "NODE_ENV"], [43, 26, 50, 24], [43, 31, 50, 29], [43, 43, 50, 41], [43, 45, 50, 43], [44, 4, 51, 2], [45, 4, 52, 2, "AppRegistry"], [45, 28, 52, 13], [45, 29, 52, 14, "registerComponent"], [45, 46, 52, 31], [45, 47, 52, 32], [45, 53, 52, 38], [45, 55, 52, 40], [45, 61, 52, 46, "AppEntryNotFound"], [45, 95, 52, 62], [45, 96, 52, 63], [46, 2, 53, 0], [47, 0, 53, 1], [47, 3]], "functionMap": {"names": ["<global>", "forEach$argument_0", "requestAnimationFrame$argument_0", "AppRegistry.registerComponent$argument_1"], "mappings": "AAA;wCCsB;4BCU;ODI;GDE;wCGa,sBH"}}, "type": "js/module"}]}