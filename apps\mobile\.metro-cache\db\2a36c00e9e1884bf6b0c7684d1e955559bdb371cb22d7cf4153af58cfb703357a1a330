{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "../../dom/events/Event", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 43}}], "key": "DDe09b6G/xKMPeTxmtlFrrA0ORw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _Event2 = _interopRequireDefault(require(_dependencyMap[6], \"../../dom/events/Event\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var ProgressEvent = exports.default = /*#__PURE__*/function (_Event) {\n    function ProgressEvent(type, options) {\n      var _this;\n      (0, _classCallCheck2.default)(this, ProgressEvent);\n      _this = _callSuper(this, ProgressEvent, [type, options]);\n      _this._lengthComputable = Boolean(options?.lengthComputable);\n      _this._loaded = Number(options?.loaded) || 0;\n      _this._total = Number(options?.total) || 0;\n      return _this;\n    }\n    (0, _inherits2.default)(ProgressEvent, _Event);\n    return (0, _createClass2.default)(ProgressEvent, [{\n      key: \"lengthComputable\",\n      get: function () {\n        return this._lengthComputable;\n      }\n    }, {\n      key: \"loaded\",\n      get: function () {\n        return this._loaded;\n      }\n    }, {\n      key: \"total\",\n      get: function () {\n        return this._total;\n      }\n    }]);\n  }(_Event2.default);\n});", "lineCount": 43, "map": [[12, 2, 20, 0], [12, 6, 20, 0, "_Event2"], [12, 13, 20, 0], [12, 16, 20, 0, "_interopRequireDefault"], [12, 38, 20, 0], [12, 39, 20, 0, "require"], [12, 46, 20, 0], [12, 47, 20, 0, "_dependencyMap"], [12, 61, 20, 0], [13, 2, 20, 43], [13, 11, 20, 43, "_callSuper"], [13, 22, 20, 43, "t"], [13, 23, 20, 43], [13, 25, 20, 43, "o"], [13, 26, 20, 43], [13, 28, 20, 43, "e"], [13, 29, 20, 43], [13, 40, 20, 43, "o"], [13, 41, 20, 43], [13, 48, 20, 43, "_getPrototypeOf2"], [13, 64, 20, 43], [13, 65, 20, 43, "default"], [13, 72, 20, 43], [13, 74, 20, 43, "o"], [13, 75, 20, 43], [13, 82, 20, 43, "_possibleConstructorReturn2"], [13, 109, 20, 43], [13, 110, 20, 43, "default"], [13, 117, 20, 43], [13, 119, 20, 43, "t"], [13, 120, 20, 43], [13, 122, 20, 43, "_isNativeReflectConstruct"], [13, 147, 20, 43], [13, 152, 20, 43, "Reflect"], [13, 159, 20, 43], [13, 160, 20, 43, "construct"], [13, 169, 20, 43], [13, 170, 20, 43, "o"], [13, 171, 20, 43], [13, 173, 20, 43, "e"], [13, 174, 20, 43], [13, 186, 20, 43, "_getPrototypeOf2"], [13, 202, 20, 43], [13, 203, 20, 43, "default"], [13, 210, 20, 43], [13, 212, 20, 43, "t"], [13, 213, 20, 43], [13, 215, 20, 43, "constructor"], [13, 226, 20, 43], [13, 230, 20, 43, "o"], [13, 231, 20, 43], [13, 232, 20, 43, "apply"], [13, 237, 20, 43], [13, 238, 20, 43, "t"], [13, 239, 20, 43], [13, 241, 20, 43, "e"], [13, 242, 20, 43], [14, 2, 20, 43], [14, 11, 20, 43, "_isNativeReflectConstruct"], [14, 37, 20, 43], [14, 51, 20, 43, "t"], [14, 52, 20, 43], [14, 56, 20, 43, "Boolean"], [14, 63, 20, 43], [14, 64, 20, 43, "prototype"], [14, 73, 20, 43], [14, 74, 20, 43, "valueOf"], [14, 81, 20, 43], [14, 82, 20, 43, "call"], [14, 86, 20, 43], [14, 87, 20, 43, "Reflect"], [14, 94, 20, 43], [14, 95, 20, 43, "construct"], [14, 104, 20, 43], [14, 105, 20, 43, "Boolean"], [14, 112, 20, 43], [14, 145, 20, 43, "t"], [14, 146, 20, 43], [14, 159, 20, 43, "_isNativeReflectConstruct"], [14, 184, 20, 43], [14, 196, 20, 43, "_isNativeReflectConstruct"], [14, 197, 20, 43], [14, 210, 20, 43, "t"], [14, 211, 20, 43], [15, 2, 20, 43], [15, 6, 28, 21, "ProgressEvent"], [15, 19, 28, 34], [15, 22, 28, 34, "exports"], [15, 29, 28, 34], [15, 30, 28, 34, "default"], [15, 37, 28, 34], [15, 63, 28, 34, "_Event"], [15, 69, 28, 34], [16, 4, 33, 2], [16, 13, 33, 2, "ProgressEvent"], [16, 27, 33, 14, "type"], [16, 31, 33, 26], [16, 33, 33, 28, "options"], [16, 40, 33, 56], [16, 42, 33, 58], [17, 6, 33, 58], [17, 10, 33, 58, "_this"], [17, 15, 33, 58], [18, 6, 33, 58], [18, 10, 33, 58, "_classCallCheck2"], [18, 26, 33, 58], [18, 27, 33, 58, "default"], [18, 34, 33, 58], [18, 42, 33, 58, "ProgressEvent"], [18, 55, 33, 58], [19, 6, 34, 4, "_this"], [19, 11, 34, 4], [19, 14, 34, 4, "_callSuper"], [19, 24, 34, 4], [19, 31, 34, 4, "ProgressEvent"], [19, 44, 34, 4], [19, 47, 34, 10, "type"], [19, 51, 34, 14], [19, 53, 34, 16, "options"], [19, 60, 34, 23], [20, 6, 36, 4, "_this"], [20, 11, 36, 4], [20, 12, 36, 9, "_lengthComputable"], [20, 29, 36, 26], [20, 32, 36, 29, "Boolean"], [20, 39, 36, 36], [20, 40, 36, 37, "options"], [20, 47, 36, 44], [20, 49, 36, 46, "lengthComputable"], [20, 65, 36, 62], [20, 66, 36, 63], [21, 6, 37, 4, "_this"], [21, 11, 37, 4], [21, 12, 37, 9, "_loaded"], [21, 19, 37, 16], [21, 22, 37, 19, "Number"], [21, 28, 37, 25], [21, 29, 37, 26, "options"], [21, 36, 37, 33], [21, 38, 37, 35, "loaded"], [21, 44, 37, 41], [21, 45, 37, 42], [21, 49, 37, 46], [21, 50, 37, 47], [22, 6, 38, 4, "_this"], [22, 11, 38, 4], [22, 12, 38, 9, "_total"], [22, 18, 38, 15], [22, 21, 38, 18, "Number"], [22, 27, 38, 24], [22, 28, 38, 25, "options"], [22, 35, 38, 32], [22, 37, 38, 34, "total"], [22, 42, 38, 39], [22, 43, 38, 40], [22, 47, 38, 44], [22, 48, 38, 45], [23, 6, 38, 46], [23, 13, 38, 46, "_this"], [23, 18, 38, 46], [24, 4, 39, 2], [25, 4, 39, 3], [25, 8, 39, 3, "_inherits2"], [25, 18, 39, 3], [25, 19, 39, 3, "default"], [25, 26, 39, 3], [25, 28, 39, 3, "ProgressEvent"], [25, 41, 39, 3], [25, 43, 39, 3, "_Event"], [25, 49, 39, 3], [26, 4, 39, 3], [26, 15, 39, 3, "_createClass2"], [26, 28, 39, 3], [26, 29, 39, 3, "default"], [26, 36, 39, 3], [26, 38, 39, 3, "ProgressEvent"], [26, 51, 39, 3], [27, 6, 39, 3, "key"], [27, 9, 39, 3], [28, 6, 39, 3, "get"], [28, 9, 39, 3], [28, 11, 41, 2], [28, 20, 41, 2, "get"], [28, 21, 41, 2], [28, 23, 41, 34], [29, 8, 42, 4], [29, 15, 42, 11], [29, 19, 42, 15], [29, 20, 42, 16, "_lengthComputable"], [29, 37, 42, 33], [30, 6, 43, 2], [31, 4, 43, 3], [32, 6, 43, 3, "key"], [32, 9, 43, 3], [33, 6, 43, 3, "get"], [33, 9, 43, 3], [33, 11, 45, 2], [33, 20, 45, 2, "get"], [33, 21, 45, 2], [33, 23, 45, 23], [34, 8, 46, 4], [34, 15, 46, 11], [34, 19, 46, 15], [34, 20, 46, 16, "_loaded"], [34, 27, 46, 23], [35, 6, 47, 2], [36, 4, 47, 3], [37, 6, 47, 3, "key"], [37, 9, 47, 3], [38, 6, 47, 3, "get"], [38, 9, 47, 3], [38, 11, 49, 2], [38, 20, 49, 2, "get"], [38, 21, 49, 2], [38, 23, 49, 22], [39, 8, 50, 4], [39, 15, 50, 11], [39, 19, 50, 15], [39, 20, 50, 16, "_total"], [39, 26, 50, 22], [40, 6, 51, 2], [41, 4, 51, 3], [42, 2, 51, 3], [42, 4, 28, 43, "Event"], [42, 19, 28, 48], [43, 0, 28, 48], [43, 3]], "functionMap": {"names": ["<global>", "ProgressEvent", "constructor", "get__lengthComputable", "get__loaded", "get__total"], "mappings": "AAA;eC2B;ECK;GDM;EEE;GFE;EGE;GHE;EIE;GJE"}}, "type": "js/module"}]}