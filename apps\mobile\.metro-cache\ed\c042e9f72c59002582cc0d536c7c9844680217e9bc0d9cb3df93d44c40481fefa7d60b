{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 127}, "end": {"line": 20, "column": 22, "index": 554}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../handlers/createNativeWrapper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 556}, "end": {"line": 22, "column": 66, "index": 622}}], "key": "uy698ALVvfnGA/D1vVARxm+VSzo=", "exportNames": ["*"]}}, {"name": "../handlers/NativeViewGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 624}, "end": {"line": 27, "column": 46, "index": 731}}], "key": "eGhtox0gd5Kv82sJSVhkegOayjI=", "exportNames": ["*"]}}, {"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 733}, "end": {"line": 29, "column": 35, "index": 768}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.TextInput = exports.Switch = exports.ScrollView = exports.RefreshControl = exports.FlatList = exports.DrawerLayoutAndroid = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var _createNativeWrapper = _interopRequireDefault(require(_dependencyMap[5], \"../handlers/createNativeWrapper\"));\n  var _NativeViewGestureHandler = require(_dependencyMap[6], \"../handlers/NativeViewGestureHandler\");\n  var _utils = require(_dependencyMap[7], \"../utils\");\n  var _jsxDevRuntime = require(_dependencyMap[8], \"react/jsx-dev-runtime\");\n  var _excluded = [\"refreshControl\", \"waitFor\"],\n    _excluded2 = [\"waitFor\", \"refreshControl\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\components\\\\GestureComponents.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var RefreshControl = exports.RefreshControl = (0, _createNativeWrapper.default)(_reactNative.RefreshControl, {\n    disallowInterruption: true,\n    shouldCancelWhenOutside: false\n  });\n  // eslint-disable-next-line @typescript-eslint/no-redeclare\n\n  var GHScrollView = (0, _createNativeWrapper.default)(_reactNative.ScrollView, {\n    disallowInterruption: true,\n    shouldCancelWhenOutside: false\n  });\n  var ScrollView = exports.ScrollView = /*#__PURE__*/React.forwardRef((props, ref) => {\n    var refreshControlGestureRef = React.useRef(null);\n    var refreshControl = props.refreshControl,\n      waitFor = props.waitFor,\n      rest = (0, _objectWithoutProperties2.default)(props, _excluded);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(GHScrollView, {\n      ...rest,\n      // @ts-ignore `ref` exists on `GHScrollView`\n      ref: ref,\n      waitFor: [...(0, _utils.toArray)(waitFor ?? []), refreshControlGestureRef]\n      // @ts-ignore we don't pass `refreshing` prop as we only want to override the ref\n      ,\n      refreshControl: refreshControl ? /*#__PURE__*/React.cloneElement(refreshControl, {\n        // @ts-ignore for reasons unknown to me, `ref` doesn't exist on the type inferred by TS\n        ref: refreshControlGestureRef\n      }) : undefined\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 5\n    }, this);\n  });\n  // Backward type compatibility with https://github.com/software-mansion/react-native-gesture-handler/blob/db78d3ca7d48e8ba57482d3fe9b0a15aa79d9932/react-native-gesture-handler.d.ts#L440-L457\n  // include methods of wrapped components by creating an intersection type with the RN component instead of duplicating them.\n  // eslint-disable-next-line @typescript-eslint/no-redeclare\n\n  var Switch = exports.Switch = (0, _createNativeWrapper.default)(_reactNative.Switch, {\n    shouldCancelWhenOutside: false,\n    shouldActivateOnStart: true,\n    disallowInterruption: true\n  });\n  // eslint-disable-next-line @typescript-eslint/no-redeclare\n\n  var TextInput = exports.TextInput = (0, _createNativeWrapper.default)(_reactNative.TextInput);\n  // eslint-disable-next-line @typescript-eslint/no-redeclare\n\n  var DrawerLayoutAndroid = exports.DrawerLayoutAndroid = (0, _createNativeWrapper.default)(_reactNative.DrawerLayoutAndroid, {\n    disallowInterruption: true\n  });\n  // eslint-disable-next-line @typescript-eslint/no-redeclare\n\n  var FlatList = exports.FlatList = /*#__PURE__*/React.forwardRef((props, ref) => {\n    var refreshControlGestureRef = React.useRef(null);\n    var waitFor = props.waitFor,\n      refreshControl = props.refreshControl,\n      rest = (0, _objectWithoutProperties2.default)(props, _excluded2);\n    var flatListProps = {};\n    var scrollViewProps = {};\n    for (var _ref of Object.entries(rest)) {\n      var _ref2 = (0, _slicedToArray2.default)(_ref, 2);\n      var propName = _ref2[0];\n      var value = _ref2[1];\n      // https://github.com/microsoft/TypeScript/issues/26255\n      if (_NativeViewGestureHandler.nativeViewProps.includes(propName)) {\n        // @ts-ignore - this function cannot have generic type so we have to ignore this error\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        scrollViewProps[propName] = value;\n      } else {\n        // @ts-ignore - this function cannot have generic type so we have to ignore this error\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        flatListProps[propName] = value;\n      }\n    }\n    return (\n      /*#__PURE__*/\n      // @ts-ignore - this function cannot have generic type so we have to ignore this error\n      (0, _jsxDevRuntime.jsxDEV)(_reactNative.FlatList, {\n        ref: ref,\n        ...flatListProps,\n        renderScrollComponent: scrollProps => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ScrollView, {\n          ...scrollProps,\n          ...scrollViewProps,\n          waitFor: [...(0, _utils.toArray)(waitFor ?? []), refreshControlGestureRef]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 9\n        }, this)\n        // @ts-ignore we don't pass `refreshing` prop as we only want to override the ref\n        ,\n        refreshControl: refreshControl ? /*#__PURE__*/React.cloneElement(refreshControl, {\n          // @ts-ignore for reasons unknown to me, `ref` doesn't exist on the type inferred by TS\n          ref: refreshControlGestureRef\n        }) : undefined\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 5\n      }, this)\n    );\n  });\n  // eslint-disable-next-line @typescript-eslint/no-redeclare\n});", "lineCount": 121, "map": [[9, 2, 1, 0], [9, 6, 1, 0, "React"], [9, 11, 1, 0], [9, 14, 1, 0, "_interopRequireWildcard"], [9, 37, 1, 0], [9, 38, 1, 0, "require"], [9, 45, 1, 0], [9, 46, 1, 0, "_dependencyMap"], [9, 60, 1, 0], [10, 2, 8, 0], [10, 6, 8, 0, "_reactNative"], [10, 18, 8, 0], [10, 21, 8, 0, "require"], [10, 28, 8, 0], [10, 29, 8, 0, "_dependencyMap"], [10, 43, 8, 0], [11, 2, 22, 0], [11, 6, 22, 0, "_createNativeWrapper"], [11, 26, 22, 0], [11, 29, 22, 0, "_interopRequireDefault"], [11, 51, 22, 0], [11, 52, 22, 0, "require"], [11, 59, 22, 0], [11, 60, 22, 0, "_dependencyMap"], [11, 74, 22, 0], [12, 2, 24, 0], [12, 6, 24, 0, "_NativeViewGestureHandler"], [12, 31, 24, 0], [12, 34, 24, 0, "require"], [12, 41, 24, 0], [12, 42, 24, 0, "_dependencyMap"], [12, 56, 24, 0], [13, 2, 29, 0], [13, 6, 29, 0, "_utils"], [13, 12, 29, 0], [13, 15, 29, 0, "require"], [13, 22, 29, 0], [13, 23, 29, 0, "_dependencyMap"], [13, 37, 29, 0], [14, 2, 29, 35], [14, 6, 29, 35, "_jsxDevRuntime"], [14, 20, 29, 35], [14, 23, 29, 35, "require"], [14, 30, 29, 35], [14, 31, 29, 35, "_dependencyMap"], [14, 45, 29, 35], [15, 2, 29, 35], [15, 6, 29, 35, "_excluded"], [15, 15, 29, 35], [16, 4, 29, 35, "_excluded2"], [16, 14, 29, 35], [17, 2, 29, 35], [17, 6, 29, 35, "_jsxFileName"], [17, 18, 29, 35], [18, 2, 29, 35], [18, 11, 29, 35, "_interopRequireWildcard"], [18, 35, 29, 35, "e"], [18, 36, 29, 35], [18, 38, 29, 35, "t"], [18, 39, 29, 35], [18, 68, 29, 35, "WeakMap"], [18, 75, 29, 35], [18, 81, 29, 35, "r"], [18, 82, 29, 35], [18, 89, 29, 35, "WeakMap"], [18, 96, 29, 35], [18, 100, 29, 35, "n"], [18, 101, 29, 35], [18, 108, 29, 35, "WeakMap"], [18, 115, 29, 35], [18, 127, 29, 35, "_interopRequireWildcard"], [18, 150, 29, 35], [18, 162, 29, 35, "_interopRequireWildcard"], [18, 163, 29, 35, "e"], [18, 164, 29, 35], [18, 166, 29, 35, "t"], [18, 167, 29, 35], [18, 176, 29, 35, "t"], [18, 177, 29, 35], [18, 181, 29, 35, "e"], [18, 182, 29, 35], [18, 186, 29, 35, "e"], [18, 187, 29, 35], [18, 188, 29, 35, "__esModule"], [18, 198, 29, 35], [18, 207, 29, 35, "e"], [18, 208, 29, 35], [18, 214, 29, 35, "o"], [18, 215, 29, 35], [18, 217, 29, 35, "i"], [18, 218, 29, 35], [18, 220, 29, 35, "f"], [18, 221, 29, 35], [18, 226, 29, 35, "__proto__"], [18, 235, 29, 35], [18, 243, 29, 35, "default"], [18, 250, 29, 35], [18, 252, 29, 35, "e"], [18, 253, 29, 35], [18, 270, 29, 35, "e"], [18, 271, 29, 35], [18, 294, 29, 35, "e"], [18, 295, 29, 35], [18, 320, 29, 35, "e"], [18, 321, 29, 35], [18, 330, 29, 35, "f"], [18, 331, 29, 35], [18, 337, 29, 35, "o"], [18, 338, 29, 35], [18, 341, 29, 35, "t"], [18, 342, 29, 35], [18, 345, 29, 35, "n"], [18, 346, 29, 35], [18, 349, 29, 35, "r"], [18, 350, 29, 35], [18, 358, 29, 35, "o"], [18, 359, 29, 35], [18, 360, 29, 35, "has"], [18, 363, 29, 35], [18, 364, 29, 35, "e"], [18, 365, 29, 35], [18, 375, 29, 35, "o"], [18, 376, 29, 35], [18, 377, 29, 35, "get"], [18, 380, 29, 35], [18, 381, 29, 35, "e"], [18, 382, 29, 35], [18, 385, 29, 35, "o"], [18, 386, 29, 35], [18, 387, 29, 35, "set"], [18, 390, 29, 35], [18, 391, 29, 35, "e"], [18, 392, 29, 35], [18, 394, 29, 35, "f"], [18, 395, 29, 35], [18, 409, 29, 35, "_t"], [18, 411, 29, 35], [18, 415, 29, 35, "e"], [18, 416, 29, 35], [18, 432, 29, 35, "_t"], [18, 434, 29, 35], [18, 441, 29, 35, "hasOwnProperty"], [18, 455, 29, 35], [18, 456, 29, 35, "call"], [18, 460, 29, 35], [18, 461, 29, 35, "e"], [18, 462, 29, 35], [18, 464, 29, 35, "_t"], [18, 466, 29, 35], [18, 473, 29, 35, "i"], [18, 474, 29, 35], [18, 478, 29, 35, "o"], [18, 479, 29, 35], [18, 482, 29, 35, "Object"], [18, 488, 29, 35], [18, 489, 29, 35, "defineProperty"], [18, 503, 29, 35], [18, 508, 29, 35, "Object"], [18, 514, 29, 35], [18, 515, 29, 35, "getOwnPropertyDescriptor"], [18, 539, 29, 35], [18, 540, 29, 35, "e"], [18, 541, 29, 35], [18, 543, 29, 35, "_t"], [18, 545, 29, 35], [18, 552, 29, 35, "i"], [18, 553, 29, 35], [18, 554, 29, 35, "get"], [18, 557, 29, 35], [18, 561, 29, 35, "i"], [18, 562, 29, 35], [18, 563, 29, 35, "set"], [18, 566, 29, 35], [18, 570, 29, 35, "o"], [18, 571, 29, 35], [18, 572, 29, 35, "f"], [18, 573, 29, 35], [18, 575, 29, 35, "_t"], [18, 577, 29, 35], [18, 579, 29, 35, "i"], [18, 580, 29, 35], [18, 584, 29, 35, "f"], [18, 585, 29, 35], [18, 586, 29, 35, "_t"], [18, 588, 29, 35], [18, 592, 29, 35, "e"], [18, 593, 29, 35], [18, 594, 29, 35, "_t"], [18, 596, 29, 35], [18, 607, 29, 35, "f"], [18, 608, 29, 35], [18, 613, 29, 35, "e"], [18, 614, 29, 35], [18, 616, 29, 35, "t"], [18, 617, 29, 35], [19, 2, 31, 7], [19, 6, 31, 13, "RefreshControl"], [19, 20, 31, 27], [19, 23, 31, 27, "exports"], [19, 30, 31, 27], [19, 31, 31, 27, "RefreshControl"], [19, 45, 31, 27], [19, 48, 31, 30], [19, 52, 31, 30, "createNativeWrapper"], [19, 80, 31, 49], [19, 82, 31, 50, "RNRefreshControl"], [19, 109, 31, 66], [19, 111, 31, 68], [20, 4, 32, 2, "disallowInterruption"], [20, 24, 32, 22], [20, 26, 32, 24], [20, 30, 32, 28], [21, 4, 33, 2, "shouldCancelWhenOutside"], [21, 27, 33, 25], [21, 29, 33, 27], [22, 2, 34, 0], [22, 3, 34, 1], [22, 4, 34, 2], [23, 2, 35, 0], [25, 2, 38, 0], [25, 6, 38, 6, "GHScrollView"], [25, 18, 38, 18], [25, 21, 38, 21], [25, 25, 38, 21, "createNativeWrapper"], [25, 53, 38, 40], [25, 55, 39, 2, "RNScrollView"], [25, 78, 39, 14], [25, 80, 40, 2], [26, 4, 41, 4, "disallowInterruption"], [26, 24, 41, 24], [26, 26, 41, 26], [26, 30, 41, 30], [27, 4, 42, 4, "shouldCancelWhenOutside"], [27, 27, 42, 27], [27, 29, 42, 29], [28, 2, 43, 2], [28, 3, 44, 0], [28, 4, 44, 1], [29, 2, 45, 7], [29, 6, 45, 13, "ScrollView"], [29, 16, 45, 23], [29, 19, 45, 23, "exports"], [29, 26, 45, 23], [29, 27, 45, 23, "ScrollView"], [29, 37, 45, 23], [29, 53, 45, 26, "React"], [29, 58, 45, 31], [29, 59, 45, 32, "forwardRef"], [29, 69, 45, 42], [29, 70, 48, 2], [29, 71, 48, 3, "props"], [29, 76, 48, 8], [29, 78, 48, 10, "ref"], [29, 81, 48, 13], [29, 86, 48, 18], [30, 4, 49, 2], [30, 8, 49, 8, "refreshControlGestureRef"], [30, 32, 49, 32], [30, 35, 49, 35, "React"], [30, 40, 49, 40], [30, 41, 49, 41, "useRef"], [30, 47, 49, 47], [30, 48, 49, 64], [30, 52, 49, 68], [30, 53, 49, 69], [31, 4, 50, 2], [31, 8, 50, 10, "refreshControl"], [31, 22, 50, 24], [31, 25, 50, 47, "props"], [31, 30, 50, 52], [31, 31, 50, 10, "refreshControl"], [31, 45, 50, 24], [32, 6, 50, 26, "waitFor"], [32, 13, 50, 33], [32, 16, 50, 47, "props"], [32, 21, 50, 52], [32, 22, 50, 26, "waitFor"], [32, 29, 50, 33], [33, 6, 50, 38, "rest"], [33, 10, 50, 42], [33, 17, 50, 42, "_objectWithoutProperties2"], [33, 42, 50, 42], [33, 43, 50, 42, "default"], [33, 50, 50, 42], [33, 52, 50, 47, "props"], [33, 57, 50, 52], [33, 59, 50, 52, "_excluded"], [33, 68, 50, 52], [34, 4, 52, 2], [34, 24, 53, 4], [34, 28, 53, 4, "_jsxDevRuntime"], [34, 42, 53, 4], [34, 43, 53, 4, "jsxDEV"], [34, 49, 53, 4], [34, 51, 53, 5, "GHScrollView"], [34, 63, 53, 17], [35, 6, 53, 17], [35, 9, 54, 10, "rest"], [35, 13, 54, 14], [36, 6, 55, 6], [37, 6, 56, 6, "ref"], [37, 9, 56, 9], [37, 11, 56, 11, "ref"], [37, 14, 56, 15], [38, 6, 57, 6, "waitFor"], [38, 13, 57, 13], [38, 15, 57, 15], [38, 16, 57, 16], [38, 19, 57, 19], [38, 23, 57, 19, "toArray"], [38, 37, 57, 26], [38, 39, 57, 27, "waitFor"], [38, 46, 57, 34], [38, 50, 57, 38], [38, 52, 57, 40], [38, 53, 57, 41], [38, 55, 57, 43, "refreshControlGestureRef"], [38, 79, 57, 67], [39, 6, 58, 6], [40, 6, 58, 6], [41, 6, 59, 6, "refreshControl"], [41, 20, 59, 20], [41, 22, 60, 8, "refreshControl"], [41, 36, 60, 22], [41, 52, 61, 12, "React"], [41, 57, 61, 17], [41, 58, 61, 18, "cloneElement"], [41, 70, 61, 30], [41, 71, 61, 31, "refreshControl"], [41, 85, 61, 45], [41, 87, 61, 47], [42, 8, 62, 14], [43, 8, 63, 14, "ref"], [43, 11, 63, 17], [43, 13, 63, 19, "refreshControlGestureRef"], [44, 6, 64, 12], [44, 7, 64, 13], [44, 8, 64, 14], [44, 11, 65, 12, "undefined"], [45, 4, 66, 7], [46, 6, 66, 7, "fileName"], [46, 14, 66, 7], [46, 16, 66, 7, "_jsxFileName"], [46, 28, 66, 7], [47, 6, 66, 7, "lineNumber"], [47, 16, 66, 7], [48, 6, 66, 7, "columnNumber"], [48, 18, 66, 7], [49, 4, 66, 7], [49, 11, 67, 5], [49, 12, 67, 6], [50, 2, 69, 0], [50, 3, 69, 1], [50, 4, 69, 2], [51, 2, 70, 0], [52, 2, 71, 0], [53, 2, 72, 0], [55, 2, 75, 7], [55, 6, 75, 13, "Switch"], [55, 12, 75, 19], [55, 15, 75, 19, "exports"], [55, 22, 75, 19], [55, 23, 75, 19, "Switch"], [55, 29, 75, 19], [55, 32, 75, 22], [55, 36, 75, 22, "createNativeWrapper"], [55, 64, 75, 41], [55, 66, 75, 57, "RNSwitch"], [55, 85, 75, 65], [55, 87, 75, 67], [56, 4, 76, 2, "shouldCancelWhenOutside"], [56, 27, 76, 25], [56, 29, 76, 27], [56, 34, 76, 32], [57, 4, 77, 2, "shouldActivateOnStart"], [57, 25, 77, 23], [57, 27, 77, 25], [57, 31, 77, 29], [58, 4, 78, 2, "disallowInterruption"], [58, 24, 78, 22], [58, 26, 78, 24], [59, 2, 79, 0], [59, 3, 79, 1], [59, 4, 79, 2], [60, 2, 80, 0], [62, 2, 83, 7], [62, 6, 83, 13, "TextInput"], [62, 15, 83, 22], [62, 18, 83, 22, "exports"], [62, 25, 83, 22], [62, 26, 83, 22, "TextInput"], [62, 35, 83, 22], [62, 38, 83, 25], [62, 42, 83, 25, "createNativeWrapper"], [62, 70, 83, 44], [62, 72, 83, 63, "RNTextInput"], [62, 94, 83, 74], [62, 95, 83, 75], [63, 2, 84, 0], [65, 2, 87, 7], [65, 6, 87, 13, "DrawerLayoutAndroid"], [65, 25, 87, 32], [65, 28, 87, 32, "exports"], [65, 35, 87, 32], [65, 36, 87, 32, "DrawerLayoutAndroid"], [65, 55, 87, 32], [65, 58, 87, 35], [65, 62, 87, 35, "createNativeWrapper"], [65, 90, 87, 54], [65, 92, 89, 2, "RNDrawerLayoutAndroid"], [65, 124, 89, 23], [65, 126, 89, 25], [66, 4, 89, 27, "disallowInterruption"], [66, 24, 89, 47], [66, 26, 89, 49], [67, 2, 89, 54], [67, 3, 89, 55], [67, 4, 89, 56], [68, 2, 90, 0], [70, 2, 94, 7], [70, 6, 94, 13, "FlatList"], [70, 14, 94, 21], [70, 17, 94, 21, "exports"], [70, 24, 94, 21], [70, 25, 94, 21, "FlatList"], [70, 33, 94, 21], [70, 49, 94, 24, "React"], [70, 54, 94, 29], [70, 55, 94, 30, "forwardRef"], [70, 65, 94, 40], [70, 66, 94, 41], [70, 67, 94, 42, "props"], [70, 72, 94, 47], [70, 74, 94, 49, "ref"], [70, 77, 94, 52], [70, 82, 94, 57], [71, 4, 95, 2], [71, 8, 95, 8, "refreshControlGestureRef"], [71, 32, 95, 32], [71, 35, 95, 35, "React"], [71, 40, 95, 40], [71, 41, 95, 41, "useRef"], [71, 47, 95, 47], [71, 48, 95, 64], [71, 52, 95, 68], [71, 53, 95, 69], [72, 4, 97, 2], [72, 8, 97, 10, "waitFor"], [72, 15, 97, 17], [72, 18, 97, 47, "props"], [72, 23, 97, 52], [72, 24, 97, 10, "waitFor"], [72, 31, 97, 17], [73, 6, 97, 19, "refreshControl"], [73, 20, 97, 33], [73, 23, 97, 47, "props"], [73, 28, 97, 52], [73, 29, 97, 19, "refreshControl"], [73, 43, 97, 33], [74, 6, 97, 38, "rest"], [74, 10, 97, 42], [74, 17, 97, 42, "_objectWithoutProperties2"], [74, 42, 97, 42], [74, 43, 97, 42, "default"], [74, 50, 97, 42], [74, 52, 97, 47, "props"], [74, 57, 97, 52], [74, 59, 97, 52, "_excluded2"], [74, 69, 97, 52], [75, 4, 99, 2], [75, 8, 99, 8, "flatListProps"], [75, 21, 99, 21], [75, 24, 99, 24], [75, 25, 99, 25], [75, 26, 99, 26], [76, 4, 100, 2], [76, 8, 100, 8, "scrollViewProps"], [76, 23, 100, 23], [76, 26, 100, 26], [76, 27, 100, 27], [76, 28, 100, 28], [77, 4, 101, 2], [77, 13, 101, 2, "_ref"], [77, 17, 101, 2], [77, 21, 101, 34, "Object"], [77, 27, 101, 40], [77, 28, 101, 41, "entries"], [77, 35, 101, 48], [77, 36, 101, 49, "rest"], [77, 40, 101, 53], [77, 41, 101, 54], [77, 43, 101, 56], [78, 6, 101, 56], [78, 10, 101, 56, "_ref2"], [78, 15, 101, 56], [78, 22, 101, 56, "_slicedToArray2"], [78, 37, 101, 56], [78, 38, 101, 56, "default"], [78, 45, 101, 56], [78, 47, 101, 56, "_ref"], [78, 51, 101, 56], [79, 6, 101, 56], [79, 10, 101, 14, "propName"], [79, 18, 101, 22], [79, 21, 101, 22, "_ref2"], [79, 26, 101, 22], [80, 6, 101, 22], [80, 10, 101, 24, "value"], [80, 15, 101, 29], [80, 18, 101, 29, "_ref2"], [80, 23, 101, 29], [81, 6, 102, 4], [82, 6, 103, 4], [82, 10, 103, 9, "nativeViewProps"], [82, 51, 103, 24], [82, 52, 103, 47, "includes"], [82, 60, 103, 55], [82, 61, 103, 56, "propName"], [82, 69, 103, 64], [82, 70, 103, 65], [82, 72, 103, 67], [83, 8, 104, 6], [84, 8, 105, 6], [85, 8, 106, 6, "scrollViewProps"], [85, 23, 106, 21], [85, 24, 106, 22, "propName"], [85, 32, 106, 30], [85, 33, 106, 31], [85, 36, 106, 34, "value"], [85, 41, 106, 39], [86, 6, 107, 4], [86, 7, 107, 5], [86, 13, 107, 11], [87, 8, 108, 6], [88, 8, 109, 6], [89, 8, 110, 6, "flatListProps"], [89, 21, 110, 19], [89, 22, 110, 20, "propName"], [89, 30, 110, 28], [89, 31, 110, 29], [89, 34, 110, 32, "value"], [89, 39, 110, 37], [90, 6, 111, 4], [91, 4, 112, 2], [92, 4, 114, 2], [93, 6, 114, 2], [94, 6, 115, 4], [95, 6, 116, 4], [95, 10, 116, 4, "_jsxDevRuntime"], [95, 24, 116, 4], [95, 25, 116, 4, "jsxDEV"], [95, 31, 116, 4], [95, 33, 116, 5, "_reactNative"], [95, 45, 116, 5], [95, 46, 116, 5, "FlatList"], [95, 54, 116, 15], [96, 8, 117, 6, "ref"], [96, 11, 117, 9], [96, 13, 117, 11, "ref"], [96, 16, 117, 15], [97, 8, 117, 15], [97, 11, 118, 10, "flatListProps"], [97, 24, 118, 23], [98, 8, 119, 6, "renderScrollComponent"], [98, 29, 119, 27], [98, 31, 119, 30, "scrollProps"], [98, 42, 119, 41], [98, 59, 120, 8], [98, 63, 120, 8, "_jsxDevRuntime"], [98, 77, 120, 8], [98, 78, 120, 8, "jsxDEV"], [98, 84, 120, 8], [98, 86, 120, 9, "ScrollView"], [98, 96, 120, 19], [99, 10, 122, 12], [99, 13, 122, 15, "scrollProps"], [99, 24, 122, 26], [100, 10, 123, 12], [100, 13, 123, 15, "scrollViewProps"], [100, 28, 123, 30], [101, 10, 124, 12, "waitFor"], [101, 17, 124, 19], [101, 19, 124, 21], [101, 20, 124, 22], [101, 23, 124, 25], [101, 27, 124, 25, "toArray"], [101, 41, 124, 32], [101, 43, 124, 33, "waitFor"], [101, 50, 124, 40], [101, 54, 124, 44], [101, 56, 124, 46], [101, 57, 124, 47], [101, 59, 124, 49, "refreshControlGestureRef"], [101, 83, 124, 73], [102, 8, 124, 74], [103, 10, 124, 74, "fileName"], [103, 18, 124, 74], [103, 20, 124, 74, "_jsxFileName"], [103, 32, 124, 74], [104, 10, 124, 74, "lineNumber"], [104, 20, 124, 74], [105, 10, 124, 74, "columnNumber"], [105, 22, 124, 74], [106, 8, 124, 74], [106, 15, 126, 9], [107, 8, 128, 6], [108, 8, 128, 6], [109, 8, 129, 6, "refreshControl"], [109, 22, 129, 20], [109, 24, 130, 8, "refreshControl"], [109, 38, 130, 22], [109, 54, 131, 12, "React"], [109, 59, 131, 17], [109, 60, 131, 18, "cloneElement"], [109, 72, 131, 30], [109, 73, 131, 31, "refreshControl"], [109, 87, 131, 45], [109, 89, 131, 47], [110, 10, 132, 14], [111, 10, 133, 14, "ref"], [111, 13, 133, 17], [111, 15, 133, 19, "refreshControlGestureRef"], [112, 8, 134, 12], [112, 9, 134, 13], [112, 10, 134, 14], [112, 13, 135, 12, "undefined"], [113, 6, 136, 7], [114, 8, 136, 7, "fileName"], [114, 16, 136, 7], [114, 18, 136, 7, "_jsxFileName"], [114, 30, 136, 7], [115, 8, 136, 7, "lineNumber"], [115, 18, 136, 7], [116, 8, 136, 7, "columnNumber"], [116, 20, 136, 7], [117, 6, 136, 7], [117, 13, 137, 5], [118, 4, 137, 6], [119, 2, 139, 0], [119, 3, 139, 1], [119, 4, 146, 24], [120, 2, 147, 0], [121, 0, 147, 0], [121, 3]], "functionMap": {"names": ["<global>", "React.forwardRef$argument_0", "RNFlatList.props.renderScrollComponent"], "mappings": "AAA;EC+C;CDqB;yCCyB;6BCyB;ODQ;CDY"}}, "type": "js/module"}]}