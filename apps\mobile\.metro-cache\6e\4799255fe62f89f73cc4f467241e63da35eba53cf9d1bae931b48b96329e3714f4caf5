{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 73, "index": 120}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}, {"name": "./NavigationRouteContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 121}, "end": {"line": 5, "column": 69, "index": 190}}], "key": "AWXnpGNA5UkH1qQUM7hLv2L9KzI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.shouldPreventRemove = void 0;\n  exports.useOnPreventRemove = useOnPreventRemove;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _NavigationBuilderContext = require(_dependencyMap[1], \"./NavigationBuilderContext.js\");\n  var _NavigationRouteContext = require(_dependencyMap[2], \"./NavigationRouteContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var VISITED_ROUTE_KEYS = Symbol('VISITED_ROUTE_KEYS');\n  var shouldPreventRemove = (emitter, beforeRemoveListeners, currentRoutes, nextRoutes, action) => {\n    var nextRouteKeys = nextRoutes.map(route => route.key);\n\n    // Call these in reverse order so last screens handle the event first\n    var removedRoutes = currentRoutes.filter(route => !nextRouteKeys.includes(route.key)).reverse();\n    var visitedRouteKeys =\n    // @ts-expect-error: add this property to mark that we've already emitted this action\n    action[VISITED_ROUTE_KEYS] ?? new Set();\n    var beforeRemoveAction = {\n      ...action,\n      [VISITED_ROUTE_KEYS]: visitedRouteKeys\n    };\n    for (var route of removedRoutes) {\n      if (visitedRouteKeys.has(route.key)) {\n        // Skip if we've already emitted this action for this screen\n        continue;\n      }\n\n      // First, we need to check if any child screens want to prevent it\n      var isPrevented = beforeRemoveListeners[route.key]?.(beforeRemoveAction);\n      if (isPrevented) {\n        return true;\n      }\n      visitedRouteKeys.add(route.key);\n      var event = emitter.emit({\n        type: 'beforeRemove',\n        target: route.key,\n        data: {\n          action: beforeRemoveAction\n        },\n        canPreventDefault: true\n      });\n      if (event.defaultPrevented) {\n        return true;\n      }\n    }\n    return false;\n  };\n  exports.shouldPreventRemove = shouldPreventRemove;\n  function useOnPreventRemove(_ref) {\n    var getState = _ref.getState,\n      emitter = _ref.emitter,\n      beforeRemoveListeners = _ref.beforeRemoveListeners;\n    var _React$useContext = React.useContext(_NavigationBuilderContext.NavigationBuilderContext),\n      addKeyedListener = _React$useContext.addKeyedListener;\n    var route = React.useContext(_NavigationRouteContext.NavigationRouteContext);\n    var routeKey = route?.key;\n    React.useEffect(() => {\n      if (routeKey) {\n        return addKeyedListener?.('beforeRemove', routeKey, action => {\n          var state = getState();\n          return shouldPreventRemove(emitter, beforeRemoveListeners, state.routes, [], action);\n        });\n      }\n    }, [addKeyedListener, beforeRemoveListeners, emitter, getState, routeKey]);\n  }\n});", "lineCount": 70, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "shouldPreventRemove"], [7, 29, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "useOnPreventRemove"], [8, 28, 1, 13], [8, 31, 1, 13, "useOnPreventRemove"], [8, 49, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "React"], [9, 11, 3, 0], [9, 14, 3, 0, "_interopRequireWildcard"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_NavigationBuilderContext"], [10, 31, 4, 0], [10, 34, 4, 0, "require"], [10, 41, 4, 0], [10, 42, 4, 0, "_dependencyMap"], [10, 56, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_NavigationRouteContext"], [11, 29, 5, 0], [11, 32, 5, 0, "require"], [11, 39, 5, 0], [11, 40, 5, 0, "_dependencyMap"], [11, 54, 5, 0], [12, 2, 5, 69], [12, 11, 5, 69, "_interopRequireWildcard"], [12, 35, 5, 69, "e"], [12, 36, 5, 69], [12, 38, 5, 69, "t"], [12, 39, 5, 69], [12, 68, 5, 69, "WeakMap"], [12, 75, 5, 69], [12, 81, 5, 69, "r"], [12, 82, 5, 69], [12, 89, 5, 69, "WeakMap"], [12, 96, 5, 69], [12, 100, 5, 69, "n"], [12, 101, 5, 69], [12, 108, 5, 69, "WeakMap"], [12, 115, 5, 69], [12, 127, 5, 69, "_interopRequireWildcard"], [12, 150, 5, 69], [12, 162, 5, 69, "_interopRequireWildcard"], [12, 163, 5, 69, "e"], [12, 164, 5, 69], [12, 166, 5, 69, "t"], [12, 167, 5, 69], [12, 176, 5, 69, "t"], [12, 177, 5, 69], [12, 181, 5, 69, "e"], [12, 182, 5, 69], [12, 186, 5, 69, "e"], [12, 187, 5, 69], [12, 188, 5, 69, "__esModule"], [12, 198, 5, 69], [12, 207, 5, 69, "e"], [12, 208, 5, 69], [12, 214, 5, 69, "o"], [12, 215, 5, 69], [12, 217, 5, 69, "i"], [12, 218, 5, 69], [12, 220, 5, 69, "f"], [12, 221, 5, 69], [12, 226, 5, 69, "__proto__"], [12, 235, 5, 69], [12, 243, 5, 69, "default"], [12, 250, 5, 69], [12, 252, 5, 69, "e"], [12, 253, 5, 69], [12, 270, 5, 69, "e"], [12, 271, 5, 69], [12, 294, 5, 69, "e"], [12, 295, 5, 69], [12, 320, 5, 69, "e"], [12, 321, 5, 69], [12, 330, 5, 69, "f"], [12, 331, 5, 69], [12, 337, 5, 69, "o"], [12, 338, 5, 69], [12, 341, 5, 69, "t"], [12, 342, 5, 69], [12, 345, 5, 69, "n"], [12, 346, 5, 69], [12, 349, 5, 69, "r"], [12, 350, 5, 69], [12, 358, 5, 69, "o"], [12, 359, 5, 69], [12, 360, 5, 69, "has"], [12, 363, 5, 69], [12, 364, 5, 69, "e"], [12, 365, 5, 69], [12, 375, 5, 69, "o"], [12, 376, 5, 69], [12, 377, 5, 69, "get"], [12, 380, 5, 69], [12, 381, 5, 69, "e"], [12, 382, 5, 69], [12, 385, 5, 69, "o"], [12, 386, 5, 69], [12, 387, 5, 69, "set"], [12, 390, 5, 69], [12, 391, 5, 69, "e"], [12, 392, 5, 69], [12, 394, 5, 69, "f"], [12, 395, 5, 69], [12, 409, 5, 69, "_t"], [12, 411, 5, 69], [12, 415, 5, 69, "e"], [12, 416, 5, 69], [12, 432, 5, 69, "_t"], [12, 434, 5, 69], [12, 441, 5, 69, "hasOwnProperty"], [12, 455, 5, 69], [12, 456, 5, 69, "call"], [12, 460, 5, 69], [12, 461, 5, 69, "e"], [12, 462, 5, 69], [12, 464, 5, 69, "_t"], [12, 466, 5, 69], [12, 473, 5, 69, "i"], [12, 474, 5, 69], [12, 478, 5, 69, "o"], [12, 479, 5, 69], [12, 482, 5, 69, "Object"], [12, 488, 5, 69], [12, 489, 5, 69, "defineProperty"], [12, 503, 5, 69], [12, 508, 5, 69, "Object"], [12, 514, 5, 69], [12, 515, 5, 69, "getOwnPropertyDescriptor"], [12, 539, 5, 69], [12, 540, 5, 69, "e"], [12, 541, 5, 69], [12, 543, 5, 69, "_t"], [12, 545, 5, 69], [12, 552, 5, 69, "i"], [12, 553, 5, 69], [12, 554, 5, 69, "get"], [12, 557, 5, 69], [12, 561, 5, 69, "i"], [12, 562, 5, 69], [12, 563, 5, 69, "set"], [12, 566, 5, 69], [12, 570, 5, 69, "o"], [12, 571, 5, 69], [12, 572, 5, 69, "f"], [12, 573, 5, 69], [12, 575, 5, 69, "_t"], [12, 577, 5, 69], [12, 579, 5, 69, "i"], [12, 580, 5, 69], [12, 584, 5, 69, "f"], [12, 585, 5, 69], [12, 586, 5, 69, "_t"], [12, 588, 5, 69], [12, 592, 5, 69, "e"], [12, 593, 5, 69], [12, 594, 5, 69, "_t"], [12, 596, 5, 69], [12, 607, 5, 69, "f"], [12, 608, 5, 69], [12, 613, 5, 69, "e"], [12, 614, 5, 69], [12, 616, 5, 69, "t"], [12, 617, 5, 69], [13, 2, 6, 0], [13, 6, 6, 6, "VISITED_ROUTE_KEYS"], [13, 24, 6, 24], [13, 27, 6, 27, "Symbol"], [13, 33, 6, 33], [13, 34, 6, 34], [13, 54, 6, 54], [13, 55, 6, 55], [14, 2, 7, 7], [14, 6, 7, 13, "shouldPreventRemove"], [14, 25, 7, 32], [14, 28, 7, 35, "shouldPreventRemove"], [14, 29, 7, 36, "emitter"], [14, 36, 7, 43], [14, 38, 7, 45, "beforeRemoveListeners"], [14, 59, 7, 66], [14, 61, 7, 68, "currentRoutes"], [14, 74, 7, 81], [14, 76, 7, 83, "nextRoutes"], [14, 86, 7, 93], [14, 88, 7, 95, "action"], [14, 94, 7, 101], [14, 99, 7, 106], [15, 4, 8, 2], [15, 8, 8, 8, "nextR<PERSON>e<PERSON>eys"], [15, 21, 8, 21], [15, 24, 8, 24, "nextRoutes"], [15, 34, 8, 34], [15, 35, 8, 35, "map"], [15, 38, 8, 38], [15, 39, 8, 39, "route"], [15, 44, 8, 44], [15, 48, 8, 48, "route"], [15, 53, 8, 53], [15, 54, 8, 54, "key"], [15, 57, 8, 57], [15, 58, 8, 58], [17, 4, 10, 2], [18, 4, 11, 2], [18, 8, 11, 8, "removedRoutes"], [18, 21, 11, 21], [18, 24, 11, 24, "currentRoutes"], [18, 37, 11, 37], [18, 38, 11, 38, "filter"], [18, 44, 11, 44], [18, 45, 11, 45, "route"], [18, 50, 11, 50], [18, 54, 11, 54], [18, 55, 11, 55, "nextR<PERSON>e<PERSON>eys"], [18, 68, 11, 68], [18, 69, 11, 69, "includes"], [18, 77, 11, 77], [18, 78, 11, 78, "route"], [18, 83, 11, 83], [18, 84, 11, 84, "key"], [18, 87, 11, 87], [18, 88, 11, 88], [18, 89, 11, 89], [18, 90, 11, 90, "reverse"], [18, 97, 11, 97], [18, 98, 11, 98], [18, 99, 11, 99], [19, 4, 12, 2], [19, 8, 12, 8, "visitedRouteKeys"], [19, 24, 12, 24], [20, 4, 13, 2], [21, 4, 14, 2, "action"], [21, 10, 14, 8], [21, 11, 14, 9, "VISITED_ROUTE_KEYS"], [21, 29, 14, 27], [21, 30, 14, 28], [21, 34, 14, 32], [21, 38, 14, 36, "Set"], [21, 41, 14, 39], [21, 42, 14, 40], [21, 43, 14, 41], [22, 4, 15, 2], [22, 8, 15, 8, "beforeRemoveAction"], [22, 26, 15, 26], [22, 29, 15, 29], [23, 6, 16, 4], [23, 9, 16, 7, "action"], [23, 15, 16, 13], [24, 6, 17, 4], [24, 7, 17, 5, "VISITED_ROUTE_KEYS"], [24, 25, 17, 23], [24, 28, 17, 26, "visitedRouteKeys"], [25, 4, 18, 2], [25, 5, 18, 3], [26, 4, 19, 2], [26, 9, 19, 7], [26, 13, 19, 13, "route"], [26, 18, 19, 18], [26, 22, 19, 22, "removedRoutes"], [26, 35, 19, 35], [26, 37, 19, 37], [27, 6, 20, 4], [27, 10, 20, 8, "visitedRouteKeys"], [27, 26, 20, 24], [27, 27, 20, 25, "has"], [27, 30, 20, 28], [27, 31, 20, 29, "route"], [27, 36, 20, 34], [27, 37, 20, 35, "key"], [27, 40, 20, 38], [27, 41, 20, 39], [27, 43, 20, 41], [28, 8, 21, 6], [29, 8, 22, 6], [30, 6, 23, 4], [32, 6, 25, 4], [33, 6, 26, 4], [33, 10, 26, 10, "isPrevented"], [33, 21, 26, 21], [33, 24, 26, 24, "beforeRemoveListeners"], [33, 45, 26, 45], [33, 46, 26, 46, "route"], [33, 51, 26, 51], [33, 52, 26, 52, "key"], [33, 55, 26, 55], [33, 56, 26, 56], [33, 59, 26, 59, "beforeRemoveAction"], [33, 77, 26, 77], [33, 78, 26, 78], [34, 6, 27, 4], [34, 10, 27, 8, "isPrevented"], [34, 21, 27, 19], [34, 23, 27, 21], [35, 8, 28, 6], [35, 15, 28, 13], [35, 19, 28, 17], [36, 6, 29, 4], [37, 6, 30, 4, "visitedRouteKeys"], [37, 22, 30, 20], [37, 23, 30, 21, "add"], [37, 26, 30, 24], [37, 27, 30, 25, "route"], [37, 32, 30, 30], [37, 33, 30, 31, "key"], [37, 36, 30, 34], [37, 37, 30, 35], [38, 6, 31, 4], [38, 10, 31, 10, "event"], [38, 15, 31, 15], [38, 18, 31, 18, "emitter"], [38, 25, 31, 25], [38, 26, 31, 26, "emit"], [38, 30, 31, 30], [38, 31, 31, 31], [39, 8, 32, 6, "type"], [39, 12, 32, 10], [39, 14, 32, 12], [39, 28, 32, 26], [40, 8, 33, 6, "target"], [40, 14, 33, 12], [40, 16, 33, 14, "route"], [40, 21, 33, 19], [40, 22, 33, 20, "key"], [40, 25, 33, 23], [41, 8, 34, 6, "data"], [41, 12, 34, 10], [41, 14, 34, 12], [42, 10, 35, 8, "action"], [42, 16, 35, 14], [42, 18, 35, 16, "beforeRemoveAction"], [43, 8, 36, 6], [43, 9, 36, 7], [44, 8, 37, 6, "canPreventDefault"], [44, 25, 37, 23], [44, 27, 37, 25], [45, 6, 38, 4], [45, 7, 38, 5], [45, 8, 38, 6], [46, 6, 39, 4], [46, 10, 39, 8, "event"], [46, 15, 39, 13], [46, 16, 39, 14, "defaultPrevented"], [46, 32, 39, 30], [46, 34, 39, 32], [47, 8, 40, 6], [47, 15, 40, 13], [47, 19, 40, 17], [48, 6, 41, 4], [49, 4, 42, 2], [50, 4, 43, 2], [50, 11, 43, 9], [50, 16, 43, 14], [51, 2, 44, 0], [51, 3, 44, 1], [52, 2, 44, 2, "exports"], [52, 9, 44, 2], [52, 10, 44, 2, "shouldPreventRemove"], [52, 29, 44, 2], [52, 32, 44, 2, "shouldPreventRemove"], [52, 51, 44, 2], [53, 2, 45, 7], [53, 11, 45, 16, "useOnPreventRemove"], [53, 29, 45, 34, "useOnPreventRemove"], [53, 30, 45, 34, "_ref"], [53, 34, 45, 34], [53, 36, 49, 3], [54, 4, 49, 3], [54, 8, 46, 2, "getState"], [54, 16, 46, 10], [54, 19, 46, 10, "_ref"], [54, 23, 46, 10], [54, 24, 46, 2, "getState"], [54, 32, 46, 10], [55, 6, 47, 2, "emitter"], [55, 13, 47, 9], [55, 16, 47, 9, "_ref"], [55, 20, 47, 9], [55, 21, 47, 2, "emitter"], [55, 28, 47, 9], [56, 6, 48, 2, "beforeRemoveListeners"], [56, 27, 48, 23], [56, 30, 48, 23, "_ref"], [56, 34, 48, 23], [56, 35, 48, 2, "beforeRemoveListeners"], [56, 56, 48, 23], [57, 4, 50, 2], [57, 8, 50, 2, "_React$useContext"], [57, 25, 50, 2], [57, 28, 52, 6, "React"], [57, 33, 52, 11], [57, 34, 52, 12, "useContext"], [57, 44, 52, 22], [57, 45, 52, 23, "NavigationBuilderContext"], [57, 95, 52, 47], [57, 96, 52, 48], [58, 6, 51, 4, "addKeyedListener"], [58, 22, 51, 20], [58, 25, 51, 20, "_React$useContext"], [58, 42, 51, 20], [58, 43, 51, 4, "addKeyedListener"], [58, 59, 51, 20], [59, 4, 53, 2], [59, 8, 53, 8, "route"], [59, 13, 53, 13], [59, 16, 53, 16, "React"], [59, 21, 53, 21], [59, 22, 53, 22, "useContext"], [59, 32, 53, 32], [59, 33, 53, 33, "NavigationRouteContext"], [59, 79, 53, 55], [59, 80, 53, 56], [60, 4, 54, 2], [60, 8, 54, 8, "routeKey"], [60, 16, 54, 16], [60, 19, 54, 19, "route"], [60, 24, 54, 24], [60, 26, 54, 26, "key"], [60, 29, 54, 29], [61, 4, 55, 2, "React"], [61, 9, 55, 7], [61, 10, 55, 8, "useEffect"], [61, 19, 55, 17], [61, 20, 55, 18], [61, 26, 55, 24], [62, 6, 56, 4], [62, 10, 56, 8, "routeKey"], [62, 18, 56, 16], [62, 20, 56, 18], [63, 8, 57, 6], [63, 15, 57, 13, "addKeyedListener"], [63, 31, 57, 29], [63, 34, 57, 32], [63, 48, 57, 46], [63, 50, 57, 48, "routeKey"], [63, 58, 57, 56], [63, 60, 57, 58, "action"], [63, 66, 57, 64], [63, 70, 57, 68], [64, 10, 58, 8], [64, 14, 58, 14, "state"], [64, 19, 58, 19], [64, 22, 58, 22, "getState"], [64, 30, 58, 30], [64, 31, 58, 31], [64, 32, 58, 32], [65, 10, 59, 8], [65, 17, 59, 15, "shouldPreventRemove"], [65, 36, 59, 34], [65, 37, 59, 35, "emitter"], [65, 44, 59, 42], [65, 46, 59, 44, "beforeRemoveListeners"], [65, 67, 59, 65], [65, 69, 59, 67, "state"], [65, 74, 59, 72], [65, 75, 59, 73, "routes"], [65, 81, 59, 79], [65, 83, 59, 81], [65, 85, 59, 83], [65, 87, 59, 85, "action"], [65, 93, 59, 91], [65, 94, 59, 92], [66, 8, 60, 6], [66, 9, 60, 7], [66, 10, 60, 8], [67, 6, 61, 4], [68, 4, 62, 2], [68, 5, 62, 3], [68, 7, 62, 5], [68, 8, 62, 6, "addKeyedListener"], [68, 24, 62, 22], [68, 26, 62, 24, "beforeRemoveListeners"], [68, 47, 62, 45], [68, 49, 62, 47, "emitter"], [68, 56, 62, 54], [68, 58, 62, 56, "getState"], [68, 66, 62, 64], [68, 68, 62, 66, "routeKey"], [68, 76, 62, 74], [68, 77, 62, 75], [68, 78, 62, 76], [69, 2, 63, 0], [70, 0, 63, 1], [70, 3]], "functionMap": {"names": ["<global>", "shouldPreventRemove", "nextRoutes.map$argument_0", "currentRoutes.filter$argument_0", "useOnPreventRemove", "React.useEffect$argument_0", "addKeyedListener$argument_2"], "mappings": "AAA;mCCM;uCCC,kBD;6CEG,2CF;CDiC;OIC;kBCU;0DCE;ODG;GDE;CJC"}}, "type": "js/module"}]}