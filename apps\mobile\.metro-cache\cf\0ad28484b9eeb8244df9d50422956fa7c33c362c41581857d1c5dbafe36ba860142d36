{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@react-navigation/elements", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 120, "index": 135}}], "key": "LmqW7jh+SpCzQZMkzh+Awcuawt0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 136}, "end": {"line": 4, "column": 79, "index": 215}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 216}, "end": {"line": 5, "column": 31, "index": 247}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 248}, "end": {"line": 6, "column": 48, "index": 296}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../../utils/ModalPresentationContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 297}, "end": {"line": 7, "column": 83, "index": 380}}], "key": "Df1aec0EkzLZaxqOOhpXHtHsUBw=", "exportNames": ["*"]}}, {"name": "../../utils/useKeyboardManager.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 381}, "end": {"line": 8, "column": 71, "index": 452}}], "key": "EkND9R95hoAvCD6YK6RT3242Jzk=", "exportNames": ["*"]}}, {"name": "./Card.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 453}, "end": {"line": 9, "column": 33, "index": 486}}], "key": "Em7JuTmcBwPftVsB35Ruen1B8xE=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 487}, "end": {"line": 10, "column": 63, "index": 550}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.CardContainer = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _elements = require(_dependencyMap[2], \"@react-navigation/elements\");\n  var _native = require(_dependencyMap[3], \"@react-navigation/native\");\n  var React = _interopRequireWildcard(require(_dependencyMap[4], \"react\"));\n  var _reactNative = require(_dependencyMap[5], \"react-native\");\n  var _ModalPresentationContext = require(_dependencyMap[6], \"../../utils/ModalPresentationContext.js\");\n  var _useKeyboardManager2 = require(_dependencyMap[7], \"../../utils/useKeyboardManager.js\");\n  var _Card = require(_dependencyMap[8], \"./Card.js\");\n  var _jsxRuntime = require(_dependencyMap[9], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var EPSILON = 0.1;\n  function CardContainerInner(_ref) {\n    var interpolationIndex = _ref.interpolationIndex,\n      index = _ref.index,\n      active = _ref.active,\n      opening = _ref.opening,\n      closing = _ref.closing,\n      gesture = _ref.gesture,\n      focused = _ref.focused,\n      modal = _ref.modal,\n      getPreviousScene = _ref.getPreviousScene,\n      getFocusedRoute = _ref.getFocusedRoute,\n      hasAbsoluteFloatHeader = _ref.hasAbsoluteFloatHeader,\n      headerHeight = _ref.headerHeight,\n      onHeaderHeightChange = _ref.onHeaderHeightChange,\n      isParentHeaderShown = _ref.isParentHeaderShown,\n      isNextScreenTransparent = _ref.isNextScreenTransparent,\n      detachCurrentScreen = _ref.detachCurrentScreen,\n      layout = _ref.layout,\n      onCloseRoute = _ref.onCloseRoute,\n      onOpenRoute = _ref.onOpenRoute,\n      onGestureCancel = _ref.onGestureCancel,\n      onGestureEnd = _ref.onGestureEnd,\n      onGestureStart = _ref.onGestureStart,\n      onTransitionEnd = _ref.onTransitionEnd,\n      onTransitionStart = _ref.onTransitionStart,\n      preloaded = _ref.preloaded,\n      renderHeader = _ref.renderHeader,\n      safeAreaInsetBottom = _ref.safeAreaInsetBottom,\n      safeAreaInsetLeft = _ref.safeAreaInsetLeft,\n      safeAreaInsetRight = _ref.safeAreaInsetRight,\n      safeAreaInsetTop = _ref.safeAreaInsetTop,\n      scene = _ref.scene;\n    var _useLocale = (0, _native.useLocale)(),\n      direction = _useLocale.direction;\n    var parentHeaderHeight = React.useContext(_elements.HeaderHeightContext);\n    var _useKeyboardManager = (0, _useKeyboardManager2.useKeyboardManager)(React.useCallback(() => {\n        var _scene$descriptor = scene.descriptor,\n          options = _scene$descriptor.options,\n          navigation = _scene$descriptor.navigation;\n        return navigation.isFocused() && options.keyboardHandlingEnabled !== false;\n      }, [scene.descriptor])),\n      onPageChangeStart = _useKeyboardManager.onPageChangeStart,\n      onPageChangeCancel = _useKeyboardManager.onPageChangeCancel,\n      onPageChangeConfirm = _useKeyboardManager.onPageChangeConfirm;\n    var handleOpen = () => {\n      var route = scene.descriptor.route;\n      onTransitionEnd({\n        route\n      }, false);\n      onOpenRoute({\n        route\n      });\n    };\n    var handleClose = () => {\n      var route = scene.descriptor.route;\n      onTransitionEnd({\n        route\n      }, true);\n      onCloseRoute({\n        route\n      });\n    };\n    var handleGestureBegin = () => {\n      var route = scene.descriptor.route;\n      onPageChangeStart();\n      onGestureStart({\n        route\n      });\n    };\n    var handleGestureCanceled = () => {\n      var route = scene.descriptor.route;\n      onPageChangeCancel();\n      onGestureCancel({\n        route\n      });\n    };\n    var handleGestureEnd = () => {\n      var route = scene.descriptor.route;\n      onGestureEnd({\n        route\n      });\n    };\n    var handleTransition = _ref2 => {\n      var closing = _ref2.closing,\n        gesture = _ref2.gesture;\n      var route = scene.descriptor.route;\n      if (!gesture) {\n        onPageChangeConfirm?.(true);\n      } else if (active && closing) {\n        onPageChangeConfirm?.(false);\n      } else {\n        onPageChangeCancel?.();\n      }\n      onTransitionStart?.({\n        route\n      }, closing);\n    };\n    var insets = {\n      top: safeAreaInsetTop,\n      right: safeAreaInsetRight,\n      bottom: safeAreaInsetBottom,\n      left: safeAreaInsetLeft\n    };\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors;\n    var _React$useState = React.useState('box-none'),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      pointerEvents = _React$useState2[0],\n      setPointerEvents = _React$useState2[1];\n    React.useEffect(() => {\n      var listener = scene.progress.next?.addListener?.(_ref3 => {\n        var value = _ref3.value;\n        setPointerEvents(value <= EPSILON ? 'box-none' : 'none');\n      });\n      return () => {\n        if (listener) {\n          scene.progress.next?.removeListener?.(listener);\n        }\n      };\n    }, [pointerEvents, scene.progress.next]);\n    var _scene$descriptor$opt = scene.descriptor.options,\n      presentation = _scene$descriptor$opt.presentation,\n      animation = _scene$descriptor$opt.animation,\n      cardOverlay = _scene$descriptor$opt.cardOverlay,\n      cardOverlayEnabled = _scene$descriptor$opt.cardOverlayEnabled,\n      cardShadowEnabled = _scene$descriptor$opt.cardShadowEnabled,\n      cardStyle = _scene$descriptor$opt.cardStyle,\n      cardStyleInterpolator = _scene$descriptor$opt.cardStyleInterpolator,\n      gestureDirection = _scene$descriptor$opt.gestureDirection,\n      gestureEnabled = _scene$descriptor$opt.gestureEnabled,\n      gestureResponseDistance = _scene$descriptor$opt.gestureResponseDistance,\n      gestureVelocityImpact = _scene$descriptor$opt.gestureVelocityImpact,\n      headerMode = _scene$descriptor$opt.headerMode,\n      headerShown = _scene$descriptor$opt.headerShown,\n      transitionSpec = _scene$descriptor$opt.transitionSpec;\n    var _useLinkBuilder = (0, _native.useLinkBuilder)(),\n      buildHref = _useLinkBuilder.buildHref;\n    var previousScene = getPreviousScene({\n      route: scene.descriptor.route\n    });\n    var backTitle;\n    var href;\n    if (previousScene) {\n      var _previousScene$descri = previousScene.descriptor,\n        options = _previousScene$descri.options,\n        route = _previousScene$descri.route;\n      backTitle = (0, _elements.getHeaderTitle)(options, route.name);\n      href = buildHref(route.name, route.params);\n    }\n    var canGoBack = previousScene != null;\n    var headerBack = React.useMemo(() => {\n      if (canGoBack) {\n        return {\n          href,\n          title: backTitle\n        };\n      }\n      return undefined;\n    }, [canGoBack, backTitle, href]);\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Card.Card, {\n      interpolationIndex: interpolationIndex,\n      gestureDirection: gestureDirection,\n      layout: layout,\n      insets: insets,\n      direction: direction,\n      gesture: gesture,\n      current: scene.progress.current,\n      next: scene.progress.next,\n      opening: opening,\n      closing: closing,\n      onOpen: handleOpen,\n      onClose: handleClose,\n      overlay: cardOverlay,\n      overlayEnabled: cardOverlayEnabled,\n      shadowEnabled: cardShadowEnabled,\n      onTransition: handleTransition,\n      onGestureBegin: handleGestureBegin,\n      onGestureCanceled: handleGestureCanceled,\n      onGestureEnd: handleGestureEnd,\n      gestureEnabled: index === 0 ? false : gestureEnabled,\n      gestureResponseDistance: gestureResponseDistance,\n      gestureVelocityImpact: gestureVelocityImpact,\n      transitionSpec: transitionSpec,\n      styleInterpolator: cardStyleInterpolator,\n      \"aria-hidden\": !focused,\n      pointerEvents: active ? 'box-none' : pointerEvents,\n      pageOverflowEnabled: headerMode !== 'float' && presentation !== 'modal',\n      preloaded: preloaded,\n      containerStyle: hasAbsoluteFloatHeader && headerMode !== 'screen' ? {\n        marginTop: headerHeight\n      } : null,\n      contentStyle: [{\n        backgroundColor: presentation === 'transparentModal' ? 'transparent' : colors.background\n      }, cardStyle],\n      style: [{\n        // This is necessary to avoid unfocused larger pages increasing scroll area\n        // The issue can be seen on the web when a smaller screen is pushed over a larger one\n        overflow: active ? undefined : 'hidden',\n        display:\n        // Hide unfocused screens when animation isn't enabled\n        // This is also necessary for a11y on web\n        animation === 'none' && isNextScreenTransparent === false && detachCurrentScreen !== false && !focused ? 'none' : 'flex'\n      }, _reactNative.StyleSheet.absoluteFill],\n      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_ModalPresentationContext.ModalPresentationContext.Provider, {\n          value: modal,\n          children: [headerMode !== 'float' ? renderHeader({\n            mode: 'screen',\n            layout,\n            scenes: [previousScene, scene],\n            getPreviousScene,\n            getFocusedRoute,\n            onContentHeightChange: onHeaderHeightChange,\n            style: styles.header\n          }) : null, /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n            style: styles.scene,\n            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.HeaderBackContext.Provider, {\n              value: headerBack,\n              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.HeaderShownContext.Provider, {\n                value: isParentHeaderShown || headerShown !== false,\n                children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.HeaderHeightContext.Provider, {\n                  value: headerShown !== false ? headerHeight : parentHeaderHeight ?? 0,\n                  children: scene.descriptor.render()\n                })\n              })\n            })\n          })]\n        })\n      })\n    });\n  }\n  var CardContainer = exports.CardContainer = /*#__PURE__*/React.memo(CardContainerInner);\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flex: 1\n    },\n    header: {\n      zIndex: 1\n    },\n    scene: {\n      flex: 1\n    }\n  });\n});", "lineCount": 264, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "CardContainer"], [8, 23, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_elements"], [10, 15, 3, 0], [10, 18, 3, 0, "require"], [10, 25, 3, 0], [10, 26, 3, 0, "_dependencyMap"], [10, 40, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_native"], [11, 13, 4, 0], [11, 16, 4, 0, "require"], [11, 23, 4, 0], [11, 24, 4, 0, "_dependencyMap"], [11, 38, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "React"], [12, 11, 5, 0], [12, 14, 5, 0, "_interopRequireWildcard"], [12, 37, 5, 0], [12, 38, 5, 0, "require"], [12, 45, 5, 0], [12, 46, 5, 0, "_dependencyMap"], [12, 60, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_reactNative"], [13, 18, 6, 0], [13, 21, 6, 0, "require"], [13, 28, 6, 0], [13, 29, 6, 0, "_dependencyMap"], [13, 43, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_ModalPresentationContext"], [14, 31, 7, 0], [14, 34, 7, 0, "require"], [14, 41, 7, 0], [14, 42, 7, 0, "_dependencyMap"], [14, 56, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_useKeyboardManager2"], [15, 26, 8, 0], [15, 29, 8, 0, "require"], [15, 36, 8, 0], [15, 37, 8, 0, "_dependencyMap"], [15, 51, 8, 0], [16, 2, 9, 0], [16, 6, 9, 0, "_Card"], [16, 11, 9, 0], [16, 14, 9, 0, "require"], [16, 21, 9, 0], [16, 22, 9, 0, "_dependencyMap"], [16, 36, 9, 0], [17, 2, 10, 0], [17, 6, 10, 0, "_jsxRuntime"], [17, 17, 10, 0], [17, 20, 10, 0, "require"], [17, 27, 10, 0], [17, 28, 10, 0, "_dependencyMap"], [17, 42, 10, 0], [18, 2, 10, 63], [18, 11, 10, 63, "_interopRequireWildcard"], [18, 35, 10, 63, "e"], [18, 36, 10, 63], [18, 38, 10, 63, "t"], [18, 39, 10, 63], [18, 68, 10, 63, "WeakMap"], [18, 75, 10, 63], [18, 81, 10, 63, "r"], [18, 82, 10, 63], [18, 89, 10, 63, "WeakMap"], [18, 96, 10, 63], [18, 100, 10, 63, "n"], [18, 101, 10, 63], [18, 108, 10, 63, "WeakMap"], [18, 115, 10, 63], [18, 127, 10, 63, "_interopRequireWildcard"], [18, 150, 10, 63], [18, 162, 10, 63, "_interopRequireWildcard"], [18, 163, 10, 63, "e"], [18, 164, 10, 63], [18, 166, 10, 63, "t"], [18, 167, 10, 63], [18, 176, 10, 63, "t"], [18, 177, 10, 63], [18, 181, 10, 63, "e"], [18, 182, 10, 63], [18, 186, 10, 63, "e"], [18, 187, 10, 63], [18, 188, 10, 63, "__esModule"], [18, 198, 10, 63], [18, 207, 10, 63, "e"], [18, 208, 10, 63], [18, 214, 10, 63, "o"], [18, 215, 10, 63], [18, 217, 10, 63, "i"], [18, 218, 10, 63], [18, 220, 10, 63, "f"], [18, 221, 10, 63], [18, 226, 10, 63, "__proto__"], [18, 235, 10, 63], [18, 243, 10, 63, "default"], [18, 250, 10, 63], [18, 252, 10, 63, "e"], [18, 253, 10, 63], [18, 270, 10, 63, "e"], [18, 271, 10, 63], [18, 294, 10, 63, "e"], [18, 295, 10, 63], [18, 320, 10, 63, "e"], [18, 321, 10, 63], [18, 330, 10, 63, "f"], [18, 331, 10, 63], [18, 337, 10, 63, "o"], [18, 338, 10, 63], [18, 341, 10, 63, "t"], [18, 342, 10, 63], [18, 345, 10, 63, "n"], [18, 346, 10, 63], [18, 349, 10, 63, "r"], [18, 350, 10, 63], [18, 358, 10, 63, "o"], [18, 359, 10, 63], [18, 360, 10, 63, "has"], [18, 363, 10, 63], [18, 364, 10, 63, "e"], [18, 365, 10, 63], [18, 375, 10, 63, "o"], [18, 376, 10, 63], [18, 377, 10, 63, "get"], [18, 380, 10, 63], [18, 381, 10, 63, "e"], [18, 382, 10, 63], [18, 385, 10, 63, "o"], [18, 386, 10, 63], [18, 387, 10, 63, "set"], [18, 390, 10, 63], [18, 391, 10, 63, "e"], [18, 392, 10, 63], [18, 394, 10, 63, "f"], [18, 395, 10, 63], [18, 409, 10, 63, "_t"], [18, 411, 10, 63], [18, 415, 10, 63, "e"], [18, 416, 10, 63], [18, 432, 10, 63, "_t"], [18, 434, 10, 63], [18, 441, 10, 63, "hasOwnProperty"], [18, 455, 10, 63], [18, 456, 10, 63, "call"], [18, 460, 10, 63], [18, 461, 10, 63, "e"], [18, 462, 10, 63], [18, 464, 10, 63, "_t"], [18, 466, 10, 63], [18, 473, 10, 63, "i"], [18, 474, 10, 63], [18, 478, 10, 63, "o"], [18, 479, 10, 63], [18, 482, 10, 63, "Object"], [18, 488, 10, 63], [18, 489, 10, 63, "defineProperty"], [18, 503, 10, 63], [18, 508, 10, 63, "Object"], [18, 514, 10, 63], [18, 515, 10, 63, "getOwnPropertyDescriptor"], [18, 539, 10, 63], [18, 540, 10, 63, "e"], [18, 541, 10, 63], [18, 543, 10, 63, "_t"], [18, 545, 10, 63], [18, 552, 10, 63, "i"], [18, 553, 10, 63], [18, 554, 10, 63, "get"], [18, 557, 10, 63], [18, 561, 10, 63, "i"], [18, 562, 10, 63], [18, 563, 10, 63, "set"], [18, 566, 10, 63], [18, 570, 10, 63, "o"], [18, 571, 10, 63], [18, 572, 10, 63, "f"], [18, 573, 10, 63], [18, 575, 10, 63, "_t"], [18, 577, 10, 63], [18, 579, 10, 63, "i"], [18, 580, 10, 63], [18, 584, 10, 63, "f"], [18, 585, 10, 63], [18, 586, 10, 63, "_t"], [18, 588, 10, 63], [18, 592, 10, 63, "e"], [18, 593, 10, 63], [18, 594, 10, 63, "_t"], [18, 596, 10, 63], [18, 607, 10, 63, "f"], [18, 608, 10, 63], [18, 613, 10, 63, "e"], [18, 614, 10, 63], [18, 616, 10, 63, "t"], [18, 617, 10, 63], [19, 2, 11, 0], [19, 6, 11, 6, "EPSILON"], [19, 13, 11, 13], [19, 16, 11, 16], [19, 19, 11, 19], [20, 2, 12, 0], [20, 11, 12, 9, "CardContainerInner"], [20, 29, 12, 27, "CardContainerInner"], [20, 30, 12, 27, "_ref"], [20, 34, 12, 27], [20, 36, 44, 3], [21, 4, 44, 3], [21, 8, 13, 2, "interpolationIndex"], [21, 26, 13, 20], [21, 29, 13, 20, "_ref"], [21, 33, 13, 20], [21, 34, 13, 2, "interpolationIndex"], [21, 52, 13, 20], [22, 6, 14, 2, "index"], [22, 11, 14, 7], [22, 14, 14, 7, "_ref"], [22, 18, 14, 7], [22, 19, 14, 2, "index"], [22, 24, 14, 7], [23, 6, 15, 2, "active"], [23, 12, 15, 8], [23, 15, 15, 8, "_ref"], [23, 19, 15, 8], [23, 20, 15, 2, "active"], [23, 26, 15, 8], [24, 6, 16, 2, "opening"], [24, 13, 16, 9], [24, 16, 16, 9, "_ref"], [24, 20, 16, 9], [24, 21, 16, 2, "opening"], [24, 28, 16, 9], [25, 6, 17, 2, "closing"], [25, 13, 17, 9], [25, 16, 17, 9, "_ref"], [25, 20, 17, 9], [25, 21, 17, 2, "closing"], [25, 28, 17, 9], [26, 6, 18, 2, "gesture"], [26, 13, 18, 9], [26, 16, 18, 9, "_ref"], [26, 20, 18, 9], [26, 21, 18, 2, "gesture"], [26, 28, 18, 9], [27, 6, 19, 2, "focused"], [27, 13, 19, 9], [27, 16, 19, 9, "_ref"], [27, 20, 19, 9], [27, 21, 19, 2, "focused"], [27, 28, 19, 9], [28, 6, 20, 2, "modal"], [28, 11, 20, 7], [28, 14, 20, 7, "_ref"], [28, 18, 20, 7], [28, 19, 20, 2, "modal"], [28, 24, 20, 7], [29, 6, 21, 2, "getPreviousScene"], [29, 22, 21, 18], [29, 25, 21, 18, "_ref"], [29, 29, 21, 18], [29, 30, 21, 2, "getPreviousScene"], [29, 46, 21, 18], [30, 6, 22, 2, "getFocusedRoute"], [30, 21, 22, 17], [30, 24, 22, 17, "_ref"], [30, 28, 22, 17], [30, 29, 22, 2, "getFocusedRoute"], [30, 44, 22, 17], [31, 6, 23, 2, "hasAbsoluteFloatHeader"], [31, 28, 23, 24], [31, 31, 23, 24, "_ref"], [31, 35, 23, 24], [31, 36, 23, 2, "hasAbsoluteFloatHeader"], [31, 58, 23, 24], [32, 6, 24, 2, "headerHeight"], [32, 18, 24, 14], [32, 21, 24, 14, "_ref"], [32, 25, 24, 14], [32, 26, 24, 2, "headerHeight"], [32, 38, 24, 14], [33, 6, 25, 2, "onHeaderHeightChange"], [33, 26, 25, 22], [33, 29, 25, 22, "_ref"], [33, 33, 25, 22], [33, 34, 25, 2, "onHeaderHeightChange"], [33, 54, 25, 22], [34, 6, 26, 2, "isParentHeaderShown"], [34, 25, 26, 21], [34, 28, 26, 21, "_ref"], [34, 32, 26, 21], [34, 33, 26, 2, "isParentHeaderShown"], [34, 52, 26, 21], [35, 6, 27, 2, "isNextScreenTransparent"], [35, 29, 27, 25], [35, 32, 27, 25, "_ref"], [35, 36, 27, 25], [35, 37, 27, 2, "isNextScreenTransparent"], [35, 60, 27, 25], [36, 6, 28, 2, "detachCurrentScreen"], [36, 25, 28, 21], [36, 28, 28, 21, "_ref"], [36, 32, 28, 21], [36, 33, 28, 2, "detachCurrentScreen"], [36, 52, 28, 21], [37, 6, 29, 2, "layout"], [37, 12, 29, 8], [37, 15, 29, 8, "_ref"], [37, 19, 29, 8], [37, 20, 29, 2, "layout"], [37, 26, 29, 8], [38, 6, 30, 2, "onCloseRoute"], [38, 18, 30, 14], [38, 21, 30, 14, "_ref"], [38, 25, 30, 14], [38, 26, 30, 2, "onCloseRoute"], [38, 38, 30, 14], [39, 6, 31, 2, "onOpenRoute"], [39, 17, 31, 13], [39, 20, 31, 13, "_ref"], [39, 24, 31, 13], [39, 25, 31, 2, "onOpenRoute"], [39, 36, 31, 13], [40, 6, 32, 2, "onGestureCancel"], [40, 21, 32, 17], [40, 24, 32, 17, "_ref"], [40, 28, 32, 17], [40, 29, 32, 2, "onGestureCancel"], [40, 44, 32, 17], [41, 6, 33, 2, "onGestureEnd"], [41, 18, 33, 14], [41, 21, 33, 14, "_ref"], [41, 25, 33, 14], [41, 26, 33, 2, "onGestureEnd"], [41, 38, 33, 14], [42, 6, 34, 2, "onGestureStart"], [42, 20, 34, 16], [42, 23, 34, 16, "_ref"], [42, 27, 34, 16], [42, 28, 34, 2, "onGestureStart"], [42, 42, 34, 16], [43, 6, 35, 2, "onTransitionEnd"], [43, 21, 35, 17], [43, 24, 35, 17, "_ref"], [43, 28, 35, 17], [43, 29, 35, 2, "onTransitionEnd"], [43, 44, 35, 17], [44, 6, 36, 2, "onTransitionStart"], [44, 23, 36, 19], [44, 26, 36, 19, "_ref"], [44, 30, 36, 19], [44, 31, 36, 2, "onTransitionStart"], [44, 48, 36, 19], [45, 6, 37, 2, "preloaded"], [45, 15, 37, 11], [45, 18, 37, 11, "_ref"], [45, 22, 37, 11], [45, 23, 37, 2, "preloaded"], [45, 32, 37, 11], [46, 6, 38, 2, "renderHeader"], [46, 18, 38, 14], [46, 21, 38, 14, "_ref"], [46, 25, 38, 14], [46, 26, 38, 2, "renderHeader"], [46, 38, 38, 14], [47, 6, 39, 2, "safeAreaInsetBottom"], [47, 25, 39, 21], [47, 28, 39, 21, "_ref"], [47, 32, 39, 21], [47, 33, 39, 2, "safeAreaInsetBottom"], [47, 52, 39, 21], [48, 6, 40, 2, "safeAreaInsetLeft"], [48, 23, 40, 19], [48, 26, 40, 19, "_ref"], [48, 30, 40, 19], [48, 31, 40, 2, "safeAreaInsetLeft"], [48, 48, 40, 19], [49, 6, 41, 2, "safeAreaInsetRight"], [49, 24, 41, 20], [49, 27, 41, 20, "_ref"], [49, 31, 41, 20], [49, 32, 41, 2, "safeAreaInsetRight"], [49, 50, 41, 20], [50, 6, 42, 2, "safeAreaInsetTop"], [50, 22, 42, 18], [50, 25, 42, 18, "_ref"], [50, 29, 42, 18], [50, 30, 42, 2, "safeAreaInsetTop"], [50, 46, 42, 18], [51, 6, 43, 2, "scene"], [51, 11, 43, 7], [51, 14, 43, 7, "_ref"], [51, 18, 43, 7], [51, 19, 43, 2, "scene"], [51, 24, 43, 7], [52, 4, 45, 2], [52, 8, 45, 2, "_useLocale"], [52, 18, 45, 2], [52, 21, 47, 6], [52, 25, 47, 6, "useLocale"], [52, 42, 47, 15], [52, 44, 47, 16], [52, 45, 47, 17], [53, 6, 46, 4, "direction"], [53, 15, 46, 13], [53, 18, 46, 13, "_useLocale"], [53, 28, 46, 13], [53, 29, 46, 4, "direction"], [53, 38, 46, 13], [54, 4, 48, 2], [54, 8, 48, 8, "parentHeaderHeight"], [54, 26, 48, 26], [54, 29, 48, 29, "React"], [54, 34, 48, 34], [54, 35, 48, 35, "useContext"], [54, 45, 48, 45], [54, 46, 48, 46, "HeaderHeightContext"], [54, 75, 48, 65], [54, 76, 48, 66], [55, 4, 49, 2], [55, 8, 49, 2, "_useKeyboardManager"], [55, 27, 49, 2], [55, 30, 53, 6], [55, 34, 53, 6, "useKeyboardManager"], [55, 73, 53, 24], [55, 75, 53, 25, "React"], [55, 80, 53, 30], [55, 81, 53, 31, "useCallback"], [55, 92, 53, 42], [55, 93, 53, 43], [55, 99, 53, 49], [56, 8, 54, 4], [56, 12, 54, 4, "_scene$descriptor"], [56, 29, 54, 4], [56, 32, 57, 8, "scene"], [56, 37, 57, 13], [56, 38, 57, 14, "descriptor"], [56, 48, 57, 24], [57, 10, 55, 6, "options"], [57, 17, 55, 13], [57, 20, 55, 13, "_scene$descriptor"], [57, 37, 55, 13], [57, 38, 55, 6, "options"], [57, 45, 55, 13], [58, 10, 56, 6, "navigation"], [58, 20, 56, 16], [58, 23, 56, 16, "_scene$descriptor"], [58, 40, 56, 16], [58, 41, 56, 6, "navigation"], [58, 51, 56, 16], [59, 8, 58, 4], [59, 15, 58, 11, "navigation"], [59, 25, 58, 21], [59, 26, 58, 22, "isFocused"], [59, 35, 58, 31], [59, 36, 58, 32], [59, 37, 58, 33], [59, 41, 58, 37, "options"], [59, 48, 58, 44], [59, 49, 58, 45, "keyboardHandlingEnabled"], [59, 72, 58, 68], [59, 77, 58, 73], [59, 82, 58, 78], [60, 6, 59, 2], [60, 7, 59, 3], [60, 9, 59, 5], [60, 10, 59, 6, "scene"], [60, 15, 59, 11], [60, 16, 59, 12, "descriptor"], [60, 26, 59, 22], [60, 27, 59, 23], [60, 28, 59, 24], [60, 29, 59, 25], [61, 6, 50, 4, "onPageChangeStart"], [61, 23, 50, 21], [61, 26, 50, 21, "_useKeyboardManager"], [61, 45, 50, 21], [61, 46, 50, 4, "onPageChangeStart"], [61, 63, 50, 21], [62, 6, 51, 4, "onPageChangeCancel"], [62, 24, 51, 22], [62, 27, 51, 22, "_useKeyboardManager"], [62, 46, 51, 22], [62, 47, 51, 4, "onPageChangeCancel"], [62, 65, 51, 22], [63, 6, 52, 4, "onPageChangeConfirm"], [63, 25, 52, 23], [63, 28, 52, 23, "_useKeyboardManager"], [63, 47, 52, 23], [63, 48, 52, 4, "onPageChangeConfirm"], [63, 67, 52, 23], [64, 4, 60, 2], [64, 8, 60, 8, "handleOpen"], [64, 18, 60, 18], [64, 21, 60, 21, "handleOpen"], [64, 22, 60, 21], [64, 27, 60, 27], [65, 6, 61, 4], [65, 10, 62, 6, "route"], [65, 15, 62, 11], [65, 18, 63, 8, "scene"], [65, 23, 63, 13], [65, 24, 63, 14, "descriptor"], [65, 34, 63, 24], [65, 35, 62, 6, "route"], [65, 40, 62, 11], [66, 6, 64, 4, "onTransitionEnd"], [66, 21, 64, 19], [66, 22, 64, 20], [67, 8, 65, 6, "route"], [68, 6, 66, 4], [68, 7, 66, 5], [68, 9, 66, 7], [68, 14, 66, 12], [68, 15, 66, 13], [69, 6, 67, 4, "onOpenRoute"], [69, 17, 67, 15], [69, 18, 67, 16], [70, 8, 68, 6, "route"], [71, 6, 69, 4], [71, 7, 69, 5], [71, 8, 69, 6], [72, 4, 70, 2], [72, 5, 70, 3], [73, 4, 71, 2], [73, 8, 71, 8, "handleClose"], [73, 19, 71, 19], [73, 22, 71, 22, "handleClose"], [73, 23, 71, 22], [73, 28, 71, 28], [74, 6, 72, 4], [74, 10, 73, 6, "route"], [74, 15, 73, 11], [74, 18, 74, 8, "scene"], [74, 23, 74, 13], [74, 24, 74, 14, "descriptor"], [74, 34, 74, 24], [74, 35, 73, 6, "route"], [74, 40, 73, 11], [75, 6, 75, 4, "onTransitionEnd"], [75, 21, 75, 19], [75, 22, 75, 20], [76, 8, 76, 6, "route"], [77, 6, 77, 4], [77, 7, 77, 5], [77, 9, 77, 7], [77, 13, 77, 11], [77, 14, 77, 12], [78, 6, 78, 4, "onCloseRoute"], [78, 18, 78, 16], [78, 19, 78, 17], [79, 8, 79, 6, "route"], [80, 6, 80, 4], [80, 7, 80, 5], [80, 8, 80, 6], [81, 4, 81, 2], [81, 5, 81, 3], [82, 4, 82, 2], [82, 8, 82, 8, "handleGestureBegin"], [82, 26, 82, 26], [82, 29, 82, 29, "handleGestureBegin"], [82, 30, 82, 29], [82, 35, 82, 35], [83, 6, 83, 4], [83, 10, 84, 6, "route"], [83, 15, 84, 11], [83, 18, 85, 8, "scene"], [83, 23, 85, 13], [83, 24, 85, 14, "descriptor"], [83, 34, 85, 24], [83, 35, 84, 6, "route"], [83, 40, 84, 11], [84, 6, 86, 4, "onPageChangeStart"], [84, 23, 86, 21], [84, 24, 86, 22], [84, 25, 86, 23], [85, 6, 87, 4, "onGestureStart"], [85, 20, 87, 18], [85, 21, 87, 19], [86, 8, 88, 6, "route"], [87, 6, 89, 4], [87, 7, 89, 5], [87, 8, 89, 6], [88, 4, 90, 2], [88, 5, 90, 3], [89, 4, 91, 2], [89, 8, 91, 8, "handleGestureCanceled"], [89, 29, 91, 29], [89, 32, 91, 32, "handleGestureCanceled"], [89, 33, 91, 32], [89, 38, 91, 38], [90, 6, 92, 4], [90, 10, 93, 6, "route"], [90, 15, 93, 11], [90, 18, 94, 8, "scene"], [90, 23, 94, 13], [90, 24, 94, 14, "descriptor"], [90, 34, 94, 24], [90, 35, 93, 6, "route"], [90, 40, 93, 11], [91, 6, 95, 4, "onPageChangeCancel"], [91, 24, 95, 22], [91, 25, 95, 23], [91, 26, 95, 24], [92, 6, 96, 4, "onGestureCancel"], [92, 21, 96, 19], [92, 22, 96, 20], [93, 8, 97, 6, "route"], [94, 6, 98, 4], [94, 7, 98, 5], [94, 8, 98, 6], [95, 4, 99, 2], [95, 5, 99, 3], [96, 4, 100, 2], [96, 8, 100, 8, "handleGestureEnd"], [96, 24, 100, 24], [96, 27, 100, 27, "handleGestureEnd"], [96, 28, 100, 27], [96, 33, 100, 33], [97, 6, 101, 4], [97, 10, 102, 6, "route"], [97, 15, 102, 11], [97, 18, 103, 8, "scene"], [97, 23, 103, 13], [97, 24, 103, 14, "descriptor"], [97, 34, 103, 24], [97, 35, 102, 6, "route"], [97, 40, 102, 11], [98, 6, 104, 4, "onGestureEnd"], [98, 18, 104, 16], [98, 19, 104, 17], [99, 8, 105, 6, "route"], [100, 6, 106, 4], [100, 7, 106, 5], [100, 8, 106, 6], [101, 4, 107, 2], [101, 5, 107, 3], [102, 4, 108, 2], [102, 8, 108, 8, "handleTransition"], [102, 24, 108, 24], [102, 27, 108, 27, "_ref2"], [102, 32, 108, 27], [102, 36, 111, 8], [103, 6, 111, 8], [103, 10, 109, 4, "closing"], [103, 17, 109, 11], [103, 20, 109, 11, "_ref2"], [103, 25, 109, 11], [103, 26, 109, 4, "closing"], [103, 33, 109, 11], [104, 8, 110, 4, "gesture"], [104, 15, 110, 11], [104, 18, 110, 11, "_ref2"], [104, 23, 110, 11], [104, 24, 110, 4, "gesture"], [104, 31, 110, 11], [105, 6, 112, 4], [105, 10, 113, 6, "route"], [105, 15, 113, 11], [105, 18, 114, 8, "scene"], [105, 23, 114, 13], [105, 24, 114, 14, "descriptor"], [105, 34, 114, 24], [105, 35, 113, 6, "route"], [105, 40, 113, 11], [106, 6, 115, 4], [106, 10, 115, 8], [106, 11, 115, 9, "gesture"], [106, 18, 115, 16], [106, 20, 115, 18], [107, 8, 116, 6, "onPageChangeConfirm"], [107, 27, 116, 25], [107, 30, 116, 28], [107, 34, 116, 32], [107, 35, 116, 33], [108, 6, 117, 4], [108, 7, 117, 5], [108, 13, 117, 11], [108, 17, 117, 15, "active"], [108, 23, 117, 21], [108, 27, 117, 25, "closing"], [108, 34, 117, 32], [108, 36, 117, 34], [109, 8, 118, 6, "onPageChangeConfirm"], [109, 27, 118, 25], [109, 30, 118, 28], [109, 35, 118, 33], [109, 36, 118, 34], [110, 6, 119, 4], [110, 7, 119, 5], [110, 13, 119, 11], [111, 8, 120, 6, "onPageChangeCancel"], [111, 26, 120, 24], [111, 29, 120, 27], [111, 30, 120, 28], [112, 6, 121, 4], [113, 6, 122, 4, "onTransitionStart"], [113, 23, 122, 21], [113, 26, 122, 24], [114, 8, 123, 6, "route"], [115, 6, 124, 4], [115, 7, 124, 5], [115, 9, 124, 7, "closing"], [115, 16, 124, 14], [115, 17, 124, 15], [116, 4, 125, 2], [116, 5, 125, 3], [117, 4, 126, 2], [117, 8, 126, 8, "insets"], [117, 14, 126, 14], [117, 17, 126, 17], [118, 6, 127, 4, "top"], [118, 9, 127, 7], [118, 11, 127, 9, "safeAreaInsetTop"], [118, 27, 127, 25], [119, 6, 128, 4, "right"], [119, 11, 128, 9], [119, 13, 128, 11, "safeAreaInsetRight"], [119, 31, 128, 29], [120, 6, 129, 4, "bottom"], [120, 12, 129, 10], [120, 14, 129, 12, "safeAreaInsetBottom"], [120, 33, 129, 31], [121, 6, 130, 4, "left"], [121, 10, 130, 8], [121, 12, 130, 10, "safeAreaInsetLeft"], [122, 4, 131, 2], [122, 5, 131, 3], [123, 4, 132, 2], [123, 8, 132, 2, "_useTheme"], [123, 17, 132, 2], [123, 20, 134, 6], [123, 24, 134, 6, "useTheme"], [123, 40, 134, 14], [123, 42, 134, 15], [123, 43, 134, 16], [124, 6, 133, 4, "colors"], [124, 12, 133, 10], [124, 15, 133, 10, "_useTheme"], [124, 24, 133, 10], [124, 25, 133, 4, "colors"], [124, 31, 133, 10], [125, 4, 135, 2], [125, 8, 135, 2, "_React$useState"], [125, 23, 135, 2], [125, 26, 135, 44, "React"], [125, 31, 135, 49], [125, 32, 135, 50, "useState"], [125, 40, 135, 58], [125, 41, 135, 59], [125, 51, 135, 69], [125, 52, 135, 70], [126, 6, 135, 70, "_React$useState2"], [126, 22, 135, 70], [126, 29, 135, 70, "_slicedToArray2"], [126, 44, 135, 70], [126, 45, 135, 70, "default"], [126, 52, 135, 70], [126, 54, 135, 70, "_React$useState"], [126, 69, 135, 70], [127, 6, 135, 9, "pointerEvents"], [127, 19, 135, 22], [127, 22, 135, 22, "_React$useState2"], [127, 38, 135, 22], [128, 6, 135, 24, "setPointerEvents"], [128, 22, 135, 40], [128, 25, 135, 40, "_React$useState2"], [128, 41, 135, 40], [129, 4, 136, 2, "React"], [129, 9, 136, 7], [129, 10, 136, 8, "useEffect"], [129, 19, 136, 17], [129, 20, 136, 18], [129, 26, 136, 24], [130, 6, 137, 4], [130, 10, 137, 10, "listener"], [130, 18, 137, 18], [130, 21, 137, 21, "scene"], [130, 26, 137, 26], [130, 27, 137, 27, "progress"], [130, 35, 137, 35], [130, 36, 137, 36, "next"], [130, 40, 137, 40], [130, 42, 137, 42, "addListener"], [130, 53, 137, 53], [130, 56, 137, 56, "_ref3"], [130, 61, 137, 56], [130, 65, 139, 10], [131, 8, 139, 10], [131, 12, 138, 6, "value"], [131, 17, 138, 11], [131, 20, 138, 11, "_ref3"], [131, 25, 138, 11], [131, 26, 138, 6, "value"], [131, 31, 138, 11], [132, 8, 140, 6, "setPointerEvents"], [132, 24, 140, 22], [132, 25, 140, 23, "value"], [132, 30, 140, 28], [132, 34, 140, 32, "EPSILON"], [132, 41, 140, 39], [132, 44, 140, 42], [132, 54, 140, 52], [132, 57, 140, 55], [132, 63, 140, 61], [132, 64, 140, 62], [133, 6, 141, 4], [133, 7, 141, 5], [133, 8, 141, 6], [134, 6, 142, 4], [134, 13, 142, 11], [134, 19, 142, 17], [135, 8, 143, 6], [135, 12, 143, 10, "listener"], [135, 20, 143, 18], [135, 22, 143, 20], [136, 10, 144, 8, "scene"], [136, 15, 144, 13], [136, 16, 144, 14, "progress"], [136, 24, 144, 22], [136, 25, 144, 23, "next"], [136, 29, 144, 27], [136, 31, 144, 29, "removeListener"], [136, 45, 144, 43], [136, 48, 144, 46, "listener"], [136, 56, 144, 54], [136, 57, 144, 55], [137, 8, 145, 6], [138, 6, 146, 4], [138, 7, 146, 5], [139, 4, 147, 2], [139, 5, 147, 3], [139, 7, 147, 5], [139, 8, 147, 6, "pointerEvents"], [139, 21, 147, 19], [139, 23, 147, 21, "scene"], [139, 28, 147, 26], [139, 29, 147, 27, "progress"], [139, 37, 147, 35], [139, 38, 147, 36, "next"], [139, 42, 147, 40], [139, 43, 147, 41], [139, 44, 147, 42], [140, 4, 148, 2], [140, 8, 148, 2, "_scene$descriptor$opt"], [140, 29, 148, 2], [140, 32, 163, 6, "scene"], [140, 37, 163, 11], [140, 38, 163, 12, "descriptor"], [140, 48, 163, 22], [140, 49, 163, 23, "options"], [140, 56, 163, 30], [141, 6, 149, 4, "presentation"], [141, 18, 149, 16], [141, 21, 149, 16, "_scene$descriptor$opt"], [141, 42, 149, 16], [141, 43, 149, 4, "presentation"], [141, 55, 149, 16], [142, 6, 150, 4, "animation"], [142, 15, 150, 13], [142, 18, 150, 13, "_scene$descriptor$opt"], [142, 39, 150, 13], [142, 40, 150, 4, "animation"], [142, 49, 150, 13], [143, 6, 151, 4, "cardOverlay"], [143, 17, 151, 15], [143, 20, 151, 15, "_scene$descriptor$opt"], [143, 41, 151, 15], [143, 42, 151, 4, "cardOverlay"], [143, 53, 151, 15], [144, 6, 152, 4, "cardOverlayEnabled"], [144, 24, 152, 22], [144, 27, 152, 22, "_scene$descriptor$opt"], [144, 48, 152, 22], [144, 49, 152, 4, "cardOverlayEnabled"], [144, 67, 152, 22], [145, 6, 153, 4, "cardShadowEnabled"], [145, 23, 153, 21], [145, 26, 153, 21, "_scene$descriptor$opt"], [145, 47, 153, 21], [145, 48, 153, 4, "cardShadowEnabled"], [145, 65, 153, 21], [146, 6, 154, 4, "cardStyle"], [146, 15, 154, 13], [146, 18, 154, 13, "_scene$descriptor$opt"], [146, 39, 154, 13], [146, 40, 154, 4, "cardStyle"], [146, 49, 154, 13], [147, 6, 155, 4, "cardStyleInterpolator"], [147, 27, 155, 25], [147, 30, 155, 25, "_scene$descriptor$opt"], [147, 51, 155, 25], [147, 52, 155, 4, "cardStyleInterpolator"], [147, 73, 155, 25], [148, 6, 156, 4, "gestureDirection"], [148, 22, 156, 20], [148, 25, 156, 20, "_scene$descriptor$opt"], [148, 46, 156, 20], [148, 47, 156, 4, "gestureDirection"], [148, 63, 156, 20], [149, 6, 157, 4, "gestureEnabled"], [149, 20, 157, 18], [149, 23, 157, 18, "_scene$descriptor$opt"], [149, 44, 157, 18], [149, 45, 157, 4, "gestureEnabled"], [149, 59, 157, 18], [150, 6, 158, 4, "gestureResponseDistance"], [150, 29, 158, 27], [150, 32, 158, 27, "_scene$descriptor$opt"], [150, 53, 158, 27], [150, 54, 158, 4, "gestureResponseDistance"], [150, 77, 158, 27], [151, 6, 159, 4, "gestureVelocityImpact"], [151, 27, 159, 25], [151, 30, 159, 25, "_scene$descriptor$opt"], [151, 51, 159, 25], [151, 52, 159, 4, "gestureVelocityImpact"], [151, 73, 159, 25], [152, 6, 160, 4, "headerMode"], [152, 16, 160, 14], [152, 19, 160, 14, "_scene$descriptor$opt"], [152, 40, 160, 14], [152, 41, 160, 4, "headerMode"], [152, 51, 160, 14], [153, 6, 161, 4, "headerShown"], [153, 17, 161, 15], [153, 20, 161, 15, "_scene$descriptor$opt"], [153, 41, 161, 15], [153, 42, 161, 4, "headerShown"], [153, 53, 161, 15], [154, 6, 162, 4, "transitionSpec"], [154, 20, 162, 18], [154, 23, 162, 18, "_scene$descriptor$opt"], [154, 44, 162, 18], [154, 45, 162, 4, "transitionSpec"], [154, 59, 162, 18], [155, 4, 164, 2], [155, 8, 164, 2, "_useLinkBuilder"], [155, 23, 164, 2], [155, 26, 166, 6], [155, 30, 166, 6, "useLinkBuilder"], [155, 52, 166, 20], [155, 54, 166, 21], [155, 55, 166, 22], [156, 6, 165, 4, "buildHref"], [156, 15, 165, 13], [156, 18, 165, 13, "_useLinkBuilder"], [156, 33, 165, 13], [156, 34, 165, 4, "buildHref"], [156, 43, 165, 13], [157, 4, 167, 2], [157, 8, 167, 8, "previousScene"], [157, 21, 167, 21], [157, 24, 167, 24, "getPreviousScene"], [157, 40, 167, 40], [157, 41, 167, 41], [158, 6, 168, 4, "route"], [158, 11, 168, 9], [158, 13, 168, 11, "scene"], [158, 18, 168, 16], [158, 19, 168, 17, "descriptor"], [158, 29, 168, 27], [158, 30, 168, 28, "route"], [159, 4, 169, 2], [159, 5, 169, 3], [159, 6, 169, 4], [160, 4, 170, 2], [160, 8, 170, 6, "backTitle"], [160, 17, 170, 15], [161, 4, 171, 2], [161, 8, 171, 6, "href"], [161, 12, 171, 10], [162, 4, 172, 2], [162, 8, 172, 6, "previousScene"], [162, 21, 172, 19], [162, 23, 172, 21], [163, 6, 173, 4], [163, 10, 173, 4, "_previousScene$descri"], [163, 31, 173, 4], [163, 34, 176, 8, "previousScene"], [163, 47, 176, 21], [163, 48, 176, 22, "descriptor"], [163, 58, 176, 32], [164, 8, 174, 6, "options"], [164, 15, 174, 13], [164, 18, 174, 13, "_previousScene$descri"], [164, 39, 174, 13], [164, 40, 174, 6, "options"], [164, 47, 174, 13], [165, 8, 175, 6, "route"], [165, 13, 175, 11], [165, 16, 175, 11, "_previousScene$descri"], [165, 37, 175, 11], [165, 38, 175, 6, "route"], [165, 43, 175, 11], [166, 6, 177, 4, "backTitle"], [166, 15, 177, 13], [166, 18, 177, 16], [166, 22, 177, 16, "getHeaderTitle"], [166, 46, 177, 30], [166, 48, 177, 31, "options"], [166, 55, 177, 38], [166, 57, 177, 40, "route"], [166, 62, 177, 45], [166, 63, 177, 46, "name"], [166, 67, 177, 50], [166, 68, 177, 51], [167, 6, 178, 4, "href"], [167, 10, 178, 8], [167, 13, 178, 11, "buildHref"], [167, 22, 178, 20], [167, 23, 178, 21, "route"], [167, 28, 178, 26], [167, 29, 178, 27, "name"], [167, 33, 178, 31], [167, 35, 178, 33, "route"], [167, 40, 178, 38], [167, 41, 178, 39, "params"], [167, 47, 178, 45], [167, 48, 178, 46], [168, 4, 179, 2], [169, 4, 180, 2], [169, 8, 180, 8, "canGoBack"], [169, 17, 180, 17], [169, 20, 180, 20, "previousScene"], [169, 33, 180, 33], [169, 37, 180, 37], [169, 41, 180, 41], [170, 4, 181, 2], [170, 8, 181, 8, "headerBack"], [170, 18, 181, 18], [170, 21, 181, 21, "React"], [170, 26, 181, 26], [170, 27, 181, 27, "useMemo"], [170, 34, 181, 34], [170, 35, 181, 35], [170, 41, 181, 41], [171, 6, 182, 4], [171, 10, 182, 8, "canGoBack"], [171, 19, 182, 17], [171, 21, 182, 19], [172, 8, 183, 6], [172, 15, 183, 13], [173, 10, 184, 8, "href"], [173, 14, 184, 12], [174, 10, 185, 8, "title"], [174, 15, 185, 13], [174, 17, 185, 15, "backTitle"], [175, 8, 186, 6], [175, 9, 186, 7], [176, 6, 187, 4], [177, 6, 188, 4], [177, 13, 188, 11, "undefined"], [177, 22, 188, 20], [178, 4, 189, 2], [178, 5, 189, 3], [178, 7, 189, 5], [178, 8, 189, 6, "canGoBack"], [178, 17, 189, 15], [178, 19, 189, 17, "backTitle"], [178, 28, 189, 26], [178, 30, 189, 28, "href"], [178, 34, 189, 32], [178, 35, 189, 33], [178, 36, 189, 34], [179, 4, 190, 2], [179, 11, 190, 9], [179, 24, 190, 22], [179, 28, 190, 22, "_jsx"], [179, 43, 190, 26], [179, 45, 190, 27, "Card"], [179, 55, 190, 31], [179, 57, 190, 33], [180, 6, 191, 4, "interpolationIndex"], [180, 24, 191, 22], [180, 26, 191, 24, "interpolationIndex"], [180, 44, 191, 42], [181, 6, 192, 4, "gestureDirection"], [181, 22, 192, 20], [181, 24, 192, 22, "gestureDirection"], [181, 40, 192, 38], [182, 6, 193, 4, "layout"], [182, 12, 193, 10], [182, 14, 193, 12, "layout"], [182, 20, 193, 18], [183, 6, 194, 4, "insets"], [183, 12, 194, 10], [183, 14, 194, 12, "insets"], [183, 20, 194, 18], [184, 6, 195, 4, "direction"], [184, 15, 195, 13], [184, 17, 195, 15, "direction"], [184, 26, 195, 24], [185, 6, 196, 4, "gesture"], [185, 13, 196, 11], [185, 15, 196, 13, "gesture"], [185, 22, 196, 20], [186, 6, 197, 4, "current"], [186, 13, 197, 11], [186, 15, 197, 13, "scene"], [186, 20, 197, 18], [186, 21, 197, 19, "progress"], [186, 29, 197, 27], [186, 30, 197, 28, "current"], [186, 37, 197, 35], [187, 6, 198, 4, "next"], [187, 10, 198, 8], [187, 12, 198, 10, "scene"], [187, 17, 198, 15], [187, 18, 198, 16, "progress"], [187, 26, 198, 24], [187, 27, 198, 25, "next"], [187, 31, 198, 29], [188, 6, 199, 4, "opening"], [188, 13, 199, 11], [188, 15, 199, 13, "opening"], [188, 22, 199, 20], [189, 6, 200, 4, "closing"], [189, 13, 200, 11], [189, 15, 200, 13, "closing"], [189, 22, 200, 20], [190, 6, 201, 4, "onOpen"], [190, 12, 201, 10], [190, 14, 201, 12, "handleOpen"], [190, 24, 201, 22], [191, 6, 202, 4, "onClose"], [191, 13, 202, 11], [191, 15, 202, 13, "handleClose"], [191, 26, 202, 24], [192, 6, 203, 4, "overlay"], [192, 13, 203, 11], [192, 15, 203, 13, "cardOverlay"], [192, 26, 203, 24], [193, 6, 204, 4, "overlayEnabled"], [193, 20, 204, 18], [193, 22, 204, 20, "cardOverlayEnabled"], [193, 40, 204, 38], [194, 6, 205, 4, "shadowEnabled"], [194, 19, 205, 17], [194, 21, 205, 19, "cardShadowEnabled"], [194, 38, 205, 36], [195, 6, 206, 4, "onTransition"], [195, 18, 206, 16], [195, 20, 206, 18, "handleTransition"], [195, 36, 206, 34], [196, 6, 207, 4, "onGestureBegin"], [196, 20, 207, 18], [196, 22, 207, 20, "handleGestureBegin"], [196, 40, 207, 38], [197, 6, 208, 4, "onGestureCanceled"], [197, 23, 208, 21], [197, 25, 208, 23, "handleGestureCanceled"], [197, 46, 208, 44], [198, 6, 209, 4, "onGestureEnd"], [198, 18, 209, 16], [198, 20, 209, 18, "handleGestureEnd"], [198, 36, 209, 34], [199, 6, 210, 4, "gestureEnabled"], [199, 20, 210, 18], [199, 22, 210, 20, "index"], [199, 27, 210, 25], [199, 32, 210, 30], [199, 33, 210, 31], [199, 36, 210, 34], [199, 41, 210, 39], [199, 44, 210, 42, "gestureEnabled"], [199, 58, 210, 56], [200, 6, 211, 4, "gestureResponseDistance"], [200, 29, 211, 27], [200, 31, 211, 29, "gestureResponseDistance"], [200, 54, 211, 52], [201, 6, 212, 4, "gestureVelocityImpact"], [201, 27, 212, 25], [201, 29, 212, 27, "gestureVelocityImpact"], [201, 50, 212, 48], [202, 6, 213, 4, "transitionSpec"], [202, 20, 213, 18], [202, 22, 213, 20, "transitionSpec"], [202, 36, 213, 34], [203, 6, 214, 4, "styleInterpolator"], [203, 23, 214, 21], [203, 25, 214, 23, "cardStyleInterpolator"], [203, 46, 214, 44], [204, 6, 215, 4], [204, 19, 215, 17], [204, 21, 215, 19], [204, 22, 215, 20, "focused"], [204, 29, 215, 27], [205, 6, 216, 4, "pointerEvents"], [205, 19, 216, 17], [205, 21, 216, 19, "active"], [205, 27, 216, 25], [205, 30, 216, 28], [205, 40, 216, 38], [205, 43, 216, 41, "pointerEvents"], [205, 56, 216, 54], [206, 6, 217, 4, "pageOverflowEnabled"], [206, 25, 217, 23], [206, 27, 217, 25, "headerMode"], [206, 37, 217, 35], [206, 42, 217, 40], [206, 49, 217, 47], [206, 53, 217, 51, "presentation"], [206, 65, 217, 63], [206, 70, 217, 68], [206, 77, 217, 75], [207, 6, 218, 4, "preloaded"], [207, 15, 218, 13], [207, 17, 218, 15, "preloaded"], [207, 26, 218, 24], [208, 6, 219, 4, "containerStyle"], [208, 20, 219, 18], [208, 22, 219, 20, "hasAbsoluteFloatHeader"], [208, 44, 219, 42], [208, 48, 219, 46, "headerMode"], [208, 58, 219, 56], [208, 63, 219, 61], [208, 71, 219, 69], [208, 74, 219, 72], [209, 8, 220, 6, "marginTop"], [209, 17, 220, 15], [209, 19, 220, 17, "headerHeight"], [210, 6, 221, 4], [210, 7, 221, 5], [210, 10, 221, 8], [210, 14, 221, 12], [211, 6, 222, 4, "contentStyle"], [211, 18, 222, 16], [211, 20, 222, 18], [211, 21, 222, 19], [212, 8, 223, 6, "backgroundColor"], [212, 23, 223, 21], [212, 25, 223, 23, "presentation"], [212, 37, 223, 35], [212, 42, 223, 40], [212, 60, 223, 58], [212, 63, 223, 61], [212, 76, 223, 74], [212, 79, 223, 77, "colors"], [212, 85, 223, 83], [212, 86, 223, 84, "background"], [213, 6, 224, 4], [213, 7, 224, 5], [213, 9, 224, 7, "cardStyle"], [213, 18, 224, 16], [213, 19, 224, 17], [214, 6, 225, 4, "style"], [214, 11, 225, 9], [214, 13, 225, 11], [214, 14, 225, 12], [215, 8, 226, 6], [216, 8, 227, 6], [217, 8, 228, 6, "overflow"], [217, 16, 228, 14], [217, 18, 228, 16, "active"], [217, 24, 228, 22], [217, 27, 228, 25, "undefined"], [217, 36, 228, 34], [217, 39, 228, 37], [217, 47, 228, 45], [218, 8, 229, 6, "display"], [218, 15, 229, 13], [219, 8, 230, 6], [220, 8, 231, 6], [221, 8, 232, 6, "animation"], [221, 17, 232, 15], [221, 22, 232, 20], [221, 28, 232, 26], [221, 32, 232, 30, "isNextScreenTransparent"], [221, 55, 232, 53], [221, 60, 232, 58], [221, 65, 232, 63], [221, 69, 232, 67, "detachCurrentScreen"], [221, 88, 232, 86], [221, 93, 232, 91], [221, 98, 232, 96], [221, 102, 232, 100], [221, 103, 232, 101, "focused"], [221, 110, 232, 108], [221, 113, 232, 111], [221, 119, 232, 117], [221, 122, 232, 120], [222, 6, 233, 4], [222, 7, 233, 5], [222, 9, 233, 7, "StyleSheet"], [222, 32, 233, 17], [222, 33, 233, 18, "absoluteFill"], [222, 45, 233, 30], [222, 46, 233, 31], [223, 6, 234, 4, "children"], [223, 14, 234, 12], [223, 16, 234, 14], [223, 29, 234, 27], [223, 33, 234, 27, "_jsx"], [223, 48, 234, 31], [223, 50, 234, 32, "View"], [223, 67, 234, 36], [223, 69, 234, 38], [224, 8, 235, 6, "style"], [224, 13, 235, 11], [224, 15, 235, 13, "styles"], [224, 21, 235, 19], [224, 22, 235, 20, "container"], [224, 31, 235, 29], [225, 8, 236, 6, "children"], [225, 16, 236, 14], [225, 18, 236, 16], [225, 31, 236, 29], [225, 35, 236, 29, "_jsxs"], [225, 51, 236, 34], [225, 53, 236, 35, "ModalPresentationContext"], [225, 103, 236, 59], [225, 104, 236, 60, "Provider"], [225, 112, 236, 68], [225, 114, 236, 70], [226, 10, 237, 8, "value"], [226, 15, 237, 13], [226, 17, 237, 15, "modal"], [226, 22, 237, 20], [227, 10, 238, 8, "children"], [227, 18, 238, 16], [227, 20, 238, 18], [227, 21, 238, 19, "headerMode"], [227, 31, 238, 29], [227, 36, 238, 34], [227, 43, 238, 41], [227, 46, 238, 44, "renderHeader"], [227, 58, 238, 56], [227, 59, 238, 57], [228, 12, 239, 10, "mode"], [228, 16, 239, 14], [228, 18, 239, 16], [228, 26, 239, 24], [229, 12, 240, 10, "layout"], [229, 18, 240, 16], [230, 12, 241, 10, "scenes"], [230, 18, 241, 16], [230, 20, 241, 18], [230, 21, 241, 19, "previousScene"], [230, 34, 241, 32], [230, 36, 241, 34, "scene"], [230, 41, 241, 39], [230, 42, 241, 40], [231, 12, 242, 10, "getPreviousScene"], [231, 28, 242, 26], [232, 12, 243, 10, "getFocusedRoute"], [232, 27, 243, 25], [233, 12, 244, 10, "onContentHeightChange"], [233, 33, 244, 31], [233, 35, 244, 33, "onHeaderHeightChange"], [233, 55, 244, 53], [234, 12, 245, 10, "style"], [234, 17, 245, 15], [234, 19, 245, 17, "styles"], [234, 25, 245, 23], [234, 26, 245, 24, "header"], [235, 10, 246, 8], [235, 11, 246, 9], [235, 12, 246, 10], [235, 15, 246, 13], [235, 19, 246, 17], [235, 21, 246, 19], [235, 34, 246, 32], [235, 38, 246, 32, "_jsx"], [235, 53, 246, 36], [235, 55, 246, 37, "View"], [235, 72, 246, 41], [235, 74, 246, 43], [236, 12, 247, 10, "style"], [236, 17, 247, 15], [236, 19, 247, 17, "styles"], [236, 25, 247, 23], [236, 26, 247, 24, "scene"], [236, 31, 247, 29], [237, 12, 248, 10, "children"], [237, 20, 248, 18], [237, 22, 248, 20], [237, 35, 248, 33], [237, 39, 248, 33, "_jsx"], [237, 54, 248, 37], [237, 56, 248, 38, "HeaderBackContext"], [237, 83, 248, 55], [237, 84, 248, 56, "Provider"], [237, 92, 248, 64], [237, 94, 248, 66], [238, 14, 249, 12, "value"], [238, 19, 249, 17], [238, 21, 249, 19, "headerBack"], [238, 31, 249, 29], [239, 14, 250, 12, "children"], [239, 22, 250, 20], [239, 24, 250, 22], [239, 37, 250, 35], [239, 41, 250, 35, "_jsx"], [239, 56, 250, 39], [239, 58, 250, 40, "HeaderShownContext"], [239, 86, 250, 58], [239, 87, 250, 59, "Provider"], [239, 95, 250, 67], [239, 97, 250, 69], [240, 16, 251, 14, "value"], [240, 21, 251, 19], [240, 23, 251, 21, "isParentHeaderShown"], [240, 42, 251, 40], [240, 46, 251, 44, "headerShown"], [240, 57, 251, 55], [240, 62, 251, 60], [240, 67, 251, 65], [241, 16, 252, 14, "children"], [241, 24, 252, 22], [241, 26, 252, 24], [241, 39, 252, 37], [241, 43, 252, 37, "_jsx"], [241, 58, 252, 41], [241, 60, 252, 42, "HeaderHeightContext"], [241, 89, 252, 61], [241, 90, 252, 62, "Provider"], [241, 98, 252, 70], [241, 100, 252, 72], [242, 18, 253, 16, "value"], [242, 23, 253, 21], [242, 25, 253, 23, "headerShown"], [242, 36, 253, 34], [242, 41, 253, 39], [242, 46, 253, 44], [242, 49, 253, 47, "headerHeight"], [242, 61, 253, 59], [242, 64, 253, 62, "parentHeaderHeight"], [242, 82, 253, 80], [242, 86, 253, 84], [242, 87, 253, 85], [243, 18, 254, 16, "children"], [243, 26, 254, 24], [243, 28, 254, 26, "scene"], [243, 33, 254, 31], [243, 34, 254, 32, "descriptor"], [243, 44, 254, 42], [243, 45, 254, 43, "render"], [243, 51, 254, 49], [243, 52, 254, 50], [244, 16, 255, 14], [244, 17, 255, 15], [245, 14, 256, 12], [245, 15, 256, 13], [246, 12, 257, 10], [246, 13, 257, 11], [247, 10, 258, 8], [247, 11, 258, 9], [247, 12, 258, 10], [248, 8, 259, 6], [248, 9, 259, 7], [249, 6, 260, 4], [249, 7, 260, 5], [250, 4, 261, 2], [250, 5, 261, 3], [250, 6, 261, 4], [251, 2, 262, 0], [252, 2, 263, 7], [252, 6, 263, 13, "CardContainer"], [252, 19, 263, 26], [252, 22, 263, 26, "exports"], [252, 29, 263, 26], [252, 30, 263, 26, "CardContainer"], [252, 43, 263, 26], [252, 46, 263, 29], [252, 59, 263, 42, "React"], [252, 64, 263, 47], [252, 65, 263, 48, "memo"], [252, 69, 263, 52], [252, 70, 263, 53, "CardContainerInner"], [252, 88, 263, 71], [252, 89, 263, 72], [253, 2, 264, 0], [253, 6, 264, 6, "styles"], [253, 12, 264, 12], [253, 15, 264, 15, "StyleSheet"], [253, 38, 264, 25], [253, 39, 264, 26, "create"], [253, 45, 264, 32], [253, 46, 264, 33], [254, 4, 265, 2, "container"], [254, 13, 265, 11], [254, 15, 265, 13], [255, 6, 266, 4, "flex"], [255, 10, 266, 8], [255, 12, 266, 10], [256, 4, 267, 2], [256, 5, 267, 3], [257, 4, 268, 2, "header"], [257, 10, 268, 8], [257, 12, 268, 10], [258, 6, 269, 4, "zIndex"], [258, 12, 269, 10], [258, 14, 269, 12], [259, 4, 270, 2], [259, 5, 270, 3], [260, 4, 271, 2, "scene"], [260, 9, 271, 7], [260, 11, 271, 9], [261, 6, 272, 4, "flex"], [261, 10, 272, 8], [261, 12, 272, 10], [262, 4, 273, 2], [263, 2, 274, 0], [263, 3, 274, 1], [263, 4, 274, 2], [264, 0, 274, 3], [264, 3]], "functionMap": {"names": ["<global>", "CardContainerInner", "useKeyboardManager$argument_0", "handleOpen", "handleClose", "handleGestureBegin", "handleGestureCanceled", "handleGestureEnd", "handleTransition", "React.useEffect$argument_0", "scene.progress.next.addListener$argument_0", "<anonymous>", "React.useMemo$argument_0"], "mappings": "AAA;ACW;2CCyC;GDM;qBEC;GFU;sBGC;GHU;6BIC;GJQ;gCKC;GLQ;2BMC;GNO;2BOC;GPiB;kBQW;wDCC;KDI;WEC;KFI;GRC;mCWkC;GXQ;CDyE"}}, "type": "js/module"}]}