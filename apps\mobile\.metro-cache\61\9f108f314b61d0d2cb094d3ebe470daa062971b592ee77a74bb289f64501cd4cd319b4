{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  /**\n   * Convert array of 16 byte values to UUID string format of the form:\n   * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n   */\n  var byteToHex = [];\n  for (var i = 0; i < 256; ++i) {\n    byteToHex[i] = (i + 0x100).toString(16).substr(1);\n  }\n  function bytesToUuid(buf, offset) {\n    var i = offset || 0;\n    var bth = byteToHex;\n    // join used to fix memory issue caused by concatenation: https://bugs.chromium.org/p/v8/issues/detail?id=3175#c4\n    return [bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]]].join('');\n  }\n  var _default = exports.default = bytesToUuid;\n});", "lineCount": 21, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 2, 5, 0], [10, 6, 5, 6, "byteToHex"], [10, 15, 5, 25], [10, 18, 5, 28], [10, 20, 5, 30], [11, 2, 6, 0], [11, 7, 6, 5], [11, 11, 6, 9, "i"], [11, 12, 6, 10], [11, 15, 6, 13], [11, 16, 6, 14], [11, 18, 6, 16, "i"], [11, 19, 6, 17], [11, 22, 6, 20], [11, 25, 6, 23], [11, 27, 6, 25], [11, 29, 6, 27, "i"], [11, 30, 6, 28], [11, 32, 6, 30], [12, 4, 7, 2, "byteToHex"], [12, 13, 7, 11], [12, 14, 7, 12, "i"], [12, 15, 7, 13], [12, 16, 7, 14], [12, 19, 7, 17], [12, 20, 7, 18, "i"], [12, 21, 7, 19], [12, 24, 7, 22], [12, 29, 7, 27], [12, 31, 7, 29, "toString"], [12, 39, 7, 37], [12, 40, 7, 38], [12, 42, 7, 40], [12, 43, 7, 41], [12, 44, 7, 42, "substr"], [12, 50, 7, 48], [12, 51, 7, 49], [12, 52, 7, 50], [12, 53, 7, 51], [13, 2, 8, 0], [14, 2, 10, 0], [14, 11, 10, 9, "bytesToUuid"], [14, 22, 10, 20, "bytesToUuid"], [14, 23, 10, 21, "buf"], [14, 26, 10, 34], [14, 28, 10, 36, "offset"], [14, 34, 10, 51], [14, 36, 10, 53], [15, 4, 11, 2], [15, 8, 11, 6, "i"], [15, 9, 11, 7], [15, 12, 11, 10, "offset"], [15, 18, 11, 16], [15, 22, 11, 20], [15, 23, 11, 21], [16, 4, 12, 2], [16, 8, 12, 8, "bth"], [16, 11, 12, 11], [16, 14, 12, 14, "byteToHex"], [16, 23, 12, 23], [17, 4, 13, 2], [18, 4, 14, 2], [18, 11, 14, 9], [18, 12, 15, 4, "bth"], [18, 15, 15, 7], [18, 16, 15, 8, "buf"], [18, 19, 15, 11], [18, 20, 15, 12, "i"], [18, 21, 15, 13], [18, 23, 15, 15], [18, 24, 15, 16], [18, 25, 15, 17], [18, 27, 16, 4, "bth"], [18, 30, 16, 7], [18, 31, 16, 8, "buf"], [18, 34, 16, 11], [18, 35, 16, 12, "i"], [18, 36, 16, 13], [18, 38, 16, 15], [18, 39, 16, 16], [18, 40, 16, 17], [18, 42, 17, 4, "bth"], [18, 45, 17, 7], [18, 46, 17, 8, "buf"], [18, 49, 17, 11], [18, 50, 17, 12, "i"], [18, 51, 17, 13], [18, 53, 17, 15], [18, 54, 17, 16], [18, 55, 17, 17], [18, 57, 18, 4, "bth"], [18, 60, 18, 7], [18, 61, 18, 8, "buf"], [18, 64, 18, 11], [18, 65, 18, 12, "i"], [18, 66, 18, 13], [18, 68, 18, 15], [18, 69, 18, 16], [18, 70, 18, 17], [18, 72, 19, 4], [18, 75, 19, 7], [18, 77, 20, 4, "bth"], [18, 80, 20, 7], [18, 81, 20, 8, "buf"], [18, 84, 20, 11], [18, 85, 20, 12, "i"], [18, 86, 20, 13], [18, 88, 20, 15], [18, 89, 20, 16], [18, 90, 20, 17], [18, 92, 21, 4, "bth"], [18, 95, 21, 7], [18, 96, 21, 8, "buf"], [18, 99, 21, 11], [18, 100, 21, 12, "i"], [18, 101, 21, 13], [18, 103, 21, 15], [18, 104, 21, 16], [18, 105, 21, 17], [18, 107, 22, 4], [18, 110, 22, 7], [18, 112, 23, 4, "bth"], [18, 115, 23, 7], [18, 116, 23, 8, "buf"], [18, 119, 23, 11], [18, 120, 23, 12, "i"], [18, 121, 23, 13], [18, 123, 23, 15], [18, 124, 23, 16], [18, 125, 23, 17], [18, 127, 24, 4, "bth"], [18, 130, 24, 7], [18, 131, 24, 8, "buf"], [18, 134, 24, 11], [18, 135, 24, 12, "i"], [18, 136, 24, 13], [18, 138, 24, 15], [18, 139, 24, 16], [18, 140, 24, 17], [18, 142, 25, 4], [18, 145, 25, 7], [18, 147, 26, 4, "bth"], [18, 150, 26, 7], [18, 151, 26, 8, "buf"], [18, 154, 26, 11], [18, 155, 26, 12, "i"], [18, 156, 26, 13], [18, 158, 26, 15], [18, 159, 26, 16], [18, 160, 26, 17], [18, 162, 27, 4, "bth"], [18, 165, 27, 7], [18, 166, 27, 8, "buf"], [18, 169, 27, 11], [18, 170, 27, 12, "i"], [18, 171, 27, 13], [18, 173, 27, 15], [18, 174, 27, 16], [18, 175, 27, 17], [18, 177, 28, 4], [18, 180, 28, 7], [18, 182, 29, 4, "bth"], [18, 185, 29, 7], [18, 186, 29, 8, "buf"], [18, 189, 29, 11], [18, 190, 29, 12, "i"], [18, 191, 29, 13], [18, 193, 29, 15], [18, 194, 29, 16], [18, 195, 29, 17], [18, 197, 30, 4, "bth"], [18, 200, 30, 7], [18, 201, 30, 8, "buf"], [18, 204, 30, 11], [18, 205, 30, 12, "i"], [18, 206, 30, 13], [18, 208, 30, 15], [18, 209, 30, 16], [18, 210, 30, 17], [18, 212, 31, 4, "bth"], [18, 215, 31, 7], [18, 216, 31, 8, "buf"], [18, 219, 31, 11], [18, 220, 31, 12, "i"], [18, 221, 31, 13], [18, 223, 31, 15], [18, 224, 31, 16], [18, 225, 31, 17], [18, 227, 32, 4, "bth"], [18, 230, 32, 7], [18, 231, 32, 8, "buf"], [18, 234, 32, 11], [18, 235, 32, 12, "i"], [18, 236, 32, 13], [18, 238, 32, 15], [18, 239, 32, 16], [18, 240, 32, 17], [18, 242, 33, 4, "bth"], [18, 245, 33, 7], [18, 246, 33, 8, "buf"], [18, 249, 33, 11], [18, 250, 33, 12, "i"], [18, 251, 33, 13], [18, 253, 33, 15], [18, 254, 33, 16], [18, 255, 33, 17], [18, 257, 34, 4, "bth"], [18, 260, 34, 7], [18, 261, 34, 8, "buf"], [18, 264, 34, 11], [18, 265, 34, 12, "i"], [18, 266, 34, 13], [18, 268, 34, 15], [18, 269, 34, 16], [18, 270, 34, 17], [18, 271, 35, 3], [18, 272, 35, 4, "join"], [18, 276, 35, 8], [18, 277, 35, 9], [18, 279, 35, 11], [18, 280, 35, 12], [19, 2, 36, 0], [20, 2, 36, 1], [20, 6, 36, 1, "_default"], [20, 14, 36, 1], [20, 17, 36, 1, "exports"], [20, 24, 36, 1], [20, 25, 36, 1, "default"], [20, 32, 36, 1], [20, 35, 38, 15, "bytesToUuid"], [20, 46, 38, 26], [21, 0, 38, 26], [21, 3]], "functionMap": {"names": ["<global>", "bytesToUuid"], "mappings": "AAA;ACS;CD0B"}}, "type": "js/module"}]}