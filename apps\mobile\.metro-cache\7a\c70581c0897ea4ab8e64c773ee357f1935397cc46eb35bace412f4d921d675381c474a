{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "nanoid/non-secure", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 43, "index": 58}}], "key": "SN8WVal79eAEDQEpzmVqVAy5JJs=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 59}, "end": {"line": 4, "column": 31, "index": 90}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./EnsureSingleNavigator.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 91}, "end": {"line": 5, "column": 68, "index": 159}}], "key": "Eeoj43oWyPbMgkhKsD7HCEmXypI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useRegisterNavigator = useRegisterNavigator;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _nonSecure = require(_dependencyMap[2], \"nanoid/non-secure\");\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _EnsureSingleNavigator = require(_dependencyMap[4], \"./EnsureSingleNavigator.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Register a navigator in the parent context (either a navigation container or a screen).\n   * This is used to prevent multiple navigators under a single container or screen.\n   */\n  function useRegisterNavigator() {\n    var _React$useState = React.useState(() => (0, _nonSecure.nanoid)()),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 1),\n      key = _React$useState2[0];\n    var container = React.useContext(_EnsureSingleNavigator.SingleNavigatorContext);\n    if (container === undefined) {\n      throw new Error(\"Couldn't register the navigator. Have you wrapped your app with 'NavigationContainer'?\\n\\nThis can also happen if there are multiple copies of '@react-navigation' packages installed.\");\n    }\n    React.useEffect(() => {\n      var register = container.register,\n        unregister = container.unregister;\n      register(key);\n      return () => unregister(key);\n    }, [container, key]);\n    return key;\n  }\n});", "lineCount": 34, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "useRegisterNavigator"], [8, 30, 1, 13], [8, 33, 1, 13, "useRegisterNavigator"], [8, 53, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_nonSecure"], [10, 16, 3, 0], [10, 19, 3, 0, "require"], [10, 26, 3, 0], [10, 27, 3, 0, "_dependencyMap"], [10, 41, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "React"], [11, 11, 4, 0], [11, 14, 4, 0, "_interopRequireWildcard"], [11, 37, 4, 0], [11, 38, 4, 0, "require"], [11, 45, 4, 0], [11, 46, 4, 0, "_dependencyMap"], [11, 60, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_EnsureSingleNavigator"], [12, 28, 5, 0], [12, 31, 5, 0, "require"], [12, 38, 5, 0], [12, 39, 5, 0, "_dependencyMap"], [12, 53, 5, 0], [13, 2, 5, 68], [13, 11, 5, 68, "_interopRequireWildcard"], [13, 35, 5, 68, "e"], [13, 36, 5, 68], [13, 38, 5, 68, "t"], [13, 39, 5, 68], [13, 68, 5, 68, "WeakMap"], [13, 75, 5, 68], [13, 81, 5, 68, "r"], [13, 82, 5, 68], [13, 89, 5, 68, "WeakMap"], [13, 96, 5, 68], [13, 100, 5, 68, "n"], [13, 101, 5, 68], [13, 108, 5, 68, "WeakMap"], [13, 115, 5, 68], [13, 127, 5, 68, "_interopRequireWildcard"], [13, 150, 5, 68], [13, 162, 5, 68, "_interopRequireWildcard"], [13, 163, 5, 68, "e"], [13, 164, 5, 68], [13, 166, 5, 68, "t"], [13, 167, 5, 68], [13, 176, 5, 68, "t"], [13, 177, 5, 68], [13, 181, 5, 68, "e"], [13, 182, 5, 68], [13, 186, 5, 68, "e"], [13, 187, 5, 68], [13, 188, 5, 68, "__esModule"], [13, 198, 5, 68], [13, 207, 5, 68, "e"], [13, 208, 5, 68], [13, 214, 5, 68, "o"], [13, 215, 5, 68], [13, 217, 5, 68, "i"], [13, 218, 5, 68], [13, 220, 5, 68, "f"], [13, 221, 5, 68], [13, 226, 5, 68, "__proto__"], [13, 235, 5, 68], [13, 243, 5, 68, "default"], [13, 250, 5, 68], [13, 252, 5, 68, "e"], [13, 253, 5, 68], [13, 270, 5, 68, "e"], [13, 271, 5, 68], [13, 294, 5, 68, "e"], [13, 295, 5, 68], [13, 320, 5, 68, "e"], [13, 321, 5, 68], [13, 330, 5, 68, "f"], [13, 331, 5, 68], [13, 337, 5, 68, "o"], [13, 338, 5, 68], [13, 341, 5, 68, "t"], [13, 342, 5, 68], [13, 345, 5, 68, "n"], [13, 346, 5, 68], [13, 349, 5, 68, "r"], [13, 350, 5, 68], [13, 358, 5, 68, "o"], [13, 359, 5, 68], [13, 360, 5, 68, "has"], [13, 363, 5, 68], [13, 364, 5, 68, "e"], [13, 365, 5, 68], [13, 375, 5, 68, "o"], [13, 376, 5, 68], [13, 377, 5, 68, "get"], [13, 380, 5, 68], [13, 381, 5, 68, "e"], [13, 382, 5, 68], [13, 385, 5, 68, "o"], [13, 386, 5, 68], [13, 387, 5, 68, "set"], [13, 390, 5, 68], [13, 391, 5, 68, "e"], [13, 392, 5, 68], [13, 394, 5, 68, "f"], [13, 395, 5, 68], [13, 409, 5, 68, "_t"], [13, 411, 5, 68], [13, 415, 5, 68, "e"], [13, 416, 5, 68], [13, 432, 5, 68, "_t"], [13, 434, 5, 68], [13, 441, 5, 68, "hasOwnProperty"], [13, 455, 5, 68], [13, 456, 5, 68, "call"], [13, 460, 5, 68], [13, 461, 5, 68, "e"], [13, 462, 5, 68], [13, 464, 5, 68, "_t"], [13, 466, 5, 68], [13, 473, 5, 68, "i"], [13, 474, 5, 68], [13, 478, 5, 68, "o"], [13, 479, 5, 68], [13, 482, 5, 68, "Object"], [13, 488, 5, 68], [13, 489, 5, 68, "defineProperty"], [13, 503, 5, 68], [13, 508, 5, 68, "Object"], [13, 514, 5, 68], [13, 515, 5, 68, "getOwnPropertyDescriptor"], [13, 539, 5, 68], [13, 540, 5, 68, "e"], [13, 541, 5, 68], [13, 543, 5, 68, "_t"], [13, 545, 5, 68], [13, 552, 5, 68, "i"], [13, 553, 5, 68], [13, 554, 5, 68, "get"], [13, 557, 5, 68], [13, 561, 5, 68, "i"], [13, 562, 5, 68], [13, 563, 5, 68, "set"], [13, 566, 5, 68], [13, 570, 5, 68, "o"], [13, 571, 5, 68], [13, 572, 5, 68, "f"], [13, 573, 5, 68], [13, 575, 5, 68, "_t"], [13, 577, 5, 68], [13, 579, 5, 68, "i"], [13, 580, 5, 68], [13, 584, 5, 68, "f"], [13, 585, 5, 68], [13, 586, 5, 68, "_t"], [13, 588, 5, 68], [13, 592, 5, 68, "e"], [13, 593, 5, 68], [13, 594, 5, 68, "_t"], [13, 596, 5, 68], [13, 607, 5, 68, "f"], [13, 608, 5, 68], [13, 613, 5, 68, "e"], [13, 614, 5, 68], [13, 616, 5, 68, "t"], [13, 617, 5, 68], [14, 2, 7, 0], [15, 0, 8, 0], [16, 0, 9, 0], [17, 0, 10, 0], [18, 2, 11, 7], [18, 11, 11, 16, "useRegisterNavigator"], [18, 31, 11, 36, "useRegisterNavigator"], [18, 32, 11, 36], [18, 34, 11, 39], [19, 4, 12, 2], [19, 8, 12, 2, "_React$useState"], [19, 23, 12, 2], [19, 26, 12, 16, "React"], [19, 31, 12, 21], [19, 32, 12, 22, "useState"], [19, 40, 12, 30], [19, 41, 12, 31], [19, 47, 12, 37], [19, 51, 12, 37, "nanoid"], [19, 68, 12, 43], [19, 70, 12, 44], [19, 71, 12, 45], [19, 72, 12, 46], [20, 6, 12, 46, "_React$useState2"], [20, 22, 12, 46], [20, 29, 12, 46, "_slicedToArray2"], [20, 44, 12, 46], [20, 45, 12, 46, "default"], [20, 52, 12, 46], [20, 54, 12, 46, "_React$useState"], [20, 69, 12, 46], [21, 6, 12, 9, "key"], [21, 9, 12, 12], [21, 12, 12, 12, "_React$useState2"], [21, 28, 12, 12], [22, 4, 13, 2], [22, 8, 13, 8, "container"], [22, 17, 13, 17], [22, 20, 13, 20, "React"], [22, 25, 13, 25], [22, 26, 13, 26, "useContext"], [22, 36, 13, 36], [22, 37, 13, 37, "SingleNavigatorContext"], [22, 82, 13, 59], [22, 83, 13, 60], [23, 4, 14, 2], [23, 8, 14, 6, "container"], [23, 17, 14, 15], [23, 22, 14, 20, "undefined"], [23, 31, 14, 29], [23, 33, 14, 31], [24, 6, 15, 4], [24, 12, 15, 10], [24, 16, 15, 14, "Error"], [24, 21, 15, 19], [24, 22, 15, 20], [24, 206, 15, 204], [24, 207, 15, 205], [25, 4, 16, 2], [26, 4, 17, 2, "React"], [26, 9, 17, 7], [26, 10, 17, 8, "useEffect"], [26, 19, 17, 17], [26, 20, 17, 18], [26, 26, 17, 24], [27, 6, 18, 4], [27, 10, 19, 6, "register"], [27, 18, 19, 14], [27, 21, 21, 8, "container"], [27, 30, 21, 17], [27, 31, 19, 6, "register"], [27, 39, 19, 14], [28, 8, 20, 6, "unregister"], [28, 18, 20, 16], [28, 21, 21, 8, "container"], [28, 30, 21, 17], [28, 31, 20, 6, "unregister"], [28, 41, 20, 16], [29, 6, 22, 4, "register"], [29, 14, 22, 12], [29, 15, 22, 13, "key"], [29, 18, 22, 16], [29, 19, 22, 17], [30, 6, 23, 4], [30, 13, 23, 11], [30, 19, 23, 17, "unregister"], [30, 29, 23, 27], [30, 30, 23, 28, "key"], [30, 33, 23, 31], [30, 34, 23, 32], [31, 4, 24, 2], [31, 5, 24, 3], [31, 7, 24, 5], [31, 8, 24, 6, "container"], [31, 17, 24, 15], [31, 19, 24, 17, "key"], [31, 22, 24, 20], [31, 23, 24, 21], [31, 24, 24, 22], [32, 4, 25, 2], [32, 11, 25, 9, "key"], [32, 14, 25, 12], [33, 2, 26, 0], [34, 0, 26, 1], [34, 3]], "functionMap": {"names": ["<global>", "useRegisterNavigator", "React.useState$argument_0", "React.useEffect$argument_0", "<anonymous>"], "mappings": "AAA;OCU;+BCC,cD;kBEK;WCM,qBD;GFC;CDE"}}, "type": "js/module"}]}