{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isPlainObject = exports.deepFreeze = void 0;\n  var isPlainObject = value => {\n    if (typeof value === 'object' && value !== null) {\n      return Object.getPrototypeOf(value) === Object.prototype;\n    }\n    return false;\n  };\n  exports.isPlainObject = isPlainObject;\n  var deepFreeze = object => {\n    // We only freeze in development to catch issues early\n    // Don't freeze in production to avoid unnecessary performance overhead\n    if (process.env.NODE_ENV === 'production') {\n      return object;\n    }\n    if (Object.isFrozen(object)) {\n      return object;\n    }\n    if (!isPlainObject(object) && !Array.isArray(object)) {\n      return object;\n    }\n\n    // Freeze properties before freezing self\n    for (var key in object) {\n      // Don't freeze objects in params since they are passed by the user\n      if (key !== 'params') {\n        if (Object.getOwnPropertyDescriptor(object, key)?.configurable) {\n          var value = object[key];\n          deepFreeze(value);\n        }\n      }\n    }\n    return Object.freeze(object);\n  };\n  exports.deepFreeze = deepFreeze;\n});", "lineCount": 41, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "isPlainObject"], [7, 23, 1, 13], [7, 26, 1, 13, "exports"], [7, 33, 1, 13], [7, 34, 1, 13, "deepFreeze"], [7, 44, 1, 13], [8, 2, 3, 7], [8, 6, 3, 13, "isPlainObject"], [8, 19, 3, 26], [8, 22, 3, 29, "value"], [8, 27, 3, 34], [8, 31, 3, 38], [9, 4, 4, 2], [9, 8, 4, 6], [9, 15, 4, 13, "value"], [9, 20, 4, 18], [9, 25, 4, 23], [9, 33, 4, 31], [9, 37, 4, 35, "value"], [9, 42, 4, 40], [9, 47, 4, 45], [9, 51, 4, 49], [9, 53, 4, 51], [10, 6, 5, 4], [10, 13, 5, 11, "Object"], [10, 19, 5, 17], [10, 20, 5, 18, "getPrototypeOf"], [10, 34, 5, 32], [10, 35, 5, 33, "value"], [10, 40, 5, 38], [10, 41, 5, 39], [10, 46, 5, 44, "Object"], [10, 52, 5, 50], [10, 53, 5, 51, "prototype"], [10, 62, 5, 60], [11, 4, 6, 2], [12, 4, 7, 2], [12, 11, 7, 9], [12, 16, 7, 14], [13, 2, 8, 0], [13, 3, 8, 1], [14, 2, 8, 2, "exports"], [14, 9, 8, 2], [14, 10, 8, 2, "isPlainObject"], [14, 23, 8, 2], [14, 26, 8, 2, "isPlainObject"], [14, 39, 8, 2], [15, 2, 9, 7], [15, 6, 9, 13, "deepFreeze"], [15, 16, 9, 23], [15, 19, 9, 26, "object"], [15, 25, 9, 32], [15, 29, 9, 36], [16, 4, 10, 2], [17, 4, 11, 2], [18, 4, 12, 2], [18, 8, 12, 6, "process"], [18, 15, 12, 13], [18, 16, 12, 14, "env"], [18, 19, 12, 17], [18, 20, 12, 18, "NODE_ENV"], [18, 28, 12, 26], [18, 33, 12, 31], [18, 45, 12, 43], [18, 47, 12, 45], [19, 6, 13, 4], [19, 13, 13, 11, "object"], [19, 19, 13, 17], [20, 4, 14, 2], [21, 4, 15, 2], [21, 8, 15, 6, "Object"], [21, 14, 15, 12], [21, 15, 15, 13, "isFrozen"], [21, 23, 15, 21], [21, 24, 15, 22, "object"], [21, 30, 15, 28], [21, 31, 15, 29], [21, 33, 15, 31], [22, 6, 16, 4], [22, 13, 16, 11, "object"], [22, 19, 16, 17], [23, 4, 17, 2], [24, 4, 18, 2], [24, 8, 18, 6], [24, 9, 18, 7, "isPlainObject"], [24, 22, 18, 20], [24, 23, 18, 21, "object"], [24, 29, 18, 27], [24, 30, 18, 28], [24, 34, 18, 32], [24, 35, 18, 33, "Array"], [24, 40, 18, 38], [24, 41, 18, 39, "isArray"], [24, 48, 18, 46], [24, 49, 18, 47, "object"], [24, 55, 18, 53], [24, 56, 18, 54], [24, 58, 18, 56], [25, 6, 19, 4], [25, 13, 19, 11, "object"], [25, 19, 19, 17], [26, 4, 20, 2], [28, 4, 22, 2], [29, 4, 23, 2], [29, 9, 23, 7], [29, 13, 23, 13, "key"], [29, 16, 23, 16], [29, 20, 23, 20, "object"], [29, 26, 23, 26], [29, 28, 23, 28], [30, 6, 24, 4], [31, 6, 25, 4], [31, 10, 25, 8, "key"], [31, 13, 25, 11], [31, 18, 25, 16], [31, 26, 25, 24], [31, 28, 25, 26], [32, 8, 26, 6], [32, 12, 26, 10, "Object"], [32, 18, 26, 16], [32, 19, 26, 17, "getOwnPropertyDescriptor"], [32, 43, 26, 41], [32, 44, 26, 42, "object"], [32, 50, 26, 48], [32, 52, 26, 50, "key"], [32, 55, 26, 53], [32, 56, 26, 54], [32, 58, 26, 56, "configurable"], [32, 70, 26, 68], [32, 72, 26, 70], [33, 10, 27, 8], [33, 14, 27, 14, "value"], [33, 19, 27, 19], [33, 22, 27, 22, "object"], [33, 28, 27, 28], [33, 29, 27, 29, "key"], [33, 32, 27, 32], [33, 33, 27, 33], [34, 10, 28, 8, "deepFreeze"], [34, 20, 28, 18], [34, 21, 28, 19, "value"], [34, 26, 28, 24], [34, 27, 28, 25], [35, 8, 29, 6], [36, 6, 30, 4], [37, 4, 31, 2], [38, 4, 32, 2], [38, 11, 32, 9, "Object"], [38, 17, 32, 15], [38, 18, 32, 16, "freeze"], [38, 24, 32, 22], [38, 25, 32, 23, "object"], [38, 31, 32, 29], [38, 32, 32, 30], [39, 2, 33, 0], [39, 3, 33, 1], [40, 2, 33, 2, "exports"], [40, 9, 33, 2], [40, 10, 33, 2, "deepFreeze"], [40, 20, 33, 2], [40, 23, 33, 2, "deepFreeze"], [40, 33, 33, 2], [41, 0, 33, 2], [41, 3]], "functionMap": {"names": ["<global>", "isPlainObject", "deepFreeze"], "mappings": "AAA;6BCE;CDK;0BEC;CFwB"}}, "type": "js/module"}]}