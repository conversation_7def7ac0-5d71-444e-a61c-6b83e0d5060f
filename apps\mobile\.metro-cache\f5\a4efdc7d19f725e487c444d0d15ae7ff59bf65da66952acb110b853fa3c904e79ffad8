{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./CurrentRenderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 65, "index": 112}}], "key": "GTNXIdAk+LGdgfwJMP6/M0rzCrs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useCurrentRender = useCurrentRender;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _CurrentRenderContext = require(_dependencyMap[1], \"./CurrentRenderContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Write the current options, so that server renderer can get current values\n   * Mutating values like this is not safe in async mode, but it doesn't apply to SSR\n   */\n  function useCurrentRender(_ref) {\n    var state = _ref.state,\n      navigation = _ref.navigation,\n      descriptors = _ref.descriptors;\n    var current = React.useContext(_CurrentRenderContext.CurrentRenderContext);\n    if (current && navigation.isFocused()) {\n      current.options = descriptors[state.routes[state.index].key].options;\n    }\n  }\n});", "lineCount": 24, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useCurrentRender"], [7, 26, 1, 13], [7, 29, 1, 13, "useCurrentRender"], [7, 45, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_CurrentRenderContext"], [9, 27, 4, 0], [9, 30, 4, 0, "require"], [9, 37, 4, 0], [9, 38, 4, 0, "_dependencyMap"], [9, 52, 4, 0], [10, 2, 4, 65], [10, 11, 4, 65, "_interopRequireWildcard"], [10, 35, 4, 65, "e"], [10, 36, 4, 65], [10, 38, 4, 65, "t"], [10, 39, 4, 65], [10, 68, 4, 65, "WeakMap"], [10, 75, 4, 65], [10, 81, 4, 65, "r"], [10, 82, 4, 65], [10, 89, 4, 65, "WeakMap"], [10, 96, 4, 65], [10, 100, 4, 65, "n"], [10, 101, 4, 65], [10, 108, 4, 65, "WeakMap"], [10, 115, 4, 65], [10, 127, 4, 65, "_interopRequireWildcard"], [10, 150, 4, 65], [10, 162, 4, 65, "_interopRequireWildcard"], [10, 163, 4, 65, "e"], [10, 164, 4, 65], [10, 166, 4, 65, "t"], [10, 167, 4, 65], [10, 176, 4, 65, "t"], [10, 177, 4, 65], [10, 181, 4, 65, "e"], [10, 182, 4, 65], [10, 186, 4, 65, "e"], [10, 187, 4, 65], [10, 188, 4, 65, "__esModule"], [10, 198, 4, 65], [10, 207, 4, 65, "e"], [10, 208, 4, 65], [10, 214, 4, 65, "o"], [10, 215, 4, 65], [10, 217, 4, 65, "i"], [10, 218, 4, 65], [10, 220, 4, 65, "f"], [10, 221, 4, 65], [10, 226, 4, 65, "__proto__"], [10, 235, 4, 65], [10, 243, 4, 65, "default"], [10, 250, 4, 65], [10, 252, 4, 65, "e"], [10, 253, 4, 65], [10, 270, 4, 65, "e"], [10, 271, 4, 65], [10, 294, 4, 65, "e"], [10, 295, 4, 65], [10, 320, 4, 65, "e"], [10, 321, 4, 65], [10, 330, 4, 65, "f"], [10, 331, 4, 65], [10, 337, 4, 65, "o"], [10, 338, 4, 65], [10, 341, 4, 65, "t"], [10, 342, 4, 65], [10, 345, 4, 65, "n"], [10, 346, 4, 65], [10, 349, 4, 65, "r"], [10, 350, 4, 65], [10, 358, 4, 65, "o"], [10, 359, 4, 65], [10, 360, 4, 65, "has"], [10, 363, 4, 65], [10, 364, 4, 65, "e"], [10, 365, 4, 65], [10, 375, 4, 65, "o"], [10, 376, 4, 65], [10, 377, 4, 65, "get"], [10, 380, 4, 65], [10, 381, 4, 65, "e"], [10, 382, 4, 65], [10, 385, 4, 65, "o"], [10, 386, 4, 65], [10, 387, 4, 65, "set"], [10, 390, 4, 65], [10, 391, 4, 65, "e"], [10, 392, 4, 65], [10, 394, 4, 65, "f"], [10, 395, 4, 65], [10, 409, 4, 65, "_t"], [10, 411, 4, 65], [10, 415, 4, 65, "e"], [10, 416, 4, 65], [10, 432, 4, 65, "_t"], [10, 434, 4, 65], [10, 441, 4, 65, "hasOwnProperty"], [10, 455, 4, 65], [10, 456, 4, 65, "call"], [10, 460, 4, 65], [10, 461, 4, 65, "e"], [10, 462, 4, 65], [10, 464, 4, 65, "_t"], [10, 466, 4, 65], [10, 473, 4, 65, "i"], [10, 474, 4, 65], [10, 478, 4, 65, "o"], [10, 479, 4, 65], [10, 482, 4, 65, "Object"], [10, 488, 4, 65], [10, 489, 4, 65, "defineProperty"], [10, 503, 4, 65], [10, 508, 4, 65, "Object"], [10, 514, 4, 65], [10, 515, 4, 65, "getOwnPropertyDescriptor"], [10, 539, 4, 65], [10, 540, 4, 65, "e"], [10, 541, 4, 65], [10, 543, 4, 65, "_t"], [10, 545, 4, 65], [10, 552, 4, 65, "i"], [10, 553, 4, 65], [10, 554, 4, 65, "get"], [10, 557, 4, 65], [10, 561, 4, 65, "i"], [10, 562, 4, 65], [10, 563, 4, 65, "set"], [10, 566, 4, 65], [10, 570, 4, 65, "o"], [10, 571, 4, 65], [10, 572, 4, 65, "f"], [10, 573, 4, 65], [10, 575, 4, 65, "_t"], [10, 577, 4, 65], [10, 579, 4, 65, "i"], [10, 580, 4, 65], [10, 584, 4, 65, "f"], [10, 585, 4, 65], [10, 586, 4, 65, "_t"], [10, 588, 4, 65], [10, 592, 4, 65, "e"], [10, 593, 4, 65], [10, 594, 4, 65, "_t"], [10, 596, 4, 65], [10, 607, 4, 65, "f"], [10, 608, 4, 65], [10, 613, 4, 65, "e"], [10, 614, 4, 65], [10, 616, 4, 65, "t"], [10, 617, 4, 65], [11, 2, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 2, 9, 7], [15, 11, 9, 16, "useCurrentRender"], [15, 27, 9, 32, "useCurrentRender"], [15, 28, 9, 32, "_ref"], [15, 32, 9, 32], [15, 34, 13, 3], [16, 4, 13, 3], [16, 8, 10, 2, "state"], [16, 13, 10, 7], [16, 16, 10, 7, "_ref"], [16, 20, 10, 7], [16, 21, 10, 2, "state"], [16, 26, 10, 7], [17, 6, 11, 2, "navigation"], [17, 16, 11, 12], [17, 19, 11, 12, "_ref"], [17, 23, 11, 12], [17, 24, 11, 2, "navigation"], [17, 34, 11, 12], [18, 6, 12, 2, "descriptors"], [18, 17, 12, 13], [18, 20, 12, 13, "_ref"], [18, 24, 12, 13], [18, 25, 12, 2, "descriptors"], [18, 36, 12, 13], [19, 4, 14, 2], [19, 8, 14, 8, "current"], [19, 15, 14, 15], [19, 18, 14, 18, "React"], [19, 23, 14, 23], [19, 24, 14, 24, "useContext"], [19, 34, 14, 34], [19, 35, 14, 35, "CurrentRenderContext"], [19, 77, 14, 55], [19, 78, 14, 56], [20, 4, 15, 2], [20, 8, 15, 6, "current"], [20, 15, 15, 13], [20, 19, 15, 17, "navigation"], [20, 29, 15, 27], [20, 30, 15, 28, "isFocused"], [20, 39, 15, 37], [20, 40, 15, 38], [20, 41, 15, 39], [20, 43, 15, 41], [21, 6, 16, 4, "current"], [21, 13, 16, 11], [21, 14, 16, 12, "options"], [21, 21, 16, 19], [21, 24, 16, 22, "descriptors"], [21, 35, 16, 33], [21, 36, 16, 34, "state"], [21, 41, 16, 39], [21, 42, 16, 40, "routes"], [21, 48, 16, 46], [21, 49, 16, 47, "state"], [21, 54, 16, 52], [21, 55, 16, 53, "index"], [21, 60, 16, 58], [21, 61, 16, 59], [21, 62, 16, 60, "key"], [21, 65, 16, 63], [21, 66, 16, 64], [21, 67, 16, 65, "options"], [21, 74, 16, 72], [22, 4, 17, 2], [23, 2, 18, 0], [24, 0, 18, 1], [24, 3]], "functionMap": {"names": ["<global>", "useCurrentRender"], "mappings": "AAA;OCQ;CDS"}}, "type": "js/module"}]}