{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "./errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 70}, "end": {"line": 3, "column": 73, "index": 143}}], "key": "rEld05quROH+iA6QLT6kkvqJ/qc=", "exportNames": ["*"]}}, {"name": "./logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 144}, "end": {"line": 9, "column": 18, "index": 276}}], "key": "RJYKXaUuTbTmL7MuVmczbacEgjY=", "exportNames": ["*"]}}, {"name": "./mockedRequestAnimationFrame", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 277}, "end": {"line": 10, "column": 76, "index": 353}}], "key": "jvDSoC1dDp0COKQ+aujAg83tQSA=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 354}, "end": {"line": 16, "column": 27, "index": 447}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}, {"name": "./threads", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 448}, "end": {"line": 23, "column": 19, "index": 572}}], "key": "ZuB0ICrjKM3htfPQkuonl9kPByQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.callGuardDEV = void 0;\n  exports.initializeUIRuntime = initializeUIRuntime;\n  exports.setupConsole = exports.setupCallGuard = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _errors = require(_dependencyMap[2], \"./errors\");\n  var _logger = require(_dependencyMap[3], \"./logger\");\n  var _mockedRequestAnimationFrame = require(_dependencyMap[4], \"./mockedRequestAnimationFrame\");\n  var _PlatformChecker = require(_dependencyMap[5], \"./PlatformChecker\");\n  var _threads = require(_dependencyMap[6], \"./threads\");\n  var IS_JEST = (0, _PlatformChecker.isJest)();\n  var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n  var IS_CHROME_DEBUGGER = (0, _PlatformChecker.isChromeDebugger)();\n\n  // Override the logFunction implementation with the one that adds logs\n  // with better stack traces to the LogBox (need to override it after `runOnJS`\n  // is defined).\n  var _worklet_11058218612285_init_data = {\n    code: \"function overrideLogFunctionImplementation_initializersTs1(){const{replaceLoggerImplementation,runOnJS,logToLogBoxAndConsole}=this.__closure;replaceLoggerImplementation(function(data){'worklet';runOnJS(logToLogBoxAndConsole)(data);});}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\initializers.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"overrideLogFunctionImplementation_initializersTs1\\\",\\\"replaceLoggerImplementation\\\",\\\"runOnJS\\\",\\\"logToLogBoxAndConsole\\\",\\\"__closure\\\",\\\"data\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/initializers.ts\\\"],\\\"mappings\\\":\\\"AA4BA,SAAAA,kDAAA,QAAAC,2BAAA,CAAAC,OAAA,CAAAC,qBAAA,OAAAC,SAAA,CAAAH,2BAAA,UAAAI,IAAA,EACA,UAEAH,OAAS,CAAAC,qBAAA,EAAAE,IAAA,EAEP,G\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_11999747359509_init_data = {\n    code: \"function initializersTs2(data){const{runOnJS,logToLogBoxAndConsole}=this.__closure;runOnJS(logToLogBoxAndConsole)(data);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\initializers.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"initializersTs2\\\",\\\"data\\\",\\\"runOnJS\\\",\\\"logToLogBoxAndConsole\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/initializers.ts\\\"],\\\"mappings\\\":\\\"AAiC+B,SAAAA,eAASA,CAAAC,IAAA,QAAAC,OAAA,CAAAC,qBAAA,OAAAC,SAAA,CAEpCF,OAAO,CAACC,qBAAqB,CAAC,CAACF,IAAI,CAAC,CACtC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var overrideLogFunctionImplementation = function () {\n    var _e = [new global.Error(), -4, -27];\n    var overrideLogFunctionImplementation = function () {\n      (0, _logger.replaceLoggerImplementation)(function () {\n        var _e = [new global.Error(), -3, -27];\n        var initializersTs2 = function (data) {\n          (0, _threads.runOnJS)(_logger.logToLogBoxAndConsole)(data);\n        };\n        initializersTs2.__closure = {\n          runOnJS: _threads.runOnJS,\n          logToLogBoxAndConsole: _logger.logToLogBoxAndConsole\n        };\n        initializersTs2.__workletHash = 11999747359509;\n        initializersTs2.__initData = _worklet_11999747359509_init_data;\n        initializersTs2.__stackDetails = _e;\n        return initializersTs2;\n      }());\n    };\n    overrideLogFunctionImplementation.__closure = {\n      replaceLoggerImplementation: _logger.replaceLoggerImplementation,\n      runOnJS: _threads.runOnJS,\n      logToLogBoxAndConsole: _logger.logToLogBoxAndConsole\n    };\n    overrideLogFunctionImplementation.__workletHash = 11058218612285;\n    overrideLogFunctionImplementation.__initData = _worklet_11058218612285_init_data;\n    overrideLogFunctionImplementation.__stackDetails = _e;\n    return overrideLogFunctionImplementation;\n  }(); // Register logger config and replace the log function implementation in\n  // the React runtime global scope\n  (0, _logger.registerLoggerConfig)(_logger.DEFAULT_LOGGER_CONFIG);\n  overrideLogFunctionImplementation();\n\n  // this is for web implementation\n  if (SHOULD_BE_USE_WEB) {\n    global._WORKLET = false;\n    global._log = console.log;\n    global._getAnimationTimestamp = () => performance.now();\n  } else {\n    // Register ReanimatedError and logger config in the UI runtime global scope.\n    // (we are using `executeOnUIRuntimeSync` here to make sure that the changes\n    // are applied before any async operations are executed on the UI runtime)\n    (0, _threads.executeOnUIRuntimeSync)(_errors.registerReanimatedError)();\n    (0, _threads.executeOnUIRuntimeSync)(_logger.registerLoggerConfig)(_logger.DEFAULT_LOGGER_CONFIG);\n    (0, _threads.executeOnUIRuntimeSync)(overrideLogFunctionImplementation)();\n  }\n\n  // callGuard is only used with debug builds\n  var _worklet_11064855674442_init_data = {\n    code: \"function callGuardDEV_initializersTs3(fn,...args){try{return fn(...args);}catch(e){if(global.__ErrorUtils){global.__ErrorUtils.reportFatalError(e);}else{throw e;}}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\initializers.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"callGuardDEV_initializersTs3\\\",\\\"fn\\\",\\\"args\\\",\\\"e\\\",\\\"global\\\",\\\"__ErrorUtils\\\",\\\"reportFatalError\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/initializers.ts\\\"],\\\"mappings\\\":\\\"AA2DO,SAAAA,4BAGeA,CAAAC,EAAA,IAAAC,IAAA,EAEpB,GAAI,CACF,MAAO,CAAAD,EAAE,CAAC,GAAGC,IAAI,CAAC,CACpB,CAAE,MAAOC,CAAC,CAAE,CACV,GAAIC,MAAM,CAACC,YAAY,CAAE,CACvBD,MAAM,CAACC,YAAY,CAACC,gBAAgB,CAACH,CAAU,CAAC,CAClD,CAAC,IAAM,CACL,KAAM,CAAAA,CAAC,CACT,CACF,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var callGuardDEV = exports.callGuardDEV = function () {\n    var _e = [new global.Error(), 1, -27];\n    var callGuardDEV = function (fn) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      try {\n        return fn(...args);\n      } catch (e) {\n        if (global.__ErrorUtils) {\n          global.__ErrorUtils.reportFatalError(e);\n        } else {\n          throw e;\n        }\n      }\n    };\n    callGuardDEV.__closure = {};\n    callGuardDEV.__workletHash = 11064855674442;\n    callGuardDEV.__initData = _worklet_11064855674442_init_data;\n    callGuardDEV.__stackDetails = _e;\n    return callGuardDEV;\n  }();\n  var _worklet_8689977933756_init_data = {\n    code: \"function setupCallGuard_initializersTs4(){const{callGuardDEV,runOnJS,reportFatalErrorOnJS}=this.__closure;global.__callGuardDEV=callGuardDEV;global.__ErrorUtils={reportFatalError:function(error){runOnJS(reportFatalErrorOnJS)({message:error.message,stack:error.stack});}};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\initializers.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setupCallGuard_initializersTs4\\\",\\\"callGuardDEV\\\",\\\"runOnJS\\\",\\\"reportFatalErrorOnJS\\\",\\\"__closure\\\",\\\"global\\\",\\\"__callGuardDEV\\\",\\\"__ErrorUtils\\\",\\\"reportFatalError\\\",\\\"error\\\",\\\"message\\\",\\\"stack\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/initializers.ts\\\"],\\\"mappings\\\":\\\"AA2EO,SAAAA,8BAA0BA,CAAA,QAAAC,YAAA,CAAAC,OAAA,CAAAC,oBAAA,OAAAC,SAAA,CAE/BC,MAAM,CAACC,cAAc,CAAGL,YAAY,CACpCI,MAAM,CAACE,YAAY,CAAG,CACpBC,gBAAgB,CAAE,QAAAA,CAACC,KAAY,CAAK,CAClCP,OAAO,CAACC,oBAAoB,CAAC,CAAC,CAC5BO,OAAO,CAAED,KAAK,CAACC,OAAO,CACtBC,KAAK,CAAEF,KAAK,CAACE,KACf,CAAC,CAAC,CACJ,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var setupCallGuard = exports.setupCallGuard = function () {\n    var _e = [new global.Error(), -4, -27];\n    var setupCallGuard = function () {\n      global.__callGuardDEV = callGuardDEV;\n      global.__ErrorUtils = {\n        reportFatalError: error => {\n          (0, _threads.runOnJS)(_errors.reportFatalErrorOnJS)({\n            message: error.message,\n            stack: error.stack\n          });\n        }\n      };\n    };\n    setupCallGuard.__closure = {\n      callGuardDEV,\n      runOnJS: _threads.runOnJS,\n      reportFatalErrorOnJS: _errors.reportFatalErrorOnJS\n    };\n    setupCallGuard.__workletHash = 8689977933756;\n    setupCallGuard.__initData = _worklet_8689977933756_init_data;\n    setupCallGuard.__stackDetails = _e;\n    return setupCallGuard;\n  }();\n  /**\n   * Currently there seems to be a bug in the JSI layer which causes a crash when\n   * we try to copy some of the console methods, i.e. `clear` or `dirxml`.\n   *\n   * The crash happens only in React Native 0.75. It's not reproducible in neither\n   * 0.76 nor 0.74. It also happens only in the configuration of a debug app and\n   * production bundle.\n   *\n   * I haven't yet discovered what exactly causes the crash. It's tied to the\n   * console methods sometimes being `HostFunction`s. Therefore, as a workaround\n   * we don't copy the methods as they are in the original console object, we copy\n   * JavaScript wrappers instead.\n   */\n  function createMemorySafeCapturableConsole() {\n    var consoleCopy = Object.fromEntries(Object.entries(console).map(_ref => {\n      var _ref2 = (0, _slicedToArray2.default)(_ref, 2),\n        methodName = _ref2[0],\n        method = _ref2[1];\n      var methodWrapper = function methodWrapper() {\n        return method(...arguments);\n      };\n      if (method.name) {\n        /**\n         * Set the original method name as the wrapper name if available.\n         *\n         * It might be unnecessary but if we want to fully mimic the console\n         * object we should take into the account the fact some code might rely\n         * on the method name.\n         */\n        Object.defineProperty(methodWrapper, 'name', {\n          value: method.name,\n          writable: false\n        });\n      }\n      return [methodName, methodWrapper];\n    }));\n    return consoleCopy;\n  }\n\n  // We really have to create a copy of console here. Function runOnJS we use on elements inside\n  // this object makes it not configurable\n  var capturableConsole = createMemorySafeCapturableConsole();\n  var _worklet_5303065199952_init_data = {\n    code: \"function setupConsole_initializersTs5(){const{IS_CHROME_DEBUGGER,runOnJS,capturableConsole}=this.__closure;if(!IS_CHROME_DEBUGGER){global.console={assert:runOnJS(capturableConsole.assert),debug:runOnJS(capturableConsole.debug),log:runOnJS(capturableConsole.log),warn:runOnJS(capturableConsole.warn),error:runOnJS(capturableConsole.error),info:runOnJS(capturableConsole.info)};}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\initializers.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setupConsole_initializersTs5\\\",\\\"IS_CHROME_DEBUGGER\\\",\\\"runOnJS\\\",\\\"capturableConsole\\\",\\\"__closure\\\",\\\"global\\\",\\\"console\\\",\\\"assert\\\",\\\"debug\\\",\\\"log\\\",\\\"warn\\\",\\\"error\\\",\\\"info\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/initializers.ts\\\"],\\\"mappings\\\":\\\"AAmIO,SAAAA,4BAAwBA,CAAA,QAAAC,kBAAA,CAAAC,OAAA,CAAAC,iBAAA,OAAAC,SAAA,CAE7B,GAAI,CAACH,kBAAkB,CAAE,CAEvBI,MAAM,CAACC,OAAO,CAAG,CAEfC,MAAM,CAAEL,OAAO,CAACC,iBAAiB,CAACI,MAAM,CAAC,CACzCC,KAAK,CAAEN,OAAO,CAACC,iBAAiB,CAACK,KAAK,CAAC,CACvCC,GAAG,CAAEP,OAAO,CAACC,iBAAiB,CAACM,GAAG,CAAC,CACnCC,IAAI,CAAER,OAAO,CAACC,iBAAiB,CAACO,IAAI,CAAC,CACrCC,KAAK,CAAET,OAAO,CAACC,iBAAiB,CAACQ,KAAK,CAAC,CACvCC,IAAI,CAAEV,OAAO,CAACC,iBAAiB,CAACS,IAAI,CAEtC,CAAC,CACH,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var setupConsole = exports.setupConsole = function () {\n    var _e = [new global.Error(), -4, -27];\n    var setupConsole = function () {\n      if (!IS_CHROME_DEBUGGER) {\n        // @ts-ignore TypeScript doesn't like that there are missing methods in console object, but we don't provide all the methods for the UI runtime console version\n        global.console = {\n          /* eslint-disable @typescript-eslint/unbound-method */\n          assert: (0, _threads.runOnJS)(capturableConsole.assert),\n          debug: (0, _threads.runOnJS)(capturableConsole.debug),\n          log: (0, _threads.runOnJS)(capturableConsole.log),\n          warn: (0, _threads.runOnJS)(capturableConsole.warn),\n          error: (0, _threads.runOnJS)(capturableConsole.error),\n          info: (0, _threads.runOnJS)(capturableConsole.info)\n          /* eslint-enable @typescript-eslint/unbound-method */\n        };\n      }\n    };\n    setupConsole.__closure = {\n      IS_CHROME_DEBUGGER,\n      runOnJS: _threads.runOnJS,\n      capturableConsole\n    };\n    setupConsole.__workletHash = 5303065199952;\n    setupConsole.__initData = _worklet_5303065199952_init_data;\n    setupConsole.__stackDetails = _e;\n    return setupConsole;\n  }();\n  var _worklet_3255663687300_init_data = {\n    code: \"function setupRequestAnimationFrame_initializersTs6(){const{callMicrotasks}=this.__closure;const nativeRequestAnimationFrame=global.requestAnimationFrame;let animationFrameCallbacks=[];let flushRequested=false;global.__flushAnimationFrame=function(frameTimestamp){const currentCallbacks=animationFrameCallbacks;animationFrameCallbacks=[];currentCallbacks.forEach(function(f){return f(frameTimestamp);});callMicrotasks();};global.requestAnimationFrame=function(callback){animationFrameCallbacks.push(callback);if(!flushRequested){flushRequested=true;nativeRequestAnimationFrame(function(timestamp){flushRequested=false;global.__frameTimestamp=timestamp;global.__flushAnimationFrame(timestamp);global.__frameTimestamp=undefined;});}return-1;};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\initializers.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setupRequestAnimationFrame_initializersTs6\\\",\\\"callMicrotasks\\\",\\\"__closure\\\",\\\"nativeRequestAnimationFrame\\\",\\\"global\\\",\\\"requestAnimationFrame\\\",\\\"animationFrameCallbacks\\\",\\\"flushRequested\\\",\\\"__flushAnimationFrame\\\",\\\"frameTimestamp\\\",\\\"currentCallbacks\\\",\\\"forEach\\\",\\\"f\\\",\\\"callback\\\",\\\"push\\\",\\\"timestamp\\\",\\\"__frameTimestamp\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/initializers.ts\\\"],\\\"mappings\\\":\\\"AAoJA,SAAAA,0CAAsCA,CAAA,QAAAC,cAAA,OAAAC,SAAA,CAKpC,KAAM,CAAAC,2BAA2B,CAAGC,MAAM,CAACC,qBAAqB,CAEhE,GAAI,CAAAC,uBAA2D,CAAG,EAAE,CACpE,GAAI,CAAAC,cAAc,CAAG,KAAK,CAE1BH,MAAM,CAACI,qBAAqB,CAAG,SAACC,cAAsB,CAAK,CACzD,KAAM,CAAAC,gBAAgB,CAAGJ,uBAAuB,CAChDA,uBAAuB,CAAG,EAAE,CAC5BI,gBAAgB,CAACC,OAAO,CAAE,SAAAC,CAAC,QAAK,CAAAA,CAAC,CAACH,cAAc,CAAC,GAAC,CAClDR,cAAc,CAAC,CAAC,CAClB,CAAC,CAEDG,MAAM,CAACC,qBAAqB,CAAG,SAC7BQ,QAAqC,CAC1B,CACXP,uBAAuB,CAACQ,IAAI,CAACD,QAAQ,CAAC,CACtC,GAAI,CAACN,cAAc,CAAE,CACnBA,cAAc,CAAG,IAAI,CACrBJ,2BAA2B,CAAE,SAAAY,SAAS,CAAK,CACzCR,cAAc,CAAG,KAAK,CACtBH,MAAM,CAACY,gBAAgB,CAAGD,SAAS,CACnCX,MAAM,CAACI,qBAAqB,CAACO,SAAS,CAAC,CACvCX,MAAM,CAACY,gBAAgB,CAAGC,SAAS,CACrC,CAAC,CAAC,CACJ,CAKA,MAAO,CAAC,CAAC,CACX,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var setupRequestAnimationFrame = function () {\n    var _e = [new global.Error(), -2, -27];\n    var setupRequestAnimationFrame = function () {\n      // Jest mocks requestAnimationFrame API and it does not like if that mock gets overridden\n      // so we avoid doing requestAnimationFrame batching in Jest environment.\n      var nativeRequestAnimationFrame = global.requestAnimationFrame;\n      var animationFrameCallbacks = [];\n      var flushRequested = false;\n      global.__flushAnimationFrame = frameTimestamp => {\n        var currentCallbacks = animationFrameCallbacks;\n        animationFrameCallbacks = [];\n        currentCallbacks.forEach(f => f(frameTimestamp));\n        (0, _threads.callMicrotasks)();\n      };\n      global.requestAnimationFrame = callback => {\n        animationFrameCallbacks.push(callback);\n        if (!flushRequested) {\n          flushRequested = true;\n          nativeRequestAnimationFrame(timestamp => {\n            flushRequested = false;\n            global.__frameTimestamp = timestamp;\n            global.__flushAnimationFrame(timestamp);\n            global.__frameTimestamp = undefined;\n          });\n        }\n        // Reanimated currently does not support cancelling callbacks requested with\n        // requestAnimationFrame. We return -1 as identifier which isn't in line\n        // with the spec but it should give users better clue in case they actually\n        // attempt to store the value returned from rAF and use it for cancelling.\n        return -1;\n      };\n    };\n    setupRequestAnimationFrame.__closure = {\n      callMicrotasks: _threads.callMicrotasks\n    };\n    setupRequestAnimationFrame.__workletHash = 3255663687300;\n    setupRequestAnimationFrame.__initData = _worklet_3255663687300_init_data;\n    setupRequestAnimationFrame.__stackDetails = _e;\n    return setupRequestAnimationFrame;\n  }();\n  var _worklet_14702258885038_init_data = {\n    code: \"function initializersTs7(){const{setupCallGuard,setupConsole,SHOULD_BE_USE_WEB,setupMicrotasks,setupRequestAnimationFrame}=this.__closure;setupCallGuard();setupConsole();if(!SHOULD_BE_USE_WEB){setupMicrotasks();setupRequestAnimationFrame();}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\initializers.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"initializersTs7\\\",\\\"setupCallGuard\\\",\\\"setupConsole\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"setupMicrotasks\\\",\\\"setupRequestAnimationFrame\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/initializers.ts\\\"],\\\"mappings\\\":\\\"AA8MqB,SAAAA,eAAMA,CAAA,QAAAC,cAAA,CAAAC,YAAA,CAAAC,iBAAA,CAAAC,eAAA,CAAAC,0BAAA,OAAAC,SAAA,CAEvBL,cAAc,CAAC,CAAC,CAChBC,YAAY,CAAC,CAAC,CACd,GAAI,CAACC,iBAAiB,CAAE,CACtBC,eAAe,CAAC,CAAC,CACjBC,0BAA0B,CAAC,CAAC,CAC9B,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function initializeUIRuntime(ReanimatedModule) {\n    if ((0, _PlatformChecker.isWeb)()) {\n      return;\n    }\n    if (!ReanimatedModule) {\n      // eslint-disable-next-line reanimated/use-reanimated-error\n      throw new Error('[Reanimated] Reanimated is trying to initialize the UI runtime without a valid ReanimatedModule');\n    }\n    if (IS_JEST) {\n      // requestAnimationFrame react-native jest's setup is incorrect as it polyfills\n      // the method directly using setTimeout, therefore the callback doesn't get the\n      // expected timestamp as the only argument: https://github.com/facebook/react-native/blob/main/packages/react-native/jest/setup.js#L28\n      // We override this setup here to make sure that callbacks get the proper timestamps\n      // when executed. For non-jest environments we define requestAnimationFrame in setupRequestAnimationFrame\n      // @ts-ignore TypeScript uses Node definition for rAF, setTimeout, etc which returns a Timeout object rather than a number\n      globalThis.requestAnimationFrame = _mockedRequestAnimationFrame.mockedRequestAnimationFrame;\n    }\n    (0, _threads.runOnUIImmediately)(function () {\n      var _e = [new global.Error(), -6, -27];\n      var initializersTs7 = function () {\n        setupCallGuard();\n        setupConsole();\n        if (!SHOULD_BE_USE_WEB) {\n          (0, _threads.setupMicrotasks)();\n          setupRequestAnimationFrame();\n        }\n      };\n      initializersTs7.__closure = {\n        setupCallGuard,\n        setupConsole,\n        SHOULD_BE_USE_WEB,\n        setupMicrotasks: _threads.setupMicrotasks,\n        setupRequestAnimationFrame\n      };\n      initializersTs7.__workletHash = 14702258885038;\n      initializersTs7.__initData = _worklet_14702258885038_init_data;\n      initializersTs7.__stackDetails = _e;\n      return initializersTs7;\n    }())();\n  }\n});", "lineCount": 307, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "callGuardDEV"], [8, 22, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "initializeUIRuntime"], [9, 29, 1, 13], [9, 32, 1, 13, "initializeUIRuntime"], [9, 51, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "setupConsole"], [10, 22, 1, 13], [10, 25, 1, 13, "exports"], [10, 32, 1, 13], [10, 33, 1, 13, "setupCallGuard"], [10, 47, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_slicedToArray2"], [11, 21, 1, 13], [11, 24, 1, 13, "_interopRequireDefault"], [11, 46, 1, 13], [11, 47, 1, 13, "require"], [11, 54, 1, 13], [11, 55, 1, 13, "_dependencyMap"], [11, 69, 1, 13], [12, 2, 3, 0], [12, 6, 3, 0, "_errors"], [12, 13, 3, 0], [12, 16, 3, 0, "require"], [12, 23, 3, 0], [12, 24, 3, 0, "_dependencyMap"], [12, 38, 3, 0], [13, 2, 4, 0], [13, 6, 4, 0, "_logger"], [13, 13, 4, 0], [13, 16, 4, 0, "require"], [13, 23, 4, 0], [13, 24, 4, 0, "_dependencyMap"], [13, 38, 4, 0], [14, 2, 10, 0], [14, 6, 10, 0, "_mockedRequestAnimationFrame"], [14, 34, 10, 0], [14, 37, 10, 0, "require"], [14, 44, 10, 0], [14, 45, 10, 0, "_dependencyMap"], [14, 59, 10, 0], [15, 2, 11, 0], [15, 6, 11, 0, "_PlatformChecker"], [15, 22, 11, 0], [15, 25, 11, 0, "require"], [15, 32, 11, 0], [15, 33, 11, 0, "_dependencyMap"], [15, 47, 11, 0], [16, 2, 17, 0], [16, 6, 17, 0, "_threads"], [16, 14, 17, 0], [16, 17, 17, 0, "require"], [16, 24, 17, 0], [16, 25, 17, 0, "_dependencyMap"], [16, 39, 17, 0], [17, 2, 25, 0], [17, 6, 25, 6, "IS_JEST"], [17, 13, 25, 13], [17, 16, 25, 16], [17, 20, 25, 16, "isJest"], [17, 43, 25, 22], [17, 45, 25, 23], [17, 46, 25, 24], [18, 2, 26, 0], [18, 6, 26, 6, "SHOULD_BE_USE_WEB"], [18, 23, 26, 23], [18, 26, 26, 26], [18, 30, 26, 26, "shouldBeUseWeb"], [18, 61, 26, 40], [18, 63, 26, 41], [18, 64, 26, 42], [19, 2, 27, 0], [19, 6, 27, 6, "IS_CHROME_DEBUGGER"], [19, 24, 27, 24], [19, 27, 27, 27], [19, 31, 27, 27, "isChromeDebugger"], [19, 64, 27, 43], [19, 66, 27, 44], [19, 67, 27, 45], [21, 2, 29, 0], [22, 2, 30, 0], [23, 2, 31, 0], [24, 2, 31, 0], [24, 6, 31, 0, "_worklet_11058218612285_init_data"], [24, 39, 31, 0], [25, 4, 31, 0, "code"], [25, 8, 31, 0], [26, 4, 31, 0, "location"], [26, 12, 31, 0], [27, 4, 31, 0, "sourceMap"], [27, 13, 31, 0], [28, 4, 31, 0, "version"], [28, 11, 31, 0], [29, 2, 31, 0], [30, 2, 31, 0], [30, 6, 31, 0, "_worklet_11999747359509_init_data"], [30, 39, 31, 0], [31, 4, 31, 0, "code"], [31, 8, 31, 0], [32, 4, 31, 0, "location"], [32, 12, 31, 0], [33, 4, 31, 0, "sourceMap"], [33, 13, 31, 0], [34, 4, 31, 0, "version"], [34, 11, 31, 0], [35, 2, 31, 0], [36, 2, 31, 0], [36, 6, 31, 0, "overrideLogFunctionImplementation"], [36, 39, 31, 0], [36, 42, 32, 0], [37, 4, 32, 0], [37, 8, 32, 0, "_e"], [37, 10, 32, 0], [37, 18, 32, 0, "global"], [37, 24, 32, 0], [37, 25, 32, 0, "Error"], [37, 30, 32, 0], [38, 4, 32, 0], [38, 8, 32, 0, "overrideLogFunctionImplementation"], [38, 41, 32, 0], [38, 53, 32, 0, "overrideLogFunctionImplementation"], [38, 54, 32, 0], [38, 56, 32, 45], [39, 6, 34, 2], [39, 10, 34, 2, "replaceLoggerImplementation"], [39, 45, 34, 29], [39, 47, 34, 30], [40, 8, 34, 30], [40, 12, 34, 30, "_e"], [40, 14, 34, 30], [40, 22, 34, 30, "global"], [40, 28, 34, 30], [40, 29, 34, 30, "Error"], [40, 34, 34, 30], [41, 8, 34, 30], [41, 12, 34, 30, "initializersTs2"], [41, 27, 34, 30], [41, 39, 34, 30, "initializersTs2"], [41, 40, 34, 31, "data"], [41, 44, 34, 35], [41, 46, 34, 40], [42, 10, 36, 4], [42, 14, 36, 4, "runOnJS"], [42, 30, 36, 11], [42, 32, 36, 12, "logToLogBoxAndConsole"], [42, 61, 36, 33], [42, 62, 36, 34], [42, 63, 36, 35, "data"], [42, 67, 36, 39], [42, 68, 36, 40], [43, 8, 37, 2], [43, 9, 37, 3], [44, 8, 37, 3, "initializersTs2"], [44, 23, 37, 3], [44, 24, 37, 3, "__closure"], [44, 33, 37, 3], [45, 10, 37, 3, "runOnJS"], [45, 17, 37, 3], [45, 19, 36, 4, "runOnJS"], [45, 35, 36, 11], [46, 10, 36, 11, "logToLogBoxAndConsole"], [46, 31, 36, 11], [46, 33, 36, 12, "logToLogBoxAndConsole"], [47, 8, 36, 33], [48, 8, 36, 33, "initializersTs2"], [48, 23, 36, 33], [48, 24, 36, 33, "__workletHash"], [48, 37, 36, 33], [49, 8, 36, 33, "initializersTs2"], [49, 23, 36, 33], [49, 24, 36, 33, "__initData"], [49, 34, 36, 33], [49, 37, 36, 33, "_worklet_11999747359509_init_data"], [49, 70, 36, 33], [50, 8, 36, 33, "initializersTs2"], [50, 23, 36, 33], [50, 24, 36, 33, "__stackDetails"], [50, 38, 36, 33], [50, 41, 36, 33, "_e"], [50, 43, 36, 33], [51, 8, 36, 33], [51, 15, 36, 33, "initializersTs2"], [51, 30, 36, 33], [52, 6, 36, 33], [52, 7, 34, 30], [52, 9, 37, 3], [52, 10, 37, 4], [53, 4, 38, 0], [53, 5, 38, 1], [54, 4, 38, 1, "overrideLogFunctionImplementation"], [54, 37, 38, 1], [54, 38, 38, 1, "__closure"], [54, 47, 38, 1], [55, 6, 38, 1, "replaceLoggerImplementation"], [55, 33, 38, 1], [55, 35, 34, 2, "replaceLoggerImplementation"], [55, 70, 34, 29], [56, 6, 34, 29, "runOnJS"], [56, 13, 34, 29], [56, 15, 36, 4, "runOnJS"], [56, 31, 36, 11], [57, 6, 36, 11, "logToLogBoxAndConsole"], [57, 27, 36, 11], [57, 29, 36, 12, "logToLogBoxAndConsole"], [58, 4, 36, 33], [59, 4, 36, 33, "overrideLogFunctionImplementation"], [59, 37, 36, 33], [59, 38, 36, 33, "__workletHash"], [59, 51, 36, 33], [60, 4, 36, 33, "overrideLogFunctionImplementation"], [60, 37, 36, 33], [60, 38, 36, 33, "__initData"], [60, 48, 36, 33], [60, 51, 36, 33, "_worklet_11058218612285_init_data"], [60, 84, 36, 33], [61, 4, 36, 33, "overrideLogFunctionImplementation"], [61, 37, 36, 33], [61, 38, 36, 33, "__stackDetails"], [61, 52, 36, 33], [61, 55, 36, 33, "_e"], [61, 57, 36, 33], [62, 4, 36, 33], [62, 11, 36, 33, "overrideLogFunctionImplementation"], [62, 44, 36, 33], [63, 2, 36, 33], [63, 3, 32, 0], [63, 7, 40, 0], [64, 2, 41, 0], [65, 2, 42, 0], [65, 6, 42, 0, "registerLoggerConfig"], [65, 34, 42, 20], [65, 36, 42, 21, "DEFAULT_LOGGER_CONFIG"], [65, 65, 42, 42], [65, 66, 42, 43], [66, 2, 43, 0, "overrideLogFunctionImplementation"], [66, 35, 43, 33], [66, 36, 43, 34], [66, 37, 43, 35], [68, 2, 45, 0], [69, 2, 46, 0], [69, 6, 46, 4, "SHOULD_BE_USE_WEB"], [69, 23, 46, 21], [69, 25, 46, 23], [70, 4, 47, 2, "global"], [70, 10, 47, 8], [70, 11, 47, 9, "_WORKLET"], [70, 19, 47, 17], [70, 22, 47, 20], [70, 27, 47, 25], [71, 4, 48, 2, "global"], [71, 10, 48, 8], [71, 11, 48, 9, "_log"], [71, 15, 48, 13], [71, 18, 48, 16, "console"], [71, 25, 48, 23], [71, 26, 48, 24, "log"], [71, 29, 48, 27], [72, 4, 49, 2, "global"], [72, 10, 49, 8], [72, 11, 49, 9, "_getAnimationTimestamp"], [72, 33, 49, 31], [72, 36, 49, 34], [72, 42, 49, 40, "performance"], [72, 53, 49, 51], [72, 54, 49, 52, "now"], [72, 57, 49, 55], [72, 58, 49, 56], [72, 59, 49, 57], [73, 2, 50, 0], [73, 3, 50, 1], [73, 9, 50, 7], [74, 4, 51, 2], [75, 4, 52, 2], [76, 4, 53, 2], [77, 4, 54, 2], [77, 8, 54, 2, "executeOnUIRuntimeSync"], [77, 39, 54, 24], [77, 41, 54, 25, "registerReanimatedError"], [77, 72, 54, 48], [77, 73, 54, 49], [77, 74, 54, 50], [77, 75, 54, 51], [78, 4, 55, 2], [78, 8, 55, 2, "executeOnUIRuntimeSync"], [78, 39, 55, 24], [78, 41, 55, 25, "registerLoggerConfig"], [78, 69, 55, 45], [78, 70, 55, 46], [78, 71, 55, 47, "DEFAULT_LOGGER_CONFIG"], [78, 100, 55, 68], [78, 101, 55, 69], [79, 4, 56, 2], [79, 8, 56, 2, "executeOnUIRuntimeSync"], [79, 39, 56, 24], [79, 41, 56, 25, "overrideLogFunctionImplementation"], [79, 74, 56, 58], [79, 75, 56, 59], [79, 76, 56, 60], [79, 77, 56, 61], [80, 2, 57, 0], [82, 2, 59, 0], [83, 2, 59, 0], [83, 6, 59, 0, "_worklet_11064855674442_init_data"], [83, 39, 59, 0], [84, 4, 59, 0, "code"], [84, 8, 59, 0], [85, 4, 59, 0, "location"], [85, 12, 59, 0], [86, 4, 59, 0, "sourceMap"], [86, 13, 59, 0], [87, 4, 59, 0, "version"], [87, 11, 59, 0], [88, 2, 59, 0], [89, 2, 59, 0], [89, 6, 59, 0, "callGuardDEV"], [89, 18, 59, 0], [89, 21, 59, 0, "exports"], [89, 28, 59, 0], [89, 29, 59, 0, "callGuardDEV"], [89, 41, 59, 0], [89, 44, 60, 7], [90, 4, 60, 7], [90, 8, 60, 7, "_e"], [90, 10, 60, 7], [90, 18, 60, 7, "global"], [90, 24, 60, 7], [90, 25, 60, 7, "Error"], [90, 30, 60, 7], [91, 4, 60, 7], [91, 8, 60, 7, "callGuardDEV"], [91, 20, 60, 7], [91, 32, 60, 7, "callGuardDEV"], [91, 33, 61, 2, "fn"], [91, 35, 61, 36], [91, 37, 63, 22], [92, 6, 63, 22], [92, 15, 63, 22, "_len"], [92, 19, 63, 22], [92, 22, 63, 22, "arguments"], [92, 31, 63, 22], [92, 32, 63, 22, "length"], [92, 38, 63, 22], [92, 40, 62, 5, "args"], [92, 44, 62, 9], [92, 51, 62, 9, "Array"], [92, 56, 62, 9], [92, 57, 62, 9, "_len"], [92, 61, 62, 9], [92, 68, 62, 9, "_len"], [92, 72, 62, 9], [92, 83, 62, 9, "_key"], [92, 87, 62, 9], [92, 93, 62, 9, "_key"], [92, 97, 62, 9], [92, 100, 62, 9, "_len"], [92, 104, 62, 9], [92, 106, 62, 9, "_key"], [92, 110, 62, 9], [93, 8, 62, 5, "args"], [93, 12, 62, 9], [93, 13, 62, 9, "_key"], [93, 17, 62, 9], [93, 25, 62, 9, "arguments"], [93, 34, 62, 9], [93, 35, 62, 9, "_key"], [93, 39, 62, 9], [94, 6, 62, 9], [95, 6, 65, 2], [95, 10, 65, 6], [96, 8, 66, 4], [96, 15, 66, 11, "fn"], [96, 17, 66, 13], [96, 18, 66, 14], [96, 21, 66, 17, "args"], [96, 25, 66, 21], [96, 26, 66, 22], [97, 6, 67, 2], [97, 7, 67, 3], [97, 8, 67, 4], [97, 15, 67, 11, "e"], [97, 16, 67, 12], [97, 18, 67, 14], [98, 8, 68, 4], [98, 12, 68, 8, "global"], [98, 18, 68, 14], [98, 19, 68, 15, "__E<PERSON><PERSON><PERSON><PERSON>s"], [98, 31, 68, 27], [98, 33, 68, 29], [99, 10, 69, 6, "global"], [99, 16, 69, 12], [99, 17, 69, 13, "__E<PERSON><PERSON><PERSON><PERSON>s"], [99, 29, 69, 25], [99, 30, 69, 26, "reportFatalError"], [99, 46, 69, 42], [99, 47, 69, 43, "e"], [99, 48, 69, 53], [99, 49, 69, 54], [100, 8, 70, 4], [100, 9, 70, 5], [100, 15, 70, 11], [101, 10, 71, 6], [101, 16, 71, 12, "e"], [101, 17, 71, 13], [102, 8, 72, 4], [103, 6, 73, 2], [104, 4, 74, 0], [104, 5, 74, 1], [105, 4, 74, 1, "callGuardDEV"], [105, 16, 74, 1], [105, 17, 74, 1, "__closure"], [105, 26, 74, 1], [106, 4, 74, 1, "callGuardDEV"], [106, 16, 74, 1], [106, 17, 74, 1, "__workletHash"], [106, 30, 74, 1], [107, 4, 74, 1, "callGuardDEV"], [107, 16, 74, 1], [107, 17, 74, 1, "__initData"], [107, 27, 74, 1], [107, 30, 74, 1, "_worklet_11064855674442_init_data"], [107, 63, 74, 1], [108, 4, 74, 1, "callGuardDEV"], [108, 16, 74, 1], [108, 17, 74, 1, "__stackDetails"], [108, 31, 74, 1], [108, 34, 74, 1, "_e"], [108, 36, 74, 1], [109, 4, 74, 1], [109, 11, 74, 1, "callGuardDEV"], [109, 23, 74, 1], [110, 2, 74, 1], [110, 3, 60, 7], [111, 2, 60, 7], [111, 6, 60, 7, "_worklet_8689977933756_init_data"], [111, 38, 60, 7], [112, 4, 60, 7, "code"], [112, 8, 60, 7], [113, 4, 60, 7, "location"], [113, 12, 60, 7], [114, 4, 60, 7, "sourceMap"], [114, 13, 60, 7], [115, 4, 60, 7, "version"], [115, 11, 60, 7], [116, 2, 60, 7], [117, 2, 60, 7], [117, 6, 60, 7, "setupCallGuard"], [117, 20, 60, 7], [117, 23, 60, 7, "exports"], [117, 30, 60, 7], [117, 31, 60, 7, "setupCallGuard"], [117, 45, 60, 7], [117, 48, 76, 7], [118, 4, 76, 7], [118, 8, 76, 7, "_e"], [118, 10, 76, 7], [118, 18, 76, 7, "global"], [118, 24, 76, 7], [118, 25, 76, 7, "Error"], [118, 30, 76, 7], [119, 4, 76, 7], [119, 8, 76, 7, "setupCallGuard"], [119, 22, 76, 7], [119, 34, 76, 7, "setupCallGuard"], [119, 35, 76, 7], [119, 37, 76, 33], [120, 6, 78, 2, "global"], [120, 12, 78, 8], [120, 13, 78, 9, "__callGuardDEV"], [120, 27, 78, 23], [120, 30, 78, 26, "callGuardDEV"], [120, 42, 78, 38], [121, 6, 79, 2, "global"], [121, 12, 79, 8], [121, 13, 79, 9, "__E<PERSON><PERSON><PERSON><PERSON>s"], [121, 25, 79, 21], [121, 28, 79, 24], [122, 8, 80, 4, "reportFatalError"], [122, 24, 80, 20], [122, 26, 80, 23, "error"], [122, 31, 80, 35], [122, 35, 80, 40], [123, 10, 81, 6], [123, 14, 81, 6, "runOnJS"], [123, 30, 81, 13], [123, 32, 81, 14, "reportFatalErrorOnJS"], [123, 60, 81, 34], [123, 61, 81, 35], [123, 62, 81, 36], [124, 12, 82, 8, "message"], [124, 19, 82, 15], [124, 21, 82, 17, "error"], [124, 26, 82, 22], [124, 27, 82, 23, "message"], [124, 34, 82, 30], [125, 12, 83, 8, "stack"], [125, 17, 83, 13], [125, 19, 83, 15, "error"], [125, 24, 83, 20], [125, 25, 83, 21, "stack"], [126, 10, 84, 6], [126, 11, 84, 7], [126, 12, 84, 8], [127, 8, 85, 4], [128, 6, 86, 2], [128, 7, 86, 3], [129, 4, 87, 0], [129, 5, 87, 1], [130, 4, 87, 1, "setupCallGuard"], [130, 18, 87, 1], [130, 19, 87, 1, "__closure"], [130, 28, 87, 1], [131, 6, 87, 1, "callGuardDEV"], [131, 18, 87, 1], [132, 6, 87, 1, "runOnJS"], [132, 13, 87, 1], [132, 15, 81, 6, "runOnJS"], [132, 31, 81, 13], [133, 6, 81, 13, "reportFatalErrorOnJS"], [133, 26, 81, 13], [133, 28, 81, 14, "reportFatalErrorOnJS"], [134, 4, 81, 34], [135, 4, 81, 34, "setupCallGuard"], [135, 18, 81, 34], [135, 19, 81, 34, "__workletHash"], [135, 32, 81, 34], [136, 4, 81, 34, "setupCallGuard"], [136, 18, 81, 34], [136, 19, 81, 34, "__initData"], [136, 29, 81, 34], [136, 32, 81, 34, "_worklet_8689977933756_init_data"], [136, 64, 81, 34], [137, 4, 81, 34, "setupCallGuard"], [137, 18, 81, 34], [137, 19, 81, 34, "__stackDetails"], [137, 33, 81, 34], [137, 36, 81, 34, "_e"], [137, 38, 81, 34], [138, 4, 81, 34], [138, 11, 81, 34, "setupCallGuard"], [138, 25, 81, 34], [139, 2, 81, 34], [139, 3, 76, 7], [140, 2, 89, 0], [141, 0, 90, 0], [142, 0, 91, 0], [143, 0, 92, 0], [144, 0, 93, 0], [145, 0, 94, 0], [146, 0, 95, 0], [147, 0, 96, 0], [148, 0, 97, 0], [149, 0, 98, 0], [150, 0, 99, 0], [151, 0, 100, 0], [152, 0, 101, 0], [153, 2, 102, 0], [153, 11, 102, 9, "createMemorySafeCapturableConsole"], [153, 44, 102, 42, "createMemorySafeCapturableConsole"], [153, 45, 102, 42], [153, 47, 102, 61], [154, 4, 103, 2], [154, 8, 103, 8, "consoleCopy"], [154, 19, 103, 19], [154, 22, 103, 22, "Object"], [154, 28, 103, 28], [154, 29, 103, 29, "fromEntries"], [154, 40, 103, 40], [154, 41, 104, 4, "Object"], [154, 47, 104, 10], [154, 48, 104, 11, "entries"], [154, 55, 104, 18], [154, 56, 104, 19, "console"], [154, 63, 104, 26], [154, 64, 104, 27], [154, 65, 104, 28, "map"], [154, 68, 104, 31], [154, 69, 104, 32, "_ref"], [154, 73, 104, 32], [154, 77, 104, 58], [155, 6, 104, 58], [155, 10, 104, 58, "_ref2"], [155, 15, 104, 58], [155, 22, 104, 58, "_slicedToArray2"], [155, 37, 104, 58], [155, 38, 104, 58, "default"], [155, 45, 104, 58], [155, 47, 104, 58, "_ref"], [155, 51, 104, 58], [156, 8, 104, 34, "methodName"], [156, 18, 104, 44], [156, 21, 104, 44, "_ref2"], [156, 26, 104, 44], [157, 8, 104, 46, "method"], [157, 14, 104, 52], [157, 17, 104, 52, "_ref2"], [157, 22, 104, 52], [158, 6, 105, 6], [158, 10, 105, 12, "methodWrapper"], [158, 23, 105, 25], [158, 26, 105, 28], [158, 35, 105, 37, "methodWrapper"], [158, 48, 105, 50, "methodWrapper"], [158, 49, 105, 50], [158, 51, 105, 71], [159, 8, 106, 8], [159, 15, 106, 15, "method"], [159, 21, 106, 21], [159, 22, 106, 22], [159, 25, 106, 22, "arguments"], [159, 34, 106, 29], [159, 35, 106, 30], [160, 6, 107, 6], [160, 7, 107, 7], [161, 6, 108, 6], [161, 10, 108, 10, "method"], [161, 16, 108, 16], [161, 17, 108, 17, "name"], [161, 21, 108, 21], [161, 23, 108, 23], [162, 8, 109, 8], [163, 0, 110, 0], [164, 0, 111, 0], [165, 0, 112, 0], [166, 0, 113, 0], [167, 0, 114, 0], [168, 0, 115, 0], [169, 8, 116, 8, "Object"], [169, 14, 116, 14], [169, 15, 116, 15, "defineProperty"], [169, 29, 116, 29], [169, 30, 116, 30, "methodWrapper"], [169, 43, 116, 43], [169, 45, 116, 45], [169, 51, 116, 51], [169, 53, 116, 53], [170, 10, 117, 10, "value"], [170, 15, 117, 15], [170, 17, 117, 17, "method"], [170, 23, 117, 23], [170, 24, 117, 24, "name"], [170, 28, 117, 28], [171, 10, 118, 10, "writable"], [171, 18, 118, 18], [171, 20, 118, 20], [172, 8, 119, 8], [172, 9, 119, 9], [172, 10, 119, 10], [173, 6, 120, 6], [174, 6, 121, 6], [174, 13, 121, 13], [174, 14, 121, 14, "methodName"], [174, 24, 121, 24], [174, 26, 121, 26, "methodWrapper"], [174, 39, 121, 39], [174, 40, 121, 40], [175, 4, 122, 4], [175, 5, 122, 5], [175, 6, 123, 2], [175, 7, 123, 3], [176, 4, 125, 2], [176, 11, 125, 9, "consoleCopy"], [176, 22, 125, 20], [177, 2, 126, 0], [179, 2, 128, 0], [180, 2, 129, 0], [181, 2, 130, 0], [181, 6, 130, 6, "capturableConsole"], [181, 23, 130, 23], [181, 26, 130, 26, "createMemorySafeCapturableConsole"], [181, 59, 130, 59], [181, 60, 130, 60], [181, 61, 130, 61], [182, 2, 130, 62], [182, 6, 130, 62, "_worklet_5303065199952_init_data"], [182, 38, 130, 62], [183, 4, 130, 62, "code"], [183, 8, 130, 62], [184, 4, 130, 62, "location"], [184, 12, 130, 62], [185, 4, 130, 62, "sourceMap"], [185, 13, 130, 62], [186, 4, 130, 62, "version"], [186, 11, 130, 62], [187, 2, 130, 62], [188, 2, 130, 62], [188, 6, 130, 62, "setupConsole"], [188, 18, 130, 62], [188, 21, 130, 62, "exports"], [188, 28, 130, 62], [188, 29, 130, 62, "setupConsole"], [188, 41, 130, 62], [188, 44, 132, 7], [189, 4, 132, 7], [189, 8, 132, 7, "_e"], [189, 10, 132, 7], [189, 18, 132, 7, "global"], [189, 24, 132, 7], [189, 25, 132, 7, "Error"], [189, 30, 132, 7], [190, 4, 132, 7], [190, 8, 132, 7, "setupConsole"], [190, 20, 132, 7], [190, 32, 132, 7, "setupConsole"], [190, 33, 132, 7], [190, 35, 132, 31], [191, 6, 134, 2], [191, 10, 134, 6], [191, 11, 134, 7, "IS_CHROME_DEBUGGER"], [191, 29, 134, 25], [191, 31, 134, 27], [192, 8, 135, 4], [193, 8, 136, 4, "global"], [193, 14, 136, 10], [193, 15, 136, 11, "console"], [193, 22, 136, 18], [193, 25, 136, 21], [194, 10, 137, 6], [195, 10, 138, 6, "assert"], [195, 16, 138, 12], [195, 18, 138, 14], [195, 22, 138, 14, "runOnJS"], [195, 38, 138, 21], [195, 40, 138, 22, "capturableConsole"], [195, 57, 138, 39], [195, 58, 138, 40, "assert"], [195, 64, 138, 46], [195, 65, 138, 47], [196, 10, 139, 6, "debug"], [196, 15, 139, 11], [196, 17, 139, 13], [196, 21, 139, 13, "runOnJS"], [196, 37, 139, 20], [196, 39, 139, 21, "capturableConsole"], [196, 56, 139, 38], [196, 57, 139, 39, "debug"], [196, 62, 139, 44], [196, 63, 139, 45], [197, 10, 140, 6, "log"], [197, 13, 140, 9], [197, 15, 140, 11], [197, 19, 140, 11, "runOnJS"], [197, 35, 140, 18], [197, 37, 140, 19, "capturableConsole"], [197, 54, 140, 36], [197, 55, 140, 37, "log"], [197, 58, 140, 40], [197, 59, 140, 41], [198, 10, 141, 6, "warn"], [198, 14, 141, 10], [198, 16, 141, 12], [198, 20, 141, 12, "runOnJS"], [198, 36, 141, 19], [198, 38, 141, 20, "capturableConsole"], [198, 55, 141, 37], [198, 56, 141, 38, "warn"], [198, 60, 141, 42], [198, 61, 141, 43], [199, 10, 142, 6, "error"], [199, 15, 142, 11], [199, 17, 142, 13], [199, 21, 142, 13, "runOnJS"], [199, 37, 142, 20], [199, 39, 142, 21, "capturableConsole"], [199, 56, 142, 38], [199, 57, 142, 39, "error"], [199, 62, 142, 44], [199, 63, 142, 45], [200, 10, 143, 6, "info"], [200, 14, 143, 10], [200, 16, 143, 12], [200, 20, 143, 12, "runOnJS"], [200, 36, 143, 19], [200, 38, 143, 20, "capturableConsole"], [200, 55, 143, 37], [200, 56, 143, 38, "info"], [200, 60, 143, 42], [201, 10, 144, 6], [202, 8, 145, 4], [202, 9, 145, 5], [203, 6, 146, 2], [204, 4, 147, 0], [204, 5, 147, 1], [205, 4, 147, 1, "setupConsole"], [205, 16, 147, 1], [205, 17, 147, 1, "__closure"], [205, 26, 147, 1], [206, 6, 147, 1, "IS_CHROME_DEBUGGER"], [206, 24, 147, 1], [207, 6, 147, 1, "runOnJS"], [207, 13, 147, 1], [207, 15, 138, 14, "runOnJS"], [207, 31, 138, 21], [208, 6, 138, 21, "capturableConsole"], [209, 4, 138, 21], [210, 4, 138, 21, "setupConsole"], [210, 16, 138, 21], [210, 17, 138, 21, "__workletHash"], [210, 30, 138, 21], [211, 4, 138, 21, "setupConsole"], [211, 16, 138, 21], [211, 17, 138, 21, "__initData"], [211, 27, 138, 21], [211, 30, 138, 21, "_worklet_5303065199952_init_data"], [211, 62, 138, 21], [212, 4, 138, 21, "setupConsole"], [212, 16, 138, 21], [212, 17, 138, 21, "__stackDetails"], [212, 31, 138, 21], [212, 34, 138, 21, "_e"], [212, 36, 138, 21], [213, 4, 138, 21], [213, 11, 138, 21, "setupConsole"], [213, 23, 138, 21], [214, 2, 138, 21], [214, 3, 132, 7], [215, 2, 132, 7], [215, 6, 132, 7, "_worklet_3255663687300_init_data"], [215, 38, 132, 7], [216, 4, 132, 7, "code"], [216, 8, 132, 7], [217, 4, 132, 7, "location"], [217, 12, 132, 7], [218, 4, 132, 7, "sourceMap"], [218, 13, 132, 7], [219, 4, 132, 7, "version"], [219, 11, 132, 7], [220, 2, 132, 7], [221, 2, 132, 7], [221, 6, 132, 7, "setupRequestAnimationFrame"], [221, 32, 132, 7], [221, 35, 149, 0], [222, 4, 149, 0], [222, 8, 149, 0, "_e"], [222, 10, 149, 0], [222, 18, 149, 0, "global"], [222, 24, 149, 0], [222, 25, 149, 0, "Error"], [222, 30, 149, 0], [223, 4, 149, 0], [223, 8, 149, 0, "setupRequestAnimationFrame"], [223, 34, 149, 0], [223, 46, 149, 0, "setupRequestAnimationFrame"], [223, 47, 149, 0], [223, 49, 149, 38], [224, 6, 152, 2], [225, 6, 153, 2], [226, 6, 154, 2], [226, 10, 154, 8, "nativeRequestAnimationFrame"], [226, 37, 154, 35], [226, 40, 154, 38, "global"], [226, 46, 154, 44], [226, 47, 154, 45, "requestAnimationFrame"], [226, 68, 154, 66], [227, 6, 156, 2], [227, 10, 156, 6, "animationFrameCallbacks"], [227, 33, 156, 65], [227, 36, 156, 68], [227, 38, 156, 70], [228, 6, 157, 2], [228, 10, 157, 6, "flushRequested"], [228, 24, 157, 20], [228, 27, 157, 23], [228, 32, 157, 28], [229, 6, 159, 2, "global"], [229, 12, 159, 8], [229, 13, 159, 9, "__flushAnimationFrame"], [229, 34, 159, 30], [229, 37, 159, 34, "frameTimestamp"], [229, 51, 159, 56], [229, 55, 159, 61], [230, 8, 160, 4], [230, 12, 160, 10, "currentCallbacks"], [230, 28, 160, 26], [230, 31, 160, 29, "animationFrameCallbacks"], [230, 54, 160, 52], [231, 8, 161, 4, "animationFrameCallbacks"], [231, 31, 161, 27], [231, 34, 161, 30], [231, 36, 161, 32], [232, 8, 162, 4, "currentCallbacks"], [232, 24, 162, 20], [232, 25, 162, 21, "for<PERSON>ach"], [232, 32, 162, 28], [232, 33, 162, 30, "f"], [232, 34, 162, 31], [232, 38, 162, 36, "f"], [232, 39, 162, 37], [232, 40, 162, 38, "frameTimestamp"], [232, 54, 162, 52], [232, 55, 162, 53], [232, 56, 162, 54], [233, 8, 163, 4], [233, 12, 163, 4, "callMicrotasks"], [233, 35, 163, 18], [233, 37, 163, 19], [233, 38, 163, 20], [234, 6, 164, 2], [234, 7, 164, 3], [235, 6, 166, 2, "global"], [235, 12, 166, 8], [235, 13, 166, 9, "requestAnimationFrame"], [235, 34, 166, 30], [235, 37, 167, 4, "callback"], [235, 45, 167, 41], [235, 49, 168, 15], [236, 8, 169, 4, "animationFrameCallbacks"], [236, 31, 169, 27], [236, 32, 169, 28, "push"], [236, 36, 169, 32], [236, 37, 169, 33, "callback"], [236, 45, 169, 41], [236, 46, 169, 42], [237, 8, 170, 4], [237, 12, 170, 8], [237, 13, 170, 9, "flushRequested"], [237, 27, 170, 23], [237, 29, 170, 25], [238, 10, 171, 6, "flushRequested"], [238, 24, 171, 20], [238, 27, 171, 23], [238, 31, 171, 27], [239, 10, 172, 6, "nativeRequestAnimationFrame"], [239, 37, 172, 33], [239, 38, 172, 35, "timestamp"], [239, 47, 172, 44], [239, 51, 172, 49], [240, 12, 173, 8, "flushRequested"], [240, 26, 173, 22], [240, 29, 173, 25], [240, 34, 173, 30], [241, 12, 174, 8, "global"], [241, 18, 174, 14], [241, 19, 174, 15, "__frameTimestamp"], [241, 35, 174, 31], [241, 38, 174, 34, "timestamp"], [241, 47, 174, 43], [242, 12, 175, 8, "global"], [242, 18, 175, 14], [242, 19, 175, 15, "__flushAnimationFrame"], [242, 40, 175, 36], [242, 41, 175, 37, "timestamp"], [242, 50, 175, 46], [242, 51, 175, 47], [243, 12, 176, 8, "global"], [243, 18, 176, 14], [243, 19, 176, 15, "__frameTimestamp"], [243, 35, 176, 31], [243, 38, 176, 34, "undefined"], [243, 47, 176, 43], [244, 10, 177, 6], [244, 11, 177, 7], [244, 12, 177, 8], [245, 8, 178, 4], [246, 8, 179, 4], [247, 8, 180, 4], [248, 8, 181, 4], [249, 8, 182, 4], [250, 8, 183, 4], [250, 15, 183, 11], [250, 16, 183, 12], [250, 17, 183, 13], [251, 6, 184, 2], [251, 7, 184, 3], [252, 4, 185, 0], [252, 5, 185, 1], [253, 4, 185, 1, "setupRequestAnimationFrame"], [253, 30, 185, 1], [253, 31, 185, 1, "__closure"], [253, 40, 185, 1], [254, 6, 185, 1, "callMicrotasks"], [254, 20, 185, 1], [254, 22, 163, 4, "callMicrotasks"], [255, 4, 163, 18], [256, 4, 163, 18, "setupRequestAnimationFrame"], [256, 30, 163, 18], [256, 31, 163, 18, "__workletHash"], [256, 44, 163, 18], [257, 4, 163, 18, "setupRequestAnimationFrame"], [257, 30, 163, 18], [257, 31, 163, 18, "__initData"], [257, 41, 163, 18], [257, 44, 163, 18, "_worklet_3255663687300_init_data"], [257, 76, 163, 18], [258, 4, 163, 18, "setupRequestAnimationFrame"], [258, 30, 163, 18], [258, 31, 163, 18, "__stackDetails"], [258, 45, 163, 18], [258, 48, 163, 18, "_e"], [258, 50, 163, 18], [259, 4, 163, 18], [259, 11, 163, 18, "setupRequestAnimationFrame"], [259, 37, 163, 18], [260, 2, 163, 18], [260, 3, 149, 0], [261, 2, 149, 0], [261, 6, 149, 0, "_worklet_14702258885038_init_data"], [261, 39, 149, 0], [262, 4, 149, 0, "code"], [262, 8, 149, 0], [263, 4, 149, 0, "location"], [263, 12, 149, 0], [264, 4, 149, 0, "sourceMap"], [264, 13, 149, 0], [265, 4, 149, 0, "version"], [265, 11, 149, 0], [266, 2, 149, 0], [267, 2, 187, 7], [267, 11, 187, 16, "initializeUIRuntime"], [267, 30, 187, 35, "initializeUIRuntime"], [267, 31, 187, 36, "ReanimatedModule"], [267, 47, 187, 71], [267, 49, 187, 73], [268, 4, 188, 2], [268, 8, 188, 6], [268, 12, 188, 6, "isWeb"], [268, 34, 188, 11], [268, 36, 188, 12], [268, 37, 188, 13], [268, 39, 188, 15], [269, 6, 189, 4], [270, 4, 190, 2], [271, 4, 191, 2], [271, 8, 191, 6], [271, 9, 191, 7, "ReanimatedModule"], [271, 25, 191, 23], [271, 27, 191, 25], [272, 6, 192, 4], [273, 6, 193, 4], [273, 12, 193, 10], [273, 16, 193, 14, "Error"], [273, 21, 193, 19], [273, 22, 194, 6], [273, 119, 195, 4], [273, 120, 195, 5], [274, 4, 196, 2], [275, 4, 197, 2], [275, 8, 197, 6, "IS_JEST"], [275, 15, 197, 13], [275, 17, 197, 15], [276, 6, 198, 4], [277, 6, 199, 4], [278, 6, 200, 4], [279, 6, 201, 4], [280, 6, 202, 4], [281, 6, 203, 4], [282, 6, 204, 4, "globalThis"], [282, 16, 204, 14], [282, 17, 204, 15, "requestAnimationFrame"], [282, 38, 204, 36], [282, 41, 204, 39, "mockedRequestAnimationFrame"], [282, 97, 204, 66], [283, 4, 205, 2], [284, 4, 207, 2], [284, 8, 207, 2, "runOnUIImmediately"], [284, 35, 207, 20], [284, 37, 207, 21], [285, 6, 207, 21], [285, 10, 207, 21, "_e"], [285, 12, 207, 21], [285, 20, 207, 21, "global"], [285, 26, 207, 21], [285, 27, 207, 21, "Error"], [285, 32, 207, 21], [286, 6, 207, 21], [286, 10, 207, 21, "initializersTs7"], [286, 25, 207, 21], [286, 37, 207, 21, "initializersTs7"], [286, 38, 207, 21], [286, 40, 207, 27], [287, 8, 209, 4, "setupCallGuard"], [287, 22, 209, 18], [287, 23, 209, 19], [287, 24, 209, 20], [288, 8, 210, 4, "setupConsole"], [288, 20, 210, 16], [288, 21, 210, 17], [288, 22, 210, 18], [289, 8, 211, 4], [289, 12, 211, 8], [289, 13, 211, 9, "SHOULD_BE_USE_WEB"], [289, 30, 211, 26], [289, 32, 211, 28], [290, 10, 212, 6], [290, 14, 212, 6, "setupMicrotasks"], [290, 38, 212, 21], [290, 40, 212, 22], [290, 41, 212, 23], [291, 10, 213, 6, "setupRequestAnimationFrame"], [291, 36, 213, 32], [291, 37, 213, 33], [291, 38, 213, 34], [292, 8, 214, 4], [293, 6, 215, 2], [293, 7, 215, 3], [294, 6, 215, 3, "initializersTs7"], [294, 21, 215, 3], [294, 22, 215, 3, "__closure"], [294, 31, 215, 3], [295, 8, 215, 3, "setupCallGuard"], [295, 22, 215, 3], [296, 8, 215, 3, "setupConsole"], [296, 20, 215, 3], [297, 8, 215, 3, "SHOULD_BE_USE_WEB"], [297, 25, 215, 3], [298, 8, 215, 3, "setupMicrotasks"], [298, 23, 215, 3], [298, 25, 212, 6, "setupMicrotasks"], [298, 49, 212, 21], [299, 8, 212, 21, "setupRequestAnimationFrame"], [300, 6, 212, 21], [301, 6, 212, 21, "initializersTs7"], [301, 21, 212, 21], [301, 22, 212, 21, "__workletHash"], [301, 35, 212, 21], [302, 6, 212, 21, "initializersTs7"], [302, 21, 212, 21], [302, 22, 212, 21, "__initData"], [302, 32, 212, 21], [302, 35, 212, 21, "_worklet_14702258885038_init_data"], [302, 68, 212, 21], [303, 6, 212, 21, "initializersTs7"], [303, 21, 212, 21], [303, 22, 212, 21, "__stackDetails"], [303, 36, 212, 21], [303, 39, 212, 21, "_e"], [303, 41, 212, 21], [304, 6, 212, 21], [304, 13, 212, 21, "initializersTs7"], [304, 28, 212, 21], [305, 4, 212, 21], [305, 5, 207, 21], [305, 7, 215, 3], [305, 8, 215, 4], [305, 9, 215, 5], [305, 10, 215, 6], [306, 2, 216, 0], [307, 0, 216, 1], [307, 3]], "functionMap": {"names": ["<global>", "overrideLogFunctionImplementation", "replaceLoggerImplementation$argument_0", "global._getAnimationTimestamp", "callGuardDEV", "setupCallGuard", "global.__ErrorUtils.reportFatalError", "createMemorySafeCapturableConsole", "Object.entries.map$argument_0", "methodWrapper", "setupConsole", "setupRequestAnimationFrame", "global.__flushAnimationFrame", "currentCallbacks.forEach$argument_0", "global.requestAnimationFrame", "nativeRequestAnimationFrame$argument_0", "initializeUIRuntime", "runOnUIImmediately$argument_0"], "mappings": "AAA;AC+B;8BCE;GDG;CDC;kCGW,uBH;OIW;CJc;OKE;sBCI;KDK;CLE;AOe;gCCE;4BCC;ODE;KDe;CPI;OUM;CVe;AWE;iCCU;6BCG,wBD;GDE;iCGE;kCCM;ODK;GHO;CXC;OgBE;qBCoB;GDQ;ChBC"}}, "type": "js/module"}]}