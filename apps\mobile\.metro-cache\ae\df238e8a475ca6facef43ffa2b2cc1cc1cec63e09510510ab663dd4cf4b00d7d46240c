{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NativeViewGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 86}, "end": {"line": 8, "column": 36, "index": 211}}], "key": "jncJ83usGHTUuB53kaES25nnuNA=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = createNativeWrapper;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var React = _react;\n  var _NativeViewGestureHandler = require(_dependencyMap[1], \"./NativeViewGestureHandler\");\n  var _jsxDevRuntime = require(_dependencyMap[2], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\createNativeWrapper.tsx\";\n  /*\n   * This array should consist of:\n   *   - All keys in propTypes from NativeGestureHandler\n   *     (and all keys in GestureHandlerPropTypes)\n   *   - 'onGestureHandlerEvent'\n   *   - 'onGestureHandlerStateChange'\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var NATIVE_WRAPPER_PROPS_FILTER = [..._NativeViewGestureHandler.nativeViewProps, 'onGestureHandlerEvent', 'onGestureHandlerStateChange'];\n  function createNativeWrapper(Component) {\n    var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var ComponentWrapper = /*#__PURE__*/React.forwardRef((props, ref) => {\n      // Filter out props that should be passed to gesture handler wrapper\n      var _Object$keys$reduce = Object.keys(props).reduce((res, key) => {\n          // TS being overly protective with it's types, see https://github.com/microsoft/TypeScript/issues/26255#issuecomment-458013731 for more info\n          var allowedKeys = NATIVE_WRAPPER_PROPS_FILTER;\n          if (allowedKeys.includes(key)) {\n            // @ts-ignore FIXME(TS)\n            res.gestureHandlerProps[key] = props[key];\n          } else {\n            // @ts-ignore FIXME(TS)\n            res.childProps[key] = props[key];\n          }\n          return res;\n        }, {\n          gestureHandlerProps: {\n            ...config\n          },\n          // Watch out not to modify config\n          childProps: {\n            enabled: props.enabled,\n            hitSlop: props.hitSlop,\n            testID: props.testID\n          }\n        }),\n        gestureHandlerProps = _Object$keys$reduce.gestureHandlerProps,\n        childProps = _Object$keys$reduce.childProps;\n      var _ref = (0, _react.useRef)(null);\n      var _gestureHandlerRef = (0, _react.useRef)(null);\n      (0, _react.useImperativeHandle)(ref,\n      // @ts-ignore TODO(TS) decide how nulls work in this context\n      () => {\n        var node = _gestureHandlerRef.current;\n        // Add handlerTag for relations config\n        if (_ref.current && node) {\n          // @ts-ignore FIXME(TS) think about createHandler return type\n          _ref.current.handlerTag = node.handlerTag;\n          return _ref.current;\n        }\n        return null;\n      }, [_ref, _gestureHandlerRef]);\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_NativeViewGestureHandler.NativeViewGestureHandler, {\n        ...gestureHandlerProps,\n        // @ts-ignore TODO(TS)\n        ref: _gestureHandlerRef,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Component, {\n          ...childProps,\n          ref: _ref\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 7\n      }, this);\n    });\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n    ComponentWrapper.displayName = Component?.displayName ||\n    // @ts-ignore if render doesn't exist it will return undefined and go further\n    Component?.render?.name || typeof Component === 'string' && Component || 'ComponentWrapper';\n    return ComponentWrapper;\n  }\n});", "lineCount": 87, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 31], [7, 6, 1, 31, "React"], [7, 11, 1, 31], [7, 14, 1, 31, "_react"], [7, 20, 1, 31], [8, 2, 4, 0], [8, 6, 4, 0, "_NativeViewGestureHandler"], [8, 31, 4, 0], [8, 34, 4, 0, "require"], [8, 41, 4, 0], [8, 42, 4, 0, "_dependencyMap"], [8, 56, 4, 0], [9, 2, 8, 36], [9, 6, 8, 36, "_jsxDevRuntime"], [9, 20, 8, 36], [9, 23, 8, 36, "require"], [9, 30, 8, 36], [9, 31, 8, 36, "_dependencyMap"], [9, 45, 8, 36], [10, 2, 8, 36], [10, 6, 8, 36, "_jsxFileName"], [10, 18, 8, 36], [11, 2, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [17, 0, 16, 0], [18, 2, 10, 0], [18, 11, 10, 0, "_interopRequireWildcard"], [18, 35, 10, 0, "e"], [18, 36, 10, 0], [18, 38, 10, 0, "t"], [18, 39, 10, 0], [18, 68, 10, 0, "WeakMap"], [18, 75, 10, 0], [18, 81, 10, 0, "r"], [18, 82, 10, 0], [18, 89, 10, 0, "WeakMap"], [18, 96, 10, 0], [18, 100, 10, 0, "n"], [18, 101, 10, 0], [18, 108, 10, 0, "WeakMap"], [18, 115, 10, 0], [18, 127, 10, 0, "_interopRequireWildcard"], [18, 150, 10, 0], [18, 162, 10, 0, "_interopRequireWildcard"], [18, 163, 10, 0, "e"], [18, 164, 10, 0], [18, 166, 10, 0, "t"], [18, 167, 10, 0], [18, 176, 10, 0, "t"], [18, 177, 10, 0], [18, 181, 10, 0, "e"], [18, 182, 10, 0], [18, 186, 10, 0, "e"], [18, 187, 10, 0], [18, 188, 10, 0, "__esModule"], [18, 198, 10, 0], [18, 207, 10, 0, "e"], [18, 208, 10, 0], [18, 214, 10, 0, "o"], [18, 215, 10, 0], [18, 217, 10, 0, "i"], [18, 218, 10, 0], [18, 220, 10, 0, "f"], [18, 221, 10, 0], [18, 226, 10, 0, "__proto__"], [18, 235, 10, 0], [18, 243, 10, 0, "default"], [18, 250, 10, 0], [18, 252, 10, 0, "e"], [18, 253, 10, 0], [18, 270, 10, 0, "e"], [18, 271, 10, 0], [18, 294, 10, 0, "e"], [18, 295, 10, 0], [18, 320, 10, 0, "e"], [18, 321, 10, 0], [18, 330, 10, 0, "f"], [18, 331, 10, 0], [18, 337, 10, 0, "o"], [18, 338, 10, 0], [18, 341, 10, 0, "t"], [18, 342, 10, 0], [18, 345, 10, 0, "n"], [18, 346, 10, 0], [18, 349, 10, 0, "r"], [18, 350, 10, 0], [18, 358, 10, 0, "o"], [18, 359, 10, 0], [18, 360, 10, 0, "has"], [18, 363, 10, 0], [18, 364, 10, 0, "e"], [18, 365, 10, 0], [18, 375, 10, 0, "o"], [18, 376, 10, 0], [18, 377, 10, 0, "get"], [18, 380, 10, 0], [18, 381, 10, 0, "e"], [18, 382, 10, 0], [18, 385, 10, 0, "o"], [18, 386, 10, 0], [18, 387, 10, 0, "set"], [18, 390, 10, 0], [18, 391, 10, 0, "e"], [18, 392, 10, 0], [18, 394, 10, 0, "f"], [18, 395, 10, 0], [18, 409, 10, 0, "_t"], [18, 411, 10, 0], [18, 415, 10, 0, "e"], [18, 416, 10, 0], [18, 432, 10, 0, "_t"], [18, 434, 10, 0], [18, 441, 10, 0, "hasOwnProperty"], [18, 455, 10, 0], [18, 456, 10, 0, "call"], [18, 460, 10, 0], [18, 461, 10, 0, "e"], [18, 462, 10, 0], [18, 464, 10, 0, "_t"], [18, 466, 10, 0], [18, 473, 10, 0, "i"], [18, 474, 10, 0], [18, 478, 10, 0, "o"], [18, 479, 10, 0], [18, 482, 10, 0, "Object"], [18, 488, 10, 0], [18, 489, 10, 0, "defineProperty"], [18, 503, 10, 0], [18, 508, 10, 0, "Object"], [18, 514, 10, 0], [18, 515, 10, 0, "getOwnPropertyDescriptor"], [18, 539, 10, 0], [18, 540, 10, 0, "e"], [18, 541, 10, 0], [18, 543, 10, 0, "_t"], [18, 545, 10, 0], [18, 552, 10, 0, "i"], [18, 553, 10, 0], [18, 554, 10, 0, "get"], [18, 557, 10, 0], [18, 561, 10, 0, "i"], [18, 562, 10, 0], [18, 563, 10, 0, "set"], [18, 566, 10, 0], [18, 570, 10, 0, "o"], [18, 571, 10, 0], [18, 572, 10, 0, "f"], [18, 573, 10, 0], [18, 575, 10, 0, "_t"], [18, 577, 10, 0], [18, 579, 10, 0, "i"], [18, 580, 10, 0], [18, 584, 10, 0, "f"], [18, 585, 10, 0], [18, 586, 10, 0, "_t"], [18, 588, 10, 0], [18, 592, 10, 0, "e"], [18, 593, 10, 0], [18, 594, 10, 0, "_t"], [18, 596, 10, 0], [18, 607, 10, 0, "f"], [18, 608, 10, 0], [18, 613, 10, 0, "e"], [18, 614, 10, 0], [18, 616, 10, 0, "t"], [18, 617, 10, 0], [19, 2, 17, 0], [19, 6, 17, 6, "NATIVE_WRAPPER_PROPS_FILTER"], [19, 33, 17, 33], [19, 36, 17, 36], [19, 37, 18, 2], [19, 40, 18, 5, "nativeViewProps"], [19, 81, 18, 20], [19, 83, 19, 2], [19, 106, 19, 25], [19, 108, 20, 2], [19, 137, 20, 31], [19, 138, 21, 10], [20, 2, 23, 15], [20, 11, 23, 24, "createNativeWrapper"], [20, 30, 23, 43, "createNativeWrapper"], [20, 31, 24, 2, "Component"], [20, 40, 24, 35], [20, 42, 26, 2], [21, 4, 26, 2], [21, 8, 25, 2, "config"], [21, 14, 25, 49], [21, 17, 25, 49, "arguments"], [21, 26, 25, 49], [21, 27, 25, 49, "length"], [21, 33, 25, 49], [21, 41, 25, 49, "arguments"], [21, 50, 25, 49], [21, 58, 25, 49, "undefined"], [21, 67, 25, 49], [21, 70, 25, 49, "arguments"], [21, 79, 25, 49], [21, 85, 25, 52], [21, 86, 25, 53], [21, 87, 25, 54], [22, 4, 27, 2], [22, 8, 27, 8, "ComponentWrapper"], [22, 24, 27, 24], [22, 40, 27, 27, "React"], [22, 45, 27, 32], [22, 46, 27, 33, "forwardRef"], [22, 56, 27, 43], [22, 57, 30, 4], [22, 58, 30, 5, "props"], [22, 63, 30, 10], [22, 65, 30, 12, "ref"], [22, 68, 30, 15], [22, 73, 30, 20], [23, 6, 31, 4], [24, 6, 32, 4], [24, 10, 32, 4, "_Object$keys$reduce"], [24, 29, 32, 4], [24, 32, 32, 48, "Object"], [24, 38, 32, 54], [24, 39, 32, 55, "keys"], [24, 43, 32, 59], [24, 44, 32, 60, "props"], [24, 49, 32, 65], [24, 50, 32, 66], [24, 51, 32, 67, "reduce"], [24, 57, 32, 73], [24, 58, 33, 6], [24, 59, 33, 7, "res"], [24, 62, 33, 10], [24, 64, 33, 12, "key"], [24, 67, 33, 15], [24, 72, 33, 20], [25, 10, 34, 8], [26, 10, 35, 8], [26, 14, 35, 14, "<PERSON><PERSON><PERSON><PERSON>"], [26, 25, 35, 44], [26, 28, 35, 47, "NATIVE_WRAPPER_PROPS_FILTER"], [26, 55, 35, 74], [27, 10, 36, 8], [27, 14, 36, 12, "<PERSON><PERSON><PERSON><PERSON>"], [27, 25, 36, 23], [27, 26, 36, 24, "includes"], [27, 34, 36, 32], [27, 35, 36, 33, "key"], [27, 38, 36, 36], [27, 39, 36, 37], [27, 41, 36, 39], [28, 12, 37, 10], [29, 12, 38, 10, "res"], [29, 15, 38, 13], [29, 16, 38, 14, "gestureHandlerProps"], [29, 35, 38, 33], [29, 36, 38, 34, "key"], [29, 39, 38, 37], [29, 40, 38, 38], [29, 43, 38, 41, "props"], [29, 48, 38, 46], [29, 49, 38, 47, "key"], [29, 52, 38, 50], [29, 53, 38, 51], [30, 10, 39, 8], [30, 11, 39, 9], [30, 17, 39, 15], [31, 12, 40, 10], [32, 12, 41, 10, "res"], [32, 15, 41, 13], [32, 16, 41, 14, "childProps"], [32, 26, 41, 24], [32, 27, 41, 25, "key"], [32, 30, 41, 28], [32, 31, 41, 29], [32, 34, 41, 32, "props"], [32, 39, 41, 37], [32, 40, 41, 38, "key"], [32, 43, 41, 41], [32, 44, 41, 42], [33, 10, 42, 8], [34, 10, 43, 8], [34, 17, 43, 15, "res"], [34, 20, 43, 18], [35, 8, 44, 6], [35, 9, 44, 7], [35, 11, 45, 6], [36, 10, 46, 8, "gestureHandlerProps"], [36, 29, 46, 27], [36, 31, 46, 29], [37, 12, 46, 31], [37, 15, 46, 34, "config"], [38, 10, 46, 41], [38, 11, 46, 42], [39, 10, 46, 44], [40, 10, 47, 8, "childProps"], [40, 20, 47, 18], [40, 22, 47, 20], [41, 12, 48, 10, "enabled"], [41, 19, 48, 17], [41, 21, 48, 19, "props"], [41, 26, 48, 24], [41, 27, 48, 25, "enabled"], [41, 34, 48, 32], [42, 12, 49, 10, "hitSlop"], [42, 19, 49, 17], [42, 21, 49, 19, "props"], [42, 26, 49, 24], [42, 27, 49, 25, "hitSlop"], [42, 34, 49, 32], [43, 12, 50, 10, "testID"], [43, 18, 50, 16], [43, 20, 50, 18, "props"], [43, 25, 50, 23], [43, 26, 50, 24, "testID"], [44, 10, 51, 8], [45, 8, 52, 6], [45, 9, 53, 4], [45, 10, 53, 5], [46, 8, 32, 12, "gestureHandlerProps"], [46, 27, 32, 31], [46, 30, 32, 31, "_Object$keys$reduce"], [46, 49, 32, 31], [46, 50, 32, 12, "gestureHandlerProps"], [46, 69, 32, 31], [47, 8, 32, 33, "childProps"], [47, 18, 32, 43], [47, 21, 32, 43, "_Object$keys$reduce"], [47, 40, 32, 43], [47, 41, 32, 33, "childProps"], [47, 51, 32, 43], [48, 6, 54, 4], [48, 10, 54, 10, "_ref"], [48, 14, 54, 14], [48, 17, 54, 17], [48, 21, 54, 17, "useRef"], [48, 34, 54, 23], [48, 36, 54, 48], [48, 40, 54, 52], [48, 41, 54, 53], [49, 6, 55, 4], [49, 10, 55, 10, "_gestureHandlerRef"], [49, 28, 55, 28], [49, 31, 55, 31], [49, 35, 55, 31, "useRef"], [49, 48, 55, 37], [49, 50, 55, 62], [49, 54, 55, 66], [49, 55, 55, 67], [50, 6, 56, 4], [50, 10, 56, 4, "useImperativeHandle"], [50, 36, 56, 23], [50, 38, 57, 6, "ref"], [50, 41, 57, 9], [51, 6, 58, 6], [52, 6, 59, 6], [52, 12, 59, 12], [53, 8, 60, 8], [53, 12, 60, 14, "node"], [53, 16, 60, 18], [53, 19, 60, 21, "_gestureHandlerRef"], [53, 37, 60, 39], [53, 38, 60, 40, "current"], [53, 45, 60, 47], [54, 8, 61, 8], [55, 8, 62, 8], [55, 12, 62, 12, "_ref"], [55, 16, 62, 16], [55, 17, 62, 17, "current"], [55, 24, 62, 24], [55, 28, 62, 28, "node"], [55, 32, 62, 32], [55, 34, 62, 34], [56, 10, 63, 10], [57, 10, 64, 10, "_ref"], [57, 14, 64, 14], [57, 15, 64, 15, "current"], [57, 22, 64, 22], [57, 23, 64, 23, "handlerTag"], [57, 33, 64, 33], [57, 36, 64, 36, "node"], [57, 40, 64, 40], [57, 41, 64, 41, "handlerTag"], [57, 51, 64, 51], [58, 10, 65, 10], [58, 17, 65, 17, "_ref"], [58, 21, 65, 21], [58, 22, 65, 22, "current"], [58, 29, 65, 29], [59, 8, 66, 8], [60, 8, 67, 8], [60, 15, 67, 15], [60, 19, 67, 19], [61, 6, 68, 6], [61, 7, 68, 7], [61, 9, 69, 6], [61, 10, 69, 7, "_ref"], [61, 14, 69, 11], [61, 16, 69, 13, "_gestureHandlerRef"], [61, 34, 69, 31], [61, 35, 70, 4], [61, 36, 70, 5], [62, 6, 71, 4], [62, 26, 72, 6], [62, 30, 72, 6, "_jsxDevRuntime"], [62, 44, 72, 6], [62, 45, 72, 6, "jsxDEV"], [62, 51, 72, 6], [62, 53, 72, 7, "_NativeViewGestureHandler"], [62, 78, 72, 7], [62, 79, 72, 7, "NativeViewGestureHandler"], [62, 103, 72, 31], [63, 8, 72, 31], [63, 11, 73, 12, "gestureHandlerProps"], [63, 30, 73, 31], [64, 8, 74, 8], [65, 8, 75, 8, "ref"], [65, 11, 75, 11], [65, 13, 75, 13, "_gestureHandlerRef"], [65, 31, 75, 32], [66, 8, 75, 32, "children"], [66, 16, 75, 32], [66, 31, 76, 8], [66, 35, 76, 8, "_jsxDevRuntime"], [66, 49, 76, 8], [66, 50, 76, 8, "jsxDEV"], [66, 56, 76, 8], [66, 58, 76, 9, "Component"], [66, 67, 76, 18], [67, 10, 76, 18], [67, 13, 76, 23, "childProps"], [67, 23, 76, 33], [68, 10, 76, 35, "ref"], [68, 13, 76, 38], [68, 15, 76, 40, "_ref"], [69, 8, 76, 45], [70, 10, 76, 45, "fileName"], [70, 18, 76, 45], [70, 20, 76, 45, "_jsxFileName"], [70, 32, 76, 45], [71, 10, 76, 45, "lineNumber"], [71, 20, 76, 45], [72, 10, 76, 45, "columnNumber"], [72, 22, 76, 45], [73, 8, 76, 45], [73, 15, 76, 47], [74, 6, 76, 48], [75, 8, 76, 48, "fileName"], [75, 16, 76, 48], [75, 18, 76, 48, "_jsxFileName"], [75, 30, 76, 48], [76, 8, 76, 48, "lineNumber"], [76, 18, 76, 48], [77, 8, 76, 48, "columnNumber"], [77, 20, 76, 48], [78, 6, 76, 48], [78, 13, 77, 32], [78, 14, 77, 33], [79, 4, 79, 2], [79, 5, 79, 3], [79, 6, 79, 4], [81, 4, 81, 2], [82, 4, 82, 2, "ComponentWrapper"], [82, 20, 82, 18], [82, 21, 82, 19, "displayName"], [82, 32, 82, 30], [82, 35, 83, 4, "Component"], [82, 44, 83, 13], [82, 46, 83, 15, "displayName"], [82, 57, 83, 26], [83, 4, 84, 4], [84, 4, 85, 4, "Component"], [84, 13, 85, 13], [84, 15, 85, 15, "render"], [84, 21, 85, 21], [84, 23, 85, 23, "name"], [84, 27, 85, 27], [84, 31, 86, 5], [84, 38, 86, 12, "Component"], [84, 47, 86, 21], [84, 52, 86, 26], [84, 60, 86, 34], [84, 64, 86, 38, "Component"], [84, 73, 86, 48], [84, 77, 87, 4], [84, 95, 87, 22], [85, 4, 89, 2], [85, 11, 89, 9, "ComponentWrapper"], [85, 27, 89, 25], [86, 2, 90, 0], [87, 0, 90, 1], [87, 3]], "functionMap": {"names": ["<global>", "createNativeWrapper", "React.forwardRef$argument_0", "Object.keys.reduce$argument_0", "useImperativeHandle$argument_1"], "mappings": "AAA;eCsB;ICO;MCG;ODW;MEe;OFS;GDW;CDW"}}, "type": "js/module"}]}