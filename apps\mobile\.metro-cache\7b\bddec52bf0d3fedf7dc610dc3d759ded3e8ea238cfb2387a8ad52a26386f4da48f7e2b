{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 66, "index": 81}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 82}, "end": {"line": 4, "column": 26, "index": 108}}], "key": "WMoKxUKO/GMHeED0pzSR/dc1v7c=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 109}, "end": {"line": 5, "column": 31, "index": 140}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 141}, "end": {"line": 6, "column": 52, "index": 193}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./PlatformPressable.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 194}, "end": {"line": 7, "column": 59, "index": 253}}], "key": "7Wm8S4t9JyY/16EtBirZwW7XtgQ=", "exportNames": ["*"]}}, {"name": "./Text.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 254}, "end": {"line": 8, "column": 33, "index": 287}}], "key": "QTnFfg9+sbvsvptKfI6RYkeAj2s=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 288}, "end": {"line": 9, "column": 48, "index": 336}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Button = Button;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _native = require(_dependencyMap[2], \"@react-navigation/native\");\n  var _color = _interopRequireDefault(require(_dependencyMap[3], \"color\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[4], \"react\"));\n  var _reactNative = require(_dependencyMap[5], \"react-native\");\n  var _PlatformPressable = require(_dependencyMap[6], \"./PlatformPressable.js\");\n  var _Text = require(_dependencyMap[7], \"./Text.js\");\n  var _jsxRuntime = require(_dependencyMap[8], \"react/jsx-runtime\");\n  var _excluded = [\"screen\", \"params\", \"action\", \"href\"],\n    _excluded2 = [\"variant\", \"color\", \"android_ripple\", \"style\", \"children\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var BUTTON_RADIUS = 40;\n  function Button(props) {\n    if ('screen' in props || 'action' in props) {\n      // @ts-expect-error: This is already type-checked by the prop types\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(ButtonLink, {\n        ...props\n      });\n    } else {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(ButtonBase, {\n        ...props\n      });\n    }\n  }\n  function ButtonLink(_ref) {\n    var screen = _ref.screen,\n      params = _ref.params,\n      action = _ref.action,\n      href = _ref.href,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    // @ts-expect-error: This is already type-checked by the prop types\n    var props = (0, _native.useLinkProps)({\n      screen,\n      params,\n      action,\n      href\n    });\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(ButtonBase, {\n      ...rest,\n      ...props\n    });\n  }\n  function ButtonBase(_ref2) {\n    var _ref2$variant = _ref2.variant,\n      variant = _ref2$variant === void 0 ? 'tinted' : _ref2$variant,\n      customColor = _ref2.color,\n      android_ripple = _ref2.android_ripple,\n      style = _ref2.style,\n      children = _ref2.children,\n      rest = (0, _objectWithoutProperties2.default)(_ref2, _excluded2);\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors,\n      fonts = _useTheme.fonts;\n    var color = customColor ?? colors.primary;\n    var backgroundColor;\n    var textColor;\n    switch (variant) {\n      case 'plain':\n        backgroundColor = 'transparent';\n        textColor = color;\n        break;\n      case 'tinted':\n        backgroundColor = (0, _color.default)(color).fade(0.85).string();\n        textColor = color;\n        break;\n      case 'filled':\n        backgroundColor = color;\n        textColor = (0, _color.default)(color).isDark() ? 'white' : (0, _color.default)(color).darken(0.71).string();\n        break;\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_PlatformPressable.PlatformPressable, {\n      ...rest,\n      android_ripple: {\n        radius: BUTTON_RADIUS,\n        color: (0, _color.default)(textColor).fade(0.85).string(),\n        ...android_ripple\n      },\n      pressOpacity: _reactNative.Platform.OS === 'ios' ? undefined : 1,\n      hoverEffect: {\n        color: textColor\n      },\n      style: [{\n        backgroundColor\n      }, styles.button, style],\n      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_Text.Text, {\n        style: [{\n          color: textColor\n        }, fonts.regular, styles.text],\n        children: children\n      })\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    button: {\n      paddingHorizontal: 24,\n      paddingVertical: 10,\n      borderRadius: BUTTON_RADIUS\n    },\n    text: {\n      fontSize: 14,\n      lineHeight: 20,\n      letterSpacing: 0.1,\n      textAlign: 'center'\n    }\n  });\n});", "lineCount": 114, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "<PERSON><PERSON>"], [8, 16, 1, 13], [8, 19, 1, 13, "<PERSON><PERSON>"], [8, 25, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_native"], [10, 13, 3, 0], [10, 16, 3, 0, "require"], [10, 23, 3, 0], [10, 24, 3, 0, "_dependencyMap"], [10, 38, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_color"], [11, 12, 4, 0], [11, 15, 4, 0, "_interopRequireDefault"], [11, 37, 4, 0], [11, 38, 4, 0, "require"], [11, 45, 4, 0], [11, 46, 4, 0, "_dependencyMap"], [11, 60, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "React"], [12, 11, 5, 0], [12, 14, 5, 0, "_interopRequireWildcard"], [12, 37, 5, 0], [12, 38, 5, 0, "require"], [12, 45, 5, 0], [12, 46, 5, 0, "_dependencyMap"], [12, 60, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_reactNative"], [13, 18, 6, 0], [13, 21, 6, 0, "require"], [13, 28, 6, 0], [13, 29, 6, 0, "_dependencyMap"], [13, 43, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_PlatformPressable"], [14, 24, 7, 0], [14, 27, 7, 0, "require"], [14, 34, 7, 0], [14, 35, 7, 0, "_dependencyMap"], [14, 49, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_Text"], [15, 11, 8, 0], [15, 14, 8, 0, "require"], [15, 21, 8, 0], [15, 22, 8, 0, "_dependencyMap"], [15, 36, 8, 0], [16, 2, 9, 0], [16, 6, 9, 0, "_jsxRuntime"], [16, 17, 9, 0], [16, 20, 9, 0, "require"], [16, 27, 9, 0], [16, 28, 9, 0, "_dependencyMap"], [16, 42, 9, 0], [17, 2, 9, 48], [17, 6, 9, 48, "_excluded"], [17, 15, 9, 48], [18, 4, 9, 48, "_excluded2"], [18, 14, 9, 48], [19, 2, 9, 48], [19, 11, 9, 48, "_interopRequireWildcard"], [19, 35, 9, 48, "e"], [19, 36, 9, 48], [19, 38, 9, 48, "t"], [19, 39, 9, 48], [19, 68, 9, 48, "WeakMap"], [19, 75, 9, 48], [19, 81, 9, 48, "r"], [19, 82, 9, 48], [19, 89, 9, 48, "WeakMap"], [19, 96, 9, 48], [19, 100, 9, 48, "n"], [19, 101, 9, 48], [19, 108, 9, 48, "WeakMap"], [19, 115, 9, 48], [19, 127, 9, 48, "_interopRequireWildcard"], [19, 150, 9, 48], [19, 162, 9, 48, "_interopRequireWildcard"], [19, 163, 9, 48, "e"], [19, 164, 9, 48], [19, 166, 9, 48, "t"], [19, 167, 9, 48], [19, 176, 9, 48, "t"], [19, 177, 9, 48], [19, 181, 9, 48, "e"], [19, 182, 9, 48], [19, 186, 9, 48, "e"], [19, 187, 9, 48], [19, 188, 9, 48, "__esModule"], [19, 198, 9, 48], [19, 207, 9, 48, "e"], [19, 208, 9, 48], [19, 214, 9, 48, "o"], [19, 215, 9, 48], [19, 217, 9, 48, "i"], [19, 218, 9, 48], [19, 220, 9, 48, "f"], [19, 221, 9, 48], [19, 226, 9, 48, "__proto__"], [19, 235, 9, 48], [19, 243, 9, 48, "default"], [19, 250, 9, 48], [19, 252, 9, 48, "e"], [19, 253, 9, 48], [19, 270, 9, 48, "e"], [19, 271, 9, 48], [19, 294, 9, 48, "e"], [19, 295, 9, 48], [19, 320, 9, 48, "e"], [19, 321, 9, 48], [19, 330, 9, 48, "f"], [19, 331, 9, 48], [19, 337, 9, 48, "o"], [19, 338, 9, 48], [19, 341, 9, 48, "t"], [19, 342, 9, 48], [19, 345, 9, 48, "n"], [19, 346, 9, 48], [19, 349, 9, 48, "r"], [19, 350, 9, 48], [19, 358, 9, 48, "o"], [19, 359, 9, 48], [19, 360, 9, 48, "has"], [19, 363, 9, 48], [19, 364, 9, 48, "e"], [19, 365, 9, 48], [19, 375, 9, 48, "o"], [19, 376, 9, 48], [19, 377, 9, 48, "get"], [19, 380, 9, 48], [19, 381, 9, 48, "e"], [19, 382, 9, 48], [19, 385, 9, 48, "o"], [19, 386, 9, 48], [19, 387, 9, 48, "set"], [19, 390, 9, 48], [19, 391, 9, 48, "e"], [19, 392, 9, 48], [19, 394, 9, 48, "f"], [19, 395, 9, 48], [19, 409, 9, 48, "_t"], [19, 411, 9, 48], [19, 415, 9, 48, "e"], [19, 416, 9, 48], [19, 432, 9, 48, "_t"], [19, 434, 9, 48], [19, 441, 9, 48, "hasOwnProperty"], [19, 455, 9, 48], [19, 456, 9, 48, "call"], [19, 460, 9, 48], [19, 461, 9, 48, "e"], [19, 462, 9, 48], [19, 464, 9, 48, "_t"], [19, 466, 9, 48], [19, 473, 9, 48, "i"], [19, 474, 9, 48], [19, 478, 9, 48, "o"], [19, 479, 9, 48], [19, 482, 9, 48, "Object"], [19, 488, 9, 48], [19, 489, 9, 48, "defineProperty"], [19, 503, 9, 48], [19, 508, 9, 48, "Object"], [19, 514, 9, 48], [19, 515, 9, 48, "getOwnPropertyDescriptor"], [19, 539, 9, 48], [19, 540, 9, 48, "e"], [19, 541, 9, 48], [19, 543, 9, 48, "_t"], [19, 545, 9, 48], [19, 552, 9, 48, "i"], [19, 553, 9, 48], [19, 554, 9, 48, "get"], [19, 557, 9, 48], [19, 561, 9, 48, "i"], [19, 562, 9, 48], [19, 563, 9, 48, "set"], [19, 566, 9, 48], [19, 570, 9, 48, "o"], [19, 571, 9, 48], [19, 572, 9, 48, "f"], [19, 573, 9, 48], [19, 575, 9, 48, "_t"], [19, 577, 9, 48], [19, 579, 9, 48, "i"], [19, 580, 9, 48], [19, 584, 9, 48, "f"], [19, 585, 9, 48], [19, 586, 9, 48, "_t"], [19, 588, 9, 48], [19, 592, 9, 48, "e"], [19, 593, 9, 48], [19, 594, 9, 48, "_t"], [19, 596, 9, 48], [19, 607, 9, 48, "f"], [19, 608, 9, 48], [19, 613, 9, 48, "e"], [19, 614, 9, 48], [19, 616, 9, 48, "t"], [19, 617, 9, 48], [20, 2, 10, 0], [20, 6, 10, 6, "BUTTON_RADIUS"], [20, 19, 10, 19], [20, 22, 10, 22], [20, 24, 10, 24], [21, 2, 11, 7], [21, 11, 11, 16, "<PERSON><PERSON>"], [21, 17, 11, 22, "<PERSON><PERSON>"], [21, 18, 11, 23, "props"], [21, 23, 11, 28], [21, 25, 11, 30], [22, 4, 12, 2], [22, 8, 12, 6], [22, 16, 12, 14], [22, 20, 12, 18, "props"], [22, 25, 12, 23], [22, 29, 12, 27], [22, 37, 12, 35], [22, 41, 12, 39, "props"], [22, 46, 12, 44], [22, 48, 12, 46], [23, 6, 13, 4], [24, 6, 14, 4], [24, 13, 14, 11], [24, 26, 14, 24], [24, 30, 14, 24, "_jsx"], [24, 45, 14, 28], [24, 47, 14, 29, "ButtonLink"], [24, 57, 14, 39], [24, 59, 14, 41], [25, 8, 15, 6], [25, 11, 15, 9, "props"], [26, 6, 16, 4], [26, 7, 16, 5], [26, 8, 16, 6], [27, 4, 17, 2], [27, 5, 17, 3], [27, 11, 17, 9], [28, 6, 18, 4], [28, 13, 18, 11], [28, 26, 18, 24], [28, 30, 18, 24, "_jsx"], [28, 45, 18, 28], [28, 47, 18, 29, "ButtonBase"], [28, 57, 18, 39], [28, 59, 18, 41], [29, 8, 19, 6], [29, 11, 19, 9, "props"], [30, 6, 20, 4], [30, 7, 20, 5], [30, 8, 20, 6], [31, 4, 21, 2], [32, 2, 22, 0], [33, 2, 23, 0], [33, 11, 23, 9, "ButtonLink"], [33, 21, 23, 19, "ButtonLink"], [33, 22, 23, 19, "_ref"], [33, 26, 23, 19], [33, 28, 29, 3], [34, 4, 29, 3], [34, 8, 24, 2, "screen"], [34, 14, 24, 8], [34, 17, 24, 8, "_ref"], [34, 21, 24, 8], [34, 22, 24, 2, "screen"], [34, 28, 24, 8], [35, 6, 25, 2, "params"], [35, 12, 25, 8], [35, 15, 25, 8, "_ref"], [35, 19, 25, 8], [35, 20, 25, 2, "params"], [35, 26, 25, 8], [36, 6, 26, 2, "action"], [36, 12, 26, 8], [36, 15, 26, 8, "_ref"], [36, 19, 26, 8], [36, 20, 26, 2, "action"], [36, 26, 26, 8], [37, 6, 27, 2, "href"], [37, 10, 27, 6], [37, 13, 27, 6, "_ref"], [37, 17, 27, 6], [37, 18, 27, 2, "href"], [37, 22, 27, 6], [38, 6, 28, 5, "rest"], [38, 10, 28, 9], [38, 17, 28, 9, "_objectWithoutProperties2"], [38, 42, 28, 9], [38, 43, 28, 9, "default"], [38, 50, 28, 9], [38, 52, 28, 9, "_ref"], [38, 56, 28, 9], [38, 58, 28, 9, "_excluded"], [38, 67, 28, 9], [39, 4, 30, 2], [40, 4, 31, 2], [40, 8, 31, 8, "props"], [40, 13, 31, 13], [40, 16, 31, 16], [40, 20, 31, 16, "useLinkProps"], [40, 40, 31, 28], [40, 42, 31, 29], [41, 6, 32, 4, "screen"], [41, 12, 32, 10], [42, 6, 33, 4, "params"], [42, 12, 33, 10], [43, 6, 34, 4, "action"], [43, 12, 34, 10], [44, 6, 35, 4, "href"], [45, 4, 36, 2], [45, 5, 36, 3], [45, 6, 36, 4], [46, 4, 37, 2], [46, 11, 37, 9], [46, 24, 37, 22], [46, 28, 37, 22, "_jsx"], [46, 43, 37, 26], [46, 45, 37, 27, "ButtonBase"], [46, 55, 37, 37], [46, 57, 37, 39], [47, 6, 38, 4], [47, 9, 38, 7, "rest"], [47, 13, 38, 11], [48, 6, 39, 4], [48, 9, 39, 7, "props"], [49, 4, 40, 2], [49, 5, 40, 3], [49, 6, 40, 4], [50, 2, 41, 0], [51, 2, 42, 0], [51, 11, 42, 9, "ButtonBase"], [51, 21, 42, 19, "ButtonBase"], [51, 22, 42, 19, "_ref2"], [51, 27, 42, 19], [51, 29, 49, 3], [52, 4, 49, 3], [52, 8, 49, 3, "_ref2$variant"], [52, 21, 49, 3], [52, 24, 49, 3, "_ref2"], [52, 29, 49, 3], [52, 30, 43, 2, "variant"], [52, 37, 43, 9], [53, 6, 43, 2, "variant"], [53, 13, 43, 9], [53, 16, 43, 9, "_ref2$variant"], [53, 29, 43, 9], [53, 43, 43, 12], [53, 51, 43, 20], [53, 54, 43, 20, "_ref2$variant"], [53, 67, 43, 20], [54, 6, 44, 9, "customColor"], [54, 17, 44, 20], [54, 20, 44, 20, "_ref2"], [54, 25, 44, 20], [54, 26, 44, 2, "color"], [54, 31, 44, 7], [55, 6, 45, 2, "android_ripple"], [55, 20, 45, 16], [55, 23, 45, 16, "_ref2"], [55, 28, 45, 16], [55, 29, 45, 2, "android_ripple"], [55, 43, 45, 16], [56, 6, 46, 2, "style"], [56, 11, 46, 7], [56, 14, 46, 7, "_ref2"], [56, 19, 46, 7], [56, 20, 46, 2, "style"], [56, 25, 46, 7], [57, 6, 47, 2, "children"], [57, 14, 47, 10], [57, 17, 47, 10, "_ref2"], [57, 22, 47, 10], [57, 23, 47, 2, "children"], [57, 31, 47, 10], [58, 6, 48, 5, "rest"], [58, 10, 48, 9], [58, 17, 48, 9, "_objectWithoutProperties2"], [58, 42, 48, 9], [58, 43, 48, 9, "default"], [58, 50, 48, 9], [58, 52, 48, 9, "_ref2"], [58, 57, 48, 9], [58, 59, 48, 9, "_excluded2"], [58, 69, 48, 9], [59, 4, 50, 2], [59, 8, 50, 2, "_useTheme"], [59, 17, 50, 2], [59, 20, 53, 6], [59, 24, 53, 6, "useTheme"], [59, 40, 53, 14], [59, 42, 53, 15], [59, 43, 53, 16], [60, 6, 51, 4, "colors"], [60, 12, 51, 10], [60, 15, 51, 10, "_useTheme"], [60, 24, 51, 10], [60, 25, 51, 4, "colors"], [60, 31, 51, 10], [61, 6, 52, 4, "fonts"], [61, 11, 52, 9], [61, 14, 52, 9, "_useTheme"], [61, 23, 52, 9], [61, 24, 52, 4, "fonts"], [61, 29, 52, 9], [62, 4, 54, 2], [62, 8, 54, 8, "color"], [62, 13, 54, 13], [62, 16, 54, 16, "customColor"], [62, 27, 54, 27], [62, 31, 54, 31, "colors"], [62, 37, 54, 37], [62, 38, 54, 38, "primary"], [62, 45, 54, 45], [63, 4, 55, 2], [63, 8, 55, 6, "backgroundColor"], [63, 23, 55, 21], [64, 4, 56, 2], [64, 8, 56, 6, "textColor"], [64, 17, 56, 15], [65, 4, 57, 2], [65, 12, 57, 10, "variant"], [65, 19, 57, 17], [66, 6, 58, 4], [66, 11, 58, 9], [66, 18, 58, 16], [67, 8, 59, 6, "backgroundColor"], [67, 23, 59, 21], [67, 26, 59, 24], [67, 39, 59, 37], [68, 8, 60, 6, "textColor"], [68, 17, 60, 15], [68, 20, 60, 18, "color"], [68, 25, 60, 23], [69, 8, 61, 6], [70, 6, 62, 4], [70, 11, 62, 9], [70, 19, 62, 17], [71, 8, 63, 6, "backgroundColor"], [71, 23, 63, 21], [71, 26, 63, 24], [71, 30, 63, 24, "Color"], [71, 44, 63, 29], [71, 46, 63, 30, "color"], [71, 51, 63, 35], [71, 52, 63, 36], [71, 53, 63, 37, "fade"], [71, 57, 63, 41], [71, 58, 63, 42], [71, 62, 63, 46], [71, 63, 63, 47], [71, 64, 63, 48, "string"], [71, 70, 63, 54], [71, 71, 63, 55], [71, 72, 63, 56], [72, 8, 64, 6, "textColor"], [72, 17, 64, 15], [72, 20, 64, 18, "color"], [72, 25, 64, 23], [73, 8, 65, 6], [74, 6, 66, 4], [74, 11, 66, 9], [74, 19, 66, 17], [75, 8, 67, 6, "backgroundColor"], [75, 23, 67, 21], [75, 26, 67, 24, "color"], [75, 31, 67, 29], [76, 8, 68, 6, "textColor"], [76, 17, 68, 15], [76, 20, 68, 18], [76, 24, 68, 18, "Color"], [76, 38, 68, 23], [76, 40, 68, 24, "color"], [76, 45, 68, 29], [76, 46, 68, 30], [76, 47, 68, 31, "isDark"], [76, 53, 68, 37], [76, 54, 68, 38], [76, 55, 68, 39], [76, 58, 68, 42], [76, 65, 68, 49], [76, 68, 68, 52], [76, 72, 68, 52, "Color"], [76, 86, 68, 57], [76, 88, 68, 58, "color"], [76, 93, 68, 63], [76, 94, 68, 64], [76, 95, 68, 65, "darken"], [76, 101, 68, 71], [76, 102, 68, 72], [76, 106, 68, 76], [76, 107, 68, 77], [76, 108, 68, 78, "string"], [76, 114, 68, 84], [76, 115, 68, 85], [76, 116, 68, 86], [77, 8, 69, 6], [78, 4, 70, 2], [79, 4, 71, 2], [79, 11, 71, 9], [79, 24, 71, 22], [79, 28, 71, 22, "_jsx"], [79, 43, 71, 26], [79, 45, 71, 27, "PlatformPressable"], [79, 81, 71, 44], [79, 83, 71, 46], [80, 6, 72, 4], [80, 9, 72, 7, "rest"], [80, 13, 72, 11], [81, 6, 73, 4, "android_ripple"], [81, 20, 73, 18], [81, 22, 73, 20], [82, 8, 74, 6, "radius"], [82, 14, 74, 12], [82, 16, 74, 14, "BUTTON_RADIUS"], [82, 29, 74, 27], [83, 8, 75, 6, "color"], [83, 13, 75, 11], [83, 15, 75, 13], [83, 19, 75, 13, "Color"], [83, 33, 75, 18], [83, 35, 75, 19, "textColor"], [83, 44, 75, 28], [83, 45, 75, 29], [83, 46, 75, 30, "fade"], [83, 50, 75, 34], [83, 51, 75, 35], [83, 55, 75, 39], [83, 56, 75, 40], [83, 57, 75, 41, "string"], [83, 63, 75, 47], [83, 64, 75, 48], [83, 65, 75, 49], [84, 8, 76, 6], [84, 11, 76, 9, "android_ripple"], [85, 6, 77, 4], [85, 7, 77, 5], [86, 6, 78, 4, "pressOpacity"], [86, 18, 78, 16], [86, 20, 78, 18, "Platform"], [86, 41, 78, 26], [86, 42, 78, 27, "OS"], [86, 44, 78, 29], [86, 49, 78, 34], [86, 54, 78, 39], [86, 57, 78, 42, "undefined"], [86, 66, 78, 51], [86, 69, 78, 54], [86, 70, 78, 55], [87, 6, 79, 4, "hoverEffect"], [87, 17, 79, 15], [87, 19, 79, 17], [88, 8, 80, 6, "color"], [88, 13, 80, 11], [88, 15, 80, 13, "textColor"], [89, 6, 81, 4], [89, 7, 81, 5], [90, 6, 82, 4, "style"], [90, 11, 82, 9], [90, 13, 82, 11], [90, 14, 82, 12], [91, 8, 83, 6, "backgroundColor"], [92, 6, 84, 4], [92, 7, 84, 5], [92, 9, 84, 7, "styles"], [92, 15, 84, 13], [92, 16, 84, 14, "button"], [92, 22, 84, 20], [92, 24, 84, 22, "style"], [92, 29, 84, 27], [92, 30, 84, 28], [93, 6, 85, 4, "children"], [93, 14, 85, 12], [93, 16, 85, 14], [93, 29, 85, 27], [93, 33, 85, 27, "_jsx"], [93, 48, 85, 31], [93, 50, 85, 32, "Text"], [93, 60, 85, 36], [93, 62, 85, 38], [94, 8, 86, 6, "style"], [94, 13, 86, 11], [94, 15, 86, 13], [94, 16, 86, 14], [95, 10, 87, 8, "color"], [95, 15, 87, 13], [95, 17, 87, 15, "textColor"], [96, 8, 88, 6], [96, 9, 88, 7], [96, 11, 88, 9, "fonts"], [96, 16, 88, 14], [96, 17, 88, 15, "regular"], [96, 24, 88, 22], [96, 26, 88, 24, "styles"], [96, 32, 88, 30], [96, 33, 88, 31, "text"], [96, 37, 88, 35], [96, 38, 88, 36], [97, 8, 89, 6, "children"], [97, 16, 89, 14], [97, 18, 89, 16, "children"], [98, 6, 90, 4], [98, 7, 90, 5], [99, 4, 91, 2], [99, 5, 91, 3], [99, 6, 91, 4], [100, 2, 92, 0], [101, 2, 93, 0], [101, 6, 93, 6, "styles"], [101, 12, 93, 12], [101, 15, 93, 15, "StyleSheet"], [101, 38, 93, 25], [101, 39, 93, 26, "create"], [101, 45, 93, 32], [101, 46, 93, 33], [102, 4, 94, 2, "button"], [102, 10, 94, 8], [102, 12, 94, 10], [103, 6, 95, 4, "paddingHorizontal"], [103, 23, 95, 21], [103, 25, 95, 23], [103, 27, 95, 25], [104, 6, 96, 4, "paddingVertical"], [104, 21, 96, 19], [104, 23, 96, 21], [104, 25, 96, 23], [105, 6, 97, 4, "borderRadius"], [105, 18, 97, 16], [105, 20, 97, 18, "BUTTON_RADIUS"], [106, 4, 98, 2], [106, 5, 98, 3], [107, 4, 99, 2, "text"], [107, 8, 99, 6], [107, 10, 99, 8], [108, 6, 100, 4, "fontSize"], [108, 14, 100, 12], [108, 16, 100, 14], [108, 18, 100, 16], [109, 6, 101, 4, "lineHeight"], [109, 16, 101, 14], [109, 18, 101, 16], [109, 20, 101, 18], [110, 6, 102, 4, "letterSpacing"], [110, 19, 102, 17], [110, 21, 102, 19], [110, 24, 102, 22], [111, 6, 103, 4, "textAlign"], [111, 15, 103, 13], [111, 17, 103, 15], [112, 4, 104, 2], [113, 2, 105, 0], [113, 3, 105, 1], [113, 4, 105, 2], [114, 0, 105, 3], [114, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON>", "ButtonLink", "ButtonBase"], "mappings": "AAA;OCU;CDW;AEC;CFkB;AGC;CHkD"}}, "type": "js/module"}]}