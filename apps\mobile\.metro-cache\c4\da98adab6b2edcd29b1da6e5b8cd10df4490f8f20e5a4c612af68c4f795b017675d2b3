{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getBackgroundColor = getBackgroundColor;\n  exports.getBackgroundDarkColor = getBackgroundDarkColor;\n  exports.getBackgroundLightColor = getBackgroundLightColor;\n  exports.getDividerColor = getDividerColor;\n  exports.getErrorColor = getErrorColor;\n  exports.getErrorDarkColor = getErrorDarkColor;\n  exports.getFatalColor = getFatalColor;\n  exports.getFatalDarkColor = getFatalDarkColor;\n  exports.getHighlightColor = getHighlightColor;\n  exports.getLogColor = getLogColor;\n  exports.getTextColor = getTextColor;\n  exports.getWarningColor = getWarningColor;\n  exports.getWarningDarkColor = getWarningDarkColor;\n  exports.getWarningHighlightColor = getWarningHighlightColor;\n  function getBackgroundColor(opacity) {\n    return `rgba(51, 51, 51, ${opacity == null ? 1 : opacity})`;\n  }\n  function getBackgroundLightColor(opacity) {\n    return `rgba(69, 69, 69, ${opacity == null ? 1 : opacity})`;\n  }\n  function getBackgroundDarkColor(opacity) {\n    return `rgba(34, 34, 34, ${opacity == null ? 1 : opacity})`;\n  }\n  function getWarningColor(opacity) {\n    return `rgba(250, 186, 48, ${opacity == null ? 1 : opacity})`;\n  }\n  function getWarningDarkColor(opacity) {\n    return `rgba(224, 167, 8, ${opacity == null ? 1 : opacity})`;\n  }\n  function getFatalColor(opacity) {\n    return `rgba(243, 83, 105, ${opacity == null ? 1 : opacity})`;\n  }\n  function getFatalDarkColor(opacity) {\n    return `rgba(208, 75, 95, ${opacity == null ? 1 : opacity})`;\n  }\n  function getErrorColor(opacity) {\n    return `rgba(243, 83, 105, ${opacity == null ? 1 : opacity})`;\n  }\n  function getErrorDarkColor(opacity) {\n    return `rgba(208, 75, 95, ${opacity == null ? 1 : opacity})`;\n  }\n  function getLogColor(opacity) {\n    return `rgba(119, 119, 119, ${opacity == null ? 1 : opacity})`;\n  }\n  function getWarningHighlightColor(opacity) {\n    return `rgba(252, 176, 29, ${opacity == null ? 1 : opacity})`;\n  }\n  function getDividerColor(opacity) {\n    return `rgba(255, 255, 255, ${opacity == null ? 1 : opacity})`;\n  }\n  function getHighlightColor(opacity) {\n    return `rgba(252, 176, 29, ${opacity == null ? 1 : opacity})`;\n  }\n  function getTextColor(opacity) {\n    return `rgba(255, 255, 255, ${opacity == null ? 1 : opacity})`;\n  }\n});", "lineCount": 61, "map": [[19, 2, 11, 7], [19, 11, 11, 16, "getBackgroundColor"], [19, 29, 11, 34, "getBackgroundColor"], [19, 30, 11, 35, "opacity"], [19, 37, 11, 51], [19, 39, 11, 61], [20, 4, 12, 2], [20, 11, 12, 9], [20, 31, 12, 29, "opacity"], [20, 38, 12, 36], [20, 42, 12, 40], [20, 46, 12, 44], [20, 49, 12, 47], [20, 50, 12, 48], [20, 53, 12, 51, "opacity"], [20, 60, 12, 58], [20, 63, 12, 61], [21, 2, 13, 0], [22, 2, 15, 7], [22, 11, 15, 16, "getBackgroundLightColor"], [22, 34, 15, 39, "getBackgroundLightColor"], [22, 35, 15, 40, "opacity"], [22, 42, 15, 56], [22, 44, 15, 66], [23, 4, 16, 2], [23, 11, 16, 9], [23, 31, 16, 29, "opacity"], [23, 38, 16, 36], [23, 42, 16, 40], [23, 46, 16, 44], [23, 49, 16, 47], [23, 50, 16, 48], [23, 53, 16, 51, "opacity"], [23, 60, 16, 58], [23, 63, 16, 61], [24, 2, 17, 0], [25, 2, 19, 7], [25, 11, 19, 16, "getBackgroundDarkColor"], [25, 33, 19, 38, "getBackgroundDarkColor"], [25, 34, 19, 39, "opacity"], [25, 41, 19, 55], [25, 43, 19, 65], [26, 4, 20, 2], [26, 11, 20, 9], [26, 31, 20, 29, "opacity"], [26, 38, 20, 36], [26, 42, 20, 40], [26, 46, 20, 44], [26, 49, 20, 47], [26, 50, 20, 48], [26, 53, 20, 51, "opacity"], [26, 60, 20, 58], [26, 63, 20, 61], [27, 2, 21, 0], [28, 2, 23, 7], [28, 11, 23, 16, "getWarningColor"], [28, 26, 23, 31, "getWarningColor"], [28, 27, 23, 32, "opacity"], [28, 34, 23, 48], [28, 36, 23, 58], [29, 4, 24, 2], [29, 11, 24, 9], [29, 33, 24, 31, "opacity"], [29, 40, 24, 38], [29, 44, 24, 42], [29, 48, 24, 46], [29, 51, 24, 49], [29, 52, 24, 50], [29, 55, 24, 53, "opacity"], [29, 62, 24, 60], [29, 65, 24, 63], [30, 2, 25, 0], [31, 2, 27, 7], [31, 11, 27, 16, "getWarningDarkColor"], [31, 30, 27, 35, "getWarningDarkColor"], [31, 31, 27, 36, "opacity"], [31, 38, 27, 52], [31, 40, 27, 62], [32, 4, 28, 2], [32, 11, 28, 9], [32, 32, 28, 30, "opacity"], [32, 39, 28, 37], [32, 43, 28, 41], [32, 47, 28, 45], [32, 50, 28, 48], [32, 51, 28, 49], [32, 54, 28, 52, "opacity"], [32, 61, 28, 59], [32, 64, 28, 62], [33, 2, 29, 0], [34, 2, 31, 7], [34, 11, 31, 16, "getFatalColor"], [34, 24, 31, 29, "getFatalColor"], [34, 25, 31, 30, "opacity"], [34, 32, 31, 46], [34, 34, 31, 56], [35, 4, 32, 2], [35, 11, 32, 9], [35, 33, 32, 31, "opacity"], [35, 40, 32, 38], [35, 44, 32, 42], [35, 48, 32, 46], [35, 51, 32, 49], [35, 52, 32, 50], [35, 55, 32, 53, "opacity"], [35, 62, 32, 60], [35, 65, 32, 63], [36, 2, 33, 0], [37, 2, 35, 7], [37, 11, 35, 16, "getFatalDarkColor"], [37, 28, 35, 33, "getFatalDarkColor"], [37, 29, 35, 34, "opacity"], [37, 36, 35, 50], [37, 38, 35, 60], [38, 4, 36, 2], [38, 11, 36, 9], [38, 32, 36, 30, "opacity"], [38, 39, 36, 37], [38, 43, 36, 41], [38, 47, 36, 45], [38, 50, 36, 48], [38, 51, 36, 49], [38, 54, 36, 52, "opacity"], [38, 61, 36, 59], [38, 64, 36, 62], [39, 2, 37, 0], [40, 2, 39, 7], [40, 11, 39, 16, "getErrorColor"], [40, 24, 39, 29, "getErrorColor"], [40, 25, 39, 30, "opacity"], [40, 32, 39, 46], [40, 34, 39, 56], [41, 4, 40, 2], [41, 11, 40, 9], [41, 33, 40, 31, "opacity"], [41, 40, 40, 38], [41, 44, 40, 42], [41, 48, 40, 46], [41, 51, 40, 49], [41, 52, 40, 50], [41, 55, 40, 53, "opacity"], [41, 62, 40, 60], [41, 65, 40, 63], [42, 2, 41, 0], [43, 2, 43, 7], [43, 11, 43, 16, "getErrorDarkColor"], [43, 28, 43, 33, "getErrorDarkColor"], [43, 29, 43, 34, "opacity"], [43, 36, 43, 50], [43, 38, 43, 60], [44, 4, 44, 2], [44, 11, 44, 9], [44, 32, 44, 30, "opacity"], [44, 39, 44, 37], [44, 43, 44, 41], [44, 47, 44, 45], [44, 50, 44, 48], [44, 51, 44, 49], [44, 54, 44, 52, "opacity"], [44, 61, 44, 59], [44, 64, 44, 62], [45, 2, 45, 0], [46, 2, 47, 7], [46, 11, 47, 16, "getLogColor"], [46, 22, 47, 27, "getLogColor"], [46, 23, 47, 28, "opacity"], [46, 30, 47, 44], [46, 32, 47, 54], [47, 4, 48, 2], [47, 11, 48, 9], [47, 34, 48, 32, "opacity"], [47, 41, 48, 39], [47, 45, 48, 43], [47, 49, 48, 47], [47, 52, 48, 50], [47, 53, 48, 51], [47, 56, 48, 54, "opacity"], [47, 63, 48, 61], [47, 66, 48, 64], [48, 2, 49, 0], [49, 2, 51, 7], [49, 11, 51, 16, "getWarningHighlightColor"], [49, 35, 51, 40, "getWarningHighlightColor"], [49, 36, 51, 41, "opacity"], [49, 43, 51, 57], [49, 45, 51, 67], [50, 4, 52, 2], [50, 11, 52, 9], [50, 33, 52, 31, "opacity"], [50, 40, 52, 38], [50, 44, 52, 42], [50, 48, 52, 46], [50, 51, 52, 49], [50, 52, 52, 50], [50, 55, 52, 53, "opacity"], [50, 62, 52, 60], [50, 65, 52, 63], [51, 2, 53, 0], [52, 2, 55, 7], [52, 11, 55, 16, "getDividerColor"], [52, 26, 55, 31, "getDividerColor"], [52, 27, 55, 32, "opacity"], [52, 34, 55, 48], [52, 36, 55, 58], [53, 4, 56, 2], [53, 11, 56, 9], [53, 34, 56, 32, "opacity"], [53, 41, 56, 39], [53, 45, 56, 43], [53, 49, 56, 47], [53, 52, 56, 50], [53, 53, 56, 51], [53, 56, 56, 54, "opacity"], [53, 63, 56, 61], [53, 66, 56, 64], [54, 2, 57, 0], [55, 2, 59, 7], [55, 11, 59, 16, "getHighlightColor"], [55, 28, 59, 33, "getHighlightColor"], [55, 29, 59, 34, "opacity"], [55, 36, 59, 50], [55, 38, 59, 60], [56, 4, 60, 2], [56, 11, 60, 9], [56, 33, 60, 31, "opacity"], [56, 40, 60, 38], [56, 44, 60, 42], [56, 48, 60, 46], [56, 51, 60, 49], [56, 52, 60, 50], [56, 55, 60, 53, "opacity"], [56, 62, 60, 60], [56, 65, 60, 63], [57, 2, 61, 0], [58, 2, 63, 7], [58, 11, 63, 16, "getTextColor"], [58, 23, 63, 28, "getTextColor"], [58, 24, 63, 29, "opacity"], [58, 31, 63, 45], [58, 33, 63, 55], [59, 4, 64, 2], [59, 11, 64, 9], [59, 34, 64, 32, "opacity"], [59, 41, 64, 39], [59, 45, 64, 43], [59, 49, 64, 47], [59, 52, 64, 50], [59, 53, 64, 51], [59, 56, 64, 54, "opacity"], [59, 63, 64, 61], [59, 66, 64, 64], [60, 2, 65, 0], [61, 0, 65, 1], [61, 3]], "functionMap": {"names": ["<global>", "getBackgroundColor", "getBackgroundLightColor", "getBackgroundDarkColor", "getWarningColor", "getWarningDarkColor", "getFatalColor", "getFatalDarkColor", "getErrorColor", "getErrorDarkColor", "getLogColor", "getWarningHighlightColor", "getDividerColor", "getHighlightColor", "getTextColor"], "mappings": "AAA;OCU;CDE;OEE;CFE;OGE;CHE;OIE;CJE;OKE;CLE;OME;CNE;OOE;CPE;OQE;CRE;OSE;CTE;OUE;CVE;OWE;CXE;OYE;CZE;OaE;CbE;OcE"}}, "type": "js/module"}]}