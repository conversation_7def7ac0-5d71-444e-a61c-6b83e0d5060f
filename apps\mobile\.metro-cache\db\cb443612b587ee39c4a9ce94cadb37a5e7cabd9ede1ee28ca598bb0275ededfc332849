{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getInvertedMultiplier = getInvertedMultiplier;\n  function getInvertedMultiplier(gestureDirection, isRTL) {\n    switch (gestureDirection) {\n      case 'vertical':\n        return 1;\n      case 'vertical-inverted':\n        return -1;\n      case 'horizontal':\n        return isRTL ? -1 : 1;\n      case 'horizontal-inverted':\n        return isRTL ? 1 : -1;\n    }\n  }\n});", "lineCount": 20, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getInvertedMultiplier"], [7, 31, 1, 13], [7, 34, 1, 13, "getInvertedMultiplier"], [7, 55, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "getInvertedMultiplier"], [8, 32, 3, 37, "getInvertedMultiplier"], [8, 33, 3, 38, "gestureDirection"], [8, 49, 3, 54], [8, 51, 3, 56, "isRTL"], [8, 56, 3, 61], [8, 58, 3, 63], [9, 4, 4, 2], [9, 12, 4, 10, "gestureDirection"], [9, 28, 4, 26], [10, 6, 5, 4], [10, 11, 5, 9], [10, 21, 5, 19], [11, 8, 6, 6], [11, 15, 6, 13], [11, 16, 6, 14], [12, 6, 7, 4], [12, 11, 7, 9], [12, 30, 7, 28], [13, 8, 8, 6], [13, 15, 8, 13], [13, 16, 8, 14], [13, 17, 8, 15], [14, 6, 9, 4], [14, 11, 9, 9], [14, 23, 9, 21], [15, 8, 10, 6], [15, 15, 10, 13, "isRTL"], [15, 20, 10, 18], [15, 23, 10, 21], [15, 24, 10, 22], [15, 25, 10, 23], [15, 28, 10, 26], [15, 29, 10, 27], [16, 6, 11, 4], [16, 11, 11, 9], [16, 32, 11, 30], [17, 8, 12, 6], [17, 15, 12, 13, "isRTL"], [17, 20, 12, 18], [17, 23, 12, 21], [17, 24, 12, 22], [17, 27, 12, 25], [17, 28, 12, 26], [17, 29, 12, 27], [18, 4, 13, 2], [19, 2, 14, 0], [20, 0, 14, 1], [20, 3]], "functionMap": {"names": ["<global>", "getInvertedMultiplier"], "mappings": "AAA;OCE;CDW"}}, "type": "js/module"}]}