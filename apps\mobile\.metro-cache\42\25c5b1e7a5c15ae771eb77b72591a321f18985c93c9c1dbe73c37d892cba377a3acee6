{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useChildListeners = useChildListeners;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Hook which lets child navigators add action listeners.\n   */\n  function useChildListeners() {\n    var _React$useRef = React.useRef({\n        action: [],\n        focus: []\n      }),\n      listeners = _React$useRef.current;\n    var addListener = React.useCallback((type, listener) => {\n      listeners[type].push(listener);\n      var removed = false;\n      return () => {\n        var index = listeners[type].indexOf(listener);\n        if (!removed && index > -1) {\n          removed = true;\n          listeners[type].splice(index, 1);\n        }\n      };\n    }, [listeners]);\n    return {\n      listeners,\n      addListener\n    };\n  }\n});", "lineCount": 35, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useChildListeners"], [7, 27, 1, 13], [7, 30, 1, 13, "useChildListeners"], [7, 47, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 3, 31], [9, 11, 3, 31, "_interopRequireWildcard"], [9, 35, 3, 31, "e"], [9, 36, 3, 31], [9, 38, 3, 31, "t"], [9, 39, 3, 31], [9, 68, 3, 31, "WeakMap"], [9, 75, 3, 31], [9, 81, 3, 31, "r"], [9, 82, 3, 31], [9, 89, 3, 31, "WeakMap"], [9, 96, 3, 31], [9, 100, 3, 31, "n"], [9, 101, 3, 31], [9, 108, 3, 31, "WeakMap"], [9, 115, 3, 31], [9, 127, 3, 31, "_interopRequireWildcard"], [9, 150, 3, 31], [9, 162, 3, 31, "_interopRequireWildcard"], [9, 163, 3, 31, "e"], [9, 164, 3, 31], [9, 166, 3, 31, "t"], [9, 167, 3, 31], [9, 176, 3, 31, "t"], [9, 177, 3, 31], [9, 181, 3, 31, "e"], [9, 182, 3, 31], [9, 186, 3, 31, "e"], [9, 187, 3, 31], [9, 188, 3, 31, "__esModule"], [9, 198, 3, 31], [9, 207, 3, 31, "e"], [9, 208, 3, 31], [9, 214, 3, 31, "o"], [9, 215, 3, 31], [9, 217, 3, 31, "i"], [9, 218, 3, 31], [9, 220, 3, 31, "f"], [9, 221, 3, 31], [9, 226, 3, 31, "__proto__"], [9, 235, 3, 31], [9, 243, 3, 31, "default"], [9, 250, 3, 31], [9, 252, 3, 31, "e"], [9, 253, 3, 31], [9, 270, 3, 31, "e"], [9, 271, 3, 31], [9, 294, 3, 31, "e"], [9, 295, 3, 31], [9, 320, 3, 31, "e"], [9, 321, 3, 31], [9, 330, 3, 31, "f"], [9, 331, 3, 31], [9, 337, 3, 31, "o"], [9, 338, 3, 31], [9, 341, 3, 31, "t"], [9, 342, 3, 31], [9, 345, 3, 31, "n"], [9, 346, 3, 31], [9, 349, 3, 31, "r"], [9, 350, 3, 31], [9, 358, 3, 31, "o"], [9, 359, 3, 31], [9, 360, 3, 31, "has"], [9, 363, 3, 31], [9, 364, 3, 31, "e"], [9, 365, 3, 31], [9, 375, 3, 31, "o"], [9, 376, 3, 31], [9, 377, 3, 31, "get"], [9, 380, 3, 31], [9, 381, 3, 31, "e"], [9, 382, 3, 31], [9, 385, 3, 31, "o"], [9, 386, 3, 31], [9, 387, 3, 31, "set"], [9, 390, 3, 31], [9, 391, 3, 31, "e"], [9, 392, 3, 31], [9, 394, 3, 31, "f"], [9, 395, 3, 31], [9, 409, 3, 31, "_t"], [9, 411, 3, 31], [9, 415, 3, 31, "e"], [9, 416, 3, 31], [9, 432, 3, 31, "_t"], [9, 434, 3, 31], [9, 441, 3, 31, "hasOwnProperty"], [9, 455, 3, 31], [9, 456, 3, 31, "call"], [9, 460, 3, 31], [9, 461, 3, 31, "e"], [9, 462, 3, 31], [9, 464, 3, 31, "_t"], [9, 466, 3, 31], [9, 473, 3, 31, "i"], [9, 474, 3, 31], [9, 478, 3, 31, "o"], [9, 479, 3, 31], [9, 482, 3, 31, "Object"], [9, 488, 3, 31], [9, 489, 3, 31, "defineProperty"], [9, 503, 3, 31], [9, 508, 3, 31, "Object"], [9, 514, 3, 31], [9, 515, 3, 31, "getOwnPropertyDescriptor"], [9, 539, 3, 31], [9, 540, 3, 31, "e"], [9, 541, 3, 31], [9, 543, 3, 31, "_t"], [9, 545, 3, 31], [9, 552, 3, 31, "i"], [9, 553, 3, 31], [9, 554, 3, 31, "get"], [9, 557, 3, 31], [9, 561, 3, 31, "i"], [9, 562, 3, 31], [9, 563, 3, 31, "set"], [9, 566, 3, 31], [9, 570, 3, 31, "o"], [9, 571, 3, 31], [9, 572, 3, 31, "f"], [9, 573, 3, 31], [9, 575, 3, 31, "_t"], [9, 577, 3, 31], [9, 579, 3, 31, "i"], [9, 580, 3, 31], [9, 584, 3, 31, "f"], [9, 585, 3, 31], [9, 586, 3, 31, "_t"], [9, 588, 3, 31], [9, 592, 3, 31, "e"], [9, 593, 3, 31], [9, 594, 3, 31, "_t"], [9, 596, 3, 31], [9, 607, 3, 31, "f"], [9, 608, 3, 31], [9, 613, 3, 31, "e"], [9, 614, 3, 31], [9, 616, 3, 31, "t"], [9, 617, 3, 31], [10, 2, 4, 0], [11, 0, 5, 0], [12, 0, 6, 0], [13, 2, 7, 7], [13, 11, 7, 16, "useChildListeners"], [13, 28, 7, 33, "useChildListeners"], [13, 29, 7, 33], [13, 31, 7, 36], [14, 4, 8, 2], [14, 8, 8, 2, "_React$useRef"], [14, 21, 8, 2], [14, 24, 10, 6, "React"], [14, 29, 10, 11], [14, 30, 10, 12, "useRef"], [14, 36, 10, 18], [14, 37, 10, 19], [15, 8, 11, 4, "action"], [15, 14, 11, 10], [15, 16, 11, 12], [15, 18, 11, 14], [16, 8, 12, 4, "focus"], [16, 13, 12, 9], [16, 15, 12, 11], [17, 6, 13, 2], [17, 7, 13, 3], [17, 8, 13, 4], [18, 6, 9, 13, "listeners"], [18, 15, 9, 22], [18, 18, 9, 22, "_React$useRef"], [18, 31, 9, 22], [18, 32, 9, 4, "current"], [18, 39, 9, 11], [19, 4, 14, 2], [19, 8, 14, 8, "addListener"], [19, 19, 14, 19], [19, 22, 14, 22, "React"], [19, 27, 14, 27], [19, 28, 14, 28, "useCallback"], [19, 39, 14, 39], [19, 40, 14, 40], [19, 41, 14, 41, "type"], [19, 45, 14, 45], [19, 47, 14, 47, "listener"], [19, 55, 14, 55], [19, 60, 14, 60], [20, 6, 15, 4, "listeners"], [20, 15, 15, 13], [20, 16, 15, 14, "type"], [20, 20, 15, 18], [20, 21, 15, 19], [20, 22, 15, 20, "push"], [20, 26, 15, 24], [20, 27, 15, 25, "listener"], [20, 35, 15, 33], [20, 36, 15, 34], [21, 6, 16, 4], [21, 10, 16, 8, "removed"], [21, 17, 16, 15], [21, 20, 16, 18], [21, 25, 16, 23], [22, 6, 17, 4], [22, 13, 17, 11], [22, 19, 17, 17], [23, 8, 18, 6], [23, 12, 18, 12, "index"], [23, 17, 18, 17], [23, 20, 18, 20, "listeners"], [23, 29, 18, 29], [23, 30, 18, 30, "type"], [23, 34, 18, 34], [23, 35, 18, 35], [23, 36, 18, 36, "indexOf"], [23, 43, 18, 43], [23, 44, 18, 44, "listener"], [23, 52, 18, 52], [23, 53, 18, 53], [24, 8, 19, 6], [24, 12, 19, 10], [24, 13, 19, 11, "removed"], [24, 20, 19, 18], [24, 24, 19, 22, "index"], [24, 29, 19, 27], [24, 32, 19, 30], [24, 33, 19, 31], [24, 34, 19, 32], [24, 36, 19, 34], [25, 10, 20, 8, "removed"], [25, 17, 20, 15], [25, 20, 20, 18], [25, 24, 20, 22], [26, 10, 21, 8, "listeners"], [26, 19, 21, 17], [26, 20, 21, 18, "type"], [26, 24, 21, 22], [26, 25, 21, 23], [26, 26, 21, 24, "splice"], [26, 32, 21, 30], [26, 33, 21, 31, "index"], [26, 38, 21, 36], [26, 40, 21, 38], [26, 41, 21, 39], [26, 42, 21, 40], [27, 8, 22, 6], [28, 6, 23, 4], [28, 7, 23, 5], [29, 4, 24, 2], [29, 5, 24, 3], [29, 7, 24, 5], [29, 8, 24, 6, "listeners"], [29, 17, 24, 15], [29, 18, 24, 16], [29, 19, 24, 17], [30, 4, 25, 2], [30, 11, 25, 9], [31, 6, 26, 4, "listeners"], [31, 15, 26, 13], [32, 6, 27, 4, "addListener"], [33, 4, 28, 2], [33, 5, 28, 3], [34, 2, 29, 0], [35, 0, 29, 1], [35, 3]], "functionMap": {"names": ["<global>", "useChildListeners", "addListener", "<anonymous>"], "mappings": "AAA;OCM;wCCO;WCG;KDM;GDC;CDK"}}, "type": "js/module"}]}