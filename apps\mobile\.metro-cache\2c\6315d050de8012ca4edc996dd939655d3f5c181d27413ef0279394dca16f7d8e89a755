{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  /** Imported from react-native */\n\n  /* eslint-disable */\n  /**\n   * This is a helper function for when a component needs to be able to forward a\n   * ref to a child component, but still needs to have access to that component as\n   * part of its implementation.\n   *\n   * Its main use case is in wrappers for native components.\n   *\n   * Usage:\n   *\n   * Class MyView extends React.Component { _nativeRef = null;\n   *\n   *     _setNativeRef = setAndForwardRef({\n   *       getForwardedRef: () => this.props.forwardedRef,\n   *       setLocalRef: ref => {\n   *         this._nativeRef = ref;\n   *       },\n   *     });\n   *\n   *     render() {\n   *       return <View ref={this._setNativeRef} />;\n   *     }\n   *\n   * }\n   *\n   * Const MyViewWithRef = React.forwardRef((props, ref) => ( <MyView {...props}\n   * forwardedRef={ref} /> ));\n   *\n   * Module.exports = MyViewWithRef;\n   */\n  /* eslint-enable */\n\n  function setAndForwardRef(_ref) {\n    var getForwardedRef = _ref.getForwardedRef,\n      setLocalRef = _ref.setLocalRef;\n    return function forwardRef(ref) {\n      var forwardedRef = getForwardedRef();\n      setLocalRef(ref);\n\n      // Forward to user ref prop (if one has been specified)\n      if (typeof forwardedRef === 'function') {\n        // Handle function-based refs. String-based refs are handled as functions.\n        forwardedRef(ref);\n      } else if (typeof forwardedRef === 'object' && forwardedRef != null) {\n        // Handle createRef-based refs\n        forwardedRef.current = ref;\n      }\n    };\n  }\n  var _default = exports.default = setAndForwardRef;\n});", "lineCount": 60, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "default"], [7, 17, 1, 13], [8, 2, 5, 0], [10, 2, 7, 0], [11, 2, 8, 0], [12, 0, 9, 0], [13, 0, 10, 0], [14, 0, 11, 0], [15, 0, 12, 0], [16, 0, 13, 0], [17, 0, 14, 0], [18, 0, 15, 0], [19, 0, 16, 0], [20, 0, 17, 0], [21, 0, 18, 0], [22, 0, 19, 0], [23, 0, 20, 0], [24, 0, 21, 0], [25, 0, 22, 0], [26, 0, 23, 0], [27, 0, 24, 0], [28, 0, 25, 0], [29, 0, 26, 0], [30, 0, 27, 0], [31, 0, 28, 0], [32, 0, 29, 0], [33, 0, 30, 0], [34, 0, 31, 0], [35, 0, 32, 0], [36, 0, 33, 0], [37, 0, 34, 0], [38, 0, 35, 0], [39, 0, 36, 0], [40, 2, 37, 0], [42, 2, 39, 0], [42, 11, 39, 9, "setAndForwardRef"], [42, 27, 39, 25, "setAndForwardRef"], [42, 28, 39, 25, "_ref"], [42, 32, 39, 25], [42, 34, 45, 21], [43, 4, 45, 21], [43, 8, 40, 2, "getForwardedRef"], [43, 23, 40, 17], [43, 26, 40, 17, "_ref"], [43, 30, 40, 17], [43, 31, 40, 2, "getForwardedRef"], [43, 46, 40, 17], [44, 6, 41, 2, "setLocalRef"], [44, 17, 41, 13], [44, 20, 41, 13, "_ref"], [44, 24, 41, 13], [44, 25, 41, 2, "setLocalRef"], [44, 36, 41, 13], [45, 4, 46, 2], [45, 11, 46, 9], [45, 20, 46, 18, "forwardRef"], [45, 30, 46, 28, "forwardRef"], [45, 31, 46, 29, "ref"], [45, 34, 46, 35], [45, 36, 46, 37], [46, 6, 47, 4], [46, 10, 47, 10, "forwardedRef"], [46, 22, 47, 22], [46, 25, 47, 25, "getForwardedRef"], [46, 40, 47, 40], [46, 41, 47, 41], [46, 42, 47, 42], [47, 6, 49, 4, "setLocalRef"], [47, 17, 49, 15], [47, 18, 49, 16, "ref"], [47, 21, 49, 19], [47, 22, 49, 20], [49, 6, 51, 4], [50, 6, 52, 4], [50, 10, 52, 8], [50, 17, 52, 15, "forwardedRef"], [50, 29, 52, 27], [50, 34, 52, 32], [50, 44, 52, 42], [50, 46, 52, 44], [51, 8, 53, 6], [52, 8, 54, 6, "forwardedRef"], [52, 20, 54, 18], [52, 21, 54, 19, "ref"], [52, 24, 54, 22], [52, 25, 54, 23], [53, 6, 55, 4], [53, 7, 55, 5], [53, 13, 55, 11], [53, 17, 55, 15], [53, 24, 55, 22, "forwardedRef"], [53, 36, 55, 34], [53, 41, 55, 39], [53, 49, 55, 47], [53, 53, 55, 51, "forwardedRef"], [53, 65, 55, 63], [53, 69, 55, 67], [53, 73, 55, 71], [53, 75, 55, 73], [54, 8, 56, 6], [55, 8, 57, 6, "forwardedRef"], [55, 20, 57, 18], [55, 21, 57, 19, "current"], [55, 28, 57, 26], [55, 31, 57, 29, "ref"], [55, 34, 57, 32], [56, 6, 58, 4], [57, 4, 59, 2], [57, 5, 59, 3], [58, 2, 60, 0], [59, 2, 60, 1], [59, 6, 60, 1, "_default"], [59, 14, 60, 1], [59, 17, 60, 1, "exports"], [59, 24, 60, 1], [59, 25, 60, 1, "default"], [59, 32, 60, 1], [59, 35, 62, 15, "setAndForwardRef"], [59, 51, 62, 31], [60, 0, 62, 31], [60, 3]], "functionMap": {"names": ["<global>", "setAndForwardRef", "forwardRef"], "mappings": "AAA;ACsC;SCO;GDa;CDC"}}, "type": "js/module"}]}