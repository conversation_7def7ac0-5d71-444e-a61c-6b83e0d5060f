{"dependencies": [{"name": "../../../Libraries/ReactNative/I18nManager", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 55}}], "key": "vnq6dXMh9SIUGptiQDzgjAU4WHM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var I18nManager = require(_dependencyMap[0], \"../../../Libraries/ReactNative/I18nManager\").default;\n  function resolveBoxStyle(prefix, style) {\n    var hasParts = false;\n    var result = {\n      bottom: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n    var styleForAll = style[prefix];\n    if (styleForAll != null) {\n      for (var key of Object.keys(result)) {\n        result[key] = styleForAll;\n      }\n      hasParts = true;\n    }\n    var styleForHorizontal = style[prefix + 'Horizontal'];\n    if (styleForHorizontal != null) {\n      result.left = styleForHorizontal;\n      result.right = styleForHorizontal;\n      hasParts = true;\n    } else {\n      var styleForLeft = style[prefix + 'Left'];\n      if (styleForLeft != null) {\n        result.left = styleForLeft;\n        hasParts = true;\n      }\n      var styleForRight = style[prefix + 'Right'];\n      if (styleForRight != null) {\n        result.right = styleForRight;\n        hasParts = true;\n      }\n      var styleForEnd = style[prefix + 'End'];\n      if (styleForEnd != null) {\n        var constants = I18nManager.getConstants();\n        if (constants.isRTL && constants.doLeftAndRightSwapInRTL) {\n          result.left = styleForEnd;\n        } else {\n          result.right = styleForEnd;\n        }\n        hasParts = true;\n      }\n      var styleForStart = style[prefix + 'Start'];\n      if (styleForStart != null) {\n        var _constants = I18nManager.getConstants();\n        if (_constants.isRTL && _constants.doLeftAndRightSwapInRTL) {\n          result.right = styleForStart;\n        } else {\n          result.left = styleForStart;\n        }\n        hasParts = true;\n      }\n    }\n    var styleForVertical = style[prefix + 'Vertical'];\n    if (styleForVertical != null) {\n      result.bottom = styleForVertical;\n      result.top = styleForVertical;\n      hasParts = true;\n    } else {\n      var styleForBottom = style[prefix + 'Bottom'];\n      if (styleForBottom != null) {\n        result.bottom = styleForBottom;\n        hasParts = true;\n      }\n      var styleForTop = style[prefix + 'Top'];\n      if (styleForTop != null) {\n        result.top = styleForTop;\n        hasParts = true;\n      }\n    }\n    return hasParts ? result : null;\n  }\n  var _default = exports.default = resolveBoxStyle;\n});", "lineCount": 81, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 13, 0], [8, 6, 13, 6, "I18nManager"], [8, 17, 13, 17], [8, 20, 14, 2, "require"], [8, 27, 14, 9], [8, 28, 14, 9, "_dependencyMap"], [8, 42, 14, 9], [8, 91, 14, 54], [8, 92, 14, 55], [8, 93, 14, 56, "default"], [8, 100, 14, 63], [9, 2, 26, 0], [9, 11, 26, 9, "resolveBoxStyle"], [9, 26, 26, 24, "resolveBoxStyle"], [9, 27, 27, 2, "prefix"], [9, 33, 27, 16], [9, 35, 28, 2, "style"], [9, 40, 28, 15], [9, 42, 34, 3], [10, 4, 35, 2], [10, 8, 35, 6, "hasP<PERSON>s"], [10, 16, 35, 14], [10, 19, 35, 17], [10, 24, 35, 22], [11, 4, 36, 2], [11, 8, 36, 8, "result"], [11, 14, 36, 14], [11, 17, 36, 17], [12, 6, 37, 4, "bottom"], [12, 12, 37, 10], [12, 14, 37, 12], [12, 15, 37, 13], [13, 6, 38, 4, "left"], [13, 10, 38, 8], [13, 12, 38, 10], [13, 13, 38, 11], [14, 6, 39, 4, "right"], [14, 11, 39, 9], [14, 13, 39, 11], [14, 14, 39, 12], [15, 6, 40, 4, "top"], [15, 9, 40, 7], [15, 11, 40, 9], [16, 4, 41, 2], [16, 5, 41, 3], [17, 4, 45, 2], [17, 8, 45, 8, "styleForAll"], [17, 19, 45, 19], [17, 22, 45, 22, "style"], [17, 27, 45, 27], [17, 28, 45, 28, "prefix"], [17, 34, 45, 34], [17, 35, 45, 35], [18, 4, 46, 2], [18, 8, 46, 6, "styleForAll"], [18, 19, 46, 17], [18, 23, 46, 21], [18, 27, 46, 25], [18, 29, 46, 27], [19, 6, 47, 4], [19, 11, 47, 9], [19, 15, 47, 15, "key"], [19, 18, 47, 18], [19, 22, 47, 22, "Object"], [19, 28, 47, 28], [19, 29, 47, 29, "keys"], [19, 33, 47, 33], [19, 34, 47, 34, "result"], [19, 40, 47, 40], [19, 41, 47, 41], [19, 43, 47, 43], [20, 8, 48, 6, "result"], [20, 14, 48, 12], [20, 15, 48, 13, "key"], [20, 18, 48, 16], [20, 19, 48, 17], [20, 22, 48, 20, "styleForAll"], [20, 33, 48, 31], [21, 6, 49, 4], [22, 6, 50, 4, "hasP<PERSON>s"], [22, 14, 50, 12], [22, 17, 50, 15], [22, 21, 50, 19], [23, 4, 51, 2], [24, 4, 53, 2], [24, 8, 53, 8, "styleForHorizontal"], [24, 26, 53, 26], [24, 29, 53, 29, "style"], [24, 34, 53, 34], [24, 35, 53, 35, "prefix"], [24, 41, 53, 41], [24, 44, 53, 44], [24, 56, 53, 56], [24, 57, 53, 57], [25, 4, 54, 2], [25, 8, 54, 6, "styleForHorizontal"], [25, 26, 54, 24], [25, 30, 54, 28], [25, 34, 54, 32], [25, 36, 54, 34], [26, 6, 55, 4, "result"], [26, 12, 55, 10], [26, 13, 55, 11, "left"], [26, 17, 55, 15], [26, 20, 55, 18, "styleForHorizontal"], [26, 38, 55, 36], [27, 6, 56, 4, "result"], [27, 12, 56, 10], [27, 13, 56, 11, "right"], [27, 18, 56, 16], [27, 21, 56, 19, "styleForHorizontal"], [27, 39, 56, 37], [28, 6, 57, 4, "hasP<PERSON>s"], [28, 14, 57, 12], [28, 17, 57, 15], [28, 21, 57, 19], [29, 4, 58, 2], [29, 5, 58, 3], [29, 11, 58, 9], [30, 6, 59, 4], [30, 10, 59, 10, "styleForLeft"], [30, 22, 59, 22], [30, 25, 59, 25, "style"], [30, 30, 59, 30], [30, 31, 59, 31, "prefix"], [30, 37, 59, 37], [30, 40, 59, 40], [30, 46, 59, 46], [30, 47, 59, 47], [31, 6, 60, 4], [31, 10, 60, 8, "styleForLeft"], [31, 22, 60, 20], [31, 26, 60, 24], [31, 30, 60, 28], [31, 32, 60, 30], [32, 8, 61, 6, "result"], [32, 14, 61, 12], [32, 15, 61, 13, "left"], [32, 19, 61, 17], [32, 22, 61, 20, "styleForLeft"], [32, 34, 61, 32], [33, 8, 62, 6, "hasP<PERSON>s"], [33, 16, 62, 14], [33, 19, 62, 17], [33, 23, 62, 21], [34, 6, 63, 4], [35, 6, 65, 4], [35, 10, 65, 10, "styleForRight"], [35, 23, 65, 23], [35, 26, 65, 26, "style"], [35, 31, 65, 31], [35, 32, 65, 32, "prefix"], [35, 38, 65, 38], [35, 41, 65, 41], [35, 48, 65, 48], [35, 49, 65, 49], [36, 6, 66, 4], [36, 10, 66, 8, "styleForRight"], [36, 23, 66, 21], [36, 27, 66, 25], [36, 31, 66, 29], [36, 33, 66, 31], [37, 8, 67, 6, "result"], [37, 14, 67, 12], [37, 15, 67, 13, "right"], [37, 20, 67, 18], [37, 23, 67, 21, "styleForRight"], [37, 36, 67, 34], [38, 8, 68, 6, "hasP<PERSON>s"], [38, 16, 68, 14], [38, 19, 68, 17], [38, 23, 68, 21], [39, 6, 69, 4], [40, 6, 71, 4], [40, 10, 71, 10, "styleForEnd"], [40, 21, 71, 21], [40, 24, 71, 24, "style"], [40, 29, 71, 29], [40, 30, 71, 30, "prefix"], [40, 36, 71, 36], [40, 39, 71, 39], [40, 44, 71, 44], [40, 45, 71, 45], [41, 6, 72, 4], [41, 10, 72, 8, "styleForEnd"], [41, 21, 72, 19], [41, 25, 72, 23], [41, 29, 72, 27], [41, 31, 72, 29], [42, 8, 73, 6], [42, 12, 73, 12, "constants"], [42, 21, 73, 21], [42, 24, 73, 24, "I18nManager"], [42, 35, 73, 35], [42, 36, 73, 36, "getConstants"], [42, 48, 73, 48], [42, 49, 73, 49], [42, 50, 73, 50], [43, 8, 74, 6], [43, 12, 74, 10, "constants"], [43, 21, 74, 19], [43, 22, 74, 20, "isRTL"], [43, 27, 74, 25], [43, 31, 74, 29, "constants"], [43, 40, 74, 38], [43, 41, 74, 39, "doLeftAndRightSwapInRTL"], [43, 64, 74, 62], [43, 66, 74, 64], [44, 10, 75, 8, "result"], [44, 16, 75, 14], [44, 17, 75, 15, "left"], [44, 21, 75, 19], [44, 24, 75, 22, "styleForEnd"], [44, 35, 75, 33], [45, 8, 76, 6], [45, 9, 76, 7], [45, 15, 76, 13], [46, 10, 77, 8, "result"], [46, 16, 77, 14], [46, 17, 77, 15, "right"], [46, 22, 77, 20], [46, 25, 77, 23, "styleForEnd"], [46, 36, 77, 34], [47, 8, 78, 6], [48, 8, 79, 6, "hasP<PERSON>s"], [48, 16, 79, 14], [48, 19, 79, 17], [48, 23, 79, 21], [49, 6, 80, 4], [50, 6, 81, 4], [50, 10, 81, 10, "styleForStart"], [50, 23, 81, 23], [50, 26, 81, 26, "style"], [50, 31, 81, 31], [50, 32, 81, 32, "prefix"], [50, 38, 81, 38], [50, 41, 81, 41], [50, 48, 81, 48], [50, 49, 81, 49], [51, 6, 82, 4], [51, 10, 82, 8, "styleForStart"], [51, 23, 82, 21], [51, 27, 82, 25], [51, 31, 82, 29], [51, 33, 82, 31], [52, 8, 83, 6], [52, 12, 83, 12, "constants"], [52, 22, 83, 21], [52, 25, 83, 24, "I18nManager"], [52, 36, 83, 35], [52, 37, 83, 36, "getConstants"], [52, 49, 83, 48], [52, 50, 83, 49], [52, 51, 83, 50], [53, 8, 84, 6], [53, 12, 84, 10, "constants"], [53, 22, 84, 19], [53, 23, 84, 20, "isRTL"], [53, 28, 84, 25], [53, 32, 84, 29, "constants"], [53, 42, 84, 38], [53, 43, 84, 39, "doLeftAndRightSwapInRTL"], [53, 66, 84, 62], [53, 68, 84, 64], [54, 10, 85, 8, "result"], [54, 16, 85, 14], [54, 17, 85, 15, "right"], [54, 22, 85, 20], [54, 25, 85, 23, "styleForStart"], [54, 38, 85, 36], [55, 8, 86, 6], [55, 9, 86, 7], [55, 15, 86, 13], [56, 10, 87, 8, "result"], [56, 16, 87, 14], [56, 17, 87, 15, "left"], [56, 21, 87, 19], [56, 24, 87, 22, "styleForStart"], [56, 37, 87, 35], [57, 8, 88, 6], [58, 8, 89, 6, "hasP<PERSON>s"], [58, 16, 89, 14], [58, 19, 89, 17], [58, 23, 89, 21], [59, 6, 90, 4], [60, 4, 91, 2], [61, 4, 93, 2], [61, 8, 93, 8, "styleForVertical"], [61, 24, 93, 24], [61, 27, 93, 27, "style"], [61, 32, 93, 32], [61, 33, 93, 33, "prefix"], [61, 39, 93, 39], [61, 42, 93, 42], [61, 52, 93, 52], [61, 53, 93, 53], [62, 4, 94, 2], [62, 8, 94, 6, "styleForVertical"], [62, 24, 94, 22], [62, 28, 94, 26], [62, 32, 94, 30], [62, 34, 94, 32], [63, 6, 95, 4, "result"], [63, 12, 95, 10], [63, 13, 95, 11, "bottom"], [63, 19, 95, 17], [63, 22, 95, 20, "styleForVertical"], [63, 38, 95, 36], [64, 6, 96, 4, "result"], [64, 12, 96, 10], [64, 13, 96, 11, "top"], [64, 16, 96, 14], [64, 19, 96, 17, "styleForVertical"], [64, 35, 96, 33], [65, 6, 97, 4, "hasP<PERSON>s"], [65, 14, 97, 12], [65, 17, 97, 15], [65, 21, 97, 19], [66, 4, 98, 2], [66, 5, 98, 3], [66, 11, 98, 9], [67, 6, 99, 4], [67, 10, 99, 10, "styleForBottom"], [67, 24, 99, 24], [67, 27, 99, 27, "style"], [67, 32, 99, 32], [67, 33, 99, 33, "prefix"], [67, 39, 99, 39], [67, 42, 99, 42], [67, 50, 99, 50], [67, 51, 99, 51], [68, 6, 100, 4], [68, 10, 100, 8, "styleForBottom"], [68, 24, 100, 22], [68, 28, 100, 26], [68, 32, 100, 30], [68, 34, 100, 32], [69, 8, 101, 6, "result"], [69, 14, 101, 12], [69, 15, 101, 13, "bottom"], [69, 21, 101, 19], [69, 24, 101, 22, "styleForBottom"], [69, 38, 101, 36], [70, 8, 102, 6, "hasP<PERSON>s"], [70, 16, 102, 14], [70, 19, 102, 17], [70, 23, 102, 21], [71, 6, 103, 4], [72, 6, 105, 4], [72, 10, 105, 10, "styleForTop"], [72, 21, 105, 21], [72, 24, 105, 24, "style"], [72, 29, 105, 29], [72, 30, 105, 30, "prefix"], [72, 36, 105, 36], [72, 39, 105, 39], [72, 44, 105, 44], [72, 45, 105, 45], [73, 6, 106, 4], [73, 10, 106, 8, "styleForTop"], [73, 21, 106, 19], [73, 25, 106, 23], [73, 29, 106, 27], [73, 31, 106, 29], [74, 8, 107, 6, "result"], [74, 14, 107, 12], [74, 15, 107, 13, "top"], [74, 18, 107, 16], [74, 21, 107, 19, "styleForTop"], [74, 32, 107, 30], [75, 8, 108, 6, "hasP<PERSON>s"], [75, 16, 108, 14], [75, 19, 108, 17], [75, 23, 108, 21], [76, 6, 109, 4], [77, 4, 110, 2], [78, 4, 112, 2], [78, 11, 112, 9, "hasP<PERSON>s"], [78, 19, 112, 17], [78, 22, 112, 20, "result"], [78, 28, 112, 26], [78, 31, 112, 29], [78, 35, 112, 33], [79, 2, 113, 0], [80, 2, 113, 1], [80, 6, 113, 1, "_default"], [80, 14, 113, 1], [80, 17, 113, 1, "exports"], [80, 24, 113, 1], [80, 25, 113, 1, "default"], [80, 32, 113, 1], [80, 35, 115, 15, "resolveBoxStyle"], [80, 50, 115, 30], [81, 0, 115, 30], [81, 3]], "functionMap": {"names": ["<global>", "resolveBoxStyle"], "mappings": "AAA;ACyB;CDuF"}}, "type": "js/module"}]}