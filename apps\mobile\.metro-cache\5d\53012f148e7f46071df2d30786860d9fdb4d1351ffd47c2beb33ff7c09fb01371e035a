{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 137}, "end": {"line": 8, "column": 42, "index": 179}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 181}, "end": {"line": 10, "column": 46, "index": 227}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.componentWithRef = componentWithRef;\n  exports.isFirstReactRender = isFirstReactRender;\n  exports.isReactRendering = isReactRendering;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _PlatformChecker = require(_dependencyMap[3], \"./PlatformChecker\");\n  var _excluded = [\"ref\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var IS_REACT_19 = (0, _PlatformChecker.isReact19)();\n  function getCurrentReactOwner() {\n    return (\n      // @ts-expect-error React secret internals aren't typed\n      _react.default.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE?.A?.getOwner() ||\n      // @ts-expect-error React secret internals aren't typed\n      _react.default.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current ||\n      // @ts-expect-error React secret internals aren't typed\n      _react.default.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE?.ReactCurrentOwner?.current\n    );\n  }\n  function isReactRendering() {\n    return !!getCurrentReactOwner();\n  }\n  function isFirstReactRender() {\n    var currentOwner = getCurrentReactOwner();\n    // alternate is not null only after the first render and stores all the\n    // data from the previous component render\n    return currentOwner && !currentOwner?.alternate;\n  }\n\n  // This is an adjusted version of https://github.com/adobe/react-spectrum/issues/7494#issuecomment-2546940052\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  function componentWithRef(render) {\n    if (IS_REACT_19) {\n      return _ref => {\n        var ref = _ref.ref,\n          props = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n        return render(props, ref);\n      };\n    }\n    return /*#__PURE__*/(0, _react.forwardRef)(render);\n  }\n});", "lineCount": 49, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "componentWithRef"], [8, 26, 1, 13], [8, 29, 1, 13, "componentWithRef"], [8, 45, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "isFirstReactRender"], [9, 28, 1, 13], [9, 31, 1, 13, "isFirstReactRender"], [9, 49, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "isReactRendering"], [10, 26, 1, 13], [10, 29, 1, 13, "isReactRendering"], [10, 45, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_objectWithoutProperties2"], [11, 31, 1, 13], [11, 34, 1, 13, "_interopRequireDefault"], [11, 56, 1, 13], [11, 57, 1, 13, "require"], [11, 64, 1, 13], [11, 65, 1, 13, "_dependencyMap"], [11, 79, 1, 13], [12, 2, 8, 0], [12, 6, 8, 0, "_react"], [12, 12, 8, 0], [12, 15, 8, 0, "_interopRequireWildcard"], [12, 38, 8, 0], [12, 39, 8, 0, "require"], [12, 46, 8, 0], [12, 47, 8, 0, "_dependencyMap"], [12, 61, 8, 0], [13, 2, 10, 0], [13, 6, 10, 0, "_PlatformChecker"], [13, 22, 10, 0], [13, 25, 10, 0, "require"], [13, 32, 10, 0], [13, 33, 10, 0, "_dependencyMap"], [13, 47, 10, 0], [14, 2, 10, 46], [14, 6, 10, 46, "_excluded"], [14, 15, 10, 46], [15, 2, 10, 46], [15, 11, 10, 46, "_interopRequireWildcard"], [15, 35, 10, 46, "e"], [15, 36, 10, 46], [15, 38, 10, 46, "t"], [15, 39, 10, 46], [15, 68, 10, 46, "WeakMap"], [15, 75, 10, 46], [15, 81, 10, 46, "r"], [15, 82, 10, 46], [15, 89, 10, 46, "WeakMap"], [15, 96, 10, 46], [15, 100, 10, 46, "n"], [15, 101, 10, 46], [15, 108, 10, 46, "WeakMap"], [15, 115, 10, 46], [15, 127, 10, 46, "_interopRequireWildcard"], [15, 150, 10, 46], [15, 162, 10, 46, "_interopRequireWildcard"], [15, 163, 10, 46, "e"], [15, 164, 10, 46], [15, 166, 10, 46, "t"], [15, 167, 10, 46], [15, 176, 10, 46, "t"], [15, 177, 10, 46], [15, 181, 10, 46, "e"], [15, 182, 10, 46], [15, 186, 10, 46, "e"], [15, 187, 10, 46], [15, 188, 10, 46, "__esModule"], [15, 198, 10, 46], [15, 207, 10, 46, "e"], [15, 208, 10, 46], [15, 214, 10, 46, "o"], [15, 215, 10, 46], [15, 217, 10, 46, "i"], [15, 218, 10, 46], [15, 220, 10, 46, "f"], [15, 221, 10, 46], [15, 226, 10, 46, "__proto__"], [15, 235, 10, 46], [15, 243, 10, 46, "default"], [15, 250, 10, 46], [15, 252, 10, 46, "e"], [15, 253, 10, 46], [15, 270, 10, 46, "e"], [15, 271, 10, 46], [15, 294, 10, 46, "e"], [15, 295, 10, 46], [15, 320, 10, 46, "e"], [15, 321, 10, 46], [15, 330, 10, 46, "f"], [15, 331, 10, 46], [15, 337, 10, 46, "o"], [15, 338, 10, 46], [15, 341, 10, 46, "t"], [15, 342, 10, 46], [15, 345, 10, 46, "n"], [15, 346, 10, 46], [15, 349, 10, 46, "r"], [15, 350, 10, 46], [15, 358, 10, 46, "o"], [15, 359, 10, 46], [15, 360, 10, 46, "has"], [15, 363, 10, 46], [15, 364, 10, 46, "e"], [15, 365, 10, 46], [15, 375, 10, 46, "o"], [15, 376, 10, 46], [15, 377, 10, 46, "get"], [15, 380, 10, 46], [15, 381, 10, 46, "e"], [15, 382, 10, 46], [15, 385, 10, 46, "o"], [15, 386, 10, 46], [15, 387, 10, 46, "set"], [15, 390, 10, 46], [15, 391, 10, 46, "e"], [15, 392, 10, 46], [15, 394, 10, 46, "f"], [15, 395, 10, 46], [15, 409, 10, 46, "_t"], [15, 411, 10, 46], [15, 415, 10, 46, "e"], [15, 416, 10, 46], [15, 432, 10, 46, "_t"], [15, 434, 10, 46], [15, 441, 10, 46, "hasOwnProperty"], [15, 455, 10, 46], [15, 456, 10, 46, "call"], [15, 460, 10, 46], [15, 461, 10, 46, "e"], [15, 462, 10, 46], [15, 464, 10, 46, "_t"], [15, 466, 10, 46], [15, 473, 10, 46, "i"], [15, 474, 10, 46], [15, 478, 10, 46, "o"], [15, 479, 10, 46], [15, 482, 10, 46, "Object"], [15, 488, 10, 46], [15, 489, 10, 46, "defineProperty"], [15, 503, 10, 46], [15, 508, 10, 46, "Object"], [15, 514, 10, 46], [15, 515, 10, 46, "getOwnPropertyDescriptor"], [15, 539, 10, 46], [15, 540, 10, 46, "e"], [15, 541, 10, 46], [15, 543, 10, 46, "_t"], [15, 545, 10, 46], [15, 552, 10, 46, "i"], [15, 553, 10, 46], [15, 554, 10, 46, "get"], [15, 557, 10, 46], [15, 561, 10, 46, "i"], [15, 562, 10, 46], [15, 563, 10, 46, "set"], [15, 566, 10, 46], [15, 570, 10, 46, "o"], [15, 571, 10, 46], [15, 572, 10, 46, "f"], [15, 573, 10, 46], [15, 575, 10, 46, "_t"], [15, 577, 10, 46], [15, 579, 10, 46, "i"], [15, 580, 10, 46], [15, 584, 10, 46, "f"], [15, 585, 10, 46], [15, 586, 10, 46, "_t"], [15, 588, 10, 46], [15, 592, 10, 46, "e"], [15, 593, 10, 46], [15, 594, 10, 46, "_t"], [15, 596, 10, 46], [15, 607, 10, 46, "f"], [15, 608, 10, 46], [15, 613, 10, 46, "e"], [15, 614, 10, 46], [15, 616, 10, 46, "t"], [15, 617, 10, 46], [16, 2, 12, 0], [16, 6, 12, 6, "IS_REACT_19"], [16, 17, 12, 17], [16, 20, 12, 20], [16, 24, 12, 20, "isReact19"], [16, 50, 12, 29], [16, 52, 12, 30], [16, 53, 12, 31], [17, 2, 14, 0], [17, 11, 14, 9, "getCurrentReactOwner"], [17, 31, 14, 29, "getCurrentReactOwner"], [17, 32, 14, 29], [17, 34, 14, 32], [18, 4, 15, 2], [19, 6, 16, 4], [20, 6, 17, 4, "React"], [20, 20, 17, 9], [20, 21, 17, 10, "__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE"], [20, 84, 17, 73], [20, 86, 17, 75, "A"], [20, 87, 17, 76], [20, 89, 17, 78, "get<PERSON>wner"], [20, 97, 17, 86], [20, 98, 17, 87], [20, 99, 17, 88], [21, 6, 18, 4], [22, 6, 19, 4, "React"], [22, 20, 19, 9], [22, 21, 19, 10, "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED"], [22, 71, 19, 60], [22, 73, 19, 62, "ReactCurrentOwner"], [22, 90, 19, 79], [22, 92, 20, 8, "current"], [22, 99, 20, 15], [23, 6, 21, 4], [24, 6, 22, 4, "React"], [24, 20, 22, 9], [24, 21, 22, 10, "__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE"], [24, 84, 22, 73], [24, 86, 23, 8, "ReactCurrentOwner"], [24, 103, 23, 25], [24, 105, 23, 27, "current"], [25, 4, 23, 34], [26, 2, 25, 0], [27, 2, 27, 7], [27, 11, 27, 16, "isReactRendering"], [27, 27, 27, 32, "isReactRendering"], [27, 28, 27, 32], [27, 30, 27, 35], [28, 4, 28, 2], [28, 11, 28, 9], [28, 12, 28, 10], [28, 13, 28, 11, "getCurrentReactOwner"], [28, 33, 28, 31], [28, 34, 28, 32], [28, 35, 28, 33], [29, 2, 29, 0], [30, 2, 31, 7], [30, 11, 31, 16, "isFirstReactRender"], [30, 29, 31, 34, "isFirstReactRender"], [30, 30, 31, 34], [30, 32, 31, 37], [31, 4, 32, 2], [31, 8, 32, 8, "current<PERSON>wner"], [31, 20, 32, 20], [31, 23, 32, 23, "getCurrentReactOwner"], [31, 43, 32, 43], [31, 44, 32, 44], [31, 45, 32, 45], [32, 4, 33, 2], [33, 4, 34, 2], [34, 4, 35, 2], [34, 11, 35, 9, "current<PERSON>wner"], [34, 23, 35, 21], [34, 27, 35, 25], [34, 28, 35, 26, "current<PERSON>wner"], [34, 40, 35, 38], [34, 42, 35, 40, "alternate"], [34, 51, 35, 49], [35, 2, 36, 0], [37, 2, 38, 0], [38, 2, 39, 0], [39, 2, 40, 7], [39, 11, 40, 16, "componentWithRef"], [39, 27, 40, 32, "componentWithRef"], [39, 28, 41, 2, "render"], [39, 34, 41, 40], [39, 36, 42, 51], [40, 4, 43, 2], [40, 8, 43, 6, "IS_REACT_19"], [40, 19, 43, 17], [40, 21, 43, 19], [41, 6, 44, 4], [41, 13, 44, 12, "_ref"], [41, 17, 44, 12], [42, 8, 44, 12], [42, 12, 44, 15, "ref"], [42, 15, 44, 18], [42, 18, 44, 18, "_ref"], [42, 22, 44, 18], [42, 23, 44, 15, "ref"], [42, 26, 44, 18], [43, 10, 44, 23, "props"], [43, 15, 44, 28], [43, 22, 44, 28, "_objectWithoutProperties2"], [43, 47, 44, 28], [43, 48, 44, 28, "default"], [43, 55, 44, 28], [43, 57, 44, 28, "_ref"], [43, 61, 44, 28], [43, 63, 44, 28, "_excluded"], [43, 72, 44, 28], [44, 8, 44, 28], [44, 15, 45, 6, "render"], [44, 21, 45, 12], [44, 22, 46, 8, "props"], [44, 27, 46, 13], [44, 29, 47, 8, "ref"], [44, 32, 48, 6], [44, 33, 48, 7], [45, 6, 48, 7], [46, 4, 49, 2], [47, 4, 51, 2], [47, 24, 51, 9], [47, 28, 51, 9, "forwardRef"], [47, 45, 51, 19], [47, 47, 52, 4, "render"], [47, 53, 53, 2], [47, 54, 53, 3], [48, 2, 54, 0], [49, 0, 54, 1], [49, 3]], "functionMap": {"names": ["<global>", "getCurrentReactOwner", "isReactRendering", "isFirstReactRender", "componentWithRef", "<anonymous>"], "mappings": "AAA;ACa;CDW;OEE;CFE;OGE;CHK;OII;YCI;ODI;CJM"}}, "type": "js/module"}]}