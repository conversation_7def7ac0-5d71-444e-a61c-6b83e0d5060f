{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 16, "index": 154}, "end": {"line": 4, "column": 32, "index": 170}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.IsolateGroupContext = exports.GroupContext = void 0;\n  var react_1 = require(_dependencyMap[0], \"react\");\n  exports.GroupContext = (0, react_1.createContext)({\n    groupHover: false,\n    groupFocus: false,\n    groupActive: false\n  });\n  exports.IsolateGroupContext = (0, react_1.createContext)({\n    isolateGroupHover: false,\n    isolateGroupFocus: false,\n    isolateGroupActive: false\n  });\n});", "lineCount": 19, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "IsolateGroupContext"], [7, 29, 3, 27], [7, 32, 3, 30, "exports"], [7, 39, 3, 37], [7, 40, 3, 38, "GroupContext"], [7, 52, 3, 50], [7, 55, 3, 53], [7, 60, 3, 58], [7, 61, 3, 59], [8, 2, 4, 0], [8, 6, 4, 6, "react_1"], [8, 13, 4, 13], [8, 16, 4, 16, "require"], [8, 23, 4, 23], [8, 24, 4, 23, "_dependencyMap"], [8, 38, 4, 23], [8, 50, 4, 31], [8, 51, 4, 32], [9, 2, 5, 0, "exports"], [9, 9, 5, 7], [9, 10, 5, 8, "GroupContext"], [9, 22, 5, 20], [9, 25, 5, 23], [9, 26, 5, 24], [9, 27, 5, 25], [9, 29, 5, 27, "react_1"], [9, 36, 5, 34], [9, 37, 5, 35, "createContext"], [9, 50, 5, 48], [9, 52, 5, 50], [10, 4, 6, 4, "groupHover"], [10, 14, 6, 14], [10, 16, 6, 16], [10, 21, 6, 21], [11, 4, 7, 4, "groupFocus"], [11, 14, 7, 14], [11, 16, 7, 16], [11, 21, 7, 21], [12, 4, 8, 4, "groupActive"], [12, 15, 8, 15], [12, 17, 8, 17], [13, 2, 9, 0], [13, 3, 9, 1], [13, 4, 9, 2], [14, 2, 10, 0, "exports"], [14, 9, 10, 7], [14, 10, 10, 8, "IsolateGroupContext"], [14, 29, 10, 27], [14, 32, 10, 30], [14, 33, 10, 31], [14, 34, 10, 32], [14, 36, 10, 34, "react_1"], [14, 43, 10, 41], [14, 44, 10, 42, "createContext"], [14, 57, 10, 55], [14, 59, 10, 57], [15, 4, 11, 4, "isolateGroupHover"], [15, 21, 11, 21], [15, 23, 11, 23], [15, 28, 11, 28], [16, 4, 12, 4, "isolateGroupFocus"], [16, 21, 12, 21], [16, 23, 12, 23], [16, 28, 12, 28], [17, 4, 13, 4, "isolateGroupActive"], [17, 22, 13, 22], [17, 24, 13, 24], [18, 2, 14, 0], [18, 3, 14, 1], [18, 4, 14, 2], [19, 0, 14, 3], [19, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}