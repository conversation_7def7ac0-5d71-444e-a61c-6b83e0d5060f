{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 26, "index": 41}}], "key": "WMoKxUKO/GMHeED0pzSR/dc1v7c=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 42}, "end": {"line": 4, "column": 40, "index": 82}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getShadowStyle = getShadowStyle;\n  var _color = _interopRequireDefault(require(_dependencyMap[1], \"color\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  function getShadowStyle(_ref) {\n    var offset = _ref.offset,\n      radius = _ref.radius,\n      opacity = _ref.opacity,\n      _ref$color = _ref.color,\n      color = _ref$color === void 0 ? '#000' : _ref$color;\n    var result = _reactNative.Platform.select({\n      web: {\n        boxShadow: `${offset.width}px ${offset.height}px ${radius}px ${(0, _color.default)(color).alpha(opacity).toString()}`\n      },\n      default: {\n        shadowOffset: offset,\n        shadowRadius: radius,\n        shadowColor: color,\n        shadowOpacity: opacity\n      }\n    });\n    return result;\n  }\n});", "lineCount": 30, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "getShadowStyle"], [8, 24, 1, 13], [8, 27, 1, 13, "getShadowStyle"], [8, 41, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_color"], [9, 12, 3, 0], [9, 15, 3, 0, "_interopRequireDefault"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_reactNative"], [10, 18, 4, 0], [10, 21, 4, 0, "require"], [10, 28, 4, 0], [10, 29, 4, 0, "_dependencyMap"], [10, 43, 4, 0], [11, 2, 5, 7], [11, 11, 5, 16, "getShadowStyle"], [11, 25, 5, 30, "getShadowStyle"], [11, 26, 5, 30, "_ref"], [11, 30, 5, 30], [11, 32, 10, 3], [12, 4, 10, 3], [12, 8, 6, 2, "offset"], [12, 14, 6, 8], [12, 17, 6, 8, "_ref"], [12, 21, 6, 8], [12, 22, 6, 2, "offset"], [12, 28, 6, 8], [13, 6, 7, 2, "radius"], [13, 12, 7, 8], [13, 15, 7, 8, "_ref"], [13, 19, 7, 8], [13, 20, 7, 2, "radius"], [13, 26, 7, 8], [14, 6, 8, 2, "opacity"], [14, 13, 8, 9], [14, 16, 8, 9, "_ref"], [14, 20, 8, 9], [14, 21, 8, 2, "opacity"], [14, 28, 8, 9], [15, 6, 8, 9, "_ref$color"], [15, 16, 8, 9], [15, 19, 8, 9, "_ref"], [15, 23, 8, 9], [15, 24, 9, 2, "color"], [15, 29, 9, 7], [16, 6, 9, 2, "color"], [16, 11, 9, 7], [16, 14, 9, 7, "_ref$color"], [16, 24, 9, 7], [16, 38, 9, 10], [16, 44, 9, 16], [16, 47, 9, 16, "_ref$color"], [16, 57, 9, 16], [17, 4, 11, 2], [17, 8, 11, 8, "result"], [17, 14, 11, 14], [17, 17, 11, 17, "Platform"], [17, 38, 11, 25], [17, 39, 11, 26, "select"], [17, 45, 11, 32], [17, 46, 11, 33], [18, 6, 12, 4, "web"], [18, 9, 12, 7], [18, 11, 12, 9], [19, 8, 13, 6, "boxShadow"], [19, 17, 13, 15], [19, 19, 13, 17], [19, 22, 13, 20, "offset"], [19, 28, 13, 26], [19, 29, 13, 27, "width"], [19, 34, 13, 32], [19, 40, 13, 38, "offset"], [19, 46, 13, 44], [19, 47, 13, 45, "height"], [19, 53, 13, 51], [19, 59, 13, 57, "radius"], [19, 65, 13, 63], [19, 71, 13, 69], [19, 75, 13, 69, "Color"], [19, 89, 13, 74], [19, 91, 13, 75, "color"], [19, 96, 13, 80], [19, 97, 13, 81], [19, 98, 13, 82, "alpha"], [19, 103, 13, 87], [19, 104, 13, 88, "opacity"], [19, 111, 13, 95], [19, 112, 13, 96], [19, 113, 13, 97, "toString"], [19, 121, 13, 105], [19, 122, 13, 106], [19, 123, 13, 107], [20, 6, 14, 4], [20, 7, 14, 5], [21, 6, 15, 4, "default"], [21, 13, 15, 11], [21, 15, 15, 13], [22, 8, 16, 6, "shadowOffset"], [22, 20, 16, 18], [22, 22, 16, 20, "offset"], [22, 28, 16, 26], [23, 8, 17, 6, "shadowRadius"], [23, 20, 17, 18], [23, 22, 17, 20, "radius"], [23, 28, 17, 26], [24, 8, 18, 6, "shadowColor"], [24, 19, 18, 17], [24, 21, 18, 19, "color"], [24, 26, 18, 24], [25, 8, 19, 6, "shadowOpacity"], [25, 21, 19, 19], [25, 23, 19, 21, "opacity"], [26, 6, 20, 4], [27, 4, 21, 2], [27, 5, 21, 3], [27, 6, 21, 4], [28, 4, 22, 2], [28, 11, 22, 9, "result"], [28, 17, 22, 15], [29, 2, 23, 0], [30, 0, 23, 1], [30, 3]], "functionMap": {"names": ["<global>", "getShadowStyle"], "mappings": "AAA;OCI;CDkB"}}, "type": "js/module"}]}