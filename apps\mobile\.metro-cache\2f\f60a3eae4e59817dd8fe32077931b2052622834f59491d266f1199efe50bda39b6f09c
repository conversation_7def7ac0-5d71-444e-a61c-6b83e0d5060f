{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 52, "index": 52}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 53}, "end": {"line": 2, "column": 31, "index": 84}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./GenericTouchable", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 120}, "end": {"line": 4, "column": 50, "index": 170}}], "key": "b7B+HFZ7s4hNhosUwPttECYmJ2U=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _reactNative = require(_dependencyMap[7], \"react-native\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[8], \"react\"));\n  var React = _react;\n  var _GenericTouchable = _interopRequireDefault(require(_dependencyMap[9], \"./GenericTouchable\"));\n  var _jsxDevRuntime = require(_dependencyMap[10], \"react/jsx-dev-runtime\");\n  var _excluded = [\"style\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\components\\\\touchables\\\\TouchableNativeFeedback.android.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  /**\n   * @deprecated TouchableNativeFeedback will be removed in the future version of Gesture Handler. Use Pressable instead.\n   *\n   * TouchableNativeFeedback behaves slightly different than RN's TouchableNativeFeedback.\n   * There's small difference with handling long press ripple since RN's implementation calls\n   * ripple animation via bridge. This solution leaves all animations' handling for native components so\n   * it follows native behaviours.\n   */\n  var TouchableNativeFeedback = exports.default = /*#__PURE__*/function (_Component) {\n    function TouchableNativeFeedback() {\n      (0, _classCallCheck2.default)(this, TouchableNativeFeedback);\n      return _callSuper(this, TouchableNativeFeedback, arguments);\n    }\n    (0, _inherits2.default)(TouchableNativeFeedback, _Component);\n    return (0, _createClass2.default)(TouchableNativeFeedback, [{\n      key: \"getExtraButtonProps\",\n      value: function getExtraButtonProps() {\n        var extraProps = {};\n        var background = this.props.background;\n        if (background) {\n          // I changed type values to match those used in RN\n          // TODO(TS): check if it works the same as previous implementation - looks like it works the same as RN component, so it should be ok\n          if (background.type === 'RippleAndroid') {\n            extraProps['borderless'] = background.borderless;\n            extraProps['rippleColor'] = background.color;\n          } else if (background.type === 'ThemeAttrAndroid') {\n            extraProps['borderless'] = background.attribute === 'selectableItemBackgroundBorderless';\n          }\n          // I moved it from above since it should be available in all options\n          extraProps['rippleRadius'] = background.rippleRadius;\n        }\n        extraProps['foreground'] = this.props.useForeground;\n        return extraProps;\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this$props = this.props,\n          _this$props$style = _this$props.style,\n          style = _this$props$style === void 0 ? {} : _this$props$style,\n          rest = (0, _objectWithoutProperties2.default)(_this$props, _excluded);\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_GenericTouchable.default, {\n          ...rest,\n          style: style,\n          extraButtonProps: this.getExtraButtonProps()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 7\n        }, this);\n      }\n    }]);\n  }(_react.Component);\n  TouchableNativeFeedback.defaultProps = {\n    ..._GenericTouchable.default.defaultProps,\n    useForeground: true,\n    extraButtonProps: {\n      // Disable hiding ripple on Android\n      rippleColor: null\n    }\n  };\n  // Could be taken as RNTouchableNativeFeedback.SelectableBackground etc. but the API may change\n  TouchableNativeFeedback.SelectableBackground = rippleRadius => ({\n    type: 'ThemeAttrAndroid',\n    // I added `attribute` prop to clone the implementation of RN and be able to use only 2 types\n    attribute: 'selectableItemBackground',\n    rippleRadius\n  });\n  TouchableNativeFeedback.SelectableBackgroundBorderless = rippleRadius => ({\n    type: 'ThemeAttrAndroid',\n    attribute: 'selectableItemBackgroundBorderless',\n    rippleRadius\n  });\n  TouchableNativeFeedback.Ripple = (color, borderless, rippleRadius) => ({\n    type: 'RippleAndroid',\n    color,\n    borderless,\n    rippleRadius\n  });\n  TouchableNativeFeedback.canUseNativeForeground = () => _reactNative.Platform.OS === 'android' && _reactNative.Platform.Version >= 23;\n});", "lineCount": 103, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_reactNative"], [13, 18, 1, 0], [13, 21, 1, 0, "require"], [13, 28, 1, 0], [13, 29, 1, 0, "_dependencyMap"], [13, 43, 1, 0], [14, 2, 2, 0], [14, 6, 2, 0, "_react"], [14, 12, 2, 0], [14, 15, 2, 0, "_interopRequireWildcard"], [14, 38, 2, 0], [14, 39, 2, 0, "require"], [14, 46, 2, 0], [14, 47, 2, 0, "_dependencyMap"], [14, 61, 2, 0], [15, 2, 2, 31], [15, 6, 2, 31, "React"], [15, 11, 2, 31], [15, 14, 2, 31, "_react"], [15, 20, 2, 31], [16, 2, 4, 0], [16, 6, 4, 0, "_GenericTouchable"], [16, 23, 4, 0], [16, 26, 4, 0, "_interopRequireDefault"], [16, 48, 4, 0], [16, 49, 4, 0, "require"], [16, 56, 4, 0], [16, 57, 4, 0, "_dependencyMap"], [16, 71, 4, 0], [17, 2, 4, 50], [17, 6, 4, 50, "_jsxDevRuntime"], [17, 20, 4, 50], [17, 23, 4, 50, "require"], [17, 30, 4, 50], [17, 31, 4, 50, "_dependencyMap"], [17, 45, 4, 50], [18, 2, 4, 50], [18, 6, 4, 50, "_excluded"], [18, 15, 4, 50], [19, 2, 4, 50], [19, 6, 4, 50, "_jsxFileName"], [19, 18, 4, 50], [20, 2, 4, 50], [20, 11, 4, 50, "_interopRequireWildcard"], [20, 35, 4, 50, "e"], [20, 36, 4, 50], [20, 38, 4, 50, "t"], [20, 39, 4, 50], [20, 68, 4, 50, "WeakMap"], [20, 75, 4, 50], [20, 81, 4, 50, "r"], [20, 82, 4, 50], [20, 89, 4, 50, "WeakMap"], [20, 96, 4, 50], [20, 100, 4, 50, "n"], [20, 101, 4, 50], [20, 108, 4, 50, "WeakMap"], [20, 115, 4, 50], [20, 127, 4, 50, "_interopRequireWildcard"], [20, 150, 4, 50], [20, 162, 4, 50, "_interopRequireWildcard"], [20, 163, 4, 50, "e"], [20, 164, 4, 50], [20, 166, 4, 50, "t"], [20, 167, 4, 50], [20, 176, 4, 50, "t"], [20, 177, 4, 50], [20, 181, 4, 50, "e"], [20, 182, 4, 50], [20, 186, 4, 50, "e"], [20, 187, 4, 50], [20, 188, 4, 50, "__esModule"], [20, 198, 4, 50], [20, 207, 4, 50, "e"], [20, 208, 4, 50], [20, 214, 4, 50, "o"], [20, 215, 4, 50], [20, 217, 4, 50, "i"], [20, 218, 4, 50], [20, 220, 4, 50, "f"], [20, 221, 4, 50], [20, 226, 4, 50, "__proto__"], [20, 235, 4, 50], [20, 243, 4, 50, "default"], [20, 250, 4, 50], [20, 252, 4, 50, "e"], [20, 253, 4, 50], [20, 270, 4, 50, "e"], [20, 271, 4, 50], [20, 294, 4, 50, "e"], [20, 295, 4, 50], [20, 320, 4, 50, "e"], [20, 321, 4, 50], [20, 330, 4, 50, "f"], [20, 331, 4, 50], [20, 337, 4, 50, "o"], [20, 338, 4, 50], [20, 341, 4, 50, "t"], [20, 342, 4, 50], [20, 345, 4, 50, "n"], [20, 346, 4, 50], [20, 349, 4, 50, "r"], [20, 350, 4, 50], [20, 358, 4, 50, "o"], [20, 359, 4, 50], [20, 360, 4, 50, "has"], [20, 363, 4, 50], [20, 364, 4, 50, "e"], [20, 365, 4, 50], [20, 375, 4, 50, "o"], [20, 376, 4, 50], [20, 377, 4, 50, "get"], [20, 380, 4, 50], [20, 381, 4, 50, "e"], [20, 382, 4, 50], [20, 385, 4, 50, "o"], [20, 386, 4, 50], [20, 387, 4, 50, "set"], [20, 390, 4, 50], [20, 391, 4, 50, "e"], [20, 392, 4, 50], [20, 394, 4, 50, "f"], [20, 395, 4, 50], [20, 409, 4, 50, "_t"], [20, 411, 4, 50], [20, 415, 4, 50, "e"], [20, 416, 4, 50], [20, 432, 4, 50, "_t"], [20, 434, 4, 50], [20, 441, 4, 50, "hasOwnProperty"], [20, 455, 4, 50], [20, 456, 4, 50, "call"], [20, 460, 4, 50], [20, 461, 4, 50, "e"], [20, 462, 4, 50], [20, 464, 4, 50, "_t"], [20, 466, 4, 50], [20, 473, 4, 50, "i"], [20, 474, 4, 50], [20, 478, 4, 50, "o"], [20, 479, 4, 50], [20, 482, 4, 50, "Object"], [20, 488, 4, 50], [20, 489, 4, 50, "defineProperty"], [20, 503, 4, 50], [20, 508, 4, 50, "Object"], [20, 514, 4, 50], [20, 515, 4, 50, "getOwnPropertyDescriptor"], [20, 539, 4, 50], [20, 540, 4, 50, "e"], [20, 541, 4, 50], [20, 543, 4, 50, "_t"], [20, 545, 4, 50], [20, 552, 4, 50, "i"], [20, 553, 4, 50], [20, 554, 4, 50, "get"], [20, 557, 4, 50], [20, 561, 4, 50, "i"], [20, 562, 4, 50], [20, 563, 4, 50, "set"], [20, 566, 4, 50], [20, 570, 4, 50, "o"], [20, 571, 4, 50], [20, 572, 4, 50, "f"], [20, 573, 4, 50], [20, 575, 4, 50, "_t"], [20, 577, 4, 50], [20, 579, 4, 50, "i"], [20, 580, 4, 50], [20, 584, 4, 50, "f"], [20, 585, 4, 50], [20, 586, 4, 50, "_t"], [20, 588, 4, 50], [20, 592, 4, 50, "e"], [20, 593, 4, 50], [20, 594, 4, 50, "_t"], [20, 596, 4, 50], [20, 607, 4, 50, "f"], [20, 608, 4, 50], [20, 613, 4, 50, "e"], [20, 614, 4, 50], [20, 616, 4, 50, "t"], [20, 617, 4, 50], [21, 2, 4, 50], [21, 11, 4, 50, "_callSuper"], [21, 22, 4, 50, "t"], [21, 23, 4, 50], [21, 25, 4, 50, "o"], [21, 26, 4, 50], [21, 28, 4, 50, "e"], [21, 29, 4, 50], [21, 40, 4, 50, "o"], [21, 41, 4, 50], [21, 48, 4, 50, "_getPrototypeOf2"], [21, 64, 4, 50], [21, 65, 4, 50, "default"], [21, 72, 4, 50], [21, 74, 4, 50, "o"], [21, 75, 4, 50], [21, 82, 4, 50, "_possibleConstructorReturn2"], [21, 109, 4, 50], [21, 110, 4, 50, "default"], [21, 117, 4, 50], [21, 119, 4, 50, "t"], [21, 120, 4, 50], [21, 122, 4, 50, "_isNativeReflectConstruct"], [21, 147, 4, 50], [21, 152, 4, 50, "Reflect"], [21, 159, 4, 50], [21, 160, 4, 50, "construct"], [21, 169, 4, 50], [21, 170, 4, 50, "o"], [21, 171, 4, 50], [21, 173, 4, 50, "e"], [21, 174, 4, 50], [21, 186, 4, 50, "_getPrototypeOf2"], [21, 202, 4, 50], [21, 203, 4, 50, "default"], [21, 210, 4, 50], [21, 212, 4, 50, "t"], [21, 213, 4, 50], [21, 215, 4, 50, "constructor"], [21, 226, 4, 50], [21, 230, 4, 50, "o"], [21, 231, 4, 50], [21, 232, 4, 50, "apply"], [21, 237, 4, 50], [21, 238, 4, 50, "t"], [21, 239, 4, 50], [21, 241, 4, 50, "e"], [21, 242, 4, 50], [22, 2, 4, 50], [22, 11, 4, 50, "_isNativeReflectConstruct"], [22, 37, 4, 50], [22, 51, 4, 50, "t"], [22, 52, 4, 50], [22, 56, 4, 50, "Boolean"], [22, 63, 4, 50], [22, 64, 4, 50, "prototype"], [22, 73, 4, 50], [22, 74, 4, 50, "valueOf"], [22, 81, 4, 50], [22, 82, 4, 50, "call"], [22, 86, 4, 50], [22, 87, 4, 50, "Reflect"], [22, 94, 4, 50], [22, 95, 4, 50, "construct"], [22, 104, 4, 50], [22, 105, 4, 50, "Boolean"], [22, 112, 4, 50], [22, 145, 4, 50, "t"], [22, 146, 4, 50], [22, 159, 4, 50, "_isNativeReflectConstruct"], [22, 184, 4, 50], [22, 196, 4, 50, "_isNativeReflectConstruct"], [22, 197, 4, 50], [22, 210, 4, 50, "t"], [22, 211, 4, 50], [23, 2, 10, 0], [24, 0, 11, 0], [25, 0, 12, 0], [26, 0, 13, 0], [27, 0, 14, 0], [28, 0, 15, 0], [29, 0, 16, 0], [30, 0, 17, 0], [31, 2, 10, 0], [31, 6, 18, 21, "TouchableNativeFeedback"], [31, 29, 18, 44], [31, 32, 18, 44, "exports"], [31, 39, 18, 44], [31, 40, 18, 44, "default"], [31, 47, 18, 44], [31, 73, 18, 44, "_Component"], [31, 83, 18, 44], [32, 4, 18, 44], [32, 13, 18, 44, "TouchableNativeFeedback"], [32, 37, 18, 44], [33, 6, 18, 44], [33, 10, 18, 44, "_classCallCheck2"], [33, 26, 18, 44], [33, 27, 18, 44, "default"], [33, 34, 18, 44], [33, 42, 18, 44, "TouchableNativeFeedback"], [33, 65, 18, 44], [34, 6, 18, 44], [34, 13, 18, 44, "_callSuper"], [34, 23, 18, 44], [34, 30, 18, 44, "TouchableNativeFeedback"], [34, 53, 18, 44], [34, 55, 18, 44, "arguments"], [34, 64, 18, 44], [35, 4, 18, 44], [36, 4, 18, 44], [36, 8, 18, 44, "_inherits2"], [36, 18, 18, 44], [36, 19, 18, 44, "default"], [36, 26, 18, 44], [36, 28, 18, 44, "TouchableNativeFeedback"], [36, 51, 18, 44], [36, 53, 18, 44, "_Component"], [36, 63, 18, 44], [37, 4, 18, 44], [37, 15, 18, 44, "_createClass2"], [37, 28, 18, 44], [37, 29, 18, 44, "default"], [37, 36, 18, 44], [37, 38, 18, 44, "TouchableNativeFeedback"], [37, 61, 18, 44], [38, 6, 18, 44, "key"], [38, 9, 18, 44], [39, 6, 18, 44, "value"], [39, 11, 18, 44], [39, 13, 54, 2], [39, 22, 54, 2, "getExtraButtonProps"], [39, 41, 54, 21, "getExtraButtonProps"], [39, 42, 54, 21], [39, 44, 54, 24], [40, 8, 55, 4], [40, 12, 55, 10, "extraProps"], [40, 22, 55, 55], [40, 25, 55, 58], [40, 26, 55, 59], [40, 27, 55, 60], [41, 8, 56, 4], [41, 12, 56, 12, "background"], [41, 22, 56, 22], [41, 25, 56, 27], [41, 29, 56, 31], [41, 30, 56, 32, "props"], [41, 35, 56, 37], [41, 36, 56, 12, "background"], [41, 46, 56, 22], [42, 8, 57, 4], [42, 12, 57, 8, "background"], [42, 22, 57, 18], [42, 24, 57, 20], [43, 10, 58, 6], [44, 10, 59, 6], [45, 10, 60, 6], [45, 14, 60, 10, "background"], [45, 24, 60, 20], [45, 25, 60, 21, "type"], [45, 29, 60, 25], [45, 34, 60, 30], [45, 49, 60, 45], [45, 51, 60, 47], [46, 12, 61, 8, "extraProps"], [46, 22, 61, 18], [46, 23, 61, 19], [46, 35, 61, 31], [46, 36, 61, 32], [46, 39, 61, 35, "background"], [46, 49, 61, 45], [46, 50, 61, 46, "borderless"], [46, 60, 61, 56], [47, 12, 62, 8, "extraProps"], [47, 22, 62, 18], [47, 23, 62, 19], [47, 36, 62, 32], [47, 37, 62, 33], [47, 40, 62, 36, "background"], [47, 50, 62, 46], [47, 51, 62, 47, "color"], [47, 56, 62, 52], [48, 10, 63, 6], [48, 11, 63, 7], [48, 17, 63, 13], [48, 21, 63, 17, "background"], [48, 31, 63, 27], [48, 32, 63, 28, "type"], [48, 36, 63, 32], [48, 41, 63, 37], [48, 59, 63, 55], [48, 61, 63, 57], [49, 12, 64, 8, "extraProps"], [49, 22, 64, 18], [49, 23, 64, 19], [49, 35, 64, 31], [49, 36, 64, 32], [49, 39, 65, 10, "background"], [49, 49, 65, 20], [49, 50, 65, 21, "attribute"], [49, 59, 65, 30], [49, 64, 65, 35], [49, 100, 65, 71], [50, 10, 66, 6], [51, 10, 67, 6], [52, 10, 68, 6, "extraProps"], [52, 20, 68, 16], [52, 21, 68, 17], [52, 35, 68, 31], [52, 36, 68, 32], [52, 39, 68, 35, "background"], [52, 49, 68, 45], [52, 50, 68, 46, "rippleRadius"], [52, 62, 68, 58], [53, 8, 69, 4], [54, 8, 70, 4, "extraProps"], [54, 18, 70, 14], [54, 19, 70, 15], [54, 31, 70, 27], [54, 32, 70, 28], [54, 35, 70, 31], [54, 39, 70, 35], [54, 40, 70, 36, "props"], [54, 45, 70, 41], [54, 46, 70, 42, "useForeground"], [54, 59, 70, 55], [55, 8, 71, 4], [55, 15, 71, 11, "extraProps"], [55, 25, 71, 21], [56, 6, 72, 2], [57, 4, 72, 3], [58, 6, 72, 3, "key"], [58, 9, 72, 3], [59, 6, 72, 3, "value"], [59, 11, 72, 3], [59, 13, 73, 2], [59, 22, 73, 2, "render"], [59, 28, 73, 8, "render"], [59, 29, 73, 8], [59, 31, 73, 11], [60, 8, 74, 4], [60, 12, 74, 4, "_this$props"], [60, 23, 74, 4], [60, 26, 74, 36], [60, 30, 74, 40], [60, 31, 74, 41, "props"], [60, 36, 74, 46], [61, 10, 74, 46, "_this$props$style"], [61, 27, 74, 46], [61, 30, 74, 46, "_this$props"], [61, 41, 74, 46], [61, 42, 74, 12, "style"], [61, 47, 74, 17], [62, 10, 74, 12, "style"], [62, 15, 74, 17], [62, 18, 74, 17, "_this$props$style"], [62, 35, 74, 17], [62, 49, 74, 20], [62, 50, 74, 21], [62, 51, 74, 22], [62, 54, 74, 22, "_this$props$style"], [62, 71, 74, 22], [63, 10, 74, 27, "rest"], [63, 14, 74, 31], [63, 21, 74, 31, "_objectWithoutProperties2"], [63, 46, 74, 31], [63, 47, 74, 31, "default"], [63, 54, 74, 31], [63, 56, 74, 31, "_this$props"], [63, 67, 74, 31], [63, 69, 74, 31, "_excluded"], [63, 78, 74, 31], [64, 8, 75, 4], [64, 28, 76, 6], [64, 32, 76, 6, "_jsxDevRuntime"], [64, 46, 76, 6], [64, 47, 76, 6, "jsxDEV"], [64, 53, 76, 6], [64, 55, 76, 7, "_GenericTouchable"], [64, 72, 76, 7], [64, 73, 76, 7, "default"], [64, 80, 76, 23], [65, 10, 76, 23], [65, 13, 77, 12, "rest"], [65, 17, 77, 16], [66, 10, 78, 8, "style"], [66, 15, 78, 13], [66, 17, 78, 15, "style"], [66, 22, 78, 21], [67, 10, 79, 8, "extraButtonProps"], [67, 26, 79, 24], [67, 28, 79, 26], [67, 32, 79, 30], [67, 33, 79, 31, "getExtraButtonProps"], [67, 52, 79, 50], [67, 53, 79, 51], [68, 8, 79, 53], [69, 10, 79, 53, "fileName"], [69, 18, 79, 53], [69, 20, 79, 53, "_jsxFileName"], [69, 32, 79, 53], [70, 10, 79, 53, "lineNumber"], [70, 20, 79, 53], [71, 10, 79, 53, "columnNumber"], [71, 22, 79, 53], [72, 8, 79, 53], [72, 15, 80, 7], [72, 16, 80, 8], [73, 6, 82, 2], [74, 4, 82, 3], [75, 2, 82, 3], [75, 4, 18, 53, "Component"], [75, 20, 18, 62], [76, 2, 18, 21, "TouchableNativeFeedback"], [76, 25, 18, 44], [76, 26, 19, 9, "defaultProps"], [76, 38, 19, 21], [76, 41, 19, 24], [77, 4, 20, 4], [77, 7, 20, 7, "GenericTouchable"], [77, 32, 20, 23], [77, 33, 20, 24, "defaultProps"], [77, 45, 20, 36], [78, 4, 21, 4, "useForeground"], [78, 17, 21, 17], [78, 19, 21, 19], [78, 23, 21, 23], [79, 4, 22, 4, "extraButtonProps"], [79, 20, 22, 20], [79, 22, 22, 22], [80, 6, 23, 6], [81, 6, 24, 6, "rippleColor"], [81, 17, 24, 17], [81, 19, 24, 19], [82, 4, 25, 4], [83, 2, 26, 2], [83, 3, 26, 3], [84, 2, 28, 2], [85, 2, 18, 21, "TouchableNativeFeedback"], [85, 25, 18, 44], [85, 26, 29, 9, "SelectableBackground"], [85, 46, 29, 29], [85, 49, 29, 33, "rippleRadius"], [85, 61, 29, 54], [85, 66, 29, 60], [86, 4, 30, 4, "type"], [86, 8, 30, 8], [86, 10, 30, 10], [86, 28, 30, 28], [87, 4, 31, 4], [88, 4, 32, 4, "attribute"], [88, 13, 32, 13], [88, 15, 32, 15], [88, 41, 32, 41], [89, 4, 33, 4, "rippleRadius"], [90, 2, 34, 2], [90, 3, 34, 3], [90, 4, 34, 4], [91, 2, 18, 21, "TouchableNativeFeedback"], [91, 25, 18, 44], [91, 26, 35, 9, "SelectableBackgroundBorderless"], [91, 56, 35, 39], [91, 59, 35, 43, "rippleRadius"], [91, 71, 35, 64], [91, 76, 35, 70], [92, 4, 36, 4, "type"], [92, 8, 36, 8], [92, 10, 36, 10], [92, 28, 36, 28], [93, 4, 37, 4, "attribute"], [93, 13, 37, 13], [93, 15, 37, 15], [93, 51, 37, 51], [94, 4, 38, 4, "rippleRadius"], [95, 2, 39, 2], [95, 3, 39, 3], [95, 4, 39, 4], [96, 2, 18, 21, "TouchableNativeFeedback"], [96, 25, 18, 44], [96, 26, 40, 9, "<PERSON><PERSON><PERSON>"], [96, 32, 40, 15], [96, 35, 40, 18], [96, 36, 41, 4, "color"], [96, 41, 41, 21], [96, 43, 42, 4, "borderless"], [96, 53, 42, 23], [96, 55, 43, 4, "rippleRadius"], [96, 67, 43, 25], [96, 73, 44, 8], [97, 4, 45, 4, "type"], [97, 8, 45, 8], [97, 10, 45, 10], [97, 25, 45, 25], [98, 4, 46, 4, "color"], [98, 9, 46, 9], [99, 4, 47, 4, "borderless"], [99, 14, 47, 14], [100, 4, 48, 4, "rippleRadius"], [101, 2, 49, 2], [101, 3, 49, 3], [101, 4, 49, 4], [102, 2, 18, 21, "TouchableNativeFeedback"], [102, 25, 18, 44], [102, 26, 51, 9, "canUseNativeForeground"], [102, 48, 51, 31], [102, 51, 51, 34], [102, 57, 52, 4, "Platform"], [102, 78, 52, 12], [102, 79, 52, 13, "OS"], [102, 81, 52, 15], [102, 86, 52, 20], [102, 95, 52, 29], [102, 99, 52, 33, "Platform"], [102, 120, 52, 41], [102, 121, 52, 42, "Version"], [102, 128, 52, 49], [102, 132, 52, 53], [102, 134, 52, 55], [103, 0, 52, 55], [103, 3]], "functionMap": {"names": ["<global>", "TouchableNativeFeedback", "SelectableBackground", "SelectableBackgroundBorderless", "<PERSON><PERSON><PERSON>", "canUseNativeForeground", "getExtraButtonProps", "render"], "mappings": "AAA;eCiB;gCCW;IDK;0CEC;IFI;kBGC;IHS;kCIE;uDJC;EKE;GLkB;EMC;GNS;CDC"}}, "type": "js/module"}]}