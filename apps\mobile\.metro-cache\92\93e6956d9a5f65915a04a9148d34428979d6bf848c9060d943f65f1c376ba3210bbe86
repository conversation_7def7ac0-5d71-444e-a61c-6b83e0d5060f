{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.FlingGesture = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _gesture = require(_dependencyMap[6], \"./gesture\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var FlingGesture = exports.FlingGesture = /*#__PURE__*/function (_BaseGesture) {\n    function FlingGesture() {\n      var _this;\n      (0, _classCallCheck2.default)(this, FlingGesture);\n      _this = _callSuper(this, FlingGesture);\n      _this.config = {};\n      _this.handlerName = 'FlingGestureHandler';\n      return _this;\n    }\n\n    /**\n     * Determine exact number of points required to handle the fling gesture.\n     * @param pointers\n     */\n    (0, _inherits2.default)(FlingGesture, _BaseGesture);\n    return (0, _createClass2.default)(FlingGesture, [{\n      key: \"numberOfPointers\",\n      value: function numberOfPointers(pointers) {\n        this.config.numberOfPointers = pointers;\n        return this;\n      }\n\n      /**\n       * Expressed allowed direction of movement.\n       * Expected values are exported as constants in the Directions object.\n       * Arguments can be combined using `|` operator. Default value is set to `MouseButton.LEFT`.\n       * @param direction\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/fling-gesture/#directionvalue-directions\n       */\n    }, {\n      key: \"direction\",\n      value: function direction(_direction) {\n        this.config.direction = _direction;\n        return this;\n      }\n    }]);\n  }(_gesture.BaseGesture);\n});", "lineCount": 52, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_gesture"], [12, 14, 1, 0], [12, 17, 1, 0, "require"], [12, 24, 1, 0], [12, 25, 1, 0, "_dependencyMap"], [12, 39, 1, 0], [13, 2, 1, 59], [13, 11, 1, 59, "_callSuper"], [13, 22, 1, 59, "t"], [13, 23, 1, 59], [13, 25, 1, 59, "o"], [13, 26, 1, 59], [13, 28, 1, 59, "e"], [13, 29, 1, 59], [13, 40, 1, 59, "o"], [13, 41, 1, 59], [13, 48, 1, 59, "_getPrototypeOf2"], [13, 64, 1, 59], [13, 65, 1, 59, "default"], [13, 72, 1, 59], [13, 74, 1, 59, "o"], [13, 75, 1, 59], [13, 82, 1, 59, "_possibleConstructorReturn2"], [13, 109, 1, 59], [13, 110, 1, 59, "default"], [13, 117, 1, 59], [13, 119, 1, 59, "t"], [13, 120, 1, 59], [13, 122, 1, 59, "_isNativeReflectConstruct"], [13, 147, 1, 59], [13, 152, 1, 59, "Reflect"], [13, 159, 1, 59], [13, 160, 1, 59, "construct"], [13, 169, 1, 59], [13, 170, 1, 59, "o"], [13, 171, 1, 59], [13, 173, 1, 59, "e"], [13, 174, 1, 59], [13, 186, 1, 59, "_getPrototypeOf2"], [13, 202, 1, 59], [13, 203, 1, 59, "default"], [13, 210, 1, 59], [13, 212, 1, 59, "t"], [13, 213, 1, 59], [13, 215, 1, 59, "constructor"], [13, 226, 1, 59], [13, 230, 1, 59, "o"], [13, 231, 1, 59], [13, 232, 1, 59, "apply"], [13, 237, 1, 59], [13, 238, 1, 59, "t"], [13, 239, 1, 59], [13, 241, 1, 59, "e"], [13, 242, 1, 59], [14, 2, 1, 59], [14, 11, 1, 59, "_isNativeReflectConstruct"], [14, 37, 1, 59], [14, 51, 1, 59, "t"], [14, 52, 1, 59], [14, 56, 1, 59, "Boolean"], [14, 63, 1, 59], [14, 64, 1, 59, "prototype"], [14, 73, 1, 59], [14, 74, 1, 59, "valueOf"], [14, 81, 1, 59], [14, 82, 1, 59, "call"], [14, 86, 1, 59], [14, 87, 1, 59, "Reflect"], [14, 94, 1, 59], [14, 95, 1, 59, "construct"], [14, 104, 1, 59], [14, 105, 1, 59, "Boolean"], [14, 112, 1, 59], [14, 145, 1, 59, "t"], [14, 146, 1, 59], [14, 159, 1, 59, "_isNativeReflectConstruct"], [14, 184, 1, 59], [14, 196, 1, 59, "_isNativeReflectConstruct"], [14, 197, 1, 59], [14, 210, 1, 59, "t"], [14, 211, 1, 59], [15, 2, 1, 59], [15, 6, 5, 13, "FlingGesture"], [15, 18, 5, 25], [15, 21, 5, 25, "exports"], [15, 28, 5, 25], [15, 29, 5, 25, "FlingGesture"], [15, 41, 5, 25], [15, 67, 5, 25, "_BaseGesture"], [15, 79, 5, 25], [16, 4, 8, 2], [16, 13, 8, 2, "FlingGesture"], [16, 26, 8, 2], [16, 28, 8, 16], [17, 6, 8, 16], [17, 10, 8, 16, "_this"], [17, 15, 8, 16], [18, 6, 8, 16], [18, 10, 8, 16, "_classCallCheck2"], [18, 26, 8, 16], [18, 27, 8, 16, "default"], [18, 34, 8, 16], [18, 42, 8, 16, "FlingGesture"], [18, 54, 8, 16], [19, 6, 9, 4, "_this"], [19, 11, 9, 4], [19, 14, 9, 4, "_callSuper"], [19, 24, 9, 4], [19, 31, 9, 4, "FlingGesture"], [19, 43, 9, 4], [20, 6, 9, 12, "_this"], [20, 11, 9, 12], [20, 12, 6, 9, "config"], [20, 18, 6, 15], [20, 21, 6, 58], [20, 22, 6, 59], [20, 23, 6, 60], [21, 6, 11, 4, "_this"], [21, 11, 11, 4], [21, 12, 11, 9, "handler<PERSON>ame"], [21, 23, 11, 20], [21, 26, 11, 23], [21, 47, 11, 44], [22, 6, 11, 45], [22, 13, 11, 45, "_this"], [22, 18, 11, 45], [23, 4, 12, 2], [25, 4, 14, 2], [26, 0, 15, 0], [27, 0, 16, 0], [28, 0, 17, 0], [29, 4, 14, 2], [29, 8, 14, 2, "_inherits2"], [29, 18, 14, 2], [29, 19, 14, 2, "default"], [29, 26, 14, 2], [29, 28, 14, 2, "FlingGesture"], [29, 40, 14, 2], [29, 42, 14, 2, "_BaseGesture"], [29, 54, 14, 2], [30, 4, 14, 2], [30, 15, 14, 2, "_createClass2"], [30, 28, 14, 2], [30, 29, 14, 2, "default"], [30, 36, 14, 2], [30, 38, 14, 2, "FlingGesture"], [30, 50, 14, 2], [31, 6, 14, 2, "key"], [31, 9, 14, 2], [32, 6, 14, 2, "value"], [32, 11, 14, 2], [32, 13, 18, 2], [32, 22, 18, 2, "numberOfPointers"], [32, 38, 18, 18, "numberOfPointers"], [32, 39, 18, 19, "pointers"], [32, 47, 18, 35], [32, 49, 18, 37], [33, 8, 19, 4], [33, 12, 19, 8], [33, 13, 19, 9, "config"], [33, 19, 19, 15], [33, 20, 19, 16, "numberOfPointers"], [33, 36, 19, 32], [33, 39, 19, 35, "pointers"], [33, 47, 19, 43], [34, 8, 20, 4], [34, 15, 20, 11], [34, 19, 20, 15], [35, 6, 21, 2], [37, 6, 23, 2], [38, 0, 24, 0], [39, 0, 25, 0], [40, 0, 26, 0], [41, 0, 27, 0], [42, 0, 28, 0], [43, 0, 29, 0], [44, 4, 23, 2], [45, 6, 23, 2, "key"], [45, 9, 23, 2], [46, 6, 23, 2, "value"], [46, 11, 23, 2], [46, 13, 30, 2], [46, 22, 30, 2, "direction"], [46, 31, 30, 11, "direction"], [46, 32, 30, 12, "direction"], [46, 42, 30, 29], [46, 44, 30, 31], [47, 8, 31, 4], [47, 12, 31, 8], [47, 13, 31, 9, "config"], [47, 19, 31, 15], [47, 20, 31, 16, "direction"], [47, 29, 31, 25], [47, 32, 31, 28, "direction"], [47, 42, 31, 37], [48, 8, 32, 4], [48, 15, 32, 11], [48, 19, 32, 15], [49, 6, 33, 2], [50, 4, 33, 3], [51, 2, 33, 3], [51, 4, 5, 34, "BaseGesture"], [51, 24, 5, 45], [52, 0, 5, 45], [52, 3]], "functionMap": {"names": ["<global>", "FlingGesture", "FlingGesture#constructor", "FlingGesture#numberOfPointers", "FlingGesture#direction"], "mappings": "AAA;OCI;ECG;GDI;EEM;GFG;EGS;GHG;CDC"}}, "type": "js/module"}]}