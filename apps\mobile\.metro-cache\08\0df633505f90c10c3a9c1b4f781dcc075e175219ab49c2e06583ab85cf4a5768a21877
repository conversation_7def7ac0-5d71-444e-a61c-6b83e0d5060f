{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 73, "index": 120}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useOnRouteFocus = useOnRouteFocus;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _NavigationBuilderContext = require(_dependencyMap[1], \"./NavigationBuilderContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Hook to handle focus actions for a route.\n   * Focus action needs to be treated specially, coz when a nested route is focused,\n   * the parent navigators also needs to be focused.\n   */\n  function useOnRouteFocus(_ref) {\n    var router = _ref.router,\n      getState = _ref.getState,\n      sourceRouteKey = _ref.key,\n      setState = _ref.setState;\n    var _React$useContext = React.useContext(_NavigationBuilderContext.NavigationBuilderContext),\n      onRouteFocusParent = _React$useContext.onRouteFocus;\n    return React.useCallback(key => {\n      var state = getState();\n      var result = router.getStateForRouteFocus(state, key);\n      if (result !== state) {\n        setState(result);\n      }\n      if (onRouteFocusParent !== undefined && sourceRouteKey !== undefined) {\n        onRouteFocusParent(sourceRouteKey);\n      }\n    }, [getState, onRouteFocusParent, router, setState, sourceRouteKey]);\n  }\n});", "lineCount": 34, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useOnRouteFocus"], [7, 25, 1, 13], [7, 28, 1, 13, "useOnRouteFocus"], [7, 43, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_NavigationBuilderContext"], [9, 31, 4, 0], [9, 34, 4, 0, "require"], [9, 41, 4, 0], [9, 42, 4, 0, "_dependencyMap"], [9, 56, 4, 0], [10, 2, 4, 73], [10, 11, 4, 73, "_interopRequireWildcard"], [10, 35, 4, 73, "e"], [10, 36, 4, 73], [10, 38, 4, 73, "t"], [10, 39, 4, 73], [10, 68, 4, 73, "WeakMap"], [10, 75, 4, 73], [10, 81, 4, 73, "r"], [10, 82, 4, 73], [10, 89, 4, 73, "WeakMap"], [10, 96, 4, 73], [10, 100, 4, 73, "n"], [10, 101, 4, 73], [10, 108, 4, 73, "WeakMap"], [10, 115, 4, 73], [10, 127, 4, 73, "_interopRequireWildcard"], [10, 150, 4, 73], [10, 162, 4, 73, "_interopRequireWildcard"], [10, 163, 4, 73, "e"], [10, 164, 4, 73], [10, 166, 4, 73, "t"], [10, 167, 4, 73], [10, 176, 4, 73, "t"], [10, 177, 4, 73], [10, 181, 4, 73, "e"], [10, 182, 4, 73], [10, 186, 4, 73, "e"], [10, 187, 4, 73], [10, 188, 4, 73, "__esModule"], [10, 198, 4, 73], [10, 207, 4, 73, "e"], [10, 208, 4, 73], [10, 214, 4, 73, "o"], [10, 215, 4, 73], [10, 217, 4, 73, "i"], [10, 218, 4, 73], [10, 220, 4, 73, "f"], [10, 221, 4, 73], [10, 226, 4, 73, "__proto__"], [10, 235, 4, 73], [10, 243, 4, 73, "default"], [10, 250, 4, 73], [10, 252, 4, 73, "e"], [10, 253, 4, 73], [10, 270, 4, 73, "e"], [10, 271, 4, 73], [10, 294, 4, 73, "e"], [10, 295, 4, 73], [10, 320, 4, 73, "e"], [10, 321, 4, 73], [10, 330, 4, 73, "f"], [10, 331, 4, 73], [10, 337, 4, 73, "o"], [10, 338, 4, 73], [10, 341, 4, 73, "t"], [10, 342, 4, 73], [10, 345, 4, 73, "n"], [10, 346, 4, 73], [10, 349, 4, 73, "r"], [10, 350, 4, 73], [10, 358, 4, 73, "o"], [10, 359, 4, 73], [10, 360, 4, 73, "has"], [10, 363, 4, 73], [10, 364, 4, 73, "e"], [10, 365, 4, 73], [10, 375, 4, 73, "o"], [10, 376, 4, 73], [10, 377, 4, 73, "get"], [10, 380, 4, 73], [10, 381, 4, 73, "e"], [10, 382, 4, 73], [10, 385, 4, 73, "o"], [10, 386, 4, 73], [10, 387, 4, 73, "set"], [10, 390, 4, 73], [10, 391, 4, 73, "e"], [10, 392, 4, 73], [10, 394, 4, 73, "f"], [10, 395, 4, 73], [10, 409, 4, 73, "_t"], [10, 411, 4, 73], [10, 415, 4, 73, "e"], [10, 416, 4, 73], [10, 432, 4, 73, "_t"], [10, 434, 4, 73], [10, 441, 4, 73, "hasOwnProperty"], [10, 455, 4, 73], [10, 456, 4, 73, "call"], [10, 460, 4, 73], [10, 461, 4, 73, "e"], [10, 462, 4, 73], [10, 464, 4, 73, "_t"], [10, 466, 4, 73], [10, 473, 4, 73, "i"], [10, 474, 4, 73], [10, 478, 4, 73, "o"], [10, 479, 4, 73], [10, 482, 4, 73, "Object"], [10, 488, 4, 73], [10, 489, 4, 73, "defineProperty"], [10, 503, 4, 73], [10, 508, 4, 73, "Object"], [10, 514, 4, 73], [10, 515, 4, 73, "getOwnPropertyDescriptor"], [10, 539, 4, 73], [10, 540, 4, 73, "e"], [10, 541, 4, 73], [10, 543, 4, 73, "_t"], [10, 545, 4, 73], [10, 552, 4, 73, "i"], [10, 553, 4, 73], [10, 554, 4, 73, "get"], [10, 557, 4, 73], [10, 561, 4, 73, "i"], [10, 562, 4, 73], [10, 563, 4, 73, "set"], [10, 566, 4, 73], [10, 570, 4, 73, "o"], [10, 571, 4, 73], [10, 572, 4, 73, "f"], [10, 573, 4, 73], [10, 575, 4, 73, "_t"], [10, 577, 4, 73], [10, 579, 4, 73, "i"], [10, 580, 4, 73], [10, 584, 4, 73, "f"], [10, 585, 4, 73], [10, 586, 4, 73, "_t"], [10, 588, 4, 73], [10, 592, 4, 73, "e"], [10, 593, 4, 73], [10, 594, 4, 73, "_t"], [10, 596, 4, 73], [10, 607, 4, 73, "f"], [10, 608, 4, 73], [10, 613, 4, 73, "e"], [10, 614, 4, 73], [10, 616, 4, 73, "t"], [10, 617, 4, 73], [11, 2, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 2, 10, 7], [16, 11, 10, 16, "useOnRouteFocus"], [16, 26, 10, 31, "useOnRouteFocus"], [16, 27, 10, 31, "_ref"], [16, 31, 10, 31], [16, 33, 15, 3], [17, 4, 15, 3], [17, 8, 11, 2, "router"], [17, 14, 11, 8], [17, 17, 11, 8, "_ref"], [17, 21, 11, 8], [17, 22, 11, 2, "router"], [17, 28, 11, 8], [18, 6, 12, 2, "getState"], [18, 14, 12, 10], [18, 17, 12, 10, "_ref"], [18, 21, 12, 10], [18, 22, 12, 2, "getState"], [18, 30, 12, 10], [19, 6, 13, 7, "sourceRouteKey"], [19, 20, 13, 21], [19, 23, 13, 21, "_ref"], [19, 27, 13, 21], [19, 28, 13, 2, "key"], [19, 31, 13, 5], [20, 6, 14, 2, "setState"], [20, 14, 14, 10], [20, 17, 14, 10, "_ref"], [20, 21, 14, 10], [20, 22, 14, 2, "setState"], [20, 30, 14, 10], [21, 4, 16, 2], [21, 8, 16, 2, "_React$useContext"], [21, 25, 16, 2], [21, 28, 18, 6, "React"], [21, 33, 18, 11], [21, 34, 18, 12, "useContext"], [21, 44, 18, 22], [21, 45, 18, 23, "NavigationBuilderContext"], [21, 95, 18, 47], [21, 96, 18, 48], [22, 6, 17, 18, "onRouteFocusParent"], [22, 24, 17, 36], [22, 27, 17, 36, "_React$useContext"], [22, 44, 17, 36], [22, 45, 17, 4, "onRouteFocus"], [22, 57, 17, 16], [23, 4, 19, 2], [23, 11, 19, 9, "React"], [23, 16, 19, 14], [23, 17, 19, 15, "useCallback"], [23, 28, 19, 26], [23, 29, 19, 27, "key"], [23, 32, 19, 30], [23, 36, 19, 34], [24, 6, 20, 4], [24, 10, 20, 10, "state"], [24, 15, 20, 15], [24, 18, 20, 18, "getState"], [24, 26, 20, 26], [24, 27, 20, 27], [24, 28, 20, 28], [25, 6, 21, 4], [25, 10, 21, 10, "result"], [25, 16, 21, 16], [25, 19, 21, 19, "router"], [25, 25, 21, 25], [25, 26, 21, 26, "getStateForRouteFocus"], [25, 47, 21, 47], [25, 48, 21, 48, "state"], [25, 53, 21, 53], [25, 55, 21, 55, "key"], [25, 58, 21, 58], [25, 59, 21, 59], [26, 6, 22, 4], [26, 10, 22, 8, "result"], [26, 16, 22, 14], [26, 21, 22, 19, "state"], [26, 26, 22, 24], [26, 28, 22, 26], [27, 8, 23, 6, "setState"], [27, 16, 23, 14], [27, 17, 23, 15, "result"], [27, 23, 23, 21], [27, 24, 23, 22], [28, 6, 24, 4], [29, 6, 25, 4], [29, 10, 25, 8, "onRouteFocusParent"], [29, 28, 25, 26], [29, 33, 25, 31, "undefined"], [29, 42, 25, 40], [29, 46, 25, 44, "sourceRouteKey"], [29, 60, 25, 58], [29, 65, 25, 63, "undefined"], [29, 74, 25, 72], [29, 76, 25, 74], [30, 8, 26, 6, "onRouteFocusParent"], [30, 26, 26, 24], [30, 27, 26, 25, "sourceRouteKey"], [30, 41, 26, 39], [30, 42, 26, 40], [31, 6, 27, 4], [32, 4, 28, 2], [32, 5, 28, 3], [32, 7, 28, 5], [32, 8, 28, 6, "getState"], [32, 16, 28, 14], [32, 18, 28, 16, "onRouteFocusParent"], [32, 36, 28, 34], [32, 38, 28, 36, "router"], [32, 44, 28, 42], [32, 46, 28, 44, "setState"], [32, 54, 28, 52], [32, 56, 28, 54, "sourceRouteKey"], [32, 70, 28, 68], [32, 71, 28, 69], [32, 72, 28, 70], [33, 2, 29, 0], [34, 0, 29, 1], [34, 3]], "functionMap": {"names": ["<global>", "useOnRouteFocus", "<anonymous>"], "mappings": "AAA;OCS;2BCS;GDS;CDC"}}, "type": "js/module"}]}