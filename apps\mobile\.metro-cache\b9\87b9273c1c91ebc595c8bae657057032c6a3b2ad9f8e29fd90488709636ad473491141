{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 46, "index": 61}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 62}, "end": {"line": 4, "column": 26, "index": 88}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 138}, "end": {"line": 6, "column": 68, "index": 206}}], "key": "OSA8xsmyvVLjxZOJ/QFvle2ua2I=", "exportNames": ["*"]}}, {"name": "../fabric/ScreenContainerNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 229}, "end": {"line": 9, "column": 86, "index": 315}}], "key": "yshf/Z+GfITEiE16ZD+ZnRuiVlU=", "exportNames": ["*"]}}, {"name": "../fabric/ScreenNavigationContainerNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 316}, "end": {"line": 10, "column": 106, "index": 422}}], "key": "3lS6CE9ixu85ZuPEZeakHI/0Jrs=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _react = _interopRequireDefault(require(_dependencyMap[3], \"react\"));\n  var _core = require(_dependencyMap[4], \"../core\");\n  var _ScreenContainerNativeComponent = _interopRequireDefault(require(_dependencyMap[5], \"../fabric/ScreenContainerNativeComponent\"));\n  var _ScreenNavigationContainerNativeComponent = _interopRequireDefault(require(_dependencyMap[6], \"../fabric/ScreenNavigationContainerNativeComponent\"));\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _excluded = [\"enabled\", \"hasTwoStates\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-screens\\\\src\\\\components\\\\ScreenContainer.tsx\"; // Native components\n  function ScreenContainer(props) {\n    var _props$enabled = props.enabled,\n      enabled = _props$enabled === void 0 ? (0, _core.screensEnabled)() : _props$enabled,\n      hasTwoStates = props.hasTwoStates,\n      rest = (0, _objectWithoutProperties2.default)(props, _excluded);\n    if (enabled && _core.isNativePlatformSupported) {\n      if (hasTwoStates) {\n        var ScreenNavigationContainer = _reactNative.Platform.OS === 'ios' ? _ScreenNavigationContainerNativeComponent.default : _ScreenContainerNativeComponent.default;\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ScreenNavigationContainer, {\n          ...rest\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 14\n        }, this);\n      }\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScreenContainerNativeComponent.default, {\n        ...rest\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 12\n      }, this);\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      ...rest\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 10\n    }, this);\n  }\n  var _default = exports.default = ScreenContainer;\n});", "lineCount": 51, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_reactNative"], [10, 18, 3, 0], [10, 21, 3, 0, "require"], [10, 28, 3, 0], [10, 29, 3, 0, "_dependencyMap"], [10, 43, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_react"], [11, 12, 4, 0], [11, 15, 4, 0, "_interopRequireDefault"], [11, 37, 4, 0], [11, 38, 4, 0, "require"], [11, 45, 4, 0], [11, 46, 4, 0, "_dependencyMap"], [11, 60, 4, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_core"], [12, 11, 6, 0], [12, 14, 6, 0, "require"], [12, 21, 6, 0], [12, 22, 6, 0, "_dependencyMap"], [12, 36, 6, 0], [13, 2, 9, 0], [13, 6, 9, 0, "_ScreenContainerNativeComponent"], [13, 37, 9, 0], [13, 40, 9, 0, "_interopRequireDefault"], [13, 62, 9, 0], [13, 63, 9, 0, "require"], [13, 70, 9, 0], [13, 71, 9, 0, "_dependencyMap"], [13, 85, 9, 0], [14, 2, 10, 0], [14, 6, 10, 0, "_ScreenNavigationContainerNativeComponent"], [14, 47, 10, 0], [14, 50, 10, 0, "_interopRequireDefault"], [14, 72, 10, 0], [14, 73, 10, 0, "require"], [14, 80, 10, 0], [14, 81, 10, 0, "_dependencyMap"], [14, 95, 10, 0], [15, 2, 10, 106], [15, 6, 10, 106, "_jsxDevRuntime"], [15, 20, 10, 106], [15, 23, 10, 106, "require"], [15, 30, 10, 106], [15, 31, 10, 106, "_dependencyMap"], [15, 45, 10, 106], [16, 2, 10, 106], [16, 6, 10, 106, "_excluded"], [16, 15, 10, 106], [17, 2, 10, 106], [17, 6, 10, 106, "_jsxFileName"], [17, 18, 10, 106], [17, 131, 8, 0], [18, 2, 12, 0], [18, 11, 12, 9, "ScreenContainer"], [18, 26, 12, 24, "ScreenContainer"], [18, 27, 12, 25, "props"], [18, 32, 12, 52], [18, 34, 12, 54], [19, 4, 13, 2], [19, 8, 13, 2, "_props$enabled"], [19, 22, 13, 2], [19, 25, 13, 64, "props"], [19, 30, 13, 69], [19, 31, 13, 10, "enabled"], [19, 38, 13, 17], [20, 6, 13, 10, "enabled"], [20, 13, 13, 17], [20, 16, 13, 17, "_props$enabled"], [20, 30, 13, 17], [20, 44, 13, 20], [20, 48, 13, 20, "screensEnabled"], [20, 68, 13, 34], [20, 70, 13, 35], [20, 71, 13, 36], [20, 74, 13, 36, "_props$enabled"], [20, 88, 13, 36], [21, 6, 13, 38, "hasTwoStates"], [21, 18, 13, 50], [21, 21, 13, 64, "props"], [21, 26, 13, 69], [21, 27, 13, 38, "hasTwoStates"], [21, 39, 13, 50], [22, 6, 13, 55, "rest"], [22, 10, 13, 59], [22, 17, 13, 59, "_objectWithoutProperties2"], [22, 42, 13, 59], [22, 43, 13, 59, "default"], [22, 50, 13, 59], [22, 52, 13, 64, "props"], [22, 57, 13, 69], [22, 59, 13, 69, "_excluded"], [22, 68, 13, 69], [23, 4, 15, 2], [23, 8, 15, 6, "enabled"], [23, 15, 15, 13], [23, 19, 15, 17, "isNativePlatformSupported"], [23, 50, 15, 42], [23, 52, 15, 44], [24, 6, 16, 4], [24, 10, 16, 8, "hasTwoStates"], [24, 22, 16, 20], [24, 24, 16, 22], [25, 8, 17, 6], [25, 12, 17, 12, "ScreenNavigationContainer"], [25, 37, 17, 37], [25, 40, 18, 8, "Platform"], [25, 61, 18, 16], [25, 62, 18, 17, "OS"], [25, 64, 18, 19], [25, 69, 18, 24], [25, 74, 18, 29], [25, 77, 19, 12, "ScreenNavigationContainerNativeComponent"], [25, 126, 19, 52], [25, 129, 20, 12, "ScreenContainerNativeComponent"], [25, 168, 20, 42], [26, 8, 21, 6], [26, 28, 21, 13], [26, 32, 21, 13, "_jsxDevRuntime"], [26, 46, 21, 13], [26, 47, 21, 13, "jsxDEV"], [26, 53, 21, 13], [26, 55, 21, 14, "ScreenNavigationContainer"], [26, 80, 21, 39], [27, 10, 21, 39], [27, 13, 21, 44, "rest"], [28, 8, 21, 48], [29, 10, 21, 48, "fileName"], [29, 18, 21, 48], [29, 20, 21, 48, "_jsxFileName"], [29, 32, 21, 48], [30, 10, 21, 48, "lineNumber"], [30, 20, 21, 48], [31, 10, 21, 48, "columnNumber"], [31, 22, 21, 48], [32, 8, 21, 48], [32, 15, 21, 51], [32, 16, 21, 52], [33, 6, 22, 4], [34, 6, 23, 4], [34, 26, 23, 11], [34, 30, 23, 11, "_jsxDevRuntime"], [34, 44, 23, 11], [34, 45, 23, 11, "jsxDEV"], [34, 51, 23, 11], [34, 53, 23, 12, "_ScreenContainerNativeComponent"], [34, 84, 23, 12], [34, 85, 23, 12, "default"], [34, 92, 23, 42], [35, 8, 23, 42], [35, 11, 23, 47, "rest"], [36, 6, 23, 51], [37, 8, 23, 51, "fileName"], [37, 16, 23, 51], [37, 18, 23, 51, "_jsxFileName"], [37, 30, 23, 51], [38, 8, 23, 51, "lineNumber"], [38, 18, 23, 51], [39, 8, 23, 51, "columnNumber"], [39, 20, 23, 51], [40, 6, 23, 51], [40, 13, 23, 54], [40, 14, 23, 55], [41, 4, 24, 2], [42, 4, 25, 2], [42, 24, 25, 9], [42, 28, 25, 9, "_jsxDevRuntime"], [42, 42, 25, 9], [42, 43, 25, 9, "jsxDEV"], [42, 49, 25, 9], [42, 51, 25, 10, "_reactNative"], [42, 63, 25, 10], [42, 64, 25, 10, "View"], [42, 68, 25, 14], [43, 6, 25, 14], [43, 9, 25, 19, "rest"], [44, 4, 25, 23], [45, 6, 25, 23, "fileName"], [45, 14, 25, 23], [45, 16, 25, 23, "_jsxFileName"], [45, 28, 25, 23], [46, 6, 25, 23, "lineNumber"], [46, 16, 25, 23], [47, 6, 25, 23, "columnNumber"], [47, 18, 25, 23], [48, 4, 25, 23], [48, 11, 25, 26], [48, 12, 25, 27], [49, 2, 26, 0], [50, 2, 26, 1], [50, 6, 26, 1, "_default"], [50, 14, 26, 1], [50, 17, 26, 1, "exports"], [50, 24, 26, 1], [50, 25, 26, 1, "default"], [50, 32, 26, 1], [50, 35, 28, 15, "ScreenContainer"], [50, 50, 28, 30], [51, 0, 28, 30], [51, 3]], "functionMap": {"names": ["<global>", "ScreenContainer"], "mappings": "AAA;ACW;CDc"}}, "type": "js/module"}]}