{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ghQueueMicrotask = void 0;\n  // We check for typeof requestAnimationFrame because of SSR\n  // Functions are bound to null to avoid issues with scope when using Metro inline requires.\n  var ghQueueMicrotask = exports.ghQueueMicrotask = typeof setImmediate === 'function' ? setImmediate.bind(null) : typeof requestAnimationFrame === 'function' ? requestAnimationFrame.bind(null) : queueMicrotask.bind(null);\n});", "lineCount": 9, "map": [[6, 2, 1, 0], [7, 2, 2, 0], [8, 2, 3, 7], [8, 6, 3, 13, "ghQueueMicrotask"], [8, 22, 3, 29], [8, 25, 3, 29, "exports"], [8, 32, 3, 29], [8, 33, 3, 29, "ghQueueMicrotask"], [8, 49, 3, 29], [8, 52, 4, 2], [8, 59, 4, 9, "setImmediate"], [8, 71, 4, 21], [8, 76, 4, 26], [8, 86, 4, 36], [8, 89, 5, 6, "setImmediate"], [8, 101, 5, 18], [8, 102, 5, 19, "bind"], [8, 106, 5, 23], [8, 107, 5, 24], [8, 111, 5, 28], [8, 112, 5, 29], [8, 115, 6, 6], [8, 122, 6, 13, "requestAnimationFrame"], [8, 143, 6, 34], [8, 148, 6, 39], [8, 158, 6, 49], [8, 161, 7, 8, "requestAnimationFrame"], [8, 182, 7, 29], [8, 183, 7, 30, "bind"], [8, 187, 7, 34], [8, 188, 7, 35], [8, 192, 7, 39], [8, 193, 7, 40], [8, 196, 8, 8, "queueMicrotask"], [8, 210, 8, 22], [8, 211, 8, 23, "bind"], [8, 215, 8, 27], [8, 216, 8, 28], [8, 220, 8, 32], [8, 221, 8, 33], [9, 0, 8, 34], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}