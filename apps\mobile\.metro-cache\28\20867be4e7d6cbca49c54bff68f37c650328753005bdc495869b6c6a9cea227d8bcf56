{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 74, "index": 74}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SimultaneousGesture = exports.ExclusiveGesture = exports.ComposedGesture = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _gesture = require(_dependencyMap[6], \"./gesture\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function extendRelation(currentRelation, extendWith) {\n    if (currentRelation === undefined) {\n      return [...extendWith];\n    } else {\n      return [...currentRelation, ...extendWith];\n    }\n  }\n  var ComposedGesture = exports.ComposedGesture = /*#__PURE__*/function (_Gesture) {\n    function ComposedGesture() {\n      var _this;\n      (0, _classCallCheck2.default)(this, ComposedGesture);\n      _this = _callSuper(this, ComposedGesture);\n      _this.gestures = [];\n      _this.simultaneousGestures = [];\n      _this.requireGesturesToFail = [];\n      for (var _len = arguments.length, gestures = new Array(_len), _key = 0; _key < _len; _key++) {\n        gestures[_key] = arguments[_key];\n      }\n      _this.gestures = gestures;\n      return _this;\n    }\n    (0, _inherits2.default)(ComposedGesture, _Gesture);\n    return (0, _createClass2.default)(ComposedGesture, [{\n      key: \"prepareSingleGesture\",\n      value: function prepareSingleGesture(gesture, simultaneousGestures, requireGesturesToFail) {\n        if (gesture instanceof _gesture.BaseGesture) {\n          var newConfig = {\n            ...gesture.config\n          };\n\n          // No need to extend `blocksHandlers` here, because it's not changed in composition.\n          // The same effect is achieved by reversing the order of 2 gestures in `Exclusive`\n          newConfig.simultaneousWith = extendRelation(newConfig.simultaneousWith, simultaneousGestures);\n          newConfig.requireToFail = extendRelation(newConfig.requireToFail, requireGesturesToFail);\n          gesture.config = newConfig;\n        } else if (gesture instanceof ComposedGesture) {\n          gesture.simultaneousGestures = simultaneousGestures;\n          gesture.requireGesturesToFail = requireGesturesToFail;\n          gesture.prepare();\n        }\n      }\n    }, {\n      key: \"prepare\",\n      value: function prepare() {\n        for (var gesture of this.gestures) {\n          this.prepareSingleGesture(gesture, this.simultaneousGestures, this.requireGesturesToFail);\n        }\n      }\n    }, {\n      key: \"initialize\",\n      value: function initialize() {\n        for (var gesture of this.gestures) {\n          gesture.initialize();\n        }\n      }\n    }, {\n      key: \"toGestureArray\",\n      value: function toGestureArray() {\n        return this.gestures.flatMap(gesture => gesture.toGestureArray());\n      }\n    }]);\n  }(_gesture.Gesture);\n  var SimultaneousGesture = exports.SimultaneousGesture = /*#__PURE__*/function (_ComposedGesture2) {\n    function SimultaneousGesture() {\n      (0, _classCallCheck2.default)(this, SimultaneousGesture);\n      return _callSuper(this, SimultaneousGesture, arguments);\n    }\n    (0, _inherits2.default)(SimultaneousGesture, _ComposedGesture2);\n    return (0, _createClass2.default)(SimultaneousGesture, [{\n      key: \"prepare\",\n      value: function prepare() {\n        // This piece of magic works something like this:\n        // for every gesture in the array\n        var simultaneousArrays = this.gestures.map(gesture =>\n        // we take the array it's in\n        this.gestures\n        // and make a copy without it\n        .filter(x => x !== gesture)\n        // then we flatmap the result to get list of raw (not composed) gestures\n        // this way we don't make the gestures simultaneous with themselves, which is\n        // important when the gesture is `ExclusiveGesture` - we don't want to make\n        // exclusive gestures simultaneous\n        .flatMap(x => x.toGestureArray()));\n        for (var i = 0; i < this.gestures.length; i++) {\n          this.prepareSingleGesture(this.gestures[i], simultaneousArrays[i], this.requireGesturesToFail);\n        }\n      }\n    }]);\n  }(ComposedGesture);\n  var ExclusiveGesture = exports.ExclusiveGesture = /*#__PURE__*/function (_ComposedGesture3) {\n    function ExclusiveGesture() {\n      (0, _classCallCheck2.default)(this, ExclusiveGesture);\n      return _callSuper(this, ExclusiveGesture, arguments);\n    }\n    (0, _inherits2.default)(ExclusiveGesture, _ComposedGesture3);\n    return (0, _createClass2.default)(ExclusiveGesture, [{\n      key: \"prepare\",\n      value: function prepare() {\n        // Transforms the array of gestures into array of grouped raw (not composed) gestures\n        // i.e. [gesture1, gesture2, ComposedGesture(gesture3, gesture4)] -> [[gesture1], [gesture2], [gesture3, gesture4]]\n        var gestureArrays = this.gestures.map(gesture => gesture.toGestureArray());\n        var requireToFail = [];\n        for (var i = 0; i < this.gestures.length; i++) {\n          this.prepareSingleGesture(this.gestures[i], this.simultaneousGestures, this.requireGesturesToFail.concat(requireToFail));\n\n          // Every group gets to wait for all groups before it\n          requireToFail = requireToFail.concat(gestureArrays[i]);\n        }\n      }\n    }]);\n  }(ComposedGesture);\n});", "lineCount": 126, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_gesture"], [12, 14, 1, 0], [12, 17, 1, 0, "require"], [12, 24, 1, 0], [12, 25, 1, 0, "_dependencyMap"], [12, 39, 1, 0], [13, 2, 1, 74], [13, 11, 1, 74, "_callSuper"], [13, 22, 1, 74, "t"], [13, 23, 1, 74], [13, 25, 1, 74, "o"], [13, 26, 1, 74], [13, 28, 1, 74, "e"], [13, 29, 1, 74], [13, 40, 1, 74, "o"], [13, 41, 1, 74], [13, 48, 1, 74, "_getPrototypeOf2"], [13, 64, 1, 74], [13, 65, 1, 74, "default"], [13, 72, 1, 74], [13, 74, 1, 74, "o"], [13, 75, 1, 74], [13, 82, 1, 74, "_possibleConstructorReturn2"], [13, 109, 1, 74], [13, 110, 1, 74, "default"], [13, 117, 1, 74], [13, 119, 1, 74, "t"], [13, 120, 1, 74], [13, 122, 1, 74, "_isNativeReflectConstruct"], [13, 147, 1, 74], [13, 152, 1, 74, "Reflect"], [13, 159, 1, 74], [13, 160, 1, 74, "construct"], [13, 169, 1, 74], [13, 170, 1, 74, "o"], [13, 171, 1, 74], [13, 173, 1, 74, "e"], [13, 174, 1, 74], [13, 186, 1, 74, "_getPrototypeOf2"], [13, 202, 1, 74], [13, 203, 1, 74, "default"], [13, 210, 1, 74], [13, 212, 1, 74, "t"], [13, 213, 1, 74], [13, 215, 1, 74, "constructor"], [13, 226, 1, 74], [13, 230, 1, 74, "o"], [13, 231, 1, 74], [13, 232, 1, 74, "apply"], [13, 237, 1, 74], [13, 238, 1, 74, "t"], [13, 239, 1, 74], [13, 241, 1, 74, "e"], [13, 242, 1, 74], [14, 2, 1, 74], [14, 11, 1, 74, "_isNativeReflectConstruct"], [14, 37, 1, 74], [14, 51, 1, 74, "t"], [14, 52, 1, 74], [14, 56, 1, 74, "Boolean"], [14, 63, 1, 74], [14, 64, 1, 74, "prototype"], [14, 73, 1, 74], [14, 74, 1, 74, "valueOf"], [14, 81, 1, 74], [14, 82, 1, 74, "call"], [14, 86, 1, 74], [14, 87, 1, 74, "Reflect"], [14, 94, 1, 74], [14, 95, 1, 74, "construct"], [14, 104, 1, 74], [14, 105, 1, 74, "Boolean"], [14, 112, 1, 74], [14, 145, 1, 74, "t"], [14, 146, 1, 74], [14, 159, 1, 74, "_isNativeReflectConstruct"], [14, 184, 1, 74], [14, 196, 1, 74, "_isNativeReflectConstruct"], [14, 197, 1, 74], [14, 210, 1, 74, "t"], [14, 211, 1, 74], [15, 2, 3, 0], [15, 11, 3, 9, "extendRelation"], [15, 25, 3, 23, "extendRelation"], [15, 26, 4, 2, "currentRelation"], [15, 41, 4, 43], [15, 43, 5, 2, "extendWith"], [15, 53, 5, 27], [15, 55, 6, 2], [16, 4, 7, 2], [16, 8, 7, 6, "currentRelation"], [16, 23, 7, 21], [16, 28, 7, 26, "undefined"], [16, 37, 7, 35], [16, 39, 7, 37], [17, 6, 8, 4], [17, 13, 8, 11], [17, 14, 8, 12], [17, 17, 8, 15, "extendWith"], [17, 27, 8, 25], [17, 28, 8, 26], [18, 4, 9, 2], [18, 5, 9, 3], [18, 11, 9, 9], [19, 6, 10, 4], [19, 13, 10, 11], [19, 14, 10, 12], [19, 17, 10, 15, "currentRelation"], [19, 32, 10, 30], [19, 34, 10, 32], [19, 37, 10, 35, "extendWith"], [19, 47, 10, 45], [19, 48, 10, 46], [20, 4, 11, 2], [21, 2, 12, 0], [22, 2, 12, 1], [22, 6, 14, 13, "ComposedGesture"], [22, 21, 14, 28], [22, 24, 14, 28, "exports"], [22, 31, 14, 28], [22, 32, 14, 28, "ComposedGesture"], [22, 47, 14, 28], [22, 73, 14, 28, "_Gesture"], [22, 81, 14, 28], [23, 4, 19, 2], [23, 13, 19, 2, "ComposedGesture"], [23, 29, 19, 2], [23, 31, 19, 38], [24, 6, 19, 38], [24, 10, 19, 38, "_this"], [24, 15, 19, 38], [25, 6, 19, 38], [25, 10, 19, 38, "_classCallCheck2"], [25, 26, 19, 38], [25, 27, 19, 38, "default"], [25, 34, 19, 38], [25, 42, 19, 38, "ComposedGesture"], [25, 57, 19, 38], [26, 6, 20, 4, "_this"], [26, 11, 20, 4], [26, 14, 20, 4, "_callSuper"], [26, 24, 20, 4], [26, 31, 20, 4, "ComposedGesture"], [26, 46, 20, 4], [27, 6, 20, 12, "_this"], [27, 11, 20, 12], [27, 12, 15, 12, "gestures"], [27, 20, 15, 20], [27, 23, 15, 34], [27, 25, 15, 36], [28, 6, 15, 36, "_this"], [28, 11, 15, 36], [28, 12, 16, 12, "simultaneousGestures"], [28, 32, 16, 32], [28, 35, 16, 50], [28, 37, 16, 52], [29, 6, 16, 52, "_this"], [29, 11, 16, 52], [29, 12, 17, 12, "requireGesturesToFail"], [29, 33, 17, 33], [29, 36, 17, 51], [29, 38, 17, 53], [30, 6, 17, 53], [30, 15, 17, 53, "_len"], [30, 19, 17, 53], [30, 22, 17, 53, "arguments"], [30, 31, 17, 53], [30, 32, 17, 53, "length"], [30, 38, 17, 53], [30, 40, 19, 17, "gestures"], [30, 48, 19, 25], [30, 55, 19, 25, "Array"], [30, 60, 19, 25], [30, 61, 19, 25, "_len"], [30, 65, 19, 25], [30, 68, 19, 25, "_key"], [30, 72, 19, 25], [30, 78, 19, 25, "_key"], [30, 82, 19, 25], [30, 85, 19, 25, "_len"], [30, 89, 19, 25], [30, 91, 19, 25, "_key"], [30, 95, 19, 25], [31, 8, 19, 17, "gestures"], [31, 16, 19, 25], [31, 17, 19, 25, "_key"], [31, 21, 19, 25], [31, 25, 19, 25, "arguments"], [31, 34, 19, 25], [31, 35, 19, 25, "_key"], [31, 39, 19, 25], [32, 6, 19, 25], [33, 6, 21, 4, "_this"], [33, 11, 21, 4], [33, 12, 21, 9, "gestures"], [33, 20, 21, 17], [33, 23, 21, 20, "gestures"], [33, 31, 21, 28], [34, 6, 21, 29], [34, 13, 21, 29, "_this"], [34, 18, 21, 29], [35, 4, 22, 2], [36, 4, 22, 3], [36, 8, 22, 3, "_inherits2"], [36, 18, 22, 3], [36, 19, 22, 3, "default"], [36, 26, 22, 3], [36, 28, 22, 3, "ComposedGesture"], [36, 43, 22, 3], [36, 45, 22, 3, "_Gesture"], [36, 53, 22, 3], [37, 4, 22, 3], [37, 15, 22, 3, "_createClass2"], [37, 28, 22, 3], [37, 29, 22, 3, "default"], [37, 36, 22, 3], [37, 38, 22, 3, "ComposedGesture"], [37, 53, 22, 3], [38, 6, 22, 3, "key"], [38, 9, 22, 3], [39, 6, 22, 3, "value"], [39, 11, 22, 3], [39, 13, 24, 2], [39, 22, 24, 12, "prepareSingleGesture"], [39, 42, 24, 32, "prepareSingleGesture"], [39, 43, 25, 4, "gesture"], [39, 50, 25, 20], [39, 52, 26, 4, "simultaneousGestures"], [39, 72, 26, 39], [39, 74, 27, 4, "requireGesturesToFail"], [39, 95, 27, 40], [39, 97, 28, 4], [40, 8, 29, 4], [40, 12, 29, 8, "gesture"], [40, 19, 29, 15], [40, 31, 29, 27, "BaseGesture"], [40, 51, 29, 38], [40, 53, 29, 40], [41, 10, 30, 6], [41, 14, 30, 12, "newConfig"], [41, 23, 30, 21], [41, 26, 30, 24], [42, 12, 30, 26], [42, 15, 30, 29, "gesture"], [42, 22, 30, 36], [42, 23, 30, 37, "config"], [43, 10, 30, 44], [43, 11, 30, 45], [45, 10, 32, 6], [46, 10, 33, 6], [47, 10, 34, 6, "newConfig"], [47, 19, 34, 15], [47, 20, 34, 16, "simultaneousWith"], [47, 36, 34, 32], [47, 39, 34, 35, "extendRelation"], [47, 53, 34, 49], [47, 54, 35, 8, "newConfig"], [47, 63, 35, 17], [47, 64, 35, 18, "simultaneousWith"], [47, 80, 35, 34], [47, 82, 36, 8, "simultaneousGestures"], [47, 102, 37, 6], [47, 103, 37, 7], [48, 10, 38, 6, "newConfig"], [48, 19, 38, 15], [48, 20, 38, 16, "requireToFail"], [48, 33, 38, 29], [48, 36, 38, 32, "extendRelation"], [48, 50, 38, 46], [48, 51, 39, 8, "newConfig"], [48, 60, 39, 17], [48, 61, 39, 18, "requireToFail"], [48, 74, 39, 31], [48, 76, 40, 8, "requireGesturesToFail"], [48, 97, 41, 6], [48, 98, 41, 7], [49, 10, 43, 6, "gesture"], [49, 17, 43, 13], [49, 18, 43, 14, "config"], [49, 24, 43, 20], [49, 27, 43, 23, "newConfig"], [49, 36, 43, 32], [50, 8, 44, 4], [50, 9, 44, 5], [50, 15, 44, 11], [50, 19, 44, 15, "gesture"], [50, 26, 44, 22], [50, 38, 44, 34, "ComposedGesture"], [50, 53, 44, 49], [50, 55, 44, 51], [51, 10, 45, 6, "gesture"], [51, 17, 45, 13], [51, 18, 45, 14, "simultaneousGestures"], [51, 38, 45, 34], [51, 41, 45, 37, "simultaneousGestures"], [51, 61, 45, 57], [52, 10, 46, 6, "gesture"], [52, 17, 46, 13], [52, 18, 46, 14, "requireGesturesToFail"], [52, 39, 46, 35], [52, 42, 46, 38, "requireGesturesToFail"], [52, 63, 46, 59], [53, 10, 47, 6, "gesture"], [53, 17, 47, 13], [53, 18, 47, 14, "prepare"], [53, 25, 47, 21], [53, 26, 47, 22], [53, 27, 47, 23], [54, 8, 48, 4], [55, 6, 49, 2], [56, 4, 49, 3], [57, 6, 49, 3, "key"], [57, 9, 49, 3], [58, 6, 49, 3, "value"], [58, 11, 49, 3], [58, 13, 51, 2], [58, 22, 51, 2, "prepare"], [58, 29, 51, 9, "prepare"], [58, 30, 51, 9], [58, 32, 51, 12], [59, 8, 52, 4], [59, 13, 52, 9], [59, 17, 52, 15, "gesture"], [59, 24, 52, 22], [59, 28, 52, 26], [59, 32, 52, 30], [59, 33, 52, 31, "gestures"], [59, 41, 52, 39], [59, 43, 52, 41], [60, 10, 53, 6], [60, 14, 53, 10], [60, 15, 53, 11, "prepareSingleGesture"], [60, 35, 53, 31], [60, 36, 54, 8, "gesture"], [60, 43, 54, 15], [60, 45, 55, 8], [60, 49, 55, 12], [60, 50, 55, 13, "simultaneousGestures"], [60, 70, 55, 33], [60, 72, 56, 8], [60, 76, 56, 12], [60, 77, 56, 13, "requireGesturesToFail"], [60, 98, 57, 6], [60, 99, 57, 7], [61, 8, 58, 4], [62, 6, 59, 2], [63, 4, 59, 3], [64, 6, 59, 3, "key"], [64, 9, 59, 3], [65, 6, 59, 3, "value"], [65, 11, 59, 3], [65, 13, 61, 2], [65, 22, 61, 2, "initialize"], [65, 32, 61, 12, "initialize"], [65, 33, 61, 12], [65, 35, 61, 15], [66, 8, 62, 4], [66, 13, 62, 9], [66, 17, 62, 15, "gesture"], [66, 24, 62, 22], [66, 28, 62, 26], [66, 32, 62, 30], [66, 33, 62, 31, "gestures"], [66, 41, 62, 39], [66, 43, 62, 41], [67, 10, 63, 6, "gesture"], [67, 17, 63, 13], [67, 18, 63, 14, "initialize"], [67, 28, 63, 24], [67, 29, 63, 25], [67, 30, 63, 26], [68, 8, 64, 4], [69, 6, 65, 2], [70, 4, 65, 3], [71, 6, 65, 3, "key"], [71, 9, 65, 3], [72, 6, 65, 3, "value"], [72, 11, 65, 3], [72, 13, 67, 2], [72, 22, 67, 2, "toGestureArray"], [72, 36, 67, 16, "toGestureArray"], [72, 37, 67, 16], [72, 39, 67, 34], [73, 8, 68, 4], [73, 15, 68, 11], [73, 19, 68, 15], [73, 20, 68, 16, "gestures"], [73, 28, 68, 24], [73, 29, 68, 25, "flatMap"], [73, 36, 68, 32], [73, 37, 68, 34, "gesture"], [73, 44, 68, 41], [73, 48, 68, 46, "gesture"], [73, 55, 68, 53], [73, 56, 68, 54, "toGestureArray"], [73, 70, 68, 68], [73, 71, 68, 69], [73, 72, 68, 70], [73, 73, 68, 71], [74, 6, 69, 2], [75, 4, 69, 3], [76, 2, 69, 3], [76, 4, 14, 37, "Gesture"], [76, 20, 14, 44], [77, 2, 14, 44], [77, 6, 72, 13, "SimultaneousGesture"], [77, 25, 72, 32], [77, 28, 72, 32, "exports"], [77, 35, 72, 32], [77, 36, 72, 32, "SimultaneousGesture"], [77, 55, 72, 32], [77, 81, 72, 32, "_ComposedGesture2"], [77, 98, 72, 32], [78, 4, 72, 32], [78, 13, 72, 32, "SimultaneousGesture"], [78, 33, 72, 32], [79, 6, 72, 32], [79, 10, 72, 32, "_classCallCheck2"], [79, 26, 72, 32], [79, 27, 72, 32, "default"], [79, 34, 72, 32], [79, 42, 72, 32, "SimultaneousGesture"], [79, 61, 72, 32], [80, 6, 72, 32], [80, 13, 72, 32, "_callSuper"], [80, 23, 72, 32], [80, 30, 72, 32, "SimultaneousGesture"], [80, 49, 72, 32], [80, 51, 72, 32, "arguments"], [80, 60, 72, 32], [81, 4, 72, 32], [82, 4, 72, 32], [82, 8, 72, 32, "_inherits2"], [82, 18, 72, 32], [82, 19, 72, 32, "default"], [82, 26, 72, 32], [82, 28, 72, 32, "SimultaneousGesture"], [82, 47, 72, 32], [82, 49, 72, 32, "_ComposedGesture2"], [82, 66, 72, 32], [83, 4, 72, 32], [83, 15, 72, 32, "_createClass2"], [83, 28, 72, 32], [83, 29, 72, 32, "default"], [83, 36, 72, 32], [83, 38, 72, 32, "SimultaneousGesture"], [83, 57, 72, 32], [84, 6, 72, 32, "key"], [84, 9, 72, 32], [85, 6, 72, 32, "value"], [85, 11, 72, 32], [85, 13, 73, 2], [85, 22, 73, 2, "prepare"], [85, 29, 73, 9, "prepare"], [85, 30, 73, 9], [85, 32, 73, 12], [86, 8, 74, 4], [87, 8, 75, 4], [88, 8, 76, 4], [88, 12, 76, 10, "simultaneousArrays"], [88, 30, 76, 28], [88, 33, 76, 31], [88, 37, 76, 35], [88, 38, 76, 36, "gestures"], [88, 46, 76, 44], [88, 47, 76, 45, "map"], [88, 50, 76, 48], [88, 51, 76, 50, "gesture"], [88, 58, 76, 57], [89, 8, 77, 6], [90, 8, 78, 6], [90, 12, 78, 10], [90, 13, 78, 11, "gestures"], [91, 8, 79, 8], [92, 8, 79, 8], [92, 9, 80, 9, "filter"], [92, 15, 80, 15], [92, 16, 80, 17, "x"], [92, 17, 80, 18], [92, 21, 80, 23, "x"], [92, 22, 80, 24], [92, 27, 80, 29, "gesture"], [92, 34, 80, 36], [93, 8, 81, 8], [94, 8, 82, 8], [95, 8, 83, 8], [96, 8, 84, 8], [97, 8, 84, 8], [97, 9, 85, 9, "flatMap"], [97, 16, 85, 16], [97, 17, 85, 18, "x"], [97, 18, 85, 19], [97, 22, 85, 24, "x"], [97, 23, 85, 25], [97, 24, 85, 26, "toGestureArray"], [97, 38, 85, 40], [97, 39, 85, 41], [97, 40, 85, 42], [97, 41, 86, 4], [97, 42, 86, 5], [98, 8, 88, 4], [98, 13, 88, 9], [98, 17, 88, 13, "i"], [98, 18, 88, 14], [98, 21, 88, 17], [98, 22, 88, 18], [98, 24, 88, 20, "i"], [98, 25, 88, 21], [98, 28, 88, 24], [98, 32, 88, 28], [98, 33, 88, 29, "gestures"], [98, 41, 88, 37], [98, 42, 88, 38, "length"], [98, 48, 88, 44], [98, 50, 88, 46, "i"], [98, 51, 88, 47], [98, 53, 88, 49], [98, 55, 88, 51], [99, 10, 89, 6], [99, 14, 89, 10], [99, 15, 89, 11, "prepareSingleGesture"], [99, 35, 89, 31], [99, 36, 90, 8], [99, 40, 90, 12], [99, 41, 90, 13, "gestures"], [99, 49, 90, 21], [99, 50, 90, 22, "i"], [99, 51, 90, 23], [99, 52, 90, 24], [99, 54, 91, 8, "simultaneousArrays"], [99, 72, 91, 26], [99, 73, 91, 27, "i"], [99, 74, 91, 28], [99, 75, 91, 29], [99, 77, 92, 8], [99, 81, 92, 12], [99, 82, 92, 13, "requireGesturesToFail"], [99, 103, 93, 6], [99, 104, 93, 7], [100, 8, 94, 4], [101, 6, 95, 2], [102, 4, 95, 3], [103, 2, 95, 3], [103, 4, 72, 41, "ComposedGesture"], [103, 19, 72, 56], [104, 2, 72, 56], [104, 6, 98, 13, "ExclusiveGesture"], [104, 22, 98, 29], [104, 25, 98, 29, "exports"], [104, 32, 98, 29], [104, 33, 98, 29, "ExclusiveGesture"], [104, 49, 98, 29], [104, 75, 98, 29, "_ComposedGesture3"], [104, 92, 98, 29], [105, 4, 98, 29], [105, 13, 98, 29, "ExclusiveGesture"], [105, 30, 98, 29], [106, 6, 98, 29], [106, 10, 98, 29, "_classCallCheck2"], [106, 26, 98, 29], [106, 27, 98, 29, "default"], [106, 34, 98, 29], [106, 42, 98, 29, "ExclusiveGesture"], [106, 58, 98, 29], [107, 6, 98, 29], [107, 13, 98, 29, "_callSuper"], [107, 23, 98, 29], [107, 30, 98, 29, "ExclusiveGesture"], [107, 46, 98, 29], [107, 48, 98, 29, "arguments"], [107, 57, 98, 29], [108, 4, 98, 29], [109, 4, 98, 29], [109, 8, 98, 29, "_inherits2"], [109, 18, 98, 29], [109, 19, 98, 29, "default"], [109, 26, 98, 29], [109, 28, 98, 29, "ExclusiveGesture"], [109, 44, 98, 29], [109, 46, 98, 29, "_ComposedGesture3"], [109, 63, 98, 29], [110, 4, 98, 29], [110, 15, 98, 29, "_createClass2"], [110, 28, 98, 29], [110, 29, 98, 29, "default"], [110, 36, 98, 29], [110, 38, 98, 29, "ExclusiveGesture"], [110, 54, 98, 29], [111, 6, 98, 29, "key"], [111, 9, 98, 29], [112, 6, 98, 29, "value"], [112, 11, 98, 29], [112, 13, 99, 2], [112, 22, 99, 2, "prepare"], [112, 29, 99, 9, "prepare"], [112, 30, 99, 9], [112, 32, 99, 12], [113, 8, 100, 4], [114, 8, 101, 4], [115, 8, 102, 4], [115, 12, 102, 10, "gestureArrays"], [115, 25, 102, 23], [115, 28, 102, 26], [115, 32, 102, 30], [115, 33, 102, 31, "gestures"], [115, 41, 102, 39], [115, 42, 102, 40, "map"], [115, 45, 102, 43], [115, 46, 102, 45, "gesture"], [115, 53, 102, 52], [115, 57, 103, 6, "gesture"], [115, 64, 103, 13], [115, 65, 103, 14, "toGestureArray"], [115, 79, 103, 28], [115, 80, 103, 29], [115, 81, 104, 4], [115, 82, 104, 5], [116, 8, 106, 4], [116, 12, 106, 8, "requireToFail"], [116, 25, 106, 36], [116, 28, 106, 39], [116, 30, 106, 41], [117, 8, 108, 4], [117, 13, 108, 9], [117, 17, 108, 13, "i"], [117, 18, 108, 14], [117, 21, 108, 17], [117, 22, 108, 18], [117, 24, 108, 20, "i"], [117, 25, 108, 21], [117, 28, 108, 24], [117, 32, 108, 28], [117, 33, 108, 29, "gestures"], [117, 41, 108, 37], [117, 42, 108, 38, "length"], [117, 48, 108, 44], [117, 50, 108, 46, "i"], [117, 51, 108, 47], [117, 53, 108, 49], [117, 55, 108, 51], [118, 10, 109, 6], [118, 14, 109, 10], [118, 15, 109, 11, "prepareSingleGesture"], [118, 35, 109, 31], [118, 36, 110, 8], [118, 40, 110, 12], [118, 41, 110, 13, "gestures"], [118, 49, 110, 21], [118, 50, 110, 22, "i"], [118, 51, 110, 23], [118, 52, 110, 24], [118, 54, 111, 8], [118, 58, 111, 12], [118, 59, 111, 13, "simultaneousGestures"], [118, 79, 111, 33], [118, 81, 112, 8], [118, 85, 112, 12], [118, 86, 112, 13, "requireGesturesToFail"], [118, 107, 112, 34], [118, 108, 112, 35, "concat"], [118, 114, 112, 41], [118, 115, 112, 42, "requireToFail"], [118, 128, 112, 55], [118, 129, 113, 6], [118, 130, 113, 7], [120, 10, 115, 6], [121, 10, 116, 6, "requireToFail"], [121, 23, 116, 19], [121, 26, 116, 22, "requireToFail"], [121, 39, 116, 35], [121, 40, 116, 36, "concat"], [121, 46, 116, 42], [121, 47, 116, 43, "gestureArrays"], [121, 60, 116, 56], [121, 61, 116, 57, "i"], [121, 62, 116, 58], [121, 63, 116, 59], [121, 64, 116, 60], [122, 8, 117, 4], [123, 6, 118, 2], [124, 4, 118, 3], [125, 2, 118, 3], [125, 4, 98, 38, "ComposedGesture"], [125, 19, 98, 53], [126, 0, 98, 53], [126, 3]], "functionMap": {"names": ["<global>", "extendRelation", "ComposedGesture", "ComposedGesture#constructor", "ComposedGesture#prepareSingleGesture", "ComposedGesture#prepare", "ComposedGesture#initialize", "ComposedGesture#toGestureArray", "gestures.flatMap$argument_0", "SimultaneousGesture", "SimultaneousGesture#prepare", "gestures.map$argument_0", "gestures.filter$argument_0", "gestures.filter.flatMap$argument_0", "ExclusiveGesture", "ExclusiveGesture#prepare"], "mappings": "AAA;ACE;CDS;OEE;ECK;GDG;EEE;GFyB;EGE;GHQ;EIE;GJI;EKE;iCCC,qCD;GLC;CFC;OSE;ECC;iDCG;gBCI,oBD;iBEK,yBF,CD;GDU;CTC;OcE;ECC;4CJG;8BIC;GDe;CdC"}}, "type": "js/module"}]}