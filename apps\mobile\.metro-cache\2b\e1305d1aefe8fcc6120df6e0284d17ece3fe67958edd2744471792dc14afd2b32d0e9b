{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "./errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 291}, "end": {"line": 12, "column": 43, "index": 334}}], "key": "rEld05quROH+iA6QLT6kkvqJ/qc=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 391}, "end": {"line": 14, "column": 43, "index": 434}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /* eslint-disable @typescript-eslint/no-namespace */\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withReanimatedTimer = exports.setUpTests = exports.getAnimatedStyle = exports.advanceAnimationByTime = exports.advanceAnimationByFrame = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _errors = require(_dependencyMap[2], \"./errors\");\n  var _PlatformChecker = require(_dependencyMap[3], \"./PlatformChecker\");\n  var defaultFramerateConfig = {\n    fps: 60\n  };\n  var isEmpty = obj => !obj || Object.keys(obj).length === 0;\n  var getStylesFromObject = obj => {\n    return obj === undefined ? {} : Object.fromEntries(Object.entries(obj).map(_ref => {\n      var _ref2 = (0, _slicedToArray2.default)(_ref, 2),\n        property = _ref2[0],\n        value = _ref2[1];\n      return [property, value._isReanimatedSharedValue ? value.value : value];\n    }));\n  };\n  var getCurrentProps = component => {\n    var propsObject = component.props.jestAnimatedProps?.value;\n    return propsObject ? {\n      ...propsObject\n    } : {};\n  };\n  var getCurrentStyle = component => {\n    var styleObject = component.props.style;\n    var currentStyle = {};\n    if (Array.isArray(styleObject)) {\n      // It is possible that style may contain nested arrays. Currently, neither `StyleSheet.flatten` nor `flattenArray` solve this issue.\n      // Hence, we're not handling nested arrays at the moment - this is a known limitation of the current implementation.\n      styleObject.forEach(style => {\n        currentStyle = {\n          ...currentStyle,\n          ...style\n        };\n      });\n    }\n    var jestInlineStyles = component.props.jestInlineStyle;\n    var jestAnimatedStyleValue = component.props.jestAnimatedStyle?.value;\n    if (Array.isArray(jestInlineStyles)) {\n      for (var obj of jestInlineStyles) {\n        if ('jestAnimatedValues' in obj) {\n          continue;\n        }\n        var _inlineStyles = getStylesFromObject(obj);\n        currentStyle = {\n          ...currentStyle,\n          ..._inlineStyles\n        };\n      }\n      currentStyle = {\n        ...currentStyle,\n        ...jestAnimatedStyleValue\n      };\n      return currentStyle;\n    }\n    var inlineStyles = getStylesFromObject(jestInlineStyles);\n    currentStyle = isEmpty(jestAnimatedStyleValue) ? {\n      ...inlineStyles\n    } : {\n      ...jestAnimatedStyleValue\n    };\n    return currentStyle;\n  };\n  var checkEqual = (current, expected) => {\n    if (Array.isArray(expected)) {\n      if (!Array.isArray(current) || expected.length !== current.length) {\n        return false;\n      }\n      for (var i = 0; i < current.length; i++) {\n        if (!checkEqual(current[i], expected[i])) {\n          return false;\n        }\n      }\n    } else if (typeof current === 'object' && current) {\n      if (typeof expected !== 'object' || !expected) {\n        return false;\n      }\n      for (var property in expected) {\n        if (!checkEqual(current[property], expected[property])) {\n          return false;\n        }\n      }\n    } else {\n      return current === expected;\n    }\n    return true;\n  };\n  var findStyleDiff = (current, expected, shouldMatchAllProps) => {\n    var diffs = [];\n    var isEqual = true;\n    var property;\n    for (property in expected) {\n      if (!checkEqual(current[property], expected[property])) {\n        isEqual = false;\n        diffs.push({\n          property,\n          current: current[property],\n          expect: expected[property]\n        });\n      }\n    }\n    if (shouldMatchAllProps && Object.keys(current).length !== Object.keys(expected).length) {\n      isEqual = false;\n      // eslint-disable-next-line @typescript-eslint/no-shadow\n      var _property;\n      for (_property in current) {\n        if (expected[_property] === undefined) {\n          diffs.push({\n            property: _property,\n            current: current[_property],\n            expect: expected[_property]\n          });\n        }\n      }\n    }\n    return {\n      isEqual,\n      diffs\n    };\n  };\n  var compareAndFormatDifferences = function (currentValues, expectedValues) {\n    var shouldMatchAllProps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var _findStyleDiff = findStyleDiff(currentValues, expectedValues, shouldMatchAllProps),\n      isEqual = _findStyleDiff.isEqual,\n      diffs = _findStyleDiff.diffs;\n    if (isEqual) {\n      return {\n        message: () => 'ok',\n        pass: true\n      };\n    }\n    var currentValuesStr = JSON.stringify(currentValues);\n    var expectedValuesStr = JSON.stringify(expectedValues);\n    var differences = diffs.map(diff => `- '${diff.property}' should be ${JSON.stringify(diff.expect)}, but is ${JSON.stringify(diff.current)}`).join('\\n');\n    return {\n      message: () => `Expected: ${expectedValuesStr}\\nReceived: ${currentValuesStr}\\n\\nDifferences:\\n${differences}`,\n      pass: false\n    };\n  };\n  var compareProps = (component, expectedProps) => {\n    if (component.props.jestAnimatedProps && Object.keys(component.props.jestAnimatedProps.value).length === 0) {\n      return {\n        message: () => `Component doesn't have props.`,\n        pass: false\n      };\n    }\n    var currentProps = getCurrentProps(component);\n    return compareAndFormatDifferences(currentProps, expectedProps);\n  };\n  var compareStyle = (component, expectedStyle, config) => {\n    if (!component.props.style) {\n      return {\n        message: () => `Component doesn't have a style.`,\n        pass: false\n      };\n    }\n    var shouldMatchAllProps = config.shouldMatchAllProps;\n    var currentStyle = getCurrentStyle(component);\n    return compareAndFormatDifferences(currentStyle, expectedStyle, shouldMatchAllProps);\n  };\n  var frameTime = Math.round(1000 / defaultFramerateConfig.fps);\n  var beforeTest = () => {\n    jest.useFakeTimers();\n  };\n  var afterTest = () => {\n    jest.runOnlyPendingTimers();\n    jest.useRealTimers();\n  };\n  var withReanimatedTimer = animationTest => {\n    console.warn('This method is deprecated, you should define your own before and after test hooks to enable jest.useFakeTimers(). Check out the documentation for details on testing');\n    beforeTest();\n    animationTest();\n    afterTest();\n  };\n  exports.withReanimatedTimer = withReanimatedTimer;\n  var advanceAnimationByTime = function () {\n    var time = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : frameTime;\n    console.warn('This method is deprecated, use jest.advanceTimersByTime directly');\n    jest.advanceTimersByTime(time);\n    jest.runOnlyPendingTimers();\n  };\n  exports.advanceAnimationByTime = advanceAnimationByTime;\n  var advanceAnimationByFrame = count => {\n    console.warn('This method is deprecated, use jest.advanceTimersByTime directly');\n    jest.advanceTimersByTime(count * frameTime);\n    jest.runOnlyPendingTimers();\n  };\n  exports.advanceAnimationByFrame = advanceAnimationByFrame;\n  var requireFunction = (0, _PlatformChecker.isJest)() ? require : () => {\n    throw new _errors.ReanimatedError('`setUpTests` is available only in Jest environment.');\n  };\n  var setUpTests = function () {\n    var userFramerateConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var expect = global.expect;\n    if (expect === undefined) {\n      var expectModule = requireFunction('expect');\n      expect = expectModule;\n      // Starting from Jest 28, \"expect\" package uses named exports instead of default export.\n      // So, requiring \"expect\" package doesn't give direct access to \"expect\" function anymore.\n      // It gives access to the module object instead.\n      // We use this info to detect if the project uses Jest 28 or higher.\n      if (typeof expect === 'object') {\n        var jestGlobals = requireFunction('@jest/globals');\n        expect = jestGlobals.expect;\n      }\n      if (expect === undefined || expect.extend === undefined) {\n        expect = expectModule.default;\n      }\n    }\n    var framerateConfig = {\n      ...defaultFramerateConfig,\n      ...userFramerateConfig\n    };\n    frameTime = Math.round(1000 / framerateConfig.fps);\n    expect.extend({\n      toHaveAnimatedProps(component, expectedProps) {\n        return compareProps(component, expectedProps);\n      }\n    });\n    expect.extend({\n      toHaveAnimatedStyle(component, expectedStyle) {\n        var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n        return compareStyle(component, expectedStyle, config);\n      }\n    });\n  };\n  exports.setUpTests = setUpTests;\n  var getAnimatedStyle = component => {\n    return getCurrentStyle(\n    // This type assertion is needed to get type checking in the following\n    // functions since `ReactTestInstance` has its `props` defined as `any`.\n    component);\n  };\n  exports.getAnimatedStyle = getAnimatedStyle;\n});", "lineCount": 242, "map": [[2, 2, 1, 0], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13], [5, 6, 2, 13, "_interopRequireDefault"], [5, 28, 2, 13], [5, 31, 2, 13, "require"], [5, 38, 2, 13], [5, 39, 2, 13, "_dependencyMap"], [5, 53, 2, 13], [6, 2, 2, 13, "Object"], [6, 8, 2, 13], [6, 9, 2, 13, "defineProperty"], [6, 23, 2, 13], [6, 24, 2, 13, "exports"], [6, 31, 2, 13], [7, 4, 2, 13, "value"], [7, 9, 2, 13], [8, 2, 2, 13], [9, 2, 2, 13, "exports"], [9, 9, 2, 13], [9, 10, 2, 13, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [9, 29, 2, 13], [9, 32, 2, 13, "exports"], [9, 39, 2, 13], [9, 40, 2, 13, "setUpTests"], [9, 50, 2, 13], [9, 53, 2, 13, "exports"], [9, 60, 2, 13], [9, 61, 2, 13, "getAnimatedStyle"], [9, 77, 2, 13], [9, 80, 2, 13, "exports"], [9, 87, 2, 13], [9, 88, 2, 13, "advanceAnimationByTime"], [9, 110, 2, 13], [9, 113, 2, 13, "exports"], [9, 120, 2, 13], [9, 121, 2, 13, "advanceAnimationByFrame"], [9, 144, 2, 13], [10, 2, 2, 13], [10, 6, 2, 13, "_slicedToArray2"], [10, 21, 2, 13], [10, 24, 2, 13, "_interopRequireDefault"], [10, 46, 2, 13], [10, 47, 2, 13, "require"], [10, 54, 2, 13], [10, 55, 2, 13, "_dependencyMap"], [10, 69, 2, 13], [11, 2, 12, 0], [11, 6, 12, 0, "_errors"], [11, 13, 12, 0], [11, 16, 12, 0, "require"], [11, 23, 12, 0], [11, 24, 12, 0, "_dependencyMap"], [11, 38, 12, 0], [12, 2, 14, 0], [12, 6, 14, 0, "_PlatformChecker"], [12, 22, 14, 0], [12, 25, 14, 0, "require"], [12, 32, 14, 0], [12, 33, 14, 0, "_dependencyMap"], [12, 47, 14, 0], [13, 2, 30, 0], [13, 6, 30, 6, "defaultFramerateConfig"], [13, 28, 30, 28], [13, 31, 30, 31], [14, 4, 31, 2, "fps"], [14, 7, 31, 5], [14, 9, 31, 7], [15, 2, 32, 0], [15, 3, 32, 1], [16, 2, 34, 0], [16, 6, 34, 6, "isEmpty"], [16, 13, 34, 13], [16, 16, 34, 17, "obj"], [16, 19, 34, 40], [16, 23, 35, 2], [16, 24, 35, 3, "obj"], [16, 27, 35, 6], [16, 31, 35, 10, "Object"], [16, 37, 35, 16], [16, 38, 35, 17, "keys"], [16, 42, 35, 21], [16, 43, 35, 22, "obj"], [16, 46, 35, 25], [16, 47, 35, 26], [16, 48, 35, 27, "length"], [16, 54, 35, 33], [16, 59, 35, 38], [16, 60, 35, 39], [17, 2, 36, 0], [17, 6, 36, 6, "getStylesFromObject"], [17, 25, 36, 25], [17, 28, 36, 29, "obj"], [17, 31, 36, 40], [17, 35, 36, 45], [18, 4, 37, 2], [18, 11, 37, 9, "obj"], [18, 14, 37, 12], [18, 19, 37, 17, "undefined"], [18, 28, 37, 26], [18, 31, 38, 6], [18, 32, 38, 7], [18, 33, 38, 8], [18, 36, 39, 6, "Object"], [18, 42, 39, 12], [18, 43, 39, 13, "fromEntries"], [18, 54, 39, 24], [18, 55, 40, 8, "Object"], [18, 61, 40, 14], [18, 62, 40, 15, "entries"], [18, 69, 40, 22], [18, 70, 40, 23, "obj"], [18, 73, 40, 26], [18, 74, 40, 27], [18, 75, 40, 28, "map"], [18, 78, 40, 31], [18, 79, 40, 32, "_ref"], [18, 83, 40, 32], [19, 6, 40, 32], [19, 10, 40, 32, "_ref2"], [19, 15, 40, 32], [19, 22, 40, 32, "_slicedToArray2"], [19, 37, 40, 32], [19, 38, 40, 32, "default"], [19, 45, 40, 32], [19, 47, 40, 32, "_ref"], [19, 51, 40, 32], [20, 8, 40, 34, "property"], [20, 16, 40, 42], [20, 19, 40, 42, "_ref2"], [20, 24, 40, 42], [21, 8, 40, 44, "value"], [21, 13, 40, 49], [21, 16, 40, 49, "_ref2"], [21, 21, 40, 49], [22, 6, 40, 49], [22, 13, 40, 55], [22, 14, 41, 10, "property"], [22, 22, 41, 18], [22, 24, 42, 10, "value"], [22, 29, 42, 15], [22, 30, 42, 16, "_isReanimatedSharedValue"], [22, 54, 42, 40], [22, 57, 42, 43, "value"], [22, 62, 42, 48], [22, 63, 42, 49, "value"], [22, 68, 42, 54], [22, 71, 42, 57, "value"], [22, 76, 42, 62], [22, 77, 43, 9], [23, 4, 43, 9], [23, 6, 44, 6], [23, 7, 44, 7], [24, 2, 45, 0], [24, 3, 45, 1], [25, 2, 54, 0], [25, 6, 54, 6, "getCurrentProps"], [25, 21, 54, 21], [25, 24, 55, 2, "component"], [25, 33, 55, 26], [25, 37, 56, 53], [26, 4, 57, 2], [26, 8, 57, 8, "propsObject"], [26, 19, 57, 19], [26, 22, 57, 22, "component"], [26, 31, 57, 31], [26, 32, 57, 32, "props"], [26, 37, 57, 37], [26, 38, 57, 38, "jestAnimatedProps"], [26, 55, 57, 55], [26, 57, 57, 57, "value"], [26, 62, 57, 62], [27, 4, 59, 2], [27, 11, 59, 9, "propsObject"], [27, 22, 59, 20], [27, 25, 59, 23], [28, 6, 59, 25], [28, 9, 59, 28, "propsObject"], [29, 4, 59, 40], [29, 5, 59, 41], [29, 8, 59, 44], [29, 9, 59, 45], [29, 10, 59, 46], [30, 2, 60, 0], [30, 3, 60, 1], [31, 2, 62, 0], [31, 6, 62, 6, "getCurrentStyle"], [31, 21, 62, 21], [31, 24, 62, 25, "component"], [31, 33, 62, 49], [31, 37, 62, 68], [32, 4, 63, 2], [32, 8, 63, 8, "styleObject"], [32, 19, 63, 19], [32, 22, 63, 22, "component"], [32, 31, 63, 31], [32, 32, 63, 32, "props"], [32, 37, 63, 37], [32, 38, 63, 38, "style"], [32, 43, 63, 43], [33, 4, 65, 2], [33, 8, 65, 6, "currentStyle"], [33, 20, 65, 18], [33, 23, 65, 21], [33, 24, 65, 22], [33, 25, 65, 23], [34, 4, 67, 2], [34, 8, 67, 6, "Array"], [34, 13, 67, 11], [34, 14, 67, 12, "isArray"], [34, 21, 67, 19], [34, 22, 67, 20, "styleObject"], [34, 33, 67, 31], [34, 34, 67, 32], [34, 36, 67, 34], [35, 6, 68, 4], [36, 6, 69, 4], [37, 6, 70, 4, "styleObject"], [37, 17, 70, 15], [37, 18, 70, 16, "for<PERSON>ach"], [37, 25, 70, 23], [37, 26, 70, 25, "style"], [37, 31, 70, 30], [37, 35, 70, 35], [38, 8, 71, 6, "currentStyle"], [38, 20, 71, 18], [38, 23, 71, 21], [39, 10, 72, 8], [39, 13, 72, 11, "currentStyle"], [39, 25, 72, 23], [40, 10, 73, 8], [40, 13, 73, 11, "style"], [41, 8, 74, 6], [41, 9, 74, 7], [42, 6, 75, 4], [42, 7, 75, 5], [42, 8, 75, 6], [43, 4, 76, 2], [44, 4, 78, 2], [44, 8, 78, 8, "jestInlineStyles"], [44, 24, 78, 24], [44, 27, 78, 27, "component"], [44, 36, 78, 36], [44, 37, 78, 37, "props"], [44, 42, 78, 42], [44, 43, 78, 43, "jestInlineStyle"], [44, 58, 78, 77], [45, 4, 79, 2], [45, 8, 79, 8, "jestAnimatedStyleValue"], [45, 30, 79, 30], [45, 33, 79, 33, "component"], [45, 42, 79, 42], [45, 43, 79, 43, "props"], [45, 48, 79, 48], [45, 49, 79, 49, "jestAnimatedStyle"], [45, 66, 79, 66], [45, 68, 79, 68, "value"], [45, 73, 79, 73], [46, 4, 81, 2], [46, 8, 81, 6, "Array"], [46, 13, 81, 11], [46, 14, 81, 12, "isArray"], [46, 21, 81, 19], [46, 22, 81, 20, "jestInlineStyles"], [46, 38, 81, 36], [46, 39, 81, 37], [46, 41, 81, 39], [47, 6, 82, 4], [47, 11, 82, 9], [47, 15, 82, 15, "obj"], [47, 18, 82, 18], [47, 22, 82, 22, "jestInlineStyles"], [47, 38, 82, 38], [47, 40, 82, 40], [48, 8, 83, 6], [48, 12, 83, 10], [48, 32, 83, 30], [48, 36, 83, 34, "obj"], [48, 39, 83, 37], [48, 41, 83, 39], [49, 10, 84, 8], [50, 8, 85, 6], [51, 8, 87, 6], [51, 12, 87, 12, "inlineStyles"], [51, 25, 87, 24], [51, 28, 87, 27, "getStylesFromObject"], [51, 47, 87, 46], [51, 48, 87, 47, "obj"], [51, 51, 87, 50], [51, 52, 87, 51], [52, 8, 89, 6, "currentStyle"], [52, 20, 89, 18], [52, 23, 89, 21], [53, 10, 90, 8], [53, 13, 90, 11, "currentStyle"], [53, 25, 90, 23], [54, 10, 91, 8], [54, 13, 91, 11, "inlineStyles"], [55, 8, 92, 6], [55, 9, 92, 7], [56, 6, 93, 4], [57, 6, 95, 4, "currentStyle"], [57, 18, 95, 16], [57, 21, 95, 19], [58, 8, 96, 6], [58, 11, 96, 9, "currentStyle"], [58, 23, 96, 21], [59, 8, 97, 6], [59, 11, 97, 9, "jestAnimatedStyleValue"], [60, 6, 98, 4], [60, 7, 98, 5], [61, 6, 100, 4], [61, 13, 100, 11, "currentStyle"], [61, 25, 100, 23], [62, 4, 101, 2], [63, 4, 103, 2], [63, 8, 103, 8, "inlineStyles"], [63, 20, 103, 20], [63, 23, 103, 23, "getStylesFromObject"], [63, 42, 103, 42], [63, 43, 103, 43, "jestInlineStyles"], [63, 59, 103, 59], [63, 60, 103, 60], [64, 4, 105, 2, "currentStyle"], [64, 16, 105, 14], [64, 19, 105, 17, "isEmpty"], [64, 26, 105, 24], [64, 27, 105, 25, "jestAnimatedStyleValue"], [64, 49, 105, 69], [64, 50, 105, 70], [64, 53, 106, 6], [65, 6, 106, 8], [65, 9, 106, 11, "inlineStyles"], [66, 4, 106, 24], [66, 5, 106, 25], [66, 8, 107, 6], [67, 6, 107, 8], [67, 9, 107, 11, "jestAnimatedStyleValue"], [68, 4, 107, 34], [68, 5, 107, 35], [69, 4, 109, 2], [69, 11, 109, 9, "currentStyle"], [69, 23, 109, 21], [70, 2, 110, 0], [70, 3, 110, 1], [71, 2, 112, 0], [71, 6, 112, 6, "checkEqual"], [71, 16, 112, 16], [71, 19, 112, 19, "checkEqual"], [71, 20, 112, 27, "current"], [71, 27, 112, 41], [71, 29, 112, 43, "expected"], [71, 37, 112, 58], [71, 42, 112, 63], [72, 4, 113, 2], [72, 8, 113, 6, "Array"], [72, 13, 113, 11], [72, 14, 113, 12, "isArray"], [72, 21, 113, 19], [72, 22, 113, 20, "expected"], [72, 30, 113, 28], [72, 31, 113, 29], [72, 33, 113, 31], [73, 6, 114, 4], [73, 10, 114, 8], [73, 11, 114, 9, "Array"], [73, 16, 114, 14], [73, 17, 114, 15, "isArray"], [73, 24, 114, 22], [73, 25, 114, 23, "current"], [73, 32, 114, 30], [73, 33, 114, 31], [73, 37, 114, 35, "expected"], [73, 45, 114, 43], [73, 46, 114, 44, "length"], [73, 52, 114, 50], [73, 57, 114, 55, "current"], [73, 64, 114, 62], [73, 65, 114, 63, "length"], [73, 71, 114, 69], [73, 73, 114, 71], [74, 8, 115, 6], [74, 15, 115, 13], [74, 20, 115, 18], [75, 6, 116, 4], [76, 6, 117, 4], [76, 11, 117, 9], [76, 15, 117, 13, "i"], [76, 16, 117, 14], [76, 19, 117, 17], [76, 20, 117, 18], [76, 22, 117, 20, "i"], [76, 23, 117, 21], [76, 26, 117, 24, "current"], [76, 33, 117, 31], [76, 34, 117, 32, "length"], [76, 40, 117, 38], [76, 42, 117, 40, "i"], [76, 43, 117, 41], [76, 45, 117, 43], [76, 47, 117, 45], [77, 8, 118, 6], [77, 12, 118, 10], [77, 13, 118, 11, "checkEqual"], [77, 23, 118, 21], [77, 24, 118, 22, "current"], [77, 31, 118, 29], [77, 32, 118, 30, "i"], [77, 33, 118, 31], [77, 34, 118, 32], [77, 36, 118, 34, "expected"], [77, 44, 118, 42], [77, 45, 118, 43, "i"], [77, 46, 118, 44], [77, 47, 118, 45], [77, 48, 118, 46], [77, 50, 118, 48], [78, 10, 119, 8], [78, 17, 119, 15], [78, 22, 119, 20], [79, 8, 120, 6], [80, 6, 121, 4], [81, 4, 122, 2], [81, 5, 122, 3], [81, 11, 122, 9], [81, 15, 122, 13], [81, 22, 122, 20, "current"], [81, 29, 122, 27], [81, 34, 122, 32], [81, 42, 122, 40], [81, 46, 122, 44, "current"], [81, 53, 122, 51], [81, 55, 122, 53], [82, 6, 123, 4], [82, 10, 123, 8], [82, 17, 123, 15, "expected"], [82, 25, 123, 23], [82, 30, 123, 28], [82, 38, 123, 36], [82, 42, 123, 40], [82, 43, 123, 41, "expected"], [82, 51, 123, 49], [82, 53, 123, 51], [83, 8, 124, 6], [83, 15, 124, 13], [83, 20, 124, 18], [84, 6, 125, 4], [85, 6, 126, 4], [85, 11, 126, 9], [85, 15, 126, 15, "property"], [85, 23, 126, 23], [85, 27, 126, 27, "expected"], [85, 35, 126, 35], [85, 37, 126, 37], [86, 8, 127, 6], [86, 12, 127, 10], [86, 13, 127, 11, "checkEqual"], [86, 23, 127, 21], [86, 24, 127, 22, "current"], [86, 31, 127, 29], [86, 32, 127, 30, "property"], [86, 40, 127, 38], [86, 41, 127, 39], [86, 43, 127, 41, "expected"], [86, 51, 127, 49], [86, 52, 127, 50, "property"], [86, 60, 127, 58], [86, 61, 127, 59], [86, 62, 127, 60], [86, 64, 127, 62], [87, 10, 128, 8], [87, 17, 128, 15], [87, 22, 128, 20], [88, 8, 129, 6], [89, 6, 130, 4], [90, 4, 131, 2], [90, 5, 131, 3], [90, 11, 131, 9], [91, 6, 132, 4], [91, 13, 132, 11, "current"], [91, 20, 132, 18], [91, 25, 132, 23, "expected"], [91, 33, 132, 31], [92, 4, 133, 2], [93, 4, 134, 2], [93, 11, 134, 9], [93, 15, 134, 13], [94, 2, 135, 0], [94, 3, 135, 1], [95, 2, 137, 0], [95, 6, 137, 6, "findStyleDiff"], [95, 19, 137, 19], [95, 22, 137, 22, "findStyleDiff"], [95, 23, 138, 2, "current"], [95, 30, 138, 72], [95, 32, 139, 2, "expected"], [95, 40, 139, 73], [95, 42, 140, 2, "shouldMatchAllProps"], [95, 61, 140, 31], [95, 66, 141, 5], [96, 4, 142, 2], [96, 8, 142, 8, "diffs"], [96, 13, 142, 13], [96, 16, 142, 16], [96, 18, 142, 18], [97, 4, 143, 2], [97, 8, 143, 6, "isEqual"], [97, 15, 143, 13], [97, 18, 143, 16], [97, 22, 143, 20], [98, 4, 144, 2], [98, 8, 144, 6, "property"], [98, 16, 144, 34], [99, 4, 145, 2], [99, 9, 145, 7, "property"], [99, 17, 145, 15], [99, 21, 145, 19, "expected"], [99, 29, 145, 27], [99, 31, 145, 29], [100, 6, 146, 4], [100, 10, 146, 8], [100, 11, 146, 9, "checkEqual"], [100, 21, 146, 19], [100, 22, 146, 20, "current"], [100, 29, 146, 27], [100, 30, 146, 28, "property"], [100, 38, 146, 36], [100, 39, 146, 37], [100, 41, 146, 39, "expected"], [100, 49, 146, 47], [100, 50, 146, 48, "property"], [100, 58, 146, 56], [100, 59, 146, 57], [100, 60, 146, 58], [100, 62, 146, 60], [101, 8, 147, 6, "isEqual"], [101, 15, 147, 13], [101, 18, 147, 16], [101, 23, 147, 21], [102, 8, 148, 6, "diffs"], [102, 13, 148, 11], [102, 14, 148, 12, "push"], [102, 18, 148, 16], [102, 19, 148, 17], [103, 10, 149, 8, "property"], [103, 18, 149, 16], [104, 10, 150, 8, "current"], [104, 17, 150, 15], [104, 19, 150, 17, "current"], [104, 26, 150, 24], [104, 27, 150, 25, "property"], [104, 35, 150, 33], [104, 36, 150, 34], [105, 10, 151, 8, "expect"], [105, 16, 151, 14], [105, 18, 151, 16, "expected"], [105, 26, 151, 24], [105, 27, 151, 25, "property"], [105, 35, 151, 33], [106, 8, 152, 6], [106, 9, 152, 7], [106, 10, 152, 8], [107, 6, 153, 4], [108, 4, 154, 2], [109, 4, 156, 2], [109, 8, 157, 4, "shouldMatchAllProps"], [109, 27, 157, 23], [109, 31, 158, 4, "Object"], [109, 37, 158, 10], [109, 38, 158, 11, "keys"], [109, 42, 158, 15], [109, 43, 158, 16, "current"], [109, 50, 158, 23], [109, 51, 158, 24], [109, 52, 158, 25, "length"], [109, 58, 158, 31], [109, 63, 158, 36, "Object"], [109, 69, 158, 42], [109, 70, 158, 43, "keys"], [109, 74, 158, 47], [109, 75, 158, 48, "expected"], [109, 83, 158, 56], [109, 84, 158, 57], [109, 85, 158, 58, "length"], [109, 91, 158, 64], [109, 93, 159, 4], [110, 6, 160, 4, "isEqual"], [110, 13, 160, 11], [110, 16, 160, 14], [110, 21, 160, 19], [111, 6, 161, 4], [112, 6, 162, 4], [112, 10, 162, 8, "property"], [112, 19, 162, 36], [113, 6, 163, 4], [113, 11, 163, 9, "property"], [113, 20, 163, 17], [113, 24, 163, 21, "current"], [113, 31, 163, 28], [113, 33, 163, 30], [114, 8, 164, 6], [114, 12, 164, 10, "expected"], [114, 20, 164, 18], [114, 21, 164, 19, "property"], [114, 30, 164, 27], [114, 31, 164, 28], [114, 36, 164, 33, "undefined"], [114, 45, 164, 42], [114, 47, 164, 44], [115, 10, 165, 8, "diffs"], [115, 15, 165, 13], [115, 16, 165, 14, "push"], [115, 20, 165, 18], [115, 21, 165, 19], [116, 12, 166, 10, "property"], [116, 20, 166, 18], [116, 22, 166, 10, "property"], [116, 31, 166, 18], [117, 12, 167, 10, "current"], [117, 19, 167, 17], [117, 21, 167, 19, "current"], [117, 28, 167, 26], [117, 29, 167, 27, "property"], [117, 38, 167, 35], [117, 39, 167, 36], [118, 12, 168, 10, "expect"], [118, 18, 168, 16], [118, 20, 168, 18, "expected"], [118, 28, 168, 26], [118, 29, 168, 27, "property"], [118, 38, 168, 35], [119, 10, 169, 8], [119, 11, 169, 9], [119, 12, 169, 10], [120, 8, 170, 6], [121, 6, 171, 4], [122, 4, 172, 2], [123, 4, 174, 2], [123, 11, 174, 9], [124, 6, 174, 11, "isEqual"], [124, 13, 174, 18], [125, 6, 174, 20, "diffs"], [126, 4, 174, 26], [126, 5, 174, 27], [127, 2, 175, 0], [127, 3, 175, 1], [128, 2, 177, 0], [128, 6, 177, 6, "compareAndFormatDifferences"], [128, 33, 177, 33], [128, 36, 177, 36], [128, 45, 177, 36, "compareAndFormatDifferences"], [128, 46, 178, 2, "currentV<PERSON>ues"], [128, 59, 178, 78], [128, 61, 179, 2, "expectedV<PERSON>ues"], [128, 75, 179, 79], [128, 77, 181, 47], [129, 4, 181, 47], [129, 8, 180, 2, "shouldMatchAllProps"], [129, 27, 180, 30], [129, 30, 180, 30, "arguments"], [129, 39, 180, 30], [129, 40, 180, 30, "length"], [129, 46, 180, 30], [129, 54, 180, 30, "arguments"], [129, 63, 180, 30], [129, 71, 180, 30, "undefined"], [129, 80, 180, 30], [129, 83, 180, 30, "arguments"], [129, 92, 180, 30], [129, 98, 180, 33], [129, 103, 180, 38], [130, 4, 182, 2], [130, 8, 182, 2, "_findStyleDiff"], [130, 22, 182, 2], [130, 25, 182, 29, "findStyleDiff"], [130, 38, 182, 42], [130, 39, 183, 4, "currentV<PERSON>ues"], [130, 52, 183, 17], [130, 54, 184, 4, "expectedV<PERSON>ues"], [130, 68, 184, 18], [130, 70, 185, 4, "shouldMatchAllProps"], [130, 89, 186, 2], [130, 90, 186, 3], [131, 6, 182, 10, "isEqual"], [131, 13, 182, 17], [131, 16, 182, 17, "_findStyleDiff"], [131, 30, 182, 17], [131, 31, 182, 10, "isEqual"], [131, 38, 182, 17], [132, 6, 182, 19, "diffs"], [132, 11, 182, 24], [132, 14, 182, 24, "_findStyleDiff"], [132, 28, 182, 24], [132, 29, 182, 19, "diffs"], [132, 34, 182, 24], [133, 4, 188, 2], [133, 8, 188, 6, "isEqual"], [133, 15, 188, 13], [133, 17, 188, 15], [134, 6, 189, 4], [134, 13, 189, 11], [135, 8, 189, 13, "message"], [135, 15, 189, 20], [135, 17, 189, 22, "message"], [135, 18, 189, 22], [135, 23, 189, 28], [135, 27, 189, 32], [136, 8, 189, 34, "pass"], [136, 12, 189, 38], [136, 14, 189, 40], [137, 6, 189, 45], [137, 7, 189, 46], [138, 4, 190, 2], [139, 4, 192, 2], [139, 8, 192, 8, "currentValuesStr"], [139, 24, 192, 24], [139, 27, 192, 27, "JSON"], [139, 31, 192, 31], [139, 32, 192, 32, "stringify"], [139, 41, 192, 41], [139, 42, 192, 42, "currentV<PERSON>ues"], [139, 55, 192, 55], [139, 56, 192, 56], [140, 4, 193, 2], [140, 8, 193, 8, "expectedValuesStr"], [140, 25, 193, 25], [140, 28, 193, 28, "JSON"], [140, 32, 193, 32], [140, 33, 193, 33, "stringify"], [140, 42, 193, 42], [140, 43, 193, 43, "expectedV<PERSON>ues"], [140, 57, 193, 57], [140, 58, 193, 58], [141, 4, 194, 2], [141, 8, 194, 8, "differences"], [141, 19, 194, 19], [141, 22, 194, 22, "diffs"], [141, 27, 194, 27], [141, 28, 195, 5, "map"], [141, 31, 195, 8], [141, 32, 196, 7, "diff"], [141, 36, 196, 11], [141, 40, 197, 8], [141, 46, 197, 14, "diff"], [141, 50, 197, 18], [141, 51, 197, 19, "property"], [141, 59, 197, 27], [141, 74, 197, 42, "JSON"], [141, 78, 197, 46], [141, 79, 197, 47, "stringify"], [141, 88, 197, 56], [141, 89, 197, 57, "diff"], [141, 93, 197, 61], [141, 94, 197, 62, "expect"], [141, 100, 197, 68], [141, 101, 197, 69], [141, 113, 197, 81, "JSON"], [141, 117, 197, 85], [141, 118, 197, 86, "stringify"], [141, 127, 197, 95], [141, 128, 197, 96, "diff"], [141, 132, 197, 100], [141, 133, 197, 101, "current"], [141, 140, 197, 108], [141, 141, 197, 109], [141, 143, 198, 4], [141, 144, 198, 5], [141, 145, 199, 5, "join"], [141, 149, 199, 9], [141, 150, 199, 10], [141, 154, 199, 14], [141, 155, 199, 15], [142, 4, 201, 2], [142, 11, 201, 9], [143, 6, 202, 4, "message"], [143, 13, 202, 11], [143, 15, 202, 13, "message"], [143, 16, 202, 13], [143, 21, 203, 6], [143, 34, 203, 19, "expectedValuesStr"], [143, 51, 203, 36], [143, 66, 203, 51, "currentValuesStr"], [143, 82, 203, 67], [143, 103, 203, 88, "differences"], [143, 114, 203, 99], [143, 116, 203, 101], [144, 6, 204, 4, "pass"], [144, 10, 204, 8], [144, 12, 204, 10], [145, 4, 205, 2], [145, 5, 205, 3], [146, 2, 206, 0], [146, 3, 206, 1], [147, 2, 208, 0], [147, 6, 208, 6, "compareProps"], [147, 18, 208, 18], [147, 21, 208, 21, "compareProps"], [147, 22, 209, 2, "component"], [147, 31, 209, 26], [147, 33, 210, 2, "expectedProps"], [147, 46, 210, 63], [147, 51, 211, 5], [148, 4, 212, 2], [148, 8, 213, 4, "component"], [148, 17, 213, 13], [148, 18, 213, 14, "props"], [148, 23, 213, 19], [148, 24, 213, 20, "jestAnimatedProps"], [148, 41, 213, 37], [148, 45, 214, 4, "Object"], [148, 51, 214, 10], [148, 52, 214, 11, "keys"], [148, 56, 214, 15], [148, 57, 214, 16, "component"], [148, 66, 214, 25], [148, 67, 214, 26, "props"], [148, 72, 214, 31], [148, 73, 214, 32, "jestAnimatedProps"], [148, 90, 214, 49], [148, 91, 214, 50, "value"], [148, 96, 214, 55], [148, 97, 214, 56], [148, 98, 214, 57, "length"], [148, 104, 214, 63], [148, 109, 214, 68], [148, 110, 214, 69], [148, 112, 215, 4], [149, 6, 216, 4], [149, 13, 216, 11], [150, 8, 216, 13, "message"], [150, 15, 216, 20], [150, 17, 216, 22, "message"], [150, 18, 216, 22], [150, 23, 216, 28], [150, 54, 216, 59], [151, 8, 216, 61, "pass"], [151, 12, 216, 65], [151, 14, 216, 67], [152, 6, 216, 73], [152, 7, 216, 74], [153, 4, 217, 2], [154, 4, 219, 2], [154, 8, 219, 8, "currentProps"], [154, 20, 219, 20], [154, 23, 219, 23, "getCurrentProps"], [154, 38, 219, 38], [154, 39, 219, 39, "component"], [154, 48, 219, 48], [154, 49, 219, 49], [155, 4, 221, 2], [155, 11, 221, 9, "compareAndFormatDifferences"], [155, 38, 221, 36], [155, 39, 221, 37, "currentProps"], [155, 51, 221, 49], [155, 53, 221, 51, "expectedProps"], [155, 66, 221, 64], [155, 67, 221, 65], [156, 2, 222, 0], [156, 3, 222, 1], [157, 2, 224, 0], [157, 6, 224, 6, "compareStyle"], [157, 18, 224, 18], [157, 21, 224, 21, "compareStyle"], [157, 22, 225, 2, "component"], [157, 31, 225, 26], [157, 33, 226, 2, "expectedStyle"], [157, 46, 226, 29], [157, 48, 227, 2, "config"], [157, 54, 227, 35], [157, 59, 228, 5], [158, 4, 229, 2], [158, 8, 229, 6], [158, 9, 229, 7, "component"], [158, 18, 229, 16], [158, 19, 229, 17, "props"], [158, 24, 229, 22], [158, 25, 229, 23, "style"], [158, 30, 229, 28], [158, 32, 229, 30], [159, 6, 230, 4], [159, 13, 230, 11], [160, 8, 230, 13, "message"], [160, 15, 230, 20], [160, 17, 230, 22, "message"], [160, 18, 230, 22], [160, 23, 230, 28], [160, 56, 230, 61], [161, 8, 230, 63, "pass"], [161, 12, 230, 67], [161, 14, 230, 69], [162, 6, 230, 75], [162, 7, 230, 76], [163, 4, 231, 2], [164, 4, 232, 2], [164, 8, 232, 10, "shouldMatchAllProps"], [164, 27, 232, 29], [164, 30, 232, 34, "config"], [164, 36, 232, 40], [164, 37, 232, 10, "shouldMatchAllProps"], [164, 56, 232, 29], [165, 4, 233, 2], [165, 8, 233, 8, "currentStyle"], [165, 20, 233, 20], [165, 23, 233, 23, "getCurrentStyle"], [165, 38, 233, 38], [165, 39, 233, 39, "component"], [165, 48, 233, 48], [165, 49, 233, 49], [166, 4, 235, 2], [166, 11, 235, 9, "compareAndFormatDifferences"], [166, 38, 235, 36], [166, 39, 236, 4, "currentStyle"], [166, 51, 236, 16], [166, 53, 237, 4, "expectedStyle"], [166, 66, 237, 17], [166, 68, 238, 4, "shouldMatchAllProps"], [166, 87, 239, 2], [166, 88, 239, 3], [167, 2, 240, 0], [167, 3, 240, 1], [168, 2, 242, 0], [168, 6, 242, 4, "frameTime"], [168, 15, 242, 13], [168, 18, 242, 16, "Math"], [168, 22, 242, 20], [168, 23, 242, 21, "round"], [168, 28, 242, 26], [168, 29, 242, 27], [168, 33, 242, 31], [168, 36, 242, 34, "defaultFramerateConfig"], [168, 58, 242, 56], [168, 59, 242, 57, "fps"], [168, 62, 242, 60], [168, 63, 242, 61], [169, 2, 244, 0], [169, 6, 244, 6, "beforeTest"], [169, 16, 244, 16], [169, 19, 244, 19, "beforeTest"], [169, 20, 244, 19], [169, 25, 244, 25], [170, 4, 245, 2, "jest"], [170, 8, 245, 6], [170, 9, 245, 7, "useFakeTimers"], [170, 22, 245, 20], [170, 23, 245, 21], [170, 24, 245, 22], [171, 2, 246, 0], [171, 3, 246, 1], [172, 2, 248, 0], [172, 6, 248, 6, "afterTest"], [172, 15, 248, 15], [172, 18, 248, 18, "afterTest"], [172, 19, 248, 18], [172, 24, 248, 24], [173, 4, 249, 2, "jest"], [173, 8, 249, 6], [173, 9, 249, 7, "runOnlyPendingTimers"], [173, 29, 249, 27], [173, 30, 249, 28], [173, 31, 249, 29], [174, 4, 250, 2, "jest"], [174, 8, 250, 6], [174, 9, 250, 7, "useRealTimers"], [174, 22, 250, 20], [174, 23, 250, 21], [174, 24, 250, 22], [175, 2, 251, 0], [175, 3, 251, 1], [176, 2, 253, 7], [176, 6, 253, 13, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [176, 25, 253, 32], [176, 28, 253, 36, "animationTest"], [176, 41, 253, 61], [176, 45, 253, 66], [177, 4, 254, 2, "console"], [177, 11, 254, 9], [177, 12, 254, 10, "warn"], [177, 16, 254, 14], [177, 17, 255, 4], [177, 183, 256, 2], [177, 184, 256, 3], [178, 4, 257, 2, "beforeTest"], [178, 14, 257, 12], [178, 15, 257, 13], [178, 16, 257, 14], [179, 4, 258, 2, "animationTest"], [179, 17, 258, 15], [179, 18, 258, 16], [179, 19, 258, 17], [180, 4, 259, 2, "afterTest"], [180, 13, 259, 11], [180, 14, 259, 12], [180, 15, 259, 13], [181, 2, 260, 0], [181, 3, 260, 1], [182, 2, 260, 2, "exports"], [182, 9, 260, 2], [182, 10, 260, 2, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [182, 29, 260, 2], [182, 32, 260, 2, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [182, 51, 260, 2], [183, 2, 262, 7], [183, 6, 262, 13, "advanceAnimationByTime"], [183, 28, 262, 35], [183, 31, 262, 38], [183, 40, 262, 38, "advanceAnimationByTime"], [183, 41, 262, 38], [183, 43, 262, 60], [184, 4, 262, 60], [184, 8, 262, 39, "time"], [184, 12, 262, 43], [184, 15, 262, 43, "arguments"], [184, 24, 262, 43], [184, 25, 262, 43, "length"], [184, 31, 262, 43], [184, 39, 262, 43, "arguments"], [184, 48, 262, 43], [184, 56, 262, 43, "undefined"], [184, 65, 262, 43], [184, 68, 262, 43, "arguments"], [184, 77, 262, 43], [184, 83, 262, 46, "frameTime"], [184, 92, 262, 55], [185, 4, 263, 2, "console"], [185, 11, 263, 9], [185, 12, 263, 10, "warn"], [185, 16, 263, 14], [185, 17, 264, 4], [185, 83, 265, 2], [185, 84, 265, 3], [186, 4, 266, 2, "jest"], [186, 8, 266, 6], [186, 9, 266, 7, "advanceTimersByTime"], [186, 28, 266, 26], [186, 29, 266, 27, "time"], [186, 33, 266, 31], [186, 34, 266, 32], [187, 4, 267, 2, "jest"], [187, 8, 267, 6], [187, 9, 267, 7, "runOnlyPendingTimers"], [187, 29, 267, 27], [187, 30, 267, 28], [187, 31, 267, 29], [188, 2, 268, 0], [188, 3, 268, 1], [189, 2, 268, 2, "exports"], [189, 9, 268, 2], [189, 10, 268, 2, "advanceAnimationByTime"], [189, 32, 268, 2], [189, 35, 268, 2, "advanceAnimationByTime"], [189, 57, 268, 2], [190, 2, 270, 7], [190, 6, 270, 13, "advanceAnimationByFrame"], [190, 29, 270, 36], [190, 32, 270, 40, "count"], [190, 37, 270, 53], [190, 41, 270, 58], [191, 4, 271, 2, "console"], [191, 11, 271, 9], [191, 12, 271, 10, "warn"], [191, 16, 271, 14], [191, 17, 272, 4], [191, 83, 273, 2], [191, 84, 273, 3], [192, 4, 274, 2, "jest"], [192, 8, 274, 6], [192, 9, 274, 7, "advanceTimersByTime"], [192, 28, 274, 26], [192, 29, 274, 27, "count"], [192, 34, 274, 32], [192, 37, 274, 35, "frameTime"], [192, 46, 274, 44], [192, 47, 274, 45], [193, 4, 275, 2, "jest"], [193, 8, 275, 6], [193, 9, 275, 7, "runOnlyPendingTimers"], [193, 29, 275, 27], [193, 30, 275, 28], [193, 31, 275, 29], [194, 2, 276, 0], [194, 3, 276, 1], [195, 2, 276, 2, "exports"], [195, 9, 276, 2], [195, 10, 276, 2, "advanceAnimationByFrame"], [195, 33, 276, 2], [195, 36, 276, 2, "advanceAnimationByFrame"], [195, 59, 276, 2], [196, 2, 278, 0], [196, 6, 278, 6, "requireFunction"], [196, 21, 278, 21], [196, 24, 278, 24], [196, 28, 278, 24, "isJest"], [196, 51, 278, 30], [196, 53, 278, 31], [196, 54, 278, 32], [196, 57, 279, 4, "require"], [196, 64, 279, 11], [196, 67, 280, 4], [196, 73, 280, 10], [197, 4, 281, 6], [197, 10, 281, 12], [197, 14, 281, 16, "ReanimatedError"], [197, 37, 281, 31], [197, 38, 282, 8], [197, 91, 283, 6], [197, 92, 283, 7], [198, 2, 284, 4], [198, 3, 284, 5], [199, 2, 290, 7], [199, 6, 290, 13, "setUpTests"], [199, 16, 290, 23], [199, 19, 290, 26], [199, 28, 290, 26, "setUpTests"], [199, 29, 290, 26], [199, 31, 290, 56], [200, 4, 290, 56], [200, 8, 290, 27, "userFramerateConfig"], [200, 27, 290, 46], [200, 30, 290, 46, "arguments"], [200, 39, 290, 46], [200, 40, 290, 46, "length"], [200, 46, 290, 46], [200, 54, 290, 46, "arguments"], [200, 63, 290, 46], [200, 71, 290, 46, "undefined"], [200, 80, 290, 46], [200, 83, 290, 46, "arguments"], [200, 92, 290, 46], [200, 98, 290, 49], [200, 99, 290, 50], [200, 100, 290, 51], [201, 4, 291, 2], [201, 8, 291, 6, "expect"], [201, 14, 291, 25], [201, 17, 291, 29, "global"], [201, 23, 291, 35], [201, 24, 292, 5, "expect"], [201, 30, 292, 11], [202, 4, 293, 2], [202, 8, 293, 6, "expect"], [202, 14, 293, 12], [202, 19, 293, 17, "undefined"], [202, 28, 293, 26], [202, 30, 293, 28], [203, 6, 294, 4], [203, 10, 294, 10, "expectModule"], [203, 22, 294, 22], [203, 25, 294, 25, "requireFunction"], [203, 40, 294, 40], [203, 41, 294, 41], [203, 49, 294, 49], [203, 50, 294, 50], [204, 6, 295, 4, "expect"], [204, 12, 295, 10], [204, 15, 295, 13, "expectModule"], [204, 27, 295, 25], [205, 6, 296, 4], [206, 6, 297, 4], [207, 6, 298, 4], [208, 6, 299, 4], [209, 6, 300, 4], [209, 10, 300, 8], [209, 17, 300, 15, "expect"], [209, 23, 300, 21], [209, 28, 300, 26], [209, 36, 300, 34], [209, 38, 300, 36], [210, 8, 301, 6], [210, 12, 301, 12, "jestGlobals"], [210, 23, 301, 23], [210, 26, 301, 26, "requireFunction"], [210, 41, 301, 41], [210, 42, 301, 42], [210, 57, 301, 57], [210, 58, 301, 58], [211, 8, 302, 6, "expect"], [211, 14, 302, 12], [211, 17, 302, 15, "jestGlobals"], [211, 28, 302, 26], [211, 29, 302, 27, "expect"], [211, 35, 302, 33], [212, 6, 303, 4], [213, 6, 304, 4], [213, 10, 304, 8, "expect"], [213, 16, 304, 14], [213, 21, 304, 19, "undefined"], [213, 30, 304, 28], [213, 34, 304, 32, "expect"], [213, 40, 304, 38], [213, 41, 304, 39, "extend"], [213, 47, 304, 45], [213, 52, 304, 50, "undefined"], [213, 61, 304, 59], [213, 63, 304, 61], [214, 8, 305, 6, "expect"], [214, 14, 305, 12], [214, 17, 305, 15, "expectModule"], [214, 29, 305, 27], [214, 30, 305, 28, "default"], [214, 37, 305, 35], [215, 6, 306, 4], [216, 4, 307, 2], [217, 4, 309, 2], [217, 8, 309, 8, "framerateConfig"], [217, 23, 309, 23], [217, 26, 309, 26], [218, 6, 310, 4], [218, 9, 310, 7, "defaultFramerateConfig"], [218, 31, 310, 29], [219, 6, 311, 4], [219, 9, 311, 7, "userFramerateConfig"], [220, 4, 312, 2], [220, 5, 312, 3], [221, 4, 313, 2, "frameTime"], [221, 13, 313, 11], [221, 16, 313, 14, "Math"], [221, 20, 313, 18], [221, 21, 313, 19, "round"], [221, 26, 313, 24], [221, 27, 313, 25], [221, 31, 313, 29], [221, 34, 313, 32, "framerateConfig"], [221, 49, 313, 47], [221, 50, 313, 48, "fps"], [221, 53, 313, 51], [221, 54, 313, 52], [222, 4, 315, 2, "expect"], [222, 10, 315, 8], [222, 11, 315, 9, "extend"], [222, 17, 315, 15], [222, 18, 315, 16], [223, 6, 316, 4, "toHaveAnimatedProps"], [223, 25, 316, 23, "toHaveAnimatedProps"], [223, 26, 317, 6, "component"], [223, 35, 320, 34], [223, 37, 321, 6, "expectedProps"], [223, 50, 321, 67], [223, 52, 322, 6], [224, 8, 323, 6], [224, 15, 323, 13, "compareProps"], [224, 27, 323, 25], [224, 28, 323, 26, "component"], [224, 37, 323, 35], [224, 39, 323, 37, "expectedProps"], [224, 52, 323, 50], [224, 53, 323, 51], [225, 6, 324, 4], [226, 4, 325, 2], [226, 5, 325, 3], [226, 6, 325, 4], [227, 4, 327, 2, "expect"], [227, 10, 327, 8], [227, 11, 327, 9, "extend"], [227, 17, 327, 15], [227, 18, 327, 16], [228, 6, 328, 4, "toHaveAnimatedStyle"], [228, 25, 328, 23, "toHaveAnimatedStyle"], [228, 26, 329, 6, "component"], [228, 35, 332, 34], [228, 37, 333, 6, "expectedStyle"], [228, 50, 333, 33], [228, 52, 335, 6], [229, 8, 335, 6], [229, 12, 334, 6, "config"], [229, 18, 334, 39], [229, 21, 334, 39, "arguments"], [229, 30, 334, 39], [229, 31, 334, 39, "length"], [229, 37, 334, 39], [229, 45, 334, 39, "arguments"], [229, 54, 334, 39], [229, 62, 334, 39, "undefined"], [229, 71, 334, 39], [229, 74, 334, 39, "arguments"], [229, 83, 334, 39], [229, 89, 334, 42], [229, 90, 334, 43], [229, 91, 334, 44], [230, 8, 336, 6], [230, 15, 336, 13, "compareStyle"], [230, 27, 336, 25], [230, 28, 336, 26, "component"], [230, 37, 336, 35], [230, 39, 336, 37, "expectedStyle"], [230, 52, 336, 50], [230, 54, 336, 52, "config"], [230, 60, 336, 58], [230, 61, 336, 59], [231, 6, 337, 4], [232, 4, 338, 2], [232, 5, 338, 3], [232, 6, 338, 4], [233, 2, 339, 0], [233, 3, 339, 1], [234, 2, 339, 2, "exports"], [234, 9, 339, 2], [234, 10, 339, 2, "setUpTests"], [234, 20, 339, 2], [234, 23, 339, 2, "setUpTests"], [234, 33, 339, 2], [235, 2, 350, 7], [235, 6, 350, 13, "getAnimatedStyle"], [235, 22, 350, 29], [235, 25, 350, 33, "component"], [235, 34, 350, 61], [235, 38, 350, 66], [236, 4, 351, 2], [236, 11, 351, 9, "getCurrentStyle"], [236, 26, 351, 24], [237, 4, 352, 4], [238, 4, 353, 4], [239, 4, 354, 4, "component"], [239, 13, 355, 2], [239, 14, 355, 3], [240, 2, 356, 0], [240, 3, 356, 1], [241, 2, 356, 2, "exports"], [241, 9, 356, 2], [241, 10, 356, 2, "getAnimatedStyle"], [241, 26, 356, 2], [241, 29, 356, 2, "getAnimatedStyle"], [241, 45, 356, 2], [242, 0, 356, 2], [242, 3]], "functionMap": {"names": ["<global>", "isEmpty", "getStylesFromObject", "Object.entries.map$argument_0", "getCurrentProps", "getCurrentStyle", "styleObject.forEach$argument_0", "checkEqual", "findStyleDiff", "compareAndFormatDifferences", "message", "diffs.map$argument_0", "compareProps", "compareStyle", "beforeTest", "afterTest", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advanceAnimationByTime", "advanceAnimationByFrame", "<anonymous>", "setUpTests", "expect.extend$argument_0.toHaveAnimatedProps", "expect.extend$argument_0.toHaveAnimatedStyle", "getAnimatedStyle"], "mappings": "AAA;gBCiC;uCDC;4BEC;gCCI;SDG;CFE;wBIS;CJM;wBKE;wBCQ;KDK;CLmC;mBOE;CPuB;sBQE;CRsC;oCSE;sBCY,UD;MEO;+GFC;aCK;qGDC;CTG;qBYE;sBFQ,qCE;CZM;qBaE;sBHM,uCG;CbU;mBcI;CdE;kBeE;CfG;mCgBE;ChBO;sCiBE;CjBM;uCkBE;ClBM;ImBI;KnBI;0BoBM;IC0B;KDQ;IEI;KFS;CpBE;gCuBW;CvBM"}}, "type": "js/module"}]}