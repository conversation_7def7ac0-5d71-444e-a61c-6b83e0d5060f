{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "../../../src/private/animated/NativeAnimatedHelper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 86}}], "key": "nPwQvxMCRdjC57J8sIprqhf4lHM=", "exportNames": ["*"]}}, {"name": "../../../src/private/featureflags/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 101}}], "key": "6CUw6Huzwfao7NX5F1mey95YZ2Q=", "exportNames": ["*"]}}, {"name": "../nodes/AnimatedProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 51}}], "key": "nP/O/8+Q4iEuHGmVwQ/fCbxI2Fc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _NativeAnimatedHelper = _interopRequireDefault(require(_dependencyMap[5], \"../../../src/private/animated/NativeAnimatedHelper\"));\n  var ReactNativeFeatureFlags = _interopRequireWildcard(require(_dependencyMap[6], \"../../../src/private/featureflags/ReactNativeFeatureFlags\"));\n  var _AnimatedProps = _interopRequireDefault(require(_dependencyMap[7], \"../nodes/AnimatedProps\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var startNativeAnimationNextId = 1;\n  var _nativeID = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"nativeID\");\n  var _onEnd = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"onEnd\");\n  var _useNativeDriver = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"useNativeDriver\");\n  var Animation = exports.default = /*#__PURE__*/function () {\n    function Animation(config) {\n      (0, _classCallCheck2.default)(this, Animation);\n      Object.defineProperty(this, _nativeID, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _onEnd, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _useNativeDriver, {\n        writable: true,\n        value: void 0\n      });\n      (0, _classPrivateFieldLooseBase2.default)(this, _useNativeDriver)[_useNativeDriver] = _NativeAnimatedHelper.default.shouldUseNativeDriver(config);\n      this.__active = false;\n      this.__isInteraction = config.isInteraction ?? !(0, _classPrivateFieldLooseBase2.default)(this, _useNativeDriver)[_useNativeDriver];\n      this.__isLooping = config.isLooping;\n      this.__iterations = config.iterations ?? 1;\n      if (__DEV__) {\n        this.__debugID = config.debugID;\n      }\n    }\n    return (0, _createClass2.default)(Animation, [{\n      key: \"start\",\n      value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n        if (!(0, _classPrivateFieldLooseBase2.default)(this, _useNativeDriver)[_useNativeDriver] && animatedValue.__isNative === true) {\n          throw new Error('Attempting to run JS driven animation on animated node ' + 'that has been moved to \"native\" earlier by starting an ' + 'animation with `useNativeDriver: true`');\n        }\n        (0, _classPrivateFieldLooseBase2.default)(this, _onEnd)[_onEnd] = onEnd;\n        this.__active = true;\n      }\n    }, {\n      key: \"stop\",\n      value: function stop() {\n        if ((0, _classPrivateFieldLooseBase2.default)(this, _nativeID)[_nativeID] != null) {\n          var nativeID = (0, _classPrivateFieldLooseBase2.default)(this, _nativeID)[_nativeID];\n          var identifier = `${nativeID}:stopAnimation`;\n          try {\n            _NativeAnimatedHelper.default.API.setWaitingForIdentifier(identifier);\n            _NativeAnimatedHelper.default.API.stopAnimation(nativeID);\n          } finally {\n            _NativeAnimatedHelper.default.API.unsetWaitingForIdentifier(identifier);\n          }\n        }\n        this.__active = false;\n      }\n    }, {\n      key: \"__getNativeAnimationConfig\",\n      value: function __getNativeAnimationConfig() {\n        throw new Error('This animation type cannot be offloaded to native');\n      }\n    }, {\n      key: \"__findAnimatedPropsNodes\",\n      value: function __findAnimatedPropsNodes(node) {\n        var result = [];\n        if (node instanceof _AnimatedProps.default) {\n          result.push(node);\n          return result;\n        }\n        for (var child of node.__getChildren()) {\n          result.push(...this.__findAnimatedPropsNodes(child));\n        }\n        return result;\n      }\n    }, {\n      key: \"__startAnimationIfNative\",\n      value: function __startAnimationIfNative(animatedValue) {\n        if (!(0, _classPrivateFieldLooseBase2.default)(this, _useNativeDriver)[_useNativeDriver]) {\n          return false;\n        }\n        var startNativeAnimationWaitId = `${startNativeAnimationNextId}:startAnimation`;\n        startNativeAnimationNextId += 1;\n        _NativeAnimatedHelper.default.API.setWaitingForIdentifier(startNativeAnimationWaitId);\n        try {\n          var config = this.__getNativeAnimationConfig();\n          animatedValue.__makeNative(config.platformConfig);\n          (0, _classPrivateFieldLooseBase2.default)(this, _nativeID)[_nativeID] = _NativeAnimatedHelper.default.generateNewAnimationId();\n          _NativeAnimatedHelper.default.API.startAnimatingNode((0, _classPrivateFieldLooseBase2.default)(this, _nativeID)[_nativeID], animatedValue.__getNativeTag(), config, result => {\n            this.__notifyAnimationEnd(result);\n            var value = result.value;\n            if (value != null) {\n              animatedValue.__onAnimatedValueUpdateReceived(value);\n              if (this.__isLooping === true) {\n                return;\n              }\n              this.__findAnimatedPropsNodes(animatedValue).forEach(node => node.update());\n            }\n          });\n          return true;\n        } catch (e) {\n          throw e;\n        } finally {\n          _NativeAnimatedHelper.default.API.unsetWaitingForIdentifier(startNativeAnimationWaitId);\n        }\n      }\n    }, {\n      key: \"__notifyAnimationEnd\",\n      value: function __notifyAnimationEnd(result) {\n        var callback = (0, _classPrivateFieldLooseBase2.default)(this, _onEnd)[_onEnd];\n        if (callback != null) {\n          (0, _classPrivateFieldLooseBase2.default)(this, _onEnd)[_onEnd] = null;\n          callback(result);\n        }\n      }\n    }, {\n      key: \"__getDebugID\",\n      value: function __getDebugID() {\n        if (__DEV__) {\n          return this.__debugID;\n        }\n        return undefined;\n      }\n    }]);\n  }();\n});", "lineCount": 135, "map": [[11, 2, 15, 0], [11, 6, 15, 0, "_NativeAnimatedHelper"], [11, 27, 15, 0], [11, 30, 15, 0, "_interopRequireDefault"], [11, 52, 15, 0], [11, 53, 15, 0, "require"], [11, 60, 15, 0], [11, 61, 15, 0, "_dependencyMap"], [11, 75, 15, 0], [12, 2, 16, 0], [12, 6, 16, 0, "ReactNativeFeatureFlags"], [12, 29, 16, 0], [12, 32, 16, 0, "_interopRequireWildcard"], [12, 55, 16, 0], [12, 56, 16, 0, "require"], [12, 63, 16, 0], [12, 64, 16, 0, "_dependencyMap"], [12, 78, 16, 0], [13, 2, 17, 0], [13, 6, 17, 0, "_AnimatedProps"], [13, 20, 17, 0], [13, 23, 17, 0, "_interopRequireDefault"], [13, 45, 17, 0], [13, 46, 17, 0, "require"], [13, 53, 17, 0], [13, 54, 17, 0, "_dependencyMap"], [13, 68, 17, 0], [14, 2, 17, 51], [14, 11, 17, 51, "_interopRequireWildcard"], [14, 35, 17, 51, "e"], [14, 36, 17, 51], [14, 38, 17, 51, "t"], [14, 39, 17, 51], [14, 68, 17, 51, "WeakMap"], [14, 75, 17, 51], [14, 81, 17, 51, "r"], [14, 82, 17, 51], [14, 89, 17, 51, "WeakMap"], [14, 96, 17, 51], [14, 100, 17, 51, "n"], [14, 101, 17, 51], [14, 108, 17, 51, "WeakMap"], [14, 115, 17, 51], [14, 127, 17, 51, "_interopRequireWildcard"], [14, 150, 17, 51], [14, 162, 17, 51, "_interopRequireWildcard"], [14, 163, 17, 51, "e"], [14, 164, 17, 51], [14, 166, 17, 51, "t"], [14, 167, 17, 51], [14, 176, 17, 51, "t"], [14, 177, 17, 51], [14, 181, 17, 51, "e"], [14, 182, 17, 51], [14, 186, 17, 51, "e"], [14, 187, 17, 51], [14, 188, 17, 51, "__esModule"], [14, 198, 17, 51], [14, 207, 17, 51, "e"], [14, 208, 17, 51], [14, 214, 17, 51, "o"], [14, 215, 17, 51], [14, 217, 17, 51, "i"], [14, 218, 17, 51], [14, 220, 17, 51, "f"], [14, 221, 17, 51], [14, 226, 17, 51, "__proto__"], [14, 235, 17, 51], [14, 243, 17, 51, "default"], [14, 250, 17, 51], [14, 252, 17, 51, "e"], [14, 253, 17, 51], [14, 270, 17, 51, "e"], [14, 271, 17, 51], [14, 294, 17, 51, "e"], [14, 295, 17, 51], [14, 320, 17, 51, "e"], [14, 321, 17, 51], [14, 330, 17, 51, "f"], [14, 331, 17, 51], [14, 337, 17, 51, "o"], [14, 338, 17, 51], [14, 341, 17, 51, "t"], [14, 342, 17, 51], [14, 345, 17, 51, "n"], [14, 346, 17, 51], [14, 349, 17, 51, "r"], [14, 350, 17, 51], [14, 358, 17, 51, "o"], [14, 359, 17, 51], [14, 360, 17, 51, "has"], [14, 363, 17, 51], [14, 364, 17, 51, "e"], [14, 365, 17, 51], [14, 375, 17, 51, "o"], [14, 376, 17, 51], [14, 377, 17, 51, "get"], [14, 380, 17, 51], [14, 381, 17, 51, "e"], [14, 382, 17, 51], [14, 385, 17, 51, "o"], [14, 386, 17, 51], [14, 387, 17, 51, "set"], [14, 390, 17, 51], [14, 391, 17, 51, "e"], [14, 392, 17, 51], [14, 394, 17, 51, "f"], [14, 395, 17, 51], [14, 409, 17, 51, "_t"], [14, 411, 17, 51], [14, 415, 17, 51, "e"], [14, 416, 17, 51], [14, 432, 17, 51, "_t"], [14, 434, 17, 51], [14, 441, 17, 51, "hasOwnProperty"], [14, 455, 17, 51], [14, 456, 17, 51, "call"], [14, 460, 17, 51], [14, 461, 17, 51, "e"], [14, 462, 17, 51], [14, 464, 17, 51, "_t"], [14, 466, 17, 51], [14, 473, 17, 51, "i"], [14, 474, 17, 51], [14, 478, 17, 51, "o"], [14, 479, 17, 51], [14, 482, 17, 51, "Object"], [14, 488, 17, 51], [14, 489, 17, 51, "defineProperty"], [14, 503, 17, 51], [14, 508, 17, 51, "Object"], [14, 514, 17, 51], [14, 515, 17, 51, "getOwnPropertyDescriptor"], [14, 539, 17, 51], [14, 540, 17, 51, "e"], [14, 541, 17, 51], [14, 543, 17, 51, "_t"], [14, 545, 17, 51], [14, 552, 17, 51, "i"], [14, 553, 17, 51], [14, 554, 17, 51, "get"], [14, 557, 17, 51], [14, 561, 17, 51, "i"], [14, 562, 17, 51], [14, 563, 17, 51, "set"], [14, 566, 17, 51], [14, 570, 17, 51, "o"], [14, 571, 17, 51], [14, 572, 17, 51, "f"], [14, 573, 17, 51], [14, 575, 17, 51, "_t"], [14, 577, 17, 51], [14, 579, 17, 51, "i"], [14, 580, 17, 51], [14, 584, 17, 51, "f"], [14, 585, 17, 51], [14, 586, 17, 51, "_t"], [14, 588, 17, 51], [14, 592, 17, 51, "e"], [14, 593, 17, 51], [14, 594, 17, 51, "_t"], [14, 596, 17, 51], [14, 607, 17, 51, "f"], [14, 608, 17, 51], [14, 613, 17, 51, "e"], [14, 614, 17, 51], [14, 616, 17, 51, "t"], [14, 617, 17, 51], [15, 2, 33, 0], [15, 6, 33, 4, "startNativeAnimationNextId"], [15, 32, 33, 30], [15, 35, 33, 33], [15, 36, 33, 34], [16, 2, 33, 35], [16, 6, 33, 35, "_nativeID"], [16, 15, 33, 35], [16, 35, 33, 35, "_classPrivateFieldLooseKey2"], [16, 62, 33, 35], [16, 63, 33, 35, "default"], [16, 70, 33, 35], [17, 2, 33, 35], [17, 6, 33, 35, "_onEnd"], [17, 12, 33, 35], [17, 32, 33, 35, "_classPrivateFieldLooseKey2"], [17, 59, 33, 35], [17, 60, 33, 35, "default"], [17, 67, 33, 35], [18, 2, 33, 35], [18, 6, 33, 35, "_useNativeDriver"], [18, 22, 33, 35], [18, 42, 33, 35, "_classPrivateFieldLooseKey2"], [18, 69, 33, 35], [18, 70, 33, 35, "default"], [18, 77, 33, 35], [19, 2, 33, 35], [19, 6, 38, 21, "Animation"], [19, 15, 38, 30], [19, 18, 38, 30, "exports"], [19, 25, 38, 30], [19, 26, 38, 30, "default"], [19, 33, 38, 30], [20, 4, 49, 2], [20, 13, 49, 2, "Animation"], [20, 23, 49, 14, "config"], [20, 29, 49, 37], [20, 31, 49, 39], [21, 6, 49, 39], [21, 10, 49, 39, "_classCallCheck2"], [21, 26, 49, 39], [21, 27, 49, 39, "default"], [21, 34, 49, 39], [21, 42, 49, 39, "Animation"], [21, 51, 49, 39], [22, 6, 49, 39, "Object"], [22, 12, 49, 39], [22, 13, 49, 39, "defineProperty"], [22, 27, 49, 39], [22, 34, 49, 39, "_nativeID"], [22, 43, 49, 39], [23, 8, 49, 39, "writable"], [23, 16, 49, 39], [24, 8, 49, 39, "value"], [24, 13, 49, 39], [25, 6, 49, 39], [26, 6, 49, 39, "Object"], [26, 12, 49, 39], [26, 13, 49, 39, "defineProperty"], [26, 27, 49, 39], [26, 34, 49, 39, "_onEnd"], [26, 40, 49, 39], [27, 8, 49, 39, "writable"], [27, 16, 49, 39], [28, 8, 49, 39, "value"], [28, 13, 49, 39], [29, 6, 49, 39], [30, 6, 49, 39, "Object"], [30, 12, 49, 39], [30, 13, 49, 39, "defineProperty"], [30, 27, 49, 39], [30, 34, 49, 39, "_useNativeDriver"], [30, 50, 49, 39], [31, 8, 49, 39, "writable"], [31, 16, 49, 39], [32, 8, 49, 39, "value"], [32, 13, 49, 39], [33, 6, 49, 39], [34, 6, 50, 4], [34, 10, 50, 4, "_classPrivateFieldLooseBase2"], [34, 38, 50, 4], [34, 39, 50, 4, "default"], [34, 46, 50, 4], [34, 52, 50, 8], [34, 54, 50, 8, "_useNativeDriver"], [34, 70, 50, 8], [34, 72, 50, 8, "_useNativeDriver"], [34, 88, 50, 8], [34, 92, 50, 28, "NativeAnimatedHelper"], [34, 121, 50, 48], [34, 122, 50, 49, "shouldUseNativeDriver"], [34, 143, 50, 70], [34, 144, 50, 71, "config"], [34, 150, 50, 77], [34, 151, 50, 78], [35, 6, 52, 4], [35, 10, 52, 8], [35, 11, 52, 9, "__active"], [35, 19, 52, 17], [35, 22, 52, 20], [35, 27, 52, 25], [36, 6, 53, 4], [36, 10, 53, 8], [36, 11, 53, 9, "__isInteraction"], [36, 26, 53, 24], [36, 29, 53, 27, "config"], [36, 35, 53, 33], [36, 36, 53, 34, "isInteraction"], [36, 49, 53, 47], [36, 53, 53, 51], [36, 58, 53, 51, "_classPrivateFieldLooseBase2"], [36, 86, 53, 51], [36, 87, 53, 51, "default"], [36, 94, 53, 51], [36, 96, 53, 52], [36, 100, 53, 56], [36, 102, 53, 56, "_useNativeDriver"], [36, 118, 53, 56], [36, 120, 53, 56, "_useNativeDriver"], [36, 136, 53, 56], [36, 137, 53, 73], [37, 6, 54, 4], [37, 10, 54, 8], [37, 11, 54, 9, "__isLooping"], [37, 22, 54, 20], [37, 25, 54, 23, "config"], [37, 31, 54, 29], [37, 32, 54, 30, "isLooping"], [37, 41, 54, 39], [38, 6, 55, 4], [38, 10, 55, 8], [38, 11, 55, 9, "__iterations"], [38, 23, 55, 21], [38, 26, 55, 24, "config"], [38, 32, 55, 30], [38, 33, 55, 31, "iterations"], [38, 43, 55, 41], [38, 47, 55, 45], [38, 48, 55, 46], [39, 6, 56, 4], [39, 10, 56, 8, "__DEV__"], [39, 17, 56, 15], [39, 19, 56, 17], [40, 8, 57, 6], [40, 12, 57, 10], [40, 13, 57, 11, "__debugID"], [40, 22, 57, 20], [40, 25, 57, 23, "config"], [40, 31, 57, 29], [40, 32, 57, 30, "debugID"], [40, 39, 57, 37], [41, 6, 58, 4], [42, 4, 59, 2], [43, 4, 59, 3], [43, 15, 59, 3, "_createClass2"], [43, 28, 59, 3], [43, 29, 59, 3, "default"], [43, 36, 59, 3], [43, 38, 59, 3, "Animation"], [43, 47, 59, 3], [44, 6, 59, 3, "key"], [44, 9, 59, 3], [45, 6, 59, 3, "value"], [45, 11, 59, 3], [45, 13, 61, 2], [45, 22, 61, 2, "start"], [45, 27, 61, 7, "start"], [45, 28, 62, 4, "fromValue"], [45, 37, 62, 21], [45, 39, 63, 4, "onUpdate"], [45, 47, 63, 37], [45, 49, 64, 4, "onEnd"], [45, 54, 64, 23], [45, 56, 65, 4, "previousAnimation"], [45, 73, 65, 33], [45, 75, 66, 4, "animatedValue"], [45, 88, 66, 32], [45, 90, 67, 10], [46, 8, 68, 4], [46, 12, 68, 8], [46, 17, 68, 8, "_classPrivateFieldLooseBase2"], [46, 45, 68, 8], [46, 46, 68, 8, "default"], [46, 53, 68, 8], [46, 55, 68, 9], [46, 59, 68, 13], [46, 61, 68, 13, "_useNativeDriver"], [46, 77, 68, 13], [46, 79, 68, 13, "_useNativeDriver"], [46, 95, 68, 13], [46, 96, 68, 30], [46, 100, 68, 34, "animatedValue"], [46, 113, 68, 47], [46, 114, 68, 48, "__isNative"], [46, 124, 68, 58], [46, 129, 68, 63], [46, 133, 68, 67], [46, 135, 68, 69], [47, 10, 69, 6], [47, 16, 69, 12], [47, 20, 69, 16, "Error"], [47, 25, 69, 21], [47, 26, 70, 8], [47, 83, 70, 65], [47, 86, 71, 10], [47, 143, 71, 67], [47, 146, 72, 10], [47, 186, 73, 6], [47, 187, 73, 7], [48, 8, 74, 4], [49, 8, 76, 4], [49, 12, 76, 4, "_classPrivateFieldLooseBase2"], [49, 40, 76, 4], [49, 41, 76, 4, "default"], [49, 48, 76, 4], [49, 54, 76, 8], [49, 56, 76, 8, "_onEnd"], [49, 62, 76, 8], [49, 64, 76, 8, "_onEnd"], [49, 70, 76, 8], [49, 74, 76, 18, "onEnd"], [49, 79, 76, 23], [50, 8, 77, 4], [50, 12, 77, 8], [50, 13, 77, 9, "__active"], [50, 21, 77, 17], [50, 24, 77, 20], [50, 28, 77, 24], [51, 6, 78, 2], [52, 4, 78, 3], [53, 6, 78, 3, "key"], [53, 9, 78, 3], [54, 6, 78, 3, "value"], [54, 11, 78, 3], [54, 13, 80, 2], [54, 22, 80, 2, "stop"], [54, 26, 80, 6, "stop"], [54, 27, 80, 6], [54, 29, 80, 15], [55, 8, 81, 4], [55, 12, 81, 8], [55, 16, 81, 8, "_classPrivateFieldLooseBase2"], [55, 44, 81, 8], [55, 45, 81, 8, "default"], [55, 52, 81, 8], [55, 58, 81, 12], [55, 60, 81, 12, "_nativeID"], [55, 69, 81, 12], [55, 71, 81, 12, "_nativeID"], [55, 80, 81, 12], [55, 85, 81, 26], [55, 89, 81, 30], [55, 91, 81, 32], [56, 10, 82, 6], [56, 14, 82, 12, "nativeID"], [56, 22, 82, 20], [56, 29, 82, 20, "_classPrivateFieldLooseBase2"], [56, 57, 82, 20], [56, 58, 82, 20, "default"], [56, 65, 82, 20], [56, 67, 82, 23], [56, 71, 82, 27], [56, 73, 82, 27, "_nativeID"], [56, 82, 82, 27], [56, 84, 82, 27, "_nativeID"], [56, 93, 82, 27], [56, 94, 82, 37], [57, 10, 83, 6], [57, 14, 83, 12, "identifier"], [57, 24, 83, 22], [57, 27, 83, 25], [57, 30, 83, 28, "nativeID"], [57, 38, 83, 36], [57, 54, 83, 52], [58, 10, 84, 6], [58, 14, 84, 10], [59, 12, 87, 8, "NativeAnimatedHelper"], [59, 41, 87, 28], [59, 42, 87, 29, "API"], [59, 45, 87, 32], [59, 46, 87, 33, "setWaitingForIdentifier"], [59, 69, 87, 56], [59, 70, 87, 57, "identifier"], [59, 80, 87, 67], [59, 81, 87, 68], [60, 12, 88, 8, "NativeAnimatedHelper"], [60, 41, 88, 28], [60, 42, 88, 29, "API"], [60, 45, 88, 32], [60, 46, 88, 33, "stopAnimation"], [60, 59, 88, 46], [60, 60, 88, 47, "nativeID"], [60, 68, 88, 55], [60, 69, 88, 56], [61, 10, 89, 6], [61, 11, 89, 7], [61, 20, 89, 16], [62, 12, 90, 8, "NativeAnimatedHelper"], [62, 41, 90, 28], [62, 42, 90, 29, "API"], [62, 45, 90, 32], [62, 46, 90, 33, "unsetWaitingForIdentifier"], [62, 71, 90, 58], [62, 72, 90, 59, "identifier"], [62, 82, 90, 69], [62, 83, 90, 70], [63, 10, 91, 6], [64, 8, 92, 4], [65, 8, 93, 4], [65, 12, 93, 8], [65, 13, 93, 9, "__active"], [65, 21, 93, 17], [65, 24, 93, 20], [65, 29, 93, 25], [66, 6, 94, 2], [67, 4, 94, 3], [68, 6, 94, 3, "key"], [68, 9, 94, 3], [69, 6, 94, 3, "value"], [69, 11, 94, 3], [69, 13, 96, 2], [69, 22, 96, 2, "__getNativeAnimationConfig"], [69, 48, 96, 28, "__getNativeAnimationConfig"], [69, 49, 96, 28], [69, 51, 99, 5], [70, 8, 102, 4], [70, 14, 102, 10], [70, 18, 102, 14, "Error"], [70, 23, 102, 19], [70, 24, 102, 20], [70, 75, 102, 71], [70, 76, 102, 72], [71, 6, 103, 2], [72, 4, 103, 3], [73, 6, 103, 3, "key"], [73, 9, 103, 3], [74, 6, 103, 3, "value"], [74, 11, 103, 3], [74, 13, 105, 2], [74, 22, 105, 2, "__findAnimatedPropsNodes"], [74, 46, 105, 26, "__findAnimatedPropsNodes"], [74, 47, 105, 27, "node"], [74, 51, 105, 45], [74, 53, 105, 69], [75, 8, 106, 4], [75, 12, 106, 10, "result"], [75, 18, 106, 16], [75, 21, 106, 19], [75, 23, 106, 21], [76, 8, 108, 4], [76, 12, 108, 8, "node"], [76, 16, 108, 12], [76, 28, 108, 24, "AnimatedProps"], [76, 50, 108, 37], [76, 52, 108, 39], [77, 10, 109, 6, "result"], [77, 16, 109, 12], [77, 17, 109, 13, "push"], [77, 21, 109, 17], [77, 22, 109, 18, "node"], [77, 26, 109, 22], [77, 27, 109, 23], [78, 10, 110, 6], [78, 17, 110, 13, "result"], [78, 23, 110, 19], [79, 8, 111, 4], [80, 8, 113, 4], [80, 13, 113, 9], [80, 17, 113, 15, "child"], [80, 22, 113, 20], [80, 26, 113, 24, "node"], [80, 30, 113, 28], [80, 31, 113, 29, "__get<PERSON><PERSON><PERSON><PERSON>"], [80, 44, 113, 42], [80, 45, 113, 43], [80, 46, 113, 44], [80, 48, 113, 46], [81, 10, 114, 6, "result"], [81, 16, 114, 12], [81, 17, 114, 13, "push"], [81, 21, 114, 17], [81, 22, 114, 18], [81, 25, 114, 21], [81, 29, 114, 25], [81, 30, 114, 26, "__findAnimatedPropsNodes"], [81, 54, 114, 50], [81, 55, 114, 51, "child"], [81, 60, 114, 56], [81, 61, 114, 57], [81, 62, 114, 58], [82, 8, 115, 4], [83, 8, 117, 4], [83, 15, 117, 11, "result"], [83, 21, 117, 17], [84, 6, 118, 2], [85, 4, 118, 3], [86, 6, 118, 3, "key"], [86, 9, 118, 3], [87, 6, 118, 3, "value"], [87, 11, 118, 3], [87, 13, 120, 2], [87, 22, 120, 2, "__startAnimationIfNative"], [87, 46, 120, 26, "__startAnimationIfNative"], [87, 47, 120, 27, "animatedValue"], [87, 60, 120, 55], [87, 62, 120, 66], [88, 8, 121, 4], [88, 12, 121, 8], [88, 17, 121, 8, "_classPrivateFieldLooseBase2"], [88, 45, 121, 8], [88, 46, 121, 8, "default"], [88, 53, 121, 8], [88, 55, 121, 9], [88, 59, 121, 13], [88, 61, 121, 13, "_useNativeDriver"], [88, 77, 121, 13], [88, 79, 121, 13, "_useNativeDriver"], [88, 95, 121, 13], [88, 96, 121, 30], [88, 98, 121, 32], [89, 10, 122, 6], [89, 17, 122, 13], [89, 22, 122, 18], [90, 8, 123, 4], [91, 8, 125, 4], [91, 12, 125, 10, "startNativeAnimationWaitId"], [91, 38, 125, 36], [91, 41, 125, 39], [91, 44, 125, 42, "startNativeAnimationNextId"], [91, 70, 125, 68], [91, 87, 125, 85], [92, 8, 126, 4, "startNativeAnimationNextId"], [92, 34, 126, 30], [92, 38, 126, 34], [92, 39, 126, 35], [93, 8, 127, 4, "NativeAnimatedHelper"], [93, 37, 127, 24], [93, 38, 127, 25, "API"], [93, 41, 127, 28], [93, 42, 127, 29, "setWaitingForIdentifier"], [93, 65, 127, 52], [93, 66, 128, 6, "startNativeAnimationWaitId"], [93, 92, 129, 4], [93, 93, 129, 5], [94, 8, 130, 4], [94, 12, 130, 8], [95, 10, 131, 6], [95, 14, 131, 12, "config"], [95, 20, 131, 18], [95, 23, 131, 21], [95, 27, 131, 25], [95, 28, 131, 26, "__getNativeAnimationConfig"], [95, 54, 131, 52], [95, 55, 131, 53], [95, 56, 131, 54], [96, 10, 132, 6, "animatedValue"], [96, 23, 132, 19], [96, 24, 132, 20, "__makeNative"], [96, 36, 132, 32], [96, 37, 132, 33, "config"], [96, 43, 132, 39], [96, 44, 132, 40, "platformConfig"], [96, 58, 132, 54], [96, 59, 132, 55], [97, 10, 133, 6], [97, 14, 133, 6, "_classPrivateFieldLooseBase2"], [97, 42, 133, 6], [97, 43, 133, 6, "default"], [97, 50, 133, 6], [97, 56, 133, 10], [97, 58, 133, 10, "_nativeID"], [97, 67, 133, 10], [97, 69, 133, 10, "_nativeID"], [97, 78, 133, 10], [97, 82, 133, 23, "NativeAnimatedHelper"], [97, 111, 133, 43], [97, 112, 133, 44, "generateNewAnimationId"], [97, 134, 133, 66], [97, 135, 133, 67], [97, 136, 133, 68], [98, 10, 134, 6, "NativeAnimatedHelper"], [98, 39, 134, 26], [98, 40, 134, 27, "API"], [98, 43, 134, 30], [98, 44, 134, 31, "startAnimatingNode"], [98, 62, 134, 49], [98, 67, 134, 49, "_classPrivateFieldLooseBase2"], [98, 95, 134, 49], [98, 96, 134, 49, "default"], [98, 103, 134, 49], [98, 105, 135, 8], [98, 109, 135, 12], [98, 111, 135, 12, "_nativeID"], [98, 120, 135, 12], [98, 122, 135, 12, "_nativeID"], [98, 131, 135, 12], [98, 134, 136, 8, "animatedValue"], [98, 147, 136, 21], [98, 148, 136, 22, "__getNativeTag"], [98, 162, 136, 36], [98, 163, 136, 37], [98, 164, 136, 38], [98, 166, 137, 8, "config"], [98, 172, 137, 14], [98, 174, 138, 8, "result"], [98, 180, 138, 14], [98, 184, 138, 18], [99, 12, 139, 10], [99, 16, 139, 14], [99, 17, 139, 15, "__notifyAnimationEnd"], [99, 37, 139, 35], [99, 38, 139, 36, "result"], [99, 44, 139, 42], [99, 45, 139, 43], [100, 12, 144, 10], [100, 16, 144, 17, "value"], [100, 21, 144, 22], [100, 24, 144, 26, "result"], [100, 30, 144, 32], [100, 31, 144, 17, "value"], [100, 36, 144, 22], [101, 12, 145, 10], [101, 16, 145, 14, "value"], [101, 21, 145, 19], [101, 25, 145, 23], [101, 29, 145, 27], [101, 31, 145, 29], [102, 14, 146, 12, "animatedValue"], [102, 27, 146, 25], [102, 28, 146, 26, "__onAnimatedValueUpdateReceived"], [102, 59, 146, 57], [102, 60, 146, 58, "value"], [102, 65, 146, 63], [102, 66, 146, 64], [103, 14, 148, 12], [103, 18, 148, 16], [103, 22, 148, 20], [103, 23, 148, 21, "__isLooping"], [103, 34, 148, 32], [103, 39, 148, 37], [103, 43, 148, 41], [103, 45, 148, 43], [104, 16, 149, 14], [105, 14, 150, 12], [106, 14, 154, 12], [106, 18, 154, 16], [106, 19, 154, 17, "__findAnimatedPropsNodes"], [106, 43, 154, 41], [106, 44, 154, 42, "animatedValue"], [106, 57, 154, 55], [106, 58, 154, 56], [106, 59, 154, 57, "for<PERSON>ach"], [106, 66, 154, 64], [106, 67, 154, 65, "node"], [106, 71, 154, 69], [106, 75, 155, 14, "node"], [106, 79, 155, 18], [106, 80, 155, 19, "update"], [106, 86, 155, 25], [106, 87, 155, 26], [106, 88, 156, 12], [106, 89, 156, 13], [107, 12, 157, 10], [108, 10, 158, 8], [108, 11, 159, 6], [108, 12, 159, 7], [109, 10, 161, 6], [109, 17, 161, 13], [109, 21, 161, 17], [110, 8, 162, 4], [110, 9, 162, 5], [110, 10, 162, 6], [110, 17, 162, 13, "e"], [110, 18, 162, 14], [110, 20, 162, 16], [111, 10, 163, 6], [111, 16, 163, 12, "e"], [111, 17, 163, 13], [112, 8, 164, 4], [112, 9, 164, 5], [112, 18, 164, 14], [113, 10, 165, 6, "NativeAnimatedHelper"], [113, 39, 165, 26], [113, 40, 165, 27, "API"], [113, 43, 165, 30], [113, 44, 165, 31, "unsetWaitingForIdentifier"], [113, 69, 165, 56], [113, 70, 166, 8, "startNativeAnimationWaitId"], [113, 96, 167, 6], [113, 97, 167, 7], [114, 8, 168, 4], [115, 6, 169, 2], [116, 4, 169, 3], [117, 6, 169, 3, "key"], [117, 9, 169, 3], [118, 6, 169, 3, "value"], [118, 11, 169, 3], [118, 13, 175, 2], [118, 22, 175, 2, "__notifyAnimationEnd"], [118, 42, 175, 22, "__notifyAnimationEnd"], [118, 43, 175, 23, "result"], [118, 49, 175, 40], [118, 51, 175, 48], [119, 8, 176, 4], [119, 12, 176, 10, "callback"], [119, 20, 176, 18], [119, 27, 176, 18, "_classPrivateFieldLooseBase2"], [119, 55, 176, 18], [119, 56, 176, 18, "default"], [119, 63, 176, 18], [119, 65, 176, 21], [119, 69, 176, 25], [119, 71, 176, 25, "_onEnd"], [119, 77, 176, 25], [119, 79, 176, 25, "_onEnd"], [119, 85, 176, 25], [119, 86, 176, 32], [120, 8, 177, 4], [120, 12, 177, 8, "callback"], [120, 20, 177, 16], [120, 24, 177, 20], [120, 28, 177, 24], [120, 30, 177, 26], [121, 10, 178, 6], [121, 14, 178, 6, "_classPrivateFieldLooseBase2"], [121, 42, 178, 6], [121, 43, 178, 6, "default"], [121, 50, 178, 6], [121, 56, 178, 10], [121, 58, 178, 10, "_onEnd"], [121, 64, 178, 10], [121, 66, 178, 10, "_onEnd"], [121, 72, 178, 10], [121, 76, 178, 20], [121, 80, 178, 24], [122, 10, 179, 6, "callback"], [122, 18, 179, 14], [122, 19, 179, 15, "result"], [122, 25, 179, 21], [122, 26, 179, 22], [123, 8, 180, 4], [124, 6, 181, 2], [125, 4, 181, 3], [126, 6, 181, 3, "key"], [126, 9, 181, 3], [127, 6, 181, 3, "value"], [127, 11, 181, 3], [127, 13, 183, 2], [127, 22, 183, 2, "__getDebugID"], [127, 34, 183, 14, "__getDebugID"], [127, 35, 183, 14], [127, 37, 183, 26], [128, 8, 184, 4], [128, 12, 184, 8, "__DEV__"], [128, 19, 184, 15], [128, 21, 184, 17], [129, 10, 185, 6], [129, 17, 185, 13], [129, 21, 185, 17], [129, 22, 185, 18, "__debugID"], [129, 31, 185, 27], [130, 8, 186, 4], [131, 8, 187, 4], [131, 15, 187, 11, "undefined"], [131, 24, 187, 20], [132, 6, 188, 2], [133, 4, 188, 3], [134, 2, 188, 3], [135, 0, 188, 3], [135, 3]], "functionMap": {"names": ["<global>", "Animation", "constructor", "start", "stop", "__getNativeAnimationConfig", "__findAnimatedPropsNodes", "__startAnimationIfNative", "NativeAnimatedHelper.API.startAnimatingNode$argument_3", "__findAnimatedPropsNodes.forEach$argument_0", "__notifyAnimationEnd", "__getDebugID"], "mappings": "AAA;eCqC;ECW;GDU;EEE;GFiB;EGE;GHc;EIE;GJO;EKE;GLa;EME;QCkB;iECgB;2BDC;SDG;GNW;ESM;GTM;EUE;GVK"}}, "type": "js/module"}]}