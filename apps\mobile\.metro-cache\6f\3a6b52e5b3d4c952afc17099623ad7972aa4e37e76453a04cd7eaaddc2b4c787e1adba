{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 43}}], "key": "G/V58dT936wq645V8EjZl0XZN3w=", "exportNames": ["*"]}}, {"name": "../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 50}}], "key": "4Y0hmo08o8yJvREbRM/f/cgl9pQ=", "exportNames": ["*"]}}, {"name": "./RootTag", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 56}}], "key": "b/YPZRk6P0xMk9igJT8xkfjtwVw=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _View = _interopRequireDefault(require(_dependencyMap[1], \"../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[2], \"../StyleSheet/StyleSheet\"));\n  var _RootTag = require(_dependencyMap[3], \"./RootTag\");\n  var React = _interopRequireWildcard(require(_dependencyMap[4], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[5], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\ReactNative\\\\AppContainer-prod.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var AppContainer = _ref => {\n    var children = _ref.children,\n      fabric = _ref.fabric,\n      initialProps = _ref.initialProps,\n      rootTag = _ref.rootTag,\n      WrapperComponent = _ref.WrapperComponent,\n      rootViewStyle = _ref.rootViewStyle;\n    var innerView = children;\n    if (WrapperComponent != null) {\n      innerView = /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(WrapperComponent, {\n        initialProps: initialProps,\n        fabric: fabric === true,\n        children: innerView\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 7\n      }, this);\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_RootTag.RootTagContext.Provider, {\n      value: (0, _RootTag.createRootTag)(rootTag),\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: rootViewStyle || styles.root,\n        pointerEvents: \"box-none\",\n        children: innerView\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 5\n    }, this);\n  };\n  var styles = _StyleSheet.default.create({\n    root: {\n      flex: 1\n    }\n  });\n  var _default = exports.default = AppContainer;\n});", "lineCount": 56, "map": [[7, 2, 14, 0], [7, 6, 14, 0, "_View"], [7, 11, 14, 0], [7, 14, 14, 0, "_interopRequireDefault"], [7, 36, 14, 0], [7, 37, 14, 0, "require"], [7, 44, 14, 0], [7, 45, 14, 0, "_dependencyMap"], [7, 59, 14, 0], [8, 2, 15, 0], [8, 6, 15, 0, "_StyleSheet"], [8, 17, 15, 0], [8, 20, 15, 0, "_interopRequireDefault"], [8, 42, 15, 0], [8, 43, 15, 0, "require"], [8, 50, 15, 0], [8, 51, 15, 0, "_dependencyMap"], [8, 65, 15, 0], [9, 2, 16, 0], [9, 6, 16, 0, "_RootTag"], [9, 14, 16, 0], [9, 17, 16, 0, "require"], [9, 24, 16, 0], [9, 25, 16, 0, "_dependencyMap"], [9, 39, 16, 0], [10, 2, 17, 0], [10, 6, 17, 0, "React"], [10, 11, 17, 0], [10, 14, 17, 0, "_interopRequireWildcard"], [10, 37, 17, 0], [10, 38, 17, 0, "require"], [10, 45, 17, 0], [10, 46, 17, 0, "_dependencyMap"], [10, 60, 17, 0], [11, 2, 17, 31], [11, 6, 17, 31, "_jsxDevRuntime"], [11, 20, 17, 31], [11, 23, 17, 31, "require"], [11, 30, 17, 31], [11, 31, 17, 31, "_dependencyMap"], [11, 45, 17, 31], [12, 2, 17, 31], [12, 6, 17, 31, "_jsxFileName"], [12, 18, 17, 31], [13, 2, 17, 31], [13, 11, 17, 31, "_interopRequireWildcard"], [13, 35, 17, 31, "e"], [13, 36, 17, 31], [13, 38, 17, 31, "t"], [13, 39, 17, 31], [13, 68, 17, 31, "WeakMap"], [13, 75, 17, 31], [13, 81, 17, 31, "r"], [13, 82, 17, 31], [13, 89, 17, 31, "WeakMap"], [13, 96, 17, 31], [13, 100, 17, 31, "n"], [13, 101, 17, 31], [13, 108, 17, 31, "WeakMap"], [13, 115, 17, 31], [13, 127, 17, 31, "_interopRequireWildcard"], [13, 150, 17, 31], [13, 162, 17, 31, "_interopRequireWildcard"], [13, 163, 17, 31, "e"], [13, 164, 17, 31], [13, 166, 17, 31, "t"], [13, 167, 17, 31], [13, 176, 17, 31, "t"], [13, 177, 17, 31], [13, 181, 17, 31, "e"], [13, 182, 17, 31], [13, 186, 17, 31, "e"], [13, 187, 17, 31], [13, 188, 17, 31, "__esModule"], [13, 198, 17, 31], [13, 207, 17, 31, "e"], [13, 208, 17, 31], [13, 214, 17, 31, "o"], [13, 215, 17, 31], [13, 217, 17, 31, "i"], [13, 218, 17, 31], [13, 220, 17, 31, "f"], [13, 221, 17, 31], [13, 226, 17, 31, "__proto__"], [13, 235, 17, 31], [13, 243, 17, 31, "default"], [13, 250, 17, 31], [13, 252, 17, 31, "e"], [13, 253, 17, 31], [13, 270, 17, 31, "e"], [13, 271, 17, 31], [13, 294, 17, 31, "e"], [13, 295, 17, 31], [13, 320, 17, 31, "e"], [13, 321, 17, 31], [13, 330, 17, 31, "f"], [13, 331, 17, 31], [13, 337, 17, 31, "o"], [13, 338, 17, 31], [13, 341, 17, 31, "t"], [13, 342, 17, 31], [13, 345, 17, 31, "n"], [13, 346, 17, 31], [13, 349, 17, 31, "r"], [13, 350, 17, 31], [13, 358, 17, 31, "o"], [13, 359, 17, 31], [13, 360, 17, 31, "has"], [13, 363, 17, 31], [13, 364, 17, 31, "e"], [13, 365, 17, 31], [13, 375, 17, 31, "o"], [13, 376, 17, 31], [13, 377, 17, 31, "get"], [13, 380, 17, 31], [13, 381, 17, 31, "e"], [13, 382, 17, 31], [13, 385, 17, 31, "o"], [13, 386, 17, 31], [13, 387, 17, 31, "set"], [13, 390, 17, 31], [13, 391, 17, 31, "e"], [13, 392, 17, 31], [13, 394, 17, 31, "f"], [13, 395, 17, 31], [13, 409, 17, 31, "_t"], [13, 411, 17, 31], [13, 415, 17, 31, "e"], [13, 416, 17, 31], [13, 432, 17, 31, "_t"], [13, 434, 17, 31], [13, 441, 17, 31, "hasOwnProperty"], [13, 455, 17, 31], [13, 456, 17, 31, "call"], [13, 460, 17, 31], [13, 461, 17, 31, "e"], [13, 462, 17, 31], [13, 464, 17, 31, "_t"], [13, 466, 17, 31], [13, 473, 17, 31, "i"], [13, 474, 17, 31], [13, 478, 17, 31, "o"], [13, 479, 17, 31], [13, 482, 17, 31, "Object"], [13, 488, 17, 31], [13, 489, 17, 31, "defineProperty"], [13, 503, 17, 31], [13, 508, 17, 31, "Object"], [13, 514, 17, 31], [13, 515, 17, 31, "getOwnPropertyDescriptor"], [13, 539, 17, 31], [13, 540, 17, 31, "e"], [13, 541, 17, 31], [13, 543, 17, 31, "_t"], [13, 545, 17, 31], [13, 552, 17, 31, "i"], [13, 553, 17, 31], [13, 554, 17, 31, "get"], [13, 557, 17, 31], [13, 561, 17, 31, "i"], [13, 562, 17, 31], [13, 563, 17, 31, "set"], [13, 566, 17, 31], [13, 570, 17, 31, "o"], [13, 571, 17, 31], [13, 572, 17, 31, "f"], [13, 573, 17, 31], [13, 575, 17, 31, "_t"], [13, 577, 17, 31], [13, 579, 17, 31, "i"], [13, 580, 17, 31], [13, 584, 17, 31, "f"], [13, 585, 17, 31], [13, 586, 17, 31, "_t"], [13, 588, 17, 31], [13, 592, 17, 31, "e"], [13, 593, 17, 31], [13, 594, 17, 31, "_t"], [13, 596, 17, 31], [13, 607, 17, 31, "f"], [13, 608, 17, 31], [13, 613, 17, 31, "e"], [13, 614, 17, 31], [13, 616, 17, 31, "t"], [13, 617, 17, 31], [14, 2, 19, 0], [14, 6, 19, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [14, 18, 19, 18], [14, 21, 19, 21, "_ref"], [14, 25, 19, 21], [14, 29, 26, 25], [15, 4, 26, 25], [15, 8, 20, 2, "children"], [15, 16, 20, 10], [15, 19, 20, 10, "_ref"], [15, 23, 20, 10], [15, 24, 20, 2, "children"], [15, 32, 20, 10], [16, 6, 21, 2, "fabric"], [16, 12, 21, 8], [16, 15, 21, 8, "_ref"], [16, 19, 21, 8], [16, 20, 21, 2, "fabric"], [16, 26, 21, 8], [17, 6, 22, 2, "initialProps"], [17, 18, 22, 14], [17, 21, 22, 14, "_ref"], [17, 25, 22, 14], [17, 26, 22, 2, "initialProps"], [17, 38, 22, 14], [18, 6, 23, 2, "rootTag"], [18, 13, 23, 9], [18, 16, 23, 9, "_ref"], [18, 20, 23, 9], [18, 21, 23, 2, "rootTag"], [18, 28, 23, 9], [19, 6, 24, 2, "WrapperComponent"], [19, 22, 24, 18], [19, 25, 24, 18, "_ref"], [19, 29, 24, 18], [19, 30, 24, 2, "WrapperComponent"], [19, 46, 24, 18], [20, 6, 25, 2, "rootViewStyle"], [20, 19, 25, 15], [20, 22, 25, 15, "_ref"], [20, 26, 25, 15], [20, 27, 25, 2, "rootViewStyle"], [20, 40, 25, 15], [21, 4, 27, 2], [21, 8, 27, 6, "innerView"], [21, 17, 27, 15], [21, 20, 27, 18, "children"], [21, 28, 27, 26], [22, 4, 29, 2], [22, 8, 29, 6, "WrapperComponent"], [22, 24, 29, 22], [22, 28, 29, 26], [22, 32, 29, 30], [22, 34, 29, 32], [23, 6, 30, 4, "innerView"], [23, 15, 30, 13], [23, 31, 31, 6], [23, 35, 31, 6, "_jsxDevRuntime"], [23, 49, 31, 6], [23, 50, 31, 6, "jsxDEV"], [23, 56, 31, 6], [23, 58, 31, 7, "WrapperComponent"], [23, 74, 31, 23], [24, 8, 31, 24, "initialProps"], [24, 20, 31, 36], [24, 22, 31, 38, "initialProps"], [24, 34, 31, 51], [25, 8, 31, 52, "fabric"], [25, 14, 31, 58], [25, 16, 31, 60, "fabric"], [25, 22, 31, 66], [25, 27, 31, 71], [25, 31, 31, 76], [26, 8, 31, 76, "children"], [26, 16, 31, 76], [26, 18, 32, 9, "innerView"], [27, 6, 32, 18], [28, 8, 32, 18, "fileName"], [28, 16, 32, 18], [28, 18, 32, 18, "_jsxFileName"], [28, 30, 32, 18], [29, 8, 32, 18, "lineNumber"], [29, 18, 32, 18], [30, 8, 32, 18, "columnNumber"], [30, 20, 32, 18], [31, 6, 32, 18], [31, 13, 33, 24], [31, 14, 34, 5], [32, 4, 35, 2], [33, 4, 37, 2], [33, 24, 38, 4], [33, 28, 38, 4, "_jsxDevRuntime"], [33, 42, 38, 4], [33, 43, 38, 4, "jsxDEV"], [33, 49, 38, 4], [33, 51, 38, 5, "_RootTag"], [33, 59, 38, 5], [33, 60, 38, 5, "RootTagContext"], [33, 74, 38, 19], [33, 75, 38, 20, "Provider"], [33, 83, 38, 28], [34, 6, 38, 29, "value"], [34, 11, 38, 34], [34, 13, 38, 36], [34, 17, 38, 36, "createRootTag"], [34, 39, 38, 49], [34, 41, 38, 50, "rootTag"], [34, 48, 38, 57], [34, 49, 38, 59], [35, 6, 38, 59, "children"], [35, 14, 38, 59], [35, 29, 39, 6], [35, 33, 39, 6, "_jsxDevRuntime"], [35, 47, 39, 6], [35, 48, 39, 6, "jsxDEV"], [35, 54, 39, 6], [35, 56, 39, 7, "_View"], [35, 61, 39, 7], [35, 62, 39, 7, "default"], [35, 69, 39, 11], [36, 8, 39, 12, "style"], [36, 13, 39, 17], [36, 15, 39, 19, "rootViewStyle"], [36, 28, 39, 32], [36, 32, 39, 36, "styles"], [36, 38, 39, 42], [36, 39, 39, 43, "root"], [36, 43, 39, 48], [37, 8, 39, 49, "pointerEvents"], [37, 21, 39, 62], [37, 23, 39, 63], [37, 33, 39, 73], [38, 8, 39, 73, "children"], [38, 16, 39, 73], [38, 18, 40, 9, "innerView"], [39, 6, 40, 18], [40, 8, 40, 18, "fileName"], [40, 16, 40, 18], [40, 18, 40, 18, "_jsxFileName"], [40, 30, 40, 18], [41, 8, 40, 18, "lineNumber"], [41, 18, 40, 18], [42, 8, 40, 18, "columnNumber"], [42, 20, 40, 18], [43, 6, 40, 18], [43, 13, 41, 12], [44, 4, 41, 13], [45, 6, 41, 13, "fileName"], [45, 14, 41, 13], [45, 16, 41, 13, "_jsxFileName"], [45, 28, 41, 13], [46, 6, 41, 13, "lineNumber"], [46, 16, 41, 13], [47, 6, 41, 13, "columnNumber"], [47, 18, 41, 13], [48, 4, 41, 13], [48, 11, 42, 29], [48, 12, 42, 30], [49, 2, 44, 0], [49, 3, 44, 1], [50, 2, 46, 0], [50, 6, 46, 6, "styles"], [50, 12, 46, 12], [50, 15, 46, 15, "StyleSheet"], [50, 34, 46, 25], [50, 35, 46, 26, "create"], [50, 41, 46, 32], [50, 42, 46, 33], [51, 4, 47, 2, "root"], [51, 8, 47, 6], [51, 10, 47, 8], [52, 6, 47, 9, "flex"], [52, 10, 47, 13], [52, 12, 47, 15], [53, 4, 47, 16], [54, 2, 48, 0], [54, 3, 48, 1], [54, 4, 48, 2], [55, 2, 48, 3], [55, 6, 48, 3, "_default"], [55, 14, 48, 3], [55, 17, 48, 3, "exports"], [55, 24, 48, 3], [55, 25, 48, 3, "default"], [55, 32, 48, 3], [55, 35, 50, 15, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [55, 47, 50, 27], [56, 0, 50, 27], [56, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;qBCkB;CDyB"}}, "type": "js/module"}]}