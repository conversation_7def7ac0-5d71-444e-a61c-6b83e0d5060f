{"dependencies": [{"name": "./typeof.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 14, "index": 14}, "end": {"line": 1, "column": 36, "index": 36}}], "key": "3Tw2gscGXiWiW1gdVz1cVllPjgA=", "exportNames": ["*"]}}, {"name": "./assertThisInitialized.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 2, "column": 28, "index": 77}, "end": {"line": 2, "column": 65, "index": 114}}], "key": "VqeWihAOLSqIiCDc/vlin2ZtF4Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _typeof = require(_dependencyMap[0], \"./typeof.js\")[\"default\"];\n  var assertThisInitialized = require(_dependencyMap[1], \"./assertThisInitialized.js\");\n  function _possibleConstructorReturn(t, e) {\n    if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n    if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n    return assertThisInitialized(t);\n  }\n  module.exports = _possibleConstructorReturn, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 10, "map": [[2, 2, 1, 0], [2, 6, 1, 4, "_typeof"], [2, 13, 1, 11], [2, 16, 1, 14, "require"], [2, 23, 1, 21], [2, 24, 1, 21, "_dependencyMap"], [2, 38, 1, 21], [2, 56, 1, 35], [2, 57, 1, 36], [2, 58, 1, 37], [2, 67, 1, 46], [2, 68, 1, 47], [3, 2, 2, 0], [3, 6, 2, 4, "assertThisInitialized"], [3, 27, 2, 25], [3, 30, 2, 28, "require"], [3, 37, 2, 35], [3, 38, 2, 35, "_dependencyMap"], [3, 52, 2, 35], [3, 85, 2, 64], [3, 86, 2, 65], [4, 2, 3, 0], [4, 11, 3, 9, "_possibleConstructorReturn"], [4, 37, 3, 35, "_possibleConstructorReturn"], [4, 38, 3, 36, "t"], [4, 39, 3, 37], [4, 41, 3, 39, "e"], [4, 42, 3, 40], [4, 44, 3, 42], [5, 4, 4, 2], [5, 8, 4, 6, "e"], [5, 9, 4, 7], [5, 14, 4, 12], [5, 22, 4, 20], [5, 26, 4, 24, "_typeof"], [5, 33, 4, 31], [5, 34, 4, 32, "e"], [5, 35, 4, 33], [5, 36, 4, 34], [5, 40, 4, 38], [5, 50, 4, 48], [5, 54, 4, 52], [5, 61, 4, 59, "e"], [5, 62, 4, 60], [5, 63, 4, 61], [5, 65, 4, 63], [5, 72, 4, 70, "e"], [5, 73, 4, 71], [6, 4, 5, 2], [6, 8, 5, 6], [6, 13, 5, 11], [6, 14, 5, 12], [6, 19, 5, 17, "e"], [6, 20, 5, 18], [6, 22, 5, 20], [6, 28, 5, 26], [6, 32, 5, 30, "TypeError"], [6, 41, 5, 39], [6, 42, 5, 40], [6, 100, 5, 98], [6, 101, 5, 99], [7, 4, 6, 2], [7, 11, 6, 9, "assertThisInitialized"], [7, 32, 6, 30], [7, 33, 6, 31, "t"], [7, 34, 6, 32], [7, 35, 6, 33], [8, 2, 7, 0], [9, 2, 8, 0, "module"], [9, 8, 8, 6], [9, 9, 8, 7, "exports"], [9, 16, 8, 14], [9, 19, 8, 17, "_possibleConstructorReturn"], [9, 45, 8, 43], [9, 47, 8, 45, "module"], [9, 53, 8, 51], [9, 54, 8, 52, "exports"], [9, 61, 8, 59], [9, 62, 8, 60, "__esModule"], [9, 72, 8, 70], [9, 75, 8, 73], [9, 79, 8, 77], [9, 81, 8, 79, "module"], [9, 87, 8, 85], [9, 88, 8, 86, "exports"], [9, 95, 8, 93], [9, 96, 8, 94], [9, 105, 8, 103], [9, 106, 8, 104], [9, 109, 8, 107, "module"], [9, 115, 8, 113], [9, 116, 8, 114, "exports"], [9, 123, 8, 121], [10, 0, 8, 122], [10, 3]], "functionMap": {"names": ["<global>", "_possibleConstructorReturn"], "mappings": "AAA;ACE;CDI"}}, "type": "js/module"}]}