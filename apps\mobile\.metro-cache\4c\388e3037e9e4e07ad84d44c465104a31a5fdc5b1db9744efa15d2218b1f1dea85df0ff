{"dependencies": [{"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "color-string", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 20, "index": 20}, "end": {"line": 1, "column": 43, "index": 43}}], "key": "KeO811fmPbkLx9IIySSOBUsX2ME=", "exportNames": ["*"]}}, {"name": "color-convert", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 2, "column": 16, "index": 61}, "end": {"line": 2, "column": 40, "index": 85}}], "key": "dzDKTMMML6wLGBvsTcvhax7VAwQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _slicedToArray = require(_dependencyMap[0], \"@babel/runtime/helpers/slicedToArray\");\n  var colorString = require(_dependencyMap[1], \"color-string\");\n  var convert = require(_dependencyMap[2], \"color-convert\");\n  var skippedModels = [\n  // To be honest, I don't really feel like keyword belongs in color convert, but eh.\n  'keyword',\n  // Gray conflicts with some method names, and has its own method defined.\n  'gray',\n  // Shouldn't really be in color-convert either...\n  'hex'];\n  var hashedModelKeys = {};\n  for (var model of Object.keys(convert)) {\n    hashedModelKeys[[...convert[model].labels].sort().join('')] = model;\n  }\n  var limiters = {};\n  function Color(object, model) {\n    if (!(this instanceof Color)) {\n      return new Color(object, model);\n    }\n    if (model && model in skippedModels) {\n      model = null;\n    }\n    if (model && !(model in convert)) {\n      throw new Error('Unknown model: ' + model);\n    }\n    var i;\n    var channels;\n    if (object == null) {\n      // eslint-disable-line no-eq-null,eqeqeq\n      this.model = 'rgb';\n      this.color = [0, 0, 0];\n      this.valpha = 1;\n    } else if (object instanceof Color) {\n      this.model = object.model;\n      this.color = [...object.color];\n      this.valpha = object.valpha;\n    } else if (typeof object === 'string') {\n      var result = colorString.get(object);\n      if (result === null) {\n        throw new Error('Unable to parse color from string: ' + object);\n      }\n      this.model = result.model;\n      channels = convert[this.model].channels;\n      this.color = result.value.slice(0, channels);\n      this.valpha = typeof result.value[channels] === 'number' ? result.value[channels] : 1;\n    } else if (object.length > 0) {\n      this.model = model || 'rgb';\n      channels = convert[this.model].channels;\n      var newArray = Array.prototype.slice.call(object, 0, channels);\n      this.color = zeroArray(newArray, channels);\n      this.valpha = typeof object[channels] === 'number' ? object[channels] : 1;\n    } else if (typeof object === 'number') {\n      // This is always RGB - can be converted later on.\n      this.model = 'rgb';\n      this.color = [object >> 16 & 0xFF, object >> 8 & 0xFF, object & 0xFF];\n      this.valpha = 1;\n    } else {\n      this.valpha = 1;\n      var keys = Object.keys(object);\n      if ('alpha' in object) {\n        keys.splice(keys.indexOf('alpha'), 1);\n        this.valpha = typeof object.alpha === 'number' ? object.alpha : 0;\n      }\n      var hashedKeys = keys.sort().join('');\n      if (!(hashedKeys in hashedModelKeys)) {\n        throw new Error('Unable to parse color from object: ' + JSON.stringify(object));\n      }\n      this.model = hashedModelKeys[hashedKeys];\n      var labels = convert[this.model].labels;\n      var color = [];\n      for (i = 0; i < labels.length; i++) {\n        color.push(object[labels[i]]);\n      }\n      this.color = zeroArray(color);\n    }\n\n    // Perform limitations (clamping, etc.)\n    if (limiters[this.model]) {\n      channels = convert[this.model].channels;\n      for (i = 0; i < channels; i++) {\n        var limit = limiters[this.model][i];\n        if (limit) {\n          this.color[i] = limit(this.color[i]);\n        }\n      }\n    }\n    this.valpha = Math.max(0, Math.min(1, this.valpha));\n    if (Object.freeze) {\n      Object.freeze(this);\n    }\n  }\n  Color.prototype = {\n    toString() {\n      return this.string();\n    },\n    toJSON() {\n      return this[this.model]();\n    },\n    string(places) {\n      var self = this.model in colorString.to ? this : this.rgb();\n      self = self.round(typeof places === 'number' ? places : 1);\n      var args = self.valpha === 1 ? self.color : [...self.color, this.valpha];\n      return colorString.to[self.model](args);\n    },\n    percentString(places) {\n      var self = this.rgb().round(typeof places === 'number' ? places : 1);\n      var args = self.valpha === 1 ? self.color : [...self.color, this.valpha];\n      return colorString.to.rgb.percent(args);\n    },\n    array() {\n      return this.valpha === 1 ? [...this.color] : [...this.color, this.valpha];\n    },\n    object() {\n      var result = {};\n      var channels = convert[this.model].channels;\n      var labels = convert[this.model].labels;\n      for (var i = 0; i < channels; i++) {\n        result[labels[i]] = this.color[i];\n      }\n      if (this.valpha !== 1) {\n        result.alpha = this.valpha;\n      }\n      return result;\n    },\n    unitArray() {\n      var rgb = this.rgb().color;\n      rgb[0] /= 255;\n      rgb[1] /= 255;\n      rgb[2] /= 255;\n      if (this.valpha !== 1) {\n        rgb.push(this.valpha);\n      }\n      return rgb;\n    },\n    unitObject() {\n      var rgb = this.rgb().object();\n      rgb.r /= 255;\n      rgb.g /= 255;\n      rgb.b /= 255;\n      if (this.valpha !== 1) {\n        rgb.alpha = this.valpha;\n      }\n      return rgb;\n    },\n    round(places) {\n      places = Math.max(places || 0, 0);\n      return new Color([...this.color.map(roundToPlace(places)), this.valpha], this.model);\n    },\n    alpha(value) {\n      if (value !== undefined) {\n        return new Color([...this.color, Math.max(0, Math.min(1, value))], this.model);\n      }\n      return this.valpha;\n    },\n    // Rgb\n    red: getset('rgb', 0, maxfn(255)),\n    green: getset('rgb', 1, maxfn(255)),\n    blue: getset('rgb', 2, maxfn(255)),\n    hue: getset(['hsl', 'hsv', 'hsl', 'hwb', 'hcg'], 0, value => (value % 360 + 360) % 360),\n    saturationl: getset('hsl', 1, maxfn(100)),\n    lightness: getset('hsl', 2, maxfn(100)),\n    saturationv: getset('hsv', 1, maxfn(100)),\n    value: getset('hsv', 2, maxfn(100)),\n    chroma: getset('hcg', 1, maxfn(100)),\n    gray: getset('hcg', 2, maxfn(100)),\n    white: getset('hwb', 1, maxfn(100)),\n    wblack: getset('hwb', 2, maxfn(100)),\n    cyan: getset('cmyk', 0, maxfn(100)),\n    magenta: getset('cmyk', 1, maxfn(100)),\n    yellow: getset('cmyk', 2, maxfn(100)),\n    black: getset('cmyk', 3, maxfn(100)),\n    x: getset('xyz', 0, maxfn(95.047)),\n    y: getset('xyz', 1, maxfn(100)),\n    z: getset('xyz', 2, maxfn(108.833)),\n    l: getset('lab', 0, maxfn(100)),\n    a: getset('lab', 1),\n    b: getset('lab', 2),\n    keyword(value) {\n      if (value !== undefined) {\n        return new Color(value);\n      }\n      return convert[this.model].keyword(this.color);\n    },\n    hex(value) {\n      if (value !== undefined) {\n        return new Color(value);\n      }\n      return colorString.to.hex(this.rgb().round().color);\n    },\n    hexa(value) {\n      if (value !== undefined) {\n        return new Color(value);\n      }\n      var rgbArray = this.rgb().round().color;\n      var alphaHex = Math.round(this.valpha * 255).toString(16).toUpperCase();\n      if (alphaHex.length === 1) {\n        alphaHex = '0' + alphaHex;\n      }\n      return colorString.to.hex(rgbArray) + alphaHex;\n    },\n    rgbNumber() {\n      var rgb = this.rgb().color;\n      return (rgb[0] & 0xFF) << 16 | (rgb[1] & 0xFF) << 8 | rgb[2] & 0xFF;\n    },\n    luminosity() {\n      // http://www.w3.org/TR/WCAG20/#relativeluminancedef\n      var rgb = this.rgb().color;\n      var lum = [];\n      for (var _ref of rgb.entries()) {\n        var _ref2 = _slicedToArray(_ref, 2);\n        var i = _ref2[0];\n        var element = _ref2[1];\n        var chan = element / 255;\n        lum[i] = chan <= 0.04045 ? chan / 12.92 : ((chan + 0.055) / 1.055) ** 2.4;\n      }\n      return 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n    },\n    contrast(color2) {\n      // http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n      var lum1 = this.luminosity();\n      var lum2 = color2.luminosity();\n      if (lum1 > lum2) {\n        return (lum1 + 0.05) / (lum2 + 0.05);\n      }\n      return (lum2 + 0.05) / (lum1 + 0.05);\n    },\n    level(color2) {\n      // https://www.w3.org/TR/WCAG/#contrast-enhanced\n      var contrastRatio = this.contrast(color2);\n      if (contrastRatio >= 7) {\n        return 'AAA';\n      }\n      return contrastRatio >= 4.5 ? 'AA' : '';\n    },\n    isDark() {\n      // YIQ equation from http://24ways.org/2010/calculating-color-contrast\n      var rgb = this.rgb().color;\n      var yiq = (rgb[0] * 2126 + rgb[1] * 7152 + rgb[2] * 722) / 10000;\n      return yiq < 128;\n    },\n    isLight() {\n      return !this.isDark();\n    },\n    negate() {\n      var rgb = this.rgb();\n      for (var i = 0; i < 3; i++) {\n        rgb.color[i] = 255 - rgb.color[i];\n      }\n      return rgb;\n    },\n    lighten(ratio) {\n      var hsl = this.hsl();\n      hsl.color[2] += hsl.color[2] * ratio;\n      return hsl;\n    },\n    darken(ratio) {\n      var hsl = this.hsl();\n      hsl.color[2] -= hsl.color[2] * ratio;\n      return hsl;\n    },\n    saturate(ratio) {\n      var hsl = this.hsl();\n      hsl.color[1] += hsl.color[1] * ratio;\n      return hsl;\n    },\n    desaturate(ratio) {\n      var hsl = this.hsl();\n      hsl.color[1] -= hsl.color[1] * ratio;\n      return hsl;\n    },\n    whiten(ratio) {\n      var hwb = this.hwb();\n      hwb.color[1] += hwb.color[1] * ratio;\n      return hwb;\n    },\n    blacken(ratio) {\n      var hwb = this.hwb();\n      hwb.color[2] += hwb.color[2] * ratio;\n      return hwb;\n    },\n    grayscale() {\n      // http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n      var rgb = this.rgb().color;\n      var value = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n      return Color.rgb(value, value, value);\n    },\n    fade(ratio) {\n      return this.alpha(this.valpha - this.valpha * ratio);\n    },\n    opaquer(ratio) {\n      return this.alpha(this.valpha + this.valpha * ratio);\n    },\n    rotate(degrees) {\n      var hsl = this.hsl();\n      var hue = hsl.color[0];\n      hue = (hue + degrees) % 360;\n      hue = hue < 0 ? 360 + hue : hue;\n      hsl.color[0] = hue;\n      return hsl;\n    },\n    mix(mixinColor, weight) {\n      // Ported from sass implementation in C\n      // https://github.com/sass/libsass/blob/0e6b4a2850092356aa3ece07c6b249f0221caced/functions.cpp#L209\n      if (!mixinColor || !mixinColor.rgb) {\n        throw new Error('Argument to \"mix\" was not a Color instance, but rather an instance of ' + typeof mixinColor);\n      }\n      var color1 = mixinColor.rgb();\n      var color2 = this.rgb();\n      var p = weight === undefined ? 0.5 : weight;\n      var w = 2 * p - 1;\n      var a = color1.alpha() - color2.alpha();\n      var w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2;\n      var w2 = 1 - w1;\n      return Color.rgb(w1 * color1.red() + w2 * color2.red(), w1 * color1.green() + w2 * color2.green(), w1 * color1.blue() + w2 * color2.blue(), color1.alpha() * p + color2.alpha() * (1 - p));\n    }\n  };\n\n  // Model conversion methods and static constructors\n  var _loop = function (_model) {\n    if (skippedModels.includes(_model)) {\n      return 1; // continue\n    }\n    var channels = convert[_model].channels;\n\n    // Conversion methods\n    Color.prototype[_model] = function () {\n      if (this.model === _model) {\n        return new Color(this);\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      if (args.length > 0) {\n        return new Color(args, _model);\n      }\n      return new Color([...assertArray(convert[this.model][_model].raw(this.color)), this.valpha], _model);\n    };\n\n    // 'static' construction methods\n    Color[_model] = function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      var color = args[0];\n      if (typeof color === 'number') {\n        color = zeroArray(args, channels);\n      }\n      return new Color(color, _model);\n    };\n  };\n  for (var _model of Object.keys(convert)) {\n    if (_loop(_model)) continue;\n  }\n  function roundTo(number, places) {\n    return Number(number.toFixed(places));\n  }\n  function roundToPlace(places) {\n    return function (number) {\n      return roundTo(number, places);\n    };\n  }\n  function getset(model, channel, modifier) {\n    model = Array.isArray(model) ? model : [model];\n    for (var m of model) {\n      (limiters[m] || (limiters[m] = []))[channel] = modifier;\n    }\n    model = model[0];\n    return function (value) {\n      var result;\n      if (value !== undefined) {\n        if (modifier) {\n          value = modifier(value);\n        }\n        result = this[model]();\n        result.color[channel] = value;\n        return result;\n      }\n      result = this[model]().color[channel];\n      if (modifier) {\n        result = modifier(result);\n      }\n      return result;\n    };\n  }\n  function maxfn(max) {\n    return function (v) {\n      return Math.max(0, Math.min(max, v));\n    };\n  }\n  function assertArray(value) {\n    return Array.isArray(value) ? value : [value];\n  }\n  function zeroArray(array, length) {\n    for (var i = 0; i < length; i++) {\n      if (typeof array[i] !== 'number') {\n        array[i] = 0;\n      }\n    }\n    return array;\n  }\n  module.exports = Color;\n});", "lineCount": 403, "map": [[3, 2, 1, 0], [3, 6, 1, 6, "colorString"], [3, 17, 1, 17], [3, 20, 1, 20, "require"], [3, 27, 1, 27], [3, 28, 1, 27, "_dependencyMap"], [3, 42, 1, 27], [3, 61, 1, 42], [3, 62, 1, 43], [4, 2, 2, 0], [4, 6, 2, 6, "convert"], [4, 13, 2, 13], [4, 16, 2, 16, "require"], [4, 23, 2, 23], [4, 24, 2, 23, "_dependencyMap"], [4, 38, 2, 23], [4, 58, 2, 39], [4, 59, 2, 40], [5, 2, 4, 0], [5, 6, 4, 6, "skippedModels"], [5, 19, 4, 19], [5, 22, 4, 22], [6, 2, 5, 1], [7, 2, 6, 1], [7, 11, 6, 10], [8, 2, 8, 1], [9, 2, 9, 1], [9, 8, 9, 7], [10, 2, 11, 1], [11, 2, 12, 1], [11, 7, 12, 6], [11, 8, 13, 1], [12, 2, 15, 0], [12, 6, 15, 6, "hashedModelKeys"], [12, 21, 15, 21], [12, 24, 15, 24], [12, 25, 15, 25], [12, 26, 15, 26], [13, 2, 16, 0], [13, 7, 16, 5], [13, 11, 16, 11, "model"], [13, 16, 16, 16], [13, 20, 16, 20, "Object"], [13, 26, 16, 26], [13, 27, 16, 27, "keys"], [13, 31, 16, 31], [13, 32, 16, 32, "convert"], [13, 39, 16, 39], [13, 40, 16, 40], [13, 42, 16, 42], [14, 4, 17, 1, "hashedModelKeys"], [14, 19, 17, 16], [14, 20, 17, 17], [14, 21, 17, 18], [14, 24, 17, 21, "convert"], [14, 31, 17, 28], [14, 32, 17, 29, "model"], [14, 37, 17, 34], [14, 38, 17, 35], [14, 39, 17, 36, "labels"], [14, 45, 17, 42], [14, 46, 17, 43], [14, 47, 17, 44, "sort"], [14, 51, 17, 48], [14, 52, 17, 49], [14, 53, 17, 50], [14, 54, 17, 51, "join"], [14, 58, 17, 55], [14, 59, 17, 56], [14, 61, 17, 58], [14, 62, 17, 59], [14, 63, 17, 60], [14, 66, 17, 63, "model"], [14, 71, 17, 68], [15, 2, 18, 0], [16, 2, 20, 0], [16, 6, 20, 6, "limiters"], [16, 14, 20, 14], [16, 17, 20, 17], [16, 18, 20, 18], [16, 19, 20, 19], [17, 2, 22, 0], [17, 11, 22, 9, "Color"], [17, 16, 22, 14, "Color"], [17, 17, 22, 15, "object"], [17, 23, 22, 21], [17, 25, 22, 23, "model"], [17, 30, 22, 28], [17, 32, 22, 30], [18, 4, 23, 1], [18, 8, 23, 5], [18, 10, 23, 7], [18, 14, 23, 11], [18, 26, 23, 23, "Color"], [18, 31, 23, 28], [18, 32, 23, 29], [18, 34, 23, 31], [19, 6, 24, 2], [19, 13, 24, 9], [19, 17, 24, 13, "Color"], [19, 22, 24, 18], [19, 23, 24, 19, "object"], [19, 29, 24, 25], [19, 31, 24, 27, "model"], [19, 36, 24, 32], [19, 37, 24, 33], [20, 4, 25, 1], [21, 4, 27, 1], [21, 8, 27, 5, "model"], [21, 13, 27, 10], [21, 17, 27, 14, "model"], [21, 22, 27, 19], [21, 26, 27, 23, "skippedModels"], [21, 39, 27, 36], [21, 41, 27, 38], [22, 6, 28, 2, "model"], [22, 11, 28, 7], [22, 14, 28, 10], [22, 18, 28, 14], [23, 4, 29, 1], [24, 4, 31, 1], [24, 8, 31, 5, "model"], [24, 13, 31, 10], [24, 17, 31, 14], [24, 19, 31, 16, "model"], [24, 24, 31, 21], [24, 28, 31, 25, "convert"], [24, 35, 31, 32], [24, 36, 31, 33], [24, 38, 31, 35], [25, 6, 32, 2], [25, 12, 32, 8], [25, 16, 32, 12, "Error"], [25, 21, 32, 17], [25, 22, 32, 18], [25, 39, 32, 35], [25, 42, 32, 38, "model"], [25, 47, 32, 43], [25, 48, 32, 44], [26, 4, 33, 1], [27, 4, 35, 1], [27, 8, 35, 5, "i"], [27, 9, 35, 6], [28, 4, 36, 1], [28, 8, 36, 5, "channels"], [28, 16, 36, 13], [29, 4, 38, 1], [29, 8, 38, 5, "object"], [29, 14, 38, 11], [29, 18, 38, 15], [29, 22, 38, 19], [29, 24, 38, 21], [30, 6, 38, 23], [31, 6, 39, 2], [31, 10, 39, 6], [31, 11, 39, 7, "model"], [31, 16, 39, 12], [31, 19, 39, 15], [31, 24, 39, 20], [32, 6, 40, 2], [32, 10, 40, 6], [32, 11, 40, 7, "color"], [32, 16, 40, 12], [32, 19, 40, 15], [32, 20, 40, 16], [32, 21, 40, 17], [32, 23, 40, 19], [32, 24, 40, 20], [32, 26, 40, 22], [32, 27, 40, 23], [32, 28, 40, 24], [33, 6, 41, 2], [33, 10, 41, 6], [33, 11, 41, 7, "valpha"], [33, 17, 41, 13], [33, 20, 41, 16], [33, 21, 41, 17], [34, 4, 42, 1], [34, 5, 42, 2], [34, 11, 42, 8], [34, 15, 42, 12, "object"], [34, 21, 42, 18], [34, 33, 42, 30, "Color"], [34, 38, 42, 35], [34, 40, 42, 37], [35, 6, 43, 2], [35, 10, 43, 6], [35, 11, 43, 7, "model"], [35, 16, 43, 12], [35, 19, 43, 15, "object"], [35, 25, 43, 21], [35, 26, 43, 22, "model"], [35, 31, 43, 27], [36, 6, 44, 2], [36, 10, 44, 6], [36, 11, 44, 7, "color"], [36, 16, 44, 12], [36, 19, 44, 15], [36, 20, 44, 16], [36, 23, 44, 19, "object"], [36, 29, 44, 25], [36, 30, 44, 26, "color"], [36, 35, 44, 31], [36, 36, 44, 32], [37, 6, 45, 2], [37, 10, 45, 6], [37, 11, 45, 7, "valpha"], [37, 17, 45, 13], [37, 20, 45, 16, "object"], [37, 26, 45, 22], [37, 27, 45, 23, "valpha"], [37, 33, 45, 29], [38, 4, 46, 1], [38, 5, 46, 2], [38, 11, 46, 8], [38, 15, 46, 12], [38, 22, 46, 19, "object"], [38, 28, 46, 25], [38, 33, 46, 30], [38, 41, 46, 38], [38, 43, 46, 40], [39, 6, 47, 2], [39, 10, 47, 8, "result"], [39, 16, 47, 14], [39, 19, 47, 17, "colorString"], [39, 30, 47, 28], [39, 31, 47, 29, "get"], [39, 34, 47, 32], [39, 35, 47, 33, "object"], [39, 41, 47, 39], [39, 42, 47, 40], [40, 6, 48, 2], [40, 10, 48, 6, "result"], [40, 16, 48, 12], [40, 21, 48, 17], [40, 25, 48, 21], [40, 27, 48, 23], [41, 8, 49, 3], [41, 14, 49, 9], [41, 18, 49, 13, "Error"], [41, 23, 49, 18], [41, 24, 49, 19], [41, 61, 49, 56], [41, 64, 49, 59, "object"], [41, 70, 49, 65], [41, 71, 49, 66], [42, 6, 50, 2], [43, 6, 52, 2], [43, 10, 52, 6], [43, 11, 52, 7, "model"], [43, 16, 52, 12], [43, 19, 52, 15, "result"], [43, 25, 52, 21], [43, 26, 52, 22, "model"], [43, 31, 52, 27], [44, 6, 53, 2, "channels"], [44, 14, 53, 10], [44, 17, 53, 13, "convert"], [44, 24, 53, 20], [44, 25, 53, 21], [44, 29, 53, 25], [44, 30, 53, 26, "model"], [44, 35, 53, 31], [44, 36, 53, 32], [44, 37, 53, 33, "channels"], [44, 45, 53, 41], [45, 6, 54, 2], [45, 10, 54, 6], [45, 11, 54, 7, "color"], [45, 16, 54, 12], [45, 19, 54, 15, "result"], [45, 25, 54, 21], [45, 26, 54, 22, "value"], [45, 31, 54, 27], [45, 32, 54, 28, "slice"], [45, 37, 54, 33], [45, 38, 54, 34], [45, 39, 54, 35], [45, 41, 54, 37, "channels"], [45, 49, 54, 45], [45, 50, 54, 46], [46, 6, 55, 2], [46, 10, 55, 6], [46, 11, 55, 7, "valpha"], [46, 17, 55, 13], [46, 20, 55, 16], [46, 27, 55, 23, "result"], [46, 33, 55, 29], [46, 34, 55, 30, "value"], [46, 39, 55, 35], [46, 40, 55, 36, "channels"], [46, 48, 55, 44], [46, 49, 55, 45], [46, 54, 55, 50], [46, 62, 55, 58], [46, 65, 55, 61, "result"], [46, 71, 55, 67], [46, 72, 55, 68, "value"], [46, 77, 55, 73], [46, 78, 55, 74, "channels"], [46, 86, 55, 82], [46, 87, 55, 83], [46, 90, 55, 86], [46, 91, 55, 87], [47, 4, 56, 1], [47, 5, 56, 2], [47, 11, 56, 8], [47, 15, 56, 12, "object"], [47, 21, 56, 18], [47, 22, 56, 19, "length"], [47, 28, 56, 25], [47, 31, 56, 28], [47, 32, 56, 29], [47, 34, 56, 31], [48, 6, 57, 2], [48, 10, 57, 6], [48, 11, 57, 7, "model"], [48, 16, 57, 12], [48, 19, 57, 15, "model"], [48, 24, 57, 20], [48, 28, 57, 24], [48, 33, 57, 29], [49, 6, 58, 2, "channels"], [49, 14, 58, 10], [49, 17, 58, 13, "convert"], [49, 24, 58, 20], [49, 25, 58, 21], [49, 29, 58, 25], [49, 30, 58, 26, "model"], [49, 35, 58, 31], [49, 36, 58, 32], [49, 37, 58, 33, "channels"], [49, 45, 58, 41], [50, 6, 59, 2], [50, 10, 59, 8, "newArray"], [50, 18, 59, 16], [50, 21, 59, 19, "Array"], [50, 26, 59, 24], [50, 27, 59, 25, "prototype"], [50, 36, 59, 34], [50, 37, 59, 35, "slice"], [50, 42, 59, 40], [50, 43, 59, 41, "call"], [50, 47, 59, 45], [50, 48, 59, 46, "object"], [50, 54, 59, 52], [50, 56, 59, 54], [50, 57, 59, 55], [50, 59, 59, 57, "channels"], [50, 67, 59, 65], [50, 68, 59, 66], [51, 6, 60, 2], [51, 10, 60, 6], [51, 11, 60, 7, "color"], [51, 16, 60, 12], [51, 19, 60, 15, "zeroArray"], [51, 28, 60, 24], [51, 29, 60, 25, "newArray"], [51, 37, 60, 33], [51, 39, 60, 35, "channels"], [51, 47, 60, 43], [51, 48, 60, 44], [52, 6, 61, 2], [52, 10, 61, 6], [52, 11, 61, 7, "valpha"], [52, 17, 61, 13], [52, 20, 61, 16], [52, 27, 61, 23, "object"], [52, 33, 61, 29], [52, 34, 61, 30, "channels"], [52, 42, 61, 38], [52, 43, 61, 39], [52, 48, 61, 44], [52, 56, 61, 52], [52, 59, 61, 55, "object"], [52, 65, 61, 61], [52, 66, 61, 62, "channels"], [52, 74, 61, 70], [52, 75, 61, 71], [52, 78, 61, 74], [52, 79, 61, 75], [53, 4, 62, 1], [53, 5, 62, 2], [53, 11, 62, 8], [53, 15, 62, 12], [53, 22, 62, 19, "object"], [53, 28, 62, 25], [53, 33, 62, 30], [53, 41, 62, 38], [53, 43, 62, 40], [54, 6, 63, 2], [55, 6, 64, 2], [55, 10, 64, 6], [55, 11, 64, 7, "model"], [55, 16, 64, 12], [55, 19, 64, 15], [55, 24, 64, 20], [56, 6, 65, 2], [56, 10, 65, 6], [56, 11, 65, 7, "color"], [56, 16, 65, 12], [56, 19, 65, 15], [56, 20, 66, 4, "object"], [56, 26, 66, 10], [56, 30, 66, 14], [56, 32, 66, 16], [56, 35, 66, 20], [56, 39, 66, 24], [56, 41, 67, 4, "object"], [56, 47, 67, 10], [56, 51, 67, 14], [56, 52, 67, 15], [56, 55, 67, 19], [56, 59, 67, 23], [56, 61, 68, 3, "object"], [56, 67, 68, 9], [56, 70, 68, 12], [56, 74, 68, 16], [56, 75, 69, 3], [57, 6, 70, 2], [57, 10, 70, 6], [57, 11, 70, 7, "valpha"], [57, 17, 70, 13], [57, 20, 70, 16], [57, 21, 70, 17], [58, 4, 71, 1], [58, 5, 71, 2], [58, 11, 71, 8], [59, 6, 72, 2], [59, 10, 72, 6], [59, 11, 72, 7, "valpha"], [59, 17, 72, 13], [59, 20, 72, 16], [59, 21, 72, 17], [60, 6, 74, 2], [60, 10, 74, 8, "keys"], [60, 14, 74, 12], [60, 17, 74, 15, "Object"], [60, 23, 74, 21], [60, 24, 74, 22, "keys"], [60, 28, 74, 26], [60, 29, 74, 27, "object"], [60, 35, 74, 33], [60, 36, 74, 34], [61, 6, 75, 2], [61, 10, 75, 6], [61, 17, 75, 13], [61, 21, 75, 17, "object"], [61, 27, 75, 23], [61, 29, 75, 25], [62, 8, 76, 3, "keys"], [62, 12, 76, 7], [62, 13, 76, 8, "splice"], [62, 19, 76, 14], [62, 20, 76, 15, "keys"], [62, 24, 76, 19], [62, 25, 76, 20, "indexOf"], [62, 32, 76, 27], [62, 33, 76, 28], [62, 40, 76, 35], [62, 41, 76, 36], [62, 43, 76, 38], [62, 44, 76, 39], [62, 45, 76, 40], [63, 8, 77, 3], [63, 12, 77, 7], [63, 13, 77, 8, "valpha"], [63, 19, 77, 14], [63, 22, 77, 17], [63, 29, 77, 24, "object"], [63, 35, 77, 30], [63, 36, 77, 31, "alpha"], [63, 41, 77, 36], [63, 46, 77, 41], [63, 54, 77, 49], [63, 57, 77, 52, "object"], [63, 63, 77, 58], [63, 64, 77, 59, "alpha"], [63, 69, 77, 64], [63, 72, 77, 67], [63, 73, 77, 68], [64, 6, 78, 2], [65, 6, 80, 2], [65, 10, 80, 8, "hashedKeys"], [65, 20, 80, 18], [65, 23, 80, 21, "keys"], [65, 27, 80, 25], [65, 28, 80, 26, "sort"], [65, 32, 80, 30], [65, 33, 80, 31], [65, 34, 80, 32], [65, 35, 80, 33, "join"], [65, 39, 80, 37], [65, 40, 80, 38], [65, 42, 80, 40], [65, 43, 80, 41], [66, 6, 81, 2], [66, 10, 81, 6], [66, 12, 81, 8, "hashedKeys"], [66, 22, 81, 18], [66, 26, 81, 22, "hashedModelKeys"], [66, 41, 81, 37], [66, 42, 81, 38], [66, 44, 81, 40], [67, 8, 82, 3], [67, 14, 82, 9], [67, 18, 82, 13, "Error"], [67, 23, 82, 18], [67, 24, 82, 19], [67, 61, 82, 56], [67, 64, 82, 59, "JSON"], [67, 68, 82, 63], [67, 69, 82, 64, "stringify"], [67, 78, 82, 73], [67, 79, 82, 74, "object"], [67, 85, 82, 80], [67, 86, 82, 81], [67, 87, 82, 82], [68, 6, 83, 2], [69, 6, 85, 2], [69, 10, 85, 6], [69, 11, 85, 7, "model"], [69, 16, 85, 12], [69, 19, 85, 15, "hashedModelKeys"], [69, 34, 85, 30], [69, 35, 85, 31, "hashedKeys"], [69, 45, 85, 41], [69, 46, 85, 42], [70, 6, 87, 2], [70, 10, 87, 9, "labels"], [70, 16, 87, 15], [70, 19, 87, 19, "convert"], [70, 26, 87, 26], [70, 27, 87, 27], [70, 31, 87, 31], [70, 32, 87, 32, "model"], [70, 37, 87, 37], [70, 38, 87, 38], [70, 39, 87, 9, "labels"], [70, 45, 87, 15], [71, 6, 88, 2], [71, 10, 88, 8, "color"], [71, 15, 88, 13], [71, 18, 88, 16], [71, 20, 88, 18], [72, 6, 89, 2], [72, 11, 89, 7, "i"], [72, 12, 89, 8], [72, 15, 89, 11], [72, 16, 89, 12], [72, 18, 89, 14, "i"], [72, 19, 89, 15], [72, 22, 89, 18, "labels"], [72, 28, 89, 24], [72, 29, 89, 25, "length"], [72, 35, 89, 31], [72, 37, 89, 33, "i"], [72, 38, 89, 34], [72, 40, 89, 36], [72, 42, 89, 38], [73, 8, 90, 3, "color"], [73, 13, 90, 8], [73, 14, 90, 9, "push"], [73, 18, 90, 13], [73, 19, 90, 14, "object"], [73, 25, 90, 20], [73, 26, 90, 21, "labels"], [73, 32, 90, 27], [73, 33, 90, 28, "i"], [73, 34, 90, 29], [73, 35, 90, 30], [73, 36, 90, 31], [73, 37, 90, 32], [74, 6, 91, 2], [75, 6, 93, 2], [75, 10, 93, 6], [75, 11, 93, 7, "color"], [75, 16, 93, 12], [75, 19, 93, 15, "zeroArray"], [75, 28, 93, 24], [75, 29, 93, 25, "color"], [75, 34, 93, 30], [75, 35, 93, 31], [76, 4, 94, 1], [78, 4, 96, 1], [79, 4, 97, 1], [79, 8, 97, 5, "limiters"], [79, 16, 97, 13], [79, 17, 97, 14], [79, 21, 97, 18], [79, 22, 97, 19, "model"], [79, 27, 97, 24], [79, 28, 97, 25], [79, 30, 97, 27], [80, 6, 98, 2, "channels"], [80, 14, 98, 10], [80, 17, 98, 13, "convert"], [80, 24, 98, 20], [80, 25, 98, 21], [80, 29, 98, 25], [80, 30, 98, 26, "model"], [80, 35, 98, 31], [80, 36, 98, 32], [80, 37, 98, 33, "channels"], [80, 45, 98, 41], [81, 6, 99, 2], [81, 11, 99, 7, "i"], [81, 12, 99, 8], [81, 15, 99, 11], [81, 16, 99, 12], [81, 18, 99, 14, "i"], [81, 19, 99, 15], [81, 22, 99, 18, "channels"], [81, 30, 99, 26], [81, 32, 99, 28, "i"], [81, 33, 99, 29], [81, 35, 99, 31], [81, 37, 99, 33], [82, 8, 100, 3], [82, 12, 100, 9, "limit"], [82, 17, 100, 14], [82, 20, 100, 17, "limiters"], [82, 28, 100, 25], [82, 29, 100, 26], [82, 33, 100, 30], [82, 34, 100, 31, "model"], [82, 39, 100, 36], [82, 40, 100, 37], [82, 41, 100, 38, "i"], [82, 42, 100, 39], [82, 43, 100, 40], [83, 8, 101, 3], [83, 12, 101, 7, "limit"], [83, 17, 101, 12], [83, 19, 101, 14], [84, 10, 102, 4], [84, 14, 102, 8], [84, 15, 102, 9, "color"], [84, 20, 102, 14], [84, 21, 102, 15, "i"], [84, 22, 102, 16], [84, 23, 102, 17], [84, 26, 102, 20, "limit"], [84, 31, 102, 25], [84, 32, 102, 26], [84, 36, 102, 30], [84, 37, 102, 31, "color"], [84, 42, 102, 36], [84, 43, 102, 37, "i"], [84, 44, 102, 38], [84, 45, 102, 39], [84, 46, 102, 40], [85, 8, 103, 3], [86, 6, 104, 2], [87, 4, 105, 1], [88, 4, 107, 1], [88, 8, 107, 5], [88, 9, 107, 6, "valpha"], [88, 15, 107, 12], [88, 18, 107, 15, "Math"], [88, 22, 107, 19], [88, 23, 107, 20, "max"], [88, 26, 107, 23], [88, 27, 107, 24], [88, 28, 107, 25], [88, 30, 107, 27, "Math"], [88, 34, 107, 31], [88, 35, 107, 32, "min"], [88, 38, 107, 35], [88, 39, 107, 36], [88, 40, 107, 37], [88, 42, 107, 39], [88, 46, 107, 43], [88, 47, 107, 44, "valpha"], [88, 53, 107, 50], [88, 54, 107, 51], [88, 55, 107, 52], [89, 4, 109, 1], [89, 8, 109, 5, "Object"], [89, 14, 109, 11], [89, 15, 109, 12, "freeze"], [89, 21, 109, 18], [89, 23, 109, 20], [90, 6, 110, 2, "Object"], [90, 12, 110, 8], [90, 13, 110, 9, "freeze"], [90, 19, 110, 15], [90, 20, 110, 16], [90, 24, 110, 20], [90, 25, 110, 21], [91, 4, 111, 1], [92, 2, 112, 0], [93, 2, 114, 0, "Color"], [93, 7, 114, 5], [93, 8, 114, 6, "prototype"], [93, 17, 114, 15], [93, 20, 114, 18], [94, 4, 115, 1, "toString"], [94, 12, 115, 9, "toString"], [94, 13, 115, 9], [94, 15, 115, 12], [95, 6, 116, 2], [95, 13, 116, 9], [95, 17, 116, 13], [95, 18, 116, 14, "string"], [95, 24, 116, 20], [95, 25, 116, 21], [95, 26, 116, 22], [96, 4, 117, 1], [96, 5, 117, 2], [97, 4, 119, 1, "toJSON"], [97, 10, 119, 7, "toJSON"], [97, 11, 119, 7], [97, 13, 119, 10], [98, 6, 120, 2], [98, 13, 120, 9], [98, 17, 120, 13], [98, 18, 120, 14], [98, 22, 120, 18], [98, 23, 120, 19, "model"], [98, 28, 120, 24], [98, 29, 120, 25], [98, 30, 120, 26], [98, 31, 120, 27], [99, 4, 121, 1], [99, 5, 121, 2], [100, 4, 123, 1, "string"], [100, 10, 123, 7, "string"], [100, 11, 123, 8, "places"], [100, 17, 123, 14], [100, 19, 123, 16], [101, 6, 124, 2], [101, 10, 124, 6, "self"], [101, 14, 124, 10], [101, 17, 124, 13], [101, 21, 124, 17], [101, 22, 124, 18, "model"], [101, 27, 124, 23], [101, 31, 124, 27, "colorString"], [101, 42, 124, 38], [101, 43, 124, 39, "to"], [101, 45, 124, 41], [101, 48, 124, 44], [101, 52, 124, 48], [101, 55, 124, 51], [101, 59, 124, 55], [101, 60, 124, 56, "rgb"], [101, 63, 124, 59], [101, 64, 124, 60], [101, 65, 124, 61], [102, 6, 125, 2, "self"], [102, 10, 125, 6], [102, 13, 125, 9, "self"], [102, 17, 125, 13], [102, 18, 125, 14, "round"], [102, 23, 125, 19], [102, 24, 125, 20], [102, 31, 125, 27, "places"], [102, 37, 125, 33], [102, 42, 125, 38], [102, 50, 125, 46], [102, 53, 125, 49, "places"], [102, 59, 125, 55], [102, 62, 125, 58], [102, 63, 125, 59], [102, 64, 125, 60], [103, 6, 126, 2], [103, 10, 126, 8, "args"], [103, 14, 126, 12], [103, 17, 126, 15, "self"], [103, 21, 126, 19], [103, 22, 126, 20, "valpha"], [103, 28, 126, 26], [103, 33, 126, 31], [103, 34, 126, 32], [103, 37, 126, 35, "self"], [103, 41, 126, 39], [103, 42, 126, 40, "color"], [103, 47, 126, 45], [103, 50, 126, 48], [103, 51, 126, 49], [103, 54, 126, 52, "self"], [103, 58, 126, 56], [103, 59, 126, 57, "color"], [103, 64, 126, 62], [103, 66, 126, 64], [103, 70, 126, 68], [103, 71, 126, 69, "valpha"], [103, 77, 126, 75], [103, 78, 126, 76], [104, 6, 127, 2], [104, 13, 127, 9, "colorString"], [104, 24, 127, 20], [104, 25, 127, 21, "to"], [104, 27, 127, 23], [104, 28, 127, 24, "self"], [104, 32, 127, 28], [104, 33, 127, 29, "model"], [104, 38, 127, 34], [104, 39, 127, 35], [104, 40, 127, 36, "args"], [104, 44, 127, 40], [104, 45, 127, 41], [105, 4, 128, 1], [105, 5, 128, 2], [106, 4, 130, 1, "percentString"], [106, 17, 130, 14, "percentString"], [106, 18, 130, 15, "places"], [106, 24, 130, 21], [106, 26, 130, 23], [107, 6, 131, 2], [107, 10, 131, 8, "self"], [107, 14, 131, 12], [107, 17, 131, 15], [107, 21, 131, 19], [107, 22, 131, 20, "rgb"], [107, 25, 131, 23], [107, 26, 131, 24], [107, 27, 131, 25], [107, 28, 131, 26, "round"], [107, 33, 131, 31], [107, 34, 131, 32], [107, 41, 131, 39, "places"], [107, 47, 131, 45], [107, 52, 131, 50], [107, 60, 131, 58], [107, 63, 131, 61, "places"], [107, 69, 131, 67], [107, 72, 131, 70], [107, 73, 131, 71], [107, 74, 131, 72], [108, 6, 132, 2], [108, 10, 132, 8, "args"], [108, 14, 132, 12], [108, 17, 132, 15, "self"], [108, 21, 132, 19], [108, 22, 132, 20, "valpha"], [108, 28, 132, 26], [108, 33, 132, 31], [108, 34, 132, 32], [108, 37, 132, 35, "self"], [108, 41, 132, 39], [108, 42, 132, 40, "color"], [108, 47, 132, 45], [108, 50, 132, 48], [108, 51, 132, 49], [108, 54, 132, 52, "self"], [108, 58, 132, 56], [108, 59, 132, 57, "color"], [108, 64, 132, 62], [108, 66, 132, 64], [108, 70, 132, 68], [108, 71, 132, 69, "valpha"], [108, 77, 132, 75], [108, 78, 132, 76], [109, 6, 133, 2], [109, 13, 133, 9, "colorString"], [109, 24, 133, 20], [109, 25, 133, 21, "to"], [109, 27, 133, 23], [109, 28, 133, 24, "rgb"], [109, 31, 133, 27], [109, 32, 133, 28, "percent"], [109, 39, 133, 35], [109, 40, 133, 36, "args"], [109, 44, 133, 40], [109, 45, 133, 41], [110, 4, 134, 1], [110, 5, 134, 2], [111, 4, 136, 1, "array"], [111, 9, 136, 6, "array"], [111, 10, 136, 6], [111, 12, 136, 9], [112, 6, 137, 2], [112, 13, 137, 9], [112, 17, 137, 13], [112, 18, 137, 14, "valpha"], [112, 24, 137, 20], [112, 29, 137, 25], [112, 30, 137, 26], [112, 33, 137, 29], [112, 34, 137, 30], [112, 37, 137, 33], [112, 41, 137, 37], [112, 42, 137, 38, "color"], [112, 47, 137, 43], [112, 48, 137, 44], [112, 51, 137, 47], [112, 52, 137, 48], [112, 55, 137, 51], [112, 59, 137, 55], [112, 60, 137, 56, "color"], [112, 65, 137, 61], [112, 67, 137, 63], [112, 71, 137, 67], [112, 72, 137, 68, "valpha"], [112, 78, 137, 74], [112, 79, 137, 75], [113, 4, 138, 1], [113, 5, 138, 2], [114, 4, 140, 1, "object"], [114, 10, 140, 7, "object"], [114, 11, 140, 7], [114, 13, 140, 10], [115, 6, 141, 2], [115, 10, 141, 8, "result"], [115, 16, 141, 14], [115, 19, 141, 17], [115, 20, 141, 18], [115, 21, 141, 19], [116, 6, 142, 2], [116, 10, 142, 9, "channels"], [116, 18, 142, 17], [116, 21, 142, 21, "convert"], [116, 28, 142, 28], [116, 29, 142, 29], [116, 33, 142, 33], [116, 34, 142, 34, "model"], [116, 39, 142, 39], [116, 40, 142, 40], [116, 41, 142, 9, "channels"], [116, 49, 142, 17], [117, 6, 143, 2], [117, 10, 143, 9, "labels"], [117, 16, 143, 15], [117, 19, 143, 19, "convert"], [117, 26, 143, 26], [117, 27, 143, 27], [117, 31, 143, 31], [117, 32, 143, 32, "model"], [117, 37, 143, 37], [117, 38, 143, 38], [117, 39, 143, 9, "labels"], [117, 45, 143, 15], [118, 6, 145, 2], [118, 11, 145, 7], [118, 15, 145, 11, "i"], [118, 16, 145, 12], [118, 19, 145, 15], [118, 20, 145, 16], [118, 22, 145, 18, "i"], [118, 23, 145, 19], [118, 26, 145, 22, "channels"], [118, 34, 145, 30], [118, 36, 145, 32, "i"], [118, 37, 145, 33], [118, 39, 145, 35], [118, 41, 145, 37], [119, 8, 146, 3, "result"], [119, 14, 146, 9], [119, 15, 146, 10, "labels"], [119, 21, 146, 16], [119, 22, 146, 17, "i"], [119, 23, 146, 18], [119, 24, 146, 19], [119, 25, 146, 20], [119, 28, 146, 23], [119, 32, 146, 27], [119, 33, 146, 28, "color"], [119, 38, 146, 33], [119, 39, 146, 34, "i"], [119, 40, 146, 35], [119, 41, 146, 36], [120, 6, 147, 2], [121, 6, 149, 2], [121, 10, 149, 6], [121, 14, 149, 10], [121, 15, 149, 11, "valpha"], [121, 21, 149, 17], [121, 26, 149, 22], [121, 27, 149, 23], [121, 29, 149, 25], [122, 8, 150, 3, "result"], [122, 14, 150, 9], [122, 15, 150, 10, "alpha"], [122, 20, 150, 15], [122, 23, 150, 18], [122, 27, 150, 22], [122, 28, 150, 23, "valpha"], [122, 34, 150, 29], [123, 6, 151, 2], [124, 6, 153, 2], [124, 13, 153, 9, "result"], [124, 19, 153, 15], [125, 4, 154, 1], [125, 5, 154, 2], [126, 4, 156, 1, "unitArray"], [126, 13, 156, 10, "unitArray"], [126, 14, 156, 10], [126, 16, 156, 13], [127, 6, 157, 2], [127, 10, 157, 8, "rgb"], [127, 13, 157, 11], [127, 16, 157, 14], [127, 20, 157, 18], [127, 21, 157, 19, "rgb"], [127, 24, 157, 22], [127, 25, 157, 23], [127, 26, 157, 24], [127, 27, 157, 25, "color"], [127, 32, 157, 30], [128, 6, 158, 2, "rgb"], [128, 9, 158, 5], [128, 10, 158, 6], [128, 11, 158, 7], [128, 12, 158, 8], [128, 16, 158, 12], [128, 19, 158, 15], [129, 6, 159, 2, "rgb"], [129, 9, 159, 5], [129, 10, 159, 6], [129, 11, 159, 7], [129, 12, 159, 8], [129, 16, 159, 12], [129, 19, 159, 15], [130, 6, 160, 2, "rgb"], [130, 9, 160, 5], [130, 10, 160, 6], [130, 11, 160, 7], [130, 12, 160, 8], [130, 16, 160, 12], [130, 19, 160, 15], [131, 6, 162, 2], [131, 10, 162, 6], [131, 14, 162, 10], [131, 15, 162, 11, "valpha"], [131, 21, 162, 17], [131, 26, 162, 22], [131, 27, 162, 23], [131, 29, 162, 25], [132, 8, 163, 3, "rgb"], [132, 11, 163, 6], [132, 12, 163, 7, "push"], [132, 16, 163, 11], [132, 17, 163, 12], [132, 21, 163, 16], [132, 22, 163, 17, "valpha"], [132, 28, 163, 23], [132, 29, 163, 24], [133, 6, 164, 2], [134, 6, 166, 2], [134, 13, 166, 9, "rgb"], [134, 16, 166, 12], [135, 4, 167, 1], [135, 5, 167, 2], [136, 4, 169, 1, "unitObject"], [136, 14, 169, 11, "unitObject"], [136, 15, 169, 11], [136, 17, 169, 14], [137, 6, 170, 2], [137, 10, 170, 8, "rgb"], [137, 13, 170, 11], [137, 16, 170, 14], [137, 20, 170, 18], [137, 21, 170, 19, "rgb"], [137, 24, 170, 22], [137, 25, 170, 23], [137, 26, 170, 24], [137, 27, 170, 25, "object"], [137, 33, 170, 31], [137, 34, 170, 32], [137, 35, 170, 33], [138, 6, 171, 2, "rgb"], [138, 9, 171, 5], [138, 10, 171, 6, "r"], [138, 11, 171, 7], [138, 15, 171, 11], [138, 18, 171, 14], [139, 6, 172, 2, "rgb"], [139, 9, 172, 5], [139, 10, 172, 6, "g"], [139, 11, 172, 7], [139, 15, 172, 11], [139, 18, 172, 14], [140, 6, 173, 2, "rgb"], [140, 9, 173, 5], [140, 10, 173, 6, "b"], [140, 11, 173, 7], [140, 15, 173, 11], [140, 18, 173, 14], [141, 6, 175, 2], [141, 10, 175, 6], [141, 14, 175, 10], [141, 15, 175, 11, "valpha"], [141, 21, 175, 17], [141, 26, 175, 22], [141, 27, 175, 23], [141, 29, 175, 25], [142, 8, 176, 3, "rgb"], [142, 11, 176, 6], [142, 12, 176, 7, "alpha"], [142, 17, 176, 12], [142, 20, 176, 15], [142, 24, 176, 19], [142, 25, 176, 20, "valpha"], [142, 31, 176, 26], [143, 6, 177, 2], [144, 6, 179, 2], [144, 13, 179, 9, "rgb"], [144, 16, 179, 12], [145, 4, 180, 1], [145, 5, 180, 2], [146, 4, 182, 1, "round"], [146, 9, 182, 6, "round"], [146, 10, 182, 7, "places"], [146, 16, 182, 13], [146, 18, 182, 15], [147, 6, 183, 2, "places"], [147, 12, 183, 8], [147, 15, 183, 11, "Math"], [147, 19, 183, 15], [147, 20, 183, 16, "max"], [147, 23, 183, 19], [147, 24, 183, 20, "places"], [147, 30, 183, 26], [147, 34, 183, 30], [147, 35, 183, 31], [147, 37, 183, 33], [147, 38, 183, 34], [147, 39, 183, 35], [148, 6, 184, 2], [148, 13, 184, 9], [148, 17, 184, 13, "Color"], [148, 22, 184, 18], [148, 23, 184, 19], [148, 24, 184, 20], [148, 27, 184, 23], [148, 31, 184, 27], [148, 32, 184, 28, "color"], [148, 37, 184, 33], [148, 38, 184, 34, "map"], [148, 41, 184, 37], [148, 42, 184, 38, "roundToPlace"], [148, 54, 184, 50], [148, 55, 184, 51, "places"], [148, 61, 184, 57], [148, 62, 184, 58], [148, 63, 184, 59], [148, 65, 184, 61], [148, 69, 184, 65], [148, 70, 184, 66, "valpha"], [148, 76, 184, 72], [148, 77, 184, 73], [148, 79, 184, 75], [148, 83, 184, 79], [148, 84, 184, 80, "model"], [148, 89, 184, 85], [148, 90, 184, 86], [149, 4, 185, 1], [149, 5, 185, 2], [150, 4, 187, 1, "alpha"], [150, 9, 187, 6, "alpha"], [150, 10, 187, 7, "value"], [150, 15, 187, 12], [150, 17, 187, 14], [151, 6, 188, 2], [151, 10, 188, 6, "value"], [151, 15, 188, 11], [151, 20, 188, 16, "undefined"], [151, 29, 188, 25], [151, 31, 188, 27], [152, 8, 189, 3], [152, 15, 189, 10], [152, 19, 189, 14, "Color"], [152, 24, 189, 19], [152, 25, 189, 20], [152, 26, 189, 21], [152, 29, 189, 24], [152, 33, 189, 28], [152, 34, 189, 29, "color"], [152, 39, 189, 34], [152, 41, 189, 36, "Math"], [152, 45, 189, 40], [152, 46, 189, 41, "max"], [152, 49, 189, 44], [152, 50, 189, 45], [152, 51, 189, 46], [152, 53, 189, 48, "Math"], [152, 57, 189, 52], [152, 58, 189, 53, "min"], [152, 61, 189, 56], [152, 62, 189, 57], [152, 63, 189, 58], [152, 65, 189, 60, "value"], [152, 70, 189, 65], [152, 71, 189, 66], [152, 72, 189, 67], [152, 73, 189, 68], [152, 75, 189, 70], [152, 79, 189, 74], [152, 80, 189, 75, "model"], [152, 85, 189, 80], [152, 86, 189, 81], [153, 6, 190, 2], [154, 6, 192, 2], [154, 13, 192, 9], [154, 17, 192, 13], [154, 18, 192, 14, "valpha"], [154, 24, 192, 20], [155, 4, 193, 1], [155, 5, 193, 2], [156, 4, 195, 1], [157, 4, 196, 1, "red"], [157, 7, 196, 4], [157, 9, 196, 6, "getset"], [157, 15, 196, 12], [157, 16, 196, 13], [157, 21, 196, 18], [157, 23, 196, 20], [157, 24, 196, 21], [157, 26, 196, 23, "maxfn"], [157, 31, 196, 28], [157, 32, 196, 29], [157, 35, 196, 32], [157, 36, 196, 33], [157, 37, 196, 34], [158, 4, 197, 1, "green"], [158, 9, 197, 6], [158, 11, 197, 8, "getset"], [158, 17, 197, 14], [158, 18, 197, 15], [158, 23, 197, 20], [158, 25, 197, 22], [158, 26, 197, 23], [158, 28, 197, 25, "maxfn"], [158, 33, 197, 30], [158, 34, 197, 31], [158, 37, 197, 34], [158, 38, 197, 35], [158, 39, 197, 36], [159, 4, 198, 1, "blue"], [159, 8, 198, 5], [159, 10, 198, 7, "getset"], [159, 16, 198, 13], [159, 17, 198, 14], [159, 22, 198, 19], [159, 24, 198, 21], [159, 25, 198, 22], [159, 27, 198, 24, "maxfn"], [159, 32, 198, 29], [159, 33, 198, 30], [159, 36, 198, 33], [159, 37, 198, 34], [159, 38, 198, 35], [160, 4, 200, 1, "hue"], [160, 7, 200, 4], [160, 9, 200, 6, "getset"], [160, 15, 200, 12], [160, 16, 200, 13], [160, 17, 200, 14], [160, 22, 200, 19], [160, 24, 200, 21], [160, 29, 200, 26], [160, 31, 200, 28], [160, 36, 200, 33], [160, 38, 200, 35], [160, 43, 200, 40], [160, 45, 200, 42], [160, 50, 200, 47], [160, 51, 200, 48], [160, 53, 200, 50], [160, 54, 200, 51], [160, 56, 200, 53, "value"], [160, 61, 200, 58], [160, 65, 200, 62], [160, 66, 200, 64, "value"], [160, 71, 200, 69], [160, 74, 200, 72], [160, 77, 200, 75], [160, 80, 200, 79], [160, 83, 200, 82], [160, 87, 200, 86], [160, 90, 200, 89], [160, 91, 200, 90], [161, 4, 202, 1, "saturationl"], [161, 15, 202, 12], [161, 17, 202, 14, "getset"], [161, 23, 202, 20], [161, 24, 202, 21], [161, 29, 202, 26], [161, 31, 202, 28], [161, 32, 202, 29], [161, 34, 202, 31, "maxfn"], [161, 39, 202, 36], [161, 40, 202, 37], [161, 43, 202, 40], [161, 44, 202, 41], [161, 45, 202, 42], [162, 4, 203, 1, "lightness"], [162, 13, 203, 10], [162, 15, 203, 12, "getset"], [162, 21, 203, 18], [162, 22, 203, 19], [162, 27, 203, 24], [162, 29, 203, 26], [162, 30, 203, 27], [162, 32, 203, 29, "maxfn"], [162, 37, 203, 34], [162, 38, 203, 35], [162, 41, 203, 38], [162, 42, 203, 39], [162, 43, 203, 40], [163, 4, 205, 1, "saturationv"], [163, 15, 205, 12], [163, 17, 205, 14, "getset"], [163, 23, 205, 20], [163, 24, 205, 21], [163, 29, 205, 26], [163, 31, 205, 28], [163, 32, 205, 29], [163, 34, 205, 31, "maxfn"], [163, 39, 205, 36], [163, 40, 205, 37], [163, 43, 205, 40], [163, 44, 205, 41], [163, 45, 205, 42], [164, 4, 206, 1, "value"], [164, 9, 206, 6], [164, 11, 206, 8, "getset"], [164, 17, 206, 14], [164, 18, 206, 15], [164, 23, 206, 20], [164, 25, 206, 22], [164, 26, 206, 23], [164, 28, 206, 25, "maxfn"], [164, 33, 206, 30], [164, 34, 206, 31], [164, 37, 206, 34], [164, 38, 206, 35], [164, 39, 206, 36], [165, 4, 208, 1, "chroma"], [165, 10, 208, 7], [165, 12, 208, 9, "getset"], [165, 18, 208, 15], [165, 19, 208, 16], [165, 24, 208, 21], [165, 26, 208, 23], [165, 27, 208, 24], [165, 29, 208, 26, "maxfn"], [165, 34, 208, 31], [165, 35, 208, 32], [165, 38, 208, 35], [165, 39, 208, 36], [165, 40, 208, 37], [166, 4, 209, 1, "gray"], [166, 8, 209, 5], [166, 10, 209, 7, "getset"], [166, 16, 209, 13], [166, 17, 209, 14], [166, 22, 209, 19], [166, 24, 209, 21], [166, 25, 209, 22], [166, 27, 209, 24, "maxfn"], [166, 32, 209, 29], [166, 33, 209, 30], [166, 36, 209, 33], [166, 37, 209, 34], [166, 38, 209, 35], [167, 4, 211, 1, "white"], [167, 9, 211, 6], [167, 11, 211, 8, "getset"], [167, 17, 211, 14], [167, 18, 211, 15], [167, 23, 211, 20], [167, 25, 211, 22], [167, 26, 211, 23], [167, 28, 211, 25, "maxfn"], [167, 33, 211, 30], [167, 34, 211, 31], [167, 37, 211, 34], [167, 38, 211, 35], [167, 39, 211, 36], [168, 4, 212, 1, "wblack"], [168, 10, 212, 7], [168, 12, 212, 9, "getset"], [168, 18, 212, 15], [168, 19, 212, 16], [168, 24, 212, 21], [168, 26, 212, 23], [168, 27, 212, 24], [168, 29, 212, 26, "maxfn"], [168, 34, 212, 31], [168, 35, 212, 32], [168, 38, 212, 35], [168, 39, 212, 36], [168, 40, 212, 37], [169, 4, 214, 1, "cyan"], [169, 8, 214, 5], [169, 10, 214, 7, "getset"], [169, 16, 214, 13], [169, 17, 214, 14], [169, 23, 214, 20], [169, 25, 214, 22], [169, 26, 214, 23], [169, 28, 214, 25, "maxfn"], [169, 33, 214, 30], [169, 34, 214, 31], [169, 37, 214, 34], [169, 38, 214, 35], [169, 39, 214, 36], [170, 4, 215, 1, "magenta"], [170, 11, 215, 8], [170, 13, 215, 10, "getset"], [170, 19, 215, 16], [170, 20, 215, 17], [170, 26, 215, 23], [170, 28, 215, 25], [170, 29, 215, 26], [170, 31, 215, 28, "maxfn"], [170, 36, 215, 33], [170, 37, 215, 34], [170, 40, 215, 37], [170, 41, 215, 38], [170, 42, 215, 39], [171, 4, 216, 1, "yellow"], [171, 10, 216, 7], [171, 12, 216, 9, "getset"], [171, 18, 216, 15], [171, 19, 216, 16], [171, 25, 216, 22], [171, 27, 216, 24], [171, 28, 216, 25], [171, 30, 216, 27, "maxfn"], [171, 35, 216, 32], [171, 36, 216, 33], [171, 39, 216, 36], [171, 40, 216, 37], [171, 41, 216, 38], [172, 4, 217, 1, "black"], [172, 9, 217, 6], [172, 11, 217, 8, "getset"], [172, 17, 217, 14], [172, 18, 217, 15], [172, 24, 217, 21], [172, 26, 217, 23], [172, 27, 217, 24], [172, 29, 217, 26, "maxfn"], [172, 34, 217, 31], [172, 35, 217, 32], [172, 38, 217, 35], [172, 39, 217, 36], [172, 40, 217, 37], [173, 4, 219, 1, "x"], [173, 5, 219, 2], [173, 7, 219, 4, "getset"], [173, 13, 219, 10], [173, 14, 219, 11], [173, 19, 219, 16], [173, 21, 219, 18], [173, 22, 219, 19], [173, 24, 219, 21, "maxfn"], [173, 29, 219, 26], [173, 30, 219, 27], [173, 36, 219, 33], [173, 37, 219, 34], [173, 38, 219, 35], [174, 4, 220, 1, "y"], [174, 5, 220, 2], [174, 7, 220, 4, "getset"], [174, 13, 220, 10], [174, 14, 220, 11], [174, 19, 220, 16], [174, 21, 220, 18], [174, 22, 220, 19], [174, 24, 220, 21, "maxfn"], [174, 29, 220, 26], [174, 30, 220, 27], [174, 33, 220, 30], [174, 34, 220, 31], [174, 35, 220, 32], [175, 4, 221, 1, "z"], [175, 5, 221, 2], [175, 7, 221, 4, "getset"], [175, 13, 221, 10], [175, 14, 221, 11], [175, 19, 221, 16], [175, 21, 221, 18], [175, 22, 221, 19], [175, 24, 221, 21, "maxfn"], [175, 29, 221, 26], [175, 30, 221, 27], [175, 37, 221, 34], [175, 38, 221, 35], [175, 39, 221, 36], [176, 4, 223, 1, "l"], [176, 5, 223, 2], [176, 7, 223, 4, "getset"], [176, 13, 223, 10], [176, 14, 223, 11], [176, 19, 223, 16], [176, 21, 223, 18], [176, 22, 223, 19], [176, 24, 223, 21, "maxfn"], [176, 29, 223, 26], [176, 30, 223, 27], [176, 33, 223, 30], [176, 34, 223, 31], [176, 35, 223, 32], [177, 4, 224, 1, "a"], [177, 5, 224, 2], [177, 7, 224, 4, "getset"], [177, 13, 224, 10], [177, 14, 224, 11], [177, 19, 224, 16], [177, 21, 224, 18], [177, 22, 224, 19], [177, 23, 224, 20], [178, 4, 225, 1, "b"], [178, 5, 225, 2], [178, 7, 225, 4, "getset"], [178, 13, 225, 10], [178, 14, 225, 11], [178, 19, 225, 16], [178, 21, 225, 18], [178, 22, 225, 19], [178, 23, 225, 20], [179, 4, 227, 1, "keyword"], [179, 11, 227, 8, "keyword"], [179, 12, 227, 9, "value"], [179, 17, 227, 14], [179, 19, 227, 16], [180, 6, 228, 2], [180, 10, 228, 6, "value"], [180, 15, 228, 11], [180, 20, 228, 16, "undefined"], [180, 29, 228, 25], [180, 31, 228, 27], [181, 8, 229, 3], [181, 15, 229, 10], [181, 19, 229, 14, "Color"], [181, 24, 229, 19], [181, 25, 229, 20, "value"], [181, 30, 229, 25], [181, 31, 229, 26], [182, 6, 230, 2], [183, 6, 232, 2], [183, 13, 232, 9, "convert"], [183, 20, 232, 16], [183, 21, 232, 17], [183, 25, 232, 21], [183, 26, 232, 22, "model"], [183, 31, 232, 27], [183, 32, 232, 28], [183, 33, 232, 29, "keyword"], [183, 40, 232, 36], [183, 41, 232, 37], [183, 45, 232, 41], [183, 46, 232, 42, "color"], [183, 51, 232, 47], [183, 52, 232, 48], [184, 4, 233, 1], [184, 5, 233, 2], [185, 4, 235, 1, "hex"], [185, 7, 235, 4, "hex"], [185, 8, 235, 5, "value"], [185, 13, 235, 10], [185, 15, 235, 12], [186, 6, 236, 2], [186, 10, 236, 6, "value"], [186, 15, 236, 11], [186, 20, 236, 16, "undefined"], [186, 29, 236, 25], [186, 31, 236, 27], [187, 8, 237, 3], [187, 15, 237, 10], [187, 19, 237, 14, "Color"], [187, 24, 237, 19], [187, 25, 237, 20, "value"], [187, 30, 237, 25], [187, 31, 237, 26], [188, 6, 238, 2], [189, 6, 240, 2], [189, 13, 240, 9, "colorString"], [189, 24, 240, 20], [189, 25, 240, 21, "to"], [189, 27, 240, 23], [189, 28, 240, 24, "hex"], [189, 31, 240, 27], [189, 32, 240, 28], [189, 36, 240, 32], [189, 37, 240, 33, "rgb"], [189, 40, 240, 36], [189, 41, 240, 37], [189, 42, 240, 38], [189, 43, 240, 39, "round"], [189, 48, 240, 44], [189, 49, 240, 45], [189, 50, 240, 46], [189, 51, 240, 47, "color"], [189, 56, 240, 52], [189, 57, 240, 53], [190, 4, 241, 1], [190, 5, 241, 2], [191, 4, 243, 1, "hexa"], [191, 8, 243, 5, "hexa"], [191, 9, 243, 6, "value"], [191, 14, 243, 11], [191, 16, 243, 13], [192, 6, 244, 2], [192, 10, 244, 6, "value"], [192, 15, 244, 11], [192, 20, 244, 16, "undefined"], [192, 29, 244, 25], [192, 31, 244, 27], [193, 8, 245, 3], [193, 15, 245, 10], [193, 19, 245, 14, "Color"], [193, 24, 245, 19], [193, 25, 245, 20, "value"], [193, 30, 245, 25], [193, 31, 245, 26], [194, 6, 246, 2], [195, 6, 248, 2], [195, 10, 248, 8, "rgbArray"], [195, 18, 248, 16], [195, 21, 248, 19], [195, 25, 248, 23], [195, 26, 248, 24, "rgb"], [195, 29, 248, 27], [195, 30, 248, 28], [195, 31, 248, 29], [195, 32, 248, 30, "round"], [195, 37, 248, 35], [195, 38, 248, 36], [195, 39, 248, 37], [195, 40, 248, 38, "color"], [195, 45, 248, 43], [196, 6, 250, 2], [196, 10, 250, 6, "alphaHex"], [196, 18, 250, 14], [196, 21, 250, 17, "Math"], [196, 25, 250, 21], [196, 26, 250, 22, "round"], [196, 31, 250, 27], [196, 32, 250, 28], [196, 36, 250, 32], [196, 37, 250, 33, "valpha"], [196, 43, 250, 39], [196, 46, 250, 42], [196, 49, 250, 45], [196, 50, 250, 46], [196, 51, 250, 47, "toString"], [196, 59, 250, 55], [196, 60, 250, 56], [196, 62, 250, 58], [196, 63, 250, 59], [196, 64, 250, 60, "toUpperCase"], [196, 75, 250, 71], [196, 76, 250, 72], [196, 77, 250, 73], [197, 6, 251, 2], [197, 10, 251, 6, "alphaHex"], [197, 18, 251, 14], [197, 19, 251, 15, "length"], [197, 25, 251, 21], [197, 30, 251, 26], [197, 31, 251, 27], [197, 33, 251, 29], [198, 8, 252, 3, "alphaHex"], [198, 16, 252, 11], [198, 19, 252, 14], [198, 22, 252, 17], [198, 25, 252, 20, "alphaHex"], [198, 33, 252, 28], [199, 6, 253, 2], [200, 6, 255, 2], [200, 13, 255, 9, "colorString"], [200, 24, 255, 20], [200, 25, 255, 21, "to"], [200, 27, 255, 23], [200, 28, 255, 24, "hex"], [200, 31, 255, 27], [200, 32, 255, 28, "rgbArray"], [200, 40, 255, 36], [200, 41, 255, 37], [200, 44, 255, 40, "alphaHex"], [200, 52, 255, 48], [201, 4, 256, 1], [201, 5, 256, 2], [202, 4, 258, 1, "rgbNumber"], [202, 13, 258, 10, "rgbNumber"], [202, 14, 258, 10], [202, 16, 258, 13], [203, 6, 259, 2], [203, 10, 259, 8, "rgb"], [203, 13, 259, 11], [203, 16, 259, 14], [203, 20, 259, 18], [203, 21, 259, 19, "rgb"], [203, 24, 259, 22], [203, 25, 259, 23], [203, 26, 259, 24], [203, 27, 259, 25, "color"], [203, 32, 259, 30], [204, 6, 260, 2], [204, 13, 260, 10], [204, 14, 260, 11, "rgb"], [204, 17, 260, 14], [204, 18, 260, 15], [204, 19, 260, 16], [204, 20, 260, 17], [204, 23, 260, 20], [204, 27, 260, 24], [204, 32, 260, 29], [204, 34, 260, 31], [204, 37, 260, 36], [204, 38, 260, 37, "rgb"], [204, 41, 260, 40], [204, 42, 260, 41], [204, 43, 260, 42], [204, 44, 260, 43], [204, 47, 260, 46], [204, 51, 260, 50], [204, 56, 260, 55], [204, 57, 260, 57], [204, 60, 260, 61, "rgb"], [204, 63, 260, 64], [204, 64, 260, 65], [204, 65, 260, 66], [204, 66, 260, 67], [204, 69, 260, 70], [204, 73, 260, 75], [205, 4, 261, 1], [205, 5, 261, 2], [206, 4, 263, 1, "luminosity"], [206, 14, 263, 11, "luminosity"], [206, 15, 263, 11], [206, 17, 263, 14], [207, 6, 264, 2], [208, 6, 265, 2], [208, 10, 265, 8, "rgb"], [208, 13, 265, 11], [208, 16, 265, 14], [208, 20, 265, 18], [208, 21, 265, 19, "rgb"], [208, 24, 265, 22], [208, 25, 265, 23], [208, 26, 265, 24], [208, 27, 265, 25, "color"], [208, 32, 265, 30], [209, 6, 267, 2], [209, 10, 267, 8, "lum"], [209, 13, 267, 11], [209, 16, 267, 14], [209, 18, 267, 16], [210, 6, 268, 2], [210, 15, 268, 2, "_ref"], [210, 19, 268, 2], [210, 23, 268, 29, "rgb"], [210, 26, 268, 32], [210, 27, 268, 33, "entries"], [210, 34, 268, 40], [210, 35, 268, 41], [210, 36, 268, 42], [210, 38, 268, 44], [211, 8, 268, 44], [211, 12, 268, 44, "_ref2"], [211, 17, 268, 44], [211, 20, 268, 44, "_slicedToArray"], [211, 34, 268, 44], [211, 35, 268, 44, "_ref"], [211, 39, 268, 44], [212, 8, 268, 44], [212, 12, 268, 14, "i"], [212, 13, 268, 15], [212, 16, 268, 15, "_ref2"], [212, 21, 268, 15], [213, 8, 268, 15], [213, 12, 268, 17, "element"], [213, 19, 268, 24], [213, 22, 268, 24, "_ref2"], [213, 27, 268, 24], [214, 8, 269, 3], [214, 12, 269, 9, "chan"], [214, 16, 269, 13], [214, 19, 269, 16, "element"], [214, 26, 269, 23], [214, 29, 269, 26], [214, 32, 269, 29], [215, 8, 270, 3, "lum"], [215, 11, 270, 6], [215, 12, 270, 7, "i"], [215, 13, 270, 8], [215, 14, 270, 9], [215, 17, 270, 13, "chan"], [215, 21, 270, 17], [215, 25, 270, 21], [215, 32, 270, 28], [215, 35, 270, 32, "chan"], [215, 39, 270, 36], [215, 42, 270, 39], [215, 47, 270, 44], [215, 50, 270, 47], [215, 51, 270, 48], [215, 52, 270, 49, "chan"], [215, 56, 270, 53], [215, 59, 270, 56], [215, 64, 270, 61], [215, 68, 270, 65], [215, 73, 270, 70], [215, 78, 270, 75], [215, 81, 270, 78], [216, 6, 271, 2], [217, 6, 273, 2], [217, 13, 273, 9], [217, 19, 273, 15], [217, 22, 273, 18, "lum"], [217, 25, 273, 21], [217, 26, 273, 22], [217, 27, 273, 23], [217, 28, 273, 24], [217, 31, 273, 27], [217, 37, 273, 33], [217, 40, 273, 36, "lum"], [217, 43, 273, 39], [217, 44, 273, 40], [217, 45, 273, 41], [217, 46, 273, 42], [217, 49, 273, 45], [217, 55, 273, 51], [217, 58, 273, 54, "lum"], [217, 61, 273, 57], [217, 62, 273, 58], [217, 63, 273, 59], [217, 64, 273, 60], [218, 4, 274, 1], [218, 5, 274, 2], [219, 4, 276, 1, "contrast"], [219, 12, 276, 9, "contrast"], [219, 13, 276, 10, "color2"], [219, 19, 276, 16], [219, 21, 276, 18], [220, 6, 277, 2], [221, 6, 278, 2], [221, 10, 278, 8, "lum1"], [221, 14, 278, 12], [221, 17, 278, 15], [221, 21, 278, 19], [221, 22, 278, 20, "luminosity"], [221, 32, 278, 30], [221, 33, 278, 31], [221, 34, 278, 32], [222, 6, 279, 2], [222, 10, 279, 8, "lum2"], [222, 14, 279, 12], [222, 17, 279, 15, "color2"], [222, 23, 279, 21], [222, 24, 279, 22, "luminosity"], [222, 34, 279, 32], [222, 35, 279, 33], [222, 36, 279, 34], [223, 6, 281, 2], [223, 10, 281, 6, "lum1"], [223, 14, 281, 10], [223, 17, 281, 13, "lum2"], [223, 21, 281, 17], [223, 23, 281, 19], [224, 8, 282, 3], [224, 15, 282, 10], [224, 16, 282, 11, "lum1"], [224, 20, 282, 15], [224, 23, 282, 18], [224, 27, 282, 22], [224, 32, 282, 27, "lum2"], [224, 36, 282, 31], [224, 39, 282, 34], [224, 43, 282, 38], [224, 44, 282, 39], [225, 6, 283, 2], [226, 6, 285, 2], [226, 13, 285, 9], [226, 14, 285, 10, "lum2"], [226, 18, 285, 14], [226, 21, 285, 17], [226, 25, 285, 21], [226, 30, 285, 26, "lum1"], [226, 34, 285, 30], [226, 37, 285, 33], [226, 41, 285, 37], [226, 42, 285, 38], [227, 4, 286, 1], [227, 5, 286, 2], [228, 4, 288, 1, "level"], [228, 9, 288, 6, "level"], [228, 10, 288, 7, "color2"], [228, 16, 288, 13], [228, 18, 288, 15], [229, 6, 289, 2], [230, 6, 290, 2], [230, 10, 290, 8, "contrastRatio"], [230, 23, 290, 21], [230, 26, 290, 24], [230, 30, 290, 28], [230, 31, 290, 29, "contrast"], [230, 39, 290, 37], [230, 40, 290, 38, "color2"], [230, 46, 290, 44], [230, 47, 290, 45], [231, 6, 291, 2], [231, 10, 291, 6, "contrastRatio"], [231, 23, 291, 19], [231, 27, 291, 23], [231, 28, 291, 24], [231, 30, 291, 26], [232, 8, 292, 3], [232, 15, 292, 10], [232, 20, 292, 15], [233, 6, 293, 2], [234, 6, 295, 2], [234, 13, 295, 10, "contrastRatio"], [234, 26, 295, 23], [234, 30, 295, 27], [234, 33, 295, 30], [234, 36, 295, 34], [234, 40, 295, 38], [234, 43, 295, 41], [234, 45, 295, 43], [235, 4, 296, 1], [235, 5, 296, 2], [236, 4, 298, 1, "isDark"], [236, 10, 298, 7, "isDark"], [236, 11, 298, 7], [236, 13, 298, 10], [237, 6, 299, 2], [238, 6, 300, 2], [238, 10, 300, 8, "rgb"], [238, 13, 300, 11], [238, 16, 300, 14], [238, 20, 300, 18], [238, 21, 300, 19, "rgb"], [238, 24, 300, 22], [238, 25, 300, 23], [238, 26, 300, 24], [238, 27, 300, 25, "color"], [238, 32, 300, 30], [239, 6, 301, 2], [239, 10, 301, 8, "yiq"], [239, 13, 301, 11], [239, 16, 301, 14], [239, 17, 301, 15, "rgb"], [239, 20, 301, 18], [239, 21, 301, 19], [239, 22, 301, 20], [239, 23, 301, 21], [239, 26, 301, 24], [239, 30, 301, 28], [239, 33, 301, 31, "rgb"], [239, 36, 301, 34], [239, 37, 301, 35], [239, 38, 301, 36], [239, 39, 301, 37], [239, 42, 301, 40], [239, 46, 301, 44], [239, 49, 301, 47, "rgb"], [239, 52, 301, 50], [239, 53, 301, 51], [239, 54, 301, 52], [239, 55, 301, 53], [239, 58, 301, 56], [239, 61, 301, 59], [239, 65, 301, 63], [239, 70, 301, 68], [240, 6, 302, 2], [240, 13, 302, 9, "yiq"], [240, 16, 302, 12], [240, 19, 302, 15], [240, 22, 302, 18], [241, 4, 303, 1], [241, 5, 303, 2], [242, 4, 305, 1, "isLight"], [242, 11, 305, 8, "isLight"], [242, 12, 305, 8], [242, 14, 305, 11], [243, 6, 306, 2], [243, 13, 306, 9], [243, 14, 306, 10], [243, 18, 306, 14], [243, 19, 306, 15, "isDark"], [243, 25, 306, 21], [243, 26, 306, 22], [243, 27, 306, 23], [244, 4, 307, 1], [244, 5, 307, 2], [245, 4, 309, 1, "negate"], [245, 10, 309, 7, "negate"], [245, 11, 309, 7], [245, 13, 309, 10], [246, 6, 310, 2], [246, 10, 310, 8, "rgb"], [246, 13, 310, 11], [246, 16, 310, 14], [246, 20, 310, 18], [246, 21, 310, 19, "rgb"], [246, 24, 310, 22], [246, 25, 310, 23], [246, 26, 310, 24], [247, 6, 311, 2], [247, 11, 311, 7], [247, 15, 311, 11, "i"], [247, 16, 311, 12], [247, 19, 311, 15], [247, 20, 311, 16], [247, 22, 311, 18, "i"], [247, 23, 311, 19], [247, 26, 311, 22], [247, 27, 311, 23], [247, 29, 311, 25, "i"], [247, 30, 311, 26], [247, 32, 311, 28], [247, 34, 311, 30], [248, 8, 312, 3, "rgb"], [248, 11, 312, 6], [248, 12, 312, 7, "color"], [248, 17, 312, 12], [248, 18, 312, 13, "i"], [248, 19, 312, 14], [248, 20, 312, 15], [248, 23, 312, 18], [248, 26, 312, 21], [248, 29, 312, 24, "rgb"], [248, 32, 312, 27], [248, 33, 312, 28, "color"], [248, 38, 312, 33], [248, 39, 312, 34, "i"], [248, 40, 312, 35], [248, 41, 312, 36], [249, 6, 313, 2], [250, 6, 315, 2], [250, 13, 315, 9, "rgb"], [250, 16, 315, 12], [251, 4, 316, 1], [251, 5, 316, 2], [252, 4, 318, 1, "lighten"], [252, 11, 318, 8, "lighten"], [252, 12, 318, 9, "ratio"], [252, 17, 318, 14], [252, 19, 318, 16], [253, 6, 319, 2], [253, 10, 319, 8, "hsl"], [253, 13, 319, 11], [253, 16, 319, 14], [253, 20, 319, 18], [253, 21, 319, 19, "hsl"], [253, 24, 319, 22], [253, 25, 319, 23], [253, 26, 319, 24], [254, 6, 320, 2, "hsl"], [254, 9, 320, 5], [254, 10, 320, 6, "color"], [254, 15, 320, 11], [254, 16, 320, 12], [254, 17, 320, 13], [254, 18, 320, 14], [254, 22, 320, 18, "hsl"], [254, 25, 320, 21], [254, 26, 320, 22, "color"], [254, 31, 320, 27], [254, 32, 320, 28], [254, 33, 320, 29], [254, 34, 320, 30], [254, 37, 320, 33, "ratio"], [254, 42, 320, 38], [255, 6, 321, 2], [255, 13, 321, 9, "hsl"], [255, 16, 321, 12], [256, 4, 322, 1], [256, 5, 322, 2], [257, 4, 324, 1, "darken"], [257, 10, 324, 7, "darken"], [257, 11, 324, 8, "ratio"], [257, 16, 324, 13], [257, 18, 324, 15], [258, 6, 325, 2], [258, 10, 325, 8, "hsl"], [258, 13, 325, 11], [258, 16, 325, 14], [258, 20, 325, 18], [258, 21, 325, 19, "hsl"], [258, 24, 325, 22], [258, 25, 325, 23], [258, 26, 325, 24], [259, 6, 326, 2, "hsl"], [259, 9, 326, 5], [259, 10, 326, 6, "color"], [259, 15, 326, 11], [259, 16, 326, 12], [259, 17, 326, 13], [259, 18, 326, 14], [259, 22, 326, 18, "hsl"], [259, 25, 326, 21], [259, 26, 326, 22, "color"], [259, 31, 326, 27], [259, 32, 326, 28], [259, 33, 326, 29], [259, 34, 326, 30], [259, 37, 326, 33, "ratio"], [259, 42, 326, 38], [260, 6, 327, 2], [260, 13, 327, 9, "hsl"], [260, 16, 327, 12], [261, 4, 328, 1], [261, 5, 328, 2], [262, 4, 330, 1, "saturate"], [262, 12, 330, 9, "saturate"], [262, 13, 330, 10, "ratio"], [262, 18, 330, 15], [262, 20, 330, 17], [263, 6, 331, 2], [263, 10, 331, 8, "hsl"], [263, 13, 331, 11], [263, 16, 331, 14], [263, 20, 331, 18], [263, 21, 331, 19, "hsl"], [263, 24, 331, 22], [263, 25, 331, 23], [263, 26, 331, 24], [264, 6, 332, 2, "hsl"], [264, 9, 332, 5], [264, 10, 332, 6, "color"], [264, 15, 332, 11], [264, 16, 332, 12], [264, 17, 332, 13], [264, 18, 332, 14], [264, 22, 332, 18, "hsl"], [264, 25, 332, 21], [264, 26, 332, 22, "color"], [264, 31, 332, 27], [264, 32, 332, 28], [264, 33, 332, 29], [264, 34, 332, 30], [264, 37, 332, 33, "ratio"], [264, 42, 332, 38], [265, 6, 333, 2], [265, 13, 333, 9, "hsl"], [265, 16, 333, 12], [266, 4, 334, 1], [266, 5, 334, 2], [267, 4, 336, 1, "desaturate"], [267, 14, 336, 11, "desaturate"], [267, 15, 336, 12, "ratio"], [267, 20, 336, 17], [267, 22, 336, 19], [268, 6, 337, 2], [268, 10, 337, 8, "hsl"], [268, 13, 337, 11], [268, 16, 337, 14], [268, 20, 337, 18], [268, 21, 337, 19, "hsl"], [268, 24, 337, 22], [268, 25, 337, 23], [268, 26, 337, 24], [269, 6, 338, 2, "hsl"], [269, 9, 338, 5], [269, 10, 338, 6, "color"], [269, 15, 338, 11], [269, 16, 338, 12], [269, 17, 338, 13], [269, 18, 338, 14], [269, 22, 338, 18, "hsl"], [269, 25, 338, 21], [269, 26, 338, 22, "color"], [269, 31, 338, 27], [269, 32, 338, 28], [269, 33, 338, 29], [269, 34, 338, 30], [269, 37, 338, 33, "ratio"], [269, 42, 338, 38], [270, 6, 339, 2], [270, 13, 339, 9, "hsl"], [270, 16, 339, 12], [271, 4, 340, 1], [271, 5, 340, 2], [272, 4, 342, 1, "whiten"], [272, 10, 342, 7, "whiten"], [272, 11, 342, 8, "ratio"], [272, 16, 342, 13], [272, 18, 342, 15], [273, 6, 343, 2], [273, 10, 343, 8, "hwb"], [273, 13, 343, 11], [273, 16, 343, 14], [273, 20, 343, 18], [273, 21, 343, 19, "hwb"], [273, 24, 343, 22], [273, 25, 343, 23], [273, 26, 343, 24], [274, 6, 344, 2, "hwb"], [274, 9, 344, 5], [274, 10, 344, 6, "color"], [274, 15, 344, 11], [274, 16, 344, 12], [274, 17, 344, 13], [274, 18, 344, 14], [274, 22, 344, 18, "hwb"], [274, 25, 344, 21], [274, 26, 344, 22, "color"], [274, 31, 344, 27], [274, 32, 344, 28], [274, 33, 344, 29], [274, 34, 344, 30], [274, 37, 344, 33, "ratio"], [274, 42, 344, 38], [275, 6, 345, 2], [275, 13, 345, 9, "hwb"], [275, 16, 345, 12], [276, 4, 346, 1], [276, 5, 346, 2], [277, 4, 348, 1, "blacken"], [277, 11, 348, 8, "blacken"], [277, 12, 348, 9, "ratio"], [277, 17, 348, 14], [277, 19, 348, 16], [278, 6, 349, 2], [278, 10, 349, 8, "hwb"], [278, 13, 349, 11], [278, 16, 349, 14], [278, 20, 349, 18], [278, 21, 349, 19, "hwb"], [278, 24, 349, 22], [278, 25, 349, 23], [278, 26, 349, 24], [279, 6, 350, 2, "hwb"], [279, 9, 350, 5], [279, 10, 350, 6, "color"], [279, 15, 350, 11], [279, 16, 350, 12], [279, 17, 350, 13], [279, 18, 350, 14], [279, 22, 350, 18, "hwb"], [279, 25, 350, 21], [279, 26, 350, 22, "color"], [279, 31, 350, 27], [279, 32, 350, 28], [279, 33, 350, 29], [279, 34, 350, 30], [279, 37, 350, 33, "ratio"], [279, 42, 350, 38], [280, 6, 351, 2], [280, 13, 351, 9, "hwb"], [280, 16, 351, 12], [281, 4, 352, 1], [281, 5, 352, 2], [282, 4, 354, 1, "grayscale"], [282, 13, 354, 10, "grayscale"], [282, 14, 354, 10], [282, 16, 354, 13], [283, 6, 355, 2], [284, 6, 356, 2], [284, 10, 356, 8, "rgb"], [284, 13, 356, 11], [284, 16, 356, 14], [284, 20, 356, 18], [284, 21, 356, 19, "rgb"], [284, 24, 356, 22], [284, 25, 356, 23], [284, 26, 356, 24], [284, 27, 356, 25, "color"], [284, 32, 356, 30], [285, 6, 357, 2], [285, 10, 357, 8, "value"], [285, 15, 357, 13], [285, 18, 357, 16, "rgb"], [285, 21, 357, 19], [285, 22, 357, 20], [285, 23, 357, 21], [285, 24, 357, 22], [285, 27, 357, 25], [285, 30, 357, 28], [285, 33, 357, 31, "rgb"], [285, 36, 357, 34], [285, 37, 357, 35], [285, 38, 357, 36], [285, 39, 357, 37], [285, 42, 357, 40], [285, 46, 357, 44], [285, 49, 357, 47, "rgb"], [285, 52, 357, 50], [285, 53, 357, 51], [285, 54, 357, 52], [285, 55, 357, 53], [285, 58, 357, 56], [285, 62, 357, 60], [286, 6, 358, 2], [286, 13, 358, 9, "Color"], [286, 18, 358, 14], [286, 19, 358, 15, "rgb"], [286, 22, 358, 18], [286, 23, 358, 19, "value"], [286, 28, 358, 24], [286, 30, 358, 26, "value"], [286, 35, 358, 31], [286, 37, 358, 33, "value"], [286, 42, 358, 38], [286, 43, 358, 39], [287, 4, 359, 1], [287, 5, 359, 2], [288, 4, 361, 1, "fade"], [288, 8, 361, 5, "fade"], [288, 9, 361, 6, "ratio"], [288, 14, 361, 11], [288, 16, 361, 13], [289, 6, 362, 2], [289, 13, 362, 9], [289, 17, 362, 13], [289, 18, 362, 14, "alpha"], [289, 23, 362, 19], [289, 24, 362, 20], [289, 28, 362, 24], [289, 29, 362, 25, "valpha"], [289, 35, 362, 31], [289, 38, 362, 35], [289, 42, 362, 39], [289, 43, 362, 40, "valpha"], [289, 49, 362, 46], [289, 52, 362, 49, "ratio"], [289, 57, 362, 55], [289, 58, 362, 56], [290, 4, 363, 1], [290, 5, 363, 2], [291, 4, 365, 1, "opaquer"], [291, 11, 365, 8, "opaquer"], [291, 12, 365, 9, "ratio"], [291, 17, 365, 14], [291, 19, 365, 16], [292, 6, 366, 2], [292, 13, 366, 9], [292, 17, 366, 13], [292, 18, 366, 14, "alpha"], [292, 23, 366, 19], [292, 24, 366, 20], [292, 28, 366, 24], [292, 29, 366, 25, "valpha"], [292, 35, 366, 31], [292, 38, 366, 35], [292, 42, 366, 39], [292, 43, 366, 40, "valpha"], [292, 49, 366, 46], [292, 52, 366, 49, "ratio"], [292, 57, 366, 55], [292, 58, 366, 56], [293, 4, 367, 1], [293, 5, 367, 2], [294, 4, 369, 1, "rotate"], [294, 10, 369, 7, "rotate"], [294, 11, 369, 8, "degrees"], [294, 18, 369, 15], [294, 20, 369, 17], [295, 6, 370, 2], [295, 10, 370, 8, "hsl"], [295, 13, 370, 11], [295, 16, 370, 14], [295, 20, 370, 18], [295, 21, 370, 19, "hsl"], [295, 24, 370, 22], [295, 25, 370, 23], [295, 26, 370, 24], [296, 6, 371, 2], [296, 10, 371, 6, "hue"], [296, 13, 371, 9], [296, 16, 371, 12, "hsl"], [296, 19, 371, 15], [296, 20, 371, 16, "color"], [296, 25, 371, 21], [296, 26, 371, 22], [296, 27, 371, 23], [296, 28, 371, 24], [297, 6, 372, 2, "hue"], [297, 9, 372, 5], [297, 12, 372, 8], [297, 13, 372, 9, "hue"], [297, 16, 372, 12], [297, 19, 372, 15, "degrees"], [297, 26, 372, 22], [297, 30, 372, 26], [297, 33, 372, 29], [298, 6, 373, 2, "hue"], [298, 9, 373, 5], [298, 12, 373, 8, "hue"], [298, 15, 373, 11], [298, 18, 373, 14], [298, 19, 373, 15], [298, 22, 373, 18], [298, 25, 373, 21], [298, 28, 373, 24, "hue"], [298, 31, 373, 27], [298, 34, 373, 30, "hue"], [298, 37, 373, 33], [299, 6, 374, 2, "hsl"], [299, 9, 374, 5], [299, 10, 374, 6, "color"], [299, 15, 374, 11], [299, 16, 374, 12], [299, 17, 374, 13], [299, 18, 374, 14], [299, 21, 374, 17, "hue"], [299, 24, 374, 20], [300, 6, 375, 2], [300, 13, 375, 9, "hsl"], [300, 16, 375, 12], [301, 4, 376, 1], [301, 5, 376, 2], [302, 4, 378, 1, "mix"], [302, 7, 378, 4, "mix"], [302, 8, 378, 5, "mixinColor"], [302, 18, 378, 15], [302, 20, 378, 17, "weight"], [302, 26, 378, 23], [302, 28, 378, 25], [303, 6, 379, 2], [304, 6, 380, 2], [305, 6, 381, 2], [305, 10, 381, 6], [305, 11, 381, 7, "mixinColor"], [305, 21, 381, 17], [305, 25, 381, 21], [305, 26, 381, 22, "mixinColor"], [305, 36, 381, 32], [305, 37, 381, 33, "rgb"], [305, 40, 381, 36], [305, 42, 381, 38], [306, 8, 382, 3], [306, 14, 382, 9], [306, 18, 382, 13, "Error"], [306, 23, 382, 18], [306, 24, 382, 19], [306, 96, 382, 91], [306, 99, 382, 94], [306, 106, 382, 101, "mixinColor"], [306, 116, 382, 111], [306, 117, 382, 112], [307, 6, 383, 2], [308, 6, 385, 2], [308, 10, 385, 8, "color1"], [308, 16, 385, 14], [308, 19, 385, 17, "mixinColor"], [308, 29, 385, 27], [308, 30, 385, 28, "rgb"], [308, 33, 385, 31], [308, 34, 385, 32], [308, 35, 385, 33], [309, 6, 386, 2], [309, 10, 386, 8, "color2"], [309, 16, 386, 14], [309, 19, 386, 17], [309, 23, 386, 21], [309, 24, 386, 22, "rgb"], [309, 27, 386, 25], [309, 28, 386, 26], [309, 29, 386, 27], [310, 6, 387, 2], [310, 10, 387, 8, "p"], [310, 11, 387, 9], [310, 14, 387, 12, "weight"], [310, 20, 387, 18], [310, 25, 387, 23, "undefined"], [310, 34, 387, 32], [310, 37, 387, 35], [310, 40, 387, 38], [310, 43, 387, 41, "weight"], [310, 49, 387, 47], [311, 6, 389, 2], [311, 10, 389, 8, "w"], [311, 11, 389, 9], [311, 14, 389, 12], [311, 15, 389, 13], [311, 18, 389, 16, "p"], [311, 19, 389, 17], [311, 22, 389, 20], [311, 23, 389, 21], [312, 6, 390, 2], [312, 10, 390, 8, "a"], [312, 11, 390, 9], [312, 14, 390, 12, "color1"], [312, 20, 390, 18], [312, 21, 390, 19, "alpha"], [312, 26, 390, 24], [312, 27, 390, 25], [312, 28, 390, 26], [312, 31, 390, 29, "color2"], [312, 37, 390, 35], [312, 38, 390, 36, "alpha"], [312, 43, 390, 41], [312, 44, 390, 42], [312, 45, 390, 43], [313, 6, 392, 2], [313, 10, 392, 8, "w1"], [313, 12, 392, 10], [313, 15, 392, 13], [313, 16, 392, 14], [313, 17, 392, 16, "w"], [313, 18, 392, 17], [313, 21, 392, 20, "a"], [313, 22, 392, 21], [313, 27, 392, 26], [313, 28, 392, 27], [313, 29, 392, 28], [313, 32, 392, 32, "w"], [313, 33, 392, 33], [313, 36, 392, 36], [313, 37, 392, 37, "w"], [313, 38, 392, 38], [313, 41, 392, 41, "a"], [313, 42, 392, 42], [313, 47, 392, 47], [313, 48, 392, 48], [313, 51, 392, 51, "w"], [313, 52, 392, 52], [313, 55, 392, 55, "a"], [313, 56, 392, 56], [313, 57, 392, 57], [313, 61, 392, 61], [313, 62, 392, 62], [313, 66, 392, 66], [313, 67, 392, 67], [314, 6, 393, 2], [314, 10, 393, 8, "w2"], [314, 12, 393, 10], [314, 15, 393, 13], [314, 16, 393, 14], [314, 19, 393, 17, "w1"], [314, 21, 393, 19], [315, 6, 395, 2], [315, 13, 395, 9, "Color"], [315, 18, 395, 14], [315, 19, 395, 15, "rgb"], [315, 22, 395, 18], [315, 23, 396, 3, "w1"], [315, 25, 396, 5], [315, 28, 396, 8, "color1"], [315, 34, 396, 14], [315, 35, 396, 15, "red"], [315, 38, 396, 18], [315, 39, 396, 19], [315, 40, 396, 20], [315, 43, 396, 23, "w2"], [315, 45, 396, 25], [315, 48, 396, 28, "color2"], [315, 54, 396, 34], [315, 55, 396, 35, "red"], [315, 58, 396, 38], [315, 59, 396, 39], [315, 60, 396, 40], [315, 62, 397, 3, "w1"], [315, 64, 397, 5], [315, 67, 397, 8, "color1"], [315, 73, 397, 14], [315, 74, 397, 15, "green"], [315, 79, 397, 20], [315, 80, 397, 21], [315, 81, 397, 22], [315, 84, 397, 25, "w2"], [315, 86, 397, 27], [315, 89, 397, 30, "color2"], [315, 95, 397, 36], [315, 96, 397, 37, "green"], [315, 101, 397, 42], [315, 102, 397, 43], [315, 103, 397, 44], [315, 105, 398, 3, "w1"], [315, 107, 398, 5], [315, 110, 398, 8, "color1"], [315, 116, 398, 14], [315, 117, 398, 15, "blue"], [315, 121, 398, 19], [315, 122, 398, 20], [315, 123, 398, 21], [315, 126, 398, 24, "w2"], [315, 128, 398, 26], [315, 131, 398, 29, "color2"], [315, 137, 398, 35], [315, 138, 398, 36, "blue"], [315, 142, 398, 40], [315, 143, 398, 41], [315, 144, 398, 42], [315, 146, 399, 3, "color1"], [315, 152, 399, 9], [315, 153, 399, 10, "alpha"], [315, 158, 399, 15], [315, 159, 399, 16], [315, 160, 399, 17], [315, 163, 399, 20, "p"], [315, 164, 399, 21], [315, 167, 399, 24, "color2"], [315, 173, 399, 30], [315, 174, 399, 31, "alpha"], [315, 179, 399, 36], [315, 180, 399, 37], [315, 181, 399, 38], [315, 185, 399, 42], [315, 186, 399, 43], [315, 189, 399, 46, "p"], [315, 190, 399, 47], [315, 191, 399, 48], [315, 192, 399, 49], [316, 4, 400, 1], [317, 2, 401, 0], [317, 3, 401, 1], [319, 2, 403, 0], [320, 2, 403, 0], [320, 6, 403, 0, "_loop"], [320, 11, 403, 0], [320, 23, 403, 0, "_loop"], [320, 24, 403, 0, "_model"], [320, 30, 403, 0], [320, 32, 404, 42], [321, 4, 405, 1], [321, 8, 405, 5, "skippedModels"], [321, 21, 405, 18], [321, 22, 405, 19, "includes"], [321, 30, 405, 27], [321, 31, 405, 28, "model"], [321, 37, 405, 33], [321, 38, 405, 34], [321, 40, 405, 36], [322, 6, 405, 36], [323, 4, 407, 1], [324, 4, 409, 1], [324, 8, 409, 8, "channels"], [324, 16, 409, 16], [324, 19, 409, 20, "convert"], [324, 26, 409, 27], [324, 27, 409, 28, "model"], [324, 33, 409, 33], [324, 34, 409, 34], [324, 35, 409, 8, "channels"], [324, 43, 409, 16], [326, 4, 411, 1], [327, 4, 412, 1, "Color"], [327, 9, 412, 6], [327, 10, 412, 7, "prototype"], [327, 19, 412, 16], [327, 20, 412, 17, "model"], [327, 26, 412, 22], [327, 27, 412, 23], [327, 30, 412, 26], [327, 42, 412, 45], [328, 6, 413, 2], [328, 10, 413, 6], [328, 14, 413, 10], [328, 15, 413, 11, "model"], [328, 20, 413, 16], [328, 25, 413, 21, "model"], [328, 31, 413, 26], [328, 33, 413, 28], [329, 8, 414, 3], [329, 15, 414, 10], [329, 19, 414, 14, "Color"], [329, 24, 414, 19], [329, 25, 414, 20], [329, 29, 414, 24], [329, 30, 414, 25], [330, 6, 415, 2], [331, 6, 415, 3], [331, 15, 415, 3, "_len"], [331, 19, 415, 3], [331, 22, 415, 3, "arguments"], [331, 31, 415, 3], [331, 32, 415, 3, "length"], [331, 38, 415, 3], [331, 40, 412, 39, "args"], [331, 44, 412, 43], [331, 51, 412, 43, "Array"], [331, 56, 412, 43], [331, 57, 412, 43, "_len"], [331, 61, 412, 43], [331, 64, 412, 43, "_key"], [331, 68, 412, 43], [331, 74, 412, 43, "_key"], [331, 78, 412, 43], [331, 81, 412, 43, "_len"], [331, 85, 412, 43], [331, 87, 412, 43, "_key"], [331, 91, 412, 43], [332, 8, 412, 39, "args"], [332, 12, 412, 43], [332, 13, 412, 43, "_key"], [332, 17, 412, 43], [332, 21, 412, 43, "arguments"], [332, 30, 412, 43], [332, 31, 412, 43, "_key"], [332, 35, 412, 43], [333, 6, 412, 43], [334, 6, 417, 2], [334, 10, 417, 6, "args"], [334, 14, 417, 10], [334, 15, 417, 11, "length"], [334, 21, 417, 17], [334, 24, 417, 20], [334, 25, 417, 21], [334, 27, 417, 23], [335, 8, 418, 3], [335, 15, 418, 10], [335, 19, 418, 14, "Color"], [335, 24, 418, 19], [335, 25, 418, 20, "args"], [335, 29, 418, 24], [335, 31, 418, 26, "model"], [335, 37, 418, 31], [335, 38, 418, 32], [336, 6, 419, 2], [337, 6, 421, 2], [337, 13, 421, 9], [337, 17, 421, 13, "Color"], [337, 22, 421, 18], [337, 23, 421, 19], [337, 24, 421, 20], [337, 27, 421, 23, "assertArray"], [337, 38, 421, 34], [337, 39, 421, 35, "convert"], [337, 46, 421, 42], [337, 47, 421, 43], [337, 51, 421, 47], [337, 52, 421, 48, "model"], [337, 57, 421, 53], [337, 58, 421, 54], [337, 59, 421, 55, "model"], [337, 65, 421, 60], [337, 66, 421, 61], [337, 67, 421, 62, "raw"], [337, 70, 421, 65], [337, 71, 421, 66], [337, 75, 421, 70], [337, 76, 421, 71, "color"], [337, 81, 421, 76], [337, 82, 421, 77], [337, 83, 421, 78], [337, 85, 421, 80], [337, 89, 421, 84], [337, 90, 421, 85, "valpha"], [337, 96, 421, 91], [337, 97, 421, 92], [337, 99, 421, 94, "model"], [337, 105, 421, 99], [337, 106, 421, 100], [338, 4, 422, 1], [338, 5, 422, 2], [340, 4, 424, 1], [341, 4, 425, 1, "Color"], [341, 9, 425, 6], [341, 10, 425, 7, "model"], [341, 16, 425, 12], [341, 17, 425, 13], [341, 20, 425, 16], [341, 32, 425, 35], [342, 6, 425, 35], [342, 15, 425, 35, "_len2"], [342, 20, 425, 35], [342, 23, 425, 35, "arguments"], [342, 32, 425, 35], [342, 33, 425, 35, "length"], [342, 39, 425, 35], [342, 41, 425, 29, "args"], [342, 45, 425, 33], [342, 52, 425, 33, "Array"], [342, 57, 425, 33], [342, 58, 425, 33, "_len2"], [342, 63, 425, 33], [342, 66, 425, 33, "_key2"], [342, 71, 425, 33], [342, 77, 425, 33, "_key2"], [342, 82, 425, 33], [342, 85, 425, 33, "_len2"], [342, 90, 425, 33], [342, 92, 425, 33, "_key2"], [342, 97, 425, 33], [343, 8, 425, 29, "args"], [343, 12, 425, 33], [343, 13, 425, 33, "_key2"], [343, 18, 425, 33], [343, 22, 425, 33, "arguments"], [343, 31, 425, 33], [343, 32, 425, 33, "_key2"], [343, 37, 425, 33], [344, 6, 425, 33], [345, 6, 426, 2], [345, 10, 426, 6, "color"], [345, 15, 426, 11], [345, 18, 426, 14, "args"], [345, 22, 426, 18], [345, 23, 426, 19], [345, 24, 426, 20], [345, 25, 426, 21], [346, 6, 427, 2], [346, 10, 427, 6], [346, 17, 427, 13, "color"], [346, 22, 427, 18], [346, 27, 427, 23], [346, 35, 427, 31], [346, 37, 427, 33], [347, 8, 428, 3, "color"], [347, 13, 428, 8], [347, 16, 428, 11, "zeroArray"], [347, 25, 428, 20], [347, 26, 428, 21, "args"], [347, 30, 428, 25], [347, 32, 428, 27, "channels"], [347, 40, 428, 35], [347, 41, 428, 36], [348, 6, 429, 2], [349, 6, 431, 2], [349, 13, 431, 9], [349, 17, 431, 13, "Color"], [349, 22, 431, 18], [349, 23, 431, 19, "color"], [349, 28, 431, 24], [349, 30, 431, 26, "model"], [349, 36, 431, 31], [349, 37, 431, 32], [350, 4, 432, 1], [350, 5, 432, 2], [351, 2, 433, 0], [351, 3, 433, 1], [352, 2, 404, 0], [352, 7, 404, 5], [352, 11, 404, 11, "model"], [352, 17, 404, 16], [352, 21, 404, 20, "Object"], [352, 27, 404, 26], [352, 28, 404, 27, "keys"], [352, 32, 404, 31], [352, 33, 404, 32, "convert"], [352, 40, 404, 39], [352, 41, 404, 40], [353, 4, 404, 40], [353, 8, 404, 40, "_loop"], [353, 13, 404, 40], [353, 14, 404, 40, "_model"], [353, 20, 404, 40], [353, 23, 406, 2], [354, 2, 406, 11], [355, 2, 435, 0], [355, 11, 435, 9, "roundTo"], [355, 18, 435, 16, "roundTo"], [355, 19, 435, 17, "number"], [355, 25, 435, 23], [355, 27, 435, 25, "places"], [355, 33, 435, 31], [355, 35, 435, 33], [356, 4, 436, 1], [356, 11, 436, 8, "Number"], [356, 17, 436, 14], [356, 18, 436, 15, "number"], [356, 24, 436, 21], [356, 25, 436, 22, "toFixed"], [356, 32, 436, 29], [356, 33, 436, 30, "places"], [356, 39, 436, 36], [356, 40, 436, 37], [356, 41, 436, 38], [357, 2, 437, 0], [358, 2, 439, 0], [358, 11, 439, 9, "roundToPlace"], [358, 23, 439, 21, "roundToPlace"], [358, 24, 439, 22, "places"], [358, 30, 439, 28], [358, 32, 439, 30], [359, 4, 440, 1], [359, 11, 440, 8], [359, 21, 440, 18, "number"], [359, 27, 440, 24], [359, 29, 440, 26], [360, 6, 441, 2], [360, 13, 441, 9, "roundTo"], [360, 20, 441, 16], [360, 21, 441, 17, "number"], [360, 27, 441, 23], [360, 29, 441, 25, "places"], [360, 35, 441, 31], [360, 36, 441, 32], [361, 4, 442, 1], [361, 5, 442, 2], [362, 2, 443, 0], [363, 2, 445, 0], [363, 11, 445, 9, "getset"], [363, 17, 445, 15, "getset"], [363, 18, 445, 16, "model"], [363, 23, 445, 21], [363, 25, 445, 23, "channel"], [363, 32, 445, 30], [363, 34, 445, 32, "modifier"], [363, 42, 445, 40], [363, 44, 445, 42], [364, 4, 446, 1, "model"], [364, 9, 446, 6], [364, 12, 446, 9, "Array"], [364, 17, 446, 14], [364, 18, 446, 15, "isArray"], [364, 25, 446, 22], [364, 26, 446, 23, "model"], [364, 31, 446, 28], [364, 32, 446, 29], [364, 35, 446, 32, "model"], [364, 40, 446, 37], [364, 43, 446, 40], [364, 44, 446, 41, "model"], [364, 49, 446, 46], [364, 50, 446, 47], [365, 4, 448, 1], [365, 9, 448, 6], [365, 13, 448, 12, "m"], [365, 14, 448, 13], [365, 18, 448, 17, "model"], [365, 23, 448, 22], [365, 25, 448, 24], [366, 6, 449, 2], [366, 7, 449, 3, "limiters"], [366, 15, 449, 11], [366, 16, 449, 12, "m"], [366, 17, 449, 13], [366, 18, 449, 14], [366, 23, 449, 19, "limiters"], [366, 31, 449, 27], [366, 32, 449, 28, "m"], [366, 33, 449, 29], [366, 34, 449, 30], [366, 37, 449, 33], [366, 39, 449, 35], [366, 40, 449, 36], [366, 42, 449, 38, "channel"], [366, 49, 449, 45], [366, 50, 449, 46], [366, 53, 449, 49, "modifier"], [366, 61, 449, 57], [367, 4, 450, 1], [368, 4, 452, 1, "model"], [368, 9, 452, 6], [368, 12, 452, 9, "model"], [368, 17, 452, 14], [368, 18, 452, 15], [368, 19, 452, 16], [368, 20, 452, 17], [369, 4, 454, 1], [369, 11, 454, 8], [369, 21, 454, 18, "value"], [369, 26, 454, 23], [369, 28, 454, 25], [370, 6, 455, 2], [370, 10, 455, 6, "result"], [370, 16, 455, 12], [371, 6, 457, 2], [371, 10, 457, 6, "value"], [371, 15, 457, 11], [371, 20, 457, 16, "undefined"], [371, 29, 457, 25], [371, 31, 457, 27], [372, 8, 458, 3], [372, 12, 458, 7, "modifier"], [372, 20, 458, 15], [372, 22, 458, 17], [373, 10, 459, 4, "value"], [373, 15, 459, 9], [373, 18, 459, 12, "modifier"], [373, 26, 459, 20], [373, 27, 459, 21, "value"], [373, 32, 459, 26], [373, 33, 459, 27], [374, 8, 460, 3], [375, 8, 462, 3, "result"], [375, 14, 462, 9], [375, 17, 462, 12], [375, 21, 462, 16], [375, 22, 462, 17, "model"], [375, 27, 462, 22], [375, 28, 462, 23], [375, 29, 462, 24], [375, 30, 462, 25], [376, 8, 463, 3, "result"], [376, 14, 463, 9], [376, 15, 463, 10, "color"], [376, 20, 463, 15], [376, 21, 463, 16, "channel"], [376, 28, 463, 23], [376, 29, 463, 24], [376, 32, 463, 27, "value"], [376, 37, 463, 32], [377, 8, 464, 3], [377, 15, 464, 10, "result"], [377, 21, 464, 16], [378, 6, 465, 2], [379, 6, 467, 2, "result"], [379, 12, 467, 8], [379, 15, 467, 11], [379, 19, 467, 15], [379, 20, 467, 16, "model"], [379, 25, 467, 21], [379, 26, 467, 22], [379, 27, 467, 23], [379, 28, 467, 24], [379, 29, 467, 25, "color"], [379, 34, 467, 30], [379, 35, 467, 31, "channel"], [379, 42, 467, 38], [379, 43, 467, 39], [380, 6, 468, 2], [380, 10, 468, 6, "modifier"], [380, 18, 468, 14], [380, 20, 468, 16], [381, 8, 469, 3, "result"], [381, 14, 469, 9], [381, 17, 469, 12, "modifier"], [381, 25, 469, 20], [381, 26, 469, 21, "result"], [381, 32, 469, 27], [381, 33, 469, 28], [382, 6, 470, 2], [383, 6, 472, 2], [383, 13, 472, 9, "result"], [383, 19, 472, 15], [384, 4, 473, 1], [384, 5, 473, 2], [385, 2, 474, 0], [386, 2, 476, 0], [386, 11, 476, 9, "maxfn"], [386, 16, 476, 14, "maxfn"], [386, 17, 476, 15, "max"], [386, 20, 476, 18], [386, 22, 476, 20], [387, 4, 477, 1], [387, 11, 477, 8], [387, 21, 477, 18, "v"], [387, 22, 477, 19], [387, 24, 477, 21], [388, 6, 478, 2], [388, 13, 478, 9, "Math"], [388, 17, 478, 13], [388, 18, 478, 14, "max"], [388, 21, 478, 17], [388, 22, 478, 18], [388, 23, 478, 19], [388, 25, 478, 21, "Math"], [388, 29, 478, 25], [388, 30, 478, 26, "min"], [388, 33, 478, 29], [388, 34, 478, 30, "max"], [388, 37, 478, 33], [388, 39, 478, 35, "v"], [388, 40, 478, 36], [388, 41, 478, 37], [388, 42, 478, 38], [389, 4, 479, 1], [389, 5, 479, 2], [390, 2, 480, 0], [391, 2, 482, 0], [391, 11, 482, 9, "assertArray"], [391, 22, 482, 20, "assertArray"], [391, 23, 482, 21, "value"], [391, 28, 482, 26], [391, 30, 482, 28], [392, 4, 483, 1], [392, 11, 483, 8, "Array"], [392, 16, 483, 13], [392, 17, 483, 14, "isArray"], [392, 24, 483, 21], [392, 25, 483, 22, "value"], [392, 30, 483, 27], [392, 31, 483, 28], [392, 34, 483, 31, "value"], [392, 39, 483, 36], [392, 42, 483, 39], [392, 43, 483, 40, "value"], [392, 48, 483, 45], [392, 49, 483, 46], [393, 2, 484, 0], [394, 2, 486, 0], [394, 11, 486, 9, "zeroArray"], [394, 20, 486, 18, "zeroArray"], [394, 21, 486, 19, "array"], [394, 26, 486, 24], [394, 28, 486, 26, "length"], [394, 34, 486, 32], [394, 36, 486, 34], [395, 4, 487, 1], [395, 9, 487, 6], [395, 13, 487, 10, "i"], [395, 14, 487, 11], [395, 17, 487, 14], [395, 18, 487, 15], [395, 20, 487, 17, "i"], [395, 21, 487, 18], [395, 24, 487, 21, "length"], [395, 30, 487, 27], [395, 32, 487, 29, "i"], [395, 33, 487, 30], [395, 35, 487, 32], [395, 37, 487, 34], [396, 6, 488, 2], [396, 10, 488, 6], [396, 17, 488, 13, "array"], [396, 22, 488, 18], [396, 23, 488, 19, "i"], [396, 24, 488, 20], [396, 25, 488, 21], [396, 30, 488, 26], [396, 38, 488, 34], [396, 40, 488, 36], [397, 8, 489, 3, "array"], [397, 13, 489, 8], [397, 14, 489, 9, "i"], [397, 15, 489, 10], [397, 16, 489, 11], [397, 19, 489, 14], [397, 20, 489, 15], [398, 6, 490, 2], [399, 4, 491, 1], [400, 4, 493, 1], [400, 11, 493, 8, "array"], [400, 16, 493, 13], [401, 2, 494, 0], [402, 2, 496, 0, "module"], [402, 8, 496, 6], [402, 9, 496, 7, "exports"], [402, 16, 496, 14], [402, 19, 496, 17, "Color"], [402, 24, 496, 22], [403, 0, 496, 23], [403, 3]], "functionMap": {"names": ["<global>", "Color", "Color.prototype.toString", "Color.prototype.toJSON", "Color.prototype.string", "Color.prototype.percentString", "Color.prototype.array", "Color.prototype.object", "Color.prototype.unitArray", "Color.prototype.unitObject", "Color.prototype.round", "Color.prototype.alpha", "getset$argument_2", "Color.prototype.keyword", "Color.prototype.hex", "Color.prototype.hexa", "Color.prototype.rgbNumber", "Color.prototype.luminosity", "Color.prototype.contrast", "Color.prototype.level", "Color.prototype.isDark", "Color.prototype.isLight", "Color.prototype.negate", "Color.prototype.lighten", "Color.prototype.darken", "Color.prototype.saturate", "Color.prototype.desaturate", "Color.prototype.whiten", "Color.prototype.blacken", "Color.prototype.grayscale", "Color.prototype.fade", "Color.prototype.opaquer", "Color.prototype.rotate", "Color.prototype.mix", "Color.prototype.model", "Color.model", "roundTo", "roundToPlace", "<anonymous>", "getset", "maxfn", "assertArray", "zeroArray"], "mappings": "AAA;ACqB;CD0F;CEG;EFE;CGE;EHE;CIE;EJK;CKE;ELI;CME;ENE;COE;EPc;CQE;ERW;CSE;ETW;CUE;EVG;CWE;EXM;qDYO,oCZ;Ca2B;EbM;CcE;EdM;CeE;Efa;CgBE;EhBG;CiBE;EjBW;CkBE;ElBU;CmBE;EnBQ;CoBE;EpBK;CqBE;ErBE;CsBE;EtBO;CuBE;EvBI;CwBE;ExBI;CyBE;EzBI;C0BE;E1BI;C2BE;E3BI;C4BE;E5BI;C6BE;E7BK;C8BE;E9BE;C+BE;E/BE;CgCE;EhCO;CiCE;EjCsB;0BkCY;ElCU;gBmCG;EnCO;AoCG;CpCE;AqCE;QCC;EDE;CrCC;AuCE;QDS;ECmB;CvCC;AwCE;QFC;EEE;CxCC;AyCE;CzCE;A0CE;C1CQ"}}, "type": "js/module"}]}