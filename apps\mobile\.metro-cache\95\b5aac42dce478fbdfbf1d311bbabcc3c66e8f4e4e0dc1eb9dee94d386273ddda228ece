{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 43, "index": 43}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./attachHandlers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 238}, "end": {"line": 10, "column": 50, "index": 288}}], "key": "3mjR74KCCo5t43evU8Hvoyi9yu0=", "exportNames": ["*"]}}, {"name": "./updateHandlers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 289}, "end": {"line": 11, "column": 50, "index": 339}}], "key": "lArlpaD/5d+RB4jQyU++60NVCc0=", "exportNames": ["*"]}}, {"name": "./needsToReattach", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 340}, "end": {"line": 12, "column": 52, "index": 392}}], "key": "AnC4N1Crd90FP+3Mxk358neOkRo=", "exportNames": ["*"]}}, {"name": "./dropHandlers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 393}, "end": {"line": 13, "column": 46, "index": 439}}], "key": "3pg09hFbTrtcJ+KzQ97dAmmPlSE=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 440}, "end": {"line": 14, "column": 67, "index": 507}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}, {"name": "../../../findNodeHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 508}, "end": {"line": 15, "column": 53, "index": 561}}], "key": "k+xfarWxri7fB3IShKFMK0oi5UQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDetectorUpdater = useDetectorUpdater;\n  var _react = require(_dependencyMap[1], \"react\");\n  var _attachHandlers = require(_dependencyMap[2], \"./attachHandlers\");\n  var _updateHandlers = require(_dependencyMap[3], \"./updateHandlers\");\n  var _needsToReattach = require(_dependencyMap[4], \"./needsToReattach\");\n  var _dropHandlers = require(_dependencyMap[5], \"./dropHandlers\");\n  var _utils = require(_dependencyMap[6], \"./utils\");\n  var _findNodeHandle = _interopRequireDefault(require(_dependencyMap[7], \"../../../findNodeHandle\"));\n  // Returns a function that's responsible for updating the attached gestures\n  // If the view has changed, it will reattach the handlers to the new view\n  // If the view remains the same, it will update the handlers with the new config\n  function useDetectorUpdater(state, preparedGesture, gesturesToAttach, gestureConfig, webEventHandlersRef) {\n    var forceRender = (0, _utils.useForceRender)();\n    var updateAttachedGestures = (0, _react.useCallback)(\n    // skipConfigUpdate is used to prevent unnecessary updates when only checking if the view has changed\n    skipConfigUpdate => {\n      // If the underlying view has changed we need to reattach handlers to the new view\n      var viewTag = (0, _findNodeHandle.default)(state.viewRef);\n      var didUnderlyingViewChange = viewTag !== state.previousViewTag;\n      if (didUnderlyingViewChange || (0, _needsToReattach.needsToReattach)(preparedGesture, gesturesToAttach)) {\n        (0, _utils.validateDetectorChildren)(state.viewRef);\n        (0, _dropHandlers.dropHandlers)(preparedGesture);\n        (0, _attachHandlers.attachHandlers)({\n          preparedGesture,\n          gestureConfig,\n          gesturesToAttach,\n          webEventHandlersRef,\n          viewTag\n        });\n        if (didUnderlyingViewChange) {\n          state.previousViewTag = viewTag;\n          state.forceRebuildReanimatedEvent = true;\n          forceRender();\n        }\n      } else if (!skipConfigUpdate) {\n        (0, _updateHandlers.updateHandlers)(preparedGesture, gestureConfig, gesturesToAttach);\n      }\n    }, [forceRender, gestureConfig, gesturesToAttach, preparedGesture, state, webEventHandlersRef]);\n    return updateAttachedGestures;\n  }\n});", "lineCount": 46, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "require"], [7, 22, 1, 0], [7, 23, 1, 0, "_dependencyMap"], [7, 37, 1, 0], [8, 2, 10, 0], [8, 6, 10, 0, "_attachHandlers"], [8, 21, 10, 0], [8, 24, 10, 0, "require"], [8, 31, 10, 0], [8, 32, 10, 0, "_dependencyMap"], [8, 46, 10, 0], [9, 2, 11, 0], [9, 6, 11, 0, "_updateHandlers"], [9, 21, 11, 0], [9, 24, 11, 0, "require"], [9, 31, 11, 0], [9, 32, 11, 0, "_dependencyMap"], [9, 46, 11, 0], [10, 2, 12, 0], [10, 6, 12, 0, "_needsToReattach"], [10, 22, 12, 0], [10, 25, 12, 0, "require"], [10, 32, 12, 0], [10, 33, 12, 0, "_dependencyMap"], [10, 47, 12, 0], [11, 2, 13, 0], [11, 6, 13, 0, "_dropHandlers"], [11, 19, 13, 0], [11, 22, 13, 0, "require"], [11, 29, 13, 0], [11, 30, 13, 0, "_dependencyMap"], [11, 44, 13, 0], [12, 2, 14, 0], [12, 6, 14, 0, "_utils"], [12, 12, 14, 0], [12, 15, 14, 0, "require"], [12, 22, 14, 0], [12, 23, 14, 0, "_dependencyMap"], [12, 37, 14, 0], [13, 2, 15, 0], [13, 6, 15, 0, "_findNodeHandle"], [13, 21, 15, 0], [13, 24, 15, 0, "_interopRequireDefault"], [13, 46, 15, 0], [13, 47, 15, 0, "require"], [13, 54, 15, 0], [13, 55, 15, 0, "_dependencyMap"], [13, 69, 15, 0], [14, 2, 17, 0], [15, 2, 18, 0], [16, 2, 19, 0], [17, 2, 20, 7], [17, 11, 20, 16, "useDetectorUpdater"], [17, 29, 20, 34, "useDetectorUpdater"], [17, 30, 21, 2, "state"], [17, 35, 21, 29], [17, 37, 22, 2, "preparedGesture"], [17, 52, 22, 39], [17, 54, 23, 2, "gestures<PERSON>oAtta<PERSON>"], [17, 70, 23, 33], [17, 72, 24, 2, "gestureConfig"], [17, 85, 24, 46], [17, 87, 25, 2, "webEventHandlersRef"], [17, 106, 25, 55], [17, 108, 26, 2], [18, 4, 27, 2], [18, 8, 27, 8, "forceRender"], [18, 19, 27, 19], [18, 22, 27, 22], [18, 26, 27, 22, "useForceRender"], [18, 47, 27, 36], [18, 49, 27, 37], [18, 50, 27, 38], [19, 4, 28, 2], [19, 8, 28, 8, "updateAttachedGestures"], [19, 30, 28, 30], [19, 33, 28, 33], [19, 37, 28, 33, "useCallback"], [19, 55, 28, 44], [20, 4, 29, 4], [21, 4, 30, 5, "skipConfigUpdate"], [21, 20, 30, 31], [21, 24, 30, 36], [22, 6, 31, 6], [23, 6, 32, 6], [23, 10, 32, 12, "viewTag"], [23, 17, 32, 19], [23, 20, 32, 22], [23, 24, 32, 22, "findNodeHandle"], [23, 47, 32, 36], [23, 49, 32, 37, "state"], [23, 54, 32, 42], [23, 55, 32, 43, "viewRef"], [23, 62, 32, 50], [23, 63, 32, 61], [24, 6, 33, 6], [24, 10, 33, 12, "didUnderlyingViewChange"], [24, 33, 33, 35], [24, 36, 33, 38, "viewTag"], [24, 43, 33, 45], [24, 48, 33, 50, "state"], [24, 53, 33, 55], [24, 54, 33, 56, "previousViewTag"], [24, 69, 33, 71], [25, 6, 35, 6], [25, 10, 36, 8, "didUnderlyingViewChange"], [25, 33, 36, 31], [25, 37, 37, 8], [25, 41, 37, 8, "needsToReattach"], [25, 73, 37, 23], [25, 75, 37, 24, "preparedGesture"], [25, 90, 37, 39], [25, 92, 37, 41, "gestures<PERSON>oAtta<PERSON>"], [25, 108, 37, 57], [25, 109, 37, 58], [25, 111, 38, 8], [26, 8, 39, 8], [26, 12, 39, 8, "validateDetectorChildren"], [26, 43, 39, 32], [26, 45, 39, 33, "state"], [26, 50, 39, 38], [26, 51, 39, 39, "viewRef"], [26, 58, 39, 46], [26, 59, 39, 47], [27, 8, 40, 8], [27, 12, 40, 8, "dropHandlers"], [27, 38, 40, 20], [27, 40, 40, 21, "preparedGesture"], [27, 55, 40, 36], [27, 56, 40, 37], [28, 8, 41, 8], [28, 12, 41, 8, "attachHandlers"], [28, 42, 41, 22], [28, 44, 41, 23], [29, 10, 42, 10, "preparedGesture"], [29, 25, 42, 25], [30, 10, 43, 10, "gestureConfig"], [30, 23, 43, 23], [31, 10, 44, 10, "gestures<PERSON>oAtta<PERSON>"], [31, 26, 44, 26], [32, 10, 45, 10, "webEventHandlersRef"], [32, 29, 45, 29], [33, 10, 46, 10, "viewTag"], [34, 8, 47, 8], [34, 9, 47, 9], [34, 10, 47, 10], [35, 8, 49, 8], [35, 12, 49, 12, "didUnderlyingViewChange"], [35, 35, 49, 35], [35, 37, 49, 37], [36, 10, 50, 10, "state"], [36, 15, 50, 15], [36, 16, 50, 16, "previousViewTag"], [36, 31, 50, 31], [36, 34, 50, 34, "viewTag"], [36, 41, 50, 41], [37, 10, 51, 10, "state"], [37, 15, 51, 15], [37, 16, 51, 16, "forceRebuildReanimatedEvent"], [37, 43, 51, 43], [37, 46, 51, 46], [37, 50, 51, 50], [38, 10, 52, 10, "forceRender"], [38, 21, 52, 21], [38, 22, 52, 22], [38, 23, 52, 23], [39, 8, 53, 8], [40, 6, 54, 6], [40, 7, 54, 7], [40, 13, 54, 13], [40, 17, 54, 17], [40, 18, 54, 18, "skipConfigUpdate"], [40, 34, 54, 34], [40, 36, 54, 36], [41, 8, 55, 8], [41, 12, 55, 8, "updateHandlers"], [41, 42, 55, 22], [41, 44, 55, 23, "preparedGesture"], [41, 59, 55, 38], [41, 61, 55, 40, "gestureConfig"], [41, 74, 55, 53], [41, 76, 55, 55, "gestures<PERSON>oAtta<PERSON>"], [41, 92, 55, 71], [41, 93, 55, 72], [42, 6, 56, 6], [43, 4, 57, 4], [43, 5, 57, 5], [43, 7, 58, 4], [43, 8, 59, 6, "forceRender"], [43, 19, 59, 17], [43, 21, 60, 6, "gestureConfig"], [43, 34, 60, 19], [43, 36, 61, 6, "gestures<PERSON>oAtta<PERSON>"], [43, 52, 61, 22], [43, 54, 62, 6, "preparedGesture"], [43, 69, 62, 21], [43, 71, 63, 6, "state"], [43, 76, 63, 11], [43, 78, 64, 6, "webEventHandlersRef"], [43, 97, 64, 25], [43, 98, 66, 2], [43, 99, 66, 3], [44, 4, 68, 2], [44, 11, 68, 9, "updateAttachedGestures"], [44, 33, 68, 31], [45, 2, 69, 0], [46, 0, 69, 1], [46, 3]], "functionMap": {"names": ["<global>", "useDetectorUpdater", "updateAttachedGestures"], "mappings": "AAA;OCmB;ICU;KD2B;CDY"}}, "type": "js/module"}]}