{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  /**\n   * We hardcode the version of Reanimated here in order to compare it with the\n   * version used to build the native part of the library in runtime. Remember to\n   * keep this in sync with the version declared in `package.json`\n   */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.jsVersion = void 0;\n  var jsVersion = exports.jsVersion = '3.17.4';\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [5, 0, 3, 0], [6, 0, 4, 0], [7, 0, 5, 0], [8, 0, 6, 0], [9, 2, 2, 0, "Object"], [9, 8, 2, 0], [9, 9, 2, 0, "defineProperty"], [9, 23, 2, 0], [9, 24, 2, 0, "exports"], [9, 31, 2, 0], [10, 4, 2, 0, "value"], [10, 9, 2, 0], [11, 2, 2, 0], [12, 2, 2, 0, "exports"], [12, 9, 2, 0], [12, 10, 2, 0, "jsVersion"], [12, 19, 2, 0], [13, 2, 7, 7], [13, 6, 7, 13, "jsVersion"], [13, 15, 7, 22], [13, 18, 7, 22, "exports"], [13, 25, 7, 22], [13, 26, 7, 22, "jsVersion"], [13, 35, 7, 22], [13, 38, 7, 25], [13, 46, 7, 33], [14, 0, 7, 34], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}