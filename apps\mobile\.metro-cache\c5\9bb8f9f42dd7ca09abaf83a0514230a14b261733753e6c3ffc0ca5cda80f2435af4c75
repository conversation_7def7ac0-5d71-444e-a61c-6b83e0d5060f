{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  function encodeFilename(filename) {\n    return encodeURIComponent(filename.replace(/\\//g, '_'));\n  }\n  var FormData = /*#__PURE__*/function () {\n    function FormData() {\n      (0, _classCallCheck2.default)(this, FormData);\n      this._parts = [];\n    }\n    return (0, _createClass2.default)(FormData, [{\n      key: \"append\",\n      value: function append(key, value) {\n        this._parts.push([key, value]);\n      }\n    }, {\n      key: \"getAll\",\n      value: function getAll(key) {\n        return this._parts.filter(_ref => {\n          var _ref2 = (0, _slicedToArray2.default)(_ref, 1),\n            name = _ref2[0];\n          return name === key;\n        }).map(_ref3 => {\n          var _ref4 = (0, _slicedToArray2.default)(_ref3, 2),\n            value = _ref4[1];\n          return value;\n        });\n      }\n    }, {\n      key: \"getParts\",\n      value: function getParts() {\n        return this._parts.map(_ref5 => {\n          var _ref6 = (0, _slicedToArray2.default)(_ref5, 2),\n            name = _ref6[0],\n            value = _ref6[1];\n          var contentDisposition = 'form-data; name=\"' + name + '\"';\n          var headers = {\n            'content-disposition': contentDisposition\n          };\n          if (typeof value === 'object' && !Array.isArray(value) && value) {\n            if (typeof value.name === 'string') {\n              headers['content-disposition'] += `; filename=\"${encodeFilename(value.name)}\"`;\n            }\n            if (typeof value.type === 'string') {\n              headers['content-type'] = value.type;\n            }\n            return {\n              ...value,\n              headers,\n              fieldName: name\n            };\n          }\n          return {\n            string: String(value),\n            headers,\n            fieldName: name\n          };\n        });\n      }\n    }]);\n  }();\n  var _default = exports.default = FormData;\n});", "lineCount": 72, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_slicedToArray2"], [9, 21, 11, 13], [9, 24, 11, 13, "_interopRequireDefault"], [9, 46, 11, 13], [9, 47, 11, 13, "require"], [9, 54, 11, 13], [9, 55, 11, 13, "_dependencyMap"], [9, 69, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_classCallCheck2"], [10, 22, 11, 13], [10, 25, 11, 13, "_interopRequireDefault"], [10, 47, 11, 13], [10, 48, 11, 13, "require"], [10, 55, 11, 13], [10, 56, 11, 13, "_dependencyMap"], [10, 70, 11, 13], [11, 2, 11, 13], [11, 6, 11, 13, "_createClass2"], [11, 19, 11, 13], [11, 22, 11, 13, "_interopRequireDefault"], [11, 44, 11, 13], [11, 45, 11, 13, "require"], [11, 52, 11, 13], [11, 53, 11, 13, "_dependencyMap"], [11, 67, 11, 13], [12, 2, 36, 0], [12, 11, 36, 9, "encodeFilename"], [12, 25, 36, 23, "encodeFilename"], [12, 26, 36, 24, "filename"], [12, 34, 36, 40], [12, 36, 36, 50], [13, 4, 37, 2], [13, 11, 37, 9, "encodeURIComponent"], [13, 29, 37, 27], [13, 30, 37, 28, "filename"], [13, 38, 37, 36], [13, 39, 37, 37, "replace"], [13, 46, 37, 44], [13, 47, 37, 45], [13, 52, 37, 50], [13, 54, 37, 52], [13, 57, 37, 55], [13, 58, 37, 56], [13, 59, 37, 57], [14, 2, 38, 0], [15, 2, 38, 1], [15, 6, 60, 6, "FormData"], [15, 14, 60, 14], [16, 4, 63, 2], [16, 13, 63, 2, "FormData"], [16, 22, 63, 2], [16, 24, 63, 16], [17, 6, 63, 16], [17, 10, 63, 16, "_classCallCheck2"], [17, 26, 63, 16], [17, 27, 63, 16, "default"], [17, 34, 63, 16], [17, 42, 63, 16, "FormData"], [17, 50, 63, 16], [18, 6, 64, 4], [18, 10, 64, 8], [18, 11, 64, 9, "_parts"], [18, 17, 64, 15], [18, 20, 64, 18], [18, 22, 64, 20], [19, 4, 65, 2], [20, 4, 65, 3], [20, 15, 65, 3, "_createClass2"], [20, 28, 65, 3], [20, 29, 65, 3, "default"], [20, 36, 65, 3], [20, 38, 65, 3, "FormData"], [20, 46, 65, 3], [21, 6, 65, 3, "key"], [21, 9, 65, 3], [22, 6, 65, 3, "value"], [22, 11, 65, 3], [22, 13, 67, 2], [22, 22, 67, 2, "append"], [22, 28, 67, 8, "append"], [22, 29, 67, 9, "key"], [22, 32, 67, 20], [22, 34, 67, 22, "value"], [22, 39, 67, 42], [22, 41, 67, 44], [23, 8, 73, 4], [23, 12, 73, 8], [23, 13, 73, 9, "_parts"], [23, 19, 73, 15], [23, 20, 73, 16, "push"], [23, 24, 73, 20], [23, 25, 73, 21], [23, 26, 73, 22, "key"], [23, 29, 73, 25], [23, 31, 73, 27, "value"], [23, 36, 73, 32], [23, 37, 73, 33], [23, 38, 73, 34], [24, 6, 74, 2], [25, 4, 74, 3], [26, 6, 74, 3, "key"], [26, 9, 74, 3], [27, 6, 74, 3, "value"], [27, 11, 74, 3], [27, 13, 76, 2], [27, 22, 76, 2, "getAll"], [27, 28, 76, 8, "getAll"], [27, 29, 76, 9, "key"], [27, 32, 76, 20], [27, 34, 76, 44], [28, 8, 77, 4], [28, 15, 77, 11], [28, 19, 77, 15], [28, 20, 77, 16, "_parts"], [28, 26, 77, 22], [28, 27, 78, 7, "filter"], [28, 33, 78, 13], [28, 34, 78, 14, "_ref"], [28, 38, 78, 14], [29, 10, 78, 14], [29, 14, 78, 14, "_ref2"], [29, 19, 78, 14], [29, 26, 78, 14, "_slicedToArray2"], [29, 41, 78, 14], [29, 42, 78, 14, "default"], [29, 49, 78, 14], [29, 51, 78, 14, "_ref"], [29, 55, 78, 14], [30, 12, 78, 16, "name"], [30, 16, 78, 20], [30, 19, 78, 20, "_ref2"], [30, 24, 78, 20], [31, 10, 78, 20], [31, 17, 78, 26, "name"], [31, 21, 78, 30], [31, 26, 78, 35, "key"], [31, 29, 78, 38], [32, 8, 78, 38], [32, 10, 78, 39], [32, 11, 79, 7, "map"], [32, 14, 79, 10], [32, 15, 79, 11, "_ref3"], [32, 20, 79, 11], [33, 10, 79, 11], [33, 14, 79, 11, "_ref4"], [33, 19, 79, 11], [33, 26, 79, 11, "_slicedToArray2"], [33, 41, 79, 11], [33, 42, 79, 11, "default"], [33, 49, 79, 11], [33, 51, 79, 11, "_ref3"], [33, 56, 79, 11], [34, 12, 79, 15, "value"], [34, 17, 79, 20], [34, 20, 79, 20, "_ref4"], [34, 25, 79, 20], [35, 10, 79, 20], [35, 17, 79, 26, "value"], [35, 22, 79, 31], [36, 8, 79, 31], [36, 10, 79, 32], [37, 6, 80, 2], [38, 4, 80, 3], [39, 6, 80, 3, "key"], [39, 9, 80, 3], [40, 6, 80, 3, "value"], [40, 11, 80, 3], [40, 13, 82, 2], [40, 22, 82, 2, "getParts"], [40, 30, 82, 10, "getParts"], [40, 31, 82, 10], [40, 33, 82, 34], [41, 8, 83, 4], [41, 15, 83, 11], [41, 19, 83, 15], [41, 20, 83, 16, "_parts"], [41, 26, 83, 22], [41, 27, 83, 23, "map"], [41, 30, 83, 26], [41, 31, 83, 27, "_ref5"], [41, 36, 83, 27], [41, 40, 83, 46], [42, 10, 83, 46], [42, 14, 83, 46, "_ref6"], [42, 19, 83, 46], [42, 26, 83, 46, "_slicedToArray2"], [42, 41, 83, 46], [42, 42, 83, 46, "default"], [42, 49, 83, 46], [42, 51, 83, 46, "_ref5"], [42, 56, 83, 46], [43, 12, 83, 29, "name"], [43, 16, 83, 33], [43, 19, 83, 33, "_ref6"], [43, 24, 83, 33], [44, 12, 83, 35, "value"], [44, 17, 83, 40], [44, 20, 83, 40, "_ref6"], [44, 25, 83, 40], [45, 10, 84, 6], [45, 14, 84, 12, "contentDisposition"], [45, 32, 84, 30], [45, 35, 84, 33], [45, 54, 84, 52], [45, 57, 84, 55, "name"], [45, 61, 84, 59], [45, 64, 84, 62], [45, 67, 84, 65], [46, 10, 86, 6], [46, 14, 86, 12, "headers"], [46, 21, 86, 28], [46, 24, 86, 31], [47, 12, 86, 32], [47, 33, 86, 53], [47, 35, 86, 55, "contentDisposition"], [48, 10, 86, 73], [48, 11, 86, 74], [49, 10, 92, 6], [49, 14, 92, 10], [49, 21, 92, 17, "value"], [49, 26, 92, 22], [49, 31, 92, 27], [49, 39, 92, 35], [49, 43, 92, 39], [49, 44, 92, 40, "Array"], [49, 49, 92, 45], [49, 50, 92, 46, "isArray"], [49, 57, 92, 53], [49, 58, 92, 54, "value"], [49, 63, 92, 59], [49, 64, 92, 60], [49, 68, 92, 64, "value"], [49, 73, 92, 69], [49, 75, 92, 71], [50, 12, 93, 8], [50, 16, 93, 12], [50, 23, 93, 19, "value"], [50, 28, 93, 24], [50, 29, 93, 25, "name"], [50, 33, 93, 29], [50, 38, 93, 34], [50, 46, 93, 42], [50, 48, 93, 44], [51, 14, 94, 10, "headers"], [51, 21, 94, 17], [51, 22, 94, 18], [51, 43, 94, 39], [51, 44, 94, 40], [51, 48, 95, 12], [51, 63, 95, 27, "encodeFilename"], [51, 77, 95, 41], [51, 78, 95, 42, "value"], [51, 83, 95, 47], [51, 84, 95, 48, "name"], [51, 88, 95, 52], [51, 89, 95, 53], [51, 92, 95, 56], [52, 12, 96, 8], [53, 12, 97, 8], [53, 16, 97, 12], [53, 23, 97, 19, "value"], [53, 28, 97, 24], [53, 29, 97, 25, "type"], [53, 33, 97, 29], [53, 38, 97, 34], [53, 46, 97, 42], [53, 48, 97, 44], [54, 14, 98, 10, "headers"], [54, 21, 98, 17], [54, 22, 98, 18], [54, 36, 98, 32], [54, 37, 98, 33], [54, 40, 98, 36, "value"], [54, 45, 98, 41], [54, 46, 98, 42, "type"], [54, 50, 98, 46], [55, 12, 99, 8], [56, 12, 100, 8], [56, 19, 100, 15], [57, 14, 100, 16], [57, 17, 100, 19, "value"], [57, 22, 100, 24], [58, 14, 100, 26, "headers"], [58, 21, 100, 33], [59, 14, 100, 35, "fieldName"], [59, 23, 100, 44], [59, 25, 100, 46, "name"], [60, 12, 100, 50], [60, 13, 100, 51], [61, 10, 101, 6], [62, 10, 103, 6], [62, 17, 103, 13], [63, 12, 103, 14, "string"], [63, 18, 103, 20], [63, 20, 103, 22, "String"], [63, 26, 103, 28], [63, 27, 103, 29, "value"], [63, 32, 103, 34], [63, 33, 103, 35], [64, 12, 103, 37, "headers"], [64, 19, 103, 44], [65, 12, 103, 46, "fieldName"], [65, 21, 103, 55], [65, 23, 103, 57, "name"], [66, 10, 103, 61], [66, 11, 103, 62], [67, 8, 104, 4], [67, 9, 104, 5], [67, 10, 104, 6], [68, 6, 105, 2], [69, 4, 105, 3], [70, 2, 105, 3], [71, 2, 105, 3], [71, 6, 105, 3, "_default"], [71, 14, 105, 3], [71, 17, 105, 3, "exports"], [71, 24, 105, 3], [71, 25, 105, 3, "default"], [71, 32, 105, 3], [71, 35, 108, 15, "FormData"], [71, 43, 108, 23], [72, 0, 108, 23], [72, 3]], "functionMap": {"names": ["<global>", "encodeFilename", "FormData", "constructor", "append", "getAll", "_parts.filter$argument_0", "_parts.filter.map$argument_0", "getParts", "_parts.map$argument_0"], "mappings": "AAA;ACmC;CDE;AEsB;ECG;GDE;EEE;GFO;EGE;cCE,wBD;WEC,oBF;GHC;EME;2BCC;KDqB;GNC;CFC"}}, "type": "js/module"}]}