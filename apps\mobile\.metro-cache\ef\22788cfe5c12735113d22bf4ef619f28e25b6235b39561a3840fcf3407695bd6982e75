{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./Font", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 45}, "end": {"line": 2, "column": 45, "index": 90}}], "key": "uhjJvb2CC+i2amHkDH4+UF8lIHQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useFonts = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = require(_dependencyMap[2], \"react\");\n  var _Font = require(_dependencyMap[3], \"./Font\");\n  function isMapLoaded(map) {\n    if (typeof map === 'string') {\n      return (0, _Font.isLoaded)(map);\n    } else {\n      return Object.keys(map).every(fontFamily => (0, _Font.isLoaded)(fontFamily));\n    }\n  }\n  function useRuntimeFonts(map) {\n    var _useState = (0, _react.useState)(\n      // For web rehydration, we need to check if the fonts are already loaded during the static render.\n      // Native will also benefit from this optimization.\n      isMapLoaded(map)),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      loaded = _useState2[0],\n      setLoaded = _useState2[1];\n    var _useState3 = (0, _react.useState)(null),\n      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),\n      error = _useState4[0],\n      setError = _useState4[1];\n    (0, _react.useEffect)(() => {\n      var isMounted = true;\n      (0, _Font.loadAsync)(map).then(() => {\n        if (isMounted) {\n          setLoaded(true);\n        }\n      }).catch(error => {\n        if (isMounted) {\n          setError(error);\n        }\n      });\n      return () => {\n        isMounted = false;\n      };\n    }, []);\n    return [loaded, error];\n  }\n  function useStaticFonts(map) {\n    (0, _Font.loadAsync)(map);\n    return [true, null];\n  }\n  // @needsAudit\n  /**\n   * Load a map of fonts with [`loadAsync`](#loadasyncfontfamilyorfontmap-source). This returns a `boolean` if the fonts are\n   * loaded and ready to use. It also returns an error if something went wrong, to use in development.\n   *\n   * > Note, the fonts are not \"reloaded\" when you dynamically change the font map.\n   *\n   * @param map A map of `fontFamily`s to [`FontSource`](#fontsource)s. After loading the font you can\n   * use the key in the `fontFamily` style prop of a `Text` element.\n   *\n   * @return\n   * - __loaded__ (`boolean`) - A boolean to detect if the font for `fontFamily` has finished\n   * loading.\n   * - __error__ (`Error | null`) - An error encountered when loading the fonts.\n   *\n   * @example\n   * ```tsx\n   * const [loaded, error] = useFonts({ ... });\n   * ```\n   */\n  var useFonts = exports.useFonts = typeof window === 'undefined' ? useStaticFonts : useRuntimeFonts;\n});", "lineCount": 71, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "require"], [8, 22, 1, 0], [8, 23, 1, 0, "_dependencyMap"], [8, 37, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_Font"], [9, 11, 2, 0], [9, 14, 2, 0, "require"], [9, 21, 2, 0], [9, 22, 2, 0, "_dependencyMap"], [9, 36, 2, 0], [10, 2, 3, 0], [10, 11, 3, 9, "isMapLoaded"], [10, 22, 3, 20, "isMapLoaded"], [10, 23, 3, 21, "map"], [10, 26, 3, 24], [10, 28, 3, 26], [11, 4, 4, 4], [11, 8, 4, 8], [11, 15, 4, 15, "map"], [11, 18, 4, 18], [11, 23, 4, 23], [11, 31, 4, 31], [11, 33, 4, 33], [12, 6, 5, 8], [12, 13, 5, 15], [12, 17, 5, 15, "isLoaded"], [12, 31, 5, 23], [12, 33, 5, 24, "map"], [12, 36, 5, 27], [12, 37, 5, 28], [13, 4, 6, 4], [13, 5, 6, 5], [13, 11, 7, 9], [14, 6, 8, 8], [14, 13, 8, 15, "Object"], [14, 19, 8, 21], [14, 20, 8, 22, "keys"], [14, 24, 8, 26], [14, 25, 8, 27, "map"], [14, 28, 8, 30], [14, 29, 8, 31], [14, 30, 8, 32, "every"], [14, 35, 8, 37], [14, 36, 8, 39, "fontFamily"], [14, 46, 8, 49], [14, 50, 8, 54], [14, 54, 8, 54, "isLoaded"], [14, 68, 8, 62], [14, 70, 8, 63, "fontFamily"], [14, 80, 8, 73], [14, 81, 8, 74], [14, 82, 8, 75], [15, 4, 9, 4], [16, 2, 10, 0], [17, 2, 11, 0], [17, 11, 11, 9, "useRuntimeFonts"], [17, 26, 11, 24, "useRuntimeFonts"], [17, 27, 11, 25, "map"], [17, 30, 11, 28], [17, 32, 11, 30], [18, 4, 12, 4], [18, 8, 12, 4, "_useState"], [18, 17, 12, 4], [18, 20, 12, 32], [18, 24, 12, 32, "useState"], [18, 39, 12, 40], [19, 6, 13, 4], [20, 6, 14, 4], [21, 6, 15, 4, "isMapLoaded"], [21, 17, 15, 15], [21, 18, 15, 16, "map"], [21, 21, 15, 19], [21, 22, 15, 20], [21, 23, 15, 21], [22, 6, 15, 21, "_useState2"], [22, 16, 15, 21], [22, 23, 15, 21, "_slicedToArray2"], [22, 38, 15, 21], [22, 39, 15, 21, "default"], [22, 46, 15, 21], [22, 48, 15, 21, "_useState"], [22, 57, 15, 21], [23, 6, 12, 11, "loaded"], [23, 12, 12, 17], [23, 15, 12, 17, "_useState2"], [23, 25, 12, 17], [24, 6, 12, 19, "setLoaded"], [24, 15, 12, 28], [24, 18, 12, 28, "_useState2"], [24, 28, 12, 28], [25, 4, 16, 4], [25, 8, 16, 4, "_useState3"], [25, 18, 16, 4], [25, 21, 16, 30], [25, 25, 16, 30, "useState"], [25, 40, 16, 38], [25, 42, 16, 39], [25, 46, 16, 43], [25, 47, 16, 44], [26, 6, 16, 44, "_useState4"], [26, 16, 16, 44], [26, 23, 16, 44, "_slicedToArray2"], [26, 38, 16, 44], [26, 39, 16, 44, "default"], [26, 46, 16, 44], [26, 48, 16, 44, "_useState3"], [26, 58, 16, 44], [27, 6, 16, 11, "error"], [27, 11, 16, 16], [27, 14, 16, 16, "_useState4"], [27, 24, 16, 16], [28, 6, 16, 18, "setError"], [28, 14, 16, 26], [28, 17, 16, 26, "_useState4"], [28, 27, 16, 26], [29, 4, 17, 4], [29, 8, 17, 4, "useEffect"], [29, 24, 17, 13], [29, 26, 17, 14], [29, 32, 17, 20], [30, 6, 18, 8], [30, 10, 18, 12, "isMounted"], [30, 19, 18, 21], [30, 22, 18, 24], [30, 26, 18, 28], [31, 6, 19, 8], [31, 10, 19, 8, "loadAsync"], [31, 25, 19, 17], [31, 27, 19, 18, "map"], [31, 30, 19, 21], [31, 31, 19, 22], [31, 32, 20, 13, "then"], [31, 36, 20, 17], [31, 37, 20, 18], [31, 43, 20, 24], [32, 8, 21, 12], [32, 12, 21, 16, "isMounted"], [32, 21, 21, 25], [32, 23, 21, 27], [33, 10, 22, 16, "setLoaded"], [33, 19, 22, 25], [33, 20, 22, 26], [33, 24, 22, 30], [33, 25, 22, 31], [34, 8, 23, 12], [35, 6, 24, 8], [35, 7, 24, 9], [35, 8, 24, 10], [35, 9, 25, 13, "catch"], [35, 14, 25, 18], [35, 15, 25, 20, "error"], [35, 20, 25, 25], [35, 24, 25, 30], [36, 8, 26, 12], [36, 12, 26, 16, "isMounted"], [36, 21, 26, 25], [36, 23, 26, 27], [37, 10, 27, 16, "setError"], [37, 18, 27, 24], [37, 19, 27, 25, "error"], [37, 24, 27, 30], [37, 25, 27, 31], [38, 8, 28, 12], [39, 6, 29, 8], [39, 7, 29, 9], [39, 8, 29, 10], [40, 6, 30, 8], [40, 13, 30, 15], [40, 19, 30, 21], [41, 8, 31, 12, "isMounted"], [41, 17, 31, 21], [41, 20, 31, 24], [41, 25, 31, 29], [42, 6, 32, 8], [42, 7, 32, 9], [43, 4, 33, 4], [43, 5, 33, 5], [43, 7, 33, 7], [43, 9, 33, 9], [43, 10, 33, 10], [44, 4, 34, 4], [44, 11, 34, 11], [44, 12, 34, 12, "loaded"], [44, 18, 34, 18], [44, 20, 34, 20, "error"], [44, 25, 34, 25], [44, 26, 34, 26], [45, 2, 35, 0], [46, 2, 36, 0], [46, 11, 36, 9, "useStaticFonts"], [46, 25, 36, 23, "useStaticFonts"], [46, 26, 36, 24, "map"], [46, 29, 36, 27], [46, 31, 36, 29], [47, 4, 37, 4], [47, 8, 37, 4, "loadAsync"], [47, 23, 37, 13], [47, 25, 37, 14, "map"], [47, 28, 37, 17], [47, 29, 37, 18], [48, 4, 38, 4], [48, 11, 38, 11], [48, 12, 38, 12], [48, 16, 38, 16], [48, 18, 38, 18], [48, 22, 38, 22], [48, 23, 38, 23], [49, 2, 39, 0], [50, 2, 40, 0], [51, 2, 41, 0], [52, 0, 42, 0], [53, 0, 43, 0], [54, 0, 44, 0], [55, 0, 45, 0], [56, 0, 46, 0], [57, 0, 47, 0], [58, 0, 48, 0], [59, 0, 49, 0], [60, 0, 50, 0], [61, 0, 51, 0], [62, 0, 52, 0], [63, 0, 53, 0], [64, 0, 54, 0], [65, 0, 55, 0], [66, 0, 56, 0], [67, 0, 57, 0], [68, 0, 58, 0], [69, 0, 59, 0], [70, 2, 60, 7], [70, 6, 60, 13, "useFonts"], [70, 14, 60, 21], [70, 17, 60, 21, "exports"], [70, 24, 60, 21], [70, 25, 60, 21, "useFonts"], [70, 33, 60, 21], [70, 36, 60, 24], [70, 43, 60, 31, "window"], [70, 49, 60, 37], [70, 54, 60, 42], [70, 65, 60, 53], [70, 68, 60, 56, "useStaticFonts"], [70, 82, 60, 70], [70, 85, 60, 73, "useRuntimeFonts"], [70, 100, 60, 88], [71, 0, 60, 89], [71, 3]], "functionMap": {"names": ["<global>", "isMapLoaded", "Object.keys.every$argument_0", "useRuntimeFonts", "useEffect$argument_0", "loadAsync.then$argument_0", "loadAsync.then._catch$argument_0", "<anonymous>", "useStaticFonts"], "mappings": "AAA;ACE;sCCK,oCD;CDE;AGC;cCM;kBCG;SDI;mBEC;SFI;eGC;SHE;KDC;CHE;AQC;CRG"}}, "type": "js/module"}]}