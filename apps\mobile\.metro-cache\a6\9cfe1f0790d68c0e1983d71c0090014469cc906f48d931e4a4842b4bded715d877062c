{"dependencies": [{"name": "./EventInternals", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 46}}], "key": "osWM0nHj/rKMvL8LiAcgeSxbpjI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.INTERNAL_DISPATCH_METHOD_KEY = exports.EVENT_TARGET_GET_THE_PARENT_KEY = void 0;\n  exports.dispatchTrustedEvent = dispatchTrustedEvent;\n  var _EventInternals = require(_dependencyMap[0], \"./EventInternals\");\n  var EVENT_TARGET_GET_THE_PARENT_KEY = exports.EVENT_TARGET_GET_THE_PARENT_KEY = Symbol('EventTarget[get the parent]');\n  var INTERNAL_DISPATCH_METHOD_KEY = exports.INTERNAL_DISPATCH_METHOD_KEY = Symbol('EventTarget[dispatch]');\n  function dispatchTrustedEvent(eventTarget, event) {\n    (0, _EventInternals.setIsTrusted)(event, true);\n    return eventTarget[INTERNAL_DISPATCH_METHOD_KEY](event);\n  }\n});", "lineCount": 14, "map": [[7, 2, 20, 0], [7, 6, 20, 0, "_EventInternals"], [7, 21, 20, 0], [7, 24, 20, 0, "require"], [7, 31, 20, 0], [7, 32, 20, 0, "_dependencyMap"], [7, 46, 20, 0], [8, 2, 26, 7], [8, 6, 26, 13, "EVENT_TARGET_GET_THE_PARENT_KEY"], [8, 37, 26, 52], [8, 40, 26, 52, "exports"], [8, 47, 26, 52], [8, 48, 26, 52, "EVENT_TARGET_GET_THE_PARENT_KEY"], [8, 79, 26, 52], [8, 82, 26, 55, "Symbol"], [8, 88, 26, 61], [8, 89, 27, 2], [8, 118, 28, 0], [8, 119, 28, 1], [9, 2, 34, 7], [9, 6, 34, 13, "INTERNAL_DISPATCH_METHOD_KEY"], [9, 34, 34, 49], [9, 37, 34, 49, "exports"], [9, 44, 34, 49], [9, 45, 34, 49, "INTERNAL_DISPATCH_METHOD_KEY"], [9, 73, 34, 49], [9, 76, 34, 52, "Symbol"], [9, 82, 34, 58], [9, 83, 35, 2], [9, 106, 36, 0], [9, 107, 36, 1], [10, 2, 44, 7], [10, 11, 44, 16, "dispatchTrustedEvent"], [10, 31, 44, 36, "dispatchTrustedEvent"], [10, 32, 45, 2, "eventTarget"], [10, 43, 45, 26], [10, 45, 46, 2, "event"], [10, 50, 46, 14], [10, 52, 47, 8], [11, 4, 48, 2], [11, 8, 48, 2, "setIsTrusted"], [11, 36, 48, 14], [11, 38, 48, 15, "event"], [11, 43, 48, 20], [11, 45, 48, 22], [11, 49, 48, 26], [11, 50, 48, 27], [12, 4, 51, 2], [12, 11, 51, 9, "eventTarget"], [12, 22, 51, 20], [12, 23, 51, 21, "INTERNAL_DISPATCH_METHOD_KEY"], [12, 51, 51, 49], [12, 52, 51, 50], [12, 53, 51, 51, "event"], [12, 58, 51, 56], [12, 59, 51, 57], [13, 2, 52, 0], [14, 0, 52, 1], [14, 3]], "functionMap": {"names": ["<global>", "dispatchTrustedEvent"], "mappings": "AAA;OC2C"}}, "type": "js/module"}]}