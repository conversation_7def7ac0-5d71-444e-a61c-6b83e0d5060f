{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.unstable_hasComponent = unstable_hasComponent;\n  var componentNameToExists = new Map();\n  function unstable_hasComponent(name) {\n    var hasNativeComponent = componentNameToExists.get(name);\n    if (hasNativeComponent == null) {\n      if (global.__nativeComponentRegistry__hasComponent) {\n        hasNativeComponent = global.__nativeComponentRegistry__hasComponent(name);\n        componentNameToExists.set(name, hasNativeComponent);\n      } else {\n        throw `unstable_hasComponent('${name}'): Global function is not registered`;\n      }\n    }\n    return hasNativeComponent;\n  }\n});", "lineCount": 19, "map": [[6, 2, 11, 0], [6, 6, 11, 4, "componentNameToExists"], [6, 27, 11, 47], [6, 30, 11, 50], [6, 34, 11, 54, "Map"], [6, 37, 11, 57], [6, 38, 11, 58], [6, 39, 11, 59], [7, 2, 19, 7], [7, 11, 19, 16, "unstable_hasComponent"], [7, 32, 19, 37, "unstable_hasComponent"], [7, 33, 19, 38, "name"], [7, 37, 19, 50], [7, 39, 19, 61], [8, 4, 20, 2], [8, 8, 20, 6, "hasNativeComponent"], [8, 26, 20, 24], [8, 29, 20, 27, "componentNameToExists"], [8, 50, 20, 48], [8, 51, 20, 49, "get"], [8, 54, 20, 52], [8, 55, 20, 53, "name"], [8, 59, 20, 57], [8, 60, 20, 58], [9, 4, 21, 2], [9, 8, 21, 6, "hasNativeComponent"], [9, 26, 21, 24], [9, 30, 21, 28], [9, 34, 21, 32], [9, 36, 21, 34], [10, 6, 22, 4], [10, 10, 22, 8, "global"], [10, 16, 22, 14], [10, 17, 22, 15, "__nativeComponentRegistry__hasComponent"], [10, 56, 22, 54], [10, 58, 22, 56], [11, 8, 23, 6, "hasNativeComponent"], [11, 26, 23, 24], [11, 29, 23, 27, "global"], [11, 35, 23, 33], [11, 36, 23, 34, "__nativeComponentRegistry__hasComponent"], [11, 75, 23, 73], [11, 76, 23, 74, "name"], [11, 80, 23, 78], [11, 81, 23, 79], [12, 8, 24, 6, "componentNameToExists"], [12, 29, 24, 27], [12, 30, 24, 28, "set"], [12, 33, 24, 31], [12, 34, 24, 32, "name"], [12, 38, 24, 36], [12, 40, 24, 38, "hasNativeComponent"], [12, 58, 24, 56], [12, 59, 24, 57], [13, 6, 25, 4], [13, 7, 25, 5], [13, 13, 25, 11], [14, 8, 26, 6], [14, 14, 26, 12], [14, 40, 26, 38, "name"], [14, 44, 26, 42], [14, 83, 26, 81], [15, 6, 27, 4], [16, 4, 28, 2], [17, 4, 29, 2], [17, 11, 29, 9, "hasNativeComponent"], [17, 29, 29, 27], [18, 2, 30, 0], [19, 0, 30, 1], [19, 3]], "functionMap": {"names": ["<global>", "unstable_hasComponent"], "mappings": "AAA;OCkB"}}, "type": "js/module"}]}