{"dependencies": [{"name": "../gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 61, "index": 61}}], "key": "tAi+3DTq2/XlM2rT1I9X7ektuV0=", "exportNames": ["*"]}}, {"name": "../reanimated<PERSON><PERSON>per", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 62}, "end": {"line": 2, "column": 50, "index": 112}}], "key": "55E8XkeO+dzLnJh3bfoVrgRwD58=", "exportNames": ["*"]}}, {"name": "../gestureStateManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 229}, "end": {"line": 11, "column": 32, "index": 320}}], "key": "6LCevhjgWrTPA6v411SOrqBFOt8=", "exportNames": ["*"]}}, {"name": "../../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 321}, "end": {"line": 12, "column": 39, "index": 360}}], "key": "IejmxDhiCXPiMeZAbmULeVBR7VE=", "exportNames": ["*"]}}, {"name": "../../../TouchEventType", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 361}, "end": {"line": 13, "column": 57, "index": 418}}], "key": "PyG3b4ZwjXzi6z4zhvmBkUTmp1M=", "exportNames": ["*"]}}, {"name": "../../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 419}, "end": {"line": 14, "column": 44, "index": 463}}], "key": "4wo4OYT4MSo2InL8kiWmZxvepwE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedGesture = useAnimatedGesture;\n  var _gesture = require(_dependencyMap[0], \"../gesture\");\n  var _reanimatedWrapper = require(_dependencyMap[1], \"../reanimatedWrapper\");\n  var _gestureStateManager = require(_dependencyMap[2], \"../gestureStateManager\");\n  var _State = require(_dependencyMap[3], \"../../../State\");\n  var _TouchEventType = require(_dependencyMap[4], \"../../../TouchEventType\");\n  var _utils = require(_dependencyMap[5], \"../../../utils\");\n  var _worklet_14672499464410_init_data = {\n    code: \"function getHandler_useAnimatedGestureTs1(type,gesture){const{CALLBACK_TYPE}=this.__closure;switch(type){case CALLBACK_TYPE.BEGAN:return gesture.onBegin;case CALLBACK_TYPE.START:return gesture.onStart;case CALLBACK_TYPE.UPDATE:return gesture.onUpdate;case CALLBACK_TYPE.CHANGE:return gesture.onChange;case CALLBACK_TYPE.END:return gesture.onEnd;case CALLBACK_TYPE.FINALIZE:return gesture.onFinalize;case CALLBACK_TYPE.TOUCHES_DOWN:return gesture.onTouchesDown;case CALLBACK_TYPE.TOUCHES_MOVE:return gesture.onTouchesMove;case CALLBACK_TYPE.TOUCHES_UP:return gesture.onTouchesUp;case CALLBACK_TYPE.TOUCHES_CANCELLED:return gesture.onTouchesCancelled;}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\GestureDetector\\\\useAnimatedGesture.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getHandler_useAnimatedGestureTs1\\\",\\\"type\\\",\\\"gesture\\\",\\\"CALLBACK_TYPE\\\",\\\"__closure\\\",\\\"BEGAN\\\",\\\"onBegin\\\",\\\"START\\\",\\\"onStart\\\",\\\"UPDATE\\\",\\\"onUpdate\\\",\\\"CHANGE\\\",\\\"onChange\\\",\\\"END\\\",\\\"onEnd\\\",\\\"FINALIZE\\\",\\\"onFinalize\\\",\\\"TOUCHES_DOWN\\\",\\\"onTouchesDown\\\",\\\"TOUCHES_MOVE\\\",\\\"onTouchesMove\\\",\\\"TOUCHES_UP\\\",\\\"onTouchesUp\\\",\\\"TOUCHES_CANCELLED\\\",\\\"onTouchesCancelled\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/useAnimatedGesture.ts\\\"],\\\"mappings\\\":\\\"AAgBA,SAAAA,gCAGEA,CAAAC,IAAA,CAAAC,OAAA,QAAAC,aAAA,OAAAC,SAAA,CAEA,OAAQH,IAAI,EACV,IAAK,CAAAE,aAAa,CAACE,KAAK,CACtB,MAAO,CAAAH,OAAO,CAACI,OAAO,CACxB,IAAK,CAAAH,aAAa,CAACI,KAAK,CACtB,MAAO,CAAAL,OAAO,CAACM,OAAO,CACxB,IAAK,CAAAL,aAAa,CAACM,MAAM,CACvB,MAAO,CAAAP,OAAO,CAACQ,QAAQ,CACzB,IAAK,CAAAP,aAAa,CAACQ,MAAM,CACvB,MAAO,CAAAT,OAAO,CAACU,QAAQ,CACzB,IAAK,CAAAT,aAAa,CAACU,GAAG,CACpB,MAAO,CAAAX,OAAO,CAACY,KAAK,CACtB,IAAK,CAAAX,aAAa,CAACY,QAAQ,CACzB,MAAO,CAAAb,OAAO,CAACc,UAAU,CAC3B,IAAK,CAAAb,aAAa,CAACc,YAAY,CAC7B,MAAO,CAAAf,OAAO,CAACgB,aAAa,CAC9B,IAAK,CAAAf,aAAa,CAACgB,YAAY,CAC7B,MAAO,CAAAjB,OAAO,CAACkB,aAAa,CAC9B,IAAK,CAAAjB,aAAa,CAACkB,UAAU,CAC3B,MAAO,CAAAnB,OAAO,CAACoB,WAAW,CAC5B,IAAK,CAAAnB,aAAa,CAACoB,iBAAiB,CAClC,MAAO,CAAArB,OAAO,CAACsB,kBAAkB,CACrC,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var getHandler = function () {\n    var _e = [new global.Error(), -2, -27];\n    var getHandler = function (type, gesture) {\n      switch (type) {\n        case _gesture.CALLBACK_TYPE.BEGAN:\n          return gesture.onBegin;\n        case _gesture.CALLBACK_TYPE.START:\n          return gesture.onStart;\n        case _gesture.CALLBACK_TYPE.UPDATE:\n          return gesture.onUpdate;\n        case _gesture.CALLBACK_TYPE.CHANGE:\n          return gesture.onChange;\n        case _gesture.CALLBACK_TYPE.END:\n          return gesture.onEnd;\n        case _gesture.CALLBACK_TYPE.FINALIZE:\n          return gesture.onFinalize;\n        case _gesture.CALLBACK_TYPE.TOUCHES_DOWN:\n          return gesture.onTouchesDown;\n        case _gesture.CALLBACK_TYPE.TOUCHES_MOVE:\n          return gesture.onTouchesMove;\n        case _gesture.CALLBACK_TYPE.TOUCHES_UP:\n          return gesture.onTouchesUp;\n        case _gesture.CALLBACK_TYPE.TOUCHES_CANCELLED:\n          return gesture.onTouchesCancelled;\n      }\n    };\n    getHandler.__closure = {\n      CALLBACK_TYPE: _gesture.CALLBACK_TYPE\n    };\n    getHandler.__workletHash = 14672499464410;\n    getHandler.__initData = _worklet_14672499464410_init_data;\n    getHandler.__stackDetails = _e;\n    return getHandler;\n  }();\n  var _worklet_1836477036505_init_data = {\n    code: \"function touchEventTypeToCallbackType_useAnimatedGestureTs2(eventType){const{TouchEventType,CALLBACK_TYPE}=this.__closure;switch(eventType){case TouchEventType.TOUCHES_DOWN:return CALLBACK_TYPE.TOUCHES_DOWN;case TouchEventType.TOUCHES_MOVE:return CALLBACK_TYPE.TOUCHES_MOVE;case TouchEventType.TOUCHES_UP:return CALLBACK_TYPE.TOUCHES_UP;case TouchEventType.TOUCHES_CANCELLED:return CALLBACK_TYPE.TOUCHES_CANCELLED;}return CALLBACK_TYPE.UNDEFINED;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\GestureDetector\\\\useAnimatedGesture.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"touchEventTypeToCallbackType_useAnimatedGestureTs2\\\",\\\"eventType\\\",\\\"TouchEventType\\\",\\\"CALLBACK_TYPE\\\",\\\"__closure\\\",\\\"TOUCHES_DOWN\\\",\\\"TOUCHES_MOVE\\\",\\\"TOUCHES_UP\\\",\\\"TOUCHES_CANCELLED\\\",\\\"UNDEFINED\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/useAnimatedGesture.ts\\\"],\\\"mappings\\\":\\\"AA6CA,SAAAA,kDAEiBA,CAAAC,SAAA,QAAAC,cAAA,CAAAC,aAAA,OAAAC,SAAA,CAEf,OAAQH,SAAS,EACf,IAAK,CAAAC,cAAc,CAACG,YAAY,CAC9B,MAAO,CAAAF,aAAa,CAACE,YAAY,CACnC,IAAK,CAAAH,cAAc,CAACI,YAAY,CAC9B,MAAO,CAAAH,aAAa,CAACG,YAAY,CACnC,IAAK,CAAAJ,cAAc,CAACK,UAAU,CAC5B,MAAO,CAAAJ,aAAa,CAACI,UAAU,CACjC,IAAK,CAAAL,cAAc,CAACM,iBAAiB,CACnC,MAAO,CAAAL,aAAa,CAACK,iBAAiB,CAC1C,CACA,MAAO,CAAAL,aAAa,CAACM,SAAS,CAChC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var touchEventTypeToCallbackType = function () {\n    var _e = [new global.Error(), -3, -27];\n    var touchEventTypeToCallbackType = function (eventType) {\n      switch (eventType) {\n        case _TouchEventType.TouchEventType.TOUCHES_DOWN:\n          return _gesture.CALLBACK_TYPE.TOUCHES_DOWN;\n        case _TouchEventType.TouchEventType.TOUCHES_MOVE:\n          return _gesture.CALLBACK_TYPE.TOUCHES_MOVE;\n        case _TouchEventType.TouchEventType.TOUCHES_UP:\n          return _gesture.CALLBACK_TYPE.TOUCHES_UP;\n        case _TouchEventType.TouchEventType.TOUCHES_CANCELLED:\n          return _gesture.CALLBACK_TYPE.TOUCHES_CANCELLED;\n      }\n      return _gesture.CALLBACK_TYPE.UNDEFINED;\n    };\n    touchEventTypeToCallbackType.__closure = {\n      TouchEventType: _TouchEventType.TouchEventType,\n      CALLBACK_TYPE: _gesture.CALLBACK_TYPE\n    };\n    touchEventTypeToCallbackType.__workletHash = 1836477036505;\n    touchEventTypeToCallbackType.__initData = _worklet_1836477036505_init_data;\n    touchEventTypeToCallbackType.__stackDetails = _e;\n    return touchEventTypeToCallbackType;\n  }();\n  var _worklet_4006900144310_init_data = {\n    code: \"function runWorklet_useAnimatedGestureTs3(type,gesture,event,...args){const{getHandler,tagMessage}=this.__closure;const handler=getHandler(type,gesture);if(gesture.isWorklet[type]){handler===null||handler===void 0||handler(event,...args);}else if(handler){console.warn(tagMessage('Animated gesture callback must be a worklet'));}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\GestureDetector\\\\useAnimatedGesture.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"runWorklet_useAnimatedGestureTs3\\\",\\\"type\\\",\\\"gesture\\\",\\\"event\\\",\\\"args\\\",\\\"getHandler\\\",\\\"tagMessage\\\",\\\"__closure\\\",\\\"handler\\\",\\\"isWorklet\\\",\\\"console\\\",\\\"warn\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/useAnimatedGesture.ts\\\"],\\\"mappings\\\":\\\"AA8DA,SAAAA,gCAGyEA,CAAAC,IACpE,CAAAC,OACH,CAAAC,KAAA,IAAAC,IAAA,QAAAC,UAAA,CAAAC,UAAA,OAAAC,SAAA,CAEA,KAAM,CAAAC,OAAO,CAAGH,UAAU,CAACJ,IAAI,CAAEC,OAAO,CAAC,CACzC,GAAIA,OAAO,CAACO,SAAS,CAACR,IAAI,CAAC,CAAE,CAG3BO,OAAO,SAAPA,OAAO,WAAPA,OAAO,CAAGL,KAAK,CAAE,GAAGC,IAAI,CAAC,CAC3B,CAAC,IAAM,IAAII,OAAO,CAAE,CAClBE,OAAO,CAACC,IAAI,CAACL,UAAU,CAAC,6CAA6C,CAAC,CAAC,CACzE,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var runWorklet = function () {\n    var _e = [new global.Error(), -3, -27];\n    var runWorklet = function (type, gesture, event) {\n      var handler = getHandler(type, gesture);\n      if (gesture.isWorklet[type]) {\n        for (var _len = arguments.length, args = new Array(_len > 3 ? _len - 3 : 0), _key = 3; _key < _len; _key++) {\n          args[_key - 3] = arguments[_key];\n        }\n        // @ts-ignore Logic below makes sure the correct event is send to the\n        // correct handler.\n        handler?.(event, ...args);\n      } else if (handler) {\n        console.warn((0, _utils.tagMessage)('Animated gesture callback must be a worklet'));\n      }\n    };\n    runWorklet.__closure = {\n      getHandler,\n      tagMessage: _utils.tagMessage\n    };\n    runWorklet.__workletHash = 4006900144310;\n    runWorklet.__initData = _worklet_4006900144310_init_data;\n    runWorklet.__stackDetails = _e;\n    return runWorklet;\n  }();\n  var _worklet_16926571502586_init_data = {\n    code: \"function isStateChangeEvent_useAnimatedGestureTs4(event){return event.oldState!=null;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\GestureDetector\\\\useAnimatedGesture.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"isStateChangeEvent_useAnimatedGestureTs4\\\",\\\"event\\\",\\\"oldState\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/useAnimatedGesture.ts\\\"],\\\"mappings\\\":\\\"AA+EA,SAAAA,wCAEoCA,CAAAC,KAAA,EAGlC,MAAO,CAAAA,KAAK,CAACC,QAAQ,EAAI,IAAI,CAC/B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var isStateChangeEvent = function () {\n    var _e = [new global.Error(), 1, -27];\n    var isStateChangeEvent = function (event) {\n      // @ts-ignore Yes, the oldState prop is missing on GestureTouchEvent, that's the point\n      return event.oldState != null;\n    };\n    isStateChangeEvent.__closure = {};\n    isStateChangeEvent.__workletHash = 16926571502586;\n    isStateChangeEvent.__initData = _worklet_16926571502586_init_data;\n    isStateChangeEvent.__stackDetails = _e;\n    return isStateChangeEvent;\n  }();\n  var _worklet_2062872232491_init_data = {\n    code: \"function isTouchEvent_useAnimatedGestureTs5(event){return event.eventType!=null;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\GestureDetector\\\\useAnimatedGesture.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"isTouchEvent_useAnimatedGestureTs5\\\",\\\"event\\\",\\\"eventType\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/useAnimatedGesture.ts\\\"],\\\"mappings\\\":\\\"AAuFA,SAAAA,kCAE8BA,CAAAC,KAAA,EAE5B,MAAO,CAAAA,KAAK,CAACC,SAAS,EAAI,IAAI,CAChC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var isTouchEvent = function () {\n    var _e = [new global.Error(), 1, -27];\n    var isTouchEvent = function (event) {\n      return event.eventType != null;\n    };\n    isTouchEvent.__closure = {};\n    isTouchEvent.__workletHash = 2062872232491;\n    isTouchEvent.__initData = _worklet_2062872232491_init_data;\n    isTouchEvent.__stackDetails = _e;\n    return isTouchEvent;\n  }();\n  var _worklet_10845617855334_init_data = {\n    code: \"function useAnimatedGestureTs6(event){const{sharedHandlersCallbacks,isStateChangeEvent,State,runWorklet,CALLBACK_TYPE,lastUpdateEvent,isTouchEvent,stateControllers,GestureStateManager,TouchEventType,touchEventTypeToCallbackType}=this.__closure;const currentCallback=sharedHandlersCallbacks.value;if(!currentCallback){return;}for(let i=0;i<currentCallback.length;i++){const gesture=currentCallback[i];if(event.handlerTag!==gesture.handlerTag){continue;}if(isStateChangeEvent(event)){if(event.oldState===State.UNDETERMINED&&event.state===State.BEGAN){runWorklet(CALLBACK_TYPE.BEGAN,gesture,event);}else if((event.oldState===State.BEGAN||event.oldState===State.UNDETERMINED)&&event.state===State.ACTIVE){runWorklet(CALLBACK_TYPE.START,gesture,event);lastUpdateEvent.value[gesture.handlerTag]=undefined;}else if(event.oldState!==event.state&&event.state===State.END){if(event.oldState===State.ACTIVE){runWorklet(CALLBACK_TYPE.END,gesture,event,true);}runWorklet(CALLBACK_TYPE.FINALIZE,gesture,event,true);}else if((event.state===State.FAILED||event.state===State.CANCELLED)&&event.state!==event.oldState){if(event.oldState===State.ACTIVE){runWorklet(CALLBACK_TYPE.END,gesture,event,false);}runWorklet(CALLBACK_TYPE.FINALIZE,gesture,event,false);}}else if(isTouchEvent(event)){if(!stateControllers[i]){stateControllers[i]=GestureStateManager.create(event.handlerTag);}if(event.eventType!==TouchEventType.UNDETERMINED){runWorklet(touchEventTypeToCallbackType(event.eventType),gesture,event,stateControllers[i]);}}else{runWorklet(CALLBACK_TYPE.UPDATE,gesture,event);if(gesture.onChange&&gesture.changeEventCalculator){var _gesture$changeEventC;runWorklet(CALLBACK_TYPE.CHANGE,gesture,(_gesture$changeEventC=gesture.changeEventCalculator)===null||_gesture$changeEventC===void 0?void 0:_gesture$changeEventC.call(gesture,event,lastUpdateEvent.value[gesture.handlerTag]));lastUpdateEvent.value[gesture.handlerTag]=event;}}}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\GestureDetector\\\\useAnimatedGesture.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useAnimatedGestureTs6\\\",\\\"event\\\",\\\"sharedHandlersCallbacks\\\",\\\"isStateChangeEvent\\\",\\\"State\\\",\\\"runWorklet\\\",\\\"CALLBACK_TYPE\\\",\\\"lastUpdateEvent\\\",\\\"isTouchEvent\\\",\\\"stateControllers\\\",\\\"GestureStateManager\\\",\\\"TouchEventType\\\",\\\"touchEventTypeToCallbackType\\\",\\\"__closure\\\",\\\"currentCallback\\\",\\\"value\\\",\\\"i\\\",\\\"length\\\",\\\"gesture\\\",\\\"handlerTag\\\",\\\"oldState\\\",\\\"UNDETERMINED\\\",\\\"state\\\",\\\"BEGAN\\\",\\\"ACTIVE\\\",\\\"START\\\",\\\"undefined\\\",\\\"END\\\",\\\"FINALIZE\\\",\\\"FAILED\\\",\\\"CANCELLED\\\",\\\"create\\\",\\\"eventType\\\",\\\"UPDATE\\\",\\\"onChange\\\",\\\"changeEventCalculator\\\",\\\"_gesture$changeEventC\\\",\\\"CHANGE\\\",\\\"call\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/useAnimatedGesture.ts\\\"],\\\"mappings\\\":\\\"AAqHmB,QACf,CAAAA,qBACGA,CAAAC,KAAA,QAAAC,uBAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAC,UAAA,CAAAC,aAAA,CAAAC,eAAA,CAAAC,YAAA,CAAAC,gBAAA,CAAAC,mBAAA,CAAAC,cAAA,CAAAC,4BAAA,OAAAC,SAAA,CAGH,KAAM,CAAAC,eAAe,CAAGZ,uBAAuB,CAACa,KAAK,CACrD,GAAI,CAACD,eAAe,CAAE,CACpB,OACF,CAEA,IAAK,GAAI,CAAAE,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGF,eAAe,CAACG,MAAM,CAAED,CAAC,EAAE,CAAE,CAC/C,KAAM,CAAAE,OAAO,CAAGJ,eAAe,CAACE,CAAC,CAAC,CAElC,GAAIf,KAAK,CAACkB,UAAU,GAAKD,OAAO,CAACC,UAAU,CAAE,CAC3C,SACF,CAEA,GAAIhB,kBAAkB,CAACF,KAAK,CAAC,CAAE,CAC7B,GACEA,KAAK,CAACmB,QAAQ,GAAKhB,KAAK,CAACiB,YAAY,EACrCpB,KAAK,CAACqB,KAAK,GAAKlB,KAAK,CAACmB,KAAK,CAC3B,CACAlB,UAAU,CAACC,aAAa,CAACiB,KAAK,CAAEL,OAAO,CAAEjB,KAAK,CAAC,CACjD,CAAC,IAAM,IACL,CAACA,KAAK,CAACmB,QAAQ,GAAKhB,KAAK,CAACmB,KAAK,EAC7BtB,KAAK,CAACmB,QAAQ,GAAKhB,KAAK,CAACiB,YAAY,GACvCpB,KAAK,CAACqB,KAAK,GAAKlB,KAAK,CAACoB,MAAM,CAC5B,CACAnB,UAAU,CAACC,aAAa,CAACmB,KAAK,CAAEP,OAAO,CAAEjB,KAAK,CAAC,CAC/CM,eAAe,CAACQ,KAAK,CAACG,OAAO,CAACC,UAAU,CAAC,CAAGO,SAAS,CACvD,CAAC,IAAM,IACLzB,KAAK,CAACmB,QAAQ,GAAKnB,KAAK,CAACqB,KAAK,EAC9BrB,KAAK,CAACqB,KAAK,GAAKlB,KAAK,CAACuB,GAAG,CACzB,CACA,GAAI1B,KAAK,CAACmB,QAAQ,GAAKhB,KAAK,CAACoB,MAAM,CAAE,CACnCnB,UAAU,CAACC,aAAa,CAACqB,GAAG,CAAET,OAAO,CAAEjB,KAAK,CAAE,IAAI,CAAC,CACrD,CACAI,UAAU,CAACC,aAAa,CAACsB,QAAQ,CAAEV,OAAO,CAAEjB,KAAK,CAAE,IAAI,CAAC,CAC1D,CAAC,IAAM,IACL,CAACA,KAAK,CAACqB,KAAK,GAAKlB,KAAK,CAACyB,MAAM,EAAI5B,KAAK,CAACqB,KAAK,GAAKlB,KAAK,CAAC0B,SAAS,GAChE7B,KAAK,CAACqB,KAAK,GAAKrB,KAAK,CAACmB,QAAQ,CAC9B,CACA,GAAInB,KAAK,CAACmB,QAAQ,GAAKhB,KAAK,CAACoB,MAAM,CAAE,CACnCnB,UAAU,CAACC,aAAa,CAACqB,GAAG,CAAET,OAAO,CAAEjB,KAAK,CAAE,KAAK,CAAC,CACtD,CACAI,UAAU,CAACC,aAAa,CAACsB,QAAQ,CAAEV,OAAO,CAAEjB,KAAK,CAAE,KAAK,CAAC,CAC3D,CACF,CAAC,IAAM,IAAIO,YAAY,CAACP,KAAK,CAAC,CAAE,CAC9B,GAAI,CAACQ,gBAAgB,CAACO,CAAC,CAAC,CAAE,CACxBP,gBAAgB,CAACO,CAAC,CAAC,CAAGN,mBAAmB,CAACqB,MAAM,CAAC9B,KAAK,CAACkB,UAAU,CAAC,CACpE,CAEA,GAAIlB,KAAK,CAAC+B,SAAS,GAAKrB,cAAc,CAACU,YAAY,CAAE,CACnDhB,UAAU,CACRO,4BAA4B,CAACX,KAAK,CAAC+B,SAAS,CAAC,CAC7Cd,OAAO,CACPjB,KAAK,CACLQ,gBAAgB,CAACO,CAAC,CACpB,CAAC,CACH,CACF,CAAC,IAAM,CACLX,UAAU,CAACC,aAAa,CAAC2B,MAAM,CAAEf,OAAO,CAAEjB,KAAK,CAAC,CAEhD,GAAIiB,OAAO,CAACgB,QAAQ,EAAIhB,OAAO,CAACiB,qBAAqB,CAAE,KAAAC,qBAAA,CACrD/B,UAAU,CACRC,aAAa,CAAC+B,MAAM,CACpBnB,OAAO,EAAAkB,qBAAA,CACPlB,OAAO,CAACiB,qBAAqB,UAAAC,qBAAA,iBAA7BA,qBAAA,CAAAE,IAAA,CAAApB,OAAO,CACLjB,KAAK,CACLM,eAAe,CAACQ,KAAK,CAACG,OAAO,CAACC,UAAU,CAC1C,CACF,CAAC,CAEDZ,eAAe,CAACQ,KAAK,CAACG,OAAO,CAACC,UAAU,CAAC,CAAGlB,KAAK,CACnD,CACF,CACF,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function useAnimatedGesture(preparedGesture, needsRebuild) {\n    if (!_reanimatedWrapper.Reanimated) {\n      return;\n    }\n\n    // Hooks are called conditionally, but the condition is whether the\n    // react-native-reanimated is installed, which shouldn't change while running\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    var sharedHandlersCallbacks = _reanimatedWrapper.Reanimated.useSharedValue(null);\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    var lastUpdateEvent = _reanimatedWrapper.Reanimated.useSharedValue([]);\n\n    // not every gesture needs a state controller, init them lazily\n    var stateControllers = [];\n    var callback = function () {\n      var _e = [new global.Error(), -12, -27];\n      var useAnimatedGestureTs6 = function (event) {\n        var currentCallback = sharedHandlersCallbacks.value;\n        if (!currentCallback) {\n          return;\n        }\n        for (var i = 0; i < currentCallback.length; i++) {\n          var gesture = currentCallback[i];\n          if (event.handlerTag !== gesture.handlerTag) {\n            continue;\n          }\n          if (isStateChangeEvent(event)) {\n            if (event.oldState === _State.State.UNDETERMINED && event.state === _State.State.BEGAN) {\n              runWorklet(_gesture.CALLBACK_TYPE.BEGAN, gesture, event);\n            } else if ((event.oldState === _State.State.BEGAN || event.oldState === _State.State.UNDETERMINED) && event.state === _State.State.ACTIVE) {\n              runWorklet(_gesture.CALLBACK_TYPE.START, gesture, event);\n              lastUpdateEvent.value[gesture.handlerTag] = undefined;\n            } else if (event.oldState !== event.state && event.state === _State.State.END) {\n              if (event.oldState === _State.State.ACTIVE) {\n                runWorklet(_gesture.CALLBACK_TYPE.END, gesture, event, true);\n              }\n              runWorklet(_gesture.CALLBACK_TYPE.FINALIZE, gesture, event, true);\n            } else if ((event.state === _State.State.FAILED || event.state === _State.State.CANCELLED) && event.state !== event.oldState) {\n              if (event.oldState === _State.State.ACTIVE) {\n                runWorklet(_gesture.CALLBACK_TYPE.END, gesture, event, false);\n              }\n              runWorklet(_gesture.CALLBACK_TYPE.FINALIZE, gesture, event, false);\n            }\n          } else if (isTouchEvent(event)) {\n            if (!stateControllers[i]) {\n              stateControllers[i] = _gestureStateManager.GestureStateManager.create(event.handlerTag);\n            }\n            if (event.eventType !== _TouchEventType.TouchEventType.UNDETERMINED) {\n              runWorklet(touchEventTypeToCallbackType(event.eventType), gesture, event, stateControllers[i]);\n            }\n          } else {\n            runWorklet(_gesture.CALLBACK_TYPE.UPDATE, gesture, event);\n            if (gesture.onChange && gesture.changeEventCalculator) {\n              runWorklet(_gesture.CALLBACK_TYPE.CHANGE, gesture, gesture.changeEventCalculator?.(event, lastUpdateEvent.value[gesture.handlerTag]));\n              lastUpdateEvent.value[gesture.handlerTag] = event;\n            }\n          }\n        }\n      };\n      useAnimatedGestureTs6.__closure = {\n        sharedHandlersCallbacks,\n        isStateChangeEvent,\n        State: _State.State,\n        runWorklet,\n        CALLBACK_TYPE: _gesture.CALLBACK_TYPE,\n        lastUpdateEvent,\n        isTouchEvent,\n        stateControllers,\n        GestureStateManager: _gestureStateManager.GestureStateManager,\n        TouchEventType: _TouchEventType.TouchEventType,\n        touchEventTypeToCallbackType\n      };\n      useAnimatedGestureTs6.__workletHash = 10845617855334;\n      useAnimatedGestureTs6.__initData = _worklet_10845617855334_init_data;\n      useAnimatedGestureTs6.__stackDetails = _e;\n      return useAnimatedGestureTs6;\n    }();\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    var event = _reanimatedWrapper.Reanimated.useEvent(callback, ['onGestureHandlerStateChange', 'onGestureHandlerEvent'], needsRebuild);\n    preparedGesture.animatedEventHandler = event;\n    preparedGesture.animatedHandlers = sharedHandlersCallbacks;\n  }\n});", "lineCount": 237, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_gesture"], [6, 14, 1, 0], [6, 17, 1, 0, "require"], [6, 24, 1, 0], [6, 25, 1, 0, "_dependencyMap"], [6, 39, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_reanimated<PERSON><PERSON>per"], [7, 24, 2, 0], [7, 27, 2, 0, "require"], [7, 34, 2, 0], [7, 35, 2, 0, "_dependencyMap"], [7, 49, 2, 0], [8, 2, 8, 0], [8, 6, 8, 0, "_gestureStateManager"], [8, 26, 8, 0], [8, 29, 8, 0, "require"], [8, 36, 8, 0], [8, 37, 8, 0, "_dependencyMap"], [8, 51, 8, 0], [9, 2, 12, 0], [9, 6, 12, 0, "_State"], [9, 12, 12, 0], [9, 15, 12, 0, "require"], [9, 22, 12, 0], [9, 23, 12, 0, "_dependencyMap"], [9, 37, 12, 0], [10, 2, 13, 0], [10, 6, 13, 0, "_TouchEventType"], [10, 21, 13, 0], [10, 24, 13, 0, "require"], [10, 31, 13, 0], [10, 32, 13, 0, "_dependencyMap"], [10, 46, 13, 0], [11, 2, 14, 0], [11, 6, 14, 0, "_utils"], [11, 12, 14, 0], [11, 15, 14, 0, "require"], [11, 22, 14, 0], [11, 23, 14, 0, "_dependencyMap"], [11, 37, 14, 0], [12, 2, 14, 44], [12, 6, 14, 44, "_worklet_14672499464410_init_data"], [12, 39, 14, 44], [13, 4, 14, 44, "code"], [13, 8, 14, 44], [14, 4, 14, 44, "location"], [14, 12, 14, 44], [15, 4, 14, 44, "sourceMap"], [15, 13, 14, 44], [16, 4, 14, 44, "version"], [16, 11, 14, 44], [17, 2, 14, 44], [18, 2, 14, 44], [18, 6, 14, 44, "<PERSON><PERSON><PERSON><PERSON>"], [18, 16, 14, 44], [18, 19, 17, 0], [19, 4, 17, 0], [19, 8, 17, 0, "_e"], [19, 10, 17, 0], [19, 18, 17, 0, "global"], [19, 24, 17, 0], [19, 25, 17, 0, "Error"], [19, 30, 17, 0], [20, 4, 17, 0], [20, 8, 17, 0, "<PERSON><PERSON><PERSON><PERSON>"], [20, 18, 17, 0], [20, 30, 17, 0, "<PERSON><PERSON><PERSON><PERSON>"], [20, 31, 18, 2, "type"], [20, 35, 18, 21], [20, 37, 19, 2, "gesture"], [20, 44, 19, 52], [20, 46, 20, 2], [21, 6, 22, 2], [21, 14, 22, 10, "type"], [21, 18, 22, 14], [22, 8, 23, 4], [22, 13, 23, 9, "CALLBACK_TYPE"], [22, 35, 23, 22], [22, 36, 23, 23, "BEGAN"], [22, 41, 23, 28], [23, 10, 24, 6], [23, 17, 24, 13, "gesture"], [23, 24, 24, 20], [23, 25, 24, 21, "onBegin"], [23, 32, 24, 28], [24, 8, 25, 4], [24, 13, 25, 9, "CALLBACK_TYPE"], [24, 35, 25, 22], [24, 36, 25, 23, "START"], [24, 41, 25, 28], [25, 10, 26, 6], [25, 17, 26, 13, "gesture"], [25, 24, 26, 20], [25, 25, 26, 21, "onStart"], [25, 32, 26, 28], [26, 8, 27, 4], [26, 13, 27, 9, "CALLBACK_TYPE"], [26, 35, 27, 22], [26, 36, 27, 23, "UPDATE"], [26, 42, 27, 29], [27, 10, 28, 6], [27, 17, 28, 13, "gesture"], [27, 24, 28, 20], [27, 25, 28, 21, "onUpdate"], [27, 33, 28, 29], [28, 8, 29, 4], [28, 13, 29, 9, "CALLBACK_TYPE"], [28, 35, 29, 22], [28, 36, 29, 23, "CHANGE"], [28, 42, 29, 29], [29, 10, 30, 6], [29, 17, 30, 13, "gesture"], [29, 24, 30, 20], [29, 25, 30, 21, "onChange"], [29, 33, 30, 29], [30, 8, 31, 4], [30, 13, 31, 9, "CALLBACK_TYPE"], [30, 35, 31, 22], [30, 36, 31, 23, "END"], [30, 39, 31, 26], [31, 10, 32, 6], [31, 17, 32, 13, "gesture"], [31, 24, 32, 20], [31, 25, 32, 21, "onEnd"], [31, 30, 32, 26], [32, 8, 33, 4], [32, 13, 33, 9, "CALLBACK_TYPE"], [32, 35, 33, 22], [32, 36, 33, 23, "FINALIZE"], [32, 44, 33, 31], [33, 10, 34, 6], [33, 17, 34, 13, "gesture"], [33, 24, 34, 20], [33, 25, 34, 21, "onFinalize"], [33, 35, 34, 31], [34, 8, 35, 4], [34, 13, 35, 9, "CALLBACK_TYPE"], [34, 35, 35, 22], [34, 36, 35, 23, "TOUCHES_DOWN"], [34, 48, 35, 35], [35, 10, 36, 6], [35, 17, 36, 13, "gesture"], [35, 24, 36, 20], [35, 25, 36, 21, "onTouchesDown"], [35, 38, 36, 34], [36, 8, 37, 4], [36, 13, 37, 9, "CALLBACK_TYPE"], [36, 35, 37, 22], [36, 36, 37, 23, "TOUCHES_MOVE"], [36, 48, 37, 35], [37, 10, 38, 6], [37, 17, 38, 13, "gesture"], [37, 24, 38, 20], [37, 25, 38, 21, "onTouchesMove"], [37, 38, 38, 34], [38, 8, 39, 4], [38, 13, 39, 9, "CALLBACK_TYPE"], [38, 35, 39, 22], [38, 36, 39, 23, "TOUCHES_UP"], [38, 46, 39, 33], [39, 10, 40, 6], [39, 17, 40, 13, "gesture"], [39, 24, 40, 20], [39, 25, 40, 21, "onTouchesUp"], [39, 36, 40, 32], [40, 8, 41, 4], [40, 13, 41, 9, "CALLBACK_TYPE"], [40, 35, 41, 22], [40, 36, 41, 23, "TOUCHES_CANCELLED"], [40, 53, 41, 40], [41, 10, 42, 6], [41, 17, 42, 13, "gesture"], [41, 24, 42, 20], [41, 25, 42, 21, "onTouchesCancelled"], [41, 43, 42, 39], [42, 6, 43, 2], [43, 4, 44, 0], [43, 5, 44, 1], [44, 4, 44, 1, "<PERSON><PERSON><PERSON><PERSON>"], [44, 14, 44, 1], [44, 15, 44, 1, "__closure"], [44, 24, 44, 1], [45, 6, 44, 1, "CALLBACK_TYPE"], [45, 19, 44, 1], [45, 21, 18, 8, "CALLBACK_TYPE"], [46, 4, 18, 21], [47, 4, 18, 21, "<PERSON><PERSON><PERSON><PERSON>"], [47, 14, 18, 21], [47, 15, 18, 21, "__workletHash"], [47, 28, 18, 21], [48, 4, 18, 21, "<PERSON><PERSON><PERSON><PERSON>"], [48, 14, 18, 21], [48, 15, 18, 21, "__initData"], [48, 25, 18, 21], [48, 28, 18, 21, "_worklet_14672499464410_init_data"], [48, 61, 18, 21], [49, 4, 18, 21, "<PERSON><PERSON><PERSON><PERSON>"], [49, 14, 18, 21], [49, 15, 18, 21, "__stackDetails"], [49, 29, 18, 21], [49, 32, 18, 21, "_e"], [49, 34, 18, 21], [50, 4, 18, 21], [50, 11, 18, 21, "<PERSON><PERSON><PERSON><PERSON>"], [50, 21, 18, 21], [51, 2, 18, 21], [51, 3, 17, 0], [52, 2, 17, 0], [52, 6, 17, 0, "_worklet_1836477036505_init_data"], [52, 38, 17, 0], [53, 4, 17, 0, "code"], [53, 8, 17, 0], [54, 4, 17, 0, "location"], [54, 12, 17, 0], [55, 4, 17, 0, "sourceMap"], [55, 13, 17, 0], [56, 4, 17, 0, "version"], [56, 11, 17, 0], [57, 2, 17, 0], [58, 2, 17, 0], [58, 6, 17, 0, "touchEventTypeToCallbackType"], [58, 34, 17, 0], [58, 37, 46, 0], [59, 4, 46, 0], [59, 8, 46, 0, "_e"], [59, 10, 46, 0], [59, 18, 46, 0, "global"], [59, 24, 46, 0], [59, 25, 46, 0, "Error"], [59, 30, 46, 0], [60, 4, 46, 0], [60, 8, 46, 0, "touchEventTypeToCallbackType"], [60, 36, 46, 0], [60, 48, 46, 0, "touchEventTypeToCallbackType"], [60, 49, 47, 2, "eventType"], [60, 58, 47, 27], [60, 60, 48, 17], [61, 6, 50, 2], [61, 14, 50, 10, "eventType"], [61, 23, 50, 19], [62, 8, 51, 4], [62, 13, 51, 9, "TouchEventType"], [62, 43, 51, 23], [62, 44, 51, 24, "TOUCHES_DOWN"], [62, 56, 51, 36], [63, 10, 52, 6], [63, 17, 52, 13, "CALLBACK_TYPE"], [63, 39, 52, 26], [63, 40, 52, 27, "TOUCHES_DOWN"], [63, 52, 52, 39], [64, 8, 53, 4], [64, 13, 53, 9, "TouchEventType"], [64, 43, 53, 23], [64, 44, 53, 24, "TOUCHES_MOVE"], [64, 56, 53, 36], [65, 10, 54, 6], [65, 17, 54, 13, "CALLBACK_TYPE"], [65, 39, 54, 26], [65, 40, 54, 27, "TOUCHES_MOVE"], [65, 52, 54, 39], [66, 8, 55, 4], [66, 13, 55, 9, "TouchEventType"], [66, 43, 55, 23], [66, 44, 55, 24, "TOUCHES_UP"], [66, 54, 55, 34], [67, 10, 56, 6], [67, 17, 56, 13, "CALLBACK_TYPE"], [67, 39, 56, 26], [67, 40, 56, 27, "TOUCHES_UP"], [67, 50, 56, 37], [68, 8, 57, 4], [68, 13, 57, 9, "TouchEventType"], [68, 43, 57, 23], [68, 44, 57, 24, "TOUCHES_CANCELLED"], [68, 61, 57, 41], [69, 10, 58, 6], [69, 17, 58, 13, "CALLBACK_TYPE"], [69, 39, 58, 26], [69, 40, 58, 27, "TOUCHES_CANCELLED"], [69, 57, 58, 44], [70, 6, 59, 2], [71, 6, 60, 2], [71, 13, 60, 9, "CALLBACK_TYPE"], [71, 35, 60, 22], [71, 36, 60, 23, "UNDEFINED"], [71, 45, 60, 32], [72, 4, 61, 0], [72, 5, 61, 1], [73, 4, 61, 1, "touchEventTypeToCallbackType"], [73, 32, 61, 1], [73, 33, 61, 1, "__closure"], [73, 42, 61, 1], [74, 6, 61, 1, "TouchEventType"], [74, 20, 61, 1], [74, 22, 47, 13, "TouchEventType"], [74, 52, 47, 27], [75, 6, 47, 27, "CALLBACK_TYPE"], [75, 19, 47, 27], [75, 21, 48, 3, "CALLBACK_TYPE"], [76, 4, 48, 16], [77, 4, 48, 16, "touchEventTypeToCallbackType"], [77, 32, 48, 16], [77, 33, 48, 16, "__workletHash"], [77, 46, 48, 16], [78, 4, 48, 16, "touchEventTypeToCallbackType"], [78, 32, 48, 16], [78, 33, 48, 16, "__initData"], [78, 43, 48, 16], [78, 46, 48, 16, "_worklet_1836477036505_init_data"], [78, 78, 48, 16], [79, 4, 48, 16, "touchEventTypeToCallbackType"], [79, 32, 48, 16], [79, 33, 48, 16, "__stackDetails"], [79, 47, 48, 16], [79, 50, 48, 16, "_e"], [79, 52, 48, 16], [80, 4, 48, 16], [80, 11, 48, 16, "touchEventTypeToCallbackType"], [80, 39, 48, 16], [81, 2, 48, 16], [81, 3, 46, 0], [82, 2, 46, 0], [82, 6, 46, 0, "_worklet_4006900144310_init_data"], [82, 38, 46, 0], [83, 4, 46, 0, "code"], [83, 8, 46, 0], [84, 4, 46, 0, "location"], [84, 12, 46, 0], [85, 4, 46, 0, "sourceMap"], [85, 13, 46, 0], [86, 4, 46, 0, "version"], [86, 11, 46, 0], [87, 2, 46, 0], [88, 2, 46, 0], [88, 6, 46, 0, "runWorklet"], [88, 16, 46, 0], [88, 19, 63, 0], [89, 4, 63, 0], [89, 8, 63, 0, "_e"], [89, 10, 63, 0], [89, 18, 63, 0, "global"], [89, 24, 63, 0], [89, 25, 63, 0, "Error"], [89, 30, 63, 0], [90, 4, 63, 0], [90, 8, 63, 0, "runWorklet"], [90, 18, 63, 0], [90, 30, 63, 0, "runWorklet"], [90, 31, 64, 2, "type"], [90, 35, 64, 21], [90, 37, 65, 2, "gesture"], [90, 44, 65, 52], [90, 46, 66, 2, "event"], [90, 51, 66, 73], [90, 53, 68, 2], [91, 6, 70, 2], [91, 10, 70, 8, "handler"], [91, 17, 70, 15], [91, 20, 70, 18, "<PERSON><PERSON><PERSON><PERSON>"], [91, 30, 70, 28], [91, 31, 70, 29, "type"], [91, 35, 70, 33], [91, 37, 70, 35, "gesture"], [91, 44, 70, 42], [91, 45, 70, 43], [92, 6, 71, 2], [92, 10, 71, 6, "gesture"], [92, 17, 71, 13], [92, 18, 71, 14, "isWorklet"], [92, 27, 71, 23], [92, 28, 71, 24, "type"], [92, 32, 71, 28], [92, 33, 71, 29], [92, 35, 71, 31], [93, 8, 71, 31], [93, 17, 71, 31, "_len"], [93, 21, 71, 31], [93, 24, 71, 31, "arguments"], [93, 33, 71, 31], [93, 34, 71, 31, "length"], [93, 40, 71, 31], [93, 42, 67, 5, "args"], [93, 46, 67, 9], [93, 53, 67, 9, "Array"], [93, 58, 67, 9], [93, 59, 67, 9, "_len"], [93, 63, 67, 9], [93, 70, 67, 9, "_len"], [93, 74, 67, 9], [93, 85, 67, 9, "_key"], [93, 89, 67, 9], [93, 95, 67, 9, "_key"], [93, 99, 67, 9], [93, 102, 67, 9, "_len"], [93, 106, 67, 9], [93, 108, 67, 9, "_key"], [93, 112, 67, 9], [94, 10, 67, 5, "args"], [94, 14, 67, 9], [94, 15, 67, 9, "_key"], [94, 19, 67, 9], [94, 27, 67, 9, "arguments"], [94, 36, 67, 9], [94, 37, 67, 9, "_key"], [94, 41, 67, 9], [95, 8, 67, 9], [96, 8, 72, 4], [97, 8, 73, 4], [98, 8, 74, 4, "handler"], [98, 15, 74, 11], [98, 18, 74, 14, "event"], [98, 23, 74, 19], [98, 25, 74, 21], [98, 28, 74, 24, "args"], [98, 32, 74, 28], [98, 33, 74, 29], [99, 6, 75, 2], [99, 7, 75, 3], [99, 13, 75, 9], [99, 17, 75, 13, "handler"], [99, 24, 75, 20], [99, 26, 75, 22], [100, 8, 76, 4, "console"], [100, 15, 76, 11], [100, 16, 76, 12, "warn"], [100, 20, 76, 16], [100, 21, 76, 17], [100, 25, 76, 17, "tagMessage"], [100, 42, 76, 27], [100, 44, 76, 28], [100, 89, 76, 73], [100, 90, 76, 74], [100, 91, 76, 75], [101, 6, 77, 2], [102, 4, 78, 0], [102, 5, 78, 1], [103, 4, 78, 1, "runWorklet"], [103, 14, 78, 1], [103, 15, 78, 1, "__closure"], [103, 24, 78, 1], [104, 6, 78, 1, "<PERSON><PERSON><PERSON><PERSON>"], [104, 16, 78, 1], [105, 6, 78, 1, "tagMessage"], [105, 16, 78, 1], [105, 18, 76, 17, "tagMessage"], [106, 4, 76, 27], [107, 4, 76, 27, "runWorklet"], [107, 14, 76, 27], [107, 15, 76, 27, "__workletHash"], [107, 28, 76, 27], [108, 4, 76, 27, "runWorklet"], [108, 14, 76, 27], [108, 15, 76, 27, "__initData"], [108, 25, 76, 27], [108, 28, 76, 27, "_worklet_4006900144310_init_data"], [108, 60, 76, 27], [109, 4, 76, 27, "runWorklet"], [109, 14, 76, 27], [109, 15, 76, 27, "__stackDetails"], [109, 29, 76, 27], [109, 32, 76, 27, "_e"], [109, 34, 76, 27], [110, 4, 76, 27], [110, 11, 76, 27, "runWorklet"], [110, 21, 76, 27], [111, 2, 76, 27], [111, 3, 63, 0], [112, 2, 63, 0], [112, 6, 63, 0, "_worklet_16926571502586_init_data"], [112, 39, 63, 0], [113, 4, 63, 0, "code"], [113, 8, 63, 0], [114, 4, 63, 0, "location"], [114, 12, 63, 0], [115, 4, 63, 0, "sourceMap"], [115, 13, 63, 0], [116, 4, 63, 0, "version"], [116, 11, 63, 0], [117, 2, 63, 0], [118, 2, 63, 0], [118, 6, 63, 0, "isStateChangeEvent"], [118, 24, 63, 0], [118, 27, 80, 0], [119, 4, 80, 0], [119, 8, 80, 0, "_e"], [119, 10, 80, 0], [119, 18, 80, 0, "global"], [119, 24, 80, 0], [119, 25, 80, 0, "Error"], [119, 30, 80, 0], [120, 4, 80, 0], [120, 8, 80, 0, "isStateChangeEvent"], [120, 26, 80, 0], [120, 38, 80, 0, "isStateChangeEvent"], [120, 39, 81, 2, "event"], [120, 44, 81, 73], [120, 46, 82, 36], [121, 6, 84, 2], [122, 6, 85, 2], [122, 13, 85, 9, "event"], [122, 18, 85, 14], [122, 19, 85, 15, "oldState"], [122, 27, 85, 23], [122, 31, 85, 27], [122, 35, 85, 31], [123, 4, 86, 0], [123, 5, 86, 1], [124, 4, 86, 1, "isStateChangeEvent"], [124, 22, 86, 1], [124, 23, 86, 1, "__closure"], [124, 32, 86, 1], [125, 4, 86, 1, "isStateChangeEvent"], [125, 22, 86, 1], [125, 23, 86, 1, "__workletHash"], [125, 36, 86, 1], [126, 4, 86, 1, "isStateChangeEvent"], [126, 22, 86, 1], [126, 23, 86, 1, "__initData"], [126, 33, 86, 1], [126, 36, 86, 1, "_worklet_16926571502586_init_data"], [126, 69, 86, 1], [127, 4, 86, 1, "isStateChangeEvent"], [127, 22, 86, 1], [127, 23, 86, 1, "__stackDetails"], [127, 37, 86, 1], [127, 40, 86, 1, "_e"], [127, 42, 86, 1], [128, 4, 86, 1], [128, 11, 86, 1, "isStateChangeEvent"], [128, 29, 86, 1], [129, 2, 86, 1], [129, 3, 80, 0], [130, 2, 80, 0], [130, 6, 80, 0, "_worklet_2062872232491_init_data"], [130, 38, 80, 0], [131, 4, 80, 0, "code"], [131, 8, 80, 0], [132, 4, 80, 0, "location"], [132, 12, 80, 0], [133, 4, 80, 0, "sourceMap"], [133, 13, 80, 0], [134, 4, 80, 0, "version"], [134, 11, 80, 0], [135, 2, 80, 0], [136, 2, 80, 0], [136, 6, 80, 0, "isTouchEvent"], [136, 18, 80, 0], [136, 21, 88, 0], [137, 4, 88, 0], [137, 8, 88, 0, "_e"], [137, 10, 88, 0], [137, 18, 88, 0, "global"], [137, 24, 88, 0], [137, 25, 88, 0, "Error"], [137, 30, 88, 0], [138, 4, 88, 0], [138, 8, 88, 0, "isTouchEvent"], [138, 20, 88, 0], [138, 32, 88, 0, "isTouchEvent"], [138, 33, 89, 2, "event"], [138, 38, 89, 73], [138, 40, 90, 30], [139, 6, 92, 2], [139, 13, 92, 9, "event"], [139, 18, 92, 14], [139, 19, 92, 15, "eventType"], [139, 28, 92, 24], [139, 32, 92, 28], [139, 36, 92, 32], [140, 4, 93, 0], [140, 5, 93, 1], [141, 4, 93, 1, "isTouchEvent"], [141, 16, 93, 1], [141, 17, 93, 1, "__closure"], [141, 26, 93, 1], [142, 4, 93, 1, "isTouchEvent"], [142, 16, 93, 1], [142, 17, 93, 1, "__workletHash"], [142, 30, 93, 1], [143, 4, 93, 1, "isTouchEvent"], [143, 16, 93, 1], [143, 17, 93, 1, "__initData"], [143, 27, 93, 1], [143, 30, 93, 1, "_worklet_2062872232491_init_data"], [143, 62, 93, 1], [144, 4, 93, 1, "isTouchEvent"], [144, 16, 93, 1], [144, 17, 93, 1, "__stackDetails"], [144, 31, 93, 1], [144, 34, 93, 1, "_e"], [144, 36, 93, 1], [145, 4, 93, 1], [145, 11, 93, 1, "isTouchEvent"], [145, 23, 93, 1], [146, 2, 93, 1], [146, 3, 88, 0], [147, 2, 88, 0], [147, 6, 88, 0, "_worklet_10845617855334_init_data"], [147, 39, 88, 0], [148, 4, 88, 0, "code"], [148, 8, 88, 0], [149, 4, 88, 0, "location"], [149, 12, 88, 0], [150, 4, 88, 0, "sourceMap"], [150, 13, 88, 0], [151, 4, 88, 0, "version"], [151, 11, 88, 0], [152, 2, 88, 0], [153, 2, 95, 7], [153, 11, 95, 16, "useAnimatedGesture"], [153, 29, 95, 34, "useAnimatedGesture"], [153, 30, 96, 2, "preparedGesture"], [153, 45, 96, 39], [153, 47, 97, 2, "needsRebuild"], [153, 59, 97, 23], [153, 61, 98, 2], [154, 4, 99, 2], [154, 8, 99, 6], [154, 9, 99, 7, "Reanimated"], [154, 38, 99, 17], [154, 40, 99, 19], [155, 6, 100, 4], [156, 4, 101, 2], [158, 4, 103, 2], [159, 4, 104, 2], [160, 4, 105, 2], [161, 4, 106, 2], [161, 8, 106, 8, "sharedHandlersCallbacks"], [161, 31, 106, 31], [161, 34, 106, 34, "Reanimated"], [161, 63, 106, 44], [161, 64, 106, 45, "useSharedValue"], [161, 78, 106, 59], [161, 79, 108, 4], [161, 83, 108, 8], [161, 84, 108, 9], [163, 4, 110, 2], [164, 4, 111, 2], [164, 8, 111, 8, "lastUpdateEvent"], [164, 23, 111, 23], [164, 26, 111, 26, "Reanimated"], [164, 55, 111, 36], [164, 56, 111, 37, "useSharedValue"], [164, 70, 111, 51], [164, 71, 113, 4], [164, 73, 113, 6], [164, 74, 113, 7], [166, 4, 115, 2], [167, 4, 116, 2], [167, 8, 116, 8, "stateControllers"], [167, 24, 116, 51], [167, 27, 116, 54], [167, 29, 116, 56], [168, 4, 118, 2], [168, 8, 118, 8, "callback"], [168, 16, 118, 16], [168, 19, 118, 19], [169, 6, 118, 19], [169, 10, 118, 19, "_e"], [169, 12, 118, 19], [169, 20, 118, 19, "global"], [169, 26, 118, 19], [169, 27, 118, 19, "Error"], [169, 32, 118, 19], [170, 6, 118, 19], [170, 10, 118, 19, "useAnimatedGestureTs6"], [170, 31, 118, 19], [170, 43, 118, 19, "useAnimatedGestureTs6"], [170, 44, 119, 4, "event"], [170, 49, 119, 75], [170, 51, 120, 7], [171, 8, 123, 4], [171, 12, 123, 10, "currentCallback"], [171, 27, 123, 25], [171, 30, 123, 28, "sharedHandlersCallbacks"], [171, 53, 123, 51], [171, 54, 123, 52, "value"], [171, 59, 123, 57], [172, 8, 124, 4], [172, 12, 124, 8], [172, 13, 124, 9, "currentCallback"], [172, 28, 124, 24], [172, 30, 124, 26], [173, 10, 125, 6], [174, 8, 126, 4], [175, 8, 128, 4], [175, 13, 128, 9], [175, 17, 128, 13, "i"], [175, 18, 128, 14], [175, 21, 128, 17], [175, 22, 128, 18], [175, 24, 128, 20, "i"], [175, 25, 128, 21], [175, 28, 128, 24, "currentCallback"], [175, 43, 128, 39], [175, 44, 128, 40, "length"], [175, 50, 128, 46], [175, 52, 128, 48, "i"], [175, 53, 128, 49], [175, 55, 128, 51], [175, 57, 128, 53], [176, 10, 129, 6], [176, 14, 129, 12, "gesture"], [176, 21, 129, 19], [176, 24, 129, 22, "currentCallback"], [176, 39, 129, 37], [176, 40, 129, 38, "i"], [176, 41, 129, 39], [176, 42, 129, 40], [177, 10, 131, 6], [177, 14, 131, 10, "event"], [177, 19, 131, 15], [177, 20, 131, 16, "handlerTag"], [177, 30, 131, 26], [177, 35, 131, 31, "gesture"], [177, 42, 131, 38], [177, 43, 131, 39, "handlerTag"], [177, 53, 131, 49], [177, 55, 131, 51], [178, 12, 132, 8], [179, 10, 133, 6], [180, 10, 135, 6], [180, 14, 135, 10, "isStateChangeEvent"], [180, 32, 135, 28], [180, 33, 135, 29, "event"], [180, 38, 135, 34], [180, 39, 135, 35], [180, 41, 135, 37], [181, 12, 136, 8], [181, 16, 137, 10, "event"], [181, 21, 137, 15], [181, 22, 137, 16, "oldState"], [181, 30, 137, 24], [181, 35, 137, 29, "State"], [181, 47, 137, 34], [181, 48, 137, 35, "UNDETERMINED"], [181, 60, 137, 47], [181, 64, 138, 10, "event"], [181, 69, 138, 15], [181, 70, 138, 16, "state"], [181, 75, 138, 21], [181, 80, 138, 26, "State"], [181, 92, 138, 31], [181, 93, 138, 32, "BEGAN"], [181, 98, 138, 37], [181, 100, 139, 10], [182, 14, 140, 10, "runWorklet"], [182, 24, 140, 20], [182, 25, 140, 21, "CALLBACK_TYPE"], [182, 47, 140, 34], [182, 48, 140, 35, "BEGAN"], [182, 53, 140, 40], [182, 55, 140, 42, "gesture"], [182, 62, 140, 49], [182, 64, 140, 51, "event"], [182, 69, 140, 56], [182, 70, 140, 57], [183, 12, 141, 8], [183, 13, 141, 9], [183, 19, 141, 15], [183, 23, 142, 10], [183, 24, 142, 11, "event"], [183, 29, 142, 16], [183, 30, 142, 17, "oldState"], [183, 38, 142, 25], [183, 43, 142, 30, "State"], [183, 55, 142, 35], [183, 56, 142, 36, "BEGAN"], [183, 61, 142, 41], [183, 65, 143, 12, "event"], [183, 70, 143, 17], [183, 71, 143, 18, "oldState"], [183, 79, 143, 26], [183, 84, 143, 31, "State"], [183, 96, 143, 36], [183, 97, 143, 37, "UNDETERMINED"], [183, 109, 143, 49], [183, 114, 144, 10, "event"], [183, 119, 144, 15], [183, 120, 144, 16, "state"], [183, 125, 144, 21], [183, 130, 144, 26, "State"], [183, 142, 144, 31], [183, 143, 144, 32, "ACTIVE"], [183, 149, 144, 38], [183, 151, 145, 10], [184, 14, 146, 10, "runWorklet"], [184, 24, 146, 20], [184, 25, 146, 21, "CALLBACK_TYPE"], [184, 47, 146, 34], [184, 48, 146, 35, "START"], [184, 53, 146, 40], [184, 55, 146, 42, "gesture"], [184, 62, 146, 49], [184, 64, 146, 51, "event"], [184, 69, 146, 56], [184, 70, 146, 57], [185, 14, 147, 10, "lastUpdateEvent"], [185, 29, 147, 25], [185, 30, 147, 26, "value"], [185, 35, 147, 31], [185, 36, 147, 32, "gesture"], [185, 43, 147, 39], [185, 44, 147, 40, "handlerTag"], [185, 54, 147, 50], [185, 55, 147, 51], [185, 58, 147, 54, "undefined"], [185, 67, 147, 63], [186, 12, 148, 8], [186, 13, 148, 9], [186, 19, 148, 15], [186, 23, 149, 10, "event"], [186, 28, 149, 15], [186, 29, 149, 16, "oldState"], [186, 37, 149, 24], [186, 42, 149, 29, "event"], [186, 47, 149, 34], [186, 48, 149, 35, "state"], [186, 53, 149, 40], [186, 57, 150, 10, "event"], [186, 62, 150, 15], [186, 63, 150, 16, "state"], [186, 68, 150, 21], [186, 73, 150, 26, "State"], [186, 85, 150, 31], [186, 86, 150, 32, "END"], [186, 89, 150, 35], [186, 91, 151, 10], [187, 14, 152, 10], [187, 18, 152, 14, "event"], [187, 23, 152, 19], [187, 24, 152, 20, "oldState"], [187, 32, 152, 28], [187, 37, 152, 33, "State"], [187, 49, 152, 38], [187, 50, 152, 39, "ACTIVE"], [187, 56, 152, 45], [187, 58, 152, 47], [188, 16, 153, 12, "runWorklet"], [188, 26, 153, 22], [188, 27, 153, 23, "CALLBACK_TYPE"], [188, 49, 153, 36], [188, 50, 153, 37, "END"], [188, 53, 153, 40], [188, 55, 153, 42, "gesture"], [188, 62, 153, 49], [188, 64, 153, 51, "event"], [188, 69, 153, 56], [188, 71, 153, 58], [188, 75, 153, 62], [188, 76, 153, 63], [189, 14, 154, 10], [190, 14, 155, 10, "runWorklet"], [190, 24, 155, 20], [190, 25, 155, 21, "CALLBACK_TYPE"], [190, 47, 155, 34], [190, 48, 155, 35, "FINALIZE"], [190, 56, 155, 43], [190, 58, 155, 45, "gesture"], [190, 65, 155, 52], [190, 67, 155, 54, "event"], [190, 72, 155, 59], [190, 74, 155, 61], [190, 78, 155, 65], [190, 79, 155, 66], [191, 12, 156, 8], [191, 13, 156, 9], [191, 19, 156, 15], [191, 23, 157, 10], [191, 24, 157, 11, "event"], [191, 29, 157, 16], [191, 30, 157, 17, "state"], [191, 35, 157, 22], [191, 40, 157, 27, "State"], [191, 52, 157, 32], [191, 53, 157, 33, "FAILED"], [191, 59, 157, 39], [191, 63, 157, 43, "event"], [191, 68, 157, 48], [191, 69, 157, 49, "state"], [191, 74, 157, 54], [191, 79, 157, 59, "State"], [191, 91, 157, 64], [191, 92, 157, 65, "CANCELLED"], [191, 101, 157, 74], [191, 106, 158, 10, "event"], [191, 111, 158, 15], [191, 112, 158, 16, "state"], [191, 117, 158, 21], [191, 122, 158, 26, "event"], [191, 127, 158, 31], [191, 128, 158, 32, "oldState"], [191, 136, 158, 40], [191, 138, 159, 10], [192, 14, 160, 10], [192, 18, 160, 14, "event"], [192, 23, 160, 19], [192, 24, 160, 20, "oldState"], [192, 32, 160, 28], [192, 37, 160, 33, "State"], [192, 49, 160, 38], [192, 50, 160, 39, "ACTIVE"], [192, 56, 160, 45], [192, 58, 160, 47], [193, 16, 161, 12, "runWorklet"], [193, 26, 161, 22], [193, 27, 161, 23, "CALLBACK_TYPE"], [193, 49, 161, 36], [193, 50, 161, 37, "END"], [193, 53, 161, 40], [193, 55, 161, 42, "gesture"], [193, 62, 161, 49], [193, 64, 161, 51, "event"], [193, 69, 161, 56], [193, 71, 161, 58], [193, 76, 161, 63], [193, 77, 161, 64], [194, 14, 162, 10], [195, 14, 163, 10, "runWorklet"], [195, 24, 163, 20], [195, 25, 163, 21, "CALLBACK_TYPE"], [195, 47, 163, 34], [195, 48, 163, 35, "FINALIZE"], [195, 56, 163, 43], [195, 58, 163, 45, "gesture"], [195, 65, 163, 52], [195, 67, 163, 54, "event"], [195, 72, 163, 59], [195, 74, 163, 61], [195, 79, 163, 66], [195, 80, 163, 67], [196, 12, 164, 8], [197, 10, 165, 6], [197, 11, 165, 7], [197, 17, 165, 13], [197, 21, 165, 17, "isTouchEvent"], [197, 33, 165, 29], [197, 34, 165, 30, "event"], [197, 39, 165, 35], [197, 40, 165, 36], [197, 42, 165, 38], [198, 12, 166, 8], [198, 16, 166, 12], [198, 17, 166, 13, "stateControllers"], [198, 33, 166, 29], [198, 34, 166, 30, "i"], [198, 35, 166, 31], [198, 36, 166, 32], [198, 38, 166, 34], [199, 14, 167, 10, "stateControllers"], [199, 30, 167, 26], [199, 31, 167, 27, "i"], [199, 32, 167, 28], [199, 33, 167, 29], [199, 36, 167, 32, "GestureStateManager"], [199, 76, 167, 51], [199, 77, 167, 52, "create"], [199, 83, 167, 58], [199, 84, 167, 59, "event"], [199, 89, 167, 64], [199, 90, 167, 65, "handlerTag"], [199, 100, 167, 75], [199, 101, 167, 76], [200, 12, 168, 8], [201, 12, 170, 8], [201, 16, 170, 12, "event"], [201, 21, 170, 17], [201, 22, 170, 18, "eventType"], [201, 31, 170, 27], [201, 36, 170, 32, "TouchEventType"], [201, 66, 170, 46], [201, 67, 170, 47, "UNDETERMINED"], [201, 79, 170, 59], [201, 81, 170, 61], [202, 14, 171, 10, "runWorklet"], [202, 24, 171, 20], [202, 25, 172, 12, "touchEventTypeToCallbackType"], [202, 53, 172, 40], [202, 54, 172, 41, "event"], [202, 59, 172, 46], [202, 60, 172, 47, "eventType"], [202, 69, 172, 56], [202, 70, 172, 57], [202, 72, 173, 12, "gesture"], [202, 79, 173, 19], [202, 81, 174, 12, "event"], [202, 86, 174, 17], [202, 88, 175, 12, "stateControllers"], [202, 104, 175, 28], [202, 105, 175, 29, "i"], [202, 106, 175, 30], [202, 107, 176, 10], [202, 108, 176, 11], [203, 12, 177, 8], [204, 10, 178, 6], [204, 11, 178, 7], [204, 17, 178, 13], [205, 12, 179, 8, "runWorklet"], [205, 22, 179, 18], [205, 23, 179, 19, "CALLBACK_TYPE"], [205, 45, 179, 32], [205, 46, 179, 33, "UPDATE"], [205, 52, 179, 39], [205, 54, 179, 41, "gesture"], [205, 61, 179, 48], [205, 63, 179, 50, "event"], [205, 68, 179, 55], [205, 69, 179, 56], [206, 12, 181, 8], [206, 16, 181, 12, "gesture"], [206, 23, 181, 19], [206, 24, 181, 20, "onChange"], [206, 32, 181, 28], [206, 36, 181, 32, "gesture"], [206, 43, 181, 39], [206, 44, 181, 40, "changeEventCalculator"], [206, 65, 181, 61], [206, 67, 181, 63], [207, 14, 182, 10, "runWorklet"], [207, 24, 182, 20], [207, 25, 183, 12, "CALLBACK_TYPE"], [207, 47, 183, 25], [207, 48, 183, 26, "CHANGE"], [207, 54, 183, 32], [207, 56, 184, 12, "gesture"], [207, 63, 184, 19], [207, 65, 185, 12, "gesture"], [207, 72, 185, 19], [207, 73, 185, 20, "changeEventCalculator"], [207, 94, 185, 41], [207, 97, 186, 14, "event"], [207, 102, 186, 19], [207, 104, 187, 14, "lastUpdateEvent"], [207, 119, 187, 29], [207, 120, 187, 30, "value"], [207, 125, 187, 35], [207, 126, 187, 36, "gesture"], [207, 133, 187, 43], [207, 134, 187, 44, "handlerTag"], [207, 144, 187, 54], [207, 145, 188, 12], [207, 146, 189, 10], [207, 147, 189, 11], [208, 14, 191, 10, "lastUpdateEvent"], [208, 29, 191, 25], [208, 30, 191, 26, "value"], [208, 35, 191, 31], [208, 36, 191, 32, "gesture"], [208, 43, 191, 39], [208, 44, 191, 40, "handlerTag"], [208, 54, 191, 50], [208, 55, 191, 51], [208, 58, 191, 54, "event"], [208, 63, 191, 59], [209, 12, 192, 8], [210, 10, 193, 6], [211, 8, 194, 4], [212, 6, 195, 2], [212, 7, 195, 3], [213, 6, 195, 3, "useAnimatedGestureTs6"], [213, 27, 195, 3], [213, 28, 195, 3, "__closure"], [213, 37, 195, 3], [214, 8, 195, 3, "sharedHandlersCallbacks"], [214, 31, 195, 3], [215, 8, 195, 3, "isStateChangeEvent"], [215, 26, 195, 3], [216, 8, 195, 3, "State"], [216, 13, 195, 3], [216, 15, 137, 29, "State"], [216, 27, 137, 34], [217, 8, 137, 34, "runWorklet"], [217, 18, 137, 34], [218, 8, 137, 34, "CALLBACK_TYPE"], [218, 21, 137, 34], [218, 23, 140, 21, "CALLBACK_TYPE"], [218, 45, 140, 34], [219, 8, 140, 34, "lastUpdateEvent"], [219, 23, 140, 34], [220, 8, 140, 34, "isTouchEvent"], [220, 20, 140, 34], [221, 8, 140, 34, "stateControllers"], [221, 24, 140, 34], [222, 8, 140, 34, "GestureStateManager"], [222, 27, 140, 34], [222, 29, 167, 32, "GestureStateManager"], [222, 69, 167, 51], [223, 8, 167, 51, "TouchEventType"], [223, 22, 167, 51], [223, 24, 170, 32, "TouchEventType"], [223, 54, 170, 46], [224, 8, 170, 46, "touchEventTypeToCallbackType"], [225, 6, 170, 46], [226, 6, 170, 46, "useAnimatedGestureTs6"], [226, 27, 170, 46], [226, 28, 170, 46, "__workletHash"], [226, 41, 170, 46], [227, 6, 170, 46, "useAnimatedGestureTs6"], [227, 27, 170, 46], [227, 28, 170, 46, "__initData"], [227, 38, 170, 46], [227, 41, 170, 46, "_worklet_10845617855334_init_data"], [227, 74, 170, 46], [228, 6, 170, 46, "useAnimatedGestureTs6"], [228, 27, 170, 46], [228, 28, 170, 46, "__stackDetails"], [228, 42, 170, 46], [228, 45, 170, 46, "_e"], [228, 47, 170, 46], [229, 6, 170, 46], [229, 13, 170, 46, "useAnimatedGestureTs6"], [229, 34, 170, 46], [230, 4, 170, 46], [230, 5, 118, 19], [230, 7, 195, 3], [232, 4, 197, 2], [233, 4, 198, 2], [233, 8, 198, 8, "event"], [233, 13, 198, 13], [233, 16, 198, 16, "Reanimated"], [233, 45, 198, 26], [233, 46, 198, 27, "useEvent"], [233, 54, 198, 35], [233, 55, 199, 4, "callback"], [233, 63, 199, 12], [233, 65, 200, 4], [233, 66, 200, 5], [233, 95, 200, 34], [233, 97, 200, 36], [233, 120, 200, 59], [233, 121, 200, 60], [233, 123, 201, 4, "needsRebuild"], [233, 135, 202, 2], [233, 136, 202, 3], [234, 4, 204, 2, "preparedGesture"], [234, 19, 204, 17], [234, 20, 204, 18, "animatedEventHandler"], [234, 40, 204, 38], [234, 43, 204, 41, "event"], [234, 48, 204, 46], [235, 4, 205, 2, "preparedGesture"], [235, 19, 205, 17], [235, 20, 205, 18, "animatedHandlers"], [235, 36, 205, 34], [235, 39, 205, 37, "sharedHandlersCallbacks"], [235, 62, 205, 60], [236, 2, 206, 0], [237, 0, 206, 1], [237, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON>", "touchEventTypeToCallbackType", "runWorklet", "isStateChangeEvent", "isTouchEvent", "useAnimatedGesture", "callback"], "mappings": "AAA;ACgB;CD2B;AEE;CFe;AGE;CHe;AIE;CJM;AKE;CLK;OME;mBCuB;GD6E;CNW"}}, "type": "js/module"}]}