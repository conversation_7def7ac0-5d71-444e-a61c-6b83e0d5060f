{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 84}, "end": {"line": 2, "column": 44, "index": 128}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 129}, "end": {"line": 6, "column": 32, "index": 224}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.longPressHandlerName = exports.longPressGestureHandlerProps = exports.LongPressGestureHandler = void 0;\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[1], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[2], \"./gestureHandlerCommon\");\n  var longPressGestureHandlerProps = exports.longPressGestureHandlerProps = ['minDurationMs', 'maxDist', 'numberOfPointers'];\n\n  /**\n   * @deprecated LongPressGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.LongPress()` instead.\n   */\n\n  var longPressHandlerName = exports.longPressHandlerName = 'LongPressGestureHandler';\n\n  /**\n   * @deprecated LongPressGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.LongPress()` instead.\n   */\n\n  /**\n   * @deprecated LongPressGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.LongPress()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n  var LongPressGestureHandler = exports.LongPressGestureHandler = (0, _createHandler.default)({\n    name: longPressHandlerName,\n    allowedProps: [..._gestureHandlerCommon.baseGestureHandlerProps, ...longPressGestureHandlerProps],\n    config: {\n      shouldCancelWhenOutside: true\n    }\n  });\n});", "lineCount": 32, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_createHandler"], [7, 20, 2, 0], [7, 23, 2, 0, "_interopRequireDefault"], [7, 45, 2, 0], [7, 46, 2, 0, "require"], [7, 53, 2, 0], [7, 54, 2, 0, "_dependencyMap"], [7, 68, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 27, 3, 0], [8, 30, 3, 0, "require"], [8, 37, 3, 0], [8, 38, 3, 0, "_dependencyMap"], [8, 52, 3, 0], [9, 2, 8, 7], [9, 6, 8, 13, "longPressGestureHandlerProps"], [9, 34, 8, 41], [9, 37, 8, 41, "exports"], [9, 44, 8, 41], [9, 45, 8, 41, "longPressGestureHandlerProps"], [9, 73, 8, 41], [9, 76, 8, 44], [9, 77, 9, 2], [9, 92, 9, 17], [9, 94, 10, 2], [9, 103, 10, 11], [9, 105, 11, 2], [9, 123, 11, 20], [9, 124, 12, 10], [11, 2, 35, 0], [12, 0, 36, 0], [13, 0, 37, 0], [15, 2, 42, 7], [15, 6, 42, 13, "longPressHandlerName"], [15, 26, 42, 33], [15, 29, 42, 33, "exports"], [15, 36, 42, 33], [15, 37, 42, 33, "longPressHandlerName"], [15, 57, 42, 33], [15, 60, 42, 36], [15, 85, 42, 61], [17, 2, 44, 0], [18, 0, 45, 0], [19, 0, 46, 0], [21, 2, 49, 0], [22, 0, 50, 0], [23, 0, 51, 0], [24, 2, 52, 0], [25, 2, 53, 7], [25, 6, 53, 13, "LongPressGestureHandler"], [25, 29, 53, 36], [25, 32, 53, 36, "exports"], [25, 39, 53, 36], [25, 40, 53, 36, "LongPressGestureHandler"], [25, 63, 53, 36], [25, 66, 53, 39], [25, 70, 53, 39, "createHandler"], [25, 92, 53, 52], [25, 94, 56, 2], [26, 4, 57, 2, "name"], [26, 8, 57, 6], [26, 10, 57, 8, "longPressHandlerName"], [26, 30, 57, 28], [27, 4, 58, 2, "allowedProps"], [27, 16, 58, 14], [27, 18, 58, 16], [27, 19, 59, 4], [27, 22, 59, 7, "baseGestureHandlerProps"], [27, 67, 59, 30], [27, 69, 60, 4], [27, 72, 60, 7, "longPressGestureHandlerProps"], [27, 100, 60, 35], [27, 101, 61, 12], [28, 4, 62, 2, "config"], [28, 10, 62, 8], [28, 12, 62, 10], [29, 6, 63, 4, "shouldCancelWhenOutside"], [29, 29, 63, 27], [29, 31, 63, 29], [30, 4, 64, 2], [31, 2, 65, 0], [31, 3, 65, 1], [31, 4, 65, 2], [32, 0, 65, 3], [32, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}