{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Components/View/View", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 13}, "end": {"line": 21, "column": 63}}], "key": "H/3fvmiHyIdASS62Hfb3a4a54KU=", "exportNames": ["*"]}}, {"name": "../../../Libraries/StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 22, "column": 19}, "end": {"line": 22, "column": 70}}], "key": "Nurrv5y9ebtgGhUjBt0E/GEpaGk=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Text/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 23, "column": 13}, "end": {"line": 23, "column": 52}}], "key": "MoPWeVCFlo44fZk7PVmQmJJxnfs=", "exportNames": ["*"]}}, {"name": "./resolveBoxStyle", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 24, "column": 24}, "end": {"line": 24, "column": 52}}], "key": "s1PkX5FW7oAloTQiiqOkan+M2fQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[2], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\src\\\\private\\\\inspector\\\\BoxInspector.js\";\n  var View = require(_dependencyMap[3], \"../../../Libraries/Components/View/View\").default;\n  var StyleSheet = require(_dependencyMap[4], \"../../../Libraries/StyleSheet/StyleSheet\").default;\n  var Text = require(_dependencyMap[5], \"../../../Libraries/Text/Text\").default;\n  var resolveBoxStyle = require(_dependencyMap[6], \"./resolveBoxStyle\").default;\n  var blank = {\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0\n  };\n  function BoxInspector(_ref) {\n    var style = _ref.style,\n      frame = _ref.frame;\n    var margin = style && resolveBoxStyle('margin', style) || blank;\n    var padding = style && resolveBoxStyle('padding', style) || blank;\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(BoxContainer, {\n      title: \"margin\",\n      titleStyle: styles.marginLabel,\n      box: margin,\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(BoxContainer, {\n        title: \"padding\",\n        box: padding,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(View, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Text, {\n            style: styles.innerText,\n            children: [\"(\", (frame?.left || 0).toFixed(1), \", \", (frame?.top || 0).toFixed(1), \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Text, {\n            style: styles.innerText,\n            children: [(frame?.width || 0).toFixed(1), \" \\xD7\", ' ', (frame?.height || 0).toFixed(1)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 5\n    }, this);\n  }\n  function BoxContainer(_ref2) {\n    var title = _ref2.title,\n      titleStyle = _ref2.titleStyle,\n      box = _ref2.box,\n      children = _ref2.children;\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(View, {\n      style: styles.box,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(View, {\n        style: styles.row,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Text, {\n          style: [titleStyle, styles.label],\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Text, {\n          style: styles.boxText,\n          children: box.top\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(View, {\n        style: styles.row,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Text, {\n          style: styles.boxText,\n          children: box.left\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 9\n        }, this), children, /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Text, {\n          style: styles.boxText,\n          children: box.right\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Text, {\n        style: styles.boxText,\n        children: box.bottom\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = StyleSheet.create({\n    row: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      justifyContent: 'space-around'\n    },\n    marginLabel: {\n      width: 60\n    },\n    label: {\n      fontSize: 10,\n      color: 'rgb(255,100,0)',\n      marginLeft: 5,\n      flex: 1,\n      textAlign: 'left',\n      top: -3\n    },\n    innerText: {\n      color: 'yellow',\n      fontSize: 12,\n      textAlign: 'center',\n      width: 70\n    },\n    box: {\n      borderWidth: 1,\n      borderColor: 'grey'\n    },\n    boxText: {\n      color: 'white',\n      fontSize: 12,\n      marginHorizontal: 3,\n      marginVertical: 2,\n      textAlign: 'center'\n    }\n  });\n  var _default = exports.default = BoxInspector;\n});", "lineCount": 165, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 19, 0], [9, 6, 19, 0, "_react"], [9, 12, 19, 0], [9, 15, 19, 0, "_interopRequireDefault"], [9, 37, 19, 0], [9, 38, 19, 0, "require"], [9, 45, 19, 0], [9, 46, 19, 0, "_dependencyMap"], [9, 60, 19, 0], [10, 2, 19, 26], [10, 6, 19, 26, "_jsxDevRuntime"], [10, 20, 19, 26], [10, 23, 19, 26, "require"], [10, 30, 19, 26], [10, 31, 19, 26, "_dependencyMap"], [10, 45, 19, 26], [11, 2, 19, 26], [11, 6, 19, 26, "_jsxFileName"], [11, 18, 19, 26], [12, 2, 21, 0], [12, 6, 21, 6, "View"], [12, 10, 21, 10], [12, 13, 21, 13, "require"], [12, 20, 21, 20], [12, 21, 21, 20, "_dependencyMap"], [12, 35, 21, 20], [12, 81, 21, 62], [12, 82, 21, 63], [12, 83, 21, 64, "default"], [12, 90, 21, 71], [13, 2, 22, 0], [13, 6, 22, 6, "StyleSheet"], [13, 16, 22, 16], [13, 19, 22, 19, "require"], [13, 26, 22, 26], [13, 27, 22, 26, "_dependencyMap"], [13, 41, 22, 26], [13, 88, 22, 69], [13, 89, 22, 70], [13, 90, 22, 71, "default"], [13, 97, 22, 78], [14, 2, 23, 0], [14, 6, 23, 6, "Text"], [14, 10, 23, 10], [14, 13, 23, 13, "require"], [14, 20, 23, 20], [14, 21, 23, 20, "_dependencyMap"], [14, 35, 23, 20], [14, 70, 23, 51], [14, 71, 23, 52], [14, 72, 23, 53, "default"], [14, 79, 23, 60], [15, 2, 24, 0], [15, 6, 24, 6, "resolveBoxStyle"], [15, 21, 24, 21], [15, 24, 24, 24, "require"], [15, 31, 24, 31], [15, 32, 24, 31, "_dependencyMap"], [15, 46, 24, 31], [15, 70, 24, 51], [15, 71, 24, 52], [15, 72, 24, 53, "default"], [15, 79, 24, 60], [16, 2, 26, 0], [16, 6, 26, 6, "blank"], [16, 11, 26, 11], [16, 14, 26, 14], [17, 4, 27, 2, "top"], [17, 7, 27, 5], [17, 9, 27, 7], [17, 10, 27, 8], [18, 4, 28, 2, "left"], [18, 8, 28, 6], [18, 10, 28, 8], [18, 11, 28, 9], [19, 4, 29, 2, "right"], [19, 9, 29, 7], [19, 11, 29, 9], [19, 12, 29, 10], [20, 4, 30, 2, "bottom"], [20, 10, 30, 8], [20, 12, 30, 10], [21, 2, 31, 0], [21, 3, 31, 1], [22, 2, 38, 0], [22, 11, 38, 9, "BoxInspector"], [22, 23, 38, 21, "BoxInspector"], [22, 24, 38, 21, "_ref"], [22, 28, 38, 21], [22, 30, 38, 69], [23, 4, 38, 69], [23, 8, 38, 23, "style"], [23, 13, 38, 28], [23, 16, 38, 28, "_ref"], [23, 20, 38, 28], [23, 21, 38, 23, "style"], [23, 26, 38, 28], [24, 6, 38, 30, "frame"], [24, 11, 38, 35], [24, 14, 38, 35, "_ref"], [24, 18, 38, 35], [24, 19, 38, 30, "frame"], [24, 24, 38, 35], [25, 4, 39, 2], [25, 8, 39, 8, "margin"], [25, 14, 39, 14], [25, 17, 39, 18, "style"], [25, 22, 39, 23], [25, 26, 39, 27, "resolveBoxStyle"], [25, 41, 39, 42], [25, 42, 39, 43], [25, 50, 39, 51], [25, 52, 39, 53, "style"], [25, 57, 39, 58], [25, 58, 39, 59], [25, 62, 39, 64, "blank"], [25, 67, 39, 69], [26, 4, 40, 2], [26, 8, 40, 8, "padding"], [26, 15, 40, 15], [26, 18, 40, 19, "style"], [26, 23, 40, 24], [26, 27, 40, 28, "resolveBoxStyle"], [26, 42, 40, 43], [26, 43, 40, 44], [26, 52, 40, 53], [26, 54, 40, 55, "style"], [26, 59, 40, 60], [26, 60, 40, 61], [26, 64, 40, 66, "blank"], [26, 69, 40, 71], [27, 4, 42, 2], [27, 24, 43, 4], [27, 28, 43, 4, "_jsxDevRuntime"], [27, 42, 43, 4], [27, 43, 43, 4, "jsxDEV"], [27, 49, 43, 4], [27, 51, 43, 5, "BoxContainer"], [27, 63, 43, 17], [28, 6, 43, 18, "title"], [28, 11, 43, 23], [28, 13, 43, 24], [28, 21, 43, 32], [29, 6, 43, 33, "titleStyle"], [29, 16, 43, 43], [29, 18, 43, 45, "styles"], [29, 24, 43, 51], [29, 25, 43, 52, "margin<PERSON>abel"], [29, 36, 43, 64], [30, 6, 43, 65, "box"], [30, 9, 43, 68], [30, 11, 43, 70, "margin"], [30, 17, 43, 77], [31, 6, 43, 77, "children"], [31, 14, 43, 77], [31, 29, 44, 6], [31, 33, 44, 6, "_jsxDevRuntime"], [31, 47, 44, 6], [31, 48, 44, 6, "jsxDEV"], [31, 54, 44, 6], [31, 56, 44, 7, "BoxContainer"], [31, 68, 44, 19], [32, 8, 44, 20, "title"], [32, 13, 44, 25], [32, 15, 44, 26], [32, 24, 44, 35], [33, 8, 44, 36, "box"], [33, 11, 44, 39], [33, 13, 44, 41, "padding"], [33, 20, 44, 49], [34, 8, 44, 49, "children"], [34, 16, 44, 49], [34, 31, 45, 8], [34, 35, 45, 8, "_jsxDevRuntime"], [34, 49, 45, 8], [34, 50, 45, 8, "jsxDEV"], [34, 56, 45, 8], [34, 58, 45, 9, "View"], [34, 62, 45, 13], [35, 10, 45, 13, "children"], [35, 18, 45, 13], [35, 34, 46, 10], [35, 38, 46, 10, "_jsxDevRuntime"], [35, 52, 46, 10], [35, 53, 46, 10, "jsxDEV"], [35, 59, 46, 10], [35, 61, 46, 11, "Text"], [35, 65, 46, 15], [36, 12, 46, 16, "style"], [36, 17, 46, 21], [36, 19, 46, 23, "styles"], [36, 25, 46, 29], [36, 26, 46, 30, "innerText"], [36, 35, 46, 40], [37, 12, 46, 40, "children"], [37, 20, 46, 40], [37, 23, 46, 41], [37, 26, 47, 13], [37, 28, 47, 14], [37, 29, 47, 15, "frame"], [37, 34, 47, 20], [37, 36, 47, 22, "left"], [37, 40, 47, 26], [37, 44, 47, 30], [37, 45, 47, 31], [37, 47, 47, 33, "toFixed"], [37, 54, 47, 40], [37, 55, 47, 41], [37, 56, 47, 42], [37, 57, 47, 43], [37, 59, 47, 44], [37, 63, 47, 46], [37, 65, 47, 47], [37, 66, 47, 48, "frame"], [37, 71, 47, 53], [37, 73, 47, 55, "top"], [37, 76, 47, 58], [37, 80, 47, 62], [37, 81, 47, 63], [37, 83, 47, 65, "toFixed"], [37, 90, 47, 72], [37, 91, 47, 73], [37, 92, 47, 74], [37, 93, 47, 75], [37, 95, 47, 76], [37, 98, 48, 10], [38, 10, 48, 10], [39, 12, 48, 10, "fileName"], [39, 20, 48, 10], [39, 22, 48, 10, "_jsxFileName"], [39, 34, 48, 10], [40, 12, 48, 10, "lineNumber"], [40, 22, 48, 10], [41, 12, 48, 10, "columnNumber"], [41, 24, 48, 10], [42, 10, 48, 10], [42, 17, 48, 16], [42, 18, 48, 17], [42, 33, 49, 10], [42, 37, 49, 10, "_jsxDevRuntime"], [42, 51, 49, 10], [42, 52, 49, 10, "jsxDEV"], [42, 58, 49, 10], [42, 60, 49, 11, "Text"], [42, 64, 49, 15], [43, 12, 49, 16, "style"], [43, 17, 49, 21], [43, 19, 49, 23, "styles"], [43, 25, 49, 29], [43, 26, 49, 30, "innerText"], [43, 35, 49, 40], [44, 12, 49, 40, "children"], [44, 20, 49, 40], [44, 23, 50, 13], [44, 24, 50, 14, "frame"], [44, 29, 50, 19], [44, 31, 50, 21, "width"], [44, 36, 50, 26], [44, 40, 50, 30], [44, 41, 50, 31], [44, 43, 50, 33, "toFixed"], [44, 50, 50, 40], [44, 51, 50, 41], [44, 52, 50, 42], [44, 53, 50, 43], [44, 55, 50, 44], [44, 62, 50, 52], [44, 64, 50, 53], [44, 67, 50, 56], [44, 69, 51, 13], [44, 70, 51, 14, "frame"], [44, 75, 51, 19], [44, 77, 51, 21, "height"], [44, 83, 51, 27], [44, 87, 51, 31], [44, 88, 51, 32], [44, 90, 51, 34, "toFixed"], [44, 97, 51, 41], [44, 98, 51, 42], [44, 99, 51, 43], [44, 100, 51, 44], [45, 10, 51, 44], [46, 12, 51, 44, "fileName"], [46, 20, 51, 44], [46, 22, 51, 44, "_jsxFileName"], [46, 34, 51, 44], [47, 12, 51, 44, "lineNumber"], [47, 22, 51, 44], [48, 12, 51, 44, "columnNumber"], [48, 24, 51, 44], [49, 10, 51, 44], [49, 17, 52, 16], [49, 18, 52, 17], [50, 8, 52, 17], [51, 10, 52, 17, "fileName"], [51, 18, 52, 17], [51, 20, 52, 17, "_jsxFileName"], [51, 32, 52, 17], [52, 10, 52, 17, "lineNumber"], [52, 20, 52, 17], [53, 10, 52, 17, "columnNumber"], [53, 22, 52, 17], [54, 8, 52, 17], [54, 15, 53, 14], [55, 6, 53, 15], [56, 8, 53, 15, "fileName"], [56, 16, 53, 15], [56, 18, 53, 15, "_jsxFileName"], [56, 30, 53, 15], [57, 8, 53, 15, "lineNumber"], [57, 18, 53, 15], [58, 8, 53, 15, "columnNumber"], [58, 20, 53, 15], [59, 6, 53, 15], [59, 13, 54, 20], [60, 4, 54, 21], [61, 6, 54, 21, "fileName"], [61, 14, 54, 21], [61, 16, 54, 21, "_jsxFileName"], [61, 28, 54, 21], [62, 6, 54, 21, "lineNumber"], [62, 16, 54, 21], [63, 6, 54, 21, "columnNumber"], [63, 18, 54, 21], [64, 4, 54, 21], [64, 11, 55, 18], [64, 12, 55, 19], [65, 2, 57, 0], [66, 2, 71, 0], [66, 11, 71, 9, "BoxContainer"], [66, 23, 71, 21, "BoxContainer"], [66, 24, 71, 21, "_ref2"], [66, 29, 71, 21], [66, 31, 76, 34], [67, 4, 76, 34], [67, 8, 72, 2, "title"], [67, 13, 72, 7], [67, 16, 72, 7, "_ref2"], [67, 21, 72, 7], [67, 22, 72, 2, "title"], [67, 27, 72, 7], [68, 6, 73, 2, "titleStyle"], [68, 16, 73, 12], [68, 19, 73, 12, "_ref2"], [68, 24, 73, 12], [68, 25, 73, 2, "titleStyle"], [68, 35, 73, 12], [69, 6, 74, 2, "box"], [69, 9, 74, 5], [69, 12, 74, 5, "_ref2"], [69, 17, 74, 5], [69, 18, 74, 2, "box"], [69, 21, 74, 5], [70, 6, 75, 2, "children"], [70, 14, 75, 10], [70, 17, 75, 10, "_ref2"], [70, 22, 75, 10], [70, 23, 75, 2, "children"], [70, 31, 75, 10], [71, 4, 77, 2], [71, 24, 78, 4], [71, 28, 78, 4, "_jsxDevRuntime"], [71, 42, 78, 4], [71, 43, 78, 4, "jsxDEV"], [71, 49, 78, 4], [71, 51, 78, 5, "View"], [71, 55, 78, 9], [72, 6, 78, 10, "style"], [72, 11, 78, 15], [72, 13, 78, 17, "styles"], [72, 19, 78, 23], [72, 20, 78, 24, "box"], [72, 23, 78, 28], [73, 6, 78, 28, "children"], [73, 14, 78, 28], [73, 30, 79, 6], [73, 34, 79, 6, "_jsxDevRuntime"], [73, 48, 79, 6], [73, 49, 79, 6, "jsxDEV"], [73, 55, 79, 6], [73, 57, 79, 7, "View"], [73, 61, 79, 11], [74, 8, 79, 12, "style"], [74, 13, 79, 17], [74, 15, 79, 19, "styles"], [74, 21, 79, 25], [74, 22, 79, 26, "row"], [74, 25, 79, 30], [75, 8, 79, 30, "children"], [75, 16, 79, 30], [75, 32, 81, 8], [75, 36, 81, 8, "_jsxDevRuntime"], [75, 50, 81, 8], [75, 51, 81, 8, "jsxDEV"], [75, 57, 81, 8], [75, 59, 81, 9, "Text"], [75, 63, 81, 13], [76, 10, 81, 14, "style"], [76, 15, 81, 19], [76, 17, 81, 21], [76, 18, 81, 22, "titleStyle"], [76, 28, 81, 32], [76, 30, 81, 34, "styles"], [76, 36, 81, 40], [76, 37, 81, 41, "label"], [76, 42, 81, 46], [76, 43, 81, 48], [77, 10, 81, 48, "children"], [77, 18, 81, 48], [77, 20, 81, 50, "title"], [78, 8, 81, 55], [79, 10, 81, 55, "fileName"], [79, 18, 81, 55], [79, 20, 81, 55, "_jsxFileName"], [79, 32, 81, 55], [80, 10, 81, 55, "lineNumber"], [80, 20, 81, 55], [81, 10, 81, 55, "columnNumber"], [81, 22, 81, 55], [82, 8, 81, 55], [82, 15, 81, 62], [82, 16, 81, 63], [82, 31, 82, 8], [82, 35, 82, 8, "_jsxDevRuntime"], [82, 49, 82, 8], [82, 50, 82, 8, "jsxDEV"], [82, 56, 82, 8], [82, 58, 82, 9, "Text"], [82, 62, 82, 13], [83, 10, 82, 14, "style"], [83, 15, 82, 19], [83, 17, 82, 21, "styles"], [83, 23, 82, 27], [83, 24, 82, 28, "boxText"], [83, 31, 82, 36], [84, 10, 82, 36, "children"], [84, 18, 82, 36], [84, 20, 82, 38, "box"], [84, 23, 82, 41], [84, 24, 82, 42, "top"], [85, 8, 82, 45], [86, 10, 82, 45, "fileName"], [86, 18, 82, 45], [86, 20, 82, 45, "_jsxFileName"], [86, 32, 82, 45], [87, 10, 82, 45, "lineNumber"], [87, 20, 82, 45], [88, 10, 82, 45, "columnNumber"], [88, 22, 82, 45], [89, 8, 82, 45], [89, 15, 82, 52], [89, 16, 82, 53], [90, 6, 82, 53], [91, 8, 82, 53, "fileName"], [91, 16, 82, 53], [91, 18, 82, 53, "_jsxFileName"], [91, 30, 82, 53], [92, 8, 82, 53, "lineNumber"], [92, 18, 82, 53], [93, 8, 82, 53, "columnNumber"], [93, 20, 82, 53], [94, 6, 82, 53], [94, 13, 83, 12], [94, 14, 83, 13], [94, 29, 84, 6], [94, 33, 84, 6, "_jsxDevRuntime"], [94, 47, 84, 6], [94, 48, 84, 6, "jsxDEV"], [94, 54, 84, 6], [94, 56, 84, 7, "View"], [94, 60, 84, 11], [95, 8, 84, 12, "style"], [95, 13, 84, 17], [95, 15, 84, 19, "styles"], [95, 21, 84, 25], [95, 22, 84, 26, "row"], [95, 25, 84, 30], [96, 8, 84, 30, "children"], [96, 16, 84, 30], [96, 32, 85, 8], [96, 36, 85, 8, "_jsxDevRuntime"], [96, 50, 85, 8], [96, 51, 85, 8, "jsxDEV"], [96, 57, 85, 8], [96, 59, 85, 9, "Text"], [96, 63, 85, 13], [97, 10, 85, 14, "style"], [97, 15, 85, 19], [97, 17, 85, 21, "styles"], [97, 23, 85, 27], [97, 24, 85, 28, "boxText"], [97, 31, 85, 36], [98, 10, 85, 36, "children"], [98, 18, 85, 36], [98, 20, 85, 38, "box"], [98, 23, 85, 41], [98, 24, 85, 42, "left"], [99, 8, 85, 46], [100, 10, 85, 46, "fileName"], [100, 18, 85, 46], [100, 20, 85, 46, "_jsxFileName"], [100, 32, 85, 46], [101, 10, 85, 46, "lineNumber"], [101, 20, 85, 46], [102, 10, 85, 46, "columnNumber"], [102, 22, 85, 46], [103, 8, 85, 46], [103, 15, 85, 53], [103, 16, 85, 54], [103, 18, 86, 9, "children"], [103, 26, 86, 17], [103, 41, 87, 8], [103, 45, 87, 8, "_jsxDevRuntime"], [103, 59, 87, 8], [103, 60, 87, 8, "jsxDEV"], [103, 66, 87, 8], [103, 68, 87, 9, "Text"], [103, 72, 87, 13], [104, 10, 87, 14, "style"], [104, 15, 87, 19], [104, 17, 87, 21, "styles"], [104, 23, 87, 27], [104, 24, 87, 28, "boxText"], [104, 31, 87, 36], [105, 10, 87, 36, "children"], [105, 18, 87, 36], [105, 20, 87, 38, "box"], [105, 23, 87, 41], [105, 24, 87, 42, "right"], [106, 8, 87, 47], [107, 10, 87, 47, "fileName"], [107, 18, 87, 47], [107, 20, 87, 47, "_jsxFileName"], [107, 32, 87, 47], [108, 10, 87, 47, "lineNumber"], [108, 20, 87, 47], [109, 10, 87, 47, "columnNumber"], [109, 22, 87, 47], [110, 8, 87, 47], [110, 15, 87, 54], [110, 16, 87, 55], [111, 6, 87, 55], [112, 8, 87, 55, "fileName"], [112, 16, 87, 55], [112, 18, 87, 55, "_jsxFileName"], [112, 30, 87, 55], [113, 8, 87, 55, "lineNumber"], [113, 18, 87, 55], [114, 8, 87, 55, "columnNumber"], [114, 20, 87, 55], [115, 6, 87, 55], [115, 13, 88, 12], [115, 14, 88, 13], [115, 29, 89, 6], [115, 33, 89, 6, "_jsxDevRuntime"], [115, 47, 89, 6], [115, 48, 89, 6, "jsxDEV"], [115, 54, 89, 6], [115, 56, 89, 7, "Text"], [115, 60, 89, 11], [116, 8, 89, 12, "style"], [116, 13, 89, 17], [116, 15, 89, 19, "styles"], [116, 21, 89, 25], [116, 22, 89, 26, "boxText"], [116, 29, 89, 34], [117, 8, 89, 34, "children"], [117, 16, 89, 34], [117, 18, 89, 36, "box"], [117, 21, 89, 39], [117, 22, 89, 40, "bottom"], [118, 6, 89, 46], [119, 8, 89, 46, "fileName"], [119, 16, 89, 46], [119, 18, 89, 46, "_jsxFileName"], [119, 30, 89, 46], [120, 8, 89, 46, "lineNumber"], [120, 18, 89, 46], [121, 8, 89, 46, "columnNumber"], [121, 20, 89, 46], [122, 6, 89, 46], [122, 13, 89, 53], [122, 14, 89, 54], [123, 4, 89, 54], [124, 6, 89, 54, "fileName"], [124, 14, 89, 54], [124, 16, 89, 54, "_jsxFileName"], [124, 28, 89, 54], [125, 6, 89, 54, "lineNumber"], [125, 16, 89, 54], [126, 6, 89, 54, "columnNumber"], [126, 18, 89, 54], [127, 4, 89, 54], [127, 11, 90, 10], [127, 12, 90, 11], [128, 2, 92, 0], [129, 2, 94, 0], [129, 6, 94, 6, "styles"], [129, 12, 94, 12], [129, 15, 94, 15, "StyleSheet"], [129, 25, 94, 25], [129, 26, 94, 26, "create"], [129, 32, 94, 32], [129, 33, 94, 33], [130, 4, 95, 2, "row"], [130, 7, 95, 5], [130, 9, 95, 7], [131, 6, 96, 4, "flexDirection"], [131, 19, 96, 17], [131, 21, 96, 19], [131, 26, 96, 24], [132, 6, 97, 4, "alignItems"], [132, 16, 97, 14], [132, 18, 97, 16], [132, 26, 97, 24], [133, 6, 98, 4, "justifyContent"], [133, 20, 98, 18], [133, 22, 98, 20], [134, 4, 99, 2], [134, 5, 99, 3], [135, 4, 100, 2, "margin<PERSON>abel"], [135, 15, 100, 13], [135, 17, 100, 15], [136, 6, 101, 4, "width"], [136, 11, 101, 9], [136, 13, 101, 11], [137, 4, 102, 2], [137, 5, 102, 3], [138, 4, 103, 2, "label"], [138, 9, 103, 7], [138, 11, 103, 9], [139, 6, 104, 4, "fontSize"], [139, 14, 104, 12], [139, 16, 104, 14], [139, 18, 104, 16], [140, 6, 105, 4, "color"], [140, 11, 105, 9], [140, 13, 105, 11], [140, 29, 105, 27], [141, 6, 106, 4, "marginLeft"], [141, 16, 106, 14], [141, 18, 106, 16], [141, 19, 106, 17], [142, 6, 107, 4, "flex"], [142, 10, 107, 8], [142, 12, 107, 10], [142, 13, 107, 11], [143, 6, 108, 4, "textAlign"], [143, 15, 108, 13], [143, 17, 108, 15], [143, 23, 108, 21], [144, 6, 109, 4, "top"], [144, 9, 109, 7], [144, 11, 109, 9], [144, 12, 109, 10], [145, 4, 110, 2], [145, 5, 110, 3], [146, 4, 111, 2, "innerText"], [146, 13, 111, 11], [146, 15, 111, 13], [147, 6, 112, 4, "color"], [147, 11, 112, 9], [147, 13, 112, 11], [147, 21, 112, 19], [148, 6, 113, 4, "fontSize"], [148, 14, 113, 12], [148, 16, 113, 14], [148, 18, 113, 16], [149, 6, 114, 4, "textAlign"], [149, 15, 114, 13], [149, 17, 114, 15], [149, 25, 114, 23], [150, 6, 115, 4, "width"], [150, 11, 115, 9], [150, 13, 115, 11], [151, 4, 116, 2], [151, 5, 116, 3], [152, 4, 117, 2, "box"], [152, 7, 117, 5], [152, 9, 117, 7], [153, 6, 118, 4, "borderWidth"], [153, 17, 118, 15], [153, 19, 118, 17], [153, 20, 118, 18], [154, 6, 119, 4, "borderColor"], [154, 17, 119, 15], [154, 19, 119, 17], [155, 4, 120, 2], [155, 5, 120, 3], [156, 4, 121, 2, "boxText"], [156, 11, 121, 9], [156, 13, 121, 11], [157, 6, 122, 4, "color"], [157, 11, 122, 9], [157, 13, 122, 11], [157, 20, 122, 18], [158, 6, 123, 4, "fontSize"], [158, 14, 123, 12], [158, 16, 123, 14], [158, 18, 123, 16], [159, 6, 124, 4, "marginHorizontal"], [159, 22, 124, 20], [159, 24, 124, 22], [159, 25, 124, 23], [160, 6, 125, 4, "marginVertical"], [160, 20, 125, 18], [160, 22, 125, 20], [160, 23, 125, 21], [161, 6, 126, 4, "textAlign"], [161, 15, 126, 13], [161, 17, 126, 15], [162, 4, 127, 2], [163, 2, 128, 0], [163, 3, 128, 1], [163, 4, 128, 2], [164, 2, 128, 3], [164, 6, 128, 3, "_default"], [164, 14, 128, 3], [164, 17, 128, 3, "exports"], [164, 24, 128, 3], [164, 25, 128, 3, "default"], [164, 32, 128, 3], [164, 35, 130, 15, "BoxInspector"], [164, 47, 130, 27], [165, 0, 130, 27], [165, 3]], "functionMap": {"names": ["<global>", "BoxInspector", "BoxContainer"], "mappings": "AAA;ACqC;CDmB;AEc;CFqB"}}, "type": "js/module"}]}