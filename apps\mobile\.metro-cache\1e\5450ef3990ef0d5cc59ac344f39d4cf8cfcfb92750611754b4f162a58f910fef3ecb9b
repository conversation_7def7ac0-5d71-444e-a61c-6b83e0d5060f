{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 123}, "end": {"line": 7, "column": 85, "index": 208}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 209}, "end": {"line": 13, "column": 22, "index": 319}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 320}, "end": {"line": 14, "column": 109, "index": 429}}], "key": "isYRR8A/48f51eJ8g98DW0FjA7o=", "exportNames": ["*"]}}, {"name": "./requireNativeModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 431}, "end": {"line": 16, "column": 60, "index": 491}}], "key": "6ybEgv0o6wMlGqg8lKStnnMYUaQ=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  // Copyright © 2024 650 Industries.\n\n  /// <reference path=\"ts-declarations/NativeComponentRegistry.d.ts\" />\n\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.requireNativeViewManager = requireNativeViewManager;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _react = require(_dependencyMap[6], \"react\");\n  var _reactNative = require(_dependencyMap[7], \"react-native\");\n  var _NativeComponentRegistry = require(_dependencyMap[8], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var _requireNativeModule = require(_dependencyMap[9], \"./requireNativeModule\");\n  var _jsxDevRuntime = require(_dependencyMap[10], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\apps\\\\mobile\\\\node_modules\\\\expo-modules-core\\\\src\\\\NativeViewManagerAdapter.native.tsx\"; // To make the transition from React Native's `requireNativeComponent` to Expo's\n  // `requireNativeViewManager` as easy as possible, `requireNativeViewManager` is a drop-in\n  // replacement for `requireNativeComponent`.\n  //\n  // For each view manager, we create a wrapper component that accepts all the props available to\n  // the author of the universal module. This wrapper component splits the props into two sets: props\n  // passed to React Native's View (ex: style, testID) and custom view props, which are passed to the\n  // adapter view component in a prop called `proxiedProperties`.\n  /**\n   * A map that caches registered native components.\n   */\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var nativeComponentsCache = new Map();\n\n  // TODO(@kitten): Optimally, this is defined on ExpoGlobal, but we treat `__expo_app_identifier__` as internal\n\n  /**\n   * Requires a React Native component using the static view config from an Expo module.\n   */\n  function requireNativeComponent(moduleName, viewName) {\n    var appIdentifier = globalThis.expo?.['__expo_app_identifier__'] ?? '';\n    var viewNameSuffix = appIdentifier ? `_${appIdentifier}` : '';\n    var nativeViewName = viewName ? `ViewManagerAdapter_${moduleName}_${viewName}${viewNameSuffix}` : `ViewManagerAdapter_${moduleName}${viewNameSuffix}`;\n    return (0, _NativeComponentRegistry.get)(nativeViewName, () => {\n      var expoViewConfig = globalThis.expo?.getViewConfig(moduleName, viewName);\n      if (!expoViewConfig) {\n        console.warn('Unable to get the view config for %s from module &s', viewName ?? 'default view', moduleName);\n      }\n      return {\n        uiViewClassName: nativeViewName,\n        ...expoViewConfig\n      };\n    });\n  }\n\n  /**\n   * Requires a React Native component from cache if possible. This prevents\n   * \"Tried to register two views with the same name\" errors on fast refresh, but\n   * also when there are multiple versions of the same package with native component.\n   */\n  function requireCachedNativeComponent(moduleName, viewName) {\n    var cacheKey = `${moduleName}_${viewName}`;\n    var cachedNativeComponent = nativeComponentsCache.get(cacheKey);\n    if (!cachedNativeComponent) {\n      var nativeComponent = requireNativeComponent(moduleName, viewName);\n      nativeComponentsCache.set(cacheKey, nativeComponent);\n      return nativeComponent;\n    }\n    return cachedNativeComponent;\n  }\n\n  /**\n   * A drop-in replacement for `requireNativeComponent`.\n   */\n  function requireNativeViewManager(moduleName, viewName) {\n    var viewManagersMetadata = _reactNative.NativeModules.NativeUnimoduleProxy.viewManagersMetadata;\n    var viewManagerConfig = viewManagersMetadata?.[moduleName];\n    if (__DEV__ && !viewManagerConfig) {\n      var exportedViewManagerNames = Object.keys(viewManagersMetadata).join(', ');\n      console.warn(`The native view manager for module(${moduleName}) ${viewName ? ` required by name (${viewName})` : ''}) from NativeViewManagerAdapter isn't exported by expo-modules-core. Views of this type may not render correctly. Exported view managers: [${exportedViewManagerNames}].`);\n    }\n    var ReactNativeComponent = requireCachedNativeComponent(moduleName, viewName);\n    var NativeComponent = /*#__PURE__*/function (_PureComponent) {\n      function NativeComponent() {\n        var _this;\n        (0, _classCallCheck2.default)(this, NativeComponent);\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        _this = _callSuper(this, NativeComponent, [...args]);\n        _this.nativeRef = /*#__PURE__*/(0, _react.createRef)();\n        // This will be accessed from native when the prototype functions are called,\n        // in order to find the associated native view.\n        _this.nativeTag = null;\n        return _this;\n      }\n      (0, _inherits2.default)(NativeComponent, _PureComponent);\n      return (0, _createClass2.default)(NativeComponent, [{\n        key: \"componentDidMount\",\n        value: function componentDidMount() {\n          this.nativeTag = (0, _reactNative.findNodeHandle)(this.nativeRef.current);\n        }\n      }, {\n        key: \"render\",\n        value: function render() {\n          return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ReactNativeComponent, {\n            ...this.props,\n            ref: this.nativeRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 14\n          }, this);\n        }\n      }]);\n    }(_react.PureComponent);\n    NativeComponent.displayName = viewName ? viewName : moduleName;\n    try {\n      var nativeModule = (0, _requireNativeModule.requireNativeModule)(moduleName);\n      var nativeViewPrototype = nativeModule.ViewPrototypes[viewName ? `${moduleName}_${viewName}` : moduleName];\n      if (nativeViewPrototype) {\n        // Assign native view functions to the component prototype, so they can be accessed from the ref.\n        Object.assign(NativeComponent.prototype, nativeViewPrototype);\n      }\n    } catch {\n      // `requireNativeModule` may throw an error when the native module cannot be found.\n      // In some tests we don't mock the entire modules, but we do want to mock native views. For now,\n      // until we still have to support the legacy modules proxy and don't have better ways to mock,\n      // let's just gracefully skip assigning the prototype functions.\n      // See: https://github.com/expo/expo/blob/main/packages/expo-modules-core/src/__tests__/NativeViewManagerAdapter-test.native.tsx\n    }\n    return NativeComponent;\n  }\n});", "lineCount": 137, "map": [[2, 2, 1, 0], [4, 2, 3, 0], [6, 2, 5, 0], [6, 14, 5, 12], [8, 2, 5, 13], [8, 6, 5, 13, "_interopRequireDefault"], [8, 28, 5, 13], [8, 31, 5, 13, "require"], [8, 38, 5, 13], [8, 39, 5, 13, "_dependencyMap"], [8, 53, 5, 13], [9, 2, 5, 13, "Object"], [9, 8, 5, 13], [9, 9, 5, 13, "defineProperty"], [9, 23, 5, 13], [9, 24, 5, 13, "exports"], [9, 31, 5, 13], [10, 4, 5, 13, "value"], [10, 9, 5, 13], [11, 2, 5, 13], [12, 2, 5, 13, "exports"], [12, 9, 5, 13], [12, 10, 5, 13, "requireNativeViewManager"], [12, 34, 5, 13], [12, 37, 5, 13, "requireNativeViewManager"], [12, 61, 5, 13], [13, 2, 5, 13], [13, 6, 5, 13, "_classCallCheck2"], [13, 22, 5, 13], [13, 25, 5, 13, "_interopRequireDefault"], [13, 47, 5, 13], [13, 48, 5, 13, "require"], [13, 55, 5, 13], [13, 56, 5, 13, "_dependencyMap"], [13, 70, 5, 13], [14, 2, 5, 13], [14, 6, 5, 13, "_createClass2"], [14, 19, 5, 13], [14, 22, 5, 13, "_interopRequireDefault"], [14, 44, 5, 13], [14, 45, 5, 13, "require"], [14, 52, 5, 13], [14, 53, 5, 13, "_dependencyMap"], [14, 67, 5, 13], [15, 2, 5, 13], [15, 6, 5, 13, "_possibleConstructorReturn2"], [15, 33, 5, 13], [15, 36, 5, 13, "_interopRequireDefault"], [15, 58, 5, 13], [15, 59, 5, 13, "require"], [15, 66, 5, 13], [15, 67, 5, 13, "_dependencyMap"], [15, 81, 5, 13], [16, 2, 5, 13], [16, 6, 5, 13, "_getPrototypeOf2"], [16, 22, 5, 13], [16, 25, 5, 13, "_interopRequireDefault"], [16, 47, 5, 13], [16, 48, 5, 13, "require"], [16, 55, 5, 13], [16, 56, 5, 13, "_dependencyMap"], [16, 70, 5, 13], [17, 2, 5, 13], [17, 6, 5, 13, "_inherits2"], [17, 16, 5, 13], [17, 19, 5, 13, "_interopRequireDefault"], [17, 41, 5, 13], [17, 42, 5, 13, "require"], [17, 49, 5, 13], [17, 50, 5, 13, "_dependencyMap"], [17, 64, 5, 13], [18, 2, 7, 0], [18, 6, 7, 0, "_react"], [18, 12, 7, 0], [18, 15, 7, 0, "require"], [18, 22, 7, 0], [18, 23, 7, 0, "_dependencyMap"], [18, 37, 7, 0], [19, 2, 8, 0], [19, 6, 8, 0, "_reactNative"], [19, 18, 8, 0], [19, 21, 8, 0, "require"], [19, 28, 8, 0], [19, 29, 8, 0, "_dependencyMap"], [19, 43, 8, 0], [20, 2, 14, 0], [20, 6, 14, 0, "_NativeComponentRegistry"], [20, 30, 14, 0], [20, 33, 14, 0, "require"], [20, 40, 14, 0], [20, 41, 14, 0, "_dependencyMap"], [20, 55, 14, 0], [21, 2, 16, 0], [21, 6, 16, 0, "_requireNativeModule"], [21, 26, 16, 0], [21, 29, 16, 0, "require"], [21, 36, 16, 0], [21, 37, 16, 0, "_dependencyMap"], [21, 51, 16, 0], [22, 2, 16, 60], [22, 6, 16, 60, "_jsxDevRuntime"], [22, 20, 16, 60], [22, 23, 16, 60, "require"], [22, 30, 16, 60], [22, 31, 16, 60, "_dependencyMap"], [22, 45, 16, 60], [23, 2, 16, 60], [23, 6, 16, 60, "_jsxFileName"], [23, 18, 16, 60], [23, 146, 18, 0], [24, 2, 19, 0], [25, 2, 20, 0], [26, 2, 21, 0], [27, 2, 22, 0], [28, 2, 23, 0], [29, 2, 24, 0], [30, 2, 25, 0], [31, 2, 27, 0], [32, 0, 28, 0], [33, 0, 29, 0], [34, 2, 27, 0], [34, 11, 27, 0, "_callSuper"], [34, 22, 27, 0, "t"], [34, 23, 27, 0], [34, 25, 27, 0, "o"], [34, 26, 27, 0], [34, 28, 27, 0, "e"], [34, 29, 27, 0], [34, 40, 27, 0, "o"], [34, 41, 27, 0], [34, 48, 27, 0, "_getPrototypeOf2"], [34, 64, 27, 0], [34, 65, 27, 0, "default"], [34, 72, 27, 0], [34, 74, 27, 0, "o"], [34, 75, 27, 0], [34, 82, 27, 0, "_possibleConstructorReturn2"], [34, 109, 27, 0], [34, 110, 27, 0, "default"], [34, 117, 27, 0], [34, 119, 27, 0, "t"], [34, 120, 27, 0], [34, 122, 27, 0, "_isNativeReflectConstruct"], [34, 147, 27, 0], [34, 152, 27, 0, "Reflect"], [34, 159, 27, 0], [34, 160, 27, 0, "construct"], [34, 169, 27, 0], [34, 170, 27, 0, "o"], [34, 171, 27, 0], [34, 173, 27, 0, "e"], [34, 174, 27, 0], [34, 186, 27, 0, "_getPrototypeOf2"], [34, 202, 27, 0], [34, 203, 27, 0, "default"], [34, 210, 27, 0], [34, 212, 27, 0, "t"], [34, 213, 27, 0], [34, 215, 27, 0, "constructor"], [34, 226, 27, 0], [34, 230, 27, 0, "o"], [34, 231, 27, 0], [34, 232, 27, 0, "apply"], [34, 237, 27, 0], [34, 238, 27, 0, "t"], [34, 239, 27, 0], [34, 241, 27, 0, "e"], [34, 242, 27, 0], [35, 2, 27, 0], [35, 11, 27, 0, "_isNativeReflectConstruct"], [35, 37, 27, 0], [35, 51, 27, 0, "t"], [35, 52, 27, 0], [35, 56, 27, 0, "Boolean"], [35, 63, 27, 0], [35, 64, 27, 0, "prototype"], [35, 73, 27, 0], [35, 74, 27, 0, "valueOf"], [35, 81, 27, 0], [35, 82, 27, 0, "call"], [35, 86, 27, 0], [35, 87, 27, 0, "Reflect"], [35, 94, 27, 0], [35, 95, 27, 0, "construct"], [35, 104, 27, 0], [35, 105, 27, 0, "Boolean"], [35, 112, 27, 0], [35, 145, 27, 0, "t"], [35, 146, 27, 0], [35, 159, 27, 0, "_isNativeReflectConstruct"], [35, 184, 27, 0], [35, 196, 27, 0, "_isNativeReflectConstruct"], [35, 197, 27, 0], [35, 210, 27, 0, "t"], [35, 211, 27, 0], [36, 2, 30, 0], [36, 6, 30, 6, "nativeComponentsCache"], [36, 27, 30, 27], [36, 30, 30, 30], [36, 34, 30, 34, "Map"], [36, 37, 30, 37], [36, 38, 30, 66], [36, 39, 30, 67], [38, 2, 32, 0], [40, 2, 48, 0], [41, 0, 49, 0], [42, 0, 50, 0], [43, 2, 51, 0], [43, 11, 51, 9, "requireNativeComponent"], [43, 33, 51, 31, "requireNativeComponent"], [43, 34, 52, 2, "moduleName"], [43, 44, 52, 20], [43, 46, 53, 2, "viewName"], [43, 54, 53, 19], [43, 56, 54, 24], [44, 4, 55, 2], [44, 8, 55, 8, "appIdentifier"], [44, 21, 55, 21], [44, 24, 55, 24, "globalThis"], [44, 34, 55, 34], [44, 35, 55, 35, "expo"], [44, 39, 55, 39], [44, 42, 55, 42], [44, 67, 55, 67], [44, 68, 55, 68], [44, 72, 55, 72], [44, 74, 55, 74], [45, 4, 56, 2], [45, 8, 56, 8, "viewNameSuffix"], [45, 22, 56, 22], [45, 25, 56, 25, "appIdentifier"], [45, 38, 56, 38], [45, 41, 56, 41], [45, 45, 56, 45, "appIdentifier"], [45, 58, 56, 58], [45, 60, 56, 60], [45, 63, 56, 63], [45, 65, 56, 65], [46, 4, 58, 2], [46, 8, 58, 8, "nativeViewName"], [46, 22, 58, 22], [46, 25, 58, 25, "viewName"], [46, 33, 58, 33], [46, 36, 59, 6], [46, 58, 59, 28, "moduleName"], [46, 68, 59, 38], [46, 72, 59, 42, "viewName"], [46, 80, 59, 50], [46, 83, 59, 53, "viewNameSuffix"], [46, 97, 59, 67], [46, 99, 59, 69], [46, 102, 60, 6], [46, 124, 60, 28, "moduleName"], [46, 134, 60, 38], [46, 137, 60, 41, "viewNameSuffix"], [46, 151, 60, 55], [46, 153, 60, 57], [47, 4, 62, 2], [47, 11, 62, 9], [47, 15, 62, 9, "componentRegistryGet"], [47, 43, 62, 29], [47, 45, 62, 37, "nativeViewName"], [47, 59, 62, 51], [47, 61, 62, 53], [47, 67, 62, 59], [48, 6, 63, 4], [48, 10, 63, 10, "expoViewConfig"], [48, 24, 63, 24], [48, 27, 63, 27, "globalThis"], [48, 37, 63, 37], [48, 38, 63, 38, "expo"], [48, 42, 63, 42], [48, 44, 63, 44, "getViewConfig"], [48, 57, 63, 57], [48, 58, 63, 58, "moduleName"], [48, 68, 63, 68], [48, 70, 63, 70, "viewName"], [48, 78, 63, 78], [48, 79, 63, 79], [49, 6, 65, 4], [49, 10, 65, 8], [49, 11, 65, 9, "expoViewConfig"], [49, 25, 65, 23], [49, 27, 65, 25], [50, 8, 66, 6, "console"], [50, 15, 66, 13], [50, 16, 66, 14, "warn"], [50, 20, 66, 18], [50, 21, 67, 8], [50, 74, 67, 61], [50, 76, 68, 8, "viewName"], [50, 84, 68, 16], [50, 88, 68, 20], [50, 102, 68, 34], [50, 104, 69, 8, "moduleName"], [50, 114, 70, 6], [50, 115, 70, 7], [51, 6, 71, 4], [52, 6, 73, 4], [52, 13, 73, 11], [53, 8, 74, 6, "uiViewClassName"], [53, 23, 74, 21], [53, 25, 74, 23, "nativeViewName"], [53, 39, 74, 37], [54, 8, 75, 6], [54, 11, 75, 9, "expoViewConfig"], [55, 6, 76, 4], [55, 7, 76, 5], [56, 4, 77, 2], [56, 5, 77, 3], [56, 6, 77, 4], [57, 2, 78, 0], [59, 2, 80, 0], [60, 0, 81, 0], [61, 0, 82, 0], [62, 0, 83, 0], [63, 0, 84, 0], [64, 2, 85, 0], [64, 11, 85, 9, "requireCachedNativeComponent"], [64, 39, 85, 37, "requireCachedNativeComponent"], [64, 40, 86, 2, "moduleName"], [64, 50, 86, 20], [64, 52, 87, 2, "viewName"], [64, 60, 87, 19], [64, 62, 88, 24], [65, 4, 89, 2], [65, 8, 89, 8, "cache<PERSON>ey"], [65, 16, 89, 16], [65, 19, 89, 19], [65, 22, 89, 22, "moduleName"], [65, 32, 89, 32], [65, 36, 89, 36, "viewName"], [65, 44, 89, 44], [65, 46, 89, 46], [66, 4, 90, 2], [66, 8, 90, 8, "cachedNativeComponent"], [66, 29, 90, 29], [66, 32, 90, 32, "nativeComponentsCache"], [66, 53, 90, 53], [66, 54, 90, 54, "get"], [66, 57, 90, 57], [66, 58, 90, 58, "cache<PERSON>ey"], [66, 66, 90, 66], [66, 67, 90, 67], [67, 4, 92, 2], [67, 8, 92, 6], [67, 9, 92, 7, "cachedNativeComponent"], [67, 30, 92, 28], [67, 32, 92, 30], [68, 6, 93, 4], [68, 10, 93, 10, "nativeComponent"], [68, 25, 93, 25], [68, 28, 93, 28, "requireNativeComponent"], [68, 50, 93, 50], [68, 51, 93, 58, "moduleName"], [68, 61, 93, 68], [68, 63, 93, 70, "viewName"], [68, 71, 93, 78], [68, 72, 93, 79], [69, 6, 94, 4, "nativeComponentsCache"], [69, 27, 94, 25], [69, 28, 94, 26, "set"], [69, 31, 94, 29], [69, 32, 94, 30, "cache<PERSON>ey"], [69, 40, 94, 38], [69, 42, 94, 40, "nativeComponent"], [69, 57, 94, 55], [69, 58, 94, 56], [70, 6, 95, 4], [70, 13, 95, 11, "nativeComponent"], [70, 28, 95, 26], [71, 4, 96, 2], [72, 4, 97, 2], [72, 11, 97, 9, "cachedNativeComponent"], [72, 32, 97, 30], [73, 2, 98, 0], [75, 2, 100, 0], [76, 0, 101, 0], [77, 0, 102, 0], [78, 2, 103, 7], [78, 11, 103, 16, "requireNativeViewManager"], [78, 35, 103, 40, "requireNativeViewManager"], [78, 36, 104, 2, "moduleName"], [78, 46, 104, 20], [78, 48, 105, 2, "viewName"], [78, 56, 105, 19], [78, 58, 106, 20], [79, 4, 107, 2], [79, 8, 107, 10, "viewManagersMetadata"], [79, 28, 107, 30], [79, 31, 107, 35, "NativeModules"], [79, 57, 107, 48], [79, 58, 107, 49, "NativeUnimoduleProxy"], [79, 78, 107, 69], [79, 79, 107, 10, "viewManagersMetadata"], [79, 99, 107, 30], [80, 4, 109, 2], [80, 8, 109, 8, "viewManagerConfig"], [80, 25, 109, 25], [80, 28, 109, 28, "viewManagersMetadata"], [80, 48, 109, 48], [80, 51, 109, 51, "moduleName"], [80, 61, 109, 61], [80, 62, 109, 62], [81, 4, 111, 2], [81, 8, 111, 6, "__DEV__"], [81, 15, 111, 13], [81, 19, 111, 17], [81, 20, 111, 18, "viewManagerConfig"], [81, 37, 111, 35], [81, 39, 111, 37], [82, 6, 112, 4], [82, 10, 112, 10, "exportedViewManagerNames"], [82, 34, 112, 34], [82, 37, 112, 37, "Object"], [82, 43, 112, 43], [82, 44, 112, 44, "keys"], [82, 48, 112, 48], [82, 49, 112, 49, "viewManagersMetadata"], [82, 69, 112, 69], [82, 70, 112, 70], [82, 71, 112, 71, "join"], [82, 75, 112, 75], [82, 76, 112, 76], [82, 80, 112, 80], [82, 81, 112, 81], [83, 6, 113, 4, "console"], [83, 13, 113, 11], [83, 14, 113, 12, "warn"], [83, 18, 113, 16], [83, 19, 114, 6], [83, 57, 114, 44, "moduleName"], [83, 67, 114, 54], [83, 72, 114, 59, "viewName"], [83, 80, 114, 67], [83, 83, 114, 70], [83, 105, 114, 92, "viewName"], [83, 113, 114, 100], [83, 116, 114, 103], [83, 119, 114, 106], [83, 121, 114, 108], [83, 263, 114, 250, "exportedViewManagerNames"], [83, 287, 114, 274], [83, 291, 115, 4], [83, 292, 115, 5], [84, 4, 116, 2], [85, 4, 118, 2], [85, 8, 118, 8, "ReactNativeComponent"], [85, 28, 118, 28], [85, 31, 118, 31, "requireCachedNativeComponent"], [85, 59, 118, 59], [85, 60, 118, 60, "moduleName"], [85, 70, 118, 70], [85, 72, 118, 72, "viewName"], [85, 80, 118, 80], [85, 81, 118, 81], [86, 4, 118, 82], [86, 8, 120, 8, "NativeComponent"], [86, 23, 120, 23], [86, 49, 120, 23, "_PureComponent"], [86, 63, 120, 23], [87, 6, 120, 23], [87, 15, 120, 23, "NativeComponent"], [87, 31, 120, 23], [88, 8, 120, 23], [88, 12, 120, 23, "_this"], [88, 17, 120, 23], [89, 8, 120, 23], [89, 12, 120, 23, "_classCallCheck2"], [89, 28, 120, 23], [89, 29, 120, 23, "default"], [89, 36, 120, 23], [89, 44, 120, 23, "NativeComponent"], [89, 59, 120, 23], [90, 8, 120, 23], [90, 17, 120, 23, "_len"], [90, 21, 120, 23], [90, 24, 120, 23, "arguments"], [90, 33, 120, 23], [90, 34, 120, 23, "length"], [90, 40, 120, 23], [90, 42, 120, 23, "args"], [90, 46, 120, 23], [90, 53, 120, 23, "Array"], [90, 58, 120, 23], [90, 59, 120, 23, "_len"], [90, 63, 120, 23], [90, 66, 120, 23, "_key"], [90, 70, 120, 23], [90, 76, 120, 23, "_key"], [90, 80, 120, 23], [90, 83, 120, 23, "_len"], [90, 87, 120, 23], [90, 89, 120, 23, "_key"], [90, 93, 120, 23], [91, 10, 120, 23, "args"], [91, 14, 120, 23], [91, 15, 120, 23, "_key"], [91, 19, 120, 23], [91, 23, 120, 23, "arguments"], [91, 32, 120, 23], [91, 33, 120, 23, "_key"], [91, 37, 120, 23], [92, 8, 120, 23], [93, 8, 120, 23, "_this"], [93, 13, 120, 23], [93, 16, 120, 23, "_callSuper"], [93, 26, 120, 23], [93, 33, 120, 23, "NativeComponent"], [93, 48, 120, 23], [93, 54, 120, 23, "args"], [93, 58, 120, 23], [94, 8, 120, 23, "_this"], [94, 13, 120, 23], [94, 14, 123, 4, "nativeRef"], [94, 23, 123, 13], [94, 39, 123, 16], [94, 43, 123, 16, "createRef"], [94, 59, 123, 25], [94, 61, 123, 53], [94, 62, 123, 54], [95, 8, 125, 4], [96, 8, 126, 4], [97, 8, 126, 4, "_this"], [97, 13, 126, 4], [97, 14, 127, 4, "nativeTag"], [97, 23, 127, 13], [97, 26, 127, 31], [97, 30, 127, 35], [98, 8, 127, 35], [98, 15, 127, 35, "_this"], [98, 20, 127, 35], [99, 6, 127, 35], [100, 6, 127, 35], [100, 10, 127, 35, "_inherits2"], [100, 20, 127, 35], [100, 21, 127, 35, "default"], [100, 28, 127, 35], [100, 30, 127, 35, "NativeComponent"], [100, 45, 127, 35], [100, 47, 127, 35, "_PureComponent"], [100, 61, 127, 35], [101, 6, 127, 35], [101, 17, 127, 35, "_createClass2"], [101, 30, 127, 35], [101, 31, 127, 35, "default"], [101, 38, 127, 35], [101, 40, 127, 35, "NativeComponent"], [101, 55, 127, 35], [102, 8, 127, 35, "key"], [102, 11, 127, 35], [103, 8, 127, 35, "value"], [103, 13, 127, 35], [103, 15, 129, 4], [103, 24, 129, 4, "componentDidMount"], [103, 41, 129, 21, "componentDidMount"], [103, 42, 129, 21], [103, 44, 129, 30], [104, 10, 130, 6], [104, 14, 130, 10], [104, 15, 130, 11, "nativeTag"], [104, 24, 130, 20], [104, 27, 130, 23], [104, 31, 130, 23, "findNodeHandle"], [104, 58, 130, 37], [104, 60, 130, 38], [104, 64, 130, 42], [104, 65, 130, 43, "nativeRef"], [104, 74, 130, 52], [104, 75, 130, 53, "current"], [104, 82, 130, 60], [104, 83, 130, 61], [105, 8, 131, 4], [106, 6, 131, 5], [107, 8, 131, 5, "key"], [107, 11, 131, 5], [108, 8, 131, 5, "value"], [108, 13, 131, 5], [108, 15, 133, 4], [108, 24, 133, 4, "render"], [108, 30, 133, 10, "render"], [108, 31, 133, 10], [108, 33, 133, 13], [109, 10, 134, 6], [109, 30, 134, 13], [109, 34, 134, 13, "_jsxDevRuntime"], [109, 48, 134, 13], [109, 49, 134, 13, "jsxDEV"], [109, 55, 134, 13], [109, 57, 134, 14, "ReactNativeComponent"], [109, 77, 134, 34], [110, 12, 134, 34], [110, 15, 134, 39], [110, 19, 134, 43], [110, 20, 134, 44, "props"], [110, 25, 134, 49], [111, 12, 134, 51, "ref"], [111, 15, 134, 54], [111, 17, 134, 56], [111, 21, 134, 60], [111, 22, 134, 61, "nativeRef"], [112, 10, 134, 71], [113, 12, 134, 71, "fileName"], [113, 20, 134, 71], [113, 22, 134, 71, "_jsxFileName"], [113, 34, 134, 71], [114, 12, 134, 71, "lineNumber"], [114, 22, 134, 71], [115, 12, 134, 71, "columnNumber"], [115, 24, 134, 71], [116, 10, 134, 71], [116, 17, 134, 73], [116, 18, 134, 74], [117, 8, 135, 4], [118, 6, 135, 5], [119, 4, 135, 5], [119, 6, 120, 32, "PureComponent"], [119, 26, 120, 45], [120, 4, 120, 8, "NativeComponent"], [120, 19, 120, 23], [120, 20, 121, 11, "displayName"], [120, 31, 121, 22], [120, 34, 121, 25, "viewName"], [120, 42, 121, 33], [120, 45, 121, 36, "viewName"], [120, 53, 121, 44], [120, 56, 121, 47, "moduleName"], [120, 66, 121, 57], [121, 4, 138, 2], [121, 8, 138, 6], [122, 6, 139, 4], [122, 10, 139, 10, "nativeModule"], [122, 22, 139, 22], [122, 25, 139, 25], [122, 29, 139, 25, "requireNativeModule"], [122, 69, 139, 44], [122, 71, 139, 45, "moduleName"], [122, 81, 139, 55], [122, 82, 139, 56], [123, 6, 140, 4], [123, 10, 140, 10, "nativeViewPrototype"], [123, 29, 140, 29], [123, 32, 141, 6, "nativeModule"], [123, 44, 141, 18], [123, 45, 141, 19, "ViewPrototypes"], [123, 59, 141, 33], [123, 60, 141, 34, "viewName"], [123, 68, 141, 42], [123, 71, 141, 45], [123, 74, 141, 48, "moduleName"], [123, 84, 141, 58], [123, 88, 141, 62, "viewName"], [123, 96, 141, 70], [123, 98, 141, 72], [123, 101, 141, 75, "moduleName"], [123, 111, 141, 85], [123, 112, 141, 86], [124, 6, 142, 4], [124, 10, 142, 8, "nativeViewPrototype"], [124, 29, 142, 27], [124, 31, 142, 29], [125, 8, 143, 6], [126, 8, 144, 6, "Object"], [126, 14, 144, 12], [126, 15, 144, 13, "assign"], [126, 21, 144, 19], [126, 22, 144, 20, "NativeComponent"], [126, 37, 144, 35], [126, 38, 144, 36, "prototype"], [126, 47, 144, 45], [126, 49, 144, 47, "nativeViewPrototype"], [126, 68, 144, 66], [126, 69, 144, 67], [127, 6, 145, 4], [128, 4, 146, 2], [128, 5, 146, 3], [128, 6, 146, 4], [128, 12, 146, 10], [129, 6, 147, 4], [130, 6, 148, 4], [131, 6, 149, 4], [132, 6, 150, 4], [133, 6, 151, 4], [134, 4, 151, 4], [135, 4, 154, 2], [135, 11, 154, 9, "NativeComponent"], [135, 26, 154, 24], [136, 2, 155, 0], [137, 0, 155, 1], [137, 3]], "functionMap": {"names": ["<global>", "requireNativeComponent", "componentRegistryGet$argument_1", "requireCachedNativeComponent", "requireNativeViewManager", "NativeComponent", "NativeComponent#componentDidMount", "NativeComponent#render"], "mappings": "AAA;ACkD;qDCW;GDe;CDC;AGO;CHa;OIK;ECiB;ICS;KDE;IEE;KFE;GDC;CJmB"}}, "type": "js/module"}]}