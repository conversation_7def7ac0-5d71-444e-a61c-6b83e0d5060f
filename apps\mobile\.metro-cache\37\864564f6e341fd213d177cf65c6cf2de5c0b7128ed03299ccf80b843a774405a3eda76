{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.URLSearchParams = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var URLSearchParams = exports.URLSearchParams = /*#__PURE__*/function () {\n    function URLSearchParams(params) {\n      (0, _classCallCheck2.default)(this, URLSearchParams);\n      this._searchParams = [];\n      if (typeof params === 'object') {\n        Object.keys(params).forEach(key => this.append(key, params[key]));\n      }\n    }\n    return (0, _createClass2.default)(URLSearchParams, [{\n      key: \"append\",\n      value: function append(key, value) {\n        this._searchParams.push([key, value]);\n      }\n    }, {\n      key: \"delete\",\n      value: function _delete(name) {\n        throw new Error('URLSearchParams.delete is not implemented');\n      }\n    }, {\n      key: \"get\",\n      value: function get(name) {\n        throw new Error('URLSearchParams.get is not implemented');\n      }\n    }, {\n      key: \"getAll\",\n      value: function getAll(name) {\n        throw new Error('URLSearchParams.getAll is not implemented');\n      }\n    }, {\n      key: \"has\",\n      value: function has(name) {\n        throw new Error('URLSearchParams.has is not implemented');\n      }\n    }, {\n      key: \"set\",\n      value: function set(name, value) {\n        throw new Error('URLSearchParams.set is not implemented');\n      }\n    }, {\n      key: \"sort\",\n      value: function sort() {\n        throw new Error('URLSearchParams.sort is not implemented');\n      }\n    }, {\n      key: Symbol.iterator,\n      value: function () {\n        return this._searchParams[Symbol.iterator]();\n      }\n    }, {\n      key: \"toString\",\n      value: function toString() {\n        if (this._searchParams.length === 0) {\n          return '';\n        }\n        var last = this._searchParams.length - 1;\n        return this._searchParams.reduce((acc, curr, index) => {\n          return acc + encodeURIComponent(curr[0]) + '=' + encodeURIComponent(curr[1]) + (index === last ? '' : '&');\n        }, '');\n      }\n    }]);\n  }();\n});", "lineCount": 70, "map": [[9, 6, 13, 13, "URLSearchParams"], [9, 21, 13, 28], [9, 24, 13, 28, "exports"], [9, 31, 13, 28], [9, 32, 13, 28, "URLSearchParams"], [9, 47, 13, 28], [10, 4, 16, 2], [10, 13, 16, 2, "URLSearchParams"], [10, 29, 16, 14, "params"], [10, 35, 16, 45], [10, 37, 16, 47], [11, 6, 16, 47], [11, 10, 16, 47, "_classCallCheck2"], [11, 26, 16, 47], [11, 27, 16, 47, "default"], [11, 34, 16, 47], [11, 42, 16, 47, "URLSearchParams"], [11, 57, 16, 47], [12, 6, 16, 47], [12, 11, 14, 2, "_searchParams"], [12, 24, 14, 15], [12, 27, 14, 43], [12, 29, 14, 45], [13, 6, 17, 4], [13, 10, 17, 8], [13, 17, 17, 15, "params"], [13, 23, 17, 21], [13, 28, 17, 26], [13, 36, 17, 34], [13, 38, 17, 36], [14, 8, 18, 6, "Object"], [14, 14, 18, 12], [14, 15, 18, 13, "keys"], [14, 19, 18, 17], [14, 20, 18, 18, "params"], [14, 26, 18, 24], [14, 27, 18, 25], [14, 28, 18, 26, "for<PERSON>ach"], [14, 35, 18, 33], [14, 36, 18, 34, "key"], [14, 39, 18, 37], [14, 43, 18, 41], [14, 47, 18, 45], [14, 48, 18, 46, "append"], [14, 54, 18, 52], [14, 55, 18, 53, "key"], [14, 58, 18, 56], [14, 60, 18, 58, "params"], [14, 66, 18, 64], [14, 67, 18, 65, "key"], [14, 70, 18, 68], [14, 71, 18, 69], [14, 72, 18, 70], [14, 73, 18, 71], [15, 6, 19, 4], [16, 4, 20, 2], [17, 4, 20, 3], [17, 15, 20, 3, "_createClass2"], [17, 28, 20, 3], [17, 29, 20, 3, "default"], [17, 36, 20, 3], [17, 38, 20, 3, "URLSearchParams"], [17, 53, 20, 3], [18, 6, 20, 3, "key"], [18, 9, 20, 3], [19, 6, 20, 3, "value"], [19, 11, 20, 3], [19, 13, 22, 2], [19, 22, 22, 2, "append"], [19, 28, 22, 8, "append"], [19, 29, 22, 9, "key"], [19, 32, 22, 20], [19, 34, 22, 22, "value"], [19, 39, 22, 35], [19, 41, 22, 43], [20, 8, 23, 4], [20, 12, 23, 8], [20, 13, 23, 9, "_searchParams"], [20, 26, 23, 22], [20, 27, 23, 23, "push"], [20, 31, 23, 27], [20, 32, 23, 28], [20, 33, 23, 29, "key"], [20, 36, 23, 32], [20, 38, 23, 34, "value"], [20, 43, 23, 39], [20, 44, 23, 40], [20, 45, 23, 41], [21, 6, 24, 2], [22, 4, 24, 3], [23, 6, 24, 3, "key"], [23, 9, 24, 3], [24, 6, 24, 3, "value"], [24, 11, 24, 3], [24, 13, 26, 2], [24, 22, 26, 2, "delete"], [24, 29, 26, 8, "delete"], [24, 30, 26, 9, "name"], [24, 34, 26, 21], [24, 36, 26, 30], [25, 8, 27, 4], [25, 14, 27, 10], [25, 18, 27, 14, "Error"], [25, 23, 27, 19], [25, 24, 27, 20], [25, 67, 27, 63], [25, 68, 27, 64], [26, 6, 28, 2], [27, 4, 28, 3], [28, 6, 28, 3, "key"], [28, 9, 28, 3], [29, 6, 28, 3, "value"], [29, 11, 28, 3], [29, 13, 30, 2], [29, 22, 30, 2, "get"], [29, 25, 30, 5, "get"], [29, 26, 30, 6, "name"], [29, 30, 30, 18], [29, 32, 30, 27], [30, 8, 31, 4], [30, 14, 31, 10], [30, 18, 31, 14, "Error"], [30, 23, 31, 19], [30, 24, 31, 20], [30, 64, 31, 60], [30, 65, 31, 61], [31, 6, 32, 2], [32, 4, 32, 3], [33, 6, 32, 3, "key"], [33, 9, 32, 3], [34, 6, 32, 3, "value"], [34, 11, 32, 3], [34, 13, 34, 2], [34, 22, 34, 2, "getAll"], [34, 28, 34, 8, "getAll"], [34, 29, 34, 9, "name"], [34, 33, 34, 21], [34, 35, 34, 30], [35, 8, 35, 4], [35, 14, 35, 10], [35, 18, 35, 14, "Error"], [35, 23, 35, 19], [35, 24, 35, 20], [35, 67, 35, 63], [35, 68, 35, 64], [36, 6, 36, 2], [37, 4, 36, 3], [38, 6, 36, 3, "key"], [38, 9, 36, 3], [39, 6, 36, 3, "value"], [39, 11, 36, 3], [39, 13, 38, 2], [39, 22, 38, 2, "has"], [39, 25, 38, 5, "has"], [39, 26, 38, 6, "name"], [39, 30, 38, 18], [39, 32, 38, 27], [40, 8, 39, 4], [40, 14, 39, 10], [40, 18, 39, 14, "Error"], [40, 23, 39, 19], [40, 24, 39, 20], [40, 64, 39, 60], [40, 65, 39, 61], [41, 6, 40, 2], [42, 4, 40, 3], [43, 6, 40, 3, "key"], [43, 9, 40, 3], [44, 6, 40, 3, "value"], [44, 11, 40, 3], [44, 13, 42, 2], [44, 22, 42, 2, "set"], [44, 25, 42, 5, "set"], [44, 26, 42, 6, "name"], [44, 30, 42, 18], [44, 32, 42, 20, "value"], [44, 37, 42, 33], [44, 39, 42, 42], [45, 8, 43, 4], [45, 14, 43, 10], [45, 18, 43, 14, "Error"], [45, 23, 43, 19], [45, 24, 43, 20], [45, 64, 43, 60], [45, 65, 43, 61], [46, 6, 44, 2], [47, 4, 44, 3], [48, 6, 44, 3, "key"], [48, 9, 44, 3], [49, 6, 44, 3, "value"], [49, 11, 44, 3], [49, 13, 46, 2], [49, 22, 46, 2, "sort"], [49, 26, 46, 6, "sort"], [49, 27, 46, 6], [49, 29, 46, 16], [50, 8, 47, 4], [50, 14, 47, 10], [50, 18, 47, 14, "Error"], [50, 23, 47, 19], [50, 24, 47, 20], [50, 65, 47, 61], [50, 66, 47, 62], [51, 6, 48, 2], [52, 4, 48, 3], [53, 6, 48, 3, "key"], [53, 9, 48, 3], [53, 11, 51, 3, "Symbol"], [53, 17, 51, 9], [53, 18, 51, 10, "iterator"], [53, 26, 51, 18], [54, 6, 51, 18, "value"], [54, 11, 51, 18], [54, 13, 51, 2], [54, 22, 51, 2, "value"], [54, 23, 51, 2], [54, 25, 51, 50], [55, 8, 52, 4], [55, 15, 52, 11], [55, 19, 52, 15], [55, 20, 52, 16, "_searchParams"], [55, 33, 52, 29], [55, 34, 52, 30, "Symbol"], [55, 40, 52, 36], [55, 41, 52, 37, "iterator"], [55, 49, 52, 45], [55, 50, 52, 46], [55, 51, 52, 47], [55, 52, 52, 48], [56, 6, 53, 2], [57, 4, 53, 3], [58, 6, 53, 3, "key"], [58, 9, 53, 3], [59, 6, 53, 3, "value"], [59, 11, 53, 3], [59, 13, 55, 2], [59, 22, 55, 2, "toString"], [59, 30, 55, 10, "toString"], [59, 31, 55, 10], [59, 33, 55, 21], [60, 8, 56, 4], [60, 12, 56, 8], [60, 16, 56, 12], [60, 17, 56, 13, "_searchParams"], [60, 30, 56, 26], [60, 31, 56, 27, "length"], [60, 37, 56, 33], [60, 42, 56, 38], [60, 43, 56, 39], [60, 45, 56, 41], [61, 10, 57, 6], [61, 17, 57, 13], [61, 19, 57, 15], [62, 8, 58, 4], [63, 8, 59, 4], [63, 12, 59, 10, "last"], [63, 16, 59, 14], [63, 19, 59, 17], [63, 23, 59, 21], [63, 24, 59, 22, "_searchParams"], [63, 37, 59, 35], [63, 38, 59, 36, "length"], [63, 44, 59, 42], [63, 47, 59, 45], [63, 48, 59, 46], [64, 8, 60, 4], [64, 15, 60, 11], [64, 19, 60, 15], [64, 20, 60, 16, "_searchParams"], [64, 33, 60, 29], [64, 34, 60, 30, "reduce"], [64, 40, 60, 36], [64, 41, 60, 37], [64, 42, 60, 38, "acc"], [64, 45, 60, 41], [64, 47, 60, 43, "curr"], [64, 51, 60, 47], [64, 53, 60, 49, "index"], [64, 58, 60, 54], [64, 63, 60, 59], [65, 10, 61, 6], [65, 17, 62, 8, "acc"], [65, 20, 62, 11], [65, 23, 63, 8, "encodeURIComponent"], [65, 41, 63, 26], [65, 42, 63, 27, "curr"], [65, 46, 63, 31], [65, 47, 63, 32], [65, 48, 63, 33], [65, 49, 63, 34], [65, 50, 63, 35], [65, 53, 64, 8], [65, 56, 64, 11], [65, 59, 65, 8, "encodeURIComponent"], [65, 77, 65, 26], [65, 78, 65, 27, "curr"], [65, 82, 65, 31], [65, 83, 65, 32], [65, 84, 65, 33], [65, 85, 65, 34], [65, 86, 65, 35], [65, 90, 66, 9, "index"], [65, 95, 66, 14], [65, 100, 66, 19, "last"], [65, 104, 66, 23], [65, 107, 66, 26], [65, 109, 66, 28], [65, 112, 66, 31], [65, 115, 66, 34], [65, 116, 66, 35], [66, 8, 68, 4], [66, 9, 68, 5], [66, 11, 68, 7], [66, 13, 68, 9], [66, 14, 68, 10], [67, 6, 69, 2], [68, 4, 69, 3], [69, 2, 69, 3], [70, 0, 69, 3], [70, 3]], "functionMap": {"names": ["<global>", "URLSearchParams", "constructor", "Object.keys.forEach$argument_0", "append", "_delete", "get", "getAll", "has", "set", "sort", "@@iterator", "toString", "_searchParams.reduce$argument_0"], "mappings": "AAA;OCY;ECG;kCCE,oCD;GDE;EGE;GHE;EIE;GJE;EKE;GLE;EME;GNE;EOE;GPE;EQE;GRE;ESE;GTE;EUG;GVE;EWE;qCCK;KDQ;GXC"}}, "type": "js/module"}]}