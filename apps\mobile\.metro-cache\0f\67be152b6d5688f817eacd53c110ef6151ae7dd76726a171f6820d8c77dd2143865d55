{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createIconSet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 118}, "end": {"line": 6, "column": 44, "index": 162}}], "key": "PQt9ucTb+ABlKWjDhj7L4XHxOIA=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/Fonts/Fontisto.ttf", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 163}, "end": {"line": 7, "column": 73, "index": 236}}], "key": "ORErDC6bwfd6ith/oY9CUTAB9zU=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/glyphmaps/Fontisto.json", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 237}, "end": {"line": 8, "column": 82, "index": 319}}], "key": "oZTmfhi4LqXEH+GW5GC/hyMesWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Feather icon set component.\n   * Usage: <Feather name=\"icon-name\" size={20} color=\"#4F8EF7\" />\n   */\n  \"use client\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createIconSet = _interopRequireDefault(require(_dependencyMap[1], \"./createIconSet\"));\n  var _Fontisto = _interopRequireDefault(require(_dependencyMap[2], \"./vendor/react-native-vector-icons/Fonts/Fontisto.ttf\"));\n  var _Fontisto2 = _interopRequireDefault(require(_dependencyMap[3], \"./vendor/react-native-vector-icons/glyphmaps/Fontisto.json\"));\n  var iconSet = (0, _createIconSet.default)(_Fontisto2.default, 'Fontisto', _Fontisto.default);\n  var _default = exports.default = iconSet;\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 2, 5, 0], [6, 14, 5, 12], [8, 2, 5, 13], [8, 6, 5, 13, "_interopRequireDefault"], [8, 28, 5, 13], [8, 31, 5, 13, "require"], [8, 38, 5, 13], [8, 39, 5, 13, "_dependencyMap"], [8, 53, 5, 13], [9, 2, 5, 13, "Object"], [9, 8, 5, 13], [9, 9, 5, 13, "defineProperty"], [9, 23, 5, 13], [9, 24, 5, 13, "exports"], [9, 31, 5, 13], [10, 4, 5, 13, "value"], [10, 9, 5, 13], [11, 2, 5, 13], [12, 2, 5, 13, "exports"], [12, 9, 5, 13], [12, 10, 5, 13, "default"], [12, 17, 5, 13], [13, 2, 6, 0], [13, 6, 6, 0, "_createIconSet"], [13, 20, 6, 0], [13, 23, 6, 0, "_interopRequireDefault"], [13, 45, 6, 0], [13, 46, 6, 0, "require"], [13, 53, 6, 0], [13, 54, 6, 0, "_dependencyMap"], [13, 68, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_Fontisto"], [14, 15, 7, 0], [14, 18, 7, 0, "_interopRequireDefault"], [14, 40, 7, 0], [14, 41, 7, 0, "require"], [14, 48, 7, 0], [14, 49, 7, 0, "_dependencyMap"], [14, 63, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_Fontisto2"], [15, 16, 8, 0], [15, 19, 8, 0, "_interopRequireDefault"], [15, 41, 8, 0], [15, 42, 8, 0, "require"], [15, 49, 8, 0], [15, 50, 8, 0, "_dependencyMap"], [15, 64, 8, 0], [16, 2, 9, 0], [16, 6, 9, 6, "iconSet"], [16, 13, 9, 13], [16, 16, 9, 16], [16, 20, 9, 16, "createIconSet"], [16, 42, 9, 29], [16, 44, 9, 30, "glyphMap"], [16, 62, 9, 38], [16, 64, 9, 40], [16, 74, 9, 50], [16, 76, 9, 52, "font"], [16, 93, 9, 56], [16, 94, 9, 57], [17, 2, 9, 58], [17, 6, 9, 58, "_default"], [17, 14, 9, 58], [17, 17, 9, 58, "exports"], [17, 24, 9, 58], [17, 25, 9, 58, "default"], [17, 32, 9, 58], [17, 35, 10, 15, "iconSet"], [17, 42, 10, 22], [18, 0, 10, 22], [18, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}