{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.numberAsInset = exports.isTouchWithinInset = exports.gestureTouchToPressableEvent = exports.gestureToPressableEvent = exports.addInsets = void 0;\n  var numberAsInset = value => ({\n    left: value,\n    right: value,\n    top: value,\n    bottom: value\n  });\n  exports.numberAsInset = numberAsInset;\n  var addInsets = (a, b) => ({\n    left: (a.left ?? 0) + (b.left ?? 0),\n    right: (a.right ?? 0) + (b.right ?? 0),\n    top: (a.top ?? 0) + (b.top ?? 0),\n    bottom: (a.bottom ?? 0) + (b.bottom ?? 0)\n  });\n  exports.addInsets = addInsets;\n  var touchDataToPressEvent = (data, timestamp, targetId) => ({\n    identifier: data.id,\n    locationX: data.x,\n    locationY: data.y,\n    pageX: data.absoluteX,\n    pageY: data.absoluteY,\n    target: targetId,\n    timestamp: timestamp,\n    touches: [],\n    // Always empty - legacy compatibility\n    changedTouches: [] // Always empty - legacy compatibility\n  });\n  var gestureToPressEvent = (event, timestamp, targetId) => ({\n    identifier: event.handlerTag,\n    locationX: event.x,\n    locationY: event.y,\n    pageX: event.absoluteX,\n    pageY: event.absoluteY,\n    target: targetId,\n    timestamp: timestamp,\n    touches: [],\n    // Always empty - legacy compatibility\n    changedTouches: [] // Always empty - legacy compatibility\n  });\n  var isTouchWithinInset = (dimensions, inset, touch) => (touch?.x ?? 0) < (inset.right ?? 0) + dimensions.width && (touch?.y ?? 0) < (inset.bottom ?? 0) + dimensions.height && (touch?.x ?? 0) > -(inset.left ?? 0) && (touch?.y ?? 0) > -(inset.top ?? 0);\n  exports.isTouchWithinInset = isTouchWithinInset;\n  var gestureToPressableEvent = event => {\n    var timestamp = Date.now();\n\n    // As far as I can see, there isn't a conventional way of getting targetId with the data we get\n    var targetId = 0;\n    var pressEvent = gestureToPressEvent(event, timestamp, targetId);\n    return {\n      nativeEvent: {\n        touches: [pressEvent],\n        changedTouches: [pressEvent],\n        identifier: pressEvent.identifier,\n        locationX: event.x,\n        locationY: event.y,\n        pageX: event.absoluteX,\n        pageY: event.absoluteY,\n        target: targetId,\n        timestamp: timestamp,\n        force: undefined\n      }\n    };\n  };\n  exports.gestureToPressableEvent = gestureToPressableEvent;\n  var gestureTouchToPressableEvent = event => {\n    var timestamp = Date.now();\n\n    // As far as I can see, there isn't a conventional way of getting targetId with the data we get\n    var targetId = 0;\n    var touchesList = event.allTouches.map(touch => touchDataToPressEvent(touch, timestamp, targetId));\n    var changedTouchesList = event.changedTouches.map(touch => touchDataToPressEvent(touch, timestamp, targetId));\n    return {\n      nativeEvent: {\n        touches: touchesList,\n        changedTouches: changedTouchesList,\n        identifier: event.handlerTag,\n        locationX: event.allTouches.at(0)?.x ?? -1,\n        locationY: event.allTouches.at(0)?.y ?? -1,\n        pageX: event.allTouches.at(0)?.absoluteX ?? -1,\n        pageY: event.allTouches.at(0)?.absoluteY ?? -1,\n        target: targetId,\n        timestamp: timestamp,\n        force: undefined\n      }\n    };\n  };\n  exports.gestureTouchToPressableEvent = gestureTouchToPressableEvent;\n});", "lineCount": 91, "map": [[6, 2, 13, 0], [6, 6, 13, 6, "numberAsInset"], [6, 19, 13, 19], [6, 22, 13, 23, "value"], [6, 27, 13, 36], [6, 32, 13, 50], [7, 4, 14, 2, "left"], [7, 8, 14, 6], [7, 10, 14, 8, "value"], [7, 15, 14, 13], [8, 4, 15, 2, "right"], [8, 9, 15, 7], [8, 11, 15, 9, "value"], [8, 16, 15, 14], [9, 4, 16, 2, "top"], [9, 7, 16, 5], [9, 9, 16, 7, "value"], [9, 14, 16, 12], [10, 4, 17, 2, "bottom"], [10, 10, 17, 8], [10, 12, 17, 10, "value"], [11, 2, 18, 0], [11, 3, 18, 1], [11, 4, 18, 2], [12, 2, 18, 3, "exports"], [12, 9, 18, 3], [12, 10, 18, 3, "numberAsInset"], [12, 23, 18, 3], [12, 26, 18, 3, "numberAsInset"], [12, 39, 18, 3], [13, 2, 20, 0], [13, 6, 20, 6, "addInsets"], [13, 15, 20, 15], [13, 18, 20, 18, "addInsets"], [13, 19, 20, 19, "a"], [13, 20, 20, 28], [13, 22, 20, 30, "b"], [13, 23, 20, 39], [13, 29, 20, 53], [14, 4, 21, 2, "left"], [14, 8, 21, 6], [14, 10, 21, 8], [14, 11, 21, 9, "a"], [14, 12, 21, 10], [14, 13, 21, 11, "left"], [14, 17, 21, 15], [14, 21, 21, 19], [14, 22, 21, 20], [14, 27, 21, 25, "b"], [14, 28, 21, 26], [14, 29, 21, 27, "left"], [14, 33, 21, 31], [14, 37, 21, 35], [14, 38, 21, 36], [14, 39, 21, 37], [15, 4, 22, 2, "right"], [15, 9, 22, 7], [15, 11, 22, 9], [15, 12, 22, 10, "a"], [15, 13, 22, 11], [15, 14, 22, 12, "right"], [15, 19, 22, 17], [15, 23, 22, 21], [15, 24, 22, 22], [15, 29, 22, 27, "b"], [15, 30, 22, 28], [15, 31, 22, 29, "right"], [15, 36, 22, 34], [15, 40, 22, 38], [15, 41, 22, 39], [15, 42, 22, 40], [16, 4, 23, 2, "top"], [16, 7, 23, 5], [16, 9, 23, 7], [16, 10, 23, 8, "a"], [16, 11, 23, 9], [16, 12, 23, 10, "top"], [16, 15, 23, 13], [16, 19, 23, 17], [16, 20, 23, 18], [16, 25, 23, 23, "b"], [16, 26, 23, 24], [16, 27, 23, 25, "top"], [16, 30, 23, 28], [16, 34, 23, 32], [16, 35, 23, 33], [16, 36, 23, 34], [17, 4, 24, 2, "bottom"], [17, 10, 24, 8], [17, 12, 24, 10], [17, 13, 24, 11, "a"], [17, 14, 24, 12], [17, 15, 24, 13, "bottom"], [17, 21, 24, 19], [17, 25, 24, 23], [17, 26, 24, 24], [17, 31, 24, 29, "b"], [17, 32, 24, 30], [17, 33, 24, 31, "bottom"], [17, 39, 24, 37], [17, 43, 24, 41], [17, 44, 24, 42], [18, 2, 25, 0], [18, 3, 25, 1], [18, 4, 25, 2], [19, 2, 25, 3, "exports"], [19, 9, 25, 3], [19, 10, 25, 3, "addInsets"], [19, 19, 25, 3], [19, 22, 25, 3, "addInsets"], [19, 31, 25, 3], [20, 2, 27, 0], [20, 6, 27, 6, "touchDataToPressEvent"], [20, 27, 27, 27], [20, 30, 27, 30, "touchDataToPressEvent"], [20, 31, 28, 2, "data"], [20, 35, 28, 17], [20, 37, 29, 2, "timestamp"], [20, 46, 29, 19], [20, 48, 30, 2, "targetId"], [20, 56, 30, 18], [20, 62, 31, 27], [21, 4, 32, 2, "identifier"], [21, 14, 32, 12], [21, 16, 32, 14, "data"], [21, 20, 32, 18], [21, 21, 32, 19, "id"], [21, 23, 32, 21], [22, 4, 33, 2, "locationX"], [22, 13, 33, 11], [22, 15, 33, 13, "data"], [22, 19, 33, 17], [22, 20, 33, 18, "x"], [22, 21, 33, 19], [23, 4, 34, 2, "locationY"], [23, 13, 34, 11], [23, 15, 34, 13, "data"], [23, 19, 34, 17], [23, 20, 34, 18, "y"], [23, 21, 34, 19], [24, 4, 35, 2, "pageX"], [24, 9, 35, 7], [24, 11, 35, 9, "data"], [24, 15, 35, 13], [24, 16, 35, 14, "absoluteX"], [24, 25, 35, 23], [25, 4, 36, 2, "pageY"], [25, 9, 36, 7], [25, 11, 36, 9, "data"], [25, 15, 36, 13], [25, 16, 36, 14, "absoluteY"], [25, 25, 36, 23], [26, 4, 37, 2, "target"], [26, 10, 37, 8], [26, 12, 37, 10, "targetId"], [26, 20, 37, 18], [27, 4, 38, 2, "timestamp"], [27, 13, 38, 11], [27, 15, 38, 13, "timestamp"], [27, 24, 38, 22], [28, 4, 39, 2, "touches"], [28, 11, 39, 9], [28, 13, 39, 11], [28, 15, 39, 13], [29, 4, 39, 15], [30, 4, 40, 2, "changedTouches"], [30, 18, 40, 16], [30, 20, 40, 18], [30, 22, 40, 20], [30, 23, 40, 22], [31, 2, 41, 0], [31, 3, 41, 1], [31, 4, 41, 2], [32, 2, 43, 0], [32, 6, 43, 6, "gestureToPressEvent"], [32, 25, 43, 25], [32, 28, 43, 28, "gestureToPressEvent"], [32, 29, 44, 2, "event"], [32, 34, 46, 3], [32, 36, 47, 2, "timestamp"], [32, 45, 47, 19], [32, 47, 48, 2, "targetId"], [32, 55, 48, 18], [32, 61, 49, 27], [33, 4, 50, 2, "identifier"], [33, 14, 50, 12], [33, 16, 50, 14, "event"], [33, 21, 50, 19], [33, 22, 50, 20, "handlerTag"], [33, 32, 50, 30], [34, 4, 51, 2, "locationX"], [34, 13, 51, 11], [34, 15, 51, 13, "event"], [34, 20, 51, 18], [34, 21, 51, 19, "x"], [34, 22, 51, 20], [35, 4, 52, 2, "locationY"], [35, 13, 52, 11], [35, 15, 52, 13, "event"], [35, 20, 52, 18], [35, 21, 52, 19, "y"], [35, 22, 52, 20], [36, 4, 53, 2, "pageX"], [36, 9, 53, 7], [36, 11, 53, 9, "event"], [36, 16, 53, 14], [36, 17, 53, 15, "absoluteX"], [36, 26, 53, 24], [37, 4, 54, 2, "pageY"], [37, 9, 54, 7], [37, 11, 54, 9, "event"], [37, 16, 54, 14], [37, 17, 54, 15, "absoluteY"], [37, 26, 54, 24], [38, 4, 55, 2, "target"], [38, 10, 55, 8], [38, 12, 55, 10, "targetId"], [38, 20, 55, 18], [39, 4, 56, 2, "timestamp"], [39, 13, 56, 11], [39, 15, 56, 13, "timestamp"], [39, 24, 56, 22], [40, 4, 57, 2, "touches"], [40, 11, 57, 9], [40, 13, 57, 11], [40, 15, 57, 13], [41, 4, 57, 15], [42, 4, 58, 2, "changedTouches"], [42, 18, 58, 16], [42, 20, 58, 18], [42, 22, 58, 20], [42, 23, 58, 22], [43, 2, 59, 0], [43, 3, 59, 1], [43, 4, 59, 2], [44, 2, 61, 0], [44, 6, 61, 6, "isTouchWithinInset"], [44, 24, 61, 24], [44, 27, 61, 27, "isTouchWithinInset"], [44, 28, 62, 2, "dimensions"], [44, 38, 62, 47], [44, 40, 63, 2, "inset"], [44, 45, 63, 15], [44, 47, 64, 2, "touch"], [44, 52, 64, 19], [44, 57, 66, 2], [44, 58, 66, 3, "touch"], [44, 63, 66, 8], [44, 65, 66, 10, "x"], [44, 66, 66, 11], [44, 70, 66, 15], [44, 71, 66, 16], [44, 75, 66, 20], [44, 76, 66, 21, "inset"], [44, 81, 66, 26], [44, 82, 66, 27, "right"], [44, 87, 66, 32], [44, 91, 66, 36], [44, 92, 66, 37], [44, 96, 66, 41, "dimensions"], [44, 106, 66, 51], [44, 107, 66, 52, "width"], [44, 112, 66, 57], [44, 116, 67, 2], [44, 117, 67, 3, "touch"], [44, 122, 67, 8], [44, 124, 67, 10, "y"], [44, 125, 67, 11], [44, 129, 67, 15], [44, 130, 67, 16], [44, 134, 67, 20], [44, 135, 67, 21, "inset"], [44, 140, 67, 26], [44, 141, 67, 27, "bottom"], [44, 147, 67, 33], [44, 151, 67, 37], [44, 152, 67, 38], [44, 156, 67, 42, "dimensions"], [44, 166, 67, 52], [44, 167, 67, 53, "height"], [44, 173, 67, 59], [44, 177, 68, 2], [44, 178, 68, 3, "touch"], [44, 183, 68, 8], [44, 185, 68, 10, "x"], [44, 186, 68, 11], [44, 190, 68, 15], [44, 191, 68, 16], [44, 195, 68, 20], [44, 197, 68, 22, "inset"], [44, 202, 68, 27], [44, 203, 68, 28, "left"], [44, 207, 68, 32], [44, 211, 68, 36], [44, 212, 68, 37], [44, 213, 68, 38], [44, 217, 69, 2], [44, 218, 69, 3, "touch"], [44, 223, 69, 8], [44, 225, 69, 10, "y"], [44, 226, 69, 11], [44, 230, 69, 15], [44, 231, 69, 16], [44, 235, 69, 20], [44, 237, 69, 22, "inset"], [44, 242, 69, 27], [44, 243, 69, 28, "top"], [44, 246, 69, 31], [44, 250, 69, 35], [44, 251, 69, 36], [44, 252, 69, 37], [45, 2, 69, 38, "exports"], [45, 9, 69, 38], [45, 10, 69, 38, "isTouchWithinInset"], [45, 28, 69, 38], [45, 31, 69, 38, "isTouchWithinInset"], [45, 49, 69, 38], [46, 2, 71, 0], [46, 6, 71, 6, "gestureToPressableEvent"], [46, 29, 71, 29], [46, 32, 72, 2, "event"], [46, 37, 74, 3], [46, 41, 75, 21], [47, 4, 76, 2], [47, 8, 76, 8, "timestamp"], [47, 17, 76, 17], [47, 20, 76, 20, "Date"], [47, 24, 76, 24], [47, 25, 76, 25, "now"], [47, 28, 76, 28], [47, 29, 76, 29], [47, 30, 76, 30], [49, 4, 78, 2], [50, 4, 79, 2], [50, 8, 79, 8, "targetId"], [50, 16, 79, 16], [50, 19, 79, 19], [50, 20, 79, 20], [51, 4, 81, 2], [51, 8, 81, 8, "pressEvent"], [51, 18, 81, 18], [51, 21, 81, 21, "gestureToPressEvent"], [51, 40, 81, 40], [51, 41, 81, 41, "event"], [51, 46, 81, 46], [51, 48, 81, 48, "timestamp"], [51, 57, 81, 57], [51, 59, 81, 59, "targetId"], [51, 67, 81, 67], [51, 68, 81, 68], [52, 4, 83, 2], [52, 11, 83, 9], [53, 6, 84, 4, "nativeEvent"], [53, 17, 84, 15], [53, 19, 84, 17], [54, 8, 85, 6, "touches"], [54, 15, 85, 13], [54, 17, 85, 15], [54, 18, 85, 16, "pressEvent"], [54, 28, 85, 26], [54, 29, 85, 27], [55, 8, 86, 6, "changedTouches"], [55, 22, 86, 20], [55, 24, 86, 22], [55, 25, 86, 23, "pressEvent"], [55, 35, 86, 33], [55, 36, 86, 34], [56, 8, 87, 6, "identifier"], [56, 18, 87, 16], [56, 20, 87, 18, "pressEvent"], [56, 30, 87, 28], [56, 31, 87, 29, "identifier"], [56, 41, 87, 39], [57, 8, 88, 6, "locationX"], [57, 17, 88, 15], [57, 19, 88, 17, "event"], [57, 24, 88, 22], [57, 25, 88, 23, "x"], [57, 26, 88, 24], [58, 8, 89, 6, "locationY"], [58, 17, 89, 15], [58, 19, 89, 17, "event"], [58, 24, 89, 22], [58, 25, 89, 23, "y"], [58, 26, 89, 24], [59, 8, 90, 6, "pageX"], [59, 13, 90, 11], [59, 15, 90, 13, "event"], [59, 20, 90, 18], [59, 21, 90, 19, "absoluteX"], [59, 30, 90, 28], [60, 8, 91, 6, "pageY"], [60, 13, 91, 11], [60, 15, 91, 13, "event"], [60, 20, 91, 18], [60, 21, 91, 19, "absoluteY"], [60, 30, 91, 28], [61, 8, 92, 6, "target"], [61, 14, 92, 12], [61, 16, 92, 14, "targetId"], [61, 24, 92, 22], [62, 8, 93, 6, "timestamp"], [62, 17, 93, 15], [62, 19, 93, 17, "timestamp"], [62, 28, 93, 26], [63, 8, 94, 6, "force"], [63, 13, 94, 11], [63, 15, 94, 13, "undefined"], [64, 6, 95, 4], [65, 4, 96, 2], [65, 5, 96, 3], [66, 2, 97, 0], [66, 3, 97, 1], [67, 2, 97, 2, "exports"], [67, 9, 97, 2], [67, 10, 97, 2, "gestureToPressableEvent"], [67, 33, 97, 2], [67, 36, 97, 2, "gestureToPressableEvent"], [67, 59, 97, 2], [68, 2, 99, 0], [68, 6, 99, 6, "gestureTouchToPressableEvent"], [68, 34, 99, 34], [68, 37, 100, 2, "event"], [68, 42, 100, 26], [68, 46, 101, 21], [69, 4, 102, 2], [69, 8, 102, 8, "timestamp"], [69, 17, 102, 17], [69, 20, 102, 20, "Date"], [69, 24, 102, 24], [69, 25, 102, 25, "now"], [69, 28, 102, 28], [69, 29, 102, 29], [69, 30, 102, 30], [71, 4, 104, 2], [72, 4, 105, 2], [72, 8, 105, 8, "targetId"], [72, 16, 105, 16], [72, 19, 105, 19], [72, 20, 105, 20], [73, 4, 107, 2], [73, 8, 107, 8, "touchesList"], [73, 19, 107, 19], [73, 22, 107, 22, "event"], [73, 27, 107, 27], [73, 28, 107, 28, "allTouches"], [73, 38, 107, 38], [73, 39, 107, 39, "map"], [73, 42, 107, 42], [73, 43, 107, 44, "touch"], [73, 48, 107, 60], [73, 52, 108, 4, "touchDataToPressEvent"], [73, 73, 108, 25], [73, 74, 108, 26, "touch"], [73, 79, 108, 31], [73, 81, 108, 33, "timestamp"], [73, 90, 108, 42], [73, 92, 108, 44, "targetId"], [73, 100, 108, 52], [73, 101, 109, 2], [73, 102, 109, 3], [74, 4, 110, 2], [74, 8, 110, 8, "changedTouchesList"], [74, 26, 110, 26], [74, 29, 110, 29, "event"], [74, 34, 110, 34], [74, 35, 110, 35, "changedTouches"], [74, 49, 110, 49], [74, 50, 110, 50, "map"], [74, 53, 110, 53], [74, 54, 110, 55, "touch"], [74, 59, 110, 71], [74, 63, 111, 4, "touchDataToPressEvent"], [74, 84, 111, 25], [74, 85, 111, 26, "touch"], [74, 90, 111, 31], [74, 92, 111, 33, "timestamp"], [74, 101, 111, 42], [74, 103, 111, 44, "targetId"], [74, 111, 111, 52], [74, 112, 112, 2], [74, 113, 112, 3], [75, 4, 114, 2], [75, 11, 114, 9], [76, 6, 115, 4, "nativeEvent"], [76, 17, 115, 15], [76, 19, 115, 17], [77, 8, 116, 6, "touches"], [77, 15, 116, 13], [77, 17, 116, 15, "touchesList"], [77, 28, 116, 26], [78, 8, 117, 6, "changedTouches"], [78, 22, 117, 20], [78, 24, 117, 22, "changedTouchesList"], [78, 42, 117, 40], [79, 8, 118, 6, "identifier"], [79, 18, 118, 16], [79, 20, 118, 18, "event"], [79, 25, 118, 23], [79, 26, 118, 24, "handlerTag"], [79, 36, 118, 34], [80, 8, 119, 6, "locationX"], [80, 17, 119, 15], [80, 19, 119, 17, "event"], [80, 24, 119, 22], [80, 25, 119, 23, "allTouches"], [80, 35, 119, 33], [80, 36, 119, 34, "at"], [80, 38, 119, 36], [80, 39, 119, 37], [80, 40, 119, 38], [80, 41, 119, 39], [80, 43, 119, 41, "x"], [80, 44, 119, 42], [80, 48, 119, 46], [80, 49, 119, 47], [80, 50, 119, 48], [81, 8, 120, 6, "locationY"], [81, 17, 120, 15], [81, 19, 120, 17, "event"], [81, 24, 120, 22], [81, 25, 120, 23, "allTouches"], [81, 35, 120, 33], [81, 36, 120, 34, "at"], [81, 38, 120, 36], [81, 39, 120, 37], [81, 40, 120, 38], [81, 41, 120, 39], [81, 43, 120, 41, "y"], [81, 44, 120, 42], [81, 48, 120, 46], [81, 49, 120, 47], [81, 50, 120, 48], [82, 8, 121, 6, "pageX"], [82, 13, 121, 11], [82, 15, 121, 13, "event"], [82, 20, 121, 18], [82, 21, 121, 19, "allTouches"], [82, 31, 121, 29], [82, 32, 121, 30, "at"], [82, 34, 121, 32], [82, 35, 121, 33], [82, 36, 121, 34], [82, 37, 121, 35], [82, 39, 121, 37, "absoluteX"], [82, 48, 121, 46], [82, 52, 121, 50], [82, 53, 121, 51], [82, 54, 121, 52], [83, 8, 122, 6, "pageY"], [83, 13, 122, 11], [83, 15, 122, 13, "event"], [83, 20, 122, 18], [83, 21, 122, 19, "allTouches"], [83, 31, 122, 29], [83, 32, 122, 30, "at"], [83, 34, 122, 32], [83, 35, 122, 33], [83, 36, 122, 34], [83, 37, 122, 35], [83, 39, 122, 37, "absoluteY"], [83, 48, 122, 46], [83, 52, 122, 50], [83, 53, 122, 51], [83, 54, 122, 52], [84, 8, 123, 6, "target"], [84, 14, 123, 12], [84, 16, 123, 14, "targetId"], [84, 24, 123, 22], [85, 8, 124, 6, "timestamp"], [85, 17, 124, 15], [85, 19, 124, 17, "timestamp"], [85, 28, 124, 26], [86, 8, 125, 6, "force"], [86, 13, 125, 11], [86, 15, 125, 13, "undefined"], [87, 6, 126, 4], [88, 4, 127, 2], [88, 5, 127, 3], [89, 2, 128, 0], [89, 3, 128, 1], [90, 2, 128, 2, "exports"], [90, 9, 128, 2], [90, 10, 128, 2, "gestureTouchToPressableEvent"], [90, 38, 128, 2], [90, 41, 128, 2, "gestureTouchToPressableEvent"], [90, 69, 128, 2], [91, 0, 128, 2], [91, 3]], "functionMap": {"names": ["<global>", "numberAsInset", "addInsets", "touchDataToPressEvent", "gestureToPressEvent", "isTouchWithinInset", "gestureToPressableEvent", "gestureTouchToPressableEvent", "event.allTouches.map$argument_0", "event.changedTouches.map$argument_0"], "mappings": "AAA;sBCY;EDK;kBEE;EFK;8BGE;EHc;4BIE;EJgB;2BKE;qCLQ;gCME;CN0B;qCOE;2CCQ;qDDC;sDEE;qDFC;CPiB"}}, "type": "js/module"}]}