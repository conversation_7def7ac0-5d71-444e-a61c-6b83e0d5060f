{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Components/View/View", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 63}}], "key": "H/3fvmiHyIdASS62Hfb3a4a54KU=", "exportNames": ["*"]}}, {"name": "../../../Libraries/StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 16, "column": 19}, "end": {"line": 16, "column": 70}}], "key": "Nurrv5y9ebtgGhUjBt0E/GEpaGk=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Text/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 13}, "end": {"line": 17, "column": 52}}], "key": "MoPWeVCFlo44fZk7PVmQmJJxnfs=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Utilities/GlobalPerformanceLogger", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 65}}], "key": "/S9vWR8DSQEL0Hbm/FcSA7AKKV4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[6], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\src\\\\private\\\\inspector\\\\PerformanceOverlay.js\";\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var View = require(_dependencyMap[8], \"../../../Libraries/Components/View/View\").default;\n  var StyleSheet = require(_dependencyMap[9], \"../../../Libraries/StyleSheet/StyleSheet\").default;\n  var Text = require(_dependencyMap[10], \"../../../Libraries/Text/Text\").default;\n  var PerformanceLogger = require(_dependencyMap[11], \"../../../Libraries/Utilities/GlobalPerformanceLogger\").default;\n  var PerformanceOverlay = /*#__PURE__*/function (_React$Component) {\n    function PerformanceOverlay() {\n      (0, _classCallCheck2.default)(this, PerformanceOverlay);\n      return _callSuper(this, PerformanceOverlay, arguments);\n    }\n    (0, _inherits2.default)(PerformanceOverlay, _React$Component);\n    return (0, _createClass2.default)(PerformanceOverlay, [{\n      key: \"render\",\n      value: function render() {\n        var perfLogs = PerformanceLogger.getTimespans();\n        var items = [];\n        for (var key in perfLogs) {\n          if (perfLogs[key]?.totalTime) {\n            var unit = key === 'BundleSize' ? 'b' : 'ms';\n            items.push(/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(View, {\n              style: styles.row,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Text, {\n                style: [styles.text, styles.label],\n                children: key\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 13\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Text, {\n                style: [styles.text, styles.totalTime],\n                children: perfLogs[key].totalTime + unit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 13\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 11\n            }, this));\n          }\n        }\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(View, {\n          style: styles.container,\n          children: items\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 12\n        }, this);\n      }\n    }]);\n  }(_react.default.Component);\n  var styles = StyleSheet.create({\n    container: {\n      height: 100,\n      paddingTop: 10\n    },\n    label: {\n      flex: 1\n    },\n    row: {\n      flexDirection: 'row',\n      paddingHorizontal: 10\n    },\n    text: {\n      color: 'white',\n      fontSize: 12\n    },\n    totalTime: {\n      paddingRight: 100\n    }\n  });\n  var _default = exports.default = PerformanceOverlay;\n});", "lineCount": 93, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_classCallCheck2"], [9, 22, 11, 13], [9, 25, 11, 13, "_interopRequireDefault"], [9, 47, 11, 13], [9, 48, 11, 13, "require"], [9, 55, 11, 13], [9, 56, 11, 13, "_dependencyMap"], [9, 70, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_createClass2"], [10, 19, 11, 13], [10, 22, 11, 13, "_interopRequireDefault"], [10, 44, 11, 13], [10, 45, 11, 13, "require"], [10, 52, 11, 13], [10, 53, 11, 13, "_dependencyMap"], [10, 67, 11, 13], [11, 2, 11, 13], [11, 6, 11, 13, "_possibleConstructorReturn2"], [11, 33, 11, 13], [11, 36, 11, 13, "_interopRequireDefault"], [11, 58, 11, 13], [11, 59, 11, 13, "require"], [11, 66, 11, 13], [11, 67, 11, 13, "_dependencyMap"], [11, 81, 11, 13], [12, 2, 11, 13], [12, 6, 11, 13, "_getPrototypeOf2"], [12, 22, 11, 13], [12, 25, 11, 13, "_interopRequireDefault"], [12, 47, 11, 13], [12, 48, 11, 13, "require"], [12, 55, 11, 13], [12, 56, 11, 13, "_dependencyMap"], [12, 70, 11, 13], [13, 2, 11, 13], [13, 6, 11, 13, "_inherits2"], [13, 16, 11, 13], [13, 19, 11, 13, "_interopRequireDefault"], [13, 41, 11, 13], [13, 42, 11, 13, "require"], [13, 49, 11, 13], [13, 50, 11, 13, "_dependencyMap"], [13, 64, 11, 13], [14, 2, 13, 0], [14, 6, 13, 0, "_react"], [14, 12, 13, 0], [14, 15, 13, 0, "_interopRequireDefault"], [14, 37, 13, 0], [14, 38, 13, 0, "require"], [14, 45, 13, 0], [14, 46, 13, 0, "_dependencyMap"], [14, 60, 13, 0], [15, 2, 13, 26], [15, 6, 13, 26, "_jsxDevRuntime"], [15, 20, 13, 26], [15, 23, 13, 26, "require"], [15, 30, 13, 26], [15, 31, 13, 26, "_dependencyMap"], [15, 45, 13, 26], [16, 2, 13, 26], [16, 6, 13, 26, "_jsxFileName"], [16, 18, 13, 26], [17, 2, 13, 26], [17, 11, 13, 26, "_callSuper"], [17, 22, 13, 26, "t"], [17, 23, 13, 26], [17, 25, 13, 26, "o"], [17, 26, 13, 26], [17, 28, 13, 26, "e"], [17, 29, 13, 26], [17, 40, 13, 26, "o"], [17, 41, 13, 26], [17, 48, 13, 26, "_getPrototypeOf2"], [17, 64, 13, 26], [17, 65, 13, 26, "default"], [17, 72, 13, 26], [17, 74, 13, 26, "o"], [17, 75, 13, 26], [17, 82, 13, 26, "_possibleConstructorReturn2"], [17, 109, 13, 26], [17, 110, 13, 26, "default"], [17, 117, 13, 26], [17, 119, 13, 26, "t"], [17, 120, 13, 26], [17, 122, 13, 26, "_isNativeReflectConstruct"], [17, 147, 13, 26], [17, 152, 13, 26, "Reflect"], [17, 159, 13, 26], [17, 160, 13, 26, "construct"], [17, 169, 13, 26], [17, 170, 13, 26, "o"], [17, 171, 13, 26], [17, 173, 13, 26, "e"], [17, 174, 13, 26], [17, 186, 13, 26, "_getPrototypeOf2"], [17, 202, 13, 26], [17, 203, 13, 26, "default"], [17, 210, 13, 26], [17, 212, 13, 26, "t"], [17, 213, 13, 26], [17, 215, 13, 26, "constructor"], [17, 226, 13, 26], [17, 230, 13, 26, "o"], [17, 231, 13, 26], [17, 232, 13, 26, "apply"], [17, 237, 13, 26], [17, 238, 13, 26, "t"], [17, 239, 13, 26], [17, 241, 13, 26, "e"], [17, 242, 13, 26], [18, 2, 13, 26], [18, 11, 13, 26, "_isNativeReflectConstruct"], [18, 37, 13, 26], [18, 51, 13, 26, "t"], [18, 52, 13, 26], [18, 56, 13, 26, "Boolean"], [18, 63, 13, 26], [18, 64, 13, 26, "prototype"], [18, 73, 13, 26], [18, 74, 13, 26, "valueOf"], [18, 81, 13, 26], [18, 82, 13, 26, "call"], [18, 86, 13, 26], [18, 87, 13, 26, "Reflect"], [18, 94, 13, 26], [18, 95, 13, 26, "construct"], [18, 104, 13, 26], [18, 105, 13, 26, "Boolean"], [18, 112, 13, 26], [18, 145, 13, 26, "t"], [18, 146, 13, 26], [18, 159, 13, 26, "_isNativeReflectConstruct"], [18, 184, 13, 26], [18, 196, 13, 26, "_isNativeReflectConstruct"], [18, 197, 13, 26], [18, 210, 13, 26, "t"], [18, 211, 13, 26], [19, 2, 15, 0], [19, 6, 15, 6, "View"], [19, 10, 15, 10], [19, 13, 15, 13, "require"], [19, 20, 15, 20], [19, 21, 15, 20, "_dependencyMap"], [19, 35, 15, 20], [19, 81, 15, 62], [19, 82, 15, 63], [19, 83, 15, 64, "default"], [19, 90, 15, 71], [20, 2, 16, 0], [20, 6, 16, 6, "StyleSheet"], [20, 16, 16, 16], [20, 19, 16, 19, "require"], [20, 26, 16, 26], [20, 27, 16, 26, "_dependencyMap"], [20, 41, 16, 26], [20, 88, 16, 69], [20, 89, 16, 70], [20, 90, 16, 71, "default"], [20, 97, 16, 78], [21, 2, 17, 0], [21, 6, 17, 6, "Text"], [21, 10, 17, 10], [21, 13, 17, 13, "require"], [21, 20, 17, 20], [21, 21, 17, 20, "_dependencyMap"], [21, 35, 17, 20], [21, 71, 17, 51], [21, 72, 17, 52], [21, 73, 17, 53, "default"], [21, 80, 17, 60], [22, 2, 18, 0], [22, 6, 18, 6, "PerformanceLogger"], [22, 23, 18, 23], [22, 26, 19, 2, "require"], [22, 33, 19, 9], [22, 34, 19, 9, "_dependencyMap"], [22, 48, 19, 9], [22, 108, 19, 64], [22, 109, 19, 65], [22, 110, 19, 66, "default"], [22, 117, 19, 73], [23, 2, 19, 74], [23, 6, 21, 6, "PerformanceOverlay"], [23, 24, 21, 24], [23, 50, 21, 24, "_React$Component"], [23, 66, 21, 24], [24, 4, 21, 24], [24, 13, 21, 24, "PerformanceOverlay"], [24, 32, 21, 24], [25, 6, 21, 24], [25, 10, 21, 24, "_classCallCheck2"], [25, 26, 21, 24], [25, 27, 21, 24, "default"], [25, 34, 21, 24], [25, 42, 21, 24, "PerformanceOverlay"], [25, 60, 21, 24], [26, 6, 21, 24], [26, 13, 21, 24, "_callSuper"], [26, 23, 21, 24], [26, 30, 21, 24, "PerformanceOverlay"], [26, 48, 21, 24], [26, 50, 21, 24, "arguments"], [26, 59, 21, 24], [27, 4, 21, 24], [28, 4, 21, 24], [28, 8, 21, 24, "_inherits2"], [28, 18, 21, 24], [28, 19, 21, 24, "default"], [28, 26, 21, 24], [28, 28, 21, 24, "PerformanceOverlay"], [28, 46, 21, 24], [28, 48, 21, 24, "_React$Component"], [28, 64, 21, 24], [29, 4, 21, 24], [29, 15, 21, 24, "_createClass2"], [29, 28, 21, 24], [29, 29, 21, 24, "default"], [29, 36, 21, 24], [29, 38, 21, 24, "PerformanceOverlay"], [29, 56, 21, 24], [30, 6, 21, 24, "key"], [30, 9, 21, 24], [31, 6, 21, 24, "value"], [31, 11, 21, 24], [31, 13, 22, 2], [31, 22, 22, 2, "render"], [31, 28, 22, 8, "render"], [31, 29, 22, 8], [31, 31, 22, 23], [32, 8, 23, 4], [32, 12, 23, 10, "perfLogs"], [32, 20, 23, 18], [32, 23, 23, 21, "PerformanceLogger"], [32, 40, 23, 38], [32, 41, 23, 39, "getTimespans"], [32, 53, 23, 51], [32, 54, 23, 52], [32, 55, 23, 53], [33, 8, 24, 4], [33, 12, 24, 10, "items"], [33, 17, 24, 15], [33, 20, 24, 18], [33, 22, 24, 20], [34, 8, 26, 4], [34, 13, 26, 9], [34, 17, 26, 15, "key"], [34, 20, 26, 18], [34, 24, 26, 22, "perfLogs"], [34, 32, 26, 30], [34, 34, 26, 32], [35, 10, 27, 6], [35, 14, 27, 10, "perfLogs"], [35, 22, 27, 18], [35, 23, 27, 19, "key"], [35, 26, 27, 22], [35, 27, 27, 23], [35, 29, 27, 25, "totalTime"], [35, 38, 27, 34], [35, 40, 27, 36], [36, 12, 28, 8], [36, 16, 28, 14, "unit"], [36, 20, 28, 18], [36, 23, 28, 21, "key"], [36, 26, 28, 24], [36, 31, 28, 29], [36, 43, 28, 41], [36, 46, 28, 44], [36, 49, 28, 47], [36, 52, 28, 50], [36, 56, 28, 54], [37, 12, 29, 8, "items"], [37, 17, 29, 13], [37, 18, 29, 14, "push"], [37, 22, 29, 18], [37, 36, 30, 10], [37, 40, 30, 10, "_jsxDevRuntime"], [37, 54, 30, 10], [37, 55, 30, 10, "jsxDEV"], [37, 61, 30, 10], [37, 63, 30, 11, "View"], [37, 67, 30, 15], [38, 14, 30, 16, "style"], [38, 19, 30, 21], [38, 21, 30, 23, "styles"], [38, 27, 30, 29], [38, 28, 30, 30, "row"], [38, 31, 30, 34], [39, 14, 30, 34, "children"], [39, 22, 30, 34], [39, 38, 31, 12], [39, 42, 31, 12, "_jsxDevRuntime"], [39, 56, 31, 12], [39, 57, 31, 12, "jsxDEV"], [39, 63, 31, 12], [39, 65, 31, 13, "Text"], [39, 69, 31, 17], [40, 16, 31, 18, "style"], [40, 21, 31, 23], [40, 23, 31, 25], [40, 24, 31, 26, "styles"], [40, 30, 31, 32], [40, 31, 31, 33, "text"], [40, 35, 31, 37], [40, 37, 31, 39, "styles"], [40, 43, 31, 45], [40, 44, 31, 46, "label"], [40, 49, 31, 51], [40, 50, 31, 53], [41, 16, 31, 53, "children"], [41, 24, 31, 53], [41, 26, 31, 55, "key"], [42, 14, 31, 58], [43, 16, 31, 58, "fileName"], [43, 24, 31, 58], [43, 26, 31, 58, "_jsxFileName"], [43, 38, 31, 58], [44, 16, 31, 58, "lineNumber"], [44, 26, 31, 58], [45, 16, 31, 58, "columnNumber"], [45, 28, 31, 58], [46, 14, 31, 58], [46, 21, 31, 65], [46, 22, 31, 66], [46, 37, 32, 12], [46, 41, 32, 12, "_jsxDevRuntime"], [46, 55, 32, 12], [46, 56, 32, 12, "jsxDEV"], [46, 62, 32, 12], [46, 64, 32, 13, "Text"], [46, 68, 32, 17], [47, 16, 32, 18, "style"], [47, 21, 32, 23], [47, 23, 32, 25], [47, 24, 32, 26, "styles"], [47, 30, 32, 32], [47, 31, 32, 33, "text"], [47, 35, 32, 37], [47, 37, 32, 39, "styles"], [47, 43, 32, 45], [47, 44, 32, 46, "totalTime"], [47, 53, 32, 55], [47, 54, 32, 57], [48, 16, 32, 57, "children"], [48, 24, 32, 57], [48, 26, 33, 15, "perfLogs"], [48, 34, 33, 23], [48, 35, 33, 24, "key"], [48, 38, 33, 27], [48, 39, 33, 28], [48, 40, 33, 29, "totalTime"], [48, 49, 33, 38], [48, 52, 33, 41, "unit"], [49, 14, 33, 45], [50, 16, 33, 45, "fileName"], [50, 24, 33, 45], [50, 26, 33, 45, "_jsxFileName"], [50, 38, 33, 45], [51, 16, 33, 45, "lineNumber"], [51, 26, 33, 45], [52, 16, 33, 45, "columnNumber"], [52, 28, 33, 45], [53, 14, 33, 45], [53, 21, 34, 18], [53, 22, 34, 19], [54, 12, 34, 19], [54, 15, 30, 40, "key"], [54, 18, 30, 43], [55, 14, 30, 43, "fileName"], [55, 22, 30, 43], [55, 24, 30, 43, "_jsxFileName"], [55, 36, 30, 43], [56, 14, 30, 43, "lineNumber"], [56, 24, 30, 43], [57, 14, 30, 43, "columnNumber"], [57, 26, 30, 43], [58, 12, 30, 43], [58, 19, 35, 16], [58, 20, 36, 8], [58, 21, 36, 9], [59, 10, 37, 6], [60, 8, 38, 4], [61, 8, 40, 4], [61, 28, 40, 11], [61, 32, 40, 11, "_jsxDevRuntime"], [61, 46, 40, 11], [61, 47, 40, 11, "jsxDEV"], [61, 53, 40, 11], [61, 55, 40, 12, "View"], [61, 59, 40, 16], [62, 10, 40, 17, "style"], [62, 15, 40, 22], [62, 17, 40, 24, "styles"], [62, 23, 40, 30], [62, 24, 40, 31, "container"], [62, 33, 40, 41], [63, 10, 40, 41, "children"], [63, 18, 40, 41], [63, 20, 40, 43, "items"], [64, 8, 40, 48], [65, 10, 40, 48, "fileName"], [65, 18, 40, 48], [65, 20, 40, 48, "_jsxFileName"], [65, 32, 40, 48], [66, 10, 40, 48, "lineNumber"], [66, 20, 40, 48], [67, 10, 40, 48, "columnNumber"], [67, 22, 40, 48], [68, 8, 40, 48], [68, 15, 40, 55], [68, 16, 40, 56], [69, 6, 41, 2], [70, 4, 41, 3], [71, 2, 41, 3], [71, 4, 21, 33, "React"], [71, 18, 21, 38], [71, 19, 21, 39, "Component"], [71, 28, 21, 48], [72, 2, 44, 0], [72, 6, 44, 6, "styles"], [72, 12, 44, 12], [72, 15, 44, 15, "StyleSheet"], [72, 25, 44, 25], [72, 26, 44, 26, "create"], [72, 32, 44, 32], [72, 33, 44, 33], [73, 4, 45, 2, "container"], [73, 13, 45, 11], [73, 15, 45, 13], [74, 6, 46, 4, "height"], [74, 12, 46, 10], [74, 14, 46, 12], [74, 17, 46, 15], [75, 6, 47, 4, "paddingTop"], [75, 16, 47, 14], [75, 18, 47, 16], [76, 4, 48, 2], [76, 5, 48, 3], [77, 4, 49, 2, "label"], [77, 9, 49, 7], [77, 11, 49, 9], [78, 6, 50, 4, "flex"], [78, 10, 50, 8], [78, 12, 50, 10], [79, 4, 51, 2], [79, 5, 51, 3], [80, 4, 52, 2, "row"], [80, 7, 52, 5], [80, 9, 52, 7], [81, 6, 53, 4, "flexDirection"], [81, 19, 53, 17], [81, 21, 53, 19], [81, 26, 53, 24], [82, 6, 54, 4, "paddingHorizontal"], [82, 23, 54, 21], [82, 25, 54, 23], [83, 4, 55, 2], [83, 5, 55, 3], [84, 4, 56, 2, "text"], [84, 8, 56, 6], [84, 10, 56, 8], [85, 6, 57, 4, "color"], [85, 11, 57, 9], [85, 13, 57, 11], [85, 20, 57, 18], [86, 6, 58, 4, "fontSize"], [86, 14, 58, 12], [86, 16, 58, 14], [87, 4, 59, 2], [87, 5, 59, 3], [88, 4, 60, 2, "totalTime"], [88, 13, 60, 11], [88, 15, 60, 13], [89, 6, 61, 4, "paddingRight"], [89, 18, 61, 16], [89, 20, 61, 18], [90, 4, 62, 2], [91, 2, 63, 0], [91, 3, 63, 1], [91, 4, 63, 2], [92, 2, 63, 3], [92, 6, 63, 3, "_default"], [92, 14, 63, 3], [92, 17, 63, 3, "exports"], [92, 24, 63, 3], [92, 25, 63, 3, "default"], [92, 32, 63, 3], [92, 35, 65, 15, "PerformanceOverlay"], [92, 53, 65, 33], [93, 0, 65, 33], [93, 3]], "functionMap": {"names": ["<global>", "PerformanceOverlay", "render"], "mappings": "AAA;ACoB;ECC;GDmB;CDC"}}, "type": "js/module"}]}