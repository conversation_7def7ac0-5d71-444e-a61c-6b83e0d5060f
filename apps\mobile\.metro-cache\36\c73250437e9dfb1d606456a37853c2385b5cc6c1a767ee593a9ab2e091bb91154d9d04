{"dependencies": [{"name": "../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 132}, "end": {"line": 6, "column": 41, "index": 173}}], "key": "ByXat9lt9duIJLDmSeH0V+tRq1s=", "exportNames": ["*"]}}, {"name": "react-native-reanimated", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 32, "column": 15, "index": 838}, "end": {"line": 32, "column": 49, "index": 872}}], "key": "+aUP6OdvebG47hiJSteO076+5ZE=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Reanimated = void 0;\n  var _utils = require(_dependencyMap[0], \"../../utils\");\n  var Reanimated;\n  try {\n    exports.Reanimated = Reanimated = require(_dependencyMap[1], \"react-native-reanimated\");\n  } catch (e) {\n    // When 'react-native-reanimated' is not available we want to quietly continue\n    // @ts-ignore TS demands the variable to be initialized\n    exports.Reanimated = Reanimated = undefined;\n  }\n  if (!Reanimated?.useSharedValue) {\n    // @ts-ignore Make sure the loaded module is actually Reanimated, if it's not\n    // reset the module to undefined so we can fallback to the default implementation\n    exports.Reanimated = Reanimated = undefined;\n  }\n  var _worklet_7900358435246_init_data = {\n    code: \"function reanimatedWrapperTs1(){const{tagMessage}=this.__closure;console.warn(tagMessage('Please use newer version of react-native-reanimated in order to control state of the gestures.'));}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\reanimatedWrapper.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reanimatedWrapperTs1\\\",\\\"tagMessage\\\",\\\"__closure\\\",\\\"console\\\",\\\"warn\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/reanimatedWrapper.ts\\\"],\\\"mappings\\\":\\\"AA8C+B,SAAAA,oBAAMA,CAAA,QAAAC,UAAA,OAAAC,SAAA,CAEjCC,OAAO,CAACC,IAAI,CACVH,UAAU,CACR,gGACF,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  if (Reanimated !== undefined && !Reanimated.setGestureState) {\n    // The loaded module is Reanimated but it doesn't have the setGestureState defined\n    Reanimated.setGestureState = function () {\n      var _e = [new global.Error(), -2, -27];\n      var reanimatedWrapperTs1 = function () {\n        console.warn((0, _utils.tagMessage)('Please use newer version of react-native-reanimated in order to control state of the gestures.'));\n      };\n      reanimatedWrapperTs1.__closure = {\n        tagMessage: _utils.tagMessage\n      };\n      reanimatedWrapperTs1.__workletHash = 7900358435246;\n      reanimatedWrapperTs1.__initData = _worklet_7900358435246_init_data;\n      reanimatedWrapperTs1.__stackDetails = _e;\n      return reanimatedWrapperTs1;\n    }();\n  }\n});", "lineCount": 42, "map": [[6, 2, 6, 0], [6, 6, 6, 0, "_utils"], [6, 12, 6, 0], [6, 15, 6, 0, "require"], [6, 22, 6, 0], [6, 23, 6, 0, "_dependencyMap"], [6, 37, 6, 0], [7, 2, 12, 0], [7, 6, 12, 4, "Reanimated"], [7, 16, 29, 13], [8, 2, 31, 0], [8, 6, 31, 4], [9, 4, 32, 2, "exports"], [9, 11, 32, 2], [9, 12, 32, 2, "Reanimated"], [9, 22, 32, 2], [9, 25, 32, 2, "Reanimated"], [9, 35, 32, 12], [9, 38, 32, 15, "require"], [9, 45, 32, 22], [9, 46, 32, 22, "_dependencyMap"], [9, 60, 32, 22], [9, 90, 32, 48], [9, 91, 32, 49], [10, 2, 33, 0], [10, 3, 33, 1], [10, 4, 33, 2], [10, 11, 33, 9, "e"], [10, 12, 33, 10], [10, 14, 33, 12], [11, 4, 34, 2], [12, 4, 35, 2], [13, 4, 36, 2, "exports"], [13, 11, 36, 2], [13, 12, 36, 2, "Reanimated"], [13, 22, 36, 2], [13, 25, 36, 2, "Reanimated"], [13, 35, 36, 12], [13, 38, 36, 15, "undefined"], [13, 47, 36, 24], [14, 2, 37, 0], [15, 2, 39, 0], [15, 6, 39, 4], [15, 7, 39, 5, "Reanimated"], [15, 17, 39, 15], [15, 19, 39, 17, "useSharedValue"], [15, 33, 39, 31], [15, 35, 39, 33], [16, 4, 40, 2], [17, 4, 41, 2], [18, 4, 42, 2, "exports"], [18, 11, 42, 2], [18, 12, 42, 2, "Reanimated"], [18, 22, 42, 2], [18, 25, 42, 2, "Reanimated"], [18, 35, 42, 12], [18, 38, 42, 15, "undefined"], [18, 47, 42, 24], [19, 2, 43, 0], [20, 2, 43, 1], [20, 6, 43, 1, "_worklet_7900358435246_init_data"], [20, 38, 43, 1], [21, 4, 43, 1, "code"], [21, 8, 43, 1], [22, 4, 43, 1, "location"], [22, 12, 43, 1], [23, 4, 43, 1, "sourceMap"], [23, 13, 43, 1], [24, 4, 43, 1, "version"], [24, 11, 43, 1], [25, 2, 43, 1], [26, 2, 45, 0], [26, 6, 45, 4, "Reanimated"], [26, 16, 45, 14], [26, 21, 45, 19, "undefined"], [26, 30, 45, 28], [26, 34, 45, 32], [26, 35, 45, 33, "Reanimated"], [26, 45, 45, 43], [26, 46, 45, 44, "setGestureState"], [26, 61, 45, 59], [26, 63, 45, 61], [27, 4, 46, 2], [28, 4, 47, 2, "Reanimated"], [28, 14, 47, 12], [28, 15, 47, 13, "setGestureState"], [28, 30, 47, 28], [28, 33, 47, 31], [29, 6, 47, 31], [29, 10, 47, 31, "_e"], [29, 12, 47, 31], [29, 20, 47, 31, "global"], [29, 26, 47, 31], [29, 27, 47, 31, "Error"], [29, 32, 47, 31], [30, 6, 47, 31], [30, 10, 47, 31, "reanimatedWrapperTs1"], [30, 30, 47, 31], [30, 42, 47, 31, "reanimatedWrapperTs1"], [30, 43, 47, 31], [30, 45, 47, 37], [31, 8, 49, 4, "console"], [31, 15, 49, 11], [31, 16, 49, 12, "warn"], [31, 20, 49, 16], [31, 21, 50, 6], [31, 25, 50, 6, "tagMessage"], [31, 42, 50, 16], [31, 44, 51, 8], [31, 140, 52, 6], [31, 141, 53, 4], [31, 142, 53, 5], [32, 6, 54, 2], [32, 7, 54, 3], [33, 6, 54, 3, "reanimatedWrapperTs1"], [33, 26, 54, 3], [33, 27, 54, 3, "__closure"], [33, 36, 54, 3], [34, 8, 54, 3, "tagMessage"], [34, 18, 54, 3], [34, 20, 50, 6, "tagMessage"], [35, 6, 50, 16], [36, 6, 50, 16, "reanimatedWrapperTs1"], [36, 26, 50, 16], [36, 27, 50, 16, "__workletHash"], [36, 40, 50, 16], [37, 6, 50, 16, "reanimatedWrapperTs1"], [37, 26, 50, 16], [37, 27, 50, 16, "__initData"], [37, 37, 50, 16], [37, 40, 50, 16, "_worklet_7900358435246_init_data"], [37, 72, 50, 16], [38, 6, 50, 16, "reanimatedWrapperTs1"], [38, 26, 50, 16], [38, 27, 50, 16, "__stackDetails"], [38, 41, 50, 16], [38, 44, 50, 16, "_e"], [38, 46, 50, 16], [39, 6, 50, 16], [39, 13, 50, 16, "reanimatedWrapperTs1"], [39, 33, 50, 16], [40, 4, 50, 16], [40, 5, 47, 31], [40, 7, 54, 3], [41, 2, 55, 0], [42, 0, 55, 1], [42, 3]], "functionMap": {"names": ["<global>", "Reanimated.setGestureState"], "mappings": "AAA;+BC8C;GDO"}}, "type": "js/module"}]}