{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./ExpoFontLoader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 46, "index": 46}}], "key": "7dk3JQGwGYesJt8OOG3pkBz+dtE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isLoadedInCache = isLoadedInCache;\n  exports.isLoadedNative = isLoadedNative;\n  exports.loadPromises = void 0;\n  exports.markLoaded = markLoaded;\n  exports.purgeCache = purgeCache;\n  exports.purgeFontFamilyFromCache = purgeFontFamilyFromCache;\n  var _ExpoFontLoader = _interopRequireDefault(require(_dependencyMap[1], \"./ExpoFontLoader\"));\n  var loadPromises = exports.loadPromises = {};\n  // cache the value on the js side for fast access to the fonts that are loaded\n  var cache = {};\n  function markLoaded(fontFamily) {\n    cache[fontFamily] = true;\n  }\n  function isLoadedInCache(fontFamily) {\n    return fontFamily in cache;\n  }\n  function isLoadedNative(fontFamily) {\n    if (isLoadedInCache(fontFamily)) {\n      return true;\n    } else {\n      var loadedNativeFonts = _ExpoFontLoader.default.getLoadedFonts();\n      // NOTE(brentvatne): Bail out here if there are no loaded fonts. This\n      // is functionally equivalent to the behavior below if the returned array\n      // is empty, but this handles improper mocking of `getLoadedFonts`.\n      if (!loadedNativeFonts?.length) {\n        return false;\n      }\n      loadedNativeFonts.forEach(font => {\n        cache[font] = true;\n      });\n      return fontFamily in cache;\n    }\n  }\n  function purgeFontFamilyFromCache(fontFamily) {\n    delete cache[fontFamily];\n  }\n  function purgeCache() {\n    cache = {};\n  }\n});", "lineCount": 45, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_ExpoFontLoader"], [12, 21, 1, 0], [12, 24, 1, 0, "_interopRequireDefault"], [12, 46, 1, 0], [12, 47, 1, 0, "require"], [12, 54, 1, 0], [12, 55, 1, 0, "_dependencyMap"], [12, 69, 1, 0], [13, 2, 2, 7], [13, 6, 2, 13, "loadPromises"], [13, 18, 2, 25], [13, 21, 2, 25, "exports"], [13, 28, 2, 25], [13, 29, 2, 25, "loadPromises"], [13, 41, 2, 25], [13, 44, 2, 28], [13, 45, 2, 29], [13, 46, 2, 30], [14, 2, 3, 0], [15, 2, 4, 0], [15, 6, 4, 4, "cache"], [15, 11, 4, 9], [15, 14, 4, 12], [15, 15, 4, 13], [15, 16, 4, 14], [16, 2, 5, 7], [16, 11, 5, 16, "markLoaded"], [16, 21, 5, 26, "markLoaded"], [16, 22, 5, 27, "fontFamily"], [16, 32, 5, 37], [16, 34, 5, 39], [17, 4, 6, 4, "cache"], [17, 9, 6, 9], [17, 10, 6, 10, "fontFamily"], [17, 20, 6, 20], [17, 21, 6, 21], [17, 24, 6, 24], [17, 28, 6, 28], [18, 2, 7, 0], [19, 2, 8, 7], [19, 11, 8, 16, "isLoadedInCache"], [19, 26, 8, 31, "isLoadedInCache"], [19, 27, 8, 32, "fontFamily"], [19, 37, 8, 42], [19, 39, 8, 44], [20, 4, 9, 4], [20, 11, 9, 11, "fontFamily"], [20, 21, 9, 21], [20, 25, 9, 25, "cache"], [20, 30, 9, 30], [21, 2, 10, 0], [22, 2, 11, 7], [22, 11, 11, 16, "isLoadedNative"], [22, 25, 11, 30, "isLoadedNative"], [22, 26, 11, 31, "fontFamily"], [22, 36, 11, 41], [22, 38, 11, 43], [23, 4, 12, 4], [23, 8, 12, 8, "isLoadedInCache"], [23, 23, 12, 23], [23, 24, 12, 24, "fontFamily"], [23, 34, 12, 34], [23, 35, 12, 35], [23, 37, 12, 37], [24, 6, 13, 8], [24, 13, 13, 15], [24, 17, 13, 19], [25, 4, 14, 4], [25, 5, 14, 5], [25, 11, 15, 9], [26, 6, 16, 8], [26, 10, 16, 14, "loadedNativeFonts"], [26, 27, 16, 31], [26, 30, 16, 34, "ExpoFontLoader"], [26, 53, 16, 48], [26, 54, 16, 49, "getLoadedFonts"], [26, 68, 16, 63], [26, 69, 16, 64], [26, 70, 16, 65], [27, 6, 17, 8], [28, 6, 18, 8], [29, 6, 19, 8], [30, 6, 20, 8], [30, 10, 20, 12], [30, 11, 20, 13, "loadedNativeFonts"], [30, 28, 20, 30], [30, 30, 20, 32, "length"], [30, 36, 20, 38], [30, 38, 20, 40], [31, 8, 21, 12], [31, 15, 21, 19], [31, 20, 21, 24], [32, 6, 22, 8], [33, 6, 23, 8, "loadedNativeFonts"], [33, 23, 23, 25], [33, 24, 23, 26, "for<PERSON>ach"], [33, 31, 23, 33], [33, 32, 23, 35, "font"], [33, 36, 23, 39], [33, 40, 23, 44], [34, 8, 24, 12, "cache"], [34, 13, 24, 17], [34, 14, 24, 18, "font"], [34, 18, 24, 22], [34, 19, 24, 23], [34, 22, 24, 26], [34, 26, 24, 30], [35, 6, 25, 8], [35, 7, 25, 9], [35, 8, 25, 10], [36, 6, 26, 8], [36, 13, 26, 15, "fontFamily"], [36, 23, 26, 25], [36, 27, 26, 29, "cache"], [36, 32, 26, 34], [37, 4, 27, 4], [38, 2, 28, 0], [39, 2, 29, 7], [39, 11, 29, 16, "purgeFontFamilyFromCache"], [39, 35, 29, 40, "purgeFontFamilyFromCache"], [39, 36, 29, 41, "fontFamily"], [39, 46, 29, 51], [39, 48, 29, 53], [40, 4, 30, 4], [40, 11, 30, 11, "cache"], [40, 16, 30, 16], [40, 17, 30, 17, "fontFamily"], [40, 27, 30, 27], [40, 28, 30, 28], [41, 2, 31, 0], [42, 2, 32, 7], [42, 11, 32, 16, "purge<PERSON>ache"], [42, 21, 32, 26, "purge<PERSON>ache"], [42, 22, 32, 26], [42, 24, 32, 29], [43, 4, 33, 4, "cache"], [43, 9, 33, 9], [43, 12, 33, 12], [43, 13, 33, 13], [43, 14, 33, 14], [44, 2, 34, 0], [45, 0, 34, 1], [45, 3]], "functionMap": {"names": ["<global>", "markLoaded", "isLoadedInCache", "isLoadedNative", "loadedNativeFonts.forEach$argument_0", "purgeFontFamilyFromCache", "purge<PERSON>ache"], "mappings": "AAA;OCI;CDE;OEC;CFE;OGC;kCCY;SDE;CHG;OKC;CLE;OMC;CNE"}}, "type": "js/module"}]}