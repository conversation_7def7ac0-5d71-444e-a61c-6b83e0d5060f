{"dependencies": [{"name": "./escapeHTML", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 41, "index": 283}, "end": {"line": 13, "column": 64, "index": 306}}], "key": "K6hg8PCwDNrCTZt2oFF1EGfqRKg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, '__esModule', {\n    value: true\n  });\n  exports.printText = exports.printProps = exports.printElementAsLeaf = exports.printElement = exports.printComment = exports.printChildren = void 0;\n  var _escapeHTML = _interopRequireDefault(require(_dependencyMap[0], \"./escapeHTML\"));\n  function _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n      default: obj\n    };\n  }\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  // Return empty string if keys is empty.\n  var printProps = (keys, props, config, indentation, depth, refs, printer) => {\n    var indentationNext = indentation + config.indent;\n    var colors = config.colors;\n    return keys.map(key => {\n      var value = props[key];\n      var printed = printer(value, config, indentationNext, depth, refs);\n      if (typeof value !== 'string') {\n        if (printed.indexOf('\\n') !== -1) {\n          printed = config.spacingOuter + indentationNext + printed + config.spacingOuter + indentation;\n        }\n        printed = `{${printed}}`;\n      }\n      return `${config.spacingInner + indentation + colors.prop.open + key + colors.prop.close}=${colors.value.open}${printed}${colors.value.close}`;\n    }).join('');\n  };\n\n  // Return empty string if children is empty.\n  exports.printProps = printProps;\n  var printChildren = (children, config, indentation, depth, refs, printer) => children.map(child => config.spacingOuter + indentation + (typeof child === 'string' ? printText(child, config) : printer(child, config, indentation, depth, refs))).join('');\n  exports.printChildren = printChildren;\n  var printText = (text, config) => {\n    var contentColor = config.colors.content;\n    return contentColor.open + (0, _escapeHTML.default)(text) + contentColor.close;\n  };\n  exports.printText = printText;\n  var printComment = (comment, config) => {\n    var commentColor = config.colors.comment;\n    return `${commentColor.open}<!--${(0, _escapeHTML.default)(comment)}-->${commentColor.close}`;\n  };\n\n  // Separate the functions to format props, children, and element,\n  // so a plugin could override a particular function, if needed.\n  // Too bad, so sad: the traditional (but unnecessary) space\n  // in a self-closing tagColor requires a second test of printedProps.\n  exports.printComment = printComment;\n  var printElement = (type, printedProps, printedChildren, config, indentation) => {\n    var tagColor = config.colors.tag;\n    return `${tagColor.open}<${type}${printedProps && tagColor.close + printedProps + config.spacingOuter + indentation + tagColor.open}${printedChildren ? `>${tagColor.close}${printedChildren}${config.spacingOuter}${indentation}${tagColor.open}</${type}` : `${printedProps && !config.min ? '' : ' '}/`}>${tagColor.close}`;\n  };\n  exports.printElement = printElement;\n  var printElementAsLeaf = (type, config) => {\n    var tagColor = config.colors.tag;\n    return `${tagColor.open}<${type}${tagColor.close} …${tagColor.open} />${tagColor.close}`;\n  };\n  exports.printElementAsLeaf = printElementAsLeaf;\n});", "lineCount": 67, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8, "printText"], [7, 19, 6, 17], [7, 22, 7, 2, "exports"], [7, 29, 7, 9], [7, 30, 7, 10, "printProps"], [7, 40, 7, 20], [7, 43, 8, 2, "exports"], [7, 50, 8, 9], [7, 51, 8, 10, "printElementAsLeaf"], [7, 69, 8, 28], [7, 72, 9, 2, "exports"], [7, 79, 9, 9], [7, 80, 9, 10, "printElement"], [7, 92, 9, 22], [7, 95, 10, 2, "exports"], [7, 102, 10, 9], [7, 103, 10, 10, "printComment"], [7, 115, 10, 22], [7, 118, 11, 2, "exports"], [7, 125, 11, 9], [7, 126, 11, 10, "printChildren"], [7, 139, 11, 23], [7, 142, 12, 4], [7, 147, 12, 9], [7, 148, 12, 10], [8, 2, 13, 0], [8, 6, 13, 4, "_escapeHTML"], [8, 17, 13, 15], [8, 20, 13, 18, "_interopRequireDefault"], [8, 42, 13, 40], [8, 43, 13, 41, "require"], [8, 50, 13, 48], [8, 51, 13, 48, "_dependencyMap"], [8, 65, 13, 48], [8, 84, 13, 63], [8, 85, 13, 64], [8, 86, 13, 65], [9, 2, 14, 0], [9, 11, 14, 9, "_interopRequireDefault"], [9, 33, 14, 31, "_interopRequireDefault"], [9, 34, 14, 32, "obj"], [9, 37, 14, 35], [9, 39, 14, 37], [10, 4, 15, 2], [10, 11, 15, 9, "obj"], [10, 14, 15, 12], [10, 18, 15, 16, "obj"], [10, 21, 15, 19], [10, 22, 15, 20, "__esModule"], [10, 32, 15, 30], [10, 35, 15, 33, "obj"], [10, 38, 15, 36], [10, 41, 15, 39], [11, 6, 15, 40, "default"], [11, 13, 15, 47], [11, 15, 15, 49, "obj"], [12, 4, 15, 52], [12, 5, 15, 53], [13, 2, 16, 0], [14, 2, 17, 0], [15, 0, 18, 0], [16, 0, 19, 0], [17, 0, 20, 0], [18, 0, 21, 0], [19, 0, 22, 0], [21, 2, 24, 0], [22, 2, 25, 0], [22, 6, 25, 6, "printProps"], [22, 16, 25, 16], [22, 19, 25, 19, "printProps"], [22, 20, 25, 20, "keys"], [22, 24, 25, 24], [22, 26, 25, 26, "props"], [22, 31, 25, 31], [22, 33, 25, 33, "config"], [22, 39, 25, 39], [22, 41, 25, 41, "indentation"], [22, 52, 25, 52], [22, 54, 25, 54, "depth"], [22, 59, 25, 59], [22, 61, 25, 61, "refs"], [22, 65, 25, 65], [22, 67, 25, 67, "printer"], [22, 74, 25, 74], [22, 79, 25, 79], [23, 4, 26, 2], [23, 8, 26, 8, "indentationNext"], [23, 23, 26, 23], [23, 26, 26, 26, "indentation"], [23, 37, 26, 37], [23, 40, 26, 40, "config"], [23, 46, 26, 46], [23, 47, 26, 47, "indent"], [23, 53, 26, 53], [24, 4, 27, 2], [24, 8, 27, 8, "colors"], [24, 14, 27, 14], [24, 17, 27, 17, "config"], [24, 23, 27, 23], [24, 24, 27, 24, "colors"], [24, 30, 27, 30], [25, 4, 28, 2], [25, 11, 28, 9, "keys"], [25, 15, 28, 13], [25, 16, 29, 5, "map"], [25, 19, 29, 8], [25, 20, 29, 9, "key"], [25, 23, 29, 12], [25, 27, 29, 16], [26, 6, 30, 6], [26, 10, 30, 12, "value"], [26, 15, 30, 17], [26, 18, 30, 20, "props"], [26, 23, 30, 25], [26, 24, 30, 26, "key"], [26, 27, 30, 29], [26, 28, 30, 30], [27, 6, 31, 6], [27, 10, 31, 10, "printed"], [27, 17, 31, 17], [27, 20, 31, 20, "printer"], [27, 27, 31, 27], [27, 28, 31, 28, "value"], [27, 33, 31, 33], [27, 35, 31, 35, "config"], [27, 41, 31, 41], [27, 43, 31, 43, "indentationNext"], [27, 58, 31, 58], [27, 60, 31, 60, "depth"], [27, 65, 31, 65], [27, 67, 31, 67, "refs"], [27, 71, 31, 71], [27, 72, 31, 72], [28, 6, 32, 6], [28, 10, 32, 10], [28, 17, 32, 17, "value"], [28, 22, 32, 22], [28, 27, 32, 27], [28, 35, 32, 35], [28, 37, 32, 37], [29, 8, 33, 8], [29, 12, 33, 12, "printed"], [29, 19, 33, 19], [29, 20, 33, 20, "indexOf"], [29, 27, 33, 27], [29, 28, 33, 28], [29, 32, 33, 32], [29, 33, 33, 33], [29, 38, 33, 38], [29, 39, 33, 39], [29, 40, 33, 40], [29, 42, 33, 42], [30, 10, 34, 10, "printed"], [30, 17, 34, 17], [30, 20, 35, 12, "config"], [30, 26, 35, 18], [30, 27, 35, 19, "spacingOuter"], [30, 39, 35, 31], [30, 42, 36, 12, "indentationNext"], [30, 57, 36, 27], [30, 60, 37, 12, "printed"], [30, 67, 37, 19], [30, 70, 38, 12, "config"], [30, 76, 38, 18], [30, 77, 38, 19, "spacingOuter"], [30, 89, 38, 31], [30, 92, 39, 12, "indentation"], [30, 103, 39, 23], [31, 8, 40, 8], [32, 8, 41, 8, "printed"], [32, 15, 41, 15], [32, 18, 41, 18], [32, 22, 41, 22, "printed"], [32, 29, 41, 29], [32, 32, 41, 32], [33, 6, 42, 6], [34, 6, 43, 6], [34, 13, 43, 13], [34, 16, 44, 8, "config"], [34, 22, 44, 14], [34, 23, 44, 15, "spacingInner"], [34, 35, 44, 27], [34, 38, 45, 8, "indentation"], [34, 49, 45, 19], [34, 52, 46, 8, "colors"], [34, 58, 46, 14], [34, 59, 46, 15, "prop"], [34, 63, 46, 19], [34, 64, 46, 20, "open"], [34, 68, 46, 24], [34, 71, 47, 8, "key"], [34, 74, 47, 11], [34, 77, 48, 8, "colors"], [34, 83, 48, 14], [34, 84, 48, 15, "prop"], [34, 88, 48, 19], [34, 89, 48, 20, "close"], [34, 94, 48, 25], [34, 98, 49, 10, "colors"], [34, 104, 49, 16], [34, 105, 49, 17, "value"], [34, 110, 49, 22], [34, 111, 49, 23, "open"], [34, 115, 49, 27], [34, 118, 49, 30, "printed"], [34, 125, 49, 37], [34, 128, 49, 40, "colors"], [34, 134, 49, 46], [34, 135, 49, 47, "value"], [34, 140, 49, 52], [34, 141, 49, 53, "close"], [34, 146, 49, 58], [34, 148, 49, 60], [35, 4, 50, 4], [35, 5, 50, 5], [35, 6, 50, 6], [35, 7, 51, 5, "join"], [35, 11, 51, 9], [35, 12, 51, 10], [35, 14, 51, 12], [35, 15, 51, 13], [36, 2, 52, 0], [36, 3, 52, 1], [38, 2, 54, 0], [39, 2, 55, 0, "exports"], [39, 9, 55, 7], [39, 10, 55, 8, "printProps"], [39, 20, 55, 18], [39, 23, 55, 21, "printProps"], [39, 33, 55, 31], [40, 2, 56, 0], [40, 6, 56, 6, "printChildren"], [40, 19, 56, 19], [40, 22, 56, 22, "printChildren"], [40, 23, 56, 23, "children"], [40, 31, 56, 31], [40, 33, 56, 33, "config"], [40, 39, 56, 39], [40, 41, 56, 41, "indentation"], [40, 52, 56, 52], [40, 54, 56, 54, "depth"], [40, 59, 56, 59], [40, 61, 56, 61, "refs"], [40, 65, 56, 65], [40, 67, 56, 67, "printer"], [40, 74, 56, 74], [40, 79, 57, 2, "children"], [40, 87, 57, 10], [40, 88, 58, 5, "map"], [40, 91, 58, 8], [40, 92, 59, 6, "child"], [40, 97, 59, 11], [40, 101, 60, 8, "config"], [40, 107, 60, 14], [40, 108, 60, 15, "spacingOuter"], [40, 120, 60, 27], [40, 123, 61, 8, "indentation"], [40, 134, 61, 19], [40, 138, 62, 9], [40, 145, 62, 16, "child"], [40, 150, 62, 21], [40, 155, 62, 26], [40, 163, 62, 34], [40, 166, 63, 12, "printText"], [40, 175, 63, 21], [40, 176, 63, 22, "child"], [40, 181, 63, 27], [40, 183, 63, 29, "config"], [40, 189, 63, 35], [40, 190, 63, 36], [40, 193, 64, 12, "printer"], [40, 200, 64, 19], [40, 201, 64, 20, "child"], [40, 206, 64, 25], [40, 208, 64, 27, "config"], [40, 214, 64, 33], [40, 216, 64, 35, "indentation"], [40, 227, 64, 46], [40, 229, 64, 48, "depth"], [40, 234, 64, 53], [40, 236, 64, 55, "refs"], [40, 240, 64, 59], [40, 241, 64, 60], [40, 242, 65, 4], [40, 243, 65, 5], [40, 244, 66, 5, "join"], [40, 248, 66, 9], [40, 249, 66, 10], [40, 251, 66, 12], [40, 252, 66, 13], [41, 2, 67, 0, "exports"], [41, 9, 67, 7], [41, 10, 67, 8, "printChildren"], [41, 23, 67, 21], [41, 26, 67, 24, "printChildren"], [41, 39, 67, 37], [42, 2, 68, 0], [42, 6, 68, 6, "printText"], [42, 15, 68, 15], [42, 18, 68, 18, "printText"], [42, 19, 68, 19, "text"], [42, 23, 68, 23], [42, 25, 68, 25, "config"], [42, 31, 68, 31], [42, 36, 68, 36], [43, 4, 69, 2], [43, 8, 69, 8, "contentColor"], [43, 20, 69, 20], [43, 23, 69, 23, "config"], [43, 29, 69, 29], [43, 30, 69, 30, "colors"], [43, 36, 69, 36], [43, 37, 69, 37, "content"], [43, 44, 69, 44], [44, 4, 70, 2], [44, 11, 71, 4, "contentColor"], [44, 23, 71, 16], [44, 24, 71, 17, "open"], [44, 28, 71, 21], [44, 31, 71, 24], [44, 32, 71, 25], [44, 33, 71, 26], [44, 35, 71, 28, "_escapeHTML"], [44, 46, 71, 39], [44, 47, 71, 40, "default"], [44, 54, 71, 47], [44, 56, 71, 49, "text"], [44, 60, 71, 53], [44, 61, 71, 54], [44, 64, 71, 57, "contentColor"], [44, 76, 71, 69], [44, 77, 71, 70, "close"], [44, 82, 71, 75], [45, 2, 73, 0], [45, 3, 73, 1], [46, 2, 74, 0, "exports"], [46, 9, 74, 7], [46, 10, 74, 8, "printText"], [46, 19, 74, 17], [46, 22, 74, 20, "printText"], [46, 31, 74, 29], [47, 2, 75, 0], [47, 6, 75, 6, "printComment"], [47, 18, 75, 18], [47, 21, 75, 21, "printComment"], [47, 22, 75, 22, "comment"], [47, 29, 75, 29], [47, 31, 75, 31, "config"], [47, 37, 75, 37], [47, 42, 75, 42], [48, 4, 76, 2], [48, 8, 76, 8, "commentColor"], [48, 20, 76, 20], [48, 23, 76, 23, "config"], [48, 29, 76, 29], [48, 30, 76, 30, "colors"], [48, 36, 76, 36], [48, 37, 76, 37, "comment"], [48, 44, 76, 44], [49, 4, 77, 2], [49, 11, 77, 9], [49, 14, 77, 12, "commentColor"], [49, 26, 77, 24], [49, 27, 77, 25, "open"], [49, 31, 77, 29], [49, 38, 77, 36], [49, 39, 77, 37], [49, 40, 77, 38], [49, 42, 77, 40, "_escapeHTML"], [49, 53, 77, 51], [49, 54, 77, 52, "default"], [49, 61, 77, 59], [49, 63, 77, 61, "comment"], [49, 70, 77, 68], [49, 71, 77, 69], [49, 77, 78, 4, "commentColor"], [49, 89, 78, 16], [49, 90, 78, 17, "close"], [49, 95, 78, 22], [49, 97, 79, 4], [50, 2, 80, 0], [50, 3, 80, 1], [52, 2, 82, 0], [53, 2, 83, 0], [54, 2, 84, 0], [55, 2, 85, 0], [56, 2, 86, 0, "exports"], [56, 9, 86, 7], [56, 10, 86, 8, "printComment"], [56, 22, 86, 20], [56, 25, 86, 23, "printComment"], [56, 37, 86, 35], [57, 2, 87, 0], [57, 6, 87, 6, "printElement"], [57, 18, 87, 18], [57, 21, 87, 21, "printElement"], [57, 22, 88, 2, "type"], [57, 26, 88, 6], [57, 28, 89, 2, "printedProps"], [57, 40, 89, 14], [57, 42, 90, 2, "printedChildren"], [57, 57, 90, 17], [57, 59, 91, 2, "config"], [57, 65, 91, 8], [57, 67, 92, 2, "indentation"], [57, 78, 92, 13], [57, 83, 93, 5], [58, 4, 94, 2], [58, 8, 94, 8, "tagColor"], [58, 16, 94, 16], [58, 19, 94, 19, "config"], [58, 25, 94, 25], [58, 26, 94, 26, "colors"], [58, 32, 94, 32], [58, 33, 94, 33, "tag"], [58, 36, 94, 36], [59, 4, 95, 2], [59, 11, 95, 9], [59, 14, 95, 12, "tagColor"], [59, 22, 95, 20], [59, 23, 95, 21, "open"], [59, 27, 95, 25], [59, 31, 95, 29, "type"], [59, 35, 95, 33], [59, 38, 96, 4, "printedProps"], [59, 50, 96, 16], [59, 54, 97, 4, "tagColor"], [59, 62, 97, 12], [59, 63, 97, 13, "close"], [59, 68, 97, 18], [59, 71, 98, 6, "printedProps"], [59, 83, 98, 18], [59, 86, 99, 6, "config"], [59, 92, 99, 12], [59, 93, 99, 13, "spacingOuter"], [59, 105, 99, 25], [59, 108, 100, 6, "indentation"], [59, 119, 100, 17], [59, 122, 101, 6, "tagColor"], [59, 130, 101, 14], [59, 131, 101, 15, "open"], [59, 135, 101, 19], [59, 138, 103, 4, "printedChildren"], [59, 153, 103, 19], [59, 156, 104, 8], [59, 160, 104, 12, "tagColor"], [59, 168, 104, 20], [59, 169, 104, 21, "close"], [59, 174, 104, 26], [59, 177, 104, 29, "printedChildren"], [59, 192, 104, 44], [59, 195, 104, 47, "config"], [59, 201, 104, 53], [59, 202, 104, 54, "spacingOuter"], [59, 214, 104, 66], [59, 217, 104, 69, "indentation"], [59, 228, 104, 80], [59, 231, 104, 83, "tagColor"], [59, 239, 104, 91], [59, 240, 104, 92, "open"], [59, 244, 104, 96], [59, 249, 104, 101, "type"], [59, 253, 104, 105], [59, 255, 104, 107], [59, 258, 105, 8], [59, 261, 105, 11, "printedProps"], [59, 273, 105, 23], [59, 277, 105, 27], [59, 278, 105, 28, "config"], [59, 284, 105, 34], [59, 285, 105, 35, "min"], [59, 288, 105, 38], [59, 291, 105, 41], [59, 293, 105, 43], [59, 296, 105, 46], [59, 299, 105, 49], [59, 302, 105, 52], [59, 306, 106, 6, "tagColor"], [59, 314, 106, 14], [59, 315, 106, 15, "close"], [59, 320, 106, 20], [59, 322, 106, 22], [60, 2, 107, 0], [60, 3, 107, 1], [61, 2, 108, 0, "exports"], [61, 9, 108, 7], [61, 10, 108, 8, "printElement"], [61, 22, 108, 20], [61, 25, 108, 23, "printElement"], [61, 37, 108, 35], [62, 2, 109, 0], [62, 6, 109, 6, "printElementAsLeaf"], [62, 24, 109, 24], [62, 27, 109, 27, "printElementAsLeaf"], [62, 28, 109, 28, "type"], [62, 32, 109, 32], [62, 34, 109, 34, "config"], [62, 40, 109, 40], [62, 45, 109, 45], [63, 4, 110, 2], [63, 8, 110, 8, "tagColor"], [63, 16, 110, 16], [63, 19, 110, 19, "config"], [63, 25, 110, 25], [63, 26, 110, 26, "colors"], [63, 32, 110, 32], [63, 33, 110, 33, "tag"], [63, 36, 110, 36], [64, 4, 111, 2], [64, 11, 111, 9], [64, 14, 111, 12, "tagColor"], [64, 22, 111, 20], [64, 23, 111, 21, "open"], [64, 27, 111, 25], [64, 31, 111, 29, "type"], [64, 35, 111, 33], [64, 38, 111, 36, "tagColor"], [64, 46, 111, 44], [64, 47, 111, 45, "close"], [64, 52, 111, 50], [64, 57, 111, 55, "tagColor"], [64, 65, 111, 63], [64, 66, 111, 64, "open"], [64, 70, 111, 68], [64, 76, 111, 74, "tagColor"], [64, 84, 111, 82], [64, 85, 111, 83, "close"], [64, 90, 111, 88], [64, 92, 111, 90], [65, 2, 112, 0], [65, 3, 112, 1], [66, 2, 113, 0, "exports"], [66, 9, 113, 7], [66, 10, 113, 8, "printElementAsLeaf"], [66, 28, 113, 26], [66, 31, 113, 29, "printElementAsLeaf"], [66, 49, 113, 47], [67, 0, 113, 48], [67, 3]], "functionMap": {"names": ["<global>", "_interopRequireDefault", "printProps", "keys.map$argument_0", "printChildren", "children.map$argument_0", "printText", "printComment", "printElement", "printElementAsLeaf"], "mappings": "AAA;ACa;CDE;mBES;SCI;KDqB;CFE;sBII;MCG;6DDK;aJE;kBME;CNK;qBOE;CPK;qBQO;CRoB;2BSE;CTG"}}, "type": "js/module"}]}