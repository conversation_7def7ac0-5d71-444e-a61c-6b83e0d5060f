{"dependencies": [{"name": "@react-navigation/core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 62, "index": 77}}], "key": "Wm75LgE4xYscVWo0KoLFlflJQCo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 78}, "end": {"line": 4, "column": 31, "index": 109}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./ServerContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 110}, "end": {"line": 5, "column": 51, "index": 161}}], "key": "+Hz1a4I7q6hCkPilSOm0KYt2/D0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 162}, "end": {"line": 6, "column": 48, "index": 210}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ServerContainer = void 0;\n  var _core = require(_dependencyMap[0], \"@react-navigation/core\");\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _ServerContext = require(_dependencyMap[2], \"./ServerContext.js\");\n  var _jsxRuntime = require(_dependencyMap[3], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Container component for server rendering.\n   *\n   * @param props.location Location object to base the initial URL for SSR.\n   * @param props.children Child elements to render the content.\n   * @param props.ref Ref object which contains helper methods.\n   */\n  var ServerContainer = exports.ServerContainer = /*#__PURE__*/React.forwardRef(function ServerContainer(_ref, ref) {\n    var children = _ref.children,\n      location = _ref.location;\n    React.useEffect(() => {\n      console.error(\"'ServerContainer' should only be used on the server with 'react-dom/server' for SSR.\");\n    }, []);\n\n    // eslint-disable-next-line @eslint-react/no-unstable-context-value\n    var current = {};\n    if (ref) {\n      var value = {\n        getCurrentOptions() {\n          return current.options;\n        }\n      };\n\n      // We write to the `ref` during render instead of `React.useImperativeHandle`\n      // This is because `useImperativeHandle` will update the ref after 'commit',\n      // and there's no 'commit' phase during SSR.\n      // Mutating ref during render is unsafe in concurrent mode, but we don't care about it for SSR.\n      if (typeof ref === 'function') {\n        ref(value);\n      } else {\n        ref.current = value;\n      }\n    }\n    return (/*#__PURE__*/\n      // eslint-disable-next-line @eslint-react/no-unstable-context-value\n      (0, _jsxRuntime.jsx)(_ServerContext.ServerContext.Provider, {\n        value: {\n          location\n        },\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_core.CurrentRenderContext.Provider, {\n          value: current,\n          children: children\n        })\n      })\n    );\n  });\n});", "lineCount": 59, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "ServerContainer"], [7, 25, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_ServerContext"], [10, 20, 5, 0], [10, 23, 5, 0, "require"], [10, 30, 5, 0], [10, 31, 5, 0, "_dependencyMap"], [10, 45, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_jsxRuntime"], [11, 17, 6, 0], [11, 20, 6, 0, "require"], [11, 27, 6, 0], [11, 28, 6, 0, "_dependencyMap"], [11, 42, 6, 0], [12, 2, 6, 48], [12, 11, 6, 48, "_interopRequireWildcard"], [12, 35, 6, 48, "e"], [12, 36, 6, 48], [12, 38, 6, 48, "t"], [12, 39, 6, 48], [12, 68, 6, 48, "WeakMap"], [12, 75, 6, 48], [12, 81, 6, 48, "r"], [12, 82, 6, 48], [12, 89, 6, 48, "WeakMap"], [12, 96, 6, 48], [12, 100, 6, 48, "n"], [12, 101, 6, 48], [12, 108, 6, 48, "WeakMap"], [12, 115, 6, 48], [12, 127, 6, 48, "_interopRequireWildcard"], [12, 150, 6, 48], [12, 162, 6, 48, "_interopRequireWildcard"], [12, 163, 6, 48, "e"], [12, 164, 6, 48], [12, 166, 6, 48, "t"], [12, 167, 6, 48], [12, 176, 6, 48, "t"], [12, 177, 6, 48], [12, 181, 6, 48, "e"], [12, 182, 6, 48], [12, 186, 6, 48, "e"], [12, 187, 6, 48], [12, 188, 6, 48, "__esModule"], [12, 198, 6, 48], [12, 207, 6, 48, "e"], [12, 208, 6, 48], [12, 214, 6, 48, "o"], [12, 215, 6, 48], [12, 217, 6, 48, "i"], [12, 218, 6, 48], [12, 220, 6, 48, "f"], [12, 221, 6, 48], [12, 226, 6, 48, "__proto__"], [12, 235, 6, 48], [12, 243, 6, 48, "default"], [12, 250, 6, 48], [12, 252, 6, 48, "e"], [12, 253, 6, 48], [12, 270, 6, 48, "e"], [12, 271, 6, 48], [12, 294, 6, 48, "e"], [12, 295, 6, 48], [12, 320, 6, 48, "e"], [12, 321, 6, 48], [12, 330, 6, 48, "f"], [12, 331, 6, 48], [12, 337, 6, 48, "o"], [12, 338, 6, 48], [12, 341, 6, 48, "t"], [12, 342, 6, 48], [12, 345, 6, 48, "n"], [12, 346, 6, 48], [12, 349, 6, 48, "r"], [12, 350, 6, 48], [12, 358, 6, 48, "o"], [12, 359, 6, 48], [12, 360, 6, 48, "has"], [12, 363, 6, 48], [12, 364, 6, 48, "e"], [12, 365, 6, 48], [12, 375, 6, 48, "o"], [12, 376, 6, 48], [12, 377, 6, 48, "get"], [12, 380, 6, 48], [12, 381, 6, 48, "e"], [12, 382, 6, 48], [12, 385, 6, 48, "o"], [12, 386, 6, 48], [12, 387, 6, 48, "set"], [12, 390, 6, 48], [12, 391, 6, 48, "e"], [12, 392, 6, 48], [12, 394, 6, 48, "f"], [12, 395, 6, 48], [12, 409, 6, 48, "_t"], [12, 411, 6, 48], [12, 415, 6, 48, "e"], [12, 416, 6, 48], [12, 432, 6, 48, "_t"], [12, 434, 6, 48], [12, 441, 6, 48, "hasOwnProperty"], [12, 455, 6, 48], [12, 456, 6, 48, "call"], [12, 460, 6, 48], [12, 461, 6, 48, "e"], [12, 462, 6, 48], [12, 464, 6, 48, "_t"], [12, 466, 6, 48], [12, 473, 6, 48, "i"], [12, 474, 6, 48], [12, 478, 6, 48, "o"], [12, 479, 6, 48], [12, 482, 6, 48, "Object"], [12, 488, 6, 48], [12, 489, 6, 48, "defineProperty"], [12, 503, 6, 48], [12, 508, 6, 48, "Object"], [12, 514, 6, 48], [12, 515, 6, 48, "getOwnPropertyDescriptor"], [12, 539, 6, 48], [12, 540, 6, 48, "e"], [12, 541, 6, 48], [12, 543, 6, 48, "_t"], [12, 545, 6, 48], [12, 552, 6, 48, "i"], [12, 553, 6, 48], [12, 554, 6, 48, "get"], [12, 557, 6, 48], [12, 561, 6, 48, "i"], [12, 562, 6, 48], [12, 563, 6, 48, "set"], [12, 566, 6, 48], [12, 570, 6, 48, "o"], [12, 571, 6, 48], [12, 572, 6, 48, "f"], [12, 573, 6, 48], [12, 575, 6, 48, "_t"], [12, 577, 6, 48], [12, 579, 6, 48, "i"], [12, 580, 6, 48], [12, 584, 6, 48, "f"], [12, 585, 6, 48], [12, 586, 6, 48, "_t"], [12, 588, 6, 48], [12, 592, 6, 48, "e"], [12, 593, 6, 48], [12, 594, 6, 48, "_t"], [12, 596, 6, 48], [12, 607, 6, 48, "f"], [12, 608, 6, 48], [12, 613, 6, 48, "e"], [12, 614, 6, 48], [12, 616, 6, 48, "t"], [12, 617, 6, 48], [13, 2, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 0, 10, 0], [17, 0, 11, 0], [18, 0, 12, 0], [19, 0, 13, 0], [20, 2, 14, 7], [20, 6, 14, 13, "ServerContainer"], [20, 21, 14, 28], [20, 24, 14, 28, "exports"], [20, 31, 14, 28], [20, 32, 14, 28, "ServerContainer"], [20, 47, 14, 28], [20, 50, 14, 31], [20, 63, 14, 44, "React"], [20, 68, 14, 49], [20, 69, 14, 50, "forwardRef"], [20, 79, 14, 60], [20, 80, 14, 61], [20, 89, 14, 70, "ServerContainer"], [20, 104, 14, 85, "ServerContainer"], [20, 105, 14, 85, "_ref"], [20, 109, 14, 85], [20, 111, 17, 3, "ref"], [20, 114, 17, 6], [20, 116, 17, 8], [21, 4, 17, 8], [21, 8, 15, 2, "children"], [21, 16, 15, 10], [21, 19, 15, 10, "_ref"], [21, 23, 15, 10], [21, 24, 15, 2, "children"], [21, 32, 15, 10], [22, 6, 16, 2, "location"], [22, 14, 16, 10], [22, 17, 16, 10, "_ref"], [22, 21, 16, 10], [22, 22, 16, 2, "location"], [22, 30, 16, 10], [23, 4, 18, 2, "React"], [23, 9, 18, 7], [23, 10, 18, 8, "useEffect"], [23, 19, 18, 17], [23, 20, 18, 18], [23, 26, 18, 24], [24, 6, 19, 4, "console"], [24, 13, 19, 11], [24, 14, 19, 12, "error"], [24, 19, 19, 17], [24, 20, 19, 18], [24, 106, 19, 104], [24, 107, 19, 105], [25, 4, 20, 2], [25, 5, 20, 3], [25, 7, 20, 5], [25, 9, 20, 7], [25, 10, 20, 8], [27, 4, 22, 2], [28, 4, 23, 2], [28, 8, 23, 8, "current"], [28, 15, 23, 15], [28, 18, 23, 18], [28, 19, 23, 19], [28, 20, 23, 20], [29, 4, 24, 2], [29, 8, 24, 6, "ref"], [29, 11, 24, 9], [29, 13, 24, 11], [30, 6, 25, 4], [30, 10, 25, 10, "value"], [30, 15, 25, 15], [30, 18, 25, 18], [31, 8, 26, 6, "getCurrentOptions"], [31, 25, 26, 23, "getCurrentOptions"], [31, 26, 26, 23], [31, 28, 26, 26], [32, 10, 27, 8], [32, 17, 27, 15, "current"], [32, 24, 27, 22], [32, 25, 27, 23, "options"], [32, 32, 27, 30], [33, 8, 28, 6], [34, 6, 29, 4], [34, 7, 29, 5], [36, 6, 31, 4], [37, 6, 32, 4], [38, 6, 33, 4], [39, 6, 34, 4], [40, 6, 35, 4], [40, 10, 35, 8], [40, 17, 35, 15, "ref"], [40, 20, 35, 18], [40, 25, 35, 23], [40, 35, 35, 33], [40, 37, 35, 35], [41, 8, 36, 6, "ref"], [41, 11, 36, 9], [41, 12, 36, 10, "value"], [41, 17, 36, 15], [41, 18, 36, 16], [42, 6, 37, 4], [42, 7, 37, 5], [42, 13, 37, 11], [43, 8, 38, 6, "ref"], [43, 11, 38, 9], [43, 12, 38, 10, "current"], [43, 19, 38, 17], [43, 22, 38, 20, "value"], [43, 27, 38, 25], [44, 6, 39, 4], [45, 4, 40, 2], [46, 4, 41, 2], [46, 12, 42, 4], [47, 6, 43, 4], [48, 6, 44, 4], [48, 10, 44, 4, "_jsx"], [48, 25, 44, 8], [48, 27, 44, 9, "ServerContext"], [48, 55, 44, 22], [48, 56, 44, 23, "Provider"], [48, 64, 44, 31], [48, 66, 44, 33], [49, 8, 45, 6, "value"], [49, 13, 45, 11], [49, 15, 45, 13], [50, 10, 46, 8, "location"], [51, 8, 47, 6], [51, 9, 47, 7], [52, 8, 48, 6, "children"], [52, 16, 48, 14], [52, 18, 48, 16], [52, 31, 48, 29], [52, 35, 48, 29, "_jsx"], [52, 50, 48, 33], [52, 52, 48, 34, "CurrentRenderContext"], [52, 78, 48, 54], [52, 79, 48, 55, "Provider"], [52, 87, 48, 63], [52, 89, 48, 65], [53, 10, 49, 8, "value"], [53, 15, 49, 13], [53, 17, 49, 15, "current"], [53, 24, 49, 22], [54, 10, 50, 8, "children"], [54, 18, 50, 16], [54, 20, 50, 18, "children"], [55, 8, 51, 6], [55, 9, 51, 7], [56, 6, 52, 4], [56, 7, 52, 5], [57, 4, 52, 6], [58, 2, 54, 0], [58, 3, 54, 1], [58, 4, 54, 2], [59, 0, 54, 3], [59, 3]], "functionMap": {"names": ["<global>", "ServerContainer", "React.useEffect$argument_0", "value.getCurrentOptions"], "mappings": "AAA;6DCa;kBCI;GDE;MEM;OFE;CD0B"}}, "type": "js/module"}]}