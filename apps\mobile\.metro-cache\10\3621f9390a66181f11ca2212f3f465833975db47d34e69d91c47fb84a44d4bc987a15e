{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./LogBox/LogBox", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 37}}], "key": "K4wyQ+DvcDfmXSE3TajdK/TbsyE=", "exportNames": ["*"]}}, {"name": "pretty-format", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 30, "column": 18}, "end": {"line": 30, "column": 42}}], "key": "olWyfE7OhSnDcSpkF2r8idaXSNU=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _LogBox = _interopRequireDefault(require(_dependencyMap[1], \"./LogBox/LogBox\"));\n  var rejectionTrackingOptions = {\n    allRejections: true,\n    onUnhandled: function (id) {\n      var rejection = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var message;\n      var stack;\n      var stringValue = Object.prototype.toString.call(rejection);\n      if (stringValue === '[object Error]') {\n        message = Error.prototype.toString.call(rejection);\n        var error = rejection;\n        stack = error.stack;\n      } else {\n        try {\n          message = require(_dependencyMap[2], \"pretty-format\").format(rejection);\n        } catch {\n          message = typeof rejection === 'string' ? rejection : JSON.stringify(rejection);\n        }\n        if (rejection?.stack && typeof rejection.stack === 'string') {\n          stack = rejection.stack;\n        }\n      }\n      var warning = `Possible unhandled promise rejection (id: ${id}):\\n${message ?? ''}`;\n      if (__DEV__) {\n        _LogBox.default.addLog({\n          level: 'warn',\n          message: {\n            content: warning,\n            substitutions: []\n          },\n          componentStack: [],\n          componentStackType: null,\n          stack,\n          category: 'possible_unhandled_promise_rejection'\n        });\n      } else {\n        console.warn(warning);\n      }\n    },\n    onHandled: id => {\n      var warning = `Promise rejection handled (id: ${id})\\n` + 'This means you can ignore any previous messages of the form ' + `\"Possible unhandled promise rejection (id: ${id}):\"`;\n      console.warn(warning);\n    }\n  };\n  var _default = exports.default = rejectionTrackingOptions;\n});", "lineCount": 52, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_LogBox"], [7, 13, 13, 0], [7, 16, 13, 0, "_interopRequireDefault"], [7, 38, 13, 0], [7, 39, 13, 0, "require"], [7, 46, 13, 0], [7, 47, 13, 0, "_dependencyMap"], [7, 61, 13, 0], [8, 2, 15, 0], [8, 6, 15, 4, "rejectionTrackingOptions"], [8, 30, 15, 66], [8, 33, 15, 69], [9, 4, 16, 2, "allRejections"], [9, 17, 16, 15], [9, 19, 16, 17], [9, 23, 16, 21], [10, 4, 17, 2, "onUnhandled"], [10, 15, 17, 13], [10, 17, 17, 15], [10, 26, 17, 15, "onUnhandled"], [10, 27, 17, 16, "id"], [10, 29, 17, 18], [10, 31, 17, 39], [11, 6, 17, 39], [11, 10, 17, 20, "rejection"], [11, 19, 17, 29], [11, 22, 17, 29, "arguments"], [11, 31, 17, 29], [11, 32, 17, 29, "length"], [11, 38, 17, 29], [11, 46, 17, 29, "arguments"], [11, 55, 17, 29], [11, 63, 17, 29, "undefined"], [11, 72, 17, 29], [11, 75, 17, 29, "arguments"], [11, 84, 17, 29], [11, 90, 17, 32], [11, 91, 17, 33], [11, 92, 17, 34], [12, 6, 18, 4], [12, 10, 18, 8, "message"], [12, 17, 18, 23], [13, 6, 19, 4], [13, 10, 19, 8, "stack"], [13, 15, 19, 22], [14, 6, 22, 4], [14, 10, 22, 10, "stringValue"], [14, 21, 22, 21], [14, 24, 22, 24, "Object"], [14, 30, 22, 30], [14, 31, 22, 31, "prototype"], [14, 40, 22, 40], [14, 41, 22, 41, "toString"], [14, 49, 22, 49], [14, 50, 22, 50, "call"], [14, 54, 22, 54], [14, 55, 22, 55, "rejection"], [14, 64, 22, 64], [14, 65, 22, 65], [15, 6, 23, 4], [15, 10, 23, 8, "stringValue"], [15, 21, 23, 19], [15, 26, 23, 24], [15, 42, 23, 40], [15, 44, 23, 42], [16, 8, 25, 6, "message"], [16, 15, 25, 13], [16, 18, 25, 16, "Error"], [16, 23, 25, 21], [16, 24, 25, 22, "prototype"], [16, 33, 25, 31], [16, 34, 25, 32, "toString"], [16, 42, 25, 40], [16, 43, 25, 41, "call"], [16, 47, 25, 45], [16, 48, 25, 46, "rejection"], [16, 57, 25, 55], [16, 58, 25, 56], [17, 8, 26, 6], [17, 12, 26, 12, "error"], [17, 17, 26, 24], [17, 20, 26, 28, "rejection"], [17, 29, 26, 50], [18, 8, 27, 6, "stack"], [18, 13, 27, 11], [18, 16, 27, 14, "error"], [18, 21, 27, 19], [18, 22, 27, 20, "stack"], [18, 27, 27, 25], [19, 6, 28, 4], [19, 7, 28, 5], [19, 13, 28, 11], [20, 8, 29, 6], [20, 12, 29, 10], [21, 10, 30, 8, "message"], [21, 17, 30, 15], [21, 20, 30, 18, "require"], [21, 27, 30, 25], [21, 28, 30, 25, "_dependencyMap"], [21, 42, 30, 25], [21, 62, 30, 41], [21, 63, 30, 42], [21, 64, 30, 43, "format"], [21, 70, 30, 49], [21, 71, 30, 50, "rejection"], [21, 80, 30, 59], [21, 81, 30, 60], [22, 8, 31, 6], [22, 9, 31, 7], [22, 10, 31, 8], [22, 16, 31, 14], [23, 10, 32, 8, "message"], [23, 17, 32, 15], [23, 20, 33, 10], [23, 27, 33, 17, "rejection"], [23, 36, 33, 26], [23, 41, 33, 31], [23, 49, 33, 39], [23, 52, 34, 14, "rejection"], [23, 61, 34, 23], [23, 64, 35, 14, "JSON"], [23, 68, 35, 18], [23, 69, 35, 19, "stringify"], [23, 78, 35, 28], [23, 79, 35, 30, "rejection"], [23, 88, 35, 52], [23, 89, 35, 53], [24, 8, 36, 6], [25, 8, 39, 6], [25, 12, 39, 10, "rejection"], [25, 21, 39, 19], [25, 23, 39, 21, "stack"], [25, 28, 39, 26], [25, 32, 39, 30], [25, 39, 39, 37, "rejection"], [25, 48, 39, 46], [25, 49, 39, 47, "stack"], [25, 54, 39, 52], [25, 59, 39, 57], [25, 67, 39, 65], [25, 69, 39, 67], [26, 10, 40, 8, "stack"], [26, 15, 40, 13], [26, 18, 40, 16, "rejection"], [26, 27, 40, 25], [26, 28, 40, 26, "stack"], [26, 33, 40, 31], [27, 8, 41, 6], [28, 6, 42, 4], [29, 6, 44, 4], [29, 10, 44, 10, "warning"], [29, 17, 44, 17], [29, 20, 44, 20], [29, 65, 44, 65, "id"], [29, 67, 44, 67], [29, 74, 45, 6, "message"], [29, 81, 45, 13], [29, 85, 45, 17], [29, 87, 45, 19], [29, 89, 46, 6], [30, 6, 47, 4], [30, 10, 47, 8, "__DEV__"], [30, 17, 47, 15], [30, 19, 47, 17], [31, 8, 48, 6, "LogBox"], [31, 23, 48, 12], [31, 24, 48, 13, "addLog"], [31, 30, 48, 19], [31, 31, 48, 20], [32, 10, 49, 8, "level"], [32, 15, 49, 13], [32, 17, 49, 15], [32, 23, 49, 21], [33, 10, 50, 8, "message"], [33, 17, 50, 15], [33, 19, 50, 17], [34, 12, 51, 10, "content"], [34, 19, 51, 17], [34, 21, 51, 19, "warning"], [34, 28, 51, 26], [35, 12, 52, 10, "substitutions"], [35, 25, 52, 23], [35, 27, 52, 25], [36, 10, 53, 8], [36, 11, 53, 9], [37, 10, 54, 8, "componentStack"], [37, 24, 54, 22], [37, 26, 54, 24], [37, 28, 54, 26], [38, 10, 55, 8, "componentStackType"], [38, 28, 55, 26], [38, 30, 55, 28], [38, 34, 55, 32], [39, 10, 56, 8, "stack"], [39, 15, 56, 13], [40, 10, 57, 8, "category"], [40, 18, 57, 16], [40, 20, 57, 18], [41, 8, 58, 6], [41, 9, 58, 7], [41, 10, 58, 8], [42, 6, 59, 4], [42, 7, 59, 5], [42, 13, 59, 11], [43, 8, 60, 6, "console"], [43, 15, 60, 13], [43, 16, 60, 14, "warn"], [43, 20, 60, 18], [43, 21, 60, 19, "warning"], [43, 28, 60, 26], [43, 29, 60, 27], [44, 6, 61, 4], [45, 4, 62, 2], [45, 5, 62, 3], [46, 4, 63, 2, "onHandled"], [46, 13, 63, 11], [46, 15, 63, 13, "id"], [46, 17, 63, 15], [46, 21, 63, 19], [47, 6, 64, 4], [47, 10, 64, 10, "warning"], [47, 17, 64, 17], [47, 20, 65, 6], [47, 54, 65, 40, "id"], [47, 56, 65, 42], [47, 61, 65, 47], [47, 64, 66, 6], [47, 126, 66, 68], [47, 129, 67, 6], [47, 175, 67, 52, "id"], [47, 177, 67, 54], [47, 182, 67, 59], [48, 6, 68, 4, "console"], [48, 13, 68, 11], [48, 14, 68, 12, "warn"], [48, 18, 68, 16], [48, 19, 68, 17, "warning"], [48, 26, 68, 24], [48, 27, 68, 25], [49, 4, 69, 2], [50, 2, 70, 0], [50, 3, 70, 1], [51, 2, 70, 2], [51, 6, 70, 2, "_default"], [51, 14, 70, 2], [51, 17, 70, 2, "exports"], [51, 24, 70, 2], [51, 25, 70, 2, "default"], [51, 32, 70, 2], [51, 35, 72, 15, "rejectionTrackingOptions"], [51, 59, 72, 39], [52, 0, 72, 39], [52, 3]], "functionMap": {"names": ["<global>", "rejectionTrackingOptions.onUnhandled", "rejectionTrackingOptions.onHandled"], "mappings": "AAA;eCgB;GD6C;aEC;GFM"}}, "type": "js/module"}]}