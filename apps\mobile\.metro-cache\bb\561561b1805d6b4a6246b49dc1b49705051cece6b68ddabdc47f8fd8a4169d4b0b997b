{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = queueMicrotask;\n  var resolvedPromise;\n  function queueMicrotask(callback) {\n    if (arguments.length < 1) {\n      throw new TypeError('queueMicrotask must be called with at least one argument (a function to call)');\n    }\n    if (typeof callback !== 'function') {\n      throw new TypeError('The argument to queueMicrotask must be a function.');\n    }\n    (resolvedPromise || (resolvedPromise = Promise.resolve())).then(callback).catch(error => setTimeout(() => {\n      throw error;\n    }, 0));\n  }\n});", "lineCount": 20, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [7, 20, 11, 13, "queueMicrotask"], [7, 34, 11, 13], [8, 2, 13, 0], [8, 6, 13, 4, "resolvedPromise"], [8, 21, 13, 19], [9, 2, 22, 15], [9, 11, 22, 24, "queueMicrotask"], [9, 25, 22, 38, "queueMicrotask"], [9, 26, 22, 39, "callback"], [9, 34, 22, 57], [9, 36, 22, 59], [10, 4, 23, 2], [10, 8, 23, 6, "arguments"], [10, 17, 23, 15], [10, 18, 23, 16, "length"], [10, 24, 23, 22], [10, 27, 23, 25], [10, 28, 23, 26], [10, 30, 23, 28], [11, 6, 24, 4], [11, 12, 24, 10], [11, 16, 24, 14, "TypeError"], [11, 25, 24, 23], [11, 26, 25, 6], [11, 105, 26, 4], [11, 106, 26, 5], [12, 4, 27, 2], [13, 4, 28, 2], [13, 8, 28, 6], [13, 15, 28, 13, "callback"], [13, 23, 28, 21], [13, 28, 28, 26], [13, 38, 28, 36], [13, 40, 28, 38], [14, 6, 29, 4], [14, 12, 29, 10], [14, 16, 29, 14, "TypeError"], [14, 25, 29, 23], [14, 26, 29, 24], [14, 78, 29, 76], [14, 79, 29, 77], [15, 4, 30, 2], [16, 4, 33, 2], [16, 5, 33, 3, "resolvedPromise"], [16, 20, 33, 18], [16, 25, 33, 23, "resolvedPromise"], [16, 40, 33, 38], [16, 43, 33, 41, "Promise"], [16, 50, 33, 48], [16, 51, 33, 49, "resolve"], [16, 58, 33, 56], [16, 59, 33, 57], [16, 60, 33, 58], [16, 61, 33, 59], [16, 63, 34, 5, "then"], [16, 67, 34, 9], [16, 68, 34, 10, "callback"], [16, 76, 34, 18], [16, 77, 34, 19], [16, 78, 35, 5, "catch"], [16, 83, 35, 10], [16, 84, 35, 11, "error"], [16, 89, 35, 16], [16, 93, 37, 6, "setTimeout"], [16, 103, 37, 16], [16, 104, 37, 17], [16, 110, 37, 23], [17, 6, 38, 8], [17, 12, 38, 14, "error"], [17, 17, 38, 19], [18, 4, 39, 6], [18, 5, 39, 7], [18, 7, 39, 9], [18, 8, 39, 10], [18, 9, 40, 4], [18, 10, 40, 5], [19, 2, 41, 0], [20, 0, 41, 1], [20, 3]], "functionMap": {"names": ["<global>", "queueMicrotask", "then._catch$argument_0", "setTimeout$argument_0"], "mappings": "AAA;eCqB;WCa;iBCE;ODE,ID"}}, "type": "js/module"}]}