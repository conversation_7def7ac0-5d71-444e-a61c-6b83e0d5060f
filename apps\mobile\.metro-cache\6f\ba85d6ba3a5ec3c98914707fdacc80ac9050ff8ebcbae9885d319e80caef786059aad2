{"dependencies": [{"name": "./RNScreensTurboModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 123}, "end": {"line": 7, "column": 62, "index": 185}}], "key": "wTJ5mED5C84sotYWMCkGn+LBtSw=", "exportNames": ["*"]}}, {"name": "./styleUpdater", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 186}, "end": {"line": 8, "column": 73, "index": 259}}], "key": "F6H1fvmPsODndnqalYVnB+rzSpc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getSwipeSimulator = void 0;\n  var _RNScreensTurboModule = require(_dependencyMap[0], \"./RNScreensTurboModule\");\n  var _styleUpdater = require(_dependencyMap[1], \"./styleUpdater\");\n  var BASE_VELOCITY = 400;\n  var ADDITIONAL_VELOCITY_FACTOR_X = 400;\n  var ADDITIONAL_VELOCITY_FACTOR_Y = 500;\n  var ADDITIONAL_VELOCITY_FACTOR_XY = 600;\n  var _worklet_3339184140838_init_data = {\n    code: \"function computeEasingProgress_swipeSimulatorTs1(startingTimestamp,distance,velocity){if(Math.abs(distance)<1){return 1;}const elapsedTime=(_getAnimationTimestamp()-startingTimestamp)/1000;const currentPosition=velocity*elapsedTime;const progress=currentPosition/distance;return progress;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\swipeSimulator.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"computeEasingProgress_swipeSimulatorTs1\\\",\\\"startingTimestamp\\\",\\\"distance\\\",\\\"velocity\\\",\\\"Math\\\",\\\"abs\\\",\\\"elapsedTime\\\",\\\"_getAnimationTimestamp\\\",\\\"currentPosition\\\",\\\"progress\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/swipeSimulator.ts\\\"],\\\"mappings\\\":\\\"AAcA,SAAAA,uCACEA,CAAyBC,iBAEzB,CAAAC,QACA,CAAAC,QAAA,EAEA,GAAIC,IAAI,CAACC,GAAG,CAACH,QAAQ,CAAC,CAAG,CAAC,CAAE,CAC1B,MAAO,EAAC,CACV,CACA,KAAM,CAAAI,WAAW,CAAG,CAACC,sBAAsB,CAAC,CAAC,CAAGN,iBAAiB,EAAI,IAAI,CACzE,KAAM,CAAAO,eAAe,CAAGL,QAAQ,CAAGG,WAAW,CAC9C,KAAM,CAAAG,QAAQ,CAAGD,eAAe,CAAGN,QAAQ,CAC3C,MAAO,CAAAO,QAAQ,CACjB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var computeEasingProgress = function () {\n    var _e = [new global.Error(), 1, -27];\n    var computeEasingProgress = function (startingTimestamp, distance, velocity) {\n      if (Math.abs(distance) < 1) {\n        return 1;\n      }\n      var elapsedTime = (_getAnimationTimestamp() - startingTimestamp) / 1000;\n      var currentPosition = velocity * elapsedTime;\n      var progress = currentPosition / distance;\n      return progress;\n    };\n    computeEasingProgress.__closure = {};\n    computeEasingProgress.__workletHash = 3339184140838;\n    computeEasingProgress.__initData = _worklet_3339184140838_init_data;\n    computeEasingProgress.__stackDetails = _e;\n    return computeEasingProgress;\n  }();\n  var _worklet_558927583902_init_data = {\n    code: \"function easing_swipeSimulatorTs2(x){return 1-Math.pow(1-x,5);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\swipeSimulator.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"easing_swipeSimulatorTs2\\\",\\\"x\\\",\\\"Math\\\",\\\"pow\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/swipeSimulator.ts\\\"],\\\"mappings\\\":\\\"AA6BA,SAAAA,wBAAmCA,CAAAC,CAAA,EAGjC,MAAO,EAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGF,CAAC,CAAE,CAAC,CAAC,CAC/B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var easing = function () {\n    var _e = [new global.Error(), 1, -27];\n    var easing = function (x) {\n      // based on https://easings.net/#easeOutQuart\n      return 1 - Math.pow(1 - x, 5);\n    };\n    easing.__closure = {};\n    easing.__workletHash = 558927583902;\n    easing.__initData = _worklet_558927583902_init_data;\n    easing.__stackDetails = _e;\n    return easing;\n  }();\n  var _worklet_11469122063489_init_data = {\n    code: \"function computeProgress_swipeSimulatorTs3(screenTransitionConfig,event,isTransitionCanceled){const screenDimensions=screenTransitionConfig.screenDimensions;const progressX=Math.abs(event.translationX/screenDimensions.width);const progressY=Math.abs(event.translationY/screenDimensions.height);const maxProgress=Math.max(progressX,progressY);const progress=isTransitionCanceled?maxProgress/2:maxProgress;return progress;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\swipeSimulator.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"computeProgress_swipeSimulatorTs3\\\",\\\"screenTransitionConfig\\\",\\\"event\\\",\\\"isTransitionCanceled\\\",\\\"screenDimensions\\\",\\\"progressX\\\",\\\"Math\\\",\\\"abs\\\",\\\"translationX\\\",\\\"width\\\",\\\"progressY\\\",\\\"translationY\\\",\\\"height\\\",\\\"maxProgress\\\",\\\"max\\\",\\\"progress\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/swipeSimulator.ts\\\"],\\\"mappings\\\":\\\"AAmCA,SAAAA,iCACEA,CAAAC,sBAEA,CAAAC,KAAA,CAAAC,oBACA,EAEA,KAAM,CAAAC,gBAAgB,CAAGH,sBAAsB,CAACG,gBAAgB,CAChE,KAAM,CAAAC,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,CAACM,YAAY,CAAGJ,gBAAgB,CAACK,KAAK,CAAC,CACvE,KAAM,CAAAC,SAAS,CAAGJ,IAAI,CAACC,GAAG,CAACL,KAAK,CAACS,YAAY,CAAGP,gBAAgB,CAACQ,MAAM,CAAC,CACxE,KAAM,CAAAC,WAAW,CAAGP,IAAI,CAACQ,GAAG,CAACT,SAAS,CAAEK,SAAS,CAAC,CAClD,KAAM,CAAAK,QAAQ,CAAGZ,oBAAoB,CAAGU,WAAW,CAAG,CAAC,CAAGA,WAAW,CACrE,MAAO,CAAAE,QAAQ,CACjB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var computeProgress = function () {\n    var _e = [new global.Error(), 1, -27];\n    var computeProgress = function (screenTransitionConfig, event, isTransitionCanceled) {\n      var screenDimensions = screenTransitionConfig.screenDimensions;\n      var progressX = Math.abs(event.translationX / screenDimensions.width);\n      var progressY = Math.abs(event.translationY / screenDimensions.height);\n      var maxProgress = Math.max(progressX, progressY);\n      var progress = isTransitionCanceled ? maxProgress / 2 : maxProgress;\n      return progress;\n    };\n    computeProgress.__closure = {};\n    computeProgress.__workletHash = 11469122063489;\n    computeProgress.__initData = _worklet_11469122063489_init_data;\n    computeProgress.__stackDetails = _e;\n    return computeProgress;\n  }();\n  var _worklet_14642652832736_init_data = {\n    code: \"function maybeScheduleNextFrame_swipeSimulatorTs4(step,didScreenReachDestination,screenTransitionConfig,event,isTransitionCanceled){const{computeProgress,RNScreensTurboModule}=this.__closure;if(!didScreenReachDestination){const stackTag=screenTransitionConfig.stackTag;const progress=computeProgress(screenTransitionConfig,event,isTransitionCanceled);RNScreensTurboModule.updateTransition(stackTag,progress);requestAnimationFrame(step);}else{var _screenTransitionConf;(_screenTransitionConf=screenTransitionConfig.onFinishAnimation)===null||_screenTransitionConf===void 0||_screenTransitionConf.call(screenTransitionConfig);}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\swipeSimulator.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"maybeScheduleNextFrame_swipeSimulatorTs4\\\",\\\"step\\\",\\\"didScreenReachDestination\\\",\\\"screenTransitionConfig\\\",\\\"event\\\",\\\"isTransitionCanceled\\\",\\\"computeProgress\\\",\\\"RNScreensTurboModule\\\",\\\"__closure\\\",\\\"stackTag\\\",\\\"progress\\\",\\\"updateTransition\\\",\\\"requestAnimationFrame\\\",\\\"_screenTransitionConf\\\",\\\"onFinishAnimation\\\",\\\"call\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/swipeSimulator.ts\\\"],\\\"mappings\\\":\\\"AAiDA,SAAAA,wCAEEA,CAAAC,IAAA,CAAAC,yBACA,CAAAC,sBAEA,CAAAC,KAAA,CAAAC,oBACA,QAAAC,eAAA,CAAAC,oBAAA,OAAAC,SAAA,CAEA,GAAI,CAACN,yBAAyB,CAAE,CAC9B,KAAM,CAAAO,QAAQ,CAAGN,sBAAsB,CAACM,QAAQ,CAChD,KAAM,CAAAC,QAAQ,CAAGJ,eAAe,CAC9BH,sBAAsB,CACtBC,KAAK,CACLC,oBACF,CAAC,CACDE,oBAAoB,CAACI,gBAAgB,CAACF,QAAQ,CAAEC,QAAQ,CAAC,CACzDE,qBAAqB,CAACX,IAAI,CAAC,CAC7B,CAAC,IAAM,KAAAY,qBAAA,CACL,CAAAA,qBAAA,CAAAV,sBAAsB,CAACW,iBAAiB,UAAAD,qBAAA,WAAxCA,qBAAA,CAAAE,IAAA,CAAAZ,sBAA2C,CAAC,CAC9C,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var maybeScheduleNextFrame = function () {\n    var _e = [new global.Error(), -3, -27];\n    var maybeScheduleNextFrame = function (step, didScreenReachDestination, screenTransitionConfig, event, isTransitionCanceled) {\n      if (!didScreenReachDestination) {\n        var stackTag = screenTransitionConfig.stackTag;\n        var progress = computeProgress(screenTransitionConfig, event, isTransitionCanceled);\n        _RNScreensTurboModule.RNScreensTurboModule.updateTransition(stackTag, progress);\n        requestAnimationFrame(step);\n      } else {\n        screenTransitionConfig.onFinishAnimation?.();\n      }\n    };\n    maybeScheduleNextFrame.__closure = {\n      computeProgress,\n      RNScreensTurboModule: _RNScreensTurboModule.RNScreensTurboModule\n    };\n    maybeScheduleNextFrame.__workletHash = 14642652832736;\n    maybeScheduleNextFrame.__initData = _worklet_14642652832736_init_data;\n    maybeScheduleNextFrame.__stackDetails = _e;\n    return maybeScheduleNextFrame;\n  }();\n  var _worklet_17409639634070_init_data = {\n    code: \"function getSwipeSimulator_swipeSimulatorTs5(event,screenTransitionConfig,lockAxis){const{BASE_VELOCITY,ADDITIONAL_VELOCITY_FACTOR_X,ADDITIONAL_VELOCITY_FACTOR_Y,ADDITIONAL_VELOCITY_FACTOR_XY,applyStyleForBelowTopScreen,computeEasingProgress,easing,applyStyle,maybeScheduleNextFrame}=this.__closure;const screenDimensions=screenTransitionConfig.screenDimensions;const startTimestamp=_getAnimationTimestamp();const{isTransitionCanceled:isTransitionCanceled}=screenTransitionConfig;const startingPosition={x:event.translationX,y:event.translationY};const direction={x:Math.sign(event.translationX),y:Math.sign(event.translationY)};const finalPosition=isTransitionCanceled?{x:0,y:0}:{x:direction.x*screenDimensions.width,y:direction.y*screenDimensions.height};const distance={x:Math.abs(finalPosition.x-startingPosition.x),y:Math.abs(finalPosition.y-startingPosition.y)};const didScreenReachDestination={x:false,y:false};const velocity={x:BASE_VELOCITY,y:BASE_VELOCITY};if(lockAxis==='x'){velocity.y=0;velocity.x+=ADDITIONAL_VELOCITY_FACTOR_X*distance.x/screenDimensions.width;}else if(lockAxis==='y'){velocity.x=0;velocity.y+=ADDITIONAL_VELOCITY_FACTOR_Y*distance.y/screenDimensions.height;}else{const euclideanDistance=Math.sqrt(distance.x**2+distance.y**2);const screenDiagonal=Math.sqrt(screenDimensions.width**2+screenDimensions.height**2);const velocityVectorLength=BASE_VELOCITY+ADDITIONAL_VELOCITY_FACTOR_XY*euclideanDistance/screenDiagonal;if(Math.abs(startingPosition.x)>Math.abs(startingPosition.y)){velocity.x=velocityVectorLength;velocity.y=velocityVectorLength*Math.abs(startingPosition.y/startingPosition.x);}else{velocity.x=velocityVectorLength*Math.abs(startingPosition.x/startingPosition.y);velocity.y=velocityVectorLength;}}if(isTransitionCanceled){function didScreenReachDestinationCheck(){if(lockAxis==='x'){return didScreenReachDestination.x;}else if(lockAxis==='y'){return didScreenReachDestination.y;}else{return didScreenReachDestination.x&&didScreenReachDestination.y;}}function restoreOriginalStyleForBelowTopScreen(){event.translationX=direction.x*screenDimensions.width;event.translationY=direction.y*screenDimensions.height;applyStyleForBelowTopScreen(screenTransitionConfig,event);}const computeFrame=function(){const progress={x:computeEasingProgress(startTimestamp,distance.x,velocity.x),y:computeEasingProgress(startTimestamp,distance.y,velocity.y)};event.translationX=startingPosition.x-direction.x*distance.x*easing(progress.x);event.translationY=startingPosition.y-direction.y*distance.y*easing(progress.y);if(direction.x>0){if(event.translationX<=0){didScreenReachDestination.x=true;event.translationX=0;}}else{if(event.translationX>=0){didScreenReachDestination.x=true;event.translationX=0;}}if(direction.y>0){if(event.translationY<=0){didScreenReachDestination.y=true;event.translationY=0;}}else{if(event.translationY>=0){didScreenReachDestination.y=true;event.translationY=0;}}applyStyle(screenTransitionConfig,event);const finished=didScreenReachDestinationCheck();if(finished){restoreOriginalStyleForBelowTopScreen();}maybeScheduleNextFrame(computeFrame,finished,screenTransitionConfig,event,isTransitionCanceled);};return computeFrame;}else{const computeFrame=function(){const progress={x:computeEasingProgress(startTimestamp,distance.x,velocity.x),y:computeEasingProgress(startTimestamp,distance.y,velocity.y)};event.translationX=startingPosition.x+direction.x*distance.x*easing(progress.x);event.translationY=startingPosition.y+direction.y*distance.y*easing(progress.y);if(direction.x>0){if(event.translationX>=screenDimensions.width){didScreenReachDestination.x=true;event.translationX=screenDimensions.width;}}else{if(event.translationX<=-screenDimensions.width){didScreenReachDestination.x=true;event.translationX=-screenDimensions.width;}}if(direction.y>0){if(event.translationY>=screenDimensions.height){didScreenReachDestination.y=true;event.translationY=screenDimensions.height;}}else{if(event.translationY<=-screenDimensions.height){didScreenReachDestination.y=true;event.translationY=-screenDimensions.height;}}applyStyle(screenTransitionConfig,event);maybeScheduleNextFrame(computeFrame,didScreenReachDestination.x||didScreenReachDestination.y,screenTransitionConfig,event,isTransitionCanceled);};return computeFrame;}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\swipeSimulator.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getSwipeSimulator_swipeSimulatorTs5\\\",\\\"event\\\",\\\"screenTransitionConfig\\\",\\\"lockAxis\\\",\\\"BASE_VELOCITY\\\",\\\"ADDITIONAL_VELOCITY_FACTOR_X\\\",\\\"ADDITIONAL_VELOCITY_FACTOR_Y\\\",\\\"ADDITIONAL_VELOCITY_FACTOR_XY\\\",\\\"applyStyleForBelowTopScreen\\\",\\\"computeEasingProgress\\\",\\\"easing\\\",\\\"applyStyle\\\",\\\"maybeScheduleNextFrame\\\",\\\"__closure\\\",\\\"screenDimensions\\\",\\\"startTimestamp\\\",\\\"_getAnimationTimestamp\\\",\\\"isTransitionCanceled\\\",\\\"startingPosition\\\",\\\"x\\\",\\\"translationX\\\",\\\"y\\\",\\\"translationY\\\",\\\"direction\\\",\\\"Math\\\",\\\"sign\\\",\\\"finalPosition\\\",\\\"width\\\",\\\"height\\\",\\\"distance\\\",\\\"abs\\\",\\\"didScreenReachDestination\\\",\\\"velocity\\\",\\\"euclideanDistance\\\",\\\"sqrt\\\",\\\"screenDiagonal\\\",\\\"velocityVectorLength\\\",\\\"didScreenReachDestinationCheck\\\",\\\"restoreOriginalStyleForBelowTopScreen\\\",\\\"computeFrame\\\",\\\"progress\\\",\\\"finished\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/swipeSimulator.ts\\\"],\\\"mappings\\\":\\\"AAuEO,SAAAA,mCAELA,CAAAC,KAAA,CAAAC,sBAEA,CAAAC,QAAA,QAAAC,aAAA,CAAAC,4BAAA,CAAAC,4BAAA,CAAAC,6BAAA,CAAAC,2BAAA,CAAAC,qBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,sBAAA,OAAAC,SAAA,CAEA,KAAM,CAAAC,gBAAgB,CAAGZ,sBAAsB,CAACY,gBAAgB,CAChE,KAAM,CAAAC,cAAc,CAAGC,sBAAsB,CAAC,CAAC,CAC/C,KAAM,CAAEC,oBAAA,CAAAA,oBAAqB,CAAC,CAAGf,sBAAsB,CACvD,KAAM,CAAAgB,gBAAgB,CAAG,CACvBC,CAAC,CAAElB,KAAK,CAACmB,YAAY,CACrBC,CAAC,CAAEpB,KAAK,CAACqB,YACX,CAAC,CACD,KAAM,CAAAC,SAAS,CAAG,CAChBJ,CAAC,CAAEK,IAAI,CAACC,IAAI,CAACxB,KAAK,CAACmB,YAAY,CAAC,CAChCC,CAAC,CAAEG,IAAI,CAACC,IAAI,CAACxB,KAAK,CAACqB,YAAY,CACjC,CAAC,CACD,KAAM,CAAAI,aAAa,CAAGT,oBAAoB,CACtC,CAAEE,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CACd,CACEF,CAAC,CAAEI,SAAS,CAACJ,CAAC,CAAGL,gBAAgB,CAACa,KAAK,CACvCN,CAAC,CAAEE,SAAS,CAACF,CAAC,CAAGP,gBAAgB,CAACc,MACpC,CAAC,CACL,KAAM,CAAAC,QAAQ,CAAG,CACfV,CAAC,CAAEK,IAAI,CAACM,GAAG,CAACJ,aAAa,CAACP,CAAC,CAAGD,gBAAgB,CAACC,CAAC,CAAC,CACjDE,CAAC,CAAEG,IAAI,CAACM,GAAG,CAACJ,aAAa,CAACL,CAAC,CAAGH,gBAAgB,CAACG,CAAC,CAClD,CAAC,CACD,KAAM,CAAAU,yBAAyB,CAAG,CAChCZ,CAAC,CAAE,KAAK,CACRE,CAAC,CAAE,KACL,CAAC,CACD,KAAM,CAAAW,QAAQ,CAAG,CAAEb,CAAC,CAAEf,aAAa,CAAEiB,CAAC,CAAEjB,aAAc,CAAC,CACvD,GAAID,QAAQ,GAAK,GAAG,CAAE,CACpB6B,QAAQ,CAACX,CAAC,CAAG,CAAC,CACdW,QAAQ,CAACb,CAAC,EACPd,4BAA4B,CAAGwB,QAAQ,CAACV,CAAC,CAAIL,gBAAgB,CAACa,KAAK,CACxE,CAAC,IAAM,IAAIxB,QAAQ,GAAK,GAAG,CAAE,CAC3B6B,QAAQ,CAACb,CAAC,CAAG,CAAC,CACda,QAAQ,CAACX,CAAC,EACPf,4BAA4B,CAAGuB,QAAQ,CAACR,CAAC,CAAIP,gBAAgB,CAACc,MAAM,CACzE,CAAC,IAAM,CACL,KAAM,CAAAK,iBAAiB,CAAGT,IAAI,CAACU,IAAI,CAACL,QAAQ,CAACV,CAAC,EAAI,CAAC,CAAGU,QAAQ,CAACR,CAAC,EAAI,CAAC,CAAC,CACtE,KAAM,CAAAc,cAAc,CAAGX,IAAI,CAACU,IAAI,CAC9BpB,gBAAgB,CAACa,KAAK,EAAI,CAAC,CAAGb,gBAAgB,CAACc,MAAM,EAAI,CAC3D,CAAC,CACD,KAAM,CAAAQ,oBAAoB,CACxBhC,aAAa,CACZG,6BAA6B,CAAG0B,iBAAiB,CAAIE,cAAc,CACtE,GAAIX,IAAI,CAACM,GAAG,CAACZ,gBAAgB,CAACC,CAAC,CAAC,CAAGK,IAAI,CAACM,GAAG,CAACZ,gBAAgB,CAACG,CAAC,CAAC,CAAE,CAC/DW,QAAQ,CAACb,CAAC,CAAGiB,oBAAoB,CACjCJ,QAAQ,CAACX,CAAC,CACRe,oBAAoB,CACpBZ,IAAI,CAACM,GAAG,CAACZ,gBAAgB,CAACG,CAAC,CAAGH,gBAAgB,CAACC,CAAC,CAAC,CACrD,CAAC,IAAM,CACLa,QAAQ,CAACb,CAAC,CACRiB,oBAAoB,CACpBZ,IAAI,CAACM,GAAG,CAACZ,gBAAgB,CAACC,CAAC,CAAGD,gBAAgB,CAACG,CAAC,CAAC,CACnDW,QAAQ,CAACX,CAAC,CAAGe,oBAAoB,CACnC,CACF,CAEA,GAAInB,oBAAoB,CAAE,CACxB,QAAS,CAAAoB,8BAA8BA,CAAA,CAAG,CACxC,GAAIlC,QAAQ,GAAK,GAAG,CAAE,CACpB,MAAO,CAAA4B,yBAAyB,CAACZ,CAAC,CACpC,CAAC,IAAM,IAAIhB,QAAQ,GAAK,GAAG,CAAE,CAC3B,MAAO,CAAA4B,yBAAyB,CAACV,CAAC,CACpC,CAAC,IAAM,CACL,MAAO,CAAAU,yBAAyB,CAACZ,CAAC,EAAIY,yBAAyB,CAACV,CAAC,CACnE,CACF,CAEA,QAAS,CAAAiB,qCAAqCA,CAAA,CAAG,CAC/CrC,KAAK,CAACmB,YAAY,CAAGG,SAAS,CAACJ,CAAC,CAAGL,gBAAgB,CAACa,KAAK,CACzD1B,KAAK,CAACqB,YAAY,CAAGC,SAAS,CAACF,CAAC,CAAGP,gBAAgB,CAACc,MAAM,CAC1DpB,2BAA2B,CAACN,sBAAsB,CAAED,KAAK,CAAC,CAC5D,CAEA,KAAM,CAAAsC,YAAY,CAAG,QAAAA,CAAA,CAAM,CACzB,KAAM,CAAAC,QAAQ,CAAG,CACfrB,CAAC,CAAEV,qBAAqB,CAACM,cAAc,CAAEc,QAAQ,CAACV,CAAC,CAAEa,QAAQ,CAACb,CAAC,CAAC,CAChEE,CAAC,CAAEZ,qBAAqB,CAACM,cAAc,CAAEc,QAAQ,CAACR,CAAC,CAAEW,QAAQ,CAACX,CAAC,CACjE,CAAC,CACDpB,KAAK,CAACmB,YAAY,CAChBF,gBAAgB,CAACC,CAAC,CAAGI,SAAS,CAACJ,CAAC,CAAGU,QAAQ,CAACV,CAAC,CAAGT,MAAM,CAAC8B,QAAQ,CAACrB,CAAC,CAAC,CACpElB,KAAK,CAACqB,YAAY,CAChBJ,gBAAgB,CAACG,CAAC,CAAGE,SAAS,CAACF,CAAC,CAAGQ,QAAQ,CAACR,CAAC,CAAGX,MAAM,CAAC8B,QAAQ,CAACnB,CAAC,CAAC,CACpE,GAAIE,SAAS,CAACJ,CAAC,CAAG,CAAC,CAAE,CACnB,GAAIlB,KAAK,CAACmB,YAAY,EAAI,CAAC,CAAE,CAC3BW,yBAAyB,CAACZ,CAAC,CAAG,IAAI,CAClClB,KAAK,CAACmB,YAAY,CAAG,CAAC,CACxB,CACF,CAAC,IAAM,CACL,GAAInB,KAAK,CAACmB,YAAY,EAAI,CAAC,CAAE,CAC3BW,yBAAyB,CAACZ,CAAC,CAAG,IAAI,CAClClB,KAAK,CAACmB,YAAY,CAAG,CAAC,CACxB,CACF,CACA,GAAIG,SAAS,CAACF,CAAC,CAAG,CAAC,CAAE,CACnB,GAAIpB,KAAK,CAACqB,YAAY,EAAI,CAAC,CAAE,CAC3BS,yBAAyB,CAACV,CAAC,CAAG,IAAI,CAClCpB,KAAK,CAACqB,YAAY,CAAG,CAAC,CACxB,CACF,CAAC,IAAM,CACL,GAAIrB,KAAK,CAACqB,YAAY,EAAI,CAAC,CAAE,CAC3BS,yBAAyB,CAACV,CAAC,CAAG,IAAI,CAClCpB,KAAK,CAACqB,YAAY,CAAG,CAAC,CACxB,CACF,CACAX,UAAU,CAACT,sBAAsB,CAAED,KAAK,CAAC,CACzC,KAAM,CAAAwC,QAAQ,CAAGJ,8BAA8B,CAAC,CAAC,CACjD,GAAII,QAAQ,CAAE,CACZH,qCAAqC,CAAC,CAAC,CACzC,CACA1B,sBAAsB,CACpB2B,YAAY,CACZE,QAAQ,CACRvC,sBAAsB,CACtBD,KAAK,CACLgB,oBACF,CAAC,CACH,CAAC,CACD,MAAO,CAAAsB,YAAY,CACrB,CAAC,IAAM,CACL,KAAM,CAAAA,YAAY,CAAG,QAAAA,CAAA,CAAM,CACzB,KAAM,CAAAC,QAAQ,CAAG,CACfrB,CAAC,CAAEV,qBAAqB,CAACM,cAAc,CAAEc,QAAQ,CAACV,CAAC,CAAEa,QAAQ,CAACb,CAAC,CAAC,CAChEE,CAAC,CAAEZ,qBAAqB,CAACM,cAAc,CAAEc,QAAQ,CAACR,CAAC,CAAEW,QAAQ,CAACX,CAAC,CACjE,CAAC,CACDpB,KAAK,CAACmB,YAAY,CAChBF,gBAAgB,CAACC,CAAC,CAAGI,SAAS,CAACJ,CAAC,CAAGU,QAAQ,CAACV,CAAC,CAAGT,MAAM,CAAC8B,QAAQ,CAACrB,CAAC,CAAC,CACpElB,KAAK,CAACqB,YAAY,CAChBJ,gBAAgB,CAACG,CAAC,CAAGE,SAAS,CAACF,CAAC,CAAGQ,QAAQ,CAACR,CAAC,CAAGX,MAAM,CAAC8B,QAAQ,CAACnB,CAAC,CAAC,CACpE,GAAIE,SAAS,CAACJ,CAAC,CAAG,CAAC,CAAE,CACnB,GAAIlB,KAAK,CAACmB,YAAY,EAAIN,gBAAgB,CAACa,KAAK,CAAE,CAChDI,yBAAyB,CAACZ,CAAC,CAAG,IAAI,CAClClB,KAAK,CAACmB,YAAY,CAAGN,gBAAgB,CAACa,KAAK,CAC7C,CACF,CAAC,IAAM,CACL,GAAI1B,KAAK,CAACmB,YAAY,EAAI,CAACN,gBAAgB,CAACa,KAAK,CAAE,CACjDI,yBAAyB,CAACZ,CAAC,CAAG,IAAI,CAClClB,KAAK,CAACmB,YAAY,CAAG,CAACN,gBAAgB,CAACa,KAAK,CAC9C,CACF,CACA,GAAIJ,SAAS,CAACF,CAAC,CAAG,CAAC,CAAE,CACnB,GAAIpB,KAAK,CAACqB,YAAY,EAAIR,gBAAgB,CAACc,MAAM,CAAE,CACjDG,yBAAyB,CAACV,CAAC,CAAG,IAAI,CAClCpB,KAAK,CAACqB,YAAY,CAAGR,gBAAgB,CAACc,MAAM,CAC9C,CACF,CAAC,IAAM,CACL,GAAI3B,KAAK,CAACqB,YAAY,EAAI,CAACR,gBAAgB,CAACc,MAAM,CAAE,CAClDG,yBAAyB,CAACV,CAAC,CAAG,IAAI,CAClCpB,KAAK,CAACqB,YAAY,CAAG,CAACR,gBAAgB,CAACc,MAAM,CAC/C,CACF,CACAjB,UAAU,CAACT,sBAAsB,CAAED,KAAK,CAAC,CACzCW,sBAAsB,CACpB2B,YAAY,CACZR,yBAAyB,CAACZ,CAAC,EAAIY,yBAAyB,CAACV,CAAC,CAC1DnB,sBAAsB,CACtBD,KAAK,CACLgB,oBACF,CAAC,CACH,CAAC,CACD,MAAO,CAAAsB,YAAY,CACrB,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var getSwipeSimulator = exports.getSwipeSimulator = function () {\n    var _e = [new global.Error(), -10, -27];\n    var getSwipeSimulator = function (event, screenTransitionConfig, lockAxis) {\n      var screenDimensions = screenTransitionConfig.screenDimensions;\n      var startTimestamp = _getAnimationTimestamp();\n      var isTransitionCanceled = screenTransitionConfig.isTransitionCanceled;\n      var startingPosition = {\n        x: event.translationX,\n        y: event.translationY\n      };\n      var direction = {\n        x: Math.sign(event.translationX),\n        y: Math.sign(event.translationY)\n      };\n      var finalPosition = isTransitionCanceled ? {\n        x: 0,\n        y: 0\n      } : {\n        x: direction.x * screenDimensions.width,\n        y: direction.y * screenDimensions.height\n      };\n      var distance = {\n        x: Math.abs(finalPosition.x - startingPosition.x),\n        y: Math.abs(finalPosition.y - startingPosition.y)\n      };\n      var didScreenReachDestination = {\n        x: false,\n        y: false\n      };\n      var velocity = {\n        x: BASE_VELOCITY,\n        y: BASE_VELOCITY\n      };\n      if (lockAxis === 'x') {\n        velocity.y = 0;\n        velocity.x += ADDITIONAL_VELOCITY_FACTOR_X * distance.x / screenDimensions.width;\n      } else if (lockAxis === 'y') {\n        velocity.x = 0;\n        velocity.y += ADDITIONAL_VELOCITY_FACTOR_Y * distance.y / screenDimensions.height;\n      } else {\n        var euclideanDistance = Math.sqrt(distance.x ** 2 + distance.y ** 2);\n        var screenDiagonal = Math.sqrt(screenDimensions.width ** 2 + screenDimensions.height ** 2);\n        var velocityVectorLength = BASE_VELOCITY + ADDITIONAL_VELOCITY_FACTOR_XY * euclideanDistance / screenDiagonal;\n        if (Math.abs(startingPosition.x) > Math.abs(startingPosition.y)) {\n          velocity.x = velocityVectorLength;\n          velocity.y = velocityVectorLength * Math.abs(startingPosition.y / startingPosition.x);\n        } else {\n          velocity.x = velocityVectorLength * Math.abs(startingPosition.x / startingPosition.y);\n          velocity.y = velocityVectorLength;\n        }\n      }\n      if (isTransitionCanceled) {\n        function didScreenReachDestinationCheck() {\n          if (lockAxis === 'x') {\n            return didScreenReachDestination.x;\n          } else if (lockAxis === 'y') {\n            return didScreenReachDestination.y;\n          } else {\n            return didScreenReachDestination.x && didScreenReachDestination.y;\n          }\n        }\n        function restoreOriginalStyleForBelowTopScreen() {\n          event.translationX = direction.x * screenDimensions.width;\n          event.translationY = direction.y * screenDimensions.height;\n          (0, _styleUpdater.applyStyleForBelowTopScreen)(screenTransitionConfig, event);\n        }\n        var computeFrame = () => {\n          var progress = {\n            x: computeEasingProgress(startTimestamp, distance.x, velocity.x),\n            y: computeEasingProgress(startTimestamp, distance.y, velocity.y)\n          };\n          event.translationX = startingPosition.x - direction.x * distance.x * easing(progress.x);\n          event.translationY = startingPosition.y - direction.y * distance.y * easing(progress.y);\n          if (direction.x > 0) {\n            if (event.translationX <= 0) {\n              didScreenReachDestination.x = true;\n              event.translationX = 0;\n            }\n          } else {\n            if (event.translationX >= 0) {\n              didScreenReachDestination.x = true;\n              event.translationX = 0;\n            }\n          }\n          if (direction.y > 0) {\n            if (event.translationY <= 0) {\n              didScreenReachDestination.y = true;\n              event.translationY = 0;\n            }\n          } else {\n            if (event.translationY >= 0) {\n              didScreenReachDestination.y = true;\n              event.translationY = 0;\n            }\n          }\n          (0, _styleUpdater.applyStyle)(screenTransitionConfig, event);\n          var finished = didScreenReachDestinationCheck();\n          if (finished) {\n            restoreOriginalStyleForBelowTopScreen();\n          }\n          maybeScheduleNextFrame(computeFrame, finished, screenTransitionConfig, event, isTransitionCanceled);\n        };\n        return computeFrame;\n      } else {\n        var _computeFrame = () => {\n          var progress = {\n            x: computeEasingProgress(startTimestamp, distance.x, velocity.x),\n            y: computeEasingProgress(startTimestamp, distance.y, velocity.y)\n          };\n          event.translationX = startingPosition.x + direction.x * distance.x * easing(progress.x);\n          event.translationY = startingPosition.y + direction.y * distance.y * easing(progress.y);\n          if (direction.x > 0) {\n            if (event.translationX >= screenDimensions.width) {\n              didScreenReachDestination.x = true;\n              event.translationX = screenDimensions.width;\n            }\n          } else {\n            if (event.translationX <= -screenDimensions.width) {\n              didScreenReachDestination.x = true;\n              event.translationX = -screenDimensions.width;\n            }\n          }\n          if (direction.y > 0) {\n            if (event.translationY >= screenDimensions.height) {\n              didScreenReachDestination.y = true;\n              event.translationY = screenDimensions.height;\n            }\n          } else {\n            if (event.translationY <= -screenDimensions.height) {\n              didScreenReachDestination.y = true;\n              event.translationY = -screenDimensions.height;\n            }\n          }\n          (0, _styleUpdater.applyStyle)(screenTransitionConfig, event);\n          maybeScheduleNextFrame(_computeFrame, didScreenReachDestination.x || didScreenReachDestination.y, screenTransitionConfig, event, isTransitionCanceled);\n        };\n        return _computeFrame;\n      }\n    };\n    getSwipeSimulator.__closure = {\n      BASE_VELOCITY,\n      ADDITIONAL_VELOCITY_FACTOR_X,\n      ADDITIONAL_VELOCITY_FACTOR_Y,\n      ADDITIONAL_VELOCITY_FACTOR_XY,\n      applyStyleForBelowTopScreen: _styleUpdater.applyStyleForBelowTopScreen,\n      computeEasingProgress,\n      easing,\n      applyStyle: _styleUpdater.applyStyle,\n      maybeScheduleNextFrame\n    };\n    getSwipeSimulator.__workletHash = 17409639634070;\n    getSwipeSimulator.__initData = _worklet_17409639634070_init_data;\n    getSwipeSimulator.__stackDetails = _e;\n    return getSwipeSimulator;\n  }();\n});", "lineCount": 265, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getSwipeSimulator"], [7, 27, 1, 13], [8, 2, 7, 0], [8, 6, 7, 0, "_RNScreensTurboModule"], [8, 27, 7, 0], [8, 30, 7, 0, "require"], [8, 37, 7, 0], [8, 38, 7, 0, "_dependencyMap"], [8, 52, 7, 0], [9, 2, 8, 0], [9, 6, 8, 0, "_styleUpdater"], [9, 19, 8, 0], [9, 22, 8, 0, "require"], [9, 29, 8, 0], [9, 30, 8, 0, "_dependencyMap"], [9, 44, 8, 0], [10, 2, 10, 0], [10, 6, 10, 6, "BASE_VELOCITY"], [10, 19, 10, 19], [10, 22, 10, 22], [10, 25, 10, 25], [11, 2, 11, 0], [11, 6, 11, 6, "ADDITIONAL_VELOCITY_FACTOR_X"], [11, 34, 11, 34], [11, 37, 11, 37], [11, 40, 11, 40], [12, 2, 12, 0], [12, 6, 12, 6, "ADDITIONAL_VELOCITY_FACTOR_Y"], [12, 34, 12, 34], [12, 37, 12, 37], [12, 40, 12, 40], [13, 2, 13, 0], [13, 6, 13, 6, "ADDITIONAL_VELOCITY_FACTOR_XY"], [13, 35, 13, 35], [13, 38, 13, 38], [13, 41, 13, 41], [14, 2, 13, 42], [14, 6, 13, 42, "_worklet_3339184140838_init_data"], [14, 38, 13, 42], [15, 4, 13, 42, "code"], [15, 8, 13, 42], [16, 4, 13, 42, "location"], [16, 12, 13, 42], [17, 4, 13, 42, "sourceMap"], [17, 13, 13, 42], [18, 4, 13, 42, "version"], [18, 11, 13, 42], [19, 2, 13, 42], [20, 2, 13, 42], [20, 6, 13, 42, "computeEasingProgress"], [20, 27, 13, 42], [20, 30, 15, 0], [21, 4, 15, 0], [21, 8, 15, 0, "_e"], [21, 10, 15, 0], [21, 18, 15, 0, "global"], [21, 24, 15, 0], [21, 25, 15, 0, "Error"], [21, 30, 15, 0], [22, 4, 15, 0], [22, 8, 15, 0, "computeEasingProgress"], [22, 29, 15, 0], [22, 41, 15, 0, "computeEasingProgress"], [22, 42, 16, 2, "startingTimestamp"], [22, 59, 16, 27], [22, 61, 17, 2, "distance"], [22, 69, 17, 18], [22, 71, 18, 2, "velocity"], [22, 79, 18, 18], [22, 81, 19, 2], [23, 6, 21, 2], [23, 10, 21, 6, "Math"], [23, 14, 21, 10], [23, 15, 21, 11, "abs"], [23, 18, 21, 14], [23, 19, 21, 15, "distance"], [23, 27, 21, 23], [23, 28, 21, 24], [23, 31, 21, 27], [23, 32, 21, 28], [23, 34, 21, 30], [24, 8, 22, 4], [24, 15, 22, 11], [24, 16, 22, 12], [25, 6, 23, 2], [26, 6, 24, 2], [26, 10, 24, 8, "elapsedTime"], [26, 21, 24, 19], [26, 24, 24, 22], [26, 25, 24, 23, "_getAnimationTimestamp"], [26, 47, 24, 45], [26, 48, 24, 46], [26, 49, 24, 47], [26, 52, 24, 50, "startingTimestamp"], [26, 69, 24, 67], [26, 73, 24, 71], [26, 77, 24, 75], [27, 6, 25, 2], [27, 10, 25, 8, "currentPosition"], [27, 25, 25, 23], [27, 28, 25, 26, "velocity"], [27, 36, 25, 34], [27, 39, 25, 37, "elapsedTime"], [27, 50, 25, 48], [28, 6, 26, 2], [28, 10, 26, 8, "progress"], [28, 18, 26, 16], [28, 21, 26, 19, "currentPosition"], [28, 36, 26, 34], [28, 39, 26, 37, "distance"], [28, 47, 26, 45], [29, 6, 27, 2], [29, 13, 27, 9, "progress"], [29, 21, 27, 17], [30, 4, 28, 0], [30, 5, 28, 1], [31, 4, 28, 1, "computeEasingProgress"], [31, 25, 28, 1], [31, 26, 28, 1, "__closure"], [31, 35, 28, 1], [32, 4, 28, 1, "computeEasingProgress"], [32, 25, 28, 1], [32, 26, 28, 1, "__workletHash"], [32, 39, 28, 1], [33, 4, 28, 1, "computeEasingProgress"], [33, 25, 28, 1], [33, 26, 28, 1, "__initData"], [33, 36, 28, 1], [33, 39, 28, 1, "_worklet_3339184140838_init_data"], [33, 71, 28, 1], [34, 4, 28, 1, "computeEasingProgress"], [34, 25, 28, 1], [34, 26, 28, 1, "__stackDetails"], [34, 40, 28, 1], [34, 43, 28, 1, "_e"], [34, 45, 28, 1], [35, 4, 28, 1], [35, 11, 28, 1, "computeEasingProgress"], [35, 32, 28, 1], [36, 2, 28, 1], [36, 3, 15, 0], [37, 2, 15, 0], [37, 6, 15, 0, "_worklet_558927583902_init_data"], [37, 37, 15, 0], [38, 4, 15, 0, "code"], [38, 8, 15, 0], [39, 4, 15, 0, "location"], [39, 12, 15, 0], [40, 4, 15, 0, "sourceMap"], [40, 13, 15, 0], [41, 4, 15, 0, "version"], [41, 11, 15, 0], [42, 2, 15, 0], [43, 2, 15, 0], [43, 6, 15, 0, "easing"], [43, 12, 15, 0], [43, 15, 30, 0], [44, 4, 30, 0], [44, 8, 30, 0, "_e"], [44, 10, 30, 0], [44, 18, 30, 0, "global"], [44, 24, 30, 0], [44, 25, 30, 0, "Error"], [44, 30, 30, 0], [45, 4, 30, 0], [45, 8, 30, 0, "easing"], [45, 14, 30, 0], [45, 26, 30, 0, "easing"], [45, 27, 30, 16, "x"], [45, 28, 30, 25], [45, 30, 30, 35], [46, 6, 32, 2], [47, 6, 33, 2], [47, 13, 33, 9], [47, 14, 33, 10], [47, 17, 33, 13, "Math"], [47, 21, 33, 17], [47, 22, 33, 18, "pow"], [47, 25, 33, 21], [47, 26, 33, 22], [47, 27, 33, 23], [47, 30, 33, 26, "x"], [47, 31, 33, 27], [47, 33, 33, 29], [47, 34, 33, 30], [47, 35, 33, 31], [48, 4, 34, 0], [48, 5, 34, 1], [49, 4, 34, 1, "easing"], [49, 10, 34, 1], [49, 11, 34, 1, "__closure"], [49, 20, 34, 1], [50, 4, 34, 1, "easing"], [50, 10, 34, 1], [50, 11, 34, 1, "__workletHash"], [50, 24, 34, 1], [51, 4, 34, 1, "easing"], [51, 10, 34, 1], [51, 11, 34, 1, "__initData"], [51, 21, 34, 1], [51, 24, 34, 1, "_worklet_558927583902_init_data"], [51, 55, 34, 1], [52, 4, 34, 1, "easing"], [52, 10, 34, 1], [52, 11, 34, 1, "__stackDetails"], [52, 25, 34, 1], [52, 28, 34, 1, "_e"], [52, 30, 34, 1], [53, 4, 34, 1], [53, 11, 34, 1, "easing"], [53, 17, 34, 1], [54, 2, 34, 1], [54, 3, 30, 0], [55, 2, 30, 0], [55, 6, 30, 0, "_worklet_11469122063489_init_data"], [55, 39, 30, 0], [56, 4, 30, 0, "code"], [56, 8, 30, 0], [57, 4, 30, 0, "location"], [57, 12, 30, 0], [58, 4, 30, 0, "sourceMap"], [58, 13, 30, 0], [59, 4, 30, 0, "version"], [59, 11, 30, 0], [60, 2, 30, 0], [61, 2, 30, 0], [61, 6, 30, 0, "computeProgress"], [61, 21, 30, 0], [61, 24, 36, 0], [62, 4, 36, 0], [62, 8, 36, 0, "_e"], [62, 10, 36, 0], [62, 18, 36, 0, "global"], [62, 24, 36, 0], [62, 25, 36, 0, "Error"], [62, 30, 36, 0], [63, 4, 36, 0], [63, 8, 36, 0, "computeProgress"], [63, 23, 36, 0], [63, 35, 36, 0, "computeProgress"], [63, 36, 37, 2, "screenTransitionConfig"], [63, 58, 37, 48], [63, 60, 38, 2, "event"], [63, 65, 38, 38], [63, 67, 39, 2, "isTransitionCanceled"], [63, 87, 39, 31], [63, 89, 40, 2], [64, 6, 42, 2], [64, 10, 42, 8, "screenDimensions"], [64, 26, 42, 24], [64, 29, 42, 27, "screenTransitionConfig"], [64, 51, 42, 49], [64, 52, 42, 50, "screenDimensions"], [64, 68, 42, 66], [65, 6, 43, 2], [65, 10, 43, 8, "progressX"], [65, 19, 43, 17], [65, 22, 43, 20, "Math"], [65, 26, 43, 24], [65, 27, 43, 25, "abs"], [65, 30, 43, 28], [65, 31, 43, 29, "event"], [65, 36, 43, 34], [65, 37, 43, 35, "translationX"], [65, 49, 43, 47], [65, 52, 43, 50, "screenDimensions"], [65, 68, 43, 66], [65, 69, 43, 67, "width"], [65, 74, 43, 72], [65, 75, 43, 73], [66, 6, 44, 2], [66, 10, 44, 8, "progressY"], [66, 19, 44, 17], [66, 22, 44, 20, "Math"], [66, 26, 44, 24], [66, 27, 44, 25, "abs"], [66, 30, 44, 28], [66, 31, 44, 29, "event"], [66, 36, 44, 34], [66, 37, 44, 35, "translationY"], [66, 49, 44, 47], [66, 52, 44, 50, "screenDimensions"], [66, 68, 44, 66], [66, 69, 44, 67, "height"], [66, 75, 44, 73], [66, 76, 44, 74], [67, 6, 45, 2], [67, 10, 45, 8, "max<PERSON>rogress"], [67, 21, 45, 19], [67, 24, 45, 22, "Math"], [67, 28, 45, 26], [67, 29, 45, 27, "max"], [67, 32, 45, 30], [67, 33, 45, 31, "progressX"], [67, 42, 45, 40], [67, 44, 45, 42, "progressY"], [67, 53, 45, 51], [67, 54, 45, 52], [68, 6, 46, 2], [68, 10, 46, 8, "progress"], [68, 18, 46, 16], [68, 21, 46, 19, "isTransitionCanceled"], [68, 41, 46, 39], [68, 44, 46, 42, "max<PERSON>rogress"], [68, 55, 46, 53], [68, 58, 46, 56], [68, 59, 46, 57], [68, 62, 46, 60, "max<PERSON>rogress"], [68, 73, 46, 71], [69, 6, 47, 2], [69, 13, 47, 9, "progress"], [69, 21, 47, 17], [70, 4, 48, 0], [70, 5, 48, 1], [71, 4, 48, 1, "computeProgress"], [71, 19, 48, 1], [71, 20, 48, 1, "__closure"], [71, 29, 48, 1], [72, 4, 48, 1, "computeProgress"], [72, 19, 48, 1], [72, 20, 48, 1, "__workletHash"], [72, 33, 48, 1], [73, 4, 48, 1, "computeProgress"], [73, 19, 48, 1], [73, 20, 48, 1, "__initData"], [73, 30, 48, 1], [73, 33, 48, 1, "_worklet_11469122063489_init_data"], [73, 66, 48, 1], [74, 4, 48, 1, "computeProgress"], [74, 19, 48, 1], [74, 20, 48, 1, "__stackDetails"], [74, 34, 48, 1], [74, 37, 48, 1, "_e"], [74, 39, 48, 1], [75, 4, 48, 1], [75, 11, 48, 1, "computeProgress"], [75, 26, 48, 1], [76, 2, 48, 1], [76, 3, 36, 0], [77, 2, 36, 0], [77, 6, 36, 0, "_worklet_14642652832736_init_data"], [77, 39, 36, 0], [78, 4, 36, 0, "code"], [78, 8, 36, 0], [79, 4, 36, 0, "location"], [79, 12, 36, 0], [80, 4, 36, 0, "sourceMap"], [80, 13, 36, 0], [81, 4, 36, 0, "version"], [81, 11, 36, 0], [82, 2, 36, 0], [83, 2, 36, 0], [83, 6, 36, 0, "maybeScheduleNextFrame"], [83, 28, 36, 0], [83, 31, 50, 0], [84, 4, 50, 0], [84, 8, 50, 0, "_e"], [84, 10, 50, 0], [84, 18, 50, 0, "global"], [84, 24, 50, 0], [84, 25, 50, 0, "Error"], [84, 30, 50, 0], [85, 4, 50, 0], [85, 8, 50, 0, "maybeScheduleNextFrame"], [85, 30, 50, 0], [85, 42, 50, 0, "maybeScheduleNextFrame"], [85, 43, 51, 2, "step"], [85, 47, 51, 18], [85, 49, 52, 2, "didScreenReachDestination"], [85, 74, 52, 36], [85, 76, 53, 2, "screenTransitionConfig"], [85, 98, 53, 48], [85, 100, 54, 2, "event"], [85, 105, 54, 38], [85, 107, 55, 2, "isTransitionCanceled"], [85, 127, 55, 31], [85, 129, 56, 2], [86, 6, 58, 2], [86, 10, 58, 6], [86, 11, 58, 7, "didScreenReachDestination"], [86, 36, 58, 32], [86, 38, 58, 34], [87, 8, 59, 4], [87, 12, 59, 10, "stackTag"], [87, 20, 59, 18], [87, 23, 59, 21, "screenTransitionConfig"], [87, 45, 59, 43], [87, 46, 59, 44, "stackTag"], [87, 54, 59, 52], [88, 8, 60, 4], [88, 12, 60, 10, "progress"], [88, 20, 60, 18], [88, 23, 60, 21, "computeProgress"], [88, 38, 60, 36], [88, 39, 61, 6, "screenTransitionConfig"], [88, 61, 61, 28], [88, 63, 62, 6, "event"], [88, 68, 62, 11], [88, 70, 63, 6, "isTransitionCanceled"], [88, 90, 64, 4], [88, 91, 64, 5], [89, 8, 65, 4, "RNScreensTurboModule"], [89, 50, 65, 24], [89, 51, 65, 25, "updateTransition"], [89, 67, 65, 41], [89, 68, 65, 42, "stackTag"], [89, 76, 65, 50], [89, 78, 65, 52, "progress"], [89, 86, 65, 60], [89, 87, 65, 61], [90, 8, 66, 4, "requestAnimationFrame"], [90, 29, 66, 25], [90, 30, 66, 26, "step"], [90, 34, 66, 30], [90, 35, 66, 31], [91, 6, 67, 2], [91, 7, 67, 3], [91, 13, 67, 9], [92, 8, 68, 4, "screenTransitionConfig"], [92, 30, 68, 26], [92, 31, 68, 27, "onFinishAnimation"], [92, 48, 68, 44], [92, 51, 68, 47], [92, 52, 68, 48], [93, 6, 69, 2], [94, 4, 70, 0], [94, 5, 70, 1], [95, 4, 70, 1, "maybeScheduleNextFrame"], [95, 26, 70, 1], [95, 27, 70, 1, "__closure"], [95, 36, 70, 1], [96, 6, 70, 1, "computeProgress"], [96, 21, 70, 1], [97, 6, 70, 1, "RNScreensTurboModule"], [97, 26, 70, 1], [97, 28, 65, 4, "RNScreensTurboModule"], [98, 4, 65, 24], [99, 4, 65, 24, "maybeScheduleNextFrame"], [99, 26, 65, 24], [99, 27, 65, 24, "__workletHash"], [99, 40, 65, 24], [100, 4, 65, 24, "maybeScheduleNextFrame"], [100, 26, 65, 24], [100, 27, 65, 24, "__initData"], [100, 37, 65, 24], [100, 40, 65, 24, "_worklet_14642652832736_init_data"], [100, 73, 65, 24], [101, 4, 65, 24, "maybeScheduleNextFrame"], [101, 26, 65, 24], [101, 27, 65, 24, "__stackDetails"], [101, 41, 65, 24], [101, 44, 65, 24, "_e"], [101, 46, 65, 24], [102, 4, 65, 24], [102, 11, 65, 24, "maybeScheduleNextFrame"], [102, 33, 65, 24], [103, 2, 65, 24], [103, 3, 50, 0], [104, 2, 50, 0], [104, 6, 50, 0, "_worklet_17409639634070_init_data"], [104, 39, 50, 0], [105, 4, 50, 0, "code"], [105, 8, 50, 0], [106, 4, 50, 0, "location"], [106, 12, 50, 0], [107, 4, 50, 0, "sourceMap"], [107, 13, 50, 0], [108, 4, 50, 0, "version"], [108, 11, 50, 0], [109, 2, 50, 0], [110, 2, 50, 0], [110, 6, 50, 0, "getSwipeSimulator"], [110, 23, 50, 0], [110, 26, 50, 0, "exports"], [110, 33, 50, 0], [110, 34, 50, 0, "getSwipeSimulator"], [110, 51, 50, 0], [110, 54, 72, 7], [111, 4, 72, 7], [111, 8, 72, 7, "_e"], [111, 10, 72, 7], [111, 18, 72, 7, "global"], [111, 24, 72, 7], [111, 25, 72, 7, "Error"], [111, 30, 72, 7], [112, 4, 72, 7], [112, 8, 72, 7, "getSwipeSimulator"], [112, 25, 72, 7], [112, 37, 72, 7, "getSwipeSimulator"], [112, 38, 73, 2, "event"], [112, 43, 73, 38], [112, 45, 74, 2, "screenTransitionConfig"], [112, 67, 74, 48], [112, 69, 75, 2, "lockAxis"], [112, 77, 75, 21], [112, 79, 76, 2], [113, 6, 78, 2], [113, 10, 78, 8, "screenDimensions"], [113, 26, 78, 24], [113, 29, 78, 27, "screenTransitionConfig"], [113, 51, 78, 49], [113, 52, 78, 50, "screenDimensions"], [113, 68, 78, 66], [114, 6, 79, 2], [114, 10, 79, 8, "startTimestamp"], [114, 24, 79, 22], [114, 27, 79, 25, "_getAnimationTimestamp"], [114, 49, 79, 47], [114, 50, 79, 48], [114, 51, 79, 49], [115, 6, 80, 2], [115, 10, 80, 10, "isTransitionCanceled"], [115, 30, 80, 30], [115, 33, 80, 35, "screenTransitionConfig"], [115, 55, 80, 57], [115, 56, 80, 10, "isTransitionCanceled"], [115, 76, 80, 30], [116, 6, 81, 2], [116, 10, 81, 8, "startingPosition"], [116, 26, 81, 24], [116, 29, 81, 27], [117, 8, 82, 4, "x"], [117, 9, 82, 5], [117, 11, 82, 7, "event"], [117, 16, 82, 12], [117, 17, 82, 13, "translationX"], [117, 29, 82, 25], [118, 8, 83, 4, "y"], [118, 9, 83, 5], [118, 11, 83, 7, "event"], [118, 16, 83, 12], [118, 17, 83, 13, "translationY"], [119, 6, 84, 2], [119, 7, 84, 3], [120, 6, 85, 2], [120, 10, 85, 8, "direction"], [120, 19, 85, 17], [120, 22, 85, 20], [121, 8, 86, 4, "x"], [121, 9, 86, 5], [121, 11, 86, 7, "Math"], [121, 15, 86, 11], [121, 16, 86, 12, "sign"], [121, 20, 86, 16], [121, 21, 86, 17, "event"], [121, 26, 86, 22], [121, 27, 86, 23, "translationX"], [121, 39, 86, 35], [121, 40, 86, 36], [122, 8, 87, 4, "y"], [122, 9, 87, 5], [122, 11, 87, 7, "Math"], [122, 15, 87, 11], [122, 16, 87, 12, "sign"], [122, 20, 87, 16], [122, 21, 87, 17, "event"], [122, 26, 87, 22], [122, 27, 87, 23, "translationY"], [122, 39, 87, 35], [123, 6, 88, 2], [123, 7, 88, 3], [124, 6, 89, 2], [124, 10, 89, 8, "finalPosition"], [124, 23, 89, 21], [124, 26, 89, 24, "isTransitionCanceled"], [124, 46, 89, 44], [124, 49, 90, 6], [125, 8, 90, 8, "x"], [125, 9, 90, 9], [125, 11, 90, 11], [125, 12, 90, 12], [126, 8, 90, 14, "y"], [126, 9, 90, 15], [126, 11, 90, 17], [127, 6, 90, 19], [127, 7, 90, 20], [127, 10, 91, 6], [128, 8, 92, 8, "x"], [128, 9, 92, 9], [128, 11, 92, 11, "direction"], [128, 20, 92, 20], [128, 21, 92, 21, "x"], [128, 22, 92, 22], [128, 25, 92, 25, "screenDimensions"], [128, 41, 92, 41], [128, 42, 92, 42, "width"], [128, 47, 92, 47], [129, 8, 93, 8, "y"], [129, 9, 93, 9], [129, 11, 93, 11, "direction"], [129, 20, 93, 20], [129, 21, 93, 21, "y"], [129, 22, 93, 22], [129, 25, 93, 25, "screenDimensions"], [129, 41, 93, 41], [129, 42, 93, 42, "height"], [130, 6, 94, 6], [130, 7, 94, 7], [131, 6, 95, 2], [131, 10, 95, 8, "distance"], [131, 18, 95, 16], [131, 21, 95, 19], [132, 8, 96, 4, "x"], [132, 9, 96, 5], [132, 11, 96, 7, "Math"], [132, 15, 96, 11], [132, 16, 96, 12, "abs"], [132, 19, 96, 15], [132, 20, 96, 16, "finalPosition"], [132, 33, 96, 29], [132, 34, 96, 30, "x"], [132, 35, 96, 31], [132, 38, 96, 34, "startingPosition"], [132, 54, 96, 50], [132, 55, 96, 51, "x"], [132, 56, 96, 52], [132, 57, 96, 53], [133, 8, 97, 4, "y"], [133, 9, 97, 5], [133, 11, 97, 7, "Math"], [133, 15, 97, 11], [133, 16, 97, 12, "abs"], [133, 19, 97, 15], [133, 20, 97, 16, "finalPosition"], [133, 33, 97, 29], [133, 34, 97, 30, "y"], [133, 35, 97, 31], [133, 38, 97, 34, "startingPosition"], [133, 54, 97, 50], [133, 55, 97, 51, "y"], [133, 56, 97, 52], [134, 6, 98, 2], [134, 7, 98, 3], [135, 6, 99, 2], [135, 10, 99, 8, "didScreenReachDestination"], [135, 35, 99, 33], [135, 38, 99, 36], [136, 8, 100, 4, "x"], [136, 9, 100, 5], [136, 11, 100, 7], [136, 16, 100, 12], [137, 8, 101, 4, "y"], [137, 9, 101, 5], [137, 11, 101, 7], [138, 6, 102, 2], [138, 7, 102, 3], [139, 6, 103, 2], [139, 10, 103, 8, "velocity"], [139, 18, 103, 16], [139, 21, 103, 19], [140, 8, 103, 21, "x"], [140, 9, 103, 22], [140, 11, 103, 24, "BASE_VELOCITY"], [140, 24, 103, 37], [141, 8, 103, 39, "y"], [141, 9, 103, 40], [141, 11, 103, 42, "BASE_VELOCITY"], [142, 6, 103, 56], [142, 7, 103, 57], [143, 6, 104, 2], [143, 10, 104, 6, "lockAxis"], [143, 18, 104, 14], [143, 23, 104, 19], [143, 26, 104, 22], [143, 28, 104, 24], [144, 8, 105, 4, "velocity"], [144, 16, 105, 12], [144, 17, 105, 13, "y"], [144, 18, 105, 14], [144, 21, 105, 17], [144, 22, 105, 18], [145, 8, 106, 4, "velocity"], [145, 16, 106, 12], [145, 17, 106, 13, "x"], [145, 18, 106, 14], [145, 22, 107, 7, "ADDITIONAL_VELOCITY_FACTOR_X"], [145, 50, 107, 35], [145, 53, 107, 38, "distance"], [145, 61, 107, 46], [145, 62, 107, 47, "x"], [145, 63, 107, 48], [145, 66, 107, 52, "screenDimensions"], [145, 82, 107, 68], [145, 83, 107, 69, "width"], [145, 88, 107, 74], [146, 6, 108, 2], [146, 7, 108, 3], [146, 13, 108, 9], [146, 17, 108, 13, "lockAxis"], [146, 25, 108, 21], [146, 30, 108, 26], [146, 33, 108, 29], [146, 35, 108, 31], [147, 8, 109, 4, "velocity"], [147, 16, 109, 12], [147, 17, 109, 13, "x"], [147, 18, 109, 14], [147, 21, 109, 17], [147, 22, 109, 18], [148, 8, 110, 4, "velocity"], [148, 16, 110, 12], [148, 17, 110, 13, "y"], [148, 18, 110, 14], [148, 22, 111, 7, "ADDITIONAL_VELOCITY_FACTOR_Y"], [148, 50, 111, 35], [148, 53, 111, 38, "distance"], [148, 61, 111, 46], [148, 62, 111, 47, "y"], [148, 63, 111, 48], [148, 66, 111, 52, "screenDimensions"], [148, 82, 111, 68], [148, 83, 111, 69, "height"], [148, 89, 111, 75], [149, 6, 112, 2], [149, 7, 112, 3], [149, 13, 112, 9], [150, 8, 113, 4], [150, 12, 113, 10, "euclideanDistance"], [150, 29, 113, 27], [150, 32, 113, 30, "Math"], [150, 36, 113, 34], [150, 37, 113, 35, "sqrt"], [150, 41, 113, 39], [150, 42, 113, 40, "distance"], [150, 50, 113, 48], [150, 51, 113, 49, "x"], [150, 52, 113, 50], [150, 56, 113, 54], [150, 57, 113, 55], [150, 60, 113, 58, "distance"], [150, 68, 113, 66], [150, 69, 113, 67, "y"], [150, 70, 113, 68], [150, 74, 113, 72], [150, 75, 113, 73], [150, 76, 113, 74], [151, 8, 114, 4], [151, 12, 114, 10, "screenDiagonal"], [151, 26, 114, 24], [151, 29, 114, 27, "Math"], [151, 33, 114, 31], [151, 34, 114, 32, "sqrt"], [151, 38, 114, 36], [151, 39, 115, 6, "screenDimensions"], [151, 55, 115, 22], [151, 56, 115, 23, "width"], [151, 61, 115, 28], [151, 65, 115, 32], [151, 66, 115, 33], [151, 69, 115, 36, "screenDimensions"], [151, 85, 115, 52], [151, 86, 115, 53, "height"], [151, 92, 115, 59], [151, 96, 115, 63], [151, 97, 116, 4], [151, 98, 116, 5], [152, 8, 117, 4], [152, 12, 117, 10, "velocityVectorLength"], [152, 32, 117, 30], [152, 35, 118, 6, "BASE_VELOCITY"], [152, 48, 118, 19], [152, 51, 119, 7, "ADDITIONAL_VELOCITY_FACTOR_XY"], [152, 80, 119, 36], [152, 83, 119, 39, "euclideanDistance"], [152, 100, 119, 56], [152, 103, 119, 60, "screenDiagonal"], [152, 117, 119, 74], [153, 8, 120, 4], [153, 12, 120, 8, "Math"], [153, 16, 120, 12], [153, 17, 120, 13, "abs"], [153, 20, 120, 16], [153, 21, 120, 17, "startingPosition"], [153, 37, 120, 33], [153, 38, 120, 34, "x"], [153, 39, 120, 35], [153, 40, 120, 36], [153, 43, 120, 39, "Math"], [153, 47, 120, 43], [153, 48, 120, 44, "abs"], [153, 51, 120, 47], [153, 52, 120, 48, "startingPosition"], [153, 68, 120, 64], [153, 69, 120, 65, "y"], [153, 70, 120, 66], [153, 71, 120, 67], [153, 73, 120, 69], [154, 10, 121, 6, "velocity"], [154, 18, 121, 14], [154, 19, 121, 15, "x"], [154, 20, 121, 16], [154, 23, 121, 19, "velocityVectorLength"], [154, 43, 121, 39], [155, 10, 122, 6, "velocity"], [155, 18, 122, 14], [155, 19, 122, 15, "y"], [155, 20, 122, 16], [155, 23, 123, 8, "velocityVectorLength"], [155, 43, 123, 28], [155, 46, 124, 8, "Math"], [155, 50, 124, 12], [155, 51, 124, 13, "abs"], [155, 54, 124, 16], [155, 55, 124, 17, "startingPosition"], [155, 71, 124, 33], [155, 72, 124, 34, "y"], [155, 73, 124, 35], [155, 76, 124, 38, "startingPosition"], [155, 92, 124, 54], [155, 93, 124, 55, "x"], [155, 94, 124, 56], [155, 95, 124, 57], [156, 8, 125, 4], [156, 9, 125, 5], [156, 15, 125, 11], [157, 10, 126, 6, "velocity"], [157, 18, 126, 14], [157, 19, 126, 15, "x"], [157, 20, 126, 16], [157, 23, 127, 8, "velocityVectorLength"], [157, 43, 127, 28], [157, 46, 128, 8, "Math"], [157, 50, 128, 12], [157, 51, 128, 13, "abs"], [157, 54, 128, 16], [157, 55, 128, 17, "startingPosition"], [157, 71, 128, 33], [157, 72, 128, 34, "x"], [157, 73, 128, 35], [157, 76, 128, 38, "startingPosition"], [157, 92, 128, 54], [157, 93, 128, 55, "y"], [157, 94, 128, 56], [157, 95, 128, 57], [158, 10, 129, 6, "velocity"], [158, 18, 129, 14], [158, 19, 129, 15, "y"], [158, 20, 129, 16], [158, 23, 129, 19, "velocityVectorLength"], [158, 43, 129, 39], [159, 8, 130, 4], [160, 6, 131, 2], [161, 6, 133, 2], [161, 10, 133, 6, "isTransitionCanceled"], [161, 30, 133, 26], [161, 32, 133, 28], [162, 8, 134, 4], [162, 17, 134, 13, "didScreenReachDestinationCheck"], [162, 47, 134, 43, "didScreenReachDestinationCheck"], [162, 48, 134, 43], [162, 50, 134, 46], [163, 10, 135, 6], [163, 14, 135, 10, "lockAxis"], [163, 22, 135, 18], [163, 27, 135, 23], [163, 30, 135, 26], [163, 32, 135, 28], [164, 12, 136, 8], [164, 19, 136, 15, "didScreenReachDestination"], [164, 44, 136, 40], [164, 45, 136, 41, "x"], [164, 46, 136, 42], [165, 10, 137, 6], [165, 11, 137, 7], [165, 17, 137, 13], [165, 21, 137, 17, "lockAxis"], [165, 29, 137, 25], [165, 34, 137, 30], [165, 37, 137, 33], [165, 39, 137, 35], [166, 12, 138, 8], [166, 19, 138, 15, "didScreenReachDestination"], [166, 44, 138, 40], [166, 45, 138, 41, "y"], [166, 46, 138, 42], [167, 10, 139, 6], [167, 11, 139, 7], [167, 17, 139, 13], [168, 12, 140, 8], [168, 19, 140, 15, "didScreenReachDestination"], [168, 44, 140, 40], [168, 45, 140, 41, "x"], [168, 46, 140, 42], [168, 50, 140, 46, "didScreenReachDestination"], [168, 75, 140, 71], [168, 76, 140, 72, "y"], [168, 77, 140, 73], [169, 10, 141, 6], [170, 8, 142, 4], [171, 8, 144, 4], [171, 17, 144, 13, "restoreOriginalStyleForBelowTopScreen"], [171, 54, 144, 50, "restoreOriginalStyleForBelowTopScreen"], [171, 55, 144, 50], [171, 57, 144, 53], [172, 10, 145, 6, "event"], [172, 15, 145, 11], [172, 16, 145, 12, "translationX"], [172, 28, 145, 24], [172, 31, 145, 27, "direction"], [172, 40, 145, 36], [172, 41, 145, 37, "x"], [172, 42, 145, 38], [172, 45, 145, 41, "screenDimensions"], [172, 61, 145, 57], [172, 62, 145, 58, "width"], [172, 67, 145, 63], [173, 10, 146, 6, "event"], [173, 15, 146, 11], [173, 16, 146, 12, "translationY"], [173, 28, 146, 24], [173, 31, 146, 27, "direction"], [173, 40, 146, 36], [173, 41, 146, 37, "y"], [173, 42, 146, 38], [173, 45, 146, 41, "screenDimensions"], [173, 61, 146, 57], [173, 62, 146, 58, "height"], [173, 68, 146, 64], [174, 10, 147, 6], [174, 14, 147, 6, "applyStyleForBelowTopScreen"], [174, 55, 147, 33], [174, 57, 147, 34, "screenTransitionConfig"], [174, 79, 147, 56], [174, 81, 147, 58, "event"], [174, 86, 147, 63], [174, 87, 147, 64], [175, 8, 148, 4], [176, 8, 150, 4], [176, 12, 150, 10, "computeFrame"], [176, 24, 150, 22], [176, 27, 150, 25, "computeFrame"], [176, 28, 150, 25], [176, 33, 150, 31], [177, 10, 151, 6], [177, 14, 151, 12, "progress"], [177, 22, 151, 20], [177, 25, 151, 23], [178, 12, 152, 8, "x"], [178, 13, 152, 9], [178, 15, 152, 11, "computeEasingProgress"], [178, 36, 152, 32], [178, 37, 152, 33, "startTimestamp"], [178, 51, 152, 47], [178, 53, 152, 49, "distance"], [178, 61, 152, 57], [178, 62, 152, 58, "x"], [178, 63, 152, 59], [178, 65, 152, 61, "velocity"], [178, 73, 152, 69], [178, 74, 152, 70, "x"], [178, 75, 152, 71], [178, 76, 152, 72], [179, 12, 153, 8, "y"], [179, 13, 153, 9], [179, 15, 153, 11, "computeEasingProgress"], [179, 36, 153, 32], [179, 37, 153, 33, "startTimestamp"], [179, 51, 153, 47], [179, 53, 153, 49, "distance"], [179, 61, 153, 57], [179, 62, 153, 58, "y"], [179, 63, 153, 59], [179, 65, 153, 61, "velocity"], [179, 73, 153, 69], [179, 74, 153, 70, "y"], [179, 75, 153, 71], [180, 10, 154, 6], [180, 11, 154, 7], [181, 10, 155, 6, "event"], [181, 15, 155, 11], [181, 16, 155, 12, "translationX"], [181, 28, 155, 24], [181, 31, 156, 8, "startingPosition"], [181, 47, 156, 24], [181, 48, 156, 25, "x"], [181, 49, 156, 26], [181, 52, 156, 29, "direction"], [181, 61, 156, 38], [181, 62, 156, 39, "x"], [181, 63, 156, 40], [181, 66, 156, 43, "distance"], [181, 74, 156, 51], [181, 75, 156, 52, "x"], [181, 76, 156, 53], [181, 79, 156, 56, "easing"], [181, 85, 156, 62], [181, 86, 156, 63, "progress"], [181, 94, 156, 71], [181, 95, 156, 72, "x"], [181, 96, 156, 73], [181, 97, 156, 74], [182, 10, 157, 6, "event"], [182, 15, 157, 11], [182, 16, 157, 12, "translationY"], [182, 28, 157, 24], [182, 31, 158, 8, "startingPosition"], [182, 47, 158, 24], [182, 48, 158, 25, "y"], [182, 49, 158, 26], [182, 52, 158, 29, "direction"], [182, 61, 158, 38], [182, 62, 158, 39, "y"], [182, 63, 158, 40], [182, 66, 158, 43, "distance"], [182, 74, 158, 51], [182, 75, 158, 52, "y"], [182, 76, 158, 53], [182, 79, 158, 56, "easing"], [182, 85, 158, 62], [182, 86, 158, 63, "progress"], [182, 94, 158, 71], [182, 95, 158, 72, "y"], [182, 96, 158, 73], [182, 97, 158, 74], [183, 10, 159, 6], [183, 14, 159, 10, "direction"], [183, 23, 159, 19], [183, 24, 159, 20, "x"], [183, 25, 159, 21], [183, 28, 159, 24], [183, 29, 159, 25], [183, 31, 159, 27], [184, 12, 160, 8], [184, 16, 160, 12, "event"], [184, 21, 160, 17], [184, 22, 160, 18, "translationX"], [184, 34, 160, 30], [184, 38, 160, 34], [184, 39, 160, 35], [184, 41, 160, 37], [185, 14, 161, 10, "didScreenReachDestination"], [185, 39, 161, 35], [185, 40, 161, 36, "x"], [185, 41, 161, 37], [185, 44, 161, 40], [185, 48, 161, 44], [186, 14, 162, 10, "event"], [186, 19, 162, 15], [186, 20, 162, 16, "translationX"], [186, 32, 162, 28], [186, 35, 162, 31], [186, 36, 162, 32], [187, 12, 163, 8], [188, 10, 164, 6], [188, 11, 164, 7], [188, 17, 164, 13], [189, 12, 165, 8], [189, 16, 165, 12, "event"], [189, 21, 165, 17], [189, 22, 165, 18, "translationX"], [189, 34, 165, 30], [189, 38, 165, 34], [189, 39, 165, 35], [189, 41, 165, 37], [190, 14, 166, 10, "didScreenReachDestination"], [190, 39, 166, 35], [190, 40, 166, 36, "x"], [190, 41, 166, 37], [190, 44, 166, 40], [190, 48, 166, 44], [191, 14, 167, 10, "event"], [191, 19, 167, 15], [191, 20, 167, 16, "translationX"], [191, 32, 167, 28], [191, 35, 167, 31], [191, 36, 167, 32], [192, 12, 168, 8], [193, 10, 169, 6], [194, 10, 170, 6], [194, 14, 170, 10, "direction"], [194, 23, 170, 19], [194, 24, 170, 20, "y"], [194, 25, 170, 21], [194, 28, 170, 24], [194, 29, 170, 25], [194, 31, 170, 27], [195, 12, 171, 8], [195, 16, 171, 12, "event"], [195, 21, 171, 17], [195, 22, 171, 18, "translationY"], [195, 34, 171, 30], [195, 38, 171, 34], [195, 39, 171, 35], [195, 41, 171, 37], [196, 14, 172, 10, "didScreenReachDestination"], [196, 39, 172, 35], [196, 40, 172, 36, "y"], [196, 41, 172, 37], [196, 44, 172, 40], [196, 48, 172, 44], [197, 14, 173, 10, "event"], [197, 19, 173, 15], [197, 20, 173, 16, "translationY"], [197, 32, 173, 28], [197, 35, 173, 31], [197, 36, 173, 32], [198, 12, 174, 8], [199, 10, 175, 6], [199, 11, 175, 7], [199, 17, 175, 13], [200, 12, 176, 8], [200, 16, 176, 12, "event"], [200, 21, 176, 17], [200, 22, 176, 18, "translationY"], [200, 34, 176, 30], [200, 38, 176, 34], [200, 39, 176, 35], [200, 41, 176, 37], [201, 14, 177, 10, "didScreenReachDestination"], [201, 39, 177, 35], [201, 40, 177, 36, "y"], [201, 41, 177, 37], [201, 44, 177, 40], [201, 48, 177, 44], [202, 14, 178, 10, "event"], [202, 19, 178, 15], [202, 20, 178, 16, "translationY"], [202, 32, 178, 28], [202, 35, 178, 31], [202, 36, 178, 32], [203, 12, 179, 8], [204, 10, 180, 6], [205, 10, 181, 6], [205, 14, 181, 6, "applyStyle"], [205, 38, 181, 16], [205, 40, 181, 17, "screenTransitionConfig"], [205, 62, 181, 39], [205, 64, 181, 41, "event"], [205, 69, 181, 46], [205, 70, 181, 47], [206, 10, 182, 6], [206, 14, 182, 12, "finished"], [206, 22, 182, 20], [206, 25, 182, 23, "didScreenReachDestinationCheck"], [206, 55, 182, 53], [206, 56, 182, 54], [206, 57, 182, 55], [207, 10, 183, 6], [207, 14, 183, 10, "finished"], [207, 22, 183, 18], [207, 24, 183, 20], [208, 12, 184, 8, "restoreOriginalStyleForBelowTopScreen"], [208, 49, 184, 45], [208, 50, 184, 46], [208, 51, 184, 47], [209, 10, 185, 6], [210, 10, 186, 6, "maybeScheduleNextFrame"], [210, 32, 186, 28], [210, 33, 187, 8, "computeFrame"], [210, 45, 187, 20], [210, 47, 188, 8, "finished"], [210, 55, 188, 16], [210, 57, 189, 8, "screenTransitionConfig"], [210, 79, 189, 30], [210, 81, 190, 8, "event"], [210, 86, 190, 13], [210, 88, 191, 8, "isTransitionCanceled"], [210, 108, 192, 6], [210, 109, 192, 7], [211, 8, 193, 4], [211, 9, 193, 5], [212, 8, 194, 4], [212, 15, 194, 11, "computeFrame"], [212, 27, 194, 23], [213, 6, 195, 2], [213, 7, 195, 3], [213, 13, 195, 9], [214, 8, 196, 4], [214, 12, 196, 10, "computeFrame"], [214, 25, 196, 22], [214, 28, 196, 25, "computeFrame"], [214, 29, 196, 25], [214, 34, 196, 31], [215, 10, 197, 6], [215, 14, 197, 12, "progress"], [215, 22, 197, 20], [215, 25, 197, 23], [216, 12, 198, 8, "x"], [216, 13, 198, 9], [216, 15, 198, 11, "computeEasingProgress"], [216, 36, 198, 32], [216, 37, 198, 33, "startTimestamp"], [216, 51, 198, 47], [216, 53, 198, 49, "distance"], [216, 61, 198, 57], [216, 62, 198, 58, "x"], [216, 63, 198, 59], [216, 65, 198, 61, "velocity"], [216, 73, 198, 69], [216, 74, 198, 70, "x"], [216, 75, 198, 71], [216, 76, 198, 72], [217, 12, 199, 8, "y"], [217, 13, 199, 9], [217, 15, 199, 11, "computeEasingProgress"], [217, 36, 199, 32], [217, 37, 199, 33, "startTimestamp"], [217, 51, 199, 47], [217, 53, 199, 49, "distance"], [217, 61, 199, 57], [217, 62, 199, 58, "y"], [217, 63, 199, 59], [217, 65, 199, 61, "velocity"], [217, 73, 199, 69], [217, 74, 199, 70, "y"], [217, 75, 199, 71], [218, 10, 200, 6], [218, 11, 200, 7], [219, 10, 201, 6, "event"], [219, 15, 201, 11], [219, 16, 201, 12, "translationX"], [219, 28, 201, 24], [219, 31, 202, 8, "startingPosition"], [219, 47, 202, 24], [219, 48, 202, 25, "x"], [219, 49, 202, 26], [219, 52, 202, 29, "direction"], [219, 61, 202, 38], [219, 62, 202, 39, "x"], [219, 63, 202, 40], [219, 66, 202, 43, "distance"], [219, 74, 202, 51], [219, 75, 202, 52, "x"], [219, 76, 202, 53], [219, 79, 202, 56, "easing"], [219, 85, 202, 62], [219, 86, 202, 63, "progress"], [219, 94, 202, 71], [219, 95, 202, 72, "x"], [219, 96, 202, 73], [219, 97, 202, 74], [220, 10, 203, 6, "event"], [220, 15, 203, 11], [220, 16, 203, 12, "translationY"], [220, 28, 203, 24], [220, 31, 204, 8, "startingPosition"], [220, 47, 204, 24], [220, 48, 204, 25, "y"], [220, 49, 204, 26], [220, 52, 204, 29, "direction"], [220, 61, 204, 38], [220, 62, 204, 39, "y"], [220, 63, 204, 40], [220, 66, 204, 43, "distance"], [220, 74, 204, 51], [220, 75, 204, 52, "y"], [220, 76, 204, 53], [220, 79, 204, 56, "easing"], [220, 85, 204, 62], [220, 86, 204, 63, "progress"], [220, 94, 204, 71], [220, 95, 204, 72, "y"], [220, 96, 204, 73], [220, 97, 204, 74], [221, 10, 205, 6], [221, 14, 205, 10, "direction"], [221, 23, 205, 19], [221, 24, 205, 20, "x"], [221, 25, 205, 21], [221, 28, 205, 24], [221, 29, 205, 25], [221, 31, 205, 27], [222, 12, 206, 8], [222, 16, 206, 12, "event"], [222, 21, 206, 17], [222, 22, 206, 18, "translationX"], [222, 34, 206, 30], [222, 38, 206, 34, "screenDimensions"], [222, 54, 206, 50], [222, 55, 206, 51, "width"], [222, 60, 206, 56], [222, 62, 206, 58], [223, 14, 207, 10, "didScreenReachDestination"], [223, 39, 207, 35], [223, 40, 207, 36, "x"], [223, 41, 207, 37], [223, 44, 207, 40], [223, 48, 207, 44], [224, 14, 208, 10, "event"], [224, 19, 208, 15], [224, 20, 208, 16, "translationX"], [224, 32, 208, 28], [224, 35, 208, 31, "screenDimensions"], [224, 51, 208, 47], [224, 52, 208, 48, "width"], [224, 57, 208, 53], [225, 12, 209, 8], [226, 10, 210, 6], [226, 11, 210, 7], [226, 17, 210, 13], [227, 12, 211, 8], [227, 16, 211, 12, "event"], [227, 21, 211, 17], [227, 22, 211, 18, "translationX"], [227, 34, 211, 30], [227, 38, 211, 34], [227, 39, 211, 35, "screenDimensions"], [227, 55, 211, 51], [227, 56, 211, 52, "width"], [227, 61, 211, 57], [227, 63, 211, 59], [228, 14, 212, 10, "didScreenReachDestination"], [228, 39, 212, 35], [228, 40, 212, 36, "x"], [228, 41, 212, 37], [228, 44, 212, 40], [228, 48, 212, 44], [229, 14, 213, 10, "event"], [229, 19, 213, 15], [229, 20, 213, 16, "translationX"], [229, 32, 213, 28], [229, 35, 213, 31], [229, 36, 213, 32, "screenDimensions"], [229, 52, 213, 48], [229, 53, 213, 49, "width"], [229, 58, 213, 54], [230, 12, 214, 8], [231, 10, 215, 6], [232, 10, 216, 6], [232, 14, 216, 10, "direction"], [232, 23, 216, 19], [232, 24, 216, 20, "y"], [232, 25, 216, 21], [232, 28, 216, 24], [232, 29, 216, 25], [232, 31, 216, 27], [233, 12, 217, 8], [233, 16, 217, 12, "event"], [233, 21, 217, 17], [233, 22, 217, 18, "translationY"], [233, 34, 217, 30], [233, 38, 217, 34, "screenDimensions"], [233, 54, 217, 50], [233, 55, 217, 51, "height"], [233, 61, 217, 57], [233, 63, 217, 59], [234, 14, 218, 10, "didScreenReachDestination"], [234, 39, 218, 35], [234, 40, 218, 36, "y"], [234, 41, 218, 37], [234, 44, 218, 40], [234, 48, 218, 44], [235, 14, 219, 10, "event"], [235, 19, 219, 15], [235, 20, 219, 16, "translationY"], [235, 32, 219, 28], [235, 35, 219, 31, "screenDimensions"], [235, 51, 219, 47], [235, 52, 219, 48, "height"], [235, 58, 219, 54], [236, 12, 220, 8], [237, 10, 221, 6], [237, 11, 221, 7], [237, 17, 221, 13], [238, 12, 222, 8], [238, 16, 222, 12, "event"], [238, 21, 222, 17], [238, 22, 222, 18, "translationY"], [238, 34, 222, 30], [238, 38, 222, 34], [238, 39, 222, 35, "screenDimensions"], [238, 55, 222, 51], [238, 56, 222, 52, "height"], [238, 62, 222, 58], [238, 64, 222, 60], [239, 14, 223, 10, "didScreenReachDestination"], [239, 39, 223, 35], [239, 40, 223, 36, "y"], [239, 41, 223, 37], [239, 44, 223, 40], [239, 48, 223, 44], [240, 14, 224, 10, "event"], [240, 19, 224, 15], [240, 20, 224, 16, "translationY"], [240, 32, 224, 28], [240, 35, 224, 31], [240, 36, 224, 32, "screenDimensions"], [240, 52, 224, 48], [240, 53, 224, 49, "height"], [240, 59, 224, 55], [241, 12, 225, 8], [242, 10, 226, 6], [243, 10, 227, 6], [243, 14, 227, 6, "applyStyle"], [243, 38, 227, 16], [243, 40, 227, 17, "screenTransitionConfig"], [243, 62, 227, 39], [243, 64, 227, 41, "event"], [243, 69, 227, 46], [243, 70, 227, 47], [244, 10, 228, 6, "maybeScheduleNextFrame"], [244, 32, 228, 28], [244, 33, 229, 8, "computeFrame"], [244, 46, 229, 20], [244, 48, 230, 8, "didScreenReachDestination"], [244, 73, 230, 33], [244, 74, 230, 34, "x"], [244, 75, 230, 35], [244, 79, 230, 39, "didScreenReachDestination"], [244, 104, 230, 64], [244, 105, 230, 65, "y"], [244, 106, 230, 66], [244, 108, 231, 8, "screenTransitionConfig"], [244, 130, 231, 30], [244, 132, 232, 8, "event"], [244, 137, 232, 13], [244, 139, 233, 8, "isTransitionCanceled"], [244, 159, 234, 6], [244, 160, 234, 7], [245, 8, 235, 4], [245, 9, 235, 5], [246, 8, 236, 4], [246, 15, 236, 11, "computeFrame"], [246, 28, 236, 23], [247, 6, 237, 2], [248, 4, 238, 0], [248, 5, 238, 1], [249, 4, 238, 1, "getSwipeSimulator"], [249, 21, 238, 1], [249, 22, 238, 1, "__closure"], [249, 31, 238, 1], [250, 6, 238, 1, "BASE_VELOCITY"], [250, 19, 238, 1], [251, 6, 238, 1, "ADDITIONAL_VELOCITY_FACTOR_X"], [251, 34, 238, 1], [252, 6, 238, 1, "ADDITIONAL_VELOCITY_FACTOR_Y"], [252, 34, 238, 1], [253, 6, 238, 1, "ADDITIONAL_VELOCITY_FACTOR_XY"], [253, 35, 238, 1], [254, 6, 238, 1, "applyStyleForBelowTopScreen"], [254, 33, 238, 1], [254, 35, 147, 6, "applyStyleForBelowTopScreen"], [254, 76, 147, 33], [255, 6, 147, 33, "computeEasingProgress"], [255, 27, 147, 33], [256, 6, 147, 33, "easing"], [256, 12, 147, 33], [257, 6, 147, 33, "applyStyle"], [257, 16, 147, 33], [257, 18, 181, 6, "applyStyle"], [257, 42, 181, 16], [258, 6, 181, 16, "maybeScheduleNextFrame"], [259, 4, 181, 16], [260, 4, 181, 16, "getSwipeSimulator"], [260, 21, 181, 16], [260, 22, 181, 16, "__workletHash"], [260, 35, 181, 16], [261, 4, 181, 16, "getSwipeSimulator"], [261, 21, 181, 16], [261, 22, 181, 16, "__initData"], [261, 32, 181, 16], [261, 35, 181, 16, "_worklet_17409639634070_init_data"], [261, 68, 181, 16], [262, 4, 181, 16, "getSwipeSimulator"], [262, 21, 181, 16], [262, 22, 181, 16, "__stackDetails"], [262, 36, 181, 16], [262, 39, 181, 16, "_e"], [262, 41, 181, 16], [263, 4, 181, 16], [263, 11, 181, 16, "getSwipeSimulator"], [263, 28, 181, 16], [264, 2, 181, 16], [264, 3, 72, 7], [265, 0, 72, 7], [265, 3]], "functionMap": {"names": ["<global>", "computeEasingProgress", "easing", "computeProgress", "maybeScheduleNextFrame", "getSwipeSimulator", "didScreenReachDestinationCheck", "restoreOriginalStyleForBelowTopScreen", "computeFrame"], "mappings": "AAA;ACc;CDa;AEE;CFI;AGE;CHY;AIE;CJoB;OKE;IC8D;KDQ;IEE;KFI;yBGE;KH2C;yBGG;KHuC;CLG"}}, "type": "js/module"}]}