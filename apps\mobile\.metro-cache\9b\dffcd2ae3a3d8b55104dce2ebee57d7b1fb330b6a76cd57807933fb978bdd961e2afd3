{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "./ListMetricsAggregator", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 60}}], "key": "+LsZXRWRdDHi7URH4twavK8owRs=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 18}, "end": {"line": 17, "column": 38}}], "key": "oQpL0Es3H146KnQH9ygFeHrzVP4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _ListMetricsAggregator = _interopRequireDefault(require(_dependencyMap[4], \"./ListMetricsAggregator\"));\n  var invariant = require(_dependencyMap[5], \"invariant\");\n  var ViewabilityHelper = /*#__PURE__*/function () {\n    function ViewabilityHelper() {\n      var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n        viewAreaCoveragePercentThreshold: 0\n      };\n      (0, _classCallCheck2.default)(this, ViewabilityHelper);\n      this._hasInteracted = false;\n      this._timers = new Set();\n      this._viewableIndices = [];\n      this._viewableItems = new Map();\n      this._config = config;\n    }\n    return (0, _createClass2.default)(ViewabilityHelper, [{\n      key: \"dispose\",\n      value: function dispose() {\n        this._timers.forEach(clearTimeout);\n      }\n    }, {\n      key: \"computeViewableItems\",\n      value: function computeViewableItems(props, scrollOffset, viewportHeight, listMetrics, renderRange) {\n        var itemCount = props.getItemCount(props.data);\n        var _this$_config = this._config,\n          itemVisiblePercentThreshold = _this$_config.itemVisiblePercentThreshold,\n          viewAreaCoveragePercentThreshold = _this$_config.viewAreaCoveragePercentThreshold;\n        var viewAreaMode = viewAreaCoveragePercentThreshold != null;\n        var viewablePercentThreshold = viewAreaMode ? viewAreaCoveragePercentThreshold : itemVisiblePercentThreshold;\n        invariant(viewablePercentThreshold != null && itemVisiblePercentThreshold != null !== (viewAreaCoveragePercentThreshold != null), 'Must set exactly one of itemVisiblePercentThreshold or viewAreaCoveragePercentThreshold');\n        var viewableIndices = [];\n        if (itemCount === 0) {\n          return viewableIndices;\n        }\n        var firstVisible = -1;\n        var _ref = renderRange || {\n            first: 0,\n            last: itemCount - 1\n          },\n          first = _ref.first,\n          last = _ref.last;\n        if (last >= itemCount) {\n          console.warn('Invalid render range computing viewability ' + JSON.stringify({\n            renderRange,\n            itemCount\n          }));\n          return [];\n        }\n        for (var idx = first; idx <= last; idx++) {\n          var metrics = listMetrics.getCellMetrics(idx, props);\n          if (!metrics) {\n            continue;\n          }\n          var top = Math.floor(metrics.offset - scrollOffset);\n          var bottom = Math.floor(top + metrics.length);\n          if (top < viewportHeight && bottom > 0) {\n            firstVisible = idx;\n            if (_isViewable(viewAreaMode, viewablePercentThreshold, top, bottom, viewportHeight, metrics.length)) {\n              viewableIndices.push(idx);\n            }\n          } else if (firstVisible >= 0) {\n            break;\n          }\n        }\n        return viewableIndices;\n      }\n    }, {\n      key: \"onUpdate\",\n      value: function onUpdate(props, scrollOffset, viewportHeight, listMetrics, createViewToken, onViewableItemsChanged, renderRange) {\n        var itemCount = props.getItemCount(props.data);\n        if (this._config.waitForInteraction && !this._hasInteracted || itemCount === 0 || !listMetrics.getCellMetrics(0, props)) {\n          return;\n        }\n        var viewableIndices = [];\n        if (itemCount) {\n          viewableIndices = this.computeViewableItems(props, scrollOffset, viewportHeight, listMetrics, renderRange);\n        }\n        if (this._viewableIndices.length === viewableIndices.length && this._viewableIndices.every((v, ii) => v === viewableIndices[ii])) {\n          return;\n        }\n        this._viewableIndices = viewableIndices;\n        if (this._config.minimumViewTime) {\n          var handle = setTimeout(() => {\n            this._timers.delete(handle);\n            this._onUpdateSync(props, viewableIndices, onViewableItemsChanged, createViewToken);\n          }, this._config.minimumViewTime);\n          this._timers.add(handle);\n        } else {\n          this._onUpdateSync(props, viewableIndices, onViewableItemsChanged, createViewToken);\n        }\n      }\n    }, {\n      key: \"resetViewableIndices\",\n      value: function resetViewableIndices() {\n        this._viewableIndices = [];\n      }\n    }, {\n      key: \"recordInteraction\",\n      value: function recordInteraction() {\n        this._hasInteracted = true;\n      }\n    }, {\n      key: \"_onUpdateSync\",\n      value: function _onUpdateSync(props, viewableIndicesToCheck, onViewableItemsChanged, createViewToken) {\n        viewableIndicesToCheck = viewableIndicesToCheck.filter(ii => this._viewableIndices.includes(ii));\n        var prevItems = this._viewableItems;\n        var nextItems = new Map(viewableIndicesToCheck.map(ii => {\n          var viewable = createViewToken(ii, true, props);\n          return [viewable.key, viewable];\n        }));\n        var changed = [];\n        for (var _ref2 of nextItems) {\n          var _ref3 = (0, _slicedToArray2.default)(_ref2, 2);\n          var key = _ref3[0];\n          var viewable = _ref3[1];\n          if (!prevItems.has(key)) {\n            changed.push(viewable);\n          }\n        }\n        for (var _ref4 of prevItems) {\n          var _ref5 = (0, _slicedToArray2.default)(_ref4, 2);\n          var _key = _ref5[0];\n          var _viewable = _ref5[1];\n          if (!nextItems.has(_key)) {\n            changed.push({\n              ..._viewable,\n              isViewable: false\n            });\n          }\n        }\n        if (changed.length > 0) {\n          this._viewableItems = nextItems;\n          onViewableItemsChanged({\n            viewableItems: Array.from(nextItems.values()),\n            changed,\n            viewabilityConfig: this._config\n          });\n        }\n      }\n    }]);\n  }();\n  function _isViewable(viewAreaMode, viewablePercentThreshold, top, bottom, viewportHeight, itemLength) {\n    if (_isEntirelyVisible(top, bottom, viewportHeight)) {\n      return true;\n    } else {\n      var pixels = _getPixelsVisible(top, bottom, viewportHeight);\n      var percent = 100 * (viewAreaMode ? pixels / viewportHeight : pixels / itemLength);\n      return percent >= viewablePercentThreshold;\n    }\n  }\n  function _getPixelsVisible(top, bottom, viewportHeight) {\n    var visibleHeight = Math.min(bottom, viewportHeight) - Math.max(top, 0);\n    return Math.max(0, visibleHeight);\n  }\n  function _isEntirelyVisible(top, bottom, viewportHeight) {\n    return top >= 0 && bottom <= viewportHeight && bottom > top;\n  }\n  var _default = exports.default = ViewabilityHelper;\n});", "lineCount": 169, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_slicedToArray2"], [9, 21, 11, 13], [9, 24, 11, 13, "_interopRequireDefault"], [9, 46, 11, 13], [9, 47, 11, 13, "require"], [9, 54, 11, 13], [9, 55, 11, 13, "_dependencyMap"], [9, 69, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_classCallCheck2"], [10, 22, 11, 13], [10, 25, 11, 13, "_interopRequireDefault"], [10, 47, 11, 13], [10, 48, 11, 13, "require"], [10, 55, 11, 13], [10, 56, 11, 13, "_dependencyMap"], [10, 70, 11, 13], [11, 2, 11, 13], [11, 6, 11, 13, "_createClass2"], [11, 19, 11, 13], [11, 22, 11, 13, "_interopRequireDefault"], [11, 44, 11, 13], [11, 45, 11, 13, "require"], [11, 52, 11, 13], [11, 53, 11, 13, "_dependencyMap"], [11, 67, 11, 13], [12, 2, 15, 0], [12, 6, 15, 0, "_ListMetricsAggregator"], [12, 28, 15, 0], [12, 31, 15, 0, "_interopRequireDefault"], [12, 53, 15, 0], [12, 54, 15, 0, "require"], [12, 61, 15, 0], [12, 62, 15, 0, "_dependencyMap"], [12, 76, 15, 0], [13, 2, 17, 0], [13, 6, 17, 6, "invariant"], [13, 15, 17, 15], [13, 18, 17, 18, "require"], [13, 25, 17, 25], [13, 26, 17, 25, "_dependencyMap"], [13, 40, 17, 25], [13, 56, 17, 37], [13, 57, 17, 38], [14, 2, 17, 39], [14, 6, 82, 6, "ViewabilityHelper"], [14, 23, 82, 23], [15, 4, 89, 2], [15, 13, 89, 2, "ViewabilityHelper"], [15, 31, 89, 2], [15, 33, 91, 4], [16, 6, 91, 4], [16, 10, 90, 4, "config"], [16, 16, 90, 29], [16, 19, 90, 29, "arguments"], [16, 28, 90, 29], [16, 29, 90, 29, "length"], [16, 35, 90, 29], [16, 43, 90, 29, "arguments"], [16, 52, 90, 29], [16, 60, 90, 29, "undefined"], [16, 69, 90, 29], [16, 72, 90, 29, "arguments"], [16, 81, 90, 29], [16, 87, 90, 32], [17, 8, 90, 33, "viewAreaCoveragePercentThreshold"], [17, 40, 90, 65], [17, 42, 90, 67], [18, 6, 90, 68], [18, 7, 90, 69], [19, 6, 90, 69], [19, 10, 90, 69, "_classCallCheck2"], [19, 26, 90, 69], [19, 27, 90, 69, "default"], [19, 34, 90, 69], [19, 42, 90, 69, "ViewabilityHelper"], [19, 59, 90, 69], [20, 6, 90, 69], [20, 11, 84, 2, "_hasInteracted"], [20, 25, 84, 16], [20, 28, 84, 28], [20, 33, 84, 33], [21, 6, 84, 33], [21, 11, 85, 2, "_timers"], [21, 18, 85, 9], [21, 21, 85, 25], [21, 25, 85, 29, "Set"], [21, 28, 85, 32], [21, 29, 85, 33], [21, 30, 85, 34], [22, 6, 85, 34], [22, 11, 86, 2, "_viewableIndices"], [22, 27, 86, 18], [22, 30, 86, 36], [22, 32, 86, 38], [23, 6, 86, 38], [23, 11, 87, 2, "_viewableItems"], [23, 25, 87, 16], [23, 28, 87, 43], [23, 32, 87, 47, "Map"], [23, 35, 87, 50], [23, 36, 87, 51], [23, 37, 87, 52], [24, 6, 92, 4], [24, 10, 92, 8], [24, 11, 92, 9, "_config"], [24, 18, 92, 16], [24, 21, 92, 19, "config"], [24, 27, 92, 25], [25, 4, 93, 2], [26, 4, 93, 3], [26, 15, 93, 3, "_createClass2"], [26, 28, 93, 3], [26, 29, 93, 3, "default"], [26, 36, 93, 3], [26, 38, 93, 3, "ViewabilityHelper"], [26, 55, 93, 3], [27, 6, 93, 3, "key"], [27, 9, 93, 3], [28, 6, 93, 3, "value"], [28, 11, 93, 3], [28, 13, 98, 2], [28, 22, 98, 2, "dispose"], [28, 29, 98, 9, "dispose"], [28, 30, 98, 9], [28, 32, 98, 12], [29, 8, 102, 4], [29, 12, 102, 8], [29, 13, 102, 9, "_timers"], [29, 20, 102, 16], [29, 21, 102, 17, "for<PERSON>ach"], [29, 28, 102, 24], [29, 29, 102, 25, "clearTimeout"], [29, 41, 102, 37], [29, 42, 102, 38], [30, 6, 103, 2], [31, 4, 103, 3], [32, 6, 103, 3, "key"], [32, 9, 103, 3], [33, 6, 103, 3, "value"], [33, 11, 103, 3], [33, 13, 108, 2], [33, 22, 108, 2, "computeViewableItems"], [33, 42, 108, 22, "computeViewableItems"], [33, 43, 109, 4, "props"], [33, 48, 109, 26], [33, 50, 110, 4, "scrollOffset"], [33, 62, 110, 24], [33, 64, 111, 4, "viewportHeight"], [33, 78, 111, 26], [33, 80, 112, 4, "listMetrics"], [33, 91, 112, 38], [33, 93, 114, 4, "renderRange"], [33, 104, 118, 5], [33, 106, 119, 19], [34, 8, 120, 4], [34, 12, 120, 10, "itemCount"], [34, 21, 120, 19], [34, 24, 120, 22, "props"], [34, 29, 120, 27], [34, 30, 120, 28, "getItemCount"], [34, 42, 120, 40], [34, 43, 120, 41, "props"], [34, 48, 120, 46], [34, 49, 120, 47, "data"], [34, 53, 120, 51], [34, 54, 120, 52], [35, 8, 121, 4], [35, 12, 121, 4, "_this$_config"], [35, 25, 121, 4], [35, 28, 122, 6], [35, 32, 122, 10], [35, 33, 122, 11, "_config"], [35, 40, 122, 18], [36, 10, 121, 11, "itemVisiblePercentThreshold"], [36, 37, 121, 38], [36, 40, 121, 38, "_this$_config"], [36, 53, 121, 38], [36, 54, 121, 11, "itemVisiblePercentThreshold"], [36, 81, 121, 38], [37, 10, 121, 40, "viewAreaCoveragePercentThreshold"], [37, 42, 121, 72], [37, 45, 121, 72, "_this$_config"], [37, 58, 121, 72], [37, 59, 121, 40, "viewAreaCoveragePercentThreshold"], [37, 91, 121, 72], [38, 8, 123, 4], [38, 12, 123, 10, "viewAreaMode"], [38, 24, 123, 22], [38, 27, 123, 25, "viewAreaCoveragePercentThreshold"], [38, 59, 123, 57], [38, 63, 123, 61], [38, 67, 123, 65], [39, 8, 124, 4], [39, 12, 124, 10, "viewablePercentThreshold"], [39, 36, 124, 34], [39, 39, 124, 37, "viewAreaMode"], [39, 51, 124, 49], [39, 54, 125, 8, "viewAreaCoveragePercentThreshold"], [39, 86, 125, 40], [39, 89, 126, 8, "itemVisiblePercentThreshold"], [39, 116, 126, 35], [40, 8, 127, 4, "invariant"], [40, 17, 127, 13], [40, 18, 128, 6, "viewablePercentThreshold"], [40, 42, 128, 30], [40, 46, 128, 34], [40, 50, 128, 38], [40, 54, 129, 9, "itemVisiblePercentThreshold"], [40, 81, 129, 36], [40, 85, 129, 40], [40, 89, 129, 44], [40, 95, 130, 11, "viewAreaCoveragePercentThreshold"], [40, 127, 130, 43], [40, 131, 130, 47], [40, 135, 130, 51], [40, 136, 130, 52], [40, 138, 131, 6], [40, 227, 132, 4], [40, 228, 132, 5], [41, 8, 133, 4], [41, 12, 133, 10, "viewableIndices"], [41, 27, 133, 25], [41, 30, 133, 28], [41, 32, 133, 30], [42, 8, 134, 4], [42, 12, 134, 8, "itemCount"], [42, 21, 134, 17], [42, 26, 134, 22], [42, 27, 134, 23], [42, 29, 134, 25], [43, 10, 135, 6], [43, 17, 135, 13, "viewableIndices"], [43, 32, 135, 28], [44, 8, 136, 4], [45, 8, 137, 4], [45, 12, 137, 8, "firstVisible"], [45, 24, 137, 20], [45, 27, 137, 23], [45, 28, 137, 24], [45, 29, 137, 25], [46, 8, 138, 4], [46, 12, 138, 4, "_ref"], [46, 16, 138, 4], [46, 19, 138, 26, "renderRange"], [46, 30, 138, 37], [46, 34, 138, 41], [47, 12, 138, 42, "first"], [47, 17, 138, 47], [47, 19, 138, 49], [47, 20, 138, 50], [48, 12, 138, 52, "last"], [48, 16, 138, 56], [48, 18, 138, 58, "itemCount"], [48, 27, 138, 67], [48, 30, 138, 70], [49, 10, 138, 71], [49, 11, 138, 72], [50, 10, 138, 11, "first"], [50, 15, 138, 16], [50, 18, 138, 16, "_ref"], [50, 22, 138, 16], [50, 23, 138, 11, "first"], [50, 28, 138, 16], [51, 10, 138, 18, "last"], [51, 14, 138, 22], [51, 17, 138, 22, "_ref"], [51, 21, 138, 22], [51, 22, 138, 18, "last"], [51, 26, 138, 22], [52, 8, 139, 4], [52, 12, 139, 8, "last"], [52, 16, 139, 12], [52, 20, 139, 16, "itemCount"], [52, 29, 139, 25], [52, 31, 139, 27], [53, 10, 140, 6, "console"], [53, 17, 140, 13], [53, 18, 140, 14, "warn"], [53, 22, 140, 18], [53, 23, 141, 8], [53, 68, 141, 53], [53, 71, 142, 10, "JSON"], [53, 75, 142, 14], [53, 76, 142, 15, "stringify"], [53, 85, 142, 24], [53, 86, 142, 25], [54, 12, 142, 26, "renderRange"], [54, 23, 142, 37], [55, 12, 142, 39, "itemCount"], [56, 10, 142, 48], [56, 11, 142, 49], [56, 12, 143, 6], [56, 13, 143, 7], [57, 10, 144, 6], [57, 17, 144, 13], [57, 19, 144, 15], [58, 8, 145, 4], [59, 8, 146, 4], [59, 13, 146, 9], [59, 17, 146, 13, "idx"], [59, 20, 146, 16], [59, 23, 146, 19, "first"], [59, 28, 146, 24], [59, 30, 146, 26, "idx"], [59, 33, 146, 29], [59, 37, 146, 33, "last"], [59, 41, 146, 37], [59, 43, 146, 39, "idx"], [59, 46, 146, 42], [59, 48, 146, 44], [59, 50, 146, 46], [60, 10, 147, 6], [60, 14, 147, 12, "metrics"], [60, 21, 147, 19], [60, 24, 147, 22, "listMetrics"], [60, 35, 147, 33], [60, 36, 147, 34, "getCellMetrics"], [60, 50, 147, 48], [60, 51, 147, 49, "idx"], [60, 54, 147, 52], [60, 56, 147, 54, "props"], [60, 61, 147, 59], [60, 62, 147, 60], [61, 10, 148, 6], [61, 14, 148, 10], [61, 15, 148, 11, "metrics"], [61, 22, 148, 18], [61, 24, 148, 20], [62, 12, 149, 8], [63, 10, 150, 6], [64, 10, 151, 6], [64, 14, 151, 12, "top"], [64, 17, 151, 15], [64, 20, 151, 18, "Math"], [64, 24, 151, 22], [64, 25, 151, 23, "floor"], [64, 30, 151, 28], [64, 31, 151, 29, "metrics"], [64, 38, 151, 36], [64, 39, 151, 37, "offset"], [64, 45, 151, 43], [64, 48, 151, 46, "scrollOffset"], [64, 60, 151, 58], [64, 61, 151, 59], [65, 10, 152, 6], [65, 14, 152, 12, "bottom"], [65, 20, 152, 18], [65, 23, 152, 21, "Math"], [65, 27, 152, 25], [65, 28, 152, 26, "floor"], [65, 33, 152, 31], [65, 34, 152, 32, "top"], [65, 37, 152, 35], [65, 40, 152, 38, "metrics"], [65, 47, 152, 45], [65, 48, 152, 46, "length"], [65, 54, 152, 52], [65, 55, 152, 53], [66, 10, 154, 6], [66, 14, 154, 10, "top"], [66, 17, 154, 13], [66, 20, 154, 16, "viewportHeight"], [66, 34, 154, 30], [66, 38, 154, 34, "bottom"], [66, 44, 154, 40], [66, 47, 154, 43], [66, 48, 154, 44], [66, 50, 154, 46], [67, 12, 155, 8, "firstVisible"], [67, 24, 155, 20], [67, 27, 155, 23, "idx"], [67, 30, 155, 26], [68, 12, 156, 8], [68, 16, 157, 10, "_isViewable"], [68, 27, 157, 21], [68, 28, 158, 12, "viewAreaMode"], [68, 40, 158, 24], [68, 42, 159, 12, "viewablePercentThreshold"], [68, 66, 159, 36], [68, 68, 160, 12, "top"], [68, 71, 160, 15], [68, 73, 161, 12, "bottom"], [68, 79, 161, 18], [68, 81, 162, 12, "viewportHeight"], [68, 95, 162, 26], [68, 97, 163, 12, "metrics"], [68, 104, 163, 19], [68, 105, 163, 20, "length"], [68, 111, 164, 10], [68, 112, 164, 11], [68, 114, 165, 10], [69, 14, 166, 10, "viewableIndices"], [69, 29, 166, 25], [69, 30, 166, 26, "push"], [69, 34, 166, 30], [69, 35, 166, 31, "idx"], [69, 38, 166, 34], [69, 39, 166, 35], [70, 12, 167, 8], [71, 10, 168, 6], [71, 11, 168, 7], [71, 17, 168, 13], [71, 21, 168, 17, "firstVisible"], [71, 33, 168, 29], [71, 37, 168, 33], [71, 38, 168, 34], [71, 40, 168, 36], [72, 12, 169, 8], [73, 10, 170, 6], [74, 8, 171, 4], [75, 8, 172, 4], [75, 15, 172, 11, "viewableIndices"], [75, 30, 172, 26], [76, 6, 173, 2], [77, 4, 173, 3], [78, 6, 173, 3, "key"], [78, 9, 173, 3], [79, 6, 173, 3, "value"], [79, 11, 173, 3], [79, 13, 179, 2], [79, 22, 179, 2, "onUpdate"], [79, 30, 179, 10, "onUpdate"], [79, 31, 180, 4, "props"], [79, 36, 180, 26], [79, 38, 181, 4, "scrollOffset"], [79, 50, 181, 24], [79, 52, 182, 4, "viewportHeight"], [79, 66, 182, 26], [79, 68, 183, 4, "listMetrics"], [79, 79, 183, 38], [79, 81, 184, 4, "createViewToken"], [79, 96, 188, 18], [79, 98, 189, 4, "onViewableItemsChanged"], [79, 120, 193, 14], [79, 122, 195, 4, "renderRange"], [79, 133, 199, 5], [79, 135, 200, 10], [80, 8, 201, 4], [80, 12, 201, 10, "itemCount"], [80, 21, 201, 19], [80, 24, 201, 22, "props"], [80, 29, 201, 27], [80, 30, 201, 28, "getItemCount"], [80, 42, 201, 40], [80, 43, 201, 41, "props"], [80, 48, 201, 46], [80, 49, 201, 47, "data"], [80, 53, 201, 51], [80, 54, 201, 52], [81, 8, 202, 4], [81, 12, 203, 7], [81, 16, 203, 11], [81, 17, 203, 12, "_config"], [81, 24, 203, 19], [81, 25, 203, 20, "waitForInteraction"], [81, 43, 203, 38], [81, 47, 203, 42], [81, 48, 203, 43], [81, 52, 203, 47], [81, 53, 203, 48, "_hasInteracted"], [81, 67, 203, 62], [81, 71, 204, 6, "itemCount"], [81, 80, 204, 15], [81, 85, 204, 20], [81, 86, 204, 21], [81, 90, 205, 6], [81, 91, 205, 7, "listMetrics"], [81, 102, 205, 18], [81, 103, 205, 19, "getCellMetrics"], [81, 117, 205, 33], [81, 118, 205, 34], [81, 119, 205, 35], [81, 121, 205, 37, "props"], [81, 126, 205, 42], [81, 127, 205, 43], [81, 129, 206, 6], [82, 10, 207, 6], [83, 8, 208, 4], [84, 8, 209, 4], [84, 12, 209, 8, "viewableIndices"], [84, 27, 209, 38], [84, 30, 209, 41], [84, 32, 209, 43], [85, 8, 210, 4], [85, 12, 210, 8, "itemCount"], [85, 21, 210, 17], [85, 23, 210, 19], [86, 10, 211, 6, "viewableIndices"], [86, 25, 211, 21], [86, 28, 211, 24], [86, 32, 211, 28], [86, 33, 211, 29, "computeViewableItems"], [86, 53, 211, 49], [86, 54, 212, 8, "props"], [86, 59, 212, 13], [86, 61, 213, 8, "scrollOffset"], [86, 73, 213, 20], [86, 75, 214, 8, "viewportHeight"], [86, 89, 214, 22], [86, 91, 215, 8, "listMetrics"], [86, 102, 215, 19], [86, 104, 216, 8, "renderRange"], [86, 115, 217, 6], [86, 116, 217, 7], [87, 8, 218, 4], [88, 8, 219, 4], [88, 12, 220, 6], [88, 16, 220, 10], [88, 17, 220, 11, "_viewableIndices"], [88, 33, 220, 27], [88, 34, 220, 28, "length"], [88, 40, 220, 34], [88, 45, 220, 39, "viewableIndices"], [88, 60, 220, 54], [88, 61, 220, 55, "length"], [88, 67, 220, 61], [88, 71, 221, 6], [88, 75, 221, 10], [88, 76, 221, 11, "_viewableIndices"], [88, 92, 221, 27], [88, 93, 221, 28, "every"], [88, 98, 221, 33], [88, 99, 221, 34], [88, 100, 221, 35, "v"], [88, 101, 221, 36], [88, 103, 221, 38, "ii"], [88, 105, 221, 40], [88, 110, 221, 45, "v"], [88, 111, 221, 46], [88, 116, 221, 51, "viewableIndices"], [88, 131, 221, 66], [88, 132, 221, 67, "ii"], [88, 134, 221, 69], [88, 135, 221, 70], [88, 136, 221, 71], [88, 138, 222, 6], [89, 10, 225, 6], [90, 8, 226, 4], [91, 8, 227, 4], [91, 12, 227, 8], [91, 13, 227, 9, "_viewableIndices"], [91, 29, 227, 25], [91, 32, 227, 28, "viewableIndices"], [91, 47, 227, 43], [92, 8, 228, 4], [92, 12, 228, 8], [92, 16, 228, 12], [92, 17, 228, 13, "_config"], [92, 24, 228, 20], [92, 25, 228, 21, "minimumViewTime"], [92, 40, 228, 36], [92, 42, 228, 38], [93, 10, 229, 6], [93, 14, 229, 12, "handle"], [93, 20, 229, 29], [93, 23, 229, 32, "setTimeout"], [93, 33, 229, 42], [93, 34, 229, 43], [93, 40, 229, 49], [94, 12, 233, 8], [94, 16, 233, 12], [94, 17, 233, 13, "_timers"], [94, 24, 233, 20], [94, 25, 233, 21, "delete"], [94, 31, 233, 27], [94, 32, 233, 28, "handle"], [94, 38, 233, 34], [94, 39, 233, 35], [95, 12, 234, 8], [95, 16, 234, 12], [95, 17, 234, 13, "_onUpdateSync"], [95, 30, 234, 26], [95, 31, 235, 10, "props"], [95, 36, 235, 15], [95, 38, 236, 10, "viewableIndices"], [95, 53, 236, 25], [95, 55, 237, 10, "onViewableItemsChanged"], [95, 77, 237, 32], [95, 79, 238, 10, "createViewToken"], [95, 94, 239, 8], [95, 95, 239, 9], [96, 10, 240, 6], [96, 11, 240, 7], [96, 13, 240, 9], [96, 17, 240, 13], [96, 18, 240, 14, "_config"], [96, 25, 240, 21], [96, 26, 240, 22, "minimumViewTime"], [96, 41, 240, 37], [96, 42, 240, 38], [97, 10, 244, 6], [97, 14, 244, 10], [97, 15, 244, 11, "_timers"], [97, 22, 244, 18], [97, 23, 244, 19, "add"], [97, 26, 244, 22], [97, 27, 244, 23, "handle"], [97, 33, 244, 29], [97, 34, 244, 30], [98, 8, 245, 4], [98, 9, 245, 5], [98, 15, 245, 11], [99, 10, 246, 6], [99, 14, 246, 10], [99, 15, 246, 11, "_onUpdateSync"], [99, 28, 246, 24], [99, 29, 247, 8, "props"], [99, 34, 247, 13], [99, 36, 248, 8, "viewableIndices"], [99, 51, 248, 23], [99, 53, 249, 8, "onViewableItemsChanged"], [99, 75, 249, 30], [99, 77, 250, 8, "createViewToken"], [99, 92, 251, 6], [99, 93, 251, 7], [100, 8, 252, 4], [101, 6, 253, 2], [102, 4, 253, 3], [103, 6, 253, 3, "key"], [103, 9, 253, 3], [104, 6, 253, 3, "value"], [104, 11, 253, 3], [104, 13, 258, 2], [104, 22, 258, 2, "resetViewableIndices"], [104, 42, 258, 22, "resetViewableIndices"], [104, 43, 258, 22], [104, 45, 258, 25], [105, 8, 259, 4], [105, 12, 259, 8], [105, 13, 259, 9, "_viewableIndices"], [105, 29, 259, 25], [105, 32, 259, 28], [105, 34, 259, 30], [106, 6, 260, 2], [107, 4, 260, 3], [108, 6, 260, 3, "key"], [108, 9, 260, 3], [109, 6, 260, 3, "value"], [109, 11, 260, 3], [109, 13, 265, 2], [109, 22, 265, 2, "recordInteraction"], [109, 39, 265, 19, "recordInteraction"], [109, 40, 265, 19], [109, 42, 265, 22], [110, 8, 266, 4], [110, 12, 266, 8], [110, 13, 266, 9, "_hasInteracted"], [110, 27, 266, 23], [110, 30, 266, 26], [110, 34, 266, 30], [111, 6, 267, 2], [112, 4, 267, 3], [113, 6, 267, 3, "key"], [113, 9, 267, 3], [114, 6, 267, 3, "value"], [114, 11, 267, 3], [114, 13, 269, 2], [114, 22, 269, 2, "_onUpdateSync"], [114, 35, 269, 15, "_onUpdateSync"], [114, 36, 270, 4, "props"], [114, 41, 270, 26], [114, 43, 271, 4, "viewableIndicesToCheck"], [114, 65, 271, 41], [114, 67, 272, 4, "onViewableItemsChanged"], [114, 89, 276, 14], [114, 91, 277, 4, "createViewToken"], [114, 106, 281, 18], [114, 108, 282, 4], [115, 8, 284, 4, "viewableIndicesToCheck"], [115, 30, 284, 26], [115, 33, 284, 29, "viewableIndicesToCheck"], [115, 55, 284, 51], [115, 56, 284, 52, "filter"], [115, 62, 284, 58], [115, 63, 284, 59, "ii"], [115, 65, 284, 61], [115, 69, 285, 6], [115, 73, 285, 10], [115, 74, 285, 11, "_viewableIndices"], [115, 90, 285, 27], [115, 91, 285, 28, "includes"], [115, 99, 285, 36], [115, 100, 285, 37, "ii"], [115, 102, 285, 39], [115, 103, 286, 4], [115, 104, 286, 5], [116, 8, 287, 4], [116, 12, 287, 10, "prevItems"], [116, 21, 287, 19], [116, 24, 287, 22], [116, 28, 287, 26], [116, 29, 287, 27, "_viewableItems"], [116, 43, 287, 41], [117, 8, 288, 4], [117, 12, 288, 10, "nextItems"], [117, 21, 288, 19], [117, 24, 288, 22], [117, 28, 288, 26, "Map"], [117, 31, 288, 29], [117, 32, 289, 6, "viewableIndicesToCheck"], [117, 54, 289, 28], [117, 55, 289, 29, "map"], [117, 58, 289, 32], [117, 59, 289, 33, "ii"], [117, 61, 289, 35], [117, 65, 289, 39], [118, 10, 290, 8], [118, 14, 290, 14, "viewable"], [118, 22, 290, 22], [118, 25, 290, 25, "createViewToken"], [118, 40, 290, 40], [118, 41, 290, 41, "ii"], [118, 43, 290, 43], [118, 45, 290, 45], [118, 49, 290, 49], [118, 51, 290, 51, "props"], [118, 56, 290, 56], [118, 57, 290, 57], [119, 10, 291, 8], [119, 17, 291, 15], [119, 18, 291, 16, "viewable"], [119, 26, 291, 24], [119, 27, 291, 25, "key"], [119, 30, 291, 28], [119, 32, 291, 30, "viewable"], [119, 40, 291, 38], [119, 41, 291, 39], [120, 8, 292, 6], [120, 9, 292, 7], [120, 10, 293, 4], [120, 11, 293, 5], [121, 8, 295, 4], [121, 12, 295, 10, "changed"], [121, 19, 295, 17], [121, 22, 295, 20], [121, 24, 295, 22], [122, 8, 296, 4], [122, 17, 296, 4, "_ref2"], [122, 22, 296, 4], [122, 26, 296, 34, "nextItems"], [122, 35, 296, 43], [122, 37, 296, 45], [123, 10, 296, 45], [123, 14, 296, 45, "_ref3"], [123, 19, 296, 45], [123, 26, 296, 45, "_slicedToArray2"], [123, 41, 296, 45], [123, 42, 296, 45, "default"], [123, 49, 296, 45], [123, 51, 296, 45, "_ref2"], [123, 56, 296, 45], [124, 10, 296, 45], [124, 14, 296, 16, "key"], [124, 17, 296, 19], [124, 20, 296, 19, "_ref3"], [124, 25, 296, 19], [125, 10, 296, 19], [125, 14, 296, 21, "viewable"], [125, 22, 296, 29], [125, 25, 296, 29, "_ref3"], [125, 30, 296, 29], [126, 10, 297, 6], [126, 14, 297, 10], [126, 15, 297, 11, "prevItems"], [126, 24, 297, 20], [126, 25, 297, 21, "has"], [126, 28, 297, 24], [126, 29, 297, 25, "key"], [126, 32, 297, 28], [126, 33, 297, 29], [126, 35, 297, 31], [127, 12, 298, 8, "changed"], [127, 19, 298, 15], [127, 20, 298, 16, "push"], [127, 24, 298, 20], [127, 25, 298, 21, "viewable"], [127, 33, 298, 29], [127, 34, 298, 30], [128, 10, 299, 6], [129, 8, 300, 4], [130, 8, 301, 4], [130, 17, 301, 4, "_ref4"], [130, 22, 301, 4], [130, 26, 301, 34, "prevItems"], [130, 35, 301, 43], [130, 37, 301, 45], [131, 10, 301, 45], [131, 14, 301, 45, "_ref5"], [131, 19, 301, 45], [131, 26, 301, 45, "_slicedToArray2"], [131, 41, 301, 45], [131, 42, 301, 45, "default"], [131, 49, 301, 45], [131, 51, 301, 45, "_ref4"], [131, 56, 301, 45], [132, 10, 301, 45], [132, 14, 301, 16, "key"], [132, 18, 301, 19], [132, 21, 301, 19, "_ref5"], [132, 26, 301, 19], [133, 10, 301, 19], [133, 14, 301, 21, "viewable"], [133, 23, 301, 29], [133, 26, 301, 29, "_ref5"], [133, 31, 301, 29], [134, 10, 302, 6], [134, 14, 302, 10], [134, 15, 302, 11, "nextItems"], [134, 24, 302, 20], [134, 25, 302, 21, "has"], [134, 28, 302, 24], [134, 29, 302, 25, "key"], [134, 33, 302, 28], [134, 34, 302, 29], [134, 36, 302, 31], [135, 12, 303, 8, "changed"], [135, 19, 303, 15], [135, 20, 303, 16, "push"], [135, 24, 303, 20], [135, 25, 303, 21], [136, 14, 303, 22], [136, 17, 303, 25, "viewable"], [136, 26, 303, 33], [137, 14, 303, 35, "isViewable"], [137, 24, 303, 45], [137, 26, 303, 47], [138, 12, 303, 52], [138, 13, 303, 53], [138, 14, 303, 54], [139, 10, 304, 6], [140, 8, 305, 4], [141, 8, 306, 4], [141, 12, 306, 8, "changed"], [141, 19, 306, 15], [141, 20, 306, 16, "length"], [141, 26, 306, 22], [141, 29, 306, 25], [141, 30, 306, 26], [141, 32, 306, 28], [142, 10, 307, 6], [142, 14, 307, 10], [142, 15, 307, 11, "_viewableItems"], [142, 29, 307, 25], [142, 32, 307, 28, "nextItems"], [142, 41, 307, 37], [143, 10, 308, 6, "onViewableItemsChanged"], [143, 32, 308, 28], [143, 33, 308, 29], [144, 12, 309, 8, "viewableItems"], [144, 25, 309, 21], [144, 27, 309, 23, "Array"], [144, 32, 309, 28], [144, 33, 309, 29, "from"], [144, 37, 309, 33], [144, 38, 309, 34, "nextItems"], [144, 47, 309, 43], [144, 48, 309, 44, "values"], [144, 54, 309, 50], [144, 55, 309, 51], [144, 56, 309, 52], [144, 57, 309, 53], [145, 12, 310, 8, "changed"], [145, 19, 310, 15], [146, 12, 311, 8, "viewabilityConfig"], [146, 29, 311, 25], [146, 31, 311, 27], [146, 35, 311, 31], [146, 36, 311, 32, "_config"], [147, 10, 312, 6], [147, 11, 312, 7], [147, 12, 312, 8], [148, 8, 313, 4], [149, 6, 314, 2], [150, 4, 314, 3], [151, 2, 314, 3], [152, 2, 317, 0], [152, 11, 317, 9, "_isViewable"], [152, 22, 317, 20, "_isViewable"], [152, 23, 318, 2, "viewAreaMode"], [152, 35, 318, 23], [152, 37, 319, 2, "viewablePercentThreshold"], [152, 61, 319, 34], [152, 63, 320, 2, "top"], [152, 66, 320, 13], [152, 68, 321, 2, "bottom"], [152, 74, 321, 16], [152, 76, 322, 2, "viewportHeight"], [152, 90, 322, 24], [152, 92, 323, 2, "itemLength"], [152, 102, 323, 20], [152, 104, 324, 11], [153, 4, 325, 2], [153, 8, 325, 6, "_isEntirelyVisible"], [153, 26, 325, 24], [153, 27, 325, 25, "top"], [153, 30, 325, 28], [153, 32, 325, 30, "bottom"], [153, 38, 325, 36], [153, 40, 325, 38, "viewportHeight"], [153, 54, 325, 52], [153, 55, 325, 53], [153, 57, 325, 55], [154, 6, 326, 4], [154, 13, 326, 11], [154, 17, 326, 15], [155, 4, 327, 2], [155, 5, 327, 3], [155, 11, 327, 9], [156, 6, 328, 4], [156, 10, 328, 10, "pixels"], [156, 16, 328, 16], [156, 19, 328, 19, "_getPixelsVisible"], [156, 36, 328, 36], [156, 37, 328, 37, "top"], [156, 40, 328, 40], [156, 42, 328, 42, "bottom"], [156, 48, 328, 48], [156, 50, 328, 50, "viewportHeight"], [156, 64, 328, 64], [156, 65, 328, 65], [157, 6, 329, 4], [157, 10, 329, 10, "percent"], [157, 17, 329, 17], [157, 20, 330, 6], [157, 23, 330, 9], [157, 27, 330, 13, "viewAreaMode"], [157, 39, 330, 25], [157, 42, 330, 28, "pixels"], [157, 48, 330, 34], [157, 51, 330, 37, "viewportHeight"], [157, 65, 330, 51], [157, 68, 330, 54, "pixels"], [157, 74, 330, 60], [157, 77, 330, 63, "itemLength"], [157, 87, 330, 73], [157, 88, 330, 74], [158, 6, 331, 4], [158, 13, 331, 11, "percent"], [158, 20, 331, 18], [158, 24, 331, 22, "viewablePercentThreshold"], [158, 48, 331, 46], [159, 4, 332, 2], [160, 2, 333, 0], [161, 2, 335, 0], [161, 11, 335, 9, "_getPixelsVisible"], [161, 28, 335, 26, "_getPixelsVisible"], [161, 29, 336, 2, "top"], [161, 32, 336, 13], [161, 34, 337, 2, "bottom"], [161, 40, 337, 16], [161, 42, 338, 2, "viewportHeight"], [161, 56, 338, 24], [161, 58, 339, 10], [162, 4, 340, 2], [162, 8, 340, 8, "visibleHeight"], [162, 21, 340, 21], [162, 24, 340, 24, "Math"], [162, 28, 340, 28], [162, 29, 340, 29, "min"], [162, 32, 340, 32], [162, 33, 340, 33, "bottom"], [162, 39, 340, 39], [162, 41, 340, 41, "viewportHeight"], [162, 55, 340, 55], [162, 56, 340, 56], [162, 59, 340, 59, "Math"], [162, 63, 340, 63], [162, 64, 340, 64, "max"], [162, 67, 340, 67], [162, 68, 340, 68, "top"], [162, 71, 340, 71], [162, 73, 340, 73], [162, 74, 340, 74], [162, 75, 340, 75], [163, 4, 341, 2], [163, 11, 341, 9, "Math"], [163, 15, 341, 13], [163, 16, 341, 14, "max"], [163, 19, 341, 17], [163, 20, 341, 18], [163, 21, 341, 19], [163, 23, 341, 21, "visibleHeight"], [163, 36, 341, 34], [163, 37, 341, 35], [164, 2, 342, 0], [165, 2, 344, 0], [165, 11, 344, 9, "_isEntirelyVisible"], [165, 29, 344, 27, "_isEntirelyVisible"], [165, 30, 345, 2, "top"], [165, 33, 345, 13], [165, 35, 346, 2, "bottom"], [165, 41, 346, 16], [165, 43, 347, 2, "viewportHeight"], [165, 57, 347, 24], [165, 59, 348, 11], [166, 4, 349, 2], [166, 11, 349, 9, "top"], [166, 14, 349, 12], [166, 18, 349, 16], [166, 19, 349, 17], [166, 23, 349, 21, "bottom"], [166, 29, 349, 27], [166, 33, 349, 31, "viewportHeight"], [166, 47, 349, 45], [166, 51, 349, 49, "bottom"], [166, 57, 349, 55], [166, 60, 349, 58, "top"], [166, 63, 349, 61], [167, 2, 350, 0], [168, 2, 350, 1], [168, 6, 350, 1, "_default"], [168, 14, 350, 1], [168, 17, 350, 1, "exports"], [168, 24, 350, 1], [168, 25, 350, 1, "default"], [168, 32, 350, 1], [168, 35, 352, 15, "ViewabilityHelper"], [168, 52, 352, 32], [169, 0, 352, 32], [169, 3]], "functionMap": {"names": ["<global>", "ViewabilityHelper", "constructor", "dispose", "computeViewableItems", "onUpdate", "_viewableIndices.every$argument_0", "setTimeout$argument_0", "resetViewableIndices", "recordInteraction", "_onUpdateSync", "viewableIndicesToCheck.filter$argument_0", "viewableIndicesToCheck.map$argument_0", "_isViewable", "_getPixelsVisible", "_isEntirelyVisible"], "mappings": "AAA;ACiF;ECO;GDI;EEK;GFK;EGK;GHiE;EIM;kCC0C,oCD;2CEQ;OFW;GJa;EOK;GPE;EQK;GRE;ESE;2DCe;wCDC;iCEI;OFG;GTsB;CDC;AaE;CbgB;AcE;CdO;AeE;CfM"}}, "type": "js/module"}]}