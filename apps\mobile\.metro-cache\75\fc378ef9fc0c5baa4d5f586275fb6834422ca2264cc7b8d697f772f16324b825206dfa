{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.throttle = throttle;\n  function throttle(func, duration) {\n    var timeout;\n    return function () {\n      if (timeout == null) {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        func.apply(this, args);\n        timeout = setTimeout(() => {\n          timeout = undefined;\n        }, duration);\n      }\n    };\n  }\n});", "lineCount": 22, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "throttle"], [7, 18, 1, 13], [7, 21, 1, 13, "throttle"], [7, 29, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "throttle"], [8, 19, 3, 24, "throttle"], [8, 20, 3, 25, "func"], [8, 24, 3, 29], [8, 26, 3, 31, "duration"], [8, 34, 3, 39], [8, 36, 3, 41], [9, 4, 4, 2], [9, 8, 4, 6, "timeout"], [9, 15, 4, 13], [10, 4, 5, 2], [10, 11, 5, 9], [10, 23, 5, 28], [11, 6, 6, 4], [11, 10, 6, 8, "timeout"], [11, 17, 6, 15], [11, 21, 6, 19], [11, 25, 6, 23], [11, 27, 6, 25], [12, 8, 6, 25], [12, 17, 6, 25, "_len"], [12, 21, 6, 25], [12, 24, 6, 25, "arguments"], [12, 33, 6, 25], [12, 34, 6, 25, "length"], [12, 40, 6, 25], [12, 42, 5, 22, "args"], [12, 46, 5, 26], [12, 53, 5, 26, "Array"], [12, 58, 5, 26], [12, 59, 5, 26, "_len"], [12, 63, 5, 26], [12, 66, 5, 26, "_key"], [12, 70, 5, 26], [12, 76, 5, 26, "_key"], [12, 80, 5, 26], [12, 83, 5, 26, "_len"], [12, 87, 5, 26], [12, 89, 5, 26, "_key"], [12, 93, 5, 26], [13, 10, 5, 22, "args"], [13, 14, 5, 26], [13, 15, 5, 26, "_key"], [13, 19, 5, 26], [13, 23, 5, 26, "arguments"], [13, 32, 5, 26], [13, 33, 5, 26, "_key"], [13, 37, 5, 26], [14, 8, 5, 26], [15, 8, 7, 6, "func"], [15, 12, 7, 10], [15, 13, 7, 11, "apply"], [15, 18, 7, 16], [15, 19, 7, 17], [15, 23, 7, 21], [15, 25, 7, 23, "args"], [15, 29, 7, 27], [15, 30, 7, 28], [16, 8, 8, 6, "timeout"], [16, 15, 8, 13], [16, 18, 8, 16, "setTimeout"], [16, 28, 8, 26], [16, 29, 8, 27], [16, 35, 8, 33], [17, 10, 9, 8, "timeout"], [17, 17, 9, 15], [17, 20, 9, 18, "undefined"], [17, 29, 9, 27], [18, 8, 10, 6], [18, 9, 10, 7], [18, 11, 10, 9, "duration"], [18, 19, 10, 17], [18, 20, 10, 18], [19, 6, 11, 4], [20, 4, 12, 2], [20, 5, 12, 3], [21, 2, 13, 0], [22, 0, 13, 1], [22, 3]], "functionMap": {"names": ["<global>", "throttle", "<anonymous>", "setTimeout$argument_0"], "mappings": "AAA;OCE;SCE;2BCG;ODE;GDE;CDC"}}, "type": "js/module"}]}