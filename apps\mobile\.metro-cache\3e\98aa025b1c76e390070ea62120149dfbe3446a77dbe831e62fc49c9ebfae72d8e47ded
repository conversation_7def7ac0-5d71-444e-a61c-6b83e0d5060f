{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.checkValidArgs = checkValidArgs;\n  exports.checkValidInput = checkValidInput;\n  exports.convertError = convertError;\n  exports.convertErrors = convertErrors;\n  function checkValidArgs(keyValuePairs, callback) {\n    if (!Array.isArray(keyValuePairs) || keyValuePairs.length === 0 || !Array.isArray(keyValuePairs[0])) {\n      throw new Error(\"[AsyncStorage] Expected array of key-value pairs as first argument to multiSet\");\n    }\n    if (callback && typeof callback !== \"function\") {\n      if (Array.isArray(callback)) {\n        throw new Error(\"[AsyncStorage] Expected function as second argument to multiSet. Did you forget to wrap key-value pairs in an array for the first argument?\");\n      }\n      throw new Error(\"[AsyncStorage] Expected function as second argument to multiSet\");\n    }\n  }\n  function checkValidInput() {\n    for (var _len = arguments.length, input = new Array(_len), _key = 0; _key < _len; _key++) {\n      input[_key] = arguments[_key];\n    }\n    var key = input[0],\n      value = input[1];\n    if (typeof key !== \"string\") {\n      // eslint-disable-next-line no-console\n      console.warn(`[AsyncStorage] Using ${typeof key} type for key is not supported. This can lead to unexpected behavior/errors. Use string instead.\\nKey passed: ${key}\\n`);\n    }\n    if (input.length > 1 && typeof value !== \"string\") {\n      if (value == null) {\n        throw new Error(`[AsyncStorage] Passing null/undefined as value is not supported. If you want to remove value, Use .removeItem method instead.\\nPassed value: ${value}\\nPassed key: ${key}\\n`);\n      } else {\n        // eslint-disable-next-line no-console\n        console.warn(`[AsyncStorage] The value for key \"${key}\" is not a string. This can lead to unexpected behavior/errors. Consider stringifying it.\\nPassed value: ${value}\\nPassed key: ${key}\\n`);\n      }\n    }\n  }\n  function convertError(error) {\n    if (!error) {\n      return null;\n    }\n    var out = new Error(error.message);\n    out[\"key\"] = error.key;\n    return out;\n  }\n  function convertErrors(errs) {\n    var errors = ensureArray(errs);\n    return errors ? errors.map(e => convertError(e)) : null;\n  }\n  function ensureArray(e) {\n    if (Array.isArray(e)) {\n      return e.length === 0 ? null : e;\n    } else if (e) {\n      return [e];\n    } else {\n      return null;\n    }\n  }\n});", "lineCount": 60, "map": [[9, 2, 3, 7], [9, 11, 3, 16, "checkValidArgs"], [9, 25, 3, 30, "checkValidArgs"], [9, 26, 4, 2, "keyValuePairs"], [9, 39, 4, 35], [9, 41, 5, 2, "callback"], [9, 49, 5, 19], [9, 51, 6, 2], [10, 4, 7, 2], [10, 8, 8, 4], [10, 9, 8, 5, "Array"], [10, 14, 8, 10], [10, 15, 8, 11, "isArray"], [10, 22, 8, 18], [10, 23, 8, 19, "keyValuePairs"], [10, 36, 8, 32], [10, 37, 8, 33], [10, 41, 9, 4, "keyValuePairs"], [10, 54, 9, 17], [10, 55, 9, 18, "length"], [10, 61, 9, 24], [10, 66, 9, 29], [10, 67, 9, 30], [10, 71, 10, 4], [10, 72, 10, 5, "Array"], [10, 77, 10, 10], [10, 78, 10, 11, "isArray"], [10, 85, 10, 18], [10, 86, 10, 19, "keyValuePairs"], [10, 99, 10, 32], [10, 100, 10, 33], [10, 101, 10, 34], [10, 102, 10, 35], [10, 103, 10, 36], [10, 105, 11, 4], [11, 6, 12, 4], [11, 12, 12, 10], [11, 16, 12, 14, "Error"], [11, 21, 12, 19], [11, 22, 13, 6], [11, 102, 14, 4], [11, 103, 14, 5], [12, 4, 15, 2], [13, 4, 17, 2], [13, 8, 17, 6, "callback"], [13, 16, 17, 14], [13, 20, 17, 18], [13, 27, 17, 25, "callback"], [13, 35, 17, 33], [13, 40, 17, 38], [13, 50, 17, 48], [13, 52, 17, 50], [14, 6, 18, 4], [14, 10, 18, 8, "Array"], [14, 15, 18, 13], [14, 16, 18, 14, "isArray"], [14, 23, 18, 21], [14, 24, 18, 22, "callback"], [14, 32, 18, 30], [14, 33, 18, 31], [14, 35, 18, 33], [15, 8, 19, 6], [15, 14, 19, 12], [15, 18, 19, 16, "Error"], [15, 23, 19, 21], [15, 24, 20, 8], [15, 165, 21, 6], [15, 166, 21, 7], [16, 6, 22, 4], [17, 6, 24, 4], [17, 12, 24, 10], [17, 16, 24, 14, "Error"], [17, 21, 24, 19], [17, 22, 25, 6], [17, 87, 26, 4], [17, 88, 26, 5], [18, 4, 27, 2], [19, 2, 28, 0], [20, 2, 30, 7], [20, 11, 30, 16, "checkValidInput"], [20, 26, 30, 31, "checkValidInput"], [20, 27, 30, 31], [20, 29, 30, 53], [21, 4, 30, 53], [21, 13, 30, 53, "_len"], [21, 17, 30, 53], [21, 20, 30, 53, "arguments"], [21, 29, 30, 53], [21, 30, 30, 53, "length"], [21, 36, 30, 53], [21, 38, 30, 35, "input"], [21, 43, 30, 40], [21, 50, 30, 40, "Array"], [21, 55, 30, 40], [21, 56, 30, 40, "_len"], [21, 60, 30, 40], [21, 63, 30, 40, "_key"], [21, 67, 30, 40], [21, 73, 30, 40, "_key"], [21, 77, 30, 40], [21, 80, 30, 40, "_len"], [21, 84, 30, 40], [21, 86, 30, 40, "_key"], [21, 90, 30, 40], [22, 6, 30, 35, "input"], [22, 11, 30, 40], [22, 12, 30, 40, "_key"], [22, 16, 30, 40], [22, 20, 30, 40, "arguments"], [22, 29, 30, 40], [22, 30, 30, 40, "_key"], [22, 34, 30, 40], [23, 4, 30, 40], [24, 4, 31, 2], [24, 8, 31, 9, "key"], [24, 11, 31, 12], [24, 14, 31, 23, "input"], [24, 19, 31, 28], [25, 6, 31, 14, "value"], [25, 11, 31, 19], [25, 14, 31, 23, "input"], [25, 19, 31, 28], [26, 4, 33, 2], [26, 8, 33, 6], [26, 15, 33, 13, "key"], [26, 18, 33, 16], [26, 23, 33, 21], [26, 31, 33, 29], [26, 33, 33, 31], [27, 6, 34, 4], [28, 6, 35, 4, "console"], [28, 13, 35, 11], [28, 14, 35, 12, "warn"], [28, 18, 35, 16], [28, 19, 36, 6], [28, 43, 36, 30], [28, 50, 36, 37, "key"], [28, 53, 36, 40], [28, 166, 36, 153, "key"], [28, 169, 36, 156], [28, 173, 37, 4], [28, 174, 37, 5], [29, 4, 38, 2], [30, 4, 40, 2], [30, 8, 40, 6, "input"], [30, 13, 40, 11], [30, 14, 40, 12, "length"], [30, 20, 40, 18], [30, 23, 40, 21], [30, 24, 40, 22], [30, 28, 40, 26], [30, 35, 40, 33, "value"], [30, 40, 40, 38], [30, 45, 40, 43], [30, 53, 40, 51], [30, 55, 40, 53], [31, 6, 41, 4], [31, 10, 41, 8, "value"], [31, 15, 41, 13], [31, 19, 41, 17], [31, 23, 41, 21], [31, 25, 41, 23], [32, 8, 42, 6], [32, 14, 42, 12], [32, 18, 42, 16, "Error"], [32, 23, 42, 21], [32, 24, 43, 8], [32, 168, 43, 152, "value"], [32, 173, 43, 157], [32, 190, 43, 174, "key"], [32, 193, 43, 177], [32, 197, 44, 6], [32, 198, 44, 7], [33, 6, 45, 4], [33, 7, 45, 5], [33, 13, 45, 11], [34, 8, 46, 6], [35, 8, 47, 6, "console"], [35, 15, 47, 13], [35, 16, 47, 14, "warn"], [35, 20, 47, 18], [35, 21, 48, 8], [35, 58, 48, 45, "key"], [35, 61, 48, 48], [35, 169, 48, 156, "value"], [35, 174, 48, 161], [35, 191, 48, 178, "key"], [35, 194, 48, 181], [35, 198, 49, 6], [35, 199, 49, 7], [36, 6, 50, 4], [37, 4, 51, 2], [38, 2, 52, 0], [39, 2, 54, 7], [39, 11, 54, 16, "convertError"], [39, 23, 54, 28, "convertError"], [39, 24, 54, 29, "error"], [39, 29, 54, 46], [39, 31, 54, 62], [40, 4, 55, 2], [40, 8, 55, 6], [40, 9, 55, 7, "error"], [40, 14, 55, 12], [40, 16, 55, 14], [41, 6, 56, 4], [41, 13, 56, 11], [41, 17, 56, 15], [42, 4, 57, 2], [43, 4, 59, 2], [43, 8, 59, 8, "out"], [43, 11, 59, 11], [43, 14, 59, 14], [43, 18, 59, 18, "Error"], [43, 23, 59, 23], [43, 24, 59, 24, "error"], [43, 29, 59, 29], [43, 30, 59, 30, "message"], [43, 37, 59, 37], [43, 38, 59, 59], [44, 4, 60, 2, "out"], [44, 7, 60, 5], [44, 8, 60, 6], [44, 13, 60, 11], [44, 14, 60, 12], [44, 17, 60, 15, "error"], [44, 22, 60, 20], [44, 23, 60, 21, "key"], [44, 26, 60, 24], [45, 4, 61, 2], [45, 11, 61, 9, "out"], [45, 14, 61, 12], [46, 2, 62, 0], [47, 2, 64, 7], [47, 11, 64, 16, "convertErrors"], [47, 24, 64, 29, "convertErrors"], [47, 25, 65, 2, "errs"], [47, 29, 65, 20], [47, 31, 66, 38], [48, 4, 67, 2], [48, 8, 67, 8, "errors"], [48, 14, 67, 14], [48, 17, 67, 17, "ensureArray"], [48, 28, 67, 28], [48, 29, 67, 29, "errs"], [48, 33, 67, 33], [48, 34, 67, 34], [49, 4, 68, 2], [49, 11, 68, 9, "errors"], [49, 17, 68, 15], [49, 20, 68, 18, "errors"], [49, 26, 68, 24], [49, 27, 68, 25, "map"], [49, 30, 68, 28], [49, 31, 68, 30, "e"], [49, 32, 68, 31], [49, 36, 68, 36, "convertError"], [49, 48, 68, 48], [49, 49, 68, 49, "e"], [49, 50, 68, 50], [49, 51, 68, 51], [49, 52, 68, 52], [49, 55, 68, 55], [49, 59, 68, 59], [50, 2, 69, 0], [51, 2, 71, 0], [51, 11, 71, 9, "ensureArray"], [51, 22, 71, 20, "ensureArray"], [51, 23, 71, 21, "e"], [51, 24, 71, 48], [51, 26, 71, 70], [52, 4, 72, 2], [52, 8, 72, 6, "Array"], [52, 13, 72, 11], [52, 14, 72, 12, "isArray"], [52, 21, 72, 19], [52, 22, 72, 20, "e"], [52, 23, 72, 21], [52, 24, 72, 22], [52, 26, 72, 24], [53, 6, 73, 4], [53, 13, 73, 11, "e"], [53, 14, 73, 12], [53, 15, 73, 13, "length"], [53, 21, 73, 19], [53, 26, 73, 24], [53, 27, 73, 25], [53, 30, 73, 28], [53, 34, 73, 32], [53, 37, 73, 35, "e"], [53, 38, 73, 36], [54, 4, 74, 2], [54, 5, 74, 3], [54, 11, 74, 9], [54, 15, 74, 13, "e"], [54, 16, 74, 14], [54, 18, 74, 16], [55, 6, 75, 4], [55, 13, 75, 11], [55, 14, 75, 12, "e"], [55, 15, 75, 13], [55, 16, 75, 14], [56, 4, 76, 2], [56, 5, 76, 3], [56, 11, 76, 9], [57, 6, 77, 4], [57, 13, 77, 11], [57, 17, 77, 15], [58, 4, 78, 2], [59, 2, 79, 0], [60, 0, 79, 1], [60, 3]], "functionMap": {"names": ["<global>", "checkValidArgs", "checkValidInput", "convertError", "convertErrors", "errors.map$argument_0", "ensureArray"], "mappings": "AAA;OCE;CDyB;OEE;CFsB;OGE;CHQ;OIE;6BCI,sBD;CJC;AME;CNQ"}}, "type": "js/module"}]}