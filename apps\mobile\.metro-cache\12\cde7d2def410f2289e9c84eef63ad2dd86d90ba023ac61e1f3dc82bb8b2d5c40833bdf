{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.State = void 0;\n  // TODO use State from RNModule\n\n  var State = exports.State = {\n    UNDETERMINED: 0,\n    FAILED: 1,\n    BEGAN: 2,\n    CANCELLED: 3,\n    ACTIVE: 4,\n    END: 5\n  };\n\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value\n});", "lineCount": 18, "map": [[6, 2, 1, 0], [8, 2, 3, 7], [8, 6, 3, 13, "State"], [8, 11, 3, 18], [8, 14, 3, 18, "exports"], [8, 21, 3, 18], [8, 22, 3, 18, "State"], [8, 27, 3, 18], [8, 30, 3, 21], [9, 4, 4, 2, "UNDETERMINED"], [9, 16, 4, 14], [9, 18, 4, 16], [9, 19, 4, 17], [10, 4, 5, 2, "FAILED"], [10, 10, 5, 8], [10, 12, 5, 10], [10, 13, 5, 11], [11, 4, 6, 2, "BEGAN"], [11, 9, 6, 7], [11, 11, 6, 9], [11, 12, 6, 10], [12, 4, 7, 2, "CANCELLED"], [12, 13, 7, 11], [12, 15, 7, 13], [12, 16, 7, 14], [13, 4, 8, 2, "ACTIVE"], [13, 10, 8, 8], [13, 12, 8, 10], [13, 13, 8, 11], [14, 4, 9, 2, "END"], [14, 7, 9, 5], [14, 9, 9, 7], [15, 2, 10, 0], [15, 3, 10, 10], [17, 2, 12, 0], [18, 0, 12, 0], [18, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}