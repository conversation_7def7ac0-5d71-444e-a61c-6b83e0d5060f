{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 116}, "end": {"line": 6, "column": 31, "index": 147}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 148}, "end": {"line": 7, "column": 41, "index": 189}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 190}, "end": {"line": 8, "column": 48, "index": 238}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}, {"name": "@react-native-masked-view/masked-view", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 18, "index": 421}, "end": {"line": 13, "column": 66, "index": 469}}], "key": "8Lz54uug/ECPLjtwTjriHO06xaI=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  /**\n   * The native MaskedView that we explicitly re-export for supported platforms: Android, iOS.\n   */\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.MaskedView = MaskedView;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _jsxRuntime = require(_dependencyMap[4], \"react/jsx-runtime\");\n  var _excluded = [\"children\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var RNCMaskedView;\n  try {\n    // Add try/catch to support usage even if it's not installed, since it's optional.\n    // Newer versions of Metro will handle it properly.\n    RNCMaskedView = require(_dependencyMap[5], \"@react-native-masked-view/masked-view\").default;\n  } catch (e) {\n    // Ignore\n  }\n  var isMaskedViewAvailable = _reactNative.UIManager.getViewManagerConfig('RNCMaskedView') != null;\n  function MaskedView(_ref) {\n    var children = _ref.children,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    if (isMaskedViewAvailable && RNCMaskedView) {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(RNCMaskedView, {\n        ...rest,\n        children: children\n      });\n    }\n    return children;\n  }\n});", "lineCount": 38, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 2, 3, 0], [7, 6, 3, 0, "_interopRequireDefault"], [7, 28, 3, 0], [7, 31, 3, 0, "require"], [7, 38, 3, 0], [7, 39, 3, 0, "_dependencyMap"], [7, 53, 3, 0], [8, 2, 3, 0, "Object"], [8, 8, 3, 0], [8, 9, 3, 0, "defineProperty"], [8, 23, 3, 0], [8, 24, 3, 0, "exports"], [8, 31, 3, 0], [9, 4, 3, 0, "value"], [9, 9, 3, 0], [10, 2, 3, 0], [11, 2, 3, 0, "exports"], [11, 9, 3, 0], [11, 10, 3, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 20, 3, 0], [11, 23, 3, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 33, 3, 0], [12, 2, 3, 0], [12, 6, 3, 0, "_objectWithoutProperties2"], [12, 31, 3, 0], [12, 34, 3, 0, "_interopRequireDefault"], [12, 56, 3, 0], [12, 57, 3, 0, "require"], [12, 64, 3, 0], [12, 65, 3, 0, "_dependencyMap"], [12, 79, 3, 0], [13, 2, 6, 0], [13, 6, 6, 0, "React"], [13, 11, 6, 0], [13, 14, 6, 0, "_interopRequireWildcard"], [13, 37, 6, 0], [13, 38, 6, 0, "require"], [13, 45, 6, 0], [13, 46, 6, 0, "_dependencyMap"], [13, 60, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_reactNative"], [14, 18, 7, 0], [14, 21, 7, 0, "require"], [14, 28, 7, 0], [14, 29, 7, 0, "_dependencyMap"], [14, 43, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_jsxRuntime"], [15, 17, 8, 0], [15, 20, 8, 0, "require"], [15, 27, 8, 0], [15, 28, 8, 0, "_dependencyMap"], [15, 42, 8, 0], [16, 2, 8, 48], [16, 6, 8, 48, "_excluded"], [16, 15, 8, 48], [17, 2, 8, 48], [17, 11, 8, 48, "_interopRequireWildcard"], [17, 35, 8, 48, "e"], [17, 36, 8, 48], [17, 38, 8, 48, "t"], [17, 39, 8, 48], [17, 68, 8, 48, "WeakMap"], [17, 75, 8, 48], [17, 81, 8, 48, "r"], [17, 82, 8, 48], [17, 89, 8, 48, "WeakMap"], [17, 96, 8, 48], [17, 100, 8, 48, "n"], [17, 101, 8, 48], [17, 108, 8, 48, "WeakMap"], [17, 115, 8, 48], [17, 127, 8, 48, "_interopRequireWildcard"], [17, 150, 8, 48], [17, 162, 8, 48, "_interopRequireWildcard"], [17, 163, 8, 48, "e"], [17, 164, 8, 48], [17, 166, 8, 48, "t"], [17, 167, 8, 48], [17, 176, 8, 48, "t"], [17, 177, 8, 48], [17, 181, 8, 48, "e"], [17, 182, 8, 48], [17, 186, 8, 48, "e"], [17, 187, 8, 48], [17, 188, 8, 48, "__esModule"], [17, 198, 8, 48], [17, 207, 8, 48, "e"], [17, 208, 8, 48], [17, 214, 8, 48, "o"], [17, 215, 8, 48], [17, 217, 8, 48, "i"], [17, 218, 8, 48], [17, 220, 8, 48, "f"], [17, 221, 8, 48], [17, 226, 8, 48, "__proto__"], [17, 235, 8, 48], [17, 243, 8, 48, "default"], [17, 250, 8, 48], [17, 252, 8, 48, "e"], [17, 253, 8, 48], [17, 270, 8, 48, "e"], [17, 271, 8, 48], [17, 294, 8, 48, "e"], [17, 295, 8, 48], [17, 320, 8, 48, "e"], [17, 321, 8, 48], [17, 330, 8, 48, "f"], [17, 331, 8, 48], [17, 337, 8, 48, "o"], [17, 338, 8, 48], [17, 341, 8, 48, "t"], [17, 342, 8, 48], [17, 345, 8, 48, "n"], [17, 346, 8, 48], [17, 349, 8, 48, "r"], [17, 350, 8, 48], [17, 358, 8, 48, "o"], [17, 359, 8, 48], [17, 360, 8, 48, "has"], [17, 363, 8, 48], [17, 364, 8, 48, "e"], [17, 365, 8, 48], [17, 375, 8, 48, "o"], [17, 376, 8, 48], [17, 377, 8, 48, "get"], [17, 380, 8, 48], [17, 381, 8, 48, "e"], [17, 382, 8, 48], [17, 385, 8, 48, "o"], [17, 386, 8, 48], [17, 387, 8, 48, "set"], [17, 390, 8, 48], [17, 391, 8, 48, "e"], [17, 392, 8, 48], [17, 394, 8, 48, "f"], [17, 395, 8, 48], [17, 409, 8, 48, "_t"], [17, 411, 8, 48], [17, 415, 8, 48, "e"], [17, 416, 8, 48], [17, 432, 8, 48, "_t"], [17, 434, 8, 48], [17, 441, 8, 48, "hasOwnProperty"], [17, 455, 8, 48], [17, 456, 8, 48, "call"], [17, 460, 8, 48], [17, 461, 8, 48, "e"], [17, 462, 8, 48], [17, 464, 8, 48, "_t"], [17, 466, 8, 48], [17, 473, 8, 48, "i"], [17, 474, 8, 48], [17, 478, 8, 48, "o"], [17, 479, 8, 48], [17, 482, 8, 48, "Object"], [17, 488, 8, 48], [17, 489, 8, 48, "defineProperty"], [17, 503, 8, 48], [17, 508, 8, 48, "Object"], [17, 514, 8, 48], [17, 515, 8, 48, "getOwnPropertyDescriptor"], [17, 539, 8, 48], [17, 540, 8, 48, "e"], [17, 541, 8, 48], [17, 543, 8, 48, "_t"], [17, 545, 8, 48], [17, 552, 8, 48, "i"], [17, 553, 8, 48], [17, 554, 8, 48, "get"], [17, 557, 8, 48], [17, 561, 8, 48, "i"], [17, 562, 8, 48], [17, 563, 8, 48, "set"], [17, 566, 8, 48], [17, 570, 8, 48, "o"], [17, 571, 8, 48], [17, 572, 8, 48, "f"], [17, 573, 8, 48], [17, 575, 8, 48, "_t"], [17, 577, 8, 48], [17, 579, 8, 48, "i"], [17, 580, 8, 48], [17, 584, 8, 48, "f"], [17, 585, 8, 48], [17, 586, 8, 48, "_t"], [17, 588, 8, 48], [17, 592, 8, 48, "e"], [17, 593, 8, 48], [17, 594, 8, 48, "_t"], [17, 596, 8, 48], [17, 607, 8, 48, "f"], [17, 608, 8, 48], [17, 613, 8, 48, "e"], [17, 614, 8, 48], [17, 616, 8, 48, "t"], [17, 617, 8, 48], [18, 2, 9, 0], [18, 6, 9, 4, "RNCMaskedView"], [18, 19, 9, 17], [19, 2, 10, 0], [19, 6, 10, 4], [20, 4, 11, 2], [21, 4, 12, 2], [22, 4, 13, 2, "RNCMaskedView"], [22, 17, 13, 15], [22, 20, 13, 18, "require"], [22, 27, 13, 25], [22, 28, 13, 25, "_dependencyMap"], [22, 42, 13, 25], [22, 86, 13, 65], [22, 87, 13, 66], [22, 88, 13, 67, "default"], [22, 95, 13, 74], [23, 2, 14, 0], [23, 3, 14, 1], [23, 4, 14, 2], [23, 11, 14, 9, "e"], [23, 12, 14, 10], [23, 14, 14, 12], [24, 4, 15, 2], [25, 2, 15, 2], [26, 2, 17, 0], [26, 6, 17, 6, "isMaskedViewAvailable"], [26, 27, 17, 27], [26, 30, 17, 30, "UIManager"], [26, 52, 17, 39], [26, 53, 17, 40, "getViewManagerConfig"], [26, 73, 17, 60], [26, 74, 17, 61], [26, 89, 17, 76], [26, 90, 17, 77], [26, 94, 17, 81], [26, 98, 17, 85], [27, 2, 18, 7], [27, 11, 18, 16, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [27, 21, 18, 26, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [27, 22, 18, 26, "_ref"], [27, 26, 18, 26], [27, 28, 21, 3], [28, 4, 21, 3], [28, 8, 19, 2, "children"], [28, 16, 19, 10], [28, 19, 19, 10, "_ref"], [28, 23, 19, 10], [28, 24, 19, 2, "children"], [28, 32, 19, 10], [29, 6, 20, 5, "rest"], [29, 10, 20, 9], [29, 17, 20, 9, "_objectWithoutProperties2"], [29, 42, 20, 9], [29, 43, 20, 9, "default"], [29, 50, 20, 9], [29, 52, 20, 9, "_ref"], [29, 56, 20, 9], [29, 58, 20, 9, "_excluded"], [29, 67, 20, 9], [30, 4, 22, 2], [30, 8, 22, 6, "isMaskedViewAvailable"], [30, 29, 22, 27], [30, 33, 22, 31, "RNCMaskedView"], [30, 46, 22, 44], [30, 48, 22, 46], [31, 6, 23, 4], [31, 13, 23, 11], [31, 26, 23, 24], [31, 30, 23, 24, "_jsx"], [31, 45, 23, 28], [31, 47, 23, 29, "RNCMaskedView"], [31, 60, 23, 42], [31, 62, 23, 44], [32, 8, 24, 6], [32, 11, 24, 9, "rest"], [32, 15, 24, 13], [33, 8, 25, 6, "children"], [33, 16, 25, 14], [33, 18, 25, 16, "children"], [34, 6, 26, 4], [34, 7, 26, 5], [34, 8, 26, 6], [35, 4, 27, 2], [36, 4, 28, 2], [36, 11, 28, 9, "children"], [36, 19, 28, 17], [37, 2, 29, 0], [38, 0, 29, 1], [38, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;OCiB;CDW"}}, "type": "js/module"}]}