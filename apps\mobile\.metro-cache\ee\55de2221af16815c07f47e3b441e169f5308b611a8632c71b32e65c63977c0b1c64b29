{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./NativeErrorManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "2G8HPQl48FQ6VudbkRbhXaaKPvk=", "exportNames": ["*"]}}, {"name": "../Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 55}, "end": {"line": 2, "column": 35, "index": 90}}], "key": "SkcN7Zi2IL0pUxWZCaWeI65icek=", "exportNames": ["*"]}}, {"name": "../errors/CodedError", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 91}, "end": {"line": 3, "column": 50, "index": 141}}], "key": "BOW/VZxuvFh2KA4VCC9eipjaigw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  var _NativeErrorManager = _interopRequireDefault(require(_dependencyMap[1], \"./NativeErrorManager\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[2], \"../Platform\"));\n  var _CodedError = require(_dependencyMap[3], \"../errors/CodedError\");\n  if (__DEV__ && _Platform.default.OS === 'android' && _NativeErrorManager.default) {\n    var onNewException = 'ExpoModulesCoreErrorManager.onNewException';\n    var onNewWarning = 'ExpoModulesCoreErrorManager.onNewWarning';\n    _NativeErrorManager.default.addListener(onNewException, _ref => {\n      var message = _ref.message;\n      console.error(message);\n    });\n    _NativeErrorManager.default.addListener(onNewWarning, _ref2 => {\n      var message = _ref2.message;\n      console.warn(message);\n    });\n  }\n  // We have to export `CodedError` via global object to use in later in the C++ code.\n  globalThis.ExpoModulesCore_CodedError = _CodedError.CodedError;\n});", "lineCount": 20, "map": [[3, 2, 1, 0], [3, 6, 1, 0, "_NativeErrorManager"], [3, 25, 1, 0], [3, 28, 1, 0, "_interopRequireDefault"], [3, 50, 1, 0], [3, 51, 1, 0, "require"], [3, 58, 1, 0], [3, 59, 1, 0, "_dependencyMap"], [3, 73, 1, 0], [4, 2, 2, 0], [4, 6, 2, 0, "_Platform"], [4, 15, 2, 0], [4, 18, 2, 0, "_interopRequireDefault"], [4, 40, 2, 0], [4, 41, 2, 0, "require"], [4, 48, 2, 0], [4, 49, 2, 0, "_dependencyMap"], [4, 63, 2, 0], [5, 2, 3, 0], [5, 6, 3, 0, "_CodedError"], [5, 17, 3, 0], [5, 20, 3, 0, "require"], [5, 27, 3, 0], [5, 28, 3, 0, "_dependencyMap"], [5, 42, 3, 0], [6, 2, 5, 0], [6, 6, 5, 4, "__DEV__"], [6, 13, 5, 11], [6, 17, 5, 15, "Platform"], [6, 34, 5, 23], [6, 35, 5, 24, "OS"], [6, 37, 5, 26], [6, 42, 5, 31], [6, 51, 5, 40], [6, 55, 5, 44, "NativeErrorManager"], [6, 82, 5, 62], [6, 84, 5, 64], [7, 4, 6, 2], [7, 8, 6, 8, "onNewException"], [7, 22, 6, 22], [7, 25, 6, 25], [7, 69, 6, 69], [8, 4, 7, 2], [8, 8, 7, 8, "onNewWarning"], [8, 20, 7, 20], [8, 23, 7, 23], [8, 65, 7, 65], [9, 4, 9, 2, "NativeErrorManager"], [9, 31, 9, 20], [9, 32, 9, 21, "addListener"], [9, 43, 9, 32], [9, 44, 9, 33, "onNewException"], [9, 58, 9, 47], [9, 60, 9, 49, "_ref"], [9, 64, 9, 49], [9, 68, 9, 87], [10, 6, 9, 87], [10, 10, 9, 52, "message"], [10, 17, 9, 59], [10, 20, 9, 59, "_ref"], [10, 24, 9, 59], [10, 25, 9, 52, "message"], [10, 32, 9, 59], [11, 6, 10, 4, "console"], [11, 13, 10, 11], [11, 14, 10, 12, "error"], [11, 19, 10, 17], [11, 20, 10, 18, "message"], [11, 27, 10, 25], [11, 28, 10, 26], [12, 4, 11, 2], [12, 5, 11, 3], [12, 6, 11, 4], [13, 4, 13, 2, "NativeErrorManager"], [13, 31, 13, 20], [13, 32, 13, 21, "addListener"], [13, 43, 13, 32], [13, 44, 13, 33, "onNewWarning"], [13, 56, 13, 45], [13, 58, 13, 47, "_ref2"], [13, 63, 13, 47], [13, 67, 13, 85], [14, 6, 13, 85], [14, 10, 13, 50, "message"], [14, 17, 13, 57], [14, 20, 13, 57, "_ref2"], [14, 25, 13, 57], [14, 26, 13, 50, "message"], [14, 33, 13, 57], [15, 6, 14, 4, "console"], [15, 13, 14, 11], [15, 14, 14, 12, "warn"], [15, 18, 14, 16], [15, 19, 14, 17, "message"], [15, 26, 14, 24], [15, 27, 14, 25], [16, 4, 15, 2], [16, 5, 15, 3], [16, 6, 15, 4], [17, 2, 16, 0], [18, 2, 22, 0], [19, 2, 23, 0, "globalThis"], [19, 12, 23, 10], [19, 13, 23, 11, "ExpoModulesCore_CodedError"], [19, 39, 23, 37], [19, 42, 23, 40, "CodedError"], [19, 64, 23, 50], [20, 0, 23, 51], [20, 3]], "functionMap": {"names": ["<global>", "NativeErrorManager.addListener$argument_1"], "mappings": "AAA;iDCQ;GDE;+CCE;GDE"}}, "type": "js/module"}]}