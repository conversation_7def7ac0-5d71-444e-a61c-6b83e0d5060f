{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./NativeFileReaderModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 62}}], "key": "2WcRsp61CFp1Im1NOrocXkatkmY=", "exportNames": ["*"]}}, {"name": "base64-js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 38}}], "key": "9arPc0KuVPvzcEfvnWXidnN1Ujk=", "exportNames": ["*"]}}, {"name": "event-target-shim", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 44}}], "key": "NbjKHRYGUQGwCXA5fondJGZijfU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _NativeFileReaderModule = _interopRequireDefault(require(_dependencyMap[6], \"./NativeFileReaderModule\"));\n  var _base64Js = require(_dependencyMap[7], \"base64-js\");\n  var _eventTargetShim = _interopRequireDefault(require(_dependencyMap[8], \"event-target-shim\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var READER_EVENTS = ['abort', 'error', 'load', 'loadstart', 'loadend', 'progress'];\n  var EMPTY = 0;\n  var LOADING = 1;\n  var DONE = 2;\n  var FileReader = /*#__PURE__*/function (_ref) {\n    function FileReader() {\n      var _this;\n      (0, _classCallCheck2.default)(this, FileReader);\n      _this = _callSuper(this, FileReader);\n      _this.EMPTY = EMPTY;\n      _this.LOADING = LOADING;\n      _this.DONE = DONE;\n      _this._aborted = false;\n      _this._reset();\n      return _this;\n    }\n    (0, _inherits2.default)(FileReader, _ref);\n    return (0, _createClass2.default)(FileReader, [{\n      key: \"_reset\",\n      value: function _reset() {\n        this._readyState = EMPTY;\n        this._error = null;\n        this._result = null;\n      }\n    }, {\n      key: \"_setReadyState\",\n      value: function _setReadyState(newState) {\n        this._readyState = newState;\n        this.dispatchEvent({\n          type: 'readystatechange'\n        });\n        if (newState === DONE) {\n          if (this._aborted) {\n            this.dispatchEvent({\n              type: 'abort'\n            });\n          } else if (this._error) {\n            this.dispatchEvent({\n              type: 'error'\n            });\n          } else {\n            this.dispatchEvent({\n              type: 'load'\n            });\n          }\n          this.dispatchEvent({\n            type: 'loadend'\n          });\n        }\n      }\n    }, {\n      key: \"readAsArrayBuffer\",\n      value: function readAsArrayBuffer(blob) {\n        this._aborted = false;\n        if (blob == null) {\n          throw new TypeError(\"Failed to execute 'readAsArrayBuffer' on 'FileReader': parameter 1 is not of type 'Blob'\");\n        }\n        _NativeFileReaderModule.default.readAsDataURL(blob.data).then(text => {\n          if (this._aborted) {\n            return;\n          }\n          var base64 = text.split(',')[1];\n          var typedArray = (0, _base64Js.toByteArray)(base64);\n          this._result = typedArray.buffer;\n          this._setReadyState(DONE);\n        }, error => {\n          if (this._aborted) {\n            return;\n          }\n          this._error = error;\n          this._setReadyState(DONE);\n        });\n      }\n    }, {\n      key: \"readAsDataURL\",\n      value: function readAsDataURL(blob) {\n        this._aborted = false;\n        if (blob == null) {\n          throw new TypeError(\"Failed to execute 'readAsDataURL' on 'FileReader': parameter 1 is not of type 'Blob'\");\n        }\n        _NativeFileReaderModule.default.readAsDataURL(blob.data).then(text => {\n          if (this._aborted) {\n            return;\n          }\n          this._result = text;\n          this._setReadyState(DONE);\n        }, error => {\n          if (this._aborted) {\n            return;\n          }\n          this._error = error;\n          this._setReadyState(DONE);\n        });\n      }\n    }, {\n      key: \"readAsText\",\n      value: function readAsText(blob) {\n        var encoding = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'UTF-8';\n        this._aborted = false;\n        if (blob == null) {\n          throw new TypeError(\"Failed to execute 'readAsText' on 'FileReader': parameter 1 is not of type 'Blob'\");\n        }\n        _NativeFileReaderModule.default.readAsText(blob.data, encoding).then(text => {\n          if (this._aborted) {\n            return;\n          }\n          this._result = text;\n          this._setReadyState(DONE);\n        }, error => {\n          if (this._aborted) {\n            return;\n          }\n          this._error = error;\n          this._setReadyState(DONE);\n        });\n      }\n    }, {\n      key: \"abort\",\n      value: function abort() {\n        this._aborted = true;\n        if (this._readyState !== EMPTY && this._readyState !== DONE) {\n          this._reset();\n          this._setReadyState(DONE);\n        }\n        this._reset();\n      }\n    }, {\n      key: \"readyState\",\n      get: function () {\n        return this._readyState;\n      }\n    }, {\n      key: \"error\",\n      get: function () {\n        return this._error;\n      }\n    }, {\n      key: \"result\",\n      get: function () {\n        return this._result;\n      }\n    }]);\n  }((0, _eventTargetShim.default)(...READER_EVENTS));\n  FileReader.EMPTY = EMPTY;\n  FileReader.LOADING = LOADING;\n  FileReader.DONE = DONE;\n  var _default = exports.default = FileReader;\n});", "lineCount": 164, "map": [[12, 2, 13, 0], [12, 6, 13, 0, "_NativeFileReaderModule"], [12, 29, 13, 0], [12, 32, 13, 0, "_interopRequireDefault"], [12, 54, 13, 0], [12, 55, 13, 0, "require"], [12, 62, 13, 0], [12, 63, 13, 0, "_dependencyMap"], [12, 77, 13, 0], [13, 2, 14, 0], [13, 6, 14, 0, "_base64Js"], [13, 15, 14, 0], [13, 18, 14, 0, "require"], [13, 25, 14, 0], [13, 26, 14, 0, "_dependencyMap"], [13, 40, 14, 0], [14, 2, 15, 0], [14, 6, 15, 0, "_eventTar<PERSON><PERSON><PERSON>"], [14, 22, 15, 0], [14, 25, 15, 0, "_interopRequireDefault"], [14, 47, 15, 0], [14, 48, 15, 0, "require"], [14, 55, 15, 0], [14, 56, 15, 0, "_dependencyMap"], [14, 70, 15, 0], [15, 2, 15, 44], [15, 11, 15, 44, "_callSuper"], [15, 22, 15, 44, "t"], [15, 23, 15, 44], [15, 25, 15, 44, "o"], [15, 26, 15, 44], [15, 28, 15, 44, "e"], [15, 29, 15, 44], [15, 40, 15, 44, "o"], [15, 41, 15, 44], [15, 48, 15, 44, "_getPrototypeOf2"], [15, 64, 15, 44], [15, 65, 15, 44, "default"], [15, 72, 15, 44], [15, 74, 15, 44, "o"], [15, 75, 15, 44], [15, 82, 15, 44, "_possibleConstructorReturn2"], [15, 109, 15, 44], [15, 110, 15, 44, "default"], [15, 117, 15, 44], [15, 119, 15, 44, "t"], [15, 120, 15, 44], [15, 122, 15, 44, "_isNativeReflectConstruct"], [15, 147, 15, 44], [15, 152, 15, 44, "Reflect"], [15, 159, 15, 44], [15, 160, 15, 44, "construct"], [15, 169, 15, 44], [15, 170, 15, 44, "o"], [15, 171, 15, 44], [15, 173, 15, 44, "e"], [15, 174, 15, 44], [15, 186, 15, 44, "_getPrototypeOf2"], [15, 202, 15, 44], [15, 203, 15, 44, "default"], [15, 210, 15, 44], [15, 212, 15, 44, "t"], [15, 213, 15, 44], [15, 215, 15, 44, "constructor"], [15, 226, 15, 44], [15, 230, 15, 44, "o"], [15, 231, 15, 44], [15, 232, 15, 44, "apply"], [15, 237, 15, 44], [15, 238, 15, 44, "t"], [15, 239, 15, 44], [15, 241, 15, 44, "e"], [15, 242, 15, 44], [16, 2, 15, 44], [16, 11, 15, 44, "_isNativeReflectConstruct"], [16, 37, 15, 44], [16, 51, 15, 44, "t"], [16, 52, 15, 44], [16, 56, 15, 44, "Boolean"], [16, 63, 15, 44], [16, 64, 15, 44, "prototype"], [16, 73, 15, 44], [16, 74, 15, 44, "valueOf"], [16, 81, 15, 44], [16, 82, 15, 44, "call"], [16, 86, 15, 44], [16, 87, 15, 44, "Reflect"], [16, 94, 15, 44], [16, 95, 15, 44, "construct"], [16, 104, 15, 44], [16, 105, 15, 44, "Boolean"], [16, 112, 15, 44], [16, 145, 15, 44, "t"], [16, 146, 15, 44], [16, 159, 15, 44, "_isNativeReflectConstruct"], [16, 184, 15, 44], [16, 196, 15, 44, "_isNativeReflectConstruct"], [16, 197, 15, 44], [16, 210, 15, 44, "t"], [16, 211, 15, 44], [17, 2, 24, 0], [17, 6, 24, 6, "READER_EVENTS"], [17, 19, 24, 19], [17, 22, 24, 22], [17, 23, 25, 2], [17, 30, 25, 9], [17, 32, 26, 2], [17, 39, 26, 9], [17, 41, 27, 2], [17, 47, 27, 8], [17, 49, 28, 2], [17, 60, 28, 13], [17, 62, 29, 2], [17, 71, 29, 11], [17, 73, 30, 2], [17, 83, 30, 12], [17, 84, 31, 1], [18, 2, 33, 0], [18, 6, 33, 6, "EMPTY"], [18, 11, 33, 11], [18, 14, 33, 14], [18, 15, 33, 24], [19, 2, 34, 0], [19, 6, 34, 6, "LOADING"], [19, 13, 34, 13], [19, 16, 34, 16], [19, 17, 34, 26], [20, 2, 35, 0], [20, 6, 35, 6, "DONE"], [20, 10, 35, 10], [20, 13, 35, 13], [20, 14, 35, 23], [21, 2, 35, 24], [21, 6, 37, 6, "FileReader"], [21, 16, 37, 16], [21, 42, 37, 16, "_ref"], [21, 46, 37, 16], [22, 4, 51, 2], [22, 13, 51, 2, "FileReader"], [22, 24, 51, 2], [22, 26, 51, 16], [23, 6, 51, 16], [23, 10, 51, 16, "_this"], [23, 15, 51, 16], [24, 6, 51, 16], [24, 10, 51, 16, "_classCallCheck2"], [24, 26, 51, 16], [24, 27, 51, 16, "default"], [24, 34, 51, 16], [24, 42, 51, 16, "FileReader"], [24, 52, 51, 16], [25, 6, 52, 4, "_this"], [25, 11, 52, 4], [25, 14, 52, 4, "_callSuper"], [25, 24, 52, 4], [25, 31, 52, 4, "FileReader"], [25, 41, 52, 4], [26, 6, 52, 12, "_this"], [26, 11, 52, 12], [26, 12, 42, 2, "EMPTY"], [26, 17, 42, 7], [26, 20, 42, 13, "EMPTY"], [26, 25, 42, 18], [27, 6, 42, 18, "_this"], [27, 11, 42, 18], [27, 12, 43, 2, "LOADING"], [27, 19, 43, 9], [27, 22, 43, 15, "LOADING"], [27, 29, 43, 22], [28, 6, 43, 22, "_this"], [28, 11, 43, 22], [28, 12, 44, 2, "DONE"], [28, 16, 44, 6], [28, 19, 44, 12, "DONE"], [28, 23, 44, 16], [29, 6, 44, 16, "_this"], [29, 11, 44, 16], [29, 12, 49, 2, "_aborted"], [29, 20, 49, 10], [29, 23, 49, 22], [29, 28, 49, 27], [30, 6, 53, 4, "_this"], [30, 11, 53, 4], [30, 12, 53, 9, "_reset"], [30, 18, 53, 15], [30, 19, 53, 16], [30, 20, 53, 17], [31, 6, 53, 18], [31, 13, 53, 18, "_this"], [31, 18, 53, 18], [32, 4, 54, 2], [33, 4, 54, 3], [33, 8, 54, 3, "_inherits2"], [33, 18, 54, 3], [33, 19, 54, 3, "default"], [33, 26, 54, 3], [33, 28, 54, 3, "FileReader"], [33, 38, 54, 3], [33, 40, 54, 3, "_ref"], [33, 44, 54, 3], [34, 4, 54, 3], [34, 15, 54, 3, "_createClass2"], [34, 28, 54, 3], [34, 29, 54, 3, "default"], [34, 36, 54, 3], [34, 38, 54, 3, "FileReader"], [34, 48, 54, 3], [35, 6, 54, 3, "key"], [35, 9, 54, 3], [36, 6, 54, 3, "value"], [36, 11, 54, 3], [36, 13, 56, 2], [36, 22, 56, 2, "_reset"], [36, 28, 56, 8, "_reset"], [36, 29, 56, 8], [36, 31, 56, 17], [37, 8, 57, 4], [37, 12, 57, 8], [37, 13, 57, 9, "_readyState"], [37, 24, 57, 20], [37, 27, 57, 23, "EMPTY"], [37, 32, 57, 28], [38, 8, 58, 4], [38, 12, 58, 8], [38, 13, 58, 9, "_error"], [38, 19, 58, 15], [38, 22, 58, 18], [38, 26, 58, 22], [39, 8, 59, 4], [39, 12, 59, 8], [39, 13, 59, 9, "_result"], [39, 20, 59, 16], [39, 23, 59, 19], [39, 27, 59, 23], [40, 6, 60, 2], [41, 4, 60, 3], [42, 6, 60, 3, "key"], [42, 9, 60, 3], [43, 6, 60, 3, "value"], [43, 11, 60, 3], [43, 13, 62, 2], [43, 22, 62, 2, "_setReadyState"], [43, 36, 62, 16, "_setReadyState"], [43, 37, 62, 17, "newState"], [43, 45, 62, 37], [43, 47, 62, 39], [44, 8, 63, 4], [44, 12, 63, 8], [44, 13, 63, 9, "_readyState"], [44, 24, 63, 20], [44, 27, 63, 23, "newState"], [44, 35, 63, 31], [45, 8, 64, 4], [45, 12, 64, 8], [45, 13, 64, 9, "dispatchEvent"], [45, 26, 64, 22], [45, 27, 64, 23], [46, 10, 64, 24, "type"], [46, 14, 64, 28], [46, 16, 64, 30], [47, 8, 64, 48], [47, 9, 64, 49], [47, 10, 64, 50], [48, 8, 65, 4], [48, 12, 65, 8, "newState"], [48, 20, 65, 16], [48, 25, 65, 21, "DONE"], [48, 29, 65, 25], [48, 31, 65, 27], [49, 10, 66, 6], [49, 14, 66, 10], [49, 18, 66, 14], [49, 19, 66, 15, "_aborted"], [49, 27, 66, 23], [49, 29, 66, 25], [50, 12, 67, 8], [50, 16, 67, 12], [50, 17, 67, 13, "dispatchEvent"], [50, 30, 67, 26], [50, 31, 67, 27], [51, 14, 67, 28, "type"], [51, 18, 67, 32], [51, 20, 67, 34], [52, 12, 67, 41], [52, 13, 67, 42], [52, 14, 67, 43], [53, 10, 68, 6], [53, 11, 68, 7], [53, 17, 68, 13], [53, 21, 68, 17], [53, 25, 68, 21], [53, 26, 68, 22, "_error"], [53, 32, 68, 28], [53, 34, 68, 30], [54, 12, 69, 8], [54, 16, 69, 12], [54, 17, 69, 13, "dispatchEvent"], [54, 30, 69, 26], [54, 31, 69, 27], [55, 14, 69, 28, "type"], [55, 18, 69, 32], [55, 20, 69, 34], [56, 12, 69, 41], [56, 13, 69, 42], [56, 14, 69, 43], [57, 10, 70, 6], [57, 11, 70, 7], [57, 17, 70, 13], [58, 12, 71, 8], [58, 16, 71, 12], [58, 17, 71, 13, "dispatchEvent"], [58, 30, 71, 26], [58, 31, 71, 27], [59, 14, 71, 28, "type"], [59, 18, 71, 32], [59, 20, 71, 34], [60, 12, 71, 40], [60, 13, 71, 41], [60, 14, 71, 42], [61, 10, 72, 6], [62, 10, 73, 6], [62, 14, 73, 10], [62, 15, 73, 11, "dispatchEvent"], [62, 28, 73, 24], [62, 29, 73, 25], [63, 12, 73, 26, "type"], [63, 16, 73, 30], [63, 18, 73, 32], [64, 10, 73, 41], [64, 11, 73, 42], [64, 12, 73, 43], [65, 8, 74, 4], [66, 6, 75, 2], [67, 4, 75, 3], [68, 6, 75, 3, "key"], [68, 9, 75, 3], [69, 6, 75, 3, "value"], [69, 11, 75, 3], [69, 13, 77, 2], [69, 22, 77, 2, "readAsA<PERSON>y<PERSON><PERSON>er"], [69, 39, 77, 19, "readAsA<PERSON>y<PERSON><PERSON>er"], [69, 40, 77, 20, "blob"], [69, 44, 77, 31], [69, 46, 77, 39], [70, 8, 78, 4], [70, 12, 78, 8], [70, 13, 78, 9, "_aborted"], [70, 21, 78, 17], [70, 24, 78, 20], [70, 29, 78, 25], [71, 8, 80, 4], [71, 12, 80, 8, "blob"], [71, 16, 80, 12], [71, 20, 80, 16], [71, 24, 80, 20], [71, 26, 80, 22], [72, 10, 81, 6], [72, 16, 81, 12], [72, 20, 81, 16, "TypeError"], [72, 29, 81, 25], [72, 30, 82, 8], [72, 120, 83, 6], [72, 121, 83, 7], [73, 8, 84, 4], [74, 8, 86, 4, "NativeFileReaderModule"], [74, 39, 86, 26], [74, 40, 86, 27, "readAsDataURL"], [74, 53, 86, 40], [74, 54, 86, 41, "blob"], [74, 58, 86, 45], [74, 59, 86, 46, "data"], [74, 63, 86, 50], [74, 64, 86, 51], [74, 65, 86, 52, "then"], [74, 69, 86, 56], [74, 70, 87, 7, "text"], [74, 74, 87, 19], [74, 78, 87, 24], [75, 10, 88, 8], [75, 14, 88, 12], [75, 18, 88, 16], [75, 19, 88, 17, "_aborted"], [75, 27, 88, 25], [75, 29, 88, 27], [76, 12, 89, 10], [77, 10, 90, 8], [78, 10, 92, 8], [78, 14, 92, 14, "base64"], [78, 20, 92, 20], [78, 23, 92, 23, "text"], [78, 27, 92, 27], [78, 28, 92, 28, "split"], [78, 33, 92, 33], [78, 34, 92, 34], [78, 37, 92, 37], [78, 38, 92, 38], [78, 39, 92, 39], [78, 40, 92, 40], [78, 41, 92, 41], [79, 10, 93, 8], [79, 14, 93, 14, "typedArray"], [79, 24, 93, 24], [79, 27, 93, 27], [79, 31, 93, 27, "toByteArray"], [79, 52, 93, 38], [79, 54, 93, 39, "base64"], [79, 60, 93, 45], [79, 61, 93, 46], [80, 10, 95, 8], [80, 14, 95, 12], [80, 15, 95, 13, "_result"], [80, 22, 95, 20], [80, 25, 95, 23, "typedArray"], [80, 35, 95, 33], [80, 36, 95, 34, "buffer"], [80, 42, 95, 40], [81, 10, 96, 8], [81, 14, 96, 12], [81, 15, 96, 13, "_setReadyState"], [81, 29, 96, 27], [81, 30, 96, 28, "DONE"], [81, 34, 96, 32], [81, 35, 96, 33], [82, 8, 97, 6], [82, 9, 97, 7], [82, 11, 98, 6, "error"], [82, 16, 98, 11], [82, 20, 98, 15], [83, 10, 99, 8], [83, 14, 99, 12], [83, 18, 99, 16], [83, 19, 99, 17, "_aborted"], [83, 27, 99, 25], [83, 29, 99, 27], [84, 12, 100, 10], [85, 10, 101, 8], [86, 10, 102, 8], [86, 14, 102, 12], [86, 15, 102, 13, "_error"], [86, 21, 102, 19], [86, 24, 102, 22, "error"], [86, 29, 102, 27], [87, 10, 103, 8], [87, 14, 103, 12], [87, 15, 103, 13, "_setReadyState"], [87, 29, 103, 27], [87, 30, 103, 28, "DONE"], [87, 34, 103, 32], [87, 35, 103, 33], [88, 8, 104, 6], [88, 9, 105, 4], [88, 10, 105, 5], [89, 6, 106, 2], [90, 4, 106, 3], [91, 6, 106, 3, "key"], [91, 9, 106, 3], [92, 6, 106, 3, "value"], [92, 11, 106, 3], [92, 13, 108, 2], [92, 22, 108, 2, "readAsDataURL"], [92, 35, 108, 15, "readAsDataURL"], [92, 36, 108, 16, "blob"], [92, 40, 108, 27], [92, 42, 108, 35], [93, 8, 109, 4], [93, 12, 109, 8], [93, 13, 109, 9, "_aborted"], [93, 21, 109, 17], [93, 24, 109, 20], [93, 29, 109, 25], [94, 8, 111, 4], [94, 12, 111, 8, "blob"], [94, 16, 111, 12], [94, 20, 111, 16], [94, 24, 111, 20], [94, 26, 111, 22], [95, 10, 112, 6], [95, 16, 112, 12], [95, 20, 112, 16, "TypeError"], [95, 29, 112, 25], [95, 30, 113, 8], [95, 116, 114, 6], [95, 117, 114, 7], [96, 8, 115, 4], [97, 8, 117, 4, "NativeFileReaderModule"], [97, 39, 117, 26], [97, 40, 117, 27, "readAsDataURL"], [97, 53, 117, 40], [97, 54, 117, 41, "blob"], [97, 58, 117, 45], [97, 59, 117, 46, "data"], [97, 63, 117, 50], [97, 64, 117, 51], [97, 65, 117, 52, "then"], [97, 69, 117, 56], [97, 70, 118, 7, "text"], [97, 74, 118, 19], [97, 78, 118, 24], [98, 10, 119, 8], [98, 14, 119, 12], [98, 18, 119, 16], [98, 19, 119, 17, "_aborted"], [98, 27, 119, 25], [98, 29, 119, 27], [99, 12, 120, 10], [100, 10, 121, 8], [101, 10, 122, 8], [101, 14, 122, 12], [101, 15, 122, 13, "_result"], [101, 22, 122, 20], [101, 25, 122, 23, "text"], [101, 29, 122, 27], [102, 10, 123, 8], [102, 14, 123, 12], [102, 15, 123, 13, "_setReadyState"], [102, 29, 123, 27], [102, 30, 123, 28, "DONE"], [102, 34, 123, 32], [102, 35, 123, 33], [103, 8, 124, 6], [103, 9, 124, 7], [103, 11, 125, 6, "error"], [103, 16, 125, 11], [103, 20, 125, 15], [104, 10, 126, 8], [104, 14, 126, 12], [104, 18, 126, 16], [104, 19, 126, 17, "_aborted"], [104, 27, 126, 25], [104, 29, 126, 27], [105, 12, 127, 10], [106, 10, 128, 8], [107, 10, 129, 8], [107, 14, 129, 12], [107, 15, 129, 13, "_error"], [107, 21, 129, 19], [107, 24, 129, 22, "error"], [107, 29, 129, 27], [108, 10, 130, 8], [108, 14, 130, 12], [108, 15, 130, 13, "_setReadyState"], [108, 29, 130, 27], [108, 30, 130, 28, "DONE"], [108, 34, 130, 32], [108, 35, 130, 33], [109, 8, 131, 6], [109, 9, 132, 4], [109, 10, 132, 5], [110, 6, 133, 2], [111, 4, 133, 3], [112, 6, 133, 3, "key"], [112, 9, 133, 3], [113, 6, 133, 3, "value"], [113, 11, 133, 3], [113, 13, 135, 2], [113, 22, 135, 2, "readAsText"], [113, 32, 135, 12, "readAsText"], [113, 33, 135, 13, "blob"], [113, 37, 135, 24], [113, 39, 135, 60], [114, 8, 135, 60], [114, 12, 135, 26, "encoding"], [114, 20, 135, 42], [114, 23, 135, 42, "arguments"], [114, 32, 135, 42], [114, 33, 135, 42, "length"], [114, 39, 135, 42], [114, 47, 135, 42, "arguments"], [114, 56, 135, 42], [114, 64, 135, 42, "undefined"], [114, 73, 135, 42], [114, 76, 135, 42, "arguments"], [114, 85, 135, 42], [114, 91, 135, 45], [114, 98, 135, 52], [115, 8, 136, 4], [115, 12, 136, 8], [115, 13, 136, 9, "_aborted"], [115, 21, 136, 17], [115, 24, 136, 20], [115, 29, 136, 25], [116, 8, 138, 4], [116, 12, 138, 8, "blob"], [116, 16, 138, 12], [116, 20, 138, 16], [116, 24, 138, 20], [116, 26, 138, 22], [117, 10, 139, 6], [117, 16, 139, 12], [117, 20, 139, 16, "TypeError"], [117, 29, 139, 25], [117, 30, 140, 8], [117, 113, 141, 6], [117, 114, 141, 7], [118, 8, 142, 4], [119, 8, 144, 4, "NativeFileReaderModule"], [119, 39, 144, 26], [119, 40, 144, 27, "readAsText"], [119, 50, 144, 37], [119, 51, 144, 38, "blob"], [119, 55, 144, 42], [119, 56, 144, 43, "data"], [119, 60, 144, 47], [119, 62, 144, 49, "encoding"], [119, 70, 144, 57], [119, 71, 144, 58], [119, 72, 144, 59, "then"], [119, 76, 144, 63], [119, 77, 145, 7, "text"], [119, 81, 145, 19], [119, 85, 145, 24], [120, 10, 146, 8], [120, 14, 146, 12], [120, 18, 146, 16], [120, 19, 146, 17, "_aborted"], [120, 27, 146, 25], [120, 29, 146, 27], [121, 12, 147, 10], [122, 10, 148, 8], [123, 10, 149, 8], [123, 14, 149, 12], [123, 15, 149, 13, "_result"], [123, 22, 149, 20], [123, 25, 149, 23, "text"], [123, 29, 149, 27], [124, 10, 150, 8], [124, 14, 150, 12], [124, 15, 150, 13, "_setReadyState"], [124, 29, 150, 27], [124, 30, 150, 28, "DONE"], [124, 34, 150, 32], [124, 35, 150, 33], [125, 8, 151, 6], [125, 9, 151, 7], [125, 11, 152, 6, "error"], [125, 16, 152, 11], [125, 20, 152, 15], [126, 10, 153, 8], [126, 14, 153, 12], [126, 18, 153, 16], [126, 19, 153, 17, "_aborted"], [126, 27, 153, 25], [126, 29, 153, 27], [127, 12, 154, 10], [128, 10, 155, 8], [129, 10, 156, 8], [129, 14, 156, 12], [129, 15, 156, 13, "_error"], [129, 21, 156, 19], [129, 24, 156, 22, "error"], [129, 29, 156, 27], [130, 10, 157, 8], [130, 14, 157, 12], [130, 15, 157, 13, "_setReadyState"], [130, 29, 157, 27], [130, 30, 157, 28, "DONE"], [130, 34, 157, 32], [130, 35, 157, 33], [131, 8, 158, 6], [131, 9, 159, 4], [131, 10, 159, 5], [132, 6, 160, 2], [133, 4, 160, 3], [134, 6, 160, 3, "key"], [134, 9, 160, 3], [135, 6, 160, 3, "value"], [135, 11, 160, 3], [135, 13, 162, 2], [135, 22, 162, 2, "abort"], [135, 27, 162, 7, "abort"], [135, 28, 162, 7], [135, 30, 162, 10], [136, 8, 163, 4], [136, 12, 163, 8], [136, 13, 163, 9, "_aborted"], [136, 21, 163, 17], [136, 24, 163, 20], [136, 28, 163, 24], [137, 8, 165, 4], [137, 12, 165, 8], [137, 16, 165, 12], [137, 17, 165, 13, "_readyState"], [137, 28, 165, 24], [137, 33, 165, 29, "EMPTY"], [137, 38, 165, 34], [137, 42, 165, 38], [137, 46, 165, 42], [137, 47, 165, 43, "_readyState"], [137, 58, 165, 54], [137, 63, 165, 59, "DONE"], [137, 67, 165, 63], [137, 69, 165, 65], [138, 10, 166, 6], [138, 14, 166, 10], [138, 15, 166, 11, "_reset"], [138, 21, 166, 17], [138, 22, 166, 18], [138, 23, 166, 19], [139, 10, 167, 6], [139, 14, 167, 10], [139, 15, 167, 11, "_setReadyState"], [139, 29, 167, 25], [139, 30, 167, 26, "DONE"], [139, 34, 167, 30], [139, 35, 167, 31], [140, 8, 168, 4], [141, 8, 170, 4], [141, 12, 170, 8], [141, 13, 170, 9, "_reset"], [141, 19, 170, 15], [141, 20, 170, 16], [141, 21, 170, 17], [142, 6, 171, 2], [143, 4, 171, 3], [144, 6, 171, 3, "key"], [144, 9, 171, 3], [145, 6, 171, 3, "get"], [145, 9, 171, 3], [145, 11, 173, 2], [145, 20, 173, 2, "get"], [145, 21, 173, 2], [145, 23, 173, 31], [146, 8, 174, 4], [146, 15, 174, 11], [146, 19, 174, 15], [146, 20, 174, 16, "_readyState"], [146, 31, 174, 27], [147, 6, 175, 2], [148, 4, 175, 3], [149, 6, 175, 3, "key"], [149, 9, 175, 3], [150, 6, 175, 3, "get"], [150, 9, 175, 3], [150, 11, 177, 2], [150, 20, 177, 2, "get"], [150, 21, 177, 2], [150, 23, 177, 22], [151, 8, 178, 4], [151, 15, 178, 11], [151, 19, 178, 15], [151, 20, 178, 16, "_error"], [151, 26, 178, 22], [152, 6, 179, 2], [153, 4, 179, 3], [154, 6, 179, 3, "key"], [154, 9, 179, 3], [155, 6, 179, 3, "get"], [155, 9, 179, 3], [155, 11, 181, 2], [155, 20, 181, 2, "get"], [155, 21, 181, 2], [155, 23, 181, 30], [156, 8, 182, 4], [156, 15, 182, 11], [156, 19, 182, 15], [156, 20, 182, 16, "_result"], [156, 27, 182, 23], [157, 6, 183, 2], [158, 4, 183, 3], [159, 2, 183, 3], [159, 4, 37, 26], [159, 8, 37, 26, "EventTarget"], [159, 32, 37, 37], [159, 34, 37, 38], [159, 37, 37, 41, "READER_EVENTS"], [159, 50, 37, 54], [159, 51, 37, 55], [160, 2, 37, 6, "FileReader"], [160, 12, 37, 16], [160, 13, 38, 9, "EMPTY"], [160, 18, 38, 14], [160, 21, 38, 20, "EMPTY"], [160, 26, 38, 25], [161, 2, 37, 6, "FileReader"], [161, 12, 37, 16], [161, 13, 39, 9, "LOADING"], [161, 20, 39, 16], [161, 23, 39, 22, "LOADING"], [161, 30, 39, 29], [162, 2, 37, 6, "FileReader"], [162, 12, 37, 16], [162, 13, 40, 9, "DONE"], [162, 17, 40, 13], [162, 20, 40, 19, "DONE"], [162, 24, 40, 23], [163, 2, 40, 23], [163, 6, 40, 23, "_default"], [163, 14, 40, 23], [163, 17, 40, 23, "exports"], [163, 24, 40, 23], [163, 25, 40, 23, "default"], [163, 32, 40, 23], [163, 35, 186, 15, "FileReader"], [163, 45, 186, 25], [164, 0, 186, 25], [164, 3]], "functionMap": {"names": ["<global>", "FileReader", "FileReader#constructor", "FileReader#_reset", "FileReader#_setReadyState", "FileReader#readAsArrayBuffer", "NativeFileReaderModule.readAsDataURL.then$argument_0", "NativeFileReaderModule.readAsDataURL.then$argument_1", "FileReader#readAsDataURL", "FileReader#readAsText", "NativeFileReaderModule.readAsText.then$argument_0", "NativeFileReaderModule.readAsText.then$argument_1", "FileReader#abort", "FileReader#get__readyState", "FileReader#get__error", "FileReader#get__result"], "mappings": "AAA;ACoC;ECc;GDG;EEE;GFI;EGE;GHa;EIE;MCU;ODU;MEC;OFM;GJE;EOE;MFU;OEM;MDC;OCM;GPE;EQE;MCU;ODM;MEC;OFM;GRE;EWE;GXS;EYE;GZE;EaE;GbE;EcE;GdE;CDC"}}, "type": "js/module"}]}