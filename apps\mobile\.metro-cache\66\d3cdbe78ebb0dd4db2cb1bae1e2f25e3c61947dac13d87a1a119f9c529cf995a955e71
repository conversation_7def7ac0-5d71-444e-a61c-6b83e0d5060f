{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 56, "index": 88}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/ReactNative/AppContainer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 138}, "end": {"line": 5, "column": 75, "index": 213}}], "key": "FRYGpgCIDQCV3LH0aEC+L46quJ0=", "exportNames": ["*"]}}, {"name": "./ScreenContentWrapper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 214}, "end": {"line": 6, "column": 58, "index": 272}}], "key": "5uhLMvAOHko31khRs+w2fAYa0Lc=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _AppContainer = _interopRequireDefault(require(_dependencyMap[4], \"react-native/Libraries/ReactNative/AppContainer\"));\n  var _ScreenContentWrapper = _interopRequireDefault(require(_dependencyMap[5], \"./ScreenContentWrapper\"));\n  var _jsxDevRuntime = require(_dependencyMap[6], \"react/jsx-dev-runtime\");\n  var _excluded = [\"stackPresentation\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-screens\\\\src\\\\components\\\\DebugContainer.tsx\"; // @ts-expect-error importing private component\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * This view must *not* be flattened.\n   * See https://github.com/software-mansion/react-native-screens/pull/1825\n   * for detailed explanation.\n   */\n  var DebugContainer = props => {\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScreenContentWrapper.default, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 10\n    }, this);\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    DebugContainer = props => {\n      var stackPresentation = props.stackPresentation,\n        rest = (0, _objectWithoutProperties2.default)(props, _excluded);\n      if (_reactNative.Platform.OS === 'ios' && stackPresentation !== 'push' && stackPresentation !== 'formSheet') {\n        // This is necessary for LogBox\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_AppContainer.default, {\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScreenContentWrapper.default, {\n            ...rest\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 9\n        }, this);\n      }\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScreenContentWrapper.default, {\n        ...rest\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 12\n      }, this);\n    };\n    DebugContainer.displayName = 'DebugContainer';\n  }\n  var _default = exports.default = DebugContainer;\n});", "lineCount": 61, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "React"], [8, 11, 1, 0], [8, 14, 1, 0, "_interopRequireWildcard"], [8, 37, 1, 0], [8, 38, 1, 0, "require"], [8, 45, 1, 0], [8, 46, 1, 0, "_dependencyMap"], [8, 60, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_AppContainer"], [10, 19, 5, 0], [10, 22, 5, 0, "_interopRequireDefault"], [10, 44, 5, 0], [10, 45, 5, 0, "require"], [10, 52, 5, 0], [10, 53, 5, 0, "_dependencyMap"], [10, 67, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_ScreenContentWrapper"], [11, 27, 6, 0], [11, 30, 6, 0, "_interopRequireDefault"], [11, 52, 6, 0], [11, 53, 6, 0, "require"], [11, 60, 6, 0], [11, 61, 6, 0, "_dependencyMap"], [11, 75, 6, 0], [12, 2, 6, 58], [12, 6, 6, 58, "_jsxDevRuntime"], [12, 20, 6, 58], [12, 23, 6, 58, "require"], [12, 30, 6, 58], [12, 31, 6, 58, "_dependencyMap"], [12, 45, 6, 58], [13, 2, 6, 58], [13, 6, 6, 58, "_excluded"], [13, 15, 6, 58], [14, 2, 6, 58], [14, 6, 6, 58, "_jsxFileName"], [14, 18, 6, 58], [14, 130, 3, 0], [15, 2, 3, 0], [15, 11, 3, 0, "_interopRequireWildcard"], [15, 35, 3, 0, "e"], [15, 36, 3, 0], [15, 38, 3, 0, "t"], [15, 39, 3, 0], [15, 68, 3, 0, "WeakMap"], [15, 75, 3, 0], [15, 81, 3, 0, "r"], [15, 82, 3, 0], [15, 89, 3, 0, "WeakMap"], [15, 96, 3, 0], [15, 100, 3, 0, "n"], [15, 101, 3, 0], [15, 108, 3, 0, "WeakMap"], [15, 115, 3, 0], [15, 127, 3, 0, "_interopRequireWildcard"], [15, 150, 3, 0], [15, 162, 3, 0, "_interopRequireWildcard"], [15, 163, 3, 0, "e"], [15, 164, 3, 0], [15, 166, 3, 0, "t"], [15, 167, 3, 0], [15, 176, 3, 0, "t"], [15, 177, 3, 0], [15, 181, 3, 0, "e"], [15, 182, 3, 0], [15, 186, 3, 0, "e"], [15, 187, 3, 0], [15, 188, 3, 0, "__esModule"], [15, 198, 3, 0], [15, 207, 3, 0, "e"], [15, 208, 3, 0], [15, 214, 3, 0, "o"], [15, 215, 3, 0], [15, 217, 3, 0, "i"], [15, 218, 3, 0], [15, 220, 3, 0, "f"], [15, 221, 3, 0], [15, 226, 3, 0, "__proto__"], [15, 235, 3, 0], [15, 243, 3, 0, "default"], [15, 250, 3, 0], [15, 252, 3, 0, "e"], [15, 253, 3, 0], [15, 270, 3, 0, "e"], [15, 271, 3, 0], [15, 294, 3, 0, "e"], [15, 295, 3, 0], [15, 320, 3, 0, "e"], [15, 321, 3, 0], [15, 330, 3, 0, "f"], [15, 331, 3, 0], [15, 337, 3, 0, "o"], [15, 338, 3, 0], [15, 341, 3, 0, "t"], [15, 342, 3, 0], [15, 345, 3, 0, "n"], [15, 346, 3, 0], [15, 349, 3, 0, "r"], [15, 350, 3, 0], [15, 358, 3, 0, "o"], [15, 359, 3, 0], [15, 360, 3, 0, "has"], [15, 363, 3, 0], [15, 364, 3, 0, "e"], [15, 365, 3, 0], [15, 375, 3, 0, "o"], [15, 376, 3, 0], [15, 377, 3, 0, "get"], [15, 380, 3, 0], [15, 381, 3, 0, "e"], [15, 382, 3, 0], [15, 385, 3, 0, "o"], [15, 386, 3, 0], [15, 387, 3, 0, "set"], [15, 390, 3, 0], [15, 391, 3, 0, "e"], [15, 392, 3, 0], [15, 394, 3, 0, "f"], [15, 395, 3, 0], [15, 409, 3, 0, "_t"], [15, 411, 3, 0], [15, 415, 3, 0, "e"], [15, 416, 3, 0], [15, 432, 3, 0, "_t"], [15, 434, 3, 0], [15, 441, 3, 0, "hasOwnProperty"], [15, 455, 3, 0], [15, 456, 3, 0, "call"], [15, 460, 3, 0], [15, 461, 3, 0, "e"], [15, 462, 3, 0], [15, 464, 3, 0, "_t"], [15, 466, 3, 0], [15, 473, 3, 0, "i"], [15, 474, 3, 0], [15, 478, 3, 0, "o"], [15, 479, 3, 0], [15, 482, 3, 0, "Object"], [15, 488, 3, 0], [15, 489, 3, 0, "defineProperty"], [15, 503, 3, 0], [15, 508, 3, 0, "Object"], [15, 514, 3, 0], [15, 515, 3, 0, "getOwnPropertyDescriptor"], [15, 539, 3, 0], [15, 540, 3, 0, "e"], [15, 541, 3, 0], [15, 543, 3, 0, "_t"], [15, 545, 3, 0], [15, 552, 3, 0, "i"], [15, 553, 3, 0], [15, 554, 3, 0, "get"], [15, 557, 3, 0], [15, 561, 3, 0, "i"], [15, 562, 3, 0], [15, 563, 3, 0, "set"], [15, 566, 3, 0], [15, 570, 3, 0, "o"], [15, 571, 3, 0], [15, 572, 3, 0, "f"], [15, 573, 3, 0], [15, 575, 3, 0, "_t"], [15, 577, 3, 0], [15, 579, 3, 0, "i"], [15, 580, 3, 0], [15, 584, 3, 0, "f"], [15, 585, 3, 0], [15, 586, 3, 0, "_t"], [15, 588, 3, 0], [15, 592, 3, 0, "e"], [15, 593, 3, 0], [15, 594, 3, 0, "_t"], [15, 596, 3, 0], [15, 607, 3, 0, "f"], [15, 608, 3, 0], [15, 613, 3, 0, "e"], [15, 614, 3, 0], [15, 616, 3, 0, "t"], [15, 617, 3, 0], [16, 2, 14, 0], [17, 0, 15, 0], [18, 0, 16, 0], [19, 0, 17, 0], [20, 0, 18, 0], [21, 2, 19, 0], [21, 6, 19, 4, "DebugContainer"], [21, 20, 19, 55], [21, 23, 19, 58, "props"], [21, 28, 19, 63], [21, 32, 19, 67], [22, 4, 20, 2], [22, 24, 20, 9], [22, 28, 20, 9, "_jsxDevRuntime"], [22, 42, 20, 9], [22, 43, 20, 9, "jsxDEV"], [22, 49, 20, 9], [22, 51, 20, 10, "_ScreenContentWrapper"], [22, 72, 20, 10], [22, 73, 20, 10, "default"], [22, 80, 20, 30], [23, 6, 20, 30], [23, 9, 20, 35, "props"], [24, 4, 20, 40], [25, 6, 20, 40, "fileName"], [25, 14, 20, 40], [25, 16, 20, 40, "_jsxFileName"], [25, 28, 20, 40], [26, 6, 20, 40, "lineNumber"], [26, 16, 20, 40], [27, 6, 20, 40, "columnNumber"], [27, 18, 20, 40], [28, 4, 20, 40], [28, 11, 20, 43], [28, 12, 20, 44], [29, 2, 21, 0], [29, 3, 21, 1], [30, 2, 23, 0], [30, 6, 23, 4, "process"], [30, 13, 23, 11], [30, 14, 23, 12, "env"], [30, 17, 23, 15], [30, 18, 23, 16, "NODE_ENV"], [30, 26, 23, 24], [30, 31, 23, 29], [30, 43, 23, 41], [30, 45, 23, 43], [31, 4, 24, 2, "DebugContainer"], [31, 18, 24, 16], [31, 21, 24, 20, "props"], [31, 26, 24, 41], [31, 30, 24, 46], [32, 6, 25, 4], [32, 10, 25, 12, "stackPresentation"], [32, 27, 25, 29], [32, 30, 25, 43, "props"], [32, 35, 25, 48], [32, 36, 25, 12, "stackPresentation"], [32, 53, 25, 29], [33, 8, 25, 34, "rest"], [33, 12, 25, 38], [33, 19, 25, 38, "_objectWithoutProperties2"], [33, 44, 25, 38], [33, 45, 25, 38, "default"], [33, 52, 25, 38], [33, 54, 25, 43, "props"], [33, 59, 25, 48], [33, 61, 25, 48, "_excluded"], [33, 70, 25, 48], [34, 6, 27, 4], [34, 10, 28, 6, "Platform"], [34, 31, 28, 14], [34, 32, 28, 15, "OS"], [34, 34, 28, 17], [34, 39, 28, 22], [34, 44, 28, 27], [34, 48, 29, 6, "stackPresentation"], [34, 65, 29, 23], [34, 70, 29, 28], [34, 76, 29, 34], [34, 80, 30, 6, "stackPresentation"], [34, 97, 30, 23], [34, 102, 30, 28], [34, 113, 30, 39], [34, 115, 31, 6], [35, 8, 32, 6], [36, 8, 33, 6], [36, 28, 34, 8], [36, 32, 34, 8, "_jsxDevRuntime"], [36, 46, 34, 8], [36, 47, 34, 8, "jsxDEV"], [36, 53, 34, 8], [36, 55, 34, 9, "_AppContainer"], [36, 68, 34, 9], [36, 69, 34, 9, "default"], [36, 76, 34, 21], [37, 10, 34, 21, "children"], [37, 18, 34, 21], [37, 33, 35, 10], [37, 37, 35, 10, "_jsxDevRuntime"], [37, 51, 35, 10], [37, 52, 35, 10, "jsxDEV"], [37, 58, 35, 10], [37, 60, 35, 11, "_ScreenContentWrapper"], [37, 81, 35, 11], [37, 82, 35, 11, "default"], [37, 89, 35, 31], [38, 12, 35, 31], [38, 15, 35, 36, "rest"], [39, 10, 35, 40], [40, 12, 35, 40, "fileName"], [40, 20, 35, 40], [40, 22, 35, 40, "_jsxFileName"], [40, 34, 35, 40], [41, 12, 35, 40, "lineNumber"], [41, 22, 35, 40], [42, 12, 35, 40, "columnNumber"], [42, 24, 35, 40], [43, 10, 35, 40], [43, 17, 35, 43], [44, 8, 35, 44], [45, 10, 35, 44, "fileName"], [45, 18, 35, 44], [45, 20, 35, 44, "_jsxFileName"], [45, 32, 35, 44], [46, 10, 35, 44, "lineNumber"], [46, 20, 35, 44], [47, 10, 35, 44, "columnNumber"], [47, 22, 35, 44], [48, 8, 35, 44], [48, 15, 36, 22], [48, 16, 36, 23], [49, 6, 38, 4], [50, 6, 40, 4], [50, 26, 40, 11], [50, 30, 40, 11, "_jsxDevRuntime"], [50, 44, 40, 11], [50, 45, 40, 11, "jsxDEV"], [50, 51, 40, 11], [50, 53, 40, 12, "_ScreenContentWrapper"], [50, 74, 40, 12], [50, 75, 40, 12, "default"], [50, 82, 40, 32], [51, 8, 40, 32], [51, 11, 40, 37, "rest"], [52, 6, 40, 41], [53, 8, 40, 41, "fileName"], [53, 16, 40, 41], [53, 18, 40, 41, "_jsxFileName"], [53, 30, 40, 41], [54, 8, 40, 41, "lineNumber"], [54, 18, 40, 41], [55, 8, 40, 41, "columnNumber"], [55, 20, 40, 41], [56, 6, 40, 41], [56, 13, 40, 44], [56, 14, 40, 45], [57, 4, 41, 2], [57, 5, 41, 3], [58, 4, 43, 2, "DebugContainer"], [58, 18, 43, 16], [58, 19, 43, 17, "displayName"], [58, 30, 43, 28], [58, 33, 43, 31], [58, 49, 43, 47], [59, 2, 44, 0], [60, 2, 44, 1], [60, 6, 44, 1, "_default"], [60, 14, 44, 1], [60, 17, 44, 1, "exports"], [60, 24, 44, 1], [60, 25, 44, 1, "default"], [60, 32, 44, 1], [60, 35, 46, 15, "DebugContainer"], [60, 49, 46, 29], [61, 0, 46, 29], [61, 3]], "functionMap": {"names": ["<global>", "DebugContainer"], "mappings": "AAA;0DCkB;CDE;mBCG;GDiB"}}, "type": "js/module"}]}