{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Animated/NativeAnimatedModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 92}}], "key": "qdkmO9OPhkFFeDj2sMqUscbZgO8=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Animated/NativeAnimatedTurboModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 94}}], "key": "H0TKcy4rEA4iORqMFOq8exgcjLE=", "exportNames": ["*"]}}, {"name": "../../../Libraries/EventEmitter/NativeEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 84}}], "key": "D1au0aDk2r1a9IRsgRz6oZshjQc=", "exportNames": ["*"]}}, {"name": "../../../Libraries/EventEmitter/RCTDeviceEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 90}}], "key": "+8CAVVaE73Nif99PUpg/9rchHq0=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 61}}], "key": "kIyIhRgCqWyDelgdE4/FX1x3fx4=", "exportNames": ["*"]}}, {"name": "../featureflags/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 83}}], "key": "Kmhs62QxtFmVwaol79R89dYm6iA=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "nullthrows", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 36}}], "key": "epufkdgpKN0G543QKwfSBBl0bWM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeAnimatedModule = _interopRequireDefault(require(_dependencyMap[1], \"../../../Libraries/Animated/NativeAnimatedModule\"));\n  var _NativeAnimatedTurboModule = _interopRequireDefault(require(_dependencyMap[2], \"../../../Libraries/Animated/NativeAnimatedTurboModule\"));\n  var _NativeEventEmitter = _interopRequireDefault(require(_dependencyMap[3], \"../../../Libraries/EventEmitter/NativeEventEmitter\"));\n  var _RCTDeviceEventEmitter = _interopRequireDefault(require(_dependencyMap[4], \"../../../Libraries/EventEmitter/RCTDeviceEventEmitter\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[5], \"../../../Libraries/Utilities/Platform\"));\n  var ReactNativeFeatureFlags = _interopRequireWildcard(require(_dependencyMap[6], \"../featureflags/ReactNativeFeatureFlags\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[7], \"invariant\"));\n  var _nullthrows = _interopRequireDefault(require(_dependencyMap[8], \"nullthrows\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var NativeAnimatedModule = _NativeAnimatedModule.default ?? _NativeAnimatedTurboModule.default;\n  var __nativeAnimatedNodeTagCount = 1;\n  var __nativeAnimationIdCount = 1;\n  var nativeEventEmitter;\n  var waitingForQueuedOperations = new Set();\n  var queueOperations = false;\n  var queue = [];\n  var singleOpQueue = [];\n  var isSingleOpBatching = _Platform.default.OS === 'android' && NativeAnimatedModule?.queueAndExecuteBatchedOperations != null && ReactNativeFeatureFlags.animatedShouldUseSingleOp();\n  var flushQueueImmediate = null;\n  var eventListenerGetValueCallbacks = {};\n  var eventListenerAnimationFinishedCallbacks = {};\n  var globalEventEmitterGetValueListener = null;\n  var globalEventEmitterAnimationFinishedListener = null;\n  function createNativeOperations() {\n    var methodNames = ['createAnimatedNode', 'updateAnimatedNodeConfig', 'getValue', 'startListeningToAnimatedNodeValue', 'stopListeningToAnimatedNodeValue', 'connectAnimatedNodes', 'disconnectAnimatedNodes', 'startAnimatingNode', 'stopAnimation', 'setAnimatedNodeValue', 'setAnimatedNodeOffset', 'flattenAnimatedNodeOffset', 'extractAnimatedNodeOffset', 'connectAnimatedNodeToView', 'disconnectAnimatedNodeFromView', 'restoreDefaultValues', 'dropAnimatedNode', 'addAnimatedEventToView', 'removeAnimatedEventFromView', 'addListener', 'removeListener'];\n    var nativeOperations = {};\n    if (isSingleOpBatching) {\n      var _loop = function () {\n        var methodName = methodNames[ii];\n        var operationID = ii + 1;\n        nativeOperations[methodName] = function () {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          singleOpQueue.push(operationID, ...args);\n        };\n      };\n      for (var ii = 0, length = methodNames.length; ii < length; ii++) {\n        _loop();\n      }\n    } else {\n      var _loop2 = function () {\n        var methodName = methodNames[_ii];\n        nativeOperations[methodName] = function () {\n          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n            args[_key2] = arguments[_key2];\n          }\n          var method = (0, _nullthrows.default)(NativeAnimatedModule)[methodName];\n          if (queueOperations || queue.length !== 0) {\n            queue.push(() => method(...args));\n          } else {\n            method(...args);\n          }\n        };\n      };\n      for (var _ii = 0, _length = methodNames.length; _ii < _length; _ii++) {\n        _loop2();\n      }\n    }\n    return nativeOperations;\n  }\n  var NativeOperations = createNativeOperations();\n  var API = {\n    getValue: isSingleOpBatching ? (tag, saveValueCallback) => {\n      if (saveValueCallback) {\n        eventListenerGetValueCallbacks[tag] = saveValueCallback;\n      }\n      NativeOperations.getValue(tag);\n    } : (tag, saveValueCallback) => {\n      NativeOperations.getValue(tag, saveValueCallback);\n    },\n    setWaitingForIdentifier(id) {\n      waitingForQueuedOperations.add(id);\n      queueOperations = true;\n      if (ReactNativeFeatureFlags.animatedShouldDebounceQueueFlush() && flushQueueImmediate) {\n        if (ReactNativeFeatureFlags.enableAnimatedClearImmediateFix()) {\n          clearImmediate(flushQueueImmediate);\n        } else {\n          clearTimeout(flushQueueImmediate);\n        }\n      }\n    },\n    unsetWaitingForIdentifier(id) {\n      waitingForQueuedOperations.delete(id);\n      if (waitingForQueuedOperations.size === 0) {\n        queueOperations = false;\n        API.disableQueue();\n      }\n    },\n    disableQueue() {\n      (0, _invariant.default)(NativeAnimatedModule, 'Native animated module is not available');\n      if (ReactNativeFeatureFlags.animatedShouldDebounceQueueFlush()) {\n        var prevImmediate = flushQueueImmediate;\n        clearImmediate(prevImmediate);\n        flushQueueImmediate = setImmediate(API.flushQueue);\n      } else {\n        API.flushQueue();\n      }\n    },\n    flushQueue: isSingleOpBatching ? () => {\n      (0, _invariant.default)(NativeAnimatedModule, 'Native animated module is not available');\n      flushQueueImmediate = null;\n      if (singleOpQueue.length === 0) {\n        return;\n      }\n      ensureGlobalEventEmitterListeners();\n      NativeAnimatedModule?.queueAndExecuteBatchedOperations?.(singleOpQueue);\n      singleOpQueue.length = 0;\n    } : () => {\n      (0, _invariant.default)(NativeAnimatedModule, 'Native animated module is not available');\n      flushQueueImmediate = null;\n      if (queue.length === 0) {\n        return;\n      }\n      if (_Platform.default.OS === 'android') {\n        NativeAnimatedModule?.startOperationBatch?.();\n      }\n      for (var q = 0, l = queue.length; q < l; q++) {\n        queue[q]();\n      }\n      queue.length = 0;\n      if (_Platform.default.OS === 'android') {\n        NativeAnimatedModule?.finishOperationBatch?.();\n      }\n    },\n    createAnimatedNode(tag, config) {\n      NativeOperations.createAnimatedNode(tag, config);\n    },\n    updateAnimatedNodeConfig(tag, config) {\n      NativeOperations.updateAnimatedNodeConfig?.(tag, config);\n    },\n    startListeningToAnimatedNodeValue(tag) {\n      NativeOperations.startListeningToAnimatedNodeValue(tag);\n    },\n    stopListeningToAnimatedNodeValue(tag) {\n      NativeOperations.stopListeningToAnimatedNodeValue(tag);\n    },\n    connectAnimatedNodes(parentTag, childTag) {\n      NativeOperations.connectAnimatedNodes(parentTag, childTag);\n    },\n    disconnectAnimatedNodes(parentTag, childTag) {\n      NativeOperations.disconnectAnimatedNodes(parentTag, childTag);\n    },\n    startAnimatingNode: isSingleOpBatching ? (animationId, nodeTag, config, endCallback) => {\n      if (endCallback) {\n        eventListenerAnimationFinishedCallbacks[animationId] = endCallback;\n      }\n      NativeOperations.startAnimatingNode(animationId, nodeTag, config);\n    } : (animationId, nodeTag, config, endCallback) => {\n      NativeOperations.startAnimatingNode(animationId, nodeTag, config, endCallback);\n    },\n    stopAnimation(animationId) {\n      NativeOperations.stopAnimation(animationId);\n    },\n    setAnimatedNodeValue(nodeTag, value) {\n      NativeOperations.setAnimatedNodeValue(nodeTag, value);\n    },\n    setAnimatedNodeOffset(nodeTag, offset) {\n      NativeOperations.setAnimatedNodeOffset(nodeTag, offset);\n    },\n    flattenAnimatedNodeOffset(nodeTag) {\n      NativeOperations.flattenAnimatedNodeOffset(nodeTag);\n    },\n    extractAnimatedNodeOffset(nodeTag) {\n      NativeOperations.extractAnimatedNodeOffset(nodeTag);\n    },\n    connectAnimatedNodeToView(nodeTag, viewTag) {\n      NativeOperations.connectAnimatedNodeToView(nodeTag, viewTag);\n    },\n    disconnectAnimatedNodeFromView(nodeTag, viewTag) {\n      NativeOperations.disconnectAnimatedNodeFromView(nodeTag, viewTag);\n    },\n    restoreDefaultValues(nodeTag) {\n      NativeOperations.restoreDefaultValues?.(nodeTag);\n    },\n    dropAnimatedNode(tag) {\n      NativeOperations.dropAnimatedNode(tag);\n    },\n    addAnimatedEventToView(viewTag, eventName, eventMapping) {\n      NativeOperations.addAnimatedEventToView(viewTag, eventName, eventMapping);\n    },\n    removeAnimatedEventFromView(viewTag, eventName, animatedNodeTag) {\n      NativeOperations.removeAnimatedEventFromView(viewTag, eventName, animatedNodeTag);\n    }\n  };\n  function ensureGlobalEventEmitterListeners() {\n    if (globalEventEmitterGetValueListener && globalEventEmitterAnimationFinishedListener) {\n      return;\n    }\n    globalEventEmitterGetValueListener = _RCTDeviceEventEmitter.default.addListener('onNativeAnimatedModuleGetValue', params => {\n      var tag = params.tag;\n      var callback = eventListenerGetValueCallbacks[tag];\n      if (!callback) {\n        return;\n      }\n      callback(params.value);\n      delete eventListenerGetValueCallbacks[tag];\n    });\n    globalEventEmitterAnimationFinishedListener = _RCTDeviceEventEmitter.default.addListener('onNativeAnimatedModuleAnimationFinished', params => {\n      var animations = Array.isArray(params) ? params : [params];\n      for (var animation of animations) {\n        var animationId = animation.animationId;\n        var callback = eventListenerAnimationFinishedCallbacks[animationId];\n        if (callback) {\n          callback(animation);\n          delete eventListenerAnimationFinishedCallbacks[animationId];\n        }\n      }\n    });\n  }\n  function generateNewNodeTag() {\n    return __nativeAnimatedNodeTagCount++;\n  }\n  function generateNewAnimationId() {\n    return __nativeAnimationIdCount++;\n  }\n  function assertNativeAnimatedModule() {\n    (0, _invariant.default)(NativeAnimatedModule, 'Native animated module is not available');\n  }\n  var _warnedMissingNativeAnimated = false;\n  function shouldUseNativeDriver(config) {\n    if (config.useNativeDriver == null) {\n      console.warn('Animated: `useNativeDriver` was not specified. This is a required ' + 'option and must be explicitly set to `true` or `false`');\n    }\n    if (config.useNativeDriver === true && !NativeAnimatedModule) {\n      if (process.env.NODE_ENV !== 'test') {\n        if (!_warnedMissingNativeAnimated) {\n          console.warn('Animated: `useNativeDriver` is not supported because the native ' + 'animated module is missing. Falling back to JS-based animation. To ' + 'resolve this, add `RCTAnimation` module to this app, or remove ' + '`useNativeDriver`. ' + 'Make sure to run `bundle exec pod install` first. Read more about autolinking: https://github.com/react-native-community/cli/blob/master/docs/autolinking.md');\n          _warnedMissingNativeAnimated = true;\n        }\n      }\n      return false;\n    }\n    return config.useNativeDriver || false;\n  }\n  function transformDataType(value) {\n    if (typeof value !== 'string') {\n      return value;\n    }\n    if (value.endsWith('deg')) {\n      var degrees = parseFloat(value) || 0;\n      return degrees * Math.PI / 180.0;\n    } else if (value.endsWith('rad')) {\n      return parseFloat(value) || 0;\n    } else {\n      return value;\n    }\n  }\n  var _default = exports.default = {\n    API,\n    generateNewNodeTag,\n    generateNewAnimationId,\n    assertNativeAnimatedModule,\n    shouldUseNativeDriver,\n    transformDataType,\n    get nativeEventEmitter() {\n      if (!nativeEventEmitter) {\n        nativeEventEmitter = new _NativeEventEmitter.default(_Platform.default.OS !== 'ios' ? null : NativeAnimatedModule);\n      }\n      return nativeEventEmitter;\n    }\n  };\n});", "lineCount": 269, "map": [[7, 2, 22, 0], [7, 6, 22, 0, "_NativeAnimatedModule"], [7, 27, 22, 0], [7, 30, 22, 0, "_interopRequireDefault"], [7, 52, 22, 0], [7, 53, 22, 0, "require"], [7, 60, 22, 0], [7, 61, 22, 0, "_dependencyMap"], [7, 75, 22, 0], [8, 2, 23, 0], [8, 6, 23, 0, "_NativeAnimatedTurboModule"], [8, 32, 23, 0], [8, 35, 23, 0, "_interopRequireDefault"], [8, 57, 23, 0], [8, 58, 23, 0, "require"], [8, 65, 23, 0], [8, 66, 23, 0, "_dependencyMap"], [8, 80, 23, 0], [9, 2, 24, 0], [9, 6, 24, 0, "_NativeEventEmitter"], [9, 25, 24, 0], [9, 28, 24, 0, "_interopRequireDefault"], [9, 50, 24, 0], [9, 51, 24, 0, "require"], [9, 58, 24, 0], [9, 59, 24, 0, "_dependencyMap"], [9, 73, 24, 0], [10, 2, 25, 0], [10, 6, 25, 0, "_RCTDeviceEventEmitter"], [10, 28, 25, 0], [10, 31, 25, 0, "_interopRequireDefault"], [10, 53, 25, 0], [10, 54, 25, 0, "require"], [10, 61, 25, 0], [10, 62, 25, 0, "_dependencyMap"], [10, 76, 25, 0], [11, 2, 26, 0], [11, 6, 26, 0, "_Platform"], [11, 15, 26, 0], [11, 18, 26, 0, "_interopRequireDefault"], [11, 40, 26, 0], [11, 41, 26, 0, "require"], [11, 48, 26, 0], [11, 49, 26, 0, "_dependencyMap"], [11, 63, 26, 0], [12, 2, 27, 0], [12, 6, 27, 0, "ReactNativeFeatureFlags"], [12, 29, 27, 0], [12, 32, 27, 0, "_interopRequireWildcard"], [12, 55, 27, 0], [12, 56, 27, 0, "require"], [12, 63, 27, 0], [12, 64, 27, 0, "_dependencyMap"], [12, 78, 27, 0], [13, 2, 28, 0], [13, 6, 28, 0, "_invariant"], [13, 16, 28, 0], [13, 19, 28, 0, "_interopRequireDefault"], [13, 41, 28, 0], [13, 42, 28, 0, "require"], [13, 49, 28, 0], [13, 50, 28, 0, "_dependencyMap"], [13, 64, 28, 0], [14, 2, 29, 0], [14, 6, 29, 0, "_nullthrows"], [14, 17, 29, 0], [14, 20, 29, 0, "_interopRequireDefault"], [14, 42, 29, 0], [14, 43, 29, 0, "require"], [14, 50, 29, 0], [14, 51, 29, 0, "_dependencyMap"], [14, 65, 29, 0], [15, 2, 29, 36], [15, 11, 29, 36, "_interopRequireWildcard"], [15, 35, 29, 36, "e"], [15, 36, 29, 36], [15, 38, 29, 36, "t"], [15, 39, 29, 36], [15, 68, 29, 36, "WeakMap"], [15, 75, 29, 36], [15, 81, 29, 36, "r"], [15, 82, 29, 36], [15, 89, 29, 36, "WeakMap"], [15, 96, 29, 36], [15, 100, 29, 36, "n"], [15, 101, 29, 36], [15, 108, 29, 36, "WeakMap"], [15, 115, 29, 36], [15, 127, 29, 36, "_interopRequireWildcard"], [15, 150, 29, 36], [15, 162, 29, 36, "_interopRequireWildcard"], [15, 163, 29, 36, "e"], [15, 164, 29, 36], [15, 166, 29, 36, "t"], [15, 167, 29, 36], [15, 176, 29, 36, "t"], [15, 177, 29, 36], [15, 181, 29, 36, "e"], [15, 182, 29, 36], [15, 186, 29, 36, "e"], [15, 187, 29, 36], [15, 188, 29, 36, "__esModule"], [15, 198, 29, 36], [15, 207, 29, 36, "e"], [15, 208, 29, 36], [15, 214, 29, 36, "o"], [15, 215, 29, 36], [15, 217, 29, 36, "i"], [15, 218, 29, 36], [15, 220, 29, 36, "f"], [15, 221, 29, 36], [15, 226, 29, 36, "__proto__"], [15, 235, 29, 36], [15, 243, 29, 36, "default"], [15, 250, 29, 36], [15, 252, 29, 36, "e"], [15, 253, 29, 36], [15, 270, 29, 36, "e"], [15, 271, 29, 36], [15, 294, 29, 36, "e"], [15, 295, 29, 36], [15, 320, 29, 36, "e"], [15, 321, 29, 36], [15, 330, 29, 36, "f"], [15, 331, 29, 36], [15, 337, 29, 36, "o"], [15, 338, 29, 36], [15, 341, 29, 36, "t"], [15, 342, 29, 36], [15, 345, 29, 36, "n"], [15, 346, 29, 36], [15, 349, 29, 36, "r"], [15, 350, 29, 36], [15, 358, 29, 36, "o"], [15, 359, 29, 36], [15, 360, 29, 36, "has"], [15, 363, 29, 36], [15, 364, 29, 36, "e"], [15, 365, 29, 36], [15, 375, 29, 36, "o"], [15, 376, 29, 36], [15, 377, 29, 36, "get"], [15, 380, 29, 36], [15, 381, 29, 36, "e"], [15, 382, 29, 36], [15, 385, 29, 36, "o"], [15, 386, 29, 36], [15, 387, 29, 36, "set"], [15, 390, 29, 36], [15, 391, 29, 36, "e"], [15, 392, 29, 36], [15, 394, 29, 36, "f"], [15, 395, 29, 36], [15, 409, 29, 36, "_t"], [15, 411, 29, 36], [15, 415, 29, 36, "e"], [15, 416, 29, 36], [15, 432, 29, 36, "_t"], [15, 434, 29, 36], [15, 441, 29, 36, "hasOwnProperty"], [15, 455, 29, 36], [15, 456, 29, 36, "call"], [15, 460, 29, 36], [15, 461, 29, 36, "e"], [15, 462, 29, 36], [15, 464, 29, 36, "_t"], [15, 466, 29, 36], [15, 473, 29, 36, "i"], [15, 474, 29, 36], [15, 478, 29, 36, "o"], [15, 479, 29, 36], [15, 482, 29, 36, "Object"], [15, 488, 29, 36], [15, 489, 29, 36, "defineProperty"], [15, 503, 29, 36], [15, 508, 29, 36, "Object"], [15, 514, 29, 36], [15, 515, 29, 36, "getOwnPropertyDescriptor"], [15, 539, 29, 36], [15, 540, 29, 36, "e"], [15, 541, 29, 36], [15, 543, 29, 36, "_t"], [15, 545, 29, 36], [15, 552, 29, 36, "i"], [15, 553, 29, 36], [15, 554, 29, 36, "get"], [15, 557, 29, 36], [15, 561, 29, 36, "i"], [15, 562, 29, 36], [15, 563, 29, 36, "set"], [15, 566, 29, 36], [15, 570, 29, 36, "o"], [15, 571, 29, 36], [15, 572, 29, 36, "f"], [15, 573, 29, 36], [15, 575, 29, 36, "_t"], [15, 577, 29, 36], [15, 579, 29, 36, "i"], [15, 580, 29, 36], [15, 584, 29, 36, "f"], [15, 585, 29, 36], [15, 586, 29, 36, "_t"], [15, 588, 29, 36], [15, 592, 29, 36, "e"], [15, 593, 29, 36], [15, 594, 29, 36, "_t"], [15, 596, 29, 36], [15, 607, 29, 36, "f"], [15, 608, 29, 36], [15, 613, 29, 36, "e"], [15, 614, 29, 36], [15, 616, 29, 36, "t"], [15, 617, 29, 36], [16, 2, 32, 0], [16, 6, 32, 6, "NativeAnimatedModule"], [16, 26, 32, 60], [16, 29, 33, 2, "NativeAnimatedNonTurboModule"], [16, 58, 33, 30], [16, 62, 33, 34, "NativeAnimatedTurboModule"], [16, 96, 33, 59], [17, 2, 35, 0], [17, 6, 35, 4, "__nativeAnimatedNodeTagCount"], [17, 34, 35, 32], [17, 37, 35, 35], [17, 38, 35, 36], [18, 2, 36, 0], [18, 6, 36, 4, "__nativeAnimationIdCount"], [18, 30, 36, 28], [18, 33, 36, 31], [18, 34, 36, 32], [19, 2, 38, 0], [19, 6, 38, 4, "nativeEventEmitter"], [19, 24, 38, 22], [20, 2, 40, 0], [20, 6, 40, 4, "waitingForQueuedOperations"], [20, 32, 40, 30], [20, 35, 40, 33], [20, 39, 40, 37, "Set"], [20, 42, 40, 40], [20, 43, 40, 49], [20, 44, 40, 50], [21, 2, 41, 0], [21, 6, 41, 4, "queueOperations"], [21, 21, 41, 19], [21, 24, 41, 22], [21, 29, 41, 27], [22, 2, 42, 0], [22, 6, 42, 4, "queue"], [22, 11, 42, 28], [22, 14, 42, 31], [22, 16, 42, 33], [23, 2, 43, 0], [23, 6, 43, 4, "singleOpQueue"], [23, 19, 43, 31], [23, 22, 43, 34], [23, 24, 43, 36], [24, 2, 45, 0], [24, 6, 45, 6, "isSingleOpBatching"], [24, 24, 45, 24], [24, 27, 46, 2, "Platform"], [24, 44, 46, 10], [24, 45, 46, 11, "OS"], [24, 47, 46, 13], [24, 52, 46, 18], [24, 61, 46, 27], [24, 65, 47, 2, "NativeAnimatedModule"], [24, 85, 47, 22], [24, 87, 47, 24, "queueAndExecuteBatchedOperations"], [24, 119, 47, 56], [24, 123, 47, 60], [24, 127, 47, 64], [24, 131, 48, 2, "ReactNativeFeatureFlags"], [24, 154, 48, 25], [24, 155, 48, 26, "animatedShouldUseSingleOp"], [24, 180, 48, 51], [24, 181, 48, 52], [24, 182, 48, 53], [25, 2, 49, 0], [25, 6, 49, 4, "flushQueueImmediate"], [25, 25, 49, 23], [25, 28, 49, 26], [25, 32, 49, 30], [26, 2, 51, 0], [26, 6, 51, 6, "eventListenerGetValueCallbacks"], [26, 36, 53, 1], [26, 39, 53, 4], [26, 40, 53, 5], [26, 41, 53, 6], [27, 2, 54, 0], [27, 6, 54, 6, "eventListenerAnimationFinishedCallbacks"], [27, 45, 56, 1], [27, 48, 56, 4], [27, 49, 56, 5], [27, 50, 56, 6], [28, 2, 57, 0], [28, 6, 57, 4, "globalEventEmitterGetValueListener"], [28, 40, 57, 58], [28, 43, 57, 61], [28, 47, 57, 65], [29, 2, 58, 0], [29, 6, 58, 4, "globalEventEmitterAnimationFinishedListener"], [29, 49, 58, 67], [29, 52, 58, 70], [29, 56, 58, 74], [30, 2, 60, 0], [30, 11, 60, 9, "createNativeOperations"], [30, 33, 60, 31, "createNativeOperations"], [30, 34, 60, 31], [30, 36, 60, 78], [31, 4, 61, 2], [31, 8, 61, 8, "methodNames"], [31, 19, 61, 19], [31, 22, 61, 22], [31, 23, 62, 4], [31, 43, 62, 24], [31, 45, 63, 4], [31, 71, 63, 30], [31, 73, 64, 4], [31, 83, 64, 14], [31, 85, 65, 4], [31, 120, 65, 39], [31, 122, 66, 4], [31, 156, 66, 38], [31, 158, 67, 4], [31, 180, 67, 26], [31, 182, 68, 4], [31, 207, 68, 29], [31, 209, 69, 4], [31, 229, 69, 24], [31, 231, 70, 4], [31, 246, 70, 19], [31, 248, 71, 4], [31, 270, 71, 26], [31, 272, 72, 4], [31, 295, 72, 27], [31, 297, 73, 4], [31, 324, 73, 31], [31, 326, 74, 4], [31, 353, 74, 31], [31, 355, 75, 4], [31, 382, 75, 31], [31, 384, 76, 4], [31, 416, 76, 36], [31, 418, 77, 4], [31, 440, 77, 26], [31, 442, 78, 4], [31, 460, 78, 22], [31, 462, 79, 4], [31, 486, 79, 28], [31, 488, 80, 4], [31, 517, 80, 33], [31, 519, 81, 4], [31, 532, 81, 17], [31, 534, 82, 4], [31, 550, 82, 20], [31, 551, 83, 3], [32, 4, 84, 2], [32, 8, 84, 8, "nativeOperations"], [32, 24, 86, 3], [32, 27, 86, 6], [32, 28, 86, 7], [32, 29, 86, 8], [33, 4, 87, 2], [33, 8, 87, 6, "isSingleOpBatching"], [33, 26, 87, 24], [33, 28, 87, 26], [34, 6, 87, 26], [34, 10, 87, 26, "_loop"], [34, 15, 87, 26], [34, 27, 87, 26, "_loop"], [34, 28, 87, 26], [34, 30, 88, 69], [35, 8, 89, 6], [35, 12, 89, 12, "methodName"], [35, 22, 89, 22], [35, 25, 89, 25, "methodNames"], [35, 36, 89, 36], [35, 37, 89, 37, "ii"], [35, 39, 89, 39], [35, 40, 89, 40], [36, 8, 90, 6], [36, 12, 90, 12, "operationID"], [36, 23, 90, 23], [36, 26, 90, 26, "ii"], [36, 28, 90, 28], [36, 31, 90, 31], [36, 32, 90, 32], [37, 8, 91, 6, "nativeOperations"], [37, 24, 91, 22], [37, 25, 91, 23, "methodName"], [37, 35, 91, 33], [37, 36, 91, 34], [37, 39, 91, 37], [37, 51, 91, 50], [38, 10, 91, 50], [38, 19, 91, 50, "_len"], [38, 23, 91, 50], [38, 26, 91, 50, "arguments"], [38, 35, 91, 50], [38, 36, 91, 50, "length"], [38, 42, 91, 50], [38, 44, 91, 41, "args"], [38, 48, 91, 45], [38, 55, 91, 45, "Array"], [38, 60, 91, 45], [38, 61, 91, 45, "_len"], [38, 65, 91, 45], [38, 68, 91, 45, "_key"], [38, 72, 91, 45], [38, 78, 91, 45, "_key"], [38, 82, 91, 45], [38, 85, 91, 45, "_len"], [38, 89, 91, 45], [38, 91, 91, 45, "_key"], [38, 95, 91, 45], [39, 12, 91, 41, "args"], [39, 16, 91, 45], [39, 17, 91, 45, "_key"], [39, 21, 91, 45], [39, 25, 91, 45, "arguments"], [39, 34, 91, 45], [39, 35, 91, 45, "_key"], [39, 39, 91, 45], [40, 10, 91, 45], [41, 10, 95, 8, "singleOpQueue"], [41, 23, 95, 21], [41, 24, 95, 22, "push"], [41, 28, 95, 26], [41, 29, 95, 27, "operationID"], [41, 40, 95, 38], [41, 42, 95, 40], [41, 45, 95, 43, "args"], [41, 49, 95, 47], [41, 50, 95, 48], [42, 8, 96, 6], [42, 9, 96, 7], [43, 6, 97, 4], [43, 7, 97, 5], [44, 6, 88, 4], [44, 11, 88, 9], [44, 15, 88, 13, "ii"], [44, 17, 88, 15], [44, 20, 88, 18], [44, 21, 88, 19], [44, 23, 88, 21, "length"], [44, 29, 88, 27], [44, 32, 88, 30, "methodNames"], [44, 43, 88, 41], [44, 44, 88, 42, "length"], [44, 50, 88, 48], [44, 52, 88, 50, "ii"], [44, 54, 88, 52], [44, 57, 88, 55, "length"], [44, 63, 88, 61], [44, 65, 88, 63, "ii"], [44, 67, 88, 65], [44, 69, 88, 67], [45, 8, 88, 67, "_loop"], [45, 13, 88, 67], [46, 6, 88, 67], [47, 4, 98, 2], [47, 5, 98, 3], [47, 11, 98, 9], [48, 6, 98, 9], [48, 10, 98, 9, "_loop2"], [48, 16, 98, 9], [48, 28, 98, 9, "_loop2"], [48, 29, 98, 9], [48, 31, 99, 69], [49, 8, 100, 6], [49, 12, 100, 12, "methodName"], [49, 22, 100, 22], [49, 25, 100, 25, "methodNames"], [49, 36, 100, 36], [49, 37, 100, 37, "ii"], [49, 40, 100, 39], [49, 41, 100, 40], [50, 8, 101, 6, "nativeOperations"], [50, 24, 101, 22], [50, 25, 101, 23, "methodName"], [50, 35, 101, 33], [50, 36, 101, 34], [50, 39, 101, 37], [50, 51, 101, 50], [51, 10, 101, 50], [51, 19, 101, 50, "_len2"], [51, 24, 101, 50], [51, 27, 101, 50, "arguments"], [51, 36, 101, 50], [51, 37, 101, 50, "length"], [51, 43, 101, 50], [51, 45, 101, 41, "args"], [51, 49, 101, 45], [51, 56, 101, 45, "Array"], [51, 61, 101, 45], [51, 62, 101, 45, "_len2"], [51, 67, 101, 45], [51, 70, 101, 45, "_key2"], [51, 75, 101, 45], [51, 81, 101, 45, "_key2"], [51, 86, 101, 45], [51, 89, 101, 45, "_len2"], [51, 94, 101, 45], [51, 96, 101, 45, "_key2"], [51, 101, 101, 45], [52, 12, 101, 41, "args"], [52, 16, 101, 45], [52, 17, 101, 45, "_key2"], [52, 22, 101, 45], [52, 26, 101, 45, "arguments"], [52, 35, 101, 45], [52, 36, 101, 45, "_key2"], [52, 41, 101, 45], [53, 10, 101, 45], [54, 10, 102, 8], [54, 14, 102, 14, "method"], [54, 20, 102, 20], [54, 23, 102, 23], [54, 27, 102, 23, "nullthrows"], [54, 46, 102, 33], [54, 48, 102, 34, "NativeAnimatedModule"], [54, 68, 102, 54], [54, 69, 102, 55], [54, 70, 102, 56, "methodName"], [54, 80, 102, 66], [54, 81, 102, 67], [55, 10, 106, 8], [55, 14, 106, 12, "queueOperations"], [55, 29, 106, 27], [55, 33, 106, 31, "queue"], [55, 38, 106, 36], [55, 39, 106, 37, "length"], [55, 45, 106, 43], [55, 50, 106, 48], [55, 51, 106, 49], [55, 53, 106, 51], [56, 12, 108, 10, "queue"], [56, 17, 108, 15], [56, 18, 108, 16, "push"], [56, 22, 108, 20], [56, 23, 108, 21], [56, 29, 108, 27, "method"], [56, 35, 108, 33], [56, 36, 108, 34], [56, 39, 108, 37, "args"], [56, 43, 108, 41], [56, 44, 108, 42], [56, 45, 108, 43], [57, 10, 109, 8], [57, 11, 109, 9], [57, 17, 109, 15], [58, 12, 111, 10, "method"], [58, 18, 111, 16], [58, 19, 111, 17], [58, 22, 111, 20, "args"], [58, 26, 111, 24], [58, 27, 111, 25], [59, 10, 112, 8], [60, 8, 113, 6], [60, 9, 113, 7], [61, 6, 114, 4], [61, 7, 114, 5], [62, 6, 99, 4], [62, 11, 99, 9], [62, 15, 99, 13, "ii"], [62, 18, 99, 15], [62, 21, 99, 18], [62, 22, 99, 19], [62, 24, 99, 21, "length"], [62, 31, 99, 27], [62, 34, 99, 30, "methodNames"], [62, 45, 99, 41], [62, 46, 99, 42, "length"], [62, 52, 99, 48], [62, 54, 99, 50, "ii"], [62, 57, 99, 52], [62, 60, 99, 55, "length"], [62, 67, 99, 61], [62, 69, 99, 63, "ii"], [62, 72, 99, 65], [62, 74, 99, 67], [63, 8, 99, 67, "_loop2"], [63, 14, 99, 67], [64, 6, 99, 67], [65, 4, 115, 2], [66, 4, 117, 2], [66, 11, 117, 9, "nativeOperations"], [66, 27, 117, 25], [67, 2, 118, 0], [68, 2, 120, 0], [68, 6, 120, 6, "NativeOperations"], [68, 22, 120, 22], [68, 25, 120, 25, "createNativeOperations"], [68, 47, 120, 47], [68, 48, 120, 48], [68, 49, 120, 49], [69, 2, 126, 0], [69, 6, 126, 6, "API"], [69, 9, 126, 9], [69, 12, 126, 12], [70, 4, 127, 2, "getValue"], [70, 12, 127, 10], [70, 14, 127, 13, "isSingleOpBatching"], [70, 32, 127, 31], [70, 35, 128, 6], [70, 36, 128, 7, "tag"], [70, 39, 128, 10], [70, 41, 128, 12, "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [70, 58, 128, 29], [70, 63, 128, 34], [71, 6, 129, 8], [71, 10, 129, 12, "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [71, 27, 129, 29], [71, 29, 129, 31], [72, 8, 130, 10, "eventListenerGetValueCallbacks"], [72, 38, 130, 40], [72, 39, 130, 41, "tag"], [72, 42, 130, 44], [72, 43, 130, 45], [72, 46, 130, 48, "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [72, 63, 130, 65], [73, 6, 131, 8], [74, 6, 134, 8, "NativeOperations"], [74, 22, 134, 24], [74, 23, 134, 25, "getValue"], [74, 31, 134, 33], [74, 32, 134, 34, "tag"], [74, 35, 134, 37], [74, 36, 134, 38], [75, 4, 135, 6], [75, 5, 135, 7], [75, 8, 136, 6], [75, 9, 136, 7, "tag"], [75, 12, 136, 10], [75, 14, 136, 12, "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [75, 31, 136, 29], [75, 36, 136, 34], [76, 6, 137, 8, "NativeOperations"], [76, 22, 137, 24], [76, 23, 137, 25, "getValue"], [76, 31, 137, 33], [76, 32, 137, 34, "tag"], [76, 35, 137, 37], [76, 37, 137, 39, "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [76, 54, 137, 56], [76, 55, 137, 57], [77, 4, 138, 6], [77, 5, 138, 66], [78, 4, 140, 2, "setWaitingForIdentifier"], [78, 27, 140, 25, "setWaitingForIdentifier"], [78, 28, 140, 26, "id"], [78, 30, 140, 36], [78, 32, 140, 44], [79, 6, 141, 4, "waitingForQueuedOperations"], [79, 32, 141, 30], [79, 33, 141, 31, "add"], [79, 36, 141, 34], [79, 37, 141, 35, "id"], [79, 39, 141, 37], [79, 40, 141, 38], [80, 6, 142, 4, "queueOperations"], [80, 21, 142, 19], [80, 24, 142, 22], [80, 28, 142, 26], [81, 6, 143, 4], [81, 10, 144, 6, "ReactNativeFeatureFlags"], [81, 33, 144, 29], [81, 34, 144, 30, "animatedShouldDebounceQueueFlush"], [81, 66, 144, 62], [81, 67, 144, 63], [81, 68, 144, 64], [81, 72, 145, 6, "flushQueueImmediate"], [81, 91, 145, 25], [81, 93, 146, 6], [82, 8, 147, 6], [82, 12, 147, 10, "ReactNativeFeatureFlags"], [82, 35, 147, 33], [82, 36, 147, 34, "enableAnimatedClearImmediateFix"], [82, 67, 147, 65], [82, 68, 147, 66], [82, 69, 147, 67], [82, 71, 147, 69], [83, 10, 148, 8, "clearImmediate"], [83, 24, 148, 22], [83, 25, 148, 23, "flushQueueImmediate"], [83, 44, 148, 42], [83, 45, 148, 43], [84, 8, 149, 6], [84, 9, 149, 7], [84, 15, 149, 13], [85, 10, 150, 8, "clearTimeout"], [85, 22, 150, 20], [85, 23, 150, 21, "flushQueueImmediate"], [85, 42, 150, 40], [85, 43, 150, 41], [86, 8, 151, 6], [87, 6, 152, 4], [88, 4, 153, 2], [88, 5, 153, 3], [89, 4, 155, 2, "unsetWaitingForIdentifier"], [89, 29, 155, 27, "unsetWaitingForIdentifier"], [89, 30, 155, 28, "id"], [89, 32, 155, 38], [89, 34, 155, 46], [90, 6, 156, 4, "waitingForQueuedOperations"], [90, 32, 156, 30], [90, 33, 156, 31, "delete"], [90, 39, 156, 37], [90, 40, 156, 38, "id"], [90, 42, 156, 40], [90, 43, 156, 41], [91, 6, 158, 4], [91, 10, 158, 8, "waitingForQueuedOperations"], [91, 36, 158, 34], [91, 37, 158, 35, "size"], [91, 41, 158, 39], [91, 46, 158, 44], [91, 47, 158, 45], [91, 49, 158, 47], [92, 8, 159, 6, "queueOperations"], [92, 23, 159, 21], [92, 26, 159, 24], [92, 31, 159, 29], [93, 8, 160, 6, "API"], [93, 11, 160, 9], [93, 12, 160, 10, "disableQueue"], [93, 24, 160, 22], [93, 25, 160, 23], [93, 26, 160, 24], [94, 6, 161, 4], [95, 4, 162, 2], [95, 5, 162, 3], [96, 4, 164, 2, "disableQueue"], [96, 16, 164, 14, "disableQueue"], [96, 17, 164, 14], [96, 19, 164, 23], [97, 6, 165, 4], [97, 10, 165, 4, "invariant"], [97, 28, 165, 13], [97, 30, 165, 14, "NativeAnimatedModule"], [97, 50, 165, 34], [97, 52, 165, 36], [97, 93, 165, 77], [97, 94, 165, 78], [98, 6, 167, 4], [98, 10, 167, 8, "ReactNativeFeatureFlags"], [98, 33, 167, 31], [98, 34, 167, 32, "animatedShouldDebounceQueueFlush"], [98, 66, 167, 64], [98, 67, 167, 65], [98, 68, 167, 66], [98, 70, 167, 68], [99, 8, 168, 6], [99, 12, 168, 12, "prevImmediate"], [99, 25, 168, 25], [99, 28, 168, 28, "flushQueueImmediate"], [99, 47, 168, 47], [100, 8, 169, 6, "clearImmediate"], [100, 22, 169, 20], [100, 23, 169, 21, "prevImmediate"], [100, 36, 169, 34], [100, 37, 169, 35], [101, 8, 170, 6, "flushQueueImmediate"], [101, 27, 170, 25], [101, 30, 170, 28, "setImmediate"], [101, 42, 170, 40], [101, 43, 170, 41, "API"], [101, 46, 170, 44], [101, 47, 170, 45, "flushQueue"], [101, 57, 170, 55], [101, 58, 170, 56], [102, 6, 171, 4], [102, 7, 171, 5], [102, 13, 171, 11], [103, 8, 172, 6, "API"], [103, 11, 172, 9], [103, 12, 172, 10, "flushQueue"], [103, 22, 172, 20], [103, 23, 172, 21], [103, 24, 172, 22], [104, 6, 173, 4], [105, 4, 174, 2], [105, 5, 174, 3], [106, 4, 176, 2, "flushQueue"], [106, 14, 176, 12], [106, 16, 176, 15, "isSingleOpBatching"], [106, 34, 176, 33], [106, 37, 177, 6], [106, 43, 177, 18], [107, 6, 178, 8], [107, 10, 178, 8, "invariant"], [107, 28, 178, 17], [107, 30, 179, 10, "NativeAnimatedModule"], [107, 50, 179, 30], [107, 52, 180, 10], [107, 93, 181, 8], [107, 94, 181, 9], [108, 6, 182, 8, "flushQueueImmediate"], [108, 25, 182, 27], [108, 28, 182, 30], [108, 32, 182, 34], [109, 6, 184, 8], [109, 10, 184, 12, "singleOpQueue"], [109, 23, 184, 25], [109, 24, 184, 26, "length"], [109, 30, 184, 32], [109, 35, 184, 37], [109, 36, 184, 38], [109, 38, 184, 40], [110, 8, 185, 10], [111, 6, 186, 8], [112, 6, 189, 8, "ensureGlobalEventEmitterListeners"], [112, 39, 189, 41], [112, 40, 189, 42], [112, 41, 189, 43], [113, 6, 195, 8, "NativeAnimatedModule"], [113, 26, 195, 28], [113, 28, 195, 30, "queueAndExecuteBatchedOperations"], [113, 60, 195, 62], [113, 63, 195, 65, "singleOpQueue"], [113, 76, 195, 78], [113, 77, 195, 79], [114, 6, 196, 8, "singleOpQueue"], [114, 19, 196, 21], [114, 20, 196, 22, "length"], [114, 26, 196, 28], [114, 29, 196, 31], [114, 30, 196, 32], [115, 4, 197, 6], [115, 5, 197, 7], [115, 8, 198, 6], [115, 14, 198, 18], [116, 6, 199, 8], [116, 10, 199, 8, "invariant"], [116, 28, 199, 17], [116, 30, 200, 10, "NativeAnimatedModule"], [116, 50, 200, 30], [116, 52, 201, 10], [116, 93, 202, 8], [116, 94, 202, 9], [117, 6, 203, 8, "flushQueueImmediate"], [117, 25, 203, 27], [117, 28, 203, 30], [117, 32, 203, 34], [118, 6, 205, 8], [118, 10, 205, 12, "queue"], [118, 15, 205, 17], [118, 16, 205, 18, "length"], [118, 22, 205, 24], [118, 27, 205, 29], [118, 28, 205, 30], [118, 30, 205, 32], [119, 8, 206, 10], [120, 6, 207, 8], [121, 6, 209, 8], [121, 10, 209, 12, "Platform"], [121, 27, 209, 20], [121, 28, 209, 21, "OS"], [121, 30, 209, 23], [121, 35, 209, 28], [121, 44, 209, 37], [121, 46, 209, 39], [122, 8, 210, 10, "NativeAnimatedModule"], [122, 28, 210, 30], [122, 30, 210, 32, "startOperationBatch"], [122, 49, 210, 51], [122, 52, 210, 54], [122, 53, 210, 55], [123, 6, 211, 8], [124, 6, 213, 8], [124, 11, 213, 13], [124, 15, 213, 17, "q"], [124, 16, 213, 18], [124, 19, 213, 21], [124, 20, 213, 22], [124, 22, 213, 24, "l"], [124, 23, 213, 25], [124, 26, 213, 28, "queue"], [124, 31, 213, 33], [124, 32, 213, 34, "length"], [124, 38, 213, 40], [124, 40, 213, 42, "q"], [124, 41, 213, 43], [124, 44, 213, 46, "l"], [124, 45, 213, 47], [124, 47, 213, 49, "q"], [124, 48, 213, 50], [124, 50, 213, 52], [124, 52, 213, 54], [125, 8, 214, 10, "queue"], [125, 13, 214, 15], [125, 14, 214, 16, "q"], [125, 15, 214, 17], [125, 16, 214, 18], [125, 17, 214, 19], [125, 18, 214, 20], [126, 6, 215, 8], [127, 6, 216, 8, "queue"], [127, 11, 216, 13], [127, 12, 216, 14, "length"], [127, 18, 216, 20], [127, 21, 216, 23], [127, 22, 216, 24], [128, 6, 218, 8], [128, 10, 218, 12, "Platform"], [128, 27, 218, 20], [128, 28, 218, 21, "OS"], [128, 30, 218, 23], [128, 35, 218, 28], [128, 44, 218, 37], [128, 46, 218, 39], [129, 8, 219, 10, "NativeAnimatedModule"], [129, 28, 219, 30], [129, 30, 219, 32, "finishOperationBatch"], [129, 50, 219, 52], [129, 53, 219, 55], [129, 54, 219, 56], [130, 6, 220, 8], [131, 4, 221, 6], [131, 5, 221, 22], [132, 4, 223, 2, "createAnimatedNode"], [132, 22, 223, 20, "createAnimatedNode"], [132, 23, 223, 21, "tag"], [132, 26, 223, 32], [132, 28, 223, 34, "config"], [132, 34, 223, 60], [132, 36, 223, 68], [133, 6, 224, 4, "NativeOperations"], [133, 22, 224, 20], [133, 23, 224, 21, "createAnimatedNode"], [133, 41, 224, 39], [133, 42, 224, 40, "tag"], [133, 45, 224, 43], [133, 47, 224, 45, "config"], [133, 53, 224, 51], [133, 54, 224, 52], [134, 4, 225, 2], [134, 5, 225, 3], [135, 4, 227, 2, "updateAnimatedNodeConfig"], [135, 28, 227, 26, "updateAnimatedNodeConfig"], [135, 29, 227, 27, "tag"], [135, 32, 227, 38], [135, 34, 227, 40, "config"], [135, 40, 227, 66], [135, 42, 227, 74], [136, 6, 228, 4, "NativeOperations"], [136, 22, 228, 20], [136, 23, 228, 21, "updateAnimatedNodeConfig"], [136, 47, 228, 45], [136, 50, 228, 48, "tag"], [136, 53, 228, 51], [136, 55, 228, 53, "config"], [136, 61, 228, 59], [136, 62, 228, 60], [137, 4, 229, 2], [137, 5, 229, 3], [138, 4, 231, 2, "startListeningToAnimatedNodeValue"], [138, 37, 231, 35, "startListeningToAnimatedNodeValue"], [138, 38, 231, 36, "tag"], [138, 41, 231, 47], [138, 43, 231, 55], [139, 6, 232, 4, "NativeOperations"], [139, 22, 232, 20], [139, 23, 232, 21, "startListeningToAnimatedNodeValue"], [139, 56, 232, 54], [139, 57, 232, 55, "tag"], [139, 60, 232, 58], [139, 61, 232, 59], [140, 4, 233, 2], [140, 5, 233, 3], [141, 4, 235, 2, "stopListeningToAnimatedNodeValue"], [141, 36, 235, 34, "stopListeningToAnimatedNodeValue"], [141, 37, 235, 35, "tag"], [141, 40, 235, 46], [141, 42, 235, 54], [142, 6, 236, 4, "NativeOperations"], [142, 22, 236, 20], [142, 23, 236, 21, "stopListeningToAnimatedNodeValue"], [142, 55, 236, 53], [142, 56, 236, 54, "tag"], [142, 59, 236, 57], [142, 60, 236, 58], [143, 4, 237, 2], [143, 5, 237, 3], [144, 4, 239, 2, "connectAnimatedNodes"], [144, 24, 239, 22, "connectAnimatedNodes"], [144, 25, 239, 23, "parentTag"], [144, 34, 239, 40], [144, 36, 239, 42, "childTag"], [144, 44, 239, 58], [144, 46, 239, 66], [145, 6, 240, 4, "NativeOperations"], [145, 22, 240, 20], [145, 23, 240, 21, "connectAnimatedNodes"], [145, 43, 240, 41], [145, 44, 240, 42, "parentTag"], [145, 53, 240, 51], [145, 55, 240, 53, "childTag"], [145, 63, 240, 61], [145, 64, 240, 62], [146, 4, 241, 2], [146, 5, 241, 3], [147, 4, 243, 2, "disconnectAnimatedNodes"], [147, 27, 243, 25, "disconnectAnimatedNodes"], [147, 28, 243, 26, "parentTag"], [147, 37, 243, 43], [147, 39, 243, 45, "childTag"], [147, 47, 243, 61], [147, 49, 243, 69], [148, 6, 244, 4, "NativeOperations"], [148, 22, 244, 20], [148, 23, 244, 21, "disconnectAnimatedNodes"], [148, 46, 244, 44], [148, 47, 244, 45, "parentTag"], [148, 56, 244, 54], [148, 58, 244, 56, "childTag"], [148, 66, 244, 64], [148, 67, 244, 65], [149, 4, 245, 2], [149, 5, 245, 3], [150, 4, 247, 2, "startAnimatingNode"], [150, 22, 247, 20], [150, 24, 247, 23, "isSingleOpBatching"], [150, 42, 247, 41], [150, 45, 248, 6], [150, 46, 248, 7, "animationId"], [150, 57, 248, 18], [150, 59, 248, 20, "nodeTag"], [150, 66, 248, 27], [150, 68, 248, 29, "config"], [150, 74, 248, 35], [150, 76, 248, 37, "endCallback"], [150, 87, 248, 48], [150, 92, 248, 53], [151, 6, 249, 8], [151, 10, 249, 12, "endCallback"], [151, 21, 249, 23], [151, 23, 249, 25], [152, 8, 250, 10, "eventListenerAnimationFinishedCallbacks"], [152, 47, 250, 49], [152, 48, 250, 50, "animationId"], [152, 59, 250, 61], [152, 60, 250, 62], [152, 63, 250, 65, "endCallback"], [152, 74, 250, 76], [153, 6, 251, 8], [154, 6, 254, 8, "NativeOperations"], [154, 22, 254, 24], [154, 23, 254, 25, "startAnimatingNode"], [154, 41, 254, 43], [154, 42, 254, 44, "animationId"], [154, 53, 254, 55], [154, 55, 254, 57, "nodeTag"], [154, 62, 254, 64], [154, 64, 254, 66, "config"], [154, 70, 254, 72], [154, 71, 254, 73], [155, 4, 255, 6], [155, 5, 255, 7], [155, 8, 256, 6], [155, 9, 256, 7, "animationId"], [155, 20, 256, 18], [155, 22, 256, 20, "nodeTag"], [155, 29, 256, 27], [155, 31, 256, 29, "config"], [155, 37, 256, 35], [155, 39, 256, 37, "endCallback"], [155, 50, 256, 48], [155, 55, 256, 53], [156, 6, 257, 8, "NativeOperations"], [156, 22, 257, 24], [156, 23, 257, 25, "startAnimatingNode"], [156, 41, 257, 43], [156, 42, 258, 10, "animationId"], [156, 53, 258, 21], [156, 55, 259, 10, "nodeTag"], [156, 62, 259, 17], [156, 64, 260, 10, "config"], [156, 70, 260, 16], [156, 72, 261, 10, "endCallback"], [156, 83, 262, 8], [156, 84, 262, 9], [157, 4, 263, 6], [157, 5, 263, 76], [158, 4, 265, 2, "stopAnimation"], [158, 17, 265, 15, "stopAnimation"], [158, 18, 265, 16, "animationId"], [158, 29, 265, 35], [158, 31, 265, 37], [159, 6, 266, 4, "NativeOperations"], [159, 22, 266, 20], [159, 23, 266, 21, "stopAnimation"], [159, 36, 266, 34], [159, 37, 266, 35, "animationId"], [159, 48, 266, 46], [159, 49, 266, 47], [160, 4, 267, 2], [160, 5, 267, 3], [161, 4, 269, 2, "setAnimatedNodeValue"], [161, 24, 269, 22, "setAnimatedNodeValue"], [161, 25, 269, 23, "nodeTag"], [161, 32, 269, 38], [161, 34, 269, 40, "value"], [161, 39, 269, 53], [161, 41, 269, 61], [162, 6, 270, 4, "NativeOperations"], [162, 22, 270, 20], [162, 23, 270, 21, "setAnimatedNodeValue"], [162, 43, 270, 41], [162, 44, 270, 42, "nodeTag"], [162, 51, 270, 49], [162, 53, 270, 51, "value"], [162, 58, 270, 56], [162, 59, 270, 57], [163, 4, 271, 2], [163, 5, 271, 3], [164, 4, 273, 2, "setAnimatedNodeOffset"], [164, 25, 273, 23, "setAnimatedNodeOffset"], [164, 26, 273, 24, "nodeTag"], [164, 33, 273, 39], [164, 35, 273, 41, "offset"], [164, 41, 273, 55], [164, 43, 273, 63], [165, 6, 274, 4, "NativeOperations"], [165, 22, 274, 20], [165, 23, 274, 21, "setAnimatedNodeOffset"], [165, 44, 274, 42], [165, 45, 274, 43, "nodeTag"], [165, 52, 274, 50], [165, 54, 274, 52, "offset"], [165, 60, 274, 58], [165, 61, 274, 59], [166, 4, 275, 2], [166, 5, 275, 3], [167, 4, 277, 2, "flattenAnimatedNodeOffset"], [167, 29, 277, 27, "flattenAnimatedNodeOffset"], [167, 30, 277, 28, "nodeTag"], [167, 37, 277, 43], [167, 39, 277, 51], [168, 6, 278, 4, "NativeOperations"], [168, 22, 278, 20], [168, 23, 278, 21, "flattenAnimatedNodeOffset"], [168, 48, 278, 46], [168, 49, 278, 47, "nodeTag"], [168, 56, 278, 54], [168, 57, 278, 55], [169, 4, 279, 2], [169, 5, 279, 3], [170, 4, 281, 2, "extractAnimatedNodeOffset"], [170, 29, 281, 27, "extractAnimatedNodeOffset"], [170, 30, 281, 28, "nodeTag"], [170, 37, 281, 43], [170, 39, 281, 51], [171, 6, 282, 4, "NativeOperations"], [171, 22, 282, 20], [171, 23, 282, 21, "extractAnimatedNodeOffset"], [171, 48, 282, 46], [171, 49, 282, 47, "nodeTag"], [171, 56, 282, 54], [171, 57, 282, 55], [172, 4, 283, 2], [172, 5, 283, 3], [173, 4, 285, 2, "connectAnimatedNodeToView"], [173, 29, 285, 27, "connectAnimatedNodeToView"], [173, 30, 285, 28, "nodeTag"], [173, 37, 285, 43], [173, 39, 285, 45, "viewTag"], [173, 46, 285, 60], [173, 48, 285, 68], [174, 6, 286, 4, "NativeOperations"], [174, 22, 286, 20], [174, 23, 286, 21, "connectAnimatedNodeToView"], [174, 48, 286, 46], [174, 49, 286, 47, "nodeTag"], [174, 56, 286, 54], [174, 58, 286, 56, "viewTag"], [174, 65, 286, 63], [174, 66, 286, 64], [175, 4, 287, 2], [175, 5, 287, 3], [176, 4, 289, 2, "disconnectAnimatedNodeFromView"], [176, 34, 289, 32, "disconnectAnimatedNodeFromView"], [176, 35, 289, 33, "nodeTag"], [176, 42, 289, 48], [176, 44, 289, 50, "viewTag"], [176, 51, 289, 65], [176, 53, 289, 73], [177, 6, 290, 4, "NativeOperations"], [177, 22, 290, 20], [177, 23, 290, 21, "disconnectAnimatedNodeFromView"], [177, 53, 290, 51], [177, 54, 290, 52, "nodeTag"], [177, 61, 290, 59], [177, 63, 290, 61, "viewTag"], [177, 70, 290, 68], [177, 71, 290, 69], [178, 4, 291, 2], [178, 5, 291, 3], [179, 4, 293, 2, "restoreDefaultValues"], [179, 24, 293, 22, "restoreDefaultValues"], [179, 25, 293, 23, "nodeTag"], [179, 32, 293, 38], [179, 34, 293, 46], [180, 6, 294, 4, "NativeOperations"], [180, 22, 294, 20], [180, 23, 294, 21, "restoreDefaultValues"], [180, 43, 294, 41], [180, 46, 294, 44, "nodeTag"], [180, 53, 294, 51], [180, 54, 294, 52], [181, 4, 295, 2], [181, 5, 295, 3], [182, 4, 297, 2, "dropAnimatedNode"], [182, 20, 297, 18, "dropAnimatedNode"], [182, 21, 297, 19, "tag"], [182, 24, 297, 30], [182, 26, 297, 38], [183, 6, 298, 4, "NativeOperations"], [183, 22, 298, 20], [183, 23, 298, 21, "dropAnimatedNode"], [183, 39, 298, 37], [183, 40, 298, 38, "tag"], [183, 43, 298, 41], [183, 44, 298, 42], [184, 4, 299, 2], [184, 5, 299, 3], [185, 4, 301, 2, "addAnimatedEventToView"], [185, 26, 301, 24, "addAnimatedEventToView"], [185, 27, 302, 4, "viewTag"], [185, 34, 302, 19], [185, 36, 303, 4, "eventName"], [185, 45, 303, 21], [185, 47, 304, 4, "eventMapping"], [185, 59, 304, 30], [185, 61, 305, 4], [186, 6, 306, 4, "NativeOperations"], [186, 22, 306, 20], [186, 23, 306, 21, "addAnimatedEventToView"], [186, 45, 306, 43], [186, 46, 306, 44, "viewTag"], [186, 53, 306, 51], [186, 55, 306, 53, "eventName"], [186, 64, 306, 62], [186, 66, 306, 64, "eventMapping"], [186, 78, 306, 76], [186, 79, 306, 77], [187, 4, 307, 2], [187, 5, 307, 3], [188, 4, 309, 2, "removeAnimatedEventFromView"], [188, 31, 309, 29, "removeAnimatedEventFromView"], [188, 32, 310, 4, "viewTag"], [188, 39, 310, 19], [188, 41, 311, 4, "eventName"], [188, 50, 311, 21], [188, 52, 312, 4, "animatedNodeTag"], [188, 67, 312, 27], [188, 69, 313, 4], [189, 6, 314, 4, "NativeOperations"], [189, 22, 314, 20], [189, 23, 314, 21, "removeAnimatedEventFromView"], [189, 50, 314, 48], [189, 51, 315, 6, "viewTag"], [189, 58, 315, 13], [189, 60, 316, 6, "eventName"], [189, 69, 316, 15], [189, 71, 317, 6, "animatedNodeTag"], [189, 86, 318, 4], [189, 87, 318, 5], [190, 4, 319, 2], [191, 2, 320, 0], [191, 3, 320, 1], [192, 2, 322, 0], [192, 11, 322, 9, "ensureGlobalEventEmitterListeners"], [192, 44, 322, 42, "ensureGlobalEventEmitterListeners"], [192, 45, 322, 42], [192, 47, 322, 45], [193, 4, 323, 2], [193, 8, 324, 4, "globalEventEmitterGetValueListener"], [193, 42, 324, 38], [193, 46, 325, 4, "globalEventEmitterAnimationFinishedListener"], [193, 89, 325, 47], [193, 91, 326, 4], [194, 6, 327, 4], [195, 4, 328, 2], [196, 4, 329, 2, "globalEventEmitterGetValueListener"], [196, 38, 329, 36], [196, 41, 329, 39, "RCTDeviceEventEmitter"], [196, 71, 329, 60], [196, 72, 329, 61, "addListener"], [196, 83, 329, 72], [196, 84, 330, 4], [196, 116, 330, 36], [196, 118, 331, 4, "params"], [196, 124, 331, 10], [196, 128, 331, 14], [197, 6, 332, 6], [197, 10, 332, 13, "tag"], [197, 13, 332, 16], [197, 16, 332, 20, "params"], [197, 22, 332, 26], [197, 23, 332, 13, "tag"], [197, 26, 332, 16], [198, 6, 333, 6], [198, 10, 333, 12, "callback"], [198, 18, 333, 20], [198, 21, 333, 23, "eventListenerGetValueCallbacks"], [198, 51, 333, 53], [198, 52, 333, 54, "tag"], [198, 55, 333, 57], [198, 56, 333, 58], [199, 6, 334, 6], [199, 10, 334, 10], [199, 11, 334, 11, "callback"], [199, 19, 334, 19], [199, 21, 334, 21], [200, 8, 335, 8], [201, 6, 336, 6], [202, 6, 337, 6, "callback"], [202, 14, 337, 14], [202, 15, 337, 15, "params"], [202, 21, 337, 21], [202, 22, 337, 22, "value"], [202, 27, 337, 27], [202, 28, 337, 28], [203, 6, 338, 6], [203, 13, 338, 13, "eventListenerGetValueCallbacks"], [203, 43, 338, 43], [203, 44, 338, 44, "tag"], [203, 47, 338, 47], [203, 48, 338, 48], [204, 4, 339, 4], [204, 5, 340, 2], [204, 6, 340, 3], [205, 4, 341, 2, "globalEventEmitterAnimationFinishedListener"], [205, 47, 341, 45], [205, 50, 342, 4, "RCTDeviceEventEmitter"], [205, 80, 342, 25], [205, 81, 342, 26, "addListener"], [205, 92, 342, 37], [205, 93, 343, 6], [205, 134, 343, 47], [205, 136, 344, 6, "params"], [205, 142, 344, 12], [205, 146, 344, 16], [206, 6, 346, 8], [206, 10, 346, 14, "animations"], [206, 20, 346, 24], [206, 23, 346, 27, "Array"], [206, 28, 346, 32], [206, 29, 346, 33, "isArray"], [206, 36, 346, 40], [206, 37, 346, 41, "params"], [206, 43, 346, 47], [206, 44, 346, 48], [206, 47, 346, 51, "params"], [206, 53, 346, 57], [206, 56, 346, 60], [206, 57, 346, 61, "params"], [206, 63, 346, 67], [206, 64, 346, 68], [207, 6, 347, 8], [207, 11, 347, 13], [207, 15, 347, 19, "animation"], [207, 24, 347, 28], [207, 28, 347, 32, "animations"], [207, 38, 347, 42], [207, 40, 347, 44], [208, 8, 348, 10], [208, 12, 348, 17, "animationId"], [208, 23, 348, 28], [208, 26, 348, 32, "animation"], [208, 35, 348, 41], [208, 36, 348, 17, "animationId"], [208, 47, 348, 28], [209, 8, 349, 10], [209, 12, 349, 16, "callback"], [209, 20, 349, 24], [209, 23, 349, 27, "eventListenerAnimationFinishedCallbacks"], [209, 62, 349, 66], [209, 63, 349, 67, "animationId"], [209, 74, 349, 78], [209, 75, 349, 79], [210, 8, 350, 10], [210, 12, 350, 14, "callback"], [210, 20, 350, 22], [210, 22, 350, 24], [211, 10, 351, 12, "callback"], [211, 18, 351, 20], [211, 19, 351, 21, "animation"], [211, 28, 351, 30], [211, 29, 351, 31], [212, 10, 352, 12], [212, 17, 352, 19, "eventListenerAnimationFinishedCallbacks"], [212, 56, 352, 58], [212, 57, 352, 59, "animationId"], [212, 68, 352, 70], [212, 69, 352, 71], [213, 8, 353, 10], [214, 6, 354, 8], [215, 4, 355, 6], [215, 5, 356, 4], [215, 6, 356, 5], [216, 2, 357, 0], [217, 2, 359, 0], [217, 11, 359, 9, "generateNewNodeTag"], [217, 29, 359, 27, "generateNewNodeTag"], [217, 30, 359, 27], [217, 32, 359, 38], [218, 4, 360, 2], [218, 11, 360, 9, "__nativeAnimatedNodeTagCount"], [218, 39, 360, 37], [218, 41, 360, 39], [219, 2, 361, 0], [220, 2, 363, 0], [220, 11, 363, 9, "generateNewAnimationId"], [220, 33, 363, 31, "generateNewAnimationId"], [220, 34, 363, 31], [220, 36, 363, 42], [221, 4, 364, 2], [221, 11, 364, 9, "__nativeAnimationIdCount"], [221, 35, 364, 33], [221, 37, 364, 35], [222, 2, 365, 0], [223, 2, 367, 0], [223, 11, 367, 9, "assertNativeAnimatedModule"], [223, 37, 367, 35, "assertNativeAnimatedModule"], [223, 38, 367, 35], [223, 40, 367, 44], [224, 4, 368, 2], [224, 8, 368, 2, "invariant"], [224, 26, 368, 11], [224, 28, 368, 12, "NativeAnimatedModule"], [224, 48, 368, 32], [224, 50, 368, 34], [224, 91, 368, 75], [224, 92, 368, 76], [225, 2, 369, 0], [226, 2, 371, 0], [226, 6, 371, 4, "_warnedMissingNativeAnimated"], [226, 34, 371, 32], [226, 37, 371, 35], [226, 42, 371, 40], [227, 2, 373, 0], [227, 11, 373, 9, "shouldUseNativeDriver"], [227, 32, 373, 30, "shouldUseNativeDriver"], [227, 33, 374, 2, "config"], [227, 39, 374, 60], [227, 41, 375, 11], [228, 4, 376, 2], [228, 8, 376, 6, "config"], [228, 14, 376, 12], [228, 15, 376, 13, "useNativeDriver"], [228, 30, 376, 28], [228, 34, 376, 32], [228, 38, 376, 36], [228, 40, 376, 38], [229, 6, 377, 4, "console"], [229, 13, 377, 11], [229, 14, 377, 12, "warn"], [229, 18, 377, 16], [229, 19, 378, 6], [229, 87, 378, 74], [229, 90, 379, 8], [229, 146, 380, 4], [229, 147, 380, 5], [230, 4, 381, 2], [231, 4, 383, 2], [231, 8, 383, 6, "config"], [231, 14, 383, 12], [231, 15, 383, 13, "useNativeDriver"], [231, 30, 383, 28], [231, 35, 383, 33], [231, 39, 383, 37], [231, 43, 383, 41], [231, 44, 383, 42, "NativeAnimatedModule"], [231, 64, 383, 62], [231, 66, 383, 64], [232, 6, 384, 4], [232, 10, 384, 8, "process"], [232, 17, 384, 15], [232, 18, 384, 16, "env"], [232, 21, 384, 19], [232, 22, 384, 20, "NODE_ENV"], [232, 30, 384, 28], [232, 35, 384, 33], [232, 41, 384, 39], [232, 43, 384, 41], [233, 8, 385, 6], [233, 12, 385, 10], [233, 13, 385, 11, "_warnedMissingNativeAnimated"], [233, 41, 385, 39], [233, 43, 385, 41], [234, 10, 386, 8, "console"], [234, 17, 386, 15], [234, 18, 386, 16, "warn"], [234, 22, 386, 20], [234, 23, 387, 10], [234, 89, 387, 76], [234, 92, 388, 12], [234, 161, 388, 81], [234, 164, 389, 12], [234, 229, 389, 77], [234, 232, 390, 12], [234, 253, 390, 33], [234, 256, 391, 12], [234, 414, 392, 8], [234, 415, 392, 9], [235, 10, 393, 8, "_warnedMissingNativeAnimated"], [235, 38, 393, 36], [235, 41, 393, 39], [235, 45, 393, 43], [236, 8, 394, 6], [237, 6, 395, 4], [238, 6, 396, 4], [238, 13, 396, 11], [238, 18, 396, 16], [239, 4, 397, 2], [240, 4, 399, 2], [240, 11, 399, 9, "config"], [240, 17, 399, 15], [240, 18, 399, 16, "useNativeDriver"], [240, 33, 399, 31], [240, 37, 399, 35], [240, 42, 399, 40], [241, 2, 400, 0], [242, 2, 402, 0], [242, 11, 402, 9, "transformDataType"], [242, 28, 402, 26, "transformDataType"], [242, 29, 402, 27, "value"], [242, 34, 402, 49], [242, 36, 402, 68], [243, 4, 405, 2], [243, 8, 405, 6], [243, 15, 405, 13, "value"], [243, 20, 405, 18], [243, 25, 405, 23], [243, 33, 405, 31], [243, 35, 405, 33], [244, 6, 406, 4], [244, 13, 406, 11, "value"], [244, 18, 406, 16], [245, 4, 407, 2], [246, 4, 410, 2], [246, 8, 410, 6, "value"], [246, 13, 410, 11], [246, 14, 410, 12, "endsWith"], [246, 22, 410, 20], [246, 23, 410, 21], [246, 28, 410, 26], [246, 29, 410, 27], [246, 31, 410, 29], [247, 6, 411, 4], [247, 10, 411, 10, "degrees"], [247, 17, 411, 17], [247, 20, 411, 20, "parseFloat"], [247, 30, 411, 30], [247, 31, 411, 31, "value"], [247, 36, 411, 36], [247, 37, 411, 37], [247, 41, 411, 41], [247, 42, 411, 42], [248, 6, 412, 4], [248, 13, 412, 12, "degrees"], [248, 20, 412, 19], [248, 23, 412, 22, "Math"], [248, 27, 412, 26], [248, 28, 412, 27, "PI"], [248, 30, 412, 29], [248, 33, 412, 33], [248, 38, 412, 38], [249, 4, 413, 2], [249, 5, 413, 3], [249, 11, 413, 9], [249, 15, 413, 13, "value"], [249, 20, 413, 18], [249, 21, 413, 19, "endsWith"], [249, 29, 413, 27], [249, 30, 413, 28], [249, 35, 413, 33], [249, 36, 413, 34], [249, 38, 413, 36], [250, 6, 414, 4], [250, 13, 414, 11, "parseFloat"], [250, 23, 414, 21], [250, 24, 414, 22, "value"], [250, 29, 414, 27], [250, 30, 414, 28], [250, 34, 414, 32], [250, 35, 414, 33], [251, 4, 415, 2], [251, 5, 415, 3], [251, 11, 415, 9], [252, 6, 416, 4], [252, 13, 416, 11, "value"], [252, 18, 416, 16], [253, 4, 417, 2], [254, 2, 418, 0], [255, 2, 418, 1], [255, 6, 418, 1, "_default"], [255, 14, 418, 1], [255, 17, 418, 1, "exports"], [255, 24, 418, 1], [255, 25, 418, 1, "default"], [255, 32, 418, 1], [255, 35, 420, 15], [256, 4, 421, 2, "API"], [256, 7, 421, 5], [257, 4, 422, 2, "generateNewNodeTag"], [257, 22, 422, 20], [258, 4, 423, 2, "generateNewAnimationId"], [258, 26, 423, 24], [259, 4, 424, 2, "assertNativeAnimatedModule"], [259, 30, 424, 28], [260, 4, 425, 2, "shouldUseNativeDriver"], [260, 25, 425, 23], [261, 4, 426, 2, "transformDataType"], [261, 21, 426, 19], [262, 4, 429, 2], [262, 8, 429, 6, "nativeEventEmitter"], [262, 26, 429, 24, "nativeEventEmitter"], [262, 27, 429, 24], [262, 29, 429, 47], [263, 6, 430, 4], [263, 10, 430, 8], [263, 11, 430, 9, "nativeEventEmitter"], [263, 29, 430, 27], [263, 31, 430, 29], [264, 8, 432, 6, "nativeEventEmitter"], [264, 26, 432, 24], [264, 29, 432, 27], [264, 33, 432, 31, "NativeEventEmitter"], [264, 60, 432, 49], [264, 61, 435, 8, "Platform"], [264, 78, 435, 16], [264, 79, 435, 17, "OS"], [264, 81, 435, 19], [264, 86, 435, 24], [264, 91, 435, 29], [264, 94, 435, 32], [264, 98, 435, 36], [264, 101, 435, 39, "NativeAnimatedModule"], [264, 121, 436, 6], [264, 122, 436, 7], [265, 6, 437, 4], [266, 6, 438, 4], [266, 13, 438, 11, "nativeEventEmitter"], [266, 31, 438, 29], [267, 4, 439, 2], [268, 2, 440, 0], [268, 3, 440, 1], [269, 0, 440, 1], [269, 3]], "functionMap": {"names": ["<global>", "createNativeOperations", "nativeOperations.methodName", "queue.push$argument_0", "<anonymous>", "API.setWaitingForIdentifier", "API.unsetWaitingForIdentifier", "API.disableQueue", "API.createAnimatedNode", "API.updateAnimatedNodeConfig", "API.startListeningToAnimatedNodeValue", "API.stopListeningToAnimatedNodeValue", "API.connectAnimatedNodes", "API.disconnectAnimatedNodes", "API.stopAnimation", "API.setAnimatedNodeValue", "API.setAnimatedNodeOffset", "API.flattenAnimatedNodeOffset", "API.extractAnimatedNodeOffset", "API.connectAnimatedNodeToView", "API.disconnectAnimatedNodeFromView", "API.restoreDefaultValues", "API.dropAnimatedNode", "API.addAnimatedEventToView", "API.removeAnimatedEventFromView", "ensureGlobalEventEmitterListeners", "RCTDeviceEventEmitter.addListener$argument_1", "generateNewNodeTag", "generateNewAnimationId", "assertNativeAnimatedModule", "shouldUseNativeDriver", "transformDataType", "default.get__nativeEventEmitter"], "mappings": "AAA;AC2D;qCC+B;ODK;qCCK;qBCO,qBD;ODK;CDK;MIU;OJO;MIC;OJE;EKE;GLa;EME;GNO;EOE;GPU;MIG;OJoB;MIC;OJuB;EQE;GRE;ESE;GTE;EUE;GVE;EWE;GXE;EYE;GZE;EaE;GbE;MIG;OJO;MIC;OJO;EcE;GdE;EeE;GfE;EgBE;GhBE;EiBE;GjBE;EkBE;GlBE;EmBE;GnBE;EoBE;GpBE;EqBE;GrBE;EsBE;GtBE;EuBE;GvBM;EwBE;GxBU;AyBG;ICS;KDQ;MCK;ODW;CzBE;A2BE;C3BE;A4BE;C5BE;A6BE;C7BE;A8BI;C9B2B;A+BE;C/BgB;EgCW;GhCU"}}, "type": "js/module"}]}