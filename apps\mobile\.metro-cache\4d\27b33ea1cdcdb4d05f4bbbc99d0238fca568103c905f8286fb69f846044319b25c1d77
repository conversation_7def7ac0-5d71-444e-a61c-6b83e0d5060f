{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  function _readOnlyError(r) {\n    throw new TypeError('\"' + r + '\" is read-only');\n  }\n  module.exports = _readOnlyError, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 6, "map": [[2, 2, 1, 0], [2, 11, 1, 9, "_readOnly<PERSON><PERSON>r"], [2, 25, 1, 23, "_readOnly<PERSON><PERSON>r"], [2, 26, 1, 24, "r"], [2, 27, 1, 25], [2, 29, 1, 27], [3, 4, 2, 2], [3, 10, 2, 8], [3, 14, 2, 12, "TypeError"], [3, 23, 2, 21], [3, 24, 2, 22], [3, 27, 2, 25], [3, 30, 2, 28, "r"], [3, 31, 2, 29], [3, 34, 2, 32], [3, 50, 2, 48], [3, 51, 2, 49], [4, 2, 3, 0], [5, 2, 4, 0, "module"], [5, 8, 4, 6], [5, 9, 4, 7, "exports"], [5, 16, 4, 14], [5, 19, 4, 17, "_readOnly<PERSON><PERSON>r"], [5, 33, 4, 31], [5, 35, 4, 33, "module"], [5, 41, 4, 39], [5, 42, 4, 40, "exports"], [5, 49, 4, 47], [5, 50, 4, 48, "__esModule"], [5, 60, 4, 58], [5, 63, 4, 61], [5, 67, 4, 65], [5, 69, 4, 67, "module"], [5, 75, 4, 73], [5, 76, 4, 74, "exports"], [5, 83, 4, 81], [5, 84, 4, 82], [5, 93, 4, 91], [5, 94, 4, 92], [5, 97, 4, 95, "module"], [5, 103, 4, 101], [5, 104, 4, 102, "exports"], [5, 111, 4, 109], [6, 0, 4, 110], [6, 3]], "functionMap": {"names": ["_readOnly<PERSON><PERSON>r", "<global>"], "mappings": "AAA;CCE"}}, "type": "js/module"}]}