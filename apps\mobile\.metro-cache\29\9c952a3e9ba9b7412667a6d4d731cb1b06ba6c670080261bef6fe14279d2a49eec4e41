{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Domain = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _listeners = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"listeners\");\n  var EventScope = /*#__PURE__*/function () {\n    function EventScope() {\n      (0, _classCallCheck2.default)(this, EventScope);\n      Object.defineProperty(this, _listeners, {\n        writable: true,\n        value: new Set()\n      });\n    }\n    return (0, _createClass2.default)(EventScope, [{\n      key: \"addEventListener\",\n      value: function addEventListener(listener) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _listeners)[_listeners].add(listener);\n      }\n    }, {\n      key: \"removeEventListener\",\n      value: function removeEventListener(listener) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _listeners)[_listeners].delete(listener);\n      }\n    }, {\n      key: \"emit\",\n      value: function emit(value) {\n        for (var listener of (0, _classPrivateFieldLooseBase2.default)(this, _listeners)[_listeners]) {\n          listener(value);\n        }\n      }\n    }]);\n  }();\n  var Domain = exports.Domain = /*#__PURE__*/function () {\n    function Domain(name) {\n      (0, _classCallCheck2.default)(this, Domain);\n      if (global[FuseboxReactDevToolsDispatcher.BINDING_NAME] == null) {\n        throw new Error(`Could not create domain ${name}: receiving end doesn't exist`);\n      }\n      this.name = name;\n      this.onMessage = new EventScope();\n    }\n    return (0, _createClass2.default)(Domain, [{\n      key: \"sendMessage\",\n      value: function sendMessage(message) {\n        var messageWithDomain = {\n          domain: this.name,\n          message\n        };\n        var serializedMessageWithDomain = JSON.stringify(messageWithDomain);\n        global[FuseboxReactDevToolsDispatcher.BINDING_NAME](serializedMessageWithDomain);\n      }\n    }]);\n  }();\n  var _domainNameToDomainMap = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"domainNameToDomainMap\");\n  var FuseboxReactDevToolsDispatcher = /*#__PURE__*/function () {\n    function FuseboxReactDevToolsDispatcher() {\n      (0, _classCallCheck2.default)(this, FuseboxReactDevToolsDispatcher);\n    }\n    return (0, _createClass2.default)(FuseboxReactDevToolsDispatcher, null, [{\n      key: \"initializeDomain\",\n      value: function initializeDomain(domainName) {\n        var domain = new Domain(domainName);\n        (0, _classPrivateFieldLooseBase2.default)(this, _domainNameToDomainMap)[_domainNameToDomainMap].set(domainName, domain);\n        this.onDomainInitialization.emit(domain);\n        return domain;\n      }\n    }, {\n      key: \"sendMessage\",\n      value: function sendMessage(domainName, message) {\n        var domain = (0, _classPrivateFieldLooseBase2.default)(this, _domainNameToDomainMap)[_domainNameToDomainMap].get(domainName);\n        if (domain == null) {\n          throw new Error(`Could not send message to ${domainName}: domain doesn't exist`);\n        }\n        try {\n          var parsedMessage = JSON.parse(message);\n          domain.onMessage.emit(parsedMessage);\n        } catch (err) {\n          console.error(`Error while trying to send a message to domain ${domainName}:`, err);\n        }\n      }\n    }]);\n  }();\n  Object.defineProperty(FuseboxReactDevToolsDispatcher, _domainNameToDomainMap, {\n    writable: true,\n    value: new Map()\n  });\n  FuseboxReactDevToolsDispatcher.BINDING_NAME = '__CHROME_DEVTOOLS_FRONTEND_BINDING__';\n  FuseboxReactDevToolsDispatcher.onDomainInitialization = new EventScope();\n  Object.defineProperty(global, '__FUSEBOX_REACT_DEVTOOLS_DISPATCHER__', {\n    value: FuseboxReactDevToolsDispatcher,\n    configurable: false,\n    enumerable: false,\n    writable: false\n  });\n});", "lineCount": 101, "map": [[12, 6, 21, 6, "EventScope"], [12, 16, 21, 16], [13, 4, 21, 16], [13, 13, 21, 16, "EventScope"], [13, 24, 21, 16], [14, 6, 21, 16], [14, 10, 21, 16, "_classCallCheck2"], [14, 26, 21, 16], [14, 27, 21, 16, "default"], [14, 34, 21, 16], [14, 42, 21, 16, "EventScope"], [14, 52, 21, 16], [15, 6, 21, 16, "Object"], [15, 12, 21, 16], [15, 13, 21, 16, "defineProperty"], [15, 27, 21, 16], [15, 34, 21, 16, "_listeners"], [15, 44, 21, 16], [16, 8, 21, 16, "writable"], [16, 16, 21, 16], [17, 8, 21, 16, "value"], [17, 13, 21, 16], [17, 15, 22, 33], [17, 19, 22, 37, "Set"], [17, 22, 22, 40], [17, 23, 22, 41], [18, 6, 22, 42], [19, 4, 22, 42], [20, 4, 22, 42], [20, 15, 22, 42, "_createClass2"], [20, 28, 22, 42], [20, 29, 22, 42, "default"], [20, 36, 22, 42], [20, 38, 22, 42, "EventScope"], [20, 48, 22, 42], [21, 6, 22, 42, "key"], [21, 9, 22, 42], [22, 6, 22, 42, "value"], [22, 11, 22, 42], [22, 13, 24, 2], [22, 22, 24, 2, "addEventListener"], [22, 38, 24, 18, "addEventListener"], [22, 39, 24, 19, "listener"], [22, 47, 24, 38], [22, 49, 24, 46], [23, 8, 25, 4], [23, 12, 25, 4, "_classPrivateFieldLooseBase2"], [23, 40, 25, 4], [23, 41, 25, 4, "default"], [23, 48, 25, 4], [23, 54, 25, 8], [23, 56, 25, 8, "_listeners"], [23, 66, 25, 8], [23, 68, 25, 8, "_listeners"], [23, 78, 25, 8], [23, 80, 25, 20, "add"], [23, 83, 25, 23], [23, 84, 25, 24, "listener"], [23, 92, 25, 32], [23, 93, 25, 33], [24, 6, 26, 2], [25, 4, 26, 3], [26, 6, 26, 3, "key"], [26, 9, 26, 3], [27, 6, 26, 3, "value"], [27, 11, 26, 3], [27, 13, 28, 2], [27, 22, 28, 2, "removeEventListener"], [27, 41, 28, 21, "removeEventListener"], [27, 42, 28, 22, "listener"], [27, 50, 28, 41], [27, 52, 28, 49], [28, 8, 29, 4], [28, 12, 29, 4, "_classPrivateFieldLooseBase2"], [28, 40, 29, 4], [28, 41, 29, 4, "default"], [28, 48, 29, 4], [28, 54, 29, 8], [28, 56, 29, 8, "_listeners"], [28, 66, 29, 8], [28, 68, 29, 8, "_listeners"], [28, 78, 29, 8], [28, 80, 29, 20, "delete"], [28, 86, 29, 26], [28, 87, 29, 27, "listener"], [28, 95, 29, 35], [28, 96, 29, 36], [29, 6, 30, 2], [30, 4, 30, 3], [31, 6, 30, 3, "key"], [31, 9, 30, 3], [32, 6, 30, 3, "value"], [32, 11, 30, 3], [32, 13, 32, 2], [32, 22, 32, 2, "emit"], [32, 26, 32, 6, "emit"], [32, 27, 32, 7, "value"], [32, 32, 32, 15], [32, 34, 32, 23], [33, 8, 34, 4], [33, 13, 34, 9], [33, 17, 34, 15, "listener"], [33, 25, 34, 23], [33, 33, 34, 23, "_classPrivateFieldLooseBase2"], [33, 61, 34, 23], [33, 62, 34, 23, "default"], [33, 69, 34, 23], [33, 71, 34, 27], [33, 75, 34, 31], [33, 77, 34, 31, "_listeners"], [33, 87, 34, 31], [33, 89, 34, 31, "_listeners"], [33, 99, 34, 31], [33, 102, 34, 44], [34, 10, 35, 6, "listener"], [34, 18, 35, 14], [34, 19, 35, 15, "value"], [34, 24, 35, 20], [34, 25, 35, 21], [35, 8, 36, 4], [36, 6, 37, 2], [37, 4, 37, 3], [38, 2, 37, 3], [39, 2, 37, 3], [39, 6, 40, 13, "Domain"], [39, 12, 40, 19], [39, 15, 40, 19, "exports"], [39, 22, 40, 19], [39, 23, 40, 19, "Domain"], [39, 29, 40, 19], [40, 4, 44, 2], [40, 13, 44, 2, "Domain"], [40, 20, 44, 14, "name"], [40, 24, 44, 30], [40, 26, 44, 32], [41, 6, 44, 32], [41, 10, 44, 32, "_classCallCheck2"], [41, 26, 44, 32], [41, 27, 44, 32, "default"], [41, 34, 44, 32], [41, 42, 44, 32, "Domain"], [41, 48, 44, 32], [42, 6, 45, 4], [42, 10, 45, 8, "global"], [42, 16, 45, 14], [42, 17, 45, 15, "FuseboxReactDevToolsDispatcher"], [42, 47, 45, 45], [42, 48, 45, 46, "BINDING_NAME"], [42, 60, 45, 58], [42, 61, 45, 59], [42, 65, 45, 63], [42, 69, 45, 67], [42, 71, 45, 69], [43, 8, 46, 6], [43, 14, 46, 12], [43, 18, 46, 16, "Error"], [43, 23, 46, 21], [43, 24, 47, 8], [43, 51, 47, 35, "name"], [43, 55, 47, 39], [43, 86, 48, 6], [43, 87, 48, 7], [44, 6, 49, 4], [45, 6, 51, 4], [45, 10, 51, 8], [45, 11, 51, 9, "name"], [45, 15, 51, 13], [45, 18, 51, 16, "name"], [45, 22, 51, 20], [46, 6, 52, 4], [46, 10, 52, 8], [46, 11, 52, 9, "onMessage"], [46, 20, 52, 18], [46, 23, 52, 21], [46, 27, 52, 25, "EventScope"], [46, 37, 52, 35], [46, 38, 52, 47], [46, 39, 52, 48], [47, 4, 53, 2], [48, 4, 53, 3], [48, 15, 53, 3, "_createClass2"], [48, 28, 53, 3], [48, 29, 53, 3, "default"], [48, 36, 53, 3], [48, 38, 53, 3, "Domain"], [48, 44, 53, 3], [49, 6, 53, 3, "key"], [49, 9, 53, 3], [50, 6, 53, 3, "value"], [50, 11, 53, 3], [50, 13, 55, 2], [50, 22, 55, 2, "sendMessage"], [50, 33, 55, 13, "sendMessage"], [50, 34, 55, 14, "message"], [50, 41, 55, 32], [50, 43, 55, 34], [51, 8, 56, 4], [51, 12, 56, 10, "messageWithDomain"], [51, 29, 56, 27], [51, 32, 56, 30], [52, 10, 56, 31, "domain"], [52, 16, 56, 37], [52, 18, 56, 39], [52, 22, 56, 43], [52, 23, 56, 44, "name"], [52, 27, 56, 48], [53, 10, 56, 50, "message"], [54, 8, 56, 57], [54, 9, 56, 58], [55, 8, 57, 4], [55, 12, 57, 10, "serializedMessageWithDomain"], [55, 39, 57, 37], [55, 42, 57, 40, "JSON"], [55, 46, 57, 44], [55, 47, 57, 45, "stringify"], [55, 56, 57, 54], [55, 57, 57, 55, "messageWithDomain"], [55, 74, 57, 72], [55, 75, 57, 73], [56, 8, 59, 4, "global"], [56, 14, 59, 10], [56, 15, 59, 11, "FuseboxReactDevToolsDispatcher"], [56, 45, 59, 41], [56, 46, 59, 42, "BINDING_NAME"], [56, 58, 59, 54], [56, 59, 59, 55], [56, 60, 60, 6, "serializedMessageWithDomain"], [56, 87, 61, 4], [56, 88, 61, 5], [57, 6, 62, 2], [58, 4, 62, 3], [59, 2, 62, 3], [60, 2, 62, 3], [60, 6, 62, 3, "_domainNameToDomainMap"], [60, 28, 62, 3], [60, 48, 62, 3, "_classPrivateFieldLooseKey2"], [60, 75, 62, 3], [60, 76, 62, 3, "default"], [60, 83, 62, 3], [61, 2, 62, 3], [61, 6, 71, 6, "FuseboxReactDevToolsDispatcher"], [61, 36, 71, 36], [62, 4, 71, 36], [62, 13, 71, 36, "FuseboxReactDevToolsDispatcher"], [62, 44, 71, 36], [63, 6, 71, 36], [63, 10, 71, 36, "_classCallCheck2"], [63, 26, 71, 36], [63, 27, 71, 36, "default"], [63, 34, 71, 36], [63, 42, 71, 36, "FuseboxReactDevToolsDispatcher"], [63, 72, 71, 36], [64, 4, 71, 36], [65, 4, 71, 36], [65, 15, 71, 36, "_createClass2"], [65, 28, 71, 36], [65, 29, 71, 36, "default"], [65, 36, 71, 36], [65, 38, 71, 36, "FuseboxReactDevToolsDispatcher"], [65, 68, 71, 36], [66, 6, 71, 36, "key"], [66, 9, 71, 36], [67, 6, 71, 36, "value"], [67, 11, 71, 36], [67, 13, 79, 2], [67, 22, 79, 9, "initializeDomain"], [67, 38, 79, 25, "initializeDomain"], [67, 39, 79, 26, "domainName"], [67, 49, 79, 48], [67, 51, 79, 58], [68, 8, 80, 4], [68, 12, 80, 10, "domain"], [68, 18, 80, 16], [68, 21, 80, 19], [68, 25, 80, 23, "Domain"], [68, 31, 80, 29], [68, 32, 80, 30, "domainName"], [68, 42, 80, 40], [68, 43, 80, 41], [69, 8, 82, 4], [69, 12, 82, 4, "_classPrivateFieldLooseBase2"], [69, 40, 82, 4], [69, 41, 82, 4, "default"], [69, 48, 82, 4], [69, 54, 82, 8], [69, 56, 82, 8, "_domainNameToDomainMap"], [69, 78, 82, 8], [69, 80, 82, 8, "_domainNameToDomainMap"], [69, 102, 82, 8], [69, 104, 82, 32, "set"], [69, 107, 82, 35], [69, 108, 82, 36, "domainName"], [69, 118, 82, 46], [69, 120, 82, 48, "domain"], [69, 126, 82, 54], [69, 127, 82, 55], [70, 8, 83, 4], [70, 12, 83, 8], [70, 13, 83, 9, "onDomainInitialization"], [70, 35, 83, 31], [70, 36, 83, 32, "emit"], [70, 40, 83, 36], [70, 41, 83, 37, "domain"], [70, 47, 83, 43], [70, 48, 83, 44], [71, 8, 85, 4], [71, 15, 85, 11, "domain"], [71, 21, 85, 17], [72, 6, 86, 2], [73, 4, 86, 3], [74, 6, 86, 3, "key"], [74, 9, 86, 3], [75, 6, 86, 3, "value"], [75, 11, 86, 3], [75, 13, 89, 2], [75, 22, 89, 9, "sendMessage"], [75, 33, 89, 20, "sendMessage"], [75, 34, 89, 21, "domainName"], [75, 44, 89, 43], [75, 46, 89, 45, "message"], [75, 53, 89, 60], [75, 55, 89, 68], [76, 8, 90, 4], [76, 12, 90, 10, "domain"], [76, 18, 90, 16], [76, 21, 90, 19], [76, 25, 90, 19, "_classPrivateFieldLooseBase2"], [76, 53, 90, 19], [76, 54, 90, 19, "default"], [76, 61, 90, 19], [76, 67, 90, 23], [76, 69, 90, 23, "_domainNameToDomainMap"], [76, 91, 90, 23], [76, 93, 90, 23, "_domainNameToDomainMap"], [76, 115, 90, 23], [76, 117, 90, 47, "get"], [76, 120, 90, 50], [76, 121, 90, 51, "domainName"], [76, 131, 90, 61], [76, 132, 90, 62], [77, 8, 91, 4], [77, 12, 91, 8, "domain"], [77, 18, 91, 14], [77, 22, 91, 18], [77, 26, 91, 22], [77, 28, 91, 24], [78, 10, 92, 6], [78, 16, 92, 12], [78, 20, 92, 16, "Error"], [78, 25, 92, 21], [78, 26, 93, 8], [78, 55, 93, 37, "domainName"], [78, 65, 93, 47], [78, 89, 94, 6], [78, 90, 94, 7], [79, 8, 95, 4], [80, 8, 97, 4], [80, 12, 97, 8], [81, 10, 98, 6], [81, 14, 98, 12, "parsedMessage"], [81, 27, 98, 25], [81, 30, 98, 28, "JSON"], [81, 34, 98, 32], [81, 35, 98, 33, "parse"], [81, 40, 98, 38], [81, 41, 98, 39, "message"], [81, 48, 98, 46], [81, 49, 98, 47], [82, 10, 99, 6, "domain"], [82, 16, 99, 12], [82, 17, 99, 13, "onMessage"], [82, 26, 99, 22], [82, 27, 99, 23, "emit"], [82, 31, 99, 27], [82, 32, 99, 28, "parsedMessage"], [82, 45, 99, 41], [82, 46, 99, 42], [83, 8, 100, 4], [83, 9, 100, 5], [83, 10, 100, 6], [83, 17, 100, 13, "err"], [83, 20, 100, 16], [83, 22, 100, 18], [84, 10, 101, 6, "console"], [84, 17, 101, 13], [84, 18, 101, 14, "error"], [84, 23, 101, 19], [84, 24, 102, 8], [84, 74, 102, 58, "domainName"], [84, 84, 102, 68], [84, 87, 102, 71], [84, 89, 103, 8, "err"], [84, 92, 104, 6], [84, 93, 104, 7], [85, 8, 105, 4], [86, 6, 106, 2], [87, 4, 106, 3], [88, 2, 106, 3], [89, 2, 106, 3, "Object"], [89, 8, 106, 3], [89, 9, 106, 3, "defineProperty"], [89, 23, 106, 3], [89, 24, 71, 6, "FuseboxReactDevToolsDispatcher"], [89, 54, 71, 36], [89, 56, 71, 36, "_domainNameToDomainMap"], [89, 78, 71, 36], [90, 4, 71, 36, "writable"], [90, 12, 71, 36], [91, 4, 71, 36, "value"], [91, 9, 71, 36], [91, 11, 72, 59], [91, 15, 72, 63, "Map"], [91, 18, 72, 66], [91, 19, 72, 67], [92, 2, 72, 68], [93, 2, 71, 6, "FuseboxReactDevToolsDispatcher"], [93, 32, 71, 36], [93, 33, 75, 9, "BINDING_NAME"], [93, 45, 75, 21], [93, 48, 75, 32], [93, 86, 75, 70], [94, 2, 71, 6, "FuseboxReactDevToolsDispatcher"], [94, 32, 71, 36], [94, 33, 76, 9, "onDomainInitialization"], [94, 55, 76, 31], [94, 58, 76, 54], [94, 62, 76, 58, "EventScope"], [94, 72, 76, 68], [94, 73, 76, 77], [94, 74, 76, 78], [95, 2, 109, 0, "Object"], [95, 8, 109, 6], [95, 9, 109, 7, "defineProperty"], [95, 23, 109, 21], [95, 24, 109, 22, "global"], [95, 30, 109, 28], [95, 32, 109, 30], [95, 71, 109, 69], [95, 73, 109, 71], [96, 4, 110, 2, "value"], [96, 9, 110, 7], [96, 11, 110, 9, "FuseboxReactDevToolsDispatcher"], [96, 41, 110, 39], [97, 4, 111, 2, "configurable"], [97, 16, 111, 14], [97, 18, 111, 16], [97, 23, 111, 21], [98, 4, 112, 2, "enumerable"], [98, 14, 112, 12], [98, 16, 112, 14], [98, 21, 112, 19], [99, 4, 113, 2, "writable"], [99, 12, 113, 10], [99, 14, 113, 12], [100, 2, 114, 0], [100, 3, 114, 1], [100, 4, 114, 2], [101, 0, 114, 3], [101, 3]], "functionMap": {"names": ["<global>", "EventScope", "EventScope#addEventListener", "EventScope#removeEventListener", "EventScope#emit", "Domain", "Domain#constructor", "Domain#sendMessage", "FuseboxReactDevToolsDispatcher", "FuseboxReactDevToolsDispatcher.initializeDomain", "FuseboxReactDevToolsDispatcher.sendMessage"], "mappings": "AAA;ACoB;ECG;GDE;EEE;GFE;EGE;GHK;CDC;OKE;ECI;GDS;EEE;GFO;CLC;AQQ;ECQ;GDO;EEG;GFiB;CRC"}}, "type": "js/module"}]}