{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.NativeGesture = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _gesture = require(_dependencyMap[6], \"./gesture\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var NativeGesture = exports.NativeGesture = /*#__PURE__*/function (_BaseGesture) {\n    function NativeGesture() {\n      var _this;\n      (0, _classCallCheck2.default)(this, NativeGesture);\n      _this = _callSuper(this, NativeGesture);\n      _this.config = {};\n      _this.handlerName = 'NativeViewGestureHandler';\n      return _this;\n    }\n\n    /**\n     * When true, underlying handler will activate unconditionally when in `BEGAN` or `UNDETERMINED` state.\n     * @param value\n     */\n    (0, _inherits2.default)(NativeGesture, _BaseGesture);\n    return (0, _createClass2.default)(NativeGesture, [{\n      key: \"shouldActivateOnStart\",\n      value: function shouldActivateOnStart(value) {\n        this.config.shouldActivateOnStart = value;\n        return this;\n      }\n\n      /**\n       * When true, cancels all other gesture handlers when this `NativeViewGestureHandler` receives an `ACTIVE` state event.\n       * @param value\n       */\n    }, {\n      key: \"disallowInterruption\",\n      value: function disallowInterruption(value) {\n        this.config.disallowInterruption = value;\n        return this;\n      }\n    }]);\n  }(_gesture.BaseGesture);\n});", "lineCount": 49, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_gesture"], [12, 14, 1, 0], [12, 17, 1, 0, "require"], [12, 24, 1, 0], [12, 25, 1, 0, "_dependencyMap"], [12, 39, 1, 0], [13, 2, 1, 59], [13, 11, 1, 59, "_callSuper"], [13, 22, 1, 59, "t"], [13, 23, 1, 59], [13, 25, 1, 59, "o"], [13, 26, 1, 59], [13, 28, 1, 59, "e"], [13, 29, 1, 59], [13, 40, 1, 59, "o"], [13, 41, 1, 59], [13, 48, 1, 59, "_getPrototypeOf2"], [13, 64, 1, 59], [13, 65, 1, 59, "default"], [13, 72, 1, 59], [13, 74, 1, 59, "o"], [13, 75, 1, 59], [13, 82, 1, 59, "_possibleConstructorReturn2"], [13, 109, 1, 59], [13, 110, 1, 59, "default"], [13, 117, 1, 59], [13, 119, 1, 59, "t"], [13, 120, 1, 59], [13, 122, 1, 59, "_isNativeReflectConstruct"], [13, 147, 1, 59], [13, 152, 1, 59, "Reflect"], [13, 159, 1, 59], [13, 160, 1, 59, "construct"], [13, 169, 1, 59], [13, 170, 1, 59, "o"], [13, 171, 1, 59], [13, 173, 1, 59, "e"], [13, 174, 1, 59], [13, 186, 1, 59, "_getPrototypeOf2"], [13, 202, 1, 59], [13, 203, 1, 59, "default"], [13, 210, 1, 59], [13, 212, 1, 59, "t"], [13, 213, 1, 59], [13, 215, 1, 59, "constructor"], [13, 226, 1, 59], [13, 230, 1, 59, "o"], [13, 231, 1, 59], [13, 232, 1, 59, "apply"], [13, 237, 1, 59], [13, 238, 1, 59, "t"], [13, 239, 1, 59], [13, 241, 1, 59, "e"], [13, 242, 1, 59], [14, 2, 1, 59], [14, 11, 1, 59, "_isNativeReflectConstruct"], [14, 37, 1, 59], [14, 51, 1, 59, "t"], [14, 52, 1, 59], [14, 56, 1, 59, "Boolean"], [14, 63, 1, 59], [14, 64, 1, 59, "prototype"], [14, 73, 1, 59], [14, 74, 1, 59, "valueOf"], [14, 81, 1, 59], [14, 82, 1, 59, "call"], [14, 86, 1, 59], [14, 87, 1, 59, "Reflect"], [14, 94, 1, 59], [14, 95, 1, 59, "construct"], [14, 104, 1, 59], [14, 105, 1, 59, "Boolean"], [14, 112, 1, 59], [14, 145, 1, 59, "t"], [14, 146, 1, 59], [14, 159, 1, 59, "_isNativeReflectConstruct"], [14, 184, 1, 59], [14, 196, 1, 59, "_isNativeReflectConstruct"], [14, 197, 1, 59], [14, 210, 1, 59, "t"], [14, 211, 1, 59], [15, 2, 1, 59], [15, 6, 5, 13, "NativeGesture"], [15, 19, 5, 26], [15, 22, 5, 26, "exports"], [15, 29, 5, 26], [15, 30, 5, 26, "NativeGesture"], [15, 43, 5, 26], [15, 69, 5, 26, "_BaseGesture"], [15, 81, 5, 26], [16, 4, 8, 2], [16, 13, 8, 2, "NativeGesture"], [16, 27, 8, 2], [16, 29, 8, 16], [17, 6, 8, 16], [17, 10, 8, 16, "_this"], [17, 15, 8, 16], [18, 6, 8, 16], [18, 10, 8, 16, "_classCallCheck2"], [18, 26, 8, 16], [18, 27, 8, 16, "default"], [18, 34, 8, 16], [18, 42, 8, 16, "NativeGesture"], [18, 55, 8, 16], [19, 6, 9, 4, "_this"], [19, 11, 9, 4], [19, 14, 9, 4, "_callSuper"], [19, 24, 9, 4], [19, 31, 9, 4, "NativeGesture"], [19, 44, 9, 4], [20, 6, 9, 12, "_this"], [20, 11, 9, 12], [20, 12, 6, 9, "config"], [20, 18, 6, 15], [20, 21, 6, 63], [20, 22, 6, 64], [20, 23, 6, 65], [21, 6, 11, 4, "_this"], [21, 11, 11, 4], [21, 12, 11, 9, "handler<PERSON>ame"], [21, 23, 11, 20], [21, 26, 11, 23], [21, 52, 11, 49], [22, 6, 11, 50], [22, 13, 11, 50, "_this"], [22, 18, 11, 50], [23, 4, 12, 2], [25, 4, 14, 2], [26, 0, 15, 0], [27, 0, 16, 0], [28, 0, 17, 0], [29, 4, 14, 2], [29, 8, 14, 2, "_inherits2"], [29, 18, 14, 2], [29, 19, 14, 2, "default"], [29, 26, 14, 2], [29, 28, 14, 2, "NativeGesture"], [29, 41, 14, 2], [29, 43, 14, 2, "_BaseGesture"], [29, 55, 14, 2], [30, 4, 14, 2], [30, 15, 14, 2, "_createClass2"], [30, 28, 14, 2], [30, 29, 14, 2, "default"], [30, 36, 14, 2], [30, 38, 14, 2, "NativeGesture"], [30, 51, 14, 2], [31, 6, 14, 2, "key"], [31, 9, 14, 2], [32, 6, 14, 2, "value"], [32, 11, 14, 2], [32, 13, 18, 2], [32, 22, 18, 2, "shouldActivateOnStart"], [32, 43, 18, 23, "shouldActivateOnStart"], [32, 44, 18, 24, "value"], [32, 49, 18, 38], [32, 51, 18, 40], [33, 8, 19, 4], [33, 12, 19, 8], [33, 13, 19, 9, "config"], [33, 19, 19, 15], [33, 20, 19, 16, "shouldActivateOnStart"], [33, 41, 19, 37], [33, 44, 19, 40, "value"], [33, 49, 19, 45], [34, 8, 20, 4], [34, 15, 20, 11], [34, 19, 20, 15], [35, 6, 21, 2], [37, 6, 23, 2], [38, 0, 24, 0], [39, 0, 25, 0], [40, 0, 26, 0], [41, 4, 23, 2], [42, 6, 23, 2, "key"], [42, 9, 23, 2], [43, 6, 23, 2, "value"], [43, 11, 23, 2], [43, 13, 27, 2], [43, 22, 27, 2, "disallowInterruption"], [43, 42, 27, 22, "disallowInterruption"], [43, 43, 27, 23, "value"], [43, 48, 27, 37], [43, 50, 27, 39], [44, 8, 28, 4], [44, 12, 28, 8], [44, 13, 28, 9, "config"], [44, 19, 28, 15], [44, 20, 28, 16, "disallowInterruption"], [44, 40, 28, 36], [44, 43, 28, 39, "value"], [44, 48, 28, 44], [45, 8, 29, 4], [45, 15, 29, 11], [45, 19, 29, 15], [46, 6, 30, 2], [47, 4, 30, 3], [48, 2, 30, 3], [48, 4, 5, 35, "BaseGesture"], [48, 24, 5, 46], [49, 0, 5, 46], [49, 3]], "functionMap": {"names": ["<global>", "NativeGesture", "NativeGesture#constructor", "NativeGesture#shouldActivateOnStart", "NativeGesture#disallowInterruption"], "mappings": "AAA;OCI;ECG;GDI;EEM;GFG;EGM;GHG;CDC"}}, "type": "js/module"}]}