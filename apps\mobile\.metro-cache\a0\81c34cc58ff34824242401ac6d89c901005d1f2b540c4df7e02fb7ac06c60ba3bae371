{"dependencies": [{"name": "./conversions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 20, "index": 20}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "AUDPhSp3SCRpotk+n4dj5MLm8fk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var conversions = require(_dependencyMap[0], \"./conversions\");\n\n  /*\n  \tThis function routes a model to all other models.\n  \n  \tall functions that are routed have a property `.conversion` attached\n  \tto the returned synthetic function. This property is an array\n  \tof strings, each with the steps in between the 'from' and 'to'\n  \tcolor models (inclusive).\n  \n  \tconversions that are not possible simply are not included.\n  */\n\n  function buildGraph() {\n    var graph = {};\n    // https://jsperf.com/object-keys-vs-for-in-with-closure/3\n    var models = Object.keys(conversions);\n    for (var len = models.length, i = 0; i < len; i++) {\n      graph[models[i]] = {\n        // http://jsperf.com/1-vs-infinity\n        // micro-opt, but this is simple.\n        distance: -1,\n        parent: null\n      };\n    }\n    return graph;\n  }\n\n  // https://en.wikipedia.org/wiki/Breadth-first_search\n  function deriveBFS(fromModel) {\n    var graph = buildGraph();\n    var queue = [fromModel]; // Unshift -> queue -> pop\n\n    graph[fromModel].distance = 0;\n    while (queue.length) {\n      var current = queue.pop();\n      var adjacents = Object.keys(conversions[current]);\n      for (var len = adjacents.length, i = 0; i < len; i++) {\n        var adjacent = adjacents[i];\n        var node = graph[adjacent];\n        if (node.distance === -1) {\n          node.distance = graph[current].distance + 1;\n          node.parent = current;\n          queue.unshift(adjacent);\n        }\n      }\n    }\n    return graph;\n  }\n  function link(from, to) {\n    return function (args) {\n      return to(from(args));\n    };\n  }\n  function wrapConversion(toModel, graph) {\n    var path = [graph[toModel].parent, toModel];\n    var fn = conversions[graph[toModel].parent][toModel];\n    var cur = graph[toModel].parent;\n    while (graph[cur].parent) {\n      path.unshift(graph[cur].parent);\n      fn = link(conversions[graph[cur].parent][cur], fn);\n      cur = graph[cur].parent;\n    }\n    fn.conversion = path;\n    return fn;\n  }\n  module.exports = function (fromModel) {\n    var graph = deriveBFS(fromModel);\n    var conversion = {};\n    var models = Object.keys(graph);\n    for (var len = models.length, i = 0; i < len; i++) {\n      var toModel = models[i];\n      var node = graph[toModel];\n      if (node.parent === null) {\n        // No possible conversion, or this node is the source model.\n        continue;\n      }\n      conversion[toModel] = wrapConversion(toModel, graph);\n    }\n    return conversion;\n  };\n});", "lineCount": 83, "map": [[2, 2, 1, 0], [2, 6, 1, 6, "conversions"], [2, 17, 1, 17], [2, 20, 1, 20, "require"], [2, 27, 1, 27], [2, 28, 1, 27, "_dependencyMap"], [2, 42, 1, 27], [2, 62, 1, 43], [2, 63, 1, 44], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [11, 0, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [15, 2, 14, 0], [15, 11, 14, 9, "buildGraph"], [15, 21, 14, 19, "buildGraph"], [15, 22, 14, 19], [15, 24, 14, 22], [16, 4, 15, 1], [16, 8, 15, 7, "graph"], [16, 13, 15, 12], [16, 16, 15, 15], [16, 17, 15, 16], [16, 18, 15, 17], [17, 4, 16, 1], [18, 4, 17, 1], [18, 8, 17, 7, "models"], [18, 14, 17, 13], [18, 17, 17, 16, "Object"], [18, 23, 17, 22], [18, 24, 17, 23, "keys"], [18, 28, 17, 27], [18, 29, 17, 28, "conversions"], [18, 40, 17, 39], [18, 41, 17, 40], [19, 4, 19, 1], [19, 9, 19, 6], [19, 13, 19, 10, "len"], [19, 16, 19, 13], [19, 19, 19, 16, "models"], [19, 25, 19, 22], [19, 26, 19, 23, "length"], [19, 32, 19, 29], [19, 34, 19, 31, "i"], [19, 35, 19, 32], [19, 38, 19, 35], [19, 39, 19, 36], [19, 41, 19, 38, "i"], [19, 42, 19, 39], [19, 45, 19, 42, "len"], [19, 48, 19, 45], [19, 50, 19, 47, "i"], [19, 51, 19, 48], [19, 53, 19, 50], [19, 55, 19, 52], [20, 6, 20, 2, "graph"], [20, 11, 20, 7], [20, 12, 20, 8, "models"], [20, 18, 20, 14], [20, 19, 20, 15, "i"], [20, 20, 20, 16], [20, 21, 20, 17], [20, 22, 20, 18], [20, 25, 20, 21], [21, 8, 21, 3], [22, 8, 22, 3], [23, 8, 23, 3, "distance"], [23, 16, 23, 11], [23, 18, 23, 13], [23, 19, 23, 14], [23, 20, 23, 15], [24, 8, 24, 3, "parent"], [24, 14, 24, 9], [24, 16, 24, 11], [25, 6, 25, 2], [25, 7, 25, 3], [26, 4, 26, 1], [27, 4, 28, 1], [27, 11, 28, 8, "graph"], [27, 16, 28, 13], [28, 2, 29, 0], [30, 2, 31, 0], [31, 2, 32, 0], [31, 11, 32, 9, "deriveBFS"], [31, 20, 32, 18, "deriveBFS"], [31, 21, 32, 19, "fromModel"], [31, 30, 32, 28], [31, 32, 32, 30], [32, 4, 33, 1], [32, 8, 33, 7, "graph"], [32, 13, 33, 12], [32, 16, 33, 15, "buildGraph"], [32, 26, 33, 25], [32, 27, 33, 26], [32, 28, 33, 27], [33, 4, 34, 1], [33, 8, 34, 7, "queue"], [33, 13, 34, 12], [33, 16, 34, 15], [33, 17, 34, 16, "fromModel"], [33, 26, 34, 25], [33, 27, 34, 26], [33, 28, 34, 27], [33, 29, 34, 28], [35, 4, 36, 1, "graph"], [35, 9, 36, 6], [35, 10, 36, 7, "fromModel"], [35, 19, 36, 16], [35, 20, 36, 17], [35, 21, 36, 18, "distance"], [35, 29, 36, 26], [35, 32, 36, 29], [35, 33, 36, 30], [36, 4, 38, 1], [36, 11, 38, 8, "queue"], [36, 16, 38, 13], [36, 17, 38, 14, "length"], [36, 23, 38, 20], [36, 25, 38, 22], [37, 6, 39, 2], [37, 10, 39, 8, "current"], [37, 17, 39, 15], [37, 20, 39, 18, "queue"], [37, 25, 39, 23], [37, 26, 39, 24, "pop"], [37, 29, 39, 27], [37, 30, 39, 28], [37, 31, 39, 29], [38, 6, 40, 2], [38, 10, 40, 8, "adjacents"], [38, 19, 40, 17], [38, 22, 40, 20, "Object"], [38, 28, 40, 26], [38, 29, 40, 27, "keys"], [38, 33, 40, 31], [38, 34, 40, 32, "conversions"], [38, 45, 40, 43], [38, 46, 40, 44, "current"], [38, 53, 40, 51], [38, 54, 40, 52], [38, 55, 40, 53], [39, 6, 42, 2], [39, 11, 42, 7], [39, 15, 42, 11, "len"], [39, 18, 42, 14], [39, 21, 42, 17, "adjacents"], [39, 30, 42, 26], [39, 31, 42, 27, "length"], [39, 37, 42, 33], [39, 39, 42, 35, "i"], [39, 40, 42, 36], [39, 43, 42, 39], [39, 44, 42, 40], [39, 46, 42, 42, "i"], [39, 47, 42, 43], [39, 50, 42, 46, "len"], [39, 53, 42, 49], [39, 55, 42, 51, "i"], [39, 56, 42, 52], [39, 58, 42, 54], [39, 60, 42, 56], [40, 8, 43, 3], [40, 12, 43, 9, "adjacent"], [40, 20, 43, 17], [40, 23, 43, 20, "adjacents"], [40, 32, 43, 29], [40, 33, 43, 30, "i"], [40, 34, 43, 31], [40, 35, 43, 32], [41, 8, 44, 3], [41, 12, 44, 9, "node"], [41, 16, 44, 13], [41, 19, 44, 16, "graph"], [41, 24, 44, 21], [41, 25, 44, 22, "adjacent"], [41, 33, 44, 30], [41, 34, 44, 31], [42, 8, 46, 3], [42, 12, 46, 7, "node"], [42, 16, 46, 11], [42, 17, 46, 12, "distance"], [42, 25, 46, 20], [42, 30, 46, 25], [42, 31, 46, 26], [42, 32, 46, 27], [42, 34, 46, 29], [43, 10, 47, 4, "node"], [43, 14, 47, 8], [43, 15, 47, 9, "distance"], [43, 23, 47, 17], [43, 26, 47, 20, "graph"], [43, 31, 47, 25], [43, 32, 47, 26, "current"], [43, 39, 47, 33], [43, 40, 47, 34], [43, 41, 47, 35, "distance"], [43, 49, 47, 43], [43, 52, 47, 46], [43, 53, 47, 47], [44, 10, 48, 4, "node"], [44, 14, 48, 8], [44, 15, 48, 9, "parent"], [44, 21, 48, 15], [44, 24, 48, 18, "current"], [44, 31, 48, 25], [45, 10, 49, 4, "queue"], [45, 15, 49, 9], [45, 16, 49, 10, "unshift"], [45, 23, 49, 17], [45, 24, 49, 18, "adjacent"], [45, 32, 49, 26], [45, 33, 49, 27], [46, 8, 50, 3], [47, 6, 51, 2], [48, 4, 52, 1], [49, 4, 54, 1], [49, 11, 54, 8, "graph"], [49, 16, 54, 13], [50, 2, 55, 0], [51, 2, 57, 0], [51, 11, 57, 9, "link"], [51, 15, 57, 13, "link"], [51, 16, 57, 14, "from"], [51, 20, 57, 18], [51, 22, 57, 20, "to"], [51, 24, 57, 22], [51, 26, 57, 24], [52, 4, 58, 1], [52, 11, 58, 8], [52, 21, 58, 18, "args"], [52, 25, 58, 22], [52, 27, 58, 24], [53, 6, 59, 2], [53, 13, 59, 9, "to"], [53, 15, 59, 11], [53, 16, 59, 12, "from"], [53, 20, 59, 16], [53, 21, 59, 17, "args"], [53, 25, 59, 21], [53, 26, 59, 22], [53, 27, 59, 23], [54, 4, 60, 1], [54, 5, 60, 2], [55, 2, 61, 0], [56, 2, 63, 0], [56, 11, 63, 9, "wrapConversion"], [56, 25, 63, 23, "wrapConversion"], [56, 26, 63, 24, "toModel"], [56, 33, 63, 31], [56, 35, 63, 33, "graph"], [56, 40, 63, 38], [56, 42, 63, 40], [57, 4, 64, 1], [57, 8, 64, 7, "path"], [57, 12, 64, 11], [57, 15, 64, 14], [57, 16, 64, 15, "graph"], [57, 21, 64, 20], [57, 22, 64, 21, "toModel"], [57, 29, 64, 28], [57, 30, 64, 29], [57, 31, 64, 30, "parent"], [57, 37, 64, 36], [57, 39, 64, 38, "toModel"], [57, 46, 64, 45], [57, 47, 64, 46], [58, 4, 65, 1], [58, 8, 65, 5, "fn"], [58, 10, 65, 7], [58, 13, 65, 10, "conversions"], [58, 24, 65, 21], [58, 25, 65, 22, "graph"], [58, 30, 65, 27], [58, 31, 65, 28, "toModel"], [58, 38, 65, 35], [58, 39, 65, 36], [58, 40, 65, 37, "parent"], [58, 46, 65, 43], [58, 47, 65, 44], [58, 48, 65, 45, "toModel"], [58, 55, 65, 52], [58, 56, 65, 53], [59, 4, 67, 1], [59, 8, 67, 5, "cur"], [59, 11, 67, 8], [59, 14, 67, 11, "graph"], [59, 19, 67, 16], [59, 20, 67, 17, "toModel"], [59, 27, 67, 24], [59, 28, 67, 25], [59, 29, 67, 26, "parent"], [59, 35, 67, 32], [60, 4, 68, 1], [60, 11, 68, 8, "graph"], [60, 16, 68, 13], [60, 17, 68, 14, "cur"], [60, 20, 68, 17], [60, 21, 68, 18], [60, 22, 68, 19, "parent"], [60, 28, 68, 25], [60, 30, 68, 27], [61, 6, 69, 2, "path"], [61, 10, 69, 6], [61, 11, 69, 7, "unshift"], [61, 18, 69, 14], [61, 19, 69, 15, "graph"], [61, 24, 69, 20], [61, 25, 69, 21, "cur"], [61, 28, 69, 24], [61, 29, 69, 25], [61, 30, 69, 26, "parent"], [61, 36, 69, 32], [61, 37, 69, 33], [62, 6, 70, 2, "fn"], [62, 8, 70, 4], [62, 11, 70, 7, "link"], [62, 15, 70, 11], [62, 16, 70, 12, "conversions"], [62, 27, 70, 23], [62, 28, 70, 24, "graph"], [62, 33, 70, 29], [62, 34, 70, 30, "cur"], [62, 37, 70, 33], [62, 38, 70, 34], [62, 39, 70, 35, "parent"], [62, 45, 70, 41], [62, 46, 70, 42], [62, 47, 70, 43, "cur"], [62, 50, 70, 46], [62, 51, 70, 47], [62, 53, 70, 49, "fn"], [62, 55, 70, 51], [62, 56, 70, 52], [63, 6, 71, 2, "cur"], [63, 9, 71, 5], [63, 12, 71, 8, "graph"], [63, 17, 71, 13], [63, 18, 71, 14, "cur"], [63, 21, 71, 17], [63, 22, 71, 18], [63, 23, 71, 19, "parent"], [63, 29, 71, 25], [64, 4, 72, 1], [65, 4, 74, 1, "fn"], [65, 6, 74, 3], [65, 7, 74, 4, "conversion"], [65, 17, 74, 14], [65, 20, 74, 17, "path"], [65, 24, 74, 21], [66, 4, 75, 1], [66, 11, 75, 8, "fn"], [66, 13, 75, 10], [67, 2, 76, 0], [68, 2, 78, 0, "module"], [68, 8, 78, 6], [68, 9, 78, 7, "exports"], [68, 16, 78, 14], [68, 19, 78, 17], [68, 29, 78, 27, "fromModel"], [68, 38, 78, 36], [68, 40, 78, 38], [69, 4, 79, 1], [69, 8, 79, 7, "graph"], [69, 13, 79, 12], [69, 16, 79, 15, "deriveBFS"], [69, 25, 79, 24], [69, 26, 79, 25, "fromModel"], [69, 35, 79, 34], [69, 36, 79, 35], [70, 4, 80, 1], [70, 8, 80, 7, "conversion"], [70, 18, 80, 17], [70, 21, 80, 20], [70, 22, 80, 21], [70, 23, 80, 22], [71, 4, 82, 1], [71, 8, 82, 7, "models"], [71, 14, 82, 13], [71, 17, 82, 16, "Object"], [71, 23, 82, 22], [71, 24, 82, 23, "keys"], [71, 28, 82, 27], [71, 29, 82, 28, "graph"], [71, 34, 82, 33], [71, 35, 82, 34], [72, 4, 83, 1], [72, 9, 83, 6], [72, 13, 83, 10, "len"], [72, 16, 83, 13], [72, 19, 83, 16, "models"], [72, 25, 83, 22], [72, 26, 83, 23, "length"], [72, 32, 83, 29], [72, 34, 83, 31, "i"], [72, 35, 83, 32], [72, 38, 83, 35], [72, 39, 83, 36], [72, 41, 83, 38, "i"], [72, 42, 83, 39], [72, 45, 83, 42, "len"], [72, 48, 83, 45], [72, 50, 83, 47, "i"], [72, 51, 83, 48], [72, 53, 83, 50], [72, 55, 83, 52], [73, 6, 84, 2], [73, 10, 84, 8, "toModel"], [73, 17, 84, 15], [73, 20, 84, 18, "models"], [73, 26, 84, 24], [73, 27, 84, 25, "i"], [73, 28, 84, 26], [73, 29, 84, 27], [74, 6, 85, 2], [74, 10, 85, 8, "node"], [74, 14, 85, 12], [74, 17, 85, 15, "graph"], [74, 22, 85, 20], [74, 23, 85, 21, "toModel"], [74, 30, 85, 28], [74, 31, 85, 29], [75, 6, 87, 2], [75, 10, 87, 6, "node"], [75, 14, 87, 10], [75, 15, 87, 11, "parent"], [75, 21, 87, 17], [75, 26, 87, 22], [75, 30, 87, 26], [75, 32, 87, 28], [76, 8, 88, 3], [77, 8, 89, 3], [78, 6, 90, 2], [79, 6, 92, 2, "conversion"], [79, 16, 92, 12], [79, 17, 92, 13, "toModel"], [79, 24, 92, 20], [79, 25, 92, 21], [79, 28, 92, 24, "wrapConversion"], [79, 42, 92, 38], [79, 43, 92, 39, "toModel"], [79, 50, 92, 46], [79, 52, 92, 48, "graph"], [79, 57, 92, 53], [79, 58, 92, 54], [80, 4, 93, 1], [81, 4, 95, 1], [81, 11, 95, 8, "conversion"], [81, 21, 95, 18], [82, 2, 96, 0], [82, 3, 96, 1], [83, 0, 96, 2], [83, 3]], "functionMap": {"names": ["<global>", "buildGraph", "deriveBFS", "link", "<anonymous>", "wrapConversion", "module.exports"], "mappings": "AAA;ACa;CDe;AEG;CFuB;AGE;QCC;EDE;CHC;AKE;CLa;iBME;CNkB"}}, "type": "js/module"}]}