{"dependencies": [], "output": [{"data": {"code": "__d(function(global, require, _importDefaultUnused, _importAllUnused, module, exports, _dependencyMapUnused) {\n  module.exports = {\n  \"activity\": 61696,\n  \"airplay\": 61697,\n  \"alert-circle\": 61698,\n  \"alert-octagon\": 61699,\n  \"alert-triangle\": 61700,\n  \"align-center\": 61701,\n  \"align-justify\": 61702,\n  \"align-left\": 61703,\n  \"align-right\": 61704,\n  \"anchor\": 61705,\n  \"aperture\": 61706,\n  \"archive\": 61707,\n  \"arrow-down\": 61708,\n  \"arrow-down-circle\": 61709,\n  \"arrow-down-left\": 61710,\n  \"arrow-down-right\": 61711,\n  \"arrow-left\": 61712,\n  \"arrow-left-circle\": 61713,\n  \"arrow-right\": 61714,\n  \"arrow-right-circle\": 61715,\n  \"arrow-up\": 61716,\n  \"arrow-up-circle\": 61717,\n  \"arrow-up-left\": 61718,\n  \"arrow-up-right\": 61719,\n  \"at-sign\": 61720,\n  \"award\": 61721,\n  \"bar-chart\": 61722,\n  \"bar-chart-2\": 61723,\n  \"battery\": 61724,\n  \"battery-charging\": 61725,\n  \"bell\": 61726,\n  \"bell-off\": 61727,\n  \"bluetooth\": 61728,\n  \"bold\": 61729,\n  \"book\": 61730,\n  \"book-open\": 61731,\n  \"bookmark\": 61732,\n  \"box\": 61733,\n  \"briefcase\": 61734,\n  \"calendar\": 61735,\n  \"camera\": 61736,\n  \"camera-off\": 61737,\n  \"cast\": 61738,\n  \"check\": 61739,\n  \"check-circle\": 61740,\n  \"check-square\": 61741,\n  \"chevron-down\": 61742,\n  \"chevron-left\": 61743,\n  \"chevron-right\": 61744,\n  \"chevron-up\": 61745,\n  \"chevrons-down\": 61746,\n  \"chevrons-left\": 61747,\n  \"chevrons-right\": 61748,\n  \"chevrons-up\": 61749,\n  \"chrome\": 61750,\n  \"circle\": 61751,\n  \"clipboard\": 61752,\n  \"clock\": 61753,\n  \"cloud\": 61754,\n  \"cloud-drizzle\": 61755,\n  \"cloud-lightning\": 61756,\n  \"cloud-off\": 61757,\n  \"cloud-rain\": 61758,\n  \"cloud-snow\": 61759,\n  \"code\": 61760,\n  \"codepen\": 61761,\n  \"codesandbox\": 61762,\n  \"coffee\": 61763,\n  \"columns\": 61764,\n  \"command\": 61765,\n  \"compass\": 61766,\n  \"copy\": 61767,\n  \"corner-down-left\": 61768,\n  \"corner-down-right\": 61769,\n  \"corner-left-down\": 61770,\n  \"corner-left-up\": 61771,\n  \"corner-right-down\": 61772,\n  \"corner-right-up\": 61773,\n  \"corner-up-left\": 61774,\n  \"corner-up-right\": 61775,\n  \"cpu\": 61776,\n  \"credit-card\": 61777,\n  \"crop\": 61778,\n  \"crosshair\": 61779,\n  \"database\": 61780,\n  \"delete\": 61781,\n  \"disc\": 61782,\n  \"divide\": 61783,\n  \"divide-circle\": 61784,\n  \"divide-square\": 61785,\n  \"dollar-sign\": 61786,\n  \"download\": 61787,\n  \"download-cloud\": 61788,\n  \"dribbble\": 61789,\n  \"droplet\": 61790,\n  \"edit\": 61791,\n  \"edit-2\": 61792,\n  \"edit-3\": 61793,\n  \"external-link\": 61794,\n  \"eye\": 61795,\n  \"eye-off\": 61796,\n  \"facebook\": 61797,\n  \"fast-forward\": 61798,\n  \"feather\": 61799,\n  \"figma\": 61800,\n  \"file\": 61801,\n  \"file-minus\": 61802,\n  \"file-plus\": 61803,\n  \"file-text\": 61804,\n  \"film\": 61805,\n  \"filter\": 61806,\n  \"flag\": 61807,\n  \"folder\": 61808,\n  \"folder-minus\": 61809,\n  \"folder-plus\": 61810,\n  \"framer\": 61811,\n  \"frown\": 61812,\n  \"gift\": 61813,\n  \"git-branch\": 61814,\n  \"git-commit\": 61815,\n  \"git-merge\": 61816,\n  \"git-pull-request\": 61817,\n  \"github\": 61818,\n  \"gitlab\": 61819,\n  \"globe\": 61820,\n  \"grid\": 61821,\n  \"hard-drive\": 61822,\n  \"hash\": 61823,\n  \"headphones\": 61824,\n  \"heart\": 61825,\n  \"help-circle\": 61826,\n  \"hexagon\": 61827,\n  \"home\": 61828,\n  \"image\": 61829,\n  \"inbox\": 61830,\n  \"info\": 61831,\n  \"instagram\": 61832,\n  \"italic\": 61833,\n  \"key\": 61834,\n  \"layers\": 61835,\n  \"layout\": 61836,\n  \"life-buoy\": 61837,\n  \"link\": 61838,\n  \"link-2\": 61839,\n  \"linkedin\": 61840,\n  \"list\": 61841,\n  \"loader\": 61842,\n  \"lock\": 61843,\n  \"log-in\": 61844,\n  \"log-out\": 61845,\n  \"mail\": 61846,\n  \"map\": 61847,\n  \"map-pin\": 61848,\n  \"maximize\": 61849,\n  \"maximize-2\": 61850,\n  \"meh\": 61851,\n  \"menu\": 61852,\n  \"message-circle\": 61853,\n  \"message-square\": 61854,\n  \"mic\": 61855,\n  \"mic-off\": 61856,\n  \"minimize\": 61857,\n  \"minimize-2\": 61858,\n  \"minus\": 61859,\n  \"minus-circle\": 61860,\n  \"minus-square\": 61861,\n  \"monitor\": 61862,\n  \"moon\": 61863,\n  \"more-horizontal\": 61864,\n  \"more-vertical\": 61865,\n  \"mouse-pointer\": 61866,\n  \"move\": 61867,\n  \"music\": 61868,\n  \"navigation\": 61869,\n  \"navigation-2\": 61870,\n  \"octagon\": 61871,\n  \"package\": 61872,\n  \"paperclip\": 61873,\n  \"pause\": 61874,\n  \"pause-circle\": 61875,\n  \"pen-tool\": 61876,\n  \"percent\": 61877,\n  \"phone\": 61878,\n  \"phone-call\": 61879,\n  \"phone-forwarded\": 61880,\n  \"phone-incoming\": 61881,\n  \"phone-missed\": 61882,\n  \"phone-off\": 61883,\n  \"phone-outgoing\": 61884,\n  \"pie-chart\": 61885,\n  \"play\": 61886,\n  \"play-circle\": 61887,\n  \"plus\": 61888,\n  \"plus-circle\": 61889,\n  \"plus-square\": 61890,\n  \"pocket\": 61891,\n  \"power\": 61892,\n  \"printer\": 61893,\n  \"radio\": 61894,\n  \"refresh-ccw\": 61895,\n  \"refresh-cw\": 61896,\n  \"repeat\": 61897,\n  \"rewind\": 61898,\n  \"rotate-ccw\": 61899,\n  \"rotate-cw\": 61900,\n  \"rss\": 61901,\n  \"save\": 61902,\n  \"scissors\": 61903,\n  \"search\": 61904,\n  \"send\": 61905,\n  \"server\": 61906,\n  \"settings\": 61907,\n  \"share\": 61908,\n  \"share-2\": 61909,\n  \"shield\": 61910,\n  \"shield-off\": 61911,\n  \"shopping-bag\": 61912,\n  \"shopping-cart\": 61913,\n  \"shuffle\": 61914,\n  \"sidebar\": 61915,\n  \"skip-back\": 61916,\n  \"skip-forward\": 61917,\n  \"slack\": 61918,\n  \"slash\": 61919,\n  \"sliders\": 61920,\n  \"smartphone\": 61921,\n  \"smile\": 61922,\n  \"speaker\": 61923,\n  \"square\": 61924,\n  \"star\": 61925,\n  \"stop-circle\": 61926,\n  \"sun\": 61927,\n  \"sunrise\": 61928,\n  \"sunset\": 61929,\n  \"tablet\": 61930,\n  \"tag\": 61931,\n  \"target\": 61932,\n  \"terminal\": 61933,\n  \"thermometer\": 61934,\n  \"thumbs-down\": 61935,\n  \"thumbs-up\": 61936,\n  \"toggle-left\": 61937,\n  \"toggle-right\": 61938,\n  \"tool\": 61939,\n  \"trash\": 61940,\n  \"trash-2\": 61941,\n  \"trello\": 61942,\n  \"trending-down\": 61943,\n  \"trending-up\": 61944,\n  \"triangle\": 61945,\n  \"truck\": 61946,\n  \"tv\": 61947,\n  \"twitch\": 61948,\n  \"twitter\": 61949,\n  \"type\": 61950,\n  \"umbrella\": 61951,\n  \"underline\": 61952,\n  \"unlock\": 61953,\n  \"upload\": 61954,\n  \"upload-cloud\": 61955,\n  \"user\": 61956,\n  \"user-check\": 61957,\n  \"user-minus\": 61958,\n  \"user-plus\": 61959,\n  \"user-x\": 61960,\n  \"users\": 61961,\n  \"video\": 61962,\n  \"video-off\": 61963,\n  \"voicemail\": 61964,\n  \"volume\": 61965,\n  \"volume-1\": 61966,\n  \"volume-2\": 61967,\n  \"volume-x\": 61968,\n  \"watch\": 61969,\n  \"wifi\": 61970,\n  \"wifi-off\": 61971,\n  \"wind\": 61972,\n  \"x\": 61973,\n  \"x-circle\": 61974,\n  \"x-octagon\": 61975,\n  \"x-square\": 61976,\n  \"youtube\": 61977,\n  \"zap\": 61978,\n  \"zap-off\": 61979,\n  \"zoom-in\": 61980,\n  \"zoom-out\": 61981\n};\n});", "lineCount": 290, "map": [[290, 3]], "functionMap": null}, "type": "js/module"}]}