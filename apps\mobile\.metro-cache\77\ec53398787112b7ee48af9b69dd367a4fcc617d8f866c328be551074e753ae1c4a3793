{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 49, "index": 49}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.RotationGesture = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _gesture = require(_dependencyMap[7], \"./gesture\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var _worklet_6529280869135_init_data = {\n    code: \"function changeEventCalculator_rotationGestureTs1(current,previous){let changePayload;if(previous===undefined){changePayload={rotationChange:current.rotation};}else{changePayload={rotationChange:current.rotation-previous.rotation};}return{...current,...changePayload};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\rotationGesture.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"changeEventCalculator_rotationGestureTs1\\\",\\\"current\\\",\\\"previous\\\",\\\"changePayload\\\",\\\"undefined\\\",\\\"rotationChange\\\",\\\"rotation\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/rotationGesture.ts\\\"],\\\"mappings\\\":\\\"AAQA,SAAAA,wCAEmEA,CAAAC,OACjE,CAAAC,QAAA,EAEA,GAAI,CAAAC,aAAgD,CACpD,GAAID,QAAQ,GAAKE,SAAS,CAAE,CAC1BD,aAAa,CAAG,CACdE,cAAc,CAAEJ,OAAO,CAACK,QAC1B,CAAC,CACH,CAAC,IAAM,CACLH,aAAa,CAAG,CACdE,cAAc,CAAEJ,OAAO,CAACK,QAAQ,CAAGJ,QAAQ,CAACI,QAC9C,CAAC,CACH,CAEA,MAAO,CAAE,GAAGL,OAAO,CAAE,GAAGE,aAAc,CAAC,CACzC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var changeEventCalculator = function () {\n    var _e = [new global.Error(), 1, -27];\n    var changeEventCalculator = function (current, previous) {\n      var changePayload;\n      if (previous === undefined) {\n        changePayload = {\n          rotationChange: current.rotation\n        };\n      } else {\n        changePayload = {\n          rotationChange: current.rotation - previous.rotation\n        };\n      }\n      return {\n        ...current,\n        ...changePayload\n      };\n    };\n    changeEventCalculator.__closure = {};\n    changeEventCalculator.__workletHash = 6529280869135;\n    changeEventCalculator.__initData = _worklet_6529280869135_init_data;\n    changeEventCalculator.__stackDetails = _e;\n    return changeEventCalculator;\n  }();\n  var RotationGesture = exports.RotationGesture = /*#__PURE__*/function (_ContinousBaseGesture) {\n    function RotationGesture() {\n      var _this;\n      (0, _classCallCheck2.default)(this, RotationGesture);\n      _this = _callSuper(this, RotationGesture);\n      _this.handlerName = 'RotationGestureHandler';\n      return _this;\n    }\n    (0, _inherits2.default)(RotationGesture, _ContinousBaseGesture);\n    return (0, _createClass2.default)(RotationGesture, [{\n      key: \"onChange\",\n      value: function onChange(callback) {\n        // @ts-ignore TS being overprotective, RotationGestureHandlerEventPayload is Record\n        this.handlers.changeEventCalculator = changeEventCalculator;\n        return _superPropGet(RotationGesture, \"onChange\", this, 3)([callback]);\n      }\n    }]);\n  }(_gesture.ContinousBaseGesture);\n});", "lineCount": 65, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_gesture"], [13, 14, 1, 0], [13, 17, 1, 0, "require"], [13, 24, 1, 0], [13, 25, 1, 0, "_dependencyMap"], [13, 39, 1, 0], [14, 2, 1, 49], [14, 11, 1, 49, "_callSuper"], [14, 22, 1, 49, "t"], [14, 23, 1, 49], [14, 25, 1, 49, "o"], [14, 26, 1, 49], [14, 28, 1, 49, "e"], [14, 29, 1, 49], [14, 40, 1, 49, "o"], [14, 41, 1, 49], [14, 48, 1, 49, "_getPrototypeOf2"], [14, 64, 1, 49], [14, 65, 1, 49, "default"], [14, 72, 1, 49], [14, 74, 1, 49, "o"], [14, 75, 1, 49], [14, 82, 1, 49, "_possibleConstructorReturn2"], [14, 109, 1, 49], [14, 110, 1, 49, "default"], [14, 117, 1, 49], [14, 119, 1, 49, "t"], [14, 120, 1, 49], [14, 122, 1, 49, "_isNativeReflectConstruct"], [14, 147, 1, 49], [14, 152, 1, 49, "Reflect"], [14, 159, 1, 49], [14, 160, 1, 49, "construct"], [14, 169, 1, 49], [14, 170, 1, 49, "o"], [14, 171, 1, 49], [14, 173, 1, 49, "e"], [14, 174, 1, 49], [14, 186, 1, 49, "_getPrototypeOf2"], [14, 202, 1, 49], [14, 203, 1, 49, "default"], [14, 210, 1, 49], [14, 212, 1, 49, "t"], [14, 213, 1, 49], [14, 215, 1, 49, "constructor"], [14, 226, 1, 49], [14, 230, 1, 49, "o"], [14, 231, 1, 49], [14, 232, 1, 49, "apply"], [14, 237, 1, 49], [14, 238, 1, 49, "t"], [14, 239, 1, 49], [14, 241, 1, 49, "e"], [14, 242, 1, 49], [15, 2, 1, 49], [15, 11, 1, 49, "_isNativeReflectConstruct"], [15, 37, 1, 49], [15, 51, 1, 49, "t"], [15, 52, 1, 49], [15, 56, 1, 49, "Boolean"], [15, 63, 1, 49], [15, 64, 1, 49, "prototype"], [15, 73, 1, 49], [15, 74, 1, 49, "valueOf"], [15, 81, 1, 49], [15, 82, 1, 49, "call"], [15, 86, 1, 49], [15, 87, 1, 49, "Reflect"], [15, 94, 1, 49], [15, 95, 1, 49, "construct"], [15, 104, 1, 49], [15, 105, 1, 49, "Boolean"], [15, 112, 1, 49], [15, 145, 1, 49, "t"], [15, 146, 1, 49], [15, 159, 1, 49, "_isNativeReflectConstruct"], [15, 184, 1, 49], [15, 196, 1, 49, "_isNativeReflectConstruct"], [15, 197, 1, 49], [15, 210, 1, 49, "t"], [15, 211, 1, 49], [16, 2, 1, 49], [16, 11, 1, 49, "_superPropGet"], [16, 25, 1, 49, "t"], [16, 26, 1, 49], [16, 28, 1, 49, "o"], [16, 29, 1, 49], [16, 31, 1, 49, "e"], [16, 32, 1, 49], [16, 34, 1, 49, "r"], [16, 35, 1, 49], [16, 43, 1, 49, "p"], [16, 44, 1, 49], [16, 51, 1, 49, "_get2"], [16, 56, 1, 49], [16, 57, 1, 49, "default"], [16, 64, 1, 49], [16, 70, 1, 49, "_getPrototypeOf2"], [16, 86, 1, 49], [16, 87, 1, 49, "default"], [16, 94, 1, 49], [16, 100, 1, 49, "r"], [16, 101, 1, 49], [16, 104, 1, 49, "t"], [16, 105, 1, 49], [16, 106, 1, 49, "prototype"], [16, 115, 1, 49], [16, 118, 1, 49, "t"], [16, 119, 1, 49], [16, 122, 1, 49, "o"], [16, 123, 1, 49], [16, 125, 1, 49, "e"], [16, 126, 1, 49], [16, 140, 1, 49, "r"], [16, 141, 1, 49], [16, 166, 1, 49, "p"], [16, 167, 1, 49], [16, 180, 1, 49, "t"], [16, 181, 1, 49], [16, 192, 1, 49, "p"], [16, 193, 1, 49], [16, 194, 1, 49, "apply"], [16, 199, 1, 49], [16, 200, 1, 49, "e"], [16, 201, 1, 49], [16, 203, 1, 49, "t"], [16, 204, 1, 49], [16, 211, 1, 49, "p"], [16, 212, 1, 49], [17, 2, 1, 49], [17, 6, 1, 49, "_worklet_6529280869135_init_data"], [17, 38, 1, 49], [18, 4, 1, 49, "code"], [18, 8, 1, 49], [19, 4, 1, 49, "location"], [19, 12, 1, 49], [20, 4, 1, 49, "sourceMap"], [20, 13, 1, 49], [21, 4, 1, 49, "version"], [21, 11, 1, 49], [22, 2, 1, 49], [23, 2, 1, 49], [23, 6, 1, 49, "changeEventCalculator"], [23, 27, 1, 49], [23, 30, 9, 0], [24, 4, 9, 0], [24, 8, 9, 0, "_e"], [24, 10, 9, 0], [24, 18, 9, 0, "global"], [24, 24, 9, 0], [24, 25, 9, 0, "Error"], [24, 30, 9, 0], [25, 4, 9, 0], [25, 8, 9, 0, "changeEventCalculator"], [25, 29, 9, 0], [25, 41, 9, 0, "changeEventCalculator"], [25, 42, 10, 2, "current"], [25, 49, 10, 65], [25, 51, 11, 2, "previous"], [25, 59, 11, 67], [25, 61, 12, 2], [26, 6, 14, 2], [26, 10, 14, 6, "changePayload"], [26, 23, 14, 54], [27, 6, 15, 2], [27, 10, 15, 6, "previous"], [27, 18, 15, 14], [27, 23, 15, 19, "undefined"], [27, 32, 15, 28], [27, 34, 15, 30], [28, 8, 16, 4, "changePayload"], [28, 21, 16, 17], [28, 24, 16, 20], [29, 10, 17, 6, "rotationChange"], [29, 24, 17, 20], [29, 26, 17, 22, "current"], [29, 33, 17, 29], [29, 34, 17, 30, "rotation"], [30, 8, 18, 4], [30, 9, 18, 5], [31, 6, 19, 2], [31, 7, 19, 3], [31, 13, 19, 9], [32, 8, 20, 4, "changePayload"], [32, 21, 20, 17], [32, 24, 20, 20], [33, 10, 21, 6, "rotationChange"], [33, 24, 21, 20], [33, 26, 21, 22, "current"], [33, 33, 21, 29], [33, 34, 21, 30, "rotation"], [33, 42, 21, 38], [33, 45, 21, 41, "previous"], [33, 53, 21, 49], [33, 54, 21, 50, "rotation"], [34, 8, 22, 4], [34, 9, 22, 5], [35, 6, 23, 2], [36, 6, 25, 2], [36, 13, 25, 9], [37, 8, 25, 11], [37, 11, 25, 14, "current"], [37, 18, 25, 21], [38, 8, 25, 23], [38, 11, 25, 26, "changePayload"], [39, 6, 25, 40], [39, 7, 25, 41], [40, 4, 26, 0], [40, 5, 26, 1], [41, 4, 26, 1, "changeEventCalculator"], [41, 25, 26, 1], [41, 26, 26, 1, "__closure"], [41, 35, 26, 1], [42, 4, 26, 1, "changeEventCalculator"], [42, 25, 26, 1], [42, 26, 26, 1, "__workletHash"], [42, 39, 26, 1], [43, 4, 26, 1, "changeEventCalculator"], [43, 25, 26, 1], [43, 26, 26, 1, "__initData"], [43, 36, 26, 1], [43, 39, 26, 1, "_worklet_6529280869135_init_data"], [43, 71, 26, 1], [44, 4, 26, 1, "changeEventCalculator"], [44, 25, 26, 1], [44, 26, 26, 1, "__stackDetails"], [44, 40, 26, 1], [44, 43, 26, 1, "_e"], [44, 45, 26, 1], [45, 4, 26, 1], [45, 11, 26, 1, "changeEventCalculator"], [45, 32, 26, 1], [46, 2, 26, 1], [46, 3, 9, 0], [47, 2, 9, 0], [47, 6, 28, 13, "RotationGesture"], [47, 21, 28, 28], [47, 24, 28, 28, "exports"], [47, 31, 28, 28], [47, 32, 28, 28, "RotationGesture"], [47, 47, 28, 28], [47, 73, 28, 28, "_ContinousBaseGesture"], [47, 94, 28, 28], [48, 4, 32, 2], [48, 13, 32, 2, "RotationGesture"], [48, 29, 32, 2], [48, 31, 32, 16], [49, 6, 32, 16], [49, 10, 32, 16, "_this"], [49, 15, 32, 16], [50, 6, 32, 16], [50, 10, 32, 16, "_classCallCheck2"], [50, 26, 32, 16], [50, 27, 32, 16, "default"], [50, 34, 32, 16], [50, 42, 32, 16, "RotationGesture"], [50, 57, 32, 16], [51, 6, 33, 4, "_this"], [51, 11, 33, 4], [51, 14, 33, 4, "_callSuper"], [51, 24, 33, 4], [51, 31, 33, 4, "RotationGesture"], [51, 46, 33, 4], [52, 6, 35, 4, "_this"], [52, 11, 35, 4], [52, 12, 35, 9, "handler<PERSON>ame"], [52, 23, 35, 20], [52, 26, 35, 23], [52, 50, 35, 47], [53, 6, 35, 48], [53, 13, 35, 48, "_this"], [53, 18, 35, 48], [54, 4, 36, 2], [55, 4, 36, 3], [55, 8, 36, 3, "_inherits2"], [55, 18, 36, 3], [55, 19, 36, 3, "default"], [55, 26, 36, 3], [55, 28, 36, 3, "RotationGesture"], [55, 43, 36, 3], [55, 45, 36, 3, "_ContinousBaseGesture"], [55, 66, 36, 3], [56, 4, 36, 3], [56, 15, 36, 3, "_createClass2"], [56, 28, 36, 3], [56, 29, 36, 3, "default"], [56, 36, 36, 3], [56, 38, 36, 3, "RotationGesture"], [56, 53, 36, 3], [57, 6, 36, 3, "key"], [57, 9, 36, 3], [58, 6, 36, 3, "value"], [58, 11, 36, 3], [58, 13, 38, 2], [58, 22, 38, 2, "onChange"], [58, 30, 38, 10, "onChange"], [58, 31, 39, 4, "callback"], [58, 39, 43, 13], [58, 41, 44, 4], [59, 8, 45, 4], [60, 8, 46, 4], [60, 12, 46, 8], [60, 13, 46, 9, "handlers"], [60, 21, 46, 17], [60, 22, 46, 18, "changeEventCalculator"], [60, 43, 46, 39], [60, 46, 46, 42, "changeEventCalculator"], [60, 67, 46, 63], [61, 8, 47, 4], [61, 15, 47, 4, "_superPropGet"], [61, 28, 47, 4], [61, 29, 47, 4, "RotationGesture"], [61, 44, 47, 4], [61, 68, 47, 26, "callback"], [61, 76, 47, 34], [62, 6, 48, 2], [63, 4, 48, 3], [64, 2, 48, 3], [64, 4, 28, 37, "ContinousBaseGesture"], [64, 33, 28, 57], [65, 0, 28, 57], [65, 3]], "functionMap": {"names": ["<global>", "changeEventCalculator", "RotationGesture", "RotationGesture#constructor", "RotationGesture#onChange"], "mappings": "AAA;ACQ;CDiB;OEE;ECI;GDI;EEE;GFU;CFC"}}, "type": "js/module"}]}