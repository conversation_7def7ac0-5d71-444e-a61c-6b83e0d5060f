{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ActionType = void 0;\n  var ActionType = exports.ActionType = {\n    REANIMATED_WORKLET: 1,\n    NATIVE_ANIMATED_EVENT: 2,\n    JS_FUNCTION_OLD_API: 3,\n    JS_FUNCTION_NEW_API: 4\n  };\n\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value\n});", "lineCount": 14, "map": [[6, 2, 1, 7], [6, 6, 1, 13, "ActionType"], [6, 16, 1, 23], [6, 19, 1, 23, "exports"], [6, 26, 1, 23], [6, 27, 1, 23, "ActionType"], [6, 37, 1, 23], [6, 40, 1, 26], [7, 4, 2, 2, "REANIMATED_WORKLET"], [7, 22, 2, 20], [7, 24, 2, 22], [7, 25, 2, 23], [8, 4, 3, 2, "NATIVE_ANIMATED_EVENT"], [8, 25, 3, 23], [8, 27, 3, 25], [8, 28, 3, 26], [9, 4, 4, 2, "JS_FUNCTION_OLD_API"], [9, 23, 4, 21], [9, 25, 4, 23], [9, 26, 4, 24], [10, 4, 5, 2, "JS_FUNCTION_NEW_API"], [10, 23, 5, 21], [10, 25, 5, 23], [11, 2, 6, 0], [11, 3, 6, 10], [13, 2, 8, 0], [14, 0, 8, 0], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}