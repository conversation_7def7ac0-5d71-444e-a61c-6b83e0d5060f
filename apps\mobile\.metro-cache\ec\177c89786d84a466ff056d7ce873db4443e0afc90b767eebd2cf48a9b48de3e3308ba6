{"dependencies": [{"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 23, "index": 135}, "end": {"line": 4, "column": 46, "index": 158}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _classCallCheck = require(_dependencyMap[0], \"@babel/runtime/helpers/classCallCheck\");\n  var _createClass = require(_dependencyMap[1], \"@babel/runtime/helpers/createClass\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ColorSchemeStore = void 0;\n  var react_native_1 = require(_dependencyMap[2], \"react-native\");\n  var ColorSchemeStore = /*#__PURE__*/function () {\n    function ColorSchemeStore() {\n      _classCallCheck(this, ColorSchemeStore);\n      this.colorSchemeListeners = new Set();\n      this.colorScheme = react_native_1.Appearance.getColorScheme() || \"light\";\n      this.colorSchemeSystem = \"system\";\n      this.subscribeColorScheme = listener => {\n        this.colorSchemeListeners.add(listener);\n        return () => this.colorSchemeListeners.delete(listener);\n      };\n      this.getColorScheme = () => {\n        return this.colorScheme;\n      };\n      this.setColorScheme = colorSchemeSystem => {\n        var oldColorScheme = this.colorScheme;\n        this.colorSchemeSystem = colorSchemeSystem;\n        this.colorScheme = this.colorSchemeSystem === \"system\" ? react_native_1.Appearance.getColorScheme() || \"light\" : this.colorSchemeSystem;\n        if (oldColorScheme !== this.colorScheme) {\n          this.notifyMedia([\"colorScheme\"]);\n          this.notifyColorScheme();\n        }\n      };\n      this.toggleColorScheme = () => {\n        var currentColor = this.colorSchemeSystem === \"system\" ? react_native_1.Appearance.getColorScheme() || \"light\" : this.colorScheme;\n        this.colorScheme = currentColor === \"light\" ? \"dark\" : \"light\";\n        this.colorSchemeSystem = currentColor;\n        this.notifyMedia([\"colorScheme\"]);\n        this.notifyColorScheme();\n      };\n      try {\n        if (typeof localStorage !== \"undefined\") {\n          var isDarkMode = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n          if (localStorage.theme === \"dark\" || !(\"theme\" in localStorage) && isDarkMode) {\n            document.documentElement.classList.add(\"dark\");\n            this.colorScheme = \"dark\";\n          } else {\n            document.documentElement.classList.remove(\"dark\");\n            this.colorScheme = \"light\";\n          }\n          this.subscribeColorScheme(() => {\n            localStorage.theme = this.colorScheme;\n          });\n        }\n      } catch {\n        // Just silently fail\n      }\n    }\n    return _createClass(ColorSchemeStore, [{\n      key: \"notifyColorScheme\",\n      value: function notifyColorScheme() {\n        for (var l of this.colorSchemeListeners) l();\n      }\n    }]);\n  }();\n  exports.ColorSchemeStore = ColorSchemeStore;\n});", "lineCount": 66, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_classCallCheck"], [4, 21, 1, 13], [4, 24, 1, 13, "require"], [4, 31, 1, 13], [4, 32, 1, 13, "_dependencyMap"], [4, 46, 1, 13], [5, 2, 1, 13], [5, 6, 1, 13, "_createClass"], [5, 18, 1, 13], [5, 21, 1, 13, "require"], [5, 28, 1, 13], [5, 29, 1, 13, "_dependencyMap"], [5, 43, 1, 13], [6, 2, 2, 0, "Object"], [6, 8, 2, 6], [6, 9, 2, 7, "defineProperty"], [6, 23, 2, 21], [6, 24, 2, 22, "exports"], [6, 31, 2, 29], [6, 33, 2, 31], [6, 45, 2, 43], [6, 47, 2, 45], [7, 4, 2, 47, "value"], [7, 9, 2, 52], [7, 11, 2, 54], [8, 2, 2, 59], [8, 3, 2, 60], [8, 4, 2, 61], [9, 2, 3, 0, "exports"], [9, 9, 3, 7], [9, 10, 3, 8, "ColorSchemeStore"], [9, 26, 3, 24], [9, 29, 3, 27], [9, 34, 3, 32], [9, 35, 3, 33], [10, 2, 4, 0], [10, 6, 4, 6, "react_native_1"], [10, 20, 4, 20], [10, 23, 4, 23, "require"], [10, 30, 4, 30], [10, 31, 4, 30, "_dependencyMap"], [10, 45, 4, 30], [10, 64, 4, 45], [10, 65, 4, 46], [11, 2, 4, 47], [11, 6, 5, 6, "ColorSchemeStore"], [11, 22, 5, 22], [12, 4, 6, 4], [12, 13, 6, 4, "ColorSchemeStore"], [12, 30, 6, 4], [12, 32, 6, 18], [13, 6, 6, 18, "_classCallCheck"], [13, 21, 6, 18], [13, 28, 6, 18, "ColorSchemeStore"], [13, 44, 6, 18], [14, 6, 7, 8], [14, 10, 7, 12], [14, 11, 7, 13, "colorSchemeListeners"], [14, 31, 7, 33], [14, 34, 7, 36], [14, 38, 7, 40, "Set"], [14, 41, 7, 43], [14, 42, 7, 44], [14, 43, 7, 45], [15, 6, 8, 8], [15, 10, 8, 12], [15, 11, 8, 13, "colorScheme"], [15, 22, 8, 24], [15, 25, 8, 27, "react_native_1"], [15, 39, 8, 41], [15, 40, 8, 42, "Appearance"], [15, 50, 8, 52], [15, 51, 8, 53, "getColorScheme"], [15, 65, 8, 67], [15, 66, 8, 68], [15, 67, 8, 69], [15, 71, 8, 73], [15, 78, 8, 80], [16, 6, 9, 8], [16, 10, 9, 12], [16, 11, 9, 13, "colorSchemeSystem"], [16, 28, 9, 30], [16, 31, 9, 33], [16, 39, 9, 41], [17, 6, 10, 8], [17, 10, 10, 12], [17, 11, 10, 13, "subscribeColorScheme"], [17, 31, 10, 33], [17, 34, 10, 37, "listener"], [17, 42, 10, 45], [17, 46, 10, 50], [18, 8, 11, 12], [18, 12, 11, 16], [18, 13, 11, 17, "colorSchemeListeners"], [18, 33, 11, 37], [18, 34, 11, 38, "add"], [18, 37, 11, 41], [18, 38, 11, 42, "listener"], [18, 46, 11, 50], [18, 47, 11, 51], [19, 8, 12, 12], [19, 15, 12, 19], [19, 21, 12, 25], [19, 25, 12, 29], [19, 26, 12, 30, "colorSchemeListeners"], [19, 46, 12, 50], [19, 47, 12, 51, "delete"], [19, 53, 12, 57], [19, 54, 12, 58, "listener"], [19, 62, 12, 66], [19, 63, 12, 67], [20, 6, 13, 8], [20, 7, 13, 9], [21, 6, 14, 8], [21, 10, 14, 12], [21, 11, 14, 13, "getColorScheme"], [21, 25, 14, 27], [21, 28, 14, 30], [21, 34, 14, 36], [22, 8, 15, 12], [22, 15, 15, 19], [22, 19, 15, 23], [22, 20, 15, 24, "colorScheme"], [22, 31, 15, 35], [23, 6, 16, 8], [23, 7, 16, 9], [24, 6, 17, 8], [24, 10, 17, 12], [24, 11, 17, 13, "setColorScheme"], [24, 25, 17, 27], [24, 28, 17, 31, "colorSchemeSystem"], [24, 45, 17, 48], [24, 49, 17, 53], [25, 8, 18, 12], [25, 12, 18, 18, "oldColorScheme"], [25, 26, 18, 32], [25, 29, 18, 35], [25, 33, 18, 39], [25, 34, 18, 40, "colorScheme"], [25, 45, 18, 51], [26, 8, 19, 12], [26, 12, 19, 16], [26, 13, 19, 17, "colorSchemeSystem"], [26, 30, 19, 34], [26, 33, 19, 37, "colorSchemeSystem"], [26, 50, 19, 54], [27, 8, 20, 12], [27, 12, 20, 16], [27, 13, 20, 17, "colorScheme"], [27, 24, 20, 28], [27, 27, 21, 16], [27, 31, 21, 20], [27, 32, 21, 21, "colorSchemeSystem"], [27, 49, 21, 38], [27, 54, 21, 43], [27, 62, 21, 51], [27, 65, 22, 22, "react_native_1"], [27, 79, 22, 36], [27, 80, 22, 37, "Appearance"], [27, 90, 22, 47], [27, 91, 22, 48, "getColorScheme"], [27, 105, 22, 62], [27, 106, 22, 63], [27, 107, 22, 64], [27, 111, 22, 68], [27, 118, 22, 75], [27, 121, 23, 22], [27, 125, 23, 26], [27, 126, 23, 27, "colorSchemeSystem"], [27, 143, 23, 44], [28, 8, 24, 12], [28, 12, 24, 16, "oldColorScheme"], [28, 26, 24, 30], [28, 31, 24, 35], [28, 35, 24, 39], [28, 36, 24, 40, "colorScheme"], [28, 47, 24, 51], [28, 49, 24, 53], [29, 10, 25, 16], [29, 14, 25, 20], [29, 15, 25, 21, "notifyMedia"], [29, 26, 25, 32], [29, 27, 25, 33], [29, 28, 25, 34], [29, 41, 25, 47], [29, 42, 25, 48], [29, 43, 25, 49], [30, 10, 26, 16], [30, 14, 26, 20], [30, 15, 26, 21, "notifyColorScheme"], [30, 32, 26, 38], [30, 33, 26, 39], [30, 34, 26, 40], [31, 8, 27, 12], [32, 6, 28, 8], [32, 7, 28, 9], [33, 6, 29, 8], [33, 10, 29, 12], [33, 11, 29, 13, "toggleColorScheme"], [33, 28, 29, 30], [33, 31, 29, 33], [33, 37, 29, 39], [34, 8, 30, 12], [34, 12, 30, 18, "currentColor"], [34, 24, 30, 30], [34, 27, 30, 33], [34, 31, 30, 37], [34, 32, 30, 38, "colorSchemeSystem"], [34, 49, 30, 55], [34, 54, 30, 60], [34, 62, 30, 68], [34, 65, 31, 18, "react_native_1"], [34, 79, 31, 32], [34, 80, 31, 33, "Appearance"], [34, 90, 31, 43], [34, 91, 31, 44, "getColorScheme"], [34, 105, 31, 58], [34, 106, 31, 59], [34, 107, 31, 60], [34, 111, 31, 64], [34, 118, 31, 71], [34, 121, 32, 18], [34, 125, 32, 22], [34, 126, 32, 23, "colorScheme"], [34, 137, 32, 34], [35, 8, 33, 12], [35, 12, 33, 16], [35, 13, 33, 17, "colorScheme"], [35, 24, 33, 28], [35, 27, 33, 31, "currentColor"], [35, 39, 33, 43], [35, 44, 33, 48], [35, 51, 33, 55], [35, 54, 33, 58], [35, 60, 33, 64], [35, 63, 33, 67], [35, 70, 33, 74], [36, 8, 34, 12], [36, 12, 34, 16], [36, 13, 34, 17, "colorSchemeSystem"], [36, 30, 34, 34], [36, 33, 34, 37, "currentColor"], [36, 45, 34, 49], [37, 8, 35, 12], [37, 12, 35, 16], [37, 13, 35, 17, "notifyMedia"], [37, 24, 35, 28], [37, 25, 35, 29], [37, 26, 35, 30], [37, 39, 35, 43], [37, 40, 35, 44], [37, 41, 35, 45], [38, 8, 36, 12], [38, 12, 36, 16], [38, 13, 36, 17, "notifyColorScheme"], [38, 30, 36, 34], [38, 31, 36, 35], [38, 32, 36, 36], [39, 6, 37, 8], [39, 7, 37, 9], [40, 6, 38, 8], [40, 10, 38, 12], [41, 8, 39, 12], [41, 12, 39, 16], [41, 19, 39, 23, "localStorage"], [41, 31, 39, 35], [41, 36, 39, 40], [41, 47, 39, 51], [41, 49, 39, 53], [42, 10, 40, 16], [42, 14, 40, 22, "isDarkMode"], [42, 24, 40, 32], [42, 27, 40, 35, "window"], [42, 33, 40, 41], [42, 34, 40, 42, "matchMedia"], [42, 44, 40, 52], [42, 45, 40, 53], [42, 75, 40, 83], [42, 76, 40, 84], [42, 77, 40, 85, "matches"], [42, 84, 40, 92], [43, 10, 41, 16], [43, 14, 41, 20, "localStorage"], [43, 26, 41, 32], [43, 27, 41, 33, "theme"], [43, 32, 41, 38], [43, 37, 41, 43], [43, 43, 41, 49], [43, 47, 42, 21], [43, 49, 42, 23], [43, 56, 42, 30], [43, 60, 42, 34, "localStorage"], [43, 72, 42, 46], [43, 73, 42, 47], [43, 77, 42, 51, "isDarkMode"], [43, 87, 42, 62], [43, 89, 42, 64], [44, 12, 43, 20, "document"], [44, 20, 43, 28], [44, 21, 43, 29, "documentElement"], [44, 36, 43, 44], [44, 37, 43, 45, "classList"], [44, 46, 43, 54], [44, 47, 43, 55, "add"], [44, 50, 43, 58], [44, 51, 43, 59], [44, 57, 43, 65], [44, 58, 43, 66], [45, 12, 44, 20], [45, 16, 44, 24], [45, 17, 44, 25, "colorScheme"], [45, 28, 44, 36], [45, 31, 44, 39], [45, 37, 44, 45], [46, 10, 45, 16], [46, 11, 45, 17], [46, 17, 46, 21], [47, 12, 47, 20, "document"], [47, 20, 47, 28], [47, 21, 47, 29, "documentElement"], [47, 36, 47, 44], [47, 37, 47, 45, "classList"], [47, 46, 47, 54], [47, 47, 47, 55, "remove"], [47, 53, 47, 61], [47, 54, 47, 62], [47, 60, 47, 68], [47, 61, 47, 69], [48, 12, 48, 20], [48, 16, 48, 24], [48, 17, 48, 25, "colorScheme"], [48, 28, 48, 36], [48, 31, 48, 39], [48, 38, 48, 46], [49, 10, 49, 16], [50, 10, 50, 16], [50, 14, 50, 20], [50, 15, 50, 21, "subscribeColorScheme"], [50, 35, 50, 41], [50, 36, 50, 42], [50, 42, 50, 48], [51, 12, 51, 20, "localStorage"], [51, 24, 51, 32], [51, 25, 51, 33, "theme"], [51, 30, 51, 38], [51, 33, 51, 41], [51, 37, 51, 45], [51, 38, 51, 46, "colorScheme"], [51, 49, 51, 57], [52, 10, 52, 16], [52, 11, 52, 17], [52, 12, 52, 18], [53, 8, 53, 12], [54, 6, 54, 8], [54, 7, 54, 9], [54, 8, 55, 8], [54, 14, 55, 14], [55, 8, 56, 12], [56, 6, 56, 12], [57, 4, 58, 4], [58, 4, 58, 5], [58, 11, 58, 5, "_createClass"], [58, 23, 58, 5], [58, 24, 58, 5, "ColorSchemeStore"], [58, 40, 58, 5], [59, 6, 58, 5, "key"], [59, 9, 58, 5], [60, 6, 58, 5, "value"], [60, 11, 58, 5], [60, 13, 59, 4], [60, 22, 59, 4, "notifyColorScheme"], [60, 39, 59, 21, "notifyColorScheme"], [60, 40, 59, 21], [60, 42, 59, 24], [61, 8, 60, 8], [61, 13, 60, 13], [61, 17, 60, 19, "l"], [61, 18, 60, 20], [61, 22, 60, 24], [61, 26, 60, 28], [61, 27, 60, 29, "colorSchemeListeners"], [61, 47, 60, 49], [61, 49, 61, 12, "l"], [61, 50, 61, 13], [61, 51, 61, 14], [61, 52, 61, 15], [62, 6, 62, 4], [63, 4, 62, 5], [64, 2, 62, 5], [65, 2, 64, 0, "exports"], [65, 9, 64, 7], [65, 10, 64, 8, "ColorSchemeStore"], [65, 26, 64, 24], [65, 29, 64, 27, "ColorSchemeStore"], [65, 45, 64, 43], [66, 0, 64, 44], [66, 3]], "functionMap": {"names": ["<global>", "ColorSchemeStore", "ColorSchemeStore#constructor", "subscribeColorScheme", "<anonymous>", "getColorScheme", "setColorScheme", "toggleColorScheme", "subscribeColorScheme$argument_0", "ColorSchemeStore#notifyColorScheme"], "mappings": "AAA;ACI;ICC;oCCI;mBCE,gDD;SDC;8BGC;SHE;8BIC;SJW;iCKC;SLQ;0CMa;iBNE;KDM;IQC;KRG;CDC"}}, "type": "js/module"}]}