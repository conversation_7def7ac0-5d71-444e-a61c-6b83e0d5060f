{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../../Components/ScrollView/ScrollView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 64}}], "key": "Z6KoJgAYym4YFhr1Vnk2ReqruYk=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../Data/LogBoxLog", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 42}}], "key": "G7ZBvdkqrR7u14zDBnWaTI9YPCM=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorCodeFrame", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 66}}], "key": "7NNJElzZ20hUmFoFKgEKs9eLTYg=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorMessageHeader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 74}}], "key": "4PjUFlfcQl+AQd6a9n1v7yx8MoA=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorReactFrames", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 70}}], "key": "x/FLnhd6QR/BXiYgu1Kex4Oc8lA=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorStackFrames", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 70}}], "key": "DkvzyF859bobyz8h+IU8uziBpe0=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = LogBoxInspectorBody;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[2], \"../../Components/ScrollView/ScrollView\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"../../StyleSheet/StyleSheet\"));\n  var _LogBoxLog = _interopRequireDefault(require(_dependencyMap[4], \"../Data/LogBoxLog\"));\n  var _LogBoxInspectorCodeFrame = _interopRequireDefault(require(_dependencyMap[5], \"./LogBoxInspectorCodeFrame\"));\n  var _LogBoxInspectorMessageHeader = _interopRequireDefault(require(_dependencyMap[6], \"./LogBoxInspectorMessageHeader\"));\n  var _LogBoxInspectorReactFrames = _interopRequireDefault(require(_dependencyMap[7], \"./LogBoxInspectorReactFrames\"));\n  var _LogBoxInspectorStackFrames = _interopRequireDefault(require(_dependencyMap[8], \"./LogBoxInspectorStackFrames\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[9], \"./LogBoxStyle\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[10], \"react\"));\n  var React = _react;\n  var _jsxDevRuntime = require(_dependencyMap[11], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxInspectorBody.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var headerTitleMap = {\n    warn: 'Console Warning',\n    error: 'Console Error',\n    fatal: 'Uncaught Error',\n    syntax: 'Syntax Error',\n    component: 'Render Error'\n  };\n  function LogBoxInspectorBody(props) {\n    var _useState = (0, _react.useState)(true),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      collapsed = _useState2[0],\n      setCollapsed = _useState2[1];\n    (0, _react.useEffect)(() => {\n      setCollapsed(true);\n    }, [props.log]);\n    var headerTitle = props.log.type ?? headerTitleMap[props.log.isComponentError ? 'component' : props.log.level];\n    if (collapsed) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorMessageHeader.default, {\n          collapsed: collapsed,\n          onPress: () => setCollapsed(!collapsed),\n          message: props.log.message,\n          level: props.log.level,\n          title: headerTitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          style: styles.scrollBody,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorCodeFrame.default, {\n            codeFrame: props.log.codeFrame,\n            componentCodeFrame: props.log.componentCodeFrame\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorReactFrames.default, {\n            log: props.log\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorStackFrames.default, {\n            log: props.log,\n            onRetry: props.onRetry\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true);\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n      style: styles.scrollBody,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorMessageHeader.default, {\n        collapsed: collapsed,\n        onPress: () => setCollapsed(!collapsed),\n        message: props.log.message,\n        level: props.log.level,\n        title: headerTitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorCodeFrame.default, {\n        codeFrame: props.log.codeFrame,\n        componentCodeFrame: props.log.componentCodeFrame\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorReactFrames.default, {\n        log: props.log\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorStackFrames.default, {\n        log: props.log,\n        onRetry: props.onRetry\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _StyleSheet.default.create({\n    root: {\n      flex: 1,\n      backgroundColor: LogBoxStyle.getTextColor()\n    },\n    scrollBody: {\n      backgroundColor: LogBoxStyle.getBackgroundColor(0.9),\n      flex: 1\n    }\n  });\n});", "lineCount": 128, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 17, 11, 0], [8, 20, 11, 0, "_interopRequireDefault"], [8, 42, 11, 0], [8, 43, 11, 0, "require"], [8, 50, 11, 0], [8, 51, 11, 0, "_dependencyMap"], [8, 65, 11, 0], [9, 2, 12, 0], [9, 6, 12, 0, "_StyleSheet"], [9, 17, 12, 0], [9, 20, 12, 0, "_interopRequireDefault"], [9, 42, 12, 0], [9, 43, 12, 0, "require"], [9, 50, 12, 0], [9, 51, 12, 0, "_dependencyMap"], [9, 65, 12, 0], [10, 2, 13, 0], [10, 6, 13, 0, "_LogBoxLog"], [10, 16, 13, 0], [10, 19, 13, 0, "_interopRequireDefault"], [10, 41, 13, 0], [10, 42, 13, 0, "require"], [10, 49, 13, 0], [10, 50, 13, 0, "_dependencyMap"], [10, 64, 13, 0], [11, 2, 14, 0], [11, 6, 14, 0, "_LogBoxInspectorCodeFrame"], [11, 31, 14, 0], [11, 34, 14, 0, "_interopRequireDefault"], [11, 56, 14, 0], [11, 57, 14, 0, "require"], [11, 64, 14, 0], [11, 65, 14, 0, "_dependencyMap"], [11, 79, 14, 0], [12, 2, 15, 0], [12, 6, 15, 0, "_LogBoxInspectorMessageHeader"], [12, 35, 15, 0], [12, 38, 15, 0, "_interopRequireDefault"], [12, 60, 15, 0], [12, 61, 15, 0, "require"], [12, 68, 15, 0], [12, 69, 15, 0, "_dependencyMap"], [12, 83, 15, 0], [13, 2, 16, 0], [13, 6, 16, 0, "_LogBoxInspectorReactFrames"], [13, 33, 16, 0], [13, 36, 16, 0, "_interopRequireDefault"], [13, 58, 16, 0], [13, 59, 16, 0, "require"], [13, 66, 16, 0], [13, 67, 16, 0, "_dependencyMap"], [13, 81, 16, 0], [14, 2, 17, 0], [14, 6, 17, 0, "_LogBoxInspectorStackFrames"], [14, 33, 17, 0], [14, 36, 17, 0, "_interopRequireDefault"], [14, 58, 17, 0], [14, 59, 17, 0, "require"], [14, 66, 17, 0], [14, 67, 17, 0, "_dependencyMap"], [14, 81, 17, 0], [15, 2, 18, 0], [15, 6, 18, 0, "LogBoxStyle"], [15, 17, 18, 0], [15, 20, 18, 0, "_interopRequireWildcard"], [15, 43, 18, 0], [15, 44, 18, 0, "require"], [15, 51, 18, 0], [15, 52, 18, 0, "_dependencyMap"], [15, 66, 18, 0], [16, 2, 19, 0], [16, 6, 19, 0, "_react"], [16, 12, 19, 0], [16, 15, 19, 0, "_interopRequireWildcard"], [16, 38, 19, 0], [16, 39, 19, 0, "require"], [16, 46, 19, 0], [16, 47, 19, 0, "_dependencyMap"], [16, 61, 19, 0], [17, 2, 19, 31], [17, 6, 19, 31, "React"], [17, 11, 19, 31], [17, 14, 19, 31, "_react"], [17, 20, 19, 31], [18, 2, 19, 31], [18, 6, 19, 31, "_jsxDevRuntime"], [18, 20, 19, 31], [18, 23, 19, 31, "require"], [18, 30, 19, 31], [18, 31, 19, 31, "_dependencyMap"], [18, 45, 19, 31], [19, 2, 19, 31], [19, 6, 19, 31, "_jsxFileName"], [19, 18, 19, 31], [20, 2, 19, 31], [20, 11, 19, 31, "_interopRequireWildcard"], [20, 35, 19, 31, "e"], [20, 36, 19, 31], [20, 38, 19, 31, "t"], [20, 39, 19, 31], [20, 68, 19, 31, "WeakMap"], [20, 75, 19, 31], [20, 81, 19, 31, "r"], [20, 82, 19, 31], [20, 89, 19, 31, "WeakMap"], [20, 96, 19, 31], [20, 100, 19, 31, "n"], [20, 101, 19, 31], [20, 108, 19, 31, "WeakMap"], [20, 115, 19, 31], [20, 127, 19, 31, "_interopRequireWildcard"], [20, 150, 19, 31], [20, 162, 19, 31, "_interopRequireWildcard"], [20, 163, 19, 31, "e"], [20, 164, 19, 31], [20, 166, 19, 31, "t"], [20, 167, 19, 31], [20, 176, 19, 31, "t"], [20, 177, 19, 31], [20, 181, 19, 31, "e"], [20, 182, 19, 31], [20, 186, 19, 31, "e"], [20, 187, 19, 31], [20, 188, 19, 31, "__esModule"], [20, 198, 19, 31], [20, 207, 19, 31, "e"], [20, 208, 19, 31], [20, 214, 19, 31, "o"], [20, 215, 19, 31], [20, 217, 19, 31, "i"], [20, 218, 19, 31], [20, 220, 19, 31, "f"], [20, 221, 19, 31], [20, 226, 19, 31, "__proto__"], [20, 235, 19, 31], [20, 243, 19, 31, "default"], [20, 250, 19, 31], [20, 252, 19, 31, "e"], [20, 253, 19, 31], [20, 270, 19, 31, "e"], [20, 271, 19, 31], [20, 294, 19, 31, "e"], [20, 295, 19, 31], [20, 320, 19, 31, "e"], [20, 321, 19, 31], [20, 330, 19, 31, "f"], [20, 331, 19, 31], [20, 337, 19, 31, "o"], [20, 338, 19, 31], [20, 341, 19, 31, "t"], [20, 342, 19, 31], [20, 345, 19, 31, "n"], [20, 346, 19, 31], [20, 349, 19, 31, "r"], [20, 350, 19, 31], [20, 358, 19, 31, "o"], [20, 359, 19, 31], [20, 360, 19, 31, "has"], [20, 363, 19, 31], [20, 364, 19, 31, "e"], [20, 365, 19, 31], [20, 375, 19, 31, "o"], [20, 376, 19, 31], [20, 377, 19, 31, "get"], [20, 380, 19, 31], [20, 381, 19, 31, "e"], [20, 382, 19, 31], [20, 385, 19, 31, "o"], [20, 386, 19, 31], [20, 387, 19, 31, "set"], [20, 390, 19, 31], [20, 391, 19, 31, "e"], [20, 392, 19, 31], [20, 394, 19, 31, "f"], [20, 395, 19, 31], [20, 409, 19, 31, "_t"], [20, 411, 19, 31], [20, 415, 19, 31, "e"], [20, 416, 19, 31], [20, 432, 19, 31, "_t"], [20, 434, 19, 31], [20, 441, 19, 31, "hasOwnProperty"], [20, 455, 19, 31], [20, 456, 19, 31, "call"], [20, 460, 19, 31], [20, 461, 19, 31, "e"], [20, 462, 19, 31], [20, 464, 19, 31, "_t"], [20, 466, 19, 31], [20, 473, 19, 31, "i"], [20, 474, 19, 31], [20, 478, 19, 31, "o"], [20, 479, 19, 31], [20, 482, 19, 31, "Object"], [20, 488, 19, 31], [20, 489, 19, 31, "defineProperty"], [20, 503, 19, 31], [20, 508, 19, 31, "Object"], [20, 514, 19, 31], [20, 515, 19, 31, "getOwnPropertyDescriptor"], [20, 539, 19, 31], [20, 540, 19, 31, "e"], [20, 541, 19, 31], [20, 543, 19, 31, "_t"], [20, 545, 19, 31], [20, 552, 19, 31, "i"], [20, 553, 19, 31], [20, 554, 19, 31, "get"], [20, 557, 19, 31], [20, 561, 19, 31, "i"], [20, 562, 19, 31], [20, 563, 19, 31, "set"], [20, 566, 19, 31], [20, 570, 19, 31, "o"], [20, 571, 19, 31], [20, 572, 19, 31, "f"], [20, 573, 19, 31], [20, 575, 19, 31, "_t"], [20, 577, 19, 31], [20, 579, 19, 31, "i"], [20, 580, 19, 31], [20, 584, 19, 31, "f"], [20, 585, 19, 31], [20, 586, 19, 31, "_t"], [20, 588, 19, 31], [20, 592, 19, 31, "e"], [20, 593, 19, 31], [20, 594, 19, 31, "_t"], [20, 596, 19, 31], [20, 607, 19, 31, "f"], [20, 608, 19, 31], [20, 613, 19, 31, "e"], [20, 614, 19, 31], [20, 616, 19, 31, "t"], [20, 617, 19, 31], [21, 2, 22, 0], [21, 6, 22, 6, "headerTitleMap"], [21, 20, 22, 20], [21, 23, 22, 23], [22, 4, 23, 2, "warn"], [22, 8, 23, 6], [22, 10, 23, 8], [22, 27, 23, 25], [23, 4, 24, 2, "error"], [23, 9, 24, 7], [23, 11, 24, 9], [23, 26, 24, 24], [24, 4, 25, 2, "fatal"], [24, 9, 25, 7], [24, 11, 25, 9], [24, 27, 25, 25], [25, 4, 26, 2, "syntax"], [25, 10, 26, 8], [25, 12, 26, 10], [25, 26, 26, 24], [26, 4, 27, 2, "component"], [26, 13, 27, 11], [26, 15, 27, 13], [27, 2, 28, 0], [27, 3, 28, 1], [28, 2, 30, 15], [28, 11, 30, 24, "LogBoxInspectorBody"], [28, 30, 30, 43, "LogBoxInspectorBody"], [28, 31, 30, 44, "props"], [28, 36, 33, 1], [28, 38, 33, 15], [29, 4, 34, 2], [29, 8, 34, 2, "_useState"], [29, 17, 34, 2], [29, 20, 34, 36], [29, 24, 34, 36, "useState"], [29, 39, 34, 44], [29, 41, 34, 45], [29, 45, 34, 49], [29, 46, 34, 50], [30, 6, 34, 50, "_useState2"], [30, 16, 34, 50], [30, 23, 34, 50, "_slicedToArray2"], [30, 38, 34, 50], [30, 39, 34, 50, "default"], [30, 46, 34, 50], [30, 48, 34, 50, "_useState"], [30, 57, 34, 50], [31, 6, 34, 9, "collapsed"], [31, 15, 34, 18], [31, 18, 34, 18, "_useState2"], [31, 28, 34, 18], [32, 6, 34, 20, "setCollapsed"], [32, 18, 34, 32], [32, 21, 34, 32, "_useState2"], [32, 31, 34, 32], [33, 4, 36, 2], [33, 8, 36, 2, "useEffect"], [33, 24, 36, 11], [33, 26, 36, 12], [33, 32, 36, 18], [34, 6, 37, 4, "setCollapsed"], [34, 18, 37, 16], [34, 19, 37, 17], [34, 23, 37, 21], [34, 24, 37, 22], [35, 4, 38, 2], [35, 5, 38, 3], [35, 7, 38, 5], [35, 8, 38, 6, "props"], [35, 13, 38, 11], [35, 14, 38, 12, "log"], [35, 17, 38, 15], [35, 18, 38, 16], [35, 19, 38, 17], [36, 4, 40, 2], [36, 8, 40, 8, "headerTitle"], [36, 19, 40, 19], [36, 22, 41, 4, "props"], [36, 27, 41, 9], [36, 28, 41, 10, "log"], [36, 31, 41, 13], [36, 32, 41, 14, "type"], [36, 36, 41, 18], [36, 40, 42, 4, "headerTitleMap"], [36, 54, 42, 18], [36, 55, 42, 19, "props"], [36, 60, 42, 24], [36, 61, 42, 25, "log"], [36, 64, 42, 28], [36, 65, 42, 29, "isComponentError"], [36, 81, 42, 45], [36, 84, 42, 48], [36, 95, 42, 59], [36, 98, 42, 62, "props"], [36, 103, 42, 67], [36, 104, 42, 68, "log"], [36, 107, 42, 71], [36, 108, 42, 72, "level"], [36, 113, 42, 77], [36, 114, 42, 78], [37, 4, 44, 2], [37, 8, 44, 6, "collapsed"], [37, 17, 44, 15], [37, 19, 44, 17], [38, 6, 45, 4], [38, 26, 46, 6], [38, 30, 46, 6, "_jsxDevRuntime"], [38, 44, 46, 6], [38, 45, 46, 6, "jsxDEV"], [38, 51, 46, 6], [38, 53, 46, 6, "_jsxDevRuntime"], [38, 67, 46, 6], [38, 68, 46, 6, "Fragment"], [38, 76, 46, 6], [39, 8, 46, 6, "children"], [39, 16, 46, 6], [39, 32, 47, 8], [39, 36, 47, 8, "_jsxDevRuntime"], [39, 50, 47, 8], [39, 51, 47, 8, "jsxDEV"], [39, 57, 47, 8], [39, 59, 47, 9, "_LogBoxInspectorMessageHeader"], [39, 88, 47, 9], [39, 89, 47, 9, "default"], [39, 96, 47, 37], [40, 10, 48, 10, "collapsed"], [40, 19, 48, 19], [40, 21, 48, 21, "collapsed"], [40, 30, 48, 31], [41, 10, 49, 10, "onPress"], [41, 17, 49, 17], [41, 19, 49, 19, "onPress"], [41, 20, 49, 19], [41, 25, 49, 25, "setCollapsed"], [41, 37, 49, 37], [41, 38, 49, 38], [41, 39, 49, 39, "collapsed"], [41, 48, 49, 48], [41, 49, 49, 50], [42, 10, 50, 10, "message"], [42, 17, 50, 17], [42, 19, 50, 19, "props"], [42, 24, 50, 24], [42, 25, 50, 25, "log"], [42, 28, 50, 28], [42, 29, 50, 29, "message"], [42, 36, 50, 37], [43, 10, 51, 10, "level"], [43, 15, 51, 15], [43, 17, 51, 17, "props"], [43, 22, 51, 22], [43, 23, 51, 23, "log"], [43, 26, 51, 26], [43, 27, 51, 27, "level"], [43, 32, 51, 33], [44, 10, 52, 10, "title"], [44, 15, 52, 15], [44, 17, 52, 17, "headerTitle"], [45, 8, 52, 29], [46, 10, 52, 29, "fileName"], [46, 18, 52, 29], [46, 20, 52, 29, "_jsxFileName"], [46, 32, 52, 29], [47, 10, 52, 29, "lineNumber"], [47, 20, 52, 29], [48, 10, 52, 29, "columnNumber"], [48, 22, 52, 29], [49, 8, 52, 29], [49, 15, 53, 9], [49, 16, 53, 10], [49, 31, 54, 8], [49, 35, 54, 8, "_jsxDevRuntime"], [49, 49, 54, 8], [49, 50, 54, 8, "jsxDEV"], [49, 56, 54, 8], [49, 58, 54, 9, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [49, 69, 54, 9], [49, 70, 54, 9, "default"], [49, 77, 54, 19], [50, 10, 54, 20, "style"], [50, 15, 54, 25], [50, 17, 54, 27, "styles"], [50, 23, 54, 33], [50, 24, 54, 34, "scrollBody"], [50, 34, 54, 45], [51, 10, 54, 45, "children"], [51, 18, 54, 45], [51, 34, 55, 10], [51, 38, 55, 10, "_jsxDevRuntime"], [51, 52, 55, 10], [51, 53, 55, 10, "jsxDEV"], [51, 59, 55, 10], [51, 61, 55, 11, "_LogBoxInspectorCodeFrame"], [51, 86, 55, 11], [51, 87, 55, 11, "default"], [51, 94, 55, 35], [52, 12, 56, 12, "codeFrame"], [52, 21, 56, 21], [52, 23, 56, 23, "props"], [52, 28, 56, 28], [52, 29, 56, 29, "log"], [52, 32, 56, 32], [52, 33, 56, 33, "codeFrame"], [52, 42, 56, 43], [53, 12, 57, 12, "componentCodeFrame"], [53, 30, 57, 30], [53, 32, 57, 32, "props"], [53, 37, 57, 37], [53, 38, 57, 38, "log"], [53, 41, 57, 41], [53, 42, 57, 42, "componentCodeFrame"], [54, 10, 57, 61], [55, 12, 57, 61, "fileName"], [55, 20, 57, 61], [55, 22, 57, 61, "_jsxFileName"], [55, 34, 57, 61], [56, 12, 57, 61, "lineNumber"], [56, 22, 57, 61], [57, 12, 57, 61, "columnNumber"], [57, 24, 57, 61], [58, 10, 57, 61], [58, 17, 58, 11], [58, 18, 58, 12], [58, 33, 59, 10], [58, 37, 59, 10, "_jsxDevRuntime"], [58, 51, 59, 10], [58, 52, 59, 10, "jsxDEV"], [58, 58, 59, 10], [58, 60, 59, 11, "_LogBoxInspectorReactFrames"], [58, 87, 59, 11], [58, 88, 59, 11, "default"], [58, 95, 59, 37], [59, 12, 59, 38, "log"], [59, 15, 59, 41], [59, 17, 59, 43, "props"], [59, 22, 59, 48], [59, 23, 59, 49, "log"], [60, 10, 59, 53], [61, 12, 59, 53, "fileName"], [61, 20, 59, 53], [61, 22, 59, 53, "_jsxFileName"], [61, 34, 59, 53], [62, 12, 59, 53, "lineNumber"], [62, 22, 59, 53], [63, 12, 59, 53, "columnNumber"], [63, 24, 59, 53], [64, 10, 59, 53], [64, 17, 59, 55], [64, 18, 59, 56], [64, 33, 60, 10], [64, 37, 60, 10, "_jsxDevRuntime"], [64, 51, 60, 10], [64, 52, 60, 10, "jsxDEV"], [64, 58, 60, 10], [64, 60, 60, 11, "_LogBoxInspectorStackFrames"], [64, 87, 60, 11], [64, 88, 60, 11, "default"], [64, 95, 60, 37], [65, 12, 60, 38, "log"], [65, 15, 60, 41], [65, 17, 60, 43, "props"], [65, 22, 60, 48], [65, 23, 60, 49, "log"], [65, 26, 60, 53], [66, 12, 60, 54, "onRetry"], [66, 19, 60, 61], [66, 21, 60, 63, "props"], [66, 26, 60, 68], [66, 27, 60, 69, "onRetry"], [67, 10, 60, 77], [68, 12, 60, 77, "fileName"], [68, 20, 60, 77], [68, 22, 60, 77, "_jsxFileName"], [68, 34, 60, 77], [69, 12, 60, 77, "lineNumber"], [69, 22, 60, 77], [70, 12, 60, 77, "columnNumber"], [70, 24, 60, 77], [71, 10, 60, 77], [71, 17, 60, 79], [71, 18, 60, 80], [72, 8, 60, 80], [73, 10, 60, 80, "fileName"], [73, 18, 60, 80], [73, 20, 60, 80, "_jsxFileName"], [73, 32, 60, 80], [74, 10, 60, 80, "lineNumber"], [74, 20, 60, 80], [75, 10, 60, 80, "columnNumber"], [75, 22, 60, 80], [76, 8, 60, 80], [76, 15, 61, 20], [76, 16, 61, 21], [77, 6, 61, 21], [77, 21, 62, 8], [77, 22, 62, 9], [78, 4, 64, 2], [79, 4, 65, 2], [79, 24, 66, 4], [79, 28, 66, 4, "_jsxDevRuntime"], [79, 42, 66, 4], [79, 43, 66, 4, "jsxDEV"], [79, 49, 66, 4], [79, 51, 66, 5, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [79, 62, 66, 5], [79, 63, 66, 5, "default"], [79, 70, 66, 15], [80, 6, 66, 16, "style"], [80, 11, 66, 21], [80, 13, 66, 23, "styles"], [80, 19, 66, 29], [80, 20, 66, 30, "scrollBody"], [80, 30, 66, 41], [81, 6, 66, 41, "children"], [81, 14, 66, 41], [81, 30, 67, 6], [81, 34, 67, 6, "_jsxDevRuntime"], [81, 48, 67, 6], [81, 49, 67, 6, "jsxDEV"], [81, 55, 67, 6], [81, 57, 67, 7, "_LogBoxInspectorMessageHeader"], [81, 86, 67, 7], [81, 87, 67, 7, "default"], [81, 94, 67, 35], [82, 8, 68, 8, "collapsed"], [82, 17, 68, 17], [82, 19, 68, 19, "collapsed"], [82, 28, 68, 29], [83, 8, 69, 8, "onPress"], [83, 15, 69, 15], [83, 17, 69, 17, "onPress"], [83, 18, 69, 17], [83, 23, 69, 23, "setCollapsed"], [83, 35, 69, 35], [83, 36, 69, 36], [83, 37, 69, 37, "collapsed"], [83, 46, 69, 46], [83, 47, 69, 48], [84, 8, 70, 8, "message"], [84, 15, 70, 15], [84, 17, 70, 17, "props"], [84, 22, 70, 22], [84, 23, 70, 23, "log"], [84, 26, 70, 26], [84, 27, 70, 27, "message"], [84, 34, 70, 35], [85, 8, 71, 8, "level"], [85, 13, 71, 13], [85, 15, 71, 15, "props"], [85, 20, 71, 20], [85, 21, 71, 21, "log"], [85, 24, 71, 24], [85, 25, 71, 25, "level"], [85, 30, 71, 31], [86, 8, 72, 8, "title"], [86, 13, 72, 13], [86, 15, 72, 15, "headerTitle"], [87, 6, 72, 27], [88, 8, 72, 27, "fileName"], [88, 16, 72, 27], [88, 18, 72, 27, "_jsxFileName"], [88, 30, 72, 27], [89, 8, 72, 27, "lineNumber"], [89, 18, 72, 27], [90, 8, 72, 27, "columnNumber"], [90, 20, 72, 27], [91, 6, 72, 27], [91, 13, 73, 7], [91, 14, 73, 8], [91, 29, 74, 6], [91, 33, 74, 6, "_jsxDevRuntime"], [91, 47, 74, 6], [91, 48, 74, 6, "jsxDEV"], [91, 54, 74, 6], [91, 56, 74, 7, "_LogBoxInspectorCodeFrame"], [91, 81, 74, 7], [91, 82, 74, 7, "default"], [91, 89, 74, 31], [92, 8, 75, 8, "codeFrame"], [92, 17, 75, 17], [92, 19, 75, 19, "props"], [92, 24, 75, 24], [92, 25, 75, 25, "log"], [92, 28, 75, 28], [92, 29, 75, 29, "codeFrame"], [92, 38, 75, 39], [93, 8, 76, 8, "componentCodeFrame"], [93, 26, 76, 26], [93, 28, 76, 28, "props"], [93, 33, 76, 33], [93, 34, 76, 34, "log"], [93, 37, 76, 37], [93, 38, 76, 38, "componentCodeFrame"], [94, 6, 76, 57], [95, 8, 76, 57, "fileName"], [95, 16, 76, 57], [95, 18, 76, 57, "_jsxFileName"], [95, 30, 76, 57], [96, 8, 76, 57, "lineNumber"], [96, 18, 76, 57], [97, 8, 76, 57, "columnNumber"], [97, 20, 76, 57], [98, 6, 76, 57], [98, 13, 77, 7], [98, 14, 77, 8], [98, 29, 78, 6], [98, 33, 78, 6, "_jsxDevRuntime"], [98, 47, 78, 6], [98, 48, 78, 6, "jsxDEV"], [98, 54, 78, 6], [98, 56, 78, 7, "_LogBoxInspectorReactFrames"], [98, 83, 78, 7], [98, 84, 78, 7, "default"], [98, 91, 78, 33], [99, 8, 78, 34, "log"], [99, 11, 78, 37], [99, 13, 78, 39, "props"], [99, 18, 78, 44], [99, 19, 78, 45, "log"], [100, 6, 78, 49], [101, 8, 78, 49, "fileName"], [101, 16, 78, 49], [101, 18, 78, 49, "_jsxFileName"], [101, 30, 78, 49], [102, 8, 78, 49, "lineNumber"], [102, 18, 78, 49], [103, 8, 78, 49, "columnNumber"], [103, 20, 78, 49], [104, 6, 78, 49], [104, 13, 78, 51], [104, 14, 78, 52], [104, 29, 79, 6], [104, 33, 79, 6, "_jsxDevRuntime"], [104, 47, 79, 6], [104, 48, 79, 6, "jsxDEV"], [104, 54, 79, 6], [104, 56, 79, 7, "_LogBoxInspectorStackFrames"], [104, 83, 79, 7], [104, 84, 79, 7, "default"], [104, 91, 79, 33], [105, 8, 79, 34, "log"], [105, 11, 79, 37], [105, 13, 79, 39, "props"], [105, 18, 79, 44], [105, 19, 79, 45, "log"], [105, 22, 79, 49], [106, 8, 79, 50, "onRetry"], [106, 15, 79, 57], [106, 17, 79, 59, "props"], [106, 22, 79, 64], [106, 23, 79, 65, "onRetry"], [107, 6, 79, 73], [108, 8, 79, 73, "fileName"], [108, 16, 79, 73], [108, 18, 79, 73, "_jsxFileName"], [108, 30, 79, 73], [109, 8, 79, 73, "lineNumber"], [109, 18, 79, 73], [110, 8, 79, 73, "columnNumber"], [110, 20, 79, 73], [111, 6, 79, 73], [111, 13, 79, 75], [111, 14, 79, 76], [112, 4, 79, 76], [113, 6, 79, 76, "fileName"], [113, 14, 79, 76], [113, 16, 79, 76, "_jsxFileName"], [113, 28, 79, 76], [114, 6, 79, 76, "lineNumber"], [114, 16, 79, 76], [115, 6, 79, 76, "columnNumber"], [115, 18, 79, 76], [116, 4, 79, 76], [116, 11, 80, 16], [116, 12, 80, 17], [117, 2, 82, 0], [118, 2, 84, 0], [118, 6, 84, 6, "styles"], [118, 12, 84, 12], [118, 15, 84, 15, "StyleSheet"], [118, 34, 84, 25], [118, 35, 84, 26, "create"], [118, 41, 84, 32], [118, 42, 84, 33], [119, 4, 85, 2, "root"], [119, 8, 85, 6], [119, 10, 85, 8], [120, 6, 86, 4, "flex"], [120, 10, 86, 8], [120, 12, 86, 10], [120, 13, 86, 11], [121, 6, 87, 4, "backgroundColor"], [121, 21, 87, 19], [121, 23, 87, 21, "LogBoxStyle"], [121, 34, 87, 32], [121, 35, 87, 33, "getTextColor"], [121, 47, 87, 45], [121, 48, 87, 46], [122, 4, 88, 2], [122, 5, 88, 3], [123, 4, 89, 2, "scrollBody"], [123, 14, 89, 12], [123, 16, 89, 14], [124, 6, 90, 4, "backgroundColor"], [124, 21, 90, 19], [124, 23, 90, 21, "LogBoxStyle"], [124, 34, 90, 32], [124, 35, 90, 33, "getBackgroundColor"], [124, 53, 90, 51], [124, 54, 90, 52], [124, 57, 90, 55], [124, 58, 90, 56], [125, 6, 91, 4, "flex"], [125, 10, 91, 8], [125, 12, 91, 10], [126, 4, 92, 2], [127, 2, 93, 0], [127, 3, 93, 1], [127, 4, 93, 2], [128, 0, 93, 3], [128, 3]], "functionMap": {"names": ["<global>", "LogBoxInspectorBody", "useEffect$argument_0", "LogBoxInspectorMessageHeader.props.onPress"], "mappings": "AAA;eC6B;YCM;GDE;mBEW,8BF;iBEoB,8BF;CDa"}}, "type": "js/module"}]}