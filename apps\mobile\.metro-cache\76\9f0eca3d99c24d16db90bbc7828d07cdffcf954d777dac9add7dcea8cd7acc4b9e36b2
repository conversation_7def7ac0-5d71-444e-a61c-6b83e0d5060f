{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 16, "index": 129}, "end": {"line": 4, "column": 32, "index": 145}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useComponentState = void 0;\n  var react_1 = require(_dependencyMap[0], \"react\");\n  var initialState = {\n    hover: false,\n    active: false,\n    focus: false\n  };\n  function reducer(state, action) {\n    switch (action.type) {\n      case \"hover\":\n        return {\n          ...state,\n          hover: action.value\n        };\n      case \"active\":\n        return {\n          ...state,\n          active: action.value\n        };\n      case \"focus\":\n        return {\n          ...state,\n          focus: action.value\n        };\n      default:\n        throw new Error(\"Unknown action\");\n    }\n  }\n  var useComponentState = () => (0, react_1.useReducer)(reducer, initialState);\n  exports.useComponentState = useComponentState;\n});", "lineCount": 37, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "useComponentState"], [7, 27, 3, 25], [7, 30, 3, 28], [7, 35, 3, 33], [7, 36, 3, 34], [8, 2, 4, 0], [8, 6, 4, 6, "react_1"], [8, 13, 4, 13], [8, 16, 4, 16, "require"], [8, 23, 4, 23], [8, 24, 4, 23, "_dependencyMap"], [8, 38, 4, 23], [8, 50, 4, 31], [8, 51, 4, 32], [9, 2, 5, 0], [9, 6, 5, 6, "initialState"], [9, 18, 5, 18], [9, 21, 5, 21], [10, 4, 5, 23, "hover"], [10, 9, 5, 28], [10, 11, 5, 30], [10, 16, 5, 35], [11, 4, 5, 37, "active"], [11, 10, 5, 43], [11, 12, 5, 45], [11, 17, 5, 50], [12, 4, 5, 52, "focus"], [12, 9, 5, 57], [12, 11, 5, 59], [13, 2, 5, 65], [13, 3, 5, 66], [14, 2, 6, 0], [14, 11, 6, 9, "reducer"], [14, 18, 6, 16, "reducer"], [14, 19, 6, 17, "state"], [14, 24, 6, 22], [14, 26, 6, 24, "action"], [14, 32, 6, 30], [14, 34, 6, 32], [15, 4, 7, 4], [15, 12, 7, 12, "action"], [15, 18, 7, 18], [15, 19, 7, 19, "type"], [15, 23, 7, 23], [16, 6, 8, 8], [16, 11, 8, 13], [16, 18, 8, 20], [17, 8, 9, 12], [17, 15, 9, 19], [18, 10, 9, 21], [18, 13, 9, 24, "state"], [18, 18, 9, 29], [19, 10, 9, 31, "hover"], [19, 15, 9, 36], [19, 17, 9, 38, "action"], [19, 23, 9, 44], [19, 24, 9, 45, "value"], [20, 8, 9, 51], [20, 9, 9, 52], [21, 6, 10, 8], [21, 11, 10, 13], [21, 19, 10, 21], [22, 8, 11, 12], [22, 15, 11, 19], [23, 10, 11, 21], [23, 13, 11, 24, "state"], [23, 18, 11, 29], [24, 10, 11, 31, "active"], [24, 16, 11, 37], [24, 18, 11, 39, "action"], [24, 24, 11, 45], [24, 25, 11, 46, "value"], [25, 8, 11, 52], [25, 9, 11, 53], [26, 6, 12, 8], [26, 11, 12, 13], [26, 18, 12, 20], [27, 8, 13, 12], [27, 15, 13, 19], [28, 10, 13, 21], [28, 13, 13, 24, "state"], [28, 18, 13, 29], [29, 10, 13, 31, "focus"], [29, 15, 13, 36], [29, 17, 13, 38, "action"], [29, 23, 13, 44], [29, 24, 13, 45, "value"], [30, 8, 13, 51], [30, 9, 13, 52], [31, 6, 14, 8], [32, 8, 15, 12], [32, 14, 15, 18], [32, 18, 15, 22, "Error"], [32, 23, 15, 27], [32, 24, 15, 28], [32, 40, 15, 44], [32, 41, 15, 45], [33, 4, 16, 4], [34, 2, 17, 0], [35, 2, 18, 0], [35, 6, 18, 6, "useComponentState"], [35, 23, 18, 23], [35, 26, 18, 26, "useComponentState"], [35, 27, 18, 26], [35, 32, 18, 32], [35, 33, 18, 33], [35, 34, 18, 34], [35, 36, 18, 36, "react_1"], [35, 43, 18, 43], [35, 44, 18, 44, "useReducer"], [35, 54, 18, 54], [35, 56, 18, 56, "reducer"], [35, 63, 18, 63], [35, 65, 18, 65, "initialState"], [35, 77, 18, 77], [35, 78, 18, 78], [36, 2, 19, 0, "exports"], [36, 9, 19, 7], [36, 10, 19, 8, "useComponentState"], [36, 27, 19, 25], [36, 30, 19, 28, "useComponentState"], [36, 47, 19, 45], [37, 0, 19, 46], [37, 3]], "functionMap": {"names": ["<global>", "reducer", "useComponentState"], "mappings": "AAA;ACK;CDW;0BEC,oDF"}}, "type": "js/module"}]}