{"dependencies": [{"name": "./ExceptionsManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 70}}], "key": "F4cuyfadkvY/n8mr5QTX9hSREzc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _ExceptionsManager = _interopRequireWildcard(require(_dependencyMap[0], \"./ExceptionsManager\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var ReactFiberErrorDialog = {\n    showErrorDialog(_ref) {\n      var componentStack = _ref.componentStack,\n        errorValue = _ref.error;\n      var error;\n      if (errorValue instanceof Error) {\n        error = errorValue;\n      } else if (typeof errorValue === 'string') {\n        error = new _ExceptionsManager.SyntheticError(errorValue);\n      } else {\n        error = new _ExceptionsManager.SyntheticError('Unspecified error');\n      }\n      try {\n        error.componentStack = componentStack;\n        error.isComponentError = true;\n      } catch {}\n      _ExceptionsManager.default.handleException(error, false);\n      return false;\n    }\n  };\n  var _default = exports.default = ReactFiberErrorDialog;\n});", "lineCount": 29, "map": [[6, 2, 13, 0], [6, 6, 13, 0, "_ExceptionsManager"], [6, 24, 13, 0], [6, 27, 13, 0, "_interopRequireWildcard"], [6, 50, 13, 0], [6, 51, 13, 0, "require"], [6, 58, 13, 0], [6, 59, 13, 0, "_dependencyMap"], [6, 73, 13, 0], [7, 2, 13, 70], [7, 11, 13, 70, "_interopRequireWildcard"], [7, 35, 13, 70, "e"], [7, 36, 13, 70], [7, 38, 13, 70, "t"], [7, 39, 13, 70], [7, 68, 13, 70, "WeakMap"], [7, 75, 13, 70], [7, 81, 13, 70, "r"], [7, 82, 13, 70], [7, 89, 13, 70, "WeakMap"], [7, 96, 13, 70], [7, 100, 13, 70, "n"], [7, 101, 13, 70], [7, 108, 13, 70, "WeakMap"], [7, 115, 13, 70], [7, 127, 13, 70, "_interopRequireWildcard"], [7, 150, 13, 70], [7, 162, 13, 70, "_interopRequireWildcard"], [7, 163, 13, 70, "e"], [7, 164, 13, 70], [7, 166, 13, 70, "t"], [7, 167, 13, 70], [7, 176, 13, 70, "t"], [7, 177, 13, 70], [7, 181, 13, 70, "e"], [7, 182, 13, 70], [7, 186, 13, 70, "e"], [7, 187, 13, 70], [7, 188, 13, 70, "__esModule"], [7, 198, 13, 70], [7, 207, 13, 70, "e"], [7, 208, 13, 70], [7, 214, 13, 70, "o"], [7, 215, 13, 70], [7, 217, 13, 70, "i"], [7, 218, 13, 70], [7, 220, 13, 70, "f"], [7, 221, 13, 70], [7, 226, 13, 70, "__proto__"], [7, 235, 13, 70], [7, 243, 13, 70, "default"], [7, 250, 13, 70], [7, 252, 13, 70, "e"], [7, 253, 13, 70], [7, 270, 13, 70, "e"], [7, 271, 13, 70], [7, 294, 13, 70, "e"], [7, 295, 13, 70], [7, 320, 13, 70, "e"], [7, 321, 13, 70], [7, 330, 13, 70, "f"], [7, 331, 13, 70], [7, 337, 13, 70, "o"], [7, 338, 13, 70], [7, 341, 13, 70, "t"], [7, 342, 13, 70], [7, 345, 13, 70, "n"], [7, 346, 13, 70], [7, 349, 13, 70, "r"], [7, 350, 13, 70], [7, 358, 13, 70, "o"], [7, 359, 13, 70], [7, 360, 13, 70, "has"], [7, 363, 13, 70], [7, 364, 13, 70, "e"], [7, 365, 13, 70], [7, 375, 13, 70, "o"], [7, 376, 13, 70], [7, 377, 13, 70, "get"], [7, 380, 13, 70], [7, 381, 13, 70, "e"], [7, 382, 13, 70], [7, 385, 13, 70, "o"], [7, 386, 13, 70], [7, 387, 13, 70, "set"], [7, 390, 13, 70], [7, 391, 13, 70, "e"], [7, 392, 13, 70], [7, 394, 13, 70, "f"], [7, 395, 13, 70], [7, 409, 13, 70, "_t"], [7, 411, 13, 70], [7, 415, 13, 70, "e"], [7, 416, 13, 70], [7, 432, 13, 70, "_t"], [7, 434, 13, 70], [7, 441, 13, 70, "hasOwnProperty"], [7, 455, 13, 70], [7, 456, 13, 70, "call"], [7, 460, 13, 70], [7, 461, 13, 70, "e"], [7, 462, 13, 70], [7, 464, 13, 70, "_t"], [7, 466, 13, 70], [7, 473, 13, 70, "i"], [7, 474, 13, 70], [7, 478, 13, 70, "o"], [7, 479, 13, 70], [7, 482, 13, 70, "Object"], [7, 488, 13, 70], [7, 489, 13, 70, "defineProperty"], [7, 503, 13, 70], [7, 508, 13, 70, "Object"], [7, 514, 13, 70], [7, 515, 13, 70, "getOwnPropertyDescriptor"], [7, 539, 13, 70], [7, 540, 13, 70, "e"], [7, 541, 13, 70], [7, 543, 13, 70, "_t"], [7, 545, 13, 70], [7, 552, 13, 70, "i"], [7, 553, 13, 70], [7, 554, 13, 70, "get"], [7, 557, 13, 70], [7, 561, 13, 70, "i"], [7, 562, 13, 70], [7, 563, 13, 70, "set"], [7, 566, 13, 70], [7, 570, 13, 70, "o"], [7, 571, 13, 70], [7, 572, 13, 70, "f"], [7, 573, 13, 70], [7, 575, 13, 70, "_t"], [7, 577, 13, 70], [7, 579, 13, 70, "i"], [7, 580, 13, 70], [7, 584, 13, 70, "f"], [7, 585, 13, 70], [7, 586, 13, 70, "_t"], [7, 588, 13, 70], [7, 592, 13, 70, "e"], [7, 593, 13, 70], [7, 594, 13, 70, "_t"], [7, 596, 13, 70], [7, 607, 13, 70, "f"], [7, 608, 13, 70], [7, 613, 13, 70, "e"], [7, 614, 13, 70], [7, 616, 13, 70, "t"], [7, 617, 13, 70], [8, 2, 22, 0], [8, 6, 22, 6, "ReactFiberErrorDialog"], [8, 27, 22, 27], [8, 30, 22, 30], [9, 4, 27, 2, "showErrorDialog"], [9, 19, 27, 17, "showErrorDialog"], [9, 20, 27, 17, "_ref"], [9, 24, 27, 17], [9, 26, 27, 79], [10, 6, 27, 79], [10, 10, 27, 19, "componentStack"], [10, 24, 27, 33], [10, 27, 27, 33, "_ref"], [10, 31, 27, 33], [10, 32, 27, 19, "componentStack"], [10, 46, 27, 33], [11, 8, 27, 42, "errorValue"], [11, 18, 27, 52], [11, 21, 27, 52, "_ref"], [11, 25, 27, 52], [11, 26, 27, 35, "error"], [11, 31, 27, 40], [12, 6, 28, 4], [12, 10, 28, 8, "error"], [12, 15, 28, 29], [13, 6, 32, 4], [13, 10, 32, 8, "errorValue"], [13, 20, 32, 18], [13, 32, 32, 30, "Error"], [13, 37, 32, 35], [13, 39, 32, 37], [14, 8, 36, 6, "error"], [14, 13, 36, 11], [14, 16, 36, 15, "errorValue"], [14, 26, 36, 41], [15, 6, 37, 4], [15, 7, 37, 5], [15, 13, 37, 11], [15, 17, 37, 15], [15, 24, 37, 22, "errorValue"], [15, 34, 37, 32], [15, 39, 37, 37], [15, 47, 37, 45], [15, 49, 37, 47], [16, 8, 41, 6, "error"], [16, 13, 41, 11], [16, 16, 41, 15], [16, 20, 41, 19, "SyntheticError"], [16, 53, 41, 33], [16, 54, 41, 34, "errorValue"], [16, 64, 41, 44], [16, 65, 41, 61], [17, 6, 42, 4], [17, 7, 42, 5], [17, 13, 42, 11], [18, 8, 46, 6, "error"], [18, 13, 46, 11], [18, 16, 46, 15], [18, 20, 46, 19, "SyntheticError"], [18, 53, 46, 33], [18, 54, 46, 34], [18, 73, 46, 53], [18, 74, 46, 70], [19, 6, 47, 4], [20, 6, 48, 4], [20, 10, 48, 8], [21, 8, 49, 6, "error"], [21, 13, 49, 11], [21, 14, 49, 12, "componentStack"], [21, 28, 49, 26], [21, 31, 49, 29, "componentStack"], [21, 45, 49, 43], [22, 8, 50, 6, "error"], [22, 13, 50, 11], [22, 14, 50, 12, "isComponentError"], [22, 30, 50, 28], [22, 33, 50, 31], [22, 37, 50, 35], [23, 6, 51, 4], [23, 7, 51, 5], [23, 8, 51, 6], [23, 14, 51, 12], [23, 15, 53, 4], [24, 6, 55, 4, "ExceptionsManager"], [24, 32, 55, 21], [24, 33, 55, 22, "handleException"], [24, 48, 55, 37], [24, 49, 55, 38, "error"], [24, 54, 55, 43], [24, 56, 55, 45], [24, 61, 55, 50], [24, 62, 55, 51], [25, 6, 61, 4], [25, 13, 61, 11], [25, 18, 61, 16], [26, 4, 62, 2], [27, 2, 63, 0], [27, 3, 63, 1], [28, 2, 63, 2], [28, 6, 63, 2, "_default"], [28, 14, 63, 2], [28, 17, 63, 2, "exports"], [28, 24, 63, 2], [28, 25, 63, 2, "default"], [28, 32, 63, 2], [28, 35, 65, 15, "ReactFiberErrorDialog"], [28, 56, 65, 36], [29, 0, 65, 36], [29, 3]], "functionMap": {"names": ["<global>", "showErrorDialog"], "mappings": "AAA;EC0B;GDmC"}}, "type": "js/module"}]}