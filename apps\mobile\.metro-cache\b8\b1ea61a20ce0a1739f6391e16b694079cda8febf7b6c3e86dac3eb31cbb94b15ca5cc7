{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "./internals/Utilities", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 62}}], "key": "TjNx4kPKXttVwQPaMsi6cYyHSX0=", "exportNames": ["*"]}}, {"name": "./PerformanceEntry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 52}}], "key": "brFNAt3Zh5rA+ZZUGgMallCwpmE=", "exportNames": ["*"]}}, {"name": "./specs/NativePerformance", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 58}}], "key": "6o1nOxWMCiRx5/kTKp0IyARkOoo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PerformanceEventTiming = exports.EventCounts = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[7], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[8], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _Utilities = require(_dependencyMap[9], \"./internals/Utilities\");\n  var _PerformanceEntry2 = require(_dependencyMap[10], \"./PerformanceEntry\");\n  var _NativePerformance = _interopRequireDefault(require(_dependencyMap[11], \"./specs/NativePerformance\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var _processingStart = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"processingStart\");\n  var _processingEnd = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"processingEnd\");\n  var _interactionId = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"interactionId\");\n  var PerformanceEventTiming = exports.PerformanceEventTiming = /*#__PURE__*/function (_PerformanceEntry) {\n    function PerformanceEventTiming(init) {\n      var _this;\n      (0, _classCallCheck2.default)(this, PerformanceEventTiming);\n      _this = _callSuper(this, PerformanceEventTiming, [{\n        name: init.name,\n        entryType: 'event',\n        startTime: init.startTime ?? 0,\n        duration: init.duration ?? 0\n      }]);\n      Object.defineProperty(_this, _processingStart, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _processingEnd, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _interactionId, {\n        writable: true,\n        value: void 0\n      });\n      (0, _classPrivateFieldLooseBase2.default)(_this, _processingStart)[_processingStart] = init.processingStart ?? 0;\n      (0, _classPrivateFieldLooseBase2.default)(_this, _processingEnd)[_processingEnd] = init.processingEnd ?? 0;\n      (0, _classPrivateFieldLooseBase2.default)(_this, _interactionId)[_interactionId] = init.interactionId ?? 0;\n      return _this;\n    }\n    (0, _inherits2.default)(PerformanceEventTiming, _PerformanceEntry);\n    return (0, _createClass2.default)(PerformanceEventTiming, [{\n      key: \"processingStart\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _processingStart)[_processingStart];\n      }\n    }, {\n      key: \"processingEnd\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _processingEnd)[_processingEnd];\n      }\n    }, {\n      key: \"interactionId\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _interactionId)[_interactionId];\n      }\n    }, {\n      key: \"toJSON\",\n      value: function toJSON() {\n        return {\n          ..._superPropGet(PerformanceEventTiming, \"toJSON\", this, 3)([]),\n          processingStart: (0, _classPrivateFieldLooseBase2.default)(this, _processingStart)[_processingStart],\n          processingEnd: (0, _classPrivateFieldLooseBase2.default)(this, _processingEnd)[_processingEnd],\n          interactionId: (0, _classPrivateFieldLooseBase2.default)(this, _interactionId)[_interactionId]\n        };\n      }\n    }]);\n  }(_PerformanceEntry2.PerformanceEntry);\n  var cachedEventCounts;\n  function getCachedEventCounts() {\n    if (cachedEventCounts) {\n      return cachedEventCounts;\n    }\n    if (!_NativePerformance.default || !_NativePerformance.default?.getEventCounts) {\n      (0, _Utilities.warnNoNativePerformance)();\n      cachedEventCounts = new Map();\n      return cachedEventCounts;\n    }\n    var eventCounts = new Map(_NativePerformance.default.getEventCounts?.() ?? []);\n    cachedEventCounts = eventCounts;\n    global.queueMicrotask(() => {\n      cachedEventCounts = null;\n    });\n    return eventCounts;\n  }\n  var EventCounts = exports.EventCounts = /*#__PURE__*/function () {\n    function EventCounts() {\n      (0, _classCallCheck2.default)(this, EventCounts);\n    }\n    return (0, _createClass2.default)(EventCounts, [{\n      key: \"size\",\n      get: function () {\n        return getCachedEventCounts().size;\n      }\n    }, {\n      key: \"entries\",\n      value: function entries() {\n        return getCachedEventCounts().entries();\n      }\n    }, {\n      key: \"forEach\",\n      value: function forEach(callback) {\n        return getCachedEventCounts().forEach(callback);\n      }\n    }, {\n      key: \"get\",\n      value: function get(key) {\n        return getCachedEventCounts().get(key);\n      }\n    }, {\n      key: \"has\",\n      value: function has(key) {\n        return getCachedEventCounts().has(key);\n      }\n    }, {\n      key: \"keys\",\n      value: function keys() {\n        return getCachedEventCounts().keys();\n      }\n    }, {\n      key: \"values\",\n      value: function values() {\n        return getCachedEventCounts().values();\n      }\n    }]);\n  }();\n});", "lineCount": 137, "map": [[15, 2, 18, 0], [15, 6, 18, 0, "_Utilities"], [15, 16, 18, 0], [15, 19, 18, 0, "require"], [15, 26, 18, 0], [15, 27, 18, 0, "_dependencyMap"], [15, 41, 18, 0], [16, 2, 19, 0], [16, 6, 19, 0, "_PerformanceEntry2"], [16, 24, 19, 0], [16, 27, 19, 0, "require"], [16, 34, 19, 0], [16, 35, 19, 0, "_dependencyMap"], [16, 49, 19, 0], [17, 2, 20, 0], [17, 6, 20, 0, "_NativePerformance"], [17, 24, 20, 0], [17, 27, 20, 0, "_interopRequireDefault"], [17, 49, 20, 0], [17, 50, 20, 0, "require"], [17, 57, 20, 0], [17, 58, 20, 0, "_dependencyMap"], [17, 72, 20, 0], [18, 2, 20, 58], [18, 11, 20, 58, "_callSuper"], [18, 22, 20, 58, "t"], [18, 23, 20, 58], [18, 25, 20, 58, "o"], [18, 26, 20, 58], [18, 28, 20, 58, "e"], [18, 29, 20, 58], [18, 40, 20, 58, "o"], [18, 41, 20, 58], [18, 48, 20, 58, "_getPrototypeOf2"], [18, 64, 20, 58], [18, 65, 20, 58, "default"], [18, 72, 20, 58], [18, 74, 20, 58, "o"], [18, 75, 20, 58], [18, 82, 20, 58, "_possibleConstructorReturn2"], [18, 109, 20, 58], [18, 110, 20, 58, "default"], [18, 117, 20, 58], [18, 119, 20, 58, "t"], [18, 120, 20, 58], [18, 122, 20, 58, "_isNativeReflectConstruct"], [18, 147, 20, 58], [18, 152, 20, 58, "Reflect"], [18, 159, 20, 58], [18, 160, 20, 58, "construct"], [18, 169, 20, 58], [18, 170, 20, 58, "o"], [18, 171, 20, 58], [18, 173, 20, 58, "e"], [18, 174, 20, 58], [18, 186, 20, 58, "_getPrototypeOf2"], [18, 202, 20, 58], [18, 203, 20, 58, "default"], [18, 210, 20, 58], [18, 212, 20, 58, "t"], [18, 213, 20, 58], [18, 215, 20, 58, "constructor"], [18, 226, 20, 58], [18, 230, 20, 58, "o"], [18, 231, 20, 58], [18, 232, 20, 58, "apply"], [18, 237, 20, 58], [18, 238, 20, 58, "t"], [18, 239, 20, 58], [18, 241, 20, 58, "e"], [18, 242, 20, 58], [19, 2, 20, 58], [19, 11, 20, 58, "_isNativeReflectConstruct"], [19, 37, 20, 58], [19, 51, 20, 58, "t"], [19, 52, 20, 58], [19, 56, 20, 58, "Boolean"], [19, 63, 20, 58], [19, 64, 20, 58, "prototype"], [19, 73, 20, 58], [19, 74, 20, 58, "valueOf"], [19, 81, 20, 58], [19, 82, 20, 58, "call"], [19, 86, 20, 58], [19, 87, 20, 58, "Reflect"], [19, 94, 20, 58], [19, 95, 20, 58, "construct"], [19, 104, 20, 58], [19, 105, 20, 58, "Boolean"], [19, 112, 20, 58], [19, 145, 20, 58, "t"], [19, 146, 20, 58], [19, 159, 20, 58, "_isNativeReflectConstruct"], [19, 184, 20, 58], [19, 196, 20, 58, "_isNativeReflectConstruct"], [19, 197, 20, 58], [19, 210, 20, 58, "t"], [19, 211, 20, 58], [20, 2, 20, 58], [20, 11, 20, 58, "_superPropGet"], [20, 25, 20, 58, "t"], [20, 26, 20, 58], [20, 28, 20, 58, "o"], [20, 29, 20, 58], [20, 31, 20, 58, "e"], [20, 32, 20, 58], [20, 34, 20, 58, "r"], [20, 35, 20, 58], [20, 43, 20, 58, "p"], [20, 44, 20, 58], [20, 51, 20, 58, "_get2"], [20, 56, 20, 58], [20, 57, 20, 58, "default"], [20, 64, 20, 58], [20, 70, 20, 58, "_getPrototypeOf2"], [20, 86, 20, 58], [20, 87, 20, 58, "default"], [20, 94, 20, 58], [20, 100, 20, 58, "r"], [20, 101, 20, 58], [20, 104, 20, 58, "t"], [20, 105, 20, 58], [20, 106, 20, 58, "prototype"], [20, 115, 20, 58], [20, 118, 20, 58, "t"], [20, 119, 20, 58], [20, 122, 20, 58, "o"], [20, 123, 20, 58], [20, 125, 20, 58, "e"], [20, 126, 20, 58], [20, 140, 20, 58, "r"], [20, 141, 20, 58], [20, 166, 20, 58, "p"], [20, 167, 20, 58], [20, 180, 20, 58, "t"], [20, 181, 20, 58], [20, 192, 20, 58, "p"], [20, 193, 20, 58], [20, 194, 20, 58, "apply"], [20, 199, 20, 58], [20, 200, 20, 58, "e"], [20, 201, 20, 58], [20, 203, 20, 58, "t"], [20, 204, 20, 58], [20, 211, 20, 58, "p"], [20, 212, 20, 58], [21, 2, 20, 58], [21, 6, 20, 58, "_processingStart"], [21, 22, 20, 58], [21, 42, 20, 58, "_classPrivateFieldLooseKey2"], [21, 69, 20, 58], [21, 70, 20, 58, "default"], [21, 77, 20, 58], [22, 2, 20, 58], [22, 6, 20, 58, "_processingEnd"], [22, 20, 20, 58], [22, 40, 20, 58, "_classPrivateFieldLooseKey2"], [22, 67, 20, 58], [22, 68, 20, 58, "default"], [22, 75, 20, 58], [23, 2, 20, 58], [23, 6, 20, 58, "_interactionId"], [23, 20, 20, 58], [23, 40, 20, 58, "_classPrivateFieldLooseKey2"], [23, 67, 20, 58], [23, 68, 20, 58, "default"], [23, 75, 20, 58], [24, 2, 20, 58], [24, 6, 30, 13, "PerformanceEventTiming"], [24, 28, 30, 35], [24, 31, 30, 35, "exports"], [24, 38, 30, 35], [24, 39, 30, 35, "PerformanceEventTiming"], [24, 61, 30, 35], [24, 87, 30, 35, "_PerformanceEntry"], [24, 104, 30, 35], [25, 4, 35, 2], [25, 13, 35, 2, "PerformanceEventTiming"], [25, 36, 35, 14, "init"], [25, 40, 42, 3], [25, 42, 42, 5], [26, 6, 42, 5], [26, 10, 42, 5, "_this"], [26, 15, 42, 5], [27, 6, 42, 5], [27, 10, 42, 5, "_classCallCheck2"], [27, 26, 42, 5], [27, 27, 42, 5, "default"], [27, 34, 42, 5], [27, 42, 42, 5, "PerformanceEventTiming"], [27, 64, 42, 5], [28, 6, 43, 4, "_this"], [28, 11, 43, 4], [28, 14, 43, 4, "_callSuper"], [28, 24, 43, 4], [28, 31, 43, 4, "PerformanceEventTiming"], [28, 53, 43, 4], [28, 56, 43, 10], [29, 8, 44, 6, "name"], [29, 12, 44, 10], [29, 14, 44, 12, "init"], [29, 18, 44, 16], [29, 19, 44, 17, "name"], [29, 23, 44, 21], [30, 8, 45, 6, "entryType"], [30, 17, 45, 15], [30, 19, 45, 17], [30, 26, 45, 24], [31, 8, 46, 6, "startTime"], [31, 17, 46, 15], [31, 19, 46, 17, "init"], [31, 23, 46, 21], [31, 24, 46, 22, "startTime"], [31, 33, 46, 31], [31, 37, 46, 35], [31, 38, 46, 36], [32, 8, 47, 6, "duration"], [32, 16, 47, 14], [32, 18, 47, 16, "init"], [32, 22, 47, 20], [32, 23, 47, 21, "duration"], [32, 31, 47, 29], [32, 35, 47, 33], [33, 6, 48, 4], [33, 7, 48, 5], [34, 6, 48, 7, "Object"], [34, 12, 48, 7], [34, 13, 48, 7, "defineProperty"], [34, 27, 48, 7], [34, 28, 48, 7, "_this"], [34, 33, 48, 7], [34, 35, 48, 7, "_processingStart"], [34, 51, 48, 7], [35, 8, 48, 7, "writable"], [35, 16, 48, 7], [36, 8, 48, 7, "value"], [36, 13, 48, 7], [37, 6, 48, 7], [38, 6, 48, 7, "Object"], [38, 12, 48, 7], [38, 13, 48, 7, "defineProperty"], [38, 27, 48, 7], [38, 28, 48, 7, "_this"], [38, 33, 48, 7], [38, 35, 48, 7, "_processingEnd"], [38, 49, 48, 7], [39, 8, 48, 7, "writable"], [39, 16, 48, 7], [40, 8, 48, 7, "value"], [40, 13, 48, 7], [41, 6, 48, 7], [42, 6, 48, 7, "Object"], [42, 12, 48, 7], [42, 13, 48, 7, "defineProperty"], [42, 27, 48, 7], [42, 28, 48, 7, "_this"], [42, 33, 48, 7], [42, 35, 48, 7, "_interactionId"], [42, 49, 48, 7], [43, 8, 48, 7, "writable"], [43, 16, 48, 7], [44, 8, 48, 7, "value"], [44, 13, 48, 7], [45, 6, 48, 7], [46, 6, 49, 4], [46, 10, 49, 4, "_classPrivateFieldLooseBase2"], [46, 38, 49, 4], [46, 39, 49, 4, "default"], [46, 46, 49, 4], [46, 48, 49, 4, "_this"], [46, 53, 49, 4], [46, 55, 49, 4, "_processingStart"], [46, 71, 49, 4], [46, 73, 49, 4, "_processingStart"], [46, 89, 49, 4], [46, 93, 49, 28, "init"], [46, 97, 49, 32], [46, 98, 49, 33, "processingStart"], [46, 113, 49, 48], [46, 117, 49, 52], [46, 118, 49, 53], [47, 6, 50, 4], [47, 10, 50, 4, "_classPrivateFieldLooseBase2"], [47, 38, 50, 4], [47, 39, 50, 4, "default"], [47, 46, 50, 4], [47, 48, 50, 4, "_this"], [47, 53, 50, 4], [47, 55, 50, 4, "_processingEnd"], [47, 69, 50, 4], [47, 71, 50, 4, "_processingEnd"], [47, 85, 50, 4], [47, 89, 50, 26, "init"], [47, 93, 50, 30], [47, 94, 50, 31, "processingEnd"], [47, 107, 50, 44], [47, 111, 50, 48], [47, 112, 50, 49], [48, 6, 51, 4], [48, 10, 51, 4, "_classPrivateFieldLooseBase2"], [48, 38, 51, 4], [48, 39, 51, 4, "default"], [48, 46, 51, 4], [48, 48, 51, 4, "_this"], [48, 53, 51, 4], [48, 55, 51, 4, "_interactionId"], [48, 69, 51, 4], [48, 71, 51, 4, "_interactionId"], [48, 85, 51, 4], [48, 89, 51, 26, "init"], [48, 93, 51, 30], [48, 94, 51, 31, "interactionId"], [48, 107, 51, 44], [48, 111, 51, 48], [48, 112, 51, 49], [49, 6, 51, 50], [49, 13, 51, 50, "_this"], [49, 18, 51, 50], [50, 4, 52, 2], [51, 4, 52, 3], [51, 8, 52, 3, "_inherits2"], [51, 18, 52, 3], [51, 19, 52, 3, "default"], [51, 26, 52, 3], [51, 28, 52, 3, "PerformanceEventTiming"], [51, 50, 52, 3], [51, 52, 52, 3, "_PerformanceEntry"], [51, 69, 52, 3], [52, 4, 52, 3], [52, 15, 52, 3, "_createClass2"], [52, 28, 52, 3], [52, 29, 52, 3, "default"], [52, 36, 52, 3], [52, 38, 52, 3, "PerformanceEventTiming"], [52, 60, 52, 3], [53, 6, 52, 3, "key"], [53, 9, 52, 3], [54, 6, 52, 3, "get"], [54, 9, 52, 3], [54, 11, 54, 2], [54, 20, 54, 2, "get"], [54, 21, 54, 2], [54, 23, 54, 45], [55, 8, 55, 4], [55, 19, 55, 4, "_classPrivateFieldLooseBase2"], [55, 47, 55, 4], [55, 48, 55, 4, "default"], [55, 55, 55, 4], [55, 57, 55, 11], [55, 61, 55, 15], [55, 63, 55, 15, "_processingStart"], [55, 79, 55, 15], [55, 81, 55, 15, "_processingStart"], [55, 97, 55, 15], [56, 6, 56, 2], [57, 4, 56, 3], [58, 6, 56, 3, "key"], [58, 9, 56, 3], [59, 6, 56, 3, "get"], [59, 9, 56, 3], [59, 11, 58, 2], [59, 20, 58, 2, "get"], [59, 21, 58, 2], [59, 23, 58, 43], [60, 8, 59, 4], [60, 19, 59, 4, "_classPrivateFieldLooseBase2"], [60, 47, 59, 4], [60, 48, 59, 4, "default"], [60, 55, 59, 4], [60, 57, 59, 11], [60, 61, 59, 15], [60, 63, 59, 15, "_processingEnd"], [60, 77, 59, 15], [60, 79, 59, 15, "_processingEnd"], [60, 93, 59, 15], [61, 6, 60, 2], [62, 4, 60, 3], [63, 6, 60, 3, "key"], [63, 9, 60, 3], [64, 6, 60, 3, "get"], [64, 9, 60, 3], [64, 11, 62, 2], [64, 20, 62, 2, "get"], [64, 21, 62, 2], [64, 23, 62, 30], [65, 8, 63, 4], [65, 19, 63, 4, "_classPrivateFieldLooseBase2"], [65, 47, 63, 4], [65, 48, 63, 4, "default"], [65, 55, 63, 4], [65, 57, 63, 11], [65, 61, 63, 15], [65, 63, 63, 15, "_interactionId"], [65, 77, 63, 15], [65, 79, 63, 15, "_interactionId"], [65, 93, 63, 15], [66, 6, 64, 2], [67, 4, 64, 3], [68, 6, 64, 3, "key"], [68, 9, 64, 3], [69, 6, 64, 3, "value"], [69, 11, 64, 3], [69, 13, 66, 2], [69, 22, 66, 2, "toJSON"], [69, 28, 66, 8, "toJSON"], [69, 29, 66, 8], [69, 31, 66, 39], [70, 8, 67, 4], [70, 15, 67, 11], [71, 10, 68, 6], [71, 13, 68, 6, "_superPropGet"], [71, 26, 68, 6], [71, 27, 68, 6, "PerformanceEventTiming"], [71, 49, 68, 6], [71, 73, 68, 23], [72, 10, 69, 6, "processingStart"], [72, 25, 69, 21], [72, 31, 69, 21, "_classPrivateFieldLooseBase2"], [72, 59, 69, 21], [72, 60, 69, 21, "default"], [72, 67, 69, 21], [72, 69, 69, 23], [72, 73, 69, 27], [72, 75, 69, 27, "_processingStart"], [72, 91, 69, 27], [72, 93, 69, 27, "_processingStart"], [72, 109, 69, 27], [72, 110, 69, 44], [73, 10, 70, 6, "processingEnd"], [73, 23, 70, 19], [73, 29, 70, 19, "_classPrivateFieldLooseBase2"], [73, 57, 70, 19], [73, 58, 70, 19, "default"], [73, 65, 70, 19], [73, 67, 70, 21], [73, 71, 70, 25], [73, 73, 70, 25, "_processingEnd"], [73, 87, 70, 25], [73, 89, 70, 25, "_processingEnd"], [73, 103, 70, 25], [73, 104, 70, 40], [74, 10, 71, 6, "interactionId"], [74, 23, 71, 19], [74, 29, 71, 19, "_classPrivateFieldLooseBase2"], [74, 57, 71, 19], [74, 58, 71, 19, "default"], [74, 65, 71, 19], [74, 67, 71, 21], [74, 71, 71, 25], [74, 73, 71, 25, "_interactionId"], [74, 87, 71, 25], [74, 89, 71, 25, "_interactionId"], [74, 103, 71, 25], [75, 8, 72, 4], [75, 9, 72, 5], [76, 6, 73, 2], [77, 4, 73, 3], [78, 2, 73, 3], [78, 4, 30, 44, "PerformanceEntry"], [78, 39, 30, 60], [79, 2, 82, 0], [79, 6, 82, 4, "cachedEventCounts"], [79, 23, 82, 43], [80, 2, 84, 0], [80, 11, 84, 9, "getCachedEventCounts"], [80, 31, 84, 29, "getCachedEventCounts"], [80, 32, 84, 29], [80, 34, 84, 53], [81, 4, 85, 2], [81, 8, 85, 6, "cachedEventCounts"], [81, 25, 85, 23], [81, 27, 85, 25], [82, 6, 86, 4], [82, 13, 86, 11, "cachedEventCounts"], [82, 30, 86, 28], [83, 4, 87, 2], [84, 4, 89, 2], [84, 8, 89, 6], [84, 9, 89, 7, "NativePerformance"], [84, 35, 89, 24], [84, 39, 89, 28], [84, 40, 89, 29, "NativePerformance"], [84, 66, 89, 46], [84, 68, 89, 48, "getEventCounts"], [84, 82, 89, 62], [84, 84, 89, 64], [85, 6, 90, 4], [85, 10, 90, 4, "warnNoNativePerformance"], [85, 44, 90, 27], [85, 46, 90, 28], [85, 47, 90, 29], [86, 6, 91, 4, "cachedEventCounts"], [86, 23, 91, 21], [86, 26, 91, 24], [86, 30, 91, 28, "Map"], [86, 33, 91, 31], [86, 34, 91, 32], [86, 35, 91, 33], [87, 6, 92, 4], [87, 13, 92, 11, "cachedEventCounts"], [87, 30, 92, 28], [88, 4, 93, 2], [89, 4, 95, 2], [89, 8, 95, 8, "eventCounts"], [89, 19, 95, 19], [89, 22, 95, 22], [89, 26, 95, 26, "Map"], [89, 29, 95, 29], [89, 30, 96, 4, "NativePerformance"], [89, 56, 96, 21], [89, 57, 96, 22, "getEventCounts"], [89, 71, 96, 36], [89, 74, 96, 39], [89, 75, 96, 40], [89, 79, 96, 44], [89, 81, 97, 2], [89, 82, 97, 3], [90, 4, 98, 2, "cachedEventCounts"], [90, 21, 98, 19], [90, 24, 98, 22, "eventCounts"], [90, 35, 98, 33], [91, 4, 101, 2, "global"], [91, 10, 101, 8], [91, 11, 101, 9, "queueMicrotask"], [91, 25, 101, 23], [91, 26, 101, 24], [91, 32, 101, 30], [92, 6, 106, 4, "cachedEventCounts"], [92, 23, 106, 21], [92, 26, 106, 24], [92, 30, 106, 28], [93, 4, 107, 2], [93, 5, 107, 3], [93, 6, 107, 4], [94, 4, 109, 2], [94, 11, 109, 9, "eventCounts"], [94, 22, 109, 20], [95, 2, 110, 0], [96, 2, 110, 1], [96, 6, 117, 13, "EventCounts"], [96, 17, 117, 24], [96, 20, 117, 24, "exports"], [96, 27, 117, 24], [96, 28, 117, 24, "EventCounts"], [96, 39, 117, 24], [97, 4, 117, 24], [97, 13, 117, 24, "EventCounts"], [97, 25, 117, 24], [98, 6, 117, 24], [98, 10, 117, 24, "_classCallCheck2"], [98, 26, 117, 24], [98, 27, 117, 24, "default"], [98, 34, 117, 24], [98, 42, 117, 24, "EventCounts"], [98, 53, 117, 24], [99, 4, 117, 24], [100, 4, 117, 24], [100, 15, 117, 24, "_createClass2"], [100, 28, 117, 24], [100, 29, 117, 24, "default"], [100, 36, 117, 24], [100, 38, 117, 24, "EventCounts"], [100, 49, 117, 24], [101, 6, 117, 24, "key"], [101, 9, 117, 24], [102, 6, 117, 24, "get"], [102, 9, 117, 24], [102, 11, 118, 2], [102, 20, 118, 2, "get"], [102, 21, 118, 2], [102, 23, 118, 21], [103, 8, 119, 4], [103, 15, 119, 11, "getCachedEventCounts"], [103, 35, 119, 31], [103, 36, 119, 32], [103, 37, 119, 33], [103, 38, 119, 34, "size"], [103, 42, 119, 38], [104, 6, 120, 2], [105, 4, 120, 3], [106, 6, 120, 3, "key"], [106, 9, 120, 3], [107, 6, 120, 3, "value"], [107, 11, 120, 3], [107, 13, 122, 2], [107, 22, 122, 2, "entries"], [107, 29, 122, 9, "entries"], [107, 30, 122, 9], [107, 32, 122, 40], [108, 8, 123, 4], [108, 15, 123, 11, "getCachedEventCounts"], [108, 35, 123, 31], [108, 36, 123, 32], [108, 37, 123, 33], [108, 38, 123, 34, "entries"], [108, 45, 123, 41], [108, 46, 123, 42], [108, 47, 123, 43], [109, 6, 124, 2], [110, 4, 124, 3], [111, 6, 124, 3, "key"], [111, 9, 124, 3], [112, 6, 124, 3, "value"], [112, 11, 124, 3], [112, 13, 126, 2], [112, 22, 126, 2, "for<PERSON>ach"], [112, 29, 126, 9, "for<PERSON>ach"], [112, 30, 126, 10, "callback"], [112, 38, 126, 50], [112, 40, 126, 58], [113, 8, 127, 4], [113, 15, 127, 11, "getCachedEventCounts"], [113, 35, 127, 31], [113, 36, 127, 32], [113, 37, 127, 33], [113, 38, 127, 34, "for<PERSON>ach"], [113, 45, 127, 41], [113, 46, 127, 42, "callback"], [113, 54, 127, 50], [113, 55, 127, 51], [114, 6, 128, 2], [115, 4, 128, 3], [116, 6, 128, 3, "key"], [116, 9, 128, 3], [117, 6, 128, 3, "value"], [117, 11, 128, 3], [117, 13, 130, 2], [117, 22, 130, 2, "get"], [117, 25, 130, 5, "get"], [117, 26, 130, 6, "key"], [117, 29, 130, 17], [117, 31, 130, 28], [118, 8, 131, 4], [118, 15, 131, 11, "getCachedEventCounts"], [118, 35, 131, 31], [118, 36, 131, 32], [118, 37, 131, 33], [118, 38, 131, 34, "get"], [118, 41, 131, 37], [118, 42, 131, 38, "key"], [118, 45, 131, 41], [118, 46, 131, 42], [119, 6, 132, 2], [120, 4, 132, 3], [121, 6, 132, 3, "key"], [121, 9, 132, 3], [122, 6, 132, 3, "value"], [122, 11, 132, 3], [122, 13, 134, 2], [122, 22, 134, 2, "has"], [122, 25, 134, 5, "has"], [122, 26, 134, 6, "key"], [122, 29, 134, 17], [122, 31, 134, 28], [123, 8, 135, 4], [123, 15, 135, 11, "getCachedEventCounts"], [123, 35, 135, 31], [123, 36, 135, 32], [123, 37, 135, 33], [123, 38, 135, 34, "has"], [123, 41, 135, 37], [123, 42, 135, 38, "key"], [123, 45, 135, 41], [123, 46, 135, 42], [124, 6, 136, 2], [125, 4, 136, 3], [126, 6, 136, 3, "key"], [126, 9, 136, 3], [127, 6, 136, 3, "value"], [127, 11, 136, 3], [127, 13, 138, 2], [127, 22, 138, 2, "keys"], [127, 26, 138, 6, "keys"], [127, 27, 138, 6], [127, 29, 138, 27], [128, 8, 139, 4], [128, 15, 139, 11, "getCachedEventCounts"], [128, 35, 139, 31], [128, 36, 139, 32], [128, 37, 139, 33], [128, 38, 139, 34, "keys"], [128, 42, 139, 38], [128, 43, 139, 39], [128, 44, 139, 40], [129, 6, 140, 2], [130, 4, 140, 3], [131, 6, 140, 3, "key"], [131, 9, 140, 3], [132, 6, 140, 3, "value"], [132, 11, 140, 3], [132, 13, 142, 2], [132, 22, 142, 2, "values"], [132, 28, 142, 8, "values"], [132, 29, 142, 8], [132, 31, 142, 29], [133, 8, 143, 4], [133, 15, 143, 11, "getCachedEventCounts"], [133, 35, 143, 31], [133, 36, 143, 32], [133, 37, 143, 33], [133, 38, 143, 34, "values"], [133, 44, 143, 40], [133, 45, 143, 41], [133, 46, 143, 42], [134, 6, 144, 2], [135, 4, 144, 3], [136, 2, 144, 3], [137, 0, 144, 3], [137, 3]], "functionMap": {"names": ["<global>", "PerformanceEventTiming", "PerformanceEventTiming#constructor", "PerformanceEventTiming#get__processingStart", "PerformanceEventTiming#get__processingEnd", "PerformanceEventTiming#get__interactionId", "PerformanceEventTiming#toJSON", "getCachedEventCounts", "global.queueMicrotask$argument_0", "EventCounts", "EventCounts#get__size", "EventCounts#entries", "EventCounts#forEach", "EventCounts#get", "EventCounts#has", "EventCounts#keys", "EventCounts#values"], "mappings": "AAA;OC6B;ECK;GDiB;EEE;GFE;EGE;GHE;EIE;GJE;EKE;GLO;CDC;AOU;wBCiB;GDM;CPG;OSO;ECC;GDE;EEE;GFE;EGE;GHE;EIE;GJE;EKE;GLE;EME;GNE;EOE;GPE"}}, "type": "js/module"}]}