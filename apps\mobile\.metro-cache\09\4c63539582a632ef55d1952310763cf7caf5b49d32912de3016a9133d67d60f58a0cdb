{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.registerWebGlobals = registerWebGlobals;\n  function registerWebGlobals() {}\n});", "lineCount": 7, "map": [[6, 2, 1, 7], [6, 11, 1, 16, "registerWebGlobals"], [6, 29, 1, 34, "registerWebGlobals"], [6, 30, 1, 34], [6, 32, 1, 37], [6, 33, 1, 38], [7, 0, 1, 39], [7, 3]], "functionMap": {"names": ["<global>", "registerWebGlobals"], "mappings": "AAA,OC,gCD"}}, "type": "js/module"}]}