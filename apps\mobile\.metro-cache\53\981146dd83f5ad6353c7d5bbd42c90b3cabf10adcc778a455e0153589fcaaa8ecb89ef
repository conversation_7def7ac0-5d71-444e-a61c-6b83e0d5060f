{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Image/Image", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 38}}], "key": "x+0sfJh3/nzfUxnJ5JIXsJmNMus=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "./LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 42}}], "key": "M6ofQu070ZUTf+Oq+Zz+7FQEgjs=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = LogBoxInspectorHeaderButton;\n  var _Image = _interopRequireDefault(require(_dependencyMap[1], \"../../Image/Image\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[2], \"../../StyleSheet/StyleSheet\"));\n  var _LogBoxButton = _interopRequireDefault(require(_dependencyMap[3], \"./LogBoxButton\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[4], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[5], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[6], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxInspectorHeaderButton.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var backgroundForLevel = level => ({\n    warn: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getWarningDarkColor()\n    },\n    error: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getErrorDarkColor()\n    },\n    fatal: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getFatalDarkColor()\n    },\n    syntax: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getFatalDarkColor()\n    }\n  })[level];\n  function LogBoxInspectorHeaderButton(props) {\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxButton.default, {\n      id: props.id,\n      backgroundColor: backgroundForLevel(props.level),\n      onPress: props.disabled ? null : props.onPress,\n      style: styles.button,\n      children: props.disabled ? null : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Image.default, {\n        source: props.image,\n        style: styles.buttonImage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _StyleSheet.default.create({\n    button: {\n      alignItems: 'center',\n      aspectRatio: 1,\n      justifyContent: 'center',\n      marginTop: 5,\n      marginRight: 6,\n      marginLeft: 6,\n      marginBottom: -8,\n      borderRadius: 3\n    },\n    buttonImage: {\n      height: 14,\n      width: 8,\n      tintColor: LogBoxStyle.getTextColor()\n    }\n  });\n});", "lineCount": 70, "map": [[7, 2, 14, 0], [7, 6, 14, 0, "_Image"], [7, 12, 14, 0], [7, 15, 14, 0, "_interopRequireDefault"], [7, 37, 14, 0], [7, 38, 14, 0, "require"], [7, 45, 14, 0], [7, 46, 14, 0, "_dependencyMap"], [7, 60, 14, 0], [8, 2, 15, 0], [8, 6, 15, 0, "_StyleSheet"], [8, 17, 15, 0], [8, 20, 15, 0, "_interopRequireDefault"], [8, 42, 15, 0], [8, 43, 15, 0, "require"], [8, 50, 15, 0], [8, 51, 15, 0, "_dependencyMap"], [8, 65, 15, 0], [9, 2, 16, 0], [9, 6, 16, 0, "_LogBoxButton"], [9, 19, 16, 0], [9, 22, 16, 0, "_interopRequireDefault"], [9, 44, 16, 0], [9, 45, 16, 0, "require"], [9, 52, 16, 0], [9, 53, 16, 0, "_dependencyMap"], [9, 67, 16, 0], [10, 2, 17, 0], [10, 6, 17, 0, "LogBoxStyle"], [10, 17, 17, 0], [10, 20, 17, 0, "_interopRequireWildcard"], [10, 43, 17, 0], [10, 44, 17, 0, "require"], [10, 51, 17, 0], [10, 52, 17, 0, "_dependencyMap"], [10, 66, 17, 0], [11, 2, 18, 0], [11, 6, 18, 0, "React"], [11, 11, 18, 0], [11, 14, 18, 0, "_interopRequireWildcard"], [11, 37, 18, 0], [11, 38, 18, 0, "require"], [11, 45, 18, 0], [11, 46, 18, 0, "_dependencyMap"], [11, 60, 18, 0], [12, 2, 18, 31], [12, 6, 18, 31, "_jsxDevRuntime"], [12, 20, 18, 31], [12, 23, 18, 31, "require"], [12, 30, 18, 31], [12, 31, 18, 31, "_dependencyMap"], [12, 45, 18, 31], [13, 2, 18, 31], [13, 6, 18, 31, "_jsxFileName"], [13, 18, 18, 31], [14, 2, 18, 31], [14, 11, 18, 31, "_interopRequireWildcard"], [14, 35, 18, 31, "e"], [14, 36, 18, 31], [14, 38, 18, 31, "t"], [14, 39, 18, 31], [14, 68, 18, 31, "WeakMap"], [14, 75, 18, 31], [14, 81, 18, 31, "r"], [14, 82, 18, 31], [14, 89, 18, 31, "WeakMap"], [14, 96, 18, 31], [14, 100, 18, 31, "n"], [14, 101, 18, 31], [14, 108, 18, 31, "WeakMap"], [14, 115, 18, 31], [14, 127, 18, 31, "_interopRequireWildcard"], [14, 150, 18, 31], [14, 162, 18, 31, "_interopRequireWildcard"], [14, 163, 18, 31, "e"], [14, 164, 18, 31], [14, 166, 18, 31, "t"], [14, 167, 18, 31], [14, 176, 18, 31, "t"], [14, 177, 18, 31], [14, 181, 18, 31, "e"], [14, 182, 18, 31], [14, 186, 18, 31, "e"], [14, 187, 18, 31], [14, 188, 18, 31, "__esModule"], [14, 198, 18, 31], [14, 207, 18, 31, "e"], [14, 208, 18, 31], [14, 214, 18, 31, "o"], [14, 215, 18, 31], [14, 217, 18, 31, "i"], [14, 218, 18, 31], [14, 220, 18, 31, "f"], [14, 221, 18, 31], [14, 226, 18, 31, "__proto__"], [14, 235, 18, 31], [14, 243, 18, 31, "default"], [14, 250, 18, 31], [14, 252, 18, 31, "e"], [14, 253, 18, 31], [14, 270, 18, 31, "e"], [14, 271, 18, 31], [14, 294, 18, 31, "e"], [14, 295, 18, 31], [14, 320, 18, 31, "e"], [14, 321, 18, 31], [14, 330, 18, 31, "f"], [14, 331, 18, 31], [14, 337, 18, 31, "o"], [14, 338, 18, 31], [14, 341, 18, 31, "t"], [14, 342, 18, 31], [14, 345, 18, 31, "n"], [14, 346, 18, 31], [14, 349, 18, 31, "r"], [14, 350, 18, 31], [14, 358, 18, 31, "o"], [14, 359, 18, 31], [14, 360, 18, 31, "has"], [14, 363, 18, 31], [14, 364, 18, 31, "e"], [14, 365, 18, 31], [14, 375, 18, 31, "o"], [14, 376, 18, 31], [14, 377, 18, 31, "get"], [14, 380, 18, 31], [14, 381, 18, 31, "e"], [14, 382, 18, 31], [14, 385, 18, 31, "o"], [14, 386, 18, 31], [14, 387, 18, 31, "set"], [14, 390, 18, 31], [14, 391, 18, 31, "e"], [14, 392, 18, 31], [14, 394, 18, 31, "f"], [14, 395, 18, 31], [14, 409, 18, 31, "_t"], [14, 411, 18, 31], [14, 415, 18, 31, "e"], [14, 416, 18, 31], [14, 432, 18, 31, "_t"], [14, 434, 18, 31], [14, 441, 18, 31, "hasOwnProperty"], [14, 455, 18, 31], [14, 456, 18, 31, "call"], [14, 460, 18, 31], [14, 461, 18, 31, "e"], [14, 462, 18, 31], [14, 464, 18, 31, "_t"], [14, 466, 18, 31], [14, 473, 18, 31, "i"], [14, 474, 18, 31], [14, 478, 18, 31, "o"], [14, 479, 18, 31], [14, 482, 18, 31, "Object"], [14, 488, 18, 31], [14, 489, 18, 31, "defineProperty"], [14, 503, 18, 31], [14, 508, 18, 31, "Object"], [14, 514, 18, 31], [14, 515, 18, 31, "getOwnPropertyDescriptor"], [14, 539, 18, 31], [14, 540, 18, 31, "e"], [14, 541, 18, 31], [14, 543, 18, 31, "_t"], [14, 545, 18, 31], [14, 552, 18, 31, "i"], [14, 553, 18, 31], [14, 554, 18, 31, "get"], [14, 557, 18, 31], [14, 561, 18, 31, "i"], [14, 562, 18, 31], [14, 563, 18, 31, "set"], [14, 566, 18, 31], [14, 570, 18, 31, "o"], [14, 571, 18, 31], [14, 572, 18, 31, "f"], [14, 573, 18, 31], [14, 575, 18, 31, "_t"], [14, 577, 18, 31], [14, 579, 18, 31, "i"], [14, 580, 18, 31], [14, 584, 18, 31, "f"], [14, 585, 18, 31], [14, 586, 18, 31, "_t"], [14, 588, 18, 31], [14, 592, 18, 31, "e"], [14, 593, 18, 31], [14, 594, 18, 31, "_t"], [14, 596, 18, 31], [14, 607, 18, 31, "f"], [14, 608, 18, 31], [14, 613, 18, 31, "e"], [14, 614, 18, 31], [14, 616, 18, 31, "t"], [14, 617, 18, 31], [15, 2, 20, 0], [15, 6, 20, 6, "backgroundForLevel"], [15, 24, 20, 24], [15, 27, 20, 28, "level"], [15, 32, 20, 43], [15, 36, 21, 2], [15, 37, 21, 3], [16, 4, 22, 4, "warn"], [16, 8, 22, 8], [16, 10, 22, 10], [17, 6, 23, 6, "default"], [17, 13, 23, 13], [17, 15, 23, 15], [17, 28, 23, 28], [18, 6, 24, 6, "pressed"], [18, 13, 24, 13], [18, 15, 24, 15, "LogBoxStyle"], [18, 26, 24, 26], [18, 27, 24, 27, "getWarningDarkColor"], [18, 46, 24, 46], [18, 47, 24, 47], [19, 4, 25, 4], [19, 5, 25, 5], [20, 4, 26, 4, "error"], [20, 9, 26, 9], [20, 11, 26, 11], [21, 6, 27, 6, "default"], [21, 13, 27, 13], [21, 15, 27, 15], [21, 28, 27, 28], [22, 6, 28, 6, "pressed"], [22, 13, 28, 13], [22, 15, 28, 15, "LogBoxStyle"], [22, 26, 28, 26], [22, 27, 28, 27, "getErrorDarkColor"], [22, 44, 28, 44], [22, 45, 28, 45], [23, 4, 29, 4], [23, 5, 29, 5], [24, 4, 30, 4, "fatal"], [24, 9, 30, 9], [24, 11, 30, 11], [25, 6, 31, 6, "default"], [25, 13, 31, 13], [25, 15, 31, 15], [25, 28, 31, 28], [26, 6, 32, 6, "pressed"], [26, 13, 32, 13], [26, 15, 32, 15, "LogBoxStyle"], [26, 26, 32, 26], [26, 27, 32, 27, "getFatalDarkColor"], [26, 44, 32, 44], [26, 45, 32, 45], [27, 4, 33, 4], [27, 5, 33, 5], [28, 4, 34, 4, "syntax"], [28, 10, 34, 10], [28, 12, 34, 12], [29, 6, 35, 6, "default"], [29, 13, 35, 13], [29, 15, 35, 15], [29, 28, 35, 28], [30, 6, 36, 6, "pressed"], [30, 13, 36, 13], [30, 15, 36, 15, "LogBoxStyle"], [30, 26, 36, 26], [30, 27, 36, 27, "getFatalDarkColor"], [30, 44, 36, 44], [30, 45, 36, 45], [31, 4, 37, 4], [32, 2, 38, 2], [32, 3, 38, 3], [32, 5, 38, 5, "level"], [32, 10, 38, 10], [32, 11, 38, 11], [33, 2, 40, 15], [33, 11, 40, 24, "LogBoxInspectorHeaderButton"], [33, 38, 40, 51, "LogBoxInspectorHeaderButton"], [33, 39, 41, 2, "props"], [33, 44, 47, 4], [33, 46, 48, 14], [34, 4, 49, 2], [34, 24, 50, 4], [34, 28, 50, 4, "_jsxDevRuntime"], [34, 42, 50, 4], [34, 43, 50, 4, "jsxDEV"], [34, 49, 50, 4], [34, 51, 50, 5, "_LogBoxButton"], [34, 64, 50, 5], [34, 65, 50, 5, "default"], [34, 72, 50, 17], [35, 6, 51, 6, "id"], [35, 8, 51, 8], [35, 10, 51, 10, "props"], [35, 15, 51, 15], [35, 16, 51, 16, "id"], [35, 18, 51, 19], [36, 6, 52, 6, "backgroundColor"], [36, 21, 52, 21], [36, 23, 52, 23, "backgroundForLevel"], [36, 41, 52, 41], [36, 42, 52, 42, "props"], [36, 47, 52, 47], [36, 48, 52, 48, "level"], [36, 53, 52, 53], [36, 54, 52, 55], [37, 6, 53, 6, "onPress"], [37, 13, 53, 13], [37, 15, 53, 15, "props"], [37, 20, 53, 20], [37, 21, 53, 21, "disabled"], [37, 29, 53, 29], [37, 32, 53, 32], [37, 36, 53, 36], [37, 39, 53, 39, "props"], [37, 44, 53, 44], [37, 45, 53, 45, "onPress"], [37, 52, 53, 53], [38, 6, 54, 6, "style"], [38, 11, 54, 11], [38, 13, 54, 13, "styles"], [38, 19, 54, 19], [38, 20, 54, 20, "button"], [38, 26, 54, 27], [39, 6, 54, 27, "children"], [39, 14, 54, 27], [39, 16, 55, 7, "props"], [39, 21, 55, 12], [39, 22, 55, 13, "disabled"], [39, 30, 55, 21], [39, 33, 55, 24], [39, 37, 55, 28], [39, 53, 56, 8], [39, 57, 56, 8, "_jsxDevRuntime"], [39, 71, 56, 8], [39, 72, 56, 8, "jsxDEV"], [39, 78, 56, 8], [39, 80, 56, 9, "_Image"], [39, 86, 56, 9], [39, 87, 56, 9, "default"], [39, 94, 56, 14], [40, 8, 56, 15, "source"], [40, 14, 56, 21], [40, 16, 56, 23, "props"], [40, 21, 56, 28], [40, 22, 56, 29, "image"], [40, 27, 56, 35], [41, 8, 56, 36, "style"], [41, 13, 56, 41], [41, 15, 56, 43, "styles"], [41, 21, 56, 49], [41, 22, 56, 50, "buttonImage"], [42, 6, 56, 62], [43, 8, 56, 62, "fileName"], [43, 16, 56, 62], [43, 18, 56, 62, "_jsxFileName"], [43, 30, 56, 62], [44, 8, 56, 62, "lineNumber"], [44, 18, 56, 62], [45, 8, 56, 62, "columnNumber"], [45, 20, 56, 62], [46, 6, 56, 62], [46, 13, 56, 64], [47, 4, 57, 7], [48, 6, 57, 7, "fileName"], [48, 14, 57, 7], [48, 16, 57, 7, "_jsxFileName"], [48, 28, 57, 7], [49, 6, 57, 7, "lineNumber"], [49, 16, 57, 7], [50, 6, 57, 7, "columnNumber"], [50, 18, 57, 7], [51, 4, 57, 7], [51, 11, 58, 18], [51, 12, 58, 19], [52, 2, 60, 0], [53, 2, 62, 0], [53, 6, 62, 6, "styles"], [53, 12, 62, 12], [53, 15, 62, 15, "StyleSheet"], [53, 34, 62, 25], [53, 35, 62, 26, "create"], [53, 41, 62, 32], [53, 42, 62, 33], [54, 4, 63, 2, "button"], [54, 10, 63, 8], [54, 12, 63, 10], [55, 6, 64, 4, "alignItems"], [55, 16, 64, 14], [55, 18, 64, 16], [55, 26, 64, 24], [56, 6, 65, 4, "aspectRatio"], [56, 17, 65, 15], [56, 19, 65, 17], [56, 20, 65, 18], [57, 6, 66, 4, "justifyContent"], [57, 20, 66, 18], [57, 22, 66, 20], [57, 30, 66, 28], [58, 6, 67, 4, "marginTop"], [58, 15, 67, 13], [58, 17, 67, 15], [58, 18, 67, 16], [59, 6, 68, 4, "marginRight"], [59, 17, 68, 15], [59, 19, 68, 17], [59, 20, 68, 18], [60, 6, 69, 4, "marginLeft"], [60, 16, 69, 14], [60, 18, 69, 16], [60, 19, 69, 17], [61, 6, 70, 4, "marginBottom"], [61, 18, 70, 16], [61, 20, 70, 18], [61, 21, 70, 19], [61, 22, 70, 20], [62, 6, 71, 4, "borderRadius"], [62, 18, 71, 16], [62, 20, 71, 18], [63, 4, 72, 2], [63, 5, 72, 3], [64, 4, 73, 2, "buttonImage"], [64, 15, 73, 13], [64, 17, 73, 15], [65, 6, 74, 4, "height"], [65, 12, 74, 10], [65, 14, 74, 12], [65, 16, 74, 14], [66, 6, 75, 4, "width"], [66, 11, 75, 9], [66, 13, 75, 11], [66, 14, 75, 12], [67, 6, 76, 4, "tintColor"], [67, 15, 76, 13], [67, 17, 76, 15, "LogBoxStyle"], [67, 28, 76, 26], [67, 29, 76, 27, "getTextColor"], [67, 41, 76, 39], [67, 42, 76, 40], [68, 4, 77, 2], [69, 2, 78, 0], [69, 3, 78, 1], [69, 4, 78, 2], [70, 0, 78, 3], [70, 3]], "functionMap": {"names": ["<global>", "backgroundForLevel", "LogBoxInspectorHeaderButton"], "mappings": "AAA;2BCmB;WDkB;eEE;CFoB"}}, "type": "js/module"}]}