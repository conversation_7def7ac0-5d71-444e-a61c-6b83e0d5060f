{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  /**\n   * Compare two records with primitive values as the content.\n   */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isRecordEqual = isRecordEqual;\n  function isRecordEqual(a, b) {\n    if (a === b) {\n      return true;\n    }\n    var aKeys = Object.keys(a);\n    var bKeys = Object.keys(b);\n    if (aKeys.length !== bKeys.length) {\n      return false;\n    }\n    return aKeys.every(key => a[key] === b[key]);\n  }\n});", "lineCount": 22, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 2, 3, 0, "Object"], [7, 8, 3, 0], [7, 9, 3, 0, "defineProperty"], [7, 23, 3, 0], [7, 24, 3, 0, "exports"], [7, 31, 3, 0], [8, 4, 3, 0, "value"], [8, 9, 3, 0], [9, 2, 3, 0], [10, 2, 3, 0, "exports"], [10, 9, 3, 0], [10, 10, 3, 0, "isRecordEqual"], [10, 23, 3, 0], [10, 26, 3, 0, "isRecordEqual"], [10, 39, 3, 0], [11, 2, 6, 7], [11, 11, 6, 16, "isRecordEqual"], [11, 24, 6, 29, "isRecordEqual"], [11, 25, 6, 30, "a"], [11, 26, 6, 31], [11, 28, 6, 33, "b"], [11, 29, 6, 34], [11, 31, 6, 36], [12, 4, 7, 2], [12, 8, 7, 6, "a"], [12, 9, 7, 7], [12, 14, 7, 12, "b"], [12, 15, 7, 13], [12, 17, 7, 15], [13, 6, 8, 4], [13, 13, 8, 11], [13, 17, 8, 15], [14, 4, 9, 2], [15, 4, 10, 2], [15, 8, 10, 8, "a<PERSON><PERSON><PERSON>"], [15, 13, 10, 13], [15, 16, 10, 16, "Object"], [15, 22, 10, 22], [15, 23, 10, 23, "keys"], [15, 27, 10, 27], [15, 28, 10, 28, "a"], [15, 29, 10, 29], [15, 30, 10, 30], [16, 4, 11, 2], [16, 8, 11, 8, "b<PERSON><PERSON><PERSON>"], [16, 13, 11, 13], [16, 16, 11, 16, "Object"], [16, 22, 11, 22], [16, 23, 11, 23, "keys"], [16, 27, 11, 27], [16, 28, 11, 28, "b"], [16, 29, 11, 29], [16, 30, 11, 30], [17, 4, 12, 2], [17, 8, 12, 6, "a<PERSON><PERSON><PERSON>"], [17, 13, 12, 11], [17, 14, 12, 12, "length"], [17, 20, 12, 18], [17, 25, 12, 23, "b<PERSON><PERSON><PERSON>"], [17, 30, 12, 28], [17, 31, 12, 29, "length"], [17, 37, 12, 35], [17, 39, 12, 37], [18, 6, 13, 4], [18, 13, 13, 11], [18, 18, 13, 16], [19, 4, 14, 2], [20, 4, 15, 2], [20, 11, 15, 9, "a<PERSON><PERSON><PERSON>"], [20, 16, 15, 14], [20, 17, 15, 15, "every"], [20, 22, 15, 20], [20, 23, 15, 21, "key"], [20, 26, 15, 24], [20, 30, 15, 28, "a"], [20, 31, 15, 29], [20, 32, 15, 30, "key"], [20, 35, 15, 33], [20, 36, 15, 34], [20, 41, 15, 39, "b"], [20, 42, 15, 40], [20, 43, 15, 41, "key"], [20, 46, 15, 44], [20, 47, 15, 45], [20, 48, 15, 46], [21, 2, 16, 0], [22, 0, 16, 1], [22, 3]], "functionMap": {"names": ["<global>", "isRecordEqual", "aKeys.every$argument_0"], "mappings": "AAA;OCK;qBCS,wBD;CDC"}}, "type": "js/module"}]}