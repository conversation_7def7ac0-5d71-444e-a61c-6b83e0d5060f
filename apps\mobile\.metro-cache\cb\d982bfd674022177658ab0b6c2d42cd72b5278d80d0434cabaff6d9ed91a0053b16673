{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./Blob", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 26}}], "key": "OVta6fzyPXLrj3BMvCvFH7pWZHw=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 18}, "end": {"line": 17, "column": 38}}], "key": "oQpL0Es3H146KnQH9ygFeHrzVP4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _Blob2 = _interopRequireDefault(require(_dependencyMap[6], \"./Blob\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var invariant = require(_dependencyMap[7], \"invariant\");\n  var File = /*#__PURE__*/function (_Blob) {\n    function File(parts, name, options) {\n      var _this;\n      (0, _classCallCheck2.default)(this, File);\n      invariant(parts != null && name != null, 'Failed to construct `File`: Must pass both `parts` and `name` arguments.');\n      _this = _callSuper(this, File, [parts, options]);\n      _this.data.name = name;\n      return _this;\n    }\n    (0, _inherits2.default)(File, _Blob);\n    return (0, _createClass2.default)(File, [{\n      key: \"name\",\n      get: function () {\n        invariant(this.data.name != null, 'Files must have a name set.');\n        return this.data.name;\n      }\n    }, {\n      key: \"lastModified\",\n      get: function () {\n        return this.data.lastModified || 0;\n      }\n    }]);\n  }(_Blob2.default);\n  var _default = exports.default = File;\n});", "lineCount": 42, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_classCallCheck2"], [9, 22, 11, 13], [9, 25, 11, 13, "_interopRequireDefault"], [9, 47, 11, 13], [9, 48, 11, 13, "require"], [9, 55, 11, 13], [9, 56, 11, 13, "_dependencyMap"], [9, 70, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_createClass2"], [10, 19, 11, 13], [10, 22, 11, 13, "_interopRequireDefault"], [10, 44, 11, 13], [10, 45, 11, 13, "require"], [10, 52, 11, 13], [10, 53, 11, 13, "_dependencyMap"], [10, 67, 11, 13], [11, 2, 11, 13], [11, 6, 11, 13, "_possibleConstructorReturn2"], [11, 33, 11, 13], [11, 36, 11, 13, "_interopRequireDefault"], [11, 58, 11, 13], [11, 59, 11, 13, "require"], [11, 66, 11, 13], [11, 67, 11, 13, "_dependencyMap"], [11, 81, 11, 13], [12, 2, 11, 13], [12, 6, 11, 13, "_getPrototypeOf2"], [12, 22, 11, 13], [12, 25, 11, 13, "_interopRequireDefault"], [12, 47, 11, 13], [12, 48, 11, 13, "require"], [12, 55, 11, 13], [12, 56, 11, 13, "_dependencyMap"], [12, 70, 11, 13], [13, 2, 11, 13], [13, 6, 11, 13, "_inherits2"], [13, 16, 11, 13], [13, 19, 11, 13, "_interopRequireDefault"], [13, 41, 11, 13], [13, 42, 11, 13, "require"], [13, 49, 11, 13], [13, 50, 11, 13, "_dependencyMap"], [13, 64, 11, 13], [14, 2, 15, 0], [14, 6, 15, 0, "_Blob2"], [14, 12, 15, 0], [14, 15, 15, 0, "_interopRequireDefault"], [14, 37, 15, 0], [14, 38, 15, 0, "require"], [14, 45, 15, 0], [14, 46, 15, 0, "_dependencyMap"], [14, 60, 15, 0], [15, 2, 15, 26], [15, 11, 15, 26, "_callSuper"], [15, 22, 15, 26, "t"], [15, 23, 15, 26], [15, 25, 15, 26, "o"], [15, 26, 15, 26], [15, 28, 15, 26, "e"], [15, 29, 15, 26], [15, 40, 15, 26, "o"], [15, 41, 15, 26], [15, 48, 15, 26, "_getPrototypeOf2"], [15, 64, 15, 26], [15, 65, 15, 26, "default"], [15, 72, 15, 26], [15, 74, 15, 26, "o"], [15, 75, 15, 26], [15, 82, 15, 26, "_possibleConstructorReturn2"], [15, 109, 15, 26], [15, 110, 15, 26, "default"], [15, 117, 15, 26], [15, 119, 15, 26, "t"], [15, 120, 15, 26], [15, 122, 15, 26, "_isNativeReflectConstruct"], [15, 147, 15, 26], [15, 152, 15, 26, "Reflect"], [15, 159, 15, 26], [15, 160, 15, 26, "construct"], [15, 169, 15, 26], [15, 170, 15, 26, "o"], [15, 171, 15, 26], [15, 173, 15, 26, "e"], [15, 174, 15, 26], [15, 186, 15, 26, "_getPrototypeOf2"], [15, 202, 15, 26], [15, 203, 15, 26, "default"], [15, 210, 15, 26], [15, 212, 15, 26, "t"], [15, 213, 15, 26], [15, 215, 15, 26, "constructor"], [15, 226, 15, 26], [15, 230, 15, 26, "o"], [15, 231, 15, 26], [15, 232, 15, 26, "apply"], [15, 237, 15, 26], [15, 238, 15, 26, "t"], [15, 239, 15, 26], [15, 241, 15, 26, "e"], [15, 242, 15, 26], [16, 2, 15, 26], [16, 11, 15, 26, "_isNativeReflectConstruct"], [16, 37, 15, 26], [16, 51, 15, 26, "t"], [16, 52, 15, 26], [16, 56, 15, 26, "Boolean"], [16, 63, 15, 26], [16, 64, 15, 26, "prototype"], [16, 73, 15, 26], [16, 74, 15, 26, "valueOf"], [16, 81, 15, 26], [16, 82, 15, 26, "call"], [16, 86, 15, 26], [16, 87, 15, 26, "Reflect"], [16, 94, 15, 26], [16, 95, 15, 26, "construct"], [16, 104, 15, 26], [16, 105, 15, 26, "Boolean"], [16, 112, 15, 26], [16, 145, 15, 26, "t"], [16, 146, 15, 26], [16, 159, 15, 26, "_isNativeReflectConstruct"], [16, 184, 15, 26], [16, 196, 15, 26, "_isNativeReflectConstruct"], [16, 197, 15, 26], [16, 210, 15, 26, "t"], [16, 211, 15, 26], [17, 2, 17, 0], [17, 6, 17, 6, "invariant"], [17, 15, 17, 15], [17, 18, 17, 18, "require"], [17, 25, 17, 25], [17, 26, 17, 25, "_dependencyMap"], [17, 40, 17, 25], [17, 56, 17, 37], [17, 57, 17, 38], [18, 2, 17, 39], [18, 6, 22, 6, "File"], [18, 10, 22, 10], [18, 36, 22, 10, "_Blob"], [18, 41, 22, 10], [19, 4, 26, 2], [19, 13, 26, 2, "File"], [19, 18, 27, 4, "parts"], [19, 23, 27, 31], [19, 25, 28, 4, "name"], [19, 29, 28, 16], [19, 31, 29, 4, "options"], [19, 38, 29, 25], [19, 40, 30, 4], [20, 6, 30, 4], [20, 10, 30, 4, "_this"], [20, 15, 30, 4], [21, 6, 30, 4], [21, 10, 30, 4, "_classCallCheck2"], [21, 26, 30, 4], [21, 27, 30, 4, "default"], [21, 34, 30, 4], [21, 42, 30, 4, "File"], [21, 46, 30, 4], [22, 6, 31, 4, "invariant"], [22, 15, 31, 13], [22, 16, 32, 6, "parts"], [22, 21, 32, 11], [22, 25, 32, 15], [22, 29, 32, 19], [22, 33, 32, 23, "name"], [22, 37, 32, 27], [22, 41, 32, 31], [22, 45, 32, 35], [22, 47, 33, 6], [22, 121, 34, 4], [22, 122, 34, 5], [23, 6, 36, 4, "_this"], [23, 11, 36, 4], [23, 14, 36, 4, "_callSuper"], [23, 24, 36, 4], [23, 31, 36, 4, "File"], [23, 35, 36, 4], [23, 38, 36, 10, "parts"], [23, 43, 36, 15], [23, 45, 36, 17, "options"], [23, 52, 36, 24], [24, 6, 37, 4, "_this"], [24, 11, 37, 4], [24, 12, 37, 9, "data"], [24, 16, 37, 13], [24, 17, 37, 14, "name"], [24, 21, 37, 18], [24, 24, 37, 21, "name"], [24, 28, 37, 25], [25, 6, 37, 26], [25, 13, 37, 26, "_this"], [25, 18, 37, 26], [26, 4, 38, 2], [27, 4, 38, 3], [27, 8, 38, 3, "_inherits2"], [27, 18, 38, 3], [27, 19, 38, 3, "default"], [27, 26, 38, 3], [27, 28, 38, 3, "File"], [27, 32, 38, 3], [27, 34, 38, 3, "_Blob"], [27, 39, 38, 3], [28, 4, 38, 3], [28, 15, 38, 3, "_createClass2"], [28, 28, 38, 3], [28, 29, 38, 3, "default"], [28, 36, 38, 3], [28, 38, 38, 3, "File"], [28, 42, 38, 3], [29, 6, 38, 3, "key"], [29, 9, 38, 3], [30, 6, 38, 3, "get"], [30, 9, 38, 3], [30, 11, 43, 2], [30, 20, 43, 2, "get"], [30, 21, 43, 2], [30, 23, 43, 21], [31, 8, 44, 4, "invariant"], [31, 17, 44, 13], [31, 18, 44, 14], [31, 22, 44, 18], [31, 23, 44, 19, "data"], [31, 27, 44, 23], [31, 28, 44, 24, "name"], [31, 32, 44, 28], [31, 36, 44, 32], [31, 40, 44, 36], [31, 42, 44, 38], [31, 71, 44, 67], [31, 72, 44, 68], [32, 8, 45, 4], [32, 15, 45, 11], [32, 19, 45, 15], [32, 20, 45, 16, "data"], [32, 24, 45, 20], [32, 25, 45, 21, "name"], [32, 29, 45, 25], [33, 6, 46, 2], [34, 4, 46, 3], [35, 6, 46, 3, "key"], [35, 9, 46, 3], [36, 6, 46, 3, "get"], [36, 9, 46, 3], [36, 11, 51, 2], [36, 20, 51, 2, "get"], [36, 21, 51, 2], [36, 23, 51, 29], [37, 8, 52, 4], [37, 15, 52, 11], [37, 19, 52, 15], [37, 20, 52, 16, "data"], [37, 24, 52, 20], [37, 25, 52, 21, "lastModified"], [37, 37, 52, 33], [37, 41, 52, 37], [37, 42, 52, 38], [38, 6, 53, 2], [39, 4, 53, 3], [40, 2, 53, 3], [40, 4, 22, 19, "Blob"], [40, 18, 22, 23], [41, 2, 22, 23], [41, 6, 22, 23, "_default"], [41, 14, 22, 23], [41, 17, 22, 23, "exports"], [41, 24, 22, 23], [41, 25, 22, 23, "default"], [41, 32, 22, 23], [41, 35, 56, 15, "File"], [41, 39, 56, 19], [42, 0, 56, 19], [42, 3]], "functionMap": {"names": ["<global>", "File", "constructor", "get__name", "get__lastModified"], "mappings": "AAA;ACqB;ECI;GDY;EEK;GFG;EGK;GHE;CDC"}}, "type": "js/module"}]}