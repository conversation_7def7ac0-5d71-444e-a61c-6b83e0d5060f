{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[3], \"invariant\"));\n  var ChildListCollection = exports.default = /*#__PURE__*/function () {\n    function ChildListCollection() {\n      (0, _classCallCheck2.default)(this, ChildListCollection);\n      this._cellKeyToChildren = new Map();\n      this._childrenToCellKey = new Map();\n    }\n    return (0, _createClass2.default)(ChildListCollection, [{\n      key: \"add\",\n      value: function add(list, cellKey) {\n        (0, _invariant.default)(!this._childrenToCellKey.has(list), 'Trying to add already present child list');\n        var cellLists = this._cellKeyToChildren.get(cellKey) ?? new Set();\n        cellLists.add(list);\n        this._cellKeyToChildren.set(cellKey, cellLists);\n        this._childrenToCellKey.set(list, cellKey);\n      }\n    }, {\n      key: \"remove\",\n      value: function remove(list) {\n        var cellKey = this._childrenToCellKey.get(list);\n        (0, _invariant.default)(cellKey != null, 'Trying to remove non-present child list');\n        this._childrenToCellKey.delete(list);\n        var cellLists = this._cellKeyToChildren.get(cellKey);\n        (0, _invariant.default)(cellLists, '_cellKeyToChildren should contain cellKey');\n        cellLists.delete(list);\n        if (cellLists.size === 0) {\n          this._cellKeyToChildren.delete(cellKey);\n        }\n      }\n    }, {\n      key: \"forEach\",\n      value: function forEach(fn) {\n        for (var listSet of this._cellKeyToChildren.values()) {\n          for (var list of listSet) {\n            fn(list);\n          }\n        }\n      }\n    }, {\n      key: \"forEachInCell\",\n      value: function forEachInCell(cellKey, fn) {\n        var listSet = this._cellKeyToChildren.get(cellKey) ?? [];\n        for (var list of listSet) {\n          fn(list);\n        }\n      }\n    }, {\n      key: \"anyInCell\",\n      value: function anyInCell(cellKey, fn) {\n        var listSet = this._cellKeyToChildren.get(cellKey) ?? [];\n        for (var list of listSet) {\n          if (fn(list)) {\n            return true;\n          }\n        }\n        return false;\n      }\n    }, {\n      key: \"size\",\n      value: function size() {\n        return this._childrenToCellKey.size;\n      }\n    }]);\n  }();\n});", "lineCount": 73, "map": [[9, 2, 11, 0], [9, 6, 11, 0, "_invariant"], [9, 16, 11, 0], [9, 19, 11, 0, "_interopRequireDefault"], [9, 41, 11, 0], [9, 42, 11, 0, "require"], [9, 49, 11, 0], [9, 50, 11, 0, "_dependencyMap"], [9, 64, 11, 0], [10, 2, 11, 34], [10, 6, 13, 21, "ChildListCollection"], [10, 25, 13, 40], [10, 28, 13, 40, "exports"], [10, 35, 13, 40], [10, 36, 13, 40, "default"], [10, 43, 13, 40], [11, 4, 13, 40], [11, 13, 13, 40, "ChildListCollection"], [11, 33, 13, 40], [12, 6, 13, 40], [12, 10, 13, 40, "_classCallCheck2"], [12, 26, 13, 40], [12, 27, 13, 40, "default"], [12, 34, 13, 40], [12, 42, 13, 40, "ChildListCollection"], [12, 61, 13, 40], [13, 6, 13, 40], [13, 11, 14, 2, "_cellKeyToChildren"], [13, 29, 14, 20], [13, 32, 14, 48], [13, 36, 14, 52, "Map"], [13, 39, 14, 55], [13, 40, 14, 56], [13, 41, 14, 57], [14, 6, 14, 57], [14, 11, 15, 2, "_children<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [14, 29, 15, 20], [14, 32, 15, 43], [14, 36, 15, 47, "Map"], [14, 39, 15, 50], [14, 40, 15, 51], [14, 41, 15, 52], [15, 4, 15, 52], [16, 4, 15, 52], [16, 15, 15, 52, "_createClass2"], [16, 28, 15, 52], [16, 29, 15, 52, "default"], [16, 36, 15, 52], [16, 38, 15, 52, "ChildListCollection"], [16, 57, 15, 52], [17, 6, 15, 52, "key"], [17, 9, 15, 52], [18, 6, 15, 52, "value"], [18, 11, 15, 52], [18, 13, 17, 2], [18, 22, 17, 2, "add"], [18, 25, 17, 5, "add"], [18, 26, 17, 6, "list"], [18, 30, 17, 17], [18, 32, 17, 19, "cellKey"], [18, 39, 17, 34], [18, 41, 17, 42], [19, 8, 18, 4], [19, 12, 18, 4, "invariant"], [19, 30, 18, 13], [19, 32, 19, 6], [19, 33, 19, 7], [19, 37, 19, 11], [19, 38, 19, 12, "_children<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [19, 56, 19, 30], [19, 57, 19, 31, "has"], [19, 60, 19, 34], [19, 61, 19, 35, "list"], [19, 65, 19, 39], [19, 66, 19, 40], [19, 68, 20, 6], [19, 110, 21, 4], [19, 111, 21, 5], [20, 8, 23, 4], [20, 12, 23, 10, "cellLists"], [20, 21, 23, 19], [20, 24, 23, 22], [20, 28, 23, 26], [20, 29, 23, 27, "_cellKeyToChildren"], [20, 47, 23, 45], [20, 48, 23, 46, "get"], [20, 51, 23, 49], [20, 52, 23, 50, "cellKey"], [20, 59, 23, 57], [20, 60, 23, 58], [20, 64, 23, 62], [20, 68, 23, 66, "Set"], [20, 71, 23, 69], [20, 72, 23, 70], [20, 73, 23, 71], [21, 8, 24, 4, "cellLists"], [21, 17, 24, 13], [21, 18, 24, 14, "add"], [21, 21, 24, 17], [21, 22, 24, 18, "list"], [21, 26, 24, 22], [21, 27, 24, 23], [22, 8, 25, 4], [22, 12, 25, 8], [22, 13, 25, 9, "_cellKeyToChildren"], [22, 31, 25, 27], [22, 32, 25, 28, "set"], [22, 35, 25, 31], [22, 36, 25, 32, "cellKey"], [22, 43, 25, 39], [22, 45, 25, 41, "cellLists"], [22, 54, 25, 50], [22, 55, 25, 51], [23, 8, 27, 4], [23, 12, 27, 8], [23, 13, 27, 9, "_children<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 31, 27, 27], [23, 32, 27, 28, "set"], [23, 35, 27, 31], [23, 36, 27, 32, "list"], [23, 40, 27, 36], [23, 42, 27, 38, "cellKey"], [23, 49, 27, 45], [23, 50, 27, 46], [24, 6, 28, 2], [25, 4, 28, 3], [26, 6, 28, 3, "key"], [26, 9, 28, 3], [27, 6, 28, 3, "value"], [27, 11, 28, 3], [27, 13, 30, 2], [27, 22, 30, 2, "remove"], [27, 28, 30, 8, "remove"], [27, 29, 30, 9, "list"], [27, 33, 30, 20], [27, 35, 30, 28], [28, 8, 31, 4], [28, 12, 31, 10, "cellKey"], [28, 19, 31, 17], [28, 22, 31, 20], [28, 26, 31, 24], [28, 27, 31, 25, "_children<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [28, 45, 31, 43], [28, 46, 31, 44, "get"], [28, 49, 31, 47], [28, 50, 31, 48, "list"], [28, 54, 31, 52], [28, 55, 31, 53], [29, 8, 32, 4], [29, 12, 32, 4, "invariant"], [29, 30, 32, 13], [29, 32, 32, 14, "cellKey"], [29, 39, 32, 21], [29, 43, 32, 25], [29, 47, 32, 29], [29, 49, 32, 31], [29, 90, 32, 72], [29, 91, 32, 73], [30, 8, 33, 4], [30, 12, 33, 8], [30, 13, 33, 9, "_children<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [30, 31, 33, 27], [30, 32, 33, 28, "delete"], [30, 38, 33, 34], [30, 39, 33, 35, "list"], [30, 43, 33, 39], [30, 44, 33, 40], [31, 8, 35, 4], [31, 12, 35, 10, "cellLists"], [31, 21, 35, 19], [31, 24, 35, 22], [31, 28, 35, 26], [31, 29, 35, 27, "_cellKeyToChildren"], [31, 47, 35, 45], [31, 48, 35, 46, "get"], [31, 51, 35, 49], [31, 52, 35, 50, "cellKey"], [31, 59, 35, 57], [31, 60, 35, 58], [32, 8, 36, 4], [32, 12, 36, 4, "invariant"], [32, 30, 36, 13], [32, 32, 36, 14, "cellLists"], [32, 41, 36, 23], [32, 43, 36, 25], [32, 86, 36, 68], [32, 87, 36, 69], [33, 8, 37, 4, "cellLists"], [33, 17, 37, 13], [33, 18, 37, 14, "delete"], [33, 24, 37, 20], [33, 25, 37, 21, "list"], [33, 29, 37, 25], [33, 30, 37, 26], [34, 8, 39, 4], [34, 12, 39, 8, "cellLists"], [34, 21, 39, 17], [34, 22, 39, 18, "size"], [34, 26, 39, 22], [34, 31, 39, 27], [34, 32, 39, 28], [34, 34, 39, 30], [35, 10, 40, 6], [35, 14, 40, 10], [35, 15, 40, 11, "_cellKeyToChildren"], [35, 33, 40, 29], [35, 34, 40, 30, "delete"], [35, 40, 40, 36], [35, 41, 40, 37, "cellKey"], [35, 48, 40, 44], [35, 49, 40, 45], [36, 8, 41, 4], [37, 6, 42, 2], [38, 4, 42, 3], [39, 6, 42, 3, "key"], [39, 9, 42, 3], [40, 6, 42, 3, "value"], [40, 11, 42, 3], [40, 13, 44, 2], [40, 22, 44, 2, "for<PERSON>ach"], [40, 29, 44, 9, "for<PERSON>ach"], [40, 30, 44, 10, "fn"], [40, 32, 44, 27], [40, 34, 44, 35], [41, 8, 45, 4], [41, 13, 45, 9], [41, 17, 45, 15, "listSet"], [41, 24, 45, 22], [41, 28, 45, 26], [41, 32, 45, 30], [41, 33, 45, 31, "_cellKeyToChildren"], [41, 51, 45, 49], [41, 52, 45, 50, "values"], [41, 58, 45, 56], [41, 59, 45, 57], [41, 60, 45, 58], [41, 62, 45, 60], [42, 10, 46, 6], [42, 15, 46, 11], [42, 19, 46, 17, "list"], [42, 23, 46, 21], [42, 27, 46, 25, "listSet"], [42, 34, 46, 32], [42, 36, 46, 34], [43, 12, 47, 8, "fn"], [43, 14, 47, 10], [43, 15, 47, 11, "list"], [43, 19, 47, 15], [43, 20, 47, 16], [44, 10, 48, 6], [45, 8, 49, 4], [46, 6, 50, 2], [47, 4, 50, 3], [48, 6, 50, 3, "key"], [48, 9, 50, 3], [49, 6, 50, 3, "value"], [49, 11, 50, 3], [49, 13, 52, 2], [49, 22, 52, 2, "forEachInCell"], [49, 35, 52, 15, "forEachInCell"], [49, 36, 52, 16, "cellKey"], [49, 43, 52, 31], [49, 45, 52, 33, "fn"], [49, 47, 52, 50], [49, 49, 52, 58], [50, 8, 53, 4], [50, 12, 53, 10, "listSet"], [50, 19, 53, 17], [50, 22, 53, 20], [50, 26, 53, 24], [50, 27, 53, 25, "_cellKeyToChildren"], [50, 45, 53, 43], [50, 46, 53, 44, "get"], [50, 49, 53, 47], [50, 50, 53, 48, "cellKey"], [50, 57, 53, 55], [50, 58, 53, 56], [50, 62, 53, 60], [50, 64, 53, 62], [51, 8, 54, 4], [51, 13, 54, 9], [51, 17, 54, 15, "list"], [51, 21, 54, 19], [51, 25, 54, 23, "listSet"], [51, 32, 54, 30], [51, 34, 54, 32], [52, 10, 55, 6, "fn"], [52, 12, 55, 8], [52, 13, 55, 9, "list"], [52, 17, 55, 13], [52, 18, 55, 14], [53, 8, 56, 4], [54, 6, 57, 2], [55, 4, 57, 3], [56, 6, 57, 3, "key"], [56, 9, 57, 3], [57, 6, 57, 3, "value"], [57, 11, 57, 3], [57, 13, 59, 2], [57, 22, 59, 2, "anyInCell"], [57, 31, 59, 11, "anyInCell"], [57, 32, 59, 12, "cellKey"], [57, 39, 59, 27], [57, 41, 59, 29, "fn"], [57, 43, 59, 49], [57, 45, 59, 60], [58, 8, 60, 4], [58, 12, 60, 10, "listSet"], [58, 19, 60, 17], [58, 22, 60, 20], [58, 26, 60, 24], [58, 27, 60, 25, "_cellKeyToChildren"], [58, 45, 60, 43], [58, 46, 60, 44, "get"], [58, 49, 60, 47], [58, 50, 60, 48, "cellKey"], [58, 57, 60, 55], [58, 58, 60, 56], [58, 62, 60, 60], [58, 64, 60, 62], [59, 8, 61, 4], [59, 13, 61, 9], [59, 17, 61, 15, "list"], [59, 21, 61, 19], [59, 25, 61, 23, "listSet"], [59, 32, 61, 30], [59, 34, 61, 32], [60, 10, 62, 6], [60, 14, 62, 10, "fn"], [60, 16, 62, 12], [60, 17, 62, 13, "list"], [60, 21, 62, 17], [60, 22, 62, 18], [60, 24, 62, 20], [61, 12, 63, 8], [61, 19, 63, 15], [61, 23, 63, 19], [62, 10, 64, 6], [63, 8, 65, 4], [64, 8, 66, 4], [64, 15, 66, 11], [64, 20, 66, 16], [65, 6, 67, 2], [66, 4, 67, 3], [67, 6, 67, 3, "key"], [67, 9, 67, 3], [68, 6, 67, 3, "value"], [68, 11, 67, 3], [68, 13, 69, 2], [68, 22, 69, 2, "size"], [68, 26, 69, 6, "size"], [68, 27, 69, 6], [68, 29, 69, 17], [69, 8, 70, 4], [69, 15, 70, 11], [69, 19, 70, 15], [69, 20, 70, 16, "_children<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [69, 38, 70, 34], [69, 39, 70, 35, "size"], [69, 43, 70, 39], [70, 6, 71, 2], [71, 4, 71, 3], [72, 2, 71, 3], [73, 0, 71, 3], [73, 3]], "functionMap": {"names": ["<global>", "ChildListCollection", "add", "remove", "for<PERSON>ach", "forEachInCell", "anyInCell", "size"], "mappings": "AAA;eCY;ECI;GDW;EEE;GFY;EGE;GHM;EIE;GJK;EKE;GLQ;EME;GNE"}}, "type": "js/module"}]}