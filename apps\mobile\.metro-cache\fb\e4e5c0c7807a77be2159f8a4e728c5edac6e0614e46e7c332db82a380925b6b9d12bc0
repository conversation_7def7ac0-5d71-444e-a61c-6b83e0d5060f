{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  // This is Jest implementation of `requestAnimationFrame` that is required\n  // by React Native for test purposes.\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.mockedRequestAnimationFrame = mockedRequestAnimationFrame;\n  function mockedRequestAnimationFrame(callback) {\n    return setTimeout(() => callback(performance.now()), 0);\n  }\n});", "lineCount": 13, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 4, 0], [6, 2, 4, 0, "Object"], [6, 8, 4, 0], [6, 9, 4, 0, "defineProperty"], [6, 23, 4, 0], [6, 24, 4, 0, "exports"], [6, 31, 4, 0], [7, 4, 4, 0, "value"], [7, 9, 4, 0], [8, 2, 4, 0], [9, 2, 4, 0, "exports"], [9, 9, 4, 0], [9, 10, 4, 0, "mockedRequestAnimationFrame"], [9, 37, 4, 0], [9, 40, 4, 0, "mockedRequestAnimationFrame"], [9, 67, 4, 0], [10, 2, 5, 7], [10, 11, 5, 16, "mockedRequestAnimationFrame"], [10, 38, 5, 43, "mockedRequestAnimationFrame"], [10, 39, 6, 2, "callback"], [10, 47, 6, 39], [10, 49, 7, 33], [11, 4, 8, 2], [11, 11, 8, 9, "setTimeout"], [11, 21, 8, 19], [11, 22, 8, 20], [11, 28, 8, 26, "callback"], [11, 36, 8, 34], [11, 37, 8, 35, "performance"], [11, 48, 8, 46], [11, 49, 8, 47, "now"], [11, 52, 8, 50], [11, 53, 8, 51], [11, 54, 8, 52], [11, 55, 8, 53], [11, 57, 8, 55], [11, 58, 8, 56], [11, 59, 8, 57], [12, 2, 9, 0], [13, 0, 9, 1], [13, 3]], "functionMap": {"names": ["<global>", "mockedRequestAnimationFrame", "setTimeout$argument_0"], "mappings": "AAA;OCI;oBCG,iCD;CDC"}}, "type": "js/module"}]}