{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../Data/LogBoxData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 49}}], "key": "A5Z1ymCQl9OoAWyeygTXLGmRNuU=", "exportNames": ["*"]}}, {"name": "../Data/LogBoxLog", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 42}}], "key": "G7ZBvdkqrR7u14zDBnWaTI9YPCM=", "exportNames": ["*"]}}, {"name": "./LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 42}}], "key": "M6ofQu070ZUTf+Oq+Zz+7FQEgjs=", "exportNames": ["*"]}}, {"name": "./LogBoxNotificationCountBadge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 74}}], "key": "I6r6KjoPsW86OB0pfsP2MnZ7ze8=", "exportNames": ["*"]}}, {"name": "./LogBoxNotificationDismissButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 80}}], "key": "u/MKnq4lY/h9vdNN8bmdgUJ0Cwo=", "exportNames": ["*"]}}, {"name": "./LogBoxNotificationMessage", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 68}}], "key": "MpBfMi03tZjqlfi9DD8dFEZNBLQ=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = LogBoxNotification;\n  var _View = _interopRequireDefault(require(_dependencyMap[1], \"../../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[2], \"../../StyleSheet/StyleSheet\"));\n  var LogBoxData = _interopRequireWildcard(require(_dependencyMap[3], \"../Data/LogBoxData\"));\n  var _LogBoxLog = _interopRequireDefault(require(_dependencyMap[4], \"../Data/LogBoxLog\"));\n  var _LogBoxButton = _interopRequireDefault(require(_dependencyMap[5], \"./LogBoxButton\"));\n  var _LogBoxNotificationCountBadge = _interopRequireDefault(require(_dependencyMap[6], \"./LogBoxNotificationCountBadge\"));\n  var _LogBoxNotificationDismissButton = _interopRequireDefault(require(_dependencyMap[7], \"./LogBoxNotificationDismissButton\"));\n  var _LogBoxNotificationMessage = _interopRequireDefault(require(_dependencyMap[8], \"./LogBoxNotificationMessage\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[9], \"./LogBoxStyle\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[10], \"react\"));\n  var React = _react;\n  var _jsxDevRuntime = require(_dependencyMap[11], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxNotification.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function LogBoxNotification(props) {\n    var totalLogCount = props.totalLogCount,\n      level = props.level,\n      log = props.log;\n    (0, _react.useEffect)(() => {\n      LogBoxData.symbolicateLogLazy(log);\n    }, [log]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      id: \"logbox_notification\",\n      style: styles.container,\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxButton.default, {\n        id: `logbox_open_button_${level}`,\n        onPress: props.onPressOpen,\n        style: styles.press,\n        backgroundColor: {\n          default: LogBoxStyle.getBackgroundColor(1),\n          pressed: LogBoxStyle.getBackgroundColor(0.9)\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.content,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxNotificationCountBadge.default, {\n            count: totalLogCount,\n            level: level\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxNotificationMessage.default, {\n            message: log.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxNotificationDismissButton.default, {\n            id: `logbox_dismiss_button_${level}`,\n            onPress: props.onPressDismiss\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _StyleSheet.default.create({\n    container: {\n      height: 48,\n      position: 'relative',\n      width: '100%',\n      justifyContent: 'center',\n      marginTop: 0.5,\n      backgroundColor: LogBoxStyle.getTextColor(1)\n    },\n    press: {\n      height: 48,\n      position: 'relative',\n      width: '100%',\n      justifyContent: 'center',\n      marginTop: 0.5,\n      paddingHorizontal: 12\n    },\n    content: {\n      alignItems: 'flex-start',\n      flexDirection: 'row',\n      borderRadius: 8,\n      flexGrow: 0,\n      flexShrink: 0,\n      flexBasis: 'auto'\n    }\n  });\n});", "lineCount": 104, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_View"], [7, 11, 11, 0], [7, 14, 11, 0, "_interopRequireDefault"], [7, 36, 11, 0], [7, 37, 11, 0, "require"], [7, 44, 11, 0], [7, 45, 11, 0, "_dependencyMap"], [7, 59, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_StyleSheet"], [8, 17, 12, 0], [8, 20, 12, 0, "_interopRequireDefault"], [8, 42, 12, 0], [8, 43, 12, 0, "require"], [8, 50, 12, 0], [8, 51, 12, 0, "_dependencyMap"], [8, 65, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "LogBoxData"], [9, 16, 13, 0], [9, 19, 13, 0, "_interopRequireWildcard"], [9, 42, 13, 0], [9, 43, 13, 0, "require"], [9, 50, 13, 0], [9, 51, 13, 0, "_dependencyMap"], [9, 65, 13, 0], [10, 2, 14, 0], [10, 6, 14, 0, "_LogBoxLog"], [10, 16, 14, 0], [10, 19, 14, 0, "_interopRequireDefault"], [10, 41, 14, 0], [10, 42, 14, 0, "require"], [10, 49, 14, 0], [10, 50, 14, 0, "_dependencyMap"], [10, 64, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "_LogBoxButton"], [11, 19, 15, 0], [11, 22, 15, 0, "_interopRequireDefault"], [11, 44, 15, 0], [11, 45, 15, 0, "require"], [11, 52, 15, 0], [11, 53, 15, 0, "_dependencyMap"], [11, 67, 15, 0], [12, 2, 16, 0], [12, 6, 16, 0, "_LogBoxNotificationCountBadge"], [12, 35, 16, 0], [12, 38, 16, 0, "_interopRequireDefault"], [12, 60, 16, 0], [12, 61, 16, 0, "require"], [12, 68, 16, 0], [12, 69, 16, 0, "_dependencyMap"], [12, 83, 16, 0], [13, 2, 17, 0], [13, 6, 17, 0, "_LogBoxNotificationDismissButton"], [13, 38, 17, 0], [13, 41, 17, 0, "_interopRequireDefault"], [13, 63, 17, 0], [13, 64, 17, 0, "require"], [13, 71, 17, 0], [13, 72, 17, 0, "_dependencyMap"], [13, 86, 17, 0], [14, 2, 18, 0], [14, 6, 18, 0, "_LogBoxNotificationMessage"], [14, 32, 18, 0], [14, 35, 18, 0, "_interopRequireDefault"], [14, 57, 18, 0], [14, 58, 18, 0, "require"], [14, 65, 18, 0], [14, 66, 18, 0, "_dependencyMap"], [14, 80, 18, 0], [15, 2, 19, 0], [15, 6, 19, 0, "LogBoxStyle"], [15, 17, 19, 0], [15, 20, 19, 0, "_interopRequireWildcard"], [15, 43, 19, 0], [15, 44, 19, 0, "require"], [15, 51, 19, 0], [15, 52, 19, 0, "_dependencyMap"], [15, 66, 19, 0], [16, 2, 20, 0], [16, 6, 20, 0, "_react"], [16, 12, 20, 0], [16, 15, 20, 0, "_interopRequireWildcard"], [16, 38, 20, 0], [16, 39, 20, 0, "require"], [16, 46, 20, 0], [16, 47, 20, 0, "_dependencyMap"], [16, 61, 20, 0], [17, 2, 20, 31], [17, 6, 20, 31, "React"], [17, 11, 20, 31], [17, 14, 20, 31, "_react"], [17, 20, 20, 31], [18, 2, 20, 31], [18, 6, 20, 31, "_jsxDevRuntime"], [18, 20, 20, 31], [18, 23, 20, 31, "require"], [18, 30, 20, 31], [18, 31, 20, 31, "_dependencyMap"], [18, 45, 20, 31], [19, 2, 20, 31], [19, 6, 20, 31, "_jsxFileName"], [19, 18, 20, 31], [20, 2, 20, 31], [20, 11, 20, 31, "_interopRequireWildcard"], [20, 35, 20, 31, "e"], [20, 36, 20, 31], [20, 38, 20, 31, "t"], [20, 39, 20, 31], [20, 68, 20, 31, "WeakMap"], [20, 75, 20, 31], [20, 81, 20, 31, "r"], [20, 82, 20, 31], [20, 89, 20, 31, "WeakMap"], [20, 96, 20, 31], [20, 100, 20, 31, "n"], [20, 101, 20, 31], [20, 108, 20, 31, "WeakMap"], [20, 115, 20, 31], [20, 127, 20, 31, "_interopRequireWildcard"], [20, 150, 20, 31], [20, 162, 20, 31, "_interopRequireWildcard"], [20, 163, 20, 31, "e"], [20, 164, 20, 31], [20, 166, 20, 31, "t"], [20, 167, 20, 31], [20, 176, 20, 31, "t"], [20, 177, 20, 31], [20, 181, 20, 31, "e"], [20, 182, 20, 31], [20, 186, 20, 31, "e"], [20, 187, 20, 31], [20, 188, 20, 31, "__esModule"], [20, 198, 20, 31], [20, 207, 20, 31, "e"], [20, 208, 20, 31], [20, 214, 20, 31, "o"], [20, 215, 20, 31], [20, 217, 20, 31, "i"], [20, 218, 20, 31], [20, 220, 20, 31, "f"], [20, 221, 20, 31], [20, 226, 20, 31, "__proto__"], [20, 235, 20, 31], [20, 243, 20, 31, "default"], [20, 250, 20, 31], [20, 252, 20, 31, "e"], [20, 253, 20, 31], [20, 270, 20, 31, "e"], [20, 271, 20, 31], [20, 294, 20, 31, "e"], [20, 295, 20, 31], [20, 320, 20, 31, "e"], [20, 321, 20, 31], [20, 330, 20, 31, "f"], [20, 331, 20, 31], [20, 337, 20, 31, "o"], [20, 338, 20, 31], [20, 341, 20, 31, "t"], [20, 342, 20, 31], [20, 345, 20, 31, "n"], [20, 346, 20, 31], [20, 349, 20, 31, "r"], [20, 350, 20, 31], [20, 358, 20, 31, "o"], [20, 359, 20, 31], [20, 360, 20, 31, "has"], [20, 363, 20, 31], [20, 364, 20, 31, "e"], [20, 365, 20, 31], [20, 375, 20, 31, "o"], [20, 376, 20, 31], [20, 377, 20, 31, "get"], [20, 380, 20, 31], [20, 381, 20, 31, "e"], [20, 382, 20, 31], [20, 385, 20, 31, "o"], [20, 386, 20, 31], [20, 387, 20, 31, "set"], [20, 390, 20, 31], [20, 391, 20, 31, "e"], [20, 392, 20, 31], [20, 394, 20, 31, "f"], [20, 395, 20, 31], [20, 409, 20, 31, "_t"], [20, 411, 20, 31], [20, 415, 20, 31, "e"], [20, 416, 20, 31], [20, 432, 20, 31, "_t"], [20, 434, 20, 31], [20, 441, 20, 31, "hasOwnProperty"], [20, 455, 20, 31], [20, 456, 20, 31, "call"], [20, 460, 20, 31], [20, 461, 20, 31, "e"], [20, 462, 20, 31], [20, 464, 20, 31, "_t"], [20, 466, 20, 31], [20, 473, 20, 31, "i"], [20, 474, 20, 31], [20, 478, 20, 31, "o"], [20, 479, 20, 31], [20, 482, 20, 31, "Object"], [20, 488, 20, 31], [20, 489, 20, 31, "defineProperty"], [20, 503, 20, 31], [20, 508, 20, 31, "Object"], [20, 514, 20, 31], [20, 515, 20, 31, "getOwnPropertyDescriptor"], [20, 539, 20, 31], [20, 540, 20, 31, "e"], [20, 541, 20, 31], [20, 543, 20, 31, "_t"], [20, 545, 20, 31], [20, 552, 20, 31, "i"], [20, 553, 20, 31], [20, 554, 20, 31, "get"], [20, 557, 20, 31], [20, 561, 20, 31, "i"], [20, 562, 20, 31], [20, 563, 20, 31, "set"], [20, 566, 20, 31], [20, 570, 20, 31, "o"], [20, 571, 20, 31], [20, 572, 20, 31, "f"], [20, 573, 20, 31], [20, 575, 20, 31, "_t"], [20, 577, 20, 31], [20, 579, 20, 31, "i"], [20, 580, 20, 31], [20, 584, 20, 31, "f"], [20, 585, 20, 31], [20, 586, 20, 31, "_t"], [20, 588, 20, 31], [20, 592, 20, 31, "e"], [20, 593, 20, 31], [20, 594, 20, 31, "_t"], [20, 596, 20, 31], [20, 607, 20, 31, "f"], [20, 608, 20, 31], [20, 613, 20, 31, "e"], [20, 614, 20, 31], [20, 616, 20, 31, "t"], [20, 617, 20, 31], [21, 2, 31, 15], [21, 11, 31, 24, "LogBoxNotification"], [21, 29, 31, 42, "LogBoxNotification"], [21, 30, 31, 43, "props"], [21, 35, 31, 55], [21, 37, 31, 69], [22, 4, 32, 2], [22, 8, 32, 9, "totalLogCount"], [22, 21, 32, 22], [22, 24, 32, 38, "props"], [22, 29, 32, 43], [22, 30, 32, 9, "totalLogCount"], [22, 43, 32, 22], [23, 6, 32, 24, "level"], [23, 11, 32, 29], [23, 14, 32, 38, "props"], [23, 19, 32, 43], [23, 20, 32, 24, "level"], [23, 25, 32, 29], [24, 6, 32, 31, "log"], [24, 9, 32, 34], [24, 12, 32, 38, "props"], [24, 17, 32, 43], [24, 18, 32, 31, "log"], [24, 21, 32, 34], [25, 4, 35, 2], [25, 8, 35, 2, "useEffect"], [25, 24, 35, 11], [25, 26, 35, 12], [25, 32, 35, 18], [26, 6, 36, 4, "LogBoxData"], [26, 16, 36, 14], [26, 17, 36, 15, "symbolicateLogLazy"], [26, 35, 36, 33], [26, 36, 36, 34, "log"], [26, 39, 36, 37], [26, 40, 36, 38], [27, 4, 37, 2], [27, 5, 37, 3], [27, 7, 37, 5], [27, 8, 37, 6, "log"], [27, 11, 37, 9], [27, 12, 37, 10], [27, 13, 37, 11], [28, 4, 39, 2], [28, 24, 40, 4], [28, 28, 40, 4, "_jsxDevRuntime"], [28, 42, 40, 4], [28, 43, 40, 4, "jsxDEV"], [28, 49, 40, 4], [28, 51, 40, 5, "_View"], [28, 56, 40, 5], [28, 57, 40, 5, "default"], [28, 64, 40, 9], [29, 6, 40, 10, "id"], [29, 8, 40, 12], [29, 10, 40, 13], [29, 31, 40, 34], [30, 6, 40, 35, "style"], [30, 11, 40, 40], [30, 13, 40, 42, "styles"], [30, 19, 40, 48], [30, 20, 40, 49, "container"], [30, 29, 40, 59], [31, 6, 40, 59, "children"], [31, 14, 40, 59], [31, 29, 41, 6], [31, 33, 41, 6, "_jsxDevRuntime"], [31, 47, 41, 6], [31, 48, 41, 6, "jsxDEV"], [31, 54, 41, 6], [31, 56, 41, 7, "_LogBoxButton"], [31, 69, 41, 7], [31, 70, 41, 7, "default"], [31, 77, 41, 19], [32, 8, 42, 8, "id"], [32, 10, 42, 10], [32, 12, 42, 12], [32, 34, 42, 34, "level"], [32, 39, 42, 39], [32, 41, 42, 42], [33, 8, 43, 8, "onPress"], [33, 15, 43, 15], [33, 17, 43, 17, "props"], [33, 22, 43, 22], [33, 23, 43, 23, "onPressOpen"], [33, 34, 43, 35], [34, 8, 44, 8, "style"], [34, 13, 44, 13], [34, 15, 44, 15, "styles"], [34, 21, 44, 21], [34, 22, 44, 22, "press"], [34, 27, 44, 28], [35, 8, 45, 8, "backgroundColor"], [35, 23, 45, 23], [35, 25, 45, 25], [36, 10, 46, 10, "default"], [36, 17, 46, 17], [36, 19, 46, 19, "LogBoxStyle"], [36, 30, 46, 30], [36, 31, 46, 31, "getBackgroundColor"], [36, 49, 46, 49], [36, 50, 46, 50], [36, 51, 46, 51], [36, 52, 46, 52], [37, 10, 47, 10, "pressed"], [37, 17, 47, 17], [37, 19, 47, 19, "LogBoxStyle"], [37, 30, 47, 30], [37, 31, 47, 31, "getBackgroundColor"], [37, 49, 47, 49], [37, 50, 47, 50], [37, 53, 47, 53], [38, 8, 48, 8], [38, 9, 48, 10], [39, 8, 48, 10, "children"], [39, 16, 48, 10], [39, 31, 49, 8], [39, 35, 49, 8, "_jsxDevRuntime"], [39, 49, 49, 8], [39, 50, 49, 8, "jsxDEV"], [39, 56, 49, 8], [39, 58, 49, 9, "_View"], [39, 63, 49, 9], [39, 64, 49, 9, "default"], [39, 71, 49, 13], [40, 10, 49, 14, "style"], [40, 15, 49, 19], [40, 17, 49, 21, "styles"], [40, 23, 49, 27], [40, 24, 49, 28, "content"], [40, 31, 49, 36], [41, 10, 49, 36, "children"], [41, 18, 49, 36], [41, 34, 50, 10], [41, 38, 50, 10, "_jsxDevRuntime"], [41, 52, 50, 10], [41, 53, 50, 10, "jsxDEV"], [41, 59, 50, 10], [41, 61, 50, 11, "_LogBoxNotificationCountBadge"], [41, 90, 50, 11], [41, 91, 50, 11, "default"], [41, 98, 50, 39], [42, 12, 50, 40, "count"], [42, 17, 50, 45], [42, 19, 50, 47, "totalLogCount"], [42, 32, 50, 61], [43, 12, 50, 62, "level"], [43, 17, 50, 67], [43, 19, 50, 69, "level"], [44, 10, 50, 75], [45, 12, 50, 75, "fileName"], [45, 20, 50, 75], [45, 22, 50, 75, "_jsxFileName"], [45, 34, 50, 75], [46, 12, 50, 75, "lineNumber"], [46, 22, 50, 75], [47, 12, 50, 75, "columnNumber"], [47, 24, 50, 75], [48, 10, 50, 75], [48, 17, 50, 77], [48, 18, 50, 78], [48, 33, 51, 10], [48, 37, 51, 10, "_jsxDevRuntime"], [48, 51, 51, 10], [48, 52, 51, 10, "jsxDEV"], [48, 58, 51, 10], [48, 60, 51, 11, "_LogBoxNotificationMessage"], [48, 86, 51, 11], [48, 87, 51, 11, "default"], [48, 94, 51, 36], [49, 12, 51, 37, "message"], [49, 19, 51, 44], [49, 21, 51, 46, "log"], [49, 24, 51, 49], [49, 25, 51, 50, "message"], [50, 10, 51, 58], [51, 12, 51, 58, "fileName"], [51, 20, 51, 58], [51, 22, 51, 58, "_jsxFileName"], [51, 34, 51, 58], [52, 12, 51, 58, "lineNumber"], [52, 22, 51, 58], [53, 12, 51, 58, "columnNumber"], [53, 24, 51, 58], [54, 10, 51, 58], [54, 17, 51, 60], [54, 18, 51, 61], [54, 33, 52, 10], [54, 37, 52, 10, "_jsxDevRuntime"], [54, 51, 52, 10], [54, 52, 52, 10, "jsxDEV"], [54, 58, 52, 10], [54, 60, 52, 11, "_LogBoxNotificationDismissButton"], [54, 92, 52, 11], [54, 93, 52, 11, "default"], [54, 100, 52, 42], [55, 12, 53, 12, "id"], [55, 14, 53, 14], [55, 16, 53, 16], [55, 41, 53, 41, "level"], [55, 46, 53, 46], [55, 48, 53, 49], [56, 12, 54, 12, "onPress"], [56, 19, 54, 19], [56, 21, 54, 21, "props"], [56, 26, 54, 26], [56, 27, 54, 27, "on<PERSON>ress<PERSON><PERSON><PERSON>"], [57, 10, 54, 42], [58, 12, 54, 42, "fileName"], [58, 20, 54, 42], [58, 22, 54, 42, "_jsxFileName"], [58, 34, 54, 42], [59, 12, 54, 42, "lineNumber"], [59, 22, 54, 42], [60, 12, 54, 42, "columnNumber"], [60, 24, 54, 42], [61, 10, 54, 42], [61, 17, 55, 11], [61, 18, 55, 12], [62, 8, 55, 12], [63, 10, 55, 12, "fileName"], [63, 18, 55, 12], [63, 20, 55, 12, "_jsxFileName"], [63, 32, 55, 12], [64, 10, 55, 12, "lineNumber"], [64, 20, 55, 12], [65, 10, 55, 12, "columnNumber"], [65, 22, 55, 12], [66, 8, 55, 12], [66, 15, 56, 14], [67, 6, 56, 15], [68, 8, 56, 15, "fileName"], [68, 16, 56, 15], [68, 18, 56, 15, "_jsxFileName"], [68, 30, 56, 15], [69, 8, 56, 15, "lineNumber"], [69, 18, 56, 15], [70, 8, 56, 15, "columnNumber"], [70, 20, 56, 15], [71, 6, 56, 15], [71, 13, 57, 20], [72, 4, 57, 21], [73, 6, 57, 21, "fileName"], [73, 14, 57, 21], [73, 16, 57, 21, "_jsxFileName"], [73, 28, 57, 21], [74, 6, 57, 21, "lineNumber"], [74, 16, 57, 21], [75, 6, 57, 21, "columnNumber"], [75, 18, 57, 21], [76, 4, 57, 21], [76, 11, 58, 10], [76, 12, 58, 11], [77, 2, 60, 0], [78, 2, 62, 0], [78, 6, 62, 6, "styles"], [78, 12, 62, 12], [78, 15, 62, 15, "StyleSheet"], [78, 34, 62, 25], [78, 35, 62, 26, "create"], [78, 41, 62, 32], [78, 42, 62, 33], [79, 4, 63, 2, "container"], [79, 13, 63, 11], [79, 15, 63, 13], [80, 6, 64, 4, "height"], [80, 12, 64, 10], [80, 14, 64, 12], [80, 16, 64, 14], [81, 6, 65, 4, "position"], [81, 14, 65, 12], [81, 16, 65, 14], [81, 26, 65, 24], [82, 6, 66, 4, "width"], [82, 11, 66, 9], [82, 13, 66, 11], [82, 19, 66, 17], [83, 6, 67, 4, "justifyContent"], [83, 20, 67, 18], [83, 22, 67, 20], [83, 30, 67, 28], [84, 6, 68, 4, "marginTop"], [84, 15, 68, 13], [84, 17, 68, 15], [84, 20, 68, 18], [85, 6, 69, 4, "backgroundColor"], [85, 21, 69, 19], [85, 23, 69, 21, "LogBoxStyle"], [85, 34, 69, 32], [85, 35, 69, 33, "getTextColor"], [85, 47, 69, 45], [85, 48, 69, 46], [85, 49, 69, 47], [86, 4, 70, 2], [86, 5, 70, 3], [87, 4, 71, 2, "press"], [87, 9, 71, 7], [87, 11, 71, 9], [88, 6, 72, 4, "height"], [88, 12, 72, 10], [88, 14, 72, 12], [88, 16, 72, 14], [89, 6, 73, 4, "position"], [89, 14, 73, 12], [89, 16, 73, 14], [89, 26, 73, 24], [90, 6, 74, 4, "width"], [90, 11, 74, 9], [90, 13, 74, 11], [90, 19, 74, 17], [91, 6, 75, 4, "justifyContent"], [91, 20, 75, 18], [91, 22, 75, 20], [91, 30, 75, 28], [92, 6, 76, 4, "marginTop"], [92, 15, 76, 13], [92, 17, 76, 15], [92, 20, 76, 18], [93, 6, 77, 4, "paddingHorizontal"], [93, 23, 77, 21], [93, 25, 77, 23], [94, 4, 78, 2], [94, 5, 78, 3], [95, 4, 79, 2, "content"], [95, 11, 79, 9], [95, 13, 79, 11], [96, 6, 80, 4, "alignItems"], [96, 16, 80, 14], [96, 18, 80, 16], [96, 30, 80, 28], [97, 6, 81, 4, "flexDirection"], [97, 19, 81, 17], [97, 21, 81, 19], [97, 26, 81, 24], [98, 6, 82, 4, "borderRadius"], [98, 18, 82, 16], [98, 20, 82, 18], [98, 21, 82, 19], [99, 6, 83, 4, "flexGrow"], [99, 14, 83, 12], [99, 16, 83, 14], [99, 17, 83, 15], [100, 6, 84, 4, "flexShrink"], [100, 16, 84, 14], [100, 18, 84, 16], [100, 19, 84, 17], [101, 6, 85, 4, "flexBasis"], [101, 15, 85, 13], [101, 17, 85, 15], [102, 4, 86, 2], [103, 2, 87, 0], [103, 3, 87, 1], [103, 4, 87, 2], [104, 0, 87, 3], [104, 3]], "functionMap": {"names": ["<global>", "LogBoxNotification", "useEffect$argument_0"], "mappings": "AAA;eC8B;YCI;GDE;CDuB"}}, "type": "js/module"}]}