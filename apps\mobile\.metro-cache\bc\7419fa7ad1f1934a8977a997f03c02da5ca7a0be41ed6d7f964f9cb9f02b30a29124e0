{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 44, "index": 58}}], "key": "ioSJ9iLOtXMo2uBjbVE14/NC9RQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.processTransformOrigin = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _errors = require(_dependencyMap[2], \"../errors\");\n  var INDEX_X = 0;\n  var INDEX_Y = 1;\n  var INDEX_Z = 2;\n\n  // Implementation based on https://github.com/facebook/react-native/blob/main/packages/react-native/Libraries/StyleSheet/processTransformOrigin.js\n  var _worklet_12997622101497_init_data = {\n    code: \"function validateTransformOrigin_processTransformOriginTs1(transformOrigin){if(transformOrigin.length!==3){throw new ReanimatedError('Transform origin must have exactly 3 values.');}const[x,y,z]=transformOrigin;if(!(typeof x==='number'||typeof x==='string'&&x.endsWith('%'))){throw new ReanimatedError(\\\"Transform origin x-position must be a number or a percentage string. Passed value: \\\"+x+\\\".\\\");}if(!(typeof y==='number'||typeof y==='string'&&y.endsWith('%'))){throw new ReanimatedError(\\\"Transform origin y-position must be a number or a percentage string. Passed value: \\\"+y+\\\".\\\");}if(typeof z!=='number'){throw new ReanimatedError(\\\"Transform origin z-position must be a number. Passed value: \\\"+z+\\\".\\\");}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\updateProps\\\\processTransformOrigin.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"validateTransformOrigin_processTransformOriginTs1\\\",\\\"transformOrigin\\\",\\\"length\\\",\\\"ReanimatedError\\\",\\\"x\\\",\\\"y\\\",\\\"z\\\",\\\"endsWith\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/updateProps/processTransformOrigin.ts\\\"],\\\"mappings\\\":\\\"AAOA,SAAAA,kDAAAC,eAAA,KAAAA,eAAA,CAAAC,MAAA,MACA,KAAS,KAAAC,eAAA,+CAAiE,EAExE,C,KACE,CAAAC,CAAM,CAAAC,CAAA,CAAIC,CAAA,EAAAL,eAAgB,CAC5B,YAAAG,CAAA,oBAAAA,CAAA,aAAAA,CAAA,CAAAG,QAAA,QACA,KAAQ,IAAG,CAAAJ,eAAO,sFAAe,CAAAC,CAAA,MACjC,C,GACE,QAAU,CAAAC,CAAA,WAAe,SAAAA,CAAA,aAAAA,CAAA,CAAAE,QAAA,QAG3B,UAAAJ,eAAA,uFAAAE,CAAA,MACA,C,GACE,MAAM,CAAAC,CAAI,YAAe,CAG3B,UAAAH,eAAA,gEAAAG,CAAA,MACA,C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var validateTransformOrigin = function () {\n    var _e = [new global.Error(), 1, -27];\n    var validateTransformOrigin = function (transformOrigin) {\n      if (transformOrigin.length !== 3) {\n        throw new _errors.ReanimatedError('Transform origin must have exactly 3 values.');\n      }\n      var _transformOrigin = (0, _slicedToArray2.default)(transformOrigin, 3),\n        x = _transformOrigin[0],\n        y = _transformOrigin[1],\n        z = _transformOrigin[2];\n      if (!(typeof x === 'number' || typeof x === 'string' && x.endsWith('%'))) {\n        throw new _errors.ReanimatedError(`Transform origin x-position must be a number or a percentage string. Passed value: ${x}.`);\n      }\n      if (!(typeof y === 'number' || typeof y === 'string' && y.endsWith('%'))) {\n        throw new _errors.ReanimatedError(`Transform origin y-position must be a number or a percentage string. Passed value: ${y}.`);\n      }\n      if (typeof z !== 'number') {\n        throw new _errors.ReanimatedError(`Transform origin z-position must be a number. Passed value: ${z}.`);\n      }\n    };\n    validateTransformOrigin.__closure = {};\n    validateTransformOrigin.__workletHash = 12997622101497;\n    validateTransformOrigin.__initData = _worklet_12997622101497_init_data;\n    validateTransformOrigin.__stackDetails = _e;\n    return validateTransformOrigin;\n  }();\n  var _worklet_8297997279702_init_data = {\n    code: \"function processTransformOrigin_processTransformOriginTs2(transformOriginIn){const{INDEX_X,INDEX_Z,INDEX_Y,__DEV__,validateTransformOrigin}=this.__closure;let transformOrigin=Array.isArray(transformOriginIn)?transformOriginIn:['50%','50%',0];if(typeof transformOriginIn==='string'){const transformOriginString=transformOriginIn;const regex=/(top|bottom|left|right|center|\\\\d+(?:%|px)|0)/gi;const transformOriginArray=['50%','50%',0];let index=INDEX_X;let matches;while(matches=regex.exec(transformOriginString)){let nextIndex=index+1;const value=matches[0];const valueLower=value.toLowerCase();switch(valueLower){case'left':case'right':{if(index!==INDEX_X){throw new ReanimatedError(\\\"Transform-origin \\\"+value+\\\" can only be used for x-position\\\");}transformOriginArray[INDEX_X]=valueLower==='left'?0:'100%';break;}case'top':case'bottom':{if(index===INDEX_Z){throw new ReanimatedError(\\\"Transform-origin \\\"+value+\\\" can only be used for y-position\\\");}transformOriginArray[INDEX_Y]=valueLower==='top'?0:'100%';if(index===INDEX_X){const horizontal=regex.exec(transformOriginString);if(horizontal==null){break;}switch(horizontal===null||horizontal===void 0?void 0:horizontal[0].toLowerCase()){case'left':transformOriginArray[INDEX_X]=0;break;case'right':transformOriginArray[INDEX_X]='100%';break;case'center':transformOriginArray[INDEX_X]='50%';break;default:throw new ReanimatedError(\\\"Could not parse transform-origin: \\\"+transformOriginString);}nextIndex=INDEX_Z;}break;}case'center':{if(index===INDEX_Z){throw new ReanimatedError(\\\"Transform-origin value \\\"+value+\\\" cannot be used for z-position\\\");}transformOriginArray[index]='50%';break;}default:{if(value.endsWith('%')){transformOriginArray[index]=value;}else{const numericValue=parseFloat(value);if(isNaN(numericValue)){throw new ReanimatedError(\\\"Invalid numeric value in transform-origin: \\\"+value);}transformOriginArray[index]=numericValue;}break;}}index=nextIndex;}transformOrigin=transformOriginArray;}if(typeof transformOriginIn!=='string'&&!Array.isArray(transformOriginIn)){throw new ReanimatedError(\\\"Invalid transformOrigin type: \\\"+typeof transformOriginIn);}if(__DEV__){validateTransformOrigin(transformOrigin);}return transformOrigin;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\updateProps\\\\processTransformOrigin.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"processTransformOrigin_processTransformOriginTs2\\\",\\\"transformOriginIn\\\",\\\"INDEX_X\\\",\\\"INDEX_Z\\\",\\\"INDEX_Y\\\",\\\"__DEV__\\\",\\\"validateTransformOrigin\\\",\\\"__closure\\\",\\\"transformOrigin\\\",\\\"Array\\\",\\\"isArray\\\",\\\"transformOriginString\\\",\\\"regex\\\",\\\"transformOriginArray\\\",\\\"index\\\",\\\"matches\\\",\\\"exec\\\",\\\"nextIndex\\\",\\\"value\\\",\\\"valueLower\\\",\\\"toLowerCase\\\",\\\"ReanimatedError\\\",\\\"horizontal\\\",\\\"endsWith\\\",\\\"numericValue\\\",\\\"parseFloat\\\",\\\"isNaN\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/updateProps/processTransformOrigin.ts\\\"],\\\"mappings\\\":\\\"AA+BO,SAAAA,gDAEmBA,CAAAC,iBAAA,QAAAC,OAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAC,uBAAA,OAAAC,SAAA,CAExB,GAAI,CAAAC,eAAuC,CAAGC,KAAK,CAACC,OAAO,CAACT,iBAAiB,CAAC,CAC1EA,iBAAiB,CACjB,CAAC,KAAK,CAAE,KAAK,CAAE,CAAC,CAAC,CAErB,GAAI,MAAO,CAAAA,iBAAiB,GAAK,QAAQ,CAAE,CACzC,KAAM,CAAAU,qBAAqB,CAAGV,iBAAiB,CAC/C,KAAM,CAAAW,KAAK,CAAG,gDAAgD,CAC9D,KAAM,CAAAC,oBAA4C,CAAG,CAAC,KAAK,CAAE,KAAK,CAAE,CAAC,CAAC,CAEtE,GAAI,CAAAC,KAAK,CAAGZ,OAAO,CACnB,GAAI,CAAAa,OAAO,CACX,MAAQA,OAAO,CAAGH,KAAK,CAACI,IAAI,CAACL,qBAAqB,CAAC,CAAG,CACpD,GAAI,CAAAM,SAAS,CAAGH,KAAK,CAAG,CAAC,CAEzB,KAAM,CAAAI,KAAK,CAAGH,OAAO,CAAC,CAAC,CAAC,CACxB,KAAM,CAAAI,UAAU,CAAGD,KAAK,CAACE,WAAW,CAAC,CAAC,CAEtC,OAAQD,UAAU,EAChB,IAAK,MAAM,CACX,IAAK,OAAO,CAAE,CACZ,GAAIL,KAAK,GAAKZ,OAAO,CAAE,CACrB,KAAM,IAAI,CAAAmB,eAAe,qBACHH,KAAK,mCAC3B,CAAC,CACH,CACAL,oBAAoB,CAACX,OAAO,CAAC,CAAGiB,UAAU,GAAK,MAAM,CAAG,CAAC,CAAG,MAAM,CAClE,MACF,CACA,IAAK,KAAK,CACV,IAAK,QAAQ,CAAE,CACb,GAAIL,KAAK,GAAKX,OAAO,CAAE,CACrB,KAAM,IAAI,CAAAkB,eAAe,qBACHH,KAAK,mCAC3B,CAAC,CACH,CACAL,oBAAoB,CAACT,OAAO,CAAC,CAAGe,UAAU,GAAK,KAAK,CAAG,CAAC,CAAG,MAAM,CAGjE,GAAIL,KAAK,GAAKZ,OAAO,CAAE,CACrB,KAAM,CAAAoB,UAAU,CAAGV,KAAK,CAACI,IAAI,CAACL,qBAAqB,CAAC,CACpD,GAAIW,UAAU,EAAI,IAAI,CAAE,CACtB,MACF,CAEA,OAAQA,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAG,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC,EACnC,IAAK,MAAM,CACTP,oBAAoB,CAACX,OAAO,CAAC,CAAG,CAAC,CACjC,MACF,IAAK,OAAO,CACVW,oBAAoB,CAACX,OAAO,CAAC,CAAG,MAAM,CACtC,MACF,IAAK,QAAQ,CACXW,oBAAoB,CAACX,OAAO,CAAC,CAAG,KAAK,CACrC,MACF,QACE,KAAM,IAAI,CAAAmB,eAAe,sCACcV,qBACvC,CAAC,CACL,CACAM,SAAS,CAAGd,OAAO,CACrB,CAEA,MACF,CACA,IAAK,QAAQ,CAAE,CACb,GAAIW,KAAK,GAAKX,OAAO,CAAE,CACrB,KAAM,IAAI,CAAAkB,eAAe,2BACGH,KAAK,iCACjC,CAAC,CACH,CACAL,oBAAoB,CAACC,KAAK,CAAC,CAAG,KAAK,CACnC,MACF,CACA,QAAS,CACP,GAAII,KAAK,CAACK,QAAQ,CAAC,GAAG,CAAC,CAAE,CACvBV,oBAAoB,CAACC,KAAK,CAAC,CAAGI,KAAK,CACrC,CAAC,IAAM,CACL,KAAM,CAAAM,YAAY,CAAGC,UAAU,CAACP,KAAK,CAAC,CACtC,GAAIQ,KAAK,CAACF,YAAY,CAAC,CAAE,CACvB,KAAM,IAAI,CAAAH,eAAe,+CACuBH,KAChD,CAAC,CACH,CACAL,oBAAoB,CAACC,KAAK,CAAC,CAAGU,YAAY,CAC5C,CACA,MACF,CACF,CAEAV,KAAK,CAAGG,SAAS,CACnB,CAEAT,eAAe,CAAGK,oBAAoB,CACxC,CAEA,GACE,MAAO,CAAAZ,iBAAiB,GAAK,QAAQ,EACrC,CAACQ,KAAK,CAACC,OAAO,CAACT,iBAAiB,CAAC,CACjC,CACA,KAAM,IAAI,CAAAoB,eAAe,kCACU,MAAO,CAAApB,iBAC1C,CAAC,CACH,CAEA,GAAII,OAAO,CAAE,CACXC,uBAAuB,CAACE,eAAe,CAAC,CAC1C,CAEA,MAAO,CAAAA,eAAe,CACxB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var processTransformOrigin = exports.processTransformOrigin = function () {\n    var _e = [new global.Error(), -6, -27];\n    var processTransformOrigin = function (transformOriginIn) {\n      var transformOrigin = Array.isArray(transformOriginIn) ? transformOriginIn : ['50%', '50%', 0];\n      if (typeof transformOriginIn === 'string') {\n        var transformOriginString = transformOriginIn;\n        var regex = /(top|bottom|left|right|center|\\d+(?:%|px)|0)/gi;\n        var transformOriginArray = ['50%', '50%', 0];\n        var index = INDEX_X;\n        var matches;\n        while (matches = regex.exec(transformOriginString)) {\n          var nextIndex = index + 1;\n          var value = matches[0];\n          var valueLower = value.toLowerCase();\n          switch (valueLower) {\n            case 'left':\n            case 'right':\n              {\n                if (index !== INDEX_X) {\n                  throw new _errors.ReanimatedError(`Transform-origin ${value} can only be used for x-position`);\n                }\n                transformOriginArray[INDEX_X] = valueLower === 'left' ? 0 : '100%';\n                break;\n              }\n            case 'top':\n            case 'bottom':\n              {\n                if (index === INDEX_Z) {\n                  throw new _errors.ReanimatedError(`Transform-origin ${value} can only be used for y-position`);\n                }\n                transformOriginArray[INDEX_Y] = valueLower === 'top' ? 0 : '100%';\n\n                // Handle [[ center | left | right ] && [ center | top | bottom ]] <length>?\n                if (index === INDEX_X) {\n                  var horizontal = regex.exec(transformOriginString);\n                  if (horizontal == null) {\n                    break;\n                  }\n                  switch (horizontal?.[0].toLowerCase()) {\n                    case 'left':\n                      transformOriginArray[INDEX_X] = 0;\n                      break;\n                    case 'right':\n                      transformOriginArray[INDEX_X] = '100%';\n                      break;\n                    case 'center':\n                      transformOriginArray[INDEX_X] = '50%';\n                      break;\n                    default:\n                      throw new _errors.ReanimatedError(`Could not parse transform-origin: ${transformOriginString}`);\n                  }\n                  nextIndex = INDEX_Z;\n                }\n                break;\n              }\n            case 'center':\n              {\n                if (index === INDEX_Z) {\n                  throw new _errors.ReanimatedError(`Transform-origin value ${value} cannot be used for z-position`);\n                }\n                transformOriginArray[index] = '50%';\n                break;\n              }\n            default:\n              {\n                if (value.endsWith('%')) {\n                  transformOriginArray[index] = value;\n                } else {\n                  var numericValue = parseFloat(value);\n                  if (isNaN(numericValue)) {\n                    throw new _errors.ReanimatedError(`Invalid numeric value in transform-origin: ${value}`);\n                  }\n                  transformOriginArray[index] = numericValue;\n                }\n                break;\n              }\n          }\n          index = nextIndex;\n        }\n        transformOrigin = transformOriginArray;\n      }\n      if (typeof transformOriginIn !== 'string' && !Array.isArray(transformOriginIn)) {\n        throw new _errors.ReanimatedError(`Invalid transformOrigin type: ${typeof transformOriginIn}`);\n      }\n      if (__DEV__) {\n        validateTransformOrigin(transformOrigin);\n      }\n      return transformOrigin;\n    };\n    processTransformOrigin.__closure = {\n      INDEX_X,\n      INDEX_Z,\n      INDEX_Y,\n      __DEV__,\n      validateTransformOrigin\n    };\n    processTransformOrigin.__workletHash = 8297997279702;\n    processTransformOrigin.__initData = _worklet_8297997279702_init_data;\n    processTransformOrigin.__stackDetails = _e;\n    return processTransformOrigin;\n  }();\n});", "lineCount": 155, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "processTransformOrigin"], [8, 32, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 2, 0], [10, 6, 2, 0, "_errors"], [10, 13, 2, 0], [10, 16, 2, 0, "require"], [10, 23, 2, 0], [10, 24, 2, 0, "_dependencyMap"], [10, 38, 2, 0], [11, 2, 4, 0], [11, 6, 4, 6, "INDEX_X"], [11, 13, 4, 13], [11, 16, 4, 16], [11, 17, 4, 17], [12, 2, 5, 0], [12, 6, 5, 6, "INDEX_Y"], [12, 13, 5, 13], [12, 16, 5, 16], [12, 17, 5, 17], [13, 2, 6, 0], [13, 6, 6, 6, "INDEX_Z"], [13, 13, 6, 13], [13, 16, 6, 16], [13, 17, 6, 17], [15, 2, 8, 0], [16, 2, 8, 0], [16, 6, 8, 0, "_worklet_12997622101497_init_data"], [16, 39, 8, 0], [17, 4, 8, 0, "code"], [17, 8, 8, 0], [18, 4, 8, 0, "location"], [18, 12, 8, 0], [19, 4, 8, 0, "sourceMap"], [19, 13, 8, 0], [20, 4, 8, 0, "version"], [20, 11, 8, 0], [21, 2, 8, 0], [22, 2, 8, 0], [22, 6, 8, 0, "validateTransformOrigin"], [22, 29, 8, 0], [22, 32, 9, 0], [23, 4, 9, 0], [23, 8, 9, 0, "_e"], [23, 10, 9, 0], [23, 18, 9, 0, "global"], [23, 24, 9, 0], [23, 25, 9, 0, "Error"], [23, 30, 9, 0], [24, 4, 9, 0], [24, 8, 9, 0, "validateTransformOrigin"], [24, 31, 9, 0], [24, 43, 9, 0, "validateTransformOrigin"], [24, 44, 9, 33, "transform<PERSON><PERSON>in"], [24, 59, 9, 72], [24, 61, 9, 74], [25, 6, 11, 2], [25, 10, 11, 6, "transform<PERSON><PERSON>in"], [25, 25, 11, 21], [25, 26, 11, 22, "length"], [25, 32, 11, 28], [25, 37, 11, 33], [25, 38, 11, 34], [25, 40, 11, 36], [26, 8, 12, 4], [26, 14, 12, 10], [26, 18, 12, 14, "ReanimatedError"], [26, 41, 12, 29], [26, 42, 12, 30], [26, 88, 12, 76], [26, 89, 12, 77], [27, 6, 13, 2], [28, 6, 14, 2], [28, 10, 14, 2, "_transform<PERSON><PERSON><PERSON>"], [28, 26, 14, 2], [28, 33, 14, 2, "_slicedToArray2"], [28, 48, 14, 2], [28, 49, 14, 2, "default"], [28, 56, 14, 2], [28, 58, 14, 20, "transform<PERSON><PERSON>in"], [28, 73, 14, 35], [29, 8, 14, 9, "x"], [29, 9, 14, 10], [29, 12, 14, 10, "_transform<PERSON><PERSON><PERSON>"], [29, 28, 14, 10], [30, 8, 14, 12, "y"], [30, 9, 14, 13], [30, 12, 14, 13, "_transform<PERSON><PERSON><PERSON>"], [30, 28, 14, 13], [31, 8, 14, 15, "z"], [31, 9, 14, 16], [31, 12, 14, 16, "_transform<PERSON><PERSON><PERSON>"], [31, 28, 14, 16], [32, 6, 15, 2], [32, 10, 15, 6], [32, 12, 15, 8], [32, 19, 15, 15, "x"], [32, 20, 15, 16], [32, 25, 15, 21], [32, 33, 15, 29], [32, 37, 15, 34], [32, 44, 15, 41, "x"], [32, 45, 15, 42], [32, 50, 15, 47], [32, 58, 15, 55], [32, 62, 15, 59, "x"], [32, 63, 15, 60], [32, 64, 15, 61, "endsWith"], [32, 72, 15, 69], [32, 73, 15, 70], [32, 76, 15, 73], [32, 77, 15, 75], [32, 78, 15, 76], [32, 80, 15, 78], [33, 8, 16, 4], [33, 14, 16, 10], [33, 18, 16, 14, "ReanimatedError"], [33, 41, 16, 29], [33, 42, 17, 6], [33, 128, 17, 92, "x"], [33, 129, 17, 93], [33, 132, 18, 4], [33, 133, 18, 5], [34, 6, 19, 2], [35, 6, 20, 2], [35, 10, 20, 6], [35, 12, 20, 8], [35, 19, 20, 15, "y"], [35, 20, 20, 16], [35, 25, 20, 21], [35, 33, 20, 29], [35, 37, 20, 34], [35, 44, 20, 41, "y"], [35, 45, 20, 42], [35, 50, 20, 47], [35, 58, 20, 55], [35, 62, 20, 59, "y"], [35, 63, 20, 60], [35, 64, 20, 61, "endsWith"], [35, 72, 20, 69], [35, 73, 20, 70], [35, 76, 20, 73], [35, 77, 20, 75], [35, 78, 20, 76], [35, 80, 20, 78], [36, 8, 21, 4], [36, 14, 21, 10], [36, 18, 21, 14, "ReanimatedError"], [36, 41, 21, 29], [36, 42, 22, 6], [36, 128, 22, 92, "y"], [36, 129, 22, 93], [36, 132, 23, 4], [36, 133, 23, 5], [37, 6, 24, 2], [38, 6, 25, 2], [38, 10, 25, 6], [38, 17, 25, 13, "z"], [38, 18, 25, 14], [38, 23, 25, 19], [38, 31, 25, 27], [38, 33, 25, 29], [39, 8, 26, 4], [39, 14, 26, 10], [39, 18, 26, 14, "ReanimatedError"], [39, 41, 26, 29], [39, 42, 27, 6], [39, 105, 27, 69, "z"], [39, 106, 27, 70], [39, 109, 28, 4], [39, 110, 28, 5], [40, 6, 29, 2], [41, 4, 30, 0], [41, 5, 30, 1], [42, 4, 30, 1, "validateTransformOrigin"], [42, 27, 30, 1], [42, 28, 30, 1, "__closure"], [42, 37, 30, 1], [43, 4, 30, 1, "validateTransformOrigin"], [43, 27, 30, 1], [43, 28, 30, 1, "__workletHash"], [43, 41, 30, 1], [44, 4, 30, 1, "validateTransformOrigin"], [44, 27, 30, 1], [44, 28, 30, 1, "__initData"], [44, 38, 30, 1], [44, 41, 30, 1, "_worklet_12997622101497_init_data"], [44, 74, 30, 1], [45, 4, 30, 1, "validateTransformOrigin"], [45, 27, 30, 1], [45, 28, 30, 1, "__stackDetails"], [45, 42, 30, 1], [45, 45, 30, 1, "_e"], [45, 47, 30, 1], [46, 4, 30, 1], [46, 11, 30, 1, "validateTransformOrigin"], [46, 34, 30, 1], [47, 2, 30, 1], [47, 3, 9, 0], [48, 2, 9, 0], [48, 6, 9, 0, "_worklet_8297997279702_init_data"], [48, 38, 9, 0], [49, 4, 9, 0, "code"], [49, 8, 9, 0], [50, 4, 9, 0, "location"], [50, 12, 9, 0], [51, 4, 9, 0, "sourceMap"], [51, 13, 9, 0], [52, 4, 9, 0, "version"], [52, 11, 9, 0], [53, 2, 9, 0], [54, 2, 9, 0], [54, 6, 9, 0, "processTransformOrigin"], [54, 28, 9, 0], [54, 31, 9, 0, "exports"], [54, 38, 9, 0], [54, 39, 9, 0, "processTransformOrigin"], [54, 61, 9, 0], [54, 64, 32, 7], [55, 4, 32, 7], [55, 8, 32, 7, "_e"], [55, 10, 32, 7], [55, 18, 32, 7, "global"], [55, 24, 32, 7], [55, 25, 32, 7, "Error"], [55, 30, 32, 7], [56, 4, 32, 7], [56, 8, 32, 7, "processTransformOrigin"], [56, 30, 32, 7], [56, 42, 32, 7, "processTransformOrigin"], [56, 43, 33, 2, "transformOriginIn"], [56, 60, 33, 64], [56, 62, 34, 26], [57, 6, 36, 2], [57, 10, 36, 6, "transform<PERSON><PERSON>in"], [57, 25, 36, 45], [57, 28, 36, 48, "Array"], [57, 33, 36, 53], [57, 34, 36, 54, "isArray"], [57, 41, 36, 61], [57, 42, 36, 62, "transformOriginIn"], [57, 59, 36, 79], [57, 60, 36, 80], [57, 63, 37, 6, "transformOriginIn"], [57, 80, 37, 23], [57, 83, 38, 6], [57, 84, 38, 7], [57, 89, 38, 12], [57, 91, 38, 14], [57, 96, 38, 19], [57, 98, 38, 21], [57, 99, 38, 22], [57, 100, 38, 23], [58, 6, 40, 2], [58, 10, 40, 6], [58, 17, 40, 13, "transformOriginIn"], [58, 34, 40, 30], [58, 39, 40, 35], [58, 47, 40, 43], [58, 49, 40, 45], [59, 8, 41, 4], [59, 12, 41, 10, "transformOriginString"], [59, 33, 41, 31], [59, 36, 41, 34, "transformOriginIn"], [59, 53, 41, 51], [60, 8, 42, 4], [60, 12, 42, 10, "regex"], [60, 17, 42, 15], [60, 20, 42, 18], [60, 68, 42, 66], [61, 8, 43, 4], [61, 12, 43, 10, "transformOriginArray"], [61, 32, 43, 54], [61, 35, 43, 57], [61, 36, 43, 58], [61, 41, 43, 63], [61, 43, 43, 65], [61, 48, 43, 70], [61, 50, 43, 72], [61, 51, 43, 73], [61, 52, 43, 74], [62, 8, 45, 4], [62, 12, 45, 8, "index"], [62, 17, 45, 13], [62, 20, 45, 16, "INDEX_X"], [62, 27, 45, 23], [63, 8, 46, 4], [63, 12, 46, 8, "matches"], [63, 19, 46, 15], [64, 8, 47, 4], [64, 15, 47, 12, "matches"], [64, 22, 47, 19], [64, 25, 47, 22, "regex"], [64, 30, 47, 27], [64, 31, 47, 28, "exec"], [64, 35, 47, 32], [64, 36, 47, 33, "transformOriginString"], [64, 57, 47, 54], [64, 58, 47, 55], [64, 60, 47, 58], [65, 10, 48, 6], [65, 14, 48, 10, "nextIndex"], [65, 23, 48, 19], [65, 26, 48, 22, "index"], [65, 31, 48, 27], [65, 34, 48, 30], [65, 35, 48, 31], [66, 10, 50, 6], [66, 14, 50, 12, "value"], [66, 19, 50, 17], [66, 22, 50, 20, "matches"], [66, 29, 50, 27], [66, 30, 50, 28], [66, 31, 50, 29], [66, 32, 50, 30], [67, 10, 51, 6], [67, 14, 51, 12, "valueLower"], [67, 24, 51, 22], [67, 27, 51, 25, "value"], [67, 32, 51, 30], [67, 33, 51, 31, "toLowerCase"], [67, 44, 51, 42], [67, 45, 51, 43], [67, 46, 51, 44], [68, 10, 53, 6], [68, 18, 53, 14, "valueLower"], [68, 28, 53, 24], [69, 12, 54, 8], [69, 17, 54, 13], [69, 23, 54, 19], [70, 12, 55, 8], [70, 17, 55, 13], [70, 24, 55, 20], [71, 14, 55, 22], [72, 16, 56, 10], [72, 20, 56, 14, "index"], [72, 25, 56, 19], [72, 30, 56, 24, "INDEX_X"], [72, 37, 56, 31], [72, 39, 56, 33], [73, 18, 57, 12], [73, 24, 57, 18], [73, 28, 57, 22, "ReanimatedError"], [73, 51, 57, 37], [73, 52, 58, 14], [73, 72, 58, 34, "value"], [73, 77, 58, 39], [73, 111, 59, 12], [73, 112, 59, 13], [74, 16, 60, 10], [75, 16, 61, 10, "transformOriginArray"], [75, 36, 61, 30], [75, 37, 61, 31, "INDEX_X"], [75, 44, 61, 38], [75, 45, 61, 39], [75, 48, 61, 42, "valueLower"], [75, 58, 61, 52], [75, 63, 61, 57], [75, 69, 61, 63], [75, 72, 61, 66], [75, 73, 61, 67], [75, 76, 61, 70], [75, 82, 61, 76], [76, 16, 62, 10], [77, 14, 63, 8], [78, 12, 64, 8], [78, 17, 64, 13], [78, 22, 64, 18], [79, 12, 65, 8], [79, 17, 65, 13], [79, 25, 65, 21], [80, 14, 65, 23], [81, 16, 66, 10], [81, 20, 66, 14, "index"], [81, 25, 66, 19], [81, 30, 66, 24, "INDEX_Z"], [81, 37, 66, 31], [81, 39, 66, 33], [82, 18, 67, 12], [82, 24, 67, 18], [82, 28, 67, 22, "ReanimatedError"], [82, 51, 67, 37], [82, 52, 68, 14], [82, 72, 68, 34, "value"], [82, 77, 68, 39], [82, 111, 69, 12], [82, 112, 69, 13], [83, 16, 70, 10], [84, 16, 71, 10, "transformOriginArray"], [84, 36, 71, 30], [84, 37, 71, 31, "INDEX_Y"], [84, 44, 71, 38], [84, 45, 71, 39], [84, 48, 71, 42, "valueLower"], [84, 58, 71, 52], [84, 63, 71, 57], [84, 68, 71, 62], [84, 71, 71, 65], [84, 72, 71, 66], [84, 75, 71, 69], [84, 81, 71, 75], [86, 16, 73, 10], [87, 16, 74, 10], [87, 20, 74, 14, "index"], [87, 25, 74, 19], [87, 30, 74, 24, "INDEX_X"], [87, 37, 74, 31], [87, 39, 74, 33], [88, 18, 75, 12], [88, 22, 75, 18, "horizontal"], [88, 32, 75, 28], [88, 35, 75, 31, "regex"], [88, 40, 75, 36], [88, 41, 75, 37, "exec"], [88, 45, 75, 41], [88, 46, 75, 42, "transformOriginString"], [88, 67, 75, 63], [88, 68, 75, 64], [89, 18, 76, 12], [89, 22, 76, 16, "horizontal"], [89, 32, 76, 26], [89, 36, 76, 30], [89, 40, 76, 34], [89, 42, 76, 36], [90, 20, 77, 14], [91, 18, 78, 12], [92, 18, 80, 12], [92, 26, 80, 20, "horizontal"], [92, 36, 80, 30], [92, 39, 80, 33], [92, 40, 80, 34], [92, 41, 80, 35], [92, 42, 80, 36, "toLowerCase"], [92, 53, 80, 47], [92, 54, 80, 48], [92, 55, 80, 49], [93, 20, 81, 14], [93, 25, 81, 19], [93, 31, 81, 25], [94, 22, 82, 16, "transformOriginArray"], [94, 42, 82, 36], [94, 43, 82, 37, "INDEX_X"], [94, 50, 82, 44], [94, 51, 82, 45], [94, 54, 82, 48], [94, 55, 82, 49], [95, 22, 83, 16], [96, 20, 84, 14], [96, 25, 84, 19], [96, 32, 84, 26], [97, 22, 85, 16, "transformOriginArray"], [97, 42, 85, 36], [97, 43, 85, 37, "INDEX_X"], [97, 50, 85, 44], [97, 51, 85, 45], [97, 54, 85, 48], [97, 60, 85, 54], [98, 22, 86, 16], [99, 20, 87, 14], [99, 25, 87, 19], [99, 33, 87, 27], [100, 22, 88, 16, "transformOriginArray"], [100, 42, 88, 36], [100, 43, 88, 37, "INDEX_X"], [100, 50, 88, 44], [100, 51, 88, 45], [100, 54, 88, 48], [100, 59, 88, 53], [101, 22, 89, 16], [102, 20, 90, 14], [103, 22, 91, 16], [103, 28, 91, 22], [103, 32, 91, 26, "ReanimatedError"], [103, 55, 91, 41], [103, 56, 92, 18], [103, 93, 92, 55, "transformOriginString"], [103, 114, 92, 76], [103, 116, 93, 16], [103, 117, 93, 17], [104, 18, 94, 12], [105, 18, 95, 12, "nextIndex"], [105, 27, 95, 21], [105, 30, 95, 24, "INDEX_Z"], [105, 37, 95, 31], [106, 16, 96, 10], [107, 16, 98, 10], [108, 14, 99, 8], [109, 12, 100, 8], [109, 17, 100, 13], [109, 25, 100, 21], [110, 14, 100, 23], [111, 16, 101, 10], [111, 20, 101, 14, "index"], [111, 25, 101, 19], [111, 30, 101, 24, "INDEX_Z"], [111, 37, 101, 31], [111, 39, 101, 33], [112, 18, 102, 12], [112, 24, 102, 18], [112, 28, 102, 22, "ReanimatedError"], [112, 51, 102, 37], [112, 52, 103, 14], [112, 78, 103, 40, "value"], [112, 83, 103, 45], [112, 115, 104, 12], [112, 116, 104, 13], [113, 16, 105, 10], [114, 16, 106, 10, "transformOriginArray"], [114, 36, 106, 30], [114, 37, 106, 31, "index"], [114, 42, 106, 36], [114, 43, 106, 37], [114, 46, 106, 40], [114, 51, 106, 45], [115, 16, 107, 10], [116, 14, 108, 8], [117, 12, 109, 8], [118, 14, 109, 17], [119, 16, 110, 10], [119, 20, 110, 14, "value"], [119, 25, 110, 19], [119, 26, 110, 20, "endsWith"], [119, 34, 110, 28], [119, 35, 110, 29], [119, 38, 110, 32], [119, 39, 110, 33], [119, 41, 110, 35], [120, 18, 111, 12, "transformOriginArray"], [120, 38, 111, 32], [120, 39, 111, 33, "index"], [120, 44, 111, 38], [120, 45, 111, 39], [120, 48, 111, 42, "value"], [120, 53, 111, 47], [121, 16, 112, 10], [121, 17, 112, 11], [121, 23, 112, 17], [122, 18, 113, 12], [122, 22, 113, 18, "numericValue"], [122, 34, 113, 30], [122, 37, 113, 33, "parseFloat"], [122, 47, 113, 43], [122, 48, 113, 44, "value"], [122, 53, 113, 49], [122, 54, 113, 50], [123, 18, 114, 12], [123, 22, 114, 16, "isNaN"], [123, 27, 114, 21], [123, 28, 114, 22, "numericValue"], [123, 40, 114, 34], [123, 41, 114, 35], [123, 43, 114, 37], [124, 20, 115, 14], [124, 26, 115, 20], [124, 30, 115, 24, "ReanimatedError"], [124, 53, 115, 39], [124, 54, 116, 16], [124, 100, 116, 62, "value"], [124, 105, 116, 67], [124, 107, 117, 14], [124, 108, 117, 15], [125, 18, 118, 12], [126, 18, 119, 12, "transformOriginArray"], [126, 38, 119, 32], [126, 39, 119, 33, "index"], [126, 44, 119, 38], [126, 45, 119, 39], [126, 48, 119, 42, "numericValue"], [126, 60, 119, 54], [127, 16, 120, 10], [128, 16, 121, 10], [129, 14, 122, 8], [130, 10, 123, 6], [131, 10, 125, 6, "index"], [131, 15, 125, 11], [131, 18, 125, 14, "nextIndex"], [131, 27, 125, 23], [132, 8, 126, 4], [133, 8, 128, 4, "transform<PERSON><PERSON>in"], [133, 23, 128, 19], [133, 26, 128, 22, "transformOriginArray"], [133, 46, 128, 42], [134, 6, 129, 2], [135, 6, 131, 2], [135, 10, 132, 4], [135, 17, 132, 11, "transformOriginIn"], [135, 34, 132, 28], [135, 39, 132, 33], [135, 47, 132, 41], [135, 51, 133, 4], [135, 52, 133, 5, "Array"], [135, 57, 133, 10], [135, 58, 133, 11, "isArray"], [135, 65, 133, 18], [135, 66, 133, 19, "transformOriginIn"], [135, 83, 133, 36], [135, 84, 133, 37], [135, 86, 134, 4], [136, 8, 135, 4], [136, 14, 135, 10], [136, 18, 135, 14, "ReanimatedError"], [136, 41, 135, 29], [136, 42, 136, 6], [136, 75, 136, 39], [136, 82, 136, 46, "transformOriginIn"], [136, 99, 136, 63], [136, 101, 137, 4], [136, 102, 137, 5], [137, 6, 138, 2], [138, 6, 140, 2], [138, 10, 140, 6, "__DEV__"], [138, 17, 140, 13], [138, 19, 140, 15], [139, 8, 141, 4, "validateTransformOrigin"], [139, 31, 141, 27], [139, 32, 141, 28, "transform<PERSON><PERSON>in"], [139, 47, 141, 43], [139, 48, 141, 44], [140, 6, 142, 2], [141, 6, 144, 2], [141, 13, 144, 9, "transform<PERSON><PERSON>in"], [141, 28, 144, 24], [142, 4, 145, 0], [142, 5, 145, 1], [143, 4, 145, 1, "processTransformOrigin"], [143, 26, 145, 1], [143, 27, 145, 1, "__closure"], [143, 36, 145, 1], [144, 6, 145, 1, "INDEX_X"], [144, 13, 145, 1], [145, 6, 145, 1, "INDEX_Z"], [145, 13, 145, 1], [146, 6, 145, 1, "INDEX_Y"], [146, 13, 145, 1], [147, 6, 145, 1, "__DEV__"], [147, 13, 145, 1], [148, 6, 145, 1, "validateTransformOrigin"], [149, 4, 145, 1], [150, 4, 145, 1, "processTransformOrigin"], [150, 26, 145, 1], [150, 27, 145, 1, "__workletHash"], [150, 40, 145, 1], [151, 4, 145, 1, "processTransformOrigin"], [151, 26, 145, 1], [151, 27, 145, 1, "__initData"], [151, 37, 145, 1], [151, 40, 145, 1, "_worklet_8297997279702_init_data"], [151, 72, 145, 1], [152, 4, 145, 1, "processTransformOrigin"], [152, 26, 145, 1], [152, 27, 145, 1, "__stackDetails"], [152, 41, 145, 1], [152, 44, 145, 1, "_e"], [152, 46, 145, 1], [153, 4, 145, 1], [153, 11, 145, 1, "processTransformOrigin"], [153, 33, 145, 1], [154, 2, 145, 1], [154, 3, 32, 7], [155, 0, 32, 7], [155, 3]], "functionMap": {"names": ["<global>", "validateTransformOrigin", "processTransformOrigin"], "mappings": "AAA;ACQ;CDqB;OEE;CFiH"}}, "type": "js/module"}]}