{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 28}, "end": {"line": 7, "column": 22, "index": 114}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 116}, "end": {"line": 8, "column": 62, "index": 178}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "../components/FooterNavigation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 180}, "end": {"line": 9, "column": 62, "index": 242}}], "key": "i0wsjvqzeC9AyK9fcghZiqZFu6Y=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = HomeScreen;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[3], \"react-native-safe-area-context\");\n  var _FooterNavigation = _interopRequireDefault(require(_dependencyMap[4], \"../components/FooterNavigation\"));\n  var _jsxDevRuntime = require(_dependencyMap[5], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\apps\\\\mobile\\\\src\\\\screens\\\\HomeScreen.tsx\";\n  function HomeScreen(_ref) {\n    var navigation = _ref.navigation;\n    console.log('HomeScreen rendering...');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: {\n        flex: 1,\n        backgroundColor: '#f9fafb'\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f3a823'\n        },\n        edges: ['top']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: {\n          flex: 1,\n          backgroundColor: '#f9fafb'\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            paddingHorizontal: 20,\n            paddingTop: 10,\n            paddingBottom: 20\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 28,\n              fontWeight: 'bold',\n              color: '#111827'\n            },\n            children: \"Welcome to Tap2Go\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 16,\n              color: '#6b7280',\n              marginTop: 4\n            },\n            children: \"Your food delivery app\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.ScrollView, {\n          style: {\n            flex: 1,\n            paddingHorizontal: 20\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              backgroundColor: 'white',\n              borderRadius: 12,\n              padding: 20,\n              marginBottom: 20,\n              alignItems: 'center',\n              shadowColor: '#000',\n              shadowOffset: {\n                width: 0,\n                height: 1\n              },\n              shadowOpacity: 0.1,\n              shadowRadius: 3,\n              elevation: 2\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 24,\n                fontWeight: 'bold',\n                color: '#111827',\n                marginBottom: 12,\n                textAlign: 'center'\n              },\n              children: \"\\uD83D\\uDE80 Tap2Go Mobile App\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                textAlign: 'center',\n                marginBottom: 20,\n                lineHeight: 24\n              },\n              children: \"App is loading successfully! Navigate using the footer below.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              style: {\n                backgroundColor: '#f3a823',\n                paddingHorizontal: 24,\n                paddingVertical: 12,\n                borderRadius: 8\n              },\n              onPress: () => {\n                console.log('Test button pressed!');\n                navigation.navigate('Cart');\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                style: {\n                  color: 'white',\n                  fontSize: 16,\n                  fontWeight: '600'\n                },\n                children: \"Go to Cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              backgroundColor: 'white',\n              borderRadius: 12,\n              padding: 20,\n              marginBottom: 20,\n              shadowColor: '#000',\n              shadowOffset: {\n                width: 0,\n                height: 1\n              },\n              shadowOpacity: 0.1,\n              shadowRadius: 3,\n              elevation: 2\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 18,\n                fontWeight: '600',\n                color: '#111827',\n                marginBottom: 12\n              },\n              children: \"Features Available:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                marginBottom: 8,\n                lineHeight: 24\n              },\n              children: \"\\u2022 Browse restaurants and menus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                marginBottom: 8,\n                lineHeight: 24\n              },\n              children: \"\\u2022 Add items to cart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                marginBottom: 8,\n                lineHeight: 24\n              },\n              children: \"\\u2022 Track your orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                marginBottom: 8,\n                lineHeight: 24\n              },\n              children: \"\\u2022 Search for food\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 16,\n                color: '#6b7280',\n                marginBottom: 8,\n                lineHeight: 24\n              },\n              children: \"\\u2022 Manage your account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              height: 100\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_FooterNavigation.default, {\n        navigation: navigation,\n        activeScreen: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f9fafb'\n        },\n        edges: ['bottom']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 5\n    }, this);\n  }\n  _c = HomeScreen;\n  var _c;\n  $RefreshReg$(_c, \"HomeScreen\");\n});", "lineCount": 283, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_reactNative"], [8, 18, 2, 0], [8, 21, 2, 0, "require"], [8, 28, 2, 0], [8, 29, 2, 0, "_dependencyMap"], [8, 43, 2, 0], [9, 2, 8, 0], [9, 6, 8, 0, "_reactNativeSafeAreaContext"], [9, 33, 8, 0], [9, 36, 8, 0, "require"], [9, 43, 8, 0], [9, 44, 8, 0, "_dependencyMap"], [9, 58, 8, 0], [10, 2, 9, 0], [10, 6, 9, 0, "_FooterNavigation"], [10, 23, 9, 0], [10, 26, 9, 0, "_interopRequireDefault"], [10, 48, 9, 0], [10, 49, 9, 0, "require"], [10, 56, 9, 0], [10, 57, 9, 0, "_dependencyMap"], [10, 71, 9, 0], [11, 2, 9, 62], [11, 6, 9, 62, "_jsxDevRuntime"], [11, 20, 9, 62], [11, 23, 9, 62, "require"], [11, 30, 9, 62], [11, 31, 9, 62, "_dependencyMap"], [11, 45, 9, 62], [12, 2, 9, 62], [12, 6, 9, 62, "_jsxFileName"], [12, 18, 9, 62], [13, 2, 11, 15], [13, 11, 11, 24, "HomeScreen"], [13, 21, 11, 34, "HomeScreen"], [13, 22, 11, 34, "_ref"], [13, 26, 11, 34], [13, 28, 11, 56], [14, 4, 11, 56], [14, 8, 11, 37, "navigation"], [14, 18, 11, 47], [14, 21, 11, 47, "_ref"], [14, 25, 11, 47], [14, 26, 11, 37, "navigation"], [14, 36, 11, 47], [15, 4, 12, 2, "console"], [15, 11, 12, 9], [15, 12, 12, 10, "log"], [15, 15, 12, 13], [15, 16, 12, 14], [15, 41, 12, 39], [15, 42, 12, 40], [16, 4, 14, 2], [16, 24, 15, 4], [16, 28, 15, 4, "_jsxDevRuntime"], [16, 42, 15, 4], [16, 43, 15, 4, "jsxDEV"], [16, 49, 15, 4], [16, 51, 15, 5, "_reactNative"], [16, 63, 15, 5], [16, 64, 15, 5, "View"], [16, 68, 15, 9], [17, 6, 15, 10, "style"], [17, 11, 15, 15], [17, 13, 15, 17], [18, 8, 15, 19, "flex"], [18, 12, 15, 23], [18, 14, 15, 25], [18, 15, 15, 26], [19, 8, 15, 28, "backgroundColor"], [19, 23, 15, 43], [19, 25, 15, 45], [20, 6, 15, 55], [20, 7, 15, 57], [21, 6, 15, 57, "children"], [21, 14, 15, 57], [21, 30, 17, 6], [21, 34, 17, 6, "_jsxDevRuntime"], [21, 48, 17, 6], [21, 49, 17, 6, "jsxDEV"], [21, 55, 17, 6], [21, 57, 17, 7, "_reactNativeSafeAreaContext"], [21, 84, 17, 7], [21, 85, 17, 7, "SafeAreaView"], [21, 97, 17, 19], [22, 8, 17, 20, "style"], [22, 13, 17, 25], [22, 15, 17, 27], [23, 10, 17, 29, "backgroundColor"], [23, 25, 17, 44], [23, 27, 17, 46], [24, 8, 17, 56], [24, 9, 17, 58], [25, 8, 17, 59, "edges"], [25, 13, 17, 64], [25, 15, 17, 66], [25, 16, 17, 67], [25, 21, 17, 72], [26, 6, 17, 74], [27, 8, 17, 74, "fileName"], [27, 16, 17, 74], [27, 18, 17, 74, "_jsxFileName"], [27, 30, 17, 74], [28, 8, 17, 74, "lineNumber"], [28, 18, 17, 74], [29, 8, 17, 74, "columnNumber"], [29, 20, 17, 74], [30, 6, 17, 74], [30, 13, 17, 76], [30, 14, 17, 77], [30, 29, 20, 6], [30, 33, 20, 6, "_jsxDevRuntime"], [30, 47, 20, 6], [30, 48, 20, 6, "jsxDEV"], [30, 54, 20, 6], [30, 56, 20, 7, "_reactNative"], [30, 68, 20, 7], [30, 69, 20, 7, "View"], [30, 73, 20, 11], [31, 8, 20, 12, "style"], [31, 13, 20, 17], [31, 15, 20, 19], [32, 10, 20, 21, "flex"], [32, 14, 20, 25], [32, 16, 20, 27], [32, 17, 20, 28], [33, 10, 20, 30, "backgroundColor"], [33, 25, 20, 45], [33, 27, 20, 47], [34, 8, 20, 57], [34, 9, 20, 59], [35, 8, 20, 59, "children"], [35, 16, 20, 59], [35, 32, 22, 8], [35, 36, 22, 8, "_jsxDevRuntime"], [35, 50, 22, 8], [35, 51, 22, 8, "jsxDEV"], [35, 57, 22, 8], [35, 59, 22, 9, "_reactNative"], [35, 71, 22, 9], [35, 72, 22, 9, "View"], [35, 76, 22, 13], [36, 10, 22, 14, "style"], [36, 15, 22, 19], [36, 17, 22, 21], [37, 12, 23, 10, "paddingHorizontal"], [37, 29, 23, 27], [37, 31, 23, 29], [37, 33, 23, 31], [38, 12, 24, 10, "paddingTop"], [38, 22, 24, 20], [38, 24, 24, 22], [38, 26, 24, 24], [39, 12, 25, 10, "paddingBottom"], [39, 25, 25, 23], [39, 27, 25, 25], [40, 10, 26, 8], [40, 11, 26, 10], [41, 10, 26, 10, "children"], [41, 18, 26, 10], [41, 34, 27, 10], [41, 38, 27, 10, "_jsxDevRuntime"], [41, 52, 27, 10], [41, 53, 27, 10, "jsxDEV"], [41, 59, 27, 10], [41, 61, 27, 11, "_reactNative"], [41, 73, 27, 11], [41, 74, 27, 11, "Text"], [41, 78, 27, 15], [42, 12, 27, 16, "style"], [42, 17, 27, 21], [42, 19, 27, 23], [43, 14, 27, 25, "fontSize"], [43, 22, 27, 33], [43, 24, 27, 35], [43, 26, 27, 37], [44, 14, 27, 39, "fontWeight"], [44, 24, 27, 49], [44, 26, 27, 51], [44, 32, 27, 57], [45, 14, 27, 59, "color"], [45, 19, 27, 64], [45, 21, 27, 66], [46, 12, 27, 76], [46, 13, 27, 78], [47, 12, 27, 78, "children"], [47, 20, 27, 78], [47, 22, 27, 79], [48, 10, 29, 10], [49, 12, 29, 10, "fileName"], [49, 20, 29, 10], [49, 22, 29, 10, "_jsxFileName"], [49, 34, 29, 10], [50, 12, 29, 10, "lineNumber"], [50, 22, 29, 10], [51, 12, 29, 10, "columnNumber"], [51, 24, 29, 10], [52, 10, 29, 10], [52, 17, 29, 16], [52, 18, 29, 17], [52, 33, 30, 10], [52, 37, 30, 10, "_jsxDevRuntime"], [52, 51, 30, 10], [52, 52, 30, 10, "jsxDEV"], [52, 58, 30, 10], [52, 60, 30, 11, "_reactNative"], [52, 72, 30, 11], [52, 73, 30, 11, "Text"], [52, 77, 30, 15], [53, 12, 30, 16, "style"], [53, 17, 30, 21], [53, 19, 30, 23], [54, 14, 30, 25, "fontSize"], [54, 22, 30, 33], [54, 24, 30, 35], [54, 26, 30, 37], [55, 14, 30, 39, "color"], [55, 19, 30, 44], [55, 21, 30, 46], [55, 30, 30, 55], [56, 14, 30, 57, "marginTop"], [56, 23, 30, 66], [56, 25, 30, 68], [57, 12, 30, 70], [57, 13, 30, 72], [58, 12, 30, 72, "children"], [58, 20, 30, 72], [58, 22, 30, 73], [59, 10, 32, 10], [60, 12, 32, 10, "fileName"], [60, 20, 32, 10], [60, 22, 32, 10, "_jsxFileName"], [60, 34, 32, 10], [61, 12, 32, 10, "lineNumber"], [61, 22, 32, 10], [62, 12, 32, 10, "columnNumber"], [62, 24, 32, 10], [63, 10, 32, 10], [63, 17, 32, 16], [63, 18, 32, 17], [64, 8, 32, 17], [65, 10, 32, 17, "fileName"], [65, 18, 32, 17], [65, 20, 32, 17, "_jsxFileName"], [65, 32, 32, 17], [66, 10, 32, 17, "lineNumber"], [66, 20, 32, 17], [67, 10, 32, 17, "columnNumber"], [67, 22, 32, 17], [68, 8, 32, 17], [68, 15, 33, 14], [68, 16, 33, 15], [68, 31, 35, 8], [68, 35, 35, 8, "_jsxDevRuntime"], [68, 49, 35, 8], [68, 50, 35, 8, "jsxDEV"], [68, 56, 35, 8], [68, 58, 35, 9, "_reactNative"], [68, 70, 35, 9], [68, 71, 35, 9, "ScrollView"], [68, 81, 35, 19], [69, 10, 35, 20, "style"], [69, 15, 35, 25], [69, 17, 35, 27], [70, 12, 35, 29, "flex"], [70, 16, 35, 33], [70, 18, 35, 35], [70, 19, 35, 36], [71, 12, 35, 38, "paddingHorizontal"], [71, 29, 35, 55], [71, 31, 35, 57], [72, 10, 35, 60], [72, 11, 35, 62], [73, 10, 35, 62, "children"], [73, 18, 35, 62], [73, 34, 36, 10], [73, 38, 36, 10, "_jsxDevRuntime"], [73, 52, 36, 10], [73, 53, 36, 10, "jsxDEV"], [73, 59, 36, 10], [73, 61, 36, 11, "_reactNative"], [73, 73, 36, 11], [73, 74, 36, 11, "View"], [73, 78, 36, 15], [74, 12, 36, 16, "style"], [74, 17, 36, 21], [74, 19, 36, 23], [75, 14, 37, 12, "backgroundColor"], [75, 29, 37, 27], [75, 31, 37, 29], [75, 38, 37, 36], [76, 14, 38, 12, "borderRadius"], [76, 26, 38, 24], [76, 28, 38, 26], [76, 30, 38, 28], [77, 14, 39, 12, "padding"], [77, 21, 39, 19], [77, 23, 39, 21], [77, 25, 39, 23], [78, 14, 40, 12, "marginBottom"], [78, 26, 40, 24], [78, 28, 40, 26], [78, 30, 40, 28], [79, 14, 41, 12, "alignItems"], [79, 24, 41, 22], [79, 26, 41, 24], [79, 34, 41, 32], [80, 14, 42, 12, "shadowColor"], [80, 25, 42, 23], [80, 27, 42, 25], [80, 33, 42, 31], [81, 14, 43, 12, "shadowOffset"], [81, 26, 43, 24], [81, 28, 43, 26], [82, 16, 43, 28, "width"], [82, 21, 43, 33], [82, 23, 43, 35], [82, 24, 43, 36], [83, 16, 43, 38, "height"], [83, 22, 43, 44], [83, 24, 43, 46], [84, 14, 43, 48], [84, 15, 43, 49], [85, 14, 44, 12, "shadowOpacity"], [85, 27, 44, 25], [85, 29, 44, 27], [85, 32, 44, 30], [86, 14, 45, 12, "shadowRadius"], [86, 26, 45, 24], [86, 28, 45, 26], [86, 29, 45, 27], [87, 14, 46, 12, "elevation"], [87, 23, 46, 21], [87, 25, 46, 23], [88, 12, 47, 10], [88, 13, 47, 12], [89, 12, 47, 12, "children"], [89, 20, 47, 12], [89, 36, 48, 12], [89, 40, 48, 12, "_jsxDevRuntime"], [89, 54, 48, 12], [89, 55, 48, 12, "jsxDEV"], [89, 61, 48, 12], [89, 63, 48, 13, "_reactNative"], [89, 75, 48, 13], [89, 76, 48, 13, "Text"], [89, 80, 48, 17], [90, 14, 48, 18, "style"], [90, 19, 48, 23], [90, 21, 48, 25], [91, 16, 49, 14, "fontSize"], [91, 24, 49, 22], [91, 26, 49, 24], [91, 28, 49, 26], [92, 16, 50, 14, "fontWeight"], [92, 26, 50, 24], [92, 28, 50, 26], [92, 34, 50, 32], [93, 16, 51, 14, "color"], [93, 21, 51, 19], [93, 23, 51, 21], [93, 32, 51, 30], [94, 16, 52, 14, "marginBottom"], [94, 28, 52, 26], [94, 30, 52, 28], [94, 32, 52, 30], [95, 16, 53, 14, "textAlign"], [95, 25, 53, 23], [95, 27, 53, 25], [96, 14, 54, 12], [96, 15, 54, 14], [97, 14, 54, 14, "children"], [97, 22, 54, 14], [97, 24, 54, 15], [98, 12, 56, 12], [99, 14, 56, 12, "fileName"], [99, 22, 56, 12], [99, 24, 56, 12, "_jsxFileName"], [99, 36, 56, 12], [100, 14, 56, 12, "lineNumber"], [100, 24, 56, 12], [101, 14, 56, 12, "columnNumber"], [101, 26, 56, 12], [102, 12, 56, 12], [102, 19, 56, 18], [102, 20, 56, 19], [102, 35, 57, 12], [102, 39, 57, 12, "_jsxDevRuntime"], [102, 53, 57, 12], [102, 54, 57, 12, "jsxDEV"], [102, 60, 57, 12], [102, 62, 57, 13, "_reactNative"], [102, 74, 57, 13], [102, 75, 57, 13, "Text"], [102, 79, 57, 17], [103, 14, 57, 18, "style"], [103, 19, 57, 23], [103, 21, 57, 25], [104, 16, 58, 14, "fontSize"], [104, 24, 58, 22], [104, 26, 58, 24], [104, 28, 58, 26], [105, 16, 59, 14, "color"], [105, 21, 59, 19], [105, 23, 59, 21], [105, 32, 59, 30], [106, 16, 60, 14, "textAlign"], [106, 25, 60, 23], [106, 27, 60, 25], [106, 35, 60, 33], [107, 16, 61, 14, "marginBottom"], [107, 28, 61, 26], [107, 30, 61, 28], [107, 32, 61, 30], [108, 16, 62, 14, "lineHeight"], [108, 26, 62, 24], [108, 28, 62, 26], [109, 14, 63, 12], [109, 15, 63, 14], [110, 14, 63, 14, "children"], [110, 22, 63, 14], [110, 24, 63, 15], [111, 12, 65, 12], [112, 14, 65, 12, "fileName"], [112, 22, 65, 12], [112, 24, 65, 12, "_jsxFileName"], [112, 36, 65, 12], [113, 14, 65, 12, "lineNumber"], [113, 24, 65, 12], [114, 14, 65, 12, "columnNumber"], [114, 26, 65, 12], [115, 12, 65, 12], [115, 19, 65, 18], [115, 20, 65, 19], [115, 35, 66, 12], [115, 39, 66, 12, "_jsxDevRuntime"], [115, 53, 66, 12], [115, 54, 66, 12, "jsxDEV"], [115, 60, 66, 12], [115, 62, 66, 13, "_reactNative"], [115, 74, 66, 13], [115, 75, 66, 13, "TouchableOpacity"], [115, 91, 66, 29], [116, 14, 67, 14, "style"], [116, 19, 67, 19], [116, 21, 67, 21], [117, 16, 68, 16, "backgroundColor"], [117, 31, 68, 31], [117, 33, 68, 33], [117, 42, 68, 42], [118, 16, 69, 16, "paddingHorizontal"], [118, 33, 69, 33], [118, 35, 69, 35], [118, 37, 69, 37], [119, 16, 70, 16, "paddingVertical"], [119, 31, 70, 31], [119, 33, 70, 33], [119, 35, 70, 35], [120, 16, 71, 16, "borderRadius"], [120, 28, 71, 28], [120, 30, 71, 30], [121, 14, 72, 14], [121, 15, 72, 16], [122, 14, 73, 14, "onPress"], [122, 21, 73, 21], [122, 23, 73, 23, "onPress"], [122, 24, 73, 23], [122, 29, 73, 29], [123, 16, 74, 16, "console"], [123, 23, 74, 23], [123, 24, 74, 24, "log"], [123, 27, 74, 27], [123, 28, 74, 28], [123, 50, 74, 50], [123, 51, 74, 51], [124, 16, 75, 16, "navigation"], [124, 26, 75, 26], [124, 27, 75, 27, "navigate"], [124, 35, 75, 35], [124, 36, 75, 36], [124, 42, 75, 42], [124, 43, 75, 43], [125, 14, 76, 14], [125, 15, 76, 16], [126, 14, 76, 16, "children"], [126, 22, 76, 16], [126, 37, 78, 14], [126, 41, 78, 14, "_jsxDevRuntime"], [126, 55, 78, 14], [126, 56, 78, 14, "jsxDEV"], [126, 62, 78, 14], [126, 64, 78, 15, "_reactNative"], [126, 76, 78, 15], [126, 77, 78, 15, "Text"], [126, 81, 78, 19], [127, 16, 78, 20, "style"], [127, 21, 78, 25], [127, 23, 78, 27], [128, 18, 79, 16, "color"], [128, 23, 79, 21], [128, 25, 79, 23], [128, 32, 79, 30], [129, 18, 80, 16, "fontSize"], [129, 26, 80, 24], [129, 28, 80, 26], [129, 30, 80, 28], [130, 18, 81, 16, "fontWeight"], [130, 28, 81, 26], [130, 30, 81, 28], [131, 16, 82, 14], [131, 17, 82, 16], [132, 16, 82, 16, "children"], [132, 24, 82, 16], [132, 26, 82, 17], [133, 14, 84, 14], [134, 16, 84, 14, "fileName"], [134, 24, 84, 14], [134, 26, 84, 14, "_jsxFileName"], [134, 38, 84, 14], [135, 16, 84, 14, "lineNumber"], [135, 26, 84, 14], [136, 16, 84, 14, "columnNumber"], [136, 28, 84, 14], [137, 14, 84, 14], [137, 21, 84, 20], [138, 12, 84, 21], [139, 14, 84, 21, "fileName"], [139, 22, 84, 21], [139, 24, 84, 21, "_jsxFileName"], [139, 36, 84, 21], [140, 14, 84, 21, "lineNumber"], [140, 24, 84, 21], [141, 14, 84, 21, "columnNumber"], [141, 26, 84, 21], [142, 12, 84, 21], [142, 19, 85, 30], [142, 20, 85, 31], [143, 10, 85, 31], [144, 12, 85, 31, "fileName"], [144, 20, 85, 31], [144, 22, 85, 31, "_jsxFileName"], [144, 34, 85, 31], [145, 12, 85, 31, "lineNumber"], [145, 22, 85, 31], [146, 12, 85, 31, "columnNumber"], [146, 24, 85, 31], [147, 10, 85, 31], [147, 17, 86, 16], [147, 18, 86, 17], [147, 33, 88, 10], [147, 37, 88, 10, "_jsxDevRuntime"], [147, 51, 88, 10], [147, 52, 88, 10, "jsxDEV"], [147, 58, 88, 10], [147, 60, 88, 11, "_reactNative"], [147, 72, 88, 11], [147, 73, 88, 11, "View"], [147, 77, 88, 15], [148, 12, 88, 16, "style"], [148, 17, 88, 21], [148, 19, 88, 23], [149, 14, 89, 12, "backgroundColor"], [149, 29, 89, 27], [149, 31, 89, 29], [149, 38, 89, 36], [150, 14, 90, 12, "borderRadius"], [150, 26, 90, 24], [150, 28, 90, 26], [150, 30, 90, 28], [151, 14, 91, 12, "padding"], [151, 21, 91, 19], [151, 23, 91, 21], [151, 25, 91, 23], [152, 14, 92, 12, "marginBottom"], [152, 26, 92, 24], [152, 28, 92, 26], [152, 30, 92, 28], [153, 14, 93, 12, "shadowColor"], [153, 25, 93, 23], [153, 27, 93, 25], [153, 33, 93, 31], [154, 14, 94, 12, "shadowOffset"], [154, 26, 94, 24], [154, 28, 94, 26], [155, 16, 94, 28, "width"], [155, 21, 94, 33], [155, 23, 94, 35], [155, 24, 94, 36], [156, 16, 94, 38, "height"], [156, 22, 94, 44], [156, 24, 94, 46], [157, 14, 94, 48], [157, 15, 94, 49], [158, 14, 95, 12, "shadowOpacity"], [158, 27, 95, 25], [158, 29, 95, 27], [158, 32, 95, 30], [159, 14, 96, 12, "shadowRadius"], [159, 26, 96, 24], [159, 28, 96, 26], [159, 29, 96, 27], [160, 14, 97, 12, "elevation"], [160, 23, 97, 21], [160, 25, 97, 23], [161, 12, 98, 10], [161, 13, 98, 12], [162, 12, 98, 12, "children"], [162, 20, 98, 12], [162, 36, 99, 12], [162, 40, 99, 12, "_jsxDevRuntime"], [162, 54, 99, 12], [162, 55, 99, 12, "jsxDEV"], [162, 61, 99, 12], [162, 63, 99, 13, "_reactNative"], [162, 75, 99, 13], [162, 76, 99, 13, "Text"], [162, 80, 99, 17], [163, 14, 99, 18, "style"], [163, 19, 99, 23], [163, 21, 99, 25], [164, 16, 100, 14, "fontSize"], [164, 24, 100, 22], [164, 26, 100, 24], [164, 28, 100, 26], [165, 16, 101, 14, "fontWeight"], [165, 26, 101, 24], [165, 28, 101, 26], [165, 33, 101, 31], [166, 16, 102, 14, "color"], [166, 21, 102, 19], [166, 23, 102, 21], [166, 32, 102, 30], [167, 16, 103, 14, "marginBottom"], [167, 28, 103, 26], [167, 30, 103, 28], [168, 14, 104, 12], [168, 15, 104, 14], [169, 14, 104, 14, "children"], [169, 22, 104, 14], [169, 24, 104, 15], [170, 12, 106, 12], [171, 14, 106, 12, "fileName"], [171, 22, 106, 12], [171, 24, 106, 12, "_jsxFileName"], [171, 36, 106, 12], [172, 14, 106, 12, "lineNumber"], [172, 24, 106, 12], [173, 14, 106, 12, "columnNumber"], [173, 26, 106, 12], [174, 12, 106, 12], [174, 19, 106, 18], [174, 20, 106, 19], [174, 35, 107, 12], [174, 39, 107, 12, "_jsxDevRuntime"], [174, 53, 107, 12], [174, 54, 107, 12, "jsxDEV"], [174, 60, 107, 12], [174, 62, 107, 13, "_reactNative"], [174, 74, 107, 13], [174, 75, 107, 13, "Text"], [174, 79, 107, 17], [175, 14, 107, 18, "style"], [175, 19, 107, 23], [175, 21, 107, 25], [176, 16, 107, 27, "fontSize"], [176, 24, 107, 35], [176, 26, 107, 37], [176, 28, 107, 39], [177, 16, 107, 41, "color"], [177, 21, 107, 46], [177, 23, 107, 48], [177, 32, 107, 57], [178, 16, 107, 59, "marginBottom"], [178, 28, 107, 71], [178, 30, 107, 73], [178, 31, 107, 74], [179, 16, 107, 76, "lineHeight"], [179, 26, 107, 86], [179, 28, 107, 88], [180, 14, 107, 91], [180, 15, 107, 93], [181, 14, 107, 93, "children"], [181, 22, 107, 93], [181, 24, 107, 94], [182, 12, 109, 12], [183, 14, 109, 12, "fileName"], [183, 22, 109, 12], [183, 24, 109, 12, "_jsxFileName"], [183, 36, 109, 12], [184, 14, 109, 12, "lineNumber"], [184, 24, 109, 12], [185, 14, 109, 12, "columnNumber"], [185, 26, 109, 12], [186, 12, 109, 12], [186, 19, 109, 18], [186, 20, 109, 19], [186, 35, 110, 12], [186, 39, 110, 12, "_jsxDevRuntime"], [186, 53, 110, 12], [186, 54, 110, 12, "jsxDEV"], [186, 60, 110, 12], [186, 62, 110, 13, "_reactNative"], [186, 74, 110, 13], [186, 75, 110, 13, "Text"], [186, 79, 110, 17], [187, 14, 110, 18, "style"], [187, 19, 110, 23], [187, 21, 110, 25], [188, 16, 110, 27, "fontSize"], [188, 24, 110, 35], [188, 26, 110, 37], [188, 28, 110, 39], [189, 16, 110, 41, "color"], [189, 21, 110, 46], [189, 23, 110, 48], [189, 32, 110, 57], [190, 16, 110, 59, "marginBottom"], [190, 28, 110, 71], [190, 30, 110, 73], [190, 31, 110, 74], [191, 16, 110, 76, "lineHeight"], [191, 26, 110, 86], [191, 28, 110, 88], [192, 14, 110, 91], [192, 15, 110, 93], [193, 14, 110, 93, "children"], [193, 22, 110, 93], [193, 24, 110, 94], [194, 12, 112, 12], [195, 14, 112, 12, "fileName"], [195, 22, 112, 12], [195, 24, 112, 12, "_jsxFileName"], [195, 36, 112, 12], [196, 14, 112, 12, "lineNumber"], [196, 24, 112, 12], [197, 14, 112, 12, "columnNumber"], [197, 26, 112, 12], [198, 12, 112, 12], [198, 19, 112, 18], [198, 20, 112, 19], [198, 35, 113, 12], [198, 39, 113, 12, "_jsxDevRuntime"], [198, 53, 113, 12], [198, 54, 113, 12, "jsxDEV"], [198, 60, 113, 12], [198, 62, 113, 13, "_reactNative"], [198, 74, 113, 13], [198, 75, 113, 13, "Text"], [198, 79, 113, 17], [199, 14, 113, 18, "style"], [199, 19, 113, 23], [199, 21, 113, 25], [200, 16, 113, 27, "fontSize"], [200, 24, 113, 35], [200, 26, 113, 37], [200, 28, 113, 39], [201, 16, 113, 41, "color"], [201, 21, 113, 46], [201, 23, 113, 48], [201, 32, 113, 57], [202, 16, 113, 59, "marginBottom"], [202, 28, 113, 71], [202, 30, 113, 73], [202, 31, 113, 74], [203, 16, 113, 76, "lineHeight"], [203, 26, 113, 86], [203, 28, 113, 88], [204, 14, 113, 91], [204, 15, 113, 93], [205, 14, 113, 93, "children"], [205, 22, 113, 93], [205, 24, 113, 94], [206, 12, 115, 12], [207, 14, 115, 12, "fileName"], [207, 22, 115, 12], [207, 24, 115, 12, "_jsxFileName"], [207, 36, 115, 12], [208, 14, 115, 12, "lineNumber"], [208, 24, 115, 12], [209, 14, 115, 12, "columnNumber"], [209, 26, 115, 12], [210, 12, 115, 12], [210, 19, 115, 18], [210, 20, 115, 19], [210, 35, 116, 12], [210, 39, 116, 12, "_jsxDevRuntime"], [210, 53, 116, 12], [210, 54, 116, 12, "jsxDEV"], [210, 60, 116, 12], [210, 62, 116, 13, "_reactNative"], [210, 74, 116, 13], [210, 75, 116, 13, "Text"], [210, 79, 116, 17], [211, 14, 116, 18, "style"], [211, 19, 116, 23], [211, 21, 116, 25], [212, 16, 116, 27, "fontSize"], [212, 24, 116, 35], [212, 26, 116, 37], [212, 28, 116, 39], [213, 16, 116, 41, "color"], [213, 21, 116, 46], [213, 23, 116, 48], [213, 32, 116, 57], [214, 16, 116, 59, "marginBottom"], [214, 28, 116, 71], [214, 30, 116, 73], [214, 31, 116, 74], [215, 16, 116, 76, "lineHeight"], [215, 26, 116, 86], [215, 28, 116, 88], [216, 14, 116, 91], [216, 15, 116, 93], [217, 14, 116, 93, "children"], [217, 22, 116, 93], [217, 24, 116, 94], [218, 12, 118, 12], [219, 14, 118, 12, "fileName"], [219, 22, 118, 12], [219, 24, 118, 12, "_jsxFileName"], [219, 36, 118, 12], [220, 14, 118, 12, "lineNumber"], [220, 24, 118, 12], [221, 14, 118, 12, "columnNumber"], [221, 26, 118, 12], [222, 12, 118, 12], [222, 19, 118, 18], [222, 20, 118, 19], [222, 35, 119, 12], [222, 39, 119, 12, "_jsxDevRuntime"], [222, 53, 119, 12], [222, 54, 119, 12, "jsxDEV"], [222, 60, 119, 12], [222, 62, 119, 13, "_reactNative"], [222, 74, 119, 13], [222, 75, 119, 13, "Text"], [222, 79, 119, 17], [223, 14, 119, 18, "style"], [223, 19, 119, 23], [223, 21, 119, 25], [224, 16, 119, 27, "fontSize"], [224, 24, 119, 35], [224, 26, 119, 37], [224, 28, 119, 39], [225, 16, 119, 41, "color"], [225, 21, 119, 46], [225, 23, 119, 48], [225, 32, 119, 57], [226, 16, 119, 59, "marginBottom"], [226, 28, 119, 71], [226, 30, 119, 73], [226, 31, 119, 74], [227, 16, 119, 76, "lineHeight"], [227, 26, 119, 86], [227, 28, 119, 88], [228, 14, 119, 91], [228, 15, 119, 93], [229, 14, 119, 93, "children"], [229, 22, 119, 93], [229, 24, 119, 94], [230, 12, 121, 12], [231, 14, 121, 12, "fileName"], [231, 22, 121, 12], [231, 24, 121, 12, "_jsxFileName"], [231, 36, 121, 12], [232, 14, 121, 12, "lineNumber"], [232, 24, 121, 12], [233, 14, 121, 12, "columnNumber"], [233, 26, 121, 12], [234, 12, 121, 12], [234, 19, 121, 18], [234, 20, 121, 19], [235, 10, 121, 19], [236, 12, 121, 19, "fileName"], [236, 20, 121, 19], [236, 22, 121, 19, "_jsxFileName"], [236, 34, 121, 19], [237, 12, 121, 19, "lineNumber"], [237, 22, 121, 19], [238, 12, 121, 19, "columnNumber"], [238, 24, 121, 19], [239, 10, 121, 19], [239, 17, 122, 16], [239, 18, 122, 17], [239, 33, 124, 10], [239, 37, 124, 10, "_jsxDevRuntime"], [239, 51, 124, 10], [239, 52, 124, 10, "jsxDEV"], [239, 58, 124, 10], [239, 60, 124, 11, "_reactNative"], [239, 72, 124, 11], [239, 73, 124, 11, "View"], [239, 77, 124, 15], [240, 12, 124, 16, "style"], [240, 17, 124, 21], [240, 19, 124, 23], [241, 14, 124, 25, "height"], [241, 20, 124, 31], [241, 22, 124, 33], [242, 12, 124, 37], [243, 10, 124, 39], [244, 12, 124, 39, "fileName"], [244, 20, 124, 39], [244, 22, 124, 39, "_jsxFileName"], [244, 34, 124, 39], [245, 12, 124, 39, "lineNumber"], [245, 22, 124, 39], [246, 12, 124, 39, "columnNumber"], [246, 24, 124, 39], [247, 10, 124, 39], [247, 17, 124, 41], [247, 18, 124, 42], [248, 8, 124, 42], [249, 10, 124, 42, "fileName"], [249, 18, 124, 42], [249, 20, 124, 42, "_jsxFileName"], [249, 32, 124, 42], [250, 10, 124, 42, "lineNumber"], [250, 20, 124, 42], [251, 10, 124, 42, "columnNumber"], [251, 22, 124, 42], [252, 8, 124, 42], [252, 15, 125, 20], [252, 16, 125, 21], [253, 6, 125, 21], [254, 8, 125, 21, "fileName"], [254, 16, 125, 21], [254, 18, 125, 21, "_jsxFileName"], [254, 30, 125, 21], [255, 8, 125, 21, "lineNumber"], [255, 18, 125, 21], [256, 8, 125, 21, "columnNumber"], [256, 20, 125, 21], [257, 6, 125, 21], [257, 13, 126, 12], [257, 14, 126, 13], [257, 29, 129, 6], [257, 33, 129, 6, "_jsxDevRuntime"], [257, 47, 129, 6], [257, 48, 129, 6, "jsxDEV"], [257, 54, 129, 6], [257, 56, 129, 7, "_FooterNavigation"], [257, 73, 129, 7], [257, 74, 129, 7, "default"], [257, 81, 129, 23], [258, 8, 129, 24, "navigation"], [258, 18, 129, 34], [258, 20, 129, 36, "navigation"], [258, 30, 129, 47], [259, 8, 129, 48, "activeScreen"], [259, 20, 129, 60], [259, 22, 129, 61], [260, 6, 129, 67], [261, 8, 129, 67, "fileName"], [261, 16, 129, 67], [261, 18, 129, 67, "_jsxFileName"], [261, 30, 129, 67], [262, 8, 129, 67, "lineNumber"], [262, 18, 129, 67], [263, 8, 129, 67, "columnNumber"], [263, 20, 129, 67], [264, 6, 129, 67], [264, 13, 129, 69], [264, 14, 129, 70], [264, 29, 132, 6], [264, 33, 132, 6, "_jsxDevRuntime"], [264, 47, 132, 6], [264, 48, 132, 6, "jsxDEV"], [264, 54, 132, 6], [264, 56, 132, 7, "_reactNativeSafeAreaContext"], [264, 83, 132, 7], [264, 84, 132, 7, "SafeAreaView"], [264, 96, 132, 19], [265, 8, 132, 20, "style"], [265, 13, 132, 25], [265, 15, 132, 27], [266, 10, 132, 29, "backgroundColor"], [266, 25, 132, 44], [266, 27, 132, 46], [267, 8, 132, 56], [267, 9, 132, 58], [268, 8, 132, 59, "edges"], [268, 13, 132, 64], [268, 15, 132, 66], [268, 16, 132, 67], [268, 24, 132, 75], [269, 6, 132, 77], [270, 8, 132, 77, "fileName"], [270, 16, 132, 77], [270, 18, 132, 77, "_jsxFileName"], [270, 30, 132, 77], [271, 8, 132, 77, "lineNumber"], [271, 18, 132, 77], [272, 8, 132, 77, "columnNumber"], [272, 20, 132, 77], [273, 6, 132, 77], [273, 13, 132, 79], [273, 14, 132, 80], [274, 4, 132, 80], [275, 6, 132, 80, "fileName"], [275, 14, 132, 80], [275, 16, 132, 80, "_jsxFileName"], [275, 28, 132, 80], [276, 6, 132, 80, "lineNumber"], [276, 16, 132, 80], [277, 6, 132, 80, "columnNumber"], [277, 18, 132, 80], [278, 4, 132, 80], [278, 11, 133, 10], [278, 12, 133, 11], [279, 2, 135, 0], [280, 2, 135, 1, "_c"], [280, 4, 135, 1], [280, 7, 11, 24, "HomeScreen"], [280, 17, 11, 34], [281, 2, 11, 34], [281, 6, 11, 34, "_c"], [281, 8, 11, 34], [282, 2, 11, 34, "$RefreshReg$"], [282, 14, 11, 34], [282, 15, 11, 34, "_c"], [282, 17, 11, 34], [283, 0, 11, 34], [283, 3]], "functionMap": {"names": ["<global>", "HomeScreen", "TouchableOpacity.props.onPress"], "mappings": "AAA;eCU;uBC8D;eDG;CD2D"}}, "type": "js/module"}]}