{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 64}, "end": {"line": 3, "column": 60, "index": 124}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 176}, "end": {"line": 6, "column": 52, "index": 228}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "../specs/NativeReanimatedModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 229}, "end": {"line": 7, "column": 69, "index": 298}}], "key": "Iz31j/q6aFKx378zxr8j+JVEUmQ=", "exportNames": ["*"]}}, {"name": "../threads", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 299}, "end": {"line": 8, "column": 57, "index": 356}}], "key": "K1yKq+VUoHdgwBY7Fz9TrE1h5uU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _PlatformChecker = require(_dependencyMap[4], \"../PlatformChecker\");\n  var _NativeReanimatedModule = _interopRequireDefault(require(_dependencyMap[5], \"../specs/NativeReanimatedModule\"));\n  var _threads = require(_dependencyMap[6], \"../threads\");\n  var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n  var JSPropsUpdaterPaper = /*#__PURE__*/function () {\n    function JSPropsUpdaterPaper() {\n      (0, _classCallCheck2.default)(this, JSPropsUpdaterPaper);\n      this._reanimatedEventEmitter = new _reactNative.NativeEventEmitter(\n      // NativeEventEmitter only uses this parameter on iOS and macOS.\n      _reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'macos' ? _NativeReanimatedModule.default : undefined);\n    }\n    return (0, _createClass2.default)(JSPropsUpdaterPaper, [{\n      key: \"addOnJSPropsChangeListener\",\n      value: function addOnJSPropsChangeListener(animatedComponent) {\n        var viewTag = animatedComponent.getComponentViewTag();\n        JSPropsUpdaterPaper._tagToComponentMapping.set(viewTag, animatedComponent);\n        if (JSPropsUpdaterPaper._tagToComponentMapping.size === 1) {\n          var listener = data => {\n            var component = JSPropsUpdaterPaper._tagToComponentMapping.get(data.viewTag);\n            component?._updateFromNative(data.props);\n          };\n          this._reanimatedEventEmitter.addListener('onReanimatedPropsChange', listener);\n        }\n      }\n    }, {\n      key: \"removeOnJSPropsChangeListener\",\n      value: function removeOnJSPropsChangeListener(animatedComponent) {\n        var viewTag = animatedComponent.getComponentViewTag();\n        JSPropsUpdaterPaper._tagToComponentMapping.delete(viewTag);\n        if (JSPropsUpdaterPaper._tagToComponentMapping.size === 0) {\n          this._reanimatedEventEmitter.removeAllListeners('onReanimatedPropsChange');\n        }\n      }\n    }]);\n  }();\n  JSPropsUpdaterPaper._tagToComponentMapping = new Map();\n  var _worklet_6931184780683_init_data = {\n    code: \"function JSPropsUpdaterTs1(){const{runOnJS,updater}=this.__closure;global.updateJSProps=function(viewTag,props){runOnJS(updater)(viewTag,props);};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\createAnimatedComponent\\\\JSPropsUpdater.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"JSPropsUpdaterTs1\\\",\\\"runOnJS\\\",\\\"updater\\\",\\\"__closure\\\",\\\"global\\\",\\\"updateJSProps\\\",\\\"viewTag\\\",\\\"props\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/createAnimatedComponent/JSPropsUpdater.ts\\\"],\\\"mappings\\\":\\\"AAoFyB,SAAAA,iBAAMA,CAAA,QAAAC,OAAA,CAAAC,OAAA,OAAAC,SAAA,CAEvBC,MAAM,CAACC,aAAa,CAAG,SAACC,OAAe,CAAEC,KAAc,CAAK,CAC1DN,OAAO,CAACC,OAAO,CAAC,CAACI,OAAO,CAAEC,KAAK,CAAC,CAClC,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var JSPropsUpdaterFabric = /*#__PURE__*/function () {\n    function JSPropsUpdaterFabric() {\n      (0, _classCallCheck2.default)(this, JSPropsUpdaterFabric);\n      if (!JSPropsUpdaterFabric.isInitialized) {\n        var updater = (viewTag, props) => {\n          var component = JSPropsUpdaterFabric._tagToComponentMapping.get(viewTag);\n          component?._updateFromNative(props);\n        };\n        (0, _threads.runOnUIImmediately)(function () {\n          var _e = [new global.Error(), -3, -27];\n          var JSPropsUpdaterTs1 = function () {\n            global.updateJSProps = (viewTag, props) => {\n              (0, _threads.runOnJS)(updater)(viewTag, props);\n            };\n          };\n          JSPropsUpdaterTs1.__closure = {\n            runOnJS: _threads.runOnJS,\n            updater\n          };\n          JSPropsUpdaterTs1.__workletHash = 6931184780683;\n          JSPropsUpdaterTs1.__initData = _worklet_6931184780683_init_data;\n          JSPropsUpdaterTs1.__stackDetails = _e;\n          return JSPropsUpdaterTs1;\n        }())();\n        JSPropsUpdaterFabric.isInitialized = true;\n      }\n    }\n    return (0, _createClass2.default)(JSPropsUpdaterFabric, [{\n      key: \"addOnJSPropsChangeListener\",\n      value: function addOnJSPropsChangeListener(animatedComponent) {\n        if (!JSPropsUpdaterFabric.isInitialized) {\n          return;\n        }\n        var viewTag = animatedComponent.getComponentViewTag();\n        JSPropsUpdaterFabric._tagToComponentMapping.set(viewTag, animatedComponent);\n      }\n    }, {\n      key: \"removeOnJSPropsChangeListener\",\n      value: function removeOnJSPropsChangeListener(animatedComponent) {\n        if (!JSPropsUpdaterFabric.isInitialized) {\n          return;\n        }\n        var viewTag = animatedComponent.getComponentViewTag();\n        JSPropsUpdaterFabric._tagToComponentMapping.delete(viewTag);\n      }\n    }]);\n  }();\n  JSPropsUpdaterFabric._tagToComponentMapping = new Map();\n  JSPropsUpdaterFabric.isInitialized = false;\n  var JSPropsUpdaterWeb = /*#__PURE__*/function () {\n    function JSPropsUpdaterWeb() {\n      (0, _classCallCheck2.default)(this, JSPropsUpdaterWeb);\n    }\n    return (0, _createClass2.default)(JSPropsUpdaterWeb, [{\n      key: \"addOnJSPropsChangeListener\",\n      value: function addOnJSPropsChangeListener(_animatedComponent) {\n        // noop\n      }\n    }, {\n      key: \"removeOnJSPropsChangeListener\",\n      value: function removeOnJSPropsChangeListener(_animatedComponent) {\n        // noop\n      }\n    }]);\n  }();\n  var JSPropsUpdater;\n  if (SHOULD_BE_USE_WEB) {\n    JSPropsUpdater = JSPropsUpdaterWeb;\n  } else if (global._IS_FABRIC) {\n    JSPropsUpdater = JSPropsUpdaterFabric;\n  } else {\n    JSPropsUpdater = JSPropsUpdaterPaper;\n  }\n  var _default = exports.default = JSPropsUpdater;\n});", "lineCount": 128, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_classCallCheck2"], [9, 22, 1, 13], [9, 25, 1, 13, "_interopRequireDefault"], [9, 47, 1, 13], [9, 48, 1, 13, "require"], [9, 55, 1, 13], [9, 56, 1, 13, "_dependencyMap"], [9, 70, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_createClass2"], [10, 19, 1, 13], [10, 22, 1, 13, "_interopRequireDefault"], [10, 44, 1, 13], [10, 45, 1, 13, "require"], [10, 52, 1, 13], [10, 53, 1, 13, "_dependencyMap"], [10, 67, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "_reactNative"], [11, 18, 3, 0], [11, 21, 3, 0, "require"], [11, 28, 3, 0], [11, 29, 3, 0, "_dependencyMap"], [11, 43, 3, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_PlatformChecker"], [12, 22, 6, 0], [12, 25, 6, 0, "require"], [12, 32, 6, 0], [12, 33, 6, 0, "_dependencyMap"], [12, 47, 6, 0], [13, 2, 7, 0], [13, 6, 7, 0, "_NativeReanimatedModule"], [13, 29, 7, 0], [13, 32, 7, 0, "_interopRequireDefault"], [13, 54, 7, 0], [13, 55, 7, 0, "require"], [13, 62, 7, 0], [13, 63, 7, 0, "_dependencyMap"], [13, 77, 7, 0], [14, 2, 8, 0], [14, 6, 8, 0, "_threads"], [14, 14, 8, 0], [14, 17, 8, 0, "require"], [14, 24, 8, 0], [14, 25, 8, 0, "_dependencyMap"], [14, 39, 8, 0], [15, 2, 21, 0], [15, 6, 21, 6, "SHOULD_BE_USE_WEB"], [15, 23, 21, 23], [15, 26, 21, 26], [15, 30, 21, 26, "shouldBeUseWeb"], [15, 61, 21, 40], [15, 63, 21, 41], [15, 64, 21, 42], [16, 2, 21, 43], [16, 6, 23, 6, "JSPropsUpdaterPaper"], [16, 25, 23, 25], [17, 4, 27, 2], [17, 13, 27, 2, "JSPropsUpdaterPaper"], [17, 33, 27, 2], [17, 35, 27, 16], [18, 6, 27, 16], [18, 10, 27, 16, "_classCallCheck2"], [18, 26, 27, 16], [18, 27, 27, 16, "default"], [18, 34, 27, 16], [18, 42, 27, 16, "JSPropsUpdaterPaper"], [18, 61, 27, 16], [19, 6, 28, 4], [19, 10, 28, 8], [19, 11, 28, 9, "_reanimatedEventEmitter"], [19, 34, 28, 32], [19, 37, 28, 35], [19, 41, 28, 39, "NativeEventEmitter"], [19, 72, 28, 57], [20, 6, 29, 6], [21, 6, 30, 6, "Platform"], [21, 27, 30, 14], [21, 28, 30, 15, "OS"], [21, 30, 30, 17], [21, 35, 30, 22], [21, 40, 30, 27], [21, 44, 30, 31, "Platform"], [21, 65, 30, 39], [21, 66, 30, 40, "OS"], [21, 68, 30, 42], [21, 73, 30, 47], [21, 80, 30, 54], [21, 83, 31, 11, "NativeReanimatedModule"], [21, 114, 31, 33], [21, 117, 32, 10, "undefined"], [21, 126, 33, 4], [21, 127, 33, 5], [22, 4, 34, 2], [23, 4, 34, 3], [23, 15, 34, 3, "_createClass2"], [23, 28, 34, 3], [23, 29, 34, 3, "default"], [23, 36, 34, 3], [23, 38, 34, 3, "JSPropsUpdaterPaper"], [23, 57, 34, 3], [24, 6, 34, 3, "key"], [24, 9, 34, 3], [25, 6, 34, 3, "value"], [25, 11, 34, 3], [25, 13, 36, 2], [25, 22, 36, 9, "addOnJSPropsChangeListener"], [25, 48, 36, 35, "addOnJSPropsChangeListener"], [25, 49, 37, 4, "animatedComponent"], [25, 66, 40, 32], [25, 68, 41, 4], [26, 8, 42, 4], [26, 12, 42, 10, "viewTag"], [26, 19, 42, 17], [26, 22, 42, 20, "animatedComponent"], [26, 39, 42, 37], [26, 40, 42, 38, "getComponentViewTag"], [26, 59, 42, 57], [26, 60, 42, 58], [26, 61, 42, 59], [27, 8, 43, 4, "JSPropsUpdaterPaper"], [27, 27, 43, 23], [27, 28, 43, 24, "_tagToComponentMapping"], [27, 50, 43, 46], [27, 51, 43, 47, "set"], [27, 54, 43, 50], [27, 55, 43, 51, "viewTag"], [27, 62, 43, 58], [27, 64, 43, 60, "animatedComponent"], [27, 81, 43, 77], [27, 82, 43, 78], [28, 8, 44, 4], [28, 12, 44, 8, "JSPropsUpdaterPaper"], [28, 31, 44, 27], [28, 32, 44, 28, "_tagToComponentMapping"], [28, 54, 44, 50], [28, 55, 44, 51, "size"], [28, 59, 44, 55], [28, 64, 44, 60], [28, 65, 44, 61], [28, 67, 44, 63], [29, 10, 45, 6], [29, 14, 45, 12, "listener"], [29, 22, 45, 20], [29, 25, 45, 24, "data"], [29, 29, 45, 42], [29, 33, 45, 47], [30, 12, 46, 8], [30, 16, 46, 14, "component"], [30, 25, 46, 23], [30, 28, 46, 26, "JSPropsUpdaterPaper"], [30, 47, 46, 45], [30, 48, 46, 46, "_tagToComponentMapping"], [30, 70, 46, 68], [30, 71, 46, 69, "get"], [30, 74, 46, 72], [30, 75, 47, 10, "data"], [30, 79, 47, 14], [30, 80, 47, 15, "viewTag"], [30, 87, 48, 8], [30, 88, 48, 9], [31, 12, 49, 8, "component"], [31, 21, 49, 17], [31, 23, 49, 19, "_updateFromNative"], [31, 40, 49, 36], [31, 41, 49, 37, "data"], [31, 45, 49, 41], [31, 46, 49, 42, "props"], [31, 51, 49, 47], [31, 52, 49, 48], [32, 10, 50, 6], [32, 11, 50, 7], [33, 10, 51, 6], [33, 14, 51, 10], [33, 15, 51, 11, "_reanimatedEventEmitter"], [33, 38, 51, 34], [33, 39, 51, 35, "addListener"], [33, 50, 51, 46], [33, 51, 52, 8], [33, 76, 52, 33], [33, 78, 53, 8, "listener"], [33, 86, 54, 6], [33, 87, 54, 7], [34, 8, 55, 4], [35, 6, 56, 2], [36, 4, 56, 3], [37, 6, 56, 3, "key"], [37, 9, 56, 3], [38, 6, 56, 3, "value"], [38, 11, 56, 3], [38, 13, 58, 2], [38, 22, 58, 9, "removeOnJSPropsChangeListener"], [38, 51, 58, 38, "removeOnJSPropsChangeListener"], [38, 52, 59, 4, "animatedComponent"], [38, 69, 62, 32], [38, 71, 63, 4], [39, 8, 64, 4], [39, 12, 64, 10, "viewTag"], [39, 19, 64, 17], [39, 22, 64, 20, "animatedComponent"], [39, 39, 64, 37], [39, 40, 64, 38, "getComponentViewTag"], [39, 59, 64, 57], [39, 60, 64, 58], [39, 61, 64, 59], [40, 8, 65, 4, "JSPropsUpdaterPaper"], [40, 27, 65, 23], [40, 28, 65, 24, "_tagToComponentMapping"], [40, 50, 65, 46], [40, 51, 65, 47, "delete"], [40, 57, 65, 53], [40, 58, 65, 54, "viewTag"], [40, 65, 65, 61], [40, 66, 65, 62], [41, 8, 66, 4], [41, 12, 66, 8, "JSPropsUpdaterPaper"], [41, 31, 66, 27], [41, 32, 66, 28, "_tagToComponentMapping"], [41, 54, 66, 50], [41, 55, 66, 51, "size"], [41, 59, 66, 55], [41, 64, 66, 60], [41, 65, 66, 61], [41, 67, 66, 63], [42, 10, 67, 6], [42, 14, 67, 10], [42, 15, 67, 11, "_reanimatedEventEmitter"], [42, 38, 67, 34], [42, 39, 67, 35, "removeAllListeners"], [42, 57, 67, 53], [42, 58, 68, 8], [42, 83, 69, 6], [42, 84, 69, 7], [43, 8, 70, 4], [44, 6, 71, 2], [45, 4, 71, 3], [46, 2, 71, 3], [47, 2, 23, 6, "JSPropsUpdaterPaper"], [47, 21, 23, 25], [47, 22, 24, 17, "_tagToComponentMapping"], [47, 44, 24, 39], [47, 47, 24, 42], [47, 51, 24, 46, "Map"], [47, 54, 24, 49], [47, 55, 24, 50], [47, 56, 24, 51], [48, 2, 24, 51], [48, 6, 24, 51, "_worklet_6931184780683_init_data"], [48, 38, 24, 51], [49, 4, 24, 51, "code"], [49, 8, 24, 51], [50, 4, 24, 51, "location"], [50, 12, 24, 51], [51, 4, 24, 51, "sourceMap"], [51, 13, 24, 51], [52, 4, 24, 51, "version"], [52, 11, 24, 51], [53, 2, 24, 51], [54, 2, 24, 51], [54, 6, 74, 6, "JSPropsUpdaterFabric"], [54, 26, 74, 26], [55, 4, 78, 2], [55, 13, 78, 2, "JSPropsUpdaterFabric"], [55, 34, 78, 2], [55, 36, 78, 16], [56, 6, 78, 16], [56, 10, 78, 16, "_classCallCheck2"], [56, 26, 78, 16], [56, 27, 78, 16, "default"], [56, 34, 78, 16], [56, 42, 78, 16, "JSPropsUpdaterFabric"], [56, 62, 78, 16], [57, 6, 79, 4], [57, 10, 79, 8], [57, 11, 79, 9, "JSPropsUpdaterFabric"], [57, 31, 79, 29], [57, 32, 79, 30, "isInitialized"], [57, 45, 79, 43], [57, 47, 79, 45], [58, 8, 80, 6], [58, 12, 80, 12, "updater"], [58, 19, 80, 19], [58, 22, 80, 22, "updater"], [58, 23, 80, 23, "viewTag"], [58, 30, 80, 38], [58, 32, 80, 40, "props"], [58, 37, 80, 54], [58, 42, 80, 59], [59, 10, 81, 8], [59, 14, 81, 14, "component"], [59, 23, 81, 23], [59, 26, 82, 10, "JSPropsUpdaterFabric"], [59, 46, 82, 30], [59, 47, 82, 31, "_tagToComponentMapping"], [59, 69, 82, 53], [59, 70, 82, 54, "get"], [59, 73, 82, 57], [59, 74, 82, 58, "viewTag"], [59, 81, 82, 65], [59, 82, 82, 66], [60, 10, 83, 8, "component"], [60, 19, 83, 17], [60, 21, 83, 19, "_updateFromNative"], [60, 38, 83, 36], [60, 39, 83, 37, "props"], [60, 44, 83, 42], [60, 45, 83, 43], [61, 8, 84, 6], [61, 9, 84, 7], [62, 8, 85, 6], [62, 12, 85, 6, "runOnUIImmediately"], [62, 39, 85, 24], [62, 41, 85, 25], [63, 10, 85, 25], [63, 14, 85, 25, "_e"], [63, 16, 85, 25], [63, 24, 85, 25, "global"], [63, 30, 85, 25], [63, 31, 85, 25, "Error"], [63, 36, 85, 25], [64, 10, 85, 25], [64, 14, 85, 25, "JSPropsUpdaterTs1"], [64, 31, 85, 25], [64, 43, 85, 25, "JSPropsUpdaterTs1"], [64, 44, 85, 25], [64, 46, 85, 31], [65, 12, 87, 8, "global"], [65, 18, 87, 14], [65, 19, 87, 15, "updateJSProps"], [65, 32, 87, 28], [65, 35, 87, 31], [65, 36, 87, 32, "viewTag"], [65, 43, 87, 47], [65, 45, 87, 49, "props"], [65, 50, 87, 63], [65, 55, 87, 68], [66, 14, 88, 10], [66, 18, 88, 10, "runOnJS"], [66, 34, 88, 17], [66, 36, 88, 18, "updater"], [66, 43, 88, 25], [66, 44, 88, 26], [66, 45, 88, 27, "viewTag"], [66, 52, 88, 34], [66, 54, 88, 36, "props"], [66, 59, 88, 41], [66, 60, 88, 42], [67, 12, 89, 8], [67, 13, 89, 9], [68, 10, 90, 6], [68, 11, 90, 7], [69, 10, 90, 7, "JSPropsUpdaterTs1"], [69, 27, 90, 7], [69, 28, 90, 7, "__closure"], [69, 37, 90, 7], [70, 12, 90, 7, "runOnJS"], [70, 19, 90, 7], [70, 21, 88, 10, "runOnJS"], [70, 37, 88, 17], [71, 12, 88, 17, "updater"], [72, 10, 88, 17], [73, 10, 88, 17, "JSPropsUpdaterTs1"], [73, 27, 88, 17], [73, 28, 88, 17, "__workletHash"], [73, 41, 88, 17], [74, 10, 88, 17, "JSPropsUpdaterTs1"], [74, 27, 88, 17], [74, 28, 88, 17, "__initData"], [74, 38, 88, 17], [74, 41, 88, 17, "_worklet_6931184780683_init_data"], [74, 73, 88, 17], [75, 10, 88, 17, "JSPropsUpdaterTs1"], [75, 27, 88, 17], [75, 28, 88, 17, "__stackDetails"], [75, 42, 88, 17], [75, 45, 88, 17, "_e"], [75, 47, 88, 17], [76, 10, 88, 17], [76, 17, 88, 17, "JSPropsUpdaterTs1"], [76, 34, 88, 17], [77, 8, 88, 17], [77, 9, 85, 25], [77, 11, 90, 7], [77, 12, 90, 8], [77, 13, 90, 9], [77, 14, 90, 10], [78, 8, 91, 6, "JSPropsUpdaterFabric"], [78, 28, 91, 26], [78, 29, 91, 27, "isInitialized"], [78, 42, 91, 40], [78, 45, 91, 43], [78, 49, 91, 47], [79, 6, 92, 4], [80, 4, 93, 2], [81, 4, 93, 3], [81, 15, 93, 3, "_createClass2"], [81, 28, 93, 3], [81, 29, 93, 3, "default"], [81, 36, 93, 3], [81, 38, 93, 3, "JSPropsUpdaterFabric"], [81, 58, 93, 3], [82, 6, 93, 3, "key"], [82, 9, 93, 3], [83, 6, 93, 3, "value"], [83, 11, 93, 3], [83, 13, 95, 2], [83, 22, 95, 9, "addOnJSPropsChangeListener"], [83, 48, 95, 35, "addOnJSPropsChangeListener"], [83, 49, 96, 4, "animatedComponent"], [83, 66, 99, 32], [83, 68, 100, 4], [84, 8, 101, 4], [84, 12, 101, 8], [84, 13, 101, 9, "JSPropsUpdaterFabric"], [84, 33, 101, 29], [84, 34, 101, 30, "isInitialized"], [84, 47, 101, 43], [84, 49, 101, 45], [85, 10, 102, 6], [86, 8, 103, 4], [87, 8, 104, 4], [87, 12, 104, 10, "viewTag"], [87, 19, 104, 17], [87, 22, 104, 20, "animatedComponent"], [87, 39, 104, 37], [87, 40, 104, 38, "getComponentViewTag"], [87, 59, 104, 57], [87, 60, 104, 58], [87, 61, 104, 59], [88, 8, 105, 4, "JSPropsUpdaterFabric"], [88, 28, 105, 24], [88, 29, 105, 25, "_tagToComponentMapping"], [88, 51, 105, 47], [88, 52, 105, 48, "set"], [88, 55, 105, 51], [88, 56, 105, 52, "viewTag"], [88, 63, 105, 59], [88, 65, 105, 61, "animatedComponent"], [88, 82, 105, 78], [88, 83, 105, 79], [89, 6, 106, 2], [90, 4, 106, 3], [91, 6, 106, 3, "key"], [91, 9, 106, 3], [92, 6, 106, 3, "value"], [92, 11, 106, 3], [92, 13, 108, 2], [92, 22, 108, 9, "removeOnJSPropsChangeListener"], [92, 51, 108, 38, "removeOnJSPropsChangeListener"], [92, 52, 109, 4, "animatedComponent"], [92, 69, 112, 32], [92, 71, 113, 4], [93, 8, 114, 4], [93, 12, 114, 8], [93, 13, 114, 9, "JSPropsUpdaterFabric"], [93, 33, 114, 29], [93, 34, 114, 30, "isInitialized"], [93, 47, 114, 43], [93, 49, 114, 45], [94, 10, 115, 6], [95, 8, 116, 4], [96, 8, 117, 4], [96, 12, 117, 10, "viewTag"], [96, 19, 117, 17], [96, 22, 117, 20, "animatedComponent"], [96, 39, 117, 37], [96, 40, 117, 38, "getComponentViewTag"], [96, 59, 117, 57], [96, 60, 117, 58], [96, 61, 117, 59], [97, 8, 118, 4, "JSPropsUpdaterFabric"], [97, 28, 118, 24], [97, 29, 118, 25, "_tagToComponentMapping"], [97, 51, 118, 47], [97, 52, 118, 48, "delete"], [97, 58, 118, 54], [97, 59, 118, 55, "viewTag"], [97, 66, 118, 62], [97, 67, 118, 63], [98, 6, 119, 2], [99, 4, 119, 3], [100, 2, 119, 3], [101, 2, 74, 6, "JSPropsUpdaterFabric"], [101, 22, 74, 26], [101, 23, 75, 17, "_tagToComponentMapping"], [101, 45, 75, 39], [101, 48, 75, 42], [101, 52, 75, 46, "Map"], [101, 55, 75, 49], [101, 56, 75, 50], [101, 57, 75, 51], [102, 2, 74, 6, "JSPropsUpdaterFabric"], [102, 22, 74, 26], [102, 23, 76, 17, "isInitialized"], [102, 36, 76, 30], [102, 39, 76, 33], [102, 44, 76, 38], [103, 2, 76, 38], [103, 6, 122, 6, "JSPropsUpdaterWeb"], [103, 23, 122, 23], [104, 4, 122, 23], [104, 13, 122, 23, "JSPropsUpdaterWeb"], [104, 31, 122, 23], [105, 6, 122, 23], [105, 10, 122, 23, "_classCallCheck2"], [105, 26, 122, 23], [105, 27, 122, 23, "default"], [105, 34, 122, 23], [105, 42, 122, 23, "JSPropsUpdaterWeb"], [105, 59, 122, 23], [106, 4, 122, 23], [107, 4, 122, 23], [107, 15, 122, 23, "_createClass2"], [107, 28, 122, 23], [107, 29, 122, 23, "default"], [107, 36, 122, 23], [107, 38, 122, 23, "JSPropsUpdaterWeb"], [107, 55, 122, 23], [108, 6, 122, 23, "key"], [108, 9, 122, 23], [109, 6, 122, 23, "value"], [109, 11, 122, 23], [109, 13, 123, 2], [109, 22, 123, 9, "addOnJSPropsChangeListener"], [109, 48, 123, 35, "addOnJSPropsChangeListener"], [109, 49, 124, 4, "_animatedComponent"], [109, 67, 127, 32], [109, 69, 128, 4], [110, 8, 129, 4], [111, 6, 129, 4], [112, 4, 130, 3], [113, 6, 130, 3, "key"], [113, 9, 130, 3], [114, 6, 130, 3, "value"], [114, 11, 130, 3], [114, 13, 132, 2], [114, 22, 132, 9, "removeOnJSPropsChangeListener"], [114, 51, 132, 38, "removeOnJSPropsChangeListener"], [114, 52, 133, 4, "_animatedComponent"], [114, 70, 136, 32], [114, 72, 137, 4], [115, 8, 138, 4], [116, 6, 138, 4], [117, 4, 139, 3], [118, 2, 139, 3], [119, 2, 147, 0], [119, 6, 147, 4, "JSPropsUpdater"], [119, 20, 147, 41], [120, 2, 148, 0], [120, 6, 148, 4, "SHOULD_BE_USE_WEB"], [120, 23, 148, 21], [120, 25, 148, 23], [121, 4, 149, 2, "JSPropsUpdater"], [121, 18, 149, 16], [121, 21, 149, 19, "JSPropsUpdaterWeb"], [121, 38, 149, 36], [122, 2, 150, 0], [122, 3, 150, 1], [122, 9, 150, 7], [122, 13, 150, 11, "global"], [122, 19, 150, 17], [122, 20, 150, 18, "_IS_FABRIC"], [122, 30, 150, 28], [122, 32, 150, 30], [123, 4, 151, 2, "JSPropsUpdater"], [123, 18, 151, 16], [123, 21, 151, 19, "JSPropsUpdaterFabric"], [123, 41, 151, 39], [124, 2, 152, 0], [124, 3, 152, 1], [124, 9, 152, 7], [125, 4, 153, 2, "JSPropsUpdater"], [125, 18, 153, 16], [125, 21, 153, 19, "JSPropsUpdaterPaper"], [125, 40, 153, 38], [126, 2, 154, 0], [127, 2, 154, 1], [127, 6, 154, 1, "_default"], [127, 14, 154, 1], [127, 17, 154, 1, "exports"], [127, 24, 154, 1], [127, 25, 154, 1, "default"], [127, 32, 154, 1], [127, 35, 156, 15, "JSPropsUpdater"], [127, 49, 156, 29], [128, 0, 156, 29], [128, 3]], "functionMap": {"names": ["<global>", "JSPropsUpdaterPaper", "JSPropsUpdaterPaper#constructor", "JSPropsUpdaterPaper#addOnJSPropsChangeListener", "listener", "JSPropsUpdaterPaper#removeOnJSPropsChangeListener", "JSPropsUpdaterFabric", "JSPropsUpdaterFabric#constructor", "updater", "runOnUIImmediately$argument_0", "global.updateJSProps", "JSPropsUpdaterFabric#addOnJSPropsChangeListener", "JSPropsUpdaterFabric#removeOnJSPropsChangeListener", "JSPropsUpdaterWeb", "JSPropsUpdaterWeb#addOnJSPropsChangeListener", "JSPropsUpdaterWeb#removeOnJSPropsChangeListener"], "mappings": "AAA;ACsB;ECI;GDO;EEE;uBCS;ODK;GFM;EIE;GJa;CDC;AME;ECI;sBCE;ODI;yBEC;+BCE;SDE;OFC;GDG;EKE;GLW;EME;GNW;CNC;AaE;ECC;GDO;EEE;GFO;CbC"}}, "type": "js/module"}]}