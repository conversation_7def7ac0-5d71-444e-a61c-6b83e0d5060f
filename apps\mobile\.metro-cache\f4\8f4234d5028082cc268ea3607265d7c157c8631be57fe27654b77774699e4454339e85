{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./EnsureSingleNavigator.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 67, "index": 114}}], "key": "Eeoj43oWyPbMgkhKsD7HCEmXypI=", "exportNames": ["*"]}}, {"name": "./NavigationFocusedRouteStateContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 115}, "end": {"line": 5, "column": 93, "index": 208}}], "key": "LvPfipyJ1qh0tCX4DRfxx1+v9xA=", "exportNames": ["*"]}}, {"name": "./NavigationStateContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 209}, "end": {"line": 6, "column": 69, "index": 278}}], "key": "vPXNy6i2DuFIp7nHtHgSOvNmS+U=", "exportNames": ["*"]}}, {"name": "./StaticContainer.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 279}, "end": {"line": 7, "column": 55, "index": 334}}], "key": "1lwu4xC1c9jAOsqz4Vr70sYD9fs=", "exportNames": ["*"]}}, {"name": "./useOptionsGetters.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 335}, "end": {"line": 8, "column": 59, "index": 394}}], "key": "ZNBMaad7yyX2HZIWNdpUkNUSHFc=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 395}, "end": {"line": 9, "column": 48, "index": 443}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SceneView = SceneView;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _EnsureSingleNavigator = require(_dependencyMap[3], \"./EnsureSingleNavigator.js\");\n  var _NavigationFocusedRouteStateContext = require(_dependencyMap[4], \"./NavigationFocusedRouteStateContext.js\");\n  var _NavigationStateContext = require(_dependencyMap[5], \"./NavigationStateContext.js\");\n  var _StaticContainer = require(_dependencyMap[6], \"./StaticContainer.js\");\n  var _useOptionsGetters2 = require(_dependencyMap[7], \"./useOptionsGetters.js\");\n  var _jsxRuntime = require(_dependencyMap[8], \"react/jsx-runtime\");\n  var _excluded = [\"state\", \"screen\", \"params\", \"initial\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Component which takes care of rendering the screen for a route.\n   * It provides all required contexts and applies optimizations when applicable.\n   */\n  function SceneView(_ref) {\n    var screen = _ref.screen,\n      route = _ref.route,\n      navigation = _ref.navigation,\n      routeState = _ref.routeState,\n      getState = _ref.getState,\n      setState = _ref.setState,\n      options = _ref.options,\n      clearOptions = _ref.clearOptions;\n    var navigatorKeyRef = React.useRef(undefined);\n    var getKey = React.useCallback(() => navigatorKeyRef.current, []);\n    var _useOptionsGetters = (0, _useOptionsGetters2.useOptionsGetters)({\n        key: route.key,\n        options,\n        navigation\n      }),\n      addOptionsGetter = _useOptionsGetters.addOptionsGetter;\n    var setKey = React.useCallback(key => {\n      navigatorKeyRef.current = key;\n    }, []);\n    var getCurrentState = React.useCallback(() => {\n      var state = getState();\n      var currentRoute = state.routes.find(r => r.key === route.key);\n      return currentRoute ? currentRoute.state : undefined;\n    }, [getState, route.key]);\n    var setCurrentState = React.useCallback(child => {\n      var state = getState();\n      setState({\n        ...state,\n        routes: state.routes.map(r => {\n          if (r.key !== route.key) {\n            return r;\n          }\n          var nextRoute = {\n            ...r,\n            state: child\n          };\n\n          // Before updating the state, cleanup any nested screen and state\n          // This will avoid the navigator trying to handle them again\n          if (nextRoute.params && ('state' in nextRoute.params && typeof nextRoute.params.state === 'object' && nextRoute.params.state !== null || 'screen' in nextRoute.params && typeof nextRoute.params.screen === 'string')) {\n            // @ts-expect-error: we don't have correct type for params\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            var _nextRoute$params = nextRoute.params,\n              _state = _nextRoute$params.state,\n              _screen = _nextRoute$params.screen,\n              params = _nextRoute$params.params,\n              initial = _nextRoute$params.initial,\n              rest = (0, _objectWithoutProperties2.default)(_nextRoute$params, _excluded);\n            if (Object.keys(rest).length) {\n              nextRoute.params = rest;\n            } else {\n              delete nextRoute.params;\n            }\n          }\n          return nextRoute;\n        })\n      });\n    }, [getState, route.key, setState]);\n    var isInitialRef = React.useRef(true);\n    React.useEffect(() => {\n      isInitialRef.current = false;\n    });\n\n    // Clear options set by this screen when it is unmounted\n    React.useEffect(() => {\n      return clearOptions;\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    var getIsInitial = React.useCallback(() => isInitialRef.current, []);\n    var parentFocusedRouteState = React.useContext(_NavigationFocusedRouteStateContext.NavigationFocusedRouteStateContext);\n    var focusedRouteState = React.useMemo(() => {\n      var state = {\n        routes: [{\n          key: route.key,\n          name: route.name,\n          params: route.params,\n          path: route.path\n        }]\n      };\n\n      // Add our state to the innermost route of the parent state\n      var addState = parent => {\n        var parentRoute = parent?.routes[0];\n        if (parentRoute) {\n          return {\n            routes: [{\n              ...parentRoute,\n              state: addState(parentRoute.state)\n            }]\n          };\n        }\n        return state;\n      };\n      return addState(parentFocusedRouteState);\n    }, [parentFocusedRouteState, route.key, route.name, route.params, route.path]);\n    var context = React.useMemo(() => ({\n      state: routeState,\n      getState: getCurrentState,\n      setState: setCurrentState,\n      getKey,\n      setKey,\n      getIsInitial,\n      addOptionsGetter\n    }), [routeState, getCurrentState, setCurrentState, getKey, setKey, getIsInitial, addOptionsGetter]);\n    var ScreenComponent = screen.getComponent ? screen.getComponent() : screen.component;\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationStateContext.NavigationStateContext.Provider, {\n      value: context,\n      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationFocusedRouteStateContext.NavigationFocusedRouteStateContext.Provider, {\n        value: focusedRouteState,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_EnsureSingleNavigator.EnsureSingleNavigator, {\n          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_StaticContainer.StaticContainer, {\n            name: screen.name,\n            render: ScreenComponent || screen.children,\n            navigation: navigation,\n            route: route,\n            children: ScreenComponent !== undefined ? /*#__PURE__*/(0, _jsxRuntime.jsx)(ScreenComponent, {\n              navigation: navigation,\n              route: route\n            }) : screen.children !== undefined ? screen.children({\n              navigation,\n              route\n            }) : null\n          })\n        })\n      })\n    });\n  }\n});", "lineCount": 151, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "SceneView"], [8, 19, 1, 13], [8, 22, 1, 13, "SceneView"], [8, 31, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "React"], [10, 11, 3, 0], [10, 14, 3, 0, "_interopRequireWildcard"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_EnsureSingleNavigator"], [11, 28, 4, 0], [11, 31, 4, 0, "require"], [11, 38, 4, 0], [11, 39, 4, 0, "_dependencyMap"], [11, 53, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_NavigationFocusedRouteStateContext"], [12, 41, 5, 0], [12, 44, 5, 0, "require"], [12, 51, 5, 0], [12, 52, 5, 0, "_dependencyMap"], [12, 66, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_NavigationStateContext"], [13, 29, 6, 0], [13, 32, 6, 0, "require"], [13, 39, 6, 0], [13, 40, 6, 0, "_dependencyMap"], [13, 54, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_StaticContainer"], [14, 22, 7, 0], [14, 25, 7, 0, "require"], [14, 32, 7, 0], [14, 33, 7, 0, "_dependencyMap"], [14, 47, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_useOptionsGetters2"], [15, 25, 8, 0], [15, 28, 8, 0, "require"], [15, 35, 8, 0], [15, 36, 8, 0, "_dependencyMap"], [15, 50, 8, 0], [16, 2, 9, 0], [16, 6, 9, 0, "_jsxRuntime"], [16, 17, 9, 0], [16, 20, 9, 0, "require"], [16, 27, 9, 0], [16, 28, 9, 0, "_dependencyMap"], [16, 42, 9, 0], [17, 2, 9, 48], [17, 6, 9, 48, "_excluded"], [17, 15, 9, 48], [18, 2, 9, 48], [18, 11, 9, 48, "_interopRequireWildcard"], [18, 35, 9, 48, "e"], [18, 36, 9, 48], [18, 38, 9, 48, "t"], [18, 39, 9, 48], [18, 68, 9, 48, "WeakMap"], [18, 75, 9, 48], [18, 81, 9, 48, "r"], [18, 82, 9, 48], [18, 89, 9, 48, "WeakMap"], [18, 96, 9, 48], [18, 100, 9, 48, "n"], [18, 101, 9, 48], [18, 108, 9, 48, "WeakMap"], [18, 115, 9, 48], [18, 127, 9, 48, "_interopRequireWildcard"], [18, 150, 9, 48], [18, 162, 9, 48, "_interopRequireWildcard"], [18, 163, 9, 48, "e"], [18, 164, 9, 48], [18, 166, 9, 48, "t"], [18, 167, 9, 48], [18, 176, 9, 48, "t"], [18, 177, 9, 48], [18, 181, 9, 48, "e"], [18, 182, 9, 48], [18, 186, 9, 48, "e"], [18, 187, 9, 48], [18, 188, 9, 48, "__esModule"], [18, 198, 9, 48], [18, 207, 9, 48, "e"], [18, 208, 9, 48], [18, 214, 9, 48, "o"], [18, 215, 9, 48], [18, 217, 9, 48, "i"], [18, 218, 9, 48], [18, 220, 9, 48, "f"], [18, 221, 9, 48], [18, 226, 9, 48, "__proto__"], [18, 235, 9, 48], [18, 243, 9, 48, "default"], [18, 250, 9, 48], [18, 252, 9, 48, "e"], [18, 253, 9, 48], [18, 270, 9, 48, "e"], [18, 271, 9, 48], [18, 294, 9, 48, "e"], [18, 295, 9, 48], [18, 320, 9, 48, "e"], [18, 321, 9, 48], [18, 330, 9, 48, "f"], [18, 331, 9, 48], [18, 337, 9, 48, "o"], [18, 338, 9, 48], [18, 341, 9, 48, "t"], [18, 342, 9, 48], [18, 345, 9, 48, "n"], [18, 346, 9, 48], [18, 349, 9, 48, "r"], [18, 350, 9, 48], [18, 358, 9, 48, "o"], [18, 359, 9, 48], [18, 360, 9, 48, "has"], [18, 363, 9, 48], [18, 364, 9, 48, "e"], [18, 365, 9, 48], [18, 375, 9, 48, "o"], [18, 376, 9, 48], [18, 377, 9, 48, "get"], [18, 380, 9, 48], [18, 381, 9, 48, "e"], [18, 382, 9, 48], [18, 385, 9, 48, "o"], [18, 386, 9, 48], [18, 387, 9, 48, "set"], [18, 390, 9, 48], [18, 391, 9, 48, "e"], [18, 392, 9, 48], [18, 394, 9, 48, "f"], [18, 395, 9, 48], [18, 409, 9, 48, "_t"], [18, 411, 9, 48], [18, 415, 9, 48, "e"], [18, 416, 9, 48], [18, 432, 9, 48, "_t"], [18, 434, 9, 48], [18, 441, 9, 48, "hasOwnProperty"], [18, 455, 9, 48], [18, 456, 9, 48, "call"], [18, 460, 9, 48], [18, 461, 9, 48, "e"], [18, 462, 9, 48], [18, 464, 9, 48, "_t"], [18, 466, 9, 48], [18, 473, 9, 48, "i"], [18, 474, 9, 48], [18, 478, 9, 48, "o"], [18, 479, 9, 48], [18, 482, 9, 48, "Object"], [18, 488, 9, 48], [18, 489, 9, 48, "defineProperty"], [18, 503, 9, 48], [18, 508, 9, 48, "Object"], [18, 514, 9, 48], [18, 515, 9, 48, "getOwnPropertyDescriptor"], [18, 539, 9, 48], [18, 540, 9, 48, "e"], [18, 541, 9, 48], [18, 543, 9, 48, "_t"], [18, 545, 9, 48], [18, 552, 9, 48, "i"], [18, 553, 9, 48], [18, 554, 9, 48, "get"], [18, 557, 9, 48], [18, 561, 9, 48, "i"], [18, 562, 9, 48], [18, 563, 9, 48, "set"], [18, 566, 9, 48], [18, 570, 9, 48, "o"], [18, 571, 9, 48], [18, 572, 9, 48, "f"], [18, 573, 9, 48], [18, 575, 9, 48, "_t"], [18, 577, 9, 48], [18, 579, 9, 48, "i"], [18, 580, 9, 48], [18, 584, 9, 48, "f"], [18, 585, 9, 48], [18, 586, 9, 48, "_t"], [18, 588, 9, 48], [18, 592, 9, 48, "e"], [18, 593, 9, 48], [18, 594, 9, 48, "_t"], [18, 596, 9, 48], [18, 607, 9, 48, "f"], [18, 608, 9, 48], [18, 613, 9, 48, "e"], [18, 614, 9, 48], [18, 616, 9, 48, "t"], [18, 617, 9, 48], [19, 2, 10, 0], [20, 0, 11, 0], [21, 0, 12, 0], [22, 0, 13, 0], [23, 2, 14, 7], [23, 11, 14, 16, "SceneView"], [23, 20, 14, 25, "SceneView"], [23, 21, 14, 25, "_ref"], [23, 25, 14, 25], [23, 27, 23, 3], [24, 4, 23, 3], [24, 8, 15, 2, "screen"], [24, 14, 15, 8], [24, 17, 15, 8, "_ref"], [24, 21, 15, 8], [24, 22, 15, 2, "screen"], [24, 28, 15, 8], [25, 6, 16, 2, "route"], [25, 11, 16, 7], [25, 14, 16, 7, "_ref"], [25, 18, 16, 7], [25, 19, 16, 2, "route"], [25, 24, 16, 7], [26, 6, 17, 2, "navigation"], [26, 16, 17, 12], [26, 19, 17, 12, "_ref"], [26, 23, 17, 12], [26, 24, 17, 2, "navigation"], [26, 34, 17, 12], [27, 6, 18, 2, "routeState"], [27, 16, 18, 12], [27, 19, 18, 12, "_ref"], [27, 23, 18, 12], [27, 24, 18, 2, "routeState"], [27, 34, 18, 12], [28, 6, 19, 2, "getState"], [28, 14, 19, 10], [28, 17, 19, 10, "_ref"], [28, 21, 19, 10], [28, 22, 19, 2, "getState"], [28, 30, 19, 10], [29, 6, 20, 2, "setState"], [29, 14, 20, 10], [29, 17, 20, 10, "_ref"], [29, 21, 20, 10], [29, 22, 20, 2, "setState"], [29, 30, 20, 10], [30, 6, 21, 2, "options"], [30, 13, 21, 9], [30, 16, 21, 9, "_ref"], [30, 20, 21, 9], [30, 21, 21, 2, "options"], [30, 28, 21, 9], [31, 6, 22, 2, "clearOptions"], [31, 18, 22, 14], [31, 21, 22, 14, "_ref"], [31, 25, 22, 14], [31, 26, 22, 2, "clearOptions"], [31, 38, 22, 14], [32, 4, 24, 2], [32, 8, 24, 8, "navigator<PERSON><PERSON><PERSON><PERSON>"], [32, 23, 24, 23], [32, 26, 24, 26, "React"], [32, 31, 24, 31], [32, 32, 24, 32, "useRef"], [32, 38, 24, 38], [32, 39, 24, 39, "undefined"], [32, 48, 24, 48], [32, 49, 24, 49], [33, 4, 25, 2], [33, 8, 25, 8, "<PERSON><PERSON><PERSON>"], [33, 14, 25, 14], [33, 17, 25, 17, "React"], [33, 22, 25, 22], [33, 23, 25, 23, "useCallback"], [33, 34, 25, 34], [33, 35, 25, 35], [33, 41, 25, 41, "navigator<PERSON><PERSON><PERSON><PERSON>"], [33, 56, 25, 56], [33, 57, 25, 57, "current"], [33, 64, 25, 64], [33, 66, 25, 66], [33, 68, 25, 68], [33, 69, 25, 69], [34, 4, 26, 2], [34, 8, 26, 2, "_useOptionsGetters"], [34, 26, 26, 2], [34, 29, 28, 6], [34, 33, 28, 6, "useOptionsGetters"], [34, 70, 28, 23], [34, 72, 28, 24], [35, 8, 29, 4, "key"], [35, 11, 29, 7], [35, 13, 29, 9, "route"], [35, 18, 29, 14], [35, 19, 29, 15, "key"], [35, 22, 29, 18], [36, 8, 30, 4, "options"], [36, 15, 30, 11], [37, 8, 31, 4, "navigation"], [38, 6, 32, 2], [38, 7, 32, 3], [38, 8, 32, 4], [39, 6, 27, 4, "addOptionsGetter"], [39, 22, 27, 20], [39, 25, 27, 20, "_useOptionsGetters"], [39, 43, 27, 20], [39, 44, 27, 4, "addOptionsGetter"], [39, 60, 27, 20], [40, 4, 33, 2], [40, 8, 33, 8, "<PERSON><PERSON><PERSON>"], [40, 14, 33, 14], [40, 17, 33, 17, "React"], [40, 22, 33, 22], [40, 23, 33, 23, "useCallback"], [40, 34, 33, 34], [40, 35, 33, 35, "key"], [40, 38, 33, 38], [40, 42, 33, 42], [41, 6, 34, 4, "navigator<PERSON><PERSON><PERSON><PERSON>"], [41, 21, 34, 19], [41, 22, 34, 20, "current"], [41, 29, 34, 27], [41, 32, 34, 30, "key"], [41, 35, 34, 33], [42, 4, 35, 2], [42, 5, 35, 3], [42, 7, 35, 5], [42, 9, 35, 7], [42, 10, 35, 8], [43, 4, 36, 2], [43, 8, 36, 8, "getCurrentState"], [43, 23, 36, 23], [43, 26, 36, 26, "React"], [43, 31, 36, 31], [43, 32, 36, 32, "useCallback"], [43, 43, 36, 43], [43, 44, 36, 44], [43, 50, 36, 50], [44, 6, 37, 4], [44, 10, 37, 10, "state"], [44, 15, 37, 15], [44, 18, 37, 18, "getState"], [44, 26, 37, 26], [44, 27, 37, 27], [44, 28, 37, 28], [45, 6, 38, 4], [45, 10, 38, 10, "currentRoute"], [45, 22, 38, 22], [45, 25, 38, 25, "state"], [45, 30, 38, 30], [45, 31, 38, 31, "routes"], [45, 37, 38, 37], [45, 38, 38, 38, "find"], [45, 42, 38, 42], [45, 43, 38, 43, "r"], [45, 44, 38, 44], [45, 48, 38, 48, "r"], [45, 49, 38, 49], [45, 50, 38, 50, "key"], [45, 53, 38, 53], [45, 58, 38, 58, "route"], [45, 63, 38, 63], [45, 64, 38, 64, "key"], [45, 67, 38, 67], [45, 68, 38, 68], [46, 6, 39, 4], [46, 13, 39, 11, "currentRoute"], [46, 25, 39, 23], [46, 28, 39, 26, "currentRoute"], [46, 40, 39, 38], [46, 41, 39, 39, "state"], [46, 46, 39, 44], [46, 49, 39, 47, "undefined"], [46, 58, 39, 56], [47, 4, 40, 2], [47, 5, 40, 3], [47, 7, 40, 5], [47, 8, 40, 6, "getState"], [47, 16, 40, 14], [47, 18, 40, 16, "route"], [47, 23, 40, 21], [47, 24, 40, 22, "key"], [47, 27, 40, 25], [47, 28, 40, 26], [47, 29, 40, 27], [48, 4, 41, 2], [48, 8, 41, 8, "setCurrentState"], [48, 23, 41, 23], [48, 26, 41, 26, "React"], [48, 31, 41, 31], [48, 32, 41, 32, "useCallback"], [48, 43, 41, 43], [48, 44, 41, 44, "child"], [48, 49, 41, 49], [48, 53, 41, 53], [49, 6, 42, 4], [49, 10, 42, 10, "state"], [49, 15, 42, 15], [49, 18, 42, 18, "getState"], [49, 26, 42, 26], [49, 27, 42, 27], [49, 28, 42, 28], [50, 6, 43, 4, "setState"], [50, 14, 43, 12], [50, 15, 43, 13], [51, 8, 44, 6], [51, 11, 44, 9, "state"], [51, 16, 44, 14], [52, 8, 45, 6, "routes"], [52, 14, 45, 12], [52, 16, 45, 14, "state"], [52, 21, 45, 19], [52, 22, 45, 20, "routes"], [52, 28, 45, 26], [52, 29, 45, 27, "map"], [52, 32, 45, 30], [52, 33, 45, 31, "r"], [52, 34, 45, 32], [52, 38, 45, 36], [53, 10, 46, 8], [53, 14, 46, 12, "r"], [53, 15, 46, 13], [53, 16, 46, 14, "key"], [53, 19, 46, 17], [53, 24, 46, 22, "route"], [53, 29, 46, 27], [53, 30, 46, 28, "key"], [53, 33, 46, 31], [53, 35, 46, 33], [54, 12, 47, 10], [54, 19, 47, 17, "r"], [54, 20, 47, 18], [55, 10, 48, 8], [56, 10, 49, 8], [56, 14, 49, 14, "nextRoute"], [56, 23, 49, 23], [56, 26, 49, 26], [57, 12, 50, 10], [57, 15, 50, 13, "r"], [57, 16, 50, 14], [58, 12, 51, 10, "state"], [58, 17, 51, 15], [58, 19, 51, 17, "child"], [59, 10, 52, 8], [59, 11, 52, 9], [61, 10, 54, 8], [62, 10, 55, 8], [63, 10, 56, 8], [63, 14, 56, 12, "nextRoute"], [63, 23, 56, 21], [63, 24, 56, 22, "params"], [63, 30, 56, 28], [63, 35, 56, 33], [63, 42, 56, 40], [63, 46, 56, 44, "nextRoute"], [63, 55, 56, 53], [63, 56, 56, 54, "params"], [63, 62, 56, 60], [63, 66, 56, 64], [63, 73, 56, 71, "nextRoute"], [63, 82, 56, 80], [63, 83, 56, 81, "params"], [63, 89, 56, 87], [63, 90, 56, 88, "state"], [63, 95, 56, 93], [63, 100, 56, 98], [63, 108, 56, 106], [63, 112, 56, 110, "nextRoute"], [63, 121, 56, 119], [63, 122, 56, 120, "params"], [63, 128, 56, 126], [63, 129, 56, 127, "state"], [63, 134, 56, 132], [63, 139, 56, 137], [63, 143, 56, 141], [63, 147, 56, 145], [63, 155, 56, 153], [63, 159, 56, 157, "nextRoute"], [63, 168, 56, 166], [63, 169, 56, 167, "params"], [63, 175, 56, 173], [63, 179, 56, 177], [63, 186, 56, 184, "nextRoute"], [63, 195, 56, 193], [63, 196, 56, 194, "params"], [63, 202, 56, 200], [63, 203, 56, 201, "screen"], [63, 209, 56, 207], [63, 214, 56, 212], [63, 222, 56, 220], [63, 223, 56, 221], [63, 225, 56, 223], [64, 12, 57, 10], [65, 12, 58, 10], [66, 12, 59, 10], [66, 16, 59, 10, "_nextRoute$params"], [66, 33, 59, 10], [66, 36, 65, 14, "nextRoute"], [66, 45, 65, 23], [66, 46, 65, 24, "params"], [66, 52, 65, 30], [67, 14, 60, 12, "state"], [67, 20, 60, 17], [67, 23, 60, 17, "_nextRoute$params"], [67, 40, 60, 17], [67, 41, 60, 12, "state"], [67, 46, 60, 17], [68, 14, 61, 12, "screen"], [68, 21, 61, 18], [68, 24, 61, 18, "_nextRoute$params"], [68, 41, 61, 18], [68, 42, 61, 12, "screen"], [68, 48, 61, 18], [69, 14, 62, 12, "params"], [69, 20, 62, 18], [69, 23, 62, 18, "_nextRoute$params"], [69, 40, 62, 18], [69, 41, 62, 12, "params"], [69, 47, 62, 18], [70, 14, 63, 12, "initial"], [70, 21, 63, 19], [70, 24, 63, 19, "_nextRoute$params"], [70, 41, 63, 19], [70, 42, 63, 12, "initial"], [70, 49, 63, 19], [71, 14, 64, 15, "rest"], [71, 18, 64, 19], [71, 25, 64, 19, "_objectWithoutProperties2"], [71, 50, 64, 19], [71, 51, 64, 19, "default"], [71, 58, 64, 19], [71, 60, 64, 19, "_nextRoute$params"], [71, 77, 64, 19], [71, 79, 64, 19, "_excluded"], [71, 88, 64, 19], [72, 12, 66, 10], [72, 16, 66, 14, "Object"], [72, 22, 66, 20], [72, 23, 66, 21, "keys"], [72, 27, 66, 25], [72, 28, 66, 26, "rest"], [72, 32, 66, 30], [72, 33, 66, 31], [72, 34, 66, 32, "length"], [72, 40, 66, 38], [72, 42, 66, 40], [73, 14, 67, 12, "nextRoute"], [73, 23, 67, 21], [73, 24, 67, 22, "params"], [73, 30, 67, 28], [73, 33, 67, 31, "rest"], [73, 37, 67, 35], [74, 12, 68, 10], [74, 13, 68, 11], [74, 19, 68, 17], [75, 14, 69, 12], [75, 21, 69, 19, "nextRoute"], [75, 30, 69, 28], [75, 31, 69, 29, "params"], [75, 37, 69, 35], [76, 12, 70, 10], [77, 10, 71, 8], [78, 10, 72, 8], [78, 17, 72, 15, "nextRoute"], [78, 26, 72, 24], [79, 8, 73, 6], [79, 9, 73, 7], [80, 6, 74, 4], [80, 7, 74, 5], [80, 8, 74, 6], [81, 4, 75, 2], [81, 5, 75, 3], [81, 7, 75, 5], [81, 8, 75, 6, "getState"], [81, 16, 75, 14], [81, 18, 75, 16, "route"], [81, 23, 75, 21], [81, 24, 75, 22, "key"], [81, 27, 75, 25], [81, 29, 75, 27, "setState"], [81, 37, 75, 35], [81, 38, 75, 36], [81, 39, 75, 37], [82, 4, 76, 2], [82, 8, 76, 8, "isInitialRef"], [82, 20, 76, 20], [82, 23, 76, 23, "React"], [82, 28, 76, 28], [82, 29, 76, 29, "useRef"], [82, 35, 76, 35], [82, 36, 76, 36], [82, 40, 76, 40], [82, 41, 76, 41], [83, 4, 77, 2, "React"], [83, 9, 77, 7], [83, 10, 77, 8, "useEffect"], [83, 19, 77, 17], [83, 20, 77, 18], [83, 26, 77, 24], [84, 6, 78, 4, "isInitialRef"], [84, 18, 78, 16], [84, 19, 78, 17, "current"], [84, 26, 78, 24], [84, 29, 78, 27], [84, 34, 78, 32], [85, 4, 79, 2], [85, 5, 79, 3], [85, 6, 79, 4], [87, 4, 81, 2], [88, 4, 82, 2, "React"], [88, 9, 82, 7], [88, 10, 82, 8, "useEffect"], [88, 19, 82, 17], [88, 20, 82, 18], [88, 26, 82, 24], [89, 6, 83, 4], [89, 13, 83, 11, "clearOptions"], [89, 25, 83, 23], [90, 6, 84, 4], [91, 4, 85, 2], [91, 5, 85, 3], [91, 7, 85, 5], [91, 9, 85, 7], [91, 10, 85, 8], [92, 4, 86, 2], [92, 8, 86, 8, "getIsInitial"], [92, 20, 86, 20], [92, 23, 86, 23, "React"], [92, 28, 86, 28], [92, 29, 86, 29, "useCallback"], [92, 40, 86, 40], [92, 41, 86, 41], [92, 47, 86, 47, "isInitialRef"], [92, 59, 86, 59], [92, 60, 86, 60, "current"], [92, 67, 86, 67], [92, 69, 86, 69], [92, 71, 86, 71], [92, 72, 86, 72], [93, 4, 87, 2], [93, 8, 87, 8, "parentFocusedRouteState"], [93, 31, 87, 31], [93, 34, 87, 34, "React"], [93, 39, 87, 39], [93, 40, 87, 40, "useContext"], [93, 50, 87, 50], [93, 51, 87, 51, "NavigationFocusedRouteStateContext"], [93, 121, 87, 85], [93, 122, 87, 86], [94, 4, 88, 2], [94, 8, 88, 8, "focusedRouteState"], [94, 25, 88, 25], [94, 28, 88, 28, "React"], [94, 33, 88, 33], [94, 34, 88, 34, "useMemo"], [94, 41, 88, 41], [94, 42, 88, 42], [94, 48, 88, 48], [95, 6, 89, 4], [95, 10, 89, 10, "state"], [95, 15, 89, 15], [95, 18, 89, 18], [96, 8, 90, 6, "routes"], [96, 14, 90, 12], [96, 16, 90, 14], [96, 17, 90, 15], [97, 10, 91, 8, "key"], [97, 13, 91, 11], [97, 15, 91, 13, "route"], [97, 20, 91, 18], [97, 21, 91, 19, "key"], [97, 24, 91, 22], [98, 10, 92, 8, "name"], [98, 14, 92, 12], [98, 16, 92, 14, "route"], [98, 21, 92, 19], [98, 22, 92, 20, "name"], [98, 26, 92, 24], [99, 10, 93, 8, "params"], [99, 16, 93, 14], [99, 18, 93, 16, "route"], [99, 23, 93, 21], [99, 24, 93, 22, "params"], [99, 30, 93, 28], [100, 10, 94, 8, "path"], [100, 14, 94, 12], [100, 16, 94, 14, "route"], [100, 21, 94, 19], [100, 22, 94, 20, "path"], [101, 8, 95, 6], [101, 9, 95, 7], [102, 6, 96, 4], [102, 7, 96, 5], [104, 6, 98, 4], [105, 6, 99, 4], [105, 10, 99, 10, "addState"], [105, 18, 99, 18], [105, 21, 99, 21, "parent"], [105, 27, 99, 27], [105, 31, 99, 31], [106, 8, 100, 6], [106, 12, 100, 12, "parentRoute"], [106, 23, 100, 23], [106, 26, 100, 26, "parent"], [106, 32, 100, 32], [106, 34, 100, 34, "routes"], [106, 40, 100, 40], [106, 41, 100, 41], [106, 42, 100, 42], [106, 43, 100, 43], [107, 8, 101, 6], [107, 12, 101, 10, "parentRoute"], [107, 23, 101, 21], [107, 25, 101, 23], [108, 10, 102, 8], [108, 17, 102, 15], [109, 12, 103, 10, "routes"], [109, 18, 103, 16], [109, 20, 103, 18], [109, 21, 103, 19], [110, 14, 104, 12], [110, 17, 104, 15, "parentRoute"], [110, 28, 104, 26], [111, 14, 105, 12, "state"], [111, 19, 105, 17], [111, 21, 105, 19, "addState"], [111, 29, 105, 27], [111, 30, 105, 28, "parentRoute"], [111, 41, 105, 39], [111, 42, 105, 40, "state"], [111, 47, 105, 45], [112, 12, 106, 10], [112, 13, 106, 11], [113, 10, 107, 8], [113, 11, 107, 9], [114, 8, 108, 6], [115, 8, 109, 6], [115, 15, 109, 13, "state"], [115, 20, 109, 18], [116, 6, 110, 4], [116, 7, 110, 5], [117, 6, 111, 4], [117, 13, 111, 11, "addState"], [117, 21, 111, 19], [117, 22, 111, 20, "parentFocusedRouteState"], [117, 45, 111, 43], [117, 46, 111, 44], [118, 4, 112, 2], [118, 5, 112, 3], [118, 7, 112, 5], [118, 8, 112, 6, "parentFocusedRouteState"], [118, 31, 112, 29], [118, 33, 112, 31, "route"], [118, 38, 112, 36], [118, 39, 112, 37, "key"], [118, 42, 112, 40], [118, 44, 112, 42, "route"], [118, 49, 112, 47], [118, 50, 112, 48, "name"], [118, 54, 112, 52], [118, 56, 112, 54, "route"], [118, 61, 112, 59], [118, 62, 112, 60, "params"], [118, 68, 112, 66], [118, 70, 112, 68, "route"], [118, 75, 112, 73], [118, 76, 112, 74, "path"], [118, 80, 112, 78], [118, 81, 112, 79], [118, 82, 112, 80], [119, 4, 113, 2], [119, 8, 113, 8, "context"], [119, 15, 113, 15], [119, 18, 113, 18, "React"], [119, 23, 113, 23], [119, 24, 113, 24, "useMemo"], [119, 31, 113, 31], [119, 32, 113, 32], [119, 39, 113, 39], [120, 6, 114, 4, "state"], [120, 11, 114, 9], [120, 13, 114, 11, "routeState"], [120, 23, 114, 21], [121, 6, 115, 4, "getState"], [121, 14, 115, 12], [121, 16, 115, 14, "getCurrentState"], [121, 31, 115, 29], [122, 6, 116, 4, "setState"], [122, 14, 116, 12], [122, 16, 116, 14, "setCurrentState"], [122, 31, 116, 29], [123, 6, 117, 4, "<PERSON><PERSON><PERSON>"], [123, 12, 117, 10], [124, 6, 118, 4, "<PERSON><PERSON><PERSON>"], [124, 12, 118, 10], [125, 6, 119, 4, "getIsInitial"], [125, 18, 119, 16], [126, 6, 120, 4, "addOptionsGetter"], [127, 4, 121, 2], [127, 5, 121, 3], [127, 6, 121, 4], [127, 8, 121, 6], [127, 9, 121, 7, "routeState"], [127, 19, 121, 17], [127, 21, 121, 19, "getCurrentState"], [127, 36, 121, 34], [127, 38, 121, 36, "setCurrentState"], [127, 53, 121, 51], [127, 55, 121, 53, "<PERSON><PERSON><PERSON>"], [127, 61, 121, 59], [127, 63, 121, 61, "<PERSON><PERSON><PERSON>"], [127, 69, 121, 67], [127, 71, 121, 69, "getIsInitial"], [127, 83, 121, 81], [127, 85, 121, 83, "addOptionsGetter"], [127, 101, 121, 99], [127, 102, 121, 100], [127, 103, 121, 101], [128, 4, 122, 2], [128, 8, 122, 8, "ScreenComponent"], [128, 23, 122, 23], [128, 26, 122, 26, "screen"], [128, 32, 122, 32], [128, 33, 122, 33, "getComponent"], [128, 45, 122, 45], [128, 48, 122, 48, "screen"], [128, 54, 122, 54], [128, 55, 122, 55, "getComponent"], [128, 67, 122, 67], [128, 68, 122, 68], [128, 69, 122, 69], [128, 72, 122, 72, "screen"], [128, 78, 122, 78], [128, 79, 122, 79, "component"], [128, 88, 122, 88], [129, 4, 123, 2], [129, 11, 123, 9], [129, 24, 123, 22], [129, 28, 123, 22, "_jsx"], [129, 43, 123, 26], [129, 45, 123, 27, "NavigationStateContext"], [129, 91, 123, 49], [129, 92, 123, 50, "Provider"], [129, 100, 123, 58], [129, 102, 123, 60], [130, 6, 124, 4, "value"], [130, 11, 124, 9], [130, 13, 124, 11, "context"], [130, 20, 124, 18], [131, 6, 125, 4, "children"], [131, 14, 125, 12], [131, 16, 125, 14], [131, 29, 125, 27], [131, 33, 125, 27, "_jsx"], [131, 48, 125, 31], [131, 50, 125, 32, "NavigationFocusedRouteStateContext"], [131, 120, 125, 66], [131, 121, 125, 67, "Provider"], [131, 129, 125, 75], [131, 131, 125, 77], [132, 8, 126, 6, "value"], [132, 13, 126, 11], [132, 15, 126, 13, "focusedRouteState"], [132, 32, 126, 30], [133, 8, 127, 6, "children"], [133, 16, 127, 14], [133, 18, 127, 16], [133, 31, 127, 29], [133, 35, 127, 29, "_jsx"], [133, 50, 127, 33], [133, 52, 127, 34, "EnsureSingleNavigator"], [133, 96, 127, 55], [133, 98, 127, 57], [134, 10, 128, 8, "children"], [134, 18, 128, 16], [134, 20, 128, 18], [134, 33, 128, 31], [134, 37, 128, 31, "_jsx"], [134, 52, 128, 35], [134, 54, 128, 36, "StaticContainer"], [134, 86, 128, 51], [134, 88, 128, 53], [135, 12, 129, 10, "name"], [135, 16, 129, 14], [135, 18, 129, 16, "screen"], [135, 24, 129, 22], [135, 25, 129, 23, "name"], [135, 29, 129, 27], [136, 12, 130, 10, "render"], [136, 18, 130, 16], [136, 20, 130, 18, "ScreenComponent"], [136, 35, 130, 33], [136, 39, 130, 37, "screen"], [136, 45, 130, 43], [136, 46, 130, 44, "children"], [136, 54, 130, 52], [137, 12, 131, 10, "navigation"], [137, 22, 131, 20], [137, 24, 131, 22, "navigation"], [137, 34, 131, 32], [138, 12, 132, 10, "route"], [138, 17, 132, 15], [138, 19, 132, 17, "route"], [138, 24, 132, 22], [139, 12, 133, 10, "children"], [139, 20, 133, 18], [139, 22, 133, 20, "ScreenComponent"], [139, 37, 133, 35], [139, 42, 133, 40, "undefined"], [139, 51, 133, 49], [139, 54, 133, 52], [139, 67, 133, 65], [139, 71, 133, 65, "_jsx"], [139, 86, 133, 69], [139, 88, 133, 70, "ScreenComponent"], [139, 103, 133, 85], [139, 105, 133, 87], [140, 14, 134, 12, "navigation"], [140, 24, 134, 22], [140, 26, 134, 24, "navigation"], [140, 36, 134, 34], [141, 14, 135, 12, "route"], [141, 19, 135, 17], [141, 21, 135, 19, "route"], [142, 12, 136, 10], [142, 13, 136, 11], [142, 14, 136, 12], [142, 17, 136, 15, "screen"], [142, 23, 136, 21], [142, 24, 136, 22, "children"], [142, 32, 136, 30], [142, 37, 136, 35, "undefined"], [142, 46, 136, 44], [142, 49, 136, 47, "screen"], [142, 55, 136, 53], [142, 56, 136, 54, "children"], [142, 64, 136, 62], [142, 65, 136, 63], [143, 14, 137, 12, "navigation"], [143, 24, 137, 22], [144, 14, 138, 12, "route"], [145, 12, 139, 10], [145, 13, 139, 11], [145, 14, 139, 12], [145, 17, 139, 15], [146, 10, 140, 8], [146, 11, 140, 9], [147, 8, 141, 6], [147, 9, 141, 7], [148, 6, 142, 4], [148, 7, 142, 5], [149, 4, 143, 2], [149, 5, 143, 3], [149, 6, 143, 4], [150, 2, 144, 0], [151, 0, 144, 1], [151, 3]], "functionMap": {"names": ["<global>", "SceneView", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getCurrentState", "state.routes.find$argument_0", "setCurrentState", "state.routes.map$argument_0", "React.useEffect$argument_0", "getIsInitial", "React.useMemo$argument_0", "addState"], "mappings": "AAA;OCa;mCCW,6BD;mCEQ;GFE;4CGC;2CCE,wBD;GHE;4CKC;+BCI;OD4B;GLE;kBOE;GPE;kBOG;GPG;yCQC,0BR;0CSE;qBCW;KDW;GTE;gCSC;ITQ;CDuB"}}, "type": "js/module"}]}