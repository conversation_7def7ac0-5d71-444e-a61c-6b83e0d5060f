{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.INT32_MAX = void 0;\n  exports.deepEqual = deepEqual;\n  exports.hasProperty = hasProperty;\n  exports.isFabric = isFabric;\n  exports.isReact19 = isReact19;\n  exports.isRemoteDebuggingEnabled = isRemoteDebuggingEnabled;\n  exports.isTestEnv = isTestEnv;\n  exports.tagMessage = tagMessage;\n  exports.toArray = toArray;\n  exports.withPrevAndCurrent = withPrevAndCurrent;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  function toArray(object) {\n    if (!Array.isArray(object)) {\n      return [object];\n    }\n    return object;\n  }\n  function withPrevAndCurrent(array, mapFn) {\n    var previousArr = [null];\n    var currentArr = [...array];\n    var transformedArr = [];\n    currentArr.forEach((current, i) => {\n      // This type cast is fine and solves problem mentioned in https://github.com/software-mansion/react-native-gesture-handler/pull/2867 (namely that `previous` can be undefined).\n      // Unfortunately, linter on our CI does not allow this type of casting as it is unnecessary. To bypass that we use eslint-disable.\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n      var previous = previousArr[i];\n      var transformed = mapFn(previous, current);\n      previousArr.push(transformed);\n      transformedArr.push(transformed);\n    });\n    return transformedArr;\n  }\n  function hasProperty(object, key) {\n    return Object.prototype.hasOwnProperty.call(object, key);\n  }\n  function isTestEnv() {\n    // @ts-ignore Do not use `@types/node` because it will prioritise Node types over RN types which breaks the types (ex. setTimeout) in React Native projects.\n    return hasProperty(global, 'process') && process.env.NODE_ENV === 'test';\n  }\n  function tagMessage(msg) {\n    return `[react-native-gesture-handler] ${msg}`;\n  }\n\n  // Helper method to check whether Fabric is enabled, however global.nativeFabricUIManager\n  // may not be initialized before the first render\n  function isFabric() {\n    // @ts-expect-error nativeFabricUIManager is not yet included in the RN types\n    return !!global?.nativeFabricUIManager;\n  }\n  function isReact19() {\n    return _react.default.version.startsWith('19.');\n  }\n  function isRemoteDebuggingEnabled() {\n    // react-native-reanimated checks if in remote debugging in the same way\n    // @ts-ignore global is available but node types are not included\n    var localGlobal = global;\n    return (!localGlobal.nativeCallSyncHook || !!localGlobal.__REMOTEDEV__) && !localGlobal.RN$Bridgeless;\n  }\n\n  /**\n   * Recursively compares two objects for deep equality.\n   *\n   * **Note:** This function does not support cyclic references.\n   *\n   * @param obj1 - The first object to compare.\n   * @param obj2 - The second object to compare.\n   * @returns `true` if the objects are deeply equal, `false` otherwise.\n   */\n  function deepEqual(obj1, obj2) {\n    if (obj1 === obj2) {\n      return true;\n    }\n    if (typeof obj1 !== 'object' || typeof obj2 !== 'object' || obj1 === null || obj2 === null) {\n      return false;\n    }\n    var keys1 = Object.keys(obj1);\n    var keys2 = Object.keys(obj2);\n    if (keys1.length !== keys2.length) {\n      return false;\n    }\n    for (var key of keys1) {\n      if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  var INT32_MAX = exports.INT32_MAX = 2 ** 31 - 1;\n});", "lineCount": 94, "map": [[16, 2, 1, 0], [16, 6, 1, 0, "_react"], [16, 12, 1, 0], [16, 15, 1, 0, "_interopRequireDefault"], [16, 37, 1, 0], [16, 38, 1, 0, "require"], [16, 45, 1, 0], [16, 46, 1, 0, "_dependencyMap"], [16, 60, 1, 0], [17, 2, 3, 7], [17, 11, 3, 16, "toArray"], [17, 18, 3, 23, "toArray"], [17, 19, 3, 27, "object"], [17, 25, 3, 42], [17, 27, 3, 49], [18, 4, 4, 2], [18, 8, 4, 6], [18, 9, 4, 7, "Array"], [18, 14, 4, 12], [18, 15, 4, 13, "isArray"], [18, 22, 4, 20], [18, 23, 4, 21, "object"], [18, 29, 4, 27], [18, 30, 4, 28], [18, 32, 4, 30], [19, 6, 5, 4], [19, 13, 5, 11], [19, 14, 5, 12, "object"], [19, 20, 5, 18], [19, 21, 5, 19], [20, 4, 6, 2], [21, 4, 8, 2], [21, 11, 8, 9, "object"], [21, 17, 8, 15], [22, 2, 9, 0], [23, 2, 15, 7], [23, 11, 15, 16, "withPrevAndCurrent"], [23, 29, 15, 34, "withPrevAndCurrent"], [23, 30, 16, 2, "array"], [23, 35, 16, 12], [23, 37, 17, 2, "mapFn"], [23, 42, 17, 48], [23, 44, 18, 17], [24, 4, 19, 2], [24, 8, 19, 8, "previousArr"], [24, 19, 19, 43], [24, 22, 19, 46], [24, 23, 19, 47], [24, 27, 19, 51], [24, 28, 19, 52], [25, 4, 20, 2], [25, 8, 20, 8, "currentArr"], [25, 18, 20, 18], [25, 21, 20, 21], [25, 22, 20, 22], [25, 25, 20, 25, "array"], [25, 30, 20, 30], [25, 31, 20, 31], [26, 4, 21, 2], [26, 8, 21, 8, "transformedArr"], [26, 22, 21, 37], [26, 25, 21, 40], [26, 27, 21, 42], [27, 4, 22, 2, "currentArr"], [27, 14, 22, 12], [27, 15, 22, 13, "for<PERSON>ach"], [27, 22, 22, 20], [27, 23, 22, 21], [27, 24, 22, 22, "current"], [27, 31, 22, 29], [27, 33, 22, 31, "i"], [27, 34, 22, 32], [27, 39, 22, 37], [28, 6, 23, 4], [29, 6, 24, 4], [30, 6, 25, 4], [31, 6, 26, 4], [31, 10, 26, 10, "previous"], [31, 18, 26, 18], [31, 21, 26, 21, "previousArr"], [31, 32, 26, 32], [31, 33, 26, 33, "i"], [31, 34, 26, 34], [31, 35, 26, 57], [32, 6, 27, 4], [32, 10, 27, 10, "transformed"], [32, 21, 27, 21], [32, 24, 27, 24, "mapFn"], [32, 29, 27, 29], [32, 30, 27, 30, "previous"], [32, 38, 27, 38], [32, 40, 27, 40, "current"], [32, 47, 27, 47], [32, 48, 27, 48], [33, 6, 28, 4, "previousArr"], [33, 17, 28, 15], [33, 18, 28, 16, "push"], [33, 22, 28, 20], [33, 23, 28, 21, "transformed"], [33, 34, 28, 32], [33, 35, 28, 33], [34, 6, 29, 4, "transformedArr"], [34, 20, 29, 18], [34, 21, 29, 19, "push"], [34, 25, 29, 23], [34, 26, 29, 24, "transformed"], [34, 37, 29, 35], [34, 38, 29, 36], [35, 4, 30, 2], [35, 5, 30, 3], [35, 6, 30, 4], [36, 4, 31, 2], [36, 11, 31, 9, "transformedArr"], [36, 25, 31, 23], [37, 2, 32, 0], [38, 2, 34, 7], [38, 11, 34, 16, "hasProperty"], [38, 22, 34, 27, "hasProperty"], [38, 23, 34, 28, "object"], [38, 29, 34, 42], [38, 31, 34, 44, "key"], [38, 34, 34, 55], [38, 36, 34, 57], [39, 4, 35, 2], [39, 11, 35, 9, "Object"], [39, 17, 35, 15], [39, 18, 35, 16, "prototype"], [39, 27, 35, 25], [39, 28, 35, 26, "hasOwnProperty"], [39, 42, 35, 40], [39, 43, 35, 41, "call"], [39, 47, 35, 45], [39, 48, 35, 46, "object"], [39, 54, 35, 52], [39, 56, 35, 54, "key"], [39, 59, 35, 57], [39, 60, 35, 58], [40, 2, 36, 0], [41, 2, 38, 7], [41, 11, 38, 16, "isTestEnv"], [41, 20, 38, 25, "isTestEnv"], [41, 21, 38, 25], [41, 23, 38, 37], [42, 4, 39, 2], [43, 4, 40, 2], [43, 11, 40, 9, "hasProperty"], [43, 22, 40, 20], [43, 23, 40, 21, "global"], [43, 29, 40, 27], [43, 31, 40, 29], [43, 40, 40, 38], [43, 41, 40, 39], [43, 45, 40, 43, "process"], [43, 52, 40, 50], [43, 53, 40, 51, "env"], [43, 56, 40, 54], [43, 57, 40, 55, "NODE_ENV"], [43, 65, 40, 63], [43, 70, 40, 68], [43, 76, 40, 74], [44, 2, 41, 0], [45, 2, 43, 7], [45, 11, 43, 16, "tagMessage"], [45, 21, 43, 26, "tagMessage"], [45, 22, 43, 27, "msg"], [45, 25, 43, 38], [45, 27, 43, 40], [46, 4, 44, 2], [46, 11, 44, 9], [46, 45, 44, 43, "msg"], [46, 48, 44, 46], [46, 50, 44, 48], [47, 2, 45, 0], [49, 2, 47, 0], [50, 2, 48, 0], [51, 2, 49, 7], [51, 11, 49, 16, "isF<PERSON><PERSON>"], [51, 19, 49, 24, "isF<PERSON><PERSON>"], [51, 20, 49, 24], [51, 22, 49, 36], [52, 4, 50, 2], [53, 4, 51, 2], [53, 11, 51, 9], [53, 12, 51, 10], [53, 13, 51, 11, "global"], [53, 19, 51, 17], [53, 21, 51, 19, "nativeFabricUIManager"], [53, 42, 51, 40], [54, 2, 52, 0], [55, 2, 54, 7], [55, 11, 54, 16, "isReact19"], [55, 20, 54, 25, "isReact19"], [55, 21, 54, 25], [55, 23, 54, 28], [56, 4, 55, 2], [56, 11, 55, 9, "React"], [56, 25, 55, 14], [56, 26, 55, 15, "version"], [56, 33, 55, 22], [56, 34, 55, 23, "startsWith"], [56, 44, 55, 33], [56, 45, 55, 34], [56, 50, 55, 39], [56, 51, 55, 40], [57, 2, 56, 0], [58, 2, 58, 7], [58, 11, 58, 16, "isRemoteDebuggingEnabled"], [58, 35, 58, 40, "isRemoteDebuggingEnabled"], [58, 36, 58, 40], [58, 38, 58, 52], [59, 4, 59, 2], [60, 4, 60, 2], [61, 4, 61, 2], [61, 8, 61, 8, "localGlobal"], [61, 19, 61, 19], [61, 22, 61, 22, "global"], [61, 28, 61, 35], [62, 4, 62, 2], [62, 11, 63, 4], [62, 12, 63, 5], [62, 13, 63, 6, "localGlobal"], [62, 24, 63, 17], [62, 25, 63, 18, "nativeCallSyncHook"], [62, 43, 63, 36], [62, 47, 63, 40], [62, 48, 63, 41], [62, 49, 63, 42, "localGlobal"], [62, 60, 63, 53], [62, 61, 63, 54, "__REMOTEDEV__"], [62, 74, 63, 67], [62, 79, 64, 4], [62, 80, 64, 5, "localGlobal"], [62, 91, 64, 16], [62, 92, 64, 17, "RN$Bridgeless"], [62, 105, 64, 30], [63, 2, 66, 0], [65, 2, 68, 0], [66, 0, 69, 0], [67, 0, 70, 0], [68, 0, 71, 0], [69, 0, 72, 0], [70, 0, 73, 0], [71, 0, 74, 0], [72, 0, 75, 0], [73, 0, 76, 0], [74, 2, 77, 7], [74, 11, 77, 16, "deepEqual"], [74, 20, 77, 25, "deepEqual"], [74, 21, 77, 26, "obj1"], [74, 25, 77, 35], [74, 27, 77, 37, "obj2"], [74, 31, 77, 46], [74, 33, 77, 48], [75, 4, 78, 2], [75, 8, 78, 6, "obj1"], [75, 12, 78, 10], [75, 17, 78, 15, "obj2"], [75, 21, 78, 19], [75, 23, 78, 21], [76, 6, 79, 4], [76, 13, 79, 11], [76, 17, 79, 15], [77, 4, 80, 2], [78, 4, 82, 2], [78, 8, 83, 4], [78, 15, 83, 11, "obj1"], [78, 19, 83, 15], [78, 24, 83, 20], [78, 32, 83, 28], [78, 36, 84, 4], [78, 43, 84, 11, "obj2"], [78, 47, 84, 15], [78, 52, 84, 20], [78, 60, 84, 28], [78, 64, 85, 4, "obj1"], [78, 68, 85, 8], [78, 73, 85, 13], [78, 77, 85, 17], [78, 81, 86, 4, "obj2"], [78, 85, 86, 8], [78, 90, 86, 13], [78, 94, 86, 17], [78, 96, 87, 4], [79, 6, 88, 4], [79, 13, 88, 11], [79, 18, 88, 16], [80, 4, 89, 2], [81, 4, 91, 2], [81, 8, 91, 8, "keys1"], [81, 13, 91, 13], [81, 16, 91, 16, "Object"], [81, 22, 91, 22], [81, 23, 91, 23, "keys"], [81, 27, 91, 27], [81, 28, 91, 28, "obj1"], [81, 32, 91, 32], [81, 33, 91, 33], [82, 4, 92, 2], [82, 8, 92, 8, "keys2"], [82, 13, 92, 13], [82, 16, 92, 16, "Object"], [82, 22, 92, 22], [82, 23, 92, 23, "keys"], [82, 27, 92, 27], [82, 28, 92, 28, "obj2"], [82, 32, 92, 32], [82, 33, 92, 33], [83, 4, 94, 2], [83, 8, 94, 6, "keys1"], [83, 13, 94, 11], [83, 14, 94, 12, "length"], [83, 20, 94, 18], [83, 25, 94, 23, "keys2"], [83, 30, 94, 28], [83, 31, 94, 29, "length"], [83, 37, 94, 35], [83, 39, 94, 37], [84, 6, 95, 4], [84, 13, 95, 11], [84, 18, 95, 16], [85, 4, 96, 2], [86, 4, 98, 2], [86, 9, 98, 7], [86, 13, 98, 13, "key"], [86, 16, 98, 16], [86, 20, 98, 20, "keys1"], [86, 25, 98, 25], [86, 27, 98, 27], [87, 6, 99, 4], [87, 10, 99, 8], [87, 11, 99, 9, "keys2"], [87, 16, 99, 14], [87, 17, 99, 15, "includes"], [87, 25, 99, 23], [87, 26, 99, 24, "key"], [87, 29, 99, 27], [87, 30, 99, 28], [87, 34, 99, 32], [87, 35, 99, 33, "deepEqual"], [87, 44, 99, 42], [87, 45, 99, 43, "obj1"], [87, 49, 99, 47], [87, 50, 99, 48, "key"], [87, 53, 99, 51], [87, 54, 99, 52], [87, 56, 99, 54, "obj2"], [87, 60, 99, 58], [87, 61, 99, 59, "key"], [87, 64, 99, 62], [87, 65, 99, 63], [87, 66, 99, 64], [87, 68, 99, 66], [88, 8, 100, 6], [88, 15, 100, 13], [88, 20, 100, 18], [89, 6, 101, 4], [90, 4, 102, 2], [91, 4, 104, 2], [91, 11, 104, 9], [91, 15, 104, 13], [92, 2, 105, 0], [93, 2, 107, 7], [93, 6, 107, 13, "INT32_MAX"], [93, 15, 107, 22], [93, 18, 107, 22, "exports"], [93, 25, 107, 22], [93, 26, 107, 22, "INT32_MAX"], [93, 35, 107, 22], [93, 38, 107, 25], [93, 39, 107, 26], [93, 43, 107, 30], [93, 45, 107, 32], [93, 48, 107, 35], [93, 49, 107, 36], [94, 0, 107, 37], [94, 3]], "functionMap": {"names": ["<global>", "toArray", "withPrevAndCurrent", "currentArr.forEach$argument_0", "hasProperty", "isTestEnv", "tagMessage", "isF<PERSON><PERSON>", "isReact19", "isRemoteDebuggingEnabled", "deepEqual"], "mappings": "AAA;OCE;CDM;OEM;qBCO;GDQ;CFE;OIE;CJE;OKE;CLG;OME;CNE;OOI;CPG;OQE;CRE;OSE;CTQ;OUW;CV4B"}}, "type": "js/module"}]}