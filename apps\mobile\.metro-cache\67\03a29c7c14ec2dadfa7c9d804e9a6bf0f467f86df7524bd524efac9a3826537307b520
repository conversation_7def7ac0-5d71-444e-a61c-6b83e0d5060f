{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var pick = function (obj) {\n    for (var _len = arguments.length, keys = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      keys[_key - 1] = arguments[_key];\n    }\n    return keys.flat().filter(key => Object.prototype.hasOwnProperty.call(obj, key)).reduce((acc, key) => {\n      acc[key] = obj[key];\n      return acc;\n    }, {});\n  };\n  var omit = function (obj) {\n    for (var _len2 = arguments.length, keysToOmit = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      keysToOmit[_key2 - 1] = arguments[_key2];\n    }\n    var keysToOmitSet = new Set(keysToOmit.flat());\n    return Object.getOwnPropertyNames(obj).filter(key => !keysToOmitSet.has(key)).reduce((acc, key) => {\n      acc[key] = obj[key];\n      return acc;\n    }, {});\n  };\n  module.exports = {\n    pick,\n    omit\n  };\n});", "lineCount": 25, "map": [[2, 2, 1, 0], [2, 6, 1, 6, "pick"], [2, 10, 1, 10], [2, 13, 1, 13], [2, 22, 1, 13, "pick"], [2, 23, 1, 14, "obj"], [2, 26, 1, 17], [3, 4, 1, 17], [3, 13, 1, 17, "_len"], [3, 17, 1, 17], [3, 20, 1, 17, "arguments"], [3, 29, 1, 17], [3, 30, 1, 17, "length"], [3, 36, 1, 17], [3, 38, 1, 22, "keys"], [3, 42, 1, 26], [3, 49, 1, 26, "Array"], [3, 54, 1, 26], [3, 55, 1, 26, "_len"], [3, 59, 1, 26], [3, 66, 1, 26, "_len"], [3, 70, 1, 26], [3, 81, 1, 26, "_key"], [3, 85, 1, 26], [3, 91, 1, 26, "_key"], [3, 95, 1, 26], [3, 98, 1, 26, "_len"], [3, 102, 1, 26], [3, 104, 1, 26, "_key"], [3, 108, 1, 26], [4, 6, 1, 22, "keys"], [4, 10, 1, 26], [4, 11, 1, 26, "_key"], [4, 15, 1, 26], [4, 23, 1, 26, "arguments"], [4, 32, 1, 26], [4, 33, 1, 26, "_key"], [4, 37, 1, 26], [5, 4, 1, 26], [6, 4, 1, 26], [6, 11, 2, 2, "keys"], [6, 15, 2, 6], [6, 16, 3, 5, "flat"], [6, 20, 3, 9], [6, 21, 3, 10], [6, 22, 3, 11], [6, 23, 4, 5, "filter"], [6, 29, 4, 11], [6, 30, 4, 12, "key"], [6, 33, 4, 15], [6, 37, 4, 19, "Object"], [6, 43, 4, 25], [6, 44, 4, 26, "prototype"], [6, 53, 4, 35], [6, 54, 4, 36, "hasOwnProperty"], [6, 68, 4, 50], [6, 69, 4, 51, "call"], [6, 73, 4, 55], [6, 74, 4, 56, "obj"], [6, 77, 4, 59], [6, 79, 4, 61, "key"], [6, 82, 4, 64], [6, 83, 4, 65], [6, 84, 4, 66], [6, 85, 5, 5, "reduce"], [6, 91, 5, 11], [6, 92, 5, 12], [6, 93, 5, 13, "acc"], [6, 96, 5, 16], [6, 98, 5, 18, "key"], [6, 101, 5, 21], [6, 106, 5, 26], [7, 6, 6, 6, "acc"], [7, 9, 6, 9], [7, 10, 6, 10, "key"], [7, 13, 6, 13], [7, 14, 6, 14], [7, 17, 6, 17, "obj"], [7, 20, 6, 20], [7, 21, 6, 21, "key"], [7, 24, 6, 24], [7, 25, 6, 25], [8, 6, 7, 6], [8, 13, 7, 13, "acc"], [8, 16, 7, 16], [9, 4, 8, 4], [9, 5, 8, 5], [9, 7, 8, 7], [9, 8, 8, 8], [9, 9, 8, 9], [9, 10, 8, 10], [10, 2, 8, 10], [11, 2, 10, 0], [11, 6, 10, 6, "omit"], [11, 10, 10, 10], [11, 13, 10, 13], [11, 22, 10, 13, "omit"], [11, 23, 10, 14, "obj"], [11, 26, 10, 17], [11, 28, 10, 37], [12, 4, 10, 37], [12, 13, 10, 37, "_len2"], [12, 18, 10, 37], [12, 21, 10, 37, "arguments"], [12, 30, 10, 37], [12, 31, 10, 37, "length"], [12, 37, 10, 37], [12, 39, 10, 22, "keysToOmit"], [12, 49, 10, 32], [12, 56, 10, 32, "Array"], [12, 61, 10, 32], [12, 62, 10, 32, "_len2"], [12, 67, 10, 32], [12, 74, 10, 32, "_len2"], [12, 79, 10, 32], [12, 90, 10, 32, "_key2"], [12, 95, 10, 32], [12, 101, 10, 32, "_key2"], [12, 106, 10, 32], [12, 109, 10, 32, "_len2"], [12, 114, 10, 32], [12, 116, 10, 32, "_key2"], [12, 121, 10, 32], [13, 6, 10, 22, "keysToOmit"], [13, 16, 10, 32], [13, 17, 10, 32, "_key2"], [13, 22, 10, 32], [13, 30, 10, 32, "arguments"], [13, 39, 10, 32], [13, 40, 10, 32, "_key2"], [13, 45, 10, 32], [14, 4, 10, 32], [15, 4, 11, 2], [15, 8, 11, 8, "keysToOmitSet"], [15, 21, 11, 21], [15, 24, 11, 24], [15, 28, 11, 28, "Set"], [15, 31, 11, 31], [15, 32, 11, 32, "keysToOmit"], [15, 42, 11, 42], [15, 43, 11, 43, "flat"], [15, 47, 11, 47], [15, 48, 11, 48], [15, 49, 11, 49], [15, 50, 11, 50], [16, 4, 12, 2], [16, 11, 12, 9, "Object"], [16, 17, 12, 15], [16, 18, 12, 16, "getOwnPropertyNames"], [16, 37, 12, 35], [16, 38, 12, 36, "obj"], [16, 41, 12, 39], [16, 42, 12, 40], [16, 43, 13, 5, "filter"], [16, 49, 13, 11], [16, 50, 13, 12, "key"], [16, 53, 13, 15], [16, 57, 13, 19], [16, 58, 13, 20, "keysToOmitSet"], [16, 71, 13, 33], [16, 72, 13, 34, "has"], [16, 75, 13, 37], [16, 76, 13, 38, "key"], [16, 79, 13, 41], [16, 80, 13, 42], [16, 81, 13, 43], [16, 82, 14, 5, "reduce"], [16, 88, 14, 11], [16, 89, 14, 12], [16, 90, 14, 13, "acc"], [16, 93, 14, 16], [16, 95, 14, 18, "key"], [16, 98, 14, 21], [16, 103, 14, 26], [17, 6, 15, 6, "acc"], [17, 9, 15, 9], [17, 10, 15, 10, "key"], [17, 13, 15, 13], [17, 14, 15, 14], [17, 17, 15, 17, "obj"], [17, 20, 15, 20], [17, 21, 15, 21, "key"], [17, 24, 15, 24], [17, 25, 15, 25], [18, 6, 16, 6], [18, 13, 16, 13, "acc"], [18, 16, 16, 16], [19, 4, 17, 4], [19, 5, 17, 5], [19, 7, 17, 7], [19, 8, 17, 8], [19, 9, 17, 9], [19, 10, 17, 10], [20, 2, 18, 0], [20, 3, 18, 1], [21, 2, 20, 0, "module"], [21, 8, 20, 6], [21, 9, 20, 7, "exports"], [21, 16, 20, 14], [21, 19, 20, 17], [22, 4, 20, 19, "pick"], [22, 8, 20, 23], [23, 4, 20, 25, "omit"], [24, 2, 20, 30], [24, 3, 20, 31], [25, 0, 20, 32], [25, 3]], "functionMap": {"names": ["<global>", "pick", "keys.flat.filter$argument_0", "keys.flat.filter.reduce$argument_0", "omit", "Object.getOwnPropertyNames.filter$argument_0", "Object.getOwnPropertyNames.filter.reduce$argument_0"], "mappings": "AAA,aC;YCG,qDD;YEC;KFG,KD;aIE;YCG,8BD;YEC;KFG;CJC"}}, "type": "js/module"}]}