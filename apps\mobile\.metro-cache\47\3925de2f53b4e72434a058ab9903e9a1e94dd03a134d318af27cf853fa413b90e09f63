{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../vendor/emitter/EventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 58}}], "key": "FSJ8dBSGMPKDnoV5nqp600Oqgzc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _EventEmitter = _interopRequireDefault(require(_dependencyMap[1], \"../vendor/emitter/EventEmitter\"));\n  var RawEventEmitter = new _EventEmitter.default();\n  var _default = exports.default = RawEventEmitter;\n});", "lineCount": 10, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_EventEmitter"], [7, 19, 13, 0], [7, 22, 13, 0, "_interopRequireDefault"], [7, 44, 13, 0], [7, 45, 13, 0, "require"], [7, 52, 13, 0], [7, 53, 13, 0, "_dependencyMap"], [7, 67, 13, 0], [8, 2, 27, 0], [8, 6, 27, 6, "RawEventEmitter"], [8, 21, 27, 57], [8, 24, 28, 2], [8, 28, 28, 6, "EventEmitter"], [8, 49, 28, 18], [8, 50, 28, 40], [8, 51, 28, 41], [9, 2, 28, 42], [9, 6, 28, 42, "_default"], [9, 14, 28, 42], [9, 17, 28, 42, "exports"], [9, 24, 28, 42], [9, 25, 28, 42, "default"], [9, 32, 28, 42], [9, 35, 39, 15, "RawEventEmitter"], [9, 50, 39, 30], [10, 0, 39, 30], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}