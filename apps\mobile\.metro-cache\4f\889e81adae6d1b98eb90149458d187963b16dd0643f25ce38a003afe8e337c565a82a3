{"dependencies": [], "output": [{"data": {"code": "__d(function(global, require, _importDefaultUnused, _importAllUnused, module, exports, _dependencyMapUnused) {\n  module.exports = {\n  \"archive\": 61696,\n  \"arrow-down\": 61697,\n  \"arrow-left\": 61698,\n  \"arrow-right\": 61699,\n  \"arrow-up\": 61700,\n  \"bell\": 61701,\n  \"calendar\": 61702,\n  \"camera\": 61703,\n  \"cart\": 61704,\n  \"chart\": 61705,\n  \"check\": 61706,\n  \"chevron-down\": 61707,\n  \"chevron-left\": 61708,\n  \"chevron-right\": 61709,\n  \"chevron-up\": 61710,\n  \"clock\": 61711,\n  \"close\": 61712,\n  \"close-o\": 61713,\n  \"comment\": 61714,\n  \"credit-card\": 61715,\n  \"envelope\": 61716,\n  \"exclamation\": 61717,\n  \"external-link\": 61718,\n  \"eye\": 61719,\n  \"gear\": 61720,\n  \"heart\": 61721,\n  \"image\": 61722,\n  \"like\": 61723,\n  \"link\": 61724,\n  \"location\": 61725,\n  \"lock\": 61726,\n  \"minus\": 61727,\n  \"navicon\": 61728,\n  \"paperclip\": 61729,\n  \"pencil\": 61730,\n  \"play\": 61731,\n  \"plus\": 61732,\n  \"pointer\": 61733,\n  \"question\": 61734,\n  \"redo\": 61735,\n  \"refresh\": 61736,\n  \"retweet\": 61737,\n  \"sc-facebook\": 61738,\n  \"sc-github\": 61739,\n  \"sc-google-plus\": 61740,\n  \"sc-instagram\": 61741,\n  \"sc-linkedin\": 61742,\n  \"sc-odnoklassniki\": 61743,\n  \"sc-pinterest\": 61744,\n  \"sc-skype\": 61745,\n  \"sc-soundcloud\": 61746,\n  \"sc-telegram\": 61747,\n  \"sc-tumblr\": 61748,\n  \"sc-twitter\": 61749,\n  \"sc-vimeo\": 61750,\n  \"sc-vk\": 61751,\n  \"sc-youtube\": 61752,\n  \"search\": 61753,\n  \"share-apple\": 61754,\n  \"share-google\": 61755,\n  \"spinner\": 61756,\n  \"spinner-2\": 61757,\n  \"spinner-3\": 61758,\n  \"star\": 61759,\n  \"tag\": 61760,\n  \"trash\": 61761,\n  \"trophy\": 61762,\n  \"undo\": 61763,\n  \"unlock\": 61764,\n  \"user\": 61765\n};\n});", "lineCount": 74, "map": [[74, 3]], "functionMap": null}, "type": "js/module"}]}