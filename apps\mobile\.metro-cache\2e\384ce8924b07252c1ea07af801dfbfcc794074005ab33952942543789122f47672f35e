{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./assets/back-icon.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 46, "index": 61}}], "key": "w6zL++t1C78TLyjGCC+MM2ga71A=", "exportNames": ["*"]}}, {"name": "./assets/back-icon-mask.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 62}, "end": {"line": 4, "column": 55, "index": 117}}], "key": "bWqzgBvO01BuxnDokNoTHCL7aZQ=", "exportNames": ["*"]}}, {"name": "./assets/clear-icon.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 118}, "end": {"line": 5, "column": 48, "index": 166}}], "key": "JuYNh04J3IxzOHqTe7Ysrz1+Lj4=", "exportNames": ["*"]}}, {"name": "./assets/close-icon.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 167}, "end": {"line": 6, "column": 48, "index": 215}}], "key": "MQDEUiNb2he1ZQsWdc2w3RrbPeI=", "exportNames": ["*"]}}, {"name": "./assets/search-icon.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 216}, "end": {"line": 7, "column": 50, "index": 266}}], "key": "ukc3pdGvpNYRRp/ybYWRfE/CBk8=", "exportNames": ["*"]}}, {"name": "./Background.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 267}, "end": {"line": 8, "column": 45, "index": 312}}], "key": "rS5c0BsCtRGU5EOTkhE8gBPdgTE=", "exportNames": ["*"]}}, {"name": "./Button.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 313}, "end": {"line": 9, "column": 37, "index": 350}}], "key": "3CNP3Wqq2CGANIOE73Nql6L4yHo=", "exportNames": ["*"]}}, {"name": "./getDefaultSidebarWidth.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 351}, "end": {"line": 10, "column": 69, "index": 420}}], "key": "cCqZCodoToCdNoiorEIBe1d6RDM=", "exportNames": ["*"]}}, {"name": "./Header/getDefaultHeaderHeight.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 421}, "end": {"line": 11, "column": 76, "index": 497}}], "key": "3odPzSL37tOWjaOt2BJGt37Bg6o=", "exportNames": ["*"]}}, {"name": "./Header/getHeaderTitle.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 498}, "end": {"line": 12, "column": 60, "index": 558}}], "key": "vQQjdX6ohuSpm1cVZzE/hH6syjU=", "exportNames": ["*"]}}, {"name": "./Header/Header.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 559}, "end": {"line": 13, "column": 44, "index": 603}}], "key": "QtqZjXgqW69l13QHw5Ot/4W8JCQ=", "exportNames": ["*"]}}, {"name": "./Header/HeaderBackButton.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 604}, "end": {"line": 14, "column": 64, "index": 668}}], "key": "jrnbKON8DvDfIIXNZgf56fMpnEY=", "exportNames": ["*"]}}, {"name": "./Header/HeaderBackContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 669}, "end": {"line": 15, "column": 66, "index": 735}}], "key": "Z4qe0ipg/+NPn7qJf8xAAzfyL38=", "exportNames": ["*"]}}, {"name": "./Header/HeaderBackground.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 736}, "end": {"line": 16, "column": 64, "index": 800}}], "key": "Td/l49P4bYQPPGmyWE4ZhHXa+oM=", "exportNames": ["*"]}}, {"name": "./Header/HeaderButton.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 801}, "end": {"line": 17, "column": 56, "index": 857}}], "key": "aefn2tr7NY3xLa8SeO/tL8gE+n8=", "exportNames": ["*"]}}, {"name": "./Header/HeaderHeightContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 858}, "end": {"line": 18, "column": 70, "index": 928}}], "key": "stZawU7KzLasMJlrjWF3s0um3fY=", "exportNames": ["*"]}}, {"name": "./Header/HeaderShownContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 929}, "end": {"line": 19, "column": 68, "index": 997}}], "key": "IveGqOWZUvFpozXTUMOpsU/p17I=", "exportNames": ["*"]}}, {"name": "./Header/HeaderTitle.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 998}, "end": {"line": 20, "column": 54, "index": 1052}}], "key": "xzXAj+Z670XXabTnNOzDr9KrppA=", "exportNames": ["*"]}}, {"name": "./Header/useHeaderHeight.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 1053}, "end": {"line": 21, "column": 62, "index": 1115}}], "key": "TCrybQh2ffNaIszijE20nVyHGpA=", "exportNames": ["*"]}}, {"name": "./Label/getLabel.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 1116}, "end": {"line": 22, "column": 47, "index": 1163}}], "key": "bPcW0HO8xvlWrWIR7LQfFMdqMxg=", "exportNames": ["*"]}}, {"name": "./Label/Label.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 1164}, "end": {"line": 23, "column": 41, "index": 1205}}], "key": "JTItHu4To5nn5KtZnG7ohpeYxgg=", "exportNames": ["*"]}}, {"name": "./MissingIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 1206}, "end": {"line": 24, "column": 47, "index": 1253}}], "key": "c6pQlRadD7Ou/tlBUoEVSTm1ymM=", "exportNames": ["*"]}}, {"name": "./PlatformPressable.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 1254}, "end": {"line": 25, "column": 59, "index": 1313}}], "key": "7Wm8S4t9JyY/16EtBirZwW7XtgQ=", "exportNames": ["*"]}}, {"name": "./ResourceSavingView.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 1314}, "end": {"line": 26, "column": 61, "index": 1375}}], "key": "THz5VuDCiQOjlaYOTav2KJ8VOXc=", "exportNames": ["*"]}}, {"name": "./SafeAreaProviderCompat.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0, "index": 1376}, "end": {"line": 27, "column": 69, "index": 1445}}], "key": "xDZVnJEu3KgR2kKvkYcGXQ8l4bM=", "exportNames": ["*"]}}, {"name": "./Screen.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 1446}, "end": {"line": 28, "column": 37, "index": 1483}}], "key": "YbGte4vf40k4Yjb9DRJvUiBpPUk=", "exportNames": ["*"]}}, {"name": "./Text.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 1484}, "end": {"line": 29, "column": 33, "index": 1517}}], "key": "QTnFfg9+sbvsvptKfI6RYkeAj2s=", "exportNames": ["*"]}}, {"name": "./useFrameSize.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 1518}, "end": {"line": 30, "column": 49, "index": 1567}}], "key": "dRzp9Mme73SbFUGqz80tDHJoVo0=", "exportNames": ["*"]}}, {"name": "./types.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 32, "column": 0, "index": 1650}, "end": {"line": 32, "column": 27, "index": 1677}}], "key": "yJvqu7zVoaSgx/LOxsKU/6eppkQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {\n    Assets: true,\n    Background: true,\n    Button: true,\n    getDefaultSidebarWidth: true,\n    getDefaultHeaderHeight: true,\n    getHeaderTitle: true,\n    Header: true,\n    HeaderBackButton: true,\n    HeaderBackContext: true,\n    HeaderBackground: true,\n    HeaderButton: true,\n    HeaderHeightContext: true,\n    HeaderShownContext: true,\n    HeaderTitle: true,\n    useHeaderHeight: true,\n    getLabel: true,\n    Label: true,\n    MissingIcon: true,\n    PlatformPressable: true,\n    ResourceSavingView: true,\n    SafeAreaProviderCompat: true,\n    Screen: true,\n    Text: true,\n    useFrameSize: true\n  };\n  exports.Assets = void 0;\n  Object.defineProperty(exports, \"Background\", {\n    enumerable: true,\n    get: function () {\n      return _Background.Background;\n    }\n  });\n  Object.defineProperty(exports, \"Button\", {\n    enumerable: true,\n    get: function () {\n      return _Button.Button;\n    }\n  });\n  Object.defineProperty(exports, \"Header\", {\n    enumerable: true,\n    get: function () {\n      return _Header.Header;\n    }\n  });\n  Object.defineProperty(exports, \"HeaderBackButton\", {\n    enumerable: true,\n    get: function () {\n      return _HeaderBackButton.HeaderBackButton;\n    }\n  });\n  Object.defineProperty(exports, \"HeaderBackContext\", {\n    enumerable: true,\n    get: function () {\n      return _HeaderBackContext.HeaderBackContext;\n    }\n  });\n  Object.defineProperty(exports, \"HeaderBackground\", {\n    enumerable: true,\n    get: function () {\n      return _HeaderBackground.HeaderBackground;\n    }\n  });\n  Object.defineProperty(exports, \"HeaderButton\", {\n    enumerable: true,\n    get: function () {\n      return _HeaderButton.HeaderButton;\n    }\n  });\n  Object.defineProperty(exports, \"HeaderHeightContext\", {\n    enumerable: true,\n    get: function () {\n      return _HeaderHeightContext.HeaderHeightContext;\n    }\n  });\n  Object.defineProperty(exports, \"HeaderShownContext\", {\n    enumerable: true,\n    get: function () {\n      return _HeaderShownContext.HeaderShownContext;\n    }\n  });\n  Object.defineProperty(exports, \"HeaderTitle\", {\n    enumerable: true,\n    get: function () {\n      return _HeaderTitle.HeaderTitle;\n    }\n  });\n  Object.defineProperty(exports, \"Label\", {\n    enumerable: true,\n    get: function () {\n      return _Label.Label;\n    }\n  });\n  Object.defineProperty(exports, \"MissingIcon\", {\n    enumerable: true,\n    get: function () {\n      return _MissingIcon.MissingIcon;\n    }\n  });\n  Object.defineProperty(exports, \"PlatformPressable\", {\n    enumerable: true,\n    get: function () {\n      return _PlatformPressable.PlatformPressable;\n    }\n  });\n  Object.defineProperty(exports, \"ResourceSavingView\", {\n    enumerable: true,\n    get: function () {\n      return _ResourceSavingView.ResourceSavingView;\n    }\n  });\n  Object.defineProperty(exports, \"SafeAreaProviderCompat\", {\n    enumerable: true,\n    get: function () {\n      return _SafeAreaProviderCompat.SafeAreaProviderCompat;\n    }\n  });\n  Object.defineProperty(exports, \"Screen\", {\n    enumerable: true,\n    get: function () {\n      return _Screen.Screen;\n    }\n  });\n  Object.defineProperty(exports, \"Text\", {\n    enumerable: true,\n    get: function () {\n      return _Text.Text;\n    }\n  });\n  Object.defineProperty(exports, \"getDefaultHeaderHeight\", {\n    enumerable: true,\n    get: function () {\n      return _getDefaultHeaderHeight.getDefaultHeaderHeight;\n    }\n  });\n  Object.defineProperty(exports, \"getDefaultSidebarWidth\", {\n    enumerable: true,\n    get: function () {\n      return _getDefaultSidebarWidth.getDefaultSidebarWidth;\n    }\n  });\n  Object.defineProperty(exports, \"getHeaderTitle\", {\n    enumerable: true,\n    get: function () {\n      return _getHeaderTitle.getHeaderTitle;\n    }\n  });\n  Object.defineProperty(exports, \"getLabel\", {\n    enumerable: true,\n    get: function () {\n      return _getLabel.getLabel;\n    }\n  });\n  Object.defineProperty(exports, \"useFrameSize\", {\n    enumerable: true,\n    get: function () {\n      return _useFrameSize.useFrameSize;\n    }\n  });\n  Object.defineProperty(exports, \"useHeaderHeight\", {\n    enumerable: true,\n    get: function () {\n      return _useHeaderHeight.useHeaderHeight;\n    }\n  });\n  var _backIcon = _interopRequireDefault(require(_dependencyMap[1], \"./assets/back-icon.png\"));\n  var _backIconMask = _interopRequireDefault(require(_dependencyMap[2], \"./assets/back-icon-mask.png\"));\n  var _clearIcon = _interopRequireDefault(require(_dependencyMap[3], \"./assets/clear-icon.png\"));\n  var _closeIcon = _interopRequireDefault(require(_dependencyMap[4], \"./assets/close-icon.png\"));\n  var _searchIcon = _interopRequireDefault(require(_dependencyMap[5], \"./assets/search-icon.png\"));\n  var _Background = require(_dependencyMap[6], \"./Background.js\");\n  var _Button = require(_dependencyMap[7], \"./Button.js\");\n  var _getDefaultSidebarWidth = require(_dependencyMap[8], \"./getDefaultSidebarWidth.js\");\n  var _getDefaultHeaderHeight = require(_dependencyMap[9], \"./Header/getDefaultHeaderHeight.js\");\n  var _getHeaderTitle = require(_dependencyMap[10], \"./Header/getHeaderTitle.js\");\n  var _Header = require(_dependencyMap[11], \"./Header/Header.js\");\n  var _HeaderBackButton = require(_dependencyMap[12], \"./Header/HeaderBackButton.js\");\n  var _HeaderBackContext = require(_dependencyMap[13], \"./Header/HeaderBackContext.js\");\n  var _HeaderBackground = require(_dependencyMap[14], \"./Header/HeaderBackground.js\");\n  var _HeaderButton = require(_dependencyMap[15], \"./Header/HeaderButton.js\");\n  var _HeaderHeightContext = require(_dependencyMap[16], \"./Header/HeaderHeightContext.js\");\n  var _HeaderShownContext = require(_dependencyMap[17], \"./Header/HeaderShownContext.js\");\n  var _HeaderTitle = require(_dependencyMap[18], \"./Header/HeaderTitle.js\");\n  var _useHeaderHeight = require(_dependencyMap[19], \"./Header/useHeaderHeight.js\");\n  var _getLabel = require(_dependencyMap[20], \"./Label/getLabel.js\");\n  var _Label = require(_dependencyMap[21], \"./Label/Label.js\");\n  var _MissingIcon = require(_dependencyMap[22], \"./MissingIcon.js\");\n  var _PlatformPressable = require(_dependencyMap[23], \"./PlatformPressable.js\");\n  var _ResourceSavingView = require(_dependencyMap[24], \"./ResourceSavingView.js\");\n  var _SafeAreaProviderCompat = require(_dependencyMap[25], \"./SafeAreaProviderCompat.js\");\n  var _Screen = require(_dependencyMap[26], \"./Screen.js\");\n  var _Text = require(_dependencyMap[27], \"./Text.js\");\n  var _useFrameSize = require(_dependencyMap[28], \"./useFrameSize.js\");\n  var _types = require(_dependencyMap[29], \"./types.js\");\n  Object.keys(_types).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _types[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _types[key];\n      }\n    });\n  });\n  var Assets = exports.Assets = [_backIcon.default, _backIconMask.default, _searchIcon.default, _closeIcon.default, _clearIcon.default];\n});", "lineCount": 214, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13], [8, 6, 1, 13, "_exportNames"], [8, 18, 1, 13], [9, 4, 1, 13, "Assets"], [9, 10, 1, 13], [10, 4, 1, 13, "Background"], [10, 14, 1, 13], [11, 4, 1, 13, "<PERSON><PERSON>"], [11, 10, 1, 13], [12, 4, 1, 13, "getDefaultSidebarWidth"], [12, 26, 1, 13], [13, 4, 1, 13, "getDefaultHeaderHeight"], [13, 26, 1, 13], [14, 4, 1, 13, "getHeaderTitle"], [14, 18, 1, 13], [15, 4, 1, 13, "Header"], [15, 10, 1, 13], [16, 4, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 20, 1, 13], [17, 4, 1, 13, "HeaderBackContext"], [17, 21, 1, 13], [18, 4, 1, 13, "HeaderBackground"], [18, 20, 1, 13], [19, 4, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [19, 16, 1, 13], [20, 4, 1, 13, "HeaderHeightContext"], [20, 23, 1, 13], [21, 4, 1, 13, "HeaderShownContext"], [21, 22, 1, 13], [22, 4, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [22, 15, 1, 13], [23, 4, 1, 13, "useHeaderHeight"], [23, 19, 1, 13], [24, 4, 1, 13, "get<PERSON><PERSON><PERSON>"], [24, 12, 1, 13], [25, 4, 1, 13, "Label"], [25, 9, 1, 13], [26, 4, 1, 13, "MissingIcon"], [26, 15, 1, 13], [27, 4, 1, 13, "PlatformPressable"], [27, 21, 1, 13], [28, 4, 1, 13, "ResourceSavingView"], [28, 22, 1, 13], [29, 4, 1, 13, "SafeAreaProviderCompat"], [29, 26, 1, 13], [30, 4, 1, 13, "Screen"], [30, 10, 1, 13], [31, 4, 1, 13, "Text"], [31, 8, 1, 13], [32, 4, 1, 13, "useFrameSize"], [32, 16, 1, 13], [33, 2, 1, 13], [34, 2, 1, 13, "exports"], [34, 9, 1, 13], [34, 10, 1, 13, "Assets"], [34, 16, 1, 13], [35, 2, 1, 13, "Object"], [35, 8, 1, 13], [35, 9, 1, 13, "defineProperty"], [35, 23, 1, 13], [35, 24, 1, 13, "exports"], [35, 31, 1, 13], [36, 4, 1, 13, "enumerable"], [36, 14, 1, 13], [37, 4, 1, 13, "get"], [37, 7, 1, 13], [37, 18, 1, 13, "get"], [37, 19, 1, 13], [38, 6, 1, 13], [38, 13, 1, 13, "_Background"], [38, 24, 1, 13], [38, 25, 1, 13, "Background"], [38, 35, 1, 13], [39, 4, 1, 13], [40, 2, 1, 13], [41, 2, 1, 13, "Object"], [41, 8, 1, 13], [41, 9, 1, 13, "defineProperty"], [41, 23, 1, 13], [41, 24, 1, 13, "exports"], [41, 31, 1, 13], [42, 4, 1, 13, "enumerable"], [42, 14, 1, 13], [43, 4, 1, 13, "get"], [43, 7, 1, 13], [43, 18, 1, 13, "get"], [43, 19, 1, 13], [44, 6, 1, 13], [44, 13, 1, 13, "_<PERSON><PERSON>"], [44, 20, 1, 13], [44, 21, 1, 13, "<PERSON><PERSON>"], [44, 27, 1, 13], [45, 4, 1, 13], [46, 2, 1, 13], [47, 2, 1, 13, "Object"], [47, 8, 1, 13], [47, 9, 1, 13, "defineProperty"], [47, 23, 1, 13], [47, 24, 1, 13, "exports"], [47, 31, 1, 13], [48, 4, 1, 13, "enumerable"], [48, 14, 1, 13], [49, 4, 1, 13, "get"], [49, 7, 1, 13], [49, 18, 1, 13, "get"], [49, 19, 1, 13], [50, 6, 1, 13], [50, 13, 1, 13, "_Header"], [50, 20, 1, 13], [50, 21, 1, 13, "Header"], [50, 27, 1, 13], [51, 4, 1, 13], [52, 2, 1, 13], [53, 2, 1, 13, "Object"], [53, 8, 1, 13], [53, 9, 1, 13, "defineProperty"], [53, 23, 1, 13], [53, 24, 1, 13, "exports"], [53, 31, 1, 13], [54, 4, 1, 13, "enumerable"], [54, 14, 1, 13], [55, 4, 1, 13, "get"], [55, 7, 1, 13], [55, 18, 1, 13, "get"], [55, 19, 1, 13], [56, 6, 1, 13], [56, 13, 1, 13, "_HeaderBackButton"], [56, 30, 1, 13], [56, 31, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [56, 47, 1, 13], [57, 4, 1, 13], [58, 2, 1, 13], [59, 2, 1, 13, "Object"], [59, 8, 1, 13], [59, 9, 1, 13, "defineProperty"], [59, 23, 1, 13], [59, 24, 1, 13, "exports"], [59, 31, 1, 13], [60, 4, 1, 13, "enumerable"], [60, 14, 1, 13], [61, 4, 1, 13, "get"], [61, 7, 1, 13], [61, 18, 1, 13, "get"], [61, 19, 1, 13], [62, 6, 1, 13], [62, 13, 1, 13, "_HeaderBackContext"], [62, 31, 1, 13], [62, 32, 1, 13, "HeaderBackContext"], [62, 49, 1, 13], [63, 4, 1, 13], [64, 2, 1, 13], [65, 2, 1, 13, "Object"], [65, 8, 1, 13], [65, 9, 1, 13, "defineProperty"], [65, 23, 1, 13], [65, 24, 1, 13, "exports"], [65, 31, 1, 13], [66, 4, 1, 13, "enumerable"], [66, 14, 1, 13], [67, 4, 1, 13, "get"], [67, 7, 1, 13], [67, 18, 1, 13, "get"], [67, 19, 1, 13], [68, 6, 1, 13], [68, 13, 1, 13, "_HeaderBackground"], [68, 30, 1, 13], [68, 31, 1, 13, "HeaderBackground"], [68, 47, 1, 13], [69, 4, 1, 13], [70, 2, 1, 13], [71, 2, 1, 13, "Object"], [71, 8, 1, 13], [71, 9, 1, 13, "defineProperty"], [71, 23, 1, 13], [71, 24, 1, 13, "exports"], [71, 31, 1, 13], [72, 4, 1, 13, "enumerable"], [72, 14, 1, 13], [73, 4, 1, 13, "get"], [73, 7, 1, 13], [73, 18, 1, 13, "get"], [73, 19, 1, 13], [74, 6, 1, 13], [74, 13, 1, 13, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [74, 26, 1, 13], [74, 27, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [74, 39, 1, 13], [75, 4, 1, 13], [76, 2, 1, 13], [77, 2, 1, 13, "Object"], [77, 8, 1, 13], [77, 9, 1, 13, "defineProperty"], [77, 23, 1, 13], [77, 24, 1, 13, "exports"], [77, 31, 1, 13], [78, 4, 1, 13, "enumerable"], [78, 14, 1, 13], [79, 4, 1, 13, "get"], [79, 7, 1, 13], [79, 18, 1, 13, "get"], [79, 19, 1, 13], [80, 6, 1, 13], [80, 13, 1, 13, "_HeaderHeightContext"], [80, 33, 1, 13], [80, 34, 1, 13, "HeaderHeightContext"], [80, 53, 1, 13], [81, 4, 1, 13], [82, 2, 1, 13], [83, 2, 1, 13, "Object"], [83, 8, 1, 13], [83, 9, 1, 13, "defineProperty"], [83, 23, 1, 13], [83, 24, 1, 13, "exports"], [83, 31, 1, 13], [84, 4, 1, 13, "enumerable"], [84, 14, 1, 13], [85, 4, 1, 13, "get"], [85, 7, 1, 13], [85, 18, 1, 13, "get"], [85, 19, 1, 13], [86, 6, 1, 13], [86, 13, 1, 13, "_HeaderShownContext"], [86, 32, 1, 13], [86, 33, 1, 13, "HeaderShownContext"], [86, 51, 1, 13], [87, 4, 1, 13], [88, 2, 1, 13], [89, 2, 1, 13, "Object"], [89, 8, 1, 13], [89, 9, 1, 13, "defineProperty"], [89, 23, 1, 13], [89, 24, 1, 13, "exports"], [89, 31, 1, 13], [90, 4, 1, 13, "enumerable"], [90, 14, 1, 13], [91, 4, 1, 13, "get"], [91, 7, 1, 13], [91, 18, 1, 13, "get"], [91, 19, 1, 13], [92, 6, 1, 13], [92, 13, 1, 13, "_Header<PERSON>itle"], [92, 25, 1, 13], [92, 26, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [92, 37, 1, 13], [93, 4, 1, 13], [94, 2, 1, 13], [95, 2, 1, 13, "Object"], [95, 8, 1, 13], [95, 9, 1, 13, "defineProperty"], [95, 23, 1, 13], [95, 24, 1, 13, "exports"], [95, 31, 1, 13], [96, 4, 1, 13, "enumerable"], [96, 14, 1, 13], [97, 4, 1, 13, "get"], [97, 7, 1, 13], [97, 18, 1, 13, "get"], [97, 19, 1, 13], [98, 6, 1, 13], [98, 13, 1, 13, "_Label"], [98, 19, 1, 13], [98, 20, 1, 13, "Label"], [98, 25, 1, 13], [99, 4, 1, 13], [100, 2, 1, 13], [101, 2, 1, 13, "Object"], [101, 8, 1, 13], [101, 9, 1, 13, "defineProperty"], [101, 23, 1, 13], [101, 24, 1, 13, "exports"], [101, 31, 1, 13], [102, 4, 1, 13, "enumerable"], [102, 14, 1, 13], [103, 4, 1, 13, "get"], [103, 7, 1, 13], [103, 18, 1, 13, "get"], [103, 19, 1, 13], [104, 6, 1, 13], [104, 13, 1, 13, "_MissingIcon"], [104, 25, 1, 13], [104, 26, 1, 13, "MissingIcon"], [104, 37, 1, 13], [105, 4, 1, 13], [106, 2, 1, 13], [107, 2, 1, 13, "Object"], [107, 8, 1, 13], [107, 9, 1, 13, "defineProperty"], [107, 23, 1, 13], [107, 24, 1, 13, "exports"], [107, 31, 1, 13], [108, 4, 1, 13, "enumerable"], [108, 14, 1, 13], [109, 4, 1, 13, "get"], [109, 7, 1, 13], [109, 18, 1, 13, "get"], [109, 19, 1, 13], [110, 6, 1, 13], [110, 13, 1, 13, "_PlatformPressable"], [110, 31, 1, 13], [110, 32, 1, 13, "PlatformPressable"], [110, 49, 1, 13], [111, 4, 1, 13], [112, 2, 1, 13], [113, 2, 1, 13, "Object"], [113, 8, 1, 13], [113, 9, 1, 13, "defineProperty"], [113, 23, 1, 13], [113, 24, 1, 13, "exports"], [113, 31, 1, 13], [114, 4, 1, 13, "enumerable"], [114, 14, 1, 13], [115, 4, 1, 13, "get"], [115, 7, 1, 13], [115, 18, 1, 13, "get"], [115, 19, 1, 13], [116, 6, 1, 13], [116, 13, 1, 13, "_ResourceSavingView"], [116, 32, 1, 13], [116, 33, 1, 13, "ResourceSavingView"], [116, 51, 1, 13], [117, 4, 1, 13], [118, 2, 1, 13], [119, 2, 1, 13, "Object"], [119, 8, 1, 13], [119, 9, 1, 13, "defineProperty"], [119, 23, 1, 13], [119, 24, 1, 13, "exports"], [119, 31, 1, 13], [120, 4, 1, 13, "enumerable"], [120, 14, 1, 13], [121, 4, 1, 13, "get"], [121, 7, 1, 13], [121, 18, 1, 13, "get"], [121, 19, 1, 13], [122, 6, 1, 13], [122, 13, 1, 13, "_SafeAreaProviderCompat"], [122, 36, 1, 13], [122, 37, 1, 13, "SafeAreaProviderCompat"], [122, 59, 1, 13], [123, 4, 1, 13], [124, 2, 1, 13], [125, 2, 1, 13, "Object"], [125, 8, 1, 13], [125, 9, 1, 13, "defineProperty"], [125, 23, 1, 13], [125, 24, 1, 13, "exports"], [125, 31, 1, 13], [126, 4, 1, 13, "enumerable"], [126, 14, 1, 13], [127, 4, 1, 13, "get"], [127, 7, 1, 13], [127, 18, 1, 13, "get"], [127, 19, 1, 13], [128, 6, 1, 13], [128, 13, 1, 13, "_Screen"], [128, 20, 1, 13], [128, 21, 1, 13, "Screen"], [128, 27, 1, 13], [129, 4, 1, 13], [130, 2, 1, 13], [131, 2, 1, 13, "Object"], [131, 8, 1, 13], [131, 9, 1, 13, "defineProperty"], [131, 23, 1, 13], [131, 24, 1, 13, "exports"], [131, 31, 1, 13], [132, 4, 1, 13, "enumerable"], [132, 14, 1, 13], [133, 4, 1, 13, "get"], [133, 7, 1, 13], [133, 18, 1, 13, "get"], [133, 19, 1, 13], [134, 6, 1, 13], [134, 13, 1, 13, "_Text"], [134, 18, 1, 13], [134, 19, 1, 13, "Text"], [134, 23, 1, 13], [135, 4, 1, 13], [136, 2, 1, 13], [137, 2, 1, 13, "Object"], [137, 8, 1, 13], [137, 9, 1, 13, "defineProperty"], [137, 23, 1, 13], [137, 24, 1, 13, "exports"], [137, 31, 1, 13], [138, 4, 1, 13, "enumerable"], [138, 14, 1, 13], [139, 4, 1, 13, "get"], [139, 7, 1, 13], [139, 18, 1, 13, "get"], [139, 19, 1, 13], [140, 6, 1, 13], [140, 13, 1, 13, "_getDefaultHeaderHeight"], [140, 36, 1, 13], [140, 37, 1, 13, "getDefaultHeaderHeight"], [140, 59, 1, 13], [141, 4, 1, 13], [142, 2, 1, 13], [143, 2, 1, 13, "Object"], [143, 8, 1, 13], [143, 9, 1, 13, "defineProperty"], [143, 23, 1, 13], [143, 24, 1, 13, "exports"], [143, 31, 1, 13], [144, 4, 1, 13, "enumerable"], [144, 14, 1, 13], [145, 4, 1, 13, "get"], [145, 7, 1, 13], [145, 18, 1, 13, "get"], [145, 19, 1, 13], [146, 6, 1, 13], [146, 13, 1, 13, "_getDefaultSidebarWidth"], [146, 36, 1, 13], [146, 37, 1, 13, "getDefaultSidebarWidth"], [146, 59, 1, 13], [147, 4, 1, 13], [148, 2, 1, 13], [149, 2, 1, 13, "Object"], [149, 8, 1, 13], [149, 9, 1, 13, "defineProperty"], [149, 23, 1, 13], [149, 24, 1, 13, "exports"], [149, 31, 1, 13], [150, 4, 1, 13, "enumerable"], [150, 14, 1, 13], [151, 4, 1, 13, "get"], [151, 7, 1, 13], [151, 18, 1, 13, "get"], [151, 19, 1, 13], [152, 6, 1, 13], [152, 13, 1, 13, "_getHeaderTitle"], [152, 28, 1, 13], [152, 29, 1, 13, "getHeaderTitle"], [152, 43, 1, 13], [153, 4, 1, 13], [154, 2, 1, 13], [155, 2, 1, 13, "Object"], [155, 8, 1, 13], [155, 9, 1, 13, "defineProperty"], [155, 23, 1, 13], [155, 24, 1, 13, "exports"], [155, 31, 1, 13], [156, 4, 1, 13, "enumerable"], [156, 14, 1, 13], [157, 4, 1, 13, "get"], [157, 7, 1, 13], [157, 18, 1, 13, "get"], [157, 19, 1, 13], [158, 6, 1, 13], [158, 13, 1, 13, "_get<PERSON><PERSON>l"], [158, 22, 1, 13], [158, 23, 1, 13, "get<PERSON><PERSON><PERSON>"], [158, 31, 1, 13], [159, 4, 1, 13], [160, 2, 1, 13], [161, 2, 1, 13, "Object"], [161, 8, 1, 13], [161, 9, 1, 13, "defineProperty"], [161, 23, 1, 13], [161, 24, 1, 13, "exports"], [161, 31, 1, 13], [162, 4, 1, 13, "enumerable"], [162, 14, 1, 13], [163, 4, 1, 13, "get"], [163, 7, 1, 13], [163, 18, 1, 13, "get"], [163, 19, 1, 13], [164, 6, 1, 13], [164, 13, 1, 13, "_useFrameSize"], [164, 26, 1, 13], [164, 27, 1, 13, "useFrameSize"], [164, 39, 1, 13], [165, 4, 1, 13], [166, 2, 1, 13], [167, 2, 1, 13, "Object"], [167, 8, 1, 13], [167, 9, 1, 13, "defineProperty"], [167, 23, 1, 13], [167, 24, 1, 13, "exports"], [167, 31, 1, 13], [168, 4, 1, 13, "enumerable"], [168, 14, 1, 13], [169, 4, 1, 13, "get"], [169, 7, 1, 13], [169, 18, 1, 13, "get"], [169, 19, 1, 13], [170, 6, 1, 13], [170, 13, 1, 13, "_useHeaderHeight"], [170, 29, 1, 13], [170, 30, 1, 13, "useHeaderHeight"], [170, 45, 1, 13], [171, 4, 1, 13], [172, 2, 1, 13], [173, 2, 3, 0], [173, 6, 3, 0, "_backIcon"], [173, 15, 3, 0], [173, 18, 3, 0, "_interopRequireDefault"], [173, 40, 3, 0], [173, 41, 3, 0, "require"], [173, 48, 3, 0], [173, 49, 3, 0, "_dependencyMap"], [173, 63, 3, 0], [174, 2, 4, 0], [174, 6, 4, 0, "_backIconMask"], [174, 19, 4, 0], [174, 22, 4, 0, "_interopRequireDefault"], [174, 44, 4, 0], [174, 45, 4, 0, "require"], [174, 52, 4, 0], [174, 53, 4, 0, "_dependencyMap"], [174, 67, 4, 0], [175, 2, 5, 0], [175, 6, 5, 0, "_clearIcon"], [175, 16, 5, 0], [175, 19, 5, 0, "_interopRequireDefault"], [175, 41, 5, 0], [175, 42, 5, 0, "require"], [175, 49, 5, 0], [175, 50, 5, 0, "_dependencyMap"], [175, 64, 5, 0], [176, 2, 6, 0], [176, 6, 6, 0, "_closeIcon"], [176, 16, 6, 0], [176, 19, 6, 0, "_interopRequireDefault"], [176, 41, 6, 0], [176, 42, 6, 0, "require"], [176, 49, 6, 0], [176, 50, 6, 0, "_dependencyMap"], [176, 64, 6, 0], [177, 2, 7, 0], [177, 6, 7, 0, "_searchIcon"], [177, 17, 7, 0], [177, 20, 7, 0, "_interopRequireDefault"], [177, 42, 7, 0], [177, 43, 7, 0, "require"], [177, 50, 7, 0], [177, 51, 7, 0, "_dependencyMap"], [177, 65, 7, 0], [178, 2, 8, 0], [178, 6, 8, 0, "_Background"], [178, 17, 8, 0], [178, 20, 8, 0, "require"], [178, 27, 8, 0], [178, 28, 8, 0, "_dependencyMap"], [178, 42, 8, 0], [179, 2, 9, 0], [179, 6, 9, 0, "_<PERSON><PERSON>"], [179, 13, 9, 0], [179, 16, 9, 0, "require"], [179, 23, 9, 0], [179, 24, 9, 0, "_dependencyMap"], [179, 38, 9, 0], [180, 2, 10, 0], [180, 6, 10, 0, "_getDefaultSidebarWidth"], [180, 29, 10, 0], [180, 32, 10, 0, "require"], [180, 39, 10, 0], [180, 40, 10, 0, "_dependencyMap"], [180, 54, 10, 0], [181, 2, 11, 0], [181, 6, 11, 0, "_getDefaultHeaderHeight"], [181, 29, 11, 0], [181, 32, 11, 0, "require"], [181, 39, 11, 0], [181, 40, 11, 0, "_dependencyMap"], [181, 54, 11, 0], [182, 2, 12, 0], [182, 6, 12, 0, "_getHeaderTitle"], [182, 21, 12, 0], [182, 24, 12, 0, "require"], [182, 31, 12, 0], [182, 32, 12, 0, "_dependencyMap"], [182, 46, 12, 0], [183, 2, 13, 0], [183, 6, 13, 0, "_Header"], [183, 13, 13, 0], [183, 16, 13, 0, "require"], [183, 23, 13, 0], [183, 24, 13, 0, "_dependencyMap"], [183, 38, 13, 0], [184, 2, 14, 0], [184, 6, 14, 0, "_HeaderBackButton"], [184, 23, 14, 0], [184, 26, 14, 0, "require"], [184, 33, 14, 0], [184, 34, 14, 0, "_dependencyMap"], [184, 48, 14, 0], [185, 2, 15, 0], [185, 6, 15, 0, "_HeaderBackContext"], [185, 24, 15, 0], [185, 27, 15, 0, "require"], [185, 34, 15, 0], [185, 35, 15, 0, "_dependencyMap"], [185, 49, 15, 0], [186, 2, 16, 0], [186, 6, 16, 0, "_HeaderBackground"], [186, 23, 16, 0], [186, 26, 16, 0, "require"], [186, 33, 16, 0], [186, 34, 16, 0, "_dependencyMap"], [186, 48, 16, 0], [187, 2, 17, 0], [187, 6, 17, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [187, 19, 17, 0], [187, 22, 17, 0, "require"], [187, 29, 17, 0], [187, 30, 17, 0, "_dependencyMap"], [187, 44, 17, 0], [188, 2, 18, 0], [188, 6, 18, 0, "_HeaderHeightContext"], [188, 26, 18, 0], [188, 29, 18, 0, "require"], [188, 36, 18, 0], [188, 37, 18, 0, "_dependencyMap"], [188, 51, 18, 0], [189, 2, 19, 0], [189, 6, 19, 0, "_HeaderShownContext"], [189, 25, 19, 0], [189, 28, 19, 0, "require"], [189, 35, 19, 0], [189, 36, 19, 0, "_dependencyMap"], [189, 50, 19, 0], [190, 2, 20, 0], [190, 6, 20, 0, "_Header<PERSON>itle"], [190, 18, 20, 0], [190, 21, 20, 0, "require"], [190, 28, 20, 0], [190, 29, 20, 0, "_dependencyMap"], [190, 43, 20, 0], [191, 2, 21, 0], [191, 6, 21, 0, "_useHeaderHeight"], [191, 22, 21, 0], [191, 25, 21, 0, "require"], [191, 32, 21, 0], [191, 33, 21, 0, "_dependencyMap"], [191, 47, 21, 0], [192, 2, 22, 0], [192, 6, 22, 0, "_get<PERSON><PERSON>l"], [192, 15, 22, 0], [192, 18, 22, 0, "require"], [192, 25, 22, 0], [192, 26, 22, 0, "_dependencyMap"], [192, 40, 22, 0], [193, 2, 23, 0], [193, 6, 23, 0, "_Label"], [193, 12, 23, 0], [193, 15, 23, 0, "require"], [193, 22, 23, 0], [193, 23, 23, 0, "_dependencyMap"], [193, 37, 23, 0], [194, 2, 24, 0], [194, 6, 24, 0, "_MissingIcon"], [194, 18, 24, 0], [194, 21, 24, 0, "require"], [194, 28, 24, 0], [194, 29, 24, 0, "_dependencyMap"], [194, 43, 24, 0], [195, 2, 25, 0], [195, 6, 25, 0, "_PlatformPressable"], [195, 24, 25, 0], [195, 27, 25, 0, "require"], [195, 34, 25, 0], [195, 35, 25, 0, "_dependencyMap"], [195, 49, 25, 0], [196, 2, 26, 0], [196, 6, 26, 0, "_ResourceSavingView"], [196, 25, 26, 0], [196, 28, 26, 0, "require"], [196, 35, 26, 0], [196, 36, 26, 0, "_dependencyMap"], [196, 50, 26, 0], [197, 2, 27, 0], [197, 6, 27, 0, "_SafeAreaProviderCompat"], [197, 29, 27, 0], [197, 32, 27, 0, "require"], [197, 39, 27, 0], [197, 40, 27, 0, "_dependencyMap"], [197, 54, 27, 0], [198, 2, 28, 0], [198, 6, 28, 0, "_Screen"], [198, 13, 28, 0], [198, 16, 28, 0, "require"], [198, 23, 28, 0], [198, 24, 28, 0, "_dependencyMap"], [198, 38, 28, 0], [199, 2, 29, 0], [199, 6, 29, 0, "_Text"], [199, 11, 29, 0], [199, 14, 29, 0, "require"], [199, 21, 29, 0], [199, 22, 29, 0, "_dependencyMap"], [199, 36, 29, 0], [200, 2, 30, 0], [200, 6, 30, 0, "_useFrameSize"], [200, 19, 30, 0], [200, 22, 30, 0, "require"], [200, 29, 30, 0], [200, 30, 30, 0, "_dependencyMap"], [200, 44, 30, 0], [201, 2, 32, 0], [201, 6, 32, 0, "_types"], [201, 12, 32, 0], [201, 15, 32, 0, "require"], [201, 22, 32, 0], [201, 23, 32, 0, "_dependencyMap"], [201, 37, 32, 0], [202, 2, 32, 0, "Object"], [202, 8, 32, 0], [202, 9, 32, 0, "keys"], [202, 13, 32, 0], [202, 14, 32, 0, "_types"], [202, 20, 32, 0], [202, 22, 32, 0, "for<PERSON>ach"], [202, 29, 32, 0], [202, 40, 32, 0, "key"], [202, 43, 32, 0], [203, 4, 32, 0], [203, 8, 32, 0, "key"], [203, 11, 32, 0], [203, 29, 32, 0, "key"], [203, 32, 32, 0], [204, 4, 32, 0], [204, 8, 32, 0, "Object"], [204, 14, 32, 0], [204, 15, 32, 0, "prototype"], [204, 24, 32, 0], [204, 25, 32, 0, "hasOwnProperty"], [204, 39, 32, 0], [204, 40, 32, 0, "call"], [204, 44, 32, 0], [204, 45, 32, 0, "_exportNames"], [204, 57, 32, 0], [204, 59, 32, 0, "key"], [204, 62, 32, 0], [205, 4, 32, 0], [205, 8, 32, 0, "key"], [205, 11, 32, 0], [205, 15, 32, 0, "exports"], [205, 22, 32, 0], [205, 26, 32, 0, "exports"], [205, 33, 32, 0], [205, 34, 32, 0, "key"], [205, 37, 32, 0], [205, 43, 32, 0, "_types"], [205, 49, 32, 0], [205, 50, 32, 0, "key"], [205, 53, 32, 0], [206, 4, 32, 0, "Object"], [206, 10, 32, 0], [206, 11, 32, 0, "defineProperty"], [206, 25, 32, 0], [206, 26, 32, 0, "exports"], [206, 33, 32, 0], [206, 35, 32, 0, "key"], [206, 38, 32, 0], [207, 6, 32, 0, "enumerable"], [207, 16, 32, 0], [208, 6, 32, 0, "get"], [208, 9, 32, 0], [208, 20, 32, 0, "get"], [208, 21, 32, 0], [209, 8, 32, 0], [209, 15, 32, 0, "_types"], [209, 21, 32, 0], [209, 22, 32, 0, "key"], [209, 25, 32, 0], [210, 6, 32, 0], [211, 4, 32, 0], [212, 2, 32, 0], [213, 2, 31, 7], [213, 6, 31, 13, "Assets"], [213, 12, 31, 19], [213, 15, 31, 19, "exports"], [213, 22, 31, 19], [213, 23, 31, 19, "Assets"], [213, 29, 31, 19], [213, 32, 31, 22], [213, 33, 31, 23, "backIcon"], [213, 50, 31, 31], [213, 52, 31, 33, "backIconMask"], [213, 73, 31, 45], [213, 75, 31, 47, "searchIcon"], [213, 94, 31, 57], [213, 96, 31, 59, "closeIcon"], [213, 114, 31, 68], [213, 116, 31, 70, "clearIcon"], [213, 134, 31, 79], [213, 135, 31, 80], [214, 0, 31, 81], [214, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}