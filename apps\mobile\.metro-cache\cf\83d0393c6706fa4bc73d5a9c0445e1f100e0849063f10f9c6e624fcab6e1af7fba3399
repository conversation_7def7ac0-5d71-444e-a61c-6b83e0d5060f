{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, '__esModule', {\n    value: true\n  });\n  exports.default = escapeHTML;\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  function escapeHTML(str) {\n    return str.replace(/</g, '&lt;').replace(/>/g, '&gt;');\n  }\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8, "default"], [7, 17, 6, 15], [7, 20, 6, 18, "escapeHTML"], [7, 30, 6, 28], [8, 2, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [11, 0, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [15, 2, 14, 0], [15, 11, 14, 9, "escapeHTML"], [15, 21, 14, 19, "escapeHTML"], [15, 22, 14, 20, "str"], [15, 25, 14, 23], [15, 27, 14, 25], [16, 4, 15, 2], [16, 11, 15, 9, "str"], [16, 14, 15, 12], [16, 15, 15, 13, "replace"], [16, 22, 15, 20], [16, 23, 15, 21], [16, 27, 15, 25], [16, 29, 15, 27], [16, 35, 15, 33], [16, 36, 15, 34], [16, 37, 15, 35, "replace"], [16, 44, 15, 42], [16, 45, 15, 43], [16, 49, 15, 47], [16, 51, 15, 49], [16, 57, 15, 55], [16, 58, 15, 56], [17, 2, 16, 0], [18, 0, 16, 1], [18, 3]], "functionMap": {"names": ["<global>", "escapeHTML"], "mappings": "AAA;ACa;CDE"}}, "type": "js/module"}]}