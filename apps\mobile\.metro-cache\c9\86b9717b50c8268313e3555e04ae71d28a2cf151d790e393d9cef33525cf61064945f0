{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./DeprecatedNavigationInChildContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 93, "index": 140}}], "key": "q52xm1L6GcwAHIDdgCYRmHsHIPQ=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 141}, "end": {"line": 5, "column": 73, "index": 214}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}, {"name": "./useOnPreventRemove.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 215}, "end": {"line": 6, "column": 82, "index": 297}}], "key": "PbhCaGdR39Jz0OkkS237FN1qkxU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useOnAction = useOnAction;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _DeprecatedNavigationInChildContext = require(_dependencyMap[1], \"./DeprecatedNavigationInChildContext.js\");\n  var _NavigationBuilderContext = require(_dependencyMap[2], \"./NavigationBuilderContext.js\");\n  var _useOnPreventRemove = require(_dependencyMap[3], \"./useOnPreventRemove.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Hook to handle actions for a navigator, including state updates and bubbling.\n   *\n   * Bubbling an action is achieved in 2 ways:\n   * 1. To bubble action to parent, we expose the action handler in context and then access the parent context\n   * 2. To bubble action to child, child adds event listeners subscribing to actions from parent\n   *\n   * When the action handler handles as action, it returns `true`, otherwise `false`.\n   */\n  function useOnAction(_ref) {\n    var router = _ref.router,\n      getState = _ref.getState,\n      setState = _ref.setState,\n      key = _ref.key,\n      actionListeners = _ref.actionListeners,\n      beforeRemoveListeners = _ref.beforeRemoveListeners,\n      routerConfigOptions = _ref.routerConfigOptions,\n      emitter = _ref.emitter;\n    var _React$useContext = React.useContext(_NavigationBuilderContext.NavigationBuilderContext),\n      onActionParent = _React$useContext.onAction,\n      onRouteFocusParent = _React$useContext.onRouteFocus,\n      addListenerParent = _React$useContext.addListener,\n      onDispatchAction = _React$useContext.onDispatchAction;\n    var navigationInChildEnabled = React.useContext(_DeprecatedNavigationInChildContext.DeprecatedNavigationInChildContext);\n    var routerConfigOptionsRef = React.useRef(routerConfigOptions);\n    React.useEffect(() => {\n      routerConfigOptionsRef.current = routerConfigOptions;\n    });\n    var onAction = React.useCallback(function (action) {\n      var visitedNavigators = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : new Set();\n      var state = getState();\n\n      // Since actions can bubble both up and down, they could come to the same navigator again\n      // We keep track of navigators which have already tried to handle the action and return if it's already visited\n      if (visitedNavigators.has(state.key)) {\n        return false;\n      }\n      visitedNavigators.add(state.key);\n      if (typeof action.target !== 'string' || action.target === state.key) {\n        var result = router.getStateForAction(state, action, routerConfigOptionsRef.current);\n\n        // If a target is specified and set to current navigator, the action shouldn't bubble\n        // So instead of `null`, we use the state object for such cases to signal that action was handled\n        result = result === null && action.target === state.key ? state : result;\n        if (result !== null) {\n          onDispatchAction(action, state === result);\n          if (state !== result) {\n            var isPrevented = (0, _useOnPreventRemove.shouldPreventRemove)(emitter, beforeRemoveListeners, state.routes, result.routes, action);\n            if (isPrevented) {\n              return true;\n            }\n            setState(result);\n          }\n          if (onRouteFocusParent !== undefined) {\n            // Some actions such as `NAVIGATE` also want to bring the navigated route to focus in the whole tree\n            // This means we need to focus all of the parent navigators of this navigator as well\n            var shouldFocus = router.shouldActionChangeFocus(action);\n            if (shouldFocus && key !== undefined) {\n              onRouteFocusParent(key);\n            }\n          }\n          return true;\n        }\n      }\n      if (onActionParent !== undefined) {\n        // Bubble action to the parent if the current navigator didn't handle it\n        if (onActionParent(action, visitedNavigators)) {\n          return true;\n        }\n      }\n      if (typeof action.target === 'string' ||\n      // For backward compatibility\n      action.type === 'NAVIGATE_DEPRECATED' || navigationInChildEnabled) {\n        // If the action wasn't handled by current navigator or a parent navigator, let children handle it\n        // Handling this when target isn't specified is deprecated and will be removed in the future\n        for (var i = actionListeners.length - 1; i >= 0; i--) {\n          var listener = actionListeners[i];\n          if (listener(action, visitedNavigators)) {\n            return true;\n          }\n        }\n      }\n      return false;\n    }, [actionListeners, beforeRemoveListeners, emitter, getState, navigationInChildEnabled, key, onActionParent, onDispatchAction, onRouteFocusParent, router, setState]);\n    (0, _useOnPreventRemove.useOnPreventRemove)({\n      getState,\n      emitter,\n      beforeRemoveListeners\n    });\n    React.useEffect(() => addListenerParent?.('action', onAction), [addListenerParent, onAction]);\n    return onAction;\n  }\n});", "lineCount": 105, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useOnAction"], [7, 21, 1, 13], [7, 24, 1, 13, "useOnAction"], [7, 35, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_DeprecatedNavigationInChildContext"], [9, 41, 4, 0], [9, 44, 4, 0, "require"], [9, 51, 4, 0], [9, 52, 4, 0, "_dependencyMap"], [9, 66, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_NavigationBuilderContext"], [10, 31, 5, 0], [10, 34, 5, 0, "require"], [10, 41, 5, 0], [10, 42, 5, 0, "_dependencyMap"], [10, 56, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_useOnPreventRemove"], [11, 25, 6, 0], [11, 28, 6, 0, "require"], [11, 35, 6, 0], [11, 36, 6, 0, "_dependencyMap"], [11, 50, 6, 0], [12, 2, 6, 82], [12, 11, 6, 82, "_interopRequireWildcard"], [12, 35, 6, 82, "e"], [12, 36, 6, 82], [12, 38, 6, 82, "t"], [12, 39, 6, 82], [12, 68, 6, 82, "WeakMap"], [12, 75, 6, 82], [12, 81, 6, 82, "r"], [12, 82, 6, 82], [12, 89, 6, 82, "WeakMap"], [12, 96, 6, 82], [12, 100, 6, 82, "n"], [12, 101, 6, 82], [12, 108, 6, 82, "WeakMap"], [12, 115, 6, 82], [12, 127, 6, 82, "_interopRequireWildcard"], [12, 150, 6, 82], [12, 162, 6, 82, "_interopRequireWildcard"], [12, 163, 6, 82, "e"], [12, 164, 6, 82], [12, 166, 6, 82, "t"], [12, 167, 6, 82], [12, 176, 6, 82, "t"], [12, 177, 6, 82], [12, 181, 6, 82, "e"], [12, 182, 6, 82], [12, 186, 6, 82, "e"], [12, 187, 6, 82], [12, 188, 6, 82, "__esModule"], [12, 198, 6, 82], [12, 207, 6, 82, "e"], [12, 208, 6, 82], [12, 214, 6, 82, "o"], [12, 215, 6, 82], [12, 217, 6, 82, "i"], [12, 218, 6, 82], [12, 220, 6, 82, "f"], [12, 221, 6, 82], [12, 226, 6, 82, "__proto__"], [12, 235, 6, 82], [12, 243, 6, 82, "default"], [12, 250, 6, 82], [12, 252, 6, 82, "e"], [12, 253, 6, 82], [12, 270, 6, 82, "e"], [12, 271, 6, 82], [12, 294, 6, 82, "e"], [12, 295, 6, 82], [12, 320, 6, 82, "e"], [12, 321, 6, 82], [12, 330, 6, 82, "f"], [12, 331, 6, 82], [12, 337, 6, 82, "o"], [12, 338, 6, 82], [12, 341, 6, 82, "t"], [12, 342, 6, 82], [12, 345, 6, 82, "n"], [12, 346, 6, 82], [12, 349, 6, 82, "r"], [12, 350, 6, 82], [12, 358, 6, 82, "o"], [12, 359, 6, 82], [12, 360, 6, 82, "has"], [12, 363, 6, 82], [12, 364, 6, 82, "e"], [12, 365, 6, 82], [12, 375, 6, 82, "o"], [12, 376, 6, 82], [12, 377, 6, 82, "get"], [12, 380, 6, 82], [12, 381, 6, 82, "e"], [12, 382, 6, 82], [12, 385, 6, 82, "o"], [12, 386, 6, 82], [12, 387, 6, 82, "set"], [12, 390, 6, 82], [12, 391, 6, 82, "e"], [12, 392, 6, 82], [12, 394, 6, 82, "f"], [12, 395, 6, 82], [12, 409, 6, 82, "_t"], [12, 411, 6, 82], [12, 415, 6, 82, "e"], [12, 416, 6, 82], [12, 432, 6, 82, "_t"], [12, 434, 6, 82], [12, 441, 6, 82, "hasOwnProperty"], [12, 455, 6, 82], [12, 456, 6, 82, "call"], [12, 460, 6, 82], [12, 461, 6, 82, "e"], [12, 462, 6, 82], [12, 464, 6, 82, "_t"], [12, 466, 6, 82], [12, 473, 6, 82, "i"], [12, 474, 6, 82], [12, 478, 6, 82, "o"], [12, 479, 6, 82], [12, 482, 6, 82, "Object"], [12, 488, 6, 82], [12, 489, 6, 82, "defineProperty"], [12, 503, 6, 82], [12, 508, 6, 82, "Object"], [12, 514, 6, 82], [12, 515, 6, 82, "getOwnPropertyDescriptor"], [12, 539, 6, 82], [12, 540, 6, 82, "e"], [12, 541, 6, 82], [12, 543, 6, 82, "_t"], [12, 545, 6, 82], [12, 552, 6, 82, "i"], [12, 553, 6, 82], [12, 554, 6, 82, "get"], [12, 557, 6, 82], [12, 561, 6, 82, "i"], [12, 562, 6, 82], [12, 563, 6, 82, "set"], [12, 566, 6, 82], [12, 570, 6, 82, "o"], [12, 571, 6, 82], [12, 572, 6, 82, "f"], [12, 573, 6, 82], [12, 575, 6, 82, "_t"], [12, 577, 6, 82], [12, 579, 6, 82, "i"], [12, 580, 6, 82], [12, 584, 6, 82, "f"], [12, 585, 6, 82], [12, 586, 6, 82, "_t"], [12, 588, 6, 82], [12, 592, 6, 82, "e"], [12, 593, 6, 82], [12, 594, 6, 82, "_t"], [12, 596, 6, 82], [12, 607, 6, 82, "f"], [12, 608, 6, 82], [12, 613, 6, 82, "e"], [12, 614, 6, 82], [12, 616, 6, 82, "t"], [12, 617, 6, 82], [13, 2, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 0, 10, 0], [17, 0, 11, 0], [18, 0, 12, 0], [19, 0, 13, 0], [20, 0, 14, 0], [21, 0, 15, 0], [22, 2, 16, 7], [22, 11, 16, 16, "useOnAction"], [22, 22, 16, 27, "useOnAction"], [22, 23, 16, 27, "_ref"], [22, 27, 16, 27], [22, 29, 25, 3], [23, 4, 25, 3], [23, 8, 17, 2, "router"], [23, 14, 17, 8], [23, 17, 17, 8, "_ref"], [23, 21, 17, 8], [23, 22, 17, 2, "router"], [23, 28, 17, 8], [24, 6, 18, 2, "getState"], [24, 14, 18, 10], [24, 17, 18, 10, "_ref"], [24, 21, 18, 10], [24, 22, 18, 2, "getState"], [24, 30, 18, 10], [25, 6, 19, 2, "setState"], [25, 14, 19, 10], [25, 17, 19, 10, "_ref"], [25, 21, 19, 10], [25, 22, 19, 2, "setState"], [25, 30, 19, 10], [26, 6, 20, 2, "key"], [26, 9, 20, 5], [26, 12, 20, 5, "_ref"], [26, 16, 20, 5], [26, 17, 20, 2, "key"], [26, 20, 20, 5], [27, 6, 21, 2, "actionListeners"], [27, 21, 21, 17], [27, 24, 21, 17, "_ref"], [27, 28, 21, 17], [27, 29, 21, 2, "actionListeners"], [27, 44, 21, 17], [28, 6, 22, 2, "beforeRemoveListeners"], [28, 27, 22, 23], [28, 30, 22, 23, "_ref"], [28, 34, 22, 23], [28, 35, 22, 2, "beforeRemoveListeners"], [28, 56, 22, 23], [29, 6, 23, 2, "routerConfigOptions"], [29, 25, 23, 21], [29, 28, 23, 21, "_ref"], [29, 32, 23, 21], [29, 33, 23, 2, "routerConfigOptions"], [29, 52, 23, 21], [30, 6, 24, 2, "emitter"], [30, 13, 24, 9], [30, 16, 24, 9, "_ref"], [30, 20, 24, 9], [30, 21, 24, 2, "emitter"], [30, 28, 24, 9], [31, 4, 26, 2], [31, 8, 26, 2, "_React$useContext"], [31, 25, 26, 2], [31, 28, 31, 6, "React"], [31, 33, 31, 11], [31, 34, 31, 12, "useContext"], [31, 44, 31, 22], [31, 45, 31, 23, "NavigationBuilderContext"], [31, 95, 31, 47], [31, 96, 31, 48], [32, 6, 27, 14, "onActionParent"], [32, 20, 27, 28], [32, 23, 27, 28, "_React$useContext"], [32, 40, 27, 28], [32, 41, 27, 4, "onAction"], [32, 49, 27, 12], [33, 6, 28, 18, "onRouteFocusParent"], [33, 24, 28, 36], [33, 27, 28, 36, "_React$useContext"], [33, 44, 28, 36], [33, 45, 28, 4, "onRouteFocus"], [33, 57, 28, 16], [34, 6, 29, 17, "addListenerParent"], [34, 23, 29, 34], [34, 26, 29, 34, "_React$useContext"], [34, 43, 29, 34], [34, 44, 29, 4, "addListener"], [34, 55, 29, 15], [35, 6, 30, 4, "onDispatchAction"], [35, 22, 30, 20], [35, 25, 30, 20, "_React$useContext"], [35, 42, 30, 20], [35, 43, 30, 4, "onDispatchAction"], [35, 59, 30, 20], [36, 4, 32, 2], [36, 8, 32, 8, "navigationInChildEnabled"], [36, 32, 32, 32], [36, 35, 32, 35, "React"], [36, 40, 32, 40], [36, 41, 32, 41, "useContext"], [36, 51, 32, 51], [36, 52, 32, 52, "DeprecatedNavigationInChildContext"], [36, 122, 32, 86], [36, 123, 32, 87], [37, 4, 33, 2], [37, 8, 33, 8, "routerConfigOptionsRef"], [37, 30, 33, 30], [37, 33, 33, 33, "React"], [37, 38, 33, 38], [37, 39, 33, 39, "useRef"], [37, 45, 33, 45], [37, 46, 33, 46, "routerConfigOptions"], [37, 65, 33, 65], [37, 66, 33, 66], [38, 4, 34, 2, "React"], [38, 9, 34, 7], [38, 10, 34, 8, "useEffect"], [38, 19, 34, 17], [38, 20, 34, 18], [38, 26, 34, 24], [39, 6, 35, 4, "routerConfigOptionsRef"], [39, 28, 35, 26], [39, 29, 35, 27, "current"], [39, 36, 35, 34], [39, 39, 35, 37, "routerConfigOptions"], [39, 58, 35, 56], [40, 4, 36, 2], [40, 5, 36, 3], [40, 6, 36, 4], [41, 4, 37, 2], [41, 8, 37, 8, "onAction"], [41, 16, 37, 16], [41, 19, 37, 19, "React"], [41, 24, 37, 24], [41, 25, 37, 25, "useCallback"], [41, 36, 37, 36], [41, 37, 37, 37], [41, 47, 37, 38, "action"], [41, 53, 37, 44], [41, 55, 37, 80], [42, 6, 37, 80], [42, 10, 37, 46, "visitedNavigators"], [42, 27, 37, 63], [42, 30, 37, 63, "arguments"], [42, 39, 37, 63], [42, 40, 37, 63, "length"], [42, 46, 37, 63], [42, 54, 37, 63, "arguments"], [42, 63, 37, 63], [42, 71, 37, 63, "undefined"], [42, 80, 37, 63], [42, 83, 37, 63, "arguments"], [42, 92, 37, 63], [42, 98, 37, 66], [42, 102, 37, 70, "Set"], [42, 105, 37, 73], [42, 106, 37, 74], [42, 107, 37, 75], [43, 6, 38, 4], [43, 10, 38, 10, "state"], [43, 15, 38, 15], [43, 18, 38, 18, "getState"], [43, 26, 38, 26], [43, 27, 38, 27], [43, 28, 38, 28], [45, 6, 40, 4], [46, 6, 41, 4], [47, 6, 42, 4], [47, 10, 42, 8, "visitedNavigators"], [47, 27, 42, 25], [47, 28, 42, 26, "has"], [47, 31, 42, 29], [47, 32, 42, 30, "state"], [47, 37, 42, 35], [47, 38, 42, 36, "key"], [47, 41, 42, 39], [47, 42, 42, 40], [47, 44, 42, 42], [48, 8, 43, 6], [48, 15, 43, 13], [48, 20, 43, 18], [49, 6, 44, 4], [50, 6, 45, 4, "visitedNavigators"], [50, 23, 45, 21], [50, 24, 45, 22, "add"], [50, 27, 45, 25], [50, 28, 45, 26, "state"], [50, 33, 45, 31], [50, 34, 45, 32, "key"], [50, 37, 45, 35], [50, 38, 45, 36], [51, 6, 46, 4], [51, 10, 46, 8], [51, 17, 46, 15, "action"], [51, 23, 46, 21], [51, 24, 46, 22, "target"], [51, 30, 46, 28], [51, 35, 46, 33], [51, 43, 46, 41], [51, 47, 46, 45, "action"], [51, 53, 46, 51], [51, 54, 46, 52, "target"], [51, 60, 46, 58], [51, 65, 46, 63, "state"], [51, 70, 46, 68], [51, 71, 46, 69, "key"], [51, 74, 46, 72], [51, 76, 46, 74], [52, 8, 47, 6], [52, 12, 47, 10, "result"], [52, 18, 47, 16], [52, 21, 47, 19, "router"], [52, 27, 47, 25], [52, 28, 47, 26, "getStateForAction"], [52, 45, 47, 43], [52, 46, 47, 44, "state"], [52, 51, 47, 49], [52, 53, 47, 51, "action"], [52, 59, 47, 57], [52, 61, 47, 59, "routerConfigOptionsRef"], [52, 83, 47, 81], [52, 84, 47, 82, "current"], [52, 91, 47, 89], [52, 92, 47, 90], [54, 8, 49, 6], [55, 8, 50, 6], [56, 8, 51, 6, "result"], [56, 14, 51, 12], [56, 17, 51, 15, "result"], [56, 23, 51, 21], [56, 28, 51, 26], [56, 32, 51, 30], [56, 36, 51, 34, "action"], [56, 42, 51, 40], [56, 43, 51, 41, "target"], [56, 49, 51, 47], [56, 54, 51, 52, "state"], [56, 59, 51, 57], [56, 60, 51, 58, "key"], [56, 63, 51, 61], [56, 66, 51, 64, "state"], [56, 71, 51, 69], [56, 74, 51, 72, "result"], [56, 80, 51, 78], [57, 8, 52, 6], [57, 12, 52, 10, "result"], [57, 18, 52, 16], [57, 23, 52, 21], [57, 27, 52, 25], [57, 29, 52, 27], [58, 10, 53, 8, "onDispatchAction"], [58, 26, 53, 24], [58, 27, 53, 25, "action"], [58, 33, 53, 31], [58, 35, 53, 33, "state"], [58, 40, 53, 38], [58, 45, 53, 43, "result"], [58, 51, 53, 49], [58, 52, 53, 50], [59, 10, 54, 8], [59, 14, 54, 12, "state"], [59, 19, 54, 17], [59, 24, 54, 22, "result"], [59, 30, 54, 28], [59, 32, 54, 30], [60, 12, 55, 10], [60, 16, 55, 16, "isPrevented"], [60, 27, 55, 27], [60, 30, 55, 30], [60, 34, 55, 30, "shouldPreventRemove"], [60, 73, 55, 49], [60, 75, 55, 50, "emitter"], [60, 82, 55, 57], [60, 84, 55, 59, "beforeRemoveListeners"], [60, 105, 55, 80], [60, 107, 55, 82, "state"], [60, 112, 55, 87], [60, 113, 55, 88, "routes"], [60, 119, 55, 94], [60, 121, 55, 96, "result"], [60, 127, 55, 102], [60, 128, 55, 103, "routes"], [60, 134, 55, 109], [60, 136, 55, 111, "action"], [60, 142, 55, 117], [60, 143, 55, 118], [61, 12, 56, 10], [61, 16, 56, 14, "isPrevented"], [61, 27, 56, 25], [61, 29, 56, 27], [62, 14, 57, 12], [62, 21, 57, 19], [62, 25, 57, 23], [63, 12, 58, 10], [64, 12, 59, 10, "setState"], [64, 20, 59, 18], [64, 21, 59, 19, "result"], [64, 27, 59, 25], [64, 28, 59, 26], [65, 10, 60, 8], [66, 10, 61, 8], [66, 14, 61, 12, "onRouteFocusParent"], [66, 32, 61, 30], [66, 37, 61, 35, "undefined"], [66, 46, 61, 44], [66, 48, 61, 46], [67, 12, 62, 10], [68, 12, 63, 10], [69, 12, 64, 10], [69, 16, 64, 16, "shouldFocus"], [69, 27, 64, 27], [69, 30, 64, 30, "router"], [69, 36, 64, 36], [69, 37, 64, 37, "shouldActionChangeFocus"], [69, 60, 64, 60], [69, 61, 64, 61, "action"], [69, 67, 64, 67], [69, 68, 64, 68], [70, 12, 65, 10], [70, 16, 65, 14, "shouldFocus"], [70, 27, 65, 25], [70, 31, 65, 29, "key"], [70, 34, 65, 32], [70, 39, 65, 37, "undefined"], [70, 48, 65, 46], [70, 50, 65, 48], [71, 14, 66, 12, "onRouteFocusParent"], [71, 32, 66, 30], [71, 33, 66, 31, "key"], [71, 36, 66, 34], [71, 37, 66, 35], [72, 12, 67, 10], [73, 10, 68, 8], [74, 10, 69, 8], [74, 17, 69, 15], [74, 21, 69, 19], [75, 8, 70, 6], [76, 6, 71, 4], [77, 6, 72, 4], [77, 10, 72, 8, "onActionParent"], [77, 24, 72, 22], [77, 29, 72, 27, "undefined"], [77, 38, 72, 36], [77, 40, 72, 38], [78, 8, 73, 6], [79, 8, 74, 6], [79, 12, 74, 10, "onActionParent"], [79, 26, 74, 24], [79, 27, 74, 25, "action"], [79, 33, 74, 31], [79, 35, 74, 33, "visitedNavigators"], [79, 52, 74, 50], [79, 53, 74, 51], [79, 55, 74, 53], [80, 10, 75, 8], [80, 17, 75, 15], [80, 21, 75, 19], [81, 8, 76, 6], [82, 6, 77, 4], [83, 6, 78, 4], [83, 10, 78, 8], [83, 17, 78, 15, "action"], [83, 23, 78, 21], [83, 24, 78, 22, "target"], [83, 30, 78, 28], [83, 35, 78, 33], [83, 43, 78, 41], [84, 6, 79, 4], [85, 6, 80, 4, "action"], [85, 12, 80, 10], [85, 13, 80, 11, "type"], [85, 17, 80, 15], [85, 22, 80, 20], [85, 43, 80, 41], [85, 47, 80, 45, "navigationInChildEnabled"], [85, 71, 80, 69], [85, 73, 80, 71], [86, 8, 81, 6], [87, 8, 82, 6], [88, 8, 83, 6], [88, 13, 83, 11], [88, 17, 83, 15, "i"], [88, 18, 83, 16], [88, 21, 83, 19, "actionListeners"], [88, 36, 83, 34], [88, 37, 83, 35, "length"], [88, 43, 83, 41], [88, 46, 83, 44], [88, 47, 83, 45], [88, 49, 83, 47, "i"], [88, 50, 83, 48], [88, 54, 83, 52], [88, 55, 83, 53], [88, 57, 83, 55, "i"], [88, 58, 83, 56], [88, 60, 83, 58], [88, 62, 83, 60], [89, 10, 84, 8], [89, 14, 84, 14, "listener"], [89, 22, 84, 22], [89, 25, 84, 25, "actionListeners"], [89, 40, 84, 40], [89, 41, 84, 41, "i"], [89, 42, 84, 42], [89, 43, 84, 43], [90, 10, 85, 8], [90, 14, 85, 12, "listener"], [90, 22, 85, 20], [90, 23, 85, 21, "action"], [90, 29, 85, 27], [90, 31, 85, 29, "visitedNavigators"], [90, 48, 85, 46], [90, 49, 85, 47], [90, 51, 85, 49], [91, 12, 86, 10], [91, 19, 86, 17], [91, 23, 86, 21], [92, 10, 87, 8], [93, 8, 88, 6], [94, 6, 89, 4], [95, 6, 90, 4], [95, 13, 90, 11], [95, 18, 90, 16], [96, 4, 91, 2], [96, 5, 91, 3], [96, 7, 91, 5], [96, 8, 91, 6, "actionListeners"], [96, 23, 91, 21], [96, 25, 91, 23, "beforeRemoveListeners"], [96, 46, 91, 44], [96, 48, 91, 46, "emitter"], [96, 55, 91, 53], [96, 57, 91, 55, "getState"], [96, 65, 91, 63], [96, 67, 91, 65, "navigationInChildEnabled"], [96, 91, 91, 89], [96, 93, 91, 91, "key"], [96, 96, 91, 94], [96, 98, 91, 96, "onActionParent"], [96, 112, 91, 110], [96, 114, 91, 112, "onDispatchAction"], [96, 130, 91, 128], [96, 132, 91, 130, "onRouteFocusParent"], [96, 150, 91, 148], [96, 152, 91, 150, "router"], [96, 158, 91, 156], [96, 160, 91, 158, "setState"], [96, 168, 91, 166], [96, 169, 91, 167], [96, 170, 91, 168], [97, 4, 92, 2], [97, 8, 92, 2, "useOnPreventRemove"], [97, 46, 92, 20], [97, 48, 92, 21], [98, 6, 93, 4, "getState"], [98, 14, 93, 12], [99, 6, 94, 4, "emitter"], [99, 13, 94, 11], [100, 6, 95, 4, "beforeRemoveListeners"], [101, 4, 96, 2], [101, 5, 96, 3], [101, 6, 96, 4], [102, 4, 97, 2, "React"], [102, 9, 97, 7], [102, 10, 97, 8, "useEffect"], [102, 19, 97, 17], [102, 20, 97, 18], [102, 26, 97, 24, "addListenerParent"], [102, 43, 97, 41], [102, 46, 97, 44], [102, 54, 97, 52], [102, 56, 97, 54, "onAction"], [102, 64, 97, 62], [102, 65, 97, 63], [102, 67, 97, 65], [102, 68, 97, 66, "addListenerParent"], [102, 85, 97, 83], [102, 87, 97, 85, "onAction"], [102, 95, 97, 93], [102, 96, 97, 94], [102, 97, 97, 95], [103, 4, 98, 2], [103, 11, 98, 9, "onAction"], [103, 19, 98, 17], [104, 2, 99, 0], [105, 0, 99, 1], [105, 3]], "functionMap": {"names": ["<global>", "useOnAction", "React.useEffect$argument_0", "onAction"], "mappings": "AAA;OCe;kBCkB;GDE;qCEC;GFsD;kBCM,6CD;CDE"}}, "type": "js/module"}]}