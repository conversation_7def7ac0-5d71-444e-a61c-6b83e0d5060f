{"dependencies": [{"name": "nanoid/non-secure", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 43, "index": 58}}], "key": "SN8WVal79eAEDQEpzmVqVAy5JJs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.BaseRouter = void 0;\n  var _nonSecure = require(_dependencyMap[0], \"nanoid/non-secure\");\n  /**\n   * Base router object that can be used when writing custom routers.\n   * This provides few helper methods to handle common actions such as `RESET`.\n   */\n  var BaseRouter = exports.BaseRouter = {\n    getStateForAction(state, action) {\n      switch (action.type) {\n        case 'SET_PARAMS':\n        case 'REPLACE_PARAMS':\n          {\n            var index = action.source ? state.routes.findIndex(r => r.key === action.source) : state.index;\n            if (index === -1) {\n              return null;\n            }\n            return {\n              ...state,\n              routes: state.routes.map((r, i) => i === index ? {\n                ...r,\n                params: action.type === 'REPLACE_PARAMS' ? action.payload.params : {\n                  ...r.params,\n                  ...action.payload.params\n                }\n              } : r)\n            };\n          }\n        case 'RESET':\n          {\n            var nextState = action.payload;\n            if (nextState.routes.length === 0 || nextState.routes.some(route => !state.routeNames.includes(route.name))) {\n              return null;\n            }\n            if (nextState.stale === false) {\n              if (state.routeNames.length !== nextState.routeNames.length || nextState.routeNames.some(name => !state.routeNames.includes(name))) {\n                return null;\n              }\n              return {\n                ...nextState,\n                routes: nextState.routes.map(route => route.key ? route : {\n                  ...route,\n                  key: `${route.name}-${(0, _nonSecure.nanoid)()}`\n                })\n              };\n            }\n            return nextState;\n          }\n        default:\n          return null;\n      }\n    },\n    shouldActionChangeFocus(action) {\n      return action.type === 'NAVIGATE' || action.type === 'NAVIGATE_DEPRECATED';\n    }\n  };\n});", "lineCount": 62, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "BaseRouter"], [7, 20, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_nonSecure"], [8, 16, 3, 0], [8, 19, 3, 0, "require"], [8, 26, 3, 0], [8, 27, 3, 0, "_dependencyMap"], [8, 41, 3, 0], [9, 2, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 2, 8, 7], [13, 6, 8, 13, "BaseRouter"], [13, 16, 8, 23], [13, 19, 8, 23, "exports"], [13, 26, 8, 23], [13, 27, 8, 23, "BaseRouter"], [13, 37, 8, 23], [13, 40, 8, 26], [14, 4, 9, 2, "getStateForAction"], [14, 21, 9, 19, "getStateForAction"], [14, 22, 9, 20, "state"], [14, 27, 9, 25], [14, 29, 9, 27, "action"], [14, 35, 9, 33], [14, 37, 9, 35], [15, 6, 10, 4], [15, 14, 10, 12, "action"], [15, 20, 10, 18], [15, 21, 10, 19, "type"], [15, 25, 10, 23], [16, 8, 11, 6], [16, 13, 11, 11], [16, 25, 11, 23], [17, 8, 12, 6], [17, 13, 12, 11], [17, 29, 12, 27], [18, 10, 13, 8], [19, 12, 14, 10], [19, 16, 14, 16, "index"], [19, 21, 14, 21], [19, 24, 14, 24, "action"], [19, 30, 14, 30], [19, 31, 14, 31, "source"], [19, 37, 14, 37], [19, 40, 14, 40, "state"], [19, 45, 14, 45], [19, 46, 14, 46, "routes"], [19, 52, 14, 52], [19, 53, 14, 53, "findIndex"], [19, 62, 14, 62], [19, 63, 14, 63, "r"], [19, 64, 14, 64], [19, 68, 14, 68, "r"], [19, 69, 14, 69], [19, 70, 14, 70, "key"], [19, 73, 14, 73], [19, 78, 14, 78, "action"], [19, 84, 14, 84], [19, 85, 14, 85, "source"], [19, 91, 14, 91], [19, 92, 14, 92], [19, 95, 14, 95, "state"], [19, 100, 14, 100], [19, 101, 14, 101, "index"], [19, 106, 14, 106], [20, 12, 15, 10], [20, 16, 15, 14, "index"], [20, 21, 15, 19], [20, 26, 15, 24], [20, 27, 15, 25], [20, 28, 15, 26], [20, 30, 15, 28], [21, 14, 16, 12], [21, 21, 16, 19], [21, 25, 16, 23], [22, 12, 17, 10], [23, 12, 18, 10], [23, 19, 18, 17], [24, 14, 19, 12], [24, 17, 19, 15, "state"], [24, 22, 19, 20], [25, 14, 20, 12, "routes"], [25, 20, 20, 18], [25, 22, 20, 20, "state"], [25, 27, 20, 25], [25, 28, 20, 26, "routes"], [25, 34, 20, 32], [25, 35, 20, 33, "map"], [25, 38, 20, 36], [25, 39, 20, 37], [25, 40, 20, 38, "r"], [25, 41, 20, 39], [25, 43, 20, 41, "i"], [25, 44, 20, 42], [25, 49, 20, 47, "i"], [25, 50, 20, 48], [25, 55, 20, 53, "index"], [25, 60, 20, 58], [25, 63, 20, 61], [26, 16, 21, 14], [26, 19, 21, 17, "r"], [26, 20, 21, 18], [27, 16, 22, 14, "params"], [27, 22, 22, 20], [27, 24, 22, 22, "action"], [27, 30, 22, 28], [27, 31, 22, 29, "type"], [27, 35, 22, 33], [27, 40, 22, 38], [27, 56, 22, 54], [27, 59, 22, 57, "action"], [27, 65, 22, 63], [27, 66, 22, 64, "payload"], [27, 73, 22, 71], [27, 74, 22, 72, "params"], [27, 80, 22, 78], [27, 83, 22, 81], [28, 18, 23, 16], [28, 21, 23, 19, "r"], [28, 22, 23, 20], [28, 23, 23, 21, "params"], [28, 29, 23, 27], [29, 18, 24, 16], [29, 21, 24, 19, "action"], [29, 27, 24, 25], [29, 28, 24, 26, "payload"], [29, 35, 24, 33], [29, 36, 24, 34, "params"], [30, 16, 25, 14], [31, 14, 26, 12], [31, 15, 26, 13], [31, 18, 26, 16, "r"], [31, 19, 26, 17], [32, 12, 27, 10], [32, 13, 27, 11], [33, 10, 28, 8], [34, 8, 29, 6], [34, 13, 29, 11], [34, 20, 29, 18], [35, 10, 30, 8], [36, 12, 31, 10], [36, 16, 31, 16, "nextState"], [36, 25, 31, 25], [36, 28, 31, 28, "action"], [36, 34, 31, 34], [36, 35, 31, 35, "payload"], [36, 42, 31, 42], [37, 12, 32, 10], [37, 16, 32, 14, "nextState"], [37, 25, 32, 23], [37, 26, 32, 24, "routes"], [37, 32, 32, 30], [37, 33, 32, 31, "length"], [37, 39, 32, 37], [37, 44, 32, 42], [37, 45, 32, 43], [37, 49, 32, 47, "nextState"], [37, 58, 32, 56], [37, 59, 32, 57, "routes"], [37, 65, 32, 63], [37, 66, 32, 64, "some"], [37, 70, 32, 68], [37, 71, 32, 69, "route"], [37, 76, 32, 74], [37, 80, 32, 78], [37, 81, 32, 79, "state"], [37, 86, 32, 84], [37, 87, 32, 85, "routeNames"], [37, 97, 32, 95], [37, 98, 32, 96, "includes"], [37, 106, 32, 104], [37, 107, 32, 105, "route"], [37, 112, 32, 110], [37, 113, 32, 111, "name"], [37, 117, 32, 115], [37, 118, 32, 116], [37, 119, 32, 117], [37, 121, 32, 119], [38, 14, 33, 12], [38, 21, 33, 19], [38, 25, 33, 23], [39, 12, 34, 10], [40, 12, 35, 10], [40, 16, 35, 14, "nextState"], [40, 25, 35, 23], [40, 26, 35, 24, "stale"], [40, 31, 35, 29], [40, 36, 35, 34], [40, 41, 35, 39], [40, 43, 35, 41], [41, 14, 36, 12], [41, 18, 36, 16, "state"], [41, 23, 36, 21], [41, 24, 36, 22, "routeNames"], [41, 34, 36, 32], [41, 35, 36, 33, "length"], [41, 41, 36, 39], [41, 46, 36, 44, "nextState"], [41, 55, 36, 53], [41, 56, 36, 54, "routeNames"], [41, 66, 36, 64], [41, 67, 36, 65, "length"], [41, 73, 36, 71], [41, 77, 36, 75, "nextState"], [41, 86, 36, 84], [41, 87, 36, 85, "routeNames"], [41, 97, 36, 95], [41, 98, 36, 96, "some"], [41, 102, 36, 100], [41, 103, 36, 101, "name"], [41, 107, 36, 105], [41, 111, 36, 109], [41, 112, 36, 110, "state"], [41, 117, 36, 115], [41, 118, 36, 116, "routeNames"], [41, 128, 36, 126], [41, 129, 36, 127, "includes"], [41, 137, 36, 135], [41, 138, 36, 136, "name"], [41, 142, 36, 140], [41, 143, 36, 141], [41, 144, 36, 142], [41, 146, 36, 144], [42, 16, 37, 14], [42, 23, 37, 21], [42, 27, 37, 25], [43, 14, 38, 12], [44, 14, 39, 12], [44, 21, 39, 19], [45, 16, 40, 14], [45, 19, 40, 17, "nextState"], [45, 28, 40, 26], [46, 16, 41, 14, "routes"], [46, 22, 41, 20], [46, 24, 41, 22, "nextState"], [46, 33, 41, 31], [46, 34, 41, 32, "routes"], [46, 40, 41, 38], [46, 41, 41, 39, "map"], [46, 44, 41, 42], [46, 45, 41, 43, "route"], [46, 50, 41, 48], [46, 54, 41, 52, "route"], [46, 59, 41, 57], [46, 60, 41, 58, "key"], [46, 63, 41, 61], [46, 66, 41, 64, "route"], [46, 71, 41, 69], [46, 74, 41, 72], [47, 18, 42, 16], [47, 21, 42, 19, "route"], [47, 26, 42, 24], [48, 18, 43, 16, "key"], [48, 21, 43, 19], [48, 23, 43, 21], [48, 26, 43, 24, "route"], [48, 31, 43, 29], [48, 32, 43, 30, "name"], [48, 36, 43, 34], [48, 40, 43, 38], [48, 44, 43, 38, "nanoid"], [48, 61, 43, 44], [48, 63, 43, 45], [48, 64, 43, 46], [49, 16, 44, 14], [49, 17, 44, 15], [50, 14, 45, 12], [50, 15, 45, 13], [51, 12, 46, 10], [52, 12, 47, 10], [52, 19, 47, 17, "nextState"], [52, 28, 47, 26], [53, 10, 48, 8], [54, 8, 49, 6], [55, 10, 50, 8], [55, 17, 50, 15], [55, 21, 50, 19], [56, 6, 51, 4], [57, 4, 52, 2], [57, 5, 52, 3], [58, 4, 53, 2, "shouldActionChangeFocus"], [58, 27, 53, 25, "shouldActionChangeFocus"], [58, 28, 53, 26, "action"], [58, 34, 53, 32], [58, 36, 53, 34], [59, 6, 54, 4], [59, 13, 54, 11, "action"], [59, 19, 54, 17], [59, 20, 54, 18, "type"], [59, 24, 54, 22], [59, 29, 54, 27], [59, 39, 54, 37], [59, 43, 54, 41, "action"], [59, 49, 54, 47], [59, 50, 54, 48, "type"], [59, 54, 54, 52], [59, 59, 54, 57], [59, 80, 54, 78], [60, 4, 55, 2], [61, 2, 56, 0], [61, 3, 56, 1], [62, 0, 56, 2], [62, 3]], "functionMap": {"names": ["<global>", "getStateForAction", "state.routes.findIndex$argument_0", "state.routes.map$argument_0", "nextState.routes.some$argument_0", "nextState.routeNames.some$argument_0", "nextState.routes.map$argument_0", "shouldActionChangeFocus"], "mappings": "AAA;ECQ;+DCK,4BD;qCEM;iBFM;qEGM,+CH;qGII,wCJ;2CKK;eLG;GDQ;EOC;GPE"}}, "type": "js/module"}]}