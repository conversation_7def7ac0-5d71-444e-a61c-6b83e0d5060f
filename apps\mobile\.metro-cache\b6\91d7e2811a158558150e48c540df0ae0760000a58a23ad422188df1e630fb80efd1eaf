{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PropsAllowlists = void 0;\n  var PropsAllowlists = exports.PropsAllowlists = {\n    /** Styles allowed to be direcly updated in UI thread */\n    UI_THREAD_PROPS_WHITELIST: {\n      opacity: true,\n      transform: true,\n      /* colors */\n      backgroundColor: true,\n      borderRightColor: true,\n      borderBottomColor: true,\n      borderColor: true,\n      borderEndColor: true,\n      borderLeftColor: true,\n      borderStartColor: true,\n      borderTopColor: true,\n      /* ios styles */\n      shadowOpacity: true,\n      shadowRadius: true,\n      /* legacy android transform properties */\n      scaleX: true,\n      scaleY: true,\n      translateX: true,\n      translateY: true\n    },\n    /**\n     * Whitelist of view props that can be updated in native thread via\n     * UIManagerModule\n     */\n    NATIVE_THREAD_PROPS_WHITELIST: {\n      borderBottomWidth: true,\n      borderEndWidth: true,\n      borderLeftWidth: true,\n      borderRightWidth: true,\n      borderStartWidth: true,\n      borderTopWidth: true,\n      borderWidth: true,\n      bottom: true,\n      boxShadow: true,\n      flex: true,\n      flexGrow: true,\n      flexShrink: true,\n      height: true,\n      left: true,\n      margin: true,\n      marginBottom: true,\n      marginEnd: true,\n      marginHorizontal: true,\n      marginLeft: true,\n      marginRight: true,\n      marginStart: true,\n      marginTop: true,\n      marginVertical: true,\n      maxHeight: true,\n      maxWidth: true,\n      minHeight: true,\n      minWidth: true,\n      padding: true,\n      paddingBottom: true,\n      paddingEnd: true,\n      paddingHorizontal: true,\n      paddingLeft: true,\n      paddingRight: true,\n      paddingStart: true,\n      paddingTop: true,\n      paddingVertical: true,\n      right: true,\n      start: true,\n      top: true,\n      width: true,\n      zIndex: true,\n      borderBottomEndRadius: true,\n      borderBottomLeftRadius: true,\n      borderBottomRightRadius: true,\n      borderBottomStartRadius: true,\n      borderRadius: true,\n      borderTopEndRadius: true,\n      borderTopLeftRadius: true,\n      borderTopRightRadius: true,\n      borderTopStartRadius: true,\n      elevation: true,\n      fontSize: true,\n      lineHeight: true,\n      textShadowRadius: true,\n      textShadowOffset: true,\n      letterSpacing: true,\n      aspectRatio: true,\n      columnGap: true,\n      // iOS only\n      end: true,\n      // number or string\n      flexBasis: true,\n      // number or string\n      gap: true,\n      rowGap: true,\n      /* strings */\n      display: true,\n      backfaceVisibility: true,\n      overflow: true,\n      resizeMode: true,\n      fontStyle: true,\n      fontWeight: true,\n      textAlign: true,\n      textDecorationLine: true,\n      fontFamily: true,\n      textAlignVertical: true,\n      fontVariant: true,\n      textDecorationStyle: true,\n      textTransform: true,\n      writingDirection: true,\n      alignContent: true,\n      alignItems: true,\n      alignSelf: true,\n      direction: true,\n      // iOS only\n      flexDirection: true,\n      flexWrap: true,\n      justifyContent: true,\n      position: true,\n      /* text color */\n      color: true,\n      tintColor: true,\n      shadowColor: true,\n      placeholderTextColor: true\n    }\n  };\n});", "lineCount": 132, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "PropsAllowlists"], [7, 25, 1, 13], [8, 2, 7, 7], [8, 6, 7, 13, "PropsAllowlists"], [8, 21, 7, 46], [8, 24, 7, 46, "exports"], [8, 31, 7, 46], [8, 32, 7, 46, "PropsAllowlists"], [8, 47, 7, 46], [8, 50, 7, 49], [9, 4, 8, 2], [10, 4, 9, 2, "UI_THREAD_PROPS_WHITELIST"], [10, 29, 9, 27], [10, 31, 9, 29], [11, 6, 10, 4, "opacity"], [11, 13, 10, 11], [11, 15, 10, 13], [11, 19, 10, 17], [12, 6, 11, 4, "transform"], [12, 15, 11, 13], [12, 17, 11, 15], [12, 21, 11, 19], [13, 6, 12, 4], [14, 6, 13, 4, "backgroundColor"], [14, 21, 13, 19], [14, 23, 13, 21], [14, 27, 13, 25], [15, 6, 14, 4, "borderRightColor"], [15, 22, 14, 20], [15, 24, 14, 22], [15, 28, 14, 26], [16, 6, 15, 4, "borderBottomColor"], [16, 23, 15, 21], [16, 25, 15, 23], [16, 29, 15, 27], [17, 6, 16, 4, "borderColor"], [17, 17, 16, 15], [17, 19, 16, 17], [17, 23, 16, 21], [18, 6, 17, 4, "borderEndColor"], [18, 20, 17, 18], [18, 22, 17, 20], [18, 26, 17, 24], [19, 6, 18, 4, "borderLeftColor"], [19, 21, 18, 19], [19, 23, 18, 21], [19, 27, 18, 25], [20, 6, 19, 4, "borderStartColor"], [20, 22, 19, 20], [20, 24, 19, 22], [20, 28, 19, 26], [21, 6, 20, 4, "borderTopColor"], [21, 20, 20, 18], [21, 22, 20, 20], [21, 26, 20, 24], [22, 6, 21, 4], [23, 6, 22, 4, "shadowOpacity"], [23, 19, 22, 17], [23, 21, 22, 19], [23, 25, 22, 23], [24, 6, 23, 4, "shadowRadius"], [24, 18, 23, 16], [24, 20, 23, 18], [24, 24, 23, 22], [25, 6, 24, 4], [26, 6, 25, 4, "scaleX"], [26, 12, 25, 10], [26, 14, 25, 12], [26, 18, 25, 16], [27, 6, 26, 4, "scaleY"], [27, 12, 26, 10], [27, 14, 26, 12], [27, 18, 26, 16], [28, 6, 27, 4, "translateX"], [28, 16, 27, 14], [28, 18, 27, 16], [28, 22, 27, 20], [29, 6, 28, 4, "translateY"], [29, 16, 28, 14], [29, 18, 28, 16], [30, 4, 29, 2], [30, 5, 29, 3], [31, 4, 30, 2], [32, 0, 31, 0], [33, 0, 32, 0], [34, 0, 33, 0], [35, 4, 34, 2, "NATIVE_THREAD_PROPS_WHITELIST"], [35, 33, 34, 31], [35, 35, 34, 33], [36, 6, 35, 4, "borderBottomWidth"], [36, 23, 35, 21], [36, 25, 35, 23], [36, 29, 35, 27], [37, 6, 36, 4, "borderEndWidth"], [37, 20, 36, 18], [37, 22, 36, 20], [37, 26, 36, 24], [38, 6, 37, 4, "borderLeftWidth"], [38, 21, 37, 19], [38, 23, 37, 21], [38, 27, 37, 25], [39, 6, 38, 4, "borderRightWidth"], [39, 22, 38, 20], [39, 24, 38, 22], [39, 28, 38, 26], [40, 6, 39, 4, "borderStartWidth"], [40, 22, 39, 20], [40, 24, 39, 22], [40, 28, 39, 26], [41, 6, 40, 4, "borderTopWidth"], [41, 20, 40, 18], [41, 22, 40, 20], [41, 26, 40, 24], [42, 6, 41, 4, "borderWidth"], [42, 17, 41, 15], [42, 19, 41, 17], [42, 23, 41, 21], [43, 6, 42, 4, "bottom"], [43, 12, 42, 10], [43, 14, 42, 12], [43, 18, 42, 16], [44, 6, 43, 4, "boxShadow"], [44, 15, 43, 13], [44, 17, 43, 15], [44, 21, 43, 19], [45, 6, 44, 4, "flex"], [45, 10, 44, 8], [45, 12, 44, 10], [45, 16, 44, 14], [46, 6, 45, 4, "flexGrow"], [46, 14, 45, 12], [46, 16, 45, 14], [46, 20, 45, 18], [47, 6, 46, 4, "flexShrink"], [47, 16, 46, 14], [47, 18, 46, 16], [47, 22, 46, 20], [48, 6, 47, 4, "height"], [48, 12, 47, 10], [48, 14, 47, 12], [48, 18, 47, 16], [49, 6, 48, 4, "left"], [49, 10, 48, 8], [49, 12, 48, 10], [49, 16, 48, 14], [50, 6, 49, 4, "margin"], [50, 12, 49, 10], [50, 14, 49, 12], [50, 18, 49, 16], [51, 6, 50, 4, "marginBottom"], [51, 18, 50, 16], [51, 20, 50, 18], [51, 24, 50, 22], [52, 6, 51, 4, "marginEnd"], [52, 15, 51, 13], [52, 17, 51, 15], [52, 21, 51, 19], [53, 6, 52, 4, "marginHorizontal"], [53, 22, 52, 20], [53, 24, 52, 22], [53, 28, 52, 26], [54, 6, 53, 4, "marginLeft"], [54, 16, 53, 14], [54, 18, 53, 16], [54, 22, 53, 20], [55, 6, 54, 4, "marginRight"], [55, 17, 54, 15], [55, 19, 54, 17], [55, 23, 54, 21], [56, 6, 55, 4, "marginStart"], [56, 17, 55, 15], [56, 19, 55, 17], [56, 23, 55, 21], [57, 6, 56, 4, "marginTop"], [57, 15, 56, 13], [57, 17, 56, 15], [57, 21, 56, 19], [58, 6, 57, 4, "marginVertical"], [58, 20, 57, 18], [58, 22, 57, 20], [58, 26, 57, 24], [59, 6, 58, 4, "maxHeight"], [59, 15, 58, 13], [59, 17, 58, 15], [59, 21, 58, 19], [60, 6, 59, 4, "max<PERSON><PERSON><PERSON>"], [60, 14, 59, 12], [60, 16, 59, 14], [60, 20, 59, 18], [61, 6, 60, 4, "minHeight"], [61, 15, 60, 13], [61, 17, 60, 15], [61, 21, 60, 19], [62, 6, 61, 4, "min<PERSON><PERSON><PERSON>"], [62, 14, 61, 12], [62, 16, 61, 14], [62, 20, 61, 18], [63, 6, 62, 4, "padding"], [63, 13, 62, 11], [63, 15, 62, 13], [63, 19, 62, 17], [64, 6, 63, 4, "paddingBottom"], [64, 19, 63, 17], [64, 21, 63, 19], [64, 25, 63, 23], [65, 6, 64, 4, "paddingEnd"], [65, 16, 64, 14], [65, 18, 64, 16], [65, 22, 64, 20], [66, 6, 65, 4, "paddingHorizontal"], [66, 23, 65, 21], [66, 25, 65, 23], [66, 29, 65, 27], [67, 6, 66, 4, "paddingLeft"], [67, 17, 66, 15], [67, 19, 66, 17], [67, 23, 66, 21], [68, 6, 67, 4, "paddingRight"], [68, 18, 67, 16], [68, 20, 67, 18], [68, 24, 67, 22], [69, 6, 68, 4, "paddingStart"], [69, 18, 68, 16], [69, 20, 68, 18], [69, 24, 68, 22], [70, 6, 69, 4, "paddingTop"], [70, 16, 69, 14], [70, 18, 69, 16], [70, 22, 69, 20], [71, 6, 70, 4, "paddingVertical"], [71, 21, 70, 19], [71, 23, 70, 21], [71, 27, 70, 25], [72, 6, 71, 4, "right"], [72, 11, 71, 9], [72, 13, 71, 11], [72, 17, 71, 15], [73, 6, 72, 4, "start"], [73, 11, 72, 9], [73, 13, 72, 11], [73, 17, 72, 15], [74, 6, 73, 4, "top"], [74, 9, 73, 7], [74, 11, 73, 9], [74, 15, 73, 13], [75, 6, 74, 4, "width"], [75, 11, 74, 9], [75, 13, 74, 11], [75, 17, 74, 15], [76, 6, 75, 4, "zIndex"], [76, 12, 75, 10], [76, 14, 75, 12], [76, 18, 75, 16], [77, 6, 76, 4, "borderBottomEndRadius"], [77, 27, 76, 25], [77, 29, 76, 27], [77, 33, 76, 31], [78, 6, 77, 4, "borderBottomLeftRadius"], [78, 28, 77, 26], [78, 30, 77, 28], [78, 34, 77, 32], [79, 6, 78, 4, "borderBottomRightRadius"], [79, 29, 78, 27], [79, 31, 78, 29], [79, 35, 78, 33], [80, 6, 79, 4, "borderBottomStartRadius"], [80, 29, 79, 27], [80, 31, 79, 29], [80, 35, 79, 33], [81, 6, 80, 4, "borderRadius"], [81, 18, 80, 16], [81, 20, 80, 18], [81, 24, 80, 22], [82, 6, 81, 4, "borderTopEndRadius"], [82, 24, 81, 22], [82, 26, 81, 24], [82, 30, 81, 28], [83, 6, 82, 4, "borderTopLeftRadius"], [83, 25, 82, 23], [83, 27, 82, 25], [83, 31, 82, 29], [84, 6, 83, 4, "borderTopRightRadius"], [84, 26, 83, 24], [84, 28, 83, 26], [84, 32, 83, 30], [85, 6, 84, 4, "borderTopStartRadius"], [85, 26, 84, 24], [85, 28, 84, 26], [85, 32, 84, 30], [86, 6, 85, 4, "elevation"], [86, 15, 85, 13], [86, 17, 85, 15], [86, 21, 85, 19], [87, 6, 86, 4, "fontSize"], [87, 14, 86, 12], [87, 16, 86, 14], [87, 20, 86, 18], [88, 6, 87, 4, "lineHeight"], [88, 16, 87, 14], [88, 18, 87, 16], [88, 22, 87, 20], [89, 6, 88, 4, "textShadowRadius"], [89, 22, 88, 20], [89, 24, 88, 22], [89, 28, 88, 26], [90, 6, 89, 4, "textShadowOffset"], [90, 22, 89, 20], [90, 24, 89, 22], [90, 28, 89, 26], [91, 6, 90, 4, "letterSpacing"], [91, 19, 90, 17], [91, 21, 90, 19], [91, 25, 90, 23], [92, 6, 91, 4, "aspectRatio"], [92, 17, 91, 15], [92, 19, 91, 17], [92, 23, 91, 21], [93, 6, 92, 4, "columnGap"], [93, 15, 92, 13], [93, 17, 92, 15], [93, 21, 92, 19], [94, 6, 92, 21], [95, 6, 93, 4, "end"], [95, 9, 93, 7], [95, 11, 93, 9], [95, 15, 93, 13], [96, 6, 93, 15], [97, 6, 94, 4, "flexBasis"], [97, 15, 94, 13], [97, 17, 94, 15], [97, 21, 94, 19], [98, 6, 94, 21], [99, 6, 95, 4, "gap"], [99, 9, 95, 7], [99, 11, 95, 9], [99, 15, 95, 13], [100, 6, 96, 4, "rowGap"], [100, 12, 96, 10], [100, 14, 96, 12], [100, 18, 96, 16], [101, 6, 97, 4], [102, 6, 98, 4, "display"], [102, 13, 98, 11], [102, 15, 98, 13], [102, 19, 98, 17], [103, 6, 99, 4, "backfaceVisibility"], [103, 24, 99, 22], [103, 26, 99, 24], [103, 30, 99, 28], [104, 6, 100, 4, "overflow"], [104, 14, 100, 12], [104, 16, 100, 14], [104, 20, 100, 18], [105, 6, 101, 4, "resizeMode"], [105, 16, 101, 14], [105, 18, 101, 16], [105, 22, 101, 20], [106, 6, 102, 4, "fontStyle"], [106, 15, 102, 13], [106, 17, 102, 15], [106, 21, 102, 19], [107, 6, 103, 4, "fontWeight"], [107, 16, 103, 14], [107, 18, 103, 16], [107, 22, 103, 20], [108, 6, 104, 4, "textAlign"], [108, 15, 104, 13], [108, 17, 104, 15], [108, 21, 104, 19], [109, 6, 105, 4, "textDecorationLine"], [109, 24, 105, 22], [109, 26, 105, 24], [109, 30, 105, 28], [110, 6, 106, 4, "fontFamily"], [110, 16, 106, 14], [110, 18, 106, 16], [110, 22, 106, 20], [111, 6, 107, 4, "textAlignVertical"], [111, 23, 107, 21], [111, 25, 107, 23], [111, 29, 107, 27], [112, 6, 108, 4, "fontVariant"], [112, 17, 108, 15], [112, 19, 108, 17], [112, 23, 108, 21], [113, 6, 109, 4, "textDecorationStyle"], [113, 25, 109, 23], [113, 27, 109, 25], [113, 31, 109, 29], [114, 6, 110, 4, "textTransform"], [114, 19, 110, 17], [114, 21, 110, 19], [114, 25, 110, 23], [115, 6, 111, 4, "writingDirection"], [115, 22, 111, 20], [115, 24, 111, 22], [115, 28, 111, 26], [116, 6, 112, 4, "align<PERSON><PERSON><PERSON>"], [116, 18, 112, 16], [116, 20, 112, 18], [116, 24, 112, 22], [117, 6, 113, 4, "alignItems"], [117, 16, 113, 14], [117, 18, 113, 16], [117, 22, 113, 20], [118, 6, 114, 4, "alignSelf"], [118, 15, 114, 13], [118, 17, 114, 15], [118, 21, 114, 19], [119, 6, 115, 4, "direction"], [119, 15, 115, 13], [119, 17, 115, 15], [119, 21, 115, 19], [120, 6, 115, 21], [121, 6, 116, 4, "flexDirection"], [121, 19, 116, 17], [121, 21, 116, 19], [121, 25, 116, 23], [122, 6, 117, 4, "flexWrap"], [122, 14, 117, 12], [122, 16, 117, 14], [122, 20, 117, 18], [123, 6, 118, 4, "justifyContent"], [123, 20, 118, 18], [123, 22, 118, 20], [123, 26, 118, 24], [124, 6, 119, 4, "position"], [124, 14, 119, 12], [124, 16, 119, 14], [124, 20, 119, 18], [125, 6, 120, 4], [126, 6, 121, 4, "color"], [126, 11, 121, 9], [126, 13, 121, 11], [126, 17, 121, 15], [127, 6, 122, 4, "tintColor"], [127, 15, 122, 13], [127, 17, 122, 15], [127, 21, 122, 19], [128, 6, 123, 4, "shadowColor"], [128, 17, 123, 15], [128, 19, 123, 17], [128, 23, 123, 21], [129, 6, 124, 4, "placeholderTextColor"], [129, 26, 124, 24], [129, 28, 124, 26], [130, 4, 125, 2], [131, 2, 126, 0], [131, 3, 126, 1], [132, 0, 126, 2], [132, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}