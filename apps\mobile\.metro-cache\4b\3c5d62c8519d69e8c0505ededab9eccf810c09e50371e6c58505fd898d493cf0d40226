{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "promise/setimmediate/es6-extensions", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 58}}], "key": "tK7wK8WZ6beOTMsf0EWhLTLTFcQ=", "exportNames": ["*"]}}, {"name": "promise/setimmediate/finally", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 39}}], "key": "O0ANMjAmpk+rmNPtK88UI7XG9ao=", "exportNames": ["*"]}}, {"name": "promise/setimmediate/rejection-tracking", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 52}}], "key": "4fDbp3ZGuaj/SnZoagfCAXtCAeg=", "exportNames": ["*"]}}, {"name": "./promiseRejectionTrackingOptions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 48}}], "key": "9NzkxmQHn/EQ+r7E9yeWvKGM6O4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _es6Extensions = _interopRequireDefault(require(_dependencyMap[1], \"promise/setimmediate/es6-extensions\"));\n  require(_dependencyMap[2], \"promise/setimmediate/finally\");\n  if (__DEV__) {\n    require(_dependencyMap[3], \"promise/setimmediate/rejection-tracking\").enable(require(_dependencyMap[4], \"./promiseRejectionTrackingOptions\").default);\n  }\n  var _default = exports.default = _es6Extensions.default;\n});", "lineCount": 15, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 13, 0], [9, 6, 13, 0, "_es6Extensions"], [9, 20, 13, 0], [9, 23, 13, 0, "_interopRequireDefault"], [9, 45, 13, 0], [9, 46, 13, 0, "require"], [9, 53, 13, 0], [9, 54, 13, 0, "_dependencyMap"], [9, 68, 13, 0], [10, 2, 15, 0, "require"], [10, 9, 15, 7], [10, 10, 15, 7, "_dependencyMap"], [10, 24, 15, 7], [10, 59, 15, 38], [10, 60, 15, 39], [11, 2, 17, 0], [11, 6, 17, 4, "__DEV__"], [11, 13, 17, 11], [11, 15, 17, 13], [12, 4, 18, 2, "require"], [12, 11, 18, 9], [12, 12, 18, 9, "_dependencyMap"], [12, 26, 18, 9], [12, 72, 18, 51], [12, 73, 18, 52], [12, 74, 18, 53, "enable"], [12, 80, 18, 59], [12, 81, 19, 4, "require"], [12, 88, 19, 11], [12, 89, 19, 11, "_dependencyMap"], [12, 103, 19, 11], [12, 143, 19, 47], [12, 144, 19, 48], [12, 145, 19, 49, "default"], [12, 152, 20, 2], [12, 153, 20, 3], [13, 2, 21, 0], [14, 2, 21, 1], [14, 6, 21, 1, "_default"], [14, 14, 21, 1], [14, 17, 21, 1, "exports"], [14, 24, 21, 1], [14, 25, 21, 1, "default"], [14, 32, 21, 1], [14, 35, 23, 15, "Promise"], [14, 57, 23, 22], [15, 0, 23, 22], [15, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}