{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "../getNextHandlerTag", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 178}, "end": {"line": 10, "column": 57, "index": 235}}], "key": "xPiMMdBr7viMwno5RpgPsKrTYT4=", "exportNames": ["*"]}}, {"name": "../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 678}, "end": {"line": 23, "column": 55, "index": 733}}], "key": "ByXat9lt9duIJLDmSeH0V+tRq1s=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Gesture = exports.ContinousBaseGesture = exports.CALLBACK_TYPE = exports.BaseGesture = void 0;\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/inherits\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/createClass\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/classCallCheck\"));\n  var _getNextHandlerTag = require(_dependencyMap[6], \"../getNextHandlerTag\");\n  var _utils = require(_dependencyMap[7], \"../../utils\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  // Allow adding a ref to a gesture handler\n\n  var CALLBACK_TYPE = exports.CALLBACK_TYPE = {\n    UNDEFINED: 0,\n    BEGAN: 1,\n    START: 2,\n    UPDATE: 3,\n    CHANGE: 4,\n    END: 5,\n    FINALIZE: 6,\n    TOUCHES_DOWN: 7,\n    TOUCHES_MOVE: 8,\n    TOUCHES_UP: 9,\n    TOUCHES_CANCELLED: 10\n  };\n\n  // Allow using CALLBACK_TYPE as object and type\n  // eslint-disable-next-line @typescript-eslint/no-redeclare\n  var Gesture = exports.Gesture = /*#__PURE__*/(0, _createClass2.default)(function Gesture() {\n    (0, _classCallCheck2.default)(this, Gesture);\n  });\n  var nextGestureId = 0;\n  var BaseGesture = exports.BaseGesture = /*#__PURE__*/function (_Gesture) {\n    function BaseGesture() {\n      var _this;\n      (0, _classCallCheck2.default)(this, BaseGesture);\n      _this = _callSuper(this, BaseGesture);\n\n      // Used to check whether the gesture config has been updated when wrapping it\n      // with `useMemo`. Since every config will have a unique id, when the dependencies\n      // don't change, the config won't be recreated and the id will stay the same.\n      // If the id is different, it means that the config has changed and the gesture\n      // needs to be updated.\n      _this.gestureId = -1;\n      _this.handlerTag = -1;\n      _this.handlerName = '';\n      _this.config = {};\n      _this.handlers = {\n        gestureId: -1,\n        handlerTag: -1,\n        isWorklet: []\n      };\n      _this.gestureId = nextGestureId++;\n      _this.handlers.gestureId = _this.gestureId;\n      return _this;\n    }\n    (0, _inherits2.default)(BaseGesture, _Gesture);\n    return (0, _createClass2.default)(BaseGesture, [{\n      key: \"addDependency\",\n      value: function addDependency(key, gesture) {\n        var value = this.config[key];\n        this.config[key] = value ? Array().concat(value, gesture) : [gesture];\n      }\n\n      /**\n       * Sets a `ref` to the gesture object, allowing for interoperability with the old API.\n       * @param ref\n       */\n    }, {\n      key: \"withRef\",\n      value: function withRef(ref) {\n        this.config.ref = ref;\n        return this;\n      }\n\n      // eslint-disable-next-line @typescript-eslint/ban-types\n    }, {\n      key: \"isWorklet\",\n      value: function isWorklet(callback) {\n        // @ts-ignore if callback is a worklet, the property will be available, if not then the check will return false\n        return callback.__workletHash !== undefined;\n      }\n\n      /**\n       * Set the callback that is being called when given gesture handler starts receiving touches.\n       * At the moment of this callback the handler is in `BEGAN` state and we don't know yet if it will recognize the gesture at all.\n       * @param callback\n       */\n    }, {\n      key: \"onBegin\",\n      value: function onBegin(callback) {\n        this.handlers.onBegin = callback;\n        this.handlers.isWorklet[CALLBACK_TYPE.BEGAN] = this.isWorklet(callback);\n        return this;\n      }\n\n      /**\n       * Set the callback that is being called when the gesture is recognized by the handler and it transitions to the `ACTIVE` state.\n       * @param callback\n       */\n    }, {\n      key: \"onStart\",\n      value: function onStart(callback) {\n        this.handlers.onStart = callback;\n        this.handlers.isWorklet[CALLBACK_TYPE.START] = this.isWorklet(callback);\n        return this;\n      }\n\n      /**\n       * Set the callback that is being called when the gesture that was recognized by the handler finishes and handler reaches `END` state.\n       * It will be called only if the handler was previously in the `ACTIVE` state.\n       * @param callback\n       */\n    }, {\n      key: \"onEnd\",\n      value: function onEnd(callback) {\n        this.handlers.onEnd = callback;\n        // @ts-ignore if callback is a worklet, the property will be available, if not then the check will return false\n        this.handlers.isWorklet[CALLBACK_TYPE.END] = this.isWorklet(callback);\n        return this;\n      }\n\n      /**\n       * Set the callback that is being called when the handler finalizes handling gesture - the gesture was recognized and has finished or it failed to recognize.\n       * @param callback\n       */\n    }, {\n      key: \"onFinalize\",\n      value: function onFinalize(callback) {\n        this.handlers.onFinalize = callback;\n        // @ts-ignore if callback is a worklet, the property will be available, if not then the check will return false\n        this.handlers.isWorklet[CALLBACK_TYPE.FINALIZE] = this.isWorklet(callback);\n        return this;\n      }\n\n      /**\n       * Set the `onTouchesDown` callback which is called every time a pointer is placed on the screen.\n       * @param callback\n       */\n    }, {\n      key: \"onTouchesDown\",\n      value: function onTouchesDown(callback) {\n        this.config.needsPointerData = true;\n        this.handlers.onTouchesDown = callback;\n        this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_DOWN] = this.isWorklet(callback);\n        return this;\n      }\n\n      /**\n       * Set the `onTouchesMove` callback which is called every time a pointer is moved on the screen.\n       * @param callback\n       */\n    }, {\n      key: \"onTouchesMove\",\n      value: function onTouchesMove(callback) {\n        this.config.needsPointerData = true;\n        this.handlers.onTouchesMove = callback;\n        this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_MOVE] = this.isWorklet(callback);\n        return this;\n      }\n\n      /**\n       * Set the `onTouchesUp` callback which is called every time a pointer is lifted from the screen.\n       * @param callback\n       */\n    }, {\n      key: \"onTouchesUp\",\n      value: function onTouchesUp(callback) {\n        this.config.needsPointerData = true;\n        this.handlers.onTouchesUp = callback;\n        this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_UP] = this.isWorklet(callback);\n        return this;\n      }\n\n      /**\n       * Set the `onTouchesCancelled` callback which is called every time a pointer stops being tracked, for example when the gesture finishes.\n       * @param callback\n       */\n    }, {\n      key: \"onTouchesCancelled\",\n      value: function onTouchesCancelled(callback) {\n        this.config.needsPointerData = true;\n        this.handlers.onTouchesCancelled = callback;\n        this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_CANCELLED] = this.isWorklet(callback);\n        return this;\n      }\n\n      /**\n       * Indicates whether the given handler should be analyzing stream of touch events or not.\n       * @param enabled\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#enabledvalue-boolean\n       */\n    }, {\n      key: \"enabled\",\n      value: function enabled(_enabled) {\n        this.config.enabled = _enabled;\n        return this;\n      }\n\n      /**\n       * When true the handler will cancel or fail recognition (depending on its current state) whenever the finger leaves the area of the connected view.\n       * @param value\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#shouldcancelwhenoutsidevalue-boolean\n       */\n    }, {\n      key: \"shouldCancelWhenOutside\",\n      value: function shouldCancelWhenOutside(value) {\n        this.config.shouldCancelWhenOutside = value;\n        return this;\n      }\n\n      /**\n       * This parameter enables control over what part of the connected view area can be used to begin recognizing the gesture.\n       * When a negative number is provided the bounds of the view will reduce the area by the given number of points in each of the sides evenly.\n       * @param hitSlop\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#hitslopsettings\n       */\n    }, {\n      key: \"hitSlop\",\n      value: function hitSlop(_hitSlop) {\n        this.config.hitSlop = _hitSlop;\n        return this;\n      }\n\n      /**\n       * #### Web only\n       * This parameter allows to specify which `cursor` should be used when gesture activates.\n       * Supports all CSS cursor values (e.g. `\"grab\"`, `\"zoom-in\"`). Default value is set to `\"auto\"`.\n       * @param activeCursor\n       */\n    }, {\n      key: \"activeCursor\",\n      value: function activeCursor(_activeCursor) {\n        this.config.activeCursor = _activeCursor;\n        return this;\n      }\n\n      /**\n       * #### Web & Android only\n       * Allows users to choose which mouse button should handler respond to.\n       * Arguments can be combined using `|` operator, e.g. `mouseButton(MouseButton.LEFT | MouseButton.RIGHT)`.\n       * Default value is set to `MouseButton.LEFT`.\n       * @param mouseButton\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#mousebuttonvalue-mousebutton-web--android-only\n       */\n    }, {\n      key: \"mouseButton\",\n      value: function mouseButton(_mouseButton) {\n        this.config.mouseButton = _mouseButton;\n        return this;\n      }\n\n      /**\n       * When `react-native-reanimated` is installed, the callbacks passed to the gestures are automatically workletized and run on the UI thread when called.\n       * This option allows for changing this behavior: when `true`, all the callbacks will be run on the JS thread instead of the UI thread, regardless of whether they are worklets or not.\n       * Defaults to `false`.\n       * @param runOnJS\n       */\n    }, {\n      key: \"runOnJS\",\n      value: function runOnJS(_runOnJS) {\n        this.config.runOnJS = _runOnJS;\n        return this;\n      }\n\n      /**\n       * Allows gestures across different components to be recognized simultaneously.\n       * @param gestures\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#simultaneouswithexternalgesture\n       */\n    }, {\n      key: \"simultaneousWithExternalGesture\",\n      value: function simultaneousWithExternalGesture() {\n        for (var _len = arguments.length, gestures = new Array(_len), _key = 0; _key < _len; _key++) {\n          gestures[_key] = arguments[_key];\n        }\n        for (var gesture of gestures) {\n          this.addDependency('simultaneousWith', gesture);\n        }\n        return this;\n      }\n\n      /**\n       * Allows to delay activation of the handler until all handlers passed as arguments to this method fail (or don't begin at all).\n       * @param gestures\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#requireexternalgesturetofail\n       */\n    }, {\n      key: \"requireExternalGestureToFail\",\n      value: function requireExternalGestureToFail() {\n        for (var _len2 = arguments.length, gestures = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          gestures[_key2] = arguments[_key2];\n        }\n        for (var gesture of gestures) {\n          this.addDependency('requireToFail', gesture);\n        }\n        return this;\n      }\n\n      /**\n       * Works similarily to `requireExternalGestureToFail` but the direction of the relation is reversed - instead of being one-to-many relation, it's many-to-one.\n       * @param gestures\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#blocksexternalgesture\n       */\n    }, {\n      key: \"blocksExternalGesture\",\n      value: function blocksExternalGesture() {\n        for (var _len3 = arguments.length, gestures = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n          gestures[_key3] = arguments[_key3];\n        }\n        for (var gesture of gestures) {\n          this.addDependency('blocksHandlers', gesture);\n        }\n        return this;\n      }\n\n      /**\n       * Sets a `testID` property for gesture object, allowing for querying for it in tests.\n       * @param id\n       */\n    }, {\n      key: \"withTestId\",\n      value: function withTestId(id) {\n        this.config.testId = id;\n        return this;\n      }\n\n      /**\n       * #### iOS only\n       * When `true`, the handler will cancel touches for native UI components (`UIButton`, `UISwitch`, etc) it's attached to when it becomes `ACTIVE`.\n       * Default value is `true`.\n       * @param value\n       */\n    }, {\n      key: \"cancelsTouchesInView\",\n      value: function cancelsTouchesInView(value) {\n        this.config.cancelsTouchesInView = value;\n        return this;\n      }\n    }, {\n      key: \"initialize\",\n      value: function initialize() {\n        this.handlerTag = (0, _getNextHandlerTag.getNextHandlerTag)();\n        this.handlers = {\n          ...this.handlers,\n          handlerTag: this.handlerTag\n        };\n        if (this.config.ref) {\n          this.config.ref.current = this;\n        }\n      }\n    }, {\n      key: \"toGestureArray\",\n      value: function toGestureArray() {\n        return [this];\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-empty-function\n    }, {\n      key: \"prepare\",\n      value: function prepare() {}\n    }, {\n      key: \"shouldUseReanimated\",\n      get: function () {\n        // Use Reanimated when runOnJS isn't set explicitly,\n        // all defined callbacks are worklets\n        // and remote debugging is disabled\n        return this.config.runOnJS !== true && !this.handlers.isWorklet.includes(false) && !(0, _utils.isRemoteDebuggingEnabled)();\n      }\n    }]);\n  }(Gesture);\n  var ContinousBaseGesture = exports.ContinousBaseGesture = /*#__PURE__*/function (_BaseGesture2) {\n    function ContinousBaseGesture() {\n      (0, _classCallCheck2.default)(this, ContinousBaseGesture);\n      return _callSuper(this, ContinousBaseGesture, arguments);\n    }\n    (0, _inherits2.default)(ContinousBaseGesture, _BaseGesture2);\n    return (0, _createClass2.default)(ContinousBaseGesture, [{\n      key: \"onUpdate\",\n      value:\n      /**\n       * Set the callback that is being called every time the gesture receives an update while it's active.\n       * @param callback\n       */\n      function onUpdate(callback) {\n        this.handlers.onUpdate = callback;\n        this.handlers.isWorklet[CALLBACK_TYPE.UPDATE] = this.isWorklet(callback);\n        return this;\n      }\n\n      /**\n       * Set the callback that is being called every time the gesture receives an update while it's active.\n       * This callback will receive information about change in value in relation to the last received event.\n       * @param callback\n       */\n    }, {\n      key: \"onChange\",\n      value: function onChange(callback) {\n        this.handlers.onChange = callback;\n        this.handlers.isWorklet[CALLBACK_TYPE.CHANGE] = this.isWorklet(callback);\n        return this;\n      }\n\n      /**\n       * When `true` the handler will not activate by itself even if its activation criteria are met.\n       * Instead you can manipulate its state using state manager.\n       * @param manualActivation\n       */\n    }, {\n      key: \"manualActivation\",\n      value: function manualActivation(_manualActivation) {\n        this.config.manualActivation = _manualActivation;\n        return this;\n      }\n    }]);\n  }(BaseGesture);\n});", "lineCount": 422, "map": [[12, 2, 10, 0], [12, 6, 10, 0, "_getNextHandlerTag"], [12, 24, 10, 0], [12, 27, 10, 0, "require"], [12, 34, 10, 0], [12, 35, 10, 0, "_dependencyMap"], [12, 49, 10, 0], [13, 2, 23, 0], [13, 6, 23, 0, "_utils"], [13, 12, 23, 0], [13, 15, 23, 0, "require"], [13, 22, 23, 0], [13, 23, 23, 0, "_dependencyMap"], [13, 37, 23, 0], [14, 2, 23, 55], [14, 11, 23, 55, "_callSuper"], [14, 22, 23, 55, "t"], [14, 23, 23, 55], [14, 25, 23, 55, "o"], [14, 26, 23, 55], [14, 28, 23, 55, "e"], [14, 29, 23, 55], [14, 40, 23, 55, "o"], [14, 41, 23, 55], [14, 48, 23, 55, "_getPrototypeOf2"], [14, 64, 23, 55], [14, 65, 23, 55, "default"], [14, 72, 23, 55], [14, 74, 23, 55, "o"], [14, 75, 23, 55], [14, 82, 23, 55, "_possibleConstructorReturn2"], [14, 109, 23, 55], [14, 110, 23, 55, "default"], [14, 117, 23, 55], [14, 119, 23, 55, "t"], [14, 120, 23, 55], [14, 122, 23, 55, "_isNativeReflectConstruct"], [14, 147, 23, 55], [14, 152, 23, 55, "Reflect"], [14, 159, 23, 55], [14, 160, 23, 55, "construct"], [14, 169, 23, 55], [14, 170, 23, 55, "o"], [14, 171, 23, 55], [14, 173, 23, 55, "e"], [14, 174, 23, 55], [14, 186, 23, 55, "_getPrototypeOf2"], [14, 202, 23, 55], [14, 203, 23, 55, "default"], [14, 210, 23, 55], [14, 212, 23, 55, "t"], [14, 213, 23, 55], [14, 215, 23, 55, "constructor"], [14, 226, 23, 55], [14, 230, 23, 55, "o"], [14, 231, 23, 55], [14, 232, 23, 55, "apply"], [14, 237, 23, 55], [14, 238, 23, 55, "t"], [14, 239, 23, 55], [14, 241, 23, 55, "e"], [14, 242, 23, 55], [15, 2, 23, 55], [15, 11, 23, 55, "_isNativeReflectConstruct"], [15, 37, 23, 55], [15, 51, 23, 55, "t"], [15, 52, 23, 55], [15, 56, 23, 55, "Boolean"], [15, 63, 23, 55], [15, 64, 23, 55, "prototype"], [15, 73, 23, 55], [15, 74, 23, 55, "valueOf"], [15, 81, 23, 55], [15, 82, 23, 55, "call"], [15, 86, 23, 55], [15, 87, 23, 55, "Reflect"], [15, 94, 23, 55], [15, 95, 23, 55, "construct"], [15, 104, 23, 55], [15, 105, 23, 55, "Boolean"], [15, 112, 23, 55], [15, 145, 23, 55, "t"], [15, 146, 23, 55], [15, 159, 23, 55, "_isNativeReflectConstruct"], [15, 184, 23, 55], [15, 196, 23, 55, "_isNativeReflectConstruct"], [15, 197, 23, 55], [15, 210, 23, 55, "t"], [15, 211, 23, 55], [16, 2, 42, 54], [18, 2, 88, 7], [18, 6, 88, 13, "CALLBACK_TYPE"], [18, 19, 88, 26], [18, 22, 88, 26, "exports"], [18, 29, 88, 26], [18, 30, 88, 26, "CALLBACK_TYPE"], [18, 43, 88, 26], [18, 46, 88, 29], [19, 4, 89, 2, "UNDEFINED"], [19, 13, 89, 11], [19, 15, 89, 13], [19, 16, 89, 14], [20, 4, 90, 2, "BEGAN"], [20, 9, 90, 7], [20, 11, 90, 9], [20, 12, 90, 10], [21, 4, 91, 2, "START"], [21, 9, 91, 7], [21, 11, 91, 9], [21, 12, 91, 10], [22, 4, 92, 2, "UPDATE"], [22, 10, 92, 8], [22, 12, 92, 10], [22, 13, 92, 11], [23, 4, 93, 2, "CHANGE"], [23, 10, 93, 8], [23, 12, 93, 10], [23, 13, 93, 11], [24, 4, 94, 2, "END"], [24, 7, 94, 5], [24, 9, 94, 7], [24, 10, 94, 8], [25, 4, 95, 2, "FINALIZE"], [25, 12, 95, 10], [25, 14, 95, 12], [25, 15, 95, 13], [26, 4, 96, 2, "TOUCHES_DOWN"], [26, 16, 96, 14], [26, 18, 96, 16], [26, 19, 96, 17], [27, 4, 97, 2, "TOUCHES_MOVE"], [27, 16, 97, 14], [27, 18, 97, 16], [27, 19, 97, 17], [28, 4, 98, 2, "TOUCHES_UP"], [28, 14, 98, 12], [28, 16, 98, 14], [28, 17, 98, 15], [29, 4, 99, 2, "TOUCHES_CANCELLED"], [29, 21, 99, 19], [29, 23, 99, 21], [30, 2, 100, 0], [30, 3, 100, 10], [32, 2, 102, 0], [33, 2, 103, 0], [34, 2, 103, 0], [34, 6, 106, 22, "Gesture"], [34, 13, 106, 29], [34, 16, 106, 29, "exports"], [34, 23, 106, 29], [34, 24, 106, 29, "Gesture"], [34, 31, 106, 29], [34, 51, 106, 29, "_createClass2"], [34, 64, 106, 29], [34, 65, 106, 29, "default"], [34, 72, 106, 29], [34, 83, 106, 29, "Gesture"], [34, 91, 106, 29], [35, 4, 106, 29], [35, 8, 106, 29, "_classCallCheck2"], [35, 24, 106, 29], [35, 25, 106, 29, "default"], [35, 32, 106, 29], [35, 40, 106, 29, "Gesture"], [35, 47, 106, 29], [36, 2, 106, 29], [37, 2, 126, 0], [37, 6, 126, 4, "nextGestureId"], [37, 19, 126, 17], [37, 22, 126, 20], [37, 23, 126, 21], [38, 2, 126, 22], [38, 6, 127, 22, "BaseGesture"], [38, 17, 127, 33], [38, 20, 127, 33, "exports"], [38, 27, 127, 33], [38, 28, 127, 33, "BaseGesture"], [38, 39, 127, 33], [38, 65, 127, 33, "_Gesture"], [38, 73, 127, 33], [39, 4, 140, 2], [39, 13, 140, 2, "BaseGesture"], [39, 25, 140, 2], [39, 27, 140, 16], [40, 6, 140, 16], [40, 10, 140, 16, "_this"], [40, 15, 140, 16], [41, 6, 140, 16], [41, 10, 140, 16, "_classCallCheck2"], [41, 26, 140, 16], [41, 27, 140, 16, "default"], [41, 34, 140, 16], [41, 42, 140, 16, "BaseGesture"], [41, 53, 140, 16], [42, 6, 141, 4, "_this"], [42, 11, 141, 4], [42, 14, 141, 4, "_callSuper"], [42, 24, 141, 4], [42, 31, 141, 4, "BaseGesture"], [42, 42, 141, 4], [44, 6, 143, 4], [45, 6, 144, 4], [46, 6, 145, 4], [47, 6, 146, 4], [48, 6, 147, 4], [49, 6, 147, 4, "_this"], [49, 11, 147, 4], [49, 12, 130, 10, "gestureId"], [49, 21, 130, 19], [49, 24, 130, 22], [49, 25, 130, 23], [49, 26, 130, 24], [50, 6, 130, 24, "_this"], [50, 11, 130, 24], [50, 12, 131, 9, "handlerTag"], [50, 22, 131, 19], [50, 25, 131, 22], [50, 26, 131, 23], [50, 27, 131, 24], [51, 6, 131, 24, "_this"], [51, 11, 131, 24], [51, 12, 132, 9, "handler<PERSON>ame"], [51, 23, 132, 20], [51, 26, 132, 23], [51, 28, 132, 25], [52, 6, 132, 25, "_this"], [52, 11, 132, 25], [52, 12, 133, 9, "config"], [52, 18, 133, 15], [52, 21, 133, 37], [52, 22, 133, 38], [52, 23, 133, 39], [53, 6, 133, 39, "_this"], [53, 11, 133, 39], [53, 12, 134, 9, "handlers"], [53, 20, 134, 17], [53, 23, 134, 53], [54, 8, 135, 4, "gestureId"], [54, 17, 135, 13], [54, 19, 135, 15], [54, 20, 135, 16], [54, 21, 135, 17], [55, 8, 136, 4, "handlerTag"], [55, 18, 136, 14], [55, 20, 136, 16], [55, 21, 136, 17], [55, 22, 136, 18], [56, 8, 137, 4, "isWorklet"], [56, 17, 137, 13], [56, 19, 137, 15], [57, 6, 138, 2], [57, 7, 138, 3], [58, 6, 148, 4, "_this"], [58, 11, 148, 4], [58, 12, 148, 9, "gestureId"], [58, 21, 148, 18], [58, 24, 148, 21, "nextGestureId"], [58, 37, 148, 34], [58, 39, 148, 36], [59, 6, 149, 4, "_this"], [59, 11, 149, 4], [59, 12, 149, 9, "handlers"], [59, 20, 149, 17], [59, 21, 149, 18, "gestureId"], [59, 30, 149, 27], [59, 33, 149, 30, "_this"], [59, 38, 149, 30], [59, 39, 149, 35, "gestureId"], [59, 48, 149, 44], [60, 6, 149, 45], [60, 13, 149, 45, "_this"], [60, 18, 149, 45], [61, 4, 150, 2], [62, 4, 150, 3], [62, 8, 150, 3, "_inherits2"], [62, 18, 150, 3], [62, 19, 150, 3, "default"], [62, 26, 150, 3], [62, 28, 150, 3, "BaseGesture"], [62, 39, 150, 3], [62, 41, 150, 3, "_Gesture"], [62, 49, 150, 3], [63, 4, 150, 3], [63, 15, 150, 3, "_createClass2"], [63, 28, 150, 3], [63, 29, 150, 3, "default"], [63, 36, 150, 3], [63, 38, 150, 3, "BaseGesture"], [63, 49, 150, 3], [64, 6, 150, 3, "key"], [64, 9, 150, 3], [65, 6, 150, 3, "value"], [65, 11, 150, 3], [65, 13, 152, 2], [65, 22, 152, 10, "addDependency"], [65, 35, 152, 23, "addDependency"], [65, 36, 153, 4, "key"], [65, 39, 153, 64], [65, 41, 154, 4, "gesture"], [65, 48, 154, 40], [65, 50, 155, 4], [66, 8, 156, 4], [66, 12, 156, 10, "value"], [66, 17, 156, 15], [66, 20, 156, 18], [66, 24, 156, 22], [66, 25, 156, 23, "config"], [66, 31, 156, 29], [66, 32, 156, 30, "key"], [66, 35, 156, 33], [66, 36, 156, 34], [67, 8, 157, 4], [67, 12, 157, 8], [67, 13, 157, 9, "config"], [67, 19, 157, 15], [67, 20, 157, 16, "key"], [67, 23, 157, 19], [67, 24, 157, 20], [67, 27, 157, 23, "value"], [67, 32, 157, 28], [67, 35, 158, 8, "Array"], [67, 40, 158, 13], [67, 41, 158, 26], [67, 42, 158, 27], [67, 43, 158, 28, "concat"], [67, 49, 158, 34], [67, 50, 158, 35, "value"], [67, 55, 158, 40], [67, 57, 158, 42, "gesture"], [67, 64, 158, 49], [67, 65, 158, 50], [67, 68, 159, 8], [67, 69, 159, 9, "gesture"], [67, 76, 159, 16], [67, 77, 159, 17], [68, 6, 160, 2], [70, 6, 162, 2], [71, 0, 163, 0], [72, 0, 164, 0], [73, 0, 165, 0], [74, 4, 162, 2], [75, 6, 162, 2, "key"], [75, 9, 162, 2], [76, 6, 162, 2, "value"], [76, 11, 162, 2], [76, 13, 166, 2], [76, 22, 166, 2, "with<PERSON>ef"], [76, 29, 166, 9, "with<PERSON>ef"], [76, 30, 166, 10, "ref"], [76, 33, 166, 62], [76, 35, 166, 64], [77, 8, 167, 4], [77, 12, 167, 8], [77, 13, 167, 9, "config"], [77, 19, 167, 15], [77, 20, 167, 16, "ref"], [77, 23, 167, 19], [77, 26, 167, 22, "ref"], [77, 29, 167, 25], [78, 8, 168, 4], [78, 15, 168, 11], [78, 19, 168, 15], [79, 6, 169, 2], [81, 6, 171, 2], [82, 4, 171, 2], [83, 6, 171, 2, "key"], [83, 9, 171, 2], [84, 6, 171, 2, "value"], [84, 11, 171, 2], [84, 13, 172, 2], [84, 22, 172, 12, "isWorklet"], [84, 31, 172, 21, "isWorklet"], [84, 32, 172, 22, "callback"], [84, 40, 172, 40], [84, 42, 172, 42], [85, 8, 173, 4], [86, 8, 174, 4], [86, 15, 174, 11, "callback"], [86, 23, 174, 19], [86, 24, 174, 20, "__workletHash"], [86, 37, 174, 33], [86, 42, 174, 38, "undefined"], [86, 51, 174, 47], [87, 6, 175, 2], [89, 6, 177, 2], [90, 0, 178, 0], [91, 0, 179, 0], [92, 0, 180, 0], [93, 0, 181, 0], [94, 4, 177, 2], [95, 6, 177, 2, "key"], [95, 9, 177, 2], [96, 6, 177, 2, "value"], [96, 11, 177, 2], [96, 13, 182, 2], [96, 22, 182, 2, "onBegin"], [96, 29, 182, 9, "onBegin"], [96, 30, 182, 10, "callback"], [96, 38, 182, 75], [96, 40, 182, 77], [97, 8, 183, 4], [97, 12, 183, 8], [97, 13, 183, 9, "handlers"], [97, 21, 183, 17], [97, 22, 183, 18, "onBegin"], [97, 29, 183, 25], [97, 32, 183, 28, "callback"], [97, 40, 183, 36], [98, 8, 184, 4], [98, 12, 184, 8], [98, 13, 184, 9, "handlers"], [98, 21, 184, 17], [98, 22, 184, 18, "isWorklet"], [98, 31, 184, 27], [98, 32, 184, 28, "CALLBACK_TYPE"], [98, 45, 184, 41], [98, 46, 184, 42, "BEGAN"], [98, 51, 184, 47], [98, 52, 184, 48], [98, 55, 184, 51], [98, 59, 184, 55], [98, 60, 184, 56, "isWorklet"], [98, 69, 184, 65], [98, 70, 184, 66, "callback"], [98, 78, 184, 74], [98, 79, 184, 75], [99, 8, 185, 4], [99, 15, 185, 11], [99, 19, 185, 15], [100, 6, 186, 2], [102, 6, 188, 2], [103, 0, 189, 0], [104, 0, 190, 0], [105, 0, 191, 0], [106, 4, 188, 2], [107, 6, 188, 2, "key"], [107, 9, 188, 2], [108, 6, 188, 2, "value"], [108, 11, 188, 2], [108, 13, 192, 2], [108, 22, 192, 2, "onStart"], [108, 29, 192, 9, "onStart"], [108, 30, 192, 10, "callback"], [108, 38, 192, 75], [108, 40, 192, 77], [109, 8, 193, 4], [109, 12, 193, 8], [109, 13, 193, 9, "handlers"], [109, 21, 193, 17], [109, 22, 193, 18, "onStart"], [109, 29, 193, 25], [109, 32, 193, 28, "callback"], [109, 40, 193, 36], [110, 8, 194, 4], [110, 12, 194, 8], [110, 13, 194, 9, "handlers"], [110, 21, 194, 17], [110, 22, 194, 18, "isWorklet"], [110, 31, 194, 27], [110, 32, 194, 28, "CALLBACK_TYPE"], [110, 45, 194, 41], [110, 46, 194, 42, "START"], [110, 51, 194, 47], [110, 52, 194, 48], [110, 55, 194, 51], [110, 59, 194, 55], [110, 60, 194, 56, "isWorklet"], [110, 69, 194, 65], [110, 70, 194, 66, "callback"], [110, 78, 194, 74], [110, 79, 194, 75], [111, 8, 195, 4], [111, 15, 195, 11], [111, 19, 195, 15], [112, 6, 196, 2], [114, 6, 198, 2], [115, 0, 199, 0], [116, 0, 200, 0], [117, 0, 201, 0], [118, 0, 202, 0], [119, 4, 198, 2], [120, 6, 198, 2, "key"], [120, 9, 198, 2], [121, 6, 198, 2, "value"], [121, 11, 198, 2], [121, 13, 203, 2], [121, 22, 203, 2, "onEnd"], [121, 27, 203, 7, "onEnd"], [121, 28, 204, 4, "callback"], [121, 36, 207, 13], [121, 38, 208, 4], [122, 8, 209, 4], [122, 12, 209, 8], [122, 13, 209, 9, "handlers"], [122, 21, 209, 17], [122, 22, 209, 18, "onEnd"], [122, 27, 209, 23], [122, 30, 209, 26, "callback"], [122, 38, 209, 34], [123, 8, 210, 4], [124, 8, 211, 4], [124, 12, 211, 8], [124, 13, 211, 9, "handlers"], [124, 21, 211, 17], [124, 22, 211, 18, "isWorklet"], [124, 31, 211, 27], [124, 32, 211, 28, "CALLBACK_TYPE"], [124, 45, 211, 41], [124, 46, 211, 42, "END"], [124, 49, 211, 45], [124, 50, 211, 46], [124, 53, 211, 49], [124, 57, 211, 53], [124, 58, 211, 54, "isWorklet"], [124, 67, 211, 63], [124, 68, 211, 64, "callback"], [124, 76, 211, 72], [124, 77, 211, 73], [125, 8, 212, 4], [125, 15, 212, 11], [125, 19, 212, 15], [126, 6, 213, 2], [128, 6, 215, 2], [129, 0, 216, 0], [130, 0, 217, 0], [131, 0, 218, 0], [132, 4, 215, 2], [133, 6, 215, 2, "key"], [133, 9, 215, 2], [134, 6, 215, 2, "value"], [134, 11, 215, 2], [134, 13, 219, 2], [134, 22, 219, 2, "onFinalize"], [134, 32, 219, 12, "onFinalize"], [134, 33, 220, 4, "callback"], [134, 41, 223, 13], [134, 43, 224, 4], [135, 8, 225, 4], [135, 12, 225, 8], [135, 13, 225, 9, "handlers"], [135, 21, 225, 17], [135, 22, 225, 18, "onFinalize"], [135, 32, 225, 28], [135, 35, 225, 31, "callback"], [135, 43, 225, 39], [136, 8, 226, 4], [137, 8, 227, 4], [137, 12, 227, 8], [137, 13, 227, 9, "handlers"], [137, 21, 227, 17], [137, 22, 227, 18, "isWorklet"], [137, 31, 227, 27], [137, 32, 227, 28, "CALLBACK_TYPE"], [137, 45, 227, 41], [137, 46, 227, 42, "FINALIZE"], [137, 54, 227, 50], [137, 55, 227, 51], [137, 58, 227, 54], [137, 62, 227, 58], [137, 63, 227, 59, "isWorklet"], [137, 72, 227, 68], [137, 73, 227, 69, "callback"], [137, 81, 227, 77], [137, 82, 227, 78], [138, 8, 228, 4], [138, 15, 228, 11], [138, 19, 228, 15], [139, 6, 229, 2], [141, 6, 231, 2], [142, 0, 232, 0], [143, 0, 233, 0], [144, 0, 234, 0], [145, 4, 231, 2], [146, 6, 231, 2, "key"], [146, 9, 231, 2], [147, 6, 231, 2, "value"], [147, 11, 231, 2], [147, 13, 235, 2], [147, 22, 235, 2, "onTouchesDown"], [147, 35, 235, 15, "onTouchesDown"], [147, 36, 235, 16, "callback"], [147, 44, 235, 47], [147, 46, 235, 49], [148, 8, 236, 4], [148, 12, 236, 8], [148, 13, 236, 9, "config"], [148, 19, 236, 15], [148, 20, 236, 16, "needsPointerData"], [148, 36, 236, 32], [148, 39, 236, 35], [148, 43, 236, 39], [149, 8, 237, 4], [149, 12, 237, 8], [149, 13, 237, 9, "handlers"], [149, 21, 237, 17], [149, 22, 237, 18, "onTouchesDown"], [149, 35, 237, 31], [149, 38, 237, 34, "callback"], [149, 46, 237, 42], [150, 8, 238, 4], [150, 12, 238, 8], [150, 13, 238, 9, "handlers"], [150, 21, 238, 17], [150, 22, 238, 18, "isWorklet"], [150, 31, 238, 27], [150, 32, 238, 28, "CALLBACK_TYPE"], [150, 45, 238, 41], [150, 46, 238, 42, "TOUCHES_DOWN"], [150, 58, 238, 54], [150, 59, 238, 55], [150, 62, 239, 6], [150, 66, 239, 10], [150, 67, 239, 11, "isWorklet"], [150, 76, 239, 20], [150, 77, 239, 21, "callback"], [150, 85, 239, 29], [150, 86, 239, 30], [151, 8, 241, 4], [151, 15, 241, 11], [151, 19, 241, 15], [152, 6, 242, 2], [154, 6, 244, 2], [155, 0, 245, 0], [156, 0, 246, 0], [157, 0, 247, 0], [158, 4, 244, 2], [159, 6, 244, 2, "key"], [159, 9, 244, 2], [160, 6, 244, 2, "value"], [160, 11, 244, 2], [160, 13, 248, 2], [160, 22, 248, 2, "onTouchesMove"], [160, 35, 248, 15, "onTouchesMove"], [160, 36, 248, 16, "callback"], [160, 44, 248, 47], [160, 46, 248, 49], [161, 8, 249, 4], [161, 12, 249, 8], [161, 13, 249, 9, "config"], [161, 19, 249, 15], [161, 20, 249, 16, "needsPointerData"], [161, 36, 249, 32], [161, 39, 249, 35], [161, 43, 249, 39], [162, 8, 250, 4], [162, 12, 250, 8], [162, 13, 250, 9, "handlers"], [162, 21, 250, 17], [162, 22, 250, 18, "onTouchesMove"], [162, 35, 250, 31], [162, 38, 250, 34, "callback"], [162, 46, 250, 42], [163, 8, 251, 4], [163, 12, 251, 8], [163, 13, 251, 9, "handlers"], [163, 21, 251, 17], [163, 22, 251, 18, "isWorklet"], [163, 31, 251, 27], [163, 32, 251, 28, "CALLBACK_TYPE"], [163, 45, 251, 41], [163, 46, 251, 42, "TOUCHES_MOVE"], [163, 58, 251, 54], [163, 59, 251, 55], [163, 62, 252, 6], [163, 66, 252, 10], [163, 67, 252, 11, "isWorklet"], [163, 76, 252, 20], [163, 77, 252, 21, "callback"], [163, 85, 252, 29], [163, 86, 252, 30], [164, 8, 254, 4], [164, 15, 254, 11], [164, 19, 254, 15], [165, 6, 255, 2], [167, 6, 257, 2], [168, 0, 258, 0], [169, 0, 259, 0], [170, 0, 260, 0], [171, 4, 257, 2], [172, 6, 257, 2, "key"], [172, 9, 257, 2], [173, 6, 257, 2, "value"], [173, 11, 257, 2], [173, 13, 261, 2], [173, 22, 261, 2, "onTouchesUp"], [173, 33, 261, 13, "onTouchesUp"], [173, 34, 261, 14, "callback"], [173, 42, 261, 45], [173, 44, 261, 47], [174, 8, 262, 4], [174, 12, 262, 8], [174, 13, 262, 9, "config"], [174, 19, 262, 15], [174, 20, 262, 16, "needsPointerData"], [174, 36, 262, 32], [174, 39, 262, 35], [174, 43, 262, 39], [175, 8, 263, 4], [175, 12, 263, 8], [175, 13, 263, 9, "handlers"], [175, 21, 263, 17], [175, 22, 263, 18, "onTouchesUp"], [175, 33, 263, 29], [175, 36, 263, 32, "callback"], [175, 44, 263, 40], [176, 8, 264, 4], [176, 12, 264, 8], [176, 13, 264, 9, "handlers"], [176, 21, 264, 17], [176, 22, 264, 18, "isWorklet"], [176, 31, 264, 27], [176, 32, 264, 28, "CALLBACK_TYPE"], [176, 45, 264, 41], [176, 46, 264, 42, "TOUCHES_UP"], [176, 56, 264, 52], [176, 57, 264, 53], [176, 60, 265, 6], [176, 64, 265, 10], [176, 65, 265, 11, "isWorklet"], [176, 74, 265, 20], [176, 75, 265, 21, "callback"], [176, 83, 265, 29], [176, 84, 265, 30], [177, 8, 267, 4], [177, 15, 267, 11], [177, 19, 267, 15], [178, 6, 268, 2], [180, 6, 270, 2], [181, 0, 271, 0], [182, 0, 272, 0], [183, 0, 273, 0], [184, 4, 270, 2], [185, 6, 270, 2, "key"], [185, 9, 270, 2], [186, 6, 270, 2, "value"], [186, 11, 270, 2], [186, 13, 274, 2], [186, 22, 274, 2, "onTouchesCancelled"], [186, 40, 274, 20, "onTouchesCancelled"], [186, 41, 274, 21, "callback"], [186, 49, 274, 52], [186, 51, 274, 54], [187, 8, 275, 4], [187, 12, 275, 8], [187, 13, 275, 9, "config"], [187, 19, 275, 15], [187, 20, 275, 16, "needsPointerData"], [187, 36, 275, 32], [187, 39, 275, 35], [187, 43, 275, 39], [188, 8, 276, 4], [188, 12, 276, 8], [188, 13, 276, 9, "handlers"], [188, 21, 276, 17], [188, 22, 276, 18, "onTouchesCancelled"], [188, 40, 276, 36], [188, 43, 276, 39, "callback"], [188, 51, 276, 47], [189, 8, 277, 4], [189, 12, 277, 8], [189, 13, 277, 9, "handlers"], [189, 21, 277, 17], [189, 22, 277, 18, "isWorklet"], [189, 31, 277, 27], [189, 32, 277, 28, "CALLBACK_TYPE"], [189, 45, 277, 41], [189, 46, 277, 42, "TOUCHES_CANCELLED"], [189, 63, 277, 59], [189, 64, 277, 60], [189, 67, 278, 6], [189, 71, 278, 10], [189, 72, 278, 11, "isWorklet"], [189, 81, 278, 20], [189, 82, 278, 21, "callback"], [189, 90, 278, 29], [189, 91, 278, 30], [190, 8, 280, 4], [190, 15, 280, 11], [190, 19, 280, 15], [191, 6, 281, 2], [193, 6, 283, 2], [194, 0, 284, 0], [195, 0, 285, 0], [196, 0, 286, 0], [197, 0, 287, 0], [198, 4, 283, 2], [199, 6, 283, 2, "key"], [199, 9, 283, 2], [200, 6, 283, 2, "value"], [200, 11, 283, 2], [200, 13, 288, 2], [200, 22, 288, 2, "enabled"], [200, 29, 288, 9, "enabled"], [200, 30, 288, 10, "enabled"], [200, 38, 288, 26], [200, 40, 288, 28], [201, 8, 289, 4], [201, 12, 289, 8], [201, 13, 289, 9, "config"], [201, 19, 289, 15], [201, 20, 289, 16, "enabled"], [201, 27, 289, 23], [201, 30, 289, 26, "enabled"], [201, 38, 289, 33], [202, 8, 290, 4], [202, 15, 290, 11], [202, 19, 290, 15], [203, 6, 291, 2], [205, 6, 293, 2], [206, 0, 294, 0], [207, 0, 295, 0], [208, 0, 296, 0], [209, 0, 297, 0], [210, 4, 293, 2], [211, 6, 293, 2, "key"], [211, 9, 293, 2], [212, 6, 293, 2, "value"], [212, 11, 293, 2], [212, 13, 298, 2], [212, 22, 298, 2, "shouldCancelWhenOutside"], [212, 45, 298, 25, "shouldCancelWhenOutside"], [212, 46, 298, 26, "value"], [212, 51, 298, 40], [212, 53, 298, 42], [213, 8, 299, 4], [213, 12, 299, 8], [213, 13, 299, 9, "config"], [213, 19, 299, 15], [213, 20, 299, 16, "shouldCancelWhenOutside"], [213, 43, 299, 39], [213, 46, 299, 42, "value"], [213, 51, 299, 47], [214, 8, 300, 4], [214, 15, 300, 11], [214, 19, 300, 15], [215, 6, 301, 2], [217, 6, 303, 2], [218, 0, 304, 0], [219, 0, 305, 0], [220, 0, 306, 0], [221, 0, 307, 0], [222, 0, 308, 0], [223, 4, 303, 2], [224, 6, 303, 2, "key"], [224, 9, 303, 2], [225, 6, 303, 2, "value"], [225, 11, 303, 2], [225, 13, 309, 2], [225, 22, 309, 2, "hitSlop"], [225, 29, 309, 9, "hitSlop"], [225, 30, 309, 10, "hitSlop"], [225, 38, 309, 26], [225, 40, 309, 28], [226, 8, 310, 4], [226, 12, 310, 8], [226, 13, 310, 9, "config"], [226, 19, 310, 15], [226, 20, 310, 16, "hitSlop"], [226, 27, 310, 23], [226, 30, 310, 26, "hitSlop"], [226, 38, 310, 33], [227, 8, 311, 4], [227, 15, 311, 11], [227, 19, 311, 15], [228, 6, 312, 2], [230, 6, 314, 2], [231, 0, 315, 0], [232, 0, 316, 0], [233, 0, 317, 0], [234, 0, 318, 0], [235, 0, 319, 0], [236, 4, 314, 2], [237, 6, 314, 2, "key"], [237, 9, 314, 2], [238, 6, 314, 2, "value"], [238, 11, 314, 2], [238, 13, 320, 2], [238, 22, 320, 2, "activeCursor"], [238, 34, 320, 14, "activeCursor"], [238, 35, 320, 15, "activeCursor"], [238, 48, 320, 41], [238, 50, 320, 43], [239, 8, 321, 4], [239, 12, 321, 8], [239, 13, 321, 9, "config"], [239, 19, 321, 15], [239, 20, 321, 16, "activeCursor"], [239, 32, 321, 28], [239, 35, 321, 31, "activeCursor"], [239, 48, 321, 43], [240, 8, 322, 4], [240, 15, 322, 11], [240, 19, 322, 15], [241, 6, 323, 2], [243, 6, 325, 2], [244, 0, 326, 0], [245, 0, 327, 0], [246, 0, 328, 0], [247, 0, 329, 0], [248, 0, 330, 0], [249, 0, 331, 0], [250, 0, 332, 0], [251, 4, 325, 2], [252, 6, 325, 2, "key"], [252, 9, 325, 2], [253, 6, 325, 2, "value"], [253, 11, 325, 2], [253, 13, 333, 2], [253, 22, 333, 2, "mouseButton"], [253, 33, 333, 13, "mouseButton"], [253, 34, 333, 14, "mouseButton"], [253, 46, 333, 38], [253, 48, 333, 40], [254, 8, 334, 4], [254, 12, 334, 8], [254, 13, 334, 9, "config"], [254, 19, 334, 15], [254, 20, 334, 16, "mouseButton"], [254, 31, 334, 27], [254, 34, 334, 30, "mouseButton"], [254, 46, 334, 41], [255, 8, 335, 4], [255, 15, 335, 11], [255, 19, 335, 15], [256, 6, 336, 2], [258, 6, 338, 2], [259, 0, 339, 0], [260, 0, 340, 0], [261, 0, 341, 0], [262, 0, 342, 0], [263, 0, 343, 0], [264, 4, 338, 2], [265, 6, 338, 2, "key"], [265, 9, 338, 2], [266, 6, 338, 2, "value"], [266, 11, 338, 2], [266, 13, 344, 2], [266, 22, 344, 2, "runOnJS"], [266, 29, 344, 9, "runOnJS"], [266, 30, 344, 10, "runOnJS"], [266, 38, 344, 26], [266, 40, 344, 28], [267, 8, 345, 4], [267, 12, 345, 8], [267, 13, 345, 9, "config"], [267, 19, 345, 15], [267, 20, 345, 16, "runOnJS"], [267, 27, 345, 23], [267, 30, 345, 26, "runOnJS"], [267, 38, 345, 33], [268, 8, 346, 4], [268, 15, 346, 11], [268, 19, 346, 15], [269, 6, 347, 2], [271, 6, 349, 2], [272, 0, 350, 0], [273, 0, 351, 0], [274, 0, 352, 0], [275, 0, 353, 0], [276, 4, 349, 2], [277, 6, 349, 2, "key"], [277, 9, 349, 2], [278, 6, 349, 2, "value"], [278, 11, 349, 2], [278, 13, 354, 2], [278, 22, 354, 2, "simultaneousWithExternalGesture"], [278, 53, 354, 33, "simultaneousWithExternalGesture"], [278, 54, 354, 33], [278, 56, 354, 78], [279, 8, 354, 78], [279, 17, 354, 78, "_len"], [279, 21, 354, 78], [279, 24, 354, 78, "arguments"], [279, 33, 354, 78], [279, 34, 354, 78, "length"], [279, 40, 354, 78], [279, 42, 354, 37, "gestures"], [279, 50, 354, 45], [279, 57, 354, 45, "Array"], [279, 62, 354, 45], [279, 63, 354, 45, "_len"], [279, 67, 354, 45], [279, 70, 354, 45, "_key"], [279, 74, 354, 45], [279, 80, 354, 45, "_key"], [279, 84, 354, 45], [279, 87, 354, 45, "_len"], [279, 91, 354, 45], [279, 93, 354, 45, "_key"], [279, 97, 354, 45], [280, 10, 354, 37, "gestures"], [280, 18, 354, 45], [280, 19, 354, 45, "_key"], [280, 23, 354, 45], [280, 27, 354, 45, "arguments"], [280, 36, 354, 45], [280, 37, 354, 45, "_key"], [280, 41, 354, 45], [281, 8, 354, 45], [282, 8, 355, 4], [282, 13, 355, 9], [282, 17, 355, 15, "gesture"], [282, 24, 355, 22], [282, 28, 355, 26, "gestures"], [282, 36, 355, 34], [282, 38, 355, 36], [283, 10, 356, 6], [283, 14, 356, 10], [283, 15, 356, 11, "addDependency"], [283, 28, 356, 24], [283, 29, 356, 25], [283, 47, 356, 43], [283, 49, 356, 45, "gesture"], [283, 56, 356, 52], [283, 57, 356, 53], [284, 8, 357, 4], [285, 8, 358, 4], [285, 15, 358, 11], [285, 19, 358, 15], [286, 6, 359, 2], [288, 6, 361, 2], [289, 0, 362, 0], [290, 0, 363, 0], [291, 0, 364, 0], [292, 0, 365, 0], [293, 4, 361, 2], [294, 6, 361, 2, "key"], [294, 9, 361, 2], [295, 6, 361, 2, "value"], [295, 11, 361, 2], [295, 13, 366, 2], [295, 22, 366, 2, "requireExternalGestureToFail"], [295, 50, 366, 30, "requireExternalGestureToFail"], [295, 51, 366, 30], [295, 53, 366, 75], [296, 8, 366, 75], [296, 17, 366, 75, "_len2"], [296, 22, 366, 75], [296, 25, 366, 75, "arguments"], [296, 34, 366, 75], [296, 35, 366, 75, "length"], [296, 41, 366, 75], [296, 43, 366, 34, "gestures"], [296, 51, 366, 42], [296, 58, 366, 42, "Array"], [296, 63, 366, 42], [296, 64, 366, 42, "_len2"], [296, 69, 366, 42], [296, 72, 366, 42, "_key2"], [296, 77, 366, 42], [296, 83, 366, 42, "_key2"], [296, 88, 366, 42], [296, 91, 366, 42, "_len2"], [296, 96, 366, 42], [296, 98, 366, 42, "_key2"], [296, 103, 366, 42], [297, 10, 366, 34, "gestures"], [297, 18, 366, 42], [297, 19, 366, 42, "_key2"], [297, 24, 366, 42], [297, 28, 366, 42, "arguments"], [297, 37, 366, 42], [297, 38, 366, 42, "_key2"], [297, 43, 366, 42], [298, 8, 366, 42], [299, 8, 367, 4], [299, 13, 367, 9], [299, 17, 367, 15, "gesture"], [299, 24, 367, 22], [299, 28, 367, 26, "gestures"], [299, 36, 367, 34], [299, 38, 367, 36], [300, 10, 368, 6], [300, 14, 368, 10], [300, 15, 368, 11, "addDependency"], [300, 28, 368, 24], [300, 29, 368, 25], [300, 44, 368, 40], [300, 46, 368, 42, "gesture"], [300, 53, 368, 49], [300, 54, 368, 50], [301, 8, 369, 4], [302, 8, 370, 4], [302, 15, 370, 11], [302, 19, 370, 15], [303, 6, 371, 2], [305, 6, 373, 2], [306, 0, 374, 0], [307, 0, 375, 0], [308, 0, 376, 0], [309, 0, 377, 0], [310, 4, 373, 2], [311, 6, 373, 2, "key"], [311, 9, 373, 2], [312, 6, 373, 2, "value"], [312, 11, 373, 2], [312, 13, 378, 2], [312, 22, 378, 2, "blocksExternalGesture"], [312, 43, 378, 23, "blocksExternalGesture"], [312, 44, 378, 23], [312, 46, 378, 68], [313, 8, 378, 68], [313, 17, 378, 68, "_len3"], [313, 22, 378, 68], [313, 25, 378, 68, "arguments"], [313, 34, 378, 68], [313, 35, 378, 68, "length"], [313, 41, 378, 68], [313, 43, 378, 27, "gestures"], [313, 51, 378, 35], [313, 58, 378, 35, "Array"], [313, 63, 378, 35], [313, 64, 378, 35, "_len3"], [313, 69, 378, 35], [313, 72, 378, 35, "_key3"], [313, 77, 378, 35], [313, 83, 378, 35, "_key3"], [313, 88, 378, 35], [313, 91, 378, 35, "_len3"], [313, 96, 378, 35], [313, 98, 378, 35, "_key3"], [313, 103, 378, 35], [314, 10, 378, 27, "gestures"], [314, 18, 378, 35], [314, 19, 378, 35, "_key3"], [314, 24, 378, 35], [314, 28, 378, 35, "arguments"], [314, 37, 378, 35], [314, 38, 378, 35, "_key3"], [314, 43, 378, 35], [315, 8, 378, 35], [316, 8, 379, 4], [316, 13, 379, 9], [316, 17, 379, 15, "gesture"], [316, 24, 379, 22], [316, 28, 379, 26, "gestures"], [316, 36, 379, 34], [316, 38, 379, 36], [317, 10, 380, 6], [317, 14, 380, 10], [317, 15, 380, 11, "addDependency"], [317, 28, 380, 24], [317, 29, 380, 25], [317, 45, 380, 41], [317, 47, 380, 43, "gesture"], [317, 54, 380, 50], [317, 55, 380, 51], [318, 8, 381, 4], [319, 8, 382, 4], [319, 15, 382, 11], [319, 19, 382, 15], [320, 6, 383, 2], [322, 6, 385, 2], [323, 0, 386, 0], [324, 0, 387, 0], [325, 0, 388, 0], [326, 4, 385, 2], [327, 6, 385, 2, "key"], [327, 9, 385, 2], [328, 6, 385, 2, "value"], [328, 11, 385, 2], [328, 13, 389, 2], [328, 22, 389, 2, "withTestId"], [328, 32, 389, 12, "withTestId"], [328, 33, 389, 13, "id"], [328, 35, 389, 23], [328, 37, 389, 25], [329, 8, 390, 4], [329, 12, 390, 8], [329, 13, 390, 9, "config"], [329, 19, 390, 15], [329, 20, 390, 16, "testId"], [329, 26, 390, 22], [329, 29, 390, 25, "id"], [329, 31, 390, 27], [330, 8, 391, 4], [330, 15, 391, 11], [330, 19, 391, 15], [331, 6, 392, 2], [333, 6, 394, 2], [334, 0, 395, 0], [335, 0, 396, 0], [336, 0, 397, 0], [337, 0, 398, 0], [338, 0, 399, 0], [339, 4, 394, 2], [340, 6, 394, 2, "key"], [340, 9, 394, 2], [341, 6, 394, 2, "value"], [341, 11, 394, 2], [341, 13, 400, 2], [341, 22, 400, 2, "cancelsTouchesInView"], [341, 42, 400, 22, "cancelsTouchesInView"], [341, 43, 400, 23, "value"], [341, 48, 400, 37], [341, 50, 400, 39], [342, 8, 401, 4], [342, 12, 401, 8], [342, 13, 401, 9, "config"], [342, 19, 401, 15], [342, 20, 401, 16, "cancelsTouchesInView"], [342, 40, 401, 36], [342, 43, 401, 39, "value"], [342, 48, 401, 44], [343, 8, 402, 4], [343, 15, 402, 11], [343, 19, 402, 15], [344, 6, 403, 2], [345, 4, 403, 3], [346, 6, 403, 3, "key"], [346, 9, 403, 3], [347, 6, 403, 3, "value"], [347, 11, 403, 3], [347, 13, 405, 2], [347, 22, 405, 2, "initialize"], [347, 32, 405, 12, "initialize"], [347, 33, 405, 12], [347, 35, 405, 15], [348, 8, 406, 4], [348, 12, 406, 8], [348, 13, 406, 9, "handlerTag"], [348, 23, 406, 19], [348, 26, 406, 22], [348, 30, 406, 22, "getNextHandlerTag"], [348, 66, 406, 39], [348, 68, 406, 40], [348, 69, 406, 41], [349, 8, 408, 4], [349, 12, 408, 8], [349, 13, 408, 9, "handlers"], [349, 21, 408, 17], [349, 24, 408, 20], [350, 10, 408, 22], [350, 13, 408, 25], [350, 17, 408, 29], [350, 18, 408, 30, "handlers"], [350, 26, 408, 38], [351, 10, 408, 40, "handlerTag"], [351, 20, 408, 50], [351, 22, 408, 52], [351, 26, 408, 56], [351, 27, 408, 57, "handlerTag"], [352, 8, 408, 68], [352, 9, 408, 69], [353, 8, 410, 4], [353, 12, 410, 8], [353, 16, 410, 12], [353, 17, 410, 13, "config"], [353, 23, 410, 19], [353, 24, 410, 20, "ref"], [353, 27, 410, 23], [353, 29, 410, 25], [354, 10, 411, 6], [354, 14, 411, 10], [354, 15, 411, 11, "config"], [354, 21, 411, 17], [354, 22, 411, 18, "ref"], [354, 25, 411, 21], [354, 26, 411, 22, "current"], [354, 33, 411, 29], [354, 36, 411, 32], [354, 40, 411, 51], [355, 8, 412, 4], [356, 6, 413, 2], [357, 4, 413, 3], [358, 6, 413, 3, "key"], [358, 9, 413, 3], [359, 6, 413, 3, "value"], [359, 11, 413, 3], [359, 13, 415, 2], [359, 22, 415, 2, "toGestureArray"], [359, 36, 415, 16, "toGestureArray"], [359, 37, 415, 16], [359, 39, 415, 34], [360, 8, 416, 4], [360, 15, 416, 11], [360, 16, 416, 12], [360, 20, 416, 16], [360, 21, 416, 32], [361, 6, 417, 2], [363, 6, 419, 2], [364, 4, 419, 2], [365, 6, 419, 2, "key"], [365, 9, 419, 2], [366, 6, 419, 2, "value"], [366, 11, 419, 2], [366, 13, 420, 2], [366, 22, 420, 2, "prepare"], [366, 29, 420, 9, "prepare"], [366, 30, 420, 9], [366, 32, 420, 12], [366, 33, 420, 13], [367, 4, 420, 14], [368, 6, 420, 14, "key"], [368, 9, 420, 14], [369, 6, 420, 14, "get"], [369, 9, 420, 14], [369, 11, 422, 2], [369, 20, 422, 2, "get"], [369, 21, 422, 2], [369, 23, 422, 37], [370, 8, 423, 4], [371, 8, 424, 4], [372, 8, 425, 4], [373, 8, 426, 4], [373, 15, 427, 6], [373, 19, 427, 10], [373, 20, 427, 11, "config"], [373, 26, 427, 17], [373, 27, 427, 18, "runOnJS"], [373, 34, 427, 25], [373, 39, 427, 30], [373, 43, 427, 34], [373, 47, 428, 6], [373, 48, 428, 7], [373, 52, 428, 11], [373, 53, 428, 12, "handlers"], [373, 61, 428, 20], [373, 62, 428, 21, "isWorklet"], [373, 71, 428, 30], [373, 72, 428, 31, "includes"], [373, 80, 428, 39], [373, 81, 428, 40], [373, 86, 428, 45], [373, 87, 428, 46], [373, 91, 429, 6], [373, 92, 429, 7], [373, 96, 429, 7, "isRemoteDebuggingEnabled"], [373, 127, 429, 31], [373, 129, 429, 32], [373, 130, 429, 33], [374, 6, 431, 2], [375, 4, 431, 3], [376, 2, 431, 3], [376, 4, 129, 10, "Gesture"], [376, 11, 129, 17], [377, 2, 129, 17], [377, 6, 434, 22, "ContinousBaseGesture"], [377, 26, 434, 42], [377, 29, 434, 42, "exports"], [377, 36, 434, 42], [377, 37, 434, 42, "ContinousBaseGesture"], [377, 57, 434, 42], [377, 83, 434, 42, "_BaseGesture2"], [377, 96, 434, 42], [378, 4, 434, 42], [378, 13, 434, 42, "ContinousBaseGesture"], [378, 34, 434, 42], [379, 6, 434, 42], [379, 10, 434, 42, "_classCallCheck2"], [379, 26, 434, 42], [379, 27, 434, 42, "default"], [379, 34, 434, 42], [379, 42, 434, 42, "ContinousBaseGesture"], [379, 62, 434, 42], [380, 6, 434, 42], [380, 13, 434, 42, "_callSuper"], [380, 23, 434, 42], [380, 30, 434, 42, "ContinousBaseGesture"], [380, 50, 434, 42], [380, 52, 434, 42, "arguments"], [380, 61, 434, 42], [381, 4, 434, 42], [382, 4, 434, 42], [382, 8, 434, 42, "_inherits2"], [382, 18, 434, 42], [382, 19, 434, 42, "default"], [382, 26, 434, 42], [382, 28, 434, 42, "ContinousBaseGesture"], [382, 48, 434, 42], [382, 50, 434, 42, "_BaseGesture2"], [382, 63, 434, 42], [383, 4, 434, 42], [383, 15, 434, 42, "_createClass2"], [383, 28, 434, 42], [383, 29, 434, 42, "default"], [383, 36, 434, 42], [383, 38, 434, 42, "ContinousBaseGesture"], [383, 58, 434, 42], [384, 6, 434, 42, "key"], [384, 9, 434, 42], [385, 6, 434, 42, "value"], [385, 11, 434, 42], [386, 6, 438, 2], [387, 0, 439, 0], [388, 0, 440, 0], [389, 0, 441, 0], [390, 6, 442, 2], [390, 15, 442, 2, "onUpdate"], [390, 23, 442, 10, "onUpdate"], [390, 24, 442, 11, "callback"], [390, 32, 442, 71], [390, 34, 442, 73], [391, 8, 443, 4], [391, 12, 443, 8], [391, 13, 443, 9, "handlers"], [391, 21, 443, 17], [391, 22, 443, 18, "onUpdate"], [391, 30, 443, 26], [391, 33, 443, 29, "callback"], [391, 41, 443, 37], [392, 8, 444, 4], [392, 12, 444, 8], [392, 13, 444, 9, "handlers"], [392, 21, 444, 17], [392, 22, 444, 18, "isWorklet"], [392, 31, 444, 27], [392, 32, 444, 28, "CALLBACK_TYPE"], [392, 45, 444, 41], [392, 46, 444, 42, "UPDATE"], [392, 52, 444, 48], [392, 53, 444, 49], [392, 56, 444, 52], [392, 60, 444, 56], [392, 61, 444, 57, "isWorklet"], [392, 70, 444, 66], [392, 71, 444, 67, "callback"], [392, 79, 444, 75], [392, 80, 444, 76], [393, 8, 445, 4], [393, 15, 445, 11], [393, 19, 445, 15], [394, 6, 446, 2], [396, 6, 448, 2], [397, 0, 449, 0], [398, 0, 450, 0], [399, 0, 451, 0], [400, 0, 452, 0], [401, 4, 448, 2], [402, 6, 448, 2, "key"], [402, 9, 448, 2], [403, 6, 448, 2, "value"], [403, 11, 448, 2], [403, 13, 453, 2], [403, 22, 453, 2, "onChange"], [403, 30, 453, 10, "onChange"], [403, 31, 454, 4, "callback"], [403, 39, 456, 13], [403, 41, 457, 4], [404, 8, 458, 4], [404, 12, 458, 8], [404, 13, 458, 9, "handlers"], [404, 21, 458, 17], [404, 22, 458, 18, "onChange"], [404, 30, 458, 26], [404, 33, 458, 29, "callback"], [404, 41, 458, 37], [405, 8, 459, 4], [405, 12, 459, 8], [405, 13, 459, 9, "handlers"], [405, 21, 459, 17], [405, 22, 459, 18, "isWorklet"], [405, 31, 459, 27], [405, 32, 459, 28, "CALLBACK_TYPE"], [405, 45, 459, 41], [405, 46, 459, 42, "CHANGE"], [405, 52, 459, 48], [405, 53, 459, 49], [405, 56, 459, 52], [405, 60, 459, 56], [405, 61, 459, 57, "isWorklet"], [405, 70, 459, 66], [405, 71, 459, 67, "callback"], [405, 79, 459, 75], [405, 80, 459, 76], [406, 8, 460, 4], [406, 15, 460, 11], [406, 19, 460, 15], [407, 6, 461, 2], [409, 6, 463, 2], [410, 0, 464, 0], [411, 0, 465, 0], [412, 0, 466, 0], [413, 0, 467, 0], [414, 4, 463, 2], [415, 6, 463, 2, "key"], [415, 9, 463, 2], [416, 6, 463, 2, "value"], [416, 11, 463, 2], [416, 13, 468, 2], [416, 22, 468, 2, "manualActivation"], [416, 38, 468, 18, "manualActivation"], [416, 39, 468, 19, "manualActivation"], [416, 56, 468, 44], [416, 58, 468, 46], [417, 8, 469, 4], [417, 12, 469, 8], [417, 13, 469, 9, "config"], [417, 19, 469, 15], [417, 20, 469, 16, "manualActivation"], [417, 36, 469, 32], [417, 39, 469, 35, "manualActivation"], [417, 56, 469, 51], [418, 8, 470, 4], [418, 15, 470, 11], [418, 19, 470, 15], [419, 6, 471, 2], [420, 4, 471, 3], [421, 2, 471, 3], [421, 4, 437, 10, "BaseGesture"], [421, 15, 437, 21], [422, 0, 437, 21], [422, 3]], "functionMap": {"names": ["<global>", "Gesture", "BaseGesture", "BaseGesture#constructor", "BaseGesture#addDependency", "BaseGesture#withRef", "BaseGesture#isWorklet", "BaseGesture#onBegin", "BaseGesture#onStart", "BaseGesture#onEnd", "BaseGesture#onFinalize", "BaseGesture#onTouchesDown", "BaseGesture#onTouchesMove", "BaseGesture#onTouchesUp", "BaseGesture#onTouchesCancelled", "BaseGesture#enabled", "BaseGesture#shouldCancelWhenOutside", "BaseGesture#hitSlop", "BaseGesture#activeCursor", "BaseGesture#mouseButton", "BaseGesture#runOnJS", "BaseGesture#simultaneousWithExternalGesture", "BaseGesture#requireExternalGestureToFail", "BaseGesture#blocksExternalGesture", "BaseGesture#withTestId", "BaseGesture#cancelsTouchesInView", "BaseGesture#initialize", "BaseGesture#toGestureArray", "BaseGesture#prepare", "BaseGesture#get__shouldUseReanimated", "ContinousBaseGesture", "ContinousBaseGesture#onUpdate", "ContinousBaseGesture#onChange", "ContinousBaseGesture#manualActivation"], "mappings": "AAA;OCyG;CDkB;OEG;ECa;GDU;EEE;GFQ;EGM;GHG;EIG;GJG;EKO;GLI;EMM;GNI;EOO;GPU;EQM;GRU;ESM;GTO;EUM;GVO;EWM;GXO;EYM;GZO;EaO;GbG;EcO;GdG;EeQ;GfG;EgBQ;GhBG;EiBU;GjBG;EkBQ;GlBG;EmBO;GnBK;EoBO;GpBK;EqBO;GrBK;EsBM;GtBG;EuBQ;GvBG;EwBE;GxBQ;EyBE;GzBE;E0BG,Y1B;E2BE;G3BS;CFC;O8BE;ECQ;GDI;EEO;GFQ;EGO;GHG;C9BC"}}, "type": "js/module"}]}