{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 27}, "end": {"line": 8, "column": 22, "index": 165}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.StatusBar = StatusBar;\n  exports.setStatusBarBackgroundColor = setStatusBarBackgroundColor;\n  exports.setStatusBarHidden = setStatusBarHidden;\n  exports.setStatusBarNetworkActivityIndicatorVisible = setStatusBarNetworkActivityIndicatorVisible;\n  exports.setStatusBarStyle = setStatusBarStyle;\n  exports.setStatusBarTranslucent = setStatusBarTranslucent;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _jsxDevRuntime = require(_dependencyMap[4], \"react/jsx-dev-runtime\");\n  var _excluded = [\"style\", \"hideTransitionAnimation\", \"translucent\", \"backgroundColor\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\expo-status-bar\\\\src\\\\NativeStatusBarWrapper.tsx\";\n  /**\n   * A component that allows you to configure your status bar without directly calling imperative\n   * methods like `setBarStyle`.\n   *\n   * You will likely have multiple `StatusBar` components mounted in the same app at the same time.\n   * For example, if you have multiple screens in your app, you may end up using one per screen.\n   * The props of each `StatusBar` component will be merged in the order that they were mounted.\n   * This component is built on top of the [StatusBar](https://reactnative.dev/docs/statusbar)\n   * component exported from React Native, and it provides defaults that work better for Expo users.\n   */\n  function StatusBar(_ref) {\n    var style = _ref.style,\n      hideTransitionAnimation = _ref.hideTransitionAnimation,\n      _ref$translucent = _ref.translucent,\n      translucent = _ref$translucent === void 0 ? true : _ref$translucent,\n      backgroundColorProp = _ref.backgroundColor,\n      props = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    // Pick appropriate default value depending on current theme, so if we are\n    // locked to light mode we don't end up with a light status bar\n    var colorScheme = (0, _reactNative.useColorScheme)();\n    var barStyle = _react.default.useMemo(() => styleToBarStyle(style, colorScheme), [style, colorScheme]);\n\n    // If translucent and no backgroundColor is provided, then use transparent\n    // background\n    var backgroundColor = backgroundColorProp;\n    if (translucent && !backgroundColor) {\n      backgroundColor = 'transparent';\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.StatusBar, {\n      ...props,\n      translucent: translucent,\n      barStyle: barStyle,\n      backgroundColor: backgroundColor,\n      showHideTransition: hideTransitionAnimation === 'none' ? undefined : hideTransitionAnimation\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 5\n    }, this);\n  }\n\n  // @needsAudit\n  /**\n   * Set the bar style of the status bar.\n   * @param style The color of the status bar text.\n   * @param animated If the transition should be animated.\n   */\n  function setStatusBarStyle(style, animated) {\n    _reactNative.StatusBar.setBarStyle(styleToBarStyle(style), animated);\n  }\n\n  // @needsAudit\n  /**\n   * Toggle visibility of the status bar.\n   * @param hidden If the status bar should be hidden.\n   * @param animation Animation to use when toggling hidden, defaults to `'none'`.\n   */\n  function setStatusBarHidden(hidden, animation) {\n    _reactNative.StatusBar.setHidden(hidden, animation);\n  }\n\n  // @needsAudit\n  /**\n   * Set the background color of the status bar.\n   * @param backgroundColor The background color of the status bar.\n   * @param animated `true` to animate the background color change, `false` to change immediately.\n   * @platform android\n   */\n  function setStatusBarBackgroundColor(backgroundColor, animated) {\n    _reactNative.StatusBar.setBackgroundColor(backgroundColor, animated);\n  }\n\n  // @needsAudit\n  /**\n   * Toggle visibility of the network activity indicator.\n   * @param visible If the network activity indicator should be visible.\n   * @platform ios\n   */\n  function setStatusBarNetworkActivityIndicatorVisible(visible) {\n    _reactNative.StatusBar.setNetworkActivityIndicatorVisible(visible);\n  }\n\n  // @needsAudit\n  /**\n   * Set the translucency of the status bar.\n   * @param translucent Whether the app can draw under the status bar. When `true`, content will be\n   * rendered under the status bar. This is always `true` on iOS and cannot be changed.\n   * @platform android\n   */\n  function setStatusBarTranslucent(translucent) {\n    _reactNative.StatusBar.setTranslucent(translucent);\n  }\n  function styleToBarStyle() {\n    var style = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'auto';\n    var colorScheme = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _reactNative.Appearance?.getColorScheme() ?? 'light';\n    if (!colorScheme) {\n      colorScheme = 'light';\n    }\n    var resolvedStyle = style;\n    if (style === 'auto') {\n      resolvedStyle = colorScheme === 'light' ? 'dark' : 'light';\n    } else if (style === 'inverted') {\n      resolvedStyle = colorScheme === 'light' ? 'light' : 'dark';\n    }\n    return resolvedStyle === 'light' ? 'light-content' : 'dark-content';\n  }\n});", "lineCount": 124, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_react"], [13, 12, 1, 0], [13, 15, 1, 0, "_interopRequireDefault"], [13, 37, 1, 0], [13, 38, 1, 0, "require"], [13, 45, 1, 0], [13, 46, 1, 0, "_dependencyMap"], [13, 60, 1, 0], [14, 2, 2, 0], [14, 6, 2, 0, "_reactNative"], [14, 18, 2, 0], [14, 21, 2, 0, "require"], [14, 28, 2, 0], [14, 29, 2, 0, "_dependencyMap"], [14, 43, 2, 0], [15, 2, 8, 22], [15, 6, 8, 22, "_jsxDevRuntime"], [15, 20, 8, 22], [15, 23, 8, 22, "require"], [15, 30, 8, 22], [15, 31, 8, 22, "_dependencyMap"], [15, 45, 8, 22], [16, 2, 8, 22], [16, 6, 8, 22, "_excluded"], [16, 15, 8, 22], [17, 2, 8, 22], [17, 6, 8, 22, "_jsxFileName"], [17, 18, 8, 22], [18, 2, 12, 0], [19, 0, 13, 0], [20, 0, 14, 0], [21, 0, 15, 0], [22, 0, 16, 0], [23, 0, 17, 0], [24, 0, 18, 0], [25, 0, 19, 0], [26, 0, 20, 0], [27, 0, 21, 0], [28, 2, 22, 7], [28, 11, 22, 16, "StatusBar"], [28, 20, 22, 25, "StatusBar"], [28, 21, 22, 25, "_ref"], [28, 25, 22, 25], [28, 27, 28, 19], [29, 4, 28, 19], [29, 8, 23, 2, "style"], [29, 13, 23, 7], [29, 16, 23, 7, "_ref"], [29, 20, 23, 7], [29, 21, 23, 2, "style"], [29, 26, 23, 7], [30, 6, 24, 2, "hideTransitionAnimation"], [30, 29, 24, 25], [30, 32, 24, 25, "_ref"], [30, 36, 24, 25], [30, 37, 24, 2, "hideTransitionAnimation"], [30, 60, 24, 25], [31, 6, 24, 25, "_ref$translucent"], [31, 22, 24, 25], [31, 25, 24, 25, "_ref"], [31, 29, 24, 25], [31, 30, 25, 2, "translucent"], [31, 41, 25, 13], [32, 6, 25, 2, "translucent"], [32, 17, 25, 13], [32, 20, 25, 13, "_ref$translucent"], [32, 36, 25, 13], [32, 50, 25, 16], [32, 54, 25, 20], [32, 57, 25, 20, "_ref$translucent"], [32, 73, 25, 20], [33, 6, 26, 19, "backgroundColorProp"], [33, 25, 26, 38], [33, 28, 26, 38, "_ref"], [33, 32, 26, 38], [33, 33, 26, 2, "backgroundColor"], [33, 48, 26, 17], [34, 6, 27, 5, "props"], [34, 11, 27, 10], [34, 18, 27, 10, "_objectWithoutProperties2"], [34, 43, 27, 10], [34, 44, 27, 10, "default"], [34, 51, 27, 10], [34, 53, 27, 10, "_ref"], [34, 57, 27, 10], [34, 59, 27, 10, "_excluded"], [34, 68, 27, 10], [35, 4, 29, 2], [36, 4, 30, 2], [37, 4, 31, 2], [37, 8, 31, 8, "colorScheme"], [37, 19, 31, 19], [37, 22, 31, 22], [37, 26, 31, 22, "useColorScheme"], [37, 53, 31, 36], [37, 55, 31, 37], [37, 56, 31, 38], [38, 4, 32, 2], [38, 8, 32, 8, "barStyle"], [38, 16, 32, 16], [38, 19, 32, 19, "React"], [38, 33, 32, 24], [38, 34, 32, 25, "useMemo"], [38, 41, 32, 32], [38, 42, 32, 33], [38, 48, 32, 39, "styleToBarStyle"], [38, 63, 32, 54], [38, 64, 32, 55, "style"], [38, 69, 32, 60], [38, 71, 32, 62, "colorScheme"], [38, 82, 32, 73], [38, 83, 32, 74], [38, 85, 32, 76], [38, 86, 32, 77, "style"], [38, 91, 32, 82], [38, 93, 32, 84, "colorScheme"], [38, 104, 32, 95], [38, 105, 32, 96], [38, 106, 32, 97], [40, 4, 34, 2], [41, 4, 35, 2], [42, 4, 36, 2], [42, 8, 36, 6, "backgroundColor"], [42, 23, 36, 21], [42, 26, 36, 24, "backgroundColorProp"], [42, 45, 36, 43], [43, 4, 37, 2], [43, 8, 37, 6, "translucent"], [43, 19, 37, 17], [43, 23, 37, 21], [43, 24, 37, 22, "backgroundColor"], [43, 39, 37, 37], [43, 41, 37, 39], [44, 6, 38, 4, "backgroundColor"], [44, 21, 38, 19], [44, 24, 38, 22], [44, 37, 38, 35], [45, 4, 39, 2], [46, 4, 41, 2], [46, 24, 42, 4], [46, 28, 42, 4, "_jsxDevRuntime"], [46, 42, 42, 4], [46, 43, 42, 4, "jsxDEV"], [46, 49, 42, 4], [46, 51, 42, 5, "_reactNative"], [46, 63, 42, 5], [46, 64, 42, 5, "StatusBar"], [46, 73, 42, 20], [47, 6, 42, 20], [47, 9, 43, 10, "props"], [47, 14, 43, 15], [48, 6, 44, 6, "translucent"], [48, 17, 44, 17], [48, 19, 44, 19, "translucent"], [48, 30, 44, 31], [49, 6, 45, 6, "barStyle"], [49, 14, 45, 14], [49, 16, 45, 16, "barStyle"], [49, 24, 45, 25], [50, 6, 46, 6, "backgroundColor"], [50, 21, 46, 21], [50, 23, 46, 23, "backgroundColor"], [50, 38, 46, 39], [51, 6, 47, 6, "showHideTransition"], [51, 24, 47, 24], [51, 26, 47, 26, "hideTransitionAnimation"], [51, 49, 47, 49], [51, 54, 47, 54], [51, 60, 47, 60], [51, 63, 47, 63, "undefined"], [51, 72, 47, 72], [51, 75, 47, 75, "hideTransitionAnimation"], [52, 4, 47, 99], [53, 6, 47, 99, "fileName"], [53, 14, 47, 99], [53, 16, 47, 99, "_jsxFileName"], [53, 28, 47, 99], [54, 6, 47, 99, "lineNumber"], [54, 16, 47, 99], [55, 6, 47, 99, "columnNumber"], [55, 18, 47, 99], [56, 4, 47, 99], [56, 11, 48, 5], [56, 12, 48, 6], [57, 2, 50, 0], [59, 2, 52, 0], [60, 2, 53, 0], [61, 0, 54, 0], [62, 0, 55, 0], [63, 0, 56, 0], [64, 0, 57, 0], [65, 2, 58, 7], [65, 11, 58, 16, "setStatusBarStyle"], [65, 28, 58, 33, "setStatusBarStyle"], [65, 29, 58, 34, "style"], [65, 34, 58, 55], [65, 36, 58, 57, "animated"], [65, 44, 58, 75], [65, 46, 58, 77], [66, 4, 59, 2, "NativeStatusBar"], [66, 26, 59, 17], [66, 27, 59, 18, "setBarStyle"], [66, 38, 59, 29], [66, 39, 59, 30, "styleToBarStyle"], [66, 54, 59, 45], [66, 55, 59, 46, "style"], [66, 60, 59, 51], [66, 61, 59, 52], [66, 63, 59, 54, "animated"], [66, 71, 59, 62], [66, 72, 59, 63], [67, 2, 60, 0], [69, 2, 62, 0], [70, 2, 63, 0], [71, 0, 64, 0], [72, 0, 65, 0], [73, 0, 66, 0], [74, 0, 67, 0], [75, 2, 68, 7], [75, 11, 68, 16, "setStatusBarHidden"], [75, 29, 68, 34, "setStatusBarHidden"], [75, 30, 68, 35, "hidden"], [75, 36, 68, 50], [75, 38, 68, 52, "animation"], [75, 47, 68, 82], [75, 49, 68, 84], [76, 4, 69, 2, "NativeStatusBar"], [76, 26, 69, 17], [76, 27, 69, 18, "setHidden"], [76, 36, 69, 27], [76, 37, 69, 28, "hidden"], [76, 43, 69, 34], [76, 45, 69, 36, "animation"], [76, 54, 69, 45], [76, 55, 69, 46], [77, 2, 70, 0], [79, 2, 72, 0], [80, 2, 73, 0], [81, 0, 74, 0], [82, 0, 75, 0], [83, 0, 76, 0], [84, 0, 77, 0], [85, 0, 78, 0], [86, 2, 79, 7], [86, 11, 79, 16, "setStatusBarBackgroundColor"], [86, 38, 79, 43, "setStatusBarBackgroundColor"], [86, 39, 79, 44, "backgroundColor"], [86, 54, 79, 71], [86, 56, 79, 73, "animated"], [86, 64, 79, 91], [86, 66, 79, 93], [87, 4, 80, 2, "NativeStatusBar"], [87, 26, 80, 17], [87, 27, 80, 18, "setBackgroundColor"], [87, 45, 80, 36], [87, 46, 80, 37, "backgroundColor"], [87, 61, 80, 52], [87, 63, 80, 54, "animated"], [87, 71, 80, 62], [87, 72, 80, 63], [88, 2, 81, 0], [90, 2, 83, 0], [91, 2, 84, 0], [92, 0, 85, 0], [93, 0, 86, 0], [94, 0, 87, 0], [95, 0, 88, 0], [96, 2, 89, 7], [96, 11, 89, 16, "setStatusBarNetworkActivityIndicatorVisible"], [96, 54, 89, 59, "setStatusBarNetworkActivityIndicatorVisible"], [96, 55, 89, 60, "visible"], [96, 62, 89, 76], [96, 64, 89, 78], [97, 4, 90, 2, "NativeStatusBar"], [97, 26, 90, 17], [97, 27, 90, 18, "setNetworkActivityIndicatorVisible"], [97, 61, 90, 52], [97, 62, 90, 53, "visible"], [97, 69, 90, 60], [97, 70, 90, 61], [98, 2, 91, 0], [100, 2, 93, 0], [101, 2, 94, 0], [102, 0, 95, 0], [103, 0, 96, 0], [104, 0, 97, 0], [105, 0, 98, 0], [106, 0, 99, 0], [107, 2, 100, 7], [107, 11, 100, 16, "setStatusBarTranslucent"], [107, 34, 100, 39, "setStatusBarTranslucent"], [107, 35, 100, 40, "translucent"], [107, 46, 100, 60], [107, 48, 100, 62], [108, 4, 101, 2, "NativeStatusBar"], [108, 26, 101, 17], [108, 27, 101, 18, "setTranslucent"], [108, 41, 101, 32], [108, 42, 101, 33, "translucent"], [108, 53, 101, 44], [108, 54, 101, 45], [109, 2, 102, 0], [110, 2, 104, 0], [110, 11, 104, 9, "styleToBarStyle"], [110, 26, 104, 24, "styleToBarStyle"], [110, 27, 104, 24], [110, 29, 107, 36], [111, 4, 107, 36], [111, 8, 105, 2, "style"], [111, 13, 105, 23], [111, 16, 105, 23, "arguments"], [111, 25, 105, 23], [111, 26, 105, 23, "length"], [111, 32, 105, 23], [111, 40, 105, 23, "arguments"], [111, 49, 105, 23], [111, 57, 105, 23, "undefined"], [111, 66, 105, 23], [111, 69, 105, 23, "arguments"], [111, 78, 105, 23], [111, 84, 105, 26], [111, 90, 105, 32], [112, 4, 105, 32], [112, 8, 106, 2, "colorScheme"], [112, 19, 106, 30], [112, 22, 106, 30, "arguments"], [112, 31, 106, 30], [112, 32, 106, 30, "length"], [112, 38, 106, 30], [112, 46, 106, 30, "arguments"], [112, 55, 106, 30], [112, 63, 106, 30, "undefined"], [112, 72, 106, 30], [112, 75, 106, 30, "arguments"], [112, 84, 106, 30], [112, 90, 106, 33, "Appearance"], [112, 113, 106, 43], [112, 115, 106, 45, "getColorScheme"], [112, 129, 106, 59], [112, 130, 106, 60], [112, 131, 106, 61], [112, 135, 106, 65], [112, 142, 106, 72], [113, 4, 108, 2], [113, 8, 108, 6], [113, 9, 108, 7, "colorScheme"], [113, 20, 108, 18], [113, 22, 108, 20], [114, 6, 109, 4, "colorScheme"], [114, 17, 109, 15], [114, 20, 109, 18], [114, 27, 109, 25], [115, 4, 110, 2], [116, 4, 112, 2], [116, 8, 112, 6, "resolvedStyle"], [116, 21, 112, 19], [116, 24, 112, 22, "style"], [116, 29, 112, 27], [117, 4, 113, 2], [117, 8, 113, 6, "style"], [117, 13, 113, 11], [117, 18, 113, 16], [117, 24, 113, 22], [117, 26, 113, 24], [118, 6, 114, 4, "resolvedStyle"], [118, 19, 114, 17], [118, 22, 114, 20, "colorScheme"], [118, 33, 114, 31], [118, 38, 114, 36], [118, 45, 114, 43], [118, 48, 114, 46], [118, 54, 114, 52], [118, 57, 114, 55], [118, 64, 114, 62], [119, 4, 115, 2], [119, 5, 115, 3], [119, 11, 115, 9], [119, 15, 115, 13, "style"], [119, 20, 115, 18], [119, 25, 115, 23], [119, 35, 115, 33], [119, 37, 115, 35], [120, 6, 116, 4, "resolvedStyle"], [120, 19, 116, 17], [120, 22, 116, 20, "colorScheme"], [120, 33, 116, 31], [120, 38, 116, 36], [120, 45, 116, 43], [120, 48, 116, 46], [120, 55, 116, 53], [120, 58, 116, 56], [120, 64, 116, 62], [121, 4, 117, 2], [122, 4, 119, 2], [122, 11, 119, 9, "resolvedStyle"], [122, 24, 119, 22], [122, 29, 119, 27], [122, 36, 119, 34], [122, 39, 119, 37], [122, 54, 119, 52], [122, 57, 119, 55], [122, 71, 119, 69], [123, 2, 120, 0], [124, 0, 120, 1], [124, 3]], "functionMap": {"names": ["<global>", "StatusBar", "React.useMemo$argument_0", "setStatusBarStyle", "setStatusBarHidden", "setStatusBarBackgroundColor", "setStatusBarNetworkActivityIndicatorVisible", "setStatusBarTranslucent", "styleToBarStyle"], "mappings": "AAA;OCqB;iCCU,yCD;CDkB;OGQ;CHE;OIQ;CJE;OKS;CLE;OMQ;CNE;OOS;CPE;AQE;CRgB"}}, "type": "js/module"}]}