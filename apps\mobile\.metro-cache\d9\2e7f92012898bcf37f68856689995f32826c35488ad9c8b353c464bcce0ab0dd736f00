{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./Expo.fx", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 19, "index": 19}}], "key": "roHI1+xdYy9UiHSFiaoMAhWogZQ=", "exportNames": ["*"]}}, {"name": "./errors/ExpoErrorManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 21}, "end": {"line": 3, "column": 65, "index": 86}}], "key": "esKyY/+Md43XSEpLGnyGTCXAU2g=", "exportNames": ["*"]}}, {"name": "./launch/registerRootComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 87}, "end": {"line": 4, "column": 82, "index": 169}}], "key": "AnyPTg3IKvmso3eRKsVmQwQ4jOM=", "exportNames": ["*"]}}, {"name": "./environment/ExpoGo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 171}, "end": {"line": 6, "column": 81, "index": 252}}], "key": "F+MwpkEnpJkgvAycBfX5pcdeFTU=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 254}, "end": {"line": 21, "column": 27, "index": 525}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "./hooks/useEvent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 714}, "end": {"line": 30, "column": 62, "index": 776}}], "key": "P8Sx0L7BcrMY9mx9orjM6gxfKjk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"EventEmitter\", {\n    enumerable: true,\n    get: function () {\n      return _expoModulesCore.EventEmitter;\n    }\n  });\n  Object.defineProperty(exports, \"NativeModule\", {\n    enumerable: true,\n    get: function () {\n      return _expoModulesCore.NativeModule;\n    }\n  });\n  Object.defineProperty(exports, \"SharedObject\", {\n    enumerable: true,\n    get: function () {\n      return _expoModulesCore.SharedObject;\n    }\n  });\n  Object.defineProperty(exports, \"SharedRef\", {\n    enumerable: true,\n    get: function () {\n      return _expoModulesCore.SharedRef;\n    }\n  });\n  Object.defineProperty(exports, \"disableErrorHandling\", {\n    enumerable: true,\n    get: function () {\n      return _ExpoErrorManager.disableErrorHandling;\n    }\n  });\n  Object.defineProperty(exports, \"getExpoGoProjectConfig\", {\n    enumerable: true,\n    get: function () {\n      return _ExpoGo.getExpoGoProjectConfig;\n    }\n  });\n  Object.defineProperty(exports, \"isRunningInExpoGo\", {\n    enumerable: true,\n    get: function () {\n      return _ExpoGo.isRunningInExpoGo;\n    }\n  });\n  Object.defineProperty(exports, \"registerRootComponent\", {\n    enumerable: true,\n    get: function () {\n      return _registerRootComponent.default;\n    }\n  });\n  Object.defineProperty(exports, \"registerWebModule\", {\n    enumerable: true,\n    get: function () {\n      return _expoModulesCore.registerWebModule;\n    }\n  });\n  Object.defineProperty(exports, \"reloadAppAsync\", {\n    enumerable: true,\n    get: function () {\n      return _expoModulesCore.reloadAppAsync;\n    }\n  });\n  Object.defineProperty(exports, \"requireNativeModule\", {\n    enumerable: true,\n    get: function () {\n      return _expoModulesCore.requireNativeModule;\n    }\n  });\n  Object.defineProperty(exports, \"requireNativeView\", {\n    enumerable: true,\n    get: function () {\n      return _expoModulesCore.requireNativeViewManager;\n    }\n  });\n  Object.defineProperty(exports, \"requireOptionalNativeModule\", {\n    enumerable: true,\n    get: function () {\n      return _expoModulesCore.requireOptionalNativeModule;\n    }\n  });\n  Object.defineProperty(exports, \"useEvent\", {\n    enumerable: true,\n    get: function () {\n      return _useEvent.useEvent;\n    }\n  });\n  Object.defineProperty(exports, \"useEventListener\", {\n    enumerable: true,\n    get: function () {\n      return _useEvent.useEventListener;\n    }\n  });\n  require(_dependencyMap[1], \"./Expo.fx\");\n  var _ExpoErrorManager = require(_dependencyMap[2], \"./errors/ExpoErrorManager\");\n  var _registerRootComponent = _interopRequireDefault(require(_dependencyMap[3], \"./launch/registerRootComponent\"));\n  var _ExpoGo = require(_dependencyMap[4], \"./environment/ExpoGo\");\n  var _expoModulesCore = require(_dependencyMap[5], \"expo-modules-core\");\n  var _useEvent = require(_dependencyMap[6], \"./hooks/useEvent\");\n});", "lineCount": 102, "map": [[96, 2, 1, 0, "require"], [96, 9, 1, 0], [96, 10, 1, 0, "_dependencyMap"], [96, 24, 1, 0], [97, 2, 3, 0], [97, 6, 3, 0, "_ExpoErrorManager"], [97, 23, 3, 0], [97, 26, 3, 0, "require"], [97, 33, 3, 0], [97, 34, 3, 0, "_dependencyMap"], [97, 48, 3, 0], [98, 2, 4, 0], [98, 6, 4, 0, "_registerRootComponent"], [98, 28, 4, 0], [98, 31, 4, 0, "_interopRequireDefault"], [98, 53, 4, 0], [98, 54, 4, 0, "require"], [98, 61, 4, 0], [98, 62, 4, 0, "_dependencyMap"], [98, 76, 4, 0], [99, 2, 6, 0], [99, 6, 6, 0, "_ExpoGo"], [99, 13, 6, 0], [99, 16, 6, 0, "require"], [99, 23, 6, 0], [99, 24, 6, 0, "_dependencyMap"], [99, 38, 6, 0], [100, 2, 8, 0], [100, 6, 8, 0, "_expoModulesCore"], [100, 22, 8, 0], [100, 25, 8, 0, "require"], [100, 32, 8, 0], [100, 33, 8, 0, "_dependencyMap"], [100, 47, 8, 0], [101, 2, 30, 0], [101, 6, 30, 0, "_useEvent"], [101, 15, 30, 0], [101, 18, 30, 0, "require"], [101, 25, 30, 0], [101, 26, 30, 0, "_dependencyMap"], [101, 40, 30, 0], [102, 0, 30, 62], [102, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}