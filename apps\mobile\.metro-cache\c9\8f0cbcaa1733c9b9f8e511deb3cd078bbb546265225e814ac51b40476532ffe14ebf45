{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "../../src/private/webapis/dom/events/Event", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 63}}], "key": "tBZi+S29HkRH7GXhvTkyJOiNv/0=", "exportNames": ["*"]}}, {"name": "../../src/private/webapis/dom/events/EventHandlerAttributes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 19, "column": 69}}], "key": "q4hoKVUYn8W65ElaXt4ibNuavMg=", "exportNames": ["*"]}}, {"name": "../../src/private/webapis/dom/events/EventTarget", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 75}}], "key": "5BtRqEWbP5KAODQg/nNtprX4z0Q=", "exportNames": ["*"]}}, {"name": "../../src/private/webapis/html/events/MessageEvent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 78}}], "key": "saemSQfESBwy6/t+YV750ejLw50=", "exportNames": ["*"]}}, {"name": "../../src/private/webapis/websockets/events/CloseEvent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 80}}], "key": "3hZwHQLQqr5AtfYcTzZ4XD5wsUE=", "exportNames": ["*"]}}, {"name": "../Blob/Blob", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 32}}], "key": "QJ9YUgVVo3YV103ML0FFHJuPYrA=", "exportNames": ["*"]}}, {"name": "../Blob/BlobManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 46}}], "key": "FtXaCM40L4ZvhVGzYeIEcCA7J8Y=", "exportNames": ["*"]}}, {"name": "../EventEmitter/NativeEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 68}}], "key": "Jk6GODPsD+OS/XTex7hK2MSfvW0=", "exportNames": ["*"]}}, {"name": "../Utilities/binaryToBase64", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 57}}], "key": "lSiEKNqE2DL2f1OV5nuMLekqR6M=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 45}}], "key": "WyqnBhspP5BAR0xvCwqfBv/v4uA=", "exportNames": ["*"]}}, {"name": "./NativeWebSocketModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 60}}], "key": "AGB2JKXOMxtGwGWQCZlW2uis6T8=", "exportNames": ["*"]}}, {"name": "base64-js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 31}}], "key": "9arPc0KuVPvzcEfvnWXidnN1Ujk=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _Event = _interopRequireDefault(require(_dependencyMap[7], \"../../src/private/webapis/dom/events/Event\"));\n  var _EventHandlerAttributes = require(_dependencyMap[8], \"../../src/private/webapis/dom/events/EventHandlerAttributes\");\n  var _EventTarget2 = _interopRequireDefault(require(_dependencyMap[9], \"../../src/private/webapis/dom/events/EventTarget\"));\n  var _MessageEvent = _interopRequireDefault(require(_dependencyMap[10], \"../../src/private/webapis/html/events/MessageEvent\"));\n  var _CloseEvent = _interopRequireDefault(require(_dependencyMap[11], \"../../src/private/webapis/websockets/events/CloseEvent\"));\n  var _Blob = _interopRequireDefault(require(_dependencyMap[12], \"../Blob/Blob\"));\n  var _BlobManager = _interopRequireDefault(require(_dependencyMap[13], \"../Blob/BlobManager\"));\n  var _NativeEventEmitter = _interopRequireDefault(require(_dependencyMap[14], \"../EventEmitter/NativeEventEmitter\"));\n  var _binaryToBase = _interopRequireDefault(require(_dependencyMap[15], \"../Utilities/binaryToBase64\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[16], \"../Utilities/Platform\"));\n  var _NativeWebSocketModule = _interopRequireDefault(require(_dependencyMap[17], \"./NativeWebSocketModule\"));\n  var _base64Js = _interopRequireDefault(require(_dependencyMap[18], \"base64-js\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[19], \"invariant\"));\n  var _excluded = [\"headers\"];\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var CONNECTING = 0;\n  var OPEN = 1;\n  var CLOSING = 2;\n  var CLOSED = 3;\n  var CLOSE_NORMAL = 1000;\n  var CLOSE_ABNORMAL = 1006;\n  var nextWebSocketId = 0;\n  var WebSocket = /*#__PURE__*/function (_EventTarget) {\n    function WebSocket(url, protocols, options) {\n      var _this;\n      (0, _classCallCheck2.default)(this, WebSocket);\n      _this = _callSuper(this, WebSocket);\n      _this.CONNECTING = CONNECTING;\n      _this.OPEN = OPEN;\n      _this.CLOSING = CLOSING;\n      _this.CLOSED = CLOSED;\n      _this.readyState = CONNECTING;\n      _this.url = url;\n      if (typeof protocols === 'string') {\n        protocols = [protocols];\n      }\n      var _ref = options || {},\n        _ref$headers = _ref.headers,\n        headers = _ref$headers === void 0 ? {} : _ref$headers,\n        unrecognized = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n      if (unrecognized && typeof unrecognized.origin === 'string') {\n        console.warn('Specifying `origin` as a WebSocket connection option is deprecated. Include it under `headers` instead.');\n        headers.origin = unrecognized.origin;\n        delete unrecognized.origin;\n      }\n      if (Object.keys(unrecognized).length > 0) {\n        console.warn('Unrecognized WebSocket connection option(s) `' + Object.keys(unrecognized).join('`, `') + '`. ' + 'Did you mean to put these under `headers`?');\n      }\n      if (!Array.isArray(protocols)) {\n        protocols = null;\n      }\n      _this._eventEmitter = new _NativeEventEmitter.default(_Platform.default.OS !== 'ios' ? null : _NativeWebSocketModule.default);\n      _this._socketId = nextWebSocketId++;\n      _this._registerEvents();\n      _NativeWebSocketModule.default.connect(url, protocols, {\n        headers\n      }, _this._socketId);\n      return _this;\n    }\n    (0, _inherits2.default)(WebSocket, _EventTarget);\n    return (0, _createClass2.default)(WebSocket, [{\n      key: \"binaryType\",\n      get: function () {\n        return this._binaryType;\n      },\n      set: function (binaryType) {\n        if (binaryType !== 'blob' && binaryType !== 'arraybuffer') {\n          throw new Error(\"binaryType must be either 'blob' or 'arraybuffer'\");\n        }\n        if (this._binaryType === 'blob' || binaryType === 'blob') {\n          (0, _invariant.default)(_BlobManager.default.isAvailable, 'Native module BlobModule is required for blob support');\n          if (binaryType === 'blob') {\n            _BlobManager.default.addWebSocketHandler(this._socketId);\n          } else {\n            _BlobManager.default.removeWebSocketHandler(this._socketId);\n          }\n        }\n        this._binaryType = binaryType;\n      }\n    }, {\n      key: \"close\",\n      value: function close(code, reason) {\n        if (this.readyState === this.CLOSING || this.readyState === this.CLOSED) {\n          return;\n        }\n        this.readyState = this.CLOSING;\n        this._close(code, reason);\n      }\n    }, {\n      key: \"send\",\n      value: function send(data) {\n        if (this.readyState === this.CONNECTING) {\n          throw new Error('INVALID_STATE_ERR');\n        }\n        if (data instanceof _Blob.default) {\n          (0, _invariant.default)(_BlobManager.default.isAvailable, 'Native module BlobModule is required for blob support');\n          _BlobManager.default.sendOverSocket(data, this._socketId);\n          return;\n        }\n        if (typeof data === 'string') {\n          _NativeWebSocketModule.default.send(data, this._socketId);\n          return;\n        }\n        if (data instanceof ArrayBuffer || ArrayBuffer.isView(data)) {\n          _NativeWebSocketModule.default.sendBinary((0, _binaryToBase.default)(data), this._socketId);\n          return;\n        }\n        throw new Error('Unsupported data type');\n      }\n    }, {\n      key: \"ping\",\n      value: function ping() {\n        if (this.readyState === this.CONNECTING) {\n          throw new Error('INVALID_STATE_ERR');\n        }\n        _NativeWebSocketModule.default.ping(this._socketId);\n      }\n    }, {\n      key: \"_close\",\n      value: function _close(code, reason) {\n        var statusCode = typeof code === 'number' ? code : CLOSE_NORMAL;\n        var closeReason = typeof reason === 'string' ? reason : '';\n        _NativeWebSocketModule.default.close(statusCode, closeReason, this._socketId);\n        if (_BlobManager.default.isAvailable && this._binaryType === 'blob') {\n          _BlobManager.default.removeWebSocketHandler(this._socketId);\n        }\n      }\n    }, {\n      key: \"_unregisterEvents\",\n      value: function _unregisterEvents() {\n        this._subscriptions.forEach(e => e.remove());\n        this._subscriptions = [];\n      }\n    }, {\n      key: \"_registerEvents\",\n      value: function _registerEvents() {\n        this._subscriptions = [this._eventEmitter.addListener('websocketMessage', ev => {\n          if (ev.id !== this._socketId) {\n            return;\n          }\n          var data = ev.data;\n          switch (ev.type) {\n            case 'binary':\n              data = _base64Js.default.toByteArray(ev.data).buffer;\n              break;\n            case 'blob':\n              data = _BlobManager.default.createFromOptions(ev.data);\n              break;\n          }\n          this.dispatchEvent(new _MessageEvent.default('message', {\n            data\n          }));\n        }), this._eventEmitter.addListener('websocketOpen', ev => {\n          if (ev.id !== this._socketId) {\n            return;\n          }\n          this.readyState = this.OPEN;\n          this.protocol = ev.protocol;\n          this.dispatchEvent(new _Event.default('open'));\n        }), this._eventEmitter.addListener('websocketClosed', ev => {\n          if (ev.id !== this._socketId) {\n            return;\n          }\n          this.readyState = this.CLOSED;\n          this.dispatchEvent(new _CloseEvent.default('close', {\n            code: ev.code,\n            reason: ev.reason\n          }));\n          this._unregisterEvents();\n          this.close();\n        }), this._eventEmitter.addListener('websocketFailed', ev => {\n          if (ev.id !== this._socketId) {\n            return;\n          }\n          this.readyState = this.CLOSED;\n          this.dispatchEvent(new _Event.default('error'));\n          this.dispatchEvent(new _CloseEvent.default('close', {\n            code: CLOSE_ABNORMAL,\n            reason: ev.message\n          }));\n          this._unregisterEvents();\n          this.close();\n        })];\n      }\n    }, {\n      key: \"onclose\",\n      get: function () {\n        return (0, _EventHandlerAttributes.getEventHandlerAttribute)(this, 'close');\n      },\n      set: function (listener) {\n        (0, _EventHandlerAttributes.setEventHandlerAttribute)(this, 'close', listener);\n      }\n    }, {\n      key: \"onerror\",\n      get: function () {\n        return (0, _EventHandlerAttributes.getEventHandlerAttribute)(this, 'error');\n      },\n      set: function (listener) {\n        (0, _EventHandlerAttributes.setEventHandlerAttribute)(this, 'error', listener);\n      }\n    }, {\n      key: \"onmessage\",\n      get: function () {\n        return (0, _EventHandlerAttributes.getEventHandlerAttribute)(this, 'message');\n      },\n      set: function (listener) {\n        (0, _EventHandlerAttributes.setEventHandlerAttribute)(this, 'message', listener);\n      }\n    }, {\n      key: \"onopen\",\n      get: function () {\n        return (0, _EventHandlerAttributes.getEventHandlerAttribute)(this, 'open');\n      },\n      set: function (listener) {\n        (0, _EventHandlerAttributes.setEventHandlerAttribute)(this, 'open', listener);\n      }\n    }]);\n  }(_EventTarget2.default);\n  WebSocket.CONNECTING = CONNECTING;\n  WebSocket.OPEN = OPEN;\n  WebSocket.CLOSING = CLOSING;\n  WebSocket.CLOSED = CLOSED;\n  var _default = exports.default = WebSocket;\n});", "lineCount": 237, "map": [[13, 2, 15, 0], [13, 6, 15, 0, "_Event"], [13, 12, 15, 0], [13, 15, 15, 0, "_interopRequireDefault"], [13, 37, 15, 0], [13, 38, 15, 0, "require"], [13, 45, 15, 0], [13, 46, 15, 0, "_dependencyMap"], [13, 60, 15, 0], [14, 2, 16, 0], [14, 6, 16, 0, "_EventHandlerAttributes"], [14, 29, 16, 0], [14, 32, 16, 0, "require"], [14, 39, 16, 0], [14, 40, 16, 0, "_dependencyMap"], [14, 54, 16, 0], [15, 2, 20, 0], [15, 6, 20, 0, "_EventTarget2"], [15, 19, 20, 0], [15, 22, 20, 0, "_interopRequireDefault"], [15, 44, 20, 0], [15, 45, 20, 0, "require"], [15, 52, 20, 0], [15, 53, 20, 0, "_dependencyMap"], [15, 67, 20, 0], [16, 2, 21, 0], [16, 6, 21, 0, "_MessageEvent"], [16, 19, 21, 0], [16, 22, 21, 0, "_interopRequireDefault"], [16, 44, 21, 0], [16, 45, 21, 0, "require"], [16, 52, 21, 0], [16, 53, 21, 0, "_dependencyMap"], [16, 67, 21, 0], [17, 2, 22, 0], [17, 6, 22, 0, "_CloseEvent"], [17, 17, 22, 0], [17, 20, 22, 0, "_interopRequireDefault"], [17, 42, 22, 0], [17, 43, 22, 0, "require"], [17, 50, 22, 0], [17, 51, 22, 0, "_dependencyMap"], [17, 65, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_Blob"], [18, 11, 23, 0], [18, 14, 23, 0, "_interopRequireDefault"], [18, 36, 23, 0], [18, 37, 23, 0, "require"], [18, 44, 23, 0], [18, 45, 23, 0, "_dependencyMap"], [18, 59, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_BlobManager"], [19, 18, 24, 0], [19, 21, 24, 0, "_interopRequireDefault"], [19, 43, 24, 0], [19, 44, 24, 0, "require"], [19, 51, 24, 0], [19, 52, 24, 0, "_dependencyMap"], [19, 66, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_NativeEventEmitter"], [20, 25, 25, 0], [20, 28, 25, 0, "_interopRequireDefault"], [20, 50, 25, 0], [20, 51, 25, 0, "require"], [20, 58, 25, 0], [20, 59, 25, 0, "_dependencyMap"], [20, 73, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_binaryToBase"], [21, 19, 26, 0], [21, 22, 26, 0, "_interopRequireDefault"], [21, 44, 26, 0], [21, 45, 26, 0, "require"], [21, 52, 26, 0], [21, 53, 26, 0, "_dependencyMap"], [21, 67, 26, 0], [22, 2, 27, 0], [22, 6, 27, 0, "_Platform"], [22, 15, 27, 0], [22, 18, 27, 0, "_interopRequireDefault"], [22, 40, 27, 0], [22, 41, 27, 0, "require"], [22, 48, 27, 0], [22, 49, 27, 0, "_dependencyMap"], [22, 63, 27, 0], [23, 2, 28, 0], [23, 6, 28, 0, "_NativeWebSocketModule"], [23, 28, 28, 0], [23, 31, 28, 0, "_interopRequireDefault"], [23, 53, 28, 0], [23, 54, 28, 0, "require"], [23, 61, 28, 0], [23, 62, 28, 0, "_dependencyMap"], [23, 76, 28, 0], [24, 2, 29, 0], [24, 6, 29, 0, "_base64Js"], [24, 15, 29, 0], [24, 18, 29, 0, "_interopRequireDefault"], [24, 40, 29, 0], [24, 41, 29, 0, "require"], [24, 48, 29, 0], [24, 49, 29, 0, "_dependencyMap"], [24, 63, 29, 0], [25, 2, 30, 0], [25, 6, 30, 0, "_invariant"], [25, 16, 30, 0], [25, 19, 30, 0, "_interopRequireDefault"], [25, 41, 30, 0], [25, 42, 30, 0, "require"], [25, 49, 30, 0], [25, 50, 30, 0, "_dependencyMap"], [25, 64, 30, 0], [26, 2, 30, 34], [26, 6, 30, 34, "_excluded"], [26, 15, 30, 34], [27, 2, 30, 34], [27, 11, 30, 34, "_callSuper"], [27, 22, 30, 34, "t"], [27, 23, 30, 34], [27, 25, 30, 34, "o"], [27, 26, 30, 34], [27, 28, 30, 34, "e"], [27, 29, 30, 34], [27, 40, 30, 34, "o"], [27, 41, 30, 34], [27, 48, 30, 34, "_getPrototypeOf2"], [27, 64, 30, 34], [27, 65, 30, 34, "default"], [27, 72, 30, 34], [27, 74, 30, 34, "o"], [27, 75, 30, 34], [27, 82, 30, 34, "_possibleConstructorReturn2"], [27, 109, 30, 34], [27, 110, 30, 34, "default"], [27, 117, 30, 34], [27, 119, 30, 34, "t"], [27, 120, 30, 34], [27, 122, 30, 34, "_isNativeReflectConstruct"], [27, 147, 30, 34], [27, 152, 30, 34, "Reflect"], [27, 159, 30, 34], [27, 160, 30, 34, "construct"], [27, 169, 30, 34], [27, 170, 30, 34, "o"], [27, 171, 30, 34], [27, 173, 30, 34, "e"], [27, 174, 30, 34], [27, 186, 30, 34, "_getPrototypeOf2"], [27, 202, 30, 34], [27, 203, 30, 34, "default"], [27, 210, 30, 34], [27, 212, 30, 34, "t"], [27, 213, 30, 34], [27, 215, 30, 34, "constructor"], [27, 226, 30, 34], [27, 230, 30, 34, "o"], [27, 231, 30, 34], [27, 232, 30, 34, "apply"], [27, 237, 30, 34], [27, 238, 30, 34, "t"], [27, 239, 30, 34], [27, 241, 30, 34, "e"], [27, 242, 30, 34], [28, 2, 30, 34], [28, 11, 30, 34, "_isNativeReflectConstruct"], [28, 37, 30, 34], [28, 51, 30, 34, "t"], [28, 52, 30, 34], [28, 56, 30, 34, "Boolean"], [28, 63, 30, 34], [28, 64, 30, 34, "prototype"], [28, 73, 30, 34], [28, 74, 30, 34, "valueOf"], [28, 81, 30, 34], [28, 82, 30, 34, "call"], [28, 86, 30, 34], [28, 87, 30, 34, "Reflect"], [28, 94, 30, 34], [28, 95, 30, 34, "construct"], [28, 104, 30, 34], [28, 105, 30, 34, "Boolean"], [28, 112, 30, 34], [28, 145, 30, 34, "t"], [28, 146, 30, 34], [28, 159, 30, 34, "_isNativeReflectConstruct"], [28, 184, 30, 34], [28, 196, 30, 34, "_isNativeReflectConstruct"], [28, 197, 30, 34], [28, 210, 30, 34, "t"], [28, 211, 30, 34], [29, 2, 46, 0], [29, 6, 46, 6, "CONNECTING"], [29, 16, 46, 16], [29, 19, 46, 19], [29, 20, 46, 20], [30, 2, 47, 0], [30, 6, 47, 6, "OPEN"], [30, 10, 47, 10], [30, 13, 47, 13], [30, 14, 47, 14], [31, 2, 48, 0], [31, 6, 48, 6, "CLOSING"], [31, 13, 48, 13], [31, 16, 48, 16], [31, 17, 48, 17], [32, 2, 49, 0], [32, 6, 49, 6, "CLOSED"], [32, 12, 49, 12], [32, 15, 49, 15], [32, 16, 49, 16], [33, 2, 51, 0], [33, 6, 51, 6, "CLOSE_NORMAL"], [33, 18, 51, 18], [33, 21, 51, 21], [33, 25, 51, 25], [34, 2, 55, 0], [34, 6, 55, 6, "CLOSE_ABNORMAL"], [34, 20, 55, 20], [34, 23, 55, 23], [34, 27, 55, 27], [35, 2, 57, 0], [35, 6, 57, 4, "nextWebSocketId"], [35, 21, 57, 19], [35, 24, 57, 22], [35, 25, 57, 23], [36, 2, 57, 24], [36, 6, 76, 6, "WebSocket"], [36, 15, 76, 15], [36, 41, 76, 15, "_EventTarget"], [36, 53, 76, 15], [37, 4, 98, 2], [37, 13, 98, 2, "WebSocket"], [37, 23, 99, 4, "url"], [37, 26, 99, 15], [37, 28, 100, 4, "protocols"], [37, 37, 100, 39], [37, 39, 101, 4, "options"], [37, 46, 101, 53], [37, 48, 102, 4], [38, 6, 102, 4], [38, 10, 102, 4, "_this"], [38, 15, 102, 4], [39, 6, 102, 4], [39, 10, 102, 4, "_classCallCheck2"], [39, 26, 102, 4], [39, 27, 102, 4, "default"], [39, 34, 102, 4], [39, 42, 102, 4, "WebSocket"], [39, 51, 102, 4], [40, 6, 103, 4, "_this"], [40, 11, 103, 4], [40, 14, 103, 4, "_callSuper"], [40, 24, 103, 4], [40, 31, 103, 4, "WebSocket"], [40, 40, 103, 4], [41, 6, 103, 12, "_this"], [41, 11, 103, 12], [41, 12, 82, 2, "CONNECTING"], [41, 22, 82, 12], [41, 25, 82, 23, "CONNECTING"], [41, 35, 82, 33], [42, 6, 82, 33, "_this"], [42, 11, 82, 33], [42, 12, 83, 2, "OPEN"], [42, 16, 83, 6], [42, 19, 83, 17, "OPEN"], [42, 23, 83, 21], [43, 6, 83, 21, "_this"], [43, 11, 83, 21], [43, 12, 84, 2, "CLOSING"], [43, 19, 84, 9], [43, 22, 84, 20, "CLOSING"], [43, 29, 84, 27], [44, 6, 84, 27, "_this"], [44, 11, 84, 27], [44, 12, 85, 2, "CLOSED"], [44, 18, 85, 8], [44, 21, 85, 19, "CLOSED"], [44, 27, 85, 25], [45, 6, 85, 25, "_this"], [45, 11, 85, 25], [45, 12, 95, 2, "readyState"], [45, 22, 95, 12], [45, 25, 95, 23, "CONNECTING"], [45, 35, 95, 33], [46, 6, 104, 4, "_this"], [46, 11, 104, 4], [46, 12, 104, 9, "url"], [46, 15, 104, 12], [46, 18, 104, 15, "url"], [46, 21, 104, 18], [47, 6, 105, 4], [47, 10, 105, 8], [47, 17, 105, 15, "protocols"], [47, 26, 105, 24], [47, 31, 105, 29], [47, 39, 105, 37], [47, 41, 105, 39], [48, 8, 106, 6, "protocols"], [48, 17, 106, 15], [48, 20, 106, 18], [48, 21, 106, 19, "protocols"], [48, 30, 106, 28], [48, 31, 106, 29], [49, 6, 107, 4], [50, 6, 109, 4], [50, 10, 109, 4, "_ref"], [50, 14, 109, 4], [50, 17, 109, 44, "options"], [50, 24, 109, 51], [50, 28, 109, 55], [50, 29, 109, 56], [50, 30, 109, 57], [51, 8, 109, 57, "_ref$headers"], [51, 20, 109, 57], [51, 23, 109, 57, "_ref"], [51, 27, 109, 57], [51, 28, 109, 11, "headers"], [51, 35, 109, 18], [52, 8, 109, 11, "headers"], [52, 15, 109, 18], [52, 18, 109, 18, "_ref$headers"], [52, 30, 109, 18], [52, 44, 109, 21], [52, 45, 109, 22], [52, 46, 109, 23], [52, 49, 109, 23, "_ref$headers"], [52, 61, 109, 23], [53, 8, 109, 28, "unrecognized"], [53, 20, 109, 40], [53, 27, 109, 40, "_objectWithoutProperties2"], [53, 52, 109, 40], [53, 53, 109, 40, "default"], [53, 60, 109, 40], [53, 62, 109, 40, "_ref"], [53, 66, 109, 40], [53, 68, 109, 40, "_excluded"], [53, 77, 109, 40], [54, 6, 113, 4], [54, 10, 113, 8, "unrecognized"], [54, 22, 113, 20], [54, 26, 113, 24], [54, 33, 113, 31, "unrecognized"], [54, 45, 113, 43], [54, 46, 113, 44, "origin"], [54, 52, 113, 50], [54, 57, 113, 55], [54, 65, 113, 63], [54, 67, 113, 65], [55, 8, 114, 6, "console"], [55, 15, 114, 13], [55, 16, 114, 14, "warn"], [55, 20, 114, 18], [55, 21, 115, 8], [55, 126, 116, 6], [55, 127, 116, 7], [56, 8, 120, 6, "headers"], [56, 15, 120, 13], [56, 16, 120, 14, "origin"], [56, 22, 120, 20], [56, 25, 120, 23, "unrecognized"], [56, 37, 120, 35], [56, 38, 120, 36, "origin"], [56, 44, 120, 42], [57, 8, 124, 6], [57, 15, 124, 13, "unrecognized"], [57, 27, 124, 25], [57, 28, 124, 26, "origin"], [57, 34, 124, 32], [58, 6, 125, 4], [59, 6, 128, 4], [59, 10, 128, 8, "Object"], [59, 16, 128, 14], [59, 17, 128, 15, "keys"], [59, 21, 128, 19], [59, 22, 128, 20, "unrecognized"], [59, 34, 128, 32], [59, 35, 128, 33], [59, 36, 128, 34, "length"], [59, 42, 128, 40], [59, 45, 128, 43], [59, 46, 128, 44], [59, 48, 128, 46], [60, 8, 129, 6, "console"], [60, 15, 129, 13], [60, 16, 129, 14, "warn"], [60, 20, 129, 18], [60, 21, 130, 8], [60, 68, 130, 55], [60, 71, 131, 10, "Object"], [60, 77, 131, 16], [60, 78, 131, 17, "keys"], [60, 82, 131, 21], [60, 83, 131, 22, "unrecognized"], [60, 95, 131, 34], [60, 96, 131, 35], [60, 97, 131, 36, "join"], [60, 101, 131, 40], [60, 102, 131, 41], [60, 108, 131, 47], [60, 109, 131, 48], [60, 112, 132, 10], [60, 117, 132, 15], [60, 120, 133, 10], [60, 164, 134, 6], [60, 165, 134, 7], [61, 6, 135, 4], [62, 6, 137, 4], [62, 10, 137, 8], [62, 11, 137, 9, "Array"], [62, 16, 137, 14], [62, 17, 137, 15, "isArray"], [62, 24, 137, 22], [62, 25, 137, 23, "protocols"], [62, 34, 137, 32], [62, 35, 137, 33], [62, 37, 137, 35], [63, 8, 138, 6, "protocols"], [63, 17, 138, 15], [63, 20, 138, 18], [63, 24, 138, 22], [64, 6, 139, 4], [65, 6, 141, 4, "_this"], [65, 11, 141, 4], [65, 12, 141, 9, "_eventEmitter"], [65, 25, 141, 22], [65, 28, 141, 25], [65, 32, 141, 29, "NativeEventEmitter"], [65, 59, 141, 47], [65, 60, 144, 6, "Platform"], [65, 77, 144, 14], [65, 78, 144, 15, "OS"], [65, 80, 144, 17], [65, 85, 144, 22], [65, 90, 144, 27], [65, 93, 144, 30], [65, 97, 144, 34], [65, 100, 144, 37, "NativeWebSocketModule"], [65, 130, 145, 4], [65, 131, 145, 5], [66, 6, 146, 4, "_this"], [66, 11, 146, 4], [66, 12, 146, 9, "_socketId"], [66, 21, 146, 18], [66, 24, 146, 21, "nextWebSocketId"], [66, 39, 146, 36], [66, 41, 146, 38], [67, 6, 147, 4, "_this"], [67, 11, 147, 4], [67, 12, 147, 9, "_registerEvents"], [67, 27, 147, 24], [67, 28, 147, 25], [67, 29, 147, 26], [68, 6, 148, 4, "NativeWebSocketModule"], [68, 36, 148, 25], [68, 37, 148, 26, "connect"], [68, 44, 148, 33], [68, 45, 148, 34, "url"], [68, 48, 148, 37], [68, 50, 148, 39, "protocols"], [68, 59, 148, 48], [68, 61, 148, 50], [69, 8, 148, 51, "headers"], [70, 6, 148, 58], [70, 7, 148, 59], [70, 9, 148, 61, "_this"], [70, 14, 148, 61], [70, 15, 148, 66, "_socketId"], [70, 24, 148, 75], [70, 25, 148, 76], [71, 6, 148, 77], [71, 13, 148, 77, "_this"], [71, 18, 148, 77], [72, 4, 149, 2], [73, 4, 149, 3], [73, 8, 149, 3, "_inherits2"], [73, 18, 149, 3], [73, 19, 149, 3, "default"], [73, 26, 149, 3], [73, 28, 149, 3, "WebSocket"], [73, 37, 149, 3], [73, 39, 149, 3, "_EventTarget"], [73, 51, 149, 3], [74, 4, 149, 3], [74, 15, 149, 3, "_createClass2"], [74, 28, 149, 3], [74, 29, 149, 3, "default"], [74, 36, 149, 3], [74, 38, 149, 3, "WebSocket"], [74, 47, 149, 3], [75, 6, 149, 3, "key"], [75, 9, 149, 3], [76, 6, 149, 3, "get"], [76, 9, 149, 3], [76, 11, 151, 2], [76, 20, 151, 2, "get"], [76, 21, 151, 2], [76, 23, 151, 32], [77, 8, 152, 4], [77, 15, 152, 11], [77, 19, 152, 15], [77, 20, 152, 16, "_binaryType"], [77, 31, 152, 27], [78, 6, 153, 2], [78, 7, 153, 3], [79, 6, 153, 3, "set"], [79, 9, 153, 3], [79, 11, 155, 2], [79, 20, 155, 2, "set"], [79, 21, 155, 17, "binaryType"], [79, 31, 155, 39], [79, 33, 155, 47], [80, 8, 156, 4], [80, 12, 156, 8, "binaryType"], [80, 22, 156, 18], [80, 27, 156, 23], [80, 33, 156, 29], [80, 37, 156, 33, "binaryType"], [80, 47, 156, 43], [80, 52, 156, 48], [80, 65, 156, 61], [80, 67, 156, 63], [81, 10, 157, 6], [81, 16, 157, 12], [81, 20, 157, 16, "Error"], [81, 25, 157, 21], [81, 26, 157, 22], [81, 77, 157, 73], [81, 78, 157, 74], [82, 8, 158, 4], [83, 8, 159, 4], [83, 12, 159, 8], [83, 16, 159, 12], [83, 17, 159, 13, "_binaryType"], [83, 28, 159, 24], [83, 33, 159, 29], [83, 39, 159, 35], [83, 43, 159, 39, "binaryType"], [83, 53, 159, 49], [83, 58, 159, 54], [83, 64, 159, 60], [83, 66, 159, 62], [84, 10, 160, 6], [84, 14, 160, 6, "invariant"], [84, 32, 160, 15], [84, 34, 161, 8, "BlobManager"], [84, 54, 161, 19], [84, 55, 161, 20, "isAvailable"], [84, 66, 161, 31], [84, 68, 162, 8], [84, 123, 163, 6], [84, 124, 163, 7], [85, 10, 164, 6], [85, 14, 164, 10, "binaryType"], [85, 24, 164, 20], [85, 29, 164, 25], [85, 35, 164, 31], [85, 37, 164, 33], [86, 12, 165, 8, "BlobManager"], [86, 32, 165, 19], [86, 33, 165, 20, "addWebSocketHandler"], [86, 52, 165, 39], [86, 53, 165, 40], [86, 57, 165, 44], [86, 58, 165, 45, "_socketId"], [86, 67, 165, 54], [86, 68, 165, 55], [87, 10, 166, 6], [87, 11, 166, 7], [87, 17, 166, 13], [88, 12, 167, 8, "BlobManager"], [88, 32, 167, 19], [88, 33, 167, 20, "removeWebSocketHandler"], [88, 55, 167, 42], [88, 56, 167, 43], [88, 60, 167, 47], [88, 61, 167, 48, "_socketId"], [88, 70, 167, 57], [88, 71, 167, 58], [89, 10, 168, 6], [90, 8, 169, 4], [91, 8, 170, 4], [91, 12, 170, 8], [91, 13, 170, 9, "_binaryType"], [91, 24, 170, 20], [91, 27, 170, 23, "binaryType"], [91, 37, 170, 33], [92, 6, 171, 2], [93, 4, 171, 3], [94, 6, 171, 3, "key"], [94, 9, 171, 3], [95, 6, 171, 3, "value"], [95, 11, 171, 3], [95, 13, 173, 2], [95, 22, 173, 2, "close"], [95, 27, 173, 7, "close"], [95, 28, 173, 8, "code"], [95, 32, 173, 21], [95, 34, 173, 23, "reason"], [95, 40, 173, 38], [95, 42, 173, 46], [96, 8, 174, 4], [96, 12, 174, 8], [96, 16, 174, 12], [96, 17, 174, 13, "readyState"], [96, 27, 174, 23], [96, 32, 174, 28], [96, 36, 174, 32], [96, 37, 174, 33, "CLOSING"], [96, 44, 174, 40], [96, 48, 174, 44], [96, 52, 174, 48], [96, 53, 174, 49, "readyState"], [96, 63, 174, 59], [96, 68, 174, 64], [96, 72, 174, 68], [96, 73, 174, 69, "CLOSED"], [96, 79, 174, 75], [96, 81, 174, 77], [97, 10, 175, 6], [98, 8, 176, 4], [99, 8, 178, 4], [99, 12, 178, 8], [99, 13, 178, 9, "readyState"], [99, 23, 178, 19], [99, 26, 178, 22], [99, 30, 178, 26], [99, 31, 178, 27, "CLOSING"], [99, 38, 178, 34], [100, 8, 179, 4], [100, 12, 179, 8], [100, 13, 179, 9, "_close"], [100, 19, 179, 15], [100, 20, 179, 16, "code"], [100, 24, 179, 20], [100, 26, 179, 22, "reason"], [100, 32, 179, 28], [100, 33, 179, 29], [101, 6, 180, 2], [102, 4, 180, 3], [103, 6, 180, 3, "key"], [103, 9, 180, 3], [104, 6, 180, 3, "value"], [104, 11, 180, 3], [104, 13, 182, 2], [104, 22, 182, 2, "send"], [104, 26, 182, 6, "send"], [104, 27, 182, 7, "data"], [104, 31, 182, 58], [104, 33, 182, 66], [105, 8, 183, 4], [105, 12, 183, 8], [105, 16, 183, 12], [105, 17, 183, 13, "readyState"], [105, 27, 183, 23], [105, 32, 183, 28], [105, 36, 183, 32], [105, 37, 183, 33, "CONNECTING"], [105, 47, 183, 43], [105, 49, 183, 45], [106, 10, 184, 6], [106, 16, 184, 12], [106, 20, 184, 16, "Error"], [106, 25, 184, 21], [106, 26, 184, 22], [106, 45, 184, 41], [106, 46, 184, 42], [107, 8, 185, 4], [108, 8, 187, 4], [108, 12, 187, 8, "data"], [108, 16, 187, 12], [108, 28, 187, 24, "Blob"], [108, 41, 187, 28], [108, 43, 187, 30], [109, 10, 188, 6], [109, 14, 188, 6, "invariant"], [109, 32, 188, 15], [109, 34, 189, 8, "BlobManager"], [109, 54, 189, 19], [109, 55, 189, 20, "isAvailable"], [109, 66, 189, 31], [109, 68, 190, 8], [109, 123, 191, 6], [109, 124, 191, 7], [110, 10, 192, 6, "BlobManager"], [110, 30, 192, 17], [110, 31, 192, 18, "sendOverSocket"], [110, 45, 192, 32], [110, 46, 192, 33, "data"], [110, 50, 192, 37], [110, 52, 192, 39], [110, 56, 192, 43], [110, 57, 192, 44, "_socketId"], [110, 66, 192, 53], [110, 67, 192, 54], [111, 10, 193, 6], [112, 8, 194, 4], [113, 8, 196, 4], [113, 12, 196, 8], [113, 19, 196, 15, "data"], [113, 23, 196, 19], [113, 28, 196, 24], [113, 36, 196, 32], [113, 38, 196, 34], [114, 10, 197, 6, "NativeWebSocketModule"], [114, 40, 197, 27], [114, 41, 197, 28, "send"], [114, 45, 197, 32], [114, 46, 197, 33, "data"], [114, 50, 197, 37], [114, 52, 197, 39], [114, 56, 197, 43], [114, 57, 197, 44, "_socketId"], [114, 66, 197, 53], [114, 67, 197, 54], [115, 10, 198, 6], [116, 8, 199, 4], [117, 8, 201, 4], [117, 12, 201, 8, "data"], [117, 16, 201, 12], [117, 28, 201, 24, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [117, 39, 201, 35], [117, 43, 201, 39, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [117, 54, 201, 50], [117, 55, 201, 51, "<PERSON><PERSON><PERSON><PERSON>"], [117, 61, 201, 57], [117, 62, 201, 58, "data"], [117, 66, 201, 62], [117, 67, 201, 63], [117, 69, 201, 65], [118, 10, 202, 6, "NativeWebSocketModule"], [118, 40, 202, 27], [118, 41, 202, 28, "sendBinary"], [118, 51, 202, 38], [118, 52, 202, 39], [118, 56, 202, 39, "binaryToBase64"], [118, 77, 202, 53], [118, 79, 202, 54, "data"], [118, 83, 202, 58], [118, 84, 202, 59], [118, 86, 202, 61], [118, 90, 202, 65], [118, 91, 202, 66, "_socketId"], [118, 100, 202, 75], [118, 101, 202, 76], [119, 10, 203, 6], [120, 8, 204, 4], [121, 8, 206, 4], [121, 14, 206, 10], [121, 18, 206, 14, "Error"], [121, 23, 206, 19], [121, 24, 206, 20], [121, 47, 206, 43], [121, 48, 206, 44], [122, 6, 207, 2], [123, 4, 207, 3], [124, 6, 207, 3, "key"], [124, 9, 207, 3], [125, 6, 207, 3, "value"], [125, 11, 207, 3], [125, 13, 209, 2], [125, 22, 209, 2, "ping"], [125, 26, 209, 6, "ping"], [125, 27, 209, 6], [125, 29, 209, 15], [126, 8, 210, 4], [126, 12, 210, 8], [126, 16, 210, 12], [126, 17, 210, 13, "readyState"], [126, 27, 210, 23], [126, 32, 210, 28], [126, 36, 210, 32], [126, 37, 210, 33, "CONNECTING"], [126, 47, 210, 43], [126, 49, 210, 45], [127, 10, 211, 6], [127, 16, 211, 12], [127, 20, 211, 16, "Error"], [127, 25, 211, 21], [127, 26, 211, 22], [127, 45, 211, 41], [127, 46, 211, 42], [128, 8, 212, 4], [129, 8, 214, 4, "NativeWebSocketModule"], [129, 38, 214, 25], [129, 39, 214, 26, "ping"], [129, 43, 214, 30], [129, 44, 214, 31], [129, 48, 214, 35], [129, 49, 214, 36, "_socketId"], [129, 58, 214, 45], [129, 59, 214, 46], [130, 6, 215, 2], [131, 4, 215, 3], [132, 6, 215, 3, "key"], [132, 9, 215, 3], [133, 6, 215, 3, "value"], [133, 11, 215, 3], [133, 13, 217, 2], [133, 22, 217, 2, "_close"], [133, 28, 217, 8, "_close"], [133, 29, 217, 9, "code"], [133, 33, 217, 22], [133, 35, 217, 24, "reason"], [133, 41, 217, 39], [133, 43, 217, 47], [134, 8, 219, 4], [134, 12, 219, 10, "statusCode"], [134, 22, 219, 20], [134, 25, 219, 23], [134, 32, 219, 30, "code"], [134, 36, 219, 34], [134, 41, 219, 39], [134, 49, 219, 47], [134, 52, 219, 50, "code"], [134, 56, 219, 54], [134, 59, 219, 57, "CLOSE_NORMAL"], [134, 71, 219, 69], [135, 8, 220, 4], [135, 12, 220, 10, "closeReason"], [135, 23, 220, 21], [135, 26, 220, 24], [135, 33, 220, 31, "reason"], [135, 39, 220, 37], [135, 44, 220, 42], [135, 52, 220, 50], [135, 55, 220, 53, "reason"], [135, 61, 220, 59], [135, 64, 220, 62], [135, 66, 220, 64], [136, 8, 221, 4, "NativeWebSocketModule"], [136, 38, 221, 25], [136, 39, 221, 26, "close"], [136, 44, 221, 31], [136, 45, 221, 32, "statusCode"], [136, 55, 221, 42], [136, 57, 221, 44, "closeReason"], [136, 68, 221, 55], [136, 70, 221, 57], [136, 74, 221, 61], [136, 75, 221, 62, "_socketId"], [136, 84, 221, 71], [136, 85, 221, 72], [137, 8, 223, 4], [137, 12, 223, 8, "BlobManager"], [137, 32, 223, 19], [137, 33, 223, 20, "isAvailable"], [137, 44, 223, 31], [137, 48, 223, 35], [137, 52, 223, 39], [137, 53, 223, 40, "_binaryType"], [137, 64, 223, 51], [137, 69, 223, 56], [137, 75, 223, 62], [137, 77, 223, 64], [138, 10, 224, 6, "BlobManager"], [138, 30, 224, 17], [138, 31, 224, 18, "removeWebSocketHandler"], [138, 53, 224, 40], [138, 54, 224, 41], [138, 58, 224, 45], [138, 59, 224, 46, "_socketId"], [138, 68, 224, 55], [138, 69, 224, 56], [139, 8, 225, 4], [140, 6, 226, 2], [141, 4, 226, 3], [142, 6, 226, 3, "key"], [142, 9, 226, 3], [143, 6, 226, 3, "value"], [143, 11, 226, 3], [143, 13, 228, 2], [143, 22, 228, 2, "_unregisterEvents"], [143, 39, 228, 19, "_unregisterEvents"], [143, 40, 228, 19], [143, 42, 228, 28], [144, 8, 229, 4], [144, 12, 229, 8], [144, 13, 229, 9, "_subscriptions"], [144, 27, 229, 23], [144, 28, 229, 24, "for<PERSON>ach"], [144, 35, 229, 31], [144, 36, 229, 32, "e"], [144, 37, 229, 33], [144, 41, 229, 37, "e"], [144, 42, 229, 38], [144, 43, 229, 39, "remove"], [144, 49, 229, 45], [144, 50, 229, 46], [144, 51, 229, 47], [144, 52, 229, 48], [145, 8, 230, 4], [145, 12, 230, 8], [145, 13, 230, 9, "_subscriptions"], [145, 27, 230, 23], [145, 30, 230, 26], [145, 32, 230, 28], [146, 6, 231, 2], [147, 4, 231, 3], [148, 6, 231, 3, "key"], [148, 9, 231, 3], [149, 6, 231, 3, "value"], [149, 11, 231, 3], [149, 13, 233, 2], [149, 22, 233, 2, "_registerEvents"], [149, 37, 233, 17, "_registerEvents"], [149, 38, 233, 17], [149, 40, 233, 26], [150, 8, 234, 4], [150, 12, 234, 8], [150, 13, 234, 9, "_subscriptions"], [150, 27, 234, 23], [150, 30, 234, 26], [150, 31, 235, 6], [150, 35, 235, 10], [150, 36, 235, 11, "_eventEmitter"], [150, 49, 235, 24], [150, 50, 235, 25, "addListener"], [150, 61, 235, 36], [150, 62, 235, 37], [150, 80, 235, 55], [150, 82, 235, 57, "ev"], [150, 84, 235, 59], [150, 88, 235, 63], [151, 10, 236, 8], [151, 14, 236, 12, "ev"], [151, 16, 236, 14], [151, 17, 236, 15, "id"], [151, 19, 236, 17], [151, 24, 236, 22], [151, 28, 236, 26], [151, 29, 236, 27, "_socketId"], [151, 38, 236, 36], [151, 40, 236, 38], [152, 12, 237, 10], [153, 10, 238, 8], [154, 10, 239, 8], [154, 14, 239, 12, "data"], [154, 18, 239, 56], [154, 21, 239, 59, "ev"], [154, 23, 239, 61], [154, 24, 239, 62, "data"], [154, 28, 239, 66], [155, 10, 240, 8], [155, 18, 240, 16, "ev"], [155, 20, 240, 18], [155, 21, 240, 19, "type"], [155, 25, 240, 23], [156, 12, 241, 10], [156, 17, 241, 15], [156, 25, 241, 23], [157, 14, 242, 12, "data"], [157, 18, 242, 16], [157, 21, 242, 19, "base64"], [157, 38, 242, 25], [157, 39, 242, 26, "toByteArray"], [157, 50, 242, 37], [157, 51, 242, 38, "ev"], [157, 53, 242, 40], [157, 54, 242, 41, "data"], [157, 58, 242, 45], [157, 59, 242, 46], [157, 60, 242, 47, "buffer"], [157, 66, 242, 53], [158, 14, 243, 12], [159, 12, 244, 10], [159, 17, 244, 15], [159, 23, 244, 21], [160, 14, 245, 12, "data"], [160, 18, 245, 16], [160, 21, 245, 19, "BlobManager"], [160, 41, 245, 30], [160, 42, 245, 31, "createFromOptions"], [160, 59, 245, 48], [160, 60, 245, 49, "ev"], [160, 62, 245, 51], [160, 63, 245, 52, "data"], [160, 67, 245, 56], [160, 68, 245, 57], [161, 14, 246, 12], [162, 10, 247, 8], [163, 10, 248, 8], [163, 14, 248, 12], [163, 15, 248, 13, "dispatchEvent"], [163, 28, 248, 26], [163, 29, 248, 27], [163, 33, 248, 31, "MessageEvent"], [163, 54, 248, 43], [163, 55, 248, 44], [163, 64, 248, 53], [163, 66, 248, 55], [164, 12, 248, 56, "data"], [165, 10, 248, 60], [165, 11, 248, 61], [165, 12, 248, 62], [165, 13, 248, 63], [166, 8, 249, 6], [166, 9, 249, 7], [166, 10, 249, 8], [166, 12, 250, 6], [166, 16, 250, 10], [166, 17, 250, 11, "_eventEmitter"], [166, 30, 250, 24], [166, 31, 250, 25, "addListener"], [166, 42, 250, 36], [166, 43, 250, 37], [166, 58, 250, 52], [166, 60, 250, 54, "ev"], [166, 62, 250, 56], [166, 66, 250, 60], [167, 10, 251, 8], [167, 14, 251, 12, "ev"], [167, 16, 251, 14], [167, 17, 251, 15, "id"], [167, 19, 251, 17], [167, 24, 251, 22], [167, 28, 251, 26], [167, 29, 251, 27, "_socketId"], [167, 38, 251, 36], [167, 40, 251, 38], [168, 12, 252, 10], [169, 10, 253, 8], [170, 10, 254, 8], [170, 14, 254, 12], [170, 15, 254, 13, "readyState"], [170, 25, 254, 23], [170, 28, 254, 26], [170, 32, 254, 30], [170, 33, 254, 31, "OPEN"], [170, 37, 254, 35], [171, 10, 255, 8], [171, 14, 255, 12], [171, 15, 255, 13, "protocol"], [171, 23, 255, 21], [171, 26, 255, 24, "ev"], [171, 28, 255, 26], [171, 29, 255, 27, "protocol"], [171, 37, 255, 35], [172, 10, 256, 8], [172, 14, 256, 12], [172, 15, 256, 13, "dispatchEvent"], [172, 28, 256, 26], [172, 29, 256, 27], [172, 33, 256, 31, "Event"], [172, 47, 256, 36], [172, 48, 256, 37], [172, 54, 256, 43], [172, 55, 256, 44], [172, 56, 256, 45], [173, 8, 257, 6], [173, 9, 257, 7], [173, 10, 257, 8], [173, 12, 258, 6], [173, 16, 258, 10], [173, 17, 258, 11, "_eventEmitter"], [173, 30, 258, 24], [173, 31, 258, 25, "addListener"], [173, 42, 258, 36], [173, 43, 258, 37], [173, 60, 258, 54], [173, 62, 258, 56, "ev"], [173, 64, 258, 58], [173, 68, 258, 62], [174, 10, 259, 8], [174, 14, 259, 12, "ev"], [174, 16, 259, 14], [174, 17, 259, 15, "id"], [174, 19, 259, 17], [174, 24, 259, 22], [174, 28, 259, 26], [174, 29, 259, 27, "_socketId"], [174, 38, 259, 36], [174, 40, 259, 38], [175, 12, 260, 10], [176, 10, 261, 8], [177, 10, 262, 8], [177, 14, 262, 12], [177, 15, 262, 13, "readyState"], [177, 25, 262, 23], [177, 28, 262, 26], [177, 32, 262, 30], [177, 33, 262, 31, "CLOSED"], [177, 39, 262, 37], [178, 10, 263, 8], [178, 14, 263, 12], [178, 15, 263, 13, "dispatchEvent"], [178, 28, 263, 26], [178, 29, 264, 10], [178, 33, 264, 14, "CloseEvent"], [178, 52, 264, 24], [178, 53, 264, 25], [178, 60, 264, 32], [178, 62, 264, 34], [179, 12, 265, 12, "code"], [179, 16, 265, 16], [179, 18, 265, 18, "ev"], [179, 20, 265, 20], [179, 21, 265, 21, "code"], [179, 25, 265, 25], [180, 12, 266, 12, "reason"], [180, 18, 266, 18], [180, 20, 266, 20, "ev"], [180, 22, 266, 22], [180, 23, 266, 23, "reason"], [181, 10, 268, 10], [181, 11, 268, 11], [181, 12, 269, 8], [181, 13, 269, 9], [182, 10, 270, 8], [182, 14, 270, 12], [182, 15, 270, 13, "_unregisterEvents"], [182, 32, 270, 30], [182, 33, 270, 31], [182, 34, 270, 32], [183, 10, 271, 8], [183, 14, 271, 12], [183, 15, 271, 13, "close"], [183, 20, 271, 18], [183, 21, 271, 19], [183, 22, 271, 20], [184, 8, 272, 6], [184, 9, 272, 7], [184, 10, 272, 8], [184, 12, 273, 6], [184, 16, 273, 10], [184, 17, 273, 11, "_eventEmitter"], [184, 30, 273, 24], [184, 31, 273, 25, "addListener"], [184, 42, 273, 36], [184, 43, 273, 37], [184, 60, 273, 54], [184, 62, 273, 56, "ev"], [184, 64, 273, 58], [184, 68, 273, 62], [185, 10, 274, 8], [185, 14, 274, 12, "ev"], [185, 16, 274, 14], [185, 17, 274, 15, "id"], [185, 19, 274, 17], [185, 24, 274, 22], [185, 28, 274, 26], [185, 29, 274, 27, "_socketId"], [185, 38, 274, 36], [185, 40, 274, 38], [186, 12, 275, 10], [187, 10, 276, 8], [188, 10, 277, 8], [188, 14, 277, 12], [188, 15, 277, 13, "readyState"], [188, 25, 277, 23], [188, 28, 277, 26], [188, 32, 277, 30], [188, 33, 277, 31, "CLOSED"], [188, 39, 277, 37], [189, 10, 278, 8], [189, 14, 278, 12], [189, 15, 278, 13, "dispatchEvent"], [189, 28, 278, 26], [189, 29, 278, 27], [189, 33, 278, 31, "Event"], [189, 47, 278, 36], [189, 48, 278, 37], [189, 55, 278, 44], [189, 56, 278, 45], [189, 57, 278, 46], [190, 10, 279, 8], [190, 14, 279, 12], [190, 15, 279, 13, "dispatchEvent"], [190, 28, 279, 26], [190, 29, 280, 10], [190, 33, 280, 14, "CloseEvent"], [190, 52, 280, 24], [190, 53, 280, 25], [190, 60, 280, 32], [190, 62, 280, 34], [191, 12, 281, 12, "code"], [191, 16, 281, 16], [191, 18, 281, 18, "CLOSE_ABNORMAL"], [191, 32, 281, 32], [192, 12, 282, 12, "reason"], [192, 18, 282, 18], [192, 20, 282, 20, "ev"], [192, 22, 282, 22], [192, 23, 282, 23, "message"], [193, 10, 284, 10], [193, 11, 284, 11], [193, 12, 285, 8], [193, 13, 285, 9], [194, 10, 286, 8], [194, 14, 286, 12], [194, 15, 286, 13, "_unregisterEvents"], [194, 32, 286, 30], [194, 33, 286, 31], [194, 34, 286, 32], [195, 10, 287, 8], [195, 14, 287, 12], [195, 15, 287, 13, "close"], [195, 20, 287, 18], [195, 21, 287, 19], [195, 22, 287, 20], [196, 8, 288, 6], [196, 9, 288, 7], [196, 10, 288, 8], [196, 11, 289, 5], [197, 6, 290, 2], [198, 4, 290, 3], [199, 6, 290, 3, "key"], [199, 9, 290, 3], [200, 6, 290, 3, "get"], [200, 9, 290, 3], [200, 11, 292, 2], [200, 20, 292, 2, "get"], [200, 21, 292, 2], [200, 23, 292, 38], [201, 8, 293, 4], [201, 15, 293, 11], [201, 19, 293, 11, "getEventHandlerAttribute"], [201, 67, 293, 35], [201, 69, 293, 36], [201, 73, 293, 40], [201, 75, 293, 42], [201, 82, 293, 49], [201, 83, 293, 50], [202, 6, 294, 2], [202, 7, 294, 3], [203, 6, 294, 3, "set"], [203, 9, 294, 3], [203, 11, 296, 2], [203, 20, 296, 2, "set"], [203, 21, 296, 14, "listener"], [203, 29, 296, 38], [203, 31, 296, 40], [204, 8, 297, 4], [204, 12, 297, 4, "setEventHandlerAttribute"], [204, 60, 297, 28], [204, 62, 297, 29], [204, 66, 297, 33], [204, 68, 297, 35], [204, 75, 297, 42], [204, 77, 297, 44, "listener"], [204, 85, 297, 52], [204, 86, 297, 53], [205, 6, 298, 2], [206, 4, 298, 3], [207, 6, 298, 3, "key"], [207, 9, 298, 3], [208, 6, 298, 3, "get"], [208, 9, 298, 3], [208, 11, 300, 2], [208, 20, 300, 2, "get"], [208, 21, 300, 2], [208, 23, 300, 38], [209, 8, 301, 4], [209, 15, 301, 11], [209, 19, 301, 11, "getEventHandlerAttribute"], [209, 67, 301, 35], [209, 69, 301, 36], [209, 73, 301, 40], [209, 75, 301, 42], [209, 82, 301, 49], [209, 83, 301, 50], [210, 6, 302, 2], [210, 7, 302, 3], [211, 6, 302, 3, "set"], [211, 9, 302, 3], [211, 11, 304, 2], [211, 20, 304, 2, "set"], [211, 21, 304, 14, "listener"], [211, 29, 304, 38], [211, 31, 304, 40], [212, 8, 305, 4], [212, 12, 305, 4, "setEventHandlerAttribute"], [212, 60, 305, 28], [212, 62, 305, 29], [212, 66, 305, 33], [212, 68, 305, 35], [212, 75, 305, 42], [212, 77, 305, 44, "listener"], [212, 85, 305, 52], [212, 86, 305, 53], [213, 6, 306, 2], [214, 4, 306, 3], [215, 6, 306, 3, "key"], [215, 9, 306, 3], [216, 6, 306, 3, "get"], [216, 9, 306, 3], [216, 11, 308, 2], [216, 20, 308, 2, "get"], [216, 21, 308, 2], [216, 23, 308, 40], [217, 8, 309, 4], [217, 15, 309, 11], [217, 19, 309, 11, "getEventHandlerAttribute"], [217, 67, 309, 35], [217, 69, 309, 36], [217, 73, 309, 40], [217, 75, 309, 42], [217, 84, 309, 51], [217, 85, 309, 52], [218, 6, 310, 2], [218, 7, 310, 3], [219, 6, 310, 3, "set"], [219, 9, 310, 3], [219, 11, 312, 2], [219, 20, 312, 2, "set"], [219, 21, 312, 16, "listener"], [219, 29, 312, 40], [219, 31, 312, 42], [220, 8, 313, 4], [220, 12, 313, 4, "setEventHandlerAttribute"], [220, 60, 313, 28], [220, 62, 313, 29], [220, 66, 313, 33], [220, 68, 313, 35], [220, 77, 313, 44], [220, 79, 313, 46, "listener"], [220, 87, 313, 54], [220, 88, 313, 55], [221, 6, 314, 2], [222, 4, 314, 3], [223, 6, 314, 3, "key"], [223, 9, 314, 3], [224, 6, 314, 3, "get"], [224, 9, 314, 3], [224, 11, 316, 2], [224, 20, 316, 2, "get"], [224, 21, 316, 2], [224, 23, 316, 37], [225, 8, 317, 4], [225, 15, 317, 11], [225, 19, 317, 11, "getEventHandlerAttribute"], [225, 67, 317, 35], [225, 69, 317, 36], [225, 73, 317, 40], [225, 75, 317, 42], [225, 81, 317, 48], [225, 82, 317, 49], [226, 6, 318, 2], [226, 7, 318, 3], [227, 6, 318, 3, "set"], [227, 9, 318, 3], [227, 11, 320, 2], [227, 20, 320, 2, "set"], [227, 21, 320, 13, "listener"], [227, 29, 320, 37], [227, 31, 320, 39], [228, 8, 321, 4], [228, 12, 321, 4, "setEventHandlerAttribute"], [228, 60, 321, 28], [228, 62, 321, 29], [228, 66, 321, 33], [228, 68, 321, 35], [228, 74, 321, 41], [228, 76, 321, 43, "listener"], [228, 84, 321, 51], [228, 85, 321, 52], [229, 6, 322, 2], [230, 4, 322, 3], [231, 2, 322, 3], [231, 4, 76, 24, "EventTarget"], [231, 25, 76, 35], [232, 2, 76, 6, "WebSocket"], [232, 11, 76, 15], [232, 12, 77, 9, "CONNECTING"], [232, 22, 77, 19], [232, 25, 77, 30, "CONNECTING"], [232, 35, 77, 40], [233, 2, 76, 6, "WebSocket"], [233, 11, 76, 15], [233, 12, 78, 9, "OPEN"], [233, 16, 78, 13], [233, 19, 78, 24, "OPEN"], [233, 23, 78, 28], [234, 2, 76, 6, "WebSocket"], [234, 11, 76, 15], [234, 12, 79, 9, "CLOSING"], [234, 19, 79, 16], [234, 22, 79, 27, "CLOSING"], [234, 29, 79, 34], [235, 2, 76, 6, "WebSocket"], [235, 11, 76, 15], [235, 12, 80, 9, "CLOSED"], [235, 18, 80, 15], [235, 21, 80, 26, "CLOSED"], [235, 27, 80, 32], [236, 2, 80, 32], [236, 6, 80, 32, "_default"], [236, 14, 80, 32], [236, 17, 80, 32, "exports"], [236, 24, 80, 32], [236, 25, 80, 32, "default"], [236, 32, 80, 32], [236, 35, 325, 15, "WebSocket"], [236, 44, 325, 24], [237, 0, 325, 24], [237, 3]], "functionMap": {"names": ["<global>", "WebSocket", "WebSocket#constructor", "WebSocket#get__binaryType", "WebSocket#set__binaryType", "WebSocket#close", "WebSocket#send", "WebSocket#ping", "WebSocket#_close", "WebSocket#_unregisterEvents", "_subscriptions.forEach$argument_0", "WebSocket#_registerEvents", "_eventEmitter.addListener$argument_1", "WebSocket#get__onclose", "WebSocket#set__onclose", "WebSocket#get__onerror", "WebSocket#set__onerror", "WebSocket#get__onmessage", "WebSocket#set__onmessage", "WebSocket#get__onopen", "WebSocket#set__onopen"], "mappings": "AAA;AC2E;ECsB;GDmD;EEE;GFE;EGE;GHgB;EIE;GJO;EKE;GLyB;EME;GNM;EOE;GPS;EQE;gCCC,eD;GRE;EUE;yDCE;ODc;sDCC;ODO;wDCC;ODc;wDCC;ODe;GVE;EYE;GZE;EaE;GbE;EcE;GdE;EeE;GfE;EgBE;GhBE;EiBE;GjBE;EkBE;GlBE;EmBE;GnBE;CDC"}}, "type": "js/module"}]}