{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PerformanceEntry = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _name = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"name\");\n  var _entryType = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"entryType\");\n  var _startTime = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"startTime\");\n  var _duration = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"duration\");\n  var PerformanceEntry = exports.PerformanceEntry = /*#__PURE__*/function () {\n    function PerformanceEntry(init) {\n      (0, _classCallCheck2.default)(this, PerformanceEntry);\n      Object.defineProperty(this, _name, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _entryType, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _startTime, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _duration, {\n        writable: true,\n        value: void 0\n      });\n      (0, _classPrivateFieldLooseBase2.default)(this, _name)[_name] = init.name;\n      (0, _classPrivateFieldLooseBase2.default)(this, _entryType)[_entryType] = init.entryType;\n      (0, _classPrivateFieldLooseBase2.default)(this, _startTime)[_startTime] = init.startTime;\n      (0, _classPrivateFieldLooseBase2.default)(this, _duration)[_duration] = init.duration;\n    }\n    return (0, _createClass2.default)(PerformanceEntry, [{\n      key: \"name\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _name)[_name];\n      }\n    }, {\n      key: \"entryType\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _entryType)[_entryType];\n      }\n    }, {\n      key: \"startTime\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _startTime)[_startTime];\n      }\n    }, {\n      key: \"duration\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _duration)[_duration];\n      }\n    }, {\n      key: \"toJSON\",\n      value: function toJSON() {\n        return {\n          name: (0, _classPrivateFieldLooseBase2.default)(this, _name)[_name],\n          entryType: (0, _classPrivateFieldLooseBase2.default)(this, _entryType)[_entryType],\n          startTime: (0, _classPrivateFieldLooseBase2.default)(this, _startTime)[_startTime],\n          duration: (0, _classPrivateFieldLooseBase2.default)(this, _duration)[_duration]\n        };\n      }\n    }]);\n  }();\n});", "lineCount": 71, "map": [[15, 6, 24, 13, "PerformanceEntry"], [15, 22, 24, 29], [15, 25, 24, 29, "exports"], [15, 32, 24, 29], [15, 33, 24, 29, "PerformanceEntry"], [15, 49, 24, 29], [16, 4, 30, 2], [16, 13, 30, 2, "PerformanceEntry"], [16, 30, 30, 14, "init"], [16, 34, 35, 3], [16, 36, 35, 5], [17, 6, 35, 5], [17, 10, 35, 5, "_classCallCheck2"], [17, 26, 35, 5], [17, 27, 35, 5, "default"], [17, 34, 35, 5], [17, 42, 35, 5, "PerformanceEntry"], [17, 58, 35, 5], [18, 6, 35, 5, "Object"], [18, 12, 35, 5], [18, 13, 35, 5, "defineProperty"], [18, 27, 35, 5], [18, 34, 35, 5, "_name"], [18, 39, 35, 5], [19, 8, 35, 5, "writable"], [19, 16, 35, 5], [20, 8, 35, 5, "value"], [20, 13, 35, 5], [21, 6, 35, 5], [22, 6, 35, 5, "Object"], [22, 12, 35, 5], [22, 13, 35, 5, "defineProperty"], [22, 27, 35, 5], [22, 34, 35, 5, "_entryType"], [22, 44, 35, 5], [23, 8, 35, 5, "writable"], [23, 16, 35, 5], [24, 8, 35, 5, "value"], [24, 13, 35, 5], [25, 6, 35, 5], [26, 6, 35, 5, "Object"], [26, 12, 35, 5], [26, 13, 35, 5, "defineProperty"], [26, 27, 35, 5], [26, 34, 35, 5, "_startTime"], [26, 44, 35, 5], [27, 8, 35, 5, "writable"], [27, 16, 35, 5], [28, 8, 35, 5, "value"], [28, 13, 35, 5], [29, 6, 35, 5], [30, 6, 35, 5, "Object"], [30, 12, 35, 5], [30, 13, 35, 5, "defineProperty"], [30, 27, 35, 5], [30, 34, 35, 5, "_duration"], [30, 43, 35, 5], [31, 8, 35, 5, "writable"], [31, 16, 35, 5], [32, 8, 35, 5, "value"], [32, 13, 35, 5], [33, 6, 35, 5], [34, 6, 36, 4], [34, 10, 36, 4, "_classPrivateFieldLooseBase2"], [34, 38, 36, 4], [34, 39, 36, 4, "default"], [34, 46, 36, 4], [34, 52, 36, 8], [34, 54, 36, 8, "_name"], [34, 59, 36, 8], [34, 61, 36, 8, "_name"], [34, 66, 36, 8], [34, 70, 36, 17, "init"], [34, 74, 36, 21], [34, 75, 36, 22, "name"], [34, 79, 36, 26], [35, 6, 37, 4], [35, 10, 37, 4, "_classPrivateFieldLooseBase2"], [35, 38, 37, 4], [35, 39, 37, 4, "default"], [35, 46, 37, 4], [35, 52, 37, 8], [35, 54, 37, 8, "_entryType"], [35, 64, 37, 8], [35, 66, 37, 8, "_entryType"], [35, 76, 37, 8], [35, 80, 37, 22, "init"], [35, 84, 37, 26], [35, 85, 37, 27, "entryType"], [35, 94, 37, 36], [36, 6, 38, 4], [36, 10, 38, 4, "_classPrivateFieldLooseBase2"], [36, 38, 38, 4], [36, 39, 38, 4, "default"], [36, 46, 38, 4], [36, 52, 38, 8], [36, 54, 38, 8, "_startTime"], [36, 64, 38, 8], [36, 66, 38, 8, "_startTime"], [36, 76, 38, 8], [36, 80, 38, 22, "init"], [36, 84, 38, 26], [36, 85, 38, 27, "startTime"], [36, 94, 38, 36], [37, 6, 39, 4], [37, 10, 39, 4, "_classPrivateFieldLooseBase2"], [37, 38, 39, 4], [37, 39, 39, 4, "default"], [37, 46, 39, 4], [37, 52, 39, 8], [37, 54, 39, 8, "_duration"], [37, 63, 39, 8], [37, 65, 39, 8, "_duration"], [37, 74, 39, 8], [37, 78, 39, 21, "init"], [37, 82, 39, 25], [37, 83, 39, 26, "duration"], [37, 91, 39, 34], [38, 4, 40, 2], [39, 4, 40, 3], [39, 15, 40, 3, "_createClass2"], [39, 28, 40, 3], [39, 29, 40, 3, "default"], [39, 36, 40, 3], [39, 38, 40, 3, "PerformanceEntry"], [39, 54, 40, 3], [40, 6, 40, 3, "key"], [40, 9, 40, 3], [41, 6, 40, 3, "get"], [41, 9, 40, 3], [41, 11, 42, 2], [41, 20, 42, 2, "get"], [41, 21, 42, 2], [41, 23, 42, 21], [42, 8, 43, 4], [42, 19, 43, 4, "_classPrivateFieldLooseBase2"], [42, 47, 43, 4], [42, 48, 43, 4, "default"], [42, 55, 43, 4], [42, 57, 43, 11], [42, 61, 43, 15], [42, 63, 43, 15, "_name"], [42, 68, 43, 15], [42, 70, 43, 15, "_name"], [42, 75, 43, 15], [43, 6, 44, 2], [44, 4, 44, 3], [45, 6, 44, 3, "key"], [45, 9, 44, 3], [46, 6, 44, 3, "get"], [46, 9, 44, 3], [46, 11, 46, 2], [46, 20, 46, 2, "get"], [46, 21, 46, 2], [46, 23, 46, 40], [47, 8, 47, 4], [47, 19, 47, 4, "_classPrivateFieldLooseBase2"], [47, 47, 47, 4], [47, 48, 47, 4, "default"], [47, 55, 47, 4], [47, 57, 47, 11], [47, 61, 47, 15], [47, 63, 47, 15, "_entryType"], [47, 73, 47, 15], [47, 75, 47, 15, "_entryType"], [47, 85, 47, 15], [48, 6, 48, 2], [49, 4, 48, 3], [50, 6, 48, 3, "key"], [50, 9, 48, 3], [51, 6, 48, 3, "get"], [51, 9, 48, 3], [51, 11, 50, 2], [51, 20, 50, 2, "get"], [51, 21, 50, 2], [51, 23, 50, 39], [52, 8, 51, 4], [52, 19, 51, 4, "_classPrivateFieldLooseBase2"], [52, 47, 51, 4], [52, 48, 51, 4, "default"], [52, 55, 51, 4], [52, 57, 51, 11], [52, 61, 51, 15], [52, 63, 51, 15, "_startTime"], [52, 73, 51, 15], [52, 75, 51, 15, "_startTime"], [52, 85, 51, 15], [53, 6, 52, 2], [54, 4, 52, 3], [55, 6, 52, 3, "key"], [55, 9, 52, 3], [56, 6, 52, 3, "get"], [56, 9, 52, 3], [56, 11, 54, 2], [56, 20, 54, 2, "get"], [56, 21, 54, 2], [56, 23, 54, 38], [57, 8, 55, 4], [57, 19, 55, 4, "_classPrivateFieldLooseBase2"], [57, 47, 55, 4], [57, 48, 55, 4, "default"], [57, 55, 55, 4], [57, 57, 55, 11], [57, 61, 55, 15], [57, 63, 55, 15, "_duration"], [57, 72, 55, 15], [57, 74, 55, 15, "_duration"], [57, 83, 55, 15], [58, 6, 56, 2], [59, 4, 56, 3], [60, 6, 56, 3, "key"], [60, 9, 56, 3], [61, 6, 56, 3, "value"], [61, 11, 56, 3], [61, 13, 58, 2], [61, 22, 58, 2, "toJSON"], [61, 28, 58, 8, "toJSON"], [61, 29, 58, 8], [61, 31, 58, 33], [62, 8, 59, 4], [62, 15, 59, 11], [63, 10, 60, 6, "name"], [63, 14, 60, 10], [63, 20, 60, 10, "_classPrivateFieldLooseBase2"], [63, 48, 60, 10], [63, 49, 60, 10, "default"], [63, 56, 60, 10], [63, 58, 60, 12], [63, 62, 60, 16], [63, 64, 60, 16, "_name"], [63, 69, 60, 16], [63, 71, 60, 16, "_name"], [63, 76, 60, 16], [63, 77, 60, 22], [64, 10, 61, 6, "entryType"], [64, 19, 61, 15], [64, 25, 61, 15, "_classPrivateFieldLooseBase2"], [64, 53, 61, 15], [64, 54, 61, 15, "default"], [64, 61, 61, 15], [64, 63, 61, 17], [64, 67, 61, 21], [64, 69, 61, 21, "_entryType"], [64, 79, 61, 21], [64, 81, 61, 21, "_entryType"], [64, 91, 61, 21], [64, 92, 61, 32], [65, 10, 62, 6, "startTime"], [65, 19, 62, 15], [65, 25, 62, 15, "_classPrivateFieldLooseBase2"], [65, 53, 62, 15], [65, 54, 62, 15, "default"], [65, 61, 62, 15], [65, 63, 62, 17], [65, 67, 62, 21], [65, 69, 62, 21, "_startTime"], [65, 79, 62, 21], [65, 81, 62, 21, "_startTime"], [65, 91, 62, 21], [65, 92, 62, 32], [66, 10, 63, 6, "duration"], [66, 18, 63, 14], [66, 24, 63, 14, "_classPrivateFieldLooseBase2"], [66, 52, 63, 14], [66, 53, 63, 14, "default"], [66, 60, 63, 14], [66, 62, 63, 16], [66, 66, 63, 20], [66, 68, 63, 20, "_duration"], [66, 77, 63, 20], [66, 79, 63, 20, "_duration"], [66, 88, 63, 20], [67, 8, 64, 4], [67, 9, 64, 5], [68, 6, 65, 2], [69, 4, 65, 3], [70, 2, 65, 3], [71, 0, 65, 3], [71, 3]], "functionMap": {"names": ["<global>", "PerformanceEntry", "constructor", "get__name", "get__entryType", "get__startTime", "get__duration", "toJSON"], "mappings": "AAA;OCuB;ECM;GDU;EEE;GFE;EGE;GHE;EIE;GJE;EKE;GLE;EME;GNO;CDC"}}, "type": "js/module"}]}