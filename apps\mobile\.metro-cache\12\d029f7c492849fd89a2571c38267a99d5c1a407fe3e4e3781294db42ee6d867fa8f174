{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function clamp(min, value, max) {\n    if (value < min) {\n      return min;\n    }\n    if (value > max) {\n      return max;\n    }\n    return value;\n  }\n  var _default = exports.default = clamp;\n});", "lineCount": 18, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 13, 0], [8, 11, 13, 9, "clamp"], [8, 16, 13, 14, "clamp"], [8, 17, 13, 15, "min"], [8, 20, 13, 26], [8, 22, 13, 28, "value"], [8, 27, 13, 41], [8, 29, 13, 43, "max"], [8, 32, 13, 54], [8, 34, 13, 64], [9, 4, 14, 2], [9, 8, 14, 6, "value"], [9, 13, 14, 11], [9, 16, 14, 14, "min"], [9, 19, 14, 17], [9, 21, 14, 19], [10, 6, 15, 4], [10, 13, 15, 11, "min"], [10, 16, 15, 14], [11, 4, 16, 2], [12, 4, 17, 2], [12, 8, 17, 6, "value"], [12, 13, 17, 11], [12, 16, 17, 14, "max"], [12, 19, 17, 17], [12, 21, 17, 19], [13, 6, 18, 4], [13, 13, 18, 11, "max"], [13, 16, 18, 14], [14, 4, 19, 2], [15, 4, 20, 2], [15, 11, 20, 9, "value"], [15, 16, 20, 14], [16, 2, 21, 0], [17, 2, 21, 1], [17, 6, 21, 1, "_default"], [17, 14, 21, 1], [17, 17, 21, 1, "exports"], [17, 24, 21, 1], [17, 25, 21, 1, "default"], [17, 32, 21, 1], [17, 35, 23, 15, "clamp"], [17, 40, 23, 20], [18, 0, 23, 20], [18, 3]], "functionMap": {"names": ["<global>", "clamp"], "mappings": "AAA;ACY;CDQ"}}, "type": "js/module"}]}