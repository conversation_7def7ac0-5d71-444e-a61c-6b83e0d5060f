{"dependencies": [{"name": "../Utilities/PolyfillFunctions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 33}, "end": {"line": 13, "column": 74}}], "key": "HYclPKQCLeyfRj4pG+IgrzgyEZ8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _require = require(_dependencyMap[0], \"../Utilities/PolyfillFunctions\"),\n    polyfillObjectProperty = _require.polyfillObjectProperty;\n  var navigator = global.navigator;\n  if (navigator === undefined) {\n    global.navigator = {\n      product: 'ReactNative'\n    };\n  } else {\n    polyfillObjectProperty(navigator, 'product', () => 'ReactNative');\n  }\n});", "lineCount": 14, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 13, 0], [4, 6, 13, 0, "_require"], [4, 14, 13, 0], [4, 17, 13, 33, "require"], [4, 24, 13, 40], [4, 25, 13, 40, "_dependencyMap"], [4, 39, 13, 40], [4, 76, 13, 73], [4, 77, 13, 74], [5, 4, 13, 7, "polyfillObjectProperty"], [5, 26, 13, 29], [5, 29, 13, 29, "_require"], [5, 37, 13, 29], [5, 38, 13, 7, "polyfillObjectProperty"], [5, 60, 13, 29], [6, 2, 15, 0], [6, 6, 15, 6, "navigator"], [6, 15, 15, 15], [6, 18, 15, 18, "global"], [6, 24, 15, 24], [6, 25, 15, 25, "navigator"], [6, 34, 15, 34], [7, 2, 16, 0], [7, 6, 16, 4, "navigator"], [7, 15, 16, 13], [7, 20, 16, 18, "undefined"], [7, 29, 16, 27], [7, 31, 16, 29], [8, 4, 18, 2, "global"], [8, 10, 18, 8], [8, 11, 18, 9, "navigator"], [8, 20, 18, 18], [8, 23, 18, 21], [9, 6, 18, 22, "product"], [9, 13, 18, 29], [9, 15, 18, 31], [10, 4, 18, 44], [10, 5, 18, 45], [11, 2, 19, 0], [11, 3, 19, 1], [11, 9, 19, 7], [12, 4, 21, 2, "polyfillObjectProperty"], [12, 26, 21, 24], [12, 27, 21, 25, "navigator"], [12, 36, 21, 34], [12, 38, 21, 36], [12, 47, 21, 45], [12, 49, 21, 47], [12, 55, 21, 53], [12, 68, 21, 66], [12, 69, 21, 67], [13, 2, 22, 0], [14, 0, 22, 1], [14, 3]], "functionMap": {"names": ["<global>", "polyfillObjectProperty$argument_2"], "mappings": "AAA;+CCoB,mBD"}}, "type": "js/module"}]}