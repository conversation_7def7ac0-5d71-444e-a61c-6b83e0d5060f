{"dependencies": [{"name": "./FileReader_new", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 31}}], "key": "vx+N9K2Az0W5a079x7k6fUUCVS0=", "exportNames": ["*"]}}, {"name": "./FileReader_old", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 31}}], "key": "smbEU9i7N0aVEWROifFOdRdXaSc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var useBuiltInEventTarget = global.RN$useBuiltInEventTarget?.();\n  var _default = exports.default = useBuiltInEventTarget ? require(_dependencyMap[0], \"./FileReader_new\").default : require(_dependencyMap[1], \"./FileReader_old\").default;\n});", "lineCount": 8, "map": [[6, 2, 15, 0], [6, 6, 15, 6, "useBuiltInEventTarget"], [6, 27, 15, 27], [6, 30, 15, 30, "global"], [6, 36, 15, 36], [6, 37, 15, 37, "RN$useBuiltInEventTarget"], [6, 61, 15, 61], [6, 64, 15, 64], [6, 65, 15, 65], [7, 2, 15, 66], [7, 6, 15, 66, "_default"], [7, 14, 15, 66], [7, 17, 15, 66, "exports"], [7, 24, 15, 66], [7, 25, 15, 66, "default"], [7, 32, 15, 66], [7, 35, 17, 16, "useBuiltInEventTarget"], [7, 56, 17, 37], [7, 59, 19, 4, "require"], [7, 66, 19, 11], [7, 67, 19, 11, "_dependencyMap"], [7, 81, 19, 11], [7, 104, 19, 30], [7, 105, 19, 31], [7, 106, 19, 32, "default"], [7, 113, 19, 39], [7, 116, 20, 4, "require"], [7, 123, 20, 11], [7, 124, 20, 11, "_dependencyMap"], [7, 138, 20, 11], [7, 161, 20, 30], [7, 162, 20, 31], [7, 163, 20, 32, "default"], [7, 170, 20, 39], [8, 0, 20, 39], [8, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}