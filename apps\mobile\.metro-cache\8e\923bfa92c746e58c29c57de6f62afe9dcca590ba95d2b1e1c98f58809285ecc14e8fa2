{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 70, "index": 117}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 118}, "end": {"line": 5, "column": 111, "index": 229}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "./useFrameSize.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 230}, "end": {"line": 6, "column": 54, "index": 284}}], "key": "dRzp9Mme73SbFUGqz80tDHJoVo0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 285}, "end": {"line": 7, "column": 48, "index": 333}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SafeAreaProviderCompat = SafeAreaProviderCompat;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _reactNative = require(_dependencyMap[1], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[2], \"react-native-safe-area-context\");\n  var _useFrameSize = require(_dependencyMap[3], \"./useFrameSize.js\");\n  var _jsxRuntime = require(_dependencyMap[4], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var _Dimensions$get = _reactNative.Dimensions.get('window'),\n    _Dimensions$get$width = _Dimensions$get.width,\n    width = _Dimensions$get$width === void 0 ? 0 : _Dimensions$get$width,\n    _Dimensions$get$heigh = _Dimensions$get.height,\n    height = _Dimensions$get$heigh === void 0 ? 0 : _Dimensions$get$heigh;\n\n  // To support SSR on web, we need to have empty insets for initial values\n  // Otherwise there can be mismatch between SSR and client output\n  // We also need to specify empty values to support tests environments\n  var initialMetrics = _reactNative.Platform.OS === 'web' || _reactNativeSafeAreaContext.initialWindowMetrics == null ? {\n    frame: {\n      x: 0,\n      y: 0,\n      width,\n      height\n    },\n    insets: {\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0\n    }\n  } : _reactNativeSafeAreaContext.initialWindowMetrics;\n  function SafeAreaProviderCompat(_ref) {\n    var children = _ref.children,\n      style = _ref.style;\n    var insets = React.useContext(_reactNativeSafeAreaContext.SafeAreaInsetsContext);\n    children = /*#__PURE__*/(0, _jsxRuntime.jsx)(_useFrameSize.FrameSizeProvider, {\n      children: children\n    });\n    if (insets) {\n      // If we already have insets, don't wrap the stack in another safe area provider\n      // This avoids an issue with updates at the cost of potentially incorrect values\n      // https://github.com/react-navigation/react-navigation/issues/174\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n        style: [styles.container, style],\n        children: children\n      });\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeSafeAreaContext.SafeAreaProvider, {\n      initialMetrics: initialMetrics,\n      style: style,\n      children: children\n    });\n  }\n  SafeAreaProviderCompat.initialMetrics = initialMetrics;\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flex: 1\n    }\n  });\n});", "lineCount": 65, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "SafeAreaProviderCompat"], [7, 32, 1, 13], [7, 35, 1, 13, "SafeAreaProviderCompat"], [7, 57, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_reactNative"], [9, 18, 4, 0], [9, 21, 4, 0, "require"], [9, 28, 4, 0], [9, 29, 4, 0, "_dependencyMap"], [9, 43, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_reactNativeSafeAreaContext"], [10, 33, 5, 0], [10, 36, 5, 0, "require"], [10, 43, 5, 0], [10, 44, 5, 0, "_dependencyMap"], [10, 58, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_useFrameSize"], [11, 19, 6, 0], [11, 22, 6, 0, "require"], [11, 29, 6, 0], [11, 30, 6, 0, "_dependencyMap"], [11, 44, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_jsxRuntime"], [12, 17, 7, 0], [12, 20, 7, 0, "require"], [12, 27, 7, 0], [12, 28, 7, 0, "_dependencyMap"], [12, 42, 7, 0], [13, 2, 7, 48], [13, 11, 7, 48, "_interopRequireWildcard"], [13, 35, 7, 48, "e"], [13, 36, 7, 48], [13, 38, 7, 48, "t"], [13, 39, 7, 48], [13, 68, 7, 48, "WeakMap"], [13, 75, 7, 48], [13, 81, 7, 48, "r"], [13, 82, 7, 48], [13, 89, 7, 48, "WeakMap"], [13, 96, 7, 48], [13, 100, 7, 48, "n"], [13, 101, 7, 48], [13, 108, 7, 48, "WeakMap"], [13, 115, 7, 48], [13, 127, 7, 48, "_interopRequireWildcard"], [13, 150, 7, 48], [13, 162, 7, 48, "_interopRequireWildcard"], [13, 163, 7, 48, "e"], [13, 164, 7, 48], [13, 166, 7, 48, "t"], [13, 167, 7, 48], [13, 176, 7, 48, "t"], [13, 177, 7, 48], [13, 181, 7, 48, "e"], [13, 182, 7, 48], [13, 186, 7, 48, "e"], [13, 187, 7, 48], [13, 188, 7, 48, "__esModule"], [13, 198, 7, 48], [13, 207, 7, 48, "e"], [13, 208, 7, 48], [13, 214, 7, 48, "o"], [13, 215, 7, 48], [13, 217, 7, 48, "i"], [13, 218, 7, 48], [13, 220, 7, 48, "f"], [13, 221, 7, 48], [13, 226, 7, 48, "__proto__"], [13, 235, 7, 48], [13, 243, 7, 48, "default"], [13, 250, 7, 48], [13, 252, 7, 48, "e"], [13, 253, 7, 48], [13, 270, 7, 48, "e"], [13, 271, 7, 48], [13, 294, 7, 48, "e"], [13, 295, 7, 48], [13, 320, 7, 48, "e"], [13, 321, 7, 48], [13, 330, 7, 48, "f"], [13, 331, 7, 48], [13, 337, 7, 48, "o"], [13, 338, 7, 48], [13, 341, 7, 48, "t"], [13, 342, 7, 48], [13, 345, 7, 48, "n"], [13, 346, 7, 48], [13, 349, 7, 48, "r"], [13, 350, 7, 48], [13, 358, 7, 48, "o"], [13, 359, 7, 48], [13, 360, 7, 48, "has"], [13, 363, 7, 48], [13, 364, 7, 48, "e"], [13, 365, 7, 48], [13, 375, 7, 48, "o"], [13, 376, 7, 48], [13, 377, 7, 48, "get"], [13, 380, 7, 48], [13, 381, 7, 48, "e"], [13, 382, 7, 48], [13, 385, 7, 48, "o"], [13, 386, 7, 48], [13, 387, 7, 48, "set"], [13, 390, 7, 48], [13, 391, 7, 48, "e"], [13, 392, 7, 48], [13, 394, 7, 48, "f"], [13, 395, 7, 48], [13, 409, 7, 48, "_t"], [13, 411, 7, 48], [13, 415, 7, 48, "e"], [13, 416, 7, 48], [13, 432, 7, 48, "_t"], [13, 434, 7, 48], [13, 441, 7, 48, "hasOwnProperty"], [13, 455, 7, 48], [13, 456, 7, 48, "call"], [13, 460, 7, 48], [13, 461, 7, 48, "e"], [13, 462, 7, 48], [13, 464, 7, 48, "_t"], [13, 466, 7, 48], [13, 473, 7, 48, "i"], [13, 474, 7, 48], [13, 478, 7, 48, "o"], [13, 479, 7, 48], [13, 482, 7, 48, "Object"], [13, 488, 7, 48], [13, 489, 7, 48, "defineProperty"], [13, 503, 7, 48], [13, 508, 7, 48, "Object"], [13, 514, 7, 48], [13, 515, 7, 48, "getOwnPropertyDescriptor"], [13, 539, 7, 48], [13, 540, 7, 48, "e"], [13, 541, 7, 48], [13, 543, 7, 48, "_t"], [13, 545, 7, 48], [13, 552, 7, 48, "i"], [13, 553, 7, 48], [13, 554, 7, 48, "get"], [13, 557, 7, 48], [13, 561, 7, 48, "i"], [13, 562, 7, 48], [13, 563, 7, 48, "set"], [13, 566, 7, 48], [13, 570, 7, 48, "o"], [13, 571, 7, 48], [13, 572, 7, 48, "f"], [13, 573, 7, 48], [13, 575, 7, 48, "_t"], [13, 577, 7, 48], [13, 579, 7, 48, "i"], [13, 580, 7, 48], [13, 584, 7, 48, "f"], [13, 585, 7, 48], [13, 586, 7, 48, "_t"], [13, 588, 7, 48], [13, 592, 7, 48, "e"], [13, 593, 7, 48], [13, 594, 7, 48, "_t"], [13, 596, 7, 48], [13, 607, 7, 48, "f"], [13, 608, 7, 48], [13, 613, 7, 48, "e"], [13, 614, 7, 48], [13, 616, 7, 48, "t"], [13, 617, 7, 48], [14, 2, 8, 0], [14, 6, 8, 0, "_Dimensions$get"], [14, 21, 8, 0], [14, 24, 11, 4, "Dimensions"], [14, 47, 11, 14], [14, 48, 11, 15, "get"], [14, 51, 11, 18], [14, 52, 11, 19], [14, 60, 11, 27], [14, 61, 11, 28], [15, 4, 11, 28, "_Dimensions$get$width"], [15, 25, 11, 28], [15, 28, 11, 28, "_Dimensions$get"], [15, 43, 11, 28], [15, 44, 9, 2, "width"], [15, 49, 9, 7], [16, 4, 9, 2, "width"], [16, 9, 9, 7], [16, 12, 9, 7, "_Dimensions$get$width"], [16, 33, 9, 7], [16, 47, 9, 10], [16, 48, 9, 11], [16, 51, 9, 11, "_Dimensions$get$width"], [16, 72, 9, 11], [17, 4, 9, 11, "_Dimensions$get$heigh"], [17, 25, 9, 11], [17, 28, 9, 11, "_Dimensions$get"], [17, 43, 9, 11], [17, 44, 10, 2, "height"], [17, 50, 10, 8], [18, 4, 10, 2, "height"], [18, 10, 10, 8], [18, 13, 10, 8, "_Dimensions$get$heigh"], [18, 34, 10, 8], [18, 48, 10, 11], [18, 49, 10, 12], [18, 52, 10, 12, "_Dimensions$get$heigh"], [18, 73, 10, 12], [20, 2, 13, 0], [21, 2, 14, 0], [22, 2, 15, 0], [23, 2, 16, 0], [23, 6, 16, 6, "initialMetrics"], [23, 20, 16, 20], [23, 23, 16, 23, "Platform"], [23, 44, 16, 31], [23, 45, 16, 32, "OS"], [23, 47, 16, 34], [23, 52, 16, 39], [23, 57, 16, 44], [23, 61, 16, 48, "initialWindowMetrics"], [23, 109, 16, 68], [23, 113, 16, 72], [23, 117, 16, 76], [23, 120, 16, 79], [24, 4, 17, 2, "frame"], [24, 9, 17, 7], [24, 11, 17, 9], [25, 6, 18, 4, "x"], [25, 7, 18, 5], [25, 9, 18, 7], [25, 10, 18, 8], [26, 6, 19, 4, "y"], [26, 7, 19, 5], [26, 9, 19, 7], [26, 10, 19, 8], [27, 6, 20, 4, "width"], [27, 11, 20, 9], [28, 6, 21, 4, "height"], [29, 4, 22, 2], [29, 5, 22, 3], [30, 4, 23, 2, "insets"], [30, 10, 23, 8], [30, 12, 23, 10], [31, 6, 24, 4, "top"], [31, 9, 24, 7], [31, 11, 24, 9], [31, 12, 24, 10], [32, 6, 25, 4, "left"], [32, 10, 25, 8], [32, 12, 25, 10], [32, 13, 25, 11], [33, 6, 26, 4, "right"], [33, 11, 26, 9], [33, 13, 26, 11], [33, 14, 26, 12], [34, 6, 27, 4, "bottom"], [34, 12, 27, 10], [34, 14, 27, 12], [35, 4, 28, 2], [36, 2, 29, 0], [36, 3, 29, 1], [36, 6, 29, 4, "initialWindowMetrics"], [36, 54, 29, 24], [37, 2, 30, 7], [37, 11, 30, 16, "SafeAreaProviderCompat"], [37, 33, 30, 38, "SafeAreaProviderCompat"], [37, 34, 30, 38, "_ref"], [37, 38, 30, 38], [37, 40, 33, 3], [38, 4, 33, 3], [38, 8, 31, 2, "children"], [38, 16, 31, 10], [38, 19, 31, 10, "_ref"], [38, 23, 31, 10], [38, 24, 31, 2, "children"], [38, 32, 31, 10], [39, 6, 32, 2, "style"], [39, 11, 32, 7], [39, 14, 32, 7, "_ref"], [39, 18, 32, 7], [39, 19, 32, 2, "style"], [39, 24, 32, 7], [40, 4, 34, 2], [40, 8, 34, 8, "insets"], [40, 14, 34, 14], [40, 17, 34, 17, "React"], [40, 22, 34, 22], [40, 23, 34, 23, "useContext"], [40, 33, 34, 33], [40, 34, 34, 34, "SafeAreaInsetsContext"], [40, 83, 34, 55], [40, 84, 34, 56], [41, 4, 35, 2, "children"], [41, 12, 35, 10], [41, 15, 35, 13], [41, 28, 35, 26], [41, 32, 35, 26, "_jsx"], [41, 47, 35, 30], [41, 49, 35, 31, "FrameSizeProvider"], [41, 80, 35, 48], [41, 82, 35, 50], [42, 6, 36, 4, "children"], [42, 14, 36, 12], [42, 16, 36, 14, "children"], [43, 4, 37, 2], [43, 5, 37, 3], [43, 6, 37, 4], [44, 4, 38, 2], [44, 8, 38, 6, "insets"], [44, 14, 38, 12], [44, 16, 38, 14], [45, 6, 39, 4], [46, 6, 40, 4], [47, 6, 41, 4], [48, 6, 42, 4], [48, 13, 42, 11], [48, 26, 42, 24], [48, 30, 42, 24, "_jsx"], [48, 45, 42, 28], [48, 47, 42, 29, "View"], [48, 64, 42, 33], [48, 66, 42, 35], [49, 8, 43, 6, "style"], [49, 13, 43, 11], [49, 15, 43, 13], [49, 16, 43, 14, "styles"], [49, 22, 43, 20], [49, 23, 43, 21, "container"], [49, 32, 43, 30], [49, 34, 43, 32, "style"], [49, 39, 43, 37], [49, 40, 43, 38], [50, 8, 44, 6, "children"], [50, 16, 44, 14], [50, 18, 44, 16, "children"], [51, 6, 45, 4], [51, 7, 45, 5], [51, 8, 45, 6], [52, 4, 46, 2], [53, 4, 47, 2], [53, 11, 47, 9], [53, 24, 47, 22], [53, 28, 47, 22, "_jsx"], [53, 43, 47, 26], [53, 45, 47, 27, "SafeAreaProvider"], [53, 89, 47, 43], [53, 91, 47, 45], [54, 6, 48, 4, "initialMetrics"], [54, 20, 48, 18], [54, 22, 48, 20, "initialMetrics"], [54, 36, 48, 34], [55, 6, 49, 4, "style"], [55, 11, 49, 9], [55, 13, 49, 11, "style"], [55, 18, 49, 16], [56, 6, 50, 4, "children"], [56, 14, 50, 12], [56, 16, 50, 14, "children"], [57, 4, 51, 2], [57, 5, 51, 3], [57, 6, 51, 4], [58, 2, 52, 0], [59, 2, 53, 0, "SafeAreaProviderCompat"], [59, 24, 53, 22], [59, 25, 53, 23, "initialMetrics"], [59, 39, 53, 37], [59, 42, 53, 40, "initialMetrics"], [59, 56, 53, 54], [60, 2, 54, 0], [60, 6, 54, 6, "styles"], [60, 12, 54, 12], [60, 15, 54, 15, "StyleSheet"], [60, 38, 54, 25], [60, 39, 54, 26, "create"], [60, 45, 54, 32], [60, 46, 54, 33], [61, 4, 55, 2, "container"], [61, 13, 55, 11], [61, 15, 55, 13], [62, 6, 56, 4, "flex"], [62, 10, 56, 8], [62, 12, 56, 10], [63, 4, 57, 2], [64, 2, 58, 0], [64, 3, 58, 1], [64, 4, 58, 2], [65, 0, 58, 3], [65, 3]], "functionMap": {"names": ["<global>", "SafeAreaProviderCompat"], "mappings": "AAA;OC6B;CDsB"}}, "type": "js/module"}]}