{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "expo-asset", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 35, "index": 35}}], "key": "ZXJFWHziJpBZf3W7vl00wXf6fd8=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 36}, "end": {"line": 2, "column": 47, "index": 83}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "./ExpoFontLoader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 84}, "end": {"line": 3, "column": 46, "index": 130}}], "key": "7dk3JQGwGYesJt8OOG3pkBz+dtE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getAssetForSource = getAssetForSource;\n  exports.loadSingleFontAsync = loadSingleFontAsync;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _expoAsset = require(_dependencyMap[2], \"expo-asset\");\n  var _expoModulesCore = require(_dependencyMap[3], \"expo-modules-core\");\n  var _ExpoFontLoader = _interopRequireDefault(require(_dependencyMap[4], \"./ExpoFontLoader\"));\n  function getAssetForSource(source) {\n    if (source instanceof _expoAsset.Asset) {\n      return source;\n    }\n    if (typeof source === 'string') {\n      return _expoAsset.Asset.fromURI(source);\n    } else if (typeof source === 'number') {\n      return _expoAsset.Asset.fromModule(source);\n    } else if (typeof source === 'object' && typeof source.uri !== 'undefined') {\n      return getAssetForSource(source.uri);\n    }\n    // @ts-ignore Error: Type 'string' is not assignable to type 'Asset'\n    // We can't have a string here, we would have thrown an error if !isWeb\n    // or returned Asset.fromModule if isWeb.\n    return source;\n  }\n  function loadSingleFontAsync(_x, _x2) {\n    return _loadSingleFontAsync.apply(this, arguments);\n  }\n  function _loadSingleFontAsync() {\n    _loadSingleFontAsync = (0, _asyncToGenerator2.default)(function* (name, input) {\n      var asset = input;\n      if (!asset.downloadAsync) {\n        throw new _expoModulesCore.CodedError(`ERR_FONT_SOURCE`, '`loadSingleFontAsync` expected resource of type `Asset` from expo-asset on native');\n      }\n      yield asset.downloadAsync();\n      if (!asset.downloaded) {\n        throw new _expoModulesCore.CodedError(`ERR_DOWNLOAD`, `Failed to download asset for font \"${name}\"`);\n      }\n      yield _ExpoFontLoader.default.loadAsync(name, asset.localUri);\n    });\n    return _loadSingleFontAsync.apply(this, arguments);\n  }\n});", "lineCount": 45, "map": [[9, 2, 1, 0], [9, 6, 1, 0, "_expoAsset"], [9, 16, 1, 0], [9, 19, 1, 0, "require"], [9, 26, 1, 0], [9, 27, 1, 0, "_dependencyMap"], [9, 41, 1, 0], [10, 2, 2, 0], [10, 6, 2, 0, "_expoModulesCore"], [10, 22, 2, 0], [10, 25, 2, 0, "require"], [10, 32, 2, 0], [10, 33, 2, 0, "_dependencyMap"], [10, 47, 2, 0], [11, 2, 3, 0], [11, 6, 3, 0, "_ExpoFontLoader"], [11, 21, 3, 0], [11, 24, 3, 0, "_interopRequireDefault"], [11, 46, 3, 0], [11, 47, 3, 0, "require"], [11, 54, 3, 0], [11, 55, 3, 0, "_dependencyMap"], [11, 69, 3, 0], [12, 2, 4, 7], [12, 11, 4, 16, "getAssetForSource"], [12, 28, 4, 33, "getAssetForSource"], [12, 29, 4, 34, "source"], [12, 35, 4, 40], [12, 37, 4, 42], [13, 4, 5, 4], [13, 8, 5, 8, "source"], [13, 14, 5, 14], [13, 26, 5, 26, "<PERSON><PERSON>"], [13, 42, 5, 31], [13, 44, 5, 33], [14, 6, 6, 8], [14, 13, 6, 15, "source"], [14, 19, 6, 21], [15, 4, 7, 4], [16, 4, 8, 4], [16, 8, 8, 8], [16, 15, 8, 15, "source"], [16, 21, 8, 21], [16, 26, 8, 26], [16, 34, 8, 34], [16, 36, 8, 36], [17, 6, 9, 8], [17, 13, 9, 15, "<PERSON><PERSON>"], [17, 29, 9, 20], [17, 30, 9, 21, "fromURI"], [17, 37, 9, 28], [17, 38, 9, 29, "source"], [17, 44, 9, 35], [17, 45, 9, 36], [18, 4, 10, 4], [18, 5, 10, 5], [18, 11, 11, 9], [18, 15, 11, 13], [18, 22, 11, 20, "source"], [18, 28, 11, 26], [18, 33, 11, 31], [18, 41, 11, 39], [18, 43, 11, 41], [19, 6, 12, 8], [19, 13, 12, 15, "<PERSON><PERSON>"], [19, 29, 12, 20], [19, 30, 12, 21, "fromModule"], [19, 40, 12, 31], [19, 41, 12, 32, "source"], [19, 47, 12, 38], [19, 48, 12, 39], [20, 4, 13, 4], [20, 5, 13, 5], [20, 11, 14, 9], [20, 15, 14, 13], [20, 22, 14, 20, "source"], [20, 28, 14, 26], [20, 33, 14, 31], [20, 41, 14, 39], [20, 45, 14, 43], [20, 52, 14, 50, "source"], [20, 58, 14, 56], [20, 59, 14, 57, "uri"], [20, 62, 14, 60], [20, 67, 14, 65], [20, 78, 14, 76], [20, 80, 14, 78], [21, 6, 15, 8], [21, 13, 15, 15, "getAssetForSource"], [21, 30, 15, 32], [21, 31, 15, 33, "source"], [21, 37, 15, 39], [21, 38, 15, 40, "uri"], [21, 41, 15, 43], [21, 42, 15, 44], [22, 4, 16, 4], [23, 4, 17, 4], [24, 4, 18, 4], [25, 4, 19, 4], [26, 4, 20, 4], [26, 11, 20, 11, "source"], [26, 17, 20, 17], [27, 2, 21, 0], [28, 2, 21, 1], [28, 11, 22, 22, "loadSingleFontAsync"], [28, 30, 22, 41, "loadSingleFontAsync"], [28, 31, 22, 41, "_x"], [28, 33, 22, 41], [28, 35, 22, 41, "_x2"], [28, 38, 22, 41], [29, 4, 22, 41], [29, 11, 22, 41, "_loadSingleFontAsync"], [29, 31, 22, 41], [29, 32, 22, 41, "apply"], [29, 37, 22, 41], [29, 44, 22, 41, "arguments"], [29, 53, 22, 41], [30, 2, 22, 41], [31, 2, 22, 41], [31, 11, 22, 41, "_loadSingleFontAsync"], [31, 32, 22, 41], [32, 4, 22, 41, "_loadSingleFontAsync"], [32, 24, 22, 41], [32, 31, 22, 41, "_asyncToGenerator2"], [32, 49, 22, 41], [32, 50, 22, 41, "default"], [32, 57, 22, 41], [32, 59, 22, 7], [32, 70, 22, 42, "name"], [32, 74, 22, 46], [32, 76, 22, 48, "input"], [32, 81, 22, 53], [32, 83, 22, 55], [33, 6, 23, 4], [33, 10, 23, 10, "asset"], [33, 15, 23, 15], [33, 18, 23, 18, "input"], [33, 23, 23, 23], [34, 6, 24, 4], [34, 10, 24, 8], [34, 11, 24, 9, "asset"], [34, 16, 24, 14], [34, 17, 24, 15, "downloadAsync"], [34, 30, 24, 28], [34, 32, 24, 30], [35, 8, 25, 8], [35, 14, 25, 14], [35, 18, 25, 18, "CodedError"], [35, 45, 25, 28], [35, 46, 25, 29], [35, 63, 25, 46], [35, 65, 25, 48], [35, 148, 25, 131], [35, 149, 25, 132], [36, 6, 26, 4], [37, 6, 27, 4], [37, 12, 27, 10, "asset"], [37, 17, 27, 15], [37, 18, 27, 16, "downloadAsync"], [37, 31, 27, 29], [37, 32, 27, 30], [37, 33, 27, 31], [38, 6, 28, 4], [38, 10, 28, 8], [38, 11, 28, 9, "asset"], [38, 16, 28, 14], [38, 17, 28, 15, "downloaded"], [38, 27, 28, 25], [38, 29, 28, 27], [39, 8, 29, 8], [39, 14, 29, 14], [39, 18, 29, 18, "CodedError"], [39, 45, 29, 28], [39, 46, 29, 29], [39, 60, 29, 43], [39, 62, 29, 45], [39, 100, 29, 83, "name"], [39, 104, 29, 87], [39, 107, 29, 90], [39, 108, 29, 91], [40, 6, 30, 4], [41, 6, 31, 4], [41, 12, 31, 10, "ExpoFontLoader"], [41, 35, 31, 24], [41, 36, 31, 25, "loadAsync"], [41, 45, 31, 34], [41, 46, 31, 35, "name"], [41, 50, 31, 39], [41, 52, 31, 41, "asset"], [41, 57, 31, 46], [41, 58, 31, 47, "localUri"], [41, 66, 31, 55], [41, 67, 31, 56], [42, 4, 32, 0], [42, 5, 32, 1], [43, 4, 32, 1], [43, 11, 32, 1, "_loadSingleFontAsync"], [43, 31, 32, 1], [43, 32, 32, 1, "apply"], [43, 37, 32, 1], [43, 44, 32, 1, "arguments"], [43, 53, 32, 1], [44, 2, 32, 1], [45, 0, 32, 1], [45, 3]], "functionMap": {"names": ["<global>", "getAssetForSource", "loadSingleFontAsync"], "mappings": "AAA;OCG;CDiB;OEC;CFU"}}, "type": "js/module"}]}