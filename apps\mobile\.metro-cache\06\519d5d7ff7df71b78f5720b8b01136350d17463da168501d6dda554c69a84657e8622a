{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.checkSerializable = checkSerializable;\n  var checkSerializableWithoutCircularReference = (o, seen, location) => {\n    if (o === undefined || o === null || typeof o === 'boolean' || typeof o === 'number' || typeof o === 'string') {\n      return {\n        serializable: true\n      };\n    }\n    if (Object.prototype.toString.call(o) !== '[object Object]' && !Array.isArray(o)) {\n      return {\n        serializable: false,\n        location,\n        reason: typeof o === 'function' ? 'Function' : String(o)\n      };\n    }\n    if (seen.has(o)) {\n      return {\n        serializable: false,\n        reason: 'Circular reference',\n        location\n      };\n    }\n    seen.add(o);\n    if (Array.isArray(o)) {\n      for (var i = 0; i < o.length; i++) {\n        var childResult = checkSerializableWithoutCircularReference(o[i], new Set(seen), [...location, i]);\n        if (!childResult.serializable) {\n          return childResult;\n        }\n      }\n    } else {\n      for (var key in o) {\n        var _childResult = checkSerializableWithoutCircularReference(o[key], new Set(seen), [...location, key]);\n        if (!_childResult.serializable) {\n          return _childResult;\n        }\n      }\n    }\n    return {\n      serializable: true\n    };\n  };\n  function checkSerializable(o) {\n    return checkSerializableWithoutCircularReference(o, new Set(), []);\n  }\n});", "lineCount": 51, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "checkSerializable"], [7, 27, 1, 13], [7, 30, 1, 13, "checkSerializable"], [7, 47, 1, 13], [8, 2, 3, 0], [8, 6, 3, 6, "checkSerializableWithoutCircularReference"], [8, 47, 3, 47], [8, 50, 3, 50, "checkSerializableWithoutCircularReference"], [8, 51, 3, 51, "o"], [8, 52, 3, 52], [8, 54, 3, 54, "seen"], [8, 58, 3, 58], [8, 60, 3, 60, "location"], [8, 68, 3, 68], [8, 73, 3, 73], [9, 4, 4, 2], [9, 8, 4, 6, "o"], [9, 9, 4, 7], [9, 14, 4, 12, "undefined"], [9, 23, 4, 21], [9, 27, 4, 25, "o"], [9, 28, 4, 26], [9, 33, 4, 31], [9, 37, 4, 35], [9, 41, 4, 39], [9, 48, 4, 46, "o"], [9, 49, 4, 47], [9, 54, 4, 52], [9, 63, 4, 61], [9, 67, 4, 65], [9, 74, 4, 72, "o"], [9, 75, 4, 73], [9, 80, 4, 78], [9, 88, 4, 86], [9, 92, 4, 90], [9, 99, 4, 97, "o"], [9, 100, 4, 98], [9, 105, 4, 103], [9, 113, 4, 111], [9, 115, 4, 113], [10, 6, 5, 4], [10, 13, 5, 11], [11, 8, 6, 6, "serializable"], [11, 20, 6, 18], [11, 22, 6, 20], [12, 6, 7, 4], [12, 7, 7, 5], [13, 4, 8, 2], [14, 4, 9, 2], [14, 8, 9, 6, "Object"], [14, 14, 9, 12], [14, 15, 9, 13, "prototype"], [14, 24, 9, 22], [14, 25, 9, 23, "toString"], [14, 33, 9, 31], [14, 34, 9, 32, "call"], [14, 38, 9, 36], [14, 39, 9, 37, "o"], [14, 40, 9, 38], [14, 41, 9, 39], [14, 46, 9, 44], [14, 63, 9, 61], [14, 67, 9, 65], [14, 68, 9, 66, "Array"], [14, 73, 9, 71], [14, 74, 9, 72, "isArray"], [14, 81, 9, 79], [14, 82, 9, 80, "o"], [14, 83, 9, 81], [14, 84, 9, 82], [14, 86, 9, 84], [15, 6, 10, 4], [15, 13, 10, 11], [16, 8, 11, 6, "serializable"], [16, 20, 11, 18], [16, 22, 11, 20], [16, 27, 11, 25], [17, 8, 12, 6, "location"], [17, 16, 12, 14], [18, 8, 13, 6, "reason"], [18, 14, 13, 12], [18, 16, 13, 14], [18, 23, 13, 21, "o"], [18, 24, 13, 22], [18, 29, 13, 27], [18, 39, 13, 37], [18, 42, 13, 40], [18, 52, 13, 50], [18, 55, 13, 53, "String"], [18, 61, 13, 59], [18, 62, 13, 60, "o"], [18, 63, 13, 61], [19, 6, 14, 4], [19, 7, 14, 5], [20, 4, 15, 2], [21, 4, 16, 2], [21, 8, 16, 6, "seen"], [21, 12, 16, 10], [21, 13, 16, 11, "has"], [21, 16, 16, 14], [21, 17, 16, 15, "o"], [21, 18, 16, 16], [21, 19, 16, 17], [21, 21, 16, 19], [22, 6, 17, 4], [22, 13, 17, 11], [23, 8, 18, 6, "serializable"], [23, 20, 18, 18], [23, 22, 18, 20], [23, 27, 18, 25], [24, 8, 19, 6, "reason"], [24, 14, 19, 12], [24, 16, 19, 14], [24, 36, 19, 34], [25, 8, 20, 6, "location"], [26, 6, 21, 4], [26, 7, 21, 5], [27, 4, 22, 2], [28, 4, 23, 2, "seen"], [28, 8, 23, 6], [28, 9, 23, 7, "add"], [28, 12, 23, 10], [28, 13, 23, 11, "o"], [28, 14, 23, 12], [28, 15, 23, 13], [29, 4, 24, 2], [29, 8, 24, 6, "Array"], [29, 13, 24, 11], [29, 14, 24, 12, "isArray"], [29, 21, 24, 19], [29, 22, 24, 20, "o"], [29, 23, 24, 21], [29, 24, 24, 22], [29, 26, 24, 24], [30, 6, 25, 4], [30, 11, 25, 9], [30, 15, 25, 13, "i"], [30, 16, 25, 14], [30, 19, 25, 17], [30, 20, 25, 18], [30, 22, 25, 20, "i"], [30, 23, 25, 21], [30, 26, 25, 24, "o"], [30, 27, 25, 25], [30, 28, 25, 26, "length"], [30, 34, 25, 32], [30, 36, 25, 34, "i"], [30, 37, 25, 35], [30, 39, 25, 37], [30, 41, 25, 39], [31, 8, 26, 6], [31, 12, 26, 12, "childResult"], [31, 23, 26, 23], [31, 26, 26, 26, "checkSerializableWithoutCircularReference"], [31, 67, 26, 67], [31, 68, 26, 68, "o"], [31, 69, 26, 69], [31, 70, 26, 70, "i"], [31, 71, 26, 71], [31, 72, 26, 72], [31, 74, 26, 74], [31, 78, 26, 78, "Set"], [31, 81, 26, 81], [31, 82, 26, 82, "seen"], [31, 86, 26, 86], [31, 87, 26, 87], [31, 89, 26, 89], [31, 90, 26, 90], [31, 93, 26, 93, "location"], [31, 101, 26, 101], [31, 103, 26, 103, "i"], [31, 104, 26, 104], [31, 105, 26, 105], [31, 106, 26, 106], [32, 8, 27, 6], [32, 12, 27, 10], [32, 13, 27, 11, "childResult"], [32, 24, 27, 22], [32, 25, 27, 23, "serializable"], [32, 37, 27, 35], [32, 39, 27, 37], [33, 10, 28, 8], [33, 17, 28, 15, "childResult"], [33, 28, 28, 26], [34, 8, 29, 6], [35, 6, 30, 4], [36, 4, 31, 2], [36, 5, 31, 3], [36, 11, 31, 9], [37, 6, 32, 4], [37, 11, 32, 9], [37, 15, 32, 15, "key"], [37, 18, 32, 18], [37, 22, 32, 22, "o"], [37, 23, 32, 23], [37, 25, 32, 25], [38, 8, 33, 6], [38, 12, 33, 12, "childResult"], [38, 24, 33, 23], [38, 27, 33, 26, "checkSerializableWithoutCircularReference"], [38, 68, 33, 67], [38, 69, 33, 68, "o"], [38, 70, 33, 69], [38, 71, 33, 70, "key"], [38, 74, 33, 73], [38, 75, 33, 74], [38, 77, 33, 76], [38, 81, 33, 80, "Set"], [38, 84, 33, 83], [38, 85, 33, 84, "seen"], [38, 89, 33, 88], [38, 90, 33, 89], [38, 92, 33, 91], [38, 93, 33, 92], [38, 96, 33, 95, "location"], [38, 104, 33, 103], [38, 106, 33, 105, "key"], [38, 109, 33, 108], [38, 110, 33, 109], [38, 111, 33, 110], [39, 8, 34, 6], [39, 12, 34, 10], [39, 13, 34, 11, "childResult"], [39, 25, 34, 22], [39, 26, 34, 23, "serializable"], [39, 38, 34, 35], [39, 40, 34, 37], [40, 10, 35, 8], [40, 17, 35, 15, "childResult"], [40, 29, 35, 26], [41, 8, 36, 6], [42, 6, 37, 4], [43, 4, 38, 2], [44, 4, 39, 2], [44, 11, 39, 9], [45, 6, 40, 4, "serializable"], [45, 18, 40, 16], [45, 20, 40, 18], [46, 4, 41, 2], [46, 5, 41, 3], [47, 2, 42, 0], [47, 3, 42, 1], [48, 2, 43, 7], [48, 11, 43, 16, "checkSerializable"], [48, 28, 43, 33, "checkSerializable"], [48, 29, 43, 34, "o"], [48, 30, 43, 35], [48, 32, 43, 37], [49, 4, 44, 2], [49, 11, 44, 9, "checkSerializableWithoutCircularReference"], [49, 52, 44, 50], [49, 53, 44, 51, "o"], [49, 54, 44, 52], [49, 56, 44, 54], [49, 60, 44, 58, "Set"], [49, 63, 44, 61], [49, 64, 44, 62], [49, 65, 44, 63], [49, 67, 44, 65], [49, 69, 44, 67], [49, 70, 44, 68], [50, 2, 45, 0], [51, 0, 45, 1], [51, 3]], "functionMap": {"names": ["<global>", "checkSerializableWithoutCircularReference", "checkSerializable"], "mappings": "AAA;kDCE;CDuC;OEC;CFE"}}, "type": "js/module"}]}