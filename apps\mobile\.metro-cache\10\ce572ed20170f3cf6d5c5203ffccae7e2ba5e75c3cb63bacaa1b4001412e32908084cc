{"dependencies": [{"name": "../../../src/private/featureflags/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 101}}], "key": "6CUw6Huzwfao7NX5F1mey95YZ2Q=", "exportNames": ["*"]}}, {"name": "../../../src/private/webapis/dom/nodes/ReactNativeDocument", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 43, "column": 38}, "end": {"line": 43, "column": 107}}], "key": "eElEK9XXAyXMg0yFdv8Hdju1G3s=", "exportNames": ["*"]}}, {"name": "../../../src/private/webapis/dom/nodes/ReactNativeElement", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 74}}], "key": "9Ns/c0aDe27reIGn6GQTXk7Mul8=", "exportNames": ["*"]}}, {"name": "./ReactFabricHostComponent", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 43}}], "key": "jcpASm4AW7FoX3DL8XUJxr4p0Ic=", "exportNames": ["*"]}}, {"name": "../../../src/private/webapis/dom/nodes/ReadOnlyText", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 68}}], "key": "lj75IIdMGuSkNiqA1u10bT58p28=", "exportNames": ["*"]}}, {"name": "../../ReactNative/RendererProxy", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 135, "column": 20}, "end": {"line": 135, "column": 62}}], "key": "T5tpzkUltdJeyXwu85s3KOfgZyI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createPublicInstance = createPublicInstance;\n  exports.createPublicRootInstance = createPublicRootInstance;\n  exports.createPublicTextInstance = createPublicTextInstance;\n  exports.getInternalInstanceHandleFromPublicInstance = getInternalInstanceHandleFromPublicInstance;\n  exports.getNativeTagFromPublicInstance = getNativeTagFromPublicInstance;\n  exports.getNodeFromPublicInstance = getNodeFromPublicInstance;\n  var ReactNativeFeatureFlags = _interopRequireWildcard(require(_dependencyMap[0], \"../../../src/private/featureflags/ReactNativeFeatureFlags\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var ReactNativeDocumentModuleObject;\n  var ReactFabricHostComponentClass;\n  var ReactNativeElementClass;\n  var ReadOnlyTextClass;\n  var RendererProxy;\n  function getReactNativeDocumentModule() {\n    if (ReactNativeDocumentModuleObject == null) {\n      ReactNativeDocumentModuleObject = require(_dependencyMap[1], \"../../../src/private/webapis/dom/nodes/ReactNativeDocument\");\n    }\n    return ReactNativeDocumentModuleObject;\n  }\n  function getReactNativeElementClass() {\n    if (ReactNativeElementClass == null) {\n      ReactNativeElementClass = require(_dependencyMap[2], \"../../../src/private/webapis/dom/nodes/ReactNativeElement\").default;\n    }\n    return ReactNativeElementClass;\n  }\n  function getReactFabricHostComponentClass() {\n    if (ReactFabricHostComponentClass == null) {\n      ReactFabricHostComponentClass = require(_dependencyMap[3], \"./ReactFabricHostComponent\").default;\n    }\n    return ReactFabricHostComponentClass;\n  }\n  function getReadOnlyTextClass() {\n    if (ReadOnlyTextClass == null) {\n      ReadOnlyTextClass = require(_dependencyMap[4], \"../../../src/private/webapis/dom/nodes/ReadOnlyText\").default;\n    }\n    return ReadOnlyTextClass;\n  }\n  function createPublicRootInstance(rootTag) {\n    if (ReactNativeFeatureFlags.enableAccessToHostTreeInFabric() && ReactNativeFeatureFlags.enableDOMDocumentAPI()) {\n      var ReactNativeDocumentModule = getReactNativeDocumentModule();\n      return ReactNativeDocumentModule.createReactNativeDocument(rootTag);\n    }\n    return null;\n  }\n  function createPublicInstance(tag, viewConfig, internalInstanceHandle, ownerDocument) {\n    if (ReactNativeFeatureFlags.enableAccessToHostTreeInFabric()) {\n      var ReactNativeElement = getReactNativeElementClass();\n      return new ReactNativeElement(tag, viewConfig, internalInstanceHandle, ownerDocument);\n    } else {\n      var ReactFabricHostComponent = getReactFabricHostComponentClass();\n      return new ReactFabricHostComponent(tag, viewConfig, internalInstanceHandle);\n    }\n  }\n  function createPublicTextInstance(internalInstanceHandle, ownerDocument) {\n    var ReadOnlyText = getReadOnlyTextClass();\n    return new ReadOnlyText(internalInstanceHandle, ownerDocument);\n  }\n  function getNativeTagFromPublicInstance(publicInstance) {\n    return publicInstance.__nativeTag;\n  }\n  function getNodeFromPublicInstance(publicInstance) {\n    if (publicInstance.__internalInstanceHandle == null) {\n      return null;\n    }\n    if (RendererProxy == null) {\n      RendererProxy = require(_dependencyMap[5], \"../../ReactNative/RendererProxy\");\n    }\n    return RendererProxy.getNodeFromInternalInstanceHandle(publicInstance.__internalInstanceHandle);\n  }\n  function getInternalInstanceHandleFromPublicInstance(publicInstance) {\n    if (publicInstance._internalInstanceHandle != null) {\n      return publicInstance._internalInstanceHandle;\n    }\n    return publicInstance.__internalInstanceHandle;\n  }\n});", "lineCount": 80, "map": [[11, 2, 29, 0], [11, 6, 29, 0, "ReactNativeFeatureFlags"], [11, 29, 29, 0], [11, 32, 29, 0, "_interopRequireWildcard"], [11, 55, 29, 0], [11, 56, 29, 0, "require"], [11, 63, 29, 0], [11, 64, 29, 0, "_dependencyMap"], [11, 78, 29, 0], [12, 2, 29, 101], [12, 11, 29, 101, "_interopRequireWildcard"], [12, 35, 29, 101, "e"], [12, 36, 29, 101], [12, 38, 29, 101, "t"], [12, 39, 29, 101], [12, 68, 29, 101, "WeakMap"], [12, 75, 29, 101], [12, 81, 29, 101, "r"], [12, 82, 29, 101], [12, 89, 29, 101, "WeakMap"], [12, 96, 29, 101], [12, 100, 29, 101, "n"], [12, 101, 29, 101], [12, 108, 29, 101, "WeakMap"], [12, 115, 29, 101], [12, 127, 29, 101, "_interopRequireWildcard"], [12, 150, 29, 101], [12, 162, 29, 101, "_interopRequireWildcard"], [12, 163, 29, 101, "e"], [12, 164, 29, 101], [12, 166, 29, 101, "t"], [12, 167, 29, 101], [12, 176, 29, 101, "t"], [12, 177, 29, 101], [12, 181, 29, 101, "e"], [12, 182, 29, 101], [12, 186, 29, 101, "e"], [12, 187, 29, 101], [12, 188, 29, 101, "__esModule"], [12, 198, 29, 101], [12, 207, 29, 101, "e"], [12, 208, 29, 101], [12, 214, 29, 101, "o"], [12, 215, 29, 101], [12, 217, 29, 101, "i"], [12, 218, 29, 101], [12, 220, 29, 101, "f"], [12, 221, 29, 101], [12, 226, 29, 101, "__proto__"], [12, 235, 29, 101], [12, 243, 29, 101, "default"], [12, 250, 29, 101], [12, 252, 29, 101, "e"], [12, 253, 29, 101], [12, 270, 29, 101, "e"], [12, 271, 29, 101], [12, 294, 29, 101, "e"], [12, 295, 29, 101], [12, 320, 29, 101, "e"], [12, 321, 29, 101], [12, 330, 29, 101, "f"], [12, 331, 29, 101], [12, 337, 29, 101, "o"], [12, 338, 29, 101], [12, 341, 29, 101, "t"], [12, 342, 29, 101], [12, 345, 29, 101, "n"], [12, 346, 29, 101], [12, 349, 29, 101, "r"], [12, 350, 29, 101], [12, 358, 29, 101, "o"], [12, 359, 29, 101], [12, 360, 29, 101, "has"], [12, 363, 29, 101], [12, 364, 29, 101, "e"], [12, 365, 29, 101], [12, 375, 29, 101, "o"], [12, 376, 29, 101], [12, 377, 29, 101, "get"], [12, 380, 29, 101], [12, 381, 29, 101, "e"], [12, 382, 29, 101], [12, 385, 29, 101, "o"], [12, 386, 29, 101], [12, 387, 29, 101, "set"], [12, 390, 29, 101], [12, 391, 29, 101, "e"], [12, 392, 29, 101], [12, 394, 29, 101, "f"], [12, 395, 29, 101], [12, 409, 29, 101, "_t"], [12, 411, 29, 101], [12, 415, 29, 101, "e"], [12, 416, 29, 101], [12, 432, 29, 101, "_t"], [12, 434, 29, 101], [12, 441, 29, 101, "hasOwnProperty"], [12, 455, 29, 101], [12, 456, 29, 101, "call"], [12, 460, 29, 101], [12, 461, 29, 101, "e"], [12, 462, 29, 101], [12, 464, 29, 101, "_t"], [12, 466, 29, 101], [12, 473, 29, 101, "i"], [12, 474, 29, 101], [12, 478, 29, 101, "o"], [12, 479, 29, 101], [12, 482, 29, 101, "Object"], [12, 488, 29, 101], [12, 489, 29, 101, "defineProperty"], [12, 503, 29, 101], [12, 508, 29, 101, "Object"], [12, 514, 29, 101], [12, 515, 29, 101, "getOwnPropertyDescriptor"], [12, 539, 29, 101], [12, 540, 29, 101, "e"], [12, 541, 29, 101], [12, 543, 29, 101, "_t"], [12, 545, 29, 101], [12, 552, 29, 101, "i"], [12, 553, 29, 101], [12, 554, 29, 101, "get"], [12, 557, 29, 101], [12, 561, 29, 101, "i"], [12, 562, 29, 101], [12, 563, 29, 101, "set"], [12, 566, 29, 101], [12, 570, 29, 101, "o"], [12, 571, 29, 101], [12, 572, 29, 101, "f"], [12, 573, 29, 101], [12, 575, 29, 101, "_t"], [12, 577, 29, 101], [12, 579, 29, 101, "i"], [12, 580, 29, 101], [12, 584, 29, 101, "f"], [12, 585, 29, 101], [12, 586, 29, 101, "_t"], [12, 588, 29, 101], [12, 592, 29, 101, "e"], [12, 593, 29, 101], [12, 594, 29, 101, "_t"], [12, 596, 29, 101], [12, 607, 29, 101, "f"], [12, 608, 29, 101], [12, 613, 29, 101, "e"], [12, 614, 29, 101], [12, 616, 29, 101, "t"], [12, 617, 29, 101], [13, 2, 34, 0], [13, 6, 34, 4, "ReactNativeDocumentModuleObject"], [13, 37, 34, 64], [14, 2, 35, 0], [14, 6, 35, 4, "ReactFabricHostComponentClass"], [14, 35, 35, 67], [15, 2, 36, 0], [15, 6, 36, 4, "ReactNativeElementClass"], [15, 29, 36, 55], [16, 2, 37, 0], [16, 6, 37, 4, "ReadOnlyTextClass"], [16, 23, 37, 43], [17, 2, 38, 0], [17, 6, 38, 4, "RendererProxy"], [17, 19, 38, 33], [18, 2, 40, 0], [18, 11, 40, 9, "getReactNativeDocumentModule"], [18, 39, 40, 37, "getReactNativeDocumentModule"], [18, 40, 40, 37], [18, 42, 40, 68], [19, 4, 41, 2], [19, 8, 41, 6, "ReactNativeDocumentModuleObject"], [19, 39, 41, 37], [19, 43, 41, 41], [19, 47, 41, 45], [19, 49, 41, 47], [20, 6, 43, 4, "ReactNativeDocumentModuleObject"], [20, 37, 43, 35], [20, 40, 43, 38, "require"], [20, 47, 43, 45], [20, 48, 43, 45, "_dependencyMap"], [20, 62, 43, 45], [20, 127, 43, 106], [20, 128, 43, 107], [21, 4, 44, 2], [22, 4, 46, 2], [22, 11, 46, 9, "ReactNativeDocumentModuleObject"], [22, 42, 46, 40], [23, 2, 47, 0], [24, 2, 49, 0], [24, 11, 49, 9, "getReactNativeElementClass"], [24, 37, 49, 35, "getReactNativeElementClass"], [24, 38, 49, 35], [24, 40, 49, 66], [25, 4, 50, 2], [25, 8, 50, 6, "ReactNativeElementClass"], [25, 31, 50, 29], [25, 35, 50, 33], [25, 39, 50, 37], [25, 41, 50, 39], [26, 6, 51, 4, "ReactNativeElementClass"], [26, 29, 51, 27], [26, 32, 52, 6, "require"], [26, 39, 52, 13], [26, 40, 52, 13, "_dependencyMap"], [26, 54, 52, 13], [26, 118, 52, 73], [26, 119, 52, 74], [26, 120, 52, 75, "default"], [26, 127, 52, 82], [27, 4, 53, 2], [28, 4, 54, 2], [28, 11, 54, 9, "ReactNativeElementClass"], [28, 34, 54, 32], [29, 2, 55, 0], [30, 2, 57, 0], [30, 11, 57, 9, "getReactFabricHostComponentClass"], [30, 43, 57, 41, "getReactFabricHostComponentClass"], [30, 44, 57, 41], [30, 46, 57, 78], [31, 4, 58, 2], [31, 8, 58, 6, "ReactFabricHostComponentClass"], [31, 37, 58, 35], [31, 41, 58, 39], [31, 45, 58, 43], [31, 47, 58, 45], [32, 6, 59, 4, "ReactFabricHostComponentClass"], [32, 35, 59, 33], [32, 38, 60, 6, "require"], [32, 45, 60, 13], [32, 46, 60, 13, "_dependencyMap"], [32, 60, 60, 13], [32, 93, 60, 42], [32, 94, 60, 43], [32, 95, 60, 44, "default"], [32, 102, 60, 51], [33, 4, 61, 2], [34, 4, 62, 2], [34, 11, 62, 9, "ReactFabricHostComponentClass"], [34, 40, 62, 38], [35, 2, 63, 0], [36, 2, 65, 0], [36, 11, 65, 9, "getReadOnlyTextClass"], [36, 31, 65, 29, "getReadOnlyTextClass"], [36, 32, 65, 29], [36, 34, 65, 54], [37, 4, 66, 2], [37, 8, 66, 6, "ReadOnlyTextClass"], [37, 25, 66, 23], [37, 29, 66, 27], [37, 33, 66, 31], [37, 35, 66, 33], [38, 6, 67, 4, "ReadOnlyTextClass"], [38, 23, 67, 21], [38, 26, 68, 6, "require"], [38, 33, 68, 13], [38, 34, 68, 13, "_dependencyMap"], [38, 48, 68, 13], [38, 106, 68, 67], [38, 107, 68, 68], [38, 108, 68, 69, "default"], [38, 115, 68, 76], [39, 4, 69, 2], [40, 4, 70, 2], [40, 11, 70, 9, "ReadOnlyTextClass"], [40, 28, 70, 26], [41, 2, 71, 0], [42, 2, 73, 7], [42, 11, 73, 16, "createPublicRootInstance"], [42, 35, 73, 40, "createPublicRootInstance"], [42, 36, 73, 41, "rootTag"], [42, 43, 73, 57], [42, 45, 73, 79], [43, 4, 74, 2], [43, 8, 75, 4, "ReactNativeFeatureFlags"], [43, 31, 75, 27], [43, 32, 75, 28, "enableAccessToHostTreeInFabric"], [43, 62, 75, 58], [43, 63, 75, 59], [43, 64, 75, 60], [43, 68, 76, 4, "ReactNativeFeatureFlags"], [43, 91, 76, 27], [43, 92, 76, 28, "enableDOMDocumentAPI"], [43, 112, 76, 48], [43, 113, 76, 49], [43, 114, 76, 50], [43, 116, 77, 4], [44, 6, 78, 4], [44, 10, 78, 10, "ReactNativeDocumentModule"], [44, 35, 78, 35], [44, 38, 78, 38, "getReactNativeDocumentModule"], [44, 66, 78, 66], [44, 67, 78, 67], [44, 68, 78, 68], [45, 6, 81, 4], [45, 13, 81, 11, "ReactNativeDocumentModule"], [45, 38, 81, 36], [45, 39, 81, 37, "createReactNativeDocument"], [45, 64, 81, 62], [45, 65, 81, 63, "rootTag"], [45, 72, 81, 70], [45, 73, 81, 71], [46, 4, 82, 2], [47, 4, 85, 2], [47, 11, 85, 9], [47, 15, 85, 13], [48, 2, 86, 0], [49, 2, 88, 7], [49, 11, 88, 16, "createPublicInstance"], [49, 31, 88, 36, "createPublicInstance"], [49, 32, 89, 2, "tag"], [49, 35, 89, 13], [49, 37, 90, 2, "viewConfig"], [49, 47, 90, 24], [49, 49, 91, 2, "internalInstanceHandle"], [49, 71, 91, 48], [49, 73, 92, 2, "ownerDocument"], [49, 86, 92, 37], [49, 88, 93, 51], [50, 4, 94, 2], [50, 8, 94, 6, "ReactNativeFeatureFlags"], [50, 31, 94, 29], [50, 32, 94, 30, "enableAccessToHostTreeInFabric"], [50, 62, 94, 60], [50, 63, 94, 61], [50, 64, 94, 62], [50, 66, 94, 64], [51, 6, 95, 4], [51, 10, 95, 10, "ReactNativeElement"], [51, 28, 95, 28], [51, 31, 95, 31, "getReactNativeElementClass"], [51, 57, 95, 57], [51, 58, 95, 58], [51, 59, 95, 59], [52, 6, 96, 4], [52, 13, 96, 11], [52, 17, 96, 15, "ReactNativeElement"], [52, 35, 96, 33], [52, 36, 97, 6, "tag"], [52, 39, 97, 9], [52, 41, 98, 6, "viewConfig"], [52, 51, 98, 16], [52, 53, 99, 6, "internalInstanceHandle"], [52, 75, 99, 28], [52, 77, 100, 6, "ownerDocument"], [52, 90, 101, 4], [52, 91, 101, 5], [53, 4, 102, 2], [53, 5, 102, 3], [53, 11, 102, 9], [54, 6, 103, 4], [54, 10, 103, 10, "ReactFabricHostComponent"], [54, 34, 103, 34], [54, 37, 103, 37, "getReactFabricHostComponentClass"], [54, 69, 103, 69], [54, 70, 103, 70], [54, 71, 103, 71], [55, 6, 104, 4], [55, 13, 104, 11], [55, 17, 104, 15, "ReactFabricHostComponent"], [55, 41, 104, 39], [55, 42, 105, 6, "tag"], [55, 45, 105, 9], [55, 47, 106, 6, "viewConfig"], [55, 57, 106, 16], [55, 59, 107, 6, "internalInstanceHandle"], [55, 81, 108, 4], [55, 82, 108, 5], [56, 4, 109, 2], [57, 2, 110, 0], [58, 2, 112, 7], [58, 11, 112, 16, "createPublicTextInstance"], [58, 35, 112, 40, "createPublicTextInstance"], [58, 36, 113, 2, "internalInstanceHandle"], [58, 58, 113, 48], [58, 60, 114, 2, "ownerDocument"], [58, 73, 114, 37], [58, 75, 115, 17], [59, 4, 116, 2], [59, 8, 116, 8, "ReadOnlyText"], [59, 20, 116, 20], [59, 23, 116, 23, "getReadOnlyTextClass"], [59, 43, 116, 43], [59, 44, 116, 44], [59, 45, 116, 45], [60, 4, 117, 2], [60, 11, 117, 9], [60, 15, 117, 13, "ReadOnlyText"], [60, 27, 117, 25], [60, 28, 117, 26, "internalInstanceHandle"], [60, 50, 117, 48], [60, 52, 117, 50, "ownerDocument"], [60, 65, 117, 63], [60, 66, 117, 64], [61, 2, 118, 0], [62, 2, 120, 7], [62, 11, 120, 16, "getNativeTagFromPublicInstance"], [62, 41, 120, 46, "getNativeTagFromPublicInstance"], [62, 42, 121, 2, "publicInstance"], [62, 56, 121, 65], [62, 58, 122, 10], [63, 4, 123, 2], [63, 11, 123, 9, "publicInstance"], [63, 25, 123, 23], [63, 26, 123, 24, "__nativeTag"], [63, 37, 123, 35], [64, 2, 124, 0], [65, 2, 126, 7], [65, 11, 126, 16, "getNodeFromPublicInstance"], [65, 36, 126, 41, "getNodeFromPublicInstance"], [65, 37, 127, 2, "publicInstance"], [65, 51, 127, 65], [65, 53, 128, 9], [66, 4, 130, 2], [66, 8, 130, 6, "publicInstance"], [66, 22, 130, 20], [66, 23, 130, 21, "__internalInstanceHandle"], [66, 47, 130, 45], [66, 51, 130, 49], [66, 55, 130, 53], [66, 57, 130, 55], [67, 6, 131, 4], [67, 13, 131, 11], [67, 17, 131, 15], [68, 4, 132, 2], [69, 4, 134, 2], [69, 8, 134, 6, "RendererProxy"], [69, 21, 134, 19], [69, 25, 134, 23], [69, 29, 134, 27], [69, 31, 134, 29], [70, 6, 135, 4, "RendererProxy"], [70, 19, 135, 17], [70, 22, 135, 20, "require"], [70, 29, 135, 27], [70, 30, 135, 27, "_dependencyMap"], [70, 44, 135, 27], [70, 82, 135, 61], [70, 83, 135, 62], [71, 4, 136, 2], [72, 4, 137, 2], [72, 11, 137, 9, "RendererProxy"], [72, 24, 137, 22], [72, 25, 137, 23, "getNodeFromInternalInstanceHandle"], [72, 58, 137, 56], [72, 59, 139, 4, "publicInstance"], [72, 73, 139, 18], [72, 74, 139, 19, "__internalInstanceHandle"], [72, 98, 140, 2], [72, 99, 140, 3], [73, 2, 141, 0], [74, 2, 143, 7], [74, 11, 143, 16, "getInternalInstanceHandleFromPublicInstance"], [74, 54, 143, 59, "getInternalInstanceHandleFromPublicInstance"], [74, 55, 144, 2, "publicInstance"], [74, 69, 144, 65], [74, 71, 145, 26], [75, 4, 148, 2], [75, 8, 148, 6, "publicInstance"], [75, 22, 148, 20], [75, 23, 148, 21, "_internalInstanceHandle"], [75, 46, 148, 44], [75, 50, 148, 48], [75, 54, 148, 52], [75, 56, 148, 54], [76, 6, 150, 4], [76, 13, 150, 11, "publicInstance"], [76, 27, 150, 25], [76, 28, 150, 26, "_internalInstanceHandle"], [76, 51, 150, 49], [77, 4, 151, 2], [78, 4, 154, 2], [78, 11, 154, 9, "publicInstance"], [78, 25, 154, 23], [78, 26, 154, 24, "__internalInstanceHandle"], [78, 50, 154, 48], [79, 2, 155, 0], [80, 0, 155, 1], [80, 3]], "functionMap": {"names": ["<global>", "getReactNativeDocumentModule", "getReactNativeElementClass", "getReactFabricHostComponentClass", "getReadOnlyTextClass", "createPublicRootInstance", "createPublicInstance", "createPublicTextInstance", "getNativeTagFromPublicInstance", "getNodeFromPublicInstance", "getInternalInstanceHandleFromPublicInstance"], "mappings": "AAA;ACuC;CDO;AEE;CFM;AGE;CHM;AIE;CJM;OKE;CLa;OME;CNsB;OOE;CPM;OQE;CRI;OSE;CTe;OUE"}}, "type": "js/module"}]}