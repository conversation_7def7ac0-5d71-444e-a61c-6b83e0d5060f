{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 50, "index": 50}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Freeze = Freeze;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-freeze\\\\src\\\\index.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var infiniteThenable = {\n    then() {}\n  };\n  function Suspender(_ref) {\n    var freeze = _ref.freeze,\n      children = _ref.children;\n    if (freeze) {\n      throw infiniteThenable;\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_react.Fragment, {\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 10\n    }, this);\n  }\n  function Freeze(_ref2) {\n    var freeze = _ref2.freeze,\n      children = _ref2.children,\n      _ref2$placeholder = _ref2.placeholder,\n      placeholder = _ref2$placeholder === void 0 ? null : _ref2$placeholder;\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_react.Suspense, {\n      fallback: placeholder,\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Suspender, {\n        freeze: freeze,\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 5\n    }, this);\n  }\n});", "lineCount": 48, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 50], [7, 6, 1, 50, "_jsxDevRuntime"], [7, 20, 1, 50], [7, 23, 1, 50, "require"], [7, 30, 1, 50], [7, 31, 1, 50, "_dependencyMap"], [7, 45, 1, 50], [8, 2, 1, 50], [8, 6, 1, 50, "_jsxFileName"], [8, 18, 1, 50], [9, 2, 1, 50], [9, 11, 1, 50, "_interopRequireWildcard"], [9, 35, 1, 50, "e"], [9, 36, 1, 50], [9, 38, 1, 50, "t"], [9, 39, 1, 50], [9, 68, 1, 50, "WeakMap"], [9, 75, 1, 50], [9, 81, 1, 50, "r"], [9, 82, 1, 50], [9, 89, 1, 50, "WeakMap"], [9, 96, 1, 50], [9, 100, 1, 50, "n"], [9, 101, 1, 50], [9, 108, 1, 50, "WeakMap"], [9, 115, 1, 50], [9, 127, 1, 50, "_interopRequireWildcard"], [9, 150, 1, 50], [9, 162, 1, 50, "_interopRequireWildcard"], [9, 163, 1, 50, "e"], [9, 164, 1, 50], [9, 166, 1, 50, "t"], [9, 167, 1, 50], [9, 176, 1, 50, "t"], [9, 177, 1, 50], [9, 181, 1, 50, "e"], [9, 182, 1, 50], [9, 186, 1, 50, "e"], [9, 187, 1, 50], [9, 188, 1, 50, "__esModule"], [9, 198, 1, 50], [9, 207, 1, 50, "e"], [9, 208, 1, 50], [9, 214, 1, 50, "o"], [9, 215, 1, 50], [9, 217, 1, 50, "i"], [9, 218, 1, 50], [9, 220, 1, 50, "f"], [9, 221, 1, 50], [9, 226, 1, 50, "__proto__"], [9, 235, 1, 50], [9, 243, 1, 50, "default"], [9, 250, 1, 50], [9, 252, 1, 50, "e"], [9, 253, 1, 50], [9, 270, 1, 50, "e"], [9, 271, 1, 50], [9, 294, 1, 50, "e"], [9, 295, 1, 50], [9, 320, 1, 50, "e"], [9, 321, 1, 50], [9, 330, 1, 50, "f"], [9, 331, 1, 50], [9, 337, 1, 50, "o"], [9, 338, 1, 50], [9, 341, 1, 50, "t"], [9, 342, 1, 50], [9, 345, 1, 50, "n"], [9, 346, 1, 50], [9, 349, 1, 50, "r"], [9, 350, 1, 50], [9, 358, 1, 50, "o"], [9, 359, 1, 50], [9, 360, 1, 50, "has"], [9, 363, 1, 50], [9, 364, 1, 50, "e"], [9, 365, 1, 50], [9, 375, 1, 50, "o"], [9, 376, 1, 50], [9, 377, 1, 50, "get"], [9, 380, 1, 50], [9, 381, 1, 50, "e"], [9, 382, 1, 50], [9, 385, 1, 50, "o"], [9, 386, 1, 50], [9, 387, 1, 50, "set"], [9, 390, 1, 50], [9, 391, 1, 50, "e"], [9, 392, 1, 50], [9, 394, 1, 50, "f"], [9, 395, 1, 50], [9, 409, 1, 50, "_t"], [9, 411, 1, 50], [9, 415, 1, 50, "e"], [9, 416, 1, 50], [9, 432, 1, 50, "_t"], [9, 434, 1, 50], [9, 441, 1, 50, "hasOwnProperty"], [9, 455, 1, 50], [9, 456, 1, 50, "call"], [9, 460, 1, 50], [9, 461, 1, 50, "e"], [9, 462, 1, 50], [9, 464, 1, 50, "_t"], [9, 466, 1, 50], [9, 473, 1, 50, "i"], [9, 474, 1, 50], [9, 478, 1, 50, "o"], [9, 479, 1, 50], [9, 482, 1, 50, "Object"], [9, 488, 1, 50], [9, 489, 1, 50, "defineProperty"], [9, 503, 1, 50], [9, 508, 1, 50, "Object"], [9, 514, 1, 50], [9, 515, 1, 50, "getOwnPropertyDescriptor"], [9, 539, 1, 50], [9, 540, 1, 50, "e"], [9, 541, 1, 50], [9, 543, 1, 50, "_t"], [9, 545, 1, 50], [9, 552, 1, 50, "i"], [9, 553, 1, 50], [9, 554, 1, 50, "get"], [9, 557, 1, 50], [9, 561, 1, 50, "i"], [9, 562, 1, 50], [9, 563, 1, 50, "set"], [9, 566, 1, 50], [9, 570, 1, 50, "o"], [9, 571, 1, 50], [9, 572, 1, 50, "f"], [9, 573, 1, 50], [9, 575, 1, 50, "_t"], [9, 577, 1, 50], [9, 579, 1, 50, "i"], [9, 580, 1, 50], [9, 584, 1, 50, "f"], [9, 585, 1, 50], [9, 586, 1, 50, "_t"], [9, 588, 1, 50], [9, 592, 1, 50, "e"], [9, 593, 1, 50], [9, 594, 1, 50, "_t"], [9, 596, 1, 50], [9, 607, 1, 50, "f"], [9, 608, 1, 50], [9, 613, 1, 50, "e"], [9, 614, 1, 50], [9, 616, 1, 50, "t"], [9, 617, 1, 50], [10, 2, 3, 0], [10, 6, 3, 6, "infiniteThenable"], [10, 22, 3, 22], [10, 25, 3, 25], [11, 4, 3, 27, "then"], [11, 8, 3, 31, "then"], [11, 9, 3, 31], [11, 11, 3, 34], [11, 12, 3, 35], [12, 2, 3, 37], [12, 3, 3, 38], [13, 2, 5, 0], [13, 11, 5, 9, "Suspender"], [13, 20, 5, 18, "Suspender"], [13, 21, 5, 18, "_ref"], [13, 25, 5, 18], [13, 27, 11, 3], [14, 4, 11, 3], [14, 8, 6, 2, "freeze"], [14, 14, 6, 8], [14, 17, 6, 8, "_ref"], [14, 21, 6, 8], [14, 22, 6, 2, "freeze"], [14, 28, 6, 8], [15, 6, 7, 2, "children"], [15, 14, 7, 10], [15, 17, 7, 10, "_ref"], [15, 21, 7, 10], [15, 22, 7, 2, "children"], [15, 30, 7, 10], [16, 4, 12, 2], [16, 8, 12, 6, "freeze"], [16, 14, 12, 12], [16, 16, 12, 14], [17, 6, 13, 4], [17, 12, 13, 10, "infiniteThenable"], [17, 28, 13, 26], [18, 4, 14, 2], [19, 4, 15, 2], [19, 24, 15, 9], [19, 28, 15, 9, "_jsxDevRuntime"], [19, 42, 15, 9], [19, 43, 15, 9, "jsxDEV"], [19, 49, 15, 9], [19, 51, 15, 10, "_react"], [19, 57, 15, 10], [19, 58, 15, 10, "Fragment"], [19, 66, 15, 18], [20, 6, 15, 18, "children"], [20, 14, 15, 18], [20, 16, 15, 20, "children"], [21, 4, 15, 28], [22, 6, 15, 28, "fileName"], [22, 14, 15, 28], [22, 16, 15, 28, "_jsxFileName"], [22, 28, 15, 28], [23, 6, 15, 28, "lineNumber"], [23, 16, 15, 28], [24, 6, 15, 28, "columnNumber"], [24, 18, 15, 28], [25, 4, 15, 28], [25, 11, 15, 39], [25, 12, 15, 40], [26, 2, 16, 0], [27, 2, 24, 7], [27, 11, 24, 16, "Freeze"], [27, 17, 24, 22, "Freeze"], [27, 18, 24, 22, "_ref2"], [27, 23, 24, 22], [27, 25, 24, 72], [28, 4, 24, 72], [28, 8, 24, 25, "freeze"], [28, 14, 24, 31], [28, 17, 24, 31, "_ref2"], [28, 22, 24, 31], [28, 23, 24, 25, "freeze"], [28, 29, 24, 31], [29, 6, 24, 33, "children"], [29, 14, 24, 41], [29, 17, 24, 41, "_ref2"], [29, 22, 24, 41], [29, 23, 24, 33, "children"], [29, 31, 24, 41], [30, 6, 24, 41, "_ref2$placeholder"], [30, 23, 24, 41], [30, 26, 24, 41, "_ref2"], [30, 31, 24, 41], [30, 32, 24, 43, "placeholder"], [30, 43, 24, 54], [31, 6, 24, 43, "placeholder"], [31, 17, 24, 54], [31, 20, 24, 54, "_ref2$placeholder"], [31, 37, 24, 54], [31, 51, 24, 57], [31, 55, 24, 61], [31, 58, 24, 61, "_ref2$placeholder"], [31, 75, 24, 61], [32, 4, 25, 2], [32, 24, 26, 4], [32, 28, 26, 4, "_jsxDevRuntime"], [32, 42, 26, 4], [32, 43, 26, 4, "jsxDEV"], [32, 49, 26, 4], [32, 51, 26, 5, "_react"], [32, 57, 26, 5], [32, 58, 26, 5, "Suspense"], [32, 66, 26, 13], [33, 6, 26, 14, "fallback"], [33, 14, 26, 22], [33, 16, 26, 24, "placeholder"], [33, 27, 26, 36], [34, 6, 26, 36, "children"], [34, 14, 26, 36], [34, 29, 27, 6], [34, 33, 27, 6, "_jsxDevRuntime"], [34, 47, 27, 6], [34, 48, 27, 6, "jsxDEV"], [34, 54, 27, 6], [34, 56, 27, 7, "Suspender"], [34, 65, 27, 16], [35, 8, 27, 17, "freeze"], [35, 14, 27, 23], [35, 16, 27, 25, "freeze"], [35, 22, 27, 32], [36, 8, 27, 32, "children"], [36, 16, 27, 32], [36, 18, 27, 34, "children"], [37, 6, 27, 42], [38, 8, 27, 42, "fileName"], [38, 16, 27, 42], [38, 18, 27, 42, "_jsxFileName"], [38, 30, 27, 42], [39, 8, 27, 42, "lineNumber"], [39, 18, 27, 42], [40, 8, 27, 42, "columnNumber"], [40, 20, 27, 42], [41, 6, 27, 42], [41, 13, 27, 54], [42, 4, 27, 55], [43, 6, 27, 55, "fileName"], [43, 14, 27, 55], [43, 16, 27, 55, "_jsxFileName"], [43, 28, 27, 55], [44, 6, 27, 55, "lineNumber"], [44, 16, 27, 55], [45, 6, 27, 55, "columnNumber"], [45, 18, 27, 55], [46, 4, 27, 55], [46, 11, 28, 14], [46, 12, 28, 15], [47, 2, 30, 0], [48, 0, 30, 1], [48, 3]], "functionMap": {"names": ["<global>", "infiniteThenable.then", "Suspender", "Freeze"], "mappings": "AAA;2BCE,SD;AEE;CFW;OGQ;CHM"}}, "type": "js/module"}]}