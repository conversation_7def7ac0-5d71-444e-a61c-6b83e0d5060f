{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./CodedError", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 42, "index": 42}}], "key": "ejEJzFfKvz0N9hBbejjoG+76cK8=", "exportNames": ["*"]}}, {"name": "../Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 43}, "end": {"line": 2, "column": 35, "index": 78}}], "key": "SkcN7Zi2IL0pUxWZCaWeI65icek=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.UnavailabilityError = void 0;\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/createClass\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _CodedError2 = require(_dependencyMap[6], \"./CodedError\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[7], \"../Platform\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  /**\n   * A class for errors to be thrown when a property is accessed which is\n   * unavailable, unsupported, or not currently implemented on the running\n   * platform.\n   */\n  var UnavailabilityError = exports.UnavailabilityError = /*#__PURE__*/function (_CodedError) {\n    function UnavailabilityError(moduleName, propertyName) {\n      (0, _classCallCheck2.default)(this, UnavailabilityError);\n      return _callSuper(this, UnavailabilityError, ['ERR_UNAVAILABLE', `The method or property ${moduleName}.${propertyName} is not available on ${_Platform.default.OS}, are you sure you've linked all the native dependencies properly?`]);\n    }\n    (0, _inherits2.default)(UnavailabilityError, _CodedError);\n    return (0, _createClass2.default)(UnavailabilityError);\n  }(_CodedError2.CodedError);\n});", "lineCount": 29, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_CodedError2"], [12, 18, 1, 0], [12, 21, 1, 0, "require"], [12, 28, 1, 0], [12, 29, 1, 0, "_dependencyMap"], [12, 43, 1, 0], [13, 2, 2, 0], [13, 6, 2, 0, "_Platform"], [13, 15, 2, 0], [13, 18, 2, 0, "_interopRequireDefault"], [13, 40, 2, 0], [13, 41, 2, 0, "require"], [13, 48, 2, 0], [13, 49, 2, 0, "_dependencyMap"], [13, 63, 2, 0], [14, 2, 2, 35], [14, 11, 2, 35, "_callSuper"], [14, 22, 2, 35, "t"], [14, 23, 2, 35], [14, 25, 2, 35, "o"], [14, 26, 2, 35], [14, 28, 2, 35, "e"], [14, 29, 2, 35], [14, 40, 2, 35, "o"], [14, 41, 2, 35], [14, 48, 2, 35, "_getPrototypeOf2"], [14, 64, 2, 35], [14, 65, 2, 35, "default"], [14, 72, 2, 35], [14, 74, 2, 35, "o"], [14, 75, 2, 35], [14, 82, 2, 35, "_possibleConstructorReturn2"], [14, 109, 2, 35], [14, 110, 2, 35, "default"], [14, 117, 2, 35], [14, 119, 2, 35, "t"], [14, 120, 2, 35], [14, 122, 2, 35, "_isNativeReflectConstruct"], [14, 147, 2, 35], [14, 152, 2, 35, "Reflect"], [14, 159, 2, 35], [14, 160, 2, 35, "construct"], [14, 169, 2, 35], [14, 170, 2, 35, "o"], [14, 171, 2, 35], [14, 173, 2, 35, "e"], [14, 174, 2, 35], [14, 186, 2, 35, "_getPrototypeOf2"], [14, 202, 2, 35], [14, 203, 2, 35, "default"], [14, 210, 2, 35], [14, 212, 2, 35, "t"], [14, 213, 2, 35], [14, 215, 2, 35, "constructor"], [14, 226, 2, 35], [14, 230, 2, 35, "o"], [14, 231, 2, 35], [14, 232, 2, 35, "apply"], [14, 237, 2, 35], [14, 238, 2, 35, "t"], [14, 239, 2, 35], [14, 241, 2, 35, "e"], [14, 242, 2, 35], [15, 2, 2, 35], [15, 11, 2, 35, "_isNativeReflectConstruct"], [15, 37, 2, 35], [15, 51, 2, 35, "t"], [15, 52, 2, 35], [15, 56, 2, 35, "Boolean"], [15, 63, 2, 35], [15, 64, 2, 35, "prototype"], [15, 73, 2, 35], [15, 74, 2, 35, "valueOf"], [15, 81, 2, 35], [15, 82, 2, 35, "call"], [15, 86, 2, 35], [15, 87, 2, 35, "Reflect"], [15, 94, 2, 35], [15, 95, 2, 35, "construct"], [15, 104, 2, 35], [15, 105, 2, 35, "Boolean"], [15, 112, 2, 35], [15, 145, 2, 35, "t"], [15, 146, 2, 35], [15, 159, 2, 35, "_isNativeReflectConstruct"], [15, 184, 2, 35], [15, 196, 2, 35, "_isNativeReflectConstruct"], [15, 197, 2, 35], [15, 210, 2, 35, "t"], [15, 211, 2, 35], [16, 2, 4, 0], [17, 0, 5, 0], [18, 0, 6, 0], [19, 0, 7, 0], [20, 0, 8, 0], [21, 2, 4, 0], [21, 6, 9, 13, "UnavailabilityError"], [21, 25, 9, 32], [21, 28, 9, 32, "exports"], [21, 35, 9, 32], [21, 36, 9, 32, "UnavailabilityError"], [21, 55, 9, 32], [21, 81, 9, 32, "_CodedError"], [21, 92, 9, 32], [22, 4, 10, 2], [22, 13, 10, 2, "UnavailabilityError"], [22, 33, 10, 14, "moduleName"], [22, 43, 10, 32], [22, 45, 10, 34, "propertyName"], [22, 57, 10, 54], [22, 59, 10, 56], [23, 6, 10, 56], [23, 10, 10, 56, "_classCallCheck2"], [23, 26, 10, 56], [23, 27, 10, 56, "default"], [23, 34, 10, 56], [23, 42, 10, 56, "UnavailabilityError"], [23, 61, 10, 56], [24, 6, 10, 56], [24, 13, 10, 56, "_callSuper"], [24, 23, 10, 56], [24, 30, 10, 56, "UnavailabilityError"], [24, 49, 10, 56], [24, 52, 12, 6], [24, 69, 12, 23], [24, 71, 13, 6], [24, 97, 13, 32, "moduleName"], [24, 107, 13, 42], [24, 111, 13, 46, "propertyName"], [24, 123, 13, 58], [24, 147, 13, 82, "Platform"], [24, 164, 13, 90], [24, 165, 13, 91, "OS"], [24, 167, 13, 93], [24, 235, 13, 161], [25, 4, 15, 2], [26, 4, 15, 3], [26, 8, 15, 3, "_inherits2"], [26, 18, 15, 3], [26, 19, 15, 3, "default"], [26, 26, 15, 3], [26, 28, 15, 3, "UnavailabilityError"], [26, 47, 15, 3], [26, 49, 15, 3, "_CodedError"], [26, 60, 15, 3], [27, 4, 15, 3], [27, 15, 15, 3, "_createClass2"], [27, 28, 15, 3], [27, 29, 15, 3, "default"], [27, 36, 15, 3], [27, 38, 15, 3, "UnavailabilityError"], [27, 57, 15, 3], [28, 2, 15, 3], [28, 4, 9, 41, "CodedError"], [28, 27, 9, 51], [29, 0, 9, 51], [29, 3]], "functionMap": {"names": ["<global>", "UnavailabilityError", "constructor"], "mappings": "AAA;OCQ;ECC;GDK;CDC"}}, "type": "js/module"}]}