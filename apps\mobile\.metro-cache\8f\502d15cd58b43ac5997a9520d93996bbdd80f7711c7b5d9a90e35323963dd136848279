{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 59}}], "key": "pb9N0Xpf+NPEwAXmL7T0XcgBMDo=", "exportNames": ["*"]}}, {"name": "../../../Libraries/ReactNative/UIManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 65}}], "key": "9Xtx2+vZcToG5lUt+TY2kdG6OQU=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 61}}], "key": "kIyIhRgCqWyDelgdE4/FX1x3fx4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../../src/private/specs_DEPRECATED/components/RCTSafeAreaViewNativeComponent", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 23, "column": 7}, "end": {"line": 23, "column": 97}}, {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 96}}], "key": "Wz77eHBLyoaCWM/Vj1/bS6dvOfM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _View = _interopRequireDefault(require(_dependencyMap[1], \"../../../Libraries/Components/View/View\"));\n  var _UIManager = _interopRequireDefault(require(_dependencyMap[2], \"../../../Libraries/ReactNative/UIManager\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[3], \"../../../Libraries/Utilities/Platform\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[4], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var exported = _Platform.default.select({\n    ios: require(_dependencyMap[5], \"../../../src/private/specs_DEPRECATED/components/RCTSafeAreaViewNativeComponent\").default,\n    android: _UIManager.default.hasViewManagerConfig('RCTSafeAreaView') ? require(_dependencyMap[5], \"../../../src/private/specs_DEPRECATED/components/RCTSafeAreaViewNativeComponent\").default : _View.default,\n    default: _View.default\n  });\n  var _default = exports.default = exported;\n});", "lineCount": 18, "map": [[7, 2, 14, 0], [7, 6, 14, 0, "_View"], [7, 11, 14, 0], [7, 14, 14, 0, "_interopRequireDefault"], [7, 36, 14, 0], [7, 37, 14, 0, "require"], [7, 44, 14, 0], [7, 45, 14, 0, "_dependencyMap"], [7, 59, 14, 0], [8, 2, 15, 0], [8, 6, 15, 0, "_UIManager"], [8, 16, 15, 0], [8, 19, 15, 0, "_interopRequireDefault"], [8, 41, 15, 0], [8, 42, 15, 0, "require"], [8, 49, 15, 0], [8, 50, 15, 0, "_dependencyMap"], [8, 64, 15, 0], [9, 2, 16, 0], [9, 6, 16, 0, "_Platform"], [9, 15, 16, 0], [9, 18, 16, 0, "_interopRequireDefault"], [9, 40, 16, 0], [9, 41, 16, 0, "require"], [9, 48, 16, 0], [9, 49, 16, 0, "_dependencyMap"], [9, 63, 16, 0], [10, 2, 17, 0], [10, 6, 17, 0, "React"], [10, 11, 17, 0], [10, 14, 17, 0, "_interopRequireWildcard"], [10, 37, 17, 0], [10, 38, 17, 0, "require"], [10, 45, 17, 0], [10, 46, 17, 0, "_dependencyMap"], [10, 60, 17, 0], [11, 2, 17, 31], [11, 11, 17, 31, "_interopRequireWildcard"], [11, 35, 17, 31, "e"], [11, 36, 17, 31], [11, 38, 17, 31, "t"], [11, 39, 17, 31], [11, 68, 17, 31, "WeakMap"], [11, 75, 17, 31], [11, 81, 17, 31, "r"], [11, 82, 17, 31], [11, 89, 17, 31, "WeakMap"], [11, 96, 17, 31], [11, 100, 17, 31, "n"], [11, 101, 17, 31], [11, 108, 17, 31, "WeakMap"], [11, 115, 17, 31], [11, 127, 17, 31, "_interopRequireWildcard"], [11, 150, 17, 31], [11, 162, 17, 31, "_interopRequireWildcard"], [11, 163, 17, 31, "e"], [11, 164, 17, 31], [11, 166, 17, 31, "t"], [11, 167, 17, 31], [11, 176, 17, 31, "t"], [11, 177, 17, 31], [11, 181, 17, 31, "e"], [11, 182, 17, 31], [11, 186, 17, 31, "e"], [11, 187, 17, 31], [11, 188, 17, 31, "__esModule"], [11, 198, 17, 31], [11, 207, 17, 31, "e"], [11, 208, 17, 31], [11, 214, 17, 31, "o"], [11, 215, 17, 31], [11, 217, 17, 31, "i"], [11, 218, 17, 31], [11, 220, 17, 31, "f"], [11, 221, 17, 31], [11, 226, 17, 31, "__proto__"], [11, 235, 17, 31], [11, 243, 17, 31, "default"], [11, 250, 17, 31], [11, 252, 17, 31, "e"], [11, 253, 17, 31], [11, 270, 17, 31, "e"], [11, 271, 17, 31], [11, 294, 17, 31, "e"], [11, 295, 17, 31], [11, 320, 17, 31, "e"], [11, 321, 17, 31], [11, 330, 17, 31, "f"], [11, 331, 17, 31], [11, 337, 17, 31, "o"], [11, 338, 17, 31], [11, 341, 17, 31, "t"], [11, 342, 17, 31], [11, 345, 17, 31, "n"], [11, 346, 17, 31], [11, 349, 17, 31, "r"], [11, 350, 17, 31], [11, 358, 17, 31, "o"], [11, 359, 17, 31], [11, 360, 17, 31, "has"], [11, 363, 17, 31], [11, 364, 17, 31, "e"], [11, 365, 17, 31], [11, 375, 17, 31, "o"], [11, 376, 17, 31], [11, 377, 17, 31, "get"], [11, 380, 17, 31], [11, 381, 17, 31, "e"], [11, 382, 17, 31], [11, 385, 17, 31, "o"], [11, 386, 17, 31], [11, 387, 17, 31, "set"], [11, 390, 17, 31], [11, 391, 17, 31, "e"], [11, 392, 17, 31], [11, 394, 17, 31, "f"], [11, 395, 17, 31], [11, 409, 17, 31, "_t"], [11, 411, 17, 31], [11, 415, 17, 31, "e"], [11, 416, 17, 31], [11, 432, 17, 31, "_t"], [11, 434, 17, 31], [11, 441, 17, 31, "hasOwnProperty"], [11, 455, 17, 31], [11, 456, 17, 31, "call"], [11, 460, 17, 31], [11, 461, 17, 31, "e"], [11, 462, 17, 31], [11, 464, 17, 31, "_t"], [11, 466, 17, 31], [11, 473, 17, 31, "i"], [11, 474, 17, 31], [11, 478, 17, 31, "o"], [11, 479, 17, 31], [11, 482, 17, 31, "Object"], [11, 488, 17, 31], [11, 489, 17, 31, "defineProperty"], [11, 503, 17, 31], [11, 508, 17, 31, "Object"], [11, 514, 17, 31], [11, 515, 17, 31, "getOwnPropertyDescriptor"], [11, 539, 17, 31], [11, 540, 17, 31, "e"], [11, 541, 17, 31], [11, 543, 17, 31, "_t"], [11, 545, 17, 31], [11, 552, 17, 31, "i"], [11, 553, 17, 31], [11, 554, 17, 31, "get"], [11, 557, 17, 31], [11, 561, 17, 31, "i"], [11, 562, 17, 31], [11, 563, 17, 31, "set"], [11, 566, 17, 31], [11, 570, 17, 31, "o"], [11, 571, 17, 31], [11, 572, 17, 31, "f"], [11, 573, 17, 31], [11, 575, 17, 31, "_t"], [11, 577, 17, 31], [11, 579, 17, 31, "i"], [11, 580, 17, 31], [11, 584, 17, 31, "f"], [11, 585, 17, 31], [11, 586, 17, 31, "_t"], [11, 588, 17, 31], [11, 592, 17, 31, "e"], [11, 593, 17, 31], [11, 594, 17, 31, "_t"], [11, 596, 17, 31], [11, 607, 17, 31, "f"], [11, 608, 17, 31], [11, 613, 17, 31, "e"], [11, 614, 17, 31], [11, 616, 17, 31, "t"], [11, 617, 17, 31], [12, 2, 19, 0], [12, 6, 19, 6, "exported"], [12, 14, 22, 1], [12, 17, 22, 4, "Platform"], [12, 34, 22, 12], [12, 35, 22, 13, "select"], [12, 41, 22, 19], [12, 42, 22, 20], [13, 4, 23, 2, "ios"], [13, 7, 23, 5], [13, 9, 23, 7, "require"], [13, 16, 23, 14], [13, 17, 23, 14, "_dependencyMap"], [13, 31, 23, 14], [13, 117, 23, 96], [13, 118, 23, 97], [13, 119, 24, 5, "default"], [13, 126, 24, 12], [14, 4, 25, 2, "android"], [14, 11, 25, 9], [14, 13, 25, 11, "UIManager"], [14, 31, 25, 20], [14, 32, 25, 21, "hasViewManagerConfig"], [14, 52, 25, 41], [14, 53, 25, 42], [14, 70, 25, 59], [14, 71, 25, 60], [14, 74, 26, 6, "require"], [14, 81, 26, 13], [14, 82, 26, 13, "_dependencyMap"], [14, 96, 26, 13], [14, 182, 26, 95], [14, 183, 26, 96], [14, 184, 27, 9, "default"], [14, 191, 27, 16], [14, 194, 28, 6, "View"], [14, 207, 28, 10], [15, 4, 29, 2, "default"], [15, 11, 29, 9], [15, 13, 29, 11, "View"], [16, 2, 30, 0], [16, 3, 30, 1], [16, 4, 30, 2], [17, 2, 30, 3], [17, 6, 30, 3, "_default"], [17, 14, 30, 3], [17, 17, 30, 3, "exports"], [17, 24, 30, 3], [17, 25, 30, 3, "default"], [17, 32, 30, 3], [17, 35, 32, 15, "exported"], [17, 43, 32, 23], [18, 0, 32, 23], [18, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}