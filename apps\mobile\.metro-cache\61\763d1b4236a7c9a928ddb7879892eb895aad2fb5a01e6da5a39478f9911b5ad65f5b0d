{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "expo-font", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 34, "index": 34}}], "key": "2pRvmGok3jynt0eNgZSF3SdmQzk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 35}, "end": {"line": 2, "column": 26, "index": 61}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 62}, "end": {"line": 3, "column": 37, "index": 99}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/lib/create-icon-set", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 100}, "end": {"line": 4, "column": 83, "index": 183}}], "key": "pFwt5s6dfgmkpQkV9eX+uAP/V1M=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/lib/icon-button", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 184}, "end": {"line": 5, "column": 91, "index": 275}}], "key": "3JA4fUPyiHB5rocnjpvZuSxxBrc=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"DEFAULT_ICON_COLOR\", {\n    enumerable: true,\n    get: function () {\n      return _createIconSet.DEFAULT_ICON_COLOR;\n    }\n  });\n  Object.defineProperty(exports, \"DEFAULT_ICON_SIZE\", {\n    enumerable: true,\n    get: function () {\n      return _createIconSet.DEFAULT_ICON_SIZE;\n    }\n  });\n  exports.default = _default;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var Font = _interopRequireWildcard(require(_dependencyMap[7], \"expo-font\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[8], \"react\"));\n  var _reactNative = require(_dependencyMap[9], \"react-native\");\n  var _createIconSet = _interopRequireWildcard(require(_dependencyMap[10], \"./vendor/react-native-vector-icons/lib/create-icon-set\"));\n  var _iconButton = _interopRequireDefault(require(_dependencyMap[11], \"./vendor/react-native-vector-icons/lib/icon-button\"));\n  var _jsxDevRuntime = require(_dependencyMap[12], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\@expo\\\\vector-icons\\\\build\\\\createIconSet.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _default(glyphMap, fontName, expoAssetId, fontStyle) {\n    var _Icon;\n    var font = {\n      [fontName]: expoAssetId\n    };\n    var RNVIconComponent = (0, _createIconSet.default)(glyphMap, fontName, null, fontStyle);\n    return _Icon = /*#__PURE__*/function (_React$Component) {\n      function Icon() {\n        var _this;\n        (0, _classCallCheck2.default)(this, Icon);\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        _this = _callSuper(this, Icon, [...args]);\n        _this._mounted = false;\n        _this.state = {\n          fontIsLoaded: Font.isLoaded(fontName)\n        };\n        return _this;\n      }\n      (0, _inherits2.default)(Icon, _React$Component);\n      return (0, _createClass2.default)(Icon, [{\n        key: \"componentDidMount\",\n        value: function () {\n          var _componentDidMount = (0, _asyncToGenerator2.default)(function* () {\n            this._mounted = true;\n            if (!this.state.fontIsLoaded) {\n              yield Font.loadAsync(font);\n              /* eslint-disable react/no-did-mount-set-state */\n              this._mounted && this.setState({\n                fontIsLoaded: true\n              });\n            }\n          });\n          function componentDidMount() {\n            return _componentDidMount.apply(this, arguments);\n          }\n          return componentDidMount;\n        }()\n      }, {\n        key: \"componentWillUnmount\",\n        value: function componentWillUnmount() {\n          this._mounted = false;\n        }\n      }, {\n        key: \"setNativeProps\",\n        value: function setNativeProps(props) {\n          if (this._icon) {\n            this._icon.setNativeProps(props);\n          }\n        }\n      }, {\n        key: \"render\",\n        value: function render() {\n          if (__DEV__ && this.props.name && !(this.props.name in glyphMap)) {\n            console.warn(`\"${this.props.name}\" is not a valid icon name for family \"${fontName}\"`);\n          }\n          if (!this.state.fontIsLoaded) {\n            return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 24\n            }, this);\n          }\n          return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(RNVIconComponent, {\n            ref: view => {\n              this._icon = view;\n            },\n            ...this.props\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 21\n          }, this);\n        }\n      }]);\n    }(_react.default.Component), _Icon.defaultProps = RNVIconComponent.defaultProps, _Icon.Button = (0, _iconButton.default)(_Icon), _Icon.glyphMap = glyphMap, _Icon.getRawGlyphMap = () => glyphMap, _Icon.getFontFamily = () => fontName, _Icon.loadFont = () => Font.loadAsync(font), _Icon.font = font, _Icon;\n  }\n});", "lineCount": 113, "map": [[25, 2, 1, 0], [25, 6, 1, 0, "Font"], [25, 10, 1, 0], [25, 13, 1, 0, "_interopRequireWildcard"], [25, 36, 1, 0], [25, 37, 1, 0, "require"], [25, 44, 1, 0], [25, 45, 1, 0, "_dependencyMap"], [25, 59, 1, 0], [26, 2, 2, 0], [26, 6, 2, 0, "_react"], [26, 12, 2, 0], [26, 15, 2, 0, "_interopRequireDefault"], [26, 37, 2, 0], [26, 38, 2, 0, "require"], [26, 45, 2, 0], [26, 46, 2, 0, "_dependencyMap"], [26, 60, 2, 0], [27, 2, 3, 0], [27, 6, 3, 0, "_reactNative"], [27, 18, 3, 0], [27, 21, 3, 0, "require"], [27, 28, 3, 0], [27, 29, 3, 0, "_dependencyMap"], [27, 43, 3, 0], [28, 2, 4, 0], [28, 6, 4, 0, "_createIconSet"], [28, 20, 4, 0], [28, 23, 4, 0, "_interopRequireWildcard"], [28, 46, 4, 0], [28, 47, 4, 0, "require"], [28, 54, 4, 0], [28, 55, 4, 0, "_dependencyMap"], [28, 69, 4, 0], [29, 2, 5, 0], [29, 6, 5, 0, "_iconButton"], [29, 17, 5, 0], [29, 20, 5, 0, "_interopRequireDefault"], [29, 42, 5, 0], [29, 43, 5, 0, "require"], [29, 50, 5, 0], [29, 51, 5, 0, "_dependencyMap"], [29, 65, 5, 0], [30, 2, 5, 91], [30, 6, 5, 91, "_jsxDevRuntime"], [30, 20, 5, 91], [30, 23, 5, 91, "require"], [30, 30, 5, 91], [30, 31, 5, 91, "_dependencyMap"], [30, 45, 5, 91], [31, 2, 5, 91], [31, 6, 5, 91, "_jsxFileName"], [31, 18, 5, 91], [32, 2, 5, 91], [32, 11, 5, 91, "_interopRequireWildcard"], [32, 35, 5, 91, "e"], [32, 36, 5, 91], [32, 38, 5, 91, "t"], [32, 39, 5, 91], [32, 68, 5, 91, "WeakMap"], [32, 75, 5, 91], [32, 81, 5, 91, "r"], [32, 82, 5, 91], [32, 89, 5, 91, "WeakMap"], [32, 96, 5, 91], [32, 100, 5, 91, "n"], [32, 101, 5, 91], [32, 108, 5, 91, "WeakMap"], [32, 115, 5, 91], [32, 127, 5, 91, "_interopRequireWildcard"], [32, 150, 5, 91], [32, 162, 5, 91, "_interopRequireWildcard"], [32, 163, 5, 91, "e"], [32, 164, 5, 91], [32, 166, 5, 91, "t"], [32, 167, 5, 91], [32, 176, 5, 91, "t"], [32, 177, 5, 91], [32, 181, 5, 91, "e"], [32, 182, 5, 91], [32, 186, 5, 91, "e"], [32, 187, 5, 91], [32, 188, 5, 91, "__esModule"], [32, 198, 5, 91], [32, 207, 5, 91, "e"], [32, 208, 5, 91], [32, 214, 5, 91, "o"], [32, 215, 5, 91], [32, 217, 5, 91, "i"], [32, 218, 5, 91], [32, 220, 5, 91, "f"], [32, 221, 5, 91], [32, 226, 5, 91, "__proto__"], [32, 235, 5, 91], [32, 243, 5, 91, "default"], [32, 250, 5, 91], [32, 252, 5, 91, "e"], [32, 253, 5, 91], [32, 270, 5, 91, "e"], [32, 271, 5, 91], [32, 294, 5, 91, "e"], [32, 295, 5, 91], [32, 320, 5, 91, "e"], [32, 321, 5, 91], [32, 330, 5, 91, "f"], [32, 331, 5, 91], [32, 337, 5, 91, "o"], [32, 338, 5, 91], [32, 341, 5, 91, "t"], [32, 342, 5, 91], [32, 345, 5, 91, "n"], [32, 346, 5, 91], [32, 349, 5, 91, "r"], [32, 350, 5, 91], [32, 358, 5, 91, "o"], [32, 359, 5, 91], [32, 360, 5, 91, "has"], [32, 363, 5, 91], [32, 364, 5, 91, "e"], [32, 365, 5, 91], [32, 375, 5, 91, "o"], [32, 376, 5, 91], [32, 377, 5, 91, "get"], [32, 380, 5, 91], [32, 381, 5, 91, "e"], [32, 382, 5, 91], [32, 385, 5, 91, "o"], [32, 386, 5, 91], [32, 387, 5, 91, "set"], [32, 390, 5, 91], [32, 391, 5, 91, "e"], [32, 392, 5, 91], [32, 394, 5, 91, "f"], [32, 395, 5, 91], [32, 409, 5, 91, "_t"], [32, 411, 5, 91], [32, 415, 5, 91, "e"], [32, 416, 5, 91], [32, 432, 5, 91, "_t"], [32, 434, 5, 91], [32, 441, 5, 91, "hasOwnProperty"], [32, 455, 5, 91], [32, 456, 5, 91, "call"], [32, 460, 5, 91], [32, 461, 5, 91, "e"], [32, 462, 5, 91], [32, 464, 5, 91, "_t"], [32, 466, 5, 91], [32, 473, 5, 91, "i"], [32, 474, 5, 91], [32, 478, 5, 91, "o"], [32, 479, 5, 91], [32, 482, 5, 91, "Object"], [32, 488, 5, 91], [32, 489, 5, 91, "defineProperty"], [32, 503, 5, 91], [32, 508, 5, 91, "Object"], [32, 514, 5, 91], [32, 515, 5, 91, "getOwnPropertyDescriptor"], [32, 539, 5, 91], [32, 540, 5, 91, "e"], [32, 541, 5, 91], [32, 543, 5, 91, "_t"], [32, 545, 5, 91], [32, 552, 5, 91, "i"], [32, 553, 5, 91], [32, 554, 5, 91, "get"], [32, 557, 5, 91], [32, 561, 5, 91, "i"], [32, 562, 5, 91], [32, 563, 5, 91, "set"], [32, 566, 5, 91], [32, 570, 5, 91, "o"], [32, 571, 5, 91], [32, 572, 5, 91, "f"], [32, 573, 5, 91], [32, 575, 5, 91, "_t"], [32, 577, 5, 91], [32, 579, 5, 91, "i"], [32, 580, 5, 91], [32, 584, 5, 91, "f"], [32, 585, 5, 91], [32, 586, 5, 91, "_t"], [32, 588, 5, 91], [32, 592, 5, 91, "e"], [32, 593, 5, 91], [32, 594, 5, 91, "_t"], [32, 596, 5, 91], [32, 607, 5, 91, "f"], [32, 608, 5, 91], [32, 613, 5, 91, "e"], [32, 614, 5, 91], [32, 616, 5, 91, "t"], [32, 617, 5, 91], [33, 2, 5, 91], [33, 11, 5, 91, "_callSuper"], [33, 22, 5, 91, "t"], [33, 23, 5, 91], [33, 25, 5, 91, "o"], [33, 26, 5, 91], [33, 28, 5, 91, "e"], [33, 29, 5, 91], [33, 40, 5, 91, "o"], [33, 41, 5, 91], [33, 48, 5, 91, "_getPrototypeOf2"], [33, 64, 5, 91], [33, 65, 5, 91, "default"], [33, 72, 5, 91], [33, 74, 5, 91, "o"], [33, 75, 5, 91], [33, 82, 5, 91, "_possibleConstructorReturn2"], [33, 109, 5, 91], [33, 110, 5, 91, "default"], [33, 117, 5, 91], [33, 119, 5, 91, "t"], [33, 120, 5, 91], [33, 122, 5, 91, "_isNativeReflectConstruct"], [33, 147, 5, 91], [33, 152, 5, 91, "Reflect"], [33, 159, 5, 91], [33, 160, 5, 91, "construct"], [33, 169, 5, 91], [33, 170, 5, 91, "o"], [33, 171, 5, 91], [33, 173, 5, 91, "e"], [33, 174, 5, 91], [33, 186, 5, 91, "_getPrototypeOf2"], [33, 202, 5, 91], [33, 203, 5, 91, "default"], [33, 210, 5, 91], [33, 212, 5, 91, "t"], [33, 213, 5, 91], [33, 215, 5, 91, "constructor"], [33, 226, 5, 91], [33, 230, 5, 91, "o"], [33, 231, 5, 91], [33, 232, 5, 91, "apply"], [33, 237, 5, 91], [33, 238, 5, 91, "t"], [33, 239, 5, 91], [33, 241, 5, 91, "e"], [33, 242, 5, 91], [34, 2, 5, 91], [34, 11, 5, 91, "_isNativeReflectConstruct"], [34, 37, 5, 91], [34, 51, 5, 91, "t"], [34, 52, 5, 91], [34, 56, 5, 91, "Boolean"], [34, 63, 5, 91], [34, 64, 5, 91, "prototype"], [34, 73, 5, 91], [34, 74, 5, 91, "valueOf"], [34, 81, 5, 91], [34, 82, 5, 91, "call"], [34, 86, 5, 91], [34, 87, 5, 91, "Reflect"], [34, 94, 5, 91], [34, 95, 5, 91, "construct"], [34, 104, 5, 91], [34, 105, 5, 91, "Boolean"], [34, 112, 5, 91], [34, 145, 5, 91, "t"], [34, 146, 5, 91], [34, 159, 5, 91, "_isNativeReflectConstruct"], [34, 184, 5, 91], [34, 196, 5, 91, "_isNativeReflectConstruct"], [34, 197, 5, 91], [34, 210, 5, 91, "t"], [34, 211, 5, 91], [35, 2, 7, 15], [35, 11, 7, 15, "_default"], [35, 20, 7, 25, "glyphMap"], [35, 28, 7, 33], [35, 30, 7, 35, "fontName"], [35, 38, 7, 43], [35, 40, 7, 45, "expoAssetId"], [35, 51, 7, 56], [35, 53, 7, 58, "fontStyle"], [35, 62, 7, 67], [35, 64, 7, 69], [36, 4, 7, 69], [36, 8, 7, 69, "_Icon"], [36, 13, 7, 69], [37, 4, 8, 4], [37, 8, 8, 10, "font"], [37, 12, 8, 14], [37, 15, 8, 17], [38, 6, 8, 19], [38, 7, 8, 20, "fontName"], [38, 15, 8, 28], [38, 18, 8, 31, "expoAssetId"], [39, 4, 8, 43], [39, 5, 8, 44], [40, 4, 9, 4], [40, 8, 9, 10, "RNVIconComponent"], [40, 24, 9, 26], [40, 27, 9, 29], [40, 31, 9, 29, "createIconSet"], [40, 53, 9, 42], [40, 55, 9, 43, "glyphMap"], [40, 63, 9, 51], [40, 65, 9, 53, "fontName"], [40, 73, 9, 61], [40, 75, 9, 63], [40, 79, 9, 67], [40, 81, 9, 69, "fontStyle"], [40, 90, 9, 78], [40, 91, 9, 79], [41, 4, 10, 4], [41, 11, 10, 4, "_Icon"], [41, 16, 10, 4], [41, 42, 10, 4, "_React$Component"], [41, 58, 10, 4], [42, 6, 10, 4], [42, 15, 10, 4, "Icon"], [42, 20, 10, 4], [43, 8, 10, 4], [43, 12, 10, 4, "_this"], [43, 17, 10, 4], [44, 8, 10, 4], [44, 12, 10, 4, "_classCallCheck2"], [44, 28, 10, 4], [44, 29, 10, 4, "default"], [44, 36, 10, 4], [44, 44, 10, 4, "Icon"], [44, 48, 10, 4], [45, 8, 10, 4], [45, 17, 10, 4, "_len"], [45, 21, 10, 4], [45, 24, 10, 4, "arguments"], [45, 33, 10, 4], [45, 34, 10, 4, "length"], [45, 40, 10, 4], [45, 42, 10, 4, "args"], [45, 46, 10, 4], [45, 53, 10, 4, "Array"], [45, 58, 10, 4], [45, 59, 10, 4, "_len"], [45, 63, 10, 4], [45, 66, 10, 4, "_key"], [45, 70, 10, 4], [45, 76, 10, 4, "_key"], [45, 80, 10, 4], [45, 83, 10, 4, "_len"], [45, 87, 10, 4], [45, 89, 10, 4, "_key"], [45, 93, 10, 4], [46, 10, 10, 4, "args"], [46, 14, 10, 4], [46, 15, 10, 4, "_key"], [46, 19, 10, 4], [46, 23, 10, 4, "arguments"], [46, 32, 10, 4], [46, 33, 10, 4, "_key"], [46, 37, 10, 4], [47, 8, 10, 4], [48, 8, 10, 4, "_this"], [48, 13, 10, 4], [48, 16, 10, 4, "_callSuper"], [48, 26, 10, 4], [48, 33, 10, 4, "Icon"], [48, 37, 10, 4], [48, 43, 10, 4, "args"], [48, 47, 10, 4], [49, 8, 10, 4, "_this"], [49, 13, 10, 4], [49, 14, 18, 8, "_mounted"], [49, 22, 18, 16], [49, 25, 18, 19], [49, 30, 18, 24], [50, 8, 18, 24, "_this"], [50, 13, 18, 24], [50, 14, 20, 8, "state"], [50, 19, 20, 13], [50, 22, 20, 16], [51, 10, 21, 12, "fontIsLoaded"], [51, 22, 21, 24], [51, 24, 21, 26, "Font"], [51, 28, 21, 30], [51, 29, 21, 31, "isLoaded"], [51, 37, 21, 39], [51, 38, 21, 40, "fontName"], [51, 46, 21, 48], [52, 8, 22, 8], [52, 9, 22, 9], [53, 8, 22, 9], [53, 15, 22, 9, "_this"], [53, 20, 22, 9], [54, 6, 22, 9], [55, 6, 22, 9], [55, 10, 22, 9, "_inherits2"], [55, 20, 22, 9], [55, 21, 22, 9, "default"], [55, 28, 22, 9], [55, 30, 22, 9, "Icon"], [55, 34, 22, 9], [55, 36, 22, 9, "_React$Component"], [55, 52, 22, 9], [56, 6, 22, 9], [56, 17, 22, 9, "_createClass2"], [56, 30, 22, 9], [56, 31, 22, 9, "default"], [56, 38, 22, 9], [56, 40, 22, 9, "Icon"], [56, 44, 22, 9], [57, 8, 22, 9, "key"], [57, 11, 22, 9], [58, 8, 22, 9, "value"], [58, 13, 22, 9], [59, 10, 22, 9], [59, 14, 22, 9, "_componentDidMount"], [59, 32, 22, 9], [59, 39, 22, 9, "_asyncToGenerator2"], [59, 57, 22, 9], [59, 58, 22, 9, "default"], [59, 65, 22, 9], [59, 67, 23, 8], [59, 80, 23, 34], [60, 12, 24, 12], [60, 16, 24, 16], [60, 17, 24, 17, "_mounted"], [60, 25, 24, 25], [60, 28, 24, 28], [60, 32, 24, 32], [61, 12, 25, 12], [61, 16, 25, 16], [61, 17, 25, 17], [61, 21, 25, 21], [61, 22, 25, 22, "state"], [61, 27, 25, 27], [61, 28, 25, 28, "fontIsLoaded"], [61, 40, 25, 40], [61, 42, 25, 42], [62, 14, 26, 16], [62, 20, 26, 22, "Font"], [62, 24, 26, 26], [62, 25, 26, 27, "loadAsync"], [62, 34, 26, 36], [62, 35, 26, 37, "font"], [62, 39, 26, 41], [62, 40, 26, 42], [63, 14, 27, 16], [64, 14, 28, 16], [64, 18, 28, 20], [64, 19, 28, 21, "_mounted"], [64, 27, 28, 29], [64, 31, 28, 33], [64, 35, 28, 37], [64, 36, 28, 38, "setState"], [64, 44, 28, 46], [64, 45, 28, 47], [65, 16, 28, 49, "fontIsLoaded"], [65, 28, 28, 61], [65, 30, 28, 63], [66, 14, 28, 68], [66, 15, 28, 69], [66, 16, 28, 70], [67, 12, 29, 12], [68, 10, 30, 8], [68, 11, 30, 9], [69, 10, 30, 9], [69, 19, 23, 14, "componentDidMount"], [69, 36, 23, 31, "componentDidMount"], [69, 37, 23, 31], [70, 12, 23, 31], [70, 19, 23, 31, "_componentDidMount"], [70, 37, 23, 31], [70, 38, 23, 31, "apply"], [70, 43, 23, 31], [70, 50, 23, 31, "arguments"], [70, 59, 23, 31], [71, 10, 23, 31], [72, 10, 23, 31], [72, 17, 23, 14, "componentDidMount"], [72, 34, 23, 31], [73, 8, 23, 31], [74, 6, 23, 31], [75, 8, 23, 31, "key"], [75, 11, 23, 31], [76, 8, 23, 31, "value"], [76, 13, 23, 31], [76, 15, 31, 8], [76, 24, 31, 8, "componentWillUnmount"], [76, 44, 31, 28, "componentWillUnmount"], [76, 45, 31, 28], [76, 47, 31, 31], [77, 10, 32, 12], [77, 14, 32, 16], [77, 15, 32, 17, "_mounted"], [77, 23, 32, 25], [77, 26, 32, 28], [77, 31, 32, 33], [78, 8, 33, 8], [79, 6, 33, 9], [80, 8, 33, 9, "key"], [80, 11, 33, 9], [81, 8, 33, 9, "value"], [81, 13, 33, 9], [81, 15, 34, 8], [81, 24, 34, 8, "setNativeProps"], [81, 38, 34, 22, "setNativeProps"], [81, 39, 34, 23, "props"], [81, 44, 34, 28], [81, 46, 34, 30], [82, 10, 35, 12], [82, 14, 35, 16], [82, 18, 35, 20], [82, 19, 35, 21, "_icon"], [82, 24, 35, 26], [82, 26, 35, 28], [83, 12, 36, 16], [83, 16, 36, 20], [83, 17, 36, 21, "_icon"], [83, 22, 36, 26], [83, 23, 36, 27, "setNativeProps"], [83, 37, 36, 41], [83, 38, 36, 42, "props"], [83, 43, 36, 47], [83, 44, 36, 48], [84, 10, 37, 12], [85, 8, 38, 8], [86, 6, 38, 9], [87, 8, 38, 9, "key"], [87, 11, 38, 9], [88, 8, 38, 9, "value"], [88, 13, 38, 9], [88, 15, 39, 8], [88, 24, 39, 8, "render"], [88, 30, 39, 14, "render"], [88, 31, 39, 14], [88, 33, 39, 17], [89, 10, 40, 12], [89, 14, 40, 16, "__DEV__"], [89, 21, 40, 23], [89, 25, 40, 27], [89, 29, 40, 31], [89, 30, 40, 32, "props"], [89, 35, 40, 37], [89, 36, 40, 38, "name"], [89, 40, 40, 42], [89, 44, 40, 46], [89, 46, 40, 48], [89, 50, 40, 52], [89, 51, 40, 53, "props"], [89, 56, 40, 58], [89, 57, 40, 59, "name"], [89, 61, 40, 63], [89, 65, 40, 67, "glyphMap"], [89, 73, 40, 75], [89, 74, 40, 76], [89, 76, 40, 78], [90, 12, 41, 16, "console"], [90, 19, 41, 23], [90, 20, 41, 24, "warn"], [90, 24, 41, 28], [90, 25, 41, 29], [90, 29, 41, 33], [90, 33, 41, 37], [90, 34, 41, 38, "props"], [90, 39, 41, 43], [90, 40, 41, 44, "name"], [90, 44, 41, 48], [90, 86, 41, 90, "fontName"], [90, 94, 41, 98], [90, 97, 41, 101], [90, 98, 41, 102], [91, 10, 42, 12], [92, 10, 43, 12], [92, 14, 43, 16], [92, 15, 43, 17], [92, 19, 43, 21], [92, 20, 43, 22, "state"], [92, 25, 43, 27], [92, 26, 43, 28, "fontIsLoaded"], [92, 38, 43, 40], [92, 40, 43, 42], [93, 12, 44, 16], [93, 32, 44, 23], [93, 36, 44, 23, "_jsxDevRuntime"], [93, 50, 44, 23], [93, 51, 44, 23, "jsxDEV"], [93, 57, 44, 23], [93, 59, 44, 24, "_reactNative"], [93, 71, 44, 24], [93, 72, 44, 24, "Text"], [93, 76, 44, 28], [94, 14, 44, 28, "fileName"], [94, 22, 44, 28], [94, 24, 44, 28, "_jsxFileName"], [94, 36, 44, 28], [95, 14, 44, 28, "lineNumber"], [95, 24, 44, 28], [96, 14, 44, 28, "columnNumber"], [96, 26, 44, 28], [97, 12, 44, 28], [97, 19, 44, 30], [97, 20, 44, 31], [98, 10, 45, 12], [99, 10, 46, 12], [99, 30, 46, 20], [99, 34, 46, 20, "_jsxDevRuntime"], [99, 48, 46, 20], [99, 49, 46, 20, "jsxDEV"], [99, 55, 46, 20], [99, 57, 46, 21, "RNVIconComponent"], [99, 73, 46, 37], [100, 12, 46, 38, "ref"], [100, 15, 46, 41], [100, 17, 46, 44, "view"], [100, 21, 46, 48], [100, 25, 46, 53], [101, 14, 47, 20], [101, 18, 47, 24], [101, 19, 47, 25, "_icon"], [101, 24, 47, 30], [101, 27, 47, 33, "view"], [101, 31, 47, 37], [102, 12, 48, 16], [102, 13, 48, 18], [103, 12, 48, 18], [103, 15, 48, 23], [103, 19, 48, 27], [103, 20, 48, 28, "props"], [104, 10, 48, 33], [105, 12, 48, 33, "fileName"], [105, 20, 48, 33], [105, 22, 48, 33, "_jsxFileName"], [105, 34, 48, 33], [106, 12, 48, 33, "lineNumber"], [106, 22, 48, 33], [107, 12, 48, 33, "columnNumber"], [107, 24, 48, 33], [108, 10, 48, 33], [108, 17, 48, 35], [108, 18, 48, 36], [109, 8, 49, 8], [110, 6, 49, 9], [111, 4, 49, 9], [111, 6, 10, 30, "React"], [111, 20, 10, 35], [111, 21, 10, 36, "Component"], [111, 30, 10, 45], [111, 33, 10, 45, "_Icon"], [111, 38, 10, 45], [111, 39, 11, 15, "defaultProps"], [111, 51, 11, 27], [111, 54, 11, 30, "RNVIconComponent"], [111, 70, 11, 46], [111, 71, 11, 47, "defaultProps"], [111, 83, 11, 59], [111, 85, 11, 59, "_Icon"], [111, 90, 11, 59], [111, 91, 12, 15, "<PERSON><PERSON>"], [111, 97, 12, 21], [111, 100, 12, 24], [111, 104, 12, 24, "createIconButtonComponent"], [111, 123, 12, 49], [111, 125, 12, 50, "Icon"], [111, 130, 12, 54], [111, 131, 12, 55], [111, 133, 12, 55, "_Icon"], [111, 138, 12, 55], [111, 139, 13, 15, "glyphMap"], [111, 147, 13, 23], [111, 150, 13, 26, "glyphMap"], [111, 158, 13, 34], [111, 160, 13, 34, "_Icon"], [111, 165, 13, 34], [111, 166, 14, 15, "getRawGlyphMap"], [111, 180, 14, 29], [111, 183, 14, 32], [111, 189, 14, 38, "glyphMap"], [111, 197, 14, 46], [111, 199, 14, 46, "_Icon"], [111, 204, 14, 46], [111, 205, 15, 15, "getFontFamily"], [111, 218, 15, 28], [111, 221, 15, 31], [111, 227, 15, 37, "fontName"], [111, 235, 15, 45], [111, 237, 15, 45, "_Icon"], [111, 242, 15, 45], [111, 243, 16, 15, "loadFont"], [111, 251, 16, 23], [111, 254, 16, 26], [111, 260, 16, 32, "Font"], [111, 264, 16, 36], [111, 265, 16, 37, "loadAsync"], [111, 274, 16, 46], [111, 275, 16, 47, "font"], [111, 279, 16, 51], [111, 280, 16, 52], [111, 282, 16, 52, "_Icon"], [111, 287, 16, 52], [111, 288, 17, 15, "font"], [111, 292, 17, 19], [111, 295, 17, 22, "font"], [111, 299, 17, 26], [111, 301, 17, 26, "_Icon"], [111, 306, 17, 26], [112, 2, 51, 0], [113, 0, 51, 1], [113, 3]], "functionMap": {"names": ["<global>", "default", "Icon", "Icon.getRawGlyphMap", "Icon.getFontFamily", "Icon.loadFont", "Icon#componentDidMount", "Icon#componentWillUnmount", "Icon#setNativeProps", "Icon#render", "RNVIconComponent.props.ref"], "mappings": "AAA;eCM;WCG;gCCI,cD;+BEC,cF;0BGC,0BH;QIO;SJO;QKC;SLE;QMC;SNI;QOC;2CCO;iBDE;SPC;KDC;CDC"}}, "type": "js/module"}]}