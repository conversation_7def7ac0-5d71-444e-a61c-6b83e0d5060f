{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 44, "index": 58}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../animation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 60}, "end": {"line": 4, "column": 47, "index": 107}}], "key": "a6n75g9KQy+KnMEjz15YzADQ7Hw=", "exportNames": ["*"]}}, {"name": "../core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 159}, "end": {"line": 6, "column": 38, "index": 197}}], "key": "OSA8xsmyvVLjxZOJ/QFvle2ua2I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useSharedValue = useSharedValue;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = require(_dependencyMap[2], \"react\");\n  var _animation = require(_dependencyMap[3], \"../animation\");\n  var _core = require(_dependencyMap[4], \"../core\");\n  /**\n   * Lets you define [shared\n   * values](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value)\n   * in your components.\n   *\n   * @param initialValue - The value you want to be initially stored to a `.value`\n   *   property.\n   * @returns A shared value with a single `.value` property initially set to the\n   *   `initialValue` - {@link SharedValue}.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useSharedValue\n   */\n  function useSharedValue(initialValue) {\n    var _useState = (0, _react.useState)(() => (0, _core.makeMutable)(initialValue)),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 1),\n      mutable = _useState2[0];\n    (0, _react.useEffect)(() => {\n      return () => {\n        (0, _animation.cancelAnimation)(mutable);\n      };\n    }, [mutable]);\n    return mutable;\n  }\n});", "lineCount": 35, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "useSharedValue"], [8, 24, 1, 13], [8, 27, 1, 13, "useSharedValue"], [8, 41, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 2, 0], [10, 6, 2, 0, "_react"], [10, 12, 2, 0], [10, 15, 2, 0, "require"], [10, 22, 2, 0], [10, 23, 2, 0, "_dependencyMap"], [10, 37, 2, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_animation"], [11, 16, 4, 0], [11, 19, 4, 0, "require"], [11, 26, 4, 0], [11, 27, 4, 0, "_dependencyMap"], [11, 41, 4, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_core"], [12, 11, 6, 0], [12, 14, 6, 0, "require"], [12, 21, 6, 0], [12, 22, 6, 0, "_dependencyMap"], [12, 36, 6, 0], [13, 2, 8, 0], [14, 0, 9, 0], [15, 0, 10, 0], [16, 0, 11, 0], [17, 0, 12, 0], [18, 0, 13, 0], [19, 0, 14, 0], [20, 0, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 2, 19, 7], [24, 11, 19, 16, "useSharedValue"], [24, 25, 19, 30, "useSharedValue"], [24, 26, 19, 38, "initialValue"], [24, 38, 19, 57], [24, 40, 19, 79], [25, 4, 20, 2], [25, 8, 20, 2, "_useState"], [25, 17, 20, 2], [25, 20, 20, 20], [25, 24, 20, 20, "useState"], [25, 39, 20, 28], [25, 41, 20, 29], [25, 47, 20, 35], [25, 51, 20, 35, "makeMutable"], [25, 68, 20, 46], [25, 70, 20, 47, "initialValue"], [25, 82, 20, 59], [25, 83, 20, 60], [25, 84, 20, 61], [26, 6, 20, 61, "_useState2"], [26, 16, 20, 61], [26, 23, 20, 61, "_slicedToArray2"], [26, 38, 20, 61], [26, 39, 20, 61, "default"], [26, 46, 20, 61], [26, 48, 20, 61, "_useState"], [26, 57, 20, 61], [27, 6, 20, 9, "mutable"], [27, 13, 20, 16], [27, 16, 20, 16, "_useState2"], [27, 26, 20, 16], [28, 4, 21, 2], [28, 8, 21, 2, "useEffect"], [28, 24, 21, 11], [28, 26, 21, 12], [28, 32, 21, 18], [29, 6, 22, 4], [29, 13, 22, 11], [29, 19, 22, 17], [30, 8, 23, 6], [30, 12, 23, 6, "cancelAnimation"], [30, 38, 23, 21], [30, 40, 23, 22, "mutable"], [30, 47, 23, 29], [30, 48, 23, 30], [31, 6, 24, 4], [31, 7, 24, 5], [32, 4, 25, 2], [32, 5, 25, 3], [32, 7, 25, 5], [32, 8, 25, 6, "mutable"], [32, 15, 25, 13], [32, 16, 25, 14], [32, 17, 25, 15], [33, 4, 26, 2], [33, 11, 26, 9, "mutable"], [33, 18, 26, 16], [34, 2, 27, 0], [35, 0, 27, 1], [35, 3]], "functionMap": {"names": ["<global>", "useSharedValue", "useState$argument_0", "useEffect$argument_0", "<anonymous>"], "mappings": "AAA;OCkB;6BCC,+BD;YEC;WCC;KDE;GFC;CDE"}}, "type": "js/module"}]}