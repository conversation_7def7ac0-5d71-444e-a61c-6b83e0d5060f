{"dependencies": [{"name": "./isNativeReflectConstruct.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 31, "index": 31}, "end": {"line": 1, "column": 71, "index": 71}}], "key": "8Qiffu0t6Ez7Q8GAdhvkrDREsUk=", "exportNames": ["*"]}}, {"name": "./setPrototypeOf.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 2, "column": 21, "index": 94}, "end": {"line": 2, "column": 51, "index": 124}}], "key": "SqKbVhpPIT7m6Gx70N8rif/ceME=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var isNativeReflectConstruct = require(_dependencyMap[0], \"./isNativeReflectConstruct.js\");\n  var setPrototypeOf = require(_dependencyMap[1], \"./setPrototypeOf.js\");\n  function _construct(t, e, r) {\n    if (isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);\n    var o = [null];\n    o.push.apply(o, e);\n    var p = new (t.bind.apply(t, o))();\n    return r && setPrototypeOf(p, r.prototype), p;\n  }\n  module.exports = _construct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 12, "map": [[2, 2, 1, 0], [2, 6, 1, 4, "isNativeReflectConstruct"], [2, 30, 1, 28], [2, 33, 1, 31, "require"], [2, 40, 1, 38], [2, 41, 1, 38, "_dependencyMap"], [2, 55, 1, 38], [2, 91, 1, 70], [2, 92, 1, 71], [3, 2, 2, 0], [3, 6, 2, 4, "setPrototypeOf"], [3, 20, 2, 18], [3, 23, 2, 21, "require"], [3, 30, 2, 28], [3, 31, 2, 28, "_dependencyMap"], [3, 45, 2, 28], [3, 71, 2, 50], [3, 72, 2, 51], [4, 2, 3, 0], [4, 11, 3, 9, "_construct"], [4, 21, 3, 19, "_construct"], [4, 22, 3, 20, "t"], [4, 23, 3, 21], [4, 25, 3, 23, "e"], [4, 26, 3, 24], [4, 28, 3, 26, "r"], [4, 29, 3, 27], [4, 31, 3, 29], [5, 4, 4, 2], [5, 8, 4, 6, "isNativeReflectConstruct"], [5, 32, 4, 30], [5, 33, 4, 31], [5, 34, 4, 32], [5, 36, 4, 34], [5, 43, 4, 41, "Reflect"], [5, 50, 4, 48], [5, 51, 4, 49, "construct"], [5, 60, 4, 58], [5, 61, 4, 59, "apply"], [5, 66, 4, 64], [5, 67, 4, 65], [5, 71, 4, 69], [5, 73, 4, 71, "arguments"], [5, 82, 4, 80], [5, 83, 4, 81], [6, 4, 5, 2], [6, 8, 5, 6, "o"], [6, 9, 5, 7], [6, 12, 5, 10], [6, 13, 5, 11], [6, 17, 5, 15], [6, 18, 5, 16], [7, 4, 6, 2, "o"], [7, 5, 6, 3], [7, 6, 6, 4, "push"], [7, 10, 6, 8], [7, 11, 6, 9, "apply"], [7, 16, 6, 14], [7, 17, 6, 15, "o"], [7, 18, 6, 16], [7, 20, 6, 18, "e"], [7, 21, 6, 19], [7, 22, 6, 20], [8, 4, 7, 2], [8, 8, 7, 6, "p"], [8, 9, 7, 7], [8, 12, 7, 10], [8, 17, 7, 15, "t"], [8, 18, 7, 16], [8, 19, 7, 17, "bind"], [8, 23, 7, 21], [8, 24, 7, 22, "apply"], [8, 29, 7, 27], [8, 30, 7, 28, "t"], [8, 31, 7, 29], [8, 33, 7, 31, "o"], [8, 34, 7, 32], [8, 35, 7, 33], [8, 37, 7, 35], [8, 38, 7, 36], [9, 4, 8, 2], [9, 11, 8, 9, "r"], [9, 12, 8, 10], [9, 16, 8, 14, "setPrototypeOf"], [9, 30, 8, 28], [9, 31, 8, 29, "p"], [9, 32, 8, 30], [9, 34, 8, 32, "r"], [9, 35, 8, 33], [9, 36, 8, 34, "prototype"], [9, 45, 8, 43], [9, 46, 8, 44], [9, 48, 8, 46, "p"], [9, 49, 8, 47], [10, 2, 9, 0], [11, 2, 10, 0, "module"], [11, 8, 10, 6], [11, 9, 10, 7, "exports"], [11, 16, 10, 14], [11, 19, 10, 17, "_construct"], [11, 29, 10, 27], [11, 31, 10, 29, "module"], [11, 37, 10, 35], [11, 38, 10, 36, "exports"], [11, 45, 10, 43], [11, 46, 10, 44, "__esModule"], [11, 56, 10, 54], [11, 59, 10, 57], [11, 63, 10, 61], [11, 65, 10, 63, "module"], [11, 71, 10, 69], [11, 72, 10, 70, "exports"], [11, 79, 10, 77], [11, 80, 10, 78], [11, 89, 10, 87], [11, 90, 10, 88], [11, 93, 10, 91, "module"], [11, 99, 10, 97], [11, 100, 10, 98, "exports"], [11, 107, 10, 105], [12, 0, 10, 106], [12, 3]], "functionMap": {"names": ["<global>", "_construct"], "mappings": "AAA;ACE;CDM"}}, "type": "js/module"}]}