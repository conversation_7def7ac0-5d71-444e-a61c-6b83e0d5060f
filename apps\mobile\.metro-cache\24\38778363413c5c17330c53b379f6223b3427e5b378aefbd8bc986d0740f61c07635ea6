{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 47, "index": 47}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "./ExpoFontLoader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 48}, "end": {"line": 2, "column": 46, "index": 94}}], "key": "7dk3JQGwGYesJt8OOG3pkBz+dtE=", "exportNames": ["*"]}}, {"name": "./FontLoader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 95}, "end": {"line": 3, "column": 70, "index": 165}}], "key": "ubgLNxOkixzH8pVapAwap9wQ8XU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getServerResources = getServerResources;\n  exports.registerStaticFont = registerStaticFont;\n  exports.resetServerContext = resetServerContext;\n  var _expoModulesCore = require(_dependencyMap[1], \"expo-modules-core\");\n  var _ExpoFontLoader = _interopRequireDefault(require(_dependencyMap[2], \"./ExpoFontLoader\"));\n  var _FontLoader = require(_dependencyMap[3], \"./FontLoader\");\n  /**\n   * @returns the server resources that should be statically extracted.\n   * @private\n   */\n  function getServerResources() {\n    return _ExpoFontLoader.default.getServerResources();\n  }\n  /**\n   * @returns clear the server resources from the global scope.\n   * @private\n   */\n  function resetServerContext() {\n    return _ExpoFontLoader.default.resetServerContext();\n  }\n  function registerStaticFont(fontFamily, source) {\n    // MUST BE A SYNC FUNCTION!\n    if (!source) {\n      throw new _expoModulesCore.CodedError(`ERR_FONT_SOURCE`, `Cannot load null or undefined font source: { \"${fontFamily}\": ${source} }. Expected asset of type \\`FontSource\\` for fontFamily of name: \"${fontFamily}\"`);\n    }\n    var asset = (0, _FontLoader.getAssetForSource)(source);\n    (0, _FontLoader.loadSingleFontAsync)(fontFamily, asset);\n  }\n});", "lineCount": 34, "map": [[9, 2, 1, 0], [9, 6, 1, 0, "_expoModulesCore"], [9, 22, 1, 0], [9, 25, 1, 0, "require"], [9, 32, 1, 0], [9, 33, 1, 0, "_dependencyMap"], [9, 47, 1, 0], [10, 2, 2, 0], [10, 6, 2, 0, "_ExpoFontLoader"], [10, 21, 2, 0], [10, 24, 2, 0, "_interopRequireDefault"], [10, 46, 2, 0], [10, 47, 2, 0, "require"], [10, 54, 2, 0], [10, 55, 2, 0, "_dependencyMap"], [10, 69, 2, 0], [11, 2, 3, 0], [11, 6, 3, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 17, 3, 0], [11, 20, 3, 0, "require"], [11, 27, 3, 0], [11, 28, 3, 0, "_dependencyMap"], [11, 42, 3, 0], [12, 2, 4, 0], [13, 0, 5, 0], [14, 0, 6, 0], [15, 0, 7, 0], [16, 2, 8, 7], [16, 11, 8, 16, "getServerResources"], [16, 29, 8, 34, "getServerResources"], [16, 30, 8, 34], [16, 32, 8, 37], [17, 4, 9, 4], [17, 11, 9, 11, "ExpoFontLoader"], [17, 34, 9, 25], [17, 35, 9, 26, "getServerResources"], [17, 53, 9, 44], [17, 54, 9, 45], [17, 55, 9, 46], [18, 2, 10, 0], [19, 2, 11, 0], [20, 0, 12, 0], [21, 0, 13, 0], [22, 0, 14, 0], [23, 2, 15, 7], [23, 11, 15, 16, "resetServerContext"], [23, 29, 15, 34, "resetServerContext"], [23, 30, 15, 34], [23, 32, 15, 37], [24, 4, 16, 4], [24, 11, 16, 11, "ExpoFontLoader"], [24, 34, 16, 25], [24, 35, 16, 26, "resetServerContext"], [24, 53, 16, 44], [24, 54, 16, 45], [24, 55, 16, 46], [25, 2, 17, 0], [26, 2, 18, 7], [26, 11, 18, 16, "registerStaticFont"], [26, 29, 18, 34, "registerStaticFont"], [26, 30, 18, 35, "fontFamily"], [26, 40, 18, 45], [26, 42, 18, 47, "source"], [26, 48, 18, 53], [26, 50, 18, 55], [27, 4, 19, 4], [28, 4, 20, 4], [28, 8, 20, 8], [28, 9, 20, 9, "source"], [28, 15, 20, 15], [28, 17, 20, 17], [29, 6, 21, 8], [29, 12, 21, 14], [29, 16, 21, 18, "CodedError"], [29, 43, 21, 28], [29, 44, 21, 29], [29, 61, 21, 46], [29, 63, 21, 48], [29, 112, 21, 97, "fontFamily"], [29, 122, 21, 107], [29, 128, 21, 113, "source"], [29, 134, 21, 119], [29, 204, 21, 189, "fontFamily"], [29, 214, 21, 199], [29, 217, 21, 202], [29, 218, 21, 203], [30, 4, 22, 4], [31, 4, 23, 4], [31, 8, 23, 10, "asset"], [31, 13, 23, 15], [31, 16, 23, 18], [31, 20, 23, 18, "getAssetForSource"], [31, 49, 23, 35], [31, 51, 23, 36, "source"], [31, 57, 23, 42], [31, 58, 23, 43], [32, 4, 24, 4], [32, 8, 24, 4, "loadSingleFontAsync"], [32, 39, 24, 23], [32, 41, 24, 24, "fontFamily"], [32, 51, 24, 34], [32, 53, 24, 36, "asset"], [32, 58, 24, 41], [32, 59, 24, 42], [33, 2, 25, 0], [34, 0, 25, 1], [34, 3]], "functionMap": {"names": ["<global>", "getServerResources", "resetServerContext", "registerStaticFont"], "mappings": "AAA;OCO;CDE;OEK;CFE;OGC;CHO"}}, "type": "js/module"}]}