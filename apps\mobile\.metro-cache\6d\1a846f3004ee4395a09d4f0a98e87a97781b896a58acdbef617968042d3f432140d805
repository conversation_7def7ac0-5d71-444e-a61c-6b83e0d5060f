{"dependencies": [{"name": "./getPrototypeOf.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 21, "index": 21}, "end": {"line": 1, "column": 51, "index": 51}}], "key": "vHkEouWADzjCoPQf+U9aET1cyIA=", "exportNames": ["*"]}}, {"name": "./setPrototypeOf.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 2, "column": 21, "index": 74}, "end": {"line": 2, "column": 51, "index": 104}}], "key": "SqKbVhpPIT7m6Gx70N8rif/ceME=", "exportNames": ["*"]}}, {"name": "./isNativeFunction.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 3, "column": 23, "index": 129}, "end": {"line": 3, "column": 55, "index": 161}}], "key": "QzJ9MOSLyzLhdJDYAjcCQg4G9Vk=", "exportNames": ["*"]}}, {"name": "./construct.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 16, "index": 179}, "end": {"line": 4, "column": 41, "index": 204}}], "key": "m/W1oxlV7/TznTGdXb/aBv9sLX0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var getPrototypeOf = require(_dependencyMap[0], \"./getPrototypeOf.js\");\n  var setPrototypeOf = require(_dependencyMap[1], \"./setPrototypeOf.js\");\n  var isNativeFunction = require(_dependencyMap[2], \"./isNativeFunction.js\");\n  var construct = require(_dependencyMap[3], \"./construct.js\");\n  function _wrapNativeSuper(t) {\n    var r = \"function\" == typeof Map ? new Map() : void 0;\n    return module.exports = _wrapNativeSuper = function _wrapNativeSuper(t) {\n      if (null === t || !isNativeFunction(t)) return t;\n      if (\"function\" != typeof t) throw new TypeError(\"Super expression must either be null or a function\");\n      if (void 0 !== r) {\n        if (r.has(t)) return r.get(t);\n        r.set(t, Wrapper);\n      }\n      function Wrapper() {\n        return construct(t, arguments, getPrototypeOf(this).constructor);\n      }\n      return Wrapper.prototype = Object.create(t.prototype, {\n        constructor: {\n          value: Wrapper,\n          enumerable: !1,\n          writable: !0,\n          configurable: !0\n        }\n      }), setPrototypeOf(Wrapper, t);\n    }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _wrapNativeSuper(t);\n  }\n  module.exports = _wrapNativeSuper, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 29, "map": [[2, 2, 1, 0], [2, 6, 1, 4, "getPrototypeOf"], [2, 20, 1, 18], [2, 23, 1, 21, "require"], [2, 30, 1, 28], [2, 31, 1, 28, "_dependencyMap"], [2, 45, 1, 28], [2, 71, 1, 50], [2, 72, 1, 51], [3, 2, 2, 0], [3, 6, 2, 4, "setPrototypeOf"], [3, 20, 2, 18], [3, 23, 2, 21, "require"], [3, 30, 2, 28], [3, 31, 2, 28, "_dependencyMap"], [3, 45, 2, 28], [3, 71, 2, 50], [3, 72, 2, 51], [4, 2, 3, 0], [4, 6, 3, 4, "isNativeFunction"], [4, 22, 3, 20], [4, 25, 3, 23, "require"], [4, 32, 3, 30], [4, 33, 3, 30, "_dependencyMap"], [4, 47, 3, 30], [4, 75, 3, 54], [4, 76, 3, 55], [5, 2, 4, 0], [5, 6, 4, 4, "construct"], [5, 15, 4, 13], [5, 18, 4, 16, "require"], [5, 25, 4, 23], [5, 26, 4, 23, "_dependencyMap"], [5, 40, 4, 23], [5, 61, 4, 40], [5, 62, 4, 41], [6, 2, 5, 0], [6, 11, 5, 9, "_wrapNativeSuper"], [6, 27, 5, 25, "_wrapNativeSuper"], [6, 28, 5, 26, "t"], [6, 29, 5, 27], [6, 31, 5, 29], [7, 4, 6, 2], [7, 8, 6, 6, "r"], [7, 9, 6, 7], [7, 12, 6, 10], [7, 22, 6, 20], [7, 26, 6, 24], [7, 33, 6, 31, "Map"], [7, 36, 6, 34], [7, 39, 6, 37], [7, 43, 6, 41, "Map"], [7, 46, 6, 44], [7, 47, 6, 45], [7, 48, 6, 46], [7, 51, 6, 49], [7, 56, 6, 54], [7, 57, 6, 55], [8, 4, 7, 2], [8, 11, 7, 9, "module"], [8, 17, 7, 15], [8, 18, 7, 16, "exports"], [8, 25, 7, 23], [8, 28, 7, 26, "_wrapNativeSuper"], [8, 44, 7, 42], [8, 47, 7, 45], [8, 56, 7, 54, "_wrapNativeSuper"], [8, 72, 7, 70, "_wrapNativeSuper"], [8, 73, 7, 71, "t"], [8, 74, 7, 72], [8, 76, 7, 74], [9, 6, 8, 4], [9, 10, 8, 8], [9, 14, 8, 12], [9, 19, 8, 17, "t"], [9, 20, 8, 18], [9, 24, 8, 22], [9, 25, 8, 23, "isNativeFunction"], [9, 41, 8, 39], [9, 42, 8, 40, "t"], [9, 43, 8, 41], [9, 44, 8, 42], [9, 46, 8, 44], [9, 53, 8, 51, "t"], [9, 54, 8, 52], [10, 6, 9, 4], [10, 10, 9, 8], [10, 20, 9, 18], [10, 24, 9, 22], [10, 31, 9, 29, "t"], [10, 32, 9, 30], [10, 34, 9, 32], [10, 40, 9, 38], [10, 44, 9, 42, "TypeError"], [10, 53, 9, 51], [10, 54, 9, 52], [10, 106, 9, 104], [10, 107, 9, 105], [11, 6, 10, 4], [11, 10, 10, 8], [11, 15, 10, 13], [11, 16, 10, 14], [11, 21, 10, 19, "r"], [11, 22, 10, 20], [11, 24, 10, 22], [12, 8, 11, 6], [12, 12, 11, 10, "r"], [12, 13, 11, 11], [12, 14, 11, 12, "has"], [12, 17, 11, 15], [12, 18, 11, 16, "t"], [12, 19, 11, 17], [12, 20, 11, 18], [12, 22, 11, 20], [12, 29, 11, 27, "r"], [12, 30, 11, 28], [12, 31, 11, 29, "get"], [12, 34, 11, 32], [12, 35, 11, 33, "t"], [12, 36, 11, 34], [12, 37, 11, 35], [13, 8, 12, 6, "r"], [13, 9, 12, 7], [13, 10, 12, 8, "set"], [13, 13, 12, 11], [13, 14, 12, 12, "t"], [13, 15, 12, 13], [13, 17, 12, 15, "Wrapper"], [13, 24, 12, 22], [13, 25, 12, 23], [14, 6, 13, 4], [15, 6, 14, 4], [15, 15, 14, 13, "Wrapper"], [15, 22, 14, 20, "Wrapper"], [15, 23, 14, 20], [15, 25, 14, 23], [16, 8, 15, 6], [16, 15, 15, 13, "construct"], [16, 24, 15, 22], [16, 25, 15, 23, "t"], [16, 26, 15, 24], [16, 28, 15, 26, "arguments"], [16, 37, 15, 35], [16, 39, 15, 37, "getPrototypeOf"], [16, 53, 15, 51], [16, 54, 15, 52], [16, 58, 15, 56], [16, 59, 15, 57], [16, 60, 15, 58, "constructor"], [16, 71, 15, 69], [16, 72, 15, 70], [17, 6, 16, 4], [18, 6, 17, 4], [18, 13, 17, 11, "Wrapper"], [18, 20, 17, 18], [18, 21, 17, 19, "prototype"], [18, 30, 17, 28], [18, 33, 17, 31, "Object"], [18, 39, 17, 37], [18, 40, 17, 38, "create"], [18, 46, 17, 44], [18, 47, 17, 45, "t"], [18, 48, 17, 46], [18, 49, 17, 47, "prototype"], [18, 58, 17, 56], [18, 60, 17, 58], [19, 8, 18, 6, "constructor"], [19, 19, 18, 17], [19, 21, 18, 19], [20, 10, 19, 8, "value"], [20, 15, 19, 13], [20, 17, 19, 15, "Wrapper"], [20, 24, 19, 22], [21, 10, 20, 8, "enumerable"], [21, 20, 20, 18], [21, 22, 20, 20], [21, 23, 20, 21], [21, 24, 20, 22], [22, 10, 21, 8, "writable"], [22, 18, 21, 16], [22, 20, 21, 18], [22, 21, 21, 19], [22, 22, 21, 20], [23, 10, 22, 8, "configurable"], [23, 22, 22, 20], [23, 24, 22, 22], [23, 25, 22, 23], [24, 8, 23, 6], [25, 6, 24, 4], [25, 7, 24, 5], [25, 8, 24, 6], [25, 10, 24, 8, "setPrototypeOf"], [25, 24, 24, 22], [25, 25, 24, 23, "Wrapper"], [25, 32, 24, 30], [25, 34, 24, 32, "t"], [25, 35, 24, 33], [25, 36, 24, 34], [26, 4, 25, 2], [26, 5, 25, 3], [26, 7, 25, 5, "module"], [26, 13, 25, 11], [26, 14, 25, 12, "exports"], [26, 21, 25, 19], [26, 22, 25, 20, "__esModule"], [26, 32, 25, 30], [26, 35, 25, 33], [26, 39, 25, 37], [26, 41, 25, 39, "module"], [26, 47, 25, 45], [26, 48, 25, 46, "exports"], [26, 55, 25, 53], [26, 56, 25, 54], [26, 65, 25, 63], [26, 66, 25, 64], [26, 69, 25, 67, "module"], [26, 75, 25, 73], [26, 76, 25, 74, "exports"], [26, 83, 25, 81], [26, 85, 25, 83, "_wrapNativeSuper"], [26, 101, 25, 99], [26, 102, 25, 100, "t"], [26, 103, 25, 101], [26, 104, 25, 102], [27, 2, 26, 0], [28, 2, 27, 0, "module"], [28, 8, 27, 6], [28, 9, 27, 7, "exports"], [28, 16, 27, 14], [28, 19, 27, 17, "_wrapNativeSuper"], [28, 35, 27, 33], [28, 37, 27, 35, "module"], [28, 43, 27, 41], [28, 44, 27, 42, "exports"], [28, 51, 27, 49], [28, 52, 27, 50, "__esModule"], [28, 62, 27, 60], [28, 65, 27, 63], [28, 69, 27, 67], [28, 71, 27, 69, "module"], [28, 77, 27, 75], [28, 78, 27, 76, "exports"], [28, 85, 27, 83], [28, 86, 27, 84], [28, 95, 27, 93], [28, 96, 27, 94], [28, 99, 27, 97, "module"], [28, 105, 27, 103], [28, 106, 27, 104, "exports"], [28, 113, 27, 111], [29, 0, 27, 112], [29, 3]], "functionMap": {"names": ["<global>", "_wrapNativeSuper", "Wrapper"], "mappings": "AAA;ACI;ICS;KDE;CDU"}}, "type": "js/module"}]}