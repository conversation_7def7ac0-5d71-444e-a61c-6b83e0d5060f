{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/createClass\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var WebSocketEvent = /*#__PURE__*/(0, _createClass2.default)(function WebSocketEvent(type, eventInitDict) {\n    (0, _classCallCheck2.default)(this, WebSocketEvent);\n    this.type = type.toString();\n    Object.assign(this, eventInitDict);\n  });\n  var _default = exports.default = WebSocketEvent;\n});", "lineCount": 17, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_createClass2"], [9, 19, 11, 13], [9, 22, 11, 13, "_interopRequireDefault"], [9, 44, 11, 13], [9, 45, 11, 13, "require"], [9, 52, 11, 13], [9, 53, 11, 13, "_dependencyMap"], [9, 67, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_classCallCheck2"], [10, 22, 11, 13], [10, 25, 11, 13, "_interopRequireDefault"], [10, 47, 11, 13], [10, 48, 11, 13, "require"], [10, 55, 11, 13], [10, 56, 11, 13, "_dependencyMap"], [10, 70, 11, 13], [11, 2, 11, 13], [11, 6, 21, 6, "WebSocketEvent"], [11, 20, 21, 20], [11, 40, 21, 20, "_createClass2"], [11, 53, 21, 20], [11, 54, 21, 20, "default"], [11, 61, 21, 20], [11, 63, 24, 2], [11, 72, 24, 2, "WebSocketEvent"], [11, 87, 24, 14, "type"], [11, 91, 24, 26], [11, 93, 24, 28, "eventInitDict"], [11, 106, 24, 49], [11, 108, 24, 51], [12, 4, 24, 51], [12, 8, 24, 51, "_classCallCheck2"], [12, 24, 24, 51], [12, 25, 24, 51, "default"], [12, 32, 24, 51], [12, 40, 24, 51, "WebSocketEvent"], [12, 54, 24, 51], [13, 4, 25, 4], [13, 8, 25, 8], [13, 9, 25, 9, "type"], [13, 13, 25, 13], [13, 16, 25, 16, "type"], [13, 20, 25, 20], [13, 21, 25, 21, "toString"], [13, 29, 25, 29], [13, 30, 25, 30], [13, 31, 25, 31], [14, 4, 26, 4, "Object"], [14, 10, 26, 10], [14, 11, 26, 11, "assign"], [14, 17, 26, 17], [14, 18, 26, 18], [14, 22, 26, 22], [14, 24, 26, 24, "eventInitDict"], [14, 37, 26, 37], [14, 38, 26, 38], [15, 2, 27, 2], [15, 3, 27, 3], [16, 2, 27, 3], [16, 6, 27, 3, "_default"], [16, 14, 27, 3], [16, 17, 27, 3, "exports"], [16, 24, 27, 3], [16, 25, 27, 3, "default"], [16, 32, 27, 3], [16, 35, 30, 15, "WebSocketEvent"], [16, 49, 30, 29], [17, 0, 30, 29], [17, 3]], "functionMap": {"names": ["<global>", "WebSocketEvent", "constructor"], "mappings": "AAA;ACoB;ECG;GDG;CDC"}}, "type": "js/module"}]}