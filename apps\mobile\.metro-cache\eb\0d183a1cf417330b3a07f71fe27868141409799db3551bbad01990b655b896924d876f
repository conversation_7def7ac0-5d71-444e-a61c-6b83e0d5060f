{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Linking/Linking", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 44}}], "key": "CVywCGEPbVKsdxemwurB4obG/mI=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _Linking = _interopRequireDefault(require(_dependencyMap[1], \"../../Linking/Linking\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[2], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"../../Text/Text\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[4], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[5], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxMessage.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function getLinkRanges(string) {\n    var regex = /https?:\\/\\/[^\\s$.?#].[^\\s]*/gi;\n    var matches = [];\n    var regexResult;\n    while ((regexResult = regex.exec(string)) !== null) {\n      if (regexResult != null) {\n        matches.push({\n          lowerBound: regexResult.index,\n          upperBound: regex.lastIndex\n        });\n      }\n    }\n    return matches;\n  }\n  function TappableLinks(props) {\n    var _this = this;\n    var matches = getLinkRanges(props.content);\n    if (matches.length === 0) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n        style: props.style,\n        children: props.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 12\n      }, this);\n    }\n    var fragments = [];\n    var indexCounter = 0;\n    var startIndex = 0;\n    var _loop = function () {\n      if (startIndex < linkRange.lowerBound) {\n        var _text = props.content.substring(startIndex, linkRange.lowerBound);\n        fragments.push(/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          children: _text\n        }, ++indexCounter, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 22\n        }, _this));\n      }\n      var link = props.content.substring(linkRange.lowerBound, linkRange.upperBound);\n      fragments.push(/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n        onPress: () => {\n          _Linking.default.openURL(link);\n        },\n        style: styles.linkText,\n        children: link\n      }, ++indexCounter, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 7\n      }, _this));\n      startIndex = linkRange.upperBound;\n    };\n    for (var linkRange of matches) {\n      _loop();\n    }\n    if (startIndex < props.content.length) {\n      var text = props.content.substring(startIndex);\n      fragments.push(/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n        style: props.style,\n        children: text\n      }, ++indexCounter, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 7\n      }, this));\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n      style: props.style,\n      children: fragments\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 10\n    }, this);\n  }\n  var cleanContent = content => content.replace(/^(TransformError |Warning: (Warning: )?|Error: )/g, '');\n  function LogBoxMessage(props) {\n    var _props$message = props.message,\n      content = _props$message.content,\n      substitutions = _props$message.substitutions;\n    if (props.plaintext === true) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n        children: cleanContent(content)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 12\n      }, this);\n    }\n    var maxLength = props.maxLength != null ? props.maxLength : Infinity;\n    var substitutionStyle = props.style;\n    var elements = [];\n    var length = 0;\n    var createUnderLength = (key, message, style) => {\n      var cleanMessage = cleanContent(message);\n      if (props.maxLength != null) {\n        cleanMessage = cleanMessage.slice(0, props.maxLength - length);\n      }\n      if (length < maxLength) {\n        elements.push(/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(TappableLinks, {\n          content: cleanMessage,\n          style: style\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 9\n        }, this));\n      }\n      length += cleanMessage.length;\n    };\n    var lastOffset = substitutions.reduce((prevOffset, substitution, index) => {\n      var key = String(index);\n      if (substitution.offset > prevOffset) {\n        var prevPart = content.slice(prevOffset, substitution.offset);\n        createUnderLength(key, prevPart);\n      }\n      var substitutionPart = content.slice(substitution.offset, substitution.offset + substitution.length);\n      createUnderLength(key + '.5', substitutionPart, substitutionStyle);\n      return substitution.offset + substitution.length;\n    }, 0);\n    if (lastOffset < content.length) {\n      var lastPart = content.slice(lastOffset);\n      createUnderLength('-1', lastPart);\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: elements\n    }, void 0, false);\n  }\n  var styles = _StyleSheet.default.create({\n    linkText: {\n      textDecorationLine: 'underline'\n    }\n  });\n  var _default = exports.default = LogBoxMessage;\n});", "lineCount": 151, "map": [[7, 2, 14, 0], [7, 6, 14, 0, "_Linking"], [7, 14, 14, 0], [7, 17, 14, 0, "_interopRequireDefault"], [7, 39, 14, 0], [7, 40, 14, 0, "require"], [7, 47, 14, 0], [7, 48, 14, 0, "_dependencyMap"], [7, 62, 14, 0], [8, 2, 15, 0], [8, 6, 15, 0, "_StyleSheet"], [8, 17, 15, 0], [8, 20, 15, 0, "_interopRequireDefault"], [8, 42, 15, 0], [8, 43, 15, 0, "require"], [8, 50, 15, 0], [8, 51, 15, 0, "_dependencyMap"], [8, 65, 15, 0], [9, 2, 16, 0], [9, 6, 16, 0, "_Text"], [9, 11, 16, 0], [9, 14, 16, 0, "_interopRequireDefault"], [9, 36, 16, 0], [9, 37, 16, 0, "require"], [9, 44, 16, 0], [9, 45, 16, 0, "_dependencyMap"], [9, 59, 16, 0], [10, 2, 17, 0], [10, 6, 17, 0, "React"], [10, 11, 17, 0], [10, 14, 17, 0, "_interopRequireWildcard"], [10, 37, 17, 0], [10, 38, 17, 0, "require"], [10, 45, 17, 0], [10, 46, 17, 0, "_dependencyMap"], [10, 60, 17, 0], [11, 2, 17, 31], [11, 6, 17, 31, "_jsxDevRuntime"], [11, 20, 17, 31], [11, 23, 17, 31, "require"], [11, 30, 17, 31], [11, 31, 17, 31, "_dependencyMap"], [11, 45, 17, 31], [12, 2, 17, 31], [12, 6, 17, 31, "_jsxFileName"], [12, 18, 17, 31], [13, 2, 17, 31], [13, 11, 17, 31, "_interopRequireWildcard"], [13, 35, 17, 31, "e"], [13, 36, 17, 31], [13, 38, 17, 31, "t"], [13, 39, 17, 31], [13, 68, 17, 31, "WeakMap"], [13, 75, 17, 31], [13, 81, 17, 31, "r"], [13, 82, 17, 31], [13, 89, 17, 31, "WeakMap"], [13, 96, 17, 31], [13, 100, 17, 31, "n"], [13, 101, 17, 31], [13, 108, 17, 31, "WeakMap"], [13, 115, 17, 31], [13, 127, 17, 31, "_interopRequireWildcard"], [13, 150, 17, 31], [13, 162, 17, 31, "_interopRequireWildcard"], [13, 163, 17, 31, "e"], [13, 164, 17, 31], [13, 166, 17, 31, "t"], [13, 167, 17, 31], [13, 176, 17, 31, "t"], [13, 177, 17, 31], [13, 181, 17, 31, "e"], [13, 182, 17, 31], [13, 186, 17, 31, "e"], [13, 187, 17, 31], [13, 188, 17, 31, "__esModule"], [13, 198, 17, 31], [13, 207, 17, 31, "e"], [13, 208, 17, 31], [13, 214, 17, 31, "o"], [13, 215, 17, 31], [13, 217, 17, 31, "i"], [13, 218, 17, 31], [13, 220, 17, 31, "f"], [13, 221, 17, 31], [13, 226, 17, 31, "__proto__"], [13, 235, 17, 31], [13, 243, 17, 31, "default"], [13, 250, 17, 31], [13, 252, 17, 31, "e"], [13, 253, 17, 31], [13, 270, 17, 31, "e"], [13, 271, 17, 31], [13, 294, 17, 31, "e"], [13, 295, 17, 31], [13, 320, 17, 31, "e"], [13, 321, 17, 31], [13, 330, 17, 31, "f"], [13, 331, 17, 31], [13, 337, 17, 31, "o"], [13, 338, 17, 31], [13, 341, 17, 31, "t"], [13, 342, 17, 31], [13, 345, 17, 31, "n"], [13, 346, 17, 31], [13, 349, 17, 31, "r"], [13, 350, 17, 31], [13, 358, 17, 31, "o"], [13, 359, 17, 31], [13, 360, 17, 31, "has"], [13, 363, 17, 31], [13, 364, 17, 31, "e"], [13, 365, 17, 31], [13, 375, 17, 31, "o"], [13, 376, 17, 31], [13, 377, 17, 31, "get"], [13, 380, 17, 31], [13, 381, 17, 31, "e"], [13, 382, 17, 31], [13, 385, 17, 31, "o"], [13, 386, 17, 31], [13, 387, 17, 31, "set"], [13, 390, 17, 31], [13, 391, 17, 31, "e"], [13, 392, 17, 31], [13, 394, 17, 31, "f"], [13, 395, 17, 31], [13, 409, 17, 31, "_t"], [13, 411, 17, 31], [13, 415, 17, 31, "e"], [13, 416, 17, 31], [13, 432, 17, 31, "_t"], [13, 434, 17, 31], [13, 441, 17, 31, "hasOwnProperty"], [13, 455, 17, 31], [13, 456, 17, 31, "call"], [13, 460, 17, 31], [13, 461, 17, 31, "e"], [13, 462, 17, 31], [13, 464, 17, 31, "_t"], [13, 466, 17, 31], [13, 473, 17, 31, "i"], [13, 474, 17, 31], [13, 478, 17, 31, "o"], [13, 479, 17, 31], [13, 482, 17, 31, "Object"], [13, 488, 17, 31], [13, 489, 17, 31, "defineProperty"], [13, 503, 17, 31], [13, 508, 17, 31, "Object"], [13, 514, 17, 31], [13, 515, 17, 31, "getOwnPropertyDescriptor"], [13, 539, 17, 31], [13, 540, 17, 31, "e"], [13, 541, 17, 31], [13, 543, 17, 31, "_t"], [13, 545, 17, 31], [13, 552, 17, 31, "i"], [13, 553, 17, 31], [13, 554, 17, 31, "get"], [13, 557, 17, 31], [13, 561, 17, 31, "i"], [13, 562, 17, 31], [13, 563, 17, 31, "set"], [13, 566, 17, 31], [13, 570, 17, 31, "o"], [13, 571, 17, 31], [13, 572, 17, 31, "f"], [13, 573, 17, 31], [13, 575, 17, 31, "_t"], [13, 577, 17, 31], [13, 579, 17, 31, "i"], [13, 580, 17, 31], [13, 584, 17, 31, "f"], [13, 585, 17, 31], [13, 586, 17, 31, "_t"], [13, 588, 17, 31], [13, 592, 17, 31, "e"], [13, 593, 17, 31], [13, 594, 17, 31, "_t"], [13, 596, 17, 31], [13, 607, 17, 31, "f"], [13, 608, 17, 31], [13, 613, 17, 31, "e"], [13, 614, 17, 31], [13, 616, 17, 31, "t"], [13, 617, 17, 31], [14, 2, 32, 0], [14, 11, 32, 9, "getLinkRanges"], [14, 24, 32, 22, "getLinkRanges"], [14, 25, 32, 23, "string"], [14, 31, 32, 37], [14, 33, 32, 62], [15, 4, 33, 2], [15, 8, 33, 8, "regex"], [15, 13, 33, 13], [15, 16, 33, 16], [15, 47, 33, 47], [16, 4, 34, 2], [16, 8, 34, 8, "matches"], [16, 15, 34, 15], [16, 18, 34, 18], [16, 20, 34, 20], [17, 4, 36, 2], [17, 8, 36, 6, "regexResult"], [17, 19, 36, 44], [18, 4, 37, 2], [18, 11, 37, 9], [18, 12, 37, 10, "regexResult"], [18, 23, 37, 21], [18, 26, 37, 24, "regex"], [18, 31, 37, 29], [18, 32, 37, 30, "exec"], [18, 36, 37, 34], [18, 37, 37, 35, "string"], [18, 43, 37, 41], [18, 44, 37, 42], [18, 50, 37, 48], [18, 54, 37, 52], [18, 56, 37, 54], [19, 6, 38, 4], [19, 10, 38, 8, "regexResult"], [19, 21, 38, 19], [19, 25, 38, 23], [19, 29, 38, 27], [19, 31, 38, 29], [20, 8, 39, 6, "matches"], [20, 15, 39, 13], [20, 16, 39, 14, "push"], [20, 20, 39, 18], [20, 21, 39, 19], [21, 10, 40, 8, "lowerBound"], [21, 20, 40, 18], [21, 22, 40, 20, "regexResult"], [21, 33, 40, 31], [21, 34, 40, 32, "index"], [21, 39, 40, 37], [22, 10, 41, 8, "upperBound"], [22, 20, 41, 18], [22, 22, 41, 20, "regex"], [22, 27, 41, 25], [22, 28, 41, 26, "lastIndex"], [23, 8, 42, 6], [23, 9, 42, 7], [23, 10, 42, 8], [24, 6, 43, 4], [25, 4, 44, 2], [26, 4, 46, 2], [26, 11, 46, 9, "matches"], [26, 18, 46, 16], [27, 2, 47, 0], [28, 2, 49, 0], [28, 11, 49, 9, "TappableLinks"], [28, 24, 49, 22, "TappableLinks"], [28, 25, 49, 23, "props"], [28, 30, 52, 1], [28, 32, 52, 15], [29, 4, 52, 15], [29, 8, 52, 15, "_this"], [29, 13, 52, 15], [30, 4, 53, 2], [30, 8, 53, 8, "matches"], [30, 15, 53, 15], [30, 18, 53, 18, "getLinkRanges"], [30, 31, 53, 31], [30, 32, 53, 32, "props"], [30, 37, 53, 37], [30, 38, 53, 38, "content"], [30, 45, 53, 45], [30, 46, 53, 46], [31, 4, 55, 2], [31, 8, 55, 6, "matches"], [31, 15, 55, 13], [31, 16, 55, 14, "length"], [31, 22, 55, 20], [31, 27, 55, 25], [31, 28, 55, 26], [31, 30, 55, 28], [32, 6, 57, 4], [32, 26, 57, 11], [32, 30, 57, 11, "_jsxDevRuntime"], [32, 44, 57, 11], [32, 45, 57, 11, "jsxDEV"], [32, 51, 57, 11], [32, 53, 57, 12, "_Text"], [32, 58, 57, 12], [32, 59, 57, 12, "default"], [32, 66, 57, 16], [33, 8, 57, 17, "style"], [33, 13, 57, 22], [33, 15, 57, 24, "props"], [33, 20, 57, 29], [33, 21, 57, 30, "style"], [33, 26, 57, 36], [34, 8, 57, 36, "children"], [34, 16, 57, 36], [34, 18, 57, 38, "props"], [34, 23, 57, 43], [34, 24, 57, 44, "content"], [35, 6, 57, 51], [36, 8, 57, 51, "fileName"], [36, 16, 57, 51], [36, 18, 57, 51, "_jsxFileName"], [36, 30, 57, 51], [37, 8, 57, 51, "lineNumber"], [37, 18, 57, 51], [38, 8, 57, 51, "columnNumber"], [38, 20, 57, 51], [39, 6, 57, 51], [39, 13, 57, 58], [39, 14, 57, 59], [40, 4, 58, 2], [41, 4, 62, 2], [41, 8, 62, 8, "fragments"], [41, 17, 62, 36], [41, 20, 62, 39], [41, 22, 62, 41], [42, 4, 63, 2], [42, 8, 63, 6, "indexCounter"], [42, 20, 63, 18], [42, 23, 63, 21], [42, 24, 63, 22], [43, 4, 64, 2], [43, 8, 64, 6, "startIndex"], [43, 18, 64, 16], [43, 21, 64, 19], [43, 22, 64, 20], [44, 4, 64, 21], [44, 8, 64, 21, "_loop"], [44, 13, 64, 21], [44, 25, 64, 21, "_loop"], [44, 26, 64, 21], [44, 28, 66, 35], [45, 6, 67, 4], [45, 10, 67, 8, "startIndex"], [45, 20, 67, 18], [45, 23, 67, 21, "linkRange"], [45, 32, 67, 30], [45, 33, 67, 31, "lowerBound"], [45, 43, 67, 41], [45, 45, 67, 43], [46, 8, 68, 6], [46, 12, 68, 12, "text"], [46, 17, 68, 16], [46, 20, 68, 19, "props"], [46, 25, 68, 24], [46, 26, 68, 25, "content"], [46, 33, 68, 32], [46, 34, 68, 33, "substring"], [46, 43, 68, 42], [46, 44, 68, 43, "startIndex"], [46, 54, 68, 53], [46, 56, 68, 55, "linkRange"], [46, 65, 68, 64], [46, 66, 68, 65, "lowerBound"], [46, 76, 68, 75], [46, 77, 68, 76], [47, 8, 69, 6, "fragments"], [47, 17, 69, 15], [47, 18, 69, 16, "push"], [47, 22, 69, 20], [47, 36, 69, 21], [47, 40, 69, 21, "_jsxDevRuntime"], [47, 54, 69, 21], [47, 55, 69, 21, "jsxDEV"], [47, 61, 69, 21], [47, 63, 69, 22, "_Text"], [47, 68, 69, 22], [47, 69, 69, 22, "default"], [47, 76, 69, 26], [48, 10, 69, 26, "children"], [48, 18, 69, 26], [48, 20, 69, 49, "text"], [49, 8, 69, 53], [49, 11, 69, 32], [49, 13, 69, 34, "indexCounter"], [49, 25, 69, 46], [50, 10, 69, 46, "fileName"], [50, 18, 69, 46], [50, 20, 69, 46, "_jsxFileName"], [50, 32, 69, 46], [51, 10, 69, 46, "lineNumber"], [51, 20, 69, 46], [52, 10, 69, 46, "columnNumber"], [52, 22, 69, 46], [53, 8, 69, 46], [53, 11, 69, 46, "_this"], [53, 16, 69, 60], [53, 17, 69, 61], [53, 18, 69, 62], [54, 6, 70, 4], [55, 6, 72, 4], [55, 10, 72, 10, "link"], [55, 14, 72, 14], [55, 17, 72, 17, "props"], [55, 22, 72, 22], [55, 23, 72, 23, "content"], [55, 30, 72, 30], [55, 31, 72, 31, "substring"], [55, 40, 72, 40], [55, 41, 73, 6, "linkRange"], [55, 50, 73, 15], [55, 51, 73, 16, "lowerBound"], [55, 61, 73, 26], [55, 63, 74, 6, "linkRange"], [55, 72, 74, 15], [55, 73, 74, 16, "upperBound"], [55, 83, 75, 4], [55, 84, 75, 5], [56, 6, 76, 4, "fragments"], [56, 15, 76, 13], [56, 16, 76, 14, "push"], [56, 20, 76, 18], [56, 34, 77, 6], [56, 38, 77, 6, "_jsxDevRuntime"], [56, 52, 77, 6], [56, 53, 77, 6, "jsxDEV"], [56, 59, 77, 6], [56, 61, 77, 7, "_Text"], [56, 66, 77, 7], [56, 67, 77, 7, "default"], [56, 74, 77, 11], [57, 8, 78, 8, "onPress"], [57, 15, 78, 15], [57, 17, 78, 17, "onPress"], [57, 18, 78, 17], [57, 23, 78, 23], [58, 10, 80, 10, "Linking"], [58, 26, 80, 17], [58, 27, 80, 18, "openURL"], [58, 34, 80, 25], [58, 35, 80, 26, "link"], [58, 39, 80, 30], [58, 40, 80, 31], [59, 8, 81, 8], [59, 9, 81, 10], [60, 8, 83, 8, "style"], [60, 13, 83, 13], [60, 15, 83, 15, "styles"], [60, 21, 83, 21], [60, 22, 83, 22, "linkText"], [60, 30, 83, 31], [61, 8, 83, 31, "children"], [61, 16, 83, 31], [61, 18, 84, 9, "link"], [62, 6, 84, 13], [62, 9, 82, 13], [62, 11, 82, 15, "indexCounter"], [62, 23, 82, 27], [63, 8, 82, 27, "fileName"], [63, 16, 82, 27], [63, 18, 82, 27, "_jsxFileName"], [63, 30, 82, 27], [64, 8, 82, 27, "lineNumber"], [64, 18, 82, 27], [65, 8, 82, 27, "columnNumber"], [65, 20, 82, 27], [66, 6, 82, 27], [66, 9, 82, 27, "_this"], [66, 14, 85, 12], [66, 15, 86, 4], [66, 16, 86, 5], [67, 6, 88, 4, "startIndex"], [67, 16, 88, 14], [67, 19, 88, 17, "linkRange"], [67, 28, 88, 26], [67, 29, 88, 27, "upperBound"], [67, 39, 88, 37], [68, 4, 89, 2], [68, 5, 89, 3], [69, 4, 66, 2], [69, 9, 66, 7], [69, 13, 66, 13, "linkRange"], [69, 22, 66, 22], [69, 26, 66, 26, "matches"], [69, 33, 66, 33], [70, 6, 66, 33, "_loop"], [70, 11, 66, 33], [71, 4, 66, 33], [72, 4, 91, 2], [72, 8, 91, 6, "startIndex"], [72, 18, 91, 16], [72, 21, 91, 19, "props"], [72, 26, 91, 24], [72, 27, 91, 25, "content"], [72, 34, 91, 32], [72, 35, 91, 33, "length"], [72, 41, 91, 39], [72, 43, 91, 41], [73, 6, 92, 4], [73, 10, 92, 10, "text"], [73, 14, 92, 14], [73, 17, 92, 17, "props"], [73, 22, 92, 22], [73, 23, 92, 23, "content"], [73, 30, 92, 30], [73, 31, 92, 31, "substring"], [73, 40, 92, 40], [73, 41, 92, 41, "startIndex"], [73, 51, 92, 51], [73, 52, 92, 52], [74, 6, 93, 4, "fragments"], [74, 15, 93, 13], [74, 16, 93, 14, "push"], [74, 20, 93, 18], [74, 34, 94, 6], [74, 38, 94, 6, "_jsxDevRuntime"], [74, 52, 94, 6], [74, 53, 94, 6, "jsxDEV"], [74, 59, 94, 6], [74, 61, 94, 7, "_Text"], [74, 66, 94, 7], [74, 67, 94, 7, "default"], [74, 74, 94, 11], [75, 8, 94, 33, "style"], [75, 13, 94, 38], [75, 15, 94, 40, "props"], [75, 20, 94, 45], [75, 21, 94, 46, "style"], [75, 26, 94, 52], [76, 8, 94, 52, "children"], [76, 16, 94, 52], [76, 18, 95, 9, "text"], [77, 6, 95, 13], [77, 9, 94, 17], [77, 11, 94, 19, "indexCounter"], [77, 23, 94, 31], [78, 8, 94, 31, "fileName"], [78, 16, 94, 31], [78, 18, 94, 31, "_jsxFileName"], [78, 30, 94, 31], [79, 8, 94, 31, "lineNumber"], [79, 18, 94, 31], [80, 8, 94, 31, "columnNumber"], [80, 20, 94, 31], [81, 6, 94, 31], [81, 13, 96, 12], [81, 14, 97, 4], [81, 15, 97, 5], [82, 4, 98, 2], [83, 4, 100, 2], [83, 24, 100, 9], [83, 28, 100, 9, "_jsxDevRuntime"], [83, 42, 100, 9], [83, 43, 100, 9, "jsxDEV"], [83, 49, 100, 9], [83, 51, 100, 10, "_Text"], [83, 56, 100, 10], [83, 57, 100, 10, "default"], [83, 64, 100, 14], [84, 6, 100, 15, "style"], [84, 11, 100, 20], [84, 13, 100, 22, "props"], [84, 18, 100, 27], [84, 19, 100, 28, "style"], [84, 24, 100, 34], [85, 6, 100, 34, "children"], [85, 14, 100, 34], [85, 16, 100, 36, "fragments"], [86, 4, 100, 45], [87, 6, 100, 45, "fileName"], [87, 14, 100, 45], [87, 16, 100, 45, "_jsxFileName"], [87, 28, 100, 45], [88, 6, 100, 45, "lineNumber"], [88, 16, 100, 45], [89, 6, 100, 45, "columnNumber"], [89, 18, 100, 45], [90, 4, 100, 45], [90, 11, 100, 52], [90, 12, 100, 53], [91, 2, 101, 0], [92, 2, 103, 0], [92, 6, 103, 6, "cleanContent"], [92, 18, 103, 18], [92, 21, 103, 22, "content"], [92, 28, 103, 37], [92, 32, 104, 2, "content"], [92, 39, 104, 9], [92, 40, 104, 10, "replace"], [92, 47, 104, 17], [92, 48, 104, 18], [92, 99, 104, 69], [92, 101, 104, 71], [92, 103, 104, 73], [92, 104, 104, 74], [93, 2, 106, 0], [93, 11, 106, 9, "LogBoxMessage"], [93, 24, 106, 22, "LogBoxMessage"], [93, 25, 106, 23, "props"], [93, 30, 106, 35], [93, 32, 106, 49], [94, 4, 107, 2], [94, 8, 107, 2, "_props$message"], [94, 22, 107, 2], [94, 25, 107, 44, "props"], [94, 30, 107, 49], [94, 31, 107, 50, "message"], [94, 38, 107, 57], [95, 6, 107, 9, "content"], [95, 13, 107, 16], [95, 16, 107, 16, "_props$message"], [95, 30, 107, 16], [95, 31, 107, 9, "content"], [95, 38, 107, 16], [96, 6, 107, 18, "substitutions"], [96, 19, 107, 31], [96, 22, 107, 31, "_props$message"], [96, 36, 107, 31], [96, 37, 107, 18, "substitutions"], [96, 50, 107, 31], [97, 4, 109, 2], [97, 8, 109, 6, "props"], [97, 13, 109, 11], [97, 14, 109, 12, "plaintext"], [97, 23, 109, 21], [97, 28, 109, 26], [97, 32, 109, 30], [97, 34, 109, 32], [98, 6, 110, 4], [98, 26, 110, 11], [98, 30, 110, 11, "_jsxDevRuntime"], [98, 44, 110, 11], [98, 45, 110, 11, "jsxDEV"], [98, 51, 110, 11], [98, 53, 110, 12, "_Text"], [98, 58, 110, 12], [98, 59, 110, 12, "default"], [98, 66, 110, 16], [99, 8, 110, 16, "children"], [99, 16, 110, 16], [99, 18, 110, 18, "cleanContent"], [99, 30, 110, 30], [99, 31, 110, 31, "content"], [99, 38, 110, 38], [100, 6, 110, 39], [101, 8, 110, 39, "fileName"], [101, 16, 110, 39], [101, 18, 110, 39, "_jsxFileName"], [101, 30, 110, 39], [102, 8, 110, 39, "lineNumber"], [102, 18, 110, 39], [103, 8, 110, 39, "columnNumber"], [103, 20, 110, 39], [104, 6, 110, 39], [104, 13, 110, 46], [104, 14, 110, 47], [105, 4, 111, 2], [106, 4, 113, 2], [106, 8, 113, 8, "max<PERSON><PERSON><PERSON>"], [106, 17, 113, 17], [106, 20, 113, 20, "props"], [106, 25, 113, 25], [106, 26, 113, 26, "max<PERSON><PERSON><PERSON>"], [106, 35, 113, 35], [106, 39, 113, 39], [106, 43, 113, 43], [106, 46, 113, 46, "props"], [106, 51, 113, 51], [106, 52, 113, 52, "max<PERSON><PERSON><PERSON>"], [106, 61, 113, 61], [106, 64, 113, 64, "Infinity"], [106, 72, 113, 72], [107, 4, 114, 2], [107, 8, 114, 8, "substitutionStyle"], [107, 25, 114, 40], [107, 28, 114, 43, "props"], [107, 33, 114, 48], [107, 34, 114, 49, "style"], [107, 39, 114, 54], [108, 4, 115, 2], [108, 8, 115, 8, "elements"], [108, 16, 115, 16], [108, 19, 115, 19], [108, 21, 115, 21], [109, 4, 116, 2], [109, 8, 116, 6, "length"], [109, 14, 116, 12], [109, 17, 116, 15], [109, 18, 116, 16], [110, 4, 117, 2], [110, 8, 117, 8, "createUnderLength"], [110, 25, 117, 25], [110, 28, 117, 28, "createUnderLength"], [110, 29, 118, 4, "key"], [110, 32, 118, 15], [110, 34, 119, 4, "message"], [110, 41, 119, 19], [110, 43, 120, 4, "style"], [110, 48, 120, 31], [110, 53, 121, 7], [111, 6, 122, 4], [111, 10, 122, 8, "cleanMessage"], [111, 22, 122, 20], [111, 25, 122, 23, "cleanContent"], [111, 37, 122, 35], [111, 38, 122, 36, "message"], [111, 45, 122, 43], [111, 46, 122, 44], [112, 6, 124, 4], [112, 10, 124, 8, "props"], [112, 15, 124, 13], [112, 16, 124, 14, "max<PERSON><PERSON><PERSON>"], [112, 25, 124, 23], [112, 29, 124, 27], [112, 33, 124, 31], [112, 35, 124, 33], [113, 8, 125, 6, "cleanMessage"], [113, 20, 125, 18], [113, 23, 125, 21, "cleanMessage"], [113, 35, 125, 33], [113, 36, 125, 34, "slice"], [113, 41, 125, 39], [113, 42, 125, 40], [113, 43, 125, 41], [113, 45, 125, 43, "props"], [113, 50, 125, 48], [113, 51, 125, 49, "max<PERSON><PERSON><PERSON>"], [113, 60, 125, 58], [113, 63, 125, 61, "length"], [113, 69, 125, 67], [113, 70, 125, 68], [114, 6, 126, 4], [115, 6, 128, 4], [115, 10, 128, 8, "length"], [115, 16, 128, 14], [115, 19, 128, 17, "max<PERSON><PERSON><PERSON>"], [115, 28, 128, 26], [115, 30, 128, 28], [116, 8, 129, 6, "elements"], [116, 16, 129, 14], [116, 17, 129, 15, "push"], [116, 21, 129, 19], [116, 35, 130, 8], [116, 39, 130, 8, "_jsxDevRuntime"], [116, 53, 130, 8], [116, 54, 130, 8, "jsxDEV"], [116, 60, 130, 8], [116, 62, 130, 9, "TappableLinks"], [116, 75, 130, 22], [117, 10, 130, 23, "content"], [117, 17, 130, 30], [117, 19, 130, 32, "cleanMessage"], [117, 31, 130, 45], [118, 10, 130, 56, "style"], [118, 15, 130, 61], [118, 17, 130, 63, "style"], [119, 8, 130, 69], [119, 11, 130, 51, "key"], [119, 14, 130, 54], [120, 10, 130, 54, "fileName"], [120, 18, 130, 54], [120, 20, 130, 54, "_jsxFileName"], [120, 32, 130, 54], [121, 10, 130, 54, "lineNumber"], [121, 20, 130, 54], [122, 10, 130, 54, "columnNumber"], [122, 22, 130, 54], [123, 8, 130, 54], [123, 15, 130, 71], [123, 16, 131, 6], [123, 17, 131, 7], [124, 6, 132, 4], [125, 6, 134, 4, "length"], [125, 12, 134, 10], [125, 16, 134, 14, "cleanMessage"], [125, 28, 134, 26], [125, 29, 134, 27, "length"], [125, 35, 134, 33], [126, 4, 135, 2], [126, 5, 135, 3], [127, 4, 137, 2], [127, 8, 137, 8, "lastOffset"], [127, 18, 137, 18], [127, 21, 137, 21, "substitutions"], [127, 34, 137, 34], [127, 35, 137, 35, "reduce"], [127, 41, 137, 41], [127, 42, 137, 42], [127, 43, 137, 43, "prevOffset"], [127, 53, 137, 53], [127, 55, 137, 55, "substitution"], [127, 67, 137, 67], [127, 69, 137, 69, "index"], [127, 74, 137, 74], [127, 79, 137, 79], [128, 6, 138, 4], [128, 10, 138, 10, "key"], [128, 13, 138, 13], [128, 16, 138, 16, "String"], [128, 22, 138, 22], [128, 23, 138, 23, "index"], [128, 28, 138, 28], [128, 29, 138, 29], [129, 6, 140, 4], [129, 10, 140, 8, "substitution"], [129, 22, 140, 20], [129, 23, 140, 21, "offset"], [129, 29, 140, 27], [129, 32, 140, 30, "prevOffset"], [129, 42, 140, 40], [129, 44, 140, 42], [130, 8, 141, 6], [130, 12, 141, 12, "prevPart"], [130, 20, 141, 20], [130, 23, 141, 23, "content"], [130, 30, 141, 30], [130, 31, 141, 31, "slice"], [130, 36, 141, 36], [130, 37, 141, 37, "prevOffset"], [130, 47, 141, 47], [130, 49, 141, 49, "substitution"], [130, 61, 141, 61], [130, 62, 141, 62, "offset"], [130, 68, 141, 68], [130, 69, 141, 69], [131, 8, 143, 6, "createUnderLength"], [131, 25, 143, 23], [131, 26, 143, 24, "key"], [131, 29, 143, 27], [131, 31, 143, 29, "prevPart"], [131, 39, 143, 37], [131, 40, 143, 38], [132, 6, 144, 4], [133, 6, 146, 4], [133, 10, 146, 10, "substitutionPart"], [133, 26, 146, 26], [133, 29, 146, 29, "content"], [133, 36, 146, 36], [133, 37, 146, 37, "slice"], [133, 42, 146, 42], [133, 43, 147, 6, "substitution"], [133, 55, 147, 18], [133, 56, 147, 19, "offset"], [133, 62, 147, 25], [133, 64, 148, 6, "substitution"], [133, 76, 148, 18], [133, 77, 148, 19, "offset"], [133, 83, 148, 25], [133, 86, 148, 28, "substitution"], [133, 98, 148, 40], [133, 99, 148, 41, "length"], [133, 105, 149, 4], [133, 106, 149, 5], [134, 6, 151, 4, "createUnderLength"], [134, 23, 151, 21], [134, 24, 151, 22, "key"], [134, 27, 151, 25], [134, 30, 151, 28], [134, 34, 151, 32], [134, 36, 151, 34, "substitutionPart"], [134, 52, 151, 50], [134, 54, 151, 52, "substitutionStyle"], [134, 71, 151, 69], [134, 72, 151, 70], [135, 6, 152, 4], [135, 13, 152, 11, "substitution"], [135, 25, 152, 23], [135, 26, 152, 24, "offset"], [135, 32, 152, 30], [135, 35, 152, 33, "substitution"], [135, 47, 152, 45], [135, 48, 152, 46, "length"], [135, 54, 152, 52], [136, 4, 153, 2], [136, 5, 153, 3], [136, 7, 153, 5], [136, 8, 153, 6], [136, 9, 153, 7], [137, 4, 155, 2], [137, 8, 155, 6, "lastOffset"], [137, 18, 155, 16], [137, 21, 155, 19, "content"], [137, 28, 155, 26], [137, 29, 155, 27, "length"], [137, 35, 155, 33], [137, 37, 155, 35], [138, 6, 156, 4], [138, 10, 156, 10, "lastPart"], [138, 18, 156, 18], [138, 21, 156, 21, "content"], [138, 28, 156, 28], [138, 29, 156, 29, "slice"], [138, 34, 156, 34], [138, 35, 156, 35, "lastOffset"], [138, 45, 156, 45], [138, 46, 156, 46], [139, 6, 157, 4, "createUnderLength"], [139, 23, 157, 21], [139, 24, 157, 22], [139, 28, 157, 26], [139, 30, 157, 28, "lastPart"], [139, 38, 157, 36], [139, 39, 157, 37], [140, 4, 158, 2], [141, 4, 160, 2], [141, 24, 160, 9], [141, 28, 160, 9, "_jsxDevRuntime"], [141, 42, 160, 9], [141, 43, 160, 9, "jsxDEV"], [141, 49, 160, 9], [141, 51, 160, 9, "_jsxDevRuntime"], [141, 65, 160, 9], [141, 66, 160, 9, "Fragment"], [141, 74, 160, 9], [142, 6, 160, 9, "children"], [142, 14, 160, 9], [142, 16, 160, 12, "elements"], [143, 4, 160, 20], [143, 20, 160, 23], [143, 21, 160, 24], [144, 2, 161, 0], [145, 2, 163, 0], [145, 6, 163, 6, "styles"], [145, 12, 163, 12], [145, 15, 163, 15, "StyleSheet"], [145, 34, 163, 25], [145, 35, 163, 26, "create"], [145, 41, 163, 32], [145, 42, 163, 33], [146, 4, 164, 2, "linkText"], [146, 12, 164, 10], [146, 14, 164, 12], [147, 6, 165, 4, "textDecorationLine"], [147, 24, 165, 22], [147, 26, 165, 24], [148, 4, 166, 2], [149, 2, 167, 0], [149, 3, 167, 1], [149, 4, 167, 2], [150, 2, 167, 3], [150, 6, 167, 3, "_default"], [150, 14, 167, 3], [150, 17, 167, 3, "exports"], [150, 24, 167, 3], [150, 25, 167, 3, "default"], [150, 32, 167, 3], [150, 35, 169, 15, "LogBoxMessage"], [150, 48, 169, 28], [151, 0, 169, 28], [151, 3]], "functionMap": {"names": ["<global>", "getLinkRanges", "TappableLinks", "Text.props.onPress", "cleanContent", "LogBoxMessage", "createUnderLength", "substitutions.reduce$argument_0"], "mappings": "AAA;AC+B;CDe;AEE;iBC6B;SDG;CFoB;qBIE;0EJC;AKE;4BCW;GDkB;0CEE;GFgB;CLQ"}}, "type": "js/module"}]}