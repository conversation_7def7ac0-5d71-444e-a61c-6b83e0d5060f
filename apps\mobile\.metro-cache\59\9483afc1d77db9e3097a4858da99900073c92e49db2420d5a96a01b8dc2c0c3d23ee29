{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SequencedTransition = SequencedTransition;\n  function SequencedTransition(name, transitionData) {\n    var translateX = transitionData.translateX,\n      translateY = transitionData.translateY,\n      scaleX = transitionData.scaleX,\n      scaleY = transitionData.scaleY,\n      reversed = transitionData.reversed;\n    var scaleValue = reversed ? `1,${scaleX}` : `${scaleY},1`;\n    var sequencedTransition = {\n      name,\n      style: {\n        0: {\n          transform: [{\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }]\n        },\n        50: {\n          transform: [{\n            translateX: reversed ? `${translateX}px` : '0px',\n            translateY: reversed ? '0px' : `${translateY}px`,\n            scale: scaleValue\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '0px',\n            translateY: '0px',\n            scale: '1,1'\n          }]\n        }\n      },\n      duration: 300\n    };\n    return sequencedTransition;\n  }\n});", "lineCount": 44, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "SequencedTransition"], [7, 29, 1, 13], [7, 32, 1, 13, "SequencedTransition"], [7, 51, 1, 13], [8, 2, 4, 7], [8, 11, 4, 16, "SequencedTransition"], [8, 30, 4, 35, "SequencedTransition"], [8, 31, 5, 2, "name"], [8, 35, 5, 14], [8, 37, 6, 2, "transitionData"], [8, 51, 6, 32], [8, 53, 7, 2], [9, 4, 8, 2], [9, 8, 8, 10, "translateX"], [9, 18, 8, 20], [9, 21, 8, 63, "transitionData"], [9, 35, 8, 77], [9, 36, 8, 10, "translateX"], [9, 46, 8, 20], [10, 6, 8, 22, "translateY"], [10, 16, 8, 32], [10, 19, 8, 63, "transitionData"], [10, 33, 8, 77], [10, 34, 8, 22, "translateY"], [10, 44, 8, 32], [11, 6, 8, 34, "scaleX"], [11, 12, 8, 40], [11, 15, 8, 63, "transitionData"], [11, 29, 8, 77], [11, 30, 8, 34, "scaleX"], [11, 36, 8, 40], [12, 6, 8, 42, "scaleY"], [12, 12, 8, 48], [12, 15, 8, 63, "transitionData"], [12, 29, 8, 77], [12, 30, 8, 42, "scaleY"], [12, 36, 8, 48], [13, 6, 8, 50, "reversed"], [13, 14, 8, 58], [13, 17, 8, 63, "transitionData"], [13, 31, 8, 77], [13, 32, 8, 50, "reversed"], [13, 40, 8, 58], [14, 4, 10, 2], [14, 8, 10, 8, "scaleValue"], [14, 18, 10, 18], [14, 21, 10, 21, "reversed"], [14, 29, 10, 29], [14, 32, 10, 32], [14, 37, 10, 37, "scaleX"], [14, 43, 10, 43], [14, 45, 10, 45], [14, 48, 10, 48], [14, 51, 10, 51, "scaleY"], [14, 57, 10, 57], [14, 61, 10, 61], [15, 4, 12, 2], [15, 8, 12, 8, "sequencedTransition"], [15, 27, 12, 27], [15, 30, 12, 30], [16, 6, 13, 4, "name"], [16, 10, 13, 8], [17, 6, 14, 4, "style"], [17, 11, 14, 9], [17, 13, 14, 11], [18, 8, 15, 6], [18, 9, 15, 7], [18, 11, 15, 9], [19, 10, 16, 8, "transform"], [19, 19, 16, 17], [19, 21, 16, 19], [19, 22, 17, 10], [20, 12, 18, 12, "translateX"], [20, 22, 18, 22], [20, 24, 18, 24], [20, 27, 18, 27, "translateX"], [20, 37, 18, 37], [20, 41, 18, 41], [21, 12, 19, 12, "translateY"], [21, 22, 19, 22], [21, 24, 19, 24], [21, 27, 19, 27, "translateY"], [21, 37, 19, 37], [21, 41, 19, 41], [22, 12, 20, 12, "scale"], [22, 17, 20, 17], [22, 19, 20, 19], [22, 22, 20, 22, "scaleX"], [22, 28, 20, 28], [22, 32, 20, 32, "scaleY"], [22, 38, 20, 38], [23, 10, 21, 10], [23, 11, 21, 11], [24, 8, 23, 6], [24, 9, 23, 7], [25, 8, 24, 6], [25, 10, 24, 8], [25, 12, 24, 10], [26, 10, 25, 8, "transform"], [26, 19, 25, 17], [26, 21, 25, 19], [26, 22, 26, 10], [27, 12, 27, 12, "translateX"], [27, 22, 27, 22], [27, 24, 27, 24, "reversed"], [27, 32, 27, 32], [27, 35, 27, 35], [27, 38, 27, 38, "translateX"], [27, 48, 27, 48], [27, 52, 27, 52], [27, 55, 27, 55], [27, 60, 27, 60], [28, 12, 28, 12, "translateY"], [28, 22, 28, 22], [28, 24, 28, 24, "reversed"], [28, 32, 28, 32], [28, 35, 28, 35], [28, 40, 28, 40], [28, 43, 28, 43], [28, 46, 28, 46, "translateY"], [28, 56, 28, 56], [28, 60, 28, 60], [29, 12, 29, 12, "scale"], [29, 17, 29, 17], [29, 19, 29, 19, "scaleValue"], [30, 10, 30, 10], [30, 11, 30, 11], [31, 8, 32, 6], [31, 9, 32, 7], [32, 8, 33, 6], [32, 11, 33, 9], [32, 13, 33, 11], [33, 10, 34, 8, "transform"], [33, 19, 34, 17], [33, 21, 34, 19], [33, 22, 34, 20], [34, 12, 34, 22, "translateX"], [34, 22, 34, 32], [34, 24, 34, 34], [34, 29, 34, 39], [35, 12, 34, 41, "translateY"], [35, 22, 34, 51], [35, 24, 34, 53], [35, 29, 34, 58], [36, 12, 34, 60, "scale"], [36, 17, 34, 65], [36, 19, 34, 67], [37, 10, 34, 73], [37, 11, 34, 74], [38, 8, 35, 6], [39, 6, 36, 4], [39, 7, 36, 5], [40, 6, 37, 4, "duration"], [40, 14, 37, 12], [40, 16, 37, 14], [41, 4, 38, 2], [41, 5, 38, 3], [42, 4, 40, 2], [42, 11, 40, 9, "sequencedTransition"], [42, 30, 40, 28], [43, 2, 41, 0], [44, 0, 41, 1], [44, 3]], "functionMap": {"names": ["<global>", "SequencedTransition"], "mappings": "AAA;OCG;CDqC"}}, "type": "js/module"}]}