{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Uuidv5Namespace = void 0;\n  /**\n   * Collection of utilities used for generating Universally Unique Identifiers.\n   */\n  /**\n   * Default namespaces for UUID v5 defined in RFC 4122\n   */\n  var Uuidv5Namespace = exports.Uuidv5Namespace = /*#__PURE__*/function (Uuidv5Namespace) {\n    // Source of the UUIDs: https://datatracker.ietf.org/doc/html/rfc4122\n    Uuidv5Namespace[\"dns\"] = \"6ba7b810-9dad-11d1-80b4-00c04fd430c8\";\n    Uuidv5Namespace[\"url\"] = \"6ba7b811-9dad-11d1-80b4-00c04fd430c8\";\n    Uuidv5Namespace[\"oid\"] = \"6ba7b812-9dad-11d1-80b4-00c04fd430c8\";\n    Uuidv5Namespace[\"x500\"] = \"6ba7b814-9dad-11d1-80b4-00c04fd430c8\";\n    return Uuidv5Namespace;\n  }({});\n});", "lineCount": 20, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 2, 16, 0], [10, 0, 17, 0], [11, 0, 18, 0], [12, 2, 16, 0], [12, 6, 19, 12, "Uuidv5Namespace"], [12, 21, 19, 27], [12, 24, 19, 27, "exports"], [12, 31, 19, 27], [12, 32, 19, 27, "Uuidv5Namespace"], [12, 47, 19, 27], [12, 73, 19, 12, "Uuidv5Namespace"], [12, 88, 19, 27], [13, 4, 20, 2], [14, 4, 19, 12, "Uuidv5Namespace"], [14, 19, 19, 27], [15, 4, 19, 12, "Uuidv5Namespace"], [15, 19, 19, 27], [16, 4, 19, 12, "Uuidv5Namespace"], [16, 19, 19, 27], [17, 4, 19, 12, "Uuidv5Namespace"], [17, 19, 19, 27], [18, 4, 19, 27], [18, 11, 19, 12, "Uuidv5Namespace"], [18, 26, 19, 27], [19, 2, 19, 27], [20, 0, 19, 27], [20, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}