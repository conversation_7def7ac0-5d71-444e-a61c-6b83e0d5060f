{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 42, "index": 56}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 148}, "end": {"line": 8, "column": 47, "index": 195}}], "key": "dQSfS57Pf/C96+Vvd1rktbJJov4=", "exportNames": ["*"]}}, {"name": "../core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 196}, "end": {"line": 13, "column": 17, "index": 300}}], "key": "OSA8xsmyvVLjxZOJ/QFvle2ua2I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedKeyboard = useAnimatedKeyboard;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _commonTypes = require(_dependencyMap[1], \"../commonTypes\");\n  var _core = require(_dependencyMap[2], \"../core\");\n  /**\n   * Lets you synchronously get the position and state of the keyboard.\n   *\n   * @param options - An additional keyboard configuration options.\n   * @returns An object with the current keyboard `height` and `state` as [shared\n   *   values](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value).\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/device/useAnimatedKeyboard\n   */\n  var _worklet_8736910501164_init_data = {\n    code: \"function useAnimatedKeyboardTs1(state,height){const{keyboardEventData}=this.__closure;keyboardEventData.state.value=state;keyboardEventData.height.value=height;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\hook\\\\useAnimatedKeyboard.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useAnimatedKeyboardTs1\\\",\\\"state\\\",\\\"height\\\",\\\"keyboardEventData\\\",\\\"__closure\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/hook/useAnimatedKeyboard.ts\\\"],\\\"mappings\\\":\\\"AAqCoD,QAAC,CAAAA,sBAAkBA,CAAAC,KAAA,CAAAC,MAAA,QAAAC,iBAAA,OAAAC,SAAA,CAEjED,iBAAiB,CAACF,KAAK,CAACI,KAAK,CAAGJ,KAAK,CACrCE,iBAAiB,CAACD,MAAM,CAACG,KAAK,CAAGH,MAAM,CACzC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_5450117293744_init_data = {\n    code: \"function useAnimatedKeyboardTs2(state,height){const{_keyboardEventData}=this.__closure;_keyboardEventData.state.value=state;_keyboardEventData.height.value=height;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\hook\\\\useAnimatedKeyboard.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useAnimatedKeyboardTs2\\\",\\\"state\\\",\\\"height\\\",\\\"_keyboardEventData\\\",\\\"__closure\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/hook/useAnimatedKeyboard.ts\\\"],\\\"mappings\\\":\\\"AAiDsD,QAAC,CAAAA,sBAAkBA,CAAAC,KAAA,CAAAC,MAAA,QAAAC,kBAAA,OAAAC,SAAA,CAEjED,kBAAiB,CAACF,KAAK,CAACI,KAAK,CAAGJ,KAAK,CACrCE,kBAAiB,CAACD,MAAM,CAACG,KAAK,CAAGH,MAAM,CACzC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function useAnimatedKeyboard() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      isStatusBarTranslucentAndroid: undefined,\n      isNavigationBarTranslucentAndroid: undefined\n    };\n    var ref = (0, _react.useRef)(null);\n    var listenerId = (0, _react.useRef)(-1);\n    var isSubscribed = (0, _react.useRef)(false);\n    if (ref.current === null) {\n      var keyboardEventData = {\n        state: (0, _core.makeMutable)(_commonTypes.KeyboardState.UNKNOWN),\n        height: (0, _core.makeMutable)(0)\n      };\n      listenerId.current = (0, _core.subscribeForKeyboardEvents)(function () {\n        var _e = [new global.Error(), -2, -27];\n        var useAnimatedKeyboardTs1 = function (state, height) {\n          keyboardEventData.state.value = state;\n          keyboardEventData.height.value = height;\n        };\n        useAnimatedKeyboardTs1.__closure = {\n          keyboardEventData\n        };\n        useAnimatedKeyboardTs1.__workletHash = 8736910501164;\n        useAnimatedKeyboardTs1.__initData = _worklet_8736910501164_init_data;\n        useAnimatedKeyboardTs1.__stackDetails = _e;\n        return useAnimatedKeyboardTs1;\n      }(), options);\n      ref.current = keyboardEventData;\n      isSubscribed.current = true;\n    }\n    (0, _react.useEffect)(() => {\n      if (isSubscribed.current === false && ref.current !== null) {\n        var _keyboardEventData = ref.current;\n        // subscribe again after Fast Refresh\n        listenerId.current = (0, _core.subscribeForKeyboardEvents)(function () {\n          var _e = [new global.Error(), -2, -27];\n          var useAnimatedKeyboardTs2 = function (state, height) {\n            _keyboardEventData.state.value = state;\n            _keyboardEventData.height.value = height;\n          };\n          useAnimatedKeyboardTs2.__closure = {\n            _keyboardEventData\n          };\n          useAnimatedKeyboardTs2.__workletHash = 5450117293744;\n          useAnimatedKeyboardTs2.__initData = _worklet_5450117293744_init_data;\n          useAnimatedKeyboardTs2.__stackDetails = _e;\n          return useAnimatedKeyboardTs2;\n        }(), options);\n        isSubscribed.current = true;\n      }\n      return () => {\n        (0, _core.unsubscribeFromKeyboardEvents)(listenerId.current);\n        isSubscribed.current = false;\n      };\n    }, []);\n    return ref.current;\n  }\n});", "lineCount": 88, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedKeyboard"], [7, 29, 1, 13], [7, 32, 1, 13, "useAnimatedKeyboard"], [7, 51, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 8, 0], [9, 6, 8, 0, "_commonTypes"], [9, 18, 8, 0], [9, 21, 8, 0, "require"], [9, 28, 8, 0], [9, 29, 8, 0, "_dependencyMap"], [9, 43, 8, 0], [10, 2, 9, 0], [10, 6, 9, 0, "_core"], [10, 11, 9, 0], [10, 14, 9, 0, "require"], [10, 21, 9, 0], [10, 22, 9, 0, "_dependencyMap"], [10, 36, 9, 0], [11, 2, 15, 0], [12, 0, 16, 0], [13, 0, 17, 0], [14, 0, 18, 0], [15, 0, 19, 0], [16, 0, 20, 0], [17, 0, 21, 0], [18, 0, 22, 0], [19, 2, 15, 0], [19, 6, 15, 0, "_worklet_8736910501164_init_data"], [19, 38, 15, 0], [20, 4, 15, 0, "code"], [20, 8, 15, 0], [21, 4, 15, 0, "location"], [21, 12, 15, 0], [22, 4, 15, 0, "sourceMap"], [22, 13, 15, 0], [23, 4, 15, 0, "version"], [23, 11, 15, 0], [24, 2, 15, 0], [25, 2, 15, 0], [25, 6, 15, 0, "_worklet_5450117293744_init_data"], [25, 38, 15, 0], [26, 4, 15, 0, "code"], [26, 8, 15, 0], [27, 4, 15, 0, "location"], [27, 12, 15, 0], [28, 4, 15, 0, "sourceMap"], [28, 13, 15, 0], [29, 4, 15, 0, "version"], [29, 11, 15, 0], [30, 2, 15, 0], [31, 2, 23, 7], [31, 11, 23, 16, "useAnimatedKeyboard"], [31, 30, 23, 35, "useAnimatedKeyboard"], [31, 31, 23, 35], [31, 33, 28, 24], [32, 4, 28, 24], [32, 8, 24, 2, "options"], [32, 15, 24, 34], [32, 18, 24, 34, "arguments"], [32, 27, 24, 34], [32, 28, 24, 34, "length"], [32, 34, 24, 34], [32, 42, 24, 34, "arguments"], [32, 51, 24, 34], [32, 59, 24, 34, "undefined"], [32, 68, 24, 34], [32, 71, 24, 34, "arguments"], [32, 80, 24, 34], [32, 86, 24, 37], [33, 6, 25, 4, "isStatusBarTranslucentAndroid"], [33, 35, 25, 33], [33, 37, 25, 35, "undefined"], [33, 46, 25, 44], [34, 6, 26, 4, "isNavigationBarTranslucentAndroid"], [34, 39, 26, 37], [34, 41, 26, 39, "undefined"], [35, 4, 27, 2], [35, 5, 27, 3], [36, 4, 29, 2], [36, 8, 29, 8, "ref"], [36, 11, 29, 11], [36, 14, 29, 14], [36, 18, 29, 14, "useRef"], [36, 31, 29, 20], [36, 33, 29, 50], [36, 37, 29, 54], [36, 38, 29, 55], [37, 4, 30, 2], [37, 8, 30, 8, "listenerId"], [37, 18, 30, 18], [37, 21, 30, 21], [37, 25, 30, 21, "useRef"], [37, 38, 30, 27], [37, 40, 30, 36], [37, 41, 30, 37], [37, 42, 30, 38], [37, 43, 30, 39], [38, 4, 31, 2], [38, 8, 31, 8, "isSubscribed"], [38, 20, 31, 20], [38, 23, 31, 23], [38, 27, 31, 23, "useRef"], [38, 40, 31, 29], [38, 42, 31, 39], [38, 47, 31, 44], [38, 48, 31, 45], [39, 4, 33, 2], [39, 8, 33, 6, "ref"], [39, 11, 33, 9], [39, 12, 33, 10, "current"], [39, 19, 33, 17], [39, 24, 33, 22], [39, 28, 33, 26], [39, 30, 33, 28], [40, 6, 34, 4], [40, 10, 34, 10, "keyboardEventData"], [40, 27, 34, 49], [40, 30, 34, 52], [41, 8, 35, 6, "state"], [41, 13, 35, 11], [41, 15, 35, 13], [41, 19, 35, 13, "makeMutable"], [41, 36, 35, 24], [41, 38, 35, 40, "KeyboardState"], [41, 64, 35, 53], [41, 65, 35, 54, "UNKNOWN"], [41, 72, 35, 61], [41, 73, 35, 62], [42, 8, 36, 6, "height"], [42, 14, 36, 12], [42, 16, 36, 14], [42, 20, 36, 14, "makeMutable"], [42, 37, 36, 25], [42, 39, 36, 26], [42, 40, 36, 27], [43, 6, 37, 4], [43, 7, 37, 5], [44, 6, 38, 4, "listenerId"], [44, 16, 38, 14], [44, 17, 38, 15, "current"], [44, 24, 38, 22], [44, 27, 38, 25], [44, 31, 38, 25, "subscribeForKeyboardEvents"], [44, 63, 38, 51], [44, 65, 38, 52], [45, 8, 38, 52], [45, 12, 38, 52, "_e"], [45, 14, 38, 52], [45, 22, 38, 52, "global"], [45, 28, 38, 52], [45, 29, 38, 52, "Error"], [45, 34, 38, 52], [46, 8, 38, 52], [46, 12, 38, 52, "useAnimatedKeyboardTs1"], [46, 34, 38, 52], [46, 46, 38, 52, "useAnimatedKeyboardTs1"], [46, 47, 38, 53, "state"], [46, 52, 38, 58], [46, 54, 38, 60, "height"], [46, 60, 38, 66], [46, 62, 38, 71], [47, 10, 40, 6, "keyboardEventData"], [47, 27, 40, 23], [47, 28, 40, 24, "state"], [47, 33, 40, 29], [47, 34, 40, 30, "value"], [47, 39, 40, 35], [47, 42, 40, 38, "state"], [47, 47, 40, 43], [48, 10, 41, 6, "keyboardEventData"], [48, 27, 41, 23], [48, 28, 41, 24, "height"], [48, 34, 41, 30], [48, 35, 41, 31, "value"], [48, 40, 41, 36], [48, 43, 41, 39, "height"], [48, 49, 41, 45], [49, 8, 42, 4], [49, 9, 42, 5], [50, 8, 42, 5, "useAnimatedKeyboardTs1"], [50, 30, 42, 5], [50, 31, 42, 5, "__closure"], [50, 40, 42, 5], [51, 10, 42, 5, "keyboardEventData"], [52, 8, 42, 5], [53, 8, 42, 5, "useAnimatedKeyboardTs1"], [53, 30, 42, 5], [53, 31, 42, 5, "__workletHash"], [53, 44, 42, 5], [54, 8, 42, 5, "useAnimatedKeyboardTs1"], [54, 30, 42, 5], [54, 31, 42, 5, "__initData"], [54, 41, 42, 5], [54, 44, 42, 5, "_worklet_8736910501164_init_data"], [54, 76, 42, 5], [55, 8, 42, 5, "useAnimatedKeyboardTs1"], [55, 30, 42, 5], [55, 31, 42, 5, "__stackDetails"], [55, 45, 42, 5], [55, 48, 42, 5, "_e"], [55, 50, 42, 5], [56, 8, 42, 5], [56, 15, 42, 5, "useAnimatedKeyboardTs1"], [56, 37, 42, 5], [57, 6, 42, 5], [57, 7, 38, 52], [57, 11, 42, 7, "options"], [57, 18, 42, 14], [57, 19, 42, 15], [58, 6, 43, 4, "ref"], [58, 9, 43, 7], [58, 10, 43, 8, "current"], [58, 17, 43, 15], [58, 20, 43, 18, "keyboardEventData"], [58, 37, 43, 35], [59, 6, 44, 4, "isSubscribed"], [59, 18, 44, 16], [59, 19, 44, 17, "current"], [59, 26, 44, 24], [59, 29, 44, 27], [59, 33, 44, 31], [60, 4, 45, 2], [61, 4, 46, 2], [61, 8, 46, 2, "useEffect"], [61, 24, 46, 11], [61, 26, 46, 12], [61, 32, 46, 18], [62, 6, 47, 4], [62, 10, 47, 8, "isSubscribed"], [62, 22, 47, 20], [62, 23, 47, 21, "current"], [62, 30, 47, 28], [62, 35, 47, 33], [62, 40, 47, 38], [62, 44, 47, 42, "ref"], [62, 47, 47, 45], [62, 48, 47, 46, "current"], [62, 55, 47, 53], [62, 60, 47, 58], [62, 64, 47, 62], [62, 66, 47, 64], [63, 8, 48, 6], [63, 12, 48, 12, "keyboardEventData"], [63, 30, 48, 29], [63, 33, 48, 32, "ref"], [63, 36, 48, 35], [63, 37, 48, 36, "current"], [63, 44, 48, 43], [64, 8, 49, 6], [65, 8, 50, 6, "listenerId"], [65, 18, 50, 16], [65, 19, 50, 17, "current"], [65, 26, 50, 24], [65, 29, 50, 27], [65, 33, 50, 27, "subscribeForKeyboardEvents"], [65, 65, 50, 53], [65, 67, 50, 54], [66, 10, 50, 54], [66, 14, 50, 54, "_e"], [66, 16, 50, 54], [66, 24, 50, 54, "global"], [66, 30, 50, 54], [66, 31, 50, 54, "Error"], [66, 36, 50, 54], [67, 10, 50, 54], [67, 14, 50, 54, "useAnimatedKeyboardTs2"], [67, 36, 50, 54], [67, 48, 50, 54, "useAnimatedKeyboardTs2"], [67, 49, 50, 55, "state"], [67, 54, 50, 60], [67, 56, 50, 62, "height"], [67, 62, 50, 68], [67, 64, 50, 73], [68, 12, 52, 8, "keyboardEventData"], [68, 30, 52, 25], [68, 31, 52, 26, "state"], [68, 36, 52, 31], [68, 37, 52, 32, "value"], [68, 42, 52, 37], [68, 45, 52, 40, "state"], [68, 50, 52, 45], [69, 12, 53, 8, "keyboardEventData"], [69, 30, 53, 25], [69, 31, 53, 26, "height"], [69, 37, 53, 32], [69, 38, 53, 33, "value"], [69, 43, 53, 38], [69, 46, 53, 41, "height"], [69, 52, 53, 47], [70, 10, 54, 6], [70, 11, 54, 7], [71, 10, 54, 7, "useAnimatedKeyboardTs2"], [71, 32, 54, 7], [71, 33, 54, 7, "__closure"], [71, 42, 54, 7], [72, 12, 54, 7, "_keyboardEventData"], [73, 10, 54, 7], [74, 10, 54, 7, "useAnimatedKeyboardTs2"], [74, 32, 54, 7], [74, 33, 54, 7, "__workletHash"], [74, 46, 54, 7], [75, 10, 54, 7, "useAnimatedKeyboardTs2"], [75, 32, 54, 7], [75, 33, 54, 7, "__initData"], [75, 43, 54, 7], [75, 46, 54, 7, "_worklet_5450117293744_init_data"], [75, 78, 54, 7], [76, 10, 54, 7, "useAnimatedKeyboardTs2"], [76, 32, 54, 7], [76, 33, 54, 7, "__stackDetails"], [76, 47, 54, 7], [76, 50, 54, 7, "_e"], [76, 52, 54, 7], [77, 10, 54, 7], [77, 17, 54, 7, "useAnimatedKeyboardTs2"], [77, 39, 54, 7], [78, 8, 54, 7], [78, 9, 50, 54], [78, 13, 54, 9, "options"], [78, 20, 54, 16], [78, 21, 54, 17], [79, 8, 55, 6, "isSubscribed"], [79, 20, 55, 18], [79, 21, 55, 19, "current"], [79, 28, 55, 26], [79, 31, 55, 29], [79, 35, 55, 33], [80, 6, 56, 4], [81, 6, 57, 4], [81, 13, 57, 11], [81, 19, 57, 17], [82, 8, 58, 6], [82, 12, 58, 6, "unsubscribeFromKeyboardEvents"], [82, 47, 58, 35], [82, 49, 58, 36, "listenerId"], [82, 59, 58, 46], [82, 60, 58, 47, "current"], [82, 67, 58, 54], [82, 68, 58, 55], [83, 8, 59, 6, "isSubscribed"], [83, 20, 59, 18], [83, 21, 59, 19, "current"], [83, 28, 59, 26], [83, 31, 59, 29], [83, 36, 59, 34], [84, 6, 60, 4], [84, 7, 60, 5], [85, 4, 61, 2], [85, 5, 61, 3], [85, 7, 61, 5], [85, 9, 61, 7], [85, 10, 61, 8], [86, 4, 62, 2], [86, 11, 62, 9, "ref"], [86, 14, 62, 12], [86, 15, 62, 13, "current"], [86, 22, 62, 20], [87, 2, 63, 0], [88, 0, 63, 1], [88, 3]], "functionMap": {"names": ["<global>", "useAnimatedKeyboard", "subscribeForKeyboardEvents$argument_0", "useEffect$argument_0", "<anonymous>"], "mappings": "AAA;OCsB;oDCe;KDI;YEI;sDDI;OCI;WCG;KDG;GFC;CDE"}}, "type": "js/module"}]}