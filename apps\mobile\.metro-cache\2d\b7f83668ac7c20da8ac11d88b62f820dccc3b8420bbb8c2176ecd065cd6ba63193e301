{"dependencies": [{"name": "../animationParser", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 71, "index": 85}}], "key": "NS2upIa4aHN1XdKmQKcusYkE9o0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.RollOutData = exports.RollOut = exports.RollInData = exports.RollIn = void 0;\n  var _animationParser = require(_dependencyMap[0], \"../animationParser\");\n  var DEFAULT_ROLL_TIME = 0.3;\n  var RollInData = exports.RollInData = {\n    RollInLeft: {\n      name: 'RollInLeft',\n      style: {\n        0: {\n          transform: [{\n            translateX: '-100vw',\n            rotate: '-180deg'\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '0vw',\n            rotate: '0deg'\n          }]\n        }\n      },\n      duration: DEFAULT_ROLL_TIME\n    },\n    RollInRight: {\n      name: 'RollInRight',\n      style: {\n        0: {\n          transform: [{\n            translateX: '100vw',\n            rotate: '180deg'\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '0vw',\n            rotate: '0deg'\n          }]\n        }\n      },\n      duration: DEFAULT_ROLL_TIME\n    }\n  };\n  var RollOutData = exports.RollOutData = {\n    RollOutLeft: {\n      name: 'RollOutLeft',\n      style: {\n        0: {\n          transform: [{\n            translateX: '0vw',\n            rotate: '0deg'\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '-100vw',\n            rotate: '-180deg'\n          }]\n        }\n      },\n      duration: DEFAULT_ROLL_TIME\n    },\n    RollOutRight: {\n      name: 'RollOutRight',\n      style: {\n        0: {\n          transform: [{\n            translateX: '0vw',\n            rotate: '0deg'\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '100vw',\n            rotate: '180deg'\n          }]\n        }\n      },\n      duration: DEFAULT_ROLL_TIME\n    }\n  };\n  var RollIn = exports.RollIn = {\n    RollInLeft: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollInData.RollInLeft),\n      duration: RollInData.RollInLeft.duration\n    },\n    RollInRight: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollInData.RollInRight),\n      duration: RollInData.RollInRight.duration\n    }\n  };\n  var RollOut = exports.RollOut = {\n    RollOutLeft: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollOutData.RollOutLeft),\n      duration: RollOutData.RollOutLeft.duration\n    },\n    RollOutRight: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollOutData.RollOutRight),\n      duration: RollOutData.RollOutRight.duration\n    }\n  };\n});", "lineCount": 106, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "RollOutData"], [7, 21, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [7, 32, 1, 13, "RollOut"], [7, 39, 1, 13], [7, 42, 1, 13, "exports"], [7, 49, 1, 13], [7, 50, 1, 13, "RollInData"], [7, 60, 1, 13], [7, 63, 1, 13, "exports"], [7, 70, 1, 13], [7, 71, 1, 13, "RollIn"], [7, 77, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_animation<PERSON><PERSON>er"], [8, 22, 2, 0], [8, 25, 2, 0, "require"], [8, 32, 2, 0], [8, 33, 2, 0, "_dependencyMap"], [8, 47, 2, 0], [9, 2, 4, 0], [9, 6, 4, 6, "DEFAULT_ROLL_TIME"], [9, 23, 4, 23], [9, 26, 4, 26], [9, 29, 4, 29], [10, 2, 6, 7], [10, 6, 6, 13, "RollInData"], [10, 16, 6, 23], [10, 19, 6, 23, "exports"], [10, 26, 6, 23], [10, 27, 6, 23, "RollInData"], [10, 37, 6, 23], [10, 40, 6, 26], [11, 4, 7, 2, "RollInLeft"], [11, 14, 7, 12], [11, 16, 7, 14], [12, 6, 8, 4, "name"], [12, 10, 8, 8], [12, 12, 8, 10], [12, 24, 8, 22], [13, 6, 9, 4, "style"], [13, 11, 9, 9], [13, 13, 9, 11], [14, 8, 10, 6], [14, 9, 10, 7], [14, 11, 10, 9], [15, 10, 11, 8, "transform"], [15, 19, 11, 17], [15, 21, 11, 19], [15, 22, 11, 20], [16, 12, 11, 22, "translateX"], [16, 22, 11, 32], [16, 24, 11, 34], [16, 32, 11, 42], [17, 12, 11, 44, "rotate"], [17, 18, 11, 50], [17, 20, 11, 52], [18, 10, 11, 62], [18, 11, 11, 63], [19, 8, 12, 6], [19, 9, 12, 7], [20, 8, 13, 6], [20, 11, 13, 9], [20, 13, 13, 11], [21, 10, 14, 8, "transform"], [21, 19, 14, 17], [21, 21, 14, 19], [21, 22, 14, 20], [22, 12, 14, 22, "translateX"], [22, 22, 14, 32], [22, 24, 14, 34], [22, 29, 14, 39], [23, 12, 14, 41, "rotate"], [23, 18, 14, 47], [23, 20, 14, 49], [24, 10, 14, 56], [24, 11, 14, 57], [25, 8, 15, 6], [26, 6, 16, 4], [26, 7, 16, 5], [27, 6, 17, 4, "duration"], [27, 14, 17, 12], [27, 16, 17, 14, "DEFAULT_ROLL_TIME"], [28, 4, 18, 2], [28, 5, 18, 3], [29, 4, 20, 2, "RollInRight"], [29, 15, 20, 13], [29, 17, 20, 15], [30, 6, 21, 4, "name"], [30, 10, 21, 8], [30, 12, 21, 10], [30, 25, 21, 23], [31, 6, 22, 4, "style"], [31, 11, 22, 9], [31, 13, 22, 11], [32, 8, 23, 6], [32, 9, 23, 7], [32, 11, 23, 9], [33, 10, 24, 8, "transform"], [33, 19, 24, 17], [33, 21, 24, 19], [33, 22, 24, 20], [34, 12, 24, 22, "translateX"], [34, 22, 24, 32], [34, 24, 24, 34], [34, 31, 24, 41], [35, 12, 24, 43, "rotate"], [35, 18, 24, 49], [35, 20, 24, 51], [36, 10, 24, 60], [36, 11, 24, 61], [37, 8, 25, 6], [37, 9, 25, 7], [38, 8, 26, 6], [38, 11, 26, 9], [38, 13, 26, 11], [39, 10, 27, 8, "transform"], [39, 19, 27, 17], [39, 21, 27, 19], [39, 22, 27, 20], [40, 12, 27, 22, "translateX"], [40, 22, 27, 32], [40, 24, 27, 34], [40, 29, 27, 39], [41, 12, 27, 41, "rotate"], [41, 18, 27, 47], [41, 20, 27, 49], [42, 10, 27, 56], [42, 11, 27, 57], [43, 8, 28, 6], [44, 6, 29, 4], [44, 7, 29, 5], [45, 6, 30, 4, "duration"], [45, 14, 30, 12], [45, 16, 30, 14, "DEFAULT_ROLL_TIME"], [46, 4, 31, 2], [47, 2, 32, 0], [47, 3, 32, 1], [48, 2, 34, 7], [48, 6, 34, 13, "RollOutData"], [48, 17, 34, 24], [48, 20, 34, 24, "exports"], [48, 27, 34, 24], [48, 28, 34, 24, "RollOutData"], [48, 39, 34, 24], [48, 42, 34, 27], [49, 4, 35, 2, "RollOutLeft"], [49, 15, 35, 13], [49, 17, 35, 15], [50, 6, 36, 4, "name"], [50, 10, 36, 8], [50, 12, 36, 10], [50, 25, 36, 23], [51, 6, 37, 4, "style"], [51, 11, 37, 9], [51, 13, 37, 11], [52, 8, 38, 6], [52, 9, 38, 7], [52, 11, 38, 9], [53, 10, 39, 8, "transform"], [53, 19, 39, 17], [53, 21, 39, 19], [53, 22, 39, 20], [54, 12, 39, 22, "translateX"], [54, 22, 39, 32], [54, 24, 39, 34], [54, 29, 39, 39], [55, 12, 39, 41, "rotate"], [55, 18, 39, 47], [55, 20, 39, 49], [56, 10, 39, 56], [56, 11, 39, 57], [57, 8, 40, 6], [57, 9, 40, 7], [58, 8, 41, 6], [58, 11, 41, 9], [58, 13, 41, 11], [59, 10, 42, 8, "transform"], [59, 19, 42, 17], [59, 21, 42, 19], [59, 22, 42, 20], [60, 12, 42, 22, "translateX"], [60, 22, 42, 32], [60, 24, 42, 34], [60, 32, 42, 42], [61, 12, 42, 44, "rotate"], [61, 18, 42, 50], [61, 20, 42, 52], [62, 10, 42, 62], [62, 11, 42, 63], [63, 8, 43, 6], [64, 6, 44, 4], [64, 7, 44, 5], [65, 6, 45, 4, "duration"], [65, 14, 45, 12], [65, 16, 45, 14, "DEFAULT_ROLL_TIME"], [66, 4, 46, 2], [66, 5, 46, 3], [67, 4, 48, 2, "RollOutRight"], [67, 16, 48, 14], [67, 18, 48, 16], [68, 6, 49, 4, "name"], [68, 10, 49, 8], [68, 12, 49, 10], [68, 26, 49, 24], [69, 6, 50, 4, "style"], [69, 11, 50, 9], [69, 13, 50, 11], [70, 8, 51, 6], [70, 9, 51, 7], [70, 11, 51, 9], [71, 10, 52, 8, "transform"], [71, 19, 52, 17], [71, 21, 52, 19], [71, 22, 52, 20], [72, 12, 52, 22, "translateX"], [72, 22, 52, 32], [72, 24, 52, 34], [72, 29, 52, 39], [73, 12, 52, 41, "rotate"], [73, 18, 52, 47], [73, 20, 52, 49], [74, 10, 52, 56], [74, 11, 52, 57], [75, 8, 53, 6], [75, 9, 53, 7], [76, 8, 54, 6], [76, 11, 54, 9], [76, 13, 54, 11], [77, 10, 55, 8, "transform"], [77, 19, 55, 17], [77, 21, 55, 19], [77, 22, 55, 20], [78, 12, 55, 22, "translateX"], [78, 22, 55, 32], [78, 24, 55, 34], [78, 31, 55, 41], [79, 12, 55, 43, "rotate"], [79, 18, 55, 49], [79, 20, 55, 51], [80, 10, 55, 60], [80, 11, 55, 61], [81, 8, 56, 6], [82, 6, 57, 4], [82, 7, 57, 5], [83, 6, 58, 4, "duration"], [83, 14, 58, 12], [83, 16, 58, 14, "DEFAULT_ROLL_TIME"], [84, 4, 59, 2], [85, 2, 60, 0], [85, 3, 60, 1], [86, 2, 62, 7], [86, 6, 62, 13, "RollIn"], [86, 12, 62, 19], [86, 15, 62, 19, "exports"], [86, 22, 62, 19], [86, 23, 62, 19, "RollIn"], [86, 29, 62, 19], [86, 32, 62, 22], [87, 4, 63, 2, "RollInLeft"], [87, 14, 63, 12], [87, 16, 63, 14], [88, 6, 64, 4, "style"], [88, 11, 64, 9], [88, 13, 64, 11], [88, 17, 64, 11, "convertAnimationObjectToKeyframes"], [88, 67, 64, 44], [88, 69, 64, 45, "RollInData"], [88, 79, 64, 55], [88, 80, 64, 56, "RollInLeft"], [88, 90, 64, 66], [88, 91, 64, 67], [89, 6, 65, 4, "duration"], [89, 14, 65, 12], [89, 16, 65, 14, "RollInData"], [89, 26, 65, 24], [89, 27, 65, 25, "RollInLeft"], [89, 37, 65, 35], [89, 38, 65, 36, "duration"], [90, 4, 66, 2], [90, 5, 66, 3], [91, 4, 67, 2, "RollInRight"], [91, 15, 67, 13], [91, 17, 67, 15], [92, 6, 68, 4, "style"], [92, 11, 68, 9], [92, 13, 68, 11], [92, 17, 68, 11, "convertAnimationObjectToKeyframes"], [92, 67, 68, 44], [92, 69, 68, 45, "RollInData"], [92, 79, 68, 55], [92, 80, 68, 56, "RollInRight"], [92, 91, 68, 67], [92, 92, 68, 68], [93, 6, 69, 4, "duration"], [93, 14, 69, 12], [93, 16, 69, 14, "RollInData"], [93, 26, 69, 24], [93, 27, 69, 25, "RollInRight"], [93, 38, 69, 36], [93, 39, 69, 37, "duration"], [94, 4, 70, 2], [95, 2, 71, 0], [95, 3, 71, 1], [96, 2, 73, 7], [96, 6, 73, 13, "RollOut"], [96, 13, 73, 20], [96, 16, 73, 20, "exports"], [96, 23, 73, 20], [96, 24, 73, 20, "RollOut"], [96, 31, 73, 20], [96, 34, 73, 23], [97, 4, 74, 2, "RollOutLeft"], [97, 15, 74, 13], [97, 17, 74, 15], [98, 6, 75, 4, "style"], [98, 11, 75, 9], [98, 13, 75, 11], [98, 17, 75, 11, "convertAnimationObjectToKeyframes"], [98, 67, 75, 44], [98, 69, 75, 45, "RollOutData"], [98, 80, 75, 56], [98, 81, 75, 57, "RollOutLeft"], [98, 92, 75, 68], [98, 93, 75, 69], [99, 6, 76, 4, "duration"], [99, 14, 76, 12], [99, 16, 76, 14, "RollOutData"], [99, 27, 76, 25], [99, 28, 76, 26, "RollOutLeft"], [99, 39, 76, 37], [99, 40, 76, 38, "duration"], [100, 4, 77, 2], [100, 5, 77, 3], [101, 4, 78, 2, "RollOutRight"], [101, 16, 78, 14], [101, 18, 78, 16], [102, 6, 79, 4, "style"], [102, 11, 79, 9], [102, 13, 79, 11], [102, 17, 79, 11, "convertAnimationObjectToKeyframes"], [102, 67, 79, 44], [102, 69, 79, 45, "RollOutData"], [102, 80, 79, 56], [102, 81, 79, 57, "RollOutRight"], [102, 93, 79, 69], [102, 94, 79, 70], [103, 6, 80, 4, "duration"], [103, 14, 80, 12], [103, 16, 80, 14, "RollOutData"], [103, 27, 80, 25], [103, 28, 80, 26, "RollOutRight"], [103, 40, 80, 38], [103, 41, 80, 39, "duration"], [104, 4, 81, 2], [105, 2, 82, 0], [105, 3, 82, 1], [106, 0, 82, 2], [106, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}