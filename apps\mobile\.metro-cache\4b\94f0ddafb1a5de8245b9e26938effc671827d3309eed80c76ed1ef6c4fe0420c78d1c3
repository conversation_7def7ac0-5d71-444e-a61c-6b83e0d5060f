{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getUseOfValueInStyleWarning = getUseOfValueInStyleWarning;\n  function getUseOfValueInStyleWarning() {\n    return \"It looks like you might be using shared value's .value inside reanimated inline style. \" + 'If you want a component to update when shared value changes you should use the shared value' + ' directly instead of its current state represented by `.value`. See documentation here: ' + 'https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary/#animations-in-inline-styling';\n  }\n});", "lineCount": 11, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getUseOfValueInStyleWarning"], [7, 37, 1, 13], [7, 40, 1, 13, "getUseOfValueInStyleWarning"], [7, 67, 1, 13], [8, 2, 2, 7], [8, 11, 2, 16, "getUseOfValueInStyleWarning"], [8, 38, 2, 43, "getUseOfValueInStyleWarning"], [8, 39, 2, 43], [8, 41, 2, 46], [9, 4, 3, 2], [9, 11, 4, 4], [9, 100, 4, 93], [9, 103, 5, 4], [9, 196, 5, 97], [9, 199, 6, 4], [9, 289, 6, 94], [9, 292, 7, 4], [9, 401, 7, 113], [10, 2, 9, 0], [11, 0, 9, 1], [11, 3]], "functionMap": {"names": ["<global>", "getUseOfValueInStyleWarning"], "mappings": "AAA;OCC;CDO"}}, "type": "js/module"}]}