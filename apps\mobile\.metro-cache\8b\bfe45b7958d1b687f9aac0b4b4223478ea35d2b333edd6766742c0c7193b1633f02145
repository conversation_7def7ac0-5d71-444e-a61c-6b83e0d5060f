{"dependencies": [{"name": "../../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 103}, "end": {"line": 7, "column": 47, "index": 150}}], "key": "eT202ujluoOcHDbauyWnF/muvbc=", "exportNames": ["*"]}}, {"name": "../util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 151}, "end": {"line": 8, "column": 71, "index": 222}}], "key": "DTmef1GE5grNQfsvpIzpBlINy9c=", "exportNames": ["*"]}}, {"name": "./rigidDecay", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 223}, "end": {"line": 9, "column": 42, "index": 265}}], "key": "W3IznW8srC6ViEfYG3GKPtICuQM=", "exportNames": ["*"]}}, {"name": "./rubberBandDecay", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 266}, "end": {"line": 10, "column": 52, "index": 318}}], "key": "hJKtyPMEvN+A4Fq70f0swvAdysM=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 429}, "end": {"line": 17, "column": 50, "index": 479}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withDecay = void 0;\n  var _errors = require(_dependencyMap[0], \"../../errors\");\n  var _util = require(_dependencyMap[1], \"../util\");\n  var _rigidDecay = require(_dependencyMap[2], \"./rigidDecay\");\n  var _rubberBandDecay = require(_dependencyMap[3], \"./rubberBandDecay\");\n  var _utils = require(_dependencyMap[4], \"./utils\");\n  // TODO TYPESCRIPT This is a temporary type to get rid of .d.ts file.\n  var _worklet_9073199851290_init_data = {\n    code: \"function validateConfig_decayTs1(config){if(config.clamp){if(!Array.isArray(config.clamp)){throw new ReanimatedError(\\\"`config.clamp` must be an array but is \\\"+typeof config.clamp+\\\".\\\");}if(config.clamp.length!==2){throw new ReanimatedError(\\\"`clamp array` must contain 2 items but is given \\\"+config.clamp.length+\\\".\\\");}}if(config.velocityFactor<=0){throw new ReanimatedError(\\\"`config.velocityFactor` must be greater then 0 but is \\\"+config.velocityFactor+\\\".\\\");}if(config.rubberBandEffect&&!config.clamp){throw new ReanimatedError('You need to set `clamp` property when using `rubberBandEffect`.');}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\decay\\\\decay.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"validateConfig_decayTs1\\\",\\\"config\\\",\\\"clamp\\\",\\\"Array\\\",\\\"isArray\\\",\\\"ReanimatedError\\\",\\\"length\\\",\\\"velocityFactor\\\",\\\"rubberBandEffect\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/decay/decay.ts\\\"],\\\"mappings\\\":\\\"AA0BA,SAAAA,uBAAmDA,CAAOC,MAAA,EAExD,GAAIA,MAAM,CAACC,KAAK,CAAE,CAChB,GAAI,CAACC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACC,KAAK,CAAC,CAAE,CAChC,KAAM,IAAI,CAAAG,eAAe,2CACqB,MAAO,CAAAJ,MAAM,CAACC,KAAK,IACjE,CAAC,CACH,CACA,GAAID,MAAM,CAACC,KAAK,CAACI,MAAM,GAAK,CAAC,CAAE,CAC7B,KAAM,IAAI,CAAAD,eAAe,oDAErBJ,MAAM,CAACC,KAAK,CAACI,MAAM,IAEvB,CAAC,CACH,CACF,CACA,GAAIL,MAAM,CAACM,cAAc,EAAI,CAAC,CAAE,CAC9B,KAAM,IAAI,CAAAF,eAAe,0DACoCJ,MAAM,CAACM,cAAc,IAClF,CAAC,CACH,CACA,GAAIN,MAAM,CAACO,gBAAgB,EAAI,CAACP,MAAM,CAACC,KAAK,CAAE,CAC5C,KAAM,IAAI,CAAAG,eAAe,CACvB,iEACF,CAAC,CACH,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var validateConfig = function () {\n    var _e = [new global.Error(), 1, -27];\n    var validateConfig = function (config) {\n      if (config.clamp) {\n        if (!Array.isArray(config.clamp)) {\n          throw new _errors.ReanimatedError(`\\`config.clamp\\` must be an array but is ${typeof config.clamp}.`);\n        }\n        if (config.clamp.length !== 2) {\n          throw new _errors.ReanimatedError(`\\`clamp array\\` must contain 2 items but is given ${config.clamp.length}.`);\n        }\n      }\n      if (config.velocityFactor <= 0) {\n        throw new _errors.ReanimatedError(`\\`config.velocityFactor\\` must be greater then 0 but is ${config.velocityFactor}.`);\n      }\n      if (config.rubberBandEffect && !config.clamp) {\n        throw new _errors.ReanimatedError('You need to set `clamp` property when using `rubberBandEffect`.');\n      }\n    };\n    validateConfig.__closure = {};\n    validateConfig.__workletHash = 9073199851290;\n    validateConfig.__initData = _worklet_9073199851290_init_data;\n    validateConfig.__stackDetails = _e;\n    return validateConfig;\n  }();\n  /**\n   * Lets you create animations that mimic objects in motion with friction.\n   *\n   * @param config - The decay animation configuration - {@link DecayConfig}.\n   * @param callback - A function called upon animation completion -\n   *   {@link AnimationCallback}.\n   * @returns An [animation\n   *   object](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animation-object)\n   *   which holds the current state of the animation.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withDecay\n   */\n  var _worklet_5171510341471_init_data = {\n    code: \"function decayTs2(userConfig,callback){const{defineAnimation,isValidRubberBandConfig,rubberBandDecay,rigidDecay,validateConfig,getReduceMotionForAnimation}=this.__closure;return defineAnimation(0,function(){'worklet';var _config$velocity;const config={deceleration:0.998,velocityFactor:1,velocity:0,rubberBandFactor:0.6};if(userConfig){Object.keys(userConfig).forEach(function(key){return config[key]=userConfig[key];});}const decay=isValidRubberBandConfig(config)?function(animation,now){return rubberBandDecay(animation,now,config);}:function(animation,now){return rigidDecay(animation,now,config);};function onStart(animation,value,now){const initialVelocity=config.velocity;animation.current=value;animation.lastTimestamp=now;animation.startTimestamp=now;animation.initialVelocity=initialVelocity;animation.velocity=initialVelocity;validateConfig(config);if(animation.reduceMotion&&config.clamp){if(value<config.clamp[0]){animation.current=config.clamp[0];}else if(value>config.clamp[1]){animation.current=config.clamp[1];}}}return{onFrame:decay,onStart:onStart,callback:callback,velocity:(_config$velocity=config.velocity)!==null&&_config$velocity!==void 0?_config$velocity:0,initialVelocity:0,current:undefined,lastTimestamp:0,startTimestamp:0,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};});}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\decay\\\\decay.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"decayTs2\\\",\\\"userConfig\\\",\\\"callback\\\",\\\"defineAnimation\\\",\\\"isValidRubberBandConfig\\\",\\\"rubberBandDecay\\\",\\\"rigidDecay\\\",\\\"validateConfig\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"_config$velocity\\\",\\\"config\\\",\\\"deceleration\\\",\\\"velocityFactor\\\",\\\"velocity\\\",\\\"rubberBandFactor\\\",\\\"Object\\\",\\\"keys\\\",\\\"forEach\\\",\\\"key\\\",\\\"decay\\\",\\\"animation\\\",\\\"now\\\",\\\"onStart\\\",\\\"value\\\",\\\"initialVelocity\\\",\\\"current\\\",\\\"lastTimestamp\\\",\\\"startTimestamp\\\",\\\"reduceMotion\\\",\\\"clamp\\\",\\\"onFrame\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/decay/decay.ts\\\"],\\\"mappings\\\":\\\"AAiEyB,SAAAA,QACvBA,CAAAC,UACA,CAAAC,QAC2B,QAAAC,eAAA,CAAAC,uBAAA,CAAAC,eAAA,CAAAC,UAAA,CAAAC,cAAA,CAAAC,2BAAA,OAAAC,SAAA,CAG3B,MAAO,CAAAN,eAAe,CAAiB,CAAC,CAAE,UAAM,CAC9C,SAAS,KAAAO,gBAAA,CACT,KAAM,CAAAC,MAA0B,CAAG,CACjCC,YAAY,CAAE,KAAK,CACnBC,cAAc,CAAE,CAAC,CACjBC,QAAQ,CAAE,CAAC,CACXC,gBAAgB,CAAE,GACpB,CAAC,CACD,GAAId,UAAU,CAAE,CACde,MAAM,CAACC,IAAI,CAAChB,UAAU,CAAC,CAACiB,OAAO,CAC5B,SAAAC,GAAG,QACA,CAAAR,MAAM,CAASQ,GAAG,CAAC,CAAGlB,UAAU,CAACkB,GAAG,CAC1C,GAAC,CACH,CAEA,KAAM,CAAAC,KAA+D,CACnEhB,uBAAuB,CAACO,MAAM,CAAC,CAC3B,SAACU,SAAS,CAAEC,GAAG,QAAK,CAAAjB,eAAe,CAACgB,SAAS,CAAEC,GAAG,CAAEX,MAAM,CAAC,GAC3D,SAACU,SAAS,CAAEC,GAAG,QAAK,CAAAhB,UAAU,CAACe,SAAS,CAAEC,GAAG,CAAEX,MAAM,CAAC,GAE5D,QAAS,CAAAY,OAAOA,CACdF,SAAyB,CACzBG,KAAa,CACbF,GAAc,CACR,CACN,KAAM,CAAAG,eAAe,CAAGd,MAAM,CAACG,QAAQ,CACvCO,SAAS,CAACK,OAAO,CAAGF,KAAK,CACzBH,SAAS,CAACM,aAAa,CAAGL,GAAG,CAC7BD,SAAS,CAACO,cAAc,CAAGN,GAAG,CAC9BD,SAAS,CAACI,eAAe,CAAGA,eAAe,CAC3CJ,SAAS,CAACP,QAAQ,CAAGW,eAAe,CAEpClB,cAAc,CAACI,MAAM,CAAC,CAEtB,GAAIU,SAAS,CAACQ,YAAY,EAAIlB,MAAM,CAACmB,KAAK,CAAE,CAC1C,GAAIN,KAAK,CAAGb,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAE,CAC3BT,SAAS,CAACK,OAAO,CAAGf,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CACrC,CAAC,IAAM,IAAIN,KAAK,CAAGb,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAE,CAClCT,SAAS,CAACK,OAAO,CAAGf,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CACrC,CACF,CACF,CAKA,MAAO,CACLC,OAAO,CAAEX,KAAK,CACdG,OAAO,CAAPA,OAAO,CACPrB,QAAQ,CAARA,QAAQ,CACRY,QAAQ,EAAAJ,gBAAA,CAAEC,MAAM,CAACG,QAAQ,UAAAJ,gBAAA,UAAAA,gBAAA,CAAI,CAAC,CAC9Be,eAAe,CAAE,CAAC,CAClBC,OAAO,CAAEM,SAAS,CAClBL,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAAC,CACjBC,YAAY,CAAErB,2BAA2B,CAACG,MAAM,CAACkB,YAAY,CAC/D,CAAC,CACH,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_16348628618416_init_data = {\n    code: \"function decayTs3(){const{userConfig,isValidRubberBandConfig,rubberBandDecay,rigidDecay,validateConfig,callback,getReduceMotionForAnimation}=this.__closure;var _config$velocity;const config={deceleration:0.998,velocityFactor:1,velocity:0,rubberBandFactor:0.6};if(userConfig){Object.keys(userConfig).forEach(function(key){return config[key]=userConfig[key];});}const decay=isValidRubberBandConfig(config)?function(animation,now){return rubberBandDecay(animation,now,config);}:function(animation,now){return rigidDecay(animation,now,config);};function onStart(animation,value,now){const initialVelocity=config.velocity;animation.current=value;animation.lastTimestamp=now;animation.startTimestamp=now;animation.initialVelocity=initialVelocity;animation.velocity=initialVelocity;validateConfig(config);if(animation.reduceMotion&&config.clamp){if(value<config.clamp[0]){animation.current=config.clamp[0];}else if(value>config.clamp[1]){animation.current=config.clamp[1];}}}return{onFrame:decay,onStart:onStart,callback:callback,velocity:(_config$velocity=config.velocity)!==null&&_config$velocity!==void 0?_config$velocity:0,initialVelocity:0,current:undefined,lastTimestamp:0,startTimestamp:0,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\decay\\\\decay.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"decayTs3\\\",\\\"userConfig\\\",\\\"isValidRubberBandConfig\\\",\\\"rubberBandDecay\\\",\\\"rigidDecay\\\",\\\"validateConfig\\\",\\\"callback\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"_config$velocity\\\",\\\"config\\\",\\\"deceleration\\\",\\\"velocityFactor\\\",\\\"velocity\\\",\\\"rubberBandFactor\\\",\\\"Object\\\",\\\"keys\\\",\\\"forEach\\\",\\\"key\\\",\\\"decay\\\",\\\"animation\\\",\\\"now\\\",\\\"onStart\\\",\\\"value\\\",\\\"initialVelocity\\\",\\\"current\\\",\\\"lastTimestamp\\\",\\\"startTimestamp\\\",\\\"reduceMotion\\\",\\\"clamp\\\",\\\"onFrame\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/decay/decay.ts\\\"],\\\"mappings\\\":\\\"AAuE4C,SAAAA,QAAMA,CAAA,QAAAC,UAAA,CAAAC,uBAAA,CAAAC,eAAA,CAAAC,UAAA,CAAAC,cAAA,CAAAC,QAAA,CAAAC,2BAAA,OAAAC,SAAA,KAAAC,gBAAA,CAE9C,KAAM,CAAAC,MAA0B,CAAG,CACjCC,YAAY,CAAE,KAAK,CACnBC,cAAc,CAAE,CAAC,CACjBC,QAAQ,CAAE,CAAC,CACXC,gBAAgB,CAAE,GACpB,CAAC,CACD,GAAIb,UAAU,CAAE,CACdc,MAAM,CAACC,IAAI,CAACf,UAAU,CAAC,CAACgB,OAAO,CAC5B,SAAAC,GAAG,QACA,CAAAR,MAAM,CAASQ,GAAG,CAAC,CAAGjB,UAAU,CAACiB,GAAG,CAC1C,GAAC,CACH,CAEA,KAAM,CAAAC,KAA+D,CACnEjB,uBAAuB,CAACQ,MAAM,CAAC,CAC3B,SAACU,SAAS,CAAEC,GAAG,QAAK,CAAAlB,eAAe,CAACiB,SAAS,CAAEC,GAAG,CAAEX,MAAM,CAAC,GAC3D,SAACU,SAAS,CAAEC,GAAG,QAAK,CAAAjB,UAAU,CAACgB,SAAS,CAAEC,GAAG,CAAEX,MAAM,CAAC,GAE5D,QAAS,CAAAY,OAAOA,CACdF,SAAyB,CACzBG,KAAa,CACbF,GAAc,CACR,CACN,KAAM,CAAAG,eAAe,CAAGd,MAAM,CAACG,QAAQ,CACvCO,SAAS,CAACK,OAAO,CAAGF,KAAK,CACzBH,SAAS,CAACM,aAAa,CAAGL,GAAG,CAC7BD,SAAS,CAACO,cAAc,CAAGN,GAAG,CAC9BD,SAAS,CAACI,eAAe,CAAGA,eAAe,CAC3CJ,SAAS,CAACP,QAAQ,CAAGW,eAAe,CAEpCnB,cAAc,CAACK,MAAM,CAAC,CAEtB,GAAIU,SAAS,CAACQ,YAAY,EAAIlB,MAAM,CAACmB,KAAK,CAAE,CAC1C,GAAIN,KAAK,CAAGb,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAE,CAC3BT,SAAS,CAACK,OAAO,CAAGf,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CACrC,CAAC,IAAM,IAAIN,KAAK,CAAGb,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAE,CAClCT,SAAS,CAACK,OAAO,CAAGf,MAAM,CAACmB,KAAK,CAAC,CAAC,CAAC,CACrC,CACF,CACF,CAKA,MAAO,CACLC,OAAO,CAAEX,KAAK,CACdG,OAAO,CAAPA,OAAO,CACPhB,QAAQ,CAARA,QAAQ,CACRO,QAAQ,EAAAJ,gBAAA,CAAEC,MAAM,CAACG,QAAQ,UAAAJ,gBAAA,UAAAA,gBAAA,CAAI,CAAC,CAC9Be,eAAe,CAAE,CAAC,CAClBC,OAAO,CAAEM,SAAS,CAClBL,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAAC,CACjBC,YAAY,CAAErB,2BAA2B,CAACG,MAAM,CAACkB,YAAY,CAC/D,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var withDecay = exports.withDecay = function () {\n    var _e = [new global.Error(), -7, -27];\n    var decayTs2 = function (userConfig, callback) {\n      return (0, _util.defineAnimation)(0, function () {\n        var _e = [new global.Error(), -8, -27];\n        var decayTs3 = function () {\n          var config = {\n            deceleration: 0.998,\n            velocityFactor: 1,\n            velocity: 0,\n            rubberBandFactor: 0.6\n          };\n          if (userConfig) {\n            Object.keys(userConfig).forEach(key => config[key] = userConfig[key]);\n          }\n          var decay = (0, _utils.isValidRubberBandConfig)(config) ? (animation, now) => (0, _rubberBandDecay.rubberBandDecay)(animation, now, config) : (animation, now) => (0, _rigidDecay.rigidDecay)(animation, now, config);\n          function onStart(animation, value, now) {\n            var initialVelocity = config.velocity;\n            animation.current = value;\n            animation.lastTimestamp = now;\n            animation.startTimestamp = now;\n            animation.initialVelocity = initialVelocity;\n            animation.velocity = initialVelocity;\n            validateConfig(config);\n            if (animation.reduceMotion && config.clamp) {\n              if (value < config.clamp[0]) {\n                animation.current = config.clamp[0];\n              } else if (value > config.clamp[1]) {\n                animation.current = config.clamp[1];\n              }\n            }\n          }\n\n          // To ensure the animation is correctly initialized and starts as expected\n          // we need to set its current value to undefined.\n          // Setting current to 0 breaks the animation.\n          return {\n            onFrame: decay,\n            onStart,\n            callback,\n            velocity: config.velocity ?? 0,\n            initialVelocity: 0,\n            current: undefined,\n            lastTimestamp: 0,\n            startTimestamp: 0,\n            reduceMotion: (0, _util.getReduceMotionForAnimation)(config.reduceMotion)\n          };\n        };\n        decayTs3.__closure = {\n          userConfig,\n          isValidRubberBandConfig: _utils.isValidRubberBandConfig,\n          rubberBandDecay: _rubberBandDecay.rubberBandDecay,\n          rigidDecay: _rigidDecay.rigidDecay,\n          validateConfig,\n          callback,\n          getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n        };\n        decayTs3.__workletHash = 16348628618416;\n        decayTs3.__initData = _worklet_16348628618416_init_data;\n        decayTs3.__stackDetails = _e;\n        return decayTs3;\n      }());\n    };\n    decayTs2.__closure = {\n      defineAnimation: _util.defineAnimation,\n      isValidRubberBandConfig: _utils.isValidRubberBandConfig,\n      rubberBandDecay: _rubberBandDecay.rubberBandDecay,\n      rigidDecay: _rigidDecay.rigidDecay,\n      validateConfig,\n      getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n    };\n    decayTs2.__workletHash = 5171510341471;\n    decayTs2.__initData = _worklet_5171510341471_init_data;\n    decayTs2.__stackDetails = _e;\n    return decayTs2;\n  }();\n});", "lineCount": 143, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON>"], [7, 19, 1, 13], [8, 2, 7, 0], [8, 6, 7, 0, "_errors"], [8, 13, 7, 0], [8, 16, 7, 0, "require"], [8, 23, 7, 0], [8, 24, 7, 0, "_dependencyMap"], [8, 38, 7, 0], [9, 2, 8, 0], [9, 6, 8, 0, "_util"], [9, 11, 8, 0], [9, 14, 8, 0, "require"], [9, 21, 8, 0], [9, 22, 8, 0, "_dependencyMap"], [9, 36, 8, 0], [10, 2, 9, 0], [10, 6, 9, 0, "_rigidDecay"], [10, 17, 9, 0], [10, 20, 9, 0, "require"], [10, 27, 9, 0], [10, 28, 9, 0, "_dependencyMap"], [10, 42, 9, 0], [11, 2, 10, 0], [11, 6, 10, 0, "_rubberBandDecay"], [11, 22, 10, 0], [11, 25, 10, 0, "require"], [11, 32, 10, 0], [11, 33, 10, 0, "_dependencyMap"], [11, 47, 10, 0], [12, 2, 17, 0], [12, 6, 17, 0, "_utils"], [12, 12, 17, 0], [12, 15, 17, 0, "require"], [12, 22, 17, 0], [12, 23, 17, 0, "_dependencyMap"], [12, 37, 17, 0], [13, 2, 21, 0], [14, 2, 21, 0], [14, 6, 21, 0, "_worklet_9073199851290_init_data"], [14, 38, 21, 0], [15, 4, 21, 0, "code"], [15, 8, 21, 0], [16, 4, 21, 0, "location"], [16, 12, 21, 0], [17, 4, 21, 0, "sourceMap"], [17, 13, 21, 0], [18, 4, 21, 0, "version"], [18, 11, 21, 0], [19, 2, 21, 0], [20, 2, 21, 0], [20, 6, 21, 0, "validateConfig"], [20, 20, 21, 0], [20, 23, 27, 0], [21, 4, 27, 0], [21, 8, 27, 0, "_e"], [21, 10, 27, 0], [21, 18, 27, 0, "global"], [21, 24, 27, 0], [21, 25, 27, 0, "Error"], [21, 30, 27, 0], [22, 4, 27, 0], [22, 8, 27, 0, "validateConfig"], [22, 22, 27, 0], [22, 34, 27, 0, "validateConfig"], [22, 35, 27, 24, "config"], [22, 41, 27, 50], [22, 43, 27, 58], [23, 6, 29, 2], [23, 10, 29, 6, "config"], [23, 16, 29, 12], [23, 17, 29, 13, "clamp"], [23, 22, 29, 18], [23, 24, 29, 20], [24, 8, 30, 4], [24, 12, 30, 8], [24, 13, 30, 9, "Array"], [24, 18, 30, 14], [24, 19, 30, 15, "isArray"], [24, 26, 30, 22], [24, 27, 30, 23, "config"], [24, 33, 30, 29], [24, 34, 30, 30, "clamp"], [24, 39, 30, 35], [24, 40, 30, 36], [24, 42, 30, 38], [25, 10, 31, 6], [25, 16, 31, 12], [25, 20, 31, 16, "ReanimatedError"], [25, 43, 31, 31], [25, 44, 32, 8], [25, 88, 32, 52], [25, 95, 32, 59, "config"], [25, 101, 32, 65], [25, 102, 32, 66, "clamp"], [25, 107, 32, 71], [25, 110, 33, 6], [25, 111, 33, 7], [26, 8, 34, 4], [27, 8, 35, 4], [27, 12, 35, 8, "config"], [27, 18, 35, 14], [27, 19, 35, 15, "clamp"], [27, 24, 35, 20], [27, 25, 35, 21, "length"], [27, 31, 35, 27], [27, 36, 35, 32], [27, 37, 35, 33], [27, 39, 35, 35], [28, 10, 36, 6], [28, 16, 36, 12], [28, 20, 36, 16, "ReanimatedError"], [28, 43, 36, 31], [28, 44, 37, 8], [28, 97, 38, 10, "config"], [28, 103, 38, 16], [28, 104, 38, 17, "clamp"], [28, 109, 38, 22], [28, 110, 38, 23, "length"], [28, 116, 38, 29], [28, 119, 40, 6], [28, 120, 40, 7], [29, 8, 41, 4], [30, 6, 42, 2], [31, 6, 43, 2], [31, 10, 43, 6, "config"], [31, 16, 43, 12], [31, 17, 43, 13, "velocityFactor"], [31, 31, 43, 27], [31, 35, 43, 31], [31, 36, 43, 32], [31, 38, 43, 34], [32, 8, 44, 4], [32, 14, 44, 10], [32, 18, 44, 14, "ReanimatedError"], [32, 41, 44, 29], [32, 42, 45, 6], [32, 101, 45, 65, "config"], [32, 107, 45, 71], [32, 108, 45, 72, "velocityFactor"], [32, 122, 45, 86], [32, 125, 46, 4], [32, 126, 46, 5], [33, 6, 47, 2], [34, 6, 48, 2], [34, 10, 48, 6, "config"], [34, 16, 48, 12], [34, 17, 48, 13, "rubberBandEffect"], [34, 33, 48, 29], [34, 37, 48, 33], [34, 38, 48, 34, "config"], [34, 44, 48, 40], [34, 45, 48, 41, "clamp"], [34, 50, 48, 46], [34, 52, 48, 48], [35, 8, 49, 4], [35, 14, 49, 10], [35, 18, 49, 14, "ReanimatedError"], [35, 41, 49, 29], [35, 42, 50, 6], [35, 107, 51, 4], [35, 108, 51, 5], [36, 6, 52, 2], [37, 4, 53, 0], [37, 5, 53, 1], [38, 4, 53, 1, "validateConfig"], [38, 18, 53, 1], [38, 19, 53, 1, "__closure"], [38, 28, 53, 1], [39, 4, 53, 1, "validateConfig"], [39, 18, 53, 1], [39, 19, 53, 1, "__workletHash"], [39, 32, 53, 1], [40, 4, 53, 1, "validateConfig"], [40, 18, 53, 1], [40, 19, 53, 1, "__initData"], [40, 29, 53, 1], [40, 32, 53, 1, "_worklet_9073199851290_init_data"], [40, 64, 53, 1], [41, 4, 53, 1, "validateConfig"], [41, 18, 53, 1], [41, 19, 53, 1, "__stackDetails"], [41, 33, 53, 1], [41, 36, 53, 1, "_e"], [41, 38, 53, 1], [42, 4, 53, 1], [42, 11, 53, 1, "validateConfig"], [42, 25, 53, 1], [43, 2, 53, 1], [43, 3, 27, 0], [44, 2, 55, 0], [45, 0, 56, 0], [46, 0, 57, 0], [47, 0, 58, 0], [48, 0, 59, 0], [49, 0, 60, 0], [50, 0, 61, 0], [51, 0, 62, 0], [52, 0, 63, 0], [53, 0, 64, 0], [54, 0, 65, 0], [55, 2, 55, 0], [55, 6, 55, 0, "_worklet_5171510341471_init_data"], [55, 38, 55, 0], [56, 4, 55, 0, "code"], [56, 8, 55, 0], [57, 4, 55, 0, "location"], [57, 12, 55, 0], [58, 4, 55, 0, "sourceMap"], [58, 13, 55, 0], [59, 4, 55, 0, "version"], [59, 11, 55, 0], [60, 2, 55, 0], [61, 2, 55, 0], [61, 6, 55, 0, "_worklet_16348628618416_init_data"], [61, 39, 55, 0], [62, 4, 55, 0, "code"], [62, 8, 55, 0], [63, 4, 55, 0, "location"], [63, 12, 55, 0], [64, 4, 55, 0, "sourceMap"], [64, 13, 55, 0], [65, 4, 55, 0, "version"], [65, 11, 55, 0], [66, 2, 55, 0], [67, 2, 66, 7], [67, 6, 66, 13, "<PERSON><PERSON><PERSON><PERSON>"], [67, 15, 66, 22], [67, 18, 66, 22, "exports"], [67, 25, 66, 22], [67, 26, 66, 22, "<PERSON><PERSON><PERSON><PERSON>"], [67, 35, 66, 22], [67, 38, 66, 25], [68, 4, 66, 25], [68, 8, 66, 25, "_e"], [68, 10, 66, 25], [68, 18, 66, 25, "global"], [68, 24, 66, 25], [68, 25, 66, 25, "Error"], [68, 30, 66, 25], [69, 4, 66, 25], [69, 8, 66, 25, "decayTs2"], [69, 16, 66, 25], [69, 28, 66, 25, "decayTs2"], [69, 29, 67, 2, "userConfig"], [69, 39, 67, 25], [69, 41, 68, 2, "callback"], [69, 49, 68, 30], [69, 51, 69, 29], [70, 6, 72, 2], [70, 13, 72, 9], [70, 17, 72, 9, "defineAnimation"], [70, 38, 72, 24], [70, 40, 72, 41], [70, 41, 72, 42], [70, 43, 72, 44], [71, 8, 72, 44], [71, 12, 72, 44, "_e"], [71, 14, 72, 44], [71, 22, 72, 44, "global"], [71, 28, 72, 44], [71, 29, 72, 44, "Error"], [71, 34, 72, 44], [72, 8, 72, 44], [72, 12, 72, 44, "decayTs3"], [72, 20, 72, 44], [72, 32, 72, 44, "decayTs3"], [72, 33, 72, 44], [72, 35, 72, 50], [73, 10, 74, 4], [73, 14, 74, 10, "config"], [73, 20, 74, 36], [73, 23, 74, 39], [74, 12, 75, 6, "deceleration"], [74, 24, 75, 18], [74, 26, 75, 20], [74, 31, 75, 25], [75, 12, 76, 6, "velocityFactor"], [75, 26, 76, 20], [75, 28, 76, 22], [75, 29, 76, 23], [76, 12, 77, 6, "velocity"], [76, 20, 77, 14], [76, 22, 77, 16], [76, 23, 77, 17], [77, 12, 78, 6, "rubberBandFactor"], [77, 28, 78, 22], [77, 30, 78, 24], [78, 10, 79, 4], [78, 11, 79, 5], [79, 10, 80, 4], [79, 14, 80, 8, "userConfig"], [79, 24, 80, 18], [79, 26, 80, 20], [80, 12, 81, 6, "Object"], [80, 18, 81, 12], [80, 19, 81, 13, "keys"], [80, 23, 81, 17], [80, 24, 81, 18, "userConfig"], [80, 34, 81, 28], [80, 35, 81, 29], [80, 36, 81, 30, "for<PERSON>ach"], [80, 43, 81, 37], [80, 44, 82, 9, "key"], [80, 47, 82, 12], [80, 51, 83, 12, "config"], [80, 57, 83, 18], [80, 58, 83, 27, "key"], [80, 61, 83, 30], [80, 62, 83, 31], [80, 65, 83, 34, "userConfig"], [80, 75, 83, 44], [80, 76, 83, 45, "key"], [80, 79, 83, 48], [80, 80, 84, 6], [80, 81, 84, 7], [81, 10, 85, 4], [82, 10, 87, 4], [82, 14, 87, 10, "decay"], [82, 19, 87, 73], [82, 22, 88, 6], [82, 26, 88, 6, "isValidRubberBandConfig"], [82, 56, 88, 29], [82, 58, 88, 30, "config"], [82, 64, 88, 36], [82, 65, 88, 37], [82, 68, 89, 10], [82, 69, 89, 11, "animation"], [82, 78, 89, 20], [82, 80, 89, 22, "now"], [82, 83, 89, 25], [82, 88, 89, 30], [82, 92, 89, 30, "rubberBandDecay"], [82, 124, 89, 45], [82, 126, 89, 46, "animation"], [82, 135, 89, 55], [82, 137, 89, 57, "now"], [82, 140, 89, 60], [82, 142, 89, 62, "config"], [82, 148, 89, 68], [82, 149, 89, 69], [82, 152, 90, 10], [82, 153, 90, 11, "animation"], [82, 162, 90, 20], [82, 164, 90, 22, "now"], [82, 167, 90, 25], [82, 172, 90, 30], [82, 176, 90, 30, "rigidDecay"], [82, 198, 90, 40], [82, 200, 90, 41, "animation"], [82, 209, 90, 50], [82, 211, 90, 52, "now"], [82, 214, 90, 55], [82, 216, 90, 57, "config"], [82, 222, 90, 63], [82, 223, 90, 64], [83, 10, 92, 4], [83, 19, 92, 13, "onStart"], [83, 26, 92, 20, "onStart"], [83, 27, 93, 6, "animation"], [83, 36, 93, 31], [83, 38, 94, 6, "value"], [83, 43, 94, 19], [83, 45, 95, 6, "now"], [83, 48, 95, 20], [83, 50, 96, 12], [84, 12, 97, 6], [84, 16, 97, 12, "initialVelocity"], [84, 31, 97, 27], [84, 34, 97, 30, "config"], [84, 40, 97, 36], [84, 41, 97, 37, "velocity"], [84, 49, 97, 45], [85, 12, 98, 6, "animation"], [85, 21, 98, 15], [85, 22, 98, 16, "current"], [85, 29, 98, 23], [85, 32, 98, 26, "value"], [85, 37, 98, 31], [86, 12, 99, 6, "animation"], [86, 21, 99, 15], [86, 22, 99, 16, "lastTimestamp"], [86, 35, 99, 29], [86, 38, 99, 32, "now"], [86, 41, 99, 35], [87, 12, 100, 6, "animation"], [87, 21, 100, 15], [87, 22, 100, 16, "startTimestamp"], [87, 36, 100, 30], [87, 39, 100, 33, "now"], [87, 42, 100, 36], [88, 12, 101, 6, "animation"], [88, 21, 101, 15], [88, 22, 101, 16, "initialVelocity"], [88, 37, 101, 31], [88, 40, 101, 34, "initialVelocity"], [88, 55, 101, 49], [89, 12, 102, 6, "animation"], [89, 21, 102, 15], [89, 22, 102, 16, "velocity"], [89, 30, 102, 24], [89, 33, 102, 27, "initialVelocity"], [89, 48, 102, 42], [90, 12, 104, 6, "validateConfig"], [90, 26, 104, 20], [90, 27, 104, 21, "config"], [90, 33, 104, 27], [90, 34, 104, 28], [91, 12, 106, 6], [91, 16, 106, 10, "animation"], [91, 25, 106, 19], [91, 26, 106, 20, "reduceMotion"], [91, 38, 106, 32], [91, 42, 106, 36, "config"], [91, 48, 106, 42], [91, 49, 106, 43, "clamp"], [91, 54, 106, 48], [91, 56, 106, 50], [92, 14, 107, 8], [92, 18, 107, 12, "value"], [92, 23, 107, 17], [92, 26, 107, 20, "config"], [92, 32, 107, 26], [92, 33, 107, 27, "clamp"], [92, 38, 107, 32], [92, 39, 107, 33], [92, 40, 107, 34], [92, 41, 107, 35], [92, 43, 107, 37], [93, 16, 108, 10, "animation"], [93, 25, 108, 19], [93, 26, 108, 20, "current"], [93, 33, 108, 27], [93, 36, 108, 30, "config"], [93, 42, 108, 36], [93, 43, 108, 37, "clamp"], [93, 48, 108, 42], [93, 49, 108, 43], [93, 50, 108, 44], [93, 51, 108, 45], [94, 14, 109, 8], [94, 15, 109, 9], [94, 21, 109, 15], [94, 25, 109, 19, "value"], [94, 30, 109, 24], [94, 33, 109, 27, "config"], [94, 39, 109, 33], [94, 40, 109, 34, "clamp"], [94, 45, 109, 39], [94, 46, 109, 40], [94, 47, 109, 41], [94, 48, 109, 42], [94, 50, 109, 44], [95, 16, 110, 10, "animation"], [95, 25, 110, 19], [95, 26, 110, 20, "current"], [95, 33, 110, 27], [95, 36, 110, 30, "config"], [95, 42, 110, 36], [95, 43, 110, 37, "clamp"], [95, 48, 110, 42], [95, 49, 110, 43], [95, 50, 110, 44], [95, 51, 110, 45], [96, 14, 111, 8], [97, 12, 112, 6], [98, 10, 113, 4], [100, 10, 115, 4], [101, 10, 116, 4], [102, 10, 117, 4], [103, 10, 118, 4], [103, 17, 118, 11], [104, 12, 119, 6, "onFrame"], [104, 19, 119, 13], [104, 21, 119, 15, "decay"], [104, 26, 119, 20], [105, 12, 120, 6, "onStart"], [105, 19, 120, 13], [106, 12, 121, 6, "callback"], [106, 20, 121, 14], [107, 12, 122, 6, "velocity"], [107, 20, 122, 14], [107, 22, 122, 16, "config"], [107, 28, 122, 22], [107, 29, 122, 23, "velocity"], [107, 37, 122, 31], [107, 41, 122, 35], [107, 42, 122, 36], [108, 12, 123, 6, "initialVelocity"], [108, 27, 123, 21], [108, 29, 123, 23], [108, 30, 123, 24], [109, 12, 124, 6, "current"], [109, 19, 124, 13], [109, 21, 124, 15, "undefined"], [109, 30, 124, 24], [110, 12, 125, 6, "lastTimestamp"], [110, 25, 125, 19], [110, 27, 125, 21], [110, 28, 125, 22], [111, 12, 126, 6, "startTimestamp"], [111, 26, 126, 20], [111, 28, 126, 22], [111, 29, 126, 23], [112, 12, 127, 6, "reduceMotion"], [112, 24, 127, 18], [112, 26, 127, 20], [112, 30, 127, 20, "getReduceMotionForAnimation"], [112, 63, 127, 47], [112, 65, 127, 48, "config"], [112, 71, 127, 54], [112, 72, 127, 55, "reduceMotion"], [112, 84, 127, 67], [113, 10, 128, 4], [113, 11, 128, 5], [114, 8, 129, 2], [114, 9, 129, 3], [115, 8, 129, 3, "decayTs3"], [115, 16, 129, 3], [115, 17, 129, 3, "__closure"], [115, 26, 129, 3], [116, 10, 129, 3, "userConfig"], [116, 20, 129, 3], [117, 10, 129, 3, "isValidRubberBandConfig"], [117, 33, 129, 3], [117, 35, 88, 6, "isValidRubberBandConfig"], [117, 65, 88, 29], [118, 10, 88, 29, "rubberBandDecay"], [118, 25, 88, 29], [118, 27, 89, 30, "rubberBandDecay"], [118, 59, 89, 45], [119, 10, 89, 45, "rigidDecay"], [119, 20, 89, 45], [119, 22, 90, 30, "rigidDecay"], [119, 44, 90, 40], [120, 10, 90, 40, "validateConfig"], [120, 24, 90, 40], [121, 10, 90, 40, "callback"], [121, 18, 90, 40], [122, 10, 90, 40, "getReduceMotionForAnimation"], [122, 37, 90, 40], [122, 39, 127, 20, "getReduceMotionForAnimation"], [123, 8, 127, 47], [124, 8, 127, 47, "decayTs3"], [124, 16, 127, 47], [124, 17, 127, 47, "__workletHash"], [124, 30, 127, 47], [125, 8, 127, 47, "decayTs3"], [125, 16, 127, 47], [125, 17, 127, 47, "__initData"], [125, 27, 127, 47], [125, 30, 127, 47, "_worklet_16348628618416_init_data"], [125, 63, 127, 47], [126, 8, 127, 47, "decayTs3"], [126, 16, 127, 47], [126, 17, 127, 47, "__stackDetails"], [126, 31, 127, 47], [126, 34, 127, 47, "_e"], [126, 36, 127, 47], [127, 8, 127, 47], [127, 15, 127, 47, "decayTs3"], [127, 23, 127, 47], [128, 6, 127, 47], [128, 7, 72, 44], [128, 9, 129, 3], [128, 10, 129, 4], [129, 4, 130, 0], [129, 5, 130, 1], [130, 4, 130, 1, "decayTs2"], [130, 12, 130, 1], [130, 13, 130, 1, "__closure"], [130, 22, 130, 1], [131, 6, 130, 1, "defineAnimation"], [131, 21, 130, 1], [131, 23, 72, 9, "defineAnimation"], [131, 44, 72, 24], [132, 6, 72, 24, "isValidRubberBandConfig"], [132, 29, 72, 24], [132, 31, 88, 6, "isValidRubberBandConfig"], [132, 61, 88, 29], [133, 6, 88, 29, "rubberBandDecay"], [133, 21, 88, 29], [133, 23, 89, 30, "rubberBandDecay"], [133, 55, 89, 45], [134, 6, 89, 45, "rigidDecay"], [134, 16, 89, 45], [134, 18, 90, 30, "rigidDecay"], [134, 40, 90, 40], [135, 6, 90, 40, "validateConfig"], [135, 20, 90, 40], [136, 6, 90, 40, "getReduceMotionForAnimation"], [136, 33, 90, 40], [136, 35, 127, 20, "getReduceMotionForAnimation"], [137, 4, 127, 47], [138, 4, 127, 47, "decayTs2"], [138, 12, 127, 47], [138, 13, 127, 47, "__workletHash"], [138, 26, 127, 47], [139, 4, 127, 47, "decayTs2"], [139, 12, 127, 47], [139, 13, 127, 47, "__initData"], [139, 23, 127, 47], [139, 26, 127, 47, "_worklet_5171510341471_init_data"], [139, 58, 127, 47], [140, 4, 127, 47, "decayTs2"], [140, 12, 127, 47], [140, 13, 127, 47, "__stackDetails"], [140, 27, 127, 47], [140, 30, 127, 47, "_e"], [140, 32, 127, 47], [141, 4, 127, 47], [141, 11, 127, 47, "decayTs2"], [141, 19, 127, 47], [142, 2, 127, 47], [142, 3, 66, 25], [142, 5, 130, 29], [143, 0, 130, 30], [143, 3]], "functionMap": {"names": ["<global>", "validateConfig", "<anonymous>", "defineAnimation$argument_1", "Object.keys.forEach$argument_0", "onStart"], "mappings": "AAA;AC0B;CD0B;yBEa;4CCM;QCU;6EDC;UDM,2DC;UDC,sDC;IEE;KFqB;GDgB;CFC"}}, "type": "js/module"}]}