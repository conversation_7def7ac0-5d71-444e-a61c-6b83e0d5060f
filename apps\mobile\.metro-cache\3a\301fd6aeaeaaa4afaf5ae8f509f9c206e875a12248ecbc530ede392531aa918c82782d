{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../../Animated/Animated", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 47}}], "key": "x27kYa85e82NRfmARgh3JzMveUI=", "exportNames": ["*"]}}, {"name": "../../Animated/Easing", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 43}}], "key": "Au2ArK5DNlFDwyS+rOHoXxFFzYs=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "./LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 42}}], "key": "M6ofQu070ZUTf+Oq+Zz+7FQEgjs=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}, {"name": "./LogBoxImages/alert-triangle.png", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 75, "column": 14}, "end": {"line": 75, "column": 58}}], "key": "IY3wCvfG8kIfmJ++7WU1ZZk9m2Q=", "exportNames": ["*"]}}, {"name": "./LogBoxImages/loader.png", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 79, "column": 14}, "end": {"line": 79, "column": 50}}], "key": "1xwDbnn6GlouktRSvDznQDg1Lqg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _Animated = _interopRequireDefault(require(_dependencyMap[2], \"../../Animated/Animated\"));\n  var _Easing = _interopRequireDefault(require(_dependencyMap[3], \"../../Animated/Easing\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[5], \"../../Text/Text\"));\n  var _LogBoxButton = _interopRequireDefault(require(_dependencyMap[6], \"./LogBoxButton\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[7], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[8], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[9], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxInspectorSourceMapStatus.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function LogBoxInspectorSourceMapStatus(props) {\n    var _React$useState = React.useState({\n        animation: null,\n        rotate: null\n      }),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      state = _React$useState2[0],\n      setState = _React$useState2[1];\n    React.useEffect(() => {\n      if (props.status === 'PENDING') {\n        if (state.animation == null) {\n          var animated = new _Animated.default.Value(0);\n          var animation = _Animated.default.loop(_Animated.default.timing(animated, {\n            duration: 2000,\n            easing: _Easing.default.linear,\n            toValue: 1,\n            useNativeDriver: true\n          }));\n          setState({\n            animation,\n            rotate: animated.interpolate({\n              inputRange: [0, 1],\n              outputRange: ['0deg', '360deg']\n            })\n          });\n          animation.start();\n        }\n      } else {\n        if (state.animation != null) {\n          state.animation.stop();\n          setState({\n            animation: null,\n            rotate: null\n          });\n        }\n      }\n      return () => {\n        if (state.animation != null) {\n          state.animation.stop();\n        }\n      };\n    }, [props.status, state.animation]);\n    var image;\n    var color;\n    switch (props.status) {\n      case 'FAILED':\n        image = require(_dependencyMap[10], \"./LogBoxImages/alert-triangle.png\");\n        color = LogBoxStyle.getErrorColor(1);\n        break;\n      case 'PENDING':\n        image = require(_dependencyMap[11], \"./LogBoxImages/loader.png\");\n        color = LogBoxStyle.getWarningColor(1);\n        break;\n    }\n    if (props.status === 'COMPLETE' || image == null) {\n      return null;\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxButton.default, {\n      backgroundColor: {\n        default: 'transparent',\n        pressed: LogBoxStyle.getBackgroundColor(1)\n      },\n      hitSlop: {\n        bottom: 8,\n        left: 8,\n        right: 8,\n        top: 8\n      },\n      onPress: props.onPress,\n      style: styles.root,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Animated.default.Image, {\n        source: image,\n        style: [styles.image, {\n          tintColor: color\n        }, state.rotate == null || props.status !== 'PENDING' ? null : {\n          transform: [{\n            rotate: state.rotate\n          }]\n        }]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n        style: [styles.text, {\n          color\n        }],\n        children: \"Source Map\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _StyleSheet.default.create({\n    root: {\n      alignItems: 'center',\n      borderRadius: 12,\n      flexDirection: 'row',\n      height: 24,\n      paddingHorizontal: 8\n    },\n    image: {\n      height: 14,\n      width: 16,\n      marginEnd: 4,\n      tintColor: LogBoxStyle.getTextColor(0.4)\n    },\n    text: {\n      fontSize: 12,\n      includeFontPadding: false,\n      lineHeight: 16\n    }\n  });\n  var _default = exports.default = LogBoxInspectorSourceMapStatus;\n});", "lineCount": 138, "map": [[8, 2, 13, 0], [8, 6, 13, 0, "_Animated"], [8, 15, 13, 0], [8, 18, 13, 0, "_interopRequireDefault"], [8, 40, 13, 0], [8, 41, 13, 0, "require"], [8, 48, 13, 0], [8, 49, 13, 0, "_dependencyMap"], [8, 63, 13, 0], [9, 2, 14, 0], [9, 6, 14, 0, "_Easing"], [9, 13, 14, 0], [9, 16, 14, 0, "_interopRequireDefault"], [9, 38, 14, 0], [9, 39, 14, 0, "require"], [9, 46, 14, 0], [9, 47, 14, 0, "_dependencyMap"], [9, 61, 14, 0], [10, 2, 15, 0], [10, 6, 15, 0, "_StyleSheet"], [10, 17, 15, 0], [10, 20, 15, 0, "_interopRequireDefault"], [10, 42, 15, 0], [10, 43, 15, 0, "require"], [10, 50, 15, 0], [10, 51, 15, 0, "_dependencyMap"], [10, 65, 15, 0], [11, 2, 16, 0], [11, 6, 16, 0, "_Text"], [11, 11, 16, 0], [11, 14, 16, 0, "_interopRequireDefault"], [11, 36, 16, 0], [11, 37, 16, 0, "require"], [11, 44, 16, 0], [11, 45, 16, 0, "_dependencyMap"], [11, 59, 16, 0], [12, 2, 17, 0], [12, 6, 17, 0, "_LogBoxButton"], [12, 19, 17, 0], [12, 22, 17, 0, "_interopRequireDefault"], [12, 44, 17, 0], [12, 45, 17, 0, "require"], [12, 52, 17, 0], [12, 53, 17, 0, "_dependencyMap"], [12, 67, 17, 0], [13, 2, 18, 0], [13, 6, 18, 0, "LogBoxStyle"], [13, 17, 18, 0], [13, 20, 18, 0, "_interopRequireWildcard"], [13, 43, 18, 0], [13, 44, 18, 0, "require"], [13, 51, 18, 0], [13, 52, 18, 0, "_dependencyMap"], [13, 66, 18, 0], [14, 2, 19, 0], [14, 6, 19, 0, "React"], [14, 11, 19, 0], [14, 14, 19, 0, "_interopRequireWildcard"], [14, 37, 19, 0], [14, 38, 19, 0, "require"], [14, 45, 19, 0], [14, 46, 19, 0, "_dependencyMap"], [14, 60, 19, 0], [15, 2, 19, 31], [15, 6, 19, 31, "_jsxDevRuntime"], [15, 20, 19, 31], [15, 23, 19, 31, "require"], [15, 30, 19, 31], [15, 31, 19, 31, "_dependencyMap"], [15, 45, 19, 31], [16, 2, 19, 31], [16, 6, 19, 31, "_jsxFileName"], [16, 18, 19, 31], [17, 2, 19, 31], [17, 11, 19, 31, "_interopRequireWildcard"], [17, 35, 19, 31, "e"], [17, 36, 19, 31], [17, 38, 19, 31, "t"], [17, 39, 19, 31], [17, 68, 19, 31, "WeakMap"], [17, 75, 19, 31], [17, 81, 19, 31, "r"], [17, 82, 19, 31], [17, 89, 19, 31, "WeakMap"], [17, 96, 19, 31], [17, 100, 19, 31, "n"], [17, 101, 19, 31], [17, 108, 19, 31, "WeakMap"], [17, 115, 19, 31], [17, 127, 19, 31, "_interopRequireWildcard"], [17, 150, 19, 31], [17, 162, 19, 31, "_interopRequireWildcard"], [17, 163, 19, 31, "e"], [17, 164, 19, 31], [17, 166, 19, 31, "t"], [17, 167, 19, 31], [17, 176, 19, 31, "t"], [17, 177, 19, 31], [17, 181, 19, 31, "e"], [17, 182, 19, 31], [17, 186, 19, 31, "e"], [17, 187, 19, 31], [17, 188, 19, 31, "__esModule"], [17, 198, 19, 31], [17, 207, 19, 31, "e"], [17, 208, 19, 31], [17, 214, 19, 31, "o"], [17, 215, 19, 31], [17, 217, 19, 31, "i"], [17, 218, 19, 31], [17, 220, 19, 31, "f"], [17, 221, 19, 31], [17, 226, 19, 31, "__proto__"], [17, 235, 19, 31], [17, 243, 19, 31, "default"], [17, 250, 19, 31], [17, 252, 19, 31, "e"], [17, 253, 19, 31], [17, 270, 19, 31, "e"], [17, 271, 19, 31], [17, 294, 19, 31, "e"], [17, 295, 19, 31], [17, 320, 19, 31, "e"], [17, 321, 19, 31], [17, 330, 19, 31, "f"], [17, 331, 19, 31], [17, 337, 19, 31, "o"], [17, 338, 19, 31], [17, 341, 19, 31, "t"], [17, 342, 19, 31], [17, 345, 19, 31, "n"], [17, 346, 19, 31], [17, 349, 19, 31, "r"], [17, 350, 19, 31], [17, 358, 19, 31, "o"], [17, 359, 19, 31], [17, 360, 19, 31, "has"], [17, 363, 19, 31], [17, 364, 19, 31, "e"], [17, 365, 19, 31], [17, 375, 19, 31, "o"], [17, 376, 19, 31], [17, 377, 19, 31, "get"], [17, 380, 19, 31], [17, 381, 19, 31, "e"], [17, 382, 19, 31], [17, 385, 19, 31, "o"], [17, 386, 19, 31], [17, 387, 19, 31, "set"], [17, 390, 19, 31], [17, 391, 19, 31, "e"], [17, 392, 19, 31], [17, 394, 19, 31, "f"], [17, 395, 19, 31], [17, 409, 19, 31, "_t"], [17, 411, 19, 31], [17, 415, 19, 31, "e"], [17, 416, 19, 31], [17, 432, 19, 31, "_t"], [17, 434, 19, 31], [17, 441, 19, 31, "hasOwnProperty"], [17, 455, 19, 31], [17, 456, 19, 31, "call"], [17, 460, 19, 31], [17, 461, 19, 31, "e"], [17, 462, 19, 31], [17, 464, 19, 31, "_t"], [17, 466, 19, 31], [17, 473, 19, 31, "i"], [17, 474, 19, 31], [17, 478, 19, 31, "o"], [17, 479, 19, 31], [17, 482, 19, 31, "Object"], [17, 488, 19, 31], [17, 489, 19, 31, "defineProperty"], [17, 503, 19, 31], [17, 508, 19, 31, "Object"], [17, 514, 19, 31], [17, 515, 19, 31, "getOwnPropertyDescriptor"], [17, 539, 19, 31], [17, 540, 19, 31, "e"], [17, 541, 19, 31], [17, 543, 19, 31, "_t"], [17, 545, 19, 31], [17, 552, 19, 31, "i"], [17, 553, 19, 31], [17, 554, 19, 31, "get"], [17, 557, 19, 31], [17, 561, 19, 31, "i"], [17, 562, 19, 31], [17, 563, 19, 31, "set"], [17, 566, 19, 31], [17, 570, 19, 31, "o"], [17, 571, 19, 31], [17, 572, 19, 31, "f"], [17, 573, 19, 31], [17, 575, 19, 31, "_t"], [17, 577, 19, 31], [17, 579, 19, 31, "i"], [17, 580, 19, 31], [17, 584, 19, 31, "f"], [17, 585, 19, 31], [17, 586, 19, 31, "_t"], [17, 588, 19, 31], [17, 592, 19, 31, "e"], [17, 593, 19, 31], [17, 594, 19, 31, "_t"], [17, 596, 19, 31], [17, 607, 19, 31, "f"], [17, 608, 19, 31], [17, 613, 19, 31, "e"], [17, 614, 19, 31], [17, 616, 19, 31, "t"], [17, 617, 19, 31], [18, 2, 26, 0], [18, 11, 26, 9, "LogBoxInspectorSourceMapStatus"], [18, 41, 26, 39, "LogBoxInspectorSourceMapStatus"], [18, 42, 26, 40, "props"], [18, 47, 26, 52], [18, 49, 26, 66], [19, 4, 27, 2], [19, 8, 27, 2, "_React$useState"], [19, 23, 27, 2], [19, 26, 27, 28, "React"], [19, 31, 27, 33], [19, 32, 27, 34, "useState"], [19, 40, 27, 42], [19, 41, 27, 43], [20, 8, 28, 4, "animation"], [20, 17, 28, 13], [20, 19, 28, 15], [20, 23, 28, 19], [21, 8, 29, 4, "rotate"], [21, 14, 29, 10], [21, 16, 29, 12], [22, 6, 30, 2], [22, 7, 30, 3], [22, 8, 30, 4], [23, 6, 30, 4, "_React$useState2"], [23, 22, 30, 4], [23, 29, 30, 4, "_slicedToArray2"], [23, 44, 30, 4], [23, 45, 30, 4, "default"], [23, 52, 30, 4], [23, 54, 30, 4, "_React$useState"], [23, 69, 30, 4], [24, 6, 27, 9, "state"], [24, 11, 27, 14], [24, 14, 27, 14, "_React$useState2"], [24, 30, 27, 14], [25, 6, 27, 16, "setState"], [25, 14, 27, 24], [25, 17, 27, 24, "_React$useState2"], [25, 33, 27, 24], [26, 4, 32, 2, "React"], [26, 9, 32, 7], [26, 10, 32, 8, "useEffect"], [26, 19, 32, 17], [26, 20, 32, 18], [26, 26, 32, 24], [27, 6, 33, 4], [27, 10, 33, 8, "props"], [27, 15, 33, 13], [27, 16, 33, 14, "status"], [27, 22, 33, 20], [27, 27, 33, 25], [27, 36, 33, 34], [27, 38, 33, 36], [28, 8, 34, 6], [28, 12, 34, 10, "state"], [28, 17, 34, 15], [28, 18, 34, 16, "animation"], [28, 27, 34, 25], [28, 31, 34, 29], [28, 35, 34, 33], [28, 37, 34, 35], [29, 10, 35, 8], [29, 14, 35, 14, "animated"], [29, 22, 35, 22], [29, 25, 35, 25], [29, 29, 35, 29, "Animated"], [29, 46, 35, 37], [29, 47, 35, 38, "Value"], [29, 52, 35, 43], [29, 53, 35, 44], [29, 54, 35, 45], [29, 55, 35, 46], [30, 10, 36, 8], [30, 14, 36, 14, "animation"], [30, 23, 36, 23], [30, 26, 36, 26, "Animated"], [30, 43, 36, 34], [30, 44, 36, 35, "loop"], [30, 48, 36, 39], [30, 49, 37, 10, "Animated"], [30, 66, 37, 18], [30, 67, 37, 19, "timing"], [30, 73, 37, 25], [30, 74, 37, 26, "animated"], [30, 82, 37, 34], [30, 84, 37, 36], [31, 12, 38, 12, "duration"], [31, 20, 38, 20], [31, 22, 38, 22], [31, 26, 38, 26], [32, 12, 39, 12, "easing"], [32, 18, 39, 18], [32, 20, 39, 20, "Easing"], [32, 35, 39, 26], [32, 36, 39, 27, "linear"], [32, 42, 39, 33], [33, 12, 40, 12, "toValue"], [33, 19, 40, 19], [33, 21, 40, 21], [33, 22, 40, 22], [34, 12, 41, 12, "useNativeDriver"], [34, 27, 41, 27], [34, 29, 41, 29], [35, 10, 42, 10], [35, 11, 42, 11], [35, 12, 43, 8], [35, 13, 43, 9], [36, 10, 45, 8, "setState"], [36, 18, 45, 16], [36, 19, 45, 17], [37, 12, 46, 10, "animation"], [37, 21, 46, 19], [38, 12, 47, 10, "rotate"], [38, 18, 47, 16], [38, 20, 47, 18, "animated"], [38, 28, 47, 26], [38, 29, 47, 27, "interpolate"], [38, 40, 47, 38], [38, 41, 47, 39], [39, 14, 48, 12, "inputRange"], [39, 24, 48, 22], [39, 26, 48, 24], [39, 27, 48, 25], [39, 28, 48, 26], [39, 30, 48, 28], [39, 31, 48, 29], [39, 32, 48, 30], [40, 14, 49, 12, "outputRange"], [40, 25, 49, 23], [40, 27, 49, 25], [40, 28, 49, 26], [40, 34, 49, 32], [40, 36, 49, 34], [40, 44, 49, 42], [41, 12, 50, 10], [41, 13, 50, 11], [42, 10, 51, 8], [42, 11, 51, 9], [42, 12, 51, 10], [43, 10, 52, 8, "animation"], [43, 19, 52, 17], [43, 20, 52, 18, "start"], [43, 25, 52, 23], [43, 26, 52, 24], [43, 27, 52, 25], [44, 8, 53, 6], [45, 6, 54, 4], [45, 7, 54, 5], [45, 13, 54, 11], [46, 8, 55, 6], [46, 12, 55, 10, "state"], [46, 17, 55, 15], [46, 18, 55, 16, "animation"], [46, 27, 55, 25], [46, 31, 55, 29], [46, 35, 55, 33], [46, 37, 55, 35], [47, 10, 56, 8, "state"], [47, 15, 56, 13], [47, 16, 56, 14, "animation"], [47, 25, 56, 23], [47, 26, 56, 24, "stop"], [47, 30, 56, 28], [47, 31, 56, 29], [47, 32, 56, 30], [48, 10, 57, 8, "setState"], [48, 18, 57, 16], [48, 19, 57, 17], [49, 12, 58, 10, "animation"], [49, 21, 58, 19], [49, 23, 58, 21], [49, 27, 58, 25], [50, 12, 59, 10, "rotate"], [50, 18, 59, 16], [50, 20, 59, 18], [51, 10, 60, 8], [51, 11, 60, 9], [51, 12, 60, 10], [52, 8, 61, 6], [53, 6, 62, 4], [54, 6, 64, 4], [54, 13, 64, 11], [54, 19, 64, 17], [55, 8, 65, 6], [55, 12, 65, 10, "state"], [55, 17, 65, 15], [55, 18, 65, 16, "animation"], [55, 27, 65, 25], [55, 31, 65, 29], [55, 35, 65, 33], [55, 37, 65, 35], [56, 10, 66, 8, "state"], [56, 15, 66, 13], [56, 16, 66, 14, "animation"], [56, 25, 66, 23], [56, 26, 66, 24, "stop"], [56, 30, 66, 28], [56, 31, 66, 29], [56, 32, 66, 30], [57, 8, 67, 6], [58, 6, 68, 4], [58, 7, 68, 5], [59, 4, 69, 2], [59, 5, 69, 3], [59, 7, 69, 5], [59, 8, 69, 6, "props"], [59, 13, 69, 11], [59, 14, 69, 12, "status"], [59, 20, 69, 18], [59, 22, 69, 20, "state"], [59, 27, 69, 25], [59, 28, 69, 26, "animation"], [59, 37, 69, 35], [59, 38, 69, 36], [59, 39, 69, 37], [60, 4, 71, 2], [60, 8, 71, 6, "image"], [60, 13, 71, 11], [61, 4, 72, 2], [61, 8, 72, 6, "color"], [61, 13, 72, 11], [62, 4, 73, 2], [62, 12, 73, 10, "props"], [62, 17, 73, 15], [62, 18, 73, 16, "status"], [62, 24, 73, 22], [63, 6, 74, 4], [63, 11, 74, 9], [63, 19, 74, 17], [64, 8, 75, 6, "image"], [64, 13, 75, 11], [64, 16, 75, 14, "require"], [64, 23, 75, 21], [64, 24, 75, 21, "_dependencyMap"], [64, 38, 75, 21], [64, 79, 75, 57], [64, 80, 75, 58], [65, 8, 76, 6, "color"], [65, 13, 76, 11], [65, 16, 76, 14, "LogBoxStyle"], [65, 27, 76, 25], [65, 28, 76, 26, "getErrorColor"], [65, 41, 76, 39], [65, 42, 76, 40], [65, 43, 76, 41], [65, 44, 76, 42], [66, 8, 77, 6], [67, 6, 78, 4], [67, 11, 78, 9], [67, 20, 78, 18], [68, 8, 79, 6, "image"], [68, 13, 79, 11], [68, 16, 79, 14, "require"], [68, 23, 79, 21], [68, 24, 79, 21, "_dependencyMap"], [68, 38, 79, 21], [68, 71, 79, 49], [68, 72, 79, 50], [69, 8, 80, 6, "color"], [69, 13, 80, 11], [69, 16, 80, 14, "LogBoxStyle"], [69, 27, 80, 25], [69, 28, 80, 26, "getWarningColor"], [69, 43, 80, 41], [69, 44, 80, 42], [69, 45, 80, 43], [69, 46, 80, 44], [70, 8, 81, 6], [71, 4, 82, 2], [72, 4, 84, 2], [72, 8, 84, 6, "props"], [72, 13, 84, 11], [72, 14, 84, 12, "status"], [72, 20, 84, 18], [72, 25, 84, 23], [72, 35, 84, 33], [72, 39, 84, 37, "image"], [72, 44, 84, 42], [72, 48, 84, 46], [72, 52, 84, 50], [72, 54, 84, 52], [73, 6, 85, 4], [73, 13, 85, 11], [73, 17, 85, 15], [74, 4, 86, 2], [75, 4, 88, 2], [75, 24, 89, 4], [75, 28, 89, 4, "_jsxDevRuntime"], [75, 42, 89, 4], [75, 43, 89, 4, "jsxDEV"], [75, 49, 89, 4], [75, 51, 89, 5, "_LogBoxButton"], [75, 64, 89, 5], [75, 65, 89, 5, "default"], [75, 72, 89, 17], [76, 6, 90, 6, "backgroundColor"], [76, 21, 90, 21], [76, 23, 90, 23], [77, 8, 91, 8, "default"], [77, 15, 91, 15], [77, 17, 91, 17], [77, 30, 91, 30], [78, 8, 92, 8, "pressed"], [78, 15, 92, 15], [78, 17, 92, 17, "LogBoxStyle"], [78, 28, 92, 28], [78, 29, 92, 29, "getBackgroundColor"], [78, 47, 92, 47], [78, 48, 92, 48], [78, 49, 92, 49], [79, 6, 93, 6], [79, 7, 93, 8], [80, 6, 94, 6, "hitSlop"], [80, 13, 94, 13], [80, 15, 94, 15], [81, 8, 94, 16, "bottom"], [81, 14, 94, 22], [81, 16, 94, 24], [81, 17, 94, 25], [82, 8, 94, 27, "left"], [82, 12, 94, 31], [82, 14, 94, 33], [82, 15, 94, 34], [83, 8, 94, 36, "right"], [83, 13, 94, 41], [83, 15, 94, 43], [83, 16, 94, 44], [84, 8, 94, 46, "top"], [84, 11, 94, 49], [84, 13, 94, 51], [85, 6, 94, 52], [85, 7, 94, 54], [86, 6, 95, 6, "onPress"], [86, 13, 95, 13], [86, 15, 95, 15, "props"], [86, 20, 95, 20], [86, 21, 95, 21, "onPress"], [86, 28, 95, 29], [87, 6, 96, 6, "style"], [87, 11, 96, 11], [87, 13, 96, 13, "styles"], [87, 19, 96, 19], [87, 20, 96, 20, "root"], [87, 24, 96, 25], [88, 6, 96, 25, "children"], [88, 14, 96, 25], [88, 30, 97, 6], [88, 34, 97, 6, "_jsxDevRuntime"], [88, 48, 97, 6], [88, 49, 97, 6, "jsxDEV"], [88, 55, 97, 6], [88, 57, 97, 7, "_Animated"], [88, 66, 97, 7], [88, 67, 97, 7, "default"], [88, 74, 97, 15], [88, 75, 97, 16, "Image"], [88, 80, 97, 21], [89, 8, 98, 8, "source"], [89, 14, 98, 14], [89, 16, 98, 16, "image"], [89, 21, 98, 22], [90, 8, 99, 8, "style"], [90, 13, 99, 13], [90, 15, 99, 15], [90, 16, 100, 10, "styles"], [90, 22, 100, 16], [90, 23, 100, 17, "image"], [90, 28, 100, 22], [90, 30, 101, 10], [91, 10, 101, 11, "tintColor"], [91, 19, 101, 20], [91, 21, 101, 22, "color"], [92, 8, 101, 27], [92, 9, 101, 28], [92, 11, 102, 10, "state"], [92, 16, 102, 15], [92, 17, 102, 16, "rotate"], [92, 23, 102, 22], [92, 27, 102, 26], [92, 31, 102, 30], [92, 35, 102, 34, "props"], [92, 40, 102, 39], [92, 41, 102, 40, "status"], [92, 47, 102, 46], [92, 52, 102, 51], [92, 61, 102, 60], [92, 64, 103, 14], [92, 68, 103, 18], [92, 71, 104, 14], [93, 10, 104, 15, "transform"], [93, 19, 104, 24], [93, 21, 104, 26], [93, 22, 104, 27], [94, 12, 104, 28, "rotate"], [94, 18, 104, 34], [94, 20, 104, 36, "state"], [94, 25, 104, 41], [94, 26, 104, 42, "rotate"], [95, 10, 104, 48], [95, 11, 104, 49], [96, 8, 104, 50], [96, 9, 104, 51], [97, 6, 105, 10], [98, 8, 105, 10, "fileName"], [98, 16, 105, 10], [98, 18, 105, 10, "_jsxFileName"], [98, 30, 105, 10], [99, 8, 105, 10, "lineNumber"], [99, 18, 105, 10], [100, 8, 105, 10, "columnNumber"], [100, 20, 105, 10], [101, 6, 105, 10], [101, 13, 106, 7], [101, 14, 106, 8], [101, 29, 107, 6], [101, 33, 107, 6, "_jsxDevRuntime"], [101, 47, 107, 6], [101, 48, 107, 6, "jsxDEV"], [101, 54, 107, 6], [101, 56, 107, 7, "_Text"], [101, 61, 107, 7], [101, 62, 107, 7, "default"], [101, 69, 107, 11], [102, 8, 107, 12, "style"], [102, 13, 107, 17], [102, 15, 107, 19], [102, 16, 107, 20, "styles"], [102, 22, 107, 26], [102, 23, 107, 27, "text"], [102, 27, 107, 31], [102, 29, 107, 33], [103, 10, 107, 34, "color"], [104, 8, 107, 39], [104, 9, 107, 40], [104, 10, 107, 42], [105, 8, 107, 42, "children"], [105, 16, 107, 42], [105, 18, 107, 43], [106, 6, 107, 53], [107, 8, 107, 53, "fileName"], [107, 16, 107, 53], [107, 18, 107, 53, "_jsxFileName"], [107, 30, 107, 53], [108, 8, 107, 53, "lineNumber"], [108, 18, 107, 53], [109, 8, 107, 53, "columnNumber"], [109, 20, 107, 53], [110, 6, 107, 53], [110, 13, 107, 59], [110, 14, 107, 60], [111, 4, 107, 60], [112, 6, 107, 60, "fileName"], [112, 14, 107, 60], [112, 16, 107, 60, "_jsxFileName"], [112, 28, 107, 60], [113, 6, 107, 60, "lineNumber"], [113, 16, 107, 60], [114, 6, 107, 60, "columnNumber"], [114, 18, 107, 60], [115, 4, 107, 60], [115, 11, 108, 18], [115, 12, 108, 19], [116, 2, 110, 0], [117, 2, 112, 0], [117, 6, 112, 6, "styles"], [117, 12, 112, 12], [117, 15, 112, 15, "StyleSheet"], [117, 34, 112, 25], [117, 35, 112, 26, "create"], [117, 41, 112, 32], [117, 42, 112, 33], [118, 4, 113, 2, "root"], [118, 8, 113, 6], [118, 10, 113, 8], [119, 6, 114, 4, "alignItems"], [119, 16, 114, 14], [119, 18, 114, 16], [119, 26, 114, 24], [120, 6, 115, 4, "borderRadius"], [120, 18, 115, 16], [120, 20, 115, 18], [120, 22, 115, 20], [121, 6, 116, 4, "flexDirection"], [121, 19, 116, 17], [121, 21, 116, 19], [121, 26, 116, 24], [122, 6, 117, 4, "height"], [122, 12, 117, 10], [122, 14, 117, 12], [122, 16, 117, 14], [123, 6, 118, 4, "paddingHorizontal"], [123, 23, 118, 21], [123, 25, 118, 23], [124, 4, 119, 2], [124, 5, 119, 3], [125, 4, 120, 2, "image"], [125, 9, 120, 7], [125, 11, 120, 9], [126, 6, 121, 4, "height"], [126, 12, 121, 10], [126, 14, 121, 12], [126, 16, 121, 14], [127, 6, 122, 4, "width"], [127, 11, 122, 9], [127, 13, 122, 11], [127, 15, 122, 13], [128, 6, 123, 4, "marginEnd"], [128, 15, 123, 13], [128, 17, 123, 15], [128, 18, 123, 16], [129, 6, 124, 4, "tintColor"], [129, 15, 124, 13], [129, 17, 124, 15, "LogBoxStyle"], [129, 28, 124, 26], [129, 29, 124, 27, "getTextColor"], [129, 41, 124, 39], [129, 42, 124, 40], [129, 45, 124, 43], [130, 4, 125, 2], [130, 5, 125, 3], [131, 4, 126, 2, "text"], [131, 8, 126, 6], [131, 10, 126, 8], [132, 6, 127, 4, "fontSize"], [132, 14, 127, 12], [132, 16, 127, 14], [132, 18, 127, 16], [133, 6, 128, 4, "includeFontPadding"], [133, 24, 128, 22], [133, 26, 128, 24], [133, 31, 128, 29], [134, 6, 129, 4, "lineHeight"], [134, 16, 129, 14], [134, 18, 129, 16], [135, 4, 130, 2], [136, 2, 131, 0], [136, 3, 131, 1], [136, 4, 131, 2], [137, 2, 131, 3], [137, 6, 131, 3, "_default"], [137, 14, 131, 3], [137, 17, 131, 3, "exports"], [137, 24, 131, 3], [137, 25, 131, 3, "default"], [137, 32, 131, 3], [137, 35, 133, 15, "LogBoxInspectorSourceMapStatus"], [137, 65, 133, 45], [138, 0, 133, 45], [138, 3]], "functionMap": {"names": ["<global>", "LogBoxInspectorSourceMapStatus", "React.useEffect$argument_0", "<anonymous>"], "mappings": "AAA;ACyB;kBCM;WCgC;KDI;GDC;CDyC"}}, "type": "js/module"}]}