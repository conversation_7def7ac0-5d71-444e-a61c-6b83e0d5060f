{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  function _iterableToArray(r) {\n    if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n  }\n  module.exports = _iterableToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 6, "map": [[2, 2, 1, 0], [2, 11, 1, 9, "_iterableToArray"], [2, 27, 1, 25, "_iterableToArray"], [2, 28, 1, 26, "r"], [2, 29, 1, 27], [2, 31, 1, 29], [3, 4, 2, 2], [3, 8, 2, 6], [3, 19, 2, 17], [3, 23, 2, 21], [3, 30, 2, 28, "Symbol"], [3, 36, 2, 34], [3, 40, 2, 38], [3, 44, 2, 42], [3, 48, 2, 46, "r"], [3, 49, 2, 47], [3, 50, 2, 48, "Symbol"], [3, 56, 2, 54], [3, 57, 2, 55, "iterator"], [3, 65, 2, 63], [3, 66, 2, 64], [3, 70, 2, 68], [3, 74, 2, 72], [3, 78, 2, 76, "r"], [3, 79, 2, 77], [3, 80, 2, 78], [3, 92, 2, 90], [3, 93, 2, 91], [3, 95, 2, 93], [3, 102, 2, 100, "Array"], [3, 107, 2, 105], [3, 108, 2, 106, "from"], [3, 112, 2, 110], [3, 113, 2, 111, "r"], [3, 114, 2, 112], [3, 115, 2, 113], [4, 2, 3, 0], [5, 2, 4, 0, "module"], [5, 8, 4, 6], [5, 9, 4, 7, "exports"], [5, 16, 4, 14], [5, 19, 4, 17, "_iterableToArray"], [5, 35, 4, 33], [5, 37, 4, 35, "module"], [5, 43, 4, 41], [5, 44, 4, 42, "exports"], [5, 51, 4, 49], [5, 52, 4, 50, "__esModule"], [5, 62, 4, 60], [5, 65, 4, 63], [5, 69, 4, 67], [5, 71, 4, 69, "module"], [5, 77, 4, 75], [5, 78, 4, 76, "exports"], [5, 85, 4, 83], [5, 86, 4, 84], [5, 95, 4, 93], [5, 96, 4, 94], [5, 99, 4, 97, "module"], [5, 105, 4, 103], [5, 106, 4, 104, "exports"], [5, 113, 4, 111], [6, 0, 4, 112], [6, 3]], "functionMap": {"names": ["_iterableToArray", "<global>"], "mappings": "AAA;CCE"}}, "type": "js/module"}]}