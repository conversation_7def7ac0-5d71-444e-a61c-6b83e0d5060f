{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 26, "index": 41}}], "key": "WMoKxUKO/GMHeED0pzSR/dc1v7c=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 42}, "end": {"line": 4, "column": 31, "index": 73}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 74}, "end": {"line": 5, "column": 88, "index": 162}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../../utils/CardAnimationContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 163}, "end": {"line": 6, "column": 75, "index": 238}}], "key": "DEr3baUhbVfGFH/Z5VCeDj5EdNY=", "exportNames": ["*"]}}, {"name": "../../utils/getDistanceForDirection.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 239}, "end": {"line": 7, "column": 81, "index": 320}}], "key": "Qhu6HWUnevuLnK0FD4YMv38jV6c=", "exportNames": ["*"]}}, {"name": "../../utils/getInvertedMultiplier.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 321}, "end": {"line": 8, "column": 77, "index": 398}}], "key": "AefbmjCMI5J6YDKFyzoTmAUKZyE=", "exportNames": ["*"]}}, {"name": "../../utils/getShadowStyle.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 399}, "end": {"line": 9, "column": 63, "index": 462}}], "key": "V3GwURkk8nF0DpzPyNaAf/zZheE=", "exportNames": ["*"]}}, {"name": "../../utils/memoize.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 463}, "end": {"line": 10, "column": 49, "index": 512}}], "key": "pCpzG6p+xU8Pk5ccgwshQRy1Mco=", "exportNames": ["*"]}}, {"name": "../GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 513}, "end": {"line": 11, "column": 68, "index": 581}}], "key": "bP+xiBbX7IFbUJ129q5EloqQNcU=", "exportNames": ["*"]}}, {"name": "./CardSheet.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 582}, "end": {"line": 12, "column": 43, "index": 625}}], "key": "H7g9bamV6F/kmhqdC7al4XaJ3jk=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 626}, "end": {"line": 13, "column": 63, "index": 689}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Card = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _color = _interopRequireDefault(require(_dependencyMap[7], \"color\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[8], \"react\"));\n  var _reactNative = require(_dependencyMap[9], \"react-native\");\n  var _CardAnimationContext = require(_dependencyMap[10], \"../../utils/CardAnimationContext.js\");\n  var _getDistanceForDirection = require(_dependencyMap[11], \"../../utils/getDistanceForDirection.js\");\n  var _getInvertedMultiplier = require(_dependencyMap[12], \"../../utils/getInvertedMultiplier.js\");\n  var _getShadowStyle = require(_dependencyMap[13], \"../../utils/getShadowStyle.js\");\n  var _memoize = require(_dependencyMap[14], \"../../utils/memoize.js\");\n  var _GestureHandler = require(_dependencyMap[15], \"../GestureHandler\");\n  var _CardSheet = require(_dependencyMap[16], \"./CardSheet.js\");\n  var _jsxRuntime = require(_dependencyMap[17], \"react/jsx-runtime\");\n  var _excluded = [\"styleInterpolator\", \"interpolationIndex\", \"current\", \"gesture\", \"next\", \"layout\", \"insets\", \"overlay\", \"overlayEnabled\", \"shadowEnabled\", \"gestureEnabled\", \"gestureDirection\", \"pageOverflowEnabled\", \"children\", \"containerStyle\", \"contentStyle\", \"closing\", \"direction\", \"gestureResponseDistance\", \"gestureVelocityImpact\", \"onClose\", \"onGestureBegin\", \"onGestureCanceled\", \"onGestureEnd\", \"onOpen\", \"onTransition\", \"transitionSpec\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var GESTURE_VELOCITY_IMPACT = 0.3;\n  var TRUE = 1;\n  var FALSE = 0;\n\n  /**\n   * The distance of touch start from the edge of the screen where the gesture will be recognized\n   */\n  var GESTURE_RESPONSE_DISTANCE_HORIZONTAL = 50;\n  var GESTURE_RESPONSE_DISTANCE_VERTICAL = 135;\n  var useNativeDriver = _reactNative.Platform.OS !== 'web';\n  var hasOpacityStyle = style => {\n    if (style) {\n      var flattenedStyle = _reactNative.StyleSheet.flatten(style);\n      return flattenedStyle.opacity != null;\n    }\n    return false;\n  };\n  var Card = exports.Card = /*#__PURE__*/function (_React$Component) {\n    function Card() {\n      var _this;\n      (0, _classCallCheck2.default)(this, Card);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, Card, [...args]);\n      _this.isCurrentlyMounted = false;\n      _this.isClosing = new _reactNative.Animated.Value(FALSE);\n      _this.inverted = new _reactNative.Animated.Value((0, _getInvertedMultiplier.getInvertedMultiplier)(_this.props.gestureDirection, _this.props.direction === 'rtl'));\n      _this.layout = {\n        width: new _reactNative.Animated.Value(_this.props.layout.width),\n        height: new _reactNative.Animated.Value(_this.props.layout.height)\n      };\n      _this.isSwiping = new _reactNative.Animated.Value(FALSE);\n      _this.animate = _ref => {\n        var closing = _ref.closing,\n          velocity = _ref.velocity;\n        var _this$props = _this.props,\n          transitionSpec = _this$props.transitionSpec,\n          onOpen = _this$props.onOpen,\n          onClose = _this$props.onClose,\n          onTransition = _this$props.onTransition,\n          gesture = _this$props.gesture;\n        var toValue = _this.getAnimateToValue({\n          ..._this.props,\n          closing\n        });\n        _this.lastToValue = toValue;\n        _this.isClosing.setValue(closing ? TRUE : FALSE);\n        var spec = closing ? transitionSpec.close : transitionSpec.open;\n        var animation = spec.animation === 'spring' ? _reactNative.Animated.spring : _reactNative.Animated.timing;\n        _this.setPointerEventsEnabled(!closing);\n        _this.handleStartInteraction();\n        clearTimeout(_this.pendingGestureCallback);\n        onTransition?.({\n          closing,\n          gesture: velocity !== undefined\n        });\n        animation(gesture, {\n          ...spec.config,\n          velocity,\n          toValue,\n          useNativeDriver,\n          isInteraction: false\n        }).start(_ref2 => {\n          var finished = _ref2.finished;\n          _this.handleEndInteraction();\n          clearTimeout(_this.pendingGestureCallback);\n          if (finished) {\n            if (closing) {\n              onClose();\n            } else {\n              onOpen();\n            }\n            if (_this.isCurrentlyMounted) {\n              // Make sure to re-open screen if it wasn't removed\n              _this.forceUpdate();\n            }\n          }\n        });\n      };\n      _this.getAnimateToValue = _ref3 => {\n        var closing = _ref3.closing,\n          layout = _ref3.layout,\n          gestureDirection = _ref3.gestureDirection,\n          direction = _ref3.direction,\n          preloaded = _ref3.preloaded;\n        if (!closing && !preloaded) {\n          return 0;\n        }\n        return (0, _getDistanceForDirection.getDistanceForDirection)(layout, gestureDirection, direction === 'rtl');\n      };\n      _this.setPointerEventsEnabled = enabled => {\n        var pointerEvents = enabled ? 'box-none' : 'none';\n        _this.ref.current?.setPointerEvents(pointerEvents);\n      };\n      _this.handleStartInteraction = () => {\n        if (_this.interactionHandle === undefined) {\n          _this.interactionHandle = _reactNative.InteractionManager.createInteractionHandle();\n        }\n      };\n      _this.handleEndInteraction = () => {\n        if (_this.interactionHandle !== undefined) {\n          _reactNative.InteractionManager.clearInteractionHandle(_this.interactionHandle);\n          _this.interactionHandle = undefined;\n        }\n      };\n      _this.handleGestureStateChange = _ref4 => {\n        var nativeEvent = _ref4.nativeEvent;\n        var _this$props2 = _this.props,\n          direction = _this$props2.direction,\n          layout = _this$props2.layout,\n          onClose = _this$props2.onClose,\n          onGestureBegin = _this$props2.onGestureBegin,\n          onGestureCanceled = _this$props2.onGestureCanceled,\n          onGestureEnd = _this$props2.onGestureEnd,\n          gestureDirection = _this$props2.gestureDirection,\n          gestureVelocityImpact = _this$props2.gestureVelocityImpact;\n        switch (nativeEvent.state) {\n          case _GestureHandler.GestureState.ACTIVE:\n            _this.isSwiping.setValue(TRUE);\n            _this.handleStartInteraction();\n            onGestureBegin?.();\n            break;\n          case _GestureHandler.GestureState.CANCELLED:\n          case _GestureHandler.GestureState.FAILED:\n            {\n              _this.isSwiping.setValue(FALSE);\n              _this.handleEndInteraction();\n              var velocity = gestureDirection === 'vertical' || gestureDirection === 'vertical-inverted' ? nativeEvent.velocityY : nativeEvent.velocityX;\n              _this.animate({\n                closing: _this.props.closing,\n                velocity\n              });\n              onGestureCanceled?.();\n              break;\n            }\n          case _GestureHandler.GestureState.END:\n            {\n              _this.isSwiping.setValue(FALSE);\n              var distance;\n              var translation;\n              var _velocity;\n              if (gestureDirection === 'vertical' || gestureDirection === 'vertical-inverted') {\n                distance = layout.height;\n                translation = nativeEvent.translationY;\n                _velocity = nativeEvent.velocityY;\n              } else {\n                distance = layout.width;\n                translation = nativeEvent.translationX;\n                _velocity = nativeEvent.velocityX;\n              }\n              var closing = (translation + _velocity * gestureVelocityImpact) * (0, _getInvertedMultiplier.getInvertedMultiplier)(gestureDirection, direction === 'rtl') > distance / 2 ? _velocity !== 0 || translation !== 0 : _this.props.closing;\n              _this.animate({\n                closing,\n                velocity: _velocity\n              });\n              if (closing) {\n                // We call onClose with a delay to make sure that the animation has already started\n                // This will make sure that the state update caused by this doesn't affect start of animation\n                _this.pendingGestureCallback = setTimeout(() => {\n                  onClose();\n\n                  // Trigger an update after we dispatch the action to remove the screen\n                  // This will make sure that we check if the screen didn't get removed so we can cancel the animation\n                  _this.forceUpdate();\n                }, 32);\n              }\n              onGestureEnd?.();\n              break;\n            }\n        }\n      };\n      // Memoize this to avoid extra work on re-render\n      _this.getInterpolatedStyle = (0, _memoize.memoize)((styleInterpolator, animation) => styleInterpolator(animation));\n      // Keep track of the animation context when deps changes.\n      _this.getCardAnimation = (0, _memoize.memoize)((interpolationIndex, current, next, layout, insetTop, insetRight, insetBottom, insetLeft) => ({\n        index: interpolationIndex,\n        current: {\n          progress: current\n        },\n        next: next && {\n          progress: next\n        },\n        closing: _this.isClosing,\n        swiping: _this.isSwiping,\n        inverted: _this.inverted,\n        layouts: {\n          screen: layout\n        },\n        insets: {\n          top: insetTop,\n          right: insetRight,\n          bottom: insetBottom,\n          left: insetLeft\n        }\n      }));\n      _this.ref = /*#__PURE__*/React.createRef();\n      return _this;\n    }\n    (0, _inherits2.default)(Card, _React$Component);\n    return (0, _createClass2.default)(Card, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        if (!this.props.preloaded) {\n          this.animate({\n            closing: this.props.closing\n          });\n        }\n        this.isCurrentlyMounted = true;\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate(prevProps) {\n        var _this$props3 = this.props,\n          gesture = _this$props3.gesture,\n          direction = _this$props3.direction,\n          layout = _this$props3.layout,\n          gestureDirection = _this$props3.gestureDirection,\n          opening = _this$props3.opening,\n          closing = _this$props3.closing;\n        var width = layout.width,\n          height = layout.height;\n        if (width !== prevProps.layout.width) {\n          this.layout.width.setValue(width);\n        }\n        if (height !== prevProps.layout.height) {\n          this.layout.height.setValue(height);\n        }\n        if (gestureDirection !== prevProps.gestureDirection) {\n          this.inverted.setValue((0, _getInvertedMultiplier.getInvertedMultiplier)(gestureDirection, direction === 'rtl'));\n        }\n        var toValue = this.getAnimateToValue(this.props);\n        if (this.getAnimateToValue(prevProps) !== toValue || this.lastToValue !== toValue) {\n          // We need to trigger the animation when route was closed\n          // The route might have been closed by a `POP` action or by a gesture\n          // When route was closed due to a gesture, the animation would've happened already\n          // It's still important to trigger the animation so that `onClose` is called\n          // If `onClose` is not called, cleanup step won't be performed for gestures\n          this.animate({\n            closing\n          });\n        } else if (opening && !prevProps.opening) {\n          // This can happen when screen somewhere below in the stack comes into focus via rearranging\n          // Also reset the animated value to make sure that the animation starts from the beginning\n          gesture.setValue((0, _getDistanceForDirection.getDistanceForDirection)(layout, gestureDirection, direction === 'rtl'));\n          this.animate({\n            closing\n          });\n        }\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this.props.gesture?.stopAnimation();\n        this.isCurrentlyMounted = false;\n        this.handleEndInteraction();\n      }\n    }, {\n      key: \"gestureActivationCriteria\",\n      value: function gestureActivationCriteria() {\n        var _this$props4 = this.props,\n          direction = _this$props4.direction,\n          layout = _this$props4.layout,\n          gestureDirection = _this$props4.gestureDirection,\n          gestureResponseDistance = _this$props4.gestureResponseDistance;\n        var enableTrackpadTwoFingerGesture = true;\n        var distance = gestureResponseDistance !== undefined ? gestureResponseDistance : gestureDirection === 'vertical' || gestureDirection === 'vertical-inverted' ? GESTURE_RESPONSE_DISTANCE_VERTICAL : GESTURE_RESPONSE_DISTANCE_HORIZONTAL;\n        if (gestureDirection === 'vertical') {\n          return {\n            maxDeltaX: 15,\n            minOffsetY: 5,\n            hitSlop: {\n              bottom: -layout.height + distance\n            },\n            enableTrackpadTwoFingerGesture\n          };\n        } else if (gestureDirection === 'vertical-inverted') {\n          return {\n            maxDeltaX: 15,\n            minOffsetY: -5,\n            hitSlop: {\n              top: -layout.height + distance\n            },\n            enableTrackpadTwoFingerGesture\n          };\n        } else {\n          var hitSlop = -layout.width + distance;\n          var invertedMultiplier = (0, _getInvertedMultiplier.getInvertedMultiplier)(gestureDirection, direction === 'rtl');\n          if (invertedMultiplier === 1) {\n            return {\n              minOffsetX: 5,\n              maxDeltaY: 20,\n              hitSlop: {\n                right: hitSlop\n              },\n              enableTrackpadTwoFingerGesture\n            };\n          } else {\n            return {\n              minOffsetX: -5,\n              maxDeltaY: 20,\n              hitSlop: {\n                left: hitSlop\n              },\n              enableTrackpadTwoFingerGesture\n            };\n          }\n        }\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this$props5 = this.props,\n          styleInterpolator = _this$props5.styleInterpolator,\n          interpolationIndex = _this$props5.interpolationIndex,\n          current = _this$props5.current,\n          gesture = _this$props5.gesture,\n          next = _this$props5.next,\n          layout = _this$props5.layout,\n          insets = _this$props5.insets,\n          overlay = _this$props5.overlay,\n          overlayEnabled = _this$props5.overlayEnabled,\n          shadowEnabled = _this$props5.shadowEnabled,\n          gestureEnabled = _this$props5.gestureEnabled,\n          gestureDirection = _this$props5.gestureDirection,\n          pageOverflowEnabled = _this$props5.pageOverflowEnabled,\n          children = _this$props5.children,\n          customContainerStyle = _this$props5.containerStyle,\n          contentStyle = _this$props5.contentStyle,\n          closing = _this$props5.closing,\n          direction = _this$props5.direction,\n          gestureResponseDistance = _this$props5.gestureResponseDistance,\n          gestureVelocityImpact = _this$props5.gestureVelocityImpact,\n          onClose = _this$props5.onClose,\n          onGestureBegin = _this$props5.onGestureBegin,\n          onGestureCanceled = _this$props5.onGestureCanceled,\n          onGestureEnd = _this$props5.onGestureEnd,\n          onOpen = _this$props5.onOpen,\n          onTransition = _this$props5.onTransition,\n          transitionSpec = _this$props5.transitionSpec,\n          rest = (0, _objectWithoutProperties2.default)(_this$props5, _excluded);\n        var interpolationProps = this.getCardAnimation(interpolationIndex, current, next, layout, insets.top, insets.right, insets.bottom, insets.left);\n        var interpolatedStyle = this.getInterpolatedStyle(styleInterpolator, interpolationProps);\n        var containerStyle = interpolatedStyle.containerStyle,\n          cardStyle = interpolatedStyle.cardStyle,\n          overlayStyle = interpolatedStyle.overlayStyle,\n          shadowStyle = interpolatedStyle.shadowStyle;\n        var handleGestureEvent = gestureEnabled ? _reactNative.Animated.event([{\n          nativeEvent: gestureDirection === 'vertical' || gestureDirection === 'vertical-inverted' ? {\n            translationY: gesture\n          } : {\n            translationX: gesture\n          }\n        }], {\n          useNativeDriver\n        }) : undefined;\n        var _StyleSheet$flatten = _reactNative.StyleSheet.flatten(contentStyle || {}),\n          backgroundColor = _StyleSheet$flatten.backgroundColor;\n        var isTransparent = typeof backgroundColor === 'string' ? (0, _color.default)(backgroundColor).alpha() === 0 : false;\n        return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_CardAnimationContext.CardAnimationContext.Provider, {\n          value: interpolationProps,\n          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {\n            style: {\n              // This is a dummy style that doesn't actually change anything visually.\n              // Animated needs the animated value to be used somewhere, otherwise things don't update properly.\n              // If we disable animations and hide header, it could end up making the value unused.\n              // So we have this dummy style that will always be used regardless of what else changed.\n              opacity: current\n            }\n            // Make sure that this view isn't removed. If this view is removed, our style with animated value won't apply\n            ,\n\n            collapsable: false\n          }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {\n            pointerEvents: \"box-none\"\n            // Make sure this view is not removed on the new architecture, as it causes focus loss during navigation on Android.\n            // This can happen when the view flattening results in different trees - due to `overflow` style changing in a parent.\n            ,\n\n            collapsable: false,\n            ...rest,\n            children: [overlayEnabled ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n              pointerEvents: \"box-none\",\n              style: _reactNative.StyleSheet.absoluteFill,\n              children: overlay({\n                style: overlayStyle\n              })\n            }) : null, /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {\n              style: [styles.container, containerStyle, customContainerStyle],\n              pointerEvents: \"box-none\",\n              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_GestureHandler.PanGestureHandler, {\n                enabled: layout.width !== 0 && gestureEnabled,\n                onGestureEvent: handleGestureEvent,\n                onHandlerStateChange: this.handleGestureStateChange,\n                ...this.gestureActivationCriteria(),\n                children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.Animated.View, {\n                  needsOffscreenAlphaCompositing: hasOpacityStyle(cardStyle),\n                  style: [styles.container, cardStyle],\n                  children: [shadowEnabled && shadowStyle && !isTransparent ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {\n                    style: [styles.shadow, gestureDirection === 'horizontal' ? [styles.shadowHorizontal, styles.shadowStart] : gestureDirection === 'horizontal-inverted' ? [styles.shadowHorizontal, styles.shadowEnd] : gestureDirection === 'vertical' ? [styles.shadowVertical, styles.shadowTop] : [styles.shadowVertical, styles.shadowBottom], {\n                      backgroundColor\n                    }, shadowStyle],\n                    pointerEvents: \"none\"\n                  }) : null, /*#__PURE__*/(0, _jsxRuntime.jsx)(_CardSheet.CardSheet, {\n                    ref: this.ref,\n                    enabled: pageOverflowEnabled,\n                    layout: layout,\n                    style: contentStyle,\n                    children: children\n                  })]\n                })\n              })\n            })]\n          })]\n        });\n      }\n    }]);\n  }(React.Component);\n  Card.defaultProps = {\n    shadowEnabled: false,\n    gestureEnabled: true,\n    gestureVelocityImpact: GESTURE_VELOCITY_IMPACT,\n    overlay: _ref5 => {\n      var style = _ref5.style;\n      return style ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {\n        pointerEvents: \"none\",\n        style: [styles.overlay, style]\n      }) : null;\n    }\n  };\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flex: 1\n    },\n    overlay: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    shadow: {\n      position: 'absolute'\n    },\n    shadowHorizontal: {\n      top: 0,\n      bottom: 0,\n      width: 3,\n      ...(0, _getShadowStyle.getShadowStyle)({\n        offset: {\n          width: -1,\n          height: 1\n        },\n        radius: 5,\n        opacity: 0.3\n      })\n    },\n    shadowStart: {\n      start: 0\n    },\n    shadowEnd: {\n      end: 0\n    },\n    shadowVertical: {\n      start: 0,\n      end: 0,\n      height: 3,\n      ...(0, _getShadowStyle.getShadowStyle)({\n        offset: {\n          width: 1,\n          height: -1\n        },\n        radius: 5,\n        opacity: 0.3\n      })\n    },\n    shadowTop: {\n      top: 0\n    },\n    shadowBottom: {\n      bottom: 0\n    }\n  });\n});", "lineCount": 510, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Card"], [8, 14, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_classCallCheck2"], [10, 22, 1, 13], [10, 25, 1, 13, "_interopRequireDefault"], [10, 47, 1, 13], [10, 48, 1, 13, "require"], [10, 55, 1, 13], [10, 56, 1, 13, "_dependencyMap"], [10, 70, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_createClass2"], [11, 19, 1, 13], [11, 22, 1, 13, "_interopRequireDefault"], [11, 44, 1, 13], [11, 45, 1, 13, "require"], [11, 52, 1, 13], [11, 53, 1, 13, "_dependencyMap"], [11, 67, 1, 13], [12, 2, 1, 13], [12, 6, 1, 13, "_possibleConstructorReturn2"], [12, 33, 1, 13], [12, 36, 1, 13, "_interopRequireDefault"], [12, 58, 1, 13], [12, 59, 1, 13, "require"], [12, 66, 1, 13], [12, 67, 1, 13, "_dependencyMap"], [12, 81, 1, 13], [13, 2, 1, 13], [13, 6, 1, 13, "_getPrototypeOf2"], [13, 22, 1, 13], [13, 25, 1, 13, "_interopRequireDefault"], [13, 47, 1, 13], [13, 48, 1, 13, "require"], [13, 55, 1, 13], [13, 56, 1, 13, "_dependencyMap"], [13, 70, 1, 13], [14, 2, 1, 13], [14, 6, 1, 13, "_inherits2"], [14, 16, 1, 13], [14, 19, 1, 13, "_interopRequireDefault"], [14, 41, 1, 13], [14, 42, 1, 13, "require"], [14, 49, 1, 13], [14, 50, 1, 13, "_dependencyMap"], [14, 64, 1, 13], [15, 2, 3, 0], [15, 6, 3, 0, "_color"], [15, 12, 3, 0], [15, 15, 3, 0, "_interopRequireDefault"], [15, 37, 3, 0], [15, 38, 3, 0, "require"], [15, 45, 3, 0], [15, 46, 3, 0, "_dependencyMap"], [15, 60, 3, 0], [16, 2, 4, 0], [16, 6, 4, 0, "React"], [16, 11, 4, 0], [16, 14, 4, 0, "_interopRequireWildcard"], [16, 37, 4, 0], [16, 38, 4, 0, "require"], [16, 45, 4, 0], [16, 46, 4, 0, "_dependencyMap"], [16, 60, 4, 0], [17, 2, 5, 0], [17, 6, 5, 0, "_reactNative"], [17, 18, 5, 0], [17, 21, 5, 0, "require"], [17, 28, 5, 0], [17, 29, 5, 0, "_dependencyMap"], [17, 43, 5, 0], [18, 2, 6, 0], [18, 6, 6, 0, "_CardAnimationContext"], [18, 27, 6, 0], [18, 30, 6, 0, "require"], [18, 37, 6, 0], [18, 38, 6, 0, "_dependencyMap"], [18, 52, 6, 0], [19, 2, 7, 0], [19, 6, 7, 0, "_getDistanceForDirection"], [19, 30, 7, 0], [19, 33, 7, 0, "require"], [19, 40, 7, 0], [19, 41, 7, 0, "_dependencyMap"], [19, 55, 7, 0], [20, 2, 8, 0], [20, 6, 8, 0, "_getInvertedMultiplier"], [20, 28, 8, 0], [20, 31, 8, 0, "require"], [20, 38, 8, 0], [20, 39, 8, 0, "_dependencyMap"], [20, 53, 8, 0], [21, 2, 9, 0], [21, 6, 9, 0, "_getShadowStyle"], [21, 21, 9, 0], [21, 24, 9, 0, "require"], [21, 31, 9, 0], [21, 32, 9, 0, "_dependencyMap"], [21, 46, 9, 0], [22, 2, 10, 0], [22, 6, 10, 0, "_memoize"], [22, 14, 10, 0], [22, 17, 10, 0, "require"], [22, 24, 10, 0], [22, 25, 10, 0, "_dependencyMap"], [22, 39, 10, 0], [23, 2, 11, 0], [23, 6, 11, 0, "_Gesture<PERSON><PERSON>ler"], [23, 21, 11, 0], [23, 24, 11, 0, "require"], [23, 31, 11, 0], [23, 32, 11, 0, "_dependencyMap"], [23, 46, 11, 0], [24, 2, 12, 0], [24, 6, 12, 0, "_CardSheet"], [24, 16, 12, 0], [24, 19, 12, 0, "require"], [24, 26, 12, 0], [24, 27, 12, 0, "_dependencyMap"], [24, 41, 12, 0], [25, 2, 13, 0], [25, 6, 13, 0, "_jsxRuntime"], [25, 17, 13, 0], [25, 20, 13, 0, "require"], [25, 27, 13, 0], [25, 28, 13, 0, "_dependencyMap"], [25, 42, 13, 0], [26, 2, 13, 63], [26, 6, 13, 63, "_excluded"], [26, 15, 13, 63], [27, 2, 13, 63], [27, 11, 13, 63, "_interopRequireWildcard"], [27, 35, 13, 63, "e"], [27, 36, 13, 63], [27, 38, 13, 63, "t"], [27, 39, 13, 63], [27, 68, 13, 63, "WeakMap"], [27, 75, 13, 63], [27, 81, 13, 63, "r"], [27, 82, 13, 63], [27, 89, 13, 63, "WeakMap"], [27, 96, 13, 63], [27, 100, 13, 63, "n"], [27, 101, 13, 63], [27, 108, 13, 63, "WeakMap"], [27, 115, 13, 63], [27, 127, 13, 63, "_interopRequireWildcard"], [27, 150, 13, 63], [27, 162, 13, 63, "_interopRequireWildcard"], [27, 163, 13, 63, "e"], [27, 164, 13, 63], [27, 166, 13, 63, "t"], [27, 167, 13, 63], [27, 176, 13, 63, "t"], [27, 177, 13, 63], [27, 181, 13, 63, "e"], [27, 182, 13, 63], [27, 186, 13, 63, "e"], [27, 187, 13, 63], [27, 188, 13, 63, "__esModule"], [27, 198, 13, 63], [27, 207, 13, 63, "e"], [27, 208, 13, 63], [27, 214, 13, 63, "o"], [27, 215, 13, 63], [27, 217, 13, 63, "i"], [27, 218, 13, 63], [27, 220, 13, 63, "f"], [27, 221, 13, 63], [27, 226, 13, 63, "__proto__"], [27, 235, 13, 63], [27, 243, 13, 63, "default"], [27, 250, 13, 63], [27, 252, 13, 63, "e"], [27, 253, 13, 63], [27, 270, 13, 63, "e"], [27, 271, 13, 63], [27, 294, 13, 63, "e"], [27, 295, 13, 63], [27, 320, 13, 63, "e"], [27, 321, 13, 63], [27, 330, 13, 63, "f"], [27, 331, 13, 63], [27, 337, 13, 63, "o"], [27, 338, 13, 63], [27, 341, 13, 63, "t"], [27, 342, 13, 63], [27, 345, 13, 63, "n"], [27, 346, 13, 63], [27, 349, 13, 63, "r"], [27, 350, 13, 63], [27, 358, 13, 63, "o"], [27, 359, 13, 63], [27, 360, 13, 63, "has"], [27, 363, 13, 63], [27, 364, 13, 63, "e"], [27, 365, 13, 63], [27, 375, 13, 63, "o"], [27, 376, 13, 63], [27, 377, 13, 63, "get"], [27, 380, 13, 63], [27, 381, 13, 63, "e"], [27, 382, 13, 63], [27, 385, 13, 63, "o"], [27, 386, 13, 63], [27, 387, 13, 63, "set"], [27, 390, 13, 63], [27, 391, 13, 63, "e"], [27, 392, 13, 63], [27, 394, 13, 63, "f"], [27, 395, 13, 63], [27, 409, 13, 63, "_t"], [27, 411, 13, 63], [27, 415, 13, 63, "e"], [27, 416, 13, 63], [27, 432, 13, 63, "_t"], [27, 434, 13, 63], [27, 441, 13, 63, "hasOwnProperty"], [27, 455, 13, 63], [27, 456, 13, 63, "call"], [27, 460, 13, 63], [27, 461, 13, 63, "e"], [27, 462, 13, 63], [27, 464, 13, 63, "_t"], [27, 466, 13, 63], [27, 473, 13, 63, "i"], [27, 474, 13, 63], [27, 478, 13, 63, "o"], [27, 479, 13, 63], [27, 482, 13, 63, "Object"], [27, 488, 13, 63], [27, 489, 13, 63, "defineProperty"], [27, 503, 13, 63], [27, 508, 13, 63, "Object"], [27, 514, 13, 63], [27, 515, 13, 63, "getOwnPropertyDescriptor"], [27, 539, 13, 63], [27, 540, 13, 63, "e"], [27, 541, 13, 63], [27, 543, 13, 63, "_t"], [27, 545, 13, 63], [27, 552, 13, 63, "i"], [27, 553, 13, 63], [27, 554, 13, 63, "get"], [27, 557, 13, 63], [27, 561, 13, 63, "i"], [27, 562, 13, 63], [27, 563, 13, 63, "set"], [27, 566, 13, 63], [27, 570, 13, 63, "o"], [27, 571, 13, 63], [27, 572, 13, 63, "f"], [27, 573, 13, 63], [27, 575, 13, 63, "_t"], [27, 577, 13, 63], [27, 579, 13, 63, "i"], [27, 580, 13, 63], [27, 584, 13, 63, "f"], [27, 585, 13, 63], [27, 586, 13, 63, "_t"], [27, 588, 13, 63], [27, 592, 13, 63, "e"], [27, 593, 13, 63], [27, 594, 13, 63, "_t"], [27, 596, 13, 63], [27, 607, 13, 63, "f"], [27, 608, 13, 63], [27, 613, 13, 63, "e"], [27, 614, 13, 63], [27, 616, 13, 63, "t"], [27, 617, 13, 63], [28, 2, 13, 63], [28, 11, 13, 63, "_callSuper"], [28, 22, 13, 63, "t"], [28, 23, 13, 63], [28, 25, 13, 63, "o"], [28, 26, 13, 63], [28, 28, 13, 63, "e"], [28, 29, 13, 63], [28, 40, 13, 63, "o"], [28, 41, 13, 63], [28, 48, 13, 63, "_getPrototypeOf2"], [28, 64, 13, 63], [28, 65, 13, 63, "default"], [28, 72, 13, 63], [28, 74, 13, 63, "o"], [28, 75, 13, 63], [28, 82, 13, 63, "_possibleConstructorReturn2"], [28, 109, 13, 63], [28, 110, 13, 63, "default"], [28, 117, 13, 63], [28, 119, 13, 63, "t"], [28, 120, 13, 63], [28, 122, 13, 63, "_isNativeReflectConstruct"], [28, 147, 13, 63], [28, 152, 13, 63, "Reflect"], [28, 159, 13, 63], [28, 160, 13, 63, "construct"], [28, 169, 13, 63], [28, 170, 13, 63, "o"], [28, 171, 13, 63], [28, 173, 13, 63, "e"], [28, 174, 13, 63], [28, 186, 13, 63, "_getPrototypeOf2"], [28, 202, 13, 63], [28, 203, 13, 63, "default"], [28, 210, 13, 63], [28, 212, 13, 63, "t"], [28, 213, 13, 63], [28, 215, 13, 63, "constructor"], [28, 226, 13, 63], [28, 230, 13, 63, "o"], [28, 231, 13, 63], [28, 232, 13, 63, "apply"], [28, 237, 13, 63], [28, 238, 13, 63, "t"], [28, 239, 13, 63], [28, 241, 13, 63, "e"], [28, 242, 13, 63], [29, 2, 13, 63], [29, 11, 13, 63, "_isNativeReflectConstruct"], [29, 37, 13, 63], [29, 51, 13, 63, "t"], [29, 52, 13, 63], [29, 56, 13, 63, "Boolean"], [29, 63, 13, 63], [29, 64, 13, 63, "prototype"], [29, 73, 13, 63], [29, 74, 13, 63, "valueOf"], [29, 81, 13, 63], [29, 82, 13, 63, "call"], [29, 86, 13, 63], [29, 87, 13, 63, "Reflect"], [29, 94, 13, 63], [29, 95, 13, 63, "construct"], [29, 104, 13, 63], [29, 105, 13, 63, "Boolean"], [29, 112, 13, 63], [29, 145, 13, 63, "t"], [29, 146, 13, 63], [29, 159, 13, 63, "_isNativeReflectConstruct"], [29, 184, 13, 63], [29, 196, 13, 63, "_isNativeReflectConstruct"], [29, 197, 13, 63], [29, 210, 13, 63, "t"], [29, 211, 13, 63], [30, 2, 14, 0], [30, 6, 14, 6, "GESTURE_VELOCITY_IMPACT"], [30, 29, 14, 29], [30, 32, 14, 32], [30, 35, 14, 35], [31, 2, 15, 0], [31, 6, 15, 6, "TRUE"], [31, 10, 15, 10], [31, 13, 15, 13], [31, 14, 15, 14], [32, 2, 16, 0], [32, 6, 16, 6, "FALSE"], [32, 11, 16, 11], [32, 14, 16, 14], [32, 15, 16, 15], [34, 2, 18, 0], [35, 0, 19, 0], [36, 0, 20, 0], [37, 2, 21, 0], [37, 6, 21, 6, "GESTURE_RESPONSE_DISTANCE_HORIZONTAL"], [37, 42, 21, 42], [37, 45, 21, 45], [37, 47, 21, 47], [38, 2, 22, 0], [38, 6, 22, 6, "GESTURE_RESPONSE_DISTANCE_VERTICAL"], [38, 40, 22, 40], [38, 43, 22, 43], [38, 46, 22, 46], [39, 2, 23, 0], [39, 6, 23, 6, "useNativeDriver"], [39, 21, 23, 21], [39, 24, 23, 24, "Platform"], [39, 45, 23, 32], [39, 46, 23, 33, "OS"], [39, 48, 23, 35], [39, 53, 23, 40], [39, 58, 23, 45], [40, 2, 24, 0], [40, 6, 24, 6, "hasOpacityStyle"], [40, 21, 24, 21], [40, 24, 24, 24, "style"], [40, 29, 24, 29], [40, 33, 24, 33], [41, 4, 25, 2], [41, 8, 25, 6, "style"], [41, 13, 25, 11], [41, 15, 25, 13], [42, 6, 26, 4], [42, 10, 26, 10, "flattenedStyle"], [42, 24, 26, 24], [42, 27, 26, 27, "StyleSheet"], [42, 50, 26, 37], [42, 51, 26, 38, "flatten"], [42, 58, 26, 45], [42, 59, 26, 46, "style"], [42, 64, 26, 51], [42, 65, 26, 52], [43, 6, 27, 4], [43, 13, 27, 11, "flattenedStyle"], [43, 27, 27, 25], [43, 28, 27, 26, "opacity"], [43, 35, 27, 33], [43, 39, 27, 37], [43, 43, 27, 41], [44, 4, 28, 2], [45, 4, 29, 2], [45, 11, 29, 9], [45, 16, 29, 14], [46, 2, 30, 0], [46, 3, 30, 1], [47, 2, 30, 2], [47, 6, 31, 13, "Card"], [47, 10, 31, 17], [47, 13, 31, 17, "exports"], [47, 20, 31, 17], [47, 21, 31, 17, "Card"], [47, 25, 31, 17], [47, 51, 31, 17, "_React$Component"], [47, 67, 31, 17], [48, 4, 31, 17], [48, 13, 31, 17, "Card"], [48, 18, 31, 17], [49, 6, 31, 17], [49, 10, 31, 17, "_this"], [49, 15, 31, 17], [50, 6, 31, 17], [50, 10, 31, 17, "_classCallCheck2"], [50, 26, 31, 17], [50, 27, 31, 17, "default"], [50, 34, 31, 17], [50, 42, 31, 17, "Card"], [50, 46, 31, 17], [51, 6, 31, 17], [51, 15, 31, 17, "_len"], [51, 19, 31, 17], [51, 22, 31, 17, "arguments"], [51, 31, 31, 17], [51, 32, 31, 17, "length"], [51, 38, 31, 17], [51, 40, 31, 17, "args"], [51, 44, 31, 17], [51, 51, 31, 17, "Array"], [51, 56, 31, 17], [51, 57, 31, 17, "_len"], [51, 61, 31, 17], [51, 64, 31, 17, "_key"], [51, 68, 31, 17], [51, 74, 31, 17, "_key"], [51, 78, 31, 17], [51, 81, 31, 17, "_len"], [51, 85, 31, 17], [51, 87, 31, 17, "_key"], [51, 91, 31, 17], [52, 8, 31, 17, "args"], [52, 12, 31, 17], [52, 13, 31, 17, "_key"], [52, 17, 31, 17], [52, 21, 31, 17, "arguments"], [52, 30, 31, 17], [52, 31, 31, 17, "_key"], [52, 35, 31, 17], [53, 6, 31, 17], [54, 6, 31, 17, "_this"], [54, 11, 31, 17], [54, 14, 31, 17, "_callSuper"], [54, 24, 31, 17], [54, 31, 31, 17, "Card"], [54, 35, 31, 17], [54, 41, 31, 17, "args"], [54, 45, 31, 17], [55, 6, 31, 17, "_this"], [55, 11, 31, 17], [55, 12, 97, 2, "isCurrentlyMounted"], [55, 30, 97, 20], [55, 33, 97, 23], [55, 38, 97, 28], [56, 6, 97, 28, "_this"], [56, 11, 97, 28], [56, 12, 98, 2, "isClosing"], [56, 21, 98, 11], [56, 24, 98, 14], [56, 28, 98, 18, "Animated"], [56, 49, 98, 26], [56, 50, 98, 27, "Value"], [56, 55, 98, 32], [56, 56, 98, 33, "FALSE"], [56, 61, 98, 38], [56, 62, 98, 39], [57, 6, 98, 39, "_this"], [57, 11, 98, 39], [57, 12, 99, 2, "inverted"], [57, 20, 99, 10], [57, 23, 99, 13], [57, 27, 99, 17, "Animated"], [57, 48, 99, 25], [57, 49, 99, 26, "Value"], [57, 54, 99, 31], [57, 55, 99, 32], [57, 59, 99, 32, "getInvertedMultiplier"], [57, 103, 99, 53], [57, 105, 99, 54, "_this"], [57, 110, 99, 54], [57, 111, 99, 59, "props"], [57, 116, 99, 64], [57, 117, 99, 65, "gestureDirection"], [57, 133, 99, 81], [57, 135, 99, 83, "_this"], [57, 140, 99, 83], [57, 141, 99, 88, "props"], [57, 146, 99, 93], [57, 147, 99, 94, "direction"], [57, 156, 99, 103], [57, 161, 99, 108], [57, 166, 99, 113], [57, 167, 99, 114], [57, 168, 99, 115], [58, 6, 99, 115, "_this"], [58, 11, 99, 115], [58, 12, 100, 2, "layout"], [58, 18, 100, 8], [58, 21, 100, 11], [59, 8, 101, 4, "width"], [59, 13, 101, 9], [59, 15, 101, 11], [59, 19, 101, 15, "Animated"], [59, 40, 101, 23], [59, 41, 101, 24, "Value"], [59, 46, 101, 29], [59, 47, 101, 30, "_this"], [59, 52, 101, 30], [59, 53, 101, 35, "props"], [59, 58, 101, 40], [59, 59, 101, 41, "layout"], [59, 65, 101, 47], [59, 66, 101, 48, "width"], [59, 71, 101, 53], [59, 72, 101, 54], [60, 8, 102, 4, "height"], [60, 14, 102, 10], [60, 16, 102, 12], [60, 20, 102, 16, "Animated"], [60, 41, 102, 24], [60, 42, 102, 25, "Value"], [60, 47, 102, 30], [60, 48, 102, 31, "_this"], [60, 53, 102, 31], [60, 54, 102, 36, "props"], [60, 59, 102, 41], [60, 60, 102, 42, "layout"], [60, 66, 102, 48], [60, 67, 102, 49, "height"], [60, 73, 102, 55], [61, 6, 103, 2], [61, 7, 103, 3], [62, 6, 103, 3, "_this"], [62, 11, 103, 3], [62, 12, 104, 2, "isSwiping"], [62, 21, 104, 11], [62, 24, 104, 14], [62, 28, 104, 18, "Animated"], [62, 49, 104, 26], [62, 50, 104, 27, "Value"], [62, 55, 104, 32], [62, 56, 104, 33, "FALSE"], [62, 61, 104, 38], [62, 62, 104, 39], [63, 6, 104, 39, "_this"], [63, 11, 104, 39], [63, 12, 105, 2, "animate"], [63, 19, 105, 9], [63, 22, 105, 12, "_ref"], [63, 26, 105, 12], [63, 30, 108, 8], [64, 8, 108, 8], [64, 12, 106, 4, "closing"], [64, 19, 106, 11], [64, 22, 106, 11, "_ref"], [64, 26, 106, 11], [64, 27, 106, 4, "closing"], [64, 34, 106, 11], [65, 10, 107, 4, "velocity"], [65, 18, 107, 12], [65, 21, 107, 12, "_ref"], [65, 25, 107, 12], [65, 26, 107, 4, "velocity"], [65, 34, 107, 12], [66, 8, 109, 4], [66, 12, 109, 4, "_this$props"], [66, 23, 109, 4], [66, 26, 115, 8, "_this"], [66, 31, 115, 8], [66, 32, 115, 13, "props"], [66, 37, 115, 18], [67, 10, 110, 6, "transitionSpec"], [67, 24, 110, 20], [67, 27, 110, 20, "_this$props"], [67, 38, 110, 20], [67, 39, 110, 6, "transitionSpec"], [67, 53, 110, 20], [68, 10, 111, 6, "onOpen"], [68, 16, 111, 12], [68, 19, 111, 12, "_this$props"], [68, 30, 111, 12], [68, 31, 111, 6, "onOpen"], [68, 37, 111, 12], [69, 10, 112, 6, "onClose"], [69, 17, 112, 13], [69, 20, 112, 13, "_this$props"], [69, 31, 112, 13], [69, 32, 112, 6, "onClose"], [69, 39, 112, 13], [70, 10, 113, 6, "onTransition"], [70, 22, 113, 18], [70, 25, 113, 18, "_this$props"], [70, 36, 113, 18], [70, 37, 113, 6, "onTransition"], [70, 49, 113, 18], [71, 10, 114, 6, "gesture"], [71, 17, 114, 13], [71, 20, 114, 13, "_this$props"], [71, 31, 114, 13], [71, 32, 114, 6, "gesture"], [71, 39, 114, 13], [72, 8, 116, 4], [72, 12, 116, 10, "toValue"], [72, 19, 116, 17], [72, 22, 116, 20, "_this"], [72, 27, 116, 20], [72, 28, 116, 25, "getAnimateToValue"], [72, 45, 116, 42], [72, 46, 116, 43], [73, 10, 117, 6], [73, 13, 117, 9, "_this"], [73, 18, 117, 9], [73, 19, 117, 14, "props"], [73, 24, 117, 19], [74, 10, 118, 6, "closing"], [75, 8, 119, 4], [75, 9, 119, 5], [75, 10, 119, 6], [76, 8, 120, 4, "_this"], [76, 13, 120, 4], [76, 14, 120, 9, "lastToValue"], [76, 25, 120, 20], [76, 28, 120, 23, "toValue"], [76, 35, 120, 30], [77, 8, 121, 4, "_this"], [77, 13, 121, 4], [77, 14, 121, 9, "isClosing"], [77, 23, 121, 18], [77, 24, 121, 19, "setValue"], [77, 32, 121, 27], [77, 33, 121, 28, "closing"], [77, 40, 121, 35], [77, 43, 121, 38, "TRUE"], [77, 47, 121, 42], [77, 50, 121, 45, "FALSE"], [77, 55, 121, 50], [77, 56, 121, 51], [78, 8, 122, 4], [78, 12, 122, 10, "spec"], [78, 16, 122, 14], [78, 19, 122, 17, "closing"], [78, 26, 122, 24], [78, 29, 122, 27, "transitionSpec"], [78, 43, 122, 41], [78, 44, 122, 42, "close"], [78, 49, 122, 47], [78, 52, 122, 50, "transitionSpec"], [78, 66, 122, 64], [78, 67, 122, 65, "open"], [78, 71, 122, 69], [79, 8, 123, 4], [79, 12, 123, 10, "animation"], [79, 21, 123, 19], [79, 24, 123, 22, "spec"], [79, 28, 123, 26], [79, 29, 123, 27, "animation"], [79, 38, 123, 36], [79, 43, 123, 41], [79, 51, 123, 49], [79, 54, 123, 52, "Animated"], [79, 75, 123, 60], [79, 76, 123, 61, "spring"], [79, 82, 123, 67], [79, 85, 123, 70, "Animated"], [79, 106, 123, 78], [79, 107, 123, 79, "timing"], [79, 113, 123, 85], [80, 8, 124, 4, "_this"], [80, 13, 124, 4], [80, 14, 124, 9, "setPointerEventsEnabled"], [80, 37, 124, 32], [80, 38, 124, 33], [80, 39, 124, 34, "closing"], [80, 46, 124, 41], [80, 47, 124, 42], [81, 8, 125, 4, "_this"], [81, 13, 125, 4], [81, 14, 125, 9, "handleStartInteraction"], [81, 36, 125, 31], [81, 37, 125, 32], [81, 38, 125, 33], [82, 8, 126, 4, "clearTimeout"], [82, 20, 126, 16], [82, 21, 126, 17, "_this"], [82, 26, 126, 17], [82, 27, 126, 22, "pendingGestureCallback"], [82, 49, 126, 44], [82, 50, 126, 45], [83, 8, 127, 4, "onTransition"], [83, 20, 127, 16], [83, 23, 127, 19], [84, 10, 128, 6, "closing"], [84, 17, 128, 13], [85, 10, 129, 6, "gesture"], [85, 17, 129, 13], [85, 19, 129, 15, "velocity"], [85, 27, 129, 23], [85, 32, 129, 28, "undefined"], [86, 8, 130, 4], [86, 9, 130, 5], [86, 10, 130, 6], [87, 8, 131, 4, "animation"], [87, 17, 131, 13], [87, 18, 131, 14, "gesture"], [87, 25, 131, 21], [87, 27, 131, 23], [88, 10, 132, 6], [88, 13, 132, 9, "spec"], [88, 17, 132, 13], [88, 18, 132, 14, "config"], [88, 24, 132, 20], [89, 10, 133, 6, "velocity"], [89, 18, 133, 14], [90, 10, 134, 6, "toValue"], [90, 17, 134, 13], [91, 10, 135, 6, "useNativeDriver"], [91, 25, 135, 21], [92, 10, 136, 6, "isInteraction"], [92, 23, 136, 19], [92, 25, 136, 21], [93, 8, 137, 4], [93, 9, 137, 5], [93, 10, 137, 6], [93, 11, 137, 7, "start"], [93, 16, 137, 12], [93, 17, 137, 13, "_ref2"], [93, 22, 137, 13], [93, 26, 139, 10], [94, 10, 139, 10], [94, 14, 138, 6, "finished"], [94, 22, 138, 14], [94, 25, 138, 14, "_ref2"], [94, 30, 138, 14], [94, 31, 138, 6, "finished"], [94, 39, 138, 14], [95, 10, 140, 6, "_this"], [95, 15, 140, 6], [95, 16, 140, 11, "handleEndInteraction"], [95, 36, 140, 31], [95, 37, 140, 32], [95, 38, 140, 33], [96, 10, 141, 6, "clearTimeout"], [96, 22, 141, 18], [96, 23, 141, 19, "_this"], [96, 28, 141, 19], [96, 29, 141, 24, "pendingGestureCallback"], [96, 51, 141, 46], [96, 52, 141, 47], [97, 10, 142, 6], [97, 14, 142, 10, "finished"], [97, 22, 142, 18], [97, 24, 142, 20], [98, 12, 143, 8], [98, 16, 143, 12, "closing"], [98, 23, 143, 19], [98, 25, 143, 21], [99, 14, 144, 10, "onClose"], [99, 21, 144, 17], [99, 22, 144, 18], [99, 23, 144, 19], [100, 12, 145, 8], [100, 13, 145, 9], [100, 19, 145, 15], [101, 14, 146, 10, "onOpen"], [101, 20, 146, 16], [101, 21, 146, 17], [101, 22, 146, 18], [102, 12, 147, 8], [103, 12, 148, 8], [103, 16, 148, 12, "_this"], [103, 21, 148, 12], [103, 22, 148, 17, "isCurrentlyMounted"], [103, 40, 148, 35], [103, 42, 148, 37], [104, 14, 149, 10], [105, 14, 150, 10, "_this"], [105, 19, 150, 10], [105, 20, 150, 15, "forceUpdate"], [105, 31, 150, 26], [105, 32, 150, 27], [105, 33, 150, 28], [106, 12, 151, 8], [107, 10, 152, 6], [108, 8, 153, 4], [108, 9, 153, 5], [108, 10, 153, 6], [109, 6, 154, 2], [109, 7, 154, 3], [110, 6, 154, 3, "_this"], [110, 11, 154, 3], [110, 12, 155, 2, "getAnimateToValue"], [110, 29, 155, 19], [110, 32, 155, 22, "_ref3"], [110, 37, 155, 22], [110, 41, 161, 8], [111, 8, 161, 8], [111, 12, 156, 4, "closing"], [111, 19, 156, 11], [111, 22, 156, 11, "_ref3"], [111, 27, 156, 11], [111, 28, 156, 4, "closing"], [111, 35, 156, 11], [112, 10, 157, 4, "layout"], [112, 16, 157, 10], [112, 19, 157, 10, "_ref3"], [112, 24, 157, 10], [112, 25, 157, 4, "layout"], [112, 31, 157, 10], [113, 10, 158, 4, "gestureDirection"], [113, 26, 158, 20], [113, 29, 158, 20, "_ref3"], [113, 34, 158, 20], [113, 35, 158, 4, "gestureDirection"], [113, 51, 158, 20], [114, 10, 159, 4, "direction"], [114, 19, 159, 13], [114, 22, 159, 13, "_ref3"], [114, 27, 159, 13], [114, 28, 159, 4, "direction"], [114, 37, 159, 13], [115, 10, 160, 4, "preloaded"], [115, 19, 160, 13], [115, 22, 160, 13, "_ref3"], [115, 27, 160, 13], [115, 28, 160, 4, "preloaded"], [115, 37, 160, 13], [116, 8, 162, 4], [116, 12, 162, 8], [116, 13, 162, 9, "closing"], [116, 20, 162, 16], [116, 24, 162, 20], [116, 25, 162, 21, "preloaded"], [116, 34, 162, 30], [116, 36, 162, 32], [117, 10, 163, 6], [117, 17, 163, 13], [117, 18, 163, 14], [118, 8, 164, 4], [119, 8, 165, 4], [119, 15, 165, 11], [119, 19, 165, 11, "getDistanceForDirection"], [119, 67, 165, 34], [119, 69, 165, 35, "layout"], [119, 75, 165, 41], [119, 77, 165, 43, "gestureDirection"], [119, 93, 165, 59], [119, 95, 165, 61, "direction"], [119, 104, 165, 70], [119, 109, 165, 75], [119, 114, 165, 80], [119, 115, 165, 81], [120, 6, 166, 2], [120, 7, 166, 3], [121, 6, 166, 3, "_this"], [121, 11, 166, 3], [121, 12, 167, 2, "setPointerEventsEnabled"], [121, 35, 167, 25], [121, 38, 167, 28, "enabled"], [121, 45, 167, 35], [121, 49, 167, 39], [122, 8, 168, 4], [122, 12, 168, 10, "pointerEvents"], [122, 25, 168, 23], [122, 28, 168, 26, "enabled"], [122, 35, 168, 33], [122, 38, 168, 36], [122, 48, 168, 46], [122, 51, 168, 49], [122, 57, 168, 55], [123, 8, 169, 4, "_this"], [123, 13, 169, 4], [123, 14, 169, 9, "ref"], [123, 17, 169, 12], [123, 18, 169, 13, "current"], [123, 25, 169, 20], [123, 27, 169, 22, "setPointerEvents"], [123, 43, 169, 38], [123, 44, 169, 39, "pointerEvents"], [123, 57, 169, 52], [123, 58, 169, 53], [124, 6, 170, 2], [124, 7, 170, 3], [125, 6, 170, 3, "_this"], [125, 11, 170, 3], [125, 12, 171, 2, "handleStartInteraction"], [125, 34, 171, 24], [125, 37, 171, 27], [125, 43, 171, 33], [126, 8, 172, 4], [126, 12, 172, 8, "_this"], [126, 17, 172, 8], [126, 18, 172, 13, "interactionHandle"], [126, 35, 172, 30], [126, 40, 172, 35, "undefined"], [126, 49, 172, 44], [126, 51, 172, 46], [127, 10, 173, 6, "_this"], [127, 15, 173, 6], [127, 16, 173, 11, "interactionHandle"], [127, 33, 173, 28], [127, 36, 173, 31, "InteractionManager"], [127, 67, 173, 49], [127, 68, 173, 50, "createInteractionHandle"], [127, 91, 173, 73], [127, 92, 173, 74], [127, 93, 173, 75], [128, 8, 174, 4], [129, 6, 175, 2], [129, 7, 175, 3], [130, 6, 175, 3, "_this"], [130, 11, 175, 3], [130, 12, 176, 2, "handleEndInteraction"], [130, 32, 176, 22], [130, 35, 176, 25], [130, 41, 176, 31], [131, 8, 177, 4], [131, 12, 177, 8, "_this"], [131, 17, 177, 8], [131, 18, 177, 13, "interactionHandle"], [131, 35, 177, 30], [131, 40, 177, 35, "undefined"], [131, 49, 177, 44], [131, 51, 177, 46], [132, 10, 178, 6, "InteractionManager"], [132, 41, 178, 24], [132, 42, 178, 25, "clearInteractionHandle"], [132, 64, 178, 47], [132, 65, 178, 48, "_this"], [132, 70, 178, 48], [132, 71, 178, 53, "interactionHandle"], [132, 88, 178, 70], [132, 89, 178, 71], [133, 10, 179, 6, "_this"], [133, 15, 179, 6], [133, 16, 179, 11, "interactionHandle"], [133, 33, 179, 28], [133, 36, 179, 31, "undefined"], [133, 45, 179, 40], [134, 8, 180, 4], [135, 6, 181, 2], [135, 7, 181, 3], [136, 6, 181, 3, "_this"], [136, 11, 181, 3], [136, 12, 182, 2, "handleGestureStateChange"], [136, 36, 182, 26], [136, 39, 182, 29, "_ref4"], [136, 44, 182, 29], [136, 48, 184, 8], [137, 8, 184, 8], [137, 12, 183, 4, "nativeEvent"], [137, 23, 183, 15], [137, 26, 183, 15, "_ref4"], [137, 31, 183, 15], [137, 32, 183, 4, "nativeEvent"], [137, 43, 183, 15], [138, 8, 185, 4], [138, 12, 185, 4, "_this$props2"], [138, 24, 185, 4], [138, 27, 194, 8, "_this"], [138, 32, 194, 8], [138, 33, 194, 13, "props"], [138, 38, 194, 18], [139, 10, 186, 6, "direction"], [139, 19, 186, 15], [139, 22, 186, 15, "_this$props2"], [139, 34, 186, 15], [139, 35, 186, 6, "direction"], [139, 44, 186, 15], [140, 10, 187, 6, "layout"], [140, 16, 187, 12], [140, 19, 187, 12, "_this$props2"], [140, 31, 187, 12], [140, 32, 187, 6, "layout"], [140, 38, 187, 12], [141, 10, 188, 6, "onClose"], [141, 17, 188, 13], [141, 20, 188, 13, "_this$props2"], [141, 32, 188, 13], [141, 33, 188, 6, "onClose"], [141, 40, 188, 13], [142, 10, 189, 6, "onGestureBegin"], [142, 24, 189, 20], [142, 27, 189, 20, "_this$props2"], [142, 39, 189, 20], [142, 40, 189, 6, "onGestureBegin"], [142, 54, 189, 20], [143, 10, 190, 6, "onGestureCanceled"], [143, 27, 190, 23], [143, 30, 190, 23, "_this$props2"], [143, 42, 190, 23], [143, 43, 190, 6, "onGestureCanceled"], [143, 60, 190, 23], [144, 10, 191, 6, "onGestureEnd"], [144, 22, 191, 18], [144, 25, 191, 18, "_this$props2"], [144, 37, 191, 18], [144, 38, 191, 6, "onGestureEnd"], [144, 50, 191, 18], [145, 10, 192, 6, "gestureDirection"], [145, 26, 192, 22], [145, 29, 192, 22, "_this$props2"], [145, 41, 192, 22], [145, 42, 192, 6, "gestureDirection"], [145, 58, 192, 22], [146, 10, 193, 6, "gestureVelocityImpact"], [146, 31, 193, 27], [146, 34, 193, 27, "_this$props2"], [146, 46, 193, 27], [146, 47, 193, 6, "gestureVelocityImpact"], [146, 68, 193, 27], [147, 8, 195, 4], [147, 16, 195, 12, "nativeEvent"], [147, 27, 195, 23], [147, 28, 195, 24, "state"], [147, 33, 195, 29], [148, 10, 196, 6], [148, 15, 196, 11, "GestureState"], [148, 43, 196, 23], [148, 44, 196, 24, "ACTIVE"], [148, 50, 196, 30], [149, 12, 197, 8, "_this"], [149, 17, 197, 8], [149, 18, 197, 13, "isSwiping"], [149, 27, 197, 22], [149, 28, 197, 23, "setValue"], [149, 36, 197, 31], [149, 37, 197, 32, "TRUE"], [149, 41, 197, 36], [149, 42, 197, 37], [150, 12, 198, 8, "_this"], [150, 17, 198, 8], [150, 18, 198, 13, "handleStartInteraction"], [150, 40, 198, 35], [150, 41, 198, 36], [150, 42, 198, 37], [151, 12, 199, 8, "onGestureBegin"], [151, 26, 199, 22], [151, 29, 199, 25], [151, 30, 199, 26], [152, 12, 200, 8], [153, 10, 201, 6], [153, 15, 201, 11, "GestureState"], [153, 43, 201, 23], [153, 44, 201, 24, "CANCELLED"], [153, 53, 201, 33], [154, 10, 202, 6], [154, 15, 202, 11, "GestureState"], [154, 43, 202, 23], [154, 44, 202, 24, "FAILED"], [154, 50, 202, 30], [155, 12, 203, 8], [156, 14, 204, 10, "_this"], [156, 19, 204, 10], [156, 20, 204, 15, "isSwiping"], [156, 29, 204, 24], [156, 30, 204, 25, "setValue"], [156, 38, 204, 33], [156, 39, 204, 34, "FALSE"], [156, 44, 204, 39], [156, 45, 204, 40], [157, 14, 205, 10, "_this"], [157, 19, 205, 10], [157, 20, 205, 15, "handleEndInteraction"], [157, 40, 205, 35], [157, 41, 205, 36], [157, 42, 205, 37], [158, 14, 206, 10], [158, 18, 206, 16, "velocity"], [158, 26, 206, 24], [158, 29, 206, 27, "gestureDirection"], [158, 45, 206, 43], [158, 50, 206, 48], [158, 60, 206, 58], [158, 64, 206, 62, "gestureDirection"], [158, 80, 206, 78], [158, 85, 206, 83], [158, 104, 206, 102], [158, 107, 206, 105, "nativeEvent"], [158, 118, 206, 116], [158, 119, 206, 117, "velocityY"], [158, 128, 206, 126], [158, 131, 206, 129, "nativeEvent"], [158, 142, 206, 140], [158, 143, 206, 141, "velocityX"], [158, 152, 206, 150], [159, 14, 207, 10, "_this"], [159, 19, 207, 10], [159, 20, 207, 15, "animate"], [159, 27, 207, 22], [159, 28, 207, 23], [160, 16, 208, 12, "closing"], [160, 23, 208, 19], [160, 25, 208, 21, "_this"], [160, 30, 208, 21], [160, 31, 208, 26, "props"], [160, 36, 208, 31], [160, 37, 208, 32, "closing"], [160, 44, 208, 39], [161, 16, 209, 12, "velocity"], [162, 14, 210, 10], [162, 15, 210, 11], [162, 16, 210, 12], [163, 14, 211, 10, "onGestureCanceled"], [163, 31, 211, 27], [163, 34, 211, 30], [163, 35, 211, 31], [164, 14, 212, 10], [165, 12, 213, 8], [166, 10, 214, 6], [166, 15, 214, 11, "GestureState"], [166, 43, 214, 23], [166, 44, 214, 24, "END"], [166, 47, 214, 27], [167, 12, 215, 8], [168, 14, 216, 10, "_this"], [168, 19, 216, 10], [168, 20, 216, 15, "isSwiping"], [168, 29, 216, 24], [168, 30, 216, 25, "setValue"], [168, 38, 216, 33], [168, 39, 216, 34, "FALSE"], [168, 44, 216, 39], [168, 45, 216, 40], [169, 14, 217, 10], [169, 18, 217, 14, "distance"], [169, 26, 217, 22], [170, 14, 218, 10], [170, 18, 218, 14, "translation"], [170, 29, 218, 25], [171, 14, 219, 10], [171, 18, 219, 14, "velocity"], [171, 27, 219, 22], [172, 14, 220, 10], [172, 18, 220, 14, "gestureDirection"], [172, 34, 220, 30], [172, 39, 220, 35], [172, 49, 220, 45], [172, 53, 220, 49, "gestureDirection"], [172, 69, 220, 65], [172, 74, 220, 70], [172, 93, 220, 89], [172, 95, 220, 91], [173, 16, 221, 12, "distance"], [173, 24, 221, 20], [173, 27, 221, 23, "layout"], [173, 33, 221, 29], [173, 34, 221, 30, "height"], [173, 40, 221, 36], [174, 16, 222, 12, "translation"], [174, 27, 222, 23], [174, 30, 222, 26, "nativeEvent"], [174, 41, 222, 37], [174, 42, 222, 38, "translationY"], [174, 54, 222, 50], [175, 16, 223, 12, "velocity"], [175, 25, 223, 20], [175, 28, 223, 23, "nativeEvent"], [175, 39, 223, 34], [175, 40, 223, 35, "velocityY"], [175, 49, 223, 44], [176, 14, 224, 10], [176, 15, 224, 11], [176, 21, 224, 17], [177, 16, 225, 12, "distance"], [177, 24, 225, 20], [177, 27, 225, 23, "layout"], [177, 33, 225, 29], [177, 34, 225, 30, "width"], [177, 39, 225, 35], [178, 16, 226, 12, "translation"], [178, 27, 226, 23], [178, 30, 226, 26, "nativeEvent"], [178, 41, 226, 37], [178, 42, 226, 38, "translationX"], [178, 54, 226, 50], [179, 16, 227, 12, "velocity"], [179, 25, 227, 20], [179, 28, 227, 23, "nativeEvent"], [179, 39, 227, 34], [179, 40, 227, 35, "velocityX"], [179, 49, 227, 44], [180, 14, 228, 10], [181, 14, 229, 10], [181, 18, 229, 16, "closing"], [181, 25, 229, 23], [181, 28, 229, 26], [181, 29, 229, 27, "translation"], [181, 40, 229, 38], [181, 43, 229, 41, "velocity"], [181, 52, 229, 49], [181, 55, 229, 52, "gestureVelocityImpact"], [181, 76, 229, 73], [181, 80, 229, 77], [181, 84, 229, 77, "getInvertedMultiplier"], [181, 128, 229, 98], [181, 130, 229, 99, "gestureDirection"], [181, 146, 229, 115], [181, 148, 229, 117, "direction"], [181, 157, 229, 126], [181, 162, 229, 131], [181, 167, 229, 136], [181, 168, 229, 137], [181, 171, 229, 140, "distance"], [181, 179, 229, 148], [181, 182, 229, 151], [181, 183, 229, 152], [181, 186, 229, 155, "velocity"], [181, 195, 229, 163], [181, 200, 229, 168], [181, 201, 229, 169], [181, 205, 229, 173, "translation"], [181, 216, 229, 184], [181, 221, 229, 189], [181, 222, 229, 190], [181, 225, 229, 193, "_this"], [181, 230, 229, 193], [181, 231, 229, 198, "props"], [181, 236, 229, 203], [181, 237, 229, 204, "closing"], [181, 244, 229, 211], [182, 14, 230, 10, "_this"], [182, 19, 230, 10], [182, 20, 230, 15, "animate"], [182, 27, 230, 22], [182, 28, 230, 23], [183, 16, 231, 12, "closing"], [183, 23, 231, 19], [184, 16, 232, 12, "velocity"], [184, 24, 232, 20], [184, 26, 232, 12, "velocity"], [185, 14, 233, 10], [185, 15, 233, 11], [185, 16, 233, 12], [186, 14, 234, 10], [186, 18, 234, 14, "closing"], [186, 25, 234, 21], [186, 27, 234, 23], [187, 16, 235, 12], [188, 16, 236, 12], [189, 16, 237, 12, "_this"], [189, 21, 237, 12], [189, 22, 237, 17, "pendingGestureCallback"], [189, 44, 237, 39], [189, 47, 237, 42, "setTimeout"], [189, 57, 237, 52], [189, 58, 237, 53], [189, 64, 237, 59], [190, 18, 238, 14, "onClose"], [190, 25, 238, 21], [190, 26, 238, 22], [190, 27, 238, 23], [192, 18, 240, 14], [193, 18, 241, 14], [194, 18, 242, 14, "_this"], [194, 23, 242, 14], [194, 24, 242, 19, "forceUpdate"], [194, 35, 242, 30], [194, 36, 242, 31], [194, 37, 242, 32], [195, 16, 243, 12], [195, 17, 243, 13], [195, 19, 243, 15], [195, 21, 243, 17], [195, 22, 243, 18], [196, 14, 244, 10], [197, 14, 245, 10, "onGestureEnd"], [197, 26, 245, 22], [197, 29, 245, 25], [197, 30, 245, 26], [198, 14, 246, 10], [199, 12, 247, 8], [200, 8, 248, 4], [201, 6, 249, 2], [201, 7, 249, 3], [202, 6, 251, 2], [203, 6, 251, 2, "_this"], [203, 11, 251, 2], [203, 12, 252, 2, "getInterpolatedStyle"], [203, 32, 252, 22], [203, 35, 252, 25], [203, 39, 252, 25, "memoize"], [203, 55, 252, 32], [203, 57, 252, 33], [203, 58, 252, 34, "styleInterpolator"], [203, 75, 252, 51], [203, 77, 252, 53, "animation"], [203, 86, 252, 62], [203, 91, 252, 67, "styleInterpolator"], [203, 108, 252, 84], [203, 109, 252, 85, "animation"], [203, 118, 252, 94], [203, 119, 252, 95], [203, 120, 252, 96], [204, 6, 254, 2], [205, 6, 254, 2, "_this"], [205, 11, 254, 2], [205, 12, 255, 2, "getCardAnimation"], [205, 28, 255, 18], [205, 31, 255, 21], [205, 35, 255, 21, "memoize"], [205, 51, 255, 28], [205, 53, 255, 29], [205, 54, 255, 30, "interpolationIndex"], [205, 72, 255, 48], [205, 74, 255, 50, "current"], [205, 81, 255, 57], [205, 83, 255, 59, "next"], [205, 87, 255, 63], [205, 89, 255, 65, "layout"], [205, 95, 255, 71], [205, 97, 255, 73, "insetTop"], [205, 105, 255, 81], [205, 107, 255, 83, "insetRight"], [205, 117, 255, 93], [205, 119, 255, 95, "insetBottom"], [205, 130, 255, 106], [205, 132, 255, 108, "insetLeft"], [205, 141, 255, 117], [205, 147, 255, 123], [206, 8, 256, 4, "index"], [206, 13, 256, 9], [206, 15, 256, 11, "interpolationIndex"], [206, 33, 256, 29], [207, 8, 257, 4, "current"], [207, 15, 257, 11], [207, 17, 257, 13], [208, 10, 258, 6, "progress"], [208, 18, 258, 14], [208, 20, 258, 16, "current"], [209, 8, 259, 4], [209, 9, 259, 5], [210, 8, 260, 4, "next"], [210, 12, 260, 8], [210, 14, 260, 10, "next"], [210, 18, 260, 14], [210, 22, 260, 18], [211, 10, 261, 6, "progress"], [211, 18, 261, 14], [211, 20, 261, 16, "next"], [212, 8, 262, 4], [212, 9, 262, 5], [213, 8, 263, 4, "closing"], [213, 15, 263, 11], [213, 17, 263, 13, "_this"], [213, 22, 263, 13], [213, 23, 263, 18, "isClosing"], [213, 32, 263, 27], [214, 8, 264, 4, "swiping"], [214, 15, 264, 11], [214, 17, 264, 13, "_this"], [214, 22, 264, 13], [214, 23, 264, 18, "isSwiping"], [214, 32, 264, 27], [215, 8, 265, 4, "inverted"], [215, 16, 265, 12], [215, 18, 265, 14, "_this"], [215, 23, 265, 14], [215, 24, 265, 19, "inverted"], [215, 32, 265, 27], [216, 8, 266, 4, "layouts"], [216, 15, 266, 11], [216, 17, 266, 13], [217, 10, 267, 6, "screen"], [217, 16, 267, 12], [217, 18, 267, 14, "layout"], [218, 8, 268, 4], [218, 9, 268, 5], [219, 8, 269, 4, "insets"], [219, 14, 269, 10], [219, 16, 269, 12], [220, 10, 270, 6, "top"], [220, 13, 270, 9], [220, 15, 270, 11, "insetTop"], [220, 23, 270, 19], [221, 10, 271, 6, "right"], [221, 15, 271, 11], [221, 17, 271, 13, "insetRight"], [221, 27, 271, 23], [222, 10, 272, 6, "bottom"], [222, 16, 272, 12], [222, 18, 272, 14, "insetBottom"], [222, 29, 272, 25], [223, 10, 273, 6, "left"], [223, 14, 273, 10], [223, 16, 273, 12, "insetLeft"], [224, 8, 274, 4], [225, 6, 275, 2], [225, 7, 275, 3], [225, 8, 275, 4], [225, 9, 275, 5], [226, 6, 275, 5, "_this"], [226, 11, 275, 5], [226, 12, 327, 2, "ref"], [226, 15, 327, 5], [226, 18, 327, 8], [226, 31, 327, 21, "React"], [226, 36, 327, 26], [226, 37, 327, 27, "createRef"], [226, 46, 327, 36], [226, 47, 327, 37], [226, 48, 327, 38], [227, 6, 327, 38], [227, 13, 327, 38, "_this"], [227, 18, 327, 38], [228, 4, 327, 38], [229, 4, 327, 38], [229, 8, 327, 38, "_inherits2"], [229, 18, 327, 38], [229, 19, 327, 38, "default"], [229, 26, 327, 38], [229, 28, 327, 38, "Card"], [229, 32, 327, 38], [229, 34, 327, 38, "_React$Component"], [229, 50, 327, 38], [230, 4, 327, 38], [230, 15, 327, 38, "_createClass2"], [230, 28, 327, 38], [230, 29, 327, 38, "default"], [230, 36, 327, 38], [230, 38, 327, 38, "Card"], [230, 42, 327, 38], [231, 6, 327, 38, "key"], [231, 9, 327, 38], [232, 6, 327, 38, "value"], [232, 11, 327, 38], [232, 13, 43, 2], [232, 22, 43, 2, "componentDidMount"], [232, 39, 43, 19, "componentDidMount"], [232, 40, 43, 19], [232, 42, 43, 22], [233, 8, 44, 4], [233, 12, 44, 8], [233, 13, 44, 9], [233, 17, 44, 13], [233, 18, 44, 14, "props"], [233, 23, 44, 19], [233, 24, 44, 20, "preloaded"], [233, 33, 44, 29], [233, 35, 44, 31], [234, 10, 45, 6], [234, 14, 45, 10], [234, 15, 45, 11, "animate"], [234, 22, 45, 18], [234, 23, 45, 19], [235, 12, 46, 8, "closing"], [235, 19, 46, 15], [235, 21, 46, 17], [235, 25, 46, 21], [235, 26, 46, 22, "props"], [235, 31, 46, 27], [235, 32, 46, 28, "closing"], [236, 10, 47, 6], [236, 11, 47, 7], [236, 12, 47, 8], [237, 8, 48, 4], [238, 8, 49, 4], [238, 12, 49, 8], [238, 13, 49, 9, "isCurrentlyMounted"], [238, 31, 49, 27], [238, 34, 49, 30], [238, 38, 49, 34], [239, 6, 50, 2], [240, 4, 50, 3], [241, 6, 50, 3, "key"], [241, 9, 50, 3], [242, 6, 50, 3, "value"], [242, 11, 50, 3], [242, 13, 51, 2], [242, 22, 51, 2, "componentDidUpdate"], [242, 40, 51, 20, "componentDidUpdate"], [242, 41, 51, 21, "prevProps"], [242, 50, 51, 30], [242, 52, 51, 32], [243, 8, 52, 4], [243, 12, 52, 4, "_this$props3"], [243, 24, 52, 4], [243, 27, 59, 8], [243, 31, 59, 12], [243, 32, 59, 13, "props"], [243, 37, 59, 18], [244, 10, 53, 6, "gesture"], [244, 17, 53, 13], [244, 20, 53, 13, "_this$props3"], [244, 32, 53, 13], [244, 33, 53, 6, "gesture"], [244, 40, 53, 13], [245, 10, 54, 6, "direction"], [245, 19, 54, 15], [245, 22, 54, 15, "_this$props3"], [245, 34, 54, 15], [245, 35, 54, 6, "direction"], [245, 44, 54, 15], [246, 10, 55, 6, "layout"], [246, 16, 55, 12], [246, 19, 55, 12, "_this$props3"], [246, 31, 55, 12], [246, 32, 55, 6, "layout"], [246, 38, 55, 12], [247, 10, 56, 6, "gestureDirection"], [247, 26, 56, 22], [247, 29, 56, 22, "_this$props3"], [247, 41, 56, 22], [247, 42, 56, 6, "gestureDirection"], [247, 58, 56, 22], [248, 10, 57, 6, "opening"], [248, 17, 57, 13], [248, 20, 57, 13, "_this$props3"], [248, 32, 57, 13], [248, 33, 57, 6, "opening"], [248, 40, 57, 13], [249, 10, 58, 6, "closing"], [249, 17, 58, 13], [249, 20, 58, 13, "_this$props3"], [249, 32, 58, 13], [249, 33, 58, 6, "closing"], [249, 40, 58, 13], [250, 8, 60, 4], [250, 12, 61, 6, "width"], [250, 17, 61, 11], [250, 20, 63, 8, "layout"], [250, 26, 63, 14], [250, 27, 61, 6, "width"], [250, 32, 61, 11], [251, 10, 62, 6, "height"], [251, 16, 62, 12], [251, 19, 63, 8, "layout"], [251, 25, 63, 14], [251, 26, 62, 6, "height"], [251, 32, 62, 12], [252, 8, 64, 4], [252, 12, 64, 8, "width"], [252, 17, 64, 13], [252, 22, 64, 18, "prevProps"], [252, 31, 64, 27], [252, 32, 64, 28, "layout"], [252, 38, 64, 34], [252, 39, 64, 35, "width"], [252, 44, 64, 40], [252, 46, 64, 42], [253, 10, 65, 6], [253, 14, 65, 10], [253, 15, 65, 11, "layout"], [253, 21, 65, 17], [253, 22, 65, 18, "width"], [253, 27, 65, 23], [253, 28, 65, 24, "setValue"], [253, 36, 65, 32], [253, 37, 65, 33, "width"], [253, 42, 65, 38], [253, 43, 65, 39], [254, 8, 66, 4], [255, 8, 67, 4], [255, 12, 67, 8, "height"], [255, 18, 67, 14], [255, 23, 67, 19, "prevProps"], [255, 32, 67, 28], [255, 33, 67, 29, "layout"], [255, 39, 67, 35], [255, 40, 67, 36, "height"], [255, 46, 67, 42], [255, 48, 67, 44], [256, 10, 68, 6], [256, 14, 68, 10], [256, 15, 68, 11, "layout"], [256, 21, 68, 17], [256, 22, 68, 18, "height"], [256, 28, 68, 24], [256, 29, 68, 25, "setValue"], [256, 37, 68, 33], [256, 38, 68, 34, "height"], [256, 44, 68, 40], [256, 45, 68, 41], [257, 8, 69, 4], [258, 8, 70, 4], [258, 12, 70, 8, "gestureDirection"], [258, 28, 70, 24], [258, 33, 70, 29, "prevProps"], [258, 42, 70, 38], [258, 43, 70, 39, "gestureDirection"], [258, 59, 70, 55], [258, 61, 70, 57], [259, 10, 71, 6], [259, 14, 71, 10], [259, 15, 71, 11, "inverted"], [259, 23, 71, 19], [259, 24, 71, 20, "setValue"], [259, 32, 71, 28], [259, 33, 71, 29], [259, 37, 71, 29, "getInvertedMultiplier"], [259, 81, 71, 50], [259, 83, 71, 51, "gestureDirection"], [259, 99, 71, 67], [259, 101, 71, 69, "direction"], [259, 110, 71, 78], [259, 115, 71, 83], [259, 120, 71, 88], [259, 121, 71, 89], [259, 122, 71, 90], [260, 8, 72, 4], [261, 8, 73, 4], [261, 12, 73, 10, "toValue"], [261, 19, 73, 17], [261, 22, 73, 20], [261, 26, 73, 24], [261, 27, 73, 25, "getAnimateToValue"], [261, 44, 73, 42], [261, 45, 73, 43], [261, 49, 73, 47], [261, 50, 73, 48, "props"], [261, 55, 73, 53], [261, 56, 73, 54], [262, 8, 74, 4], [262, 12, 74, 8], [262, 16, 74, 12], [262, 17, 74, 13, "getAnimateToValue"], [262, 34, 74, 30], [262, 35, 74, 31, "prevProps"], [262, 44, 74, 40], [262, 45, 74, 41], [262, 50, 74, 46, "toValue"], [262, 57, 74, 53], [262, 61, 74, 57], [262, 65, 74, 61], [262, 66, 74, 62, "lastToValue"], [262, 77, 74, 73], [262, 82, 74, 78, "toValue"], [262, 89, 74, 85], [262, 91, 74, 87], [263, 10, 75, 6], [264, 10, 76, 6], [265, 10, 77, 6], [266, 10, 78, 6], [267, 10, 79, 6], [268, 10, 80, 6], [268, 14, 80, 10], [268, 15, 80, 11, "animate"], [268, 22, 80, 18], [268, 23, 80, 19], [269, 12, 81, 8, "closing"], [270, 10, 82, 6], [270, 11, 82, 7], [270, 12, 82, 8], [271, 8, 83, 4], [271, 9, 83, 5], [271, 15, 83, 11], [271, 19, 83, 15, "opening"], [271, 26, 83, 22], [271, 30, 83, 26], [271, 31, 83, 27, "prevProps"], [271, 40, 83, 36], [271, 41, 83, 37, "opening"], [271, 48, 83, 44], [271, 50, 83, 46], [272, 10, 84, 6], [273, 10, 85, 6], [274, 10, 86, 6, "gesture"], [274, 17, 86, 13], [274, 18, 86, 14, "setValue"], [274, 26, 86, 22], [274, 27, 86, 23], [274, 31, 86, 23, "getDistanceForDirection"], [274, 79, 86, 46], [274, 81, 86, 47, "layout"], [274, 87, 86, 53], [274, 89, 86, 55, "gestureDirection"], [274, 105, 86, 71], [274, 107, 86, 73, "direction"], [274, 116, 86, 82], [274, 121, 86, 87], [274, 126, 86, 92], [274, 127, 86, 93], [274, 128, 86, 94], [275, 10, 87, 6], [275, 14, 87, 10], [275, 15, 87, 11, "animate"], [275, 22, 87, 18], [275, 23, 87, 19], [276, 12, 88, 8, "closing"], [277, 10, 89, 6], [277, 11, 89, 7], [277, 12, 89, 8], [278, 8, 90, 4], [279, 6, 91, 2], [280, 4, 91, 3], [281, 6, 91, 3, "key"], [281, 9, 91, 3], [282, 6, 91, 3, "value"], [282, 11, 91, 3], [282, 13, 92, 2], [282, 22, 92, 2, "componentWillUnmount"], [282, 42, 92, 22, "componentWillUnmount"], [282, 43, 92, 22], [282, 45, 92, 25], [283, 8, 93, 4], [283, 12, 93, 8], [283, 13, 93, 9, "props"], [283, 18, 93, 14], [283, 19, 93, 15, "gesture"], [283, 26, 93, 22], [283, 28, 93, 24, "stopAnimation"], [283, 41, 93, 37], [283, 42, 93, 38], [283, 43, 93, 39], [284, 8, 94, 4], [284, 12, 94, 8], [284, 13, 94, 9, "isCurrentlyMounted"], [284, 31, 94, 27], [284, 34, 94, 30], [284, 39, 94, 35], [285, 8, 95, 4], [285, 12, 95, 8], [285, 13, 95, 9, "handleEndInteraction"], [285, 33, 95, 29], [285, 34, 95, 30], [285, 35, 95, 31], [286, 6, 96, 2], [287, 4, 96, 3], [288, 6, 96, 3, "key"], [288, 9, 96, 3], [289, 6, 96, 3, "value"], [289, 11, 96, 3], [289, 13, 276, 2], [289, 22, 276, 2, "gestureActivationCriteria"], [289, 47, 276, 27, "gestureActivationCriteria"], [289, 48, 276, 27], [289, 50, 276, 30], [290, 8, 277, 4], [290, 12, 277, 4, "_this$props4"], [290, 24, 277, 4], [290, 27, 282, 8], [290, 31, 282, 12], [290, 32, 282, 13, "props"], [290, 37, 282, 18], [291, 10, 278, 6, "direction"], [291, 19, 278, 15], [291, 22, 278, 15, "_this$props4"], [291, 34, 278, 15], [291, 35, 278, 6, "direction"], [291, 44, 278, 15], [292, 10, 279, 6, "layout"], [292, 16, 279, 12], [292, 19, 279, 12, "_this$props4"], [292, 31, 279, 12], [292, 32, 279, 6, "layout"], [292, 38, 279, 12], [293, 10, 280, 6, "gestureDirection"], [293, 26, 280, 22], [293, 29, 280, 22, "_this$props4"], [293, 41, 280, 22], [293, 42, 280, 6, "gestureDirection"], [293, 58, 280, 22], [294, 10, 281, 6, "gestureResponseDistance"], [294, 33, 281, 29], [294, 36, 281, 29, "_this$props4"], [294, 48, 281, 29], [294, 49, 281, 6, "gestureResponseDistance"], [294, 72, 281, 29], [295, 8, 283, 4], [295, 12, 283, 10, "enableTrackpadTwoFingerGesture"], [295, 42, 283, 40], [295, 45, 283, 43], [295, 49, 283, 47], [296, 8, 284, 4], [296, 12, 284, 10, "distance"], [296, 20, 284, 18], [296, 23, 284, 21, "gestureResponseDistance"], [296, 46, 284, 44], [296, 51, 284, 49, "undefined"], [296, 60, 284, 58], [296, 63, 284, 61, "gestureResponseDistance"], [296, 86, 284, 84], [296, 89, 284, 87, "gestureDirection"], [296, 105, 284, 103], [296, 110, 284, 108], [296, 120, 284, 118], [296, 124, 284, 122, "gestureDirection"], [296, 140, 284, 138], [296, 145, 284, 143], [296, 164, 284, 162], [296, 167, 284, 165, "GESTURE_RESPONSE_DISTANCE_VERTICAL"], [296, 201, 284, 199], [296, 204, 284, 202, "GESTURE_RESPONSE_DISTANCE_HORIZONTAL"], [296, 240, 284, 238], [297, 8, 285, 4], [297, 12, 285, 8, "gestureDirection"], [297, 28, 285, 24], [297, 33, 285, 29], [297, 43, 285, 39], [297, 45, 285, 41], [298, 10, 286, 6], [298, 17, 286, 13], [299, 12, 287, 8, "maxDeltaX"], [299, 21, 287, 17], [299, 23, 287, 19], [299, 25, 287, 21], [300, 12, 288, 8, "minOffsetY"], [300, 22, 288, 18], [300, 24, 288, 20], [300, 25, 288, 21], [301, 12, 289, 8, "hitSlop"], [301, 19, 289, 15], [301, 21, 289, 17], [302, 14, 290, 10, "bottom"], [302, 20, 290, 16], [302, 22, 290, 18], [302, 23, 290, 19, "layout"], [302, 29, 290, 25], [302, 30, 290, 26, "height"], [302, 36, 290, 32], [302, 39, 290, 35, "distance"], [303, 12, 291, 8], [303, 13, 291, 9], [304, 12, 292, 8, "enableTrackpadTwoFingerGesture"], [305, 10, 293, 6], [305, 11, 293, 7], [306, 8, 294, 4], [306, 9, 294, 5], [306, 15, 294, 11], [306, 19, 294, 15, "gestureDirection"], [306, 35, 294, 31], [306, 40, 294, 36], [306, 59, 294, 55], [306, 61, 294, 57], [307, 10, 295, 6], [307, 17, 295, 13], [308, 12, 296, 8, "maxDeltaX"], [308, 21, 296, 17], [308, 23, 296, 19], [308, 25, 296, 21], [309, 12, 297, 8, "minOffsetY"], [309, 22, 297, 18], [309, 24, 297, 20], [309, 25, 297, 21], [309, 26, 297, 22], [310, 12, 298, 8, "hitSlop"], [310, 19, 298, 15], [310, 21, 298, 17], [311, 14, 299, 10, "top"], [311, 17, 299, 13], [311, 19, 299, 15], [311, 20, 299, 16, "layout"], [311, 26, 299, 22], [311, 27, 299, 23, "height"], [311, 33, 299, 29], [311, 36, 299, 32, "distance"], [312, 12, 300, 8], [312, 13, 300, 9], [313, 12, 301, 8, "enableTrackpadTwoFingerGesture"], [314, 10, 302, 6], [314, 11, 302, 7], [315, 8, 303, 4], [315, 9, 303, 5], [315, 15, 303, 11], [316, 10, 304, 6], [316, 14, 304, 12, "hitSlop"], [316, 21, 304, 19], [316, 24, 304, 22], [316, 25, 304, 23, "layout"], [316, 31, 304, 29], [316, 32, 304, 30, "width"], [316, 37, 304, 35], [316, 40, 304, 38, "distance"], [316, 48, 304, 46], [317, 10, 305, 6], [317, 14, 305, 12, "invertedMultiplier"], [317, 32, 305, 30], [317, 35, 305, 33], [317, 39, 305, 33, "getInvertedMultiplier"], [317, 83, 305, 54], [317, 85, 305, 55, "gestureDirection"], [317, 101, 305, 71], [317, 103, 305, 73, "direction"], [317, 112, 305, 82], [317, 117, 305, 87], [317, 122, 305, 92], [317, 123, 305, 93], [318, 10, 306, 6], [318, 14, 306, 10, "invertedMultiplier"], [318, 32, 306, 28], [318, 37, 306, 33], [318, 38, 306, 34], [318, 40, 306, 36], [319, 12, 307, 8], [319, 19, 307, 15], [320, 14, 308, 10, "minOffsetX"], [320, 24, 308, 20], [320, 26, 308, 22], [320, 27, 308, 23], [321, 14, 309, 10, "maxDeltaY"], [321, 23, 309, 19], [321, 25, 309, 21], [321, 27, 309, 23], [322, 14, 310, 10, "hitSlop"], [322, 21, 310, 17], [322, 23, 310, 19], [323, 16, 311, 12, "right"], [323, 21, 311, 17], [323, 23, 311, 19, "hitSlop"], [324, 14, 312, 10], [324, 15, 312, 11], [325, 14, 313, 10, "enableTrackpadTwoFingerGesture"], [326, 12, 314, 8], [326, 13, 314, 9], [327, 10, 315, 6], [327, 11, 315, 7], [327, 17, 315, 13], [328, 12, 316, 8], [328, 19, 316, 15], [329, 14, 317, 10, "minOffsetX"], [329, 24, 317, 20], [329, 26, 317, 22], [329, 27, 317, 23], [329, 28, 317, 24], [330, 14, 318, 10, "maxDeltaY"], [330, 23, 318, 19], [330, 25, 318, 21], [330, 27, 318, 23], [331, 14, 319, 10, "hitSlop"], [331, 21, 319, 17], [331, 23, 319, 19], [332, 16, 320, 12, "left"], [332, 20, 320, 16], [332, 22, 320, 18, "hitSlop"], [333, 14, 321, 10], [333, 15, 321, 11], [334, 14, 322, 10, "enableTrackpadTwoFingerGesture"], [335, 12, 323, 8], [335, 13, 323, 9], [336, 10, 324, 6], [337, 8, 325, 4], [338, 6, 326, 2], [339, 4, 326, 3], [340, 6, 326, 3, "key"], [340, 9, 326, 3], [341, 6, 326, 3, "value"], [341, 11, 326, 3], [341, 13, 328, 2], [341, 22, 328, 2, "render"], [341, 28, 328, 8, "render"], [341, 29, 328, 8], [341, 31, 328, 11], [342, 8, 329, 4], [342, 12, 329, 4, "_this$props5"], [342, 24, 329, 4], [342, 27, 360, 8], [342, 31, 360, 12], [342, 32, 360, 13, "props"], [342, 37, 360, 18], [343, 10, 330, 6, "styleInterpolator"], [343, 27, 330, 23], [343, 30, 330, 23, "_this$props5"], [343, 42, 330, 23], [343, 43, 330, 6, "styleInterpolator"], [343, 60, 330, 23], [344, 10, 331, 6, "interpolationIndex"], [344, 28, 331, 24], [344, 31, 331, 24, "_this$props5"], [344, 43, 331, 24], [344, 44, 331, 6, "interpolationIndex"], [344, 62, 331, 24], [345, 10, 332, 6, "current"], [345, 17, 332, 13], [345, 20, 332, 13, "_this$props5"], [345, 32, 332, 13], [345, 33, 332, 6, "current"], [345, 40, 332, 13], [346, 10, 333, 6, "gesture"], [346, 17, 333, 13], [346, 20, 333, 13, "_this$props5"], [346, 32, 333, 13], [346, 33, 333, 6, "gesture"], [346, 40, 333, 13], [347, 10, 334, 6, "next"], [347, 14, 334, 10], [347, 17, 334, 10, "_this$props5"], [347, 29, 334, 10], [347, 30, 334, 6, "next"], [347, 34, 334, 10], [348, 10, 335, 6, "layout"], [348, 16, 335, 12], [348, 19, 335, 12, "_this$props5"], [348, 31, 335, 12], [348, 32, 335, 6, "layout"], [348, 38, 335, 12], [349, 10, 336, 6, "insets"], [349, 16, 336, 12], [349, 19, 336, 12, "_this$props5"], [349, 31, 336, 12], [349, 32, 336, 6, "insets"], [349, 38, 336, 12], [350, 10, 337, 6, "overlay"], [350, 17, 337, 13], [350, 20, 337, 13, "_this$props5"], [350, 32, 337, 13], [350, 33, 337, 6, "overlay"], [350, 40, 337, 13], [351, 10, 338, 6, "overlayEnabled"], [351, 24, 338, 20], [351, 27, 338, 20, "_this$props5"], [351, 39, 338, 20], [351, 40, 338, 6, "overlayEnabled"], [351, 54, 338, 20], [352, 10, 339, 6, "shadowEnabled"], [352, 23, 339, 19], [352, 26, 339, 19, "_this$props5"], [352, 38, 339, 19], [352, 39, 339, 6, "shadowEnabled"], [352, 52, 339, 19], [353, 10, 340, 6, "gestureEnabled"], [353, 24, 340, 20], [353, 27, 340, 20, "_this$props5"], [353, 39, 340, 20], [353, 40, 340, 6, "gestureEnabled"], [353, 54, 340, 20], [354, 10, 341, 6, "gestureDirection"], [354, 26, 341, 22], [354, 29, 341, 22, "_this$props5"], [354, 41, 341, 22], [354, 42, 341, 6, "gestureDirection"], [354, 58, 341, 22], [355, 10, 342, 6, "pageOverflowEnabled"], [355, 29, 342, 25], [355, 32, 342, 25, "_this$props5"], [355, 44, 342, 25], [355, 45, 342, 6, "pageOverflowEnabled"], [355, 64, 342, 25], [356, 10, 343, 6, "children"], [356, 18, 343, 14], [356, 21, 343, 14, "_this$props5"], [356, 33, 343, 14], [356, 34, 343, 6, "children"], [356, 42, 343, 14], [357, 10, 344, 22, "customContainerStyle"], [357, 30, 344, 42], [357, 33, 344, 42, "_this$props5"], [357, 45, 344, 42], [357, 46, 344, 6, "containerStyle"], [357, 60, 344, 20], [358, 10, 345, 6, "contentStyle"], [358, 22, 345, 18], [358, 25, 345, 18, "_this$props5"], [358, 37, 345, 18], [358, 38, 345, 6, "contentStyle"], [358, 50, 345, 18], [359, 10, 347, 6, "closing"], [359, 17, 347, 13], [359, 20, 347, 13, "_this$props5"], [359, 32, 347, 13], [359, 33, 347, 6, "closing"], [359, 40, 347, 13], [360, 10, 348, 6, "direction"], [360, 19, 348, 15], [360, 22, 348, 15, "_this$props5"], [360, 34, 348, 15], [360, 35, 348, 6, "direction"], [360, 44, 348, 15], [361, 10, 349, 6, "gestureResponseDistance"], [361, 33, 349, 29], [361, 36, 349, 29, "_this$props5"], [361, 48, 349, 29], [361, 49, 349, 6, "gestureResponseDistance"], [361, 72, 349, 29], [362, 10, 350, 6, "gestureVelocityImpact"], [362, 31, 350, 27], [362, 34, 350, 27, "_this$props5"], [362, 46, 350, 27], [362, 47, 350, 6, "gestureVelocityImpact"], [362, 68, 350, 27], [363, 10, 351, 6, "onClose"], [363, 17, 351, 13], [363, 20, 351, 13, "_this$props5"], [363, 32, 351, 13], [363, 33, 351, 6, "onClose"], [363, 40, 351, 13], [364, 10, 352, 6, "onGestureBegin"], [364, 24, 352, 20], [364, 27, 352, 20, "_this$props5"], [364, 39, 352, 20], [364, 40, 352, 6, "onGestureBegin"], [364, 54, 352, 20], [365, 10, 353, 6, "onGestureCanceled"], [365, 27, 353, 23], [365, 30, 353, 23, "_this$props5"], [365, 42, 353, 23], [365, 43, 353, 6, "onGestureCanceled"], [365, 60, 353, 23], [366, 10, 354, 6, "onGestureEnd"], [366, 22, 354, 18], [366, 25, 354, 18, "_this$props5"], [366, 37, 354, 18], [366, 38, 354, 6, "onGestureEnd"], [366, 50, 354, 18], [367, 10, 355, 6, "onOpen"], [367, 16, 355, 12], [367, 19, 355, 12, "_this$props5"], [367, 31, 355, 12], [367, 32, 355, 6, "onOpen"], [367, 38, 355, 12], [368, 10, 356, 6, "onTransition"], [368, 22, 356, 18], [368, 25, 356, 18, "_this$props5"], [368, 37, 356, 18], [368, 38, 356, 6, "onTransition"], [368, 50, 356, 18], [369, 10, 357, 6, "transitionSpec"], [369, 24, 357, 20], [369, 27, 357, 20, "_this$props5"], [369, 39, 357, 20], [369, 40, 357, 6, "transitionSpec"], [369, 54, 357, 20], [370, 10, 359, 9, "rest"], [370, 14, 359, 13], [370, 21, 359, 13, "_objectWithoutProperties2"], [370, 46, 359, 13], [370, 47, 359, 13, "default"], [370, 54, 359, 13], [370, 56, 359, 13, "_this$props5"], [370, 68, 359, 13], [370, 70, 359, 13, "_excluded"], [370, 79, 359, 13], [371, 8, 361, 4], [371, 12, 361, 10, "interpolationProps"], [371, 30, 361, 28], [371, 33, 361, 31], [371, 37, 361, 35], [371, 38, 361, 36, "getCardAnimation"], [371, 54, 361, 52], [371, 55, 361, 53, "interpolationIndex"], [371, 73, 361, 71], [371, 75, 361, 73, "current"], [371, 82, 361, 80], [371, 84, 361, 82, "next"], [371, 88, 361, 86], [371, 90, 361, 88, "layout"], [371, 96, 361, 94], [371, 98, 361, 96, "insets"], [371, 104, 361, 102], [371, 105, 361, 103, "top"], [371, 108, 361, 106], [371, 110, 361, 108, "insets"], [371, 116, 361, 114], [371, 117, 361, 115, "right"], [371, 122, 361, 120], [371, 124, 361, 122, "insets"], [371, 130, 361, 128], [371, 131, 361, 129, "bottom"], [371, 137, 361, 135], [371, 139, 361, 137, "insets"], [371, 145, 361, 143], [371, 146, 361, 144, "left"], [371, 150, 361, 148], [371, 151, 361, 149], [372, 8, 362, 4], [372, 12, 362, 10, "interpolatedStyle"], [372, 29, 362, 27], [372, 32, 362, 30], [372, 36, 362, 34], [372, 37, 362, 35, "getInterpolatedStyle"], [372, 57, 362, 55], [372, 58, 362, 56, "styleInterpolator"], [372, 75, 362, 73], [372, 77, 362, 75, "interpolationProps"], [372, 95, 362, 93], [372, 96, 362, 94], [373, 8, 363, 4], [373, 12, 364, 6, "containerStyle"], [373, 26, 364, 20], [373, 29, 368, 8, "interpolatedStyle"], [373, 46, 368, 25], [373, 47, 364, 6, "containerStyle"], [373, 61, 364, 20], [374, 10, 365, 6, "cardStyle"], [374, 19, 365, 15], [374, 22, 368, 8, "interpolatedStyle"], [374, 39, 368, 25], [374, 40, 365, 6, "cardStyle"], [374, 49, 365, 15], [375, 10, 366, 6, "overlayStyle"], [375, 22, 366, 18], [375, 25, 368, 8, "interpolatedStyle"], [375, 42, 368, 25], [375, 43, 366, 6, "overlayStyle"], [375, 55, 366, 18], [376, 10, 367, 6, "shadowStyle"], [376, 21, 367, 17], [376, 24, 368, 8, "interpolatedStyle"], [376, 41, 368, 25], [376, 42, 367, 6, "shadowStyle"], [376, 53, 367, 17], [377, 8, 369, 4], [377, 12, 369, 10, "handleGestureEvent"], [377, 30, 369, 28], [377, 33, 369, 31, "gestureEnabled"], [377, 47, 369, 45], [377, 50, 369, 48, "Animated"], [377, 71, 369, 56], [377, 72, 369, 57, "event"], [377, 77, 369, 62], [377, 78, 369, 63], [377, 79, 369, 64], [378, 10, 370, 6, "nativeEvent"], [378, 21, 370, 17], [378, 23, 370, 19, "gestureDirection"], [378, 39, 370, 35], [378, 44, 370, 40], [378, 54, 370, 50], [378, 58, 370, 54, "gestureDirection"], [378, 74, 370, 70], [378, 79, 370, 75], [378, 98, 370, 94], [378, 101, 370, 97], [379, 12, 371, 8, "translationY"], [379, 24, 371, 20], [379, 26, 371, 22, "gesture"], [380, 10, 372, 6], [380, 11, 372, 7], [380, 14, 372, 10], [381, 12, 373, 8, "translationX"], [381, 24, 373, 20], [381, 26, 373, 22, "gesture"], [382, 10, 374, 6], [383, 8, 375, 4], [383, 9, 375, 5], [383, 10, 375, 6], [383, 12, 375, 8], [384, 10, 376, 6, "useNativeDriver"], [385, 8, 377, 4], [385, 9, 377, 5], [385, 10, 377, 6], [385, 13, 377, 9, "undefined"], [385, 22, 377, 18], [386, 8, 378, 4], [386, 12, 378, 4, "_StyleSheet$flatten"], [386, 31, 378, 4], [386, 34, 380, 8, "StyleSheet"], [386, 57, 380, 18], [386, 58, 380, 19, "flatten"], [386, 65, 380, 26], [386, 66, 380, 27, "contentStyle"], [386, 78, 380, 39], [386, 82, 380, 43], [386, 83, 380, 44], [386, 84, 380, 45], [386, 85, 380, 46], [387, 10, 379, 6, "backgroundColor"], [387, 25, 379, 21], [387, 28, 379, 21, "_StyleSheet$flatten"], [387, 47, 379, 21], [387, 48, 379, 6, "backgroundColor"], [387, 63, 379, 21], [388, 8, 381, 4], [388, 12, 381, 10, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [388, 25, 381, 23], [388, 28, 381, 26], [388, 35, 381, 33, "backgroundColor"], [388, 50, 381, 48], [388, 55, 381, 53], [388, 63, 381, 61], [388, 66, 381, 64], [388, 70, 381, 64, "Color"], [388, 84, 381, 69], [388, 86, 381, 70, "backgroundColor"], [388, 101, 381, 85], [388, 102, 381, 86], [388, 103, 381, 87, "alpha"], [388, 108, 381, 92], [388, 109, 381, 93], [388, 110, 381, 94], [388, 115, 381, 99], [388, 116, 381, 100], [388, 119, 381, 103], [388, 124, 381, 108], [389, 8, 382, 4], [389, 15, 382, 11], [389, 28, 382, 24], [389, 32, 382, 24, "_jsxs"], [389, 48, 382, 29], [389, 50, 382, 30, "CardAnimationContext"], [389, 92, 382, 50], [389, 93, 382, 51, "Provider"], [389, 101, 382, 59], [389, 103, 382, 61], [390, 10, 383, 6, "value"], [390, 15, 383, 11], [390, 17, 383, 13, "interpolationProps"], [390, 35, 383, 31], [391, 10, 384, 6, "children"], [391, 18, 384, 14], [391, 20, 384, 16], [391, 21, 384, 17], [391, 34, 384, 30], [391, 38, 384, 30, "_jsx"], [391, 53, 384, 34], [391, 55, 384, 35, "Animated"], [391, 76, 384, 43], [391, 77, 384, 44, "View"], [391, 81, 384, 48], [391, 83, 384, 50], [392, 12, 385, 8, "style"], [392, 17, 385, 13], [392, 19, 385, 15], [393, 14, 386, 10], [394, 14, 387, 10], [395, 14, 388, 10], [396, 14, 389, 10], [397, 14, 390, 10, "opacity"], [397, 21, 390, 17], [397, 23, 390, 19, "current"], [398, 12, 391, 8], [399, 12, 392, 8], [400, 12, 392, 8], [402, 12, 394, 8, "collapsable"], [402, 23, 394, 19], [402, 25, 394, 21], [403, 10, 395, 6], [403, 11, 395, 7], [403, 12, 395, 8], [403, 14, 395, 10], [403, 27, 395, 23], [403, 31, 395, 23, "_jsxs"], [403, 47, 395, 28], [403, 49, 395, 29, "View"], [403, 66, 395, 33], [403, 68, 395, 35], [404, 12, 396, 8, "pointerEvents"], [404, 25, 396, 21], [404, 27, 396, 23], [405, 12, 397, 8], [406, 12, 398, 8], [407, 12, 398, 8], [409, 12, 400, 8, "collapsable"], [409, 23, 400, 19], [409, 25, 400, 21], [409, 30, 400, 26], [410, 12, 401, 8], [410, 15, 401, 11, "rest"], [410, 19, 401, 15], [411, 12, 402, 8, "children"], [411, 20, 402, 16], [411, 22, 402, 18], [411, 23, 402, 19, "overlayEnabled"], [411, 37, 402, 33], [411, 40, 402, 36], [411, 53, 402, 49], [411, 57, 402, 49, "_jsx"], [411, 72, 402, 53], [411, 74, 402, 54, "View"], [411, 91, 402, 58], [411, 93, 402, 60], [412, 14, 403, 10, "pointerEvents"], [412, 27, 403, 23], [412, 29, 403, 25], [412, 39, 403, 35], [413, 14, 404, 10, "style"], [413, 19, 404, 15], [413, 21, 404, 17, "StyleSheet"], [413, 44, 404, 27], [413, 45, 404, 28, "absoluteFill"], [413, 57, 404, 40], [414, 14, 405, 10, "children"], [414, 22, 405, 18], [414, 24, 405, 20, "overlay"], [414, 31, 405, 27], [414, 32, 405, 28], [415, 16, 406, 12, "style"], [415, 21, 406, 17], [415, 23, 406, 19, "overlayStyle"], [416, 14, 407, 10], [416, 15, 407, 11], [417, 12, 408, 8], [417, 13, 408, 9], [417, 14, 408, 10], [417, 17, 408, 13], [417, 21, 408, 17], [417, 23, 408, 19], [417, 36, 408, 32], [417, 40, 408, 32, "_jsx"], [417, 55, 408, 36], [417, 57, 408, 37, "Animated"], [417, 78, 408, 45], [417, 79, 408, 46, "View"], [417, 83, 408, 50], [417, 85, 408, 52], [418, 14, 409, 10, "style"], [418, 19, 409, 15], [418, 21, 409, 17], [418, 22, 409, 18, "styles"], [418, 28, 409, 24], [418, 29, 409, 25, "container"], [418, 38, 409, 34], [418, 40, 409, 36, "containerStyle"], [418, 54, 409, 50], [418, 56, 409, 52, "customContainerStyle"], [418, 76, 409, 72], [418, 77, 409, 73], [419, 14, 410, 10, "pointerEvents"], [419, 27, 410, 23], [419, 29, 410, 25], [419, 39, 410, 35], [420, 14, 411, 10, "children"], [420, 22, 411, 18], [420, 24, 411, 20], [420, 37, 411, 33], [420, 41, 411, 33, "_jsx"], [420, 56, 411, 37], [420, 58, 411, 38, "PanGestureHandler"], [420, 91, 411, 55], [420, 93, 411, 57], [421, 16, 412, 12, "enabled"], [421, 23, 412, 19], [421, 25, 412, 21, "layout"], [421, 31, 412, 27], [421, 32, 412, 28, "width"], [421, 37, 412, 33], [421, 42, 412, 38], [421, 43, 412, 39], [421, 47, 412, 43, "gestureEnabled"], [421, 61, 412, 57], [422, 16, 413, 12, "onGestureEvent"], [422, 30, 413, 26], [422, 32, 413, 28, "handleGestureEvent"], [422, 50, 413, 46], [423, 16, 414, 12, "onHandlerStateChange"], [423, 36, 414, 32], [423, 38, 414, 34], [423, 42, 414, 38], [423, 43, 414, 39, "handleGestureStateChange"], [423, 67, 414, 63], [424, 16, 415, 12], [424, 19, 415, 15], [424, 23, 415, 19], [424, 24, 415, 20, "gestureActivationCriteria"], [424, 49, 415, 45], [424, 50, 415, 46], [424, 51, 415, 47], [425, 16, 416, 12, "children"], [425, 24, 416, 20], [425, 26, 416, 22], [425, 39, 416, 35], [425, 43, 416, 35, "_jsxs"], [425, 59, 416, 40], [425, 61, 416, 41, "Animated"], [425, 82, 416, 49], [425, 83, 416, 50, "View"], [425, 87, 416, 54], [425, 89, 416, 56], [426, 18, 417, 14, "needsOffscreenAlphaCompositing"], [426, 48, 417, 44], [426, 50, 417, 46, "hasOpacityStyle"], [426, 65, 417, 61], [426, 66, 417, 62, "cardStyle"], [426, 75, 417, 71], [426, 76, 417, 72], [427, 18, 418, 14, "style"], [427, 23, 418, 19], [427, 25, 418, 21], [427, 26, 418, 22, "styles"], [427, 32, 418, 28], [427, 33, 418, 29, "container"], [427, 42, 418, 38], [427, 44, 418, 40, "cardStyle"], [427, 53, 418, 49], [427, 54, 418, 50], [428, 18, 419, 14, "children"], [428, 26, 419, 22], [428, 28, 419, 24], [428, 29, 419, 25, "shadowEnabled"], [428, 42, 419, 38], [428, 46, 419, 42, "shadowStyle"], [428, 57, 419, 53], [428, 61, 419, 57], [428, 62, 419, 58, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [428, 75, 419, 71], [428, 78, 419, 74], [428, 91, 419, 87], [428, 95, 419, 87, "_jsx"], [428, 110, 419, 91], [428, 112, 419, 92, "Animated"], [428, 133, 419, 100], [428, 134, 419, 101, "View"], [428, 138, 419, 105], [428, 140, 419, 107], [429, 20, 420, 16, "style"], [429, 25, 420, 21], [429, 27, 420, 23], [429, 28, 420, 24, "styles"], [429, 34, 420, 30], [429, 35, 420, 31, "shadow"], [429, 41, 420, 37], [429, 43, 420, 39, "gestureDirection"], [429, 59, 420, 55], [429, 64, 420, 60], [429, 76, 420, 72], [429, 79, 420, 75], [429, 80, 420, 76, "styles"], [429, 86, 420, 82], [429, 87, 420, 83, "shadowHorizontal"], [429, 103, 420, 99], [429, 105, 420, 101, "styles"], [429, 111, 420, 107], [429, 112, 420, 108, "shadowStart"], [429, 123, 420, 119], [429, 124, 420, 120], [429, 127, 420, 123, "gestureDirection"], [429, 143, 420, 139], [429, 148, 420, 144], [429, 169, 420, 165], [429, 172, 420, 168], [429, 173, 420, 169, "styles"], [429, 179, 420, 175], [429, 180, 420, 176, "shadowHorizontal"], [429, 196, 420, 192], [429, 198, 420, 194, "styles"], [429, 204, 420, 200], [429, 205, 420, 201, "shadowEnd"], [429, 214, 420, 210], [429, 215, 420, 211], [429, 218, 420, 214, "gestureDirection"], [429, 234, 420, 230], [429, 239, 420, 235], [429, 249, 420, 245], [429, 252, 420, 248], [429, 253, 420, 249, "styles"], [429, 259, 420, 255], [429, 260, 420, 256, "shadowVertical"], [429, 274, 420, 270], [429, 276, 420, 272, "styles"], [429, 282, 420, 278], [429, 283, 420, 279, "shadowTop"], [429, 292, 420, 288], [429, 293, 420, 289], [429, 296, 420, 292], [429, 297, 420, 293, "styles"], [429, 303, 420, 299], [429, 304, 420, 300, "shadowVertical"], [429, 318, 420, 314], [429, 320, 420, 316, "styles"], [429, 326, 420, 322], [429, 327, 420, 323, "shadowBottom"], [429, 339, 420, 335], [429, 340, 420, 336], [429, 342, 420, 338], [430, 22, 421, 18, "backgroundColor"], [431, 20, 422, 16], [431, 21, 422, 17], [431, 23, 422, 19, "shadowStyle"], [431, 34, 422, 30], [431, 35, 422, 31], [432, 20, 423, 16, "pointerEvents"], [432, 33, 423, 29], [432, 35, 423, 31], [433, 18, 424, 14], [433, 19, 424, 15], [433, 20, 424, 16], [433, 23, 424, 19], [433, 27, 424, 23], [433, 29, 424, 25], [433, 42, 424, 38], [433, 46, 424, 38, "_jsx"], [433, 61, 424, 42], [433, 63, 424, 43, "CardSheet"], [433, 83, 424, 52], [433, 85, 424, 54], [434, 20, 425, 16, "ref"], [434, 23, 425, 19], [434, 25, 425, 21], [434, 29, 425, 25], [434, 30, 425, 26, "ref"], [434, 33, 425, 29], [435, 20, 426, 16, "enabled"], [435, 27, 426, 23], [435, 29, 426, 25, "pageOverflowEnabled"], [435, 48, 426, 44], [436, 20, 427, 16, "layout"], [436, 26, 427, 22], [436, 28, 427, 24, "layout"], [436, 34, 427, 30], [437, 20, 428, 16, "style"], [437, 25, 428, 21], [437, 27, 428, 23, "contentStyle"], [437, 39, 428, 35], [438, 20, 429, 16, "children"], [438, 28, 429, 24], [438, 30, 429, 26, "children"], [439, 18, 430, 14], [439, 19, 430, 15], [439, 20, 430, 16], [440, 16, 431, 12], [440, 17, 431, 13], [441, 14, 432, 10], [441, 15, 432, 11], [442, 12, 433, 8], [442, 13, 433, 9], [442, 14, 433, 10], [443, 10, 434, 6], [443, 11, 434, 7], [443, 12, 434, 8], [444, 8, 435, 4], [444, 9, 435, 5], [444, 10, 435, 6], [445, 6, 436, 2], [446, 4, 436, 3], [447, 2, 436, 3], [447, 4, 31, 26, "React"], [447, 9, 31, 31], [447, 10, 31, 32, "Component"], [447, 19, 31, 41], [448, 2, 31, 13, "Card"], [448, 6, 31, 17], [448, 7, 32, 9, "defaultProps"], [448, 19, 32, 21], [448, 22, 32, 24], [449, 4, 33, 4, "shadowEnabled"], [449, 17, 33, 17], [449, 19, 33, 19], [449, 24, 33, 24], [450, 4, 34, 4, "gestureEnabled"], [450, 18, 34, 18], [450, 20, 34, 20], [450, 24, 34, 24], [451, 4, 35, 4, "gestureVelocityImpact"], [451, 25, 35, 25], [451, 27, 35, 27, "GESTURE_VELOCITY_IMPACT"], [451, 50, 35, 50], [452, 4, 36, 4, "overlay"], [452, 11, 36, 11], [452, 13, 36, 13, "_ref5"], [452, 18, 36, 13], [453, 6, 36, 13], [453, 10, 37, 6, "style"], [453, 15, 37, 11], [453, 18, 37, 11, "_ref5"], [453, 23, 37, 11], [453, 24, 37, 6, "style"], [453, 29, 37, 11], [454, 6, 37, 11], [454, 13, 38, 10, "style"], [454, 18, 38, 15], [454, 21, 38, 18], [454, 34, 38, 31], [454, 38, 38, 31, "_jsx"], [454, 53, 38, 35], [454, 55, 38, 36, "Animated"], [454, 76, 38, 44], [454, 77, 38, 45, "View"], [454, 81, 38, 49], [454, 83, 38, 51], [455, 8, 39, 6, "pointerEvents"], [455, 21, 39, 19], [455, 23, 39, 21], [455, 29, 39, 27], [456, 8, 40, 6, "style"], [456, 13, 40, 11], [456, 15, 40, 13], [456, 16, 40, 14, "styles"], [456, 22, 40, 20], [456, 23, 40, 21, "overlay"], [456, 30, 40, 28], [456, 32, 40, 30, "style"], [456, 37, 40, 35], [457, 6, 41, 4], [457, 7, 41, 5], [457, 8, 41, 6], [457, 11, 41, 9], [457, 15, 41, 13], [458, 4, 41, 13], [459, 2, 42, 2], [459, 3, 42, 3], [460, 2, 438, 0], [460, 6, 438, 6, "styles"], [460, 12, 438, 12], [460, 15, 438, 15, "StyleSheet"], [460, 38, 438, 25], [460, 39, 438, 26, "create"], [460, 45, 438, 32], [460, 46, 438, 33], [461, 4, 439, 2, "container"], [461, 13, 439, 11], [461, 15, 439, 13], [462, 6, 440, 4, "flex"], [462, 10, 440, 8], [462, 12, 440, 10], [463, 4, 441, 2], [463, 5, 441, 3], [464, 4, 442, 2, "overlay"], [464, 11, 442, 9], [464, 13, 442, 11], [465, 6, 443, 4, "flex"], [465, 10, 443, 8], [465, 12, 443, 10], [465, 13, 443, 11], [466, 6, 444, 4, "backgroundColor"], [466, 21, 444, 19], [466, 23, 444, 21], [467, 4, 445, 2], [467, 5, 445, 3], [468, 4, 446, 2, "shadow"], [468, 10, 446, 8], [468, 12, 446, 10], [469, 6, 447, 4, "position"], [469, 14, 447, 12], [469, 16, 447, 14], [470, 4, 448, 2], [470, 5, 448, 3], [471, 4, 449, 2, "shadowHorizontal"], [471, 20, 449, 18], [471, 22, 449, 20], [472, 6, 450, 4, "top"], [472, 9, 450, 7], [472, 11, 450, 9], [472, 12, 450, 10], [473, 6, 451, 4, "bottom"], [473, 12, 451, 10], [473, 14, 451, 12], [473, 15, 451, 13], [474, 6, 452, 4, "width"], [474, 11, 452, 9], [474, 13, 452, 11], [474, 14, 452, 12], [475, 6, 453, 4], [475, 9, 453, 7], [475, 13, 453, 7, "getShadowStyle"], [475, 43, 453, 21], [475, 45, 453, 22], [476, 8, 454, 6, "offset"], [476, 14, 454, 12], [476, 16, 454, 14], [477, 10, 455, 8, "width"], [477, 15, 455, 13], [477, 17, 455, 15], [477, 18, 455, 16], [477, 19, 455, 17], [478, 10, 456, 8, "height"], [478, 16, 456, 14], [478, 18, 456, 16], [479, 8, 457, 6], [479, 9, 457, 7], [480, 8, 458, 6, "radius"], [480, 14, 458, 12], [480, 16, 458, 14], [480, 17, 458, 15], [481, 8, 459, 6, "opacity"], [481, 15, 459, 13], [481, 17, 459, 15], [482, 6, 460, 4], [482, 7, 460, 5], [483, 4, 461, 2], [483, 5, 461, 3], [484, 4, 462, 2, "shadowStart"], [484, 15, 462, 13], [484, 17, 462, 15], [485, 6, 463, 4, "start"], [485, 11, 463, 9], [485, 13, 463, 11], [486, 4, 464, 2], [486, 5, 464, 3], [487, 4, 465, 2, "shadowEnd"], [487, 13, 465, 11], [487, 15, 465, 13], [488, 6, 466, 4, "end"], [488, 9, 466, 7], [488, 11, 466, 9], [489, 4, 467, 2], [489, 5, 467, 3], [490, 4, 468, 2, "shadowVertical"], [490, 18, 468, 16], [490, 20, 468, 18], [491, 6, 469, 4, "start"], [491, 11, 469, 9], [491, 13, 469, 11], [491, 14, 469, 12], [492, 6, 470, 4, "end"], [492, 9, 470, 7], [492, 11, 470, 9], [492, 12, 470, 10], [493, 6, 471, 4, "height"], [493, 12, 471, 10], [493, 14, 471, 12], [493, 15, 471, 13], [494, 6, 472, 4], [494, 9, 472, 7], [494, 13, 472, 7, "getShadowStyle"], [494, 43, 472, 21], [494, 45, 472, 22], [495, 8, 473, 6, "offset"], [495, 14, 473, 12], [495, 16, 473, 14], [496, 10, 474, 8, "width"], [496, 15, 474, 13], [496, 17, 474, 15], [496, 18, 474, 16], [497, 10, 475, 8, "height"], [497, 16, 475, 14], [497, 18, 475, 16], [497, 19, 475, 17], [498, 8, 476, 6], [498, 9, 476, 7], [499, 8, 477, 6, "radius"], [499, 14, 477, 12], [499, 16, 477, 14], [499, 17, 477, 15], [500, 8, 478, 6, "opacity"], [500, 15, 478, 13], [500, 17, 478, 15], [501, 6, 479, 4], [501, 7, 479, 5], [502, 4, 480, 2], [502, 5, 480, 3], [503, 4, 481, 2, "shadowTop"], [503, 13, 481, 11], [503, 15, 481, 13], [504, 6, 482, 4, "top"], [504, 9, 482, 7], [504, 11, 482, 9], [505, 4, 483, 2], [505, 5, 483, 3], [506, 4, 484, 2, "shadowBottom"], [506, 16, 484, 14], [506, 18, 484, 16], [507, 6, 485, 4, "bottom"], [507, 12, 485, 10], [507, 14, 485, 12], [508, 4, 486, 2], [509, 2, 487, 0], [509, 3, 487, 1], [509, 4, 487, 2], [510, 0, 487, 3], [510, 3]], "functionMap": {"names": ["<global>", "hasOpacityStyle", "Card", "defaultProps.overlay", "componentDidMount", "componentDidUpdate", "componentWillUnmount", "animate", "animation.start$argument_0", "getAnimateToValue", "setPointerEventsEnabled", "handleStartInteraction", "handleEndInteraction", "handleGestureStateChange", "setTimeout$argument_0", "memoize$argument_0", "gestureActivationCriteria", "render"], "mappings": "AAA;wBCuB;CDM;OEC;aCK;aDK;EEE;GFO;EGC;GHwC;EIC;GJI;YKS;aCgC;KDgB;GLC;sBOC;GPW;4BQC;GRG;2BSC;GTI;yBUC;GVK;6BWC;qDCuD;aDM;GXM;iCaG,8Db;6BaG;IboB;EcC;GdkD;EeE;Gf4G;CFC"}}, "type": "js/module"}]}