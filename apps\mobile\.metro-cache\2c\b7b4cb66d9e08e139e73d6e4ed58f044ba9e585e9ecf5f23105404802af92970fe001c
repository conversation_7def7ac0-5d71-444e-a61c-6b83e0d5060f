{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 93, "index": 108}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 77, "column": 0, "index": 2209}, "end": {"line": 82, "column": 2, "index": 2329}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/ViewConfigIgnore", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 77, "column": 0, "index": 2209}, "end": {"line": 82, "column": 2, "index": 2329}}], "key": "IAMNY1s5722b4GYH12DgGSx1R70=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 77, "column": 0, "index": 2209}, "end": {"line": 82, "column": 2, "index": 2329}}, {"start": {"line": 77, "column": 0, "index": 2209}, "end": {"line": 82, "column": 2, "index": 2329}}, {"start": {"line": 77, "column": 0, "index": 2209}, "end": {"line": 82, "column": 2, "index": 2329}}, {"start": {"line": 77, "column": 0, "index": 2209}, "end": {"line": 82, "column": 2, "index": 2329}}, {"start": {"line": 77, "column": 0, "index": 2209}, "end": {"line": 82, "column": 2, "index": 2329}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  // eslint-disable-next-line @typescript-eslint/ban-types\n\n  // eslint-disable-next-line @typescript-eslint/ban-types\n\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var _require = require(_dependencyMap[3], \"react-native/Libraries/NativeComponent/ViewConfigIgnore\"),\n    ConditionallyIgnoredEventHandlers = _require.ConditionallyIgnoredEventHandlers;\n  var nativeComponentName = 'RNSScreenStackHeaderConfig';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSScreenStackHeaderConfig\",\n    directEventTypes: {\n      topAttached: {\n        registrationName: \"onAttached\"\n      },\n      topDetached: {\n        registrationName: \"onDetached\"\n      }\n    },\n    validAttributes: {\n      backgroundColor: {\n        process: require(_dependencyMap[4], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      backTitle: true,\n      backTitleFontFamily: true,\n      backTitleFontSize: true,\n      backTitleVisible: true,\n      color: {\n        process: require(_dependencyMap[4], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      direction: true,\n      hidden: true,\n      hideShadow: true,\n      largeTitle: true,\n      largeTitleFontFamily: true,\n      largeTitleFontSize: true,\n      largeTitleFontWeight: true,\n      largeTitleBackgroundColor: {\n        process: require(_dependencyMap[4], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      largeTitleHideShadow: true,\n      largeTitleColor: {\n        process: require(_dependencyMap[4], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      translucent: true,\n      title: true,\n      titleFontFamily: true,\n      titleFontSize: true,\n      titleFontWeight: true,\n      titleColor: {\n        process: require(_dependencyMap[4], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      disableBackButtonMenu: true,\n      backButtonDisplayMode: true,\n      hideBackButton: true,\n      backButtonInCustomView: true,\n      blurEffect: true,\n      topInsetEnabled: true,\n      ...ConditionallyIgnoredEventHandlers({\n        onAttached: true,\n        onDetached: true\n      })\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 74, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [8, 20, 1, 13, "exports"], [8, 27, 1, 13], [8, 28, 1, 13, "__INTERNAL_VIEW_CONFIG"], [8, 50, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_codegenNativeComponent"], [9, 29, 3, 0], [9, 32, 3, 0, "_interopRequireDefault"], [9, 54, 3, 0], [9, 55, 3, 0, "require"], [9, 62, 3, 0], [9, 63, 3, 0, "_dependencyMap"], [9, 77, 3, 0], [10, 2, 13, 0], [12, 2, 15, 0], [14, 2, 77, 0], [14, 6, 77, 0, "NativeComponentRegistry"], [14, 29, 82, 2], [14, 32, 77, 0, "require"], [14, 39, 82, 2], [14, 40, 82, 2, "_dependencyMap"], [14, 54, 82, 2], [14, 123, 82, 1], [14, 124, 82, 2], [15, 2, 77, 0], [15, 6, 77, 0, "_require"], [15, 14, 77, 0], [15, 17, 77, 0, "require"], [15, 24, 82, 2], [15, 25, 82, 2, "_dependencyMap"], [15, 39, 82, 2], [15, 101, 82, 1], [15, 102, 82, 2], [16, 4, 77, 0, "ConditionallyIgnoredEventHandlers"], [16, 37, 82, 2], [16, 40, 82, 2, "_require"], [16, 48, 82, 2], [16, 49, 77, 0, "ConditionallyIgnoredEventHandlers"], [16, 82, 82, 2], [17, 2, 77, 0], [17, 6, 77, 0, "nativeComponentName"], [17, 25, 82, 2], [17, 28, 77, 0], [17, 56, 82, 2], [18, 2, 77, 0], [18, 6, 77, 0, "__INTERNAL_VIEW_CONFIG"], [18, 28, 82, 2], [18, 31, 82, 2, "exports"], [18, 38, 82, 2], [18, 39, 82, 2, "__INTERNAL_VIEW_CONFIG"], [18, 61, 82, 2], [18, 64, 77, 0], [19, 4, 77, 0, "uiViewClassName"], [19, 19, 82, 2], [19, 21, 77, 0], [19, 49, 82, 2], [20, 4, 77, 0, "directEventTypes"], [20, 20, 82, 2], [20, 22, 77, 0], [21, 6, 77, 0, "topAttached"], [21, 17, 82, 2], [21, 19, 77, 0], [22, 8, 77, 0, "registrationName"], [22, 24, 82, 2], [22, 26, 77, 0], [23, 6, 82, 1], [23, 7, 82, 2], [24, 6, 77, 0, "topDetached"], [24, 17, 82, 2], [24, 19, 77, 0], [25, 8, 77, 0, "registrationName"], [25, 24, 82, 2], [25, 26, 77, 0], [26, 6, 82, 1], [27, 4, 82, 1], [27, 5, 82, 2], [28, 4, 77, 0, "validAttributes"], [28, 19, 82, 2], [28, 21, 77, 0], [29, 6, 77, 0, "backgroundColor"], [29, 21, 82, 2], [29, 23, 77, 0], [30, 8, 77, 0, "process"], [30, 15, 82, 2], [30, 17, 77, 0, "require"], [30, 24, 82, 2], [30, 25, 82, 2, "_dependencyMap"], [30, 39, 82, 2], [30, 92, 82, 1], [30, 93, 82, 2], [30, 94, 77, 0, "default"], [31, 6, 82, 1], [31, 7, 82, 2], [32, 6, 77, 0, "backTitle"], [32, 15, 82, 2], [32, 17, 77, 0], [32, 21, 82, 2], [33, 6, 77, 0, "backTitleFontFamily"], [33, 25, 82, 2], [33, 27, 77, 0], [33, 31, 82, 2], [34, 6, 77, 0, "backTitleFontSize"], [34, 23, 82, 2], [34, 25, 77, 0], [34, 29, 82, 2], [35, 6, 77, 0, "backTitleVisible"], [35, 22, 82, 2], [35, 24, 77, 0], [35, 28, 82, 2], [36, 6, 77, 0, "color"], [36, 11, 82, 2], [36, 13, 77, 0], [37, 8, 77, 0, "process"], [37, 15, 82, 2], [37, 17, 77, 0, "require"], [37, 24, 82, 2], [37, 25, 82, 2, "_dependencyMap"], [37, 39, 82, 2], [37, 92, 82, 1], [37, 93, 82, 2], [37, 94, 77, 0, "default"], [38, 6, 82, 1], [38, 7, 82, 2], [39, 6, 77, 0, "direction"], [39, 15, 82, 2], [39, 17, 77, 0], [39, 21, 82, 2], [40, 6, 77, 0, "hidden"], [40, 12, 82, 2], [40, 14, 77, 0], [40, 18, 82, 2], [41, 6, 77, 0, "hideShadow"], [41, 16, 82, 2], [41, 18, 77, 0], [41, 22, 82, 2], [42, 6, 77, 0, "largeTitle"], [42, 16, 82, 2], [42, 18, 77, 0], [42, 22, 82, 2], [43, 6, 77, 0, "largeTitleFontFamily"], [43, 26, 82, 2], [43, 28, 77, 0], [43, 32, 82, 2], [44, 6, 77, 0, "largeTitleFontSize"], [44, 24, 82, 2], [44, 26, 77, 0], [44, 30, 82, 2], [45, 6, 77, 0, "largeTitleFontWeight"], [45, 26, 82, 2], [45, 28, 77, 0], [45, 32, 82, 2], [46, 6, 77, 0, "largeTitleBackgroundColor"], [46, 31, 82, 2], [46, 33, 77, 0], [47, 8, 77, 0, "process"], [47, 15, 82, 2], [47, 17, 77, 0, "require"], [47, 24, 82, 2], [47, 25, 82, 2, "_dependencyMap"], [47, 39, 82, 2], [47, 92, 82, 1], [47, 93, 82, 2], [47, 94, 77, 0, "default"], [48, 6, 82, 1], [48, 7, 82, 2], [49, 6, 77, 0, "largeTitleHideShadow"], [49, 26, 82, 2], [49, 28, 77, 0], [49, 32, 82, 2], [50, 6, 77, 0, "largeTitleColor"], [50, 21, 82, 2], [50, 23, 77, 0], [51, 8, 77, 0, "process"], [51, 15, 82, 2], [51, 17, 77, 0, "require"], [51, 24, 82, 2], [51, 25, 82, 2, "_dependencyMap"], [51, 39, 82, 2], [51, 92, 82, 1], [51, 93, 82, 2], [51, 94, 77, 0, "default"], [52, 6, 82, 1], [52, 7, 82, 2], [53, 6, 77, 0, "translucent"], [53, 17, 82, 2], [53, 19, 77, 0], [53, 23, 82, 2], [54, 6, 77, 0, "title"], [54, 11, 82, 2], [54, 13, 77, 0], [54, 17, 82, 2], [55, 6, 77, 0, "titleFontFamily"], [55, 21, 82, 2], [55, 23, 77, 0], [55, 27, 82, 2], [56, 6, 77, 0, "titleFontSize"], [56, 19, 82, 2], [56, 21, 77, 0], [56, 25, 82, 2], [57, 6, 77, 0, "titleFontWeight"], [57, 21, 82, 2], [57, 23, 77, 0], [57, 27, 82, 2], [58, 6, 77, 0, "titleColor"], [58, 16, 82, 2], [58, 18, 77, 0], [59, 8, 77, 0, "process"], [59, 15, 82, 2], [59, 17, 77, 0, "require"], [59, 24, 82, 2], [59, 25, 82, 2, "_dependencyMap"], [59, 39, 82, 2], [59, 92, 82, 1], [59, 93, 82, 2], [59, 94, 77, 0, "default"], [60, 6, 82, 1], [60, 7, 82, 2], [61, 6, 77, 0, "disableBackButtonMenu"], [61, 27, 82, 2], [61, 29, 77, 0], [61, 33, 82, 2], [62, 6, 77, 0, "backButtonDisplayMode"], [62, 27, 82, 2], [62, 29, 77, 0], [62, 33, 82, 2], [63, 6, 77, 0, "hideBackButton"], [63, 20, 82, 2], [63, 22, 77, 0], [63, 26, 82, 2], [64, 6, 77, 0, "backButtonInCustomView"], [64, 28, 82, 2], [64, 30, 77, 0], [64, 34, 82, 2], [65, 6, 77, 0, "blurEffect"], [65, 16, 82, 2], [65, 18, 77, 0], [65, 22, 82, 2], [66, 6, 77, 0, "topInsetEnabled"], [66, 21, 82, 2], [66, 23, 77, 0], [66, 27, 82, 2], [67, 6, 77, 0], [67, 9, 77, 0, "ConditionallyIgnoredEventHandlers"], [67, 42, 82, 2], [67, 43, 77, 0], [68, 8, 77, 0, "onAttached"], [68, 18, 82, 2], [68, 20, 77, 0], [68, 24, 82, 2], [69, 8, 77, 0, "onDetached"], [69, 18, 82, 2], [69, 20, 77, 0], [70, 6, 82, 1], [71, 4, 82, 1], [72, 2, 82, 1], [72, 3, 82, 2], [73, 2, 82, 2], [73, 6, 82, 2, "_default"], [73, 14, 82, 2], [73, 17, 82, 2, "exports"], [73, 24, 82, 2], [73, 25, 82, 2, "default"], [73, 32, 82, 2], [73, 35, 77, 0, "NativeComponentRegistry"], [73, 58, 82, 2], [73, 59, 77, 0, "get"], [73, 62, 82, 2], [73, 63, 77, 0, "nativeComponentName"], [73, 82, 82, 2], [73, 84, 77, 0], [73, 90, 77, 0, "__INTERNAL_VIEW_CONFIG"], [73, 112, 82, 1], [73, 113, 82, 2], [74, 0, 82, 2], [74, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}