{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function mapWithSeparator(items, itemRenderer, spacerRenderer) {\n    var mapped = [];\n    if (items.length > 0) {\n      mapped.push(itemRenderer(items[0], 0, items));\n      for (var ii = 1; ii < items.length; ii++) {\n        mapped.push(spacer<PERSON>enderer(ii - 1), itemRenderer(items[ii], ii, items));\n      }\n    }\n    return mapped;\n  }\n  var _default = exports.default = mapWithSeparator;\n});", "lineCount": 19, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 13, 0], [8, 11, 13, 9, "mapWithSeparator"], [8, 27, 13, 25, "mapWithSeparator"], [8, 28, 14, 2, "items"], [8, 33, 14, 21], [8, 35, 15, 2, "itemRenderer"], [8, 47, 15, 72], [8, 49, 16, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 63, 16, 40], [8, 65, 17, 14], [9, 4, 18, 2], [9, 8, 18, 8, "mapped"], [9, 14, 18, 14], [9, 17, 18, 17], [9, 19, 18, 19], [10, 4, 19, 2], [10, 8, 19, 6, "items"], [10, 13, 19, 11], [10, 14, 19, 12, "length"], [10, 20, 19, 18], [10, 23, 19, 21], [10, 24, 19, 22], [10, 26, 19, 24], [11, 6, 20, 4, "mapped"], [11, 12, 20, 10], [11, 13, 20, 11, "push"], [11, 17, 20, 15], [11, 18, 20, 16, "itemRenderer"], [11, 30, 20, 28], [11, 31, 20, 29, "items"], [11, 36, 20, 34], [11, 37, 20, 35], [11, 38, 20, 36], [11, 39, 20, 37], [11, 41, 20, 39], [11, 42, 20, 40], [11, 44, 20, 42, "items"], [11, 49, 20, 47], [11, 50, 20, 48], [11, 51, 20, 49], [12, 6, 21, 4], [12, 11, 21, 9], [12, 15, 21, 13, "ii"], [12, 17, 21, 15], [12, 20, 21, 18], [12, 21, 21, 19], [12, 23, 21, 21, "ii"], [12, 25, 21, 23], [12, 28, 21, 26, "items"], [12, 33, 21, 31], [12, 34, 21, 32, "length"], [12, 40, 21, 38], [12, 42, 21, 40, "ii"], [12, 44, 21, 42], [12, 46, 21, 44], [12, 48, 21, 46], [13, 8, 22, 6, "mapped"], [13, 14, 22, 12], [13, 15, 22, 13, "push"], [13, 19, 22, 17], [13, 20, 22, 18, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [13, 34, 22, 32], [13, 35, 22, 33, "ii"], [13, 37, 22, 35], [13, 40, 22, 38], [13, 41, 22, 39], [13, 42, 22, 40], [13, 44, 22, 42, "itemRenderer"], [13, 56, 22, 54], [13, 57, 22, 55, "items"], [13, 62, 22, 60], [13, 63, 22, 61, "ii"], [13, 65, 22, 63], [13, 66, 22, 64], [13, 68, 22, 66, "ii"], [13, 70, 22, 68], [13, 72, 22, 70, "items"], [13, 77, 22, 75], [13, 78, 22, 76], [13, 79, 22, 77], [14, 6, 23, 4], [15, 4, 24, 2], [16, 4, 25, 2], [16, 11, 25, 9, "mapped"], [16, 17, 25, 15], [17, 2, 26, 0], [18, 2, 26, 1], [18, 6, 26, 1, "_default"], [18, 14, 26, 1], [18, 17, 26, 1, "exports"], [18, 24, 26, 1], [18, 25, 26, 1, "default"], [18, 32, 26, 1], [18, 35, 28, 15, "mapWithSeparator"], [18, 51, 28, 31], [19, 0, 28, 31], [19, 3]], "functionMap": {"names": ["<global>", "mapWithSeparator"], "mappings": "AAA;ACY;CDa"}}, "type": "js/module"}]}