{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/readOnlyError", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "tW9vjPH6bEH6zBBMaoehmzXhDcQ=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "../nodes/AnimatedColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 51}}], "key": "POxzKTLNRaHZiLfC97dJcrsUHew=", "exportNames": ["*"]}}, {"name": "./Animation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 36}}], "key": "IpfQWRKePvRqyvs8Q+vGFqyieVw=", "exportNames": ["*"]}}, {"name": "../Easing", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 53, "column": 19}, "end": {"line": 53, "column": 39}}], "key": "PFY8BT2lKU8PqqhdctcsX7xdwes=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _readOnlyError2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/readOnlyError\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[7], \"@babel/runtime/helpers/inherits\"));\n  var _AnimatedColor = _interopRequireDefault(require(_dependencyMap[8], \"../nodes/AnimatedColor\"));\n  var _Animation2 = _interopRequireDefault(require(_dependencyMap[9], \"./Animation\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var _easeInOut;\n  function easeInOut() {\n    if (!_easeInOut) {\n      var Easing = require(_dependencyMap[10], \"../Easing\").default;\n      _easeInOut = Easing.inOut(Easing.ease);\n    }\n    return _easeInOut;\n  }\n  var TimingAnimation = exports.default = /*#__PURE__*/function (_Animation) {\n    function TimingAnimation(config) {\n      var _this;\n      (0, _classCallCheck2.default)(this, TimingAnimation);\n      _this = _callSuper(this, TimingAnimation, [config]);\n      _this._toValue = config.toValue;\n      _this._easing = config.easing ?? easeInOut();\n      _this._duration = config.duration ?? 500;\n      _this._delay = config.delay ?? 0;\n      _this._platformConfig = config.platformConfig;\n      return _this;\n    }\n    (0, _inherits2.default)(TimingAnimation, _Animation);\n    return (0, _createClass2.default)(TimingAnimation, [{\n      key: \"__getNativeAnimationConfig\",\n      value: function __getNativeAnimationConfig() {\n        var frameDuration = 1000.0 / 60.0;\n        var frames = [];\n        var numFrames = Math.round(this._duration / frameDuration);\n        for (var frame = 0; frame < numFrames; frame++) {\n          frames.push(this._easing(frame / numFrames));\n        }\n        frames.push(this._easing(1));\n        return {\n          type: 'frames',\n          frames,\n          toValue: this._toValue,\n          iterations: this.__iterations,\n          platformConfig: this._platformConfig,\n          debugID: this.__getDebugID()\n        };\n      }\n    }, {\n      key: \"start\",\n      value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n        _superPropGet(TimingAnimation, \"start\", this, 3)([fromValue, onUpdate, onEnd, previousAnimation, animatedValue]);\n        this._fromValue = fromValue;\n        this._onUpdate = onUpdate;\n        var start = () => {\n          this._startTime = Date.now();\n          var useNativeDriver = this.__startAnimationIfNative(animatedValue);\n          if (!useNativeDriver) {\n            if (this._duration === 0) {\n              this._onUpdate(this._toValue);\n              this.__notifyAnimationEnd({\n                finished: true\n              });\n            } else {\n              this._animationFrame = requestAnimationFrame(() => this.onUpdate());\n            }\n          }\n        };\n        if (this._delay) {\n          this._timeout = setTimeout(start, this._delay);\n        } else {\n          start();\n        }\n      }\n    }, {\n      key: \"onUpdate\",\n      value: function onUpdate() {\n        var now = Date.now();\n        if (now >= this._startTime + this._duration) {\n          if (this._duration === 0) {\n            this._onUpdate(this._toValue);\n          } else {\n            this._onUpdate(this._fromValue + this._easing(1) * (this._toValue - this._fromValue));\n          }\n          this.__notifyAnimationEnd({\n            finished: true\n          });\n          return;\n        }\n        this._onUpdate(this._fromValue + this._easing((now - this._startTime) / this._duration) * (this._toValue - this._fromValue));\n        if (this.__active) {\n          this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n        }\n      }\n    }, {\n      key: \"stop\",\n      value: function stop() {\n        _superPropGet(TimingAnimation, \"stop\", this, 3)([]);\n        clearTimeout(this._timeout);\n        if (this._animationFrame != null) {\n          global.cancelAnimationFrame(this._animationFrame);\n        }\n        this.__notifyAnimationEnd({\n          finished: false\n        });\n      }\n    }]);\n  }(_Animation2.default);\n});", "lineCount": 119, "map": [[14, 2, 18, 0], [14, 6, 18, 0, "_AnimatedColor"], [14, 20, 18, 0], [14, 23, 18, 0, "_interopRequireDefault"], [14, 45, 18, 0], [14, 46, 18, 0, "require"], [14, 53, 18, 0], [14, 54, 18, 0, "_dependencyMap"], [14, 68, 18, 0], [15, 2, 19, 0], [15, 6, 19, 0, "_Animation2"], [15, 17, 19, 0], [15, 20, 19, 0, "_interopRequireDefault"], [15, 42, 19, 0], [15, 43, 19, 0, "require"], [15, 50, 19, 0], [15, 51, 19, 0, "_dependencyMap"], [15, 65, 19, 0], [16, 2, 19, 36], [16, 11, 19, 36, "_callSuper"], [16, 22, 19, 36, "t"], [16, 23, 19, 36], [16, 25, 19, 36, "o"], [16, 26, 19, 36], [16, 28, 19, 36, "e"], [16, 29, 19, 36], [16, 40, 19, 36, "o"], [16, 41, 19, 36], [16, 48, 19, 36, "_getPrototypeOf2"], [16, 64, 19, 36], [16, 65, 19, 36, "default"], [16, 72, 19, 36], [16, 74, 19, 36, "o"], [16, 75, 19, 36], [16, 82, 19, 36, "_possibleConstructorReturn2"], [16, 109, 19, 36], [16, 110, 19, 36, "default"], [16, 117, 19, 36], [16, 119, 19, 36, "t"], [16, 120, 19, 36], [16, 122, 19, 36, "_isNativeReflectConstruct"], [16, 147, 19, 36], [16, 152, 19, 36, "Reflect"], [16, 159, 19, 36], [16, 160, 19, 36, "construct"], [16, 169, 19, 36], [16, 170, 19, 36, "o"], [16, 171, 19, 36], [16, 173, 19, 36, "e"], [16, 174, 19, 36], [16, 186, 19, 36, "_getPrototypeOf2"], [16, 202, 19, 36], [16, 203, 19, 36, "default"], [16, 210, 19, 36], [16, 212, 19, 36, "t"], [16, 213, 19, 36], [16, 215, 19, 36, "constructor"], [16, 226, 19, 36], [16, 230, 19, 36, "o"], [16, 231, 19, 36], [16, 232, 19, 36, "apply"], [16, 237, 19, 36], [16, 238, 19, 36, "t"], [16, 239, 19, 36], [16, 241, 19, 36, "e"], [16, 242, 19, 36], [17, 2, 19, 36], [17, 11, 19, 36, "_isNativeReflectConstruct"], [17, 37, 19, 36], [17, 51, 19, 36, "t"], [17, 52, 19, 36], [17, 56, 19, 36, "Boolean"], [17, 63, 19, 36], [17, 64, 19, 36, "prototype"], [17, 73, 19, 36], [17, 74, 19, 36, "valueOf"], [17, 81, 19, 36], [17, 82, 19, 36, "call"], [17, 86, 19, 36], [17, 87, 19, 36, "Reflect"], [17, 94, 19, 36], [17, 95, 19, 36, "construct"], [17, 104, 19, 36], [17, 105, 19, 36, "Boolean"], [17, 112, 19, 36], [17, 145, 19, 36, "t"], [17, 146, 19, 36], [17, 159, 19, 36, "_isNativeReflectConstruct"], [17, 184, 19, 36], [17, 196, 19, 36, "_isNativeReflectConstruct"], [17, 197, 19, 36], [17, 210, 19, 36, "t"], [17, 211, 19, 36], [18, 2, 19, 36], [18, 11, 19, 36, "_superPropGet"], [18, 25, 19, 36, "t"], [18, 26, 19, 36], [18, 28, 19, 36, "o"], [18, 29, 19, 36], [18, 31, 19, 36, "e"], [18, 32, 19, 36], [18, 34, 19, 36, "r"], [18, 35, 19, 36], [18, 43, 19, 36, "p"], [18, 44, 19, 36], [18, 51, 19, 36, "_get2"], [18, 56, 19, 36], [18, 57, 19, 36, "default"], [18, 64, 19, 36], [18, 70, 19, 36, "_getPrototypeOf2"], [18, 86, 19, 36], [18, 87, 19, 36, "default"], [18, 94, 19, 36], [18, 100, 19, 36, "r"], [18, 101, 19, 36], [18, 104, 19, 36, "t"], [18, 105, 19, 36], [18, 106, 19, 36, "prototype"], [18, 115, 19, 36], [18, 118, 19, 36, "t"], [18, 119, 19, 36], [18, 122, 19, 36, "o"], [18, 123, 19, 36], [18, 125, 19, 36, "e"], [18, 126, 19, 36], [18, 140, 19, 36, "r"], [18, 141, 19, 36], [18, 166, 19, 36, "p"], [18, 167, 19, 36], [18, 180, 19, 36, "t"], [18, 181, 19, 36], [18, 192, 19, 36, "p"], [18, 193, 19, 36], [18, 194, 19, 36, "apply"], [18, 199, 19, 36], [18, 200, 19, 36, "e"], [18, 201, 19, 36], [18, 203, 19, 36, "t"], [18, 204, 19, 36], [18, 211, 19, 36, "p"], [18, 212, 19, 36], [19, 2, 50, 0], [19, 6, 50, 4, "_easeInOut"], [19, 16, 50, 14], [20, 2, 51, 0], [20, 11, 51, 9, "easeInOut"], [20, 20, 51, 18, "easeInOut"], [20, 21, 51, 18], [20, 23, 51, 21], [21, 4, 52, 2], [21, 8, 52, 6], [21, 9, 52, 7, "_easeInOut"], [21, 19, 52, 17], [21, 21, 52, 19], [22, 6, 53, 4], [22, 10, 53, 10, "Easing"], [22, 16, 53, 16], [22, 19, 53, 19, "require"], [22, 26, 53, 26], [22, 27, 53, 26, "_dependencyMap"], [22, 41, 53, 26], [22, 58, 53, 38], [22, 59, 53, 39], [22, 60, 53, 40, "default"], [22, 67, 53, 47], [23, 6, 54, 4, "_easeInOut"], [23, 16, 54, 14], [23, 19, 54, 17, "Easing"], [23, 25, 54, 23], [23, 26, 54, 24, "inOut"], [23, 31, 54, 29], [23, 32, 54, 30, "Easing"], [23, 38, 54, 36], [23, 39, 54, 37, "ease"], [23, 43, 54, 41], [23, 44, 54, 42], [24, 4, 55, 2], [25, 4, 56, 2], [25, 11, 56, 9, "_easeInOut"], [25, 21, 56, 19], [26, 2, 57, 0], [27, 2, 57, 1], [27, 6, 59, 21, "TimingAnimation"], [27, 21, 59, 36], [27, 24, 59, 36, "exports"], [27, 31, 59, 36], [27, 32, 59, 36, "default"], [27, 39, 59, 36], [27, 65, 59, 36, "_Animation"], [27, 75, 59, 36], [28, 4, 71, 2], [28, 13, 71, 2, "TimingAnimation"], [28, 29, 71, 14, "config"], [28, 35, 71, 49], [28, 37, 71, 51], [29, 6, 71, 51], [29, 10, 71, 51, "_this"], [29, 15, 71, 51], [30, 6, 71, 51], [30, 10, 71, 51, "_classCallCheck2"], [30, 26, 71, 51], [30, 27, 71, 51, "default"], [30, 34, 71, 51], [30, 42, 71, 51, "TimingAnimation"], [30, 57, 71, 51], [31, 6, 72, 4, "_this"], [31, 11, 72, 4], [31, 14, 72, 4, "_callSuper"], [31, 24, 72, 4], [31, 31, 72, 4, "TimingAnimation"], [31, 46, 72, 4], [31, 49, 72, 10, "config"], [31, 55, 72, 16], [32, 6, 74, 4, "_this"], [32, 11, 74, 4], [32, 12, 74, 9, "_toValue"], [32, 20, 74, 17], [32, 23, 74, 20, "config"], [32, 29, 74, 26], [32, 30, 74, 27, "toValue"], [32, 37, 74, 34], [33, 6, 75, 4, "_this"], [33, 11, 75, 4], [33, 12, 75, 9, "_easing"], [33, 19, 75, 16], [33, 22, 75, 19, "config"], [33, 28, 75, 25], [33, 29, 75, 26, "easing"], [33, 35, 75, 32], [33, 39, 75, 36, "easeInOut"], [33, 48, 75, 45], [33, 49, 75, 46], [33, 50, 75, 47], [34, 6, 76, 4, "_this"], [34, 11, 76, 4], [34, 12, 76, 9, "_duration"], [34, 21, 76, 18], [34, 24, 76, 21, "config"], [34, 30, 76, 27], [34, 31, 76, 28, "duration"], [34, 39, 76, 36], [34, 43, 76, 40], [34, 46, 76, 43], [35, 6, 77, 4, "_this"], [35, 11, 77, 4], [35, 12, 77, 9, "_delay"], [35, 18, 77, 15], [35, 21, 77, 18, "config"], [35, 27, 77, 24], [35, 28, 77, 25, "delay"], [35, 33, 77, 30], [35, 37, 77, 34], [35, 38, 77, 35], [36, 6, 78, 4, "_this"], [36, 11, 78, 4], [36, 12, 78, 9, "_platformConfig"], [36, 27, 78, 24], [36, 30, 78, 27, "config"], [36, 36, 78, 33], [36, 37, 78, 34, "platformConfig"], [36, 51, 78, 48], [37, 6, 78, 49], [37, 13, 78, 49, "_this"], [37, 18, 78, 49], [38, 4, 79, 2], [39, 4, 79, 3], [39, 8, 79, 3, "_inherits2"], [39, 18, 79, 3], [39, 19, 79, 3, "default"], [39, 26, 79, 3], [39, 28, 79, 3, "TimingAnimation"], [39, 43, 79, 3], [39, 45, 79, 3, "_Animation"], [39, 55, 79, 3], [40, 4, 79, 3], [40, 15, 79, 3, "_createClass2"], [40, 28, 79, 3], [40, 29, 79, 3, "default"], [40, 36, 79, 3], [40, 38, 79, 3, "TimingAnimation"], [40, 53, 79, 3], [41, 6, 79, 3, "key"], [41, 9, 79, 3], [42, 6, 79, 3, "value"], [42, 11, 79, 3], [42, 13, 81, 2], [42, 22, 81, 2, "__getNativeAnimationConfig"], [42, 48, 81, 28, "__getNativeAnimationConfig"], [42, 49, 81, 28], [42, 51, 88, 5], [43, 8, 89, 4], [43, 12, 89, 10, "frameDuration"], [43, 25, 89, 23], [43, 28, 89, 26], [43, 34, 89, 32], [43, 37, 89, 35], [43, 41, 89, 39], [44, 8, 90, 4], [44, 12, 90, 10, "frames"], [44, 18, 90, 16], [44, 21, 90, 19], [44, 23, 90, 21], [45, 8, 91, 4], [45, 12, 91, 10, "numFrames"], [45, 21, 91, 19], [45, 24, 91, 22, "Math"], [45, 28, 91, 26], [45, 29, 91, 27, "round"], [45, 34, 91, 32], [45, 35, 91, 33], [45, 39, 91, 37], [45, 40, 91, 38, "_duration"], [45, 49, 91, 47], [45, 52, 91, 50, "frameDuration"], [45, 65, 91, 63], [45, 66, 91, 64], [46, 8, 92, 4], [46, 13, 92, 9], [46, 17, 92, 13, "frame"], [46, 22, 92, 18], [46, 25, 92, 21], [46, 26, 92, 22], [46, 28, 92, 24, "frame"], [46, 33, 92, 29], [46, 36, 92, 32, "numFrames"], [46, 45, 92, 41], [46, 47, 92, 43, "frame"], [46, 52, 92, 48], [46, 54, 92, 50], [46, 56, 92, 52], [47, 10, 93, 6, "frames"], [47, 16, 93, 12], [47, 17, 93, 13, "push"], [47, 21, 93, 17], [47, 22, 93, 18], [47, 26, 93, 22], [47, 27, 93, 23, "_easing"], [47, 34, 93, 30], [47, 35, 93, 31, "frame"], [47, 40, 93, 36], [47, 43, 93, 39, "numFrames"], [47, 52, 93, 48], [47, 53, 93, 49], [47, 54, 93, 50], [48, 8, 94, 4], [49, 8, 95, 4, "frames"], [49, 14, 95, 10], [49, 15, 95, 11, "push"], [49, 19, 95, 15], [49, 20, 95, 16], [49, 24, 95, 20], [49, 25, 95, 21, "_easing"], [49, 32, 95, 28], [49, 33, 95, 29], [49, 34, 95, 30], [49, 35, 95, 31], [49, 36, 95, 32], [50, 8, 96, 4], [50, 15, 96, 11], [51, 10, 97, 6, "type"], [51, 14, 97, 10], [51, 16, 97, 12], [51, 24, 97, 20], [52, 10, 98, 6, "frames"], [52, 16, 98, 12], [53, 10, 99, 6, "toValue"], [53, 17, 99, 13], [53, 19, 99, 15], [53, 23, 99, 19], [53, 24, 99, 20, "_toValue"], [53, 32, 99, 28], [54, 10, 100, 6, "iterations"], [54, 20, 100, 16], [54, 22, 100, 18], [54, 26, 100, 22], [54, 27, 100, 23, "__iterations"], [54, 39, 100, 35], [55, 10, 101, 6, "platformConfig"], [55, 24, 101, 20], [55, 26, 101, 22], [55, 30, 101, 26], [55, 31, 101, 27, "_platformConfig"], [55, 46, 101, 42], [56, 10, 102, 6, "debugID"], [56, 17, 102, 13], [56, 19, 102, 15], [56, 23, 102, 19], [56, 24, 102, 20, "__getDebugID"], [56, 36, 102, 32], [56, 37, 102, 33], [57, 8, 103, 4], [57, 9, 103, 5], [58, 6, 104, 2], [59, 4, 104, 3], [60, 6, 104, 3, "key"], [60, 9, 104, 3], [61, 6, 104, 3, "value"], [61, 11, 104, 3], [61, 13, 106, 2], [61, 22, 106, 2, "start"], [61, 27, 106, 7, "start"], [61, 28, 107, 4, "fromValue"], [61, 37, 107, 21], [61, 39, 108, 4, "onUpdate"], [61, 47, 108, 37], [61, 49, 109, 4, "onEnd"], [61, 54, 109, 23], [61, 56, 110, 4, "previousAnimation"], [61, 73, 110, 33], [61, 75, 111, 4, "animatedValue"], [61, 88, 111, 32], [61, 90, 112, 10], [62, 8, 113, 4, "_superPropGet"], [62, 21, 113, 4], [62, 22, 113, 4, "TimingAnimation"], [62, 37, 113, 4], [62, 58, 113, 16, "fromValue"], [62, 67, 113, 25], [62, 69, 113, 27, "onUpdate"], [62, 77, 113, 35], [62, 79, 113, 37, "onEnd"], [62, 84, 113, 42], [62, 86, 113, 44, "previousAnimation"], [62, 103, 113, 61], [62, 105, 113, 63, "animatedValue"], [62, 118, 113, 76], [63, 8, 115, 4], [63, 12, 115, 8], [63, 13, 115, 9, "_fromValue"], [63, 23, 115, 19], [63, 26, 115, 22, "fromValue"], [63, 35, 115, 31], [64, 8, 116, 4], [64, 12, 116, 8], [64, 13, 116, 9, "_onUpdate"], [64, 22, 116, 18], [64, 25, 116, 21, "onUpdate"], [64, 33, 116, 29], [65, 8, 118, 4], [65, 12, 118, 10, "start"], [65, 17, 118, 15], [65, 20, 118, 18, "start"], [65, 21, 118, 18], [65, 26, 118, 24], [66, 10, 119, 6], [66, 14, 119, 10], [66, 15, 119, 11, "_startTime"], [66, 25, 119, 21], [66, 28, 119, 24, "Date"], [66, 32, 119, 28], [66, 33, 119, 29, "now"], [66, 36, 119, 32], [66, 37, 119, 33], [66, 38, 119, 34], [67, 10, 121, 6], [67, 14, 121, 12, "useNativeDriver"], [67, 29, 121, 27], [67, 32, 121, 30], [67, 36, 121, 34], [67, 37, 121, 35, "__startAnimationIfNative"], [67, 61, 121, 59], [67, 62, 121, 60, "animatedValue"], [67, 75, 121, 73], [67, 76, 121, 74], [68, 10, 122, 6], [68, 14, 122, 10], [68, 15, 122, 11, "useNativeDriver"], [68, 30, 122, 26], [68, 32, 122, 28], [69, 12, 126, 8], [69, 16, 126, 12], [69, 20, 126, 16], [69, 21, 126, 17, "_duration"], [69, 30, 126, 26], [69, 35, 126, 31], [69, 36, 126, 32], [69, 38, 126, 34], [70, 14, 127, 10], [70, 18, 127, 14], [70, 19, 127, 15, "_onUpdate"], [70, 28, 127, 24], [70, 29, 127, 25], [70, 33, 127, 29], [70, 34, 127, 30, "_toValue"], [70, 42, 127, 38], [70, 43, 127, 39], [71, 14, 128, 10], [71, 18, 128, 14], [71, 19, 128, 15, "__notifyAnimationEnd"], [71, 39, 128, 35], [71, 40, 128, 36], [72, 16, 128, 37, "finished"], [72, 24, 128, 45], [72, 26, 128, 47], [73, 14, 128, 51], [73, 15, 128, 52], [73, 16, 128, 53], [74, 12, 129, 8], [74, 13, 129, 9], [74, 19, 129, 15], [75, 14, 130, 10], [75, 18, 130, 14], [75, 19, 130, 15, "_animationFrame"], [75, 34, 130, 30], [75, 37, 130, 33, "requestAnimationFrame"], [75, 58, 130, 54], [75, 59, 130, 55], [75, 65, 130, 61], [75, 69, 130, 65], [75, 70, 130, 66, "onUpdate"], [75, 78, 130, 74], [75, 79, 130, 75], [75, 80, 130, 76], [75, 81, 130, 77], [76, 12, 131, 8], [77, 10, 132, 6], [78, 8, 133, 4], [78, 9, 133, 5], [79, 8, 134, 4], [79, 12, 134, 8], [79, 16, 134, 12], [79, 17, 134, 13, "_delay"], [79, 23, 134, 19], [79, 25, 134, 21], [80, 10, 135, 6], [80, 14, 135, 10], [80, 15, 135, 11, "_timeout"], [80, 23, 135, 19], [80, 26, 135, 22, "setTimeout"], [80, 36, 135, 32], [80, 37, 135, 33, "start"], [80, 42, 135, 38], [80, 44, 135, 40], [80, 48, 135, 44], [80, 49, 135, 45, "_delay"], [80, 55, 135, 51], [80, 56, 135, 52], [81, 8, 136, 4], [81, 9, 136, 5], [81, 15, 136, 11], [82, 10, 137, 6, "start"], [82, 15, 137, 11], [82, 16, 137, 12], [82, 17, 137, 13], [83, 8, 138, 4], [84, 6, 139, 2], [85, 4, 139, 3], [86, 6, 139, 3, "key"], [86, 9, 139, 3], [87, 6, 139, 3, "value"], [87, 11, 139, 3], [87, 13, 141, 2], [87, 22, 141, 2, "onUpdate"], [87, 30, 141, 10, "onUpdate"], [87, 31, 141, 10], [87, 33, 141, 19], [88, 8, 142, 4], [88, 12, 142, 10, "now"], [88, 15, 142, 13], [88, 18, 142, 16, "Date"], [88, 22, 142, 20], [88, 23, 142, 21, "now"], [88, 26, 142, 24], [88, 27, 142, 25], [88, 28, 142, 26], [89, 8, 143, 4], [89, 12, 143, 8, "now"], [89, 15, 143, 11], [89, 19, 143, 15], [89, 23, 143, 19], [89, 24, 143, 20, "_startTime"], [89, 34, 143, 30], [89, 37, 143, 33], [89, 41, 143, 37], [89, 42, 143, 38, "_duration"], [89, 51, 143, 47], [89, 53, 143, 49], [90, 10, 144, 6], [90, 14, 144, 10], [90, 18, 144, 14], [90, 19, 144, 15, "_duration"], [90, 28, 144, 24], [90, 33, 144, 29], [90, 34, 144, 30], [90, 36, 144, 32], [91, 12, 145, 8], [91, 16, 145, 12], [91, 17, 145, 13, "_onUpdate"], [91, 26, 145, 22], [91, 27, 145, 23], [91, 31, 145, 27], [91, 32, 145, 28, "_toValue"], [91, 40, 145, 36], [91, 41, 145, 37], [92, 10, 146, 6], [92, 11, 146, 7], [92, 17, 146, 13], [93, 12, 147, 8], [93, 16, 147, 12], [93, 17, 147, 13, "_onUpdate"], [93, 26, 147, 22], [93, 27, 148, 10], [93, 31, 148, 14], [93, 32, 148, 15, "_fromValue"], [93, 42, 148, 25], [93, 45, 148, 28], [93, 49, 148, 32], [93, 50, 148, 33, "_easing"], [93, 57, 148, 40], [93, 58, 148, 41], [93, 59, 148, 42], [93, 60, 148, 43], [93, 64, 148, 47], [93, 68, 148, 51], [93, 69, 148, 52, "_toValue"], [93, 77, 148, 60], [93, 80, 148, 63], [93, 84, 148, 67], [93, 85, 148, 68, "_fromValue"], [93, 95, 148, 78], [93, 96, 149, 8], [93, 97, 149, 9], [94, 10, 150, 6], [95, 10, 151, 6], [95, 14, 151, 10], [95, 15, 151, 11, "__notifyAnimationEnd"], [95, 35, 151, 31], [95, 36, 151, 32], [96, 12, 151, 33, "finished"], [96, 20, 151, 41], [96, 22, 151, 43], [97, 10, 151, 47], [97, 11, 151, 48], [97, 12, 151, 49], [98, 10, 152, 6], [99, 8, 153, 4], [100, 8, 155, 4], [100, 12, 155, 8], [100, 13, 155, 9, "_onUpdate"], [100, 22, 155, 18], [100, 23, 156, 6], [100, 27, 156, 10], [100, 28, 156, 11, "_fromValue"], [100, 38, 156, 21], [100, 41, 157, 8], [100, 45, 157, 12], [100, 46, 157, 13, "_easing"], [100, 53, 157, 20], [100, 54, 157, 21], [100, 55, 157, 22, "now"], [100, 58, 157, 25], [100, 61, 157, 28], [100, 65, 157, 32], [100, 66, 157, 33, "_startTime"], [100, 76, 157, 43], [100, 80, 157, 47], [100, 84, 157, 51], [100, 85, 157, 52, "_duration"], [100, 94, 157, 61], [100, 95, 157, 62], [100, 99, 158, 11], [100, 103, 158, 15], [100, 104, 158, 16, "_toValue"], [100, 112, 158, 24], [100, 115, 158, 27], [100, 119, 158, 31], [100, 120, 158, 32, "_fromValue"], [100, 130, 158, 42], [100, 131, 159, 4], [100, 132, 159, 5], [101, 8, 160, 4], [101, 12, 160, 8], [101, 16, 160, 12], [101, 17, 160, 13, "__active"], [101, 25, 160, 21], [101, 27, 160, 23], [102, 10, 162, 6], [102, 14, 162, 10], [102, 15, 162, 11, "_animationFrame"], [102, 30, 162, 26], [102, 33, 162, 29, "requestAnimationFrame"], [102, 54, 162, 50], [102, 55, 162, 51], [102, 59, 162, 55], [102, 60, 162, 56, "onUpdate"], [102, 68, 162, 64], [102, 69, 162, 65, "bind"], [102, 73, 162, 69], [102, 74, 162, 70], [102, 78, 162, 74], [102, 79, 162, 75], [102, 80, 162, 76], [103, 8, 163, 4], [104, 6, 164, 2], [105, 4, 164, 3], [106, 6, 164, 3, "key"], [106, 9, 164, 3], [107, 6, 164, 3, "value"], [107, 11, 164, 3], [107, 13, 166, 2], [107, 22, 166, 2, "stop"], [107, 26, 166, 6, "stop"], [107, 27, 166, 6], [107, 29, 166, 15], [108, 8, 167, 4, "_superPropGet"], [108, 21, 167, 4], [108, 22, 167, 4, "TimingAnimation"], [108, 37, 167, 4], [109, 8, 168, 4, "clearTimeout"], [109, 20, 168, 16], [109, 21, 168, 17], [109, 25, 168, 21], [109, 26, 168, 22, "_timeout"], [109, 34, 168, 30], [109, 35, 168, 31], [110, 8, 169, 4], [110, 12, 169, 8], [110, 16, 169, 12], [110, 17, 169, 13, "_animationFrame"], [110, 32, 169, 28], [110, 36, 169, 32], [110, 40, 169, 36], [110, 42, 169, 38], [111, 10, 170, 6, "global"], [111, 16, 170, 12], [111, 17, 170, 13, "cancelAnimationFrame"], [111, 37, 170, 33], [111, 38, 170, 34], [111, 42, 170, 38], [111, 43, 170, 39, "_animationFrame"], [111, 58, 170, 54], [111, 59, 170, 55], [112, 8, 171, 4], [113, 8, 172, 4], [113, 12, 172, 8], [113, 13, 172, 9, "__notifyAnimationEnd"], [113, 33, 172, 29], [113, 34, 172, 30], [114, 10, 172, 31, "finished"], [114, 18, 172, 39], [114, 20, 172, 41], [115, 8, 172, 46], [115, 9, 172, 47], [115, 10, 172, 48], [116, 6, 173, 2], [117, 4, 173, 3], [118, 2, 173, 3], [118, 4, 59, 45, "Animation"], [118, 23, 59, 54], [119, 0, 59, 54], [119, 3]], "functionMap": {"names": ["<global>", "easeInOut", "TimingAnimation", "constructor", "__getNativeAnimationConfig", "start", "requestAnimationFrame$argument_0", "onUpdate", "stop"], "mappings": "AAA;ACkD;CDM;eEE;ECY;GDQ;EEE;GFuB;EGE;uDCwB,qBD;GHS;EKE;GLuB;EME;GNO"}}, "type": "js/module"}]}