{"dependencies": [{"name": "./core", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 3, "column": 14, "index": 29}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "FXN3sf/EjRB4E2rwdgFcvRpWg7U=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var Promise = require(_dependencyMap[0], \"./core\");\n  var DEFAULT_WHITELIST = [ReferenceError, TypeError, RangeError];\n  var enabled = false;\n  exports.disable = disable;\n  function disable() {\n    enabled = false;\n    Promise._B = null;\n    Promise._C = null;\n  }\n  exports.enable = enable;\n  function enable(options) {\n    options = options || {};\n    if (enabled) disable();\n    enabled = true;\n    var id = 0;\n    var displayId = 0;\n    var rejections = {};\n    Promise._B = function (promise) {\n      if (promise._y === 2 &&\n      // IS REJECTED\n      rejections[promise._E]) {\n        if (rejections[promise._E].logged) {\n          onHandled(promise._E);\n        } else {\n          clearTimeout(rejections[promise._E].timeout);\n        }\n        delete rejections[promise._E];\n      }\n    };\n    Promise._C = function (promise, err) {\n      if (promise._x === 0) {\n        // not yet handled\n        promise._E = id++;\n        rejections[promise._E] = {\n          displayId: null,\n          error: err,\n          timeout: setTimeout(onUnhandled.bind(null, promise._E),\n          // For reference errors and type errors, this almost always\n          // means the programmer made a mistake, so log them after just\n          // 100ms\n          // otherwise, wait 2 seconds to see if they get handled\n          matchWhitelist(err, DEFAULT_WHITELIST) ? 100 : 2000),\n          logged: false\n        };\n      }\n    };\n    function onUnhandled(id) {\n      if (options.allRejections || matchWhitelist(rejections[id].error, options.whitelist || DEFAULT_WHITELIST)) {\n        rejections[id].displayId = displayId++;\n        if (options.onUnhandled) {\n          rejections[id].logged = true;\n          options.onUnhandled(rejections[id].displayId, rejections[id].error);\n        } else {\n          rejections[id].logged = true;\n          logError(rejections[id].displayId, rejections[id].error);\n        }\n      }\n    }\n    function onHandled(id) {\n      if (rejections[id].logged) {\n        if (options.onHandled) {\n          options.onHandled(rejections[id].displayId, rejections[id].error);\n        } else if (!rejections[id].onUnhandled) {\n          console.warn('Promise Rejection Handled (id: ' + rejections[id].displayId + '):');\n          console.warn('  This means you can ignore any previous messages of the form \"Possible Unhandled Promise Rejection\" with id ' + rejections[id].displayId + '.');\n        }\n      }\n    }\n  }\n  function logError(id, error) {\n    console.warn('Possible Unhandled Promise Rejection (id: ' + id + '):');\n    var errStr = (error && (error.stack || error)) + '';\n    errStr.split('\\n').forEach(function (line) {\n      console.warn('  ' + line);\n    });\n  }\n  function matchWhitelist(error, list) {\n    return list.some(function (cls) {\n      return error instanceof cls;\n    });\n  }\n});", "lineCount": 85, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [4, 6, 3, 4, "Promise"], [4, 13, 3, 11], [4, 16, 3, 14, "require"], [4, 23, 3, 21], [4, 24, 3, 21, "_dependencyMap"], [4, 38, 3, 21], [4, 51, 3, 30], [4, 52, 3, 31], [5, 2, 5, 0], [5, 6, 5, 4, "DEFAULT_WHITELIST"], [5, 23, 5, 21], [5, 26, 5, 24], [5, 27, 6, 2, "ReferenceError"], [5, 41, 6, 16], [5, 43, 7, 2, "TypeError"], [5, 52, 7, 11], [5, 54, 8, 2, "RangeError"], [5, 64, 8, 12], [5, 65, 9, 1], [6, 2, 11, 0], [6, 6, 11, 4, "enabled"], [6, 13, 11, 11], [6, 16, 11, 14], [6, 21, 11, 19], [7, 2, 12, 0, "exports"], [7, 9, 12, 7], [7, 10, 12, 8, "disable"], [7, 17, 12, 15], [7, 20, 12, 18, "disable"], [7, 27, 12, 25], [8, 2, 13, 0], [8, 11, 13, 9, "disable"], [8, 18, 13, 16, "disable"], [8, 19, 13, 16], [8, 21, 13, 19], [9, 4, 14, 2, "enabled"], [9, 11, 14, 9], [9, 14, 14, 12], [9, 19, 14, 17], [10, 4, 15, 2, "Promise"], [10, 11, 15, 9], [10, 12, 15, 10, "_B"], [10, 14, 15, 12], [10, 17, 15, 15], [10, 21, 15, 19], [11, 4, 16, 2, "Promise"], [11, 11, 16, 9], [11, 12, 16, 10, "_C"], [11, 14, 16, 12], [11, 17, 16, 15], [11, 21, 16, 19], [12, 2, 17, 0], [13, 2, 19, 0, "exports"], [13, 9, 19, 7], [13, 10, 19, 8, "enable"], [13, 16, 19, 14], [13, 19, 19, 17, "enable"], [13, 25, 19, 23], [14, 2, 20, 0], [14, 11, 20, 9, "enable"], [14, 17, 20, 15, "enable"], [14, 18, 20, 16, "options"], [14, 25, 20, 23], [14, 27, 20, 25], [15, 4, 21, 2, "options"], [15, 11, 21, 9], [15, 14, 21, 12, "options"], [15, 21, 21, 19], [15, 25, 21, 23], [15, 26, 21, 24], [15, 27, 21, 25], [16, 4, 22, 2], [16, 8, 22, 6, "enabled"], [16, 15, 22, 13], [16, 17, 22, 15, "disable"], [16, 24, 22, 22], [16, 25, 22, 23], [16, 26, 22, 24], [17, 4, 23, 2, "enabled"], [17, 11, 23, 9], [17, 14, 23, 12], [17, 18, 23, 16], [18, 4, 24, 2], [18, 8, 24, 6, "id"], [18, 10, 24, 8], [18, 13, 24, 11], [18, 14, 24, 12], [19, 4, 25, 2], [19, 8, 25, 6, "displayId"], [19, 17, 25, 15], [19, 20, 25, 18], [19, 21, 25, 19], [20, 4, 26, 2], [20, 8, 26, 6, "rejections"], [20, 18, 26, 16], [20, 21, 26, 19], [20, 22, 26, 20], [20, 23, 26, 21], [21, 4, 27, 2, "Promise"], [21, 11, 27, 9], [21, 12, 27, 10, "_B"], [21, 14, 27, 12], [21, 17, 27, 15], [21, 27, 27, 25, "promise"], [21, 34, 27, 32], [21, 36, 27, 34], [22, 6, 28, 4], [22, 10, 29, 6, "promise"], [22, 17, 29, 13], [22, 18, 29, 14, "_y"], [22, 20, 29, 16], [22, 25, 29, 21], [22, 26, 29, 22], [23, 6, 29, 26], [24, 6, 30, 6, "rejections"], [24, 16, 30, 16], [24, 17, 30, 17, "promise"], [24, 24, 30, 24], [24, 25, 30, 25, "_E"], [24, 27, 30, 27], [24, 28, 30, 28], [24, 30, 31, 6], [25, 8, 32, 6], [25, 12, 32, 10, "rejections"], [25, 22, 32, 20], [25, 23, 32, 21, "promise"], [25, 30, 32, 28], [25, 31, 32, 29, "_E"], [25, 33, 32, 31], [25, 34, 32, 32], [25, 35, 32, 33, "logged"], [25, 41, 32, 39], [25, 43, 32, 41], [26, 10, 33, 8, "onHandled"], [26, 19, 33, 17], [26, 20, 33, 18, "promise"], [26, 27, 33, 25], [26, 28, 33, 26, "_E"], [26, 30, 33, 28], [26, 31, 33, 29], [27, 8, 34, 6], [27, 9, 34, 7], [27, 15, 34, 13], [28, 10, 35, 8, "clearTimeout"], [28, 22, 35, 20], [28, 23, 35, 21, "rejections"], [28, 33, 35, 31], [28, 34, 35, 32, "promise"], [28, 41, 35, 39], [28, 42, 35, 40, "_E"], [28, 44, 35, 42], [28, 45, 35, 43], [28, 46, 35, 44, "timeout"], [28, 53, 35, 51], [28, 54, 35, 52], [29, 8, 36, 6], [30, 8, 37, 6], [30, 15, 37, 13, "rejections"], [30, 25, 37, 23], [30, 26, 37, 24, "promise"], [30, 33, 37, 31], [30, 34, 37, 32, "_E"], [30, 36, 37, 34], [30, 37, 37, 35], [31, 6, 38, 4], [32, 4, 39, 2], [32, 5, 39, 3], [33, 4, 40, 2, "Promise"], [33, 11, 40, 9], [33, 12, 40, 10, "_C"], [33, 14, 40, 12], [33, 17, 40, 15], [33, 27, 40, 25, "promise"], [33, 34, 40, 32], [33, 36, 40, 34, "err"], [33, 39, 40, 37], [33, 41, 40, 39], [34, 6, 41, 4], [34, 10, 41, 8, "promise"], [34, 17, 41, 15], [34, 18, 41, 16, "_x"], [34, 20, 41, 18], [34, 25, 41, 23], [34, 26, 41, 24], [34, 28, 41, 26], [35, 8, 41, 28], [36, 8, 42, 6, "promise"], [36, 15, 42, 13], [36, 16, 42, 14, "_E"], [36, 18, 42, 16], [36, 21, 42, 19, "id"], [36, 23, 42, 21], [36, 25, 42, 23], [37, 8, 43, 6, "rejections"], [37, 18, 43, 16], [37, 19, 43, 17, "promise"], [37, 26, 43, 24], [37, 27, 43, 25, "_E"], [37, 29, 43, 27], [37, 30, 43, 28], [37, 33, 43, 31], [38, 10, 44, 8, "displayId"], [38, 19, 44, 17], [38, 21, 44, 19], [38, 25, 44, 23], [39, 10, 45, 8, "error"], [39, 15, 45, 13], [39, 17, 45, 15, "err"], [39, 20, 45, 18], [40, 10, 46, 8, "timeout"], [40, 17, 46, 15], [40, 19, 46, 17, "setTimeout"], [40, 29, 46, 27], [40, 30, 47, 10, "onUnhandled"], [40, 41, 47, 21], [40, 42, 47, 22, "bind"], [40, 46, 47, 26], [40, 47, 47, 27], [40, 51, 47, 31], [40, 53, 47, 33, "promise"], [40, 60, 47, 40], [40, 61, 47, 41, "_E"], [40, 63, 47, 43], [40, 64, 47, 44], [41, 10, 48, 10], [42, 10, 49, 10], [43, 10, 50, 10], [44, 10, 51, 10], [45, 10, 52, 10, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [45, 24, 52, 24], [45, 25, 52, 25, "err"], [45, 28, 52, 28], [45, 30, 52, 30, "DEFAULT_WHITELIST"], [45, 47, 52, 47], [45, 48, 52, 48], [45, 51, 53, 14], [45, 54, 53, 17], [45, 57, 54, 14], [45, 61, 55, 8], [45, 62, 55, 9], [46, 10, 56, 8, "logged"], [46, 16, 56, 14], [46, 18, 56, 16], [47, 8, 57, 6], [47, 9, 57, 7], [48, 6, 58, 4], [49, 4, 59, 2], [49, 5, 59, 3], [50, 4, 60, 2], [50, 13, 60, 11, "onUnhandled"], [50, 24, 60, 22, "onUnhandled"], [50, 25, 60, 23, "id"], [50, 27, 60, 25], [50, 29, 60, 27], [51, 6, 61, 4], [51, 10, 62, 6, "options"], [51, 17, 62, 13], [51, 18, 62, 14, "allRejections"], [51, 31, 62, 27], [51, 35, 63, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [51, 49, 63, 20], [51, 50, 64, 8, "rejections"], [51, 60, 64, 18], [51, 61, 64, 19, "id"], [51, 63, 64, 21], [51, 64, 64, 22], [51, 65, 64, 23, "error"], [51, 70, 64, 28], [51, 72, 65, 8, "options"], [51, 79, 65, 15], [51, 80, 65, 16, "whitelist"], [51, 89, 65, 25], [51, 93, 65, 29, "DEFAULT_WHITELIST"], [51, 110, 66, 6], [51, 111, 66, 7], [51, 113, 67, 6], [52, 8, 68, 6, "rejections"], [52, 18, 68, 16], [52, 19, 68, 17, "id"], [52, 21, 68, 19], [52, 22, 68, 20], [52, 23, 68, 21, "displayId"], [52, 32, 68, 30], [52, 35, 68, 33, "displayId"], [52, 44, 68, 42], [52, 46, 68, 44], [53, 8, 69, 6], [53, 12, 69, 10, "options"], [53, 19, 69, 17], [53, 20, 69, 18, "onUnhandled"], [53, 31, 69, 29], [53, 33, 69, 31], [54, 10, 70, 8, "rejections"], [54, 20, 70, 18], [54, 21, 70, 19, "id"], [54, 23, 70, 21], [54, 24, 70, 22], [54, 25, 70, 23, "logged"], [54, 31, 70, 29], [54, 34, 70, 32], [54, 38, 70, 36], [55, 10, 71, 8, "options"], [55, 17, 71, 15], [55, 18, 71, 16, "onUnhandled"], [55, 29, 71, 27], [55, 30, 72, 10, "rejections"], [55, 40, 72, 20], [55, 41, 72, 21, "id"], [55, 43, 72, 23], [55, 44, 72, 24], [55, 45, 72, 25, "displayId"], [55, 54, 72, 34], [55, 56, 73, 10, "rejections"], [55, 66, 73, 20], [55, 67, 73, 21, "id"], [55, 69, 73, 23], [55, 70, 73, 24], [55, 71, 73, 25, "error"], [55, 76, 74, 8], [55, 77, 74, 9], [56, 8, 75, 6], [56, 9, 75, 7], [56, 15, 75, 13], [57, 10, 76, 8, "rejections"], [57, 20, 76, 18], [57, 21, 76, 19, "id"], [57, 23, 76, 21], [57, 24, 76, 22], [57, 25, 76, 23, "logged"], [57, 31, 76, 29], [57, 34, 76, 32], [57, 38, 76, 36], [58, 10, 77, 8, "logError"], [58, 18, 77, 16], [58, 19, 78, 10, "rejections"], [58, 29, 78, 20], [58, 30, 78, 21, "id"], [58, 32, 78, 23], [58, 33, 78, 24], [58, 34, 78, 25, "displayId"], [58, 43, 78, 34], [58, 45, 79, 10, "rejections"], [58, 55, 79, 20], [58, 56, 79, 21, "id"], [58, 58, 79, 23], [58, 59, 79, 24], [58, 60, 79, 25, "error"], [58, 65, 80, 8], [58, 66, 80, 9], [59, 8, 81, 6], [60, 6, 82, 4], [61, 4, 83, 2], [62, 4, 84, 2], [62, 13, 84, 11, "onHandled"], [62, 22, 84, 20, "onHandled"], [62, 23, 84, 21, "id"], [62, 25, 84, 23], [62, 27, 84, 25], [63, 6, 85, 4], [63, 10, 85, 8, "rejections"], [63, 20, 85, 18], [63, 21, 85, 19, "id"], [63, 23, 85, 21], [63, 24, 85, 22], [63, 25, 85, 23, "logged"], [63, 31, 85, 29], [63, 33, 85, 31], [64, 8, 86, 6], [64, 12, 86, 10, "options"], [64, 19, 86, 17], [64, 20, 86, 18, "onHandled"], [64, 29, 86, 27], [64, 31, 86, 29], [65, 10, 87, 8, "options"], [65, 17, 87, 15], [65, 18, 87, 16, "onHandled"], [65, 27, 87, 25], [65, 28, 87, 26, "rejections"], [65, 38, 87, 36], [65, 39, 87, 37, "id"], [65, 41, 87, 39], [65, 42, 87, 40], [65, 43, 87, 41, "displayId"], [65, 52, 87, 50], [65, 54, 87, 52, "rejections"], [65, 64, 87, 62], [65, 65, 87, 63, "id"], [65, 67, 87, 65], [65, 68, 87, 66], [65, 69, 87, 67, "error"], [65, 74, 87, 72], [65, 75, 87, 73], [66, 8, 88, 6], [66, 9, 88, 7], [66, 15, 88, 13], [66, 19, 88, 17], [66, 20, 88, 18, "rejections"], [66, 30, 88, 28], [66, 31, 88, 29, "id"], [66, 33, 88, 31], [66, 34, 88, 32], [66, 35, 88, 33, "onUnhandled"], [66, 46, 88, 44], [66, 48, 88, 46], [67, 10, 89, 8, "console"], [67, 17, 89, 15], [67, 18, 89, 16, "warn"], [67, 22, 89, 20], [67, 23, 90, 10], [67, 56, 90, 43], [67, 59, 90, 46, "rejections"], [67, 69, 90, 56], [67, 70, 90, 57, "id"], [67, 72, 90, 59], [67, 73, 90, 60], [67, 74, 90, 61, "displayId"], [67, 83, 90, 70], [67, 86, 90, 73], [67, 90, 91, 8], [67, 91, 91, 9], [68, 10, 92, 8, "console"], [68, 17, 92, 15], [68, 18, 92, 16, "warn"], [68, 22, 92, 20], [68, 23, 93, 10], [68, 134, 93, 121], [68, 137, 94, 10, "rejections"], [68, 147, 94, 20], [68, 148, 94, 21, "id"], [68, 150, 94, 23], [68, 151, 94, 24], [68, 152, 94, 25, "displayId"], [68, 161, 94, 34], [68, 164, 94, 37], [68, 167, 95, 8], [68, 168, 95, 9], [69, 8, 96, 6], [70, 6, 97, 4], [71, 4, 98, 2], [72, 2, 99, 0], [73, 2, 101, 0], [73, 11, 101, 9, "logError"], [73, 19, 101, 17, "logError"], [73, 20, 101, 18, "id"], [73, 22, 101, 20], [73, 24, 101, 22, "error"], [73, 29, 101, 27], [73, 31, 101, 29], [74, 4, 102, 2, "console"], [74, 11, 102, 9], [74, 12, 102, 10, "warn"], [74, 16, 102, 14], [74, 17, 102, 15], [74, 61, 102, 59], [74, 64, 102, 62, "id"], [74, 66, 102, 64], [74, 69, 102, 67], [74, 73, 102, 71], [74, 74, 102, 72], [75, 4, 103, 2], [75, 8, 103, 6, "errStr"], [75, 14, 103, 12], [75, 17, 103, 15], [75, 18, 103, 16, "error"], [75, 23, 103, 21], [75, 28, 103, 26, "error"], [75, 33, 103, 31], [75, 34, 103, 32, "stack"], [75, 39, 103, 37], [75, 43, 103, 41, "error"], [75, 48, 103, 46], [75, 49, 103, 47], [75, 53, 103, 51], [75, 55, 103, 53], [76, 4, 104, 2, "errStr"], [76, 10, 104, 8], [76, 11, 104, 9, "split"], [76, 16, 104, 14], [76, 17, 104, 15], [76, 21, 104, 19], [76, 22, 104, 20], [76, 23, 104, 21, "for<PERSON>ach"], [76, 30, 104, 28], [76, 31, 104, 29], [76, 41, 104, 39, "line"], [76, 45, 104, 43], [76, 47, 104, 45], [77, 6, 105, 4, "console"], [77, 13, 105, 11], [77, 14, 105, 12, "warn"], [77, 18, 105, 16], [77, 19, 105, 17], [77, 23, 105, 21], [77, 26, 105, 24, "line"], [77, 30, 105, 28], [77, 31, 105, 29], [78, 4, 106, 2], [78, 5, 106, 3], [78, 6, 106, 4], [79, 2, 107, 0], [80, 2, 109, 0], [80, 11, 109, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [80, 25, 109, 23, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [80, 26, 109, 24, "error"], [80, 31, 109, 29], [80, 33, 109, 31, "list"], [80, 37, 109, 35], [80, 39, 109, 37], [81, 4, 110, 2], [81, 11, 110, 9, "list"], [81, 15, 110, 13], [81, 16, 110, 14, "some"], [81, 20, 110, 18], [81, 21, 110, 19], [81, 31, 110, 29, "cls"], [81, 34, 110, 32], [81, 36, 110, 34], [82, 6, 111, 4], [82, 13, 111, 11, "error"], [82, 18, 111, 16], [82, 30, 111, 28, "cls"], [82, 33, 111, 31], [83, 4, 112, 2], [83, 5, 112, 3], [83, 6, 112, 4], [84, 2, 113, 0], [85, 0, 113, 1], [85, 3]], "functionMap": {"names": ["<global>", "disable", "enable", "Promise._B", "Promise._C", "onUnhandled", "onHandled", "logError", "errStr.split.forEach$argument_0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list.some$argument_0"], "mappings": "AAA;ACY;CDI;AEG;eCO;GDY;eEC;GFmB;EGC;GHuB;EIC;GJc;CFC;AOE;6BCG;GDE;CPC;ASE;mBCC;GDE"}}, "type": "js/module"}]}