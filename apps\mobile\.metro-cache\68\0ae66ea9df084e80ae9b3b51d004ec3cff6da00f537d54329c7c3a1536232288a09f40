{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 327}, "end": {"line": 12, "column": 51, "index": 378}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useResponsive = exports.typography = exports.spacing = exports.responsive = exports.layout = exports.getResponsiveSpacing = exports.getResponsiveLayout = exports.getResponsiveFontSize = exports.BREAKPOINTS = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _s = $RefreshSig$(),\n    _s2 = $RefreshSig$(),\n    _s3 = $RefreshSig$(),\n    _s4 = $RefreshSig$(),\n    _s5 = $RefreshSig$();\n  /**\r\n   * Professional Responsive Design System for React Native\r\n   * Based on Google Play Store requirements and Android Developer Guidelines\r\n   * \r\n   * Breakpoints follow Material Design and Android guidelines:\r\n   * - Mobile: < 600dp\r\n   * - Tablet: 600dp - 840dp  \r\n   * - Large Tablet: 840dp - 1200dp\r\n   * - Desktop: > 1200dp\r\n   */\n  // Professional breakpoints based on Android Developer Guidelines\n  var BREAKPOINTS = exports.BREAKPOINTS = {\n    mobile: 0,\n    tablet: 600,\n    largeTablet: 840,\n    desktop: 1200\n  };\n\n  // Device types for better semantic naming\n\n  // Screen size categories\n\n  /**\r\n   * Custom hook for responsive design\r\n   * Returns screen information and device type detection\r\n   */\n  var useResponsive = () => {\n    _s();\n    var _useWindowDimensions = (0, _reactNative.useWindowDimensions)(),\n      width = _useWindowDimensions.width,\n      height = _useWindowDimensions.height;\n    var getDeviceType = screenWidth => {\n      if (screenWidth >= BREAKPOINTS.desktop) return 'desktop';\n      if (screenWidth >= BREAKPOINTS.largeTablet) return 'largeTablet';\n      if (screenWidth >= BREAKPOINTS.tablet) return 'tablet';\n      return 'mobile';\n    };\n    var deviceType = getDeviceType(width);\n    var isTablet = deviceType === 'tablet' || deviceType === 'largeTablet';\n    var isLargeScreen = deviceType === 'largeTablet' || deviceType === 'desktop';\n    var orientation = width > height ? 'landscape' : 'portrait';\n    return {\n      width,\n      height,\n      deviceType,\n      isTablet,\n      isLargeScreen,\n      orientation\n    };\n  };\n\n  /**\r\n   * Responsive value selector\r\n   * Returns different values based on device type\r\n   */\n  exports.useResponsive = useResponsive;\n  _s(useResponsive, \"hZg34DXQMoj2f5F9O/uvYVrpq/c=\", false, function () {\n    return [_reactNative.useWindowDimensions];\n  });\n  var responsive = values => {\n    _s2();\n    var _useResponsive = useResponsive(),\n      deviceType = _useResponsive.deviceType;\n    switch (deviceType) {\n      case 'desktop':\n        return values.desktop ?? values.largeTablet ?? values.tablet ?? values.mobile;\n      case 'largeTablet':\n        return values.largeTablet ?? values.tablet ?? values.mobile;\n      case 'tablet':\n        return values.tablet ?? values.mobile;\n      default:\n        return values.mobile;\n    }\n  };\n\n  /**\r\n   * Responsive spacing system\r\n   * Based on Material Design spacing guidelines\r\n   */\n  exports.responsive = responsive;\n  _s2(responsive, \"wIdGm+q/vDdt6chX6eQSLi8JgO0=\", false, function () {\n    return [useResponsive];\n  });\n  var spacing = exports.spacing = {\n    xs: 4,\n    sm: 8,\n    md: 16,\n    lg: 24,\n    xl: 32,\n    xxl: 48,\n    xxxl: 64\n  };\n\n  /**\r\n   * Get responsive spacing based on device type\r\n   */\n  var getResponsiveSpacing = size => {\n    _s3();\n    var _useResponsive2 = useResponsive(),\n      isTablet = _useResponsive2.isTablet,\n      isLargeScreen = _useResponsive2.isLargeScreen;\n    var baseSpacing = spacing[size];\n    if (isLargeScreen) {\n      return baseSpacing * 1.5; // 50% larger for large screens\n    }\n    if (isTablet) {\n      return baseSpacing * 1.25; // 25% larger for tablets\n    }\n    return baseSpacing;\n  };\n\n  /**\r\n   * Responsive font sizes\r\n   * Following Material Design typography scale\r\n   */\n  exports.getResponsiveSpacing = getResponsiveSpacing;\n  _s3(getResponsiveSpacing, \"JjdRpZ/4MWJ1mL9yTgxs+/aHI20=\", false, function () {\n    return [useResponsive];\n  });\n  var typography = exports.typography = {\n    xs: 12,\n    sm: 14,\n    base: 16,\n    lg: 18,\n    xl: 20,\n    '2xl': 24,\n    '3xl': 30,\n    '4xl': 36,\n    '5xl': 48\n  };\n\n  /**\r\n   * Get responsive font size based on device type\r\n   */\n  var getResponsiveFontSize = size => {\n    _s4();\n    var _useResponsive3 = useResponsive(),\n      isTablet = _useResponsive3.isTablet,\n      isLargeScreen = _useResponsive3.isLargeScreen;\n    var baseFontSize = typography[size];\n    if (isLargeScreen) {\n      return baseFontSize * 1.2; // 20% larger for large screens\n    }\n    if (isTablet) {\n      return baseFontSize * 1.1; // 10% larger for tablets\n    }\n    return baseFontSize;\n  };\n\n  /**\r\n   * Responsive layout dimensions\r\n   */\n  exports.getResponsiveFontSize = getResponsiveFontSize;\n  _s4(getResponsiveFontSize, \"JjdRpZ/4MWJ1mL9yTgxs+/aHI20=\", false, function () {\n    return [useResponsive];\n  });\n  var layout = exports.layout = {\n    // Maximum content width for readability\n    maxContentWidth: {\n      mobile: '100%',\n      tablet: 768,\n      largeTablet: 1024,\n      desktop: 1200\n    },\n    // Grid columns for different screen sizes\n    gridColumns: {\n      mobile: 1,\n      tablet: 2,\n      largeTablet: 3,\n      desktop: 4\n    },\n    // Header heights\n    headerHeight: {\n      mobile: 56,\n      tablet: 64,\n      largeTablet: 72,\n      desktop: 80\n    }\n  };\n\n  /**\r\n   * Get responsive layout value\r\n   */\n  var getResponsiveLayout = property => {\n    _s5();\n    var _useResponsive4 = useResponsive(),\n      deviceType = _useResponsive4.deviceType;\n    return layout[property][deviceType];\n  };\n  exports.getResponsiveLayout = getResponsiveLayout;\n  _s5(getResponsiveLayout, \"wIdGm+q/vDdt6chX6eQSLi8JgO0=\", false, function () {\n    return [useResponsive];\n  });\n});", "lineCount": 205, "map": [[6, 2, 12, 0], [6, 6, 12, 0, "_reactNative"], [6, 18, 12, 0], [6, 21, 12, 0, "require"], [6, 28, 12, 0], [6, 29, 12, 0, "_dependencyMap"], [6, 43, 12, 0], [7, 2, 12, 51], [7, 6, 12, 51, "_s"], [7, 8, 12, 51], [7, 11, 12, 51, "$RefreshSig$"], [7, 23, 12, 51], [8, 4, 12, 51, "_s2"], [8, 7, 12, 51], [8, 10, 12, 51, "$RefreshSig$"], [8, 22, 12, 51], [9, 4, 12, 51, "_s3"], [9, 7, 12, 51], [9, 10, 12, 51, "$RefreshSig$"], [9, 22, 12, 51], [10, 4, 12, 51, "_s4"], [10, 7, 12, 51], [10, 10, 12, 51, "$RefreshSig$"], [10, 22, 12, 51], [11, 4, 12, 51, "_s5"], [11, 7, 12, 51], [11, 10, 12, 51, "$RefreshSig$"], [11, 22, 12, 51], [12, 2, 1, 0], [13, 0, 2, 0], [14, 0, 3, 0], [15, 0, 4, 0], [16, 0, 5, 0], [17, 0, 6, 0], [18, 0, 7, 0], [19, 0, 8, 0], [20, 0, 9, 0], [21, 0, 10, 0], [22, 2, 14, 0], [23, 2, 15, 7], [23, 6, 15, 13, "BREAKPOINTS"], [23, 17, 15, 24], [23, 20, 15, 24, "exports"], [23, 27, 15, 24], [23, 28, 15, 24, "BREAKPOINTS"], [23, 39, 15, 24], [23, 42, 15, 27], [24, 4, 16, 2, "mobile"], [24, 10, 16, 8], [24, 12, 16, 10], [24, 13, 16, 11], [25, 4, 17, 2, "tablet"], [25, 10, 17, 8], [25, 12, 17, 10], [25, 15, 17, 13], [26, 4, 18, 2, "largeTablet"], [26, 15, 18, 13], [26, 17, 18, 15], [26, 20, 18, 18], [27, 4, 19, 2, "desktop"], [27, 11, 19, 9], [27, 13, 19, 11], [28, 2, 20, 0], [28, 3, 20, 10], [30, 2, 22, 0], [32, 2, 25, 0], [34, 2, 35, 0], [35, 0, 36, 0], [36, 0, 37, 0], [37, 0, 38, 0], [38, 2, 39, 7], [38, 6, 39, 13, "useResponsive"], [38, 19, 39, 26], [38, 22, 39, 29, "useResponsive"], [38, 23, 39, 29], [38, 28, 39, 47], [39, 4, 39, 47, "_s"], [39, 6, 39, 47], [40, 4, 40, 2], [40, 8, 40, 2, "_useWindowDimensions"], [40, 28, 40, 2], [40, 31, 40, 28], [40, 35, 40, 28, "useWindowDimensions"], [40, 67, 40, 47], [40, 69, 40, 48], [40, 70, 40, 49], [41, 6, 40, 10, "width"], [41, 11, 40, 15], [41, 14, 40, 15, "_useWindowDimensions"], [41, 34, 40, 15], [41, 35, 40, 10, "width"], [41, 40, 40, 15], [42, 6, 40, 17, "height"], [42, 12, 40, 23], [42, 15, 40, 23, "_useWindowDimensions"], [42, 35, 40, 23], [42, 36, 40, 17, "height"], [42, 42, 40, 23], [43, 4, 42, 2], [43, 8, 42, 8, "getDeviceType"], [43, 21, 42, 21], [43, 24, 42, 25, "screenWidth"], [43, 35, 42, 44], [43, 39, 42, 61], [44, 6, 43, 4], [44, 10, 43, 8, "screenWidth"], [44, 21, 43, 19], [44, 25, 43, 23, "BREAKPOINTS"], [44, 36, 43, 34], [44, 37, 43, 35, "desktop"], [44, 44, 43, 42], [44, 46, 43, 44], [44, 53, 43, 51], [44, 62, 43, 60], [45, 6, 44, 4], [45, 10, 44, 8, "screenWidth"], [45, 21, 44, 19], [45, 25, 44, 23, "BREAKPOINTS"], [45, 36, 44, 34], [45, 37, 44, 35, "largeTablet"], [45, 48, 44, 46], [45, 50, 44, 48], [45, 57, 44, 55], [45, 70, 44, 68], [46, 6, 45, 4], [46, 10, 45, 8, "screenWidth"], [46, 21, 45, 19], [46, 25, 45, 23, "BREAKPOINTS"], [46, 36, 45, 34], [46, 37, 45, 35, "tablet"], [46, 43, 45, 41], [46, 45, 45, 43], [46, 52, 45, 50], [46, 60, 45, 58], [47, 6, 46, 4], [47, 13, 46, 11], [47, 21, 46, 19], [48, 4, 47, 2], [48, 5, 47, 3], [49, 4, 49, 2], [49, 8, 49, 8, "deviceType"], [49, 18, 49, 18], [49, 21, 49, 21, "getDeviceType"], [49, 34, 49, 34], [49, 35, 49, 35, "width"], [49, 40, 49, 40], [49, 41, 49, 41], [50, 4, 50, 2], [50, 8, 50, 8, "isTablet"], [50, 16, 50, 16], [50, 19, 50, 19, "deviceType"], [50, 29, 50, 29], [50, 34, 50, 34], [50, 42, 50, 42], [50, 46, 50, 46, "deviceType"], [50, 56, 50, 56], [50, 61, 50, 61], [50, 74, 50, 74], [51, 4, 51, 2], [51, 8, 51, 8, "isLargeScreen"], [51, 21, 51, 21], [51, 24, 51, 24, "deviceType"], [51, 34, 51, 34], [51, 39, 51, 39], [51, 52, 51, 52], [51, 56, 51, 56, "deviceType"], [51, 66, 51, 66], [51, 71, 51, 71], [51, 80, 51, 80], [52, 4, 52, 2], [52, 8, 52, 8, "orientation"], [52, 19, 52, 19], [52, 22, 52, 22, "width"], [52, 27, 52, 27], [52, 30, 52, 30, "height"], [52, 36, 52, 36], [52, 39, 52, 39], [52, 50, 52, 50], [52, 53, 52, 53], [52, 63, 52, 63], [53, 4, 54, 2], [53, 11, 54, 9], [54, 6, 55, 4, "width"], [54, 11, 55, 9], [55, 6, 56, 4, "height"], [55, 12, 56, 10], [56, 6, 57, 4, "deviceType"], [56, 16, 57, 14], [57, 6, 58, 4, "isTablet"], [57, 14, 58, 12], [58, 6, 59, 4, "isLargeScreen"], [58, 19, 59, 17], [59, 6, 60, 4, "orientation"], [60, 4, 61, 2], [60, 5, 61, 3], [61, 2, 62, 0], [61, 3, 62, 1], [63, 2, 64, 0], [64, 0, 65, 0], [65, 0, 66, 0], [66, 0, 67, 0], [67, 2, 64, 0, "exports"], [67, 9, 64, 0], [67, 10, 64, 0, "useResponsive"], [67, 23, 64, 0], [67, 26, 64, 0, "useResponsive"], [67, 39, 64, 0], [68, 2, 64, 0, "_s"], [68, 4, 64, 0], [68, 5, 39, 13, "useResponsive"], [68, 18, 39, 26], [69, 4, 39, 26], [69, 12, 40, 28, "useWindowDimensions"], [69, 44, 40, 47], [70, 2, 40, 47], [71, 2, 68, 7], [71, 6, 68, 13, "responsive"], [71, 16, 68, 23], [71, 19, 68, 30, "values"], [71, 25, 73, 1], [71, 29, 73, 6], [72, 4, 73, 6, "_s2"], [72, 7, 73, 6], [73, 4, 74, 2], [73, 8, 74, 2, "_useResponsive"], [73, 22, 74, 2], [73, 25, 74, 25, "useResponsive"], [73, 38, 74, 38], [73, 39, 74, 39], [73, 40, 74, 40], [74, 6, 74, 10, "deviceType"], [74, 16, 74, 20], [74, 19, 74, 20, "_useResponsive"], [74, 33, 74, 20], [74, 34, 74, 10, "deviceType"], [74, 44, 74, 20], [75, 4, 76, 2], [75, 12, 76, 10, "deviceType"], [75, 22, 76, 20], [76, 6, 77, 4], [76, 11, 77, 9], [76, 20, 77, 18], [77, 8, 78, 6], [77, 15, 78, 13, "values"], [77, 21, 78, 19], [77, 22, 78, 20, "desktop"], [77, 29, 78, 27], [77, 33, 78, 31, "values"], [77, 39, 78, 37], [77, 40, 78, 38, "largeTablet"], [77, 51, 78, 49], [77, 55, 78, 53, "values"], [77, 61, 78, 59], [77, 62, 78, 60, "tablet"], [77, 68, 78, 66], [77, 72, 78, 70, "values"], [77, 78, 78, 76], [77, 79, 78, 77, "mobile"], [77, 85, 78, 83], [78, 6, 79, 4], [78, 11, 79, 9], [78, 24, 79, 22], [79, 8, 80, 6], [79, 15, 80, 13, "values"], [79, 21, 80, 19], [79, 22, 80, 20, "largeTablet"], [79, 33, 80, 31], [79, 37, 80, 35, "values"], [79, 43, 80, 41], [79, 44, 80, 42, "tablet"], [79, 50, 80, 48], [79, 54, 80, 52, "values"], [79, 60, 80, 58], [79, 61, 80, 59, "mobile"], [79, 67, 80, 65], [80, 6, 81, 4], [80, 11, 81, 9], [80, 19, 81, 17], [81, 8, 82, 6], [81, 15, 82, 13, "values"], [81, 21, 82, 19], [81, 22, 82, 20, "tablet"], [81, 28, 82, 26], [81, 32, 82, 30, "values"], [81, 38, 82, 36], [81, 39, 82, 37, "mobile"], [81, 45, 82, 43], [82, 6, 83, 4], [83, 8, 84, 6], [83, 15, 84, 13, "values"], [83, 21, 84, 19], [83, 22, 84, 20, "mobile"], [83, 28, 84, 26], [84, 4, 85, 2], [85, 2, 86, 0], [85, 3, 86, 1], [87, 2, 88, 0], [88, 0, 89, 0], [89, 0, 90, 0], [90, 0, 91, 0], [91, 2, 88, 0, "exports"], [91, 9, 88, 0], [91, 10, 88, 0, "responsive"], [91, 20, 88, 0], [91, 23, 88, 0, "responsive"], [91, 33, 88, 0], [92, 2, 88, 0, "_s2"], [92, 5, 88, 0], [92, 6, 68, 13, "responsive"], [92, 16, 68, 23], [93, 4, 68, 23], [93, 12, 74, 25, "useResponsive"], [93, 25, 74, 38], [94, 2, 74, 38], [95, 2, 92, 7], [95, 6, 92, 13, "spacing"], [95, 13, 92, 20], [95, 16, 92, 20, "exports"], [95, 23, 92, 20], [95, 24, 92, 20, "spacing"], [95, 31, 92, 20], [95, 34, 92, 23], [96, 4, 93, 2, "xs"], [96, 6, 93, 4], [96, 8, 93, 6], [96, 9, 93, 7], [97, 4, 94, 2, "sm"], [97, 6, 94, 4], [97, 8, 94, 6], [97, 9, 94, 7], [98, 4, 95, 2, "md"], [98, 6, 95, 4], [98, 8, 95, 6], [98, 10, 95, 8], [99, 4, 96, 2, "lg"], [99, 6, 96, 4], [99, 8, 96, 6], [99, 10, 96, 8], [100, 4, 97, 2, "xl"], [100, 6, 97, 4], [100, 8, 97, 6], [100, 10, 97, 8], [101, 4, 98, 2, "xxl"], [101, 7, 98, 5], [101, 9, 98, 7], [101, 11, 98, 9], [102, 4, 99, 2, "xxxl"], [102, 8, 99, 6], [102, 10, 99, 8], [103, 2, 100, 0], [103, 3, 100, 10], [105, 2, 102, 0], [106, 0, 103, 0], [107, 0, 104, 0], [108, 2, 105, 7], [108, 6, 105, 13, "getResponsiveSpacing"], [108, 26, 105, 33], [108, 29, 105, 37, "size"], [108, 33, 105, 63], [108, 37, 105, 68], [109, 4, 105, 68, "_s3"], [109, 7, 105, 68], [110, 4, 106, 2], [110, 8, 106, 2, "_useResponsive2"], [110, 23, 106, 2], [110, 26, 106, 38, "useResponsive"], [110, 39, 106, 51], [110, 40, 106, 52], [110, 41, 106, 53], [111, 6, 106, 10, "isTablet"], [111, 14, 106, 18], [111, 17, 106, 18, "_useResponsive2"], [111, 32, 106, 18], [111, 33, 106, 10, "isTablet"], [111, 41, 106, 18], [112, 6, 106, 20, "isLargeScreen"], [112, 19, 106, 33], [112, 22, 106, 33, "_useResponsive2"], [112, 37, 106, 33], [112, 38, 106, 20, "isLargeScreen"], [112, 51, 106, 33], [113, 4, 108, 2], [113, 8, 108, 8, "baseSpacing"], [113, 19, 108, 19], [113, 22, 108, 22, "spacing"], [113, 29, 108, 29], [113, 30, 108, 30, "size"], [113, 34, 108, 34], [113, 35, 108, 35], [114, 4, 110, 2], [114, 8, 110, 6, "isLargeScreen"], [114, 21, 110, 19], [114, 23, 110, 21], [115, 6, 111, 4], [115, 13, 111, 11, "baseSpacing"], [115, 24, 111, 22], [115, 27, 111, 25], [115, 30, 111, 28], [115, 31, 111, 29], [115, 32, 111, 30], [116, 4, 112, 2], [117, 4, 114, 2], [117, 8, 114, 6, "isTablet"], [117, 16, 114, 14], [117, 18, 114, 16], [118, 6, 115, 4], [118, 13, 115, 11, "baseSpacing"], [118, 24, 115, 22], [118, 27, 115, 25], [118, 31, 115, 29], [118, 32, 115, 30], [118, 33, 115, 31], [119, 4, 116, 2], [120, 4, 118, 2], [120, 11, 118, 9, "baseSpacing"], [120, 22, 118, 20], [121, 2, 119, 0], [121, 3, 119, 1], [123, 2, 121, 0], [124, 0, 122, 0], [125, 0, 123, 0], [126, 0, 124, 0], [127, 2, 121, 0, "exports"], [127, 9, 121, 0], [127, 10, 121, 0, "getResponsiveSpacing"], [127, 30, 121, 0], [127, 33, 121, 0, "getResponsiveSpacing"], [127, 53, 121, 0], [128, 2, 121, 0, "_s3"], [128, 5, 121, 0], [128, 6, 105, 13, "getResponsiveSpacing"], [128, 26, 105, 33], [129, 4, 105, 33], [129, 12, 106, 38, "useResponsive"], [129, 25, 106, 51], [130, 2, 106, 51], [131, 2, 125, 7], [131, 6, 125, 13, "typography"], [131, 16, 125, 23], [131, 19, 125, 23, "exports"], [131, 26, 125, 23], [131, 27, 125, 23, "typography"], [131, 37, 125, 23], [131, 40, 125, 26], [132, 4, 126, 2, "xs"], [132, 6, 126, 4], [132, 8, 126, 6], [132, 10, 126, 8], [133, 4, 127, 2, "sm"], [133, 6, 127, 4], [133, 8, 127, 6], [133, 10, 127, 8], [134, 4, 128, 2, "base"], [134, 8, 128, 6], [134, 10, 128, 8], [134, 12, 128, 10], [135, 4, 129, 2, "lg"], [135, 6, 129, 4], [135, 8, 129, 6], [135, 10, 129, 8], [136, 4, 130, 2, "xl"], [136, 6, 130, 4], [136, 8, 130, 6], [136, 10, 130, 8], [137, 4, 131, 2], [137, 9, 131, 7], [137, 11, 131, 9], [137, 13, 131, 11], [138, 4, 132, 2], [138, 9, 132, 7], [138, 11, 132, 9], [138, 13, 132, 11], [139, 4, 133, 2], [139, 9, 133, 7], [139, 11, 133, 9], [139, 13, 133, 11], [140, 4, 134, 2], [140, 9, 134, 7], [140, 11, 134, 9], [141, 2, 135, 0], [141, 3, 135, 10], [143, 2, 137, 0], [144, 0, 138, 0], [145, 0, 139, 0], [146, 2, 140, 7], [146, 6, 140, 13, "getResponsiveFontSize"], [146, 27, 140, 34], [146, 30, 140, 38, "size"], [146, 34, 140, 67], [146, 38, 140, 72], [147, 4, 140, 72, "_s4"], [147, 7, 140, 72], [148, 4, 141, 2], [148, 8, 141, 2, "_useResponsive3"], [148, 23, 141, 2], [148, 26, 141, 38, "useResponsive"], [148, 39, 141, 51], [148, 40, 141, 52], [148, 41, 141, 53], [149, 6, 141, 10, "isTablet"], [149, 14, 141, 18], [149, 17, 141, 18, "_useResponsive3"], [149, 32, 141, 18], [149, 33, 141, 10, "isTablet"], [149, 41, 141, 18], [150, 6, 141, 20, "isLargeScreen"], [150, 19, 141, 33], [150, 22, 141, 33, "_useResponsive3"], [150, 37, 141, 33], [150, 38, 141, 20, "isLargeScreen"], [150, 51, 141, 33], [151, 4, 143, 2], [151, 8, 143, 8, "baseFontSize"], [151, 20, 143, 20], [151, 23, 143, 23, "typography"], [151, 33, 143, 33], [151, 34, 143, 34, "size"], [151, 38, 143, 38], [151, 39, 143, 39], [152, 4, 145, 2], [152, 8, 145, 6, "isLargeScreen"], [152, 21, 145, 19], [152, 23, 145, 21], [153, 6, 146, 4], [153, 13, 146, 11, "baseFontSize"], [153, 25, 146, 23], [153, 28, 146, 26], [153, 31, 146, 29], [153, 32, 146, 30], [153, 33, 146, 31], [154, 4, 147, 2], [155, 4, 149, 2], [155, 8, 149, 6, "isTablet"], [155, 16, 149, 14], [155, 18, 149, 16], [156, 6, 150, 4], [156, 13, 150, 11, "baseFontSize"], [156, 25, 150, 23], [156, 28, 150, 26], [156, 31, 150, 29], [156, 32, 150, 30], [156, 33, 150, 31], [157, 4, 151, 2], [158, 4, 153, 2], [158, 11, 153, 9, "baseFontSize"], [158, 23, 153, 21], [159, 2, 154, 0], [159, 3, 154, 1], [161, 2, 156, 0], [162, 0, 157, 0], [163, 0, 158, 0], [164, 2, 156, 0, "exports"], [164, 9, 156, 0], [164, 10, 156, 0, "getResponsiveFontSize"], [164, 31, 156, 0], [164, 34, 156, 0, "getResponsiveFontSize"], [164, 55, 156, 0], [165, 2, 156, 0, "_s4"], [165, 5, 156, 0], [165, 6, 140, 13, "getResponsiveFontSize"], [165, 27, 140, 34], [166, 4, 140, 34], [166, 12, 141, 38, "useResponsive"], [166, 25, 141, 51], [167, 2, 141, 51], [168, 2, 159, 7], [168, 6, 159, 13, "layout"], [168, 12, 159, 19], [168, 15, 159, 19, "exports"], [168, 22, 159, 19], [168, 23, 159, 19, "layout"], [168, 29, 159, 19], [168, 32, 159, 22], [169, 4, 160, 2], [170, 4, 161, 2, "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [170, 19, 161, 17], [170, 21, 161, 19], [171, 6, 162, 4, "mobile"], [171, 12, 162, 10], [171, 14, 162, 12], [171, 20, 162, 18], [172, 6, 163, 4, "tablet"], [172, 12, 163, 10], [172, 14, 163, 12], [172, 17, 163, 15], [173, 6, 164, 4, "largeTablet"], [173, 17, 164, 15], [173, 19, 164, 17], [173, 23, 164, 21], [174, 6, 165, 4, "desktop"], [174, 13, 165, 11], [174, 15, 165, 13], [175, 4, 166, 2], [175, 5, 166, 3], [176, 4, 168, 2], [177, 4, 169, 2, "gridColumns"], [177, 15, 169, 13], [177, 17, 169, 15], [178, 6, 170, 4, "mobile"], [178, 12, 170, 10], [178, 14, 170, 12], [178, 15, 170, 13], [179, 6, 171, 4, "tablet"], [179, 12, 171, 10], [179, 14, 171, 12], [179, 15, 171, 13], [180, 6, 172, 4, "largeTablet"], [180, 17, 172, 15], [180, 19, 172, 17], [180, 20, 172, 18], [181, 6, 173, 4, "desktop"], [181, 13, 173, 11], [181, 15, 173, 13], [182, 4, 174, 2], [182, 5, 174, 3], [183, 4, 176, 2], [184, 4, 177, 2, "headerHeight"], [184, 16, 177, 14], [184, 18, 177, 16], [185, 6, 178, 4, "mobile"], [185, 12, 178, 10], [185, 14, 178, 12], [185, 16, 178, 14], [186, 6, 179, 4, "tablet"], [186, 12, 179, 10], [186, 14, 179, 12], [186, 16, 179, 14], [187, 6, 180, 4, "largeTablet"], [187, 17, 180, 15], [187, 19, 180, 17], [187, 21, 180, 19], [188, 6, 181, 4, "desktop"], [188, 13, 181, 11], [188, 15, 181, 13], [189, 4, 182, 2], [190, 2, 183, 0], [190, 3, 183, 10], [192, 2, 185, 0], [193, 0, 186, 0], [194, 0, 187, 0], [195, 2, 188, 7], [195, 6, 188, 13, "getResponsiveLayout"], [195, 25, 188, 32], [195, 28, 189, 2, "property"], [195, 36, 189, 13], [195, 40, 190, 10], [196, 4, 190, 10, "_s5"], [196, 7, 190, 10], [197, 4, 191, 2], [197, 8, 191, 2, "_useResponsive4"], [197, 23, 191, 2], [197, 26, 191, 25, "useResponsive"], [197, 39, 191, 38], [197, 40, 191, 39], [197, 41, 191, 40], [198, 6, 191, 10, "deviceType"], [198, 16, 191, 20], [198, 19, 191, 20, "_useResponsive4"], [198, 34, 191, 20], [198, 35, 191, 10, "deviceType"], [198, 45, 191, 20], [199, 4, 192, 2], [199, 11, 192, 9, "layout"], [199, 17, 192, 15], [199, 18, 192, 16, "property"], [199, 26, 192, 24], [199, 27, 192, 25], [199, 28, 192, 26, "deviceType"], [199, 38, 192, 36], [199, 39, 192, 37], [200, 2, 193, 0], [200, 3, 193, 1], [201, 2, 193, 2, "exports"], [201, 9, 193, 2], [201, 10, 193, 2, "getResponsiveLayout"], [201, 29, 193, 2], [201, 32, 193, 2, "getResponsiveLayout"], [201, 51, 193, 2], [202, 2, 193, 2, "_s5"], [202, 5, 193, 2], [202, 6, 188, 13, "getResponsiveLayout"], [202, 25, 188, 32], [203, 4, 188, 32], [203, 12, 191, 25, "useResponsive"], [203, 25, 191, 38], [204, 2, 191, 38], [205, 0, 191, 38], [205, 3]], "functionMap": {"names": ["<global>", "useResponsive", "getDeviceType", "responsive", "getResponsiveSpacing", "getResponsiveFontSize", "getResponsiveLayout"], "mappings": "AAA;6BCsC;wBCG;GDK;CDe;0BGM;CHkB;oCImB;CJc;qCKqB;CLc;mCMkC;CNK"}}, "type": "js/module"}]}