{"dependencies": [{"name": "./SegmentFetcher/NativeSegmentFetcher", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 52}}], "key": "QDiorBbMhThy89o6Lr7iXD352VI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  function __fetchSegment(segmentId, options, callback) {\n    var SegmentFetcher = require(_dependencyMap[0], \"./SegmentFetcher/NativeSegmentFetcher\").default;\n    SegmentFetcher.fetchSegment(segmentId, options, errorObject => {\n      if (errorObject) {\n        var error = new Error(errorObject.message);\n        error.code = errorObject.code;\n        callback(error);\n        return;\n      }\n      callback(null);\n    });\n  }\n  global.__fetchSegment = __fetchSegment;\n});", "lineCount": 17, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 20, 0], [4, 11, 20, 9, "__fetchSegment"], [4, 25, 20, 23, "__fetchSegment"], [4, 26, 21, 2, "segmentId"], [4, 35, 21, 19], [4, 37, 22, 2, "options"], [4, 44, 26, 4], [4, 46, 27, 2, "callback"], [4, 54, 27, 28], [4, 56, 28, 2], [5, 4, 29, 2], [5, 8, 29, 8, "SegmentFetcher"], [5, 22, 29, 22], [5, 25, 30, 4, "require"], [5, 32, 30, 11], [5, 33, 30, 11, "_dependencyMap"], [5, 47, 30, 11], [5, 91, 30, 51], [5, 92, 30, 52], [5, 93, 30, 53, "default"], [5, 100, 30, 60], [6, 4, 31, 2, "SegmentFetcher"], [6, 18, 31, 16], [6, 19, 31, 17, "fetchSegment"], [6, 31, 31, 29], [6, 32, 32, 4, "segmentId"], [6, 41, 32, 13], [6, 43, 33, 4, "options"], [6, 50, 33, 11], [6, 52, 35, 6, "errorObject"], [6, 63, 39, 7], [6, 67, 40, 9], [7, 6, 41, 6], [7, 10, 41, 10, "errorObject"], [7, 21, 41, 21], [7, 23, 41, 23], [8, 8, 42, 8], [8, 12, 42, 14, "error"], [8, 17, 42, 19], [8, 20, 42, 22], [8, 24, 42, 26, "Error"], [8, 29, 42, 31], [8, 30, 42, 32, "errorObject"], [8, 41, 42, 43], [8, 42, 42, 44, "message"], [8, 49, 42, 51], [8, 50, 42, 52], [9, 8, 43, 9, "error"], [9, 13, 43, 14], [9, 14, 43, 21, "code"], [9, 18, 43, 25], [9, 21, 43, 28, "errorObject"], [9, 32, 43, 39], [9, 33, 43, 40, "code"], [9, 37, 43, 44], [10, 8, 44, 8, "callback"], [10, 16, 44, 16], [10, 17, 44, 17, "error"], [10, 22, 44, 22], [10, 23, 44, 23], [11, 8, 45, 8], [12, 6, 46, 6], [13, 6, 48, 6, "callback"], [13, 14, 48, 14], [13, 15, 48, 15], [13, 19, 48, 19], [13, 20, 48, 20], [14, 4, 49, 4], [14, 5, 50, 2], [14, 6, 50, 3], [15, 2, 51, 0], [16, 2, 53, 0, "global"], [16, 8, 53, 6], [16, 9, 53, 7, "__fetchSegment"], [16, 23, 53, 21], [16, 26, 53, 24, "__fetchSegment"], [16, 40, 53, 38], [17, 0, 53, 39], [17, 3]], "functionMap": {"names": ["<global>", "__fetchSegment", "SegmentFetcher.fetchSegment$argument_2"], "mappings": "AAA;ACmB;ICc;KDe;CDE"}}, "type": "js/module"}]}