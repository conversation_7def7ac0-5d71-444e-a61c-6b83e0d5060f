{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 414}, "end": {"line": 9, "column": 31, "index": 445}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 481}, "end": {"line": 11, "column": 34, "index": 515}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 516}, "end": {"line": 24, "column": 22, "index": 714}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../handlers/PanGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 33, "column": 0, "index": 856}, "end": {"line": 33, "column": 66, "index": 922}}], "key": "Z7WldcovRqVbXERv5Mkjp/bqHuA=", "exportNames": ["*"]}}, {"name": "../handlers/TapGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 38, "column": 0, "index": 1052}, "end": {"line": 38, "column": 66, "index": 1118}}], "key": "+Msf+8iAdn6txPXicJvQZ0hdOZ0=", "exportNames": ["*"]}}, {"name": "../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 39, "column": 0, "index": 1119}, "end": {"line": 39, "column": 33, "index": 1152}}], "key": "ISRoyBmrsYyTcSqLDCBIFNoRZWE=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var React = _react;\n  var _invariant = _interopRequireDefault(require(_dependencyMap[7], \"invariant\"));\n  var _reactNative = require(_dependencyMap[8], \"react-native\");\n  var _PanGestureHandler = require(_dependencyMap[9], \"../handlers/PanGestureHandler\");\n  var _TapGestureHandler = require(_dependencyMap[10], \"../handlers/TapGestureHandler\");\n  var _State = require(_dependencyMap[11], \"../State\");\n  var _jsxDevRuntime = require(_dependencyMap[12], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\components\\\\DrawerLayout.tsx\"; // This component is based on RN's DrawerLayoutAndroid API\n  //\n  // It perhaps deserves to be put in a separate repo, but since it relies on\n  // react-native-gesture-handler library which isn't very popular at the moment I\n  // decided to keep it here for the time being. It will allow us to move faster\n  // and fix issues that may arise in gesture handler library that could be found\n  // when using the drawer component\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var DRAG_TOSS = 0.05;\n  var IDLE = 'Idle';\n  var DRAGGING = 'Dragging';\n  var SETTLING = 'Settling';\n\n  /**\n   * @deprecated DrawerLayout is deprecated. Use Reanimated version of DrawerLayout instead.\n   */\n\n  /**\n   * @deprecated DrawerLayout is deprecated. Use Reanimated version of DrawerLayout instead.\n   */\n\n  /**\n   * @deprecated DrawerLayout is deprecated. Use Reanimated version of DrawerLayout instead.\n   */\n\n  /**\n   * @deprecated DrawerLayout is deprecated. Use Reanimated version of DrawerLayout instead.\n   */\n\n  /**\n   * @deprecated DrawerLayout is deprecated. Use Reanimated version of DrawerLayout instead.\n   */\n\n  // Animated.AnimatedInterpolation has been converted to a generic type\n  // in @types/react-native 0.70. This way we can maintain compatibility\n  // with all versions of @types/react-native`\n\n  /**\n   * @deprecated DrawerLayout is deprecated. Use Reanimated version of DrawerLayout instead.\n   */\n\n  /**\n   * @deprecated DrawerLayout is deprecated. Use Reanimated version of DrawerLayout instead.\n   */\n\n  /**\n   * @deprecated DrawerLayout is deprecated. Use Reanimated version of DrawerLayout instead.\n   */\n  /**\n   * @deprecated use Reanimated version of DrawerLayout instead\n   */\n  var DrawerLayout = exports.default = /*#__PURE__*/function (_Component) {\n    function DrawerLayout(_props) {\n      var _this;\n      (0, _classCallCheck2.default)(this, DrawerLayout);\n      _this = _callSuper(this, DrawerLayout, [_props]);\n      _this.accessibilityIsModalView = /*#__PURE__*/React.createRef();\n      _this.pointerEventsView = /*#__PURE__*/React.createRef();\n      _this.panGestureHandler = /*#__PURE__*/React.createRef();\n      _this.drawerShown = false;\n      _this.updateAnimatedEvent = (props, state) => {\n        // Event definition is based on\n        var drawerPosition = props.drawerPosition,\n          drawerWidth = props.drawerWidth,\n          drawerType = props.drawerType;\n        var dragXValue = state.dragX,\n          touchXValue = state.touchX,\n          drawerTranslation = state.drawerTranslation,\n          containerWidth = state.containerWidth;\n        var dragX = dragXValue;\n        var touchX = touchXValue;\n        if (drawerPosition !== 'left') {\n          // Most of the code is written in a way to handle left-side drawer. In\n          // order to handle right-side drawer the only thing we need to do is to\n          // reverse events coming from gesture handler in a way they emulate\n          // left-side drawer gestures. E.g. dragX is simply -dragX, and touchX is\n          // calulcated by subtracing real touchX from the width of the container\n          // (such that when touch happens at the right edge the value is simply 0)\n          dragX = _reactNative.Animated.multiply(new _reactNative.Animated.Value(-1), dragXValue); // TODO(TS): (for all \"as\" in this file) make sure we can map this\n          touchX = _reactNative.Animated.add(new _reactNative.Animated.Value(containerWidth), _reactNative.Animated.multiply(new _reactNative.Animated.Value(-1), touchXValue)); // TODO(TS): make sure we can map this;\n          touchXValue.setValue(containerWidth);\n        } else {\n          touchXValue.setValue(0);\n        }\n\n        // While closing the drawer when user starts gesture outside of its area (in greyed\n        // out part of the window), we want the drawer to follow only once finger reaches the\n        // edge of the drawer.\n        // E.g. on the diagram below drawer is illustrate by X signs and the greyed out area by\n        // dots. The touch gesture starts at '*' and moves left, touch path is indicated by\n        // an arrow pointing left\n        // 1) +---------------+ 2) +---------------+ 3) +---------------+ 4) +---------------+\n        //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n        //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n        //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n        //    |XXXXXXXX|......|    |XXXXXXXX|.<-*..|    |XXXXXXXX|<--*..|    |XXXXX|<-----*..|\n        //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n        //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n        //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n        //    +---------------+    +---------------+    +---------------+    +---------------+\n        //\n        // For the above to work properly we define animated value that will keep\n        // start position of the gesture. Then we use that value to calculate how\n        // much we need to subtract from the dragX. If the gesture started on the\n        // greyed out area we take the distance from the edge of the drawer to the\n        // start position. Otherwise we don't subtract at all and the drawer be\n        // pulled back as soon as you start the pan.\n        //\n        // This is used only when drawerType is \"front\"\n        //\n        var translationX = dragX;\n        if (drawerType === 'front') {\n          var startPositionX = _reactNative.Animated.add(touchX, _reactNative.Animated.multiply(new _reactNative.Animated.Value(-1), dragX));\n          var dragOffsetFromOnStartPosition = startPositionX.interpolate({\n            inputRange: [drawerWidth - 1, drawerWidth, drawerWidth + 1],\n            outputRange: [0, 0, 1]\n          });\n          translationX = _reactNative.Animated.add(dragX, dragOffsetFromOnStartPosition); // TODO: as above\n        }\n        _this.openValue = _reactNative.Animated.add(translationX, drawerTranslation).interpolate({\n          inputRange: [0, drawerWidth],\n          outputRange: [0, 1],\n          extrapolate: 'clamp'\n        });\n        var gestureOptions = {\n          useNativeDriver: props.useNativeAnimations\n        };\n        if (_this.props.onDrawerSlide) {\n          gestureOptions.listener = ev => {\n            var translationX = Math.floor(Math.abs(ev.nativeEvent.translationX));\n            var position = translationX / _this.state.containerWidth;\n            _this.props.onDrawerSlide?.(position);\n          };\n        }\n        _this.onGestureEvent = _reactNative.Animated.event([{\n          nativeEvent: {\n            translationX: dragXValue,\n            x: touchXValue\n          }\n        }], gestureOptions);\n      };\n      _this.handleContainerLayout = _ref => {\n        var nativeEvent = _ref.nativeEvent;\n        _this.setState({\n          containerWidth: nativeEvent.layout.width\n        });\n      };\n      _this.emitStateChanged = (newState, drawerWillShow) => {\n        _this.props.onDrawerStateChanged?.(newState, drawerWillShow);\n      };\n      _this.openingHandlerStateChange = _ref2 => {\n        var nativeEvent = _ref2.nativeEvent;\n        if (nativeEvent.oldState === _State.State.ACTIVE) {\n          _this.handleRelease({\n            nativeEvent\n          });\n        } else if (nativeEvent.state === _State.State.ACTIVE) {\n          _this.emitStateChanged(DRAGGING, false);\n          _this.setState({\n            drawerState: DRAGGING\n          });\n          if (_this.props.keyboardDismissMode === 'on-drag') {\n            _reactNative.Keyboard.dismiss();\n          }\n          if (_this.props.hideStatusBar) {\n            _reactNative.StatusBar.setHidden(true, _this.props.statusBarAnimation || 'slide');\n          }\n        }\n      };\n      _this.onTapHandlerStateChange = _ref3 => {\n        var nativeEvent = _ref3.nativeEvent;\n        if (_this.drawerShown && nativeEvent.oldState === _State.State.ACTIVE && _this.props.drawerLockMode !== 'locked-open') {\n          _this.closeDrawer();\n        }\n      };\n      _this.handleRelease = _ref4 => {\n        var nativeEvent = _ref4.nativeEvent;\n        var _this$props = _this.props,\n          drawerWidth = _this$props.drawerWidth,\n          drawerPosition = _this$props.drawerPosition,\n          drawerType = _this$props.drawerType;\n        var containerWidth = _this.state.containerWidth;\n        var dragX = nativeEvent.translationX,\n          velocityX = nativeEvent.velocityX,\n          touchX = nativeEvent.x;\n        if (drawerPosition !== 'left') {\n          // See description in _updateAnimatedEvent about why events are flipped\n          // for right-side drawer\n          dragX = -dragX;\n          touchX = containerWidth - touchX;\n          velocityX = -velocityX;\n        }\n        var gestureStartX = touchX - dragX;\n        var dragOffsetBasedOnStart = 0;\n        if (drawerType === 'front') {\n          dragOffsetBasedOnStart = gestureStartX > drawerWidth ? gestureStartX - drawerWidth : 0;\n        }\n        var startOffsetX = dragX + dragOffsetBasedOnStart + (_this.drawerShown ? drawerWidth : 0);\n        var projOffsetX = startOffsetX + DRAG_TOSS * velocityX;\n        var shouldOpen = projOffsetX > drawerWidth / 2;\n        if (shouldOpen) {\n          _this.animateDrawer(startOffsetX, drawerWidth, velocityX);\n        } else {\n          _this.animateDrawer(startOffsetX, 0, velocityX);\n        }\n      };\n      _this.updateShowing = showing => {\n        _this.drawerShown = showing;\n        _this.accessibilityIsModalView.current?.setNativeProps({\n          accessibilityViewIsModal: showing\n        });\n        _this.pointerEventsView.current?.setNativeProps({\n          pointerEvents: showing ? 'auto' : 'none'\n        });\n        var _this$props2 = _this.props,\n          drawerPosition = _this$props2.drawerPosition,\n          minSwipeDistance = _this$props2.minSwipeDistance,\n          edgeWidth = _this$props2.edgeWidth;\n        var fromLeft = drawerPosition === 'left';\n        // gestureOrientation is 1 if the expected gesture is from left to right and\n        // -1 otherwise e.g. when drawer is on the left and is closed we expect left\n        // to right gesture, thus orientation will be 1.\n        var gestureOrientation = (fromLeft ? 1 : -1) * (_this.drawerShown ? -1 : 1);\n        // When drawer is closed we want the hitSlop to be horizontally shorter than\n        // the container size by the value of SLOP. This will make it only activate\n        // when gesture happens not further than SLOP away from the edge\n        var hitSlop = fromLeft ? {\n          left: 0,\n          width: showing ? undefined : edgeWidth\n        } : {\n          right: 0,\n          width: showing ? undefined : edgeWidth\n        };\n        // @ts-ignore internal API, maybe could be fixed in handler types\n        _this.panGestureHandler.current?.setNativeProps({\n          hitSlop,\n          activeOffsetX: gestureOrientation * minSwipeDistance\n        });\n      };\n      _this.animateDrawer = (fromValue, toValue, velocity, speed) => {\n        _this.state.dragX.setValue(0);\n        _this.state.touchX.setValue(_this.props.drawerPosition === 'left' ? 0 : _this.state.containerWidth);\n        if (fromValue != null) {\n          var nextFramePosition = fromValue;\n          if (_this.props.useNativeAnimations) {\n            // When using native driver, we predict the next position of the\n            // animation because it takes one frame of a roundtrip to pass RELEASE\n            // event from native driver to JS before we can start animating. Without\n            // it, it is more noticable that the frame is dropped.\n            if (fromValue < toValue && velocity > 0) {\n              nextFramePosition = Math.min(fromValue + velocity / 60.0, toValue);\n            } else if (fromValue > toValue && velocity < 0) {\n              nextFramePosition = Math.max(fromValue + velocity / 60.0, toValue);\n            }\n          }\n          _this.state.drawerTranslation.setValue(nextFramePosition);\n        }\n        var willShow = toValue !== 0;\n        _this.updateShowing(willShow);\n        _this.emitStateChanged(SETTLING, willShow);\n        _this.setState({\n          drawerState: SETTLING\n        });\n        if (_this.props.hideStatusBar) {\n          _reactNative.StatusBar.setHidden(willShow, _this.props.statusBarAnimation || 'slide');\n        }\n        _reactNative.Animated.spring(_this.state.drawerTranslation, {\n          velocity,\n          bounciness: 0,\n          toValue,\n          useNativeDriver: _this.props.useNativeAnimations,\n          speed: speed ?? undefined\n        }).start(_ref5 => {\n          var finished = _ref5.finished;\n          if (finished) {\n            _this.emitStateChanged(IDLE, willShow);\n            _this.setState({\n              drawerOpened: willShow\n            });\n            if (_this.state.drawerState !== DRAGGING) {\n              // It's possilbe that user started drag while the drawer\n              // was settling, don't override state in this case\n              _this.setState({\n                drawerState: IDLE\n              });\n            }\n            if (willShow) {\n              _this.props.onDrawerOpen?.();\n            } else {\n              _this.props.onDrawerClose?.();\n            }\n          }\n        });\n      };\n      // eslint-disable-next-line @eslint-react/no-unused-class-component-members\n      _this.openDrawer = function () {\n        var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        _this.animateDrawer(\n        // TODO: decide if it should be null or undefined is the proper value\n        undefined, _this.props.drawerWidth, options.velocity ? options.velocity : 0, options.speed);\n\n        // We need to force the update, otherwise the overlay is not rerendered and\n        // it would not be clickable\n        _this.forceUpdate();\n      };\n      _this.closeDrawer = function () {\n        var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        // TODO: decide if it should be null or undefined is the proper value\n        _this.animateDrawer(undefined, 0, options.velocity ? options.velocity : 0, options.speed);\n\n        // We need to force the update, otherwise the overlay is not rerendered and\n        // it would be still clickable\n        _this.forceUpdate();\n      };\n      _this.renderOverlay = () => {\n        /* Overlay styles */\n        (0, _invariant.default)(_this.openValue, 'should be set');\n        var overlayOpacity;\n        if (_this.state.drawerState !== IDLE) {\n          overlayOpacity = _this.openValue;\n        } else {\n          overlayOpacity = _this.state.drawerOpened ? 1 : 0;\n        }\n        var dynamicOverlayStyles = {\n          opacity: overlayOpacity,\n          backgroundColor: _this.props.overlayColor\n        };\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TapGestureHandler.TapGestureHandler, {\n          onHandlerStateChange: _this.onTapHandlerStateChange,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Animated.View, {\n            pointerEvents: _this.drawerShown ? 'auto' : 'none',\n            ref: _this.pointerEventsView,\n            style: [styles.overlay, dynamicOverlayStyles]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 9\n          }, _this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 7\n        }, _this);\n      };\n      _this.renderDrawer = () => {\n        var _this$props3 = _this.props,\n          drawerBackgroundColor = _this$props3.drawerBackgroundColor,\n          drawerWidth = _this$props3.drawerWidth,\n          drawerPosition = _this$props3.drawerPosition,\n          drawerType = _this$props3.drawerType,\n          drawerContainerStyle = _this$props3.drawerContainerStyle,\n          contentContainerStyle = _this$props3.contentContainerStyle;\n        var fromLeft = drawerPosition === 'left';\n        var drawerSlide = drawerType !== 'back';\n        var containerSlide = drawerType !== 'front';\n\n        // We rely on row and row-reverse flex directions to position the drawer\n        // properly. Apparently for RTL these are flipped which requires us to use\n        // the opposite setting for the drawer to appear from left or right\n        // according to the drawerPosition prop\n        var reverseContentDirection = _reactNative.I18nManager.isRTL ? fromLeft : !fromLeft;\n        var dynamicDrawerStyles = {\n          backgroundColor: drawerBackgroundColor,\n          width: drawerWidth\n        };\n        var openValue = _this.openValue;\n        (0, _invariant.default)(openValue, 'should be set');\n        var containerStyles;\n        if (containerSlide) {\n          var containerTranslateX = openValue.interpolate({\n            inputRange: [0, 1],\n            outputRange: fromLeft ? [0, drawerWidth] : [0, -drawerWidth],\n            extrapolate: 'clamp'\n          });\n          containerStyles = {\n            transform: [{\n              translateX: containerTranslateX\n            }]\n          };\n        }\n        var drawerTranslateX = 0;\n        if (drawerSlide) {\n          var closedDrawerOffset = fromLeft ? -drawerWidth : drawerWidth;\n          if (_this.state.drawerState !== IDLE) {\n            drawerTranslateX = openValue.interpolate({\n              inputRange: [0, 1],\n              outputRange: [closedDrawerOffset, 0],\n              extrapolate: 'clamp'\n            });\n          } else {\n            drawerTranslateX = _this.state.drawerOpened ? 0 : closedDrawerOffset;\n          }\n        }\n        var drawerStyles = {\n          transform: [{\n            translateX: drawerTranslateX\n          }],\n          flexDirection: reverseContentDirection ? 'row-reverse' : 'row'\n        };\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Animated.View, {\n          style: styles.main,\n          onLayout: _this.handleContainerLayout,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Animated.View, {\n            style: [drawerType === 'front' ? styles.containerOnBack : styles.containerInFront, containerStyles, contentContainerStyle],\n            importantForAccessibility: _this.drawerShown ? 'no-hide-descendants' : 'yes',\n            children: [typeof _this.props.children === 'function' ? _this.props.children(_this.openValue) : _this.props.children, _this.renderOverlay()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 9\n          }, _this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Animated.View, {\n            pointerEvents: \"box-none\",\n            ref: _this.accessibilityIsModalView,\n            accessibilityViewIsModal: _this.drawerShown,\n            style: [styles.drawerContainer, drawerStyles, drawerContainerStyle],\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: dynamicDrawerStyles,\n              children: _this.props.renderNavigationView(_this.openValue)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 11\n            }, _this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 9\n          }, _this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 7\n        }, _this);\n      };\n      _this.setPanGestureRef = ref => {\n        // TODO(TS): make sure it is OK taken from\n        // https://github.com/DefinitelyTyped/DefinitelyTyped/issues/31065#issuecomment-596081842\n        _this.panGestureHandler.current = ref;\n        _this.props.onGestureRef?.(ref);\n      };\n      var _dragX = new _reactNative.Animated.Value(0);\n      var _touchX = new _reactNative.Animated.Value(0);\n      var _drawerTranslation = new _reactNative.Animated.Value(0);\n      _this.state = {\n        dragX: _dragX,\n        touchX: _touchX,\n        drawerTranslation: _drawerTranslation,\n        containerWidth: 0,\n        drawerState: IDLE,\n        drawerOpened: false\n      };\n      _this.updateAnimatedEvent(_props, _this.state);\n      return _this;\n    }\n    (0, _inherits2.default)(DrawerLayout, _Component);\n    return (0, _createClass2.default)(DrawerLayout, [{\n      key: \"shouldComponentUpdate\",\n      value: function shouldComponentUpdate(props, state) {\n        if (this.props.drawerPosition !== props.drawerPosition || this.props.drawerWidth !== props.drawerWidth || this.props.drawerType !== props.drawerType || this.state.containerWidth !== state.containerWidth) {\n          this.updateAnimatedEvent(props, state);\n        }\n        return true;\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this$props4 = this.props,\n          drawerPosition = _this$props4.drawerPosition,\n          drawerLockMode = _this$props4.drawerLockMode,\n          edgeWidth = _this$props4.edgeWidth,\n          minSwipeDistance = _this$props4.minSwipeDistance;\n        var fromLeft = drawerPosition === 'left';\n\n        // gestureOrientation is 1 if the expected gesture is from left to right and\n        // -1 otherwise e.g. when drawer is on the left and is closed we expect left\n        // to right gesture, thus orientation will be 1.\n        var gestureOrientation = (fromLeft ? 1 : -1) * (this.drawerShown ? -1 : 1);\n\n        // When drawer is closed we want the hitSlop to be horizontally shorter than\n        // the container size by the value of SLOP. This will make it only activate\n        // when gesture happens not further than SLOP away from the edge\n        var hitSlop = fromLeft ? {\n          left: 0,\n          width: this.drawerShown ? undefined : edgeWidth\n        } : {\n          right: 0,\n          width: this.drawerShown ? undefined : edgeWidth\n        };\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_PanGestureHandler.PanGestureHandler\n        // @ts-ignore could be fixed in handler types\n        , {\n          userSelect: this.props.userSelect,\n          activeCursor: this.props.activeCursor,\n          mouseButton: this.props.mouseButton,\n          enableContextMenu: this.props.enableContextMenu,\n          ref: this.setPanGestureRef,\n          hitSlop: hitSlop,\n          activeOffsetX: gestureOrientation * minSwipeDistance,\n          failOffsetY: [-15, 15],\n          onGestureEvent: this.onGestureEvent,\n          onHandlerStateChange: this.openingHandlerStateChange,\n          enableTrackpadTwoFingerGesture: this.props.enableTrackpadTwoFingerGesture,\n          enabled: drawerLockMode !== 'locked-closed' && drawerLockMode !== 'locked-open',\n          children: this.renderDrawer()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 7\n        }, this);\n      }\n    }]);\n  }(_react.Component);\n  DrawerLayout.defaultProps = {\n    drawerWidth: 200,\n    drawerPosition: 'left',\n    useNativeAnimations: true,\n    drawerType: 'front',\n    edgeWidth: 20,\n    minSwipeDistance: 3,\n    overlayColor: 'rgba(0, 0, 0, 0.7)',\n    drawerLockMode: 'unlocked',\n    enableTrackpadTwoFingerGesture: false\n  };\n  DrawerLayout.positions = {\n    Left: 'left',\n    Right: 'right'\n  };\n  var styles = _reactNative.StyleSheet.create({\n    drawerContainer: {\n      ..._reactNative.StyleSheet.absoluteFillObject,\n      zIndex: 1001,\n      flexDirection: 'row'\n    },\n    containerInFront: {\n      ..._reactNative.StyleSheet.absoluteFillObject,\n      zIndex: 1002\n    },\n    containerOnBack: {\n      ..._reactNative.StyleSheet.absoluteFillObject\n    },\n    main: {\n      flex: 1,\n      zIndex: 0,\n      overflow: 'hidden'\n    },\n    overlay: {\n      ..._reactNative.StyleSheet.absoluteFillObject,\n      zIndex: 1000\n    }\n  });\n});", "lineCount": 572, "map": [[12, 2, 9, 0], [12, 6, 9, 0, "_react"], [12, 12, 9, 0], [12, 15, 9, 0, "_interopRequireWildcard"], [12, 38, 9, 0], [12, 39, 9, 0, "require"], [12, 46, 9, 0], [12, 47, 9, 0, "_dependencyMap"], [12, 61, 9, 0], [13, 2, 9, 31], [13, 6, 9, 31, "React"], [13, 11, 9, 31], [13, 14, 9, 31, "_react"], [13, 20, 9, 31], [14, 2, 11, 0], [14, 6, 11, 0, "_invariant"], [14, 16, 11, 0], [14, 19, 11, 0, "_interopRequireDefault"], [14, 41, 11, 0], [14, 42, 11, 0, "require"], [14, 49, 11, 0], [14, 50, 11, 0, "_dependencyMap"], [14, 64, 11, 0], [15, 2, 12, 0], [15, 6, 12, 0, "_reactNative"], [15, 18, 12, 0], [15, 21, 12, 0, "require"], [15, 28, 12, 0], [15, 29, 12, 0, "_dependencyMap"], [15, 43, 12, 0], [16, 2, 33, 0], [16, 6, 33, 0, "_PanGestureHandler"], [16, 24, 33, 0], [16, 27, 33, 0, "require"], [16, 34, 33, 0], [16, 35, 33, 0, "_dependencyMap"], [16, 49, 33, 0], [17, 2, 38, 0], [17, 6, 38, 0, "_TapGestureHandler"], [17, 24, 38, 0], [17, 27, 38, 0, "require"], [17, 34, 38, 0], [17, 35, 38, 0, "_dependencyMap"], [17, 49, 38, 0], [18, 2, 39, 0], [18, 6, 39, 0, "_State"], [18, 12, 39, 0], [18, 15, 39, 0, "require"], [18, 22, 39, 0], [18, 23, 39, 0, "_dependencyMap"], [18, 37, 39, 0], [19, 2, 39, 33], [19, 6, 39, 33, "_jsxDevRuntime"], [19, 20, 39, 33], [19, 23, 39, 33, "require"], [19, 30, 39, 33], [19, 31, 39, 33, "_dependencyMap"], [19, 45, 39, 33], [20, 2, 39, 33], [20, 6, 39, 33, "_jsxFileName"], [20, 18, 39, 33], [20, 136, 1, 0], [21, 2, 2, 0], [22, 2, 3, 0], [23, 2, 4, 0], [24, 2, 5, 0], [25, 2, 6, 0], [26, 2, 7, 0], [27, 2, 7, 0], [27, 11, 7, 0, "_interopRequireWildcard"], [27, 35, 7, 0, "e"], [27, 36, 7, 0], [27, 38, 7, 0, "t"], [27, 39, 7, 0], [27, 68, 7, 0, "WeakMap"], [27, 75, 7, 0], [27, 81, 7, 0, "r"], [27, 82, 7, 0], [27, 89, 7, 0, "WeakMap"], [27, 96, 7, 0], [27, 100, 7, 0, "n"], [27, 101, 7, 0], [27, 108, 7, 0, "WeakMap"], [27, 115, 7, 0], [27, 127, 7, 0, "_interopRequireWildcard"], [27, 150, 7, 0], [27, 162, 7, 0, "_interopRequireWildcard"], [27, 163, 7, 0, "e"], [27, 164, 7, 0], [27, 166, 7, 0, "t"], [27, 167, 7, 0], [27, 176, 7, 0, "t"], [27, 177, 7, 0], [27, 181, 7, 0, "e"], [27, 182, 7, 0], [27, 186, 7, 0, "e"], [27, 187, 7, 0], [27, 188, 7, 0, "__esModule"], [27, 198, 7, 0], [27, 207, 7, 0, "e"], [27, 208, 7, 0], [27, 214, 7, 0, "o"], [27, 215, 7, 0], [27, 217, 7, 0, "i"], [27, 218, 7, 0], [27, 220, 7, 0, "f"], [27, 221, 7, 0], [27, 226, 7, 0, "__proto__"], [27, 235, 7, 0], [27, 243, 7, 0, "default"], [27, 250, 7, 0], [27, 252, 7, 0, "e"], [27, 253, 7, 0], [27, 270, 7, 0, "e"], [27, 271, 7, 0], [27, 294, 7, 0, "e"], [27, 295, 7, 0], [27, 320, 7, 0, "e"], [27, 321, 7, 0], [27, 330, 7, 0, "f"], [27, 331, 7, 0], [27, 337, 7, 0, "o"], [27, 338, 7, 0], [27, 341, 7, 0, "t"], [27, 342, 7, 0], [27, 345, 7, 0, "n"], [27, 346, 7, 0], [27, 349, 7, 0, "r"], [27, 350, 7, 0], [27, 358, 7, 0, "o"], [27, 359, 7, 0], [27, 360, 7, 0, "has"], [27, 363, 7, 0], [27, 364, 7, 0, "e"], [27, 365, 7, 0], [27, 375, 7, 0, "o"], [27, 376, 7, 0], [27, 377, 7, 0, "get"], [27, 380, 7, 0], [27, 381, 7, 0, "e"], [27, 382, 7, 0], [27, 385, 7, 0, "o"], [27, 386, 7, 0], [27, 387, 7, 0, "set"], [27, 390, 7, 0], [27, 391, 7, 0, "e"], [27, 392, 7, 0], [27, 394, 7, 0, "f"], [27, 395, 7, 0], [27, 409, 7, 0, "_t"], [27, 411, 7, 0], [27, 415, 7, 0, "e"], [27, 416, 7, 0], [27, 432, 7, 0, "_t"], [27, 434, 7, 0], [27, 441, 7, 0, "hasOwnProperty"], [27, 455, 7, 0], [27, 456, 7, 0, "call"], [27, 460, 7, 0], [27, 461, 7, 0, "e"], [27, 462, 7, 0], [27, 464, 7, 0, "_t"], [27, 466, 7, 0], [27, 473, 7, 0, "i"], [27, 474, 7, 0], [27, 478, 7, 0, "o"], [27, 479, 7, 0], [27, 482, 7, 0, "Object"], [27, 488, 7, 0], [27, 489, 7, 0, "defineProperty"], [27, 503, 7, 0], [27, 508, 7, 0, "Object"], [27, 514, 7, 0], [27, 515, 7, 0, "getOwnPropertyDescriptor"], [27, 539, 7, 0], [27, 540, 7, 0, "e"], [27, 541, 7, 0], [27, 543, 7, 0, "_t"], [27, 545, 7, 0], [27, 552, 7, 0, "i"], [27, 553, 7, 0], [27, 554, 7, 0, "get"], [27, 557, 7, 0], [27, 561, 7, 0, "i"], [27, 562, 7, 0], [27, 563, 7, 0, "set"], [27, 566, 7, 0], [27, 570, 7, 0, "o"], [27, 571, 7, 0], [27, 572, 7, 0, "f"], [27, 573, 7, 0], [27, 575, 7, 0, "_t"], [27, 577, 7, 0], [27, 579, 7, 0, "i"], [27, 580, 7, 0], [27, 584, 7, 0, "f"], [27, 585, 7, 0], [27, 586, 7, 0, "_t"], [27, 588, 7, 0], [27, 592, 7, 0, "e"], [27, 593, 7, 0], [27, 594, 7, 0, "_t"], [27, 596, 7, 0], [27, 607, 7, 0, "f"], [27, 608, 7, 0], [27, 613, 7, 0, "e"], [27, 614, 7, 0], [27, 616, 7, 0, "t"], [27, 617, 7, 0], [28, 2, 7, 0], [28, 11, 7, 0, "_callSuper"], [28, 22, 7, 0, "t"], [28, 23, 7, 0], [28, 25, 7, 0, "o"], [28, 26, 7, 0], [28, 28, 7, 0, "e"], [28, 29, 7, 0], [28, 40, 7, 0, "o"], [28, 41, 7, 0], [28, 48, 7, 0, "_getPrototypeOf2"], [28, 64, 7, 0], [28, 65, 7, 0, "default"], [28, 72, 7, 0], [28, 74, 7, 0, "o"], [28, 75, 7, 0], [28, 82, 7, 0, "_possibleConstructorReturn2"], [28, 109, 7, 0], [28, 110, 7, 0, "default"], [28, 117, 7, 0], [28, 119, 7, 0, "t"], [28, 120, 7, 0], [28, 122, 7, 0, "_isNativeReflectConstruct"], [28, 147, 7, 0], [28, 152, 7, 0, "Reflect"], [28, 159, 7, 0], [28, 160, 7, 0, "construct"], [28, 169, 7, 0], [28, 170, 7, 0, "o"], [28, 171, 7, 0], [28, 173, 7, 0, "e"], [28, 174, 7, 0], [28, 186, 7, 0, "_getPrototypeOf2"], [28, 202, 7, 0], [28, 203, 7, 0, "default"], [28, 210, 7, 0], [28, 212, 7, 0, "t"], [28, 213, 7, 0], [28, 215, 7, 0, "constructor"], [28, 226, 7, 0], [28, 230, 7, 0, "o"], [28, 231, 7, 0], [28, 232, 7, 0, "apply"], [28, 237, 7, 0], [28, 238, 7, 0, "t"], [28, 239, 7, 0], [28, 241, 7, 0, "e"], [28, 242, 7, 0], [29, 2, 7, 0], [29, 11, 7, 0, "_isNativeReflectConstruct"], [29, 37, 7, 0], [29, 51, 7, 0, "t"], [29, 52, 7, 0], [29, 56, 7, 0, "Boolean"], [29, 63, 7, 0], [29, 64, 7, 0, "prototype"], [29, 73, 7, 0], [29, 74, 7, 0, "valueOf"], [29, 81, 7, 0], [29, 82, 7, 0, "call"], [29, 86, 7, 0], [29, 87, 7, 0, "Reflect"], [29, 94, 7, 0], [29, 95, 7, 0, "construct"], [29, 104, 7, 0], [29, 105, 7, 0, "Boolean"], [29, 112, 7, 0], [29, 145, 7, 0, "t"], [29, 146, 7, 0], [29, 159, 7, 0, "_isNativeReflectConstruct"], [29, 184, 7, 0], [29, 196, 7, 0, "_isNativeReflectConstruct"], [29, 197, 7, 0], [29, 210, 7, 0, "t"], [29, 211, 7, 0], [30, 2, 41, 0], [30, 6, 41, 6, "DRAG_TOSS"], [30, 15, 41, 15], [30, 18, 41, 18], [30, 22, 41, 22], [31, 2, 43, 0], [31, 6, 43, 6, "IDLE"], [31, 10, 43, 23], [31, 13, 43, 26], [31, 19, 43, 32], [32, 2, 44, 0], [32, 6, 44, 6, "DRAGGING"], [32, 14, 44, 27], [32, 17, 44, 30], [32, 27, 44, 40], [33, 2, 45, 0], [33, 6, 45, 6, "SETTLING"], [33, 14, 45, 27], [33, 17, 45, 30], [33, 27, 45, 40], [35, 2, 47, 0], [36, 0, 48, 0], [37, 0, 49, 0], [39, 2, 52, 0], [40, 0, 53, 0], [41, 0, 54, 0], [43, 2, 57, 0], [44, 0, 58, 0], [45, 0, 59, 0], [47, 2, 62, 0], [48, 0, 63, 0], [49, 0, 64, 0], [51, 2, 67, 0], [52, 0, 68, 0], [53, 0, 69, 0], [55, 2, 72, 0], [56, 2, 73, 0], [57, 2, 74, 0], [59, 2, 77, 0], [60, 0, 78, 0], [61, 0, 79, 0], [63, 2, 208, 0], [64, 0, 209, 0], [65, 0, 210, 0], [67, 2, 220, 0], [68, 0, 221, 0], [69, 0, 222, 0], [70, 2, 228, 0], [71, 0, 229, 0], [72, 0, 230, 0], [73, 2, 228, 0], [73, 6, 231, 21, "DrawerLayout"], [73, 18, 231, 33], [73, 21, 231, 33, "exports"], [73, 28, 231, 33], [73, 29, 231, 33, "default"], [73, 36, 231, 33], [73, 62, 231, 33, "_Component"], [73, 72, 231, 33], [74, 4, 247, 2], [74, 13, 247, 2, "DrawerLayout"], [74, 26, 247, 14, "props"], [74, 32, 247, 38], [74, 34, 247, 40], [75, 6, 247, 40], [75, 10, 247, 40, "_this"], [75, 15, 247, 40], [76, 6, 247, 40], [76, 10, 247, 40, "_classCallCheck2"], [76, 26, 247, 40], [76, 27, 247, 40, "default"], [76, 34, 247, 40], [76, 42, 247, 40, "DrawerLayout"], [76, 54, 247, 40], [77, 6, 248, 4, "_this"], [77, 11, 248, 4], [77, 14, 248, 4, "_callSuper"], [77, 24, 248, 4], [77, 31, 248, 4, "DrawerLayout"], [77, 43, 248, 4], [77, 46, 248, 10, "props"], [77, 52, 248, 15], [78, 6, 248, 17, "_this"], [78, 11, 248, 17], [78, 12, 283, 10, "accessibilityIsModalView"], [78, 36, 283, 34], [78, 52, 284, 4, "React"], [78, 57, 284, 9], [78, 58, 284, 10, "createRef"], [78, 67, 284, 19], [78, 68, 284, 53], [78, 69, 284, 54], [79, 6, 284, 54, "_this"], [79, 11, 284, 54], [79, 12, 285, 10, "pointerEventsView"], [79, 29, 285, 27], [79, 45, 286, 4, "React"], [79, 50, 286, 9], [79, 51, 286, 10, "createRef"], [79, 60, 286, 19], [79, 61, 286, 53], [79, 62, 286, 54], [80, 6, 286, 54, "_this"], [80, 11, 286, 54], [80, 12, 287, 10, "panGestureHandler"], [80, 29, 287, 27], [80, 45, 287, 30, "React"], [80, 50, 287, 35], [80, 51, 287, 36, "createRef"], [80, 60, 287, 45], [80, 61, 287, 72], [80, 62, 287, 73], [81, 6, 287, 73, "_this"], [81, 11, 287, 73], [81, 12, 288, 10, "drawerShown"], [81, 23, 288, 21], [81, 26, 288, 24], [81, 31, 288, 29], [82, 6, 288, 29, "_this"], [82, 11, 288, 29], [82, 12, 295, 10, "updateAnimatedEvent"], [82, 31, 295, 29], [82, 34, 295, 32], [82, 35, 296, 4, "props"], [82, 40, 296, 28], [82, 42, 297, 4, "state"], [82, 47, 297, 28], [82, 52, 298, 7], [83, 8, 299, 4], [84, 8, 300, 4], [84, 12, 300, 12, "drawerPosition"], [84, 26, 300, 26], [84, 29, 300, 56, "props"], [84, 34, 300, 61], [84, 35, 300, 12, "drawerPosition"], [84, 49, 300, 26], [85, 10, 300, 28, "drawerWidth"], [85, 21, 300, 39], [85, 24, 300, 56, "props"], [85, 29, 300, 61], [85, 30, 300, 28, "drawerWidth"], [85, 41, 300, 39], [86, 10, 300, 41, "drawerType"], [86, 20, 300, 51], [86, 23, 300, 56, "props"], [86, 28, 300, 61], [86, 29, 300, 41, "drawerType"], [86, 39, 300, 51], [87, 8, 301, 4], [87, 12, 302, 13, "dragXValue"], [87, 22, 302, 23], [87, 25, 306, 8, "state"], [87, 30, 306, 13], [87, 31, 302, 6, "dragX"], [87, 36, 302, 11], [88, 10, 303, 14, "touchXValue"], [88, 21, 303, 25], [88, 24, 306, 8, "state"], [88, 29, 306, 13], [88, 30, 303, 6, "touchX"], [88, 36, 303, 12], [89, 10, 304, 6, "drawerTranslation"], [89, 27, 304, 23], [89, 30, 306, 8, "state"], [89, 35, 306, 13], [89, 36, 304, 6, "drawerTranslation"], [89, 53, 304, 23], [90, 10, 305, 6, "containerWidth"], [90, 24, 305, 20], [90, 27, 306, 8, "state"], [90, 32, 306, 13], [90, 33, 305, 6, "containerWidth"], [90, 47, 305, 20], [91, 8, 308, 4], [91, 12, 308, 8, "dragX"], [91, 17, 308, 13], [91, 20, 308, 16, "dragXValue"], [91, 30, 308, 26], [92, 8, 309, 4], [92, 12, 309, 8, "touchX"], [92, 18, 309, 14], [92, 21, 309, 17, "touchXValue"], [92, 32, 309, 28], [93, 8, 311, 4], [93, 12, 311, 8, "drawerPosition"], [93, 26, 311, 22], [93, 31, 311, 27], [93, 37, 311, 33], [93, 39, 311, 35], [94, 10, 312, 6], [95, 10, 313, 6], [96, 10, 314, 6], [97, 10, 315, 6], [98, 10, 316, 6], [99, 10, 317, 6], [100, 10, 318, 6, "dragX"], [100, 15, 318, 11], [100, 18, 318, 14, "Animated"], [100, 39, 318, 22], [100, 40, 318, 23, "multiply"], [100, 48, 318, 31], [100, 49, 319, 8], [100, 53, 319, 12, "Animated"], [100, 74, 319, 20], [100, 75, 319, 21, "Value"], [100, 80, 319, 26], [100, 81, 319, 27], [100, 82, 319, 28], [100, 83, 319, 29], [100, 84, 319, 30], [100, 86, 320, 8, "dragXValue"], [100, 96, 321, 6], [100, 97, 321, 25], [100, 98, 321, 26], [100, 99, 321, 27], [101, 10, 322, 6, "touchX"], [101, 16, 322, 12], [101, 19, 322, 15, "Animated"], [101, 40, 322, 23], [101, 41, 322, 24, "add"], [101, 44, 322, 27], [101, 45, 323, 8], [101, 49, 323, 12, "Animated"], [101, 70, 323, 20], [101, 71, 323, 21, "Value"], [101, 76, 323, 26], [101, 77, 323, 27, "containerWidth"], [101, 91, 323, 41], [101, 92, 323, 42], [101, 94, 324, 8, "Animated"], [101, 115, 324, 16], [101, 116, 324, 17, "multiply"], [101, 124, 324, 25], [101, 125, 324, 26], [101, 129, 324, 30, "Animated"], [101, 150, 324, 38], [101, 151, 324, 39, "Value"], [101, 156, 324, 44], [101, 157, 324, 45], [101, 158, 324, 46], [101, 159, 324, 47], [101, 160, 324, 48], [101, 162, 324, 50, "touchXValue"], [101, 173, 324, 61], [101, 174, 325, 6], [101, 175, 325, 25], [101, 176, 325, 26], [101, 177, 325, 27], [102, 10, 326, 6, "touchXValue"], [102, 21, 326, 17], [102, 22, 326, 18, "setValue"], [102, 30, 326, 26], [102, 31, 326, 27, "containerWidth"], [102, 45, 326, 41], [102, 46, 326, 42], [103, 8, 327, 4], [103, 9, 327, 5], [103, 15, 327, 11], [104, 10, 328, 6, "touchXValue"], [104, 21, 328, 17], [104, 22, 328, 18, "setValue"], [104, 30, 328, 26], [104, 31, 328, 27], [104, 32, 328, 28], [104, 33, 328, 29], [105, 8, 329, 4], [107, 8, 331, 4], [108, 8, 332, 4], [109, 8, 333, 4], [110, 8, 334, 4], [111, 8, 335, 4], [112, 8, 336, 4], [113, 8, 337, 4], [114, 8, 338, 4], [115, 8, 339, 4], [116, 8, 340, 4], [117, 8, 341, 4], [118, 8, 342, 4], [119, 8, 343, 4], [120, 8, 344, 4], [121, 8, 345, 4], [122, 8, 346, 4], [123, 8, 347, 4], [124, 8, 348, 4], [125, 8, 349, 4], [126, 8, 350, 4], [127, 8, 351, 4], [128, 8, 352, 4], [129, 8, 353, 4], [130, 8, 354, 4], [131, 8, 355, 4], [132, 8, 356, 4], [132, 12, 356, 8, "translationX"], [132, 24, 356, 20], [132, 27, 356, 23, "dragX"], [132, 32, 356, 28], [133, 8, 357, 4], [133, 12, 357, 8, "drawerType"], [133, 22, 357, 18], [133, 27, 357, 23], [133, 34, 357, 30], [133, 36, 357, 32], [134, 10, 358, 6], [134, 14, 358, 12, "startPositionX"], [134, 28, 358, 26], [134, 31, 358, 29, "Animated"], [134, 52, 358, 37], [134, 53, 358, 38, "add"], [134, 56, 358, 41], [134, 57, 359, 8, "touchX"], [134, 63, 359, 14], [134, 65, 360, 8, "Animated"], [134, 86, 360, 16], [134, 87, 360, 17, "multiply"], [134, 95, 360, 25], [134, 96, 360, 26], [134, 100, 360, 30, "Animated"], [134, 121, 360, 38], [134, 122, 360, 39, "Value"], [134, 127, 360, 44], [134, 128, 360, 45], [134, 129, 360, 46], [134, 130, 360, 47], [134, 131, 360, 48], [134, 133, 360, 50, "dragX"], [134, 138, 360, 55], [134, 139, 361, 6], [134, 140, 361, 7], [135, 10, 363, 6], [135, 14, 363, 12, "dragOffsetFromOnStartPosition"], [135, 43, 363, 41], [135, 46, 363, 44, "startPositionX"], [135, 60, 363, 58], [135, 61, 363, 59, "interpolate"], [135, 72, 363, 70], [135, 73, 363, 71], [136, 12, 364, 8, "inputRange"], [136, 22, 364, 18], [136, 24, 364, 20], [136, 25, 364, 21, "drawerWidth"], [136, 36, 364, 32], [136, 39, 364, 36], [136, 40, 364, 37], [136, 42, 364, 39, "drawerWidth"], [136, 53, 364, 50], [136, 55, 364, 53, "drawerWidth"], [136, 66, 364, 64], [136, 69, 364, 68], [136, 70, 364, 69], [136, 71, 364, 70], [137, 12, 365, 8, "outputRange"], [137, 23, 365, 19], [137, 25, 365, 21], [137, 26, 365, 22], [137, 27, 365, 23], [137, 29, 365, 25], [137, 30, 365, 26], [137, 32, 365, 28], [137, 33, 365, 29], [138, 10, 366, 6], [138, 11, 366, 7], [138, 12, 366, 8], [139, 10, 367, 6, "translationX"], [139, 22, 367, 18], [139, 25, 367, 21, "Animated"], [139, 46, 367, 29], [139, 47, 367, 30, "add"], [139, 50, 367, 33], [139, 51, 368, 8, "dragX"], [139, 56, 368, 13], [139, 58, 369, 8, "dragOffsetFromOnStartPosition"], [139, 87, 370, 6], [139, 88, 370, 25], [139, 89, 370, 26], [139, 90, 370, 27], [140, 8, 371, 4], [141, 8, 373, 4, "_this"], [141, 13, 373, 4], [141, 14, 373, 9, "openValue"], [141, 23, 373, 18], [141, 26, 373, 21, "Animated"], [141, 47, 373, 29], [141, 48, 373, 30, "add"], [141, 51, 373, 33], [141, 52, 373, 34, "translationX"], [141, 64, 373, 46], [141, 66, 373, 48, "drawerTranslation"], [141, 83, 373, 65], [141, 84, 373, 66], [141, 85, 373, 67, "interpolate"], [141, 96, 373, 78], [141, 97, 373, 79], [142, 10, 374, 6, "inputRange"], [142, 20, 374, 16], [142, 22, 374, 18], [142, 23, 374, 19], [142, 24, 374, 20], [142, 26, 374, 22, "drawerWidth"], [142, 37, 374, 33], [142, 38, 374, 35], [143, 10, 375, 6, "outputRange"], [143, 21, 375, 17], [143, 23, 375, 19], [143, 24, 375, 20], [143, 25, 375, 21], [143, 27, 375, 23], [143, 28, 375, 24], [143, 29, 375, 25], [144, 10, 376, 6, "extrapolate"], [144, 21, 376, 17], [144, 23, 376, 19], [145, 8, 377, 4], [145, 9, 377, 5], [145, 10, 377, 6], [146, 8, 379, 4], [146, 12, 379, 10, "gestureOptions"], [146, 26, 385, 5], [146, 29, 385, 8], [147, 10, 386, 6, "useNativeDriver"], [147, 25, 386, 21], [147, 27, 386, 23, "props"], [147, 32, 386, 28], [147, 33, 386, 29, "useNativeAnimations"], [148, 8, 387, 4], [148, 9, 387, 5], [149, 8, 389, 4], [149, 12, 389, 8, "_this"], [149, 17, 389, 8], [149, 18, 389, 13, "props"], [149, 23, 389, 18], [149, 24, 389, 19, "onDrawerSlide"], [149, 37, 389, 32], [149, 39, 389, 34], [150, 10, 390, 6, "gestureOptions"], [150, 24, 390, 20], [150, 25, 390, 21, "listener"], [150, 33, 390, 29], [150, 36, 390, 33, "ev"], [150, 38, 390, 35], [150, 42, 390, 40], [151, 12, 391, 8], [151, 16, 391, 14, "translationX"], [151, 28, 391, 26], [151, 31, 391, 29, "Math"], [151, 35, 391, 33], [151, 36, 391, 34, "floor"], [151, 41, 391, 39], [151, 42, 391, 40, "Math"], [151, 46, 391, 44], [151, 47, 391, 45, "abs"], [151, 50, 391, 48], [151, 51, 391, 49, "ev"], [151, 53, 391, 51], [151, 54, 391, 52, "nativeEvent"], [151, 65, 391, 63], [151, 66, 391, 64, "translationX"], [151, 78, 391, 76], [151, 79, 391, 77], [151, 80, 391, 78], [152, 12, 392, 8], [152, 16, 392, 14, "position"], [152, 24, 392, 22], [152, 27, 392, 25, "translationX"], [152, 39, 392, 37], [152, 42, 392, 40, "_this"], [152, 47, 392, 40], [152, 48, 392, 45, "state"], [152, 53, 392, 50], [152, 54, 392, 51, "containerWidth"], [152, 68, 392, 65], [153, 12, 394, 8, "_this"], [153, 17, 394, 8], [153, 18, 394, 13, "props"], [153, 23, 394, 18], [153, 24, 394, 19, "onDrawerSlide"], [153, 37, 394, 32], [153, 40, 394, 35, "position"], [153, 48, 394, 43], [153, 49, 394, 44], [154, 10, 395, 6], [154, 11, 395, 7], [155, 8, 396, 4], [156, 8, 398, 4, "_this"], [156, 13, 398, 4], [156, 14, 398, 9, "onGestureEvent"], [156, 28, 398, 23], [156, 31, 398, 26, "Animated"], [156, 52, 398, 34], [156, 53, 398, 35, "event"], [156, 58, 398, 40], [156, 59, 399, 6], [156, 60, 399, 7], [157, 10, 399, 9, "nativeEvent"], [157, 21, 399, 20], [157, 23, 399, 22], [158, 12, 399, 24, "translationX"], [158, 24, 399, 36], [158, 26, 399, 38, "dragXValue"], [158, 36, 399, 48], [159, 12, 399, 50, "x"], [159, 13, 399, 51], [159, 15, 399, 53, "touchXValue"], [160, 10, 399, 65], [161, 8, 399, 67], [161, 9, 399, 68], [161, 10, 399, 69], [161, 12, 400, 6, "gestureOptions"], [161, 26, 401, 4], [161, 27, 401, 5], [162, 6, 402, 2], [162, 7, 402, 3], [163, 6, 402, 3, "_this"], [163, 11, 402, 3], [163, 12, 404, 10, "handleContainerLayout"], [163, 33, 404, 31], [163, 36, 404, 34, "_ref"], [163, 40, 404, 34], [163, 44, 404, 74], [164, 8, 404, 74], [164, 12, 404, 37, "nativeEvent"], [164, 23, 404, 48], [164, 26, 404, 48, "_ref"], [164, 30, 404, 48], [164, 31, 404, 37, "nativeEvent"], [164, 42, 404, 48], [165, 8, 405, 4, "_this"], [165, 13, 405, 4], [165, 14, 405, 9, "setState"], [165, 22, 405, 17], [165, 23, 405, 18], [166, 10, 405, 20, "containerWidth"], [166, 24, 405, 34], [166, 26, 405, 36, "nativeEvent"], [166, 37, 405, 47], [166, 38, 405, 48, "layout"], [166, 44, 405, 54], [166, 45, 405, 55, "width"], [167, 8, 405, 61], [167, 9, 405, 62], [167, 10, 405, 63], [168, 6, 406, 2], [168, 7, 406, 3], [169, 6, 406, 3, "_this"], [169, 11, 406, 3], [169, 12, 408, 10, "emitStateChanged"], [169, 28, 408, 26], [169, 31, 408, 29], [169, 32, 409, 4, "newState"], [169, 40, 409, 25], [169, 42, 410, 4, "drawerWillShow"], [169, 56, 410, 27], [169, 61, 411, 7], [170, 8, 412, 4, "_this"], [170, 13, 412, 4], [170, 14, 412, 9, "props"], [170, 19, 412, 14], [170, 20, 412, 15, "onDrawerStateChanged"], [170, 40, 412, 35], [170, 43, 412, 38, "newState"], [170, 51, 412, 46], [170, 53, 412, 48, "drawerWillShow"], [170, 67, 412, 62], [170, 68, 412, 63], [171, 6, 413, 2], [171, 7, 413, 3], [172, 6, 413, 3, "_this"], [172, 11, 413, 3], [172, 12, 415, 10, "openingHandlerStateChange"], [172, 37, 415, 35], [172, 40, 415, 38, "_ref2"], [172, 45, 415, 38], [172, 49, 417, 64], [173, 8, 417, 64], [173, 12, 416, 4, "nativeEvent"], [173, 23, 416, 15], [173, 26, 416, 15, "_ref2"], [173, 31, 416, 15], [173, 32, 416, 4, "nativeEvent"], [173, 43, 416, 15], [174, 8, 418, 4], [174, 12, 418, 8, "nativeEvent"], [174, 23, 418, 19], [174, 24, 418, 20, "oldState"], [174, 32, 418, 28], [174, 37, 418, 33, "State"], [174, 49, 418, 38], [174, 50, 418, 39, "ACTIVE"], [174, 56, 418, 45], [174, 58, 418, 47], [175, 10, 419, 6, "_this"], [175, 15, 419, 6], [175, 16, 419, 11, "handleRelease"], [175, 29, 419, 24], [175, 30, 419, 25], [176, 12, 419, 27, "nativeEvent"], [177, 10, 419, 39], [177, 11, 419, 40], [177, 12, 419, 41], [178, 8, 420, 4], [178, 9, 420, 5], [178, 15, 420, 11], [178, 19, 420, 15, "nativeEvent"], [178, 30, 420, 26], [178, 31, 420, 27, "state"], [178, 36, 420, 32], [178, 41, 420, 37, "State"], [178, 53, 420, 42], [178, 54, 420, 43, "ACTIVE"], [178, 60, 420, 49], [178, 62, 420, 51], [179, 10, 421, 6, "_this"], [179, 15, 421, 6], [179, 16, 421, 11, "emitStateChanged"], [179, 32, 421, 27], [179, 33, 421, 28, "DRAGGING"], [179, 41, 421, 36], [179, 43, 421, 38], [179, 48, 421, 43], [179, 49, 421, 44], [180, 10, 422, 6, "_this"], [180, 15, 422, 6], [180, 16, 422, 11, "setState"], [180, 24, 422, 19], [180, 25, 422, 20], [181, 12, 422, 22, "drawerState"], [181, 23, 422, 33], [181, 25, 422, 35, "DRAGGING"], [182, 10, 422, 44], [182, 11, 422, 45], [182, 12, 422, 46], [183, 10, 423, 6], [183, 14, 423, 10, "_this"], [183, 19, 423, 10], [183, 20, 423, 15, "props"], [183, 25, 423, 20], [183, 26, 423, 21, "keyboardDismissMode"], [183, 45, 423, 40], [183, 50, 423, 45], [183, 59, 423, 54], [183, 61, 423, 56], [184, 12, 424, 8, "Keyboard"], [184, 33, 424, 16], [184, 34, 424, 17, "dismiss"], [184, 41, 424, 24], [184, 42, 424, 25], [184, 43, 424, 26], [185, 10, 425, 6], [186, 10, 426, 6], [186, 14, 426, 10, "_this"], [186, 19, 426, 10], [186, 20, 426, 15, "props"], [186, 25, 426, 20], [186, 26, 426, 21, "hideStatusBar"], [186, 39, 426, 34], [186, 41, 426, 36], [187, 12, 427, 8, "StatusBar"], [187, 34, 427, 17], [187, 35, 427, 18, "setHidden"], [187, 44, 427, 27], [187, 45, 427, 28], [187, 49, 427, 32], [187, 51, 427, 34, "_this"], [187, 56, 427, 34], [187, 57, 427, 39, "props"], [187, 62, 427, 44], [187, 63, 427, 45, "statusBarAnimation"], [187, 81, 427, 63], [187, 85, 427, 67], [187, 92, 427, 74], [187, 93, 427, 75], [188, 10, 428, 6], [189, 8, 429, 4], [190, 6, 430, 2], [190, 7, 430, 3], [191, 6, 430, 3, "_this"], [191, 11, 430, 3], [191, 12, 432, 10, "onTapHandlerStateChange"], [191, 35, 432, 33], [191, 38, 432, 36, "_ref3"], [191, 43, 432, 36], [191, 47, 434, 64], [192, 8, 434, 64], [192, 12, 433, 4, "nativeEvent"], [192, 23, 433, 15], [192, 26, 433, 15, "_ref3"], [192, 31, 433, 15], [192, 32, 433, 4, "nativeEvent"], [192, 43, 433, 15], [193, 8, 435, 4], [193, 12, 436, 6, "_this"], [193, 17, 436, 6], [193, 18, 436, 11, "drawerShown"], [193, 29, 436, 22], [193, 33, 437, 6, "nativeEvent"], [193, 44, 437, 17], [193, 45, 437, 18, "oldState"], [193, 53, 437, 26], [193, 58, 437, 31, "State"], [193, 70, 437, 36], [193, 71, 437, 37, "ACTIVE"], [193, 77, 437, 43], [193, 81, 438, 6, "_this"], [193, 86, 438, 6], [193, 87, 438, 11, "props"], [193, 92, 438, 16], [193, 93, 438, 17, "drawerLockMode"], [193, 107, 438, 31], [193, 112, 438, 36], [193, 125, 438, 49], [193, 127, 439, 6], [194, 10, 440, 6, "_this"], [194, 15, 440, 6], [194, 16, 440, 11, "closeDrawer"], [194, 27, 440, 22], [194, 28, 440, 23], [194, 29, 440, 24], [195, 8, 441, 4], [196, 6, 442, 2], [196, 7, 442, 3], [197, 6, 442, 3, "_this"], [197, 11, 442, 3], [197, 12, 444, 10, "handleRelease"], [197, 25, 444, 23], [197, 28, 444, 26, "_ref4"], [197, 33, 444, 26], [197, 37, 446, 64], [198, 8, 446, 64], [198, 12, 445, 4, "nativeEvent"], [198, 23, 445, 15], [198, 26, 445, 15, "_ref4"], [198, 31, 445, 15], [198, 32, 445, 4, "nativeEvent"], [198, 43, 445, 15], [199, 8, 447, 4], [199, 12, 447, 4, "_this$props"], [199, 23, 447, 4], [199, 26, 447, 56, "_this"], [199, 31, 447, 56], [199, 32, 447, 61, "props"], [199, 37, 447, 66], [200, 10, 447, 12, "drawerWidth"], [200, 21, 447, 23], [200, 24, 447, 23, "_this$props"], [200, 35, 447, 23], [200, 36, 447, 12, "drawerWidth"], [200, 47, 447, 23], [201, 10, 447, 25, "drawerPosition"], [201, 24, 447, 39], [201, 27, 447, 39, "_this$props"], [201, 38, 447, 39], [201, 39, 447, 25, "drawerPosition"], [201, 53, 447, 39], [202, 10, 447, 41, "drawerType"], [202, 20, 447, 51], [202, 23, 447, 51, "_this$props"], [202, 34, 447, 51], [202, 35, 447, 41, "drawerType"], [202, 45, 447, 51], [203, 8, 448, 4], [203, 12, 448, 12, "containerWidth"], [203, 26, 448, 26], [203, 29, 448, 31, "_this"], [203, 34, 448, 31], [203, 35, 448, 36, "state"], [203, 40, 448, 41], [203, 41, 448, 12, "containerWidth"], [203, 55, 448, 26], [204, 8, 449, 4], [204, 12, 449, 24, "dragX"], [204, 17, 449, 29], [204, 20, 449, 56, "nativeEvent"], [204, 31, 449, 67], [204, 32, 449, 10, "translationX"], [204, 44, 449, 22], [205, 10, 449, 31, "velocityX"], [205, 19, 449, 40], [205, 22, 449, 56, "nativeEvent"], [205, 33, 449, 67], [205, 34, 449, 31, "velocityX"], [205, 43, 449, 40], [206, 10, 449, 45, "touchX"], [206, 16, 449, 51], [206, 19, 449, 56, "nativeEvent"], [206, 30, 449, 67], [206, 31, 449, 42, "x"], [206, 32, 449, 43], [207, 8, 451, 4], [207, 12, 451, 8, "drawerPosition"], [207, 26, 451, 22], [207, 31, 451, 27], [207, 37, 451, 33], [207, 39, 451, 35], [208, 10, 452, 6], [209, 10, 453, 6], [210, 10, 454, 6, "dragX"], [210, 15, 454, 11], [210, 18, 454, 14], [210, 19, 454, 15, "dragX"], [210, 24, 454, 20], [211, 10, 455, 6, "touchX"], [211, 16, 455, 12], [211, 19, 455, 15, "containerWidth"], [211, 33, 455, 29], [211, 36, 455, 32, "touchX"], [211, 42, 455, 38], [212, 10, 456, 6, "velocityX"], [212, 19, 456, 15], [212, 22, 456, 18], [212, 23, 456, 19, "velocityX"], [212, 32, 456, 28], [213, 8, 457, 4], [214, 8, 459, 4], [214, 12, 459, 10, "gestureStartX"], [214, 25, 459, 23], [214, 28, 459, 26, "touchX"], [214, 34, 459, 32], [214, 37, 459, 35, "dragX"], [214, 42, 459, 40], [215, 8, 460, 4], [215, 12, 460, 8, "dragOffsetBasedOnStart"], [215, 34, 460, 30], [215, 37, 460, 33], [215, 38, 460, 34], [216, 8, 462, 4], [216, 12, 462, 8, "drawerType"], [216, 22, 462, 18], [216, 27, 462, 23], [216, 34, 462, 30], [216, 36, 462, 32], [217, 10, 463, 6, "dragOffsetBasedOnStart"], [217, 32, 463, 28], [217, 35, 464, 8, "gestureStartX"], [217, 48, 464, 21], [217, 51, 464, 24, "drawerWidth"], [217, 62, 464, 36], [217, 65, 464, 39, "gestureStartX"], [217, 78, 464, 52], [217, 81, 464, 55, "drawerWidth"], [217, 92, 464, 67], [217, 95, 464, 70], [217, 96, 464, 71], [218, 8, 465, 4], [219, 8, 467, 4], [219, 12, 467, 10, "startOffsetX"], [219, 24, 467, 22], [219, 27, 468, 6, "dragX"], [219, 32, 468, 11], [219, 35, 468, 14, "dragOffsetBasedOnStart"], [219, 57, 468, 36], [219, 61, 468, 40, "_this"], [219, 66, 468, 40], [219, 67, 468, 45, "drawerShown"], [219, 78, 468, 56], [219, 81, 468, 59, "drawerWidth"], [219, 92, 468, 70], [219, 95, 468, 74], [219, 96, 468, 75], [219, 97, 468, 76], [220, 8, 469, 4], [220, 12, 469, 10, "projOffsetX"], [220, 23, 469, 21], [220, 26, 469, 24, "startOffsetX"], [220, 38, 469, 36], [220, 41, 469, 39, "DRAG_TOSS"], [220, 50, 469, 48], [220, 53, 469, 51, "velocityX"], [220, 62, 469, 60], [221, 8, 471, 4], [221, 12, 471, 10, "shouldOpen"], [221, 22, 471, 20], [221, 25, 471, 23, "projOffsetX"], [221, 36, 471, 34], [221, 39, 471, 37, "drawerWidth"], [221, 50, 471, 48], [221, 53, 471, 52], [221, 54, 471, 53], [222, 8, 473, 4], [222, 12, 473, 8, "shouldOpen"], [222, 22, 473, 18], [222, 24, 473, 20], [223, 10, 474, 6, "_this"], [223, 15, 474, 6], [223, 16, 474, 11, "animateDrawer"], [223, 29, 474, 24], [223, 30, 474, 25, "startOffsetX"], [223, 42, 474, 37], [223, 44, 474, 39, "drawerWidth"], [223, 55, 474, 50], [223, 57, 474, 53, "velocityX"], [223, 66, 474, 62], [223, 67, 474, 63], [224, 8, 475, 4], [224, 9, 475, 5], [224, 15, 475, 11], [225, 10, 476, 6, "_this"], [225, 15, 476, 6], [225, 16, 476, 11, "animateDrawer"], [225, 29, 476, 24], [225, 30, 476, 25, "startOffsetX"], [225, 42, 476, 37], [225, 44, 476, 39], [225, 45, 476, 40], [225, 47, 476, 42, "velocityX"], [225, 56, 476, 51], [225, 57, 476, 52], [226, 8, 477, 4], [227, 6, 478, 2], [227, 7, 478, 3], [228, 6, 478, 3, "_this"], [228, 11, 478, 3], [228, 12, 480, 10, "updateShowing"], [228, 25, 480, 23], [228, 28, 480, 27, "showing"], [228, 35, 480, 43], [228, 39, 480, 48], [229, 8, 481, 4, "_this"], [229, 13, 481, 4], [229, 14, 481, 9, "drawerShown"], [229, 25, 481, 20], [229, 28, 481, 23, "showing"], [229, 35, 481, 30], [230, 8, 482, 4, "_this"], [230, 13, 482, 4], [230, 14, 482, 9, "accessibilityIsModalView"], [230, 38, 482, 33], [230, 39, 482, 34, "current"], [230, 46, 482, 41], [230, 48, 482, 43, "setNativeProps"], [230, 62, 482, 57], [230, 63, 482, 58], [231, 10, 483, 6, "accessibilityViewIsModal"], [231, 34, 483, 30], [231, 36, 483, 32, "showing"], [232, 8, 484, 4], [232, 9, 484, 5], [232, 10, 484, 6], [233, 8, 485, 4, "_this"], [233, 13, 485, 4], [233, 14, 485, 9, "pointerEventsView"], [233, 31, 485, 26], [233, 32, 485, 27, "current"], [233, 39, 485, 34], [233, 41, 485, 36, "setNativeProps"], [233, 55, 485, 50], [233, 56, 485, 51], [234, 10, 486, 6, "pointerEvents"], [234, 23, 486, 19], [234, 25, 486, 21, "showing"], [234, 32, 486, 28], [234, 35, 486, 31], [234, 41, 486, 37], [234, 44, 486, 40], [235, 8, 487, 4], [235, 9, 487, 5], [235, 10, 487, 6], [236, 8, 488, 4], [236, 12, 488, 4, "_this$props2"], [236, 24, 488, 4], [236, 27, 488, 60, "_this"], [236, 32, 488, 60], [236, 33, 488, 65, "props"], [236, 38, 488, 70], [237, 10, 488, 12, "drawerPosition"], [237, 24, 488, 26], [237, 27, 488, 26, "_this$props2"], [237, 39, 488, 26], [237, 40, 488, 12, "drawerPosition"], [237, 54, 488, 26], [238, 10, 488, 28, "minSwipeDistance"], [238, 26, 488, 44], [238, 29, 488, 44, "_this$props2"], [238, 41, 488, 44], [238, 42, 488, 28, "minSwipeDistance"], [238, 58, 488, 44], [239, 10, 488, 46, "edgeWidth"], [239, 19, 488, 55], [239, 22, 488, 55, "_this$props2"], [239, 34, 488, 55], [239, 35, 488, 46, "edgeWidth"], [239, 44, 488, 55], [240, 8, 489, 4], [240, 12, 489, 10, "fromLeft"], [240, 20, 489, 18], [240, 23, 489, 21, "drawerPosition"], [240, 37, 489, 35], [240, 42, 489, 40], [240, 48, 489, 46], [241, 8, 490, 4], [242, 8, 491, 4], [243, 8, 492, 4], [244, 8, 493, 4], [244, 12, 493, 10, "gestureOrientation"], [244, 30, 493, 28], [244, 33, 494, 6], [244, 34, 494, 7, "fromLeft"], [244, 42, 494, 15], [244, 45, 494, 18], [244, 46, 494, 19], [244, 49, 494, 22], [244, 50, 494, 23], [244, 51, 494, 24], [244, 56, 494, 29, "_this"], [244, 61, 494, 29], [244, 62, 494, 34, "drawerShown"], [244, 73, 494, 45], [244, 76, 494, 48], [244, 77, 494, 49], [244, 78, 494, 50], [244, 81, 494, 53], [244, 82, 494, 54], [244, 83, 494, 55], [245, 8, 495, 4], [246, 8, 496, 4], [247, 8, 497, 4], [248, 8, 498, 4], [248, 12, 498, 10, "hitSlop"], [248, 19, 498, 17], [248, 22, 498, 20, "fromLeft"], [248, 30, 498, 28], [248, 33, 499, 8], [249, 10, 499, 10, "left"], [249, 14, 499, 14], [249, 16, 499, 16], [249, 17, 499, 17], [250, 10, 499, 19, "width"], [250, 15, 499, 24], [250, 17, 499, 26, "showing"], [250, 24, 499, 33], [250, 27, 499, 36, "undefined"], [250, 36, 499, 45], [250, 39, 499, 48, "edgeWidth"], [251, 8, 499, 58], [251, 9, 499, 59], [251, 12, 500, 8], [252, 10, 500, 10, "right"], [252, 15, 500, 15], [252, 17, 500, 17], [252, 18, 500, 18], [253, 10, 500, 20, "width"], [253, 15, 500, 25], [253, 17, 500, 27, "showing"], [253, 24, 500, 34], [253, 27, 500, 37, "undefined"], [253, 36, 500, 46], [253, 39, 500, 49, "edgeWidth"], [254, 8, 500, 59], [254, 9, 500, 60], [255, 8, 501, 4], [256, 8, 502, 4, "_this"], [256, 13, 502, 4], [256, 14, 502, 9, "panGestureHandler"], [256, 31, 502, 26], [256, 32, 502, 27, "current"], [256, 39, 502, 34], [256, 41, 502, 36, "setNativeProps"], [256, 55, 502, 50], [256, 56, 502, 51], [257, 10, 503, 6, "hitSlop"], [257, 17, 503, 13], [258, 10, 504, 6, "activeOffsetX"], [258, 23, 504, 19], [258, 25, 504, 21, "gestureOrientation"], [258, 43, 504, 39], [258, 46, 504, 42, "minSwipeDistance"], [259, 8, 505, 4], [259, 9, 505, 5], [259, 10, 505, 6], [260, 6, 506, 2], [260, 7, 506, 3], [261, 6, 506, 3, "_this"], [261, 11, 506, 3], [261, 12, 508, 10, "animateDrawer"], [261, 25, 508, 23], [261, 28, 508, 26], [261, 29, 509, 4, "fromValue"], [261, 38, 509, 40], [261, 40, 510, 4, "toValue"], [261, 47, 510, 19], [261, 49, 511, 4, "velocity"], [261, 57, 511, 20], [261, 59, 512, 4, "speed"], [261, 64, 512, 18], [261, 69, 513, 7], [262, 8, 514, 4, "_this"], [262, 13, 514, 4], [262, 14, 514, 9, "state"], [262, 19, 514, 14], [262, 20, 514, 15, "dragX"], [262, 25, 514, 20], [262, 26, 514, 21, "setValue"], [262, 34, 514, 29], [262, 35, 514, 30], [262, 36, 514, 31], [262, 37, 514, 32], [263, 8, 515, 4, "_this"], [263, 13, 515, 4], [263, 14, 515, 9, "state"], [263, 19, 515, 14], [263, 20, 515, 15, "touchX"], [263, 26, 515, 21], [263, 27, 515, 22, "setValue"], [263, 35, 515, 30], [263, 36, 516, 6, "_this"], [263, 41, 516, 6], [263, 42, 516, 11, "props"], [263, 47, 516, 16], [263, 48, 516, 17, "drawerPosition"], [263, 62, 516, 31], [263, 67, 516, 36], [263, 73, 516, 42], [263, 76, 516, 45], [263, 77, 516, 46], [263, 80, 516, 49, "_this"], [263, 85, 516, 49], [263, 86, 516, 54, "state"], [263, 91, 516, 59], [263, 92, 516, 60, "containerWidth"], [263, 106, 517, 4], [263, 107, 517, 5], [264, 8, 519, 4], [264, 12, 519, 8, "fromValue"], [264, 21, 519, 17], [264, 25, 519, 21], [264, 29, 519, 25], [264, 31, 519, 27], [265, 10, 520, 6], [265, 14, 520, 10, "nextFramePosition"], [265, 31, 520, 27], [265, 34, 520, 30, "fromValue"], [265, 43, 520, 39], [266, 10, 521, 6], [266, 14, 521, 10, "_this"], [266, 19, 521, 10], [266, 20, 521, 15, "props"], [266, 25, 521, 20], [266, 26, 521, 21, "useNativeAnimations"], [266, 45, 521, 40], [266, 47, 521, 42], [267, 12, 522, 8], [268, 12, 523, 8], [269, 12, 524, 8], [270, 12, 525, 8], [271, 12, 526, 8], [271, 16, 526, 12, "fromValue"], [271, 25, 526, 21], [271, 28, 526, 24, "toValue"], [271, 35, 526, 31], [271, 39, 526, 35, "velocity"], [271, 47, 526, 43], [271, 50, 526, 46], [271, 51, 526, 47], [271, 53, 526, 49], [272, 14, 527, 10, "nextFramePosition"], [272, 31, 527, 27], [272, 34, 527, 30, "Math"], [272, 38, 527, 34], [272, 39, 527, 35, "min"], [272, 42, 527, 38], [272, 43, 527, 39, "fromValue"], [272, 52, 527, 48], [272, 55, 527, 51, "velocity"], [272, 63, 527, 59], [272, 66, 527, 62], [272, 70, 527, 66], [272, 72, 527, 68, "toValue"], [272, 79, 527, 75], [272, 80, 527, 76], [273, 12, 528, 8], [273, 13, 528, 9], [273, 19, 528, 15], [273, 23, 528, 19, "fromValue"], [273, 32, 528, 28], [273, 35, 528, 31, "toValue"], [273, 42, 528, 38], [273, 46, 528, 42, "velocity"], [273, 54, 528, 50], [273, 57, 528, 53], [273, 58, 528, 54], [273, 60, 528, 56], [274, 14, 529, 10, "nextFramePosition"], [274, 31, 529, 27], [274, 34, 529, 30, "Math"], [274, 38, 529, 34], [274, 39, 529, 35, "max"], [274, 42, 529, 38], [274, 43, 529, 39, "fromValue"], [274, 52, 529, 48], [274, 55, 529, 51, "velocity"], [274, 63, 529, 59], [274, 66, 529, 62], [274, 70, 529, 66], [274, 72, 529, 68, "toValue"], [274, 79, 529, 75], [274, 80, 529, 76], [275, 12, 530, 8], [276, 10, 531, 6], [277, 10, 532, 6, "_this"], [277, 15, 532, 6], [277, 16, 532, 11, "state"], [277, 21, 532, 16], [277, 22, 532, 17, "drawerTranslation"], [277, 39, 532, 34], [277, 40, 532, 35, "setValue"], [277, 48, 532, 43], [277, 49, 532, 44, "nextFramePosition"], [277, 66, 532, 61], [277, 67, 532, 62], [278, 8, 533, 4], [279, 8, 535, 4], [279, 12, 535, 10, "willShow"], [279, 20, 535, 18], [279, 23, 535, 21, "toValue"], [279, 30, 535, 28], [279, 35, 535, 33], [279, 36, 535, 34], [280, 8, 536, 4, "_this"], [280, 13, 536, 4], [280, 14, 536, 9, "updateShowing"], [280, 27, 536, 22], [280, 28, 536, 23, "willShow"], [280, 36, 536, 31], [280, 37, 536, 32], [281, 8, 537, 4, "_this"], [281, 13, 537, 4], [281, 14, 537, 9, "emitStateChanged"], [281, 30, 537, 25], [281, 31, 537, 26, "SETTLING"], [281, 39, 537, 34], [281, 41, 537, 36, "willShow"], [281, 49, 537, 44], [281, 50, 537, 45], [282, 8, 538, 4, "_this"], [282, 13, 538, 4], [282, 14, 538, 9, "setState"], [282, 22, 538, 17], [282, 23, 538, 18], [283, 10, 538, 20, "drawerState"], [283, 21, 538, 31], [283, 23, 538, 33, "SETTLING"], [284, 8, 538, 42], [284, 9, 538, 43], [284, 10, 538, 44], [285, 8, 539, 4], [285, 12, 539, 8, "_this"], [285, 17, 539, 8], [285, 18, 539, 13, "props"], [285, 23, 539, 18], [285, 24, 539, 19, "hideStatusBar"], [285, 37, 539, 32], [285, 39, 539, 34], [286, 10, 540, 6, "StatusBar"], [286, 32, 540, 15], [286, 33, 540, 16, "setHidden"], [286, 42, 540, 25], [286, 43, 540, 26, "willShow"], [286, 51, 540, 34], [286, 53, 540, 36, "_this"], [286, 58, 540, 36], [286, 59, 540, 41, "props"], [286, 64, 540, 46], [286, 65, 540, 47, "statusBarAnimation"], [286, 83, 540, 65], [286, 87, 540, 69], [286, 94, 540, 76], [286, 95, 540, 77], [287, 8, 541, 4], [288, 8, 542, 4, "Animated"], [288, 29, 542, 12], [288, 30, 542, 13, "spring"], [288, 36, 542, 19], [288, 37, 542, 20, "_this"], [288, 42, 542, 20], [288, 43, 542, 25, "state"], [288, 48, 542, 30], [288, 49, 542, 31, "drawerTranslation"], [288, 66, 542, 48], [288, 68, 542, 50], [289, 10, 543, 6, "velocity"], [289, 18, 543, 14], [290, 10, 544, 6, "bounciness"], [290, 20, 544, 16], [290, 22, 544, 18], [290, 23, 544, 19], [291, 10, 545, 6, "toValue"], [291, 17, 545, 13], [292, 10, 546, 6, "useNativeDriver"], [292, 25, 546, 21], [292, 27, 546, 23, "_this"], [292, 32, 546, 23], [292, 33, 546, 28, "props"], [292, 38, 546, 33], [292, 39, 546, 34, "useNativeAnimations"], [292, 58, 546, 54], [293, 10, 547, 6, "speed"], [293, 15, 547, 11], [293, 17, 547, 13, "speed"], [293, 22, 547, 18], [293, 26, 547, 22, "undefined"], [294, 8, 548, 4], [294, 9, 548, 5], [294, 10, 548, 6], [294, 11, 548, 7, "start"], [294, 16, 548, 12], [294, 17, 548, 13, "_ref5"], [294, 22, 548, 13], [294, 26, 548, 31], [295, 10, 548, 31], [295, 14, 548, 16, "finished"], [295, 22, 548, 24], [295, 25, 548, 24, "_ref5"], [295, 30, 548, 24], [295, 31, 548, 16, "finished"], [295, 39, 548, 24], [296, 10, 549, 6], [296, 14, 549, 10, "finished"], [296, 22, 549, 18], [296, 24, 549, 20], [297, 12, 550, 8, "_this"], [297, 17, 550, 8], [297, 18, 550, 13, "emitStateChanged"], [297, 34, 550, 29], [297, 35, 550, 30, "IDLE"], [297, 39, 550, 34], [297, 41, 550, 36, "willShow"], [297, 49, 550, 44], [297, 50, 550, 45], [298, 12, 551, 8, "_this"], [298, 17, 551, 8], [298, 18, 551, 13, "setState"], [298, 26, 551, 21], [298, 27, 551, 22], [299, 14, 551, 24, "drawerOpened"], [299, 26, 551, 36], [299, 28, 551, 38, "willShow"], [300, 12, 551, 47], [300, 13, 551, 48], [300, 14, 551, 49], [301, 12, 552, 8], [301, 16, 552, 12, "_this"], [301, 21, 552, 12], [301, 22, 552, 17, "state"], [301, 27, 552, 22], [301, 28, 552, 23, "drawerState"], [301, 39, 552, 34], [301, 44, 552, 39, "DRAGGING"], [301, 52, 552, 47], [301, 54, 552, 49], [302, 14, 553, 10], [303, 14, 554, 10], [304, 14, 555, 10, "_this"], [304, 19, 555, 10], [304, 20, 555, 15, "setState"], [304, 28, 555, 23], [304, 29, 555, 24], [305, 16, 555, 26, "drawerState"], [305, 27, 555, 37], [305, 29, 555, 39, "IDLE"], [306, 14, 555, 44], [306, 15, 555, 45], [306, 16, 555, 46], [307, 12, 556, 8], [308, 12, 557, 8], [308, 16, 557, 12, "willShow"], [308, 24, 557, 20], [308, 26, 557, 22], [309, 14, 558, 10, "_this"], [309, 19, 558, 10], [309, 20, 558, 15, "props"], [309, 25, 558, 20], [309, 26, 558, 21, "onDrawerOpen"], [309, 38, 558, 33], [309, 41, 558, 36], [309, 42, 558, 37], [310, 12, 559, 8], [310, 13, 559, 9], [310, 19, 559, 15], [311, 14, 560, 10, "_this"], [311, 19, 560, 10], [311, 20, 560, 15, "props"], [311, 25, 560, 20], [311, 26, 560, 21, "onDrawerClose"], [311, 39, 560, 34], [311, 42, 560, 37], [311, 43, 560, 38], [312, 12, 561, 8], [313, 10, 562, 6], [314, 8, 563, 4], [314, 9, 563, 5], [314, 10, 563, 6], [315, 6, 564, 2], [315, 7, 564, 3], [316, 6, 566, 2], [317, 6, 566, 2, "_this"], [317, 11, 566, 2], [317, 12, 567, 2, "openDrawer"], [317, 22, 567, 12], [317, 25, 567, 15], [317, 37, 567, 55], [318, 8, 567, 55], [318, 12, 567, 16, "options"], [318, 19, 567, 45], [318, 22, 567, 45, "arguments"], [318, 31, 567, 45], [318, 32, 567, 45, "length"], [318, 38, 567, 45], [318, 46, 567, 45, "arguments"], [318, 55, 567, 45], [318, 63, 567, 45, "undefined"], [318, 72, 567, 45], [318, 75, 567, 45, "arguments"], [318, 84, 567, 45], [318, 90, 567, 48], [318, 91, 567, 49], [318, 92, 567, 50], [319, 8, 568, 4, "_this"], [319, 13, 568, 4], [319, 14, 568, 9, "animateDrawer"], [319, 27, 568, 22], [320, 8, 569, 6], [321, 8, 570, 6, "undefined"], [321, 17, 570, 15], [321, 19, 571, 6, "_this"], [321, 24, 571, 6], [321, 25, 571, 11, "props"], [321, 30, 571, 16], [321, 31, 571, 17, "drawerWidth"], [321, 42, 571, 28], [321, 44, 572, 6, "options"], [321, 51, 572, 13], [321, 52, 572, 14, "velocity"], [321, 60, 572, 22], [321, 63, 572, 25, "options"], [321, 70, 572, 32], [321, 71, 572, 33, "velocity"], [321, 79, 572, 41], [321, 82, 572, 44], [321, 83, 572, 45], [321, 85, 573, 6, "options"], [321, 92, 573, 13], [321, 93, 573, 14, "speed"], [321, 98, 574, 4], [321, 99, 574, 5], [323, 8, 576, 4], [324, 8, 577, 4], [325, 8, 578, 4, "_this"], [325, 13, 578, 4], [325, 14, 578, 9, "forceUpdate"], [325, 25, 578, 20], [325, 26, 578, 21], [325, 27, 578, 22], [326, 6, 579, 2], [326, 7, 579, 3], [327, 6, 579, 3, "_this"], [327, 11, 579, 3], [327, 12, 581, 2, "closeDrawer"], [327, 23, 581, 13], [327, 26, 581, 16], [327, 38, 581, 56], [328, 8, 581, 56], [328, 12, 581, 17, "options"], [328, 19, 581, 46], [328, 22, 581, 46, "arguments"], [328, 31, 581, 46], [328, 32, 581, 46, "length"], [328, 38, 581, 46], [328, 46, 581, 46, "arguments"], [328, 55, 581, 46], [328, 63, 581, 46, "undefined"], [328, 72, 581, 46], [328, 75, 581, 46, "arguments"], [328, 84, 581, 46], [328, 90, 581, 49], [328, 91, 581, 50], [328, 92, 581, 51], [329, 8, 582, 4], [330, 8, 583, 4, "_this"], [330, 13, 583, 4], [330, 14, 583, 9, "animateDrawer"], [330, 27, 583, 22], [330, 28, 584, 6, "undefined"], [330, 37, 584, 15], [330, 39, 585, 6], [330, 40, 585, 7], [330, 42, 586, 6, "options"], [330, 49, 586, 13], [330, 50, 586, 14, "velocity"], [330, 58, 586, 22], [330, 61, 586, 25, "options"], [330, 68, 586, 32], [330, 69, 586, 33, "velocity"], [330, 77, 586, 41], [330, 80, 586, 44], [330, 81, 586, 45], [330, 83, 587, 6, "options"], [330, 90, 587, 13], [330, 91, 587, 14, "speed"], [330, 96, 588, 4], [330, 97, 588, 5], [332, 8, 590, 4], [333, 8, 591, 4], [334, 8, 592, 4, "_this"], [334, 13, 592, 4], [334, 14, 592, 9, "forceUpdate"], [334, 25, 592, 20], [334, 26, 592, 21], [334, 27, 592, 22], [335, 6, 593, 2], [335, 7, 593, 3], [336, 6, 593, 3, "_this"], [336, 11, 593, 3], [336, 12, 595, 10, "renderOverlay"], [336, 25, 595, 23], [336, 28, 595, 26], [336, 34, 595, 32], [337, 8, 596, 4], [338, 8, 597, 4], [338, 12, 597, 4, "invariant"], [338, 30, 597, 13], [338, 32, 597, 14, "_this"], [338, 37, 597, 14], [338, 38, 597, 19, "openValue"], [338, 47, 597, 28], [338, 49, 597, 30], [338, 64, 597, 45], [338, 65, 597, 46], [339, 8, 598, 4], [339, 12, 598, 8, "overlayOpacity"], [339, 26, 598, 22], [340, 8, 600, 4], [340, 12, 600, 8, "_this"], [340, 17, 600, 8], [340, 18, 600, 13, "state"], [340, 23, 600, 18], [340, 24, 600, 19, "drawerState"], [340, 35, 600, 30], [340, 40, 600, 35, "IDLE"], [340, 44, 600, 39], [340, 46, 600, 41], [341, 10, 601, 6, "overlayOpacity"], [341, 24, 601, 20], [341, 27, 601, 23, "_this"], [341, 32, 601, 23], [341, 33, 601, 28, "openValue"], [341, 42, 601, 37], [342, 8, 602, 4], [342, 9, 602, 5], [342, 15, 602, 11], [343, 10, 603, 6, "overlayOpacity"], [343, 24, 603, 20], [343, 27, 603, 23, "_this"], [343, 32, 603, 23], [343, 33, 603, 28, "state"], [343, 38, 603, 33], [343, 39, 603, 34, "drawerOpened"], [343, 51, 603, 46], [343, 54, 603, 49], [343, 55, 603, 50], [343, 58, 603, 53], [343, 59, 603, 54], [344, 8, 604, 4], [345, 8, 606, 4], [345, 12, 606, 10, "dynamicOverlayStyles"], [345, 32, 606, 30], [345, 35, 606, 33], [346, 10, 607, 6, "opacity"], [346, 17, 607, 13], [346, 19, 607, 15, "overlayOpacity"], [346, 33, 607, 29], [347, 10, 608, 6, "backgroundColor"], [347, 25, 608, 21], [347, 27, 608, 23, "_this"], [347, 32, 608, 23], [347, 33, 608, 28, "props"], [347, 38, 608, 33], [347, 39, 608, 34, "overlayColor"], [348, 8, 609, 4], [348, 9, 609, 5], [349, 8, 611, 4], [349, 28, 612, 6], [349, 32, 612, 6, "_jsxDevRuntime"], [349, 46, 612, 6], [349, 47, 612, 6, "jsxDEV"], [349, 53, 612, 6], [349, 55, 612, 7, "_TapGestureHandler"], [349, 73, 612, 7], [349, 74, 612, 7, "TapGestureHandler"], [349, 91, 612, 24], [350, 10, 612, 25, "onHandlerStateChange"], [350, 30, 612, 45], [350, 32, 612, 47, "_this"], [350, 37, 612, 47], [350, 38, 612, 52, "onTapHandlerStateChange"], [350, 61, 612, 76], [351, 10, 612, 76, "children"], [351, 18, 612, 76], [351, 33, 613, 8], [351, 37, 613, 8, "_jsxDevRuntime"], [351, 51, 613, 8], [351, 52, 613, 8, "jsxDEV"], [351, 58, 613, 8], [351, 60, 613, 9, "_reactNative"], [351, 72, 613, 9], [351, 73, 613, 9, "Animated"], [351, 81, 613, 17], [351, 82, 613, 18, "View"], [351, 86, 613, 22], [352, 12, 614, 10, "pointerEvents"], [352, 25, 614, 23], [352, 27, 614, 25, "_this"], [352, 32, 614, 25], [352, 33, 614, 30, "drawerShown"], [352, 44, 614, 41], [352, 47, 614, 44], [352, 53, 614, 50], [352, 56, 614, 53], [352, 62, 614, 60], [353, 12, 615, 10, "ref"], [353, 15, 615, 13], [353, 17, 615, 15, "_this"], [353, 22, 615, 15], [353, 23, 615, 20, "pointerEventsView"], [353, 40, 615, 38], [354, 12, 616, 10, "style"], [354, 17, 616, 15], [354, 19, 616, 17], [354, 20, 616, 18, "styles"], [354, 26, 616, 24], [354, 27, 616, 25, "overlay"], [354, 34, 616, 32], [354, 36, 616, 34, "dynamicOverlayStyles"], [354, 56, 616, 54], [355, 10, 616, 56], [356, 12, 616, 56, "fileName"], [356, 20, 616, 56], [356, 22, 616, 56, "_jsxFileName"], [356, 34, 616, 56], [357, 12, 616, 56, "lineNumber"], [357, 22, 616, 56], [358, 12, 616, 56, "columnNumber"], [358, 24, 616, 56], [359, 10, 616, 56], [359, 13, 616, 56, "_this"], [359, 18, 617, 9], [360, 8, 617, 10], [361, 10, 617, 10, "fileName"], [361, 18, 617, 10], [361, 20, 617, 10, "_jsxFileName"], [361, 32, 617, 10], [362, 10, 617, 10, "lineNumber"], [362, 20, 617, 10], [363, 10, 617, 10, "columnNumber"], [363, 22, 617, 10], [364, 8, 617, 10], [364, 11, 617, 10, "_this"], [364, 16, 618, 25], [364, 17, 618, 26], [365, 6, 620, 2], [365, 7, 620, 3], [366, 6, 620, 3, "_this"], [366, 11, 620, 3], [366, 12, 622, 10, "renderDrawer"], [366, 24, 622, 22], [366, 27, 622, 25], [366, 33, 622, 31], [367, 8, 623, 4], [367, 12, 623, 4, "_this$props3"], [367, 24, 623, 4], [367, 27, 630, 8, "_this"], [367, 32, 630, 8], [367, 33, 630, 13, "props"], [367, 38, 630, 18], [368, 10, 624, 6, "drawerBackgroundColor"], [368, 31, 624, 27], [368, 34, 624, 27, "_this$props3"], [368, 46, 624, 27], [368, 47, 624, 6, "drawerBackgroundColor"], [368, 68, 624, 27], [369, 10, 625, 6, "drawerWidth"], [369, 21, 625, 17], [369, 24, 625, 17, "_this$props3"], [369, 36, 625, 17], [369, 37, 625, 6, "drawerWidth"], [369, 48, 625, 17], [370, 10, 626, 6, "drawerPosition"], [370, 24, 626, 20], [370, 27, 626, 20, "_this$props3"], [370, 39, 626, 20], [370, 40, 626, 6, "drawerPosition"], [370, 54, 626, 20], [371, 10, 627, 6, "drawerType"], [371, 20, 627, 16], [371, 23, 627, 16, "_this$props3"], [371, 35, 627, 16], [371, 36, 627, 6, "drawerType"], [371, 46, 627, 16], [372, 10, 628, 6, "drawerContainerStyle"], [372, 30, 628, 26], [372, 33, 628, 26, "_this$props3"], [372, 45, 628, 26], [372, 46, 628, 6, "drawerContainerStyle"], [372, 66, 628, 26], [373, 10, 629, 6, "contentContainerStyle"], [373, 31, 629, 27], [373, 34, 629, 27, "_this$props3"], [373, 46, 629, 27], [373, 47, 629, 6, "contentContainerStyle"], [373, 68, 629, 27], [374, 8, 632, 4], [374, 12, 632, 10, "fromLeft"], [374, 20, 632, 18], [374, 23, 632, 21, "drawerPosition"], [374, 37, 632, 35], [374, 42, 632, 40], [374, 48, 632, 46], [375, 8, 633, 4], [375, 12, 633, 10, "drawerSlide"], [375, 23, 633, 21], [375, 26, 633, 24, "drawerType"], [375, 36, 633, 34], [375, 41, 633, 39], [375, 47, 633, 45], [376, 8, 634, 4], [376, 12, 634, 10, "containerSlide"], [376, 26, 634, 24], [376, 29, 634, 27, "drawerType"], [376, 39, 634, 37], [376, 44, 634, 42], [376, 51, 634, 49], [378, 8, 636, 4], [379, 8, 637, 4], [380, 8, 638, 4], [381, 8, 639, 4], [382, 8, 640, 4], [382, 12, 640, 10, "reverseContentDirection"], [382, 35, 640, 33], [382, 38, 640, 36, "I18nManager"], [382, 62, 640, 47], [382, 63, 640, 48, "isRTL"], [382, 68, 640, 53], [382, 71, 640, 56, "fromLeft"], [382, 79, 640, 64], [382, 82, 640, 67], [382, 83, 640, 68, "fromLeft"], [382, 91, 640, 76], [383, 8, 642, 4], [383, 12, 642, 10, "dynamicDrawerStyles"], [383, 31, 642, 29], [383, 34, 642, 32], [384, 10, 643, 6, "backgroundColor"], [384, 25, 643, 21], [384, 27, 643, 23, "drawerBackgroundColor"], [384, 48, 643, 44], [385, 10, 644, 6, "width"], [385, 15, 644, 11], [385, 17, 644, 13, "drawerWidth"], [386, 8, 645, 4], [386, 9, 645, 5], [387, 8, 646, 4], [387, 12, 646, 10, "openValue"], [387, 21, 646, 19], [387, 24, 646, 22, "_this"], [387, 29, 646, 22], [387, 30, 646, 27, "openValue"], [387, 39, 646, 36], [388, 8, 647, 4], [388, 12, 647, 4, "invariant"], [388, 30, 647, 13], [388, 32, 647, 14, "openValue"], [388, 41, 647, 23], [388, 43, 647, 25], [388, 58, 647, 40], [388, 59, 647, 41], [389, 8, 649, 4], [389, 12, 649, 8, "containerStyles"], [389, 27, 649, 23], [390, 8, 650, 4], [390, 12, 650, 8, "containerSlide"], [390, 26, 650, 22], [390, 28, 650, 24], [391, 10, 651, 6], [391, 14, 651, 12, "containerTranslateX"], [391, 33, 651, 31], [391, 36, 651, 34, "openValue"], [391, 45, 651, 43], [391, 46, 651, 44, "interpolate"], [391, 57, 651, 55], [391, 58, 651, 56], [392, 12, 652, 8, "inputRange"], [392, 22, 652, 18], [392, 24, 652, 20], [392, 25, 652, 21], [392, 26, 652, 22], [392, 28, 652, 24], [392, 29, 652, 25], [392, 30, 652, 26], [393, 12, 653, 8, "outputRange"], [393, 23, 653, 19], [393, 25, 653, 21, "fromLeft"], [393, 33, 653, 29], [393, 36, 653, 32], [393, 37, 653, 33], [393, 38, 653, 34], [393, 40, 653, 36, "drawerWidth"], [393, 51, 653, 47], [393, 52, 653, 49], [393, 55, 653, 52], [393, 56, 653, 53], [393, 57, 653, 54], [393, 59, 653, 56], [393, 60, 653, 57, "drawerWidth"], [393, 71, 653, 69], [393, 72, 653, 70], [394, 12, 654, 8, "extrapolate"], [394, 23, 654, 19], [394, 25, 654, 21], [395, 10, 655, 6], [395, 11, 655, 7], [395, 12, 655, 8], [396, 10, 656, 6, "containerStyles"], [396, 25, 656, 21], [396, 28, 656, 24], [397, 12, 657, 8, "transform"], [397, 21, 657, 17], [397, 23, 657, 19], [397, 24, 657, 20], [398, 14, 657, 22, "translateX"], [398, 24, 657, 32], [398, 26, 657, 34, "containerTranslateX"], [399, 12, 657, 54], [399, 13, 657, 55], [400, 10, 658, 6], [400, 11, 658, 7], [401, 8, 659, 4], [402, 8, 661, 4], [402, 12, 661, 8, "drawerTranslateX"], [402, 28, 661, 56], [402, 31, 661, 59], [402, 32, 661, 60], [403, 8, 662, 4], [403, 12, 662, 8, "drawerSlide"], [403, 23, 662, 19], [403, 25, 662, 21], [404, 10, 663, 6], [404, 14, 663, 12, "closedDrawerOffset"], [404, 32, 663, 30], [404, 35, 663, 33, "fromLeft"], [404, 43, 663, 41], [404, 46, 663, 44], [404, 47, 663, 45, "drawerWidth"], [404, 58, 663, 57], [404, 61, 663, 60, "drawerWidth"], [404, 72, 663, 72], [405, 10, 664, 6], [405, 14, 664, 10, "_this"], [405, 19, 664, 10], [405, 20, 664, 15, "state"], [405, 25, 664, 20], [405, 26, 664, 21, "drawerState"], [405, 37, 664, 32], [405, 42, 664, 37, "IDLE"], [405, 46, 664, 41], [405, 48, 664, 43], [406, 12, 665, 8, "drawerTranslateX"], [406, 28, 665, 24], [406, 31, 665, 27, "openValue"], [406, 40, 665, 36], [406, 41, 665, 37, "interpolate"], [406, 52, 665, 48], [406, 53, 665, 49], [407, 14, 666, 10, "inputRange"], [407, 24, 666, 20], [407, 26, 666, 22], [407, 27, 666, 23], [407, 28, 666, 24], [407, 30, 666, 26], [407, 31, 666, 27], [407, 32, 666, 28], [408, 14, 667, 10, "outputRange"], [408, 25, 667, 21], [408, 27, 667, 23], [408, 28, 667, 24, "closedDrawerOffset"], [408, 46, 667, 42], [408, 48, 667, 44], [408, 49, 667, 45], [408, 50, 667, 46], [409, 14, 668, 10, "extrapolate"], [409, 25, 668, 21], [409, 27, 668, 23], [410, 12, 669, 8], [410, 13, 669, 9], [410, 14, 669, 10], [411, 10, 670, 6], [411, 11, 670, 7], [411, 17, 670, 13], [412, 12, 671, 8, "drawerTranslateX"], [412, 28, 671, 24], [412, 31, 671, 27, "_this"], [412, 36, 671, 27], [412, 37, 671, 32, "state"], [412, 42, 671, 37], [412, 43, 671, 38, "drawerOpened"], [412, 55, 671, 50], [412, 58, 671, 53], [412, 59, 671, 54], [412, 62, 671, 57, "closedDrawerOffset"], [412, 80, 671, 75], [413, 10, 672, 6], [414, 8, 673, 4], [415, 8, 674, 4], [415, 12, 674, 10, "drawerStyles"], [415, 24, 677, 5], [415, 27, 677, 8], [416, 10, 678, 6, "transform"], [416, 19, 678, 15], [416, 21, 678, 17], [416, 22, 678, 18], [417, 12, 678, 20, "translateX"], [417, 22, 678, 30], [417, 24, 678, 32, "drawerTranslateX"], [418, 10, 678, 49], [418, 11, 678, 50], [418, 12, 678, 51], [419, 10, 679, 6, "flexDirection"], [419, 23, 679, 19], [419, 25, 679, 21, "reverseContentDirection"], [419, 48, 679, 44], [419, 51, 679, 47], [419, 64, 679, 60], [419, 67, 679, 63], [420, 8, 680, 4], [420, 9, 680, 5], [421, 8, 682, 4], [421, 28, 683, 6], [421, 32, 683, 6, "_jsxDevRuntime"], [421, 46, 683, 6], [421, 47, 683, 6, "jsxDEV"], [421, 53, 683, 6], [421, 55, 683, 7, "_reactNative"], [421, 67, 683, 7], [421, 68, 683, 7, "Animated"], [421, 76, 683, 15], [421, 77, 683, 16, "View"], [421, 81, 683, 20], [422, 10, 683, 21, "style"], [422, 15, 683, 26], [422, 17, 683, 28, "styles"], [422, 23, 683, 34], [422, 24, 683, 35, "main"], [422, 28, 683, 40], [423, 10, 683, 41, "onLayout"], [423, 18, 683, 49], [423, 20, 683, 51, "_this"], [423, 25, 683, 51], [423, 26, 683, 56, "handleContainerLayout"], [423, 47, 683, 78], [424, 10, 683, 78, "children"], [424, 18, 683, 78], [424, 34, 684, 8], [424, 38, 684, 8, "_jsxDevRuntime"], [424, 52, 684, 8], [424, 53, 684, 8, "jsxDEV"], [424, 59, 684, 8], [424, 61, 684, 9, "_reactNative"], [424, 73, 684, 9], [424, 74, 684, 9, "Animated"], [424, 82, 684, 17], [424, 83, 684, 18, "View"], [424, 87, 684, 22], [425, 12, 685, 10, "style"], [425, 17, 685, 15], [425, 19, 685, 17], [425, 20, 686, 12, "drawerType"], [425, 30, 686, 22], [425, 35, 686, 27], [425, 42, 686, 34], [425, 45, 687, 16, "styles"], [425, 51, 687, 22], [425, 52, 687, 23, "containerOnBack"], [425, 67, 687, 38], [425, 70, 688, 16, "styles"], [425, 76, 688, 22], [425, 77, 688, 23, "containerInFront"], [425, 93, 688, 39], [425, 95, 689, 12, "containerStyles"], [425, 110, 689, 27], [425, 112, 690, 12, "contentContainerStyle"], [425, 133, 690, 33], [425, 134, 691, 12], [426, 12, 692, 10, "importantForAccessibility"], [426, 37, 692, 35], [426, 39, 693, 12, "_this"], [426, 44, 693, 12], [426, 45, 693, 17, "drawerShown"], [426, 56, 693, 28], [426, 59, 693, 31], [426, 80, 693, 52], [426, 83, 693, 55], [426, 88, 694, 11], [427, 12, 694, 11, "children"], [427, 20, 694, 11], [427, 23, 695, 11], [427, 30, 695, 18, "_this"], [427, 35, 695, 18], [427, 36, 695, 23, "props"], [427, 41, 695, 28], [427, 42, 695, 29, "children"], [427, 50, 695, 37], [427, 55, 695, 42], [427, 65, 695, 52], [427, 68, 696, 14, "_this"], [427, 73, 696, 14], [427, 74, 696, 19, "props"], [427, 79, 696, 24], [427, 80, 696, 25, "children"], [427, 88, 696, 33], [427, 89, 696, 34, "_this"], [427, 94, 696, 34], [427, 95, 696, 39, "openValue"], [427, 104, 696, 48], [427, 105, 696, 49], [427, 108, 697, 14, "_this"], [427, 113, 697, 14], [427, 114, 697, 19, "props"], [427, 119, 697, 24], [427, 120, 697, 25, "children"], [427, 128, 697, 33], [427, 130, 698, 11, "_this"], [427, 135, 698, 11], [427, 136, 698, 16, "renderOverlay"], [427, 149, 698, 29], [427, 150, 698, 30], [427, 151, 698, 31], [428, 10, 698, 31], [429, 12, 698, 31, "fileName"], [429, 20, 698, 31], [429, 22, 698, 31, "_jsxFileName"], [429, 34, 698, 31], [430, 12, 698, 31, "lineNumber"], [430, 22, 698, 31], [431, 12, 698, 31, "columnNumber"], [431, 24, 698, 31], [432, 10, 698, 31], [432, 13, 698, 31, "_this"], [432, 18, 699, 23], [432, 19, 699, 24], [432, 34, 700, 8], [432, 38, 700, 8, "_jsxDevRuntime"], [432, 52, 700, 8], [432, 53, 700, 8, "jsxDEV"], [432, 59, 700, 8], [432, 61, 700, 9, "_reactNative"], [432, 73, 700, 9], [432, 74, 700, 9, "Animated"], [432, 82, 700, 17], [432, 83, 700, 18, "View"], [432, 87, 700, 22], [433, 12, 701, 10, "pointerEvents"], [433, 25, 701, 23], [433, 27, 701, 24], [433, 37, 701, 34], [434, 12, 702, 10, "ref"], [434, 15, 702, 13], [434, 17, 702, 15, "_this"], [434, 22, 702, 15], [434, 23, 702, 20, "accessibilityIsModalView"], [434, 47, 702, 45], [435, 12, 703, 10, "accessibilityViewIsModal"], [435, 36, 703, 34], [435, 38, 703, 36, "_this"], [435, 43, 703, 36], [435, 44, 703, 41, "drawerShown"], [435, 55, 703, 53], [436, 12, 704, 10, "style"], [436, 17, 704, 15], [436, 19, 704, 17], [436, 20, 704, 18, "styles"], [436, 26, 704, 24], [436, 27, 704, 25, "drawerContainer"], [436, 42, 704, 40], [436, 44, 704, 42, "drawerStyles"], [436, 56, 704, 54], [436, 58, 704, 56, "drawerContainerStyle"], [436, 78, 704, 76], [436, 79, 704, 78], [437, 12, 704, 78, "children"], [437, 20, 704, 78], [437, 35, 705, 10], [437, 39, 705, 10, "_jsxDevRuntime"], [437, 53, 705, 10], [437, 54, 705, 10, "jsxDEV"], [437, 60, 705, 10], [437, 62, 705, 11, "_reactNative"], [437, 74, 705, 11], [437, 75, 705, 11, "View"], [437, 79, 705, 15], [438, 14, 705, 16, "style"], [438, 19, 705, 21], [438, 21, 705, 23, "dynamicDrawerStyles"], [438, 40, 705, 43], [439, 14, 705, 43, "children"], [439, 22, 705, 43], [439, 24, 706, 13, "_this"], [439, 29, 706, 13], [439, 30, 706, 18, "props"], [439, 35, 706, 23], [439, 36, 706, 24, "renderNavigationView"], [439, 56, 706, 44], [439, 57, 706, 45, "_this"], [439, 62, 706, 45], [439, 63, 706, 50, "openValue"], [439, 72, 706, 77], [440, 12, 706, 78], [441, 14, 706, 78, "fileName"], [441, 22, 706, 78], [441, 24, 706, 78, "_jsxFileName"], [441, 36, 706, 78], [442, 14, 706, 78, "lineNumber"], [442, 24, 706, 78], [443, 14, 706, 78, "columnNumber"], [443, 26, 706, 78], [444, 12, 706, 78], [444, 15, 706, 78, "_this"], [444, 20, 707, 16], [445, 10, 707, 17], [446, 12, 707, 17, "fileName"], [446, 20, 707, 17], [446, 22, 707, 17, "_jsxFileName"], [446, 34, 707, 17], [447, 12, 707, 17, "lineNumber"], [447, 22, 707, 17], [448, 12, 707, 17, "columnNumber"], [448, 24, 707, 17], [449, 10, 707, 17], [449, 13, 707, 17, "_this"], [449, 18, 708, 23], [449, 19, 708, 24], [450, 8, 708, 24], [451, 10, 708, 24, "fileName"], [451, 18, 708, 24], [451, 20, 708, 24, "_jsxFileName"], [451, 32, 708, 24], [452, 10, 708, 24, "lineNumber"], [452, 20, 708, 24], [453, 10, 708, 24, "columnNumber"], [453, 22, 708, 24], [454, 8, 708, 24], [454, 11, 708, 24, "_this"], [454, 16, 709, 21], [454, 17, 709, 22], [455, 6, 711, 2], [455, 7, 711, 3], [456, 6, 711, 3, "_this"], [456, 11, 711, 3], [456, 12, 713, 10, "setPanGestureRef"], [456, 28, 713, 26], [456, 31, 713, 30, "ref"], [456, 34, 713, 52], [456, 38, 713, 57], [457, 8, 714, 4], [458, 8, 715, 4], [459, 8, 717, 6, "_this"], [459, 13, 717, 6], [459, 14, 717, 11, "panGestureHandler"], [459, 31, 717, 28], [459, 32, 718, 6, "current"], [459, 39, 718, 13], [459, 42, 718, 16, "ref"], [459, 45, 718, 19], [460, 8, 719, 4, "_this"], [460, 13, 719, 4], [460, 14, 719, 9, "props"], [460, 19, 719, 14], [460, 20, 719, 15, "onGestureRef"], [460, 32, 719, 27], [460, 35, 719, 30, "ref"], [460, 38, 719, 33], [460, 39, 719, 34], [461, 6, 720, 2], [461, 7, 720, 3], [462, 6, 250, 4], [462, 10, 250, 10, "dragX"], [462, 16, 250, 15], [462, 19, 250, 18], [462, 23, 250, 22, "Animated"], [462, 44, 250, 30], [462, 45, 250, 31, "Value"], [462, 50, 250, 36], [462, 51, 250, 37], [462, 52, 250, 38], [462, 53, 250, 39], [463, 6, 251, 4], [463, 10, 251, 10, "touchX"], [463, 17, 251, 16], [463, 20, 251, 19], [463, 24, 251, 23, "Animated"], [463, 45, 251, 31], [463, 46, 251, 32, "Value"], [463, 51, 251, 37], [463, 52, 251, 38], [463, 53, 251, 39], [463, 54, 251, 40], [464, 6, 252, 4], [464, 10, 252, 10, "drawerTranslation"], [464, 28, 252, 27], [464, 31, 252, 30], [464, 35, 252, 34, "Animated"], [464, 56, 252, 42], [464, 57, 252, 43, "Value"], [464, 62, 252, 48], [464, 63, 252, 49], [464, 64, 252, 50], [464, 65, 252, 51], [465, 6, 254, 4, "_this"], [465, 11, 254, 4], [465, 12, 254, 9, "state"], [465, 17, 254, 14], [465, 20, 254, 17], [466, 8, 255, 6, "dragX"], [466, 13, 255, 11], [466, 15, 255, 6, "dragX"], [466, 21, 255, 11], [467, 8, 256, 6, "touchX"], [467, 14, 256, 12], [467, 16, 256, 6, "touchX"], [467, 23, 256, 12], [468, 8, 257, 6, "drawerTranslation"], [468, 25, 257, 23], [468, 27, 257, 6, "drawerTranslation"], [468, 45, 257, 23], [469, 8, 258, 6, "containerWidth"], [469, 22, 258, 20], [469, 24, 258, 22], [469, 25, 258, 23], [470, 8, 259, 6, "drawerState"], [470, 19, 259, 17], [470, 21, 259, 19, "IDLE"], [470, 25, 259, 23], [471, 8, 260, 6, "drawerOpened"], [471, 20, 260, 18], [471, 22, 260, 20], [472, 6, 261, 4], [472, 7, 261, 5], [473, 6, 263, 4, "_this"], [473, 11, 263, 4], [473, 12, 263, 9, "updateAnimatedEvent"], [473, 31, 263, 28], [473, 32, 263, 29, "props"], [473, 38, 263, 34], [473, 40, 263, 36, "_this"], [473, 45, 263, 36], [473, 46, 263, 41, "state"], [473, 51, 263, 46], [473, 52, 263, 47], [474, 6, 263, 48], [474, 13, 263, 48, "_this"], [474, 18, 263, 48], [475, 4, 264, 2], [476, 4, 264, 3], [476, 8, 264, 3, "_inherits2"], [476, 18, 264, 3], [476, 19, 264, 3, "default"], [476, 26, 264, 3], [476, 28, 264, 3, "DrawerLayout"], [476, 40, 264, 3], [476, 42, 264, 3, "_Component"], [476, 52, 264, 3], [477, 4, 264, 3], [477, 15, 264, 3, "_createClass2"], [477, 28, 264, 3], [477, 29, 264, 3, "default"], [477, 36, 264, 3], [477, 38, 264, 3, "DrawerLayout"], [477, 50, 264, 3], [478, 6, 264, 3, "key"], [478, 9, 264, 3], [479, 6, 264, 3, "value"], [479, 11, 264, 3], [479, 13, 266, 2], [479, 22, 266, 2, "shouldComponentUpdate"], [479, 43, 266, 23, "shouldComponentUpdate"], [479, 44, 266, 24, "props"], [479, 49, 266, 48], [479, 51, 266, 50, "state"], [479, 56, 266, 74], [479, 58, 266, 76], [480, 8, 267, 4], [480, 12, 268, 6], [480, 16, 268, 10], [480, 17, 268, 11, "props"], [480, 22, 268, 16], [480, 23, 268, 17, "drawerPosition"], [480, 37, 268, 31], [480, 42, 268, 36, "props"], [480, 47, 268, 41], [480, 48, 268, 42, "drawerPosition"], [480, 62, 268, 56], [480, 66, 269, 6], [480, 70, 269, 10], [480, 71, 269, 11, "props"], [480, 76, 269, 16], [480, 77, 269, 17, "drawerWidth"], [480, 88, 269, 28], [480, 93, 269, 33, "props"], [480, 98, 269, 38], [480, 99, 269, 39, "drawerWidth"], [480, 110, 269, 50], [480, 114, 270, 6], [480, 118, 270, 10], [480, 119, 270, 11, "props"], [480, 124, 270, 16], [480, 125, 270, 17, "drawerType"], [480, 135, 270, 27], [480, 140, 270, 32, "props"], [480, 145, 270, 37], [480, 146, 270, 38, "drawerType"], [480, 156, 270, 48], [480, 160, 271, 6], [480, 164, 271, 10], [480, 165, 271, 11, "state"], [480, 170, 271, 16], [480, 171, 271, 17, "containerWidth"], [480, 185, 271, 31], [480, 190, 271, 36, "state"], [480, 195, 271, 41], [480, 196, 271, 42, "containerWidth"], [480, 210, 271, 56], [480, 212, 272, 6], [481, 10, 273, 6], [481, 14, 273, 10], [481, 15, 273, 11, "updateAnimatedEvent"], [481, 34, 273, 30], [481, 35, 273, 31, "props"], [481, 40, 273, 36], [481, 42, 273, 38, "state"], [481, 47, 273, 43], [481, 48, 273, 44], [482, 8, 274, 4], [483, 8, 276, 4], [483, 15, 276, 11], [483, 19, 276, 15], [484, 6, 277, 2], [485, 4, 277, 3], [486, 6, 277, 3, "key"], [486, 9, 277, 3], [487, 6, 277, 3, "value"], [487, 11, 277, 3], [487, 13, 722, 2], [487, 22, 722, 2, "render"], [487, 28, 722, 8, "render"], [487, 29, 722, 8], [487, 31, 722, 11], [488, 8, 723, 4], [488, 12, 723, 4, "_this$props4"], [488, 24, 723, 4], [488, 27, 724, 6], [488, 31, 724, 10], [488, 32, 724, 11, "props"], [488, 37, 724, 16], [489, 10, 723, 12, "drawerPosition"], [489, 24, 723, 26], [489, 27, 723, 26, "_this$props4"], [489, 39, 723, 26], [489, 40, 723, 12, "drawerPosition"], [489, 54, 723, 26], [490, 10, 723, 28, "drawerLockMode"], [490, 24, 723, 42], [490, 27, 723, 42, "_this$props4"], [490, 39, 723, 42], [490, 40, 723, 28, "drawerLockMode"], [490, 54, 723, 42], [491, 10, 723, 44, "edgeWidth"], [491, 19, 723, 53], [491, 22, 723, 53, "_this$props4"], [491, 34, 723, 53], [491, 35, 723, 44, "edgeWidth"], [491, 44, 723, 53], [492, 10, 723, 55, "minSwipeDistance"], [492, 26, 723, 71], [492, 29, 723, 71, "_this$props4"], [492, 41, 723, 71], [492, 42, 723, 55, "minSwipeDistance"], [492, 58, 723, 71], [493, 8, 726, 4], [493, 12, 726, 10, "fromLeft"], [493, 20, 726, 18], [493, 23, 726, 21, "drawerPosition"], [493, 37, 726, 35], [493, 42, 726, 40], [493, 48, 726, 46], [495, 8, 728, 4], [496, 8, 729, 4], [497, 8, 730, 4], [498, 8, 731, 4], [498, 12, 731, 10, "gestureOrientation"], [498, 30, 731, 28], [498, 33, 732, 6], [498, 34, 732, 7, "fromLeft"], [498, 42, 732, 15], [498, 45, 732, 18], [498, 46, 732, 19], [498, 49, 732, 22], [498, 50, 732, 23], [498, 51, 732, 24], [498, 56, 732, 29], [498, 60, 732, 33], [498, 61, 732, 34, "drawerShown"], [498, 72, 732, 45], [498, 75, 732, 48], [498, 76, 732, 49], [498, 77, 732, 50], [498, 80, 732, 53], [498, 81, 732, 54], [498, 82, 732, 55], [500, 8, 734, 4], [501, 8, 735, 4], [502, 8, 736, 4], [503, 8, 737, 4], [503, 12, 737, 10, "hitSlop"], [503, 19, 737, 17], [503, 22, 737, 20, "fromLeft"], [503, 30, 737, 28], [503, 33, 738, 8], [504, 10, 738, 10, "left"], [504, 14, 738, 14], [504, 16, 738, 16], [504, 17, 738, 17], [505, 10, 738, 19, "width"], [505, 15, 738, 24], [505, 17, 738, 26], [505, 21, 738, 30], [505, 22, 738, 31, "drawerShown"], [505, 33, 738, 42], [505, 36, 738, 45, "undefined"], [505, 45, 738, 54], [505, 48, 738, 57, "edgeWidth"], [506, 8, 738, 67], [506, 9, 738, 68], [506, 12, 739, 8], [507, 10, 739, 10, "right"], [507, 15, 739, 15], [507, 17, 739, 17], [507, 18, 739, 18], [508, 10, 739, 20, "width"], [508, 15, 739, 25], [508, 17, 739, 27], [508, 21, 739, 31], [508, 22, 739, 32, "drawerShown"], [508, 33, 739, 43], [508, 36, 739, 46, "undefined"], [508, 45, 739, 55], [508, 48, 739, 58, "edgeWidth"], [509, 8, 739, 68], [509, 9, 739, 69], [510, 8, 741, 4], [510, 28, 742, 6], [510, 32, 742, 6, "_jsxDevRuntime"], [510, 46, 742, 6], [510, 47, 742, 6, "jsxDEV"], [510, 53, 742, 6], [510, 55, 742, 7, "_PanGestureHandler"], [510, 73, 742, 7], [510, 74, 742, 7, "PanGestureHandler"], [511, 8, 743, 8], [512, 8, 743, 8], [513, 10, 744, 8, "userSelect"], [513, 20, 744, 18], [513, 22, 744, 20], [513, 26, 744, 24], [513, 27, 744, 25, "props"], [513, 32, 744, 30], [513, 33, 744, 31, "userSelect"], [513, 43, 744, 42], [514, 10, 745, 8, "activeCursor"], [514, 22, 745, 20], [514, 24, 745, 22], [514, 28, 745, 26], [514, 29, 745, 27, "props"], [514, 34, 745, 32], [514, 35, 745, 33, "activeCursor"], [514, 47, 745, 46], [515, 10, 746, 8, "mouseButton"], [515, 21, 746, 19], [515, 23, 746, 21], [515, 27, 746, 25], [515, 28, 746, 26, "props"], [515, 33, 746, 31], [515, 34, 746, 32, "mouseButton"], [515, 45, 746, 44], [516, 10, 747, 8, "enableContextMenu"], [516, 27, 747, 25], [516, 29, 747, 27], [516, 33, 747, 31], [516, 34, 747, 32, "props"], [516, 39, 747, 37], [516, 40, 747, 38, "enableContextMenu"], [516, 57, 747, 56], [517, 10, 748, 8, "ref"], [517, 13, 748, 11], [517, 15, 748, 13], [517, 19, 748, 17], [517, 20, 748, 18, "setPanGestureRef"], [517, 36, 748, 35], [518, 10, 749, 8, "hitSlop"], [518, 17, 749, 15], [518, 19, 749, 17, "hitSlop"], [518, 26, 749, 25], [519, 10, 750, 8, "activeOffsetX"], [519, 23, 750, 21], [519, 25, 750, 23, "gestureOrientation"], [519, 43, 750, 41], [519, 46, 750, 44, "minSwipeDistance"], [519, 62, 750, 62], [520, 10, 751, 8, "failOffsetY"], [520, 21, 751, 19], [520, 23, 751, 21], [520, 24, 751, 22], [520, 25, 751, 23], [520, 27, 751, 25], [520, 29, 751, 27], [520, 31, 751, 29], [520, 32, 751, 31], [521, 10, 752, 8, "onGestureEvent"], [521, 24, 752, 22], [521, 26, 752, 24], [521, 30, 752, 28], [521, 31, 752, 29, "onGestureEvent"], [521, 45, 752, 44], [522, 10, 753, 8, "onHandlerStateChange"], [522, 30, 753, 28], [522, 32, 753, 30], [522, 36, 753, 34], [522, 37, 753, 35, "openingHandlerStateChange"], [522, 62, 753, 61], [523, 10, 754, 8, "enableTrackpadTwoFingerGesture"], [523, 40, 754, 38], [523, 42, 755, 10], [523, 46, 755, 14], [523, 47, 755, 15, "props"], [523, 52, 755, 20], [523, 53, 755, 21, "enableTrackpadTwoFingerGesture"], [523, 83, 756, 9], [524, 10, 757, 8, "enabled"], [524, 17, 757, 15], [524, 19, 758, 10, "drawerLockMode"], [524, 33, 758, 24], [524, 38, 758, 29], [524, 53, 758, 44], [524, 57, 758, 48, "drawerLockMode"], [524, 71, 758, 62], [524, 76, 758, 67], [524, 89, 759, 9], [525, 10, 759, 9, "children"], [525, 18, 759, 9], [525, 20, 760, 9], [525, 24, 760, 13], [525, 25, 760, 14, "renderDrawer"], [525, 37, 760, 26], [525, 38, 760, 27], [526, 8, 760, 28], [527, 10, 760, 28, "fileName"], [527, 18, 760, 28], [527, 20, 760, 28, "_jsxFileName"], [527, 32, 760, 28], [528, 10, 760, 28, "lineNumber"], [528, 20, 760, 28], [529, 10, 760, 28, "columnNumber"], [529, 22, 760, 28], [530, 8, 760, 28], [530, 15, 761, 25], [530, 16, 761, 26], [531, 6, 763, 2], [532, 4, 763, 3], [533, 2, 763, 3], [533, 4, 231, 42, "Component"], [533, 20, 231, 51], [534, 2, 231, 21, "DrawerLayout"], [534, 14, 231, 33], [534, 15, 235, 9, "defaultProps"], [534, 27, 235, 21], [534, 30, 235, 24], [535, 4, 236, 4, "drawerWidth"], [535, 15, 236, 15], [535, 17, 236, 17], [535, 20, 236, 20], [536, 4, 237, 4, "drawerPosition"], [536, 18, 237, 18], [536, 20, 237, 20], [536, 26, 237, 26], [537, 4, 238, 4, "useNativeAnimations"], [537, 23, 238, 23], [537, 25, 238, 25], [537, 29, 238, 29], [538, 4, 239, 4, "drawerType"], [538, 14, 239, 14], [538, 16, 239, 16], [538, 23, 239, 23], [539, 4, 240, 4, "edgeWidth"], [539, 13, 240, 13], [539, 15, 240, 15], [539, 17, 240, 17], [540, 4, 241, 4, "minSwipeDistance"], [540, 20, 241, 20], [540, 22, 241, 22], [540, 23, 241, 23], [541, 4, 242, 4, "overlayColor"], [541, 16, 242, 16], [541, 18, 242, 18], [541, 38, 242, 38], [542, 4, 243, 4, "drawerLockMode"], [542, 18, 243, 18], [542, 20, 243, 20], [542, 30, 243, 30], [543, 4, 244, 4, "enableTrackpadTwoFingerGesture"], [543, 34, 244, 34], [543, 36, 244, 36], [544, 2, 245, 2], [544, 3, 245, 3], [545, 2, 231, 21, "DrawerLayout"], [545, 14, 231, 33], [545, 15, 290, 9, "positions"], [545, 24, 290, 18], [545, 27, 290, 21], [546, 4, 291, 4, "Left"], [546, 8, 291, 8], [546, 10, 291, 10], [546, 16, 291, 16], [547, 4, 292, 4, "Right"], [547, 9, 292, 9], [547, 11, 292, 11], [548, 2, 293, 2], [548, 3, 293, 3], [549, 2, 766, 0], [549, 6, 766, 6, "styles"], [549, 12, 766, 12], [549, 15, 766, 15, "StyleSheet"], [549, 38, 766, 25], [549, 39, 766, 26, "create"], [549, 45, 766, 32], [549, 46, 766, 33], [550, 4, 767, 2, "drawerContainer"], [550, 19, 767, 17], [550, 21, 767, 19], [551, 6, 768, 4], [551, 9, 768, 7, "StyleSheet"], [551, 32, 768, 17], [551, 33, 768, 18, "absoluteFillObject"], [551, 51, 768, 36], [552, 6, 769, 4, "zIndex"], [552, 12, 769, 10], [552, 14, 769, 12], [552, 18, 769, 16], [553, 6, 770, 4, "flexDirection"], [553, 19, 770, 17], [553, 21, 770, 19], [554, 4, 771, 2], [554, 5, 771, 3], [555, 4, 772, 2, "containerInFront"], [555, 20, 772, 18], [555, 22, 772, 20], [556, 6, 773, 4], [556, 9, 773, 7, "StyleSheet"], [556, 32, 773, 17], [556, 33, 773, 18, "absoluteFillObject"], [556, 51, 773, 36], [557, 6, 774, 4, "zIndex"], [557, 12, 774, 10], [557, 14, 774, 12], [558, 4, 775, 2], [558, 5, 775, 3], [559, 4, 776, 2, "containerOnBack"], [559, 19, 776, 17], [559, 21, 776, 19], [560, 6, 777, 4], [560, 9, 777, 7, "StyleSheet"], [560, 32, 777, 17], [560, 33, 777, 18, "absoluteFillObject"], [561, 4, 778, 2], [561, 5, 778, 3], [562, 4, 779, 2, "main"], [562, 8, 779, 6], [562, 10, 779, 8], [563, 6, 780, 4, "flex"], [563, 10, 780, 8], [563, 12, 780, 10], [563, 13, 780, 11], [564, 6, 781, 4, "zIndex"], [564, 12, 781, 10], [564, 14, 781, 12], [564, 15, 781, 13], [565, 6, 782, 4, "overflow"], [565, 14, 782, 12], [565, 16, 782, 14], [566, 4, 783, 2], [566, 5, 783, 3], [567, 4, 784, 2, "overlay"], [567, 11, 784, 9], [567, 13, 784, 11], [568, 6, 785, 4], [568, 9, 785, 7, "StyleSheet"], [568, 32, 785, 17], [568, 33, 785, 18, "absoluteFillObject"], [568, 51, 785, 36], [569, 6, 786, 4, "zIndex"], [569, 12, 786, 10], [569, 14, 786, 12], [570, 4, 787, 2], [571, 2, 788, 0], [571, 3, 788, 1], [571, 4, 788, 2], [572, 0, 788, 3], [572, 3]], "functionMap": {"names": ["<global>", "DrawerLayout", "constructor", "shouldComponentUpdate", "updateAnimatedEvent", "gestureOptions.listener", "handleContainerLayout", "emitStateChanged", "openingHandlerStateChange", "onTapHandlerStateChange", "handleRelease", "updateShowing", "animateDrawer", "Animated.spring.start$argument_0", "openDrawer", "closeDrawer", "renderOverlay", "renderDrawer", "setPanGestureRef", "render"], "mappings": "AAA;eCsO;ECgB;GDiB;EEE;GFW;gCGkB;gCC+F;ODK;GHO;kCKE;GLE;6BME;GNK;sCOE;GPe;oCQE;GRU;0BSE;GTkC;0BUE;GV0B;0BWE;aCwC;KDe;GXC;eaG;GbY;gBcE;GdY;0BeE;GfyB;yBgBE;GhByF;6BiBE;GjBO;EkBE;GlByC;CDC"}}, "type": "js/module"}]}