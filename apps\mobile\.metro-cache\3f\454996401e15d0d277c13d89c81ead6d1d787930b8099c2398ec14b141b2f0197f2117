{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 16, "index": 126}, "end": {"line": 4, "column": 32, "index": 142}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 23, "index": 167}, "end": {"line": 5, "column": 46, "index": 190}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "../utils/selector", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 19, "index": 211}, "end": {"line": 6, "column": 47, "index": 239}}], "key": "7YgKqtu3BoMZhI8fW1WyoV3whVo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useInteraction = void 0;\n  var react_1 = require(_dependencyMap[0], \"react\");\n  var react_native_1 = require(_dependencyMap[1], \"react-native\");\n  var selector_1 = require(_dependencyMap[2], \"../utils/selector\");\n  function useInteraction(dispatch, mask, props) {\n    var ref = (0, react_1.useRef)(props);\n    ref.current = props;\n    var handlers = (0, react_1.useMemo)(() => {\n      var isParentOrGroup = (0, selector_1.matchesMask)(mask, selector_1.PARENT) || (0, selector_1.matchesMask)(mask, selector_1.GROUP);\n      var handlers = {};\n      if (isParentOrGroup || (0, selector_1.matchesMask)(mask, selector_1.ACTIVE)) {\n        if (react_native_1.Platform.OS === \"web\") {\n          handlers.onMouseDown = function (event) {\n            if (ref.current.onMouseDown) {\n              ref.current.onMouseDown(event);\n            }\n            dispatch({\n              type: \"active\",\n              value: true\n            });\n          };\n          handlers.onMouseUp = function (event) {\n            if (ref.current.onMouseUp) {\n              ref.current.onMouseUp(event);\n            }\n            dispatch({\n              type: \"active\",\n              value: false\n            });\n          };\n        } else {\n          handlers.onPressIn = function (event) {\n            if (ref.current.onPressIn) {\n              ref.current.onPressIn(event);\n            }\n            dispatch({\n              type: \"active\",\n              value: true\n            });\n          };\n          handlers.onPressOut = function (event) {\n            if (ref.current.onPressOut) {\n              ref.current.onPressOut(event);\n            }\n            dispatch({\n              type: \"active\",\n              value: false\n            });\n          };\n        }\n      }\n      if (isParentOrGroup || (0, selector_1.matchesMask)(mask, selector_1.HOVER)) {\n        handlers.onHoverIn = function (event) {\n          if (ref.current.onHoverIn) {\n            ref.current.onHoverIn(event);\n          }\n          dispatch({\n            type: \"hover\",\n            value: true\n          });\n        };\n        handlers.onHoverOut = function (event) {\n          if (ref.current.onHoverIn) {\n            ref.current.onHoverIn(event);\n          }\n          dispatch({\n            type: \"hover\",\n            value: true\n          });\n        };\n      }\n      if (isParentOrGroup || (0, selector_1.matchesMask)(mask, selector_1.FOCUS)) {\n        handlers.onFocus = function (event) {\n          if (ref.current.onFocus) {\n            ref.current.onFocus(event);\n          }\n          dispatch({\n            type: \"focus\",\n            value: true\n          });\n        };\n        handlers.onBlur = function (event) {\n          if (ref.current.onBlur) {\n            ref.current.onBlur(event);\n          }\n          dispatch({\n            type: \"focus\",\n            value: false\n          });\n        };\n      }\n      return handlers;\n    }, [mask]);\n    return handlers;\n  }\n  exports.useInteraction = useInteraction;\n});", "lineCount": 103, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "useInteraction"], [7, 24, 3, 22], [7, 27, 3, 25], [7, 32, 3, 30], [7, 33, 3, 31], [8, 2, 4, 0], [8, 6, 4, 6, "react_1"], [8, 13, 4, 13], [8, 16, 4, 16, "require"], [8, 23, 4, 23], [8, 24, 4, 23, "_dependencyMap"], [8, 38, 4, 23], [8, 50, 4, 31], [8, 51, 4, 32], [9, 2, 5, 0], [9, 6, 5, 6, "react_native_1"], [9, 20, 5, 20], [9, 23, 5, 23, "require"], [9, 30, 5, 30], [9, 31, 5, 30, "_dependencyMap"], [9, 45, 5, 30], [9, 64, 5, 45], [9, 65, 5, 46], [10, 2, 6, 0], [10, 6, 6, 6, "selector_1"], [10, 16, 6, 16], [10, 19, 6, 19, "require"], [10, 26, 6, 26], [10, 27, 6, 26, "_dependencyMap"], [10, 41, 6, 26], [10, 65, 6, 46], [10, 66, 6, 47], [11, 2, 7, 0], [11, 11, 7, 9, "useInteraction"], [11, 25, 7, 23, "useInteraction"], [11, 26, 7, 24, "dispatch"], [11, 34, 7, 32], [11, 36, 7, 34, "mask"], [11, 40, 7, 38], [11, 42, 7, 40, "props"], [11, 47, 7, 45], [11, 49, 7, 47], [12, 4, 8, 4], [12, 8, 8, 10, "ref"], [12, 11, 8, 13], [12, 14, 8, 16], [12, 15, 8, 17], [12, 16, 8, 18], [12, 18, 8, 20, "react_1"], [12, 25, 8, 27], [12, 26, 8, 28, "useRef"], [12, 32, 8, 34], [12, 34, 8, 36, "props"], [12, 39, 8, 41], [12, 40, 8, 42], [13, 4, 9, 4, "ref"], [13, 7, 9, 7], [13, 8, 9, 8, "current"], [13, 15, 9, 15], [13, 18, 9, 18, "props"], [13, 23, 9, 23], [14, 4, 10, 4], [14, 8, 10, 10, "handlers"], [14, 16, 10, 18], [14, 19, 10, 21], [14, 20, 10, 22], [14, 21, 10, 23], [14, 23, 10, 25, "react_1"], [14, 30, 10, 32], [14, 31, 10, 33, "useMemo"], [14, 38, 10, 40], [14, 40, 10, 42], [14, 46, 10, 48], [15, 6, 11, 8], [15, 10, 11, 14, "isParentOrGroup"], [15, 25, 11, 29], [15, 28, 11, 32], [15, 29, 11, 33], [15, 30, 11, 34], [15, 32, 11, 36, "selector_1"], [15, 42, 11, 46], [15, 43, 11, 47, "matchesMask"], [15, 54, 11, 58], [15, 56, 11, 60, "mask"], [15, 60, 11, 64], [15, 62, 11, 66, "selector_1"], [15, 72, 11, 76], [15, 73, 11, 77, "PARENT"], [15, 79, 11, 83], [15, 80, 11, 84], [15, 84, 11, 88], [15, 85, 11, 89], [15, 86, 11, 90], [15, 88, 11, 92, "selector_1"], [15, 98, 11, 102], [15, 99, 11, 103, "matchesMask"], [15, 110, 11, 114], [15, 112, 11, 116, "mask"], [15, 116, 11, 120], [15, 118, 11, 122, "selector_1"], [15, 128, 11, 132], [15, 129, 11, 133, "GROUP"], [15, 134, 11, 138], [15, 135, 11, 139], [16, 6, 12, 8], [16, 10, 12, 14, "handlers"], [16, 18, 12, 22], [16, 21, 12, 25], [16, 22, 12, 26], [16, 23, 12, 27], [17, 6, 13, 8], [17, 10, 13, 12, "isParentOrGroup"], [17, 25, 13, 27], [17, 29, 13, 31], [17, 30, 13, 32], [17, 31, 13, 33], [17, 33, 13, 35, "selector_1"], [17, 43, 13, 45], [17, 44, 13, 46, "matchesMask"], [17, 55, 13, 57], [17, 57, 13, 59, "mask"], [17, 61, 13, 63], [17, 63, 13, 65, "selector_1"], [17, 73, 13, 75], [17, 74, 13, 76, "ACTIVE"], [17, 80, 13, 82], [17, 81, 13, 83], [17, 83, 13, 85], [18, 8, 14, 12], [18, 12, 14, 16, "react_native_1"], [18, 26, 14, 30], [18, 27, 14, 31, "Platform"], [18, 35, 14, 39], [18, 36, 14, 40, "OS"], [18, 38, 14, 42], [18, 43, 14, 47], [18, 48, 14, 52], [18, 50, 14, 54], [19, 10, 15, 16, "handlers"], [19, 18, 15, 24], [19, 19, 15, 25, "onMouseDown"], [19, 30, 15, 36], [19, 33, 15, 39], [19, 43, 15, 49, "event"], [19, 48, 15, 54], [19, 50, 15, 56], [20, 12, 16, 20], [20, 16, 16, 24, "ref"], [20, 19, 16, 27], [20, 20, 16, 28, "current"], [20, 27, 16, 35], [20, 28, 16, 36, "onMouseDown"], [20, 39, 16, 47], [20, 41, 16, 49], [21, 14, 17, 24, "ref"], [21, 17, 17, 27], [21, 18, 17, 28, "current"], [21, 25, 17, 35], [21, 26, 17, 36, "onMouseDown"], [21, 37, 17, 47], [21, 38, 17, 48, "event"], [21, 43, 17, 53], [21, 44, 17, 54], [22, 12, 18, 20], [23, 12, 19, 20, "dispatch"], [23, 20, 19, 28], [23, 21, 19, 29], [24, 14, 19, 31, "type"], [24, 18, 19, 35], [24, 20, 19, 37], [24, 28, 19, 45], [25, 14, 19, 47, "value"], [25, 19, 19, 52], [25, 21, 19, 54], [26, 12, 19, 59], [26, 13, 19, 60], [26, 14, 19, 61], [27, 10, 20, 16], [27, 11, 20, 17], [28, 10, 21, 16, "handlers"], [28, 18, 21, 24], [28, 19, 21, 25, "onMouseUp"], [28, 28, 21, 34], [28, 31, 21, 37], [28, 41, 21, 47, "event"], [28, 46, 21, 52], [28, 48, 21, 54], [29, 12, 22, 20], [29, 16, 22, 24, "ref"], [29, 19, 22, 27], [29, 20, 22, 28, "current"], [29, 27, 22, 35], [29, 28, 22, 36, "onMouseUp"], [29, 37, 22, 45], [29, 39, 22, 47], [30, 14, 23, 24, "ref"], [30, 17, 23, 27], [30, 18, 23, 28, "current"], [30, 25, 23, 35], [30, 26, 23, 36, "onMouseUp"], [30, 35, 23, 45], [30, 36, 23, 46, "event"], [30, 41, 23, 51], [30, 42, 23, 52], [31, 12, 24, 20], [32, 12, 25, 20, "dispatch"], [32, 20, 25, 28], [32, 21, 25, 29], [33, 14, 25, 31, "type"], [33, 18, 25, 35], [33, 20, 25, 37], [33, 28, 25, 45], [34, 14, 25, 47, "value"], [34, 19, 25, 52], [34, 21, 25, 54], [35, 12, 25, 60], [35, 13, 25, 61], [35, 14, 25, 62], [36, 10, 26, 16], [36, 11, 26, 17], [37, 8, 27, 12], [37, 9, 27, 13], [37, 15, 28, 17], [38, 10, 29, 16, "handlers"], [38, 18, 29, 24], [38, 19, 29, 25, "onPressIn"], [38, 28, 29, 34], [38, 31, 29, 37], [38, 41, 29, 47, "event"], [38, 46, 29, 52], [38, 48, 29, 54], [39, 12, 30, 20], [39, 16, 30, 24, "ref"], [39, 19, 30, 27], [39, 20, 30, 28, "current"], [39, 27, 30, 35], [39, 28, 30, 36, "onPressIn"], [39, 37, 30, 45], [39, 39, 30, 47], [40, 14, 31, 24, "ref"], [40, 17, 31, 27], [40, 18, 31, 28, "current"], [40, 25, 31, 35], [40, 26, 31, 36, "onPressIn"], [40, 35, 31, 45], [40, 36, 31, 46, "event"], [40, 41, 31, 51], [40, 42, 31, 52], [41, 12, 32, 20], [42, 12, 33, 20, "dispatch"], [42, 20, 33, 28], [42, 21, 33, 29], [43, 14, 33, 31, "type"], [43, 18, 33, 35], [43, 20, 33, 37], [43, 28, 33, 45], [44, 14, 33, 47, "value"], [44, 19, 33, 52], [44, 21, 33, 54], [45, 12, 33, 59], [45, 13, 33, 60], [45, 14, 33, 61], [46, 10, 34, 16], [46, 11, 34, 17], [47, 10, 35, 16, "handlers"], [47, 18, 35, 24], [47, 19, 35, 25, "onPressOut"], [47, 29, 35, 35], [47, 32, 35, 38], [47, 42, 35, 48, "event"], [47, 47, 35, 53], [47, 49, 35, 55], [48, 12, 36, 20], [48, 16, 36, 24, "ref"], [48, 19, 36, 27], [48, 20, 36, 28, "current"], [48, 27, 36, 35], [48, 28, 36, 36, "onPressOut"], [48, 38, 36, 46], [48, 40, 36, 48], [49, 14, 37, 24, "ref"], [49, 17, 37, 27], [49, 18, 37, 28, "current"], [49, 25, 37, 35], [49, 26, 37, 36, "onPressOut"], [49, 36, 37, 46], [49, 37, 37, 47, "event"], [49, 42, 37, 52], [49, 43, 37, 53], [50, 12, 38, 20], [51, 12, 39, 20, "dispatch"], [51, 20, 39, 28], [51, 21, 39, 29], [52, 14, 39, 31, "type"], [52, 18, 39, 35], [52, 20, 39, 37], [52, 28, 39, 45], [53, 14, 39, 47, "value"], [53, 19, 39, 52], [53, 21, 39, 54], [54, 12, 39, 60], [54, 13, 39, 61], [54, 14, 39, 62], [55, 10, 40, 16], [55, 11, 40, 17], [56, 8, 41, 12], [57, 6, 42, 8], [58, 6, 43, 8], [58, 10, 43, 12, "isParentOrGroup"], [58, 25, 43, 27], [58, 29, 43, 31], [58, 30, 43, 32], [58, 31, 43, 33], [58, 33, 43, 35, "selector_1"], [58, 43, 43, 45], [58, 44, 43, 46, "matchesMask"], [58, 55, 43, 57], [58, 57, 43, 59, "mask"], [58, 61, 43, 63], [58, 63, 43, 65, "selector_1"], [58, 73, 43, 75], [58, 74, 43, 76, "HOVER"], [58, 79, 43, 81], [58, 80, 43, 82], [58, 82, 43, 84], [59, 8, 44, 12, "handlers"], [59, 16, 44, 20], [59, 17, 44, 21, "onHoverIn"], [59, 26, 44, 30], [59, 29, 44, 33], [59, 39, 44, 43, "event"], [59, 44, 44, 48], [59, 46, 44, 50], [60, 10, 45, 16], [60, 14, 45, 20, "ref"], [60, 17, 45, 23], [60, 18, 45, 24, "current"], [60, 25, 45, 31], [60, 26, 45, 32, "onHoverIn"], [60, 35, 45, 41], [60, 37, 45, 43], [61, 12, 46, 20, "ref"], [61, 15, 46, 23], [61, 16, 46, 24, "current"], [61, 23, 46, 31], [61, 24, 46, 32, "onHoverIn"], [61, 33, 46, 41], [61, 34, 46, 42, "event"], [61, 39, 46, 47], [61, 40, 46, 48], [62, 10, 47, 16], [63, 10, 48, 16, "dispatch"], [63, 18, 48, 24], [63, 19, 48, 25], [64, 12, 48, 27, "type"], [64, 16, 48, 31], [64, 18, 48, 33], [64, 25, 48, 40], [65, 12, 48, 42, "value"], [65, 17, 48, 47], [65, 19, 48, 49], [66, 10, 48, 54], [66, 11, 48, 55], [66, 12, 48, 56], [67, 8, 49, 12], [67, 9, 49, 13], [68, 8, 50, 12, "handlers"], [68, 16, 50, 20], [68, 17, 50, 21, "onHoverOut"], [68, 27, 50, 31], [68, 30, 50, 34], [68, 40, 50, 44, "event"], [68, 45, 50, 49], [68, 47, 50, 51], [69, 10, 51, 16], [69, 14, 51, 20, "ref"], [69, 17, 51, 23], [69, 18, 51, 24, "current"], [69, 25, 51, 31], [69, 26, 51, 32, "onHoverIn"], [69, 35, 51, 41], [69, 37, 51, 43], [70, 12, 52, 20, "ref"], [70, 15, 52, 23], [70, 16, 52, 24, "current"], [70, 23, 52, 31], [70, 24, 52, 32, "onHoverIn"], [70, 33, 52, 41], [70, 34, 52, 42, "event"], [70, 39, 52, 47], [70, 40, 52, 48], [71, 10, 53, 16], [72, 10, 54, 16, "dispatch"], [72, 18, 54, 24], [72, 19, 54, 25], [73, 12, 54, 27, "type"], [73, 16, 54, 31], [73, 18, 54, 33], [73, 25, 54, 40], [74, 12, 54, 42, "value"], [74, 17, 54, 47], [74, 19, 54, 49], [75, 10, 54, 54], [75, 11, 54, 55], [75, 12, 54, 56], [76, 8, 55, 12], [76, 9, 55, 13], [77, 6, 56, 8], [78, 6, 57, 8], [78, 10, 57, 12, "isParentOrGroup"], [78, 25, 57, 27], [78, 29, 57, 31], [78, 30, 57, 32], [78, 31, 57, 33], [78, 33, 57, 35, "selector_1"], [78, 43, 57, 45], [78, 44, 57, 46, "matchesMask"], [78, 55, 57, 57], [78, 57, 57, 59, "mask"], [78, 61, 57, 63], [78, 63, 57, 65, "selector_1"], [78, 73, 57, 75], [78, 74, 57, 76, "FOCUS"], [78, 79, 57, 81], [78, 80, 57, 82], [78, 82, 57, 84], [79, 8, 58, 12, "handlers"], [79, 16, 58, 20], [79, 17, 58, 21, "onFocus"], [79, 24, 58, 28], [79, 27, 58, 31], [79, 37, 58, 41, "event"], [79, 42, 58, 46], [79, 44, 58, 48], [80, 10, 59, 16], [80, 14, 59, 20, "ref"], [80, 17, 59, 23], [80, 18, 59, 24, "current"], [80, 25, 59, 31], [80, 26, 59, 32, "onFocus"], [80, 33, 59, 39], [80, 35, 59, 41], [81, 12, 60, 20, "ref"], [81, 15, 60, 23], [81, 16, 60, 24, "current"], [81, 23, 60, 31], [81, 24, 60, 32, "onFocus"], [81, 31, 60, 39], [81, 32, 60, 40, "event"], [81, 37, 60, 45], [81, 38, 60, 46], [82, 10, 61, 16], [83, 10, 62, 16, "dispatch"], [83, 18, 62, 24], [83, 19, 62, 25], [84, 12, 62, 27, "type"], [84, 16, 62, 31], [84, 18, 62, 33], [84, 25, 62, 40], [85, 12, 62, 42, "value"], [85, 17, 62, 47], [85, 19, 62, 49], [86, 10, 62, 54], [86, 11, 62, 55], [86, 12, 62, 56], [87, 8, 63, 12], [87, 9, 63, 13], [88, 8, 64, 12, "handlers"], [88, 16, 64, 20], [88, 17, 64, 21, "onBlur"], [88, 23, 64, 27], [88, 26, 64, 30], [88, 36, 64, 40, "event"], [88, 41, 64, 45], [88, 43, 64, 47], [89, 10, 65, 16], [89, 14, 65, 20, "ref"], [89, 17, 65, 23], [89, 18, 65, 24, "current"], [89, 25, 65, 31], [89, 26, 65, 32, "onBlur"], [89, 32, 65, 38], [89, 34, 65, 40], [90, 12, 66, 20, "ref"], [90, 15, 66, 23], [90, 16, 66, 24, "current"], [90, 23, 66, 31], [90, 24, 66, 32, "onBlur"], [90, 30, 66, 38], [90, 31, 66, 39, "event"], [90, 36, 66, 44], [90, 37, 66, 45], [91, 10, 67, 16], [92, 10, 68, 16, "dispatch"], [92, 18, 68, 24], [92, 19, 68, 25], [93, 12, 68, 27, "type"], [93, 16, 68, 31], [93, 18, 68, 33], [93, 25, 68, 40], [94, 12, 68, 42, "value"], [94, 17, 68, 47], [94, 19, 68, 49], [95, 10, 68, 55], [95, 11, 68, 56], [95, 12, 68, 57], [96, 8, 69, 12], [96, 9, 69, 13], [97, 6, 70, 8], [98, 6, 71, 8], [98, 13, 71, 15, "handlers"], [98, 21, 71, 23], [99, 4, 72, 4], [99, 5, 72, 5], [99, 7, 72, 7], [99, 8, 72, 8, "mask"], [99, 12, 72, 12], [99, 13, 72, 13], [99, 14, 72, 14], [100, 4, 73, 4], [100, 11, 73, 11, "handlers"], [100, 19, 73, 19], [101, 2, 74, 0], [102, 2, 75, 0, "exports"], [102, 9, 75, 7], [102, 10, 75, 8, "useInteraction"], [102, 24, 75, 22], [102, 27, 75, 25, "useInteraction"], [102, 41, 75, 39], [103, 0, 75, 40], [103, 3]], "functionMap": {"names": ["<global>", "useInteraction", "<anonymous>", "handlers.onMouseDown", "handlers.onMouseUp", "handlers.onPressIn", "handlers.onPressOut", "handlers.onHoverIn", "handlers.onHoverOut", "handlers.onFocus", "handlers.onBlur"], "mappings": "AAA;ACM;0CCG;uCCK;iBDK;qCEC;iBFK;qCGG;iBHK;sCIC;iBJK;iCKI;aLK;kCMC;aNK;+BOG;aPK;8BQC;aRK;KDG;CDE"}}, "type": "js/module"}]}