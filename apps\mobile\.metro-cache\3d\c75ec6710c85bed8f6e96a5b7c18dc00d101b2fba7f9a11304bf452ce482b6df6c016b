{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 93, "index": 108}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 0, "index": 447}, "end": {"line": 24, "column": 2, "index": 568}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var nativeComponentName = 'RNSScreenStackHeaderSubview';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSScreenStackHeaderSubview\",\n    validAttributes: {\n      type: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 19, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [8, 20, 1, 13, "exports"], [8, 27, 1, 13], [8, 28, 1, 13, "__INTERNAL_VIEW_CONFIG"], [8, 50, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_codegenNativeComponent"], [9, 29, 3, 0], [9, 32, 3, 0, "_interopRequireDefault"], [9, 54, 3, 0], [9, 55, 3, 0, "require"], [9, 62, 3, 0], [9, 63, 3, 0, "_dependencyMap"], [9, 77, 3, 0], [10, 2, 19, 0], [10, 6, 19, 0, "NativeComponentRegistry"], [10, 29, 24, 2], [10, 32, 19, 0, "require"], [10, 39, 24, 2], [10, 40, 24, 2, "_dependencyMap"], [10, 54, 24, 2], [10, 123, 24, 1], [10, 124, 24, 2], [11, 2, 19, 0], [11, 6, 19, 0, "nativeComponentName"], [11, 25, 24, 2], [11, 28, 19, 0], [11, 57, 24, 2], [12, 2, 19, 0], [12, 6, 19, 0, "__INTERNAL_VIEW_CONFIG"], [12, 28, 24, 2], [12, 31, 24, 2, "exports"], [12, 38, 24, 2], [12, 39, 24, 2, "__INTERNAL_VIEW_CONFIG"], [12, 61, 24, 2], [12, 64, 19, 0], [13, 4, 19, 0, "uiViewClassName"], [13, 19, 24, 2], [13, 21, 19, 0], [13, 50, 24, 2], [14, 4, 19, 0, "validAttributes"], [14, 19, 24, 2], [14, 21, 19, 0], [15, 6, 19, 0, "type"], [15, 10, 24, 2], [15, 12, 19, 0], [16, 4, 24, 1], [17, 2, 24, 1], [17, 3, 24, 2], [18, 2, 24, 2], [18, 6, 24, 2, "_default"], [18, 14, 24, 2], [18, 17, 24, 2, "exports"], [18, 24, 24, 2], [18, 25, 24, 2, "default"], [18, 32, 24, 2], [18, 35, 19, 0, "NativeComponentRegistry"], [18, 58, 24, 2], [18, 59, 19, 0, "get"], [18, 62, 24, 2], [18, 63, 19, 0, "nativeComponentName"], [18, 82, 24, 2], [18, 84, 19, 0], [18, 90, 19, 0, "__INTERNAL_VIEW_CONFIG"], [18, 112, 24, 1], [18, 113, 24, 2], [19, 0, 24, 2], [19, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}