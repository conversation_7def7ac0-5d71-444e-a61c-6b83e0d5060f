{"dependencies": [{"name": "@react-navigation/core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 196, "index": 211}}], "key": "Wm75LgE4xYscVWo0KoLFlflJQCo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 212}, "end": {"line": 4, "column": 31, "index": 243}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./LinkingContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 244}, "end": {"line": 5, "column": 53, "index": 297}}], "key": "r/0Yvi+HouDAqn4vN4m4I6AMfEI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useLinkBuilder = useLinkBuilder;\n  var _core = require(_dependencyMap[0], \"@react-navigation/core\");\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _LinkingContext = require(_dependencyMap[2], \"./LinkingContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Helpers to build href or action based on the linking options.\n   *\n   * @returns `buildHref` to build an `href` for screen and `buildAction` to build an action from an `href`.\n   */\n  function useLinkBuilder() {\n    var navigation = React.useContext(_core.NavigationHelpersContext);\n    var route = React.useContext(_core.NavigationRouteContext);\n    var _React$useContext = React.useContext(_LinkingContext.LinkingContext),\n      options = _React$useContext.options;\n    var focusedRouteState = (0, _core.useStateForPath)();\n    var getPathFromStateHelper = options?.getPathFromState ?? _core.getPathFromState;\n    var getStateFromPathHelper = options?.getStateFromPath ?? _core.getStateFromPath;\n    var getActionFromStateHelper = options?.getActionFromState ?? _core.getActionFromState;\n    var buildHref = React.useCallback((name, params) => {\n      if (options?.enabled === false) {\n        return undefined;\n      }\n\n      // Check that we're inside:\n      // - navigator's context\n      // - route context of the navigator (could be a screen, tab, etc.)\n      // - route matches the state for path (from the screen's context)\n      // This ensures that we're inside a screen\n      var isScreen = navigation && route?.key && focusedRouteState ? route.key === (0, _core.findFocusedRoute)(focusedRouteState)?.key && navigation.getState().routes.some(r => r.key === route.key) : false;\n      var stateForRoute = {\n        routes: [{\n          name,\n          params\n        }]\n      };\n      var constructState = state => {\n        if (state) {\n          var _route = state.routes[0];\n\n          // If we're inside a screen and at the innermost route\n          // We need to replace the state with the provided one\n          // This assumes that we're navigating to a sibling route\n          if (isScreen && !_route.state) {\n            return stateForRoute;\n          }\n\n          // Otherwise, dive into the nested state of the route\n          return {\n            routes: [{\n              ..._route,\n              state: constructState(_route.state)\n            }]\n          };\n        }\n\n        // Once there is no more nested state, we're at the innermost route\n        // We can add a state based on provided parameters\n        // This assumes that we're navigating to a child of this route\n        // In this case, the helper is used in a navigator for its routes\n        return stateForRoute;\n      };\n      var state = constructState(focusedRouteState);\n      var path = getPathFromStateHelper(state, options?.config);\n      return path;\n    }, [options?.enabled, options?.config, route?.key, navigation, focusedRouteState, getPathFromStateHelper]);\n    var buildAction = React.useCallback(href => {\n      if (!href.startsWith('/')) {\n        throw new Error(`The href must start with '/' (${href}).`);\n      }\n      var state = getStateFromPathHelper(href, options?.config);\n      if (state) {\n        var action = getActionFromStateHelper(state, options?.config);\n        return action ?? _core.CommonActions.reset(state);\n      } else {\n        throw new Error('Failed to parse the href to a navigation state.');\n      }\n    }, [options?.config, getStateFromPathHelper, getActionFromStateHelper]);\n    return {\n      buildHref,\n      buildAction\n    };\n  }\n});", "lineCount": 90, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useLinkBuilder"], [7, 24, 1, 13], [7, 27, 1, 13, "useLinkBuilder"], [7, 41, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_LinkingContext"], [10, 21, 5, 0], [10, 24, 5, 0, "require"], [10, 31, 5, 0], [10, 32, 5, 0, "_dependencyMap"], [10, 46, 5, 0], [11, 2, 5, 53], [11, 11, 5, 53, "_interopRequireWildcard"], [11, 35, 5, 53, "e"], [11, 36, 5, 53], [11, 38, 5, 53, "t"], [11, 39, 5, 53], [11, 68, 5, 53, "WeakMap"], [11, 75, 5, 53], [11, 81, 5, 53, "r"], [11, 82, 5, 53], [11, 89, 5, 53, "WeakMap"], [11, 96, 5, 53], [11, 100, 5, 53, "n"], [11, 101, 5, 53], [11, 108, 5, 53, "WeakMap"], [11, 115, 5, 53], [11, 127, 5, 53, "_interopRequireWildcard"], [11, 150, 5, 53], [11, 162, 5, 53, "_interopRequireWildcard"], [11, 163, 5, 53, "e"], [11, 164, 5, 53], [11, 166, 5, 53, "t"], [11, 167, 5, 53], [11, 176, 5, 53, "t"], [11, 177, 5, 53], [11, 181, 5, 53, "e"], [11, 182, 5, 53], [11, 186, 5, 53, "e"], [11, 187, 5, 53], [11, 188, 5, 53, "__esModule"], [11, 198, 5, 53], [11, 207, 5, 53, "e"], [11, 208, 5, 53], [11, 214, 5, 53, "o"], [11, 215, 5, 53], [11, 217, 5, 53, "i"], [11, 218, 5, 53], [11, 220, 5, 53, "f"], [11, 221, 5, 53], [11, 226, 5, 53, "__proto__"], [11, 235, 5, 53], [11, 243, 5, 53, "default"], [11, 250, 5, 53], [11, 252, 5, 53, "e"], [11, 253, 5, 53], [11, 270, 5, 53, "e"], [11, 271, 5, 53], [11, 294, 5, 53, "e"], [11, 295, 5, 53], [11, 320, 5, 53, "e"], [11, 321, 5, 53], [11, 330, 5, 53, "f"], [11, 331, 5, 53], [11, 337, 5, 53, "o"], [11, 338, 5, 53], [11, 341, 5, 53, "t"], [11, 342, 5, 53], [11, 345, 5, 53, "n"], [11, 346, 5, 53], [11, 349, 5, 53, "r"], [11, 350, 5, 53], [11, 358, 5, 53, "o"], [11, 359, 5, 53], [11, 360, 5, 53, "has"], [11, 363, 5, 53], [11, 364, 5, 53, "e"], [11, 365, 5, 53], [11, 375, 5, 53, "o"], [11, 376, 5, 53], [11, 377, 5, 53, "get"], [11, 380, 5, 53], [11, 381, 5, 53, "e"], [11, 382, 5, 53], [11, 385, 5, 53, "o"], [11, 386, 5, 53], [11, 387, 5, 53, "set"], [11, 390, 5, 53], [11, 391, 5, 53, "e"], [11, 392, 5, 53], [11, 394, 5, 53, "f"], [11, 395, 5, 53], [11, 409, 5, 53, "_t"], [11, 411, 5, 53], [11, 415, 5, 53, "e"], [11, 416, 5, 53], [11, 432, 5, 53, "_t"], [11, 434, 5, 53], [11, 441, 5, 53, "hasOwnProperty"], [11, 455, 5, 53], [11, 456, 5, 53, "call"], [11, 460, 5, 53], [11, 461, 5, 53, "e"], [11, 462, 5, 53], [11, 464, 5, 53, "_t"], [11, 466, 5, 53], [11, 473, 5, 53, "i"], [11, 474, 5, 53], [11, 478, 5, 53, "o"], [11, 479, 5, 53], [11, 482, 5, 53, "Object"], [11, 488, 5, 53], [11, 489, 5, 53, "defineProperty"], [11, 503, 5, 53], [11, 508, 5, 53, "Object"], [11, 514, 5, 53], [11, 515, 5, 53, "getOwnPropertyDescriptor"], [11, 539, 5, 53], [11, 540, 5, 53, "e"], [11, 541, 5, 53], [11, 543, 5, 53, "_t"], [11, 545, 5, 53], [11, 552, 5, 53, "i"], [11, 553, 5, 53], [11, 554, 5, 53, "get"], [11, 557, 5, 53], [11, 561, 5, 53, "i"], [11, 562, 5, 53], [11, 563, 5, 53, "set"], [11, 566, 5, 53], [11, 570, 5, 53, "o"], [11, 571, 5, 53], [11, 572, 5, 53, "f"], [11, 573, 5, 53], [11, 575, 5, 53, "_t"], [11, 577, 5, 53], [11, 579, 5, 53, "i"], [11, 580, 5, 53], [11, 584, 5, 53, "f"], [11, 585, 5, 53], [11, 586, 5, 53, "_t"], [11, 588, 5, 53], [11, 592, 5, 53, "e"], [11, 593, 5, 53], [11, 594, 5, 53, "_t"], [11, 596, 5, 53], [11, 607, 5, 53, "f"], [11, 608, 5, 53], [11, 613, 5, 53, "e"], [11, 614, 5, 53], [11, 616, 5, 53, "t"], [11, 617, 5, 53], [12, 2, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 0, 10, 0], [17, 2, 11, 7], [17, 11, 11, 16, "useLinkBuilder"], [17, 25, 11, 30, "useLinkBuilder"], [17, 26, 11, 30], [17, 28, 11, 33], [18, 4, 12, 2], [18, 8, 12, 8, "navigation"], [18, 18, 12, 18], [18, 21, 12, 21, "React"], [18, 26, 12, 26], [18, 27, 12, 27, "useContext"], [18, 37, 12, 37], [18, 38, 12, 38, "NavigationHelpersContext"], [18, 68, 12, 62], [18, 69, 12, 63], [19, 4, 13, 2], [19, 8, 13, 8, "route"], [19, 13, 13, 13], [19, 16, 13, 16, "React"], [19, 21, 13, 21], [19, 22, 13, 22, "useContext"], [19, 32, 13, 32], [19, 33, 13, 33, "NavigationRouteContext"], [19, 61, 13, 55], [19, 62, 13, 56], [20, 4, 14, 2], [20, 8, 14, 2, "_React$useContext"], [20, 25, 14, 2], [20, 28, 16, 6, "React"], [20, 33, 16, 11], [20, 34, 16, 12, "useContext"], [20, 44, 16, 22], [20, 45, 16, 23, "LinkingContext"], [20, 75, 16, 37], [20, 76, 16, 38], [21, 6, 15, 4, "options"], [21, 13, 15, 11], [21, 16, 15, 11, "_React$useContext"], [21, 33, 15, 11], [21, 34, 15, 4, "options"], [21, 41, 15, 11], [22, 4, 17, 2], [22, 8, 17, 8, "focusedRouteState"], [22, 25, 17, 25], [22, 28, 17, 28], [22, 32, 17, 28, "useStateForPath"], [22, 53, 17, 43], [22, 55, 17, 44], [22, 56, 17, 45], [23, 4, 18, 2], [23, 8, 18, 8, "getPathFromStateHelper"], [23, 30, 18, 30], [23, 33, 18, 33, "options"], [23, 40, 18, 40], [23, 42, 18, 42, "getPathFromState"], [23, 58, 18, 58], [23, 62, 18, 62, "getPathFromState"], [23, 84, 18, 78], [24, 4, 19, 2], [24, 8, 19, 8, "getStateFromPathHelper"], [24, 30, 19, 30], [24, 33, 19, 33, "options"], [24, 40, 19, 40], [24, 42, 19, 42, "getStateFromPath"], [24, 58, 19, 58], [24, 62, 19, 62, "getStateFromPath"], [24, 84, 19, 78], [25, 4, 20, 2], [25, 8, 20, 8, "getActionFromStateHelper"], [25, 32, 20, 32], [25, 35, 20, 35, "options"], [25, 42, 20, 42], [25, 44, 20, 44, "getActionFromState"], [25, 62, 20, 62], [25, 66, 20, 66, "getActionFromState"], [25, 90, 20, 84], [26, 4, 21, 2], [26, 8, 21, 8, "buildHref"], [26, 17, 21, 17], [26, 20, 21, 20, "React"], [26, 25, 21, 25], [26, 26, 21, 26, "useCallback"], [26, 37, 21, 37], [26, 38, 21, 38], [26, 39, 21, 39, "name"], [26, 43, 21, 43], [26, 45, 21, 45, "params"], [26, 51, 21, 51], [26, 56, 21, 56], [27, 6, 22, 4], [27, 10, 22, 8, "options"], [27, 17, 22, 15], [27, 19, 22, 17, "enabled"], [27, 26, 22, 24], [27, 31, 22, 29], [27, 36, 22, 34], [27, 38, 22, 36], [28, 8, 23, 6], [28, 15, 23, 13, "undefined"], [28, 24, 23, 22], [29, 6, 24, 4], [31, 6, 26, 4], [32, 6, 27, 4], [33, 6, 28, 4], [34, 6, 29, 4], [35, 6, 30, 4], [36, 6, 31, 4], [36, 10, 31, 10, "isScreen"], [36, 18, 31, 18], [36, 21, 31, 21, "navigation"], [36, 31, 31, 31], [36, 35, 31, 35, "route"], [36, 40, 31, 40], [36, 42, 31, 42, "key"], [36, 45, 31, 45], [36, 49, 31, 49, "focusedRouteState"], [36, 66, 31, 66], [36, 69, 31, 69, "route"], [36, 74, 31, 74], [36, 75, 31, 75, "key"], [36, 78, 31, 78], [36, 83, 31, 83], [36, 87, 31, 83, "findFocusedRoute"], [36, 109, 31, 99], [36, 111, 31, 100, "focusedRouteState"], [36, 128, 31, 117], [36, 129, 31, 118], [36, 131, 31, 120, "key"], [36, 134, 31, 123], [36, 138, 31, 127, "navigation"], [36, 148, 31, 137], [36, 149, 31, 138, "getState"], [36, 157, 31, 146], [36, 158, 31, 147], [36, 159, 31, 148], [36, 160, 31, 149, "routes"], [36, 166, 31, 155], [36, 167, 31, 156, "some"], [36, 171, 31, 160], [36, 172, 31, 161, "r"], [36, 173, 31, 162], [36, 177, 31, 166, "r"], [36, 178, 31, 167], [36, 179, 31, 168, "key"], [36, 182, 31, 171], [36, 187, 31, 176, "route"], [36, 192, 31, 181], [36, 193, 31, 182, "key"], [36, 196, 31, 185], [36, 197, 31, 186], [36, 200, 31, 189], [36, 205, 31, 194], [37, 6, 32, 4], [37, 10, 32, 10, "stateForRoute"], [37, 23, 32, 23], [37, 26, 32, 26], [38, 8, 33, 6, "routes"], [38, 14, 33, 12], [38, 16, 33, 14], [38, 17, 33, 15], [39, 10, 34, 8, "name"], [39, 14, 34, 12], [40, 10, 35, 8, "params"], [41, 8, 36, 6], [41, 9, 36, 7], [42, 6, 37, 4], [42, 7, 37, 5], [43, 6, 38, 4], [43, 10, 38, 10, "constructState"], [43, 24, 38, 24], [43, 27, 38, 27, "state"], [43, 32, 38, 32], [43, 36, 38, 36], [44, 8, 39, 6], [44, 12, 39, 10, "state"], [44, 17, 39, 15], [44, 19, 39, 17], [45, 10, 40, 8], [45, 14, 40, 14, "route"], [45, 20, 40, 19], [45, 23, 40, 22, "state"], [45, 28, 40, 27], [45, 29, 40, 28, "routes"], [45, 35, 40, 34], [45, 36, 40, 35], [45, 37, 40, 36], [45, 38, 40, 37], [47, 10, 42, 8], [48, 10, 43, 8], [49, 10, 44, 8], [50, 10, 45, 8], [50, 14, 45, 12, "isScreen"], [50, 22, 45, 20], [50, 26, 45, 24], [50, 27, 45, 25, "route"], [50, 33, 45, 30], [50, 34, 45, 31, "state"], [50, 39, 45, 36], [50, 41, 45, 38], [51, 12, 46, 10], [51, 19, 46, 17, "stateForRoute"], [51, 32, 46, 30], [52, 10, 47, 8], [54, 10, 49, 8], [55, 10, 50, 8], [55, 17, 50, 15], [56, 12, 51, 10, "routes"], [56, 18, 51, 16], [56, 20, 51, 18], [56, 21, 51, 19], [57, 14, 52, 12], [57, 17, 52, 15, "route"], [57, 23, 52, 20], [58, 14, 53, 12, "state"], [58, 19, 53, 17], [58, 21, 53, 19, "constructState"], [58, 35, 53, 33], [58, 36, 53, 34, "route"], [58, 42, 53, 39], [58, 43, 53, 40, "state"], [58, 48, 53, 45], [59, 12, 54, 10], [59, 13, 54, 11], [60, 10, 55, 8], [60, 11, 55, 9], [61, 8, 56, 6], [63, 8, 58, 6], [64, 8, 59, 6], [65, 8, 60, 6], [66, 8, 61, 6], [67, 8, 62, 6], [67, 15, 62, 13, "stateForRoute"], [67, 28, 62, 26], [68, 6, 63, 4], [68, 7, 63, 5], [69, 6, 64, 4], [69, 10, 64, 10, "state"], [69, 15, 64, 15], [69, 18, 64, 18, "constructState"], [69, 32, 64, 32], [69, 33, 64, 33, "focusedRouteState"], [69, 50, 64, 50], [69, 51, 64, 51], [70, 6, 65, 4], [70, 10, 65, 10, "path"], [70, 14, 65, 14], [70, 17, 65, 17, "getPathFromStateHelper"], [70, 39, 65, 39], [70, 40, 65, 40, "state"], [70, 45, 65, 45], [70, 47, 65, 47, "options"], [70, 54, 65, 54], [70, 56, 65, 56, "config"], [70, 62, 65, 62], [70, 63, 65, 63], [71, 6, 66, 4], [71, 13, 66, 11, "path"], [71, 17, 66, 15], [72, 4, 67, 2], [72, 5, 67, 3], [72, 7, 67, 5], [72, 8, 67, 6, "options"], [72, 15, 67, 13], [72, 17, 67, 15, "enabled"], [72, 24, 67, 22], [72, 26, 67, 24, "options"], [72, 33, 67, 31], [72, 35, 67, 33, "config"], [72, 41, 67, 39], [72, 43, 67, 41, "route"], [72, 48, 67, 46], [72, 50, 67, 48, "key"], [72, 53, 67, 51], [72, 55, 67, 53, "navigation"], [72, 65, 67, 63], [72, 67, 67, 65, "focusedRouteState"], [72, 84, 67, 82], [72, 86, 67, 84, "getPathFromStateHelper"], [72, 108, 67, 106], [72, 109, 67, 107], [72, 110, 67, 108], [73, 4, 68, 2], [73, 8, 68, 8, "buildAction"], [73, 19, 68, 19], [73, 22, 68, 22, "React"], [73, 27, 68, 27], [73, 28, 68, 28, "useCallback"], [73, 39, 68, 39], [73, 40, 68, 40, "href"], [73, 44, 68, 44], [73, 48, 68, 48], [74, 6, 69, 4], [74, 10, 69, 8], [74, 11, 69, 9, "href"], [74, 15, 69, 13], [74, 16, 69, 14, "startsWith"], [74, 26, 69, 24], [74, 27, 69, 25], [74, 30, 69, 28], [74, 31, 69, 29], [74, 33, 69, 31], [75, 8, 70, 6], [75, 14, 70, 12], [75, 18, 70, 16, "Error"], [75, 23, 70, 21], [75, 24, 70, 22], [75, 57, 70, 55, "href"], [75, 61, 70, 59], [75, 65, 70, 63], [75, 66, 70, 64], [76, 6, 71, 4], [77, 6, 72, 4], [77, 10, 72, 10, "state"], [77, 15, 72, 15], [77, 18, 72, 18, "getStateFromPathHelper"], [77, 40, 72, 40], [77, 41, 72, 41, "href"], [77, 45, 72, 45], [77, 47, 72, 47, "options"], [77, 54, 72, 54], [77, 56, 72, 56, "config"], [77, 62, 72, 62], [77, 63, 72, 63], [78, 6, 73, 4], [78, 10, 73, 8, "state"], [78, 15, 73, 13], [78, 17, 73, 15], [79, 8, 74, 6], [79, 12, 74, 12, "action"], [79, 18, 74, 18], [79, 21, 74, 21, "getActionFromStateHelper"], [79, 45, 74, 45], [79, 46, 74, 46, "state"], [79, 51, 74, 51], [79, 53, 74, 53, "options"], [79, 60, 74, 60], [79, 62, 74, 62, "config"], [79, 68, 74, 68], [79, 69, 74, 69], [80, 8, 75, 6], [80, 15, 75, 13, "action"], [80, 21, 75, 19], [80, 25, 75, 23, "CommonActions"], [80, 44, 75, 36], [80, 45, 75, 37, "reset"], [80, 50, 75, 42], [80, 51, 75, 43, "state"], [80, 56, 75, 48], [80, 57, 75, 49], [81, 6, 76, 4], [81, 7, 76, 5], [81, 13, 76, 11], [82, 8, 77, 6], [82, 14, 77, 12], [82, 18, 77, 16, "Error"], [82, 23, 77, 21], [82, 24, 77, 22], [82, 73, 77, 71], [82, 74, 77, 72], [83, 6, 78, 4], [84, 4, 79, 2], [84, 5, 79, 3], [84, 7, 79, 5], [84, 8, 79, 6, "options"], [84, 15, 79, 13], [84, 17, 79, 15, "config"], [84, 23, 79, 21], [84, 25, 79, 23, "getStateFromPathHelper"], [84, 47, 79, 45], [84, 49, 79, 47, "getActionFromStateHelper"], [84, 73, 79, 71], [84, 74, 79, 72], [84, 75, 79, 73], [85, 4, 80, 2], [85, 11, 80, 9], [86, 6, 81, 4, "buildHref"], [86, 15, 81, 13], [87, 6, 82, 4, "buildAction"], [88, 4, 83, 2], [88, 5, 83, 3], [89, 2, 84, 0], [90, 0, 84, 1], [90, 3]], "functionMap": {"names": ["<global>", "useLinkBuilder", "buildHref", "navigation.getState.routes.some$argument_0", "constructState", "buildAction"], "mappings": "AAA;OCU;sCCU;iKCU,wBD;2BEO;KFyB;GDI;wCIC;GJW;CDK"}}, "type": "js/module"}]}