{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./NativeTiming", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 42}}], "key": "gVIp452uZqNJk5Rsq+e6syfk1MQ=", "exportNames": ["*"]}}, {"name": "../../BatchedBridge/BatchedBridge", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 22}, "end": {"line": 13, "column": 66}}], "key": "pPRmlENUzWeIw881/Qt8eAb5Xro=", "exportNames": ["*"]}}, {"name": "../../Performance/Systrace", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 17}, "end": {"line": 14, "column": 54}}], "key": "zbLsO45P34ZkR7tqRnHSvFfS/EU=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 18}, "end": {"line": 15, "column": 38}}], "key": "oQpL0Es3H146KnQH9ygFeHrzVP4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeTiming = _interopRequireDefault(require(_dependencyMap[1], \"./NativeTiming\"));\n  var BatchedBridge = require(_dependencyMap[2], \"../../BatchedBridge/BatchedBridge\").default;\n  var Systrace = require(_dependencyMap[3], \"../../Performance/Systrace\");\n  var invariant = require(_dependencyMap[4], \"invariant\");\n  var FRAME_DURATION = 1000 / 60;\n  var IDLE_CALLBACK_FRAME_DEADLINE = 1;\n  var callbacks = [];\n  var types = [];\n  var timerIDs = [];\n  var freeIdxs = [];\n  var reactNativeMicrotasks = [];\n  var requestIdleCallbacks = [];\n  var requestIdleCallbackTimeouts = {};\n  var GUID = 1;\n  var errors = [];\n  var hasEmittedTimeDriftWarning = false;\n  function _getFreeIndex() {\n    var freeIdx = freeIdxs.pop();\n    if (freeIdx === undefined) {\n      return timerIDs.length;\n    }\n    return freeIdx;\n  }\n  function _allocateCallback(func, type) {\n    var id = GUID++;\n    var freeIndex = _getFreeIndex();\n    timerIDs[freeIndex] = id;\n    callbacks[freeIndex] = func;\n    types[freeIndex] = type;\n    return id;\n  }\n  function _callTimer(timerID, frameTime, didTimeout) {\n    if (timerID > GUID) {\n      console.warn('Tried to call timer with ID %s but no such timer exists.', timerID);\n    }\n    var timerIndex = timerIDs.indexOf(timerID);\n    if (timerIndex === -1) {\n      return;\n    }\n    var type = types[timerIndex];\n    var callback = callbacks[timerIndex];\n    if (!callback || !type) {\n      console.error('No callback found for timerID ' + timerID);\n      return;\n    }\n    if (__DEV__) {\n      Systrace.beginEvent(type + ' [invoke]');\n    }\n    if (type !== 'setInterval') {\n      _clearIndex(timerIndex);\n    }\n    try {\n      if (type === 'setTimeout' || type === 'setInterval' || type === 'queueReactNativeMicrotask') {\n        callback();\n      } else if (type === 'requestAnimationFrame') {\n        callback(global.performance.now());\n      } else if (type === 'requestIdleCallback') {\n        callback({\n          timeRemaining: function () {\n            return Math.max(0, FRAME_DURATION - (global.performance.now() - frameTime));\n          },\n          didTimeout: !!didTimeout\n        });\n      } else {\n        console.error('Tried to call a callback with invalid type: ' + type);\n      }\n    } catch (e) {\n      errors.push(e);\n    }\n    if (__DEV__) {\n      Systrace.endEvent();\n    }\n  }\n  function _callReactNativeMicrotasksPass() {\n    if (reactNativeMicrotasks.length === 0) {\n      return false;\n    }\n    if (__DEV__) {\n      Systrace.beginEvent('callReactNativeMicrotasksPass()');\n    }\n    var passReactNativeMicrotasks = reactNativeMicrotasks;\n    reactNativeMicrotasks = [];\n    for (var i = 0; i < passReactNativeMicrotasks.length; ++i) {\n      _callTimer(passReactNativeMicrotasks[i], 0);\n    }\n    if (__DEV__) {\n      Systrace.endEvent();\n    }\n    return reactNativeMicrotasks.length > 0;\n  }\n  function _clearIndex(i) {\n    timerIDs[i] = null;\n    callbacks[i] = null;\n    types[i] = null;\n    freeIdxs.push(i);\n  }\n  function _freeCallback(timerID) {\n    if (timerID == null) {\n      return;\n    }\n    var index = timerIDs.indexOf(timerID);\n    if (index !== -1) {\n      var type = types[index];\n      _clearIndex(index);\n      if (type !== 'queueReactNativeMicrotask' && type !== 'requestIdleCallback') {\n        deleteTimer(timerID);\n      }\n    }\n  }\n  var JSTimers = {\n    setTimeout: function (func, duration) {\n      for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n        args[_key - 2] = arguments[_key];\n      }\n      var id = _allocateCallback(() => func.apply(undefined, args), 'setTimeout');\n      createTimer(id, duration || 0, Date.now(), false);\n      return id;\n    },\n    setInterval: function (func, duration) {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 2 ? _len2 - 2 : 0), _key2 = 2; _key2 < _len2; _key2++) {\n        args[_key2 - 2] = arguments[_key2];\n      }\n      var id = _allocateCallback(() => func.apply(undefined, args), 'setInterval');\n      createTimer(id, duration || 0, Date.now(), true);\n      return id;\n    },\n    queueReactNativeMicrotask: function (func) {\n      for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n        args[_key3 - 1] = arguments[_key3];\n      }\n      var id = _allocateCallback(() => func.apply(undefined, args), 'queueReactNativeMicrotask');\n      reactNativeMicrotasks.push(id);\n      return id;\n    },\n    requestAnimationFrame: function (func) {\n      var id = _allocateCallback(func, 'requestAnimationFrame');\n      createTimer(id, 1, Date.now(), false);\n      return id;\n    },\n    requestIdleCallback: function (func, options) {\n      if (requestIdleCallbacks.length === 0) {\n        setSendIdleEvents(true);\n      }\n      var timeout = options && options.timeout;\n      var id = _allocateCallback(timeout != null ? deadline => {\n        var timeoutId = requestIdleCallbackTimeouts[id];\n        if (timeoutId) {\n          JSTimers.clearTimeout(timeoutId);\n          delete requestIdleCallbackTimeouts[id];\n        }\n        return func(deadline);\n      } : func, 'requestIdleCallback');\n      requestIdleCallbacks.push(id);\n      if (timeout != null) {\n        var timeoutId = JSTimers.setTimeout(() => {\n          var index = requestIdleCallbacks.indexOf(id);\n          if (index > -1) {\n            requestIdleCallbacks.splice(index, 1);\n            _callTimer(id, global.performance.now(), true);\n          }\n          delete requestIdleCallbackTimeouts[id];\n          if (requestIdleCallbacks.length === 0) {\n            setSendIdleEvents(false);\n          }\n        }, timeout);\n        requestIdleCallbackTimeouts[id] = timeoutId;\n      }\n      return id;\n    },\n    cancelIdleCallback: function (timerID) {\n      _freeCallback(timerID);\n      var index = requestIdleCallbacks.indexOf(timerID);\n      if (index !== -1) {\n        requestIdleCallbacks.splice(index, 1);\n      }\n      var timeoutId = requestIdleCallbackTimeouts[timerID];\n      if (timeoutId) {\n        JSTimers.clearTimeout(timeoutId);\n        delete requestIdleCallbackTimeouts[timerID];\n      }\n      if (requestIdleCallbacks.length === 0) {\n        setSendIdleEvents(false);\n      }\n    },\n    clearTimeout: function (timerID) {\n      _freeCallback(timerID);\n    },\n    clearInterval: function (timerID) {\n      _freeCallback(timerID);\n    },\n    clearReactNativeMicrotask: function (timerID) {\n      _freeCallback(timerID);\n      var index = reactNativeMicrotasks.indexOf(timerID);\n      if (index !== -1) {\n        reactNativeMicrotasks.splice(index, 1);\n      }\n    },\n    cancelAnimationFrame: function (timerID) {\n      _freeCallback(timerID);\n    },\n    callTimers: function (timersToCall) {\n      invariant(timersToCall.length !== 0, 'Cannot call `callTimers` with an empty list of IDs.');\n      errors.length = 0;\n      for (var i = 0; i < timersToCall.length; i++) {\n        _callTimer(timersToCall[i], 0);\n      }\n      var errorCount = errors.length;\n      if (errorCount > 0) {\n        if (errorCount > 1) {\n          for (var ii = 1; ii < errorCount; ii++) {\n            JSTimers.setTimeout((error => {\n              throw error;\n            }).bind(null, errors[ii]), 0);\n          }\n        }\n        throw errors[0];\n      }\n    },\n    callIdleCallbacks: function (frameTime) {\n      if (FRAME_DURATION - (Date.now() - frameTime) < IDLE_CALLBACK_FRAME_DEADLINE) {\n        return;\n      }\n      errors.length = 0;\n      if (requestIdleCallbacks.length > 0) {\n        var passIdleCallbacks = requestIdleCallbacks;\n        requestIdleCallbacks = [];\n        for (var i = 0; i < passIdleCallbacks.length; ++i) {\n          _callTimer(passIdleCallbacks[i], frameTime);\n        }\n      }\n      if (requestIdleCallbacks.length === 0) {\n        setSendIdleEvents(false);\n      }\n      errors.forEach(error => JSTimers.setTimeout(() => {\n        throw error;\n      }, 0));\n    },\n    callReactNativeMicrotasks() {\n      errors.length = 0;\n      while (_callReactNativeMicrotasksPass()) {}\n      errors.forEach(error => JSTimers.setTimeout(() => {\n        throw error;\n      }, 0));\n    },\n    emitTimeDriftWarning(warningMessage) {\n      if (hasEmittedTimeDriftWarning) {\n        return;\n      }\n      hasEmittedTimeDriftWarning = true;\n      console.warn(warningMessage);\n    }\n  };\n  function createTimer(callbackID, duration, jsSchedulingTime, repeats) {\n    invariant(_NativeTiming.default, 'NativeTiming is available');\n    _NativeTiming.default.createTimer(callbackID, duration, jsSchedulingTime, repeats);\n  }\n  function deleteTimer(timerID) {\n    invariant(_NativeTiming.default, 'NativeTiming is available');\n    _NativeTiming.default.deleteTimer(timerID);\n  }\n  function setSendIdleEvents(sendIdleEvents) {\n    invariant(_NativeTiming.default, 'NativeTiming is available');\n    _NativeTiming.default.setSendIdleEvents(sendIdleEvents);\n  }\n  var ExportedJSTimers;\n  if (!_NativeTiming.default) {\n    console.warn(\"Timing native module is not available, can't set timers.\");\n    ExportedJSTimers = {\n      callReactNativeMicrotasks: JSTimers.callReactNativeMicrotasks,\n      queueReactNativeMicrotask: JSTimers.queueReactNativeMicrotask\n    };\n  } else {\n    ExportedJSTimers = JSTimers;\n  }\n  BatchedBridge.setReactNativeMicrotasksCallback(JSTimers.callReactNativeMicrotasks);\n  var _default = exports.default = ExportedJSTimers;\n});", "lineCount": 283, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeTiming"], [7, 19, 11, 0], [7, 22, 11, 0, "_interopRequireDefault"], [7, 44, 11, 0], [7, 45, 11, 0, "require"], [7, 52, 11, 0], [7, 53, 11, 0, "_dependencyMap"], [7, 67, 11, 0], [8, 2, 13, 0], [8, 6, 13, 6, "BatchedBridge"], [8, 19, 13, 19], [8, 22, 13, 22, "require"], [8, 29, 13, 29], [8, 30, 13, 29, "_dependencyMap"], [8, 44, 13, 29], [8, 84, 13, 65], [8, 85, 13, 66], [8, 86, 13, 67, "default"], [8, 93, 13, 74], [9, 2, 14, 0], [9, 6, 14, 6, "Systrace"], [9, 14, 14, 14], [9, 17, 14, 17, "require"], [9, 24, 14, 24], [9, 25, 14, 24, "_dependencyMap"], [9, 39, 14, 24], [9, 72, 14, 53], [9, 73, 14, 54], [10, 2, 15, 0], [10, 6, 15, 6, "invariant"], [10, 15, 15, 15], [10, 18, 15, 18, "require"], [10, 25, 15, 25], [10, 26, 15, 25, "_dependencyMap"], [10, 40, 15, 25], [10, 56, 15, 37], [10, 57, 15, 38], [11, 2, 32, 0], [11, 6, 32, 6, "FRAME_DURATION"], [11, 20, 32, 20], [11, 23, 32, 23], [11, 27, 32, 27], [11, 30, 32, 30], [11, 32, 32, 32], [12, 2, 33, 0], [12, 6, 33, 6, "IDLE_CALLBACK_FRAME_DEADLINE"], [12, 34, 33, 34], [12, 37, 33, 37], [12, 38, 33, 38], [13, 2, 36, 0], [13, 6, 36, 6, "callbacks"], [13, 15, 36, 33], [13, 18, 36, 36], [13, 20, 36, 38], [14, 2, 37, 0], [14, 6, 37, 6, "types"], [14, 11, 37, 32], [14, 14, 37, 35], [14, 16, 37, 37], [15, 2, 38, 0], [15, 6, 38, 6, "timerIDs"], [15, 14, 38, 30], [15, 17, 38, 33], [15, 19, 38, 35], [16, 2, 39, 0], [16, 6, 39, 6, "freeIdxs"], [16, 14, 39, 29], [16, 17, 39, 32], [16, 19, 39, 34], [17, 2, 40, 0], [17, 6, 40, 4, "reactNativeMicrotasks"], [17, 27, 40, 40], [17, 30, 40, 43], [17, 32, 40, 45], [18, 2, 41, 0], [18, 6, 41, 4, "requestIdleCallbacks"], [18, 26, 41, 39], [18, 29, 41, 42], [18, 31, 41, 44], [19, 2, 42, 0], [19, 6, 42, 6, "requestIdleCallbackTimeouts"], [19, 33, 42, 58], [19, 36, 42, 61], [19, 37, 42, 62], [19, 38, 42, 63], [20, 2, 44, 0], [20, 6, 44, 4, "GUID"], [20, 10, 44, 8], [20, 13, 44, 11], [20, 14, 44, 12], [21, 2, 45, 0], [21, 6, 45, 6, "errors"], [21, 12, 45, 26], [21, 15, 45, 29], [21, 17, 45, 31], [22, 2, 47, 0], [22, 6, 47, 4, "hasEmittedTimeDriftWarning"], [22, 32, 47, 30], [22, 35, 47, 33], [22, 40, 47, 38], [23, 2, 50, 0], [23, 11, 50, 9, "_getFreeIndex"], [23, 24, 50, 22, "_getFreeIndex"], [23, 25, 50, 22], [23, 27, 50, 33], [24, 4, 51, 2], [24, 8, 51, 8, "freeIdx"], [24, 15, 51, 15], [24, 18, 51, 18, "freeIdxs"], [24, 26, 51, 26], [24, 27, 51, 27, "pop"], [24, 30, 51, 30], [24, 31, 51, 31], [24, 32, 51, 32], [25, 4, 52, 2], [25, 8, 52, 6, "freeIdx"], [25, 15, 52, 13], [25, 20, 52, 18, "undefined"], [25, 29, 52, 27], [25, 31, 52, 29], [26, 6, 53, 4], [26, 13, 53, 11, "timerIDs"], [26, 21, 53, 19], [26, 22, 53, 20, "length"], [26, 28, 53, 26], [27, 4, 54, 2], [28, 4, 55, 2], [28, 11, 55, 9, "freeIdx"], [28, 18, 55, 16], [29, 2, 56, 0], [30, 2, 58, 0], [30, 11, 58, 9, "_allocateCallback"], [30, 28, 58, 26, "_allocateCallback"], [30, 29, 58, 27, "func"], [30, 33, 58, 41], [30, 35, 58, 43, "type"], [30, 39, 58, 60], [30, 41, 58, 70], [31, 4, 59, 2], [31, 8, 59, 8, "id"], [31, 10, 59, 10], [31, 13, 59, 13, "GUID"], [31, 17, 59, 17], [31, 19, 59, 19], [32, 4, 60, 2], [32, 8, 60, 8, "freeIndex"], [32, 17, 60, 17], [32, 20, 60, 20, "_getFreeIndex"], [32, 33, 60, 33], [32, 34, 60, 34], [32, 35, 60, 35], [33, 4, 61, 2, "timerIDs"], [33, 12, 61, 10], [33, 13, 61, 11, "freeIndex"], [33, 22, 61, 20], [33, 23, 61, 21], [33, 26, 61, 24, "id"], [33, 28, 61, 26], [34, 4, 62, 2, "callbacks"], [34, 13, 62, 11], [34, 14, 62, 12, "freeIndex"], [34, 23, 62, 21], [34, 24, 62, 22], [34, 27, 62, 25, "func"], [34, 31, 62, 29], [35, 4, 63, 2, "types"], [35, 9, 63, 7], [35, 10, 63, 8, "freeIndex"], [35, 19, 63, 17], [35, 20, 63, 18], [35, 23, 63, 21, "type"], [35, 27, 63, 25], [36, 4, 64, 2], [36, 11, 64, 9, "id"], [36, 13, 64, 11], [37, 2, 65, 0], [38, 2, 72, 0], [38, 11, 72, 9, "_callTimer"], [38, 21, 72, 19, "_callTimer"], [38, 22, 72, 20, "timerID"], [38, 29, 72, 35], [38, 31, 72, 37, "frameTime"], [38, 40, 72, 54], [38, 42, 72, 56, "didTimeout"], [38, 52, 72, 76], [38, 54, 72, 78], [39, 4, 73, 2], [39, 8, 73, 6, "timerID"], [39, 15, 73, 13], [39, 18, 73, 16, "GUID"], [39, 22, 73, 20], [39, 24, 73, 22], [40, 6, 74, 4, "console"], [40, 13, 74, 11], [40, 14, 74, 12, "warn"], [40, 18, 74, 16], [40, 19, 75, 6], [40, 77, 75, 64], [40, 79, 76, 6, "timerID"], [40, 86, 77, 4], [40, 87, 77, 5], [41, 4, 78, 2], [42, 4, 85, 2], [42, 8, 85, 8, "timerIndex"], [42, 18, 85, 18], [42, 21, 85, 21, "timerIDs"], [42, 29, 85, 29], [42, 30, 85, 30, "indexOf"], [42, 37, 85, 37], [42, 38, 85, 38, "timerID"], [42, 45, 85, 45], [42, 46, 85, 46], [43, 4, 86, 2], [43, 8, 86, 6, "timerIndex"], [43, 18, 86, 16], [43, 23, 86, 21], [43, 24, 86, 22], [43, 25, 86, 23], [43, 27, 86, 25], [44, 6, 87, 4], [45, 4, 88, 2], [46, 4, 90, 2], [46, 8, 90, 8, "type"], [46, 12, 90, 12], [46, 15, 90, 15, "types"], [46, 20, 90, 20], [46, 21, 90, 21, "timerIndex"], [46, 31, 90, 31], [46, 32, 90, 32], [47, 4, 91, 2], [47, 8, 91, 8, "callback"], [47, 16, 91, 16], [47, 19, 91, 19, "callbacks"], [47, 28, 91, 28], [47, 29, 91, 29, "timerIndex"], [47, 39, 91, 39], [47, 40, 91, 40], [48, 4, 92, 2], [48, 8, 92, 6], [48, 9, 92, 7, "callback"], [48, 17, 92, 15], [48, 21, 92, 19], [48, 22, 92, 20, "type"], [48, 26, 92, 24], [48, 28, 92, 26], [49, 6, 93, 4, "console"], [49, 13, 93, 11], [49, 14, 93, 12, "error"], [49, 19, 93, 17], [49, 20, 93, 18], [49, 52, 93, 50], [49, 55, 93, 53, "timerID"], [49, 62, 93, 60], [49, 63, 93, 61], [50, 6, 94, 4], [51, 4, 95, 2], [52, 4, 97, 2], [52, 8, 97, 6, "__DEV__"], [52, 15, 97, 13], [52, 17, 97, 15], [53, 6, 98, 4, "Systrace"], [53, 14, 98, 12], [53, 15, 98, 13, "beginEvent"], [53, 25, 98, 23], [53, 26, 98, 24, "type"], [53, 30, 98, 28], [53, 33, 98, 31], [53, 44, 98, 42], [53, 45, 98, 43], [54, 4, 99, 2], [55, 4, 102, 2], [55, 8, 102, 6, "type"], [55, 12, 102, 10], [55, 17, 102, 15], [55, 30, 102, 28], [55, 32, 102, 30], [56, 6, 103, 4, "_clearIndex"], [56, 17, 103, 15], [56, 18, 103, 16, "timerIndex"], [56, 28, 103, 26], [56, 29, 103, 27], [57, 4, 104, 2], [58, 4, 106, 2], [58, 8, 106, 6], [59, 6, 107, 4], [59, 10, 108, 6, "type"], [59, 14, 108, 10], [59, 19, 108, 15], [59, 31, 108, 27], [59, 35, 109, 6, "type"], [59, 39, 109, 10], [59, 44, 109, 15], [59, 57, 109, 28], [59, 61, 110, 6, "type"], [59, 65, 110, 10], [59, 70, 110, 15], [59, 97, 110, 42], [59, 99, 111, 6], [60, 8, 112, 6, "callback"], [60, 16, 112, 14], [60, 17, 112, 15], [60, 18, 112, 16], [61, 6, 113, 4], [61, 7, 113, 5], [61, 13, 113, 11], [61, 17, 113, 15, "type"], [61, 21, 113, 19], [61, 26, 113, 24], [61, 49, 113, 47], [61, 51, 113, 49], [62, 8, 114, 6, "callback"], [62, 16, 114, 14], [62, 17, 114, 15, "global"], [62, 23, 114, 21], [62, 24, 114, 22, "performance"], [62, 35, 114, 33], [62, 36, 114, 34, "now"], [62, 39, 114, 37], [62, 40, 114, 38], [62, 41, 114, 39], [62, 42, 114, 40], [63, 6, 115, 4], [63, 7, 115, 5], [63, 13, 115, 11], [63, 17, 115, 15, "type"], [63, 21, 115, 19], [63, 26, 115, 24], [63, 47, 115, 45], [63, 49, 115, 47], [64, 8, 116, 6, "callback"], [64, 16, 116, 14], [64, 17, 116, 15], [65, 10, 117, 8, "timeRemaining"], [65, 23, 117, 21], [65, 25, 117, 23], [65, 34, 117, 23, "timeRemaining"], [65, 35, 117, 23], [65, 37, 117, 35], [66, 12, 121, 10], [66, 19, 121, 17, "Math"], [66, 23, 121, 21], [66, 24, 121, 22, "max"], [66, 27, 121, 25], [66, 28, 122, 12], [66, 29, 122, 13], [66, 31, 123, 12, "FRAME_DURATION"], [66, 45, 123, 26], [66, 49, 123, 30, "global"], [66, 55, 123, 36], [66, 56, 123, 37, "performance"], [66, 67, 123, 48], [66, 68, 123, 49, "now"], [66, 71, 123, 52], [66, 72, 123, 53], [66, 73, 123, 54], [66, 76, 123, 57, "frameTime"], [66, 85, 123, 66], [66, 86, 124, 10], [66, 87, 124, 11], [67, 10, 125, 8], [67, 11, 125, 9], [68, 10, 126, 8, "didTimeout"], [68, 20, 126, 18], [68, 22, 126, 20], [68, 23, 126, 21], [68, 24, 126, 22, "didTimeout"], [69, 8, 127, 6], [69, 9, 127, 7], [69, 10, 127, 8], [70, 6, 128, 4], [70, 7, 128, 5], [70, 13, 128, 11], [71, 8, 129, 6, "console"], [71, 15, 129, 13], [71, 16, 129, 14, "error"], [71, 21, 129, 19], [71, 22, 129, 20], [71, 68, 129, 66], [71, 71, 129, 69, "type"], [71, 75, 129, 73], [71, 76, 129, 74], [72, 6, 130, 4], [73, 4, 131, 2], [73, 5, 131, 3], [73, 6, 131, 4], [73, 13, 131, 11, "e"], [73, 14, 131, 12], [73, 16, 131, 14], [74, 6, 133, 4, "errors"], [74, 12, 133, 10], [74, 13, 133, 11, "push"], [74, 17, 133, 15], [74, 18, 133, 16, "e"], [74, 19, 133, 17], [74, 20, 133, 18], [75, 4, 134, 2], [76, 4, 136, 2], [76, 8, 136, 6, "__DEV__"], [76, 15, 136, 13], [76, 17, 136, 15], [77, 6, 137, 4, "Systrace"], [77, 14, 137, 12], [77, 15, 137, 13, "endEvent"], [77, 23, 137, 21], [77, 24, 137, 22], [77, 25, 137, 23], [78, 4, 138, 2], [79, 2, 139, 0], [80, 2, 145, 0], [80, 11, 145, 9, "_callReactNativeMicrotasksPass"], [80, 41, 145, 39, "_callReactNativeMicrotasksPass"], [80, 42, 145, 39], [80, 44, 145, 42], [81, 4, 146, 2], [81, 8, 146, 6, "reactNativeMicrotasks"], [81, 29, 146, 27], [81, 30, 146, 28, "length"], [81, 36, 146, 34], [81, 41, 146, 39], [81, 42, 146, 40], [81, 44, 146, 42], [82, 6, 147, 4], [82, 13, 147, 11], [82, 18, 147, 16], [83, 4, 148, 2], [84, 4, 150, 2], [84, 8, 150, 6, "__DEV__"], [84, 15, 150, 13], [84, 17, 150, 15], [85, 6, 151, 4, "Systrace"], [85, 14, 151, 12], [85, 15, 151, 13, "beginEvent"], [85, 25, 151, 23], [85, 26, 151, 24], [85, 59, 151, 57], [85, 60, 151, 58], [86, 4, 152, 2], [87, 4, 156, 2], [87, 8, 156, 8, "passReactNativeMicrotasks"], [87, 33, 156, 33], [87, 36, 156, 36, "reactNativeMicrotasks"], [87, 57, 156, 57], [88, 4, 157, 2, "reactNativeMicrotasks"], [88, 25, 157, 23], [88, 28, 157, 26], [88, 30, 157, 28], [89, 4, 161, 2], [89, 9, 161, 7], [89, 13, 161, 11, "i"], [89, 14, 161, 12], [89, 17, 161, 15], [89, 18, 161, 16], [89, 20, 161, 18, "i"], [89, 21, 161, 19], [89, 24, 161, 22, "passReactNativeMicrotasks"], [89, 49, 161, 47], [89, 50, 161, 48, "length"], [89, 56, 161, 54], [89, 58, 161, 56], [89, 60, 161, 58, "i"], [89, 61, 161, 59], [89, 63, 161, 61], [90, 6, 162, 4, "_callTimer"], [90, 16, 162, 14], [90, 17, 162, 15, "passReactNativeMicrotasks"], [90, 42, 162, 40], [90, 43, 162, 41, "i"], [90, 44, 162, 42], [90, 45, 162, 43], [90, 47, 162, 45], [90, 48, 162, 46], [90, 49, 162, 47], [91, 4, 163, 2], [92, 4, 165, 2], [92, 8, 165, 6, "__DEV__"], [92, 15, 165, 13], [92, 17, 165, 15], [93, 6, 166, 4, "Systrace"], [93, 14, 166, 12], [93, 15, 166, 13, "endEvent"], [93, 23, 166, 21], [93, 24, 166, 22], [93, 25, 166, 23], [94, 4, 167, 2], [95, 4, 168, 2], [95, 11, 168, 9, "reactNativeMicrotasks"], [95, 32, 168, 30], [95, 33, 168, 31, "length"], [95, 39, 168, 37], [95, 42, 168, 40], [95, 43, 168, 41], [96, 2, 169, 0], [97, 2, 171, 0], [97, 11, 171, 9, "_clearIndex"], [97, 22, 171, 20, "_clearIndex"], [97, 23, 171, 21, "i"], [97, 24, 171, 30], [97, 26, 171, 32], [98, 4, 172, 2, "timerIDs"], [98, 12, 172, 10], [98, 13, 172, 11, "i"], [98, 14, 172, 12], [98, 15, 172, 13], [98, 18, 172, 16], [98, 22, 172, 20], [99, 4, 173, 2, "callbacks"], [99, 13, 173, 11], [99, 14, 173, 12, "i"], [99, 15, 173, 13], [99, 16, 173, 14], [99, 19, 173, 17], [99, 23, 173, 21], [100, 4, 174, 2, "types"], [100, 9, 174, 7], [100, 10, 174, 8, "i"], [100, 11, 174, 9], [100, 12, 174, 10], [100, 15, 174, 13], [100, 19, 174, 17], [101, 4, 175, 2, "freeIdxs"], [101, 12, 175, 10], [101, 13, 175, 11, "push"], [101, 17, 175, 15], [101, 18, 175, 16, "i"], [101, 19, 175, 17], [101, 20, 175, 18], [102, 2, 176, 0], [103, 2, 178, 0], [103, 11, 178, 9, "_freeCallback"], [103, 24, 178, 22, "_freeCallback"], [103, 25, 178, 23, "timerID"], [103, 32, 178, 38], [103, 34, 178, 40], [104, 4, 181, 2], [104, 8, 181, 6, "timerID"], [104, 15, 181, 13], [104, 19, 181, 17], [104, 23, 181, 21], [104, 25, 181, 23], [105, 6, 182, 4], [106, 4, 183, 2], [107, 4, 185, 2], [107, 8, 185, 8, "index"], [107, 13, 185, 13], [107, 16, 185, 16, "timerIDs"], [107, 24, 185, 24], [107, 25, 185, 25, "indexOf"], [107, 32, 185, 32], [107, 33, 185, 33, "timerID"], [107, 40, 185, 40], [107, 41, 185, 41], [108, 4, 187, 2], [108, 8, 187, 6, "index"], [108, 13, 187, 11], [108, 18, 187, 16], [108, 19, 187, 17], [108, 20, 187, 18], [108, 22, 187, 20], [109, 6, 188, 4], [109, 10, 188, 10, "type"], [109, 14, 188, 14], [109, 17, 188, 17, "types"], [109, 22, 188, 22], [109, 23, 188, 23, "index"], [109, 28, 188, 28], [109, 29, 188, 29], [110, 6, 189, 4, "_clearIndex"], [110, 17, 189, 15], [110, 18, 189, 16, "index"], [110, 23, 189, 21], [110, 24, 189, 22], [111, 6, 190, 4], [111, 10, 191, 6, "type"], [111, 14, 191, 10], [111, 19, 191, 15], [111, 46, 191, 42], [111, 50, 192, 6, "type"], [111, 54, 192, 10], [111, 59, 192, 15], [111, 80, 192, 36], [111, 82, 193, 6], [112, 8, 194, 6, "deleteTimer"], [112, 19, 194, 17], [112, 20, 194, 18, "timerID"], [112, 27, 194, 25], [112, 28, 194, 26], [113, 6, 195, 4], [114, 4, 196, 2], [115, 2, 197, 0], [116, 2, 204, 0], [116, 6, 204, 6, "JSTimers"], [116, 14, 204, 14], [116, 17, 204, 17], [117, 4, 209, 2, "setTimeout"], [117, 14, 209, 12], [117, 16, 209, 14], [117, 25, 209, 14, "setTimeout"], [117, 26, 210, 4, "func"], [117, 30, 210, 18], [117, 32, 211, 4, "duration"], [117, 40, 211, 20], [117, 42, 213, 12], [118, 6, 213, 12], [118, 15, 213, 12, "_len"], [118, 19, 213, 12], [118, 22, 213, 12, "arguments"], [118, 31, 213, 12], [118, 32, 213, 12, "length"], [118, 38, 213, 12], [118, 40, 212, 7, "args"], [118, 44, 212, 11], [118, 51, 212, 11, "Array"], [118, 56, 212, 11], [118, 57, 212, 11, "_len"], [118, 61, 212, 11], [118, 68, 212, 11, "_len"], [118, 72, 212, 11], [118, 83, 212, 11, "_key"], [118, 87, 212, 11], [118, 93, 212, 11, "_key"], [118, 97, 212, 11], [118, 100, 212, 11, "_len"], [118, 104, 212, 11], [118, 106, 212, 11, "_key"], [118, 110, 212, 11], [119, 8, 212, 7, "args"], [119, 12, 212, 11], [119, 13, 212, 11, "_key"], [119, 17, 212, 11], [119, 25, 212, 11, "arguments"], [119, 34, 212, 11], [119, 35, 212, 11, "_key"], [119, 39, 212, 11], [120, 6, 212, 11], [121, 6, 214, 4], [121, 10, 214, 10, "id"], [121, 12, 214, 12], [121, 15, 214, 15, "_allocateCallback"], [121, 32, 214, 32], [121, 33, 215, 6], [121, 39, 215, 12, "func"], [121, 43, 215, 16], [121, 44, 215, 17, "apply"], [121, 49, 215, 22], [121, 50, 215, 23, "undefined"], [121, 59, 215, 32], [121, 61, 215, 34, "args"], [121, 65, 215, 38], [121, 66, 215, 39], [121, 68, 216, 6], [121, 80, 217, 4], [121, 81, 217, 5], [122, 6, 218, 4, "createTimer"], [122, 17, 218, 15], [122, 18, 218, 16, "id"], [122, 20, 218, 18], [122, 22, 218, 20, "duration"], [122, 30, 218, 28], [122, 34, 218, 32], [122, 35, 218, 33], [122, 37, 218, 35, "Date"], [122, 41, 218, 39], [122, 42, 218, 40, "now"], [122, 45, 218, 43], [122, 46, 218, 44], [122, 47, 218, 45], [122, 49, 218, 63], [122, 54, 218, 68], [122, 55, 218, 69], [123, 6, 219, 4], [123, 13, 219, 11, "id"], [123, 15, 219, 13], [124, 4, 220, 2], [124, 5, 220, 3], [125, 4, 226, 2, "setInterval"], [125, 15, 226, 13], [125, 17, 226, 15], [125, 26, 226, 15, "setInterval"], [125, 27, 227, 4, "func"], [125, 31, 227, 18], [125, 33, 228, 4, "duration"], [125, 41, 228, 20], [125, 43, 230, 12], [126, 6, 230, 12], [126, 15, 230, 12, "_len2"], [126, 20, 230, 12], [126, 23, 230, 12, "arguments"], [126, 32, 230, 12], [126, 33, 230, 12, "length"], [126, 39, 230, 12], [126, 41, 229, 7, "args"], [126, 45, 229, 11], [126, 52, 229, 11, "Array"], [126, 57, 229, 11], [126, 58, 229, 11, "_len2"], [126, 63, 229, 11], [126, 70, 229, 11, "_len2"], [126, 75, 229, 11], [126, 86, 229, 11, "_key2"], [126, 91, 229, 11], [126, 97, 229, 11, "_key2"], [126, 102, 229, 11], [126, 105, 229, 11, "_len2"], [126, 110, 229, 11], [126, 112, 229, 11, "_key2"], [126, 117, 229, 11], [127, 8, 229, 7, "args"], [127, 12, 229, 11], [127, 13, 229, 11, "_key2"], [127, 18, 229, 11], [127, 26, 229, 11, "arguments"], [127, 35, 229, 11], [127, 36, 229, 11, "_key2"], [127, 41, 229, 11], [128, 6, 229, 11], [129, 6, 231, 4], [129, 10, 231, 10, "id"], [129, 12, 231, 12], [129, 15, 231, 15, "_allocateCallback"], [129, 32, 231, 32], [129, 33, 232, 6], [129, 39, 232, 12, "func"], [129, 43, 232, 16], [129, 44, 232, 17, "apply"], [129, 49, 232, 22], [129, 50, 232, 23, "undefined"], [129, 59, 232, 32], [129, 61, 232, 34, "args"], [129, 65, 232, 38], [129, 66, 232, 39], [129, 68, 233, 6], [129, 81, 234, 4], [129, 82, 234, 5], [130, 6, 235, 4, "createTimer"], [130, 17, 235, 15], [130, 18, 235, 16, "id"], [130, 20, 235, 18], [130, 22, 235, 20, "duration"], [130, 30, 235, 28], [130, 34, 235, 32], [130, 35, 235, 33], [130, 37, 235, 35, "Date"], [130, 41, 235, 39], [130, 42, 235, 40, "now"], [130, 45, 235, 43], [130, 46, 235, 44], [130, 47, 235, 45], [130, 49, 235, 63], [130, 53, 235, 67], [130, 54, 235, 68], [131, 6, 236, 4], [131, 13, 236, 11, "id"], [131, 15, 236, 13], [132, 4, 237, 2], [132, 5, 237, 3], [133, 4, 247, 2, "queueReactNativeMicrotask"], [133, 29, 247, 27], [133, 31, 247, 29], [133, 40, 247, 29, "queueReactNativeMicrotask"], [133, 41, 247, 39, "func"], [133, 45, 247, 53], [133, 47, 247, 77], [134, 6, 247, 77], [134, 15, 247, 77, "_len3"], [134, 20, 247, 77], [134, 23, 247, 77, "arguments"], [134, 32, 247, 77], [134, 33, 247, 77, "length"], [134, 39, 247, 77], [134, 41, 247, 58, "args"], [134, 45, 247, 62], [134, 52, 247, 62, "Array"], [134, 57, 247, 62], [134, 58, 247, 62, "_len3"], [134, 63, 247, 62], [134, 70, 247, 62, "_len3"], [134, 75, 247, 62], [134, 86, 247, 62, "_key3"], [134, 91, 247, 62], [134, 97, 247, 62, "_key3"], [134, 102, 247, 62], [134, 105, 247, 62, "_len3"], [134, 110, 247, 62], [134, 112, 247, 62, "_key3"], [134, 117, 247, 62], [135, 8, 247, 58, "args"], [135, 12, 247, 62], [135, 13, 247, 62, "_key3"], [135, 18, 247, 62], [135, 26, 247, 62, "arguments"], [135, 35, 247, 62], [135, 36, 247, 62, "_key3"], [135, 41, 247, 62], [136, 6, 247, 62], [137, 6, 248, 4], [137, 10, 248, 10, "id"], [137, 12, 248, 12], [137, 15, 248, 15, "_allocateCallback"], [137, 32, 248, 32], [137, 33, 249, 6], [137, 39, 249, 12, "func"], [137, 43, 249, 16], [137, 44, 249, 17, "apply"], [137, 49, 249, 22], [137, 50, 249, 23, "undefined"], [137, 59, 249, 32], [137, 61, 249, 34, "args"], [137, 65, 249, 38], [137, 66, 249, 39], [137, 68, 250, 6], [137, 95, 251, 4], [137, 96, 251, 5], [138, 6, 252, 4, "reactNativeMicrotasks"], [138, 27, 252, 25], [138, 28, 252, 26, "push"], [138, 32, 252, 30], [138, 33, 252, 31, "id"], [138, 35, 252, 33], [138, 36, 252, 34], [139, 6, 253, 4], [139, 13, 253, 11, "id"], [139, 15, 253, 13], [140, 4, 254, 2], [140, 5, 254, 3], [141, 4, 259, 2, "requestAnimationFrame"], [141, 25, 259, 23], [141, 27, 259, 25], [141, 36, 259, 25, "requestAnimationFrame"], [141, 37, 259, 35, "func"], [141, 41, 259, 49], [141, 43, 259, 65], [142, 6, 260, 4], [142, 10, 260, 10, "id"], [142, 12, 260, 12], [142, 15, 260, 15, "_allocateCallback"], [142, 32, 260, 32], [142, 33, 260, 33, "func"], [142, 37, 260, 37], [142, 39, 260, 39], [142, 62, 260, 62], [142, 63, 260, 63], [143, 6, 261, 4, "createTimer"], [143, 17, 261, 15], [143, 18, 261, 16, "id"], [143, 20, 261, 18], [143, 22, 261, 20], [143, 23, 261, 21], [143, 25, 261, 23, "Date"], [143, 29, 261, 27], [143, 30, 261, 28, "now"], [143, 33, 261, 31], [143, 34, 261, 32], [143, 35, 261, 33], [143, 37, 261, 51], [143, 42, 261, 56], [143, 43, 261, 57], [144, 6, 262, 4], [144, 13, 262, 11, "id"], [144, 15, 262, 13], [145, 4, 263, 2], [145, 5, 263, 3], [146, 4, 270, 2, "requestIdleCallback"], [146, 23, 270, 21], [146, 25, 270, 23], [146, 34, 270, 23, "requestIdleCallback"], [146, 35, 271, 4, "func"], [146, 39, 271, 18], [146, 41, 272, 4, "options"], [146, 48, 272, 20], [146, 50, 273, 18], [147, 6, 274, 4], [147, 10, 274, 8, "requestIdleCallbacks"], [147, 30, 274, 28], [147, 31, 274, 29, "length"], [147, 37, 274, 35], [147, 42, 274, 40], [147, 43, 274, 41], [147, 45, 274, 43], [148, 8, 275, 6, "setSendIdleEvents"], [148, 25, 275, 23], [148, 26, 275, 24], [148, 30, 275, 28], [148, 31, 275, 29], [149, 6, 276, 4], [150, 6, 278, 4], [150, 10, 278, 10, "timeout"], [150, 17, 278, 17], [150, 20, 278, 20, "options"], [150, 27, 278, 27], [150, 31, 278, 31, "options"], [150, 38, 278, 38], [150, 39, 278, 39, "timeout"], [150, 46, 278, 46], [151, 6, 279, 4], [151, 10, 279, 10, "id"], [151, 12, 279, 20], [151, 15, 279, 23, "_allocateCallback"], [151, 32, 279, 40], [151, 33, 280, 6, "timeout"], [151, 40, 280, 13], [151, 44, 280, 17], [151, 48, 280, 21], [151, 51, 281, 11, "deadline"], [151, 59, 281, 24], [151, 63, 281, 29], [152, 8, 282, 12], [152, 12, 282, 18, "timeoutId"], [152, 21, 282, 35], [152, 24, 282, 38, "requestIdleCallbackTimeouts"], [152, 51, 282, 65], [152, 52, 282, 66, "id"], [152, 54, 282, 68], [152, 55, 282, 69], [153, 8, 283, 12], [153, 12, 283, 16, "timeoutId"], [153, 21, 283, 25], [153, 23, 283, 27], [154, 10, 284, 14, "JSTimers"], [154, 18, 284, 22], [154, 19, 284, 23, "clearTimeout"], [154, 31, 284, 35], [154, 32, 284, 36, "timeoutId"], [154, 41, 284, 45], [154, 42, 284, 46], [155, 10, 285, 14], [155, 17, 285, 21, "requestIdleCallbackTimeouts"], [155, 44, 285, 48], [155, 45, 285, 49, "id"], [155, 47, 285, 51], [155, 48, 285, 52], [156, 8, 286, 12], [157, 8, 287, 12], [157, 15, 287, 19, "func"], [157, 19, 287, 23], [157, 20, 287, 24, "deadline"], [157, 28, 287, 32], [157, 29, 287, 33], [158, 6, 288, 10], [158, 7, 288, 11], [158, 10, 289, 10, "func"], [158, 14, 289, 14], [158, 16, 290, 6], [158, 37, 291, 4], [158, 38, 291, 5], [159, 6, 292, 4, "requestIdleCallbacks"], [159, 26, 292, 24], [159, 27, 292, 25, "push"], [159, 31, 292, 29], [159, 32, 292, 30, "id"], [159, 34, 292, 32], [159, 35, 292, 33], [160, 6, 294, 4], [160, 10, 294, 8, "timeout"], [160, 17, 294, 15], [160, 21, 294, 19], [160, 25, 294, 23], [160, 27, 294, 25], [161, 8, 295, 6], [161, 12, 295, 12, "timeoutId"], [161, 21, 295, 29], [161, 24, 295, 32, "JSTimers"], [161, 32, 295, 40], [161, 33, 295, 41, "setTimeout"], [161, 43, 295, 51], [161, 44, 295, 52], [161, 50, 295, 58], [162, 10, 296, 8], [162, 14, 296, 14, "index"], [162, 19, 296, 27], [162, 22, 296, 30, "requestIdleCallbacks"], [162, 42, 296, 50], [162, 43, 296, 51, "indexOf"], [162, 50, 296, 58], [162, 51, 296, 59, "id"], [162, 53, 296, 61], [162, 54, 296, 62], [163, 10, 297, 8], [163, 14, 297, 12, "index"], [163, 19, 297, 17], [163, 22, 297, 20], [163, 23, 297, 21], [163, 24, 297, 22], [163, 26, 297, 24], [164, 12, 298, 10, "requestIdleCallbacks"], [164, 32, 298, 30], [164, 33, 298, 31, "splice"], [164, 39, 298, 37], [164, 40, 298, 38, "index"], [164, 45, 298, 43], [164, 47, 298, 45], [164, 48, 298, 46], [164, 49, 298, 47], [165, 12, 299, 10, "_callTimer"], [165, 22, 299, 20], [165, 23, 299, 21, "id"], [165, 25, 299, 23], [165, 27, 299, 25, "global"], [165, 33, 299, 31], [165, 34, 299, 32, "performance"], [165, 45, 299, 43], [165, 46, 299, 44, "now"], [165, 49, 299, 47], [165, 50, 299, 48], [165, 51, 299, 49], [165, 53, 299, 51], [165, 57, 299, 55], [165, 58, 299, 56], [166, 10, 300, 8], [167, 10, 301, 8], [167, 17, 301, 15, "requestIdleCallbackTimeouts"], [167, 44, 301, 42], [167, 45, 301, 43, "id"], [167, 47, 301, 45], [167, 48, 301, 46], [168, 10, 302, 8], [168, 14, 302, 12, "requestIdleCallbacks"], [168, 34, 302, 32], [168, 35, 302, 33, "length"], [168, 41, 302, 39], [168, 46, 302, 44], [168, 47, 302, 45], [168, 49, 302, 47], [169, 12, 303, 10, "setSendIdleEvents"], [169, 29, 303, 27], [169, 30, 303, 28], [169, 35, 303, 33], [169, 36, 303, 34], [170, 10, 304, 8], [171, 8, 305, 6], [171, 9, 305, 7], [171, 11, 305, 9, "timeout"], [171, 18, 305, 16], [171, 19, 305, 17], [172, 8, 306, 6, "requestIdleCallbackTimeouts"], [172, 35, 306, 33], [172, 36, 306, 34, "id"], [172, 38, 306, 36], [172, 39, 306, 37], [172, 42, 306, 40, "timeoutId"], [172, 51, 306, 49], [173, 6, 307, 4], [174, 6, 308, 4], [174, 13, 308, 11, "id"], [174, 15, 308, 13], [175, 4, 309, 2], [175, 5, 309, 3], [176, 4, 311, 2, "cancelIdleCallback"], [176, 22, 311, 20], [176, 24, 311, 22], [176, 33, 311, 22, "cancelIdleCallback"], [176, 34, 311, 32, "timerID"], [176, 41, 311, 47], [176, 43, 311, 49], [177, 6, 312, 4, "_freeCallback"], [177, 19, 312, 17], [177, 20, 312, 18, "timerID"], [177, 27, 312, 25], [177, 28, 312, 26], [178, 6, 313, 4], [178, 10, 313, 10, "index"], [178, 15, 313, 15], [178, 18, 313, 18, "requestIdleCallbacks"], [178, 38, 313, 38], [178, 39, 313, 39, "indexOf"], [178, 46, 313, 46], [178, 47, 313, 47, "timerID"], [178, 54, 313, 54], [178, 55, 313, 55], [179, 6, 314, 4], [179, 10, 314, 8, "index"], [179, 15, 314, 13], [179, 20, 314, 18], [179, 21, 314, 19], [179, 22, 314, 20], [179, 24, 314, 22], [180, 8, 315, 6, "requestIdleCallbacks"], [180, 28, 315, 26], [180, 29, 315, 27, "splice"], [180, 35, 315, 33], [180, 36, 315, 34, "index"], [180, 41, 315, 39], [180, 43, 315, 41], [180, 44, 315, 42], [180, 45, 315, 43], [181, 6, 316, 4], [182, 6, 318, 4], [182, 10, 318, 10, "timeoutId"], [182, 19, 318, 19], [182, 22, 318, 22, "requestIdleCallbackTimeouts"], [182, 49, 318, 49], [182, 50, 318, 50, "timerID"], [182, 57, 318, 57], [182, 58, 318, 58], [183, 6, 319, 4], [183, 10, 319, 8, "timeoutId"], [183, 19, 319, 17], [183, 21, 319, 19], [184, 8, 320, 6, "JSTimers"], [184, 16, 320, 14], [184, 17, 320, 15, "clearTimeout"], [184, 29, 320, 27], [184, 30, 320, 28, "timeoutId"], [184, 39, 320, 37], [184, 40, 320, 38], [185, 8, 321, 6], [185, 15, 321, 13, "requestIdleCallbackTimeouts"], [185, 42, 321, 40], [185, 43, 321, 41, "timerID"], [185, 50, 321, 48], [185, 51, 321, 49], [186, 6, 322, 4], [187, 6, 324, 4], [187, 10, 324, 8, "requestIdleCallbacks"], [187, 30, 324, 28], [187, 31, 324, 29, "length"], [187, 37, 324, 35], [187, 42, 324, 40], [187, 43, 324, 41], [187, 45, 324, 43], [188, 8, 325, 6, "setSendIdleEvents"], [188, 25, 325, 23], [188, 26, 325, 24], [188, 31, 325, 29], [188, 32, 325, 30], [189, 6, 326, 4], [190, 4, 327, 2], [190, 5, 327, 3], [191, 4, 329, 2, "clearTimeout"], [191, 16, 329, 14], [191, 18, 329, 16], [191, 27, 329, 16, "clearTimeout"], [191, 28, 329, 26, "timerID"], [191, 35, 329, 41], [191, 37, 329, 43], [192, 6, 330, 4, "_freeCallback"], [192, 19, 330, 17], [192, 20, 330, 18, "timerID"], [192, 27, 330, 25], [192, 28, 330, 26], [193, 4, 331, 2], [193, 5, 331, 3], [194, 4, 333, 2, "clearInterval"], [194, 17, 333, 15], [194, 19, 333, 17], [194, 28, 333, 17, "clearInterval"], [194, 29, 333, 27, "timerID"], [194, 36, 333, 42], [194, 38, 333, 44], [195, 6, 334, 4, "_freeCallback"], [195, 19, 334, 17], [195, 20, 334, 18, "timerID"], [195, 27, 334, 25], [195, 28, 334, 26], [196, 4, 335, 2], [196, 5, 335, 3], [197, 4, 337, 2, "clearReactNativeMicrotask"], [197, 29, 337, 27], [197, 31, 337, 29], [197, 40, 337, 29, "clearReactNativeMicrotask"], [197, 41, 337, 39, "timerID"], [197, 48, 337, 54], [197, 50, 337, 56], [198, 6, 338, 4, "_freeCallback"], [198, 19, 338, 17], [198, 20, 338, 18, "timerID"], [198, 27, 338, 25], [198, 28, 338, 26], [199, 6, 339, 4], [199, 10, 339, 10, "index"], [199, 15, 339, 15], [199, 18, 339, 18, "reactNativeMicrotasks"], [199, 39, 339, 39], [199, 40, 339, 40, "indexOf"], [199, 47, 339, 47], [199, 48, 339, 48, "timerID"], [199, 55, 339, 55], [199, 56, 339, 56], [200, 6, 340, 4], [200, 10, 340, 8, "index"], [200, 15, 340, 13], [200, 20, 340, 18], [200, 21, 340, 19], [200, 22, 340, 20], [200, 24, 340, 22], [201, 8, 341, 6, "reactNativeMicrotasks"], [201, 29, 341, 27], [201, 30, 341, 28, "splice"], [201, 36, 341, 34], [201, 37, 341, 35, "index"], [201, 42, 341, 40], [201, 44, 341, 42], [201, 45, 341, 43], [201, 46, 341, 44], [202, 6, 342, 4], [203, 4, 343, 2], [203, 5, 343, 3], [204, 4, 345, 2, "cancelAnimationFrame"], [204, 24, 345, 22], [204, 26, 345, 24], [204, 35, 345, 24, "cancelAnimationFrame"], [204, 36, 345, 34, "timerID"], [204, 43, 345, 49], [204, 45, 345, 51], [205, 6, 346, 4, "_freeCallback"], [205, 19, 346, 17], [205, 20, 346, 18, "timerID"], [205, 27, 346, 25], [205, 28, 346, 26], [206, 4, 347, 2], [206, 5, 347, 3], [207, 4, 353, 2, "callTimers"], [207, 14, 353, 12], [207, 16, 353, 14], [207, 25, 353, 14, "callTimers"], [207, 26, 353, 24, "timersToCall"], [207, 38, 353, 51], [207, 40, 353, 65], [208, 6, 354, 4, "invariant"], [208, 15, 354, 13], [208, 16, 355, 6, "timersToCall"], [208, 28, 355, 18], [208, 29, 355, 19, "length"], [208, 35, 355, 25], [208, 40, 355, 30], [208, 41, 355, 31], [208, 43, 356, 6], [208, 96, 357, 4], [208, 97, 357, 5], [209, 6, 359, 4, "errors"], [209, 12, 359, 10], [209, 13, 359, 11, "length"], [209, 19, 359, 17], [209, 22, 359, 20], [209, 23, 359, 21], [210, 6, 360, 4], [210, 11, 360, 9], [210, 15, 360, 13, "i"], [210, 16, 360, 14], [210, 19, 360, 17], [210, 20, 360, 18], [210, 22, 360, 20, "i"], [210, 23, 360, 21], [210, 26, 360, 24, "timersToCall"], [210, 38, 360, 36], [210, 39, 360, 37, "length"], [210, 45, 360, 43], [210, 47, 360, 45, "i"], [210, 48, 360, 46], [210, 50, 360, 48], [210, 52, 360, 50], [211, 8, 361, 6, "_callTimer"], [211, 18, 361, 16], [211, 19, 361, 17, "timersToCall"], [211, 31, 361, 29], [211, 32, 361, 30, "i"], [211, 33, 361, 31], [211, 34, 361, 32], [211, 36, 361, 34], [211, 37, 361, 35], [211, 38, 361, 36], [212, 6, 362, 4], [213, 6, 364, 4], [213, 10, 364, 10, "errorCount"], [213, 20, 364, 20], [213, 23, 364, 23, "errors"], [213, 29, 364, 29], [213, 30, 364, 30, "length"], [213, 36, 364, 36], [214, 6, 365, 4], [214, 10, 365, 8, "errorCount"], [214, 20, 365, 18], [214, 23, 365, 21], [214, 24, 365, 22], [214, 26, 365, 24], [215, 8, 366, 6], [215, 12, 366, 10, "errorCount"], [215, 22, 366, 20], [215, 25, 366, 23], [215, 26, 366, 24], [215, 28, 366, 26], [216, 10, 369, 8], [216, 15, 369, 13], [216, 19, 369, 17, "ii"], [216, 21, 369, 19], [216, 24, 369, 22], [216, 25, 369, 23], [216, 27, 369, 25, "ii"], [216, 29, 369, 27], [216, 32, 369, 30, "errorCount"], [216, 42, 369, 40], [216, 44, 369, 42, "ii"], [216, 46, 369, 44], [216, 48, 369, 46], [216, 50, 369, 48], [217, 12, 370, 10, "JSTimers"], [217, 20, 370, 18], [217, 21, 370, 19, "setTimeout"], [217, 31, 370, 29], [217, 32, 371, 12], [217, 33, 371, 14, "error"], [217, 38, 371, 26], [217, 42, 371, 31], [218, 14, 372, 14], [218, 20, 372, 20, "error"], [218, 25, 372, 25], [219, 12, 373, 12], [219, 13, 373, 13], [219, 15, 373, 15, "bind"], [219, 19, 373, 19], [219, 20, 373, 20], [219, 24, 373, 24], [219, 26, 373, 26, "errors"], [219, 32, 373, 32], [219, 33, 373, 33, "ii"], [219, 35, 373, 35], [219, 36, 373, 36], [219, 37, 373, 37], [219, 39, 374, 12], [219, 40, 375, 10], [219, 41, 375, 11], [220, 10, 376, 8], [221, 8, 377, 6], [222, 8, 378, 6], [222, 14, 378, 12, "errors"], [222, 20, 378, 18], [222, 21, 378, 19], [222, 22, 378, 20], [222, 23, 378, 21], [223, 6, 379, 4], [224, 4, 380, 2], [224, 5, 380, 3], [225, 4, 382, 2, "callIdleCallbacks"], [225, 21, 382, 19], [225, 23, 382, 21], [225, 32, 382, 21, "callIdleCallbacks"], [225, 33, 382, 31, "frameTime"], [225, 42, 382, 48], [225, 44, 382, 50], [226, 6, 383, 4], [226, 10, 384, 6, "FRAME_DURATION"], [226, 24, 384, 20], [226, 28, 384, 24, "Date"], [226, 32, 384, 28], [226, 33, 384, 29, "now"], [226, 36, 384, 32], [226, 37, 384, 33], [226, 38, 384, 34], [226, 41, 384, 37, "frameTime"], [226, 50, 384, 46], [226, 51, 384, 47], [226, 54, 385, 6, "IDLE_CALLBACK_FRAME_DEADLINE"], [226, 82, 385, 34], [226, 84, 386, 6], [227, 8, 387, 6], [228, 6, 388, 4], [229, 6, 390, 4, "errors"], [229, 12, 390, 10], [229, 13, 390, 11, "length"], [229, 19, 390, 17], [229, 22, 390, 20], [229, 23, 390, 21], [230, 6, 391, 4], [230, 10, 391, 8, "requestIdleCallbacks"], [230, 30, 391, 28], [230, 31, 391, 29, "length"], [230, 37, 391, 35], [230, 40, 391, 38], [230, 41, 391, 39], [230, 43, 391, 41], [231, 8, 392, 6], [231, 12, 392, 12, "passIdleCallbacks"], [231, 29, 392, 29], [231, 32, 392, 32, "requestIdleCallbacks"], [231, 52, 392, 52], [232, 8, 393, 6, "requestIdleCallbacks"], [232, 28, 393, 26], [232, 31, 393, 29], [232, 33, 393, 31], [233, 8, 395, 6], [233, 13, 395, 11], [233, 17, 395, 15, "i"], [233, 18, 395, 16], [233, 21, 395, 19], [233, 22, 395, 20], [233, 24, 395, 22, "i"], [233, 25, 395, 23], [233, 28, 395, 26, "passIdleCallbacks"], [233, 45, 395, 43], [233, 46, 395, 44, "length"], [233, 52, 395, 50], [233, 54, 395, 52], [233, 56, 395, 54, "i"], [233, 57, 395, 55], [233, 59, 395, 57], [234, 10, 396, 8, "_callTimer"], [234, 20, 396, 18], [234, 21, 396, 19, "passIdleCallbacks"], [234, 38, 396, 36], [234, 39, 396, 37, "i"], [234, 40, 396, 38], [234, 41, 396, 39], [234, 43, 396, 41, "frameTime"], [234, 52, 396, 50], [234, 53, 396, 51], [235, 8, 397, 6], [236, 6, 398, 4], [237, 6, 400, 4], [237, 10, 400, 8, "requestIdleCallbacks"], [237, 30, 400, 28], [237, 31, 400, 29, "length"], [237, 37, 400, 35], [237, 42, 400, 40], [237, 43, 400, 41], [237, 45, 400, 43], [238, 8, 401, 6, "setSendIdleEvents"], [238, 25, 401, 23], [238, 26, 401, 24], [238, 31, 401, 29], [238, 32, 401, 30], [239, 6, 402, 4], [240, 6, 404, 4, "errors"], [240, 12, 404, 10], [240, 13, 404, 11, "for<PERSON>ach"], [240, 20, 404, 18], [240, 21, 404, 19, "error"], [240, 26, 404, 24], [240, 30, 405, 6, "JSTimers"], [240, 38, 405, 14], [240, 39, 405, 15, "setTimeout"], [240, 49, 405, 25], [240, 50, 405, 26], [240, 56, 405, 32], [241, 8, 406, 8], [241, 14, 406, 14, "error"], [241, 19, 406, 19], [242, 6, 407, 6], [242, 7, 407, 7], [242, 9, 407, 9], [242, 10, 407, 10], [242, 11, 408, 4], [242, 12, 408, 5], [243, 4, 409, 2], [243, 5, 409, 3], [244, 4, 415, 2, "callReactNativeMicrotasks"], [244, 29, 415, 27, "callReactNativeMicrotasks"], [244, 30, 415, 27], [244, 32, 415, 30], [245, 6, 416, 4, "errors"], [245, 12, 416, 10], [245, 13, 416, 11, "length"], [245, 19, 416, 17], [245, 22, 416, 20], [245, 23, 416, 21], [246, 6, 417, 4], [246, 13, 417, 11, "_callReactNativeMicrotasksPass"], [246, 43, 417, 41], [246, 44, 417, 42], [246, 45, 417, 43], [246, 47, 417, 45], [246, 48, 417, 46], [247, 6, 418, 4, "errors"], [247, 12, 418, 10], [247, 13, 418, 11, "for<PERSON>ach"], [247, 20, 418, 18], [247, 21, 418, 19, "error"], [247, 26, 418, 24], [247, 30, 419, 6, "JSTimers"], [247, 38, 419, 14], [247, 39, 419, 15, "setTimeout"], [247, 49, 419, 25], [247, 50, 419, 26], [247, 56, 419, 32], [248, 8, 420, 8], [248, 14, 420, 14, "error"], [248, 19, 420, 19], [249, 6, 421, 6], [249, 7, 421, 7], [249, 9, 421, 9], [249, 10, 421, 10], [249, 11, 422, 4], [249, 12, 422, 5], [250, 4, 423, 2], [250, 5, 423, 3], [251, 4, 428, 2, "emitTimeDriftWarning"], [251, 24, 428, 22, "emitTimeDriftWarning"], [251, 25, 428, 23, "warningMessage"], [251, 39, 428, 45], [251, 41, 428, 47], [252, 6, 429, 4], [252, 10, 429, 8, "hasEmittedTimeDriftWarning"], [252, 36, 429, 34], [252, 38, 429, 36], [253, 8, 430, 6], [254, 6, 431, 4], [255, 6, 432, 4, "hasEmittedTimeDriftWarning"], [255, 32, 432, 30], [255, 35, 432, 33], [255, 39, 432, 37], [256, 6, 433, 4, "console"], [256, 13, 433, 11], [256, 14, 433, 12, "warn"], [256, 18, 433, 16], [256, 19, 433, 17, "warningMessage"], [256, 33, 433, 31], [256, 34, 433, 32], [257, 4, 434, 2], [258, 2, 435, 0], [258, 3, 435, 1], [259, 2, 437, 0], [259, 11, 437, 9, "createTimer"], [259, 22, 437, 20, "createTimer"], [259, 23, 438, 2, "callbackID"], [259, 33, 438, 20], [259, 35, 439, 2, "duration"], [259, 43, 439, 18], [259, 45, 440, 2, "jsSchedulingTime"], [259, 61, 440, 26], [259, 63, 441, 2, "repeats"], [259, 70, 441, 18], [259, 72, 442, 8], [260, 4, 443, 2, "invariant"], [260, 13, 443, 11], [260, 14, 443, 12, "NativeTiming"], [260, 35, 443, 24], [260, 37, 443, 26], [260, 64, 443, 53], [260, 65, 443, 54], [261, 4, 444, 2, "NativeTiming"], [261, 25, 444, 14], [261, 26, 444, 15, "createTimer"], [261, 37, 444, 26], [261, 38, 444, 27, "callbackID"], [261, 48, 444, 37], [261, 50, 444, 39, "duration"], [261, 58, 444, 47], [261, 60, 444, 49, "jsSchedulingTime"], [261, 76, 444, 65], [261, 78, 444, 67, "repeats"], [261, 85, 444, 74], [261, 86, 444, 75], [262, 2, 445, 0], [263, 2, 447, 0], [263, 11, 447, 9, "deleteTimer"], [263, 22, 447, 20, "deleteTimer"], [263, 23, 447, 21, "timerID"], [263, 30, 447, 36], [263, 32, 447, 44], [264, 4, 448, 2, "invariant"], [264, 13, 448, 11], [264, 14, 448, 12, "NativeTiming"], [264, 35, 448, 24], [264, 37, 448, 26], [264, 64, 448, 53], [264, 65, 448, 54], [265, 4, 449, 2, "NativeTiming"], [265, 25, 449, 14], [265, 26, 449, 15, "deleteTimer"], [265, 37, 449, 26], [265, 38, 449, 27, "timerID"], [265, 45, 449, 34], [265, 46, 449, 35], [266, 2, 450, 0], [267, 2, 452, 0], [267, 11, 452, 9, "setSendIdleEvents"], [267, 28, 452, 26, "setSendIdleEvents"], [267, 29, 452, 27, "sendIdleEvents"], [267, 43, 452, 50], [267, 45, 452, 58], [268, 4, 453, 2, "invariant"], [268, 13, 453, 11], [268, 14, 453, 12, "NativeTiming"], [268, 35, 453, 24], [268, 37, 453, 26], [268, 64, 453, 53], [268, 65, 453, 54], [269, 4, 454, 2, "NativeTiming"], [269, 25, 454, 14], [269, 26, 454, 15, "setSendIdleEvents"], [269, 43, 454, 32], [269, 44, 454, 33, "sendIdleEvents"], [269, 58, 454, 47], [269, 59, 454, 48], [270, 2, 455, 0], [271, 2, 457, 0], [271, 6, 457, 4, "ExportedJSTimers"], [271, 22, 472, 1], [272, 2, 474, 0], [272, 6, 474, 4], [272, 7, 474, 5, "NativeTiming"], [272, 28, 474, 17], [272, 30, 474, 19], [273, 4, 475, 2, "console"], [273, 11, 475, 9], [273, 12, 475, 10, "warn"], [273, 16, 475, 14], [273, 17, 475, 15], [273, 75, 475, 73], [273, 76, 475, 74], [274, 4, 477, 2, "ExportedJSTimers"], [274, 20, 477, 18], [274, 23, 477, 22], [275, 6, 478, 4, "callReactNativeMicrotasks"], [275, 31, 478, 29], [275, 33, 478, 31, "JSTimers"], [275, 41, 478, 39], [275, 42, 478, 40, "callReactNativeMicrotasks"], [275, 67, 478, 65], [276, 6, 479, 4, "queueReactNativeMicrotask"], [276, 31, 479, 29], [276, 33, 479, 31, "JSTimers"], [276, 41, 479, 39], [276, 42, 479, 40, "queueReactNativeMicrotask"], [277, 4, 480, 2], [277, 5, 480, 21], [278, 2, 481, 0], [278, 3, 481, 1], [278, 9, 481, 7], [279, 4, 482, 2, "ExportedJSTimers"], [279, 20, 482, 18], [279, 23, 482, 21, "JSTimers"], [279, 31, 482, 29], [280, 2, 483, 0], [281, 2, 485, 0, "BatchedBridge"], [281, 15, 485, 13], [281, 16, 485, 14, "setReactNativeMicrotasksCallback"], [281, 48, 485, 46], [281, 49, 486, 2, "JSTimers"], [281, 57, 486, 10], [281, 58, 486, 11, "callReactNativeMicrotasks"], [281, 83, 487, 0], [281, 84, 487, 1], [282, 2, 487, 2], [282, 6, 487, 2, "_default"], [282, 14, 487, 2], [282, 17, 487, 2, "exports"], [282, 24, 487, 2], [282, 25, 487, 2, "default"], [282, 32, 487, 2], [282, 35, 489, 15, "ExportedJSTimers"], [282, 51, 489, 31], [283, 0, 489, 31], [283, 3]], "functionMap": {"names": ["<global>", "_getFreeIndex", "_allocateCallback", "_callTimer", "callback$argument_0.timeRemaining", "_callReactNativeMicrotasksPass", "_clearIndex", "_freeCallback", "setTimeout", "_allocateCallback$argument_0", "setInterval", "queueReactNativeMicrotask", "requestAnimationFrame", "requestIdleCallback", "<anonymous>", "setTimeout$argument_0", "cancelIdleCallback", "clearTimeout", "clearInterval", "clearReactNativeMicrotask", "cancelAnimationFrame", "callTimers", "callIdleCallbacks", "errors.forEach$argument_0", "callReactNativeMicrotasks", "emitTimeDriftWarning", "createTimer", "deleteTimer", "setSendIdleEvents"], "mappings": "AAA;ACiD;CDM;AEE;CFO;AGO;uBC6C;SDQ;CHc;AKM;CLwB;AME;CNK;AOE;CPmB;cQY;MCM,iCD;GRK;eUM;MDM,iCC;GVK;6BWU;MFE,iCE;GXK;yBYK;GZI;uBaO;UCW;WDO;oDEO;OFU;GbI;sBgBE;GhBgB;gBiBE;GjBE;iBkBE;GlBE;6BmBE;GnBM;wBoBE;GpBE;cqBM;aPkB;aOE;GrBO;qBsBE;mBCsB;0BRC;OQE,ID;GtBE;EwBM;mBDG;0BRC;OQE,IC;GxBE;EyBK;GzBM;A0BG;C1BQ;A2BE;C3BG;A4BE;C5BG"}}, "type": "js/module"}]}