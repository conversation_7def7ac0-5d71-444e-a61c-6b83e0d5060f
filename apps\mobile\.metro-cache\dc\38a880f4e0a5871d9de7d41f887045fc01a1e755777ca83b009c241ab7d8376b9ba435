{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./specs/NativeReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 82}}], "key": "8z68qK7+uEheWLutZQc4gQDT+Dk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createJavaScriptFlagGetter = createJavaScriptFlagGetter;\n  exports.createNativeFlagGetter = createNativeFlagGetter;\n  exports.getOverrides = getOverrides;\n  exports.setOverrides = setOverrides;\n  var _NativeReactNativeFeatureFlags = _interopRequireDefault(require(_dependencyMap[1], \"./specs/NativeReactNativeFeatureFlags\"));\n  var accessedFeatureFlags = new Set();\n  var overrides;\n  function createGetter(configName, customValueGetter, defaultValue) {\n    var cachedValue;\n    return () => {\n      if (cachedValue == null) {\n        cachedValue = customValueGetter() ?? defaultValue;\n      }\n      return cachedValue;\n    };\n  }\n  function createJavaScriptFlagGetter(configName, defaultValue) {\n    return createGetter(configName, () => {\n      accessedFeatureFlags.add(configName);\n      return overrides?.[configName]?.(defaultValue);\n    }, defaultValue);\n  }\n  function createNativeFlagGetter(configName, defaultValue) {\n    var skipUnavailableNativeModuleError = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    return createGetter(configName, () => {\n      maybeLogUnavailableNativeModuleError(configName);\n      return _NativeReactNativeFeatureFlags.default?.[configName]?.();\n    }, defaultValue);\n  }\n  function getOverrides() {\n    return overrides;\n  }\n  function setOverrides(newOverrides) {\n    if (overrides != null) {\n      throw new Error('Feature flags cannot be overridden more than once');\n    }\n    if (accessedFeatureFlags.size > 0) {\n      var accessedFeatureFlagsStr = Array.from(accessedFeatureFlags).join(', ');\n      throw new Error(`Feature flags were accessed before being overridden: ${accessedFeatureFlagsStr}`);\n    }\n    overrides = newOverrides;\n  }\n  var reportedConfigNames = new Set();\n  function maybeLogUnavailableNativeModuleError(configName) {\n    if (!_NativeReactNativeFeatureFlags.default && !reportedConfigNames.has(configName)) {\n      reportedConfigNames.add(configName);\n      console.error(`Could not access feature flag '${configName}' because native module method was not available`);\n    }\n  }\n});", "lineCount": 55, "map": [[10, 2, 16, 0], [10, 6, 16, 0, "_NativeReactNativeFeatureFlags"], [10, 36, 16, 0], [10, 39, 16, 0, "_interopRequireDefault"], [10, 61, 16, 0], [10, 62, 16, 0, "require"], [10, 69, 16, 0], [10, 70, 16, 0, "_dependencyMap"], [10, 84, 16, 0], [11, 2, 18, 0], [11, 6, 18, 6, "accessedFeatureFlags"], [11, 26, 18, 39], [11, 29, 18, 42], [11, 33, 18, 46, "Set"], [11, 36, 18, 49], [11, 37, 18, 50], [11, 38, 18, 51], [12, 2, 19, 0], [12, 6, 19, 4, "overrides"], [12, 15, 19, 54], [13, 2, 29, 0], [13, 11, 29, 9, "createGetter"], [13, 23, 29, 21, "createGetter"], [13, 24, 30, 2, "config<PERSON><PERSON>"], [13, 34, 30, 20], [13, 36, 31, 2, "customValueGetter"], [13, 53, 31, 31], [13, 55, 32, 2, "defaultValue"], [13, 67, 32, 17], [13, 69, 33, 13], [14, 4, 34, 2], [14, 8, 34, 6, "cachedValue"], [14, 19, 34, 21], [15, 4, 36, 2], [15, 11, 36, 9], [15, 17, 36, 15], [16, 6, 37, 4], [16, 10, 37, 8, "cachedValue"], [16, 21, 37, 19], [16, 25, 37, 23], [16, 29, 37, 27], [16, 31, 37, 29], [17, 8, 38, 6, "cachedValue"], [17, 19, 38, 17], [17, 22, 38, 20, "customValueGetter"], [17, 39, 38, 37], [17, 40, 38, 38], [17, 41, 38, 39], [17, 45, 38, 43, "defaultValue"], [17, 57, 38, 55], [18, 6, 39, 4], [19, 6, 40, 4], [19, 13, 40, 11, "cachedValue"], [19, 24, 40, 22], [20, 4, 41, 2], [20, 5, 41, 3], [21, 2, 42, 0], [22, 2, 44, 7], [22, 11, 44, 16, "createJavaScriptFlagGetter"], [22, 37, 44, 42, "createJavaScriptFlagGetter"], [22, 38, 47, 2, "config<PERSON><PERSON>"], [22, 48, 47, 15], [22, 50, 48, 2, "defaultValue"], [22, 62, 48, 60], [22, 64, 49, 56], [23, 4, 50, 2], [23, 11, 50, 9, "createGetter"], [23, 23, 50, 21], [23, 24, 51, 4, "config<PERSON><PERSON>"], [23, 34, 51, 14], [23, 36, 52, 4], [23, 42, 52, 10], [24, 6, 53, 6, "accessedFeatureFlags"], [24, 26, 53, 26], [24, 27, 53, 27, "add"], [24, 30, 53, 30], [24, 31, 53, 31, "config<PERSON><PERSON>"], [24, 41, 53, 41], [24, 42, 53, 42], [25, 6, 54, 6], [25, 13, 54, 13, "overrides"], [25, 22, 54, 22], [25, 25, 54, 25, "config<PERSON><PERSON>"], [25, 35, 54, 35], [25, 36, 54, 36], [25, 39, 54, 39, "defaultValue"], [25, 51, 54, 51], [25, 52, 54, 52], [26, 4, 55, 4], [26, 5, 55, 5], [26, 7, 56, 4, "defaultValue"], [26, 19, 57, 2], [26, 20, 57, 3], [27, 2, 58, 0], [28, 2, 62, 7], [28, 11, 62, 16, "createNativeFlagGetter"], [28, 33, 62, 38, "createNativeFlagGetter"], [28, 34, 63, 2, "config<PERSON><PERSON>"], [28, 44, 63, 15], [28, 46, 64, 2, "defaultValue"], [28, 58, 64, 64], [28, 60, 66, 60], [29, 4, 66, 60], [29, 8, 65, 2, "skipUnavailableNativeModuleError"], [29, 40, 65, 43], [29, 43, 65, 43, "arguments"], [29, 52, 65, 43], [29, 53, 65, 43, "length"], [29, 59, 65, 43], [29, 67, 65, 43, "arguments"], [29, 76, 65, 43], [29, 84, 65, 43, "undefined"], [29, 93, 65, 43], [29, 96, 65, 43, "arguments"], [29, 105, 65, 43], [29, 111, 65, 46], [29, 116, 65, 51], [30, 4, 67, 2], [30, 11, 67, 9, "createGetter"], [30, 23, 67, 21], [30, 24, 68, 4, "config<PERSON><PERSON>"], [30, 34, 68, 14], [30, 36, 69, 4], [30, 42, 69, 10], [31, 6, 70, 6, "maybeLogUnavailableNativeModuleError"], [31, 42, 70, 42], [31, 43, 70, 43, "config<PERSON><PERSON>"], [31, 53, 70, 53], [31, 54, 70, 54], [32, 6, 71, 6], [32, 13, 71, 13, "NativeReactNativeFeatureFlags"], [32, 51, 71, 42], [32, 54, 71, 45, "config<PERSON><PERSON>"], [32, 64, 71, 55], [32, 65, 71, 56], [32, 68, 71, 59], [32, 69, 71, 60], [33, 4, 72, 4], [33, 5, 72, 5], [33, 7, 73, 4, "defaultValue"], [33, 19, 74, 2], [33, 20, 74, 3], [34, 2, 75, 0], [35, 2, 77, 7], [35, 11, 77, 16, "getOverrides"], [35, 23, 77, 28, "getOverrides"], [35, 24, 77, 28], [35, 26, 77, 72], [36, 4, 78, 2], [36, 11, 78, 9, "overrides"], [36, 20, 78, 18], [37, 2, 79, 0], [38, 2, 81, 7], [38, 11, 81, 16, "setOverrides"], [38, 23, 81, 28, "setOverrides"], [38, 24, 82, 2, "newOverrides"], [38, 36, 82, 54], [38, 38, 83, 8], [39, 4, 84, 2], [39, 8, 84, 6, "overrides"], [39, 17, 84, 15], [39, 21, 84, 19], [39, 25, 84, 23], [39, 27, 84, 25], [40, 6, 85, 4], [40, 12, 85, 10], [40, 16, 85, 14, "Error"], [40, 21, 85, 19], [40, 22, 85, 20], [40, 73, 85, 71], [40, 74, 85, 72], [41, 4, 86, 2], [42, 4, 88, 2], [42, 8, 88, 6, "accessedFeatureFlags"], [42, 28, 88, 26], [42, 29, 88, 27, "size"], [42, 33, 88, 31], [42, 36, 88, 34], [42, 37, 88, 35], [42, 39, 88, 37], [43, 6, 89, 4], [43, 10, 89, 10, "accessedFeatureFlagsStr"], [43, 33, 89, 33], [43, 36, 89, 36, "Array"], [43, 41, 89, 41], [43, 42, 89, 42, "from"], [43, 46, 89, 46], [43, 47, 89, 47, "accessedFeatureFlags"], [43, 67, 89, 67], [43, 68, 89, 68], [43, 69, 89, 69, "join"], [43, 73, 89, 73], [43, 74, 89, 74], [43, 78, 89, 78], [43, 79, 89, 79], [44, 6, 90, 4], [44, 12, 90, 10], [44, 16, 90, 14, "Error"], [44, 21, 90, 19], [44, 22, 91, 6], [44, 78, 91, 62, "accessedFeatureFlagsStr"], [44, 101, 91, 85], [44, 103, 92, 4], [44, 104, 92, 5], [45, 4, 93, 2], [46, 4, 95, 2, "overrides"], [46, 13, 95, 11], [46, 16, 95, 14, "newOverrides"], [46, 28, 95, 26], [47, 2, 96, 0], [48, 2, 98, 0], [48, 6, 98, 6, "reportedConfigNames"], [48, 25, 98, 38], [48, 28, 98, 41], [48, 32, 98, 45, "Set"], [48, 35, 98, 48], [48, 36, 98, 49], [48, 37, 98, 50], [49, 2, 100, 0], [49, 11, 100, 9, "maybeLogUnavailableNativeModuleError"], [49, 47, 100, 45, "maybeLogUnavailableNativeModuleError"], [49, 48, 100, 46, "config<PERSON><PERSON>"], [49, 58, 100, 64], [49, 60, 100, 72], [50, 4, 101, 2], [50, 8, 101, 6], [50, 9, 101, 7, "NativeReactNativeFeatureFlags"], [50, 47, 101, 36], [50, 51, 101, 40], [50, 52, 101, 41, "reportedConfigNames"], [50, 71, 101, 60], [50, 72, 101, 61, "has"], [50, 75, 101, 64], [50, 76, 101, 65, "config<PERSON><PERSON>"], [50, 86, 101, 75], [50, 87, 101, 76], [50, 89, 101, 78], [51, 6, 102, 4, "reportedConfigNames"], [51, 25, 102, 23], [51, 26, 102, 24, "add"], [51, 29, 102, 27], [51, 30, 102, 28, "config<PERSON><PERSON>"], [51, 40, 102, 38], [51, 41, 102, 39], [52, 6, 103, 4, "console"], [52, 13, 103, 11], [52, 14, 103, 12, "error"], [52, 19, 103, 17], [52, 20, 104, 6], [52, 54, 104, 40, "config<PERSON><PERSON>"], [52, 64, 104, 50], [52, 114, 105, 4], [52, 115, 105, 5], [53, 4, 106, 2], [54, 2, 107, 0], [55, 0, 107, 1], [55, 3]], "functionMap": {"names": ["<global>", "createGetter", "<anonymous>", "createJavaScriptFlagGetter", "createGetter$argument_1", "createNativeFlagGetter", "getOverrides", "setOverrides", "maybeLogUnavailableNativeModuleError"], "mappings": "AAA;AC4B;SCO;GDK;CDC;OGE;ICQ;KDG;CHG;OKI;IDO;KCG;CLG;OME;CNE;OOE;CPe;AQI"}}, "type": "js/module"}]}