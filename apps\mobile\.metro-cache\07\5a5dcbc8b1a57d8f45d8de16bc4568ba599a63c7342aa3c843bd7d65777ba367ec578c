{"dependencies": [{"name": "../../../../Libraries/TurboModule/TurboModuleRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 93}}], "key": "H+9Pk6sLVUPsBv6YXnwcNYMfH5g=", "exportNames": ["*"]}}, {"name": "../../../../Libraries/Utilities/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 17}, "end": {"line": 15, "column": 68}}], "key": "DBYMWtzTaFrTYC6Qt0jLpyQphfA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var TurboModuleRegistry = _interopRequireWildcard(require(_dependencyMap[0], \"../../../../Libraries/TurboModule/TurboModuleRegistry\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var Platform = require(_dependencyMap[1], \"../../../../Libraries/Utilities/Platform\").default;\n  var NativeModule = TurboModuleRegistry.getEnforcing('ExceptionsManager');\n  var ExceptionsManager = {\n    reportFatalException(message, stack, exceptionId) {\n      NativeModule.reportFatalException(message, stack, exceptionId);\n    },\n    reportSoftException(message, stack, exceptionId) {\n      NativeModule.reportSoftException(message, stack, exceptionId);\n    },\n    dismissRedbox() {\n      if (Platform.OS !== 'ios' && NativeModule.dismissRedbox) {\n        NativeModule.dismissRedbox();\n      }\n    },\n    reportException(data) {\n      if (NativeModule.reportException) {\n        NativeModule.reportException(data);\n        return;\n      }\n      if (data.isFatal) {\n        ExceptionsManager.reportFatalException(data.message, data.stack, data.id);\n      } else {\n        ExceptionsManager.reportSoftException(data.message, data.stack, data.id);\n      }\n    }\n  };\n  var _default = exports.default = ExceptionsManager;\n});", "lineCount": 35, "map": [[6, 2, 13, 0], [6, 6, 13, 0, "TurboModuleRegistry"], [6, 25, 13, 0], [6, 28, 13, 0, "_interopRequireWildcard"], [6, 51, 13, 0], [6, 52, 13, 0, "require"], [6, 59, 13, 0], [6, 60, 13, 0, "_dependencyMap"], [6, 74, 13, 0], [7, 2, 13, 93], [7, 11, 13, 93, "_interopRequireWildcard"], [7, 35, 13, 93, "e"], [7, 36, 13, 93], [7, 38, 13, 93, "t"], [7, 39, 13, 93], [7, 68, 13, 93, "WeakMap"], [7, 75, 13, 93], [7, 81, 13, 93, "r"], [7, 82, 13, 93], [7, 89, 13, 93, "WeakMap"], [7, 96, 13, 93], [7, 100, 13, 93, "n"], [7, 101, 13, 93], [7, 108, 13, 93, "WeakMap"], [7, 115, 13, 93], [7, 127, 13, 93, "_interopRequireWildcard"], [7, 150, 13, 93], [7, 162, 13, 93, "_interopRequireWildcard"], [7, 163, 13, 93, "e"], [7, 164, 13, 93], [7, 166, 13, 93, "t"], [7, 167, 13, 93], [7, 176, 13, 93, "t"], [7, 177, 13, 93], [7, 181, 13, 93, "e"], [7, 182, 13, 93], [7, 186, 13, 93, "e"], [7, 187, 13, 93], [7, 188, 13, 93, "__esModule"], [7, 198, 13, 93], [7, 207, 13, 93, "e"], [7, 208, 13, 93], [7, 214, 13, 93, "o"], [7, 215, 13, 93], [7, 217, 13, 93, "i"], [7, 218, 13, 93], [7, 220, 13, 93, "f"], [7, 221, 13, 93], [7, 226, 13, 93, "__proto__"], [7, 235, 13, 93], [7, 243, 13, 93, "default"], [7, 250, 13, 93], [7, 252, 13, 93, "e"], [7, 253, 13, 93], [7, 270, 13, 93, "e"], [7, 271, 13, 93], [7, 294, 13, 93, "e"], [7, 295, 13, 93], [7, 320, 13, 93, "e"], [7, 321, 13, 93], [7, 330, 13, 93, "f"], [7, 331, 13, 93], [7, 337, 13, 93, "o"], [7, 338, 13, 93], [7, 341, 13, 93, "t"], [7, 342, 13, 93], [7, 345, 13, 93, "n"], [7, 346, 13, 93], [7, 349, 13, 93, "r"], [7, 350, 13, 93], [7, 358, 13, 93, "o"], [7, 359, 13, 93], [7, 360, 13, 93, "has"], [7, 363, 13, 93], [7, 364, 13, 93, "e"], [7, 365, 13, 93], [7, 375, 13, 93, "o"], [7, 376, 13, 93], [7, 377, 13, 93, "get"], [7, 380, 13, 93], [7, 381, 13, 93, "e"], [7, 382, 13, 93], [7, 385, 13, 93, "o"], [7, 386, 13, 93], [7, 387, 13, 93, "set"], [7, 390, 13, 93], [7, 391, 13, 93, "e"], [7, 392, 13, 93], [7, 394, 13, 93, "f"], [7, 395, 13, 93], [7, 409, 13, 93, "_t"], [7, 411, 13, 93], [7, 415, 13, 93, "e"], [7, 416, 13, 93], [7, 432, 13, 93, "_t"], [7, 434, 13, 93], [7, 441, 13, 93, "hasOwnProperty"], [7, 455, 13, 93], [7, 456, 13, 93, "call"], [7, 460, 13, 93], [7, 461, 13, 93, "e"], [7, 462, 13, 93], [7, 464, 13, 93, "_t"], [7, 466, 13, 93], [7, 473, 13, 93, "i"], [7, 474, 13, 93], [7, 478, 13, 93, "o"], [7, 479, 13, 93], [7, 482, 13, 93, "Object"], [7, 488, 13, 93], [7, 489, 13, 93, "defineProperty"], [7, 503, 13, 93], [7, 508, 13, 93, "Object"], [7, 514, 13, 93], [7, 515, 13, 93, "getOwnPropertyDescriptor"], [7, 539, 13, 93], [7, 540, 13, 93, "e"], [7, 541, 13, 93], [7, 543, 13, 93, "_t"], [7, 545, 13, 93], [7, 552, 13, 93, "i"], [7, 553, 13, 93], [7, 554, 13, 93, "get"], [7, 557, 13, 93], [7, 561, 13, 93, "i"], [7, 562, 13, 93], [7, 563, 13, 93, "set"], [7, 566, 13, 93], [7, 570, 13, 93, "o"], [7, 571, 13, 93], [7, 572, 13, 93, "f"], [7, 573, 13, 93], [7, 575, 13, 93, "_t"], [7, 577, 13, 93], [7, 579, 13, 93, "i"], [7, 580, 13, 93], [7, 584, 13, 93, "f"], [7, 585, 13, 93], [7, 586, 13, 93, "_t"], [7, 588, 13, 93], [7, 592, 13, 93, "e"], [7, 593, 13, 93], [7, 594, 13, 93, "_t"], [7, 596, 13, 93], [7, 607, 13, 93, "f"], [7, 608, 13, 93], [7, 613, 13, 93, "e"], [7, 614, 13, 93], [7, 616, 13, 93, "t"], [7, 617, 13, 93], [8, 2, 15, 0], [8, 6, 15, 6, "Platform"], [8, 14, 15, 14], [8, 17, 15, 17, "require"], [8, 24, 15, 24], [8, 25, 15, 24, "_dependencyMap"], [8, 39, 15, 24], [8, 86, 15, 67], [8, 87, 15, 68], [8, 88, 15, 69, "default"], [8, 95, 15, 76], [9, 2, 54, 0], [9, 6, 54, 6, "NativeModule"], [9, 18, 54, 18], [9, 21, 55, 2, "TurboModuleRegistry"], [9, 40, 55, 21], [9, 41, 55, 22, "getEnforcing"], [9, 53, 55, 34], [9, 54, 55, 41], [9, 73, 55, 60], [9, 74, 55, 61], [10, 2, 57, 0], [10, 6, 57, 6, "ExceptionsManager"], [10, 23, 57, 23], [10, 26, 57, 26], [11, 4, 58, 2, "reportFatalException"], [11, 24, 58, 22, "reportFatalException"], [11, 25, 59, 4, "message"], [11, 32, 59, 19], [11, 34, 60, 4, "stack"], [11, 39, 60, 28], [11, 41, 61, 4, "exceptionId"], [11, 52, 61, 23], [11, 54, 62, 4], [12, 6, 63, 4, "NativeModule"], [12, 18, 63, 16], [12, 19, 63, 17, "reportFatalException"], [12, 39, 63, 37], [12, 40, 63, 38, "message"], [12, 47, 63, 45], [12, 49, 63, 47, "stack"], [12, 54, 63, 52], [12, 56, 63, 54, "exceptionId"], [12, 67, 63, 65], [12, 68, 63, 66], [13, 4, 64, 2], [13, 5, 64, 3], [14, 4, 65, 2, "reportSoftException"], [14, 23, 65, 21, "reportSoftException"], [14, 24, 66, 4, "message"], [14, 31, 66, 19], [14, 33, 67, 4, "stack"], [14, 38, 67, 28], [14, 40, 68, 4, "exceptionId"], [14, 51, 68, 23], [14, 53, 69, 4], [15, 6, 70, 4, "NativeModule"], [15, 18, 70, 16], [15, 19, 70, 17, "reportSoftException"], [15, 38, 70, 36], [15, 39, 70, 37, "message"], [15, 46, 70, 44], [15, 48, 70, 46, "stack"], [15, 53, 70, 51], [15, 55, 70, 53, "exceptionId"], [15, 66, 70, 64], [15, 67, 70, 65], [16, 4, 71, 2], [16, 5, 71, 3], [17, 4, 72, 2, "dismissRedbox"], [17, 17, 72, 15, "dismissRedbox"], [17, 18, 72, 15], [17, 20, 72, 24], [18, 6, 73, 4], [18, 10, 73, 8, "Platform"], [18, 18, 73, 16], [18, 19, 73, 17, "OS"], [18, 21, 73, 19], [18, 26, 73, 24], [18, 31, 73, 29], [18, 35, 73, 33, "NativeModule"], [18, 47, 73, 45], [18, 48, 73, 46, "dismissRedbox"], [18, 61, 73, 59], [18, 63, 73, 61], [19, 8, 75, 6, "NativeModule"], [19, 20, 75, 18], [19, 21, 75, 19, "dismissRedbox"], [19, 34, 75, 32], [19, 35, 75, 33], [19, 36, 75, 34], [20, 6, 76, 4], [21, 4, 77, 2], [21, 5, 77, 3], [22, 4, 78, 2, "reportException"], [22, 19, 78, 17, "reportException"], [22, 20, 78, 18, "data"], [22, 24, 78, 37], [22, 26, 78, 45], [23, 6, 79, 4], [23, 10, 79, 8, "NativeModule"], [23, 22, 79, 20], [23, 23, 79, 21, "reportException"], [23, 38, 79, 36], [23, 40, 79, 38], [24, 8, 80, 6, "NativeModule"], [24, 20, 80, 18], [24, 21, 80, 19, "reportException"], [24, 36, 80, 34], [24, 37, 80, 35, "data"], [24, 41, 80, 39], [24, 42, 80, 40], [25, 8, 81, 6], [26, 6, 82, 4], [27, 6, 83, 4], [27, 10, 83, 8, "data"], [27, 14, 83, 12], [27, 15, 83, 13, "isFatal"], [27, 22, 83, 20], [27, 24, 83, 22], [28, 8, 84, 6, "ExceptionsManager"], [28, 25, 84, 23], [28, 26, 84, 24, "reportFatalException"], [28, 46, 84, 44], [28, 47, 84, 45, "data"], [28, 51, 84, 49], [28, 52, 84, 50, "message"], [28, 59, 84, 57], [28, 61, 84, 59, "data"], [28, 65, 84, 63], [28, 66, 84, 64, "stack"], [28, 71, 84, 69], [28, 73, 84, 71, "data"], [28, 77, 84, 75], [28, 78, 84, 76, "id"], [28, 80, 84, 78], [28, 81, 84, 79], [29, 6, 85, 4], [29, 7, 85, 5], [29, 13, 85, 11], [30, 8, 86, 6, "ExceptionsManager"], [30, 25, 86, 23], [30, 26, 86, 24, "reportSoftException"], [30, 45, 86, 43], [30, 46, 86, 44, "data"], [30, 50, 86, 48], [30, 51, 86, 49, "message"], [30, 58, 86, 56], [30, 60, 86, 58, "data"], [30, 64, 86, 62], [30, 65, 86, 63, "stack"], [30, 70, 86, 68], [30, 72, 86, 70, "data"], [30, 76, 86, 74], [30, 77, 86, 75, "id"], [30, 79, 86, 77], [30, 80, 86, 78], [31, 6, 87, 4], [32, 4, 88, 2], [33, 2, 89, 0], [33, 3, 89, 1], [34, 2, 89, 2], [34, 6, 89, 2, "_default"], [34, 14, 89, 2], [34, 17, 89, 2, "exports"], [34, 24, 89, 2], [34, 25, 89, 2, "default"], [34, 32, 89, 2], [34, 35, 91, 15, "ExceptionsManager"], [34, 52, 91, 32], [35, 0, 91, 32], [35, 3]], "functionMap": {"names": ["<global>", "ExceptionsManager.reportFatalException", "ExceptionsManager.reportSoftException", "ExceptionsManager.dismissRedbox", "ExceptionsManager.reportException"], "mappings": "AAA;ECyD;GDM;EEC;GFM;EGC;GHK;EIC;GJU"}}, "type": "js/module"}]}