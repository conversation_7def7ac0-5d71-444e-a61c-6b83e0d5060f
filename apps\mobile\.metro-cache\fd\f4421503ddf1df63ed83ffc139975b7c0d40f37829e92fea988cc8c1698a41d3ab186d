{"dependencies": [{"name": "./mutables", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 41, "index": 55}}], "key": "SiNpLdwfdd4YE0waycjIzPSn4ZU=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 56}, "end": {"line": 3, "column": 61, "index": 117}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ReducedMotionManager = void 0;\n  exports.isReducedMotionEnabledInSystem = isReducedMotionEnabledInSystem;\n  var _mutables = require(_dependencyMap[0], \"./mutables\");\n  var _PlatformChecker = require(_dependencyMap[1], \"./PlatformChecker\");\n  function isReducedMotionEnabledInSystem() {\n    return (0, _PlatformChecker.isWeb)() ? (0, _PlatformChecker.isWindowAvailable)() ?\n    // @ts-ignore Fallback if `window` is undefined.\n    window.matchMedia('(prefers-reduced-motion: reduce)').matches : false : !!global._REANIMATED_IS_REDUCED_MOTION;\n  }\n  var IS_REDUCED_MOTION_ENABLED_IN_SYSTEM = isReducedMotionEnabledInSystem();\n  var ReducedMotionManager = exports.ReducedMotionManager = {\n    jsValue: IS_REDUCED_MOTION_ENABLED_IN_SYSTEM,\n    uiValue: (0, _mutables.makeMutable)(IS_REDUCED_MOTION_ENABLED_IN_SYSTEM),\n    setEnabled(value) {\n      ReducedMotionManager.jsValue = value;\n      ReducedMotionManager.uiValue.value = value;\n    }\n  };\n});", "lineCount": 25, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "ReducedMotionManager"], [7, 30, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "isReducedMotionEnabledInSystem"], [8, 40, 1, 13], [8, 43, 1, 13, "isReducedMotionEnabledInSystem"], [8, 73, 1, 13], [9, 2, 2, 0], [9, 6, 2, 0, "_mutables"], [9, 15, 2, 0], [9, 18, 2, 0, "require"], [9, 25, 2, 0], [9, 26, 2, 0, "_dependencyMap"], [9, 40, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_PlatformChecker"], [10, 22, 3, 0], [10, 25, 3, 0, "require"], [10, 32, 3, 0], [10, 33, 3, 0, "_dependencyMap"], [10, 47, 3, 0], [11, 2, 7, 7], [11, 11, 7, 16, "isReducedMotionEnabledInSystem"], [11, 41, 7, 46, "isReducedMotionEnabledInSystem"], [11, 42, 7, 46], [11, 44, 7, 49], [12, 4, 8, 2], [12, 11, 8, 9], [12, 15, 8, 9, "isWeb"], [12, 37, 8, 14], [12, 39, 8, 15], [12, 40, 8, 16], [12, 43, 9, 6], [12, 47, 9, 6, "isWindowAvailable"], [12, 81, 9, 23], [12, 83, 9, 24], [12, 84, 9, 25], [13, 4, 10, 8], [14, 4, 11, 8, "window"], [14, 10, 11, 14], [14, 11, 11, 15, "matchMedia"], [14, 21, 11, 25], [14, 22, 11, 26], [14, 56, 11, 60], [14, 57, 11, 61], [14, 58, 11, 62, "matches"], [14, 65, 11, 69], [14, 68, 12, 8], [14, 73, 12, 13], [14, 76, 13, 6], [14, 77, 13, 7], [14, 78, 13, 9, "global"], [14, 84, 13, 15], [14, 85, 13, 32, "_REANIMATED_IS_REDUCED_MOTION"], [14, 114, 13, 61], [15, 2, 14, 0], [16, 2, 16, 0], [16, 6, 16, 6, "IS_REDUCED_MOTION_ENABLED_IN_SYSTEM"], [16, 41, 16, 41], [16, 44, 16, 44, "isReducedMotionEnabledInSystem"], [16, 74, 16, 74], [16, 75, 16, 75], [16, 76, 16, 76], [17, 2, 18, 7], [17, 6, 18, 13, "ReducedMotionManager"], [17, 26, 18, 33], [17, 29, 18, 33, "exports"], [17, 36, 18, 33], [17, 37, 18, 33, "ReducedMotionManager"], [17, 57, 18, 33], [17, 60, 18, 36], [18, 4, 19, 2, "jsValue"], [18, 11, 19, 9], [18, 13, 19, 11, "IS_REDUCED_MOTION_ENABLED_IN_SYSTEM"], [18, 48, 19, 46], [19, 4, 20, 2, "uiValue"], [19, 11, 20, 9], [19, 13, 20, 11], [19, 17, 20, 11, "makeMutable"], [19, 38, 20, 22], [19, 40, 20, 23, "IS_REDUCED_MOTION_ENABLED_IN_SYSTEM"], [19, 75, 20, 58], [19, 76, 20, 59], [20, 4, 21, 2, "setEnabled"], [20, 14, 21, 12, "setEnabled"], [20, 15, 21, 13, "value"], [20, 20, 21, 27], [20, 22, 21, 29], [21, 6, 22, 4, "ReducedMotionManager"], [21, 26, 22, 24], [21, 27, 22, 25, "jsValue"], [21, 34, 22, 32], [21, 37, 22, 35, "value"], [21, 42, 22, 40], [22, 6, 23, 4, "ReducedMotionManager"], [22, 26, 23, 24], [22, 27, 23, 25, "uiValue"], [22, 34, 23, 32], [22, 35, 23, 33, "value"], [22, 40, 23, 38], [22, 43, 23, 41, "value"], [22, 48, 23, 46], [23, 4, 24, 2], [24, 2, 25, 0], [24, 3, 25, 1], [25, 0, 25, 2], [25, 3]], "functionMap": {"names": ["<global>", "isReducedMotionEnabledInSystem", "ReducedMotionManager.setEnabled"], "mappings": "AAA;OCM;CDO;EEO;GFG"}}, "type": "js/module"}]}