{"dependencies": [{"name": "./lib/markup", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 14, "index": 155}, "end": {"line": 7, "column": 37, "index": 178}}], "key": "jmWyOIwUhawNxN+alOZIsh3EVEk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, '__esModule', {\n    value: true\n  });\n  exports.test = exports.serialize = exports.default = void 0;\n  var _markup = require(_dependencyMap[0], \"./lib/markup\");\n  var Symbol = globalThis['jest-symbol-do-not-touch'] || globalThis.Symbol;\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  // Child can be `number` in Stack renderer but not in Fiber renderer.\n\n  var testSymbol = typeof Symbol === 'function' && Symbol.for ? Symbol.for('react.test.json') : 0xea71357;\n  var getPropKeys = object => {\n    var props = object.props;\n    return props ? Object.keys(props).filter(key => props[key] !== undefined).sort() : [];\n  };\n  var serialize = (object, config, indentation, depth, refs, printer) => ++depth > config.maxDepth ? (0, _markup.printElementAsLeaf)(object.type, config) : (0, _markup.printElement)(object.type, object.props ? (0, _markup.printProps)(getPropKeys(object), object.props, config, indentation + config.indent, depth, refs, printer) : '', object.children ? (0, _markup.printChildren)(object.children, config, indentation + config.indent, depth, refs, printer) : '', config, indentation);\n  exports.serialize = serialize;\n  var test = val => val && val.$$typeof === testSymbol;\n  exports.test = test;\n  var plugin = {\n    serialize,\n    test\n  };\n  var _default = plugin;\n  exports.default = _default;\n});", "lineCount": 33, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8, "test"], [7, 14, 6, 12], [7, 17, 6, 15, "exports"], [7, 24, 6, 22], [7, 25, 6, 23, "serialize"], [7, 34, 6, 32], [7, 37, 6, 35, "exports"], [7, 44, 6, 42], [7, 45, 6, 43, "default"], [7, 52, 6, 50], [7, 55, 6, 53], [7, 60, 6, 58], [7, 61, 6, 59], [8, 2, 7, 0], [8, 6, 7, 4, "_markup"], [8, 13, 7, 11], [8, 16, 7, 14, "require"], [8, 23, 7, 21], [8, 24, 7, 21, "_dependencyMap"], [8, 38, 7, 21], [8, 57, 7, 36], [8, 58, 7, 37], [9, 2, 8, 0], [9, 6, 8, 4, "Symbol"], [9, 12, 8, 10], [9, 15, 8, 13, "globalThis"], [9, 25, 8, 23], [9, 26, 8, 24], [9, 52, 8, 50], [9, 53, 8, 51], [9, 57, 8, 55, "globalThis"], [9, 67, 8, 65], [9, 68, 8, 66, "Symbol"], [9, 74, 8, 72], [10, 2, 9, 0], [11, 0, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 2, 15, 0], [18, 2, 17, 0], [18, 6, 17, 6, "testSymbol"], [18, 16, 17, 16], [18, 19, 18, 2], [18, 26, 18, 9, "Symbol"], [18, 32, 18, 15], [18, 37, 18, 20], [18, 47, 18, 30], [18, 51, 18, 34, "Symbol"], [18, 57, 18, 40], [18, 58, 18, 41, "for"], [18, 61, 18, 44], [18, 64, 19, 6, "Symbol"], [18, 70, 19, 12], [18, 71, 19, 13, "for"], [18, 74, 19, 16], [18, 75, 19, 17], [18, 92, 19, 34], [18, 93, 19, 35], [18, 96, 20, 6], [18, 105, 20, 15], [19, 2, 21, 0], [19, 6, 21, 6, "getPropKeys"], [19, 17, 21, 17], [19, 20, 21, 20, "object"], [19, 26, 21, 26], [19, 30, 21, 30], [20, 4, 22, 2], [20, 8, 22, 9, "props"], [20, 13, 22, 14], [20, 16, 22, 18, "object"], [20, 22, 22, 24], [20, 23, 22, 9, "props"], [20, 28, 22, 14], [21, 4, 23, 2], [21, 11, 23, 9, "props"], [21, 16, 23, 14], [21, 19, 24, 6, "Object"], [21, 25, 24, 12], [21, 26, 24, 13, "keys"], [21, 30, 24, 17], [21, 31, 24, 18, "props"], [21, 36, 24, 23], [21, 37, 24, 24], [21, 38, 25, 9, "filter"], [21, 44, 25, 15], [21, 45, 25, 16, "key"], [21, 48, 25, 19], [21, 52, 25, 23, "props"], [21, 57, 25, 28], [21, 58, 25, 29, "key"], [21, 61, 25, 32], [21, 62, 25, 33], [21, 67, 25, 38, "undefined"], [21, 76, 25, 47], [21, 77, 25, 48], [21, 78, 26, 9, "sort"], [21, 82, 26, 13], [21, 83, 26, 14], [21, 84, 26, 15], [21, 87, 27, 6], [21, 89, 27, 8], [22, 2, 28, 0], [22, 3, 28, 1], [23, 2, 29, 0], [23, 6, 29, 6, "serialize"], [23, 15, 29, 15], [23, 18, 29, 18, "serialize"], [23, 19, 29, 19, "object"], [23, 25, 29, 25], [23, 27, 29, 27, "config"], [23, 33, 29, 33], [23, 35, 29, 35, "indentation"], [23, 46, 29, 46], [23, 48, 29, 48, "depth"], [23, 53, 29, 53], [23, 55, 29, 55, "refs"], [23, 59, 29, 59], [23, 61, 29, 61, "printer"], [23, 68, 29, 68], [23, 73, 30, 2], [23, 75, 30, 4, "depth"], [23, 80, 30, 9], [23, 83, 30, 12, "config"], [23, 89, 30, 18], [23, 90, 30, 19, "max<PERSON><PERSON><PERSON>"], [23, 98, 30, 27], [23, 101, 31, 6], [23, 102, 31, 7], [23, 103, 31, 8], [23, 105, 31, 10, "_markup"], [23, 112, 31, 17], [23, 113, 31, 18, "printElementAsLeaf"], [23, 131, 31, 36], [23, 133, 31, 38, "object"], [23, 139, 31, 44], [23, 140, 31, 45, "type"], [23, 144, 31, 49], [23, 146, 31, 51, "config"], [23, 152, 31, 57], [23, 153, 31, 58], [23, 156, 32, 6], [23, 157, 32, 7], [23, 158, 32, 8], [23, 160, 32, 10, "_markup"], [23, 167, 32, 17], [23, 168, 32, 18, "printElement"], [23, 180, 32, 30], [23, 182, 33, 8, "object"], [23, 188, 33, 14], [23, 189, 33, 15, "type"], [23, 193, 33, 19], [23, 195, 34, 8, "object"], [23, 201, 34, 14], [23, 202, 34, 15, "props"], [23, 207, 34, 20], [23, 210, 35, 12], [23, 211, 35, 13], [23, 212, 35, 14], [23, 214, 35, 16, "_markup"], [23, 221, 35, 23], [23, 222, 35, 24, "printProps"], [23, 232, 35, 34], [23, 234, 36, 14, "getPropKeys"], [23, 245, 36, 25], [23, 246, 36, 26, "object"], [23, 252, 36, 32], [23, 253, 36, 33], [23, 255, 37, 14, "object"], [23, 261, 37, 20], [23, 262, 37, 21, "props"], [23, 267, 37, 26], [23, 269, 38, 14, "config"], [23, 275, 38, 20], [23, 277, 39, 14, "indentation"], [23, 288, 39, 25], [23, 291, 39, 28, "config"], [23, 297, 39, 34], [23, 298, 39, 35, "indent"], [23, 304, 39, 41], [23, 306, 40, 14, "depth"], [23, 311, 40, 19], [23, 313, 41, 14, "refs"], [23, 317, 41, 18], [23, 319, 42, 14, "printer"], [23, 326, 43, 12], [23, 327, 43, 13], [23, 330, 44, 12], [23, 332, 44, 14], [23, 334, 45, 8, "object"], [23, 340, 45, 14], [23, 341, 45, 15, "children"], [23, 349, 45, 23], [23, 352, 46, 12], [23, 353, 46, 13], [23, 354, 46, 14], [23, 356, 46, 16, "_markup"], [23, 363, 46, 23], [23, 364, 46, 24, "printChildren"], [23, 377, 46, 37], [23, 379, 47, 14, "object"], [23, 385, 47, 20], [23, 386, 47, 21, "children"], [23, 394, 47, 29], [23, 396, 48, 14, "config"], [23, 402, 48, 20], [23, 404, 49, 14, "indentation"], [23, 415, 49, 25], [23, 418, 49, 28, "config"], [23, 424, 49, 34], [23, 425, 49, 35, "indent"], [23, 431, 49, 41], [23, 433, 50, 14, "depth"], [23, 438, 50, 19], [23, 440, 51, 14, "refs"], [23, 444, 51, 18], [23, 446, 52, 14, "printer"], [23, 453, 53, 12], [23, 454, 53, 13], [23, 457, 54, 12], [23, 459, 54, 14], [23, 461, 55, 8, "config"], [23, 467, 55, 14], [23, 469, 56, 8, "indentation"], [23, 480, 57, 6], [23, 481, 57, 7], [24, 2, 58, 0, "exports"], [24, 9, 58, 7], [24, 10, 58, 8, "serialize"], [24, 19, 58, 17], [24, 22, 58, 20, "serialize"], [24, 31, 58, 29], [25, 2, 59, 0], [25, 6, 59, 6, "test"], [25, 10, 59, 10], [25, 13, 59, 13, "val"], [25, 16, 59, 16], [25, 20, 59, 20, "val"], [25, 23, 59, 23], [25, 27, 59, 27, "val"], [25, 30, 59, 30], [25, 31, 59, 31, "$$typeof"], [25, 39, 59, 39], [25, 44, 59, 44, "testSymbol"], [25, 54, 59, 54], [26, 2, 60, 0, "exports"], [26, 9, 60, 7], [26, 10, 60, 8, "test"], [26, 14, 60, 12], [26, 17, 60, 15, "test"], [26, 21, 60, 19], [27, 2, 61, 0], [27, 6, 61, 6, "plugin"], [27, 12, 61, 12], [27, 15, 61, 15], [28, 4, 62, 2, "serialize"], [28, 13, 62, 11], [29, 4, 63, 2, "test"], [30, 2, 64, 0], [30, 3, 64, 1], [31, 2, 65, 0], [31, 6, 65, 4, "_default"], [31, 14, 65, 12], [31, 17, 65, 15, "plugin"], [31, 23, 65, 21], [32, 2, 66, 0, "exports"], [32, 9, 66, 7], [32, 10, 66, 8, "default"], [32, 17, 66, 15], [32, 20, 66, 18, "_default"], [32, 28, 66, 26], [33, 0, 66, 27], [33, 3]], "functionMap": {"names": ["<global>", "getPropKeys", "Object.keys.filter$argument_0", "serialize", "test"], "mappings": "AAA;oBCoB;gBCI,+BD;CDG;kBGC;OH4B;aIE,yCJ"}}, "type": "js/module"}]}