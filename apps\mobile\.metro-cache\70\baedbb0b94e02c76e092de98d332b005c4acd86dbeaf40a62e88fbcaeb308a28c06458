{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 36, "index": 50}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../createAnimatedComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 52}, "end": {"line": 4, "column": 69, "index": 121}}], "key": "e2Y7i0GjZ0FYhc0zsmE7V0rtFCw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.AnimatedView = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _createAnimatedComponent = require(_dependencyMap[1], \"../createAnimatedComponent\");\n  // Since createAnimatedComponent return type is ComponentClass that has the props of the argument,\n  // but not things like NativeMethods, etc. we need to add them manually by extending the type.\n\n  var AnimatedView = exports.AnimatedView = (0, _createAnimatedComponent.createAnimatedComponent)(_reactNative.View);\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "AnimatedView"], [7, 22, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_reactNative"], [8, 18, 2, 0], [8, 21, 2, 0, "require"], [8, 28, 2, 0], [8, 29, 2, 0, "_dependencyMap"], [8, 43, 2, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_createAnimatedComponent"], [9, 30, 4, 0], [9, 33, 4, 0, "require"], [9, 40, 4, 0], [9, 41, 4, 0, "_dependencyMap"], [9, 55, 4, 0], [10, 2, 6, 0], [11, 2, 7, 0], [13, 2, 12, 7], [13, 6, 12, 13, "AnimatedView"], [13, 18, 12, 25], [13, 21, 12, 25, "exports"], [13, 28, 12, 25], [13, 29, 12, 25, "AnimatedView"], [13, 41, 12, 25], [13, 44, 12, 28], [13, 48, 12, 28, "createAnimatedComponent"], [13, 96, 12, 51], [13, 98, 12, 52, "View"], [13, 115, 12, 56], [13, 116, 12, 57], [14, 0, 12, 58], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}