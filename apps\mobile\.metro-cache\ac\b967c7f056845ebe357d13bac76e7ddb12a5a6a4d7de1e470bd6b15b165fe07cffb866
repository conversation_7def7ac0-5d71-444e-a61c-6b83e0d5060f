{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../../Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 92}}], "key": "jzP+LUi0+8ZCeIUw7GN35c9PLT4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 51, "column": 0}, "end": {"line": 53, "column": 32}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 51, "column": 0}, "end": {"line": 53, "column": 32}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"../../../../Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var nativeComponentName = 'RCTActivityIndicatorView';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RCTActivityIndicatorView\",\n    validAttributes: {\n      hidesWhenStopped: true,\n      animating: true,\n      color: {\n        process: require(_dependencyMap[3], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      size: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 22, "map": [[7, 2, 16, 0], [7, 6, 16, 0, "_codegenNativeComponent"], [7, 29, 16, 0], [7, 32, 16, 0, "_interopRequireDefault"], [7, 54, 16, 0], [7, 55, 16, 0, "require"], [7, 62, 16, 0], [7, 63, 16, 0, "_dependencyMap"], [7, 77, 16, 0], [8, 2, 51, 0], [8, 6, 51, 0, "NativeComponentRegistry"], [8, 29, 53, 32], [8, 32, 51, 0, "require"], [8, 39, 53, 32], [8, 40, 53, 32, "_dependencyMap"], [8, 54, 53, 32], [8, 123, 53, 31], [8, 124, 53, 32], [9, 2, 51, 0], [9, 6, 51, 0, "nativeComponentName"], [9, 25, 53, 32], [9, 28, 51, 0], [9, 54, 53, 32], [10, 2, 51, 0], [10, 6, 51, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 53, 32], [10, 31, 53, 32, "exports"], [10, 38, 53, 32], [10, 39, 53, 32, "__INTERNAL_VIEW_CONFIG"], [10, 61, 53, 32], [10, 64, 51, 0], [11, 4, 51, 0, "uiViewClassName"], [11, 19, 53, 32], [11, 21, 51, 0], [11, 47, 53, 32], [12, 4, 51, 0, "validAttributes"], [12, 19, 53, 32], [12, 21, 51, 0], [13, 6, 51, 0, "hidesWhenStopped"], [13, 22, 53, 32], [13, 24, 51, 0], [13, 28, 53, 32], [14, 6, 51, 0, "animating"], [14, 15, 53, 32], [14, 17, 51, 0], [14, 21, 53, 32], [15, 6, 51, 0, "color"], [15, 11, 53, 32], [15, 13, 51, 0], [16, 8, 51, 0, "process"], [16, 15, 53, 32], [16, 17, 51, 0, "require"], [16, 24, 53, 32], [16, 25, 53, 32, "_dependencyMap"], [16, 39, 53, 32], [16, 92, 53, 31], [16, 93, 53, 32], [16, 94, 51, 0, "default"], [17, 6, 53, 31], [17, 7, 53, 32], [18, 6, 51, 0, "size"], [18, 10, 53, 32], [18, 12, 51, 0], [19, 4, 53, 31], [20, 2, 53, 31], [20, 3, 53, 32], [21, 2, 53, 32], [21, 6, 53, 32, "_default"], [21, 14, 53, 32], [21, 17, 53, 32, "exports"], [21, 24, 53, 32], [21, 25, 53, 32, "default"], [21, 32, 53, 32], [21, 35, 51, 0, "NativeComponentRegistry"], [21, 58, 53, 32], [21, 59, 51, 0, "get"], [21, 62, 53, 32], [21, 63, 51, 0, "nativeComponentName"], [21, 82, 53, 32], [21, 84, 51, 0], [21, 90, 51, 0, "__INTERNAL_VIEW_CONFIG"], [21, 112, 53, 31], [21, 113, 53, 32], [22, 0, 53, 32], [22, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}