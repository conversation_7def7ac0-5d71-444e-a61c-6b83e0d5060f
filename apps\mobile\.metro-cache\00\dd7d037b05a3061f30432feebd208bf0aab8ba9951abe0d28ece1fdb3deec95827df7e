{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../src/private/featureflags/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 98}}], "key": "fdTx5edELD8GYD7vaakWfKKte1Y=", "exportNames": ["*"]}}, {"name": "../../src/private/featureflags/specs/NativeReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 111}}], "key": "NejBJTGtZ8egYXexrx+ej/SB8V4=", "exportNames": ["*"]}}, {"name": "../Components/View/ReactNativeStyleAttributes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 87}}], "key": "LRZpG48GR8owQQPluG+E1VB5zh8=", "exportNames": ["*"]}}, {"name": "./ViewConfigIgnore", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 71}}], "key": "GOS7wR7hJbEacSQhF8FI1Fg9tiM=", "exportNames": ["*"]}}, {"name": "../StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 168, "column": 29}, "end": {"line": 168, "column": 66}}, {"start": {"line": 192, "column": 25}, "end": {"line": 192, "column": 62}}, {"start": {"line": 282, "column": 26}, "end": {"line": 282, "column": 63}}, {"start": {"line": 339, "column": 13}, "end": {"line": 339, "column": 50}}, {"start": {"line": 342, "column": 13}, "end": {"line": 342, "column": 50}}, {"start": {"line": 345, "column": 13}, "end": {"line": 345, "column": 50}}, {"start": {"line": 348, "column": 13}, "end": {"line": 348, "column": 50}}, {"start": {"line": 351, "column": 13}, "end": {"line": 351, "column": 50}}, {"start": {"line": 354, "column": 13}, "end": {"line": 354, "column": 50}}, {"start": {"line": 357, "column": 13}, "end": {"line": 357, "column": 50}}, {"start": {"line": 360, "column": 13}, "end": {"line": 360, "column": 50}}, {"start": {"line": 363, "column": 13}, "end": {"line": 363, "column": 50}}, {"start": {"line": 366, "column": 13}, "end": {"line": 366, "column": 50}}], "key": "I0Lk++/6Upr1uZbth/i3RrMPl94=", "exportNames": ["*"]}}, {"name": "../StyleSheet/processBackgroundImage", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 172, "column": 13}, "end": {"line": 172, "column": 60}}], "key": "6Dhrs6ZN1v/lbm52j81jml/TcnQ=", "exportNames": ["*"]}}, {"name": "../StyleSheet/processBoxShadow", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 179, "column": 19}, "end": {"line": 179, "column": 60}}], "key": "Y9zjfv4IO0Tw1qs0sMCmU4vk3IE=", "exportNames": ["*"]}}, {"name": "../StyleSheet/processFilter", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 186, "column": 19}, "end": {"line": 186, "column": 57}}], "key": "PH3OhIHU7mHt5nNvgvnyCytfZGA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var ReactNativeFeatureFlags = _interopRequireWildcard(require(_dependencyMap[1], \"../../src/private/featureflags/ReactNativeFeatureFlags\"));\n  var _NativeReactNativeFeatureFlags = _interopRequireDefault(require(_dependencyMap[2], \"../../src/private/featureflags/specs/NativeReactNativeFeatureFlags\"));\n  var _ReactNativeStyleAttributes = _interopRequireDefault(require(_dependencyMap[3], \"../Components/View/ReactNativeStyleAttributes\"));\n  var _ViewConfigIgnore = require(_dependencyMap[4], \"./ViewConfigIgnore\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var bubblingEventTypes = {\n    topChange: {\n      phasedRegistrationNames: {\n        captured: 'onChangeCapture',\n        bubbled: 'onChange'\n      }\n    },\n    topSelect: {\n      phasedRegistrationNames: {\n        captured: 'onSelectCapture',\n        bubbled: 'onSelect'\n      }\n    },\n    topTouchEnd: {\n      phasedRegistrationNames: {\n        captured: 'onTouchEndCapture',\n        bubbled: 'onTouchEnd'\n      }\n    },\n    topTouchCancel: {\n      phasedRegistrationNames: {\n        captured: 'onTouchCancelCapture',\n        bubbled: 'onTouchCancel'\n      }\n    },\n    topTouchStart: {\n      phasedRegistrationNames: {\n        captured: 'onTouchStartCapture',\n        bubbled: 'onTouchStart'\n      }\n    },\n    topTouchMove: {\n      phasedRegistrationNames: {\n        captured: 'onTouchMoveCapture',\n        bubbled: 'onTouchMove'\n      }\n    },\n    topPointerCancel: {\n      phasedRegistrationNames: {\n        captured: 'onPointerCancelCapture',\n        bubbled: 'onPointerCancel'\n      }\n    },\n    topPointerDown: {\n      phasedRegistrationNames: {\n        captured: 'onPointerDownCapture',\n        bubbled: 'onPointerDown'\n      }\n    },\n    topPointerEnter: {\n      phasedRegistrationNames: {\n        captured: 'onPointerEnterCapture',\n        bubbled: 'onPointerEnter',\n        skipBubbling: true\n      }\n    },\n    topPointerLeave: {\n      phasedRegistrationNames: {\n        captured: 'onPointerLeaveCapture',\n        bubbled: 'onPointerLeave',\n        skipBubbling: true\n      }\n    },\n    topPointerMove: {\n      phasedRegistrationNames: {\n        captured: 'onPointerMoveCapture',\n        bubbled: 'onPointerMove'\n      }\n    },\n    topPointerUp: {\n      phasedRegistrationNames: {\n        captured: 'onPointerUpCapture',\n        bubbled: 'onPointerUp'\n      }\n    },\n    topPointerOut: {\n      phasedRegistrationNames: {\n        captured: 'onPointerOutCapture',\n        bubbled: 'onPointerOut'\n      }\n    },\n    topPointerOver: {\n      phasedRegistrationNames: {\n        captured: 'onPointerOverCapture',\n        bubbled: 'onPointerOver'\n      }\n    },\n    topClick: {\n      phasedRegistrationNames: {\n        captured: 'onClickCapture',\n        bubbled: 'onClick'\n      }\n    }\n  };\n  var directEventTypes = {\n    topAccessibilityAction: {\n      registrationName: 'onAccessibilityAction'\n    },\n    onGestureHandlerEvent: (0, _ViewConfigIgnore.DynamicallyInjectedByGestureHandler)({\n      registrationName: 'onGestureHandlerEvent'\n    }),\n    onGestureHandlerStateChange: (0, _ViewConfigIgnore.DynamicallyInjectedByGestureHandler)({\n      registrationName: 'onGestureHandlerStateChange'\n    }),\n    topContentSizeChange: {\n      registrationName: 'onContentSizeChange'\n    },\n    topScrollBeginDrag: {\n      registrationName: 'onScrollBeginDrag'\n    },\n    topMessage: {\n      registrationName: 'onMessage'\n    },\n    topSelectionChange: {\n      registrationName: 'onSelectionChange'\n    },\n    topLoadingFinish: {\n      registrationName: 'onLoadingFinish'\n    },\n    topMomentumScrollEnd: {\n      registrationName: 'onMomentumScrollEnd'\n    },\n    topLoadingStart: {\n      registrationName: 'onLoadingStart'\n    },\n    topLoadingError: {\n      registrationName: 'onLoadingError'\n    },\n    topMomentumScrollBegin: {\n      registrationName: 'onMomentumScrollBegin'\n    },\n    topScrollEndDrag: {\n      registrationName: 'onScrollEndDrag'\n    },\n    topScroll: {\n      registrationName: 'onScroll'\n    },\n    topLayout: {\n      registrationName: 'onLayout'\n    }\n  };\n  var validAttributesForNonEventProps = {\n    backgroundColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    transform: true,\n    transformOrigin: true,\n    experimental_backgroundImage: {\n      process: require(_dependencyMap[6], \"../StyleSheet/processBackgroundImage\").default\n    },\n    boxShadow: _NativeReactNativeFeatureFlags.default != null && ReactNativeFeatureFlags.enableNativeCSSParsing() ? true : {\n      process: require(_dependencyMap[7], \"../StyleSheet/processBoxShadow\").default\n    },\n    filter: _NativeReactNativeFeatureFlags.default != null && ReactNativeFeatureFlags.enableNativeCSSParsing() ? true : {\n      process: require(_dependencyMap[8], \"../StyleSheet/processFilter\").default\n    },\n    mixBlendMode: true,\n    isolation: true,\n    opacity: true,\n    elevation: true,\n    shadowColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    zIndex: true,\n    renderToHardwareTextureAndroid: true,\n    testID: true,\n    nativeID: true,\n    accessibilityLabelledBy: true,\n    accessibilityLabel: true,\n    accessibilityHint: true,\n    accessibilityRole: true,\n    accessibilityCollection: true,\n    accessibilityCollectionItem: true,\n    accessibilityState: true,\n    accessibilityActions: true,\n    accessibilityValue: true,\n    importantForAccessibility: true,\n    role: true,\n    rotation: true,\n    scaleX: true,\n    scaleY: true,\n    translateX: true,\n    translateY: true,\n    accessibilityLiveRegion: true,\n    width: true,\n    minWidth: true,\n    collapsable: true,\n    collapsableChildren: true,\n    maxWidth: true,\n    height: true,\n    minHeight: true,\n    maxHeight: true,\n    flex: true,\n    flexGrow: true,\n    rowGap: true,\n    columnGap: true,\n    gap: true,\n    flexShrink: true,\n    flexBasis: true,\n    aspectRatio: true,\n    flexDirection: true,\n    flexWrap: true,\n    alignSelf: true,\n    alignItems: true,\n    alignContent: true,\n    justifyContent: true,\n    overflow: true,\n    display: true,\n    boxSizing: true,\n    margin: true,\n    marginBlock: true,\n    marginBlockEnd: true,\n    marginBlockStart: true,\n    marginBottom: true,\n    marginEnd: true,\n    marginHorizontal: true,\n    marginInline: true,\n    marginInlineEnd: true,\n    marginInlineStart: true,\n    marginLeft: true,\n    marginRight: true,\n    marginStart: true,\n    marginTop: true,\n    marginVertical: true,\n    padding: true,\n    paddingBlock: true,\n    paddingBlockEnd: true,\n    paddingBlockStart: true,\n    paddingBottom: true,\n    paddingEnd: true,\n    paddingHorizontal: true,\n    paddingInline: true,\n    paddingInlineEnd: true,\n    paddingInlineStart: true,\n    paddingLeft: true,\n    paddingRight: true,\n    paddingStart: true,\n    paddingTop: true,\n    paddingVertical: true,\n    borderWidth: true,\n    borderStartWidth: true,\n    borderEndWidth: true,\n    borderTopWidth: true,\n    borderBottomWidth: true,\n    borderLeftWidth: true,\n    borderRightWidth: true,\n    outlineColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    outlineOffset: true,\n    outlineStyle: true,\n    outlineWidth: true,\n    start: true,\n    end: true,\n    left: true,\n    right: true,\n    top: true,\n    bottom: true,\n    inset: true,\n    insetBlock: true,\n    insetBlockEnd: true,\n    insetBlockStart: true,\n    insetInline: true,\n    insetInlineEnd: true,\n    insetInlineStart: true,\n    position: true,\n    style: _ReactNativeStyleAttributes.default,\n    removeClippedSubviews: true,\n    accessible: true,\n    hasTVPreferredFocus: true,\n    nextFocusDown: true,\n    nextFocusForward: true,\n    nextFocusLeft: true,\n    nextFocusRight: true,\n    nextFocusUp: true,\n    borderRadius: true,\n    borderTopLeftRadius: true,\n    borderTopRightRadius: true,\n    borderBottomRightRadius: true,\n    borderBottomLeftRadius: true,\n    borderTopStartRadius: true,\n    borderTopEndRadius: true,\n    borderBottomStartRadius: true,\n    borderBottomEndRadius: true,\n    borderEndEndRadius: true,\n    borderEndStartRadius: true,\n    borderStartEndRadius: true,\n    borderStartStartRadius: true,\n    borderStyle: true,\n    hitSlop: true,\n    pointerEvents: true,\n    nativeBackgroundAndroid: true,\n    nativeForegroundAndroid: true,\n    needsOffscreenAlphaCompositing: true,\n    borderColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderLeftColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderRightColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderTopColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderBottomColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderStartColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderEndColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderBlockColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderBlockEndColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderBlockStartColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    focusable: true,\n    backfaceVisibility: true\n  };\n  var validAttributesForEventProps = {\n    onLayout: true,\n    onMoveShouldSetResponder: true,\n    onMoveShouldSetResponderCapture: true,\n    onStartShouldSetResponder: true,\n    onStartShouldSetResponderCapture: true,\n    onResponderGrant: true,\n    onResponderReject: true,\n    onResponderStart: true,\n    onResponderEnd: true,\n    onResponderRelease: true,\n    onResponderMove: true,\n    onResponderTerminate: true,\n    onResponderTerminationRequest: true,\n    onShouldBlockNativeResponder: true,\n    onTouchStart: true,\n    onTouchMove: true,\n    onTouchEnd: true,\n    onTouchCancel: true,\n    onClick: true,\n    onClickCapture: true,\n    onPointerEnter: true,\n    onPointerEnterCapture: true,\n    onPointerLeave: true,\n    onPointerLeaveCapture: true,\n    onPointerMove: true,\n    onPointerMoveCapture: true,\n    onPointerOut: true,\n    onPointerOutCapture: true,\n    onPointerOver: true,\n    onPointerOverCapture: true\n  };\n  var PlatformBaseViewConfigAndroid = {\n    directEventTypes,\n    bubblingEventTypes,\n    validAttributes: {\n      ...validAttributesForNonEventProps,\n      ...validAttributesForEventProps\n    }\n  };\n  var _default = exports.default = PlatformBaseViewConfigAndroid;\n});", "lineCount": 380, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "ReactNativeFeatureFlags"], [7, 29, 13, 0], [7, 32, 13, 0, "_interopRequireWildcard"], [7, 55, 13, 0], [7, 56, 13, 0, "require"], [7, 63, 13, 0], [7, 64, 13, 0, "_dependencyMap"], [7, 78, 13, 0], [8, 2, 14, 0], [8, 6, 14, 0, "_NativeReactNativeFeatureFlags"], [8, 36, 14, 0], [8, 39, 14, 0, "_interopRequireDefault"], [8, 61, 14, 0], [8, 62, 14, 0, "require"], [8, 69, 14, 0], [8, 70, 14, 0, "_dependencyMap"], [8, 84, 14, 0], [9, 2, 15, 0], [9, 6, 15, 0, "_ReactNativeStyleAttributes"], [9, 33, 15, 0], [9, 36, 15, 0, "_interopRequireDefault"], [9, 58, 15, 0], [9, 59, 15, 0, "require"], [9, 66, 15, 0], [9, 67, 15, 0, "_dependencyMap"], [9, 81, 15, 0], [10, 2, 16, 0], [10, 6, 16, 0, "_ViewConfigIgnore"], [10, 23, 16, 0], [10, 26, 16, 0, "require"], [10, 33, 16, 0], [10, 34, 16, 0, "_dependencyMap"], [10, 48, 16, 0], [11, 2, 16, 71], [11, 11, 16, 71, "_interopRequireWildcard"], [11, 35, 16, 71, "e"], [11, 36, 16, 71], [11, 38, 16, 71, "t"], [11, 39, 16, 71], [11, 68, 16, 71, "WeakMap"], [11, 75, 16, 71], [11, 81, 16, 71, "r"], [11, 82, 16, 71], [11, 89, 16, 71, "WeakMap"], [11, 96, 16, 71], [11, 100, 16, 71, "n"], [11, 101, 16, 71], [11, 108, 16, 71, "WeakMap"], [11, 115, 16, 71], [11, 127, 16, 71, "_interopRequireWildcard"], [11, 150, 16, 71], [11, 162, 16, 71, "_interopRequireWildcard"], [11, 163, 16, 71, "e"], [11, 164, 16, 71], [11, 166, 16, 71, "t"], [11, 167, 16, 71], [11, 176, 16, 71, "t"], [11, 177, 16, 71], [11, 181, 16, 71, "e"], [11, 182, 16, 71], [11, 186, 16, 71, "e"], [11, 187, 16, 71], [11, 188, 16, 71, "__esModule"], [11, 198, 16, 71], [11, 207, 16, 71, "e"], [11, 208, 16, 71], [11, 214, 16, 71, "o"], [11, 215, 16, 71], [11, 217, 16, 71, "i"], [11, 218, 16, 71], [11, 220, 16, 71, "f"], [11, 221, 16, 71], [11, 226, 16, 71, "__proto__"], [11, 235, 16, 71], [11, 243, 16, 71, "default"], [11, 250, 16, 71], [11, 252, 16, 71, "e"], [11, 253, 16, 71], [11, 270, 16, 71, "e"], [11, 271, 16, 71], [11, 294, 16, 71, "e"], [11, 295, 16, 71], [11, 320, 16, 71, "e"], [11, 321, 16, 71], [11, 330, 16, 71, "f"], [11, 331, 16, 71], [11, 337, 16, 71, "o"], [11, 338, 16, 71], [11, 341, 16, 71, "t"], [11, 342, 16, 71], [11, 345, 16, 71, "n"], [11, 346, 16, 71], [11, 349, 16, 71, "r"], [11, 350, 16, 71], [11, 358, 16, 71, "o"], [11, 359, 16, 71], [11, 360, 16, 71, "has"], [11, 363, 16, 71], [11, 364, 16, 71, "e"], [11, 365, 16, 71], [11, 375, 16, 71, "o"], [11, 376, 16, 71], [11, 377, 16, 71, "get"], [11, 380, 16, 71], [11, 381, 16, 71, "e"], [11, 382, 16, 71], [11, 385, 16, 71, "o"], [11, 386, 16, 71], [11, 387, 16, 71, "set"], [11, 390, 16, 71], [11, 391, 16, 71, "e"], [11, 392, 16, 71], [11, 394, 16, 71, "f"], [11, 395, 16, 71], [11, 409, 16, 71, "_t"], [11, 411, 16, 71], [11, 415, 16, 71, "e"], [11, 416, 16, 71], [11, 432, 16, 71, "_t"], [11, 434, 16, 71], [11, 441, 16, 71, "hasOwnProperty"], [11, 455, 16, 71], [11, 456, 16, 71, "call"], [11, 460, 16, 71], [11, 461, 16, 71, "e"], [11, 462, 16, 71], [11, 464, 16, 71, "_t"], [11, 466, 16, 71], [11, 473, 16, 71, "i"], [11, 474, 16, 71], [11, 478, 16, 71, "o"], [11, 479, 16, 71], [11, 482, 16, 71, "Object"], [11, 488, 16, 71], [11, 489, 16, 71, "defineProperty"], [11, 503, 16, 71], [11, 508, 16, 71, "Object"], [11, 514, 16, 71], [11, 515, 16, 71, "getOwnPropertyDescriptor"], [11, 539, 16, 71], [11, 540, 16, 71, "e"], [11, 541, 16, 71], [11, 543, 16, 71, "_t"], [11, 545, 16, 71], [11, 552, 16, 71, "i"], [11, 553, 16, 71], [11, 554, 16, 71, "get"], [11, 557, 16, 71], [11, 561, 16, 71, "i"], [11, 562, 16, 71], [11, 563, 16, 71, "set"], [11, 566, 16, 71], [11, 570, 16, 71, "o"], [11, 571, 16, 71], [11, 572, 16, 71, "f"], [11, 573, 16, 71], [11, 575, 16, 71, "_t"], [11, 577, 16, 71], [11, 579, 16, 71, "i"], [11, 580, 16, 71], [11, 584, 16, 71, "f"], [11, 585, 16, 71], [11, 586, 16, 71, "_t"], [11, 588, 16, 71], [11, 592, 16, 71, "e"], [11, 593, 16, 71], [11, 594, 16, 71, "_t"], [11, 596, 16, 71], [11, 607, 16, 71, "f"], [11, 608, 16, 71], [11, 613, 16, 71, "e"], [11, 614, 16, 71], [11, 616, 16, 71, "t"], [11, 617, 16, 71], [12, 2, 18, 0], [12, 6, 18, 6, "bubblingEventTypes"], [12, 24, 18, 24], [12, 27, 18, 27], [13, 4, 20, 2, "topChange"], [13, 13, 20, 11], [13, 15, 20, 13], [14, 6, 21, 4, "phasedRegistrationNames"], [14, 29, 21, 27], [14, 31, 21, 29], [15, 8, 22, 6, "captured"], [15, 16, 22, 14], [15, 18, 22, 16], [15, 35, 22, 33], [16, 8, 23, 6, "bubbled"], [16, 15, 23, 13], [16, 17, 23, 15], [17, 6, 24, 4], [18, 4, 25, 2], [18, 5, 25, 3], [19, 4, 26, 2, "topSelect"], [19, 13, 26, 11], [19, 15, 26, 13], [20, 6, 27, 4, "phasedRegistrationNames"], [20, 29, 27, 27], [20, 31, 27, 29], [21, 8, 28, 6, "captured"], [21, 16, 28, 14], [21, 18, 28, 16], [21, 35, 28, 33], [22, 8, 29, 6, "bubbled"], [22, 15, 29, 13], [22, 17, 29, 15], [23, 6, 30, 4], [24, 4, 31, 2], [24, 5, 31, 3], [25, 4, 32, 2, "topTouchEnd"], [25, 15, 32, 13], [25, 17, 32, 15], [26, 6, 33, 4, "phasedRegistrationNames"], [26, 29, 33, 27], [26, 31, 33, 29], [27, 8, 34, 6, "captured"], [27, 16, 34, 14], [27, 18, 34, 16], [27, 37, 34, 35], [28, 8, 35, 6, "bubbled"], [28, 15, 35, 13], [28, 17, 35, 15], [29, 6, 36, 4], [30, 4, 37, 2], [30, 5, 37, 3], [31, 4, 38, 2, "topTouchCancel"], [31, 18, 38, 16], [31, 20, 38, 18], [32, 6, 39, 4, "phasedRegistrationNames"], [32, 29, 39, 27], [32, 31, 39, 29], [33, 8, 40, 6, "captured"], [33, 16, 40, 14], [33, 18, 40, 16], [33, 40, 40, 38], [34, 8, 41, 6, "bubbled"], [34, 15, 41, 13], [34, 17, 41, 15], [35, 6, 42, 4], [36, 4, 43, 2], [36, 5, 43, 3], [37, 4, 44, 2, "topTouchStart"], [37, 17, 44, 15], [37, 19, 44, 17], [38, 6, 45, 4, "phasedRegistrationNames"], [38, 29, 45, 27], [38, 31, 45, 29], [39, 8, 46, 6, "captured"], [39, 16, 46, 14], [39, 18, 46, 16], [39, 39, 46, 37], [40, 8, 47, 6, "bubbled"], [40, 15, 47, 13], [40, 17, 47, 15], [41, 6, 48, 4], [42, 4, 49, 2], [42, 5, 49, 3], [43, 4, 50, 2, "topTouchMove"], [43, 16, 50, 14], [43, 18, 50, 16], [44, 6, 51, 4, "phasedRegistrationNames"], [44, 29, 51, 27], [44, 31, 51, 29], [45, 8, 52, 6, "captured"], [45, 16, 52, 14], [45, 18, 52, 16], [45, 38, 52, 36], [46, 8, 53, 6, "bubbled"], [46, 15, 53, 13], [46, 17, 53, 15], [47, 6, 54, 4], [48, 4, 55, 2], [48, 5, 55, 3], [49, 4, 58, 2, "topPointerCancel"], [49, 20, 58, 18], [49, 22, 58, 20], [50, 6, 59, 4, "phasedRegistrationNames"], [50, 29, 59, 27], [50, 31, 59, 29], [51, 8, 60, 6, "captured"], [51, 16, 60, 14], [51, 18, 60, 16], [51, 42, 60, 40], [52, 8, 61, 6, "bubbled"], [52, 15, 61, 13], [52, 17, 61, 15], [53, 6, 62, 4], [54, 4, 63, 2], [54, 5, 63, 3], [55, 4, 64, 2, "topPointerDown"], [55, 18, 64, 16], [55, 20, 64, 18], [56, 6, 65, 4, "phasedRegistrationNames"], [56, 29, 65, 27], [56, 31, 65, 29], [57, 8, 66, 6, "captured"], [57, 16, 66, 14], [57, 18, 66, 16], [57, 40, 66, 38], [58, 8, 67, 6, "bubbled"], [58, 15, 67, 13], [58, 17, 67, 15], [59, 6, 68, 4], [60, 4, 69, 2], [60, 5, 69, 3], [61, 4, 70, 2, "topPointerEnter"], [61, 19, 70, 17], [61, 21, 70, 19], [62, 6, 71, 4, "phasedRegistrationNames"], [62, 29, 71, 27], [62, 31, 71, 29], [63, 8, 72, 6, "captured"], [63, 16, 72, 14], [63, 18, 72, 16], [63, 41, 72, 39], [64, 8, 73, 6, "bubbled"], [64, 15, 73, 13], [64, 17, 73, 15], [64, 33, 73, 31], [65, 8, 74, 6, "skipBubbling"], [65, 20, 74, 18], [65, 22, 74, 20], [66, 6, 75, 4], [67, 4, 76, 2], [67, 5, 76, 3], [68, 4, 77, 2, "topPointerLeave"], [68, 19, 77, 17], [68, 21, 77, 19], [69, 6, 78, 4, "phasedRegistrationNames"], [69, 29, 78, 27], [69, 31, 78, 29], [70, 8, 79, 6, "captured"], [70, 16, 79, 14], [70, 18, 79, 16], [70, 41, 79, 39], [71, 8, 80, 6, "bubbled"], [71, 15, 80, 13], [71, 17, 80, 15], [71, 33, 80, 31], [72, 8, 81, 6, "skipBubbling"], [72, 20, 81, 18], [72, 22, 81, 20], [73, 6, 82, 4], [74, 4, 83, 2], [74, 5, 83, 3], [75, 4, 84, 2, "topPointerMove"], [75, 18, 84, 16], [75, 20, 84, 18], [76, 6, 85, 4, "phasedRegistrationNames"], [76, 29, 85, 27], [76, 31, 85, 29], [77, 8, 86, 6, "captured"], [77, 16, 86, 14], [77, 18, 86, 16], [77, 40, 86, 38], [78, 8, 87, 6, "bubbled"], [78, 15, 87, 13], [78, 17, 87, 15], [79, 6, 88, 4], [80, 4, 89, 2], [80, 5, 89, 3], [81, 4, 90, 2, "topPointerUp"], [81, 16, 90, 14], [81, 18, 90, 16], [82, 6, 91, 4, "phasedRegistrationNames"], [82, 29, 91, 27], [82, 31, 91, 29], [83, 8, 92, 6, "captured"], [83, 16, 92, 14], [83, 18, 92, 16], [83, 38, 92, 36], [84, 8, 93, 6, "bubbled"], [84, 15, 93, 13], [84, 17, 93, 15], [85, 6, 94, 4], [86, 4, 95, 2], [86, 5, 95, 3], [87, 4, 96, 2, "topPointerOut"], [87, 17, 96, 15], [87, 19, 96, 17], [88, 6, 97, 4, "phasedRegistrationNames"], [88, 29, 97, 27], [88, 31, 97, 29], [89, 8, 98, 6, "captured"], [89, 16, 98, 14], [89, 18, 98, 16], [89, 39, 98, 37], [90, 8, 99, 6, "bubbled"], [90, 15, 99, 13], [90, 17, 99, 15], [91, 6, 100, 4], [92, 4, 101, 2], [92, 5, 101, 3], [93, 4, 102, 2, "topPointerOver"], [93, 18, 102, 16], [93, 20, 102, 18], [94, 6, 103, 4, "phasedRegistrationNames"], [94, 29, 103, 27], [94, 31, 103, 29], [95, 8, 104, 6, "captured"], [95, 16, 104, 14], [95, 18, 104, 16], [95, 40, 104, 38], [96, 8, 105, 6, "bubbled"], [96, 15, 105, 13], [96, 17, 105, 15], [97, 6, 106, 4], [98, 4, 107, 2], [98, 5, 107, 3], [99, 4, 108, 2, "topClick"], [99, 12, 108, 10], [99, 14, 108, 12], [100, 6, 109, 4, "phasedRegistrationNames"], [100, 29, 109, 27], [100, 31, 109, 29], [101, 8, 110, 6, "captured"], [101, 16, 110, 14], [101, 18, 110, 16], [101, 34, 110, 32], [102, 8, 111, 6, "bubbled"], [102, 15, 111, 13], [102, 17, 111, 15], [103, 6, 112, 4], [104, 4, 113, 2], [105, 2, 114, 0], [105, 3, 114, 1], [106, 2, 116, 0], [106, 6, 116, 6, "directEventTypes"], [106, 22, 116, 22], [106, 25, 116, 25], [107, 4, 117, 2, "topAccessibilityAction"], [107, 26, 117, 24], [107, 28, 117, 26], [108, 6, 118, 4, "registrationName"], [108, 22, 118, 20], [108, 24, 118, 22], [109, 4, 119, 2], [109, 5, 119, 3], [110, 4, 120, 2, "onGestureHandlerEvent"], [110, 25, 120, 23], [110, 27, 120, 25], [110, 31, 120, 25, "DynamicallyInjectedByGestureHandler"], [110, 84, 120, 60], [110, 86, 120, 61], [111, 6, 121, 4, "registrationName"], [111, 22, 121, 20], [111, 24, 121, 22], [112, 4, 122, 2], [112, 5, 122, 3], [112, 6, 122, 4], [113, 4, 123, 2, "onGestureHandlerStateChange"], [113, 31, 123, 29], [113, 33, 123, 31], [113, 37, 123, 31, "DynamicallyInjectedByGestureHandler"], [113, 90, 123, 66], [113, 92, 123, 67], [114, 6, 124, 4, "registrationName"], [114, 22, 124, 20], [114, 24, 124, 22], [115, 4, 125, 2], [115, 5, 125, 3], [115, 6, 125, 4], [116, 4, 128, 2, "topContentSizeChange"], [116, 24, 128, 22], [116, 26, 128, 24], [117, 6, 129, 4, "registrationName"], [117, 22, 129, 20], [117, 24, 129, 22], [118, 4, 130, 2], [118, 5, 130, 3], [119, 4, 131, 2, "topScrollBeginDrag"], [119, 22, 131, 20], [119, 24, 131, 22], [120, 6, 132, 4, "registrationName"], [120, 22, 132, 20], [120, 24, 132, 22], [121, 4, 133, 2], [121, 5, 133, 3], [122, 4, 134, 2, "topMessage"], [122, 14, 134, 12], [122, 16, 134, 14], [123, 6, 135, 4, "registrationName"], [123, 22, 135, 20], [123, 24, 135, 22], [124, 4, 136, 2], [124, 5, 136, 3], [125, 4, 137, 2, "topSelectionChange"], [125, 22, 137, 20], [125, 24, 137, 22], [126, 6, 138, 4, "registrationName"], [126, 22, 138, 20], [126, 24, 138, 22], [127, 4, 139, 2], [127, 5, 139, 3], [128, 4, 140, 2, "topLoadingFinish"], [128, 20, 140, 18], [128, 22, 140, 20], [129, 6, 141, 4, "registrationName"], [129, 22, 141, 20], [129, 24, 141, 22], [130, 4, 142, 2], [130, 5, 142, 3], [131, 4, 143, 2, "topMomentumScrollEnd"], [131, 24, 143, 22], [131, 26, 143, 24], [132, 6, 144, 4, "registrationName"], [132, 22, 144, 20], [132, 24, 144, 22], [133, 4, 145, 2], [133, 5, 145, 3], [134, 4, 146, 2, "topLoadingStart"], [134, 19, 146, 17], [134, 21, 146, 19], [135, 6, 147, 4, "registrationName"], [135, 22, 147, 20], [135, 24, 147, 22], [136, 4, 148, 2], [136, 5, 148, 3], [137, 4, 149, 2, "topLoadingError"], [137, 19, 149, 17], [137, 21, 149, 19], [138, 6, 150, 4, "registrationName"], [138, 22, 150, 20], [138, 24, 150, 22], [139, 4, 151, 2], [139, 5, 151, 3], [140, 4, 152, 2, "topMomentumScrollBegin"], [140, 26, 152, 24], [140, 28, 152, 26], [141, 6, 153, 4, "registrationName"], [141, 22, 153, 20], [141, 24, 153, 22], [142, 4, 154, 2], [142, 5, 154, 3], [143, 4, 155, 2, "topScrollEndDrag"], [143, 20, 155, 18], [143, 22, 155, 20], [144, 6, 156, 4, "registrationName"], [144, 22, 156, 20], [144, 24, 156, 22], [145, 4, 157, 2], [145, 5, 157, 3], [146, 4, 158, 2, "topScroll"], [146, 13, 158, 11], [146, 15, 158, 13], [147, 6, 159, 4, "registrationName"], [147, 22, 159, 20], [147, 24, 159, 22], [148, 4, 160, 2], [148, 5, 160, 3], [149, 4, 161, 2, "topLayout"], [149, 13, 161, 11], [149, 15, 161, 13], [150, 6, 162, 4, "registrationName"], [150, 22, 162, 20], [150, 24, 162, 22], [151, 4, 163, 2], [152, 2, 164, 0], [152, 3, 164, 1], [153, 2, 166, 0], [153, 6, 166, 6, "validAttributesForNonEventProps"], [153, 37, 166, 37], [153, 40, 166, 40], [154, 4, 168, 2, "backgroundColor"], [154, 19, 168, 17], [154, 21, 168, 19], [155, 6, 168, 20, "process"], [155, 13, 168, 27], [155, 15, 168, 29, "require"], [155, 22, 168, 36], [155, 23, 168, 36, "_dependencyMap"], [155, 37, 168, 36], [155, 70, 168, 65], [155, 71, 168, 66], [155, 72, 168, 67, "default"], [156, 4, 168, 74], [156, 5, 168, 75], [157, 4, 169, 2, "transform"], [157, 13, 169, 11], [157, 15, 169, 13], [157, 19, 169, 17], [158, 4, 170, 2, "transform<PERSON><PERSON>in"], [158, 19, 170, 17], [158, 21, 170, 19], [158, 25, 170, 23], [159, 4, 171, 2, "experimental_backgroundImage"], [159, 32, 171, 30], [159, 34, 171, 32], [160, 6, 172, 4, "process"], [160, 13, 172, 11], [160, 15, 172, 13, "require"], [160, 22, 172, 20], [160, 23, 172, 20, "_dependencyMap"], [160, 37, 172, 20], [160, 80, 172, 59], [160, 81, 172, 60], [160, 82, 172, 61, "default"], [161, 4, 173, 2], [161, 5, 173, 3], [162, 4, 174, 2, "boxShadow"], [162, 13, 174, 11], [162, 15, 175, 4, "NativeReactNativeFeatureFlags"], [162, 53, 175, 33], [162, 57, 175, 37], [162, 61, 175, 41], [162, 65, 176, 4, "ReactNativeFeatureFlags"], [162, 88, 176, 27], [162, 89, 176, 28, "enableNativeCSSParsing"], [162, 111, 176, 50], [162, 112, 176, 51], [162, 113, 176, 52], [162, 116, 177, 8], [162, 120, 177, 12], [162, 123, 178, 8], [163, 6, 179, 10, "process"], [163, 13, 179, 17], [163, 15, 179, 19, "require"], [163, 22, 179, 26], [163, 23, 179, 26, "_dependencyMap"], [163, 37, 179, 26], [163, 74, 179, 59], [163, 75, 179, 60], [163, 76, 179, 61, "default"], [164, 4, 180, 8], [164, 5, 180, 9], [165, 4, 181, 2, "filter"], [165, 10, 181, 8], [165, 12, 182, 4, "NativeReactNativeFeatureFlags"], [165, 50, 182, 33], [165, 54, 182, 37], [165, 58, 182, 41], [165, 62, 183, 4, "ReactNativeFeatureFlags"], [165, 85, 183, 27], [165, 86, 183, 28, "enableNativeCSSParsing"], [165, 108, 183, 50], [165, 109, 183, 51], [165, 110, 183, 52], [165, 113, 184, 8], [165, 117, 184, 12], [165, 120, 185, 8], [166, 6, 186, 10, "process"], [166, 13, 186, 17], [166, 15, 186, 19, "require"], [166, 22, 186, 26], [166, 23, 186, 26, "_dependencyMap"], [166, 37, 186, 26], [166, 71, 186, 56], [166, 72, 186, 57], [166, 73, 186, 58, "default"], [167, 4, 187, 8], [167, 5, 187, 9], [168, 4, 188, 2, "mixBlendMode"], [168, 16, 188, 14], [168, 18, 188, 16], [168, 22, 188, 20], [169, 4, 189, 2, "isolation"], [169, 13, 189, 11], [169, 15, 189, 13], [169, 19, 189, 17], [170, 4, 190, 2, "opacity"], [170, 11, 190, 9], [170, 13, 190, 11], [170, 17, 190, 15], [171, 4, 191, 2, "elevation"], [171, 13, 191, 11], [171, 15, 191, 13], [171, 19, 191, 17], [172, 4, 192, 2, "shadowColor"], [172, 15, 192, 13], [172, 17, 192, 15], [173, 6, 192, 16, "process"], [173, 13, 192, 23], [173, 15, 192, 25, "require"], [173, 22, 192, 32], [173, 23, 192, 32, "_dependencyMap"], [173, 37, 192, 32], [173, 70, 192, 61], [173, 71, 192, 62], [173, 72, 192, 63, "default"], [174, 4, 192, 70], [174, 5, 192, 71], [175, 4, 193, 2, "zIndex"], [175, 10, 193, 8], [175, 12, 193, 10], [175, 16, 193, 14], [176, 4, 194, 2, "renderToHardwareTextureAndroid"], [176, 34, 194, 32], [176, 36, 194, 34], [176, 40, 194, 38], [177, 4, 195, 2, "testID"], [177, 10, 195, 8], [177, 12, 195, 10], [177, 16, 195, 14], [178, 4, 196, 2, "nativeID"], [178, 12, 196, 10], [178, 14, 196, 12], [178, 18, 196, 16], [179, 4, 197, 2, "accessibilityLabelledBy"], [179, 27, 197, 25], [179, 29, 197, 27], [179, 33, 197, 31], [180, 4, 198, 2, "accessibilityLabel"], [180, 22, 198, 20], [180, 24, 198, 22], [180, 28, 198, 26], [181, 4, 199, 2, "accessibilityHint"], [181, 21, 199, 19], [181, 23, 199, 21], [181, 27, 199, 25], [182, 4, 200, 2, "accessibilityRole"], [182, 21, 200, 19], [182, 23, 200, 21], [182, 27, 200, 25], [183, 4, 201, 2, "accessibilityCollection"], [183, 27, 201, 25], [183, 29, 201, 27], [183, 33, 201, 31], [184, 4, 202, 2, "accessibilityCollectionItem"], [184, 31, 202, 29], [184, 33, 202, 31], [184, 37, 202, 35], [185, 4, 203, 2, "accessibilityState"], [185, 22, 203, 20], [185, 24, 203, 22], [185, 28, 203, 26], [186, 4, 204, 2, "accessibilityActions"], [186, 24, 204, 22], [186, 26, 204, 24], [186, 30, 204, 28], [187, 4, 205, 2, "accessibilityValue"], [187, 22, 205, 20], [187, 24, 205, 22], [187, 28, 205, 26], [188, 4, 206, 2, "importantForAccessibility"], [188, 29, 206, 27], [188, 31, 206, 29], [188, 35, 206, 33], [189, 4, 207, 2, "role"], [189, 8, 207, 6], [189, 10, 207, 8], [189, 14, 207, 12], [190, 4, 208, 2, "rotation"], [190, 12, 208, 10], [190, 14, 208, 12], [190, 18, 208, 16], [191, 4, 209, 2, "scaleX"], [191, 10, 209, 8], [191, 12, 209, 10], [191, 16, 209, 14], [192, 4, 210, 2, "scaleY"], [192, 10, 210, 8], [192, 12, 210, 10], [192, 16, 210, 14], [193, 4, 211, 2, "translateX"], [193, 14, 211, 12], [193, 16, 211, 14], [193, 20, 211, 18], [194, 4, 212, 2, "translateY"], [194, 14, 212, 12], [194, 16, 212, 14], [194, 20, 212, 18], [195, 4, 213, 2, "accessibilityLiveRegion"], [195, 27, 213, 25], [195, 29, 213, 27], [195, 33, 213, 31], [196, 4, 216, 2, "width"], [196, 9, 216, 7], [196, 11, 216, 9], [196, 15, 216, 13], [197, 4, 217, 2, "min<PERSON><PERSON><PERSON>"], [197, 12, 217, 10], [197, 14, 217, 12], [197, 18, 217, 16], [198, 4, 218, 2, "collapsable"], [198, 15, 218, 13], [198, 17, 218, 15], [198, 21, 218, 19], [199, 4, 219, 2, "collaps<PERSON><PERSON><PERSON><PERSON><PERSON>"], [199, 23, 219, 21], [199, 25, 219, 23], [199, 29, 219, 27], [200, 4, 220, 2, "max<PERSON><PERSON><PERSON>"], [200, 12, 220, 10], [200, 14, 220, 12], [200, 18, 220, 16], [201, 4, 221, 2, "height"], [201, 10, 221, 8], [201, 12, 221, 10], [201, 16, 221, 14], [202, 4, 222, 2, "minHeight"], [202, 13, 222, 11], [202, 15, 222, 13], [202, 19, 222, 17], [203, 4, 223, 2, "maxHeight"], [203, 13, 223, 11], [203, 15, 223, 13], [203, 19, 223, 17], [204, 4, 224, 2, "flex"], [204, 8, 224, 6], [204, 10, 224, 8], [204, 14, 224, 12], [205, 4, 225, 2, "flexGrow"], [205, 12, 225, 10], [205, 14, 225, 12], [205, 18, 225, 16], [206, 4, 226, 2, "rowGap"], [206, 10, 226, 8], [206, 12, 226, 10], [206, 16, 226, 14], [207, 4, 227, 2, "columnGap"], [207, 13, 227, 11], [207, 15, 227, 13], [207, 19, 227, 17], [208, 4, 228, 2, "gap"], [208, 7, 228, 5], [208, 9, 228, 7], [208, 13, 228, 11], [209, 4, 229, 2, "flexShrink"], [209, 14, 229, 12], [209, 16, 229, 14], [209, 20, 229, 18], [210, 4, 230, 2, "flexBasis"], [210, 13, 230, 11], [210, 15, 230, 13], [210, 19, 230, 17], [211, 4, 231, 2, "aspectRatio"], [211, 15, 231, 13], [211, 17, 231, 15], [211, 21, 231, 19], [212, 4, 232, 2, "flexDirection"], [212, 17, 232, 15], [212, 19, 232, 17], [212, 23, 232, 21], [213, 4, 233, 2, "flexWrap"], [213, 12, 233, 10], [213, 14, 233, 12], [213, 18, 233, 16], [214, 4, 234, 2, "alignSelf"], [214, 13, 234, 11], [214, 15, 234, 13], [214, 19, 234, 17], [215, 4, 235, 2, "alignItems"], [215, 14, 235, 12], [215, 16, 235, 14], [215, 20, 235, 18], [216, 4, 236, 2, "align<PERSON><PERSON><PERSON>"], [216, 16, 236, 14], [216, 18, 236, 16], [216, 22, 236, 20], [217, 4, 237, 2, "justifyContent"], [217, 18, 237, 16], [217, 20, 237, 18], [217, 24, 237, 22], [218, 4, 238, 2, "overflow"], [218, 12, 238, 10], [218, 14, 238, 12], [218, 18, 238, 16], [219, 4, 239, 2, "display"], [219, 11, 239, 9], [219, 13, 239, 11], [219, 17, 239, 15], [220, 4, 240, 2, "boxSizing"], [220, 13, 240, 11], [220, 15, 240, 13], [220, 19, 240, 17], [221, 4, 242, 2, "margin"], [221, 10, 242, 8], [221, 12, 242, 10], [221, 16, 242, 14], [222, 4, 243, 2, "marginBlock"], [222, 15, 243, 13], [222, 17, 243, 15], [222, 21, 243, 19], [223, 4, 244, 2, "marginBlockEnd"], [223, 18, 244, 16], [223, 20, 244, 18], [223, 24, 244, 22], [224, 4, 245, 2, "marginBlockStart"], [224, 20, 245, 18], [224, 22, 245, 20], [224, 26, 245, 24], [225, 4, 246, 2, "marginBottom"], [225, 16, 246, 14], [225, 18, 246, 16], [225, 22, 246, 20], [226, 4, 247, 2, "marginEnd"], [226, 13, 247, 11], [226, 15, 247, 13], [226, 19, 247, 17], [227, 4, 248, 2, "marginHorizontal"], [227, 20, 248, 18], [227, 22, 248, 20], [227, 26, 248, 24], [228, 4, 249, 2, "marginInline"], [228, 16, 249, 14], [228, 18, 249, 16], [228, 22, 249, 20], [229, 4, 250, 2, "marginInlineEnd"], [229, 19, 250, 17], [229, 21, 250, 19], [229, 25, 250, 23], [230, 4, 251, 2, "marginInlineStart"], [230, 21, 251, 19], [230, 23, 251, 21], [230, 27, 251, 25], [231, 4, 252, 2, "marginLeft"], [231, 14, 252, 12], [231, 16, 252, 14], [231, 20, 252, 18], [232, 4, 253, 2, "marginRight"], [232, 15, 253, 13], [232, 17, 253, 15], [232, 21, 253, 19], [233, 4, 254, 2, "marginStart"], [233, 15, 254, 13], [233, 17, 254, 15], [233, 21, 254, 19], [234, 4, 255, 2, "marginTop"], [234, 13, 255, 11], [234, 15, 255, 13], [234, 19, 255, 17], [235, 4, 256, 2, "marginVertical"], [235, 18, 256, 16], [235, 20, 256, 18], [235, 24, 256, 22], [236, 4, 258, 2, "padding"], [236, 11, 258, 9], [236, 13, 258, 11], [236, 17, 258, 15], [237, 4, 259, 2, "paddingBlock"], [237, 16, 259, 14], [237, 18, 259, 16], [237, 22, 259, 20], [238, 4, 260, 2, "paddingBlockEnd"], [238, 19, 260, 17], [238, 21, 260, 19], [238, 25, 260, 23], [239, 4, 261, 2, "paddingBlockStart"], [239, 21, 261, 19], [239, 23, 261, 21], [239, 27, 261, 25], [240, 4, 262, 2, "paddingBottom"], [240, 17, 262, 15], [240, 19, 262, 17], [240, 23, 262, 21], [241, 4, 263, 2, "paddingEnd"], [241, 14, 263, 12], [241, 16, 263, 14], [241, 20, 263, 18], [242, 4, 264, 2, "paddingHorizontal"], [242, 21, 264, 19], [242, 23, 264, 21], [242, 27, 264, 25], [243, 4, 265, 2, "paddingInline"], [243, 17, 265, 15], [243, 19, 265, 17], [243, 23, 265, 21], [244, 4, 266, 2, "paddingInlineEnd"], [244, 20, 266, 18], [244, 22, 266, 20], [244, 26, 266, 24], [245, 4, 267, 2, "paddingInlineStart"], [245, 22, 267, 20], [245, 24, 267, 22], [245, 28, 267, 26], [246, 4, 268, 2, "paddingLeft"], [246, 15, 268, 13], [246, 17, 268, 15], [246, 21, 268, 19], [247, 4, 269, 2, "paddingRight"], [247, 16, 269, 14], [247, 18, 269, 16], [247, 22, 269, 20], [248, 4, 270, 2, "paddingStart"], [248, 16, 270, 14], [248, 18, 270, 16], [248, 22, 270, 20], [249, 4, 271, 2, "paddingTop"], [249, 14, 271, 12], [249, 16, 271, 14], [249, 20, 271, 18], [250, 4, 272, 2, "paddingVertical"], [250, 19, 272, 17], [250, 21, 272, 19], [250, 25, 272, 23], [251, 4, 274, 2, "borderWidth"], [251, 15, 274, 13], [251, 17, 274, 15], [251, 21, 274, 19], [252, 4, 275, 2, "borderStartWidth"], [252, 20, 275, 18], [252, 22, 275, 20], [252, 26, 275, 24], [253, 4, 276, 2, "borderEndWidth"], [253, 18, 276, 16], [253, 20, 276, 18], [253, 24, 276, 22], [254, 4, 277, 2, "borderTopWidth"], [254, 18, 277, 16], [254, 20, 277, 18], [254, 24, 277, 22], [255, 4, 278, 2, "borderBottomWidth"], [255, 21, 278, 19], [255, 23, 278, 21], [255, 27, 278, 25], [256, 4, 279, 2, "borderLeftWidth"], [256, 19, 279, 17], [256, 21, 279, 19], [256, 25, 279, 23], [257, 4, 280, 2, "borderRightWidth"], [257, 20, 280, 18], [257, 22, 280, 20], [257, 26, 280, 24], [258, 4, 282, 2, "outlineColor"], [258, 16, 282, 14], [258, 18, 282, 16], [259, 6, 282, 17, "process"], [259, 13, 282, 24], [259, 15, 282, 26, "require"], [259, 22, 282, 33], [259, 23, 282, 33, "_dependencyMap"], [259, 37, 282, 33], [259, 70, 282, 62], [259, 71, 282, 63], [259, 72, 282, 64, "default"], [260, 4, 282, 71], [260, 5, 282, 72], [261, 4, 283, 2, "outlineOffset"], [261, 17, 283, 15], [261, 19, 283, 17], [261, 23, 283, 21], [262, 4, 284, 2, "outlineStyle"], [262, 16, 284, 14], [262, 18, 284, 16], [262, 22, 284, 20], [263, 4, 285, 2, "outlineWidth"], [263, 16, 285, 14], [263, 18, 285, 16], [263, 22, 285, 20], [264, 4, 287, 2, "start"], [264, 9, 287, 7], [264, 11, 287, 9], [264, 15, 287, 13], [265, 4, 288, 2, "end"], [265, 7, 288, 5], [265, 9, 288, 7], [265, 13, 288, 11], [266, 4, 289, 2, "left"], [266, 8, 289, 6], [266, 10, 289, 8], [266, 14, 289, 12], [267, 4, 290, 2, "right"], [267, 9, 290, 7], [267, 11, 290, 9], [267, 15, 290, 13], [268, 4, 291, 2, "top"], [268, 7, 291, 5], [268, 9, 291, 7], [268, 13, 291, 11], [269, 4, 292, 2, "bottom"], [269, 10, 292, 8], [269, 12, 292, 10], [269, 16, 292, 14], [270, 4, 294, 2, "inset"], [270, 9, 294, 7], [270, 11, 294, 9], [270, 15, 294, 13], [271, 4, 295, 2, "insetBlock"], [271, 14, 295, 12], [271, 16, 295, 14], [271, 20, 295, 18], [272, 4, 296, 2, "insetBlockEnd"], [272, 17, 296, 15], [272, 19, 296, 17], [272, 23, 296, 21], [273, 4, 297, 2, "insetBlockStart"], [273, 19, 297, 17], [273, 21, 297, 19], [273, 25, 297, 23], [274, 4, 298, 2, "insetInline"], [274, 15, 298, 13], [274, 17, 298, 15], [274, 21, 298, 19], [275, 4, 299, 2, "insetInlineEnd"], [275, 18, 299, 16], [275, 20, 299, 18], [275, 24, 299, 22], [276, 4, 300, 2, "insetInlineStart"], [276, 20, 300, 18], [276, 22, 300, 20], [276, 26, 300, 24], [277, 4, 302, 2, "position"], [277, 12, 302, 10], [277, 14, 302, 12], [277, 18, 302, 16], [278, 4, 304, 2, "style"], [278, 9, 304, 7], [278, 11, 304, 9, "ReactNativeStyleAttributes"], [278, 46, 304, 35], [279, 4, 307, 2, "removeClippedSubviews"], [279, 25, 307, 23], [279, 27, 307, 25], [279, 31, 307, 29], [280, 4, 310, 2, "accessible"], [280, 14, 310, 12], [280, 16, 310, 14], [280, 20, 310, 18], [281, 4, 311, 2, "hasTVPreferredFocus"], [281, 23, 311, 21], [281, 25, 311, 23], [281, 29, 311, 27], [282, 4, 312, 2, "nextFocusDown"], [282, 17, 312, 15], [282, 19, 312, 17], [282, 23, 312, 21], [283, 4, 313, 2, "nextFocusForward"], [283, 20, 313, 18], [283, 22, 313, 20], [283, 26, 313, 24], [284, 4, 314, 2, "nextFocusLeft"], [284, 17, 314, 15], [284, 19, 314, 17], [284, 23, 314, 21], [285, 4, 315, 2, "nextFocusRight"], [285, 18, 315, 16], [285, 20, 315, 18], [285, 24, 315, 22], [286, 4, 316, 2, "nextFocusUp"], [286, 15, 316, 13], [286, 17, 316, 15], [286, 21, 316, 19], [287, 4, 318, 2, "borderRadius"], [287, 16, 318, 14], [287, 18, 318, 16], [287, 22, 318, 20], [288, 4, 319, 2, "borderTopLeftRadius"], [288, 23, 319, 21], [288, 25, 319, 23], [288, 29, 319, 27], [289, 4, 320, 2, "borderTopRightRadius"], [289, 24, 320, 22], [289, 26, 320, 24], [289, 30, 320, 28], [290, 4, 321, 2, "borderBottomRightRadius"], [290, 27, 321, 25], [290, 29, 321, 27], [290, 33, 321, 31], [291, 4, 322, 2, "borderBottomLeftRadius"], [291, 26, 322, 24], [291, 28, 322, 26], [291, 32, 322, 30], [292, 4, 323, 2, "borderTopStartRadius"], [292, 24, 323, 22], [292, 26, 323, 24], [292, 30, 323, 28], [293, 4, 324, 2, "borderTopEndRadius"], [293, 22, 324, 20], [293, 24, 324, 22], [293, 28, 324, 26], [294, 4, 325, 2, "borderBottomStartRadius"], [294, 27, 325, 25], [294, 29, 325, 27], [294, 33, 325, 31], [295, 4, 326, 2, "borderBottomEndRadius"], [295, 25, 326, 23], [295, 27, 326, 25], [295, 31, 326, 29], [296, 4, 327, 2, "borderEndEndRadius"], [296, 22, 327, 20], [296, 24, 327, 22], [296, 28, 327, 26], [297, 4, 328, 2, "borderEndStartRadius"], [297, 24, 328, 22], [297, 26, 328, 24], [297, 30, 328, 28], [298, 4, 329, 2, "borderStartEndRadius"], [298, 24, 329, 22], [298, 26, 329, 24], [298, 30, 329, 28], [299, 4, 330, 2, "borderStartStartRadius"], [299, 26, 330, 24], [299, 28, 330, 26], [299, 32, 330, 30], [300, 4, 331, 2, "borderStyle"], [300, 15, 331, 13], [300, 17, 331, 15], [300, 21, 331, 19], [301, 4, 332, 2, "hitSlop"], [301, 11, 332, 9], [301, 13, 332, 11], [301, 17, 332, 15], [302, 4, 333, 2, "pointerEvents"], [302, 17, 333, 15], [302, 19, 333, 17], [302, 23, 333, 21], [303, 4, 334, 2, "nativeBackgroundAndroid"], [303, 27, 334, 25], [303, 29, 334, 27], [303, 33, 334, 31], [304, 4, 335, 2, "nativeForegroundAndroid"], [304, 27, 335, 25], [304, 29, 335, 27], [304, 33, 335, 31], [305, 4, 336, 2, "needsOffscreenAlphaCompositing"], [305, 34, 336, 32], [305, 36, 336, 34], [305, 40, 336, 38], [306, 4, 338, 2, "borderColor"], [306, 15, 338, 13], [306, 17, 338, 15], [307, 6, 339, 4, "process"], [307, 13, 339, 11], [307, 15, 339, 13, "require"], [307, 22, 339, 20], [307, 23, 339, 20, "_dependencyMap"], [307, 37, 339, 20], [307, 70, 339, 49], [307, 71, 339, 50], [307, 72, 339, 51, "default"], [308, 4, 340, 2], [308, 5, 340, 3], [309, 4, 341, 2, "borderLeftColor"], [309, 19, 341, 17], [309, 21, 341, 19], [310, 6, 342, 4, "process"], [310, 13, 342, 11], [310, 15, 342, 13, "require"], [310, 22, 342, 20], [310, 23, 342, 20, "_dependencyMap"], [310, 37, 342, 20], [310, 70, 342, 49], [310, 71, 342, 50], [310, 72, 342, 51, "default"], [311, 4, 343, 2], [311, 5, 343, 3], [312, 4, 344, 2, "borderRightColor"], [312, 20, 344, 18], [312, 22, 344, 20], [313, 6, 345, 4, "process"], [313, 13, 345, 11], [313, 15, 345, 13, "require"], [313, 22, 345, 20], [313, 23, 345, 20, "_dependencyMap"], [313, 37, 345, 20], [313, 70, 345, 49], [313, 71, 345, 50], [313, 72, 345, 51, "default"], [314, 4, 346, 2], [314, 5, 346, 3], [315, 4, 347, 2, "borderTopColor"], [315, 18, 347, 16], [315, 20, 347, 18], [316, 6, 348, 4, "process"], [316, 13, 348, 11], [316, 15, 348, 13, "require"], [316, 22, 348, 20], [316, 23, 348, 20, "_dependencyMap"], [316, 37, 348, 20], [316, 70, 348, 49], [316, 71, 348, 50], [316, 72, 348, 51, "default"], [317, 4, 349, 2], [317, 5, 349, 3], [318, 4, 350, 2, "borderBottomColor"], [318, 21, 350, 19], [318, 23, 350, 21], [319, 6, 351, 4, "process"], [319, 13, 351, 11], [319, 15, 351, 13, "require"], [319, 22, 351, 20], [319, 23, 351, 20, "_dependencyMap"], [319, 37, 351, 20], [319, 70, 351, 49], [319, 71, 351, 50], [319, 72, 351, 51, "default"], [320, 4, 352, 2], [320, 5, 352, 3], [321, 4, 353, 2, "borderStartColor"], [321, 20, 353, 18], [321, 22, 353, 20], [322, 6, 354, 4, "process"], [322, 13, 354, 11], [322, 15, 354, 13, "require"], [322, 22, 354, 20], [322, 23, 354, 20, "_dependencyMap"], [322, 37, 354, 20], [322, 70, 354, 49], [322, 71, 354, 50], [322, 72, 354, 51, "default"], [323, 4, 355, 2], [323, 5, 355, 3], [324, 4, 356, 2, "borderEndColor"], [324, 18, 356, 16], [324, 20, 356, 18], [325, 6, 357, 4, "process"], [325, 13, 357, 11], [325, 15, 357, 13, "require"], [325, 22, 357, 20], [325, 23, 357, 20, "_dependencyMap"], [325, 37, 357, 20], [325, 70, 357, 49], [325, 71, 357, 50], [325, 72, 357, 51, "default"], [326, 4, 358, 2], [326, 5, 358, 3], [327, 4, 359, 2, "borderBlockColor"], [327, 20, 359, 18], [327, 22, 359, 20], [328, 6, 360, 4, "process"], [328, 13, 360, 11], [328, 15, 360, 13, "require"], [328, 22, 360, 20], [328, 23, 360, 20, "_dependencyMap"], [328, 37, 360, 20], [328, 70, 360, 49], [328, 71, 360, 50], [328, 72, 360, 51, "default"], [329, 4, 361, 2], [329, 5, 361, 3], [330, 4, 362, 2, "borderBlockEndColor"], [330, 23, 362, 21], [330, 25, 362, 23], [331, 6, 363, 4, "process"], [331, 13, 363, 11], [331, 15, 363, 13, "require"], [331, 22, 363, 20], [331, 23, 363, 20, "_dependencyMap"], [331, 37, 363, 20], [331, 70, 363, 49], [331, 71, 363, 50], [331, 72, 363, 51, "default"], [332, 4, 364, 2], [332, 5, 364, 3], [333, 4, 365, 2, "borderBlockStartColor"], [333, 25, 365, 23], [333, 27, 365, 25], [334, 6, 366, 4, "process"], [334, 13, 366, 11], [334, 15, 366, 13, "require"], [334, 22, 366, 20], [334, 23, 366, 20, "_dependencyMap"], [334, 37, 366, 20], [334, 70, 366, 49], [334, 71, 366, 50], [334, 72, 366, 51, "default"], [335, 4, 367, 2], [335, 5, 367, 3], [336, 4, 368, 2, "focusable"], [336, 13, 368, 11], [336, 15, 368, 13], [336, 19, 368, 17], [337, 4, 369, 2, "backfaceVisibility"], [337, 22, 369, 20], [337, 24, 369, 22], [338, 2, 370, 0], [338, 3, 370, 1], [339, 2, 373, 0], [339, 6, 373, 6, "validAttributesForEventProps"], [339, 34, 373, 34], [339, 37, 373, 37], [340, 4, 374, 2, "onLayout"], [340, 12, 374, 10], [340, 14, 374, 12], [340, 18, 374, 16], [341, 4, 377, 2, "onMoveShouldSetResponder"], [341, 28, 377, 26], [341, 30, 377, 28], [341, 34, 377, 32], [342, 4, 378, 2, "onMoveShouldSetResponderCapture"], [342, 35, 378, 33], [342, 37, 378, 35], [342, 41, 378, 39], [343, 4, 379, 2, "onStartShouldSetResponder"], [343, 29, 379, 27], [343, 31, 379, 29], [343, 35, 379, 33], [344, 4, 380, 2, "onStartShouldSetResponderCapture"], [344, 36, 380, 34], [344, 38, 380, 36], [344, 42, 380, 40], [345, 4, 381, 2, "onResponderGrant"], [345, 20, 381, 18], [345, 22, 381, 20], [345, 26, 381, 24], [346, 4, 382, 2, "onResponderReject"], [346, 21, 382, 19], [346, 23, 382, 21], [346, 27, 382, 25], [347, 4, 383, 2, "onResponderStart"], [347, 20, 383, 18], [347, 22, 383, 20], [347, 26, 383, 24], [348, 4, 384, 2, "onResponderEnd"], [348, 18, 384, 16], [348, 20, 384, 18], [348, 24, 384, 22], [349, 4, 385, 2, "onResponderRelease"], [349, 22, 385, 20], [349, 24, 385, 22], [349, 28, 385, 26], [350, 4, 386, 2, "onResponderMove"], [350, 19, 386, 17], [350, 21, 386, 19], [350, 25, 386, 23], [351, 4, 387, 2, "onResponderTerminate"], [351, 24, 387, 22], [351, 26, 387, 24], [351, 30, 387, 28], [352, 4, 388, 2, "onResponderTerminationRequest"], [352, 33, 388, 31], [352, 35, 388, 33], [352, 39, 388, 37], [353, 4, 389, 2, "onShouldBlockNativeResponder"], [353, 32, 389, 30], [353, 34, 389, 32], [353, 38, 389, 36], [354, 4, 392, 2, "onTouchStart"], [354, 16, 392, 14], [354, 18, 392, 16], [354, 22, 392, 20], [355, 4, 393, 2, "onTouchMove"], [355, 15, 393, 13], [355, 17, 393, 15], [355, 21, 393, 19], [356, 4, 394, 2, "onTouchEnd"], [356, 14, 394, 12], [356, 16, 394, 14], [356, 20, 394, 18], [357, 4, 395, 2, "onTouchCancel"], [357, 17, 395, 15], [357, 19, 395, 17], [357, 23, 395, 21], [358, 4, 398, 2, "onClick"], [358, 11, 398, 9], [358, 13, 398, 11], [358, 17, 398, 15], [359, 4, 399, 2, "onClickCapture"], [359, 18, 399, 16], [359, 20, 399, 18], [359, 24, 399, 22], [360, 4, 400, 2, "onPointerEnter"], [360, 18, 400, 16], [360, 20, 400, 18], [360, 24, 400, 22], [361, 4, 401, 2, "onPointerEnterCapture"], [361, 25, 401, 23], [361, 27, 401, 25], [361, 31, 401, 29], [362, 4, 402, 2, "onPointerLeave"], [362, 18, 402, 16], [362, 20, 402, 18], [362, 24, 402, 22], [363, 4, 403, 2, "onPointerLeaveCapture"], [363, 25, 403, 23], [363, 27, 403, 25], [363, 31, 403, 29], [364, 4, 404, 2, "onPointerMove"], [364, 17, 404, 15], [364, 19, 404, 17], [364, 23, 404, 21], [365, 4, 405, 2, "onPointerMoveCapture"], [365, 24, 405, 22], [365, 26, 405, 24], [365, 30, 405, 28], [366, 4, 406, 2, "onPointerOut"], [366, 16, 406, 14], [366, 18, 406, 16], [366, 22, 406, 20], [367, 4, 407, 2, "onPointerOutCapture"], [367, 23, 407, 21], [367, 25, 407, 23], [367, 29, 407, 27], [368, 4, 408, 2, "onPointerOver"], [368, 17, 408, 15], [368, 19, 408, 17], [368, 23, 408, 21], [369, 4, 409, 2, "onPointerOverCapture"], [369, 24, 409, 22], [369, 26, 409, 24], [370, 2, 410, 0], [370, 3, 410, 1], [371, 2, 422, 0], [371, 6, 422, 6, "PlatformBaseViewConfigAndroid"], [371, 35, 422, 65], [371, 38, 422, 68], [372, 4, 423, 2, "directEventTypes"], [372, 20, 423, 18], [373, 4, 424, 2, "bubblingEventTypes"], [373, 22, 424, 20], [374, 4, 425, 2, "validAttributes"], [374, 19, 425, 17], [374, 21, 425, 19], [375, 6, 426, 4], [375, 9, 426, 7, "validAttributesForNonEventProps"], [375, 40, 426, 38], [376, 6, 427, 4], [376, 9, 427, 7, "validAttributesForEventProps"], [377, 4, 428, 2], [378, 2, 429, 0], [378, 3, 429, 1], [379, 2, 429, 2], [379, 6, 429, 2, "_default"], [379, 14, 429, 2], [379, 17, 429, 2, "exports"], [379, 24, 429, 2], [379, 25, 429, 2, "default"], [379, 32, 429, 2], [379, 35, 431, 15, "PlatformBaseViewConfigAndroid"], [379, 64, 431, 44], [380, 0, 431, 44], [380, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}