{"dependencies": [{"name": "./WorkletsModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 76}, "end": {"line": 4, "column": 50, "index": 126}}], "key": "7BsUkCdrqb1UxMi5qvmPkNSKkvQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"WorkletsModule\", {\n    enumerable: true,\n    get: function () {\n      return _WorkletsModule.WorkletsModule;\n    }\n  });\n  var _WorkletsModule = require(_dependencyMap[0], \"./WorkletsModule\");\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "Object"], [7, 8, 1, 13], [7, 9, 1, 13, "defineProperty"], [7, 23, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [8, 4, 1, 13, "enumerable"], [8, 14, 1, 13], [9, 4, 1, 13, "get"], [9, 7, 1, 13], [9, 18, 1, 13, "get"], [9, 19, 1, 13], [10, 6, 1, 13], [10, 13, 1, 13, "_WorkletsModule"], [10, 28, 1, 13], [10, 29, 1, 13, "WorkletsModule"], [10, 43, 1, 13], [11, 4, 1, 13], [12, 2, 1, 13], [13, 2, 4, 0], [13, 6, 4, 0, "_WorkletsModule"], [13, 21, 4, 0], [13, 24, 4, 0, "require"], [13, 31, 4, 0], [13, 32, 4, 0, "_dependencyMap"], [13, 46, 4, 0], [14, 0, 4, 50], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}