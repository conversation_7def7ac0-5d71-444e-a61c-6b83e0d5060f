{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var EventPolyfill = /*#__PURE__*/function () {\n    function EventPolyfill(type, eventInitDict) {\n      (0, _classCallCheck2.default)(this, EventPolyfill);\n      this.type = type;\n      this.bubbles = !!(eventInitDict?.bubbles || false);\n      this.cancelable = !!(eventInitDict?.cancelable || false);\n      this.composed = !!(eventInitDict?.composed || false);\n      this.scoped = !!(eventInitDict?.scoped || false);\n      this.isTrusted = false;\n      this.timeStamp = Date.now();\n      this.defaultPrevented = false;\n      this.NONE = 0;\n      this.AT_TARGET = 1;\n      this.BUBBLING_PHASE = 2;\n      this.CAPTURING_PHASE = 3;\n      this.eventPhase = this.NONE;\n      this.currentTarget = null;\n      this.target = null;\n      this.srcElement = null;\n    }\n    return (0, _createClass2.default)(EventPolyfill, [{\n      key: \"composedPath\",\n      value: function composedPath() {\n        throw new Error('TODO: not yet implemented');\n      }\n    }, {\n      key: \"preventDefault\",\n      value: function preventDefault() {\n        this.defaultPrevented = true;\n        if (this._syntheticEvent != null) {\n          this._syntheticEvent.preventDefault();\n        }\n      }\n    }, {\n      key: \"initEvent\",\n      value: function initEvent(type, bubbles, cancelable) {\n        throw new Error('TODO: not yet implemented. This method is also deprecated.');\n      }\n    }, {\n      key: \"stopImmediatePropagation\",\n      value: function stopImmediatePropagation() {\n        throw new Error('TODO: not yet implemented');\n      }\n    }, {\n      key: \"stopPropagation\",\n      value: function stopPropagation() {\n        if (this._syntheticEvent != null) {\n          this._syntheticEvent.stopPropagation();\n        }\n      }\n    }, {\n      key: \"setSyntheticEvent\",\n      value: function setSyntheticEvent(value) {\n        this._syntheticEvent = value;\n      }\n    }]);\n  }();\n  global.Event = EventPolyfill;\n  var _default = exports.default = EventPolyfill;\n});", "lineCount": 68, "map": [[9, 6, 131, 6, "EventPolyfill"], [9, 19, 131, 19], [10, 4, 162, 2], [10, 13, 162, 2, "EventPolyfill"], [10, 27, 162, 14, "type"], [10, 31, 162, 26], [10, 33, 162, 28, "eventInitDict"], [10, 46, 162, 54], [10, 48, 162, 56], [11, 6, 162, 56], [11, 10, 162, 56, "_classCallCheck2"], [11, 26, 162, 56], [11, 27, 162, 56, "default"], [11, 34, 162, 56], [11, 42, 162, 56, "EventPolyfill"], [11, 55, 162, 56], [12, 6, 163, 4], [12, 10, 163, 8], [12, 11, 163, 9, "type"], [12, 15, 163, 13], [12, 18, 163, 16, "type"], [12, 22, 163, 20], [13, 6, 164, 4], [13, 10, 164, 8], [13, 11, 164, 9, "bubbles"], [13, 18, 164, 16], [13, 21, 164, 19], [13, 22, 164, 20], [13, 24, 164, 22, "eventInitDict"], [13, 37, 164, 35], [13, 39, 164, 37, "bubbles"], [13, 46, 164, 44], [13, 50, 164, 48], [13, 55, 164, 53], [13, 56, 164, 54], [14, 6, 165, 4], [14, 10, 165, 8], [14, 11, 165, 9, "cancelable"], [14, 21, 165, 19], [14, 24, 165, 22], [14, 25, 165, 23], [14, 27, 165, 25, "eventInitDict"], [14, 40, 165, 38], [14, 42, 165, 40, "cancelable"], [14, 52, 165, 50], [14, 56, 165, 54], [14, 61, 165, 59], [14, 62, 165, 60], [15, 6, 166, 4], [15, 10, 166, 8], [15, 11, 166, 9, "composed"], [15, 19, 166, 17], [15, 22, 166, 20], [15, 23, 166, 21], [15, 25, 166, 23, "eventInitDict"], [15, 38, 166, 36], [15, 40, 166, 38, "composed"], [15, 48, 166, 46], [15, 52, 166, 50], [15, 57, 166, 55], [15, 58, 166, 56], [16, 6, 167, 4], [16, 10, 167, 8], [16, 11, 167, 9, "scoped"], [16, 17, 167, 15], [16, 20, 167, 18], [16, 21, 167, 19], [16, 23, 167, 21, "eventInitDict"], [16, 36, 167, 34], [16, 38, 167, 36, "scoped"], [16, 44, 167, 42], [16, 48, 167, 46], [16, 53, 167, 51], [16, 54, 167, 52], [17, 6, 171, 4], [17, 10, 171, 8], [17, 11, 171, 9, "isTrusted"], [17, 20, 171, 18], [17, 23, 171, 21], [17, 28, 171, 26], [18, 6, 175, 4], [18, 10, 175, 8], [18, 11, 175, 9, "timeStamp"], [18, 20, 175, 18], [18, 23, 175, 21, "Date"], [18, 27, 175, 25], [18, 28, 175, 26, "now"], [18, 31, 175, 29], [18, 32, 175, 30], [18, 33, 175, 31], [19, 6, 177, 4], [19, 10, 177, 8], [19, 11, 177, 9, "defaultPrevented"], [19, 27, 177, 25], [19, 30, 177, 28], [19, 35, 177, 33], [20, 6, 180, 4], [20, 10, 180, 8], [20, 11, 180, 9, "NONE"], [20, 15, 180, 13], [20, 18, 180, 16], [20, 19, 180, 17], [21, 6, 181, 4], [21, 10, 181, 8], [21, 11, 181, 9, "AT_TARGET"], [21, 20, 181, 18], [21, 23, 181, 21], [21, 24, 181, 22], [22, 6, 182, 4], [22, 10, 182, 8], [22, 11, 182, 9, "BUBBLING_PHASE"], [22, 25, 182, 23], [22, 28, 182, 26], [22, 29, 182, 27], [23, 6, 183, 4], [23, 10, 183, 8], [23, 11, 183, 9, "CAPTURING_PHASE"], [23, 26, 183, 24], [23, 29, 183, 27], [23, 30, 183, 28], [24, 6, 184, 4], [24, 10, 184, 8], [24, 11, 184, 9, "eventPhase"], [24, 21, 184, 19], [24, 24, 184, 22], [24, 28, 184, 26], [24, 29, 184, 27, "NONE"], [24, 33, 184, 31], [25, 6, 187, 4], [25, 10, 187, 8], [25, 11, 187, 9, "currentTarget"], [25, 24, 187, 22], [25, 27, 187, 25], [25, 31, 187, 29], [26, 6, 189, 4], [26, 10, 189, 8], [26, 11, 189, 9, "target"], [26, 17, 189, 15], [26, 20, 189, 18], [26, 24, 189, 22], [27, 6, 191, 4], [27, 10, 191, 8], [27, 11, 191, 9, "srcElement"], [27, 21, 191, 19], [27, 24, 191, 22], [27, 28, 191, 26], [28, 4, 192, 2], [29, 4, 192, 3], [29, 15, 192, 3, "_createClass2"], [29, 28, 192, 3], [29, 29, 192, 3, "default"], [29, 36, 192, 3], [29, 38, 192, 3, "EventPolyfill"], [29, 51, 192, 3], [30, 6, 192, 3, "key"], [30, 9, 192, 3], [31, 6, 192, 3, "value"], [31, 11, 192, 3], [31, 13, 194, 2], [31, 22, 194, 2, "<PERSON><PERSON><PERSON>"], [31, 34, 194, 14, "<PERSON><PERSON><PERSON>"], [31, 35, 194, 14], [31, 37, 194, 37], [32, 8, 195, 4], [32, 14, 195, 10], [32, 18, 195, 14, "Error"], [32, 23, 195, 19], [32, 24, 195, 20], [32, 51, 195, 47], [32, 52, 195, 48], [33, 6, 196, 2], [34, 4, 196, 3], [35, 6, 196, 3, "key"], [35, 9, 196, 3], [36, 6, 196, 3, "value"], [36, 11, 196, 3], [36, 13, 198, 2], [36, 22, 198, 2, "preventDefault"], [36, 36, 198, 16, "preventDefault"], [36, 37, 198, 16], [36, 39, 198, 25], [37, 8, 199, 4], [37, 12, 199, 8], [37, 13, 199, 9, "defaultPrevented"], [37, 29, 199, 25], [37, 32, 199, 28], [37, 36, 199, 32], [38, 8, 201, 4], [38, 12, 201, 8], [38, 16, 201, 12], [38, 17, 201, 13, "_syntheticEvent"], [38, 32, 201, 28], [38, 36, 201, 32], [38, 40, 201, 36], [38, 42, 201, 38], [39, 10, 203, 6], [39, 14, 203, 10], [39, 15, 203, 11, "_syntheticEvent"], [39, 30, 203, 26], [39, 31, 203, 27, "preventDefault"], [39, 45, 203, 41], [39, 46, 203, 42], [39, 47, 203, 43], [40, 8, 204, 4], [41, 6, 205, 2], [42, 4, 205, 3], [43, 6, 205, 3, "key"], [43, 9, 205, 3], [44, 6, 205, 3, "value"], [44, 11, 205, 3], [44, 13, 207, 2], [44, 22, 207, 2, "initEvent"], [44, 31, 207, 11, "initEvent"], [44, 32, 207, 12, "type"], [44, 36, 207, 24], [44, 38, 207, 26, "bubbles"], [44, 45, 207, 42], [44, 47, 207, 44, "cancelable"], [44, 57, 207, 63], [44, 59, 207, 71], [45, 8, 208, 4], [45, 14, 208, 10], [45, 18, 208, 14, "Error"], [45, 23, 208, 19], [45, 24, 209, 6], [45, 84, 210, 4], [45, 85, 210, 5], [46, 6, 211, 2], [47, 4, 211, 3], [48, 6, 211, 3, "key"], [48, 9, 211, 3], [49, 6, 211, 3, "value"], [49, 11, 211, 3], [49, 13, 213, 2], [49, 22, 213, 2, "stopImmediatePropagation"], [49, 46, 213, 26, "stopImmediatePropagation"], [49, 47, 213, 26], [49, 49, 213, 35], [50, 8, 214, 4], [50, 14, 214, 10], [50, 18, 214, 14, "Error"], [50, 23, 214, 19], [50, 24, 214, 20], [50, 51, 214, 47], [50, 52, 214, 48], [51, 6, 215, 2], [52, 4, 215, 3], [53, 6, 215, 3, "key"], [53, 9, 215, 3], [54, 6, 215, 3, "value"], [54, 11, 215, 3], [54, 13, 217, 2], [54, 22, 217, 2, "stopPropagation"], [54, 37, 217, 17, "stopPropagation"], [54, 38, 217, 17], [54, 40, 217, 26], [55, 8, 218, 4], [55, 12, 218, 8], [55, 16, 218, 12], [55, 17, 218, 13, "_syntheticEvent"], [55, 32, 218, 28], [55, 36, 218, 32], [55, 40, 218, 36], [55, 42, 218, 38], [56, 10, 220, 6], [56, 14, 220, 10], [56, 15, 220, 11, "_syntheticEvent"], [56, 30, 220, 26], [56, 31, 220, 27, "stopPropagation"], [56, 46, 220, 42], [56, 47, 220, 43], [56, 48, 220, 44], [57, 8, 221, 4], [58, 6, 222, 2], [59, 4, 222, 3], [60, 6, 222, 3, "key"], [60, 9, 222, 3], [61, 6, 222, 3, "value"], [61, 11, 222, 3], [61, 13, 224, 2], [61, 22, 224, 2, "setSyntheticEvent"], [61, 39, 224, 19, "setSyntheticEvent"], [61, 40, 224, 20, "value"], [61, 45, 224, 32], [61, 47, 224, 40], [62, 8, 225, 4], [62, 12, 225, 8], [62, 13, 225, 9, "_syntheticEvent"], [62, 28, 225, 24], [62, 31, 225, 27, "value"], [62, 36, 225, 32], [63, 6, 226, 2], [64, 4, 226, 3], [65, 2, 226, 3], [66, 2, 237, 0, "global"], [66, 8, 237, 6], [66, 9, 237, 7, "Event"], [66, 14, 237, 12], [66, 17, 237, 15, "EventPolyfill"], [66, 30, 237, 28], [67, 2, 237, 29], [67, 6, 237, 29, "_default"], [67, 14, 237, 29], [67, 17, 237, 29, "exports"], [67, 24, 237, 29], [67, 25, 237, 29, "default"], [67, 32, 237, 29], [67, 35, 239, 15, "EventPolyfill"], [67, 48, 239, 28], [68, 0, 239, 28], [68, 3]], "functionMap": {"names": ["<global>", "EventPolyfill", "constructor", "<PERSON><PERSON><PERSON>", "preventDefault", "initEvent", "stopImmediatePropagation", "stopPropagation", "setSyntheticEvent"], "mappings": "AAA;ACkI;EC+B;GD8B;EEE;GFE;EGE;GHO;EIE;GJI;EKE;GLE;EME;GNK;EOE;GPE;CDC"}}, "type": "js/module"}]}