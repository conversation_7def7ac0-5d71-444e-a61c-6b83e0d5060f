{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./specs/NativeRNGestureHandlerModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 185}, "end": {"line": 4, "column": 58, "index": 243}}], "key": "DCHlmtt+A0NUf50cVACHuf9czLY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeRNGestureHandlerModule = _interopRequireDefault(require(_dependencyMap[1], \"./specs/NativeRNGestureHandlerModule\"));\n  // Reexport the native module spec used by codegen. The relevant files are inluded on Android\n  // to ensure the compatibility with the old arch, while iOS doesn't require those at all.\n  var _default = exports.default = _NativeRNGestureHandlerModule.default;\n});", "lineCount": 11, "map": [[7, 2, 4, 0], [7, 6, 4, 0, "_NativeRNGestureHandlerModule"], [7, 35, 4, 0], [7, 38, 4, 0, "_interopRequireDefault"], [7, 60, 4, 0], [7, 61, 4, 0, "require"], [7, 68, 4, 0], [7, 69, 4, 0, "_dependencyMap"], [7, 83, 4, 0], [8, 2, 1, 0], [9, 2, 2, 0], [10, 2, 2, 0], [10, 6, 2, 0, "_default"], [10, 14, 2, 0], [10, 17, 2, 0, "exports"], [10, 24, 2, 0], [10, 25, 2, 0, "default"], [10, 32, 2, 0], [10, 35, 5, 15, "<PERSON><PERSON><PERSON>"], [10, 72, 5, 21], [11, 0, 5, 21], [11, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}