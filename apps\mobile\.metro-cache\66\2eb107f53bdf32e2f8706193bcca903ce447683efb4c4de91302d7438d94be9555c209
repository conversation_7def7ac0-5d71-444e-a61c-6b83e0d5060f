{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 55, "index": 55}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 57}, "end": {"line": 3, "column": 43, "index": 100}}], "key": "8hbLzyIXFhqWXguD+C6jDKIcDJ4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ensureNativeModulesAreInstalled = ensureNativeModulesAreInstalled;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _web = require(_dependencyMap[1], \"./web\");\n  /**\n   * Ensures that the native modules are installed in the current runtime.\n   * Otherwise, it synchronously calls a native function that installs them.\n   */\n  function ensureNativeModulesAreInstalled() {\n    if (globalThis.expo) {\n      return;\n    }\n    try {\n      if (_reactNative.Platform.OS === 'web') {\n        // Requiring web folder sets up the `globalThis.expo` object.\n        (0, _web.registerWebGlobals)();\n      } else {\n        // TODO: ExpoModulesCore shouldn't be optional here,\n        // but to keep backwards compatibility let's just ignore it in SDK 50.\n        // In most cases the modules were already installed from the native side.\n        _reactNative.NativeModules.ExpoModulesCore?.installModules();\n      }\n    } catch (error) {\n      console.error(`Unable to install Expo modules: ${error}`);\n    }\n  }\n});", "lineCount": 30, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_reactNative"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 3, 0], [7, 6, 3, 0, "_web"], [7, 10, 3, 0], [7, 13, 3, 0, "require"], [7, 20, 3, 0], [7, 21, 3, 0, "_dependencyMap"], [7, 35, 3, 0], [8, 2, 5, 0], [9, 0, 6, 0], [10, 0, 7, 0], [11, 0, 8, 0], [12, 2, 9, 7], [12, 11, 9, 16, "ensureNativeModulesAreInstalled"], [12, 42, 9, 47, "ensureNativeModulesAreInstalled"], [12, 43, 9, 47], [12, 45, 9, 56], [13, 4, 10, 2], [13, 8, 10, 6, "globalThis"], [13, 18, 10, 16], [13, 19, 10, 17, "expo"], [13, 23, 10, 21], [13, 25, 10, 23], [14, 6, 11, 4], [15, 4, 12, 2], [16, 4, 13, 2], [16, 8, 13, 6], [17, 6, 14, 4], [17, 10, 14, 8, "Platform"], [17, 31, 14, 16], [17, 32, 14, 17, "OS"], [17, 34, 14, 19], [17, 39, 14, 24], [17, 44, 14, 29], [17, 46, 14, 31], [18, 8, 15, 6], [19, 8, 16, 6], [19, 12, 16, 6, "registerWebGlobals"], [19, 35, 16, 24], [19, 37, 16, 25], [19, 38, 16, 26], [20, 6, 17, 4], [20, 7, 17, 5], [20, 13, 17, 11], [21, 8, 18, 6], [22, 8, 19, 6], [23, 8, 20, 6], [24, 8, 21, 6, "NativeModules"], [24, 34, 21, 19], [24, 35, 21, 20, "ExpoModulesCore"], [24, 50, 21, 35], [24, 52, 21, 37, "installModules"], [24, 66, 21, 51], [24, 67, 21, 52], [24, 68, 21, 53], [25, 6, 22, 4], [26, 4, 23, 2], [26, 5, 23, 3], [26, 6, 23, 4], [26, 13, 23, 11, "error"], [26, 18, 23, 16], [26, 20, 23, 18], [27, 6, 24, 4, "console"], [27, 13, 24, 11], [27, 14, 24, 12, "error"], [27, 19, 24, 17], [27, 20, 24, 18], [27, 55, 24, 53, "error"], [27, 60, 24, 58], [27, 62, 24, 60], [27, 63, 24, 61], [28, 4, 25, 2], [29, 2, 26, 0], [30, 0, 26, 1], [30, 3]], "functionMap": {"names": ["<global>", "ensureNativeModulesAreInstalled"], "mappings": "AAA;OCQ;CDiB"}}, "type": "js/module"}]}