{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 16, "index": 183}, "end": {"line": 4, "column": 32, "index": 199}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 18, "index": 219}, "end": {"line": 5, "column": 38, "index": 239}}, {"start": {"line": 6, "column": 16, "index": 257}, "end": {"line": 6, "column": 36, "index": 277}}], "key": "v9XDHaMNVvI8LGEEffd8gYvlOb8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.StoreContext = exports.NativeWindStyleSheet = exports.StyleSheetRuntime = void 0;\n  var react_1 = require(_dependencyMap[0], \"react\");\n  var runtime_1 = require(_dependencyMap[1], \"./runtime\");\n  var runtime_2 = require(_dependencyMap[1], \"./runtime\");\n  Object.defineProperty(exports, \"StyleSheetRuntime\", {\n    enumerable: true,\n    get: function () {\n      return runtime_2.StyleSheetRuntime;\n    }\n  });\n  var runtime = new runtime_1.StyleSheetRuntime();\n  exports.NativeWindStyleSheet = {\n    create: runtime.create.bind(runtime),\n    setDimensions: runtime.setDimensions.bind(runtime),\n    setAppearance: runtime.setAppearance.bind(runtime),\n    setPlatform: runtime.setPlatform.bind(runtime),\n    setOutput: runtime.setOutput.bind(runtime),\n    setColorScheme: runtime.setColorScheme.bind(runtime),\n    platformSelect: runtime.platformSelect.bind(runtime),\n    platformColor: runtime.platformColor.bind(runtime),\n    hairlineWidth: runtime.hairlineWidth.bind(runtime),\n    pixelRatio: runtime.pixelRatio.bind(runtime),\n    fontScale: runtime.fontScale.bind(runtime),\n    getPixelSizeForLayoutSize: runtime.getPixelSizeForLayoutSize.bind(runtime),\n    roundToNearestPixel: runtime.roundToNearestPixel.bind(runtime),\n    setDangerouslyCompileStyles: runtime.setDangerouslyCompileStyles.bind(runtime)\n  };\n  // We add this to a context so its overridable in tests\n  exports.StoreContext = (0, react_1.createContext)(runtime);\n});", "lineCount": 36, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "StoreContext"], [7, 22, 3, 20], [7, 25, 3, 23, "exports"], [7, 32, 3, 30], [7, 33, 3, 31, "NativeWindStyleSheet"], [7, 53, 3, 51], [7, 56, 3, 54, "exports"], [7, 63, 3, 61], [7, 64, 3, 62, "StyleSheetRuntime"], [7, 81, 3, 79], [7, 84, 3, 82], [7, 89, 3, 87], [7, 90, 3, 88], [8, 2, 4, 0], [8, 6, 4, 6, "react_1"], [8, 13, 4, 13], [8, 16, 4, 16, "require"], [8, 23, 4, 23], [8, 24, 4, 23, "_dependencyMap"], [8, 38, 4, 23], [8, 50, 4, 31], [8, 51, 4, 32], [9, 2, 5, 0], [9, 6, 5, 6, "runtime_1"], [9, 15, 5, 15], [9, 18, 5, 18, "require"], [9, 25, 5, 25], [9, 26, 5, 25, "_dependencyMap"], [9, 40, 5, 25], [9, 56, 5, 37], [9, 57, 5, 38], [10, 2, 6, 0], [10, 6, 6, 4, "runtime_2"], [10, 15, 6, 13], [10, 18, 6, 16, "require"], [10, 25, 6, 23], [10, 26, 6, 23, "_dependencyMap"], [10, 40, 6, 23], [10, 56, 6, 35], [10, 57, 6, 36], [11, 2, 7, 0, "Object"], [11, 8, 7, 6], [11, 9, 7, 7, "defineProperty"], [11, 23, 7, 21], [11, 24, 7, 22, "exports"], [11, 31, 7, 29], [11, 33, 7, 31], [11, 52, 7, 50], [11, 54, 7, 52], [12, 4, 7, 54, "enumerable"], [12, 14, 7, 64], [12, 16, 7, 66], [12, 20, 7, 70], [13, 4, 7, 72, "get"], [13, 7, 7, 75], [13, 9, 7, 77], [13, 18, 7, 77, "get"], [13, 19, 7, 77], [13, 21, 7, 89], [14, 6, 7, 91], [14, 13, 7, 98, "runtime_2"], [14, 22, 7, 107], [14, 23, 7, 108, "StyleSheetRuntime"], [14, 40, 7, 125], [15, 4, 7, 127], [16, 2, 7, 129], [16, 3, 7, 130], [16, 4, 7, 131], [17, 2, 8, 0], [17, 6, 8, 6, "runtime"], [17, 13, 8, 13], [17, 16, 8, 16], [17, 20, 8, 20, "runtime_1"], [17, 29, 8, 29], [17, 30, 8, 30, "StyleSheetRuntime"], [17, 47, 8, 47], [17, 48, 8, 48], [17, 49, 8, 49], [18, 2, 9, 0, "exports"], [18, 9, 9, 7], [18, 10, 9, 8, "NativeWindStyleSheet"], [18, 30, 9, 28], [18, 33, 9, 31], [19, 4, 10, 4, "create"], [19, 10, 10, 10], [19, 12, 10, 12, "runtime"], [19, 19, 10, 19], [19, 20, 10, 20, "create"], [19, 26, 10, 26], [19, 27, 10, 27, "bind"], [19, 31, 10, 31], [19, 32, 10, 32, "runtime"], [19, 39, 10, 39], [19, 40, 10, 40], [20, 4, 11, 4, "setDimensions"], [20, 17, 11, 17], [20, 19, 11, 19, "runtime"], [20, 26, 11, 26], [20, 27, 11, 27, "setDimensions"], [20, 40, 11, 40], [20, 41, 11, 41, "bind"], [20, 45, 11, 45], [20, 46, 11, 46, "runtime"], [20, 53, 11, 53], [20, 54, 11, 54], [21, 4, 12, 4, "set<PERSON><PERSON><PERSON>ce"], [21, 17, 12, 17], [21, 19, 12, 19, "runtime"], [21, 26, 12, 26], [21, 27, 12, 27, "set<PERSON><PERSON><PERSON>ce"], [21, 40, 12, 40], [21, 41, 12, 41, "bind"], [21, 45, 12, 45], [21, 46, 12, 46, "runtime"], [21, 53, 12, 53], [21, 54, 12, 54], [22, 4, 13, 4, "setPlatform"], [22, 15, 13, 15], [22, 17, 13, 17, "runtime"], [22, 24, 13, 24], [22, 25, 13, 25, "setPlatform"], [22, 36, 13, 36], [22, 37, 13, 37, "bind"], [22, 41, 13, 41], [22, 42, 13, 42, "runtime"], [22, 49, 13, 49], [22, 50, 13, 50], [23, 4, 14, 4, "setOutput"], [23, 13, 14, 13], [23, 15, 14, 15, "runtime"], [23, 22, 14, 22], [23, 23, 14, 23, "setOutput"], [23, 32, 14, 32], [23, 33, 14, 33, "bind"], [23, 37, 14, 37], [23, 38, 14, 38, "runtime"], [23, 45, 14, 45], [23, 46, 14, 46], [24, 4, 15, 4, "setColorScheme"], [24, 18, 15, 18], [24, 20, 15, 20, "runtime"], [24, 27, 15, 27], [24, 28, 15, 28, "setColorScheme"], [24, 42, 15, 42], [24, 43, 15, 43, "bind"], [24, 47, 15, 47], [24, 48, 15, 48, "runtime"], [24, 55, 15, 55], [24, 56, 15, 56], [25, 4, 16, 4, "platformSelect"], [25, 18, 16, 18], [25, 20, 16, 20, "runtime"], [25, 27, 16, 27], [25, 28, 16, 28, "platformSelect"], [25, 42, 16, 42], [25, 43, 16, 43, "bind"], [25, 47, 16, 47], [25, 48, 16, 48, "runtime"], [25, 55, 16, 55], [25, 56, 16, 56], [26, 4, 17, 4, "platformColor"], [26, 17, 17, 17], [26, 19, 17, 19, "runtime"], [26, 26, 17, 26], [26, 27, 17, 27, "platformColor"], [26, 40, 17, 40], [26, 41, 17, 41, "bind"], [26, 45, 17, 45], [26, 46, 17, 46, "runtime"], [26, 53, 17, 53], [26, 54, 17, 54], [27, 4, 18, 4, "hairlineWidth"], [27, 17, 18, 17], [27, 19, 18, 19, "runtime"], [27, 26, 18, 26], [27, 27, 18, 27, "hairlineWidth"], [27, 40, 18, 40], [27, 41, 18, 41, "bind"], [27, 45, 18, 45], [27, 46, 18, 46, "runtime"], [27, 53, 18, 53], [27, 54, 18, 54], [28, 4, 19, 4, "pixelRatio"], [28, 14, 19, 14], [28, 16, 19, 16, "runtime"], [28, 23, 19, 23], [28, 24, 19, 24, "pixelRatio"], [28, 34, 19, 34], [28, 35, 19, 35, "bind"], [28, 39, 19, 39], [28, 40, 19, 40, "runtime"], [28, 47, 19, 47], [28, 48, 19, 48], [29, 4, 20, 4, "fontScale"], [29, 13, 20, 13], [29, 15, 20, 15, "runtime"], [29, 22, 20, 22], [29, 23, 20, 23, "fontScale"], [29, 32, 20, 32], [29, 33, 20, 33, "bind"], [29, 37, 20, 37], [29, 38, 20, 38, "runtime"], [29, 45, 20, 45], [29, 46, 20, 46], [30, 4, 21, 4, "getPixelSizeForLayoutSize"], [30, 29, 21, 29], [30, 31, 21, 31, "runtime"], [30, 38, 21, 38], [30, 39, 21, 39, "getPixelSizeForLayoutSize"], [30, 64, 21, 64], [30, 65, 21, 65, "bind"], [30, 69, 21, 69], [30, 70, 21, 70, "runtime"], [30, 77, 21, 77], [30, 78, 21, 78], [31, 4, 22, 4, "roundToNearestPixel"], [31, 23, 22, 23], [31, 25, 22, 25, "runtime"], [31, 32, 22, 32], [31, 33, 22, 33, "roundToNearestPixel"], [31, 52, 22, 52], [31, 53, 22, 53, "bind"], [31, 57, 22, 57], [31, 58, 22, 58, "runtime"], [31, 65, 22, 65], [31, 66, 22, 66], [32, 4, 23, 4, "setDangerouslyCompileStyles"], [32, 31, 23, 31], [32, 33, 23, 33, "runtime"], [32, 40, 23, 40], [32, 41, 23, 41, "setDangerouslyCompileStyles"], [32, 68, 23, 68], [32, 69, 23, 69, "bind"], [32, 73, 23, 73], [32, 74, 23, 74, "runtime"], [32, 81, 23, 81], [33, 2, 24, 0], [33, 3, 24, 1], [34, 2, 25, 0], [35, 2, 26, 0, "exports"], [35, 9, 26, 7], [35, 10, 26, 8, "StoreContext"], [35, 22, 26, 20], [35, 25, 26, 23], [35, 26, 26, 24], [35, 27, 26, 25], [35, 29, 26, 27, "react_1"], [35, 36, 26, 34], [35, 37, 26, 35, "createContext"], [35, 50, 26, 48], [35, 52, 26, 50, "runtime"], [35, 59, 26, 57], [35, 60, 26, 58], [36, 0, 26, 59], [36, 3]], "functionMap": {"names": ["<global>", "Object.defineProperty$argument_2.get"], "mappings": "AAA;6ECM,mDD"}}, "type": "js/module"}]}