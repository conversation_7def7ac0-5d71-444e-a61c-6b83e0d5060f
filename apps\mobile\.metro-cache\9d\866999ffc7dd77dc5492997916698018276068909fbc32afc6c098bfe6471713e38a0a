{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 50, "index": 65}}], "key": "Wm75LgE4xYscVWo0KoLFlflJQCo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 66}, "end": {"line": 4, "column": 31, "index": 97}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 98}, "end": {"line": 5, "column": 46, "index": 144}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./useLinkProps.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 145}, "end": {"line": 6, "column": 49, "index": 194}}], "key": "pbNsupLgLtK+PYpgSgLKE1J9mSA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Link = Link;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _core = require(_dependencyMap[2], \"@react-navigation/core\");\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var _useLinkProps = require(_dependencyMap[5], \"./useLinkProps.js\");\n  var _excluded = [\"screen\", \"params\", \"action\", \"href\", \"style\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Component to render link to another screen using a path.\n   * Uses an anchor tag on the web.\n   *\n   * @param props.screen Name of the screen to navigate to (e.g. `'Feeds'`).\n   * @param props.params Params to pass to the screen to navigate to (e.g. `{ sort: 'hot' }`).\n   * @param props.href Optional absolute path to use for the href (e.g. `/feeds/hot`).\n   * @param props.action Optional action to use for in-page navigation. By default, the path is parsed to an action based on linking config.\n   * @param props.children Child elements to render the content.\n   */\n  function Link(_ref) {\n    var screen = _ref.screen,\n      params = _ref.params,\n      action = _ref.action,\n      href = _ref.href,\n      style = _ref.style,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var _useTheme = (0, _core.useTheme)(),\n      colors = _useTheme.colors,\n      fonts = _useTheme.fonts;\n    // @ts-expect-error: This is already type-checked by the prop types\n    var props = (0, _useLinkProps.useLinkProps)({\n      screen,\n      params,\n      action,\n      href\n    });\n    var onPress = e => {\n      if ('onPress' in rest) {\n        rest.onPress?.(e);\n      }\n\n      // Let user prevent default behavior\n      if (!e.defaultPrevented) {\n        props.onPress(e);\n      }\n    };\n    return /*#__PURE__*/React.createElement(_reactNative.Text, {\n      ...props,\n      ...rest,\n      ..._reactNative.Platform.select({\n        web: {\n          onClick: onPress\n        },\n        default: {\n          onPress\n        }\n      }),\n      style: [{\n        color: colors.primary\n      }, fonts.regular, style]\n    });\n  }\n});", "lineCount": 69, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Link"], [8, 14, 1, 13], [8, 17, 1, 13, "Link"], [8, 21, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_core"], [10, 11, 3, 0], [10, 14, 3, 0, "require"], [10, 21, 3, 0], [10, 22, 3, 0, "_dependencyMap"], [10, 36, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "React"], [11, 11, 4, 0], [11, 14, 4, 0, "_interopRequireWildcard"], [11, 37, 4, 0], [11, 38, 4, 0, "require"], [11, 45, 4, 0], [11, 46, 4, 0, "_dependencyMap"], [11, 60, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_reactNative"], [12, 18, 5, 0], [12, 21, 5, 0, "require"], [12, 28, 5, 0], [12, 29, 5, 0, "_dependencyMap"], [12, 43, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_useLinkProps"], [13, 19, 6, 0], [13, 22, 6, 0, "require"], [13, 29, 6, 0], [13, 30, 6, 0, "_dependencyMap"], [13, 44, 6, 0], [14, 2, 6, 49], [14, 6, 6, 49, "_excluded"], [14, 15, 6, 49], [15, 2, 6, 49], [15, 11, 6, 49, "_interopRequireWildcard"], [15, 35, 6, 49, "e"], [15, 36, 6, 49], [15, 38, 6, 49, "t"], [15, 39, 6, 49], [15, 68, 6, 49, "WeakMap"], [15, 75, 6, 49], [15, 81, 6, 49, "r"], [15, 82, 6, 49], [15, 89, 6, 49, "WeakMap"], [15, 96, 6, 49], [15, 100, 6, 49, "n"], [15, 101, 6, 49], [15, 108, 6, 49, "WeakMap"], [15, 115, 6, 49], [15, 127, 6, 49, "_interopRequireWildcard"], [15, 150, 6, 49], [15, 162, 6, 49, "_interopRequireWildcard"], [15, 163, 6, 49, "e"], [15, 164, 6, 49], [15, 166, 6, 49, "t"], [15, 167, 6, 49], [15, 176, 6, 49, "t"], [15, 177, 6, 49], [15, 181, 6, 49, "e"], [15, 182, 6, 49], [15, 186, 6, 49, "e"], [15, 187, 6, 49], [15, 188, 6, 49, "__esModule"], [15, 198, 6, 49], [15, 207, 6, 49, "e"], [15, 208, 6, 49], [15, 214, 6, 49, "o"], [15, 215, 6, 49], [15, 217, 6, 49, "i"], [15, 218, 6, 49], [15, 220, 6, 49, "f"], [15, 221, 6, 49], [15, 226, 6, 49, "__proto__"], [15, 235, 6, 49], [15, 243, 6, 49, "default"], [15, 250, 6, 49], [15, 252, 6, 49, "e"], [15, 253, 6, 49], [15, 270, 6, 49, "e"], [15, 271, 6, 49], [15, 294, 6, 49, "e"], [15, 295, 6, 49], [15, 320, 6, 49, "e"], [15, 321, 6, 49], [15, 330, 6, 49, "f"], [15, 331, 6, 49], [15, 337, 6, 49, "o"], [15, 338, 6, 49], [15, 341, 6, 49, "t"], [15, 342, 6, 49], [15, 345, 6, 49, "n"], [15, 346, 6, 49], [15, 349, 6, 49, "r"], [15, 350, 6, 49], [15, 358, 6, 49, "o"], [15, 359, 6, 49], [15, 360, 6, 49, "has"], [15, 363, 6, 49], [15, 364, 6, 49, "e"], [15, 365, 6, 49], [15, 375, 6, 49, "o"], [15, 376, 6, 49], [15, 377, 6, 49, "get"], [15, 380, 6, 49], [15, 381, 6, 49, "e"], [15, 382, 6, 49], [15, 385, 6, 49, "o"], [15, 386, 6, 49], [15, 387, 6, 49, "set"], [15, 390, 6, 49], [15, 391, 6, 49, "e"], [15, 392, 6, 49], [15, 394, 6, 49, "f"], [15, 395, 6, 49], [15, 409, 6, 49, "_t"], [15, 411, 6, 49], [15, 415, 6, 49, "e"], [15, 416, 6, 49], [15, 432, 6, 49, "_t"], [15, 434, 6, 49], [15, 441, 6, 49, "hasOwnProperty"], [15, 455, 6, 49], [15, 456, 6, 49, "call"], [15, 460, 6, 49], [15, 461, 6, 49, "e"], [15, 462, 6, 49], [15, 464, 6, 49, "_t"], [15, 466, 6, 49], [15, 473, 6, 49, "i"], [15, 474, 6, 49], [15, 478, 6, 49, "o"], [15, 479, 6, 49], [15, 482, 6, 49, "Object"], [15, 488, 6, 49], [15, 489, 6, 49, "defineProperty"], [15, 503, 6, 49], [15, 508, 6, 49, "Object"], [15, 514, 6, 49], [15, 515, 6, 49, "getOwnPropertyDescriptor"], [15, 539, 6, 49], [15, 540, 6, 49, "e"], [15, 541, 6, 49], [15, 543, 6, 49, "_t"], [15, 545, 6, 49], [15, 552, 6, 49, "i"], [15, 553, 6, 49], [15, 554, 6, 49, "get"], [15, 557, 6, 49], [15, 561, 6, 49, "i"], [15, 562, 6, 49], [15, 563, 6, 49, "set"], [15, 566, 6, 49], [15, 570, 6, 49, "o"], [15, 571, 6, 49], [15, 572, 6, 49, "f"], [15, 573, 6, 49], [15, 575, 6, 49, "_t"], [15, 577, 6, 49], [15, 579, 6, 49, "i"], [15, 580, 6, 49], [15, 584, 6, 49, "f"], [15, 585, 6, 49], [15, 586, 6, 49, "_t"], [15, 588, 6, 49], [15, 592, 6, 49, "e"], [15, 593, 6, 49], [15, 594, 6, 49, "_t"], [15, 596, 6, 49], [15, 607, 6, 49, "f"], [15, 608, 6, 49], [15, 613, 6, 49, "e"], [15, 614, 6, 49], [15, 616, 6, 49, "t"], [15, 617, 6, 49], [16, 2, 7, 0], [17, 0, 8, 0], [18, 0, 9, 0], [19, 0, 10, 0], [20, 0, 11, 0], [21, 0, 12, 0], [22, 0, 13, 0], [23, 0, 14, 0], [24, 0, 15, 0], [25, 0, 16, 0], [26, 2, 17, 7], [26, 11, 17, 16, "Link"], [26, 15, 17, 20, "Link"], [26, 16, 17, 20, "_ref"], [26, 20, 17, 20], [26, 22, 24, 3], [27, 4, 24, 3], [27, 8, 18, 2, "screen"], [27, 14, 18, 8], [27, 17, 18, 8, "_ref"], [27, 21, 18, 8], [27, 22, 18, 2, "screen"], [27, 28, 18, 8], [28, 6, 19, 2, "params"], [28, 12, 19, 8], [28, 15, 19, 8, "_ref"], [28, 19, 19, 8], [28, 20, 19, 2, "params"], [28, 26, 19, 8], [29, 6, 20, 2, "action"], [29, 12, 20, 8], [29, 15, 20, 8, "_ref"], [29, 19, 20, 8], [29, 20, 20, 2, "action"], [29, 26, 20, 8], [30, 6, 21, 2, "href"], [30, 10, 21, 6], [30, 13, 21, 6, "_ref"], [30, 17, 21, 6], [30, 18, 21, 2, "href"], [30, 22, 21, 6], [31, 6, 22, 2, "style"], [31, 11, 22, 7], [31, 14, 22, 7, "_ref"], [31, 18, 22, 7], [31, 19, 22, 2, "style"], [31, 24, 22, 7], [32, 6, 23, 5, "rest"], [32, 10, 23, 9], [32, 17, 23, 9, "_objectWithoutProperties2"], [32, 42, 23, 9], [32, 43, 23, 9, "default"], [32, 50, 23, 9], [32, 52, 23, 9, "_ref"], [32, 56, 23, 9], [32, 58, 23, 9, "_excluded"], [32, 67, 23, 9], [33, 4, 25, 2], [33, 8, 25, 2, "_useTheme"], [33, 17, 25, 2], [33, 20, 28, 6], [33, 24, 28, 6, "useTheme"], [33, 38, 28, 14], [33, 40, 28, 15], [33, 41, 28, 16], [34, 6, 26, 4, "colors"], [34, 12, 26, 10], [34, 15, 26, 10, "_useTheme"], [34, 24, 26, 10], [34, 25, 26, 4, "colors"], [34, 31, 26, 10], [35, 6, 27, 4, "fonts"], [35, 11, 27, 9], [35, 14, 27, 9, "_useTheme"], [35, 23, 27, 9], [35, 24, 27, 4, "fonts"], [35, 29, 27, 9], [36, 4, 29, 2], [37, 4, 30, 2], [37, 8, 30, 8, "props"], [37, 13, 30, 13], [37, 16, 30, 16], [37, 20, 30, 16, "useLinkProps"], [37, 46, 30, 28], [37, 48, 30, 29], [38, 6, 31, 4, "screen"], [38, 12, 31, 10], [39, 6, 32, 4, "params"], [39, 12, 32, 10], [40, 6, 33, 4, "action"], [40, 12, 33, 10], [41, 6, 34, 4, "href"], [42, 4, 35, 2], [42, 5, 35, 3], [42, 6, 35, 4], [43, 4, 36, 2], [43, 8, 36, 8, "onPress"], [43, 15, 36, 15], [43, 18, 36, 18, "e"], [43, 19, 36, 19], [43, 23, 36, 23], [44, 6, 37, 4], [44, 10, 37, 8], [44, 19, 37, 17], [44, 23, 37, 21, "rest"], [44, 27, 37, 25], [44, 29, 37, 27], [45, 8, 38, 6, "rest"], [45, 12, 38, 10], [45, 13, 38, 11, "onPress"], [45, 20, 38, 18], [45, 23, 38, 21, "e"], [45, 24, 38, 22], [45, 25, 38, 23], [46, 6, 39, 4], [48, 6, 41, 4], [49, 6, 42, 4], [49, 10, 42, 8], [49, 11, 42, 9, "e"], [49, 12, 42, 10], [49, 13, 42, 11, "defaultPrevented"], [49, 29, 42, 27], [49, 31, 42, 29], [50, 8, 43, 6, "props"], [50, 13, 43, 11], [50, 14, 43, 12, "onPress"], [50, 21, 43, 19], [50, 22, 43, 20, "e"], [50, 23, 43, 21], [50, 24, 43, 22], [51, 6, 44, 4], [52, 4, 45, 2], [52, 5, 45, 3], [53, 4, 46, 2], [53, 11, 46, 9], [53, 24, 46, 22, "React"], [53, 29, 46, 27], [53, 30, 46, 28, "createElement"], [53, 43, 46, 41], [53, 44, 46, 42, "Text"], [53, 61, 46, 46], [53, 63, 46, 48], [54, 6, 47, 4], [54, 9, 47, 7, "props"], [54, 14, 47, 12], [55, 6, 48, 4], [55, 9, 48, 7, "rest"], [55, 13, 48, 11], [56, 6, 49, 4], [56, 9, 49, 7, "Platform"], [56, 30, 49, 15], [56, 31, 49, 16, "select"], [56, 37, 49, 22], [56, 38, 49, 23], [57, 8, 50, 6, "web"], [57, 11, 50, 9], [57, 13, 50, 11], [58, 10, 51, 8, "onClick"], [58, 17, 51, 15], [58, 19, 51, 17, "onPress"], [59, 8, 52, 6], [59, 9, 52, 7], [60, 8, 53, 6, "default"], [60, 15, 53, 13], [60, 17, 53, 15], [61, 10, 54, 8, "onPress"], [62, 8, 55, 6], [63, 6, 56, 4], [63, 7, 56, 5], [63, 8, 56, 6], [64, 6, 57, 4, "style"], [64, 11, 57, 9], [64, 13, 57, 11], [64, 14, 57, 12], [65, 8, 58, 6, "color"], [65, 13, 58, 11], [65, 15, 58, 13, "colors"], [65, 21, 58, 19], [65, 22, 58, 20, "primary"], [66, 6, 59, 4], [66, 7, 59, 5], [66, 9, 59, 7, "fonts"], [66, 14, 59, 12], [66, 15, 59, 13, "regular"], [66, 22, 59, 20], [66, 24, 59, 22, "style"], [66, 29, 59, 27], [67, 4, 60, 2], [67, 5, 60, 3], [67, 6, 60, 4], [68, 2, 61, 0], [69, 0, 61, 1], [69, 3]], "functionMap": {"names": ["<global>", "Link", "onPress"], "mappings": "AAA;OCgB;kBCmB;GDS;CDgB"}}, "type": "js/module"}]}