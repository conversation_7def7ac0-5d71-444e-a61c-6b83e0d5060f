{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 54}, "end": {"line": 3, "column": 31, "index": 85}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 144}, "end": {"line": 5, "column": 40, "index": 184}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../fabricUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 243}, "end": {"line": 8, "column": 61, "index": 304}}], "key": "ZVfFH5AYEX8+P/wT0N7617eOwLE=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 305}, "end": {"line": 9, "column": 53, "index": 358}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "../platformFunctions/findNodeHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 359}, "end": {"line": 10, "column": 69, "index": 428}}], "key": "1isdGYORv8bBV0ZCFH0po00eajE=", "exportNames": ["*"]}}, {"name": "../shareableMappingCache", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 429}, "end": {"line": 11, "column": 65, "index": 494}}], "key": "6VuVYSBl2ZIM4DV3+e3oOxcYRw4=", "exportNames": ["*"]}}, {"name": "../shareables", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 495}, "end": {"line": 12, "column": 60, "index": 555}}], "key": "FdbDFSDZ4WXqE0/hGYXRyz7sZ44=", "exportNames": ["*"]}}, {"name": "./useSharedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 623}, "end": {"line": 14, "column": 50, "index": 673}}], "key": "6yldmc0IldDX63zJLZukWRMfHng=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedRef = useAnimatedRef;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _reactNative = require(_dependencyMap[1], \"react-native\");\n  var _fabricUtils = require(_dependencyMap[2], \"../fabricUtils\");\n  var _PlatformChecker = require(_dependencyMap[3], \"../PlatformChecker\");\n  var _findNodeHandle = require(_dependencyMap[4], \"../platformFunctions/findNodeHandle\");\n  var _shareableMappingCache = require(_dependencyMap[5], \"../shareableMappingCache\");\n  var _shareables = require(_dependencyMap[6], \"../shareables\");\n  var _useSharedValue = require(_dependencyMap[7], \"./useSharedValue\");\n  var IS_WEB = (0, _PlatformChecker.isWeb)();\n  function getComponentOrScrollable(component) {\n    if ((0, _PlatformChecker.isFabric)() && component.getNativeScrollRef) {\n      return component.getNativeScrollRef();\n    } else if (!(0, _PlatformChecker.isFabric)() && component.getScrollableNode) {\n      return component.getScrollableNode();\n    }\n    return component;\n  }\n\n  /**\n   * Lets you get a reference of a view that you can use inside a worklet.\n   *\n   * @returns An object with a `.current` property which contains an instance of a\n   *   component.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef\n   */\n  var _worklet_11891707279406_init_data = {\n    code: \"function useAnimatedRefTs1(){const{tag,viewName}=this.__closure;const f=function(){return tag.value;};f.viewName=viewName;return f;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\hook\\\\useAnimatedRef.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useAnimatedRefTs1\\\",\\\"tag\\\",\\\"viewName\\\",\\\"__closure\\\",\\\"f\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/hook/useAnimatedRef.ts\\\"],\\\"mappings\\\":\\\"AAwFc,SAAAA,iBAAMA,CAAA,QAAAC,GAAA,CAAAC,QAAA,OAAAC,SAAA,CAEZ,KAAM,CAAAC,CAAkB,CAAG,QAAAA,CAAA,QAAM,CAAAH,GAAG,CAACI,KAAK,GAC1CD,CAAC,CAACF,QAAQ,CAAGA,QAAQ,CACrB,MAAO,CAAAE,CAAC,CACV\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function useAnimatedRef() {\n    var tag = (0, _useSharedValue.useSharedValue)(-1);\n    var viewName = (0, _useSharedValue.useSharedValue)(null);\n    var ref = (0, _react.useRef)(null);\n    if (!ref.current) {\n      var fun = component => {\n        // enters when ref is set by attaching to a component\n        if (component) {\n          var getTagValueFunction = (0, _PlatformChecker.isFabric)() ? _fabricUtils.getShadowNodeWrapperFromRef : _findNodeHandle.findNodeHandle;\n          var getTagOrShadowNodeWrapper = () => {\n            return IS_WEB ? getComponentOrScrollable(component) : getTagValueFunction(getComponentOrScrollable(component));\n          };\n          tag.value = getTagOrShadowNodeWrapper();\n\n          // On Fabric we have to unwrap the tag from the shadow node wrapper\n          fun.getTag = (0, _PlatformChecker.isFabric)() ? () => (0, _findNodeHandle.findNodeHandle)(getComponentOrScrollable(component)) : getTagOrShadowNodeWrapper;\n          fun.current = component;\n          // viewName is required only on iOS with Paper\n          if (_reactNative.Platform.OS === 'ios' && !(0, _PlatformChecker.isFabric)()) {\n            viewName.value = component?.viewConfig?.uiViewClassName || 'RCTView';\n          }\n        }\n        return tag.value;\n      };\n      fun.current = null;\n      var animatedRefShareableHandle = (0, _shareables.makeShareableCloneRecursive)({\n        __init: function () {\n          var _e = [new global.Error(), -3, -27];\n          var useAnimatedRefTs1 = function () {\n            var f = () => tag.value;\n            f.viewName = viewName;\n            return f;\n          };\n          useAnimatedRefTs1.__closure = {\n            tag,\n            viewName\n          };\n          useAnimatedRefTs1.__workletHash = 11891707279406;\n          useAnimatedRefTs1.__initData = _worklet_11891707279406_init_data;\n          useAnimatedRefTs1.__stackDetails = _e;\n          return useAnimatedRefTs1;\n        }()\n      });\n      _shareableMappingCache.shareableMappingCache.set(fun, animatedRefShareableHandle);\n      ref.current = fun;\n    }\n    return ref.current;\n  }\n});", "lineCount": 87, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedRef"], [7, 24, 1, 13], [7, 27, 1, 13, "useAnimatedRef"], [7, 41, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_reactNative"], [9, 18, 5, 0], [9, 21, 5, 0, "require"], [9, 28, 5, 0], [9, 29, 5, 0, "_dependencyMap"], [9, 43, 5, 0], [10, 2, 8, 0], [10, 6, 8, 0, "_fabricUtils"], [10, 18, 8, 0], [10, 21, 8, 0, "require"], [10, 28, 8, 0], [10, 29, 8, 0, "_dependencyMap"], [10, 43, 8, 0], [11, 2, 9, 0], [11, 6, 9, 0, "_PlatformChecker"], [11, 22, 9, 0], [11, 25, 9, 0, "require"], [11, 32, 9, 0], [11, 33, 9, 0, "_dependencyMap"], [11, 47, 9, 0], [12, 2, 10, 0], [12, 6, 10, 0, "_findNodeHandle"], [12, 21, 10, 0], [12, 24, 10, 0, "require"], [12, 31, 10, 0], [12, 32, 10, 0, "_dependencyMap"], [12, 46, 10, 0], [13, 2, 11, 0], [13, 6, 11, 0, "_shareableMappingCache"], [13, 28, 11, 0], [13, 31, 11, 0, "require"], [13, 38, 11, 0], [13, 39, 11, 0, "_dependencyMap"], [13, 53, 11, 0], [14, 2, 12, 0], [14, 6, 12, 0, "_shareables"], [14, 17, 12, 0], [14, 20, 12, 0, "require"], [14, 27, 12, 0], [14, 28, 12, 0, "_dependencyMap"], [14, 42, 12, 0], [15, 2, 14, 0], [15, 6, 14, 0, "_useSharedValue"], [15, 21, 14, 0], [15, 24, 14, 0, "require"], [15, 31, 14, 0], [15, 32, 14, 0, "_dependencyMap"], [15, 46, 14, 0], [16, 2, 16, 0], [16, 6, 16, 6, "IS_WEB"], [16, 12, 16, 12], [16, 15, 16, 15], [16, 19, 16, 15, "isWeb"], [16, 41, 16, 20], [16, 43, 16, 21], [16, 44, 16, 22], [17, 2, 28, 0], [17, 11, 28, 9, "getComponentOrScrollable"], [17, 35, 28, 33, "getComponentOrScrollable"], [17, 36, 28, 34, "component"], [17, 45, 28, 69], [17, 47, 28, 71], [18, 4, 29, 2], [18, 8, 29, 6], [18, 12, 29, 6, "isF<PERSON><PERSON>"], [18, 37, 29, 14], [18, 39, 29, 15], [18, 40, 29, 16], [18, 44, 29, 20, "component"], [18, 53, 29, 29], [18, 54, 29, 30, "getNativeScrollRef"], [18, 72, 29, 48], [18, 74, 29, 50], [19, 6, 30, 4], [19, 13, 30, 11, "component"], [19, 22, 30, 20], [19, 23, 30, 21, "getNativeScrollRef"], [19, 41, 30, 39], [19, 42, 30, 40], [19, 43, 30, 41], [20, 4, 31, 2], [20, 5, 31, 3], [20, 11, 31, 9], [20, 15, 31, 13], [20, 16, 31, 14], [20, 20, 31, 14, "isF<PERSON><PERSON>"], [20, 45, 31, 22], [20, 47, 31, 23], [20, 48, 31, 24], [20, 52, 31, 28, "component"], [20, 61, 31, 37], [20, 62, 31, 38, "getScrollableNode"], [20, 79, 31, 55], [20, 81, 31, 57], [21, 6, 32, 4], [21, 13, 32, 11, "component"], [21, 22, 32, 20], [21, 23, 32, 21, "getScrollableNode"], [21, 40, 32, 38], [21, 41, 32, 39], [21, 42, 32, 40], [22, 4, 33, 2], [23, 4, 34, 2], [23, 11, 34, 9, "component"], [23, 20, 34, 18], [24, 2, 35, 0], [26, 2, 37, 0], [27, 0, 38, 0], [28, 0, 39, 0], [29, 0, 40, 0], [30, 0, 41, 0], [31, 0, 42, 0], [32, 0, 43, 0], [33, 2, 37, 0], [33, 6, 37, 0, "_worklet_11891707279406_init_data"], [33, 39, 37, 0], [34, 4, 37, 0, "code"], [34, 8, 37, 0], [35, 4, 37, 0, "location"], [35, 12, 37, 0], [36, 4, 37, 0, "sourceMap"], [36, 13, 37, 0], [37, 4, 37, 0, "version"], [37, 11, 37, 0], [38, 2, 37, 0], [39, 2, 44, 7], [39, 11, 44, 16, "useAnimatedRef"], [39, 25, 44, 30, "useAnimatedRef"], [39, 26, 44, 30], [39, 28, 46, 29], [40, 4, 47, 2], [40, 8, 47, 8, "tag"], [40, 11, 47, 11], [40, 14, 47, 14], [40, 18, 47, 14, "useSharedValue"], [40, 48, 47, 28], [40, 50, 47, 64], [40, 51, 47, 65], [40, 52, 47, 66], [40, 53, 47, 67], [41, 4, 48, 2], [41, 8, 48, 8, "viewName"], [41, 16, 48, 16], [41, 19, 48, 19], [41, 23, 48, 19, "useSharedValue"], [41, 53, 48, 33], [41, 55, 48, 49], [41, 59, 48, 53], [41, 60, 48, 54], [42, 4, 50, 2], [42, 8, 50, 8, "ref"], [42, 11, 50, 11], [42, 14, 50, 14], [42, 18, 50, 14, "useRef"], [42, 31, 50, 20], [42, 33, 50, 53], [42, 37, 50, 57], [42, 38, 50, 58], [43, 4, 52, 2], [43, 8, 52, 6], [43, 9, 52, 7, "ref"], [43, 12, 52, 10], [43, 13, 52, 11, "current"], [43, 20, 52, 18], [43, 22, 52, 20], [44, 6, 53, 4], [44, 10, 53, 10, "fun"], [44, 13, 53, 38], [44, 16, 54, 6, "component"], [44, 25, 54, 15], [44, 29, 55, 9], [45, 8, 56, 6], [46, 8, 57, 6], [46, 12, 57, 10, "component"], [46, 21, 57, 19], [46, 23, 57, 21], [47, 10, 58, 8], [47, 14, 58, 14, "getTagValueFunction"], [47, 33, 58, 33], [47, 36, 58, 36], [47, 40, 58, 36, "isF<PERSON><PERSON>"], [47, 65, 58, 44], [47, 67, 58, 45], [47, 68, 58, 46], [47, 71, 59, 12, "getShadowNodeWrapperFromRef"], [47, 111, 59, 39], [47, 114, 60, 12, "findNodeHandle"], [47, 144, 60, 26], [48, 10, 62, 8], [48, 14, 62, 14, "getTagOrShadowNodeWrapper"], [48, 39, 62, 39], [48, 42, 62, 42, "getTagOrShadowNodeWrapper"], [48, 43, 62, 42], [48, 48, 62, 48], [49, 12, 63, 10], [49, 19, 63, 17, "IS_WEB"], [49, 25, 63, 23], [49, 28, 64, 14, "getComponentOrScrollable"], [49, 52, 64, 38], [49, 53, 64, 39, "component"], [49, 62, 64, 48], [49, 63, 64, 49], [49, 66, 65, 14, "getTagValueFunction"], [49, 85, 65, 33], [49, 86, 65, 34, "getComponentOrScrollable"], [49, 110, 65, 58], [49, 111, 65, 59, "component"], [49, 120, 65, 68], [49, 121, 65, 69], [49, 122, 65, 70], [50, 10, 66, 8], [50, 11, 66, 9], [51, 10, 68, 8, "tag"], [51, 13, 68, 11], [51, 14, 68, 12, "value"], [51, 19, 68, 17], [51, 22, 68, 20, "getTagOrShadowNodeWrapper"], [51, 47, 68, 45], [51, 48, 68, 46], [51, 49, 68, 47], [53, 10, 70, 8], [54, 10, 71, 8, "fun"], [54, 13, 71, 11], [54, 14, 71, 12, "getTag"], [54, 20, 71, 18], [54, 23, 71, 21], [54, 27, 71, 21, "isF<PERSON><PERSON>"], [54, 52, 71, 29], [54, 54, 71, 30], [54, 55, 71, 31], [54, 58, 72, 12], [54, 64, 72, 18], [54, 68, 72, 18, "findNodeHandle"], [54, 98, 72, 32], [54, 100, 72, 33, "getComponentOrScrollable"], [54, 124, 72, 57], [54, 125, 72, 58, "component"], [54, 134, 72, 67], [54, 135, 72, 68], [54, 136, 72, 69], [54, 139, 73, 12, "getTagOrShadowNodeWrapper"], [54, 164, 73, 37], [55, 10, 75, 8, "fun"], [55, 13, 75, 11], [55, 14, 75, 12, "current"], [55, 21, 75, 19], [55, 24, 75, 22, "component"], [55, 33, 75, 31], [56, 10, 76, 8], [57, 10, 77, 8], [57, 14, 77, 12, "Platform"], [57, 35, 77, 20], [57, 36, 77, 21, "OS"], [57, 38, 77, 23], [57, 43, 77, 28], [57, 48, 77, 33], [57, 52, 77, 37], [57, 53, 77, 38], [57, 57, 77, 38, "isF<PERSON><PERSON>"], [57, 82, 77, 46], [57, 84, 77, 47], [57, 85, 77, 48], [57, 87, 77, 50], [58, 12, 78, 10, "viewName"], [58, 20, 78, 18], [58, 21, 78, 19, "value"], [58, 26, 78, 24], [58, 29, 79, 13, "component"], [58, 38, 79, 22], [58, 40, 79, 53, "viewConfig"], [58, 50, 79, 63], [58, 52, 80, 16, "uiViewClassName"], [58, 67, 80, 31], [58, 71, 80, 35], [58, 80, 80, 44], [59, 10, 81, 8], [60, 8, 82, 6], [61, 8, 83, 6], [61, 15, 83, 13, "tag"], [61, 18, 83, 16], [61, 19, 83, 17, "value"], [61, 24, 83, 22], [62, 6, 84, 4], [62, 7, 84, 6], [63, 6, 86, 4, "fun"], [63, 9, 86, 7], [63, 10, 86, 8, "current"], [63, 17, 86, 15], [63, 20, 86, 18], [63, 24, 86, 22], [64, 6, 88, 4], [64, 10, 88, 10, "animatedRefShareableHandle"], [64, 36, 88, 36], [64, 39, 88, 39], [64, 43, 88, 39, "makeShareableCloneRecursive"], [64, 82, 88, 66], [64, 84, 88, 67], [65, 8, 89, 6, "__init"], [65, 14, 89, 12], [65, 16, 89, 14], [66, 10, 89, 14], [66, 14, 89, 14, "_e"], [66, 16, 89, 14], [66, 24, 89, 14, "global"], [66, 30, 89, 14], [66, 31, 89, 14, "Error"], [66, 36, 89, 14], [67, 10, 89, 14], [67, 14, 89, 14, "useAnimatedRefTs1"], [67, 31, 89, 14], [67, 43, 89, 14, "useAnimatedRefTs1"], [67, 44, 89, 14], [67, 46, 89, 20], [68, 12, 91, 8], [68, 16, 91, 14, "f"], [68, 17, 91, 32], [68, 20, 91, 35, "f"], [68, 21, 91, 35], [68, 26, 91, 41, "tag"], [68, 29, 91, 44], [68, 30, 91, 45, "value"], [68, 35, 91, 50], [69, 12, 92, 8, "f"], [69, 13, 92, 9], [69, 14, 92, 10, "viewName"], [69, 22, 92, 18], [69, 25, 92, 21, "viewName"], [69, 33, 92, 29], [70, 12, 93, 8], [70, 19, 93, 15, "f"], [70, 20, 93, 16], [71, 10, 94, 6], [71, 11, 94, 7], [72, 10, 94, 7, "useAnimatedRefTs1"], [72, 27, 94, 7], [72, 28, 94, 7, "__closure"], [72, 37, 94, 7], [73, 12, 94, 7, "tag"], [73, 15, 94, 7], [74, 12, 94, 7, "viewName"], [75, 10, 94, 7], [76, 10, 94, 7, "useAnimatedRefTs1"], [76, 27, 94, 7], [76, 28, 94, 7, "__workletHash"], [76, 41, 94, 7], [77, 10, 94, 7, "useAnimatedRefTs1"], [77, 27, 94, 7], [77, 28, 94, 7, "__initData"], [77, 38, 94, 7], [77, 41, 94, 7, "_worklet_11891707279406_init_data"], [77, 74, 94, 7], [78, 10, 94, 7, "useAnimatedRefTs1"], [78, 27, 94, 7], [78, 28, 94, 7, "__stackDetails"], [78, 42, 94, 7], [78, 45, 94, 7, "_e"], [78, 47, 94, 7], [79, 10, 94, 7], [79, 17, 94, 7, "useAnimatedRefTs1"], [79, 34, 94, 7], [80, 8, 94, 7], [80, 9, 89, 14], [81, 6, 95, 4], [81, 7, 95, 5], [81, 8, 95, 6], [82, 6, 96, 4, "shareableMappingCache"], [82, 50, 96, 25], [82, 51, 96, 26, "set"], [82, 54, 96, 29], [82, 55, 96, 30, "fun"], [82, 58, 96, 33], [82, 60, 96, 35, "animatedRefShareableHandle"], [82, 86, 96, 61], [82, 87, 96, 62], [83, 6, 97, 4, "ref"], [83, 9, 97, 7], [83, 10, 97, 8, "current"], [83, 17, 97, 15], [83, 20, 97, 18, "fun"], [83, 23, 97, 21], [84, 4, 98, 2], [85, 4, 100, 2], [85, 11, 100, 9, "ref"], [85, 14, 100, 12], [85, 15, 100, 13, "current"], [85, 22, 100, 20], [86, 2, 101, 0], [87, 0, 101, 1], [87, 3]], "functionMap": {"names": ["<global>", "getComponentOrScrollable", "useAnimatedRef", "<anonymous>", "getTagOrShadowNodeWrapper", "makeShareableCloneRecursive$argument_0.__init", "f"], "mappings": "AAA;AC2B;CDO;OES;mECS;0CCS;SDI;KDkB;cGK;mCCE,eD;OHG;CFO"}}, "type": "js/module"}]}