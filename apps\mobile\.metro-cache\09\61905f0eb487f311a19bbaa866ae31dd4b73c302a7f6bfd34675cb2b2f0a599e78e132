{"dependencies": [{"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 89}, "end": {"line": 3, "column": 53, "index": 142}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.rubberBandDecay = void 0;\n  var _utils = require(_dependencyMap[0], \"./utils\");\n  var DERIVATIVE_EPS = 0.1;\n  var _worklet_5974929077163_init_data = {\n    code: \"function rubberBandDecay_rubberBandDecayTs1(animation,now,config){const{SLOPE_FACTOR,DERIVATIVE_EPS,VELOCITY_EPS}=this.__closure;const{lastTimestamp:lastTimestamp,startTimestamp:startTimestamp,current:current,velocity:velocity}=animation;const deltaTime=Math.min(now-lastTimestamp,64);const clampIndex=Math.abs(current-config.clamp[0])<Math.abs(current-config.clamp[1])?0:1;let derivative=0;if(current<config.clamp[0]||current>config.clamp[1]){derivative=current-config.clamp[clampIndex];}const v=velocity*Math.exp(-(1-config.deceleration)*(now-startTimestamp)*SLOPE_FACTOR)-derivative*config.rubberBandFactor;if(Math.abs(derivative)>DERIVATIVE_EPS){animation.springActive=true;}else if(animation.springActive){animation.current=config.clamp[clampIndex];return true;}else if(Math.abs(v)<VELOCITY_EPS){return true;}animation.current=current+v*config.velocityFactor*deltaTime/1000;animation.velocity=v;animation.lastTimestamp=now;return false;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\animation\\\\decay\\\\rubberBandDecay.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"rubberBandDecay_rubberBandDecayTs1\\\",\\\"animation\\\",\\\"now\\\",\\\"config\\\",\\\"SLOPE_FACTOR\\\",\\\"DERIVATIVE_EPS\\\",\\\"VELOCITY_EPS\\\",\\\"__closure\\\",\\\"lastTimestamp\\\",\\\"startTimestamp\\\",\\\"current\\\",\\\"velocity\\\",\\\"deltaTime\\\",\\\"Math\\\",\\\"min\\\",\\\"clampIndex\\\",\\\"abs\\\",\\\"clamp\\\",\\\"derivative\\\",\\\"v\\\",\\\"exp\\\",\\\"deceleration\\\",\\\"rubberBandFactor\\\",\\\"springActive\\\",\\\"velocityFactor\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/animation/decay/rubberBandDecay.ts\\\"],\\\"mappings\\\":\\\"AAMO,SAAAA,kCAGLA,CAAAC,SACS,CAAAC,GAAA,CAAAC,MAAA,QAAAC,YAAA,CAAAC,cAAA,CAAAC,YAAA,OAAAC,SAAA,CAET,KAAM,CAAEC,aAAa,CAAbA,aAAa,CAAEC,cAAc,CAAdA,cAAc,CAAEC,OAAO,CAAPA,OAAO,CAAEC,QAAA,CAAAA,QAAS,CAAC,CAAGV,SAAS,CAEtE,KAAM,CAAAW,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACZ,GAAG,CAAGM,aAAa,CAAE,EAAE,CAAC,CACnD,KAAM,CAAAO,UAAU,CACdF,IAAI,CAACG,GAAG,CAACN,OAAO,CAAGP,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAGJ,IAAI,CAACG,GAAG,CAACN,OAAO,CAAGP,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC,CACrE,CAAC,CACD,CAAC,CAEP,GAAI,CAAAC,UAAU,CAAG,CAAC,CAClB,GAAIR,OAAO,CAAGP,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,EAAIP,OAAO,CAAGP,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAAE,CAC1DC,UAAU,CAAGR,OAAO,CAAGP,MAAM,CAACc,KAAK,CAACF,UAAU,CAAC,CACjD,CAEA,KAAM,CAAAI,CAAC,CACLR,QAAQ,CACNE,IAAI,CAACO,GAAG,CACN,EAAE,CAAC,CAAGjB,MAAM,CAACkB,YAAY,CAAC,EAAInB,GAAG,CAAGO,cAAc,CAAC,CAAGL,YACxD,CAAC,CACHc,UAAU,CAAGf,MAAM,CAACmB,gBAAgB,CAEtC,GAAIT,IAAI,CAACG,GAAG,CAACE,UAAU,CAAC,CAAGb,cAAc,CAAE,CACzCJ,SAAS,CAACsB,YAAY,CAAG,IAAI,CAC/B,CAAC,IAAM,IAAItB,SAAS,CAACsB,YAAY,CAAE,CACjCtB,SAAS,CAACS,OAAO,CAAGP,MAAM,CAACc,KAAK,CAACF,UAAU,CAAC,CAC5C,MAAO,KAAI,CACb,CAAC,IAAM,IAAIF,IAAI,CAACG,GAAG,CAACG,CAAC,CAAC,CAAGb,YAAY,CAAE,CACrC,MAAO,KAAI,CACb,CAEAL,SAAS,CAACS,OAAO,CAAGA,OAAO,CAAIS,CAAC,CAAGhB,MAAM,CAACqB,cAAc,CAAGZ,SAAS,CAAI,IAAI,CAC5EX,SAAS,CAACU,QAAQ,CAAGQ,CAAC,CACtBlB,SAAS,CAACO,aAAa,CAAGN,GAAG,CAC7B,MAAO,MAAK,CACd\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var rubberBandDecay = exports.rubberBandDecay = function () {\n    var _e = [new global.Error(), -4, -27];\n    var rubberBandDecay = function (animation, now, config) {\n      var lastTimestamp = animation.lastTimestamp,\n        startTimestamp = animation.startTimestamp,\n        current = animation.current,\n        velocity = animation.velocity;\n      var deltaTime = Math.min(now - lastTimestamp, 64);\n      var clampIndex = Math.abs(current - config.clamp[0]) < Math.abs(current - config.clamp[1]) ? 0 : 1;\n      var derivative = 0;\n      if (current < config.clamp[0] || current > config.clamp[1]) {\n        derivative = current - config.clamp[clampIndex];\n      }\n      var v = velocity * Math.exp(-(1 - config.deceleration) * (now - startTimestamp) * _utils.SLOPE_FACTOR) - derivative * config.rubberBandFactor;\n      if (Math.abs(derivative) > DERIVATIVE_EPS) {\n        animation.springActive = true;\n      } else if (animation.springActive) {\n        animation.current = config.clamp[clampIndex];\n        return true;\n      } else if (Math.abs(v) < _utils.VELOCITY_EPS) {\n        return true;\n      }\n      animation.current = current + v * config.velocityFactor * deltaTime / 1000;\n      animation.velocity = v;\n      animation.lastTimestamp = now;\n      return false;\n    };\n    rubberBandDecay.__closure = {\n      SLOPE_FACTOR: _utils.SLOPE_FACTOR,\n      DERIVATIVE_EPS,\n      VELOCITY_EPS: _utils.VELOCITY_EPS\n    };\n    rubberBandDecay.__workletHash = 5974929077163;\n    rubberBandDecay.__initData = _worklet_5974929077163_init_data;\n    rubberBandDecay.__stackDetails = _e;\n    return rubberBandDecay;\n  }();\n});", "lineCount": 53, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "rubberBandDecay"], [7, 25, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_utils"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 5, 0], [9, 6, 5, 6, "DERIVATIVE_EPS"], [9, 20, 5, 20], [9, 23, 5, 23], [9, 26, 5, 26], [10, 2, 5, 27], [10, 6, 5, 27, "_worklet_5974929077163_init_data"], [10, 38, 5, 27], [11, 4, 5, 27, "code"], [11, 8, 5, 27], [12, 4, 5, 27, "location"], [12, 12, 5, 27], [13, 4, 5, 27, "sourceMap"], [13, 13, 5, 27], [14, 4, 5, 27, "version"], [14, 11, 5, 27], [15, 2, 5, 27], [16, 2, 5, 27], [16, 6, 5, 27, "rubberBandDecay"], [16, 21, 5, 27], [16, 24, 5, 27, "exports"], [16, 31, 5, 27], [16, 32, 5, 27, "rubberBandDecay"], [16, 47, 5, 27], [16, 50, 7, 7], [17, 4, 7, 7], [17, 8, 7, 7, "_e"], [17, 10, 7, 7], [17, 18, 7, 7, "global"], [17, 24, 7, 7], [17, 25, 7, 7, "Error"], [17, 30, 7, 7], [18, 4, 7, 7], [18, 8, 7, 7, "rubberBandDecay"], [18, 23, 7, 7], [18, 35, 7, 7, "rubberBandDecay"], [18, 36, 8, 2, "animation"], [18, 45, 8, 32], [18, 47, 9, 2, "now"], [18, 50, 9, 13], [18, 52, 10, 2, "config"], [18, 58, 10, 31], [18, 60, 11, 11], [19, 6, 13, 2], [19, 10, 13, 10, "lastTimestamp"], [19, 23, 13, 23], [19, 26, 13, 63, "animation"], [19, 35, 13, 72], [19, 36, 13, 10, "lastTimestamp"], [19, 49, 13, 23], [20, 8, 13, 25, "startTimestamp"], [20, 22, 13, 39], [20, 25, 13, 63, "animation"], [20, 34, 13, 72], [20, 35, 13, 25, "startTimestamp"], [20, 49, 13, 39], [21, 8, 13, 41, "current"], [21, 15, 13, 48], [21, 18, 13, 63, "animation"], [21, 27, 13, 72], [21, 28, 13, 41, "current"], [21, 35, 13, 48], [22, 8, 13, 50, "velocity"], [22, 16, 13, 58], [22, 19, 13, 63, "animation"], [22, 28, 13, 72], [22, 29, 13, 50, "velocity"], [22, 37, 13, 58], [23, 6, 15, 2], [23, 10, 15, 8, "deltaTime"], [23, 19, 15, 17], [23, 22, 15, 20, "Math"], [23, 26, 15, 24], [23, 27, 15, 25, "min"], [23, 30, 15, 28], [23, 31, 15, 29, "now"], [23, 34, 15, 32], [23, 37, 15, 35, "lastTimestamp"], [23, 50, 15, 48], [23, 52, 15, 50], [23, 54, 15, 52], [23, 55, 15, 53], [24, 6, 16, 2], [24, 10, 16, 8, "clampIndex"], [24, 20, 16, 18], [24, 23, 17, 4, "Math"], [24, 27, 17, 8], [24, 28, 17, 9, "abs"], [24, 31, 17, 12], [24, 32, 17, 13, "current"], [24, 39, 17, 20], [24, 42, 17, 23, "config"], [24, 48, 17, 29], [24, 49, 17, 30, "clamp"], [24, 54, 17, 35], [24, 55, 17, 36], [24, 56, 17, 37], [24, 57, 17, 38], [24, 58, 17, 39], [24, 61, 17, 42, "Math"], [24, 65, 17, 46], [24, 66, 17, 47, "abs"], [24, 69, 17, 50], [24, 70, 17, 51, "current"], [24, 77, 17, 58], [24, 80, 17, 61, "config"], [24, 86, 17, 67], [24, 87, 17, 68, "clamp"], [24, 92, 17, 73], [24, 93, 17, 74], [24, 94, 17, 75], [24, 95, 17, 76], [24, 96, 17, 77], [24, 99, 18, 8], [24, 100, 18, 9], [24, 103, 19, 8], [24, 104, 19, 9], [25, 6, 21, 2], [25, 10, 21, 6, "derivative"], [25, 20, 21, 16], [25, 23, 21, 19], [25, 24, 21, 20], [26, 6, 22, 2], [26, 10, 22, 6, "current"], [26, 17, 22, 13], [26, 20, 22, 16, "config"], [26, 26, 22, 22], [26, 27, 22, 23, "clamp"], [26, 32, 22, 28], [26, 33, 22, 29], [26, 34, 22, 30], [26, 35, 22, 31], [26, 39, 22, 35, "current"], [26, 46, 22, 42], [26, 49, 22, 45, "config"], [26, 55, 22, 51], [26, 56, 22, 52, "clamp"], [26, 61, 22, 57], [26, 62, 22, 58], [26, 63, 22, 59], [26, 64, 22, 60], [26, 66, 22, 62], [27, 8, 23, 4, "derivative"], [27, 18, 23, 14], [27, 21, 23, 17, "current"], [27, 28, 23, 24], [27, 31, 23, 27, "config"], [27, 37, 23, 33], [27, 38, 23, 34, "clamp"], [27, 43, 23, 39], [27, 44, 23, 40, "clampIndex"], [27, 54, 23, 50], [27, 55, 23, 51], [28, 6, 24, 2], [29, 6, 26, 2], [29, 10, 26, 8, "v"], [29, 11, 26, 9], [29, 14, 27, 4, "velocity"], [29, 22, 27, 12], [29, 25, 28, 6, "Math"], [29, 29, 28, 10], [29, 30, 28, 11, "exp"], [29, 33, 28, 14], [29, 34, 29, 8], [29, 36, 29, 10], [29, 37, 29, 11], [29, 40, 29, 14, "config"], [29, 46, 29, 20], [29, 47, 29, 21, "deceleration"], [29, 59, 29, 33], [29, 60, 29, 34], [29, 64, 29, 38, "now"], [29, 67, 29, 41], [29, 70, 29, 44, "startTimestamp"], [29, 84, 29, 58], [29, 85, 29, 59], [29, 88, 29, 62, "SLOPE_FACTOR"], [29, 107, 30, 6], [29, 108, 30, 7], [29, 111, 31, 4, "derivative"], [29, 121, 31, 14], [29, 124, 31, 17, "config"], [29, 130, 31, 23], [29, 131, 31, 24, "rubberBandFactor"], [29, 147, 31, 40], [30, 6, 33, 2], [30, 10, 33, 6, "Math"], [30, 14, 33, 10], [30, 15, 33, 11, "abs"], [30, 18, 33, 14], [30, 19, 33, 15, "derivative"], [30, 29, 33, 25], [30, 30, 33, 26], [30, 33, 33, 29, "DERIVATIVE_EPS"], [30, 47, 33, 43], [30, 49, 33, 45], [31, 8, 34, 4, "animation"], [31, 17, 34, 13], [31, 18, 34, 14, "springActive"], [31, 30, 34, 26], [31, 33, 34, 29], [31, 37, 34, 33], [32, 6, 35, 2], [32, 7, 35, 3], [32, 13, 35, 9], [32, 17, 35, 13, "animation"], [32, 26, 35, 22], [32, 27, 35, 23, "springActive"], [32, 39, 35, 35], [32, 41, 35, 37], [33, 8, 36, 4, "animation"], [33, 17, 36, 13], [33, 18, 36, 14, "current"], [33, 25, 36, 21], [33, 28, 36, 24, "config"], [33, 34, 36, 30], [33, 35, 36, 31, "clamp"], [33, 40, 36, 36], [33, 41, 36, 37, "clampIndex"], [33, 51, 36, 47], [33, 52, 36, 48], [34, 8, 37, 4], [34, 15, 37, 11], [34, 19, 37, 15], [35, 6, 38, 2], [35, 7, 38, 3], [35, 13, 38, 9], [35, 17, 38, 13, "Math"], [35, 21, 38, 17], [35, 22, 38, 18, "abs"], [35, 25, 38, 21], [35, 26, 38, 22, "v"], [35, 27, 38, 23], [35, 28, 38, 24], [35, 31, 38, 27, "VELOCITY_EPS"], [35, 50, 38, 39], [35, 52, 38, 41], [36, 8, 39, 4], [36, 15, 39, 11], [36, 19, 39, 15], [37, 6, 40, 2], [38, 6, 42, 2, "animation"], [38, 15, 42, 11], [38, 16, 42, 12, "current"], [38, 23, 42, 19], [38, 26, 42, 22, "current"], [38, 33, 42, 29], [38, 36, 42, 33, "v"], [38, 37, 42, 34], [38, 40, 42, 37, "config"], [38, 46, 42, 43], [38, 47, 42, 44, "velocityFactor"], [38, 61, 42, 58], [38, 64, 42, 61, "deltaTime"], [38, 73, 42, 70], [38, 76, 42, 74], [38, 80, 42, 78], [39, 6, 43, 2, "animation"], [39, 15, 43, 11], [39, 16, 43, 12, "velocity"], [39, 24, 43, 20], [39, 27, 43, 23, "v"], [39, 28, 43, 24], [40, 6, 44, 2, "animation"], [40, 15, 44, 11], [40, 16, 44, 12, "lastTimestamp"], [40, 29, 44, 25], [40, 32, 44, 28, "now"], [40, 35, 44, 31], [41, 6, 45, 2], [41, 13, 45, 9], [41, 18, 45, 14], [42, 4, 46, 0], [42, 5, 46, 1], [43, 4, 46, 1, "rubberBandDecay"], [43, 19, 46, 1], [43, 20, 46, 1, "__closure"], [43, 29, 46, 1], [44, 6, 46, 1, "SLOPE_FACTOR"], [44, 18, 46, 1], [44, 20, 29, 62, "SLOPE_FACTOR"], [44, 39, 29, 74], [45, 6, 29, 74, "DERIVATIVE_EPS"], [45, 20, 29, 74], [46, 6, 29, 74, "VELOCITY_EPS"], [46, 18, 29, 74], [46, 20, 38, 27, "VELOCITY_EPS"], [47, 4, 38, 39], [48, 4, 38, 39, "rubberBandDecay"], [48, 19, 38, 39], [48, 20, 38, 39, "__workletHash"], [48, 33, 38, 39], [49, 4, 38, 39, "rubberBandDecay"], [49, 19, 38, 39], [49, 20, 38, 39, "__initData"], [49, 30, 38, 39], [49, 33, 38, 39, "_worklet_5974929077163_init_data"], [49, 65, 38, 39], [50, 4, 38, 39, "rubberBandDecay"], [50, 19, 38, 39], [50, 20, 38, 39, "__stackDetails"], [50, 34, 38, 39], [50, 37, 38, 39, "_e"], [50, 39, 38, 39], [51, 4, 38, 39], [51, 11, 38, 39, "rubberBandDecay"], [51, 26, 38, 39], [52, 2, 38, 39], [52, 3, 7, 7], [53, 0, 7, 7], [53, 3]], "functionMap": {"names": ["<global>", "rubberBandDecay"], "mappings": "AAA;OCM;CDuC"}}, "type": "js/module"}]}