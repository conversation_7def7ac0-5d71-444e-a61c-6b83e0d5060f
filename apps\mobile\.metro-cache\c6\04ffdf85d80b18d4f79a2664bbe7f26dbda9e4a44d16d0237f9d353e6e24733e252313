{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 68, "index": 68}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.hoverGestureHandlerProps = exports.HoverGesture = exports.HoverEffect = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _gesture = require(_dependencyMap[7], \"./gesture\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var HoverEffect = exports.HoverEffect = /*#__PURE__*/function (HoverEffect) {\n    HoverEffect[HoverEffect[\"NONE\"] = 0] = \"NONE\";\n    HoverEffect[HoverEffect[\"LIFT\"] = 1] = \"LIFT\";\n    HoverEffect[HoverEffect[\"HIGHLIGHT\"] = 2] = \"HIGHLIGHT\";\n    return HoverEffect;\n  }({});\n  var hoverGestureHandlerProps = exports.hoverGestureHandlerProps = ['hoverEffect'];\n  var _worklet_921424365338_init_data = {\n    code: \"function changeEventCalculator_hoverGestureTs1(current,previous){let changePayload;if(previous===undefined){changePayload={changeX:current.x,changeY:current.y};}else{changePayload={changeX:current.x-previous.x,changeY:current.y-previous.y};}return{...current,...changePayload};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\gestures\\\\hoverGesture.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"changeEventCalculator_hoverGestureTs1\\\",\\\"current\\\",\\\"previous\\\",\\\"changePayload\\\",\\\"undefined\\\",\\\"changeX\\\",\\\"x\\\",\\\"changeY\\\",\\\"y\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-gesture-handler/src/handlers/gestures/hoverGesture.ts\\\"],\\\"mappings\\\":\\\"AAqBA,SAAAA,qCAEEA,CAAAC,OACA,CAAAC,QAAA,EAEA,GAAI,CAAAC,aAA6C,CACjD,GAAID,QAAQ,GAAKE,SAAS,CAAE,CAC1BD,aAAa,CAAG,CACdE,OAAO,CAAEJ,OAAO,CAACK,CAAC,CAClBC,OAAO,CAAEN,OAAO,CAACO,CACnB,CAAC,CACH,CAAC,IAAM,CACLL,aAAa,CAAG,CACdE,OAAO,CAAEJ,OAAO,CAACK,CAAC,CAAGJ,QAAQ,CAACI,CAAC,CAC/BC,OAAO,CAAEN,OAAO,CAACO,CAAC,CAAGN,QAAQ,CAACM,CAChC,CAAC,CACH,CAEA,MAAO,CAAE,GAAGP,OAAO,CAAE,GAAGE,aAAc,CAAC,CACzC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var changeEventCalculator = function () {\n    var _e = [new global.Error(), 1, -27];\n    var changeEventCalculator = function (current, previous) {\n      var changePayload;\n      if (previous === undefined) {\n        changePayload = {\n          changeX: current.x,\n          changeY: current.y\n        };\n      } else {\n        changePayload = {\n          changeX: current.x - previous.x,\n          changeY: current.y - previous.y\n        };\n      }\n      return {\n        ...current,\n        ...changePayload\n      };\n    };\n    changeEventCalculator.__closure = {};\n    changeEventCalculator.__workletHash = 921424365338;\n    changeEventCalculator.__initData = _worklet_921424365338_init_data;\n    changeEventCalculator.__stackDetails = _e;\n    return changeEventCalculator;\n  }();\n  var HoverGesture = exports.HoverGesture = /*#__PURE__*/function (_ContinousBaseGesture) {\n    function HoverGesture() {\n      var _this;\n      (0, _classCallCheck2.default)(this, HoverGesture);\n      _this = _callSuper(this, HoverGesture);\n      _this.config = {};\n      _this.handlerName = 'HoverGestureHandler';\n      return _this;\n    }\n\n    /**\n     * #### iOS only\n     * Sets the visual hover effect.\n     */\n    (0, _inherits2.default)(HoverGesture, _ContinousBaseGesture);\n    return (0, _createClass2.default)(HoverGesture, [{\n      key: \"effect\",\n      value: function effect(_effect) {\n        this.config.hoverEffect = _effect;\n        return this;\n      }\n    }, {\n      key: \"onChange\",\n      value: function onChange(callback) {\n        // @ts-ignore TS being overprotective, HoverGestureHandlerEventPayload is Record\n        this.handlers.changeEventCalculator = changeEventCalculator;\n        return _superPropGet(HoverGesture, \"onChange\", this, 3)([callback]);\n      }\n    }]);\n  }(_gesture.ContinousBaseGesture);\n});", "lineCount": 86, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_gesture"], [13, 14, 1, 0], [13, 17, 1, 0, "require"], [13, 24, 1, 0], [13, 25, 1, 0, "_dependencyMap"], [13, 39, 1, 0], [14, 2, 1, 68], [14, 11, 1, 68, "_callSuper"], [14, 22, 1, 68, "t"], [14, 23, 1, 68], [14, 25, 1, 68, "o"], [14, 26, 1, 68], [14, 28, 1, 68, "e"], [14, 29, 1, 68], [14, 40, 1, 68, "o"], [14, 41, 1, 68], [14, 48, 1, 68, "_getPrototypeOf2"], [14, 64, 1, 68], [14, 65, 1, 68, "default"], [14, 72, 1, 68], [14, 74, 1, 68, "o"], [14, 75, 1, 68], [14, 82, 1, 68, "_possibleConstructorReturn2"], [14, 109, 1, 68], [14, 110, 1, 68, "default"], [14, 117, 1, 68], [14, 119, 1, 68, "t"], [14, 120, 1, 68], [14, 122, 1, 68, "_isNativeReflectConstruct"], [14, 147, 1, 68], [14, 152, 1, 68, "Reflect"], [14, 159, 1, 68], [14, 160, 1, 68, "construct"], [14, 169, 1, 68], [14, 170, 1, 68, "o"], [14, 171, 1, 68], [14, 173, 1, 68, "e"], [14, 174, 1, 68], [14, 186, 1, 68, "_getPrototypeOf2"], [14, 202, 1, 68], [14, 203, 1, 68, "default"], [14, 210, 1, 68], [14, 212, 1, 68, "t"], [14, 213, 1, 68], [14, 215, 1, 68, "constructor"], [14, 226, 1, 68], [14, 230, 1, 68, "o"], [14, 231, 1, 68], [14, 232, 1, 68, "apply"], [14, 237, 1, 68], [14, 238, 1, 68, "t"], [14, 239, 1, 68], [14, 241, 1, 68, "e"], [14, 242, 1, 68], [15, 2, 1, 68], [15, 11, 1, 68, "_isNativeReflectConstruct"], [15, 37, 1, 68], [15, 51, 1, 68, "t"], [15, 52, 1, 68], [15, 56, 1, 68, "Boolean"], [15, 63, 1, 68], [15, 64, 1, 68, "prototype"], [15, 73, 1, 68], [15, 74, 1, 68, "valueOf"], [15, 81, 1, 68], [15, 82, 1, 68, "call"], [15, 86, 1, 68], [15, 87, 1, 68, "Reflect"], [15, 94, 1, 68], [15, 95, 1, 68, "construct"], [15, 104, 1, 68], [15, 105, 1, 68, "Boolean"], [15, 112, 1, 68], [15, 145, 1, 68, "t"], [15, 146, 1, 68], [15, 159, 1, 68, "_isNativeReflectConstruct"], [15, 184, 1, 68], [15, 196, 1, 68, "_isNativeReflectConstruct"], [15, 197, 1, 68], [15, 210, 1, 68, "t"], [15, 211, 1, 68], [16, 2, 1, 68], [16, 11, 1, 68, "_superPropGet"], [16, 25, 1, 68, "t"], [16, 26, 1, 68], [16, 28, 1, 68, "o"], [16, 29, 1, 68], [16, 31, 1, 68, "e"], [16, 32, 1, 68], [16, 34, 1, 68, "r"], [16, 35, 1, 68], [16, 43, 1, 68, "p"], [16, 44, 1, 68], [16, 51, 1, 68, "_get2"], [16, 56, 1, 68], [16, 57, 1, 68, "default"], [16, 64, 1, 68], [16, 70, 1, 68, "_getPrototypeOf2"], [16, 86, 1, 68], [16, 87, 1, 68, "default"], [16, 94, 1, 68], [16, 100, 1, 68, "r"], [16, 101, 1, 68], [16, 104, 1, 68, "t"], [16, 105, 1, 68], [16, 106, 1, 68, "prototype"], [16, 115, 1, 68], [16, 118, 1, 68, "t"], [16, 119, 1, 68], [16, 122, 1, 68, "o"], [16, 123, 1, 68], [16, 125, 1, 68, "e"], [16, 126, 1, 68], [16, 140, 1, 68, "r"], [16, 141, 1, 68], [16, 166, 1, 68, "p"], [16, 167, 1, 68], [16, 180, 1, 68, "t"], [16, 181, 1, 68], [16, 192, 1, 68, "p"], [16, 193, 1, 68], [16, 194, 1, 68, "apply"], [16, 199, 1, 68], [16, 200, 1, 68, "e"], [16, 201, 1, 68], [16, 203, 1, 68, "t"], [16, 204, 1, 68], [16, 211, 1, 68, "p"], [16, 212, 1, 68], [17, 2, 1, 68], [17, 6, 10, 12, "HoverEffect"], [17, 17, 10, 23], [17, 20, 10, 23, "exports"], [17, 27, 10, 23], [17, 28, 10, 23, "HoverEffect"], [17, 39, 10, 23], [17, 65, 10, 12, "HoverEffect"], [17, 76, 10, 23], [18, 4, 10, 12, "HoverEffect"], [18, 15, 10, 23], [18, 16, 10, 12, "HoverEffect"], [18, 27, 10, 23], [19, 4, 10, 12, "HoverEffect"], [19, 15, 10, 23], [19, 16, 10, 12, "HoverEffect"], [19, 27, 10, 23], [20, 4, 10, 12, "HoverEffect"], [20, 15, 10, 23], [20, 16, 10, 12, "HoverEffect"], [20, 27, 10, 23], [21, 4, 10, 23], [21, 11, 10, 12, "HoverEffect"], [21, 22, 10, 23], [22, 2, 10, 23], [23, 2, 20, 7], [23, 6, 20, 13, "hoverGestureHandlerProps"], [23, 30, 20, 37], [23, 33, 20, 37, "exports"], [23, 40, 20, 37], [23, 41, 20, 37, "hoverGestureHandlerProps"], [23, 65, 20, 37], [23, 68, 20, 40], [23, 69, 20, 41], [23, 82, 20, 54], [23, 83, 20, 64], [24, 2, 20, 65], [24, 6, 20, 65, "_worklet_921424365338_init_data"], [24, 37, 20, 65], [25, 4, 20, 65, "code"], [25, 8, 20, 65], [26, 4, 20, 65, "location"], [26, 12, 20, 65], [27, 4, 20, 65, "sourceMap"], [27, 13, 20, 65], [28, 4, 20, 65, "version"], [28, 11, 20, 65], [29, 2, 20, 65], [30, 2, 20, 65], [30, 6, 20, 65, "changeEventCalculator"], [30, 27, 20, 65], [30, 30, 22, 0], [31, 4, 22, 0], [31, 8, 22, 0, "_e"], [31, 10, 22, 0], [31, 18, 22, 0, "global"], [31, 24, 22, 0], [31, 25, 22, 0, "Error"], [31, 30, 22, 0], [32, 4, 22, 0], [32, 8, 22, 0, "changeEventCalculator"], [32, 29, 22, 0], [32, 41, 22, 0, "changeEventCalculator"], [32, 42, 23, 2, "current"], [32, 49, 23, 62], [32, 51, 24, 2, "previous"], [32, 59, 24, 64], [32, 61, 25, 2], [33, 6, 27, 2], [33, 10, 27, 6, "changePayload"], [33, 23, 27, 51], [34, 6, 28, 2], [34, 10, 28, 6, "previous"], [34, 18, 28, 14], [34, 23, 28, 19, "undefined"], [34, 32, 28, 28], [34, 34, 28, 30], [35, 8, 29, 4, "changePayload"], [35, 21, 29, 17], [35, 24, 29, 20], [36, 10, 30, 6, "changeX"], [36, 17, 30, 13], [36, 19, 30, 15, "current"], [36, 26, 30, 22], [36, 27, 30, 23, "x"], [36, 28, 30, 24], [37, 10, 31, 6, "changeY"], [37, 17, 31, 13], [37, 19, 31, 15, "current"], [37, 26, 31, 22], [37, 27, 31, 23, "y"], [38, 8, 32, 4], [38, 9, 32, 5], [39, 6, 33, 2], [39, 7, 33, 3], [39, 13, 33, 9], [40, 8, 34, 4, "changePayload"], [40, 21, 34, 17], [40, 24, 34, 20], [41, 10, 35, 6, "changeX"], [41, 17, 35, 13], [41, 19, 35, 15, "current"], [41, 26, 35, 22], [41, 27, 35, 23, "x"], [41, 28, 35, 24], [41, 31, 35, 27, "previous"], [41, 39, 35, 35], [41, 40, 35, 36, "x"], [41, 41, 35, 37], [42, 10, 36, 6, "changeY"], [42, 17, 36, 13], [42, 19, 36, 15, "current"], [42, 26, 36, 22], [42, 27, 36, 23, "y"], [42, 28, 36, 24], [42, 31, 36, 27, "previous"], [42, 39, 36, 35], [42, 40, 36, 36, "y"], [43, 8, 37, 4], [43, 9, 37, 5], [44, 6, 38, 2], [45, 6, 40, 2], [45, 13, 40, 9], [46, 8, 40, 11], [46, 11, 40, 14, "current"], [46, 18, 40, 21], [47, 8, 40, 23], [47, 11, 40, 26, "changePayload"], [48, 6, 40, 40], [48, 7, 40, 41], [49, 4, 41, 0], [49, 5, 41, 1], [50, 4, 41, 1, "changeEventCalculator"], [50, 25, 41, 1], [50, 26, 41, 1, "__closure"], [50, 35, 41, 1], [51, 4, 41, 1, "changeEventCalculator"], [51, 25, 41, 1], [51, 26, 41, 1, "__workletHash"], [51, 39, 41, 1], [52, 4, 41, 1, "changeEventCalculator"], [52, 25, 41, 1], [52, 26, 41, 1, "__initData"], [52, 36, 41, 1], [52, 39, 41, 1, "_worklet_921424365338_init_data"], [52, 70, 41, 1], [53, 4, 41, 1, "changeEventCalculator"], [53, 25, 41, 1], [53, 26, 41, 1, "__stackDetails"], [53, 40, 41, 1], [53, 43, 41, 1, "_e"], [53, 45, 41, 1], [54, 4, 41, 1], [54, 11, 41, 1, "changeEventCalculator"], [54, 32, 41, 1], [55, 2, 41, 1], [55, 3, 22, 0], [56, 2, 22, 0], [56, 6, 43, 13, "HoverGesture"], [56, 18, 43, 25], [56, 21, 43, 25, "exports"], [56, 28, 43, 25], [56, 29, 43, 25, "HoverGesture"], [56, 41, 43, 25], [56, 67, 43, 25, "_ContinousBaseGesture"], [56, 88, 43, 25], [57, 4, 49, 2], [57, 13, 49, 2, "HoverGesture"], [57, 26, 49, 2], [57, 28, 49, 16], [58, 6, 49, 16], [58, 10, 49, 16, "_this"], [58, 15, 49, 16], [59, 6, 49, 16], [59, 10, 49, 16, "_classCallCheck2"], [59, 26, 49, 16], [59, 27, 49, 16, "default"], [59, 34, 49, 16], [59, 42, 49, 16, "HoverGesture"], [59, 54, 49, 16], [60, 6, 50, 4, "_this"], [60, 11, 50, 4], [60, 14, 50, 4, "_callSuper"], [60, 24, 50, 4], [60, 31, 50, 4, "HoverGesture"], [60, 43, 50, 4], [61, 6, 50, 12, "_this"], [61, 11, 50, 12], [61, 12, 47, 9, "config"], [61, 18, 47, 15], [61, 21, 47, 58], [61, 22, 47, 59], [61, 23, 47, 60], [62, 6, 52, 4, "_this"], [62, 11, 52, 4], [62, 12, 52, 9, "handler<PERSON>ame"], [62, 23, 52, 20], [62, 26, 52, 23], [62, 47, 52, 44], [63, 6, 52, 45], [63, 13, 52, 45, "_this"], [63, 18, 52, 45], [64, 4, 53, 2], [66, 4, 55, 2], [67, 0, 56, 0], [68, 0, 57, 0], [69, 0, 58, 0], [70, 4, 55, 2], [70, 8, 55, 2, "_inherits2"], [70, 18, 55, 2], [70, 19, 55, 2, "default"], [70, 26, 55, 2], [70, 28, 55, 2, "HoverGesture"], [70, 40, 55, 2], [70, 42, 55, 2, "_ContinousBaseGesture"], [70, 63, 55, 2], [71, 4, 55, 2], [71, 15, 55, 2, "_createClass2"], [71, 28, 55, 2], [71, 29, 55, 2, "default"], [71, 36, 55, 2], [71, 38, 55, 2, "HoverGesture"], [71, 50, 55, 2], [72, 6, 55, 2, "key"], [72, 9, 55, 2], [73, 6, 55, 2, "value"], [73, 11, 55, 2], [73, 13, 59, 2], [73, 22, 59, 2, "effect"], [73, 28, 59, 8, "effect"], [73, 29, 59, 9, "effect"], [73, 36, 59, 28], [73, 38, 59, 30], [74, 8, 60, 4], [74, 12, 60, 8], [74, 13, 60, 9, "config"], [74, 19, 60, 15], [74, 20, 60, 16, "hoverEffect"], [74, 31, 60, 27], [74, 34, 60, 30, "effect"], [74, 41, 60, 36], [75, 8, 61, 4], [75, 15, 61, 11], [75, 19, 61, 15], [76, 6, 62, 2], [77, 4, 62, 3], [78, 6, 62, 3, "key"], [78, 9, 62, 3], [79, 6, 62, 3, "value"], [79, 11, 62, 3], [79, 13, 64, 2], [79, 22, 64, 2, "onChange"], [79, 30, 64, 10, "onChange"], [79, 31, 65, 4, "callback"], [79, 39, 69, 13], [79, 41, 70, 4], [80, 8, 71, 4], [81, 8, 72, 4], [81, 12, 72, 8], [81, 13, 72, 9, "handlers"], [81, 21, 72, 17], [81, 22, 72, 18, "changeEventCalculator"], [81, 43, 72, 39], [81, 46, 72, 42, "changeEventCalculator"], [81, 67, 72, 63], [82, 8, 73, 4], [82, 15, 73, 4, "_superPropGet"], [82, 28, 73, 4], [82, 29, 73, 4, "HoverGesture"], [82, 41, 73, 4], [82, 65, 73, 26, "callback"], [82, 73, 73, 34], [83, 6, 74, 2], [84, 4, 74, 3], [85, 2, 74, 3], [85, 4, 43, 34, "ContinousBaseGesture"], [85, 33, 43, 54], [86, 0, 43, 54], [86, 3]], "functionMap": {"names": ["<global>", "changeEventCalculator", "HoverGesture", "HoverGesture#constructor", "HoverGesture#effect", "HoverGesture#onChange"], "mappings": "AAA;ACqB;CDmB;OEE;ECM;GDI;EEM;GFG;EGE;GHU;CFC"}}, "type": "js/module"}]}