{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2F..%2F..%2Fnode_modules%2F%40react-navigation%2Felements%2Flib%2Fmodule%2Fassets\",\n    \"width\": 96,\n    \"height\": 96,\n    \"scales\": [1, 1, 2, 3, 4],\n    \"hash\": \"3162e8a244d8f6fbd259e79043e23ce4\",\n    \"name\": \"close-icon\",\n    \"type\": \"png\",\n    \"fileHashes\": [\"0747a1317bbe9c6fc340b889ef8ab3ae\", \"d84e297c3b3e49a614248143d53e40ca\", \"1190ab078c57159f4245a328118fcd9a\", \"78c625386b4d0690b421eb0fc78f7b9c\", \"0747a1317bbe9c6fc340b889ef8ab3ae\"]\n  });\n});", "lineCount": 13, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 134, 1, 154], [5, 4, 1, 155], [5, 11, 1, 162], [5, 13, 1, 163], [5, 15, 1, 165], [6, 4, 1, 166], [6, 12, 1, 174], [6, 14, 1, 175], [6, 16, 1, 177], [7, 4, 1, 178], [7, 12, 1, 186], [7, 14, 1, 187], [7, 15, 1, 188], [7, 16, 1, 189], [7, 18, 1, 190], [7, 19, 1, 191], [7, 21, 1, 192], [7, 22, 1, 193], [7, 24, 1, 194], [7, 25, 1, 195], [7, 27, 1, 196], [7, 28, 1, 197], [7, 29, 1, 198], [8, 4, 1, 199], [8, 10, 1, 205], [8, 12, 1, 206], [8, 46, 1, 240], [9, 4, 1, 241], [9, 10, 1, 247], [9, 12, 1, 248], [9, 24, 1, 260], [10, 4, 1, 261], [10, 10, 1, 267], [10, 12, 1, 268], [10, 17, 1, 273], [11, 4, 1, 274], [11, 16, 1, 286], [11, 18, 1, 287], [11, 19, 1, 288], [11, 53, 1, 322], [11, 55, 1, 323], [11, 89, 1, 357], [11, 91, 1, 358], [11, 125, 1, 392], [11, 127, 1, 393], [11, 161, 1, 427], [11, 163, 1, 428], [11, 197, 1, 462], [12, 2, 1, 463], [12, 3, 1, 464], [13, 0, 1, 464], [13, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}