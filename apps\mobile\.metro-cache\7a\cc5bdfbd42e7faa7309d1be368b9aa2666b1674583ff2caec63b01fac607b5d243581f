{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../../../../Libraries/TurboModule/TurboModuleRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 99}}], "key": "e2fBb+YBB0xAvQzPR7+5PqHCOdc=", "exportNames": ["*"]}}, {"name": "nullthrows", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 36}}], "key": "epufkdgpKN0G543QKwfSBBl0bWM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var TurboModuleRegistry = _interopRequireWildcard(require(_dependencyMap[1], \"../../../../../../Libraries/TurboModule/TurboModuleRegistry\"));\n  var _nullthrows = _interopRequireDefault(require(_dependencyMap[2], \"nullthrows\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var RawNativeDOM = TurboModuleRegistry.get('NativeDOMCxx');\n  var NativeDOM = {\n    compareDocumentPosition(nativeNodeReference, otherNativeNodeReference) {\n      return (0, _nullthrows.default)(RawNativeDOM).compareDocumentPosition(nativeNodeReference, otherNativeNodeReference);\n    },\n    getChildNodes(nativeNodeReference) {\n      return (0, _nullthrows.default)(RawNativeDOM).getChildNodes(nativeNodeReference);\n    },\n    getParentNode(nativeNodeReference) {\n      return (0, _nullthrows.default)(RawNativeDOM).getParentNode(nativeNodeReference);\n    },\n    isConnected(nativeNodeReference) {\n      return (0, _nullthrows.default)(RawNativeDOM).isConnected(nativeNodeReference);\n    },\n    getBorderWidth(nativeNodeReference) {\n      return (0, _nullthrows.default)(RawNativeDOM).getBorderWidth(nativeNodeReference);\n    },\n    getBoundingClientRect(nativeNodeReference, includeTransform) {\n      return (0, _nullthrows.default)(RawNativeDOM).getBoundingClientRect(nativeNodeReference, includeTransform);\n    },\n    getInnerSize(nativeNodeReference) {\n      return (0, _nullthrows.default)(RawNativeDOM).getInnerSize(nativeNodeReference);\n    },\n    getScrollPosition(nativeNodeReference) {\n      return (0, _nullthrows.default)(RawNativeDOM).getScrollPosition(nativeNodeReference);\n    },\n    getScrollSize(nativeNodeReference) {\n      return (0, _nullthrows.default)(RawNativeDOM).getScrollSize(nativeNodeReference);\n    },\n    getTagName(nativeNodeReference) {\n      return (0, _nullthrows.default)(RawNativeDOM).getTagName(nativeNodeReference);\n    },\n    getTextContent(nativeNodeReference) {\n      return (0, _nullthrows.default)(RawNativeDOM).getTextContent(nativeNodeReference);\n    },\n    hasPointerCapture(nativeNodeReference, pointerId) {\n      return (0, _nullthrows.default)(RawNativeDOM).hasPointerCapture(nativeNodeReference, pointerId);\n    },\n    releasePointerCapture(nativeNodeReference, pointerId) {\n      return (0, _nullthrows.default)(RawNativeDOM).releasePointerCapture(nativeNodeReference, pointerId);\n    },\n    setPointerCapture(nativeNodeReference, pointerId) {\n      return (0, _nullthrows.default)(RawNativeDOM).setPointerCapture(nativeNodeReference, pointerId);\n    },\n    getOffset(nativeNodeReference) {\n      return (0, _nullthrows.default)(RawNativeDOM).getOffset(nativeNodeReference);\n    },\n    linkRootNode(rootTag, instanceHandle) {\n      return (0, _nullthrows.default)(RawNativeDOM?.linkRootNode)(rootTag, instanceHandle);\n    },\n    measure(nativeNodeReference, callback) {\n      return (0, _nullthrows.default)(RawNativeDOM).measure(nativeNodeReference, callback);\n    },\n    measureInWindow(nativeNodeReference, callback) {\n      return (0, _nullthrows.default)(RawNativeDOM).measureInWindow(nativeNodeReference, callback);\n    },\n    measureLayout(nativeNodeReference, relativeNode, onFail, onSuccess) {\n      return (0, _nullthrows.default)(RawNativeDOM).measureLayout(nativeNodeReference, relativeNode, onFail, onSuccess);\n    }\n  };\n  var _default = exports.default = NativeDOM;\n});", "lineCount": 71, "map": [[7, 2, 16, 0], [7, 6, 16, 0, "TurboModuleRegistry"], [7, 25, 16, 0], [7, 28, 16, 0, "_interopRequireWildcard"], [7, 51, 16, 0], [7, 52, 16, 0, "require"], [7, 59, 16, 0], [7, 60, 16, 0, "_dependencyMap"], [7, 74, 16, 0], [8, 2, 17, 0], [8, 6, 17, 0, "_nullthrows"], [8, 17, 17, 0], [8, 20, 17, 0, "_interopRequireDefault"], [8, 42, 17, 0], [8, 43, 17, 0, "require"], [8, 50, 17, 0], [8, 51, 17, 0, "_dependencyMap"], [8, 65, 17, 0], [9, 2, 17, 36], [9, 11, 17, 36, "_interopRequireWildcard"], [9, 35, 17, 36, "e"], [9, 36, 17, 36], [9, 38, 17, 36, "t"], [9, 39, 17, 36], [9, 68, 17, 36, "WeakMap"], [9, 75, 17, 36], [9, 81, 17, 36, "r"], [9, 82, 17, 36], [9, 89, 17, 36, "WeakMap"], [9, 96, 17, 36], [9, 100, 17, 36, "n"], [9, 101, 17, 36], [9, 108, 17, 36, "WeakMap"], [9, 115, 17, 36], [9, 127, 17, 36, "_interopRequireWildcard"], [9, 150, 17, 36], [9, 162, 17, 36, "_interopRequireWildcard"], [9, 163, 17, 36, "e"], [9, 164, 17, 36], [9, 166, 17, 36, "t"], [9, 167, 17, 36], [9, 176, 17, 36, "t"], [9, 177, 17, 36], [9, 181, 17, 36, "e"], [9, 182, 17, 36], [9, 186, 17, 36, "e"], [9, 187, 17, 36], [9, 188, 17, 36, "__esModule"], [9, 198, 17, 36], [9, 207, 17, 36, "e"], [9, 208, 17, 36], [9, 214, 17, 36, "o"], [9, 215, 17, 36], [9, 217, 17, 36, "i"], [9, 218, 17, 36], [9, 220, 17, 36, "f"], [9, 221, 17, 36], [9, 226, 17, 36, "__proto__"], [9, 235, 17, 36], [9, 243, 17, 36, "default"], [9, 250, 17, 36], [9, 252, 17, 36, "e"], [9, 253, 17, 36], [9, 270, 17, 36, "e"], [9, 271, 17, 36], [9, 294, 17, 36, "e"], [9, 295, 17, 36], [9, 320, 17, 36, "e"], [9, 321, 17, 36], [9, 330, 17, 36, "f"], [9, 331, 17, 36], [9, 337, 17, 36, "o"], [9, 338, 17, 36], [9, 341, 17, 36, "t"], [9, 342, 17, 36], [9, 345, 17, 36, "n"], [9, 346, 17, 36], [9, 349, 17, 36, "r"], [9, 350, 17, 36], [9, 358, 17, 36, "o"], [9, 359, 17, 36], [9, 360, 17, 36, "has"], [9, 363, 17, 36], [9, 364, 17, 36, "e"], [9, 365, 17, 36], [9, 375, 17, 36, "o"], [9, 376, 17, 36], [9, 377, 17, 36, "get"], [9, 380, 17, 36], [9, 381, 17, 36, "e"], [9, 382, 17, 36], [9, 385, 17, 36, "o"], [9, 386, 17, 36], [9, 387, 17, 36, "set"], [9, 390, 17, 36], [9, 391, 17, 36, "e"], [9, 392, 17, 36], [9, 394, 17, 36, "f"], [9, 395, 17, 36], [9, 409, 17, 36, "_t"], [9, 411, 17, 36], [9, 415, 17, 36, "e"], [9, 416, 17, 36], [9, 432, 17, 36, "_t"], [9, 434, 17, 36], [9, 441, 17, 36, "hasOwnProperty"], [9, 455, 17, 36], [9, 456, 17, 36, "call"], [9, 460, 17, 36], [9, 461, 17, 36, "e"], [9, 462, 17, 36], [9, 464, 17, 36, "_t"], [9, 466, 17, 36], [9, 473, 17, 36, "i"], [9, 474, 17, 36], [9, 478, 17, 36, "o"], [9, 479, 17, 36], [9, 482, 17, 36, "Object"], [9, 488, 17, 36], [9, 489, 17, 36, "defineProperty"], [9, 503, 17, 36], [9, 508, 17, 36, "Object"], [9, 514, 17, 36], [9, 515, 17, 36, "getOwnPropertyDescriptor"], [9, 539, 17, 36], [9, 540, 17, 36, "e"], [9, 541, 17, 36], [9, 543, 17, 36, "_t"], [9, 545, 17, 36], [9, 552, 17, 36, "i"], [9, 553, 17, 36], [9, 554, 17, 36, "get"], [9, 557, 17, 36], [9, 561, 17, 36, "i"], [9, 562, 17, 36], [9, 563, 17, 36, "set"], [9, 566, 17, 36], [9, 570, 17, 36, "o"], [9, 571, 17, 36], [9, 572, 17, 36, "f"], [9, 573, 17, 36], [9, 575, 17, 36, "_t"], [9, 577, 17, 36], [9, 579, 17, 36, "i"], [9, 580, 17, 36], [9, 584, 17, 36, "f"], [9, 585, 17, 36], [9, 586, 17, 36, "_t"], [9, 588, 17, 36], [9, 592, 17, 36, "e"], [9, 593, 17, 36], [9, 594, 17, 36, "_t"], [9, 596, 17, 36], [9, 607, 17, 36, "f"], [9, 608, 17, 36], [9, 613, 17, 36, "e"], [9, 614, 17, 36], [9, 616, 17, 36, "t"], [9, 617, 17, 36], [10, 2, 159, 0], [10, 6, 159, 6, "RawNativeDOM"], [10, 18, 159, 18], [10, 21, 159, 22, "TurboModuleRegistry"], [10, 40, 159, 41], [10, 41, 159, 42, "get"], [10, 44, 159, 45], [10, 45, 159, 52], [10, 59, 159, 66], [10, 60, 159, 75], [11, 2, 413, 0], [11, 6, 413, 6, "NativeDOM"], [11, 15, 413, 28], [11, 18, 413, 31], [12, 4, 418, 2, "compareDocumentPosition"], [12, 27, 418, 25, "compareDocumentPosition"], [12, 28, 418, 26, "nativeNodeReference"], [12, 47, 418, 45], [12, 49, 418, 47, "otherNativeNodeReference"], [12, 73, 418, 71], [12, 75, 418, 73], [13, 6, 419, 4], [13, 13, 419, 11], [13, 17, 419, 11, "nullthrows"], [13, 36, 419, 21], [13, 38, 419, 22, "RawNativeDOM"], [13, 50, 419, 34], [13, 51, 419, 35], [13, 52, 419, 36, "compareDocumentPosition"], [13, 75, 419, 59], [13, 76, 420, 6, "nativeNodeReference"], [13, 95, 420, 25], [13, 97, 421, 6, "otherNativeNodeReference"], [13, 121, 422, 4], [13, 122, 422, 5], [14, 4, 423, 2], [14, 5, 423, 3], [15, 4, 425, 2, "getChildNodes"], [15, 17, 425, 15, "getChildNodes"], [15, 18, 425, 16, "nativeNodeReference"], [15, 37, 425, 35], [15, 39, 425, 37], [16, 6, 427, 4], [16, 13, 427, 12], [16, 17, 427, 12, "nullthrows"], [16, 36, 427, 22], [16, 38, 427, 23, "RawNativeDOM"], [16, 50, 427, 35], [16, 51, 427, 36], [16, 52, 427, 37, "getChildNodes"], [16, 65, 427, 50], [16, 66, 428, 6, "nativeNodeReference"], [16, 85, 429, 4], [16, 86, 429, 5], [17, 4, 430, 2], [17, 5, 430, 3], [18, 4, 432, 2, "getParentNode"], [18, 17, 432, 15, "getParentNode"], [18, 18, 432, 16, "nativeNodeReference"], [18, 37, 432, 35], [18, 39, 432, 37], [19, 6, 434, 4], [19, 13, 434, 12], [19, 17, 434, 12, "nullthrows"], [19, 36, 434, 22], [19, 38, 434, 23, "RawNativeDOM"], [19, 50, 434, 35], [19, 51, 434, 36], [19, 52, 434, 37, "getParentNode"], [19, 65, 434, 50], [19, 66, 435, 6, "nativeNodeReference"], [19, 85, 436, 4], [19, 86, 436, 5], [20, 4, 437, 2], [20, 5, 437, 3], [21, 4, 439, 2, "isConnected"], [21, 15, 439, 13, "isConnected"], [21, 16, 439, 14, "nativeNodeReference"], [21, 35, 439, 33], [21, 37, 439, 35], [22, 6, 440, 4], [22, 13, 440, 11], [22, 17, 440, 11, "nullthrows"], [22, 36, 440, 21], [22, 38, 440, 22, "RawNativeDOM"], [22, 50, 440, 34], [22, 51, 440, 35], [22, 52, 440, 36, "isConnected"], [22, 63, 440, 47], [22, 64, 440, 48, "nativeNodeReference"], [22, 83, 440, 67], [22, 84, 440, 68], [23, 4, 441, 2], [23, 5, 441, 3], [24, 4, 447, 2, "getBorderWidth"], [24, 18, 447, 16, "getBorderWidth"], [24, 19, 447, 17, "nativeNodeReference"], [24, 38, 447, 36], [24, 40, 447, 38], [25, 6, 449, 4], [25, 13, 449, 12], [25, 17, 449, 12, "nullthrows"], [25, 36, 449, 22], [25, 38, 449, 23, "RawNativeDOM"], [25, 50, 449, 35], [25, 51, 449, 36], [25, 52, 449, 37, "getBorderWidth"], [25, 66, 449, 51], [25, 67, 450, 6, "nativeNodeReference"], [25, 86, 451, 4], [25, 87, 451, 5], [26, 4, 459, 2], [26, 5, 459, 3], [27, 4, 461, 2, "getBoundingClientRect"], [27, 25, 461, 23, "getBoundingClientRect"], [27, 26, 461, 24, "nativeNodeReference"], [27, 45, 461, 43], [27, 47, 461, 45, "includeTransform"], [27, 63, 461, 70], [27, 65, 461, 72], [28, 6, 463, 4], [28, 13, 463, 12], [28, 17, 463, 12, "nullthrows"], [28, 36, 463, 22], [28, 38, 463, 23, "RawNativeDOM"], [28, 50, 463, 35], [28, 51, 463, 36], [28, 52, 463, 37, "getBoundingClientRect"], [28, 73, 463, 58], [28, 74, 464, 6, "nativeNodeReference"], [28, 93, 464, 25], [28, 95, 465, 6, "includeTransform"], [28, 111, 466, 4], [28, 112, 466, 5], [29, 4, 474, 2], [29, 5, 474, 3], [30, 4, 476, 2, "getInnerSize"], [30, 16, 476, 14, "getInnerSize"], [30, 17, 476, 15, "nativeNodeReference"], [30, 36, 476, 34], [30, 38, 476, 36], [31, 6, 478, 4], [31, 13, 478, 12], [31, 17, 478, 12, "nullthrows"], [31, 36, 478, 22], [31, 38, 478, 23, "RawNativeDOM"], [31, 50, 478, 35], [31, 51, 478, 36], [31, 52, 478, 37, "getInnerSize"], [31, 64, 478, 49], [31, 65, 479, 6, "nativeNodeReference"], [31, 84, 480, 4], [31, 85, 480, 5], [32, 4, 481, 2], [32, 5, 481, 3], [33, 4, 483, 2, "getScrollPosition"], [33, 21, 483, 19, "getScrollPosition"], [33, 22, 483, 20, "nativeNodeReference"], [33, 41, 483, 39], [33, 43, 483, 41], [34, 6, 485, 4], [34, 13, 485, 12], [34, 17, 485, 12, "nullthrows"], [34, 36, 485, 22], [34, 38, 485, 23, "RawNativeDOM"], [34, 50, 485, 35], [34, 51, 485, 36], [34, 52, 485, 37, "getScrollPosition"], [34, 69, 485, 54], [34, 70, 486, 6, "nativeNodeReference"], [34, 89, 487, 4], [34, 90, 487, 5], [35, 4, 488, 2], [35, 5, 488, 3], [36, 4, 490, 2, "getScrollSize"], [36, 17, 490, 15, "getScrollSize"], [36, 18, 490, 16, "nativeNodeReference"], [36, 37, 490, 35], [36, 39, 490, 37], [37, 6, 492, 4], [37, 13, 492, 12], [37, 17, 492, 12, "nullthrows"], [37, 36, 492, 22], [37, 38, 492, 23, "RawNativeDOM"], [37, 50, 492, 35], [37, 51, 492, 36], [37, 52, 492, 37, "getScrollSize"], [37, 65, 492, 50], [37, 66, 493, 6, "nativeNodeReference"], [37, 85, 494, 4], [37, 86, 494, 5], [38, 4, 495, 2], [38, 5, 495, 3], [39, 4, 497, 2, "getTagName"], [39, 14, 497, 12, "getTagName"], [39, 15, 497, 13, "nativeNodeReference"], [39, 34, 497, 32], [39, 36, 497, 34], [40, 6, 498, 4], [40, 13, 498, 11], [40, 17, 498, 11, "nullthrows"], [40, 36, 498, 21], [40, 38, 498, 22, "RawNativeDOM"], [40, 50, 498, 34], [40, 51, 498, 35], [40, 52, 498, 36, "getTagName"], [40, 62, 498, 46], [40, 63, 498, 47, "nativeNodeReference"], [40, 82, 498, 66], [40, 83, 498, 67], [41, 4, 499, 2], [41, 5, 499, 3], [42, 4, 501, 2, "getTextContent"], [42, 18, 501, 16, "getTextContent"], [42, 19, 501, 17, "nativeNodeReference"], [42, 38, 501, 36], [42, 40, 501, 38], [43, 6, 502, 4], [43, 13, 502, 11], [43, 17, 502, 11, "nullthrows"], [43, 36, 502, 21], [43, 38, 502, 22, "RawNativeDOM"], [43, 50, 502, 34], [43, 51, 502, 35], [43, 52, 502, 36, "getTextContent"], [43, 66, 502, 50], [43, 67, 502, 51, "nativeNodeReference"], [43, 86, 502, 70], [43, 87, 502, 71], [44, 4, 503, 2], [44, 5, 503, 3], [45, 4, 505, 2, "hasPointerCapture"], [45, 21, 505, 19, "hasPointerCapture"], [45, 22, 505, 20, "nativeNodeReference"], [45, 41, 505, 39], [45, 43, 505, 41, "pointerId"], [45, 52, 505, 50], [45, 54, 505, 52], [46, 6, 506, 4], [46, 13, 506, 11], [46, 17, 506, 11, "nullthrows"], [46, 36, 506, 21], [46, 38, 506, 22, "RawNativeDOM"], [46, 50, 506, 34], [46, 51, 506, 35], [46, 52, 506, 36, "hasPointerCapture"], [46, 69, 506, 53], [46, 70, 507, 6, "nativeNodeReference"], [46, 89, 507, 25], [46, 91, 508, 6, "pointerId"], [46, 100, 509, 4], [46, 101, 509, 5], [47, 4, 510, 2], [47, 5, 510, 3], [48, 4, 512, 2, "releasePointerCapture"], [48, 25, 512, 23, "releasePointerCapture"], [48, 26, 512, 24, "nativeNodeReference"], [48, 45, 512, 43], [48, 47, 512, 45, "pointerId"], [48, 56, 512, 54], [48, 58, 512, 56], [49, 6, 513, 4], [49, 13, 513, 11], [49, 17, 513, 11, "nullthrows"], [49, 36, 513, 21], [49, 38, 513, 22, "RawNativeDOM"], [49, 50, 513, 34], [49, 51, 513, 35], [49, 52, 513, 36, "releasePointerCapture"], [49, 73, 513, 57], [49, 74, 514, 6, "nativeNodeReference"], [49, 93, 514, 25], [49, 95, 515, 6, "pointerId"], [49, 104, 516, 4], [49, 105, 516, 5], [50, 4, 517, 2], [50, 5, 517, 3], [51, 4, 519, 2, "setPointerCapture"], [51, 21, 519, 19, "setPointerCapture"], [51, 22, 519, 20, "nativeNodeReference"], [51, 41, 519, 39], [51, 43, 519, 41, "pointerId"], [51, 52, 519, 50], [51, 54, 519, 52], [52, 6, 520, 4], [52, 13, 520, 11], [52, 17, 520, 11, "nullthrows"], [52, 36, 520, 21], [52, 38, 520, 22, "RawNativeDOM"], [52, 50, 520, 34], [52, 51, 520, 35], [52, 52, 520, 36, "setPointerCapture"], [52, 69, 520, 53], [52, 70, 521, 6, "nativeNodeReference"], [52, 89, 521, 25], [52, 91, 522, 6, "pointerId"], [52, 100, 523, 4], [52, 101, 523, 5], [53, 4, 524, 2], [53, 5, 524, 3], [54, 4, 530, 2, "getOffset"], [54, 13, 530, 11, "getOffset"], [54, 14, 530, 12, "nativeNodeReference"], [54, 33, 530, 31], [54, 35, 530, 33], [55, 6, 532, 4], [55, 13, 532, 12], [55, 17, 532, 12, "nullthrows"], [55, 36, 532, 22], [55, 38, 532, 23, "RawNativeDOM"], [55, 50, 532, 35], [55, 51, 532, 36], [55, 52, 532, 37, "getOffset"], [55, 61, 532, 46], [55, 62, 532, 47, "nativeNodeReference"], [55, 81, 532, 66], [55, 82, 532, 67], [56, 4, 539, 2], [56, 5, 539, 3], [57, 4, 545, 2, "linkRootNode"], [57, 16, 545, 14, "linkRootNode"], [57, 17, 545, 15, "rootTag"], [57, 24, 545, 22], [57, 26, 545, 24, "instanceHandle"], [57, 40, 545, 38], [57, 42, 545, 40], [58, 6, 547, 4], [58, 13, 547, 12], [58, 17, 547, 12, "nullthrows"], [58, 36, 547, 22], [58, 38, 547, 23, "RawNativeDOM"], [58, 50, 547, 35], [58, 52, 547, 37, "linkRootNode"], [58, 64, 547, 49], [58, 65, 547, 50], [58, 66, 549, 6, "rootTag"], [58, 73, 549, 13], [58, 75, 550, 6, "instanceHandle"], [58, 89, 551, 4], [58, 90, 551, 5], [59, 4, 552, 2], [59, 5, 552, 3], [60, 4, 558, 2, "measure"], [60, 11, 558, 9, "measure"], [60, 12, 558, 10, "nativeNodeReference"], [60, 31, 558, 29], [60, 33, 558, 31, "callback"], [60, 41, 558, 39], [60, 43, 558, 41], [61, 6, 559, 4], [61, 13, 559, 11], [61, 17, 559, 11, "nullthrows"], [61, 36, 559, 21], [61, 38, 559, 22, "RawNativeDOM"], [61, 50, 559, 34], [61, 51, 559, 35], [61, 52, 559, 36, "measure"], [61, 59, 559, 43], [61, 60, 559, 44, "nativeNodeReference"], [61, 79, 559, 63], [61, 81, 559, 65, "callback"], [61, 89, 559, 73], [61, 90, 559, 74], [62, 4, 560, 2], [62, 5, 560, 3], [63, 4, 562, 2, "measureInWindow"], [63, 19, 562, 17, "measureInWindow"], [63, 20, 562, 18, "nativeNodeReference"], [63, 39, 562, 37], [63, 41, 562, 39, "callback"], [63, 49, 562, 47], [63, 51, 562, 49], [64, 6, 563, 4], [64, 13, 563, 11], [64, 17, 563, 11, "nullthrows"], [64, 36, 563, 21], [64, 38, 563, 22, "RawNativeDOM"], [64, 50, 563, 34], [64, 51, 563, 35], [64, 52, 563, 36, "measureInWindow"], [64, 67, 563, 51], [64, 68, 564, 6, "nativeNodeReference"], [64, 87, 564, 25], [64, 89, 565, 6, "callback"], [64, 97, 566, 4], [64, 98, 566, 5], [65, 4, 567, 2], [65, 5, 567, 3], [66, 4, 569, 2, "measureLayout"], [66, 17, 569, 15, "measureLayout"], [66, 18, 569, 16, "nativeNodeReference"], [66, 37, 569, 35], [66, 39, 569, 37, "relativeNode"], [66, 51, 569, 49], [66, 53, 569, 51, "onFail"], [66, 59, 569, 57], [66, 61, 569, 59, "onSuccess"], [66, 70, 569, 68], [66, 72, 569, 70], [67, 6, 570, 4], [67, 13, 570, 11], [67, 17, 570, 11, "nullthrows"], [67, 36, 570, 21], [67, 38, 570, 22, "RawNativeDOM"], [67, 50, 570, 34], [67, 51, 570, 35], [67, 52, 570, 36, "measureLayout"], [67, 65, 570, 49], [67, 66, 571, 6, "nativeNodeReference"], [67, 85, 571, 25], [67, 87, 572, 6, "relativeNode"], [67, 99, 572, 18], [67, 101, 573, 6, "onFail"], [67, 107, 573, 12], [67, 109, 574, 6, "onSuccess"], [67, 118, 575, 4], [67, 119, 575, 5], [68, 4, 576, 2], [69, 2, 577, 0], [69, 3, 577, 1], [70, 2, 577, 2], [70, 6, 577, 2, "_default"], [70, 14, 577, 2], [70, 17, 577, 2, "exports"], [70, 24, 577, 2], [70, 25, 577, 2, "default"], [70, 32, 577, 2], [70, 35, 579, 15, "NativeDOM"], [70, 44, 579, 24], [71, 0, 579, 24], [71, 3]], "functionMap": {"names": ["<global>", "compareDocumentPosition", "getChildNodes", "getParentNode", "isConnected", "getBorderWidth", "getBoundingClientRect", "getInnerSize", "getScrollPosition", "getScrollSize", "getTagName", "getTextContent", "hasPointerCapture", "releasePointerCapture", "setPointerCapture", "getOffset", "linkRootNode", "measure", "measureInWindow", "measureLayout"], "mappings": "AAA;ECia;GDK;EEE;GFK;EGE;GHK;EIE;GJE;EKM;GLY;EME;GNa;EOE;GPK;EQE;GRK;ESE;GTK;EUE;GVE;EWE;GXE;EYE;GZK;EaE;GbK;EcE;GdK;EeM;GfS;EgBM;GhBO;EiBM;GjBE;EkBE;GlBK;EmBE;GnBO"}}, "type": "js/module"}]}