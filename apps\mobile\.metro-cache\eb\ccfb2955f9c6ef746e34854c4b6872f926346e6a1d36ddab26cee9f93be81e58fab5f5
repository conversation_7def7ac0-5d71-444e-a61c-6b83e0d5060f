{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 42}, "end": {"line": 3, "column": 55, "index": 97}}], "key": "4wo4OYT4MSo2InL8kiWmZxvepwE=", "exportNames": ["*"]}}, {"name": "../gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 98}, "end": {"line": 4, "column": 66, "index": 164}}], "key": "tAi+3DTq2/XlM2rT1I9X7ektuV0=", "exportNames": ["*"]}}, {"name": "../../FlingGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 166}, "end": {"line": 6, "column": 69, "index": 235}}], "key": "m7drCniziiNjEu9Grhe6bV/AFd0=", "exportNames": ["*"]}}, {"name": "../../ForceTouchGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 236}, "end": {"line": 7, "column": 79, "index": 315}}], "key": "Zs82FqJFIcjv50A58P90bQbMZ6s=", "exportNames": ["*"]}}, {"name": "../../LongPressGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 316}, "end": {"line": 8, "column": 77, "index": 393}}], "key": "xLOe4E3z7HzpJ/yq6aTNrEbuZio=", "exportNames": ["*"]}}, {"name": "../../PanGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 394}, "end": {"line": 12, "column": 33, "index": 500}}], "key": "bk07O1d6H26279U6oqqnjaS7c4c=", "exportNames": ["*"]}}, {"name": "../../TapGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 501}, "end": {"line": 13, "column": 65, "index": 566}}], "key": "ZqoHIcC0SF6bf3AIHltJPu1k9xI=", "exportNames": ["*"]}}, {"name": "../hoverGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 567}, "end": {"line": 14, "column": 59, "index": 626}}], "key": "2DA6Vfuec14INlrI/GwdOE0FAK4=", "exportNames": ["*"]}}, {"name": "../../NativeViewGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 627}, "end": {"line": 15, "column": 79, "index": 706}}], "key": "FiQaORn3aiHBJrGlJ2FeE26dr38=", "exportNames": ["*"]}}, {"name": "../../gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 707}, "end": {"line": 19, "column": 36, "index": 818}}], "key": "iYkm+MgDTErKT5jbMC97JfuzH7k=", "exportNames": ["*"]}}, {"name": "../../../EnableNewWebImplementation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 819}, "end": {"line": 20, "column": 84, "index": 903}}], "key": "eaIoOU9/v4mL1aA6FJZKxHzRNGE=", "exportNames": ["*"]}}, {"name": "../../../<PERSON><PERSON><PERSON><PERSON>", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 904}, "end": {"line": 21, "column": 49, "index": 953}}], "key": "dd5GIUVDNjn2BlVM36KIcGPscVA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 954}, "end": {"line": 22, "column": 54, "index": 1008}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../reanimated<PERSON><PERSON>per", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 1009}, "end": {"line": 23, "column": 50, "index": 1059}}], "key": "55E8XkeO+dzLnJh3bfoVrgRwD58=", "exportNames": ["*"]}}, {"name": "../eventReceiver", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 1060}, "end": {"line": 24, "column": 57, "index": 1117}}], "key": "5nSvdaEKN+h/ZTz2T/BGCtOX48A=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ALLOWED_PROPS = void 0;\n  exports.checkGestureCallbacksForWorklets = checkGestureCallbacksForWorklets;\n  exports.extractGestureRelations = extractGestureRelations;\n  exports.useForceRender = useForceRender;\n  exports.useWebEventHandlers = useWebEventHandlers;\n  exports.validateDetectorChildren = validateDetectorChildren;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _utils = require(_dependencyMap[3], \"../../../utils\");\n  var _gesture = require(_dependencyMap[4], \"../gesture\");\n  var _FlingGestureHandler = require(_dependencyMap[5], \"../../FlingGestureHandler\");\n  var _ForceTouchGestureHandler = require(_dependencyMap[6], \"../../ForceTouchGestureHandler\");\n  var _LongPressGestureHandler = require(_dependencyMap[7], \"../../LongPressGestureHandler\");\n  var _PanGestureHandler = require(_dependencyMap[8], \"../../PanGestureHandler\");\n  var _TapGestureHandler = require(_dependencyMap[9], \"../../TapGestureHandler\");\n  var _hoverGesture = require(_dependencyMap[10], \"../hoverGesture\");\n  var _NativeViewGestureHandler = require(_dependencyMap[11], \"../../NativeViewGestureHandler\");\n  var _gestureHandlerCommon = require(_dependencyMap[12], \"../../gestureHandlerCommon\");\n  var _EnableNewWebImplementation = require(_dependencyMap[13], \"../../../EnableNewWebImplementation\");\n  var _RNRenderer = require(_dependencyMap[14], \"../../../RNRenderer\");\n  var _react = require(_dependencyMap[15], \"react\");\n  var _reanimatedWrapper = require(_dependencyMap[16], \"../reanimatedWrapper\");\n  var _eventReceiver = require(_dependencyMap[17], \"../eventReceiver\");\n  var ALLOWED_PROPS = exports.ALLOWED_PROPS = [..._gestureHandlerCommon.baseGestureHandlerWithDetectorProps, ..._TapGestureHandler.tapGestureHandlerProps, ..._PanGestureHandler.panGestureHandlerProps, ..._PanGestureHandler.panGestureHandlerCustomNativeProps, ..._LongPressGestureHandler.longPressGestureHandlerProps, ..._ForceTouchGestureHandler.forceTouchGestureHandlerProps, ..._FlingGestureHandler.flingGestureHandlerProps, ..._hoverGesture.hoverGestureHandlerProps, ..._NativeViewGestureHandler.nativeViewGestureHandlerProps];\n  function convertToHandlerTag(ref) {\n    if (typeof ref === 'number') {\n      return ref;\n    } else if (ref instanceof _gesture.BaseGesture) {\n      return ref.handlerTag;\n    } else {\n      // @ts-ignore in this case it should be a ref either to gesture object or\n      // a gesture handler component, in both cases handlerTag property exists\n      return ref.current?.handlerTag ?? -1;\n    }\n  }\n  function extractValidHandlerTags(interactionGroup) {\n    return interactionGroup?.map(convertToHandlerTag)?.filter(tag => tag > 0) ?? [];\n  }\n  function extractGestureRelations(gesture) {\n    var requireToFail = extractValidHandlerTags(gesture.config.requireToFail);\n    var simultaneousWith = extractValidHandlerTags(gesture.config.simultaneousWith);\n    var blocksHandlers = extractValidHandlerTags(gesture.config.blocksHandlers);\n    return {\n      waitFor: requireToFail,\n      simultaneousHandlers: simultaneousWith,\n      blocksHandlers: blocksHandlers\n    };\n  }\n  function checkGestureCallbacksForWorklets(gesture) {\n    if (!__DEV__) {\n      return;\n    }\n    // If a gesture is explicitly marked to run on the JS thread there is no need to check\n    // if callbacks are worklets as the user is aware they will be ran on the JS thread\n    if (gesture.config.runOnJS) {\n      return;\n    }\n    var areSomeNotWorklets = gesture.handlers.isWorklet.includes(false);\n    var areSomeWorklets = gesture.handlers.isWorklet.includes(true);\n\n    // If some of the callbacks are worklets and some are not, and the gesture is not\n    // explicitly marked with `.runOnJS(true)` show an error\n    if (areSomeNotWorklets && areSomeWorklets) {\n      console.error((0, _utils.tagMessage)(`Some of the callbacks in the gesture are worklets and some are not. Either make sure that all calbacks are marked as 'worklet' if you wish to run them on the UI thread or use '.runOnJS(true)' modifier on the gesture explicitly to run all callbacks on the JS thread.`));\n    }\n    if (_reanimatedWrapper.Reanimated === undefined) {\n      // If Reanimated is not available, we can't run worklets, so we shouldn't show the warning\n      return;\n    }\n    var areAllNotWorklets = !areSomeWorklets && areSomeNotWorklets;\n    // If none of the callbacks are worklets and the gesture is not explicitly marked with\n    // `.runOnJS(true)` show a warning\n    if (areAllNotWorklets && !(0, _utils.isTestEnv)()) {\n      console.warn((0, _utils.tagMessage)(`None of the callbacks in the gesture are worklets. If you wish to run them on the JS thread use '.runOnJS(true)' modifier on the gesture to make this explicit. Otherwise, mark the callbacks as 'worklet' to run them on the UI thread.`));\n    }\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  function validateDetectorChildren(ref) {\n    // Finds the first native view under the Wrap component and traverses the fiber tree upwards\n    // to check whether there is more than one native view as a pseudo-direct child of GestureDetector\n    // i.e. this is not ok:\n    //            Wrap\n    //             |\n    //            / \\\n    //           /   \\\n    //          /     \\\n    //         /       \\\n    //   NativeView  NativeView\n    //\n    // but this is fine:\n    //            Wrap\n    //             |\n    //         NativeView\n    //             |\n    //            / \\\n    //           /   \\\n    //          /     \\\n    //         /       \\\n    //   NativeView  NativeView\n    if (__DEV__ && _reactNative.Platform.OS !== 'web') {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      var wrapType =\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      ref._reactInternals.elementType;\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      var instance = _RNRenderer.RNRenderer.findHostInstance_DEPRECATED(ref)._internalFiberInstanceHandleDEV;\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      while (instance && instance.elementType !== wrapType) {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        if (instance.sibling) {\n          throw new Error('GestureDetector has more than one native view as its children. This can happen if you are using a custom component that renders multiple views, like React.Fragment. You should wrap content of GestureDetector with a <View> or <Animated.View>.');\n        }\n\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n        instance = instance.return;\n      }\n    }\n  }\n  function useForceRender() {\n    var _useState = (0, _react.useState)(false),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      renderState = _useState2[0],\n      setRenderState = _useState2[1];\n    var forceRender = (0, _react.useCallback)(() => {\n      setRenderState(!renderState);\n    }, [renderState, setRenderState]);\n    return forceRender;\n  }\n  function useWebEventHandlers() {\n    return (0, _react.useRef)({\n      onGestureHandlerEvent: e => {\n        (0, _eventReceiver.onGestureHandlerEvent)(e.nativeEvent);\n      },\n      onGestureHandlerStateChange: (0, _EnableNewWebImplementation.isNewWebImplementationEnabled)() ? e => {\n        (0, _eventReceiver.onGestureHandlerEvent)(e.nativeEvent);\n      } : undefined\n    });\n  }\n});", "lineCount": 147, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_reactNative"], [13, 18, 1, 0], [13, 21, 1, 0, "require"], [13, 28, 1, 0], [13, 29, 1, 0, "_dependencyMap"], [13, 43, 1, 0], [14, 2, 3, 0], [14, 6, 3, 0, "_utils"], [14, 12, 3, 0], [14, 15, 3, 0, "require"], [14, 22, 3, 0], [14, 23, 3, 0, "_dependencyMap"], [14, 37, 3, 0], [15, 2, 4, 0], [15, 6, 4, 0, "_gesture"], [15, 14, 4, 0], [15, 17, 4, 0, "require"], [15, 24, 4, 0], [15, 25, 4, 0, "_dependencyMap"], [15, 39, 4, 0], [16, 2, 6, 0], [16, 6, 6, 0, "_FlingGestureHandler"], [16, 26, 6, 0], [16, 29, 6, 0, "require"], [16, 36, 6, 0], [16, 37, 6, 0, "_dependencyMap"], [16, 51, 6, 0], [17, 2, 7, 0], [17, 6, 7, 0, "_ForceTouchGestureHandler"], [17, 31, 7, 0], [17, 34, 7, 0, "require"], [17, 41, 7, 0], [17, 42, 7, 0, "_dependencyMap"], [17, 56, 7, 0], [18, 2, 8, 0], [18, 6, 8, 0, "_LongPressGestureHandler"], [18, 30, 8, 0], [18, 33, 8, 0, "require"], [18, 40, 8, 0], [18, 41, 8, 0, "_dependencyMap"], [18, 55, 8, 0], [19, 2, 9, 0], [19, 6, 9, 0, "_PanGestureHandler"], [19, 24, 9, 0], [19, 27, 9, 0, "require"], [19, 34, 9, 0], [19, 35, 9, 0, "_dependencyMap"], [19, 49, 9, 0], [20, 2, 13, 0], [20, 6, 13, 0, "_TapGestureHandler"], [20, 24, 13, 0], [20, 27, 13, 0, "require"], [20, 34, 13, 0], [20, 35, 13, 0, "_dependencyMap"], [20, 49, 13, 0], [21, 2, 14, 0], [21, 6, 14, 0, "_hoverGesture"], [21, 19, 14, 0], [21, 22, 14, 0, "require"], [21, 29, 14, 0], [21, 30, 14, 0, "_dependencyMap"], [21, 44, 14, 0], [22, 2, 15, 0], [22, 6, 15, 0, "_NativeViewGestureHandler"], [22, 31, 15, 0], [22, 34, 15, 0, "require"], [22, 41, 15, 0], [22, 42, 15, 0, "_dependencyMap"], [22, 56, 15, 0], [23, 2, 16, 0], [23, 6, 16, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 27, 16, 0], [23, 30, 16, 0, "require"], [23, 37, 16, 0], [23, 38, 16, 0, "_dependencyMap"], [23, 52, 16, 0], [24, 2, 20, 0], [24, 6, 20, 0, "_EnableNewWebImplementation"], [24, 33, 20, 0], [24, 36, 20, 0, "require"], [24, 43, 20, 0], [24, 44, 20, 0, "_dependencyMap"], [24, 58, 20, 0], [25, 2, 21, 0], [25, 6, 21, 0, "_<PERSON><PERSON><PERSON><PERSON>"], [25, 17, 21, 0], [25, 20, 21, 0, "require"], [25, 27, 21, 0], [25, 28, 21, 0, "_dependencyMap"], [25, 42, 21, 0], [26, 2, 22, 0], [26, 6, 22, 0, "_react"], [26, 12, 22, 0], [26, 15, 22, 0, "require"], [26, 22, 22, 0], [26, 23, 22, 0, "_dependencyMap"], [26, 37, 22, 0], [27, 2, 23, 0], [27, 6, 23, 0, "_reanimated<PERSON><PERSON>per"], [27, 24, 23, 0], [27, 27, 23, 0, "require"], [27, 34, 23, 0], [27, 35, 23, 0, "_dependencyMap"], [27, 49, 23, 0], [28, 2, 24, 0], [28, 6, 24, 0, "_eventReceiver"], [28, 20, 24, 0], [28, 23, 24, 0, "require"], [28, 30, 24, 0], [28, 31, 24, 0, "_dependencyMap"], [28, 45, 24, 0], [29, 2, 27, 7], [29, 6, 27, 13, "ALLOWED_PROPS"], [29, 19, 27, 26], [29, 22, 27, 26, "exports"], [29, 29, 27, 26], [29, 30, 27, 26, "ALLOWED_PROPS"], [29, 43, 27, 26], [29, 46, 27, 29], [29, 47, 28, 2], [29, 50, 28, 5, "baseGestureHandlerWithDetectorProps"], [29, 107, 28, 40], [29, 109, 29, 2], [29, 112, 29, 5, "tapGestureHandlerProps"], [29, 153, 29, 27], [29, 155, 30, 2], [29, 158, 30, 5, "panGestureHandlerProps"], [29, 199, 30, 27], [29, 201, 31, 2], [29, 204, 31, 5, "panGestureHandlerCustomNativeProps"], [29, 257, 31, 39], [29, 259, 32, 2], [29, 262, 32, 5, "longPressGestureHandlerProps"], [29, 315, 32, 33], [29, 317, 33, 2], [29, 320, 33, 5, "forceTouchGestureHandlerProps"], [29, 375, 33, 34], [29, 377, 34, 2], [29, 380, 34, 5, "flingGestureHandlerProps"], [29, 425, 34, 29], [29, 427, 35, 2], [29, 430, 35, 5, "hoverGestureHandlerProps"], [29, 468, 35, 29], [29, 470, 36, 2], [29, 473, 36, 5, "nativeViewGestureHandlerProps"], [29, 528, 36, 34], [29, 529, 37, 1], [30, 2, 39, 0], [30, 11, 39, 9, "convertToHandlerTag"], [30, 30, 39, 28, "convertToHandlerTag"], [30, 31, 39, 29, "ref"], [30, 34, 39, 44], [30, 36, 39, 54], [31, 4, 40, 2], [31, 8, 40, 6], [31, 15, 40, 13, "ref"], [31, 18, 40, 16], [31, 23, 40, 21], [31, 31, 40, 29], [31, 33, 40, 31], [32, 6, 41, 4], [32, 13, 41, 11, "ref"], [32, 16, 41, 14], [33, 4, 42, 2], [33, 5, 42, 3], [33, 11, 42, 9], [33, 15, 42, 13, "ref"], [33, 18, 42, 16], [33, 30, 42, 28, "BaseGesture"], [33, 50, 42, 39], [33, 52, 42, 41], [34, 6, 43, 4], [34, 13, 43, 11, "ref"], [34, 16, 43, 14], [34, 17, 43, 15, "handlerTag"], [34, 27, 43, 25], [35, 4, 44, 2], [35, 5, 44, 3], [35, 11, 44, 9], [36, 6, 45, 4], [37, 6, 46, 4], [38, 6, 47, 4], [38, 13, 47, 11, "ref"], [38, 16, 47, 14], [38, 17, 47, 15, "current"], [38, 24, 47, 22], [38, 26, 47, 24, "handlerTag"], [38, 36, 47, 34], [38, 40, 47, 38], [38, 41, 47, 39], [38, 42, 47, 40], [39, 4, 48, 2], [40, 2, 49, 0], [41, 2, 51, 0], [41, 11, 51, 9, "extractValidHandlerTags"], [41, 34, 51, 32, "extractValidHandlerTags"], [41, 35, 51, 33, "interactionGroup"], [41, 51, 51, 75], [41, 53, 51, 77], [42, 4, 52, 2], [42, 11, 53, 4, "interactionGroup"], [42, 27, 53, 20], [42, 29, 53, 22, "map"], [42, 32, 53, 25], [42, 33, 53, 26, "convertToHandlerTag"], [42, 52, 53, 45], [42, 53, 53, 46], [42, 55, 53, 48, "filter"], [42, 61, 53, 54], [42, 62, 53, 56, "tag"], [42, 65, 53, 59], [42, 69, 53, 64, "tag"], [42, 72, 53, 67], [42, 75, 53, 70], [42, 76, 53, 71], [42, 77, 53, 72], [42, 81, 53, 76], [42, 83, 53, 78], [43, 2, 55, 0], [44, 2, 57, 7], [44, 11, 57, 16, "extractGestureRelations"], [44, 34, 57, 39, "extractGestureRelations"], [44, 35, 57, 40, "gesture"], [44, 42, 57, 60], [44, 44, 57, 62], [45, 4, 58, 2], [45, 8, 58, 8, "requireToFail"], [45, 21, 58, 21], [45, 24, 58, 24, "extractValidHandlerTags"], [45, 47, 58, 47], [45, 48, 58, 48, "gesture"], [45, 55, 58, 55], [45, 56, 58, 56, "config"], [45, 62, 58, 62], [45, 63, 58, 63, "requireToFail"], [45, 76, 58, 76], [45, 77, 58, 77], [46, 4, 59, 2], [46, 8, 59, 8, "simultaneousWith"], [46, 24, 59, 24], [46, 27, 59, 27, "extractValidHandlerTags"], [46, 50, 59, 50], [46, 51, 60, 4, "gesture"], [46, 58, 60, 11], [46, 59, 60, 12, "config"], [46, 65, 60, 18], [46, 66, 60, 19, "simultaneousWith"], [46, 82, 61, 2], [46, 83, 61, 3], [47, 4, 62, 2], [47, 8, 62, 8, "blocksHandlers"], [47, 22, 62, 22], [47, 25, 62, 25, "extractValidHandlerTags"], [47, 48, 62, 48], [47, 49, 62, 49, "gesture"], [47, 56, 62, 56], [47, 57, 62, 57, "config"], [47, 63, 62, 63], [47, 64, 62, 64, "blocksHandlers"], [47, 78, 62, 78], [47, 79, 62, 79], [48, 4, 64, 2], [48, 11, 64, 9], [49, 6, 65, 4, "waitFor"], [49, 13, 65, 11], [49, 15, 65, 13, "requireToFail"], [49, 28, 65, 26], [50, 6, 66, 4, "simultaneousHandlers"], [50, 26, 66, 24], [50, 28, 66, 26, "simultaneousWith"], [50, 44, 66, 42], [51, 6, 67, 4, "blocksHandlers"], [51, 20, 67, 18], [51, 22, 67, 20, "blocksHandlers"], [52, 4, 68, 2], [52, 5, 68, 3], [53, 2, 69, 0], [54, 2, 71, 7], [54, 11, 71, 16, "checkGestureCallbacksForWorklets"], [54, 43, 71, 48, "checkGestureCallbacksForWorklets"], [54, 44, 71, 49, "gesture"], [54, 51, 71, 69], [54, 53, 71, 71], [55, 4, 72, 2], [55, 8, 72, 6], [55, 9, 72, 7, "__DEV__"], [55, 16, 72, 14], [55, 18, 72, 16], [56, 6, 73, 4], [57, 4, 74, 2], [58, 4, 75, 2], [59, 4, 76, 2], [60, 4, 77, 2], [60, 8, 77, 6, "gesture"], [60, 15, 77, 13], [60, 16, 77, 14, "config"], [60, 22, 77, 20], [60, 23, 77, 21, "runOnJS"], [60, 30, 77, 28], [60, 32, 77, 30], [61, 6, 78, 4], [62, 4, 79, 2], [63, 4, 81, 2], [63, 8, 81, 8, "areSomeNotWorklets"], [63, 26, 81, 26], [63, 29, 81, 29, "gesture"], [63, 36, 81, 36], [63, 37, 81, 37, "handlers"], [63, 45, 81, 45], [63, 46, 81, 46, "isWorklet"], [63, 55, 81, 55], [63, 56, 81, 56, "includes"], [63, 64, 81, 64], [63, 65, 81, 65], [63, 70, 81, 70], [63, 71, 81, 71], [64, 4, 82, 2], [64, 8, 82, 8, "areSomeWorklets"], [64, 23, 82, 23], [64, 26, 82, 26, "gesture"], [64, 33, 82, 33], [64, 34, 82, 34, "handlers"], [64, 42, 82, 42], [64, 43, 82, 43, "isWorklet"], [64, 52, 82, 52], [64, 53, 82, 53, "includes"], [64, 61, 82, 61], [64, 62, 82, 62], [64, 66, 82, 66], [64, 67, 82, 67], [66, 4, 84, 2], [67, 4, 85, 2], [68, 4, 86, 2], [68, 8, 86, 6, "areSomeNotWorklets"], [68, 26, 86, 24], [68, 30, 86, 28, "areSomeWorklets"], [68, 45, 86, 43], [68, 47, 86, 45], [69, 6, 87, 4, "console"], [69, 13, 87, 11], [69, 14, 87, 12, "error"], [69, 19, 87, 17], [69, 20, 88, 6], [69, 24, 88, 6, "tagMessage"], [69, 41, 88, 16], [69, 43, 89, 8], [69, 310, 90, 6], [69, 311, 91, 4], [69, 312, 91, 5], [70, 4, 92, 2], [71, 4, 94, 2], [71, 8, 94, 6, "Reanimated"], [71, 37, 94, 16], [71, 42, 94, 21, "undefined"], [71, 51, 94, 30], [71, 53, 94, 32], [72, 6, 95, 4], [73, 6, 96, 4], [74, 4, 97, 2], [75, 4, 99, 2], [75, 8, 99, 8, "areAllNotWorklets"], [75, 25, 99, 25], [75, 28, 99, 28], [75, 29, 99, 29, "areSomeWorklets"], [75, 44, 99, 44], [75, 48, 99, 48, "areSomeNotWorklets"], [75, 66, 99, 66], [76, 4, 100, 2], [77, 4, 101, 2], [78, 4, 102, 2], [78, 8, 102, 6, "areAllNotWorklets"], [78, 25, 102, 23], [78, 29, 102, 27], [78, 30, 102, 28], [78, 34, 102, 28, "isTestEnv"], [78, 50, 102, 37], [78, 52, 102, 38], [78, 53, 102, 39], [78, 55, 102, 41], [79, 6, 103, 4, "console"], [79, 13, 103, 11], [79, 14, 103, 12, "warn"], [79, 18, 103, 16], [79, 19, 104, 6], [79, 23, 104, 6, "tagMessage"], [79, 40, 104, 16], [79, 42, 105, 8], [79, 276, 106, 6], [79, 277, 107, 4], [79, 278, 107, 5], [80, 4, 108, 2], [81, 2, 109, 0], [83, 2, 111, 0], [84, 2, 112, 7], [84, 11, 112, 16, "validateDetectorChildren"], [84, 35, 112, 40, "validateDetectorChildren"], [84, 36, 112, 41, "ref"], [84, 39, 112, 49], [84, 41, 112, 51], [85, 4, 113, 2], [86, 4, 114, 2], [87, 4, 115, 2], [88, 4, 116, 2], [89, 4, 117, 2], [90, 4, 118, 2], [91, 4, 119, 2], [92, 4, 120, 2], [93, 4, 121, 2], [94, 4, 122, 2], [95, 4, 123, 2], [96, 4, 124, 2], [97, 4, 125, 2], [98, 4, 126, 2], [99, 4, 127, 2], [100, 4, 128, 2], [101, 4, 129, 2], [102, 4, 130, 2], [103, 4, 131, 2], [104, 4, 132, 2], [105, 4, 133, 2], [106, 4, 134, 2], [106, 8, 134, 6, "__DEV__"], [106, 15, 134, 13], [106, 19, 134, 17, "Platform"], [106, 40, 134, 25], [106, 41, 134, 26, "OS"], [106, 43, 134, 28], [106, 48, 134, 33], [106, 53, 134, 38], [106, 55, 134, 40], [107, 6, 135, 4], [108, 6, 136, 4], [108, 10, 136, 10, "wrapType"], [108, 18, 136, 18], [109, 6, 137, 6], [110, 6, 138, 6, "ref"], [110, 9, 138, 9], [110, 10, 138, 10, "_reactInternals"], [110, 25, 138, 25], [110, 26, 138, 26, "elementType"], [110, 37, 138, 37], [112, 6, 140, 4], [113, 6, 141, 4], [113, 10, 141, 8, "instance"], [113, 18, 141, 16], [113, 21, 142, 6, "<PERSON><PERSON><PERSON><PERSON>"], [113, 43, 142, 16], [113, 44, 142, 17, "findHostInstance_DEPRECATED"], [113, 71, 142, 44], [113, 72, 143, 8, "ref"], [113, 75, 144, 6], [113, 76, 144, 7], [113, 77, 144, 8, "_internalFiberInstanceHandleDEV"], [113, 108, 144, 39], [115, 6, 146, 4], [116, 6, 147, 4], [116, 13, 147, 11, "instance"], [116, 21, 147, 19], [116, 25, 147, 23, "instance"], [116, 33, 147, 31], [116, 34, 147, 32, "elementType"], [116, 45, 147, 43], [116, 50, 147, 48, "wrapType"], [116, 58, 147, 56], [116, 60, 147, 58], [117, 8, 148, 6], [118, 8, 149, 6], [118, 12, 149, 10, "instance"], [118, 20, 149, 18], [118, 21, 149, 19, "sibling"], [118, 28, 149, 26], [118, 30, 149, 28], [119, 10, 150, 8], [119, 16, 150, 14], [119, 20, 150, 18, "Error"], [119, 25, 150, 23], [119, 26, 151, 10], [119, 269, 152, 8], [119, 270, 152, 9], [120, 8, 153, 6], [122, 8, 155, 6], [123, 8, 156, 6, "instance"], [123, 16, 156, 14], [123, 19, 156, 17, "instance"], [123, 27, 156, 25], [123, 28, 156, 26, "return"], [123, 34, 156, 32], [124, 6, 157, 4], [125, 4, 158, 2], [126, 2, 159, 0], [127, 2, 161, 7], [127, 11, 161, 16, "useForceRender"], [127, 25, 161, 30, "useForceRender"], [127, 26, 161, 30], [127, 28, 161, 33], [128, 4, 162, 2], [128, 8, 162, 2, "_useState"], [128, 17, 162, 2], [128, 20, 162, 40], [128, 24, 162, 40, "useState"], [128, 39, 162, 48], [128, 41, 162, 49], [128, 46, 162, 54], [128, 47, 162, 55], [129, 6, 162, 55, "_useState2"], [129, 16, 162, 55], [129, 23, 162, 55, "_slicedToArray2"], [129, 38, 162, 55], [129, 39, 162, 55, "default"], [129, 46, 162, 55], [129, 48, 162, 55, "_useState"], [129, 57, 162, 55], [130, 6, 162, 9, "renderState"], [130, 17, 162, 20], [130, 20, 162, 20, "_useState2"], [130, 30, 162, 20], [131, 6, 162, 22, "setRenderState"], [131, 20, 162, 36], [131, 23, 162, 36, "_useState2"], [131, 33, 162, 36], [132, 4, 163, 2], [132, 8, 163, 8, "forceRender"], [132, 19, 163, 19], [132, 22, 163, 22], [132, 26, 163, 22, "useCallback"], [132, 44, 163, 33], [132, 46, 163, 34], [132, 52, 163, 40], [133, 6, 164, 4, "setRenderState"], [133, 20, 164, 18], [133, 21, 164, 19], [133, 22, 164, 20, "renderState"], [133, 33, 164, 31], [133, 34, 164, 32], [134, 4, 165, 2], [134, 5, 165, 3], [134, 7, 165, 5], [134, 8, 165, 6, "renderState"], [134, 19, 165, 17], [134, 21, 165, 19, "setRenderState"], [134, 35, 165, 33], [134, 36, 165, 34], [134, 37, 165, 35], [135, 4, 167, 2], [135, 11, 167, 9, "forceRender"], [135, 22, 167, 20], [136, 2, 168, 0], [137, 2, 170, 7], [137, 11, 170, 16, "useWebEventHandlers"], [137, 30, 170, 35, "useWebEventHandlers"], [137, 31, 170, 35], [137, 33, 170, 38], [138, 4, 171, 2], [138, 11, 171, 9], [138, 15, 171, 9, "useRef"], [138, 28, 171, 15], [138, 30, 171, 33], [139, 6, 172, 4, "onGestureHandlerEvent"], [139, 27, 172, 25], [139, 29, 172, 28, "e"], [139, 30, 172, 63], [139, 34, 172, 68], [140, 8, 173, 6], [140, 12, 173, 6, "onGestureHandlerEvent"], [140, 48, 173, 27], [140, 50, 173, 28, "e"], [140, 51, 173, 29], [140, 52, 173, 30, "nativeEvent"], [140, 63, 173, 41], [140, 64, 173, 42], [141, 6, 174, 4], [141, 7, 174, 5], [142, 6, 175, 4, "onGestureHandlerStateChange"], [142, 33, 175, 31], [142, 35, 175, 33], [142, 39, 175, 33, "isNewWebImplementationEnabled"], [142, 96, 175, 62], [142, 98, 175, 63], [142, 99, 175, 64], [142, 102, 176, 9, "e"], [142, 103, 176, 44], [142, 107, 176, 49], [143, 8, 177, 10], [143, 12, 177, 10, "onGestureHandlerEvent"], [143, 48, 177, 31], [143, 50, 177, 32, "e"], [143, 51, 177, 33], [143, 52, 177, 34, "nativeEvent"], [143, 63, 177, 45], [143, 64, 177, 46], [144, 6, 178, 8], [144, 7, 178, 9], [144, 10, 179, 8, "undefined"], [145, 4, 180, 2], [145, 5, 180, 3], [145, 6, 180, 4], [146, 2, 181, 0], [147, 0, 181, 1], [147, 3]], "functionMap": {"names": ["<global>", "convertToHandlerTag", "extractValidHandlerTags", "interactionGroup.map.filter$argument_0", "extractGestureRelations", "checkGestureCallbacksForWorklets", "validateDetectorChildren", "useForceRender", "forceRender", "useWebEventHandlers", "useRef$argument_0.onGestureHandlerEvent", "<anonymous>"], "mappings": "AAA;ACsC;CDU;AEE;uDCE,gBD;CFE;OIE;CJY;OKE;CLsC;OMG;CN+C;OOE;kCCE;GDE;CPG;OSE;2BCE;KDE;QEE;SFE;CTG"}}, "type": "js/module"}]}