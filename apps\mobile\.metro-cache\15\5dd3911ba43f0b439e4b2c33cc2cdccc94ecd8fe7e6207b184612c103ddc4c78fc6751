{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "@react-navigation/elements", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 92, "index": 107}}], "key": "LmqW7jh+SpCzQZMkzh+Awcuawt0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 108}, "end": {"line": 4, "column": 31, "index": 139}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 140}, "end": {"line": 5, "column": 68, "index": 208}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../../TransitionConfigs/CardStyleInterpolators.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 209}, "end": {"line": 6, "column": 130, "index": 339}}], "key": "WQrnT432sHc0IAjfxvy+8kcpVqE=", "exportNames": ["*"]}}, {"name": "../../TransitionConfigs/TransitionPresets.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 340}, "end": {"line": 7, "column": 295, "index": 635}}], "key": "qGPvdCHa12B7My1IcTBjt/ZjEN8=", "exportNames": ["*"]}}, {"name": "../../utils/findLastIndex.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 636}, "end": {"line": 8, "column": 61, "index": 697}}], "key": "PsurlFEEk02i+2hKfT+H0LvqIFM=", "exportNames": ["*"]}}, {"name": "../../utils/getDistanceForDirection.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 698}, "end": {"line": 9, "column": 81, "index": 779}}], "key": "Qhu6HWUnevuLnK0FD4YMv38jV6c=", "exportNames": ["*"]}}, {"name": "../../utils/getModalRoutesKeys.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 780}, "end": {"line": 10, "column": 70, "index": 850}}], "key": "JnU1xJs101Gng0l1FTqwlJFPlho=", "exportNames": ["*"]}}, {"name": "../Screens.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 851}, "end": {"line": 11, "column": 66, "index": 917}}], "key": "C/CQg4G4EMSw5ow404L5fcHq3N8=", "exportNames": ["*"]}}, {"name": "./CardContainer.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 918}, "end": {"line": 12, "column": 51, "index": 969}}], "key": "dRNK4dXmIFRg8IMvnxRQLcKAgls=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 970}, "end": {"line": 13, "column": 63, "index": 1033}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.CardStack = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _elements = require(_dependencyMap[6], \"@react-navigation/elements\");\n  var React = _interopRequireWildcard(require(_dependencyMap[7], \"react\"));\n  var _reactNative = require(_dependencyMap[8], \"react-native\");\n  var _CardStyleInterpolators = require(_dependencyMap[9], \"../../TransitionConfigs/CardStyleInterpolators.js\");\n  var _TransitionPresets = require(_dependencyMap[10], \"../../TransitionConfigs/TransitionPresets.js\");\n  var _findLastIndex = require(_dependencyMap[11], \"../../utils/findLastIndex.js\");\n  var _getDistanceForDirection = require(_dependencyMap[12], \"../../utils/getDistanceForDirection.js\");\n  var _getModalRoutesKeys = require(_dependencyMap[13], \"../../utils/getModalRoutesKeys.js\");\n  var _Screens = require(_dependencyMap[14], \"../Screens.js\");\n  var _CardContainer = require(_dependencyMap[15], \"./CardContainer.js\");\n  var _jsxRuntime = require(_dependencyMap[16], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var NAMED_TRANSITIONS_PRESETS = {\n    default: _TransitionPresets.DefaultTransition,\n    fade: _TransitionPresets.ModalFadeTransition,\n    fade_from_bottom: _TransitionPresets.FadeFromBottomAndroid,\n    fade_from_right: _TransitionPresets.FadeFromRightAndroid,\n    none: _TransitionPresets.DefaultTransition,\n    reveal_from_bottom: _TransitionPresets.RevealFromBottomAndroid,\n    scale_from_center: _TransitionPresets.ScaleFromCenterAndroid,\n    slide_from_left: _TransitionPresets.SlideFromLeftIOS,\n    slide_from_right: _TransitionPresets.SlideFromRightIOS,\n    slide_from_bottom: _reactNative.Platform.select({\n      ios: _TransitionPresets.ModalSlideFromBottomIOS,\n      default: _TransitionPresets.BottomSheetAndroid\n    })\n  };\n  var EPSILON = 1e-5;\n  var STATE_INACTIVE = 0;\n  var STATE_TRANSITIONING_OR_BELOW_TOP = 1;\n  var STATE_ON_TOP = 2;\n  var FALLBACK_DESCRIPTOR = Object.freeze({\n    options: {}\n  });\n  var getInterpolationIndex = (scenes, index) => {\n    var cardStyleInterpolator = scenes[index].descriptor.options.cardStyleInterpolator;\n\n    // Start from current card and count backwards the number of cards with same interpolation\n    var interpolationIndex = 0;\n    for (var i = index - 1; i >= 0; i--) {\n      var cardStyleInterpolatorCurrent = scenes[i]?.descriptor.options.cardStyleInterpolator;\n      if (cardStyleInterpolatorCurrent !== cardStyleInterpolator) {\n        break;\n      }\n      interpolationIndex++;\n    }\n    return interpolationIndex;\n  };\n  var getIsModalPresentation = cardStyleInterpolator => {\n    return cardStyleInterpolator === _CardStyleInterpolators.forModalPresentationIOS ||\n    // Handle custom modal presentation interpolators as well\n    cardStyleInterpolator.name === 'forModalPresentationIOS';\n  };\n  var getIsModal = (scene, interpolationIndex, isParentModal) => {\n    if (isParentModal) {\n      return true;\n    }\n    var cardStyleInterpolator = scene.descriptor.options.cardStyleInterpolator;\n    var isModalPresentation = getIsModalPresentation(cardStyleInterpolator);\n    var isModal = isModalPresentation && interpolationIndex !== 0;\n    return isModal;\n  };\n  var getHeaderHeights = (scenes, insets, isParentHeaderShown, isParentModal, layout, previous) => {\n    return scenes.reduce((acc, curr, index) => {\n      var _curr$descriptor$opti = curr.descriptor.options,\n        _curr$descriptor$opti2 = _curr$descriptor$opti.headerStatusBarHeight,\n        headerStatusBarHeight = _curr$descriptor$opti2 === void 0 ? isParentHeaderShown ? 0 : insets.top : _curr$descriptor$opti2,\n        headerStyle = _curr$descriptor$opti.headerStyle;\n      var style = _reactNative.StyleSheet.flatten(headerStyle || {});\n      var height = 'height' in style && typeof style.height === 'number' ? style.height : previous[curr.route.key];\n      var interpolationIndex = getInterpolationIndex(scenes, index);\n      var isModal = getIsModal(curr, interpolationIndex, isParentModal);\n      acc[curr.route.key] = typeof height === 'number' ? height : (0, _elements.getDefaultHeaderHeight)(layout, isModal, headerStatusBarHeight);\n      return acc;\n    }, {});\n  };\n  var getDistanceFromOptions = (layout, descriptor, isRTL) => {\n    if (descriptor?.options.gestureDirection) {\n      return (0, _getDistanceForDirection.getDistanceForDirection)(layout, descriptor?.options.gestureDirection, isRTL);\n    }\n    var defaultGestureDirection = descriptor?.options.presentation === 'modal' ? _TransitionPresets.ModalTransition.gestureDirection : _TransitionPresets.DefaultTransition.gestureDirection;\n    var gestureDirection = descriptor?.options.animation ? NAMED_TRANSITIONS_PRESETS[descriptor?.options.animation]?.gestureDirection : defaultGestureDirection;\n    return (0, _getDistanceForDirection.getDistanceForDirection)(layout, gestureDirection, isRTL);\n  };\n  var getProgressFromGesture = (gesture, layout, descriptor, isRTL) => {\n    var distance = getDistanceFromOptions({\n      // Make sure that we have a non-zero distance, otherwise there will be incorrect progress\n      // This causes blank screen on web if it was previously inside container with display: none\n      width: Math.max(1, layout.width),\n      height: Math.max(1, layout.height)\n    }, descriptor, isRTL);\n    if (distance > 0) {\n      return gesture.interpolate({\n        inputRange: [0, distance],\n        outputRange: [1, 0]\n      });\n    }\n    return gesture.interpolate({\n      inputRange: [distance, 0],\n      outputRange: [0, 1]\n    });\n  };\n  var CardStack = exports.CardStack = /*#__PURE__*/function (_React$Component) {\n    function CardStack(_props) {\n      var _this;\n      (0, _classCallCheck2.default)(this, CardStack);\n      _this = _callSuper(this, CardStack, [_props]);\n      _this.handleLayout = e => {\n        var _e$nativeEvent$layout = e.nativeEvent.layout,\n          height = _e$nativeEvent$layout.height,\n          width = _e$nativeEvent$layout.width;\n        var layout = {\n          width,\n          height\n        };\n        _this.setState((state, props) => {\n          if (height === state.layout.height && width === state.layout.width) {\n            return null;\n          }\n          return {\n            layout,\n            headerHeights: getHeaderHeights(state.scenes, props.insets, props.isParentHeaderShown, props.isParentModal, layout, state.headerHeights)\n          };\n        });\n      };\n      _this.handleHeaderLayout = _ref => {\n        var route = _ref.route,\n          height = _ref.height;\n        _this.setState(_ref2 => {\n          var headerHeights = _ref2.headerHeights;\n          var previousHeight = headerHeights[route.key];\n          if (previousHeight === height) {\n            return null;\n          }\n          return {\n            headerHeights: {\n              ...headerHeights,\n              [route.key]: height\n            }\n          };\n        });\n      };\n      _this.getFocusedRoute = () => {\n        var state = _this.props.state;\n        return state.routes[state.index];\n      };\n      _this.getPreviousScene = _ref3 => {\n        var route = _ref3.route;\n        var getPreviousRoute = _this.props.getPreviousRoute;\n        var scenes = _this.state.scenes;\n        var previousRoute = getPreviousRoute({\n          route\n        });\n        if (previousRoute) {\n          var previousScene = scenes.find(scene => scene.descriptor.route.key === previousRoute.key);\n          return previousScene;\n        }\n        return undefined;\n      };\n      _this.state = {\n        routes: [],\n        scenes: [],\n        gestures: {},\n        layout: _elements.SafeAreaProviderCompat.initialMetrics.frame,\n        descriptors: _this.props.descriptors,\n        // Used when card's header is null and mode is float to make transition\n        // between screens with headers and those without headers smooth.\n        // This is not a great heuristic here. We don't know synchronously\n        // on mount what the header height is so we have just used the most\n        // common cases here.\n        headerHeights: {}\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(CardStack, _React$Component);\n    return (0, _createClass2.default)(CardStack, [{\n      key: \"render\",\n      value: function render() {\n        var _this$props = this.props,\n          insets = _this$props.insets,\n          state = _this$props.state,\n          routes = _this$props.routes,\n          openingRouteKeys = _this$props.openingRouteKeys,\n          closingRouteKeys = _this$props.closingRouteKeys,\n          onOpenRoute = _this$props.onOpenRoute,\n          onCloseRoute = _this$props.onCloseRoute,\n          renderHeader = _this$props.renderHeader,\n          isParentHeaderShown = _this$props.isParentHeaderShown,\n          isParentModal = _this$props.isParentModal,\n          onTransitionStart = _this$props.onTransitionStart,\n          onTransitionEnd = _this$props.onTransitionEnd,\n          onGestureStart = _this$props.onGestureStart,\n          onGestureEnd = _this$props.onGestureEnd,\n          onGestureCancel = _this$props.onGestureCancel,\n          _this$props$detachIna = _this$props.detachInactiveScreens,\n          detachInactiveScreens = _this$props$detachIna === void 0 ? _reactNative.Platform.OS === 'web' || _reactNative.Platform.OS === 'android' || _reactNative.Platform.OS === 'ios' : _this$props$detachIna;\n        var _this$state = this.state,\n          scenes = _this$state.scenes,\n          layout = _this$state.layout,\n          gestures = _this$state.gestures,\n          headerHeights = _this$state.headerHeights;\n        var focusedRoute = state.routes[state.index];\n        var focusedHeaderHeight = headerHeights[focusedRoute.key];\n        var isFloatHeaderAbsolute = this.state.scenes.slice(-2).some(scene => {\n          var options = scene.descriptor.options ?? {};\n          var headerMode = options.headerMode,\n            headerTransparent = options.headerTransparent,\n            _options$headerShown = options.headerShown,\n            headerShown = _options$headerShown === void 0 ? true : _options$headerShown;\n          if (headerTransparent || headerShown === false || headerMode === 'screen') {\n            return true;\n          }\n          return false;\n        });\n        var activeScreensLimit = 1;\n        for (var i = scenes.length - 1; i >= 0; i--) {\n          var options = scenes[i].descriptor.options;\n          var _options$detachPrevio = options.detachPreviousScreen,\n            detachPreviousScreen = _options$detachPrevio === void 0 ? options.presentation === 'transparentModal' ? false : getIsModalPresentation(options.cardStyleInterpolator) ? i !== (0, _findLastIndex.findLastIndex)(scenes, scene => {\n              var cardStyleInterpolator = scene.descriptor.options.cardStyleInterpolator;\n              return cardStyleInterpolator === _CardStyleInterpolators.forModalPresentationIOS || cardStyleInterpolator?.name === 'forModalPresentationIOS';\n            }) : true : _options$detachPrevio;\n          if (detachPreviousScreen === false) {\n            activeScreensLimit++;\n          } else {\n            // Check at least last 2 screens before stopping\n            // This will make sure that screen isn't detached when another screen is animating on top of the transparent one\n            // For example, (Opaque -> Transparent -> Opaque)\n            if (i <= scenes.length - 2) {\n              break;\n            }\n          }\n        }\n        var floatingHeader = /*#__PURE__*/(0, _jsxRuntime.jsx)(React.Fragment, {\n          children: renderHeader({\n            mode: 'float',\n            layout,\n            scenes,\n            getPreviousScene: this.getPreviousScene,\n            getFocusedRoute: this.getFocusedRoute,\n            onContentHeightChange: this.handleHeaderLayout,\n            style: [styles.floating, isFloatHeaderAbsolute && [\n            // Without this, the header buttons won't be touchable on Android when headerTransparent: true\n            {\n              height: focusedHeaderHeight\n            }, styles.absolute]]\n          })\n        }, \"header\");\n        return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {\n          style: styles.container,\n          children: [isFloatHeaderAbsolute ? null : floatingHeader, /*#__PURE__*/(0, _jsxRuntime.jsx)(_Screens.MaybeScreenContainer, {\n            enabled: detachInactiveScreens,\n            style: styles.container,\n            onLayout: this.handleLayout,\n            children: [...routes, ...state.preloadedRoutes].map((route, index) => {\n              var focused = focusedRoute.key === route.key;\n              var gesture = gestures[route.key];\n              var scene = scenes[index];\n              // It is possible that for a short period the route appears in both arrays.\n              // Particularly, if the screen is removed with `retain`, then it needs a moment to execute the animation.\n              // However, due to the router action, it immediately populates the `preloadedRoutes` array.\n              // Practically, the logic below takes care that it is rendered only once.\n              var isPreloaded = state.preloadedRoutes.includes(route) && !routes.includes(route);\n              if (state.preloadedRoutes.includes(route) && routes.includes(route) && index >= routes.length) {\n                return null;\n              }\n\n              // For the screens that shouldn't be active, the value is 0\n              // For those that should be active, but are not the top screen, the value is 1\n              // For those on top of the stack and with interaction enabled, the value is 2\n              // For the old implementation, it stays the same it was\n              var isScreenActive = 1;\n              if (index < routes.length - activeScreensLimit - 1 || isPreloaded) {\n                // screen should be inactive because it is too deep in the stack\n                isScreenActive = STATE_INACTIVE;\n              } else {\n                var sceneForActivity = scenes[routes.length - 1];\n                var outputValue = index === routes.length - 1 ? STATE_ON_TOP // the screen is on top after the transition\n                : index >= routes.length - activeScreensLimit ? STATE_TRANSITIONING_OR_BELOW_TOP // the screen should stay active after the transition, it is not on top but is in activeLimit\n                : STATE_INACTIVE; // the screen should be active only during the transition, it is at the edge of activeLimit\n                isScreenActive = sceneForActivity ? sceneForActivity.progress.current.interpolate({\n                  inputRange: [0, 1 - EPSILON, 1],\n                  outputRange: [1, 1, outputValue],\n                  extrapolate: 'clamp'\n                }) : STATE_TRANSITIONING_OR_BELOW_TOP;\n              }\n              var _scene$descriptor$opt = scene.descriptor.options,\n                _scene$descriptor$opt2 = _scene$descriptor$opt.headerShown,\n                headerShown = _scene$descriptor$opt2 === void 0 ? true : _scene$descriptor$opt2,\n                headerTransparent = _scene$descriptor$opt.headerTransparent,\n                freezeOnBlur = _scene$descriptor$opt.freezeOnBlur,\n                autoHideHomeIndicator = _scene$descriptor$opt.autoHideHomeIndicator;\n              var safeAreaInsetTop = insets.top;\n              var safeAreaInsetRight = insets.right;\n              var safeAreaInsetBottom = insets.bottom;\n              var safeAreaInsetLeft = insets.left;\n              var headerHeight = headerShown !== false ? headerHeights[route.key] : 0;\n\n              // Start from current card and count backwards the number of cards with same interpolation\n              var interpolationIndex = getInterpolationIndex(scenes, index);\n              var isModal = getIsModal(scene, interpolationIndex, isParentModal);\n              var isNextScreenTransparent = scenes[index + 1]?.descriptor.options.presentation === 'transparentModal';\n              var detachCurrentScreen = scenes[index + 1]?.descriptor.options.detachPreviousScreen !== false;\n              return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Screens.MaybeScreen, {\n                style: [_reactNative.StyleSheet.absoluteFill],\n                enabled: detachInactiveScreens,\n                active: isScreenActive,\n                freezeOnBlur: freezeOnBlur,\n                shouldFreeze: isScreenActive === STATE_INACTIVE && !isPreloaded,\n                homeIndicatorHidden: autoHideHomeIndicator,\n                pointerEvents: \"box-none\",\n                children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_CardContainer.CardContainer, {\n                  index: index,\n                  interpolationIndex: interpolationIndex,\n                  modal: isModal,\n                  active: index === routes.length - 1,\n                  focused: focused,\n                  opening: openingRouteKeys.includes(route.key),\n                  closing: closingRouteKeys.includes(route.key),\n                  layout: layout,\n                  gesture: gesture,\n                  scene: scene,\n                  safeAreaInsetTop: safeAreaInsetTop,\n                  safeAreaInsetRight: safeAreaInsetRight,\n                  safeAreaInsetBottom: safeAreaInsetBottom,\n                  safeAreaInsetLeft: safeAreaInsetLeft,\n                  onGestureStart: onGestureStart,\n                  onGestureCancel: onGestureCancel,\n                  onGestureEnd: onGestureEnd,\n                  headerHeight: headerHeight,\n                  isParentHeaderShown: isParentHeaderShown,\n                  onHeaderHeightChange: this.handleHeaderLayout,\n                  getPreviousScene: this.getPreviousScene,\n                  getFocusedRoute: this.getFocusedRoute,\n                  hasAbsoluteFloatHeader: isFloatHeaderAbsolute && !headerTransparent,\n                  renderHeader: renderHeader,\n                  onOpenRoute: onOpenRoute,\n                  onCloseRoute: onCloseRoute,\n                  onTransitionStart: onTransitionStart,\n                  onTransitionEnd: onTransitionEnd,\n                  isNextScreenTransparent: isNextScreenTransparent,\n                  detachCurrentScreen: detachCurrentScreen,\n                  preloaded: isPreloaded\n                })\n              }, route.key);\n            })\n          }), isFloatHeaderAbsolute ? floatingHeader : null]\n        });\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(props, state) {\n        if (props.routes === state.routes && props.descriptors === state.descriptors) {\n          return null;\n        }\n        var gestures = [...props.routes, ...props.state.preloadedRoutes].reduce((acc, curr) => {\n          var descriptor = props.descriptors[curr.key] || props.preloadedDescriptors[curr.key];\n          var _ref4 = descriptor?.options || {},\n            animation = _ref4.animation;\n          acc[curr.key] = state.gestures[curr.key] || new _reactNative.Animated.Value(props.openingRouteKeys.includes(curr.key) && animation !== 'none' || props.state.preloadedRoutes.includes(curr) ? getDistanceFromOptions(state.layout, descriptor, props.direction === 'rtl') : 0);\n          return acc;\n        }, {});\n        var modalRouteKeys = (0, _getModalRoutesKeys.getModalRouteKeys)([...props.routes, ...props.state.preloadedRoutes], {\n          ...props.descriptors,\n          ...props.preloadedDescriptors\n        });\n        var scenes = [...props.routes, ...props.state.preloadedRoutes].map((route, index, self) => {\n          // For preloaded screens, we don't care about the previous and the next screen\n          var isPreloaded = props.state.preloadedRoutes.includes(route);\n          var previousRoute = isPreloaded ? undefined : self[index - 1];\n          var nextRoute = isPreloaded ? undefined : self[index + 1];\n          var oldScene = state.scenes[index];\n          var currentGesture = gestures[route.key];\n          var previousGesture = previousRoute ? gestures[previousRoute.key] : undefined;\n          var nextGesture = nextRoute ? gestures[nextRoute.key] : undefined;\n          var descriptor = (isPreloaded ? props.preloadedDescriptors : props.descriptors)[route.key] || state.descriptors[route.key] || (oldScene ? oldScene.descriptor : FALLBACK_DESCRIPTOR);\n          var nextDescriptor = nextRoute && (props.descriptors[nextRoute?.key] || state.descriptors[nextRoute?.key]);\n          var previousDescriptor = previousRoute && (props.descriptors[previousRoute?.key] || state.descriptors[previousRoute?.key]);\n\n          // When a screen is not the last, it should use next screen's transition config\n          // Many transitions also animate the previous screen, so using 2 different transitions doesn't look right\n          // For example combining a slide and a modal transition would look wrong otherwise\n          // With this approach, combining different transition styles in the same navigator mostly looks right\n          // This will still be broken when 2 transitions have different idle state (e.g. modal presentation),\n          // but the majority of the transitions look alright\n          var optionsForTransitionConfig = index !== self.length - 1 && nextDescriptor && nextDescriptor.options.presentation !== 'transparentModal' ? nextDescriptor.options : descriptor.options;\n\n          // Assume modal if there are already modal screens in the stack\n          // or current screen is a modal when no presentation is specified\n          var isModal = modalRouteKeys.includes(route.key);\n\n          // Disable screen transition animation by default on web, windows and macos to match the native behavior\n          var excludedPlatforms = _reactNative.Platform.OS !== 'web' && _reactNative.Platform.OS !== 'windows' && _reactNative.Platform.OS !== 'macos';\n          var animation = optionsForTransitionConfig.animation ?? (excludedPlatforms ? 'default' : 'none');\n          var isAnimationEnabled = animation !== 'none';\n          var transitionPreset = animation !== 'default' ? NAMED_TRANSITIONS_PRESETS[animation] : isModal || optionsForTransitionConfig.presentation === 'modal' ? _TransitionPresets.ModalTransition : optionsForTransitionConfig.presentation === 'transparentModal' ? _TransitionPresets.ModalFadeTransition : _TransitionPresets.DefaultTransition;\n          var _optionsForTransition = optionsForTransitionConfig.gestureEnabled,\n            gestureEnabled = _optionsForTransition === void 0 ? _reactNative.Platform.OS === 'ios' && isAnimationEnabled : _optionsForTransition,\n            _optionsForTransition2 = optionsForTransitionConfig.gestureDirection,\n            gestureDirection = _optionsForTransition2 === void 0 ? transitionPreset.gestureDirection : _optionsForTransition2,\n            _optionsForTransition3 = optionsForTransitionConfig.transitionSpec,\n            transitionSpec = _optionsForTransition3 === void 0 ? transitionPreset.transitionSpec : _optionsForTransition3,\n            _optionsForTransition4 = optionsForTransitionConfig.cardStyleInterpolator,\n            cardStyleInterpolator = _optionsForTransition4 === void 0 ? isAnimationEnabled ? transitionPreset.cardStyleInterpolator : _CardStyleInterpolators.forNoAnimation : _optionsForTransition4,\n            _optionsForTransition5 = optionsForTransitionConfig.headerStyleInterpolator,\n            headerStyleInterpolator = _optionsForTransition5 === void 0 ? transitionPreset.headerStyleInterpolator : _optionsForTransition5,\n            _optionsForTransition6 = optionsForTransitionConfig.cardOverlayEnabled,\n            cardOverlayEnabled = _optionsForTransition6 === void 0 ? _reactNative.Platform.OS !== 'ios' && optionsForTransitionConfig.presentation !== 'transparentModal' || getIsModalPresentation(cardStyleInterpolator) : _optionsForTransition6;\n          var headerMode = descriptor.options.headerMode ?? (!(optionsForTransitionConfig.presentation === 'modal' || optionsForTransitionConfig.presentation === 'transparentModal' || nextDescriptor?.options.presentation === 'modal' || nextDescriptor?.options.presentation === 'transparentModal' || getIsModalPresentation(cardStyleInterpolator)) && _reactNative.Platform.OS === 'ios' && descriptor.options.header === undefined ? 'float' : 'screen');\n          var isRTL = props.direction === 'rtl';\n          var scene = {\n            route,\n            descriptor: {\n              ...descriptor,\n              options: {\n                ...descriptor.options,\n                animation,\n                cardOverlayEnabled,\n                cardStyleInterpolator,\n                gestureDirection,\n                gestureEnabled,\n                headerStyleInterpolator,\n                transitionSpec,\n                headerMode\n              }\n            },\n            progress: {\n              current: getProgressFromGesture(currentGesture, state.layout, descriptor, isRTL),\n              next: nextGesture && nextDescriptor?.options.presentation !== 'transparentModal' ? getProgressFromGesture(nextGesture, state.layout, nextDescriptor, isRTL) : undefined,\n              previous: previousGesture ? getProgressFromGesture(previousGesture, state.layout, previousDescriptor, isRTL) : undefined\n            },\n            __memo: [state.layout, descriptor, nextDescriptor, previousDescriptor, currentGesture, nextGesture, previousGesture]\n          };\n          if (oldScene && scene.__memo.every((it, i) => {\n            // @ts-expect-error: we haven't added __memo to the annotation to prevent usage elsewhere\n            return oldScene.__memo[i] === it;\n          })) {\n            return oldScene;\n          }\n          return scene;\n        });\n        return {\n          routes: props.routes,\n          scenes,\n          gestures,\n          descriptors: props.descriptors,\n          headerHeights: getHeaderHeights(scenes, props.insets, props.isParentHeaderShown, props.isParentModal, state.layout, state.headerHeights)\n        };\n      }\n    }]);\n  }(React.Component);\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flex: 1\n    },\n    absolute: {\n      position: 'absolute',\n      top: 0,\n      start: 0,\n      end: 0\n    },\n    floating: {\n      zIndex: 1\n    }\n  });\n});", "lineCount": 481, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "CardStack"], [8, 19, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_classCallCheck2"], [9, 22, 1, 13], [9, 25, 1, 13, "_interopRequireDefault"], [9, 47, 1, 13], [9, 48, 1, 13, "require"], [9, 55, 1, 13], [9, 56, 1, 13, "_dependencyMap"], [9, 70, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_createClass2"], [10, 19, 1, 13], [10, 22, 1, 13, "_interopRequireDefault"], [10, 44, 1, 13], [10, 45, 1, 13, "require"], [10, 52, 1, 13], [10, 53, 1, 13, "_dependencyMap"], [10, 67, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_possibleConstructorReturn2"], [11, 33, 1, 13], [11, 36, 1, 13, "_interopRequireDefault"], [11, 58, 1, 13], [11, 59, 1, 13, "require"], [11, 66, 1, 13], [11, 67, 1, 13, "_dependencyMap"], [11, 81, 1, 13], [12, 2, 1, 13], [12, 6, 1, 13, "_getPrototypeOf2"], [12, 22, 1, 13], [12, 25, 1, 13, "_interopRequireDefault"], [12, 47, 1, 13], [12, 48, 1, 13, "require"], [12, 55, 1, 13], [12, 56, 1, 13, "_dependencyMap"], [12, 70, 1, 13], [13, 2, 1, 13], [13, 6, 1, 13, "_inherits2"], [13, 16, 1, 13], [13, 19, 1, 13, "_interopRequireDefault"], [13, 41, 1, 13], [13, 42, 1, 13, "require"], [13, 49, 1, 13], [13, 50, 1, 13, "_dependencyMap"], [13, 64, 1, 13], [14, 2, 3, 0], [14, 6, 3, 0, "_elements"], [14, 15, 3, 0], [14, 18, 3, 0, "require"], [14, 25, 3, 0], [14, 26, 3, 0, "_dependencyMap"], [14, 40, 3, 0], [15, 2, 4, 0], [15, 6, 4, 0, "React"], [15, 11, 4, 0], [15, 14, 4, 0, "_interopRequireWildcard"], [15, 37, 4, 0], [15, 38, 4, 0, "require"], [15, 45, 4, 0], [15, 46, 4, 0, "_dependencyMap"], [15, 60, 4, 0], [16, 2, 5, 0], [16, 6, 5, 0, "_reactNative"], [16, 18, 5, 0], [16, 21, 5, 0, "require"], [16, 28, 5, 0], [16, 29, 5, 0, "_dependencyMap"], [16, 43, 5, 0], [17, 2, 6, 0], [17, 6, 6, 0, "_CardStyleInterpolators"], [17, 29, 6, 0], [17, 32, 6, 0, "require"], [17, 39, 6, 0], [17, 40, 6, 0, "_dependencyMap"], [17, 54, 6, 0], [18, 2, 7, 0], [18, 6, 7, 0, "_TransitionPresets"], [18, 24, 7, 0], [18, 27, 7, 0, "require"], [18, 34, 7, 0], [18, 35, 7, 0, "_dependencyMap"], [18, 49, 7, 0], [19, 2, 8, 0], [19, 6, 8, 0, "_findLastIndex"], [19, 20, 8, 0], [19, 23, 8, 0, "require"], [19, 30, 8, 0], [19, 31, 8, 0, "_dependencyMap"], [19, 45, 8, 0], [20, 2, 9, 0], [20, 6, 9, 0, "_getDistanceForDirection"], [20, 30, 9, 0], [20, 33, 9, 0, "require"], [20, 40, 9, 0], [20, 41, 9, 0, "_dependencyMap"], [20, 55, 9, 0], [21, 2, 10, 0], [21, 6, 10, 0, "_getModalRoutesKeys"], [21, 25, 10, 0], [21, 28, 10, 0, "require"], [21, 35, 10, 0], [21, 36, 10, 0, "_dependencyMap"], [21, 50, 10, 0], [22, 2, 11, 0], [22, 6, 11, 0, "_Screens"], [22, 14, 11, 0], [22, 17, 11, 0, "require"], [22, 24, 11, 0], [22, 25, 11, 0, "_dependencyMap"], [22, 39, 11, 0], [23, 2, 12, 0], [23, 6, 12, 0, "_CardContainer"], [23, 20, 12, 0], [23, 23, 12, 0, "require"], [23, 30, 12, 0], [23, 31, 12, 0, "_dependencyMap"], [23, 45, 12, 0], [24, 2, 13, 0], [24, 6, 13, 0, "_jsxRuntime"], [24, 17, 13, 0], [24, 20, 13, 0, "require"], [24, 27, 13, 0], [24, 28, 13, 0, "_dependencyMap"], [24, 42, 13, 0], [25, 2, 13, 63], [25, 11, 13, 63, "_interopRequireWildcard"], [25, 35, 13, 63, "e"], [25, 36, 13, 63], [25, 38, 13, 63, "t"], [25, 39, 13, 63], [25, 68, 13, 63, "WeakMap"], [25, 75, 13, 63], [25, 81, 13, 63, "r"], [25, 82, 13, 63], [25, 89, 13, 63, "WeakMap"], [25, 96, 13, 63], [25, 100, 13, 63, "n"], [25, 101, 13, 63], [25, 108, 13, 63, "WeakMap"], [25, 115, 13, 63], [25, 127, 13, 63, "_interopRequireWildcard"], [25, 150, 13, 63], [25, 162, 13, 63, "_interopRequireWildcard"], [25, 163, 13, 63, "e"], [25, 164, 13, 63], [25, 166, 13, 63, "t"], [25, 167, 13, 63], [25, 176, 13, 63, "t"], [25, 177, 13, 63], [25, 181, 13, 63, "e"], [25, 182, 13, 63], [25, 186, 13, 63, "e"], [25, 187, 13, 63], [25, 188, 13, 63, "__esModule"], [25, 198, 13, 63], [25, 207, 13, 63, "e"], [25, 208, 13, 63], [25, 214, 13, 63, "o"], [25, 215, 13, 63], [25, 217, 13, 63, "i"], [25, 218, 13, 63], [25, 220, 13, 63, "f"], [25, 221, 13, 63], [25, 226, 13, 63, "__proto__"], [25, 235, 13, 63], [25, 243, 13, 63, "default"], [25, 250, 13, 63], [25, 252, 13, 63, "e"], [25, 253, 13, 63], [25, 270, 13, 63, "e"], [25, 271, 13, 63], [25, 294, 13, 63, "e"], [25, 295, 13, 63], [25, 320, 13, 63, "e"], [25, 321, 13, 63], [25, 330, 13, 63, "f"], [25, 331, 13, 63], [25, 337, 13, 63, "o"], [25, 338, 13, 63], [25, 341, 13, 63, "t"], [25, 342, 13, 63], [25, 345, 13, 63, "n"], [25, 346, 13, 63], [25, 349, 13, 63, "r"], [25, 350, 13, 63], [25, 358, 13, 63, "o"], [25, 359, 13, 63], [25, 360, 13, 63, "has"], [25, 363, 13, 63], [25, 364, 13, 63, "e"], [25, 365, 13, 63], [25, 375, 13, 63, "o"], [25, 376, 13, 63], [25, 377, 13, 63, "get"], [25, 380, 13, 63], [25, 381, 13, 63, "e"], [25, 382, 13, 63], [25, 385, 13, 63, "o"], [25, 386, 13, 63], [25, 387, 13, 63, "set"], [25, 390, 13, 63], [25, 391, 13, 63, "e"], [25, 392, 13, 63], [25, 394, 13, 63, "f"], [25, 395, 13, 63], [25, 409, 13, 63, "_t"], [25, 411, 13, 63], [25, 415, 13, 63, "e"], [25, 416, 13, 63], [25, 432, 13, 63, "_t"], [25, 434, 13, 63], [25, 441, 13, 63, "hasOwnProperty"], [25, 455, 13, 63], [25, 456, 13, 63, "call"], [25, 460, 13, 63], [25, 461, 13, 63, "e"], [25, 462, 13, 63], [25, 464, 13, 63, "_t"], [25, 466, 13, 63], [25, 473, 13, 63, "i"], [25, 474, 13, 63], [25, 478, 13, 63, "o"], [25, 479, 13, 63], [25, 482, 13, 63, "Object"], [25, 488, 13, 63], [25, 489, 13, 63, "defineProperty"], [25, 503, 13, 63], [25, 508, 13, 63, "Object"], [25, 514, 13, 63], [25, 515, 13, 63, "getOwnPropertyDescriptor"], [25, 539, 13, 63], [25, 540, 13, 63, "e"], [25, 541, 13, 63], [25, 543, 13, 63, "_t"], [25, 545, 13, 63], [25, 552, 13, 63, "i"], [25, 553, 13, 63], [25, 554, 13, 63, "get"], [25, 557, 13, 63], [25, 561, 13, 63, "i"], [25, 562, 13, 63], [25, 563, 13, 63, "set"], [25, 566, 13, 63], [25, 570, 13, 63, "o"], [25, 571, 13, 63], [25, 572, 13, 63, "f"], [25, 573, 13, 63], [25, 575, 13, 63, "_t"], [25, 577, 13, 63], [25, 579, 13, 63, "i"], [25, 580, 13, 63], [25, 584, 13, 63, "f"], [25, 585, 13, 63], [25, 586, 13, 63, "_t"], [25, 588, 13, 63], [25, 592, 13, 63, "e"], [25, 593, 13, 63], [25, 594, 13, 63, "_t"], [25, 596, 13, 63], [25, 607, 13, 63, "f"], [25, 608, 13, 63], [25, 613, 13, 63, "e"], [25, 614, 13, 63], [25, 616, 13, 63, "t"], [25, 617, 13, 63], [26, 2, 13, 63], [26, 11, 13, 63, "_callSuper"], [26, 22, 13, 63, "t"], [26, 23, 13, 63], [26, 25, 13, 63, "o"], [26, 26, 13, 63], [26, 28, 13, 63, "e"], [26, 29, 13, 63], [26, 40, 13, 63, "o"], [26, 41, 13, 63], [26, 48, 13, 63, "_getPrototypeOf2"], [26, 64, 13, 63], [26, 65, 13, 63, "default"], [26, 72, 13, 63], [26, 74, 13, 63, "o"], [26, 75, 13, 63], [26, 82, 13, 63, "_possibleConstructorReturn2"], [26, 109, 13, 63], [26, 110, 13, 63, "default"], [26, 117, 13, 63], [26, 119, 13, 63, "t"], [26, 120, 13, 63], [26, 122, 13, 63, "_isNativeReflectConstruct"], [26, 147, 13, 63], [26, 152, 13, 63, "Reflect"], [26, 159, 13, 63], [26, 160, 13, 63, "construct"], [26, 169, 13, 63], [26, 170, 13, 63, "o"], [26, 171, 13, 63], [26, 173, 13, 63, "e"], [26, 174, 13, 63], [26, 186, 13, 63, "_getPrototypeOf2"], [26, 202, 13, 63], [26, 203, 13, 63, "default"], [26, 210, 13, 63], [26, 212, 13, 63, "t"], [26, 213, 13, 63], [26, 215, 13, 63, "constructor"], [26, 226, 13, 63], [26, 230, 13, 63, "o"], [26, 231, 13, 63], [26, 232, 13, 63, "apply"], [26, 237, 13, 63], [26, 238, 13, 63, "t"], [26, 239, 13, 63], [26, 241, 13, 63, "e"], [26, 242, 13, 63], [27, 2, 13, 63], [27, 11, 13, 63, "_isNativeReflectConstruct"], [27, 37, 13, 63], [27, 51, 13, 63, "t"], [27, 52, 13, 63], [27, 56, 13, 63, "Boolean"], [27, 63, 13, 63], [27, 64, 13, 63, "prototype"], [27, 73, 13, 63], [27, 74, 13, 63, "valueOf"], [27, 81, 13, 63], [27, 82, 13, 63, "call"], [27, 86, 13, 63], [27, 87, 13, 63, "Reflect"], [27, 94, 13, 63], [27, 95, 13, 63, "construct"], [27, 104, 13, 63], [27, 105, 13, 63, "Boolean"], [27, 112, 13, 63], [27, 145, 13, 63, "t"], [27, 146, 13, 63], [27, 159, 13, 63, "_isNativeReflectConstruct"], [27, 184, 13, 63], [27, 196, 13, 63, "_isNativeReflectConstruct"], [27, 197, 13, 63], [27, 210, 13, 63, "t"], [27, 211, 13, 63], [28, 2, 14, 0], [28, 6, 14, 6, "NAMED_TRANSITIONS_PRESETS"], [28, 31, 14, 31], [28, 34, 14, 34], [29, 4, 15, 2, "default"], [29, 11, 15, 9], [29, 13, 15, 11, "DefaultTransition"], [29, 49, 15, 28], [30, 4, 16, 2, "fade"], [30, 8, 16, 6], [30, 10, 16, 8, "ModalFadeTransition"], [30, 48, 16, 27], [31, 4, 17, 2, "fade_from_bottom"], [31, 20, 17, 18], [31, 22, 17, 20, "FadeFromBottomAndroid"], [31, 62, 17, 41], [32, 4, 18, 2, "fade_from_right"], [32, 19, 18, 17], [32, 21, 18, 19, "FadeFromRightAndroid"], [32, 60, 18, 39], [33, 4, 19, 2, "none"], [33, 8, 19, 6], [33, 10, 19, 8, "DefaultTransition"], [33, 46, 19, 25], [34, 4, 20, 2, "reveal_from_bottom"], [34, 22, 20, 20], [34, 24, 20, 22, "RevealFromBottomAndroid"], [34, 66, 20, 45], [35, 4, 21, 2, "scale_from_center"], [35, 21, 21, 19], [35, 23, 21, 21, "ScaleFromCenterAndroid"], [35, 64, 21, 43], [36, 4, 22, 2, "slide_from_left"], [36, 19, 22, 17], [36, 21, 22, 19, "SlideFromLeftIOS"], [36, 56, 22, 35], [37, 4, 23, 2, "slide_from_right"], [37, 20, 23, 18], [37, 22, 23, 20, "SlideFromRightIOS"], [37, 58, 23, 37], [38, 4, 24, 2, "slide_from_bottom"], [38, 21, 24, 19], [38, 23, 24, 21, "Platform"], [38, 44, 24, 29], [38, 45, 24, 30, "select"], [38, 51, 24, 36], [38, 52, 24, 37], [39, 6, 25, 4, "ios"], [39, 9, 25, 7], [39, 11, 25, 9, "ModalSlideFromBottomIOS"], [39, 53, 25, 32], [40, 6, 26, 4, "default"], [40, 13, 26, 11], [40, 15, 26, 13, "BottomSheetAndroid"], [41, 4, 27, 2], [41, 5, 27, 3], [42, 2, 28, 0], [42, 3, 28, 1], [43, 2, 29, 0], [43, 6, 29, 6, "EPSILON"], [43, 13, 29, 13], [43, 16, 29, 16], [43, 20, 29, 20], [44, 2, 30, 0], [44, 6, 30, 6, "STATE_INACTIVE"], [44, 20, 30, 20], [44, 23, 30, 23], [44, 24, 30, 24], [45, 2, 31, 0], [45, 6, 31, 6, "STATE_TRANSITIONING_OR_BELOW_TOP"], [45, 38, 31, 38], [45, 41, 31, 41], [45, 42, 31, 42], [46, 2, 32, 0], [46, 6, 32, 6, "STATE_ON_TOP"], [46, 18, 32, 18], [46, 21, 32, 21], [46, 22, 32, 22], [47, 2, 33, 0], [47, 6, 33, 6, "FALLBACK_DESCRIPTOR"], [47, 25, 33, 25], [47, 28, 33, 28, "Object"], [47, 34, 33, 34], [47, 35, 33, 35, "freeze"], [47, 41, 33, 41], [47, 42, 33, 42], [48, 4, 34, 2, "options"], [48, 11, 34, 9], [48, 13, 34, 11], [48, 14, 34, 12], [49, 2, 35, 0], [49, 3, 35, 1], [49, 4, 35, 2], [50, 2, 36, 0], [50, 6, 36, 6, "getInterpolationIndex"], [50, 27, 36, 27], [50, 30, 36, 30, "getInterpolationIndex"], [50, 31, 36, 31, "scenes"], [50, 37, 36, 37], [50, 39, 36, 39, "index"], [50, 44, 36, 44], [50, 49, 36, 49], [51, 4, 37, 2], [51, 8, 38, 4, "cardStyleInterpolator"], [51, 29, 38, 25], [51, 32, 39, 6, "scenes"], [51, 38, 39, 12], [51, 39, 39, 13, "index"], [51, 44, 39, 18], [51, 45, 39, 19], [51, 46, 39, 20, "descriptor"], [51, 56, 39, 30], [51, 57, 39, 31, "options"], [51, 64, 39, 38], [51, 65, 38, 4, "cardStyleInterpolator"], [51, 86, 38, 25], [53, 4, 41, 2], [54, 4, 42, 2], [54, 8, 42, 6, "interpolationIndex"], [54, 26, 42, 24], [54, 29, 42, 27], [54, 30, 42, 28], [55, 4, 43, 2], [55, 9, 43, 7], [55, 13, 43, 11, "i"], [55, 14, 43, 12], [55, 17, 43, 15, "index"], [55, 22, 43, 20], [55, 25, 43, 23], [55, 26, 43, 24], [55, 28, 43, 26, "i"], [55, 29, 43, 27], [55, 33, 43, 31], [55, 34, 43, 32], [55, 36, 43, 34, "i"], [55, 37, 43, 35], [55, 39, 43, 37], [55, 41, 43, 39], [56, 6, 44, 4], [56, 10, 44, 10, "cardStyleInterpolatorCurrent"], [56, 38, 44, 38], [56, 41, 44, 41, "scenes"], [56, 47, 44, 47], [56, 48, 44, 48, "i"], [56, 49, 44, 49], [56, 50, 44, 50], [56, 52, 44, 52, "descriptor"], [56, 62, 44, 62], [56, 63, 44, 63, "options"], [56, 70, 44, 70], [56, 71, 44, 71, "cardStyleInterpolator"], [56, 92, 44, 92], [57, 6, 45, 4], [57, 10, 45, 8, "cardStyleInterpolatorCurrent"], [57, 38, 45, 36], [57, 43, 45, 41, "cardStyleInterpolator"], [57, 64, 45, 62], [57, 66, 45, 64], [58, 8, 46, 6], [59, 6, 47, 4], [60, 6, 48, 4, "interpolationIndex"], [60, 24, 48, 22], [60, 26, 48, 24], [61, 4, 49, 2], [62, 4, 50, 2], [62, 11, 50, 9, "interpolationIndex"], [62, 29, 50, 27], [63, 2, 51, 0], [63, 3, 51, 1], [64, 2, 52, 0], [64, 6, 52, 6, "getIsModalPresentation"], [64, 28, 52, 28], [64, 31, 52, 31, "cardStyleInterpolator"], [64, 52, 52, 52], [64, 56, 52, 56], [65, 4, 53, 2], [65, 11, 53, 9, "cardStyleInterpolator"], [65, 32, 53, 30], [65, 37, 53, 35, "forModalPresentationIOS"], [65, 84, 53, 58], [66, 4, 54, 2], [67, 4, 55, 2, "cardStyleInterpolator"], [67, 25, 55, 23], [67, 26, 55, 24, "name"], [67, 30, 55, 28], [67, 35, 55, 33], [67, 60, 55, 58], [68, 2, 56, 0], [68, 3, 56, 1], [69, 2, 57, 0], [69, 6, 57, 6, "getIsModal"], [69, 16, 57, 16], [69, 19, 57, 19, "getIsModal"], [69, 20, 57, 20, "scene"], [69, 25, 57, 25], [69, 27, 57, 27, "interpolationIndex"], [69, 45, 57, 45], [69, 47, 57, 47, "isParentModal"], [69, 60, 57, 60], [69, 65, 57, 65], [70, 4, 58, 2], [70, 8, 58, 6, "isParentModal"], [70, 21, 58, 19], [70, 23, 58, 21], [71, 6, 59, 4], [71, 13, 59, 11], [71, 17, 59, 15], [72, 4, 60, 2], [73, 4, 61, 2], [73, 8, 62, 4, "cardStyleInterpolator"], [73, 29, 62, 25], [73, 32, 63, 6, "scene"], [73, 37, 63, 11], [73, 38, 63, 12, "descriptor"], [73, 48, 63, 22], [73, 49, 63, 23, "options"], [73, 56, 63, 30], [73, 57, 62, 4, "cardStyleInterpolator"], [73, 78, 62, 25], [74, 4, 64, 2], [74, 8, 64, 8, "isModalPresentation"], [74, 27, 64, 27], [74, 30, 64, 30, "getIsModalPresentation"], [74, 52, 64, 52], [74, 53, 64, 53, "cardStyleInterpolator"], [74, 74, 64, 74], [74, 75, 64, 75], [75, 4, 65, 2], [75, 8, 65, 8, "isModal"], [75, 15, 65, 15], [75, 18, 65, 18, "isModalPresentation"], [75, 37, 65, 37], [75, 41, 65, 41, "interpolationIndex"], [75, 59, 65, 59], [75, 64, 65, 64], [75, 65, 65, 65], [76, 4, 66, 2], [76, 11, 66, 9, "isModal"], [76, 18, 66, 16], [77, 2, 67, 0], [77, 3, 67, 1], [78, 2, 68, 0], [78, 6, 68, 6, "getHeaderHeights"], [78, 22, 68, 22], [78, 25, 68, 25, "getHeaderHeights"], [78, 26, 68, 26, "scenes"], [78, 32, 68, 32], [78, 34, 68, 34, "insets"], [78, 40, 68, 40], [78, 42, 68, 42, "isParentHeaderShown"], [78, 61, 68, 61], [78, 63, 68, 63, "isParentModal"], [78, 76, 68, 76], [78, 78, 68, 78, "layout"], [78, 84, 68, 84], [78, 86, 68, 86, "previous"], [78, 94, 68, 94], [78, 99, 68, 99], [79, 4, 69, 2], [79, 11, 69, 9, "scenes"], [79, 17, 69, 15], [79, 18, 69, 16, "reduce"], [79, 24, 69, 22], [79, 25, 69, 23], [79, 26, 69, 24, "acc"], [79, 29, 69, 27], [79, 31, 69, 29, "curr"], [79, 35, 69, 33], [79, 37, 69, 35, "index"], [79, 42, 69, 40], [79, 47, 69, 45], [80, 6, 70, 4], [80, 10, 70, 4, "_curr$descriptor$opti"], [80, 31, 70, 4], [80, 34, 73, 8, "curr"], [80, 38, 73, 12], [80, 39, 73, 13, "descriptor"], [80, 49, 73, 23], [80, 50, 73, 24, "options"], [80, 57, 73, 31], [81, 8, 73, 31, "_curr$descriptor$opti2"], [81, 30, 73, 31], [81, 33, 73, 31, "_curr$descriptor$opti"], [81, 54, 73, 31], [81, 55, 71, 6, "headerStatusBarHeight"], [81, 76, 71, 27], [82, 8, 71, 6, "headerStatusBarHeight"], [82, 29, 71, 27], [82, 32, 71, 27, "_curr$descriptor$opti2"], [82, 54, 71, 27], [82, 68, 71, 30, "isParentHeaderShown"], [82, 87, 71, 49], [82, 90, 71, 52], [82, 91, 71, 53], [82, 94, 71, 56, "insets"], [82, 100, 71, 62], [82, 101, 71, 63, "top"], [82, 104, 71, 66], [82, 107, 71, 66, "_curr$descriptor$opti2"], [82, 129, 71, 66], [83, 8, 72, 6, "headerStyle"], [83, 19, 72, 17], [83, 22, 72, 17, "_curr$descriptor$opti"], [83, 43, 72, 17], [83, 44, 72, 6, "headerStyle"], [83, 55, 72, 17], [84, 6, 74, 4], [84, 10, 74, 10, "style"], [84, 15, 74, 15], [84, 18, 74, 18, "StyleSheet"], [84, 41, 74, 28], [84, 42, 74, 29, "flatten"], [84, 49, 74, 36], [84, 50, 74, 37, "headerStyle"], [84, 61, 74, 48], [84, 65, 74, 52], [84, 66, 74, 53], [84, 67, 74, 54], [84, 68, 74, 55], [85, 6, 75, 4], [85, 10, 75, 10, "height"], [85, 16, 75, 16], [85, 19, 75, 19], [85, 27, 75, 27], [85, 31, 75, 31, "style"], [85, 36, 75, 36], [85, 40, 75, 40], [85, 47, 75, 47, "style"], [85, 52, 75, 52], [85, 53, 75, 53, "height"], [85, 59, 75, 59], [85, 64, 75, 64], [85, 72, 75, 72], [85, 75, 75, 75, "style"], [85, 80, 75, 80], [85, 81, 75, 81, "height"], [85, 87, 75, 87], [85, 90, 75, 90, "previous"], [85, 98, 75, 98], [85, 99, 75, 99, "curr"], [85, 103, 75, 103], [85, 104, 75, 104, "route"], [85, 109, 75, 109], [85, 110, 75, 110, "key"], [85, 113, 75, 113], [85, 114, 75, 114], [86, 6, 76, 4], [86, 10, 76, 10, "interpolationIndex"], [86, 28, 76, 28], [86, 31, 76, 31, "getInterpolationIndex"], [86, 52, 76, 52], [86, 53, 76, 53, "scenes"], [86, 59, 76, 59], [86, 61, 76, 61, "index"], [86, 66, 76, 66], [86, 67, 76, 67], [87, 6, 77, 4], [87, 10, 77, 10, "isModal"], [87, 17, 77, 17], [87, 20, 77, 20, "getIsModal"], [87, 30, 77, 30], [87, 31, 77, 31, "curr"], [87, 35, 77, 35], [87, 37, 77, 37, "interpolationIndex"], [87, 55, 77, 55], [87, 57, 77, 57, "isParentModal"], [87, 70, 77, 70], [87, 71, 77, 71], [88, 6, 78, 4, "acc"], [88, 9, 78, 7], [88, 10, 78, 8, "curr"], [88, 14, 78, 12], [88, 15, 78, 13, "route"], [88, 20, 78, 18], [88, 21, 78, 19, "key"], [88, 24, 78, 22], [88, 25, 78, 23], [88, 28, 78, 26], [88, 35, 78, 33, "height"], [88, 41, 78, 39], [88, 46, 78, 44], [88, 54, 78, 52], [88, 57, 78, 55, "height"], [88, 63, 78, 61], [88, 66, 78, 64], [88, 70, 78, 64, "getDefaultHeaderHeight"], [88, 102, 78, 86], [88, 104, 78, 87, "layout"], [88, 110, 78, 93], [88, 112, 78, 95, "isModal"], [88, 119, 78, 102], [88, 121, 78, 104, "headerStatusBarHeight"], [88, 142, 78, 125], [88, 143, 78, 126], [89, 6, 79, 4], [89, 13, 79, 11, "acc"], [89, 16, 79, 14], [90, 4, 80, 2], [90, 5, 80, 3], [90, 7, 80, 5], [90, 8, 80, 6], [90, 9, 80, 7], [90, 10, 80, 8], [91, 2, 81, 0], [91, 3, 81, 1], [92, 2, 82, 0], [92, 6, 82, 6, "getDistanceFromOptions"], [92, 28, 82, 28], [92, 31, 82, 31, "getDistanceFromOptions"], [92, 32, 82, 32, "layout"], [92, 38, 82, 38], [92, 40, 82, 40, "descriptor"], [92, 50, 82, 50], [92, 52, 82, 52, "isRTL"], [92, 57, 82, 57], [92, 62, 82, 62], [93, 4, 83, 2], [93, 8, 83, 6, "descriptor"], [93, 18, 83, 16], [93, 20, 83, 18, "options"], [93, 27, 83, 25], [93, 28, 83, 26, "gestureDirection"], [93, 44, 83, 42], [93, 46, 83, 44], [94, 6, 84, 4], [94, 13, 84, 11], [94, 17, 84, 11, "getDistanceForDirection"], [94, 65, 84, 34], [94, 67, 84, 35, "layout"], [94, 73, 84, 41], [94, 75, 84, 43, "descriptor"], [94, 85, 84, 53], [94, 87, 84, 55, "options"], [94, 94, 84, 62], [94, 95, 84, 63, "gestureDirection"], [94, 111, 84, 79], [94, 113, 84, 81, "isRTL"], [94, 118, 84, 86], [94, 119, 84, 87], [95, 4, 85, 2], [96, 4, 86, 2], [96, 8, 86, 8, "defaultGestureDirection"], [96, 31, 86, 31], [96, 34, 86, 34, "descriptor"], [96, 44, 86, 44], [96, 46, 86, 46, "options"], [96, 53, 86, 53], [96, 54, 86, 54, "presentation"], [96, 66, 86, 66], [96, 71, 86, 71], [96, 78, 86, 78], [96, 81, 86, 81, "ModalTransition"], [96, 115, 86, 96], [96, 116, 86, 97, "gestureDirection"], [96, 132, 86, 113], [96, 135, 86, 116, "DefaultTransition"], [96, 171, 86, 133], [96, 172, 86, 134, "gestureDirection"], [96, 188, 86, 150], [97, 4, 87, 2], [97, 8, 87, 8, "gestureDirection"], [97, 24, 87, 24], [97, 27, 87, 27, "descriptor"], [97, 37, 87, 37], [97, 39, 87, 39, "options"], [97, 46, 87, 46], [97, 47, 87, 47, "animation"], [97, 56, 87, 56], [97, 59, 87, 59, "NAMED_TRANSITIONS_PRESETS"], [97, 84, 87, 84], [97, 85, 87, 85, "descriptor"], [97, 95, 87, 95], [97, 97, 87, 97, "options"], [97, 104, 87, 104], [97, 105, 87, 105, "animation"], [97, 114, 87, 114], [97, 115, 87, 115], [97, 117, 87, 117, "gestureDirection"], [97, 133, 87, 133], [97, 136, 87, 136, "defaultGestureDirection"], [97, 159, 87, 159], [98, 4, 88, 2], [98, 11, 88, 9], [98, 15, 88, 9, "getDistanceForDirection"], [98, 63, 88, 32], [98, 65, 88, 33, "layout"], [98, 71, 88, 39], [98, 73, 88, 41, "gestureDirection"], [98, 89, 88, 57], [98, 91, 88, 59, "isRTL"], [98, 96, 88, 64], [98, 97, 88, 65], [99, 2, 89, 0], [99, 3, 89, 1], [100, 2, 90, 0], [100, 6, 90, 6, "getProgressFromGesture"], [100, 28, 90, 28], [100, 31, 90, 31, "getProgressFromGesture"], [100, 32, 90, 32, "gesture"], [100, 39, 90, 39], [100, 41, 90, 41, "layout"], [100, 47, 90, 47], [100, 49, 90, 49, "descriptor"], [100, 59, 90, 59], [100, 61, 90, 61, "isRTL"], [100, 66, 90, 66], [100, 71, 90, 71], [101, 4, 91, 2], [101, 8, 91, 8, "distance"], [101, 16, 91, 16], [101, 19, 91, 19, "getDistanceFromOptions"], [101, 41, 91, 41], [101, 42, 91, 42], [102, 6, 92, 4], [103, 6, 93, 4], [104, 6, 94, 4, "width"], [104, 11, 94, 9], [104, 13, 94, 11, "Math"], [104, 17, 94, 15], [104, 18, 94, 16, "max"], [104, 21, 94, 19], [104, 22, 94, 20], [104, 23, 94, 21], [104, 25, 94, 23, "layout"], [104, 31, 94, 29], [104, 32, 94, 30, "width"], [104, 37, 94, 35], [104, 38, 94, 36], [105, 6, 95, 4, "height"], [105, 12, 95, 10], [105, 14, 95, 12, "Math"], [105, 18, 95, 16], [105, 19, 95, 17, "max"], [105, 22, 95, 20], [105, 23, 95, 21], [105, 24, 95, 22], [105, 26, 95, 24, "layout"], [105, 32, 95, 30], [105, 33, 95, 31, "height"], [105, 39, 95, 37], [106, 4, 96, 2], [106, 5, 96, 3], [106, 7, 96, 5, "descriptor"], [106, 17, 96, 15], [106, 19, 96, 17, "isRTL"], [106, 24, 96, 22], [106, 25, 96, 23], [107, 4, 97, 2], [107, 8, 97, 6, "distance"], [107, 16, 97, 14], [107, 19, 97, 17], [107, 20, 97, 18], [107, 22, 97, 20], [108, 6, 98, 4], [108, 13, 98, 11, "gesture"], [108, 20, 98, 18], [108, 21, 98, 19, "interpolate"], [108, 32, 98, 30], [108, 33, 98, 31], [109, 8, 99, 6, "inputRange"], [109, 18, 99, 16], [109, 20, 99, 18], [109, 21, 99, 19], [109, 22, 99, 20], [109, 24, 99, 22, "distance"], [109, 32, 99, 30], [109, 33, 99, 31], [110, 8, 100, 6, "outputRange"], [110, 19, 100, 17], [110, 21, 100, 19], [110, 22, 100, 20], [110, 23, 100, 21], [110, 25, 100, 23], [110, 26, 100, 24], [111, 6, 101, 4], [111, 7, 101, 5], [111, 8, 101, 6], [112, 4, 102, 2], [113, 4, 103, 2], [113, 11, 103, 9, "gesture"], [113, 18, 103, 16], [113, 19, 103, 17, "interpolate"], [113, 30, 103, 28], [113, 31, 103, 29], [114, 6, 104, 4, "inputRange"], [114, 16, 104, 14], [114, 18, 104, 16], [114, 19, 104, 17, "distance"], [114, 27, 104, 25], [114, 29, 104, 27], [114, 30, 104, 28], [114, 31, 104, 29], [115, 6, 105, 4, "outputRange"], [115, 17, 105, 15], [115, 19, 105, 17], [115, 20, 105, 18], [115, 21, 105, 19], [115, 23, 105, 21], [115, 24, 105, 22], [116, 4, 106, 2], [116, 5, 106, 3], [116, 6, 106, 4], [117, 2, 107, 0], [117, 3, 107, 1], [118, 2, 107, 2], [118, 6, 108, 13, "CardStack"], [118, 15, 108, 22], [118, 18, 108, 22, "exports"], [118, 25, 108, 22], [118, 26, 108, 22, "CardStack"], [118, 35, 108, 22], [118, 61, 108, 22, "_React$Component"], [118, 77, 108, 22], [119, 4, 204, 2], [119, 13, 204, 2, "CardStack"], [119, 23, 204, 14, "props"], [119, 29, 204, 19], [119, 31, 204, 21], [120, 6, 204, 21], [120, 10, 204, 21, "_this"], [120, 15, 204, 21], [121, 6, 204, 21], [121, 10, 204, 21, "_classCallCheck2"], [121, 26, 204, 21], [121, 27, 204, 21, "default"], [121, 34, 204, 21], [121, 42, 204, 21, "CardStack"], [121, 51, 204, 21], [122, 6, 205, 4, "_this"], [122, 11, 205, 4], [122, 14, 205, 4, "_callSuper"], [122, 24, 205, 4], [122, 31, 205, 4, "CardStack"], [122, 40, 205, 4], [122, 43, 205, 10, "props"], [122, 49, 205, 15], [123, 6, 205, 17, "_this"], [123, 11, 205, 17], [123, 12, 220, 2, "handleLayout"], [123, 24, 220, 14], [123, 27, 220, 17, "e"], [123, 28, 220, 18], [123, 32, 220, 22], [124, 8, 221, 4], [124, 12, 221, 4, "_e$nativeEvent$layout"], [124, 33, 221, 4], [124, 36, 224, 8, "e"], [124, 37, 224, 9], [124, 38, 224, 10, "nativeEvent"], [124, 49, 224, 21], [124, 50, 224, 22, "layout"], [124, 56, 224, 28], [125, 10, 222, 6, "height"], [125, 16, 222, 12], [125, 19, 222, 12, "_e$nativeEvent$layout"], [125, 40, 222, 12], [125, 41, 222, 6, "height"], [125, 47, 222, 12], [126, 10, 223, 6, "width"], [126, 15, 223, 11], [126, 18, 223, 11, "_e$nativeEvent$layout"], [126, 39, 223, 11], [126, 40, 223, 6, "width"], [126, 45, 223, 11], [127, 8, 225, 4], [127, 12, 225, 10, "layout"], [127, 18, 225, 16], [127, 21, 225, 19], [128, 10, 226, 6, "width"], [128, 15, 226, 11], [129, 10, 227, 6, "height"], [130, 8, 228, 4], [130, 9, 228, 5], [131, 8, 229, 4, "_this"], [131, 13, 229, 4], [131, 14, 229, 9, "setState"], [131, 22, 229, 17], [131, 23, 229, 18], [131, 24, 229, 19, "state"], [131, 29, 229, 24], [131, 31, 229, 26, "props"], [131, 36, 229, 31], [131, 41, 229, 36], [132, 10, 230, 6], [132, 14, 230, 10, "height"], [132, 20, 230, 16], [132, 25, 230, 21, "state"], [132, 30, 230, 26], [132, 31, 230, 27, "layout"], [132, 37, 230, 33], [132, 38, 230, 34, "height"], [132, 44, 230, 40], [132, 48, 230, 44, "width"], [132, 53, 230, 49], [132, 58, 230, 54, "state"], [132, 63, 230, 59], [132, 64, 230, 60, "layout"], [132, 70, 230, 66], [132, 71, 230, 67, "width"], [132, 76, 230, 72], [132, 78, 230, 74], [133, 12, 231, 8], [133, 19, 231, 15], [133, 23, 231, 19], [134, 10, 232, 6], [135, 10, 233, 6], [135, 17, 233, 13], [136, 12, 234, 8, "layout"], [136, 18, 234, 14], [137, 12, 235, 8, "headerHeights"], [137, 25, 235, 21], [137, 27, 235, 23, "getHeaderHeights"], [137, 43, 235, 39], [137, 44, 235, 40, "state"], [137, 49, 235, 45], [137, 50, 235, 46, "scenes"], [137, 56, 235, 52], [137, 58, 235, 54, "props"], [137, 63, 235, 59], [137, 64, 235, 60, "insets"], [137, 70, 235, 66], [137, 72, 235, 68, "props"], [137, 77, 235, 73], [137, 78, 235, 74, "isParentHeaderShown"], [137, 97, 235, 93], [137, 99, 235, 95, "props"], [137, 104, 235, 100], [137, 105, 235, 101, "isParentModal"], [137, 118, 235, 114], [137, 120, 235, 116, "layout"], [137, 126, 235, 122], [137, 128, 235, 124, "state"], [137, 133, 235, 129], [137, 134, 235, 130, "headerHeights"], [137, 147, 235, 143], [138, 10, 236, 6], [138, 11, 236, 7], [139, 8, 237, 4], [139, 9, 237, 5], [139, 10, 237, 6], [140, 6, 238, 2], [140, 7, 238, 3], [141, 6, 238, 3, "_this"], [141, 11, 238, 3], [141, 12, 239, 2, "handleHeaderLayout"], [141, 30, 239, 20], [141, 33, 239, 23, "_ref"], [141, 37, 239, 23], [141, 41, 242, 8], [142, 8, 242, 8], [142, 12, 240, 4, "route"], [142, 17, 240, 9], [142, 20, 240, 9, "_ref"], [142, 24, 240, 9], [142, 25, 240, 4, "route"], [142, 30, 240, 9], [143, 10, 241, 4, "height"], [143, 16, 241, 10], [143, 19, 241, 10, "_ref"], [143, 23, 241, 10], [143, 24, 241, 4, "height"], [143, 30, 241, 10], [144, 8, 243, 4, "_this"], [144, 13, 243, 4], [144, 14, 243, 9, "setState"], [144, 22, 243, 17], [144, 23, 243, 18, "_ref2"], [144, 28, 243, 18], [144, 32, 245, 10], [145, 10, 245, 10], [145, 14, 244, 6, "headerHeights"], [145, 27, 244, 19], [145, 30, 244, 19, "_ref2"], [145, 35, 244, 19], [145, 36, 244, 6, "headerHeights"], [145, 49, 244, 19], [146, 10, 246, 6], [146, 14, 246, 12, "previousHeight"], [146, 28, 246, 26], [146, 31, 246, 29, "headerHeights"], [146, 44, 246, 42], [146, 45, 246, 43, "route"], [146, 50, 246, 48], [146, 51, 246, 49, "key"], [146, 54, 246, 52], [146, 55, 246, 53], [147, 10, 247, 6], [147, 14, 247, 10, "previousHeight"], [147, 28, 247, 24], [147, 33, 247, 29, "height"], [147, 39, 247, 35], [147, 41, 247, 37], [148, 12, 248, 8], [148, 19, 248, 15], [148, 23, 248, 19], [149, 10, 249, 6], [150, 10, 250, 6], [150, 17, 250, 13], [151, 12, 251, 8, "headerHeights"], [151, 25, 251, 21], [151, 27, 251, 23], [152, 14, 252, 10], [152, 17, 252, 13, "headerHeights"], [152, 30, 252, 26], [153, 14, 253, 10], [153, 15, 253, 11, "route"], [153, 20, 253, 16], [153, 21, 253, 17, "key"], [153, 24, 253, 20], [153, 27, 253, 23, "height"], [154, 12, 254, 8], [155, 10, 255, 6], [155, 11, 255, 7], [156, 8, 256, 4], [156, 9, 256, 5], [156, 10, 256, 6], [157, 6, 257, 2], [157, 7, 257, 3], [158, 6, 257, 3, "_this"], [158, 11, 257, 3], [158, 12, 258, 2, "getFocusedRoute"], [158, 27, 258, 17], [158, 30, 258, 20], [158, 36, 258, 26], [159, 8, 259, 4], [159, 12, 260, 6, "state"], [159, 17, 260, 11], [159, 20, 261, 8, "_this"], [159, 25, 261, 8], [159, 26, 261, 13, "props"], [159, 31, 261, 18], [159, 32, 260, 6, "state"], [159, 37, 260, 11], [160, 8, 262, 4], [160, 15, 262, 11, "state"], [160, 20, 262, 16], [160, 21, 262, 17, "routes"], [160, 27, 262, 23], [160, 28, 262, 24, "state"], [160, 33, 262, 29], [160, 34, 262, 30, "index"], [160, 39, 262, 35], [160, 40, 262, 36], [161, 6, 263, 2], [161, 7, 263, 3], [162, 6, 263, 3, "_this"], [162, 11, 263, 3], [162, 12, 264, 2, "getPreviousScene"], [162, 28, 264, 18], [162, 31, 264, 21, "_ref3"], [162, 36, 264, 21], [162, 40, 266, 8], [163, 8, 266, 8], [163, 12, 265, 4, "route"], [163, 17, 265, 9], [163, 20, 265, 9, "_ref3"], [163, 25, 265, 9], [163, 26, 265, 4, "route"], [163, 31, 265, 9], [164, 8, 267, 4], [164, 12, 268, 6, "getPreviousRoute"], [164, 28, 268, 22], [164, 31, 269, 8, "_this"], [164, 36, 269, 8], [164, 37, 269, 13, "props"], [164, 42, 269, 18], [164, 43, 268, 6, "getPreviousRoute"], [164, 59, 268, 22], [165, 8, 270, 4], [165, 12, 271, 6, "scenes"], [165, 18, 271, 12], [165, 21, 272, 8, "_this"], [165, 26, 272, 8], [165, 27, 272, 13, "state"], [165, 32, 272, 18], [165, 33, 271, 6, "scenes"], [165, 39, 271, 12], [166, 8, 273, 4], [166, 12, 273, 10, "previousRoute"], [166, 25, 273, 23], [166, 28, 273, 26, "getPreviousRoute"], [166, 44, 273, 42], [166, 45, 273, 43], [167, 10, 274, 6, "route"], [168, 8, 275, 4], [168, 9, 275, 5], [168, 10, 275, 6], [169, 8, 276, 4], [169, 12, 276, 8, "previousRoute"], [169, 25, 276, 21], [169, 27, 276, 23], [170, 10, 277, 6], [170, 14, 277, 12, "previousScene"], [170, 27, 277, 25], [170, 30, 277, 28, "scenes"], [170, 36, 277, 34], [170, 37, 277, 35, "find"], [170, 41, 277, 39], [170, 42, 277, 40, "scene"], [170, 47, 277, 45], [170, 51, 277, 49, "scene"], [170, 56, 277, 54], [170, 57, 277, 55, "descriptor"], [170, 67, 277, 65], [170, 68, 277, 66, "route"], [170, 73, 277, 71], [170, 74, 277, 72, "key"], [170, 77, 277, 75], [170, 82, 277, 80, "previousRoute"], [170, 95, 277, 93], [170, 96, 277, 94, "key"], [170, 99, 277, 97], [170, 100, 277, 98], [171, 10, 278, 6], [171, 17, 278, 13, "previousScene"], [171, 30, 278, 26], [172, 8, 279, 4], [173, 8, 280, 4], [173, 15, 280, 11, "undefined"], [173, 24, 280, 20], [174, 6, 281, 2], [174, 7, 281, 3], [175, 6, 206, 4, "_this"], [175, 11, 206, 4], [175, 12, 206, 9, "state"], [175, 17, 206, 14], [175, 20, 206, 17], [176, 8, 207, 6, "routes"], [176, 14, 207, 12], [176, 16, 207, 14], [176, 18, 207, 16], [177, 8, 208, 6, "scenes"], [177, 14, 208, 12], [177, 16, 208, 14], [177, 18, 208, 16], [178, 8, 209, 6, "gestures"], [178, 16, 209, 14], [178, 18, 209, 16], [178, 19, 209, 17], [178, 20, 209, 18], [179, 8, 210, 6, "layout"], [179, 14, 210, 12], [179, 16, 210, 14, "SafeAreaProviderCompat"], [179, 48, 210, 36], [179, 49, 210, 37, "initialMetrics"], [179, 63, 210, 51], [179, 64, 210, 52, "frame"], [179, 69, 210, 57], [180, 8, 211, 6, "descriptors"], [180, 19, 211, 17], [180, 21, 211, 19, "_this"], [180, 26, 211, 19], [180, 27, 211, 24, "props"], [180, 32, 211, 29], [180, 33, 211, 30, "descriptors"], [180, 44, 211, 41], [181, 8, 212, 6], [182, 8, 213, 6], [183, 8, 214, 6], [184, 8, 215, 6], [185, 8, 216, 6], [186, 8, 217, 6, "headerHeights"], [186, 21, 217, 19], [186, 23, 217, 21], [186, 24, 217, 22], [187, 6, 218, 4], [187, 7, 218, 5], [188, 6, 218, 6], [188, 13, 218, 6, "_this"], [188, 18, 218, 6], [189, 4, 219, 2], [190, 4, 219, 3], [190, 8, 219, 3, "_inherits2"], [190, 18, 219, 3], [190, 19, 219, 3, "default"], [190, 26, 219, 3], [190, 28, 219, 3, "CardStack"], [190, 37, 219, 3], [190, 39, 219, 3, "_React$Component"], [190, 55, 219, 3], [191, 4, 219, 3], [191, 15, 219, 3, "_createClass2"], [191, 28, 219, 3], [191, 29, 219, 3, "default"], [191, 36, 219, 3], [191, 38, 219, 3, "CardStack"], [191, 47, 219, 3], [192, 6, 219, 3, "key"], [192, 9, 219, 3], [193, 6, 219, 3, "value"], [193, 11, 219, 3], [193, 13, 282, 2], [193, 22, 282, 2, "render"], [193, 28, 282, 8, "render"], [193, 29, 282, 8], [193, 31, 282, 11], [194, 8, 283, 4], [194, 12, 283, 4, "_this$props"], [194, 23, 283, 4], [194, 26, 300, 8], [194, 30, 300, 12], [194, 31, 300, 13, "props"], [194, 36, 300, 18], [195, 10, 284, 6, "insets"], [195, 16, 284, 12], [195, 19, 284, 12, "_this$props"], [195, 30, 284, 12], [195, 31, 284, 6, "insets"], [195, 37, 284, 12], [196, 10, 285, 6, "state"], [196, 15, 285, 11], [196, 18, 285, 11, "_this$props"], [196, 29, 285, 11], [196, 30, 285, 6, "state"], [196, 35, 285, 11], [197, 10, 286, 6, "routes"], [197, 16, 286, 12], [197, 19, 286, 12, "_this$props"], [197, 30, 286, 12], [197, 31, 286, 6, "routes"], [197, 37, 286, 12], [198, 10, 287, 6, "openingRouteKeys"], [198, 26, 287, 22], [198, 29, 287, 22, "_this$props"], [198, 40, 287, 22], [198, 41, 287, 6, "openingRouteKeys"], [198, 57, 287, 22], [199, 10, 288, 6, "closingRouteKeys"], [199, 26, 288, 22], [199, 29, 288, 22, "_this$props"], [199, 40, 288, 22], [199, 41, 288, 6, "closingRouteKeys"], [199, 57, 288, 22], [200, 10, 289, 6, "onOpenRoute"], [200, 21, 289, 17], [200, 24, 289, 17, "_this$props"], [200, 35, 289, 17], [200, 36, 289, 6, "onOpenRoute"], [200, 47, 289, 17], [201, 10, 290, 6, "onCloseRoute"], [201, 22, 290, 18], [201, 25, 290, 18, "_this$props"], [201, 36, 290, 18], [201, 37, 290, 6, "onCloseRoute"], [201, 49, 290, 18], [202, 10, 291, 6, "renderHeader"], [202, 22, 291, 18], [202, 25, 291, 18, "_this$props"], [202, 36, 291, 18], [202, 37, 291, 6, "renderHeader"], [202, 49, 291, 18], [203, 10, 292, 6, "isParentHeaderShown"], [203, 29, 292, 25], [203, 32, 292, 25, "_this$props"], [203, 43, 292, 25], [203, 44, 292, 6, "isParentHeaderShown"], [203, 63, 292, 25], [204, 10, 293, 6, "isParentModal"], [204, 23, 293, 19], [204, 26, 293, 19, "_this$props"], [204, 37, 293, 19], [204, 38, 293, 6, "isParentModal"], [204, 51, 293, 19], [205, 10, 294, 6, "onTransitionStart"], [205, 27, 294, 23], [205, 30, 294, 23, "_this$props"], [205, 41, 294, 23], [205, 42, 294, 6, "onTransitionStart"], [205, 59, 294, 23], [206, 10, 295, 6, "onTransitionEnd"], [206, 25, 295, 21], [206, 28, 295, 21, "_this$props"], [206, 39, 295, 21], [206, 40, 295, 6, "onTransitionEnd"], [206, 55, 295, 21], [207, 10, 296, 6, "onGestureStart"], [207, 24, 296, 20], [207, 27, 296, 20, "_this$props"], [207, 38, 296, 20], [207, 39, 296, 6, "onGestureStart"], [207, 53, 296, 20], [208, 10, 297, 6, "onGestureEnd"], [208, 22, 297, 18], [208, 25, 297, 18, "_this$props"], [208, 36, 297, 18], [208, 37, 297, 6, "onGestureEnd"], [208, 49, 297, 18], [209, 10, 298, 6, "onGestureCancel"], [209, 25, 298, 21], [209, 28, 298, 21, "_this$props"], [209, 39, 298, 21], [209, 40, 298, 6, "onGestureCancel"], [209, 55, 298, 21], [210, 10, 298, 21, "_this$props$detachIna"], [210, 31, 298, 21], [210, 34, 298, 21, "_this$props"], [210, 45, 298, 21], [210, 46, 299, 6, "detachInactiveScreens"], [210, 67, 299, 27], [211, 10, 299, 6, "detachInactiveScreens"], [211, 31, 299, 27], [211, 34, 299, 27, "_this$props$detachIna"], [211, 55, 299, 27], [211, 69, 299, 30, "Platform"], [211, 90, 299, 38], [211, 91, 299, 39, "OS"], [211, 93, 299, 41], [211, 98, 299, 46], [211, 103, 299, 51], [211, 107, 299, 55, "Platform"], [211, 128, 299, 63], [211, 129, 299, 64, "OS"], [211, 131, 299, 66], [211, 136, 299, 71], [211, 145, 299, 80], [211, 149, 299, 84, "Platform"], [211, 170, 299, 92], [211, 171, 299, 93, "OS"], [211, 173, 299, 95], [211, 178, 299, 100], [211, 183, 299, 105], [211, 186, 299, 105, "_this$props$detachIna"], [211, 207, 299, 105], [212, 8, 301, 4], [212, 12, 301, 4, "_this$state"], [212, 23, 301, 4], [212, 26, 306, 8], [212, 30, 306, 12], [212, 31, 306, 13, "state"], [212, 36, 306, 18], [213, 10, 302, 6, "scenes"], [213, 16, 302, 12], [213, 19, 302, 12, "_this$state"], [213, 30, 302, 12], [213, 31, 302, 6, "scenes"], [213, 37, 302, 12], [214, 10, 303, 6, "layout"], [214, 16, 303, 12], [214, 19, 303, 12, "_this$state"], [214, 30, 303, 12], [214, 31, 303, 6, "layout"], [214, 37, 303, 12], [215, 10, 304, 6, "gestures"], [215, 18, 304, 14], [215, 21, 304, 14, "_this$state"], [215, 32, 304, 14], [215, 33, 304, 6, "gestures"], [215, 41, 304, 14], [216, 10, 305, 6, "headerHeights"], [216, 23, 305, 19], [216, 26, 305, 19, "_this$state"], [216, 37, 305, 19], [216, 38, 305, 6, "headerHeights"], [216, 51, 305, 19], [217, 8, 307, 4], [217, 12, 307, 10, "focusedRoute"], [217, 24, 307, 22], [217, 27, 307, 25, "state"], [217, 32, 307, 30], [217, 33, 307, 31, "routes"], [217, 39, 307, 37], [217, 40, 307, 38, "state"], [217, 45, 307, 43], [217, 46, 307, 44, "index"], [217, 51, 307, 49], [217, 52, 307, 50], [218, 8, 308, 4], [218, 12, 308, 10, "focusedHeaderHeight"], [218, 31, 308, 29], [218, 34, 308, 32, "headerHeights"], [218, 47, 308, 45], [218, 48, 308, 46, "focusedRoute"], [218, 60, 308, 58], [218, 61, 308, 59, "key"], [218, 64, 308, 62], [218, 65, 308, 63], [219, 8, 309, 4], [219, 12, 309, 10, "isFloatHeaderAbsolute"], [219, 33, 309, 31], [219, 36, 309, 34], [219, 40, 309, 38], [219, 41, 309, 39, "state"], [219, 46, 309, 44], [219, 47, 309, 45, "scenes"], [219, 53, 309, 51], [219, 54, 309, 52, "slice"], [219, 59, 309, 57], [219, 60, 309, 58], [219, 61, 309, 59], [219, 62, 309, 60], [219, 63, 309, 61], [219, 64, 309, 62, "some"], [219, 68, 309, 66], [219, 69, 309, 67, "scene"], [219, 74, 309, 72], [219, 78, 309, 76], [220, 10, 310, 6], [220, 14, 310, 12, "options"], [220, 21, 310, 19], [220, 24, 310, 22, "scene"], [220, 29, 310, 27], [220, 30, 310, 28, "descriptor"], [220, 40, 310, 38], [220, 41, 310, 39, "options"], [220, 48, 310, 46], [220, 52, 310, 50], [220, 53, 310, 51], [220, 54, 310, 52], [221, 10, 311, 6], [221, 14, 312, 8, "headerMode"], [221, 24, 312, 18], [221, 27, 315, 10, "options"], [221, 34, 315, 17], [221, 35, 312, 8, "headerMode"], [221, 45, 312, 18], [222, 12, 313, 8, "headerTransparent"], [222, 29, 313, 25], [222, 32, 315, 10, "options"], [222, 39, 315, 17], [222, 40, 313, 8, "headerTransparent"], [222, 57, 313, 25], [223, 12, 313, 25, "_options$headerShown"], [223, 32, 313, 25], [223, 35, 315, 10, "options"], [223, 42, 315, 17], [223, 43, 314, 8, "headerShown"], [223, 54, 314, 19], [224, 12, 314, 8, "headerShown"], [224, 23, 314, 19], [224, 26, 314, 19, "_options$headerShown"], [224, 46, 314, 19], [224, 60, 314, 22], [224, 64, 314, 26], [224, 67, 314, 26, "_options$headerShown"], [224, 87, 314, 26], [225, 10, 316, 6], [225, 14, 316, 10, "headerTransparent"], [225, 31, 316, 27], [225, 35, 316, 31, "headerShown"], [225, 46, 316, 42], [225, 51, 316, 47], [225, 56, 316, 52], [225, 60, 316, 56, "headerMode"], [225, 70, 316, 66], [225, 75, 316, 71], [225, 83, 316, 79], [225, 85, 316, 81], [226, 12, 317, 8], [226, 19, 317, 15], [226, 23, 317, 19], [227, 10, 318, 6], [228, 10, 319, 6], [228, 17, 319, 13], [228, 22, 319, 18], [229, 8, 320, 4], [229, 9, 320, 5], [229, 10, 320, 6], [230, 8, 321, 4], [230, 12, 321, 8, "activeScreensLimit"], [230, 30, 321, 26], [230, 33, 321, 29], [230, 34, 321, 30], [231, 8, 322, 4], [231, 13, 322, 9], [231, 17, 322, 13, "i"], [231, 18, 322, 14], [231, 21, 322, 17, "scenes"], [231, 27, 322, 23], [231, 28, 322, 24, "length"], [231, 34, 322, 30], [231, 37, 322, 33], [231, 38, 322, 34], [231, 40, 322, 36, "i"], [231, 41, 322, 37], [231, 45, 322, 41], [231, 46, 322, 42], [231, 48, 322, 44, "i"], [231, 49, 322, 45], [231, 51, 322, 47], [231, 53, 322, 49], [232, 10, 323, 6], [232, 14, 324, 8, "options"], [232, 21, 324, 15], [232, 24, 325, 10, "scenes"], [232, 30, 325, 16], [232, 31, 325, 17, "i"], [232, 32, 325, 18], [232, 33, 325, 19], [232, 34, 325, 20, "descriptor"], [232, 44, 325, 30], [232, 45, 324, 8, "options"], [232, 52, 324, 15], [233, 10, 326, 6], [233, 14, 326, 6, "_options$detachPrevio"], [233, 35, 326, 6], [233, 38, 334, 10, "options"], [233, 45, 334, 17], [233, 46, 328, 8, "detachPreviousScreen"], [233, 66, 328, 28], [234, 12, 328, 8, "detachPreviousScreen"], [234, 32, 328, 28], [234, 35, 328, 28, "_options$detachPrevio"], [234, 56, 328, 28], [234, 70, 328, 31, "options"], [234, 77, 328, 38], [234, 78, 328, 39, "presentation"], [234, 90, 328, 51], [234, 95, 328, 56], [234, 113, 328, 74], [234, 116, 328, 77], [234, 121, 328, 82], [234, 124, 328, 85, "getIsModalPresentation"], [234, 146, 328, 107], [234, 147, 328, 108, "options"], [234, 154, 328, 115], [234, 155, 328, 116, "cardStyleInterpolator"], [234, 176, 328, 137], [234, 177, 328, 138], [234, 180, 328, 141, "i"], [234, 181, 328, 142], [234, 186, 328, 147], [234, 190, 328, 147, "findLastIndex"], [234, 218, 328, 160], [234, 220, 328, 161, "scenes"], [234, 226, 328, 167], [234, 228, 328, 169, "scene"], [234, 233, 328, 174], [234, 237, 328, 178], [235, 14, 329, 10], [235, 18, 330, 12, "cardStyleInterpolator"], [235, 39, 330, 33], [235, 42, 331, 14, "scene"], [235, 47, 331, 19], [235, 48, 331, 20, "descriptor"], [235, 58, 331, 30], [235, 59, 331, 31, "options"], [235, 66, 331, 38], [235, 67, 330, 12, "cardStyleInterpolator"], [235, 88, 330, 33], [236, 14, 332, 10], [236, 21, 332, 17, "cardStyleInterpolator"], [236, 42, 332, 38], [236, 47, 332, 43, "forModalPresentationIOS"], [236, 94, 332, 66], [236, 98, 332, 70, "cardStyleInterpolator"], [236, 119, 332, 91], [236, 121, 332, 93, "name"], [236, 125, 332, 97], [236, 130, 332, 102], [236, 155, 332, 127], [237, 12, 333, 8], [237, 13, 333, 9], [237, 14, 333, 10], [237, 17, 333, 13], [237, 21, 333, 17], [237, 24, 333, 17, "_options$detachPrevio"], [237, 45, 333, 17], [238, 10, 335, 6], [238, 14, 335, 10, "detachPreviousScreen"], [238, 34, 335, 30], [238, 39, 335, 35], [238, 44, 335, 40], [238, 46, 335, 42], [239, 12, 336, 8, "activeScreensLimit"], [239, 30, 336, 26], [239, 32, 336, 28], [240, 10, 337, 6], [240, 11, 337, 7], [240, 17, 337, 13], [241, 12, 338, 8], [242, 12, 339, 8], [243, 12, 340, 8], [244, 12, 341, 8], [244, 16, 341, 12, "i"], [244, 17, 341, 13], [244, 21, 341, 17, "scenes"], [244, 27, 341, 23], [244, 28, 341, 24, "length"], [244, 34, 341, 30], [244, 37, 341, 33], [244, 38, 341, 34], [244, 40, 341, 36], [245, 14, 342, 10], [246, 12, 343, 8], [247, 10, 344, 6], [248, 8, 345, 4], [249, 8, 346, 4], [249, 12, 346, 10, "floatingHeader"], [249, 26, 346, 24], [249, 29, 346, 27], [249, 42, 346, 40], [249, 46, 346, 40, "_jsx"], [249, 61, 346, 44], [249, 63, 346, 45, "React"], [249, 68, 346, 50], [249, 69, 346, 51, "Fragment"], [249, 77, 346, 59], [249, 79, 346, 61], [250, 10, 347, 6, "children"], [250, 18, 347, 14], [250, 20, 347, 16, "renderHeader"], [250, 32, 347, 28], [250, 33, 347, 29], [251, 12, 348, 8, "mode"], [251, 16, 348, 12], [251, 18, 348, 14], [251, 25, 348, 21], [252, 12, 349, 8, "layout"], [252, 18, 349, 14], [253, 12, 350, 8, "scenes"], [253, 18, 350, 14], [254, 12, 351, 8, "getPreviousScene"], [254, 28, 351, 24], [254, 30, 351, 26], [254, 34, 351, 30], [254, 35, 351, 31, "getPreviousScene"], [254, 51, 351, 47], [255, 12, 352, 8, "getFocusedRoute"], [255, 27, 352, 23], [255, 29, 352, 25], [255, 33, 352, 29], [255, 34, 352, 30, "getFocusedRoute"], [255, 49, 352, 45], [256, 12, 353, 8, "onContentHeightChange"], [256, 33, 353, 29], [256, 35, 353, 31], [256, 39, 353, 35], [256, 40, 353, 36, "handleHeaderLayout"], [256, 58, 353, 54], [257, 12, 354, 8, "style"], [257, 17, 354, 13], [257, 19, 354, 15], [257, 20, 354, 16, "styles"], [257, 26, 354, 22], [257, 27, 354, 23, "floating"], [257, 35, 354, 31], [257, 37, 354, 33, "isFloatHeaderAbsolute"], [257, 58, 354, 54], [257, 62, 354, 58], [258, 12, 355, 8], [259, 12, 356, 8], [260, 14, 357, 10, "height"], [260, 20, 357, 16], [260, 22, 357, 18, "focusedHeaderHeight"], [261, 12, 358, 8], [261, 13, 358, 9], [261, 15, 358, 11, "styles"], [261, 21, 358, 17], [261, 22, 358, 18, "absolute"], [261, 30, 358, 26], [261, 31, 358, 27], [262, 10, 359, 6], [262, 11, 359, 7], [263, 8, 360, 4], [263, 9, 360, 5], [263, 11, 360, 7], [263, 19, 360, 15], [263, 20, 360, 16], [264, 8, 361, 4], [264, 15, 361, 11], [264, 28, 361, 24], [264, 32, 361, 24, "_jsxs"], [264, 48, 361, 29], [264, 50, 361, 30, "View"], [264, 67, 361, 34], [264, 69, 361, 36], [265, 10, 362, 6, "style"], [265, 15, 362, 11], [265, 17, 362, 13, "styles"], [265, 23, 362, 19], [265, 24, 362, 20, "container"], [265, 33, 362, 29], [266, 10, 363, 6, "children"], [266, 18, 363, 14], [266, 20, 363, 16], [266, 21, 363, 17, "isFloatHeaderAbsolute"], [266, 42, 363, 38], [266, 45, 363, 41], [266, 49, 363, 45], [266, 52, 363, 48, "floatingHeader"], [266, 66, 363, 62], [266, 68, 363, 64], [266, 81, 363, 77], [266, 85, 363, 77, "_jsx"], [266, 100, 363, 81], [266, 102, 363, 82, "MaybeScreenContainer"], [266, 131, 363, 102], [266, 133, 363, 104], [267, 12, 364, 8, "enabled"], [267, 19, 364, 15], [267, 21, 364, 17, "detachInactiveScreens"], [267, 42, 364, 38], [268, 12, 365, 8, "style"], [268, 17, 365, 13], [268, 19, 365, 15, "styles"], [268, 25, 365, 21], [268, 26, 365, 22, "container"], [268, 35, 365, 31], [269, 12, 366, 8, "onLayout"], [269, 20, 366, 16], [269, 22, 366, 18], [269, 26, 366, 22], [269, 27, 366, 23, "handleLayout"], [269, 39, 366, 35], [270, 12, 367, 8, "children"], [270, 20, 367, 16], [270, 22, 367, 18], [270, 23, 367, 19], [270, 26, 367, 22, "routes"], [270, 32, 367, 28], [270, 34, 367, 30], [270, 37, 367, 33, "state"], [270, 42, 367, 38], [270, 43, 367, 39, "preloadedRoutes"], [270, 58, 367, 54], [270, 59, 367, 55], [270, 60, 367, 56, "map"], [270, 63, 367, 59], [270, 64, 367, 60], [270, 65, 367, 61, "route"], [270, 70, 367, 66], [270, 72, 367, 68, "index"], [270, 77, 367, 73], [270, 82, 367, 78], [271, 14, 368, 10], [271, 18, 368, 16, "focused"], [271, 25, 368, 23], [271, 28, 368, 26, "focusedRoute"], [271, 40, 368, 38], [271, 41, 368, 39, "key"], [271, 44, 368, 42], [271, 49, 368, 47, "route"], [271, 54, 368, 52], [271, 55, 368, 53, "key"], [271, 58, 368, 56], [272, 14, 369, 10], [272, 18, 369, 16, "gesture"], [272, 25, 369, 23], [272, 28, 369, 26, "gestures"], [272, 36, 369, 34], [272, 37, 369, 35, "route"], [272, 42, 369, 40], [272, 43, 369, 41, "key"], [272, 46, 369, 44], [272, 47, 369, 45], [273, 14, 370, 10], [273, 18, 370, 16, "scene"], [273, 23, 370, 21], [273, 26, 370, 24, "scenes"], [273, 32, 370, 30], [273, 33, 370, 31, "index"], [273, 38, 370, 36], [273, 39, 370, 37], [274, 14, 371, 10], [275, 14, 372, 10], [276, 14, 373, 10], [277, 14, 374, 10], [278, 14, 375, 10], [278, 18, 375, 16, "isPreloaded"], [278, 29, 375, 27], [278, 32, 375, 30, "state"], [278, 37, 375, 35], [278, 38, 375, 36, "preloadedRoutes"], [278, 53, 375, 51], [278, 54, 375, 52, "includes"], [278, 62, 375, 60], [278, 63, 375, 61, "route"], [278, 68, 375, 66], [278, 69, 375, 67], [278, 73, 375, 71], [278, 74, 375, 72, "routes"], [278, 80, 375, 78], [278, 81, 375, 79, "includes"], [278, 89, 375, 87], [278, 90, 375, 88, "route"], [278, 95, 375, 93], [278, 96, 375, 94], [279, 14, 376, 10], [279, 18, 376, 14, "state"], [279, 23, 376, 19], [279, 24, 376, 20, "preloadedRoutes"], [279, 39, 376, 35], [279, 40, 376, 36, "includes"], [279, 48, 376, 44], [279, 49, 376, 45, "route"], [279, 54, 376, 50], [279, 55, 376, 51], [279, 59, 376, 55, "routes"], [279, 65, 376, 61], [279, 66, 376, 62, "includes"], [279, 74, 376, 70], [279, 75, 376, 71, "route"], [279, 80, 376, 76], [279, 81, 376, 77], [279, 85, 376, 81, "index"], [279, 90, 376, 86], [279, 94, 376, 90, "routes"], [279, 100, 376, 96], [279, 101, 376, 97, "length"], [279, 107, 376, 103], [279, 109, 376, 105], [280, 16, 377, 12], [280, 23, 377, 19], [280, 27, 377, 23], [281, 14, 378, 10], [283, 14, 380, 10], [284, 14, 381, 10], [285, 14, 382, 10], [286, 14, 383, 10], [287, 14, 384, 10], [287, 18, 384, 14, "isScreenActive"], [287, 32, 384, 28], [287, 35, 384, 31], [287, 36, 384, 32], [288, 14, 385, 10], [288, 18, 385, 14, "index"], [288, 23, 385, 19], [288, 26, 385, 22, "routes"], [288, 32, 385, 28], [288, 33, 385, 29, "length"], [288, 39, 385, 35], [288, 42, 385, 38, "activeScreensLimit"], [288, 60, 385, 56], [288, 63, 385, 59], [288, 64, 385, 60], [288, 68, 385, 64, "isPreloaded"], [288, 79, 385, 75], [288, 81, 385, 77], [289, 16, 386, 12], [290, 16, 387, 12, "isScreenActive"], [290, 30, 387, 26], [290, 33, 387, 29, "STATE_INACTIVE"], [290, 47, 387, 43], [291, 14, 388, 10], [291, 15, 388, 11], [291, 21, 388, 17], [292, 16, 389, 12], [292, 20, 389, 18, "sceneForActivity"], [292, 36, 389, 34], [292, 39, 389, 37, "scenes"], [292, 45, 389, 43], [292, 46, 389, 44, "routes"], [292, 52, 389, 50], [292, 53, 389, 51, "length"], [292, 59, 389, 57], [292, 62, 389, 60], [292, 63, 389, 61], [292, 64, 389, 62], [293, 16, 390, 12], [293, 20, 390, 18, "outputValue"], [293, 31, 390, 29], [293, 34, 390, 32, "index"], [293, 39, 390, 37], [293, 44, 390, 42, "routes"], [293, 50, 390, 48], [293, 51, 390, 49, "length"], [293, 57, 390, 55], [293, 60, 390, 58], [293, 61, 390, 59], [293, 64, 390, 62, "STATE_ON_TOP"], [293, 76, 390, 74], [293, 77, 390, 75], [294, 16, 390, 75], [294, 18, 391, 14, "index"], [294, 23, 391, 19], [294, 27, 391, 23, "routes"], [294, 33, 391, 29], [294, 34, 391, 30, "length"], [294, 40, 391, 36], [294, 43, 391, 39, "activeScreensLimit"], [294, 61, 391, 57], [294, 64, 391, 60, "STATE_TRANSITIONING_OR_BELOW_TOP"], [294, 96, 391, 92], [294, 97, 391, 93], [295, 16, 391, 93], [295, 18, 392, 14, "STATE_INACTIVE"], [295, 32, 392, 28], [295, 33, 392, 29], [295, 34, 392, 30], [296, 16, 393, 12, "isScreenActive"], [296, 30, 393, 26], [296, 33, 393, 29, "sceneForActivity"], [296, 49, 393, 45], [296, 52, 393, 48, "sceneForActivity"], [296, 68, 393, 64], [296, 69, 393, 65, "progress"], [296, 77, 393, 73], [296, 78, 393, 74, "current"], [296, 85, 393, 81], [296, 86, 393, 82, "interpolate"], [296, 97, 393, 93], [296, 98, 393, 94], [297, 18, 394, 14, "inputRange"], [297, 28, 394, 24], [297, 30, 394, 26], [297, 31, 394, 27], [297, 32, 394, 28], [297, 34, 394, 30], [297, 35, 394, 31], [297, 38, 394, 34, "EPSILON"], [297, 45, 394, 41], [297, 47, 394, 43], [297, 48, 394, 44], [297, 49, 394, 45], [298, 18, 395, 14, "outputRange"], [298, 29, 395, 25], [298, 31, 395, 27], [298, 32, 395, 28], [298, 33, 395, 29], [298, 35, 395, 31], [298, 36, 395, 32], [298, 38, 395, 34, "outputValue"], [298, 49, 395, 45], [298, 50, 395, 46], [299, 18, 396, 14, "extrapolate"], [299, 29, 396, 25], [299, 31, 396, 27], [300, 16, 397, 12], [300, 17, 397, 13], [300, 18, 397, 14], [300, 21, 397, 17, "STATE_TRANSITIONING_OR_BELOW_TOP"], [300, 53, 397, 49], [301, 14, 398, 10], [302, 14, 399, 10], [302, 18, 399, 10, "_scene$descriptor$opt"], [302, 39, 399, 10], [302, 42, 404, 14, "scene"], [302, 47, 404, 19], [302, 48, 404, 20, "descriptor"], [302, 58, 404, 30], [302, 59, 404, 31, "options"], [302, 66, 404, 38], [303, 16, 404, 38, "_scene$descriptor$opt2"], [303, 38, 404, 38], [303, 41, 404, 38, "_scene$descriptor$opt"], [303, 62, 404, 38], [303, 63, 400, 12, "headerShown"], [303, 74, 400, 23], [304, 16, 400, 12, "headerShown"], [304, 27, 400, 23], [304, 30, 400, 23, "_scene$descriptor$opt2"], [304, 52, 400, 23], [304, 66, 400, 26], [304, 70, 400, 30], [304, 73, 400, 30, "_scene$descriptor$opt2"], [304, 95, 400, 30], [305, 16, 401, 12, "headerTransparent"], [305, 33, 401, 29], [305, 36, 401, 29, "_scene$descriptor$opt"], [305, 57, 401, 29], [305, 58, 401, 12, "headerTransparent"], [305, 75, 401, 29], [306, 16, 402, 12, "freezeOnBlur"], [306, 28, 402, 24], [306, 31, 402, 24, "_scene$descriptor$opt"], [306, 52, 402, 24], [306, 53, 402, 12, "freezeOnBlur"], [306, 65, 402, 24], [307, 16, 403, 12, "autoHideHomeIndicator"], [307, 37, 403, 33], [307, 40, 403, 33, "_scene$descriptor$opt"], [307, 61, 403, 33], [307, 62, 403, 12, "autoHideHomeIndicator"], [307, 83, 403, 33], [308, 14, 405, 10], [308, 18, 405, 16, "safeAreaInsetTop"], [308, 34, 405, 32], [308, 37, 405, 35, "insets"], [308, 43, 405, 41], [308, 44, 405, 42, "top"], [308, 47, 405, 45], [309, 14, 406, 10], [309, 18, 406, 16, "safeAreaInsetRight"], [309, 36, 406, 34], [309, 39, 406, 37, "insets"], [309, 45, 406, 43], [309, 46, 406, 44, "right"], [309, 51, 406, 49], [310, 14, 407, 10], [310, 18, 407, 16, "safeAreaInsetBottom"], [310, 37, 407, 35], [310, 40, 407, 38, "insets"], [310, 46, 407, 44], [310, 47, 407, 45, "bottom"], [310, 53, 407, 51], [311, 14, 408, 10], [311, 18, 408, 16, "safeAreaInsetLeft"], [311, 35, 408, 33], [311, 38, 408, 36, "insets"], [311, 44, 408, 42], [311, 45, 408, 43, "left"], [311, 49, 408, 47], [312, 14, 409, 10], [312, 18, 409, 16, "headerHeight"], [312, 30, 409, 28], [312, 33, 409, 31, "headerShown"], [312, 44, 409, 42], [312, 49, 409, 47], [312, 54, 409, 52], [312, 57, 409, 55, "headerHeights"], [312, 70, 409, 68], [312, 71, 409, 69, "route"], [312, 76, 409, 74], [312, 77, 409, 75, "key"], [312, 80, 409, 78], [312, 81, 409, 79], [312, 84, 409, 82], [312, 85, 409, 83], [314, 14, 411, 10], [315, 14, 412, 10], [315, 18, 412, 16, "interpolationIndex"], [315, 36, 412, 34], [315, 39, 412, 37, "getInterpolationIndex"], [315, 60, 412, 58], [315, 61, 412, 59, "scenes"], [315, 67, 412, 65], [315, 69, 412, 67, "index"], [315, 74, 412, 72], [315, 75, 412, 73], [316, 14, 413, 10], [316, 18, 413, 16, "isModal"], [316, 25, 413, 23], [316, 28, 413, 26, "getIsModal"], [316, 38, 413, 36], [316, 39, 413, 37, "scene"], [316, 44, 413, 42], [316, 46, 413, 44, "interpolationIndex"], [316, 64, 413, 62], [316, 66, 413, 64, "isParentModal"], [316, 79, 413, 77], [316, 80, 413, 78], [317, 14, 414, 10], [317, 18, 414, 16, "isNextScreenTransparent"], [317, 41, 414, 39], [317, 44, 414, 42, "scenes"], [317, 50, 414, 48], [317, 51, 414, 49, "index"], [317, 56, 414, 54], [317, 59, 414, 57], [317, 60, 414, 58], [317, 61, 414, 59], [317, 63, 414, 61, "descriptor"], [317, 73, 414, 71], [317, 74, 414, 72, "options"], [317, 81, 414, 79], [317, 82, 414, 80, "presentation"], [317, 94, 414, 92], [317, 99, 414, 97], [317, 117, 414, 115], [318, 14, 415, 10], [318, 18, 415, 16, "detachCurrentScreen"], [318, 37, 415, 35], [318, 40, 415, 38, "scenes"], [318, 46, 415, 44], [318, 47, 415, 45, "index"], [318, 52, 415, 50], [318, 55, 415, 53], [318, 56, 415, 54], [318, 57, 415, 55], [318, 59, 415, 57, "descriptor"], [318, 69, 415, 67], [318, 70, 415, 68, "options"], [318, 77, 415, 75], [318, 78, 415, 76, "detachPreviousScreen"], [318, 98, 415, 96], [318, 103, 415, 101], [318, 108, 415, 106], [319, 14, 416, 10], [319, 21, 416, 17], [319, 34, 416, 30], [319, 38, 416, 30, "_jsx"], [319, 53, 416, 34], [319, 55, 416, 35, "MaybeScreen"], [319, 75, 416, 46], [319, 77, 416, 48], [320, 16, 417, 12, "style"], [320, 21, 417, 17], [320, 23, 417, 19], [320, 24, 417, 20, "StyleSheet"], [320, 47, 417, 30], [320, 48, 417, 31, "absoluteFill"], [320, 60, 417, 43], [320, 61, 417, 44], [321, 16, 418, 12, "enabled"], [321, 23, 418, 19], [321, 25, 418, 21, "detachInactiveScreens"], [321, 46, 418, 42], [322, 16, 419, 12, "active"], [322, 22, 419, 18], [322, 24, 419, 20, "isScreenActive"], [322, 38, 419, 34], [323, 16, 420, 12, "freezeOnBlur"], [323, 28, 420, 24], [323, 30, 420, 26, "freezeOnBlur"], [323, 42, 420, 38], [324, 16, 421, 12, "shouldFreeze"], [324, 28, 421, 24], [324, 30, 421, 26, "isScreenActive"], [324, 44, 421, 40], [324, 49, 421, 45, "STATE_INACTIVE"], [324, 63, 421, 59], [324, 67, 421, 63], [324, 68, 421, 64, "isPreloaded"], [324, 79, 421, 75], [325, 16, 422, 12, "homeIndicatorHidden"], [325, 35, 422, 31], [325, 37, 422, 33, "autoHideHomeIndicator"], [325, 58, 422, 54], [326, 16, 423, 12, "pointerEvents"], [326, 29, 423, 25], [326, 31, 423, 27], [326, 41, 423, 37], [327, 16, 424, 12, "children"], [327, 24, 424, 20], [327, 26, 424, 22], [327, 39, 424, 35], [327, 43, 424, 35, "_jsx"], [327, 58, 424, 39], [327, 60, 424, 40, "CardContainer"], [327, 88, 424, 53], [327, 90, 424, 55], [328, 18, 425, 14, "index"], [328, 23, 425, 19], [328, 25, 425, 21, "index"], [328, 30, 425, 26], [329, 18, 426, 14, "interpolationIndex"], [329, 36, 426, 32], [329, 38, 426, 34, "interpolationIndex"], [329, 56, 426, 52], [330, 18, 427, 14, "modal"], [330, 23, 427, 19], [330, 25, 427, 21, "isModal"], [330, 32, 427, 28], [331, 18, 428, 14, "active"], [331, 24, 428, 20], [331, 26, 428, 22, "index"], [331, 31, 428, 27], [331, 36, 428, 32, "routes"], [331, 42, 428, 38], [331, 43, 428, 39, "length"], [331, 49, 428, 45], [331, 52, 428, 48], [331, 53, 428, 49], [332, 18, 429, 14, "focused"], [332, 25, 429, 21], [332, 27, 429, 23, "focused"], [332, 34, 429, 30], [333, 18, 430, 14, "opening"], [333, 25, 430, 21], [333, 27, 430, 23, "openingRouteKeys"], [333, 43, 430, 39], [333, 44, 430, 40, "includes"], [333, 52, 430, 48], [333, 53, 430, 49, "route"], [333, 58, 430, 54], [333, 59, 430, 55, "key"], [333, 62, 430, 58], [333, 63, 430, 59], [334, 18, 431, 14, "closing"], [334, 25, 431, 21], [334, 27, 431, 23, "closingRouteKeys"], [334, 43, 431, 39], [334, 44, 431, 40, "includes"], [334, 52, 431, 48], [334, 53, 431, 49, "route"], [334, 58, 431, 54], [334, 59, 431, 55, "key"], [334, 62, 431, 58], [334, 63, 431, 59], [335, 18, 432, 14, "layout"], [335, 24, 432, 20], [335, 26, 432, 22, "layout"], [335, 32, 432, 28], [336, 18, 433, 14, "gesture"], [336, 25, 433, 21], [336, 27, 433, 23, "gesture"], [336, 34, 433, 30], [337, 18, 434, 14, "scene"], [337, 23, 434, 19], [337, 25, 434, 21, "scene"], [337, 30, 434, 26], [338, 18, 435, 14, "safeAreaInsetTop"], [338, 34, 435, 30], [338, 36, 435, 32, "safeAreaInsetTop"], [338, 52, 435, 48], [339, 18, 436, 14, "safeAreaInsetRight"], [339, 36, 436, 32], [339, 38, 436, 34, "safeAreaInsetRight"], [339, 56, 436, 52], [340, 18, 437, 14, "safeAreaInsetBottom"], [340, 37, 437, 33], [340, 39, 437, 35, "safeAreaInsetBottom"], [340, 58, 437, 54], [341, 18, 438, 14, "safeAreaInsetLeft"], [341, 35, 438, 31], [341, 37, 438, 33, "safeAreaInsetLeft"], [341, 54, 438, 50], [342, 18, 439, 14, "onGestureStart"], [342, 32, 439, 28], [342, 34, 439, 30, "onGestureStart"], [342, 48, 439, 44], [343, 18, 440, 14, "onGestureCancel"], [343, 33, 440, 29], [343, 35, 440, 31, "onGestureCancel"], [343, 50, 440, 46], [344, 18, 441, 14, "onGestureEnd"], [344, 30, 441, 26], [344, 32, 441, 28, "onGestureEnd"], [344, 44, 441, 40], [345, 18, 442, 14, "headerHeight"], [345, 30, 442, 26], [345, 32, 442, 28, "headerHeight"], [345, 44, 442, 40], [346, 18, 443, 14, "isParentHeaderShown"], [346, 37, 443, 33], [346, 39, 443, 35, "isParentHeaderShown"], [346, 58, 443, 54], [347, 18, 444, 14, "onHeaderHeightChange"], [347, 38, 444, 34], [347, 40, 444, 36], [347, 44, 444, 40], [347, 45, 444, 41, "handleHeaderLayout"], [347, 63, 444, 59], [348, 18, 445, 14, "getPreviousScene"], [348, 34, 445, 30], [348, 36, 445, 32], [348, 40, 445, 36], [348, 41, 445, 37, "getPreviousScene"], [348, 57, 445, 53], [349, 18, 446, 14, "getFocusedRoute"], [349, 33, 446, 29], [349, 35, 446, 31], [349, 39, 446, 35], [349, 40, 446, 36, "getFocusedRoute"], [349, 55, 446, 51], [350, 18, 447, 14, "hasAbsoluteFloatHeader"], [350, 40, 447, 36], [350, 42, 447, 38, "isFloatHeaderAbsolute"], [350, 63, 447, 59], [350, 67, 447, 63], [350, 68, 447, 64, "headerTransparent"], [350, 85, 447, 81], [351, 18, 448, 14, "renderHeader"], [351, 30, 448, 26], [351, 32, 448, 28, "renderHeader"], [351, 44, 448, 40], [352, 18, 449, 14, "onOpenRoute"], [352, 29, 449, 25], [352, 31, 449, 27, "onOpenRoute"], [352, 42, 449, 38], [353, 18, 450, 14, "onCloseRoute"], [353, 30, 450, 26], [353, 32, 450, 28, "onCloseRoute"], [353, 44, 450, 40], [354, 18, 451, 14, "onTransitionStart"], [354, 35, 451, 31], [354, 37, 451, 33, "onTransitionStart"], [354, 54, 451, 50], [355, 18, 452, 14, "onTransitionEnd"], [355, 33, 452, 29], [355, 35, 452, 31, "onTransitionEnd"], [355, 50, 452, 46], [356, 18, 453, 14, "isNextScreenTransparent"], [356, 41, 453, 37], [356, 43, 453, 39, "isNextScreenTransparent"], [356, 66, 453, 62], [357, 18, 454, 14, "detachCurrentScreen"], [357, 37, 454, 33], [357, 39, 454, 35, "detachCurrentScreen"], [357, 58, 454, 54], [358, 18, 455, 14, "preloaded"], [358, 27, 455, 23], [358, 29, 455, 25, "isPreloaded"], [359, 16, 456, 12], [359, 17, 456, 13], [360, 14, 457, 10], [360, 15, 457, 11], [360, 17, 457, 13, "route"], [360, 22, 457, 18], [360, 23, 457, 19, "key"], [360, 26, 457, 22], [360, 27, 457, 23], [361, 12, 458, 8], [361, 13, 458, 9], [362, 10, 459, 6], [362, 11, 459, 7], [362, 12, 459, 8], [362, 14, 459, 10, "isFloatHeaderAbsolute"], [362, 35, 459, 31], [362, 38, 459, 34, "floatingHeader"], [362, 52, 459, 48], [362, 55, 459, 51], [362, 59, 459, 55], [363, 8, 460, 4], [363, 9, 460, 5], [363, 10, 460, 6], [364, 6, 461, 2], [365, 4, 461, 3], [366, 6, 461, 3, "key"], [366, 9, 461, 3], [367, 6, 461, 3, "value"], [367, 11, 461, 3], [367, 13, 109, 2], [367, 22, 109, 9, "getDerivedStateFromProps"], [367, 46, 109, 33, "getDerivedStateFromProps"], [367, 47, 109, 34, "props"], [367, 52, 109, 39], [367, 54, 109, 41, "state"], [367, 59, 109, 46], [367, 61, 109, 48], [368, 8, 110, 4], [368, 12, 110, 8, "props"], [368, 17, 110, 13], [368, 18, 110, 14, "routes"], [368, 24, 110, 20], [368, 29, 110, 25, "state"], [368, 34, 110, 30], [368, 35, 110, 31, "routes"], [368, 41, 110, 37], [368, 45, 110, 41, "props"], [368, 50, 110, 46], [368, 51, 110, 47, "descriptors"], [368, 62, 110, 58], [368, 67, 110, 63, "state"], [368, 72, 110, 68], [368, 73, 110, 69, "descriptors"], [368, 84, 110, 80], [368, 86, 110, 82], [369, 10, 111, 6], [369, 17, 111, 13], [369, 21, 111, 17], [370, 8, 112, 4], [371, 8, 113, 4], [371, 12, 113, 10, "gestures"], [371, 20, 113, 18], [371, 23, 113, 21], [371, 24, 113, 22], [371, 27, 113, 25, "props"], [371, 32, 113, 30], [371, 33, 113, 31, "routes"], [371, 39, 113, 37], [371, 41, 113, 39], [371, 44, 113, 42, "props"], [371, 49, 113, 47], [371, 50, 113, 48, "state"], [371, 55, 113, 53], [371, 56, 113, 54, "preloadedRoutes"], [371, 71, 113, 69], [371, 72, 113, 70], [371, 73, 113, 71, "reduce"], [371, 79, 113, 77], [371, 80, 113, 78], [371, 81, 113, 79, "acc"], [371, 84, 113, 82], [371, 86, 113, 84, "curr"], [371, 90, 113, 88], [371, 95, 113, 93], [372, 10, 114, 6], [372, 14, 114, 12, "descriptor"], [372, 24, 114, 22], [372, 27, 114, 25, "props"], [372, 32, 114, 30], [372, 33, 114, 31, "descriptors"], [372, 44, 114, 42], [372, 45, 114, 43, "curr"], [372, 49, 114, 47], [372, 50, 114, 48, "key"], [372, 53, 114, 51], [372, 54, 114, 52], [372, 58, 114, 56, "props"], [372, 63, 114, 61], [372, 64, 114, 62, "preloadedDescriptors"], [372, 84, 114, 82], [372, 85, 114, 83, "curr"], [372, 89, 114, 87], [372, 90, 114, 88, "key"], [372, 93, 114, 91], [372, 94, 114, 92], [373, 10, 115, 6], [373, 14, 115, 6, "_ref4"], [373, 19, 115, 6], [373, 22, 117, 10, "descriptor"], [373, 32, 117, 20], [373, 34, 117, 22, "options"], [373, 41, 117, 29], [373, 45, 117, 33], [373, 46, 117, 34], [373, 47, 117, 35], [374, 12, 116, 8, "animation"], [374, 21, 116, 17], [374, 24, 116, 17, "_ref4"], [374, 29, 116, 17], [374, 30, 116, 8, "animation"], [374, 39, 116, 17], [375, 10, 118, 6, "acc"], [375, 13, 118, 9], [375, 14, 118, 10, "curr"], [375, 18, 118, 14], [375, 19, 118, 15, "key"], [375, 22, 118, 18], [375, 23, 118, 19], [375, 26, 118, 22, "state"], [375, 31, 118, 27], [375, 32, 118, 28, "gestures"], [375, 40, 118, 36], [375, 41, 118, 37, "curr"], [375, 45, 118, 41], [375, 46, 118, 42, "key"], [375, 49, 118, 45], [375, 50, 118, 46], [375, 54, 118, 50], [375, 58, 118, 54, "Animated"], [375, 79, 118, 62], [375, 80, 118, 63, "Value"], [375, 85, 118, 68], [375, 86, 118, 69, "props"], [375, 91, 118, 74], [375, 92, 118, 75, "openingRouteKeys"], [375, 108, 118, 91], [375, 109, 118, 92, "includes"], [375, 117, 118, 100], [375, 118, 118, 101, "curr"], [375, 122, 118, 105], [375, 123, 118, 106, "key"], [375, 126, 118, 109], [375, 127, 118, 110], [375, 131, 118, 114, "animation"], [375, 140, 118, 123], [375, 145, 118, 128], [375, 151, 118, 134], [375, 155, 118, 138, "props"], [375, 160, 118, 143], [375, 161, 118, 144, "state"], [375, 166, 118, 149], [375, 167, 118, 150, "preloadedRoutes"], [375, 182, 118, 165], [375, 183, 118, 166, "includes"], [375, 191, 118, 174], [375, 192, 118, 175, "curr"], [375, 196, 118, 179], [375, 197, 118, 180], [375, 200, 118, 183, "getDistanceFromOptions"], [375, 222, 118, 205], [375, 223, 118, 206, "state"], [375, 228, 118, 211], [375, 229, 118, 212, "layout"], [375, 235, 118, 218], [375, 237, 118, 220, "descriptor"], [375, 247, 118, 230], [375, 249, 118, 232, "props"], [375, 254, 118, 237], [375, 255, 118, 238, "direction"], [375, 264, 118, 247], [375, 269, 118, 252], [375, 274, 118, 257], [375, 275, 118, 258], [375, 278, 118, 261], [375, 279, 118, 262], [375, 280, 118, 263], [376, 10, 119, 6], [376, 17, 119, 13, "acc"], [376, 20, 119, 16], [377, 8, 120, 4], [377, 9, 120, 5], [377, 11, 120, 7], [377, 12, 120, 8], [377, 13, 120, 9], [377, 14, 120, 10], [378, 8, 121, 4], [378, 12, 121, 10, "modalRouteKeys"], [378, 26, 121, 24], [378, 29, 121, 27], [378, 33, 121, 27, "getModalRouteKeys"], [378, 70, 121, 44], [378, 72, 121, 45], [378, 73, 121, 46], [378, 76, 121, 49, "props"], [378, 81, 121, 54], [378, 82, 121, 55, "routes"], [378, 88, 121, 61], [378, 90, 121, 63], [378, 93, 121, 66, "props"], [378, 98, 121, 71], [378, 99, 121, 72, "state"], [378, 104, 121, 77], [378, 105, 121, 78, "preloadedRoutes"], [378, 120, 121, 93], [378, 121, 121, 94], [378, 123, 121, 96], [379, 10, 122, 6], [379, 13, 122, 9, "props"], [379, 18, 122, 14], [379, 19, 122, 15, "descriptors"], [379, 30, 122, 26], [380, 10, 123, 6], [380, 13, 123, 9, "props"], [380, 18, 123, 14], [380, 19, 123, 15, "preloadedDescriptors"], [381, 8, 124, 4], [381, 9, 124, 5], [381, 10, 124, 6], [382, 8, 125, 4], [382, 12, 125, 10, "scenes"], [382, 18, 125, 16], [382, 21, 125, 19], [382, 22, 125, 20], [382, 25, 125, 23, "props"], [382, 30, 125, 28], [382, 31, 125, 29, "routes"], [382, 37, 125, 35], [382, 39, 125, 37], [382, 42, 125, 40, "props"], [382, 47, 125, 45], [382, 48, 125, 46, "state"], [382, 53, 125, 51], [382, 54, 125, 52, "preloadedRoutes"], [382, 69, 125, 67], [382, 70, 125, 68], [382, 71, 125, 69, "map"], [382, 74, 125, 72], [382, 75, 125, 73], [382, 76, 125, 74, "route"], [382, 81, 125, 79], [382, 83, 125, 81, "index"], [382, 88, 125, 86], [382, 90, 125, 88, "self"], [382, 94, 125, 92], [382, 99, 125, 97], [383, 10, 126, 6], [384, 10, 127, 6], [384, 14, 127, 12, "isPreloaded"], [384, 25, 127, 23], [384, 28, 127, 26, "props"], [384, 33, 127, 31], [384, 34, 127, 32, "state"], [384, 39, 127, 37], [384, 40, 127, 38, "preloadedRoutes"], [384, 55, 127, 53], [384, 56, 127, 54, "includes"], [384, 64, 127, 62], [384, 65, 127, 63, "route"], [384, 70, 127, 68], [384, 71, 127, 69], [385, 10, 128, 6], [385, 14, 128, 12, "previousRoute"], [385, 27, 128, 25], [385, 30, 128, 28, "isPreloaded"], [385, 41, 128, 39], [385, 44, 128, 42, "undefined"], [385, 53, 128, 51], [385, 56, 128, 54, "self"], [385, 60, 128, 58], [385, 61, 128, 59, "index"], [385, 66, 128, 64], [385, 69, 128, 67], [385, 70, 128, 68], [385, 71, 128, 69], [386, 10, 129, 6], [386, 14, 129, 12, "nextRoute"], [386, 23, 129, 21], [386, 26, 129, 24, "isPreloaded"], [386, 37, 129, 35], [386, 40, 129, 38, "undefined"], [386, 49, 129, 47], [386, 52, 129, 50, "self"], [386, 56, 129, 54], [386, 57, 129, 55, "index"], [386, 62, 129, 60], [386, 65, 129, 63], [386, 66, 129, 64], [386, 67, 129, 65], [387, 10, 130, 6], [387, 14, 130, 12, "oldScene"], [387, 22, 130, 20], [387, 25, 130, 23, "state"], [387, 30, 130, 28], [387, 31, 130, 29, "scenes"], [387, 37, 130, 35], [387, 38, 130, 36, "index"], [387, 43, 130, 41], [387, 44, 130, 42], [388, 10, 131, 6], [388, 14, 131, 12, "currentGesture"], [388, 28, 131, 26], [388, 31, 131, 29, "gestures"], [388, 39, 131, 37], [388, 40, 131, 38, "route"], [388, 45, 131, 43], [388, 46, 131, 44, "key"], [388, 49, 131, 47], [388, 50, 131, 48], [389, 10, 132, 6], [389, 14, 132, 12, "previousGesture"], [389, 29, 132, 27], [389, 32, 132, 30, "previousRoute"], [389, 45, 132, 43], [389, 48, 132, 46, "gestures"], [389, 56, 132, 54], [389, 57, 132, 55, "previousRoute"], [389, 70, 132, 68], [389, 71, 132, 69, "key"], [389, 74, 132, 72], [389, 75, 132, 73], [389, 78, 132, 76, "undefined"], [389, 87, 132, 85], [390, 10, 133, 6], [390, 14, 133, 12, "nextGesture"], [390, 25, 133, 23], [390, 28, 133, 26, "nextRoute"], [390, 37, 133, 35], [390, 40, 133, 38, "gestures"], [390, 48, 133, 46], [390, 49, 133, 47, "nextRoute"], [390, 58, 133, 56], [390, 59, 133, 57, "key"], [390, 62, 133, 60], [390, 63, 133, 61], [390, 66, 133, 64, "undefined"], [390, 75, 133, 73], [391, 10, 134, 6], [391, 14, 134, 12, "descriptor"], [391, 24, 134, 22], [391, 27, 134, 25], [391, 28, 134, 26, "isPreloaded"], [391, 39, 134, 37], [391, 42, 134, 40, "props"], [391, 47, 134, 45], [391, 48, 134, 46, "preloadedDescriptors"], [391, 68, 134, 66], [391, 71, 134, 69, "props"], [391, 76, 134, 74], [391, 77, 134, 75, "descriptors"], [391, 88, 134, 86], [391, 90, 134, 88, "route"], [391, 95, 134, 93], [391, 96, 134, 94, "key"], [391, 99, 134, 97], [391, 100, 134, 98], [391, 104, 134, 102, "state"], [391, 109, 134, 107], [391, 110, 134, 108, "descriptors"], [391, 121, 134, 119], [391, 122, 134, 120, "route"], [391, 127, 134, 125], [391, 128, 134, 126, "key"], [391, 131, 134, 129], [391, 132, 134, 130], [391, 137, 134, 135, "oldScene"], [391, 145, 134, 143], [391, 148, 134, 146, "oldScene"], [391, 156, 134, 154], [391, 157, 134, 155, "descriptor"], [391, 167, 134, 165], [391, 170, 134, 168, "FALLBACK_DESCRIPTOR"], [391, 189, 134, 187], [391, 190, 134, 188], [392, 10, 135, 6], [392, 14, 135, 12, "nextDescriptor"], [392, 28, 135, 26], [392, 31, 135, 29, "nextRoute"], [392, 40, 135, 38], [392, 45, 135, 43, "props"], [392, 50, 135, 48], [392, 51, 135, 49, "descriptors"], [392, 62, 135, 60], [392, 63, 135, 61, "nextRoute"], [392, 72, 135, 70], [392, 74, 135, 72, "key"], [392, 77, 135, 75], [392, 78, 135, 76], [392, 82, 135, 80, "state"], [392, 87, 135, 85], [392, 88, 135, 86, "descriptors"], [392, 99, 135, 97], [392, 100, 135, 98, "nextRoute"], [392, 109, 135, 107], [392, 111, 135, 109, "key"], [392, 114, 135, 112], [392, 115, 135, 113], [392, 116, 135, 114], [393, 10, 136, 6], [393, 14, 136, 12, "previousDescriptor"], [393, 32, 136, 30], [393, 35, 136, 33, "previousRoute"], [393, 48, 136, 46], [393, 53, 136, 51, "props"], [393, 58, 136, 56], [393, 59, 136, 57, "descriptors"], [393, 70, 136, 68], [393, 71, 136, 69, "previousRoute"], [393, 84, 136, 82], [393, 86, 136, 84, "key"], [393, 89, 136, 87], [393, 90, 136, 88], [393, 94, 136, 92, "state"], [393, 99, 136, 97], [393, 100, 136, 98, "descriptors"], [393, 111, 136, 109], [393, 112, 136, 110, "previousRoute"], [393, 125, 136, 123], [393, 127, 136, 125, "key"], [393, 130, 136, 128], [393, 131, 136, 129], [393, 132, 136, 130], [395, 10, 138, 6], [396, 10, 139, 6], [397, 10, 140, 6], [398, 10, 141, 6], [399, 10, 142, 6], [400, 10, 143, 6], [401, 10, 144, 6], [401, 14, 144, 12, "optionsForTransitionConfig"], [401, 40, 144, 38], [401, 43, 144, 41, "index"], [401, 48, 144, 46], [401, 53, 144, 51, "self"], [401, 57, 144, 55], [401, 58, 144, 56, "length"], [401, 64, 144, 62], [401, 67, 144, 65], [401, 68, 144, 66], [401, 72, 144, 70, "nextDescriptor"], [401, 86, 144, 84], [401, 90, 144, 88, "nextDescriptor"], [401, 104, 144, 102], [401, 105, 144, 103, "options"], [401, 112, 144, 110], [401, 113, 144, 111, "presentation"], [401, 125, 144, 123], [401, 130, 144, 128], [401, 148, 144, 146], [401, 151, 144, 149, "nextDescriptor"], [401, 165, 144, 163], [401, 166, 144, 164, "options"], [401, 173, 144, 171], [401, 176, 144, 174, "descriptor"], [401, 186, 144, 184], [401, 187, 144, 185, "options"], [401, 194, 144, 192], [403, 10, 146, 6], [404, 10, 147, 6], [405, 10, 148, 6], [405, 14, 148, 12, "isModal"], [405, 21, 148, 19], [405, 24, 148, 22, "modalRouteKeys"], [405, 38, 148, 36], [405, 39, 148, 37, "includes"], [405, 47, 148, 45], [405, 48, 148, 46, "route"], [405, 53, 148, 51], [405, 54, 148, 52, "key"], [405, 57, 148, 55], [405, 58, 148, 56], [407, 10, 150, 6], [408, 10, 151, 6], [408, 14, 151, 12, "excludedPlatforms"], [408, 31, 151, 29], [408, 34, 151, 32, "Platform"], [408, 55, 151, 40], [408, 56, 151, 41, "OS"], [408, 58, 151, 43], [408, 63, 151, 48], [408, 68, 151, 53], [408, 72, 151, 57, "Platform"], [408, 93, 151, 65], [408, 94, 151, 66, "OS"], [408, 96, 151, 68], [408, 101, 151, 73], [408, 110, 151, 82], [408, 114, 151, 86, "Platform"], [408, 135, 151, 94], [408, 136, 151, 95, "OS"], [408, 138, 151, 97], [408, 143, 151, 102], [408, 150, 151, 109], [409, 10, 152, 6], [409, 14, 152, 12, "animation"], [409, 23, 152, 21], [409, 26, 152, 24, "optionsForTransitionConfig"], [409, 52, 152, 50], [409, 53, 152, 51, "animation"], [409, 62, 152, 60], [409, 67, 152, 65, "excludedPlatforms"], [409, 84, 152, 82], [409, 87, 152, 85], [409, 96, 152, 94], [409, 99, 152, 97], [409, 105, 152, 103], [409, 106, 152, 104], [410, 10, 153, 6], [410, 14, 153, 12, "isAnimationEnabled"], [410, 32, 153, 30], [410, 35, 153, 33, "animation"], [410, 44, 153, 42], [410, 49, 153, 47], [410, 55, 153, 53], [411, 10, 154, 6], [411, 14, 154, 12, "transitionPreset"], [411, 30, 154, 28], [411, 33, 154, 31, "animation"], [411, 42, 154, 40], [411, 47, 154, 45], [411, 56, 154, 54], [411, 59, 154, 57, "NAMED_TRANSITIONS_PRESETS"], [411, 84, 154, 82], [411, 85, 154, 83, "animation"], [411, 94, 154, 92], [411, 95, 154, 93], [411, 98, 154, 96, "isModal"], [411, 105, 154, 103], [411, 109, 154, 107, "optionsForTransitionConfig"], [411, 135, 154, 133], [411, 136, 154, 134, "presentation"], [411, 148, 154, 146], [411, 153, 154, 151], [411, 160, 154, 158], [411, 163, 154, 161, "ModalTransition"], [411, 197, 154, 176], [411, 200, 154, 179, "optionsForTransitionConfig"], [411, 226, 154, 205], [411, 227, 154, 206, "presentation"], [411, 239, 154, 218], [411, 244, 154, 223], [411, 262, 154, 241], [411, 265, 154, 244, "ModalFadeTransition"], [411, 303, 154, 263], [411, 306, 154, 266, "DefaultTransition"], [411, 342, 154, 283], [412, 10, 155, 6], [412, 14, 155, 6, "_optionsForTransition"], [412, 35, 155, 6], [412, 38, 162, 10, "optionsForTransitionConfig"], [412, 64, 162, 36], [412, 65, 156, 8, "gestureEnabled"], [412, 79, 156, 22], [413, 12, 156, 8, "gestureEnabled"], [413, 26, 156, 22], [413, 29, 156, 22, "_optionsForTransition"], [413, 50, 156, 22], [413, 64, 156, 25, "Platform"], [413, 85, 156, 33], [413, 86, 156, 34, "OS"], [413, 88, 156, 36], [413, 93, 156, 41], [413, 98, 156, 46], [413, 102, 156, 50, "isAnimationEnabled"], [413, 120, 156, 68], [413, 123, 156, 68, "_optionsForTransition"], [413, 144, 156, 68], [414, 12, 156, 68, "_optionsForTransition2"], [414, 34, 156, 68], [414, 37, 162, 10, "optionsForTransitionConfig"], [414, 63, 162, 36], [414, 64, 157, 8, "gestureDirection"], [414, 80, 157, 24], [415, 12, 157, 8, "gestureDirection"], [415, 28, 157, 24], [415, 31, 157, 24, "_optionsForTransition2"], [415, 53, 157, 24], [415, 67, 157, 27, "transitionPreset"], [415, 83, 157, 43], [415, 84, 157, 44, "gestureDirection"], [415, 100, 157, 60], [415, 103, 157, 60, "_optionsForTransition2"], [415, 125, 157, 60], [416, 12, 157, 60, "_optionsForTransition3"], [416, 34, 157, 60], [416, 37, 162, 10, "optionsForTransitionConfig"], [416, 63, 162, 36], [416, 64, 158, 8, "transitionSpec"], [416, 78, 158, 22], [417, 12, 158, 8, "transitionSpec"], [417, 26, 158, 22], [417, 29, 158, 22, "_optionsForTransition3"], [417, 51, 158, 22], [417, 65, 158, 25, "transitionPreset"], [417, 81, 158, 41], [417, 82, 158, 42, "transitionSpec"], [417, 96, 158, 56], [417, 99, 158, 56, "_optionsForTransition3"], [417, 121, 158, 56], [418, 12, 158, 56, "_optionsForTransition4"], [418, 34, 158, 56], [418, 37, 162, 10, "optionsForTransitionConfig"], [418, 63, 162, 36], [418, 64, 159, 8, "cardStyleInterpolator"], [418, 85, 159, 29], [419, 12, 159, 8, "cardStyleInterpolator"], [419, 33, 159, 29], [419, 36, 159, 29, "_optionsForTransition4"], [419, 58, 159, 29], [419, 72, 159, 32, "isAnimationEnabled"], [419, 90, 159, 50], [419, 93, 159, 53, "transitionPreset"], [419, 109, 159, 69], [419, 110, 159, 70, "cardStyleInterpolator"], [419, 131, 159, 91], [419, 134, 159, 94, "forNoAnimationCard"], [419, 172, 159, 112], [419, 175, 159, 112, "_optionsForTransition4"], [419, 197, 159, 112], [420, 12, 159, 112, "_optionsForTransition5"], [420, 34, 159, 112], [420, 37, 162, 10, "optionsForTransitionConfig"], [420, 63, 162, 36], [420, 64, 160, 8, "headerStyleInterpolator"], [420, 87, 160, 31], [421, 12, 160, 8, "headerStyleInterpolator"], [421, 35, 160, 31], [421, 38, 160, 31, "_optionsForTransition5"], [421, 60, 160, 31], [421, 74, 160, 34, "transitionPreset"], [421, 90, 160, 50], [421, 91, 160, 51, "headerStyleInterpolator"], [421, 114, 160, 74], [421, 117, 160, 74, "_optionsForTransition5"], [421, 139, 160, 74], [422, 12, 160, 74, "_optionsForTransition6"], [422, 34, 160, 74], [422, 37, 162, 10, "optionsForTransitionConfig"], [422, 63, 162, 36], [422, 64, 161, 8, "cardOverlayEnabled"], [422, 82, 161, 26], [423, 12, 161, 8, "cardOverlayEnabled"], [423, 30, 161, 26], [423, 33, 161, 26, "_optionsForTransition6"], [423, 55, 161, 26], [423, 69, 161, 29, "Platform"], [423, 90, 161, 37], [423, 91, 161, 38, "OS"], [423, 93, 161, 40], [423, 98, 161, 45], [423, 103, 161, 50], [423, 107, 161, 54, "optionsForTransitionConfig"], [423, 133, 161, 80], [423, 134, 161, 81, "presentation"], [423, 146, 161, 93], [423, 151, 161, 98], [423, 169, 161, 116], [423, 173, 161, 120, "getIsModalPresentation"], [423, 195, 161, 142], [423, 196, 161, 143, "cardStyleInterpolator"], [423, 217, 161, 164], [423, 218, 161, 165], [423, 221, 161, 165, "_optionsForTransition6"], [423, 243, 161, 165], [424, 10, 163, 6], [424, 14, 163, 12, "headerMode"], [424, 24, 163, 22], [424, 27, 163, 25, "descriptor"], [424, 37, 163, 35], [424, 38, 163, 36, "options"], [424, 45, 163, 43], [424, 46, 163, 44, "headerMode"], [424, 56, 163, 54], [424, 61, 163, 59], [424, 63, 163, 61, "optionsForTransitionConfig"], [424, 89, 163, 87], [424, 90, 163, 88, "presentation"], [424, 102, 163, 100], [424, 107, 163, 105], [424, 114, 163, 112], [424, 118, 163, 116, "optionsForTransitionConfig"], [424, 144, 163, 142], [424, 145, 163, 143, "presentation"], [424, 157, 163, 155], [424, 162, 163, 160], [424, 180, 163, 178], [424, 184, 163, 182, "nextDescriptor"], [424, 198, 163, 196], [424, 200, 163, 198, "options"], [424, 207, 163, 205], [424, 208, 163, 206, "presentation"], [424, 220, 163, 218], [424, 225, 163, 223], [424, 232, 163, 230], [424, 236, 163, 234, "nextDescriptor"], [424, 250, 163, 248], [424, 252, 163, 250, "options"], [424, 259, 163, 257], [424, 260, 163, 258, "presentation"], [424, 272, 163, 270], [424, 277, 163, 275], [424, 295, 163, 293], [424, 299, 163, 297, "getIsModalPresentation"], [424, 321, 163, 319], [424, 322, 163, 320, "cardStyleInterpolator"], [424, 343, 163, 341], [424, 344, 163, 342], [424, 345, 163, 343], [424, 349, 163, 347, "Platform"], [424, 370, 163, 355], [424, 371, 163, 356, "OS"], [424, 373, 163, 358], [424, 378, 163, 363], [424, 383, 163, 368], [424, 387, 163, 372, "descriptor"], [424, 397, 163, 382], [424, 398, 163, 383, "options"], [424, 405, 163, 390], [424, 406, 163, 391, "header"], [424, 412, 163, 397], [424, 417, 163, 402, "undefined"], [424, 426, 163, 411], [424, 429, 163, 414], [424, 436, 163, 421], [424, 439, 163, 424], [424, 447, 163, 432], [424, 448, 163, 433], [425, 10, 164, 6], [425, 14, 164, 12, "isRTL"], [425, 19, 164, 17], [425, 22, 164, 20, "props"], [425, 27, 164, 25], [425, 28, 164, 26, "direction"], [425, 37, 164, 35], [425, 42, 164, 40], [425, 47, 164, 45], [426, 10, 165, 6], [426, 14, 165, 12, "scene"], [426, 19, 165, 17], [426, 22, 165, 20], [427, 12, 166, 8, "route"], [427, 17, 166, 13], [428, 12, 167, 8, "descriptor"], [428, 22, 167, 18], [428, 24, 167, 20], [429, 14, 168, 10], [429, 17, 168, 13, "descriptor"], [429, 27, 168, 23], [430, 14, 169, 10, "options"], [430, 21, 169, 17], [430, 23, 169, 19], [431, 16, 170, 12], [431, 19, 170, 15, "descriptor"], [431, 29, 170, 25], [431, 30, 170, 26, "options"], [431, 37, 170, 33], [432, 16, 171, 12, "animation"], [432, 25, 171, 21], [433, 16, 172, 12, "cardOverlayEnabled"], [433, 34, 172, 30], [434, 16, 173, 12, "cardStyleInterpolator"], [434, 37, 173, 33], [435, 16, 174, 12, "gestureDirection"], [435, 32, 174, 28], [436, 16, 175, 12, "gestureEnabled"], [436, 30, 175, 26], [437, 16, 176, 12, "headerStyleInterpolator"], [437, 39, 176, 35], [438, 16, 177, 12, "transitionSpec"], [438, 30, 177, 26], [439, 16, 178, 12, "headerMode"], [440, 14, 179, 10], [441, 12, 180, 8], [441, 13, 180, 9], [442, 12, 181, 8, "progress"], [442, 20, 181, 16], [442, 22, 181, 18], [443, 14, 182, 10, "current"], [443, 21, 182, 17], [443, 23, 182, 19, "getProgressFromGesture"], [443, 45, 182, 41], [443, 46, 182, 42, "currentGesture"], [443, 60, 182, 56], [443, 62, 182, 58, "state"], [443, 67, 182, 63], [443, 68, 182, 64, "layout"], [443, 74, 182, 70], [443, 76, 182, 72, "descriptor"], [443, 86, 182, 82], [443, 88, 182, 84, "isRTL"], [443, 93, 182, 89], [443, 94, 182, 90], [444, 14, 183, 10, "next"], [444, 18, 183, 14], [444, 20, 183, 16, "nextGesture"], [444, 31, 183, 27], [444, 35, 183, 31, "nextDescriptor"], [444, 49, 183, 45], [444, 51, 183, 47, "options"], [444, 58, 183, 54], [444, 59, 183, 55, "presentation"], [444, 71, 183, 67], [444, 76, 183, 72], [444, 94, 183, 90], [444, 97, 183, 93, "getProgressFromGesture"], [444, 119, 183, 115], [444, 120, 183, 116, "nextGesture"], [444, 131, 183, 127], [444, 133, 183, 129, "state"], [444, 138, 183, 134], [444, 139, 183, 135, "layout"], [444, 145, 183, 141], [444, 147, 183, 143, "nextDescriptor"], [444, 161, 183, 157], [444, 163, 183, 159, "isRTL"], [444, 168, 183, 164], [444, 169, 183, 165], [444, 172, 183, 168, "undefined"], [444, 181, 183, 177], [445, 14, 184, 10, "previous"], [445, 22, 184, 18], [445, 24, 184, 20, "previousGesture"], [445, 39, 184, 35], [445, 42, 184, 38, "getProgressFromGesture"], [445, 64, 184, 60], [445, 65, 184, 61, "previousGesture"], [445, 80, 184, 76], [445, 82, 184, 78, "state"], [445, 87, 184, 83], [445, 88, 184, 84, "layout"], [445, 94, 184, 90], [445, 96, 184, 92, "previousDescriptor"], [445, 114, 184, 110], [445, 116, 184, 112, "isRTL"], [445, 121, 184, 117], [445, 122, 184, 118], [445, 125, 184, 121, "undefined"], [446, 12, 185, 8], [446, 13, 185, 9], [447, 12, 186, 8, "__memo"], [447, 18, 186, 14], [447, 20, 186, 16], [447, 21, 186, 17, "state"], [447, 26, 186, 22], [447, 27, 186, 23, "layout"], [447, 33, 186, 29], [447, 35, 186, 31, "descriptor"], [447, 45, 186, 41], [447, 47, 186, 43, "nextDescriptor"], [447, 61, 186, 57], [447, 63, 186, 59, "previousDescriptor"], [447, 81, 186, 77], [447, 83, 186, 79, "currentGesture"], [447, 97, 186, 93], [447, 99, 186, 95, "nextGesture"], [447, 110, 186, 106], [447, 112, 186, 108, "previousGesture"], [447, 127, 186, 123], [448, 10, 187, 6], [448, 11, 187, 7], [449, 10, 188, 6], [449, 14, 188, 10, "oldScene"], [449, 22, 188, 18], [449, 26, 188, 22, "scene"], [449, 31, 188, 27], [449, 32, 188, 28, "__memo"], [449, 38, 188, 34], [449, 39, 188, 35, "every"], [449, 44, 188, 40], [449, 45, 188, 41], [449, 46, 188, 42, "it"], [449, 48, 188, 44], [449, 50, 188, 46, "i"], [449, 51, 188, 47], [449, 56, 188, 52], [450, 12, 189, 8], [451, 12, 190, 8], [451, 19, 190, 15, "oldScene"], [451, 27, 190, 23], [451, 28, 190, 24, "__memo"], [451, 34, 190, 30], [451, 35, 190, 31, "i"], [451, 36, 190, 32], [451, 37, 190, 33], [451, 42, 190, 38, "it"], [451, 44, 190, 40], [452, 10, 191, 6], [452, 11, 191, 7], [452, 12, 191, 8], [452, 14, 191, 10], [453, 12, 192, 8], [453, 19, 192, 15, "oldScene"], [453, 27, 192, 23], [454, 10, 193, 6], [455, 10, 194, 6], [455, 17, 194, 13, "scene"], [455, 22, 194, 18], [456, 8, 195, 4], [456, 9, 195, 5], [456, 10, 195, 6], [457, 8, 196, 4], [457, 15, 196, 11], [458, 10, 197, 6, "routes"], [458, 16, 197, 12], [458, 18, 197, 14, "props"], [458, 23, 197, 19], [458, 24, 197, 20, "routes"], [458, 30, 197, 26], [459, 10, 198, 6, "scenes"], [459, 16, 198, 12], [460, 10, 199, 6, "gestures"], [460, 18, 199, 14], [461, 10, 200, 6, "descriptors"], [461, 21, 200, 17], [461, 23, 200, 19, "props"], [461, 28, 200, 24], [461, 29, 200, 25, "descriptors"], [461, 40, 200, 36], [462, 10, 201, 6, "headerHeights"], [462, 23, 201, 19], [462, 25, 201, 21, "getHeaderHeights"], [462, 41, 201, 37], [462, 42, 201, 38, "scenes"], [462, 48, 201, 44], [462, 50, 201, 46, "props"], [462, 55, 201, 51], [462, 56, 201, 52, "insets"], [462, 62, 201, 58], [462, 64, 201, 60, "props"], [462, 69, 201, 65], [462, 70, 201, 66, "isParentHeaderShown"], [462, 89, 201, 85], [462, 91, 201, 87, "props"], [462, 96, 201, 92], [462, 97, 201, 93, "isParentModal"], [462, 110, 201, 106], [462, 112, 201, 108, "state"], [462, 117, 201, 113], [462, 118, 201, 114, "layout"], [462, 124, 201, 120], [462, 126, 201, 122, "state"], [462, 131, 201, 127], [462, 132, 201, 128, "headerHeights"], [462, 145, 201, 141], [463, 8, 202, 4], [463, 9, 202, 5], [464, 6, 203, 2], [465, 4, 203, 3], [466, 2, 203, 3], [466, 4, 108, 31, "React"], [466, 9, 108, 36], [466, 10, 108, 37, "Component"], [466, 19, 108, 46], [467, 2, 463, 0], [467, 6, 463, 6, "styles"], [467, 12, 463, 12], [467, 15, 463, 15, "StyleSheet"], [467, 38, 463, 25], [467, 39, 463, 26, "create"], [467, 45, 463, 32], [467, 46, 463, 33], [468, 4, 464, 2, "container"], [468, 13, 464, 11], [468, 15, 464, 13], [469, 6, 465, 4, "flex"], [469, 10, 465, 8], [469, 12, 465, 10], [470, 4, 466, 2], [470, 5, 466, 3], [471, 4, 467, 2, "absolute"], [471, 12, 467, 10], [471, 14, 467, 12], [472, 6, 468, 4, "position"], [472, 14, 468, 12], [472, 16, 468, 14], [472, 26, 468, 24], [473, 6, 469, 4, "top"], [473, 9, 469, 7], [473, 11, 469, 9], [473, 12, 469, 10], [474, 6, 470, 4, "start"], [474, 11, 470, 9], [474, 13, 470, 11], [474, 14, 470, 12], [475, 6, 471, 4, "end"], [475, 9, 471, 7], [475, 11, 471, 9], [476, 4, 472, 2], [476, 5, 472, 3], [477, 4, 473, 2, "floating"], [477, 12, 473, 10], [477, 14, 473, 12], [478, 6, 474, 4, "zIndex"], [478, 12, 474, 10], [478, 14, 474, 12], [479, 4, 475, 2], [480, 2, 476, 0], [480, 3, 476, 1], [480, 4, 476, 2], [481, 0, 476, 3], [481, 3]], "functionMap": {"names": ["<global>", "getInterpolationIndex", "getIsModalPresentation", "getIsModal", "getHeaderHeights", "scenes.reduce$argument_0", "getDistanceFromOptions", "getProgressFromGesture", "CardStack", "getDerivedStateFromProps", "reduce$argument_0", "map$argument_0", "scene.__memo.every$argument_0", "constructor", "handleLayout", "setState$argument_0", "handleHeaderLayout", "getFocusedRoute", "getPreviousScene", "scenes.find$argument_0", "render", "state.scenes.slice.some$argument_0", "findLastIndex$argument_1"], "mappings": "AAA;8BCmC;CDe;+BEC;CFI;mBGC;CHU;yBIC;uBCC;GDW;CJC;+BMC;CNO;+BOC;CPiB;OQC;ECC;8ECI;KDO;yEEK;yCC+D;ODG;KFI;GDQ;EKC;GLe;iBMC;kBCS;KDQ;GNC;uBQC;kBDI;KCa;GRC;oBSC;GTK;qBUC;wCCa,yDD;GVI;EYC;mEC2B;KDW;yKEQ;SFK;4DTkC;SS2F;GZG;CRC"}}, "type": "js/module"}]}