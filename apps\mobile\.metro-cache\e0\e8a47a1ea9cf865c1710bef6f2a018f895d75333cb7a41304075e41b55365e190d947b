{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.memoize = memoize;\n  function memoize(callback) {\n    var previous;\n    var result;\n    return function () {\n      var hasChanged = false;\n      for (var _len = arguments.length, dependencies = new Array(_len), _key = 0; _key < _len; _key++) {\n        dependencies[_key] = arguments[_key];\n      }\n      if (previous) {\n        if (previous.length !== dependencies.length) {\n          hasChanged = true;\n        } else {\n          for (var i = 0; i < previous.length; i++) {\n            if (previous[i] !== dependencies[i]) {\n              hasChanged = true;\n              break;\n            }\n          }\n        }\n      } else {\n        hasChanged = true;\n      }\n      previous = dependencies;\n      if (hasChanged || result === undefined) {\n        result = callback(...dependencies);\n      }\n      return result;\n    };\n  }\n});", "lineCount": 37, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "memoize"], [7, 17, 1, 13], [7, 20, 1, 13, "memoize"], [7, 27, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "memoize"], [8, 18, 3, 23, "memoize"], [8, 19, 3, 24, "callback"], [8, 27, 3, 32], [8, 29, 3, 34], [9, 4, 4, 2], [9, 8, 4, 6, "previous"], [9, 16, 4, 14], [10, 4, 5, 2], [10, 8, 5, 6, "result"], [10, 14, 5, 12], [11, 4, 6, 2], [11, 11, 6, 9], [11, 23, 6, 30], [12, 6, 7, 4], [12, 10, 7, 8, "has<PERSON><PERSON>ed"], [12, 20, 7, 18], [12, 23, 7, 21], [12, 28, 7, 26], [13, 6, 7, 27], [13, 15, 7, 27, "_len"], [13, 19, 7, 27], [13, 22, 7, 27, "arguments"], [13, 31, 7, 27], [13, 32, 7, 27, "length"], [13, 38, 7, 27], [13, 40, 6, 13, "dependencies"], [13, 52, 6, 25], [13, 59, 6, 25, "Array"], [13, 64, 6, 25], [13, 65, 6, 25, "_len"], [13, 69, 6, 25], [13, 72, 6, 25, "_key"], [13, 76, 6, 25], [13, 82, 6, 25, "_key"], [13, 86, 6, 25], [13, 89, 6, 25, "_len"], [13, 93, 6, 25], [13, 95, 6, 25, "_key"], [13, 99, 6, 25], [14, 8, 6, 13, "dependencies"], [14, 20, 6, 25], [14, 21, 6, 25, "_key"], [14, 25, 6, 25], [14, 29, 6, 25, "arguments"], [14, 38, 6, 25], [14, 39, 6, 25, "_key"], [14, 43, 6, 25], [15, 6, 6, 25], [16, 6, 8, 4], [16, 10, 8, 8, "previous"], [16, 18, 8, 16], [16, 20, 8, 18], [17, 8, 9, 6], [17, 12, 9, 10, "previous"], [17, 20, 9, 18], [17, 21, 9, 19, "length"], [17, 27, 9, 25], [17, 32, 9, 30, "dependencies"], [17, 44, 9, 42], [17, 45, 9, 43, "length"], [17, 51, 9, 49], [17, 53, 9, 51], [18, 10, 10, 8, "has<PERSON><PERSON>ed"], [18, 20, 10, 18], [18, 23, 10, 21], [18, 27, 10, 25], [19, 8, 11, 6], [19, 9, 11, 7], [19, 15, 11, 13], [20, 10, 12, 8], [20, 15, 12, 13], [20, 19, 12, 17, "i"], [20, 20, 12, 18], [20, 23, 12, 21], [20, 24, 12, 22], [20, 26, 12, 24, "i"], [20, 27, 12, 25], [20, 30, 12, 28, "previous"], [20, 38, 12, 36], [20, 39, 12, 37, "length"], [20, 45, 12, 43], [20, 47, 12, 45, "i"], [20, 48, 12, 46], [20, 50, 12, 48], [20, 52, 12, 50], [21, 12, 13, 10], [21, 16, 13, 14, "previous"], [21, 24, 13, 22], [21, 25, 13, 23, "i"], [21, 26, 13, 24], [21, 27, 13, 25], [21, 32, 13, 30, "dependencies"], [21, 44, 13, 42], [21, 45, 13, 43, "i"], [21, 46, 13, 44], [21, 47, 13, 45], [21, 49, 13, 47], [22, 14, 14, 12, "has<PERSON><PERSON>ed"], [22, 24, 14, 22], [22, 27, 14, 25], [22, 31, 14, 29], [23, 14, 15, 12], [24, 12, 16, 10], [25, 10, 17, 8], [26, 8, 18, 6], [27, 6, 19, 4], [27, 7, 19, 5], [27, 13, 19, 11], [28, 8, 20, 6, "has<PERSON><PERSON>ed"], [28, 18, 20, 16], [28, 21, 20, 19], [28, 25, 20, 23], [29, 6, 21, 4], [30, 6, 22, 4, "previous"], [30, 14, 22, 12], [30, 17, 22, 15, "dependencies"], [30, 29, 22, 27], [31, 6, 23, 4], [31, 10, 23, 8, "has<PERSON><PERSON>ed"], [31, 20, 23, 18], [31, 24, 23, 22, "result"], [31, 30, 23, 28], [31, 35, 23, 33, "undefined"], [31, 44, 23, 42], [31, 46, 23, 44], [32, 8, 24, 6, "result"], [32, 14, 24, 12], [32, 17, 24, 15, "callback"], [32, 25, 24, 23], [32, 26, 24, 24], [32, 29, 24, 27, "dependencies"], [32, 41, 24, 39], [32, 42, 24, 40], [33, 6, 25, 4], [34, 6, 26, 4], [34, 13, 26, 11, "result"], [34, 19, 26, 17], [35, 4, 27, 2], [35, 5, 27, 3], [36, 2, 28, 0], [37, 0, 28, 1], [37, 3]], "functionMap": {"names": ["<global>", "memoize", "<anonymous>"], "mappings": "AAA;OCE;SCG;GDqB;CDC"}}, "type": "js/module"}]}