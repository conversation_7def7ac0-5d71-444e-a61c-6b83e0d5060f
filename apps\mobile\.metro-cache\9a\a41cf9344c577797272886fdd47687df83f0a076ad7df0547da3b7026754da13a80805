{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 58, "index": 105}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 106}, "end": {"line": 5, "column": 48, "index": 154}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ResourceSavingView = ResourceSavingView;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _jsxRuntime = require(_dependencyMap[4], \"react/jsx-runtime\");\n  var _excluded = [\"visible\", \"children\", \"style\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var FAR_FAR_AWAY = 30000; // this should be big enough to move the whole view out of its container\n\n  function ResourceSavingView(_ref) {\n    var visible = _ref.visible,\n      children = _ref.children,\n      style = _ref.style,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    if (_reactNative.Platform.OS === 'web') {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View\n      // @ts-expect-error: hidden exists on web, but not in React Native\n      , {\n        hidden: !visible,\n        style: [{\n          display: visible ? 'flex' : 'none'\n        }, styles.container, style],\n        pointerEvents: visible ? 'auto' : 'none',\n        ...rest,\n        children: children\n      });\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n      style: [styles.container, style]\n      // box-none doesn't seem to work properly on Android\n      ,\n\n      pointerEvents: visible ? 'auto' : 'none',\n      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n        collapsable: false,\n        removeClippedSubviews:\n        // On iOS & macOS, set removeClippedSubviews to true only when not focused\n        // This is an workaround for a bug where the clipped view never re-appears\n        _reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'macos' ? !visible : true,\n        pointerEvents: visible ? 'auto' : 'none',\n        style: visible ? styles.attached : styles.detached,\n        children: children\n      })\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flex: 1,\n      overflow: 'hidden'\n    },\n    attached: {\n      flex: 1\n    },\n    detached: {\n      flex: 1,\n      top: FAR_FAR_AWAY\n    }\n  });\n});", "lineCount": 66, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "ResourceSavingView"], [8, 28, 1, 13], [8, 31, 1, 13, "ResourceSavingView"], [8, 49, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "React"], [10, 11, 3, 0], [10, 14, 3, 0, "_interopRequireWildcard"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_reactNative"], [11, 18, 4, 0], [11, 21, 4, 0, "require"], [11, 28, 4, 0], [11, 29, 4, 0, "_dependencyMap"], [11, 43, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_jsxRuntime"], [12, 17, 5, 0], [12, 20, 5, 0, "require"], [12, 27, 5, 0], [12, 28, 5, 0, "_dependencyMap"], [12, 42, 5, 0], [13, 2, 5, 48], [13, 6, 5, 48, "_excluded"], [13, 15, 5, 48], [14, 2, 5, 48], [14, 11, 5, 48, "_interopRequireWildcard"], [14, 35, 5, 48, "e"], [14, 36, 5, 48], [14, 38, 5, 48, "t"], [14, 39, 5, 48], [14, 68, 5, 48, "WeakMap"], [14, 75, 5, 48], [14, 81, 5, 48, "r"], [14, 82, 5, 48], [14, 89, 5, 48, "WeakMap"], [14, 96, 5, 48], [14, 100, 5, 48, "n"], [14, 101, 5, 48], [14, 108, 5, 48, "WeakMap"], [14, 115, 5, 48], [14, 127, 5, 48, "_interopRequireWildcard"], [14, 150, 5, 48], [14, 162, 5, 48, "_interopRequireWildcard"], [14, 163, 5, 48, "e"], [14, 164, 5, 48], [14, 166, 5, 48, "t"], [14, 167, 5, 48], [14, 176, 5, 48, "t"], [14, 177, 5, 48], [14, 181, 5, 48, "e"], [14, 182, 5, 48], [14, 186, 5, 48, "e"], [14, 187, 5, 48], [14, 188, 5, 48, "__esModule"], [14, 198, 5, 48], [14, 207, 5, 48, "e"], [14, 208, 5, 48], [14, 214, 5, 48, "o"], [14, 215, 5, 48], [14, 217, 5, 48, "i"], [14, 218, 5, 48], [14, 220, 5, 48, "f"], [14, 221, 5, 48], [14, 226, 5, 48, "__proto__"], [14, 235, 5, 48], [14, 243, 5, 48, "default"], [14, 250, 5, 48], [14, 252, 5, 48, "e"], [14, 253, 5, 48], [14, 270, 5, 48, "e"], [14, 271, 5, 48], [14, 294, 5, 48, "e"], [14, 295, 5, 48], [14, 320, 5, 48, "e"], [14, 321, 5, 48], [14, 330, 5, 48, "f"], [14, 331, 5, 48], [14, 337, 5, 48, "o"], [14, 338, 5, 48], [14, 341, 5, 48, "t"], [14, 342, 5, 48], [14, 345, 5, 48, "n"], [14, 346, 5, 48], [14, 349, 5, 48, "r"], [14, 350, 5, 48], [14, 358, 5, 48, "o"], [14, 359, 5, 48], [14, 360, 5, 48, "has"], [14, 363, 5, 48], [14, 364, 5, 48, "e"], [14, 365, 5, 48], [14, 375, 5, 48, "o"], [14, 376, 5, 48], [14, 377, 5, 48, "get"], [14, 380, 5, 48], [14, 381, 5, 48, "e"], [14, 382, 5, 48], [14, 385, 5, 48, "o"], [14, 386, 5, 48], [14, 387, 5, 48, "set"], [14, 390, 5, 48], [14, 391, 5, 48, "e"], [14, 392, 5, 48], [14, 394, 5, 48, "f"], [14, 395, 5, 48], [14, 409, 5, 48, "_t"], [14, 411, 5, 48], [14, 415, 5, 48, "e"], [14, 416, 5, 48], [14, 432, 5, 48, "_t"], [14, 434, 5, 48], [14, 441, 5, 48, "hasOwnProperty"], [14, 455, 5, 48], [14, 456, 5, 48, "call"], [14, 460, 5, 48], [14, 461, 5, 48, "e"], [14, 462, 5, 48], [14, 464, 5, 48, "_t"], [14, 466, 5, 48], [14, 473, 5, 48, "i"], [14, 474, 5, 48], [14, 478, 5, 48, "o"], [14, 479, 5, 48], [14, 482, 5, 48, "Object"], [14, 488, 5, 48], [14, 489, 5, 48, "defineProperty"], [14, 503, 5, 48], [14, 508, 5, 48, "Object"], [14, 514, 5, 48], [14, 515, 5, 48, "getOwnPropertyDescriptor"], [14, 539, 5, 48], [14, 540, 5, 48, "e"], [14, 541, 5, 48], [14, 543, 5, 48, "_t"], [14, 545, 5, 48], [14, 552, 5, 48, "i"], [14, 553, 5, 48], [14, 554, 5, 48, "get"], [14, 557, 5, 48], [14, 561, 5, 48, "i"], [14, 562, 5, 48], [14, 563, 5, 48, "set"], [14, 566, 5, 48], [14, 570, 5, 48, "o"], [14, 571, 5, 48], [14, 572, 5, 48, "f"], [14, 573, 5, 48], [14, 575, 5, 48, "_t"], [14, 577, 5, 48], [14, 579, 5, 48, "i"], [14, 580, 5, 48], [14, 584, 5, 48, "f"], [14, 585, 5, 48], [14, 586, 5, 48, "_t"], [14, 588, 5, 48], [14, 592, 5, 48, "e"], [14, 593, 5, 48], [14, 594, 5, 48, "_t"], [14, 596, 5, 48], [14, 607, 5, 48, "f"], [14, 608, 5, 48], [14, 613, 5, 48, "e"], [14, 614, 5, 48], [14, 616, 5, 48, "t"], [14, 617, 5, 48], [15, 2, 6, 0], [15, 6, 6, 6, "FAR_FAR_AWAY"], [15, 18, 6, 18], [15, 21, 6, 21], [15, 26, 6, 26], [15, 27, 6, 27], [15, 28, 6, 28], [17, 2, 8, 7], [17, 11, 8, 16, "ResourceSavingView"], [17, 29, 8, 34, "ResourceSavingView"], [17, 30, 8, 34, "_ref"], [17, 34, 8, 34], [17, 36, 13, 3], [18, 4, 13, 3], [18, 8, 9, 2, "visible"], [18, 15, 9, 9], [18, 18, 9, 9, "_ref"], [18, 22, 9, 9], [18, 23, 9, 2, "visible"], [18, 30, 9, 9], [19, 6, 10, 2, "children"], [19, 14, 10, 10], [19, 17, 10, 10, "_ref"], [19, 21, 10, 10], [19, 22, 10, 2, "children"], [19, 30, 10, 10], [20, 6, 11, 2, "style"], [20, 11, 11, 7], [20, 14, 11, 7, "_ref"], [20, 18, 11, 7], [20, 19, 11, 2, "style"], [20, 24, 11, 7], [21, 6, 12, 5, "rest"], [21, 10, 12, 9], [21, 17, 12, 9, "_objectWithoutProperties2"], [21, 42, 12, 9], [21, 43, 12, 9, "default"], [21, 50, 12, 9], [21, 52, 12, 9, "_ref"], [21, 56, 12, 9], [21, 58, 12, 9, "_excluded"], [21, 67, 12, 9], [22, 4, 14, 2], [22, 8, 14, 6, "Platform"], [22, 29, 14, 14], [22, 30, 14, 15, "OS"], [22, 32, 14, 17], [22, 37, 14, 22], [22, 42, 14, 27], [22, 44, 14, 29], [23, 6, 15, 4], [23, 13, 15, 11], [23, 26, 15, 24], [23, 30, 15, 24, "_jsx"], [23, 45, 15, 28], [23, 47, 15, 29, "View"], [24, 6, 16, 4], [25, 6, 16, 4], [25, 8, 17, 6], [26, 8, 18, 6, "hidden"], [26, 14, 18, 12], [26, 16, 18, 14], [26, 17, 18, 15, "visible"], [26, 24, 18, 22], [27, 8, 19, 6, "style"], [27, 13, 19, 11], [27, 15, 19, 13], [27, 16, 19, 14], [28, 10, 20, 8, "display"], [28, 17, 20, 15], [28, 19, 20, 17, "visible"], [28, 26, 20, 24], [28, 29, 20, 27], [28, 35, 20, 33], [28, 38, 20, 36], [29, 8, 21, 6], [29, 9, 21, 7], [29, 11, 21, 9, "styles"], [29, 17, 21, 15], [29, 18, 21, 16, "container"], [29, 27, 21, 25], [29, 29, 21, 27, "style"], [29, 34, 21, 32], [29, 35, 21, 33], [30, 8, 22, 6, "pointerEvents"], [30, 21, 22, 19], [30, 23, 22, 21, "visible"], [30, 30, 22, 28], [30, 33, 22, 31], [30, 39, 22, 37], [30, 42, 22, 40], [30, 48, 22, 46], [31, 8, 23, 6], [31, 11, 23, 9, "rest"], [31, 15, 23, 13], [32, 8, 24, 6, "children"], [32, 16, 24, 14], [32, 18, 24, 16, "children"], [33, 6, 25, 4], [33, 7, 25, 5], [33, 8, 25, 6], [34, 4, 26, 2], [35, 4, 27, 2], [35, 11, 27, 9], [35, 24, 27, 22], [35, 28, 27, 22, "_jsx"], [35, 43, 27, 26], [35, 45, 27, 27, "View"], [35, 62, 27, 31], [35, 64, 27, 33], [36, 6, 28, 4, "style"], [36, 11, 28, 9], [36, 13, 28, 11], [36, 14, 28, 12, "styles"], [36, 20, 28, 18], [36, 21, 28, 19, "container"], [36, 30, 28, 28], [36, 32, 28, 30, "style"], [36, 37, 28, 35], [37, 6, 29, 4], [38, 6, 29, 4], [40, 6, 31, 4, "pointerEvents"], [40, 19, 31, 17], [40, 21, 31, 19, "visible"], [40, 28, 31, 26], [40, 31, 31, 29], [40, 37, 31, 35], [40, 40, 31, 38], [40, 46, 31, 44], [41, 6, 32, 4, "children"], [41, 14, 32, 12], [41, 16, 32, 14], [41, 29, 32, 27], [41, 33, 32, 27, "_jsx"], [41, 48, 32, 31], [41, 50, 32, 32, "View"], [41, 67, 32, 36], [41, 69, 32, 38], [42, 8, 33, 6, "collapsable"], [42, 19, 33, 17], [42, 21, 33, 19], [42, 26, 33, 24], [43, 8, 34, 6, "removeClippedSubviews"], [43, 29, 34, 27], [44, 8, 35, 6], [45, 8, 36, 6], [46, 8, 37, 6, "Platform"], [46, 29, 37, 14], [46, 30, 37, 15, "OS"], [46, 32, 37, 17], [46, 37, 37, 22], [46, 42, 37, 27], [46, 46, 37, 31, "Platform"], [46, 67, 37, 39], [46, 68, 37, 40, "OS"], [46, 70, 37, 42], [46, 75, 37, 47], [46, 82, 37, 54], [46, 85, 37, 57], [46, 86, 37, 58, "visible"], [46, 93, 37, 65], [46, 96, 37, 68], [46, 100, 37, 72], [47, 8, 38, 6, "pointerEvents"], [47, 21, 38, 19], [47, 23, 38, 21, "visible"], [47, 30, 38, 28], [47, 33, 38, 31], [47, 39, 38, 37], [47, 42, 38, 40], [47, 48, 38, 46], [48, 8, 39, 6, "style"], [48, 13, 39, 11], [48, 15, 39, 13, "visible"], [48, 22, 39, 20], [48, 25, 39, 23, "styles"], [48, 31, 39, 29], [48, 32, 39, 30, "attached"], [48, 40, 39, 38], [48, 43, 39, 41, "styles"], [48, 49, 39, 47], [48, 50, 39, 48, "detached"], [48, 58, 39, 56], [49, 8, 40, 6, "children"], [49, 16, 40, 14], [49, 18, 40, 16, "children"], [50, 6, 41, 4], [50, 7, 41, 5], [51, 4, 42, 2], [51, 5, 42, 3], [51, 6, 42, 4], [52, 2, 43, 0], [53, 2, 44, 0], [53, 6, 44, 6, "styles"], [53, 12, 44, 12], [53, 15, 44, 15, "StyleSheet"], [53, 38, 44, 25], [53, 39, 44, 26, "create"], [53, 45, 44, 32], [53, 46, 44, 33], [54, 4, 45, 2, "container"], [54, 13, 45, 11], [54, 15, 45, 13], [55, 6, 46, 4, "flex"], [55, 10, 46, 8], [55, 12, 46, 10], [55, 13, 46, 11], [56, 6, 47, 4, "overflow"], [56, 14, 47, 12], [56, 16, 47, 14], [57, 4, 48, 2], [57, 5, 48, 3], [58, 4, 49, 2, "attached"], [58, 12, 49, 10], [58, 14, 49, 12], [59, 6, 50, 4, "flex"], [59, 10, 50, 8], [59, 12, 50, 10], [60, 4, 51, 2], [60, 5, 51, 3], [61, 4, 52, 2, "detached"], [61, 12, 52, 10], [61, 14, 52, 12], [62, 6, 53, 4, "flex"], [62, 10, 53, 8], [62, 12, 53, 10], [62, 13, 53, 11], [63, 6, 54, 4, "top"], [63, 9, 54, 7], [63, 11, 54, 9, "FAR_FAR_AWAY"], [64, 4, 55, 2], [65, 2, 56, 0], [65, 3, 56, 1], [65, 4, 56, 2], [66, 0, 56, 3], [66, 3]], "functionMap": {"names": ["<global>", "ResourceSavingView"], "mappings": "AAA;OCO;CDmC"}}, "type": "js/module"}]}