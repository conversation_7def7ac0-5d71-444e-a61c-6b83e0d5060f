{"dependencies": [{"name": "./ConfigHelper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 115}, "end": {"line": 6, "column": 59, "index": 174}}], "key": "6ZcM0rOyHgJgja+Qc1i3or5WnnE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createAnimatedPropAdapter = createAnimatedPropAdapter;\n  var _ConfigHelper = require(_dependencyMap[0], \"./ConfigHelper\");\n  // @ts-expect-error This overload is required by our API.\n\n  function createAnimatedPropAdapter(adapter, nativeProps) {\n    var nativePropsToAdd = {};\n    nativeProps?.forEach(prop => {\n      nativePropsToAdd[prop] = true;\n    });\n    (0, _ConfigHelper.addWhitelistedNativeProps)(nativePropsToAdd);\n    return adapter;\n  }\n});", "lineCount": 19, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "createAnimatedPropAdapter"], [7, 35, 1, 13], [7, 38, 1, 13, "createAnimatedPropAdapter"], [7, 63, 1, 13], [8, 2, 6, 0], [8, 6, 6, 0, "_ConfigHelper"], [8, 19, 6, 0], [8, 22, 6, 0, "require"], [8, 29, 6, 0], [8, 30, 6, 0, "_dependencyMap"], [8, 44, 6, 0], [9, 2, 8, 0], [11, 2, 14, 7], [11, 11, 14, 16, "createAnimatedPropAdapter"], [11, 36, 14, 41, "createAnimatedPropAdapter"], [11, 37, 15, 2, "adapter"], [11, 44, 15, 38], [11, 46, 16, 2, "nativeProps"], [11, 57, 16, 24], [11, 59, 17, 31], [12, 4, 18, 2], [12, 8, 18, 8, "nativePropsToAdd"], [12, 24, 18, 52], [12, 27, 18, 55], [12, 28, 18, 56], [12, 29, 18, 57], [13, 4, 19, 2, "nativeProps"], [13, 15, 19, 13], [13, 17, 19, 15, "for<PERSON>ach"], [13, 24, 19, 22], [13, 25, 19, 24, "prop"], [13, 29, 19, 28], [13, 33, 19, 33], [14, 6, 20, 4, "nativePropsToAdd"], [14, 22, 20, 20], [14, 23, 20, 21, "prop"], [14, 27, 20, 25], [14, 28, 20, 26], [14, 31, 20, 29], [14, 35, 20, 33], [15, 4, 21, 2], [15, 5, 21, 3], [15, 6, 21, 4], [16, 4, 22, 2], [16, 8, 22, 2, "addWhitelistedNativeProps"], [16, 47, 22, 27], [16, 49, 22, 28, "nativePropsToAdd"], [16, 65, 22, 44], [16, 66, 22, 45], [17, 4, 23, 2], [17, 11, 23, 9, "adapter"], [17, 18, 23, 16], [18, 2, 24, 0], [19, 0, 24, 1], [19, 3]], "functionMap": {"names": ["<global>", "createAnimatedPropAdapter", "nativeProps.forEach$argument_0"], "mappings": "AAA;OCa;uBCK;GDE;CDG"}}, "type": "js/module"}]}