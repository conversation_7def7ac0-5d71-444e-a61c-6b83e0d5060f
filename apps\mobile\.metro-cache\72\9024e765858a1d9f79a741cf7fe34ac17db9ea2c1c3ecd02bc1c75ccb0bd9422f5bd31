{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./handlers/gestures/eventReceiver", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 67, "index": 67}}], "key": "K5FfC/mLU2BDXaFXT3iLm8XIJzw=", "exportNames": ["*"]}}, {"name": "./RNGestureHandlerModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 68}, "end": {"line": 2, "column": 62, "index": 130}}], "key": "etySpHONDQ3VUavJYfOCzF+6lCk=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 131}, "end": {"line": 3, "column": 35, "index": 166}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.initialize = initialize;\n  exports.maybeInitializeFabric = maybeInitializeFabric;\n  var _eventReceiver = require(_dependencyMap[1], \"./handlers/gestures/eventReceiver\");\n  var _RNGestureHandlerModule = _interopRequireDefault(require(_dependencyMap[2], \"./RNGestureHandlerModule\"));\n  var _utils = require(_dependencyMap[3], \"./utils\");\n  var fabricInitialized = false;\n  function initialize() {\n    (0, _eventReceiver.startListening)();\n  }\n\n  // Since isFabric() may give wrong results before the first render, we call this\n  // method during render of GestureHandlerRootView\n  function maybeInitializeFabric() {\n    if ((0, _utils.isFabric)() && !fabricInitialized) {\n      _RNGestureHandlerModule.default.install();\n      fabricInitialized = true;\n    }\n  }\n});", "lineCount": 24, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_eventReceiver"], [8, 20, 1, 0], [8, 23, 1, 0, "require"], [8, 30, 1, 0], [8, 31, 1, 0, "_dependencyMap"], [8, 45, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_RNGestureHandlerModule"], [9, 29, 2, 0], [9, 32, 2, 0, "_interopRequireDefault"], [9, 54, 2, 0], [9, 55, 2, 0, "require"], [9, 62, 2, 0], [9, 63, 2, 0, "_dependencyMap"], [9, 77, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_utils"], [10, 12, 3, 0], [10, 15, 3, 0, "require"], [10, 22, 3, 0], [10, 23, 3, 0, "_dependencyMap"], [10, 37, 3, 0], [11, 2, 5, 0], [11, 6, 5, 4, "fabricInitialized"], [11, 23, 5, 21], [11, 26, 5, 24], [11, 31, 5, 29], [12, 2, 7, 7], [12, 11, 7, 16, "initialize"], [12, 21, 7, 26, "initialize"], [12, 22, 7, 26], [12, 24, 7, 29], [13, 4, 8, 2], [13, 8, 8, 2, "startListening"], [13, 37, 8, 16], [13, 39, 8, 17], [13, 40, 8, 18], [14, 2, 9, 0], [16, 2, 11, 0], [17, 2, 12, 0], [18, 2, 13, 7], [18, 11, 13, 16, "maybeInitializeFabric"], [18, 32, 13, 37, "maybeInitializeFabric"], [18, 33, 13, 37], [18, 35, 13, 40], [19, 4, 14, 2], [19, 8, 14, 6], [19, 12, 14, 6, "isF<PERSON><PERSON>"], [19, 27, 14, 14], [19, 29, 14, 15], [19, 30, 14, 16], [19, 34, 14, 20], [19, 35, 14, 21, "fabricInitialized"], [19, 52, 14, 38], [19, 54, 14, 40], [20, 6, 15, 4, "RNGestureHandlerModule"], [20, 37, 15, 26], [20, 38, 15, 27, "install"], [20, 45, 15, 34], [20, 46, 15, 35], [20, 47, 15, 36], [21, 6, 16, 4, "fabricInitialized"], [21, 23, 16, 21], [21, 26, 16, 24], [21, 30, 16, 28], [22, 4, 17, 2], [23, 2, 18, 0], [24, 0, 18, 1], [24, 3]], "functionMap": {"names": ["<global>", "initialize", "maybeInitializeFabric"], "mappings": "AAA;OCM;CDE;OEI;CFK"}}, "type": "js/module"}]}