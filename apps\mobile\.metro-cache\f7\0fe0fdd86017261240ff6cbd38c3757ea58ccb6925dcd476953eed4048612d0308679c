{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./isArrayEqual.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 49, "index": 96}}], "key": "0Xv+Jf6vWjo3/vKyf7CxgrGRylo=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 97}, "end": {"line": 5, "column": 73, "index": 170}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}, {"name": "./NavigationRouteContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 171}, "end": {"line": 6, "column": 69, "index": 240}}], "key": "AWXnpGNA5UkH1qQUM7hLv2L9KzI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useOnGetState = useOnGetState;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _isArrayEqual = require(_dependencyMap[1], \"./isArrayEqual.js\");\n  var _NavigationBuilderContext = require(_dependencyMap[2], \"./NavigationBuilderContext.js\");\n  var _NavigationRouteContext = require(_dependencyMap[3], \"./NavigationRouteContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function useOnGetState(_ref) {\n    var getState = _ref.getState,\n      getStateListeners = _ref.getStateListeners;\n    var _React$useContext = React.useContext(_NavigationBuilderContext.NavigationBuilderContext),\n      addKeyedListener = _React$useContext.addKeyedListener;\n    var route = React.useContext(_NavigationRouteContext.NavigationRouteContext);\n    var key = route ? route.key : 'root';\n    var getRehydratedState = React.useCallback(() => {\n      var state = getState();\n\n      // Avoid returning new route objects if we don't need to\n      var routes = state.routes.map(route => {\n        var childState = getStateListeners[route.key]?.();\n        if (route.state === childState) {\n          return route;\n        }\n        return {\n          ...route,\n          state: childState\n        };\n      });\n      if ((0, _isArrayEqual.isArrayEqual)(state.routes, routes)) {\n        return state;\n      }\n      return {\n        ...state,\n        routes\n      };\n    }, [getState, getStateListeners]);\n    React.useEffect(() => {\n      return addKeyedListener?.('getState', key, getRehydratedState);\n    }, [addKeyedListener, getRehydratedState, key]);\n  }\n});", "lineCount": 46, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useOnGetState"], [7, 23, 1, 13], [7, 26, 1, 13, "useOnGetState"], [7, 39, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_isArrayEqual"], [9, 19, 4, 0], [9, 22, 4, 0, "require"], [9, 29, 4, 0], [9, 30, 4, 0, "_dependencyMap"], [9, 44, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_NavigationBuilderContext"], [10, 31, 5, 0], [10, 34, 5, 0, "require"], [10, 41, 5, 0], [10, 42, 5, 0, "_dependencyMap"], [10, 56, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_NavigationRouteContext"], [11, 29, 6, 0], [11, 32, 6, 0, "require"], [11, 39, 6, 0], [11, 40, 6, 0, "_dependencyMap"], [11, 54, 6, 0], [12, 2, 6, 69], [12, 11, 6, 69, "_interopRequireWildcard"], [12, 35, 6, 69, "e"], [12, 36, 6, 69], [12, 38, 6, 69, "t"], [12, 39, 6, 69], [12, 68, 6, 69, "WeakMap"], [12, 75, 6, 69], [12, 81, 6, 69, "r"], [12, 82, 6, 69], [12, 89, 6, 69, "WeakMap"], [12, 96, 6, 69], [12, 100, 6, 69, "n"], [12, 101, 6, 69], [12, 108, 6, 69, "WeakMap"], [12, 115, 6, 69], [12, 127, 6, 69, "_interopRequireWildcard"], [12, 150, 6, 69], [12, 162, 6, 69, "_interopRequireWildcard"], [12, 163, 6, 69, "e"], [12, 164, 6, 69], [12, 166, 6, 69, "t"], [12, 167, 6, 69], [12, 176, 6, 69, "t"], [12, 177, 6, 69], [12, 181, 6, 69, "e"], [12, 182, 6, 69], [12, 186, 6, 69, "e"], [12, 187, 6, 69], [12, 188, 6, 69, "__esModule"], [12, 198, 6, 69], [12, 207, 6, 69, "e"], [12, 208, 6, 69], [12, 214, 6, 69, "o"], [12, 215, 6, 69], [12, 217, 6, 69, "i"], [12, 218, 6, 69], [12, 220, 6, 69, "f"], [12, 221, 6, 69], [12, 226, 6, 69, "__proto__"], [12, 235, 6, 69], [12, 243, 6, 69, "default"], [12, 250, 6, 69], [12, 252, 6, 69, "e"], [12, 253, 6, 69], [12, 270, 6, 69, "e"], [12, 271, 6, 69], [12, 294, 6, 69, "e"], [12, 295, 6, 69], [12, 320, 6, 69, "e"], [12, 321, 6, 69], [12, 330, 6, 69, "f"], [12, 331, 6, 69], [12, 337, 6, 69, "o"], [12, 338, 6, 69], [12, 341, 6, 69, "t"], [12, 342, 6, 69], [12, 345, 6, 69, "n"], [12, 346, 6, 69], [12, 349, 6, 69, "r"], [12, 350, 6, 69], [12, 358, 6, 69, "o"], [12, 359, 6, 69], [12, 360, 6, 69, "has"], [12, 363, 6, 69], [12, 364, 6, 69, "e"], [12, 365, 6, 69], [12, 375, 6, 69, "o"], [12, 376, 6, 69], [12, 377, 6, 69, "get"], [12, 380, 6, 69], [12, 381, 6, 69, "e"], [12, 382, 6, 69], [12, 385, 6, 69, "o"], [12, 386, 6, 69], [12, 387, 6, 69, "set"], [12, 390, 6, 69], [12, 391, 6, 69, "e"], [12, 392, 6, 69], [12, 394, 6, 69, "f"], [12, 395, 6, 69], [12, 409, 6, 69, "_t"], [12, 411, 6, 69], [12, 415, 6, 69, "e"], [12, 416, 6, 69], [12, 432, 6, 69, "_t"], [12, 434, 6, 69], [12, 441, 6, 69, "hasOwnProperty"], [12, 455, 6, 69], [12, 456, 6, 69, "call"], [12, 460, 6, 69], [12, 461, 6, 69, "e"], [12, 462, 6, 69], [12, 464, 6, 69, "_t"], [12, 466, 6, 69], [12, 473, 6, 69, "i"], [12, 474, 6, 69], [12, 478, 6, 69, "o"], [12, 479, 6, 69], [12, 482, 6, 69, "Object"], [12, 488, 6, 69], [12, 489, 6, 69, "defineProperty"], [12, 503, 6, 69], [12, 508, 6, 69, "Object"], [12, 514, 6, 69], [12, 515, 6, 69, "getOwnPropertyDescriptor"], [12, 539, 6, 69], [12, 540, 6, 69, "e"], [12, 541, 6, 69], [12, 543, 6, 69, "_t"], [12, 545, 6, 69], [12, 552, 6, 69, "i"], [12, 553, 6, 69], [12, 554, 6, 69, "get"], [12, 557, 6, 69], [12, 561, 6, 69, "i"], [12, 562, 6, 69], [12, 563, 6, 69, "set"], [12, 566, 6, 69], [12, 570, 6, 69, "o"], [12, 571, 6, 69], [12, 572, 6, 69, "f"], [12, 573, 6, 69], [12, 575, 6, 69, "_t"], [12, 577, 6, 69], [12, 579, 6, 69, "i"], [12, 580, 6, 69], [12, 584, 6, 69, "f"], [12, 585, 6, 69], [12, 586, 6, 69, "_t"], [12, 588, 6, 69], [12, 592, 6, 69, "e"], [12, 593, 6, 69], [12, 594, 6, 69, "_t"], [12, 596, 6, 69], [12, 607, 6, 69, "f"], [12, 608, 6, 69], [12, 613, 6, 69, "e"], [12, 614, 6, 69], [12, 616, 6, 69, "t"], [12, 617, 6, 69], [13, 2, 7, 7], [13, 11, 7, 16, "useOnGetState"], [13, 24, 7, 29, "useOnGetState"], [13, 25, 7, 29, "_ref"], [13, 29, 7, 29], [13, 31, 10, 3], [14, 4, 10, 3], [14, 8, 8, 2, "getState"], [14, 16, 8, 10], [14, 19, 8, 10, "_ref"], [14, 23, 8, 10], [14, 24, 8, 2, "getState"], [14, 32, 8, 10], [15, 6, 9, 2, "getStateListeners"], [15, 23, 9, 19], [15, 26, 9, 19, "_ref"], [15, 30, 9, 19], [15, 31, 9, 2, "getStateListeners"], [15, 48, 9, 19], [16, 4, 11, 2], [16, 8, 11, 2, "_React$useContext"], [16, 25, 11, 2], [16, 28, 13, 6, "React"], [16, 33, 13, 11], [16, 34, 13, 12, "useContext"], [16, 44, 13, 22], [16, 45, 13, 23, "NavigationBuilderContext"], [16, 95, 13, 47], [16, 96, 13, 48], [17, 6, 12, 4, "addKeyedListener"], [17, 22, 12, 20], [17, 25, 12, 20, "_React$useContext"], [17, 42, 12, 20], [17, 43, 12, 4, "addKeyedListener"], [17, 59, 12, 20], [18, 4, 14, 2], [18, 8, 14, 8, "route"], [18, 13, 14, 13], [18, 16, 14, 16, "React"], [18, 21, 14, 21], [18, 22, 14, 22, "useContext"], [18, 32, 14, 32], [18, 33, 14, 33, "NavigationRouteContext"], [18, 79, 14, 55], [18, 80, 14, 56], [19, 4, 15, 2], [19, 8, 15, 8, "key"], [19, 11, 15, 11], [19, 14, 15, 14, "route"], [19, 19, 15, 19], [19, 22, 15, 22, "route"], [19, 27, 15, 27], [19, 28, 15, 28, "key"], [19, 31, 15, 31], [19, 34, 15, 34], [19, 40, 15, 40], [20, 4, 16, 2], [20, 8, 16, 8, "getRehydratedState"], [20, 26, 16, 26], [20, 29, 16, 29, "React"], [20, 34, 16, 34], [20, 35, 16, 35, "useCallback"], [20, 46, 16, 46], [20, 47, 16, 47], [20, 53, 16, 53], [21, 6, 17, 4], [21, 10, 17, 10, "state"], [21, 15, 17, 15], [21, 18, 17, 18, "getState"], [21, 26, 17, 26], [21, 27, 17, 27], [21, 28, 17, 28], [23, 6, 19, 4], [24, 6, 20, 4], [24, 10, 20, 10, "routes"], [24, 16, 20, 16], [24, 19, 20, 19, "state"], [24, 24, 20, 24], [24, 25, 20, 25, "routes"], [24, 31, 20, 31], [24, 32, 20, 32, "map"], [24, 35, 20, 35], [24, 36, 20, 36, "route"], [24, 41, 20, 41], [24, 45, 20, 45], [25, 8, 21, 6], [25, 12, 21, 12, "childState"], [25, 22, 21, 22], [25, 25, 21, 25, "getStateListeners"], [25, 42, 21, 42], [25, 43, 21, 43, "route"], [25, 48, 21, 48], [25, 49, 21, 49, "key"], [25, 52, 21, 52], [25, 53, 21, 53], [25, 56, 21, 56], [25, 57, 21, 57], [26, 8, 22, 6], [26, 12, 22, 10, "route"], [26, 17, 22, 15], [26, 18, 22, 16, "state"], [26, 23, 22, 21], [26, 28, 22, 26, "childState"], [26, 38, 22, 36], [26, 40, 22, 38], [27, 10, 23, 8], [27, 17, 23, 15, "route"], [27, 22, 23, 20], [28, 8, 24, 6], [29, 8, 25, 6], [29, 15, 25, 13], [30, 10, 26, 8], [30, 13, 26, 11, "route"], [30, 18, 26, 16], [31, 10, 27, 8, "state"], [31, 15, 27, 13], [31, 17, 27, 15, "childState"], [32, 8, 28, 6], [32, 9, 28, 7], [33, 6, 29, 4], [33, 7, 29, 5], [33, 8, 29, 6], [34, 6, 30, 4], [34, 10, 30, 8], [34, 14, 30, 8, "isArrayEqual"], [34, 40, 30, 20], [34, 42, 30, 21, "state"], [34, 47, 30, 26], [34, 48, 30, 27, "routes"], [34, 54, 30, 33], [34, 56, 30, 35, "routes"], [34, 62, 30, 41], [34, 63, 30, 42], [34, 65, 30, 44], [35, 8, 31, 6], [35, 15, 31, 13, "state"], [35, 20, 31, 18], [36, 6, 32, 4], [37, 6, 33, 4], [37, 13, 33, 11], [38, 8, 34, 6], [38, 11, 34, 9, "state"], [38, 16, 34, 14], [39, 8, 35, 6, "routes"], [40, 6, 36, 4], [40, 7, 36, 5], [41, 4, 37, 2], [41, 5, 37, 3], [41, 7, 37, 5], [41, 8, 37, 6, "getState"], [41, 16, 37, 14], [41, 18, 37, 16, "getStateListeners"], [41, 35, 37, 33], [41, 36, 37, 34], [41, 37, 37, 35], [42, 4, 38, 2, "React"], [42, 9, 38, 7], [42, 10, 38, 8, "useEffect"], [42, 19, 38, 17], [42, 20, 38, 18], [42, 26, 38, 24], [43, 6, 39, 4], [43, 13, 39, 11, "addKeyedListener"], [43, 29, 39, 27], [43, 32, 39, 30], [43, 42, 39, 40], [43, 44, 39, 42, "key"], [43, 47, 39, 45], [43, 49, 39, 47, "getRehydratedState"], [43, 67, 39, 65], [43, 68, 39, 66], [44, 4, 40, 2], [44, 5, 40, 3], [44, 7, 40, 5], [44, 8, 40, 6, "addKeyedListener"], [44, 24, 40, 22], [44, 26, 40, 24, "getRehydratedState"], [44, 44, 40, 42], [44, 46, 40, 44, "key"], [44, 49, 40, 47], [44, 50, 40, 48], [44, 51, 40, 49], [45, 2, 41, 0], [46, 0, 41, 1], [46, 3]], "functionMap": {"names": ["<global>", "useOnGetState", "getRehydratedState", "state.routes.map$argument_0", "React.useEffect$argument_0"], "mappings": "AAA;OCM;+CCS;oCCI;KDS;GDQ;kBGC;GHE;CDC"}}, "type": "js/module"}]}