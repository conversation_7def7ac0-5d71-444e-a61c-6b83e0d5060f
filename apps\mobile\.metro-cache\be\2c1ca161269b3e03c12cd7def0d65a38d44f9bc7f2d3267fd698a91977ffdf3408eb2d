{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./Colors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 11, "column": 18, "index": 131}}], "key": "zR6Hzer+l+w/2Wpfyy2UztSb1Pk=", "exportNames": ["*"]}}, {"name": "./core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 182}, "end": {"line": 13, "column": 37, "index": 219}}], "key": "0ONCEUiDM4TuRiJMnypk4k7v4nE=", "exportNames": ["*"]}}, {"name": "./culori", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 220}, "end": {"line": 14, "column": 30, "index": 250}}], "key": "4qS0vmAbB8g1HsXN+jyiAPHnocA=", "exportNames": ["*"]}}, {"name": "./errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 251}, "end": {"line": 15, "column": 43, "index": 294}}], "key": "rEld05quROH+iA6QLT6kkvqJ/qc=", "exportNames": ["*"]}}, {"name": "./hook/useSharedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 295}, "end": {"line": 16, "column": 55, "index": 350}}], "key": "i4Ic8zb0vc+XX5SmwW/ZGhEhmb4=", "exportNames": ["*"]}}, {"name": "./interpolation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 351}, "end": {"line": 17, "column": 61, "index": 412}}], "key": "Sh+s0sg7+1xEfnYiVkwzHVXvd5Q=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.interpolateColor = exports.Extrapolate = exports.ColorSpace = void 0;\n  exports.useInterpolateConfig = useInterpolateConfig;\n  var _Colors = require(_dependencyMap[1], \"./Colors\");\n  var _core = require(_dependencyMap[2], \"./core\");\n  var _culori = _interopRequireDefault(require(_dependencyMap[3], \"./culori\"));\n  var _errors = require(_dependencyMap[4], \"./errors\");\n  var _useSharedValue = require(_dependencyMap[5], \"./hook/useSharedValue\");\n  var _interpolation = require(_dependencyMap[6], \"./interpolation\");\n  /** @deprecated Please use Extrapolation instead */\n  var Extrapolate = exports.Extrapolate = _interpolation.Extrapolation;\n\n  /**\n   * Options for color interpolation.\n   *\n   * @param gamma - Gamma value used in gamma correction. Defaults to `2.2`.\n   * @param useCorrectedHSVInterpolation - Whether to reduce the number of colors\n   *   the interpolation has to go through. Defaults to `true`.\n   */\n  var _worklet_12089101195034_init_data = {\n    code: \"function interpolateColorTs1(value,inputRange,colors,options){const{interpolate,Extrapolation,hsvToColor}=this.__closure;let h=0;const{useCorrectedHSVInterpolation=true}=options;if(useCorrectedHSVInterpolation){const correctedInputRange=[inputRange[0]];const originalH=colors.h;const correctedH=[originalH[0]];for(let i=1;i<originalH.length;++i){const d=originalH[i]-originalH[i-1];if(originalH[i]>originalH[i-1]&&d>0.5){correctedInputRange.push(inputRange[i]);correctedInputRange.push(inputRange[i]+0.00001);correctedH.push(originalH[i]-1);correctedH.push(originalH[i]);}else if(originalH[i]<originalH[i-1]&&d<-0.5){correctedInputRange.push(inputRange[i]);correctedInputRange.push(inputRange[i]+0.00001);correctedH.push(originalH[i]+1);correctedH.push(originalH[i]);}else{correctedInputRange.push(inputRange[i]);correctedH.push(originalH[i]);}}h=(interpolate(value,correctedInputRange,correctedH,Extrapolation.CLAMP)+1)%1;}else{h=interpolate(value,inputRange,colors.h,Extrapolation.CLAMP);}const s=interpolate(value,inputRange,colors.s,Extrapolation.CLAMP);const v=interpolate(value,inputRange,colors.v,Extrapolation.CLAMP);const a=interpolate(value,inputRange,colors.a,Extrapolation.CLAMP);return hsvToColor(h,s,v,a);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolateColor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolateColorTs1\\\",\\\"value\\\",\\\"inputRange\\\",\\\"colors\\\",\\\"options\\\",\\\"interpolate\\\",\\\"Extrapolation\\\",\\\"hsvToColor\\\",\\\"__closure\\\",\\\"h\\\",\\\"useCorrectedHSVInterpolation\\\",\\\"correctedInputRange\\\",\\\"originalH\\\",\\\"correctedH\\\",\\\"i\\\",\\\"length\\\",\\\"d\\\",\\\"push\\\",\\\"CLAMP\\\",\\\"s\\\",\\\"v\\\",\\\"a\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolateColor.ts\\\"],\\\"mappings\\\":\\\"AAiC6B,QAC3B,CAAAA,mBAC6BA,CAAAC,KAC7B,CAAsBC,UACO,CAC1BC,MAAA,CAAAC,OAAA,QAAAC,WAAA,CAAAC,aAAA,CAAAC,UAAA,OAAAC,SAAA,CAEH,GAAI,CAAAC,CAAC,CAAG,CAAC,CACT,KAAM,CAAEC,4BAA4B,CAAG,IAAK,CAAC,CAAGN,OAAO,CACvD,GAAIM,4BAA4B,CAAE,CAKhC,KAAM,CAAAC,mBAAmB,CAAG,CAACT,UAAU,CAAC,CAAC,CAAC,CAAC,CAC3C,KAAM,CAAAU,SAAS,CAAGT,MAAM,CAACM,CAAC,CAC1B,KAAM,CAAAI,UAAU,CAAG,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC,CAEjC,IAAK,GAAI,CAAAE,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGF,SAAS,CAACG,MAAM,CAAE,EAAED,CAAC,CAAE,CACzC,KAAM,CAAAE,CAAC,CAAGJ,SAAS,CAACE,CAAC,CAAC,CAAGF,SAAS,CAACE,CAAC,CAAG,CAAC,CAAC,CACzC,GAAIF,SAAS,CAACE,CAAC,CAAC,CAAGF,SAAS,CAACE,CAAC,CAAG,CAAC,CAAC,EAAIE,CAAC,CAAG,GAAG,CAAE,CAC9CL,mBAAmB,CAACM,IAAI,CAACf,UAAU,CAACY,CAAC,CAAC,CAAC,CACvCH,mBAAmB,CAACM,IAAI,CAACf,UAAU,CAACY,CAAC,CAAC,CAAG,OAAO,CAAC,CACjDD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAG,CAAC,CAAC,CACjCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC,CAC/B,CAAC,IAAM,IAAIF,SAAS,CAACE,CAAC,CAAC,CAAGF,SAAS,CAACE,CAAC,CAAG,CAAC,CAAC,EAAIE,CAAC,CAAG,CAAC,GAAG,CAAE,CACtDL,mBAAmB,CAACM,IAAI,CAACf,UAAU,CAACY,CAAC,CAAC,CAAC,CACvCH,mBAAmB,CAACM,IAAI,CAACf,UAAU,CAACY,CAAC,CAAC,CAAG,OAAO,CAAC,CACjDD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAG,CAAC,CAAC,CACjCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC,CAC/B,CAAC,IAAM,CACLH,mBAAmB,CAACM,IAAI,CAACf,UAAU,CAACY,CAAC,CAAC,CAAC,CACvCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC,CAC/B,CACF,CACAL,CAAC,CACC,CAACJ,WAAW,CACVJ,KAAK,CACLU,mBAAmB,CACnBE,UAAU,CACVP,aAAa,CAACY,KAChB,CAAC,CACC,CAAC,EACH,CAAC,CACL,CAAC,IAAM,CACLT,CAAC,CAAGJ,WAAW,CAACJ,KAAK,CAAEC,UAAU,CAAEC,MAAM,CAACM,CAAC,CAAEH,aAAa,CAACY,KAAK,CAAC,CACnE,CACA,KAAM,CAAAC,CAAC,CAAGd,WAAW,CAACJ,KAAK,CAAEC,UAAU,CAAEC,MAAM,CAACgB,CAAC,CAAEb,aAAa,CAACY,KAAK,CAAC,CACvE,KAAM,CAAAE,CAAC,CAAGf,WAAW,CAACJ,KAAK,CAAEC,UAAU,CAAEC,MAAM,CAACiB,CAAC,CAAEd,aAAa,CAACY,KAAK,CAAC,CACvE,KAAM,CAAAG,CAAC,CAAGhB,WAAW,CAACJ,KAAK,CAAEC,UAAU,CAAEC,MAAM,CAACkB,CAAC,CAAEf,aAAa,CAACY,KAAK,CAAC,CACvE,MAAO,CAAAX,UAAU,CAACE,CAAC,CAAEU,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAC,CAC/B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var interpolateColorsHSV = function () {\n    var _e = [new global.Error(), -4, -27];\n    var interpolateColorTs1 = function (value, inputRange, colors, options) {\n      var h = 0;\n      var _options$useCorrected = options.useCorrectedHSVInterpolation,\n        useCorrectedHSVInterpolation = _options$useCorrected === void 0 ? true : _options$useCorrected;\n      if (useCorrectedHSVInterpolation) {\n        // if the difference between hues in a range is > 180 deg\n        // then move the hue at the right end of the range +/- 360 deg\n        // and add the next point in the original place + 0.00001 with original hue\n        // to not break the next range\n        var correctedInputRange = [inputRange[0]];\n        var originalH = colors.h;\n        var correctedH = [originalH[0]];\n        for (var i = 1; i < originalH.length; ++i) {\n          var d = originalH[i] - originalH[i - 1];\n          if (originalH[i] > originalH[i - 1] && d > 0.5) {\n            correctedInputRange.push(inputRange[i]);\n            correctedInputRange.push(inputRange[i] + 0.00001);\n            correctedH.push(originalH[i] - 1);\n            correctedH.push(originalH[i]);\n          } else if (originalH[i] < originalH[i - 1] && d < -0.5) {\n            correctedInputRange.push(inputRange[i]);\n            correctedInputRange.push(inputRange[i] + 0.00001);\n            correctedH.push(originalH[i] + 1);\n            correctedH.push(originalH[i]);\n          } else {\n            correctedInputRange.push(inputRange[i]);\n            correctedH.push(originalH[i]);\n          }\n        }\n        h = ((0, _interpolation.interpolate)(value, correctedInputRange, correctedH, _interpolation.Extrapolation.CLAMP) + 1) % 1;\n      } else {\n        h = (0, _interpolation.interpolate)(value, inputRange, colors.h, _interpolation.Extrapolation.CLAMP);\n      }\n      var s = (0, _interpolation.interpolate)(value, inputRange, colors.s, _interpolation.Extrapolation.CLAMP);\n      var v = (0, _interpolation.interpolate)(value, inputRange, colors.v, _interpolation.Extrapolation.CLAMP);\n      var a = (0, _interpolation.interpolate)(value, inputRange, colors.a, _interpolation.Extrapolation.CLAMP);\n      return (0, _Colors.hsvToColor)(h, s, v, a);\n    };\n    interpolateColorTs1.__closure = {\n      interpolate: _interpolation.interpolate,\n      Extrapolation: _interpolation.Extrapolation,\n      hsvToColor: _Colors.hsvToColor\n    };\n    interpolateColorTs1.__workletHash = 12089101195034;\n    interpolateColorTs1.__initData = _worklet_12089101195034_init_data;\n    interpolateColorTs1.__stackDetails = _e;\n    return interpolateColorTs1;\n  }();\n  var _worklet_12758569594929_init_data = {\n    code: \"function interpolateColorTs2(x,gamma){return x.map(function(v){return Math.pow(v/255,gamma);});}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolateColor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolateColorTs2\\\",\\\"x\\\",\\\"gamma\\\",\\\"map\\\",\\\"v\\\",\\\"Math\\\",\\\"pow\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolateColor.ts\\\"],\\\"mappings\\\":\\\"AAsFsB,QAAC,CAAAA,mBAAyCA,CAAAC,CAAA,CAAAC,KAAA,EAE9D,MAAO,CAAAD,CAAC,CAACE,GAAG,CAAE,SAAAC,CAAC,QAAK,CAAAC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAG,GAAG,CAAEF,KAAK,CAAC,GAAC,CAC/C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var toLinearSpace = function () {\n    var _e = [new global.Error(), 1, -27];\n    var interpolateColorTs2 = function (x, gamma) {\n      return x.map(v => Math.pow(v / 255, gamma));\n    };\n    interpolateColorTs2.__closure = {};\n    interpolateColorTs2.__workletHash = 12758569594929;\n    interpolateColorTs2.__initData = _worklet_12758569594929_init_data;\n    interpolateColorTs2.__stackDetails = _e;\n    return interpolateColorTs2;\n  }();\n  var _worklet_16323081088977_init_data = {\n    code: \"function interpolateColorTs3(x,gamma){return Math.round(Math.pow(x,1/gamma)*255);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolateColor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolateColorTs3\\\",\\\"x\\\",\\\"gamma\\\",\\\"Math\\\",\\\"round\\\",\\\"pow\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolateColor.ts\\\"],\\\"mappings\\\":\\\"AA2FqB,QAAC,CAAAA,mBAAqCA,CAAAC,CAAA,CAAAC,KAAA,EAEzD,MAAO,CAAAC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAE,CAAC,CAAGC,KAAK,CAAC,CAAG,GAAG,CAAC,CACjD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var toGammaSpace = function () {\n    var _e = [new global.Error(), 1, -27];\n    var interpolateColorTs3 = function (x, gamma) {\n      return Math.round(Math.pow(x, 1 / gamma) * 255);\n    };\n    interpolateColorTs3.__closure = {};\n    interpolateColorTs3.__workletHash = 16323081088977;\n    interpolateColorTs3.__initData = _worklet_16323081088977_init_data;\n    interpolateColorTs3.__stackDetails = _e;\n    return interpolateColorTs3;\n  }();\n  var _worklet_11009674977133_init_data = {\n    code: \"function interpolateColorTs4(value,inputRange,colors,options){const{toLinearSpace,interpolate,Extrapolation,rgbaColor,toGammaSpace}=this.__closure;const{gamma=2.2}=options;let{r:outputR,g:outputG,b:outputB}=colors;if(gamma!==1){outputR=toLinearSpace(outputR,gamma);outputG=toLinearSpace(outputG,gamma);outputB=toLinearSpace(outputB,gamma);}const r=interpolate(value,inputRange,outputR,Extrapolation.CLAMP);const g=interpolate(value,inputRange,outputG,Extrapolation.CLAMP);const b=interpolate(value,inputRange,outputB,Extrapolation.CLAMP);const a=interpolate(value,inputRange,colors.a,Extrapolation.CLAMP);if(gamma===1){return rgbaColor(r,g,b,a);}return rgbaColor(toGammaSpace(r,gamma),toGammaSpace(g,gamma),toGammaSpace(b,gamma),a);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolateColor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolateColorTs4\\\",\\\"value\\\",\\\"inputRange\\\",\\\"colors\\\",\\\"options\\\",\\\"toLinearSpace\\\",\\\"interpolate\\\",\\\"Extrapolation\\\",\\\"rgbaColor\\\",\\\"toGammaSpace\\\",\\\"__closure\\\",\\\"gamma\\\",\\\"r\\\",\\\"outputR\\\",\\\"g\\\",\\\"outputG\\\",\\\"b\\\",\\\"outputB\\\",\\\"CLAMP\\\",\\\"a\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolateColor.ts\\\"],\\\"mappings\\\":\\\"AAgG6B,QAC3B,CAAAA,mBAC6BA,CAAAC,KAC7B,CAAsBC,UACO,CAC1BC,MAAA,CAAAC,OAAA,QAAAC,aAAA,CAAAC,WAAA,CAAAC,aAAA,CAAAC,SAAA,CAAAC,YAAA,OAAAC,SAAA,CAEH,KAAM,CAAEC,KAAK,CAAG,GAAI,CAAC,CAAGP,OAAO,CAC/B,GAAI,CAAEQ,CAAC,CAAEC,OAAO,CAAEC,CAAC,CAAEC,OAAO,CAAEC,CAAC,CAAEC,OAAQ,CAAC,CAAGd,MAAM,CACnD,GAAIQ,KAAK,GAAK,CAAC,CAAE,CACfE,OAAO,CAAGR,aAAa,CAACQ,OAAO,CAAEF,KAAK,CAAC,CACvCI,OAAO,CAAGV,aAAa,CAACU,OAAO,CAAEJ,KAAK,CAAC,CACvCM,OAAO,CAAGZ,aAAa,CAACY,OAAO,CAAEN,KAAK,CAAC,CACzC,CACA,KAAM,CAAAC,CAAC,CAAGN,WAAW,CAACL,KAAK,CAAEC,UAAU,CAAEW,OAAO,CAAEN,aAAa,CAACW,KAAK,CAAC,CACtE,KAAM,CAAAJ,CAAC,CAAGR,WAAW,CAACL,KAAK,CAAEC,UAAU,CAAEa,OAAO,CAAER,aAAa,CAACW,KAAK,CAAC,CACtE,KAAM,CAAAF,CAAC,CAAGV,WAAW,CAACL,KAAK,CAAEC,UAAU,CAAEe,OAAO,CAAEV,aAAa,CAACW,KAAK,CAAC,CACtE,KAAM,CAAAC,CAAC,CAAGb,WAAW,CAACL,KAAK,CAAEC,UAAU,CAAEC,MAAM,CAACgB,CAAC,CAAEZ,aAAa,CAACW,KAAK,CAAC,CACvE,GAAIP,KAAK,GAAK,CAAC,CAAE,CACf,MAAO,CAAAH,SAAS,CAACI,CAAC,CAAEE,CAAC,CAAEE,CAAC,CAAEG,CAAC,CAAC,CAC9B,CACA,MAAO,CAAAX,SAAS,CACdC,YAAY,CAACG,CAAC,CAAED,KAAK,CAAC,CACtBF,YAAY,CAACK,CAAC,CAAEH,KAAK,CAAC,CACtBF,YAAY,CAACO,CAAC,CAAEL,KAAK,CAAC,CACtBQ,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var interpolateColorsRGB = function () {\n    var _e = [new global.Error(), -6, -27];\n    var interpolateColorTs4 = function (value, inputRange, colors, options) {\n      var _options$gamma = options.gamma,\n        gamma = _options$gamma === void 0 ? 2.2 : _options$gamma;\n      var outputR = colors.r,\n        outputG = colors.g,\n        outputB = colors.b;\n      if (gamma !== 1) {\n        outputR = toLinearSpace(outputR, gamma);\n        outputG = toLinearSpace(outputG, gamma);\n        outputB = toLinearSpace(outputB, gamma);\n      }\n      var r = (0, _interpolation.interpolate)(value, inputRange, outputR, _interpolation.Extrapolation.CLAMP);\n      var g = (0, _interpolation.interpolate)(value, inputRange, outputG, _interpolation.Extrapolation.CLAMP);\n      var b = (0, _interpolation.interpolate)(value, inputRange, outputB, _interpolation.Extrapolation.CLAMP);\n      var a = (0, _interpolation.interpolate)(value, inputRange, colors.a, _interpolation.Extrapolation.CLAMP);\n      if (gamma === 1) {\n        return (0, _Colors.rgbaColor)(r, g, b, a);\n      }\n      return (0, _Colors.rgbaColor)(toGammaSpace(r, gamma), toGammaSpace(g, gamma), toGammaSpace(b, gamma), a);\n    };\n    interpolateColorTs4.__closure = {\n      toLinearSpace,\n      interpolate: _interpolation.interpolate,\n      Extrapolation: _interpolation.Extrapolation,\n      rgbaColor: _Colors.rgbaColor,\n      toGammaSpace\n    };\n    interpolateColorTs4.__workletHash = 11009674977133;\n    interpolateColorTs4.__initData = _worklet_11009674977133_init_data;\n    interpolateColorTs4.__stackDetails = _e;\n    return interpolateColorTs4;\n  }();\n  var _worklet_12726126991717_init_data = {\n    code: \"function interpolateColorTs5(value,inputRange,colors,_options){const{interpolate,Extrapolation,culori,rgbaColor}=this.__closure;const l=interpolate(value,inputRange,colors.l,Extrapolation.CLAMP);const a=interpolate(value,inputRange,colors.a,Extrapolation.CLAMP);const b=interpolate(value,inputRange,colors.b,Extrapolation.CLAMP);const alpha=interpolate(value,inputRange,colors.alpha,Extrapolation.CLAMP);const{r:_r,g:_g,b:_b,alpha:_alpha}=culori.oklab.convert.toRgb({l:l,a:a,b:b,alpha:alpha});return rgbaColor(_r,_g,_b,_alpha);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolateColor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolateColorTs5\\\",\\\"value\\\",\\\"inputRange\\\",\\\"colors\\\",\\\"_options\\\",\\\"interpolate\\\",\\\"Extrapolation\\\",\\\"culori\\\",\\\"rgbaColor\\\",\\\"__closure\\\",\\\"l\\\",\\\"CLAMP\\\",\\\"a\\\",\\\"b\\\",\\\"alpha\\\",\\\"r\\\",\\\"_r\\\",\\\"g\\\",\\\"_g\\\",\\\"_b\\\",\\\"_alpha\\\",\\\"oklab\\\",\\\"convert\\\",\\\"toRgb\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolateColor.ts\\\"],\\\"mappings\\\":\\\"AA6H6B,QAC3B,CAAAA,mBAC6BA,CAAAC,KAC7B,CAAsBC,UACQ,CAC3BC,MAAA,CAAAC,QAAA,QAAAC,WAAA,CAAAC,aAAA,CAAAC,MAAA,CAAAC,SAAA,OAAAC,SAAA,CAEH,KAAM,CAAAC,CAAC,CAAGL,WAAW,CAACJ,KAAK,CAAEC,UAAU,CAAEC,MAAM,CAACO,CAAC,CAAEJ,aAAa,CAACK,KAAK,CAAC,CACvE,KAAM,CAAAC,CAAC,CAAGP,WAAW,CAACJ,KAAK,CAAEC,UAAU,CAAEC,MAAM,CAACS,CAAC,CAAEN,aAAa,CAACK,KAAK,CAAC,CACvE,KAAM,CAAAE,CAAC,CAAGR,WAAW,CAACJ,KAAK,CAAEC,UAAU,CAAEC,MAAM,CAACU,CAAC,CAAEP,aAAa,CAACK,KAAK,CAAC,CACvE,KAAM,CAAAG,KAAK,CAAGT,WAAW,CACvBJ,KAAK,CACLC,UAAU,CACVC,MAAM,CAACW,KAAK,CACZR,aAAa,CAACK,KAChB,CAAC,CACD,KAAM,CACJI,CAAC,CAAEC,EAAE,CACLC,CAAC,CAAEC,EAAE,CACLL,CAAC,CAAEM,EAAE,CACLL,KAAK,CAAEM,MACT,CAAC,CAAGb,MAAM,CAACc,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC,CAAEb,CAAC,CAADA,CAAC,CAAEE,CAAC,CAADA,CAAC,CAAEC,CAAC,CAADA,CAAC,CAAEC,KAAA,CAAAA,KAAM,CAAC,CAAC,CAClD,MAAO,CAAAN,SAAS,CAACQ,EAAE,CAAEE,EAAE,CAAEC,EAAE,CAAEC,MAAM,CAAC,CACtC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var interpolateColorsLAB = function () {\n    var _e = [new global.Error(), -5, -27];\n    var interpolateColorTs5 = function (value, inputRange, colors, _options) {\n      var l = (0, _interpolation.interpolate)(value, inputRange, colors.l, _interpolation.Extrapolation.CLAMP);\n      var a = (0, _interpolation.interpolate)(value, inputRange, colors.a, _interpolation.Extrapolation.CLAMP);\n      var b = (0, _interpolation.interpolate)(value, inputRange, colors.b, _interpolation.Extrapolation.CLAMP);\n      var alpha = (0, _interpolation.interpolate)(value, inputRange, colors.alpha, _interpolation.Extrapolation.CLAMP);\n      var _culori$oklab$convert = _culori.default.oklab.convert.toRgb({\n          l,\n          a,\n          b,\n          alpha\n        }),\n        _r = _culori$oklab$convert.r,\n        _g = _culori$oklab$convert.g,\n        _b = _culori$oklab$convert.b,\n        _alpha = _culori$oklab$convert.alpha;\n      return (0, _Colors.rgbaColor)(_r, _g, _b, _alpha);\n    };\n    interpolateColorTs5.__closure = {\n      interpolate: _interpolation.interpolate,\n      Extrapolation: _interpolation.Extrapolation,\n      culori: _culori.default,\n      rgbaColor: _Colors.rgbaColor\n    };\n    interpolateColorTs5.__workletHash = 12726126991717;\n    interpolateColorTs5.__initData = _worklet_12726126991717_init_data;\n    interpolateColorTs5.__stackDetails = _e;\n    return interpolateColorTs5;\n  }();\n  var _worklet_4376090521074_init_data = {\n    code: \"function interpolateColorTs6(colors,convFromRgb){const{processColor,red,green,blue,opacity}=this.__closure;const ch1=[];const ch2=[];const ch3=[];const alpha=[];for(let i=0;i<colors.length;i++){const color=colors[i];const processedColor=processColor(color);if(typeof processedColor==='number'){const convertedColor=convFromRgb({r:red(processedColor),g:green(processedColor),b:blue(processedColor)});ch1.push(convertedColor.ch1);ch2.push(convertedColor.ch2);ch3.push(convertedColor.ch3);alpha.push(opacity(processedColor));}}return{ch1:ch1,ch2:ch2,ch3:ch3,alpha:alpha};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolateColor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolateColorTs6\\\",\\\"colors\\\",\\\"convFromRgb\\\",\\\"processColor\\\",\\\"red\\\",\\\"green\\\",\\\"blue\\\",\\\"opacity\\\",\\\"__closure\\\",\\\"ch1\\\",\\\"ch2\\\",\\\"ch3\\\",\\\"alpha\\\",\\\"i\\\",\\\"length\\\",\\\"color\\\",\\\"processedColor\\\",\\\"convertedColor\\\",\\\"r\\\",\\\"g\\\",\\\"b\\\",\\\"push\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolateColor.ts\\\"],\\\"mappings\\\":\\\"AAsJiC,QAC/B,CAAAA,mBACAA,CAAAC,MAUG,CAAAC,WAAA,QAAAC,YAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,OAAA,OAAAC,SAAA,CAEH,KAAM,CAAAC,GAAa,CAAG,EAAE,CACxB,KAAM,CAAAC,GAAa,CAAG,EAAE,CACxB,KAAM,CAAAC,GAAa,CAAG,EAAE,CACxB,KAAM,CAAAC,KAAe,CAAG,EAAE,CAE1B,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGZ,MAAM,CAACa,MAAM,CAAED,CAAC,EAAE,CAAE,CACtC,KAAM,CAAAE,KAAK,CAAGd,MAAM,CAACY,CAAC,CAAC,CACvB,KAAM,CAAAG,cAAc,CAAGb,YAAY,CAACY,KAAK,CAAC,CAC1C,GAAI,MAAO,CAAAC,cAAc,GAAK,QAAQ,CAAE,CACtC,KAAM,CAAAC,cAAc,CAAGf,WAAW,CAAC,CACjCgB,CAAC,CAAEd,GAAG,CAACY,cAAc,CAAC,CACtBG,CAAC,CAAEd,KAAK,CAACW,cAAc,CAAC,CACxBI,CAAC,CAAEd,IAAI,CAACU,cAAc,CACxB,CAAC,CAAC,CAEFP,GAAG,CAACY,IAAI,CAACJ,cAAc,CAACR,GAAG,CAAC,CAC5BC,GAAG,CAACW,IAAI,CAACJ,cAAc,CAACP,GAAG,CAAC,CAC5BC,GAAG,CAACU,IAAI,CAACJ,cAAc,CAACN,GAAG,CAAC,CAC5BC,KAAK,CAACS,IAAI,CAACd,OAAO,CAACS,cAAc,CAAC,CAAC,CACrC,CACF,CAEA,MAAO,CACLP,GAAG,CAAHA,GAAG,CACHC,GAAG,CAAHA,GAAG,CACHC,GAAG,CAAHA,GAAG,CACHC,KAAA,CAAAA,KACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _splitColorsIntoChannels = function () {\n    var _e = [new global.Error(), -6, -27];\n    var interpolateColorTs6 = function (colors, convFromRgb) {\n      var ch1 = [];\n      var ch2 = [];\n      var ch3 = [];\n      var alpha = [];\n      for (var i = 0; i < colors.length; i++) {\n        var color = colors[i];\n        var processedColor = (0, _Colors.processColor)(color);\n        if (typeof processedColor === 'number') {\n          var convertedColor = convFromRgb({\n            r: (0, _Colors.red)(processedColor),\n            g: (0, _Colors.green)(processedColor),\n            b: (0, _Colors.blue)(processedColor)\n          });\n          ch1.push(convertedColor.ch1);\n          ch2.push(convertedColor.ch2);\n          ch3.push(convertedColor.ch3);\n          alpha.push((0, _Colors.opacity)(processedColor));\n        }\n      }\n      return {\n        ch1,\n        ch2,\n        ch3,\n        alpha\n      };\n    };\n    interpolateColorTs6.__closure = {\n      processColor: _Colors.processColor,\n      red: _Colors.red,\n      green: _Colors.green,\n      blue: _Colors.blue,\n      opacity: _Colors.opacity\n    };\n    interpolateColorTs6.__workletHash = 4376090521074;\n    interpolateColorTs6.__initData = _worklet_4376090521074_init_data;\n    interpolateColorTs6.__stackDetails = _e;\n    return interpolateColorTs6;\n  }();\n  var _worklet_17110655634904_init_data = {\n    code: \"function interpolateColorTs7(colors){const{_splitColorsIntoChannels}=this.__closure;const{ch1:ch1,ch2:ch2,ch3:ch3,alpha:alpha}=_splitColorsIntoChannels(colors,function(color){return{ch1:color.r,ch2:color.g,ch3:color.b};});return{r:ch1,g:ch2,b:ch3,a:alpha};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolateColor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolateColorTs7\\\",\\\"colors\\\",\\\"_splitColorsIntoChannels\\\",\\\"__closure\\\",\\\"ch1\\\",\\\"ch2\\\",\\\"ch3\\\",\\\"alpha\\\",\\\"color\\\",\\\"r\\\",\\\"g\\\",\\\"b\\\",\\\"a\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolateColor.ts\\\"],\\\"mappings\\\":\\\"AAyM0B,QACxB,CAAAA,mBACmBA,CAAAC,MAAA,QAAAC,wBAAA,OAAAC,SAAA,CAEnB,KAAM,CAAEC,GAAG,CAAHA,GAAG,CAAEC,GAAG,CAAHA,GAAG,CAAEC,GAAG,CAAHA,GAAG,CAAEC,KAAA,CAAAA,KAAM,CAAC,CAAGL,wBAAwB,CACvDD,MAAM,CACL,SAAAO,KAAK,QAAM,CACVJ,GAAG,CAAEI,KAAK,CAACC,CAAC,CACZJ,GAAG,CAAEG,KAAK,CAACE,CAAC,CACZJ,GAAG,CAAEE,KAAK,CAACG,CACb,CAAC,EACH,CAAC,CAED,MAAO,CACLF,CAAC,CAAEL,GAAG,CACNM,CAAC,CAAEL,GAAG,CACNM,CAAC,CAAEL,GAAG,CACNM,CAAC,CAAEL,KACL,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var getInterpolateRGB = function () {\n    var _e = [new global.Error(), -2, -27];\n    var interpolateColorTs7 = function (colors) {\n      var _splitColorsIntoChann = _splitColorsIntoChannels(colors, color => ({\n          ch1: color.r,\n          ch2: color.g,\n          ch3: color.b\n        })),\n        ch1 = _splitColorsIntoChann.ch1,\n        ch2 = _splitColorsIntoChann.ch2,\n        ch3 = _splitColorsIntoChann.ch3,\n        alpha = _splitColorsIntoChann.alpha;\n      return {\n        r: ch1,\n        g: ch2,\n        b: ch3,\n        a: alpha\n      };\n    };\n    interpolateColorTs7.__closure = {\n      _splitColorsIntoChannels\n    };\n    interpolateColorTs7.__workletHash = 17110655634904;\n    interpolateColorTs7.__initData = _worklet_17110655634904_init_data;\n    interpolateColorTs7.__stackDetails = _e;\n    return interpolateColorTs7;\n  }();\n  var _worklet_142566816320_init_data = {\n    code: \"function interpolateColorTs8(colors){const{_splitColorsIntoChannels,RGBtoHSV}=this.__closure;const{ch1:ch1,ch2:ch2,ch3:ch3,alpha:alpha}=_splitColorsIntoChannels(colors,function(color){const hsvColor=RGBtoHSV(color.r,color.g,color.b);return{ch1:hsvColor.h,ch2:hsvColor.s,ch3:hsvColor.v};});return{h:ch1,s:ch2,v:ch3,a:alpha};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolateColor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolateColorTs8\\\",\\\"colors\\\",\\\"_splitColorsIntoChannels\\\",\\\"RGBtoHSV\\\",\\\"__closure\\\",\\\"ch1\\\",\\\"ch2\\\",\\\"ch3\\\",\\\"alpha\\\",\\\"color\\\",\\\"hsvColor\\\",\\\"r\\\",\\\"g\\\",\\\"b\\\",\\\"h\\\",\\\"s\\\",\\\"v\\\",\\\"a\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolateColor.ts\\\"],\\\"mappings\\\":\\\"AAqO0B,QACxB,CAAAA,mBACmBA,CAAAC,MAAA,QAAAC,wBAAA,CAAAC,QAAA,OAAAC,SAAA,CAEnB,KAAM,CAAEC,GAAG,CAAHA,GAAG,CAAEC,GAAG,CAAHA,GAAG,CAAEC,GAAG,CAAHA,GAAG,CAAEC,KAAA,CAAAA,KAAM,CAAC,CAAGN,wBAAwB,CAACD,MAAM,CAAG,SAAAQ,KAAK,CAAK,CAC3E,KAAM,CAAAC,QAAQ,CAAGP,QAAQ,CAACM,KAAK,CAACE,CAAC,CAAEF,KAAK,CAACG,CAAC,CAAEH,KAAK,CAACI,CAAC,CAAC,CACpD,MAAO,CACLR,GAAG,CAAEK,QAAQ,CAACI,CAAC,CACfR,GAAG,CAAEI,QAAQ,CAACK,CAAC,CACfR,GAAG,CAAEG,QAAQ,CAACM,CAChB,CAAC,CACH,CAAC,CAAC,CAEF,MAAO,CACLF,CAAC,CAAET,GAAG,CACNU,CAAC,CAAET,GAAG,CACNU,CAAC,CAAET,GAAG,CACNU,CAAC,CAAET,KACL,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var getInterpolateHSV = function () {\n    var _e = [new global.Error(), -3, -27];\n    var interpolateColorTs8 = function (colors) {\n      var _splitColorsIntoChann2 = _splitColorsIntoChannels(colors, color => {\n          var hsvColor = (0, _Colors.RGBtoHSV)(color.r, color.g, color.b);\n          return {\n            ch1: hsvColor.h,\n            ch2: hsvColor.s,\n            ch3: hsvColor.v\n          };\n        }),\n        ch1 = _splitColorsIntoChann2.ch1,\n        ch2 = _splitColorsIntoChann2.ch2,\n        ch3 = _splitColorsIntoChann2.ch3,\n        alpha = _splitColorsIntoChann2.alpha;\n      return {\n        h: ch1,\n        s: ch2,\n        v: ch3,\n        a: alpha\n      };\n    };\n    interpolateColorTs8.__closure = {\n      _splitColorsIntoChannels,\n      RGBtoHSV: _Colors.RGBtoHSV\n    };\n    interpolateColorTs8.__workletHash = 142566816320;\n    interpolateColorTs8.__initData = _worklet_142566816320_init_data;\n    interpolateColorTs8.__stackDetails = _e;\n    return interpolateColorTs8;\n  }();\n  var _worklet_12286367320350_init_data = {\n    code: \"function interpolateColorTs9(colors){const{_splitColorsIntoChannels,culori}=this.__closure;const{ch1:ch1,ch2:ch2,ch3:ch3,alpha:alpha}=_splitColorsIntoChannels(colors,function(color){const labColor=culori.oklab.convert.fromRgb(color);return{ch1:labColor.l,ch2:labColor.a,ch3:labColor.b};});return{l:ch1,a:ch2,b:ch3,alpha:alpha};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolateColor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolateColorTs9\\\",\\\"colors\\\",\\\"_splitColorsIntoChannels\\\",\\\"culori\\\",\\\"__closure\\\",\\\"ch1\\\",\\\"ch2\\\",\\\"ch3\\\",\\\"alpha\\\",\\\"color\\\",\\\"labColor\\\",\\\"oklab\\\",\\\"convert\\\",\\\"fromRgb\\\",\\\"l\\\",\\\"a\\\",\\\"b\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolateColor.ts\\\"],\\\"mappings\\\":\\\"AAiQ0B,QACxB,CAAAA,mBACmBA,CAAAC,MAAA,QAAAC,wBAAA,CAAAC,MAAA,OAAAC,SAAA,CAGnB,KAAM,CAAEC,GAAG,CAAHA,GAAG,CAAEC,GAAG,CAAHA,GAAG,CAAEC,GAAG,CAAHA,GAAG,CAAEC,KAAA,CAAAA,KAAM,CAAC,CAAGN,wBAAwB,CAACD,MAAM,CAAG,SAAAQ,KAAK,CAAK,CAC3E,KAAM,CAAAC,QAAQ,CAAGP,MAAM,CAACQ,KAAK,CAACC,OAAO,CAACC,OAAO,CAACJ,KAAK,CAAC,CACpD,MAAO,CACLJ,GAAG,CAAEK,QAAQ,CAACI,CAAC,CACfR,GAAG,CAAEI,QAAQ,CAACK,CAAC,CACfR,GAAG,CAAEG,QAAQ,CAACM,CAChB,CAAC,CACH,CAAC,CAAC,CAEF,MAAO,CACLF,CAAC,CAAET,GAAG,CACNU,CAAC,CAAET,GAAG,CACNU,CAAC,CAAET,GAAG,CACNC,KAAA,CAAAA,KACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var getInterpolateLAB = function () {\n    var _e = [new global.Error(), -3, -27];\n    var interpolateColorTs9 = function (colors) {\n      var _splitColorsIntoChann3 = _splitColorsIntoChannels(colors, color => {\n          var labColor = _culori.default.oklab.convert.fromRgb(color);\n          return {\n            ch1: labColor.l,\n            ch2: labColor.a,\n            ch3: labColor.b\n          };\n        }),\n        ch1 = _splitColorsIntoChann3.ch1,\n        ch2 = _splitColorsIntoChann3.ch2,\n        ch3 = _splitColorsIntoChann3.ch3,\n        alpha = _splitColorsIntoChann3.alpha;\n      return {\n        l: ch1,\n        a: ch2,\n        b: ch3,\n        alpha\n      };\n    };\n    interpolateColorTs9.__closure = {\n      _splitColorsIntoChannels,\n      culori: _culori.default\n    };\n    interpolateColorTs9.__workletHash = 12286367320350;\n    interpolateColorTs9.__initData = _worklet_12286367320350_init_data;\n    interpolateColorTs9.__stackDetails = _e;\n    return interpolateColorTs9;\n  }();\n\n  /**\n   * Lets you map a value from a range of numbers to a range of colors using\n   * linear interpolation.\n   *\n   * @param value - A number from the `input` range that is going to be mapped to\n   *   the color in the `output` range.\n   * @param inputRange - An array of numbers specifying the input range of the\n   *   interpolation.\n   * @param outputRange - An array of output colors values (eg. \"red\", \"#00FFCC\",\n   *   \"rgba(255, 0, 0, 0.5)\").\n   * @param colorSpace - The color space to use for interpolation. Defaults to\n   *   'RGB'.\n   * @param options - Additional options for interpolation -\n   *   {@link InterpolationOptions}.\n   * @returns The color after interpolation from within the output range in\n   *   rgba(r, g, b, a) format.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/utilities/interpolateColor\n   */\n  var _worklet_8363465133138_init_data = {\n    code: \"function interpolateColor_interpolateColorTs10(value,inputRange,outputRange,colorSpace='RGB',options={}){const{interpolateColorsHSV,getInterpolateHSV,interpolateColorsRGB,getInterpolateRGB,interpolateColorsLAB,getInterpolateLAB}=this.__closure;if(colorSpace==='HSV'){return interpolateColorsHSV(value,inputRange,getInterpolateHSV(outputRange),options);}else if(colorSpace==='RGB'){return interpolateColorsRGB(value,inputRange,getInterpolateRGB(outputRange),options);}else if(colorSpace==='LAB'){return interpolateColorsLAB(value,inputRange,getInterpolateLAB(outputRange),options);}throw new ReanimatedError(\\\"Invalid color space provided: \\\"+colorSpace+\\\". Supported values are: ['RGB', 'HSV', 'LAB'].\\\");}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\interpolateColor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolateColor_interpolateColorTs10\\\",\\\"value\\\",\\\"inputRange\\\",\\\"outputRange\\\",\\\"colorSpace\\\",\\\"options\\\",\\\"interpolateColorsHSV\\\",\\\"getInterpolateHSV\\\",\\\"interpolateColorsRGB\\\",\\\"getInterpolateRGB\\\",\\\"interpolateColorsLAB\\\",\\\"getInterpolateLAB\\\",\\\"__closure\\\",\\\"ReanimatedError\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/interpolateColor.ts\\\"],\\\"mappings\\\":\\\"AAyTO,SAAAA,qCAGLA,CAAAC,KAAA,CAAAC,UACA,CAAAC,WAAyC,CACzCC,UAAiC,CAClC,KAAkB,CAAAC,OAAA,WAAAC,oBAAA,CAAAC,iBAAA,CAAAC,oBAAA,CAAAC,iBAAA,CAAAC,oBAAA,CAAAC,iBAAA,OAAAC,SAAA,CAEjB,GAAIR,UAAU,GAAK,KAAK,CAAE,CACxB,MAAO,CAAAE,oBAAoB,CACzBL,KAAK,CACLC,UAAU,CACVK,iBAAiB,CAACJ,WAAW,CAAC,CAC9BE,OACF,CAAC,CACH,CAAC,IAAM,IAAID,UAAU,GAAK,KAAK,CAAE,CAC/B,MAAO,CAAAI,oBAAoB,CACzBP,KAAK,CACLC,UAAU,CACVO,iBAAiB,CAACN,WAAW,CAAC,CAC9BE,OACF,CAAC,CACH,CAAC,IAAM,IAAID,UAAU,GAAK,KAAK,CAAE,CAC/B,MAAO,CAAAM,oBAAoB,CACzBT,KAAK,CACLC,UAAU,CACVS,iBAAiB,CAACR,WAAW,CAAC,CAC9BE,OACF,CAAC,CACH,CAEA,KAAM,IAAI,CAAAQ,eAAe,kCAErBT,UAAU,iDAEd,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var interpolateColor = exports.interpolateColor = function () {\n    var _e = [new global.Error(), -7, -27];\n    var interpolateColor = function (value, inputRange, outputRange) {\n      var colorSpace = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'RGB';\n      var options = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {};\n      if (colorSpace === 'HSV') {\n        return interpolateColorsHSV(value, inputRange, getInterpolateHSV(outputRange), options);\n      } else if (colorSpace === 'RGB') {\n        return interpolateColorsRGB(value, inputRange, getInterpolateRGB(outputRange), options);\n      } else if (colorSpace === 'LAB') {\n        return interpolateColorsLAB(value, inputRange, getInterpolateLAB(outputRange), options);\n      }\n      throw new _errors.ReanimatedError(`Invalid color space provided: ${colorSpace}. Supported values are: ['RGB', 'HSV', 'LAB'].`);\n    };\n    interpolateColor.__closure = {\n      interpolateColorsHSV,\n      getInterpolateHSV,\n      interpolateColorsRGB,\n      getInterpolateRGB,\n      interpolateColorsLAB,\n      getInterpolateLAB\n    };\n    interpolateColor.__workletHash = 8363465133138;\n    interpolateColor.__initData = _worklet_8363465133138_init_data;\n    interpolateColor.__stackDetails = _e;\n    return interpolateColor;\n  }();\n  var ColorSpace = exports.ColorSpace = /*#__PURE__*/function (ColorSpace) {\n    ColorSpace[ColorSpace[\"RGB\"] = 0] = \"RGB\";\n    ColorSpace[ColorSpace[\"HSV\"] = 1] = \"HSV\";\n    ColorSpace[ColorSpace[\"LAB\"] = 2] = \"LAB\";\n    return ColorSpace;\n  }({});\n  function useInterpolateConfig(inputRange, outputRange) {\n    var colorSpace = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : ColorSpace.RGB;\n    var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    return (0, _useSharedValue.useSharedValue)({\n      inputRange,\n      outputRange,\n      colorSpace,\n      cache: (0, _core.makeMutable)(null),\n      options\n    });\n  }\n});", "lineCount": 415, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "interpolateColor"], [8, 26, 1, 13], [8, 29, 1, 13, "exports"], [8, 36, 1, 13], [8, 37, 1, 13, "Extrapolate"], [8, 48, 1, 13], [8, 51, 1, 13, "exports"], [8, 58, 1, 13], [8, 59, 1, 13, "ColorSpace"], [8, 69, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "useInterpolateConfig"], [9, 30, 1, 13], [9, 33, 1, 13, "useInterpolateConfig"], [9, 53, 1, 13], [10, 2, 2, 0], [10, 6, 2, 0, "_Colors"], [10, 13, 2, 0], [10, 16, 2, 0, "require"], [10, 23, 2, 0], [10, 24, 2, 0, "_dependencyMap"], [10, 38, 2, 0], [11, 2, 13, 0], [11, 6, 13, 0, "_core"], [11, 11, 13, 0], [11, 14, 13, 0, "require"], [11, 21, 13, 0], [11, 22, 13, 0, "_dependencyMap"], [11, 36, 13, 0], [12, 2, 14, 0], [12, 6, 14, 0, "_culori"], [12, 13, 14, 0], [12, 16, 14, 0, "_interopRequireDefault"], [12, 38, 14, 0], [12, 39, 14, 0, "require"], [12, 46, 14, 0], [12, 47, 14, 0, "_dependencyMap"], [12, 61, 14, 0], [13, 2, 15, 0], [13, 6, 15, 0, "_errors"], [13, 13, 15, 0], [13, 16, 15, 0, "require"], [13, 23, 15, 0], [13, 24, 15, 0, "_dependencyMap"], [13, 38, 15, 0], [14, 2, 16, 0], [14, 6, 16, 0, "_useSharedValue"], [14, 21, 16, 0], [14, 24, 16, 0, "require"], [14, 31, 16, 0], [14, 32, 16, 0, "_dependencyMap"], [14, 46, 16, 0], [15, 2, 17, 0], [15, 6, 17, 0, "_interpolation"], [15, 20, 17, 0], [15, 23, 17, 0, "require"], [15, 30, 17, 0], [15, 31, 17, 0, "_dependencyMap"], [15, 45, 17, 0], [16, 2, 19, 0], [17, 2, 20, 7], [17, 6, 20, 13, "Extrapolate"], [17, 17, 20, 24], [17, 20, 20, 24, "exports"], [17, 27, 20, 24], [17, 28, 20, 24, "Extrapolate"], [17, 39, 20, 24], [17, 42, 20, 27, "Extrapolation"], [17, 70, 20, 40], [19, 2, 22, 0], [20, 0, 23, 0], [21, 0, 24, 0], [22, 0, 25, 0], [23, 0, 26, 0], [24, 0, 27, 0], [25, 0, 28, 0], [26, 2, 22, 0], [26, 6, 22, 0, "_worklet_12089101195034_init_data"], [26, 39, 22, 0], [27, 4, 22, 0, "code"], [27, 8, 22, 0], [28, 4, 22, 0, "location"], [28, 12, 22, 0], [29, 4, 22, 0, "sourceMap"], [29, 13, 22, 0], [30, 4, 22, 0, "version"], [30, 11, 22, 0], [31, 2, 22, 0], [32, 2, 34, 0], [32, 6, 34, 6, "interpolateColorsHSV"], [32, 26, 34, 26], [32, 29, 34, 29], [33, 4, 34, 29], [33, 8, 34, 29, "_e"], [33, 10, 34, 29], [33, 18, 34, 29, "global"], [33, 24, 34, 29], [33, 25, 34, 29, "Error"], [33, 30, 34, 29], [34, 4, 34, 29], [34, 8, 34, 29, "interpolateColorTs1"], [34, 27, 34, 29], [34, 39, 34, 29, "interpolateColorTs1"], [34, 40, 35, 2, "value"], [34, 45, 35, 15], [34, 47, 36, 2, "inputRange"], [34, 57, 36, 31], [34, 59, 37, 2, "colors"], [34, 65, 37, 24], [34, 67, 38, 2, "options"], [34, 74, 38, 31], [34, 76, 39, 5], [35, 6, 41, 2], [35, 10, 41, 6, "h"], [35, 11, 41, 7], [35, 14, 41, 10], [35, 15, 41, 11], [36, 6, 42, 2], [36, 10, 42, 2, "_options$useCorrected"], [36, 31, 42, 2], [36, 34, 42, 50, "options"], [36, 41, 42, 57], [36, 42, 42, 10, "useCorrectedHSVInterpolation"], [36, 70, 42, 38], [37, 8, 42, 10, "useCorrectedHSVInterpolation"], [37, 36, 42, 38], [37, 39, 42, 38, "_options$useCorrected"], [37, 60, 42, 38], [37, 74, 42, 41], [37, 78, 42, 45], [37, 81, 42, 45, "_options$useCorrected"], [37, 102, 42, 45], [38, 6, 43, 2], [38, 10, 43, 6, "useCorrectedHSVInterpolation"], [38, 38, 43, 34], [38, 40, 43, 36], [39, 8, 44, 4], [40, 8, 45, 4], [41, 8, 46, 4], [42, 8, 47, 4], [43, 8, 48, 4], [43, 12, 48, 10, "correctedInputRange"], [43, 31, 48, 29], [43, 34, 48, 32], [43, 35, 48, 33, "inputRange"], [43, 45, 48, 43], [43, 46, 48, 44], [43, 47, 48, 45], [43, 48, 48, 46], [43, 49, 48, 47], [44, 8, 49, 4], [44, 12, 49, 10, "originalH"], [44, 21, 49, 19], [44, 24, 49, 22, "colors"], [44, 30, 49, 28], [44, 31, 49, 29, "h"], [44, 32, 49, 30], [45, 8, 50, 4], [45, 12, 50, 10, "correctedH"], [45, 22, 50, 20], [45, 25, 50, 23], [45, 26, 50, 24, "originalH"], [45, 35, 50, 33], [45, 36, 50, 34], [45, 37, 50, 35], [45, 38, 50, 36], [45, 39, 50, 37], [46, 8, 52, 4], [46, 13, 52, 9], [46, 17, 52, 13, "i"], [46, 18, 52, 14], [46, 21, 52, 17], [46, 22, 52, 18], [46, 24, 52, 20, "i"], [46, 25, 52, 21], [46, 28, 52, 24, "originalH"], [46, 37, 52, 33], [46, 38, 52, 34, "length"], [46, 44, 52, 40], [46, 46, 52, 42], [46, 48, 52, 44, "i"], [46, 49, 52, 45], [46, 51, 52, 47], [47, 10, 53, 6], [47, 14, 53, 12, "d"], [47, 15, 53, 13], [47, 18, 53, 16, "originalH"], [47, 27, 53, 25], [47, 28, 53, 26, "i"], [47, 29, 53, 27], [47, 30, 53, 28], [47, 33, 53, 31, "originalH"], [47, 42, 53, 40], [47, 43, 53, 41, "i"], [47, 44, 53, 42], [47, 47, 53, 45], [47, 48, 53, 46], [47, 49, 53, 47], [48, 10, 54, 6], [48, 14, 54, 10, "originalH"], [48, 23, 54, 19], [48, 24, 54, 20, "i"], [48, 25, 54, 21], [48, 26, 54, 22], [48, 29, 54, 25, "originalH"], [48, 38, 54, 34], [48, 39, 54, 35, "i"], [48, 40, 54, 36], [48, 43, 54, 39], [48, 44, 54, 40], [48, 45, 54, 41], [48, 49, 54, 45, "d"], [48, 50, 54, 46], [48, 53, 54, 49], [48, 56, 54, 52], [48, 58, 54, 54], [49, 12, 55, 8, "correctedInputRange"], [49, 31, 55, 27], [49, 32, 55, 28, "push"], [49, 36, 55, 32], [49, 37, 55, 33, "inputRange"], [49, 47, 55, 43], [49, 48, 55, 44, "i"], [49, 49, 55, 45], [49, 50, 55, 46], [49, 51, 55, 47], [50, 12, 56, 8, "correctedInputRange"], [50, 31, 56, 27], [50, 32, 56, 28, "push"], [50, 36, 56, 32], [50, 37, 56, 33, "inputRange"], [50, 47, 56, 43], [50, 48, 56, 44, "i"], [50, 49, 56, 45], [50, 50, 56, 46], [50, 53, 56, 49], [50, 60, 56, 56], [50, 61, 56, 57], [51, 12, 57, 8, "correctedH"], [51, 22, 57, 18], [51, 23, 57, 19, "push"], [51, 27, 57, 23], [51, 28, 57, 24, "originalH"], [51, 37, 57, 33], [51, 38, 57, 34, "i"], [51, 39, 57, 35], [51, 40, 57, 36], [51, 43, 57, 39], [51, 44, 57, 40], [51, 45, 57, 41], [52, 12, 58, 8, "correctedH"], [52, 22, 58, 18], [52, 23, 58, 19, "push"], [52, 27, 58, 23], [52, 28, 58, 24, "originalH"], [52, 37, 58, 33], [52, 38, 58, 34, "i"], [52, 39, 58, 35], [52, 40, 58, 36], [52, 41, 58, 37], [53, 10, 59, 6], [53, 11, 59, 7], [53, 17, 59, 13], [53, 21, 59, 17, "originalH"], [53, 30, 59, 26], [53, 31, 59, 27, "i"], [53, 32, 59, 28], [53, 33, 59, 29], [53, 36, 59, 32, "originalH"], [53, 45, 59, 41], [53, 46, 59, 42, "i"], [53, 47, 59, 43], [53, 50, 59, 46], [53, 51, 59, 47], [53, 52, 59, 48], [53, 56, 59, 52, "d"], [53, 57, 59, 53], [53, 60, 59, 56], [53, 61, 59, 57], [53, 64, 59, 60], [53, 66, 59, 62], [54, 12, 60, 8, "correctedInputRange"], [54, 31, 60, 27], [54, 32, 60, 28, "push"], [54, 36, 60, 32], [54, 37, 60, 33, "inputRange"], [54, 47, 60, 43], [54, 48, 60, 44, "i"], [54, 49, 60, 45], [54, 50, 60, 46], [54, 51, 60, 47], [55, 12, 61, 8, "correctedInputRange"], [55, 31, 61, 27], [55, 32, 61, 28, "push"], [55, 36, 61, 32], [55, 37, 61, 33, "inputRange"], [55, 47, 61, 43], [55, 48, 61, 44, "i"], [55, 49, 61, 45], [55, 50, 61, 46], [55, 53, 61, 49], [55, 60, 61, 56], [55, 61, 61, 57], [56, 12, 62, 8, "correctedH"], [56, 22, 62, 18], [56, 23, 62, 19, "push"], [56, 27, 62, 23], [56, 28, 62, 24, "originalH"], [56, 37, 62, 33], [56, 38, 62, 34, "i"], [56, 39, 62, 35], [56, 40, 62, 36], [56, 43, 62, 39], [56, 44, 62, 40], [56, 45, 62, 41], [57, 12, 63, 8, "correctedH"], [57, 22, 63, 18], [57, 23, 63, 19, "push"], [57, 27, 63, 23], [57, 28, 63, 24, "originalH"], [57, 37, 63, 33], [57, 38, 63, 34, "i"], [57, 39, 63, 35], [57, 40, 63, 36], [57, 41, 63, 37], [58, 10, 64, 6], [58, 11, 64, 7], [58, 17, 64, 13], [59, 12, 65, 8, "correctedInputRange"], [59, 31, 65, 27], [59, 32, 65, 28, "push"], [59, 36, 65, 32], [59, 37, 65, 33, "inputRange"], [59, 47, 65, 43], [59, 48, 65, 44, "i"], [59, 49, 65, 45], [59, 50, 65, 46], [59, 51, 65, 47], [60, 12, 66, 8, "correctedH"], [60, 22, 66, 18], [60, 23, 66, 19, "push"], [60, 27, 66, 23], [60, 28, 66, 24, "originalH"], [60, 37, 66, 33], [60, 38, 66, 34, "i"], [60, 39, 66, 35], [60, 40, 66, 36], [60, 41, 66, 37], [61, 10, 67, 6], [62, 8, 68, 4], [63, 8, 69, 4, "h"], [63, 9, 69, 5], [63, 12, 70, 6], [63, 13, 70, 7], [63, 17, 70, 7, "interpolate"], [63, 43, 70, 18], [63, 45, 71, 8, "value"], [63, 50, 71, 13], [63, 52, 72, 8, "correctedInputRange"], [63, 71, 72, 27], [63, 73, 73, 8, "correctedH"], [63, 83, 73, 18], [63, 85, 74, 8, "Extrapolation"], [63, 113, 74, 21], [63, 114, 74, 22, "CLAMP"], [63, 119, 75, 6], [63, 120, 75, 7], [63, 123, 76, 8], [63, 124, 76, 9], [63, 128, 77, 6], [63, 129, 77, 7], [64, 6, 78, 2], [64, 7, 78, 3], [64, 13, 78, 9], [65, 8, 79, 4, "h"], [65, 9, 79, 5], [65, 12, 79, 8], [65, 16, 79, 8, "interpolate"], [65, 42, 79, 19], [65, 44, 79, 20, "value"], [65, 49, 79, 25], [65, 51, 79, 27, "inputRange"], [65, 61, 79, 37], [65, 63, 79, 39, "colors"], [65, 69, 79, 45], [65, 70, 79, 46, "h"], [65, 71, 79, 47], [65, 73, 79, 49, "Extrapolation"], [65, 101, 79, 62], [65, 102, 79, 63, "CLAMP"], [65, 107, 79, 68], [65, 108, 79, 69], [66, 6, 80, 2], [67, 6, 81, 2], [67, 10, 81, 8, "s"], [67, 11, 81, 9], [67, 14, 81, 12], [67, 18, 81, 12, "interpolate"], [67, 44, 81, 23], [67, 46, 81, 24, "value"], [67, 51, 81, 29], [67, 53, 81, 31, "inputRange"], [67, 63, 81, 41], [67, 65, 81, 43, "colors"], [67, 71, 81, 49], [67, 72, 81, 50, "s"], [67, 73, 81, 51], [67, 75, 81, 53, "Extrapolation"], [67, 103, 81, 66], [67, 104, 81, 67, "CLAMP"], [67, 109, 81, 72], [67, 110, 81, 73], [68, 6, 82, 2], [68, 10, 82, 8, "v"], [68, 11, 82, 9], [68, 14, 82, 12], [68, 18, 82, 12, "interpolate"], [68, 44, 82, 23], [68, 46, 82, 24, "value"], [68, 51, 82, 29], [68, 53, 82, 31, "inputRange"], [68, 63, 82, 41], [68, 65, 82, 43, "colors"], [68, 71, 82, 49], [68, 72, 82, 50, "v"], [68, 73, 82, 51], [68, 75, 82, 53, "Extrapolation"], [68, 103, 82, 66], [68, 104, 82, 67, "CLAMP"], [68, 109, 82, 72], [68, 110, 82, 73], [69, 6, 83, 2], [69, 10, 83, 8, "a"], [69, 11, 83, 9], [69, 14, 83, 12], [69, 18, 83, 12, "interpolate"], [69, 44, 83, 23], [69, 46, 83, 24, "value"], [69, 51, 83, 29], [69, 53, 83, 31, "inputRange"], [69, 63, 83, 41], [69, 65, 83, 43, "colors"], [69, 71, 83, 49], [69, 72, 83, 50, "a"], [69, 73, 83, 51], [69, 75, 83, 53, "Extrapolation"], [69, 103, 83, 66], [69, 104, 83, 67, "CLAMP"], [69, 109, 83, 72], [69, 110, 83, 73], [70, 6, 84, 2], [70, 13, 84, 9], [70, 17, 84, 9, "hsvToColor"], [70, 35, 84, 19], [70, 37, 84, 20, "h"], [70, 38, 84, 21], [70, 40, 84, 23, "s"], [70, 41, 84, 24], [70, 43, 84, 26, "v"], [70, 44, 84, 27], [70, 46, 84, 29, "a"], [70, 47, 84, 30], [70, 48, 84, 31], [71, 4, 85, 0], [71, 5, 85, 1], [72, 4, 85, 1, "interpolateColorTs1"], [72, 23, 85, 1], [72, 24, 85, 1, "__closure"], [72, 33, 85, 1], [73, 6, 85, 1, "interpolate"], [73, 17, 85, 1], [73, 19, 70, 7, "interpolate"], [73, 45, 70, 18], [74, 6, 70, 18, "Extrapolation"], [74, 19, 70, 18], [74, 21, 74, 8, "Extrapolation"], [74, 49, 74, 21], [75, 6, 74, 21, "hsvToColor"], [75, 16, 74, 21], [75, 18, 84, 9, "hsvToColor"], [76, 4, 84, 19], [77, 4, 84, 19, "interpolateColorTs1"], [77, 23, 84, 19], [77, 24, 84, 19, "__workletHash"], [77, 37, 84, 19], [78, 4, 84, 19, "interpolateColorTs1"], [78, 23, 84, 19], [78, 24, 84, 19, "__initData"], [78, 34, 84, 19], [78, 37, 84, 19, "_worklet_12089101195034_init_data"], [78, 70, 84, 19], [79, 4, 84, 19, "interpolateColorTs1"], [79, 23, 84, 19], [79, 24, 84, 19, "__stackDetails"], [79, 38, 84, 19], [79, 41, 84, 19, "_e"], [79, 43, 84, 19], [80, 4, 84, 19], [80, 11, 84, 19, "interpolateColorTs1"], [80, 30, 84, 19], [81, 2, 84, 19], [81, 3, 34, 29], [81, 5, 85, 1], [82, 2, 85, 2], [82, 6, 85, 2, "_worklet_12758569594929_init_data"], [82, 39, 85, 2], [83, 4, 85, 2, "code"], [83, 8, 85, 2], [84, 4, 85, 2, "location"], [84, 12, 85, 2], [85, 4, 85, 2, "sourceMap"], [85, 13, 85, 2], [86, 4, 85, 2, "version"], [86, 11, 85, 2], [87, 2, 85, 2], [88, 2, 87, 0], [88, 6, 87, 6, "toLinearSpace"], [88, 19, 87, 19], [88, 22, 87, 22], [89, 4, 87, 22], [89, 8, 87, 22, "_e"], [89, 10, 87, 22], [89, 18, 87, 22, "global"], [89, 24, 87, 22], [89, 25, 87, 22, "Error"], [89, 30, 87, 22], [90, 4, 87, 22], [90, 8, 87, 22, "interpolateColorTs2"], [90, 27, 87, 22], [90, 39, 87, 22, "interpolateColorTs2"], [90, 40, 87, 23, "x"], [90, 41, 87, 34], [90, 43, 87, 36, "gamma"], [90, 48, 87, 49], [90, 50, 87, 64], [91, 6, 89, 2], [91, 13, 89, 9, "x"], [91, 14, 89, 10], [91, 15, 89, 11, "map"], [91, 18, 89, 14], [91, 19, 89, 16, "v"], [91, 20, 89, 17], [91, 24, 89, 22, "Math"], [91, 28, 89, 26], [91, 29, 89, 27, "pow"], [91, 32, 89, 30], [91, 33, 89, 31, "v"], [91, 34, 89, 32], [91, 37, 89, 35], [91, 40, 89, 38], [91, 42, 89, 40, "gamma"], [91, 47, 89, 45], [91, 48, 89, 46], [91, 49, 89, 47], [92, 4, 90, 0], [92, 5, 90, 1], [93, 4, 90, 1, "interpolateColorTs2"], [93, 23, 90, 1], [93, 24, 90, 1, "__closure"], [93, 33, 90, 1], [94, 4, 90, 1, "interpolateColorTs2"], [94, 23, 90, 1], [94, 24, 90, 1, "__workletHash"], [94, 37, 90, 1], [95, 4, 90, 1, "interpolateColorTs2"], [95, 23, 90, 1], [95, 24, 90, 1, "__initData"], [95, 34, 90, 1], [95, 37, 90, 1, "_worklet_12758569594929_init_data"], [95, 70, 90, 1], [96, 4, 90, 1, "interpolateColorTs2"], [96, 23, 90, 1], [96, 24, 90, 1, "__stackDetails"], [96, 38, 90, 1], [96, 41, 90, 1, "_e"], [96, 43, 90, 1], [97, 4, 90, 1], [97, 11, 90, 1, "interpolateColorTs2"], [97, 30, 90, 1], [98, 2, 90, 1], [98, 3, 87, 22], [98, 5, 90, 1], [99, 2, 90, 2], [99, 6, 90, 2, "_worklet_16323081088977_init_data"], [99, 39, 90, 2], [100, 4, 90, 2, "code"], [100, 8, 90, 2], [101, 4, 90, 2, "location"], [101, 12, 90, 2], [102, 4, 90, 2, "sourceMap"], [102, 13, 90, 2], [103, 4, 90, 2, "version"], [103, 11, 90, 2], [104, 2, 90, 2], [105, 2, 92, 0], [105, 6, 92, 6, "toGammaSpace"], [105, 18, 92, 18], [105, 21, 92, 21], [106, 4, 92, 21], [106, 8, 92, 21, "_e"], [106, 10, 92, 21], [106, 18, 92, 21, "global"], [106, 24, 92, 21], [106, 25, 92, 21, "Error"], [106, 30, 92, 21], [107, 4, 92, 21], [107, 8, 92, 21, "interpolateColorTs3"], [107, 27, 92, 21], [107, 39, 92, 21, "interpolateColorTs3"], [107, 40, 92, 22, "x"], [107, 41, 92, 31], [107, 43, 92, 33, "gamma"], [107, 48, 92, 46], [107, 50, 92, 59], [108, 6, 94, 2], [108, 13, 94, 9, "Math"], [108, 17, 94, 13], [108, 18, 94, 14, "round"], [108, 23, 94, 19], [108, 24, 94, 20, "Math"], [108, 28, 94, 24], [108, 29, 94, 25, "pow"], [108, 32, 94, 28], [108, 33, 94, 29, "x"], [108, 34, 94, 30], [108, 36, 94, 32], [108, 37, 94, 33], [108, 40, 94, 36, "gamma"], [108, 45, 94, 41], [108, 46, 94, 42], [108, 49, 94, 45], [108, 52, 94, 48], [108, 53, 94, 49], [109, 4, 95, 0], [109, 5, 95, 1], [110, 4, 95, 1, "interpolateColorTs3"], [110, 23, 95, 1], [110, 24, 95, 1, "__closure"], [110, 33, 95, 1], [111, 4, 95, 1, "interpolateColorTs3"], [111, 23, 95, 1], [111, 24, 95, 1, "__workletHash"], [111, 37, 95, 1], [112, 4, 95, 1, "interpolateColorTs3"], [112, 23, 95, 1], [112, 24, 95, 1, "__initData"], [112, 34, 95, 1], [112, 37, 95, 1, "_worklet_16323081088977_init_data"], [112, 70, 95, 1], [113, 4, 95, 1, "interpolateColorTs3"], [113, 23, 95, 1], [113, 24, 95, 1, "__stackDetails"], [113, 38, 95, 1], [113, 41, 95, 1, "_e"], [113, 43, 95, 1], [114, 4, 95, 1], [114, 11, 95, 1, "interpolateColorTs3"], [114, 30, 95, 1], [115, 2, 95, 1], [115, 3, 92, 21], [115, 5, 95, 1], [116, 2, 95, 2], [116, 6, 95, 2, "_worklet_11009674977133_init_data"], [116, 39, 95, 2], [117, 4, 95, 2, "code"], [117, 8, 95, 2], [118, 4, 95, 2, "location"], [118, 12, 95, 2], [119, 4, 95, 2, "sourceMap"], [119, 13, 95, 2], [120, 4, 95, 2, "version"], [120, 11, 95, 2], [121, 2, 95, 2], [122, 2, 97, 0], [122, 6, 97, 6, "interpolateColorsRGB"], [122, 26, 97, 26], [122, 29, 97, 29], [123, 4, 97, 29], [123, 8, 97, 29, "_e"], [123, 10, 97, 29], [123, 18, 97, 29, "global"], [123, 24, 97, 29], [123, 25, 97, 29, "Error"], [123, 30, 97, 29], [124, 4, 97, 29], [124, 8, 97, 29, "interpolateColorTs4"], [124, 27, 97, 29], [124, 39, 97, 29, "interpolateColorTs4"], [124, 40, 98, 2, "value"], [124, 45, 98, 15], [124, 47, 99, 2, "inputRange"], [124, 57, 99, 31], [124, 59, 100, 2, "colors"], [124, 65, 100, 24], [124, 67, 101, 2, "options"], [124, 74, 101, 31], [124, 76, 102, 5], [125, 6, 104, 2], [125, 10, 104, 2, "_options$gamma"], [125, 24, 104, 2], [125, 27, 104, 26, "options"], [125, 34, 104, 33], [125, 35, 104, 10, "gamma"], [125, 40, 104, 15], [126, 8, 104, 10, "gamma"], [126, 13, 104, 15], [126, 16, 104, 15, "_options$gamma"], [126, 30, 104, 15], [126, 44, 104, 18], [126, 47, 104, 21], [126, 50, 104, 21, "_options$gamma"], [126, 64, 104, 21], [127, 6, 105, 2], [127, 10, 105, 11, "outputR"], [127, 17, 105, 18], [127, 20, 105, 47, "colors"], [127, 26, 105, 53], [127, 27, 105, 8, "r"], [127, 28, 105, 9], [128, 8, 105, 23, "outputG"], [128, 15, 105, 30], [128, 18, 105, 47, "colors"], [128, 24, 105, 53], [128, 25, 105, 20, "g"], [128, 26, 105, 21], [129, 8, 105, 35, "outputB"], [129, 15, 105, 42], [129, 18, 105, 47, "colors"], [129, 24, 105, 53], [129, 25, 105, 32, "b"], [129, 26, 105, 33], [130, 6, 106, 2], [130, 10, 106, 6, "gamma"], [130, 15, 106, 11], [130, 20, 106, 16], [130, 21, 106, 17], [130, 23, 106, 19], [131, 8, 107, 4, "outputR"], [131, 15, 107, 11], [131, 18, 107, 14, "toLinearSpace"], [131, 31, 107, 27], [131, 32, 107, 28, "outputR"], [131, 39, 107, 35], [131, 41, 107, 37, "gamma"], [131, 46, 107, 42], [131, 47, 107, 43], [132, 8, 108, 4, "outputG"], [132, 15, 108, 11], [132, 18, 108, 14, "toLinearSpace"], [132, 31, 108, 27], [132, 32, 108, 28, "outputG"], [132, 39, 108, 35], [132, 41, 108, 37, "gamma"], [132, 46, 108, 42], [132, 47, 108, 43], [133, 8, 109, 4, "outputB"], [133, 15, 109, 11], [133, 18, 109, 14, "toLinearSpace"], [133, 31, 109, 27], [133, 32, 109, 28, "outputB"], [133, 39, 109, 35], [133, 41, 109, 37, "gamma"], [133, 46, 109, 42], [133, 47, 109, 43], [134, 6, 110, 2], [135, 6, 111, 2], [135, 10, 111, 8, "r"], [135, 11, 111, 9], [135, 14, 111, 12], [135, 18, 111, 12, "interpolate"], [135, 44, 111, 23], [135, 46, 111, 24, "value"], [135, 51, 111, 29], [135, 53, 111, 31, "inputRange"], [135, 63, 111, 41], [135, 65, 111, 43, "outputR"], [135, 72, 111, 50], [135, 74, 111, 52, "Extrapolation"], [135, 102, 111, 65], [135, 103, 111, 66, "CLAMP"], [135, 108, 111, 71], [135, 109, 111, 72], [136, 6, 112, 2], [136, 10, 112, 8, "g"], [136, 11, 112, 9], [136, 14, 112, 12], [136, 18, 112, 12, "interpolate"], [136, 44, 112, 23], [136, 46, 112, 24, "value"], [136, 51, 112, 29], [136, 53, 112, 31, "inputRange"], [136, 63, 112, 41], [136, 65, 112, 43, "outputG"], [136, 72, 112, 50], [136, 74, 112, 52, "Extrapolation"], [136, 102, 112, 65], [136, 103, 112, 66, "CLAMP"], [136, 108, 112, 71], [136, 109, 112, 72], [137, 6, 113, 2], [137, 10, 113, 8, "b"], [137, 11, 113, 9], [137, 14, 113, 12], [137, 18, 113, 12, "interpolate"], [137, 44, 113, 23], [137, 46, 113, 24, "value"], [137, 51, 113, 29], [137, 53, 113, 31, "inputRange"], [137, 63, 113, 41], [137, 65, 113, 43, "outputB"], [137, 72, 113, 50], [137, 74, 113, 52, "Extrapolation"], [137, 102, 113, 65], [137, 103, 113, 66, "CLAMP"], [137, 108, 113, 71], [137, 109, 113, 72], [138, 6, 114, 2], [138, 10, 114, 8, "a"], [138, 11, 114, 9], [138, 14, 114, 12], [138, 18, 114, 12, "interpolate"], [138, 44, 114, 23], [138, 46, 114, 24, "value"], [138, 51, 114, 29], [138, 53, 114, 31, "inputRange"], [138, 63, 114, 41], [138, 65, 114, 43, "colors"], [138, 71, 114, 49], [138, 72, 114, 50, "a"], [138, 73, 114, 51], [138, 75, 114, 53, "Extrapolation"], [138, 103, 114, 66], [138, 104, 114, 67, "CLAMP"], [138, 109, 114, 72], [138, 110, 114, 73], [139, 6, 115, 2], [139, 10, 115, 6, "gamma"], [139, 15, 115, 11], [139, 20, 115, 16], [139, 21, 115, 17], [139, 23, 115, 19], [140, 8, 116, 4], [140, 15, 116, 11], [140, 19, 116, 11, "rgbaColor"], [140, 36, 116, 20], [140, 38, 116, 21, "r"], [140, 39, 116, 22], [140, 41, 116, 24, "g"], [140, 42, 116, 25], [140, 44, 116, 27, "b"], [140, 45, 116, 28], [140, 47, 116, 30, "a"], [140, 48, 116, 31], [140, 49, 116, 32], [141, 6, 117, 2], [142, 6, 118, 2], [142, 13, 118, 9], [142, 17, 118, 9, "rgbaColor"], [142, 34, 118, 18], [142, 36, 119, 4, "toGammaSpace"], [142, 48, 119, 16], [142, 49, 119, 17, "r"], [142, 50, 119, 18], [142, 52, 119, 20, "gamma"], [142, 57, 119, 25], [142, 58, 119, 26], [142, 60, 120, 4, "toGammaSpace"], [142, 72, 120, 16], [142, 73, 120, 17, "g"], [142, 74, 120, 18], [142, 76, 120, 20, "gamma"], [142, 81, 120, 25], [142, 82, 120, 26], [142, 84, 121, 4, "toGammaSpace"], [142, 96, 121, 16], [142, 97, 121, 17, "b"], [142, 98, 121, 18], [142, 100, 121, 20, "gamma"], [142, 105, 121, 25], [142, 106, 121, 26], [142, 108, 122, 4, "a"], [142, 109, 123, 2], [142, 110, 123, 3], [143, 4, 124, 0], [143, 5, 124, 1], [144, 4, 124, 1, "interpolateColorTs4"], [144, 23, 124, 1], [144, 24, 124, 1, "__closure"], [144, 33, 124, 1], [145, 6, 124, 1, "toLinearSpace"], [145, 19, 124, 1], [146, 6, 124, 1, "interpolate"], [146, 17, 124, 1], [146, 19, 111, 12, "interpolate"], [146, 45, 111, 23], [147, 6, 111, 23, "Extrapolation"], [147, 19, 111, 23], [147, 21, 111, 52, "Extrapolation"], [147, 49, 111, 65], [148, 6, 111, 65, "rgbaColor"], [148, 15, 111, 65], [148, 17, 116, 11, "rgbaColor"], [148, 34, 116, 20], [149, 6, 116, 20, "toGammaSpace"], [150, 4, 116, 20], [151, 4, 116, 20, "interpolateColorTs4"], [151, 23, 116, 20], [151, 24, 116, 20, "__workletHash"], [151, 37, 116, 20], [152, 4, 116, 20, "interpolateColorTs4"], [152, 23, 116, 20], [152, 24, 116, 20, "__initData"], [152, 34, 116, 20], [152, 37, 116, 20, "_worklet_11009674977133_init_data"], [152, 70, 116, 20], [153, 4, 116, 20, "interpolateColorTs4"], [153, 23, 116, 20], [153, 24, 116, 20, "__stackDetails"], [153, 38, 116, 20], [153, 41, 116, 20, "_e"], [153, 43, 116, 20], [154, 4, 116, 20], [154, 11, 116, 20, "interpolateColorTs4"], [154, 30, 116, 20], [155, 2, 116, 20], [155, 3, 97, 29], [155, 5, 124, 1], [156, 2, 124, 2], [156, 6, 124, 2, "_worklet_12726126991717_init_data"], [156, 39, 124, 2], [157, 4, 124, 2, "code"], [157, 8, 124, 2], [158, 4, 124, 2, "location"], [158, 12, 124, 2], [159, 4, 124, 2, "sourceMap"], [159, 13, 124, 2], [160, 4, 124, 2, "version"], [160, 11, 124, 2], [161, 2, 124, 2], [162, 2, 126, 0], [162, 6, 126, 6, "interpolateColorsLAB"], [162, 26, 126, 26], [162, 29, 126, 29], [163, 4, 126, 29], [163, 8, 126, 29, "_e"], [163, 10, 126, 29], [163, 18, 126, 29, "global"], [163, 24, 126, 29], [163, 25, 126, 29, "Error"], [163, 30, 126, 29], [164, 4, 126, 29], [164, 8, 126, 29, "interpolateColorTs5"], [164, 27, 126, 29], [164, 39, 126, 29, "interpolateColorTs5"], [164, 40, 127, 2, "value"], [164, 45, 127, 15], [164, 47, 128, 2, "inputRange"], [164, 57, 128, 31], [164, 59, 129, 2, "colors"], [164, 65, 129, 24], [164, 67, 130, 2, "_options"], [164, 75, 130, 32], [164, 77, 131, 5], [165, 6, 133, 2], [165, 10, 133, 8, "l"], [165, 11, 133, 9], [165, 14, 133, 12], [165, 18, 133, 12, "interpolate"], [165, 44, 133, 23], [165, 46, 133, 24, "value"], [165, 51, 133, 29], [165, 53, 133, 31, "inputRange"], [165, 63, 133, 41], [165, 65, 133, 43, "colors"], [165, 71, 133, 49], [165, 72, 133, 50, "l"], [165, 73, 133, 51], [165, 75, 133, 53, "Extrapolation"], [165, 103, 133, 66], [165, 104, 133, 67, "CLAMP"], [165, 109, 133, 72], [165, 110, 133, 73], [166, 6, 134, 2], [166, 10, 134, 8, "a"], [166, 11, 134, 9], [166, 14, 134, 12], [166, 18, 134, 12, "interpolate"], [166, 44, 134, 23], [166, 46, 134, 24, "value"], [166, 51, 134, 29], [166, 53, 134, 31, "inputRange"], [166, 63, 134, 41], [166, 65, 134, 43, "colors"], [166, 71, 134, 49], [166, 72, 134, 50, "a"], [166, 73, 134, 51], [166, 75, 134, 53, "Extrapolation"], [166, 103, 134, 66], [166, 104, 134, 67, "CLAMP"], [166, 109, 134, 72], [166, 110, 134, 73], [167, 6, 135, 2], [167, 10, 135, 8, "b"], [167, 11, 135, 9], [167, 14, 135, 12], [167, 18, 135, 12, "interpolate"], [167, 44, 135, 23], [167, 46, 135, 24, "value"], [167, 51, 135, 29], [167, 53, 135, 31, "inputRange"], [167, 63, 135, 41], [167, 65, 135, 43, "colors"], [167, 71, 135, 49], [167, 72, 135, 50, "b"], [167, 73, 135, 51], [167, 75, 135, 53, "Extrapolation"], [167, 103, 135, 66], [167, 104, 135, 67, "CLAMP"], [167, 109, 135, 72], [167, 110, 135, 73], [168, 6, 136, 2], [168, 10, 136, 8, "alpha"], [168, 15, 136, 13], [168, 18, 136, 16], [168, 22, 136, 16, "interpolate"], [168, 48, 136, 27], [168, 50, 137, 4, "value"], [168, 55, 137, 9], [168, 57, 138, 4, "inputRange"], [168, 67, 138, 14], [168, 69, 139, 4, "colors"], [168, 75, 139, 10], [168, 76, 139, 11, "alpha"], [168, 81, 139, 16], [168, 83, 140, 4, "Extrapolation"], [168, 111, 140, 17], [168, 112, 140, 18, "CLAMP"], [168, 117, 141, 2], [168, 118, 141, 3], [169, 6, 142, 2], [169, 10, 142, 2, "_culori$oklab$convert"], [169, 31, 142, 2], [169, 34, 147, 6, "culori"], [169, 49, 147, 12], [169, 50, 147, 13, "oklab"], [169, 55, 147, 18], [169, 56, 147, 19, "convert"], [169, 63, 147, 26], [169, 64, 147, 27, "toRgb"], [169, 69, 147, 32], [169, 70, 147, 33], [170, 10, 147, 35, "l"], [170, 11, 147, 36], [171, 10, 147, 38, "a"], [171, 11, 147, 39], [172, 10, 147, 41, "b"], [172, 11, 147, 42], [173, 10, 147, 44, "alpha"], [174, 8, 147, 50], [174, 9, 147, 51], [174, 10, 147, 52], [175, 8, 143, 7, "_r"], [175, 10, 143, 9], [175, 13, 143, 9, "_culori$oklab$convert"], [175, 34, 143, 9], [175, 35, 143, 4, "r"], [175, 36, 143, 5], [176, 8, 144, 7, "_g"], [176, 10, 144, 9], [176, 13, 144, 9, "_culori$oklab$convert"], [176, 34, 144, 9], [176, 35, 144, 4, "g"], [176, 36, 144, 5], [177, 8, 145, 7, "_b"], [177, 10, 145, 9], [177, 13, 145, 9, "_culori$oklab$convert"], [177, 34, 145, 9], [177, 35, 145, 4, "b"], [177, 36, 145, 5], [178, 8, 146, 11, "_alpha"], [178, 14, 146, 17], [178, 17, 146, 17, "_culori$oklab$convert"], [178, 38, 146, 17], [178, 39, 146, 4, "alpha"], [178, 44, 146, 9], [179, 6, 148, 2], [179, 13, 148, 9], [179, 17, 148, 9, "rgbaColor"], [179, 34, 148, 18], [179, 36, 148, 19, "_r"], [179, 38, 148, 21], [179, 40, 148, 23, "_g"], [179, 42, 148, 25], [179, 44, 148, 27, "_b"], [179, 46, 148, 29], [179, 48, 148, 31, "_alpha"], [179, 54, 148, 37], [179, 55, 148, 38], [180, 4, 149, 0], [180, 5, 149, 1], [181, 4, 149, 1, "interpolateColorTs5"], [181, 23, 149, 1], [181, 24, 149, 1, "__closure"], [181, 33, 149, 1], [182, 6, 149, 1, "interpolate"], [182, 17, 149, 1], [182, 19, 133, 12, "interpolate"], [182, 45, 133, 23], [183, 6, 133, 23, "Extrapolation"], [183, 19, 133, 23], [183, 21, 133, 53, "Extrapolation"], [183, 49, 133, 66], [184, 6, 133, 66, "culori"], [184, 12, 133, 66], [184, 14, 147, 6, "culori"], [184, 29, 147, 12], [185, 6, 147, 12, "rgbaColor"], [185, 15, 147, 12], [185, 17, 148, 9, "rgbaColor"], [186, 4, 148, 18], [187, 4, 148, 18, "interpolateColorTs5"], [187, 23, 148, 18], [187, 24, 148, 18, "__workletHash"], [187, 37, 148, 18], [188, 4, 148, 18, "interpolateColorTs5"], [188, 23, 148, 18], [188, 24, 148, 18, "__initData"], [188, 34, 148, 18], [188, 37, 148, 18, "_worklet_12726126991717_init_data"], [188, 70, 148, 18], [189, 4, 148, 18, "interpolateColorTs5"], [189, 23, 148, 18], [189, 24, 148, 18, "__stackDetails"], [189, 38, 148, 18], [189, 41, 148, 18, "_e"], [189, 43, 148, 18], [190, 4, 148, 18], [190, 11, 148, 18, "interpolateColorTs5"], [190, 30, 148, 18], [191, 2, 148, 18], [191, 3, 126, 29], [191, 5, 149, 1], [192, 2, 149, 2], [192, 6, 149, 2, "_worklet_4376090521074_init_data"], [192, 38, 149, 2], [193, 4, 149, 2, "code"], [193, 8, 149, 2], [194, 4, 149, 2, "location"], [194, 12, 149, 2], [195, 4, 149, 2, "sourceMap"], [195, 13, 149, 2], [196, 4, 149, 2, "version"], [196, 11, 149, 2], [197, 2, 149, 2], [198, 2, 151, 0], [198, 6, 151, 6, "_splitColorsIntoChannels"], [198, 30, 151, 30], [198, 33, 151, 33], [199, 4, 151, 33], [199, 8, 151, 33, "_e"], [199, 10, 151, 33], [199, 18, 151, 33, "global"], [199, 24, 151, 33], [199, 25, 151, 33, "Error"], [199, 30, 151, 33], [200, 4, 151, 33], [200, 8, 151, 33, "interpolateColorTs6"], [200, 27, 151, 33], [200, 39, 151, 33, "interpolateColorTs6"], [200, 40, 152, 2, "colors"], [200, 46, 152, 38], [200, 48, 153, 2, "convFromRgb"], [200, 59, 157, 3], [200, 61, 163, 5], [201, 6, 165, 2], [201, 10, 165, 8, "ch1"], [201, 13, 165, 21], [201, 16, 165, 24], [201, 18, 165, 26], [202, 6, 166, 2], [202, 10, 166, 8, "ch2"], [202, 13, 166, 21], [202, 16, 166, 24], [202, 18, 166, 26], [203, 6, 167, 2], [203, 10, 167, 8, "ch3"], [203, 13, 167, 21], [203, 16, 167, 24], [203, 18, 167, 26], [204, 6, 168, 2], [204, 10, 168, 8, "alpha"], [204, 15, 168, 23], [204, 18, 168, 26], [204, 20, 168, 28], [205, 6, 170, 2], [205, 11, 170, 7], [205, 15, 170, 11, "i"], [205, 16, 170, 12], [205, 19, 170, 15], [205, 20, 170, 16], [205, 22, 170, 18, "i"], [205, 23, 170, 19], [205, 26, 170, 22, "colors"], [205, 32, 170, 28], [205, 33, 170, 29, "length"], [205, 39, 170, 35], [205, 41, 170, 37, "i"], [205, 42, 170, 38], [205, 44, 170, 40], [205, 46, 170, 42], [206, 8, 171, 4], [206, 12, 171, 10, "color"], [206, 17, 171, 15], [206, 20, 171, 18, "colors"], [206, 26, 171, 24], [206, 27, 171, 25, "i"], [206, 28, 171, 26], [206, 29, 171, 27], [207, 8, 172, 4], [207, 12, 172, 10, "processedColor"], [207, 26, 172, 24], [207, 29, 172, 27], [207, 33, 172, 27, "processColor"], [207, 53, 172, 39], [207, 55, 172, 40, "color"], [207, 60, 172, 45], [207, 61, 172, 46], [208, 8, 173, 4], [208, 12, 173, 8], [208, 19, 173, 15, "processedColor"], [208, 33, 173, 29], [208, 38, 173, 34], [208, 46, 173, 42], [208, 48, 173, 44], [209, 10, 174, 6], [209, 14, 174, 12, "convertedColor"], [209, 28, 174, 26], [209, 31, 174, 29, "convFromRgb"], [209, 42, 174, 40], [209, 43, 174, 41], [210, 12, 175, 8, "r"], [210, 13, 175, 9], [210, 15, 175, 11], [210, 19, 175, 11, "red"], [210, 30, 175, 14], [210, 32, 175, 15, "processedColor"], [210, 46, 175, 29], [210, 47, 175, 30], [211, 12, 176, 8, "g"], [211, 13, 176, 9], [211, 15, 176, 11], [211, 19, 176, 11, "green"], [211, 32, 176, 16], [211, 34, 176, 17, "processedColor"], [211, 48, 176, 31], [211, 49, 176, 32], [212, 12, 177, 8, "b"], [212, 13, 177, 9], [212, 15, 177, 11], [212, 19, 177, 11, "blue"], [212, 31, 177, 15], [212, 33, 177, 16, "processedColor"], [212, 47, 177, 30], [213, 10, 178, 6], [213, 11, 178, 7], [213, 12, 178, 8], [214, 10, 180, 6, "ch1"], [214, 13, 180, 9], [214, 14, 180, 10, "push"], [214, 18, 180, 14], [214, 19, 180, 15, "convertedColor"], [214, 33, 180, 29], [214, 34, 180, 30, "ch1"], [214, 37, 180, 33], [214, 38, 180, 34], [215, 10, 181, 6, "ch2"], [215, 13, 181, 9], [215, 14, 181, 10, "push"], [215, 18, 181, 14], [215, 19, 181, 15, "convertedColor"], [215, 33, 181, 29], [215, 34, 181, 30, "ch2"], [215, 37, 181, 33], [215, 38, 181, 34], [216, 10, 182, 6, "ch3"], [216, 13, 182, 9], [216, 14, 182, 10, "push"], [216, 18, 182, 14], [216, 19, 182, 15, "convertedColor"], [216, 33, 182, 29], [216, 34, 182, 30, "ch3"], [216, 37, 182, 33], [216, 38, 182, 34], [217, 10, 183, 6, "alpha"], [217, 15, 183, 11], [217, 16, 183, 12, "push"], [217, 20, 183, 16], [217, 21, 183, 17], [217, 25, 183, 17, "opacity"], [217, 40, 183, 24], [217, 42, 183, 25, "processedColor"], [217, 56, 183, 39], [217, 57, 183, 40], [217, 58, 183, 41], [218, 8, 184, 4], [219, 6, 185, 2], [220, 6, 187, 2], [220, 13, 187, 9], [221, 8, 188, 4, "ch1"], [221, 11, 188, 7], [222, 8, 189, 4, "ch2"], [222, 11, 189, 7], [223, 8, 190, 4, "ch3"], [223, 11, 190, 7], [224, 8, 191, 4, "alpha"], [225, 6, 192, 2], [225, 7, 192, 3], [226, 4, 193, 0], [226, 5, 193, 1], [227, 4, 193, 1, "interpolateColorTs6"], [227, 23, 193, 1], [227, 24, 193, 1, "__closure"], [227, 33, 193, 1], [228, 6, 193, 1, "processColor"], [228, 18, 193, 1], [228, 20, 172, 27, "processColor"], [228, 40, 172, 39], [229, 6, 172, 39, "red"], [229, 9, 172, 39], [229, 11, 175, 11, "red"], [229, 22, 175, 14], [230, 6, 175, 14, "green"], [230, 11, 175, 14], [230, 13, 176, 11, "green"], [230, 26, 176, 16], [231, 6, 176, 16, "blue"], [231, 10, 176, 16], [231, 12, 177, 11, "blue"], [231, 24, 177, 15], [232, 6, 177, 15, "opacity"], [232, 13, 177, 15], [232, 15, 183, 17, "opacity"], [233, 4, 183, 24], [234, 4, 183, 24, "interpolateColorTs6"], [234, 23, 183, 24], [234, 24, 183, 24, "__workletHash"], [234, 37, 183, 24], [235, 4, 183, 24, "interpolateColorTs6"], [235, 23, 183, 24], [235, 24, 183, 24, "__initData"], [235, 34, 183, 24], [235, 37, 183, 24, "_worklet_4376090521074_init_data"], [235, 69, 183, 24], [236, 4, 183, 24, "interpolateColorTs6"], [236, 23, 183, 24], [236, 24, 183, 24, "__stackDetails"], [236, 38, 183, 24], [236, 41, 183, 24, "_e"], [236, 43, 183, 24], [237, 4, 183, 24], [237, 11, 183, 24, "interpolateColorTs6"], [237, 30, 183, 24], [238, 2, 183, 24], [238, 3, 151, 33], [238, 5, 193, 1], [239, 2, 193, 2], [239, 6, 193, 2, "_worklet_17110655634904_init_data"], [239, 39, 193, 2], [240, 4, 193, 2, "code"], [240, 8, 193, 2], [241, 4, 193, 2, "location"], [241, 12, 193, 2], [242, 4, 193, 2, "sourceMap"], [242, 13, 193, 2], [243, 4, 193, 2, "version"], [243, 11, 193, 2], [244, 2, 193, 2], [245, 2, 202, 0], [245, 6, 202, 6, "getInterpolateRGB"], [245, 23, 202, 23], [245, 26, 202, 26], [246, 4, 202, 26], [246, 8, 202, 26, "_e"], [246, 10, 202, 26], [246, 18, 202, 26, "global"], [246, 24, 202, 26], [246, 25, 202, 26, "Error"], [246, 30, 202, 26], [247, 4, 202, 26], [247, 8, 202, 26, "interpolateColorTs7"], [247, 27, 202, 26], [247, 39, 202, 26, "interpolateColorTs7"], [247, 40, 203, 2, "colors"], [247, 46, 203, 38], [247, 48, 204, 21], [248, 6, 206, 2], [248, 10, 206, 2, "_splitColorsIntoChann"], [248, 31, 206, 2], [248, 34, 206, 35, "_splitColorsIntoChannels"], [248, 58, 206, 59], [248, 59, 207, 4, "colors"], [248, 65, 207, 10], [248, 67, 208, 5, "color"], [248, 72, 208, 10], [248, 77, 208, 16], [249, 10, 209, 6, "ch1"], [249, 13, 209, 9], [249, 15, 209, 11, "color"], [249, 20, 209, 16], [249, 21, 209, 17, "r"], [249, 22, 209, 18], [250, 10, 210, 6, "ch2"], [250, 13, 210, 9], [250, 15, 210, 11, "color"], [250, 20, 210, 16], [250, 21, 210, 17, "g"], [250, 22, 210, 18], [251, 10, 211, 6, "ch3"], [251, 13, 211, 9], [251, 15, 211, 11, "color"], [251, 20, 211, 16], [251, 21, 211, 17, "b"], [252, 8, 212, 4], [252, 9, 212, 5], [252, 10, 213, 2], [252, 11, 213, 3], [253, 8, 206, 10, "ch1"], [253, 11, 206, 13], [253, 14, 206, 13, "_splitColorsIntoChann"], [253, 35, 206, 13], [253, 36, 206, 10, "ch1"], [253, 39, 206, 13], [254, 8, 206, 15, "ch2"], [254, 11, 206, 18], [254, 14, 206, 18, "_splitColorsIntoChann"], [254, 35, 206, 18], [254, 36, 206, 15, "ch2"], [254, 39, 206, 18], [255, 8, 206, 20, "ch3"], [255, 11, 206, 23], [255, 14, 206, 23, "_splitColorsIntoChann"], [255, 35, 206, 23], [255, 36, 206, 20, "ch3"], [255, 39, 206, 23], [256, 8, 206, 25, "alpha"], [256, 13, 206, 30], [256, 16, 206, 30, "_splitColorsIntoChann"], [256, 37, 206, 30], [256, 38, 206, 25, "alpha"], [256, 43, 206, 30], [257, 6, 215, 2], [257, 13, 215, 9], [258, 8, 216, 4, "r"], [258, 9, 216, 5], [258, 11, 216, 7, "ch1"], [258, 14, 216, 10], [259, 8, 217, 4, "g"], [259, 9, 217, 5], [259, 11, 217, 7, "ch2"], [259, 14, 217, 10], [260, 8, 218, 4, "b"], [260, 9, 218, 5], [260, 11, 218, 7, "ch3"], [260, 14, 218, 10], [261, 8, 219, 4, "a"], [261, 9, 219, 5], [261, 11, 219, 7, "alpha"], [262, 6, 220, 2], [262, 7, 220, 3], [263, 4, 221, 0], [263, 5, 221, 1], [264, 4, 221, 1, "interpolateColorTs7"], [264, 23, 221, 1], [264, 24, 221, 1, "__closure"], [264, 33, 221, 1], [265, 6, 221, 1, "_splitColorsIntoChannels"], [266, 4, 221, 1], [267, 4, 221, 1, "interpolateColorTs7"], [267, 23, 221, 1], [267, 24, 221, 1, "__workletHash"], [267, 37, 221, 1], [268, 4, 221, 1, "interpolateColorTs7"], [268, 23, 221, 1], [268, 24, 221, 1, "__initData"], [268, 34, 221, 1], [268, 37, 221, 1, "_worklet_17110655634904_init_data"], [268, 70, 221, 1], [269, 4, 221, 1, "interpolateColorTs7"], [269, 23, 221, 1], [269, 24, 221, 1, "__stackDetails"], [269, 38, 221, 1], [269, 41, 221, 1, "_e"], [269, 43, 221, 1], [270, 4, 221, 1], [270, 11, 221, 1, "interpolateColorTs7"], [270, 30, 221, 1], [271, 2, 221, 1], [271, 3, 202, 26], [271, 5, 221, 1], [272, 2, 221, 2], [272, 6, 221, 2, "_worklet_142566816320_init_data"], [272, 37, 221, 2], [273, 4, 221, 2, "code"], [273, 8, 221, 2], [274, 4, 221, 2, "location"], [274, 12, 221, 2], [275, 4, 221, 2, "sourceMap"], [275, 13, 221, 2], [276, 4, 221, 2, "version"], [276, 11, 221, 2], [277, 2, 221, 2], [278, 2, 230, 0], [278, 6, 230, 6, "getInterpolateHSV"], [278, 23, 230, 23], [278, 26, 230, 26], [279, 4, 230, 26], [279, 8, 230, 26, "_e"], [279, 10, 230, 26], [279, 18, 230, 26, "global"], [279, 24, 230, 26], [279, 25, 230, 26, "Error"], [279, 30, 230, 26], [280, 4, 230, 26], [280, 8, 230, 26, "interpolateColorTs8"], [280, 27, 230, 26], [280, 39, 230, 26, "interpolateColorTs8"], [280, 40, 231, 2, "colors"], [280, 46, 231, 38], [280, 48, 232, 21], [281, 6, 234, 2], [281, 10, 234, 2, "_splitColorsIntoChann2"], [281, 32, 234, 2], [281, 35, 234, 35, "_splitColorsIntoChannels"], [281, 59, 234, 59], [281, 60, 234, 60, "colors"], [281, 66, 234, 66], [281, 68, 234, 69, "color"], [281, 73, 234, 74], [281, 77, 234, 79], [282, 10, 235, 4], [282, 14, 235, 10, "hsvColor"], [282, 22, 235, 18], [282, 25, 235, 21], [282, 29, 235, 21, "RGBtoHSV"], [282, 45, 235, 29], [282, 47, 235, 30, "color"], [282, 52, 235, 35], [282, 53, 235, 36, "r"], [282, 54, 235, 37], [282, 56, 235, 39, "color"], [282, 61, 235, 44], [282, 62, 235, 45, "g"], [282, 63, 235, 46], [282, 65, 235, 48, "color"], [282, 70, 235, 53], [282, 71, 235, 54, "b"], [282, 72, 235, 55], [282, 73, 235, 56], [283, 10, 236, 4], [283, 17, 236, 11], [284, 12, 237, 6, "ch1"], [284, 15, 237, 9], [284, 17, 237, 11, "hsvColor"], [284, 25, 237, 19], [284, 26, 237, 20, "h"], [284, 27, 237, 21], [285, 12, 238, 6, "ch2"], [285, 15, 238, 9], [285, 17, 238, 11, "hsvColor"], [285, 25, 238, 19], [285, 26, 238, 20, "s"], [285, 27, 238, 21], [286, 12, 239, 6, "ch3"], [286, 15, 239, 9], [286, 17, 239, 11, "hsvColor"], [286, 25, 239, 19], [286, 26, 239, 20, "v"], [287, 10, 240, 4], [287, 11, 240, 5], [288, 8, 241, 2], [288, 9, 241, 3], [288, 10, 241, 4], [289, 8, 234, 10, "ch1"], [289, 11, 234, 13], [289, 14, 234, 13, "_splitColorsIntoChann2"], [289, 36, 234, 13], [289, 37, 234, 10, "ch1"], [289, 40, 234, 13], [290, 8, 234, 15, "ch2"], [290, 11, 234, 18], [290, 14, 234, 18, "_splitColorsIntoChann2"], [290, 36, 234, 18], [290, 37, 234, 15, "ch2"], [290, 40, 234, 18], [291, 8, 234, 20, "ch3"], [291, 11, 234, 23], [291, 14, 234, 23, "_splitColorsIntoChann2"], [291, 36, 234, 23], [291, 37, 234, 20, "ch3"], [291, 40, 234, 23], [292, 8, 234, 25, "alpha"], [292, 13, 234, 30], [292, 16, 234, 30, "_splitColorsIntoChann2"], [292, 38, 234, 30], [292, 39, 234, 25, "alpha"], [292, 44, 234, 30], [293, 6, 243, 2], [293, 13, 243, 9], [294, 8, 244, 4, "h"], [294, 9, 244, 5], [294, 11, 244, 7, "ch1"], [294, 14, 244, 10], [295, 8, 245, 4, "s"], [295, 9, 245, 5], [295, 11, 245, 7, "ch2"], [295, 14, 245, 10], [296, 8, 246, 4, "v"], [296, 9, 246, 5], [296, 11, 246, 7, "ch3"], [296, 14, 246, 10], [297, 8, 247, 4, "a"], [297, 9, 247, 5], [297, 11, 247, 7, "alpha"], [298, 6, 248, 2], [298, 7, 248, 3], [299, 4, 249, 0], [299, 5, 249, 1], [300, 4, 249, 1, "interpolateColorTs8"], [300, 23, 249, 1], [300, 24, 249, 1, "__closure"], [300, 33, 249, 1], [301, 6, 249, 1, "_splitColorsIntoChannels"], [301, 30, 249, 1], [302, 6, 249, 1, "RGBtoHSV"], [302, 14, 249, 1], [302, 16, 235, 21, "RGBtoHSV"], [303, 4, 235, 29], [304, 4, 235, 29, "interpolateColorTs8"], [304, 23, 235, 29], [304, 24, 235, 29, "__workletHash"], [304, 37, 235, 29], [305, 4, 235, 29, "interpolateColorTs8"], [305, 23, 235, 29], [305, 24, 235, 29, "__initData"], [305, 34, 235, 29], [305, 37, 235, 29, "_worklet_142566816320_init_data"], [305, 68, 235, 29], [306, 4, 235, 29, "interpolateColorTs8"], [306, 23, 235, 29], [306, 24, 235, 29, "__stackDetails"], [306, 38, 235, 29], [306, 41, 235, 29, "_e"], [306, 43, 235, 29], [307, 4, 235, 29], [307, 11, 235, 29, "interpolateColorTs8"], [307, 30, 235, 29], [308, 2, 235, 29], [308, 3, 230, 26], [308, 5, 249, 1], [309, 2, 249, 2], [309, 6, 249, 2, "_worklet_12286367320350_init_data"], [309, 39, 249, 2], [310, 4, 249, 2, "code"], [310, 8, 249, 2], [311, 4, 249, 2, "location"], [311, 12, 249, 2], [312, 4, 249, 2, "sourceMap"], [312, 13, 249, 2], [313, 4, 249, 2, "version"], [313, 11, 249, 2], [314, 2, 249, 2], [315, 2, 258, 0], [315, 6, 258, 6, "getInterpolateLAB"], [315, 23, 258, 23], [315, 26, 258, 26], [316, 4, 258, 26], [316, 8, 258, 26, "_e"], [316, 10, 258, 26], [316, 18, 258, 26, "global"], [316, 24, 258, 26], [316, 25, 258, 26, "Error"], [316, 30, 258, 26], [317, 4, 258, 26], [317, 8, 258, 26, "interpolateColorTs9"], [317, 27, 258, 26], [317, 39, 258, 26, "interpolateColorTs9"], [317, 40, 259, 2, "colors"], [317, 46, 259, 38], [317, 48, 260, 21], [318, 6, 263, 2], [318, 10, 263, 2, "_splitColorsIntoChann3"], [318, 32, 263, 2], [318, 35, 263, 35, "_splitColorsIntoChannels"], [318, 59, 263, 59], [318, 60, 263, 60, "colors"], [318, 66, 263, 66], [318, 68, 263, 69, "color"], [318, 73, 263, 74], [318, 77, 263, 79], [319, 10, 264, 4], [319, 14, 264, 10, "labColor"], [319, 22, 264, 18], [319, 25, 264, 21, "culori"], [319, 40, 264, 27], [319, 41, 264, 28, "oklab"], [319, 46, 264, 33], [319, 47, 264, 34, "convert"], [319, 54, 264, 41], [319, 55, 264, 42, "fromRgb"], [319, 62, 264, 49], [319, 63, 264, 50, "color"], [319, 68, 264, 55], [319, 69, 264, 56], [320, 10, 265, 4], [320, 17, 265, 11], [321, 12, 266, 6, "ch1"], [321, 15, 266, 9], [321, 17, 266, 11, "labColor"], [321, 25, 266, 19], [321, 26, 266, 20, "l"], [321, 27, 266, 21], [322, 12, 267, 6, "ch2"], [322, 15, 267, 9], [322, 17, 267, 11, "labColor"], [322, 25, 267, 19], [322, 26, 267, 20, "a"], [322, 27, 267, 21], [323, 12, 268, 6, "ch3"], [323, 15, 268, 9], [323, 17, 268, 11, "labColor"], [323, 25, 268, 19], [323, 26, 268, 20, "b"], [324, 10, 269, 4], [324, 11, 269, 5], [325, 8, 270, 2], [325, 9, 270, 3], [325, 10, 270, 4], [326, 8, 263, 10, "ch1"], [326, 11, 263, 13], [326, 14, 263, 13, "_splitColorsIntoChann3"], [326, 36, 263, 13], [326, 37, 263, 10, "ch1"], [326, 40, 263, 13], [327, 8, 263, 15, "ch2"], [327, 11, 263, 18], [327, 14, 263, 18, "_splitColorsIntoChann3"], [327, 36, 263, 18], [327, 37, 263, 15, "ch2"], [327, 40, 263, 18], [328, 8, 263, 20, "ch3"], [328, 11, 263, 23], [328, 14, 263, 23, "_splitColorsIntoChann3"], [328, 36, 263, 23], [328, 37, 263, 20, "ch3"], [328, 40, 263, 23], [329, 8, 263, 25, "alpha"], [329, 13, 263, 30], [329, 16, 263, 30, "_splitColorsIntoChann3"], [329, 38, 263, 30], [329, 39, 263, 25, "alpha"], [329, 44, 263, 30], [330, 6, 272, 2], [330, 13, 272, 9], [331, 8, 273, 4, "l"], [331, 9, 273, 5], [331, 11, 273, 7, "ch1"], [331, 14, 273, 10], [332, 8, 274, 4, "a"], [332, 9, 274, 5], [332, 11, 274, 7, "ch2"], [332, 14, 274, 10], [333, 8, 275, 4, "b"], [333, 9, 275, 5], [333, 11, 275, 7, "ch3"], [333, 14, 275, 10], [334, 8, 276, 4, "alpha"], [335, 6, 277, 2], [335, 7, 277, 3], [336, 4, 278, 0], [336, 5, 278, 1], [337, 4, 278, 1, "interpolateColorTs9"], [337, 23, 278, 1], [337, 24, 278, 1, "__closure"], [337, 33, 278, 1], [338, 6, 278, 1, "_splitColorsIntoChannels"], [338, 30, 278, 1], [339, 6, 278, 1, "culori"], [339, 12, 278, 1], [339, 14, 264, 21, "culori"], [340, 4, 264, 27], [341, 4, 264, 27, "interpolateColorTs9"], [341, 23, 264, 27], [341, 24, 264, 27, "__workletHash"], [341, 37, 264, 27], [342, 4, 264, 27, "interpolateColorTs9"], [342, 23, 264, 27], [342, 24, 264, 27, "__initData"], [342, 34, 264, 27], [342, 37, 264, 27, "_worklet_12286367320350_init_data"], [342, 70, 264, 27], [343, 4, 264, 27, "interpolateColorTs9"], [343, 23, 264, 27], [343, 24, 264, 27, "__stackDetails"], [343, 38, 264, 27], [343, 41, 264, 27, "_e"], [343, 43, 264, 27], [344, 4, 264, 27], [344, 11, 264, 27, "interpolateColorTs9"], [344, 30, 264, 27], [345, 2, 264, 27], [345, 3, 258, 26], [345, 5, 278, 1], [347, 2, 280, 0], [348, 0, 281, 0], [349, 0, 282, 0], [350, 0, 283, 0], [351, 0, 284, 0], [352, 0, 285, 0], [353, 0, 286, 0], [354, 0, 287, 0], [355, 0, 288, 0], [356, 0, 289, 0], [357, 0, 290, 0], [358, 0, 291, 0], [359, 0, 292, 0], [360, 0, 293, 0], [361, 0, 294, 0], [362, 0, 295, 0], [363, 0, 296, 0], [364, 0, 297, 0], [365, 2, 280, 0], [365, 6, 280, 0, "_worklet_8363465133138_init_data"], [365, 38, 280, 0], [366, 4, 280, 0, "code"], [366, 8, 280, 0], [367, 4, 280, 0, "location"], [367, 12, 280, 0], [368, 4, 280, 0, "sourceMap"], [368, 13, 280, 0], [369, 4, 280, 0, "version"], [369, 11, 280, 0], [370, 2, 280, 0], [371, 2, 280, 0], [371, 6, 280, 0, "interpolateColor"], [371, 22, 280, 0], [371, 25, 280, 0, "exports"], [371, 32, 280, 0], [371, 33, 280, 0, "interpolateColor"], [371, 49, 280, 0], [371, 52, 314, 7], [372, 4, 314, 7], [372, 8, 314, 7, "_e"], [372, 10, 314, 7], [372, 18, 314, 7, "global"], [372, 24, 314, 7], [372, 25, 314, 7, "Error"], [372, 30, 314, 7], [373, 4, 314, 7], [373, 8, 314, 7, "interpolateColor"], [373, 24, 314, 7], [373, 36, 314, 7, "interpolateColor"], [373, 37, 315, 2, "value"], [373, 42, 315, 15], [373, 44, 316, 2, "inputRange"], [373, 54, 316, 31], [373, 56, 317, 2, "outputRange"], [373, 67, 317, 43], [373, 69, 320, 19], [374, 6, 320, 19], [374, 10, 318, 2, "colorSpace"], [374, 20, 318, 35], [374, 23, 318, 35, "arguments"], [374, 32, 318, 35], [374, 33, 318, 35, "length"], [374, 39, 318, 35], [374, 47, 318, 35, "arguments"], [374, 56, 318, 35], [374, 64, 318, 35, "undefined"], [374, 73, 318, 35], [374, 76, 318, 35, "arguments"], [374, 85, 318, 35], [374, 91, 318, 38], [374, 96, 318, 43], [375, 6, 318, 43], [375, 10, 319, 2, "options"], [375, 17, 319, 31], [375, 20, 319, 31, "arguments"], [375, 29, 319, 31], [375, 30, 319, 31, "length"], [375, 36, 319, 31], [375, 44, 319, 31, "arguments"], [375, 53, 319, 31], [375, 61, 319, 31, "undefined"], [375, 70, 319, 31], [375, 73, 319, 31, "arguments"], [375, 82, 319, 31], [375, 88, 319, 34], [375, 89, 319, 35], [375, 90, 319, 36], [376, 6, 322, 2], [376, 10, 322, 6, "colorSpace"], [376, 20, 322, 16], [376, 25, 322, 21], [376, 30, 322, 26], [376, 32, 322, 28], [377, 8, 323, 4], [377, 15, 323, 11, "interpolateColorsHSV"], [377, 35, 323, 31], [377, 36, 324, 6, "value"], [377, 41, 324, 11], [377, 43, 325, 6, "inputRange"], [377, 53, 325, 16], [377, 55, 326, 6, "getInterpolateHSV"], [377, 72, 326, 23], [377, 73, 326, 24, "outputRange"], [377, 84, 326, 35], [377, 85, 326, 36], [377, 87, 327, 6, "options"], [377, 94, 328, 4], [377, 95, 328, 5], [378, 6, 329, 2], [378, 7, 329, 3], [378, 13, 329, 9], [378, 17, 329, 13, "colorSpace"], [378, 27, 329, 23], [378, 32, 329, 28], [378, 37, 329, 33], [378, 39, 329, 35], [379, 8, 330, 4], [379, 15, 330, 11, "interpolateColorsRGB"], [379, 35, 330, 31], [379, 36, 331, 6, "value"], [379, 41, 331, 11], [379, 43, 332, 6, "inputRange"], [379, 53, 332, 16], [379, 55, 333, 6, "getInterpolateRGB"], [379, 72, 333, 23], [379, 73, 333, 24, "outputRange"], [379, 84, 333, 35], [379, 85, 333, 36], [379, 87, 334, 6, "options"], [379, 94, 335, 4], [379, 95, 335, 5], [380, 6, 336, 2], [380, 7, 336, 3], [380, 13, 336, 9], [380, 17, 336, 13, "colorSpace"], [380, 27, 336, 23], [380, 32, 336, 28], [380, 37, 336, 33], [380, 39, 336, 35], [381, 8, 337, 4], [381, 15, 337, 11, "interpolateColorsLAB"], [381, 35, 337, 31], [381, 36, 338, 6, "value"], [381, 41, 338, 11], [381, 43, 339, 6, "inputRange"], [381, 53, 339, 16], [381, 55, 340, 6, "getInterpolateLAB"], [381, 72, 340, 23], [381, 73, 340, 24, "outputRange"], [381, 84, 340, 35], [381, 85, 340, 36], [381, 87, 341, 6, "options"], [381, 94, 342, 4], [381, 95, 342, 5], [382, 6, 343, 2], [383, 6, 345, 2], [383, 12, 345, 8], [383, 16, 345, 12, "ReanimatedError"], [383, 39, 345, 27], [383, 40, 346, 4], [383, 73, 347, 6, "colorSpace"], [383, 83, 347, 16], [383, 131, 349, 2], [383, 132, 349, 3], [384, 4, 350, 0], [384, 5, 350, 1], [385, 4, 350, 1, "interpolateColor"], [385, 20, 350, 1], [385, 21, 350, 1, "__closure"], [385, 30, 350, 1], [386, 6, 350, 1, "interpolateColorsHSV"], [386, 26, 350, 1], [387, 6, 350, 1, "getInterpolateHSV"], [387, 23, 350, 1], [388, 6, 350, 1, "interpolateColorsRGB"], [388, 26, 350, 1], [389, 6, 350, 1, "getInterpolateRGB"], [389, 23, 350, 1], [390, 6, 350, 1, "interpolateColorsLAB"], [390, 26, 350, 1], [391, 6, 350, 1, "getInterpolateLAB"], [392, 4, 350, 1], [393, 4, 350, 1, "interpolateColor"], [393, 20, 350, 1], [393, 21, 350, 1, "__workletHash"], [393, 34, 350, 1], [394, 4, 350, 1, "interpolateColor"], [394, 20, 350, 1], [394, 21, 350, 1, "__initData"], [394, 31, 350, 1], [394, 34, 350, 1, "_worklet_8363465133138_init_data"], [394, 66, 350, 1], [395, 4, 350, 1, "interpolateColor"], [395, 20, 350, 1], [395, 21, 350, 1, "__stackDetails"], [395, 35, 350, 1], [395, 38, 350, 1, "_e"], [395, 40, 350, 1], [396, 4, 350, 1], [396, 11, 350, 1, "interpolateColor"], [396, 27, 350, 1], [397, 2, 350, 1], [397, 3, 314, 7], [398, 2, 314, 7], [398, 6, 352, 12, "ColorSpace"], [398, 16, 352, 22], [398, 19, 352, 22, "exports"], [398, 26, 352, 22], [398, 27, 352, 22, "ColorSpace"], [398, 37, 352, 22], [398, 63, 352, 12, "ColorSpace"], [398, 73, 352, 22], [399, 4, 352, 12, "ColorSpace"], [399, 14, 352, 22], [399, 15, 352, 12, "ColorSpace"], [399, 25, 352, 22], [400, 4, 352, 12, "ColorSpace"], [400, 14, 352, 22], [400, 15, 352, 12, "ColorSpace"], [400, 25, 352, 22], [401, 4, 352, 12, "ColorSpace"], [401, 14, 352, 22], [401, 15, 352, 12, "ColorSpace"], [401, 25, 352, 22], [402, 4, 352, 22], [402, 11, 352, 12, "ColorSpace"], [402, 21, 352, 22], [403, 2, 352, 22], [404, 2, 366, 7], [404, 11, 366, 16, "useInterpolateConfig"], [404, 31, 366, 36, "useInterpolateConfig"], [404, 32, 367, 2, "inputRange"], [404, 42, 367, 31], [404, 44, 368, 2, "outputRange"], [404, 55, 368, 43], [404, 57, 371, 34], [405, 4, 371, 34], [405, 8, 369, 2, "colorSpace"], [405, 18, 369, 12], [405, 21, 369, 12, "arguments"], [405, 30, 369, 12], [405, 31, 369, 12, "length"], [405, 37, 369, 12], [405, 45, 369, 12, "arguments"], [405, 54, 369, 12], [405, 62, 369, 12, "undefined"], [405, 71, 369, 12], [405, 74, 369, 12, "arguments"], [405, 83, 369, 12], [405, 89, 369, 15, "ColorSpace"], [405, 99, 369, 25], [405, 100, 369, 26, "RGB"], [405, 103, 369, 29], [406, 4, 369, 29], [406, 8, 370, 2, "options"], [406, 15, 370, 31], [406, 18, 370, 31, "arguments"], [406, 27, 370, 31], [406, 28, 370, 31, "length"], [406, 34, 370, 31], [406, 42, 370, 31, "arguments"], [406, 51, 370, 31], [406, 59, 370, 31, "undefined"], [406, 68, 370, 31], [406, 71, 370, 31, "arguments"], [406, 80, 370, 31], [406, 86, 370, 34], [406, 87, 370, 35], [406, 88, 370, 36], [407, 4, 372, 2], [407, 11, 372, 9], [407, 15, 372, 9, "useSharedValue"], [407, 45, 372, 23], [407, 47, 372, 43], [408, 6, 373, 4, "inputRange"], [408, 16, 373, 14], [409, 6, 374, 4, "outputRange"], [409, 17, 374, 15], [410, 6, 375, 4, "colorSpace"], [410, 16, 375, 14], [411, 6, 376, 4, "cache"], [411, 11, 376, 9], [411, 13, 376, 11], [411, 17, 376, 11, "makeMutable"], [411, 34, 376, 22], [411, 36, 376, 63], [411, 40, 376, 67], [411, 41, 376, 68], [412, 6, 377, 4, "options"], [413, 4, 378, 2], [413, 5, 378, 3], [413, 6, 378, 4], [414, 2, 379, 0], [415, 0, 379, 1], [415, 3]], "functionMap": {"names": ["<global>", "interpolateColorsHSV", "toLinearSpace", "x.map$argument_0", "toGammaSpace", "interpolateColorsRGB", "interpolateColorsLAB", "_splitColorsIntoChannels", "getInterpolateRGB", "_splitColorsIntoChannels$argument_1", "getInterpolateHSV", "getInterpolateLAB", "interpolateColor", "useInterpolateConfig"], "mappings": "AAA;6BCiC;CDmD;sBEE;eCE,+BD;CFC;qBIE;CJG;6BKE;CL2B;6BME;CNuB;iCOE;CP0C;0BQS;ICM;MDI;CRS;0BUS;oEDI;GCO;CVQ;0BWS;oEFK;GEO;CXQ;OYoC;CZoC;OagB;Cba"}}, "type": "js/module"}]}