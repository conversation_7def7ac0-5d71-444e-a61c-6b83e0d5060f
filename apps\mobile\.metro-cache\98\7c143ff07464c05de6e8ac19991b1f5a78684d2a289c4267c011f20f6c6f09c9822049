{"dependencies": [{"name": "../ReadOnlyNode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 46}}], "key": "kmQO1D1LWE2UgCAXE58M/kfGqeg=", "exportNames": ["*"]}}, {"name": "../ReadOnlyElement", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 43, "column": 27}, "end": {"line": 43, "column": 56}}], "key": "G4zKTjrgAtYZS71Z8btSm0PNIi8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getElementSibling = getElementSibling;\n  var _ReadOnlyNode = require(_dependencyMap[0], \"../ReadOnlyNode\");\n  var ReadOnlyElementClass;\n  function getElementSibling(node, direction) {\n    var parent = node.parentNode;\n    if (parent == null) {\n      return null;\n    }\n    var childNodes = (0, _ReadOnlyNode.getChildNodes)(parent);\n    var startPosition = childNodes.indexOf(node);\n    if (startPosition === -1) {\n      return null;\n    }\n    var increment = direction === 'next' ? 1 : -1;\n    var position = startPosition + increment;\n    if (ReadOnlyElementClass == null) {\n      ReadOnlyElementClass = require(_dependencyMap[1], \"../ReadOnlyElement\").default;\n    }\n    while (childNodes[position] != null && !(childNodes[position] instanceof ReadOnlyElementClass)) {\n      position = position + increment;\n    }\n    return childNodes[position] ?? null;\n  }\n});", "lineCount": 28, "map": [[6, 2, 14, 0], [6, 6, 14, 0, "_ReadOnlyNode"], [6, 19, 14, 0], [6, 22, 14, 0, "require"], [6, 29, 14, 0], [6, 30, 14, 0, "_dependencyMap"], [6, 44, 14, 0], [7, 2, 18, 0], [7, 6, 18, 4, "ReadOnlyElementClass"], [7, 26, 18, 48], [8, 2, 20, 7], [8, 11, 20, 16, "getElementSibling"], [8, 28, 20, 33, "getElementSibling"], [8, 29, 21, 2, "node"], [8, 33, 21, 20], [8, 35, 22, 2, "direction"], [8, 44, 22, 32], [8, 46, 23, 26], [9, 4, 24, 2], [9, 8, 24, 8, "parent"], [9, 14, 24, 14], [9, 17, 24, 17, "node"], [9, 21, 24, 21], [9, 22, 24, 22, "parentNode"], [9, 32, 24, 32], [10, 4, 25, 2], [10, 8, 25, 6, "parent"], [10, 14, 25, 12], [10, 18, 25, 16], [10, 22, 25, 20], [10, 24, 25, 22], [11, 6, 27, 4], [11, 13, 27, 11], [11, 17, 27, 15], [12, 4, 28, 2], [13, 4, 30, 2], [13, 8, 30, 8, "childNodes"], [13, 18, 30, 18], [13, 21, 30, 21], [13, 25, 30, 21, "getChildNodes"], [13, 52, 30, 34], [13, 54, 30, 35, "parent"], [13, 60, 30, 41], [13, 61, 30, 42], [14, 4, 32, 2], [14, 8, 32, 8, "startPosition"], [14, 21, 32, 21], [14, 24, 32, 24, "childNodes"], [14, 34, 32, 34], [14, 35, 32, 35, "indexOf"], [14, 42, 32, 42], [14, 43, 32, 43, "node"], [14, 47, 32, 47], [14, 48, 32, 48], [15, 4, 33, 2], [15, 8, 33, 6, "startPosition"], [15, 21, 33, 19], [15, 26, 33, 24], [15, 27, 33, 25], [15, 28, 33, 26], [15, 30, 33, 28], [16, 6, 34, 4], [16, 13, 34, 11], [16, 17, 34, 15], [17, 4, 35, 2], [18, 4, 37, 2], [18, 8, 37, 8, "increment"], [18, 17, 37, 17], [18, 20, 37, 20, "direction"], [18, 29, 37, 29], [18, 34, 37, 34], [18, 40, 37, 40], [18, 43, 37, 43], [18, 44, 37, 44], [18, 47, 37, 47], [18, 48, 37, 48], [18, 49, 37, 49], [19, 4, 39, 2], [19, 8, 39, 6, "position"], [19, 16, 39, 14], [19, 19, 39, 17, "startPosition"], [19, 32, 39, 30], [19, 35, 39, 33, "increment"], [19, 44, 39, 42], [20, 4, 41, 2], [20, 8, 41, 6, "ReadOnlyElementClass"], [20, 28, 41, 26], [20, 32, 41, 30], [20, 36, 41, 34], [20, 38, 41, 36], [21, 6, 43, 4, "ReadOnlyElementClass"], [21, 26, 43, 24], [21, 29, 43, 27, "require"], [21, 36, 43, 34], [21, 37, 43, 34, "_dependencyMap"], [21, 51, 43, 34], [21, 76, 43, 55], [21, 77, 43, 56], [21, 78, 43, 57, "default"], [21, 85, 43, 64], [22, 4, 44, 2], [23, 4, 46, 2], [23, 11, 47, 4, "childNodes"], [23, 21, 47, 14], [23, 22, 47, 15, "position"], [23, 30, 47, 23], [23, 31, 47, 24], [23, 35, 47, 28], [23, 39, 47, 32], [23, 43, 48, 4], [23, 45, 48, 6, "childNodes"], [23, 55, 48, 16], [23, 56, 48, 17, "position"], [23, 64, 48, 25], [23, 65, 48, 26], [23, 77, 48, 38, "ReadOnlyElementClass"], [23, 97, 48, 58], [23, 98, 48, 59], [23, 100, 49, 4], [24, 6, 50, 4, "position"], [24, 14, 50, 12], [24, 17, 50, 15, "position"], [24, 25, 50, 23], [24, 28, 50, 26, "increment"], [24, 37, 50, 35], [25, 4, 51, 2], [26, 4, 53, 2], [26, 11, 53, 9, "childNodes"], [26, 21, 53, 19], [26, 22, 53, 20, "position"], [26, 30, 53, 28], [26, 31, 53, 29], [26, 35, 53, 33], [26, 39, 53, 37], [27, 2, 54, 0], [28, 0, 54, 1], [28, 3]], "functionMap": {"names": ["<global>", "getElementSibling"], "mappings": "AAA;OCmB"}}, "type": "js/module"}]}