{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2F..%2F..%2Fnode_modules%2F%40react-navigation%2Felements%2Flib%2Fmodule%2Fassets\",\n    \"width\": 50,\n    \"height\": 85,\n    \"scales\": [1],\n    \"hash\": \"0a328cd9c1afd0afe8e3b1ec5165b1b4\",\n    \"name\": \"back-icon-mask\",\n    \"type\": \"png\",\n    \"fileHashes\": [\"0a328cd9c1afd0afe8e3b1ec5165b1b4\"]\n  });\n});", "lineCount": 13, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 134, 1, 154], [5, 4, 1, 155], [5, 11, 1, 162], [5, 13, 1, 163], [5, 15, 1, 165], [6, 4, 1, 166], [6, 12, 1, 174], [6, 14, 1, 175], [6, 16, 1, 177], [7, 4, 1, 178], [7, 12, 1, 186], [7, 14, 1, 187], [7, 15, 1, 188], [7, 16, 1, 189], [7, 17, 1, 190], [8, 4, 1, 191], [8, 10, 1, 197], [8, 12, 1, 198], [8, 46, 1, 232], [9, 4, 1, 233], [9, 10, 1, 239], [9, 12, 1, 240], [9, 28, 1, 256], [10, 4, 1, 257], [10, 10, 1, 263], [10, 12, 1, 264], [10, 17, 1, 269], [11, 4, 1, 270], [11, 16, 1, 282], [11, 18, 1, 283], [11, 19, 1, 284], [11, 53, 1, 318], [12, 2, 1, 319], [12, 3, 1, 320], [13, 0, 1, 320], [13, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}