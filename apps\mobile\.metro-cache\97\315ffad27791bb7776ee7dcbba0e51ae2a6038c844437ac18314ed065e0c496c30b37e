{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../../Libraries/Utilities/codegenNativeCommands", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 90}}], "key": "AjOtvDn+5p61rGCbO8jnqBuzJgA=", "exportNames": ["*"]}}, {"name": "../../../../Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 92}}], "key": "jzP+LUi0+8ZCeIUw7GN35c9PLT4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 59, "column": 0}, "end": {"line": 61, "column": 31}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/ReactNative/RendererProxy", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 59, "column": 0}, "end": {"line": 61, "column": 31}}], "key": "3I0755DLARiFS4in/Xu6jYBSFBs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = exports.Commands = void 0;\n  var _codegenNativeCommands = _interopRequireDefault(require(_dependencyMap[1], \"../../../../Libraries/Utilities/codegenNativeCommands\"));\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[2], \"../../../../Libraries/Utilities/codegenNativeComponent\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var NativeComponentRegistry = require(_dependencyMap[4], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var _require = require(_dependencyMap[5], \"react-native/Libraries/ReactNative/RendererProxy\"),\n    dispatchCommand = _require.dispatchCommand;\n  var nativeComponentName = 'DebuggingOverlay';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"DebuggingOverlay\",\n    validAttributes: {}\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n  var Commands = exports.Commands = {\n    highlightTraceUpdates(ref, updates) {\n      dispatchCommand(ref, \"highlightTraceUpdates\", [updates]);\n    },\n    highlightElements(ref, elements) {\n      dispatchCommand(ref, \"highlightElements\", [elements]);\n    },\n    clearElementsHighlights(ref) {\n      dispatchCommand(ref, \"clearElementsHighlights\", []);\n    }\n  };\n});", "lineCount": 31, "map": [[7, 2, 15, 0], [7, 6, 15, 0, "_codegenNativeCommands"], [7, 28, 15, 0], [7, 31, 15, 0, "_interopRequireDefault"], [7, 53, 15, 0], [7, 54, 15, 0, "require"], [7, 61, 15, 0], [7, 62, 15, 0, "_dependencyMap"], [7, 76, 15, 0], [8, 2, 16, 0], [8, 6, 16, 0, "_codegenNativeComponent"], [8, 29, 16, 0], [8, 32, 16, 0, "_interopRequireDefault"], [8, 54, 16, 0], [8, 55, 16, 0, "require"], [8, 62, 16, 0], [8, 63, 16, 0, "_dependencyMap"], [8, 77, 16, 0], [9, 2, 17, 0], [9, 6, 17, 0, "React"], [9, 11, 17, 0], [9, 14, 17, 0, "_interopRequireWildcard"], [9, 37, 17, 0], [9, 38, 17, 0, "require"], [9, 45, 17, 0], [9, 46, 17, 0, "_dependencyMap"], [9, 60, 17, 0], [10, 2, 17, 31], [10, 11, 17, 31, "_interopRequireWildcard"], [10, 35, 17, 31, "e"], [10, 36, 17, 31], [10, 38, 17, 31, "t"], [10, 39, 17, 31], [10, 68, 17, 31, "WeakMap"], [10, 75, 17, 31], [10, 81, 17, 31, "r"], [10, 82, 17, 31], [10, 89, 17, 31, "WeakMap"], [10, 96, 17, 31], [10, 100, 17, 31, "n"], [10, 101, 17, 31], [10, 108, 17, 31, "WeakMap"], [10, 115, 17, 31], [10, 127, 17, 31, "_interopRequireWildcard"], [10, 150, 17, 31], [10, 162, 17, 31, "_interopRequireWildcard"], [10, 163, 17, 31, "e"], [10, 164, 17, 31], [10, 166, 17, 31, "t"], [10, 167, 17, 31], [10, 176, 17, 31, "t"], [10, 177, 17, 31], [10, 181, 17, 31, "e"], [10, 182, 17, 31], [10, 186, 17, 31, "e"], [10, 187, 17, 31], [10, 188, 17, 31, "__esModule"], [10, 198, 17, 31], [10, 207, 17, 31, "e"], [10, 208, 17, 31], [10, 214, 17, 31, "o"], [10, 215, 17, 31], [10, 217, 17, 31, "i"], [10, 218, 17, 31], [10, 220, 17, 31, "f"], [10, 221, 17, 31], [10, 226, 17, 31, "__proto__"], [10, 235, 17, 31], [10, 243, 17, 31, "default"], [10, 250, 17, 31], [10, 252, 17, 31, "e"], [10, 253, 17, 31], [10, 270, 17, 31, "e"], [10, 271, 17, 31], [10, 294, 17, 31, "e"], [10, 295, 17, 31], [10, 320, 17, 31, "e"], [10, 321, 17, 31], [10, 330, 17, 31, "f"], [10, 331, 17, 31], [10, 337, 17, 31, "o"], [10, 338, 17, 31], [10, 341, 17, 31, "t"], [10, 342, 17, 31], [10, 345, 17, 31, "n"], [10, 346, 17, 31], [10, 349, 17, 31, "r"], [10, 350, 17, 31], [10, 358, 17, 31, "o"], [10, 359, 17, 31], [10, 360, 17, 31, "has"], [10, 363, 17, 31], [10, 364, 17, 31, "e"], [10, 365, 17, 31], [10, 375, 17, 31, "o"], [10, 376, 17, 31], [10, 377, 17, 31, "get"], [10, 380, 17, 31], [10, 381, 17, 31, "e"], [10, 382, 17, 31], [10, 385, 17, 31, "o"], [10, 386, 17, 31], [10, 387, 17, 31, "set"], [10, 390, 17, 31], [10, 391, 17, 31, "e"], [10, 392, 17, 31], [10, 394, 17, 31, "f"], [10, 395, 17, 31], [10, 409, 17, 31, "_t"], [10, 411, 17, 31], [10, 415, 17, 31, "e"], [10, 416, 17, 31], [10, 432, 17, 31, "_t"], [10, 434, 17, 31], [10, 441, 17, 31, "hasOwnProperty"], [10, 455, 17, 31], [10, 456, 17, 31, "call"], [10, 460, 17, 31], [10, 461, 17, 31, "e"], [10, 462, 17, 31], [10, 464, 17, 31, "_t"], [10, 466, 17, 31], [10, 473, 17, 31, "i"], [10, 474, 17, 31], [10, 478, 17, 31, "o"], [10, 479, 17, 31], [10, 482, 17, 31, "Object"], [10, 488, 17, 31], [10, 489, 17, 31, "defineProperty"], [10, 503, 17, 31], [10, 508, 17, 31, "Object"], [10, 514, 17, 31], [10, 515, 17, 31, "getOwnPropertyDescriptor"], [10, 539, 17, 31], [10, 540, 17, 31, "e"], [10, 541, 17, 31], [10, 543, 17, 31, "_t"], [10, 545, 17, 31], [10, 552, 17, 31, "i"], [10, 553, 17, 31], [10, 554, 17, 31, "get"], [10, 557, 17, 31], [10, 561, 17, 31, "i"], [10, 562, 17, 31], [10, 563, 17, 31, "set"], [10, 566, 17, 31], [10, 570, 17, 31, "o"], [10, 571, 17, 31], [10, 572, 17, 31, "f"], [10, 573, 17, 31], [10, 575, 17, 31, "_t"], [10, 577, 17, 31], [10, 579, 17, 31, "i"], [10, 580, 17, 31], [10, 584, 17, 31, "f"], [10, 585, 17, 31], [10, 586, 17, 31, "_t"], [10, 588, 17, 31], [10, 592, 17, 31, "e"], [10, 593, 17, 31], [10, 594, 17, 31, "_t"], [10, 596, 17, 31], [10, 607, 17, 31, "f"], [10, 608, 17, 31], [10, 613, 17, 31, "e"], [10, 614, 17, 31], [10, 616, 17, 31, "t"], [10, 617, 17, 31], [11, 2, 59, 0], [11, 6, 59, 0, "NativeComponentRegistry"], [11, 29, 61, 31], [11, 32, 59, 0, "require"], [11, 39, 61, 31], [11, 40, 61, 31, "_dependencyMap"], [11, 54, 61, 31], [11, 123, 61, 30], [11, 124, 61, 31], [12, 2, 59, 0], [12, 6, 59, 0, "_require"], [12, 14, 59, 0], [12, 17, 59, 0, "require"], [12, 24, 61, 31], [12, 25, 61, 31, "_dependencyMap"], [12, 39, 61, 31], [12, 94, 61, 30], [12, 95, 61, 31], [13, 4, 59, 0, "dispatchCommand"], [13, 19, 61, 31], [13, 22, 61, 31, "_require"], [13, 30, 61, 31], [13, 31, 59, 0, "dispatchCommand"], [13, 46, 61, 31], [14, 2, 59, 0], [14, 6, 59, 0, "nativeComponentName"], [14, 25, 61, 31], [14, 28, 59, 0], [14, 46, 61, 31], [15, 2, 59, 0], [15, 6, 59, 0, "__INTERNAL_VIEW_CONFIG"], [15, 28, 61, 31], [15, 31, 61, 31, "exports"], [15, 38, 61, 31], [15, 39, 61, 31, "__INTERNAL_VIEW_CONFIG"], [15, 61, 61, 31], [15, 64, 59, 0], [16, 4, 59, 0, "uiViewClassName"], [16, 19, 61, 31], [16, 21, 59, 0], [16, 39, 61, 31], [17, 4, 59, 0, "validAttributes"], [17, 19, 61, 31], [17, 21, 59, 0], [17, 22, 61, 30], [18, 2, 61, 30], [18, 3, 61, 31], [19, 2, 61, 31], [19, 6, 61, 31, "_default"], [19, 14, 61, 31], [19, 17, 61, 31, "exports"], [19, 24, 61, 31], [19, 25, 61, 31, "default"], [19, 32, 61, 31], [19, 35, 59, 0, "NativeComponentRegistry"], [19, 58, 61, 31], [19, 59, 59, 0, "get"], [19, 62, 61, 31], [19, 63, 59, 0, "nativeComponentName"], [19, 82, 61, 31], [19, 84, 59, 0], [19, 90, 59, 0, "__INTERNAL_VIEW_CONFIG"], [19, 112, 61, 30], [19, 113, 61, 31], [20, 2, 59, 0], [20, 6, 59, 0, "Commands"], [20, 14, 61, 31], [20, 17, 61, 31, "exports"], [20, 24, 61, 31], [20, 25, 61, 31, "Commands"], [20, 33, 61, 31], [20, 36, 59, 0], [21, 4, 59, 0, "highlightTraceUpdates"], [21, 25, 61, 31, "highlightTraceUpdates"], [21, 26, 59, 0, "ref"], [21, 29, 61, 31], [21, 31, 59, 0, "updates"], [21, 38, 61, 31], [21, 40, 59, 0], [22, 6, 59, 0, "dispatchCommand"], [22, 21, 61, 31], [22, 22, 59, 0, "ref"], [22, 25, 61, 31], [22, 27, 59, 0], [22, 50, 61, 31], [22, 52, 59, 0], [22, 53, 59, 0, "updates"], [22, 60, 61, 31], [22, 61, 61, 30], [22, 62, 61, 31], [23, 4, 61, 30], [23, 5, 61, 31], [24, 4, 59, 0, "highlightElements"], [24, 21, 61, 31, "highlightElements"], [24, 22, 59, 0, "ref"], [24, 25, 61, 31], [24, 27, 59, 0, "elements"], [24, 35, 61, 31], [24, 37, 59, 0], [25, 6, 59, 0, "dispatchCommand"], [25, 21, 61, 31], [25, 22, 59, 0, "ref"], [25, 25, 61, 31], [25, 27, 59, 0], [25, 46, 61, 31], [25, 48, 59, 0], [25, 49, 59, 0, "elements"], [25, 57, 61, 31], [25, 58, 61, 30], [25, 59, 61, 31], [26, 4, 61, 30], [26, 5, 61, 31], [27, 4, 59, 0, "clearElementsHighlights"], [27, 27, 61, 31, "clearElementsHighlights"], [27, 28, 59, 0, "ref"], [27, 31, 61, 31], [27, 33, 59, 0], [28, 6, 59, 0, "dispatchCommand"], [28, 21, 61, 31], [28, 22, 59, 0, "ref"], [28, 25, 61, 31], [28, 27, 59, 0], [28, 52, 61, 31], [28, 54, 59, 0], [28, 56, 61, 30], [28, 57, 61, 31], [29, 4, 61, 30], [30, 2, 61, 30], [30, 3, 61, 31], [31, 0, 61, 31], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}