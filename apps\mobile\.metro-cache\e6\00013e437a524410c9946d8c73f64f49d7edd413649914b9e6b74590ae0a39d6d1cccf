{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 55, "index": 69}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 189}, "end": {"line": 6, "column": 43, "index": 232}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "./useEvent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 390}, "end": {"line": 13, "column": 38, "index": 428}}], "key": "agcKO4KjKVVd8qmhkCqgPk8SZT0=", "exportNames": ["*"]}}, {"name": "./useSharedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 429}, "end": {"line": 14, "column": 50, "index": 479}}], "key": "6yldmc0IldDX63zJLZukWRMfHng=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useScrollViewOffset = void 0;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _PlatformChecker = require(_dependencyMap[1], \"../PlatformChecker\");\n  var _useEvent = require(_dependencyMap[2], \"./useEvent\");\n  var _useSharedValue = require(_dependencyMap[3], \"./useSharedValue\");\n  var IS_WEB = (0, _PlatformChecker.isWeb)();\n\n  /**\n   * Lets you synchronously get the current offset of a `ScrollView`.\n   *\n   * @param animatedRef - An [animated\n   *   ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef)\n   *   attached to an Animated.ScrollView component.\n   * @returns A shared value which holds the current offset of the `ScrollView`.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/scroll/useScrollViewOffset\n   */\n  var useScrollViewOffset = exports.useScrollViewOffset = IS_WEB ? useScrollViewOffsetWeb : useScrollViewOffsetNative;\n  var _worklet_11808413640710_init_data = {\n    code: \"function useScrollViewOffsetTs1(){const{animatedRef,getWebScrollableElement,offset}=this.__closure;if(animatedRef){const element=getWebScrollableElement(animatedRef.current);offset.value=element.scrollLeft===0?element.scrollTop:element.scrollLeft;}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\hook\\\\useScrollViewOffset.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useScrollViewOffsetTs1\\\",\\\"animatedRef\\\",\\\"getWebScrollableElement\\\",\\\"offset\\\",\\\"__closure\\\",\\\"element\\\",\\\"current\\\",\\\"value\\\",\\\"scrollLeft\\\",\\\"scrollTop\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/hook/useScrollViewOffset.ts\\\"],\\\"mappings\\\":\\\"AAqCmC,SAAAA,sBAAMA,CAAA,QAAAC,WAAA,CAAAC,uBAAA,CAAAC,MAAA,OAAAC,SAAA,CAErC,GAAIH,WAAW,CAAE,CACf,KAAM,CAAAI,OAAO,CAAGH,uBAAuB,CAACD,WAAW,CAACK,OAAO,CAAC,CAE5DH,MAAM,CAACI,KAAK,CACVF,OAAO,CAACG,UAAU,GAAK,CAAC,CAAGH,OAAO,CAACI,SAAS,CAAGJ,OAAO,CAACG,UAAU,CACrE,CAEF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function useScrollViewOffsetWeb(animatedRef, providedOffset) {\n    var internalOffset = (0, _useSharedValue.useSharedValue)(0);\n    var offset = (0, _react.useRef)(providedOffset ?? internalOffset).current;\n    var eventHandler = (0, _react.useCallback)(function () {\n      var _e = [new global.Error(), -4, -27];\n      var useScrollViewOffsetTs1 = function () {\n        if (animatedRef) {\n          var element = getWebScrollableElement(animatedRef.current);\n          // scrollLeft is the X axis scrolled offset, works properly also with RTL layout\n          offset.value = element.scrollLeft === 0 ? element.scrollTop : element.scrollLeft;\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n      };\n      useScrollViewOffsetTs1.__closure = {\n        animatedRef,\n        getWebScrollableElement,\n        offset\n      };\n      useScrollViewOffsetTs1.__workletHash = 11808413640710;\n      useScrollViewOffsetTs1.__initData = _worklet_11808413640710_init_data;\n      useScrollViewOffsetTs1.__stackDetails = _e;\n      return useScrollViewOffsetTs1;\n    }(), [animatedRef, animatedRef?.current]);\n    (0, _react.useEffect)(() => {\n      var element = animatedRef?.current ? getWebScrollableElement(animatedRef.current) : null;\n      if (element) {\n        element.addEventListener('scroll', eventHandler);\n      }\n      return () => {\n        if (element) {\n          element.removeEventListener('scroll', eventHandler);\n        }\n      };\n      // React here has a problem with `animatedRef.current` since a Ref .current\n      // field shouldn't be used as a dependency. However, in this case we have\n      // to do it this way.\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [animatedRef, animatedRef?.current, eventHandler]);\n    return offset;\n  }\n  var _worklet_1312696806867_init_data = {\n    code: \"function useScrollViewOffsetTs2(event){const{offset}=this.__closure;offset.value=event.contentOffset.x===0?event.contentOffset.y:event.contentOffset.x;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\hook\\\\useScrollViewOffset.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useScrollViewOffsetTs2\\\",\\\"event\\\",\\\"offset\\\",\\\"__closure\\\",\\\"value\\\",\\\"contentOffset\\\",\\\"x\\\",\\\"y\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/hook/useScrollViewOffset.ts\\\"],\\\"mappings\\\":\\\"AA8EI,QAAC,CAAAA,sBAAiCA,CAAAC,KAAA,QAAAC,MAAA,OAAAC,SAAA,CAEhCD,MAAM,CAACE,KAAK,CACVH,KAAK,CAACI,aAAa,CAACC,CAAC,GAAK,CAAC,CACvBL,KAAK,CAACI,aAAa,CAACE,CAAC,CACrBN,KAAK,CAACI,aAAa,CAACC,CAAC,CAC7B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function useScrollViewOffsetNative(animatedRef, providedOffset) {\n    var internalOffset = (0, _useSharedValue.useSharedValue)(0);\n    var offset = (0, _react.useRef)(providedOffset ?? internalOffset).current;\n    var eventHandler = (0, _useEvent.useEvent)(function () {\n      var _e = [new global.Error(), -2, -27];\n      var useScrollViewOffsetTs2 = function (event) {\n        offset.value = event.contentOffset.x === 0 ? event.contentOffset.y : event.contentOffset.x;\n      };\n      useScrollViewOffsetTs2.__closure = {\n        offset\n      };\n      useScrollViewOffsetTs2.__workletHash = 1312696806867;\n      useScrollViewOffsetTs2.__initData = _worklet_1312696806867_init_data;\n      useScrollViewOffsetTs2.__stackDetails = _e;\n      return useScrollViewOffsetTs2;\n    }(), scrollNativeEventNames\n    // Read https://github.com/software-mansion/react-native-reanimated/pull/5056\n    // for more information about this cast.\n    );\n    (0, _react.useEffect)(() => {\n      var elementTag = animatedRef?.getTag() ?? null;\n      if (elementTag) {\n        eventHandler.workletEventHandler.registerForEvents(elementTag);\n      }\n      return () => {\n        if (elementTag) {\n          eventHandler.workletEventHandler.unregisterFromEvents(elementTag);\n        }\n      };\n      // React here has a problem with `animatedRef.current` since a Ref .current\n      // field shouldn't be used as a dependency. However, in this case we have\n      // to do it this way.\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [animatedRef, animatedRef?.current, eventHandler]);\n    return offset;\n  }\n  function getWebScrollableElement(scrollComponent) {\n    return scrollComponent?.getScrollableNode() ?? scrollComponent;\n  }\n  var scrollNativeEventNames = ['onScroll', 'onScrollBeginDrag', 'onScrollEndDrag', 'onMomentumScrollBegin', 'onMomentumScrollEnd'];\n});", "lineCount": 116, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useScrollViewOffset"], [7, 29, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 6, 0], [9, 6, 6, 0, "_PlatformChecker"], [9, 22, 6, 0], [9, 25, 6, 0, "require"], [9, 32, 6, 0], [9, 33, 6, 0, "_dependencyMap"], [9, 47, 6, 0], [10, 2, 13, 0], [10, 6, 13, 0, "_useEvent"], [10, 15, 13, 0], [10, 18, 13, 0, "require"], [10, 25, 13, 0], [10, 26, 13, 0, "_dependencyMap"], [10, 40, 13, 0], [11, 2, 14, 0], [11, 6, 14, 0, "_useSharedValue"], [11, 21, 14, 0], [11, 24, 14, 0, "require"], [11, 31, 14, 0], [11, 32, 14, 0, "_dependencyMap"], [11, 46, 14, 0], [12, 2, 16, 0], [12, 6, 16, 6, "IS_WEB"], [12, 12, 16, 12], [12, 15, 16, 15], [12, 19, 16, 15, "isWeb"], [12, 41, 16, 20], [12, 43, 16, 21], [12, 44, 16, 22], [14, 2, 18, 0], [15, 0, 19, 0], [16, 0, 20, 0], [17, 0, 21, 0], [18, 0, 22, 0], [19, 0, 23, 0], [20, 0, 24, 0], [21, 0, 25, 0], [22, 0, 26, 0], [23, 2, 27, 7], [23, 6, 27, 13, "useScrollViewOffset"], [23, 25, 27, 32], [23, 28, 27, 32, "exports"], [23, 35, 27, 32], [23, 36, 27, 32, "useScrollViewOffset"], [23, 55, 27, 32], [23, 58, 27, 35, "IS_WEB"], [23, 64, 27, 41], [23, 67, 28, 4, "useScrollViewOffsetWeb"], [23, 89, 28, 26], [23, 92, 29, 4, "useScrollViewOffsetNative"], [23, 117, 29, 29], [24, 2, 29, 30], [24, 6, 29, 30, "_worklet_11808413640710_init_data"], [24, 39, 29, 30], [25, 4, 29, 30, "code"], [25, 8, 29, 30], [26, 4, 29, 30, "location"], [26, 12, 29, 30], [27, 4, 29, 30, "sourceMap"], [27, 13, 29, 30], [28, 4, 29, 30, "version"], [28, 11, 29, 30], [29, 2, 29, 30], [30, 2, 31, 0], [30, 11, 31, 9, "useScrollViewOffsetWeb"], [30, 33, 31, 31, "useScrollViewOffsetWeb"], [30, 34, 32, 2, "animatedRef"], [30, 45, 32, 53], [30, 47, 33, 2, "providedOffset"], [30, 61, 33, 38], [30, 63, 34, 23], [31, 4, 35, 2], [31, 8, 35, 8, "internalOffset"], [31, 22, 35, 22], [31, 25, 35, 25], [31, 29, 35, 25, "useSharedValue"], [31, 59, 35, 39], [31, 61, 35, 40], [31, 62, 35, 41], [31, 63, 35, 42], [32, 4, 36, 2], [32, 8, 36, 8, "offset"], [32, 14, 36, 14], [32, 17, 36, 17], [32, 21, 36, 17, "useRef"], [32, 34, 36, 23], [32, 36, 36, 24, "providedOffset"], [32, 50, 36, 38], [32, 54, 36, 42, "internalOffset"], [32, 68, 36, 56], [32, 69, 36, 57], [32, 70, 36, 58, "current"], [32, 77, 36, 65], [33, 4, 38, 2], [33, 8, 38, 8, "<PERSON><PERSON><PERSON><PERSON>"], [33, 20, 38, 20], [33, 23, 38, 23], [33, 27, 38, 23, "useCallback"], [33, 45, 38, 34], [33, 47, 38, 35], [34, 6, 38, 35], [34, 10, 38, 35, "_e"], [34, 12, 38, 35], [34, 20, 38, 35, "global"], [34, 26, 38, 35], [34, 27, 38, 35, "Error"], [34, 32, 38, 35], [35, 6, 38, 35], [35, 10, 38, 35, "useScrollViewOffsetTs1"], [35, 32, 38, 35], [35, 44, 38, 35, "useScrollViewOffsetTs1"], [35, 45, 38, 35], [35, 47, 38, 41], [36, 8, 40, 4], [36, 12, 40, 8, "animatedRef"], [36, 23, 40, 19], [36, 25, 40, 21], [37, 10, 41, 6], [37, 14, 41, 12, "element"], [37, 21, 41, 19], [37, 24, 41, 22, "getWebScrollableElement"], [37, 47, 41, 45], [37, 48, 41, 46, "animatedRef"], [37, 59, 41, 57], [37, 60, 41, 58, "current"], [37, 67, 41, 65], [37, 68, 41, 66], [38, 10, 42, 6], [39, 10, 43, 6, "offset"], [39, 16, 43, 12], [39, 17, 43, 13, "value"], [39, 22, 43, 18], [39, 25, 44, 8, "element"], [39, 32, 44, 15], [39, 33, 44, 16, "scrollLeft"], [39, 43, 44, 26], [39, 48, 44, 31], [39, 49, 44, 32], [39, 52, 44, 35, "element"], [39, 59, 44, 42], [39, 60, 44, 43, "scrollTop"], [39, 69, 44, 52], [39, 72, 44, 55, "element"], [39, 79, 44, 62], [39, 80, 44, 63, "scrollLeft"], [39, 90, 44, 73], [40, 8, 45, 4], [41, 8, 46, 4], [42, 6, 47, 2], [42, 7, 47, 3], [43, 6, 47, 3, "useScrollViewOffsetTs1"], [43, 28, 47, 3], [43, 29, 47, 3, "__closure"], [43, 38, 47, 3], [44, 8, 47, 3, "animatedRef"], [44, 19, 47, 3], [45, 8, 47, 3, "getWebScrollableElement"], [45, 31, 47, 3], [46, 8, 47, 3, "offset"], [47, 6, 47, 3], [48, 6, 47, 3, "useScrollViewOffsetTs1"], [48, 28, 47, 3], [48, 29, 47, 3, "__workletHash"], [48, 42, 47, 3], [49, 6, 47, 3, "useScrollViewOffsetTs1"], [49, 28, 47, 3], [49, 29, 47, 3, "__initData"], [49, 39, 47, 3], [49, 42, 47, 3, "_worklet_11808413640710_init_data"], [49, 75, 47, 3], [50, 6, 47, 3, "useScrollViewOffsetTs1"], [50, 28, 47, 3], [50, 29, 47, 3, "__stackDetails"], [50, 43, 47, 3], [50, 46, 47, 3, "_e"], [50, 48, 47, 3], [51, 6, 47, 3], [51, 13, 47, 3, "useScrollViewOffsetTs1"], [51, 35, 47, 3], [52, 4, 47, 3], [52, 5, 38, 35], [52, 9, 47, 5], [52, 10, 47, 6, "animatedRef"], [52, 21, 47, 17], [52, 23, 47, 19, "animatedRef"], [52, 34, 47, 30], [52, 36, 47, 32, "current"], [52, 43, 47, 39], [52, 44, 47, 40], [52, 45, 47, 41], [53, 4, 49, 2], [53, 8, 49, 2, "useEffect"], [53, 24, 49, 11], [53, 26, 49, 12], [53, 32, 49, 18], [54, 6, 50, 4], [54, 10, 50, 10, "element"], [54, 17, 50, 17], [54, 20, 50, 20, "animatedRef"], [54, 31, 50, 31], [54, 33, 50, 33, "current"], [54, 40, 50, 40], [54, 43, 51, 8, "getWebScrollableElement"], [54, 66, 51, 31], [54, 67, 51, 32, "animatedRef"], [54, 78, 51, 43], [54, 79, 51, 44, "current"], [54, 86, 51, 51], [54, 87, 51, 52], [54, 90, 52, 8], [54, 94, 52, 12], [55, 6, 54, 4], [55, 10, 54, 8, "element"], [55, 17, 54, 15], [55, 19, 54, 17], [56, 8, 55, 6, "element"], [56, 15, 55, 13], [56, 16, 55, 14, "addEventListener"], [56, 32, 55, 30], [56, 33, 55, 31], [56, 41, 55, 39], [56, 43, 55, 41, "<PERSON><PERSON><PERSON><PERSON>"], [56, 55, 55, 53], [56, 56, 55, 54], [57, 6, 56, 4], [58, 6, 57, 4], [58, 13, 57, 11], [58, 19, 57, 17], [59, 8, 58, 6], [59, 12, 58, 10, "element"], [59, 19, 58, 17], [59, 21, 58, 19], [60, 10, 59, 8, "element"], [60, 17, 59, 15], [60, 18, 59, 16, "removeEventListener"], [60, 37, 59, 35], [60, 38, 59, 36], [60, 46, 59, 44], [60, 48, 59, 46, "<PERSON><PERSON><PERSON><PERSON>"], [60, 60, 59, 58], [60, 61, 59, 59], [61, 8, 60, 6], [62, 6, 61, 4], [62, 7, 61, 5], [63, 6, 62, 4], [64, 6, 63, 4], [65, 6, 64, 4], [66, 6, 65, 4], [67, 4, 66, 2], [67, 5, 66, 3], [67, 7, 66, 5], [67, 8, 66, 6, "animatedRef"], [67, 19, 66, 17], [67, 21, 66, 19, "animatedRef"], [67, 32, 66, 30], [67, 34, 66, 32, "current"], [67, 41, 66, 39], [67, 43, 66, 41, "<PERSON><PERSON><PERSON><PERSON>"], [67, 55, 66, 53], [67, 56, 66, 54], [67, 57, 66, 55], [68, 4, 68, 2], [68, 11, 68, 9, "offset"], [68, 17, 68, 15], [69, 2, 69, 0], [70, 2, 69, 1], [70, 6, 69, 1, "_worklet_1312696806867_init_data"], [70, 38, 69, 1], [71, 4, 69, 1, "code"], [71, 8, 69, 1], [72, 4, 69, 1, "location"], [72, 12, 69, 1], [73, 4, 69, 1, "sourceMap"], [73, 13, 69, 1], [74, 4, 69, 1, "version"], [74, 11, 69, 1], [75, 2, 69, 1], [76, 2, 71, 0], [76, 11, 71, 9, "useScrollViewOffsetNative"], [76, 36, 71, 34, "useScrollViewOffsetNative"], [76, 37, 72, 2, "animatedRef"], [76, 48, 72, 53], [76, 50, 73, 2, "providedOffset"], [76, 64, 73, 38], [76, 66, 74, 23], [77, 4, 75, 2], [77, 8, 75, 8, "internalOffset"], [77, 22, 75, 22], [77, 25, 75, 25], [77, 29, 75, 25, "useSharedValue"], [77, 59, 75, 39], [77, 61, 75, 40], [77, 62, 75, 41], [77, 63, 75, 42], [78, 4, 76, 2], [78, 8, 76, 8, "offset"], [78, 14, 76, 14], [78, 17, 76, 17], [78, 21, 76, 17, "useRef"], [78, 34, 76, 23], [78, 36, 76, 24, "providedOffset"], [78, 50, 76, 38], [78, 54, 76, 42, "internalOffset"], [78, 68, 76, 56], [78, 69, 76, 57], [78, 70, 76, 58, "current"], [78, 77, 76, 65], [79, 4, 78, 2], [79, 8, 78, 8, "<PERSON><PERSON><PERSON><PERSON>"], [79, 20, 78, 20], [79, 23, 78, 23], [79, 27, 78, 23, "useEvent"], [79, 45, 78, 31], [79, 47, 79, 4], [80, 6, 79, 4], [80, 10, 79, 4, "_e"], [80, 12, 79, 4], [80, 20, 79, 4, "global"], [80, 26, 79, 4], [80, 27, 79, 4, "Error"], [80, 32, 79, 4], [81, 6, 79, 4], [81, 10, 79, 4, "useScrollViewOffsetTs2"], [81, 32, 79, 4], [81, 44, 79, 4, "useScrollViewOffsetTs2"], [81, 45, 79, 5, "event"], [81, 50, 79, 33], [81, 52, 79, 38], [82, 8, 81, 6, "offset"], [82, 14, 81, 12], [82, 15, 81, 13, "value"], [82, 20, 81, 18], [82, 23, 82, 8, "event"], [82, 28, 82, 13], [82, 29, 82, 14, "contentOffset"], [82, 42, 82, 27], [82, 43, 82, 28, "x"], [82, 44, 82, 29], [82, 49, 82, 34], [82, 50, 82, 35], [82, 53, 83, 12, "event"], [82, 58, 83, 17], [82, 59, 83, 18, "contentOffset"], [82, 72, 83, 31], [82, 73, 83, 32, "y"], [82, 74, 83, 33], [82, 77, 84, 12, "event"], [82, 82, 84, 17], [82, 83, 84, 18, "contentOffset"], [82, 96, 84, 31], [82, 97, 84, 32, "x"], [82, 98, 84, 33], [83, 6, 85, 4], [83, 7, 85, 5], [84, 6, 85, 5, "useScrollViewOffsetTs2"], [84, 28, 85, 5], [84, 29, 85, 5, "__closure"], [84, 38, 85, 5], [85, 8, 85, 5, "offset"], [86, 6, 85, 5], [87, 6, 85, 5, "useScrollViewOffsetTs2"], [87, 28, 85, 5], [87, 29, 85, 5, "__workletHash"], [87, 42, 85, 5], [88, 6, 85, 5, "useScrollViewOffsetTs2"], [88, 28, 85, 5], [88, 29, 85, 5, "__initData"], [88, 39, 85, 5], [88, 42, 85, 5, "_worklet_1312696806867_init_data"], [88, 74, 85, 5], [89, 6, 85, 5, "useScrollViewOffsetTs2"], [89, 28, 85, 5], [89, 29, 85, 5, "__stackDetails"], [89, 43, 85, 5], [89, 46, 85, 5, "_e"], [89, 48, 85, 5], [90, 6, 85, 5], [90, 13, 85, 5, "useScrollViewOffsetTs2"], [90, 35, 85, 5], [91, 4, 85, 5], [91, 5, 79, 4], [91, 9, 86, 4, "scrollNativeEventNames"], [92, 4, 87, 4], [93, 4, 88, 4], [94, 4, 89, 2], [94, 5, 89, 61], [95, 4, 91, 2], [95, 8, 91, 2, "useEffect"], [95, 24, 91, 11], [95, 26, 91, 12], [95, 32, 91, 18], [96, 6, 92, 4], [96, 10, 92, 10, "elementTag"], [96, 20, 92, 20], [96, 23, 92, 23, "animatedRef"], [96, 34, 92, 34], [96, 36, 92, 36, "getTag"], [96, 42, 92, 42], [96, 43, 92, 43], [96, 44, 92, 44], [96, 48, 92, 48], [96, 52, 92, 52], [97, 6, 94, 4], [97, 10, 94, 8, "elementTag"], [97, 20, 94, 18], [97, 22, 94, 20], [98, 8, 95, 6, "<PERSON><PERSON><PERSON><PERSON>"], [98, 20, 95, 18], [98, 21, 95, 19, "workletEventHandler"], [98, 40, 95, 38], [98, 41, 95, 39, "registerForEvents"], [98, 58, 95, 56], [98, 59, 95, 57, "elementTag"], [98, 69, 95, 67], [98, 70, 95, 68], [99, 6, 96, 4], [100, 6, 97, 4], [100, 13, 97, 11], [100, 19, 97, 17], [101, 8, 98, 6], [101, 12, 98, 10, "elementTag"], [101, 22, 98, 20], [101, 24, 98, 22], [102, 10, 99, 8, "<PERSON><PERSON><PERSON><PERSON>"], [102, 22, 99, 20], [102, 23, 99, 21, "workletEventHandler"], [102, 42, 99, 40], [102, 43, 99, 41, "unregisterFromEvents"], [102, 63, 99, 61], [102, 64, 99, 62, "elementTag"], [102, 74, 99, 72], [102, 75, 99, 73], [103, 8, 100, 6], [104, 6, 101, 4], [104, 7, 101, 5], [105, 6, 102, 4], [106, 6, 103, 4], [107, 6, 104, 4], [108, 6, 105, 4], [109, 4, 106, 2], [109, 5, 106, 3], [109, 7, 106, 5], [109, 8, 106, 6, "animatedRef"], [109, 19, 106, 17], [109, 21, 106, 19, "animatedRef"], [109, 32, 106, 30], [109, 34, 106, 32, "current"], [109, 41, 106, 39], [109, 43, 106, 41, "<PERSON><PERSON><PERSON><PERSON>"], [109, 55, 106, 53], [109, 56, 106, 54], [109, 57, 106, 55], [110, 4, 108, 2], [110, 11, 108, 9, "offset"], [110, 17, 108, 15], [111, 2, 109, 0], [112, 2, 111, 0], [112, 11, 111, 9, "getWebScrollableElement"], [112, 34, 111, 32, "getWebScrollableElement"], [112, 35, 112, 2, "scrollComponent"], [112, 50, 112, 44], [112, 52, 113, 15], [113, 4, 114, 2], [113, 11, 115, 5, "scrollComponent"], [113, 26, 115, 20], [113, 28, 115, 22, "getScrollableNode"], [113, 45, 115, 39], [113, 46, 115, 40], [113, 47, 115, 41], [113, 51, 116, 4, "scrollComponent"], [113, 66, 116, 19], [114, 2, 118, 0], [115, 2, 120, 0], [115, 6, 120, 6, "scrollNativeEventNames"], [115, 28, 120, 28], [115, 31, 120, 31], [115, 32, 121, 2], [115, 42, 121, 12], [115, 44, 122, 2], [115, 63, 122, 21], [115, 65, 123, 2], [115, 82, 123, 19], [115, 84, 124, 2], [115, 107, 124, 25], [115, 109, 125, 2], [115, 130, 125, 23], [115, 131, 126, 1], [116, 0, 126, 2], [116, 3]], "functionMap": {"names": ["<global>", "useScrollViewOffsetWeb", "<PERSON><PERSON><PERSON><PERSON>", "useEffect$argument_0", "<anonymous>", "useScrollViewOffsetNative", "useEvent$argument_0", "getWebScrollableElement"], "mappings": "AAA;AC8B;mCCO;GDS;YEE;WCQ;KDI;GFK;CDG;AKE;ICQ;KDM;YFM;WCM;KDI;GEK;CLG;AOE;CPO"}}, "type": "js/module"}]}