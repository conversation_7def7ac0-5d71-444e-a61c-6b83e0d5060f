{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../src/private/components/SafeAreaView_INTERNAL_DO_NOT_USE", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 89}}], "key": "Ff705APltHsg0/gU8+eOKZBELY8=", "exportNames": ["*"]}}, {"name": "../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 43}}], "key": "G/V58dT936wq645V8EjZl0XZN3w=", "exportNames": ["*"]}}, {"name": "../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 50}}], "key": "4Y0hmo08o8yJvREbRM/f/cgl9pQ=", "exportNames": ["*"]}}, {"name": "./Data/LogBoxData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 48}}], "key": "FcZz6T1PfQ3V75b51ZE2xBq3vms=", "exportNames": ["*"]}}, {"name": "./Data/LogBoxLog", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 41}}], "key": "YvSnG7eZ7+wBLcrtWA0WjccojTM=", "exportNames": ["*"]}}, {"name": "./UI/LogBoxNotification", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 60}}], "key": "ZhnsFrf+rkId8khtlHDpRQufIKs=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports._LogBoxNotificationContainer = _LogBoxNotificationContainer;\n  exports.default = void 0;\n  var _SafeAreaView_INTERNAL_DO_NOT_USE = _interopRequireDefault(require(_dependencyMap[1], \"../../src/private/components/SafeAreaView_INTERNAL_DO_NOT_USE\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"../StyleSheet/StyleSheet\"));\n  var LogBoxData = _interopRequireWildcard(require(_dependencyMap[4], \"./Data/LogBoxData\"));\n  var _LogBoxLog = _interopRequireDefault(require(_dependencyMap[5], \"./Data/LogBoxLog\"));\n  var _LogBoxNotification = _interopRequireDefault(require(_dependencyMap[6], \"./UI/LogBoxNotification\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[7], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[8], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\LogBoxNotificationContainer.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _LogBoxNotificationContainer(props) {\n    var logs = props.logs;\n    var onDismissWarns = () => {\n      LogBoxData.clearWarnings();\n    };\n    var onDismissErrors = () => {\n      LogBoxData.clearErrors();\n    };\n    var setSelectedLog = index => {\n      LogBoxData.setSelectedLog(index);\n    };\n    function openLog(log) {\n      if (log.onNotificationPress) {\n        log.onNotificationPress();\n        return;\n      }\n      var index = logs.length - 1;\n      while (index > 0 && logs[index] !== log) {\n        index -= 1;\n      }\n      setSelectedLog(index);\n    }\n    if (logs.length === 0 || props.isDisabled === true) {\n      return null;\n    }\n    var warnings = logs.filter(log => log.level === 'warn');\n    var errors = logs.filter(log => log.level === 'error' || log.level === 'fatal');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_SafeAreaView_INTERNAL_DO_NOT_USE.default, {\n      style: styles.list,\n      children: [warnings.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.toast,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxNotification.default, {\n          log: warnings[warnings.length - 1],\n          level: \"warn\",\n          totalLogCount: warnings.length,\n          onPressOpen: () => openLog(warnings[warnings.length - 1]),\n          onPressDismiss: onDismissWarns\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), errors.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.toast,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxNotification.default, {\n          log: errors[errors.length - 1],\n          level: \"error\",\n          totalLogCount: errors.length,\n          onPressOpen: () => openLog(errors[errors.length - 1]),\n          onPressDismiss: onDismissErrors\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _StyleSheet.default.create({\n    list: {\n      bottom: 20,\n      left: 10,\n      right: 10,\n      position: 'absolute'\n    },\n    toast: {\n      borderRadius: 8,\n      marginBottom: 5,\n      overflow: 'hidden'\n    }\n  });\n  var _default = exports.default = LogBoxData.withSubscription(_LogBoxNotificationContainer);\n});", "lineCount": 102, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_SafeAreaView_INTERNAL_DO_NOT_USE"], [8, 39, 11, 0], [8, 42, 11, 0, "_interopRequireDefault"], [8, 64, 11, 0], [8, 65, 11, 0, "require"], [8, 72, 11, 0], [8, 73, 11, 0, "_dependencyMap"], [8, 87, 11, 0], [9, 2, 12, 0], [9, 6, 12, 0, "_View"], [9, 11, 12, 0], [9, 14, 12, 0, "_interopRequireDefault"], [9, 36, 12, 0], [9, 37, 12, 0, "require"], [9, 44, 12, 0], [9, 45, 12, 0, "_dependencyMap"], [9, 59, 12, 0], [10, 2, 13, 0], [10, 6, 13, 0, "_StyleSheet"], [10, 17, 13, 0], [10, 20, 13, 0, "_interopRequireDefault"], [10, 42, 13, 0], [10, 43, 13, 0, "require"], [10, 50, 13, 0], [10, 51, 13, 0, "_dependencyMap"], [10, 65, 13, 0], [11, 2, 14, 0], [11, 6, 14, 0, "LogBoxData"], [11, 16, 14, 0], [11, 19, 14, 0, "_interopRequireWildcard"], [11, 42, 14, 0], [11, 43, 14, 0, "require"], [11, 50, 14, 0], [11, 51, 14, 0, "_dependencyMap"], [11, 65, 14, 0], [12, 2, 15, 0], [12, 6, 15, 0, "_LogBoxLog"], [12, 16, 15, 0], [12, 19, 15, 0, "_interopRequireDefault"], [12, 41, 15, 0], [12, 42, 15, 0, "require"], [12, 49, 15, 0], [12, 50, 15, 0, "_dependencyMap"], [12, 64, 15, 0], [13, 2, 16, 0], [13, 6, 16, 0, "_LogBoxNotification"], [13, 25, 16, 0], [13, 28, 16, 0, "_interopRequireDefault"], [13, 50, 16, 0], [13, 51, 16, 0, "require"], [13, 58, 16, 0], [13, 59, 16, 0, "_dependencyMap"], [13, 73, 16, 0], [14, 2, 17, 0], [14, 6, 17, 0, "React"], [14, 11, 17, 0], [14, 14, 17, 0, "_interopRequireWildcard"], [14, 37, 17, 0], [14, 38, 17, 0, "require"], [14, 45, 17, 0], [14, 46, 17, 0, "_dependencyMap"], [14, 60, 17, 0], [15, 2, 17, 31], [15, 6, 17, 31, "_jsxDevRuntime"], [15, 20, 17, 31], [15, 23, 17, 31, "require"], [15, 30, 17, 31], [15, 31, 17, 31, "_dependencyMap"], [15, 45, 17, 31], [16, 2, 17, 31], [16, 6, 17, 31, "_jsxFileName"], [16, 18, 17, 31], [17, 2, 17, 31], [17, 11, 17, 31, "_interopRequireWildcard"], [17, 35, 17, 31, "e"], [17, 36, 17, 31], [17, 38, 17, 31, "t"], [17, 39, 17, 31], [17, 68, 17, 31, "WeakMap"], [17, 75, 17, 31], [17, 81, 17, 31, "r"], [17, 82, 17, 31], [17, 89, 17, 31, "WeakMap"], [17, 96, 17, 31], [17, 100, 17, 31, "n"], [17, 101, 17, 31], [17, 108, 17, 31, "WeakMap"], [17, 115, 17, 31], [17, 127, 17, 31, "_interopRequireWildcard"], [17, 150, 17, 31], [17, 162, 17, 31, "_interopRequireWildcard"], [17, 163, 17, 31, "e"], [17, 164, 17, 31], [17, 166, 17, 31, "t"], [17, 167, 17, 31], [17, 176, 17, 31, "t"], [17, 177, 17, 31], [17, 181, 17, 31, "e"], [17, 182, 17, 31], [17, 186, 17, 31, "e"], [17, 187, 17, 31], [17, 188, 17, 31, "__esModule"], [17, 198, 17, 31], [17, 207, 17, 31, "e"], [17, 208, 17, 31], [17, 214, 17, 31, "o"], [17, 215, 17, 31], [17, 217, 17, 31, "i"], [17, 218, 17, 31], [17, 220, 17, 31, "f"], [17, 221, 17, 31], [17, 226, 17, 31, "__proto__"], [17, 235, 17, 31], [17, 243, 17, 31, "default"], [17, 250, 17, 31], [17, 252, 17, 31, "e"], [17, 253, 17, 31], [17, 270, 17, 31, "e"], [17, 271, 17, 31], [17, 294, 17, 31, "e"], [17, 295, 17, 31], [17, 320, 17, 31, "e"], [17, 321, 17, 31], [17, 330, 17, 31, "f"], [17, 331, 17, 31], [17, 337, 17, 31, "o"], [17, 338, 17, 31], [17, 341, 17, 31, "t"], [17, 342, 17, 31], [17, 345, 17, 31, "n"], [17, 346, 17, 31], [17, 349, 17, 31, "r"], [17, 350, 17, 31], [17, 358, 17, 31, "o"], [17, 359, 17, 31], [17, 360, 17, 31, "has"], [17, 363, 17, 31], [17, 364, 17, 31, "e"], [17, 365, 17, 31], [17, 375, 17, 31, "o"], [17, 376, 17, 31], [17, 377, 17, 31, "get"], [17, 380, 17, 31], [17, 381, 17, 31, "e"], [17, 382, 17, 31], [17, 385, 17, 31, "o"], [17, 386, 17, 31], [17, 387, 17, 31, "set"], [17, 390, 17, 31], [17, 391, 17, 31, "e"], [17, 392, 17, 31], [17, 394, 17, 31, "f"], [17, 395, 17, 31], [17, 409, 17, 31, "_t"], [17, 411, 17, 31], [17, 415, 17, 31, "e"], [17, 416, 17, 31], [17, 432, 17, 31, "_t"], [17, 434, 17, 31], [17, 441, 17, 31, "hasOwnProperty"], [17, 455, 17, 31], [17, 456, 17, 31, "call"], [17, 460, 17, 31], [17, 461, 17, 31, "e"], [17, 462, 17, 31], [17, 464, 17, 31, "_t"], [17, 466, 17, 31], [17, 473, 17, 31, "i"], [17, 474, 17, 31], [17, 478, 17, 31, "o"], [17, 479, 17, 31], [17, 482, 17, 31, "Object"], [17, 488, 17, 31], [17, 489, 17, 31, "defineProperty"], [17, 503, 17, 31], [17, 508, 17, 31, "Object"], [17, 514, 17, 31], [17, 515, 17, 31, "getOwnPropertyDescriptor"], [17, 539, 17, 31], [17, 540, 17, 31, "e"], [17, 541, 17, 31], [17, 543, 17, 31, "_t"], [17, 545, 17, 31], [17, 552, 17, 31, "i"], [17, 553, 17, 31], [17, 554, 17, 31, "get"], [17, 557, 17, 31], [17, 561, 17, 31, "i"], [17, 562, 17, 31], [17, 563, 17, 31, "set"], [17, 566, 17, 31], [17, 570, 17, 31, "o"], [17, 571, 17, 31], [17, 572, 17, 31, "f"], [17, 573, 17, 31], [17, 575, 17, 31, "_t"], [17, 577, 17, 31], [17, 579, 17, 31, "i"], [17, 580, 17, 31], [17, 584, 17, 31, "f"], [17, 585, 17, 31], [17, 586, 17, 31, "_t"], [17, 588, 17, 31], [17, 592, 17, 31, "e"], [17, 593, 17, 31], [17, 594, 17, 31, "_t"], [17, 596, 17, 31], [17, 607, 17, 31, "f"], [17, 608, 17, 31], [17, 613, 17, 31, "e"], [17, 614, 17, 31], [17, 616, 17, 31, "t"], [17, 617, 17, 31], [18, 2, 25, 7], [18, 11, 25, 16, "_LogBoxNotificationContainer"], [18, 39, 25, 44, "_LogBoxNotificationContainer"], [18, 40, 25, 45, "props"], [18, 45, 25, 57], [18, 47, 25, 71], [19, 4, 26, 2], [19, 8, 26, 9, "logs"], [19, 12, 26, 13], [19, 15, 26, 17, "props"], [19, 20, 26, 22], [19, 21, 26, 9, "logs"], [19, 25, 26, 13], [20, 4, 28, 2], [20, 8, 28, 8, "onDismissWarns"], [20, 22, 28, 22], [20, 25, 28, 25, "onDismissWarns"], [20, 26, 28, 25], [20, 31, 28, 31], [21, 6, 29, 4, "LogBoxData"], [21, 16, 29, 14], [21, 17, 29, 15, "clearWarnings"], [21, 30, 29, 28], [21, 31, 29, 29], [21, 32, 29, 30], [22, 4, 30, 2], [22, 5, 30, 3], [23, 4, 31, 2], [23, 8, 31, 8, "onDismissErrors"], [23, 23, 31, 23], [23, 26, 31, 26, "onDismissErrors"], [23, 27, 31, 26], [23, 32, 31, 32], [24, 6, 32, 4, "LogBoxData"], [24, 16, 32, 14], [24, 17, 32, 15, "clearErrors"], [24, 28, 32, 26], [24, 29, 32, 27], [24, 30, 32, 28], [25, 4, 33, 2], [25, 5, 33, 3], [26, 4, 35, 2], [26, 8, 35, 8, "setSelectedLog"], [26, 22, 35, 22], [26, 25, 35, 26, "index"], [26, 30, 35, 39], [26, 34, 35, 50], [27, 6, 36, 4, "LogBoxData"], [27, 16, 36, 14], [27, 17, 36, 15, "setSelectedLog"], [27, 31, 36, 29], [27, 32, 36, 30, "index"], [27, 37, 36, 35], [27, 38, 36, 36], [28, 4, 37, 2], [28, 5, 37, 3], [29, 4, 39, 2], [29, 13, 39, 11, "openLog"], [29, 20, 39, 18, "openLog"], [29, 21, 39, 19, "log"], [29, 24, 39, 33], [29, 26, 39, 35], [30, 6, 40, 4], [30, 10, 40, 8, "log"], [30, 13, 40, 11], [30, 14, 40, 12, "onNotificationPress"], [30, 33, 40, 31], [30, 35, 40, 33], [31, 8, 41, 6, "log"], [31, 11, 41, 9], [31, 12, 41, 10, "onNotificationPress"], [31, 31, 41, 29], [31, 32, 41, 30], [31, 33, 41, 31], [32, 8, 42, 6], [33, 6, 43, 4], [34, 6, 44, 4], [34, 10, 44, 8, "index"], [34, 15, 44, 13], [34, 18, 44, 16, "logs"], [34, 22, 44, 20], [34, 23, 44, 21, "length"], [34, 29, 44, 27], [34, 32, 44, 30], [34, 33, 44, 31], [35, 6, 47, 4], [35, 13, 47, 11, "index"], [35, 18, 47, 16], [35, 21, 47, 19], [35, 22, 47, 20], [35, 26, 47, 24, "logs"], [35, 30, 47, 28], [35, 31, 47, 29, "index"], [35, 36, 47, 34], [35, 37, 47, 35], [35, 42, 47, 40, "log"], [35, 45, 47, 43], [35, 47, 47, 45], [36, 8, 48, 6, "index"], [36, 13, 48, 11], [36, 17, 48, 15], [36, 18, 48, 16], [37, 6, 49, 4], [38, 6, 50, 4, "setSelectedLog"], [38, 20, 50, 18], [38, 21, 50, 19, "index"], [38, 26, 50, 24], [38, 27, 50, 25], [39, 4, 51, 2], [40, 4, 53, 2], [40, 8, 53, 6, "logs"], [40, 12, 53, 10], [40, 13, 53, 11, "length"], [40, 19, 53, 17], [40, 24, 53, 22], [40, 25, 53, 23], [40, 29, 53, 27, "props"], [40, 34, 53, 32], [40, 35, 53, 33, "isDisabled"], [40, 45, 53, 43], [40, 50, 53, 48], [40, 54, 53, 52], [40, 56, 53, 54], [41, 6, 54, 4], [41, 13, 54, 11], [41, 17, 54, 15], [42, 4, 55, 2], [43, 4, 57, 2], [43, 8, 57, 8, "warnings"], [43, 16, 57, 16], [43, 19, 57, 19, "logs"], [43, 23, 57, 23], [43, 24, 57, 24, "filter"], [43, 30, 57, 30], [43, 31, 57, 31, "log"], [43, 34, 57, 34], [43, 38, 57, 38, "log"], [43, 41, 57, 41], [43, 42, 57, 42, "level"], [43, 47, 57, 47], [43, 52, 57, 52], [43, 58, 57, 58], [43, 59, 57, 59], [44, 4, 58, 2], [44, 8, 58, 8, "errors"], [44, 14, 58, 14], [44, 17, 58, 17, "logs"], [44, 21, 58, 21], [44, 22, 58, 22, "filter"], [44, 28, 58, 28], [44, 29, 59, 4, "log"], [44, 32, 59, 7], [44, 36, 59, 11, "log"], [44, 39, 59, 14], [44, 40, 59, 15, "level"], [44, 45, 59, 20], [44, 50, 59, 25], [44, 57, 59, 32], [44, 61, 59, 36, "log"], [44, 64, 59, 39], [44, 65, 59, 40, "level"], [44, 70, 59, 45], [44, 75, 59, 50], [44, 82, 60, 2], [44, 83, 60, 3], [45, 4, 61, 2], [45, 24, 62, 4], [45, 28, 62, 4, "_jsxDevRuntime"], [45, 42, 62, 4], [45, 43, 62, 4, "jsxDEV"], [45, 49, 62, 4], [45, 51, 62, 5, "_SafeAreaView_INTERNAL_DO_NOT_USE"], [45, 84, 62, 5], [45, 85, 62, 5, "default"], [45, 92, 62, 17], [46, 6, 62, 18, "style"], [46, 11, 62, 23], [46, 13, 62, 25, "styles"], [46, 19, 62, 31], [46, 20, 62, 32, "list"], [46, 24, 62, 37], [47, 6, 62, 37, "children"], [47, 14, 62, 37], [47, 17, 63, 7, "warnings"], [47, 25, 63, 15], [47, 26, 63, 16, "length"], [47, 32, 63, 22], [47, 35, 63, 25], [47, 36, 63, 26], [47, 53, 64, 8], [47, 57, 64, 8, "_jsxDevRuntime"], [47, 71, 64, 8], [47, 72, 64, 8, "jsxDEV"], [47, 78, 64, 8], [47, 80, 64, 9, "_View"], [47, 85, 64, 9], [47, 86, 64, 9, "default"], [47, 93, 64, 13], [48, 8, 64, 14, "style"], [48, 13, 64, 19], [48, 15, 64, 21, "styles"], [48, 21, 64, 27], [48, 22, 64, 28, "toast"], [48, 27, 64, 34], [49, 8, 64, 34, "children"], [49, 16, 64, 34], [49, 31, 65, 10], [49, 35, 65, 10, "_jsxDevRuntime"], [49, 49, 65, 10], [49, 50, 65, 10, "jsxDEV"], [49, 56, 65, 10], [49, 58, 65, 11, "_LogBoxNotification"], [49, 77, 65, 11], [49, 78, 65, 11, "default"], [49, 85, 65, 32], [50, 10, 66, 12, "log"], [50, 13, 66, 15], [50, 15, 66, 17, "warnings"], [50, 23, 66, 25], [50, 24, 66, 26, "warnings"], [50, 32, 66, 34], [50, 33, 66, 35, "length"], [50, 39, 66, 41], [50, 42, 66, 44], [50, 43, 66, 45], [50, 44, 66, 47], [51, 10, 67, 12, "level"], [51, 15, 67, 17], [51, 17, 67, 18], [51, 23, 67, 24], [52, 10, 68, 12, "totalLogCount"], [52, 23, 68, 25], [52, 25, 68, 27, "warnings"], [52, 33, 68, 35], [52, 34, 68, 36, "length"], [52, 40, 68, 43], [53, 10, 69, 12, "onPressOpen"], [53, 21, 69, 23], [53, 23, 69, 25, "onPressOpen"], [53, 24, 69, 25], [53, 29, 69, 31, "openLog"], [53, 36, 69, 38], [53, 37, 69, 39, "warnings"], [53, 45, 69, 47], [53, 46, 69, 48, "warnings"], [53, 54, 69, 56], [53, 55, 69, 57, "length"], [53, 61, 69, 63], [53, 64, 69, 66], [53, 65, 69, 67], [53, 66, 69, 68], [53, 67, 69, 70], [54, 10, 70, 12, "on<PERSON>ress<PERSON><PERSON><PERSON>"], [54, 24, 70, 26], [54, 26, 70, 28, "onDismissWarns"], [55, 8, 70, 43], [56, 10, 70, 43, "fileName"], [56, 18, 70, 43], [56, 20, 70, 43, "_jsxFileName"], [56, 32, 70, 43], [57, 10, 70, 43, "lineNumber"], [57, 20, 70, 43], [58, 10, 70, 43, "columnNumber"], [58, 22, 70, 43], [59, 8, 70, 43], [59, 15, 71, 11], [60, 6, 71, 12], [61, 8, 71, 12, "fileName"], [61, 16, 71, 12], [61, 18, 71, 12, "_jsxFileName"], [61, 30, 71, 12], [62, 8, 71, 12, "lineNumber"], [62, 18, 71, 12], [63, 8, 71, 12, "columnNumber"], [63, 20, 71, 12], [64, 6, 71, 12], [64, 13, 72, 14], [64, 14, 73, 7], [64, 16, 74, 7, "errors"], [64, 22, 74, 13], [64, 23, 74, 14, "length"], [64, 29, 74, 20], [64, 32, 74, 23], [64, 33, 74, 24], [64, 50, 75, 8], [64, 54, 75, 8, "_jsxDevRuntime"], [64, 68, 75, 8], [64, 69, 75, 8, "jsxDEV"], [64, 75, 75, 8], [64, 77, 75, 9, "_View"], [64, 82, 75, 9], [64, 83, 75, 9, "default"], [64, 90, 75, 13], [65, 8, 75, 14, "style"], [65, 13, 75, 19], [65, 15, 75, 21, "styles"], [65, 21, 75, 27], [65, 22, 75, 28, "toast"], [65, 27, 75, 34], [66, 8, 75, 34, "children"], [66, 16, 75, 34], [66, 31, 76, 10], [66, 35, 76, 10, "_jsxDevRuntime"], [66, 49, 76, 10], [66, 50, 76, 10, "jsxDEV"], [66, 56, 76, 10], [66, 58, 76, 11, "_LogBoxNotification"], [66, 77, 76, 11], [66, 78, 76, 11, "default"], [66, 85, 76, 32], [67, 10, 77, 12, "log"], [67, 13, 77, 15], [67, 15, 77, 17, "errors"], [67, 21, 77, 23], [67, 22, 77, 24, "errors"], [67, 28, 77, 30], [67, 29, 77, 31, "length"], [67, 35, 77, 37], [67, 38, 77, 40], [67, 39, 77, 41], [67, 40, 77, 43], [68, 10, 78, 12, "level"], [68, 15, 78, 17], [68, 17, 78, 18], [68, 24, 78, 25], [69, 10, 79, 12, "totalLogCount"], [69, 23, 79, 25], [69, 25, 79, 27, "errors"], [69, 31, 79, 33], [69, 32, 79, 34, "length"], [69, 38, 79, 41], [70, 10, 80, 12, "onPressOpen"], [70, 21, 80, 23], [70, 23, 80, 25, "onPressOpen"], [70, 24, 80, 25], [70, 29, 80, 31, "openLog"], [70, 36, 80, 38], [70, 37, 80, 39, "errors"], [70, 43, 80, 45], [70, 44, 80, 46, "errors"], [70, 50, 80, 52], [70, 51, 80, 53, "length"], [70, 57, 80, 59], [70, 60, 80, 62], [70, 61, 80, 63], [70, 62, 80, 64], [70, 63, 80, 66], [71, 10, 81, 12, "on<PERSON>ress<PERSON><PERSON><PERSON>"], [71, 24, 81, 26], [71, 26, 81, 28, "onDismissErrors"], [72, 8, 81, 44], [73, 10, 81, 44, "fileName"], [73, 18, 81, 44], [73, 20, 81, 44, "_jsxFileName"], [73, 32, 81, 44], [74, 10, 81, 44, "lineNumber"], [74, 20, 81, 44], [75, 10, 81, 44, "columnNumber"], [75, 22, 81, 44], [76, 8, 81, 44], [76, 15, 82, 11], [77, 6, 82, 12], [78, 8, 82, 12, "fileName"], [78, 16, 82, 12], [78, 18, 82, 12, "_jsxFileName"], [78, 30, 82, 12], [79, 8, 82, 12, "lineNumber"], [79, 18, 82, 12], [80, 8, 82, 12, "columnNumber"], [80, 20, 82, 12], [81, 6, 82, 12], [81, 13, 83, 14], [81, 14, 84, 7], [82, 4, 84, 7], [83, 6, 84, 7, "fileName"], [83, 14, 84, 7], [83, 16, 84, 7, "_jsxFileName"], [83, 28, 84, 7], [84, 6, 84, 7, "lineNumber"], [84, 16, 84, 7], [85, 6, 84, 7, "columnNumber"], [85, 18, 84, 7], [86, 4, 84, 7], [86, 11, 85, 18], [86, 12, 85, 19], [87, 2, 87, 0], [88, 2, 89, 0], [88, 6, 89, 6, "styles"], [88, 12, 89, 12], [88, 15, 89, 15, "StyleSheet"], [88, 34, 89, 25], [88, 35, 89, 26, "create"], [88, 41, 89, 32], [88, 42, 89, 33], [89, 4, 90, 2, "list"], [89, 8, 90, 6], [89, 10, 90, 8], [90, 6, 91, 4, "bottom"], [90, 12, 91, 10], [90, 14, 91, 12], [90, 16, 91, 14], [91, 6, 92, 4, "left"], [91, 10, 92, 8], [91, 12, 92, 10], [91, 14, 92, 12], [92, 6, 93, 4, "right"], [92, 11, 93, 9], [92, 13, 93, 11], [92, 15, 93, 13], [93, 6, 94, 4, "position"], [93, 14, 94, 12], [93, 16, 94, 14], [94, 4, 95, 2], [94, 5, 95, 3], [95, 4, 96, 2, "toast"], [95, 9, 96, 7], [95, 11, 96, 9], [96, 6, 97, 4, "borderRadius"], [96, 18, 97, 16], [96, 20, 97, 18], [96, 21, 97, 19], [97, 6, 98, 4, "marginBottom"], [97, 18, 98, 16], [97, 20, 98, 18], [97, 21, 98, 19], [98, 6, 99, 4, "overflow"], [98, 14, 99, 12], [98, 16, 99, 14], [99, 4, 100, 2], [100, 2, 101, 0], [100, 3, 101, 1], [100, 4, 101, 2], [101, 2, 101, 3], [101, 6, 101, 3, "_default"], [101, 14, 101, 3], [101, 17, 101, 3, "exports"], [101, 24, 101, 3], [101, 25, 101, 3, "default"], [101, 32, 101, 3], [101, 35, 103, 16, "LogBoxData"], [101, 45, 103, 26], [101, 46, 103, 27, "withSubscription"], [101, 62, 103, 43], [101, 63, 104, 2, "_LogBoxNotificationContainer"], [101, 91, 105, 0], [101, 92, 105, 1], [102, 0, 105, 1], [102, 3]], "functionMap": {"names": ["<global>", "_LogBoxNotificationContainer", "onDismissWarns", "onDismissErrors", "setSelectedLog", "openLog", "logs.filter$argument_0", "LogBoxLogNotification.props.onPressOpen"], "mappings": "AAA;OCwB;yBCG;GDE;0BEC;GFE;yBGE;GHE;EIE;GJY;+BKM,2BL;IKE,qDL;yBMU,4CN;yBMW,wCN;CDO"}}, "type": "js/module"}]}