{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 78, "index": 78}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "./ExpoFontLoader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 79}, "end": {"line": 2, "column": 46, "index": 125}}], "key": "7dk3JQGwGYesJt8OOG3pkBz+dtE=", "exportNames": ["*"]}}, {"name": "./Font.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 126}, "end": {"line": 3, "column": 43, "index": 169}}], "key": "iwvcxaVgfIXdww6iPrKSgtcaZy8=", "exportNames": ["*"]}}, {"name": "./FontLoader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 170}, "end": {"line": 4, "column": 70, "index": 240}}], "key": "ubgLNxOkixzH8pVapAwap9wQ8XU=", "exportNames": ["*"]}}, {"name": "./memory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 241}, "end": {"line": 5, "column": 124, "index": 365}}], "key": "wlrMBDc1MVhnZOig0xhYu83J328=", "exportNames": ["*"]}}, {"name": "./server", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 366}, "end": {"line": 6, "column": 46, "index": 412}}], "key": "QinwpQMs5c8GY+myVdgXEzx55Tw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"FontDisplay\", {\n    enumerable: true,\n    get: function () {\n      return _Font.FontDisplay;\n    }\n  });\n  exports.getLoadedFonts = getLoadedFonts;\n  exports.isLoaded = isLoaded;\n  exports.isLoading = isLoading;\n  exports.loadAsync = loadAsync;\n  exports.unloadAllAsync = unloadAllAsync;\n  exports.unloadAsync = unloadAsync;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _expoModulesCore = require(_dependencyMap[2], \"expo-modules-core\");\n  var _ExpoFontLoader = _interopRequireDefault(require(_dependencyMap[3], \"./ExpoFontLoader\"));\n  var _Font = require(_dependencyMap[4], \"./Font.types\");\n  var _FontLoader = require(_dependencyMap[5], \"./FontLoader\");\n  var _memory = require(_dependencyMap[6], \"./memory\");\n  var _server = require(_dependencyMap[7], \"./server\");\n  // @needsAudit\n  /**\n   * Synchronously detect if the font for `fontFamily` has finished loading.\n   *\n   * @param fontFamily The name used to load the `FontResource`.\n   * @return Returns `true` if the font has fully loaded.\n   */\n  function isLoaded(fontFamily) {\n    if (_expoModulesCore.Platform.OS === 'web') {\n      return (0, _memory.isLoadedInCache)(fontFamily) || !!_ExpoFontLoader.default.isLoaded(fontFamily);\n    }\n    return (0, _memory.isLoadedNative)(fontFamily);\n  }\n  /**\n   * Synchronously get all the fonts that have been loaded.\n   * This includes fonts that were bundled at build time using the config plugin, as well as those loaded at runtime using `loadAsync`.\n   *\n   * @returns Returns array of strings which you can use as `fontFamily` [style prop](https://reactnative.dev/docs/text#style).\n   */\n  function getLoadedFonts() {\n    return _ExpoFontLoader.default.getLoadedFonts();\n  }\n  // @needsAudit\n  /**\n   * Synchronously detect if the font for `fontFamily` is still being loaded.\n   *\n   * @param fontFamily The name used to load the `FontResource`.\n   * @returns Returns `true` if the font is still loading.\n   */\n  function isLoading(fontFamily) {\n    return fontFamily in _memory.loadPromises;\n  }\n  // @needsAudit\n  /**\n   * An efficient method for loading fonts from static or remote resources which can then be used\n   * with the platform's native text elements. In the browser, this generates a `@font-face` block in\n   * a shared style sheet for fonts. No CSS is needed to use this method.\n   *\n   * > **Note**: We recommend using the [config plugin](#configuration-in-appjsonappconfigjs) instead whenever possible.\n   *\n   * @param fontFamilyOrFontMap String or map of values that can be used as the `fontFamily` [style prop](https://reactnative.dev/docs/text#style)\n   * with React Native `Text` elements.\n   * @param source The font asset that should be loaded into the `fontFamily` namespace.\n   *\n   * @return Returns a promise that fulfils when the font has loaded. Often you may want to wrap the\n   * method in a `try/catch/finally` to ensure the app continues if the font fails to load.\n   */\n  function loadAsync(fontFamilyOrFontMap, source) {\n    // NOTE(EvanBacon): Static render pass on web must be synchronous to collect all fonts.\n    // Because of this, `loadAsync` doesn't use the `async` keyword and deviates from the\n    // standard Expo SDK style guide.\n    var isServer = _expoModulesCore.Platform.OS === 'web' && typeof window === 'undefined';\n    if (typeof fontFamilyOrFontMap === 'object') {\n      if (source) {\n        return Promise.reject(new _expoModulesCore.CodedError(`ERR_FONT_API`, `No fontFamily can be used for the provided source: ${source}. The second argument of \\`loadAsync()\\` can only be used with a \\`string\\` value as the first argument.`));\n      }\n      var fontMap = fontFamilyOrFontMap;\n      var names = Object.keys(fontMap);\n      if (isServer) {\n        names.map(name => (0, _server.registerStaticFont)(name, fontMap[name]));\n        return Promise.resolve();\n      }\n      return Promise.all(names.map(name => loadFontInNamespaceAsync(name, fontMap[name]))).then(() => {});\n    }\n    if (isServer) {\n      (0, _server.registerStaticFont)(fontFamilyOrFontMap, source);\n      return Promise.resolve();\n    }\n    return loadFontInNamespaceAsync(fontFamilyOrFontMap, source);\n  }\n  function loadFontInNamespaceAsync(_x, _x2) {\n    return _loadFontInNamespaceAsync.apply(this, arguments);\n  } // @needsAudit\n  /**\n   * Unloads all the custom fonts. This is used for testing.\n   * @hidden\n   */\n  function _loadFontInNamespaceAsync() {\n    _loadFontInNamespaceAsync = (0, _asyncToGenerator2.default)(function* (fontFamily, source) {\n      if (!source) {\n        throw new _expoModulesCore.CodedError(`ERR_FONT_SOURCE`, `Cannot load null or undefined font source: { \"${fontFamily}\": ${source} }. Expected asset of type \\`FontSource\\` for fontFamily of name: \"${fontFamily}\"`);\n      }\n      // we consult the native module to see if the font is already loaded\n      // this is slower than checking the cache but can help avoid loading the same font n times\n      if (isLoaded(fontFamily)) {\n        return;\n      }\n      if (_memory.loadPromises.hasOwnProperty(fontFamily)) {\n        return _memory.loadPromises[fontFamily];\n      }\n      // Important: we want all callers that concurrently try to load the same font to await the same\n      // promise. If we're here, we haven't created the promise yet. To ensure we create only one\n      // promise in the program, we need to create the promise synchronously without yielding the event\n      // loop from this point.\n      var asset = (0, _FontLoader.getAssetForSource)(source);\n      _memory.loadPromises[fontFamily] = (0, _asyncToGenerator2.default)(function* () {\n        try {\n          yield (0, _FontLoader.loadSingleFontAsync)(fontFamily, asset);\n          (0, _memory.markLoaded)(fontFamily);\n        } finally {\n          delete _memory.loadPromises[fontFamily];\n        }\n      })();\n      yield _memory.loadPromises[fontFamily];\n    });\n    return _loadFontInNamespaceAsync.apply(this, arguments);\n  }\n  function unloadAllAsync() {\n    return _unloadAllAsync.apply(this, arguments);\n  } // @needsAudit\n  /**\n   * Unload custom fonts matching the `fontFamily`s and display values provided.\n   * This is used for testing.\n   *\n   * @param fontFamilyOrFontMap The name or names of the custom fonts that will be unloaded.\n   * @param options When `fontFamilyOrFontMap` is a string, this should be the font source used to load\n   * the custom font originally.\n   * @hidden\n   */\n  function _unloadAllAsync() {\n    _unloadAllAsync = (0, _asyncToGenerator2.default)(function* () {\n      if (!_ExpoFontLoader.default.unloadAllAsync) {\n        throw new _expoModulesCore.UnavailabilityError('expo-font', 'unloadAllAsync');\n      }\n      if (Object.keys(_memory.loadPromises).length) {\n        throw new _expoModulesCore.CodedError(`ERR_UNLOAD`, `Cannot unload fonts while they're still loading: ${Object.keys(_memory.loadPromises).join(', ')}`);\n      }\n      (0, _memory.purgeCache)();\n      yield _ExpoFontLoader.default.unloadAllAsync();\n    });\n    return _unloadAllAsync.apply(this, arguments);\n  }\n  function unloadAsync(_x3, _x4) {\n    return _unloadAsync.apply(this, arguments);\n  }\n  function _unloadAsync() {\n    _unloadAsync = (0, _asyncToGenerator2.default)(function* (fontFamilyOrFontMap, options) {\n      if (!_ExpoFontLoader.default.unloadAsync) {\n        throw new _expoModulesCore.UnavailabilityError('expo-font', 'unloadAsync');\n      }\n      if (typeof fontFamilyOrFontMap === 'object') {\n        if (options) {\n          throw new _expoModulesCore.CodedError(`ERR_FONT_API`, `No fontFamily can be used for the provided options: ${options}. The second argument of \\`unloadAsync()\\` can only be used with a \\`string\\` value as the first argument.`);\n        }\n        var fontMap = fontFamilyOrFontMap;\n        var names = Object.keys(fontMap);\n        yield Promise.all(names.map(name => unloadFontInNamespaceAsync(name, fontMap[name])));\n        return;\n      }\n      return yield unloadFontInNamespaceAsync(fontFamilyOrFontMap, options);\n    });\n    return _unloadAsync.apply(this, arguments);\n  }\n  function unloadFontInNamespaceAsync(_x5, _x6) {\n    return _unloadFontInNamespaceAsync.apply(this, arguments);\n  }\n  function _unloadFontInNamespaceAsync() {\n    _unloadFontInNamespaceAsync = (0, _asyncToGenerator2.default)(function* (fontFamily, options) {\n      if (!isLoaded(fontFamily)) {\n        return;\n      } else {\n        (0, _memory.purgeFontFamilyFromCache)(fontFamily);\n      }\n      // Important: we want all callers that concurrently try to load the same font to await the same\n      // promise. If we're here, we haven't created the promise yet. To ensure we create only one\n      // promise in the program, we need to create the promise synchronously without yielding the event\n      // loop from this point.\n      if (!fontFamily) {\n        throw new _expoModulesCore.CodedError(`ERR_FONT_FAMILY`, `Cannot unload an empty name`);\n      }\n      yield _ExpoFontLoader.default.unloadAsync(fontFamily, options);\n    });\n    return _unloadFontInNamespaceAsync.apply(this, arguments);\n  }\n});", "lineCount": 199, "map": [[19, 2, 1, 0], [19, 6, 1, 0, "_expoModulesCore"], [19, 22, 1, 0], [19, 25, 1, 0, "require"], [19, 32, 1, 0], [19, 33, 1, 0, "_dependencyMap"], [19, 47, 1, 0], [20, 2, 2, 0], [20, 6, 2, 0, "_ExpoFontLoader"], [20, 21, 2, 0], [20, 24, 2, 0, "_interopRequireDefault"], [20, 46, 2, 0], [20, 47, 2, 0, "require"], [20, 54, 2, 0], [20, 55, 2, 0, "_dependencyMap"], [20, 69, 2, 0], [21, 2, 3, 0], [21, 6, 3, 0, "_Font"], [21, 11, 3, 0], [21, 14, 3, 0, "require"], [21, 21, 3, 0], [21, 22, 3, 0, "_dependencyMap"], [21, 36, 3, 0], [22, 2, 4, 0], [22, 6, 4, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [22, 17, 4, 0], [22, 20, 4, 0, "require"], [22, 27, 4, 0], [22, 28, 4, 0, "_dependencyMap"], [22, 42, 4, 0], [23, 2, 5, 0], [23, 6, 5, 0, "_memory"], [23, 13, 5, 0], [23, 16, 5, 0, "require"], [23, 23, 5, 0], [23, 24, 5, 0, "_dependencyMap"], [23, 38, 5, 0], [24, 2, 6, 0], [24, 6, 6, 0, "_server"], [24, 13, 6, 0], [24, 16, 6, 0, "require"], [24, 23, 6, 0], [24, 24, 6, 0, "_dependencyMap"], [24, 38, 6, 0], [25, 2, 7, 0], [26, 2, 8, 0], [27, 0, 9, 0], [28, 0, 10, 0], [29, 0, 11, 0], [30, 0, 12, 0], [31, 0, 13, 0], [32, 2, 14, 7], [32, 11, 14, 16, "isLoaded"], [32, 19, 14, 24, "isLoaded"], [32, 20, 14, 25, "fontFamily"], [32, 30, 14, 35], [32, 32, 14, 37], [33, 4, 15, 4], [33, 8, 15, 8, "Platform"], [33, 33, 15, 16], [33, 34, 15, 17, "OS"], [33, 36, 15, 19], [33, 41, 15, 24], [33, 46, 15, 29], [33, 48, 15, 31], [34, 6, 16, 8], [34, 13, 16, 15], [34, 17, 16, 15, "isLoadedInCache"], [34, 40, 16, 30], [34, 42, 16, 31, "fontFamily"], [34, 52, 16, 41], [34, 53, 16, 42], [34, 57, 16, 46], [34, 58, 16, 47], [34, 59, 16, 48, "ExpoFontLoader"], [34, 82, 16, 62], [34, 83, 16, 63, "isLoaded"], [34, 91, 16, 71], [34, 92, 16, 72, "fontFamily"], [34, 102, 16, 82], [34, 103, 16, 83], [35, 4, 17, 4], [36, 4, 18, 4], [36, 11, 18, 11], [36, 15, 18, 11, "isLoadedNative"], [36, 37, 18, 25], [36, 39, 18, 26, "fontFamily"], [36, 49, 18, 36], [36, 50, 18, 37], [37, 2, 19, 0], [38, 2, 20, 0], [39, 0, 21, 0], [40, 0, 22, 0], [41, 0, 23, 0], [42, 0, 24, 0], [43, 0, 25, 0], [44, 2, 26, 7], [44, 11, 26, 16, "getLoadedFonts"], [44, 25, 26, 30, "getLoadedFonts"], [44, 26, 26, 30], [44, 28, 26, 33], [45, 4, 27, 4], [45, 11, 27, 11, "ExpoFontLoader"], [45, 34, 27, 25], [45, 35, 27, 26, "getLoadedFonts"], [45, 49, 27, 40], [45, 50, 27, 41], [45, 51, 27, 42], [46, 2, 28, 0], [47, 2, 29, 0], [48, 2, 30, 0], [49, 0, 31, 0], [50, 0, 32, 0], [51, 0, 33, 0], [52, 0, 34, 0], [53, 0, 35, 0], [54, 2, 36, 7], [54, 11, 36, 16, "isLoading"], [54, 20, 36, 25, "isLoading"], [54, 21, 36, 26, "fontFamily"], [54, 31, 36, 36], [54, 33, 36, 38], [55, 4, 37, 4], [55, 11, 37, 11, "fontFamily"], [55, 21, 37, 21], [55, 25, 37, 25, "loadPromises"], [55, 45, 37, 37], [56, 2, 38, 0], [57, 2, 39, 0], [58, 2, 40, 0], [59, 0, 41, 0], [60, 0, 42, 0], [61, 0, 43, 0], [62, 0, 44, 0], [63, 0, 45, 0], [64, 0, 46, 0], [65, 0, 47, 0], [66, 0, 48, 0], [67, 0, 49, 0], [68, 0, 50, 0], [69, 0, 51, 0], [70, 0, 52, 0], [71, 0, 53, 0], [72, 2, 54, 7], [72, 11, 54, 16, "loadAsync"], [72, 20, 54, 25, "loadAsync"], [72, 21, 54, 26, "fontFamilyOrFontMap"], [72, 40, 54, 45], [72, 42, 54, 47, "source"], [72, 48, 54, 53], [72, 50, 54, 55], [73, 4, 55, 4], [74, 4, 56, 4], [75, 4, 57, 4], [76, 4, 58, 4], [76, 8, 58, 10, "isServer"], [76, 16, 58, 18], [76, 19, 58, 21, "Platform"], [76, 44, 58, 29], [76, 45, 58, 30, "OS"], [76, 47, 58, 32], [76, 52, 58, 37], [76, 57, 58, 42], [76, 61, 58, 46], [76, 68, 58, 53, "window"], [76, 74, 58, 59], [76, 79, 58, 64], [76, 90, 58, 75], [77, 4, 59, 4], [77, 8, 59, 8], [77, 15, 59, 15, "fontFamilyOrFontMap"], [77, 34, 59, 34], [77, 39, 59, 39], [77, 47, 59, 47], [77, 49, 59, 49], [78, 6, 60, 8], [78, 10, 60, 12, "source"], [78, 16, 60, 18], [78, 18, 60, 20], [79, 8, 61, 12], [79, 15, 61, 19, "Promise"], [79, 22, 61, 26], [79, 23, 61, 27, "reject"], [79, 29, 61, 33], [79, 30, 61, 34], [79, 34, 61, 38, "CodedError"], [79, 61, 61, 48], [79, 62, 61, 49], [79, 76, 61, 63], [79, 78, 61, 65], [79, 132, 61, 119, "source"], [79, 138, 61, 125], [79, 244, 61, 231], [79, 245, 61, 232], [79, 246, 61, 233], [80, 6, 62, 8], [81, 6, 63, 8], [81, 10, 63, 14, "fontMap"], [81, 17, 63, 21], [81, 20, 63, 24, "fontFamilyOrFontMap"], [81, 39, 63, 43], [82, 6, 64, 8], [82, 10, 64, 14, "names"], [82, 15, 64, 19], [82, 18, 64, 22, "Object"], [82, 24, 64, 28], [82, 25, 64, 29, "keys"], [82, 29, 64, 33], [82, 30, 64, 34, "fontMap"], [82, 37, 64, 41], [82, 38, 64, 42], [83, 6, 65, 8], [83, 10, 65, 12, "isServer"], [83, 18, 65, 20], [83, 20, 65, 22], [84, 8, 66, 12, "names"], [84, 13, 66, 17], [84, 14, 66, 18, "map"], [84, 17, 66, 21], [84, 18, 66, 23, "name"], [84, 22, 66, 27], [84, 26, 66, 32], [84, 30, 66, 32, "registerStaticFont"], [84, 56, 66, 50], [84, 58, 66, 51, "name"], [84, 62, 66, 55], [84, 64, 66, 57, "fontMap"], [84, 71, 66, 64], [84, 72, 66, 65, "name"], [84, 76, 66, 69], [84, 77, 66, 70], [84, 78, 66, 71], [84, 79, 66, 72], [85, 8, 67, 12], [85, 15, 67, 19, "Promise"], [85, 22, 67, 26], [85, 23, 67, 27, "resolve"], [85, 30, 67, 34], [85, 31, 67, 35], [85, 32, 67, 36], [86, 6, 68, 8], [87, 6, 69, 8], [87, 13, 69, 15, "Promise"], [87, 20, 69, 22], [87, 21, 69, 23, "all"], [87, 24, 69, 26], [87, 25, 69, 27, "names"], [87, 30, 69, 32], [87, 31, 69, 33, "map"], [87, 34, 69, 36], [87, 35, 69, 38, "name"], [87, 39, 69, 42], [87, 43, 69, 47, "loadFontInNamespaceAsync"], [87, 67, 69, 71], [87, 68, 69, 72, "name"], [87, 72, 69, 76], [87, 74, 69, 78, "fontMap"], [87, 81, 69, 85], [87, 82, 69, 86, "name"], [87, 86, 69, 90], [87, 87, 69, 91], [87, 88, 69, 92], [87, 89, 69, 93], [87, 90, 69, 94], [87, 91, 69, 95, "then"], [87, 95, 69, 99], [87, 96, 69, 100], [87, 102, 69, 106], [87, 103, 69, 108], [87, 104, 69, 109], [87, 105, 69, 110], [88, 4, 70, 4], [89, 4, 71, 4], [89, 8, 71, 8, "isServer"], [89, 16, 71, 16], [89, 18, 71, 18], [90, 6, 72, 8], [90, 10, 72, 8, "registerStaticFont"], [90, 36, 72, 26], [90, 38, 72, 27, "fontFamilyOrFontMap"], [90, 57, 72, 46], [90, 59, 72, 48, "source"], [90, 65, 72, 54], [90, 66, 72, 55], [91, 6, 73, 8], [91, 13, 73, 15, "Promise"], [91, 20, 73, 22], [91, 21, 73, 23, "resolve"], [91, 28, 73, 30], [91, 29, 73, 31], [91, 30, 73, 32], [92, 4, 74, 4], [93, 4, 75, 4], [93, 11, 75, 11, "loadFontInNamespaceAsync"], [93, 35, 75, 35], [93, 36, 75, 36, "fontFamilyOrFontMap"], [93, 55, 75, 55], [93, 57, 75, 57, "source"], [93, 63, 75, 63], [93, 64, 75, 64], [94, 2, 76, 0], [95, 2, 76, 1], [95, 11, 77, 15, "loadFontInNamespaceAsync"], [95, 35, 77, 39, "loadFontInNamespaceAsync"], [95, 36, 77, 39, "_x"], [95, 38, 77, 39], [95, 40, 77, 39, "_x2"], [95, 43, 77, 39], [96, 4, 77, 39], [96, 11, 77, 39, "_loadFontInNamespaceAsync"], [96, 36, 77, 39], [96, 37, 77, 39, "apply"], [96, 42, 77, 39], [96, 49, 77, 39, "arguments"], [96, 58, 77, 39], [97, 2, 77, 39], [97, 4, 105, 0], [98, 2, 106, 0], [99, 0, 107, 0], [100, 0, 108, 0], [101, 0, 109, 0], [102, 2, 106, 0], [102, 11, 106, 0, "_loadFontInNamespaceAsync"], [102, 37, 106, 0], [103, 4, 106, 0, "_loadFontInNamespaceAsync"], [103, 29, 106, 0], [103, 36, 106, 0, "_asyncToGenerator2"], [103, 54, 106, 0], [103, 55, 106, 0, "default"], [103, 62, 106, 0], [103, 64, 77, 0], [103, 75, 77, 40, "fontFamily"], [103, 85, 77, 50], [103, 87, 77, 52, "source"], [103, 93, 77, 58], [103, 95, 77, 60], [104, 6, 78, 4], [104, 10, 78, 8], [104, 11, 78, 9, "source"], [104, 17, 78, 15], [104, 19, 78, 17], [105, 8, 79, 8], [105, 14, 79, 14], [105, 18, 79, 18, "CodedError"], [105, 45, 79, 28], [105, 46, 79, 29], [105, 63, 79, 46], [105, 65, 79, 48], [105, 114, 79, 97, "fontFamily"], [105, 124, 79, 107], [105, 130, 79, 113, "source"], [105, 136, 79, 119], [105, 206, 79, 189, "fontFamily"], [105, 216, 79, 199], [105, 219, 79, 202], [105, 220, 79, 203], [106, 6, 80, 4], [107, 6, 81, 4], [108, 6, 82, 4], [109, 6, 83, 4], [109, 10, 83, 8, "isLoaded"], [109, 18, 83, 16], [109, 19, 83, 17, "fontFamily"], [109, 29, 83, 27], [109, 30, 83, 28], [109, 32, 83, 30], [110, 8, 84, 8], [111, 6, 85, 4], [112, 6, 86, 4], [112, 10, 86, 8, "loadPromises"], [112, 30, 86, 20], [112, 31, 86, 21, "hasOwnProperty"], [112, 45, 86, 35], [112, 46, 86, 36, "fontFamily"], [112, 56, 86, 46], [112, 57, 86, 47], [112, 59, 86, 49], [113, 8, 87, 8], [113, 15, 87, 15, "loadPromises"], [113, 35, 87, 27], [113, 36, 87, 28, "fontFamily"], [113, 46, 87, 38], [113, 47, 87, 39], [114, 6, 88, 4], [115, 6, 89, 4], [116, 6, 90, 4], [117, 6, 91, 4], [118, 6, 92, 4], [119, 6, 93, 4], [119, 10, 93, 10, "asset"], [119, 15, 93, 15], [119, 18, 93, 18], [119, 22, 93, 18, "getAssetForSource"], [119, 51, 93, 35], [119, 53, 93, 36, "source"], [119, 59, 93, 42], [119, 60, 93, 43], [120, 6, 94, 4, "loadPromises"], [120, 26, 94, 16], [120, 27, 94, 17, "fontFamily"], [120, 37, 94, 27], [120, 38, 94, 28], [120, 41, 94, 31], [120, 45, 94, 31, "_asyncToGenerator2"], [120, 63, 94, 31], [120, 64, 94, 31, "default"], [120, 71, 94, 31], [120, 73, 94, 32], [120, 86, 94, 44], [121, 8, 95, 8], [121, 12, 95, 12], [122, 10, 96, 12], [122, 16, 96, 18], [122, 20, 96, 18, "loadSingleFontAsync"], [122, 51, 96, 37], [122, 53, 96, 38, "fontFamily"], [122, 63, 96, 48], [122, 65, 96, 50, "asset"], [122, 70, 96, 55], [122, 71, 96, 56], [123, 10, 97, 12], [123, 14, 97, 12, "markLoaded"], [123, 32, 97, 22], [123, 34, 97, 23, "fontFamily"], [123, 44, 97, 33], [123, 45, 97, 34], [124, 8, 98, 8], [124, 9, 98, 9], [124, 18, 99, 16], [125, 10, 100, 12], [125, 17, 100, 19, "loadPromises"], [125, 37, 100, 31], [125, 38, 100, 32, "fontFamily"], [125, 48, 100, 42], [125, 49, 100, 43], [126, 8, 101, 8], [127, 6, 102, 4], [127, 7, 102, 5], [127, 9, 102, 7], [127, 10, 102, 8], [128, 6, 103, 4], [128, 12, 103, 10, "loadPromises"], [128, 32, 103, 22], [128, 33, 103, 23, "fontFamily"], [128, 43, 103, 33], [128, 44, 103, 34], [129, 4, 104, 0], [129, 5, 104, 1], [130, 4, 104, 1], [130, 11, 104, 1, "_loadFontInNamespaceAsync"], [130, 36, 104, 1], [130, 37, 104, 1, "apply"], [130, 42, 104, 1], [130, 49, 104, 1, "arguments"], [130, 58, 104, 1], [131, 2, 104, 1], [132, 2, 104, 1], [132, 11, 110, 22, "unloadAllAsync"], [132, 25, 110, 36, "unloadAllAsync"], [132, 26, 110, 36], [133, 4, 110, 36], [133, 11, 110, 36, "_unloadAllAsync"], [133, 26, 110, 36], [133, 27, 110, 36, "apply"], [133, 32, 110, 36], [133, 39, 110, 36, "arguments"], [133, 48, 110, 36], [134, 2, 110, 36], [134, 4, 120, 0], [135, 2, 121, 0], [136, 0, 122, 0], [137, 0, 123, 0], [138, 0, 124, 0], [139, 0, 125, 0], [140, 0, 126, 0], [141, 0, 127, 0], [142, 0, 128, 0], [143, 0, 129, 0], [144, 2, 121, 0], [144, 11, 121, 0, "_unloadAllAsync"], [144, 27, 121, 0], [145, 4, 121, 0, "_unloadAllAsync"], [145, 19, 121, 0], [145, 26, 121, 0, "_asyncToGenerator2"], [145, 44, 121, 0], [145, 45, 121, 0, "default"], [145, 52, 121, 0], [145, 54, 110, 7], [145, 67, 110, 39], [146, 6, 111, 4], [146, 10, 111, 8], [146, 11, 111, 9, "ExpoFontLoader"], [146, 34, 111, 23], [146, 35, 111, 24, "unloadAllAsync"], [146, 49, 111, 38], [146, 51, 111, 40], [147, 8, 112, 8], [147, 14, 112, 14], [147, 18, 112, 18, "UnavailabilityError"], [147, 54, 112, 37], [147, 55, 112, 38], [147, 66, 112, 49], [147, 68, 112, 51], [147, 84, 112, 67], [147, 85, 112, 68], [148, 6, 113, 4], [149, 6, 114, 4], [149, 10, 114, 8, "Object"], [149, 16, 114, 14], [149, 17, 114, 15, "keys"], [149, 21, 114, 19], [149, 22, 114, 20, "loadPromises"], [149, 42, 114, 32], [149, 43, 114, 33], [149, 44, 114, 34, "length"], [149, 50, 114, 40], [149, 52, 114, 42], [150, 8, 115, 8], [150, 14, 115, 14], [150, 18, 115, 18, "CodedError"], [150, 45, 115, 28], [150, 46, 115, 29], [150, 58, 115, 41], [150, 60, 115, 43], [150, 112, 115, 95, "Object"], [150, 118, 115, 101], [150, 119, 115, 102, "keys"], [150, 123, 115, 106], [150, 124, 115, 107, "loadPromises"], [150, 144, 115, 119], [150, 145, 115, 120], [150, 146, 115, 121, "join"], [150, 150, 115, 125], [150, 151, 115, 126], [150, 155, 115, 130], [150, 156, 115, 131], [150, 158, 115, 133], [150, 159, 115, 134], [151, 6, 116, 4], [152, 6, 117, 4], [152, 10, 117, 4, "purge<PERSON>ache"], [152, 28, 117, 14], [152, 30, 117, 15], [152, 31, 117, 16], [153, 6, 118, 4], [153, 12, 118, 10, "ExpoFontLoader"], [153, 35, 118, 24], [153, 36, 118, 25, "unloadAllAsync"], [153, 50, 118, 39], [153, 51, 118, 40], [153, 52, 118, 41], [154, 4, 119, 0], [154, 5, 119, 1], [155, 4, 119, 1], [155, 11, 119, 1, "_unloadAllAsync"], [155, 26, 119, 1], [155, 27, 119, 1, "apply"], [155, 32, 119, 1], [155, 39, 119, 1, "arguments"], [155, 48, 119, 1], [156, 2, 119, 1], [157, 2, 119, 1], [157, 11, 130, 22, "unloadAsync"], [157, 22, 130, 33, "unloadAsync"], [157, 23, 130, 33, "_x3"], [157, 26, 130, 33], [157, 28, 130, 33, "_x4"], [157, 31, 130, 33], [158, 4, 130, 33], [158, 11, 130, 33, "_unloadAsync"], [158, 23, 130, 33], [158, 24, 130, 33, "apply"], [158, 29, 130, 33], [158, 36, 130, 33, "arguments"], [158, 45, 130, 33], [159, 2, 130, 33], [160, 2, 130, 33], [160, 11, 130, 33, "_unloadAsync"], [160, 24, 130, 33], [161, 4, 130, 33, "_unloadAsync"], [161, 16, 130, 33], [161, 23, 130, 33, "_asyncToGenerator2"], [161, 41, 130, 33], [161, 42, 130, 33, "default"], [161, 49, 130, 33], [161, 51, 130, 7], [161, 62, 130, 34, "fontFamilyOrFontMap"], [161, 81, 130, 53], [161, 83, 130, 55, "options"], [161, 90, 130, 62], [161, 92, 130, 64], [162, 6, 131, 4], [162, 10, 131, 8], [162, 11, 131, 9, "ExpoFontLoader"], [162, 34, 131, 23], [162, 35, 131, 24, "unloadAsync"], [162, 46, 131, 35], [162, 48, 131, 37], [163, 8, 132, 8], [163, 14, 132, 14], [163, 18, 132, 18, "UnavailabilityError"], [163, 54, 132, 37], [163, 55, 132, 38], [163, 66, 132, 49], [163, 68, 132, 51], [163, 81, 132, 64], [163, 82, 132, 65], [164, 6, 133, 4], [165, 6, 134, 4], [165, 10, 134, 8], [165, 17, 134, 15, "fontFamilyOrFontMap"], [165, 36, 134, 34], [165, 41, 134, 39], [165, 49, 134, 47], [165, 51, 134, 49], [166, 8, 135, 8], [166, 12, 135, 12, "options"], [166, 19, 135, 19], [166, 21, 135, 21], [167, 10, 136, 12], [167, 16, 136, 18], [167, 20, 136, 22, "CodedError"], [167, 47, 136, 32], [167, 48, 136, 33], [167, 62, 136, 47], [167, 64, 136, 49], [167, 119, 136, 104, "options"], [167, 126, 136, 111], [167, 234, 136, 219], [167, 235, 136, 220], [168, 8, 137, 8], [169, 8, 138, 8], [169, 12, 138, 14, "fontMap"], [169, 19, 138, 21], [169, 22, 138, 24, "fontFamilyOrFontMap"], [169, 41, 138, 43], [170, 8, 139, 8], [170, 12, 139, 14, "names"], [170, 17, 139, 19], [170, 20, 139, 22, "Object"], [170, 26, 139, 28], [170, 27, 139, 29, "keys"], [170, 31, 139, 33], [170, 32, 139, 34, "fontMap"], [170, 39, 139, 41], [170, 40, 139, 42], [171, 8, 140, 8], [171, 14, 140, 14, "Promise"], [171, 21, 140, 21], [171, 22, 140, 22, "all"], [171, 25, 140, 25], [171, 26, 140, 26, "names"], [171, 31, 140, 31], [171, 32, 140, 32, "map"], [171, 35, 140, 35], [171, 36, 140, 37, "name"], [171, 40, 140, 41], [171, 44, 140, 46, "unloadFontInNamespaceAsync"], [171, 70, 140, 72], [171, 71, 140, 73, "name"], [171, 75, 140, 77], [171, 77, 140, 79, "fontMap"], [171, 84, 140, 86], [171, 85, 140, 87, "name"], [171, 89, 140, 91], [171, 90, 140, 92], [171, 91, 140, 93], [171, 92, 140, 94], [171, 93, 140, 95], [172, 8, 141, 8], [173, 6, 142, 4], [174, 6, 143, 4], [174, 19, 143, 17, "unloadFontInNamespaceAsync"], [174, 45, 143, 43], [174, 46, 143, 44, "fontFamilyOrFontMap"], [174, 65, 143, 63], [174, 67, 143, 65, "options"], [174, 74, 143, 72], [174, 75, 143, 73], [175, 4, 144, 0], [175, 5, 144, 1], [176, 4, 144, 1], [176, 11, 144, 1, "_unloadAsync"], [176, 23, 144, 1], [176, 24, 144, 1, "apply"], [176, 29, 144, 1], [176, 36, 144, 1, "arguments"], [176, 45, 144, 1], [177, 2, 144, 1], [178, 2, 144, 1], [178, 11, 145, 15, "unloadFontInNamespaceAsync"], [178, 37, 145, 41, "unloadFontInNamespaceAsync"], [178, 38, 145, 41, "_x5"], [178, 41, 145, 41], [178, 43, 145, 41, "_x6"], [178, 46, 145, 41], [179, 4, 145, 41], [179, 11, 145, 41, "_unloadFontInNamespaceAsync"], [179, 38, 145, 41], [179, 39, 145, 41, "apply"], [179, 44, 145, 41], [179, 51, 145, 41, "arguments"], [179, 60, 145, 41], [180, 2, 145, 41], [181, 2, 145, 41], [181, 11, 145, 41, "_unloadFontInNamespaceAsync"], [181, 39, 145, 41], [182, 4, 145, 41, "_unloadFontInNamespaceAsync"], [182, 31, 145, 41], [182, 38, 145, 41, "_asyncToGenerator2"], [182, 56, 145, 41], [182, 57, 145, 41, "default"], [182, 64, 145, 41], [182, 66, 145, 0], [182, 77, 145, 42, "fontFamily"], [182, 87, 145, 52], [182, 89, 145, 54, "options"], [182, 96, 145, 61], [182, 98, 145, 63], [183, 6, 146, 4], [183, 10, 146, 8], [183, 11, 146, 9, "isLoaded"], [183, 19, 146, 17], [183, 20, 146, 18, "fontFamily"], [183, 30, 146, 28], [183, 31, 146, 29], [183, 33, 146, 31], [184, 8, 147, 8], [185, 6, 148, 4], [185, 7, 148, 5], [185, 13, 149, 9], [186, 8, 150, 8], [186, 12, 150, 8, "purgeFontFamilyFromCache"], [186, 44, 150, 32], [186, 46, 150, 33, "fontFamily"], [186, 56, 150, 43], [186, 57, 150, 44], [187, 6, 151, 4], [188, 6, 152, 4], [189, 6, 153, 4], [190, 6, 154, 4], [191, 6, 155, 4], [192, 6, 156, 4], [192, 10, 156, 8], [192, 11, 156, 9, "fontFamily"], [192, 21, 156, 19], [192, 23, 156, 21], [193, 8, 157, 8], [193, 14, 157, 14], [193, 18, 157, 18, "CodedError"], [193, 45, 157, 28], [193, 46, 157, 29], [193, 63, 157, 46], [193, 65, 157, 48], [193, 94, 157, 77], [193, 95, 157, 78], [194, 6, 158, 4], [195, 6, 159, 4], [195, 12, 159, 10, "ExpoFontLoader"], [195, 35, 159, 24], [195, 36, 159, 25, "unloadAsync"], [195, 47, 159, 36], [195, 48, 159, 37, "fontFamily"], [195, 58, 159, 47], [195, 60, 159, 49, "options"], [195, 67, 159, 56], [195, 68, 159, 57], [196, 4, 160, 0], [196, 5, 160, 1], [197, 4, 160, 1], [197, 11, 160, 1, "_unloadFontInNamespaceAsync"], [197, 38, 160, 1], [197, 39, 160, 1, "apply"], [197, 44, 160, 1], [197, 51, 160, 1, "arguments"], [197, 60, 160, 1], [198, 2, 160, 1], [199, 0, 160, 1], [199, 3]], "functionMap": {"names": ["<global>", "isLoaded", "getLoadedFonts", "isLoading", "loadAsync", "names.map$argument_0", "Promise.all.then$argument_0", "loadFontInNamespaceAsync", "<anonymous>", "unloadAllAsync", "unloadAsync", "unloadFontInNamespaceAsync"], "mappings": "AAA;OCa;CDK;OEO;CFE;OGQ;CHE;OIgB;sBCY,iDD;qCCG,uDD,QE,SF;CJO;AOC;gCCiB;KDQ;CPE;OSM;CTS;OUW;oCLU,yDK;CVI;AWC;CXe"}}, "type": "js/module"}]}