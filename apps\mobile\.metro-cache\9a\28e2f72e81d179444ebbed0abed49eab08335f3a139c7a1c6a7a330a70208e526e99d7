{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 54}}], "key": "zpfG3XVT0+lb0mLhCz1tihcdf8E=", "exportNames": ["*"]}}, {"name": "./Appearance", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 44}}], "key": "S5tntIpCjRGVDHgXyDhnEF+9lhk=", "exportNames": ["*"]}}, {"name": "./NativeDevLoadingView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 58}}], "key": "rODsTJB1HQMNM2wWVaJHmrPDtSs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _processColor = _interopRequireDefault(require(_dependencyMap[1], \"../StyleSheet/processColor\"));\n  var _Appearance = require(_dependencyMap[2], \"./Appearance\");\n  var _NativeDevLoadingView = _interopRequireDefault(require(_dependencyMap[3], \"./NativeDevLoadingView\"));\n  var COLOR_SCHEME = {\n    dark: {\n      refresh: {\n        backgroundColor: '#2584e8',\n        textColor: '#ffffff'\n      },\n      load: {\n        backgroundColor: '#fafafa',\n        textColor: '#242526'\n      }\n    },\n    default: {\n      refresh: {\n        backgroundColor: '#2584e8',\n        textColor: '#ffffff'\n      },\n      load: {\n        backgroundColor: '#404040',\n        textColor: '#ffffff'\n      }\n    }\n  };\n  var _default = exports.default = {\n    showMessage(message, type) {\n      if (_NativeDevLoadingView.default) {\n        var colorScheme = (0, _Appearance.getColorScheme)() === 'dark' ? COLOR_SCHEME.dark : COLOR_SCHEME.default;\n        var colorSet = colorScheme[type];\n        var backgroundColor;\n        var textColor;\n        if (colorSet) {\n          backgroundColor = (0, _processColor.default)(colorSet.backgroundColor);\n          textColor = (0, _processColor.default)(colorSet.textColor);\n        }\n        _NativeDevLoadingView.default.showMessage(message, typeof textColor === 'number' ? textColor : null, typeof backgroundColor === 'number' ? backgroundColor : null);\n      }\n    },\n    hide() {\n      _NativeDevLoadingView.default && _NativeDevLoadingView.default.hide();\n    }\n  };\n});", "lineCount": 50, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_processColor"], [7, 19, 11, 0], [7, 22, 11, 0, "_interopRequireDefault"], [7, 44, 11, 0], [7, 45, 11, 0, "require"], [7, 52, 11, 0], [7, 53, 11, 0, "_dependencyMap"], [7, 67, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_Appearance"], [8, 17, 12, 0], [8, 20, 12, 0, "require"], [8, 27, 12, 0], [8, 28, 12, 0, "_dependencyMap"], [8, 42, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_NativeDevLoadingView"], [9, 27, 13, 0], [9, 30, 13, 0, "_interopRequireDefault"], [9, 52, 13, 0], [9, 53, 13, 0, "require"], [9, 60, 13, 0], [9, 61, 13, 0, "_dependencyMap"], [9, 75, 13, 0], [10, 2, 15, 0], [10, 6, 15, 6, "COLOR_SCHEME"], [10, 18, 15, 18], [10, 21, 15, 21], [11, 4, 16, 2, "dark"], [11, 8, 16, 6], [11, 10, 16, 8], [12, 6, 17, 4, "refresh"], [12, 13, 17, 11], [12, 15, 17, 13], [13, 8, 18, 6, "backgroundColor"], [13, 23, 18, 21], [13, 25, 18, 23], [13, 34, 18, 32], [14, 8, 19, 6, "textColor"], [14, 17, 19, 15], [14, 19, 19, 17], [15, 6, 20, 4], [15, 7, 20, 5], [16, 6, 21, 4, "load"], [16, 10, 21, 8], [16, 12, 21, 10], [17, 8, 22, 6, "backgroundColor"], [17, 23, 22, 21], [17, 25, 22, 23], [17, 34, 22, 32], [18, 8, 23, 6, "textColor"], [18, 17, 23, 15], [18, 19, 23, 17], [19, 6, 24, 4], [20, 4, 25, 2], [20, 5, 25, 3], [21, 4, 26, 2, "default"], [21, 11, 26, 9], [21, 13, 26, 11], [22, 6, 27, 4, "refresh"], [22, 13, 27, 11], [22, 15, 27, 13], [23, 8, 28, 6, "backgroundColor"], [23, 23, 28, 21], [23, 25, 28, 23], [23, 34, 28, 32], [24, 8, 29, 6, "textColor"], [24, 17, 29, 15], [24, 19, 29, 17], [25, 6, 30, 4], [25, 7, 30, 5], [26, 6, 31, 4, "load"], [26, 10, 31, 8], [26, 12, 31, 10], [27, 8, 32, 6, "backgroundColor"], [27, 23, 32, 21], [27, 25, 32, 23], [27, 34, 32, 32], [28, 8, 33, 6, "textColor"], [28, 17, 33, 15], [28, 19, 33, 17], [29, 6, 34, 4], [30, 4, 35, 2], [31, 2, 36, 0], [31, 3, 36, 1], [32, 2, 36, 2], [32, 6, 36, 2, "_default"], [32, 14, 36, 2], [32, 17, 36, 2, "exports"], [32, 24, 36, 2], [32, 25, 36, 2, "default"], [32, 32, 36, 2], [32, 35, 38, 15], [33, 4, 39, 2, "showMessage"], [33, 15, 39, 13, "showMessage"], [33, 16, 39, 14, "message"], [33, 23, 39, 29], [33, 25, 39, 31, "type"], [33, 29, 39, 55], [33, 31, 39, 57], [34, 6, 40, 4], [34, 10, 40, 8, "NativeDevLoadingView"], [34, 39, 40, 28], [34, 41, 40, 30], [35, 8, 41, 6], [35, 12, 41, 12, "colorScheme"], [35, 23, 41, 23], [35, 26, 42, 8], [35, 30, 42, 8, "getColorScheme"], [35, 56, 42, 22], [35, 58, 42, 23], [35, 59, 42, 24], [35, 64, 42, 29], [35, 70, 42, 35], [35, 73, 42, 38, "COLOR_SCHEME"], [35, 85, 42, 50], [35, 86, 42, 51, "dark"], [35, 90, 42, 55], [35, 93, 42, 58, "COLOR_SCHEME"], [35, 105, 42, 70], [35, 106, 42, 71, "default"], [35, 113, 42, 78], [36, 8, 44, 6], [36, 12, 44, 12, "colorSet"], [36, 20, 44, 20], [36, 23, 44, 23, "colorScheme"], [36, 34, 44, 34], [36, 35, 44, 35, "type"], [36, 39, 44, 39], [36, 40, 44, 40], [37, 8, 46, 6], [37, 12, 46, 10, "backgroundColor"], [37, 27, 46, 25], [38, 8, 47, 6], [38, 12, 47, 10, "textColor"], [38, 21, 47, 19], [39, 8, 49, 6], [39, 12, 49, 10, "colorSet"], [39, 20, 49, 18], [39, 22, 49, 20], [40, 10, 50, 8, "backgroundColor"], [40, 25, 50, 23], [40, 28, 50, 26], [40, 32, 50, 26, "processColor"], [40, 53, 50, 38], [40, 55, 50, 39, "colorSet"], [40, 63, 50, 47], [40, 64, 50, 48, "backgroundColor"], [40, 79, 50, 63], [40, 80, 50, 64], [41, 10, 51, 8, "textColor"], [41, 19, 51, 17], [41, 22, 51, 20], [41, 26, 51, 20, "processColor"], [41, 47, 51, 32], [41, 49, 51, 33, "colorSet"], [41, 57, 51, 41], [41, 58, 51, 42, "textColor"], [41, 67, 51, 51], [41, 68, 51, 52], [42, 8, 52, 6], [43, 8, 54, 6, "NativeDevLoadingView"], [43, 37, 54, 26], [43, 38, 54, 27, "showMessage"], [43, 49, 54, 38], [43, 50, 55, 8, "message"], [43, 57, 55, 15], [43, 59, 56, 8], [43, 66, 56, 15, "textColor"], [43, 75, 56, 24], [43, 80, 56, 29], [43, 88, 56, 37], [43, 91, 56, 40, "textColor"], [43, 100, 56, 49], [43, 103, 56, 52], [43, 107, 56, 56], [43, 109, 57, 8], [43, 116, 57, 15, "backgroundColor"], [43, 131, 57, 30], [43, 136, 57, 35], [43, 144, 57, 43], [43, 147, 57, 46, "backgroundColor"], [43, 162, 57, 61], [43, 165, 57, 64], [43, 169, 58, 6], [43, 170, 58, 7], [44, 6, 59, 4], [45, 4, 60, 2], [45, 5, 60, 3], [46, 4, 61, 2, "hide"], [46, 8, 61, 6, "hide"], [46, 9, 61, 6], [46, 11, 61, 9], [47, 6, 62, 4, "NativeDevLoadingView"], [47, 35, 62, 24], [47, 39, 62, 28, "NativeDevLoadingView"], [47, 68, 62, 48], [47, 69, 62, 49, "hide"], [47, 73, 62, 53], [47, 74, 62, 54], [47, 75, 62, 55], [48, 4, 63, 2], [49, 2, 64, 0], [49, 3, 64, 1], [50, 0, 64, 1], [50, 3]], "functionMap": {"names": ["<global>", "default.showMessage", "default.hide"], "mappings": "AAA;ECsC;GDqB;EEC;GFE"}}, "type": "js/module"}]}