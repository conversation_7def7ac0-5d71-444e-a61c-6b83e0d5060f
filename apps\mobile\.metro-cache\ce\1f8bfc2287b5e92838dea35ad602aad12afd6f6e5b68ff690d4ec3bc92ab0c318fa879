{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var dummySize = {\n    width: undefined,\n    height: undefined\n  };\n  function sizesDiffer(one, two) {\n    var defaultedOne = one || dummySize;\n    var defaultedTwo = two || dummySize;\n    return defaultedOne !== defaultedTwo && (defaultedOne.width !== defaultedTwo.width || defaultedOne.height !== defaultedTwo.height);\n  }\n  var _default = exports.default = sizesDiffer;\n});", "lineCount": 18, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 13, 0], [8, 6, 13, 6, "dummySize"], [8, 15, 13, 15], [8, 18, 13, 18], [9, 4, 13, 19, "width"], [9, 9, 13, 24], [9, 11, 13, 26, "undefined"], [9, 20, 13, 35], [10, 4, 13, 37, "height"], [10, 10, 13, 43], [10, 12, 13, 45, "undefined"], [11, 2, 13, 54], [11, 3, 13, 55], [12, 2, 16, 0], [12, 11, 16, 9, "<PERSON><PERSON><PERSON><PERSON>"], [12, 22, 16, 20, "<PERSON><PERSON><PERSON><PERSON>"], [12, 23, 16, 21, "one"], [12, 26, 16, 30], [12, 28, 16, 32, "two"], [12, 31, 16, 41], [12, 33, 16, 52], [13, 4, 17, 2], [13, 8, 17, 8, "defaultedOne"], [13, 20, 17, 20], [13, 23, 17, 23, "one"], [13, 26, 17, 26], [13, 30, 17, 30, "dummySize"], [13, 39, 17, 39], [14, 4, 18, 2], [14, 8, 18, 8, "defaultedTwo"], [14, 20, 18, 20], [14, 23, 18, 23, "two"], [14, 26, 18, 26], [14, 30, 18, 30, "dummySize"], [14, 39, 18, 39], [15, 4, 19, 2], [15, 11, 20, 4, "defaultedOne"], [15, 23, 20, 16], [15, 28, 20, 21, "defaultedTwo"], [15, 40, 20, 33], [15, 45, 21, 5, "defaultedOne"], [15, 57, 21, 17], [15, 58, 21, 18, "width"], [15, 63, 21, 23], [15, 68, 21, 28, "defaultedTwo"], [15, 80, 21, 40], [15, 81, 21, 41, "width"], [15, 86, 21, 46], [15, 90, 22, 6, "defaultedOne"], [15, 102, 22, 18], [15, 103, 22, 19, "height"], [15, 109, 22, 25], [15, 114, 22, 30, "defaultedTwo"], [15, 126, 22, 42], [15, 127, 22, 43, "height"], [15, 133, 22, 49], [15, 134, 22, 50], [16, 2, 24, 0], [17, 2, 24, 1], [17, 6, 24, 1, "_default"], [17, 14, 24, 1], [17, 17, 24, 1, "exports"], [17, 24, 24, 1], [17, 25, 24, 1, "default"], [17, 32, 24, 1], [17, 35, 26, 15, "<PERSON><PERSON><PERSON><PERSON>"], [17, 46, 26, 26], [18, 0, 26, 26], [18, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;ACe;CDQ"}}, "type": "js/module"}]}