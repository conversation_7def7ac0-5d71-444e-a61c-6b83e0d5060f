{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 76, "index": 108}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./handlersRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 109}, "end": {"line": 3, "column": 52, "index": 161}}], "key": "icHMSVIKxbHLSdF6K64ideInyBg=", "exportNames": ["*"]}}, {"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 162}, "end": {"line": 4, "column": 35, "index": 197}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}, {"name": "../RNGestureHandlerModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 198}, "end": {"line": 5, "column": 63, "index": 261}}], "key": "bY7FGgfi8WGOEKHKyXsenNEOYXM=", "exportNames": ["*"]}}, {"name": "../ghQueueMicrotask", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 262}, "end": {"line": 6, "column": 55, "index": 317}}], "key": "Ty3ERJQ4RajY8XDWg1+a8wq7RdE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.filterConfig = filterConfig;\n  exports.findNodeHandle = findNodeHandle;\n  exports.scheduleFlushOperations = scheduleFlushOperations;\n  exports.transformIntoHandlerTags = transformIntoHandlerTags;\n  var _reactNative = require(_dependencyMap[1], \"react-native\");\n  var _handlersRegistry = require(_dependencyMap[2], \"./handlersRegistry\");\n  var _utils = require(_dependencyMap[3], \"../utils\");\n  var _RNGestureHandlerModule = _interopRequireDefault(require(_dependencyMap[4], \"../RNGestureHandlerModule\"));\n  var _ghQueueMicrotask = require(_dependencyMap[5], \"../ghQueueMicrotask\");\n  function isConfigParam(param, name) {\n    // param !== Object(param) returns false if `param` is a function\n    // or an object and returns true if `param` is null\n    return param !== undefined && (param !== Object(param) || !('__isNative' in param)) && name !== 'onHandlerStateChange' && name !== 'onGestureEvent';\n  }\n  function filterConfig(props, validProps) {\n    var defaults = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var filteredConfig = {\n      ...defaults\n    };\n    for (var key of validProps) {\n      var value = props[key];\n      if (isConfigParam(value, key)) {\n        if (key === 'simultaneousHandlers' || key === 'waitFor') {\n          value = transformIntoHandlerTags(props[key]);\n        } else if (key === 'hitSlop' && typeof value !== 'object') {\n          value = {\n            top: value,\n            left: value,\n            bottom: value,\n            right: value\n          };\n        }\n        filteredConfig[key] = value;\n      }\n    }\n    return filteredConfig;\n  }\n  function transformIntoHandlerTags(handlerIDs) {\n    handlerIDs = (0, _utils.toArray)(handlerIDs);\n    if (_reactNative.Platform.OS === 'web') {\n      return handlerIDs.map(_ref => {\n        var current = _ref.current;\n        return current;\n      }).filter(handle => handle);\n    }\n    // converts handler string IDs into their numeric tags\n    return handlerIDs.map(handlerID => _handlersRegistry.handlerIDToTag[handlerID] || handlerID.current?.handlerTag || -1).filter(handlerTag => handlerTag > 0);\n  }\n  function findNodeHandle(node) {\n    if (_reactNative.Platform.OS === 'web') {\n      return node;\n    }\n    return (0, _reactNative.findNodeHandle)(node) ?? null;\n  }\n  var flushOperationsScheduled = false;\n  function scheduleFlushOperations() {\n    if (!flushOperationsScheduled) {\n      flushOperationsScheduled = true;\n      (0, _ghQueueMicrotask.ghQueueMicrotask)(() => {\n        _RNGestureHandlerModule.default.flushOperations();\n        flushOperationsScheduled = false;\n      });\n    }\n  }\n});", "lineCount": 70, "map": [[10, 2, 2, 0], [10, 6, 2, 0, "_reactNative"], [10, 18, 2, 0], [10, 21, 2, 0, "require"], [10, 28, 2, 0], [10, 29, 2, 0, "_dependencyMap"], [10, 43, 2, 0], [11, 2, 3, 0], [11, 6, 3, 0, "_handlersRegistry"], [11, 23, 3, 0], [11, 26, 3, 0, "require"], [11, 33, 3, 0], [11, 34, 3, 0, "_dependencyMap"], [11, 48, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_utils"], [12, 12, 4, 0], [12, 15, 4, 0, "require"], [12, 22, 4, 0], [12, 23, 4, 0, "_dependencyMap"], [12, 37, 4, 0], [13, 2, 5, 0], [13, 6, 5, 0, "_RNGestureHandlerModule"], [13, 29, 5, 0], [13, 32, 5, 0, "_interopRequireDefault"], [13, 54, 5, 0], [13, 55, 5, 0, "require"], [13, 62, 5, 0], [13, 63, 5, 0, "_dependencyMap"], [13, 77, 5, 0], [14, 2, 6, 0], [14, 6, 6, 0, "_ghQueueMicrotask"], [14, 23, 6, 0], [14, 26, 6, 0, "require"], [14, 33, 6, 0], [14, 34, 6, 0, "_dependencyMap"], [14, 48, 6, 0], [15, 2, 8, 0], [15, 11, 8, 9, "isConfigParam"], [15, 24, 8, 22, "isConfigParam"], [15, 25, 8, 23, "param"], [15, 30, 8, 37], [15, 32, 8, 39, "name"], [15, 36, 8, 51], [15, 38, 8, 53], [16, 4, 9, 2], [17, 4, 10, 2], [18, 4, 11, 2], [18, 11, 12, 4, "param"], [18, 16, 12, 9], [18, 21, 12, 14, "undefined"], [18, 30, 12, 23], [18, 35, 13, 5, "param"], [18, 40, 13, 10], [18, 45, 13, 15, "Object"], [18, 51, 13, 21], [18, 52, 13, 22, "param"], [18, 57, 13, 27], [18, 58, 13, 28], [18, 62, 14, 6], [18, 64, 14, 8], [18, 76, 14, 20], [18, 80, 14, 25, "param"], [18, 85, 14, 58], [18, 86, 14, 59], [18, 87, 14, 60], [18, 91, 15, 4, "name"], [18, 95, 15, 8], [18, 100, 15, 13], [18, 122, 15, 35], [18, 126, 16, 4, "name"], [18, 130, 16, 8], [18, 135, 16, 13], [18, 151, 16, 29], [19, 2, 18, 0], [20, 2, 20, 7], [20, 11, 20, 16, "filterConfig"], [20, 23, 20, 28, "filterConfig"], [20, 24, 21, 2, "props"], [20, 29, 21, 32], [20, 31, 22, 2, "validProps"], [20, 41, 22, 22], [20, 43, 24, 2], [21, 4, 24, 2], [21, 8, 23, 2, "defaults"], [21, 16, 23, 35], [21, 19, 23, 35, "arguments"], [21, 28, 23, 35], [21, 29, 23, 35, "length"], [21, 35, 23, 35], [21, 43, 23, 35, "arguments"], [21, 52, 23, 35], [21, 60, 23, 35, "undefined"], [21, 69, 23, 35], [21, 72, 23, 35, "arguments"], [21, 81, 23, 35], [21, 87, 23, 38], [21, 88, 23, 39], [21, 89, 23, 40], [22, 4, 25, 2], [22, 8, 25, 8, "filteredConfig"], [22, 22, 25, 22], [22, 25, 25, 25], [23, 6, 25, 27], [23, 9, 25, 30, "defaults"], [24, 4, 25, 39], [24, 5, 25, 40], [25, 4, 26, 2], [25, 9, 26, 7], [25, 13, 26, 13, "key"], [25, 16, 26, 16], [25, 20, 26, 20, "validProps"], [25, 30, 26, 30], [25, 32, 26, 32], [26, 6, 27, 4], [26, 10, 27, 8, "value"], [26, 15, 27, 13], [26, 18, 27, 16, "props"], [26, 23, 27, 21], [26, 24, 27, 22, "key"], [26, 27, 27, 25], [26, 28, 27, 26], [27, 6, 28, 4], [27, 10, 28, 8, "isConfigParam"], [27, 23, 28, 21], [27, 24, 28, 22, "value"], [27, 29, 28, 27], [27, 31, 28, 29, "key"], [27, 34, 28, 32], [27, 35, 28, 33], [27, 37, 28, 35], [28, 8, 29, 6], [28, 12, 29, 10, "key"], [28, 15, 29, 13], [28, 20, 29, 18], [28, 42, 29, 40], [28, 46, 29, 44, "key"], [28, 49, 29, 47], [28, 54, 29, 52], [28, 63, 29, 61], [28, 65, 29, 63], [29, 10, 30, 8, "value"], [29, 15, 30, 13], [29, 18, 30, 16, "transformIntoHandlerTags"], [29, 42, 30, 40], [29, 43, 30, 41, "props"], [29, 48, 30, 46], [29, 49, 30, 47, "key"], [29, 52, 30, 50], [29, 53, 30, 51], [29, 54, 30, 52], [30, 8, 31, 6], [30, 9, 31, 7], [30, 15, 31, 13], [30, 19, 31, 17, "key"], [30, 22, 31, 20], [30, 27, 31, 25], [30, 36, 31, 34], [30, 40, 31, 38], [30, 47, 31, 45, "value"], [30, 52, 31, 50], [30, 57, 31, 55], [30, 65, 31, 63], [30, 67, 31, 65], [31, 10, 32, 8, "value"], [31, 15, 32, 13], [31, 18, 32, 16], [32, 12, 32, 18, "top"], [32, 15, 32, 21], [32, 17, 32, 23, "value"], [32, 22, 32, 28], [33, 12, 32, 30, "left"], [33, 16, 32, 34], [33, 18, 32, 36, "value"], [33, 23, 32, 41], [34, 12, 32, 43, "bottom"], [34, 18, 32, 49], [34, 20, 32, 51, "value"], [34, 25, 32, 56], [35, 12, 32, 58, "right"], [35, 17, 32, 63], [35, 19, 32, 65, "value"], [36, 10, 32, 71], [36, 11, 32, 72], [37, 8, 33, 6], [38, 8, 34, 6, "filteredConfig"], [38, 22, 34, 20], [38, 23, 34, 21, "key"], [38, 26, 34, 24], [38, 27, 34, 25], [38, 30, 34, 28, "value"], [38, 35, 34, 33], [39, 6, 35, 4], [40, 4, 36, 2], [41, 4, 37, 2], [41, 11, 37, 9, "filteredConfig"], [41, 25, 37, 23], [42, 2, 38, 0], [43, 2, 40, 7], [43, 11, 40, 16, "transformIntoHandlerTags"], [43, 35, 40, 40, "transformIntoHandlerTags"], [43, 36, 40, 41, "handlerIDs"], [43, 46, 40, 56], [43, 48, 40, 58], [44, 4, 41, 2, "handlerIDs"], [44, 14, 41, 12], [44, 17, 41, 15], [44, 21, 41, 15, "toArray"], [44, 35, 41, 22], [44, 37, 41, 23, "handlerIDs"], [44, 47, 41, 33], [44, 48, 41, 34], [45, 4, 43, 2], [45, 8, 43, 6, "Platform"], [45, 29, 43, 14], [45, 30, 43, 15, "OS"], [45, 32, 43, 17], [45, 37, 43, 22], [45, 42, 43, 27], [45, 44, 43, 29], [46, 6, 44, 4], [46, 13, 44, 11, "handlerIDs"], [46, 23, 44, 21], [46, 24, 45, 7, "map"], [46, 27, 45, 10], [46, 28, 45, 11, "_ref"], [46, 32, 45, 11], [47, 8, 45, 11], [47, 12, 45, 14, "current"], [47, 19, 45, 21], [47, 22, 45, 21, "_ref"], [47, 26, 45, 21], [47, 27, 45, 14, "current"], [47, 34, 45, 21], [48, 8, 45, 21], [48, 15, 45, 46, "current"], [48, 22, 45, 53], [49, 6, 45, 53], [49, 8, 45, 54], [49, 9, 46, 7, "filter"], [49, 15, 46, 13], [49, 16, 46, 15, "handle"], [49, 22, 46, 26], [49, 26, 46, 31, "handle"], [49, 32, 46, 37], [49, 33, 46, 38], [50, 4, 47, 2], [51, 4, 48, 2], [52, 4, 49, 2], [52, 11, 49, 9, "handlerIDs"], [52, 21, 49, 19], [52, 22, 50, 5, "map"], [52, 25, 50, 8], [52, 26, 51, 7, "handlerID"], [52, 35, 51, 21], [52, 39, 52, 8, "handlerIDToTag"], [52, 71, 52, 22], [52, 72, 52, 23, "handlerID"], [52, 81, 52, 32], [52, 82, 52, 33], [52, 86, 52, 37, "handlerID"], [52, 95, 52, 46], [52, 96, 52, 47, "current"], [52, 103, 52, 54], [52, 105, 52, 56, "handlerTag"], [52, 115, 52, 66], [52, 119, 52, 70], [52, 120, 52, 71], [52, 121, 53, 4], [52, 122, 53, 5], [52, 123, 54, 5, "filter"], [52, 129, 54, 11], [52, 130, 54, 13, "handlerTag"], [52, 140, 54, 31], [52, 144, 54, 36, "handlerTag"], [52, 154, 54, 46], [52, 157, 54, 49], [52, 158, 54, 50], [52, 159, 54, 51], [53, 2, 55, 0], [54, 2, 57, 7], [54, 11, 57, 16, "findNodeHandle"], [54, 25, 57, 30, "findNodeHandle"], [54, 26, 58, 2, "node"], [54, 30, 58, 77], [54, 32, 59, 73], [55, 4, 60, 2], [55, 8, 60, 6, "Platform"], [55, 29, 60, 14], [55, 30, 60, 15, "OS"], [55, 32, 60, 17], [55, 37, 60, 22], [55, 42, 60, 27], [55, 44, 60, 29], [56, 6, 61, 4], [56, 13, 61, 11, "node"], [56, 17, 61, 15], [57, 4, 62, 2], [58, 4, 63, 2], [58, 11, 63, 9], [58, 15, 63, 9, "findNodeHandleRN"], [58, 42, 63, 25], [58, 44, 63, 26, "node"], [58, 48, 63, 30], [58, 49, 63, 31], [58, 53, 63, 35], [58, 57, 63, 39], [59, 2, 64, 0], [60, 2, 65, 0], [60, 6, 65, 4, "flushOperationsScheduled"], [60, 30, 65, 28], [60, 33, 65, 31], [60, 38, 65, 36], [61, 2, 67, 7], [61, 11, 67, 16, "scheduleFlushOperations"], [61, 34, 67, 39, "scheduleFlushOperations"], [61, 35, 67, 39], [61, 37, 67, 42], [62, 4, 68, 2], [62, 8, 68, 6], [62, 9, 68, 7, "flushOperationsScheduled"], [62, 33, 68, 31], [62, 35, 68, 33], [63, 6, 69, 4, "flushOperationsScheduled"], [63, 30, 69, 28], [63, 33, 69, 31], [63, 37, 69, 35], [64, 6, 70, 4], [64, 10, 70, 4, "ghQueueMicrotask"], [64, 44, 70, 20], [64, 46, 70, 21], [64, 52, 70, 27], [65, 8, 71, 6, "RNGestureHandlerModule"], [65, 39, 71, 28], [65, 40, 71, 29, "flushOperations"], [65, 55, 71, 44], [65, 56, 71, 45], [65, 57, 71, 46], [66, 8, 73, 6, "flushOperationsScheduled"], [66, 32, 73, 30], [66, 35, 73, 33], [66, 40, 73, 38], [67, 6, 74, 4], [67, 7, 74, 5], [67, 8, 74, 6], [68, 4, 75, 2], [69, 2, 76, 0], [70, 0, 76, 1], [70, 3]], "functionMap": {"names": ["<global>", "isConfigParam", "filterConfig", "transformIntoHandlerTags", "handlerIDs.map$argument_0", "handlerIDs.map.filter$argument_0", "findNodeHandle", "scheduleFlushOperations", "ghQueueMicrotask$argument_0"], "mappings": "AAA;ACO;CDU;OEE;CFkB;OGE;WCK,0CD;cEC,uBF;MCK;wEDC;YEE,sCF;CHC;OME;CNO;OOG;qBCG;KDI;CPE"}}, "type": "js/module"}]}