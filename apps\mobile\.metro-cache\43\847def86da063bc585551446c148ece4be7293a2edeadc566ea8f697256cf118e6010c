{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 75}, "end": {"line": 3, "column": 53, "index": 128}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../init", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 129}, "end": {"line": 4, "column": 48, "index": 177}}], "key": "VhBcUEY6vHt8FCKn4U+vMZ7nZc0=", "exportNames": ["*"]}}, {"name": "../GestureHandlerRootViewContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 178}, "end": {"line": 5, "column": 77, "index": 255}}], "key": "4dFi+SgkqvO8MS5HEh2OcvBDODc=", "exportNames": ["*"]}}, {"name": "../specs/RNGestureHandlerRootViewNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 256}, "end": {"line": 6, "column": 101, "index": 357}}], "key": "qPeqBbt4OxSrEiT5c1s08fgHn58=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = GestureHandlerRootView;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _init = require(_dependencyMap[4], \"../init\");\n  var _GestureHandlerRootViewContext = _interopRequireDefault(require(_dependencyMap[5], \"../GestureHandlerRootViewContext\"));\n  var _RNGestureHandlerRootViewNativeComponent = _interopRequireDefault(require(_dependencyMap[6], \"../specs/RNGestureHandlerRootViewNativeComponent\"));\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _excluded = [\"style\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\components\\\\GestureHandlerRootView.android.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function GestureHandlerRootView(_ref) {\n    var style = _ref.style,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    // Try initialize fabric on the first render, at this point we can\n    // reliably check if fabric is enabled (the function contains a flag\n    // to make sure it's called only once)\n    (0, _init.maybeInitializeFabric)();\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_GestureHandlerRootViewContext.default.Provider, {\n      value: true,\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_RNGestureHandlerRootViewNativeComponent.default, {\n        style: style ?? styles.container,\n        ...rest\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flex: 1\n    }\n  });\n});", "lineCount": 45, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "React"], [8, 11, 1, 0], [8, 14, 1, 0, "_interopRequireWildcard"], [8, 37, 1, 0], [8, 38, 1, 0, "require"], [8, 45, 1, 0], [8, 46, 1, 0, "_dependencyMap"], [8, 60, 1, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_reactNative"], [9, 18, 3, 0], [9, 21, 3, 0, "require"], [9, 28, 3, 0], [9, 29, 3, 0, "_dependencyMap"], [9, 43, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_init"], [10, 11, 4, 0], [10, 14, 4, 0, "require"], [10, 21, 4, 0], [10, 22, 4, 0, "_dependencyMap"], [10, 36, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_GestureHandlerRootViewContext"], [11, 36, 5, 0], [11, 39, 5, 0, "_interopRequireDefault"], [11, 61, 5, 0], [11, 62, 5, 0, "require"], [11, 69, 5, 0], [11, 70, 5, 0, "_dependencyMap"], [11, 84, 5, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_RNGestureHandlerRootViewNativeComponent"], [12, 46, 6, 0], [12, 49, 6, 0, "_interopRequireDefault"], [12, 71, 6, 0], [12, 72, 6, 0, "require"], [12, 79, 6, 0], [12, 80, 6, 0, "_dependencyMap"], [12, 94, 6, 0], [13, 2, 6, 101], [13, 6, 6, 101, "_jsxDevRuntime"], [13, 20, 6, 101], [13, 23, 6, 101, "require"], [13, 30, 6, 101], [13, 31, 6, 101, "_dependencyMap"], [13, 45, 6, 101], [14, 2, 6, 101], [14, 6, 6, 101, "_excluded"], [14, 15, 6, 101], [15, 2, 6, 101], [15, 6, 6, 101, "_jsxFileName"], [15, 18, 6, 101], [16, 2, 6, 101], [16, 11, 6, 101, "_interopRequireWildcard"], [16, 35, 6, 101, "e"], [16, 36, 6, 101], [16, 38, 6, 101, "t"], [16, 39, 6, 101], [16, 68, 6, 101, "WeakMap"], [16, 75, 6, 101], [16, 81, 6, 101, "r"], [16, 82, 6, 101], [16, 89, 6, 101, "WeakMap"], [16, 96, 6, 101], [16, 100, 6, 101, "n"], [16, 101, 6, 101], [16, 108, 6, 101, "WeakMap"], [16, 115, 6, 101], [16, 127, 6, 101, "_interopRequireWildcard"], [16, 150, 6, 101], [16, 162, 6, 101, "_interopRequireWildcard"], [16, 163, 6, 101, "e"], [16, 164, 6, 101], [16, 166, 6, 101, "t"], [16, 167, 6, 101], [16, 176, 6, 101, "t"], [16, 177, 6, 101], [16, 181, 6, 101, "e"], [16, 182, 6, 101], [16, 186, 6, 101, "e"], [16, 187, 6, 101], [16, 188, 6, 101, "__esModule"], [16, 198, 6, 101], [16, 207, 6, 101, "e"], [16, 208, 6, 101], [16, 214, 6, 101, "o"], [16, 215, 6, 101], [16, 217, 6, 101, "i"], [16, 218, 6, 101], [16, 220, 6, 101, "f"], [16, 221, 6, 101], [16, 226, 6, 101, "__proto__"], [16, 235, 6, 101], [16, 243, 6, 101, "default"], [16, 250, 6, 101], [16, 252, 6, 101, "e"], [16, 253, 6, 101], [16, 270, 6, 101, "e"], [16, 271, 6, 101], [16, 294, 6, 101, "e"], [16, 295, 6, 101], [16, 320, 6, 101, "e"], [16, 321, 6, 101], [16, 330, 6, 101, "f"], [16, 331, 6, 101], [16, 337, 6, 101, "o"], [16, 338, 6, 101], [16, 341, 6, 101, "t"], [16, 342, 6, 101], [16, 345, 6, 101, "n"], [16, 346, 6, 101], [16, 349, 6, 101, "r"], [16, 350, 6, 101], [16, 358, 6, 101, "o"], [16, 359, 6, 101], [16, 360, 6, 101, "has"], [16, 363, 6, 101], [16, 364, 6, 101, "e"], [16, 365, 6, 101], [16, 375, 6, 101, "o"], [16, 376, 6, 101], [16, 377, 6, 101, "get"], [16, 380, 6, 101], [16, 381, 6, 101, "e"], [16, 382, 6, 101], [16, 385, 6, 101, "o"], [16, 386, 6, 101], [16, 387, 6, 101, "set"], [16, 390, 6, 101], [16, 391, 6, 101, "e"], [16, 392, 6, 101], [16, 394, 6, 101, "f"], [16, 395, 6, 101], [16, 409, 6, 101, "_t"], [16, 411, 6, 101], [16, 415, 6, 101, "e"], [16, 416, 6, 101], [16, 432, 6, 101, "_t"], [16, 434, 6, 101], [16, 441, 6, 101, "hasOwnProperty"], [16, 455, 6, 101], [16, 456, 6, 101, "call"], [16, 460, 6, 101], [16, 461, 6, 101, "e"], [16, 462, 6, 101], [16, 464, 6, 101, "_t"], [16, 466, 6, 101], [16, 473, 6, 101, "i"], [16, 474, 6, 101], [16, 478, 6, 101, "o"], [16, 479, 6, 101], [16, 482, 6, 101, "Object"], [16, 488, 6, 101], [16, 489, 6, 101, "defineProperty"], [16, 503, 6, 101], [16, 508, 6, 101, "Object"], [16, 514, 6, 101], [16, 515, 6, 101, "getOwnPropertyDescriptor"], [16, 539, 6, 101], [16, 540, 6, 101, "e"], [16, 541, 6, 101], [16, 543, 6, 101, "_t"], [16, 545, 6, 101], [16, 552, 6, 101, "i"], [16, 553, 6, 101], [16, 554, 6, 101, "get"], [16, 557, 6, 101], [16, 561, 6, 101, "i"], [16, 562, 6, 101], [16, 563, 6, 101, "set"], [16, 566, 6, 101], [16, 570, 6, 101, "o"], [16, 571, 6, 101], [16, 572, 6, 101, "f"], [16, 573, 6, 101], [16, 575, 6, 101, "_t"], [16, 577, 6, 101], [16, 579, 6, 101, "i"], [16, 580, 6, 101], [16, 584, 6, 101, "f"], [16, 585, 6, 101], [16, 586, 6, 101, "_t"], [16, 588, 6, 101], [16, 592, 6, 101, "e"], [16, 593, 6, 101], [16, 594, 6, 101, "_t"], [16, 596, 6, 101], [16, 607, 6, 101, "f"], [16, 608, 6, 101], [16, 613, 6, 101, "e"], [16, 614, 6, 101], [16, 616, 6, 101, "t"], [16, 617, 6, 101], [17, 2, 11, 15], [17, 11, 11, 24, "GestureHandlerRootView"], [17, 33, 11, 46, "GestureHandlerRootView"], [17, 34, 11, 46, "_ref"], [17, 38, 11, 46], [17, 40, 14, 32], [18, 4, 14, 32], [18, 8, 12, 2, "style"], [18, 13, 12, 7], [18, 16, 12, 7, "_ref"], [18, 20, 12, 7], [18, 21, 12, 2, "style"], [18, 26, 12, 7], [19, 6, 13, 5, "rest"], [19, 10, 13, 9], [19, 17, 13, 9, "_objectWithoutProperties2"], [19, 42, 13, 9], [19, 43, 13, 9, "default"], [19, 50, 13, 9], [19, 52, 13, 9, "_ref"], [19, 56, 13, 9], [19, 58, 13, 9, "_excluded"], [19, 67, 13, 9], [20, 4, 15, 2], [21, 4, 16, 2], [22, 4, 17, 2], [23, 4, 18, 2], [23, 8, 18, 2, "maybeInitializeFabric"], [23, 35, 18, 23], [23, 37, 18, 24], [23, 38, 18, 25], [24, 4, 20, 2], [24, 24, 21, 4], [24, 28, 21, 4, "_jsxDevRuntime"], [24, 42, 21, 4], [24, 43, 21, 4, "jsxDEV"], [24, 49, 21, 4], [24, 51, 21, 5, "_GestureHandlerRootViewContext"], [24, 81, 21, 5], [24, 82, 21, 5, "default"], [24, 89, 21, 34], [24, 90, 21, 35, "Provider"], [24, 98, 21, 43], [25, 6, 21, 44, "value"], [25, 11, 21, 49], [26, 6, 21, 49, "children"], [26, 14, 21, 49], [26, 29, 22, 6], [26, 33, 22, 6, "_jsxDevRuntime"], [26, 47, 22, 6], [26, 48, 22, 6, "jsxDEV"], [26, 54, 22, 6], [26, 56, 22, 7, "_RNGestureHandlerRootViewNativeComponent"], [26, 96, 22, 7], [26, 97, 22, 7, "default"], [26, 104, 22, 44], [27, 8, 23, 8, "style"], [27, 13, 23, 13], [27, 15, 23, 15, "style"], [27, 20, 23, 20], [27, 24, 23, 24, "styles"], [27, 30, 23, 30], [27, 31, 23, 31, "container"], [27, 40, 23, 41], [28, 8, 23, 41], [28, 11, 24, 12, "rest"], [29, 6, 24, 16], [30, 8, 24, 16, "fileName"], [30, 16, 24, 16], [30, 18, 24, 16, "_jsxFileName"], [30, 30, 24, 16], [31, 8, 24, 16, "lineNumber"], [31, 18, 24, 16], [32, 8, 24, 16, "columnNumber"], [32, 20, 24, 16], [33, 6, 24, 16], [33, 13, 25, 7], [34, 4, 25, 8], [35, 6, 25, 8, "fileName"], [35, 14, 25, 8], [35, 16, 25, 8, "_jsxFileName"], [35, 28, 25, 8], [36, 6, 25, 8, "lineNumber"], [36, 16, 25, 8], [37, 6, 25, 8, "columnNumber"], [37, 18, 25, 8], [38, 4, 25, 8], [38, 11, 26, 44], [38, 12, 26, 45], [39, 2, 28, 0], [40, 2, 30, 0], [40, 6, 30, 6, "styles"], [40, 12, 30, 12], [40, 15, 30, 15, "StyleSheet"], [40, 38, 30, 25], [40, 39, 30, 26, "create"], [40, 45, 30, 32], [40, 46, 30, 33], [41, 4, 31, 2, "container"], [41, 13, 31, 11], [41, 15, 31, 13], [42, 6, 31, 15, "flex"], [42, 10, 31, 19], [42, 12, 31, 21], [43, 4, 31, 23], [44, 2, 32, 0], [44, 3, 32, 1], [44, 4, 32, 2], [45, 0, 32, 3], [45, 3]], "functionMap": {"names": ["<global>", "GestureHandlerRootView"], "mappings": "AAA;eCU;CDiB"}}, "type": "js/module"}]}