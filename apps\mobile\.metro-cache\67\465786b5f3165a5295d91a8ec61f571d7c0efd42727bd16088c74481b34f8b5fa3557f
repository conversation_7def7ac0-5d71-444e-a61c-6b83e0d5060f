{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./GenericTouchable", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 67}, "end": {"line": 3, "column": 71, "index": 138}}], "key": "b7B+HFZ7s4hNhosUwPttECYmJ2U=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 209}, "end": {"line": 11, "column": 22, "index": 345}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[7], \"react\"));\n  var React = _react;\n  var _GenericTouchable = _interopRequireWildcard(require(_dependencyMap[8], \"./GenericTouchable\"));\n  var _reactNative = require(_dependencyMap[9], \"react-native\");\n  var _jsxDevRuntime = require(_dependencyMap[10], \"react/jsx-dev-runtime\");\n  var _excluded = [\"style\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\components\\\\touchables\\\\TouchableHighlight.tsx\";\n  /**\n   * @deprecated TouchableHighlight will be removed in the future version of Gesture Handler. Use Pressable instead.\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  /**\n   * @deprecated TouchableHighlight will be removed in the future version of Gesture Handler. Use Pressable instead.\n   *\n   * TouchableHighlight follows RN's implementation\n   */\n  var TouchableHighlight = exports.default = /*#__PURE__*/function (_Component) {\n    function TouchableHighlight(props) {\n      var _this;\n      (0, _classCallCheck2.default)(this, TouchableHighlight);\n      _this = _callSuper(this, TouchableHighlight, [props]);\n      // Copied from RN\n      _this.showUnderlay = () => {\n        if (!_this.hasPressHandler()) {\n          return;\n        }\n        _this.setState({\n          extraChildStyle: {\n            opacity: _this.props.activeOpacity\n          },\n          extraUnderlayStyle: {\n            backgroundColor: _this.props.underlayColor\n          }\n        });\n        _this.props.onShowUnderlay?.();\n      };\n      _this.hasPressHandler = () => _this.props.onPress || _this.props.onPressIn || _this.props.onPressOut || _this.props.onLongPress;\n      _this.hideUnderlay = () => {\n        _this.setState({\n          extraChildStyle: null,\n          extraUnderlayStyle: null\n        });\n        _this.props.onHideUnderlay?.();\n      };\n      _this.onStateChange = (_from, to) => {\n        if (to === _GenericTouchable.TOUCHABLE_STATE.BEGAN) {\n          _this.showUnderlay();\n        } else if (to === _GenericTouchable.TOUCHABLE_STATE.UNDETERMINED || to === _GenericTouchable.TOUCHABLE_STATE.MOVED_OUTSIDE) {\n          _this.hideUnderlay();\n        }\n      };\n      _this.state = {\n        extraChildStyle: null,\n        extraUnderlayStyle: null\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(TouchableHighlight, _Component);\n    return (0, _createClass2.default)(TouchableHighlight, [{\n      key: \"renderChildren\",\n      value: function renderChildren() {\n        if (!this.props.children) {\n          return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 14\n          }, this);\n        }\n        var child = React.Children.only(this.props.children); // TODO: not sure if OK but fixes error\n        return /*#__PURE__*/React.cloneElement(child, {\n          style: _reactNative.StyleSheet.compose(child.props.style, this.state.extraChildStyle)\n        });\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this$props = this.props,\n          _this$props$style = _this$props.style,\n          style = _this$props$style === void 0 ? {} : _this$props$style,\n          rest = (0, _objectWithoutProperties2.default)(_this$props, _excluded);\n        var extraUnderlayStyle = this.state.extraUnderlayStyle;\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_GenericTouchable.default, {\n          ...rest,\n          style: [style, extraUnderlayStyle],\n          onStateChange: this.onStateChange,\n          children: this.renderChildren()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 7\n        }, this);\n      }\n    }]);\n  }(_react.Component);\n  TouchableHighlight.defaultProps = {\n    ..._GenericTouchable.default.defaultProps,\n    activeOpacity: 0.85,\n    delayPressOut: 100,\n    underlayColor: 'black'\n  };\n});", "lineCount": 115, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_react"], [13, 12, 1, 0], [13, 15, 1, 0, "_interopRequireWildcard"], [13, 38, 1, 0], [13, 39, 1, 0, "require"], [13, 46, 1, 0], [13, 47, 1, 0, "_dependencyMap"], [13, 61, 1, 0], [14, 2, 1, 31], [14, 6, 1, 31, "React"], [14, 11, 1, 31], [14, 14, 1, 31, "_react"], [14, 20, 1, 31], [15, 2, 3, 0], [15, 6, 3, 0, "_GenericTouchable"], [15, 23, 3, 0], [15, 26, 3, 0, "_interopRequireWildcard"], [15, 49, 3, 0], [15, 50, 3, 0, "require"], [15, 57, 3, 0], [15, 58, 3, 0, "_dependencyMap"], [15, 72, 3, 0], [16, 2, 5, 0], [16, 6, 5, 0, "_reactNative"], [16, 18, 5, 0], [16, 21, 5, 0, "require"], [16, 28, 5, 0], [16, 29, 5, 0, "_dependencyMap"], [16, 43, 5, 0], [17, 2, 11, 22], [17, 6, 11, 22, "_jsxDevRuntime"], [17, 20, 11, 22], [17, 23, 11, 22, "require"], [17, 30, 11, 22], [17, 31, 11, 22, "_dependencyMap"], [17, 45, 11, 22], [18, 2, 11, 22], [18, 6, 11, 22, "_excluded"], [18, 15, 11, 22], [19, 2, 11, 22], [19, 6, 11, 22, "_jsxFileName"], [19, 18, 11, 22], [20, 2, 22, 0], [21, 0, 23, 0], [22, 0, 24, 0], [23, 2, 22, 0], [23, 11, 22, 0, "_interopRequireWildcard"], [23, 35, 22, 0, "e"], [23, 36, 22, 0], [23, 38, 22, 0, "t"], [23, 39, 22, 0], [23, 68, 22, 0, "WeakMap"], [23, 75, 22, 0], [23, 81, 22, 0, "r"], [23, 82, 22, 0], [23, 89, 22, 0, "WeakMap"], [23, 96, 22, 0], [23, 100, 22, 0, "n"], [23, 101, 22, 0], [23, 108, 22, 0, "WeakMap"], [23, 115, 22, 0], [23, 127, 22, 0, "_interopRequireWildcard"], [23, 150, 22, 0], [23, 162, 22, 0, "_interopRequireWildcard"], [23, 163, 22, 0, "e"], [23, 164, 22, 0], [23, 166, 22, 0, "t"], [23, 167, 22, 0], [23, 176, 22, 0, "t"], [23, 177, 22, 0], [23, 181, 22, 0, "e"], [23, 182, 22, 0], [23, 186, 22, 0, "e"], [23, 187, 22, 0], [23, 188, 22, 0, "__esModule"], [23, 198, 22, 0], [23, 207, 22, 0, "e"], [23, 208, 22, 0], [23, 214, 22, 0, "o"], [23, 215, 22, 0], [23, 217, 22, 0, "i"], [23, 218, 22, 0], [23, 220, 22, 0, "f"], [23, 221, 22, 0], [23, 226, 22, 0, "__proto__"], [23, 235, 22, 0], [23, 243, 22, 0, "default"], [23, 250, 22, 0], [23, 252, 22, 0, "e"], [23, 253, 22, 0], [23, 270, 22, 0, "e"], [23, 271, 22, 0], [23, 294, 22, 0, "e"], [23, 295, 22, 0], [23, 320, 22, 0, "e"], [23, 321, 22, 0], [23, 330, 22, 0, "f"], [23, 331, 22, 0], [23, 337, 22, 0, "o"], [23, 338, 22, 0], [23, 341, 22, 0, "t"], [23, 342, 22, 0], [23, 345, 22, 0, "n"], [23, 346, 22, 0], [23, 349, 22, 0, "r"], [23, 350, 22, 0], [23, 358, 22, 0, "o"], [23, 359, 22, 0], [23, 360, 22, 0, "has"], [23, 363, 22, 0], [23, 364, 22, 0, "e"], [23, 365, 22, 0], [23, 375, 22, 0, "o"], [23, 376, 22, 0], [23, 377, 22, 0, "get"], [23, 380, 22, 0], [23, 381, 22, 0, "e"], [23, 382, 22, 0], [23, 385, 22, 0, "o"], [23, 386, 22, 0], [23, 387, 22, 0, "set"], [23, 390, 22, 0], [23, 391, 22, 0, "e"], [23, 392, 22, 0], [23, 394, 22, 0, "f"], [23, 395, 22, 0], [23, 409, 22, 0, "_t"], [23, 411, 22, 0], [23, 415, 22, 0, "e"], [23, 416, 22, 0], [23, 432, 22, 0, "_t"], [23, 434, 22, 0], [23, 441, 22, 0, "hasOwnProperty"], [23, 455, 22, 0], [23, 456, 22, 0, "call"], [23, 460, 22, 0], [23, 461, 22, 0, "e"], [23, 462, 22, 0], [23, 464, 22, 0, "_t"], [23, 466, 22, 0], [23, 473, 22, 0, "i"], [23, 474, 22, 0], [23, 478, 22, 0, "o"], [23, 479, 22, 0], [23, 482, 22, 0, "Object"], [23, 488, 22, 0], [23, 489, 22, 0, "defineProperty"], [23, 503, 22, 0], [23, 508, 22, 0, "Object"], [23, 514, 22, 0], [23, 515, 22, 0, "getOwnPropertyDescriptor"], [23, 539, 22, 0], [23, 540, 22, 0, "e"], [23, 541, 22, 0], [23, 543, 22, 0, "_t"], [23, 545, 22, 0], [23, 552, 22, 0, "i"], [23, 553, 22, 0], [23, 554, 22, 0, "get"], [23, 557, 22, 0], [23, 561, 22, 0, "i"], [23, 562, 22, 0], [23, 563, 22, 0, "set"], [23, 566, 22, 0], [23, 570, 22, 0, "o"], [23, 571, 22, 0], [23, 572, 22, 0, "f"], [23, 573, 22, 0], [23, 575, 22, 0, "_t"], [23, 577, 22, 0], [23, 579, 22, 0, "i"], [23, 580, 22, 0], [23, 584, 22, 0, "f"], [23, 585, 22, 0], [23, 586, 22, 0, "_t"], [23, 588, 22, 0], [23, 592, 22, 0, "e"], [23, 593, 22, 0], [23, 594, 22, 0, "_t"], [23, 596, 22, 0], [23, 607, 22, 0, "f"], [23, 608, 22, 0], [23, 613, 22, 0, "e"], [23, 614, 22, 0], [23, 616, 22, 0, "t"], [23, 617, 22, 0], [24, 2, 22, 0], [24, 11, 22, 0, "_callSuper"], [24, 22, 22, 0, "t"], [24, 23, 22, 0], [24, 25, 22, 0, "o"], [24, 26, 22, 0], [24, 28, 22, 0, "e"], [24, 29, 22, 0], [24, 40, 22, 0, "o"], [24, 41, 22, 0], [24, 48, 22, 0, "_getPrototypeOf2"], [24, 64, 22, 0], [24, 65, 22, 0, "default"], [24, 72, 22, 0], [24, 74, 22, 0, "o"], [24, 75, 22, 0], [24, 82, 22, 0, "_possibleConstructorReturn2"], [24, 109, 22, 0], [24, 110, 22, 0, "default"], [24, 117, 22, 0], [24, 119, 22, 0, "t"], [24, 120, 22, 0], [24, 122, 22, 0, "_isNativeReflectConstruct"], [24, 147, 22, 0], [24, 152, 22, 0, "Reflect"], [24, 159, 22, 0], [24, 160, 22, 0, "construct"], [24, 169, 22, 0], [24, 170, 22, 0, "o"], [24, 171, 22, 0], [24, 173, 22, 0, "e"], [24, 174, 22, 0], [24, 186, 22, 0, "_getPrototypeOf2"], [24, 202, 22, 0], [24, 203, 22, 0, "default"], [24, 210, 22, 0], [24, 212, 22, 0, "t"], [24, 213, 22, 0], [24, 215, 22, 0, "constructor"], [24, 226, 22, 0], [24, 230, 22, 0, "o"], [24, 231, 22, 0], [24, 232, 22, 0, "apply"], [24, 237, 22, 0], [24, 238, 22, 0, "t"], [24, 239, 22, 0], [24, 241, 22, 0, "e"], [24, 242, 22, 0], [25, 2, 22, 0], [25, 11, 22, 0, "_isNativeReflectConstruct"], [25, 37, 22, 0], [25, 51, 22, 0, "t"], [25, 52, 22, 0], [25, 56, 22, 0, "Boolean"], [25, 63, 22, 0], [25, 64, 22, 0, "prototype"], [25, 73, 22, 0], [25, 74, 22, 0, "valueOf"], [25, 81, 22, 0], [25, 82, 22, 0, "call"], [25, 86, 22, 0], [25, 87, 22, 0, "Reflect"], [25, 94, 22, 0], [25, 95, 22, 0, "construct"], [25, 104, 22, 0], [25, 105, 22, 0, "Boolean"], [25, 112, 22, 0], [25, 145, 22, 0, "t"], [25, 146, 22, 0], [25, 159, 22, 0, "_isNativeReflectConstruct"], [25, 184, 22, 0], [25, 196, 22, 0, "_isNativeReflectConstruct"], [25, 197, 22, 0], [25, 210, 22, 0, "t"], [25, 211, 22, 0], [26, 2, 28, 0], [27, 0, 29, 0], [28, 0, 30, 0], [29, 0, 31, 0], [30, 0, 32, 0], [31, 2, 28, 0], [31, 6, 33, 21, "TouchableHighlight"], [31, 24, 33, 39], [31, 27, 33, 39, "exports"], [31, 34, 33, 39], [31, 35, 33, 39, "default"], [31, 42, 33, 39], [31, 68, 33, 39, "_Component"], [31, 78, 33, 39], [32, 4, 44, 2], [32, 13, 44, 2, "TouchableHighlight"], [32, 32, 44, 14, "props"], [32, 37, 44, 44], [32, 39, 44, 46], [33, 6, 44, 46], [33, 10, 44, 46, "_this"], [33, 15, 44, 46], [34, 6, 44, 46], [34, 10, 44, 46, "_classCallCheck2"], [34, 26, 44, 46], [34, 27, 44, 46, "default"], [34, 34, 44, 46], [34, 42, 44, 46, "TouchableHighlight"], [34, 60, 44, 46], [35, 6, 45, 4, "_this"], [35, 11, 45, 4], [35, 14, 45, 4, "_callSuper"], [35, 24, 45, 4], [35, 31, 45, 4, "TouchableHighlight"], [35, 49, 45, 4], [35, 52, 45, 10, "props"], [35, 57, 45, 15], [36, 6, 52, 2], [37, 6, 52, 2, "_this"], [37, 11, 52, 2], [37, 12, 53, 2, "showUnderlay"], [37, 24, 53, 14], [37, 27, 53, 17], [37, 33, 53, 23], [38, 8, 54, 4], [38, 12, 54, 8], [38, 13, 54, 9, "_this"], [38, 18, 54, 9], [38, 19, 54, 14, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [38, 34, 54, 29], [38, 35, 54, 30], [38, 36, 54, 31], [38, 38, 54, 33], [39, 10, 55, 6], [40, 8, 56, 4], [41, 8, 57, 4, "_this"], [41, 13, 57, 4], [41, 14, 57, 9, "setState"], [41, 22, 57, 17], [41, 23, 57, 18], [42, 10, 58, 6, "extraChildStyle"], [42, 25, 58, 21], [42, 27, 58, 23], [43, 12, 59, 8, "opacity"], [43, 19, 59, 15], [43, 21, 59, 17, "_this"], [43, 26, 59, 17], [43, 27, 59, 22, "props"], [43, 32, 59, 27], [43, 33, 59, 28, "activeOpacity"], [44, 10, 60, 6], [44, 11, 60, 7], [45, 10, 61, 6, "extraUnderlayStyle"], [45, 28, 61, 24], [45, 30, 61, 26], [46, 12, 62, 8, "backgroundColor"], [46, 27, 62, 23], [46, 29, 62, 25, "_this"], [46, 34, 62, 25], [46, 35, 62, 30, "props"], [46, 40, 62, 35], [46, 41, 62, 36, "underlayColor"], [47, 10, 63, 6], [48, 8, 64, 4], [48, 9, 64, 5], [48, 10, 64, 6], [49, 8, 65, 4, "_this"], [49, 13, 65, 4], [49, 14, 65, 9, "props"], [49, 19, 65, 14], [49, 20, 65, 15, "onShowUnderlay"], [49, 34, 65, 29], [49, 37, 65, 32], [49, 38, 65, 33], [50, 6, 66, 2], [50, 7, 66, 3], [51, 6, 66, 3, "_this"], [51, 11, 66, 3], [51, 12, 68, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [51, 27, 68, 17], [51, 30, 68, 20], [51, 36, 69, 4, "_this"], [51, 41, 69, 4], [51, 42, 69, 9, "props"], [51, 47, 69, 14], [51, 48, 69, 15, "onPress"], [51, 55, 69, 22], [51, 59, 70, 4, "_this"], [51, 64, 70, 4], [51, 65, 70, 9, "props"], [51, 70, 70, 14], [51, 71, 70, 15, "onPressIn"], [51, 80, 70, 24], [51, 84, 71, 4, "_this"], [51, 89, 71, 4], [51, 90, 71, 9, "props"], [51, 95, 71, 14], [51, 96, 71, 15, "onPressOut"], [51, 106, 71, 25], [51, 110, 72, 4, "_this"], [51, 115, 72, 4], [51, 116, 72, 9, "props"], [51, 121, 72, 14], [51, 122, 72, 15, "onLongPress"], [51, 133, 72, 26], [52, 6, 72, 26, "_this"], [52, 11, 72, 26], [52, 12, 74, 2, "<PERSON><PERSON><PERSON><PERSON>"], [52, 24, 74, 14], [52, 27, 74, 17], [52, 33, 74, 23], [53, 8, 75, 4, "_this"], [53, 13, 75, 4], [53, 14, 75, 9, "setState"], [53, 22, 75, 17], [53, 23, 75, 18], [54, 10, 76, 6, "extraChildStyle"], [54, 25, 76, 21], [54, 27, 76, 23], [54, 31, 76, 27], [55, 10, 77, 6, "extraUnderlayStyle"], [55, 28, 77, 24], [55, 30, 77, 26], [56, 8, 78, 4], [56, 9, 78, 5], [56, 10, 78, 6], [57, 8, 79, 4, "_this"], [57, 13, 79, 4], [57, 14, 79, 9, "props"], [57, 19, 79, 14], [57, 20, 79, 15, "onHideUnderlay"], [57, 34, 79, 29], [57, 37, 79, 32], [57, 38, 79, 33], [58, 6, 80, 2], [58, 7, 80, 3], [59, 6, 80, 3, "_this"], [59, 11, 80, 3], [59, 12, 95, 2, "onStateChange"], [59, 25, 95, 15], [59, 28, 95, 18], [59, 29, 95, 19, "_from"], [59, 34, 95, 32], [59, 36, 95, 34, "to"], [59, 38, 95, 44], [59, 43, 95, 49], [60, 8, 96, 4], [60, 12, 96, 8, "to"], [60, 14, 96, 10], [60, 19, 96, 15, "TOUCHABLE_STATE"], [60, 52, 96, 30], [60, 53, 96, 31, "BEGAN"], [60, 58, 96, 36], [60, 60, 96, 38], [61, 10, 97, 6, "_this"], [61, 15, 97, 6], [61, 16, 97, 11, "showUnderlay"], [61, 28, 97, 23], [61, 29, 97, 24], [61, 30, 97, 25], [62, 8, 98, 4], [62, 9, 98, 5], [62, 15, 98, 11], [62, 19, 99, 6, "to"], [62, 21, 99, 8], [62, 26, 99, 13, "TOUCHABLE_STATE"], [62, 59, 99, 28], [62, 60, 99, 29, "UNDETERMINED"], [62, 72, 99, 41], [62, 76, 100, 6, "to"], [62, 78, 100, 8], [62, 83, 100, 13, "TOUCHABLE_STATE"], [62, 116, 100, 28], [62, 117, 100, 29, "MOVED_OUTSIDE"], [62, 130, 100, 42], [62, 132, 101, 6], [63, 10, 102, 6, "_this"], [63, 15, 102, 6], [63, 16, 102, 11, "<PERSON><PERSON><PERSON><PERSON>"], [63, 28, 102, 23], [63, 29, 102, 24], [63, 30, 102, 25], [64, 8, 103, 4], [65, 6, 104, 2], [65, 7, 104, 3], [66, 6, 46, 4, "_this"], [66, 11, 46, 4], [66, 12, 46, 9, "state"], [66, 17, 46, 14], [66, 20, 46, 17], [67, 8, 47, 6, "extraChildStyle"], [67, 23, 47, 21], [67, 25, 47, 23], [67, 29, 47, 27], [68, 8, 48, 6, "extraUnderlayStyle"], [68, 26, 48, 24], [68, 28, 48, 26], [69, 6, 49, 4], [69, 7, 49, 5], [70, 6, 49, 6], [70, 13, 49, 6, "_this"], [70, 18, 49, 6], [71, 4, 50, 2], [72, 4, 50, 3], [72, 8, 50, 3, "_inherits2"], [72, 18, 50, 3], [72, 19, 50, 3, "default"], [72, 26, 50, 3], [72, 28, 50, 3, "TouchableHighlight"], [72, 46, 50, 3], [72, 48, 50, 3, "_Component"], [72, 58, 50, 3], [73, 4, 50, 3], [73, 15, 50, 3, "_createClass2"], [73, 28, 50, 3], [73, 29, 50, 3, "default"], [73, 36, 50, 3], [73, 38, 50, 3, "TouchableHighlight"], [73, 56, 50, 3], [74, 6, 50, 3, "key"], [74, 9, 50, 3], [75, 6, 50, 3, "value"], [75, 11, 50, 3], [75, 13, 82, 2], [75, 22, 82, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [75, 36, 82, 16, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [75, 37, 82, 16], [75, 39, 82, 19], [76, 8, 83, 4], [76, 12, 83, 8], [76, 13, 83, 9], [76, 17, 83, 13], [76, 18, 83, 14, "props"], [76, 23, 83, 19], [76, 24, 83, 20, "children"], [76, 32, 83, 28], [76, 34, 83, 30], [77, 10, 84, 6], [77, 30, 84, 13], [77, 34, 84, 13, "_jsxDevRuntime"], [77, 48, 84, 13], [77, 49, 84, 13, "jsxDEV"], [77, 55, 84, 13], [77, 57, 84, 14, "_reactNative"], [77, 69, 84, 14], [77, 70, 84, 14, "View"], [77, 74, 84, 18], [78, 12, 84, 18, "fileName"], [78, 20, 84, 18], [78, 22, 84, 18, "_jsxFileName"], [78, 34, 84, 18], [79, 12, 84, 18, "lineNumber"], [79, 22, 84, 18], [80, 12, 84, 18, "columnNumber"], [80, 24, 84, 18], [81, 10, 84, 18], [81, 17, 84, 20], [81, 18, 84, 21], [82, 8, 85, 4], [83, 8, 87, 4], [83, 12, 87, 10, "child"], [83, 17, 87, 15], [83, 20, 87, 18, "React"], [83, 25, 87, 23], [83, 26, 87, 24, "Children"], [83, 34, 87, 32], [83, 35, 87, 33, "only"], [83, 39, 87, 37], [83, 40, 88, 6], [83, 44, 88, 10], [83, 45, 88, 11, "props"], [83, 50, 88, 16], [83, 51, 88, 17, "children"], [83, 59, 89, 4], [83, 60, 89, 38], [83, 61, 89, 39], [83, 62, 89, 40], [84, 8, 90, 4], [84, 28, 90, 11, "React"], [84, 33, 90, 16], [84, 34, 90, 17, "cloneElement"], [84, 46, 90, 29], [84, 47, 90, 30, "child"], [84, 52, 90, 35], [84, 54, 90, 37], [85, 10, 91, 6, "style"], [85, 15, 91, 11], [85, 17, 91, 13, "StyleSheet"], [85, 40, 91, 23], [85, 41, 91, 24, "compose"], [85, 48, 91, 31], [85, 49, 91, 32, "child"], [85, 54, 91, 37], [85, 55, 91, 38, "props"], [85, 60, 91, 43], [85, 61, 91, 44, "style"], [85, 66, 91, 49], [85, 68, 91, 51], [85, 72, 91, 55], [85, 73, 91, 56, "state"], [85, 78, 91, 61], [85, 79, 91, 62, "extraChildStyle"], [85, 94, 91, 77], [86, 8, 92, 4], [86, 9, 92, 5], [86, 10, 92, 6], [87, 6, 93, 2], [88, 4, 93, 3], [89, 6, 93, 3, "key"], [89, 9, 93, 3], [90, 6, 93, 3, "value"], [90, 11, 93, 3], [90, 13, 106, 2], [90, 22, 106, 2, "render"], [90, 28, 106, 8, "render"], [90, 29, 106, 8], [90, 31, 106, 11], [91, 8, 107, 4], [91, 12, 107, 4, "_this$props"], [91, 23, 107, 4], [91, 26, 107, 36], [91, 30, 107, 40], [91, 31, 107, 41, "props"], [91, 36, 107, 46], [92, 10, 107, 46, "_this$props$style"], [92, 27, 107, 46], [92, 30, 107, 46, "_this$props"], [92, 41, 107, 46], [92, 42, 107, 12, "style"], [92, 47, 107, 17], [93, 10, 107, 12, "style"], [93, 15, 107, 17], [93, 18, 107, 17, "_this$props$style"], [93, 35, 107, 17], [93, 49, 107, 20], [93, 50, 107, 21], [93, 51, 107, 22], [93, 54, 107, 22, "_this$props$style"], [93, 71, 107, 22], [94, 10, 107, 27, "rest"], [94, 14, 107, 31], [94, 21, 107, 31, "_objectWithoutProperties2"], [94, 46, 107, 31], [94, 47, 107, 31, "default"], [94, 54, 107, 31], [94, 56, 107, 31, "_this$props"], [94, 67, 107, 31], [94, 69, 107, 31, "_excluded"], [94, 78, 107, 31], [95, 8, 108, 4], [95, 12, 108, 12, "extraUnderlayStyle"], [95, 30, 108, 30], [95, 33, 108, 35], [95, 37, 108, 39], [95, 38, 108, 40, "state"], [95, 43, 108, 45], [95, 44, 108, 12, "extraUnderlayStyle"], [95, 62, 108, 30], [96, 8, 109, 4], [96, 28, 110, 6], [96, 32, 110, 6, "_jsxDevRuntime"], [96, 46, 110, 6], [96, 47, 110, 6, "jsxDEV"], [96, 53, 110, 6], [96, 55, 110, 7, "_GenericTouchable"], [96, 72, 110, 7], [96, 73, 110, 7, "default"], [96, 80, 110, 23], [97, 10, 110, 23], [97, 13, 111, 12, "rest"], [97, 17, 111, 16], [98, 10, 112, 8, "style"], [98, 15, 112, 13], [98, 17, 112, 15], [98, 18, 112, 16, "style"], [98, 23, 112, 21], [98, 25, 112, 23, "extraUnderlayStyle"], [98, 43, 112, 41], [98, 44, 112, 43], [99, 10, 113, 8, "onStateChange"], [99, 23, 113, 21], [99, 25, 113, 23], [99, 29, 113, 27], [99, 30, 113, 28, "onStateChange"], [99, 43, 113, 42], [100, 10, 113, 42, "children"], [100, 18, 113, 42], [100, 20, 114, 9], [100, 24, 114, 13], [100, 25, 114, 14, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [100, 39, 114, 28], [100, 40, 114, 29], [101, 8, 114, 30], [102, 10, 114, 30, "fileName"], [102, 18, 114, 30], [102, 20, 114, 30, "_jsxFileName"], [102, 32, 114, 30], [103, 10, 114, 30, "lineNumber"], [103, 20, 114, 30], [104, 10, 114, 30, "columnNumber"], [104, 22, 114, 30], [105, 8, 114, 30], [105, 15, 115, 24], [105, 16, 115, 25], [106, 6, 117, 2], [107, 4, 117, 3], [108, 2, 117, 3], [108, 4, 33, 48, "Component"], [108, 20, 33, 57], [109, 2, 33, 21, "TouchableHighlight"], [109, 20, 33, 39], [109, 21, 37, 9, "defaultProps"], [109, 33, 37, 21], [109, 36, 37, 24], [110, 4, 38, 4], [110, 7, 38, 7, "GenericTouchable"], [110, 32, 38, 23], [110, 33, 38, 24, "defaultProps"], [110, 45, 38, 36], [111, 4, 39, 4, "activeOpacity"], [111, 17, 39, 17], [111, 19, 39, 19], [111, 23, 39, 23], [112, 4, 40, 4, "delayPressOut"], [112, 17, 40, 17], [112, 19, 40, 19], [112, 22, 40, 22], [113, 4, 41, 4, "underlayColor"], [113, 17, 41, 17], [113, 19, 41, 19], [114, 2, 42, 2], [114, 3, 42, 3], [115, 0, 42, 3], [115, 3]], "functionMap": {"names": ["<global>", "TouchableHighlight", "constructor", "showUnderlay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onStateChange", "render"], "mappings": "AAA;eCgC;ECW;GDM;iBEG;GFa;oBGE;0BHI;iBIE;GJM;EKE;GLW;kBME;GNS;EOE;GPW;CDC"}}, "type": "js/module"}]}