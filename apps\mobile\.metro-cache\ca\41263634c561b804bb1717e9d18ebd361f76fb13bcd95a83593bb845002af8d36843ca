{"dependencies": [{"name": "./core.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 3, "column": 14, "index": 29}, "end": {"line": 3, "column": 34, "index": 49}}], "key": "ud7OA+9V36/ALBXnU4BYy09opnw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var Promise = require(_dependencyMap[0], \"./core.js\");\n  module.exports = Promise;\n  Promise.prototype.finally = function (f) {\n    return this.then(function (value) {\n      return Promise.resolve(f()).then(function () {\n        return value;\n      });\n    }, function (err) {\n      return Promise.resolve(f()).then(function () {\n        throw err;\n      });\n    });\n  };\n});", "lineCount": 17, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [4, 6, 3, 4, "Promise"], [4, 13, 3, 11], [4, 16, 3, 14, "require"], [4, 23, 3, 21], [4, 24, 3, 21, "_dependencyMap"], [4, 38, 3, 21], [4, 54, 3, 33], [4, 55, 3, 34], [5, 2, 5, 0, "module"], [5, 8, 5, 6], [5, 9, 5, 7, "exports"], [5, 16, 5, 14], [5, 19, 5, 17, "Promise"], [5, 26, 5, 24], [6, 2, 6, 0, "Promise"], [6, 9, 6, 7], [6, 10, 6, 8, "prototype"], [6, 19, 6, 17], [6, 20, 6, 18, "finally"], [6, 27, 6, 25], [6, 30, 6, 28], [6, 40, 6, 38, "f"], [6, 41, 6, 39], [6, 43, 6, 41], [7, 4, 7, 2], [7, 11, 7, 9], [7, 15, 7, 13], [7, 16, 7, 14, "then"], [7, 20, 7, 18], [7, 21, 7, 19], [7, 31, 7, 29, "value"], [7, 36, 7, 34], [7, 38, 7, 36], [8, 6, 8, 4], [8, 13, 8, 11, "Promise"], [8, 20, 8, 18], [8, 21, 8, 19, "resolve"], [8, 28, 8, 26], [8, 29, 8, 27, "f"], [8, 30, 8, 28], [8, 31, 8, 29], [8, 32, 8, 30], [8, 33, 8, 31], [8, 34, 8, 32, "then"], [8, 38, 8, 36], [8, 39, 8, 37], [8, 51, 8, 49], [9, 8, 9, 6], [9, 15, 9, 13, "value"], [9, 20, 9, 18], [10, 6, 10, 4], [10, 7, 10, 5], [10, 8, 10, 6], [11, 4, 11, 2], [11, 5, 11, 3], [11, 7, 11, 5], [11, 17, 11, 15, "err"], [11, 20, 11, 18], [11, 22, 11, 20], [12, 6, 12, 4], [12, 13, 12, 11, "Promise"], [12, 20, 12, 18], [12, 21, 12, 19, "resolve"], [12, 28, 12, 26], [12, 29, 12, 27, "f"], [12, 30, 12, 28], [12, 31, 12, 29], [12, 32, 12, 30], [12, 33, 12, 31], [12, 34, 12, 32, "then"], [12, 38, 12, 36], [12, 39, 12, 37], [12, 51, 12, 49], [13, 8, 13, 6], [13, 14, 13, 12, "err"], [13, 17, 13, 15], [14, 6, 14, 4], [14, 7, 14, 5], [14, 8, 14, 6], [15, 4, 15, 2], [15, 5, 15, 3], [15, 6, 15, 4], [16, 2, 16, 0], [16, 3, 16, 1], [17, 0, 16, 2], [17, 3]], "functionMap": {"names": ["<global>", "Promise.prototype._finally", "then$argument_0", "Promise.resolve.then$argument_0", "then$argument_1"], "mappings": "AAA;4BCK;mBCC;qCCC;KDE;GDC,EG;qCDC;KCE;GHC;CDC"}}, "type": "js/module"}]}