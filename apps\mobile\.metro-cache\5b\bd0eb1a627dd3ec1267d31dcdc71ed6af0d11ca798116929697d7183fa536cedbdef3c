{"dependencies": [{"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 22, "index": 131}, "end": {"line": 4, "column": 50, "index": 159}}], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 23, "index": 184}, "end": {"line": 5, "column": 46, "index": 207}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "./style-sheet", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 22, "index": 231}, "end": {"line": 6, "column": 46, "index": 255}}], "key": "/bYi8u7M4Q4+2ZvRGwVsQ8kBQ3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _slicedToArray = require(_dependencyMap[0], \"@babel/runtime/helpers/slicedToArray\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withExpoSnack = void 0;\n  var jsx_runtime_1 = require(_dependencyMap[1], \"react/jsx-runtime\");\n  var react_native_1 = require(_dependencyMap[2], \"react-native\");\n  var style_sheet_1 = require(_dependencyMap[3], \"./style-sheet\");\n  var fetched = {};\n  var canUseCSS = false;\n  // const canUseCSS = typeof StyleSheet.create({ test: {} }).test !== \"number\";\n  function ExpoSnackWrapper(_ref) {\n    var children = _ref.children;\n    return react_native_1.Platform.OS === \"web\" && canUseCSS ? (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {\n      children: [(0, jsx_runtime_1.jsx)(\"script\", {\n        id: \"tailwind-cdn\",\n        type: \"text/javascript\",\n        src: \"https://cdn.tailwindcss.com\"\n      }), children]\n    }) : (0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, {\n      children: children\n    });\n  }\n  function withExpoSnack(Component) {\n    var theme = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    function dangerouslyCompileStyles(css, store) {\n      var themeString = JSON.stringify(theme);\n      css = css.replace(/\\s+/g, \" \").trim();\n      var cacheKey = `${css}${themeString}`;\n      if (!css) return;\n      if (fetched[cacheKey]) return;\n      fetch(`https://nativewind-demo-compiler.vercel.app/api/compile?css=${css}&theme=${themeString}`).then(response => response.json()).then(_ref2 => {\n        var body = _ref2.body;\n        fetched[cacheKey] = true;\n        store.create(body);\n        // This the async, the store will have already cached\n        // incorrect results, so we need to clear these\n        // and set the correct ones\n        for (var className of css.split(/\\s+/)) {\n          delete store.snapshot[className];\n        }\n        for (var key of Object.keys(store.snapshot)) {\n          if (key.includes(css)) {\n            delete store.snapshot[key];\n            var _key$split = key.split(\".\"),\n              _key$split2 = _slicedToArray(_key$split, 2),\n              bit = _key$split2[1];\n            store.prepare(css, {\n              baseBit: Number.parseInt(bit)\n            });\n          }\n        }\n        store.notify();\n      }).catch(error => {\n        console.error(error);\n      });\n    }\n    if (!canUseCSS) {\n      style_sheet_1.NativeWindStyleSheet.setDangerouslyCompileStyles(dangerouslyCompileStyles);\n      style_sheet_1.NativeWindStyleSheet.setOutput({\n        default: \"native\"\n      });\n    }\n    return () => (0, jsx_runtime_1.jsx)(ExpoSnackWrapper, {\n      children: (0, jsx_runtime_1.jsx)(Component, {})\n    });\n  }\n  exports.withExpoSnack = withExpoSnack;\n});", "lineCount": 72, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_slicedToArray"], [4, 20, 1, 13], [4, 23, 1, 13, "require"], [4, 30, 1, 13], [4, 31, 1, 13, "_dependencyMap"], [4, 45, 1, 13], [5, 2, 2, 0, "Object"], [5, 8, 2, 6], [5, 9, 2, 7, "defineProperty"], [5, 23, 2, 21], [5, 24, 2, 22, "exports"], [5, 31, 2, 29], [5, 33, 2, 31], [5, 45, 2, 43], [5, 47, 2, 45], [6, 4, 2, 47, "value"], [6, 9, 2, 52], [6, 11, 2, 54], [7, 2, 2, 59], [7, 3, 2, 60], [7, 4, 2, 61], [8, 2, 3, 0, "exports"], [8, 9, 3, 7], [8, 10, 3, 8, "withExpoSnack"], [8, 23, 3, 21], [8, 26, 3, 24], [8, 31, 3, 29], [8, 32, 3, 30], [9, 2, 4, 0], [9, 6, 4, 6, "jsx_runtime_1"], [9, 19, 4, 19], [9, 22, 4, 22, "require"], [9, 29, 4, 29], [9, 30, 4, 29, "_dependencyMap"], [9, 44, 4, 29], [9, 68, 4, 49], [9, 69, 4, 50], [10, 2, 5, 0], [10, 6, 5, 6, "react_native_1"], [10, 20, 5, 20], [10, 23, 5, 23, "require"], [10, 30, 5, 30], [10, 31, 5, 30, "_dependencyMap"], [10, 45, 5, 30], [10, 64, 5, 45], [10, 65, 5, 46], [11, 2, 6, 0], [11, 6, 6, 6, "style_sheet_1"], [11, 19, 6, 19], [11, 22, 6, 22, "require"], [11, 29, 6, 29], [11, 30, 6, 29, "_dependencyMap"], [11, 44, 6, 29], [11, 64, 6, 45], [11, 65, 6, 46], [12, 2, 7, 0], [12, 6, 7, 6, "fetched"], [12, 13, 7, 13], [12, 16, 7, 16], [12, 17, 7, 17], [12, 18, 7, 18], [13, 2, 8, 0], [13, 6, 8, 6, "canUseCSS"], [13, 15, 8, 15], [13, 18, 8, 18], [13, 23, 8, 23], [14, 2, 9, 0], [15, 2, 10, 0], [15, 11, 10, 9, "ExpoSnackWrapper"], [15, 27, 10, 25, "ExpoSnackWrapper"], [15, 28, 10, 25, "_ref"], [15, 32, 10, 25], [15, 34, 10, 40], [16, 4, 10, 40], [16, 8, 10, 28, "children"], [16, 16, 10, 36], [16, 19, 10, 36, "_ref"], [16, 23, 10, 36], [16, 24, 10, 28, "children"], [16, 32, 10, 36], [17, 4, 11, 4], [17, 11, 11, 11, "react_native_1"], [17, 25, 11, 25], [17, 26, 11, 26, "Platform"], [17, 34, 11, 34], [17, 35, 11, 35, "OS"], [17, 37, 11, 37], [17, 42, 11, 42], [17, 47, 11, 47], [17, 51, 11, 51, "canUseCSS"], [17, 60, 11, 60], [17, 63, 11, 64], [17, 64, 11, 65], [17, 65, 11, 66], [17, 67, 11, 68, "jsx_runtime_1"], [17, 80, 11, 81], [17, 81, 11, 82, "jsxs"], [17, 85, 11, 86], [17, 87, 11, 88, "jsx_runtime_1"], [17, 100, 11, 101], [17, 101, 11, 102, "Fragment"], [17, 109, 11, 110], [17, 111, 11, 112], [18, 6, 11, 114, "children"], [18, 14, 11, 122], [18, 16, 11, 124], [18, 17, 11, 125], [18, 18, 11, 126], [18, 19, 11, 127], [18, 21, 11, 129, "jsx_runtime_1"], [18, 34, 11, 142], [18, 35, 11, 143, "jsx"], [18, 38, 11, 146], [18, 40, 11, 148], [18, 48, 11, 156], [18, 50, 11, 158], [19, 8, 11, 160, "id"], [19, 10, 11, 162], [19, 12, 11, 164], [19, 26, 11, 178], [20, 8, 11, 180, "type"], [20, 12, 11, 184], [20, 14, 11, 186], [20, 31, 11, 203], [21, 8, 11, 205, "src"], [21, 11, 11, 208], [21, 13, 11, 210], [22, 6, 11, 240], [22, 7, 11, 241], [22, 8, 11, 242], [22, 10, 11, 244, "children"], [22, 18, 11, 252], [23, 4, 11, 254], [23, 5, 11, 255], [23, 6, 11, 256], [23, 9, 11, 261], [23, 10, 11, 262], [23, 11, 11, 263], [23, 13, 11, 265, "jsx_runtime_1"], [23, 26, 11, 278], [23, 27, 11, 279, "jsx"], [23, 30, 11, 282], [23, 32, 11, 284, "jsx_runtime_1"], [23, 45, 11, 297], [23, 46, 11, 298, "Fragment"], [23, 54, 11, 306], [23, 56, 11, 308], [24, 6, 11, 310, "children"], [24, 14, 11, 318], [24, 16, 11, 320, "children"], [25, 4, 11, 329], [25, 5, 11, 330], [25, 6, 11, 332], [26, 2, 12, 0], [27, 2, 13, 0], [27, 11, 13, 9, "withExpoSnack"], [27, 24, 13, 22, "withExpoSnack"], [27, 25, 13, 23, "Component"], [27, 34, 13, 32], [27, 36, 13, 46], [28, 4, 13, 46], [28, 8, 13, 34, "theme"], [28, 13, 13, 39], [28, 16, 13, 39, "arguments"], [28, 25, 13, 39], [28, 26, 13, 39, "length"], [28, 32, 13, 39], [28, 40, 13, 39, "arguments"], [28, 49, 13, 39], [28, 57, 13, 39, "undefined"], [28, 66, 13, 39], [28, 69, 13, 39, "arguments"], [28, 78, 13, 39], [28, 84, 13, 42], [28, 85, 13, 43], [28, 86, 13, 44], [29, 4, 14, 4], [29, 13, 14, 13, "dangerouslyCompileStyles"], [29, 37, 14, 37, "dangerouslyCompileStyles"], [29, 38, 14, 38, "css"], [29, 41, 14, 41], [29, 43, 14, 43, "store"], [29, 48, 14, 48], [29, 50, 14, 50], [30, 6, 15, 8], [30, 10, 15, 14, "themeString"], [30, 21, 15, 25], [30, 24, 15, 28, "JSON"], [30, 28, 15, 32], [30, 29, 15, 33, "stringify"], [30, 38, 15, 42], [30, 39, 15, 43, "theme"], [30, 44, 15, 48], [30, 45, 15, 49], [31, 6, 16, 8, "css"], [31, 9, 16, 11], [31, 12, 16, 14, "css"], [31, 15, 16, 17], [31, 16, 16, 18, "replace"], [31, 23, 16, 25], [31, 24, 16, 26], [31, 30, 16, 32], [31, 32, 16, 34], [31, 35, 16, 37], [31, 36, 16, 38], [31, 37, 16, 39, "trim"], [31, 41, 16, 43], [31, 42, 16, 44], [31, 43, 16, 45], [32, 6, 17, 8], [32, 10, 17, 14, "cache<PERSON>ey"], [32, 18, 17, 22], [32, 21, 17, 25], [32, 24, 17, 28, "css"], [32, 27, 17, 31], [32, 30, 17, 34, "themeString"], [32, 41, 17, 45], [32, 43, 17, 47], [33, 6, 18, 8], [33, 10, 18, 12], [33, 11, 18, 13, "css"], [33, 14, 18, 16], [33, 16, 19, 12], [34, 6, 20, 8], [34, 10, 20, 12, "fetched"], [34, 17, 20, 19], [34, 18, 20, 20, "cache<PERSON>ey"], [34, 26, 20, 28], [34, 27, 20, 29], [34, 29, 21, 12], [35, 6, 22, 8, "fetch"], [35, 11, 22, 13], [35, 12, 22, 14], [35, 75, 22, 77, "css"], [35, 78, 22, 80], [35, 88, 22, 90, "themeString"], [35, 99, 22, 101], [35, 101, 22, 103], [35, 102, 22, 104], [35, 103, 23, 13, "then"], [35, 107, 23, 17], [35, 108, 23, 19, "response"], [35, 116, 23, 27], [35, 120, 23, 32, "response"], [35, 128, 23, 40], [35, 129, 23, 41, "json"], [35, 133, 23, 45], [35, 134, 23, 46], [35, 135, 23, 47], [35, 136, 23, 48], [35, 137, 24, 13, "then"], [35, 141, 24, 17], [35, 142, 24, 18, "_ref2"], [35, 147, 24, 18], [35, 151, 24, 32], [36, 8, 24, 32], [36, 12, 24, 21, "body"], [36, 16, 24, 25], [36, 19, 24, 25, "_ref2"], [36, 24, 24, 25], [36, 25, 24, 21, "body"], [36, 29, 24, 25], [37, 8, 25, 12, "fetched"], [37, 15, 25, 19], [37, 16, 25, 20, "cache<PERSON>ey"], [37, 24, 25, 28], [37, 25, 25, 29], [37, 28, 25, 32], [37, 32, 25, 36], [38, 8, 26, 12, "store"], [38, 13, 26, 17], [38, 14, 26, 18, "create"], [38, 20, 26, 24], [38, 21, 26, 25, "body"], [38, 25, 26, 29], [38, 26, 26, 30], [39, 8, 27, 12], [40, 8, 28, 12], [41, 8, 29, 12], [42, 8, 30, 12], [42, 13, 30, 17], [42, 17, 30, 23, "className"], [42, 26, 30, 32], [42, 30, 30, 36, "css"], [42, 33, 30, 39], [42, 34, 30, 40, "split"], [42, 39, 30, 45], [42, 40, 30, 46], [42, 45, 30, 51], [42, 46, 30, 52], [42, 48, 30, 54], [43, 10, 31, 16], [43, 17, 31, 23, "store"], [43, 22, 31, 28], [43, 23, 31, 29, "snapshot"], [43, 31, 31, 37], [43, 32, 31, 38, "className"], [43, 41, 31, 47], [43, 42, 31, 48], [44, 8, 32, 12], [45, 8, 33, 12], [45, 13, 33, 17], [45, 17, 33, 23, "key"], [45, 20, 33, 26], [45, 24, 33, 30, "Object"], [45, 30, 33, 36], [45, 31, 33, 37, "keys"], [45, 35, 33, 41], [45, 36, 33, 42, "store"], [45, 41, 33, 47], [45, 42, 33, 48, "snapshot"], [45, 50, 33, 56], [45, 51, 33, 57], [45, 53, 33, 59], [46, 10, 34, 16], [46, 14, 34, 20, "key"], [46, 17, 34, 23], [46, 18, 34, 24, "includes"], [46, 26, 34, 32], [46, 27, 34, 33, "css"], [46, 30, 34, 36], [46, 31, 34, 37], [46, 33, 34, 39], [47, 12, 35, 20], [47, 19, 35, 27, "store"], [47, 24, 35, 32], [47, 25, 35, 33, "snapshot"], [47, 33, 35, 41], [47, 34, 35, 42, "key"], [47, 37, 35, 45], [47, 38, 35, 46], [48, 12, 36, 20], [48, 16, 36, 20, "_key$split"], [48, 26, 36, 20], [48, 29, 36, 36, "key"], [48, 32, 36, 39], [48, 33, 36, 40, "split"], [48, 38, 36, 45], [48, 39, 36, 46], [48, 42, 36, 49], [48, 43, 36, 50], [49, 14, 36, 50, "_key$split2"], [49, 25, 36, 50], [49, 28, 36, 50, "_slicedToArray"], [49, 42, 36, 50], [49, 43, 36, 50, "_key$split"], [49, 53, 36, 50], [50, 14, 36, 29, "bit"], [50, 17, 36, 32], [50, 20, 36, 32, "_key$split2"], [50, 31, 36, 32], [51, 12, 37, 20, "store"], [51, 17, 37, 25], [51, 18, 37, 26, "prepare"], [51, 25, 37, 33], [51, 26, 37, 34, "css"], [51, 29, 37, 37], [51, 31, 37, 39], [52, 14, 37, 41, "baseBit"], [52, 21, 37, 48], [52, 23, 37, 50, "Number"], [52, 29, 37, 56], [52, 30, 37, 57, "parseInt"], [52, 38, 37, 65], [52, 39, 37, 66, "bit"], [52, 42, 37, 69], [53, 12, 37, 71], [53, 13, 37, 72], [53, 14, 37, 73], [54, 10, 38, 16], [55, 8, 39, 12], [56, 8, 40, 12, "store"], [56, 13, 40, 17], [56, 14, 40, 18, "notify"], [56, 20, 40, 24], [56, 21, 40, 25], [56, 22, 40, 26], [57, 6, 41, 8], [57, 7, 41, 9], [57, 8, 41, 10], [57, 9, 42, 13, "catch"], [57, 14, 42, 18], [57, 15, 42, 20, "error"], [57, 20, 42, 25], [57, 24, 42, 30], [58, 8, 43, 12, "console"], [58, 15, 43, 19], [58, 16, 43, 20, "error"], [58, 21, 43, 25], [58, 22, 43, 26, "error"], [58, 27, 43, 31], [58, 28, 43, 32], [59, 6, 44, 8], [59, 7, 44, 9], [59, 8, 44, 10], [60, 4, 45, 4], [61, 4, 46, 4], [61, 8, 46, 8], [61, 9, 46, 9, "canUseCSS"], [61, 18, 46, 18], [61, 20, 46, 20], [62, 6, 47, 8, "style_sheet_1"], [62, 19, 47, 21], [62, 20, 47, 22, "NativeWindStyleSheet"], [62, 40, 47, 42], [62, 41, 47, 43, "setDangerouslyCompileStyles"], [62, 68, 47, 70], [62, 69, 47, 71, "dangerouslyCompileStyles"], [62, 93, 47, 95], [62, 94, 47, 96], [63, 6, 48, 8, "style_sheet_1"], [63, 19, 48, 21], [63, 20, 48, 22, "NativeWindStyleSheet"], [63, 40, 48, 42], [63, 41, 48, 43, "setOutput"], [63, 50, 48, 52], [63, 51, 48, 53], [64, 8, 49, 12, "default"], [64, 15, 49, 19], [64, 17, 49, 21], [65, 6, 50, 8], [65, 7, 50, 9], [65, 8, 50, 10], [66, 4, 51, 4], [67, 4, 52, 4], [67, 11, 52, 11], [67, 17, 52, 18], [67, 18, 52, 19], [67, 19, 52, 20], [67, 21, 52, 22, "jsx_runtime_1"], [67, 34, 52, 35], [67, 35, 52, 36, "jsx"], [67, 38, 52, 39], [67, 40, 52, 41, "ExpoSnackWrapper"], [67, 56, 52, 57], [67, 58, 52, 59], [68, 6, 52, 61, "children"], [68, 14, 52, 69], [68, 16, 52, 71], [68, 17, 52, 72], [68, 18, 52, 73], [68, 20, 52, 75, "jsx_runtime_1"], [68, 33, 52, 88], [68, 34, 52, 89, "jsx"], [68, 37, 52, 92], [68, 39, 52, 94, "Component"], [68, 48, 52, 103], [68, 50, 52, 105], [68, 51, 52, 106], [68, 52, 52, 107], [69, 4, 52, 109], [69, 5, 52, 110], [69, 6, 52, 112], [70, 2, 53, 0], [71, 2, 54, 0, "exports"], [71, 9, 54, 7], [71, 10, 54, 8, "withExpoSnack"], [71, 23, 54, 21], [71, 26, 54, 24, "withExpoSnack"], [71, 39, 54, 37], [72, 0, 54, 38], [72, 3]], "functionMap": {"names": ["<global>", "ExpoSnackWrapper", "withExpoSnack", "dangerouslyCompileStyles", "fetch.then$argument_0", "fetch.then.then$argument_0", "fetch.then.then._catch$argument_0", "<anonymous>"], "mappings": "AAA;ACS;CDE;AEC;ICC;kBCS,6BD;kBEC;SFiB;mBGC;SHE;KDC;WKO,qGL;CFC"}}, "type": "js/module"}]}