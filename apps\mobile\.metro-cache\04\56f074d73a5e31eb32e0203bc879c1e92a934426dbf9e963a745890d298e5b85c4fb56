{"dependencies": [{"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 35, "index": 50}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 51}, "end": {"line": 4, "column": 78, "index": 129}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.setGestureState = void 0;\n  var _logger = require(_dependencyMap[0], \"../logger\");\n  var _PlatformChecker = require(_dependencyMap[1], \"../PlatformChecker\");\n  var setGestureState;\n  var _worklet_8681939311903_init_data = {\n    code: \"function setGestureStateNative_setGestureStateTs1(handlerTag,newState){const{logger}=this.__closure;if(!_WORKLET){logger.warn('You can not use setGestureState in non-worklet function.');return;}global._setGestureState(handlerTag,newState);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\platformFunctions\\\\setGestureState.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setGestureStateNative_setGestureStateTs1\\\",\\\"handlerTag\\\",\\\"newState\\\",\\\"logger\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"warn\\\",\\\"global\\\",\\\"_setGestureState\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/platformFunctions/setGestureState.ts\\\"],\\\"mappings\\\":\\\"AASA,SAAAA,wCAAmDA,CAAAC,UAAkB,CAAAC,QAAA,QAAAC,MAAA,OAAAC,SAAA,CAEnE,GAAI,CAACC,QAAQ,CAAE,CACbF,MAAM,CAACG,IAAI,CAAC,0DAA0D,CAAC,CACvE,OACF,CACAC,MAAM,CAACC,gBAAgB,CAACP,UAAU,CAAEC,QAAQ,CAAC,CAC/C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var setGestureStateNative = function () {\n    var _e = [new global.Error(), -2, -27];\n    var setGestureStateNative = function (handlerTag, newState) {\n      if (!_WORKLET) {\n        _logger.logger.warn('You can not use setGestureState in non-worklet function.');\n        return;\n      }\n      global._setGestureState(handlerTag, newState);\n    };\n    setGestureStateNative.__closure = {\n      logger: _logger.logger\n    };\n    setGestureStateNative.__workletHash = 8681939311903;\n    setGestureStateNative.__initData = _worklet_8681939311903_init_data;\n    setGestureStateNative.__stackDetails = _e;\n    return setGestureStateNative;\n  }();\n  function setGestureStateJest() {\n    _logger.logger.warn('setGestureState() cannot be used with Jest.');\n  }\n  function setGestureStateChromeDebugger() {\n    _logger.logger.warn('setGestureState() cannot be used with Chrome Debugger.');\n  }\n  function setGestureStateDefault() {\n    _logger.logger.warn('setGestureState() is not supported on this configuration.');\n  }\n  if (!(0, _PlatformChecker.shouldBeUseWeb)()) {\n    exports.setGestureState = setGestureState = setGestureStateNative;\n  } else if ((0, _PlatformChecker.isJest)()) {\n    exports.setGestureState = setGestureState = setGestureStateJest;\n  } else if ((0, _PlatformChecker.isChromeDebugger)()) {\n    exports.setGestureState = setGestureState = setGestureStateChromeDebugger;\n  } else {\n    exports.setGestureState = setGestureState = setGestureStateDefault;\n  }\n});", "lineCount": 52, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "setGestureState"], [7, 25, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_logger"], [8, 13, 3, 0], [8, 16, 3, 0, "require"], [8, 23, 3, 0], [8, 24, 3, 0, "_dependencyMap"], [8, 38, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_PlatformChecker"], [9, 22, 4, 0], [9, 25, 4, 0, "require"], [9, 32, 4, 0], [9, 33, 4, 0, "_dependencyMap"], [9, 47, 4, 0], [10, 2, 8, 7], [10, 6, 8, 11, "setGestureState"], [10, 21, 8, 43], [11, 2, 8, 44], [11, 6, 8, 44, "_worklet_8681939311903_init_data"], [11, 38, 8, 44], [12, 4, 8, 44, "code"], [12, 8, 8, 44], [13, 4, 8, 44, "location"], [13, 12, 8, 44], [14, 4, 8, 44, "sourceMap"], [14, 13, 8, 44], [15, 4, 8, 44, "version"], [15, 11, 8, 44], [16, 2, 8, 44], [17, 2, 8, 44], [17, 6, 8, 44, "setGestureStateNative"], [17, 27, 8, 44], [17, 30, 10, 0], [18, 4, 10, 0], [18, 8, 10, 0, "_e"], [18, 10, 10, 0], [18, 18, 10, 0, "global"], [18, 24, 10, 0], [18, 25, 10, 0, "Error"], [18, 30, 10, 0], [19, 4, 10, 0], [19, 8, 10, 0, "setGestureStateNative"], [19, 29, 10, 0], [19, 41, 10, 0, "setGestureStateNative"], [19, 42, 10, 31, "handlerTag"], [19, 52, 10, 49], [19, 54, 10, 51, "newState"], [19, 62, 10, 67], [19, 64, 10, 69], [20, 6, 12, 2], [20, 10, 12, 6], [20, 11, 12, 7, "_WORKLET"], [20, 19, 12, 15], [20, 21, 12, 17], [21, 8, 13, 4, "logger"], [21, 22, 13, 10], [21, 23, 13, 11, "warn"], [21, 27, 13, 15], [21, 28, 13, 16], [21, 86, 13, 74], [21, 87, 13, 75], [22, 8, 14, 4], [23, 6, 15, 2], [24, 6, 16, 2, "global"], [24, 12, 16, 8], [24, 13, 16, 9, "_setGestureState"], [24, 29, 16, 25], [24, 30, 16, 26, "handlerTag"], [24, 40, 16, 36], [24, 42, 16, 38, "newState"], [24, 50, 16, 46], [24, 51, 16, 47], [25, 4, 17, 0], [25, 5, 17, 1], [26, 4, 17, 1, "setGestureStateNative"], [26, 25, 17, 1], [26, 26, 17, 1, "__closure"], [26, 35, 17, 1], [27, 6, 17, 1, "logger"], [27, 12, 17, 1], [27, 14, 13, 4, "logger"], [28, 4, 13, 10], [29, 4, 13, 10, "setGestureStateNative"], [29, 25, 13, 10], [29, 26, 13, 10, "__workletHash"], [29, 39, 13, 10], [30, 4, 13, 10, "setGestureStateNative"], [30, 25, 13, 10], [30, 26, 13, 10, "__initData"], [30, 36, 13, 10], [30, 39, 13, 10, "_worklet_8681939311903_init_data"], [30, 71, 13, 10], [31, 4, 13, 10, "setGestureStateNative"], [31, 25, 13, 10], [31, 26, 13, 10, "__stackDetails"], [31, 40, 13, 10], [31, 43, 13, 10, "_e"], [31, 45, 13, 10], [32, 4, 13, 10], [32, 11, 13, 10, "setGestureStateNative"], [32, 32, 13, 10], [33, 2, 13, 10], [33, 3, 10, 0], [34, 2, 19, 0], [34, 11, 19, 9, "setGestureStateJest"], [34, 30, 19, 28, "setGestureStateJest"], [34, 31, 19, 28], [34, 33, 19, 31], [35, 4, 20, 2, "logger"], [35, 18, 20, 8], [35, 19, 20, 9, "warn"], [35, 23, 20, 13], [35, 24, 20, 14], [35, 69, 20, 59], [35, 70, 20, 60], [36, 2, 21, 0], [37, 2, 23, 0], [37, 11, 23, 9, "setGestureStateChromeDebugger"], [37, 40, 23, 38, "setGestureStateChromeDebugger"], [37, 41, 23, 38], [37, 43, 23, 41], [38, 4, 24, 2, "logger"], [38, 18, 24, 8], [38, 19, 24, 9, "warn"], [38, 23, 24, 13], [38, 24, 24, 14], [38, 80, 24, 70], [38, 81, 24, 71], [39, 2, 25, 0], [40, 2, 27, 0], [40, 11, 27, 9, "setGestureStateDefault"], [40, 33, 27, 31, "setGestureStateDefault"], [40, 34, 27, 31], [40, 36, 27, 34], [41, 4, 28, 2, "logger"], [41, 18, 28, 8], [41, 19, 28, 9, "warn"], [41, 23, 28, 13], [41, 24, 28, 14], [41, 83, 28, 73], [41, 84, 28, 74], [42, 2, 29, 0], [43, 2, 31, 0], [43, 6, 31, 4], [43, 7, 31, 5], [43, 11, 31, 5, "shouldBeUseWeb"], [43, 42, 31, 19], [43, 44, 31, 20], [43, 45, 31, 21], [43, 47, 31, 23], [44, 4, 32, 2, "exports"], [44, 11, 32, 2], [44, 12, 32, 2, "setGestureState"], [44, 27, 32, 2], [44, 30, 32, 2, "setGestureState"], [44, 45, 32, 17], [44, 48, 32, 20, "setGestureStateNative"], [44, 69, 32, 41], [45, 2, 33, 0], [45, 3, 33, 1], [45, 9, 33, 7], [45, 13, 33, 11], [45, 17, 33, 11, "isJest"], [45, 40, 33, 17], [45, 42, 33, 18], [45, 43, 33, 19], [45, 45, 33, 21], [46, 4, 34, 2, "exports"], [46, 11, 34, 2], [46, 12, 34, 2, "setGestureState"], [46, 27, 34, 2], [46, 30, 34, 2, "setGestureState"], [46, 45, 34, 17], [46, 48, 34, 20, "setGestureStateJest"], [46, 67, 34, 39], [47, 2, 35, 0], [47, 3, 35, 1], [47, 9, 35, 7], [47, 13, 35, 11], [47, 17, 35, 11, "isChromeDebugger"], [47, 50, 35, 27], [47, 52, 35, 28], [47, 53, 35, 29], [47, 55, 35, 31], [48, 4, 36, 2, "exports"], [48, 11, 36, 2], [48, 12, 36, 2, "setGestureState"], [48, 27, 36, 2], [48, 30, 36, 2, "setGestureState"], [48, 45, 36, 17], [48, 48, 36, 20, "setGestureStateChromeDebugger"], [48, 77, 36, 49], [49, 2, 37, 0], [49, 3, 37, 1], [49, 9, 37, 7], [50, 4, 38, 2, "exports"], [50, 11, 38, 2], [50, 12, 38, 2, "setGestureState"], [50, 27, 38, 2], [50, 30, 38, 2, "setGestureState"], [50, 45, 38, 17], [50, 48, 38, 20, "setGestureStateDefault"], [50, 70, 38, 42], [51, 2, 39, 0], [52, 0, 39, 1], [52, 3]], "functionMap": {"names": ["<global>", "setGestureStateNative", "setGestureStateJest", "setGestureStateChromeDebugger", "setGestureStateDefault"], "mappings": "AAA;ACS;CDO;AEE;CFE;AGE;CHE;AIE;CJE"}}, "type": "js/module"}]}