{"dependencies": [{"name": "../Utilities/PolyfillFunctions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 25}, "end": {"line": 13, "column": 66}}], "key": "HYclPKQCLeyfRj4pG+IgrzgyEZ8=", "exportNames": ["*"]}}, {"name": "../../src/private/webapis/microtasks/specs/NativeMicrotasks", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 76}}], "key": "I00kQUS2rmYjjzUykcEdCMhJxXM=", "exportNames": ["*"]}}, {"name": "./Timers/immediateShim", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 40, "column": 10}, "end": {"line": 40, "column": 43}}, {"start": {"line": 44, "column": 10}, "end": {"line": 44, "column": 43}}], "key": "Fjnk+V2fuSsXPECJrGpZ2hTjI1s=", "exportNames": ["*"]}}, {"name": "../../src/private/webapis/idlecallbacks/specs/NativeIdleCallbacks", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 82}}, {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 82}}], "key": "vfnFdIg+LR1qIfPNPqpoFOL0lEI=", "exportNames": ["*"]}}, {"name": "./Timers/JSTimers", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 76, "column": 31}, "end": {"line": 76, "column": 59}}, {"start": {"line": 98, "column": 10}, "end": {"line": 98, "column": 38}}, {"start": {"line": 102, "column": 10}, "end": {"line": 102, "column": 38}}], "key": "9ATwFhjB/ZZoZoAnTZ/jDe/5LMU=", "exportNames": ["*"]}}, {"name": "./Timers/queueMicrotask.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 90, "column": 10}, "end": {"line": 90, "column": 47}}], "key": "PbauGy8hrTkorkANPJQdDZMpWQM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _require = require(_dependencyMap[0], \"../Utilities/PolyfillFunctions\"),\n    polyfillGlobal = _require.polyfillGlobal;\n  if (__DEV__) {\n    if (typeof global.Promise !== 'function') {\n      console.error('Promise should exist before setting up timers.');\n    }\n  }\n  if (global.RN$Bridgeless === true) {\n    global.RN$enableMicrotasksInReact = true;\n    polyfillGlobal('queueMicrotask', () => require(_dependencyMap[1], \"../../src/private/webapis/microtasks/specs/NativeMicrotasks\").default.queueMicrotask);\n    polyfillGlobal('setImmediate', () => require(_dependencyMap[2], \"./Timers/immediateShim\").setImmediate);\n    polyfillGlobal('clearImmediate', () => require(_dependencyMap[2], \"./Timers/immediateShim\").clearImmediate);\n    polyfillGlobal('requestIdleCallback', () => require(_dependencyMap[3], \"../../src/private/webapis/idlecallbacks/specs/NativeIdleCallbacks\").default.requestIdleCallback);\n    polyfillGlobal('cancelIdleCallback', () => require(_dependencyMap[3], \"../../src/private/webapis/idlecallbacks/specs/NativeIdleCallbacks\").default.cancelIdleCallback);\n  } else {\n    var defineLazyTimer = name => {\n      polyfillGlobal(name, () => require(_dependencyMap[4], \"./Timers/JSTimers\").default[name]);\n    };\n    defineLazyTimer('setTimeout');\n    defineLazyTimer('clearTimeout');\n    defineLazyTimer('setInterval');\n    defineLazyTimer('clearInterval');\n    defineLazyTimer('requestAnimationFrame');\n    defineLazyTimer('cancelAnimationFrame');\n    defineLazyTimer('requestIdleCallback');\n    defineLazyTimer('cancelIdleCallback');\n    polyfillGlobal('queueMicrotask', () => require(_dependencyMap[5], \"./Timers/queueMicrotask.js\").default);\n    polyfillGlobal('setImmediate', () => require(_dependencyMap[4], \"./Timers/JSTimers\").default.queueReactNativeMicrotask);\n    polyfillGlobal('clearImmediate', () => require(_dependencyMap[4], \"./Timers/JSTimers\").default.clearReactNativeMicrotask);\n  }\n});", "lineCount": 34, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 13, 0], [4, 6, 13, 0, "_require"], [4, 14, 13, 0], [4, 17, 13, 25, "require"], [4, 24, 13, 32], [4, 25, 13, 32, "_dependencyMap"], [4, 39, 13, 32], [4, 76, 13, 65], [4, 77, 13, 66], [5, 4, 13, 7, "polyfillGlobal"], [5, 18, 13, 21], [5, 21, 13, 21, "_require"], [5, 29, 13, 21], [5, 30, 13, 7, "polyfillGlobal"], [5, 44, 13, 21], [6, 2, 15, 0], [6, 6, 15, 4, "__DEV__"], [6, 13, 15, 11], [6, 15, 15, 13], [7, 4, 16, 2], [7, 8, 16, 6], [7, 15, 16, 13, "global"], [7, 21, 16, 19], [7, 22, 16, 20, "Promise"], [7, 29, 16, 27], [7, 34, 16, 32], [7, 44, 16, 42], [7, 46, 16, 44], [8, 6, 17, 4, "console"], [8, 13, 17, 11], [8, 14, 17, 12, "error"], [8, 19, 17, 17], [8, 20, 17, 18], [8, 68, 17, 66], [8, 69, 17, 67], [9, 4, 18, 2], [10, 2, 19, 0], [11, 2, 22, 0], [11, 6, 22, 4, "global"], [11, 12, 22, 10], [11, 13, 22, 11, "RN$Bridgeless"], [11, 26, 22, 24], [11, 31, 22, 29], [11, 35, 22, 33], [11, 37, 22, 35], [12, 4, 27, 2, "global"], [12, 10, 27, 8], [12, 11, 27, 9, "RN$enableMicrotasksInReact"], [12, 37, 27, 35], [12, 40, 27, 38], [12, 44, 27, 42], [13, 4, 29, 2, "polyfillGlobal"], [13, 18, 29, 16], [13, 19, 30, 4], [13, 35, 30, 20], [13, 37, 31, 4], [13, 43, 32, 6, "require"], [13, 50, 32, 13], [13, 51, 32, 13, "_dependencyMap"], [13, 65, 32, 13], [13, 131, 32, 75], [13, 132, 32, 76], [13, 133, 33, 9, "default"], [13, 140, 33, 16], [13, 141, 33, 17, "queueMicrotask"], [13, 155, 34, 2], [13, 156, 34, 3], [14, 4, 38, 2, "polyfillGlobal"], [14, 18, 38, 16], [14, 19, 39, 4], [14, 33, 39, 18], [14, 35, 40, 4], [14, 41, 40, 10, "require"], [14, 48, 40, 17], [14, 49, 40, 17, "_dependencyMap"], [14, 63, 40, 17], [14, 92, 40, 42], [14, 93, 40, 43], [14, 94, 40, 44, "setImmediate"], [14, 106, 41, 2], [14, 107, 41, 3], [15, 4, 42, 2, "polyfillGlobal"], [15, 18, 42, 16], [15, 19, 43, 4], [15, 35, 43, 20], [15, 37, 44, 4], [15, 43, 44, 10, "require"], [15, 50, 44, 17], [15, 51, 44, 17, "_dependencyMap"], [15, 65, 44, 17], [15, 94, 44, 42], [15, 95, 44, 43], [15, 96, 44, 44, "clearImmediate"], [15, 110, 45, 2], [15, 111, 45, 3], [16, 4, 47, 2, "polyfillGlobal"], [16, 18, 47, 16], [16, 19, 48, 4], [16, 40, 48, 25], [16, 42, 49, 4], [16, 48, 50, 6, "require"], [16, 55, 50, 13], [16, 56, 50, 13, "_dependencyMap"], [16, 70, 50, 13], [16, 142, 50, 81], [16, 143, 50, 82], [16, 144, 51, 9, "default"], [16, 151, 51, 16], [16, 152, 51, 17, "requestIdleCallback"], [16, 171, 52, 2], [16, 172, 52, 3], [17, 4, 54, 2, "polyfillGlobal"], [17, 18, 54, 16], [17, 19, 55, 4], [17, 39, 55, 24], [17, 41, 56, 4], [17, 47, 57, 6, "require"], [17, 54, 57, 13], [17, 55, 57, 13, "_dependencyMap"], [17, 69, 57, 13], [17, 141, 57, 81], [17, 142, 57, 82], [17, 143, 58, 9, "default"], [17, 150, 58, 16], [17, 151, 58, 17, "cancelIdleCallback"], [17, 169, 59, 2], [17, 170, 59, 3], [18, 2, 60, 0], [18, 3, 60, 1], [18, 9, 60, 7], [19, 4, 65, 2], [19, 8, 65, 8, "defineLazyTimer"], [19, 23, 65, 23], [19, 26, 66, 4, "name"], [19, 30, 74, 20], [19, 34, 75, 7], [20, 6, 76, 4, "polyfillGlobal"], [20, 20, 76, 18], [20, 21, 76, 19, "name"], [20, 25, 76, 23], [20, 27, 76, 25], [20, 33, 76, 31, "require"], [20, 40, 76, 38], [20, 41, 76, 38, "_dependencyMap"], [20, 55, 76, 38], [20, 79, 76, 58], [20, 80, 76, 59], [20, 81, 76, 60, "default"], [20, 88, 76, 67], [20, 89, 76, 68, "name"], [20, 93, 76, 72], [20, 94, 76, 73], [20, 95, 76, 74], [21, 4, 77, 2], [21, 5, 77, 3], [22, 4, 78, 2, "defineLazyTimer"], [22, 19, 78, 17], [22, 20, 78, 18], [22, 32, 78, 30], [22, 33, 78, 31], [23, 4, 79, 2, "defineLazyTimer"], [23, 19, 79, 17], [23, 20, 79, 18], [23, 34, 79, 32], [23, 35, 79, 33], [24, 4, 80, 2, "defineLazyTimer"], [24, 19, 80, 17], [24, 20, 80, 18], [24, 33, 80, 31], [24, 34, 80, 32], [25, 4, 81, 2, "defineLazyTimer"], [25, 19, 81, 17], [25, 20, 81, 18], [25, 35, 81, 33], [25, 36, 81, 34], [26, 4, 82, 2, "defineLazyTimer"], [26, 19, 82, 17], [26, 20, 82, 18], [26, 43, 82, 41], [26, 44, 82, 42], [27, 4, 83, 2, "defineLazyTimer"], [27, 19, 83, 17], [27, 20, 83, 18], [27, 42, 83, 40], [27, 43, 83, 41], [28, 4, 84, 2, "defineLazyTimer"], [28, 19, 84, 17], [28, 20, 84, 18], [28, 41, 84, 39], [28, 42, 84, 40], [29, 4, 85, 2, "defineLazyTimer"], [29, 19, 85, 17], [29, 20, 85, 18], [29, 40, 85, 38], [29, 41, 85, 39], [30, 4, 88, 2, "polyfillGlobal"], [30, 18, 88, 16], [30, 19, 89, 4], [30, 35, 89, 20], [30, 37, 90, 4], [30, 43, 90, 10, "require"], [30, 50, 90, 17], [30, 51, 90, 17, "_dependencyMap"], [30, 65, 90, 17], [30, 98, 90, 46], [30, 99, 90, 47], [30, 100, 90, 48, "default"], [30, 107, 91, 2], [30, 108, 91, 3], [31, 4, 96, 2, "polyfillGlobal"], [31, 18, 96, 16], [31, 19, 97, 4], [31, 33, 97, 18], [31, 35, 98, 4], [31, 41, 98, 10, "require"], [31, 48, 98, 17], [31, 49, 98, 17, "_dependencyMap"], [31, 63, 98, 17], [31, 87, 98, 37], [31, 88, 98, 38], [31, 89, 98, 39, "default"], [31, 96, 98, 46], [31, 97, 98, 47, "queueReactNativeMicrotask"], [31, 122, 99, 2], [31, 123, 99, 3], [32, 4, 100, 2, "polyfillGlobal"], [32, 18, 100, 16], [32, 19, 101, 4], [32, 35, 101, 20], [32, 37, 102, 4], [32, 43, 102, 10, "require"], [32, 50, 102, 17], [32, 51, 102, 17, "_dependencyMap"], [32, 65, 102, 17], [32, 89, 102, 37], [32, 90, 102, 38], [32, 91, 102, 39, "default"], [32, 98, 102, 46], [32, 99, 102, 47, "clearReactNativeMicrotask"], [32, 124, 103, 2], [32, 125, 103, 3], [33, 2, 104, 0], [34, 0, 104, 1], [34, 3]], "functionMap": {"names": ["<global>", "polyfillGlobal$argument_1", "defineLazyTimer"], "mappings": "AAA;IC8B;+BDE;ICO,oDD;ICI,sDD;ICK;oCDE;ICK;mCDE;0BEO;yBDW,gDC;GFC;ICa,mDD;ICQ,oED;ICI,oED"}}, "type": "js/module"}]}