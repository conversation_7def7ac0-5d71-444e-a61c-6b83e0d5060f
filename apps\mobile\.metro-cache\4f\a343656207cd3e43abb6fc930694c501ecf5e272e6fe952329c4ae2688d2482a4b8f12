{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./NativeReanimatedModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 61, "index": 76}}], "key": "3yR7U6hxYMYNXMBajXu8pjkLa9w=", "exportNames": ["*"]}}, {"name": "./NativeWorkletsModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 77}, "end": {"line": 4, "column": 57, "index": 134}}], "key": "o9NHEAo+bY62k9K6CA6rRTT/a6w=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"ReanimatedTurboModule\", {\n    enumerable: true,\n    get: function () {\n      return _NativeReanimatedModule.default;\n    }\n  });\n  Object.defineProperty(exports, \"WorkletsTurboModule\", {\n    enumerable: true,\n    get: function () {\n      return _NativeWorkletsModule.default;\n    }\n  });\n  var _NativeReanimatedModule = _interopRequireDefault(require(_dependencyMap[1], \"./NativeReanimatedModule\"));\n  var _NativeWorkletsModule = _interopRequireDefault(require(_dependencyMap[2], \"./NativeWorkletsModule\"));\n});", "lineCount": 22, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "Object"], [8, 8, 1, 13], [8, 9, 1, 13, "defineProperty"], [8, 23, 1, 13], [8, 24, 1, 13, "exports"], [8, 31, 1, 13], [9, 4, 1, 13, "enumerable"], [9, 14, 1, 13], [10, 4, 1, 13, "get"], [10, 7, 1, 13], [10, 18, 1, 13, "get"], [10, 19, 1, 13], [11, 6, 1, 13], [11, 13, 1, 13, "_NativeReanimatedModule"], [11, 36, 1, 13], [11, 37, 1, 13, "default"], [11, 44, 1, 13], [12, 4, 1, 13], [13, 2, 1, 13], [14, 2, 1, 13, "Object"], [14, 8, 1, 13], [14, 9, 1, 13, "defineProperty"], [14, 23, 1, 13], [14, 24, 1, 13, "exports"], [14, 31, 1, 13], [15, 4, 1, 13, "enumerable"], [15, 14, 1, 13], [16, 4, 1, 13, "get"], [16, 7, 1, 13], [16, 18, 1, 13, "get"], [16, 19, 1, 13], [17, 6, 1, 13], [17, 13, 1, 13, "_NativeWorkletsModule"], [17, 34, 1, 13], [17, 35, 1, 13, "default"], [17, 42, 1, 13], [18, 4, 1, 13], [19, 2, 1, 13], [20, 2, 3, 0], [20, 6, 3, 0, "_NativeReanimatedModule"], [20, 29, 3, 0], [20, 32, 3, 0, "_interopRequireDefault"], [20, 54, 3, 0], [20, 55, 3, 0, "require"], [20, 62, 3, 0], [20, 63, 3, 0, "_dependencyMap"], [20, 77, 3, 0], [21, 2, 4, 0], [21, 6, 4, 0, "_NativeWorkletsModule"], [21, 27, 4, 0], [21, 30, 4, 0, "_interopRequireDefault"], [21, 52, 4, 0], [21, 53, 4, 0, "require"], [21, 60, 4, 0], [21, 61, 4, 0, "_dependencyMap"], [21, 75, 4, 0], [22, 0, 4, 57], [22, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}