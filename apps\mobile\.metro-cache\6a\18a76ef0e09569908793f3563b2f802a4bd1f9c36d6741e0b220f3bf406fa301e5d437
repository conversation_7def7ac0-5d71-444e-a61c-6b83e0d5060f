{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 42, "index": 57}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./Text.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 58}, "end": {"line": 4, "column": 33, "index": 91}}], "key": "QTnFfg9+sbvsvptKfI6RYkeAj2s=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 92}, "end": {"line": 5, "column": 48, "index": 140}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.MissingIcon = MissingIcon;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _Text = require(_dependencyMap[1], \"./Text.js\");\n  var _jsxRuntime = require(_dependencyMap[2], \"react/jsx-runtime\");\n  function MissingIcon(_ref) {\n    var color = _ref.color,\n      size = _ref.size,\n      style = _ref.style;\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Text.Text, {\n      style: [styles.icon, {\n        color,\n        fontSize: size\n      }, style],\n      children: \"\\u23F7\"\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    icon: {\n      backgroundColor: 'transparent'\n    }\n  });\n});", "lineCount": 28, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "MissingIcon"], [7, 21, 1, 13], [7, 24, 1, 13, "MissingIcon"], [7, 35, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_reactNative"], [8, 18, 3, 0], [8, 21, 3, 0, "require"], [8, 28, 3, 0], [8, 29, 3, 0, "_dependencyMap"], [8, 43, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_Text"], [9, 11, 4, 0], [9, 14, 4, 0, "require"], [9, 21, 4, 0], [9, 22, 4, 0, "_dependencyMap"], [9, 36, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_jsxRuntime"], [10, 17, 5, 0], [10, 20, 5, 0, "require"], [10, 27, 5, 0], [10, 28, 5, 0, "_dependencyMap"], [10, 42, 5, 0], [11, 2, 6, 7], [11, 11, 6, 16, "MissingIcon"], [11, 22, 6, 27, "MissingIcon"], [11, 23, 6, 27, "_ref"], [11, 27, 6, 27], [11, 29, 10, 3], [12, 4, 10, 3], [12, 8, 7, 2, "color"], [12, 13, 7, 7], [12, 16, 7, 7, "_ref"], [12, 20, 7, 7], [12, 21, 7, 2, "color"], [12, 26, 7, 7], [13, 6, 8, 2, "size"], [13, 10, 8, 6], [13, 13, 8, 6, "_ref"], [13, 17, 8, 6], [13, 18, 8, 2, "size"], [13, 22, 8, 6], [14, 6, 9, 2, "style"], [14, 11, 9, 7], [14, 14, 9, 7, "_ref"], [14, 18, 9, 7], [14, 19, 9, 2, "style"], [14, 24, 9, 7], [15, 4, 11, 2], [15, 11, 11, 9], [15, 24, 11, 22], [15, 28, 11, 22, "_jsx"], [15, 43, 11, 26], [15, 45, 11, 27, "Text"], [15, 55, 11, 31], [15, 57, 11, 33], [16, 6, 12, 4, "style"], [16, 11, 12, 9], [16, 13, 12, 11], [16, 14, 12, 12, "styles"], [16, 20, 12, 18], [16, 21, 12, 19, "icon"], [16, 25, 12, 23], [16, 27, 12, 25], [17, 8, 13, 6, "color"], [17, 13, 13, 11], [18, 8, 14, 6, "fontSize"], [18, 16, 14, 14], [18, 18, 14, 16, "size"], [19, 6, 15, 4], [19, 7, 15, 5], [19, 9, 15, 7, "style"], [19, 14, 15, 12], [19, 15, 15, 13], [20, 6, 16, 4, "children"], [20, 14, 16, 12], [20, 16, 16, 14], [21, 4, 17, 2], [21, 5, 17, 3], [21, 6, 17, 4], [22, 2, 18, 0], [23, 2, 19, 0], [23, 6, 19, 6, "styles"], [23, 12, 19, 12], [23, 15, 19, 15, "StyleSheet"], [23, 38, 19, 25], [23, 39, 19, 26, "create"], [23, 45, 19, 32], [23, 46, 19, 33], [24, 4, 20, 2, "icon"], [24, 8, 20, 6], [24, 10, 20, 8], [25, 6, 21, 4, "backgroundColor"], [25, 21, 21, 19], [25, 23, 21, 21], [26, 4, 22, 2], [27, 2, 23, 0], [27, 3, 23, 1], [27, 4, 23, 2], [28, 0, 23, 3], [28, 3]], "functionMap": {"names": ["<global>", "MissingIcon"], "mappings": "AAA;OCK;CDY"}}, "type": "js/module"}]}