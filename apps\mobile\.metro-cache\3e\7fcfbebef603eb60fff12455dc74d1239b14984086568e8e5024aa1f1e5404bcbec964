{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LongPressGesture = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _gesture = require(_dependencyMap[6], \"./gesture\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var LongPressGesture = exports.LongPressGesture = /*#__PURE__*/function (_BaseGesture) {\n    function LongPressGesture() {\n      var _this;\n      (0, _classCallCheck2.default)(this, LongPressGesture);\n      _this = _callSuper(this, LongPressGesture);\n      _this.config = {};\n      _this.handlerName = 'LongPressGestureHandler';\n      _this.shouldCancelWhenOutside(true);\n      return _this;\n    }\n\n    /**\n     * Minimum time, expressed in milliseconds, that a finger must remain pressed on the corresponding view.\n     * The default value is 500.\n     * @param duration\n     */\n    (0, _inherits2.default)(LongPressGesture, _BaseGesture);\n    return (0, _createClass2.default)(LongPressGesture, [{\n      key: \"minDuration\",\n      value: function minDuration(duration) {\n        this.config.minDurationMs = duration;\n        return this;\n      }\n\n      /**\n       * Maximum distance, expressed in points, that defines how far the finger is allowed to travel during a long press gesture.\n       * @param distance\n       * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/long-press-gesture#maxdistancevalue-number\n       */\n    }, {\n      key: \"maxDistance\",\n      value: function maxDistance(distance) {\n        this.config.maxDist = distance;\n        return this;\n      }\n\n      /**\n       * Determine exact number of points required to handle the long press gesture.\n       * @param pointers\n       */\n    }, {\n      key: \"numberOfPointers\",\n      value: function numberOfPointers(pointers) {\n        this.config.numberOfPointers = pointers;\n        return this;\n      }\n    }]);\n  }(_gesture.BaseGesture);\n});", "lineCount": 63, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_gesture"], [12, 14, 1, 0], [12, 17, 1, 0, "require"], [12, 24, 1, 0], [12, 25, 1, 0, "_dependencyMap"], [12, 39, 1, 0], [13, 2, 1, 59], [13, 11, 1, 59, "_callSuper"], [13, 22, 1, 59, "t"], [13, 23, 1, 59], [13, 25, 1, 59, "o"], [13, 26, 1, 59], [13, 28, 1, 59, "e"], [13, 29, 1, 59], [13, 40, 1, 59, "o"], [13, 41, 1, 59], [13, 48, 1, 59, "_getPrototypeOf2"], [13, 64, 1, 59], [13, 65, 1, 59, "default"], [13, 72, 1, 59], [13, 74, 1, 59, "o"], [13, 75, 1, 59], [13, 82, 1, 59, "_possibleConstructorReturn2"], [13, 109, 1, 59], [13, 110, 1, 59, "default"], [13, 117, 1, 59], [13, 119, 1, 59, "t"], [13, 120, 1, 59], [13, 122, 1, 59, "_isNativeReflectConstruct"], [13, 147, 1, 59], [13, 152, 1, 59, "Reflect"], [13, 159, 1, 59], [13, 160, 1, 59, "construct"], [13, 169, 1, 59], [13, 170, 1, 59, "o"], [13, 171, 1, 59], [13, 173, 1, 59, "e"], [13, 174, 1, 59], [13, 186, 1, 59, "_getPrototypeOf2"], [13, 202, 1, 59], [13, 203, 1, 59, "default"], [13, 210, 1, 59], [13, 212, 1, 59, "t"], [13, 213, 1, 59], [13, 215, 1, 59, "constructor"], [13, 226, 1, 59], [13, 230, 1, 59, "o"], [13, 231, 1, 59], [13, 232, 1, 59, "apply"], [13, 237, 1, 59], [13, 238, 1, 59, "t"], [13, 239, 1, 59], [13, 241, 1, 59, "e"], [13, 242, 1, 59], [14, 2, 1, 59], [14, 11, 1, 59, "_isNativeReflectConstruct"], [14, 37, 1, 59], [14, 51, 1, 59, "t"], [14, 52, 1, 59], [14, 56, 1, 59, "Boolean"], [14, 63, 1, 59], [14, 64, 1, 59, "prototype"], [14, 73, 1, 59], [14, 74, 1, 59, "valueOf"], [14, 81, 1, 59], [14, 82, 1, 59, "call"], [14, 86, 1, 59], [14, 87, 1, 59, "Reflect"], [14, 94, 1, 59], [14, 95, 1, 59, "construct"], [14, 104, 1, 59], [14, 105, 1, 59, "Boolean"], [14, 112, 1, 59], [14, 145, 1, 59, "t"], [14, 146, 1, 59], [14, 159, 1, 59, "_isNativeReflectConstruct"], [14, 184, 1, 59], [14, 196, 1, 59, "_isNativeReflectConstruct"], [14, 197, 1, 59], [14, 210, 1, 59, "t"], [14, 211, 1, 59], [15, 2, 1, 59], [15, 6, 5, 13, "LongPressGesture"], [15, 22, 5, 29], [15, 25, 5, 29, "exports"], [15, 32, 5, 29], [15, 33, 5, 29, "LongPressGesture"], [15, 49, 5, 29], [15, 75, 5, 29, "_BaseGesture"], [15, 87, 5, 29], [16, 4, 8, 2], [16, 13, 8, 2, "LongPressGesture"], [16, 30, 8, 2], [16, 32, 8, 16], [17, 6, 8, 16], [17, 10, 8, 16, "_this"], [17, 15, 8, 16], [18, 6, 8, 16], [18, 10, 8, 16, "_classCallCheck2"], [18, 26, 8, 16], [18, 27, 8, 16, "default"], [18, 34, 8, 16], [18, 42, 8, 16, "LongPressGesture"], [18, 58, 8, 16], [19, 6, 9, 4, "_this"], [19, 11, 9, 4], [19, 14, 9, 4, "_callSuper"], [19, 24, 9, 4], [19, 31, 9, 4, "LongPressGesture"], [19, 47, 9, 4], [20, 6, 9, 12, "_this"], [20, 11, 9, 12], [20, 12, 6, 9, "config"], [20, 18, 6, 15], [20, 21, 6, 62], [20, 22, 6, 63], [20, 23, 6, 64], [21, 6, 11, 4, "_this"], [21, 11, 11, 4], [21, 12, 11, 9, "handler<PERSON>ame"], [21, 23, 11, 20], [21, 26, 11, 23], [21, 51, 11, 48], [22, 6, 12, 4, "_this"], [22, 11, 12, 4], [22, 12, 12, 9, "shouldCancelWhenOutside"], [22, 35, 12, 32], [22, 36, 12, 33], [22, 40, 12, 37], [22, 41, 12, 38], [23, 6, 12, 39], [23, 13, 12, 39, "_this"], [23, 18, 12, 39], [24, 4, 13, 2], [26, 4, 15, 2], [27, 0, 16, 0], [28, 0, 17, 0], [29, 0, 18, 0], [30, 0, 19, 0], [31, 4, 15, 2], [31, 8, 15, 2, "_inherits2"], [31, 18, 15, 2], [31, 19, 15, 2, "default"], [31, 26, 15, 2], [31, 28, 15, 2, "LongPressGesture"], [31, 44, 15, 2], [31, 46, 15, 2, "_BaseGesture"], [31, 58, 15, 2], [32, 4, 15, 2], [32, 15, 15, 2, "_createClass2"], [32, 28, 15, 2], [32, 29, 15, 2, "default"], [32, 36, 15, 2], [32, 38, 15, 2, "LongPressGesture"], [32, 54, 15, 2], [33, 6, 15, 2, "key"], [33, 9, 15, 2], [34, 6, 15, 2, "value"], [34, 11, 15, 2], [34, 13, 20, 2], [34, 22, 20, 2, "minDuration"], [34, 33, 20, 13, "minDuration"], [34, 34, 20, 14, "duration"], [34, 42, 20, 30], [34, 44, 20, 32], [35, 8, 21, 4], [35, 12, 21, 8], [35, 13, 21, 9, "config"], [35, 19, 21, 15], [35, 20, 21, 16, "minDurationMs"], [35, 33, 21, 29], [35, 36, 21, 32, "duration"], [35, 44, 21, 40], [36, 8, 22, 4], [36, 15, 22, 11], [36, 19, 22, 15], [37, 6, 23, 2], [39, 6, 25, 2], [40, 0, 26, 0], [41, 0, 27, 0], [42, 0, 28, 0], [43, 0, 29, 0], [44, 4, 25, 2], [45, 6, 25, 2, "key"], [45, 9, 25, 2], [46, 6, 25, 2, "value"], [46, 11, 25, 2], [46, 13, 30, 2], [46, 22, 30, 2, "maxDistance"], [46, 33, 30, 13, "maxDistance"], [46, 34, 30, 14, "distance"], [46, 42, 30, 30], [46, 44, 30, 32], [47, 8, 31, 4], [47, 12, 31, 8], [47, 13, 31, 9, "config"], [47, 19, 31, 15], [47, 20, 31, 16, "maxDist"], [47, 27, 31, 23], [47, 30, 31, 26, "distance"], [47, 38, 31, 34], [48, 8, 32, 4], [48, 15, 32, 11], [48, 19, 32, 15], [49, 6, 33, 2], [51, 6, 35, 2], [52, 0, 36, 0], [53, 0, 37, 0], [54, 0, 38, 0], [55, 4, 35, 2], [56, 6, 35, 2, "key"], [56, 9, 35, 2], [57, 6, 35, 2, "value"], [57, 11, 35, 2], [57, 13, 39, 2], [57, 22, 39, 2, "numberOfPointers"], [57, 38, 39, 18, "numberOfPointers"], [57, 39, 39, 19, "pointers"], [57, 47, 39, 35], [57, 49, 39, 37], [58, 8, 40, 4], [58, 12, 40, 8], [58, 13, 40, 9, "config"], [58, 19, 40, 15], [58, 20, 40, 16, "numberOfPointers"], [58, 36, 40, 32], [58, 39, 40, 35, "pointers"], [58, 47, 40, 43], [59, 8, 41, 4], [59, 15, 41, 11], [59, 19, 41, 15], [60, 6, 42, 2], [61, 4, 42, 3], [62, 2, 42, 3], [62, 4, 5, 38, "BaseGesture"], [62, 24, 5, 49], [63, 0, 5, 49], [63, 3]], "functionMap": {"names": ["<global>", "LongPressGesture", "LongPressGesture#constructor", "LongPressGesture#minDuration", "LongPressGesture#maxDistance", "LongPressGesture#numberOfPointers"], "mappings": "AAA;OCI;ECG;GDK;EEO;GFG;EGO;GHG;EIM;GJG;CDC"}}, "type": "js/module"}]}