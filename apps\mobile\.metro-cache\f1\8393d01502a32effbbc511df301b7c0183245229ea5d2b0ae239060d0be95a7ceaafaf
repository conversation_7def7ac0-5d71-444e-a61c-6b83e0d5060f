{"dependencies": [{"name": "../animationParser", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 71, "index": 85}}], "key": "NS2upIa4aHN1XdKmQKcusYkE9o0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LightSpeedOutData = exports.LightSpeedOut = exports.LightSpeedInData = exports.LightSpeedIn = void 0;\n  var _animationParser = require(_dependencyMap[0], \"../animationParser\");\n  var DEFAULT_LIGHTSPEED_TIME = 0.3;\n  var LightSpeedInData = exports.LightSpeedInData = {\n    LightSpeedInRight: {\n      name: 'LightSpeedInRight',\n      style: {\n        0: {\n          transform: [{\n            translateX: '100vw',\n            skewX: '-45deg'\n          }],\n          opacity: 0\n        },\n        70: {\n          transform: [{\n            skewX: '10deg'\n          }]\n        },\n        85: {\n          transform: [{\n            skewX: '-5deg'\n          }]\n        },\n        100: {\n          transform: [{\n            skewX: '0deg'\n          }]\n        }\n      },\n      duration: DEFAULT_LIGHTSPEED_TIME\n    },\n    LightSpeedInLeft: {\n      name: 'LightSpeedInLeft',\n      style: {\n        0: {\n          transform: [{\n            translateX: '-100vw',\n            skewX: '45deg'\n          }],\n          opacity: 0\n        },\n        70: {\n          transform: [{\n            skewX: '-10deg'\n          }]\n        },\n        85: {\n          transform: [{\n            skewX: '5deg'\n          }]\n        },\n        100: {\n          transform: [{\n            skewX: '0deg'\n          }]\n        }\n      },\n      duration: DEFAULT_LIGHTSPEED_TIME\n    }\n  };\n  var LightSpeedOutData = exports.LightSpeedOutData = {\n    LightSpeedOutRight: {\n      name: 'LightSpeedOutRight',\n      style: {\n        0: {\n          transform: [{\n            translateX: '0vw',\n            skewX: '0deg'\n          }],\n          opacity: 1\n        },\n        100: {\n          transform: [{\n            translateX: '100vw',\n            skewX: '-45deg'\n          }],\n          opacity: 0\n        }\n      },\n      duration: DEFAULT_LIGHTSPEED_TIME\n    },\n    LightSpeedOutLeft: {\n      name: 'LightSpeedOutLeft',\n      style: {\n        0: {\n          transform: [{\n            translateX: '0vw',\n            skew: '0deg'\n          }],\n          opacity: 1\n        },\n        100: {\n          transform: [{\n            translateX: '-100vw',\n            skew: '45deg'\n          }],\n          opacity: 0\n        }\n      },\n      duration: DEFAULT_LIGHTSPEED_TIME\n    }\n  };\n  var LightSpeedIn = exports.LightSpeedIn = {\n    LightSpeedInRight: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedInData.LightSpeedInRight),\n      duration: LightSpeedInData.LightSpeedInRight.duration\n    },\n    LightSpeedInLeft: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedInData.LightSpeedInLeft),\n      duration: LightSpeedInData.LightSpeedInLeft.duration\n    }\n  };\n  var LightSpeedOut = exports.LightSpeedOut = {\n    LightSpeedOutRight: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedOutData.LightSpeedOutRight),\n      duration: LightSpeedOutData.LightSpeedOutRight.duration\n    },\n    LightSpeedOutLeft: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedOutData.LightSpeedOutLeft),\n      duration: LightSpeedOutData.LightSpeedOutLeft.duration\n    }\n  };\n});", "lineCount": 130, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "LightSpeedOutData"], [7, 27, 1, 13], [7, 30, 1, 13, "exports"], [7, 37, 1, 13], [7, 38, 1, 13, "LightSpeedOut"], [7, 51, 1, 13], [7, 54, 1, 13, "exports"], [7, 61, 1, 13], [7, 62, 1, 13, "LightSpeedInData"], [7, 78, 1, 13], [7, 81, 1, 13, "exports"], [7, 88, 1, 13], [7, 89, 1, 13, "LightSpeedIn"], [7, 101, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_animation<PERSON><PERSON>er"], [8, 22, 2, 0], [8, 25, 2, 0, "require"], [8, 32, 2, 0], [8, 33, 2, 0, "_dependencyMap"], [8, 47, 2, 0], [9, 2, 4, 0], [9, 6, 4, 6, "DEFAULT_LIGHTSPEED_TIME"], [9, 29, 4, 29], [9, 32, 4, 32], [9, 35, 4, 35], [10, 2, 6, 7], [10, 6, 6, 13, "LightSpeedInData"], [10, 22, 6, 29], [10, 25, 6, 29, "exports"], [10, 32, 6, 29], [10, 33, 6, 29, "LightSpeedInData"], [10, 49, 6, 29], [10, 52, 6, 32], [11, 4, 7, 2, "LightSpeedInRight"], [11, 21, 7, 19], [11, 23, 7, 21], [12, 6, 8, 4, "name"], [12, 10, 8, 8], [12, 12, 8, 10], [12, 31, 8, 29], [13, 6, 9, 4, "style"], [13, 11, 9, 9], [13, 13, 9, 11], [14, 8, 10, 6], [14, 9, 10, 7], [14, 11, 10, 9], [15, 10, 11, 8, "transform"], [15, 19, 11, 17], [15, 21, 11, 19], [15, 22, 11, 20], [16, 12, 11, 22, "translateX"], [16, 22, 11, 32], [16, 24, 11, 34], [16, 31, 11, 41], [17, 12, 11, 43, "skewX"], [17, 17, 11, 48], [17, 19, 11, 50], [18, 10, 11, 59], [18, 11, 11, 60], [18, 12, 11, 61], [19, 10, 12, 8, "opacity"], [19, 17, 12, 15], [19, 19, 12, 17], [20, 8, 13, 6], [20, 9, 13, 7], [21, 8, 14, 6], [21, 10, 14, 8], [21, 12, 14, 10], [22, 10, 14, 12, "transform"], [22, 19, 14, 21], [22, 21, 14, 23], [22, 22, 14, 24], [23, 12, 14, 26, "skewX"], [23, 17, 14, 31], [23, 19, 14, 33], [24, 10, 14, 41], [24, 11, 14, 42], [25, 8, 14, 44], [25, 9, 14, 45], [26, 8, 15, 6], [26, 10, 15, 8], [26, 12, 15, 10], [27, 10, 15, 12, "transform"], [27, 19, 15, 21], [27, 21, 15, 23], [27, 22, 15, 24], [28, 12, 15, 26, "skewX"], [28, 17, 15, 31], [28, 19, 15, 33], [29, 10, 15, 41], [29, 11, 15, 42], [30, 8, 15, 44], [30, 9, 15, 45], [31, 8, 16, 6], [31, 11, 16, 9], [31, 13, 16, 11], [32, 10, 16, 13, "transform"], [32, 19, 16, 22], [32, 21, 16, 24], [32, 22, 16, 25], [33, 12, 16, 27, "skewX"], [33, 17, 16, 32], [33, 19, 16, 34], [34, 10, 16, 41], [34, 11, 16, 42], [35, 8, 16, 44], [36, 6, 17, 4], [36, 7, 17, 5], [37, 6, 18, 4, "duration"], [37, 14, 18, 12], [37, 16, 18, 14, "DEFAULT_LIGHTSPEED_TIME"], [38, 4, 19, 2], [38, 5, 19, 3], [39, 4, 21, 2, "LightSpeedInLeft"], [39, 20, 21, 18], [39, 22, 21, 20], [40, 6, 22, 4, "name"], [40, 10, 22, 8], [40, 12, 22, 10], [40, 30, 22, 28], [41, 6, 23, 4, "style"], [41, 11, 23, 9], [41, 13, 23, 11], [42, 8, 24, 6], [42, 9, 24, 7], [42, 11, 24, 9], [43, 10, 25, 8, "transform"], [43, 19, 25, 17], [43, 21, 25, 19], [43, 22, 25, 20], [44, 12, 25, 22, "translateX"], [44, 22, 25, 32], [44, 24, 25, 34], [44, 32, 25, 42], [45, 12, 25, 44, "skewX"], [45, 17, 25, 49], [45, 19, 25, 51], [46, 10, 25, 59], [46, 11, 25, 60], [46, 12, 25, 61], [47, 10, 26, 8, "opacity"], [47, 17, 26, 15], [47, 19, 26, 17], [48, 8, 27, 6], [48, 9, 27, 7], [49, 8, 28, 6], [49, 10, 28, 8], [49, 12, 28, 10], [50, 10, 28, 12, "transform"], [50, 19, 28, 21], [50, 21, 28, 23], [50, 22, 28, 24], [51, 12, 28, 26, "skewX"], [51, 17, 28, 31], [51, 19, 28, 33], [52, 10, 28, 42], [52, 11, 28, 43], [53, 8, 28, 45], [53, 9, 28, 46], [54, 8, 29, 6], [54, 10, 29, 8], [54, 12, 29, 10], [55, 10, 29, 12, "transform"], [55, 19, 29, 21], [55, 21, 29, 23], [55, 22, 29, 24], [56, 12, 29, 26, "skewX"], [56, 17, 29, 31], [56, 19, 29, 33], [57, 10, 29, 40], [57, 11, 29, 41], [58, 8, 29, 43], [58, 9, 29, 44], [59, 8, 30, 6], [59, 11, 30, 9], [59, 13, 30, 11], [60, 10, 30, 13, "transform"], [60, 19, 30, 22], [60, 21, 30, 24], [60, 22, 30, 25], [61, 12, 30, 27, "skewX"], [61, 17, 30, 32], [61, 19, 30, 34], [62, 10, 30, 41], [62, 11, 30, 42], [63, 8, 30, 44], [64, 6, 31, 4], [64, 7, 31, 5], [65, 6, 32, 4, "duration"], [65, 14, 32, 12], [65, 16, 32, 14, "DEFAULT_LIGHTSPEED_TIME"], [66, 4, 33, 2], [67, 2, 34, 0], [67, 3, 34, 1], [68, 2, 36, 7], [68, 6, 36, 13, "LightSpeedOutData"], [68, 23, 36, 30], [68, 26, 36, 30, "exports"], [68, 33, 36, 30], [68, 34, 36, 30, "LightSpeedOutData"], [68, 51, 36, 30], [68, 54, 36, 33], [69, 4, 37, 2, "LightSpeedOutRight"], [69, 22, 37, 20], [69, 24, 37, 22], [70, 6, 38, 4, "name"], [70, 10, 38, 8], [70, 12, 38, 10], [70, 32, 38, 30], [71, 6, 39, 4, "style"], [71, 11, 39, 9], [71, 13, 39, 11], [72, 8, 40, 6], [72, 9, 40, 7], [72, 11, 40, 9], [73, 10, 41, 8, "transform"], [73, 19, 41, 17], [73, 21, 41, 19], [73, 22, 41, 20], [74, 12, 41, 22, "translateX"], [74, 22, 41, 32], [74, 24, 41, 34], [74, 29, 41, 39], [75, 12, 41, 41, "skewX"], [75, 17, 41, 46], [75, 19, 41, 48], [76, 10, 41, 55], [76, 11, 41, 56], [76, 12, 41, 57], [77, 10, 42, 8, "opacity"], [77, 17, 42, 15], [77, 19, 42, 17], [78, 8, 43, 6], [78, 9, 43, 7], [79, 8, 44, 6], [79, 11, 44, 9], [79, 13, 44, 11], [80, 10, 45, 8, "transform"], [80, 19, 45, 17], [80, 21, 45, 19], [80, 22, 45, 20], [81, 12, 45, 22, "translateX"], [81, 22, 45, 32], [81, 24, 45, 34], [81, 31, 45, 41], [82, 12, 45, 43, "skewX"], [82, 17, 45, 48], [82, 19, 45, 50], [83, 10, 45, 59], [83, 11, 45, 60], [83, 12, 45, 61], [84, 10, 46, 8, "opacity"], [84, 17, 46, 15], [84, 19, 46, 17], [85, 8, 47, 6], [86, 6, 48, 4], [86, 7, 48, 5], [87, 6, 49, 4, "duration"], [87, 14, 49, 12], [87, 16, 49, 14, "DEFAULT_LIGHTSPEED_TIME"], [88, 4, 50, 2], [88, 5, 50, 3], [89, 4, 52, 2, "LightSpeedOutLeft"], [89, 21, 52, 19], [89, 23, 52, 21], [90, 6, 53, 4, "name"], [90, 10, 53, 8], [90, 12, 53, 10], [90, 31, 53, 29], [91, 6, 54, 4, "style"], [91, 11, 54, 9], [91, 13, 54, 11], [92, 8, 55, 6], [92, 9, 55, 7], [92, 11, 55, 9], [93, 10, 56, 8, "transform"], [93, 19, 56, 17], [93, 21, 56, 19], [93, 22, 56, 20], [94, 12, 56, 22, "translateX"], [94, 22, 56, 32], [94, 24, 56, 34], [94, 29, 56, 39], [95, 12, 56, 41, "skew"], [95, 16, 56, 45], [95, 18, 56, 47], [96, 10, 56, 54], [96, 11, 56, 55], [96, 12, 56, 56], [97, 10, 57, 8, "opacity"], [97, 17, 57, 15], [97, 19, 57, 17], [98, 8, 58, 6], [98, 9, 58, 7], [99, 8, 59, 6], [99, 11, 59, 9], [99, 13, 59, 11], [100, 10, 60, 8, "transform"], [100, 19, 60, 17], [100, 21, 60, 19], [100, 22, 60, 20], [101, 12, 60, 22, "translateX"], [101, 22, 60, 32], [101, 24, 60, 34], [101, 32, 60, 42], [102, 12, 60, 44, "skew"], [102, 16, 60, 48], [102, 18, 60, 50], [103, 10, 60, 58], [103, 11, 60, 59], [103, 12, 60, 60], [104, 10, 61, 8, "opacity"], [104, 17, 61, 15], [104, 19, 61, 17], [105, 8, 62, 6], [106, 6, 63, 4], [106, 7, 63, 5], [107, 6, 64, 4, "duration"], [107, 14, 64, 12], [107, 16, 64, 14, "DEFAULT_LIGHTSPEED_TIME"], [108, 4, 65, 2], [109, 2, 66, 0], [109, 3, 66, 1], [110, 2, 68, 7], [110, 6, 68, 13, "LightSpeedIn"], [110, 18, 68, 25], [110, 21, 68, 25, "exports"], [110, 28, 68, 25], [110, 29, 68, 25, "LightSpeedIn"], [110, 41, 68, 25], [110, 44, 68, 28], [111, 4, 69, 2, "LightSpeedInRight"], [111, 21, 69, 19], [111, 23, 69, 21], [112, 6, 70, 4, "style"], [112, 11, 70, 9], [112, 13, 70, 11], [112, 17, 70, 11, "convertAnimationObjectToKeyframes"], [112, 67, 70, 44], [112, 69, 71, 6, "LightSpeedInData"], [112, 85, 71, 22], [112, 86, 71, 23, "LightSpeedInRight"], [112, 103, 72, 4], [112, 104, 72, 5], [113, 6, 73, 4, "duration"], [113, 14, 73, 12], [113, 16, 73, 14, "LightSpeedInData"], [113, 32, 73, 30], [113, 33, 73, 31, "LightSpeedInRight"], [113, 50, 73, 48], [113, 51, 73, 49, "duration"], [114, 4, 74, 2], [114, 5, 74, 3], [115, 4, 75, 2, "LightSpeedInLeft"], [115, 20, 75, 18], [115, 22, 75, 20], [116, 6, 76, 4, "style"], [116, 11, 76, 9], [116, 13, 76, 11], [116, 17, 76, 11, "convertAnimationObjectToKeyframes"], [116, 67, 76, 44], [116, 69, 76, 45, "LightSpeedInData"], [116, 85, 76, 61], [116, 86, 76, 62, "LightSpeedInLeft"], [116, 102, 76, 78], [116, 103, 76, 79], [117, 6, 77, 4, "duration"], [117, 14, 77, 12], [117, 16, 77, 14, "LightSpeedInData"], [117, 32, 77, 30], [117, 33, 77, 31, "LightSpeedInLeft"], [117, 49, 77, 47], [117, 50, 77, 48, "duration"], [118, 4, 78, 2], [119, 2, 79, 0], [119, 3, 79, 1], [120, 2, 81, 7], [120, 6, 81, 13, "LightSpeedOut"], [120, 19, 81, 26], [120, 22, 81, 26, "exports"], [120, 29, 81, 26], [120, 30, 81, 26, "LightSpeedOut"], [120, 43, 81, 26], [120, 46, 81, 29], [121, 4, 82, 2, "LightSpeedOutRight"], [121, 22, 82, 20], [121, 24, 82, 22], [122, 6, 83, 4, "style"], [122, 11, 83, 9], [122, 13, 83, 11], [122, 17, 83, 11, "convertAnimationObjectToKeyframes"], [122, 67, 83, 44], [122, 69, 84, 6, "LightSpeedOutData"], [122, 86, 84, 23], [122, 87, 84, 24, "LightSpeedOutRight"], [122, 105, 85, 4], [122, 106, 85, 5], [123, 6, 86, 4, "duration"], [123, 14, 86, 12], [123, 16, 86, 14, "LightSpeedOutData"], [123, 33, 86, 31], [123, 34, 86, 32, "LightSpeedOutRight"], [123, 52, 86, 50], [123, 53, 86, 51, "duration"], [124, 4, 87, 2], [124, 5, 87, 3], [125, 4, 88, 2, "LightSpeedOutLeft"], [125, 21, 88, 19], [125, 23, 88, 21], [126, 6, 89, 4, "style"], [126, 11, 89, 9], [126, 13, 89, 11], [126, 17, 89, 11, "convertAnimationObjectToKeyframes"], [126, 67, 89, 44], [126, 69, 90, 6, "LightSpeedOutData"], [126, 86, 90, 23], [126, 87, 90, 24, "LightSpeedOutLeft"], [126, 104, 91, 4], [126, 105, 91, 5], [127, 6, 92, 4, "duration"], [127, 14, 92, 12], [127, 16, 92, 14, "LightSpeedOutData"], [127, 33, 92, 31], [127, 34, 92, 32, "LightSpeedOutLeft"], [127, 51, 92, 49], [127, 52, 92, 50, "duration"], [128, 4, 93, 2], [129, 2, 94, 0], [129, 3, 94, 1], [130, 0, 94, 2], [130, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}