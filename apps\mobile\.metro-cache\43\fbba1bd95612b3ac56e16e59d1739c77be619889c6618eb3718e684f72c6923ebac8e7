{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 12, "index": 292}, "end": {"line": 12, "column": 28, "index": 308}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * @license React\n   * use-sync-external-store-shim.native.production.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  \"use strict\";\n\n  var React = require(_dependencyMap[0], \"react\");\n  function is(x, y) {\n    return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n  }\n  var objectIs = \"function\" === typeof Object.is ? Object.is : is,\n    useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue;\n  function useSyncExternalStore$1(subscribe, getSnapshot) {\n    var value = getSnapshot(),\n      _useState = useState({\n        inst: {\n          value: value,\n          getSnapshot: getSnapshot\n        }\n      }),\n      inst = _useState[0].inst,\n      forceUpdate = _useState[1];\n    useLayoutEffect(function () {\n      inst.value = value;\n      inst.getSnapshot = getSnapshot;\n      checkIfSnapshotChanged(inst) && forceUpdate({\n        inst: inst\n      });\n    }, [subscribe, value, getSnapshot]);\n    useEffect(function () {\n      checkIfSnapshotChanged(inst) && forceUpdate({\n        inst: inst\n      });\n      return subscribe(function () {\n        checkIfSnapshotChanged(inst) && forceUpdate({\n          inst: inst\n        });\n      });\n    }, [subscribe]);\n    useDebugValue(value);\n    return value;\n  }\n  function checkIfSnapshotChanged(inst) {\n    var latestGetSnapshot = inst.getSnapshot;\n    inst = inst.value;\n    try {\n      var nextValue = latestGetSnapshot();\n      return !objectIs(inst, nextValue);\n    } catch (error) {\n      return !0;\n    }\n  }\n  exports.useSyncExternalStore = void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : useSyncExternalStore$1;\n});", "lineCount": 64, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 12, 0], [14, 6, 12, 4, "React"], [14, 11, 12, 9], [14, 14, 12, 12, "require"], [14, 21, 12, 19], [14, 22, 12, 19, "_dependencyMap"], [14, 36, 12, 19], [14, 48, 12, 27], [14, 49, 12, 28], [15, 2, 13, 0], [15, 11, 13, 9, "is"], [15, 13, 13, 11, "is"], [15, 14, 13, 12, "x"], [15, 15, 13, 13], [15, 17, 13, 15, "y"], [15, 18, 13, 16], [15, 20, 13, 18], [16, 4, 14, 2], [16, 11, 14, 10, "x"], [16, 12, 14, 11], [16, 17, 14, 16, "y"], [16, 18, 14, 17], [16, 23, 14, 22], [16, 24, 14, 23], [16, 29, 14, 28, "x"], [16, 30, 14, 29], [16, 34, 14, 33], [16, 35, 14, 34], [16, 38, 14, 37, "x"], [16, 39, 14, 38], [16, 44, 14, 43], [16, 45, 14, 44], [16, 48, 14, 47, "y"], [16, 49, 14, 48], [16, 50, 14, 49], [16, 54, 14, 55, "x"], [16, 55, 14, 56], [16, 60, 14, 61, "x"], [16, 61, 14, 62], [16, 65, 14, 66, "y"], [16, 66, 14, 67], [16, 71, 14, 72, "y"], [16, 72, 14, 74], [17, 2, 15, 0], [18, 2, 16, 0], [18, 6, 16, 4, "objectIs"], [18, 14, 16, 12], [18, 17, 16, 15], [18, 27, 16, 25], [18, 32, 16, 30], [18, 39, 16, 37, "Object"], [18, 45, 16, 43], [18, 46, 16, 44, "is"], [18, 48, 16, 46], [18, 51, 16, 49, "Object"], [18, 57, 16, 55], [18, 58, 16, 56, "is"], [18, 60, 16, 58], [18, 63, 16, 61, "is"], [18, 65, 16, 63], [19, 4, 17, 2, "useState"], [19, 12, 17, 10], [19, 15, 17, 13, "React"], [19, 20, 17, 18], [19, 21, 17, 19, "useState"], [19, 29, 17, 27], [20, 4, 18, 2, "useEffect"], [20, 13, 18, 11], [20, 16, 18, 14, "React"], [20, 21, 18, 19], [20, 22, 18, 20, "useEffect"], [20, 31, 18, 29], [21, 4, 19, 2, "useLayoutEffect"], [21, 19, 19, 17], [21, 22, 19, 20, "React"], [21, 27, 19, 25], [21, 28, 19, 26, "useLayoutEffect"], [21, 43, 19, 41], [22, 4, 20, 2, "useDebugValue"], [22, 17, 20, 15], [22, 20, 20, 18, "React"], [22, 25, 20, 23], [22, 26, 20, 24, "useDebugValue"], [22, 39, 20, 37], [23, 2, 21, 0], [23, 11, 21, 9, "useSyncExternalStore$1"], [23, 33, 21, 31, "useSyncExternalStore$1"], [23, 34, 21, 32, "subscribe"], [23, 43, 21, 41], [23, 45, 21, 43, "getSnapshot"], [23, 56, 21, 54], [23, 58, 21, 56], [24, 4, 22, 2], [24, 8, 22, 6, "value"], [24, 13, 22, 11], [24, 16, 22, 14, "getSnapshot"], [24, 27, 22, 25], [24, 28, 22, 26], [24, 29, 22, 27], [25, 6, 23, 4, "_useState"], [25, 15, 23, 13], [25, 18, 23, 16, "useState"], [25, 26, 23, 24], [25, 27, 23, 25], [26, 8, 23, 27, "inst"], [26, 12, 23, 31], [26, 14, 23, 33], [27, 10, 23, 35, "value"], [27, 15, 23, 40], [27, 17, 23, 42, "value"], [27, 22, 23, 47], [28, 10, 23, 49, "getSnapshot"], [28, 21, 23, 60], [28, 23, 23, 62, "getSnapshot"], [29, 8, 23, 74], [30, 6, 23, 76], [30, 7, 23, 77], [30, 8, 23, 78], [31, 6, 24, 4, "inst"], [31, 10, 24, 8], [31, 13, 24, 11, "_useState"], [31, 22, 24, 20], [31, 23, 24, 21], [31, 24, 24, 22], [31, 25, 24, 23], [31, 26, 24, 24, "inst"], [31, 30, 24, 28], [32, 6, 25, 4, "forceUpdate"], [32, 17, 25, 15], [32, 20, 25, 18, "_useState"], [32, 29, 25, 27], [32, 30, 25, 28], [32, 31, 25, 29], [32, 32, 25, 30], [33, 4, 26, 2, "useLayoutEffect"], [33, 19, 26, 17], [33, 20, 27, 4], [33, 32, 27, 16], [34, 6, 28, 6, "inst"], [34, 10, 28, 10], [34, 11, 28, 11, "value"], [34, 16, 28, 16], [34, 19, 28, 19, "value"], [34, 24, 28, 24], [35, 6, 29, 6, "inst"], [35, 10, 29, 10], [35, 11, 29, 11, "getSnapshot"], [35, 22, 29, 22], [35, 25, 29, 25, "getSnapshot"], [35, 36, 29, 36], [36, 6, 30, 6, "checkIfSnapshotChanged"], [36, 28, 30, 28], [36, 29, 30, 29, "inst"], [36, 33, 30, 33], [36, 34, 30, 34], [36, 38, 30, 38, "forceUpdate"], [36, 49, 30, 49], [36, 50, 30, 50], [37, 8, 30, 52, "inst"], [37, 12, 30, 56], [37, 14, 30, 58, "inst"], [38, 6, 30, 63], [38, 7, 30, 64], [38, 8, 30, 65], [39, 4, 31, 4], [39, 5, 31, 5], [39, 7, 32, 4], [39, 8, 32, 5, "subscribe"], [39, 17, 32, 14], [39, 19, 32, 16, "value"], [39, 24, 32, 21], [39, 26, 32, 23, "getSnapshot"], [39, 37, 32, 34], [39, 38, 33, 2], [39, 39, 33, 3], [40, 4, 34, 2, "useEffect"], [40, 13, 34, 11], [40, 14, 35, 4], [40, 26, 35, 16], [41, 6, 36, 6, "checkIfSnapshotChanged"], [41, 28, 36, 28], [41, 29, 36, 29, "inst"], [41, 33, 36, 33], [41, 34, 36, 34], [41, 38, 36, 38, "forceUpdate"], [41, 49, 36, 49], [41, 50, 36, 50], [42, 8, 36, 52, "inst"], [42, 12, 36, 56], [42, 14, 36, 58, "inst"], [43, 6, 36, 63], [43, 7, 36, 64], [43, 8, 36, 65], [44, 6, 37, 6], [44, 13, 37, 13, "subscribe"], [44, 22, 37, 22], [44, 23, 37, 23], [44, 35, 37, 35], [45, 8, 38, 8, "checkIfSnapshotChanged"], [45, 30, 38, 30], [45, 31, 38, 31, "inst"], [45, 35, 38, 35], [45, 36, 38, 36], [45, 40, 38, 40, "forceUpdate"], [45, 51, 38, 51], [45, 52, 38, 52], [46, 10, 38, 54, "inst"], [46, 14, 38, 58], [46, 16, 38, 60, "inst"], [47, 8, 38, 65], [47, 9, 38, 66], [47, 10, 38, 67], [48, 6, 39, 6], [48, 7, 39, 7], [48, 8, 39, 8], [49, 4, 40, 4], [49, 5, 40, 5], [49, 7, 41, 4], [49, 8, 41, 5, "subscribe"], [49, 17, 41, 14], [49, 18, 42, 2], [49, 19, 42, 3], [50, 4, 43, 2, "useDebugValue"], [50, 17, 43, 15], [50, 18, 43, 16, "value"], [50, 23, 43, 21], [50, 24, 43, 22], [51, 4, 44, 2], [51, 11, 44, 9, "value"], [51, 16, 44, 14], [52, 2, 45, 0], [53, 2, 46, 0], [53, 11, 46, 9, "checkIfSnapshotChanged"], [53, 33, 46, 31, "checkIfSnapshotChanged"], [53, 34, 46, 32, "inst"], [53, 38, 46, 36], [53, 40, 46, 38], [54, 4, 47, 2], [54, 8, 47, 6, "latestGetSnapshot"], [54, 25, 47, 23], [54, 28, 47, 26, "inst"], [54, 32, 47, 30], [54, 33, 47, 31, "getSnapshot"], [54, 44, 47, 42], [55, 4, 48, 2, "inst"], [55, 8, 48, 6], [55, 11, 48, 9, "inst"], [55, 15, 48, 13], [55, 16, 48, 14, "value"], [55, 21, 48, 19], [56, 4, 49, 2], [56, 8, 49, 6], [57, 6, 50, 4], [57, 10, 50, 8, "nextValue"], [57, 19, 50, 17], [57, 22, 50, 20, "latestGetSnapshot"], [57, 39, 50, 37], [57, 40, 50, 38], [57, 41, 50, 39], [58, 6, 51, 4], [58, 13, 51, 11], [58, 14, 51, 12, "objectIs"], [58, 22, 51, 20], [58, 23, 51, 21, "inst"], [58, 27, 51, 25], [58, 29, 51, 27, "nextValue"], [58, 38, 51, 36], [58, 39, 51, 37], [59, 4, 52, 2], [59, 5, 52, 3], [59, 6, 52, 4], [59, 13, 52, 11, "error"], [59, 18, 52, 16], [59, 20, 52, 18], [60, 6, 53, 4], [60, 13, 53, 11], [60, 14, 53, 12], [60, 15, 53, 13], [61, 4, 54, 2], [62, 2, 55, 0], [63, 2, 56, 0, "exports"], [63, 9, 56, 7], [63, 10, 56, 8, "useSyncExternalStore"], [63, 30, 56, 28], [63, 33, 57, 2], [63, 38, 57, 7], [63, 39, 57, 8], [63, 44, 57, 13, "React"], [63, 49, 57, 18], [63, 50, 57, 19, "useSyncExternalStore"], [63, 70, 57, 39], [63, 73, 58, 6, "React"], [63, 78, 58, 11], [63, 79, 58, 12, "useSyncExternalStore"], [63, 99, 58, 32], [63, 102, 59, 6, "useSyncExternalStore$1"], [63, 124, 59, 28], [64, 0, 59, 29], [64, 3]], "functionMap": {"names": ["<global>", "is", "useSyncExternalStore$1", "useLayoutEffect$argument_0", "useEffect$argument_0", "subscribe$argument_0", "checkIfSnapshotChanged"], "mappings": "AAA;ACY;CDE;AEM;ICM;KDI;IEI;uBCE;ODE;KFC;CFK;AMC;CNS"}}, "type": "js/module"}]}