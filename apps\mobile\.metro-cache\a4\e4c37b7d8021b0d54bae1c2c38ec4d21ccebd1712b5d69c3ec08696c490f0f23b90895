{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../NativeModulesProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 55, "index": 55}}], "key": "JGB5dk35yFQGC/Iu4YA/4w41RN4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeModulesProxy = _interopRequireDefault(require(_dependencyMap[1], \"../NativeModulesProxy\"));\n  var _default = exports.default = _NativeModulesProxy.default.ExpoModulesCoreErrorManager;\n});", "lineCount": 9, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_NativeModulesProxy"], [7, 25, 1, 0], [7, 28, 1, 0, "_interopRequireDefault"], [7, 50, 1, 0], [7, 51, 1, 0, "require"], [7, 58, 1, 0], [7, 59, 1, 0, "_dependencyMap"], [7, 73, 1, 0], [8, 2, 1, 55], [8, 6, 1, 55, "_default"], [8, 14, 1, 55], [8, 17, 1, 55, "exports"], [8, 24, 1, 55], [8, 25, 1, 55, "default"], [8, 32, 1, 55], [8, 35, 2, 15, "NativeModulesProxy"], [8, 62, 2, 33], [8, 63, 2, 34, "ExpoModulesCoreErrorManager"], [8, 90, 2, 61], [9, 0, 2, 61], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}