{"dependencies": [{"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 22, "index": 272}, "end": {"line": 7, "column": 50, "index": 300}}], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 32, "index": 334}, "end": {"line": 8, "column": 48, "index": 350}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./styled", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 17, "index": 370}, "end": {"line": 9, "column": 36, "index": 389}}], "key": "6cuwlGG7DlwtU0+5eGZ/oKyu3Tk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _objectWithoutProperties = require(_dependencyMap[0], \"@babel/runtime/helpers/objectWithoutProperties\");\n  var _excluded = [\"component\"];\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.StyledComponent = void 0;\n  var jsx_runtime_1 = require(_dependencyMap[1], \"react/jsx-runtime\");\n  var react_1 = __importDefault(require(_dependencyMap[2], \"react\"));\n  var styled_1 = require(_dependencyMap[3], \"./styled\");\n  exports.StyledComponent = react_1.default.forwardRef((_ref, ref) => {\n    var component = _ref.component,\n      options = _objectWithoutProperties(_ref, _excluded);\n    var Component = react_1.default.useMemo(() => (0, styled_1.styled)(component), [component]);\n    return (0, jsx_runtime_1.jsx)(Component, {\n      ...options,\n      ref: ref\n    });\n  });\n});", "lineCount": 27, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_objectWithoutProperties"], [4, 30, 1, 13], [4, 33, 1, 13, "require"], [4, 40, 1, 13], [4, 41, 1, 13, "_dependencyMap"], [4, 55, 1, 13], [5, 2, 1, 13], [5, 6, 1, 13, "_excluded"], [5, 15, 1, 13], [6, 2, 2, 0], [6, 6, 2, 4, "__importDefault"], [6, 21, 2, 19], [6, 24, 2, 23], [6, 28, 2, 27], [6, 32, 2, 31], [6, 36, 2, 35], [6, 37, 2, 36, "__importDefault"], [6, 52, 2, 51], [6, 56, 2, 56], [6, 66, 2, 66, "mod"], [6, 69, 2, 69], [6, 71, 2, 71], [7, 4, 3, 4], [7, 11, 3, 12, "mod"], [7, 14, 3, 15], [7, 18, 3, 19, "mod"], [7, 21, 3, 22], [7, 22, 3, 23, "__esModule"], [7, 32, 3, 33], [7, 35, 3, 37, "mod"], [7, 38, 3, 40], [7, 41, 3, 43], [8, 6, 3, 45], [8, 15, 3, 54], [8, 17, 3, 56, "mod"], [9, 4, 3, 60], [9, 5, 3, 61], [10, 2, 4, 0], [10, 3, 4, 1], [11, 2, 5, 0, "Object"], [11, 8, 5, 6], [11, 9, 5, 7, "defineProperty"], [11, 23, 5, 21], [11, 24, 5, 22, "exports"], [11, 31, 5, 29], [11, 33, 5, 31], [11, 45, 5, 43], [11, 47, 5, 45], [12, 4, 5, 47, "value"], [12, 9, 5, 52], [12, 11, 5, 54], [13, 2, 5, 59], [13, 3, 5, 60], [13, 4, 5, 61], [14, 2, 6, 0, "exports"], [14, 9, 6, 7], [14, 10, 6, 8, "StyledComponent"], [14, 25, 6, 23], [14, 28, 6, 26], [14, 33, 6, 31], [14, 34, 6, 32], [15, 2, 7, 0], [15, 6, 7, 6, "jsx_runtime_1"], [15, 19, 7, 19], [15, 22, 7, 22, "require"], [15, 29, 7, 29], [15, 30, 7, 29, "_dependencyMap"], [15, 44, 7, 29], [15, 68, 7, 49], [15, 69, 7, 50], [16, 2, 8, 0], [16, 6, 8, 6, "react_1"], [16, 13, 8, 13], [16, 16, 8, 16, "__importDefault"], [16, 31, 8, 31], [16, 32, 8, 32, "require"], [16, 39, 8, 39], [16, 40, 8, 39, "_dependencyMap"], [16, 54, 8, 39], [16, 66, 8, 47], [16, 67, 8, 48], [16, 68, 8, 49], [17, 2, 9, 0], [17, 6, 9, 6, "styled_1"], [17, 14, 9, 14], [17, 17, 9, 17, "require"], [17, 24, 9, 24], [17, 25, 9, 24, "_dependencyMap"], [17, 39, 9, 24], [17, 54, 9, 35], [17, 55, 9, 36], [18, 2, 10, 0, "exports"], [18, 9, 10, 7], [18, 10, 10, 8, "StyledComponent"], [18, 25, 10, 23], [18, 28, 10, 26, "react_1"], [18, 35, 10, 33], [18, 36, 10, 34, "default"], [18, 43, 10, 41], [18, 44, 10, 42, "forwardRef"], [18, 54, 10, 52], [18, 55, 10, 53], [18, 56, 10, 53, "_ref"], [18, 60, 10, 53], [18, 62, 10, 81, "ref"], [18, 65, 10, 84], [18, 70, 10, 89], [19, 4, 10, 89], [19, 8, 10, 56, "component"], [19, 17, 10, 65], [19, 20, 10, 65, "_ref"], [19, 24, 10, 65], [19, 25, 10, 56, "component"], [19, 34, 10, 65], [20, 6, 10, 70, "options"], [20, 13, 10, 77], [20, 16, 10, 77, "_objectWithoutProperties"], [20, 40, 10, 77], [20, 41, 10, 77, "_ref"], [20, 45, 10, 77], [20, 47, 10, 77, "_excluded"], [20, 56, 10, 77], [21, 4, 11, 4], [21, 8, 11, 10, "Component"], [21, 17, 11, 19], [21, 20, 11, 22, "react_1"], [21, 27, 11, 29], [21, 28, 11, 30, "default"], [21, 35, 11, 37], [21, 36, 11, 38, "useMemo"], [21, 43, 11, 45], [21, 44, 11, 46], [21, 50, 11, 52], [21, 51, 11, 53], [21, 52, 11, 54], [21, 54, 11, 56, "styled_1"], [21, 62, 11, 64], [21, 63, 11, 65, "styled"], [21, 69, 11, 71], [21, 71, 11, 73, "component"], [21, 80, 11, 82], [21, 81, 11, 83], [21, 83, 11, 85], [21, 84, 11, 86, "component"], [21, 93, 11, 95], [21, 94, 11, 96], [21, 95, 11, 97], [22, 4, 12, 4], [22, 11, 12, 12], [22, 12, 12, 13], [22, 13, 12, 14], [22, 15, 12, 16, "jsx_runtime_1"], [22, 28, 12, 29], [22, 29, 12, 30, "jsx"], [22, 32, 12, 33], [22, 34, 12, 35, "Component"], [22, 43, 12, 44], [22, 45, 12, 46], [23, 6, 12, 48], [23, 9, 12, 51, "options"], [23, 16, 12, 58], [24, 6, 12, 60, "ref"], [24, 9, 12, 63], [24, 11, 12, 65, "ref"], [25, 4, 12, 69], [25, 5, 12, 70], [25, 6, 12, 71], [26, 2, 13, 0], [26, 3, 13, 1], [26, 4, 13, 2], [27, 0, 13, 3], [27, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "react_1._default.forwardRef$argument_0", "react_1._default.useMemo$argument_0"], "mappings": "AAA;wDCC;CDE;qDEM;8CCC,qCD;CFE"}}, "type": "js/module"}]}