{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react-native-is-edge-to-edge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 4, "column": 38, "index": 90}}], "key": "YCmhlO6SGIbU/kBLtNkISr4D7G0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.EDGE_TO_EDGE = void 0;\n  exports.transformEdgeToEdgeProps = transformEdgeToEdgeProps;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _reactNativeIsEdgeToEdge = require(_dependencyMap[2], \"react-native-is-edge-to-edge\");\n  var _excluded = [\"statusBarColor\", \"statusBarTranslucent\", \"navigationBarColor\", \"navigationBarTranslucent\"];\n  var EDGE_TO_EDGE = exports.EDGE_TO_EDGE = (0, _reactNativeIsEdgeToEdge.isEdgeToEdge)();\n  function transformEdgeToEdgeProps(props) {\n    var statusBarColor = props.statusBarColor,\n      statusBarTranslucent = props.statusBarTranslucent,\n      navigationBarColor = props.navigationBarColor,\n      navigationBarTranslucent = props.navigationBarTranslucent,\n      rest = (0, _objectWithoutProperties2.default)(props, _excluded);\n    if (__DEV__) {\n      (0, _reactNativeIsEdgeToEdge.controlEdgeToEdgeValues)({\n        statusBarColor,\n        statusBarTranslucent,\n        navigationBarColor,\n        navigationBarTranslucent\n      });\n    }\n    return rest;\n  }\n});", "lineCount": 28, "map": [[9, 2, 1, 0], [9, 6, 1, 0, "_reactNativeIsEdgeToEdge"], [9, 30, 1, 0], [9, 33, 1, 0, "require"], [9, 40, 1, 0], [9, 41, 1, 0, "_dependencyMap"], [9, 55, 1, 0], [10, 2, 4, 38], [10, 6, 4, 38, "_excluded"], [10, 15, 4, 38], [11, 2, 7, 7], [11, 6, 7, 13, "EDGE_TO_EDGE"], [11, 18, 7, 25], [11, 21, 7, 25, "exports"], [11, 28, 7, 25], [11, 29, 7, 25, "EDGE_TO_EDGE"], [11, 41, 7, 25], [11, 44, 7, 28], [11, 48, 7, 28, "isEdgeToEdge"], [11, 85, 7, 40], [11, 87, 7, 41], [11, 88, 7, 42], [12, 2, 9, 7], [12, 11, 9, 16, "transformEdgeToEdgeProps"], [12, 35, 9, 40, "transformEdgeToEdgeProps"], [12, 36, 9, 41, "props"], [12, 41, 9, 59], [12, 43, 9, 74], [13, 4, 10, 2], [13, 8, 12, 4, "statusBarColor"], [13, 22, 12, 18], [13, 25, 17, 6, "props"], [13, 30, 17, 11], [13, 31, 12, 4, "statusBarColor"], [13, 45, 12, 18], [14, 6, 13, 4, "statusBarTranslucent"], [14, 26, 13, 24], [14, 29, 17, 6, "props"], [14, 34, 17, 11], [14, 35, 13, 4, "statusBarTranslucent"], [14, 55, 13, 24], [15, 6, 14, 4, "navigationBarColor"], [15, 24, 14, 22], [15, 27, 17, 6, "props"], [15, 32, 17, 11], [15, 33, 14, 4, "navigationBarColor"], [15, 51, 14, 22], [16, 6, 15, 4, "navigationBarTranslucent"], [16, 30, 15, 28], [16, 33, 17, 6, "props"], [16, 38, 17, 11], [16, 39, 15, 4, "navigationBarTranslucent"], [16, 63, 15, 28], [17, 6, 16, 7, "rest"], [17, 10, 16, 11], [17, 17, 16, 11, "_objectWithoutProperties2"], [17, 42, 16, 11], [17, 43, 16, 11, "default"], [17, 50, 16, 11], [17, 52, 17, 6, "props"], [17, 57, 17, 11], [17, 59, 17, 11, "_excluded"], [17, 68, 17, 11], [18, 4, 19, 2], [18, 8, 19, 6, "__DEV__"], [18, 15, 19, 13], [18, 17, 19, 15], [19, 6, 20, 4], [19, 10, 20, 4, "controlEdgeToEdgeValues"], [19, 58, 20, 27], [19, 60, 20, 28], [20, 8, 21, 6, "statusBarColor"], [20, 22, 21, 20], [21, 8, 22, 6, "statusBarTranslucent"], [21, 28, 22, 26], [22, 8, 23, 6, "navigationBarColor"], [22, 26, 23, 24], [23, 8, 24, 6, "navigationBarTranslucent"], [24, 6, 25, 4], [24, 7, 25, 5], [24, 8, 25, 6], [25, 4, 26, 2], [26, 4, 28, 2], [26, 11, 28, 9, "rest"], [26, 15, 28, 13], [27, 2, 29, 0], [28, 0, 29, 1], [28, 3]], "functionMap": {"names": ["<global>", "transformEdgeToEdgeProps"], "mappings": "AAA;OCQ;CDoB"}}, "type": "js/module"}]}