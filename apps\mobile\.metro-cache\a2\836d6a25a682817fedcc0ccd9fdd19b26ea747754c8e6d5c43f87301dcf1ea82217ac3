{"dependencies": [{"name": "./useEvent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 197}, "end": {"line": 8, "column": 38, "index": 235}}], "key": "agcKO4KjKVVd8qmhkCqgPk8SZT0=", "exportNames": ["*"]}}, {"name": "./useHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 236}, "end": {"line": 9, "column": 42, "index": 278}}], "key": "4fwTVy9JjjGj2GzFTCIyp4pa48c=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedScrollHandler = useAnimatedScrollHandler;\n  var _useEvent = require(_dependencyMap[0], \"./useEvent\");\n  var _useHandler2 = require(_dependencyMap[1], \"./useHandler\");\n  /**\n   * Lets you run callbacks on ScrollView events. Supports `onScroll`,\n   * `onBeginDrag`, `onEndDrag`, `onMomentumBegin`, and `onMomentumEnd` events.\n   *\n   * These callbacks are automatically workletized and ran on the UI thread.\n   *\n   * @param handlers - An object containing event handlers.\n   * @param dependencies - An optional array of dependencies. Only relevant when\n   *   using Reanimated without the Babel plugin on the Web.\n   * @returns An object you need to pass to `onScroll` prop on the\n   *   `Animated.ScrollView` component.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/scroll/useAnimatedScrollHandler\n   */\n  // @ts-expect-error This overload is required by our API.\n  var _worklet_14483949764600_init_data = {\n    code: \"function useAnimatedScrollHandlerTs1(event){const{scrollHandlers,context}=this.__closure;const{onScroll:onScroll,onBeginDrag:onBeginDrag,onEndDrag:onEndDrag,onMomentumBegin:onMomentumBegin,onMomentumEnd:onMomentumEnd}=scrollHandlers;if(onScroll&&event.eventName.endsWith('onScroll')){onScroll(event,context);}else if(onBeginDrag&&event.eventName.endsWith('onScrollBeginDrag')){onBeginDrag(event,context);}else if(onEndDrag&&event.eventName.endsWith('onScrollEndDrag')){onEndDrag(event,context);}else if(onMomentumBegin&&event.eventName.endsWith('onMomentumScrollBegin')){onMomentumBegin(event,context);}else if(onMomentumEnd&&event.eventName.endsWith('onMomentumScrollEnd')){onMomentumEnd(event,context);}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\hook\\\\useAnimatedScrollHandler.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useAnimatedScrollHandlerTs1\\\",\\\"event\\\",\\\"scrollHandlers\\\",\\\"context\\\",\\\"__closure\\\",\\\"onScroll\\\",\\\"onBeginDrag\\\",\\\"onEndDrag\\\",\\\"onMomentumBegin\\\",\\\"onMomentumEnd\\\",\\\"eventName\\\",\\\"endsWith\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/hook/useAnimatedScrollHandler.ts\\\"],\\\"mappings\\\":\\\"AA8EI,QAAC,CAAAA,2BAAiCA,CAAAC,KAAA,QAAAC,cAAA,CAAAC,OAAA,OAAAC,SAAA,CAEhC,KAAM,CACJC,QAAQ,CAARA,QAAQ,CACRC,WAAW,CAAXA,WAAW,CACXC,SAAS,CAATA,SAAS,CACTC,eAAe,CAAfA,eAAe,CACfC,aAAA,CAAAA,aACF,CAAC,CAAGP,cAAc,CAClB,GAAIG,QAAQ,EAAIJ,KAAK,CAACS,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAE,CACpDN,QAAQ,CAACJ,KAAK,CAAEE,OAAO,CAAC,CAC1B,CAAC,IAAM,IAAIG,WAAW,EAAIL,KAAK,CAACS,SAAS,CAACC,QAAQ,CAAC,mBAAmB,CAAC,CAAE,CACvEL,WAAW,CAACL,KAAK,CAAEE,OAAO,CAAC,CAC7B,CAAC,IAAM,IAAII,SAAS,EAAIN,KAAK,CAACS,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAAE,CACnEJ,SAAS,CAACN,KAAK,CAAEE,OAAO,CAAC,CAC3B,CAAC,IAAM,IACLK,eAAe,EACfP,KAAK,CAACS,SAAS,CAACC,QAAQ,CAAC,uBAAuB,CAAC,CACjD,CACAH,eAAe,CAACP,KAAK,CAAEE,OAAO,CAAC,CACjC,CAAC,IAAM,IACLM,aAAa,EACbR,KAAK,CAACS,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,CAC/C,CACAF,aAAa,CAACR,KAAK,CAAEE,OAAO,CAAC,CAC/B,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function useAnimatedScrollHandler(handlers, dependencies) {\n    // case when handlers is a function\n    var scrollHandlers = typeof handlers === 'function' ? {\n      onScroll: handlers\n    } : handlers;\n    var _useHandler = (0, _useHandler2.useHandler)(scrollHandlers, dependencies),\n      context = _useHandler.context,\n      doDependenciesDiffer = _useHandler.doDependenciesDiffer;\n\n    // build event subscription array\n    var subscribeForEvents = ['onScroll'];\n    if (scrollHandlers.onBeginDrag !== undefined) {\n      subscribeForEvents.push('onScrollBeginDrag');\n    }\n    if (scrollHandlers.onEndDrag !== undefined) {\n      subscribeForEvents.push('onScrollEndDrag');\n    }\n    if (scrollHandlers.onMomentumBegin !== undefined) {\n      subscribeForEvents.push('onMomentumScrollBegin');\n    }\n    if (scrollHandlers.onMomentumEnd !== undefined) {\n      subscribeForEvents.push('onMomentumScrollEnd');\n    }\n    return (0, _useEvent.useEvent)(function () {\n      var _e = [new global.Error(), -3, -27];\n      var useAnimatedScrollHandlerTs1 = function (event) {\n        var onScroll = scrollHandlers.onScroll,\n          onBeginDrag = scrollHandlers.onBeginDrag,\n          onEndDrag = scrollHandlers.onEndDrag,\n          onMomentumBegin = scrollHandlers.onMomentumBegin,\n          onMomentumEnd = scrollHandlers.onMomentumEnd;\n        if (onScroll && event.eventName.endsWith('onScroll')) {\n          onScroll(event, context);\n        } else if (onBeginDrag && event.eventName.endsWith('onScrollBeginDrag')) {\n          onBeginDrag(event, context);\n        } else if (onEndDrag && event.eventName.endsWith('onScrollEndDrag')) {\n          onEndDrag(event, context);\n        } else if (onMomentumBegin && event.eventName.endsWith('onMomentumScrollBegin')) {\n          onMomentumBegin(event, context);\n        } else if (onMomentumEnd && event.eventName.endsWith('onMomentumScrollEnd')) {\n          onMomentumEnd(event, context);\n        }\n      };\n      useAnimatedScrollHandlerTs1.__closure = {\n        scrollHandlers,\n        context\n      };\n      useAnimatedScrollHandlerTs1.__workletHash = 14483949764600;\n      useAnimatedScrollHandlerTs1.__initData = _worklet_14483949764600_init_data;\n      useAnimatedScrollHandlerTs1.__stackDetails = _e;\n      return useAnimatedScrollHandlerTs1;\n    }(), subscribeForEvents, doDependenciesDiffer\n    // Read https://github.com/software-mansion/react-native-reanimated/pull/5056\n    // for more information about this cast.\n    );\n  }\n});", "lineCount": 86, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedScrollHandler"], [7, 34, 1, 13], [7, 37, 1, 13, "useAnimatedScrollHandler"], [7, 61, 1, 13], [8, 2, 8, 0], [8, 6, 8, 0, "_useEvent"], [8, 15, 8, 0], [8, 18, 8, 0, "require"], [8, 25, 8, 0], [8, 26, 8, 0, "_dependencyMap"], [8, 40, 8, 0], [9, 2, 9, 0], [9, 6, 9, 0, "_useHandler2"], [9, 18, 9, 0], [9, 21, 9, 0, "require"], [9, 28, 9, 0], [9, 29, 9, 0, "_dependencyMap"], [9, 43, 9, 0], [10, 2, 28, 0], [11, 0, 29, 0], [12, 0, 30, 0], [13, 0, 31, 0], [14, 0, 32, 0], [15, 0, 33, 0], [16, 0, 34, 0], [17, 0, 35, 0], [18, 0, 36, 0], [19, 0, 37, 0], [20, 0, 38, 0], [21, 0, 39, 0], [22, 0, 40, 0], [23, 2, 41, 0], [24, 2, 41, 0], [24, 6, 41, 0, "_worklet_14483949764600_init_data"], [24, 39, 41, 0], [25, 4, 41, 0, "code"], [25, 8, 41, 0], [26, 4, 41, 0, "location"], [26, 12, 41, 0], [27, 4, 41, 0, "sourceMap"], [27, 13, 41, 0], [28, 4, 41, 0, "version"], [28, 11, 41, 0], [29, 2, 41, 0], [30, 2, 49, 7], [30, 11, 49, 16, "useAnimatedScrollHandler"], [30, 35, 49, 40, "useAnimatedScrollHandler"], [30, 36, 52, 2, "handlers"], [30, 44, 52, 60], [30, 46, 53, 2, "dependencies"], [30, 58, 53, 31], [30, 60, 54, 2], [31, 4, 55, 2], [32, 4, 56, 2], [32, 8, 56, 8, "scrollHandlers"], [32, 22, 56, 47], [32, 25, 57, 4], [32, 32, 57, 11, "handlers"], [32, 40, 57, 19], [32, 45, 57, 24], [32, 55, 57, 34], [32, 58, 57, 37], [33, 6, 57, 39, "onScroll"], [33, 14, 57, 47], [33, 16, 57, 49, "handlers"], [34, 4, 57, 58], [34, 5, 57, 59], [34, 8, 57, 62, "handlers"], [34, 16, 57, 70], [35, 4, 58, 2], [35, 8, 58, 2, "_use<PERSON><PERSON>ler"], [35, 19, 58, 2], [35, 22, 58, 44], [35, 26, 58, 44, "useHandler"], [35, 49, 58, 54], [35, 51, 61, 4, "scrollHandlers"], [35, 65, 61, 18], [35, 67, 61, 62, "dependencies"], [35, 79, 61, 74], [35, 80, 61, 75], [36, 6, 58, 10, "context"], [36, 13, 58, 17], [36, 16, 58, 17, "_use<PERSON><PERSON>ler"], [36, 27, 58, 17], [36, 28, 58, 10, "context"], [36, 35, 58, 17], [37, 6, 58, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [37, 26, 58, 39], [37, 29, 58, 39, "_use<PERSON><PERSON>ler"], [37, 40, 58, 39], [37, 41, 58, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [37, 61, 58, 39], [39, 4, 63, 2], [40, 4, 64, 2], [40, 8, 64, 8, "subscribeForEvents"], [40, 26, 64, 26], [40, 29, 64, 29], [40, 30, 64, 30], [40, 40, 64, 40], [40, 41, 64, 41], [41, 4, 65, 2], [41, 8, 65, 6, "scrollHandlers"], [41, 22, 65, 20], [41, 23, 65, 21, "onBeginDrag"], [41, 34, 65, 32], [41, 39, 65, 37, "undefined"], [41, 48, 65, 46], [41, 50, 65, 48], [42, 6, 66, 4, "subscribeForEvents"], [42, 24, 66, 22], [42, 25, 66, 23, "push"], [42, 29, 66, 27], [42, 30, 66, 28], [42, 49, 66, 47], [42, 50, 66, 48], [43, 4, 67, 2], [44, 4, 68, 2], [44, 8, 68, 6, "scrollHandlers"], [44, 22, 68, 20], [44, 23, 68, 21, "onEndDrag"], [44, 32, 68, 30], [44, 37, 68, 35, "undefined"], [44, 46, 68, 44], [44, 48, 68, 46], [45, 6, 69, 4, "subscribeForEvents"], [45, 24, 69, 22], [45, 25, 69, 23, "push"], [45, 29, 69, 27], [45, 30, 69, 28], [45, 47, 69, 45], [45, 48, 69, 46], [46, 4, 70, 2], [47, 4, 71, 2], [47, 8, 71, 6, "scrollHandlers"], [47, 22, 71, 20], [47, 23, 71, 21, "onMomentumBegin"], [47, 38, 71, 36], [47, 43, 71, 41, "undefined"], [47, 52, 71, 50], [47, 54, 71, 52], [48, 6, 72, 4, "subscribeForEvents"], [48, 24, 72, 22], [48, 25, 72, 23, "push"], [48, 29, 72, 27], [48, 30, 72, 28], [48, 53, 72, 51], [48, 54, 72, 52], [49, 4, 73, 2], [50, 4, 74, 2], [50, 8, 74, 6, "scrollHandlers"], [50, 22, 74, 20], [50, 23, 74, 21, "onMomentumEnd"], [50, 36, 74, 34], [50, 41, 74, 39, "undefined"], [50, 50, 74, 48], [50, 52, 74, 50], [51, 6, 75, 4, "subscribeForEvents"], [51, 24, 75, 22], [51, 25, 75, 23, "push"], [51, 29, 75, 27], [51, 30, 75, 28], [51, 51, 75, 49], [51, 52, 75, 50], [52, 4, 76, 2], [53, 4, 78, 2], [53, 11, 78, 9], [53, 15, 78, 9, "useEvent"], [53, 33, 78, 17], [53, 35, 79, 4], [54, 6, 79, 4], [54, 10, 79, 4, "_e"], [54, 12, 79, 4], [54, 20, 79, 4, "global"], [54, 26, 79, 4], [54, 27, 79, 4, "Error"], [54, 32, 79, 4], [55, 6, 79, 4], [55, 10, 79, 4, "useAnimatedScrollHandlerTs1"], [55, 37, 79, 4], [55, 49, 79, 4, "useAnimatedScrollHandlerTs1"], [55, 50, 79, 5, "event"], [55, 55, 79, 33], [55, 57, 79, 38], [56, 8, 81, 6], [56, 12, 82, 8, "onScroll"], [56, 20, 82, 16], [56, 23, 87, 10, "scrollHandlers"], [56, 37, 87, 24], [56, 38, 82, 8, "onScroll"], [56, 46, 82, 16], [57, 10, 83, 8, "onBeginDrag"], [57, 21, 83, 19], [57, 24, 87, 10, "scrollHandlers"], [57, 38, 87, 24], [57, 39, 83, 8, "onBeginDrag"], [57, 50, 83, 19], [58, 10, 84, 8, "onEndDrag"], [58, 19, 84, 17], [58, 22, 87, 10, "scrollHandlers"], [58, 36, 87, 24], [58, 37, 84, 8, "onEndDrag"], [58, 46, 84, 17], [59, 10, 85, 8, "onMomentumBegin"], [59, 25, 85, 23], [59, 28, 87, 10, "scrollHandlers"], [59, 42, 87, 24], [59, 43, 85, 8, "onMomentumBegin"], [59, 58, 85, 23], [60, 10, 86, 8, "onMomentumEnd"], [60, 23, 86, 21], [60, 26, 87, 10, "scrollHandlers"], [60, 40, 87, 24], [60, 41, 86, 8, "onMomentumEnd"], [60, 54, 86, 21], [61, 8, 88, 6], [61, 12, 88, 10, "onScroll"], [61, 20, 88, 18], [61, 24, 88, 22, "event"], [61, 29, 88, 27], [61, 30, 88, 28, "eventName"], [61, 39, 88, 37], [61, 40, 88, 38, "endsWith"], [61, 48, 88, 46], [61, 49, 88, 47], [61, 59, 88, 57], [61, 60, 88, 58], [61, 62, 88, 60], [62, 10, 89, 8, "onScroll"], [62, 18, 89, 16], [62, 19, 89, 17, "event"], [62, 24, 89, 22], [62, 26, 89, 24, "context"], [62, 33, 89, 31], [62, 34, 89, 32], [63, 8, 90, 6], [63, 9, 90, 7], [63, 15, 90, 13], [63, 19, 90, 17, "onBeginDrag"], [63, 30, 90, 28], [63, 34, 90, 32, "event"], [63, 39, 90, 37], [63, 40, 90, 38, "eventName"], [63, 49, 90, 47], [63, 50, 90, 48, "endsWith"], [63, 58, 90, 56], [63, 59, 90, 57], [63, 78, 90, 76], [63, 79, 90, 77], [63, 81, 90, 79], [64, 10, 91, 8, "onBeginDrag"], [64, 21, 91, 19], [64, 22, 91, 20, "event"], [64, 27, 91, 25], [64, 29, 91, 27, "context"], [64, 36, 91, 34], [64, 37, 91, 35], [65, 8, 92, 6], [65, 9, 92, 7], [65, 15, 92, 13], [65, 19, 92, 17, "onEndDrag"], [65, 28, 92, 26], [65, 32, 92, 30, "event"], [65, 37, 92, 35], [65, 38, 92, 36, "eventName"], [65, 47, 92, 45], [65, 48, 92, 46, "endsWith"], [65, 56, 92, 54], [65, 57, 92, 55], [65, 74, 92, 72], [65, 75, 92, 73], [65, 77, 92, 75], [66, 10, 93, 8, "onEndDrag"], [66, 19, 93, 17], [66, 20, 93, 18, "event"], [66, 25, 93, 23], [66, 27, 93, 25, "context"], [66, 34, 93, 32], [66, 35, 93, 33], [67, 8, 94, 6], [67, 9, 94, 7], [67, 15, 94, 13], [67, 19, 95, 8, "onMomentumBegin"], [67, 34, 95, 23], [67, 38, 96, 8, "event"], [67, 43, 96, 13], [67, 44, 96, 14, "eventName"], [67, 53, 96, 23], [67, 54, 96, 24, "endsWith"], [67, 62, 96, 32], [67, 63, 96, 33], [67, 86, 96, 56], [67, 87, 96, 57], [67, 89, 97, 8], [68, 10, 98, 8, "onMomentumBegin"], [68, 25, 98, 23], [68, 26, 98, 24, "event"], [68, 31, 98, 29], [68, 33, 98, 31, "context"], [68, 40, 98, 38], [68, 41, 98, 39], [69, 8, 99, 6], [69, 9, 99, 7], [69, 15, 99, 13], [69, 19, 100, 8, "onMomentumEnd"], [69, 32, 100, 21], [69, 36, 101, 8, "event"], [69, 41, 101, 13], [69, 42, 101, 14, "eventName"], [69, 51, 101, 23], [69, 52, 101, 24, "endsWith"], [69, 60, 101, 32], [69, 61, 101, 33], [69, 82, 101, 54], [69, 83, 101, 55], [69, 85, 102, 8], [70, 10, 103, 8, "onMomentumEnd"], [70, 23, 103, 21], [70, 24, 103, 22, "event"], [70, 29, 103, 27], [70, 31, 103, 29, "context"], [70, 38, 103, 36], [70, 39, 103, 37], [71, 8, 104, 6], [72, 6, 105, 4], [72, 7, 105, 5], [73, 6, 105, 5, "useAnimatedScrollHandlerTs1"], [73, 33, 105, 5], [73, 34, 105, 5, "__closure"], [73, 43, 105, 5], [74, 8, 105, 5, "scrollHandlers"], [74, 22, 105, 5], [75, 8, 105, 5, "context"], [76, 6, 105, 5], [77, 6, 105, 5, "useAnimatedScrollHandlerTs1"], [77, 33, 105, 5], [77, 34, 105, 5, "__workletHash"], [77, 47, 105, 5], [78, 6, 105, 5, "useAnimatedScrollHandlerTs1"], [78, 33, 105, 5], [78, 34, 105, 5, "__initData"], [78, 44, 105, 5], [78, 47, 105, 5, "_worklet_14483949764600_init_data"], [78, 80, 105, 5], [79, 6, 105, 5, "useAnimatedScrollHandlerTs1"], [79, 33, 105, 5], [79, 34, 105, 5, "__stackDetails"], [79, 48, 105, 5], [79, 51, 105, 5, "_e"], [79, 53, 105, 5], [80, 6, 105, 5], [80, 13, 105, 5, "useAnimatedScrollHandlerTs1"], [80, 40, 105, 5], [81, 4, 105, 5], [81, 5, 79, 4], [81, 9, 106, 4, "subscribeForEvents"], [81, 27, 106, 22], [81, 29, 107, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [82, 4, 108, 4], [83, 4, 109, 4], [84, 4, 110, 2], [84, 5, 110, 3], [85, 2, 111, 0], [86, 0, 111, 1], [86, 3]], "functionMap": {"names": ["<global>", "useAnimatedScrollHandler", "useEvent$argument_0"], "mappings": "AAA;OCgD;IC8B;KD0B;CDM"}}, "type": "js/module"}]}