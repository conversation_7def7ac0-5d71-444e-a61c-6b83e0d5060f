{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 26, "index": 41}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 42}, "end": {"line": 4, "column": 56, "index": 98}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../TransitionProgressContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 100}, "end": {"line": 6, "column": 69, "index": 169}}], "key": "AqyNj8nTLfvNdJrg2KD+whMFl5o=", "exportNames": ["*"]}}, {"name": "./helpers/DelayedFreeze", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 170}, "end": {"line": 7, "column": 52, "index": 222}}], "key": "jGtOlnFq9CxanVSyHkha7XzCNd0=", "exportNames": ["*"]}}, {"name": "../core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 264}, "end": {"line": 14, "column": 17, "index": 354}}], "key": "OSA8xsmyvVLjxZOJ/QFvle2ua2I=", "exportNames": ["*"]}}, {"name": "../fabric/ScreenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 377}, "end": {"line": 19, "column": 41, "index": 495}}], "key": "wlomHWOusmSCcjJ/NHJjvgfe9zQ=", "exportNames": ["*"]}}, {"name": "../fabric/ModalScreenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 496}, "end": {"line": 22, "column": 46, "index": 629}}], "key": "sr6VxzitAwtEKr4JSQiS/5govc4=", "exportNames": ["*"]}}, {"name": "./helpers/usePrevious", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 631}, "end": {"line": 24, "column": 52, "index": 683}}], "key": "d6ouR14Kw4B/si9bUpR46j+mpHY=", "exportNames": ["*"]}}, {"name": "./helpers/edge-to-edge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 684}, "end": {"line": 25, "column": 80, "index": 764}}], "key": "mL3LRc1/0hIqBAJosobpJZ1fPWw=", "exportNames": ["*"]}}, {"name": "./helpers/sheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 765}, "end": {"line": 31, "column": 25, "index": 923}}], "key": "nu2KXxstkUld6s05YtUOnIOF430=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.ScreenContext = exports.InnerScreen = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _TransitionProgressContext = _interopRequireDefault(require(_dependencyMap[4], \"../TransitionProgressContext\"));\n  var _DelayedFreeze = _interopRequireDefault(require(_dependencyMap[5], \"./helpers/DelayedFreeze\"));\n  var _core = require(_dependencyMap[6], \"../core\");\n  var _ScreenNativeComponent = _interopRequireDefault(require(_dependencyMap[7], \"../fabric/ScreenNativeComponent\"));\n  var _ModalScreenNativeComponent = _interopRequireDefault(require(_dependencyMap[8], \"../fabric/ModalScreenNativeComponent\"));\n  var _usePrevious = require(_dependencyMap[9], \"./helpers/usePrevious\");\n  var _edgeToEdge = require(_dependencyMap[10], \"./helpers/edge-to-edge\");\n  var _sheet = require(_dependencyMap[11], \"./helpers/sheet\");\n  var _jsxDevRuntime = require(_dependencyMap[12], \"react/jsx-dev-runtime\");\n  var _excluded = [\"enabled\", \"freezeOnBlur\", \"shouldFreeze\"],\n    _excluded2 = [\"active\", \"activityState\", \"children\", \"isNativeStack\", \"gestureResponseDistance\", \"onGestureCancel\", \"style\"],\n    _excluded3 = [\"active\", \"activityState\", \"style\", \"onComponentRef\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-screens\\\\src\\\\components\\\\Screen.tsx\"; // Native components\n  var AnimatedNativeScreen = _reactNative.Animated.createAnimatedComponent(_ScreenNativeComponent.default);\n  var AnimatedNativeModalScreen = _reactNative.Animated.createAnimatedComponent(_ModalScreenNativeComponent.default);\n\n  // Incomplete type, all accessible properties available at:\n  // react-native/Libraries/Components/View/ReactNativeViewViewConfig.js\n\n  var InnerScreen = exports.InnerScreen = /*#__PURE__*/_react.default.forwardRef(function InnerScreen(props, ref) {\n    var innerRef = _react.default.useRef(null);\n    _react.default.useImperativeHandle(ref, () => innerRef.current, []);\n    var prevActivityState = (0, _usePrevious.usePrevious)(props.activityState);\n    var setRef = ref => {\n      innerRef.current = ref;\n      props.onComponentRef?.(ref);\n    };\n    var closing = _react.default.useRef(new _reactNative.Animated.Value(0)).current;\n    var progress = _react.default.useRef(new _reactNative.Animated.Value(0)).current;\n    var goingForward = _react.default.useRef(new _reactNative.Animated.Value(0)).current;\n    var _props$enabled = props.enabled,\n      enabled = _props$enabled === void 0 ? (0, _core.screensEnabled)() : _props$enabled,\n      _props$freezeOnBlur = props.freezeOnBlur,\n      freezeOnBlur = _props$freezeOnBlur === void 0 ? (0, _core.freezeEnabled)() : _props$freezeOnBlur,\n      shouldFreeze = props.shouldFreeze,\n      rest = (0, _objectWithoutProperties2.default)(props, _excluded);\n\n    // To maintain default behavior of formSheet stack presentation style and to have reasonable\n    // defaults for new medium-detent iOS API we need to set defaults here\n    var _rest$sheetAllowedDet = rest.sheetAllowedDetents,\n      sheetAllowedDetents = _rest$sheetAllowedDet === void 0 ? [1.0] : _rest$sheetAllowedDet,\n      _rest$sheetLargestUnd = rest.sheetLargestUndimmedDetentIndex,\n      sheetLargestUndimmedDetentIndex = _rest$sheetLargestUnd === void 0 ? _sheet.SHEET_DIMMED_ALWAYS : _rest$sheetLargestUnd,\n      _rest$sheetGrabberVis = rest.sheetGrabberVisible,\n      sheetGrabberVisible = _rest$sheetGrabberVis === void 0 ? false : _rest$sheetGrabberVis,\n      _rest$sheetCornerRadi = rest.sheetCornerRadius,\n      sheetCornerRadius = _rest$sheetCornerRadi === void 0 ? -1.0 : _rest$sheetCornerRadi,\n      _rest$sheetExpandsWhe = rest.sheetExpandsWhenScrolledToEdge,\n      sheetExpandsWhenScrolledToEdge = _rest$sheetExpandsWhe === void 0 ? true : _rest$sheetExpandsWhe,\n      _rest$sheetElevation = rest.sheetElevation,\n      sheetElevation = _rest$sheetElevation === void 0 ? 24 : _rest$sheetElevation,\n      _rest$sheetInitialDet = rest.sheetInitialDetentIndex,\n      sheetInitialDetentIndex = _rest$sheetInitialDet === void 0 ? 0 : _rest$sheetInitialDet,\n      stackPresentation = rest.stackPresentation,\n      onAppear = rest.onAppear,\n      onDisappear = rest.onDisappear,\n      onWillAppear = rest.onWillAppear,\n      onWillDisappear = rest.onWillDisappear;\n    if (enabled && _core.isNativePlatformSupported) {\n      var resolvedSheetAllowedDetents = (0, _sheet.resolveSheetAllowedDetents)(sheetAllowedDetents);\n      var resolvedSheetLargestUndimmedDetent = (0, _sheet.resolveSheetLargestUndimmedDetent)(sheetLargestUndimmedDetentIndex, resolvedSheetAllowedDetents.length - 1);\n      var resolvedSheetInitialDetentIndex = (0, _sheet.resolveSheetInitialDetentIndex)(sheetInitialDetentIndex, resolvedSheetAllowedDetents.length - 1);\n\n      // Due to how Yoga resolves layout, we need to have different components for modal nad non-modal screens (there is a need for different\n      // shadow nodes).\n      var shouldUseModalScreenComponent = _reactNative.Platform.select({\n        ios: !(stackPresentation === undefined || stackPresentation === 'push' || stackPresentation === 'containedModal' || stackPresentation === 'containedTransparentModal'),\n        android: false,\n        default: false\n      });\n      var AnimatedScreen = shouldUseModalScreenComponent ? AnimatedNativeModalScreen : AnimatedNativeScreen;\n      var active = rest.active,\n        activityState = rest.activityState,\n        children = rest.children,\n        isNativeStack = rest.isNativeStack,\n        gestureResponseDistance = rest.gestureResponseDistance,\n        onGestureCancel = rest.onGestureCancel,\n        style = rest.style,\n        _props = (0, _objectWithoutProperties2.default)(rest, _excluded2);\n      if (active !== undefined && activityState === undefined) {\n        console.warn('It appears that you are using old version of react-navigation library. Please update @react-navigation/bottom-tabs, @react-navigation/stack and @react-navigation/drawer to version 5.10.0 or above to take full advantage of new functionality added to react-native-screens');\n        activityState = active !== 0 ? 2 : 0; // in the new version, we need one of the screens to have value of 2 after the transition\n      }\n      if (isNativeStack && prevActivityState !== undefined && activityState !== undefined) {\n        if (prevActivityState > activityState) {\n          throw new Error('[RNScreens] activityState cannot be decreased in NativeStack');\n        }\n      }\n      var handleRef = ref => {\n        // Workaround is necessary to prevent React Native from hiding frozen screens.\n        // See this PR: https://github.com/grahammendick/navigation/pull/860\n        if (ref?.viewConfig?.validAttributes?.style) {\n          ref.viewConfig.validAttributes.style = {\n            ...ref.viewConfig.validAttributes.style,\n            display: null\n          };\n          setRef(ref);\n        } else if (ref?._viewConfig?.validAttributes?.style) {\n          ref._viewConfig.validAttributes.style = {\n            ...ref._viewConfig.validAttributes.style,\n            display: null\n          };\n          setRef(ref);\n        }\n      };\n      var freeze = freezeOnBlur && (shouldFreeze !== undefined ? shouldFreeze : activityState === 0);\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_DelayedFreeze.default, {\n        freeze: freeze,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(AnimatedScreen, {\n          ..._props,\n          /**\n           * This messy override is to conform NativeProps used by codegen and\n           * our Public API. To see reasoning go to this PR:\n           * https://github.com/software-mansion/react-native-screens/pull/2423#discussion_r1810616995\n           */\n          onAppear: onAppear,\n          onDisappear: onDisappear,\n          onWillAppear: onWillAppear,\n          onWillDisappear: onWillDisappear,\n          onGestureCancel: onGestureCancel ?? (() => {\n            // for internal use\n          })\n          //\n          // Hierarchy of screens is handled on the native side and setting zIndex value causes this issue:\n          // https://github.com/software-mansion/react-native-screens/issues/2345\n          // With below change of zIndex, we force RN diffing mechanism to NOT include detaching and attaching mutation in one transaction.\n          // Detailed information can be found here https://github.com/software-mansion/react-native-screens/pull/2351\n          ,\n          style: [style, {\n            zIndex: undefined\n          }],\n          activityState: activityState,\n          sheetAllowedDetents: resolvedSheetAllowedDetents,\n          sheetLargestUndimmedDetent: resolvedSheetLargestUndimmedDetent,\n          sheetElevation: sheetElevation,\n          sheetGrabberVisible: sheetGrabberVisible,\n          sheetCornerRadius: sheetCornerRadius,\n          sheetExpandsWhenScrolledToEdge: sheetExpandsWhenScrolledToEdge,\n          sheetInitialDetent: resolvedSheetInitialDetentIndex,\n          gestureResponseDistance: {\n            start: gestureResponseDistance?.start ?? -1,\n            end: gestureResponseDistance?.end ?? -1,\n            top: gestureResponseDistance?.top ?? -1,\n            bottom: gestureResponseDistance?.bottom ?? -1\n          }\n          // This prevents showing blank screen when navigating between multiple screens with freezing\n          // https://github.com/software-mansion/react-native-screens/pull/1208\n          ,\n          ref: handleRef,\n          onTransitionProgress: !isNativeStack ? undefined : _reactNative.Animated.event([{\n            nativeEvent: {\n              progress,\n              closing,\n              goingForward\n            }\n          }], {\n            useNativeDriver: true\n          }),\n          children: !isNativeStack ?\n          // see comment of this prop in types.tsx for information why it is needed\n          children : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TransitionProgressContext.default.Provider, {\n            value: {\n              progress,\n              closing,\n              goingForward\n            },\n            children: children\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this);\n    } else {\n      // same reason as above\n      var _active = rest.active,\n        _activityState = rest.activityState,\n        _style = rest.style,\n        onComponentRef = rest.onComponentRef,\n        _props2 = (0, _objectWithoutProperties2.default)(rest, _excluded3);\n      if (_active !== undefined && _activityState === undefined) {\n        _activityState = _active !== 0 ? 2 : 0;\n      }\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Animated.View, {\n        style: [_style, {\n          display: _activityState !== 0 ? 'flex' : 'none'\n        }],\n        ref: setRef,\n        ..._props2\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this);\n    }\n  });\n\n  // context to be used when the user wants to use enhanced implementation\n  // e.g. to use `useReanimatedTransitionProgress` (see `reanimated` folder in repo)\n  var ScreenContext = exports.ScreenContext = /*#__PURE__*/_react.default.createContext(InnerScreen);\n  var Screen = /*#__PURE__*/_react.default.forwardRef((props, ref) => {\n    var ScreenWrapper = _react.default.useContext(ScreenContext) || InnerScreen;\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ScreenWrapper, {\n      ...(_edgeToEdge.EDGE_TO_EDGE ? (0, _edgeToEdge.transformEdgeToEdgeProps)(props) : props),\n      ref: ref\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 5\n    }, this);\n  });\n  Screen.displayName = 'Screen';\n  var _default = exports.default = Screen;\n});", "lineCount": 234, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [8, 20, 1, 13, "exports"], [8, 27, 1, 13], [8, 28, 1, 13, "ScreenContext"], [8, 41, 1, 13], [8, 44, 1, 13, "exports"], [8, 51, 1, 13], [8, 52, 1, 13, "InnerScreen"], [8, 63, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_react"], [10, 12, 3, 0], [10, 15, 3, 0, "_interopRequireDefault"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_reactNative"], [11, 18, 4, 0], [11, 21, 4, 0, "require"], [11, 28, 4, 0], [11, 29, 4, 0, "_dependencyMap"], [11, 43, 4, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_TransitionProgressContext"], [12, 32, 6, 0], [12, 35, 6, 0, "_interopRequireDefault"], [12, 57, 6, 0], [12, 58, 6, 0, "require"], [12, 65, 6, 0], [12, 66, 6, 0, "_dependencyMap"], [12, 80, 6, 0], [13, 2, 7, 0], [13, 6, 7, 0, "_DelayedFreeze"], [13, 20, 7, 0], [13, 23, 7, 0, "_interopRequireDefault"], [13, 45, 7, 0], [13, 46, 7, 0, "require"], [13, 53, 7, 0], [13, 54, 7, 0, "_dependencyMap"], [13, 68, 7, 0], [14, 2, 10, 0], [14, 6, 10, 0, "_core"], [14, 11, 10, 0], [14, 14, 10, 0, "require"], [14, 21, 10, 0], [14, 22, 10, 0, "_dependencyMap"], [14, 36, 10, 0], [15, 2, 17, 0], [15, 6, 17, 0, "_ScreenNativeComponent"], [15, 28, 17, 0], [15, 31, 17, 0, "_interopRequireDefault"], [15, 53, 17, 0], [15, 54, 17, 0, "require"], [15, 61, 17, 0], [15, 62, 17, 0, "_dependencyMap"], [15, 76, 17, 0], [16, 2, 20, 0], [16, 6, 20, 0, "_ModalScreenNativeComponent"], [16, 33, 20, 0], [16, 36, 20, 0, "_interopRequireDefault"], [16, 58, 20, 0], [16, 59, 20, 0, "require"], [16, 66, 20, 0], [16, 67, 20, 0, "_dependencyMap"], [16, 81, 20, 0], [17, 2, 24, 0], [17, 6, 24, 0, "_usePrevious"], [17, 18, 24, 0], [17, 21, 24, 0, "require"], [17, 28, 24, 0], [17, 29, 24, 0, "_dependencyMap"], [17, 43, 24, 0], [18, 2, 25, 0], [18, 6, 25, 0, "_edgeToEdge"], [18, 17, 25, 0], [18, 20, 25, 0, "require"], [18, 27, 25, 0], [18, 28, 25, 0, "_dependencyMap"], [18, 42, 25, 0], [19, 2, 26, 0], [19, 6, 26, 0, "_sheet"], [19, 12, 26, 0], [19, 15, 26, 0, "require"], [19, 22, 26, 0], [19, 23, 26, 0, "_dependencyMap"], [19, 37, 26, 0], [20, 2, 31, 25], [20, 6, 31, 25, "_jsxDevRuntime"], [20, 20, 31, 25], [20, 23, 31, 25, "require"], [20, 30, 31, 25], [20, 31, 31, 25, "_dependencyMap"], [20, 45, 31, 25], [21, 2, 31, 25], [21, 6, 31, 25, "_excluded"], [21, 15, 31, 25], [22, 4, 31, 25, "_excluded2"], [22, 14, 31, 25], [23, 4, 31, 25, "_excluded3"], [23, 14, 31, 25], [24, 2, 31, 25], [24, 6, 31, 25, "_jsxFileName"], [24, 18, 31, 25], [24, 122, 16, 0], [25, 2, 34, 0], [25, 6, 34, 6, "AnimatedNativeScreen"], [25, 26, 34, 26], [25, 29, 34, 29, "Animated"], [25, 50, 34, 37], [25, 51, 34, 38, "createAnimatedComponent"], [25, 74, 34, 61], [25, 75, 35, 2, "ScreenNativeComponent"], [25, 105, 36, 0], [25, 106, 36, 1], [26, 2, 37, 0], [26, 6, 37, 6, "AnimatedNativeModalScreen"], [26, 31, 37, 31], [26, 34, 37, 34, "Animated"], [26, 55, 37, 42], [26, 56, 37, 43, "createAnimatedComponent"], [26, 79, 37, 66], [26, 80, 38, 2, "ModalScreenNativeComponent"], [26, 115, 39, 0], [26, 116, 39, 1], [28, 2, 41, 0], [29, 2, 42, 0], [31, 2, 60, 7], [31, 6, 60, 13, "InnerScreen"], [31, 17, 60, 24], [31, 20, 60, 24, "exports"], [31, 27, 60, 24], [31, 28, 60, 24, "InnerScreen"], [31, 39, 60, 24], [31, 55, 60, 27, "React"], [31, 69, 60, 32], [31, 70, 60, 33, "forwardRef"], [31, 80, 60, 43], [31, 81, 61, 2], [31, 90, 61, 11, "InnerScreen"], [31, 101, 61, 22, "InnerScreen"], [31, 102, 61, 23, "props"], [31, 107, 61, 28], [31, 109, 61, 30, "ref"], [31, 112, 61, 33], [31, 114, 61, 35], [32, 4, 62, 4], [32, 8, 62, 10, "innerRef"], [32, 16, 62, 18], [32, 19, 62, 21, "React"], [32, 33, 62, 26], [32, 34, 62, 27, "useRef"], [32, 40, 62, 33], [32, 41, 62, 53], [32, 45, 62, 57], [32, 46, 62, 58], [33, 4, 63, 4, "React"], [33, 18, 63, 9], [33, 19, 63, 10, "useImperativeHandle"], [33, 38, 63, 29], [33, 39, 63, 30, "ref"], [33, 42, 63, 33], [33, 44, 63, 35], [33, 50, 63, 41, "innerRef"], [33, 58, 63, 49], [33, 59, 63, 50, "current"], [33, 66, 63, 58], [33, 68, 63, 60], [33, 70, 63, 62], [33, 71, 63, 63], [34, 4, 64, 4], [34, 8, 64, 10, "prevActivityState"], [34, 25, 64, 27], [34, 28, 64, 30], [34, 32, 64, 30, "usePrevious"], [34, 56, 64, 41], [34, 58, 64, 42, "props"], [34, 63, 64, 47], [34, 64, 64, 48, "activityState"], [34, 77, 64, 61], [34, 78, 64, 62], [35, 4, 66, 4], [35, 8, 66, 10, "setRef"], [35, 14, 66, 16], [35, 17, 66, 20, "ref"], [35, 20, 66, 35], [35, 24, 66, 40], [36, 6, 67, 6, "innerRef"], [36, 14, 67, 14], [36, 15, 67, 15, "current"], [36, 22, 67, 22], [36, 25, 67, 25, "ref"], [36, 28, 67, 28], [37, 6, 68, 6, "props"], [37, 11, 68, 11], [37, 12, 68, 12, "onComponentRef"], [37, 26, 68, 26], [37, 29, 68, 29, "ref"], [37, 32, 68, 32], [37, 33, 68, 33], [38, 4, 69, 4], [38, 5, 69, 5], [39, 4, 71, 4], [39, 8, 71, 10, "closing"], [39, 15, 71, 17], [39, 18, 71, 20, "React"], [39, 32, 71, 25], [39, 33, 71, 26, "useRef"], [39, 39, 71, 32], [39, 40, 71, 33], [39, 44, 71, 37, "Animated"], [39, 65, 71, 45], [39, 66, 71, 46, "Value"], [39, 71, 71, 51], [39, 72, 71, 52], [39, 73, 71, 53], [39, 74, 71, 54], [39, 75, 71, 55], [39, 76, 71, 56, "current"], [39, 83, 71, 63], [40, 4, 72, 4], [40, 8, 72, 10, "progress"], [40, 16, 72, 18], [40, 19, 72, 21, "React"], [40, 33, 72, 26], [40, 34, 72, 27, "useRef"], [40, 40, 72, 33], [40, 41, 72, 34], [40, 45, 72, 38, "Animated"], [40, 66, 72, 46], [40, 67, 72, 47, "Value"], [40, 72, 72, 52], [40, 73, 72, 53], [40, 74, 72, 54], [40, 75, 72, 55], [40, 76, 72, 56], [40, 77, 72, 57, "current"], [40, 84, 72, 64], [41, 4, 73, 4], [41, 8, 73, 10, "goingForward"], [41, 20, 73, 22], [41, 23, 73, 25, "React"], [41, 37, 73, 30], [41, 38, 73, 31, "useRef"], [41, 44, 73, 37], [41, 45, 73, 38], [41, 49, 73, 42, "Animated"], [41, 70, 73, 50], [41, 71, 73, 51, "Value"], [41, 76, 73, 56], [41, 77, 73, 57], [41, 78, 73, 58], [41, 79, 73, 59], [41, 80, 73, 60], [41, 81, 73, 61, "current"], [41, 88, 73, 68], [42, 4, 75, 4], [42, 8, 75, 4, "_props$enabled"], [42, 22, 75, 4], [42, 25, 80, 8, "props"], [42, 30, 80, 13], [42, 31, 76, 6, "enabled"], [42, 38, 76, 13], [43, 6, 76, 6, "enabled"], [43, 13, 76, 13], [43, 16, 76, 13, "_props$enabled"], [43, 30, 76, 13], [43, 44, 76, 16], [43, 48, 76, 16, "screensEnabled"], [43, 68, 76, 30], [43, 70, 76, 31], [43, 71, 76, 32], [43, 74, 76, 32, "_props$enabled"], [43, 88, 76, 32], [44, 6, 76, 32, "_props$freezeOnBlur"], [44, 25, 76, 32], [44, 28, 80, 8, "props"], [44, 33, 80, 13], [44, 34, 77, 6, "freezeOnBlur"], [44, 46, 77, 18], [45, 6, 77, 6, "freezeOnBlur"], [45, 18, 77, 18], [45, 21, 77, 18, "_props$freezeOnBlur"], [45, 40, 77, 18], [45, 54, 77, 21], [45, 58, 77, 21, "freezeEnabled"], [45, 77, 77, 34], [45, 79, 77, 35], [45, 80, 77, 36], [45, 83, 77, 36, "_props$freezeOnBlur"], [45, 102, 77, 36], [46, 6, 78, 6, "shouldFreeze"], [46, 18, 78, 18], [46, 21, 80, 8, "props"], [46, 26, 80, 13], [46, 27, 78, 6, "shouldFreeze"], [46, 39, 78, 18], [47, 6, 79, 9, "rest"], [47, 10, 79, 13], [47, 17, 79, 13, "_objectWithoutProperties2"], [47, 42, 79, 13], [47, 43, 79, 13, "default"], [47, 50, 79, 13], [47, 52, 80, 8, "props"], [47, 57, 80, 13], [47, 59, 80, 13, "_excluded"], [47, 68, 80, 13], [49, 4, 82, 4], [50, 4, 83, 4], [51, 4, 84, 4], [51, 8, 84, 4, "_rest$sheetAllowedDet"], [51, 29, 84, 4], [51, 32, 100, 8, "rest"], [51, 36, 100, 12], [51, 37, 86, 6, "sheetAllowedDetents"], [51, 56, 86, 25], [52, 6, 86, 6, "sheetAllowedDetents"], [52, 25, 86, 25], [52, 28, 86, 25, "_rest$sheetAllowedDet"], [52, 49, 86, 25], [52, 63, 86, 28], [52, 64, 86, 29], [52, 67, 86, 32], [52, 68, 86, 33], [52, 71, 86, 33, "_rest$sheetAllowedDet"], [52, 92, 86, 33], [53, 6, 86, 33, "_rest$sheetLargestUnd"], [53, 27, 86, 33], [53, 30, 100, 8, "rest"], [53, 34, 100, 12], [53, 35, 87, 6, "sheetLargestUndimmedDetentIndex"], [53, 66, 87, 37], [54, 6, 87, 6, "sheetLargestUndimmedDetentIndex"], [54, 37, 87, 37], [54, 40, 87, 37, "_rest$sheetLargestUnd"], [54, 61, 87, 37], [54, 75, 87, 40, "SHEET_DIMMED_ALWAYS"], [54, 101, 87, 59], [54, 104, 87, 59, "_rest$sheetLargestUnd"], [54, 125, 87, 59], [55, 6, 87, 59, "_rest$sheetGrabberVis"], [55, 27, 87, 59], [55, 30, 100, 8, "rest"], [55, 34, 100, 12], [55, 35, 88, 6, "sheetGrabberVisible"], [55, 54, 88, 25], [56, 6, 88, 6, "sheetGrabberVisible"], [56, 25, 88, 25], [56, 28, 88, 25, "_rest$sheetGrabberVis"], [56, 49, 88, 25], [56, 63, 88, 28], [56, 68, 88, 33], [56, 71, 88, 33, "_rest$sheetGrabberVis"], [56, 92, 88, 33], [57, 6, 88, 33, "_rest$sheetCornerRadi"], [57, 27, 88, 33], [57, 30, 100, 8, "rest"], [57, 34, 100, 12], [57, 35, 89, 6, "sheetCornerRadius"], [57, 52, 89, 23], [58, 6, 89, 6, "sheetCornerRadius"], [58, 23, 89, 23], [58, 26, 89, 23, "_rest$sheetCornerRadi"], [58, 47, 89, 23], [58, 61, 89, 26], [58, 62, 89, 27], [58, 65, 89, 30], [58, 68, 89, 30, "_rest$sheetCornerRadi"], [58, 89, 89, 30], [59, 6, 89, 30, "_rest$sheetExpandsWhe"], [59, 27, 89, 30], [59, 30, 100, 8, "rest"], [59, 34, 100, 12], [59, 35, 90, 6, "sheetExpandsWhenScrolledToEdge"], [59, 65, 90, 36], [60, 6, 90, 6, "sheetExpandsWhenScrolledToEdge"], [60, 36, 90, 36], [60, 39, 90, 36, "_rest$sheetExpandsWhe"], [60, 60, 90, 36], [60, 74, 90, 39], [60, 78, 90, 43], [60, 81, 90, 43, "_rest$sheetExpandsWhe"], [60, 102, 90, 43], [61, 6, 90, 43, "_rest$sheetElevation"], [61, 26, 90, 43], [61, 29, 100, 8, "rest"], [61, 33, 100, 12], [61, 34, 91, 6, "sheetElevation"], [61, 48, 91, 20], [62, 6, 91, 6, "sheetElevation"], [62, 20, 91, 20], [62, 23, 91, 20, "_rest$sheetElevation"], [62, 43, 91, 20], [62, 57, 91, 23], [62, 59, 91, 25], [62, 62, 91, 25, "_rest$sheetElevation"], [62, 82, 91, 25], [63, 6, 91, 25, "_rest$sheetInitialDet"], [63, 27, 91, 25], [63, 30, 100, 8, "rest"], [63, 34, 100, 12], [63, 35, 92, 6, "sheetInitialDetentIndex"], [63, 58, 92, 29], [64, 6, 92, 6, "sheetInitialDetentIndex"], [64, 29, 92, 29], [64, 32, 92, 29, "_rest$sheetInitialDet"], [64, 53, 92, 29], [64, 67, 92, 32], [64, 68, 92, 33], [64, 71, 92, 33, "_rest$sheetInitialDet"], [64, 92, 92, 33], [65, 6, 94, 6, "stackPresentation"], [65, 23, 94, 23], [65, 26, 100, 8, "rest"], [65, 30, 100, 12], [65, 31, 94, 6, "stackPresentation"], [65, 48, 94, 23], [66, 6, 96, 6, "onAppear"], [66, 14, 96, 14], [66, 17, 100, 8, "rest"], [66, 21, 100, 12], [66, 22, 96, 6, "onAppear"], [66, 30, 96, 14], [67, 6, 97, 6, "onDisappear"], [67, 17, 97, 17], [67, 20, 100, 8, "rest"], [67, 24, 100, 12], [67, 25, 97, 6, "onDisappear"], [67, 36, 97, 17], [68, 6, 98, 6, "onWillAppear"], [68, 18, 98, 18], [68, 21, 100, 8, "rest"], [68, 25, 100, 12], [68, 26, 98, 6, "onWillAppear"], [68, 38, 98, 18], [69, 6, 99, 6, "onWillDisappear"], [69, 21, 99, 21], [69, 24, 100, 8, "rest"], [69, 28, 100, 12], [69, 29, 99, 6, "onWillDisappear"], [69, 44, 99, 21], [70, 4, 102, 4], [70, 8, 102, 8, "enabled"], [70, 15, 102, 15], [70, 19, 102, 19, "isNativePlatformSupported"], [70, 50, 102, 44], [70, 52, 102, 46], [71, 6, 103, 6], [71, 10, 103, 12, "resolvedSheetAllowedDetents"], [71, 37, 103, 39], [71, 40, 104, 8], [71, 44, 104, 8, "resolveSheetAllowedDetents"], [71, 77, 104, 34], [71, 79, 104, 35, "sheetAllowedDetents"], [71, 98, 104, 54], [71, 99, 104, 55], [72, 6, 105, 6], [72, 10, 105, 12, "resolvedSheetLargestUndimmedDetent"], [72, 44, 105, 46], [72, 47, 106, 8], [72, 51, 106, 8, "resolveSheetLargestUndimmedDetent"], [72, 91, 106, 41], [72, 93, 107, 10, "sheetLargestUndimmedDetentIndex"], [72, 124, 107, 41], [72, 126, 108, 10, "resolvedSheetAllowedDetents"], [72, 153, 108, 37], [72, 154, 108, 38, "length"], [72, 160, 108, 44], [72, 163, 108, 47], [72, 164, 109, 8], [72, 165, 109, 9], [73, 6, 110, 6], [73, 10, 110, 12, "resolvedSheetInitialDetentIndex"], [73, 41, 110, 43], [73, 44, 110, 46], [73, 48, 110, 46, "resolveSheetInitialDetentIndex"], [73, 85, 110, 76], [73, 87, 111, 8, "sheetInitialDetentIndex"], [73, 110, 111, 31], [73, 112, 112, 8, "resolvedSheetAllowedDetents"], [73, 139, 112, 35], [73, 140, 112, 36, "length"], [73, 146, 112, 42], [73, 149, 112, 45], [73, 150, 113, 6], [73, 151, 113, 7], [75, 6, 115, 6], [76, 6, 116, 6], [77, 6, 117, 6], [77, 10, 117, 12, "shouldUseModalScreenComponent"], [77, 39, 117, 41], [77, 42, 117, 44, "Platform"], [77, 63, 117, 52], [77, 64, 117, 53, "select"], [77, 70, 117, 59], [77, 71, 117, 60], [78, 8, 118, 8, "ios"], [78, 11, 118, 11], [78, 13, 118, 13], [78, 15, 119, 10, "stackPresentation"], [78, 32, 119, 27], [78, 37, 119, 32, "undefined"], [78, 46, 119, 41], [78, 50, 120, 10, "stackPresentation"], [78, 67, 120, 27], [78, 72, 120, 32], [78, 78, 120, 38], [78, 82, 121, 10, "stackPresentation"], [78, 99, 121, 27], [78, 104, 121, 32], [78, 120, 121, 48], [78, 124, 122, 10, "stackPresentation"], [78, 141, 122, 27], [78, 146, 122, 32], [78, 173, 122, 59], [78, 174, 123, 9], [79, 8, 124, 8, "android"], [79, 15, 124, 15], [79, 17, 124, 17], [79, 22, 124, 22], [80, 8, 125, 8, "default"], [80, 15, 125, 15], [80, 17, 125, 17], [81, 6, 126, 6], [81, 7, 126, 7], [81, 8, 126, 8], [82, 6, 128, 6], [82, 10, 128, 12, "AnimatedScreen"], [82, 24, 128, 26], [82, 27, 128, 29, "shouldUseModalScreenComponent"], [82, 56, 128, 58], [82, 59, 129, 10, "AnimatedNativeModalScreen"], [82, 84, 129, 35], [82, 87, 130, 10, "AnimatedNativeScreen"], [82, 107, 130, 30], [83, 6, 132, 6], [83, 10, 136, 8, "active"], [83, 16, 136, 14], [83, 19, 144, 10, "rest"], [83, 23, 144, 14], [83, 24, 136, 8, "active"], [83, 30, 136, 14], [84, 8, 137, 8, "activityState"], [84, 21, 137, 21], [84, 24, 144, 10, "rest"], [84, 28, 144, 14], [84, 29, 137, 8, "activityState"], [84, 42, 137, 21], [85, 8, 138, 8, "children"], [85, 16, 138, 16], [85, 19, 144, 10, "rest"], [85, 23, 144, 14], [85, 24, 138, 8, "children"], [85, 32, 138, 16], [86, 8, 139, 8, "isNativeStack"], [86, 21, 139, 21], [86, 24, 144, 10, "rest"], [86, 28, 144, 14], [86, 29, 139, 8, "isNativeStack"], [86, 42, 139, 21], [87, 8, 140, 8, "gestureResponseDistance"], [87, 31, 140, 31], [87, 34, 144, 10, "rest"], [87, 38, 144, 14], [87, 39, 140, 8, "gestureResponseDistance"], [87, 62, 140, 31], [88, 8, 141, 8, "onGestureCancel"], [88, 23, 141, 23], [88, 26, 144, 10, "rest"], [88, 30, 144, 14], [88, 31, 141, 8, "onGestureCancel"], [88, 46, 141, 23], [89, 8, 142, 8, "style"], [89, 13, 142, 13], [89, 16, 144, 10, "rest"], [89, 20, 144, 14], [89, 21, 142, 8, "style"], [89, 26, 142, 13], [90, 8, 143, 11, "props"], [90, 14, 143, 16], [90, 21, 143, 16, "_objectWithoutProperties2"], [90, 46, 143, 16], [90, 47, 143, 16, "default"], [90, 54, 143, 16], [90, 56, 144, 10, "rest"], [90, 60, 144, 14], [90, 62, 144, 14, "_excluded2"], [90, 72, 144, 14], [91, 6, 146, 6], [91, 10, 146, 10, "active"], [91, 16, 146, 16], [91, 21, 146, 21, "undefined"], [91, 30, 146, 30], [91, 34, 146, 34, "activityState"], [91, 47, 146, 47], [91, 52, 146, 52, "undefined"], [91, 61, 146, 61], [91, 63, 146, 63], [92, 8, 147, 8, "console"], [92, 15, 147, 15], [92, 16, 147, 16, "warn"], [92, 20, 147, 20], [92, 21, 148, 10], [92, 292, 149, 8], [92, 293, 149, 9], [93, 8, 150, 8, "activityState"], [93, 21, 150, 21], [93, 24, 150, 24, "active"], [93, 30, 150, 30], [93, 35, 150, 35], [93, 36, 150, 36], [93, 39, 150, 39], [93, 40, 150, 40], [93, 43, 150, 43], [93, 44, 150, 44], [93, 45, 150, 45], [93, 46, 150, 46], [94, 6, 151, 6], [95, 6, 153, 6], [95, 10, 154, 8, "isNativeStack"], [95, 23, 154, 21], [95, 27, 155, 8, "prevActivityState"], [95, 44, 155, 25], [95, 49, 155, 30, "undefined"], [95, 58, 155, 39], [95, 62, 156, 8, "activityState"], [95, 75, 156, 21], [95, 80, 156, 26, "undefined"], [95, 89, 156, 35], [95, 91, 157, 8], [96, 8, 158, 8], [96, 12, 158, 12, "prevActivityState"], [96, 29, 158, 29], [96, 32, 158, 32, "activityState"], [96, 45, 158, 45], [96, 47, 158, 47], [97, 10, 159, 10], [97, 16, 159, 16], [97, 20, 159, 20, "Error"], [97, 25, 159, 25], [97, 26, 160, 12], [97, 88, 161, 10], [97, 89, 161, 11], [98, 8, 162, 8], [99, 6, 163, 6], [100, 6, 165, 6], [100, 10, 165, 12, "handleRef"], [100, 19, 165, 21], [100, 22, 165, 25, "ref"], [100, 25, 165, 40], [100, 29, 165, 45], [101, 8, 166, 8], [102, 8, 167, 8], [103, 8, 168, 8], [103, 12, 168, 12, "ref"], [103, 15, 168, 15], [103, 17, 168, 17, "viewConfig"], [103, 27, 168, 27], [103, 29, 168, 29, "validAttributes"], [103, 44, 168, 44], [103, 46, 168, 46, "style"], [103, 51, 168, 51], [103, 53, 168, 53], [104, 10, 169, 10, "ref"], [104, 13, 169, 13], [104, 14, 169, 14, "viewConfig"], [104, 24, 169, 24], [104, 25, 169, 25, "validAttributes"], [104, 40, 169, 40], [104, 41, 169, 41, "style"], [104, 46, 169, 46], [104, 49, 169, 49], [105, 12, 170, 12], [105, 15, 170, 15, "ref"], [105, 18, 170, 18], [105, 19, 170, 19, "viewConfig"], [105, 29, 170, 29], [105, 30, 170, 30, "validAttributes"], [105, 45, 170, 45], [105, 46, 170, 46, "style"], [105, 51, 170, 51], [106, 12, 171, 12, "display"], [106, 19, 171, 19], [106, 21, 171, 21], [107, 10, 172, 10], [107, 11, 172, 11], [108, 10, 173, 10, "setRef"], [108, 16, 173, 16], [108, 17, 173, 17, "ref"], [108, 20, 173, 20], [108, 21, 173, 21], [109, 8, 174, 8], [109, 9, 174, 9], [109, 15, 174, 15], [109, 19, 174, 19, "ref"], [109, 22, 174, 22], [109, 24, 174, 24, "_viewConfig"], [109, 35, 174, 35], [109, 37, 174, 37, "validAttributes"], [109, 52, 174, 52], [109, 54, 174, 54, "style"], [109, 59, 174, 59], [109, 61, 174, 61], [110, 10, 175, 10, "ref"], [110, 13, 175, 13], [110, 14, 175, 14, "_viewConfig"], [110, 25, 175, 25], [110, 26, 175, 26, "validAttributes"], [110, 41, 175, 41], [110, 42, 175, 42, "style"], [110, 47, 175, 47], [110, 50, 175, 50], [111, 12, 176, 12], [111, 15, 176, 15, "ref"], [111, 18, 176, 18], [111, 19, 176, 19, "_viewConfig"], [111, 30, 176, 30], [111, 31, 176, 31, "validAttributes"], [111, 46, 176, 46], [111, 47, 176, 47, "style"], [111, 52, 176, 52], [112, 12, 177, 12, "display"], [112, 19, 177, 19], [112, 21, 177, 21], [113, 10, 178, 10], [113, 11, 178, 11], [114, 10, 179, 10, "setRef"], [114, 16, 179, 16], [114, 17, 179, 17, "ref"], [114, 20, 179, 20], [114, 21, 179, 21], [115, 8, 180, 8], [116, 6, 181, 6], [116, 7, 181, 7], [117, 6, 183, 6], [117, 10, 183, 12, "freeze"], [117, 16, 183, 18], [117, 19, 184, 8, "freezeOnBlur"], [117, 31, 184, 20], [117, 36, 185, 9, "shouldFreeze"], [117, 48, 185, 21], [117, 53, 185, 26, "undefined"], [117, 62, 185, 35], [117, 65, 185, 38, "shouldFreeze"], [117, 77, 185, 50], [117, 80, 185, 53, "activityState"], [117, 93, 185, 66], [117, 98, 185, 71], [117, 99, 185, 72], [117, 100, 185, 73], [118, 6, 187, 6], [118, 26, 188, 8], [118, 30, 188, 8, "_jsxDevRuntime"], [118, 44, 188, 8], [118, 45, 188, 8, "jsxDEV"], [118, 51, 188, 8], [118, 53, 188, 9, "_DelayedFreeze"], [118, 67, 188, 9], [118, 68, 188, 9, "default"], [118, 75, 188, 22], [119, 8, 188, 23, "freeze"], [119, 14, 188, 29], [119, 16, 188, 31, "freeze"], [119, 22, 188, 38], [120, 8, 188, 38, "children"], [120, 16, 188, 38], [120, 31, 189, 10], [120, 35, 189, 10, "_jsxDevRuntime"], [120, 49, 189, 10], [120, 50, 189, 10, "jsxDEV"], [120, 56, 189, 10], [120, 58, 189, 11, "AnimatedScreen"], [120, 72, 189, 25], [121, 10, 189, 25], [121, 13, 190, 16, "props"], [121, 19, 190, 21], [122, 10, 191, 12], [123, 0, 192, 0], [124, 0, 193, 0], [125, 0, 194, 0], [126, 0, 195, 0], [127, 10, 196, 12, "onAppear"], [127, 18, 196, 20], [127, 20, 196, 22, "onAppear"], [127, 28, 196, 58], [128, 10, 197, 12, "onDisappear"], [128, 21, 197, 23], [128, 23, 197, 25, "onDisappear"], [128, 34, 197, 67], [129, 10, 198, 12, "onWillAppear"], [129, 22, 198, 24], [129, 24, 198, 26, "onWillAppear"], [129, 36, 198, 70], [130, 10, 199, 12, "onWillDisappear"], [130, 25, 199, 27], [130, 27, 199, 29, "onWillDisappear"], [130, 42, 199, 79], [131, 10, 200, 12, "onGestureCancel"], [131, 25, 200, 27], [131, 27, 201, 15, "onGestureCancel"], [131, 42, 201, 30], [131, 47, 202, 15], [131, 53, 202, 21], [132, 12, 203, 16], [133, 10, 203, 16], [133, 11, 204, 15], [134, 10, 206, 12], [135, 10, 207, 12], [136, 10, 208, 12], [137, 10, 209, 12], [138, 10, 210, 12], [139, 10, 210, 12], [140, 10, 211, 12, "style"], [140, 15, 211, 17], [140, 17, 211, 19], [140, 18, 211, 20, "style"], [140, 23, 211, 25], [140, 25, 211, 27], [141, 12, 211, 29, "zIndex"], [141, 18, 211, 35], [141, 20, 211, 37, "undefined"], [142, 10, 211, 47], [142, 11, 211, 48], [142, 12, 211, 50], [143, 10, 212, 12, "activityState"], [143, 23, 212, 25], [143, 25, 212, 27, "activityState"], [143, 38, 212, 41], [144, 10, 213, 12, "sheetAllowedDetents"], [144, 29, 213, 31], [144, 31, 213, 33, "resolvedSheetAllowedDetents"], [144, 58, 213, 61], [145, 10, 214, 12, "sheetLargestUndimmedDetent"], [145, 36, 214, 38], [145, 38, 214, 40, "resolvedSheetLargestUndimmedDetent"], [145, 72, 214, 75], [146, 10, 215, 12, "sheetElevation"], [146, 24, 215, 26], [146, 26, 215, 28, "sheetElevation"], [146, 40, 215, 43], [147, 10, 216, 12, "sheetGrabberVisible"], [147, 29, 216, 31], [147, 31, 216, 33, "sheetGrabberVisible"], [147, 50, 216, 53], [148, 10, 217, 12, "sheetCornerRadius"], [148, 27, 217, 29], [148, 29, 217, 31, "sheetCornerRadius"], [148, 46, 217, 49], [149, 10, 218, 12, "sheetExpandsWhenScrolledToEdge"], [149, 40, 218, 42], [149, 42, 218, 44, "sheetExpandsWhenScrolledToEdge"], [149, 72, 218, 75], [150, 10, 219, 12, "sheetInitialDetent"], [150, 28, 219, 30], [150, 30, 219, 32, "resolvedSheetInitialDetentIndex"], [150, 61, 219, 64], [151, 10, 220, 12, "gestureResponseDistance"], [151, 33, 220, 35], [151, 35, 220, 37], [152, 12, 221, 14, "start"], [152, 17, 221, 19], [152, 19, 221, 21, "gestureResponseDistance"], [152, 42, 221, 44], [152, 44, 221, 46, "start"], [152, 49, 221, 51], [152, 53, 221, 55], [152, 54, 221, 56], [152, 55, 221, 57], [153, 12, 222, 14, "end"], [153, 15, 222, 17], [153, 17, 222, 19, "gestureResponseDistance"], [153, 40, 222, 42], [153, 42, 222, 44, "end"], [153, 45, 222, 47], [153, 49, 222, 51], [153, 50, 222, 52], [153, 51, 222, 53], [154, 12, 223, 14, "top"], [154, 15, 223, 17], [154, 17, 223, 19, "gestureResponseDistance"], [154, 40, 223, 42], [154, 42, 223, 44, "top"], [154, 45, 223, 47], [154, 49, 223, 51], [154, 50, 223, 52], [154, 51, 223, 53], [155, 12, 224, 14, "bottom"], [155, 18, 224, 20], [155, 20, 224, 22, "gestureResponseDistance"], [155, 43, 224, 45], [155, 45, 224, 47, "bottom"], [155, 51, 224, 53], [155, 55, 224, 57], [155, 56, 224, 58], [156, 10, 225, 12], [157, 10, 226, 12], [158, 10, 227, 12], [159, 10, 227, 12], [160, 10, 228, 12, "ref"], [160, 13, 228, 15], [160, 15, 228, 17, "handleRef"], [160, 24, 228, 27], [161, 10, 229, 12, "onTransitionProgress"], [161, 30, 229, 32], [161, 32, 230, 14], [161, 33, 230, 15, "isNativeStack"], [161, 46, 230, 28], [161, 49, 231, 18, "undefined"], [161, 58, 231, 27], [161, 61, 232, 18, "Animated"], [161, 82, 232, 26], [161, 83, 232, 27, "event"], [161, 88, 232, 32], [161, 89, 233, 20], [161, 90, 234, 22], [162, 12, 235, 24, "nativeEvent"], [162, 23, 235, 35], [162, 25, 235, 37], [163, 14, 236, 26, "progress"], [163, 22, 236, 34], [164, 14, 237, 26, "closing"], [164, 21, 237, 33], [165, 14, 238, 26, "goingForward"], [166, 12, 239, 24], [167, 10, 240, 22], [167, 11, 240, 23], [167, 12, 241, 21], [167, 14, 242, 20], [168, 12, 242, 22, "useNativeDriver"], [168, 27, 242, 37], [168, 29, 242, 39], [169, 10, 242, 44], [169, 11, 243, 18], [169, 12, 244, 13], [170, 10, 244, 13, "children"], [170, 18, 244, 13], [170, 20, 245, 13], [170, 21, 245, 14, "isNativeStack"], [170, 34, 245, 27], [171, 10, 245, 32], [172, 10, 246, 14, "children"], [172, 18, 246, 22], [172, 34, 248, 14], [172, 38, 248, 14, "_jsxDevRuntime"], [172, 52, 248, 14], [172, 53, 248, 14, "jsxDEV"], [172, 59, 248, 14], [172, 61, 248, 15, "_TransitionProgressContext"], [172, 87, 248, 15], [172, 88, 248, 15, "default"], [172, 95, 248, 40], [172, 96, 248, 41, "Provider"], [172, 104, 248, 49], [173, 12, 249, 16, "value"], [173, 17, 249, 21], [173, 19, 249, 23], [174, 14, 250, 18, "progress"], [174, 22, 250, 26], [175, 14, 251, 18, "closing"], [175, 21, 251, 25], [176, 14, 252, 18, "goingForward"], [177, 12, 253, 16], [177, 13, 253, 18], [178, 12, 253, 18, "children"], [178, 20, 253, 18], [178, 22, 254, 17, "children"], [179, 10, 254, 25], [180, 12, 254, 25, "fileName"], [180, 20, 254, 25], [180, 22, 254, 25, "_jsxFileName"], [180, 34, 254, 25], [181, 12, 254, 25, "lineNumber"], [181, 22, 254, 25], [182, 12, 254, 25, "columnNumber"], [182, 24, 254, 25], [183, 10, 254, 25], [183, 17, 255, 50], [184, 8, 256, 13], [185, 10, 256, 13, "fileName"], [185, 18, 256, 13], [185, 20, 256, 13, "_jsxFileName"], [185, 32, 256, 13], [186, 10, 256, 13, "lineNumber"], [186, 20, 256, 13], [187, 10, 256, 13, "columnNumber"], [187, 22, 256, 13], [188, 8, 256, 13], [188, 15, 257, 26], [189, 6, 257, 27], [190, 8, 257, 27, "fileName"], [190, 16, 257, 27], [190, 18, 257, 27, "_jsxFileName"], [190, 30, 257, 27], [191, 8, 257, 27, "lineNumber"], [191, 18, 257, 27], [192, 8, 257, 27, "columnNumber"], [192, 20, 257, 27], [193, 6, 257, 27], [193, 13, 258, 23], [193, 14, 258, 24], [194, 4, 260, 4], [194, 5, 260, 5], [194, 11, 260, 11], [195, 6, 261, 6], [196, 6, 262, 6], [196, 10, 263, 8, "active"], [196, 17, 263, 14], [196, 20, 269, 10, "rest"], [196, 24, 269, 14], [196, 25, 263, 8, "active"], [196, 31, 263, 14], [197, 8, 264, 8, "activityState"], [197, 22, 264, 21], [197, 25, 269, 10, "rest"], [197, 29, 269, 14], [197, 30, 264, 8, "activityState"], [197, 43, 264, 21], [198, 8, 265, 8, "style"], [198, 14, 265, 13], [198, 17, 269, 10, "rest"], [198, 21, 269, 14], [198, 22, 265, 8, "style"], [198, 27, 265, 13], [199, 8, 267, 8, "onComponentRef"], [199, 22, 267, 22], [199, 25, 269, 10, "rest"], [199, 29, 269, 14], [199, 30, 267, 8, "onComponentRef"], [199, 44, 267, 22], [200, 8, 268, 11, "props"], [200, 15, 268, 16], [200, 22, 268, 16, "_objectWithoutProperties2"], [200, 47, 268, 16], [200, 48, 268, 16, "default"], [200, 55, 268, 16], [200, 57, 269, 10, "rest"], [200, 61, 269, 14], [200, 63, 269, 14, "_excluded3"], [200, 73, 269, 14], [201, 6, 271, 6], [201, 10, 271, 10, "active"], [201, 17, 271, 16], [201, 22, 271, 21, "undefined"], [201, 31, 271, 30], [201, 35, 271, 34, "activityState"], [201, 49, 271, 47], [201, 54, 271, 52, "undefined"], [201, 63, 271, 61], [201, 65, 271, 63], [202, 8, 272, 8, "activityState"], [202, 22, 272, 21], [202, 25, 272, 24, "active"], [202, 32, 272, 30], [202, 37, 272, 35], [202, 38, 272, 36], [202, 41, 272, 39], [202, 42, 272, 40], [202, 45, 272, 43], [202, 46, 272, 44], [203, 6, 273, 6], [204, 6, 274, 6], [204, 26, 275, 8], [204, 30, 275, 8, "_jsxDevRuntime"], [204, 44, 275, 8], [204, 45, 275, 8, "jsxDEV"], [204, 51, 275, 8], [204, 53, 275, 9, "_reactNative"], [204, 65, 275, 9], [204, 66, 275, 9, "Animated"], [204, 74, 275, 17], [204, 75, 275, 18, "View"], [204, 79, 275, 22], [205, 8, 276, 10, "style"], [205, 13, 276, 15], [205, 15, 276, 17], [205, 16, 276, 18, "style"], [205, 22, 276, 23], [205, 24, 276, 25], [206, 10, 276, 27, "display"], [206, 17, 276, 34], [206, 19, 276, 36, "activityState"], [206, 33, 276, 49], [206, 38, 276, 54], [206, 39, 276, 55], [206, 42, 276, 58], [206, 48, 276, 64], [206, 51, 276, 67], [207, 8, 276, 74], [207, 9, 276, 75], [207, 10, 276, 77], [208, 8, 277, 10, "ref"], [208, 11, 277, 13], [208, 13, 277, 15, "setRef"], [208, 19, 277, 22], [209, 8, 277, 22], [209, 11, 278, 14, "props"], [210, 6, 278, 19], [211, 8, 278, 19, "fileName"], [211, 16, 278, 19], [211, 18, 278, 19, "_jsxFileName"], [211, 30, 278, 19], [212, 8, 278, 19, "lineNumber"], [212, 18, 278, 19], [213, 8, 278, 19, "columnNumber"], [213, 20, 278, 19], [214, 6, 278, 19], [214, 13, 279, 9], [214, 14, 279, 10], [215, 4, 281, 4], [216, 2, 282, 2], [216, 3, 283, 0], [216, 4, 283, 1], [218, 2, 285, 0], [219, 2, 286, 0], [220, 2, 287, 7], [220, 6, 287, 13, "ScreenContext"], [220, 19, 287, 26], [220, 22, 287, 26, "exports"], [220, 29, 287, 26], [220, 30, 287, 26, "ScreenContext"], [220, 43, 287, 26], [220, 59, 287, 29, "React"], [220, 73, 287, 34], [220, 74, 287, 35, "createContext"], [220, 87, 287, 48], [220, 88, 287, 49, "InnerScreen"], [220, 99, 287, 60], [220, 100, 287, 61], [221, 2, 289, 0], [221, 6, 289, 6, "Screen"], [221, 12, 289, 12], [221, 28, 289, 15, "React"], [221, 42, 289, 20], [221, 43, 289, 21, "forwardRef"], [221, 53, 289, 31], [221, 54, 289, 51], [221, 55, 289, 52, "props"], [221, 60, 289, 57], [221, 62, 289, 59, "ref"], [221, 65, 289, 62], [221, 70, 289, 67], [222, 4, 290, 2], [222, 8, 290, 8, "ScreenWrapper"], [222, 21, 290, 21], [222, 24, 290, 24, "React"], [222, 38, 290, 29], [222, 39, 290, 30, "useContext"], [222, 49, 290, 40], [222, 50, 290, 41, "ScreenContext"], [222, 63, 290, 54], [222, 64, 290, 55], [222, 68, 290, 59, "InnerScreen"], [222, 79, 290, 70], [223, 4, 292, 2], [223, 24, 293, 4], [223, 28, 293, 4, "_jsxDevRuntime"], [223, 42, 293, 4], [223, 43, 293, 4, "jsxDEV"], [223, 49, 293, 4], [223, 51, 293, 5, "ScreenWrapper"], [223, 64, 293, 18], [224, 6, 293, 18], [224, 10, 294, 11, "EDGE_TO_EDGE"], [224, 34, 294, 23], [224, 37, 294, 26], [224, 41, 294, 26, "transformEdgeToEdgeProps"], [224, 77, 294, 50], [224, 79, 294, 51, "props"], [224, 84, 294, 56], [224, 85, 294, 57], [224, 88, 294, 60, "props"], [224, 93, 294, 65], [225, 6, 295, 6, "ref"], [225, 9, 295, 9], [225, 11, 295, 11, "ref"], [226, 4, 295, 15], [227, 6, 295, 15, "fileName"], [227, 14, 295, 15], [227, 16, 295, 15, "_jsxFileName"], [227, 28, 295, 15], [228, 6, 295, 15, "lineNumber"], [228, 16, 295, 15], [229, 6, 295, 15, "columnNumber"], [229, 18, 295, 15], [230, 4, 295, 15], [230, 11, 296, 5], [230, 12, 296, 6], [231, 2, 298, 0], [231, 3, 298, 1], [231, 4, 298, 2], [232, 2, 300, 0, "Screen"], [232, 8, 300, 6], [232, 9, 300, 7, "displayName"], [232, 20, 300, 18], [232, 23, 300, 21], [232, 31, 300, 29], [233, 2, 300, 30], [233, 6, 300, 30, "_default"], [233, 14, 300, 30], [233, 17, 300, 30, "exports"], [233, 24, 300, 30], [233, 25, 300, 30, "default"], [233, 32, 300, 30], [233, 35, 302, 15, "Screen"], [233, 41, 302, 21], [234, 0, 302, 21], [234, 3]], "functionMap": {"names": ["<global>", "InnerScreen", "React.useImperativeHandle$argument_1", "setRef", "handleRef", "<anonymous>", "React.forwardRef$argument_0"], "mappings": "AAA;EC4D;mCCE,uBD;mBEG;KFG;wBGgG;OHgB;eIqB;eJE;GD8E;mDMO;CNS"}}, "type": "js/module"}]}