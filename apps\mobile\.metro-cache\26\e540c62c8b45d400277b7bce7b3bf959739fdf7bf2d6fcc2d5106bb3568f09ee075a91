{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 36, "index": 83}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 84}, "end": {"line": 5, "column": 48, "index": 132}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}, {"name": "react-native-screens", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 12, "index": 164}, "end": {"line": 8, "column": 43, "index": 195}}], "key": "qQ2YJW8DeYbhOj8Ni3d8gc/Gjng=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.MaybeScreenContainer = exports.MaybeScreen = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _jsxRuntime = require(_dependencyMap[4], \"react/jsx-runtime\");\n  var _excluded = [\"enabled\"],\n    _excluded2 = [\"enabled\", \"active\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var Screens;\n  try {\n    Screens = require(_dependencyMap[5], \"react-native-screens\");\n  } catch (e) {\n    // Ignore\n  }\n  var MaybeScreenContainer = _ref => {\n    var enabled = _ref.enabled,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    if (Screens != null) {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(Screens.ScreenContainer, {\n        enabled: enabled,\n        ...rest\n      });\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n      ...rest\n    });\n  };\n  exports.MaybeScreenContainer = MaybeScreenContainer;\n  var MaybeScreen = _ref2 => {\n    var enabled = _ref2.enabled,\n      active = _ref2.active,\n      rest = (0, _objectWithoutProperties2.default)(_ref2, _excluded2);\n    if (Screens != null) {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(Screens.Screen, {\n        enabled: enabled,\n        activityState: active,\n        ...rest\n      });\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n      ...rest\n    });\n  };\n  exports.MaybeScreen = MaybeScreen;\n});", "lineCount": 52, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "MaybeScreenContainer"], [8, 30, 1, 13], [8, 33, 1, 13, "exports"], [8, 40, 1, 13], [8, 41, 1, 13, "MaybeScreen"], [8, 52, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "React"], [10, 11, 3, 0], [10, 14, 3, 0, "_interopRequireWildcard"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_reactNative"], [11, 18, 4, 0], [11, 21, 4, 0, "require"], [11, 28, 4, 0], [11, 29, 4, 0, "_dependencyMap"], [11, 43, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_jsxRuntime"], [12, 17, 5, 0], [12, 20, 5, 0, "require"], [12, 27, 5, 0], [12, 28, 5, 0, "_dependencyMap"], [12, 42, 5, 0], [13, 2, 5, 48], [13, 6, 5, 48, "_excluded"], [13, 15, 5, 48], [14, 4, 5, 48, "_excluded2"], [14, 14, 5, 48], [15, 2, 5, 48], [15, 11, 5, 48, "_interopRequireWildcard"], [15, 35, 5, 48, "e"], [15, 36, 5, 48], [15, 38, 5, 48, "t"], [15, 39, 5, 48], [15, 68, 5, 48, "WeakMap"], [15, 75, 5, 48], [15, 81, 5, 48, "r"], [15, 82, 5, 48], [15, 89, 5, 48, "WeakMap"], [15, 96, 5, 48], [15, 100, 5, 48, "n"], [15, 101, 5, 48], [15, 108, 5, 48, "WeakMap"], [15, 115, 5, 48], [15, 127, 5, 48, "_interopRequireWildcard"], [15, 150, 5, 48], [15, 162, 5, 48, "_interopRequireWildcard"], [15, 163, 5, 48, "e"], [15, 164, 5, 48], [15, 166, 5, 48, "t"], [15, 167, 5, 48], [15, 176, 5, 48, "t"], [15, 177, 5, 48], [15, 181, 5, 48, "e"], [15, 182, 5, 48], [15, 186, 5, 48, "e"], [15, 187, 5, 48], [15, 188, 5, 48, "__esModule"], [15, 198, 5, 48], [15, 207, 5, 48, "e"], [15, 208, 5, 48], [15, 214, 5, 48, "o"], [15, 215, 5, 48], [15, 217, 5, 48, "i"], [15, 218, 5, 48], [15, 220, 5, 48, "f"], [15, 221, 5, 48], [15, 226, 5, 48, "__proto__"], [15, 235, 5, 48], [15, 243, 5, 48, "default"], [15, 250, 5, 48], [15, 252, 5, 48, "e"], [15, 253, 5, 48], [15, 270, 5, 48, "e"], [15, 271, 5, 48], [15, 294, 5, 48, "e"], [15, 295, 5, 48], [15, 320, 5, 48, "e"], [15, 321, 5, 48], [15, 330, 5, 48, "f"], [15, 331, 5, 48], [15, 337, 5, 48, "o"], [15, 338, 5, 48], [15, 341, 5, 48, "t"], [15, 342, 5, 48], [15, 345, 5, 48, "n"], [15, 346, 5, 48], [15, 349, 5, 48, "r"], [15, 350, 5, 48], [15, 358, 5, 48, "o"], [15, 359, 5, 48], [15, 360, 5, 48, "has"], [15, 363, 5, 48], [15, 364, 5, 48, "e"], [15, 365, 5, 48], [15, 375, 5, 48, "o"], [15, 376, 5, 48], [15, 377, 5, 48, "get"], [15, 380, 5, 48], [15, 381, 5, 48, "e"], [15, 382, 5, 48], [15, 385, 5, 48, "o"], [15, 386, 5, 48], [15, 387, 5, 48, "set"], [15, 390, 5, 48], [15, 391, 5, 48, "e"], [15, 392, 5, 48], [15, 394, 5, 48, "f"], [15, 395, 5, 48], [15, 409, 5, 48, "_t"], [15, 411, 5, 48], [15, 415, 5, 48, "e"], [15, 416, 5, 48], [15, 432, 5, 48, "_t"], [15, 434, 5, 48], [15, 441, 5, 48, "hasOwnProperty"], [15, 455, 5, 48], [15, 456, 5, 48, "call"], [15, 460, 5, 48], [15, 461, 5, 48, "e"], [15, 462, 5, 48], [15, 464, 5, 48, "_t"], [15, 466, 5, 48], [15, 473, 5, 48, "i"], [15, 474, 5, 48], [15, 478, 5, 48, "o"], [15, 479, 5, 48], [15, 482, 5, 48, "Object"], [15, 488, 5, 48], [15, 489, 5, 48, "defineProperty"], [15, 503, 5, 48], [15, 508, 5, 48, "Object"], [15, 514, 5, 48], [15, 515, 5, 48, "getOwnPropertyDescriptor"], [15, 539, 5, 48], [15, 540, 5, 48, "e"], [15, 541, 5, 48], [15, 543, 5, 48, "_t"], [15, 545, 5, 48], [15, 552, 5, 48, "i"], [15, 553, 5, 48], [15, 554, 5, 48, "get"], [15, 557, 5, 48], [15, 561, 5, 48, "i"], [15, 562, 5, 48], [15, 563, 5, 48, "set"], [15, 566, 5, 48], [15, 570, 5, 48, "o"], [15, 571, 5, 48], [15, 572, 5, 48, "f"], [15, 573, 5, 48], [15, 575, 5, 48, "_t"], [15, 577, 5, 48], [15, 579, 5, 48, "i"], [15, 580, 5, 48], [15, 584, 5, 48, "f"], [15, 585, 5, 48], [15, 586, 5, 48, "_t"], [15, 588, 5, 48], [15, 592, 5, 48, "e"], [15, 593, 5, 48], [15, 594, 5, 48, "_t"], [15, 596, 5, 48], [15, 607, 5, 48, "f"], [15, 608, 5, 48], [15, 613, 5, 48, "e"], [15, 614, 5, 48], [15, 616, 5, 48, "t"], [15, 617, 5, 48], [16, 2, 6, 0], [16, 6, 6, 4, "Screens"], [16, 13, 6, 11], [17, 2, 7, 0], [17, 6, 7, 4], [18, 4, 8, 2, "Screens"], [18, 11, 8, 9], [18, 14, 8, 12, "require"], [18, 21, 8, 19], [18, 22, 8, 19, "_dependencyMap"], [18, 36, 8, 19], [18, 63, 8, 42], [18, 64, 8, 43], [19, 2, 9, 0], [19, 3, 9, 1], [19, 4, 9, 2], [19, 11, 9, 9, "e"], [19, 12, 9, 10], [19, 14, 9, 12], [20, 4, 10, 2], [21, 2, 10, 2], [22, 2, 12, 7], [22, 6, 12, 13, "MaybeScreenContainer"], [22, 26, 12, 33], [22, 29, 12, 36, "_ref"], [22, 33, 12, 36], [22, 37, 15, 6], [23, 4, 15, 6], [23, 8, 13, 2, "enabled"], [23, 15, 13, 9], [23, 18, 13, 9, "_ref"], [23, 22, 13, 9], [23, 23, 13, 2, "enabled"], [23, 30, 13, 9], [24, 6, 14, 5, "rest"], [24, 10, 14, 9], [24, 17, 14, 9, "_objectWithoutProperties2"], [24, 42, 14, 9], [24, 43, 14, 9, "default"], [24, 50, 14, 9], [24, 52, 14, 9, "_ref"], [24, 56, 14, 9], [24, 58, 14, 9, "_excluded"], [24, 67, 14, 9], [25, 4, 16, 2], [25, 8, 16, 6, "Screens"], [25, 15, 16, 13], [25, 19, 16, 17], [25, 23, 16, 21], [25, 25, 16, 23], [26, 6, 17, 4], [26, 13, 17, 11], [26, 26, 17, 24], [26, 30, 17, 24, "_jsx"], [26, 45, 17, 28], [26, 47, 17, 29, "Screens"], [26, 54, 17, 36], [26, 55, 17, 37, "ScreenContainer"], [26, 70, 17, 52], [26, 72, 17, 54], [27, 8, 18, 6, "enabled"], [27, 15, 18, 13], [27, 17, 18, 15, "enabled"], [27, 24, 18, 22], [28, 8, 19, 6], [28, 11, 19, 9, "rest"], [29, 6, 20, 4], [29, 7, 20, 5], [29, 8, 20, 6], [30, 4, 21, 2], [31, 4, 22, 2], [31, 11, 22, 9], [31, 24, 22, 22], [31, 28, 22, 22, "_jsx"], [31, 43, 22, 26], [31, 45, 22, 27, "View"], [31, 62, 22, 31], [31, 64, 22, 33], [32, 6, 23, 4], [32, 9, 23, 7, "rest"], [33, 4, 24, 2], [33, 5, 24, 3], [33, 6, 24, 4], [34, 2, 25, 0], [34, 3, 25, 1], [35, 2, 25, 2, "exports"], [35, 9, 25, 2], [35, 10, 25, 2, "MaybeScreenContainer"], [35, 30, 25, 2], [35, 33, 25, 2, "MaybeScreenContainer"], [35, 53, 25, 2], [36, 2, 26, 7], [36, 6, 26, 13, "MaybeScreen"], [36, 17, 26, 24], [36, 20, 26, 27, "_ref2"], [36, 25, 26, 27], [36, 29, 30, 6], [37, 4, 30, 6], [37, 8, 27, 2, "enabled"], [37, 15, 27, 9], [37, 18, 27, 9, "_ref2"], [37, 23, 27, 9], [37, 24, 27, 2, "enabled"], [37, 31, 27, 9], [38, 6, 28, 2, "active"], [38, 12, 28, 8], [38, 15, 28, 8, "_ref2"], [38, 20, 28, 8], [38, 21, 28, 2, "active"], [38, 27, 28, 8], [39, 6, 29, 5, "rest"], [39, 10, 29, 9], [39, 17, 29, 9, "_objectWithoutProperties2"], [39, 42, 29, 9], [39, 43, 29, 9, "default"], [39, 50, 29, 9], [39, 52, 29, 9, "_ref2"], [39, 57, 29, 9], [39, 59, 29, 9, "_excluded2"], [39, 69, 29, 9], [40, 4, 31, 2], [40, 8, 31, 6, "Screens"], [40, 15, 31, 13], [40, 19, 31, 17], [40, 23, 31, 21], [40, 25, 31, 23], [41, 6, 32, 4], [41, 13, 32, 11], [41, 26, 32, 24], [41, 30, 32, 24, "_jsx"], [41, 45, 32, 28], [41, 47, 32, 29, "Screens"], [41, 54, 32, 36], [41, 55, 32, 37, "Screen"], [41, 61, 32, 43], [41, 63, 32, 45], [42, 8, 33, 6, "enabled"], [42, 15, 33, 13], [42, 17, 33, 15, "enabled"], [42, 24, 33, 22], [43, 8, 34, 6, "activityState"], [43, 21, 34, 19], [43, 23, 34, 21, "active"], [43, 29, 34, 27], [44, 8, 35, 6], [44, 11, 35, 9, "rest"], [45, 6, 36, 4], [45, 7, 36, 5], [45, 8, 36, 6], [46, 4, 37, 2], [47, 4, 38, 2], [47, 11, 38, 9], [47, 24, 38, 22], [47, 28, 38, 22, "_jsx"], [47, 43, 38, 26], [47, 45, 38, 27, "View"], [47, 62, 38, 31], [47, 64, 38, 33], [48, 6, 39, 4], [48, 9, 39, 7, "rest"], [49, 4, 40, 2], [49, 5, 40, 3], [49, 6, 40, 4], [50, 2, 41, 0], [50, 3, 41, 1], [51, 2, 41, 2, "exports"], [51, 9, 41, 2], [51, 10, 41, 2, "MaybeScreen"], [51, 21, 41, 2], [51, 24, 41, 2, "MaybeScreen"], [51, 35, 41, 2], [52, 0, 41, 2], [52, 3]], "functionMap": {"names": ["<global>", "MaybeScreenContainer", "MaybeScreen"], "mappings": "AAA;oCCW;CDa;2BEC;CFe"}}, "type": "js/module"}]}