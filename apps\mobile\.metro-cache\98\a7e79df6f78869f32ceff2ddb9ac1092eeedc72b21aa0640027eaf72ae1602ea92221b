{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _View = _interopRequireDefault(require(_dependencyMap[1], \"../../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[2], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"../../Text/Text\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[4], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[5], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[6], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxInspectorSection.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function LogBoxInspectorSection(props) {\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.section,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.heading,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.headingText,\n          children: props.heading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 9\n        }, this), props.action]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.body,\n        children: props.children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _StyleSheet.default.create({\n    section: {\n      marginTop: 15\n    },\n    heading: {\n      alignItems: 'center',\n      flexDirection: 'row',\n      paddingHorizontal: 12,\n      marginBottom: 10\n    },\n    headingText: {\n      color: LogBoxStyle.getTextColor(1),\n      flex: 1,\n      fontSize: 18,\n      fontWeight: '600',\n      includeFontPadding: false,\n      lineHeight: 20\n    },\n    body: {\n      paddingBottom: 10\n    }\n  });\n  var _default = exports.default = LogBoxInspectorSection;\n});", "lineCount": 69, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_View"], [7, 11, 11, 0], [7, 14, 11, 0, "_interopRequireDefault"], [7, 36, 11, 0], [7, 37, 11, 0, "require"], [7, 44, 11, 0], [7, 45, 11, 0, "_dependencyMap"], [7, 59, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_StyleSheet"], [8, 17, 12, 0], [8, 20, 12, 0, "_interopRequireDefault"], [8, 42, 12, 0], [8, 43, 12, 0, "require"], [8, 50, 12, 0], [8, 51, 12, 0, "_dependencyMap"], [8, 65, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_Text"], [9, 11, 13, 0], [9, 14, 13, 0, "_interopRequireDefault"], [9, 36, 13, 0], [9, 37, 13, 0, "require"], [9, 44, 13, 0], [9, 45, 13, 0, "_dependencyMap"], [9, 59, 13, 0], [10, 2, 14, 0], [10, 6, 14, 0, "LogBoxStyle"], [10, 17, 14, 0], [10, 20, 14, 0, "_interopRequireWildcard"], [10, 43, 14, 0], [10, 44, 14, 0, "require"], [10, 51, 14, 0], [10, 52, 14, 0, "_dependencyMap"], [10, 66, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "React"], [11, 11, 15, 0], [11, 14, 15, 0, "_interopRequireWildcard"], [11, 37, 15, 0], [11, 38, 15, 0, "require"], [11, 45, 15, 0], [11, 46, 15, 0, "_dependencyMap"], [11, 60, 15, 0], [12, 2, 15, 31], [12, 6, 15, 31, "_jsxDevRuntime"], [12, 20, 15, 31], [12, 23, 15, 31, "require"], [12, 30, 15, 31], [12, 31, 15, 31, "_dependencyMap"], [12, 45, 15, 31], [13, 2, 15, 31], [13, 6, 15, 31, "_jsxFileName"], [13, 18, 15, 31], [14, 2, 15, 31], [14, 11, 15, 31, "_interopRequireWildcard"], [14, 35, 15, 31, "e"], [14, 36, 15, 31], [14, 38, 15, 31, "t"], [14, 39, 15, 31], [14, 68, 15, 31, "WeakMap"], [14, 75, 15, 31], [14, 81, 15, 31, "r"], [14, 82, 15, 31], [14, 89, 15, 31, "WeakMap"], [14, 96, 15, 31], [14, 100, 15, 31, "n"], [14, 101, 15, 31], [14, 108, 15, 31, "WeakMap"], [14, 115, 15, 31], [14, 127, 15, 31, "_interopRequireWildcard"], [14, 150, 15, 31], [14, 162, 15, 31, "_interopRequireWildcard"], [14, 163, 15, 31, "e"], [14, 164, 15, 31], [14, 166, 15, 31, "t"], [14, 167, 15, 31], [14, 176, 15, 31, "t"], [14, 177, 15, 31], [14, 181, 15, 31, "e"], [14, 182, 15, 31], [14, 186, 15, 31, "e"], [14, 187, 15, 31], [14, 188, 15, 31, "__esModule"], [14, 198, 15, 31], [14, 207, 15, 31, "e"], [14, 208, 15, 31], [14, 214, 15, 31, "o"], [14, 215, 15, 31], [14, 217, 15, 31, "i"], [14, 218, 15, 31], [14, 220, 15, 31, "f"], [14, 221, 15, 31], [14, 226, 15, 31, "__proto__"], [14, 235, 15, 31], [14, 243, 15, 31, "default"], [14, 250, 15, 31], [14, 252, 15, 31, "e"], [14, 253, 15, 31], [14, 270, 15, 31, "e"], [14, 271, 15, 31], [14, 294, 15, 31, "e"], [14, 295, 15, 31], [14, 320, 15, 31, "e"], [14, 321, 15, 31], [14, 330, 15, 31, "f"], [14, 331, 15, 31], [14, 337, 15, 31, "o"], [14, 338, 15, 31], [14, 341, 15, 31, "t"], [14, 342, 15, 31], [14, 345, 15, 31, "n"], [14, 346, 15, 31], [14, 349, 15, 31, "r"], [14, 350, 15, 31], [14, 358, 15, 31, "o"], [14, 359, 15, 31], [14, 360, 15, 31, "has"], [14, 363, 15, 31], [14, 364, 15, 31, "e"], [14, 365, 15, 31], [14, 375, 15, 31, "o"], [14, 376, 15, 31], [14, 377, 15, 31, "get"], [14, 380, 15, 31], [14, 381, 15, 31, "e"], [14, 382, 15, 31], [14, 385, 15, 31, "o"], [14, 386, 15, 31], [14, 387, 15, 31, "set"], [14, 390, 15, 31], [14, 391, 15, 31, "e"], [14, 392, 15, 31], [14, 394, 15, 31, "f"], [14, 395, 15, 31], [14, 409, 15, 31, "_t"], [14, 411, 15, 31], [14, 415, 15, 31, "e"], [14, 416, 15, 31], [14, 432, 15, 31, "_t"], [14, 434, 15, 31], [14, 441, 15, 31, "hasOwnProperty"], [14, 455, 15, 31], [14, 456, 15, 31, "call"], [14, 460, 15, 31], [14, 461, 15, 31, "e"], [14, 462, 15, 31], [14, 464, 15, 31, "_t"], [14, 466, 15, 31], [14, 473, 15, 31, "i"], [14, 474, 15, 31], [14, 478, 15, 31, "o"], [14, 479, 15, 31], [14, 482, 15, 31, "Object"], [14, 488, 15, 31], [14, 489, 15, 31, "defineProperty"], [14, 503, 15, 31], [14, 508, 15, 31, "Object"], [14, 514, 15, 31], [14, 515, 15, 31, "getOwnPropertyDescriptor"], [14, 539, 15, 31], [14, 540, 15, 31, "e"], [14, 541, 15, 31], [14, 543, 15, 31, "_t"], [14, 545, 15, 31], [14, 552, 15, 31, "i"], [14, 553, 15, 31], [14, 554, 15, 31, "get"], [14, 557, 15, 31], [14, 561, 15, 31, "i"], [14, 562, 15, 31], [14, 563, 15, 31, "set"], [14, 566, 15, 31], [14, 570, 15, 31, "o"], [14, 571, 15, 31], [14, 572, 15, 31, "f"], [14, 573, 15, 31], [14, 575, 15, 31, "_t"], [14, 577, 15, 31], [14, 579, 15, 31, "i"], [14, 580, 15, 31], [14, 584, 15, 31, "f"], [14, 585, 15, 31], [14, 586, 15, 31, "_t"], [14, 588, 15, 31], [14, 592, 15, 31, "e"], [14, 593, 15, 31], [14, 594, 15, 31, "_t"], [14, 596, 15, 31], [14, 607, 15, 31, "f"], [14, 608, 15, 31], [14, 613, 15, 31, "e"], [14, 614, 15, 31], [14, 616, 15, 31, "t"], [14, 617, 15, 31], [15, 2, 23, 0], [15, 11, 23, 9, "LogBoxInspectorSection"], [15, 33, 23, 31, "LogBoxInspectorSection"], [15, 34, 23, 32, "props"], [15, 39, 23, 44], [15, 41, 23, 58], [16, 4, 24, 2], [16, 24, 25, 4], [16, 28, 25, 4, "_jsxDevRuntime"], [16, 42, 25, 4], [16, 43, 25, 4, "jsxDEV"], [16, 49, 25, 4], [16, 51, 25, 5, "_View"], [16, 56, 25, 5], [16, 57, 25, 5, "default"], [16, 64, 25, 9], [17, 6, 25, 10, "style"], [17, 11, 25, 15], [17, 13, 25, 17, "styles"], [17, 19, 25, 23], [17, 20, 25, 24, "section"], [17, 27, 25, 32], [18, 6, 25, 32, "children"], [18, 14, 25, 32], [18, 30, 26, 6], [18, 34, 26, 6, "_jsxDevRuntime"], [18, 48, 26, 6], [18, 49, 26, 6, "jsxDEV"], [18, 55, 26, 6], [18, 57, 26, 7, "_View"], [18, 62, 26, 7], [18, 63, 26, 7, "default"], [18, 70, 26, 11], [19, 8, 26, 12, "style"], [19, 13, 26, 17], [19, 15, 26, 19, "styles"], [19, 21, 26, 25], [19, 22, 26, 26, "heading"], [19, 29, 26, 34], [20, 8, 26, 34, "children"], [20, 16, 26, 34], [20, 32, 27, 8], [20, 36, 27, 8, "_jsxDevRuntime"], [20, 50, 27, 8], [20, 51, 27, 8, "jsxDEV"], [20, 57, 27, 8], [20, 59, 27, 9, "_Text"], [20, 64, 27, 9], [20, 65, 27, 9, "default"], [20, 72, 27, 13], [21, 10, 27, 14, "style"], [21, 15, 27, 19], [21, 17, 27, 21, "styles"], [21, 23, 27, 27], [21, 24, 27, 28, "headingText"], [21, 35, 27, 40], [22, 10, 27, 40, "children"], [22, 18, 27, 40], [22, 20, 27, 42, "props"], [22, 25, 27, 47], [22, 26, 27, 48, "heading"], [23, 8, 27, 55], [24, 10, 27, 55, "fileName"], [24, 18, 27, 55], [24, 20, 27, 55, "_jsxFileName"], [24, 32, 27, 55], [25, 10, 27, 55, "lineNumber"], [25, 20, 27, 55], [26, 10, 27, 55, "columnNumber"], [26, 22, 27, 55], [27, 8, 27, 55], [27, 15, 27, 62], [27, 16, 27, 63], [27, 18, 28, 9, "props"], [27, 23, 28, 14], [27, 24, 28, 15, "action"], [27, 30, 28, 21], [28, 6, 28, 21], [29, 8, 28, 21, "fileName"], [29, 16, 28, 21], [29, 18, 28, 21, "_jsxFileName"], [29, 30, 28, 21], [30, 8, 28, 21, "lineNumber"], [30, 18, 28, 21], [31, 8, 28, 21, "columnNumber"], [31, 20, 28, 21], [32, 6, 28, 21], [32, 13, 29, 12], [32, 14, 29, 13], [32, 29, 30, 6], [32, 33, 30, 6, "_jsxDevRuntime"], [32, 47, 30, 6], [32, 48, 30, 6, "jsxDEV"], [32, 54, 30, 6], [32, 56, 30, 7, "_View"], [32, 61, 30, 7], [32, 62, 30, 7, "default"], [32, 69, 30, 11], [33, 8, 30, 12, "style"], [33, 13, 30, 17], [33, 15, 30, 19, "styles"], [33, 21, 30, 25], [33, 22, 30, 26, "body"], [33, 26, 30, 31], [34, 8, 30, 31, "children"], [34, 16, 30, 31], [34, 18, 30, 33, "props"], [34, 23, 30, 38], [34, 24, 30, 39, "children"], [35, 6, 30, 47], [36, 8, 30, 47, "fileName"], [36, 16, 30, 47], [36, 18, 30, 47, "_jsxFileName"], [36, 30, 30, 47], [37, 8, 30, 47, "lineNumber"], [37, 18, 30, 47], [38, 8, 30, 47, "columnNumber"], [38, 20, 30, 47], [39, 6, 30, 47], [39, 13, 30, 54], [39, 14, 30, 55], [40, 4, 30, 55], [41, 6, 30, 55, "fileName"], [41, 14, 30, 55], [41, 16, 30, 55, "_jsxFileName"], [41, 28, 30, 55], [42, 6, 30, 55, "lineNumber"], [42, 16, 30, 55], [43, 6, 30, 55, "columnNumber"], [43, 18, 30, 55], [44, 4, 30, 55], [44, 11, 31, 10], [44, 12, 31, 11], [45, 2, 33, 0], [46, 2, 35, 0], [46, 6, 35, 6, "styles"], [46, 12, 35, 12], [46, 15, 35, 15, "StyleSheet"], [46, 34, 35, 25], [46, 35, 35, 26, "create"], [46, 41, 35, 32], [46, 42, 35, 33], [47, 4, 36, 2, "section"], [47, 11, 36, 9], [47, 13, 36, 11], [48, 6, 37, 4, "marginTop"], [48, 15, 37, 13], [48, 17, 37, 15], [49, 4, 38, 2], [49, 5, 38, 3], [50, 4, 39, 2, "heading"], [50, 11, 39, 9], [50, 13, 39, 11], [51, 6, 40, 4, "alignItems"], [51, 16, 40, 14], [51, 18, 40, 16], [51, 26, 40, 24], [52, 6, 41, 4, "flexDirection"], [52, 19, 41, 17], [52, 21, 41, 19], [52, 26, 41, 24], [53, 6, 42, 4, "paddingHorizontal"], [53, 23, 42, 21], [53, 25, 42, 23], [53, 27, 42, 25], [54, 6, 43, 4, "marginBottom"], [54, 18, 43, 16], [54, 20, 43, 18], [55, 4, 44, 2], [55, 5, 44, 3], [56, 4, 45, 2, "headingText"], [56, 15, 45, 13], [56, 17, 45, 15], [57, 6, 46, 4, "color"], [57, 11, 46, 9], [57, 13, 46, 11, "LogBoxStyle"], [57, 24, 46, 22], [57, 25, 46, 23, "getTextColor"], [57, 37, 46, 35], [57, 38, 46, 36], [57, 39, 46, 37], [57, 40, 46, 38], [58, 6, 47, 4, "flex"], [58, 10, 47, 8], [58, 12, 47, 10], [58, 13, 47, 11], [59, 6, 48, 4, "fontSize"], [59, 14, 48, 12], [59, 16, 48, 14], [59, 18, 48, 16], [60, 6, 49, 4, "fontWeight"], [60, 16, 49, 14], [60, 18, 49, 16], [60, 23, 49, 21], [61, 6, 50, 4, "includeFontPadding"], [61, 24, 50, 22], [61, 26, 50, 24], [61, 31, 50, 29], [62, 6, 51, 4, "lineHeight"], [62, 16, 51, 14], [62, 18, 51, 16], [63, 4, 52, 2], [63, 5, 52, 3], [64, 4, 53, 2, "body"], [64, 8, 53, 6], [64, 10, 53, 8], [65, 6, 54, 4, "paddingBottom"], [65, 19, 54, 17], [65, 21, 54, 19], [66, 4, 55, 2], [67, 2, 56, 0], [67, 3, 56, 1], [67, 4, 56, 2], [68, 2, 56, 3], [68, 6, 56, 3, "_default"], [68, 14, 56, 3], [68, 17, 56, 3, "exports"], [68, 24, 56, 3], [68, 25, 56, 3, "default"], [68, 32, 56, 3], [68, 35, 58, 15, "LogBoxInspectorSection"], [68, 57, 58, 37], [69, 0, 58, 37], [69, 3]], "functionMap": {"names": ["<global>", "LogBoxInspectorSection"], "mappings": "AAA;ACsB;CDU"}}, "type": "js/module"}]}