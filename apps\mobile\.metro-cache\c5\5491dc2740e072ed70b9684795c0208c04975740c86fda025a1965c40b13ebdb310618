{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "nanoid/non-secure", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 43, "index": 58}}], "key": "SN8WVal79eAEDQEpzmVqVAy5JJs=", "exportNames": ["*"]}}, {"name": "./TabRouter.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 59}, "end": {"line": 4, "column": 55, "index": 114}}], "key": "jC/Bg2g+cpTuZwE6Bq9LoiYVdNc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.DrawerActions = void 0;\n  exports.DrawerRouter = DrawerRouter;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _nonSecure = require(_dependencyMap[2], \"nanoid/non-secure\");\n  var _TabRouter = require(_dependencyMap[3], \"./TabRouter.js\");\n  var _excluded = [\"defaultStatus\"];\n  var DrawerActions = exports.DrawerActions = {\n    ..._TabRouter.TabActions,\n    openDrawer() {\n      return {\n        type: 'OPEN_DRAWER'\n      };\n    },\n    closeDrawer() {\n      return {\n        type: 'CLOSE_DRAWER'\n      };\n    },\n    toggleDrawer() {\n      return {\n        type: 'TOGGLE_DRAWER'\n      };\n    }\n  };\n  function DrawerRouter(_ref) {\n    var _ref$defaultStatus = _ref.defaultStatus,\n      defaultStatus = _ref$defaultStatus === void 0 ? 'closed' : _ref$defaultStatus,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var router = (0, _TabRouter.TabRouter)(rest);\n    var isDrawerInHistory = state => Boolean(state.history?.some(it => it.type === 'drawer'));\n    var addDrawerToHistory = state => {\n      if (isDrawerInHistory(state)) {\n        return state;\n      }\n      return {\n        ...state,\n        history: [...state.history, {\n          type: 'drawer',\n          status: defaultStatus === 'open' ? 'closed' : 'open'\n        }]\n      };\n    };\n    var removeDrawerFromHistory = state => {\n      if (!isDrawerInHistory(state)) {\n        return state;\n      }\n      return {\n        ...state,\n        history: state.history.filter(it => it.type !== 'drawer')\n      };\n    };\n    var openDrawer = state => {\n      if (defaultStatus === 'open') {\n        return removeDrawerFromHistory(state);\n      }\n      return addDrawerToHistory(state);\n    };\n    var closeDrawer = state => {\n      if (defaultStatus === 'open') {\n        return addDrawerToHistory(state);\n      }\n      return removeDrawerFromHistory(state);\n    };\n    return {\n      ...router,\n      type: 'drawer',\n      getInitialState(_ref2) {\n        var routeNames = _ref2.routeNames,\n          routeParamList = _ref2.routeParamList,\n          routeGetIdList = _ref2.routeGetIdList;\n        var state = router.getInitialState({\n          routeNames,\n          routeParamList,\n          routeGetIdList\n        });\n        return {\n          ...state,\n          default: defaultStatus,\n          stale: false,\n          type: 'drawer',\n          key: `drawer-${(0, _nonSecure.nanoid)()}`\n        };\n      },\n      getRehydratedState(partialState, _ref3) {\n        var routeNames = _ref3.routeNames,\n          routeParamList = _ref3.routeParamList,\n          routeGetIdList = _ref3.routeGetIdList;\n        if (partialState.stale === false) {\n          return partialState;\n        }\n        var state = router.getRehydratedState(partialState, {\n          routeNames,\n          routeParamList,\n          routeGetIdList\n        });\n        if (isDrawerInHistory(partialState)) {\n          // Re-sync the drawer entry in history to correct it if it was wrong\n          state = removeDrawerFromHistory(state);\n          state = addDrawerToHistory(state);\n        }\n        return {\n          ...state,\n          default: defaultStatus,\n          type: 'drawer',\n          key: `drawer-${(0, _nonSecure.nanoid)()}`\n        };\n      },\n      getStateForRouteFocus(state, key) {\n        var result = router.getStateForRouteFocus(state, key);\n        return closeDrawer(result);\n      },\n      getStateForAction(state, action, options) {\n        switch (action.type) {\n          case 'OPEN_DRAWER':\n            return openDrawer(state);\n          case 'CLOSE_DRAWER':\n            return closeDrawer(state);\n          case 'TOGGLE_DRAWER':\n            if (isDrawerInHistory(state)) {\n              return removeDrawerFromHistory(state);\n            }\n            return addDrawerToHistory(state);\n          case 'JUMP_TO':\n          case 'NAVIGATE':\n          case 'NAVIGATE_DEPRECATED':\n            {\n              var result = router.getStateForAction(state, action, options);\n              if (result != null && result.index !== state.index) {\n                return closeDrawer(result);\n              }\n              return result;\n            }\n          case 'GO_BACK':\n            if (isDrawerInHistory(state)) {\n              return removeDrawerFromHistory(state);\n            }\n            return router.getStateForAction(state, action, options);\n          default:\n            return router.getStateForAction(state, action, options);\n        }\n      },\n      actionCreators: DrawerActions\n    };\n  }\n});", "lineCount": 152, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "DrawerActions"], [8, 23, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "DrawerRouter"], [9, 22, 1, 13], [9, 25, 1, 13, "DrawerRouter"], [9, 37, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_objectWithoutProperties2"], [10, 31, 1, 13], [10, 34, 1, 13, "_interopRequireDefault"], [10, 56, 1, 13], [10, 57, 1, 13, "require"], [10, 64, 1, 13], [10, 65, 1, 13, "_dependencyMap"], [10, 79, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "_nonSecure"], [11, 16, 3, 0], [11, 19, 3, 0, "require"], [11, 26, 3, 0], [11, 27, 3, 0, "_dependencyMap"], [11, 41, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_Tab<PERSON><PERSON>er"], [12, 16, 4, 0], [12, 19, 4, 0, "require"], [12, 26, 4, 0], [12, 27, 4, 0, "_dependencyMap"], [12, 41, 4, 0], [13, 2, 4, 55], [13, 6, 4, 55, "_excluded"], [13, 15, 4, 55], [14, 2, 5, 7], [14, 6, 5, 13, "DrawerActions"], [14, 19, 5, 26], [14, 22, 5, 26, "exports"], [14, 29, 5, 26], [14, 30, 5, 26, "DrawerActions"], [14, 43, 5, 26], [14, 46, 5, 29], [15, 4, 6, 2], [15, 7, 6, 5, "TabActions"], [15, 28, 6, 15], [16, 4, 7, 2, "openDrawer"], [16, 14, 7, 12, "openDrawer"], [16, 15, 7, 12], [16, 17, 7, 15], [17, 6, 8, 4], [17, 13, 8, 11], [18, 8, 9, 6, "type"], [18, 12, 9, 10], [18, 14, 9, 12], [19, 6, 10, 4], [19, 7, 10, 5], [20, 4, 11, 2], [20, 5, 11, 3], [21, 4, 12, 2, "closeDrawer"], [21, 15, 12, 13, "closeDrawer"], [21, 16, 12, 13], [21, 18, 12, 16], [22, 6, 13, 4], [22, 13, 13, 11], [23, 8, 14, 6, "type"], [23, 12, 14, 10], [23, 14, 14, 12], [24, 6, 15, 4], [24, 7, 15, 5], [25, 4, 16, 2], [25, 5, 16, 3], [26, 4, 17, 2, "toggle<PERSON>rawer"], [26, 16, 17, 14, "toggle<PERSON>rawer"], [26, 17, 17, 14], [26, 19, 17, 17], [27, 6, 18, 4], [27, 13, 18, 11], [28, 8, 19, 6, "type"], [28, 12, 19, 10], [28, 14, 19, 12], [29, 6, 20, 4], [29, 7, 20, 5], [30, 4, 21, 2], [31, 2, 22, 0], [31, 3, 22, 1], [32, 2, 23, 7], [32, 11, 23, 16, "DrawerRouter"], [32, 23, 23, 28, "DrawerRouter"], [32, 24, 23, 28, "_ref"], [32, 28, 23, 28], [32, 30, 26, 3], [33, 4, 26, 3], [33, 8, 26, 3, "_ref$defaultStatus"], [33, 26, 26, 3], [33, 29, 26, 3, "_ref"], [33, 33, 26, 3], [33, 34, 24, 2, "defaultStatus"], [33, 47, 24, 15], [34, 6, 24, 2, "defaultStatus"], [34, 19, 24, 15], [34, 22, 24, 15, "_ref$defaultStatus"], [34, 40, 24, 15], [34, 54, 24, 18], [34, 62, 24, 26], [34, 65, 24, 26, "_ref$defaultStatus"], [34, 83, 24, 26], [35, 6, 25, 5, "rest"], [35, 10, 25, 9], [35, 17, 25, 9, "_objectWithoutProperties2"], [35, 42, 25, 9], [35, 43, 25, 9, "default"], [35, 50, 25, 9], [35, 52, 25, 9, "_ref"], [35, 56, 25, 9], [35, 58, 25, 9, "_excluded"], [35, 67, 25, 9], [36, 4, 27, 2], [36, 8, 27, 8, "router"], [36, 14, 27, 14], [36, 17, 27, 17], [36, 21, 27, 17, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 41, 27, 26], [36, 43, 27, 27, "rest"], [36, 47, 27, 31], [36, 48, 27, 32], [37, 4, 28, 2], [37, 8, 28, 8, "isDrawerInHistory"], [37, 25, 28, 25], [37, 28, 28, 28, "state"], [37, 33, 28, 33], [37, 37, 28, 37, "Boolean"], [37, 44, 28, 44], [37, 45, 28, 45, "state"], [37, 50, 28, 50], [37, 51, 28, 51, "history"], [37, 58, 28, 58], [37, 60, 28, 60, "some"], [37, 64, 28, 64], [37, 65, 28, 65, "it"], [37, 67, 28, 67], [37, 71, 28, 71, "it"], [37, 73, 28, 73], [37, 74, 28, 74, "type"], [37, 78, 28, 78], [37, 83, 28, 83], [37, 91, 28, 91], [37, 92, 28, 92], [37, 93, 28, 93], [38, 4, 29, 2], [38, 8, 29, 8, "addDrawerToHistory"], [38, 26, 29, 26], [38, 29, 29, 29, "state"], [38, 34, 29, 34], [38, 38, 29, 38], [39, 6, 30, 4], [39, 10, 30, 8, "isDrawerInHistory"], [39, 27, 30, 25], [39, 28, 30, 26, "state"], [39, 33, 30, 31], [39, 34, 30, 32], [39, 36, 30, 34], [40, 8, 31, 6], [40, 15, 31, 13, "state"], [40, 20, 31, 18], [41, 6, 32, 4], [42, 6, 33, 4], [42, 13, 33, 11], [43, 8, 34, 6], [43, 11, 34, 9, "state"], [43, 16, 34, 14], [44, 8, 35, 6, "history"], [44, 15, 35, 13], [44, 17, 35, 15], [44, 18, 35, 16], [44, 21, 35, 19, "state"], [44, 26, 35, 24], [44, 27, 35, 25, "history"], [44, 34, 35, 32], [44, 36, 35, 34], [45, 10, 36, 8, "type"], [45, 14, 36, 12], [45, 16, 36, 14], [45, 24, 36, 22], [46, 10, 37, 8, "status"], [46, 16, 37, 14], [46, 18, 37, 16, "defaultStatus"], [46, 31, 37, 29], [46, 36, 37, 34], [46, 42, 37, 40], [46, 45, 37, 43], [46, 53, 37, 51], [46, 56, 37, 54], [47, 8, 38, 6], [47, 9, 38, 7], [48, 6, 39, 4], [48, 7, 39, 5], [49, 4, 40, 2], [49, 5, 40, 3], [50, 4, 41, 2], [50, 8, 41, 8, "removeDrawerFromHistory"], [50, 31, 41, 31], [50, 34, 41, 34, "state"], [50, 39, 41, 39], [50, 43, 41, 43], [51, 6, 42, 4], [51, 10, 42, 8], [51, 11, 42, 9, "isDrawerInHistory"], [51, 28, 42, 26], [51, 29, 42, 27, "state"], [51, 34, 42, 32], [51, 35, 42, 33], [51, 37, 42, 35], [52, 8, 43, 6], [52, 15, 43, 13, "state"], [52, 20, 43, 18], [53, 6, 44, 4], [54, 6, 45, 4], [54, 13, 45, 11], [55, 8, 46, 6], [55, 11, 46, 9, "state"], [55, 16, 46, 14], [56, 8, 47, 6, "history"], [56, 15, 47, 13], [56, 17, 47, 15, "state"], [56, 22, 47, 20], [56, 23, 47, 21, "history"], [56, 30, 47, 28], [56, 31, 47, 29, "filter"], [56, 37, 47, 35], [56, 38, 47, 36, "it"], [56, 40, 47, 38], [56, 44, 47, 42, "it"], [56, 46, 47, 44], [56, 47, 47, 45, "type"], [56, 51, 47, 49], [56, 56, 47, 54], [56, 64, 47, 62], [57, 6, 48, 4], [57, 7, 48, 5], [58, 4, 49, 2], [58, 5, 49, 3], [59, 4, 50, 2], [59, 8, 50, 8, "openDrawer"], [59, 18, 50, 18], [59, 21, 50, 21, "state"], [59, 26, 50, 26], [59, 30, 50, 30], [60, 6, 51, 4], [60, 10, 51, 8, "defaultStatus"], [60, 23, 51, 21], [60, 28, 51, 26], [60, 34, 51, 32], [60, 36, 51, 34], [61, 8, 52, 6], [61, 15, 52, 13, "removeDrawerFromHistory"], [61, 38, 52, 36], [61, 39, 52, 37, "state"], [61, 44, 52, 42], [61, 45, 52, 43], [62, 6, 53, 4], [63, 6, 54, 4], [63, 13, 54, 11, "addDrawerToHistory"], [63, 31, 54, 29], [63, 32, 54, 30, "state"], [63, 37, 54, 35], [63, 38, 54, 36], [64, 4, 55, 2], [64, 5, 55, 3], [65, 4, 56, 2], [65, 8, 56, 8, "closeDrawer"], [65, 19, 56, 19], [65, 22, 56, 22, "state"], [65, 27, 56, 27], [65, 31, 56, 31], [66, 6, 57, 4], [66, 10, 57, 8, "defaultStatus"], [66, 23, 57, 21], [66, 28, 57, 26], [66, 34, 57, 32], [66, 36, 57, 34], [67, 8, 58, 6], [67, 15, 58, 13, "addDrawerToHistory"], [67, 33, 58, 31], [67, 34, 58, 32, "state"], [67, 39, 58, 37], [67, 40, 58, 38], [68, 6, 59, 4], [69, 6, 60, 4], [69, 13, 60, 11, "removeDrawerFromHistory"], [69, 36, 60, 34], [69, 37, 60, 35, "state"], [69, 42, 60, 40], [69, 43, 60, 41], [70, 4, 61, 2], [70, 5, 61, 3], [71, 4, 62, 2], [71, 11, 62, 9], [72, 6, 63, 4], [72, 9, 63, 7, "router"], [72, 15, 63, 13], [73, 6, 64, 4, "type"], [73, 10, 64, 8], [73, 12, 64, 10], [73, 20, 64, 18], [74, 6, 65, 4, "getInitialState"], [74, 21, 65, 19, "getInitialState"], [74, 22, 65, 19, "_ref2"], [74, 27, 65, 19], [74, 29, 69, 7], [75, 8, 69, 7], [75, 12, 66, 6, "routeNames"], [75, 22, 66, 16], [75, 25, 66, 16, "_ref2"], [75, 30, 66, 16], [75, 31, 66, 6, "routeNames"], [75, 41, 66, 16], [76, 10, 67, 6, "routeParamList"], [76, 24, 67, 20], [76, 27, 67, 20, "_ref2"], [76, 32, 67, 20], [76, 33, 67, 6, "routeParamList"], [76, 47, 67, 20], [77, 10, 68, 6, "routeGetIdList"], [77, 24, 68, 20], [77, 27, 68, 20, "_ref2"], [77, 32, 68, 20], [77, 33, 68, 6, "routeGetIdList"], [77, 47, 68, 20], [78, 8, 70, 6], [78, 12, 70, 12, "state"], [78, 17, 70, 17], [78, 20, 70, 20, "router"], [78, 26, 70, 26], [78, 27, 70, 27, "getInitialState"], [78, 42, 70, 42], [78, 43, 70, 43], [79, 10, 71, 8, "routeNames"], [79, 20, 71, 18], [80, 10, 72, 8, "routeParamList"], [80, 24, 72, 22], [81, 10, 73, 8, "routeGetIdList"], [82, 8, 74, 6], [82, 9, 74, 7], [82, 10, 74, 8], [83, 8, 75, 6], [83, 15, 75, 13], [84, 10, 76, 8], [84, 13, 76, 11, "state"], [84, 18, 76, 16], [85, 10, 77, 8, "default"], [85, 17, 77, 15], [85, 19, 77, 17, "defaultStatus"], [85, 32, 77, 30], [86, 10, 78, 8, "stale"], [86, 15, 78, 13], [86, 17, 78, 15], [86, 22, 78, 20], [87, 10, 79, 8, "type"], [87, 14, 79, 12], [87, 16, 79, 14], [87, 24, 79, 22], [88, 10, 80, 8, "key"], [88, 13, 80, 11], [88, 15, 80, 13], [88, 25, 80, 23], [88, 29, 80, 23, "nanoid"], [88, 46, 80, 29], [88, 48, 80, 30], [88, 49, 80, 31], [89, 8, 81, 6], [89, 9, 81, 7], [90, 6, 82, 4], [90, 7, 82, 5], [91, 6, 83, 4, "getRehydratedState"], [91, 24, 83, 22, "getRehydratedState"], [91, 25, 83, 23, "partialState"], [91, 37, 83, 35], [91, 39, 83, 35, "_ref3"], [91, 44, 83, 35], [91, 46, 87, 7], [92, 8, 87, 7], [92, 12, 84, 6, "routeNames"], [92, 22, 84, 16], [92, 25, 84, 16, "_ref3"], [92, 30, 84, 16], [92, 31, 84, 6, "routeNames"], [92, 41, 84, 16], [93, 10, 85, 6, "routeParamList"], [93, 24, 85, 20], [93, 27, 85, 20, "_ref3"], [93, 32, 85, 20], [93, 33, 85, 6, "routeParamList"], [93, 47, 85, 20], [94, 10, 86, 6, "routeGetIdList"], [94, 24, 86, 20], [94, 27, 86, 20, "_ref3"], [94, 32, 86, 20], [94, 33, 86, 6, "routeGetIdList"], [94, 47, 86, 20], [95, 8, 88, 6], [95, 12, 88, 10, "partialState"], [95, 24, 88, 22], [95, 25, 88, 23, "stale"], [95, 30, 88, 28], [95, 35, 88, 33], [95, 40, 88, 38], [95, 42, 88, 40], [96, 10, 89, 8], [96, 17, 89, 15, "partialState"], [96, 29, 89, 27], [97, 8, 90, 6], [98, 8, 91, 6], [98, 12, 91, 10, "state"], [98, 17, 91, 15], [98, 20, 91, 18, "router"], [98, 26, 91, 24], [98, 27, 91, 25, "getRehydratedState"], [98, 45, 91, 43], [98, 46, 91, 44, "partialState"], [98, 58, 91, 56], [98, 60, 91, 58], [99, 10, 92, 8, "routeNames"], [99, 20, 92, 18], [100, 10, 93, 8, "routeParamList"], [100, 24, 93, 22], [101, 10, 94, 8, "routeGetIdList"], [102, 8, 95, 6], [102, 9, 95, 7], [102, 10, 95, 8], [103, 8, 96, 6], [103, 12, 96, 10, "isDrawerInHistory"], [103, 29, 96, 27], [103, 30, 96, 28, "partialState"], [103, 42, 96, 40], [103, 43, 96, 41], [103, 45, 96, 43], [104, 10, 97, 8], [105, 10, 98, 8, "state"], [105, 15, 98, 13], [105, 18, 98, 16, "removeDrawerFromHistory"], [105, 41, 98, 39], [105, 42, 98, 40, "state"], [105, 47, 98, 45], [105, 48, 98, 46], [106, 10, 99, 8, "state"], [106, 15, 99, 13], [106, 18, 99, 16, "addDrawerToHistory"], [106, 36, 99, 34], [106, 37, 99, 35, "state"], [106, 42, 99, 40], [106, 43, 99, 41], [107, 8, 100, 6], [108, 8, 101, 6], [108, 15, 101, 13], [109, 10, 102, 8], [109, 13, 102, 11, "state"], [109, 18, 102, 16], [110, 10, 103, 8, "default"], [110, 17, 103, 15], [110, 19, 103, 17, "defaultStatus"], [110, 32, 103, 30], [111, 10, 104, 8, "type"], [111, 14, 104, 12], [111, 16, 104, 14], [111, 24, 104, 22], [112, 10, 105, 8, "key"], [112, 13, 105, 11], [112, 15, 105, 13], [112, 25, 105, 23], [112, 29, 105, 23, "nanoid"], [112, 46, 105, 29], [112, 48, 105, 30], [112, 49, 105, 31], [113, 8, 106, 6], [113, 9, 106, 7], [114, 6, 107, 4], [114, 7, 107, 5], [115, 6, 108, 4, "getStateForRouteFocus"], [115, 27, 108, 25, "getStateForRouteFocus"], [115, 28, 108, 26, "state"], [115, 33, 108, 31], [115, 35, 108, 33, "key"], [115, 38, 108, 36], [115, 40, 108, 38], [116, 8, 109, 6], [116, 12, 109, 12, "result"], [116, 18, 109, 18], [116, 21, 109, 21, "router"], [116, 27, 109, 27], [116, 28, 109, 28, "getStateForRouteFocus"], [116, 49, 109, 49], [116, 50, 109, 50, "state"], [116, 55, 109, 55], [116, 57, 109, 57, "key"], [116, 60, 109, 60], [116, 61, 109, 61], [117, 8, 110, 6], [117, 15, 110, 13, "closeDrawer"], [117, 26, 110, 24], [117, 27, 110, 25, "result"], [117, 33, 110, 31], [117, 34, 110, 32], [118, 6, 111, 4], [118, 7, 111, 5], [119, 6, 112, 4, "getStateForAction"], [119, 23, 112, 21, "getStateForAction"], [119, 24, 112, 22, "state"], [119, 29, 112, 27], [119, 31, 112, 29, "action"], [119, 37, 112, 35], [119, 39, 112, 37, "options"], [119, 46, 112, 44], [119, 48, 112, 46], [120, 8, 113, 6], [120, 16, 113, 14, "action"], [120, 22, 113, 20], [120, 23, 113, 21, "type"], [120, 27, 113, 25], [121, 10, 114, 8], [121, 15, 114, 13], [121, 28, 114, 26], [122, 12, 115, 10], [122, 19, 115, 17, "openDrawer"], [122, 29, 115, 27], [122, 30, 115, 28, "state"], [122, 35, 115, 33], [122, 36, 115, 34], [123, 10, 116, 8], [123, 15, 116, 13], [123, 29, 116, 27], [124, 12, 117, 10], [124, 19, 117, 17, "closeDrawer"], [124, 30, 117, 28], [124, 31, 117, 29, "state"], [124, 36, 117, 34], [124, 37, 117, 35], [125, 10, 118, 8], [125, 15, 118, 13], [125, 30, 118, 28], [126, 12, 119, 10], [126, 16, 119, 14, "isDrawerInHistory"], [126, 33, 119, 31], [126, 34, 119, 32, "state"], [126, 39, 119, 37], [126, 40, 119, 38], [126, 42, 119, 40], [127, 14, 120, 12], [127, 21, 120, 19, "removeDrawerFromHistory"], [127, 44, 120, 42], [127, 45, 120, 43, "state"], [127, 50, 120, 48], [127, 51, 120, 49], [128, 12, 121, 10], [129, 12, 122, 10], [129, 19, 122, 17, "addDrawerToHistory"], [129, 37, 122, 35], [129, 38, 122, 36, "state"], [129, 43, 122, 41], [129, 44, 122, 42], [130, 10, 123, 8], [130, 15, 123, 13], [130, 24, 123, 22], [131, 10, 124, 8], [131, 15, 124, 13], [131, 25, 124, 23], [132, 10, 125, 8], [132, 15, 125, 13], [132, 36, 125, 34], [133, 12, 126, 10], [134, 14, 127, 12], [134, 18, 127, 18, "result"], [134, 24, 127, 24], [134, 27, 127, 27, "router"], [134, 33, 127, 33], [134, 34, 127, 34, "getStateForAction"], [134, 51, 127, 51], [134, 52, 127, 52, "state"], [134, 57, 127, 57], [134, 59, 127, 59, "action"], [134, 65, 127, 65], [134, 67, 127, 67, "options"], [134, 74, 127, 74], [134, 75, 127, 75], [135, 14, 128, 12], [135, 18, 128, 16, "result"], [135, 24, 128, 22], [135, 28, 128, 26], [135, 32, 128, 30], [135, 36, 128, 34, "result"], [135, 42, 128, 40], [135, 43, 128, 41, "index"], [135, 48, 128, 46], [135, 53, 128, 51, "state"], [135, 58, 128, 56], [135, 59, 128, 57, "index"], [135, 64, 128, 62], [135, 66, 128, 64], [136, 16, 129, 14], [136, 23, 129, 21, "closeDrawer"], [136, 34, 129, 32], [136, 35, 129, 33, "result"], [136, 41, 129, 39], [136, 42, 129, 40], [137, 14, 130, 12], [138, 14, 131, 12], [138, 21, 131, 19, "result"], [138, 27, 131, 25], [139, 12, 132, 10], [140, 10, 133, 8], [140, 15, 133, 13], [140, 24, 133, 22], [141, 12, 134, 10], [141, 16, 134, 14, "isDrawerInHistory"], [141, 33, 134, 31], [141, 34, 134, 32, "state"], [141, 39, 134, 37], [141, 40, 134, 38], [141, 42, 134, 40], [142, 14, 135, 12], [142, 21, 135, 19, "removeDrawerFromHistory"], [142, 44, 135, 42], [142, 45, 135, 43, "state"], [142, 50, 135, 48], [142, 51, 135, 49], [143, 12, 136, 10], [144, 12, 137, 10], [144, 19, 137, 17, "router"], [144, 25, 137, 23], [144, 26, 137, 24, "getStateForAction"], [144, 43, 137, 41], [144, 44, 137, 42, "state"], [144, 49, 137, 47], [144, 51, 137, 49, "action"], [144, 57, 137, 55], [144, 59, 137, 57, "options"], [144, 66, 137, 64], [144, 67, 137, 65], [145, 10, 138, 8], [146, 12, 139, 10], [146, 19, 139, 17, "router"], [146, 25, 139, 23], [146, 26, 139, 24, "getStateForAction"], [146, 43, 139, 41], [146, 44, 139, 42, "state"], [146, 49, 139, 47], [146, 51, 139, 49, "action"], [146, 57, 139, 55], [146, 59, 139, 57, "options"], [146, 66, 139, 64], [146, 67, 139, 65], [147, 8, 140, 6], [148, 6, 141, 4], [148, 7, 141, 5], [149, 6, 142, 4, "actionCreators"], [149, 20, 142, 18], [149, 22, 142, 20, "DrawerActions"], [150, 4, 143, 2], [150, 5, 143, 3], [151, 2, 144, 0], [152, 0, 144, 1], [152, 3]], "functionMap": {"names": ["<global>", "DrawerActions.openDrawer", "DrawerActions.closeDrawer", "DrawerActions.toggleDrawer", "DrawerRouter", "isDrawerInHistory", "state.history.some$argument_0", "addDrawerToHistory", "removeDrawerFromHistory", "state.history.filter$argument_0", "openDrawer", "closeDrawer", "getInitialState", "getRehydratedState", "getStateForRouteFocus", "getStateForAction"], "mappings": "AAA;ECM;GDI;EEC;GFI;EGC;GHI;OIE;4BCK,qCC,0BD,ED;6BGC;GHW;kCIC;oCCM,0BD;GJE;qBMC;GNK;sBOC;GPK;IQI;KRiB;ISC;KTwB;IUC;KVG;IWC;KX6B;CJG"}}, "type": "js/module"}]}