{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[7], \"invariant\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[8], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var StateSafePureComponent = exports.default = /*#__PURE__*/function (_React$PureComponent) {\n    function StateSafePureComponent(props) {\n      var _this;\n      (0, _classCallCheck2.default)(this, StateSafePureComponent);\n      _this = _callSuper(this, StateSafePureComponent, [props]);\n      _this._inAsyncStateUpdate = false;\n      _this._installSetStateHooks();\n      return _this;\n    }\n    (0, _inherits2.default)(StateSafePureComponent, _React$PureComponent);\n    return (0, _createClass2.default)(StateSafePureComponent, [{\n      key: \"setState\",\n      value: function setState(partialState, callback) {\n        if (typeof partialState === 'function') {\n          _superPropGet(StateSafePureComponent, \"setState\", this, 3)([(state, props) => {\n            this._inAsyncStateUpdate = true;\n            var ret;\n            try {\n              ret = partialState(state, props);\n            } catch (err) {\n              throw err;\n            } finally {\n              this._inAsyncStateUpdate = false;\n            }\n            return ret;\n          }, callback]);\n        } else {\n          _superPropGet(StateSafePureComponent, \"setState\", this, 3)([partialState, callback]);\n        }\n      }\n    }, {\n      key: \"_installSetStateHooks\",\n      value: function _installSetStateHooks() {\n        var that = this;\n        var props = this.props,\n          state = this.state;\n        Object.defineProperty(this, 'props', {\n          get() {\n            (0, _invariant.default)(!that._inAsyncStateUpdate, '\"this.props\" should not be accessed during state updates');\n            return props;\n          },\n          set(newProps) {\n            props = newProps;\n          }\n        });\n        Object.defineProperty(this, 'state', {\n          get() {\n            (0, _invariant.default)(!that._inAsyncStateUpdate, '\"this.state\" should not be acceessed during state updates');\n            return state;\n          },\n          set(newState) {\n            state = newState;\n          }\n        });\n      }\n    }]);\n  }(React.PureComponent);\n});", "lineCount": 76, "map": [[13, 2, 11, 0], [13, 6, 11, 0, "_invariant"], [13, 16, 11, 0], [13, 19, 11, 0, "_interopRequireDefault"], [13, 41, 11, 0], [13, 42, 11, 0, "require"], [13, 49, 11, 0], [13, 50, 11, 0, "_dependencyMap"], [13, 64, 11, 0], [14, 2, 12, 0], [14, 6, 12, 0, "React"], [14, 11, 12, 0], [14, 14, 12, 0, "_interopRequireWildcard"], [14, 37, 12, 0], [14, 38, 12, 0, "require"], [14, 45, 12, 0], [14, 46, 12, 0, "_dependencyMap"], [14, 60, 12, 0], [15, 2, 12, 31], [15, 11, 12, 31, "_interopRequireWildcard"], [15, 35, 12, 31, "e"], [15, 36, 12, 31], [15, 38, 12, 31, "t"], [15, 39, 12, 31], [15, 68, 12, 31, "WeakMap"], [15, 75, 12, 31], [15, 81, 12, 31, "r"], [15, 82, 12, 31], [15, 89, 12, 31, "WeakMap"], [15, 96, 12, 31], [15, 100, 12, 31, "n"], [15, 101, 12, 31], [15, 108, 12, 31, "WeakMap"], [15, 115, 12, 31], [15, 127, 12, 31, "_interopRequireWildcard"], [15, 150, 12, 31], [15, 162, 12, 31, "_interopRequireWildcard"], [15, 163, 12, 31, "e"], [15, 164, 12, 31], [15, 166, 12, 31, "t"], [15, 167, 12, 31], [15, 176, 12, 31, "t"], [15, 177, 12, 31], [15, 181, 12, 31, "e"], [15, 182, 12, 31], [15, 186, 12, 31, "e"], [15, 187, 12, 31], [15, 188, 12, 31, "__esModule"], [15, 198, 12, 31], [15, 207, 12, 31, "e"], [15, 208, 12, 31], [15, 214, 12, 31, "o"], [15, 215, 12, 31], [15, 217, 12, 31, "i"], [15, 218, 12, 31], [15, 220, 12, 31, "f"], [15, 221, 12, 31], [15, 226, 12, 31, "__proto__"], [15, 235, 12, 31], [15, 243, 12, 31, "default"], [15, 250, 12, 31], [15, 252, 12, 31, "e"], [15, 253, 12, 31], [15, 270, 12, 31, "e"], [15, 271, 12, 31], [15, 294, 12, 31, "e"], [15, 295, 12, 31], [15, 320, 12, 31, "e"], [15, 321, 12, 31], [15, 330, 12, 31, "f"], [15, 331, 12, 31], [15, 337, 12, 31, "o"], [15, 338, 12, 31], [15, 341, 12, 31, "t"], [15, 342, 12, 31], [15, 345, 12, 31, "n"], [15, 346, 12, 31], [15, 349, 12, 31, "r"], [15, 350, 12, 31], [15, 358, 12, 31, "o"], [15, 359, 12, 31], [15, 360, 12, 31, "has"], [15, 363, 12, 31], [15, 364, 12, 31, "e"], [15, 365, 12, 31], [15, 375, 12, 31, "o"], [15, 376, 12, 31], [15, 377, 12, 31, "get"], [15, 380, 12, 31], [15, 381, 12, 31, "e"], [15, 382, 12, 31], [15, 385, 12, 31, "o"], [15, 386, 12, 31], [15, 387, 12, 31, "set"], [15, 390, 12, 31], [15, 391, 12, 31, "e"], [15, 392, 12, 31], [15, 394, 12, 31, "f"], [15, 395, 12, 31], [15, 409, 12, 31, "_t"], [15, 411, 12, 31], [15, 415, 12, 31, "e"], [15, 416, 12, 31], [15, 432, 12, 31, "_t"], [15, 434, 12, 31], [15, 441, 12, 31, "hasOwnProperty"], [15, 455, 12, 31], [15, 456, 12, 31, "call"], [15, 460, 12, 31], [15, 461, 12, 31, "e"], [15, 462, 12, 31], [15, 464, 12, 31, "_t"], [15, 466, 12, 31], [15, 473, 12, 31, "i"], [15, 474, 12, 31], [15, 478, 12, 31, "o"], [15, 479, 12, 31], [15, 482, 12, 31, "Object"], [15, 488, 12, 31], [15, 489, 12, 31, "defineProperty"], [15, 503, 12, 31], [15, 508, 12, 31, "Object"], [15, 514, 12, 31], [15, 515, 12, 31, "getOwnPropertyDescriptor"], [15, 539, 12, 31], [15, 540, 12, 31, "e"], [15, 541, 12, 31], [15, 543, 12, 31, "_t"], [15, 545, 12, 31], [15, 552, 12, 31, "i"], [15, 553, 12, 31], [15, 554, 12, 31, "get"], [15, 557, 12, 31], [15, 561, 12, 31, "i"], [15, 562, 12, 31], [15, 563, 12, 31, "set"], [15, 566, 12, 31], [15, 570, 12, 31, "o"], [15, 571, 12, 31], [15, 572, 12, 31, "f"], [15, 573, 12, 31], [15, 575, 12, 31, "_t"], [15, 577, 12, 31], [15, 579, 12, 31, "i"], [15, 580, 12, 31], [15, 584, 12, 31, "f"], [15, 585, 12, 31], [15, 586, 12, 31, "_t"], [15, 588, 12, 31], [15, 592, 12, 31, "e"], [15, 593, 12, 31], [15, 594, 12, 31, "_t"], [15, 596, 12, 31], [15, 607, 12, 31, "f"], [15, 608, 12, 31], [15, 613, 12, 31, "e"], [15, 614, 12, 31], [15, 616, 12, 31, "t"], [15, 617, 12, 31], [16, 2, 12, 31], [16, 11, 12, 31, "_callSuper"], [16, 22, 12, 31, "t"], [16, 23, 12, 31], [16, 25, 12, 31, "o"], [16, 26, 12, 31], [16, 28, 12, 31, "e"], [16, 29, 12, 31], [16, 40, 12, 31, "o"], [16, 41, 12, 31], [16, 48, 12, 31, "_getPrototypeOf2"], [16, 64, 12, 31], [16, 65, 12, 31, "default"], [16, 72, 12, 31], [16, 74, 12, 31, "o"], [16, 75, 12, 31], [16, 82, 12, 31, "_possibleConstructorReturn2"], [16, 109, 12, 31], [16, 110, 12, 31, "default"], [16, 117, 12, 31], [16, 119, 12, 31, "t"], [16, 120, 12, 31], [16, 122, 12, 31, "_isNativeReflectConstruct"], [16, 147, 12, 31], [16, 152, 12, 31, "Reflect"], [16, 159, 12, 31], [16, 160, 12, 31, "construct"], [16, 169, 12, 31], [16, 170, 12, 31, "o"], [16, 171, 12, 31], [16, 173, 12, 31, "e"], [16, 174, 12, 31], [16, 186, 12, 31, "_getPrototypeOf2"], [16, 202, 12, 31], [16, 203, 12, 31, "default"], [16, 210, 12, 31], [16, 212, 12, 31, "t"], [16, 213, 12, 31], [16, 215, 12, 31, "constructor"], [16, 226, 12, 31], [16, 230, 12, 31, "o"], [16, 231, 12, 31], [16, 232, 12, 31, "apply"], [16, 237, 12, 31], [16, 238, 12, 31, "t"], [16, 239, 12, 31], [16, 241, 12, 31, "e"], [16, 242, 12, 31], [17, 2, 12, 31], [17, 11, 12, 31, "_isNativeReflectConstruct"], [17, 37, 12, 31], [17, 51, 12, 31, "t"], [17, 52, 12, 31], [17, 56, 12, 31, "Boolean"], [17, 63, 12, 31], [17, 64, 12, 31, "prototype"], [17, 73, 12, 31], [17, 74, 12, 31, "valueOf"], [17, 81, 12, 31], [17, 82, 12, 31, "call"], [17, 86, 12, 31], [17, 87, 12, 31, "Reflect"], [17, 94, 12, 31], [17, 95, 12, 31, "construct"], [17, 104, 12, 31], [17, 105, 12, 31, "Boolean"], [17, 112, 12, 31], [17, 145, 12, 31, "t"], [17, 146, 12, 31], [17, 159, 12, 31, "_isNativeReflectConstruct"], [17, 184, 12, 31], [17, 196, 12, 31, "_isNativeReflectConstruct"], [17, 197, 12, 31], [17, 210, 12, 31, "t"], [17, 211, 12, 31], [18, 2, 12, 31], [18, 11, 12, 31, "_superPropGet"], [18, 25, 12, 31, "t"], [18, 26, 12, 31], [18, 28, 12, 31, "o"], [18, 29, 12, 31], [18, 31, 12, 31, "e"], [18, 32, 12, 31], [18, 34, 12, 31, "r"], [18, 35, 12, 31], [18, 43, 12, 31, "p"], [18, 44, 12, 31], [18, 51, 12, 31, "_get2"], [18, 56, 12, 31], [18, 57, 12, 31, "default"], [18, 64, 12, 31], [18, 70, 12, 31, "_getPrototypeOf2"], [18, 86, 12, 31], [18, 87, 12, 31, "default"], [18, 94, 12, 31], [18, 100, 12, 31, "r"], [18, 101, 12, 31], [18, 104, 12, 31, "t"], [18, 105, 12, 31], [18, 106, 12, 31, "prototype"], [18, 115, 12, 31], [18, 118, 12, 31, "t"], [18, 119, 12, 31], [18, 122, 12, 31, "o"], [18, 123, 12, 31], [18, 125, 12, 31, "e"], [18, 126, 12, 31], [18, 140, 12, 31, "r"], [18, 141, 12, 31], [18, 166, 12, 31, "p"], [18, 167, 12, 31], [18, 180, 12, 31, "t"], [18, 181, 12, 31], [18, 192, 12, 31, "p"], [18, 193, 12, 31], [18, 194, 12, 31, "apply"], [18, 199, 12, 31], [18, 200, 12, 31, "e"], [18, 201, 12, 31], [18, 203, 12, 31, "t"], [18, 204, 12, 31], [18, 211, 12, 31, "p"], [18, 212, 12, 31], [19, 2, 12, 31], [19, 6, 23, 21, "StateSafePureComponent"], [19, 28, 23, 43], [19, 31, 23, 43, "exports"], [19, 38, 23, 43], [19, 39, 23, 43, "default"], [19, 46, 23, 43], [19, 72, 23, 43, "_React$PureComponent"], [19, 92, 23, 43], [20, 4, 29, 2], [20, 13, 29, 2, "StateSafePureComponent"], [20, 36, 29, 14, "props"], [20, 41, 29, 26], [20, 43, 29, 28], [21, 6, 29, 28], [21, 10, 29, 28, "_this"], [21, 15, 29, 28], [22, 6, 29, 28], [22, 10, 29, 28, "_classCallCheck2"], [22, 26, 29, 28], [22, 27, 29, 28, "default"], [22, 34, 29, 28], [22, 42, 29, 28, "StateSafePureComponent"], [22, 64, 29, 28], [23, 6, 30, 4, "_this"], [23, 11, 30, 4], [23, 14, 30, 4, "_callSuper"], [23, 24, 30, 4], [23, 31, 30, 4, "StateSafePureComponent"], [23, 53, 30, 4], [23, 56, 30, 10, "props"], [23, 61, 30, 15], [24, 6, 30, 17, "_this"], [24, 11, 30, 17], [24, 12, 27, 2, "_inAsyncStateUpdate"], [24, 31, 27, 21], [24, 34, 27, 24], [24, 39, 27, 29], [25, 6, 31, 4, "_this"], [25, 11, 31, 4], [25, 12, 31, 9, "_installSetStateHooks"], [25, 33, 31, 30], [25, 34, 31, 31], [25, 35, 31, 32], [26, 6, 31, 33], [26, 13, 31, 33, "_this"], [26, 18, 31, 33], [27, 4, 32, 2], [28, 4, 32, 3], [28, 8, 32, 3, "_inherits2"], [28, 18, 32, 3], [28, 19, 32, 3, "default"], [28, 26, 32, 3], [28, 28, 32, 3, "StateSafePureComponent"], [28, 50, 32, 3], [28, 52, 32, 3, "_React$PureComponent"], [28, 72, 32, 3], [29, 4, 32, 3], [29, 15, 32, 3, "_createClass2"], [29, 28, 32, 3], [29, 29, 32, 3, "default"], [29, 36, 32, 3], [29, 38, 32, 3, "StateSafePureComponent"], [29, 60, 32, 3], [30, 6, 32, 3, "key"], [30, 9, 32, 3], [31, 6, 32, 3, "value"], [31, 11, 32, 3], [31, 13, 34, 2], [31, 22, 34, 2, "setState"], [31, 30, 34, 10, "setState"], [31, 31, 35, 4, "partialState"], [31, 43, 35, 73], [31, 45, 36, 4, "callback"], [31, 53, 36, 26], [31, 55, 37, 10], [32, 8, 38, 4], [32, 12, 38, 8], [32, 19, 38, 15, "partialState"], [32, 31, 38, 27], [32, 36, 38, 32], [32, 46, 38, 42], [32, 48, 38, 44], [33, 10, 39, 6, "_superPropGet"], [33, 23, 39, 6], [33, 24, 39, 6, "StateSafePureComponent"], [33, 46, 39, 6], [33, 70, 39, 21], [33, 71, 39, 22, "state"], [33, 76, 39, 27], [33, 78, 39, 29, "props"], [33, 83, 39, 34], [33, 88, 39, 39], [34, 12, 40, 8], [34, 16, 40, 12], [34, 17, 40, 13, "_inAsyncStateUpdate"], [34, 36, 40, 32], [34, 39, 40, 35], [34, 43, 40, 39], [35, 12, 41, 8], [35, 16, 41, 12, "ret"], [35, 19, 41, 15], [36, 12, 42, 8], [36, 16, 42, 12], [37, 14, 43, 10, "ret"], [37, 17, 43, 13], [37, 20, 43, 16, "partialState"], [37, 32, 43, 28], [37, 33, 43, 29, "state"], [37, 38, 43, 34], [37, 40, 43, 36, "props"], [37, 45, 43, 41], [37, 46, 43, 42], [38, 12, 44, 8], [38, 13, 44, 9], [38, 14, 44, 10], [38, 21, 44, 17, "err"], [38, 24, 44, 20], [38, 26, 44, 22], [39, 14, 45, 10], [39, 20, 45, 16, "err"], [39, 23, 45, 19], [40, 12, 46, 8], [40, 13, 46, 9], [40, 22, 46, 18], [41, 14, 47, 10], [41, 18, 47, 14], [41, 19, 47, 15, "_inAsyncStateUpdate"], [41, 38, 47, 34], [41, 41, 47, 37], [41, 46, 47, 42], [42, 12, 48, 8], [43, 12, 49, 8], [43, 19, 49, 15, "ret"], [43, 22, 49, 18], [44, 10, 50, 6], [44, 11, 50, 7], [44, 13, 50, 9, "callback"], [44, 21, 50, 17], [45, 8, 51, 4], [45, 9, 51, 5], [45, 15, 51, 11], [46, 10, 52, 6, "_superPropGet"], [46, 23, 52, 6], [46, 24, 52, 6, "StateSafePureComponent"], [46, 46, 52, 6], [46, 70, 52, 21, "partialState"], [46, 82, 52, 33], [46, 84, 52, 35, "callback"], [46, 92, 52, 43], [47, 8, 53, 4], [48, 6, 54, 2], [49, 4, 54, 3], [50, 6, 54, 3, "key"], [50, 9, 54, 3], [51, 6, 54, 3, "value"], [51, 11, 54, 3], [51, 13, 56, 2], [51, 22, 56, 2, "_installSetStateHooks"], [51, 43, 56, 23, "_installSetStateHooks"], [51, 44, 56, 23], [51, 46, 56, 26], [52, 8, 57, 4], [52, 12, 57, 10, "that"], [52, 16, 57, 14], [52, 19, 57, 17], [52, 23, 57, 21], [53, 8, 58, 4], [53, 12, 58, 9, "props"], [53, 17, 58, 14], [53, 20, 58, 25], [53, 24, 58, 29], [53, 25, 58, 9, "props"], [53, 30, 58, 14], [54, 10, 58, 16, "state"], [54, 15, 58, 21], [54, 18, 58, 25], [54, 22, 58, 29], [54, 23, 58, 16, "state"], [54, 28, 58, 21], [55, 8, 60, 4, "Object"], [55, 14, 60, 10], [55, 15, 60, 11, "defineProperty"], [55, 29, 60, 25], [55, 30, 60, 26], [55, 34, 60, 30], [55, 36, 60, 32], [55, 43, 60, 39], [55, 45, 60, 41], [56, 10, 61, 6, "get"], [56, 13, 61, 9, "get"], [56, 14, 61, 9], [56, 16, 61, 12], [57, 12, 62, 8], [57, 16, 62, 8, "invariant"], [57, 34, 62, 17], [57, 36, 63, 10], [57, 37, 63, 11, "that"], [57, 41, 63, 15], [57, 42, 63, 16, "_inAsyncStateUpdate"], [57, 61, 63, 35], [57, 63, 64, 10], [57, 121, 65, 8], [57, 122, 65, 9], [58, 12, 66, 8], [58, 19, 66, 15, "props"], [58, 24, 66, 20], [59, 10, 67, 6], [59, 11, 67, 7], [60, 10, 68, 6, "set"], [60, 13, 68, 9, "set"], [60, 14, 68, 10, "newProps"], [60, 22, 68, 25], [60, 24, 68, 27], [61, 12, 69, 8, "props"], [61, 17, 69, 13], [61, 20, 69, 16, "newProps"], [61, 28, 69, 24], [62, 10, 70, 6], [63, 8, 71, 4], [63, 9, 71, 5], [63, 10, 71, 6], [64, 8, 72, 4, "Object"], [64, 14, 72, 10], [64, 15, 72, 11, "defineProperty"], [64, 29, 72, 25], [64, 30, 72, 26], [64, 34, 72, 30], [64, 36, 72, 32], [64, 43, 72, 39], [64, 45, 72, 41], [65, 10, 73, 6, "get"], [65, 13, 73, 9, "get"], [65, 14, 73, 9], [65, 16, 73, 12], [66, 12, 74, 8], [66, 16, 74, 8, "invariant"], [66, 34, 74, 17], [66, 36, 75, 10], [66, 37, 75, 11, "that"], [66, 41, 75, 15], [66, 42, 75, 16, "_inAsyncStateUpdate"], [66, 61, 75, 35], [66, 63, 76, 10], [66, 122, 77, 8], [66, 123, 77, 9], [67, 12, 78, 8], [67, 19, 78, 15, "state"], [67, 24, 78, 20], [68, 10, 79, 6], [68, 11, 79, 7], [69, 10, 80, 6, "set"], [69, 13, 80, 9, "set"], [69, 14, 80, 10, "newState"], [69, 22, 80, 25], [69, 24, 80, 27], [70, 12, 81, 8, "state"], [70, 17, 81, 13], [70, 20, 81, 16, "newState"], [70, 28, 81, 24], [71, 10, 82, 6], [72, 8, 83, 4], [72, 9, 83, 5], [72, 10, 83, 6], [73, 6, 84, 2], [74, 4, 84, 3], [75, 2, 84, 3], [75, 4, 26, 10, "React"], [75, 9, 26, 15], [75, 10, 26, 16, "PureComponent"], [75, 23, 26, 29], [76, 0, 26, 29], [76, 3]], "functionMap": {"names": ["<global>", "StateSafePureComponent", "constructor", "setState", "setState$argument_0", "_installSetStateHooks", "Object.defineProperty$argument_2.get", "Object.defineProperty$argument_2.set"], "mappings": "AAA;eCsB;ECM;GDG;EEE;qBCK;ODW;GFI;EIE;MCK;ODM;MEC;OFE;MCG;ODM;MEC;OFE;GJE"}}, "type": "js/module"}]}