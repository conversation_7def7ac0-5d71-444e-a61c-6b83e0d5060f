{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 42}, "end": {"line": 9, "column": 22, "index": 152}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 154}, "end": {"line": 10, "column": 62, "index": 216}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo/vector-icons", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 218}, "end": {"line": 11, "column": 46, "index": 264}}], "key": "ow7vkrqkIckRjlSi/+MhMmRYtUE=", "exportNames": ["*"]}}, {"name": "../components/FooterNavigation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 266}, "end": {"line": 12, "column": 62, "index": 328}}], "key": "i0wsjvqzeC9AyK9fcghZiqZFu6Y=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = SearchScreen;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[4], \"react-native-safe-area-context\");\n  var _vectorIcons = require(_dependencyMap[5], \"@expo/vector-icons\");\n  var _FooterNavigation = _interopRequireDefault(require(_dependencyMap[6], \"../components/FooterNavigation\"));\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\apps\\\\mobile\\\\src\\\\screens\\\\SearchScreen.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function SearchScreen(_ref) {\n    _s();\n    var navigation = _ref.navigation;\n    var _useState = (0, _react.useState)(''),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      searchQuery = _useState2[0],\n      setSearchQuery = _useState2[1];\n    var _useState3 = (0, _react.useState)('All'),\n      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),\n      activeFilter = _useState4[0],\n      setActiveFilter = _useState4[1];\n    var filters = ['All', 'Restaurants', 'Food', 'Cuisines'];\n    var searchResults = [{\n      id: '1',\n      type: 'restaurant',\n      name: 'Burger Palace',\n      description: 'Best burgers in town',\n      image: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400',\n      rating: 4.8,\n      deliveryTime: '15-25 min'\n    }, {\n      id: '2',\n      type: 'food',\n      name: 'Margherita Pizza',\n      description: 'Fresh tomatoes, mozzarella, basil',\n      image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',\n      restaurant: 'Pizza Corner',\n      price: 18.99\n    }];\n    var filteredResults = searchResults.filter(item => {\n      var matchesSearch = !searchQuery || item.name.toLowerCase().includes(searchQuery.toLowerCase()) || item.description.toLowerCase().includes(searchQuery.toLowerCase());\n      var matchesFilter = activeFilter === 'All' || activeFilter === 'Restaurants' && item.type === 'restaurant' || activeFilter === 'Food' && item.type === 'food';\n      return matchesSearch && matchesFilter;\n    });\n    var popularSearches = ['Pizza', 'Burger', 'Sushi', 'Salad'];\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: {\n        flex: 1,\n        backgroundColor: '#f9fafb'\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f3a823'\n        },\n        edges: ['top']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: {\n          flex: 1,\n          backgroundColor: '#f9fafb'\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            paddingHorizontal: 16,\n            paddingVertical: 12,\n            backgroundColor: '#fff',\n            borderBottomWidth: 1,\n            borderBottomColor: '#e5e7eb'\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 24,\n              fontWeight: 'bold',\n              color: '#111827',\n              marginBottom: 16\n            },\n            children: \"Search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 9\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              flexDirection: 'row',\n              alignItems: 'center',\n              backgroundColor: '#f3f4f6',\n              borderRadius: 12,\n              paddingHorizontal: 16,\n              paddingVertical: 12\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n              name: \"search-outline\",\n              size: 20,\n              color: \"#9ca3af\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 11\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TextInput, {\n              style: {\n                flex: 1,\n                marginLeft: 12,\n                fontSize: 16,\n                color: '#111827'\n              },\n              placeholder: \"Search for food, restaurants...\",\n              placeholderTextColor: \"#9ca3af\",\n              value: searchQuery,\n              onChangeText: setSearchQuery\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 11\n            }, this), searchQuery.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              onPress: () => setSearchQuery(''),\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                name: \"close-circle\",\n                size: 20,\n                color: \"#9ca3af\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.ScrollView, {\n          style: {\n            flex: 1\n          },\n          children: [searchQuery.length === 0 ?\n          /*#__PURE__*/\n          // Popular Searches\n          (0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              padding: 16\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 18,\n                fontWeight: 'bold',\n                color: '#111827',\n                marginBottom: 16\n              },\n              children: \"Popular Searches\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: {\n                flexDirection: 'row',\n                flexWrap: 'wrap'\n              },\n              children: popularSearches.map(search => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n                onPress: () => setSearchQuery(search),\n                style: {\n                  backgroundColor: '#fff',\n                  paddingHorizontal: 16,\n                  paddingVertical: 10,\n                  borderRadius: 20,\n                  marginRight: 12,\n                  marginBottom: 12,\n                  borderWidth: 1,\n                  borderColor: '#e5e7eb'\n                },\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                  style: {\n                    color: '#6b7280',\n                    fontWeight: '500'\n                  },\n                  children: search\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this)\n              }, search, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 11\n          }, this) :\n          /*#__PURE__*/\n          // Search Results\n          (0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              padding: 16\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 18,\n                fontWeight: 'bold',\n                color: '#111827',\n                marginBottom: 16\n              },\n              children: [filteredResults.length, \" results for \\\"\", searchQuery, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 13\n            }, this), filteredResults.map(item => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              style: {\n                backgroundColor: '#fff',\n                borderRadius: 16,\n                marginBottom: 16,\n                shadowColor: '#000',\n                shadowOffset: {\n                  width: 0,\n                  height: 2\n                },\n                shadowOpacity: 0.1,\n                shadowRadius: 4,\n                elevation: 3\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: {\n                  flexDirection: 'row',\n                  padding: 16\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Image, {\n                  source: {\n                    uri: item.image\n                  },\n                  style: {\n                    width: 80,\n                    height: 80,\n                    borderRadius: 12,\n                    marginRight: 16\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                  style: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                    style: {\n                      fontSize: 16,\n                      fontWeight: 'bold',\n                      color: '#111827',\n                      marginBottom: 4\n                    },\n                    children: item.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                    style: {\n                      color: '#6b7280',\n                      fontSize: 14,\n                      marginBottom: 8\n                    },\n                    children: item.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 21\n                  }, this), item.type === 'restaurant' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                    style: {\n                      color: '#9ca3af',\n                      fontSize: 12\n                    },\n                    children: item.deliveryTime\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 23\n                  }, this), item.type === 'food' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                    style: {\n                      flexDirection: 'row',\n                      justifyContent: 'space-between',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        color: '#9ca3af',\n                        fontSize: 12\n                      },\n                      children: [\"from \", item.restaurant]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        fontSize: 16,\n                        fontWeight: 'bold',\n                        color: '#f97316'\n                      },\n                      children: [\"$\", item.price?.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              height: 100\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_FooterNavigation.default, {\n        navigation: navigation,\n        activeScreen: \"Search\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f9fafb'\n        },\n        edges: ['bottom']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 5\n    }, this);\n  }\n  _s(SearchScreen, \"gQIGoJIiCcmlzy7EOItQNvpwh6U=\");\n  _c = SearchScreen;\n  var _c;\n  $RefreshReg$(_c, \"SearchScreen\");\n});", "lineCount": 399, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireWildcard"], [8, 38, 1, 0], [8, 39, 1, 0, "require"], [8, 46, 1, 0], [8, 47, 1, 0, "_dependencyMap"], [8, 61, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 10, 0], [10, 6, 10, 0, "_reactNativeSafeAreaContext"], [10, 33, 10, 0], [10, 36, 10, 0, "require"], [10, 43, 10, 0], [10, 44, 10, 0, "_dependencyMap"], [10, 58, 10, 0], [11, 2, 11, 0], [11, 6, 11, 0, "_vectorIcons"], [11, 18, 11, 0], [11, 21, 11, 0, "require"], [11, 28, 11, 0], [11, 29, 11, 0, "_dependencyMap"], [11, 43, 11, 0], [12, 2, 12, 0], [12, 6, 12, 0, "_FooterNavigation"], [12, 23, 12, 0], [12, 26, 12, 0, "_interopRequireDefault"], [12, 48, 12, 0], [12, 49, 12, 0, "require"], [12, 56, 12, 0], [12, 57, 12, 0, "_dependencyMap"], [12, 71, 12, 0], [13, 2, 12, 62], [13, 6, 12, 62, "_jsxDevRuntime"], [13, 20, 12, 62], [13, 23, 12, 62, "require"], [13, 30, 12, 62], [13, 31, 12, 62, "_dependencyMap"], [13, 45, 12, 62], [14, 2, 12, 62], [14, 6, 12, 62, "_jsxFileName"], [14, 18, 12, 62], [15, 4, 12, 62, "_s"], [15, 6, 12, 62], [15, 9, 12, 62, "$RefreshSig$"], [15, 21, 12, 62], [16, 2, 12, 62], [16, 11, 12, 62, "_interopRequireWildcard"], [16, 35, 12, 62, "e"], [16, 36, 12, 62], [16, 38, 12, 62, "t"], [16, 39, 12, 62], [16, 68, 12, 62, "WeakMap"], [16, 75, 12, 62], [16, 81, 12, 62, "r"], [16, 82, 12, 62], [16, 89, 12, 62, "WeakMap"], [16, 96, 12, 62], [16, 100, 12, 62, "n"], [16, 101, 12, 62], [16, 108, 12, 62, "WeakMap"], [16, 115, 12, 62], [16, 127, 12, 62, "_interopRequireWildcard"], [16, 150, 12, 62], [16, 162, 12, 62, "_interopRequireWildcard"], [16, 163, 12, 62, "e"], [16, 164, 12, 62], [16, 166, 12, 62, "t"], [16, 167, 12, 62], [16, 176, 12, 62, "t"], [16, 177, 12, 62], [16, 181, 12, 62, "e"], [16, 182, 12, 62], [16, 186, 12, 62, "e"], [16, 187, 12, 62], [16, 188, 12, 62, "__esModule"], [16, 198, 12, 62], [16, 207, 12, 62, "e"], [16, 208, 12, 62], [16, 214, 12, 62, "o"], [16, 215, 12, 62], [16, 217, 12, 62, "i"], [16, 218, 12, 62], [16, 220, 12, 62, "f"], [16, 221, 12, 62], [16, 226, 12, 62, "__proto__"], [16, 235, 12, 62], [16, 243, 12, 62, "default"], [16, 250, 12, 62], [16, 252, 12, 62, "e"], [16, 253, 12, 62], [16, 270, 12, 62, "e"], [16, 271, 12, 62], [16, 294, 12, 62, "e"], [16, 295, 12, 62], [16, 320, 12, 62, "e"], [16, 321, 12, 62], [16, 330, 12, 62, "f"], [16, 331, 12, 62], [16, 337, 12, 62, "o"], [16, 338, 12, 62], [16, 341, 12, 62, "t"], [16, 342, 12, 62], [16, 345, 12, 62, "n"], [16, 346, 12, 62], [16, 349, 12, 62, "r"], [16, 350, 12, 62], [16, 358, 12, 62, "o"], [16, 359, 12, 62], [16, 360, 12, 62, "has"], [16, 363, 12, 62], [16, 364, 12, 62, "e"], [16, 365, 12, 62], [16, 375, 12, 62, "o"], [16, 376, 12, 62], [16, 377, 12, 62, "get"], [16, 380, 12, 62], [16, 381, 12, 62, "e"], [16, 382, 12, 62], [16, 385, 12, 62, "o"], [16, 386, 12, 62], [16, 387, 12, 62, "set"], [16, 390, 12, 62], [16, 391, 12, 62, "e"], [16, 392, 12, 62], [16, 394, 12, 62, "f"], [16, 395, 12, 62], [16, 409, 12, 62, "_t"], [16, 411, 12, 62], [16, 415, 12, 62, "e"], [16, 416, 12, 62], [16, 432, 12, 62, "_t"], [16, 434, 12, 62], [16, 441, 12, 62, "hasOwnProperty"], [16, 455, 12, 62], [16, 456, 12, 62, "call"], [16, 460, 12, 62], [16, 461, 12, 62, "e"], [16, 462, 12, 62], [16, 464, 12, 62, "_t"], [16, 466, 12, 62], [16, 473, 12, 62, "i"], [16, 474, 12, 62], [16, 478, 12, 62, "o"], [16, 479, 12, 62], [16, 482, 12, 62, "Object"], [16, 488, 12, 62], [16, 489, 12, 62, "defineProperty"], [16, 503, 12, 62], [16, 508, 12, 62, "Object"], [16, 514, 12, 62], [16, 515, 12, 62, "getOwnPropertyDescriptor"], [16, 539, 12, 62], [16, 540, 12, 62, "e"], [16, 541, 12, 62], [16, 543, 12, 62, "_t"], [16, 545, 12, 62], [16, 552, 12, 62, "i"], [16, 553, 12, 62], [16, 554, 12, 62, "get"], [16, 557, 12, 62], [16, 561, 12, 62, "i"], [16, 562, 12, 62], [16, 563, 12, 62, "set"], [16, 566, 12, 62], [16, 570, 12, 62, "o"], [16, 571, 12, 62], [16, 572, 12, 62, "f"], [16, 573, 12, 62], [16, 575, 12, 62, "_t"], [16, 577, 12, 62], [16, 579, 12, 62, "i"], [16, 580, 12, 62], [16, 584, 12, 62, "f"], [16, 585, 12, 62], [16, 586, 12, 62, "_t"], [16, 588, 12, 62], [16, 592, 12, 62, "e"], [16, 593, 12, 62], [16, 594, 12, 62, "_t"], [16, 596, 12, 62], [16, 607, 12, 62, "f"], [16, 608, 12, 62], [16, 613, 12, 62, "e"], [16, 614, 12, 62], [16, 616, 12, 62, "t"], [16, 617, 12, 62], [17, 2, 14, 15], [17, 11, 14, 24, "SearchScreen"], [17, 23, 14, 36, "SearchScreen"], [17, 24, 14, 36, "_ref"], [17, 28, 14, 36], [17, 30, 14, 58], [18, 4, 14, 58, "_s"], [18, 6, 14, 58], [19, 4, 14, 58], [19, 8, 14, 39, "navigation"], [19, 18, 14, 49], [19, 21, 14, 49, "_ref"], [19, 25, 14, 49], [19, 26, 14, 39, "navigation"], [19, 36, 14, 49], [20, 4, 15, 2], [20, 8, 15, 2, "_useState"], [20, 17, 15, 2], [20, 20, 15, 40], [20, 24, 15, 40, "useState"], [20, 39, 15, 48], [20, 41, 15, 49], [20, 43, 15, 51], [20, 44, 15, 52], [21, 6, 15, 52, "_useState2"], [21, 16, 15, 52], [21, 23, 15, 52, "_slicedToArray2"], [21, 38, 15, 52], [21, 39, 15, 52, "default"], [21, 46, 15, 52], [21, 48, 15, 52, "_useState"], [21, 57, 15, 52], [22, 6, 15, 9, "searchQuery"], [22, 17, 15, 20], [22, 20, 15, 20, "_useState2"], [22, 30, 15, 20], [23, 6, 15, 22, "setSearch<PERSON>uery"], [23, 20, 15, 36], [23, 23, 15, 36, "_useState2"], [23, 33, 15, 36], [24, 4, 16, 2], [24, 8, 16, 2, "_useState3"], [24, 18, 16, 2], [24, 21, 16, 42], [24, 25, 16, 42, "useState"], [24, 40, 16, 50], [24, 42, 16, 51], [24, 47, 16, 56], [24, 48, 16, 57], [25, 6, 16, 57, "_useState4"], [25, 16, 16, 57], [25, 23, 16, 57, "_slicedToArray2"], [25, 38, 16, 57], [25, 39, 16, 57, "default"], [25, 46, 16, 57], [25, 48, 16, 57, "_useState3"], [25, 58, 16, 57], [26, 6, 16, 9, "activeFilter"], [26, 18, 16, 21], [26, 21, 16, 21, "_useState4"], [26, 31, 16, 21], [27, 6, 16, 23, "setActiveFilter"], [27, 21, 16, 38], [27, 24, 16, 38, "_useState4"], [27, 34, 16, 38], [28, 4, 18, 2], [28, 8, 18, 8, "filters"], [28, 15, 18, 15], [28, 18, 18, 18], [28, 19, 18, 19], [28, 24, 18, 24], [28, 26, 18, 26], [28, 39, 18, 39], [28, 41, 18, 41], [28, 47, 18, 47], [28, 49, 18, 49], [28, 59, 18, 59], [28, 60, 18, 60], [29, 4, 20, 2], [29, 8, 20, 8, "searchResults"], [29, 21, 20, 21], [29, 24, 20, 24], [29, 25, 21, 4], [30, 6, 22, 6, "id"], [30, 8, 22, 8], [30, 10, 22, 10], [30, 13, 22, 13], [31, 6, 23, 6, "type"], [31, 10, 23, 10], [31, 12, 23, 12], [31, 24, 23, 24], [32, 6, 24, 6, "name"], [32, 10, 24, 10], [32, 12, 24, 12], [32, 27, 24, 27], [33, 6, 25, 6, "description"], [33, 17, 25, 17], [33, 19, 25, 19], [33, 41, 25, 41], [34, 6, 26, 6, "image"], [34, 11, 26, 11], [34, 13, 26, 13], [34, 81, 26, 81], [35, 6, 27, 6, "rating"], [35, 12, 27, 12], [35, 14, 27, 14], [35, 17, 27, 17], [36, 6, 28, 6, "deliveryTime"], [36, 18, 28, 18], [36, 20, 28, 20], [37, 4, 29, 4], [37, 5, 29, 5], [37, 7, 30, 4], [38, 6, 31, 6, "id"], [38, 8, 31, 8], [38, 10, 31, 10], [38, 13, 31, 13], [39, 6, 32, 6, "type"], [39, 10, 32, 10], [39, 12, 32, 12], [39, 18, 32, 18], [40, 6, 33, 6, "name"], [40, 10, 33, 10], [40, 12, 33, 12], [40, 30, 33, 30], [41, 6, 34, 6, "description"], [41, 17, 34, 17], [41, 19, 34, 19], [41, 54, 34, 54], [42, 6, 35, 6, "image"], [42, 11, 35, 11], [42, 13, 35, 13], [42, 81, 35, 81], [43, 6, 36, 6, "restaurant"], [43, 16, 36, 16], [43, 18, 36, 18], [43, 32, 36, 32], [44, 6, 37, 6, "price"], [44, 11, 37, 11], [44, 13, 37, 13], [45, 4, 38, 4], [45, 5, 38, 5], [45, 6, 39, 3], [46, 4, 41, 2], [46, 8, 41, 8, "filteredResults"], [46, 23, 41, 23], [46, 26, 41, 26, "searchResults"], [46, 39, 41, 39], [46, 40, 41, 40, "filter"], [46, 46, 41, 46], [46, 47, 41, 47, "item"], [46, 51, 41, 51], [46, 55, 41, 55], [47, 6, 42, 4], [47, 10, 42, 10, "matchesSearch"], [47, 23, 42, 23], [47, 26, 42, 26], [47, 27, 42, 27, "searchQuery"], [47, 38, 42, 38], [47, 42, 43, 6, "item"], [47, 46, 43, 10], [47, 47, 43, 11, "name"], [47, 51, 43, 15], [47, 52, 43, 16, "toLowerCase"], [47, 63, 43, 27], [47, 64, 43, 28], [47, 65, 43, 29], [47, 66, 43, 30, "includes"], [47, 74, 43, 38], [47, 75, 43, 39, "searchQuery"], [47, 86, 43, 50], [47, 87, 43, 51, "toLowerCase"], [47, 98, 43, 62], [47, 99, 43, 63], [47, 100, 43, 64], [47, 101, 43, 65], [47, 105, 44, 6, "item"], [47, 109, 44, 10], [47, 110, 44, 11, "description"], [47, 121, 44, 22], [47, 122, 44, 23, "toLowerCase"], [47, 133, 44, 34], [47, 134, 44, 35], [47, 135, 44, 36], [47, 136, 44, 37, "includes"], [47, 144, 44, 45], [47, 145, 44, 46, "searchQuery"], [47, 156, 44, 57], [47, 157, 44, 58, "toLowerCase"], [47, 168, 44, 69], [47, 169, 44, 70], [47, 170, 44, 71], [47, 171, 44, 72], [48, 6, 46, 4], [48, 10, 46, 10, "matchesFilter"], [48, 23, 46, 23], [48, 26, 46, 26, "activeFilter"], [48, 38, 46, 38], [48, 43, 46, 43], [48, 48, 46, 48], [48, 52, 47, 7, "activeFilter"], [48, 64, 47, 19], [48, 69, 47, 24], [48, 82, 47, 37], [48, 86, 47, 41, "item"], [48, 90, 47, 45], [48, 91, 47, 46, "type"], [48, 95, 47, 50], [48, 100, 47, 55], [48, 112, 47, 68], [48, 116, 48, 7, "activeFilter"], [48, 128, 48, 19], [48, 133, 48, 24], [48, 139, 48, 30], [48, 143, 48, 34, "item"], [48, 147, 48, 38], [48, 148, 48, 39, "type"], [48, 152, 48, 43], [48, 157, 48, 48], [48, 163, 48, 55], [49, 6, 50, 4], [49, 13, 50, 11, "matchesSearch"], [49, 26, 50, 24], [49, 30, 50, 28, "matchesFilter"], [49, 43, 50, 41], [50, 4, 51, 2], [50, 5, 51, 3], [50, 6, 51, 4], [51, 4, 53, 2], [51, 8, 53, 8, "popularSearches"], [51, 23, 53, 23], [51, 26, 53, 26], [51, 27, 53, 27], [51, 34, 53, 34], [51, 36, 53, 36], [51, 44, 53, 44], [51, 46, 53, 46], [51, 53, 53, 53], [51, 55, 53, 55], [51, 62, 53, 62], [51, 63, 53, 63], [52, 4, 55, 2], [52, 24, 56, 4], [52, 28, 56, 4, "_jsxDevRuntime"], [52, 42, 56, 4], [52, 43, 56, 4, "jsxDEV"], [52, 49, 56, 4], [52, 51, 56, 5, "_reactNative"], [52, 63, 56, 5], [52, 64, 56, 5, "View"], [52, 68, 56, 9], [53, 6, 56, 10, "style"], [53, 11, 56, 15], [53, 13, 56, 17], [54, 8, 56, 19, "flex"], [54, 12, 56, 23], [54, 14, 56, 25], [54, 15, 56, 26], [55, 8, 56, 28, "backgroundColor"], [55, 23, 56, 43], [55, 25, 56, 45], [56, 6, 56, 55], [56, 7, 56, 57], [57, 6, 56, 57, "children"], [57, 14, 56, 57], [57, 30, 58, 6], [57, 34, 58, 6, "_jsxDevRuntime"], [57, 48, 58, 6], [57, 49, 58, 6, "jsxDEV"], [57, 55, 58, 6], [57, 57, 58, 7, "_reactNativeSafeAreaContext"], [57, 84, 58, 7], [57, 85, 58, 7, "SafeAreaView"], [57, 97, 58, 19], [58, 8, 58, 20, "style"], [58, 13, 58, 25], [58, 15, 58, 27], [59, 10, 58, 29, "backgroundColor"], [59, 25, 58, 44], [59, 27, 58, 46], [60, 8, 58, 56], [60, 9, 58, 58], [61, 8, 58, 59, "edges"], [61, 13, 58, 64], [61, 15, 58, 66], [61, 16, 58, 67], [61, 21, 58, 72], [62, 6, 58, 74], [63, 8, 58, 74, "fileName"], [63, 16, 58, 74], [63, 18, 58, 74, "_jsxFileName"], [63, 30, 58, 74], [64, 8, 58, 74, "lineNumber"], [64, 18, 58, 74], [65, 8, 58, 74, "columnNumber"], [65, 20, 58, 74], [66, 6, 58, 74], [66, 13, 58, 76], [66, 14, 58, 77], [66, 29, 61, 6], [66, 33, 61, 6, "_jsxDevRuntime"], [66, 47, 61, 6], [66, 48, 61, 6, "jsxDEV"], [66, 54, 61, 6], [66, 56, 61, 7, "_reactNative"], [66, 68, 61, 7], [66, 69, 61, 7, "View"], [66, 73, 61, 11], [67, 8, 61, 12, "style"], [67, 13, 61, 17], [67, 15, 61, 19], [68, 10, 61, 21, "flex"], [68, 14, 61, 25], [68, 16, 61, 27], [68, 17, 61, 28], [69, 10, 61, 30, "backgroundColor"], [69, 25, 61, 45], [69, 27, 61, 47], [70, 8, 61, 57], [70, 9, 61, 59], [71, 8, 61, 59, "children"], [71, 16, 61, 59], [71, 32, 63, 8], [71, 36, 63, 8, "_jsxDevRuntime"], [71, 50, 63, 8], [71, 51, 63, 8, "jsxDEV"], [71, 57, 63, 8], [71, 59, 63, 9, "_reactNative"], [71, 71, 63, 9], [71, 72, 63, 9, "View"], [71, 76, 63, 13], [72, 10, 63, 14, "style"], [72, 15, 63, 19], [72, 17, 63, 21], [73, 12, 64, 10, "paddingHorizontal"], [73, 29, 64, 27], [73, 31, 64, 29], [73, 33, 64, 31], [74, 12, 65, 10, "paddingVertical"], [74, 27, 65, 25], [74, 29, 65, 27], [74, 31, 65, 29], [75, 12, 66, 10, "backgroundColor"], [75, 27, 66, 25], [75, 29, 66, 27], [75, 35, 66, 33], [76, 12, 67, 10, "borderBottomWidth"], [76, 29, 67, 27], [76, 31, 67, 29], [76, 32, 67, 30], [77, 12, 68, 10, "borderBottomColor"], [77, 29, 68, 27], [77, 31, 68, 29], [78, 10, 69, 8], [78, 11, 69, 10], [79, 10, 69, 10, "children"], [79, 18, 69, 10], [79, 34, 70, 8], [79, 38, 70, 8, "_jsxDevRuntime"], [79, 52, 70, 8], [79, 53, 70, 8, "jsxDEV"], [79, 59, 70, 8], [79, 61, 70, 9, "_reactNative"], [79, 73, 70, 9], [79, 74, 70, 9, "Text"], [79, 78, 70, 13], [80, 12, 70, 14, "style"], [80, 17, 70, 19], [80, 19, 70, 21], [81, 14, 70, 23, "fontSize"], [81, 22, 70, 31], [81, 24, 70, 33], [81, 26, 70, 35], [82, 14, 70, 37, "fontWeight"], [82, 24, 70, 47], [82, 26, 70, 49], [82, 32, 70, 55], [83, 14, 70, 57, "color"], [83, 19, 70, 62], [83, 21, 70, 64], [83, 30, 70, 73], [84, 14, 70, 75, "marginBottom"], [84, 26, 70, 87], [84, 28, 70, 89], [85, 12, 70, 92], [85, 13, 70, 94], [86, 12, 70, 94, "children"], [86, 20, 70, 94], [86, 22, 70, 95], [87, 10, 72, 8], [88, 12, 72, 8, "fileName"], [88, 20, 72, 8], [88, 22, 72, 8, "_jsxFileName"], [88, 34, 72, 8], [89, 12, 72, 8, "lineNumber"], [89, 22, 72, 8], [90, 12, 72, 8, "columnNumber"], [90, 24, 72, 8], [91, 10, 72, 8], [91, 17, 72, 14], [91, 18, 72, 15], [91, 33, 75, 8], [91, 37, 75, 8, "_jsxDevRuntime"], [91, 51, 75, 8], [91, 52, 75, 8, "jsxDEV"], [91, 58, 75, 8], [91, 60, 75, 9, "_reactNative"], [91, 72, 75, 9], [91, 73, 75, 9, "View"], [91, 77, 75, 13], [92, 12, 75, 14, "style"], [92, 17, 75, 19], [92, 19, 75, 21], [93, 14, 76, 10, "flexDirection"], [93, 27, 76, 23], [93, 29, 76, 25], [93, 34, 76, 30], [94, 14, 77, 10, "alignItems"], [94, 24, 77, 20], [94, 26, 77, 22], [94, 34, 77, 30], [95, 14, 78, 10, "backgroundColor"], [95, 29, 78, 25], [95, 31, 78, 27], [95, 40, 78, 36], [96, 14, 79, 10, "borderRadius"], [96, 26, 79, 22], [96, 28, 79, 24], [96, 30, 79, 26], [97, 14, 80, 10, "paddingHorizontal"], [97, 31, 80, 27], [97, 33, 80, 29], [97, 35, 80, 31], [98, 14, 81, 10, "paddingVertical"], [98, 29, 81, 25], [98, 31, 81, 27], [99, 12, 82, 8], [99, 13, 82, 10], [100, 12, 82, 10, "children"], [100, 20, 82, 10], [100, 36, 83, 10], [100, 40, 83, 10, "_jsxDevRuntime"], [100, 54, 83, 10], [100, 55, 83, 10, "jsxDEV"], [100, 61, 83, 10], [100, 63, 83, 11, "_vectorIcons"], [100, 75, 83, 11], [100, 76, 83, 11, "Ionicons"], [100, 84, 83, 19], [101, 14, 83, 20, "name"], [101, 18, 83, 24], [101, 20, 83, 25], [101, 36, 83, 41], [102, 14, 83, 42, "size"], [102, 18, 83, 46], [102, 20, 83, 48], [102, 22, 83, 51], [103, 14, 83, 52, "color"], [103, 19, 83, 57], [103, 21, 83, 58], [104, 12, 83, 67], [105, 14, 83, 67, "fileName"], [105, 22, 83, 67], [105, 24, 83, 67, "_jsxFileName"], [105, 36, 83, 67], [106, 14, 83, 67, "lineNumber"], [106, 24, 83, 67], [107, 14, 83, 67, "columnNumber"], [107, 26, 83, 67], [108, 12, 83, 67], [108, 19, 83, 69], [108, 20, 83, 70], [108, 35, 84, 10], [108, 39, 84, 10, "_jsxDevRuntime"], [108, 53, 84, 10], [108, 54, 84, 10, "jsxDEV"], [108, 60, 84, 10], [108, 62, 84, 11, "_reactNative"], [108, 74, 84, 11], [108, 75, 84, 11, "TextInput"], [108, 84, 84, 20], [109, 14, 85, 12, "style"], [109, 19, 85, 17], [109, 21, 85, 19], [110, 16, 85, 21, "flex"], [110, 20, 85, 25], [110, 22, 85, 27], [110, 23, 85, 28], [111, 16, 85, 30, "marginLeft"], [111, 26, 85, 40], [111, 28, 85, 42], [111, 30, 85, 44], [112, 16, 85, 46, "fontSize"], [112, 24, 85, 54], [112, 26, 85, 56], [112, 28, 85, 58], [113, 16, 85, 60, "color"], [113, 21, 85, 65], [113, 23, 85, 67], [114, 14, 85, 77], [114, 15, 85, 79], [115, 14, 86, 12, "placeholder"], [115, 25, 86, 23], [115, 27, 86, 24], [115, 60, 86, 57], [116, 14, 87, 12, "placeholderTextColor"], [116, 34, 87, 32], [116, 36, 87, 33], [116, 45, 87, 42], [117, 14, 88, 12, "value"], [117, 19, 88, 17], [117, 21, 88, 19, "searchQuery"], [117, 32, 88, 31], [118, 14, 89, 12, "onChangeText"], [118, 26, 89, 24], [118, 28, 89, 26, "setSearch<PERSON>uery"], [119, 12, 89, 41], [120, 14, 89, 41, "fileName"], [120, 22, 89, 41], [120, 24, 89, 41, "_jsxFileName"], [120, 36, 89, 41], [121, 14, 89, 41, "lineNumber"], [121, 24, 89, 41], [122, 14, 89, 41, "columnNumber"], [122, 26, 89, 41], [123, 12, 89, 41], [123, 19, 90, 11], [123, 20, 90, 12], [123, 22, 91, 11, "searchQuery"], [123, 33, 91, 22], [123, 34, 91, 23, "length"], [123, 40, 91, 29], [123, 43, 91, 32], [123, 44, 91, 33], [123, 61, 92, 12], [123, 65, 92, 12, "_jsxDevRuntime"], [123, 79, 92, 12], [123, 80, 92, 12, "jsxDEV"], [123, 86, 92, 12], [123, 88, 92, 13, "_reactNative"], [123, 100, 92, 13], [123, 101, 92, 13, "TouchableOpacity"], [123, 117, 92, 29], [124, 14, 92, 30, "onPress"], [124, 21, 92, 37], [124, 23, 92, 39, "onPress"], [124, 24, 92, 39], [124, 29, 92, 45, "setSearch<PERSON>uery"], [124, 43, 92, 59], [124, 44, 92, 60], [124, 46, 92, 62], [124, 47, 92, 64], [125, 14, 92, 64, "children"], [125, 22, 92, 64], [125, 37, 93, 14], [125, 41, 93, 14, "_jsxDevRuntime"], [125, 55, 93, 14], [125, 56, 93, 14, "jsxDEV"], [125, 62, 93, 14], [125, 64, 93, 15, "_vectorIcons"], [125, 76, 93, 15], [125, 77, 93, 15, "Ionicons"], [125, 85, 93, 23], [126, 16, 93, 24, "name"], [126, 20, 93, 28], [126, 22, 93, 29], [126, 36, 93, 43], [127, 16, 93, 44, "size"], [127, 20, 93, 48], [127, 22, 93, 50], [127, 24, 93, 53], [128, 16, 93, 54, "color"], [128, 21, 93, 59], [128, 23, 93, 60], [129, 14, 93, 69], [130, 16, 93, 69, "fileName"], [130, 24, 93, 69], [130, 26, 93, 69, "_jsxFileName"], [130, 38, 93, 69], [131, 16, 93, 69, "lineNumber"], [131, 26, 93, 69], [132, 16, 93, 69, "columnNumber"], [132, 28, 93, 69], [133, 14, 93, 69], [133, 21, 93, 71], [134, 12, 93, 72], [135, 14, 93, 72, "fileName"], [135, 22, 93, 72], [135, 24, 93, 72, "_jsxFileName"], [135, 36, 93, 72], [136, 14, 93, 72, "lineNumber"], [136, 24, 93, 72], [137, 14, 93, 72, "columnNumber"], [137, 26, 93, 72], [138, 12, 93, 72], [138, 19, 94, 30], [138, 20, 95, 11], [139, 10, 95, 11], [140, 12, 95, 11, "fileName"], [140, 20, 95, 11], [140, 22, 95, 11, "_jsxFileName"], [140, 34, 95, 11], [141, 12, 95, 11, "lineNumber"], [141, 22, 95, 11], [142, 12, 95, 11, "columnNumber"], [142, 24, 95, 11], [143, 10, 95, 11], [143, 17, 96, 14], [143, 18, 96, 15], [144, 8, 96, 15], [145, 10, 96, 15, "fileName"], [145, 18, 96, 15], [145, 20, 96, 15, "_jsxFileName"], [145, 32, 96, 15], [146, 10, 96, 15, "lineNumber"], [146, 20, 96, 15], [147, 10, 96, 15, "columnNumber"], [147, 22, 96, 15], [148, 8, 96, 15], [148, 15, 97, 12], [148, 16, 97, 13], [148, 31, 99, 6], [148, 35, 99, 6, "_jsxDevRuntime"], [148, 49, 99, 6], [148, 50, 99, 6, "jsxDEV"], [148, 56, 99, 6], [148, 58, 99, 7, "_reactNative"], [148, 70, 99, 7], [148, 71, 99, 7, "ScrollView"], [148, 81, 99, 17], [149, 10, 99, 18, "style"], [149, 15, 99, 23], [149, 17, 99, 25], [150, 12, 99, 27, "flex"], [150, 16, 99, 31], [150, 18, 99, 33], [151, 10, 99, 35], [151, 11, 99, 37], [152, 10, 99, 37, "children"], [152, 18, 99, 37], [152, 21, 100, 9, "searchQuery"], [152, 32, 100, 20], [152, 33, 100, 21, "length"], [152, 39, 100, 27], [152, 44, 100, 32], [152, 45, 100, 33], [153, 10, 100, 33], [154, 10, 101, 10], [155, 10, 102, 10], [155, 14, 102, 10, "_jsxDevRuntime"], [155, 28, 102, 10], [155, 29, 102, 10, "jsxDEV"], [155, 35, 102, 10], [155, 37, 102, 11, "_reactNative"], [155, 49, 102, 11], [155, 50, 102, 11, "View"], [155, 54, 102, 15], [156, 12, 102, 16, "style"], [156, 17, 102, 21], [156, 19, 102, 23], [157, 14, 102, 25, "padding"], [157, 21, 102, 32], [157, 23, 102, 34], [158, 12, 102, 37], [158, 13, 102, 39], [159, 12, 102, 39, "children"], [159, 20, 102, 39], [159, 36, 103, 12], [159, 40, 103, 12, "_jsxDevRuntime"], [159, 54, 103, 12], [159, 55, 103, 12, "jsxDEV"], [159, 61, 103, 12], [159, 63, 103, 13, "_reactNative"], [159, 75, 103, 13], [159, 76, 103, 13, "Text"], [159, 80, 103, 17], [160, 14, 103, 18, "style"], [160, 19, 103, 23], [160, 21, 103, 25], [161, 16, 103, 27, "fontSize"], [161, 24, 103, 35], [161, 26, 103, 37], [161, 28, 103, 39], [162, 16, 103, 41, "fontWeight"], [162, 26, 103, 51], [162, 28, 103, 53], [162, 34, 103, 59], [163, 16, 103, 61, "color"], [163, 21, 103, 66], [163, 23, 103, 68], [163, 32, 103, 77], [164, 16, 103, 79, "marginBottom"], [164, 28, 103, 91], [164, 30, 103, 93], [165, 14, 103, 96], [165, 15, 103, 98], [166, 14, 103, 98, "children"], [166, 22, 103, 98], [166, 24, 103, 99], [167, 12, 105, 12], [168, 14, 105, 12, "fileName"], [168, 22, 105, 12], [168, 24, 105, 12, "_jsxFileName"], [168, 36, 105, 12], [169, 14, 105, 12, "lineNumber"], [169, 24, 105, 12], [170, 14, 105, 12, "columnNumber"], [170, 26, 105, 12], [171, 12, 105, 12], [171, 19, 105, 18], [171, 20, 105, 19], [171, 35, 106, 12], [171, 39, 106, 12, "_jsxDevRuntime"], [171, 53, 106, 12], [171, 54, 106, 12, "jsxDEV"], [171, 60, 106, 12], [171, 62, 106, 13, "_reactNative"], [171, 74, 106, 13], [171, 75, 106, 13, "View"], [171, 79, 106, 17], [172, 14, 106, 18, "style"], [172, 19, 106, 23], [172, 21, 106, 25], [173, 16, 106, 27, "flexDirection"], [173, 29, 106, 40], [173, 31, 106, 42], [173, 36, 106, 47], [174, 16, 106, 49, "flexWrap"], [174, 24, 106, 57], [174, 26, 106, 59], [175, 14, 106, 66], [175, 15, 106, 68], [176, 14, 106, 68, "children"], [176, 22, 106, 68], [176, 24, 107, 15, "popularSearches"], [176, 39, 107, 30], [176, 40, 107, 31, "map"], [176, 43, 107, 34], [176, 44, 107, 36, "search"], [176, 50, 107, 42], [176, 67, 108, 16], [176, 71, 108, 16, "_jsxDevRuntime"], [176, 85, 108, 16], [176, 86, 108, 16, "jsxDEV"], [176, 92, 108, 16], [176, 94, 108, 17, "_reactNative"], [176, 106, 108, 17], [176, 107, 108, 17, "TouchableOpacity"], [176, 123, 108, 33], [177, 16, 110, 18, "onPress"], [177, 23, 110, 25], [177, 25, 110, 27, "onPress"], [177, 26, 110, 27], [177, 31, 110, 33, "setSearch<PERSON>uery"], [177, 45, 110, 47], [177, 46, 110, 48, "search"], [177, 52, 110, 54], [177, 53, 110, 56], [178, 16, 111, 18, "style"], [178, 21, 111, 23], [178, 23, 111, 25], [179, 18, 112, 20, "backgroundColor"], [179, 33, 112, 35], [179, 35, 112, 37], [179, 41, 112, 43], [180, 18, 113, 20, "paddingHorizontal"], [180, 35, 113, 37], [180, 37, 113, 39], [180, 39, 113, 41], [181, 18, 114, 20, "paddingVertical"], [181, 33, 114, 35], [181, 35, 114, 37], [181, 37, 114, 39], [182, 18, 115, 20, "borderRadius"], [182, 30, 115, 32], [182, 32, 115, 34], [182, 34, 115, 36], [183, 18, 116, 20, "marginRight"], [183, 29, 116, 31], [183, 31, 116, 33], [183, 33, 116, 35], [184, 18, 117, 20, "marginBottom"], [184, 30, 117, 32], [184, 32, 117, 34], [184, 34, 117, 36], [185, 18, 118, 20, "borderWidth"], [185, 29, 118, 31], [185, 31, 118, 33], [185, 32, 118, 34], [186, 18, 119, 20, "borderColor"], [186, 29, 119, 31], [186, 31, 119, 33], [187, 16, 120, 18], [187, 17, 120, 20], [188, 16, 120, 20, "children"], [188, 24, 120, 20], [188, 39, 122, 18], [188, 43, 122, 18, "_jsxDevRuntime"], [188, 57, 122, 18], [188, 58, 122, 18, "jsxDEV"], [188, 64, 122, 18], [188, 66, 122, 19, "_reactNative"], [188, 78, 122, 19], [188, 79, 122, 19, "Text"], [188, 83, 122, 23], [189, 18, 122, 24, "style"], [189, 23, 122, 29], [189, 25, 122, 31], [190, 20, 122, 33, "color"], [190, 25, 122, 38], [190, 27, 122, 40], [190, 36, 122, 49], [191, 20, 122, 51, "fontWeight"], [191, 30, 122, 61], [191, 32, 122, 63], [192, 18, 122, 69], [192, 19, 122, 71], [193, 18, 122, 71, "children"], [193, 26, 122, 71], [193, 28, 123, 21, "search"], [194, 16, 123, 27], [195, 18, 123, 27, "fileName"], [195, 26, 123, 27], [195, 28, 123, 27, "_jsxFileName"], [195, 40, 123, 27], [196, 18, 123, 27, "lineNumber"], [196, 28, 123, 27], [197, 18, 123, 27, "columnNumber"], [197, 30, 123, 27], [198, 16, 123, 27], [198, 23, 124, 24], [199, 14, 124, 25], [199, 17, 109, 23, "search"], [199, 23, 109, 29], [200, 16, 109, 29, "fileName"], [200, 24, 109, 29], [200, 26, 109, 29, "_jsxFileName"], [200, 38, 109, 29], [201, 16, 109, 29, "lineNumber"], [201, 26, 109, 29], [202, 16, 109, 29, "columnNumber"], [202, 28, 109, 29], [203, 14, 109, 29], [203, 21, 125, 34], [203, 22, 126, 15], [204, 12, 126, 16], [205, 14, 126, 16, "fileName"], [205, 22, 126, 16], [205, 24, 126, 16, "_jsxFileName"], [205, 36, 126, 16], [206, 14, 126, 16, "lineNumber"], [206, 24, 126, 16], [207, 14, 126, 16, "columnNumber"], [207, 26, 126, 16], [208, 12, 126, 16], [208, 19, 127, 18], [208, 20, 127, 19], [209, 10, 127, 19], [210, 12, 127, 19, "fileName"], [210, 20, 127, 19], [210, 22, 127, 19, "_jsxFileName"], [210, 34, 127, 19], [211, 12, 127, 19, "lineNumber"], [211, 22, 127, 19], [212, 12, 127, 19, "columnNumber"], [212, 24, 127, 19], [213, 10, 127, 19], [213, 17, 128, 16], [213, 18, 128, 17], [214, 10, 128, 17], [215, 10, 130, 10], [216, 10, 131, 10], [216, 14, 131, 10, "_jsxDevRuntime"], [216, 28, 131, 10], [216, 29, 131, 10, "jsxDEV"], [216, 35, 131, 10], [216, 37, 131, 11, "_reactNative"], [216, 49, 131, 11], [216, 50, 131, 11, "View"], [216, 54, 131, 15], [217, 12, 131, 16, "style"], [217, 17, 131, 21], [217, 19, 131, 23], [218, 14, 131, 25, "padding"], [218, 21, 131, 32], [218, 23, 131, 34], [219, 12, 131, 37], [219, 13, 131, 39], [220, 12, 131, 39, "children"], [220, 20, 131, 39], [220, 36, 132, 12], [220, 40, 132, 12, "_jsxDevRuntime"], [220, 54, 132, 12], [220, 55, 132, 12, "jsxDEV"], [220, 61, 132, 12], [220, 63, 132, 13, "_reactNative"], [220, 75, 132, 13], [220, 76, 132, 13, "Text"], [220, 80, 132, 17], [221, 14, 132, 18, "style"], [221, 19, 132, 23], [221, 21, 132, 25], [222, 16, 132, 27, "fontSize"], [222, 24, 132, 35], [222, 26, 132, 37], [222, 28, 132, 39], [223, 16, 132, 41, "fontWeight"], [223, 26, 132, 51], [223, 28, 132, 53], [223, 34, 132, 59], [224, 16, 132, 61, "color"], [224, 21, 132, 66], [224, 23, 132, 68], [224, 32, 132, 77], [225, 16, 132, 79, "marginBottom"], [225, 28, 132, 91], [225, 30, 132, 93], [226, 14, 132, 96], [226, 15, 132, 98], [227, 14, 132, 98, "children"], [227, 22, 132, 98], [227, 25, 133, 15, "filteredResults"], [227, 40, 133, 30], [227, 41, 133, 31, "length"], [227, 47, 133, 37], [227, 49, 133, 38], [227, 66, 133, 52], [227, 68, 133, 53, "searchQuery"], [227, 79, 133, 64], [227, 81, 133, 65], [227, 85, 134, 12], [228, 12, 134, 12], [229, 14, 134, 12, "fileName"], [229, 22, 134, 12], [229, 24, 134, 12, "_jsxFileName"], [229, 36, 134, 12], [230, 14, 134, 12, "lineNumber"], [230, 24, 134, 12], [231, 14, 134, 12, "columnNumber"], [231, 26, 134, 12], [232, 12, 134, 12], [232, 19, 134, 18], [232, 20, 134, 19], [232, 22, 136, 13, "filteredResults"], [232, 37, 136, 28], [232, 38, 136, 29, "map"], [232, 41, 136, 32], [232, 42, 136, 34, "item"], [232, 46, 136, 38], [232, 63, 137, 14], [232, 67, 137, 14, "_jsxDevRuntime"], [232, 81, 137, 14], [232, 82, 137, 14, "jsxDEV"], [232, 88, 137, 14], [232, 90, 137, 15, "_reactNative"], [232, 102, 137, 15], [232, 103, 137, 15, "TouchableOpacity"], [232, 119, 137, 31], [233, 14, 139, 16, "style"], [233, 19, 139, 21], [233, 21, 139, 23], [234, 16, 140, 18, "backgroundColor"], [234, 31, 140, 33], [234, 33, 140, 35], [234, 39, 140, 41], [235, 16, 141, 18, "borderRadius"], [235, 28, 141, 30], [235, 30, 141, 32], [235, 32, 141, 34], [236, 16, 142, 18, "marginBottom"], [236, 28, 142, 30], [236, 30, 142, 32], [236, 32, 142, 34], [237, 16, 143, 18, "shadowColor"], [237, 27, 143, 29], [237, 29, 143, 31], [237, 35, 143, 37], [238, 16, 144, 18, "shadowOffset"], [238, 28, 144, 30], [238, 30, 144, 32], [239, 18, 144, 34, "width"], [239, 23, 144, 39], [239, 25, 144, 41], [239, 26, 144, 42], [240, 18, 144, 44, "height"], [240, 24, 144, 50], [240, 26, 144, 52], [241, 16, 144, 54], [241, 17, 144, 55], [242, 16, 145, 18, "shadowOpacity"], [242, 29, 145, 31], [242, 31, 145, 33], [242, 34, 145, 36], [243, 16, 146, 18, "shadowRadius"], [243, 28, 146, 30], [243, 30, 146, 32], [243, 31, 146, 33], [244, 16, 147, 18, "elevation"], [244, 25, 147, 27], [244, 27, 147, 29], [245, 14, 148, 16], [245, 15, 148, 18], [246, 14, 148, 18, "children"], [246, 22, 148, 18], [246, 37, 150, 16], [246, 41, 150, 16, "_jsxDevRuntime"], [246, 55, 150, 16], [246, 56, 150, 16, "jsxDEV"], [246, 62, 150, 16], [246, 64, 150, 17, "_reactNative"], [246, 76, 150, 17], [246, 77, 150, 17, "View"], [246, 81, 150, 21], [247, 16, 150, 22, "style"], [247, 21, 150, 27], [247, 23, 150, 29], [248, 18, 150, 31, "flexDirection"], [248, 31, 150, 44], [248, 33, 150, 46], [248, 38, 150, 51], [249, 18, 150, 53, "padding"], [249, 25, 150, 60], [249, 27, 150, 62], [250, 16, 150, 65], [250, 17, 150, 67], [251, 16, 150, 67, "children"], [251, 24, 150, 67], [251, 40, 151, 18], [251, 44, 151, 18, "_jsxDevRuntime"], [251, 58, 151, 18], [251, 59, 151, 18, "jsxDEV"], [251, 65, 151, 18], [251, 67, 151, 19, "_reactNative"], [251, 79, 151, 19], [251, 80, 151, 19, "Image"], [251, 85, 151, 24], [252, 18, 152, 20, "source"], [252, 24, 152, 26], [252, 26, 152, 28], [253, 20, 152, 30, "uri"], [253, 23, 152, 33], [253, 25, 152, 35, "item"], [253, 29, 152, 39], [253, 30, 152, 40, "image"], [254, 18, 152, 46], [254, 19, 152, 48], [255, 18, 153, 20, "style"], [255, 23, 153, 25], [255, 25, 153, 27], [256, 20, 154, 22, "width"], [256, 25, 154, 27], [256, 27, 154, 29], [256, 29, 154, 31], [257, 20, 155, 22, "height"], [257, 26, 155, 28], [257, 28, 155, 30], [257, 30, 155, 32], [258, 20, 156, 22, "borderRadius"], [258, 32, 156, 34], [258, 34, 156, 36], [258, 36, 156, 38], [259, 20, 157, 22, "marginRight"], [259, 31, 157, 33], [259, 33, 157, 35], [260, 18, 158, 20], [261, 16, 158, 22], [262, 18, 158, 22, "fileName"], [262, 26, 158, 22], [262, 28, 158, 22, "_jsxFileName"], [262, 40, 158, 22], [263, 18, 158, 22, "lineNumber"], [263, 28, 158, 22], [264, 18, 158, 22, "columnNumber"], [264, 30, 158, 22], [265, 16, 158, 22], [265, 23, 159, 19], [265, 24, 159, 20], [265, 39, 160, 18], [265, 43, 160, 18, "_jsxDevRuntime"], [265, 57, 160, 18], [265, 58, 160, 18, "jsxDEV"], [265, 64, 160, 18], [265, 66, 160, 19, "_reactNative"], [265, 78, 160, 19], [265, 79, 160, 19, "View"], [265, 83, 160, 23], [266, 18, 160, 24, "style"], [266, 23, 160, 29], [266, 25, 160, 31], [267, 20, 160, 33, "flex"], [267, 24, 160, 37], [267, 26, 160, 39], [268, 18, 160, 41], [268, 19, 160, 43], [269, 18, 160, 43, "children"], [269, 26, 160, 43], [269, 42, 161, 20], [269, 46, 161, 20, "_jsxDevRuntime"], [269, 60, 161, 20], [269, 61, 161, 20, "jsxDEV"], [269, 67, 161, 20], [269, 69, 161, 21, "_reactNative"], [269, 81, 161, 21], [269, 82, 161, 21, "Text"], [269, 86, 161, 25], [270, 20, 161, 26, "style"], [270, 25, 161, 31], [270, 27, 161, 33], [271, 22, 161, 35, "fontSize"], [271, 30, 161, 43], [271, 32, 161, 45], [271, 34, 161, 47], [272, 22, 161, 49, "fontWeight"], [272, 32, 161, 59], [272, 34, 161, 61], [272, 40, 161, 67], [273, 22, 161, 69, "color"], [273, 27, 161, 74], [273, 29, 161, 76], [273, 38, 161, 85], [274, 22, 161, 87, "marginBottom"], [274, 34, 161, 99], [274, 36, 161, 101], [275, 20, 161, 103], [275, 21, 161, 105], [276, 20, 161, 105, "children"], [276, 28, 161, 105], [276, 30, 162, 23, "item"], [276, 34, 162, 27], [276, 35, 162, 28, "name"], [277, 18, 162, 32], [278, 20, 162, 32, "fileName"], [278, 28, 162, 32], [278, 30, 162, 32, "_jsxFileName"], [278, 42, 162, 32], [279, 20, 162, 32, "lineNumber"], [279, 30, 162, 32], [280, 20, 162, 32, "columnNumber"], [280, 32, 162, 32], [281, 18, 162, 32], [281, 25, 163, 26], [281, 26, 163, 27], [281, 41, 164, 20], [281, 45, 164, 20, "_jsxDevRuntime"], [281, 59, 164, 20], [281, 60, 164, 20, "jsxDEV"], [281, 66, 164, 20], [281, 68, 164, 21, "_reactNative"], [281, 80, 164, 21], [281, 81, 164, 21, "Text"], [281, 85, 164, 25], [282, 20, 164, 26, "style"], [282, 25, 164, 31], [282, 27, 164, 33], [283, 22, 164, 35, "color"], [283, 27, 164, 40], [283, 29, 164, 42], [283, 38, 164, 51], [284, 22, 164, 53, "fontSize"], [284, 30, 164, 61], [284, 32, 164, 63], [284, 34, 164, 65], [285, 22, 164, 67, "marginBottom"], [285, 34, 164, 79], [285, 36, 164, 81], [286, 20, 164, 83], [286, 21, 164, 85], [287, 20, 164, 85, "children"], [287, 28, 164, 85], [287, 30, 165, 23, "item"], [287, 34, 165, 27], [287, 35, 165, 28, "description"], [288, 18, 165, 39], [289, 20, 165, 39, "fileName"], [289, 28, 165, 39], [289, 30, 165, 39, "_jsxFileName"], [289, 42, 165, 39], [290, 20, 165, 39, "lineNumber"], [290, 30, 165, 39], [291, 20, 165, 39, "columnNumber"], [291, 32, 165, 39], [292, 18, 165, 39], [292, 25, 166, 26], [292, 26, 166, 27], [292, 28, 167, 21, "item"], [292, 32, 167, 25], [292, 33, 167, 26, "type"], [292, 37, 167, 30], [292, 42, 167, 35], [292, 54, 167, 47], [292, 71, 168, 22], [292, 75, 168, 22, "_jsxDevRuntime"], [292, 89, 168, 22], [292, 90, 168, 22, "jsxDEV"], [292, 96, 168, 22], [292, 98, 168, 23, "_reactNative"], [292, 110, 168, 23], [292, 111, 168, 23, "Text"], [292, 115, 168, 27], [293, 20, 168, 28, "style"], [293, 25, 168, 33], [293, 27, 168, 35], [294, 22, 168, 37, "color"], [294, 27, 168, 42], [294, 29, 168, 44], [294, 38, 168, 53], [295, 22, 168, 55, "fontSize"], [295, 30, 168, 63], [295, 32, 168, 65], [296, 20, 168, 68], [296, 21, 168, 70], [297, 20, 168, 70, "children"], [297, 28, 168, 70], [297, 30, 169, 25, "item"], [297, 34, 169, 29], [297, 35, 169, 30, "deliveryTime"], [298, 18, 169, 42], [299, 20, 169, 42, "fileName"], [299, 28, 169, 42], [299, 30, 169, 42, "_jsxFileName"], [299, 42, 169, 42], [300, 20, 169, 42, "lineNumber"], [300, 30, 169, 42], [301, 20, 169, 42, "columnNumber"], [301, 32, 169, 42], [302, 18, 169, 42], [302, 25, 170, 28], [302, 26, 171, 21], [302, 28, 172, 21, "item"], [302, 32, 172, 25], [302, 33, 172, 26, "type"], [302, 37, 172, 30], [302, 42, 172, 35], [302, 48, 172, 41], [302, 65, 173, 22], [302, 69, 173, 22, "_jsxDevRuntime"], [302, 83, 173, 22], [302, 84, 173, 22, "jsxDEV"], [302, 90, 173, 22], [302, 92, 173, 23, "_reactNative"], [302, 104, 173, 23], [302, 105, 173, 23, "View"], [302, 109, 173, 27], [303, 20, 173, 28, "style"], [303, 25, 173, 33], [303, 27, 173, 35], [304, 22, 173, 37, "flexDirection"], [304, 35, 173, 50], [304, 37, 173, 52], [304, 42, 173, 57], [305, 22, 173, 59, "justifyContent"], [305, 36, 173, 73], [305, 38, 173, 75], [305, 53, 173, 90], [306, 22, 173, 92, "alignItems"], [306, 32, 173, 102], [306, 34, 173, 104], [307, 20, 173, 113], [307, 21, 173, 115], [308, 20, 173, 115, "children"], [308, 28, 173, 115], [308, 44, 174, 24], [308, 48, 174, 24, "_jsxDevRuntime"], [308, 62, 174, 24], [308, 63, 174, 24, "jsxDEV"], [308, 69, 174, 24], [308, 71, 174, 25, "_reactNative"], [308, 83, 174, 25], [308, 84, 174, 25, "Text"], [308, 88, 174, 29], [309, 22, 174, 30, "style"], [309, 27, 174, 35], [309, 29, 174, 37], [310, 24, 174, 39, "color"], [310, 29, 174, 44], [310, 31, 174, 46], [310, 40, 174, 55], [311, 24, 174, 57, "fontSize"], [311, 32, 174, 65], [311, 34, 174, 67], [312, 22, 174, 70], [312, 23, 174, 72], [313, 22, 174, 72, "children"], [313, 30, 174, 72], [313, 33, 174, 73], [313, 40, 175, 31], [313, 42, 175, 32, "item"], [313, 46, 175, 36], [313, 47, 175, 37, "restaurant"], [313, 57, 175, 47], [314, 20, 175, 47], [315, 22, 175, 47, "fileName"], [315, 30, 175, 47], [315, 32, 175, 47, "_jsxFileName"], [315, 44, 175, 47], [316, 22, 175, 47, "lineNumber"], [316, 32, 175, 47], [317, 22, 175, 47, "columnNumber"], [317, 34, 175, 47], [318, 20, 175, 47], [318, 27, 176, 30], [318, 28, 176, 31], [318, 43, 177, 24], [318, 47, 177, 24, "_jsxDevRuntime"], [318, 61, 177, 24], [318, 62, 177, 24, "jsxDEV"], [318, 68, 177, 24], [318, 70, 177, 25, "_reactNative"], [318, 82, 177, 25], [318, 83, 177, 25, "Text"], [318, 87, 177, 29], [319, 22, 177, 30, "style"], [319, 27, 177, 35], [319, 29, 177, 37], [320, 24, 177, 39, "fontSize"], [320, 32, 177, 47], [320, 34, 177, 49], [320, 36, 177, 51], [321, 24, 177, 53, "fontWeight"], [321, 34, 177, 63], [321, 36, 177, 65], [321, 42, 177, 71], [322, 24, 177, 73, "color"], [322, 29, 177, 78], [322, 31, 177, 80], [323, 22, 177, 90], [323, 23, 177, 92], [324, 22, 177, 92, "children"], [324, 30, 177, 92], [324, 33, 177, 93], [324, 36, 178, 27], [324, 38, 178, 28, "item"], [324, 42, 178, 32], [324, 43, 178, 33, "price"], [324, 48, 178, 38], [324, 50, 178, 40, "toFixed"], [324, 57, 178, 47], [324, 58, 178, 48], [324, 59, 178, 49], [324, 60, 178, 50], [325, 20, 178, 50], [326, 22, 178, 50, "fileName"], [326, 30, 178, 50], [326, 32, 178, 50, "_jsxFileName"], [326, 44, 178, 50], [327, 22, 178, 50, "lineNumber"], [327, 32, 178, 50], [328, 22, 178, 50, "columnNumber"], [328, 34, 178, 50], [329, 20, 178, 50], [329, 27, 179, 30], [329, 28, 179, 31], [330, 18, 179, 31], [331, 20, 179, 31, "fileName"], [331, 28, 179, 31], [331, 30, 179, 31, "_jsxFileName"], [331, 42, 179, 31], [332, 20, 179, 31, "lineNumber"], [332, 30, 179, 31], [333, 20, 179, 31, "columnNumber"], [333, 32, 179, 31], [334, 18, 179, 31], [334, 25, 180, 28], [334, 26, 181, 21], [335, 16, 181, 21], [336, 18, 181, 21, "fileName"], [336, 26, 181, 21], [336, 28, 181, 21, "_jsxFileName"], [336, 40, 181, 21], [337, 18, 181, 21, "lineNumber"], [337, 28, 181, 21], [338, 18, 181, 21, "columnNumber"], [338, 30, 181, 21], [339, 16, 181, 21], [339, 23, 182, 24], [339, 24, 182, 25], [340, 14, 182, 25], [341, 16, 182, 25, "fileName"], [341, 24, 182, 25], [341, 26, 182, 25, "_jsxFileName"], [341, 38, 182, 25], [342, 16, 182, 25, "lineNumber"], [342, 26, 182, 25], [343, 16, 182, 25, "columnNumber"], [343, 28, 182, 25], [344, 14, 182, 25], [344, 21, 183, 22], [345, 12, 183, 23], [345, 15, 138, 21, "item"], [345, 19, 138, 25], [345, 20, 138, 26, "id"], [345, 22, 138, 28], [346, 14, 138, 28, "fileName"], [346, 22, 138, 28], [346, 24, 138, 28, "_jsxFileName"], [346, 36, 138, 28], [347, 14, 138, 28, "lineNumber"], [347, 24, 138, 28], [348, 14, 138, 28, "columnNumber"], [348, 26, 138, 28], [349, 12, 138, 28], [349, 19, 184, 32], [349, 20, 185, 13], [349, 21, 185, 14], [350, 10, 185, 14], [351, 12, 185, 14, "fileName"], [351, 20, 185, 14], [351, 22, 185, 14, "_jsxFileName"], [351, 34, 185, 14], [352, 12, 185, 14, "lineNumber"], [352, 22, 185, 14], [353, 12, 185, 14, "columnNumber"], [353, 24, 185, 14], [354, 10, 185, 14], [354, 17, 186, 16], [354, 18, 187, 9], [354, 33, 189, 8], [354, 37, 189, 8, "_jsxDevRuntime"], [354, 51, 189, 8], [354, 52, 189, 8, "jsxDEV"], [354, 58, 189, 8], [354, 60, 189, 9, "_reactNative"], [354, 72, 189, 9], [354, 73, 189, 9, "View"], [354, 77, 189, 13], [355, 12, 189, 14, "style"], [355, 17, 189, 19], [355, 19, 189, 21], [356, 14, 189, 23, "height"], [356, 20, 189, 29], [356, 22, 189, 31], [357, 12, 189, 35], [358, 10, 189, 37], [359, 12, 189, 37, "fileName"], [359, 20, 189, 37], [359, 22, 189, 37, "_jsxFileName"], [359, 34, 189, 37], [360, 12, 189, 37, "lineNumber"], [360, 22, 189, 37], [361, 12, 189, 37, "columnNumber"], [361, 24, 189, 37], [362, 10, 189, 37], [362, 17, 189, 39], [362, 18, 189, 40], [363, 8, 189, 40], [364, 10, 189, 40, "fileName"], [364, 18, 189, 40], [364, 20, 189, 40, "_jsxFileName"], [364, 32, 189, 40], [365, 10, 189, 40, "lineNumber"], [365, 20, 189, 40], [366, 10, 189, 40, "columnNumber"], [366, 22, 189, 40], [367, 8, 189, 40], [367, 15, 190, 20], [367, 16, 190, 21], [368, 6, 190, 21], [369, 8, 190, 21, "fileName"], [369, 16, 190, 21], [369, 18, 190, 21, "_jsxFileName"], [369, 30, 190, 21], [370, 8, 190, 21, "lineNumber"], [370, 18, 190, 21], [371, 8, 190, 21, "columnNumber"], [371, 20, 190, 21], [372, 6, 190, 21], [372, 13, 191, 12], [372, 14, 191, 13], [372, 29, 194, 6], [372, 33, 194, 6, "_jsxDevRuntime"], [372, 47, 194, 6], [372, 48, 194, 6, "jsxDEV"], [372, 54, 194, 6], [372, 56, 194, 7, "_FooterNavigation"], [372, 73, 194, 7], [372, 74, 194, 7, "default"], [372, 81, 194, 23], [373, 8, 194, 24, "navigation"], [373, 18, 194, 34], [373, 20, 194, 36, "navigation"], [373, 30, 194, 47], [374, 8, 194, 48, "activeScreen"], [374, 20, 194, 60], [374, 22, 194, 61], [375, 6, 194, 69], [376, 8, 194, 69, "fileName"], [376, 16, 194, 69], [376, 18, 194, 69, "_jsxFileName"], [376, 30, 194, 69], [377, 8, 194, 69, "lineNumber"], [377, 18, 194, 69], [378, 8, 194, 69, "columnNumber"], [378, 20, 194, 69], [379, 6, 194, 69], [379, 13, 194, 71], [379, 14, 194, 72], [379, 29, 197, 6], [379, 33, 197, 6, "_jsxDevRuntime"], [379, 47, 197, 6], [379, 48, 197, 6, "jsxDEV"], [379, 54, 197, 6], [379, 56, 197, 7, "_reactNativeSafeAreaContext"], [379, 83, 197, 7], [379, 84, 197, 7, "SafeAreaView"], [379, 96, 197, 19], [380, 8, 197, 20, "style"], [380, 13, 197, 25], [380, 15, 197, 27], [381, 10, 197, 29, "backgroundColor"], [381, 25, 197, 44], [381, 27, 197, 46], [382, 8, 197, 56], [382, 9, 197, 58], [383, 8, 197, 59, "edges"], [383, 13, 197, 64], [383, 15, 197, 66], [383, 16, 197, 67], [383, 24, 197, 75], [384, 6, 197, 77], [385, 8, 197, 77, "fileName"], [385, 16, 197, 77], [385, 18, 197, 77, "_jsxFileName"], [385, 30, 197, 77], [386, 8, 197, 77, "lineNumber"], [386, 18, 197, 77], [387, 8, 197, 77, "columnNumber"], [387, 20, 197, 77], [388, 6, 197, 77], [388, 13, 197, 79], [388, 14, 197, 80], [389, 4, 197, 80], [390, 6, 197, 80, "fileName"], [390, 14, 197, 80], [390, 16, 197, 80, "_jsxFileName"], [390, 28, 197, 80], [391, 6, 197, 80, "lineNumber"], [391, 16, 197, 80], [392, 6, 197, 80, "columnNumber"], [392, 18, 197, 80], [393, 4, 197, 80], [393, 11, 198, 10], [393, 12, 198, 11], [394, 2, 200, 0], [395, 2, 200, 1, "_s"], [395, 4, 200, 1], [395, 5, 14, 24, "SearchScreen"], [395, 17, 14, 36], [396, 2, 14, 36, "_c"], [396, 4, 14, 36], [396, 7, 14, 24, "SearchScreen"], [396, 19, 14, 36], [397, 2, 14, 36], [397, 6, 14, 36, "_c"], [397, 8, 14, 36], [398, 2, 14, 36, "$RefreshReg$"], [398, 14, 14, 36], [398, 15, 14, 36, "_c"], [398, 17, 14, 36], [399, 0, 14, 36], [399, 3]], "functionMap": {"names": ["<global>", "SearchScreen", "searchResults.filter$argument_0", "TouchableOpacity.props.onPress", "popularSearches.map$argument_0", "filteredResults.map$argument_0"], "mappings": "AAA;eCa;+CC2B;GDU;uCEyC,wBF;mCGe;2BDG,4BC;eHgB;iCIU;aJiD;CDe"}}, "type": "js/module"}]}