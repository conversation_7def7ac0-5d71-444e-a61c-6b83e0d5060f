{"dependencies": [{"name": "./typeof.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 14, "index": 14}, "end": {"line": 1, "column": 36, "index": 36}}], "key": "3Tw2gscGXiWiW1gdVz1cVllPjgA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _typeof = require(_dependencyMap[0], \"./typeof.js\")[\"default\"];\n  function toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != _typeof(i)) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  module.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 6, 1, 4, "_typeof"], [2, 13, 1, 11], [2, 16, 1, 14, "require"], [2, 23, 1, 21], [2, 24, 1, 21, "_dependencyMap"], [2, 38, 1, 21], [2, 56, 1, 35], [2, 57, 1, 36], [2, 58, 1, 37], [2, 67, 1, 46], [2, 68, 1, 47], [3, 2, 2, 0], [3, 11, 2, 9, "toPrimitive"], [3, 22, 2, 20, "toPrimitive"], [3, 23, 2, 21, "t"], [3, 24, 2, 22], [3, 26, 2, 24, "r"], [3, 27, 2, 25], [3, 29, 2, 27], [4, 4, 3, 2], [4, 8, 3, 6], [4, 16, 3, 14], [4, 20, 3, 18, "_typeof"], [4, 27, 3, 25], [4, 28, 3, 26, "t"], [4, 29, 3, 27], [4, 30, 3, 28], [4, 34, 3, 32], [4, 35, 3, 33, "t"], [4, 36, 3, 34], [4, 38, 3, 36], [4, 45, 3, 43, "t"], [4, 46, 3, 44], [5, 4, 4, 2], [5, 8, 4, 6, "e"], [5, 9, 4, 7], [5, 12, 4, 10, "t"], [5, 13, 4, 11], [5, 14, 4, 12, "Symbol"], [5, 20, 4, 18], [5, 21, 4, 19, "toPrimitive"], [5, 32, 4, 30], [5, 33, 4, 31], [6, 4, 5, 2], [6, 8, 5, 6], [6, 13, 5, 11], [6, 14, 5, 12], [6, 19, 5, 17, "e"], [6, 20, 5, 18], [6, 22, 5, 20], [7, 6, 6, 4], [7, 10, 6, 8, "i"], [7, 11, 6, 9], [7, 14, 6, 12, "e"], [7, 15, 6, 13], [7, 16, 6, 14, "call"], [7, 20, 6, 18], [7, 21, 6, 19, "t"], [7, 22, 6, 20], [7, 24, 6, 22, "r"], [7, 25, 6, 23], [7, 29, 6, 27], [7, 38, 6, 36], [7, 39, 6, 37], [8, 6, 7, 4], [8, 10, 7, 8], [8, 18, 7, 16], [8, 22, 7, 20, "_typeof"], [8, 29, 7, 27], [8, 30, 7, 28, "i"], [8, 31, 7, 29], [8, 32, 7, 30], [8, 34, 7, 32], [8, 41, 7, 39, "i"], [8, 42, 7, 40], [9, 6, 8, 4], [9, 12, 8, 10], [9, 16, 8, 14, "TypeError"], [9, 25, 8, 23], [9, 26, 8, 24], [9, 72, 8, 70], [9, 73, 8, 71], [10, 4, 9, 2], [11, 4, 10, 2], [11, 11, 10, 9], [11, 12, 10, 10], [11, 20, 10, 18], [11, 25, 10, 23, "r"], [11, 26, 10, 24], [11, 29, 10, 27, "String"], [11, 35, 10, 33], [11, 38, 10, 36, "Number"], [11, 44, 10, 42], [11, 46, 10, 44, "t"], [11, 47, 10, 45], [11, 48, 10, 46], [12, 2, 11, 0], [13, 2, 12, 0, "module"], [13, 8, 12, 6], [13, 9, 12, 7, "exports"], [13, 16, 12, 14], [13, 19, 12, 17, "toPrimitive"], [13, 30, 12, 28], [13, 32, 12, 30, "module"], [13, 38, 12, 36], [13, 39, 12, 37, "exports"], [13, 46, 12, 44], [13, 47, 12, 45, "__esModule"], [13, 57, 12, 55], [13, 60, 12, 58], [13, 64, 12, 62], [13, 66, 12, 64, "module"], [13, 72, 12, 70], [13, 73, 12, 71, "exports"], [13, 80, 12, 78], [13, 81, 12, 79], [13, 90, 12, 88], [13, 91, 12, 89], [13, 94, 12, 92, "module"], [13, 100, 12, 98], [13, 101, 12, 99, "exports"], [13, 108, 12, 106], [14, 0, 12, 107], [14, 3]], "functionMap": {"names": ["<global>", "toPrimitive"], "mappings": "AAA;ACC;CDS"}}, "type": "js/module"}]}