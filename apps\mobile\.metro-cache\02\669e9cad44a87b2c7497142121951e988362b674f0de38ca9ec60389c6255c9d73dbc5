{"dependencies": [{"name": "../../../../../../Libraries/ReactNative/RendererProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 87}}], "key": "NEuuOJk4bCzTlqd9js5Ll0kmQp8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createReactNativeDocumentInstanceHandle = createReactNativeDocumentInstanceHandle;\n  exports.getNativeNodeReferenceFromReactNativeDocumentInstanceHandle = getNativeNodeReferenceFromReactNativeDocumentInstanceHandle;\n  exports.getPublicInstanceFromReactNativeDocumentInstanceHandle = getPublicInstanceFromReactNativeDocumentInstanceHandle;\n  exports.isReactNativeDocumentInstanceHandle = isReactNativeDocumentInstanceHandle;\n  var RendererProxy = _interopRequireWildcard(require(_dependencyMap[0], \"../../../../../../Libraries/ReactNative/RendererProxy\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function createReactNativeDocumentInstanceHandle(rootTag) {\n    return rootTag;\n  }\n  function getNativeNodeReferenceFromReactNativeDocumentInstanceHandle(instanceHandle) {\n    return instanceHandle;\n  }\n  function getPublicInstanceFromReactNativeDocumentInstanceHandle(instanceHandle) {\n    return RendererProxy.getPublicInstanceFromRootTag(Number(instanceHandle));\n  }\n  function isReactNativeDocumentInstanceHandle(instanceHandle) {\n    return typeof instanceHandle === 'number' && instanceHandle % 10 === 1;\n  }\n});", "lineCount": 23, "map": [[9, 2, 15, 0], [9, 6, 15, 0, "RendererProxy"], [9, 19, 15, 0], [9, 22, 15, 0, "_interopRequireWildcard"], [9, 45, 15, 0], [9, 46, 15, 0, "require"], [9, 53, 15, 0], [9, 54, 15, 0, "_dependencyMap"], [9, 68, 15, 0], [10, 2, 15, 87], [10, 11, 15, 87, "_interopRequireWildcard"], [10, 35, 15, 87, "e"], [10, 36, 15, 87], [10, 38, 15, 87, "t"], [10, 39, 15, 87], [10, 68, 15, 87, "WeakMap"], [10, 75, 15, 87], [10, 81, 15, 87, "r"], [10, 82, 15, 87], [10, 89, 15, 87, "WeakMap"], [10, 96, 15, 87], [10, 100, 15, 87, "n"], [10, 101, 15, 87], [10, 108, 15, 87, "WeakMap"], [10, 115, 15, 87], [10, 127, 15, 87, "_interopRequireWildcard"], [10, 150, 15, 87], [10, 162, 15, 87, "_interopRequireWildcard"], [10, 163, 15, 87, "e"], [10, 164, 15, 87], [10, 166, 15, 87, "t"], [10, 167, 15, 87], [10, 176, 15, 87, "t"], [10, 177, 15, 87], [10, 181, 15, 87, "e"], [10, 182, 15, 87], [10, 186, 15, 87, "e"], [10, 187, 15, 87], [10, 188, 15, 87, "__esModule"], [10, 198, 15, 87], [10, 207, 15, 87, "e"], [10, 208, 15, 87], [10, 214, 15, 87, "o"], [10, 215, 15, 87], [10, 217, 15, 87, "i"], [10, 218, 15, 87], [10, 220, 15, 87, "f"], [10, 221, 15, 87], [10, 226, 15, 87, "__proto__"], [10, 235, 15, 87], [10, 243, 15, 87, "default"], [10, 250, 15, 87], [10, 252, 15, 87, "e"], [10, 253, 15, 87], [10, 270, 15, 87, "e"], [10, 271, 15, 87], [10, 294, 15, 87, "e"], [10, 295, 15, 87], [10, 320, 15, 87, "e"], [10, 321, 15, 87], [10, 330, 15, 87, "f"], [10, 331, 15, 87], [10, 337, 15, 87, "o"], [10, 338, 15, 87], [10, 341, 15, 87, "t"], [10, 342, 15, 87], [10, 345, 15, 87, "n"], [10, 346, 15, 87], [10, 349, 15, 87, "r"], [10, 350, 15, 87], [10, 358, 15, 87, "o"], [10, 359, 15, 87], [10, 360, 15, 87, "has"], [10, 363, 15, 87], [10, 364, 15, 87, "e"], [10, 365, 15, 87], [10, 375, 15, 87, "o"], [10, 376, 15, 87], [10, 377, 15, 87, "get"], [10, 380, 15, 87], [10, 381, 15, 87, "e"], [10, 382, 15, 87], [10, 385, 15, 87, "o"], [10, 386, 15, 87], [10, 387, 15, 87, "set"], [10, 390, 15, 87], [10, 391, 15, 87, "e"], [10, 392, 15, 87], [10, 394, 15, 87, "f"], [10, 395, 15, 87], [10, 409, 15, 87, "_t"], [10, 411, 15, 87], [10, 415, 15, 87, "e"], [10, 416, 15, 87], [10, 432, 15, 87, "_t"], [10, 434, 15, 87], [10, 441, 15, 87, "hasOwnProperty"], [10, 455, 15, 87], [10, 456, 15, 87, "call"], [10, 460, 15, 87], [10, 461, 15, 87, "e"], [10, 462, 15, 87], [10, 464, 15, 87, "_t"], [10, 466, 15, 87], [10, 473, 15, 87, "i"], [10, 474, 15, 87], [10, 478, 15, 87, "o"], [10, 479, 15, 87], [10, 482, 15, 87, "Object"], [10, 488, 15, 87], [10, 489, 15, 87, "defineProperty"], [10, 503, 15, 87], [10, 508, 15, 87, "Object"], [10, 514, 15, 87], [10, 515, 15, 87, "getOwnPropertyDescriptor"], [10, 539, 15, 87], [10, 540, 15, 87, "e"], [10, 541, 15, 87], [10, 543, 15, 87, "_t"], [10, 545, 15, 87], [10, 552, 15, 87, "i"], [10, 553, 15, 87], [10, 554, 15, 87, "get"], [10, 557, 15, 87], [10, 561, 15, 87, "i"], [10, 562, 15, 87], [10, 563, 15, 87, "set"], [10, 566, 15, 87], [10, 570, 15, 87, "o"], [10, 571, 15, 87], [10, 572, 15, 87, "f"], [10, 573, 15, 87], [10, 575, 15, 87, "_t"], [10, 577, 15, 87], [10, 579, 15, 87, "i"], [10, 580, 15, 87], [10, 584, 15, 87, "f"], [10, 585, 15, 87], [10, 586, 15, 87, "_t"], [10, 588, 15, 87], [10, 592, 15, 87, "e"], [10, 593, 15, 87], [10, 594, 15, 87, "_t"], [10, 596, 15, 87], [10, 607, 15, 87, "f"], [10, 608, 15, 87], [10, 613, 15, 87, "e"], [10, 614, 15, 87], [10, 616, 15, 87, "t"], [10, 617, 15, 87], [11, 2, 19, 7], [11, 11, 19, 16, "createReactNativeDocumentInstanceHandle"], [11, 50, 19, 55, "createReactNativeDocumentInstanceHandle"], [11, 51, 20, 2, "rootTag"], [11, 58, 20, 18], [11, 60, 21, 37], [12, 4, 22, 2], [12, 11, 22, 9, "rootTag"], [12, 18, 22, 16], [13, 2, 23, 0], [14, 2, 25, 7], [14, 11, 25, 16, "getNativeNodeReferenceFromReactNativeDocumentInstanceHandle"], [14, 70, 25, 75, "getNativeNodeReferenceFromReactNativeDocumentInstanceHandle"], [14, 71, 26, 2, "instanceHandle"], [14, 85, 26, 51], [14, 87, 27, 24], [15, 4, 28, 2], [15, 11, 28, 9, "instanceHandle"], [15, 25, 28, 23], [16, 2, 29, 0], [17, 2, 31, 7], [17, 11, 31, 16, "getPublicInstanceFromReactNativeDocumentInstanceHandle"], [17, 65, 31, 70, "getPublicInstanceFromReactNativeDocumentInstanceHandle"], [17, 66, 32, 2, "instanceHandle"], [17, 80, 32, 51], [17, 82, 33, 24], [18, 4, 35, 2], [18, 11, 35, 9, "RendererProxy"], [18, 24, 35, 22], [18, 25, 35, 23, "getPublicInstanceFromRootTag"], [18, 53, 35, 51], [18, 54, 35, 52, "Number"], [18, 60, 35, 58], [18, 61, 35, 59, "instanceHandle"], [18, 75, 35, 73], [18, 76, 35, 74], [18, 77, 35, 75], [19, 2, 36, 0], [20, 2, 38, 7], [20, 11, 38, 16, "isReactNativeDocumentInstanceHandle"], [20, 46, 38, 51, "isReactNativeDocumentInstanceHandle"], [20, 47, 39, 2, "instanceHandle"], [20, 61, 39, 23], [20, 63, 41, 55], [21, 4, 43, 2], [21, 11, 43, 9], [21, 18, 43, 16, "instanceHandle"], [21, 32, 43, 30], [21, 37, 43, 35], [21, 45, 43, 43], [21, 49, 43, 47, "instanceHandle"], [21, 63, 43, 61], [21, 66, 43, 64], [21, 68, 43, 66], [21, 73, 43, 71], [21, 74, 43, 72], [22, 2, 44, 0], [23, 0, 44, 1], [23, 3]], "functionMap": {"names": ["<global>", "createReactNativeDocumentInstanceHandle", "getNativeNodeReferenceFromReactNativeDocumentInstanceHandle", "getPublicInstanceFromReactNativeDocumentInstanceHandle", "isReactNativeDocumentInstanceHandle"], "mappings": "AAA;OCkB;CDI;OEE;CFI;OGE;CHK;OIE"}}, "type": "js/module"}]}