{"dependencies": [{"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 16, "index": 123}, "end": {"line": 4, "column": 32, "index": 139}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 23, "index": 164}, "end": {"line": 5, "column": 46, "index": 187}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "use-sync-external-store/shim/with-selector", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 24, "index": 213}, "end": {"line": 6, "column": 77, "index": 266}}], "key": "GKIwZc7JgpG4SPgBqvS5BZz2Pa8=", "exportNames": ["*"]}}, {"name": "../style-sheet", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 22, "index": 290}, "end": {"line": 7, "column": 47, "index": 315}}], "key": "H867pV8MtE7d9CKnHRvizzO0rbw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _slicedToArray = require(_dependencyMap[0], \"@babel/runtime/helpers/slicedToArray\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useTailwind = void 0;\n  var react_1 = require(_dependencyMap[1], \"react\");\n  var react_native_1 = require(_dependencyMap[2], \"react-native\");\n  var with_selector_1 = require(_dependencyMap[3], \"use-sync-external-store/shim/with-selector\");\n  var style_sheet_1 = require(_dependencyMap[4], \"../style-sheet\");\n  function useTailwind(_ref) {\n    var className = _ref.className,\n      inlineStyles = _ref.inlineStyles,\n      additionalStyles = _ref.additionalStyles,\n      hover = _ref.hover,\n      focus = _ref.focus,\n      active = _ref.active,\n      isolateGroupHover = _ref.isolateGroupHover,\n      isolateGroupFocus = _ref.isolateGroupFocus,\n      isolateGroupActive = _ref.isolateGroupActive,\n      groupHover = _ref.groupHover,\n      groupFocus = _ref.groupFocus,\n      groupActive = _ref.groupActive,\n      flatten = _ref.flatten;\n    var store = (0, react_1.useContext)(style_sheet_1.StoreContext);\n    var _ref2 = (0, react_1.useMemo)(() => {\n        var selector = store.prepare(className, {\n          hover,\n          focus,\n          active,\n          isolateGroupHover,\n          isolateGroupFocus,\n          isolateGroupActive,\n          groupHover,\n          groupFocus,\n          groupActive\n        });\n        return [store.subscribe, store.getSnapshot, snapshot => snapshot[selector]];\n      }, [store, className, hover, focus, active, isolateGroupHover, isolateGroupFocus, isolateGroupActive, groupHover, groupFocus, groupActive]),\n      _ref3 = _slicedToArray(_ref2, 3),\n      subscribe = _ref3[0],\n      getSnapshot = _ref3[1],\n      selector = _ref3[2];\n    var styles = (0, with_selector_1.useSyncExternalStoreWithSelector)(subscribe, getSnapshot, getSnapshot, selector);\n    return (0, react_1.useMemo)(() => {\n      var stylesArray = [];\n      if (styles) {\n        stylesArray.push(...styles);\n        stylesArray.childClassNames = styles.childClassNames;\n      }\n      if (additionalStyles) {\n        stylesArray.push(...additionalStyles);\n      }\n      if (inlineStyles) {\n        stylesArray.push(inlineStyles);\n      }\n      if (flatten) {\n        var flatStyles = [react_native_1.StyleSheet.flatten(stylesArray)];\n        flatStyles.mask = styles === null || styles === void 0 ? void 0 : styles.mask;\n        return flatStyles;\n      }\n      stylesArray.mask = styles === null || styles === void 0 ? void 0 : styles.mask;\n      return stylesArray;\n    }, [styles, inlineStyles, additionalStyles, flatten]);\n  }\n  exports.useTailwind = useTailwind;\n});", "lineCount": 69, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_slicedToArray"], [4, 20, 1, 13], [4, 23, 1, 13, "require"], [4, 30, 1, 13], [4, 31, 1, 13, "_dependencyMap"], [4, 45, 1, 13], [5, 2, 2, 0, "Object"], [5, 8, 2, 6], [5, 9, 2, 7, "defineProperty"], [5, 23, 2, 21], [5, 24, 2, 22, "exports"], [5, 31, 2, 29], [5, 33, 2, 31], [5, 45, 2, 43], [5, 47, 2, 45], [6, 4, 2, 47, "value"], [6, 9, 2, 52], [6, 11, 2, 54], [7, 2, 2, 59], [7, 3, 2, 60], [7, 4, 2, 61], [8, 2, 3, 0, "exports"], [8, 9, 3, 7], [8, 10, 3, 8, "useTailwind"], [8, 21, 3, 19], [8, 24, 3, 22], [8, 29, 3, 27], [8, 30, 3, 28], [9, 2, 4, 0], [9, 6, 4, 6, "react_1"], [9, 13, 4, 13], [9, 16, 4, 16, "require"], [9, 23, 4, 23], [9, 24, 4, 23, "_dependencyMap"], [9, 38, 4, 23], [9, 50, 4, 31], [9, 51, 4, 32], [10, 2, 5, 0], [10, 6, 5, 6, "react_native_1"], [10, 20, 5, 20], [10, 23, 5, 23, "require"], [10, 30, 5, 30], [10, 31, 5, 30, "_dependencyMap"], [10, 45, 5, 30], [10, 64, 5, 45], [10, 65, 5, 46], [11, 2, 6, 0], [11, 6, 6, 6, "with_selector_1"], [11, 21, 6, 21], [11, 24, 6, 24, "require"], [11, 31, 6, 31], [11, 32, 6, 31, "_dependencyMap"], [11, 46, 6, 31], [11, 95, 6, 76], [11, 96, 6, 77], [12, 2, 7, 0], [12, 6, 7, 6, "style_sheet_1"], [12, 19, 7, 19], [12, 22, 7, 22, "require"], [12, 29, 7, 29], [12, 30, 7, 29, "_dependencyMap"], [12, 44, 7, 29], [12, 65, 7, 46], [12, 66, 7, 47], [13, 2, 8, 0], [13, 11, 8, 9, "useTailwind"], [13, 22, 8, 20, "useTailwind"], [13, 23, 8, 20, "_ref"], [13, 27, 8, 20], [13, 29, 8, 195], [14, 4, 8, 195], [14, 8, 8, 23, "className"], [14, 17, 8, 32], [14, 20, 8, 32, "_ref"], [14, 24, 8, 32], [14, 25, 8, 23, "className"], [14, 34, 8, 32], [15, 6, 8, 34, "inlineStyles"], [15, 18, 8, 46], [15, 21, 8, 46, "_ref"], [15, 25, 8, 46], [15, 26, 8, 34, "inlineStyles"], [15, 38, 8, 46], [16, 6, 8, 48, "additionalStyles"], [16, 22, 8, 64], [16, 25, 8, 64, "_ref"], [16, 29, 8, 64], [16, 30, 8, 48, "additionalStyles"], [16, 46, 8, 64], [17, 6, 8, 66, "hover"], [17, 11, 8, 71], [17, 14, 8, 71, "_ref"], [17, 18, 8, 71], [17, 19, 8, 66, "hover"], [17, 24, 8, 71], [18, 6, 8, 73, "focus"], [18, 11, 8, 78], [18, 14, 8, 78, "_ref"], [18, 18, 8, 78], [18, 19, 8, 73, "focus"], [18, 24, 8, 78], [19, 6, 8, 80, "active"], [19, 12, 8, 86], [19, 15, 8, 86, "_ref"], [19, 19, 8, 86], [19, 20, 8, 80, "active"], [19, 26, 8, 86], [20, 6, 8, 88, "isolateGroupHover"], [20, 23, 8, 105], [20, 26, 8, 105, "_ref"], [20, 30, 8, 105], [20, 31, 8, 88, "isolateGroupHover"], [20, 48, 8, 105], [21, 6, 8, 107, "isolateGroupFocus"], [21, 23, 8, 124], [21, 26, 8, 124, "_ref"], [21, 30, 8, 124], [21, 31, 8, 107, "isolateGroupFocus"], [21, 48, 8, 124], [22, 6, 8, 126, "isolateGroupActive"], [22, 24, 8, 144], [22, 27, 8, 144, "_ref"], [22, 31, 8, 144], [22, 32, 8, 126, "isolateGroupActive"], [22, 50, 8, 144], [23, 6, 8, 146, "groupHover"], [23, 16, 8, 156], [23, 19, 8, 156, "_ref"], [23, 23, 8, 156], [23, 24, 8, 146, "groupHover"], [23, 34, 8, 156], [24, 6, 8, 158, "groupFocus"], [24, 16, 8, 168], [24, 19, 8, 168, "_ref"], [24, 23, 8, 168], [24, 24, 8, 158, "groupFocus"], [24, 34, 8, 168], [25, 6, 8, 170, "groupActive"], [25, 17, 8, 181], [25, 20, 8, 181, "_ref"], [25, 24, 8, 181], [25, 25, 8, 170, "groupActive"], [25, 36, 8, 181], [26, 6, 8, 183, "flatten"], [26, 13, 8, 190], [26, 16, 8, 190, "_ref"], [26, 20, 8, 190], [26, 21, 8, 183, "flatten"], [26, 28, 8, 190], [27, 4, 9, 4], [27, 8, 9, 10, "store"], [27, 13, 9, 15], [27, 16, 9, 18], [27, 17, 9, 19], [27, 18, 9, 20], [27, 20, 9, 22, "react_1"], [27, 27, 9, 29], [27, 28, 9, 30, "useContext"], [27, 38, 9, 40], [27, 40, 9, 42, "style_sheet_1"], [27, 53, 9, 55], [27, 54, 9, 56, "StoreContext"], [27, 66, 9, 68], [27, 67, 9, 69], [28, 4, 10, 4], [28, 8, 10, 4, "_ref2"], [28, 13, 10, 4], [28, 16, 10, 47], [28, 17, 10, 48], [28, 18, 10, 49], [28, 20, 10, 51, "react_1"], [28, 27, 10, 58], [28, 28, 10, 59, "useMemo"], [28, 35, 10, 66], [28, 37, 10, 68], [28, 43, 10, 74], [29, 8, 11, 8], [29, 12, 11, 14, "selector"], [29, 20, 11, 22], [29, 23, 11, 25, "store"], [29, 28, 11, 30], [29, 29, 11, 31, "prepare"], [29, 36, 11, 38], [29, 37, 11, 39, "className"], [29, 46, 11, 48], [29, 48, 11, 50], [30, 10, 12, 12, "hover"], [30, 15, 12, 17], [31, 10, 13, 12, "focus"], [31, 15, 13, 17], [32, 10, 14, 12, "active"], [32, 16, 14, 18], [33, 10, 15, 12, "isolateGroupHover"], [33, 27, 15, 29], [34, 10, 16, 12, "isolateGroupFocus"], [34, 27, 16, 29], [35, 10, 17, 12, "isolateGroupActive"], [35, 28, 17, 30], [36, 10, 18, 12, "groupHover"], [36, 20, 18, 22], [37, 10, 19, 12, "groupFocus"], [37, 20, 19, 22], [38, 10, 20, 12, "groupActive"], [39, 8, 21, 8], [39, 9, 21, 9], [39, 10, 21, 10], [40, 8, 22, 8], [40, 15, 22, 15], [40, 16, 23, 12, "store"], [40, 21, 23, 17], [40, 22, 23, 18, "subscribe"], [40, 31, 23, 27], [40, 33, 24, 12, "store"], [40, 38, 24, 17], [40, 39, 24, 18, "getSnapshot"], [40, 50, 24, 29], [40, 52, 25, 13, "snapshot"], [40, 60, 25, 21], [40, 64, 25, 26, "snapshot"], [40, 72, 25, 34], [40, 73, 25, 35, "selector"], [40, 81, 25, 43], [40, 82, 25, 44], [40, 83, 26, 9], [41, 6, 27, 4], [41, 7, 27, 5], [41, 9, 27, 7], [41, 10, 28, 8, "store"], [41, 15, 28, 13], [41, 17, 29, 8, "className"], [41, 26, 29, 17], [41, 28, 30, 8, "hover"], [41, 33, 30, 13], [41, 35, 31, 8, "focus"], [41, 40, 31, 13], [41, 42, 32, 8, "active"], [41, 48, 32, 14], [41, 50, 33, 8, "isolateGroupHover"], [41, 67, 33, 25], [41, 69, 34, 8, "isolateGroupFocus"], [41, 86, 34, 25], [41, 88, 35, 8, "isolateGroupActive"], [41, 106, 35, 26], [41, 108, 36, 8, "groupHover"], [41, 118, 36, 18], [41, 120, 37, 8, "groupFocus"], [41, 130, 37, 18], [41, 132, 38, 8, "groupActive"], [41, 143, 38, 19], [41, 144, 39, 5], [41, 145, 39, 6], [42, 6, 39, 6, "_ref3"], [42, 11, 39, 6], [42, 14, 39, 6, "_slicedToArray"], [42, 28, 39, 6], [42, 29, 39, 6, "_ref2"], [42, 34, 39, 6], [43, 6, 10, 11, "subscribe"], [43, 15, 10, 20], [43, 18, 10, 20, "_ref3"], [43, 23, 10, 20], [44, 6, 10, 22, "getSnapshot"], [44, 17, 10, 33], [44, 20, 10, 33, "_ref3"], [44, 25, 10, 33], [45, 6, 10, 35, "selector"], [45, 14, 10, 43], [45, 17, 10, 43, "_ref3"], [45, 22, 10, 43], [46, 4, 40, 4], [46, 8, 40, 10, "styles"], [46, 14, 40, 16], [46, 17, 40, 19], [46, 18, 40, 20], [46, 19, 40, 21], [46, 21, 40, 23, "with_selector_1"], [46, 36, 40, 38], [46, 37, 40, 39, "useSyncExternalStoreWithSelector"], [46, 69, 40, 71], [46, 71, 40, 73, "subscribe"], [46, 80, 40, 82], [46, 82, 40, 84, "getSnapshot"], [46, 93, 40, 95], [46, 95, 40, 97, "getSnapshot"], [46, 106, 40, 108], [46, 108, 40, 110, "selector"], [46, 116, 40, 118], [46, 117, 40, 119], [47, 4, 41, 4], [47, 11, 41, 11], [47, 12, 41, 12], [47, 13, 41, 13], [47, 15, 41, 15, "react_1"], [47, 22, 41, 22], [47, 23, 41, 23, "useMemo"], [47, 30, 41, 30], [47, 32, 41, 32], [47, 38, 41, 38], [48, 6, 42, 8], [48, 10, 42, 14, "stylesArray"], [48, 21, 42, 25], [48, 24, 42, 28], [48, 26, 42, 30], [49, 6, 43, 8], [49, 10, 43, 12, "styles"], [49, 16, 43, 18], [49, 18, 43, 20], [50, 8, 44, 12, "stylesArray"], [50, 19, 44, 23], [50, 20, 44, 24, "push"], [50, 24, 44, 28], [50, 25, 44, 29], [50, 28, 44, 32, "styles"], [50, 34, 44, 38], [50, 35, 44, 39], [51, 8, 45, 12, "stylesArray"], [51, 19, 45, 23], [51, 20, 45, 24, "childClassNames"], [51, 35, 45, 39], [51, 38, 45, 42, "styles"], [51, 44, 45, 48], [51, 45, 45, 49, "childClassNames"], [51, 60, 45, 64], [52, 6, 46, 8], [53, 6, 47, 8], [53, 10, 47, 12, "additionalStyles"], [53, 26, 47, 28], [53, 28, 47, 30], [54, 8, 48, 12, "stylesArray"], [54, 19, 48, 23], [54, 20, 48, 24, "push"], [54, 24, 48, 28], [54, 25, 48, 29], [54, 28, 48, 32, "additionalStyles"], [54, 44, 48, 48], [54, 45, 48, 49], [55, 6, 49, 8], [56, 6, 50, 8], [56, 10, 50, 12, "inlineStyles"], [56, 22, 50, 24], [56, 24, 50, 26], [57, 8, 51, 12, "stylesArray"], [57, 19, 51, 23], [57, 20, 51, 24, "push"], [57, 24, 51, 28], [57, 25, 51, 29, "inlineStyles"], [57, 37, 51, 41], [57, 38, 51, 42], [58, 6, 52, 8], [59, 6, 53, 8], [59, 10, 53, 12, "flatten"], [59, 17, 53, 19], [59, 19, 53, 21], [60, 8, 54, 12], [60, 12, 54, 18, "flatStyles"], [60, 22, 54, 28], [60, 25, 54, 31], [60, 26, 54, 32, "react_native_1"], [60, 40, 54, 46], [60, 41, 54, 47, "StyleSheet"], [60, 51, 54, 57], [60, 52, 54, 58, "flatten"], [60, 59, 54, 65], [60, 60, 54, 66, "stylesArray"], [60, 71, 54, 77], [60, 72, 54, 78], [60, 73, 54, 79], [61, 8, 55, 12, "flatStyles"], [61, 18, 55, 22], [61, 19, 55, 23, "mask"], [61, 23, 55, 27], [61, 26, 55, 30, "styles"], [61, 32, 55, 36], [61, 37, 55, 41], [61, 41, 55, 45], [61, 45, 55, 49, "styles"], [61, 51, 55, 55], [61, 56, 55, 60], [61, 61, 55, 65], [61, 62, 55, 66], [61, 65, 55, 69], [61, 70, 55, 74], [61, 71, 55, 75], [61, 74, 55, 78, "styles"], [61, 80, 55, 84], [61, 81, 55, 85, "mask"], [61, 85, 55, 89], [62, 8, 56, 12], [62, 15, 56, 19, "flatStyles"], [62, 25, 56, 29], [63, 6, 57, 8], [64, 6, 58, 8, "stylesArray"], [64, 17, 58, 19], [64, 18, 58, 20, "mask"], [64, 22, 58, 24], [64, 25, 58, 27, "styles"], [64, 31, 58, 33], [64, 36, 58, 38], [64, 40, 58, 42], [64, 44, 58, 46, "styles"], [64, 50, 58, 52], [64, 55, 58, 57], [64, 60, 58, 62], [64, 61, 58, 63], [64, 64, 58, 66], [64, 69, 58, 71], [64, 70, 58, 72], [64, 73, 58, 75, "styles"], [64, 79, 58, 81], [64, 80, 58, 82, "mask"], [64, 84, 58, 86], [65, 6, 59, 8], [65, 13, 59, 15, "stylesArray"], [65, 24, 59, 26], [66, 4, 60, 4], [66, 5, 60, 5], [66, 7, 60, 7], [66, 8, 60, 8, "styles"], [66, 14, 60, 14], [66, 16, 60, 16, "inlineStyles"], [66, 28, 60, 28], [66, 30, 60, 30, "additionalStyles"], [66, 46, 60, 46], [66, 48, 60, 48, "flatten"], [66, 55, 60, 55], [66, 56, 60, 56], [66, 57, 60, 57], [67, 2, 61, 0], [68, 2, 62, 0, "exports"], [68, 9, 62, 7], [68, 10, 62, 8, "useTailwind"], [68, 21, 62, 19], [68, 24, 62, 22, "useTailwind"], [68, 35, 62, 33], [69, 0, 62, 34], [69, 3]], "functionMap": {"names": ["<global>", "useTailwind", "<anonymous>"], "mappings": "AAA;ACO;oECE;KDiB;gCCc;KDmB;CDC"}}, "type": "js/module"}]}