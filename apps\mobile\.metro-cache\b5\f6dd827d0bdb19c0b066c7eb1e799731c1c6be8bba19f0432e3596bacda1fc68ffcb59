{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.baseGestureHandlerWithDetectorProps = exports.baseGestureHandlerProps = exports.MouseButton = void 0;\n  // Previous types exported gesture handlers as classes which creates an interface and variable, both named the same as class.\n  // Without those types, we'd introduce breaking change, forcing users to prefix every handler type specification with typeof\n  // e.g. React.createRef<TapGestureHandler> -> React.createRef<typeof TapGestureHandler>.\n  // See https://www.typescriptlang.org/docs/handbook/classes.html#constructor-functions for reference.\n\n  var commonProps = ['id', 'enabled', 'shouldCancelWhenOutside', 'hitSlop', 'cancelsTouchesInView', 'userSelect', 'activeCursor', 'mouseButton', 'enableContextMenu', 'touchAction'];\n  var componentInteractionProps = ['waitFor', 'simultaneousHandlers', 'blocksHandlers'];\n  var baseGestureHandlerProps = exports.baseGestureHandlerProps = [...commonProps, ...componentInteractionProps, 'onBegan', 'onFailed', 'onCancelled', 'onActivated', 'onEnded', 'onGestureEvent', 'onHandlerStateChange'];\n  var baseGestureHandlerWithDetectorProps = exports.baseGestureHandlerWithDetectorProps = [...commonProps, 'needsPointerData', 'manualActivation'];\n  var MouseButton = exports.MouseButton = /*#__PURE__*/function (MouseButton) {\n    MouseButton[MouseButton[\"LEFT\"] = 1] = \"LEFT\";\n    MouseButton[MouseButton[\"RIGHT\"] = 2] = \"RIGHT\";\n    MouseButton[MouseButton[\"MIDDLE\"] = 4] = \"MIDDLE\";\n    MouseButton[MouseButton[\"BUTTON_4\"] = 8] = \"BUTTON_4\";\n    MouseButton[MouseButton[\"BUTTON_5\"] = 16] = \"BUTTON_5\";\n    MouseButton[MouseButton[\"ALL\"] = 31] = \"ALL\";\n    return MouseButton;\n  }({}); // TODO(TS) events in handlers\n  // Events payloads are types instead of interfaces due to TS limitation.\n  // See https://github.com/microsoft/TypeScript/issues/15300 for more info.\n});", "lineCount": 26, "map": [[6, 2, 1, 0], [7, 2, 2, 0], [8, 2, 3, 0], [9, 2, 4, 0], [11, 2, 12, 0], [11, 6, 12, 6, "commonProps"], [11, 17, 12, 17], [11, 20, 12, 20], [11, 21, 13, 2], [11, 25, 13, 6], [11, 27, 14, 2], [11, 36, 14, 11], [11, 38, 15, 2], [11, 63, 15, 27], [11, 65, 16, 2], [11, 74, 16, 11], [11, 76, 17, 2], [11, 98, 17, 24], [11, 100, 18, 2], [11, 112, 18, 14], [11, 114, 19, 2], [11, 128, 19, 16], [11, 130, 20, 2], [11, 143, 20, 15], [11, 145, 21, 2], [11, 164, 21, 21], [11, 166, 22, 2], [11, 179, 22, 15], [11, 180, 23, 10], [12, 2, 25, 0], [12, 6, 25, 6, "componentInteractionProps"], [12, 31, 25, 31], [12, 34, 25, 34], [12, 35, 26, 2], [12, 44, 26, 11], [12, 46, 27, 2], [12, 68, 27, 24], [12, 70, 28, 2], [12, 86, 28, 18], [12, 87, 29, 10], [13, 2, 31, 7], [13, 6, 31, 13, "baseGestureHandlerProps"], [13, 29, 31, 36], [13, 32, 31, 36, "exports"], [13, 39, 31, 36], [13, 40, 31, 36, "baseGestureHandlerProps"], [13, 63, 31, 36], [13, 66, 31, 39], [13, 67, 32, 2], [13, 70, 32, 5, "commonProps"], [13, 81, 32, 16], [13, 83, 33, 2], [13, 86, 33, 5, "componentInteractionProps"], [13, 111, 33, 30], [13, 113, 34, 2], [13, 122, 34, 11], [13, 124, 35, 2], [13, 134, 35, 12], [13, 136, 36, 2], [13, 149, 36, 15], [13, 151, 37, 2], [13, 164, 37, 15], [13, 166, 38, 2], [13, 175, 38, 11], [13, 177, 39, 2], [13, 193, 39, 18], [13, 195, 40, 2], [13, 217, 40, 24], [13, 218, 41, 10], [14, 2, 43, 7], [14, 6, 43, 13, "baseGestureHandlerWithDetectorProps"], [14, 41, 43, 48], [14, 44, 43, 48, "exports"], [14, 51, 43, 48], [14, 52, 43, 48, "baseGestureHandlerWithDetectorProps"], [14, 87, 43, 48], [14, 90, 43, 51], [14, 91, 44, 2], [14, 94, 44, 5, "commonProps"], [14, 105, 44, 16], [14, 107, 45, 2], [14, 125, 45, 20], [14, 127, 46, 2], [14, 145, 46, 20], [14, 146, 47, 1], [15, 2, 47, 2], [15, 6, 113, 12, "MouseB<PERSON>on"], [15, 17, 113, 23], [15, 20, 113, 23, "exports"], [15, 27, 113, 23], [15, 28, 113, 23, "MouseB<PERSON>on"], [15, 39, 113, 23], [15, 65, 113, 12, "MouseB<PERSON>on"], [15, 76, 113, 23], [16, 4, 113, 12, "MouseB<PERSON>on"], [16, 15, 113, 23], [16, 16, 113, 12, "MouseB<PERSON>on"], [16, 27, 113, 23], [17, 4, 113, 12, "MouseB<PERSON>on"], [17, 15, 113, 23], [17, 16, 113, 12, "MouseB<PERSON>on"], [17, 27, 113, 23], [18, 4, 113, 12, "MouseB<PERSON>on"], [18, 15, 113, 23], [18, 16, 113, 12, "MouseB<PERSON>on"], [18, 27, 113, 23], [19, 4, 113, 12, "MouseB<PERSON>on"], [19, 15, 113, 23], [19, 16, 113, 12, "MouseB<PERSON>on"], [19, 27, 113, 23], [20, 4, 113, 12, "MouseB<PERSON>on"], [20, 15, 113, 23], [20, 16, 113, 12, "MouseB<PERSON>on"], [20, 27, 113, 23], [21, 4, 113, 12, "MouseB<PERSON>on"], [21, 15, 113, 23], [21, 16, 113, 12, "MouseB<PERSON>on"], [21, 27, 113, 23], [22, 4, 113, 23], [22, 11, 113, 12, "MouseB<PERSON>on"], [22, 22, 113, 23], [23, 2, 113, 23], [23, 9, 139, 0], [24, 2, 186, 0], [25, 2, 187, 0], [26, 0, 187, 0], [26, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}