{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "../../../featureflags/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 89}}], "key": "SDoRPWTN8hRvYGSpUA9tX1WFjrs=", "exportNames": ["*"]}}, {"name": "../oldstylecollections/NodeList", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 63}}], "key": "g5jmir8Q4+ANSEdKwIJy+zXvD+0=", "exportNames": ["*"]}}, {"name": "./internals/NodeInternals", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 26, "column": 35}}], "key": "F4zamoEwysYHV6P3i/QfSQO3bPI=", "exportNames": ["*"]}}, {"name": "./specs/NativeDOM", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 42}}], "key": "9AthY4AxLdDxCbVd7pMFoUw/FmE=", "exportNames": ["*"]}}, {"name": "./ReadOnlyElement", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 342, "column": 27}, "end": {"line": 342, "column": 55}}], "key": "zI0KNLDwEK2Wax54FqFoyWdYCCs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  exports.getChildNodes = getChildNodes;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var ReactNativeFeatureFlags = _interopRequireWildcard(require(_dependencyMap[4], \"../../../featureflags/ReactNativeFeatureFlags\"));\n  var _NodeList = require(_dependencyMap[5], \"../oldstylecollections/NodeList\");\n  var _NodeInternals = require(_dependencyMap[6], \"./internals/NodeInternals\");\n  var _NativeDOM = _interopRequireDefault(require(_dependencyMap[7], \"./specs/NativeDOM\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var ReadOnlyNode = exports.default = /*#__PURE__*/function () {\n    function ReadOnlyNode(instanceHandle, ownerDocument) {\n      (0, _classCallCheck2.default)(this, ReadOnlyNode);\n      (0, _NodeInternals.setOwnerDocument)(this, ownerDocument);\n      (0, _NodeInternals.setInstanceHandle)(this, instanceHandle);\n    }\n    return (0, _createClass2.default)(ReadOnlyNode, [{\n      key: \"childNodes\",\n      get: function () {\n        var childNodes = getChildNodes(this);\n        return (0, _NodeList.createNodeList)(childNodes);\n      }\n    }, {\n      key: \"firstChild\",\n      get: function () {\n        var childNodes = getChildNodes(this);\n        if (childNodes.length === 0) {\n          return null;\n        }\n        return childNodes[0];\n      }\n    }, {\n      key: \"isConnected\",\n      get: function () {\n        var shadowNode = (0, _NodeInternals.getNativeNodeReference)(this);\n        if (shadowNode == null) {\n          return false;\n        }\n        return _NativeDOM.default.isConnected(shadowNode);\n      }\n    }, {\n      key: \"lastChild\",\n      get: function () {\n        var childNodes = getChildNodes(this);\n        if (childNodes.length === 0) {\n          return null;\n        }\n        return childNodes[childNodes.length - 1];\n      }\n    }, {\n      key: \"nextSibling\",\n      get: function () {\n        var _getNodeSiblingsAndPo = getNodeSiblingsAndPosition(this),\n          _getNodeSiblingsAndPo2 = (0, _slicedToArray2.default)(_getNodeSiblingsAndPo, 2),\n          siblings = _getNodeSiblingsAndPo2[0],\n          position = _getNodeSiblingsAndPo2[1];\n        if (position === siblings.length - 1) {\n          return null;\n        }\n        return siblings[position + 1];\n      }\n    }, {\n      key: \"nodeName\",\n      get: function () {\n        throw new TypeError('`nodeName` is abstract and must be implemented in a subclass of `ReadOnlyNode`');\n      }\n    }, {\n      key: \"nodeType\",\n      get: function () {\n        throw new TypeError('`nodeType` is abstract and must be implemented in a subclass of `ReadOnlyNode`');\n      }\n    }, {\n      key: \"nodeValue\",\n      get: function () {\n        throw new TypeError('`nodeValue` is abstract and must be implemented in a subclass of `ReadOnlyNode`');\n      }\n    }, {\n      key: \"ownerDocument\",\n      get: function () {\n        return (0, _NodeInternals.getOwnerDocument)(this);\n      }\n    }, {\n      key: \"parentElement\",\n      get: function () {\n        var parentNode = this.parentNode;\n        if (parentNode instanceof getReadOnlyElementClass()) {\n          return parentNode;\n        }\n        return null;\n      }\n    }, {\n      key: \"parentNode\",\n      get: function () {\n        var shadowNode = (0, _NodeInternals.getNativeNodeReference)(this);\n        if (shadowNode == null) {\n          return null;\n        }\n        var parentInstanceHandle = _NativeDOM.default.getParentNode(shadowNode);\n        if (parentInstanceHandle == null) {\n          return null;\n        }\n        return (0, _NodeInternals.getPublicInstanceFromInstanceHandle)(parentInstanceHandle) ?? null;\n      }\n    }, {\n      key: \"previousSibling\",\n      get: function () {\n        var _getNodeSiblingsAndPo3 = getNodeSiblingsAndPosition(this),\n          _getNodeSiblingsAndPo4 = (0, _slicedToArray2.default)(_getNodeSiblingsAndPo3, 2),\n          siblings = _getNodeSiblingsAndPo4[0],\n          position = _getNodeSiblingsAndPo4[1];\n        if (position === 0) {\n          return null;\n        }\n        return siblings[position - 1];\n      }\n    }, {\n      key: \"textContent\",\n      get: function () {\n        throw new TypeError('`textContent` is abstract and must be implemented in a subclass of `ReadOnlyNode`');\n      }\n    }, {\n      key: \"compareDocumentPosition\",\n      value: function compareDocumentPosition(otherNode) {\n        if (otherNode === this) {\n          return 0;\n        }\n        var shadowNode = (0, _NodeInternals.getNativeNodeReference)(this);\n        var otherShadowNode = (0, _NodeInternals.getNativeNodeReference)(otherNode);\n        if (shadowNode == null || otherShadowNode == null) {\n          return ReadOnlyNode.DOCUMENT_POSITION_DISCONNECTED;\n        }\n        return _NativeDOM.default.compareDocumentPosition(shadowNode, otherShadowNode);\n      }\n    }, {\n      key: \"contains\",\n      value: function contains(otherNode) {\n        if (otherNode === this) {\n          return true;\n        }\n        var position = this.compareDocumentPosition(otherNode);\n        return (position & ReadOnlyNode.DOCUMENT_POSITION_CONTAINED_BY) !== 0;\n      }\n    }, {\n      key: \"getRootNode\",\n      value: function getRootNode() {\n        if (ReactNativeFeatureFlags.enableDOMDocumentAPI()) {\n          if (this.isConnected) {\n            return this.ownerDocument ?? this;\n          }\n          return this;\n        } else {\n          var lastKnownParent = this;\n          var nextPossibleParent = this.parentNode;\n          while (nextPossibleParent != null) {\n            lastKnownParent = nextPossibleParent;\n            nextPossibleParent = nextPossibleParent.parentNode;\n          }\n          return lastKnownParent;\n        }\n      }\n    }, {\n      key: \"hasChildNodes\",\n      value: function hasChildNodes() {\n        return getChildNodes(this).length > 0;\n      }\n    }]);\n  }();\n  ReadOnlyNode.ELEMENT_NODE = 1;\n  ReadOnlyNode.ATTRIBUTE_NODE = 2;\n  ReadOnlyNode.TEXT_NODE = 3;\n  ReadOnlyNode.CDATA_SECTION_NODE = 4;\n  ReadOnlyNode.ENTITY_REFERENCE_NODE = 5;\n  ReadOnlyNode.ENTITY_NODE = 6;\n  ReadOnlyNode.PROCESSING_INSTRUCTION_NODE = 7;\n  ReadOnlyNode.COMMENT_NODE = 8;\n  ReadOnlyNode.DOCUMENT_NODE = 9;\n  ReadOnlyNode.DOCUMENT_TYPE_NODE = 10;\n  ReadOnlyNode.DOCUMENT_FRAGMENT_NODE = 11;\n  ReadOnlyNode.NOTATION_NODE = 12;\n  ReadOnlyNode.DOCUMENT_POSITION_DISCONNECTED = 1;\n  ReadOnlyNode.DOCUMENT_POSITION_PRECEDING = 2;\n  ReadOnlyNode.DOCUMENT_POSITION_FOLLOWING = 4;\n  ReadOnlyNode.DOCUMENT_POSITION_CONTAINS = 8;\n  ReadOnlyNode.DOCUMENT_POSITION_CONTAINED_BY = 16;\n  ReadOnlyNode.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC = 32;\n  function getChildNodes(node) {\n    var shadowNode = (0, _NodeInternals.getNativeNodeReference)(node);\n    if (shadowNode == null) {\n      return [];\n    }\n    var childNodeInstanceHandles = _NativeDOM.default.getChildNodes(shadowNode);\n    return childNodeInstanceHandles.map(instanceHandle => (0, _NodeInternals.getPublicInstanceFromInstanceHandle)(instanceHandle)).filter(Boolean);\n  }\n  function getNodeSiblingsAndPosition(node) {\n    var parent = node.parentNode;\n    if (parent == null) {\n      return [[node], 0];\n    }\n    var siblings = getChildNodes(parent);\n    var position = siblings.indexOf(node);\n    if (position === -1) {\n      throw new TypeError(\"Missing node in parent's child node list\");\n    }\n    return [siblings, position];\n  }\n  var ReadOnlyElementClass;\n  function getReadOnlyElementClass() {\n    if (ReadOnlyElementClass == null) {\n      ReadOnlyElementClass = require(_dependencyMap[8], \"./ReadOnlyElement\").default;\n    }\n    return ReadOnlyElementClass;\n  }\n});", "lineCount": 218, "map": [[11, 2, 18, 0], [11, 6, 18, 0, "ReactNativeFeatureFlags"], [11, 29, 18, 0], [11, 32, 18, 0, "_interopRequireWildcard"], [11, 55, 18, 0], [11, 56, 18, 0, "require"], [11, 63, 18, 0], [11, 64, 18, 0, "_dependencyMap"], [11, 78, 18, 0], [12, 2, 19, 0], [12, 6, 19, 0, "_NodeList"], [12, 15, 19, 0], [12, 18, 19, 0, "require"], [12, 25, 19, 0], [12, 26, 19, 0, "_dependencyMap"], [12, 40, 19, 0], [13, 2, 20, 0], [13, 6, 20, 0, "_NodeInternals"], [13, 20, 20, 0], [13, 23, 20, 0, "require"], [13, 30, 20, 0], [13, 31, 20, 0, "_dependencyMap"], [13, 45, 20, 0], [14, 2, 27, 0], [14, 6, 27, 0, "_NativeDOM"], [14, 16, 27, 0], [14, 19, 27, 0, "_interopRequireDefault"], [14, 41, 27, 0], [14, 42, 27, 0, "require"], [14, 49, 27, 0], [14, 50, 27, 0, "_dependencyMap"], [14, 64, 27, 0], [15, 2, 27, 42], [15, 11, 27, 42, "_interopRequireWildcard"], [15, 35, 27, 42, "e"], [15, 36, 27, 42], [15, 38, 27, 42, "t"], [15, 39, 27, 42], [15, 68, 27, 42, "WeakMap"], [15, 75, 27, 42], [15, 81, 27, 42, "r"], [15, 82, 27, 42], [15, 89, 27, 42, "WeakMap"], [15, 96, 27, 42], [15, 100, 27, 42, "n"], [15, 101, 27, 42], [15, 108, 27, 42, "WeakMap"], [15, 115, 27, 42], [15, 127, 27, 42, "_interopRequireWildcard"], [15, 150, 27, 42], [15, 162, 27, 42, "_interopRequireWildcard"], [15, 163, 27, 42, "e"], [15, 164, 27, 42], [15, 166, 27, 42, "t"], [15, 167, 27, 42], [15, 176, 27, 42, "t"], [15, 177, 27, 42], [15, 181, 27, 42, "e"], [15, 182, 27, 42], [15, 186, 27, 42, "e"], [15, 187, 27, 42], [15, 188, 27, 42, "__esModule"], [15, 198, 27, 42], [15, 207, 27, 42, "e"], [15, 208, 27, 42], [15, 214, 27, 42, "o"], [15, 215, 27, 42], [15, 217, 27, 42, "i"], [15, 218, 27, 42], [15, 220, 27, 42, "f"], [15, 221, 27, 42], [15, 226, 27, 42, "__proto__"], [15, 235, 27, 42], [15, 243, 27, 42, "default"], [15, 250, 27, 42], [15, 252, 27, 42, "e"], [15, 253, 27, 42], [15, 270, 27, 42, "e"], [15, 271, 27, 42], [15, 294, 27, 42, "e"], [15, 295, 27, 42], [15, 320, 27, 42, "e"], [15, 321, 27, 42], [15, 330, 27, 42, "f"], [15, 331, 27, 42], [15, 337, 27, 42, "o"], [15, 338, 27, 42], [15, 341, 27, 42, "t"], [15, 342, 27, 42], [15, 345, 27, 42, "n"], [15, 346, 27, 42], [15, 349, 27, 42, "r"], [15, 350, 27, 42], [15, 358, 27, 42, "o"], [15, 359, 27, 42], [15, 360, 27, 42, "has"], [15, 363, 27, 42], [15, 364, 27, 42, "e"], [15, 365, 27, 42], [15, 375, 27, 42, "o"], [15, 376, 27, 42], [15, 377, 27, 42, "get"], [15, 380, 27, 42], [15, 381, 27, 42, "e"], [15, 382, 27, 42], [15, 385, 27, 42, "o"], [15, 386, 27, 42], [15, 387, 27, 42, "set"], [15, 390, 27, 42], [15, 391, 27, 42, "e"], [15, 392, 27, 42], [15, 394, 27, 42, "f"], [15, 395, 27, 42], [15, 409, 27, 42, "_t"], [15, 411, 27, 42], [15, 415, 27, 42, "e"], [15, 416, 27, 42], [15, 432, 27, 42, "_t"], [15, 434, 27, 42], [15, 441, 27, 42, "hasOwnProperty"], [15, 455, 27, 42], [15, 456, 27, 42, "call"], [15, 460, 27, 42], [15, 461, 27, 42, "e"], [15, 462, 27, 42], [15, 464, 27, 42, "_t"], [15, 466, 27, 42], [15, 473, 27, 42, "i"], [15, 474, 27, 42], [15, 478, 27, 42, "o"], [15, 479, 27, 42], [15, 482, 27, 42, "Object"], [15, 488, 27, 42], [15, 489, 27, 42, "defineProperty"], [15, 503, 27, 42], [15, 508, 27, 42, "Object"], [15, 514, 27, 42], [15, 515, 27, 42, "getOwnPropertyDescriptor"], [15, 539, 27, 42], [15, 540, 27, 42, "e"], [15, 541, 27, 42], [15, 543, 27, 42, "_t"], [15, 545, 27, 42], [15, 552, 27, 42, "i"], [15, 553, 27, 42], [15, 554, 27, 42, "get"], [15, 557, 27, 42], [15, 561, 27, 42, "i"], [15, 562, 27, 42], [15, 563, 27, 42, "set"], [15, 566, 27, 42], [15, 570, 27, 42, "o"], [15, 571, 27, 42], [15, 572, 27, 42, "f"], [15, 573, 27, 42], [15, 575, 27, 42, "_t"], [15, 577, 27, 42], [15, 579, 27, 42, "i"], [15, 580, 27, 42], [15, 584, 27, 42, "f"], [15, 585, 27, 42], [15, 586, 27, 42, "_t"], [15, 588, 27, 42], [15, 592, 27, 42, "e"], [15, 593, 27, 42], [15, 594, 27, 42, "_t"], [15, 596, 27, 42], [15, 607, 27, 42, "f"], [15, 608, 27, 42], [15, 613, 27, 42, "e"], [15, 614, 27, 42], [15, 616, 27, 42, "t"], [15, 617, 27, 42], [16, 2, 27, 42], [16, 6, 29, 21, "ReadOnlyNode"], [16, 18, 29, 33], [16, 21, 29, 33, "exports"], [16, 28, 29, 33], [16, 29, 29, 33, "default"], [16, 36, 29, 33], [17, 4, 30, 2], [17, 13, 30, 2, "ReadOnlyNode"], [17, 26, 31, 4, "instanceHandle"], [17, 40, 31, 34], [17, 42, 33, 4, "ownerDocument"], [17, 55, 33, 45], [17, 57, 34, 4], [18, 6, 34, 4], [18, 10, 34, 4, "_classCallCheck2"], [18, 26, 34, 4], [18, 27, 34, 4, "default"], [18, 34, 34, 4], [18, 42, 34, 4, "ReadOnlyNode"], [18, 54, 34, 4], [19, 6, 37, 4], [19, 10, 37, 4, "setOwnerDocument"], [19, 41, 37, 20], [19, 43, 37, 21], [19, 47, 37, 25], [19, 49, 37, 27, "ownerDocument"], [19, 62, 37, 40], [19, 63, 37, 41], [20, 6, 38, 4], [20, 10, 38, 4, "setInstanceHandle"], [20, 42, 38, 21], [20, 44, 38, 22], [20, 48, 38, 26], [20, 50, 38, 28, "instanceHandle"], [20, 64, 38, 42], [20, 65, 38, 43], [21, 4, 39, 2], [22, 4, 39, 3], [22, 15, 39, 3, "_createClass2"], [22, 28, 39, 3], [22, 29, 39, 3, "default"], [22, 36, 39, 3], [22, 38, 39, 3, "ReadOnlyNode"], [22, 50, 39, 3], [23, 6, 39, 3, "key"], [23, 9, 39, 3], [24, 6, 39, 3, "get"], [24, 9, 39, 3], [24, 11, 41, 2], [24, 20, 41, 2, "get"], [24, 21, 41, 2], [24, 23, 41, 43], [25, 8, 42, 4], [25, 12, 42, 10, "childNodes"], [25, 22, 42, 20], [25, 25, 42, 23, "getChildNodes"], [25, 38, 42, 36], [25, 39, 42, 37], [25, 43, 42, 41], [25, 44, 42, 42], [26, 8, 43, 4], [26, 15, 43, 11], [26, 19, 43, 11, "createNodeList"], [26, 43, 43, 25], [26, 45, 43, 26, "childNodes"], [26, 55, 43, 36], [26, 56, 43, 37], [27, 6, 44, 2], [28, 4, 44, 3], [29, 6, 44, 3, "key"], [29, 9, 44, 3], [30, 6, 44, 3, "get"], [30, 9, 44, 3], [30, 11, 46, 2], [30, 20, 46, 2, "get"], [30, 21, 46, 2], [30, 23, 46, 40], [31, 8, 47, 4], [31, 12, 47, 10, "childNodes"], [31, 22, 47, 20], [31, 25, 47, 23, "getChildNodes"], [31, 38, 47, 36], [31, 39, 47, 37], [31, 43, 47, 41], [31, 44, 47, 42], [32, 8, 49, 4], [32, 12, 49, 8, "childNodes"], [32, 22, 49, 18], [32, 23, 49, 19, "length"], [32, 29, 49, 25], [32, 34, 49, 30], [32, 35, 49, 31], [32, 37, 49, 33], [33, 10, 50, 6], [33, 17, 50, 13], [33, 21, 50, 17], [34, 8, 51, 4], [35, 8, 53, 4], [35, 15, 53, 11, "childNodes"], [35, 25, 53, 21], [35, 26, 53, 22], [35, 27, 53, 23], [35, 28, 53, 24], [36, 6, 54, 2], [37, 4, 54, 3], [38, 6, 54, 3, "key"], [38, 9, 54, 3], [39, 6, 54, 3, "get"], [39, 9, 54, 3], [39, 11, 56, 2], [39, 20, 56, 2, "get"], [39, 21, 56, 2], [39, 23, 56, 29], [40, 8, 57, 4], [40, 12, 57, 10, "shadowNode"], [40, 22, 57, 20], [40, 25, 57, 23], [40, 29, 57, 23, "getNativeNodeReference"], [40, 66, 57, 45], [40, 68, 57, 46], [40, 72, 57, 50], [40, 73, 57, 51], [41, 8, 59, 4], [41, 12, 59, 8, "shadowNode"], [41, 22, 59, 18], [41, 26, 59, 22], [41, 30, 59, 26], [41, 32, 59, 28], [42, 10, 60, 6], [42, 17, 60, 13], [42, 22, 60, 18], [43, 8, 61, 4], [44, 8, 63, 4], [44, 15, 63, 11, "NativeDOM"], [44, 33, 63, 20], [44, 34, 63, 21, "isConnected"], [44, 45, 63, 32], [44, 46, 63, 33, "shadowNode"], [44, 56, 63, 43], [44, 57, 63, 44], [45, 6, 64, 2], [46, 4, 64, 3], [47, 6, 64, 3, "key"], [47, 9, 64, 3], [48, 6, 64, 3, "get"], [48, 9, 64, 3], [48, 11, 66, 2], [48, 20, 66, 2, "get"], [48, 21, 66, 2], [48, 23, 66, 39], [49, 8, 67, 4], [49, 12, 67, 10, "childNodes"], [49, 22, 67, 20], [49, 25, 67, 23, "getChildNodes"], [49, 38, 67, 36], [49, 39, 67, 37], [49, 43, 67, 41], [49, 44, 67, 42], [50, 8, 69, 4], [50, 12, 69, 8, "childNodes"], [50, 22, 69, 18], [50, 23, 69, 19, "length"], [50, 29, 69, 25], [50, 34, 69, 30], [50, 35, 69, 31], [50, 37, 69, 33], [51, 10, 70, 6], [51, 17, 70, 13], [51, 21, 70, 17], [52, 8, 71, 4], [53, 8, 73, 4], [53, 15, 73, 11, "childNodes"], [53, 25, 73, 21], [53, 26, 73, 22, "childNodes"], [53, 36, 73, 32], [53, 37, 73, 33, "length"], [53, 43, 73, 39], [53, 46, 73, 42], [53, 47, 73, 43], [53, 48, 73, 44], [54, 6, 74, 2], [55, 4, 74, 3], [56, 6, 74, 3, "key"], [56, 9, 74, 3], [57, 6, 74, 3, "get"], [57, 9, 74, 3], [57, 11, 76, 2], [57, 20, 76, 2, "get"], [57, 21, 76, 2], [57, 23, 76, 41], [58, 8, 77, 4], [58, 12, 77, 4, "_getNodeSiblingsAndPo"], [58, 33, 77, 4], [58, 36, 77, 33, "getNodeSiblingsAndPosition"], [58, 62, 77, 59], [58, 63, 77, 60], [58, 67, 77, 64], [58, 68, 77, 65], [59, 10, 77, 65, "_getNodeSiblingsAndPo2"], [59, 32, 77, 65], [59, 39, 77, 65, "_slicedToArray2"], [59, 54, 77, 65], [59, 55, 77, 65, "default"], [59, 62, 77, 65], [59, 64, 77, 65, "_getNodeSiblingsAndPo"], [59, 85, 77, 65], [60, 10, 77, 11, "siblings"], [60, 18, 77, 19], [60, 21, 77, 19, "_getNodeSiblingsAndPo2"], [60, 43, 77, 19], [61, 10, 77, 21, "position"], [61, 18, 77, 29], [61, 21, 77, 29, "_getNodeSiblingsAndPo2"], [61, 43, 77, 29], [62, 8, 79, 4], [62, 12, 79, 8, "position"], [62, 20, 79, 16], [62, 25, 79, 21, "siblings"], [62, 33, 79, 29], [62, 34, 79, 30, "length"], [62, 40, 79, 36], [62, 43, 79, 39], [62, 44, 79, 40], [62, 46, 79, 42], [63, 10, 81, 6], [63, 17, 81, 13], [63, 21, 81, 17], [64, 8, 82, 4], [65, 8, 84, 4], [65, 15, 84, 11, "siblings"], [65, 23, 84, 19], [65, 24, 84, 20, "position"], [65, 32, 84, 28], [65, 35, 84, 31], [65, 36, 84, 32], [65, 37, 84, 33], [66, 6, 85, 2], [67, 4, 85, 3], [68, 6, 85, 3, "key"], [68, 9, 85, 3], [69, 6, 85, 3, "get"], [69, 9, 85, 3], [69, 11, 90, 2], [69, 20, 90, 2, "get"], [69, 21, 90, 2], [69, 23, 90, 25], [70, 8, 91, 4], [70, 14, 91, 10], [70, 18, 91, 14, "TypeError"], [70, 27, 91, 23], [70, 28, 92, 6], [70, 108, 93, 4], [70, 109, 93, 5], [71, 6, 94, 2], [72, 4, 94, 3], [73, 6, 94, 3, "key"], [73, 9, 94, 3], [74, 6, 94, 3, "get"], [74, 9, 94, 3], [74, 11, 99, 2], [74, 20, 99, 2, "get"], [74, 21, 99, 2], [74, 23, 99, 25], [75, 8, 100, 4], [75, 14, 100, 10], [75, 18, 100, 14, "TypeError"], [75, 27, 100, 23], [75, 28, 101, 6], [75, 108, 102, 4], [75, 109, 102, 5], [76, 6, 103, 2], [77, 4, 103, 3], [78, 6, 103, 3, "key"], [78, 9, 103, 3], [79, 6, 103, 3, "get"], [79, 9, 103, 3], [79, 11, 108, 2], [79, 20, 108, 2, "get"], [79, 21, 108, 2], [79, 23, 108, 33], [80, 8, 109, 4], [80, 14, 109, 10], [80, 18, 109, 14, "TypeError"], [80, 27, 109, 23], [80, 28, 110, 6], [80, 109, 111, 4], [80, 110, 111, 5], [81, 6, 112, 2], [82, 4, 112, 3], [83, 6, 112, 3, "key"], [83, 9, 112, 3], [84, 6, 112, 3, "get"], [84, 9, 112, 3], [84, 11, 114, 2], [84, 20, 114, 2, "get"], [84, 21, 114, 2], [84, 23, 114, 50], [85, 8, 115, 4], [85, 15, 115, 11], [85, 19, 115, 11, "getOwnerDocument"], [85, 50, 115, 27], [85, 52, 115, 28], [85, 56, 115, 32], [85, 57, 115, 33], [86, 6, 116, 2], [87, 4, 116, 3], [88, 6, 116, 3, "key"], [88, 9, 116, 3], [89, 6, 116, 3, "get"], [89, 9, 116, 3], [89, 11, 118, 2], [89, 20, 118, 2, "get"], [89, 21, 118, 2], [89, 23, 118, 46], [90, 8, 119, 4], [90, 12, 119, 10, "parentNode"], [90, 22, 119, 20], [90, 25, 119, 23], [90, 29, 119, 27], [90, 30, 119, 28, "parentNode"], [90, 40, 119, 38], [91, 8, 121, 4], [91, 12, 121, 8, "parentNode"], [91, 22, 121, 18], [91, 34, 121, 30, "getReadOnlyElementClass"], [91, 57, 121, 53], [91, 58, 121, 54], [91, 59, 121, 55], [91, 61, 121, 57], [92, 10, 122, 6], [92, 17, 122, 13, "parentNode"], [92, 27, 122, 23], [93, 8, 123, 4], [94, 8, 125, 4], [94, 15, 125, 11], [94, 19, 125, 15], [95, 6, 126, 2], [96, 4, 126, 3], [97, 6, 126, 3, "key"], [97, 9, 126, 3], [98, 6, 126, 3, "get"], [98, 9, 126, 3], [98, 11, 128, 2], [98, 20, 128, 2, "get"], [98, 21, 128, 2], [98, 23, 128, 40], [99, 8, 129, 4], [99, 12, 129, 10, "shadowNode"], [99, 22, 129, 20], [99, 25, 129, 23], [99, 29, 129, 23, "getNativeNodeReference"], [99, 66, 129, 45], [99, 68, 129, 46], [99, 72, 129, 50], [99, 73, 129, 51], [100, 8, 131, 4], [100, 12, 131, 8, "shadowNode"], [100, 22, 131, 18], [100, 26, 131, 22], [100, 30, 131, 26], [100, 32, 131, 28], [101, 10, 132, 6], [101, 17, 132, 13], [101, 21, 132, 17], [102, 8, 133, 4], [103, 8, 135, 4], [103, 12, 135, 10, "parentInstanceHandle"], [103, 32, 135, 30], [103, 35, 135, 33, "NativeDOM"], [103, 53, 135, 42], [103, 54, 135, 43, "getParentNode"], [103, 67, 135, 56], [103, 68, 135, 57, "shadowNode"], [103, 78, 135, 67], [103, 79, 135, 68], [104, 8, 137, 4], [104, 12, 137, 8, "parentInstanceHandle"], [104, 32, 137, 28], [104, 36, 137, 32], [104, 40, 137, 36], [104, 42, 137, 38], [105, 10, 138, 6], [105, 17, 138, 13], [105, 21, 138, 17], [106, 8, 139, 4], [107, 8, 141, 4], [107, 15, 141, 11], [107, 19, 141, 11, "getPublicInstanceFromInstanceHandle"], [107, 69, 141, 46], [107, 71, 141, 47, "parentInstanceHandle"], [107, 91, 141, 67], [107, 92, 141, 68], [107, 96, 141, 72], [107, 100, 141, 76], [108, 6, 142, 2], [109, 4, 142, 3], [110, 6, 142, 3, "key"], [110, 9, 142, 3], [111, 6, 142, 3, "get"], [111, 9, 142, 3], [111, 11, 144, 2], [111, 20, 144, 2, "get"], [111, 21, 144, 2], [111, 23, 144, 45], [112, 8, 145, 4], [112, 12, 145, 4, "_getNodeSiblingsAndPo3"], [112, 34, 145, 4], [112, 37, 145, 33, "getNodeSiblingsAndPosition"], [112, 63, 145, 59], [112, 64, 145, 60], [112, 68, 145, 64], [112, 69, 145, 65], [113, 10, 145, 65, "_getNodeSiblingsAndPo4"], [113, 32, 145, 65], [113, 39, 145, 65, "_slicedToArray2"], [113, 54, 145, 65], [113, 55, 145, 65, "default"], [113, 62, 145, 65], [113, 64, 145, 65, "_getNodeSiblingsAndPo3"], [113, 86, 145, 65], [114, 10, 145, 11, "siblings"], [114, 18, 145, 19], [114, 21, 145, 19, "_getNodeSiblingsAndPo4"], [114, 43, 145, 19], [115, 10, 145, 21, "position"], [115, 18, 145, 29], [115, 21, 145, 29, "_getNodeSiblingsAndPo4"], [115, 43, 145, 29], [116, 8, 147, 4], [116, 12, 147, 8, "position"], [116, 20, 147, 16], [116, 25, 147, 21], [116, 26, 147, 22], [116, 28, 147, 24], [117, 10, 149, 6], [117, 17, 149, 13], [117, 21, 149, 17], [118, 8, 150, 4], [119, 8, 152, 4], [119, 15, 152, 11, "siblings"], [119, 23, 152, 19], [119, 24, 152, 20, "position"], [119, 32, 152, 28], [119, 35, 152, 31], [119, 36, 152, 32], [119, 37, 152, 33], [120, 6, 153, 2], [121, 4, 153, 3], [122, 6, 153, 3, "key"], [122, 9, 153, 3], [123, 6, 153, 3, "get"], [123, 9, 153, 3], [123, 11, 158, 2], [123, 20, 158, 2, "get"], [123, 21, 158, 2], [123, 23, 158, 28], [124, 8, 159, 4], [124, 14, 159, 10], [124, 18, 159, 14, "TypeError"], [124, 27, 159, 23], [124, 28, 160, 6], [124, 111, 161, 4], [124, 112, 161, 5], [125, 6, 162, 2], [126, 4, 162, 3], [127, 6, 162, 3, "key"], [127, 9, 162, 3], [128, 6, 162, 3, "value"], [128, 11, 162, 3], [128, 13, 164, 2], [128, 22, 164, 2, "compareDocumentPosition"], [128, 45, 164, 25, "compareDocumentPosition"], [128, 46, 164, 26, "otherNode"], [128, 55, 164, 49], [128, 57, 164, 59], [129, 8, 166, 4], [129, 12, 166, 8, "otherNode"], [129, 21, 166, 17], [129, 26, 166, 22], [129, 30, 166, 26], [129, 32, 166, 28], [130, 10, 167, 6], [130, 17, 167, 13], [130, 18, 167, 14], [131, 8, 168, 4], [132, 8, 170, 4], [132, 12, 170, 10, "shadowNode"], [132, 22, 170, 20], [132, 25, 170, 23], [132, 29, 170, 23, "getNativeNodeReference"], [132, 66, 170, 45], [132, 68, 170, 46], [132, 72, 170, 50], [132, 73, 170, 51], [133, 8, 171, 4], [133, 12, 171, 10, "otherShadowNode"], [133, 27, 171, 25], [133, 30, 171, 28], [133, 34, 171, 28, "getNativeNodeReference"], [133, 71, 171, 50], [133, 73, 171, 51, "otherNode"], [133, 82, 171, 60], [133, 83, 171, 61], [134, 8, 173, 4], [134, 12, 173, 8, "shadowNode"], [134, 22, 173, 18], [134, 26, 173, 22], [134, 30, 173, 26], [134, 34, 173, 30, "otherShadowNode"], [134, 49, 173, 45], [134, 53, 173, 49], [134, 57, 173, 53], [134, 59, 173, 55], [135, 10, 174, 6], [135, 17, 174, 13, "ReadOnlyNode"], [135, 29, 174, 25], [135, 30, 174, 26, "DOCUMENT_POSITION_DISCONNECTED"], [135, 60, 174, 56], [136, 8, 175, 4], [137, 8, 177, 4], [137, 15, 177, 11, "NativeDOM"], [137, 33, 177, 20], [137, 34, 177, 21, "compareDocumentPosition"], [137, 57, 177, 44], [137, 58, 177, 45, "shadowNode"], [137, 68, 177, 55], [137, 70, 177, 57, "otherShadowNode"], [137, 85, 177, 72], [137, 86, 177, 73], [138, 6, 178, 2], [139, 4, 178, 3], [140, 6, 178, 3, "key"], [140, 9, 178, 3], [141, 6, 178, 3, "value"], [141, 11, 178, 3], [141, 13, 180, 2], [141, 22, 180, 2, "contains"], [141, 30, 180, 10, "contains"], [141, 31, 180, 11, "otherNode"], [141, 40, 180, 34], [141, 42, 180, 45], [142, 8, 181, 4], [142, 12, 181, 8, "otherNode"], [142, 21, 181, 17], [142, 26, 181, 22], [142, 30, 181, 26], [142, 32, 181, 28], [143, 10, 182, 6], [143, 17, 182, 13], [143, 21, 182, 17], [144, 8, 183, 4], [145, 8, 185, 4], [145, 12, 185, 10, "position"], [145, 20, 185, 18], [145, 23, 185, 21], [145, 27, 185, 25], [145, 28, 185, 26, "compareDocumentPosition"], [145, 51, 185, 49], [145, 52, 185, 50, "otherNode"], [145, 61, 185, 59], [145, 62, 185, 60], [146, 8, 187, 4], [146, 15, 187, 11], [146, 16, 187, 12, "position"], [146, 24, 187, 20], [146, 27, 187, 23, "ReadOnlyNode"], [146, 39, 187, 35], [146, 40, 187, 36, "DOCUMENT_POSITION_CONTAINED_BY"], [146, 70, 187, 66], [146, 76, 187, 72], [146, 77, 187, 73], [147, 6, 188, 2], [148, 4, 188, 3], [149, 6, 188, 3, "key"], [149, 9, 188, 3], [150, 6, 188, 3, "value"], [150, 11, 188, 3], [150, 13, 190, 2], [150, 22, 190, 2, "getRootNode"], [150, 33, 190, 13, "getRootNode"], [150, 34, 190, 13], [150, 36, 190, 30], [151, 8, 191, 4], [151, 12, 191, 8, "ReactNativeFeatureFlags"], [151, 35, 191, 31], [151, 36, 191, 32, "enableDOMDocumentAPI"], [151, 56, 191, 52], [151, 57, 191, 53], [151, 58, 191, 54], [151, 60, 191, 56], [152, 10, 192, 6], [152, 14, 192, 10], [152, 18, 192, 14], [152, 19, 192, 15, "isConnected"], [152, 30, 192, 26], [152, 32, 192, 28], [153, 12, 194, 8], [153, 19, 194, 15], [153, 23, 194, 19], [153, 24, 194, 20, "ownerDocument"], [153, 37, 194, 33], [153, 41, 194, 37], [153, 45, 194, 41], [154, 10, 195, 6], [155, 10, 197, 6], [155, 17, 197, 13], [155, 21, 197, 17], [156, 8, 198, 4], [156, 9, 198, 5], [156, 15, 198, 11], [157, 10, 200, 6], [157, 14, 200, 10, "lastKnownParent"], [157, 29, 200, 39], [157, 32, 200, 42], [157, 36, 200, 46], [158, 10, 201, 6], [158, 14, 201, 10, "nextPossibleParent"], [158, 32, 201, 43], [158, 35, 201, 46], [158, 39, 201, 50], [158, 40, 201, 51, "parentNode"], [158, 50, 201, 61], [159, 10, 203, 6], [159, 17, 203, 13, "nextPossibleParent"], [159, 35, 203, 31], [159, 39, 203, 35], [159, 43, 203, 39], [159, 45, 203, 41], [160, 12, 204, 8, "lastKnownParent"], [160, 27, 204, 23], [160, 30, 204, 26, "nextPossibleParent"], [160, 48, 204, 44], [161, 12, 205, 8, "nextPossibleParent"], [161, 30, 205, 26], [161, 33, 205, 29, "nextPossibleParent"], [161, 51, 205, 47], [161, 52, 205, 48, "parentNode"], [161, 62, 205, 58], [162, 10, 206, 6], [163, 10, 208, 6], [163, 17, 208, 13, "lastKnownParent"], [163, 32, 208, 28], [164, 8, 209, 4], [165, 6, 210, 2], [166, 4, 210, 3], [167, 6, 210, 3, "key"], [167, 9, 210, 3], [168, 6, 210, 3, "value"], [168, 11, 210, 3], [168, 13, 212, 2], [168, 22, 212, 2, "hasChildNodes"], [168, 35, 212, 15, "hasChildNodes"], [168, 36, 212, 15], [168, 38, 212, 27], [169, 8, 213, 4], [169, 15, 213, 11, "getChildNodes"], [169, 28, 213, 24], [169, 29, 213, 25], [169, 33, 213, 29], [169, 34, 213, 30], [169, 35, 213, 31, "length"], [169, 41, 213, 37], [169, 44, 213, 40], [169, 45, 213, 41], [170, 6, 214, 2], [171, 4, 214, 3], [172, 2, 214, 3], [173, 2, 29, 21, "ReadOnlyNode"], [173, 14, 29, 33], [173, 15, 223, 9, "ELEMENT_NODE"], [173, 27, 223, 21], [173, 30, 223, 32], [173, 31, 223, 33], [174, 2, 29, 21, "ReadOnlyNode"], [174, 14, 29, 33], [174, 15, 227, 9, "ATTRIBUTE_NODE"], [174, 29, 227, 23], [174, 32, 227, 34], [174, 33, 227, 35], [175, 2, 29, 21, "ReadOnlyNode"], [175, 14, 29, 33], [175, 15, 231, 9, "TEXT_NODE"], [175, 24, 231, 18], [175, 27, 231, 29], [175, 28, 231, 30], [176, 2, 29, 21, "ReadOnlyNode"], [176, 14, 29, 33], [176, 15, 235, 9, "CDATA_SECTION_NODE"], [176, 33, 235, 27], [176, 36, 235, 38], [176, 37, 235, 39], [177, 2, 29, 21, "ReadOnlyNode"], [177, 14, 29, 33], [177, 15, 239, 9, "ENTITY_REFERENCE_NODE"], [177, 36, 239, 30], [177, 39, 239, 41], [177, 40, 239, 42], [178, 2, 29, 21, "ReadOnlyNode"], [178, 14, 29, 33], [178, 15, 243, 9, "ENTITY_NODE"], [178, 26, 243, 20], [178, 29, 243, 31], [178, 30, 243, 32], [179, 2, 29, 21, "ReadOnlyNode"], [179, 14, 29, 33], [179, 15, 247, 9, "PROCESSING_INSTRUCTION_NODE"], [179, 42, 247, 36], [179, 45, 247, 47], [179, 46, 247, 48], [180, 2, 29, 21, "ReadOnlyNode"], [180, 14, 29, 33], [180, 15, 251, 9, "COMMENT_NODE"], [180, 27, 251, 21], [180, 30, 251, 32], [180, 31, 251, 33], [181, 2, 29, 21, "ReadOnlyNode"], [181, 14, 29, 33], [181, 15, 255, 9, "DOCUMENT_NODE"], [181, 28, 255, 22], [181, 31, 255, 33], [181, 32, 255, 34], [182, 2, 29, 21, "ReadOnlyNode"], [182, 14, 29, 33], [182, 15, 259, 9, "DOCUMENT_TYPE_NODE"], [182, 33, 259, 27], [182, 36, 259, 38], [182, 38, 259, 40], [183, 2, 29, 21, "ReadOnlyNode"], [183, 14, 29, 33], [183, 15, 263, 9, "DOCUMENT_FRAGMENT_NODE"], [183, 37, 263, 31], [183, 40, 263, 42], [183, 42, 263, 44], [184, 2, 29, 21, "ReadOnlyNode"], [184, 14, 29, 33], [184, 15, 267, 9, "NOTATION_NODE"], [184, 28, 267, 22], [184, 31, 267, 33], [184, 33, 267, 35], [185, 2, 29, 21, "ReadOnlyNode"], [185, 14, 29, 33], [185, 15, 277, 9, "DOCUMENT_POSITION_DISCONNECTED"], [185, 45, 277, 39], [185, 48, 277, 50], [185, 49, 277, 51], [186, 2, 29, 21, "ReadOnlyNode"], [186, 14, 29, 33], [186, 15, 283, 9, "DOCUMENT_POSITION_PRECEDING"], [186, 42, 283, 36], [186, 45, 283, 47], [186, 46, 283, 48], [187, 2, 29, 21, "ReadOnlyNode"], [187, 14, 29, 33], [187, 15, 289, 9, "DOCUMENT_POSITION_FOLLOWING"], [187, 42, 289, 36], [187, 45, 289, 47], [187, 46, 289, 48], [188, 2, 29, 21, "ReadOnlyNode"], [188, 14, 29, 33], [188, 15, 293, 9, "DOCUMENT_POSITION_CONTAINS"], [188, 41, 293, 35], [188, 44, 293, 46], [188, 45, 293, 47], [189, 2, 29, 21, "ReadOnlyNode"], [189, 14, 29, 33], [189, 15, 297, 9, "DOCUMENT_POSITION_CONTAINED_BY"], [189, 45, 297, 39], [189, 48, 297, 50], [189, 50, 297, 52], [190, 2, 29, 21, "ReadOnlyNode"], [190, 14, 29, 33], [190, 15, 301, 9, "DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC"], [190, 56, 301, 50], [190, 59, 301, 61], [190, 61, 301, 63], [191, 2, 304, 7], [191, 11, 304, 16, "getChildNodes"], [191, 24, 304, 29, "getChildNodes"], [191, 25, 305, 2, "node"], [191, 29, 305, 20], [191, 31, 306, 32], [192, 4, 307, 2], [192, 8, 307, 8, "shadowNode"], [192, 18, 307, 18], [192, 21, 307, 21], [192, 25, 307, 21, "getNativeNodeReference"], [192, 62, 307, 43], [192, 64, 307, 44, "node"], [192, 68, 307, 48], [192, 69, 307, 49], [193, 4, 309, 2], [193, 8, 309, 6, "shadowNode"], [193, 18, 309, 16], [193, 22, 309, 20], [193, 26, 309, 24], [193, 28, 309, 26], [194, 6, 310, 4], [194, 13, 310, 11], [194, 15, 310, 13], [195, 4, 311, 2], [196, 4, 313, 2], [196, 8, 313, 8, "childNodeInstanceHandles"], [196, 32, 313, 32], [196, 35, 313, 35, "NativeDOM"], [196, 53, 313, 44], [196, 54, 313, 45, "getChildNodes"], [196, 67, 313, 58], [196, 68, 313, 59, "shadowNode"], [196, 78, 313, 69], [196, 79, 313, 70], [197, 4, 314, 2], [197, 11, 314, 9, "childNodeInstanceHandles"], [197, 35, 314, 33], [197, 36, 315, 5, "map"], [197, 39, 315, 8], [197, 40, 315, 9, "instanceHandle"], [197, 54, 315, 23], [197, 58, 315, 27], [197, 62, 315, 27, "getPublicInstanceFromInstanceHandle"], [197, 112, 315, 62], [197, 114, 315, 63, "instanceHandle"], [197, 128, 315, 77], [197, 129, 315, 78], [197, 130, 315, 79], [197, 131, 316, 5, "filter"], [197, 137, 316, 11], [197, 138, 316, 12, "Boolean"], [197, 145, 316, 19], [197, 146, 316, 20], [198, 2, 317, 0], [199, 2, 319, 0], [199, 11, 319, 9, "getNodeSiblingsAndPosition"], [199, 37, 319, 35, "getNodeSiblingsAndPosition"], [199, 38, 320, 2, "node"], [199, 42, 320, 20], [199, 44, 321, 42], [200, 4, 322, 2], [200, 8, 322, 8, "parent"], [200, 14, 322, 14], [200, 17, 322, 17, "node"], [200, 21, 322, 21], [200, 22, 322, 22, "parentNode"], [200, 32, 322, 32], [201, 4, 323, 2], [201, 8, 323, 6, "parent"], [201, 14, 323, 12], [201, 18, 323, 16], [201, 22, 323, 20], [201, 24, 323, 22], [202, 6, 325, 4], [202, 13, 325, 11], [202, 14, 325, 12], [202, 15, 325, 13, "node"], [202, 19, 325, 17], [202, 20, 325, 18], [202, 22, 325, 20], [202, 23, 325, 21], [202, 24, 325, 22], [203, 4, 326, 2], [204, 4, 328, 2], [204, 8, 328, 8, "siblings"], [204, 16, 328, 16], [204, 19, 328, 19, "getChildNodes"], [204, 32, 328, 32], [204, 33, 328, 33, "parent"], [204, 39, 328, 39], [204, 40, 328, 40], [205, 4, 329, 2], [205, 8, 329, 8, "position"], [205, 16, 329, 16], [205, 19, 329, 19, "siblings"], [205, 27, 329, 27], [205, 28, 329, 28, "indexOf"], [205, 35, 329, 35], [205, 36, 329, 36, "node"], [205, 40, 329, 40], [205, 41, 329, 41], [206, 4, 331, 2], [206, 8, 331, 6, "position"], [206, 16, 331, 14], [206, 21, 331, 19], [206, 22, 331, 20], [206, 23, 331, 21], [206, 25, 331, 23], [207, 6, 332, 4], [207, 12, 332, 10], [207, 16, 332, 14, "TypeError"], [207, 25, 332, 23], [207, 26, 332, 24], [207, 68, 332, 66], [207, 69, 332, 67], [208, 4, 333, 2], [209, 4, 335, 2], [209, 11, 335, 9], [209, 12, 335, 10, "siblings"], [209, 20, 335, 18], [209, 22, 335, 20, "position"], [209, 30, 335, 28], [209, 31, 335, 29], [210, 2, 336, 0], [211, 2, 338, 0], [211, 6, 338, 4, "ReadOnlyElementClass"], [211, 26, 338, 24], [212, 2, 339, 0], [212, 11, 339, 9, "getReadOnlyElementClass"], [212, 34, 339, 32, "getReadOnlyElementClass"], [212, 35, 339, 32], [212, 37, 339, 59], [213, 4, 340, 2], [213, 8, 340, 6, "ReadOnlyElementClass"], [213, 28, 340, 26], [213, 32, 340, 30], [213, 36, 340, 34], [213, 38, 340, 36], [214, 6, 342, 4, "ReadOnlyElementClass"], [214, 26, 342, 24], [214, 29, 342, 27, "require"], [214, 36, 342, 34], [214, 37, 342, 34, "_dependencyMap"], [214, 51, 342, 34], [214, 75, 342, 54], [214, 76, 342, 55], [214, 77, 342, 56, "default"], [214, 84, 342, 63], [215, 4, 343, 2], [216, 4, 344, 2], [216, 11, 344, 9, "ReadOnlyElementClass"], [216, 31, 344, 29], [217, 2, 345, 0], [218, 0, 345, 1], [218, 3]], "functionMap": {"names": ["<global>", "ReadOnlyNode", "constructor", "get__childNodes", "get__first<PERSON><PERSON>d", "get__isConnected", "get__last<PERSON><PERSON>d", "get__nextSibling", "get__nodeName", "get__nodeType", "get__nodeValue", "get__ownerDocument", "get__parentElement", "get__parentNode", "get__previousSibling", "get__textContent", "compareDocumentPosition", "contains", "getRootNode", "hasChildNodes", "getChildNodes", "childNodeInstanceHandles.map$argument_0", "getNodeSiblingsAndPosition", "getReadOnlyElementClass"], "mappings": "AAA;eC4B;ECC;GDS;EEE;GFG;EGE;GHQ;EIE;GJQ;EKE;GLQ;EME;GNS;EOK;GPI;EQK;GRI;ESK;GTI;EUE;GVE;EWE;GXQ;EYE;GZc;EaE;GbS;EcK;GdI;EeE;Gfc;EgBE;GhBQ;EiBE;GjBoB;EkBE;GlBE;CDwF;OoBE;SCW,qED;CpBE;AsBE;CtBiB;AuBG"}}, "type": "js/module"}]}