{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./lib/bytesToUuid", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "Adv/QtGJyBcWM7qosrbwSJ0p9bA=", "exportNames": ["*"]}}, {"name": "./uuid.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 45}, "end": {"line": 2, "column": 53, "index": 98}}], "key": "ShnVy+zJjUvhB99WMorBhtzZ4s0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _bytesToUuid = _interopRequireDefault(require(_dependencyMap[1], \"./lib/bytesToUuid\"));\n  var _uuid = require(_dependencyMap[2], \"./uuid.types\");\n  function uuidv4() {\n    var nativeUuidv4 = globalThis?.expo?.uuidv4;\n    if (!nativeUuidv4) {\n      throw Error(\"Native UUID version 4 generator implementation wasn't found in `expo-modules-core`\");\n    }\n    return nativeUuidv4();\n  }\n  function uuidv5(name, namespace) {\n    var parsedNamespace = Array.isArray(namespace) && namespace.length === 16 ? (0, _bytesToUuid.default)(namespace) : namespace;\n\n    // If parsed namespace is still an array it means that it wasn't valid\n    if (Array.isArray(parsedNamespace)) {\n      throw new Error('`namespace` must be a valid UUID string or an Array of 16 byte values');\n    }\n    var nativeUuidv5 = globalThis?.expo?.uuidv5;\n    if (!nativeUuidv5) {\n      throw Error(\"Native UUID type 5 generator implementation wasn't found in `expo-modules-core`\");\n    }\n    return nativeUuidv5(name, parsedNamespace);\n  }\n  var uuid = {\n    v4: uuidv4,\n    v5: uuidv5,\n    namespace: _uuid.Uuidv5Namespace\n  };\n  var _default = exports.default = uuid;\n});", "lineCount": 35, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_bytesToUuid"], [7, 18, 1, 0], [7, 21, 1, 0, "_interopRequireDefault"], [7, 43, 1, 0], [7, 44, 1, 0, "require"], [7, 51, 1, 0], [7, 52, 1, 0, "_dependencyMap"], [7, 66, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_uuid"], [8, 11, 2, 0], [8, 14, 2, 0, "require"], [8, 21, 2, 0], [8, 22, 2, 0, "_dependencyMap"], [8, 36, 2, 0], [9, 2, 4, 0], [9, 11, 4, 9, "uuidv4"], [9, 17, 4, 15, "uuidv4"], [9, 18, 4, 15], [9, 20, 4, 26], [10, 4, 5, 2], [10, 8, 5, 8, "nativeUuidv4"], [10, 20, 5, 20], [10, 23, 5, 23, "globalThis"], [10, 33, 5, 33], [10, 35, 5, 35, "expo"], [10, 39, 5, 39], [10, 41, 5, 41, "uuidv4"], [10, 47, 5, 47], [11, 4, 7, 2], [11, 8, 7, 6], [11, 9, 7, 7, "nativeUuidv4"], [11, 21, 7, 19], [11, 23, 7, 21], [12, 6, 8, 4], [12, 12, 8, 10, "Error"], [12, 17, 8, 15], [12, 18, 9, 6], [12, 102, 10, 4], [12, 103, 10, 5], [13, 4, 11, 2], [14, 4, 13, 2], [14, 11, 13, 9, "nativeUuidv4"], [14, 23, 13, 21], [14, 24, 13, 22], [14, 25, 13, 23], [15, 2, 14, 0], [16, 2, 16, 0], [16, 11, 16, 9, "uuidv5"], [16, 17, 16, 15, "uuidv5"], [16, 18, 16, 16, "name"], [16, 22, 16, 28], [16, 24, 16, 30, "namespace"], [16, 33, 16, 58], [16, 35, 16, 60], [17, 4, 17, 2], [17, 8, 17, 8, "parsedNamespace"], [17, 23, 17, 23], [17, 26, 18, 4, "Array"], [17, 31, 18, 9], [17, 32, 18, 10, "isArray"], [17, 39, 18, 17], [17, 40, 18, 18, "namespace"], [17, 49, 18, 27], [17, 50, 18, 28], [17, 54, 18, 32, "namespace"], [17, 63, 18, 41], [17, 64, 18, 42, "length"], [17, 70, 18, 48], [17, 75, 18, 53], [17, 77, 18, 55], [17, 80, 18, 58], [17, 84, 18, 58, "bytesToUuid"], [17, 104, 18, 69], [17, 106, 18, 70, "namespace"], [17, 115, 18, 79], [17, 116, 18, 80], [17, 119, 18, 83, "namespace"], [17, 128, 18, 92], [19, 4, 20, 2], [20, 4, 21, 2], [20, 8, 21, 6, "Array"], [20, 13, 21, 11], [20, 14, 21, 12, "isArray"], [20, 21, 21, 19], [20, 22, 21, 20, "parsedNamespace"], [20, 37, 21, 35], [20, 38, 21, 36], [20, 40, 21, 38], [21, 6, 22, 4], [21, 12, 22, 10], [21, 16, 22, 14, "Error"], [21, 21, 22, 19], [21, 22, 22, 20], [21, 93, 22, 91], [21, 94, 22, 92], [22, 4, 23, 2], [23, 4, 25, 2], [23, 8, 25, 8, "nativeUuidv5"], [23, 20, 25, 20], [23, 23, 25, 23, "globalThis"], [23, 33, 25, 33], [23, 35, 25, 35, "expo"], [23, 39, 25, 39], [23, 41, 25, 41, "uuidv5"], [23, 47, 25, 47], [24, 4, 27, 2], [24, 8, 27, 6], [24, 9, 27, 7, "nativeUuidv5"], [24, 21, 27, 19], [24, 23, 27, 21], [25, 6, 28, 4], [25, 12, 28, 10, "Error"], [25, 17, 28, 15], [25, 18, 28, 16], [25, 99, 28, 97], [25, 100, 28, 98], [26, 4, 29, 2], [27, 4, 31, 2], [27, 11, 31, 9, "nativeUuidv5"], [27, 23, 31, 21], [27, 24, 31, 22, "name"], [27, 28, 31, 26], [27, 30, 31, 28, "parsedNamespace"], [27, 45, 31, 43], [27, 46, 31, 44], [28, 2, 32, 0], [29, 2, 34, 0], [29, 6, 34, 6, "uuid"], [29, 10, 34, 16], [29, 13, 34, 19], [30, 4, 35, 2, "v4"], [30, 6, 35, 4], [30, 8, 35, 6, "uuidv4"], [30, 14, 35, 12], [31, 4, 36, 2, "v5"], [31, 6, 36, 4], [31, 8, 36, 6, "uuidv5"], [31, 14, 36, 12], [32, 4, 37, 2, "namespace"], [32, 13, 37, 11], [32, 15, 37, 13, "Uuidv5Namespace"], [33, 2, 38, 0], [33, 3, 38, 1], [34, 2, 38, 2], [34, 6, 38, 2, "_default"], [34, 14, 38, 2], [34, 17, 38, 2, "exports"], [34, 24, 38, 2], [34, 25, 38, 2, "default"], [34, 32, 38, 2], [34, 35, 39, 15, "uuid"], [34, 39, 39, 19], [35, 0, 39, 19], [35, 3]], "functionMap": {"names": ["<global>", "uuidv4", "uuidv5"], "mappings": "AAA;ACG;CDU;AEE;CFgB"}}, "type": "js/module"}]}