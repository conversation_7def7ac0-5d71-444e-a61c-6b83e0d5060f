{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 7, "column": 22, "index": 133}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./customDirectEventTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 134}, "end": {"line": 8, "column": 66, "index": 200}}], "key": "Ma+NL+aygNje9FkYPbYorogBWdc=", "exportNames": ["*"]}}, {"name": "../RNGestureHandlerModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 201}, "end": {"line": 9, "column": 63, "index": 264}}], "key": "bY7FGgfi8WGOEKHKyXsenNEOYXM=", "exportNames": ["*"]}}, {"name": "../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 265}, "end": {"line": 10, "column": 33, "index": 298}}], "key": "ISRoyBmrsYyTcSqLDCBIFNoRZWE=", "exportNames": ["*"]}}, {"name": "./handlersRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 299}, "end": {"line": 15, "column": 28, "index": 414}}], "key": "icHMSVIKxbHLSdF6K64ideInyBg=", "exportNames": ["*"]}}, {"name": "./getNextHandlerTag", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 415}, "end": {"line": 16, "column": 56, "index": 471}}], "key": "ASeSN4ZNwd93et71FExQyqcEKrQ=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 585}, "end": {"line": 23, "column": 64, "index": 649}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}, {"name": "../findNodeHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 650}, "end": {"line": 24, "column": 47, "index": 697}}], "key": "xyNSnMFPN/DtDOjbkd/LduwteJ8=", "exportNames": ["*"]}}, {"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 738}, "end": {"line": 32, "column": 18, "index": 830}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}, {"name": "../ActionType", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 33, "column": 0, "index": 831}, "end": {"line": 33, "column": 43, "index": 874}}], "key": "9/RyJNk4Ge0yFKi3o3v4pSn63xw=", "exportNames": ["*"]}}, {"name": "./PressabilityDebugView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 34, "column": 0, "index": 875}, "end": {"line": 34, "column": 64, "index": 939}}], "key": "xiOM100qt13MoYC/wKaLkt2GEAw=", "exportNames": ["*"]}}, {"name": "../GestureHandlerRootViewContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 35, "column": 0, "index": 940}, "end": {"line": 35, "column": 77, "index": 1017}}], "key": "4dFi+SgkqvO8MS5HEh2OcvBDODc=", "exportNames": ["*"]}}, {"name": "../ghQueueMicrotask", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 36, "column": 0, "index": 1018}, "end": {"line": 36, "column": 55, "index": 1073}}], "key": "Ty3ERJQ4RajY8XDWg1+a8wq7RdE=", "exportNames": ["*"]}}, {"name": "../mountRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 37, "column": 0, "index": 1074}, "end": {"line": 37, "column": 49, "index": 1123}}], "key": "oDeWMahf4tOBx8qk8Rg2qrQUZVM=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = createHandler;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var _reactNative = require(_dependencyMap[7], \"react-native\");\n  var _customDirectEventTypes = require(_dependencyMap[8], \"./customDirectEventTypes\");\n  var _RNGestureHandlerModule = _interopRequireDefault(require(_dependencyMap[9], \"../RNGestureHandlerModule\"));\n  var _State = require(_dependencyMap[10], \"../State\");\n  var _handlersRegistry = require(_dependencyMap[11], \"./handlersRegistry\");\n  var _getNextHandlerTag = require(_dependencyMap[12], \"./getNextHandlerTag\");\n  var _utils = require(_dependencyMap[13], \"./utils\");\n  var _findNodeHandle = _interopRequireDefault(require(_dependencyMap[14], \"../findNodeHandle\"));\n  var _utils2 = require(_dependencyMap[15], \"../utils\");\n  var _ActionType = require(_dependencyMap[16], \"../ActionType\");\n  var _PressabilityDebugView = require(_dependencyMap[17], \"./PressabilityDebugView\");\n  var _GestureHandlerRootViewContext = _interopRequireDefault(require(_dependencyMap[18], \"../GestureHandlerRootViewContext\"));\n  var _ghQueueMicrotask = require(_dependencyMap[19], \"../ghQueueMicrotask\");\n  var _mountRegistry = require(_dependencyMap[20], \"../mountRegistry\");\n  var _jsxDevRuntime = require(_dependencyMap[21], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\handlers\\\\createHandler.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var UIManagerAny = _reactNative.UIManager;\n  _customDirectEventTypes.customDirectEventTypes.topGestureHandlerEvent = {\n    registrationName: 'onGestureHandlerEvent'\n  };\n  var customGHEventsConfigFabricAndroid = {\n    topOnGestureHandlerEvent: {\n      registrationName: 'onGestureHandlerEvent'\n    },\n    topOnGestureHandlerStateChange: {\n      registrationName: 'onGestureHandlerStateChange'\n    }\n  };\n  var customGHEventsConfig = {\n    onGestureHandlerEvent: {\n      registrationName: 'onGestureHandlerEvent'\n    },\n    onGestureHandlerStateChange: {\n      registrationName: 'onGestureHandlerStateChange'\n    },\n    // When using React Native Gesture Handler for Animated.event with useNativeDriver: true\n    // on Android with Fabric enabled, the native part still sends the native events to JS\n    // but prefixed with \"top\". We cannot simply rename the events above so they are prefixed\n    // with \"top\" instead of \"on\" because in such case Animated.events would not be registered.\n    // That's why we need to register another pair of event names.\n    // The incoming events will be queued but never handled.\n    // Without this piece of code below, you'll get the following JS error:\n    // Unsupported top level event type \"topOnGestureHandlerEvent\" dispatched\n    ...((0, _utils2.isFabric)() && _reactNative.Platform.OS === 'android' && customGHEventsConfigFabricAndroid)\n  };\n\n  // Add gesture specific events to genericDirectEventTypes object exported from UIManager\n  // native module.\n  // Once new event types are registered with react it is possible to dispatch these\n  // events to all kind of native views.\n  UIManagerAny.genericDirectEventTypes = {\n    ...UIManagerAny.genericDirectEventTypes,\n    ...customGHEventsConfig\n  };\n  var UIManagerConstants = UIManagerAny.getViewManagerConfig?.('getConstants');\n  if (UIManagerConstants) {\n    UIManagerConstants.genericDirectEventTypes = {\n      ...UIManagerConstants.genericDirectEventTypes,\n      ...customGHEventsConfig\n    };\n  }\n\n  // Wrap JS responder calls and notify gesture handler manager\n  var _UIManagerAny$setJSRe = UIManagerAny.setJSResponder,\n    oldSetJSResponder = _UIManagerAny$setJSRe === void 0 ? () => {\n      // no-op\n    } : _UIManagerAny$setJSRe,\n    _UIManagerAny$clearJS = UIManagerAny.clearJSResponder,\n    oldClearJSResponder = _UIManagerAny$clearJS === void 0 ? () => {\n      // no-op\n    } : _UIManagerAny$clearJS;\n  UIManagerAny.setJSResponder = (tag, blockNativeResponder) => {\n    _RNGestureHandlerModule.default.handleSetJSResponder(tag, blockNativeResponder);\n    oldSetJSResponder(tag, blockNativeResponder);\n  };\n  UIManagerAny.clearJSResponder = () => {\n    _RNGestureHandlerModule.default.handleClearJSResponder();\n    oldClearJSResponder();\n  };\n  var allowTouches = true;\n  var DEV_ON_ANDROID = __DEV__ && _reactNative.Platform.OS === 'android';\n  // Toggled inspector blocks touch events in order to allow inspecting on Android\n  // This needs to be a global variable in order to set initial state for `allowTouches` property in Handler component\n  if (DEV_ON_ANDROID) {\n    _reactNative.DeviceEventEmitter.addListener('toggleElementInspector', () => {\n      allowTouches = !allowTouches;\n    });\n  }\n  function hasUnresolvedRefs(props) {\n    // TODO(TS) - add type for extract arg\n    var extract = refs => {\n      if (!Array.isArray(refs)) {\n        return refs && refs.current === null;\n      }\n      return refs.some(r => r && r.current === null);\n    };\n    return extract(props['simultaneousHandlers']) || extract(props['waitFor']);\n  }\n  var stateToPropMappings = {\n    [_State.State.UNDETERMINED]: undefined,\n    [_State.State.BEGAN]: 'onBegan',\n    [_State.State.FAILED]: 'onFailed',\n    [_State.State.CANCELLED]: 'onCancelled',\n    [_State.State.ACTIVE]: 'onActivated',\n    [_State.State.END]: 'onEnded'\n  };\n\n  // TODO(TS) fix event types\n\n  var UNRESOLVED_REFS_RETRY_LIMIT = 1;\n\n  // TODO(TS) - make sure that BaseGestureHandlerProps doesn't need other generic parameter to work with custom properties.\n  function createHandler(_ref) {\n    var name = _ref.name,\n      _ref$allowedProps = _ref.allowedProps,\n      allowedProps = _ref$allowedProps === void 0 ? [] : _ref$allowedProps,\n      _ref$config = _ref.config,\n      config = _ref$config === void 0 ? {} : _ref$config,\n      transformProps = _ref.transformProps,\n      _ref$customNativeProp = _ref.customNativeProps,\n      customNativeProps = _ref$customNativeProp === void 0 ? [] : _ref$customNativeProp;\n    var Handler = /*#__PURE__*/function (_React$Component) {\n      function Handler(props) {\n        var _this;\n        (0, _classCallCheck2.default)(this, Handler);\n        _this = _callSuper(this, Handler, [props]);\n        _this.handlerTag = -1;\n        _this.onGestureHandlerEvent = event => {\n          if (event.nativeEvent.handlerTag === _this.handlerTag) {\n            if (typeof _this.props.onGestureEvent === 'function') {\n              _this.props.onGestureEvent?.(event);\n            }\n          } else {\n            _this.props.onGestureHandlerEvent?.(event);\n          }\n        };\n        // TODO(TS) - make sure this is right type for event\n        _this.onGestureHandlerStateChange = event => {\n          if (event.nativeEvent.handlerTag === _this.handlerTag) {\n            if (typeof _this.props.onHandlerStateChange === 'function') {\n              _this.props.onHandlerStateChange?.(event);\n            }\n            var state = event.nativeEvent.state;\n            var stateEventName = stateToPropMappings[state];\n            var eventHandler = stateEventName && _this.props[stateEventName];\n            if (eventHandler && typeof eventHandler === 'function') {\n              eventHandler(event);\n            }\n          } else {\n            _this.props.onGestureHandlerStateChange?.(event);\n          }\n        };\n        _this.refHandler = node => {\n          _this.viewNode = node;\n          var child = React.Children.only(_this.props.children);\n          // @ts-ignore Since React 19 ref is accessible as standard prop\n          // https://react.dev/blog/2024/04/25/react-19-upgrade-guide#deprecated-element-ref\n          var ref = (0, _utils2.isReact19)() ? child.props?.ref : child?.ref;\n          if (!ref) {\n            return;\n          }\n          if (typeof ref === 'function') {\n            ref(node);\n          } else {\n            ref.current = node;\n          }\n        };\n        _this.createGestureHandler = newConfig => {\n          _this.handlerTag = (0, _getNextHandlerTag.getNextHandlerTag)();\n          _this.config = newConfig;\n          _RNGestureHandlerModule.default.createGestureHandler(name, _this.handlerTag, newConfig);\n        };\n        _this.attachGestureHandler = newViewTag => {\n          _this.viewTag = newViewTag;\n          if (_reactNative.Platform.OS === 'web') {\n            // Typecast due to dynamic resolution, attachGestureHandler should have web version signature in this branch\n            _RNGestureHandlerModule.default.attachGestureHandler(_this.handlerTag, newViewTag, _ActionType.ActionType.JS_FUNCTION_OLD_API,\n            // ignored on web\n            _this.propsRef);\n          } else {\n            (0, _handlersRegistry.registerOldGestureHandler)(_this.handlerTag, {\n              onGestureEvent: _this.onGestureHandlerEvent,\n              onGestureStateChange: _this.onGestureHandlerStateChange\n            });\n            var actionType = (() => {\n              var onGestureEvent = _this.props?.onGestureEvent;\n              var isGestureHandlerWorklet = onGestureEvent && ('current' in onGestureEvent || 'workletEventHandler' in onGestureEvent);\n              var onHandlerStateChange = _this.props?.onHandlerStateChange;\n              var isStateChangeHandlerWorklet = onHandlerStateChange && ('current' in onHandlerStateChange || 'workletEventHandler' in onHandlerStateChange);\n              var isReanimatedHandler = isGestureHandlerWorklet || isStateChangeHandlerWorklet;\n              if (isReanimatedHandler) {\n                // Reanimated worklet\n                return _ActionType.ActionType.REANIMATED_WORKLET;\n              } else if (onGestureEvent && '__isNative' in onGestureEvent) {\n                // Animated.event with useNativeDriver: true\n                return _ActionType.ActionType.NATIVE_ANIMATED_EVENT;\n              } else {\n                // JS callback or Animated.event with useNativeDriver: false\n                return _ActionType.ActionType.JS_FUNCTION_OLD_API;\n              }\n            })();\n            _RNGestureHandlerModule.default.attachGestureHandler(_this.handlerTag, newViewTag, actionType);\n          }\n          (0, _utils.scheduleFlushOperations)();\n          (0, _ghQueueMicrotask.ghQueueMicrotask)(() => {\n            _mountRegistry.MountRegistry.gestureHandlerWillMount(_this);\n          });\n        };\n        _this.updateGestureHandler = newConfig => {\n          _this.config = newConfig;\n          _RNGestureHandlerModule.default.updateGestureHandler(_this.handlerTag, newConfig);\n          (0, _utils.scheduleFlushOperations)();\n        };\n        _this.config = {};\n        _this.propsRef = /*#__PURE__*/React.createRef();\n        _this.isMountedRef = /*#__PURE__*/React.createRef();\n        _this.state = {\n          allowTouches\n        };\n        if (props.id) {\n          if (_handlersRegistry.handlerIDToTag[props.id] !== undefined) {\n            throw new Error(`Handler with ID \"${props.id}\" already registered`);\n          }\n          _handlersRegistry.handlerIDToTag[props.id] = _this.handlerTag;\n        }\n        return _this;\n      }\n      (0, _inherits2.default)(Handler, _React$Component);\n      return (0, _createClass2.default)(Handler, [{\n        key: \"componentDidMount\",\n        value: function componentDidMount() {\n          var props = this.props;\n          this.isMountedRef.current = true;\n          if (DEV_ON_ANDROID) {\n            this.inspectorToggleListener = _reactNative.DeviceEventEmitter.addListener('toggleElementInspector', () => {\n              this.setState(_ => ({\n                allowTouches\n              }));\n              this.update(UNRESOLVED_REFS_RETRY_LIMIT);\n            });\n          }\n          if (hasUnresolvedRefs(props)) {\n            // If there are unresolved refs (e.g. \".current\" has not yet been set)\n            // passed as `simultaneousHandlers` or `waitFor`, we enqueue a call to\n            // _update method that will try to update native handler props using\n            // queueMicrotask. This makes it so update() function gets called after all\n            // react components are mounted and we expect the missing ref object to\n            // be resolved by then.\n            (0, _ghQueueMicrotask.ghQueueMicrotask)(() => {\n              this.update(UNRESOLVED_REFS_RETRY_LIMIT);\n            });\n          }\n          this.createGestureHandler((0, _utils.filterConfig)(transformProps ? transformProps(this.props) : this.props, [...allowedProps, ...customNativeProps], config));\n          if (!this.viewNode) {\n            throw new Error(`[Gesture Handler] Failed to obtain view for ${Handler.displayName}. Note that old API doesn't support functional components.`);\n          }\n          this.attachGestureHandler((0, _findNodeHandle.default)(this.viewNode)); // TODO(TS) - check if this can be null\n        }\n      }, {\n        key: \"componentDidUpdate\",\n        value: function componentDidUpdate() {\n          var viewTag = (0, _findNodeHandle.default)(this.viewNode);\n          if (this.viewTag !== viewTag) {\n            this.attachGestureHandler(viewTag); // TODO(TS) - check interaction between _viewTag & findNodeHandle\n          }\n          this.update(UNRESOLVED_REFS_RETRY_LIMIT);\n        }\n      }, {\n        key: \"componentWillUnmount\",\n        value: function componentWillUnmount() {\n          this.inspectorToggleListener?.remove();\n          this.isMountedRef.current = false;\n          if (_reactNative.Platform.OS !== 'web') {\n            (0, _handlersRegistry.unregisterOldGestureHandler)(this.handlerTag);\n          }\n          _RNGestureHandlerModule.default.dropGestureHandler(this.handlerTag);\n          (0, _utils.scheduleFlushOperations)();\n          // We can't use this.props.id directly due to TS generic type narrowing bug, see https://github.com/microsoft/TypeScript/issues/13995 for more context\n          var handlerID = this.props.id;\n          if (handlerID) {\n            // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n            delete _handlersRegistry.handlerIDToTag[handlerID];\n          }\n          _mountRegistry.MountRegistry.gestureHandlerWillUnmount(this);\n        }\n      }, {\n        key: \"update\",\n        value: function update(remainingTries) {\n          if (!this.isMountedRef.current) {\n            return;\n          }\n          var props = this.props;\n\n          // When ref is set via a function i.e. `ref={(r) => refObject.current = r}` instead of\n          // `ref={refObject}` it's possible that it won't be resolved in time. Seems like trying\n          // again is easy enough fix.\n          if (hasUnresolvedRefs(props) && remainingTries > 0) {\n            (0, _ghQueueMicrotask.ghQueueMicrotask)(() => {\n              this.update(remainingTries - 1);\n            });\n          } else {\n            var newConfig = (0, _utils.filterConfig)(transformProps ? transformProps(this.props) : this.props, [...allowedProps, ...customNativeProps], config);\n            if (!(0, _utils2.deepEqual)(this.config, newConfig)) {\n              this.updateGestureHandler(newConfig);\n            }\n          }\n        }\n\n        // eslint-disable-next-line @eslint-react/no-unused-class-component-members\n      }, {\n        key: \"setNativeProps\",\n        value: function setNativeProps(updates) {\n          var mergedProps = {\n            ...this.props,\n            ...updates\n          };\n          var newConfig = (0, _utils.filterConfig)(transformProps ? transformProps(mergedProps) : mergedProps, [...allowedProps, ...customNativeProps], config);\n          this.updateGestureHandler(newConfig);\n        }\n      }, {\n        key: \"render\",\n        value: function render() {\n          if (__DEV__ && !this.context && !(0, _utils2.isTestEnv)() && _reactNative.Platform.OS !== 'web') {\n            throw new Error(name + ' must be used as a descendant of GestureHandlerRootView. Otherwise the gestures will not be recognized. See https://docs.swmansion.com/react-native-gesture-handler/docs/installation for more details.');\n          }\n          var gestureEventHandler = this.onGestureHandlerEvent;\n          // Another instance of https://github.com/microsoft/TypeScript/issues/13995\n\n          var _this$props = this.props,\n            onGestureEvent = _this$props.onGestureEvent,\n            onGestureHandlerEvent = _this$props.onGestureHandlerEvent;\n          if (onGestureEvent && typeof onGestureEvent !== 'function') {\n            // If it's not a method it should be an native Animated.event\n            // object. We set it directly as the handler for the view\n            // In this case nested handlers are not going to be supported\n            if (onGestureHandlerEvent) {\n              throw new Error('Nesting touch handlers with native animated driver is not supported yet');\n            }\n            gestureEventHandler = onGestureEvent;\n          } else {\n            if (onGestureHandlerEvent && typeof onGestureHandlerEvent !== 'function') {\n              throw new Error('Nesting touch handlers with native animated driver is not supported yet');\n            }\n          }\n          var gestureStateEventHandler = this.onGestureHandlerStateChange;\n          // Another instance of https://github.com/microsoft/TypeScript/issues/13995\n\n          var _this$props2 = this.props,\n            onHandlerStateChange = _this$props2.onHandlerStateChange,\n            onGestureHandlerStateChange = _this$props2.onGestureHandlerStateChange;\n          if (onHandlerStateChange && typeof onHandlerStateChange !== 'function') {\n            // If it's not a method it should be an native Animated.event\n            // object. We set it directly as the handler for the view\n            // In this case nested handlers are not going to be supported\n            if (onGestureHandlerStateChange) {\n              throw new Error('Nesting touch handlers with native animated driver is not supported yet');\n            }\n            gestureStateEventHandler = onHandlerStateChange;\n          } else {\n            if (onGestureHandlerStateChange && typeof onGestureHandlerStateChange !== 'function') {\n              throw new Error('Nesting touch handlers with native animated driver is not supported yet');\n            }\n          }\n          var events = {\n            onGestureHandlerEvent: this.state.allowTouches ? gestureEventHandler : undefined,\n            onGestureHandlerStateChange: this.state.allowTouches ? gestureStateEventHandler : undefined\n          };\n          this.propsRef.current = events;\n          var child = null;\n          try {\n            child = React.Children.only(this.props.children);\n          } catch (e) {\n            throw new Error((0, _utils2.tagMessage)(`${name} got more than one view as a child. If you want the gesture to work on multiple views, wrap them with a common parent and attach the gesture to that view.`));\n          }\n          var grandChildren = child.props.children;\n          if (__DEV__ && child.type && (child.type === 'RNGestureHandlerButton' || child.type.name === 'View' || child.type.displayName === 'View')) {\n            grandChildren = React.Children.toArray(grandChildren);\n            grandChildren.push(/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_PressabilityDebugView.PressabilityDebugView, {\n              color: \"mediumspringgreen\",\n              hitSlop: child.props.hitSlop\n            }, \"pressabilityDebugView\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 11\n            }, this));\n          }\n          return /*#__PURE__*/React.cloneElement(child, {\n            ref: this.refHandler,\n            collapsable: false,\n            ...((0, _utils2.isTestEnv)() ? {\n              handlerType: name,\n              handlerTag: this.handlerTag,\n              enabled: this.props.enabled\n            } : {}),\n            testID: this.props.testID ?? child.props.testID,\n            ...events\n          }, grandChildren);\n        }\n      }]);\n    }(React.Component);\n    Handler.displayName = name;\n    Handler.contextType = _GestureHandlerRootViewContext.default;\n    return Handler;\n  }\n});", "lineCount": 420, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "React"], [12, 11, 1, 0], [12, 14, 1, 0, "_interopRequireWildcard"], [12, 37, 1, 0], [12, 38, 1, 0, "require"], [12, 45, 1, 0], [12, 46, 1, 0, "_dependencyMap"], [12, 60, 1, 0], [13, 2, 2, 0], [13, 6, 2, 0, "_reactNative"], [13, 18, 2, 0], [13, 21, 2, 0, "require"], [13, 28, 2, 0], [13, 29, 2, 0, "_dependencyMap"], [13, 43, 2, 0], [14, 2, 8, 0], [14, 6, 8, 0, "_customDirectEventTypes"], [14, 29, 8, 0], [14, 32, 8, 0, "require"], [14, 39, 8, 0], [14, 40, 8, 0, "_dependencyMap"], [14, 54, 8, 0], [15, 2, 9, 0], [15, 6, 9, 0, "_RNGestureHandlerModule"], [15, 29, 9, 0], [15, 32, 9, 0, "_interopRequireDefault"], [15, 54, 9, 0], [15, 55, 9, 0, "require"], [15, 62, 9, 0], [15, 63, 9, 0, "_dependencyMap"], [15, 77, 9, 0], [16, 2, 10, 0], [16, 6, 10, 0, "_State"], [16, 12, 10, 0], [16, 15, 10, 0, "require"], [16, 22, 10, 0], [16, 23, 10, 0, "_dependencyMap"], [16, 37, 10, 0], [17, 2, 11, 0], [17, 6, 11, 0, "_handlersRegistry"], [17, 23, 11, 0], [17, 26, 11, 0, "require"], [17, 33, 11, 0], [17, 34, 11, 0, "_dependencyMap"], [17, 48, 11, 0], [18, 2, 16, 0], [18, 6, 16, 0, "_getNextHandlerTag"], [18, 24, 16, 0], [18, 27, 16, 0, "require"], [18, 34, 16, 0], [18, 35, 16, 0, "_dependencyMap"], [18, 49, 16, 0], [19, 2, 23, 0], [19, 6, 23, 0, "_utils"], [19, 12, 23, 0], [19, 15, 23, 0, "require"], [19, 22, 23, 0], [19, 23, 23, 0, "_dependencyMap"], [19, 37, 23, 0], [20, 2, 24, 0], [20, 6, 24, 0, "_findNodeHandle"], [20, 21, 24, 0], [20, 24, 24, 0, "_interopRequireDefault"], [20, 46, 24, 0], [20, 47, 24, 0, "require"], [20, 54, 24, 0], [20, 55, 24, 0, "_dependencyMap"], [20, 69, 24, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_utils2"], [21, 13, 26, 0], [21, 16, 26, 0, "require"], [21, 23, 26, 0], [21, 24, 26, 0, "_dependencyMap"], [21, 38, 26, 0], [22, 2, 33, 0], [22, 6, 33, 0, "_ActionType"], [22, 17, 33, 0], [22, 20, 33, 0, "require"], [22, 27, 33, 0], [22, 28, 33, 0, "_dependencyMap"], [22, 42, 33, 0], [23, 2, 34, 0], [23, 6, 34, 0, "_PressabilityDebugView"], [23, 28, 34, 0], [23, 31, 34, 0, "require"], [23, 38, 34, 0], [23, 39, 34, 0, "_dependencyMap"], [23, 53, 34, 0], [24, 2, 35, 0], [24, 6, 35, 0, "_GestureHandlerRootViewContext"], [24, 36, 35, 0], [24, 39, 35, 0, "_interopRequireDefault"], [24, 61, 35, 0], [24, 62, 35, 0, "require"], [24, 69, 35, 0], [24, 70, 35, 0, "_dependencyMap"], [24, 84, 35, 0], [25, 2, 36, 0], [25, 6, 36, 0, "_ghQueueMicrotask"], [25, 23, 36, 0], [25, 26, 36, 0, "require"], [25, 33, 36, 0], [25, 34, 36, 0, "_dependencyMap"], [25, 48, 36, 0], [26, 2, 37, 0], [26, 6, 37, 0, "_mountRegistry"], [26, 20, 37, 0], [26, 23, 37, 0, "require"], [26, 30, 37, 0], [26, 31, 37, 0, "_dependencyMap"], [26, 45, 37, 0], [27, 2, 37, 49], [27, 6, 37, 49, "_jsxDevRuntime"], [27, 20, 37, 49], [27, 23, 37, 49, "require"], [27, 30, 37, 49], [27, 31, 37, 49, "_dependencyMap"], [27, 45, 37, 49], [28, 2, 37, 49], [28, 6, 37, 49, "_jsxFileName"], [28, 18, 37, 49], [29, 2, 37, 49], [29, 11, 37, 49, "_interopRequireWildcard"], [29, 35, 37, 49, "e"], [29, 36, 37, 49], [29, 38, 37, 49, "t"], [29, 39, 37, 49], [29, 68, 37, 49, "WeakMap"], [29, 75, 37, 49], [29, 81, 37, 49, "r"], [29, 82, 37, 49], [29, 89, 37, 49, "WeakMap"], [29, 96, 37, 49], [29, 100, 37, 49, "n"], [29, 101, 37, 49], [29, 108, 37, 49, "WeakMap"], [29, 115, 37, 49], [29, 127, 37, 49, "_interopRequireWildcard"], [29, 150, 37, 49], [29, 162, 37, 49, "_interopRequireWildcard"], [29, 163, 37, 49, "e"], [29, 164, 37, 49], [29, 166, 37, 49, "t"], [29, 167, 37, 49], [29, 176, 37, 49, "t"], [29, 177, 37, 49], [29, 181, 37, 49, "e"], [29, 182, 37, 49], [29, 186, 37, 49, "e"], [29, 187, 37, 49], [29, 188, 37, 49, "__esModule"], [29, 198, 37, 49], [29, 207, 37, 49, "e"], [29, 208, 37, 49], [29, 214, 37, 49, "o"], [29, 215, 37, 49], [29, 217, 37, 49, "i"], [29, 218, 37, 49], [29, 220, 37, 49, "f"], [29, 221, 37, 49], [29, 226, 37, 49, "__proto__"], [29, 235, 37, 49], [29, 243, 37, 49, "default"], [29, 250, 37, 49], [29, 252, 37, 49, "e"], [29, 253, 37, 49], [29, 270, 37, 49, "e"], [29, 271, 37, 49], [29, 294, 37, 49, "e"], [29, 295, 37, 49], [29, 320, 37, 49, "e"], [29, 321, 37, 49], [29, 330, 37, 49, "f"], [29, 331, 37, 49], [29, 337, 37, 49, "o"], [29, 338, 37, 49], [29, 341, 37, 49, "t"], [29, 342, 37, 49], [29, 345, 37, 49, "n"], [29, 346, 37, 49], [29, 349, 37, 49, "r"], [29, 350, 37, 49], [29, 358, 37, 49, "o"], [29, 359, 37, 49], [29, 360, 37, 49, "has"], [29, 363, 37, 49], [29, 364, 37, 49, "e"], [29, 365, 37, 49], [29, 375, 37, 49, "o"], [29, 376, 37, 49], [29, 377, 37, 49, "get"], [29, 380, 37, 49], [29, 381, 37, 49, "e"], [29, 382, 37, 49], [29, 385, 37, 49, "o"], [29, 386, 37, 49], [29, 387, 37, 49, "set"], [29, 390, 37, 49], [29, 391, 37, 49, "e"], [29, 392, 37, 49], [29, 394, 37, 49, "f"], [29, 395, 37, 49], [29, 409, 37, 49, "_t"], [29, 411, 37, 49], [29, 415, 37, 49, "e"], [29, 416, 37, 49], [29, 432, 37, 49, "_t"], [29, 434, 37, 49], [29, 441, 37, 49, "hasOwnProperty"], [29, 455, 37, 49], [29, 456, 37, 49, "call"], [29, 460, 37, 49], [29, 461, 37, 49, "e"], [29, 462, 37, 49], [29, 464, 37, 49, "_t"], [29, 466, 37, 49], [29, 473, 37, 49, "i"], [29, 474, 37, 49], [29, 478, 37, 49, "o"], [29, 479, 37, 49], [29, 482, 37, 49, "Object"], [29, 488, 37, 49], [29, 489, 37, 49, "defineProperty"], [29, 503, 37, 49], [29, 508, 37, 49, "Object"], [29, 514, 37, 49], [29, 515, 37, 49, "getOwnPropertyDescriptor"], [29, 539, 37, 49], [29, 540, 37, 49, "e"], [29, 541, 37, 49], [29, 543, 37, 49, "_t"], [29, 545, 37, 49], [29, 552, 37, 49, "i"], [29, 553, 37, 49], [29, 554, 37, 49, "get"], [29, 557, 37, 49], [29, 561, 37, 49, "i"], [29, 562, 37, 49], [29, 563, 37, 49, "set"], [29, 566, 37, 49], [29, 570, 37, 49, "o"], [29, 571, 37, 49], [29, 572, 37, 49, "f"], [29, 573, 37, 49], [29, 575, 37, 49, "_t"], [29, 577, 37, 49], [29, 579, 37, 49, "i"], [29, 580, 37, 49], [29, 584, 37, 49, "f"], [29, 585, 37, 49], [29, 586, 37, 49, "_t"], [29, 588, 37, 49], [29, 592, 37, 49, "e"], [29, 593, 37, 49], [29, 594, 37, 49, "_t"], [29, 596, 37, 49], [29, 607, 37, 49, "f"], [29, 608, 37, 49], [29, 613, 37, 49, "e"], [29, 614, 37, 49], [29, 616, 37, 49, "t"], [29, 617, 37, 49], [30, 2, 37, 49], [30, 11, 37, 49, "_callSuper"], [30, 22, 37, 49, "t"], [30, 23, 37, 49], [30, 25, 37, 49, "o"], [30, 26, 37, 49], [30, 28, 37, 49, "e"], [30, 29, 37, 49], [30, 40, 37, 49, "o"], [30, 41, 37, 49], [30, 48, 37, 49, "_getPrototypeOf2"], [30, 64, 37, 49], [30, 65, 37, 49, "default"], [30, 72, 37, 49], [30, 74, 37, 49, "o"], [30, 75, 37, 49], [30, 82, 37, 49, "_possibleConstructorReturn2"], [30, 109, 37, 49], [30, 110, 37, 49, "default"], [30, 117, 37, 49], [30, 119, 37, 49, "t"], [30, 120, 37, 49], [30, 122, 37, 49, "_isNativeReflectConstruct"], [30, 147, 37, 49], [30, 152, 37, 49, "Reflect"], [30, 159, 37, 49], [30, 160, 37, 49, "construct"], [30, 169, 37, 49], [30, 170, 37, 49, "o"], [30, 171, 37, 49], [30, 173, 37, 49, "e"], [30, 174, 37, 49], [30, 186, 37, 49, "_getPrototypeOf2"], [30, 202, 37, 49], [30, 203, 37, 49, "default"], [30, 210, 37, 49], [30, 212, 37, 49, "t"], [30, 213, 37, 49], [30, 215, 37, 49, "constructor"], [30, 226, 37, 49], [30, 230, 37, 49, "o"], [30, 231, 37, 49], [30, 232, 37, 49, "apply"], [30, 237, 37, 49], [30, 238, 37, 49, "t"], [30, 239, 37, 49], [30, 241, 37, 49, "e"], [30, 242, 37, 49], [31, 2, 37, 49], [31, 11, 37, 49, "_isNativeReflectConstruct"], [31, 37, 37, 49], [31, 51, 37, 49, "t"], [31, 52, 37, 49], [31, 56, 37, 49, "Boolean"], [31, 63, 37, 49], [31, 64, 37, 49, "prototype"], [31, 73, 37, 49], [31, 74, 37, 49, "valueOf"], [31, 81, 37, 49], [31, 82, 37, 49, "call"], [31, 86, 37, 49], [31, 87, 37, 49, "Reflect"], [31, 94, 37, 49], [31, 95, 37, 49, "construct"], [31, 104, 37, 49], [31, 105, 37, 49, "Boolean"], [31, 112, 37, 49], [31, 145, 37, 49, "t"], [31, 146, 37, 49], [31, 159, 37, 49, "_isNativeReflectConstruct"], [31, 184, 37, 49], [31, 196, 37, 49, "_isNativeReflectConstruct"], [31, 197, 37, 49], [31, 210, 37, 49, "t"], [31, 211, 37, 49], [32, 2, 40, 0], [32, 6, 40, 6, "UIManagerAny"], [32, 18, 40, 18], [32, 21, 40, 21, "UIManager"], [32, 43, 40, 37], [33, 2, 42, 0, "customDirectEventTypes"], [33, 48, 42, 22], [33, 49, 42, 23, "topGestureHandlerEvent"], [33, 71, 42, 45], [33, 74, 42, 48], [34, 4, 43, 2, "registrationName"], [34, 20, 43, 18], [34, 22, 43, 20], [35, 2, 44, 0], [35, 3, 44, 1], [36, 2, 46, 0], [36, 6, 46, 6, "customGHEventsConfigFabricAndroid"], [36, 39, 46, 39], [36, 42, 46, 42], [37, 4, 47, 2, "topOnGestureHandlerEvent"], [37, 28, 47, 26], [37, 30, 47, 28], [38, 6, 47, 30, "registrationName"], [38, 22, 47, 46], [38, 24, 47, 48], [39, 4, 47, 72], [39, 5, 47, 73], [40, 4, 48, 2, "topOnGestureHandlerStateChange"], [40, 34, 48, 32], [40, 36, 48, 34], [41, 6, 49, 4, "registrationName"], [41, 22, 49, 20], [41, 24, 49, 22], [42, 4, 50, 2], [43, 2, 51, 0], [43, 3, 51, 1], [44, 2, 53, 0], [44, 6, 53, 6, "customGHEventsConfig"], [44, 26, 53, 26], [44, 29, 53, 29], [45, 4, 54, 2, "onGestureHandlerEvent"], [45, 25, 54, 23], [45, 27, 54, 25], [46, 6, 54, 27, "registrationName"], [46, 22, 54, 43], [46, 24, 54, 45], [47, 4, 54, 69], [47, 5, 54, 70], [48, 4, 55, 2, "onGestureHandlerStateChange"], [48, 31, 55, 29], [48, 33, 55, 31], [49, 6, 56, 4, "registrationName"], [49, 22, 56, 20], [49, 24, 56, 22], [50, 4, 57, 2], [50, 5, 57, 3], [51, 4, 59, 2], [52, 4, 60, 2], [53, 4, 61, 2], [54, 4, 62, 2], [55, 4, 63, 2], [56, 4, 64, 2], [57, 4, 65, 2], [58, 4, 66, 2], [59, 4, 67, 2], [59, 8, 67, 6], [59, 12, 67, 6, "isF<PERSON><PERSON>"], [59, 28, 67, 14], [59, 30, 67, 15], [59, 31, 67, 16], [59, 35, 68, 4, "Platform"], [59, 56, 68, 12], [59, 57, 68, 13, "OS"], [59, 59, 68, 15], [59, 64, 68, 20], [59, 73, 68, 29], [59, 77, 69, 4, "customGHEventsConfigFabricAndroid"], [59, 110, 69, 37], [60, 2, 70, 0], [60, 3, 70, 1], [62, 2, 72, 0], [63, 2, 73, 0], [64, 2, 74, 0], [65, 2, 75, 0], [66, 2, 76, 0, "UIManagerAny"], [66, 14, 76, 12], [66, 15, 76, 13, "genericDirectEventTypes"], [66, 38, 76, 36], [66, 41, 76, 39], [67, 4, 77, 2], [67, 7, 77, 5, "UIManagerAny"], [67, 19, 77, 17], [67, 20, 77, 18, "genericDirectEventTypes"], [67, 43, 77, 41], [68, 4, 78, 2], [68, 7, 78, 5, "customGHEventsConfig"], [69, 2, 79, 0], [69, 3, 79, 1], [70, 2, 81, 0], [70, 6, 81, 6, "UIManagerConstants"], [70, 24, 81, 24], [70, 27, 81, 27, "UIManagerAny"], [70, 39, 81, 39], [70, 40, 81, 40, "getViewManagerConfig"], [70, 60, 81, 60], [70, 63, 81, 63], [70, 77, 81, 77], [70, 78, 81, 78], [71, 2, 83, 0], [71, 6, 83, 4, "UIManagerConstants"], [71, 24, 83, 22], [71, 26, 83, 24], [72, 4, 84, 2, "UIManagerConstants"], [72, 22, 84, 20], [72, 23, 84, 21, "genericDirectEventTypes"], [72, 46, 84, 44], [72, 49, 84, 47], [73, 6, 85, 4], [73, 9, 85, 7, "UIManagerConstants"], [73, 27, 85, 25], [73, 28, 85, 26, "genericDirectEventTypes"], [73, 51, 85, 49], [74, 6, 86, 4], [74, 9, 86, 7, "customGHEventsConfig"], [75, 4, 87, 2], [75, 5, 87, 3], [76, 2, 88, 0], [78, 2, 90, 0], [79, 2, 91, 0], [79, 6, 91, 0, "_UIManagerAny$setJSRe"], [79, 27, 91, 0], [79, 30, 98, 4, "UIManagerAny"], [79, 42, 98, 16], [79, 43, 92, 2, "setJSResponder"], [79, 57, 92, 16], [80, 4, 92, 18, "oldSetJSResponder"], [80, 21, 92, 35], [80, 24, 92, 35, "_UIManagerAny$setJSRe"], [80, 45, 92, 35], [80, 59, 92, 38], [80, 65, 92, 44], [81, 6, 93, 4], [82, 4, 93, 4], [82, 5, 94, 3], [82, 8, 94, 3, "_UIManagerAny$setJSRe"], [82, 29, 94, 3], [83, 4, 94, 3, "_UIManagerAny$clearJS"], [83, 25, 94, 3], [83, 28, 98, 4, "UIManagerAny"], [83, 40, 98, 16], [83, 41, 95, 2, "clearJSResponder"], [83, 57, 95, 18], [84, 4, 95, 20, "oldClearJSResponder"], [84, 23, 95, 39], [84, 26, 95, 39, "_UIManagerAny$clearJS"], [84, 47, 95, 39], [84, 61, 95, 42], [84, 67, 95, 48], [85, 6, 96, 4], [86, 4, 96, 4], [86, 5, 97, 3], [86, 8, 97, 3, "_UIManagerAny$clearJS"], [86, 29, 97, 3], [87, 2, 99, 0, "UIManagerAny"], [87, 14, 99, 12], [87, 15, 99, 13, "setJSResponder"], [87, 29, 99, 27], [87, 32, 99, 30], [87, 33, 99, 31, "tag"], [87, 36, 99, 42], [87, 38, 99, 44, "blockNativeResponder"], [87, 58, 99, 73], [87, 63, 99, 78], [88, 4, 100, 2, "RNGestureHandlerModule"], [88, 35, 100, 24], [88, 36, 100, 25, "handleSetJSResponder"], [88, 56, 100, 45], [88, 57, 100, 46, "tag"], [88, 60, 100, 49], [88, 62, 100, 51, "blockNativeResponder"], [88, 82, 100, 71], [88, 83, 100, 72], [89, 4, 101, 2, "oldSetJSResponder"], [89, 21, 101, 19], [89, 22, 101, 20, "tag"], [89, 25, 101, 23], [89, 27, 101, 25, "blockNativeResponder"], [89, 47, 101, 45], [89, 48, 101, 46], [90, 2, 102, 0], [90, 3, 102, 1], [91, 2, 103, 0, "UIManagerAny"], [91, 14, 103, 12], [91, 15, 103, 13, "clearJSResponder"], [91, 31, 103, 29], [91, 34, 103, 32], [91, 40, 103, 38], [92, 4, 104, 2, "RNGestureHandlerModule"], [92, 35, 104, 24], [92, 36, 104, 25, "handleClearJSResponder"], [92, 58, 104, 47], [92, 59, 104, 48], [92, 60, 104, 49], [93, 4, 105, 2, "oldClearJSResponder"], [93, 23, 105, 21], [93, 24, 105, 22], [93, 25, 105, 23], [94, 2, 106, 0], [94, 3, 106, 1], [95, 2, 108, 0], [95, 6, 108, 4, "allowTouches"], [95, 18, 108, 16], [95, 21, 108, 19], [95, 25, 108, 23], [96, 2, 109, 0], [96, 6, 109, 6, "DEV_ON_ANDROID"], [96, 20, 109, 20], [96, 23, 109, 23, "__DEV__"], [96, 30, 109, 30], [96, 34, 109, 34, "Platform"], [96, 55, 109, 42], [96, 56, 109, 43, "OS"], [96, 58, 109, 45], [96, 63, 109, 50], [96, 72, 109, 59], [97, 2, 110, 0], [98, 2, 111, 0], [99, 2, 112, 0], [99, 6, 112, 4, "DEV_ON_ANDROID"], [99, 20, 112, 18], [99, 22, 112, 20], [100, 4, 113, 2, "DeviceEventEmitter"], [100, 35, 113, 20], [100, 36, 113, 21, "addListener"], [100, 47, 113, 32], [100, 48, 113, 33], [100, 72, 113, 57], [100, 74, 113, 59], [100, 80, 113, 65], [101, 6, 114, 4, "allowTouches"], [101, 18, 114, 16], [101, 21, 114, 19], [101, 22, 114, 20, "allowTouches"], [101, 34, 114, 32], [102, 4, 115, 2], [102, 5, 115, 3], [102, 6, 115, 4], [103, 2, 116, 0], [104, 2, 121, 0], [104, 11, 121, 9, "hasUnresolvedRefs"], [104, 28, 121, 26, "hasUnresolvedRefs"], [104, 29, 122, 2, "props"], [104, 34, 122, 24], [104, 36, 123, 2], [105, 4, 124, 2], [106, 4, 125, 2], [106, 8, 125, 8, "extract"], [106, 15, 125, 15], [106, 18, 125, 19, "refs"], [106, 22, 125, 36], [106, 26, 125, 41], [107, 6, 126, 4], [107, 10, 126, 8], [107, 11, 126, 9, "Array"], [107, 16, 126, 14], [107, 17, 126, 15, "isArray"], [107, 24, 126, 22], [107, 25, 126, 23, "refs"], [107, 29, 126, 27], [107, 30, 126, 28], [107, 32, 126, 30], [108, 8, 127, 6], [108, 15, 127, 13, "refs"], [108, 19, 127, 17], [108, 23, 127, 21, "refs"], [108, 27, 127, 25], [108, 28, 127, 26, "current"], [108, 35, 127, 33], [108, 40, 127, 38], [108, 44, 127, 42], [109, 6, 128, 4], [110, 6, 129, 4], [110, 13, 129, 11, "refs"], [110, 17, 129, 15], [110, 18, 129, 16, "some"], [110, 22, 129, 20], [110, 23, 129, 22, "r"], [110, 24, 129, 23], [110, 28, 129, 28, "r"], [110, 29, 129, 29], [110, 33, 129, 33, "r"], [110, 34, 129, 34], [110, 35, 129, 35, "current"], [110, 42, 129, 42], [110, 47, 129, 47], [110, 51, 129, 51], [110, 52, 129, 52], [111, 4, 130, 2], [111, 5, 130, 3], [112, 4, 131, 2], [112, 11, 131, 9, "extract"], [112, 18, 131, 16], [112, 19, 131, 17, "props"], [112, 24, 131, 22], [112, 25, 131, 23], [112, 47, 131, 45], [112, 48, 131, 46], [112, 49, 131, 47], [112, 53, 131, 51, "extract"], [112, 60, 131, 58], [112, 61, 131, 59, "props"], [112, 66, 131, 64], [112, 67, 131, 65], [112, 76, 131, 74], [112, 77, 131, 75], [112, 78, 131, 76], [113, 2, 132, 0], [114, 2, 134, 0], [114, 6, 134, 6, "stateToPropMappings"], [114, 25, 134, 25], [114, 28, 134, 28], [115, 4, 135, 2], [115, 5, 135, 3, "State"], [115, 17, 135, 8], [115, 18, 135, 9, "UNDETERMINED"], [115, 30, 135, 21], [115, 33, 135, 24, "undefined"], [115, 42, 135, 33], [116, 4, 136, 2], [116, 5, 136, 3, "State"], [116, 17, 136, 8], [116, 18, 136, 9, "BEGAN"], [116, 23, 136, 14], [116, 26, 136, 17], [116, 35, 136, 26], [117, 4, 137, 2], [117, 5, 137, 3, "State"], [117, 17, 137, 8], [117, 18, 137, 9, "FAILED"], [117, 24, 137, 15], [117, 27, 137, 18], [117, 37, 137, 28], [118, 4, 138, 2], [118, 5, 138, 3, "State"], [118, 17, 138, 8], [118, 18, 138, 9, "CANCELLED"], [118, 27, 138, 18], [118, 30, 138, 21], [118, 43, 138, 34], [119, 4, 139, 2], [119, 5, 139, 3, "State"], [119, 17, 139, 8], [119, 18, 139, 9, "ACTIVE"], [119, 24, 139, 15], [119, 27, 139, 18], [119, 40, 139, 31], [120, 4, 140, 2], [120, 5, 140, 3, "State"], [120, 17, 140, 8], [120, 18, 140, 9, "END"], [120, 21, 140, 12], [120, 24, 140, 15], [121, 2, 141, 0], [121, 3, 141, 10], [123, 2, 152, 0], [125, 2, 166, 0], [125, 6, 166, 6, "UNRESOLVED_REFS_RETRY_LIMIT"], [125, 33, 166, 33], [125, 36, 166, 36], [125, 37, 166, 37], [127, 2, 168, 0], [128, 2, 169, 15], [128, 11, 169, 24, "createHandler"], [128, 24, 169, 37, "createHandler"], [128, 25, 169, 37, "_ref"], [128, 29, 169, 37], [128, 31, 178, 76], [129, 4, 178, 76], [129, 8, 173, 2, "name"], [129, 12, 173, 6], [129, 15, 173, 6, "_ref"], [129, 19, 173, 6], [129, 20, 173, 2, "name"], [129, 24, 173, 6], [130, 6, 173, 6, "_ref$allowedProps"], [130, 23, 173, 6], [130, 26, 173, 6, "_ref"], [130, 30, 173, 6], [130, 31, 174, 2, "allowedProps"], [130, 43, 174, 14], [131, 6, 174, 2, "allowedProps"], [131, 18, 174, 14], [131, 21, 174, 14, "_ref$allowedProps"], [131, 38, 174, 14], [131, 52, 174, 17], [131, 54, 174, 19], [131, 57, 174, 19, "_ref$allowedProps"], [131, 74, 174, 19], [132, 6, 174, 19, "_ref$config"], [132, 17, 174, 19], [132, 20, 174, 19, "_ref"], [132, 24, 174, 19], [132, 25, 175, 2, "config"], [132, 31, 175, 8], [133, 6, 175, 2, "config"], [133, 12, 175, 8], [133, 15, 175, 8, "_ref$config"], [133, 26, 175, 8], [133, 40, 175, 11], [133, 41, 175, 12], [133, 42, 175, 13], [133, 45, 175, 13, "_ref$config"], [133, 56, 175, 13], [134, 6, 176, 2, "transformProps"], [134, 20, 176, 16], [134, 23, 176, 16, "_ref"], [134, 27, 176, 16], [134, 28, 176, 2, "transformProps"], [134, 42, 176, 16], [135, 6, 176, 16, "_ref$customNativeProp"], [135, 27, 176, 16], [135, 30, 176, 16, "_ref"], [135, 34, 176, 16], [135, 35, 177, 2, "customNativeProps"], [135, 52, 177, 19], [136, 6, 177, 2, "customNativeProps"], [136, 23, 177, 19], [136, 26, 177, 19, "_ref$customNativeProp"], [136, 47, 177, 19], [136, 61, 177, 22], [136, 63, 177, 24], [136, 66, 177, 24, "_ref$customNativeProp"], [136, 87, 177, 24], [137, 4, 177, 24], [137, 8, 182, 8, "Handler"], [137, 15, 182, 15], [137, 41, 182, 15, "_React$Component"], [137, 57, 182, 15], [138, 6, 197, 4], [138, 15, 197, 4, "Handler"], [138, 23, 197, 16, "props"], [138, 28, 197, 48], [138, 30, 197, 50], [139, 8, 197, 50], [139, 12, 197, 50, "_this"], [139, 17, 197, 50], [140, 8, 197, 50], [140, 12, 197, 50, "_classCallCheck2"], [140, 28, 197, 50], [140, 29, 197, 50, "default"], [140, 36, 197, 50], [140, 44, 197, 50, "Handler"], [140, 51, 197, 50], [141, 8, 198, 6, "_this"], [141, 13, 198, 6], [141, 16, 198, 6, "_callSuper"], [141, 26, 198, 6], [141, 33, 198, 6, "Handler"], [141, 40, 198, 6], [141, 43, 198, 12, "props"], [141, 48, 198, 17], [142, 8, 198, 19, "_this"], [142, 13, 198, 19], [142, 14, 189, 12, "handlerTag"], [142, 24, 189, 22], [142, 27, 189, 25], [142, 28, 189, 26], [142, 29, 189, 27], [143, 8, 189, 27, "_this"], [143, 13, 189, 27], [143, 14, 279, 12, "onGestureHandlerEvent"], [143, 35, 279, 33], [143, 38, 279, 37, "event"], [143, 43, 279, 59], [143, 47, 279, 64], [144, 10, 280, 6], [144, 14, 280, 10, "event"], [144, 19, 280, 15], [144, 20, 280, 16, "nativeEvent"], [144, 31, 280, 27], [144, 32, 280, 28, "handlerTag"], [144, 42, 280, 38], [144, 47, 280, 43, "_this"], [144, 52, 280, 43], [144, 53, 280, 48, "handlerTag"], [144, 63, 280, 58], [144, 65, 280, 60], [145, 12, 281, 8], [145, 16, 281, 12], [145, 23, 281, 19, "_this"], [145, 28, 281, 19], [145, 29, 281, 24, "props"], [145, 34, 281, 29], [145, 35, 281, 30, "onGestureEvent"], [145, 49, 281, 44], [145, 54, 281, 49], [145, 64, 281, 59], [145, 66, 281, 61], [146, 14, 282, 10, "_this"], [146, 19, 282, 10], [146, 20, 282, 15, "props"], [146, 25, 282, 20], [146, 26, 282, 21, "onGestureEvent"], [146, 40, 282, 35], [146, 43, 282, 38, "event"], [146, 48, 282, 43], [146, 49, 282, 44], [147, 12, 283, 8], [148, 10, 284, 6], [148, 11, 284, 7], [148, 17, 284, 13], [149, 12, 285, 8, "_this"], [149, 17, 285, 8], [149, 18, 285, 13, "props"], [149, 23, 285, 18], [149, 24, 285, 19, "onGestureHandlerEvent"], [149, 45, 285, 40], [149, 48, 285, 43, "event"], [149, 53, 285, 48], [149, 54, 285, 49], [150, 10, 286, 6], [151, 8, 287, 4], [151, 9, 287, 5], [152, 8, 289, 4], [153, 8, 289, 4, "_this"], [153, 13, 289, 4], [153, 14, 290, 12, "onGestureHandlerStateChange"], [153, 41, 290, 39], [153, 44, 291, 6, "event"], [153, 49, 291, 39], [153, 53, 292, 9], [154, 10, 293, 6], [154, 14, 293, 10, "event"], [154, 19, 293, 15], [154, 20, 293, 16, "nativeEvent"], [154, 31, 293, 27], [154, 32, 293, 28, "handlerTag"], [154, 42, 293, 38], [154, 47, 293, 43, "_this"], [154, 52, 293, 43], [154, 53, 293, 48, "handlerTag"], [154, 63, 293, 58], [154, 65, 293, 60], [155, 12, 294, 8], [155, 16, 294, 12], [155, 23, 294, 19, "_this"], [155, 28, 294, 19], [155, 29, 294, 24, "props"], [155, 34, 294, 29], [155, 35, 294, 30, "onHandlerStateChange"], [155, 55, 294, 50], [155, 60, 294, 55], [155, 70, 294, 65], [155, 72, 294, 67], [156, 14, 295, 10, "_this"], [156, 19, 295, 10], [156, 20, 295, 15, "props"], [156, 25, 295, 20], [156, 26, 295, 21, "onHandlerStateChange"], [156, 46, 295, 41], [156, 49, 295, 44, "event"], [156, 54, 295, 49], [156, 55, 295, 50], [157, 12, 296, 8], [158, 12, 298, 8], [158, 16, 298, 14, "state"], [158, 21, 298, 42], [158, 24, 298, 45, "event"], [158, 29, 298, 50], [158, 30, 298, 51, "nativeEvent"], [158, 41, 298, 62], [158, 42, 298, 63, "state"], [158, 47, 298, 68], [159, 12, 299, 8], [159, 16, 299, 14, "stateEventName"], [159, 30, 299, 28], [159, 33, 299, 31, "stateToPropMappings"], [159, 52, 299, 50], [159, 53, 299, 51, "state"], [159, 58, 299, 56], [159, 59, 299, 57], [160, 12, 300, 8], [160, 16, 300, 14, "<PERSON><PERSON><PERSON><PERSON>"], [160, 28, 300, 26], [160, 31, 300, 29, "stateEventName"], [160, 45, 300, 43], [160, 49, 300, 47, "_this"], [160, 54, 300, 47], [160, 55, 300, 52, "props"], [160, 60, 300, 57], [160, 61, 300, 58, "stateEventName"], [160, 75, 300, 72], [160, 76, 300, 73], [161, 12, 301, 8], [161, 16, 301, 12, "<PERSON><PERSON><PERSON><PERSON>"], [161, 28, 301, 24], [161, 32, 301, 28], [161, 39, 301, 35, "<PERSON><PERSON><PERSON><PERSON>"], [161, 51, 301, 47], [161, 56, 301, 52], [161, 66, 301, 62], [161, 68, 301, 64], [162, 14, 302, 10, "<PERSON><PERSON><PERSON><PERSON>"], [162, 26, 302, 22], [162, 27, 302, 23, "event"], [162, 32, 302, 28], [162, 33, 302, 29], [163, 12, 303, 8], [164, 10, 304, 6], [164, 11, 304, 7], [164, 17, 304, 13], [165, 12, 305, 8, "_this"], [165, 17, 305, 8], [165, 18, 305, 13, "props"], [165, 23, 305, 18], [165, 24, 305, 19, "onGestureHandlerStateChange"], [165, 51, 305, 46], [165, 54, 305, 49, "event"], [165, 59, 305, 54], [165, 60, 305, 55], [166, 10, 306, 6], [167, 8, 307, 4], [167, 9, 307, 5], [168, 8, 307, 5, "_this"], [168, 13, 307, 5], [168, 14, 309, 12, "ref<PERSON><PERSON><PERSON>"], [168, 24, 309, 22], [168, 27, 309, 26, "node"], [168, 31, 309, 35], [168, 35, 309, 40], [169, 10, 310, 6, "_this"], [169, 15, 310, 6], [169, 16, 310, 11, "viewNode"], [169, 24, 310, 19], [169, 27, 310, 22, "node"], [169, 31, 310, 26], [170, 10, 312, 6], [170, 14, 312, 12, "child"], [170, 19, 312, 17], [170, 22, 312, 20, "React"], [170, 27, 312, 25], [170, 28, 312, 26, "Children"], [170, 36, 312, 34], [170, 37, 312, 35, "only"], [170, 41, 312, 39], [170, 42, 312, 40, "_this"], [170, 47, 312, 40], [170, 48, 312, 45, "props"], [170, 53, 312, 50], [170, 54, 312, 51, "children"], [170, 62, 312, 59], [170, 63, 312, 60], [171, 10, 313, 6], [172, 10, 314, 6], [173, 10, 315, 6], [173, 14, 315, 12, "ref"], [173, 17, 315, 15], [173, 20, 315, 18], [173, 24, 315, 18, "isReact19"], [173, 41, 315, 27], [173, 43, 315, 28], [173, 44, 315, 29], [173, 47, 315, 33, "child"], [173, 52, 315, 38], [173, 53, 315, 56, "props"], [173, 58, 315, 61], [173, 60, 315, 63, "ref"], [173, 63, 315, 66], [173, 66, 315, 69, "child"], [173, 71, 315, 74], [173, 73, 315, 76, "ref"], [173, 76, 315, 79], [174, 10, 317, 6], [174, 14, 317, 10], [174, 15, 317, 11, "ref"], [174, 18, 317, 14], [174, 20, 317, 16], [175, 12, 318, 8], [176, 10, 319, 6], [177, 10, 321, 6], [177, 14, 321, 10], [177, 21, 321, 17, "ref"], [177, 24, 321, 20], [177, 29, 321, 25], [177, 39, 321, 35], [177, 41, 321, 37], [178, 12, 322, 8, "ref"], [178, 15, 322, 11], [178, 16, 322, 12, "node"], [178, 20, 322, 16], [178, 21, 322, 17], [179, 10, 323, 6], [179, 11, 323, 7], [179, 17, 323, 13], [180, 12, 324, 8, "ref"], [180, 15, 324, 11], [180, 16, 324, 12, "current"], [180, 23, 324, 19], [180, 26, 324, 22, "node"], [180, 30, 324, 26], [181, 10, 325, 6], [182, 8, 326, 4], [182, 9, 326, 5], [183, 8, 326, 5, "_this"], [183, 13, 326, 5], [183, 14, 328, 12, "createGestureHandler"], [183, 34, 328, 32], [183, 37, 329, 6, "newConfig"], [183, 46, 329, 50], [183, 50, 330, 9], [184, 10, 331, 6, "_this"], [184, 15, 331, 6], [184, 16, 331, 11, "handlerTag"], [184, 26, 331, 21], [184, 29, 331, 24], [184, 33, 331, 24, "getNextHandlerTag"], [184, 69, 331, 41], [184, 71, 331, 42], [184, 72, 331, 43], [185, 10, 332, 6, "_this"], [185, 15, 332, 6], [185, 16, 332, 11, "config"], [185, 22, 332, 17], [185, 25, 332, 20, "newConfig"], [185, 34, 332, 29], [186, 10, 334, 6, "RNGestureHandlerModule"], [186, 41, 334, 28], [186, 42, 334, 29, "createGestureHandler"], [186, 62, 334, 49], [186, 63, 335, 8, "name"], [186, 67, 335, 12], [186, 69, 336, 8, "_this"], [186, 74, 336, 8], [186, 75, 336, 13, "handlerTag"], [186, 85, 336, 23], [186, 87, 337, 8, "newConfig"], [186, 96, 338, 6], [186, 97, 338, 7], [187, 8, 339, 4], [187, 9, 339, 5], [188, 8, 339, 5, "_this"], [188, 13, 339, 5], [188, 14, 341, 12, "attachGestureHandler"], [188, 34, 341, 32], [188, 37, 341, 36, "newViewTag"], [188, 47, 341, 54], [188, 51, 341, 59], [189, 10, 342, 6, "_this"], [189, 15, 342, 6], [189, 16, 342, 11, "viewTag"], [189, 23, 342, 18], [189, 26, 342, 21, "newViewTag"], [189, 36, 342, 31], [190, 10, 344, 6], [190, 14, 344, 10, "Platform"], [190, 35, 344, 18], [190, 36, 344, 19, "OS"], [190, 38, 344, 21], [190, 43, 344, 26], [190, 48, 344, 31], [190, 50, 344, 33], [191, 12, 345, 8], [192, 12, 347, 10, "RNGestureHandlerModule"], [192, 43, 347, 32], [192, 44, 347, 33, "attachGestureHandler"], [192, 64, 347, 53], [192, 65, 349, 10, "_this"], [192, 70, 349, 10], [192, 71, 349, 15, "handlerTag"], [192, 81, 349, 25], [192, 83, 350, 10, "newViewTag"], [192, 93, 350, 20], [192, 95, 351, 10, "ActionType"], [192, 117, 351, 20], [192, 118, 351, 21, "JS_FUNCTION_OLD_API"], [192, 137, 351, 40], [193, 12, 351, 42], [194, 12, 352, 10, "_this"], [194, 17, 352, 10], [194, 18, 352, 15, "propsRef"], [194, 26, 353, 8], [194, 27, 353, 9], [195, 10, 354, 6], [195, 11, 354, 7], [195, 17, 354, 13], [196, 12, 355, 8], [196, 16, 355, 8, "registerOldGestureHandler"], [196, 59, 355, 33], [196, 61, 355, 34, "_this"], [196, 66, 355, 34], [196, 67, 355, 39, "handlerTag"], [196, 77, 355, 49], [196, 79, 355, 51], [197, 14, 356, 10, "onGestureEvent"], [197, 28, 356, 24], [197, 30, 356, 26, "_this"], [197, 35, 356, 26], [197, 36, 356, 31, "onGestureHandlerEvent"], [197, 57, 356, 52], [198, 14, 357, 10, "onGestureStateChange"], [198, 34, 357, 30], [198, 36, 357, 32, "_this"], [198, 41, 357, 32], [198, 42, 357, 37, "onGestureHandlerStateChange"], [199, 12, 358, 8], [199, 13, 358, 9], [199, 14, 358, 10], [200, 12, 360, 8], [200, 16, 360, 14, "actionType"], [200, 26, 360, 24], [200, 29, 360, 27], [200, 30, 360, 28], [200, 36, 360, 34], [201, 14, 361, 10], [201, 18, 361, 16, "onGestureEvent"], [201, 32, 361, 30], [201, 35, 361, 33, "_this"], [201, 40, 361, 33], [201, 41, 361, 38, "props"], [201, 46, 361, 43], [201, 48, 361, 45, "onGestureEvent"], [201, 62, 361, 59], [202, 14, 362, 10], [202, 18, 362, 16, "isGestureHandlerWorklet"], [202, 41, 362, 39], [202, 44, 363, 12, "onGestureEvent"], [202, 58, 363, 26], [202, 63, 364, 13], [202, 72, 364, 22], [202, 76, 364, 26, "onGestureEvent"], [202, 90, 364, 40], [202, 94, 365, 14], [202, 115, 365, 35], [202, 119, 365, 39, "onGestureEvent"], [202, 133, 365, 53], [202, 134, 365, 54], [203, 14, 366, 10], [203, 18, 366, 16, "onHandlerStateChange"], [203, 38, 366, 36], [203, 41, 366, 39, "_this"], [203, 46, 366, 39], [203, 47, 366, 44, "props"], [203, 52, 366, 49], [203, 54, 366, 51, "onHandlerStateChange"], [203, 74, 366, 71], [204, 14, 367, 10], [204, 18, 367, 16, "isStateChangeHandlerWorklet"], [204, 45, 367, 43], [204, 48, 368, 12, "onHandlerStateChange"], [204, 68, 368, 32], [204, 73, 369, 13], [204, 82, 369, 22], [204, 86, 369, 26, "onHandlerStateChange"], [204, 106, 369, 46], [204, 110, 370, 14], [204, 131, 370, 35], [204, 135, 370, 39, "onHandlerStateChange"], [204, 155, 370, 59], [204, 156, 370, 60], [205, 14, 371, 10], [205, 18, 371, 16, "is<PERSON><PERSON><PERSON>"], [205, 37, 371, 35], [205, 40, 372, 12, "isGestureHandlerWorklet"], [205, 63, 372, 35], [205, 67, 372, 39, "isStateChangeHandlerWorklet"], [205, 94, 372, 66], [206, 14, 373, 10], [206, 18, 373, 14, "is<PERSON><PERSON><PERSON>"], [206, 37, 373, 33], [206, 39, 373, 35], [207, 16, 374, 12], [208, 16, 375, 12], [208, 23, 375, 19, "ActionType"], [208, 45, 375, 29], [208, 46, 375, 30, "REANIMATED_WORKLET"], [208, 64, 375, 48], [209, 14, 376, 10], [209, 15, 376, 11], [209, 21, 376, 17], [209, 25, 376, 21, "onGestureEvent"], [209, 39, 376, 35], [209, 43, 376, 39], [209, 55, 376, 51], [209, 59, 376, 55, "onGestureEvent"], [209, 73, 376, 69], [209, 75, 376, 71], [210, 16, 377, 12], [211, 16, 378, 12], [211, 23, 378, 19, "ActionType"], [211, 45, 378, 29], [211, 46, 378, 30, "NATIVE_ANIMATED_EVENT"], [211, 67, 378, 51], [212, 14, 379, 10], [212, 15, 379, 11], [212, 21, 379, 17], [213, 16, 380, 12], [214, 16, 381, 12], [214, 23, 381, 19, "ActionType"], [214, 45, 381, 29], [214, 46, 381, 30, "JS_FUNCTION_OLD_API"], [214, 65, 381, 49], [215, 14, 382, 10], [216, 12, 383, 8], [216, 13, 383, 9], [216, 15, 383, 11], [216, 16, 383, 12], [217, 12, 385, 8, "RNGestureHandlerModule"], [217, 43, 385, 30], [217, 44, 385, 31, "attachGestureHandler"], [217, 64, 385, 51], [217, 65, 386, 10, "_this"], [217, 70, 386, 10], [217, 71, 386, 15, "handlerTag"], [217, 81, 386, 25], [217, 83, 387, 10, "newViewTag"], [217, 93, 387, 20], [217, 95, 388, 10, "actionType"], [217, 105, 389, 8], [217, 106, 389, 9], [218, 10, 390, 6], [219, 10, 392, 6], [219, 14, 392, 6, "scheduleFlushOperations"], [219, 44, 392, 29], [219, 46, 392, 30], [219, 47, 392, 31], [220, 10, 394, 6], [220, 14, 394, 6, "ghQueueMicrotask"], [220, 48, 394, 22], [220, 50, 394, 23], [220, 56, 394, 29], [221, 12, 395, 8, "MountRegistry"], [221, 40, 395, 21], [221, 41, 395, 22, "gestureHandlerWillMount"], [221, 64, 395, 45], [221, 65, 395, 45, "_this"], [221, 70, 395, 50], [221, 71, 395, 51], [222, 10, 396, 6], [222, 11, 396, 7], [222, 12, 396, 8], [223, 8, 397, 4], [223, 9, 397, 5], [224, 8, 397, 5, "_this"], [224, 13, 397, 5], [224, 14, 399, 12, "updateGestureHandler"], [224, 34, 399, 32], [224, 37, 400, 6, "newConfig"], [224, 46, 400, 50], [224, 50, 401, 9], [225, 10, 402, 6, "_this"], [225, 15, 402, 6], [225, 16, 402, 11, "config"], [225, 22, 402, 17], [225, 25, 402, 20, "newConfig"], [225, 34, 402, 29], [226, 10, 404, 6, "RNGestureHandlerModule"], [226, 41, 404, 28], [226, 42, 404, 29, "updateGestureHandler"], [226, 62, 404, 49], [226, 63, 404, 50, "_this"], [226, 68, 404, 50], [226, 69, 404, 55, "handlerTag"], [226, 79, 404, 65], [226, 81, 404, 67, "newConfig"], [226, 90, 404, 76], [226, 91, 404, 77], [227, 10, 405, 6], [227, 14, 405, 6, "scheduleFlushOperations"], [227, 44, 405, 29], [227, 46, 405, 30], [227, 47, 405, 31], [228, 8, 406, 4], [228, 9, 406, 5], [229, 8, 199, 6, "_this"], [229, 13, 199, 6], [229, 14, 199, 11, "config"], [229, 20, 199, 17], [229, 23, 199, 20], [229, 24, 199, 21], [229, 25, 199, 22], [230, 8, 200, 6, "_this"], [230, 13, 200, 6], [230, 14, 200, 11, "propsRef"], [230, 22, 200, 19], [230, 38, 200, 22, "React"], [230, 43, 200, 27], [230, 44, 200, 28, "createRef"], [230, 53, 200, 37], [230, 54, 200, 38], [230, 55, 200, 39], [231, 8, 201, 6, "_this"], [231, 13, 201, 6], [231, 14, 201, 11, "isMountedRef"], [231, 26, 201, 23], [231, 42, 201, 26, "React"], [231, 47, 201, 31], [231, 48, 201, 32, "createRef"], [231, 57, 201, 41], [231, 58, 201, 42], [231, 59, 201, 43], [232, 8, 202, 6, "_this"], [232, 13, 202, 6], [232, 14, 202, 11, "state"], [232, 19, 202, 16], [232, 22, 202, 19], [233, 10, 202, 21, "allowTouches"], [234, 8, 202, 34], [234, 9, 202, 35], [235, 8, 203, 6], [235, 12, 203, 10, "props"], [235, 17, 203, 15], [235, 18, 203, 16, "id"], [235, 20, 203, 18], [235, 22, 203, 20], [236, 10, 204, 8], [236, 14, 204, 12, "handlerIDToTag"], [236, 46, 204, 26], [236, 47, 204, 27, "props"], [236, 52, 204, 32], [236, 53, 204, 33, "id"], [236, 55, 204, 35], [236, 56, 204, 36], [236, 61, 204, 41, "undefined"], [236, 70, 204, 50], [236, 72, 204, 52], [237, 12, 205, 10], [237, 18, 205, 16], [237, 22, 205, 20, "Error"], [237, 27, 205, 25], [237, 28, 205, 26], [237, 48, 205, 46, "props"], [237, 53, 205, 51], [237, 54, 205, 52, "id"], [237, 56, 205, 54], [237, 78, 205, 76], [237, 79, 205, 77], [238, 10, 206, 8], [239, 10, 207, 8, "handlerIDToTag"], [239, 42, 207, 22], [239, 43, 207, 23, "props"], [239, 48, 207, 28], [239, 49, 207, 29, "id"], [239, 51, 207, 31], [239, 52, 207, 32], [239, 55, 207, 35, "_this"], [239, 60, 207, 35], [239, 61, 207, 40, "handlerTag"], [239, 71, 207, 50], [240, 8, 208, 6], [241, 8, 208, 7], [241, 15, 208, 7, "_this"], [241, 20, 208, 7], [242, 6, 209, 4], [243, 6, 209, 5], [243, 10, 209, 5, "_inherits2"], [243, 20, 209, 5], [243, 21, 209, 5, "default"], [243, 28, 209, 5], [243, 30, 209, 5, "Handler"], [243, 37, 209, 5], [243, 39, 209, 5, "_React$Component"], [243, 55, 209, 5], [244, 6, 209, 5], [244, 17, 209, 5, "_createClass2"], [244, 30, 209, 5], [244, 31, 209, 5, "default"], [244, 38, 209, 5], [244, 40, 209, 5, "Handler"], [244, 47, 209, 5], [245, 8, 209, 5, "key"], [245, 11, 209, 5], [246, 8, 209, 5, "value"], [246, 13, 209, 5], [246, 15, 211, 4], [246, 24, 211, 4, "componentDidMount"], [246, 41, 211, 21, "componentDidMount"], [246, 42, 211, 21], [246, 44, 211, 24], [247, 10, 212, 6], [247, 14, 212, 12, "props"], [247, 19, 212, 34], [247, 22, 212, 37], [247, 26, 212, 41], [247, 27, 212, 42, "props"], [247, 32, 212, 47], [248, 10, 213, 6], [248, 14, 213, 10], [248, 15, 213, 11, "isMountedRef"], [248, 27, 213, 23], [248, 28, 213, 24, "current"], [248, 35, 213, 31], [248, 38, 213, 34], [248, 42, 213, 38], [249, 10, 215, 6], [249, 14, 215, 10, "DEV_ON_ANDROID"], [249, 28, 215, 24], [249, 30, 215, 26], [250, 12, 216, 8], [250, 16, 216, 12], [250, 17, 216, 13, "inspectorToggleListener"], [250, 40, 216, 36], [250, 43, 216, 39, "DeviceEventEmitter"], [250, 74, 216, 57], [250, 75, 216, 58, "addListener"], [250, 86, 216, 69], [250, 87, 217, 10], [250, 111, 217, 34], [250, 113, 218, 10], [250, 119, 218, 16], [251, 14, 219, 12], [251, 18, 219, 16], [251, 19, 219, 17, "setState"], [251, 27, 219, 25], [251, 28, 219, 27, "_"], [251, 29, 219, 28], [251, 34, 219, 34], [252, 16, 219, 36, "allowTouches"], [253, 14, 219, 49], [253, 15, 219, 50], [253, 16, 219, 51], [253, 17, 219, 52], [254, 14, 220, 12], [254, 18, 220, 16], [254, 19, 220, 17, "update"], [254, 25, 220, 23], [254, 26, 220, 24, "UNRESOLVED_REFS_RETRY_LIMIT"], [254, 53, 220, 51], [254, 54, 220, 52], [255, 12, 221, 10], [255, 13, 222, 8], [255, 14, 222, 9], [256, 10, 223, 6], [257, 10, 224, 6], [257, 14, 224, 10, "hasUnresolvedRefs"], [257, 31, 224, 27], [257, 32, 224, 28, "props"], [257, 37, 224, 33], [257, 38, 224, 34], [257, 40, 224, 36], [258, 12, 225, 8], [259, 12, 226, 8], [260, 12, 227, 8], [261, 12, 228, 8], [262, 12, 229, 8], [263, 12, 230, 8], [264, 12, 231, 8], [264, 16, 231, 8, "ghQueueMicrotask"], [264, 50, 231, 24], [264, 52, 231, 25], [264, 58, 231, 31], [265, 14, 232, 10], [265, 18, 232, 14], [265, 19, 232, 15, "update"], [265, 25, 232, 21], [265, 26, 232, 22, "UNRESOLVED_REFS_RETRY_LIMIT"], [265, 53, 232, 49], [265, 54, 232, 50], [266, 12, 233, 8], [266, 13, 233, 9], [266, 14, 233, 10], [267, 10, 234, 6], [268, 10, 236, 6], [268, 14, 236, 10], [268, 15, 236, 11, "createGestureHandler"], [268, 35, 236, 31], [268, 36, 237, 8], [268, 40, 237, 8, "filterConfig"], [268, 59, 237, 20], [268, 61, 238, 10, "transformProps"], [268, 75, 238, 24], [268, 78, 238, 27, "transformProps"], [268, 92, 238, 41], [268, 93, 238, 42], [268, 97, 238, 46], [268, 98, 238, 47, "props"], [268, 103, 238, 52], [268, 104, 238, 53], [268, 107, 238, 56], [268, 111, 238, 60], [268, 112, 238, 61, "props"], [268, 117, 238, 66], [268, 119, 239, 10], [268, 120, 239, 11], [268, 123, 239, 14, "allowedProps"], [268, 135, 239, 26], [268, 137, 239, 28], [268, 140, 239, 31, "customNativeProps"], [268, 157, 239, 48], [268, 158, 239, 49], [268, 160, 240, 10, "config"], [268, 166, 241, 8], [268, 167, 242, 6], [268, 168, 242, 7], [269, 10, 244, 6], [269, 14, 244, 10], [269, 15, 244, 11], [269, 19, 244, 15], [269, 20, 244, 16, "viewNode"], [269, 28, 244, 24], [269, 30, 244, 26], [270, 12, 245, 8], [270, 18, 245, 14], [270, 22, 245, 18, "Error"], [270, 27, 245, 23], [270, 28, 246, 10], [270, 75, 246, 57, "Handler"], [270, 82, 246, 64], [270, 83, 246, 65, "displayName"], [270, 94, 246, 76], [270, 154, 247, 8], [270, 155, 247, 9], [271, 10, 248, 6], [272, 10, 250, 6], [272, 14, 250, 10], [272, 15, 250, 11, "attachGestureHandler"], [272, 35, 250, 31], [272, 36, 250, 32], [272, 40, 250, 32, "findNodeHandle"], [272, 63, 250, 46], [272, 65, 250, 47], [272, 69, 250, 51], [272, 70, 250, 52, "viewNode"], [272, 78, 250, 60], [272, 79, 250, 71], [272, 80, 250, 72], [272, 81, 250, 73], [272, 82, 250, 74], [273, 8, 251, 4], [274, 6, 251, 5], [275, 8, 251, 5, "key"], [275, 11, 251, 5], [276, 8, 251, 5, "value"], [276, 13, 251, 5], [276, 15, 253, 4], [276, 24, 253, 4, "componentDidUpdate"], [276, 42, 253, 22, "componentDidUpdate"], [276, 43, 253, 22], [276, 45, 253, 25], [277, 10, 254, 6], [277, 14, 254, 12, "viewTag"], [277, 21, 254, 19], [277, 24, 254, 22], [277, 28, 254, 22, "findNodeHandle"], [277, 51, 254, 36], [277, 53, 254, 37], [277, 57, 254, 41], [277, 58, 254, 42, "viewNode"], [277, 66, 254, 50], [277, 67, 254, 51], [278, 10, 255, 6], [278, 14, 255, 10], [278, 18, 255, 14], [278, 19, 255, 15, "viewTag"], [278, 26, 255, 22], [278, 31, 255, 27, "viewTag"], [278, 38, 255, 34], [278, 40, 255, 36], [279, 12, 256, 8], [279, 16, 256, 12], [279, 17, 256, 13, "attachGestureHandler"], [279, 37, 256, 33], [279, 38, 256, 34, "viewTag"], [279, 45, 256, 51], [279, 46, 256, 52], [279, 47, 256, 53], [279, 48, 256, 54], [280, 10, 257, 6], [281, 10, 258, 6], [281, 14, 258, 10], [281, 15, 258, 11, "update"], [281, 21, 258, 17], [281, 22, 258, 18, "UNRESOLVED_REFS_RETRY_LIMIT"], [281, 49, 258, 45], [281, 50, 258, 46], [282, 8, 259, 4], [283, 6, 259, 5], [284, 8, 259, 5, "key"], [284, 11, 259, 5], [285, 8, 259, 5, "value"], [285, 13, 259, 5], [285, 15, 261, 4], [285, 24, 261, 4, "componentWillUnmount"], [285, 44, 261, 24, "componentWillUnmount"], [285, 45, 261, 24], [285, 47, 261, 27], [286, 10, 262, 6], [286, 14, 262, 10], [286, 15, 262, 11, "inspectorToggleListener"], [286, 38, 262, 34], [286, 40, 262, 36, "remove"], [286, 46, 262, 42], [286, 47, 262, 43], [286, 48, 262, 44], [287, 10, 263, 6], [287, 14, 263, 10], [287, 15, 263, 11, "isMountedRef"], [287, 27, 263, 23], [287, 28, 263, 24, "current"], [287, 35, 263, 31], [287, 38, 263, 34], [287, 43, 263, 39], [288, 10, 264, 6], [288, 14, 264, 10, "Platform"], [288, 35, 264, 18], [288, 36, 264, 19, "OS"], [288, 38, 264, 21], [288, 43, 264, 26], [288, 48, 264, 31], [288, 50, 264, 33], [289, 12, 265, 8], [289, 16, 265, 8, "unregisterOldGestureHandler"], [289, 61, 265, 35], [289, 63, 265, 36], [289, 67, 265, 40], [289, 68, 265, 41, "handlerTag"], [289, 78, 265, 51], [289, 79, 265, 52], [290, 10, 266, 6], [291, 10, 267, 6, "RNGestureHandlerModule"], [291, 41, 267, 28], [291, 42, 267, 29, "dropGestureHandler"], [291, 60, 267, 47], [291, 61, 267, 48], [291, 65, 267, 52], [291, 66, 267, 53, "handlerTag"], [291, 76, 267, 63], [291, 77, 267, 64], [292, 10, 268, 6], [292, 14, 268, 6, "scheduleFlushOperations"], [292, 44, 268, 29], [292, 46, 268, 30], [292, 47, 268, 31], [293, 10, 269, 6], [294, 10, 270, 6], [294, 14, 270, 12, "handlerID"], [294, 23, 270, 41], [294, 26, 270, 44], [294, 30, 270, 48], [294, 31, 270, 49, "props"], [294, 36, 270, 54], [294, 37, 270, 55, "id"], [294, 39, 270, 57], [295, 10, 271, 6], [295, 14, 271, 10, "handlerID"], [295, 23, 271, 19], [295, 25, 271, 21], [296, 12, 272, 8], [297, 12, 273, 8], [297, 19, 273, 15, "handlerIDToTag"], [297, 51, 273, 29], [297, 52, 273, 30, "handlerID"], [297, 61, 273, 39], [297, 62, 273, 40], [298, 10, 274, 6], [299, 10, 276, 6, "MountRegistry"], [299, 38, 276, 19], [299, 39, 276, 20, "gestureHandlerWillUnmount"], [299, 64, 276, 45], [299, 65, 276, 46], [299, 69, 276, 50], [299, 70, 276, 51], [300, 8, 277, 4], [301, 6, 277, 5], [302, 8, 277, 5, "key"], [302, 11, 277, 5], [303, 8, 277, 5, "value"], [303, 13, 277, 5], [303, 15, 408, 4], [303, 24, 408, 12, "update"], [303, 30, 408, 18, "update"], [303, 31, 408, 19, "remainingTries"], [303, 45, 408, 41], [303, 47, 408, 43], [304, 10, 409, 6], [304, 14, 409, 10], [304, 15, 409, 11], [304, 19, 409, 15], [304, 20, 409, 16, "isMountedRef"], [304, 32, 409, 28], [304, 33, 409, 29, "current"], [304, 40, 409, 36], [304, 42, 409, 38], [305, 12, 410, 8], [306, 10, 411, 6], [307, 10, 413, 6], [307, 14, 413, 12, "props"], [307, 19, 413, 34], [307, 22, 413, 37], [307, 26, 413, 41], [307, 27, 413, 42, "props"], [307, 32, 413, 47], [309, 10, 415, 6], [310, 10, 416, 6], [311, 10, 417, 6], [312, 10, 418, 6], [312, 14, 418, 10, "hasUnresolvedRefs"], [312, 31, 418, 27], [312, 32, 418, 28, "props"], [312, 37, 418, 33], [312, 38, 418, 34], [312, 42, 418, 38, "remainingTries"], [312, 56, 418, 52], [312, 59, 418, 55], [312, 60, 418, 56], [312, 62, 418, 58], [313, 12, 419, 8], [313, 16, 419, 8, "ghQueueMicrotask"], [313, 50, 419, 24], [313, 52, 419, 25], [313, 58, 419, 31], [314, 14, 420, 10], [314, 18, 420, 14], [314, 19, 420, 15, "update"], [314, 25, 420, 21], [314, 26, 420, 22, "remainingTries"], [314, 40, 420, 36], [314, 43, 420, 39], [314, 44, 420, 40], [314, 45, 420, 41], [315, 12, 421, 8], [315, 13, 421, 9], [315, 14, 421, 10], [316, 10, 422, 6], [316, 11, 422, 7], [316, 17, 422, 13], [317, 12, 423, 8], [317, 16, 423, 14, "newConfig"], [317, 25, 423, 23], [317, 28, 423, 26], [317, 32, 423, 26, "filterConfig"], [317, 51, 423, 38], [317, 53, 424, 10, "transformProps"], [317, 67, 424, 24], [317, 70, 424, 27, "transformProps"], [317, 84, 424, 41], [317, 85, 424, 42], [317, 89, 424, 46], [317, 90, 424, 47, "props"], [317, 95, 424, 52], [317, 96, 424, 53], [317, 99, 424, 56], [317, 103, 424, 60], [317, 104, 424, 61, "props"], [317, 109, 424, 66], [317, 111, 425, 10], [317, 112, 425, 11], [317, 115, 425, 14, "allowedProps"], [317, 127, 425, 26], [317, 129, 425, 28], [317, 132, 425, 31, "customNativeProps"], [317, 149, 425, 48], [317, 150, 425, 49], [317, 152, 426, 10, "config"], [317, 158, 427, 8], [317, 159, 427, 9], [318, 12, 428, 8], [318, 16, 428, 12], [318, 17, 428, 13], [318, 21, 428, 13, "deepEqual"], [318, 38, 428, 22], [318, 40, 428, 23], [318, 44, 428, 27], [318, 45, 428, 28, "config"], [318, 51, 428, 34], [318, 53, 428, 36, "newConfig"], [318, 62, 428, 45], [318, 63, 428, 46], [318, 65, 428, 48], [319, 14, 429, 10], [319, 18, 429, 14], [319, 19, 429, 15, "updateGestureHandler"], [319, 39, 429, 35], [319, 40, 429, 36, "newConfig"], [319, 49, 429, 45], [319, 50, 429, 46], [320, 12, 430, 8], [321, 10, 431, 6], [322, 8, 432, 4], [324, 8, 434, 4], [325, 6, 434, 4], [326, 8, 434, 4, "key"], [326, 11, 434, 4], [327, 8, 434, 4, "value"], [327, 13, 434, 4], [327, 15, 435, 4], [327, 24, 435, 4, "setNativeProps"], [327, 38, 435, 18, "setNativeProps"], [327, 39, 435, 19, "updates"], [327, 46, 435, 31], [327, 48, 435, 33], [328, 10, 436, 6], [328, 14, 436, 12, "mergedProps"], [328, 25, 436, 23], [328, 28, 436, 26], [329, 12, 436, 28], [329, 15, 436, 31], [329, 19, 436, 35], [329, 20, 436, 36, "props"], [329, 25, 436, 41], [330, 12, 436, 43], [330, 15, 436, 46, "updates"], [331, 10, 436, 54], [331, 11, 436, 55], [332, 10, 437, 6], [332, 14, 437, 12, "newConfig"], [332, 23, 437, 21], [332, 26, 437, 24], [332, 30, 437, 24, "filterConfig"], [332, 49, 437, 36], [332, 51, 438, 8, "transformProps"], [332, 65, 438, 22], [332, 68, 438, 25, "transformProps"], [332, 82, 438, 39], [332, 83, 438, 40, "mergedProps"], [332, 94, 438, 51], [332, 95, 438, 52], [332, 98, 438, 55, "mergedProps"], [332, 109, 438, 66], [332, 111, 439, 8], [332, 112, 439, 9], [332, 115, 439, 12, "allowedProps"], [332, 127, 439, 24], [332, 129, 439, 26], [332, 132, 439, 29, "customNativeProps"], [332, 149, 439, 46], [332, 150, 439, 47], [332, 152, 440, 8, "config"], [332, 158, 441, 6], [332, 159, 441, 7], [333, 10, 442, 6], [333, 14, 442, 10], [333, 15, 442, 11, "updateGestureHandler"], [333, 35, 442, 31], [333, 36, 442, 32, "newConfig"], [333, 45, 442, 41], [333, 46, 442, 42], [334, 8, 443, 4], [335, 6, 443, 5], [336, 8, 443, 5, "key"], [336, 11, 443, 5], [337, 8, 443, 5, "value"], [337, 13, 443, 5], [337, 15, 445, 4], [337, 24, 445, 4, "render"], [337, 30, 445, 10, "render"], [337, 31, 445, 10], [337, 33, 445, 13], [338, 10, 446, 6], [338, 14, 446, 10, "__DEV__"], [338, 21, 446, 17], [338, 25, 446, 21], [338, 26, 446, 22], [338, 30, 446, 26], [338, 31, 446, 27, "context"], [338, 38, 446, 34], [338, 42, 446, 38], [338, 43, 446, 39], [338, 47, 446, 39, "isTestEnv"], [338, 64, 446, 48], [338, 66, 446, 49], [338, 67, 446, 50], [338, 71, 446, 54, "Platform"], [338, 92, 446, 62], [338, 93, 446, 63, "OS"], [338, 95, 446, 65], [338, 100, 446, 70], [338, 105, 446, 75], [338, 107, 446, 77], [339, 12, 447, 8], [339, 18, 447, 14], [339, 22, 447, 18, "Error"], [339, 27, 447, 23], [339, 28, 448, 10, "name"], [339, 32, 448, 14], [339, 35, 449, 12], [339, 236, 450, 8], [339, 237, 450, 9], [340, 10, 451, 6], [341, 10, 453, 6], [341, 14, 453, 10, "gestureEventHandler"], [341, 33, 453, 29], [341, 36, 453, 32], [341, 40, 453, 36], [341, 41, 453, 37, "onGestureHandlerEvent"], [341, 62, 453, 58], [342, 10, 454, 6], [344, 10, 459, 6], [344, 14, 459, 6, "_this$props"], [344, 25, 459, 6], [344, 28, 460, 8], [344, 32, 460, 12], [344, 33, 460, 13, "props"], [344, 38, 460, 18], [345, 12, 459, 14, "onGestureEvent"], [345, 26, 459, 28], [345, 29, 459, 28, "_this$props"], [345, 40, 459, 28], [345, 41, 459, 14, "onGestureEvent"], [345, 55, 459, 28], [346, 12, 459, 30, "onGestureHandlerEvent"], [346, 33, 459, 51], [346, 36, 459, 51, "_this$props"], [346, 47, 459, 51], [346, 48, 459, 30, "onGestureHandlerEvent"], [346, 69, 459, 51], [347, 10, 461, 6], [347, 14, 461, 10, "onGestureEvent"], [347, 28, 461, 24], [347, 32, 461, 28], [347, 39, 461, 35, "onGestureEvent"], [347, 53, 461, 49], [347, 58, 461, 54], [347, 68, 461, 64], [347, 70, 461, 66], [348, 12, 462, 8], [349, 12, 463, 8], [350, 12, 464, 8], [351, 12, 465, 8], [351, 16, 465, 12, "onGestureHandlerEvent"], [351, 37, 465, 33], [351, 39, 465, 35], [352, 14, 466, 10], [352, 20, 466, 16], [352, 24, 466, 20, "Error"], [352, 29, 466, 25], [352, 30, 467, 12], [352, 103, 468, 10], [352, 104, 468, 11], [353, 12, 469, 8], [354, 12, 470, 8, "gestureEventHandler"], [354, 31, 470, 27], [354, 34, 470, 30, "onGestureEvent"], [354, 48, 470, 44], [355, 10, 471, 6], [355, 11, 471, 7], [355, 17, 471, 13], [356, 12, 472, 8], [356, 16, 473, 10, "onGestureHandlerEvent"], [356, 37, 473, 31], [356, 41, 474, 10], [356, 48, 474, 17, "onGestureHandlerEvent"], [356, 69, 474, 38], [356, 74, 474, 43], [356, 84, 474, 53], [356, 86, 475, 10], [357, 14, 476, 10], [357, 20, 476, 16], [357, 24, 476, 20, "Error"], [357, 29, 476, 25], [357, 30, 477, 12], [357, 103, 478, 10], [357, 104, 478, 11], [358, 12, 479, 8], [359, 10, 480, 6], [360, 10, 482, 6], [360, 14, 482, 10, "gestureStateEventHandler"], [360, 38, 482, 34], [360, 41, 482, 37], [360, 45, 482, 41], [360, 46, 482, 42, "onGestureHandlerStateChange"], [360, 73, 482, 69], [361, 10, 483, 6], [363, 10, 488, 6], [363, 14, 488, 6, "_this$props2"], [363, 26, 488, 6], [363, 29, 491, 40], [363, 33, 491, 44], [363, 34, 491, 45, "props"], [363, 39, 491, 50], [364, 12, 489, 8, "onHandlerStateChange"], [364, 32, 489, 28], [364, 35, 489, 28, "_this$props2"], [364, 47, 489, 28], [364, 48, 489, 8, "onHandlerStateChange"], [364, 68, 489, 28], [365, 12, 490, 8, "onGestureHandlerStateChange"], [365, 39, 490, 35], [365, 42, 490, 35, "_this$props2"], [365, 54, 490, 35], [365, 55, 490, 8, "onGestureHandlerStateChange"], [365, 82, 490, 35], [366, 10, 492, 6], [366, 14, 492, 10, "onHandlerStateChange"], [366, 34, 492, 30], [366, 38, 492, 34], [366, 45, 492, 41, "onHandlerStateChange"], [366, 65, 492, 61], [366, 70, 492, 66], [366, 80, 492, 76], [366, 82, 492, 78], [367, 12, 493, 8], [368, 12, 494, 8], [369, 12, 495, 8], [370, 12, 496, 8], [370, 16, 496, 12, "onGestureHandlerStateChange"], [370, 43, 496, 39], [370, 45, 496, 41], [371, 14, 497, 10], [371, 20, 497, 16], [371, 24, 497, 20, "Error"], [371, 29, 497, 25], [371, 30, 498, 12], [371, 103, 499, 10], [371, 104, 499, 11], [372, 12, 500, 8], [373, 12, 501, 8, "gestureStateEventHandler"], [373, 36, 501, 32], [373, 39, 501, 35, "onHandlerStateChange"], [373, 59, 501, 55], [374, 10, 502, 6], [374, 11, 502, 7], [374, 17, 502, 13], [375, 12, 503, 8], [375, 16, 504, 10, "onGestureHandlerStateChange"], [375, 43, 504, 37], [375, 47, 505, 10], [375, 54, 505, 17, "onGestureHandlerStateChange"], [375, 81, 505, 44], [375, 86, 505, 49], [375, 96, 505, 59], [375, 98, 506, 10], [376, 14, 507, 10], [376, 20, 507, 16], [376, 24, 507, 20, "Error"], [376, 29, 507, 25], [376, 30, 508, 12], [376, 103, 509, 10], [376, 104, 509, 11], [377, 12, 510, 8], [378, 10, 511, 6], [379, 10, 512, 6], [379, 14, 512, 12, "events"], [379, 20, 512, 18], [379, 23, 512, 21], [380, 12, 513, 8, "onGestureHandlerEvent"], [380, 33, 513, 29], [380, 35, 513, 31], [380, 39, 513, 35], [380, 40, 513, 36, "state"], [380, 45, 513, 41], [380, 46, 513, 42, "allowTouches"], [380, 58, 513, 54], [380, 61, 514, 12, "gestureEventHandler"], [380, 80, 514, 31], [380, 83, 515, 12, "undefined"], [380, 92, 515, 21], [381, 12, 516, 8, "onGestureHandlerStateChange"], [381, 39, 516, 35], [381, 41, 516, 37], [381, 45, 516, 41], [381, 46, 516, 42, "state"], [381, 51, 516, 47], [381, 52, 516, 48, "allowTouches"], [381, 64, 516, 60], [381, 67, 517, 12, "gestureStateEventHandler"], [381, 91, 517, 36], [381, 94, 518, 12, "undefined"], [382, 10, 519, 6], [382, 11, 519, 7], [383, 10, 521, 6], [383, 14, 521, 10], [383, 15, 521, 11, "propsRef"], [383, 23, 521, 19], [383, 24, 521, 20, "current"], [383, 31, 521, 27], [383, 34, 521, 30, "events"], [383, 40, 521, 36], [384, 10, 523, 6], [384, 14, 523, 10, "child"], [384, 19, 523, 20], [384, 22, 523, 23], [384, 26, 523, 27], [385, 10, 524, 6], [385, 14, 524, 10], [386, 12, 525, 8, "child"], [386, 17, 525, 13], [386, 20, 525, 16, "React"], [386, 25, 525, 21], [386, 26, 525, 22, "Children"], [386, 34, 525, 30], [386, 35, 525, 31, "only"], [386, 39, 525, 35], [386, 40, 525, 36], [386, 44, 525, 40], [386, 45, 525, 41, "props"], [386, 50, 525, 46], [386, 51, 525, 47, "children"], [386, 59, 525, 55], [386, 60, 525, 56], [387, 10, 526, 6], [387, 11, 526, 7], [387, 12, 526, 8], [387, 19, 526, 15, "e"], [387, 20, 526, 16], [387, 22, 526, 18], [388, 12, 527, 8], [388, 18, 527, 14], [388, 22, 527, 18, "Error"], [388, 27, 527, 23], [388, 28, 528, 10], [388, 32, 528, 10, "tagMessage"], [388, 50, 528, 20], [388, 52, 529, 12], [388, 55, 529, 15, "name"], [388, 59, 529, 19], [388, 215, 530, 10], [388, 216, 531, 8], [388, 217, 531, 9], [389, 10, 532, 6], [390, 10, 534, 6], [390, 14, 534, 10, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [390, 27, 534, 23], [390, 30, 534, 26, "child"], [390, 35, 534, 31], [390, 36, 534, 32, "props"], [390, 41, 534, 37], [390, 42, 534, 38, "children"], [390, 50, 534, 46], [391, 10, 535, 6], [391, 14, 536, 8, "__DEV__"], [391, 21, 536, 15], [391, 25, 537, 8, "child"], [391, 30, 537, 13], [391, 31, 537, 14, "type"], [391, 35, 537, 18], [391, 40, 538, 9, "child"], [391, 45, 538, 14], [391, 46, 538, 15, "type"], [391, 50, 538, 19], [391, 55, 538, 24], [391, 79, 538, 48], [391, 83, 539, 10, "child"], [391, 88, 539, 15], [391, 89, 539, 16, "type"], [391, 93, 539, 20], [391, 94, 539, 21, "name"], [391, 98, 539, 25], [391, 103, 539, 30], [391, 109, 539, 36], [391, 113, 540, 10, "child"], [391, 118, 540, 15], [391, 119, 540, 16, "type"], [391, 123, 540, 20], [391, 124, 540, 21, "displayName"], [391, 135, 540, 32], [391, 140, 540, 37], [391, 146, 540, 43], [391, 147, 540, 44], [391, 149, 541, 8], [392, 12, 542, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [392, 25, 542, 21], [392, 28, 542, 24, "React"], [392, 33, 542, 29], [392, 34, 542, 30, "Children"], [392, 42, 542, 38], [392, 43, 542, 39, "toArray"], [392, 50, 542, 46], [392, 51, 542, 47, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [392, 64, 542, 60], [392, 65, 542, 61], [393, 12, 543, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [393, 25, 543, 21], [393, 26, 543, 22, "push"], [393, 30, 543, 26], [393, 44, 544, 10], [393, 48, 544, 10, "_jsxDevRuntime"], [393, 62, 544, 10], [393, 63, 544, 10, "jsxDEV"], [393, 69, 544, 10], [393, 71, 544, 11, "_PressabilityDebugView"], [393, 93, 544, 11], [393, 94, 544, 11, "PressabilityDebugView"], [393, 115, 544, 32], [394, 14, 546, 12, "color"], [394, 19, 546, 17], [394, 21, 546, 18], [394, 40, 546, 37], [395, 14, 547, 12, "hitSlop"], [395, 21, 547, 19], [395, 23, 547, 21, "child"], [395, 28, 547, 26], [395, 29, 547, 27, "props"], [395, 34, 547, 32], [395, 35, 547, 33, "hitSlop"], [396, 12, 547, 41], [396, 15, 545, 16], [396, 38, 545, 39], [397, 14, 545, 39, "fileName"], [397, 22, 545, 39], [397, 24, 545, 39, "_jsxFileName"], [397, 36, 545, 39], [398, 14, 545, 39, "lineNumber"], [398, 24, 545, 39], [399, 14, 545, 39, "columnNumber"], [399, 26, 545, 39], [400, 12, 545, 39], [400, 19, 548, 11], [400, 20, 549, 8], [400, 21, 549, 9], [401, 10, 550, 6], [402, 10, 552, 6], [402, 30, 552, 13, "React"], [402, 35, 552, 18], [402, 36, 552, 19, "cloneElement"], [402, 48, 552, 31], [402, 49, 553, 8, "child"], [402, 54, 553, 13], [402, 56, 554, 8], [403, 12, 555, 10, "ref"], [403, 15, 555, 13], [403, 17, 555, 15], [403, 21, 555, 19], [403, 22, 555, 20, "ref<PERSON><PERSON><PERSON>"], [403, 32, 555, 30], [404, 12, 556, 10, "collapsable"], [404, 23, 556, 21], [404, 25, 556, 23], [404, 30, 556, 28], [405, 12, 557, 10], [405, 16, 557, 14], [405, 20, 557, 14, "isTestEnv"], [405, 37, 557, 23], [405, 39, 557, 24], [405, 40, 557, 25], [405, 43, 558, 14], [406, 14, 559, 16, "handlerType"], [406, 25, 559, 27], [406, 27, 559, 29, "name"], [406, 31, 559, 33], [407, 14, 560, 16, "handlerTag"], [407, 24, 560, 26], [407, 26, 560, 28], [407, 30, 560, 32], [407, 31, 560, 33, "handlerTag"], [407, 41, 560, 43], [408, 14, 561, 16, "enabled"], [408, 21, 561, 23], [408, 23, 561, 25], [408, 27, 561, 29], [408, 28, 561, 30, "props"], [408, 33, 561, 35], [408, 34, 561, 36, "enabled"], [409, 12, 562, 14], [409, 13, 562, 15], [409, 16, 563, 14], [409, 17, 563, 15], [409, 18, 563, 16], [409, 19, 563, 17], [410, 12, 564, 10, "testID"], [410, 18, 564, 16], [410, 20, 564, 18], [410, 24, 564, 22], [410, 25, 564, 23, "props"], [410, 30, 564, 28], [410, 31, 564, 29, "testID"], [410, 37, 564, 35], [410, 41, 564, 39, "child"], [410, 46, 564, 44], [410, 47, 564, 45, "props"], [410, 52, 564, 50], [410, 53, 564, 51, "testID"], [410, 59, 564, 57], [411, 12, 565, 10], [411, 15, 565, 13, "events"], [412, 10, 566, 8], [412, 11, 566, 9], [412, 13, 567, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [412, 26, 568, 6], [412, 27, 568, 7], [413, 8, 569, 4], [414, 6, 569, 5], [415, 4, 569, 5], [415, 6, 182, 24, "React"], [415, 11, 182, 29], [415, 12, 182, 30, "Component"], [415, 21, 182, 39], [416, 4, 182, 8, "Handler"], [416, 11, 182, 15], [416, 12, 186, 11, "displayName"], [416, 23, 186, 22], [416, 26, 186, 25, "name"], [416, 30, 186, 29], [417, 4, 182, 8, "Handler"], [417, 11, 182, 15], [417, 12, 187, 11, "contextType"], [417, 23, 187, 22], [417, 26, 187, 25, "GestureHandlerRootViewContext"], [417, 64, 187, 54], [418, 4, 571, 2], [418, 11, 571, 9, "Handler"], [418, 18, 571, 16], [419, 2, 572, 0], [420, 0, 572, 1], [420, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "UIManagerAny.setJSResponder", "UIManagerAny.clearJSResponder", "DeviceEventEmitter.addListener$argument_1", "hasUnresolvedRefs", "extract", "refs.some$argument_0", "createHandler", "Handler", "Handler#constructor", "Handler#componentDidMount", "setState$argument_0", "ghQueueMicrotask$argument_0", "Handler#componentDidUpdate", "Handler#componentWillUnmount", "Handler#onGestureHandlerEvent", "Handler#onGestureHandlerStateChange", "Handler#refHandler", "Handler#createGestureHandler", "Handler#attachGestureHandler", "Handler#updateGestureHandler", "Handler#update", "Handler#setNativeProps", "Handler#render"], "mappings": "AAA;sCC2F;GDE;0CCC;GDE;8BEE;CFG;gCGC;CHG;2DIO;GJE;AKM;kBCI;qBCI,8BD;GDC;CLE;eQqC;ECa;ICe;KDY;IEE;UPO;0BQC,yBR;WOE;yBEU;SFE;KFkB;IKE;KLM;IME;KNgB;oCOE;KPQ;0CQG;KRiB;yBSE;KTiB;mCUE;KVW;mCWE;4BnBmB;SmBuB;uBPW;OOE;KXC;mCYE;KZO;IaE;yBTW;SSE;KbW;IcG;KdQ;IeE;Kf4H;GDC;CRE"}}, "type": "js/module"}]}