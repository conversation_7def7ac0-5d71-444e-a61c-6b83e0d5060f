{"dependencies": [{"name": "css-mediaquery", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 25, "index": 159}, "end": {"line": 4, "column": 50, "index": 184}}], "key": "fJlmbhkoxzqzeY/WnvmQodpWBmc=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 23, "index": 209}, "end": {"line": 5, "column": 46, "index": 232}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.matchChildAtRule = exports.matchAtRule = void 0;\n  var css_mediaquery_1 = require(_dependencyMap[0], \"css-mediaquery\");\n  var react_native_1 = require(_dependencyMap[1], \"react-native\");\n  function matchAtRule(_ref) {\n    var rule = _ref.rule,\n      params = _ref.params,\n      width = _ref.width,\n      height = _ref.height,\n      orientation = _ref.orientation;\n    if (rule === \"media\" && params) {\n      return (0, css_mediaquery_1.match)(params, {\n        type: react_native_1.Platform.OS,\n        \"aspect-ratio\": width / height,\n        \"device-aspect-ratio\": width / height,\n        width,\n        height,\n        \"device-width\": width,\n        \"device-height\": width,\n        orientation\n      });\n    }\n    return false;\n  }\n  exports.matchAtRule = matchAtRule;\n  function matchChildAtRule(rule) {\n    var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n    var _ref2 = arguments.length > 2 ? arguments[2] : undefined,\n      _ref2$nthChild = _ref2.nthChild,\n      nthChild = _ref2$nthChild === void 0 ? -1 : _ref2$nthChild,\n      _ref2$parentHover = _ref2.parentHover,\n      parentHover = _ref2$parentHover === void 0 ? false : _ref2$parentHover,\n      _ref2$parentFocus = _ref2.parentFocus,\n      parentFocus = _ref2$parentFocus === void 0 ? false : _ref2$parentFocus,\n      _ref2$parentActive = _ref2.parentActive,\n      parentActive = _ref2$parentActive === void 0 ? false : _ref2$parentActive;\n    if (rule === \"selector\" && params === \"(> *:not(:first-child))\" && nthChild > 1) {\n      return true;\n    } else if (rule === \"selector\" && params === \"(> *)\") {\n      return true;\n    } else if (rule === \"parent\") {\n      switch (params) {\n        case \"hover\":\n          return parentHover;\n        case \"focus\":\n          return parentFocus;\n        case \"active\":\n          return parentActive;\n        default:\n          return false;\n      }\n    }\n    return false;\n  }\n  exports.matchChildAtRule = matchChildAtRule;\n});", "lineCount": 61, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "matchChildAtRule"], [7, 26, 3, 24], [7, 29, 3, 27, "exports"], [7, 36, 3, 34], [7, 37, 3, 35, "matchAtRule"], [7, 48, 3, 46], [7, 51, 3, 49], [7, 56, 3, 54], [7, 57, 3, 55], [8, 2, 4, 0], [8, 6, 4, 6, "css_mediaquery_1"], [8, 22, 4, 22], [8, 25, 4, 25, "require"], [8, 32, 4, 32], [8, 33, 4, 32, "_dependencyMap"], [8, 47, 4, 32], [8, 68, 4, 49], [8, 69, 4, 50], [9, 2, 5, 0], [9, 6, 5, 6, "react_native_1"], [9, 20, 5, 20], [9, 23, 5, 23, "require"], [9, 30, 5, 30], [9, 31, 5, 30, "_dependencyMap"], [9, 45, 5, 30], [9, 64, 5, 45], [9, 65, 5, 46], [10, 2, 6, 0], [10, 11, 6, 9, "matchAtRule"], [10, 22, 6, 20, "matchAtRule"], [10, 23, 6, 20, "_ref"], [10, 27, 6, 20], [10, 29, 6, 68], [11, 4, 6, 68], [11, 8, 6, 23, "rule"], [11, 12, 6, 27], [11, 15, 6, 27, "_ref"], [11, 19, 6, 27], [11, 20, 6, 23, "rule"], [11, 24, 6, 27], [12, 6, 6, 29, "params"], [12, 12, 6, 35], [12, 15, 6, 35, "_ref"], [12, 19, 6, 35], [12, 20, 6, 29, "params"], [12, 26, 6, 35], [13, 6, 6, 37, "width"], [13, 11, 6, 42], [13, 14, 6, 42, "_ref"], [13, 18, 6, 42], [13, 19, 6, 37, "width"], [13, 24, 6, 42], [14, 6, 6, 44, "height"], [14, 12, 6, 50], [14, 15, 6, 50, "_ref"], [14, 19, 6, 50], [14, 20, 6, 44, "height"], [14, 26, 6, 50], [15, 6, 6, 52, "orientation"], [15, 17, 6, 63], [15, 20, 6, 63, "_ref"], [15, 24, 6, 63], [15, 25, 6, 52, "orientation"], [15, 36, 6, 63], [16, 4, 7, 4], [16, 8, 7, 8, "rule"], [16, 12, 7, 12], [16, 17, 7, 17], [16, 24, 7, 24], [16, 28, 7, 28, "params"], [16, 34, 7, 34], [16, 36, 7, 36], [17, 6, 8, 8], [17, 13, 8, 15], [17, 14, 8, 16], [17, 15, 8, 17], [17, 17, 8, 19, "css_mediaquery_1"], [17, 33, 8, 35], [17, 34, 8, 36, "match"], [17, 39, 8, 41], [17, 41, 8, 43, "params"], [17, 47, 8, 49], [17, 49, 8, 51], [18, 8, 9, 12, "type"], [18, 12, 9, 16], [18, 14, 9, 18, "react_native_1"], [18, 28, 9, 32], [18, 29, 9, 33, "Platform"], [18, 37, 9, 41], [18, 38, 9, 42, "OS"], [18, 40, 9, 44], [19, 8, 10, 12], [19, 22, 10, 26], [19, 24, 10, 28, "width"], [19, 29, 10, 33], [19, 32, 10, 36, "height"], [19, 38, 10, 42], [20, 8, 11, 12], [20, 29, 11, 33], [20, 31, 11, 35, "width"], [20, 36, 11, 40], [20, 39, 11, 43, "height"], [20, 45, 11, 49], [21, 8, 12, 12, "width"], [21, 13, 12, 17], [22, 8, 13, 12, "height"], [22, 14, 13, 18], [23, 8, 14, 12], [23, 22, 14, 26], [23, 24, 14, 28, "width"], [23, 29, 14, 33], [24, 8, 15, 12], [24, 23, 15, 27], [24, 25, 15, 29, "width"], [24, 30, 15, 34], [25, 8, 16, 12, "orientation"], [26, 6, 17, 8], [26, 7, 17, 9], [26, 8, 17, 10], [27, 4, 18, 4], [28, 4, 19, 4], [28, 11, 19, 11], [28, 16, 19, 16], [29, 2, 20, 0], [30, 2, 21, 0, "exports"], [30, 9, 21, 7], [30, 10, 21, 8, "matchAtRule"], [30, 21, 21, 19], [30, 24, 21, 22, "matchAtRule"], [30, 35, 21, 33], [31, 2, 22, 0], [31, 11, 22, 9, "matchChildAtRule"], [31, 27, 22, 25, "matchChildAtRule"], [31, 28, 22, 26, "rule"], [31, 32, 22, 30], [31, 34, 22, 129], [32, 4, 22, 129], [32, 8, 22, 32, "params"], [32, 14, 22, 38], [32, 17, 22, 38, "arguments"], [32, 26, 22, 38], [32, 27, 22, 38, "length"], [32, 33, 22, 38], [32, 41, 22, 38, "arguments"], [32, 50, 22, 38], [32, 58, 22, 38, "undefined"], [32, 67, 22, 38], [32, 70, 22, 38, "arguments"], [32, 79, 22, 38], [32, 85, 22, 41], [32, 87, 22, 43], [33, 4, 22, 43], [33, 8, 22, 43, "_ref2"], [33, 13, 22, 43], [33, 16, 22, 43, "arguments"], [33, 25, 22, 43], [33, 26, 22, 43, "length"], [33, 32, 22, 43], [33, 39, 22, 43, "arguments"], [33, 48, 22, 43], [33, 54, 22, 43, "undefined"], [33, 63, 22, 43], [34, 6, 22, 43, "_ref2$nthChild"], [34, 20, 22, 43], [34, 23, 22, 43, "_ref2"], [34, 28, 22, 43], [34, 29, 22, 47, "nthChild"], [34, 37, 22, 55], [35, 6, 22, 47, "nthChild"], [35, 14, 22, 55], [35, 17, 22, 55, "_ref2$nthChild"], [35, 31, 22, 55], [35, 45, 22, 58], [35, 46, 22, 59], [35, 47, 22, 60], [35, 50, 22, 60, "_ref2$nthChild"], [35, 64, 22, 60], [36, 6, 22, 60, "_ref2$parentHover"], [36, 23, 22, 60], [36, 26, 22, 60, "_ref2"], [36, 31, 22, 60], [36, 32, 22, 62, "parentHover"], [36, 43, 22, 73], [37, 6, 22, 62, "parentHover"], [37, 17, 22, 73], [37, 20, 22, 73, "_ref2$parentHover"], [37, 37, 22, 73], [37, 51, 22, 76], [37, 56, 22, 81], [37, 59, 22, 81, "_ref2$parentHover"], [37, 76, 22, 81], [38, 6, 22, 81, "_ref2$parentFocus"], [38, 23, 22, 81], [38, 26, 22, 81, "_ref2"], [38, 31, 22, 81], [38, 32, 22, 83, "parentFocus"], [38, 43, 22, 94], [39, 6, 22, 83, "parentFocus"], [39, 17, 22, 94], [39, 20, 22, 94, "_ref2$parentFocus"], [39, 37, 22, 94], [39, 51, 22, 97], [39, 56, 22, 102], [39, 59, 22, 102, "_ref2$parentFocus"], [39, 76, 22, 102], [40, 6, 22, 102, "_ref2$parentActive"], [40, 24, 22, 102], [40, 27, 22, 102, "_ref2"], [40, 32, 22, 102], [40, 33, 22, 104, "parentActive"], [40, 45, 22, 116], [41, 6, 22, 104, "parentActive"], [41, 18, 22, 116], [41, 21, 22, 116, "_ref2$parentActive"], [41, 39, 22, 116], [41, 53, 22, 119], [41, 58, 22, 124], [41, 61, 22, 124, "_ref2$parentActive"], [41, 79, 22, 124], [42, 4, 23, 4], [42, 8, 23, 8, "rule"], [42, 12, 23, 12], [42, 17, 23, 17], [42, 27, 23, 27], [42, 31, 24, 8, "params"], [42, 37, 24, 14], [42, 42, 24, 19], [42, 67, 24, 44], [42, 71, 25, 8, "nthChild"], [42, 79, 25, 16], [42, 82, 25, 19], [42, 83, 25, 20], [42, 85, 25, 22], [43, 6, 26, 8], [43, 13, 26, 15], [43, 17, 26, 19], [44, 4, 27, 4], [44, 5, 27, 5], [44, 11, 28, 9], [44, 15, 28, 13, "rule"], [44, 19, 28, 17], [44, 24, 28, 22], [44, 34, 28, 32], [44, 38, 28, 36, "params"], [44, 44, 28, 42], [44, 49, 28, 47], [44, 56, 28, 54], [44, 58, 28, 56], [45, 6, 29, 8], [45, 13, 29, 15], [45, 17, 29, 19], [46, 4, 30, 4], [46, 5, 30, 5], [46, 11, 31, 9], [46, 15, 31, 13, "rule"], [46, 19, 31, 17], [46, 24, 31, 22], [46, 32, 31, 30], [46, 34, 31, 32], [47, 6, 32, 8], [47, 14, 32, 16, "params"], [47, 20, 32, 22], [48, 8, 33, 12], [48, 13, 33, 17], [48, 20, 33, 24], [49, 10, 34, 16], [49, 17, 34, 23, "parentHover"], [49, 28, 34, 34], [50, 8, 35, 12], [50, 13, 35, 17], [50, 20, 35, 24], [51, 10, 36, 16], [51, 17, 36, 23, "parentFocus"], [51, 28, 36, 34], [52, 8, 37, 12], [52, 13, 37, 17], [52, 21, 37, 25], [53, 10, 38, 16], [53, 17, 38, 23, "parentActive"], [53, 29, 38, 35], [54, 8, 39, 12], [55, 10, 40, 16], [55, 17, 40, 23], [55, 22, 40, 28], [56, 6, 41, 8], [57, 4, 42, 4], [58, 4, 43, 4], [58, 11, 43, 11], [58, 16, 43, 16], [59, 2, 44, 0], [60, 2, 45, 0, "exports"], [60, 9, 45, 7], [60, 10, 45, 8, "matchChildAtRule"], [60, 26, 45, 24], [60, 29, 45, 27, "matchChildAtRule"], [60, 45, 45, 43], [61, 0, 45, 44], [61, 3]], "functionMap": {"names": ["<global>", "matchAtRule", "matchChildAtRule"], "mappings": "AAA;ACK;CDc;AEE;CFsB"}}, "type": "js/module"}]}