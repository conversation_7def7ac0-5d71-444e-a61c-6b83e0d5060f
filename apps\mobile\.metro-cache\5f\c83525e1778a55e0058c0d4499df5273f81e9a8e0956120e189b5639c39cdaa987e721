{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 49, "index": 49}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 50}, "end": {"line": 2, "column": 38, "index": 88}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}, {"name": "../PlatformConstants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 89}, "end": {"line": 3, "column": 53, "index": 142}}], "key": "Q46Gl6Q6xVYszCJBCyudBafKJCc=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 143}, "end": {"line": 4, "column": 44, "index": 187}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 188}, "end": {"line": 8, "column": 32, "index": 283}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.forceTouchHandlerName = exports.forceTouchGestureHandlerProps = exports.ForceTouchGestureHandler = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[6], \"react\"));\n  var _utils = require(_dependencyMap[7], \"../utils\");\n  var _PlatformConstants = _interopRequireDefault(require(_dependencyMap[8], \"../PlatformConstants\"));\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[9], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[10], \"./gestureHandlerCommon\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var forceTouchGestureHandlerProps = exports.forceTouchGestureHandlerProps = ['minForce', 'maxForce', 'feedbackOnActivation'];\n\n  // implicit `children` prop has been removed in @types/react^18.0.0\n  var ForceTouchFallback = /*#__PURE__*/function (_React$Component) {\n    function ForceTouchFallback() {\n      (0, _classCallCheck2.default)(this, ForceTouchFallback);\n      return _callSuper(this, ForceTouchFallback, arguments);\n    }\n    (0, _inherits2.default)(ForceTouchFallback, _React$Component);\n    return (0, _createClass2.default)(ForceTouchFallback, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        console.warn((0, _utils.tagMessage)('ForceTouchGestureHandler is not available on this platform. Please use ForceTouchGestureHandler.forceTouchAvailable to conditionally render other components that would provide a fallback behavior specific to your usecase'));\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        return this.props.children;\n      }\n    }]);\n  }(_react.default.Component);\n  ForceTouchFallback.forceTouchAvailable = false;\n  /**\n   * @deprecated ForceTouchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.ForceTouch()` instead.\n   */\n  /**\n   * @deprecated ForceTouchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.ForceTouch()` instead.\n   */\n  var forceTouchHandlerName = exports.forceTouchHandlerName = 'ForceTouchGestureHandler';\n\n  /**\n   * @deprecated ForceTouchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.ForceTouch()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n  var ForceTouchGestureHandler = exports.ForceTouchGestureHandler = _PlatformConstants.default?.forceTouchAvailable ? (0, _createHandler.default)({\n    name: forceTouchHandlerName,\n    allowedProps: [..._gestureHandlerCommon.baseGestureHandlerProps, ...forceTouchGestureHandlerProps],\n    config: {}\n  }) : ForceTouchFallback;\n  ForceTouchGestureHandler.forceTouchAvailable = _PlatformConstants.default?.forceTouchAvailable || false;\n});", "lineCount": 59, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_react"], [12, 12, 1, 0], [12, 15, 1, 0, "_interopRequireDefault"], [12, 37, 1, 0], [12, 38, 1, 0, "require"], [12, 45, 1, 0], [12, 46, 1, 0, "_dependencyMap"], [12, 60, 1, 0], [13, 2, 2, 0], [13, 6, 2, 0, "_utils"], [13, 12, 2, 0], [13, 15, 2, 0, "require"], [13, 22, 2, 0], [13, 23, 2, 0, "_dependencyMap"], [13, 37, 2, 0], [14, 2, 3, 0], [14, 6, 3, 0, "_PlatformConstants"], [14, 24, 3, 0], [14, 27, 3, 0, "_interopRequireDefault"], [14, 49, 3, 0], [14, 50, 3, 0, "require"], [14, 57, 3, 0], [14, 58, 3, 0, "_dependencyMap"], [14, 72, 3, 0], [15, 2, 4, 0], [15, 6, 4, 0, "_createHandler"], [15, 20, 4, 0], [15, 23, 4, 0, "_interopRequireDefault"], [15, 45, 4, 0], [15, 46, 4, 0, "require"], [15, 53, 4, 0], [15, 54, 4, 0, "_dependencyMap"], [15, 68, 4, 0], [16, 2, 5, 0], [16, 6, 5, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 27, 5, 0], [16, 30, 5, 0, "require"], [16, 37, 5, 0], [16, 38, 5, 0, "_dependencyMap"], [16, 52, 5, 0], [17, 2, 8, 32], [17, 11, 8, 32, "_callSuper"], [17, 22, 8, 32, "t"], [17, 23, 8, 32], [17, 25, 8, 32, "o"], [17, 26, 8, 32], [17, 28, 8, 32, "e"], [17, 29, 8, 32], [17, 40, 8, 32, "o"], [17, 41, 8, 32], [17, 48, 8, 32, "_getPrototypeOf2"], [17, 64, 8, 32], [17, 65, 8, 32, "default"], [17, 72, 8, 32], [17, 74, 8, 32, "o"], [17, 75, 8, 32], [17, 82, 8, 32, "_possibleConstructorReturn2"], [17, 109, 8, 32], [17, 110, 8, 32, "default"], [17, 117, 8, 32], [17, 119, 8, 32, "t"], [17, 120, 8, 32], [17, 122, 8, 32, "_isNativeReflectConstruct"], [17, 147, 8, 32], [17, 152, 8, 32, "Reflect"], [17, 159, 8, 32], [17, 160, 8, 32, "construct"], [17, 169, 8, 32], [17, 170, 8, 32, "o"], [17, 171, 8, 32], [17, 173, 8, 32, "e"], [17, 174, 8, 32], [17, 186, 8, 32, "_getPrototypeOf2"], [17, 202, 8, 32], [17, 203, 8, 32, "default"], [17, 210, 8, 32], [17, 212, 8, 32, "t"], [17, 213, 8, 32], [17, 215, 8, 32, "constructor"], [17, 226, 8, 32], [17, 230, 8, 32, "o"], [17, 231, 8, 32], [17, 232, 8, 32, "apply"], [17, 237, 8, 32], [17, 238, 8, 32, "t"], [17, 239, 8, 32], [17, 241, 8, 32, "e"], [17, 242, 8, 32], [18, 2, 8, 32], [18, 11, 8, 32, "_isNativeReflectConstruct"], [18, 37, 8, 32], [18, 51, 8, 32, "t"], [18, 52, 8, 32], [18, 56, 8, 32, "Boolean"], [18, 63, 8, 32], [18, 64, 8, 32, "prototype"], [18, 73, 8, 32], [18, 74, 8, 32, "valueOf"], [18, 81, 8, 32], [18, 82, 8, 32, "call"], [18, 86, 8, 32], [18, 87, 8, 32, "Reflect"], [18, 94, 8, 32], [18, 95, 8, 32, "construct"], [18, 104, 8, 32], [18, 105, 8, 32, "Boolean"], [18, 112, 8, 32], [18, 145, 8, 32, "t"], [18, 146, 8, 32], [18, 159, 8, 32, "_isNativeReflectConstruct"], [18, 184, 8, 32], [18, 196, 8, 32, "_isNativeReflectConstruct"], [18, 197, 8, 32], [18, 210, 8, 32, "t"], [18, 211, 8, 32], [19, 2, 11, 7], [19, 6, 11, 13, "forceTouchGestureHandlerProps"], [19, 35, 11, 42], [19, 38, 11, 42, "exports"], [19, 45, 11, 42], [19, 46, 11, 42, "forceTouchGestureHandlerProps"], [19, 75, 11, 42], [19, 78, 11, 45], [19, 79, 12, 2], [19, 89, 12, 12], [19, 91, 13, 2], [19, 101, 13, 12], [19, 103, 14, 2], [19, 125, 14, 24], [19, 126, 15, 10], [21, 2, 17, 0], [22, 2, 17, 0], [22, 6, 18, 6, "ForceTouchFallback"], [22, 24, 18, 24], [22, 50, 18, 24, "_React$Component"], [22, 66, 18, 24], [23, 4, 18, 24], [23, 13, 18, 24, "ForceTouchFallback"], [23, 32, 18, 24], [24, 6, 18, 24], [24, 10, 18, 24, "_classCallCheck2"], [24, 26, 18, 24], [24, 27, 18, 24, "default"], [24, 34, 18, 24], [24, 42, 18, 24, "ForceTouchFallback"], [24, 60, 18, 24], [25, 6, 18, 24], [25, 13, 18, 24, "_callSuper"], [25, 23, 18, 24], [25, 30, 18, 24, "ForceTouchFallback"], [25, 48, 18, 24], [25, 50, 18, 24, "arguments"], [25, 59, 18, 24], [26, 4, 18, 24], [27, 4, 18, 24], [27, 8, 18, 24, "_inherits2"], [27, 18, 18, 24], [27, 19, 18, 24, "default"], [27, 26, 18, 24], [27, 28, 18, 24, "ForceTouchFallback"], [27, 46, 18, 24], [27, 48, 18, 24, "_React$Component"], [27, 64, 18, 24], [28, 4, 18, 24], [28, 15, 18, 24, "_createClass2"], [28, 28, 18, 24], [28, 29, 18, 24, "default"], [28, 36, 18, 24], [28, 38, 18, 24, "ForceTouchFallback"], [28, 56, 18, 24], [29, 6, 18, 24, "key"], [29, 9, 18, 24], [30, 6, 18, 24, "value"], [30, 11, 18, 24], [30, 13, 20, 2], [30, 22, 20, 2, "componentDidMount"], [30, 39, 20, 19, "componentDidMount"], [30, 40, 20, 19], [30, 42, 20, 22], [31, 8, 21, 4, "console"], [31, 15, 21, 11], [31, 16, 21, 12, "warn"], [31, 20, 21, 16], [31, 21, 22, 6], [31, 25, 22, 6, "tagMessage"], [31, 42, 22, 16], [31, 44, 23, 8], [31, 266, 24, 6], [31, 267, 25, 4], [31, 268, 25, 5], [32, 6, 26, 2], [33, 4, 26, 3], [34, 6, 26, 3, "key"], [34, 9, 26, 3], [35, 6, 26, 3, "value"], [35, 11, 26, 3], [35, 13, 27, 2], [35, 22, 27, 2, "render"], [35, 28, 27, 8, "render"], [35, 29, 27, 8], [35, 31, 27, 11], [36, 8, 28, 4], [36, 15, 28, 11], [36, 19, 28, 15], [36, 20, 28, 16, "props"], [36, 25, 28, 21], [36, 26, 28, 22, "children"], [36, 34, 28, 30], [37, 6, 29, 2], [38, 4, 29, 3], [39, 2, 29, 3], [39, 4, 18, 33, "React"], [39, 18, 18, 38], [39, 19, 18, 39, "Component"], [39, 28, 18, 48], [40, 2, 18, 6, "ForceTouchFallback"], [40, 20, 18, 24], [40, 21, 19, 9, "forceTouchAvailable"], [40, 40, 19, 28], [40, 43, 19, 31], [40, 48, 19, 36], [41, 2, 53, 0], [42, 0, 54, 0], [43, 0, 55, 0], [44, 2, 60, 0], [45, 0, 61, 0], [46, 0, 62, 0], [47, 2, 67, 7], [47, 6, 67, 13, "forceTouchHandlerName"], [47, 27, 67, 34], [47, 30, 67, 34, "exports"], [47, 37, 67, 34], [47, 38, 67, 34, "forceTouchHandlerName"], [47, 59, 67, 34], [47, 62, 67, 37], [47, 88, 67, 63], [49, 2, 69, 0], [50, 0, 70, 0], [51, 0, 71, 0], [52, 2, 72, 0], [53, 2, 73, 7], [53, 6, 73, 13, "ForceTouchGestureHandler"], [53, 30, 73, 37], [53, 33, 73, 37, "exports"], [53, 40, 73, 37], [53, 41, 73, 37, "ForceTouchGestureHandler"], [53, 65, 73, 37], [53, 68, 73, 40, "PlatformConstants"], [53, 94, 73, 57], [53, 96, 73, 59, "forceTouchAvailable"], [53, 115, 73, 78], [53, 118, 74, 4], [53, 122, 74, 4, "createHandler"], [53, 144, 74, 17], [53, 146, 77, 6], [54, 4, 78, 6, "name"], [54, 8, 78, 10], [54, 10, 78, 12, "forceTouchHandlerName"], [54, 31, 78, 33], [55, 4, 79, 6, "allowedProps"], [55, 16, 79, 18], [55, 18, 79, 20], [55, 19, 80, 8], [55, 22, 80, 11, "baseGestureHandlerProps"], [55, 67, 80, 34], [55, 69, 81, 8], [55, 72, 81, 11, "forceTouchGestureHandlerProps"], [55, 101, 81, 40], [55, 102, 82, 16], [56, 4, 83, 6, "config"], [56, 10, 83, 12], [56, 12, 83, 14], [56, 13, 83, 15], [57, 2, 84, 4], [57, 3, 84, 5], [57, 4, 84, 6], [57, 7, 85, 4, "ForceTouchFallback"], [57, 25, 85, 22], [58, 2, 87, 1, "ForceTouchGestureHandler"], [58, 26, 87, 25], [58, 27, 87, 55, "forceTouchAvailable"], [58, 46, 87, 74], [58, 49, 88, 2, "PlatformConstants"], [58, 75, 88, 19], [58, 77, 88, 21, "forceTouchAvailable"], [58, 96, 88, 40], [58, 100, 88, 44], [58, 105, 88, 49], [59, 0, 88, 50], [59, 3]], "functionMap": {"names": ["<global>", "ForceTouchFallback", "ForceTouchFallback#componentDidMount", "ForceTouchFallback#render"], "mappings": "AAA;ACiB;ECE;GDM;EEC;GFE;CDC"}}, "type": "js/module"}]}