{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Core/Devtools/symbolicateStackTrace", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 78}}], "key": "hV8WFuV5m0WnRrAl7up+IGwo1ng=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.deleteStack = deleteStack;\n  exports.symbolicate = symbolicate;\n  var _symbolicateStackTrace = _interopRequireDefault(require(_dependencyMap[1], \"../../Core/Devtools/symbolicateStackTrace\"));\n  var cache = new Map();\n  var sanitize = _ref => {\n    var maybeStack = _ref.stack,\n      codeFrame = _ref.codeFrame;\n    if (!Array.isArray(maybeStack)) {\n      throw new Error('Expected stack to be an array.');\n    }\n    var stack = [];\n    for (var maybeFrame of maybeStack) {\n      var collapse = false;\n      if ('collapse' in maybeFrame) {\n        if (typeof maybeFrame.collapse !== 'boolean') {\n          throw new Error('Expected stack frame `collapse` to be a boolean.');\n        }\n        collapse = maybeFrame.collapse;\n      }\n      stack.push({\n        column: maybeFrame.column,\n        file: maybeFrame.file,\n        lineNumber: maybeFrame.lineNumber,\n        methodName: maybeFrame.methodName,\n        collapse\n      });\n    }\n    return {\n      stack,\n      codeFrame\n    };\n  };\n  function deleteStack(stack) {\n    cache.delete(stack);\n  }\n  function symbolicate(stack, extraData) {\n    var promise = cache.get(stack);\n    if (promise == null) {\n      promise = (0, _symbolicateStackTrace.default)(stack, extraData).then(sanitize);\n      cache.set(stack, promise);\n    }\n    return promise;\n  }\n});", "lineCount": 49, "map": [[8, 2, 14, 0], [8, 6, 14, 0, "_symbolicateStackTrace"], [8, 28, 14, 0], [8, 31, 14, 0, "_interopRequireDefault"], [8, 53, 14, 0], [8, 54, 14, 0, "require"], [8, 61, 14, 0], [8, 62, 14, 0, "_dependencyMap"], [8, 76, 14, 0], [9, 2, 18, 0], [9, 6, 18, 6, "cache"], [9, 11, 18, 56], [9, 14, 18, 59], [9, 18, 18, 63, "Map"], [9, 21, 18, 66], [9, 22, 18, 67], [9, 23, 18, 68], [10, 2, 23, 0], [10, 6, 23, 6, "sanitize"], [10, 14, 23, 14], [10, 17, 23, 17, "_ref"], [10, 21, 23, 17], [10, 25, 26, 54], [11, 4, 26, 54], [11, 8, 24, 9, "maybeStack"], [11, 18, 24, 19], [11, 21, 24, 19, "_ref"], [11, 25, 24, 19], [11, 26, 24, 2, "stack"], [11, 31, 24, 7], [12, 6, 25, 2, "codeFrame"], [12, 15, 25, 11], [12, 18, 25, 11, "_ref"], [12, 22, 25, 11], [12, 23, 25, 2, "codeFrame"], [12, 32, 25, 11], [13, 4, 27, 2], [13, 8, 27, 6], [13, 9, 27, 7, "Array"], [13, 14, 27, 12], [13, 15, 27, 13, "isArray"], [13, 22, 27, 20], [13, 23, 27, 21, "maybeStack"], [13, 33, 27, 31], [13, 34, 27, 32], [13, 36, 27, 34], [14, 6, 28, 4], [14, 12, 28, 10], [14, 16, 28, 14, "Error"], [14, 21, 28, 19], [14, 22, 28, 20], [14, 54, 28, 52], [14, 55, 28, 53], [15, 4, 29, 2], [16, 4, 30, 2], [16, 8, 30, 8, "stack"], [16, 13, 30, 32], [16, 16, 30, 35], [16, 18, 30, 37], [17, 4, 31, 2], [17, 9, 31, 7], [17, 13, 31, 13, "<PERSON><PERSON><PERSON><PERSON>"], [17, 23, 31, 23], [17, 27, 31, 27, "maybeStack"], [17, 37, 31, 37], [17, 39, 31, 39], [18, 6, 32, 4], [18, 10, 32, 8, "collapse"], [18, 18, 32, 16], [18, 21, 32, 19], [18, 26, 32, 24], [19, 6, 33, 4], [19, 10, 33, 8], [19, 20, 33, 18], [19, 24, 33, 22, "<PERSON><PERSON><PERSON><PERSON>"], [19, 34, 33, 32], [19, 36, 33, 34], [20, 8, 34, 6], [20, 12, 34, 10], [20, 19, 34, 17, "<PERSON><PERSON><PERSON><PERSON>"], [20, 29, 34, 27], [20, 30, 34, 28, "collapse"], [20, 38, 34, 36], [20, 43, 34, 41], [20, 52, 34, 50], [20, 54, 34, 52], [21, 10, 35, 8], [21, 16, 35, 14], [21, 20, 35, 18, "Error"], [21, 25, 35, 23], [21, 26, 35, 24], [21, 76, 35, 74], [21, 77, 35, 75], [22, 8, 36, 6], [23, 8, 37, 6, "collapse"], [23, 16, 37, 14], [23, 19, 37, 17, "<PERSON><PERSON><PERSON><PERSON>"], [23, 29, 37, 27], [23, 30, 37, 28, "collapse"], [23, 38, 37, 36], [24, 6, 38, 4], [25, 6, 39, 4, "stack"], [25, 11, 39, 9], [25, 12, 39, 10, "push"], [25, 16, 39, 14], [25, 17, 39, 15], [26, 8, 40, 6, "column"], [26, 14, 40, 12], [26, 16, 40, 14, "<PERSON><PERSON><PERSON><PERSON>"], [26, 26, 40, 24], [26, 27, 40, 25, "column"], [26, 33, 40, 31], [27, 8, 41, 6, "file"], [27, 12, 41, 10], [27, 14, 41, 12, "<PERSON><PERSON><PERSON><PERSON>"], [27, 24, 41, 22], [27, 25, 41, 23, "file"], [27, 29, 41, 27], [28, 8, 42, 6, "lineNumber"], [28, 18, 42, 16], [28, 20, 42, 18, "<PERSON><PERSON><PERSON><PERSON>"], [28, 30, 42, 28], [28, 31, 42, 29, "lineNumber"], [28, 41, 42, 39], [29, 8, 43, 6, "methodName"], [29, 18, 43, 16], [29, 20, 43, 18, "<PERSON><PERSON><PERSON><PERSON>"], [29, 30, 43, 28], [29, 31, 43, 29, "methodName"], [29, 41, 43, 39], [30, 8, 44, 6, "collapse"], [31, 6, 45, 4], [31, 7, 45, 5], [31, 8, 45, 6], [32, 4, 46, 2], [33, 4, 47, 2], [33, 11, 47, 9], [34, 6, 47, 10, "stack"], [34, 11, 47, 15], [35, 6, 47, 17, "codeFrame"], [36, 4, 47, 26], [36, 5, 47, 27], [37, 2, 48, 0], [37, 3, 48, 1], [38, 2, 50, 7], [38, 11, 50, 16, "deleteStack"], [38, 22, 50, 27, "deleteStack"], [38, 23, 50, 28, "stack"], [38, 28, 50, 40], [38, 30, 50, 48], [39, 4, 51, 2, "cache"], [39, 9, 51, 7], [39, 10, 51, 8, "delete"], [39, 16, 51, 14], [39, 17, 51, 15, "stack"], [39, 22, 51, 20], [39, 23, 51, 21], [40, 2, 52, 0], [41, 2, 54, 7], [41, 11, 54, 16, "symbolicate"], [41, 22, 54, 27, "symbolicate"], [41, 23, 55, 2, "stack"], [41, 28, 55, 14], [41, 30, 56, 2, "extraData"], [41, 39, 56, 19], [41, 41, 57, 35], [42, 4, 58, 2], [42, 8, 58, 6, "promise"], [42, 15, 58, 13], [42, 18, 58, 16, "cache"], [42, 23, 58, 21], [42, 24, 58, 22, "get"], [42, 27, 58, 25], [42, 28, 58, 26, "stack"], [42, 33, 58, 31], [42, 34, 58, 32], [43, 4, 59, 2], [43, 8, 59, 6, "promise"], [43, 15, 59, 13], [43, 19, 59, 17], [43, 23, 59, 21], [43, 25, 59, 23], [44, 6, 60, 4, "promise"], [44, 13, 60, 11], [44, 16, 60, 14], [44, 20, 60, 14, "symbolicateStackTrace"], [44, 50, 60, 35], [44, 52, 60, 36, "stack"], [44, 57, 60, 41], [44, 59, 60, 43, "extraData"], [44, 68, 60, 52], [44, 69, 60, 53], [44, 70, 60, 54, "then"], [44, 74, 60, 58], [44, 75, 60, 59, "sanitize"], [44, 83, 60, 67], [44, 84, 60, 68], [45, 6, 61, 4, "cache"], [45, 11, 61, 9], [45, 12, 61, 10, "set"], [45, 15, 61, 13], [45, 16, 61, 14, "stack"], [45, 21, 61, 19], [45, 23, 61, 21, "promise"], [45, 30, 61, 28], [45, 31, 61, 29], [46, 4, 62, 2], [47, 4, 64, 2], [47, 11, 64, 9, "promise"], [47, 18, 64, 16], [48, 2, 65, 0], [49, 0, 65, 1], [49, 3]], "functionMap": {"names": ["<global>", "sanitize", "deleteStack", "symbolicate"], "mappings": "AAA;iBCsB;CDyB;OEE;CFE;OGE"}}, "type": "js/module"}]}