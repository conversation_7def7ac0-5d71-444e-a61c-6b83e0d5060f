{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n  'use strict';\n\n  if (global.window === undefined) {\n    global.window = global;\n  }\n  if (global.self === undefined) {\n    global.self = global;\n  }\n  global.process = global.process || {};\n  global.process.env = global.process.env || {};\n  if (!global.process.env.NODE_ENV) {\n    global.process.env.NODE_ENV = __DEV__ ? 'development' : 'production';\n  }\n});", "lineCount": 16, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [3, 2, 12, 0], [3, 14, 12, 12], [5, 2, 18, 0], [5, 6, 18, 4, "global"], [5, 12, 18, 10], [5, 13, 18, 11, "window"], [5, 19, 18, 17], [5, 24, 18, 22, "undefined"], [5, 33, 18, 31], [5, 35, 18, 33], [6, 4, 20, 2, "global"], [6, 10, 20, 8], [6, 11, 20, 9, "window"], [6, 17, 20, 15], [6, 20, 20, 18, "global"], [6, 26, 20, 24], [7, 2, 21, 0], [8, 2, 23, 0], [8, 6, 23, 4, "global"], [8, 12, 23, 10], [8, 13, 23, 11, "self"], [8, 17, 23, 15], [8, 22, 23, 20, "undefined"], [8, 31, 23, 29], [8, 33, 23, 31], [9, 4, 25, 2, "global"], [9, 10, 25, 8], [9, 11, 25, 9, "self"], [9, 15, 25, 13], [9, 18, 25, 16, "global"], [9, 24, 25, 22], [10, 2, 26, 0], [11, 2, 30, 0, "global"], [11, 8, 30, 6], [11, 9, 30, 7, "process"], [11, 16, 30, 14], [11, 19, 30, 17, "global"], [11, 25, 30, 23], [11, 26, 30, 24, "process"], [11, 33, 30, 31], [11, 37, 30, 35], [11, 38, 30, 36], [11, 39, 30, 37], [12, 2, 32, 0, "global"], [12, 8, 32, 6], [12, 9, 32, 7, "process"], [12, 16, 32, 14], [12, 17, 32, 15, "env"], [12, 20, 32, 18], [12, 23, 32, 21, "global"], [12, 29, 32, 27], [12, 30, 32, 28, "process"], [12, 37, 32, 35], [12, 38, 32, 36, "env"], [12, 41, 32, 39], [12, 45, 32, 43], [12, 46, 32, 44], [12, 47, 32, 45], [13, 2, 33, 0], [13, 6, 33, 4], [13, 7, 33, 5, "global"], [13, 13, 33, 11], [13, 14, 33, 12, "process"], [13, 21, 33, 19], [13, 22, 33, 20, "env"], [13, 25, 33, 23], [13, 26, 33, 24, "NODE_ENV"], [13, 34, 33, 32], [13, 36, 33, 34], [14, 4, 35, 2, "global"], [14, 10, 35, 8], [14, 11, 35, 9, "process"], [14, 18, 35, 16], [14, 19, 35, 17, "env"], [14, 22, 35, 20], [14, 23, 35, 21, "NODE_ENV"], [14, 31, 35, 29], [14, 34, 35, 32, "__DEV__"], [14, 41, 35, 39], [14, 44, 35, 42], [14, 57, 35, 55], [14, 60, 35, 58], [14, 72, 35, 70], [15, 2, 36, 0], [16, 0, 36, 1], [16, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}