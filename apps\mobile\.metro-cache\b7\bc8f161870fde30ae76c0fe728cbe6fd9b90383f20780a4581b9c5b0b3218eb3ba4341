{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../src/private/webapis/performance/Performance", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 76}}], "key": "WrVcLXAyCNy1Dgqwoy3wJvbhTdE=", "exportNames": ["*"]}}, {"name": "../../src/private/webapis/performance/specs/NativePerformance", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 94}}], "key": "AyF5dvxoHF37YC4/s68K/BdS//4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  var _Performance = _interopRequireDefault(require(_dependencyMap[1], \"../../src/private/webapis/performance/Performance\"));\n  var _NativePerformance = _interopRequireDefault(require(_dependencyMap[2], \"../../src/private/webapis/performance/specs/NativePerformance\"));\n  if (_NativePerformance.default) {\n    global.performance = new _Performance.default();\n  } else {\n    if (!global.performance) {\n      global.performance = {\n        mark: () => {},\n        measure: () => {},\n        now: () => {\n          var performanceNow = global.nativePerformanceNow || Date.now;\n          return performanceNow();\n        }\n      };\n    }\n  }\n});", "lineCount": 19, "map": [[3, 2, 11, 0], [3, 6, 11, 0, "_Performance"], [3, 18, 11, 0], [3, 21, 11, 0, "_interopRequireDefault"], [3, 43, 11, 0], [3, 44, 11, 0, "require"], [3, 51, 11, 0], [3, 52, 11, 0, "_dependencyMap"], [3, 66, 11, 0], [4, 2, 12, 0], [4, 6, 12, 0, "_NativePerformance"], [4, 24, 12, 0], [4, 27, 12, 0, "_interopRequireDefault"], [4, 49, 12, 0], [4, 50, 12, 0, "require"], [4, 57, 12, 0], [4, 58, 12, 0, "_dependencyMap"], [4, 72, 12, 0], [5, 2, 16, 0], [5, 6, 16, 4, "NativePerformance"], [5, 32, 16, 21], [5, 34, 16, 23], [6, 4, 18, 2, "global"], [6, 10, 18, 8], [6, 11, 18, 9, "performance"], [6, 22, 18, 20], [6, 25, 18, 23], [6, 29, 18, 27, "Performance"], [6, 49, 18, 38], [6, 50, 18, 39], [6, 51, 18, 40], [7, 2, 19, 0], [7, 3, 19, 1], [7, 9, 19, 7], [8, 4, 20, 2], [8, 8, 20, 6], [8, 9, 20, 7, "global"], [8, 15, 20, 13], [8, 16, 20, 14, "performance"], [8, 27, 20, 25], [8, 29, 20, 27], [9, 6, 22, 4, "global"], [9, 12, 22, 10], [9, 13, 22, 11, "performance"], [9, 24, 22, 22], [9, 27, 22, 25], [10, 8, 23, 6, "mark"], [10, 12, 23, 10], [10, 14, 23, 12, "mark"], [10, 15, 23, 12], [10, 20, 23, 18], [10, 21, 23, 19], [10, 22, 23, 20], [11, 8, 24, 6, "measure"], [11, 15, 24, 13], [11, 17, 24, 15, "measure"], [11, 18, 24, 15], [11, 23, 24, 21], [11, 24, 24, 22], [11, 25, 24, 23], [12, 8, 25, 6, "now"], [12, 11, 25, 9], [12, 13, 25, 11, "now"], [12, 14, 25, 11], [12, 19, 25, 17], [13, 10, 26, 8], [13, 14, 26, 14, "performanceNow"], [13, 28, 26, 28], [13, 31, 26, 31, "global"], [13, 37, 26, 37], [13, 38, 26, 38, "nativePerformanceNow"], [13, 58, 26, 58], [13, 62, 26, 62, "Date"], [13, 66, 26, 66], [13, 67, 26, 67, "now"], [13, 70, 26, 70], [14, 10, 27, 8], [14, 17, 27, 15, "performanceNow"], [14, 31, 27, 29], [14, 32, 27, 30], [14, 33, 27, 31], [15, 8, 28, 6], [16, 6, 29, 4], [16, 7, 29, 5], [17, 4, 30, 2], [18, 2, 31, 0], [19, 0, 31, 1], [19, 3]], "functionMap": {"names": ["<global>", "global.performance.mark", "global.performance.measure", "global.performance.now"], "mappings": "AAA;YCsB,QD;eEC,QF;WGC;OHG"}}, "type": "js/module"}]}