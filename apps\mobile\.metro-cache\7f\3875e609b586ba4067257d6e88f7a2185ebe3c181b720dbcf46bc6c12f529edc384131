{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2F..%2F..%2Fnode_modules%2F%40react-navigation%2Felements%2Flib%2Fmodule%2Fassets\",\n    \"width\": 24,\n    \"height\": 24,\n    \"scales\": [1, 2, 3, 4],\n    \"hash\": \"dbc3af23c3cbbe45d326afc1d31c2e92\",\n    \"name\": \"back-icon\",\n    \"type\": \"png\",\n    \"fileHashes\": [\"778ffc9fe8773a878e9c30a6304784de\", \"c79c3606a1cf168006ad3979763c7e0c\", \"02bc1fa7c0313217bde2d65ccbff40c9\", \"35ba0eaec5a4f5ed12ca16fabeae451d\"]\n  });\n});", "lineCount": 13, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 134, 1, 154], [5, 4, 1, 155], [5, 11, 1, 162], [5, 13, 1, 163], [5, 15, 1, 165], [6, 4, 1, 166], [6, 12, 1, 174], [6, 14, 1, 175], [6, 16, 1, 177], [7, 4, 1, 178], [7, 12, 1, 186], [7, 14, 1, 187], [7, 15, 1, 188], [7, 16, 1, 189], [7, 18, 1, 190], [7, 19, 1, 191], [7, 21, 1, 192], [7, 22, 1, 193], [7, 24, 1, 194], [7, 25, 1, 195], [7, 26, 1, 196], [8, 4, 1, 197], [8, 10, 1, 203], [8, 12, 1, 204], [8, 46, 1, 238], [9, 4, 1, 239], [9, 10, 1, 245], [9, 12, 1, 246], [9, 23, 1, 257], [10, 4, 1, 258], [10, 10, 1, 264], [10, 12, 1, 265], [10, 17, 1, 270], [11, 4, 1, 271], [11, 16, 1, 283], [11, 18, 1, 284], [11, 19, 1, 285], [11, 53, 1, 319], [11, 55, 1, 320], [11, 89, 1, 354], [11, 91, 1, 355], [11, 125, 1, 389], [11, 127, 1, 390], [11, 161, 1, 424], [12, 2, 1, 425], [12, 3, 1, 426], [13, 0, 1, 426], [13, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}