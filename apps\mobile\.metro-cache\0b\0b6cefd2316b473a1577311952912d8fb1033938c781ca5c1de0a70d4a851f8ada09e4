{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 79, "index": 79}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./environment/browser", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 81}, "end": {"line": 8, "column": 31, "index": 201}}], "key": "4jLi2oL77AuNgQ8OVF8xZurKgiA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _browser = require(_dependencyMap[1], \"./environment/browser\");\n  if (__DEV__ && typeof \"android\" === 'undefined') {\n    console.warn(`The global process.env.EXPO_OS is not defined. This should be inlined by babel-preset-expo during transformation.`);\n  }\n  var nativeSelect = typeof window !== 'undefined' ? _reactNative.Platform.select :\n  // process.env.EXPO_OS is injected by `babel-preset-expo` and available in both client and `react-server` environments.\n  // Opt to use the env var when possible, and fallback to the React Native Platform module when it's not (arbitrary bundlers and transformers).\n  function select(specifics) {\n    if (!\"android\") return undefined;\n    if (specifics.hasOwnProperty(\"android\")) {\n      return specifics[\"android\"];\n    } else if (true && specifics.hasOwnProperty('native')) {\n      return specifics.native;\n    } else if (specifics.hasOwnProperty('default')) {\n      return specifics.default;\n    }\n    // do nothing...\n    return undefined;\n  };\n  var Platform = {\n    /**\n     * Denotes the currently running platform.\n     * Can be one of ios, android, web.\n     */\n    OS: \"android\" || _reactNative.Platform.OS,\n    /**\n     * Returns the value with the matching platform.\n     * Object keys can be any of ios, android, native, web, default.\n     *\n     * @ios ios, native, default\n     * @android android, native, default\n     * @web web, default\n     */\n    select: nativeSelect,\n    /**\n     * Denotes if the DOM API is available in the current environment.\n     * The DOM is not available in native React runtimes and Node.js.\n     */\n    isDOMAvailable: _browser.isDOMAvailable,\n    /**\n     * Denotes if the current environment can attach event listeners\n     * to the window. This will return false in native React\n     * runtimes and Node.js.\n     */\n    canUseEventListeners: _browser.canUseEventListeners,\n    /**\n     * Denotes if the current environment can inspect properties of the\n     * screen on which the current window is being rendered. This will\n     * return false in native React runtimes and Node.js.\n     */\n    canUseViewport: _browser.canUseViewport,\n    /**\n     * If the JavaScript is being executed in a remote JavaScript environment.\n     * When `true`, synchronous native invocations cannot be executed.\n     */\n    isAsyncDebugging: _browser.isAsyncDebugging\n  };\n  var _default = exports.default = Platform;\n});", "lineCount": 65, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_reactNative"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 3, 0], [7, 6, 3, 0, "_browser"], [7, 14, 3, 0], [7, 17, 3, 0, "require"], [7, 24, 3, 0], [7, 25, 3, 0, "_dependencyMap"], [7, 39, 3, 0], [8, 2, 14, 0], [8, 6, 14, 4, "__DEV__"], [8, 13, 14, 11], [8, 17, 14, 15], [8, 33, 14, 41], [8, 38, 14, 46], [8, 49, 14, 57], [8, 51, 14, 59], [9, 4, 15, 2, "console"], [9, 11, 15, 9], [9, 12, 15, 10, "warn"], [9, 16, 15, 14], [9, 17, 16, 4], [9, 132, 17, 2], [9, 133, 17, 3], [10, 2, 18, 0], [11, 2, 20, 0], [11, 6, 20, 6, "nativeSelect"], [11, 18, 20, 18], [11, 21, 21, 2], [11, 28, 21, 9, "window"], [11, 34, 21, 15], [11, 39, 21, 20], [11, 50, 21, 31], [11, 53, 22, 6, "ReactNativePlatform"], [11, 74, 22, 25], [11, 75, 22, 26, "select"], [11, 81, 22, 32], [12, 2, 23, 6], [13, 2, 24, 6], [14, 2, 25, 6], [14, 11, 25, 15, "select"], [14, 17, 25, 21, "select"], [14, 18, 25, 25, "specifics"], [14, 27, 25, 78], [14, 29, 25, 95], [15, 4, 26, 8], [15, 8, 26, 12], [15, 18, 26, 32], [15, 20, 26, 34], [15, 27, 26, 41, "undefined"], [15, 36, 26, 50], [16, 4, 27, 8], [16, 8, 27, 12, "specifics"], [16, 17, 27, 21], [16, 18, 27, 22, "hasOwnProperty"], [16, 32, 27, 36], [16, 42, 27, 56], [16, 43, 27, 57], [16, 45, 27, 59], [17, 6, 28, 10], [17, 13, 28, 17, "specifics"], [17, 22, 28, 26], [17, 33, 28, 71], [18, 4, 29, 8], [18, 5, 29, 9], [18, 11, 29, 15], [18, 15, 29, 19], [18, 23, 29, 52, "specifics"], [18, 32, 29, 61], [18, 33, 29, 62, "hasOwnProperty"], [18, 47, 29, 76], [18, 48, 29, 77], [18, 56, 29, 85], [18, 57, 29, 86], [18, 59, 29, 88], [19, 6, 30, 10], [19, 13, 30, 17, "specifics"], [19, 22, 30, 26], [19, 23, 30, 27, "native"], [19, 29, 30, 33], [20, 4, 31, 8], [20, 5, 31, 9], [20, 11, 31, 15], [20, 15, 31, 19, "specifics"], [20, 24, 31, 28], [20, 25, 31, 29, "hasOwnProperty"], [20, 39, 31, 43], [20, 40, 31, 44], [20, 49, 31, 53], [20, 50, 31, 54], [20, 52, 31, 56], [21, 6, 32, 10], [21, 13, 32, 17, "specifics"], [21, 22, 32, 26], [21, 23, 32, 27, "default"], [21, 30, 32, 34], [22, 4, 33, 8], [23, 4, 34, 8], [24, 4, 35, 8], [24, 11, 35, 15, "undefined"], [24, 20, 35, 24], [25, 2, 36, 6], [25, 3, 36, 7], [26, 2, 38, 0], [26, 6, 38, 6, "Platform"], [26, 14, 38, 14], [26, 17, 38, 17], [27, 4, 39, 2], [28, 0, 40, 0], [29, 0, 41, 0], [30, 0, 42, 0], [31, 4, 43, 2, "OS"], [31, 6, 43, 4], [31, 8, 43, 6], [31, 21, 43, 29, "ReactNativePlatform"], [31, 42, 43, 48], [31, 43, 43, 49, "OS"], [31, 45, 43, 51], [32, 4, 44, 2], [33, 0, 45, 0], [34, 0, 46, 0], [35, 0, 47, 0], [36, 0, 48, 0], [37, 0, 49, 0], [38, 0, 50, 0], [39, 0, 51, 0], [40, 4, 52, 2, "select"], [40, 10, 52, 8], [40, 12, 52, 10, "nativeSelect"], [40, 24, 52, 40], [41, 4, 53, 2], [42, 0, 54, 0], [43, 0, 55, 0], [44, 0, 56, 0], [45, 4, 57, 2, "isDOMAvailable"], [45, 18, 57, 16], [45, 20, 57, 2, "isDOMAvailable"], [45, 43, 57, 16], [46, 4, 58, 2], [47, 0, 59, 0], [48, 0, 60, 0], [49, 0, 61, 0], [50, 0, 62, 0], [51, 4, 63, 2, "canUseEventListeners"], [51, 24, 63, 22], [51, 26, 63, 2, "canUseEventListeners"], [51, 55, 63, 22], [52, 4, 64, 2], [53, 0, 65, 0], [54, 0, 66, 0], [55, 0, 67, 0], [56, 0, 68, 0], [57, 4, 69, 2, "canUseViewport"], [57, 18, 69, 16], [57, 20, 69, 2, "canUseViewport"], [57, 43, 69, 16], [58, 4, 70, 2], [59, 0, 71, 0], [60, 0, 72, 0], [61, 0, 73, 0], [62, 4, 74, 2, "isAsyncDebugging"], [62, 20, 74, 18], [62, 22, 74, 2, "isAsyncDebugging"], [63, 2, 75, 0], [63, 3, 75, 1], [64, 2, 75, 2], [64, 6, 75, 2, "_default"], [64, 14, 75, 2], [64, 17, 75, 2, "exports"], [64, 24, 75, 2], [64, 25, 75, 2, "default"], [64, 32, 75, 2], [64, 35, 77, 15, "Platform"], [64, 43, 77, 23], [65, 0, 77, 23], [65, 3]], "functionMap": {"names": ["<global>", "select"], "mappings": "AAA;MCwB;ODW"}}, "type": "js/module"}]}