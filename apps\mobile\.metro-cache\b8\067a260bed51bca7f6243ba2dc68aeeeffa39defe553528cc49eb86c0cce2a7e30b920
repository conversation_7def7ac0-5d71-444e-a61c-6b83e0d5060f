{"dependencies": [{"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 37, "index": 37}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.findHandler = findHandler;\n  exports.findHandlerByTestID = findHandlerByTestID;\n  exports.findOldGestureHandler = findOldGestureHandler;\n  exports.handlerIDToTag = void 0;\n  exports.registerHandler = registerHandler;\n  exports.registerOldGestureHandler = registerOldGestureHandler;\n  exports.unregisterHandler = unregisterHandler;\n  exports.unregisterOldGestureHandler = unregisterOldGestureHandler;\n  var _utils = require(_dependencyMap[0], \"../utils\");\n  var handlerIDToTag = exports.handlerIDToTag = {};\n  var gestures = new Map();\n  var oldHandlers = new Map();\n  var testIDs = new Map();\n  function registerHandler(handlerTag, handler, testID) {\n    gestures.set(handlerTag, handler);\n    if ((0, _utils.isTestEnv)() && testID) {\n      testIDs.set(testID, handlerTag);\n    }\n  }\n  function registerOldGestureHandler(handlerTag, handler) {\n    oldHandlers.set(handlerTag, handler);\n  }\n  function unregisterOldGestureHandler(handlerTag) {\n    oldHandlers.delete(handlerTag);\n  }\n  function unregisterHandler(handlerTag, testID) {\n    gestures.delete(handlerTag);\n    if ((0, _utils.isTestEnv)() && testID) {\n      testIDs.delete(testID);\n    }\n  }\n  function findHandler(handlerTag) {\n    return gestures.get(handlerTag);\n  }\n  function findOldGestureHandler(handlerTag) {\n    return oldHandlers.get(handlerTag);\n  }\n  function findHandlerByTestID(testID) {\n    var handlerTag = testIDs.get(testID);\n    if (handlerTag !== undefined) {\n      return findHandler(handlerTag) ?? null;\n    }\n    return null;\n  }\n});", "lineCount": 49, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_utils"], [13, 12, 1, 0], [13, 15, 1, 0, "require"], [13, 22, 1, 0], [13, 23, 1, 0, "_dependencyMap"], [13, 37, 1, 0], [14, 2, 5, 7], [14, 6, 5, 13, "handlerIDToTag"], [14, 20, 5, 51], [14, 23, 5, 51, "exports"], [14, 30, 5, 51], [14, 31, 5, 51, "handlerIDToTag"], [14, 45, 5, 51], [14, 48, 5, 54], [14, 49, 5, 55], [14, 50, 5, 56], [15, 2, 6, 0], [15, 6, 6, 6, "gestures"], [15, 14, 6, 14], [15, 17, 6, 17], [15, 21, 6, 21, "Map"], [15, 24, 6, 24], [15, 25, 6, 46], [15, 26, 6, 47], [16, 2, 7, 0], [16, 6, 7, 6, "oldHandlers"], [16, 17, 7, 17], [16, 20, 7, 20], [16, 24, 7, 24, "Map"], [16, 27, 7, 27], [16, 28, 7, 61], [16, 29, 7, 62], [17, 2, 8, 0], [17, 6, 8, 6, "testIDs"], [17, 13, 8, 13], [17, 16, 8, 16], [17, 20, 8, 20, "Map"], [17, 23, 8, 23], [17, 24, 8, 40], [17, 25, 8, 41], [18, 2, 10, 7], [18, 11, 10, 16, "registerHandler"], [18, 26, 10, 31, "registerHandler"], [18, 27, 11, 2, "handlerTag"], [18, 37, 11, 20], [18, 39, 12, 2, "handler"], [18, 46, 12, 22], [18, 48, 13, 2, "testID"], [18, 54, 13, 17], [18, 56, 14, 2], [19, 4, 15, 2, "gestures"], [19, 12, 15, 10], [19, 13, 15, 11, "set"], [19, 16, 15, 14], [19, 17, 15, 15, "handlerTag"], [19, 27, 15, 25], [19, 29, 15, 27, "handler"], [19, 36, 15, 34], [19, 37, 15, 35], [20, 4, 16, 2], [20, 8, 16, 6], [20, 12, 16, 6, "isTestEnv"], [20, 28, 16, 15], [20, 30, 16, 16], [20, 31, 16, 17], [20, 35, 16, 21, "testID"], [20, 41, 16, 27], [20, 43, 16, 29], [21, 6, 17, 4, "testIDs"], [21, 13, 17, 11], [21, 14, 17, 12, "set"], [21, 17, 17, 15], [21, 18, 17, 16, "testID"], [21, 24, 17, 22], [21, 26, 17, 24, "handlerTag"], [21, 36, 17, 34], [21, 37, 17, 35], [22, 4, 18, 2], [23, 2, 19, 0], [24, 2, 21, 7], [24, 11, 21, 16, "registerOldGestureHandler"], [24, 36, 21, 41, "registerOldGestureHandler"], [24, 37, 22, 2, "handlerTag"], [24, 47, 22, 20], [24, 49, 23, 2, "handler"], [24, 56, 23, 34], [24, 58, 24, 2], [25, 4, 25, 2, "oldHandlers"], [25, 15, 25, 13], [25, 16, 25, 14, "set"], [25, 19, 25, 17], [25, 20, 25, 18, "handlerTag"], [25, 30, 25, 28], [25, 32, 25, 30, "handler"], [25, 39, 25, 37], [25, 40, 25, 38], [26, 2, 26, 0], [27, 2, 28, 7], [27, 11, 28, 16, "unregisterOldGestureHandler"], [27, 38, 28, 43, "unregisterOldGestureHandler"], [27, 39, 28, 44, "handlerTag"], [27, 49, 28, 62], [27, 51, 28, 64], [28, 4, 29, 2, "oldHandlers"], [28, 15, 29, 13], [28, 16, 29, 14, "delete"], [28, 22, 29, 20], [28, 23, 29, 21, "handlerTag"], [28, 33, 29, 31], [28, 34, 29, 32], [29, 2, 30, 0], [30, 2, 32, 7], [30, 11, 32, 16, "unregister<PERSON><PERSON><PERSON>"], [30, 28, 32, 33, "unregister<PERSON><PERSON><PERSON>"], [30, 29, 32, 34, "handlerTag"], [30, 39, 32, 52], [30, 41, 32, 54, "testID"], [30, 47, 32, 69], [30, 49, 32, 71], [31, 4, 33, 2, "gestures"], [31, 12, 33, 10], [31, 13, 33, 11, "delete"], [31, 19, 33, 17], [31, 20, 33, 18, "handlerTag"], [31, 30, 33, 28], [31, 31, 33, 29], [32, 4, 34, 2], [32, 8, 34, 6], [32, 12, 34, 6, "isTestEnv"], [32, 28, 34, 15], [32, 30, 34, 16], [32, 31, 34, 17], [32, 35, 34, 21, "testID"], [32, 41, 34, 27], [32, 43, 34, 29], [33, 6, 35, 4, "testIDs"], [33, 13, 35, 11], [33, 14, 35, 12, "delete"], [33, 20, 35, 18], [33, 21, 35, 19, "testID"], [33, 27, 35, 25], [33, 28, 35, 26], [34, 4, 36, 2], [35, 2, 37, 0], [36, 2, 39, 7], [36, 11, 39, 16, "<PERSON><PERSON><PERSON><PERSON>"], [36, 22, 39, 27, "<PERSON><PERSON><PERSON><PERSON>"], [36, 23, 39, 28, "handlerTag"], [36, 33, 39, 46], [36, 35, 39, 48], [37, 4, 40, 2], [37, 11, 40, 9, "gestures"], [37, 19, 40, 17], [37, 20, 40, 18, "get"], [37, 23, 40, 21], [37, 24, 40, 22, "handlerTag"], [37, 34, 40, 32], [37, 35, 40, 33], [38, 2, 41, 0], [39, 2, 43, 7], [39, 11, 43, 16, "findOldGestureHandler"], [39, 32, 43, 37, "findOldGestureHandler"], [39, 33, 43, 38, "handlerTag"], [39, 43, 43, 56], [39, 45, 43, 58], [40, 4, 44, 2], [40, 11, 44, 9, "oldHandlers"], [40, 22, 44, 20], [40, 23, 44, 21, "get"], [40, 26, 44, 24], [40, 27, 44, 25, "handlerTag"], [40, 37, 44, 35], [40, 38, 44, 36], [41, 2, 45, 0], [42, 2, 47, 7], [42, 11, 47, 16, "findHandlerByTestID"], [42, 30, 47, 35, "findHandlerByTestID"], [42, 31, 47, 36, "testID"], [42, 37, 47, 50], [42, 39, 47, 52], [43, 4, 48, 2], [43, 8, 48, 8, "handlerTag"], [43, 18, 48, 18], [43, 21, 48, 21, "testIDs"], [43, 28, 48, 28], [43, 29, 48, 29, "get"], [43, 32, 48, 32], [43, 33, 48, 33, "testID"], [43, 39, 48, 39], [43, 40, 48, 40], [44, 4, 49, 2], [44, 8, 49, 6, "handlerTag"], [44, 18, 49, 16], [44, 23, 49, 21, "undefined"], [44, 32, 49, 30], [44, 34, 49, 32], [45, 6, 50, 4], [45, 13, 50, 11, "<PERSON><PERSON><PERSON><PERSON>"], [45, 24, 50, 22], [45, 25, 50, 23, "handlerTag"], [45, 35, 50, 33], [45, 36, 50, 34], [45, 40, 50, 38], [45, 44, 50, 42], [46, 4, 51, 2], [47, 4, 52, 2], [47, 11, 52, 9], [47, 15, 52, 13], [48, 2, 53, 0], [49, 0, 53, 1], [49, 3]], "functionMap": {"names": ["<global>", "registerHandler", "registerOldGestureHandler", "unregisterOldGestureHandler", "unregister<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "findOldGestureHandler", "findHandlerByTestID"], "mappings": "AAA;OCS;CDS;OEE;CFK;OGE;CHE;OIE;CJK;OKE;CLE;OME;CNE;OOE;CPM"}}, "type": "js/module"}]}