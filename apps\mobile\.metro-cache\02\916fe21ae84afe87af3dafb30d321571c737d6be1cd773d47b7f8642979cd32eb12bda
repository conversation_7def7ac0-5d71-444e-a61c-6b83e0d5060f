{"dependencies": [{"name": "../ConfigHelper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 49, "index": 64}}], "key": "G9xd6OWOTj9ITxVE1K601ohQjLg=", "exportNames": ["*"]}}, {"name": "./styleUpdater", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 136}, "end": {"line": 5, "column": 44, "index": 180}}], "key": "F6H1fvmPsODndnqalYVnB+rzSpc=", "exportNames": ["*"]}}, {"name": "./swipeSimulator", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 181}, "end": {"line": 6, "column": 53, "index": 234}}], "key": "IiAyyxCCIbl+sreblR0DiQS9NIo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.startScreenTransition = exports.finishScreenTransition = void 0;\n  var _ConfigHelper = require(_dependencyMap[0], \"../ConfigHelper\");\n  var _styleUpdater = require(_dependencyMap[1], \"./styleUpdater\");\n  var _swipeSimulator = require(_dependencyMap[2], \"./swipeSimulator\");\n  (0, _ConfigHelper.configureProps)();\n  var _worklet_1305408908412_init_data = {\n    code: \"function startScreenTransition_animationManagerTs1(screenTransitionConfig){const{applyStyle}=this.__closure;const{stackTag:stackTag,sharedEvent:sharedEvent}=screenTransitionConfig;sharedEvent.addListener(stackTag,function(){applyStyle(screenTransitionConfig,sharedEvent.value);});}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\animationManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"startScreenTransition_animationManagerTs1\\\",\\\"screenTransitionConfig\\\",\\\"applyStyle\\\",\\\"__closure\\\",\\\"stackTag\\\",\\\"sharedEvent\\\",\\\"addListener\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/animationManager.ts\\\"],\\\"mappings\\\":\\\"AASO,SAAAA,yCACLA,CAAAC,sBACA,QAAAC,UAAA,OAAAC,SAAA,CAEA,KAAM,CAAEC,QAAQ,CAARA,QAAQ,CAAEC,WAAA,CAAAA,WAAY,CAAC,CAAGJ,sBAAsB,CACxDI,WAAW,CAACC,WAAW,CAACF,QAAQ,CAAE,UAAM,CACtCF,UAAU,CAACD,sBAAsB,CAAEI,WAAW,CAACE,KAAK,CAAC,CACvD,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var startScreenTransition = exports.startScreenTransition = function () {\n    var _e = [new global.Error(), -2, -27];\n    var startScreenTransition = function (screenTransitionConfig) {\n      var stackTag = screenTransitionConfig.stackTag,\n        sharedEvent = screenTransitionConfig.sharedEvent;\n      sharedEvent.addListener(stackTag, () => {\n        (0, _styleUpdater.applyStyle)(screenTransitionConfig, sharedEvent.value);\n      });\n    };\n    startScreenTransition.__closure = {\n      applyStyle: _styleUpdater.applyStyle\n    };\n    startScreenTransition.__workletHash = 1305408908412;\n    startScreenTransition.__initData = _worklet_1305408908412_init_data;\n    startScreenTransition.__stackDetails = _e;\n    return startScreenTransition;\n  }();\n  var _worklet_405909314210_init_data = {\n    code: \"function getLockAxis_animationManagerTs2(goBackGesture){if(['swipeRight','swipeLeft','horizontalSwipe'].includes(goBackGesture)){return'x';}else if(['swipeUp','swipeDown','verticalSwipe'].includes(goBackGesture)){return'y';}return undefined;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\animationManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getLockAxis_animationManagerTs2\\\",\\\"goBackGesture\\\",\\\"includes\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/animationManager.ts\\\"],\\\"mappings\\\":\\\"AAmBA,SAAAA,+BAAsDA,CAAAC,aAAA,EAEpD,GAAI,CAAC,YAAY,CAAE,WAAW,CAAE,iBAAiB,CAAC,CAACC,QAAQ,CAACD,aAAa,CAAC,CAAE,CAC1E,MAAO,GAAG,CACZ,CAAC,IAAM,IACL,CAAC,SAAS,CAAE,WAAW,CAAE,eAAe,CAAC,CAACC,QAAQ,CAACD,aAAa,CAAC,CACjE,CACA,MAAO,GAAG,CACZ,CACA,MAAO,CAAAE,SAAS,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var getLockAxis = function () {\n    var _e = [new global.Error(), 1, -27];\n    var getLockAxis = function (goBackGesture) {\n      if (['swipeRight', 'swipeLeft', 'horizontalSwipe'].includes(goBackGesture)) {\n        return 'x';\n      } else if (['swipeUp', 'swipeDown', 'verticalSwipe'].includes(goBackGesture)) {\n        return 'y';\n      }\n      return undefined;\n    };\n    getLockAxis.__closure = {};\n    getLockAxis.__workletHash = 405909314210;\n    getLockAxis.__initData = _worklet_405909314210_init_data;\n    getLockAxis.__stackDetails = _e;\n    return getLockAxis;\n  }();\n  var _worklet_14124077705105_init_data = {\n    code: \"function finishScreenTransition_animationManagerTs3(screenTransitionConfig){const{getLockAxis,getSwipeSimulator}=this.__closure;const{stackTag:stackTag,sharedEvent:sharedEvent,goBackGesture:goBackGesture}=screenTransitionConfig;sharedEvent.removeListener(stackTag);const lockAxis=getLockAxis(goBackGesture);const step=getSwipeSimulator(sharedEvent.value,screenTransitionConfig,lockAxis);step();}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\animationManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"finishScreenTransition_animationManagerTs3\\\",\\\"screenTransitionConfig\\\",\\\"getLockAxis\\\",\\\"getSwipeSimulator\\\",\\\"__closure\\\",\\\"stackTag\\\",\\\"sharedEvent\\\",\\\"goBackGesture\\\",\\\"removeListener\\\",\\\"lockAxis\\\",\\\"step\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/animationManager.ts\\\"],\\\"mappings\\\":\\\"AA+BO,SAAAA,0CACLA,CAAAC,sBACA,QAAAC,WAAA,CAAAC,iBAAA,OAAAC,SAAA,CAEA,KAAM,CAAEC,QAAQ,CAARA,QAAQ,CAAEC,WAAW,CAAXA,WAAW,CAAEC,aAAA,CAAAA,aAAc,CAAC,CAAGN,sBAAsB,CACvEK,WAAW,CAACE,cAAc,CAACH,QAAQ,CAAC,CACpC,KAAM,CAAAI,QAAQ,CAAGP,WAAW,CAACK,aAAa,CAAC,CAC3C,KAAM,CAAAG,IAAI,CAAGP,iBAAiB,CAC5BG,WAAW,CAACK,KAAK,CACjBV,sBAAsB,CACtBQ,QACF,CAAC,CACDC,IAAI,CAAC,CAAC,CACR\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var finishScreenTransition = exports.finishScreenTransition = function () {\n    var _e = [new global.Error(), -3, -27];\n    var finishScreenTransition = function (screenTransitionConfig) {\n      var stackTag = screenTransitionConfig.stackTag,\n        sharedEvent = screenTransitionConfig.sharedEvent,\n        goBackGesture = screenTransitionConfig.goBackGesture;\n      sharedEvent.removeListener(stackTag);\n      var lockAxis = getLockAxis(goBackGesture);\n      var step = (0, _swipeSimulator.getSwipeSimulator)(sharedEvent.value, screenTransitionConfig, lockAxis);\n      step();\n    };\n    finishScreenTransition.__closure = {\n      getLockAxis,\n      getSwipeSimulator: _swipeSimulator.getSwipeSimulator\n    };\n    finishScreenTransition.__workletHash = 14124077705105;\n    finishScreenTransition.__initData = _worklet_14124077705105_init_data;\n    finishScreenTransition.__stackDetails = _e;\n    return finishScreenTransition;\n  }();\n});", "lineCount": 83, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "startScreenTransition"], [7, 31, 1, 13], [7, 34, 1, 13, "exports"], [7, 41, 1, 13], [7, 42, 1, 13, "finishScreenTransition"], [7, 64, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_ConfigHelper"], [8, 19, 3, 0], [8, 22, 3, 0, "require"], [8, 29, 3, 0], [8, 30, 3, 0, "_dependencyMap"], [8, 44, 3, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_styleUpdater"], [9, 19, 5, 0], [9, 22, 5, 0, "require"], [9, 29, 5, 0], [9, 30, 5, 0, "_dependencyMap"], [9, 44, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_swipeSimulator"], [10, 21, 6, 0], [10, 24, 6, 0, "require"], [10, 31, 6, 0], [10, 32, 6, 0, "_dependencyMap"], [10, 46, 6, 0], [11, 2, 8, 0], [11, 6, 8, 0, "configureProps"], [11, 34, 8, 14], [11, 36, 8, 15], [11, 37, 8, 16], [12, 2, 8, 17], [12, 6, 8, 17, "_worklet_1305408908412_init_data"], [12, 38, 8, 17], [13, 4, 8, 17, "code"], [13, 8, 8, 17], [14, 4, 8, 17, "location"], [14, 12, 8, 17], [15, 4, 8, 17, "sourceMap"], [15, 13, 8, 17], [16, 4, 8, 17, "version"], [16, 11, 8, 17], [17, 2, 8, 17], [18, 2, 8, 17], [18, 6, 8, 17, "startScreenTransition"], [18, 27, 8, 17], [18, 30, 8, 17, "exports"], [18, 37, 8, 17], [18, 38, 8, 17, "startScreenTransition"], [18, 59, 8, 17], [18, 62, 10, 7], [19, 4, 10, 7], [19, 8, 10, 7, "_e"], [19, 10, 10, 7], [19, 18, 10, 7, "global"], [19, 24, 10, 7], [19, 25, 10, 7, "Error"], [19, 30, 10, 7], [20, 4, 10, 7], [20, 8, 10, 7, "startScreenTransition"], [20, 29, 10, 7], [20, 41, 10, 7, "startScreenTransition"], [20, 42, 11, 2, "screenTransitionConfig"], [20, 64, 11, 48], [20, 66, 12, 2], [21, 6, 14, 2], [21, 10, 14, 10, "stackTag"], [21, 18, 14, 18], [21, 21, 14, 36, "screenTransitionConfig"], [21, 43, 14, 58], [21, 44, 14, 10, "stackTag"], [21, 52, 14, 18], [22, 8, 14, 20, "sharedEvent"], [22, 19, 14, 31], [22, 22, 14, 36, "screenTransitionConfig"], [22, 44, 14, 58], [22, 45, 14, 20, "sharedEvent"], [22, 56, 14, 31], [23, 6, 15, 2, "sharedEvent"], [23, 17, 15, 13], [23, 18, 15, 14, "addListener"], [23, 29, 15, 25], [23, 30, 15, 26, "stackTag"], [23, 38, 15, 34], [23, 40, 15, 36], [23, 46, 15, 42], [24, 8, 16, 4], [24, 12, 16, 4, "applyStyle"], [24, 36, 16, 14], [24, 38, 16, 15, "screenTransitionConfig"], [24, 60, 16, 37], [24, 62, 16, 39, "sharedEvent"], [24, 73, 16, 50], [24, 74, 16, 51, "value"], [24, 79, 16, 56], [24, 80, 16, 57], [25, 6, 17, 2], [25, 7, 17, 3], [25, 8, 17, 4], [26, 4, 18, 0], [26, 5, 18, 1], [27, 4, 18, 1, "startScreenTransition"], [27, 25, 18, 1], [27, 26, 18, 1, "__closure"], [27, 35, 18, 1], [28, 6, 18, 1, "applyStyle"], [28, 16, 18, 1], [28, 18, 16, 4, "applyStyle"], [29, 4, 16, 14], [30, 4, 16, 14, "startScreenTransition"], [30, 25, 16, 14], [30, 26, 16, 14, "__workletHash"], [30, 39, 16, 14], [31, 4, 16, 14, "startScreenTransition"], [31, 25, 16, 14], [31, 26, 16, 14, "__initData"], [31, 36, 16, 14], [31, 39, 16, 14, "_worklet_1305408908412_init_data"], [31, 71, 16, 14], [32, 4, 16, 14, "startScreenTransition"], [32, 25, 16, 14], [32, 26, 16, 14, "__stackDetails"], [32, 40, 16, 14], [32, 43, 16, 14, "_e"], [32, 45, 16, 14], [33, 4, 16, 14], [33, 11, 16, 14, "startScreenTransition"], [33, 32, 16, 14], [34, 2, 16, 14], [34, 3, 10, 7], [35, 2, 10, 7], [35, 6, 10, 7, "_worklet_405909314210_init_data"], [35, 37, 10, 7], [36, 4, 10, 7, "code"], [36, 8, 10, 7], [37, 4, 10, 7, "location"], [37, 12, 10, 7], [38, 4, 10, 7, "sourceMap"], [38, 13, 10, 7], [39, 4, 10, 7, "version"], [39, 11, 10, 7], [40, 2, 10, 7], [41, 2, 10, 7], [41, 6, 10, 7, "getLockAxis"], [41, 17, 10, 7], [41, 20, 20, 0], [42, 4, 20, 0], [42, 8, 20, 0, "_e"], [42, 10, 20, 0], [42, 18, 20, 0, "global"], [42, 24, 20, 0], [42, 25, 20, 0, "Error"], [42, 30, 20, 0], [43, 4, 20, 0], [43, 8, 20, 0, "getLockAxis"], [43, 19, 20, 0], [43, 31, 20, 0, "getLockAxis"], [43, 32, 20, 21, "goBackGesture"], [43, 45, 20, 42], [43, 47, 20, 54], [44, 6, 22, 2], [44, 10, 22, 6], [44, 11, 22, 7], [44, 23, 22, 19], [44, 25, 22, 21], [44, 36, 22, 32], [44, 38, 22, 34], [44, 55, 22, 51], [44, 56, 22, 52], [44, 57, 22, 53, "includes"], [44, 65, 22, 61], [44, 66, 22, 62, "goBackGesture"], [44, 79, 22, 75], [44, 80, 22, 76], [44, 82, 22, 78], [45, 8, 23, 4], [45, 15, 23, 11], [45, 18, 23, 14], [46, 6, 24, 2], [46, 7, 24, 3], [46, 13, 24, 9], [46, 17, 25, 4], [46, 18, 25, 5], [46, 27, 25, 14], [46, 29, 25, 16], [46, 40, 25, 27], [46, 42, 25, 29], [46, 57, 25, 44], [46, 58, 25, 45], [46, 59, 25, 46, "includes"], [46, 67, 25, 54], [46, 68, 25, 55, "goBackGesture"], [46, 81, 25, 68], [46, 82, 25, 69], [46, 84, 26, 4], [47, 8, 27, 4], [47, 15, 27, 11], [47, 18, 27, 14], [48, 6, 28, 2], [49, 6, 29, 2], [49, 13, 29, 9, "undefined"], [49, 22, 29, 18], [50, 4, 30, 0], [50, 5, 30, 1], [51, 4, 30, 1, "getLockAxis"], [51, 15, 30, 1], [51, 16, 30, 1, "__closure"], [51, 25, 30, 1], [52, 4, 30, 1, "getLockAxis"], [52, 15, 30, 1], [52, 16, 30, 1, "__workletHash"], [52, 29, 30, 1], [53, 4, 30, 1, "getLockAxis"], [53, 15, 30, 1], [53, 16, 30, 1, "__initData"], [53, 26, 30, 1], [53, 29, 30, 1, "_worklet_405909314210_init_data"], [53, 60, 30, 1], [54, 4, 30, 1, "getLockAxis"], [54, 15, 30, 1], [54, 16, 30, 1, "__stackDetails"], [54, 30, 30, 1], [54, 33, 30, 1, "_e"], [54, 35, 30, 1], [55, 4, 30, 1], [55, 11, 30, 1, "getLockAxis"], [55, 22, 30, 1], [56, 2, 30, 1], [56, 3, 20, 0], [57, 2, 20, 0], [57, 6, 20, 0, "_worklet_14124077705105_init_data"], [57, 39, 20, 0], [58, 4, 20, 0, "code"], [58, 8, 20, 0], [59, 4, 20, 0, "location"], [59, 12, 20, 0], [60, 4, 20, 0, "sourceMap"], [60, 13, 20, 0], [61, 4, 20, 0, "version"], [61, 11, 20, 0], [62, 2, 20, 0], [63, 2, 20, 0], [63, 6, 20, 0, "finishScreenTransition"], [63, 28, 20, 0], [63, 31, 20, 0, "exports"], [63, 38, 20, 0], [63, 39, 20, 0, "finishScreenTransition"], [63, 61, 20, 0], [63, 64, 32, 7], [64, 4, 32, 7], [64, 8, 32, 7, "_e"], [64, 10, 32, 7], [64, 18, 32, 7, "global"], [64, 24, 32, 7], [64, 25, 32, 7, "Error"], [64, 30, 32, 7], [65, 4, 32, 7], [65, 8, 32, 7, "finishScreenTransition"], [65, 30, 32, 7], [65, 42, 32, 7, "finishScreenTransition"], [65, 43, 33, 2, "screenTransitionConfig"], [65, 65, 33, 48], [65, 67, 34, 2], [66, 6, 36, 2], [66, 10, 36, 10, "stackTag"], [66, 18, 36, 18], [66, 21, 36, 51, "screenTransitionConfig"], [66, 43, 36, 73], [66, 44, 36, 10, "stackTag"], [66, 52, 36, 18], [67, 8, 36, 20, "sharedEvent"], [67, 19, 36, 31], [67, 22, 36, 51, "screenTransitionConfig"], [67, 44, 36, 73], [67, 45, 36, 20, "sharedEvent"], [67, 56, 36, 31], [68, 8, 36, 33, "goBackGesture"], [68, 21, 36, 46], [68, 24, 36, 51, "screenTransitionConfig"], [68, 46, 36, 73], [68, 47, 36, 33, "goBackGesture"], [68, 60, 36, 46], [69, 6, 37, 2, "sharedEvent"], [69, 17, 37, 13], [69, 18, 37, 14, "removeListener"], [69, 32, 37, 28], [69, 33, 37, 29, "stackTag"], [69, 41, 37, 37], [69, 42, 37, 38], [70, 6, 38, 2], [70, 10, 38, 8, "lockAxis"], [70, 18, 38, 16], [70, 21, 38, 19, "getLockAxis"], [70, 32, 38, 30], [70, 33, 38, 31, "goBackGesture"], [70, 46, 38, 44], [70, 47, 38, 45], [71, 6, 39, 2], [71, 10, 39, 8, "step"], [71, 14, 39, 12], [71, 17, 39, 15], [71, 21, 39, 15, "getSwipeSimulator"], [71, 54, 39, 32], [71, 56, 40, 4, "sharedEvent"], [71, 67, 40, 15], [71, 68, 40, 16, "value"], [71, 73, 40, 21], [71, 75, 41, 4, "screenTransitionConfig"], [71, 97, 41, 26], [71, 99, 42, 4, "lockAxis"], [71, 107, 43, 2], [71, 108, 43, 3], [72, 6, 44, 2, "step"], [72, 10, 44, 6], [72, 11, 44, 7], [72, 12, 44, 8], [73, 4, 45, 0], [73, 5, 45, 1], [74, 4, 45, 1, "finishScreenTransition"], [74, 26, 45, 1], [74, 27, 45, 1, "__closure"], [74, 36, 45, 1], [75, 6, 45, 1, "getLockAxis"], [75, 17, 45, 1], [76, 6, 45, 1, "getSwipeSimulator"], [76, 23, 45, 1], [76, 25, 39, 15, "getSwipeSimulator"], [77, 4, 39, 32], [78, 4, 39, 32, "finishScreenTransition"], [78, 26, 39, 32], [78, 27, 39, 32, "__workletHash"], [78, 40, 39, 32], [79, 4, 39, 32, "finishScreenTransition"], [79, 26, 39, 32], [79, 27, 39, 32, "__initData"], [79, 37, 39, 32], [79, 40, 39, 32, "_worklet_14124077705105_init_data"], [79, 73, 39, 32], [80, 4, 39, 32, "finishScreenTransition"], [80, 26, 39, 32], [80, 27, 39, 32, "__stackDetails"], [80, 41, 39, 32], [80, 44, 39, 32, "_e"], [80, 46, 39, 32], [81, 4, 39, 32], [81, 11, 39, 32, "finishScreenTransition"], [81, 33, 39, 32], [82, 2, 39, 32], [82, 3, 32, 7], [83, 0, 32, 7], [83, 3]], "functionMap": {"names": ["<global>", "startScreenTransition", "sharedEvent.addListener$argument_1", "getLockAxis", "finishScreenTransition"], "mappings": "AAA;OCS;oCCK;GDE;CDC;AGE;CHU;OIE;CJa"}}, "type": "js/module"}]}