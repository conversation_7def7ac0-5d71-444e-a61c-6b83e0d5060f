{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 42}, "end": {"line": 7, "column": 22, "index": 128}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 130}, "end": {"line": 8, "column": 62, "index": 192}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo/vector-icons", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 194}, "end": {"line": 9, "column": 46, "index": 240}}], "key": "ow7vkrqkIckRjlSi/+MhMmRYtUE=", "exportNames": ["*"]}}, {"name": "../components/FooterNavigation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 242}, "end": {"line": 10, "column": 62, "index": 304}}], "key": "i0wsjvqzeC9AyK9fcghZiqZFu6Y=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = NotificationsScreen;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[4], \"react-native-safe-area-context\");\n  var _vectorIcons = require(_dependencyMap[5], \"@expo/vector-icons\");\n  var _FooterNavigation = _interopRequireDefault(require(_dependencyMap[6], \"../components/FooterNavigation\"));\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\apps\\\\mobile\\\\src\\\\screens\\\\NotificationsScreen.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function NotificationsScreen(_ref) {\n    _s();\n    var navigation = _ref.navigation;\n    var _useState = (0, _react.useState)([{\n        id: '1',\n        type: 'order',\n        title: 'Order Delivered!',\n        message: 'Your order from Pizza Corner has been delivered. Enjoy your meal!',\n        time: '5 minutes ago',\n        read: false,\n        icon: 'checkmark-circle',\n        iconColor: '#10b981'\n      }, {\n        id: '2',\n        type: 'promotion',\n        title: '20% Off Today!',\n        message: 'Get 20% off on all orders from Burger Palace. Use code SAVE20.',\n        time: '1 hour ago',\n        read: false,\n        icon: 'pricetag',\n        iconColor: '#f3a823'\n      }, {\n        id: '3',\n        type: 'order',\n        title: 'Order Confirmed',\n        message: 'Your order from Sushi Express has been confirmed and is being prepared.',\n        time: '2 hours ago',\n        read: true,\n        icon: 'restaurant',\n        iconColor: '#6366f1'\n      }, {\n        id: '4',\n        type: 'system',\n        title: 'New Restaurant Added',\n        message: 'Green Bowl is now available in your area. Check out their healthy options!',\n        time: '1 day ago',\n        read: true,\n        icon: 'storefront',\n        iconColor: '#8b5cf6'\n      }, {\n        id: '5',\n        type: 'order',\n        title: 'Order Cancelled',\n        message: 'Your order from Thai Garden has been cancelled. Refund will be processed within 3-5 business days.',\n        time: '2 days ago',\n        read: true,\n        icon: 'close-circle',\n        iconColor: '#ef4444'\n      }, {\n        id: '6',\n        type: 'promotion',\n        title: 'Free Delivery Weekend',\n        message: 'Enjoy free delivery on all orders this weekend. No minimum order required!',\n        time: '3 days ago',\n        read: true,\n        icon: 'bicycle',\n        iconColor: '#10b981'\n      }]),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      notifications = _useState2[0],\n      setNotifications = _useState2[1];\n    var markAsRead = id => {\n      setNotifications(notifications.map(notif => notif.id === id ? {\n        ...notif,\n        read: true\n      } : notif));\n    };\n    var markAllAsRead = () => {\n      setNotifications(notifications.map(notif => ({\n        ...notif,\n        read: true\n      })));\n    };\n    var unreadCount = notifications.filter(notif => !notif.read).length;\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: {\n        flex: 1,\n        backgroundColor: '#f9fafb'\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f3a823'\n        },\n        edges: ['top']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: {\n          flex: 1,\n          backgroundColor: '#f9fafb'\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            flexDirection: 'row',\n            alignItems: 'center',\n            paddingHorizontal: 16,\n            paddingVertical: 12,\n            backgroundColor: '#fff',\n            borderBottomWidth: 1,\n            borderBottomColor: '#e5e7eb'\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n            onPress: () => navigation.goBack(),\n            style: {\n              padding: 8,\n              marginRight: 8\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n              name: \"arrow-back\",\n              size: 24,\n              color: \"#111827\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 9\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 24,\n              fontWeight: 'bold',\n              color: '#111827',\n              flex: 1\n            },\n            children: \"Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 9\n          }, this), unreadCount > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n            onPress: markAllAsRead,\n            style: {\n              backgroundColor: '#f3a823',\n              paddingHorizontal: 12,\n              paddingVertical: 6,\n              borderRadius: 16\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                color: '#fff',\n                fontWeight: '600',\n                fontSize: 12\n              },\n              children: \"Mark all read\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 9\n        }, this), notifications.length === 0 ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            flex: 1,\n            justifyContent: 'center',\n            alignItems: 'center',\n            paddingHorizontal: 32\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              width: 120,\n              height: 120,\n              backgroundColor: '#f3f4f6',\n              borderRadius: 60,\n              alignItems: 'center',\n              justifyContent: 'center',\n              marginBottom: 24\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n              name: \"notifications-outline\",\n              size: 60,\n              color: \"#9ca3af\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 24,\n              fontWeight: 'bold',\n              color: '#111827',\n              marginBottom: 8,\n              textAlign: 'center'\n            },\n            children: \"No notifications yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              color: '#6b7280',\n              textAlign: 'center',\n              lineHeight: 20\n            },\n            children: \"We'll notify you about order updates, promotions, and more!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 9\n        }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.ScrollView, {\n          style: {\n            flex: 1\n          },\n          children: [unreadCount > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              backgroundColor: '#fef3c7',\n              paddingHorizontal: 16,\n              paddingVertical: 12,\n              borderBottomWidth: 1,\n              borderBottomColor: '#fde68a'\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                color: '#92400e',\n                fontSize: 14,\n                fontWeight: '600'\n              },\n              children: [\"You have \", unreadCount, \" unread notification\", unreadCount !== 1 ? 's' : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              padding: 16\n            },\n            children: notifications.map(notification => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              onPress: () => markAsRead(notification.id),\n              style: {\n                backgroundColor: notification.read ? '#fff' : '#f0f9ff',\n                borderRadius: 12,\n                marginBottom: 12,\n                padding: 16,\n                borderLeftWidth: 4,\n                borderLeftColor: notification.read ? '#e5e7eb' : '#f3a823',\n                shadowColor: '#000',\n                shadowOffset: {\n                  width: 0,\n                  height: 1\n                },\n                shadowOpacity: 0.05,\n                shadowRadius: 2,\n                elevation: 2\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'flex-start'\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                  style: {\n                    width: 40,\n                    height: 40,\n                    borderRadius: 20,\n                    backgroundColor: `${notification.iconColor}20`,\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    marginRight: 12\n                  },\n                  children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_vectorIcons.Ionicons, {\n                    name: notification.icon,\n                    size: 20,\n                    color: notification.iconColor\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                  style: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                    style: {\n                      flexDirection: 'row',\n                      justifyContent: 'space-between',\n                      alignItems: 'flex-start',\n                      marginBottom: 4\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                      style: {\n                        fontSize: 16,\n                        fontWeight: notification.read ? '600' : 'bold',\n                        color: '#111827',\n                        flex: 1\n                      },\n                      children: notification.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 23\n                    }, this), !notification.read && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n                      style: {\n                        width: 8,\n                        height: 8,\n                        borderRadius: 4,\n                        backgroundColor: '#f97316',\n                        marginLeft: 8,\n                        marginTop: 4\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                    style: {\n                      color: '#6b7280',\n                      fontSize: 14,\n                      lineHeight: 20,\n                      marginBottom: 8\n                    },\n                    children: notification.message\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                    style: {\n                      color: '#9ca3af',\n                      fontSize: 12,\n                      fontWeight: '500'\n                    },\n                    children: notification.time\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this)\n            }, notification.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              height: 100\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_FooterNavigation.default, {\n        navigation: navigation,\n        activeScreen: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNativeSafeAreaContext.SafeAreaView, {\n        style: {\n          backgroundColor: '#f9fafb'\n        },\n        edges: ['bottom']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 5\n    }, this);\n  }\n  _s(NotificationsScreen, \"nP+h1FWA0k+3VufkARp8o3lgQxo=\");\n  _c = NotificationsScreen;\n  var _c;\n  $RefreshReg$(_c, \"NotificationsScreen\");\n});", "lineCount": 446, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireWildcard"], [8, 38, 1, 0], [8, 39, 1, 0, "require"], [8, 46, 1, 0], [8, 47, 1, 0, "_dependencyMap"], [8, 61, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 8, 0], [10, 6, 8, 0, "_reactNativeSafeAreaContext"], [10, 33, 8, 0], [10, 36, 8, 0, "require"], [10, 43, 8, 0], [10, 44, 8, 0, "_dependencyMap"], [10, 58, 8, 0], [11, 2, 9, 0], [11, 6, 9, 0, "_vectorIcons"], [11, 18, 9, 0], [11, 21, 9, 0, "require"], [11, 28, 9, 0], [11, 29, 9, 0, "_dependencyMap"], [11, 43, 9, 0], [12, 2, 10, 0], [12, 6, 10, 0, "_FooterNavigation"], [12, 23, 10, 0], [12, 26, 10, 0, "_interopRequireDefault"], [12, 48, 10, 0], [12, 49, 10, 0, "require"], [12, 56, 10, 0], [12, 57, 10, 0, "_dependencyMap"], [12, 71, 10, 0], [13, 2, 10, 62], [13, 6, 10, 62, "_jsxDevRuntime"], [13, 20, 10, 62], [13, 23, 10, 62, "require"], [13, 30, 10, 62], [13, 31, 10, 62, "_dependencyMap"], [13, 45, 10, 62], [14, 2, 10, 62], [14, 6, 10, 62, "_jsxFileName"], [14, 18, 10, 62], [15, 4, 10, 62, "_s"], [15, 6, 10, 62], [15, 9, 10, 62, "$RefreshSig$"], [15, 21, 10, 62], [16, 2, 10, 62], [16, 11, 10, 62, "_interopRequireWildcard"], [16, 35, 10, 62, "e"], [16, 36, 10, 62], [16, 38, 10, 62, "t"], [16, 39, 10, 62], [16, 68, 10, 62, "WeakMap"], [16, 75, 10, 62], [16, 81, 10, 62, "r"], [16, 82, 10, 62], [16, 89, 10, 62, "WeakMap"], [16, 96, 10, 62], [16, 100, 10, 62, "n"], [16, 101, 10, 62], [16, 108, 10, 62, "WeakMap"], [16, 115, 10, 62], [16, 127, 10, 62, "_interopRequireWildcard"], [16, 150, 10, 62], [16, 162, 10, 62, "_interopRequireWildcard"], [16, 163, 10, 62, "e"], [16, 164, 10, 62], [16, 166, 10, 62, "t"], [16, 167, 10, 62], [16, 176, 10, 62, "t"], [16, 177, 10, 62], [16, 181, 10, 62, "e"], [16, 182, 10, 62], [16, 186, 10, 62, "e"], [16, 187, 10, 62], [16, 188, 10, 62, "__esModule"], [16, 198, 10, 62], [16, 207, 10, 62, "e"], [16, 208, 10, 62], [16, 214, 10, 62, "o"], [16, 215, 10, 62], [16, 217, 10, 62, "i"], [16, 218, 10, 62], [16, 220, 10, 62, "f"], [16, 221, 10, 62], [16, 226, 10, 62, "__proto__"], [16, 235, 10, 62], [16, 243, 10, 62, "default"], [16, 250, 10, 62], [16, 252, 10, 62, "e"], [16, 253, 10, 62], [16, 270, 10, 62, "e"], [16, 271, 10, 62], [16, 294, 10, 62, "e"], [16, 295, 10, 62], [16, 320, 10, 62, "e"], [16, 321, 10, 62], [16, 330, 10, 62, "f"], [16, 331, 10, 62], [16, 337, 10, 62, "o"], [16, 338, 10, 62], [16, 341, 10, 62, "t"], [16, 342, 10, 62], [16, 345, 10, 62, "n"], [16, 346, 10, 62], [16, 349, 10, 62, "r"], [16, 350, 10, 62], [16, 358, 10, 62, "o"], [16, 359, 10, 62], [16, 360, 10, 62, "has"], [16, 363, 10, 62], [16, 364, 10, 62, "e"], [16, 365, 10, 62], [16, 375, 10, 62, "o"], [16, 376, 10, 62], [16, 377, 10, 62, "get"], [16, 380, 10, 62], [16, 381, 10, 62, "e"], [16, 382, 10, 62], [16, 385, 10, 62, "o"], [16, 386, 10, 62], [16, 387, 10, 62, "set"], [16, 390, 10, 62], [16, 391, 10, 62, "e"], [16, 392, 10, 62], [16, 394, 10, 62, "f"], [16, 395, 10, 62], [16, 409, 10, 62, "_t"], [16, 411, 10, 62], [16, 415, 10, 62, "e"], [16, 416, 10, 62], [16, 432, 10, 62, "_t"], [16, 434, 10, 62], [16, 441, 10, 62, "hasOwnProperty"], [16, 455, 10, 62], [16, 456, 10, 62, "call"], [16, 460, 10, 62], [16, 461, 10, 62, "e"], [16, 462, 10, 62], [16, 464, 10, 62, "_t"], [16, 466, 10, 62], [16, 473, 10, 62, "i"], [16, 474, 10, 62], [16, 478, 10, 62, "o"], [16, 479, 10, 62], [16, 482, 10, 62, "Object"], [16, 488, 10, 62], [16, 489, 10, 62, "defineProperty"], [16, 503, 10, 62], [16, 508, 10, 62, "Object"], [16, 514, 10, 62], [16, 515, 10, 62, "getOwnPropertyDescriptor"], [16, 539, 10, 62], [16, 540, 10, 62, "e"], [16, 541, 10, 62], [16, 543, 10, 62, "_t"], [16, 545, 10, 62], [16, 552, 10, 62, "i"], [16, 553, 10, 62], [16, 554, 10, 62, "get"], [16, 557, 10, 62], [16, 561, 10, 62, "i"], [16, 562, 10, 62], [16, 563, 10, 62, "set"], [16, 566, 10, 62], [16, 570, 10, 62, "o"], [16, 571, 10, 62], [16, 572, 10, 62, "f"], [16, 573, 10, 62], [16, 575, 10, 62, "_t"], [16, 577, 10, 62], [16, 579, 10, 62, "i"], [16, 580, 10, 62], [16, 584, 10, 62, "f"], [16, 585, 10, 62], [16, 586, 10, 62, "_t"], [16, 588, 10, 62], [16, 592, 10, 62, "e"], [16, 593, 10, 62], [16, 594, 10, 62, "_t"], [16, 596, 10, 62], [16, 607, 10, 62, "f"], [16, 608, 10, 62], [16, 613, 10, 62, "e"], [16, 614, 10, 62], [16, 616, 10, 62, "t"], [16, 617, 10, 62], [17, 2, 12, 15], [17, 11, 12, 24, "NotificationsScreen"], [17, 30, 12, 43, "NotificationsScreen"], [17, 31, 12, 43, "_ref"], [17, 35, 12, 43], [17, 37, 12, 65], [18, 4, 12, 65, "_s"], [18, 6, 12, 65], [19, 4, 12, 65], [19, 8, 12, 46, "navigation"], [19, 18, 12, 56], [19, 21, 12, 56, "_ref"], [19, 25, 12, 56], [19, 26, 12, 46, "navigation"], [19, 36, 12, 56], [20, 4, 13, 2], [20, 8, 13, 2, "_useState"], [20, 17, 13, 2], [20, 20, 13, 44], [20, 24, 13, 44, "useState"], [20, 39, 13, 52], [20, 41, 13, 53], [20, 42, 14, 4], [21, 8, 15, 6, "id"], [21, 10, 15, 8], [21, 12, 15, 10], [21, 15, 15, 13], [22, 8, 16, 6, "type"], [22, 12, 16, 10], [22, 14, 16, 12], [22, 21, 16, 19], [23, 8, 17, 6, "title"], [23, 13, 17, 11], [23, 15, 17, 13], [23, 33, 17, 31], [24, 8, 18, 6, "message"], [24, 15, 18, 13], [24, 17, 18, 15], [24, 84, 18, 82], [25, 8, 19, 6, "time"], [25, 12, 19, 10], [25, 14, 19, 12], [25, 29, 19, 27], [26, 8, 20, 6, "read"], [26, 12, 20, 10], [26, 14, 20, 12], [26, 19, 20, 17], [27, 8, 21, 6, "icon"], [27, 12, 21, 10], [27, 14, 21, 12], [27, 32, 21, 30], [28, 8, 22, 6, "iconColor"], [28, 17, 22, 15], [28, 19, 22, 17], [29, 6, 23, 4], [29, 7, 23, 5], [29, 9, 24, 4], [30, 8, 25, 6, "id"], [30, 10, 25, 8], [30, 12, 25, 10], [30, 15, 25, 13], [31, 8, 26, 6, "type"], [31, 12, 26, 10], [31, 14, 26, 12], [31, 25, 26, 23], [32, 8, 27, 6, "title"], [32, 13, 27, 11], [32, 15, 27, 13], [32, 31, 27, 29], [33, 8, 28, 6, "message"], [33, 15, 28, 13], [33, 17, 28, 15], [33, 81, 28, 79], [34, 8, 29, 6, "time"], [34, 12, 29, 10], [34, 14, 29, 12], [34, 26, 29, 24], [35, 8, 30, 6, "read"], [35, 12, 30, 10], [35, 14, 30, 12], [35, 19, 30, 17], [36, 8, 31, 6, "icon"], [36, 12, 31, 10], [36, 14, 31, 12], [36, 24, 31, 22], [37, 8, 32, 6, "iconColor"], [37, 17, 32, 15], [37, 19, 32, 17], [38, 6, 33, 4], [38, 7, 33, 5], [38, 9, 34, 4], [39, 8, 35, 6, "id"], [39, 10, 35, 8], [39, 12, 35, 10], [39, 15, 35, 13], [40, 8, 36, 6, "type"], [40, 12, 36, 10], [40, 14, 36, 12], [40, 21, 36, 19], [41, 8, 37, 6, "title"], [41, 13, 37, 11], [41, 15, 37, 13], [41, 32, 37, 30], [42, 8, 38, 6, "message"], [42, 15, 38, 13], [42, 17, 38, 15], [42, 90, 38, 88], [43, 8, 39, 6, "time"], [43, 12, 39, 10], [43, 14, 39, 12], [43, 27, 39, 25], [44, 8, 40, 6, "read"], [44, 12, 40, 10], [44, 14, 40, 12], [44, 18, 40, 16], [45, 8, 41, 6, "icon"], [45, 12, 41, 10], [45, 14, 41, 12], [45, 26, 41, 24], [46, 8, 42, 6, "iconColor"], [46, 17, 42, 15], [46, 19, 42, 17], [47, 6, 43, 4], [47, 7, 43, 5], [47, 9, 44, 4], [48, 8, 45, 6, "id"], [48, 10, 45, 8], [48, 12, 45, 10], [48, 15, 45, 13], [49, 8, 46, 6, "type"], [49, 12, 46, 10], [49, 14, 46, 12], [49, 22, 46, 20], [50, 8, 47, 6, "title"], [50, 13, 47, 11], [50, 15, 47, 13], [50, 37, 47, 35], [51, 8, 48, 6, "message"], [51, 15, 48, 13], [51, 17, 48, 15], [51, 93, 48, 91], [52, 8, 49, 6, "time"], [52, 12, 49, 10], [52, 14, 49, 12], [52, 25, 49, 23], [53, 8, 50, 6, "read"], [53, 12, 50, 10], [53, 14, 50, 12], [53, 18, 50, 16], [54, 8, 51, 6, "icon"], [54, 12, 51, 10], [54, 14, 51, 12], [54, 26, 51, 24], [55, 8, 52, 6, "iconColor"], [55, 17, 52, 15], [55, 19, 52, 17], [56, 6, 53, 4], [56, 7, 53, 5], [56, 9, 54, 4], [57, 8, 55, 6, "id"], [57, 10, 55, 8], [57, 12, 55, 10], [57, 15, 55, 13], [58, 8, 56, 6, "type"], [58, 12, 56, 10], [58, 14, 56, 12], [58, 21, 56, 19], [59, 8, 57, 6, "title"], [59, 13, 57, 11], [59, 15, 57, 13], [59, 32, 57, 30], [60, 8, 58, 6, "message"], [60, 15, 58, 13], [60, 17, 58, 15], [60, 117, 58, 115], [61, 8, 59, 6, "time"], [61, 12, 59, 10], [61, 14, 59, 12], [61, 26, 59, 24], [62, 8, 60, 6, "read"], [62, 12, 60, 10], [62, 14, 60, 12], [62, 18, 60, 16], [63, 8, 61, 6, "icon"], [63, 12, 61, 10], [63, 14, 61, 12], [63, 28, 61, 26], [64, 8, 62, 6, "iconColor"], [64, 17, 62, 15], [64, 19, 62, 17], [65, 6, 63, 4], [65, 7, 63, 5], [65, 9, 64, 4], [66, 8, 65, 6, "id"], [66, 10, 65, 8], [66, 12, 65, 10], [66, 15, 65, 13], [67, 8, 66, 6, "type"], [67, 12, 66, 10], [67, 14, 66, 12], [67, 25, 66, 23], [68, 8, 67, 6, "title"], [68, 13, 67, 11], [68, 15, 67, 13], [68, 38, 67, 36], [69, 8, 68, 6, "message"], [69, 15, 68, 13], [69, 17, 68, 15], [69, 93, 68, 91], [70, 8, 69, 6, "time"], [70, 12, 69, 10], [70, 14, 69, 12], [70, 26, 69, 24], [71, 8, 70, 6, "read"], [71, 12, 70, 10], [71, 14, 70, 12], [71, 18, 70, 16], [72, 8, 71, 6, "icon"], [72, 12, 71, 10], [72, 14, 71, 12], [72, 23, 71, 21], [73, 8, 72, 6, "iconColor"], [73, 17, 72, 15], [73, 19, 72, 17], [74, 6, 73, 4], [74, 7, 73, 5], [74, 8, 74, 3], [74, 9, 74, 4], [75, 6, 74, 4, "_useState2"], [75, 16, 74, 4], [75, 23, 74, 4, "_slicedToArray2"], [75, 38, 74, 4], [75, 39, 74, 4, "default"], [75, 46, 74, 4], [75, 48, 74, 4, "_useState"], [75, 57, 74, 4], [76, 6, 13, 9, "notifications"], [76, 19, 13, 22], [76, 22, 13, 22, "_useState2"], [76, 32, 13, 22], [77, 6, 13, 24, "setNotifications"], [77, 22, 13, 40], [77, 25, 13, 40, "_useState2"], [77, 35, 13, 40], [78, 4, 76, 2], [78, 8, 76, 8, "mark<PERSON><PERSON><PERSON>"], [78, 18, 76, 18], [78, 21, 76, 22, "id"], [78, 23, 76, 32], [78, 27, 76, 37], [79, 6, 77, 4, "setNotifications"], [79, 22, 77, 20], [79, 23, 77, 21, "notifications"], [79, 36, 77, 34], [79, 37, 77, 35, "map"], [79, 40, 77, 38], [79, 41, 77, 39, "notif"], [79, 46, 77, 44], [79, 50, 78, 6, "notif"], [79, 55, 78, 11], [79, 56, 78, 12, "id"], [79, 58, 78, 14], [79, 63, 78, 19, "id"], [79, 65, 78, 21], [79, 68, 78, 24], [80, 8, 78, 26], [80, 11, 78, 29, "notif"], [80, 16, 78, 34], [81, 8, 78, 36, "read"], [81, 12, 78, 40], [81, 14, 78, 42], [82, 6, 78, 47], [82, 7, 78, 48], [82, 10, 78, 51, "notif"], [82, 15, 79, 4], [82, 16, 79, 5], [82, 17, 79, 6], [83, 4, 80, 2], [83, 5, 80, 3], [84, 4, 82, 2], [84, 8, 82, 8, "markAllAsRead"], [84, 21, 82, 21], [84, 24, 82, 24, "markAllAsRead"], [84, 25, 82, 24], [84, 30, 82, 30], [85, 6, 83, 4, "setNotifications"], [85, 22, 83, 20], [85, 23, 83, 21, "notifications"], [85, 36, 83, 34], [85, 37, 83, 35, "map"], [85, 40, 83, 38], [85, 41, 83, 39, "notif"], [85, 46, 83, 44], [85, 51, 83, 49], [86, 8, 83, 51], [86, 11, 83, 54, "notif"], [86, 16, 83, 59], [87, 8, 83, 61, "read"], [87, 12, 83, 65], [87, 14, 83, 67], [88, 6, 83, 72], [88, 7, 83, 73], [88, 8, 83, 74], [88, 9, 83, 75], [88, 10, 83, 76], [89, 4, 84, 2], [89, 5, 84, 3], [90, 4, 86, 2], [90, 8, 86, 8, "unreadCount"], [90, 19, 86, 19], [90, 22, 86, 22, "notifications"], [90, 35, 86, 35], [90, 36, 86, 36, "filter"], [90, 42, 86, 42], [90, 43, 86, 43, "notif"], [90, 48, 86, 48], [90, 52, 86, 52], [90, 53, 86, 53, "notif"], [90, 58, 86, 58], [90, 59, 86, 59, "read"], [90, 63, 86, 63], [90, 64, 86, 64], [90, 65, 86, 65, "length"], [90, 71, 86, 71], [91, 4, 88, 2], [91, 24, 89, 4], [91, 28, 89, 4, "_jsxDevRuntime"], [91, 42, 89, 4], [91, 43, 89, 4, "jsxDEV"], [91, 49, 89, 4], [91, 51, 89, 5, "_reactNative"], [91, 63, 89, 5], [91, 64, 89, 5, "View"], [91, 68, 89, 9], [92, 6, 89, 10, "style"], [92, 11, 89, 15], [92, 13, 89, 17], [93, 8, 89, 19, "flex"], [93, 12, 89, 23], [93, 14, 89, 25], [93, 15, 89, 26], [94, 8, 89, 28, "backgroundColor"], [94, 23, 89, 43], [94, 25, 89, 45], [95, 6, 89, 55], [95, 7, 89, 57], [96, 6, 89, 57, "children"], [96, 14, 89, 57], [96, 30, 91, 6], [96, 34, 91, 6, "_jsxDevRuntime"], [96, 48, 91, 6], [96, 49, 91, 6, "jsxDEV"], [96, 55, 91, 6], [96, 57, 91, 7, "_reactNativeSafeAreaContext"], [96, 84, 91, 7], [96, 85, 91, 7, "SafeAreaView"], [96, 97, 91, 19], [97, 8, 91, 20, "style"], [97, 13, 91, 25], [97, 15, 91, 27], [98, 10, 91, 29, "backgroundColor"], [98, 25, 91, 44], [98, 27, 91, 46], [99, 8, 91, 56], [99, 9, 91, 58], [100, 8, 91, 59, "edges"], [100, 13, 91, 64], [100, 15, 91, 66], [100, 16, 91, 67], [100, 21, 91, 72], [101, 6, 91, 74], [102, 8, 91, 74, "fileName"], [102, 16, 91, 74], [102, 18, 91, 74, "_jsxFileName"], [102, 30, 91, 74], [103, 8, 91, 74, "lineNumber"], [103, 18, 91, 74], [104, 8, 91, 74, "columnNumber"], [104, 20, 91, 74], [105, 6, 91, 74], [105, 13, 91, 76], [105, 14, 91, 77], [105, 29, 94, 6], [105, 33, 94, 6, "_jsxDevRuntime"], [105, 47, 94, 6], [105, 48, 94, 6, "jsxDEV"], [105, 54, 94, 6], [105, 56, 94, 7, "_reactNative"], [105, 68, 94, 7], [105, 69, 94, 7, "View"], [105, 73, 94, 11], [106, 8, 94, 12, "style"], [106, 13, 94, 17], [106, 15, 94, 19], [107, 10, 94, 21, "flex"], [107, 14, 94, 25], [107, 16, 94, 27], [107, 17, 94, 28], [108, 10, 94, 30, "backgroundColor"], [108, 25, 94, 45], [108, 27, 94, 47], [109, 8, 94, 57], [109, 9, 94, 59], [110, 8, 94, 59, "children"], [110, 16, 94, 59], [110, 32, 96, 8], [110, 36, 96, 8, "_jsxDevRuntime"], [110, 50, 96, 8], [110, 51, 96, 8, "jsxDEV"], [110, 57, 96, 8], [110, 59, 96, 9, "_reactNative"], [110, 71, 96, 9], [110, 72, 96, 9, "View"], [110, 76, 96, 13], [111, 10, 96, 14, "style"], [111, 15, 96, 19], [111, 17, 96, 21], [112, 12, 97, 10, "flexDirection"], [112, 25, 97, 23], [112, 27, 97, 25], [112, 32, 97, 30], [113, 12, 98, 10, "alignItems"], [113, 22, 98, 20], [113, 24, 98, 22], [113, 32, 98, 30], [114, 12, 99, 10, "paddingHorizontal"], [114, 29, 99, 27], [114, 31, 99, 29], [114, 33, 99, 31], [115, 12, 100, 10, "paddingVertical"], [115, 27, 100, 25], [115, 29, 100, 27], [115, 31, 100, 29], [116, 12, 101, 10, "backgroundColor"], [116, 27, 101, 25], [116, 29, 101, 27], [116, 35, 101, 33], [117, 12, 102, 10, "borderBottomWidth"], [117, 29, 102, 27], [117, 31, 102, 29], [117, 32, 102, 30], [118, 12, 103, 10, "borderBottomColor"], [118, 29, 103, 27], [118, 31, 103, 29], [119, 10, 104, 8], [119, 11, 104, 10], [120, 10, 104, 10, "children"], [120, 18, 104, 10], [120, 34, 105, 8], [120, 38, 105, 8, "_jsxDevRuntime"], [120, 52, 105, 8], [120, 53, 105, 8, "jsxDEV"], [120, 59, 105, 8], [120, 61, 105, 9, "_reactNative"], [120, 73, 105, 9], [120, 74, 105, 9, "TouchableOpacity"], [120, 90, 105, 25], [121, 12, 106, 10, "onPress"], [121, 19, 106, 17], [121, 21, 106, 19, "onPress"], [121, 22, 106, 19], [121, 27, 106, 25, "navigation"], [121, 37, 106, 35], [121, 38, 106, 36, "goBack"], [121, 44, 106, 42], [121, 45, 106, 43], [121, 46, 106, 45], [122, 12, 107, 10, "style"], [122, 17, 107, 15], [122, 19, 107, 17], [123, 14, 107, 19, "padding"], [123, 21, 107, 26], [123, 23, 107, 28], [123, 24, 107, 29], [124, 14, 107, 31, "marginRight"], [124, 25, 107, 42], [124, 27, 107, 44], [125, 12, 107, 46], [125, 13, 107, 48], [126, 12, 107, 48, "children"], [126, 20, 107, 48], [126, 35, 109, 10], [126, 39, 109, 10, "_jsxDevRuntime"], [126, 53, 109, 10], [126, 54, 109, 10, "jsxDEV"], [126, 60, 109, 10], [126, 62, 109, 11, "_vectorIcons"], [126, 74, 109, 11], [126, 75, 109, 11, "Ionicons"], [126, 83, 109, 19], [127, 14, 109, 20, "name"], [127, 18, 109, 24], [127, 20, 109, 25], [127, 32, 109, 37], [128, 14, 109, 38, "size"], [128, 18, 109, 42], [128, 20, 109, 44], [128, 22, 109, 47], [129, 14, 109, 48, "color"], [129, 19, 109, 53], [129, 21, 109, 54], [130, 12, 109, 63], [131, 14, 109, 63, "fileName"], [131, 22, 109, 63], [131, 24, 109, 63, "_jsxFileName"], [131, 36, 109, 63], [132, 14, 109, 63, "lineNumber"], [132, 24, 109, 63], [133, 14, 109, 63, "columnNumber"], [133, 26, 109, 63], [134, 12, 109, 63], [134, 19, 109, 65], [135, 10, 109, 66], [136, 12, 109, 66, "fileName"], [136, 20, 109, 66], [136, 22, 109, 66, "_jsxFileName"], [136, 34, 109, 66], [137, 12, 109, 66, "lineNumber"], [137, 22, 109, 66], [138, 12, 109, 66, "columnNumber"], [138, 24, 109, 66], [139, 10, 109, 66], [139, 17, 110, 26], [139, 18, 110, 27], [139, 33, 111, 8], [139, 37, 111, 8, "_jsxDevRuntime"], [139, 51, 111, 8], [139, 52, 111, 8, "jsxDEV"], [139, 58, 111, 8], [139, 60, 111, 9, "_reactNative"], [139, 72, 111, 9], [139, 73, 111, 9, "Text"], [139, 77, 111, 13], [140, 12, 111, 14, "style"], [140, 17, 111, 19], [140, 19, 111, 21], [141, 14, 111, 23, "fontSize"], [141, 22, 111, 31], [141, 24, 111, 33], [141, 26, 111, 35], [142, 14, 111, 37, "fontWeight"], [142, 24, 111, 47], [142, 26, 111, 49], [142, 32, 111, 55], [143, 14, 111, 57, "color"], [143, 19, 111, 62], [143, 21, 111, 64], [143, 30, 111, 73], [144, 14, 111, 75, "flex"], [144, 18, 111, 79], [144, 20, 111, 81], [145, 12, 111, 83], [145, 13, 111, 85], [146, 12, 111, 85, "children"], [146, 20, 111, 85], [146, 22, 111, 86], [147, 10, 113, 8], [148, 12, 113, 8, "fileName"], [148, 20, 113, 8], [148, 22, 113, 8, "_jsxFileName"], [148, 34, 113, 8], [149, 12, 113, 8, "lineNumber"], [149, 22, 113, 8], [150, 12, 113, 8, "columnNumber"], [150, 24, 113, 8], [151, 10, 113, 8], [151, 17, 113, 14], [151, 18, 113, 15], [151, 20, 114, 9, "unreadCount"], [151, 31, 114, 20], [151, 34, 114, 23], [151, 35, 114, 24], [151, 52, 115, 10], [151, 56, 115, 10, "_jsxDevRuntime"], [151, 70, 115, 10], [151, 71, 115, 10, "jsxDEV"], [151, 77, 115, 10], [151, 79, 115, 11, "_reactNative"], [151, 91, 115, 11], [151, 92, 115, 11, "TouchableOpacity"], [151, 108, 115, 27], [152, 12, 116, 12, "onPress"], [152, 19, 116, 19], [152, 21, 116, 21, "markAllAsRead"], [152, 34, 116, 35], [153, 12, 117, 12, "style"], [153, 17, 117, 17], [153, 19, 117, 19], [154, 14, 118, 14, "backgroundColor"], [154, 29, 118, 29], [154, 31, 118, 31], [154, 40, 118, 40], [155, 14, 119, 14, "paddingHorizontal"], [155, 31, 119, 31], [155, 33, 119, 33], [155, 35, 119, 35], [156, 14, 120, 14, "paddingVertical"], [156, 29, 120, 29], [156, 31, 120, 31], [156, 32, 120, 32], [157, 14, 121, 14, "borderRadius"], [157, 26, 121, 26], [157, 28, 121, 28], [158, 12, 122, 12], [158, 13, 122, 14], [159, 12, 122, 14, "children"], [159, 20, 122, 14], [159, 35, 124, 12], [159, 39, 124, 12, "_jsxDevRuntime"], [159, 53, 124, 12], [159, 54, 124, 12, "jsxDEV"], [159, 60, 124, 12], [159, 62, 124, 13, "_reactNative"], [159, 74, 124, 13], [159, 75, 124, 13, "Text"], [159, 79, 124, 17], [160, 14, 124, 18, "style"], [160, 19, 124, 23], [160, 21, 124, 25], [161, 16, 124, 27, "color"], [161, 21, 124, 32], [161, 23, 124, 34], [161, 29, 124, 40], [162, 16, 124, 42, "fontWeight"], [162, 26, 124, 52], [162, 28, 124, 54], [162, 33, 124, 59], [163, 16, 124, 61, "fontSize"], [163, 24, 124, 69], [163, 26, 124, 71], [164, 14, 124, 74], [164, 15, 124, 76], [165, 14, 124, 76, "children"], [165, 22, 124, 76], [165, 24, 124, 77], [166, 12, 126, 12], [167, 14, 126, 12, "fileName"], [167, 22, 126, 12], [167, 24, 126, 12, "_jsxFileName"], [167, 36, 126, 12], [168, 14, 126, 12, "lineNumber"], [168, 24, 126, 12], [169, 14, 126, 12, "columnNumber"], [169, 26, 126, 12], [170, 12, 126, 12], [170, 19, 126, 18], [171, 10, 126, 19], [172, 12, 126, 19, "fileName"], [172, 20, 126, 19], [172, 22, 126, 19, "_jsxFileName"], [172, 34, 126, 19], [173, 12, 126, 19, "lineNumber"], [173, 22, 126, 19], [174, 12, 126, 19, "columnNumber"], [174, 24, 126, 19], [175, 10, 126, 19], [175, 17, 127, 28], [175, 18, 128, 9], [176, 8, 128, 9], [177, 10, 128, 9, "fileName"], [177, 18, 128, 9], [177, 20, 128, 9, "_jsxFileName"], [177, 32, 128, 9], [178, 10, 128, 9, "lineNumber"], [178, 20, 128, 9], [179, 10, 128, 9, "columnNumber"], [179, 22, 128, 9], [180, 8, 128, 9], [180, 15, 129, 12], [180, 16, 129, 13], [180, 18, 131, 7, "notifications"], [180, 31, 131, 20], [180, 32, 131, 21, "length"], [180, 38, 131, 27], [180, 43, 131, 32], [180, 44, 131, 33], [180, 60, 132, 8], [180, 64, 132, 8, "_jsxDevRuntime"], [180, 78, 132, 8], [180, 79, 132, 8, "jsxDEV"], [180, 85, 132, 8], [180, 87, 132, 9, "_reactNative"], [180, 99, 132, 9], [180, 100, 132, 9, "View"], [180, 104, 132, 13], [181, 10, 132, 14, "style"], [181, 15, 132, 19], [181, 17, 132, 21], [182, 12, 132, 23, "flex"], [182, 16, 132, 27], [182, 18, 132, 29], [182, 19, 132, 30], [183, 12, 132, 32, "justifyContent"], [183, 26, 132, 46], [183, 28, 132, 48], [183, 36, 132, 56], [184, 12, 132, 58, "alignItems"], [184, 22, 132, 68], [184, 24, 132, 70], [184, 32, 132, 78], [185, 12, 132, 80, "paddingHorizontal"], [185, 29, 132, 97], [185, 31, 132, 99], [186, 10, 132, 102], [186, 11, 132, 104], [187, 10, 132, 104, "children"], [187, 18, 132, 104], [187, 34, 133, 10], [187, 38, 133, 10, "_jsxDevRuntime"], [187, 52, 133, 10], [187, 53, 133, 10, "jsxDEV"], [187, 59, 133, 10], [187, 61, 133, 11, "_reactNative"], [187, 73, 133, 11], [187, 74, 133, 11, "View"], [187, 78, 133, 15], [188, 12, 133, 16, "style"], [188, 17, 133, 21], [188, 19, 133, 23], [189, 14, 134, 12, "width"], [189, 19, 134, 17], [189, 21, 134, 19], [189, 24, 134, 22], [190, 14, 135, 12, "height"], [190, 20, 135, 18], [190, 22, 135, 20], [190, 25, 135, 23], [191, 14, 136, 12, "backgroundColor"], [191, 29, 136, 27], [191, 31, 136, 29], [191, 40, 136, 38], [192, 14, 137, 12, "borderRadius"], [192, 26, 137, 24], [192, 28, 137, 26], [192, 30, 137, 28], [193, 14, 138, 12, "alignItems"], [193, 24, 138, 22], [193, 26, 138, 24], [193, 34, 138, 32], [194, 14, 139, 12, "justifyContent"], [194, 28, 139, 26], [194, 30, 139, 28], [194, 38, 139, 36], [195, 14, 140, 12, "marginBottom"], [195, 26, 140, 24], [195, 28, 140, 26], [196, 12, 141, 10], [196, 13, 141, 12], [197, 12, 141, 12, "children"], [197, 20, 141, 12], [197, 35, 142, 12], [197, 39, 142, 12, "_jsxDevRuntime"], [197, 53, 142, 12], [197, 54, 142, 12, "jsxDEV"], [197, 60, 142, 12], [197, 62, 142, 13, "_vectorIcons"], [197, 74, 142, 13], [197, 75, 142, 13, "Ionicons"], [197, 83, 142, 21], [198, 14, 142, 22, "name"], [198, 18, 142, 26], [198, 20, 142, 27], [198, 43, 142, 50], [199, 14, 142, 51, "size"], [199, 18, 142, 55], [199, 20, 142, 57], [199, 22, 142, 60], [200, 14, 142, 61, "color"], [200, 19, 142, 66], [200, 21, 142, 67], [201, 12, 142, 76], [202, 14, 142, 76, "fileName"], [202, 22, 142, 76], [202, 24, 142, 76, "_jsxFileName"], [202, 36, 142, 76], [203, 14, 142, 76, "lineNumber"], [203, 24, 142, 76], [204, 14, 142, 76, "columnNumber"], [204, 26, 142, 76], [205, 12, 142, 76], [205, 19, 142, 78], [206, 10, 142, 79], [207, 12, 142, 79, "fileName"], [207, 20, 142, 79], [207, 22, 142, 79, "_jsxFileName"], [207, 34, 142, 79], [208, 12, 142, 79, "lineNumber"], [208, 22, 142, 79], [209, 12, 142, 79, "columnNumber"], [209, 24, 142, 79], [210, 10, 142, 79], [210, 17, 143, 16], [210, 18, 143, 17], [210, 33, 144, 10], [210, 37, 144, 10, "_jsxDevRuntime"], [210, 51, 144, 10], [210, 52, 144, 10, "jsxDEV"], [210, 58, 144, 10], [210, 60, 144, 11, "_reactNative"], [210, 72, 144, 11], [210, 73, 144, 11, "Text"], [210, 77, 144, 15], [211, 12, 144, 16, "style"], [211, 17, 144, 21], [211, 19, 144, 23], [212, 14, 144, 25, "fontSize"], [212, 22, 144, 33], [212, 24, 144, 35], [212, 26, 144, 37], [213, 14, 144, 39, "fontWeight"], [213, 24, 144, 49], [213, 26, 144, 51], [213, 32, 144, 57], [214, 14, 144, 59, "color"], [214, 19, 144, 64], [214, 21, 144, 66], [214, 30, 144, 75], [215, 14, 144, 77, "marginBottom"], [215, 26, 144, 89], [215, 28, 144, 91], [215, 29, 144, 92], [216, 14, 144, 94, "textAlign"], [216, 23, 144, 103], [216, 25, 144, 105], [217, 12, 144, 114], [217, 13, 144, 116], [218, 12, 144, 116, "children"], [218, 20, 144, 116], [218, 22, 144, 117], [219, 10, 146, 10], [220, 12, 146, 10, "fileName"], [220, 20, 146, 10], [220, 22, 146, 10, "_jsxFileName"], [220, 34, 146, 10], [221, 12, 146, 10, "lineNumber"], [221, 22, 146, 10], [222, 12, 146, 10, "columnNumber"], [222, 24, 146, 10], [223, 10, 146, 10], [223, 17, 146, 16], [223, 18, 146, 17], [223, 33, 147, 10], [223, 37, 147, 10, "_jsxDevRuntime"], [223, 51, 147, 10], [223, 52, 147, 10, "jsxDEV"], [223, 58, 147, 10], [223, 60, 147, 11, "_reactNative"], [223, 72, 147, 11], [223, 73, 147, 11, "Text"], [223, 77, 147, 15], [224, 12, 147, 16, "style"], [224, 17, 147, 21], [224, 19, 147, 23], [225, 14, 147, 25, "color"], [225, 19, 147, 30], [225, 21, 147, 32], [225, 30, 147, 41], [226, 14, 147, 43, "textAlign"], [226, 23, 147, 52], [226, 25, 147, 54], [226, 33, 147, 62], [227, 14, 147, 64, "lineHeight"], [227, 24, 147, 74], [227, 26, 147, 76], [228, 12, 147, 79], [228, 13, 147, 81], [229, 12, 147, 81, "children"], [229, 20, 147, 81], [229, 22, 147, 82], [230, 10, 149, 10], [231, 12, 149, 10, "fileName"], [231, 20, 149, 10], [231, 22, 149, 10, "_jsxFileName"], [231, 34, 149, 10], [232, 12, 149, 10, "lineNumber"], [232, 22, 149, 10], [233, 12, 149, 10, "columnNumber"], [233, 24, 149, 10], [234, 10, 149, 10], [234, 17, 149, 16], [234, 18, 149, 17], [235, 8, 149, 17], [236, 10, 149, 17, "fileName"], [236, 18, 149, 17], [236, 20, 149, 17, "_jsxFileName"], [236, 32, 149, 17], [237, 10, 149, 17, "lineNumber"], [237, 20, 149, 17], [238, 10, 149, 17, "columnNumber"], [238, 22, 149, 17], [239, 8, 149, 17], [239, 15, 150, 14], [239, 16, 150, 15], [239, 32, 152, 8], [239, 36, 152, 8, "_jsxDevRuntime"], [239, 50, 152, 8], [239, 51, 152, 8, "jsxDEV"], [239, 57, 152, 8], [239, 59, 152, 9, "_reactNative"], [239, 71, 152, 9], [239, 72, 152, 9, "ScrollView"], [239, 82, 152, 19], [240, 10, 152, 20, "style"], [240, 15, 152, 25], [240, 17, 152, 27], [241, 12, 152, 29, "flex"], [241, 16, 152, 33], [241, 18, 152, 35], [242, 10, 152, 37], [242, 11, 152, 39], [243, 10, 152, 39, "children"], [243, 18, 152, 39], [243, 21, 154, 11, "unreadCount"], [243, 32, 154, 22], [243, 35, 154, 25], [243, 36, 154, 26], [243, 53, 155, 12], [243, 57, 155, 12, "_jsxDevRuntime"], [243, 71, 155, 12], [243, 72, 155, 12, "jsxDEV"], [243, 78, 155, 12], [243, 80, 155, 13, "_reactNative"], [243, 92, 155, 13], [243, 93, 155, 13, "View"], [243, 97, 155, 17], [244, 12, 155, 18, "style"], [244, 17, 155, 23], [244, 19, 155, 25], [245, 14, 156, 14, "backgroundColor"], [245, 29, 156, 29], [245, 31, 156, 31], [245, 40, 156, 40], [246, 14, 157, 14, "paddingHorizontal"], [246, 31, 157, 31], [246, 33, 157, 33], [246, 35, 157, 35], [247, 14, 158, 14, "paddingVertical"], [247, 29, 158, 29], [247, 31, 158, 31], [247, 33, 158, 33], [248, 14, 159, 14, "borderBottomWidth"], [248, 31, 159, 31], [248, 33, 159, 33], [248, 34, 159, 34], [249, 14, 160, 14, "borderBottomColor"], [249, 31, 160, 31], [249, 33, 160, 33], [250, 12, 161, 12], [250, 13, 161, 14], [251, 12, 161, 14, "children"], [251, 20, 161, 14], [251, 35, 162, 14], [251, 39, 162, 14, "_jsxDevRuntime"], [251, 53, 162, 14], [251, 54, 162, 14, "jsxDEV"], [251, 60, 162, 14], [251, 62, 162, 15, "_reactNative"], [251, 74, 162, 15], [251, 75, 162, 15, "Text"], [251, 79, 162, 19], [252, 14, 162, 20, "style"], [252, 19, 162, 25], [252, 21, 162, 27], [253, 16, 162, 29, "color"], [253, 21, 162, 34], [253, 23, 162, 36], [253, 32, 162, 45], [254, 16, 162, 47, "fontSize"], [254, 24, 162, 55], [254, 26, 162, 57], [254, 28, 162, 59], [255, 16, 162, 61, "fontWeight"], [255, 26, 162, 71], [255, 28, 162, 73], [256, 14, 162, 79], [256, 15, 162, 81], [257, 14, 162, 81, "children"], [257, 22, 162, 81], [257, 25, 162, 82], [257, 36, 163, 25], [257, 38, 163, 26, "unreadCount"], [257, 49, 163, 37], [257, 51, 163, 38], [257, 73, 163, 58], [257, 75, 163, 59, "unreadCount"], [257, 86, 163, 70], [257, 91, 163, 75], [257, 92, 163, 76], [257, 95, 163, 79], [257, 98, 163, 82], [257, 101, 163, 85], [257, 103, 163, 87], [258, 12, 163, 87], [259, 14, 163, 87, "fileName"], [259, 22, 163, 87], [259, 24, 163, 87, "_jsxFileName"], [259, 36, 163, 87], [260, 14, 163, 87, "lineNumber"], [260, 24, 163, 87], [261, 14, 163, 87, "columnNumber"], [261, 26, 163, 87], [262, 12, 163, 87], [262, 19, 164, 20], [263, 10, 164, 21], [264, 12, 164, 21, "fileName"], [264, 20, 164, 21], [264, 22, 164, 21, "_jsxFileName"], [264, 34, 164, 21], [265, 12, 164, 21, "lineNumber"], [265, 22, 164, 21], [266, 12, 164, 21, "columnNumber"], [266, 24, 164, 21], [267, 10, 164, 21], [267, 17, 165, 18], [267, 18, 166, 11], [267, 33, 168, 10], [267, 37, 168, 10, "_jsxDevRuntime"], [267, 51, 168, 10], [267, 52, 168, 10, "jsxDEV"], [267, 58, 168, 10], [267, 60, 168, 11, "_reactNative"], [267, 72, 168, 11], [267, 73, 168, 11, "View"], [267, 77, 168, 15], [268, 12, 168, 16, "style"], [268, 17, 168, 21], [268, 19, 168, 23], [269, 14, 168, 25, "padding"], [269, 21, 168, 32], [269, 23, 168, 34], [270, 12, 168, 37], [270, 13, 168, 39], [271, 12, 168, 39, "children"], [271, 20, 168, 39], [271, 22, 169, 13, "notifications"], [271, 35, 169, 26], [271, 36, 169, 27, "map"], [271, 39, 169, 30], [271, 40, 169, 32, "notification"], [271, 52, 169, 44], [271, 69, 170, 14], [271, 73, 170, 14, "_jsxDevRuntime"], [271, 87, 170, 14], [271, 88, 170, 14, "jsxDEV"], [271, 94, 170, 14], [271, 96, 170, 15, "_reactNative"], [271, 108, 170, 15], [271, 109, 170, 15, "TouchableOpacity"], [271, 125, 170, 31], [272, 14, 172, 16, "onPress"], [272, 21, 172, 23], [272, 23, 172, 25, "onPress"], [272, 24, 172, 25], [272, 29, 172, 31, "mark<PERSON><PERSON><PERSON>"], [272, 39, 172, 41], [272, 40, 172, 42, "notification"], [272, 52, 172, 54], [272, 53, 172, 55, "id"], [272, 55, 172, 57], [272, 56, 172, 59], [273, 14, 173, 16, "style"], [273, 19, 173, 21], [273, 21, 173, 23], [274, 16, 174, 18, "backgroundColor"], [274, 31, 174, 33], [274, 33, 174, 35, "notification"], [274, 45, 174, 47], [274, 46, 174, 48, "read"], [274, 50, 174, 52], [274, 53, 174, 55], [274, 59, 174, 61], [274, 62, 174, 64], [274, 71, 174, 73], [275, 16, 175, 18, "borderRadius"], [275, 28, 175, 30], [275, 30, 175, 32], [275, 32, 175, 34], [276, 16, 176, 18, "marginBottom"], [276, 28, 176, 30], [276, 30, 176, 32], [276, 32, 176, 34], [277, 16, 177, 18, "padding"], [277, 23, 177, 25], [277, 25, 177, 27], [277, 27, 177, 29], [278, 16, 178, 18, "borderLeftWidth"], [278, 31, 178, 33], [278, 33, 178, 35], [278, 34, 178, 36], [279, 16, 179, 18, "borderLeftColor"], [279, 31, 179, 33], [279, 33, 179, 35, "notification"], [279, 45, 179, 47], [279, 46, 179, 48, "read"], [279, 50, 179, 52], [279, 53, 179, 55], [279, 62, 179, 64], [279, 65, 179, 67], [279, 74, 179, 76], [280, 16, 180, 18, "shadowColor"], [280, 27, 180, 29], [280, 29, 180, 31], [280, 35, 180, 37], [281, 16, 181, 18, "shadowOffset"], [281, 28, 181, 30], [281, 30, 181, 32], [282, 18, 181, 34, "width"], [282, 23, 181, 39], [282, 25, 181, 41], [282, 26, 181, 42], [283, 18, 181, 44, "height"], [283, 24, 181, 50], [283, 26, 181, 52], [284, 16, 181, 54], [284, 17, 181, 55], [285, 16, 182, 18, "shadowOpacity"], [285, 29, 182, 31], [285, 31, 182, 33], [285, 35, 182, 37], [286, 16, 183, 18, "shadowRadius"], [286, 28, 183, 30], [286, 30, 183, 32], [286, 31, 183, 33], [287, 16, 184, 18, "elevation"], [287, 25, 184, 27], [287, 27, 184, 29], [288, 14, 185, 16], [288, 15, 185, 18], [289, 14, 185, 18, "children"], [289, 22, 185, 18], [289, 37, 187, 16], [289, 41, 187, 16, "_jsxDevRuntime"], [289, 55, 187, 16], [289, 56, 187, 16, "jsxDEV"], [289, 62, 187, 16], [289, 64, 187, 17, "_reactNative"], [289, 76, 187, 17], [289, 77, 187, 17, "View"], [289, 81, 187, 21], [290, 16, 187, 22, "style"], [290, 21, 187, 27], [290, 23, 187, 29], [291, 18, 187, 31, "flexDirection"], [291, 31, 187, 44], [291, 33, 187, 46], [291, 38, 187, 51], [292, 18, 187, 53, "alignItems"], [292, 28, 187, 63], [292, 30, 187, 65], [293, 16, 187, 78], [293, 17, 187, 80], [294, 16, 187, 80, "children"], [294, 24, 187, 80], [294, 40, 188, 18], [294, 44, 188, 18, "_jsxDevRuntime"], [294, 58, 188, 18], [294, 59, 188, 18, "jsxDEV"], [294, 65, 188, 18], [294, 67, 188, 19, "_reactNative"], [294, 79, 188, 19], [294, 80, 188, 19, "View"], [294, 84, 188, 23], [295, 18, 188, 24, "style"], [295, 23, 188, 29], [295, 25, 188, 31], [296, 20, 189, 20, "width"], [296, 25, 189, 25], [296, 27, 189, 27], [296, 29, 189, 29], [297, 20, 190, 20, "height"], [297, 26, 190, 26], [297, 28, 190, 28], [297, 30, 190, 30], [298, 20, 191, 20, "borderRadius"], [298, 32, 191, 32], [298, 34, 191, 34], [298, 36, 191, 36], [299, 20, 192, 20, "backgroundColor"], [299, 35, 192, 35], [299, 37, 192, 37], [299, 40, 192, 40, "notification"], [299, 52, 192, 52], [299, 53, 192, 53, "iconColor"], [299, 62, 192, 62], [299, 66, 192, 66], [300, 20, 193, 20, "alignItems"], [300, 30, 193, 30], [300, 32, 193, 32], [300, 40, 193, 40], [301, 20, 194, 20, "justifyContent"], [301, 34, 194, 34], [301, 36, 194, 36], [301, 44, 194, 44], [302, 20, 195, 20, "marginRight"], [302, 31, 195, 31], [302, 33, 195, 33], [303, 18, 196, 18], [303, 19, 196, 20], [304, 18, 196, 20, "children"], [304, 26, 196, 20], [304, 41, 197, 20], [304, 45, 197, 20, "_jsxDevRuntime"], [304, 59, 197, 20], [304, 60, 197, 20, "jsxDEV"], [304, 66, 197, 20], [304, 68, 197, 21, "_vectorIcons"], [304, 80, 197, 21], [304, 81, 197, 21, "Ionicons"], [304, 89, 197, 29], [305, 20, 198, 22, "name"], [305, 24, 198, 26], [305, 26, 198, 28, "notification"], [305, 38, 198, 40], [305, 39, 198, 41, "icon"], [305, 43, 198, 53], [306, 20, 199, 22, "size"], [306, 24, 199, 26], [306, 26, 199, 28], [306, 28, 199, 31], [307, 20, 200, 22, "color"], [307, 25, 200, 27], [307, 27, 200, 29, "notification"], [307, 39, 200, 41], [307, 40, 200, 42, "iconColor"], [308, 18, 200, 52], [309, 20, 200, 52, "fileName"], [309, 28, 200, 52], [309, 30, 200, 52, "_jsxFileName"], [309, 42, 200, 52], [310, 20, 200, 52, "lineNumber"], [310, 30, 200, 52], [311, 20, 200, 52, "columnNumber"], [311, 32, 200, 52], [312, 18, 200, 52], [312, 25, 201, 21], [313, 16, 201, 22], [314, 18, 201, 22, "fileName"], [314, 26, 201, 22], [314, 28, 201, 22, "_jsxFileName"], [314, 40, 201, 22], [315, 18, 201, 22, "lineNumber"], [315, 28, 201, 22], [316, 18, 201, 22, "columnNumber"], [316, 30, 201, 22], [317, 16, 201, 22], [317, 23, 202, 24], [317, 24, 202, 25], [317, 39, 204, 18], [317, 43, 204, 18, "_jsxDevRuntime"], [317, 57, 204, 18], [317, 58, 204, 18, "jsxDEV"], [317, 64, 204, 18], [317, 66, 204, 19, "_reactNative"], [317, 78, 204, 19], [317, 79, 204, 19, "View"], [317, 83, 204, 23], [318, 18, 204, 24, "style"], [318, 23, 204, 29], [318, 25, 204, 31], [319, 20, 204, 33, "flex"], [319, 24, 204, 37], [319, 26, 204, 39], [320, 18, 204, 41], [320, 19, 204, 43], [321, 18, 204, 43, "children"], [321, 26, 204, 43], [321, 42, 205, 20], [321, 46, 205, 20, "_jsxDevRuntime"], [321, 60, 205, 20], [321, 61, 205, 20, "jsxDEV"], [321, 67, 205, 20], [321, 69, 205, 21, "_reactNative"], [321, 81, 205, 21], [321, 82, 205, 21, "View"], [321, 86, 205, 25], [322, 20, 205, 26, "style"], [322, 25, 205, 31], [322, 27, 205, 33], [323, 22, 205, 35, "flexDirection"], [323, 35, 205, 48], [323, 37, 205, 50], [323, 42, 205, 55], [324, 22, 205, 57, "justifyContent"], [324, 36, 205, 71], [324, 38, 205, 73], [324, 53, 205, 88], [325, 22, 205, 90, "alignItems"], [325, 32, 205, 100], [325, 34, 205, 102], [325, 46, 205, 114], [326, 22, 205, 116, "marginBottom"], [326, 34, 205, 128], [326, 36, 205, 130], [327, 20, 205, 132], [327, 21, 205, 134], [328, 20, 205, 134, "children"], [328, 28, 205, 134], [328, 44, 206, 22], [328, 48, 206, 22, "_jsxDevRuntime"], [328, 62, 206, 22], [328, 63, 206, 22, "jsxDEV"], [328, 69, 206, 22], [328, 71, 206, 23, "_reactNative"], [328, 83, 206, 23], [328, 84, 206, 23, "Text"], [328, 88, 206, 27], [329, 22, 206, 28, "style"], [329, 27, 206, 33], [329, 29, 206, 35], [330, 24, 207, 24, "fontSize"], [330, 32, 207, 32], [330, 34, 207, 34], [330, 36, 207, 36], [331, 24, 208, 24, "fontWeight"], [331, 34, 208, 34], [331, 36, 208, 36, "notification"], [331, 48, 208, 48], [331, 49, 208, 49, "read"], [331, 53, 208, 53], [331, 56, 208, 56], [331, 61, 208, 61], [331, 64, 208, 64], [331, 70, 208, 70], [332, 24, 209, 24, "color"], [332, 29, 209, 29], [332, 31, 209, 31], [332, 40, 209, 40], [333, 24, 210, 24, "flex"], [333, 28, 210, 28], [333, 30, 210, 30], [334, 22, 211, 22], [334, 23, 211, 24], [335, 22, 211, 24, "children"], [335, 30, 211, 24], [335, 32, 212, 25, "notification"], [335, 44, 212, 37], [335, 45, 212, 38, "title"], [336, 20, 212, 43], [337, 22, 212, 43, "fileName"], [337, 30, 212, 43], [337, 32, 212, 43, "_jsxFileName"], [337, 44, 212, 43], [338, 22, 212, 43, "lineNumber"], [338, 32, 212, 43], [339, 22, 212, 43, "columnNumber"], [339, 34, 212, 43], [340, 20, 212, 43], [340, 27, 213, 28], [340, 28, 213, 29], [340, 30, 214, 23], [340, 31, 214, 24, "notification"], [340, 43, 214, 36], [340, 44, 214, 37, "read"], [340, 48, 214, 41], [340, 65, 215, 24], [340, 69, 215, 24, "_jsxDevRuntime"], [340, 83, 215, 24], [340, 84, 215, 24, "jsxDEV"], [340, 90, 215, 24], [340, 92, 215, 25, "_reactNative"], [340, 104, 215, 25], [340, 105, 215, 25, "View"], [340, 109, 215, 29], [341, 22, 215, 30, "style"], [341, 27, 215, 35], [341, 29, 215, 37], [342, 24, 216, 26, "width"], [342, 29, 216, 31], [342, 31, 216, 33], [342, 32, 216, 34], [343, 24, 217, 26, "height"], [343, 30, 217, 32], [343, 32, 217, 34], [343, 33, 217, 35], [344, 24, 218, 26, "borderRadius"], [344, 36, 218, 38], [344, 38, 218, 40], [344, 39, 218, 41], [345, 24, 219, 26, "backgroundColor"], [345, 39, 219, 41], [345, 41, 219, 43], [345, 50, 219, 52], [346, 24, 220, 26, "marginLeft"], [346, 34, 220, 36], [346, 36, 220, 38], [346, 37, 220, 39], [347, 24, 221, 26, "marginTop"], [347, 33, 221, 35], [347, 35, 221, 37], [348, 22, 222, 24], [349, 20, 222, 26], [350, 22, 222, 26, "fileName"], [350, 30, 222, 26], [350, 32, 222, 26, "_jsxFileName"], [350, 44, 222, 26], [351, 22, 222, 26, "lineNumber"], [351, 32, 222, 26], [352, 22, 222, 26, "columnNumber"], [352, 34, 222, 26], [353, 20, 222, 26], [353, 27, 222, 28], [353, 28, 223, 23], [354, 18, 223, 23], [355, 20, 223, 23, "fileName"], [355, 28, 223, 23], [355, 30, 223, 23, "_jsxFileName"], [355, 42, 223, 23], [356, 20, 223, 23, "lineNumber"], [356, 30, 223, 23], [357, 20, 223, 23, "columnNumber"], [357, 32, 223, 23], [358, 18, 223, 23], [358, 25, 224, 26], [358, 26, 224, 27], [358, 41, 226, 20], [358, 45, 226, 20, "_jsxDevRuntime"], [358, 59, 226, 20], [358, 60, 226, 20, "jsxDEV"], [358, 66, 226, 20], [358, 68, 226, 21, "_reactNative"], [358, 80, 226, 21], [358, 81, 226, 21, "Text"], [358, 85, 226, 25], [359, 20, 226, 26, "style"], [359, 25, 226, 31], [359, 27, 226, 33], [360, 22, 227, 22, "color"], [360, 27, 227, 27], [360, 29, 227, 29], [360, 38, 227, 38], [361, 22, 228, 22, "fontSize"], [361, 30, 228, 30], [361, 32, 228, 32], [361, 34, 228, 34], [362, 22, 229, 22, "lineHeight"], [362, 32, 229, 32], [362, 34, 229, 34], [362, 36, 229, 36], [363, 22, 230, 22, "marginBottom"], [363, 34, 230, 34], [363, 36, 230, 36], [364, 20, 231, 20], [364, 21, 231, 22], [365, 20, 231, 22, "children"], [365, 28, 231, 22], [365, 30, 232, 23, "notification"], [365, 42, 232, 35], [365, 43, 232, 36, "message"], [366, 18, 232, 43], [367, 20, 232, 43, "fileName"], [367, 28, 232, 43], [367, 30, 232, 43, "_jsxFileName"], [367, 42, 232, 43], [368, 20, 232, 43, "lineNumber"], [368, 30, 232, 43], [369, 20, 232, 43, "columnNumber"], [369, 32, 232, 43], [370, 18, 232, 43], [370, 25, 233, 26], [370, 26, 233, 27], [370, 41, 235, 20], [370, 45, 235, 20, "_jsxDevRuntime"], [370, 59, 235, 20], [370, 60, 235, 20, "jsxDEV"], [370, 66, 235, 20], [370, 68, 235, 21, "_reactNative"], [370, 80, 235, 21], [370, 81, 235, 21, "Text"], [370, 85, 235, 25], [371, 20, 235, 26, "style"], [371, 25, 235, 31], [371, 27, 235, 33], [372, 22, 236, 22, "color"], [372, 27, 236, 27], [372, 29, 236, 29], [372, 38, 236, 38], [373, 22, 237, 22, "fontSize"], [373, 30, 237, 30], [373, 32, 237, 32], [373, 34, 237, 34], [374, 22, 238, 22, "fontWeight"], [374, 32, 238, 32], [374, 34, 238, 34], [375, 20, 239, 20], [375, 21, 239, 22], [376, 20, 239, 22, "children"], [376, 28, 239, 22], [376, 30, 240, 23, "notification"], [376, 42, 240, 35], [376, 43, 240, 36, "time"], [377, 18, 240, 40], [378, 20, 240, 40, "fileName"], [378, 28, 240, 40], [378, 30, 240, 40, "_jsxFileName"], [378, 42, 240, 40], [379, 20, 240, 40, "lineNumber"], [379, 30, 240, 40], [380, 20, 240, 40, "columnNumber"], [380, 32, 240, 40], [381, 18, 240, 40], [381, 25, 241, 26], [381, 26, 241, 27], [382, 16, 241, 27], [383, 18, 241, 27, "fileName"], [383, 26, 241, 27], [383, 28, 241, 27, "_jsxFileName"], [383, 40, 241, 27], [384, 18, 241, 27, "lineNumber"], [384, 28, 241, 27], [385, 18, 241, 27, "columnNumber"], [385, 30, 241, 27], [386, 16, 241, 27], [386, 23, 242, 24], [386, 24, 242, 25], [387, 14, 242, 25], [388, 16, 242, 25, "fileName"], [388, 24, 242, 25], [388, 26, 242, 25, "_jsxFileName"], [388, 38, 242, 25], [389, 16, 242, 25, "lineNumber"], [389, 26, 242, 25], [390, 16, 242, 25, "columnNumber"], [390, 28, 242, 25], [391, 14, 242, 25], [391, 21, 243, 22], [392, 12, 243, 23], [392, 15, 171, 21, "notification"], [392, 27, 171, 33], [392, 28, 171, 34, "id"], [392, 30, 171, 36], [393, 14, 171, 36, "fileName"], [393, 22, 171, 36], [393, 24, 171, 36, "_jsxFileName"], [393, 36, 171, 36], [394, 14, 171, 36, "lineNumber"], [394, 24, 171, 36], [395, 14, 171, 36, "columnNumber"], [395, 26, 171, 36], [396, 12, 171, 36], [396, 19, 244, 32], [396, 20, 245, 13], [397, 10, 245, 14], [398, 12, 245, 14, "fileName"], [398, 20, 245, 14], [398, 22, 245, 14, "_jsxFileName"], [398, 34, 245, 14], [399, 12, 245, 14, "lineNumber"], [399, 22, 245, 14], [400, 12, 245, 14, "columnNumber"], [400, 24, 245, 14], [401, 10, 245, 14], [401, 17, 246, 16], [401, 18, 246, 17], [401, 33, 248, 10], [401, 37, 248, 10, "_jsxDevRuntime"], [401, 51, 248, 10], [401, 52, 248, 10, "jsxDEV"], [401, 58, 248, 10], [401, 60, 248, 11, "_reactNative"], [401, 72, 248, 11], [401, 73, 248, 11, "View"], [401, 77, 248, 15], [402, 12, 248, 16, "style"], [402, 17, 248, 21], [402, 19, 248, 23], [403, 14, 248, 25, "height"], [403, 20, 248, 31], [403, 22, 248, 33], [404, 12, 248, 37], [405, 10, 248, 39], [406, 12, 248, 39, "fileName"], [406, 20, 248, 39], [406, 22, 248, 39, "_jsxFileName"], [406, 34, 248, 39], [407, 12, 248, 39, "lineNumber"], [407, 22, 248, 39], [408, 12, 248, 39, "columnNumber"], [408, 24, 248, 39], [409, 10, 248, 39], [409, 17, 248, 41], [409, 18, 248, 42], [410, 8, 248, 42], [411, 10, 248, 42, "fileName"], [411, 18, 248, 42], [411, 20, 248, 42, "_jsxFileName"], [411, 32, 248, 42], [412, 10, 248, 42, "lineNumber"], [412, 20, 248, 42], [413, 10, 248, 42, "columnNumber"], [413, 22, 248, 42], [414, 8, 248, 42], [414, 15, 249, 22], [414, 16, 250, 9], [415, 6, 250, 9], [416, 8, 250, 9, "fileName"], [416, 16, 250, 9], [416, 18, 250, 9, "_jsxFileName"], [416, 30, 250, 9], [417, 8, 250, 9, "lineNumber"], [417, 18, 250, 9], [418, 8, 250, 9, "columnNumber"], [418, 20, 250, 9], [419, 6, 250, 9], [419, 13, 251, 12], [419, 14, 251, 13], [419, 29, 254, 6], [419, 33, 254, 6, "_jsxDevRuntime"], [419, 47, 254, 6], [419, 48, 254, 6, "jsxDEV"], [419, 54, 254, 6], [419, 56, 254, 7, "_FooterNavigation"], [419, 73, 254, 7], [419, 74, 254, 7, "default"], [419, 81, 254, 23], [420, 8, 254, 24, "navigation"], [420, 18, 254, 34], [420, 20, 254, 36, "navigation"], [420, 30, 254, 47], [421, 8, 254, 48, "activeScreen"], [421, 20, 254, 60], [421, 22, 254, 61], [422, 6, 254, 67], [423, 8, 254, 67, "fileName"], [423, 16, 254, 67], [423, 18, 254, 67, "_jsxFileName"], [423, 30, 254, 67], [424, 8, 254, 67, "lineNumber"], [424, 18, 254, 67], [425, 8, 254, 67, "columnNumber"], [425, 20, 254, 67], [426, 6, 254, 67], [426, 13, 254, 69], [426, 14, 254, 70], [426, 29, 257, 6], [426, 33, 257, 6, "_jsxDevRuntime"], [426, 47, 257, 6], [426, 48, 257, 6, "jsxDEV"], [426, 54, 257, 6], [426, 56, 257, 7, "_reactNativeSafeAreaContext"], [426, 83, 257, 7], [426, 84, 257, 7, "SafeAreaView"], [426, 96, 257, 19], [427, 8, 257, 20, "style"], [427, 13, 257, 25], [427, 15, 257, 27], [428, 10, 257, 29, "backgroundColor"], [428, 25, 257, 44], [428, 27, 257, 46], [429, 8, 257, 56], [429, 9, 257, 58], [430, 8, 257, 59, "edges"], [430, 13, 257, 64], [430, 15, 257, 66], [430, 16, 257, 67], [430, 24, 257, 75], [431, 6, 257, 77], [432, 8, 257, 77, "fileName"], [432, 16, 257, 77], [432, 18, 257, 77, "_jsxFileName"], [432, 30, 257, 77], [433, 8, 257, 77, "lineNumber"], [433, 18, 257, 77], [434, 8, 257, 77, "columnNumber"], [434, 20, 257, 77], [435, 6, 257, 77], [435, 13, 257, 79], [435, 14, 257, 80], [436, 4, 257, 80], [437, 6, 257, 80, "fileName"], [437, 14, 257, 80], [437, 16, 257, 80, "_jsxFileName"], [437, 28, 257, 80], [438, 6, 257, 80, "lineNumber"], [438, 16, 257, 80], [439, 6, 257, 80, "columnNumber"], [439, 18, 257, 80], [440, 4, 257, 80], [440, 11, 258, 10], [440, 12, 258, 11], [441, 2, 260, 0], [442, 2, 260, 1, "_s"], [442, 4, 260, 1], [442, 5, 12, 24, "NotificationsScreen"], [442, 24, 12, 43], [443, 2, 12, 43, "_c"], [443, 4, 12, 43], [443, 7, 12, 24, "NotificationsScreen"], [443, 26, 12, 43], [444, 2, 12, 43], [444, 6, 12, 43, "_c"], [444, 8, 12, 43], [445, 2, 12, 43, "$RefreshReg$"], [445, 14, 12, 43], [445, 15, 12, 43, "_c"], [445, 17, 12, 43], [446, 0, 12, 43], [446, 3]], "functionMap": {"names": ["<global>", "NotificationsScreen", "mark<PERSON><PERSON><PERSON>", "notifications.map$argument_0", "markAllAsRead", "notifications.filter$argument_0", "TouchableOpacity.props.onPress"], "mappings": "AAA;eCW;qBCgE;uCCC;wDDC;GDE;wBGE;uCDC,mCC;GHC;2CIE,oBJ;mBKoB,yBL;+BE+D;yBGG,iCH;aFyE;CDe"}}, "type": "js/module"}]}