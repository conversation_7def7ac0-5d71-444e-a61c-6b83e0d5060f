{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 45}}], "key": "WyqnBhspP5BAR0xvCwqfBv/v4uA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ConditionallyIgnoredEventHandlers = ConditionallyIgnoredEventHandlers;\n  exports.DynamicallyInjectedByGestureHandler = DynamicallyInjectedByGestureHandler;\n  exports.isIgnored = isIgnored;\n  var _Platform = _interopRequireDefault(require(_dependencyMap[1], \"../Utilities/Platform\"));\n  var ignoredViewConfigProps = new WeakSet();\n  function DynamicallyInjectedByGestureHandler(object) {\n    ignoredViewConfigProps.add(object);\n    return object;\n  }\n  function ConditionallyIgnoredEventHandlers(value) {\n    if (_Platform.default.OS === 'ios') {\n      return value;\n    }\n    return undefined;\n  }\n  function isIgnored(value) {\n    if (typeof value === 'object' && value != null) {\n      return ignoredViewConfigProps.has(value);\n    }\n    return false;\n  }\n});", "lineCount": 27, "map": [[9, 2, 11, 0], [9, 6, 11, 0, "_Platform"], [9, 15, 11, 0], [9, 18, 11, 0, "_interopRequireDefault"], [9, 40, 11, 0], [9, 41, 11, 0, "require"], [9, 48, 11, 0], [9, 49, 11, 0, "_dependencyMap"], [9, 63, 11, 0], [10, 2, 13, 0], [10, 6, 13, 6, "ignoredViewConfigProps"], [10, 28, 13, 28], [10, 31, 13, 31], [10, 35, 13, 35, "WeakSet"], [10, 42, 13, 42], [10, 43, 13, 50], [10, 44, 13, 51], [11, 2, 19, 7], [11, 11, 19, 16, "DynamicallyInjectedByGestureHandler"], [11, 46, 19, 51, "DynamicallyInjectedByGestureHandler"], [11, 47, 19, 62, "object"], [11, 53, 19, 71], [11, 55, 19, 76], [12, 4, 20, 2, "ignoredViewConfigProps"], [12, 26, 20, 24], [12, 27, 20, 25, "add"], [12, 30, 20, 28], [12, 31, 20, 29, "object"], [12, 37, 20, 35], [12, 38, 20, 36], [13, 4, 21, 2], [13, 11, 21, 9, "object"], [13, 17, 21, 15], [14, 2, 22, 0], [15, 2, 37, 7], [15, 11, 37, 16, "ConditionallyIgnoredEventHandlers"], [15, 44, 37, 49, "ConditionallyIgnoredEventHandlers"], [15, 45, 38, 2, "value"], [15, 50, 38, 10], [15, 52, 39, 12], [16, 4, 40, 2], [16, 8, 40, 6, "Platform"], [16, 25, 40, 14], [16, 26, 40, 15, "OS"], [16, 28, 40, 17], [16, 33, 40, 22], [16, 38, 40, 27], [16, 40, 40, 29], [17, 6, 41, 4], [17, 13, 41, 11, "value"], [17, 18, 41, 16], [18, 4, 42, 2], [19, 4, 43, 2], [19, 11, 43, 9, "undefined"], [19, 20, 43, 18], [20, 2, 44, 0], [21, 2, 46, 7], [21, 11, 46, 16, "isIgnored"], [21, 20, 46, 25, "isIgnored"], [21, 21, 46, 26, "value"], [21, 26, 46, 38], [21, 28, 46, 49], [22, 4, 47, 2], [22, 8, 47, 6], [22, 15, 47, 13, "value"], [22, 20, 47, 18], [22, 25, 47, 23], [22, 33, 47, 31], [22, 37, 47, 35, "value"], [22, 42, 47, 40], [22, 46, 47, 44], [22, 50, 47, 48], [22, 52, 47, 50], [23, 6, 48, 4], [23, 13, 48, 11, "ignoredViewConfigProps"], [23, 35, 48, 33], [23, 36, 48, 34, "has"], [23, 39, 48, 37], [23, 40, 48, 38, "value"], [23, 45, 48, 43], [23, 46, 48, 44], [24, 4, 49, 2], [25, 4, 50, 2], [25, 11, 50, 9], [25, 16, 50, 14], [26, 2, 51, 0], [27, 0, 51, 1], [27, 3]], "functionMap": {"names": ["<global>", "DynamicallyInjectedByGestureHandler", "ConditionallyIgnoredEventHandlers", "isIgnored"], "mappings": "AAA;OCkB;CDG;OEe;CFO;OGE"}}, "type": "js/module"}]}