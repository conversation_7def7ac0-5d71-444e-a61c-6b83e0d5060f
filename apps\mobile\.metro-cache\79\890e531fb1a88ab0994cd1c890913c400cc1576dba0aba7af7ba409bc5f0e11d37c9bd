{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./isRecordEqual.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 51, "index": 98}}], "key": "E5lU3h554k94C0V5puhR219BWnU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.CHILD_STATE = void 0;\n  exports.useRouteCache = useRouteCache;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _isRecordEqual = require(_dependencyMap[3], \"./isRecordEqual.js\");\n  var _excluded = [\"state\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Utilities such as `getFocusedRouteNameFromRoute` need to access state.\n   * So we need a way to suppress the warning for those use cases.\n   * This is fine since they are internal utilities and this is not public API.\n   */\n  var CHILD_STATE = exports.CHILD_STATE = Symbol('CHILD_STATE');\n\n  /**\n   * Hook to cache route props for each screen in the navigator.\n   * This lets add warnings and modifications to the route object but keep references between renders.\n   */\n  function useRouteCache(routes) {\n    // Cache object which holds route objects for each screen\n    var cache = React.useMemo(() => ({\n      current: new Map()\n    }), []);\n    if (process.env.NODE_ENV === 'production') {\n      // We don't want the overhead of creating extra maps every render in prod\n      return routes;\n    }\n    cache.current = routes.reduce((acc, route) => {\n      var previous = cache.current.get(route.key);\n      var state = route.state,\n        routeWithoutState = (0, _objectWithoutProperties2.default)(route, _excluded);\n      var proxy;\n      if (previous && (0, _isRecordEqual.isRecordEqual)(previous, routeWithoutState)) {\n        // If a cached route object already exists, reuse it\n        proxy = previous;\n      } else {\n        proxy = routeWithoutState;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        // FIXME: since the state is updated with mutation, the route object cannot be frozen\n        // As a workaround, loop through the object and make the properties readonly\n        for (var key in proxy) {\n          // @ts-expect-error: this is fine since we are looping through the object\n          var value = proxy[key];\n          Object.defineProperty(proxy, key, {\n            enumerable: true,\n            configurable: true,\n            writable: false,\n            value\n          });\n        }\n      }\n      Object.defineProperty(proxy, CHILD_STATE, {\n        enumerable: false,\n        configurable: true,\n        value: state\n      });\n      acc.set(route.key, proxy);\n      return acc;\n    }, new Map());\n    return Array.from(cache.current.values());\n  }\n});", "lineCount": 70, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "CHILD_STATE"], [8, 21, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "useRouteCache"], [9, 23, 1, 13], [9, 26, 1, 13, "useRouteCache"], [9, 39, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_objectWithoutProperties2"], [10, 31, 1, 13], [10, 34, 1, 13, "_interopRequireDefault"], [10, 56, 1, 13], [10, 57, 1, 13, "require"], [10, 64, 1, 13], [10, 65, 1, 13, "_dependencyMap"], [10, 79, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "React"], [11, 11, 3, 0], [11, 14, 3, 0, "_interopRequireWildcard"], [11, 37, 3, 0], [11, 38, 3, 0, "require"], [11, 45, 3, 0], [11, 46, 3, 0, "_dependencyMap"], [11, 60, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_isRecordEqual"], [12, 20, 4, 0], [12, 23, 4, 0, "require"], [12, 30, 4, 0], [12, 31, 4, 0, "_dependencyMap"], [12, 45, 4, 0], [13, 2, 4, 51], [13, 6, 4, 51, "_excluded"], [13, 15, 4, 51], [14, 2, 4, 51], [14, 11, 4, 51, "_interopRequireWildcard"], [14, 35, 4, 51, "e"], [14, 36, 4, 51], [14, 38, 4, 51, "t"], [14, 39, 4, 51], [14, 68, 4, 51, "WeakMap"], [14, 75, 4, 51], [14, 81, 4, 51, "r"], [14, 82, 4, 51], [14, 89, 4, 51, "WeakMap"], [14, 96, 4, 51], [14, 100, 4, 51, "n"], [14, 101, 4, 51], [14, 108, 4, 51, "WeakMap"], [14, 115, 4, 51], [14, 127, 4, 51, "_interopRequireWildcard"], [14, 150, 4, 51], [14, 162, 4, 51, "_interopRequireWildcard"], [14, 163, 4, 51, "e"], [14, 164, 4, 51], [14, 166, 4, 51, "t"], [14, 167, 4, 51], [14, 176, 4, 51, "t"], [14, 177, 4, 51], [14, 181, 4, 51, "e"], [14, 182, 4, 51], [14, 186, 4, 51, "e"], [14, 187, 4, 51], [14, 188, 4, 51, "__esModule"], [14, 198, 4, 51], [14, 207, 4, 51, "e"], [14, 208, 4, 51], [14, 214, 4, 51, "o"], [14, 215, 4, 51], [14, 217, 4, 51, "i"], [14, 218, 4, 51], [14, 220, 4, 51, "f"], [14, 221, 4, 51], [14, 226, 4, 51, "__proto__"], [14, 235, 4, 51], [14, 243, 4, 51, "default"], [14, 250, 4, 51], [14, 252, 4, 51, "e"], [14, 253, 4, 51], [14, 270, 4, 51, "e"], [14, 271, 4, 51], [14, 294, 4, 51, "e"], [14, 295, 4, 51], [14, 320, 4, 51, "e"], [14, 321, 4, 51], [14, 330, 4, 51, "f"], [14, 331, 4, 51], [14, 337, 4, 51, "o"], [14, 338, 4, 51], [14, 341, 4, 51, "t"], [14, 342, 4, 51], [14, 345, 4, 51, "n"], [14, 346, 4, 51], [14, 349, 4, 51, "r"], [14, 350, 4, 51], [14, 358, 4, 51, "o"], [14, 359, 4, 51], [14, 360, 4, 51, "has"], [14, 363, 4, 51], [14, 364, 4, 51, "e"], [14, 365, 4, 51], [14, 375, 4, 51, "o"], [14, 376, 4, 51], [14, 377, 4, 51, "get"], [14, 380, 4, 51], [14, 381, 4, 51, "e"], [14, 382, 4, 51], [14, 385, 4, 51, "o"], [14, 386, 4, 51], [14, 387, 4, 51, "set"], [14, 390, 4, 51], [14, 391, 4, 51, "e"], [14, 392, 4, 51], [14, 394, 4, 51, "f"], [14, 395, 4, 51], [14, 409, 4, 51, "_t"], [14, 411, 4, 51], [14, 415, 4, 51, "e"], [14, 416, 4, 51], [14, 432, 4, 51, "_t"], [14, 434, 4, 51], [14, 441, 4, 51, "hasOwnProperty"], [14, 455, 4, 51], [14, 456, 4, 51, "call"], [14, 460, 4, 51], [14, 461, 4, 51, "e"], [14, 462, 4, 51], [14, 464, 4, 51, "_t"], [14, 466, 4, 51], [14, 473, 4, 51, "i"], [14, 474, 4, 51], [14, 478, 4, 51, "o"], [14, 479, 4, 51], [14, 482, 4, 51, "Object"], [14, 488, 4, 51], [14, 489, 4, 51, "defineProperty"], [14, 503, 4, 51], [14, 508, 4, 51, "Object"], [14, 514, 4, 51], [14, 515, 4, 51, "getOwnPropertyDescriptor"], [14, 539, 4, 51], [14, 540, 4, 51, "e"], [14, 541, 4, 51], [14, 543, 4, 51, "_t"], [14, 545, 4, 51], [14, 552, 4, 51, "i"], [14, 553, 4, 51], [14, 554, 4, 51, "get"], [14, 557, 4, 51], [14, 561, 4, 51, "i"], [14, 562, 4, 51], [14, 563, 4, 51, "set"], [14, 566, 4, 51], [14, 570, 4, 51, "o"], [14, 571, 4, 51], [14, 572, 4, 51, "f"], [14, 573, 4, 51], [14, 575, 4, 51, "_t"], [14, 577, 4, 51], [14, 579, 4, 51, "i"], [14, 580, 4, 51], [14, 584, 4, 51, "f"], [14, 585, 4, 51], [14, 586, 4, 51, "_t"], [14, 588, 4, 51], [14, 592, 4, 51, "e"], [14, 593, 4, 51], [14, 594, 4, 51, "_t"], [14, 596, 4, 51], [14, 607, 4, 51, "f"], [14, 608, 4, 51], [14, 613, 4, 51, "e"], [14, 614, 4, 51], [14, 616, 4, 51, "t"], [14, 617, 4, 51], [15, 2, 5, 0], [16, 0, 6, 0], [17, 0, 7, 0], [18, 0, 8, 0], [19, 0, 9, 0], [20, 2, 10, 7], [20, 6, 10, 13, "CHILD_STATE"], [20, 17, 10, 24], [20, 20, 10, 24, "exports"], [20, 27, 10, 24], [20, 28, 10, 24, "CHILD_STATE"], [20, 39, 10, 24], [20, 42, 10, 27, "Symbol"], [20, 48, 10, 33], [20, 49, 10, 34], [20, 62, 10, 47], [20, 63, 10, 48], [22, 2, 12, 0], [23, 0, 13, 0], [24, 0, 14, 0], [25, 0, 15, 0], [26, 2, 16, 7], [26, 11, 16, 16, "useRouteCache"], [26, 24, 16, 29, "useRouteCache"], [26, 25, 16, 30, "routes"], [26, 31, 16, 36], [26, 33, 16, 38], [27, 4, 17, 2], [28, 4, 18, 2], [28, 8, 18, 8, "cache"], [28, 13, 18, 13], [28, 16, 18, 16, "React"], [28, 21, 18, 21], [28, 22, 18, 22, "useMemo"], [28, 29, 18, 29], [28, 30, 18, 30], [28, 37, 18, 37], [29, 6, 19, 4, "current"], [29, 13, 19, 11], [29, 15, 19, 13], [29, 19, 19, 17, "Map"], [29, 22, 19, 20], [29, 23, 19, 21], [30, 4, 20, 2], [30, 5, 20, 3], [30, 6, 20, 4], [30, 8, 20, 6], [30, 10, 20, 8], [30, 11, 20, 9], [31, 4, 21, 2], [31, 8, 21, 6, "process"], [31, 15, 21, 13], [31, 16, 21, 14, "env"], [31, 19, 21, 17], [31, 20, 21, 18, "NODE_ENV"], [31, 28, 21, 26], [31, 33, 21, 31], [31, 45, 21, 43], [31, 47, 21, 45], [32, 6, 22, 4], [33, 6, 23, 4], [33, 13, 23, 11, "routes"], [33, 19, 23, 17], [34, 4, 24, 2], [35, 4, 25, 2, "cache"], [35, 9, 25, 7], [35, 10, 25, 8, "current"], [35, 17, 25, 15], [35, 20, 25, 18, "routes"], [35, 26, 25, 24], [35, 27, 25, 25, "reduce"], [35, 33, 25, 31], [35, 34, 25, 32], [35, 35, 25, 33, "acc"], [35, 38, 25, 36], [35, 40, 25, 38, "route"], [35, 45, 25, 43], [35, 50, 25, 48], [36, 6, 26, 4], [36, 10, 26, 10, "previous"], [36, 18, 26, 18], [36, 21, 26, 21, "cache"], [36, 26, 26, 26], [36, 27, 26, 27, "current"], [36, 34, 26, 34], [36, 35, 26, 35, "get"], [36, 38, 26, 38], [36, 39, 26, 39, "route"], [36, 44, 26, 44], [36, 45, 26, 45, "key"], [36, 48, 26, 48], [36, 49, 26, 49], [37, 6, 27, 4], [37, 10, 28, 6, "state"], [37, 15, 28, 11], [37, 18, 30, 8, "route"], [37, 23, 30, 13], [37, 24, 28, 6, "state"], [37, 29, 28, 11], [38, 8, 29, 9, "routeWithoutState"], [38, 25, 29, 26], [38, 32, 29, 26, "_objectWithoutProperties2"], [38, 57, 29, 26], [38, 58, 29, 26, "default"], [38, 65, 29, 26], [38, 67, 30, 8, "route"], [38, 72, 30, 13], [38, 74, 30, 13, "_excluded"], [38, 83, 30, 13], [39, 6, 31, 4], [39, 10, 31, 8, "proxy"], [39, 15, 31, 13], [40, 6, 32, 4], [40, 10, 32, 8, "previous"], [40, 18, 32, 16], [40, 22, 32, 20], [40, 26, 32, 20, "isRecordEqual"], [40, 54, 32, 33], [40, 56, 32, 34, "previous"], [40, 64, 32, 42], [40, 66, 32, 44, "routeWithoutState"], [40, 83, 32, 61], [40, 84, 32, 62], [40, 86, 32, 64], [41, 8, 33, 6], [42, 8, 34, 6, "proxy"], [42, 13, 34, 11], [42, 16, 34, 14, "previous"], [42, 24, 34, 22], [43, 6, 35, 4], [43, 7, 35, 5], [43, 13, 35, 11], [44, 8, 36, 6, "proxy"], [44, 13, 36, 11], [44, 16, 36, 14, "routeWithoutState"], [44, 33, 36, 31], [45, 6, 37, 4], [46, 6, 38, 4], [46, 10, 38, 8, "process"], [46, 17, 38, 15], [46, 18, 38, 16, "env"], [46, 21, 38, 19], [46, 22, 38, 20, "NODE_ENV"], [46, 30, 38, 28], [46, 35, 38, 33], [46, 47, 38, 45], [46, 49, 38, 47], [47, 8, 39, 6], [48, 8, 40, 6], [49, 8, 41, 6], [49, 13, 41, 11], [49, 17, 41, 17, "key"], [49, 20, 41, 20], [49, 24, 41, 24, "proxy"], [49, 29, 41, 29], [49, 31, 41, 31], [50, 10, 42, 8], [51, 10, 43, 8], [51, 14, 43, 14, "value"], [51, 19, 43, 19], [51, 22, 43, 22, "proxy"], [51, 27, 43, 27], [51, 28, 43, 28, "key"], [51, 31, 43, 31], [51, 32, 43, 32], [52, 10, 44, 8, "Object"], [52, 16, 44, 14], [52, 17, 44, 15, "defineProperty"], [52, 31, 44, 29], [52, 32, 44, 30, "proxy"], [52, 37, 44, 35], [52, 39, 44, 37, "key"], [52, 42, 44, 40], [52, 44, 44, 42], [53, 12, 45, 10, "enumerable"], [53, 22, 45, 20], [53, 24, 45, 22], [53, 28, 45, 26], [54, 12, 46, 10, "configurable"], [54, 24, 46, 22], [54, 26, 46, 24], [54, 30, 46, 28], [55, 12, 47, 10, "writable"], [55, 20, 47, 18], [55, 22, 47, 20], [55, 27, 47, 25], [56, 12, 48, 10, "value"], [57, 10, 49, 8], [57, 11, 49, 9], [57, 12, 49, 10], [58, 8, 50, 6], [59, 6, 51, 4], [60, 6, 52, 4, "Object"], [60, 12, 52, 10], [60, 13, 52, 11, "defineProperty"], [60, 27, 52, 25], [60, 28, 52, 26, "proxy"], [60, 33, 52, 31], [60, 35, 52, 33, "CHILD_STATE"], [60, 46, 52, 44], [60, 48, 52, 46], [61, 8, 53, 6, "enumerable"], [61, 18, 53, 16], [61, 20, 53, 18], [61, 25, 53, 23], [62, 8, 54, 6, "configurable"], [62, 20, 54, 18], [62, 22, 54, 20], [62, 26, 54, 24], [63, 8, 55, 6, "value"], [63, 13, 55, 11], [63, 15, 55, 13, "state"], [64, 6, 56, 4], [64, 7, 56, 5], [64, 8, 56, 6], [65, 6, 57, 4, "acc"], [65, 9, 57, 7], [65, 10, 57, 8, "set"], [65, 13, 57, 11], [65, 14, 57, 12, "route"], [65, 19, 57, 17], [65, 20, 57, 18, "key"], [65, 23, 57, 21], [65, 25, 57, 23, "proxy"], [65, 30, 57, 28], [65, 31, 57, 29], [66, 6, 58, 4], [66, 13, 58, 11, "acc"], [66, 16, 58, 14], [67, 4, 59, 2], [67, 5, 59, 3], [67, 7, 59, 5], [67, 11, 59, 9, "Map"], [67, 14, 59, 12], [67, 15, 59, 13], [67, 16, 59, 14], [67, 17, 59, 15], [68, 4, 60, 2], [68, 11, 60, 9, "Array"], [68, 16, 60, 14], [68, 17, 60, 15, "from"], [68, 21, 60, 19], [68, 22, 60, 20, "cache"], [68, 27, 60, 25], [68, 28, 60, 26, "current"], [68, 35, 60, 33], [68, 36, 60, 34, "values"], [68, 42, 60, 40], [68, 43, 60, 41], [68, 44, 60, 42], [68, 45, 60, 43], [69, 2, 61, 0], [70, 0, 61, 1], [70, 3]], "functionMap": {"names": ["<global>", "useRouteCache", "React.useMemo$argument_0", "routes.reduce$argument_0"], "mappings": "AAA;OCe;8BCE;IDE;gCEK;GFkC;CDE"}}, "type": "js/module"}]}