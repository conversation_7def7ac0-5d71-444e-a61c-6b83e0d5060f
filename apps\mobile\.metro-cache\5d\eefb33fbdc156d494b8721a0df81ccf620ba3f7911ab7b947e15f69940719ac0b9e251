{"dependencies": [{"name": "./WebSocket_new", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 30}}], "key": "jWQNc3S9t9eNBY4wmpbjYcCPLdA=", "exportNames": ["*"]}}, {"name": "./WebSocket_old", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 30}}], "key": "ZXU5qdSzck1BgiyDCyJcpxW1EhA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var useBuiltInEventTarget = global.RN$useBuiltInEventTarget?.();\n  var _default = exports.default = useBuiltInEventTarget ? require(_dependencyMap[0], \"./WebSocket_new\").default : require(_dependencyMap[1], \"./WebSocket_old\").default;\n});", "lineCount": 8, "map": [[6, 2, 15, 0], [6, 6, 15, 6, "useBuiltInEventTarget"], [6, 27, 15, 27], [6, 30, 15, 30, "global"], [6, 36, 15, 36], [6, 37, 15, 37, "RN$useBuiltInEventTarget"], [6, 61, 15, 61], [6, 64, 15, 64], [6, 65, 15, 65], [7, 2, 15, 66], [7, 6, 15, 66, "_default"], [7, 14, 15, 66], [7, 17, 15, 66, "exports"], [7, 24, 15, 66], [7, 25, 15, 66, "default"], [7, 32, 15, 66], [7, 35, 17, 16, "useBuiltInEventTarget"], [7, 56, 17, 37], [7, 59, 19, 4, "require"], [7, 66, 19, 11], [7, 67, 19, 11, "_dependencyMap"], [7, 81, 19, 11], [7, 103, 19, 29], [7, 104, 19, 30], [7, 105, 19, 31, "default"], [7, 112, 19, 38], [7, 115, 20, 4, "require"], [7, 122, 20, 11], [7, 123, 20, 11, "_dependencyMap"], [7, 137, 20, 11], [7, 159, 20, 29], [7, 160, 20, 30], [7, 161, 20, 31, "default"], [7, 168, 20, 38], [8, 0, 20, 38], [8, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}