{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 93, "index": 108}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 0, "index": 470}, "end": {"line": 14, "column": 73, "index": 543}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/ViewConfigIgnore", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 0, "index": 470}, "end": {"line": 14, "column": 73, "index": 543}}], "key": "IAMNY1s5722b4GYH12DgGSx1R70=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  // eslint-disable-next-line @typescript-eslint/ban-types\n\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var _require = require(_dependencyMap[3], \"react-native/Libraries/NativeComponent/ViewConfigIgnore\"),\n    ConditionallyIgnoredEventHandlers = _require.ConditionallyIgnoredEventHandlers;\n  var nativeComponentName = 'RNSScreenStack';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSScreenStack\",\n    directEventTypes: {\n      topFinishTransitioning: {\n        registrationName: \"onFinishTransitioning\"\n      }\n    },\n    validAttributes: {\n      ...ConditionallyIgnoredEventHandlers({\n        onFinishTransitioning: true\n      })\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 30, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [8, 20, 1, 13, "exports"], [8, 27, 1, 13], [8, 28, 1, 13, "__INTERNAL_VIEW_CONFIG"], [8, 50, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_codegenNativeComponent"], [9, 29, 3, 0], [9, 32, 3, 0, "_interopRequireDefault"], [9, 54, 3, 0], [9, 55, 3, 0, "require"], [9, 62, 3, 0], [9, 63, 3, 0, "_dependencyMap"], [9, 77, 3, 0], [10, 2, 7, 0], [12, 2, 14, 0], [12, 6, 14, 0, "NativeComponentRegistry"], [12, 29, 14, 73], [12, 32, 14, 0, "require"], [12, 39, 14, 73], [12, 40, 14, 73, "_dependencyMap"], [12, 54, 14, 73], [12, 123, 14, 72], [12, 124, 14, 73], [13, 2, 14, 0], [13, 6, 14, 0, "_require"], [13, 14, 14, 0], [13, 17, 14, 0, "require"], [13, 24, 14, 73], [13, 25, 14, 73, "_dependencyMap"], [13, 39, 14, 73], [13, 101, 14, 72], [13, 102, 14, 73], [14, 4, 14, 0, "ConditionallyIgnoredEventHandlers"], [14, 37, 14, 73], [14, 40, 14, 73, "_require"], [14, 48, 14, 73], [14, 49, 14, 0, "ConditionallyIgnoredEventHandlers"], [14, 82, 14, 73], [15, 2, 14, 0], [15, 6, 14, 0, "nativeComponentName"], [15, 25, 14, 73], [15, 28, 14, 0], [15, 44, 14, 73], [16, 2, 14, 0], [16, 6, 14, 0, "__INTERNAL_VIEW_CONFIG"], [16, 28, 14, 73], [16, 31, 14, 73, "exports"], [16, 38, 14, 73], [16, 39, 14, 73, "__INTERNAL_VIEW_CONFIG"], [16, 61, 14, 73], [16, 64, 14, 0], [17, 4, 14, 0, "uiViewClassName"], [17, 19, 14, 73], [17, 21, 14, 0], [17, 37, 14, 73], [18, 4, 14, 0, "directEventTypes"], [18, 20, 14, 73], [18, 22, 14, 0], [19, 6, 14, 0, "topFinishTransitioning"], [19, 28, 14, 73], [19, 30, 14, 0], [20, 8, 14, 0, "registrationName"], [20, 24, 14, 73], [20, 26, 14, 0], [21, 6, 14, 72], [22, 4, 14, 72], [22, 5, 14, 73], [23, 4, 14, 0, "validAttributes"], [23, 19, 14, 73], [23, 21, 14, 0], [24, 6, 14, 0], [24, 9, 14, 0, "ConditionallyIgnoredEventHandlers"], [24, 42, 14, 73], [24, 43, 14, 0], [25, 8, 14, 0, "onFinishTransitioning"], [25, 29, 14, 73], [25, 31, 14, 0], [26, 6, 14, 72], [27, 4, 14, 72], [28, 2, 14, 72], [28, 3, 14, 73], [29, 2, 14, 73], [29, 6, 14, 73, "_default"], [29, 14, 14, 73], [29, 17, 14, 73, "exports"], [29, 24, 14, 73], [29, 25, 14, 73, "default"], [29, 32, 14, 73], [29, 35, 14, 0, "NativeComponentRegistry"], [29, 58, 14, 73], [29, 59, 14, 0, "get"], [29, 62, 14, 73], [29, 63, 14, 0, "nativeComponentName"], [29, 82, 14, 73], [29, 84, 14, 0], [29, 90, 14, 0, "__INTERNAL_VIEW_CONFIG"], [29, 112, 14, 72], [29, 113, 14, 73], [30, 0, 14, 73], [30, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}