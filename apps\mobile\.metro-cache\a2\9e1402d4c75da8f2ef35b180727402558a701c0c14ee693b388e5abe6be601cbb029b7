{"dependencies": [{"name": "../../../Libraries/Animated/NativeAnimatedAllowlist", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 17, "column": 61}}], "key": "1MP4leiTQM7G0GQe8/VPNyS8WAs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.validateInterpolation = validateInterpolation;\n  exports.validateStyles = validateStyles;\n  exports.validateTransform = validateTransform;\n  var _NativeAnimatedAllowlist = require(_dependencyMap[0], \"../../../Libraries/Animated/NativeAnimatedAllowlist\");\n  function validateInterpolation(config) {\n    for (var _key in config) {\n      if (_key !== 'debugID' && !(0, _NativeAnimatedAllowlist.isSupportedInterpolationParam)(_key)) {\n        console.error(`Interpolation property '${_key}' is not supported by native animated module`);\n      }\n    }\n  }\n  function validateStyles(styles) {\n    for (var _key2 in styles) {\n      if (!(0, _NativeAnimatedAllowlist.isSupportedStyleProp)(_key2)) {\n        console.error(`Style property '${_key2}' is not supported by native animated module`);\n      }\n    }\n  }\n  function validateTransform(configs) {\n    configs.forEach(config => {\n      if (!(0, _NativeAnimatedAllowlist.isSupportedTransformProp)(config.property)) {\n        console.error(`Property '${config.property}' is not supported by native animated module`);\n      }\n    });\n  }\n});", "lineCount": 30, "map": [[8, 2, 13, 0], [8, 6, 13, 0, "_NativeAnimatedAllowlist"], [8, 30, 13, 0], [8, 33, 13, 0, "require"], [8, 40, 13, 0], [8, 41, 13, 0, "_dependencyMap"], [8, 55, 13, 0], [9, 2, 19, 7], [9, 11, 19, 16, "validateInterpolation"], [9, 32, 19, 37, "validateInterpolation"], [9, 33, 20, 2, "config"], [9, 39, 20, 42], [9, 41, 21, 8], [10, 4, 22, 2], [10, 9, 22, 7], [10, 13, 22, 13, "key"], [10, 17, 22, 16], [10, 21, 22, 20, "config"], [10, 27, 22, 26], [10, 29, 22, 28], [11, 6, 23, 4], [11, 10, 23, 8, "key"], [11, 14, 23, 11], [11, 19, 23, 16], [11, 28, 23, 25], [11, 32, 23, 29], [11, 33, 23, 30], [11, 37, 23, 30, "isSupportedInterpolationParam"], [11, 91, 23, 59], [11, 93, 23, 60, "key"], [11, 97, 23, 63], [11, 98, 23, 64], [11, 100, 23, 66], [12, 8, 24, 6, "console"], [12, 15, 24, 13], [12, 16, 24, 14, "error"], [12, 21, 24, 19], [12, 22, 25, 8], [12, 49, 25, 35, "key"], [12, 53, 25, 38], [12, 99, 26, 6], [12, 100, 26, 7], [13, 6, 27, 4], [14, 4, 28, 2], [15, 2, 29, 0], [16, 2, 31, 7], [16, 11, 31, 16, "validateStyles"], [16, 25, 31, 30, "validateStyles"], [16, 26, 31, 31, "styles"], [16, 32, 31, 68], [16, 34, 31, 76], [17, 4, 32, 2], [17, 9, 32, 7], [17, 13, 32, 13, "key"], [17, 18, 32, 16], [17, 22, 32, 20, "styles"], [17, 28, 32, 26], [17, 30, 32, 28], [18, 6, 33, 4], [18, 10, 33, 8], [18, 11, 33, 9], [18, 15, 33, 9, "isSupportedStyleProp"], [18, 60, 33, 29], [18, 62, 33, 30, "key"], [18, 67, 33, 33], [18, 68, 33, 34], [18, 70, 33, 36], [19, 8, 34, 6, "console"], [19, 15, 34, 13], [19, 16, 34, 14, "error"], [19, 21, 34, 19], [19, 22, 35, 8], [19, 41, 35, 27, "key"], [19, 46, 35, 30], [19, 92, 36, 6], [19, 93, 36, 7], [20, 6, 37, 4], [21, 4, 38, 2], [22, 2, 39, 0], [23, 2, 41, 7], [23, 11, 41, 16, "validateTransform"], [23, 28, 41, 33, "validateTransform"], [23, 29, 42, 2, "configs"], [23, 36, 55, 3], [23, 38, 56, 8], [24, 4, 57, 2, "configs"], [24, 11, 57, 9], [24, 12, 57, 10, "for<PERSON>ach"], [24, 19, 57, 17], [24, 20, 57, 18, "config"], [24, 26, 57, 24], [24, 30, 57, 28], [25, 6, 58, 4], [25, 10, 58, 8], [25, 11, 58, 9], [25, 15, 58, 9, "isSupportedTransformProp"], [25, 64, 58, 33], [25, 66, 58, 34, "config"], [25, 72, 58, 40], [25, 73, 58, 41, "property"], [25, 81, 58, 49], [25, 82, 58, 50], [25, 84, 58, 52], [26, 8, 59, 6, "console"], [26, 15, 59, 13], [26, 16, 59, 14, "error"], [26, 21, 59, 19], [26, 22, 60, 8], [26, 35, 60, 21, "config"], [26, 41, 60, 27], [26, 42, 60, 28, "property"], [26, 50, 60, 36], [26, 96, 61, 6], [26, 97, 61, 7], [27, 6, 62, 4], [28, 4, 63, 2], [28, 5, 63, 3], [28, 6, 63, 4], [29, 2, 64, 0], [30, 0, 64, 1], [30, 3]], "functionMap": {"names": ["<global>", "validateInterpolation", "validateStyles", "validateTransform", "configs.forEach$argument_0"], "mappings": "AAA;OCkB;CDU;OEE;CFQ;OGE;kBCgB;GDM"}}, "type": "js/module"}]}