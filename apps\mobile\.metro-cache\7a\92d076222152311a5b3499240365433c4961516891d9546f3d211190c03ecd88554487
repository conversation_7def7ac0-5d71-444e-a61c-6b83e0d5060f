{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = LogBoxNotificationCountBadge;\n  var _View = _interopRequireDefault(require(_dependencyMap[1], \"../../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[2], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"../../Text/Text\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[4], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[5], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[6], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxNotificationCountBadge.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function LogBoxNotificationCountBadge(props) {\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.outside,\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: [styles.inside, styles[props.level]],\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          id: \"logbox_notification_count_text\",\n          style: styles.text,\n          children: props.count <= 1 ? '!' : props.count\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _StyleSheet.default.create({\n    warn: {\n      backgroundColor: LogBoxStyle.getWarningColor(1)\n    },\n    error: {\n      backgroundColor: LogBoxStyle.getErrorColor(1)\n    },\n    outside: {\n      padding: 2,\n      borderRadius: 25,\n      backgroundColor: '#fff',\n      marginRight: 8\n    },\n    inside: {\n      minWidth: 18,\n      paddingLeft: 4,\n      paddingRight: 4,\n      borderRadius: 25,\n      fontWeight: '600'\n    },\n    text: {\n      color: LogBoxStyle.getTextColor(1),\n      fontSize: 14,\n      lineHeight: 18,\n      textAlign: 'center',\n      fontWeight: '600',\n      textShadowColor: LogBoxStyle.getBackgroundColor(0.4),\n      textShadowOffset: {\n        width: 0,\n        height: 0\n      },\n      textShadowRadius: 3\n    }\n  });\n});", "lineCount": 74, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_View"], [7, 11, 11, 0], [7, 14, 11, 0, "_interopRequireDefault"], [7, 36, 11, 0], [7, 37, 11, 0, "require"], [7, 44, 11, 0], [7, 45, 11, 0, "_dependencyMap"], [7, 59, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_StyleSheet"], [8, 17, 12, 0], [8, 20, 12, 0, "_interopRequireDefault"], [8, 42, 12, 0], [8, 43, 12, 0, "require"], [8, 50, 12, 0], [8, 51, 12, 0, "_dependencyMap"], [8, 65, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_Text"], [9, 11, 13, 0], [9, 14, 13, 0, "_interopRequireDefault"], [9, 36, 13, 0], [9, 37, 13, 0, "require"], [9, 44, 13, 0], [9, 45, 13, 0, "_dependencyMap"], [9, 59, 13, 0], [10, 2, 14, 0], [10, 6, 14, 0, "LogBoxStyle"], [10, 17, 14, 0], [10, 20, 14, 0, "_interopRequireWildcard"], [10, 43, 14, 0], [10, 44, 14, 0, "require"], [10, 51, 14, 0], [10, 52, 14, 0, "_dependencyMap"], [10, 66, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "React"], [11, 11, 15, 0], [11, 14, 15, 0, "_interopRequireWildcard"], [11, 37, 15, 0], [11, 38, 15, 0, "require"], [11, 45, 15, 0], [11, 46, 15, 0, "_dependencyMap"], [11, 60, 15, 0], [12, 2, 15, 31], [12, 6, 15, 31, "_jsxDevRuntime"], [12, 20, 15, 31], [12, 23, 15, 31, "require"], [12, 30, 15, 31], [12, 31, 15, 31, "_dependencyMap"], [12, 45, 15, 31], [13, 2, 15, 31], [13, 6, 15, 31, "_jsxFileName"], [13, 18, 15, 31], [14, 2, 15, 31], [14, 11, 15, 31, "_interopRequireWildcard"], [14, 35, 15, 31, "e"], [14, 36, 15, 31], [14, 38, 15, 31, "t"], [14, 39, 15, 31], [14, 68, 15, 31, "WeakMap"], [14, 75, 15, 31], [14, 81, 15, 31, "r"], [14, 82, 15, 31], [14, 89, 15, 31, "WeakMap"], [14, 96, 15, 31], [14, 100, 15, 31, "n"], [14, 101, 15, 31], [14, 108, 15, 31, "WeakMap"], [14, 115, 15, 31], [14, 127, 15, 31, "_interopRequireWildcard"], [14, 150, 15, 31], [14, 162, 15, 31, "_interopRequireWildcard"], [14, 163, 15, 31, "e"], [14, 164, 15, 31], [14, 166, 15, 31, "t"], [14, 167, 15, 31], [14, 176, 15, 31, "t"], [14, 177, 15, 31], [14, 181, 15, 31, "e"], [14, 182, 15, 31], [14, 186, 15, 31, "e"], [14, 187, 15, 31], [14, 188, 15, 31, "__esModule"], [14, 198, 15, 31], [14, 207, 15, 31, "e"], [14, 208, 15, 31], [14, 214, 15, 31, "o"], [14, 215, 15, 31], [14, 217, 15, 31, "i"], [14, 218, 15, 31], [14, 220, 15, 31, "f"], [14, 221, 15, 31], [14, 226, 15, 31, "__proto__"], [14, 235, 15, 31], [14, 243, 15, 31, "default"], [14, 250, 15, 31], [14, 252, 15, 31, "e"], [14, 253, 15, 31], [14, 270, 15, 31, "e"], [14, 271, 15, 31], [14, 294, 15, 31, "e"], [14, 295, 15, 31], [14, 320, 15, 31, "e"], [14, 321, 15, 31], [14, 330, 15, 31, "f"], [14, 331, 15, 31], [14, 337, 15, 31, "o"], [14, 338, 15, 31], [14, 341, 15, 31, "t"], [14, 342, 15, 31], [14, 345, 15, 31, "n"], [14, 346, 15, 31], [14, 349, 15, 31, "r"], [14, 350, 15, 31], [14, 358, 15, 31, "o"], [14, 359, 15, 31], [14, 360, 15, 31, "has"], [14, 363, 15, 31], [14, 364, 15, 31, "e"], [14, 365, 15, 31], [14, 375, 15, 31, "o"], [14, 376, 15, 31], [14, 377, 15, 31, "get"], [14, 380, 15, 31], [14, 381, 15, 31, "e"], [14, 382, 15, 31], [14, 385, 15, 31, "o"], [14, 386, 15, 31], [14, 387, 15, 31, "set"], [14, 390, 15, 31], [14, 391, 15, 31, "e"], [14, 392, 15, 31], [14, 394, 15, 31, "f"], [14, 395, 15, 31], [14, 409, 15, 31, "_t"], [14, 411, 15, 31], [14, 415, 15, 31, "e"], [14, 416, 15, 31], [14, 432, 15, 31, "_t"], [14, 434, 15, 31], [14, 441, 15, 31, "hasOwnProperty"], [14, 455, 15, 31], [14, 456, 15, 31, "call"], [14, 460, 15, 31], [14, 461, 15, 31, "e"], [14, 462, 15, 31], [14, 464, 15, 31, "_t"], [14, 466, 15, 31], [14, 473, 15, 31, "i"], [14, 474, 15, 31], [14, 478, 15, 31, "o"], [14, 479, 15, 31], [14, 482, 15, 31, "Object"], [14, 488, 15, 31], [14, 489, 15, 31, "defineProperty"], [14, 503, 15, 31], [14, 508, 15, 31, "Object"], [14, 514, 15, 31], [14, 515, 15, 31, "getOwnPropertyDescriptor"], [14, 539, 15, 31], [14, 540, 15, 31, "e"], [14, 541, 15, 31], [14, 543, 15, 31, "_t"], [14, 545, 15, 31], [14, 552, 15, 31, "i"], [14, 553, 15, 31], [14, 554, 15, 31, "get"], [14, 557, 15, 31], [14, 561, 15, 31, "i"], [14, 562, 15, 31], [14, 563, 15, 31, "set"], [14, 566, 15, 31], [14, 570, 15, 31, "o"], [14, 571, 15, 31], [14, 572, 15, 31, "f"], [14, 573, 15, 31], [14, 575, 15, 31, "_t"], [14, 577, 15, 31], [14, 579, 15, 31, "i"], [14, 580, 15, 31], [14, 584, 15, 31, "f"], [14, 585, 15, 31], [14, 586, 15, 31, "_t"], [14, 588, 15, 31], [14, 592, 15, 31, "e"], [14, 593, 15, 31], [14, 594, 15, 31, "_t"], [14, 596, 15, 31], [14, 607, 15, 31, "f"], [14, 608, 15, 31], [14, 613, 15, 31, "e"], [14, 614, 15, 31], [14, 616, 15, 31, "t"], [14, 617, 15, 31], [15, 2, 17, 15], [15, 11, 17, 24, "LogBoxNotificationCountBadge"], [15, 39, 17, 52, "LogBoxNotificationCountBadge"], [15, 40, 17, 53, "props"], [15, 45, 20, 1], [15, 47, 20, 15], [16, 4, 21, 2], [16, 24, 22, 4], [16, 28, 22, 4, "_jsxDevRuntime"], [16, 42, 22, 4], [16, 43, 22, 4, "jsxDEV"], [16, 49, 22, 4], [16, 51, 22, 5, "_View"], [16, 56, 22, 5], [16, 57, 22, 5, "default"], [16, 64, 22, 9], [17, 6, 22, 10, "style"], [17, 11, 22, 15], [17, 13, 22, 17, "styles"], [17, 19, 22, 23], [17, 20, 22, 24, "outside"], [17, 27, 22, 32], [18, 6, 22, 32, "children"], [18, 14, 22, 32], [18, 29, 26, 6], [18, 33, 26, 6, "_jsxDevRuntime"], [18, 47, 26, 6], [18, 48, 26, 6, "jsxDEV"], [18, 54, 26, 6], [18, 56, 26, 7, "_View"], [18, 61, 26, 7], [18, 62, 26, 7, "default"], [18, 69, 26, 11], [19, 8, 26, 12, "style"], [19, 13, 26, 17], [19, 15, 26, 19], [19, 16, 26, 20, "styles"], [19, 22, 26, 26], [19, 23, 26, 27, "inside"], [19, 29, 26, 33], [19, 31, 26, 35, "styles"], [19, 37, 26, 41], [19, 38, 26, 42, "props"], [19, 43, 26, 47], [19, 44, 26, 48, "level"], [19, 49, 26, 53], [19, 50, 26, 54], [19, 51, 26, 56], [20, 8, 26, 56, "children"], [20, 16, 26, 56], [20, 31, 27, 8], [20, 35, 27, 8, "_jsxDevRuntime"], [20, 49, 27, 8], [20, 50, 27, 8, "jsxDEV"], [20, 56, 27, 8], [20, 58, 27, 9, "_Text"], [20, 63, 27, 9], [20, 64, 27, 9, "default"], [20, 71, 27, 13], [21, 10, 27, 14, "id"], [21, 12, 27, 16], [21, 14, 27, 17], [21, 46, 27, 49], [22, 10, 27, 50, "style"], [22, 15, 27, 55], [22, 17, 27, 57, "styles"], [22, 23, 27, 63], [22, 24, 27, 64, "text"], [22, 28, 27, 69], [23, 10, 27, 69, "children"], [23, 18, 27, 69], [23, 20, 28, 11, "props"], [23, 25, 28, 16], [23, 26, 28, 17, "count"], [23, 31, 28, 22], [23, 35, 28, 26], [23, 36, 28, 27], [23, 39, 28, 30], [23, 42, 28, 33], [23, 45, 28, 36, "props"], [23, 50, 28, 41], [23, 51, 28, 42, "count"], [24, 8, 28, 47], [25, 10, 28, 47, "fileName"], [25, 18, 28, 47], [25, 20, 28, 47, "_jsxFileName"], [25, 32, 28, 47], [26, 10, 28, 47, "lineNumber"], [26, 20, 28, 47], [27, 10, 28, 47, "columnNumber"], [27, 22, 28, 47], [28, 8, 28, 47], [28, 15, 29, 14], [29, 6, 29, 15], [30, 8, 29, 15, "fileName"], [30, 16, 29, 15], [30, 18, 29, 15, "_jsxFileName"], [30, 30, 29, 15], [31, 8, 29, 15, "lineNumber"], [31, 18, 29, 15], [32, 8, 29, 15, "columnNumber"], [32, 20, 29, 15], [33, 6, 29, 15], [33, 13, 30, 12], [34, 4, 30, 13], [35, 6, 30, 13, "fileName"], [35, 14, 30, 13], [35, 16, 30, 13, "_jsxFileName"], [35, 28, 30, 13], [36, 6, 30, 13, "lineNumber"], [36, 16, 30, 13], [37, 6, 30, 13, "columnNumber"], [37, 18, 30, 13], [38, 4, 30, 13], [38, 11, 31, 10], [38, 12, 31, 11], [39, 2, 33, 0], [40, 2, 35, 0], [40, 6, 35, 6, "styles"], [40, 12, 35, 12], [40, 15, 35, 15, "StyleSheet"], [40, 34, 35, 25], [40, 35, 35, 26, "create"], [40, 41, 35, 32], [40, 42, 35, 33], [41, 4, 36, 2, "warn"], [41, 8, 36, 6], [41, 10, 36, 8], [42, 6, 37, 4, "backgroundColor"], [42, 21, 37, 19], [42, 23, 37, 21, "LogBoxStyle"], [42, 34, 37, 32], [42, 35, 37, 33, "getWarningColor"], [42, 50, 37, 48], [42, 51, 37, 49], [42, 52, 37, 50], [43, 4, 38, 2], [43, 5, 38, 3], [44, 4, 39, 2, "error"], [44, 9, 39, 7], [44, 11, 39, 9], [45, 6, 40, 4, "backgroundColor"], [45, 21, 40, 19], [45, 23, 40, 21, "LogBoxStyle"], [45, 34, 40, 32], [45, 35, 40, 33, "getErrorColor"], [45, 48, 40, 46], [45, 49, 40, 47], [45, 50, 40, 48], [46, 4, 41, 2], [46, 5, 41, 3], [47, 4, 42, 2, "outside"], [47, 11, 42, 9], [47, 13, 42, 11], [48, 6, 43, 4, "padding"], [48, 13, 43, 11], [48, 15, 43, 13], [48, 16, 43, 14], [49, 6, 44, 4, "borderRadius"], [49, 18, 44, 16], [49, 20, 44, 18], [49, 22, 44, 20], [50, 6, 45, 4, "backgroundColor"], [50, 21, 45, 19], [50, 23, 45, 21], [50, 29, 45, 27], [51, 6, 46, 4, "marginRight"], [51, 17, 46, 15], [51, 19, 46, 17], [52, 4, 47, 2], [52, 5, 47, 3], [53, 4, 48, 2, "inside"], [53, 10, 48, 8], [53, 12, 48, 10], [54, 6, 49, 4, "min<PERSON><PERSON><PERSON>"], [54, 14, 49, 12], [54, 16, 49, 14], [54, 18, 49, 16], [55, 6, 50, 4, "paddingLeft"], [55, 17, 50, 15], [55, 19, 50, 17], [55, 20, 50, 18], [56, 6, 51, 4, "paddingRight"], [56, 18, 51, 16], [56, 20, 51, 18], [56, 21, 51, 19], [57, 6, 52, 4, "borderRadius"], [57, 18, 52, 16], [57, 20, 52, 18], [57, 22, 52, 20], [58, 6, 53, 4, "fontWeight"], [58, 16, 53, 14], [58, 18, 53, 16], [59, 4, 54, 2], [59, 5, 54, 3], [60, 4, 55, 2, "text"], [60, 8, 55, 6], [60, 10, 55, 8], [61, 6, 56, 4, "color"], [61, 11, 56, 9], [61, 13, 56, 11, "LogBoxStyle"], [61, 24, 56, 22], [61, 25, 56, 23, "getTextColor"], [61, 37, 56, 35], [61, 38, 56, 36], [61, 39, 56, 37], [61, 40, 56, 38], [62, 6, 57, 4, "fontSize"], [62, 14, 57, 12], [62, 16, 57, 14], [62, 18, 57, 16], [63, 6, 58, 4, "lineHeight"], [63, 16, 58, 14], [63, 18, 58, 16], [63, 20, 58, 18], [64, 6, 59, 4, "textAlign"], [64, 15, 59, 13], [64, 17, 59, 15], [64, 25, 59, 23], [65, 6, 60, 4, "fontWeight"], [65, 16, 60, 14], [65, 18, 60, 16], [65, 23, 60, 21], [66, 6, 61, 4, "textShadowColor"], [66, 21, 61, 19], [66, 23, 61, 21, "LogBoxStyle"], [66, 34, 61, 32], [66, 35, 61, 33, "getBackgroundColor"], [66, 53, 61, 51], [66, 54, 61, 52], [66, 57, 61, 55], [66, 58, 61, 56], [67, 6, 62, 4, "textShadowOffset"], [67, 22, 62, 20], [67, 24, 62, 22], [68, 8, 62, 23, "width"], [68, 13, 62, 28], [68, 15, 62, 30], [68, 16, 62, 31], [69, 8, 62, 33, "height"], [69, 14, 62, 39], [69, 16, 62, 41], [70, 6, 62, 42], [70, 7, 62, 43], [71, 6, 63, 4, "textShadowRadius"], [71, 22, 63, 20], [71, 24, 63, 22], [72, 4, 64, 2], [73, 2, 65, 0], [73, 3, 65, 1], [73, 4, 65, 2], [74, 0, 65, 3], [74, 3]], "functionMap": {"names": ["<global>", "LogBoxNotificationCountBadge"], "mappings": "AAA;eCgB;CDgB"}}, "type": "js/module"}]}