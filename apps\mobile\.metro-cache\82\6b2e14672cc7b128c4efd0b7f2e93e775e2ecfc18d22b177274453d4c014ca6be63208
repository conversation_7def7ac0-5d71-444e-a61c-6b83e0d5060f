{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2F..%2F..%2Fnode_modules%2F%40expo%2Fvector-icons%2Fbuild%2Fvendor%2Freact-native-vector-icons%2FFonts\",\n    \"scales\": [1],\n    \"hash\": \"370dd5af19f8364907b6e2c41f45dbbf\",\n    \"name\": \"FontAwesome6_Regular\",\n    \"type\": \"ttf\",\n    \"fileHashes\": [\"370dd5af19f8364907b6e2c41f45dbbf\"]\n  });\n});", "lineCount": 11, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 155, 1, 175], [5, 4, 1, 176], [5, 12, 1, 184], [5, 14, 1, 185], [5, 15, 1, 186], [5, 16, 1, 187], [5, 17, 1, 188], [6, 4, 1, 189], [6, 10, 1, 195], [6, 12, 1, 196], [6, 46, 1, 230], [7, 4, 1, 231], [7, 10, 1, 237], [7, 12, 1, 238], [7, 34, 1, 260], [8, 4, 1, 261], [8, 10, 1, 267], [8, 12, 1, 268], [8, 17, 1, 273], [9, 4, 1, 274], [9, 16, 1, 286], [9, 18, 1, 287], [9, 19, 1, 288], [9, 53, 1, 322], [10, 2, 1, 323], [10, 3, 1, 324], [11, 0, 1, 324], [11, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}