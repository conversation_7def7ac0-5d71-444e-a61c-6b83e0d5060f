{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  function castToNumber(value) {\n    return value ? Number(value) : 0;\n  }\n  var _x = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"x\");\n  var _y = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"y\");\n  var _width = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"width\");\n  var _height = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"height\");\n  var DOMRectReadOnly = exports.default = /*#__PURE__*/function () {\n    function DOMRectReadOnly(x, y, width, height) {\n      (0, _classCallCheck2.default)(this, DOMRectReadOnly);\n      Object.defineProperty(this, _x, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _y, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _width, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _height, {\n        writable: true,\n        value: void 0\n      });\n      this.__setInternalX(x);\n      this.__setInternalY(y);\n      this.__setInternalWidth(width);\n      this.__setInternalHeight(height);\n    }\n    return (0, _createClass2.default)(DOMRectReadOnly, [{\n      key: \"x\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _x)[_x];\n      }\n    }, {\n      key: \"y\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _y)[_y];\n      }\n    }, {\n      key: \"width\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _width)[_width];\n      }\n    }, {\n      key: \"height\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _height)[_height];\n      }\n    }, {\n      key: \"top\",\n      get: function () {\n        var height = (0, _classPrivateFieldLooseBase2.default)(this, _height)[_height];\n        var y = (0, _classPrivateFieldLooseBase2.default)(this, _y)[_y];\n        if (height < 0) {\n          return y + height;\n        }\n        return y;\n      }\n    }, {\n      key: \"right\",\n      get: function () {\n        var width = (0, _classPrivateFieldLooseBase2.default)(this, _width)[_width];\n        var x = (0, _classPrivateFieldLooseBase2.default)(this, _x)[_x];\n        if (width < 0) {\n          return x;\n        }\n        return x + width;\n      }\n    }, {\n      key: \"bottom\",\n      get: function () {\n        var height = (0, _classPrivateFieldLooseBase2.default)(this, _height)[_height];\n        var y = (0, _classPrivateFieldLooseBase2.default)(this, _y)[_y];\n        if (height < 0) {\n          return y;\n        }\n        return y + height;\n      }\n    }, {\n      key: \"left\",\n      get: function () {\n        var width = (0, _classPrivateFieldLooseBase2.default)(this, _width)[_width];\n        var x = (0, _classPrivateFieldLooseBase2.default)(this, _x)[_x];\n        if (width < 0) {\n          return x + width;\n        }\n        return x;\n      }\n    }, {\n      key: \"toJSON\",\n      value: function toJSON() {\n        var x = this.x,\n          y = this.y,\n          width = this.width,\n          height = this.height,\n          top = this.top,\n          left = this.left,\n          bottom = this.bottom,\n          right = this.right;\n        return {\n          x,\n          y,\n          width,\n          height,\n          top,\n          left,\n          bottom,\n          right\n        };\n      }\n    }, {\n      key: \"__getInternalX\",\n      value: function __getInternalX() {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _x)[_x];\n      }\n    }, {\n      key: \"__getInternalY\",\n      value: function __getInternalY() {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _y)[_y];\n      }\n    }, {\n      key: \"__getInternalWidth\",\n      value: function __getInternalWidth() {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _width)[_width];\n      }\n    }, {\n      key: \"__getInternalHeight\",\n      value: function __getInternalHeight() {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _height)[_height];\n      }\n    }, {\n      key: \"__setInternalX\",\n      value: function __setInternalX(x) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _x)[_x] = castToNumber(x);\n      }\n    }, {\n      key: \"__setInternalY\",\n      value: function __setInternalY(y) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _y)[_y] = castToNumber(y);\n      }\n    }, {\n      key: \"__setInternalWidth\",\n      value: function __setInternalWidth(width) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _width)[_width] = castToNumber(width);\n      }\n    }, {\n      key: \"__setInternalHeight\",\n      value: function __setInternalHeight(height) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _height)[_height] = castToNumber(height);\n      }\n    }], [{\n      key: \"fromRect\",\n      value: function fromRect(rect) {\n        if (!rect) {\n          return new DOMRectReadOnly();\n        }\n        return new DOMRectReadOnly(rect.x, rect.y, rect.width, rect.height);\n      }\n    }]);\n  }();\n});", "lineCount": 174, "map": [[11, 2, 26, 0], [11, 11, 26, 9, "castToNumber"], [11, 23, 26, 21, "castToNumber"], [11, 24, 26, 22, "value"], [11, 29, 26, 34], [11, 31, 26, 44], [12, 4, 27, 2], [12, 11, 27, 9, "value"], [12, 16, 27, 14], [12, 19, 27, 17, "Number"], [12, 25, 27, 23], [12, 26, 27, 24, "value"], [12, 31, 27, 29], [12, 32, 27, 30], [12, 35, 27, 33], [12, 36, 27, 34], [13, 2, 28, 0], [14, 2, 28, 1], [14, 6, 28, 1, "_x"], [14, 8, 28, 1], [14, 28, 28, 1, "_classPrivateFieldLooseKey2"], [14, 55, 28, 1], [14, 56, 28, 1, "default"], [14, 63, 28, 1], [15, 2, 28, 1], [15, 6, 28, 1, "_y"], [15, 8, 28, 1], [15, 28, 28, 1, "_classPrivateFieldLooseKey2"], [15, 55, 28, 1], [15, 56, 28, 1, "default"], [15, 63, 28, 1], [16, 2, 28, 1], [16, 6, 28, 1, "_width"], [16, 12, 28, 1], [16, 32, 28, 1, "_classPrivateFieldLooseKey2"], [16, 59, 28, 1], [16, 60, 28, 1, "default"], [16, 67, 28, 1], [17, 2, 28, 1], [17, 6, 28, 1, "_height"], [17, 13, 28, 1], [17, 33, 28, 1, "_classPrivateFieldLooseKey2"], [17, 60, 28, 1], [17, 61, 28, 1, "default"], [17, 68, 28, 1], [18, 2, 28, 1], [18, 6, 35, 21, "DOMRectReadOnly"], [18, 21, 35, 36], [18, 24, 35, 36, "exports"], [18, 31, 35, 36], [18, 32, 35, 36, "default"], [18, 39, 35, 36], [19, 4, 41, 2], [19, 13, 41, 2, "DOMRectReadOnly"], [19, 29, 41, 14, "x"], [19, 30, 41, 24], [19, 32, 41, 26, "y"], [19, 33, 41, 36], [19, 35, 41, 38, "width"], [19, 40, 41, 52], [19, 42, 41, 54, "height"], [19, 48, 41, 69], [19, 50, 41, 71], [20, 6, 41, 71], [20, 10, 41, 71, "_classCallCheck2"], [20, 26, 41, 71], [20, 27, 41, 71, "default"], [20, 34, 41, 71], [20, 42, 41, 71, "DOMRectReadOnly"], [20, 57, 41, 71], [21, 6, 41, 71, "Object"], [21, 12, 41, 71], [21, 13, 41, 71, "defineProperty"], [21, 27, 41, 71], [21, 34, 41, 71, "_x"], [21, 36, 41, 71], [22, 8, 41, 71, "writable"], [22, 16, 41, 71], [23, 8, 41, 71, "value"], [23, 13, 41, 71], [24, 6, 41, 71], [25, 6, 41, 71, "Object"], [25, 12, 41, 71], [25, 13, 41, 71, "defineProperty"], [25, 27, 41, 71], [25, 34, 41, 71, "_y"], [25, 36, 41, 71], [26, 8, 41, 71, "writable"], [26, 16, 41, 71], [27, 8, 41, 71, "value"], [27, 13, 41, 71], [28, 6, 41, 71], [29, 6, 41, 71, "Object"], [29, 12, 41, 71], [29, 13, 41, 71, "defineProperty"], [29, 27, 41, 71], [29, 34, 41, 71, "_width"], [29, 40, 41, 71], [30, 8, 41, 71, "writable"], [30, 16, 41, 71], [31, 8, 41, 71, "value"], [31, 13, 41, 71], [32, 6, 41, 71], [33, 6, 41, 71, "Object"], [33, 12, 41, 71], [33, 13, 41, 71, "defineProperty"], [33, 27, 41, 71], [33, 34, 41, 71, "_height"], [33, 41, 41, 71], [34, 8, 41, 71, "writable"], [34, 16, 41, 71], [35, 8, 41, 71, "value"], [35, 13, 41, 71], [36, 6, 41, 71], [37, 6, 42, 4], [37, 10, 42, 8], [37, 11, 42, 9, "__setInternalX"], [37, 25, 42, 23], [37, 26, 42, 24, "x"], [37, 27, 42, 25], [37, 28, 42, 26], [38, 6, 43, 4], [38, 10, 43, 8], [38, 11, 43, 9, "__setInternalY"], [38, 25, 43, 23], [38, 26, 43, 24, "y"], [38, 27, 43, 25], [38, 28, 43, 26], [39, 6, 44, 4], [39, 10, 44, 8], [39, 11, 44, 9, "__setInternalWidth"], [39, 29, 44, 27], [39, 30, 44, 28, "width"], [39, 35, 44, 33], [39, 36, 44, 34], [40, 6, 45, 4], [40, 10, 45, 8], [40, 11, 45, 9, "__setInternalHeight"], [40, 30, 45, 28], [40, 31, 45, 29, "height"], [40, 37, 45, 35], [40, 38, 45, 36], [41, 4, 46, 2], [42, 4, 46, 3], [42, 15, 46, 3, "_createClass2"], [42, 28, 46, 3], [42, 29, 46, 3, "default"], [42, 36, 46, 3], [42, 38, 46, 3, "DOMRectReadOnly"], [42, 53, 46, 3], [43, 6, 46, 3, "key"], [43, 9, 46, 3], [44, 6, 46, 3, "get"], [44, 9, 46, 3], [44, 11, 51, 2], [44, 20, 51, 2, "get"], [44, 21, 51, 2], [44, 23, 51, 18], [45, 8, 52, 4], [45, 19, 52, 4, "_classPrivateFieldLooseBase2"], [45, 47, 52, 4], [45, 48, 52, 4, "default"], [45, 55, 52, 4], [45, 57, 52, 11], [45, 61, 52, 15], [45, 63, 52, 15, "_x"], [45, 65, 52, 15], [45, 67, 52, 15, "_x"], [45, 69, 52, 15], [46, 6, 53, 2], [47, 4, 53, 3], [48, 6, 53, 3, "key"], [48, 9, 53, 3], [49, 6, 53, 3, "get"], [49, 9, 53, 3], [49, 11, 58, 2], [49, 20, 58, 2, "get"], [49, 21, 58, 2], [49, 23, 58, 18], [50, 8, 59, 4], [50, 19, 59, 4, "_classPrivateFieldLooseBase2"], [50, 47, 59, 4], [50, 48, 59, 4, "default"], [50, 55, 59, 4], [50, 57, 59, 11], [50, 61, 59, 15], [50, 63, 59, 15, "_y"], [50, 65, 59, 15], [50, 67, 59, 15, "_y"], [50, 69, 59, 15], [51, 6, 60, 2], [52, 4, 60, 3], [53, 6, 60, 3, "key"], [53, 9, 60, 3], [54, 6, 60, 3, "get"], [54, 9, 60, 3], [54, 11, 65, 2], [54, 20, 65, 2, "get"], [54, 21, 65, 2], [54, 23, 65, 22], [55, 8, 66, 4], [55, 19, 66, 4, "_classPrivateFieldLooseBase2"], [55, 47, 66, 4], [55, 48, 66, 4, "default"], [55, 55, 66, 4], [55, 57, 66, 11], [55, 61, 66, 15], [55, 63, 66, 15, "_width"], [55, 69, 66, 15], [55, 71, 66, 15, "_width"], [55, 77, 66, 15], [56, 6, 67, 2], [57, 4, 67, 3], [58, 6, 67, 3, "key"], [58, 9, 67, 3], [59, 6, 67, 3, "get"], [59, 9, 67, 3], [59, 11, 72, 2], [59, 20, 72, 2, "get"], [59, 21, 72, 2], [59, 23, 72, 23], [60, 8, 73, 4], [60, 19, 73, 4, "_classPrivateFieldLooseBase2"], [60, 47, 73, 4], [60, 48, 73, 4, "default"], [60, 55, 73, 4], [60, 57, 73, 11], [60, 61, 73, 15], [60, 63, 73, 15, "_height"], [60, 70, 73, 15], [60, 72, 73, 15, "_height"], [60, 79, 73, 15], [61, 6, 74, 2], [62, 4, 74, 3], [63, 6, 74, 3, "key"], [63, 9, 74, 3], [64, 6, 74, 3, "get"], [64, 9, 74, 3], [64, 11, 79, 2], [64, 20, 79, 2, "get"], [64, 21, 79, 2], [64, 23, 79, 20], [65, 8, 80, 4], [65, 12, 80, 10, "height"], [65, 18, 80, 16], [65, 25, 80, 16, "_classPrivateFieldLooseBase2"], [65, 53, 80, 16], [65, 54, 80, 16, "default"], [65, 61, 80, 16], [65, 63, 80, 19], [65, 67, 80, 23], [65, 69, 80, 23, "_height"], [65, 76, 80, 23], [65, 78, 80, 23, "_height"], [65, 85, 80, 23], [65, 86, 80, 31], [66, 8, 81, 4], [66, 12, 81, 10, "y"], [66, 13, 81, 11], [66, 20, 81, 11, "_classPrivateFieldLooseBase2"], [66, 48, 81, 11], [66, 49, 81, 11, "default"], [66, 56, 81, 11], [66, 58, 81, 14], [66, 62, 81, 18], [66, 64, 81, 18, "_y"], [66, 66, 81, 18], [66, 68, 81, 18, "_y"], [66, 70, 81, 18], [66, 71, 81, 21], [67, 8, 83, 4], [67, 12, 83, 8, "height"], [67, 18, 83, 14], [67, 21, 83, 17], [67, 22, 83, 18], [67, 24, 83, 20], [68, 10, 84, 6], [68, 17, 84, 13, "y"], [68, 18, 84, 14], [68, 21, 84, 17, "height"], [68, 27, 84, 23], [69, 8, 85, 4], [70, 8, 87, 4], [70, 15, 87, 11, "y"], [70, 16, 87, 12], [71, 6, 88, 2], [72, 4, 88, 3], [73, 6, 88, 3, "key"], [73, 9, 88, 3], [74, 6, 88, 3, "get"], [74, 9, 88, 3], [74, 11, 93, 2], [74, 20, 93, 2, "get"], [74, 21, 93, 2], [74, 23, 93, 22], [75, 8, 94, 4], [75, 12, 94, 10, "width"], [75, 17, 94, 15], [75, 24, 94, 15, "_classPrivateFieldLooseBase2"], [75, 52, 94, 15], [75, 53, 94, 15, "default"], [75, 60, 94, 15], [75, 62, 94, 18], [75, 66, 94, 22], [75, 68, 94, 22, "_width"], [75, 74, 94, 22], [75, 76, 94, 22, "_width"], [75, 82, 94, 22], [75, 83, 94, 29], [76, 8, 95, 4], [76, 12, 95, 10, "x"], [76, 13, 95, 11], [76, 20, 95, 11, "_classPrivateFieldLooseBase2"], [76, 48, 95, 11], [76, 49, 95, 11, "default"], [76, 56, 95, 11], [76, 58, 95, 14], [76, 62, 95, 18], [76, 64, 95, 18, "_x"], [76, 66, 95, 18], [76, 68, 95, 18, "_x"], [76, 70, 95, 18], [76, 71, 95, 21], [77, 8, 97, 4], [77, 12, 97, 8, "width"], [77, 17, 97, 13], [77, 20, 97, 16], [77, 21, 97, 17], [77, 23, 97, 19], [78, 10, 98, 6], [78, 17, 98, 13, "x"], [78, 18, 98, 14], [79, 8, 99, 4], [80, 8, 101, 4], [80, 15, 101, 11, "x"], [80, 16, 101, 12], [80, 19, 101, 15, "width"], [80, 24, 101, 20], [81, 6, 102, 2], [82, 4, 102, 3], [83, 6, 102, 3, "key"], [83, 9, 102, 3], [84, 6, 102, 3, "get"], [84, 9, 102, 3], [84, 11, 107, 2], [84, 20, 107, 2, "get"], [84, 21, 107, 2], [84, 23, 107, 23], [85, 8, 108, 4], [85, 12, 108, 10, "height"], [85, 18, 108, 16], [85, 25, 108, 16, "_classPrivateFieldLooseBase2"], [85, 53, 108, 16], [85, 54, 108, 16, "default"], [85, 61, 108, 16], [85, 63, 108, 19], [85, 67, 108, 23], [85, 69, 108, 23, "_height"], [85, 76, 108, 23], [85, 78, 108, 23, "_height"], [85, 85, 108, 23], [85, 86, 108, 31], [86, 8, 109, 4], [86, 12, 109, 10, "y"], [86, 13, 109, 11], [86, 20, 109, 11, "_classPrivateFieldLooseBase2"], [86, 48, 109, 11], [86, 49, 109, 11, "default"], [86, 56, 109, 11], [86, 58, 109, 14], [86, 62, 109, 18], [86, 64, 109, 18, "_y"], [86, 66, 109, 18], [86, 68, 109, 18, "_y"], [86, 70, 109, 18], [86, 71, 109, 21], [87, 8, 111, 4], [87, 12, 111, 8, "height"], [87, 18, 111, 14], [87, 21, 111, 17], [87, 22, 111, 18], [87, 24, 111, 20], [88, 10, 112, 6], [88, 17, 112, 13, "y"], [88, 18, 112, 14], [89, 8, 113, 4], [90, 8, 115, 4], [90, 15, 115, 11, "y"], [90, 16, 115, 12], [90, 19, 115, 15, "height"], [90, 25, 115, 21], [91, 6, 116, 2], [92, 4, 116, 3], [93, 6, 116, 3, "key"], [93, 9, 116, 3], [94, 6, 116, 3, "get"], [94, 9, 116, 3], [94, 11, 121, 2], [94, 20, 121, 2, "get"], [94, 21, 121, 2], [94, 23, 121, 21], [95, 8, 122, 4], [95, 12, 122, 10, "width"], [95, 17, 122, 15], [95, 24, 122, 15, "_classPrivateFieldLooseBase2"], [95, 52, 122, 15], [95, 53, 122, 15, "default"], [95, 60, 122, 15], [95, 62, 122, 18], [95, 66, 122, 22], [95, 68, 122, 22, "_width"], [95, 74, 122, 22], [95, 76, 122, 22, "_width"], [95, 82, 122, 22], [95, 83, 122, 29], [96, 8, 123, 4], [96, 12, 123, 10, "x"], [96, 13, 123, 11], [96, 20, 123, 11, "_classPrivateFieldLooseBase2"], [96, 48, 123, 11], [96, 49, 123, 11, "default"], [96, 56, 123, 11], [96, 58, 123, 14], [96, 62, 123, 18], [96, 64, 123, 18, "_x"], [96, 66, 123, 18], [96, 68, 123, 18, "_x"], [96, 70, 123, 18], [96, 71, 123, 21], [97, 8, 125, 4], [97, 12, 125, 8, "width"], [97, 17, 125, 13], [97, 20, 125, 16], [97, 21, 125, 17], [97, 23, 125, 19], [98, 10, 126, 6], [98, 17, 126, 13, "x"], [98, 18, 126, 14], [98, 21, 126, 17, "width"], [98, 26, 126, 22], [99, 8, 127, 4], [100, 8, 129, 4], [100, 15, 129, 11, "x"], [100, 16, 129, 12], [101, 6, 130, 2], [102, 4, 130, 3], [103, 6, 130, 3, "key"], [103, 9, 130, 3], [104, 6, 130, 3, "value"], [104, 11, 130, 3], [104, 13, 132, 2], [104, 22, 132, 2, "toJSON"], [104, 28, 132, 8, "toJSON"], [104, 29, 132, 8], [104, 31, 141, 4], [105, 8, 142, 4], [105, 12, 142, 11, "x"], [105, 13, 142, 12], [105, 16, 142, 60], [105, 20, 142, 64], [105, 21, 142, 11, "x"], [105, 22, 142, 12], [106, 10, 142, 14, "y"], [106, 11, 142, 15], [106, 14, 142, 60], [106, 18, 142, 64], [106, 19, 142, 14, "y"], [106, 20, 142, 15], [107, 10, 142, 17, "width"], [107, 15, 142, 22], [107, 18, 142, 60], [107, 22, 142, 64], [107, 23, 142, 17, "width"], [107, 28, 142, 22], [108, 10, 142, 24, "height"], [108, 16, 142, 30], [108, 19, 142, 60], [108, 23, 142, 64], [108, 24, 142, 24, "height"], [108, 30, 142, 30], [109, 10, 142, 32, "top"], [109, 13, 142, 35], [109, 16, 142, 60], [109, 20, 142, 64], [109, 21, 142, 32, "top"], [109, 24, 142, 35], [110, 10, 142, 37, "left"], [110, 14, 142, 41], [110, 17, 142, 60], [110, 21, 142, 64], [110, 22, 142, 37, "left"], [110, 26, 142, 41], [111, 10, 142, 43, "bottom"], [111, 16, 142, 49], [111, 19, 142, 60], [111, 23, 142, 64], [111, 24, 142, 43, "bottom"], [111, 30, 142, 49], [112, 10, 142, 51, "right"], [112, 15, 142, 56], [112, 18, 142, 60], [112, 22, 142, 64], [112, 23, 142, 51, "right"], [112, 28, 142, 56], [113, 8, 143, 4], [113, 15, 143, 11], [114, 10, 143, 12, "x"], [114, 11, 143, 13], [115, 10, 143, 15, "y"], [115, 11, 143, 16], [116, 10, 143, 18, "width"], [116, 15, 143, 23], [117, 10, 143, 25, "height"], [117, 16, 143, 31], [118, 10, 143, 33, "top"], [118, 13, 143, 36], [119, 10, 143, 38, "left"], [119, 14, 143, 42], [120, 10, 143, 44, "bottom"], [120, 16, 143, 50], [121, 10, 143, 52, "right"], [122, 8, 143, 57], [122, 9, 143, 58], [123, 6, 144, 2], [124, 4, 144, 3], [125, 6, 144, 3, "key"], [125, 9, 144, 3], [126, 6, 144, 3, "value"], [126, 11, 144, 3], [126, 13, 157, 2], [126, 22, 157, 2, "__getInternalX"], [126, 36, 157, 16, "__getInternalX"], [126, 37, 157, 16], [126, 39, 157, 27], [127, 8, 158, 4], [127, 19, 158, 4, "_classPrivateFieldLooseBase2"], [127, 47, 158, 4], [127, 48, 158, 4, "default"], [127, 55, 158, 4], [127, 57, 158, 11], [127, 61, 158, 15], [127, 63, 158, 15, "_x"], [127, 65, 158, 15], [127, 67, 158, 15, "_x"], [127, 69, 158, 15], [128, 6, 159, 2], [129, 4, 159, 3], [130, 6, 159, 3, "key"], [130, 9, 159, 3], [131, 6, 159, 3, "value"], [131, 11, 159, 3], [131, 13, 161, 2], [131, 22, 161, 2, "__getInternalY"], [131, 36, 161, 16, "__getInternalY"], [131, 37, 161, 16], [131, 39, 161, 27], [132, 8, 162, 4], [132, 19, 162, 4, "_classPrivateFieldLooseBase2"], [132, 47, 162, 4], [132, 48, 162, 4, "default"], [132, 55, 162, 4], [132, 57, 162, 11], [132, 61, 162, 15], [132, 63, 162, 15, "_y"], [132, 65, 162, 15], [132, 67, 162, 15, "_y"], [132, 69, 162, 15], [133, 6, 163, 2], [134, 4, 163, 3], [135, 6, 163, 3, "key"], [135, 9, 163, 3], [136, 6, 163, 3, "value"], [136, 11, 163, 3], [136, 13, 165, 2], [136, 22, 165, 2, "__getInternalWidth"], [136, 40, 165, 20, "__getInternalWidth"], [136, 41, 165, 20], [136, 43, 165, 31], [137, 8, 166, 4], [137, 19, 166, 4, "_classPrivateFieldLooseBase2"], [137, 47, 166, 4], [137, 48, 166, 4, "default"], [137, 55, 166, 4], [137, 57, 166, 11], [137, 61, 166, 15], [137, 63, 166, 15, "_width"], [137, 69, 166, 15], [137, 71, 166, 15, "_width"], [137, 77, 166, 15], [138, 6, 167, 2], [139, 4, 167, 3], [140, 6, 167, 3, "key"], [140, 9, 167, 3], [141, 6, 167, 3, "value"], [141, 11, 167, 3], [141, 13, 169, 2], [141, 22, 169, 2, "__getInternalHeight"], [141, 41, 169, 21, "__getInternalHeight"], [141, 42, 169, 21], [141, 44, 169, 32], [142, 8, 170, 4], [142, 19, 170, 4, "_classPrivateFieldLooseBase2"], [142, 47, 170, 4], [142, 48, 170, 4, "default"], [142, 55, 170, 4], [142, 57, 170, 11], [142, 61, 170, 15], [142, 63, 170, 15, "_height"], [142, 70, 170, 15], [142, 72, 170, 15, "_height"], [142, 79, 170, 15], [143, 6, 171, 2], [144, 4, 171, 3], [145, 6, 171, 3, "key"], [145, 9, 171, 3], [146, 6, 171, 3, "value"], [146, 11, 171, 3], [146, 13, 173, 2], [146, 22, 173, 2, "__setInternalX"], [146, 36, 173, 16, "__setInternalX"], [146, 37, 173, 17, "x"], [146, 38, 173, 27], [146, 40, 173, 29], [147, 8, 174, 4], [147, 12, 174, 4, "_classPrivateFieldLooseBase2"], [147, 40, 174, 4], [147, 41, 174, 4, "default"], [147, 48, 174, 4], [147, 54, 174, 8], [147, 56, 174, 8, "_x"], [147, 58, 174, 8], [147, 60, 174, 8, "_x"], [147, 62, 174, 8], [147, 66, 174, 14, "castToNumber"], [147, 78, 174, 26], [147, 79, 174, 27, "x"], [147, 80, 174, 28], [147, 81, 174, 29], [148, 6, 175, 2], [149, 4, 175, 3], [150, 6, 175, 3, "key"], [150, 9, 175, 3], [151, 6, 175, 3, "value"], [151, 11, 175, 3], [151, 13, 177, 2], [151, 22, 177, 2, "__setInternalY"], [151, 36, 177, 16, "__setInternalY"], [151, 37, 177, 17, "y"], [151, 38, 177, 27], [151, 40, 177, 29], [152, 8, 178, 4], [152, 12, 178, 4, "_classPrivateFieldLooseBase2"], [152, 40, 178, 4], [152, 41, 178, 4, "default"], [152, 48, 178, 4], [152, 54, 178, 8], [152, 56, 178, 8, "_y"], [152, 58, 178, 8], [152, 60, 178, 8, "_y"], [152, 62, 178, 8], [152, 66, 178, 14, "castToNumber"], [152, 78, 178, 26], [152, 79, 178, 27, "y"], [152, 80, 178, 28], [152, 81, 178, 29], [153, 6, 179, 2], [154, 4, 179, 3], [155, 6, 179, 3, "key"], [155, 9, 179, 3], [156, 6, 179, 3, "value"], [156, 11, 179, 3], [156, 13, 181, 2], [156, 22, 181, 2, "__setInternalWidth"], [156, 40, 181, 20, "__setInternalWidth"], [156, 41, 181, 21, "width"], [156, 46, 181, 35], [156, 48, 181, 37], [157, 8, 182, 4], [157, 12, 182, 4, "_classPrivateFieldLooseBase2"], [157, 40, 182, 4], [157, 41, 182, 4, "default"], [157, 48, 182, 4], [157, 54, 182, 8], [157, 56, 182, 8, "_width"], [157, 62, 182, 8], [157, 64, 182, 8, "_width"], [157, 70, 182, 8], [157, 74, 182, 18, "castToNumber"], [157, 86, 182, 30], [157, 87, 182, 31, "width"], [157, 92, 182, 36], [157, 93, 182, 37], [158, 6, 183, 2], [159, 4, 183, 3], [160, 6, 183, 3, "key"], [160, 9, 183, 3], [161, 6, 183, 3, "value"], [161, 11, 183, 3], [161, 13, 185, 2], [161, 22, 185, 2, "__setInternalHeight"], [161, 41, 185, 21, "__setInternalHeight"], [161, 42, 185, 22, "height"], [161, 48, 185, 37], [161, 50, 185, 39], [162, 8, 186, 4], [162, 12, 186, 4, "_classPrivateFieldLooseBase2"], [162, 40, 186, 4], [162, 41, 186, 4, "default"], [162, 48, 186, 4], [162, 54, 186, 8], [162, 56, 186, 8, "_height"], [162, 63, 186, 8], [162, 65, 186, 8, "_height"], [162, 72, 186, 8], [162, 76, 186, 19, "castToNumber"], [162, 88, 186, 31], [162, 89, 186, 32, "height"], [162, 95, 186, 38], [162, 96, 186, 39], [163, 6, 187, 2], [164, 4, 187, 3], [165, 6, 187, 3, "key"], [165, 9, 187, 3], [166, 6, 187, 3, "value"], [166, 11, 187, 3], [166, 13, 149, 2], [166, 22, 149, 9, "fromRect"], [166, 30, 149, 17, "fromRect"], [166, 31, 149, 18, "rect"], [166, 35, 149, 37], [166, 37, 149, 56], [167, 8, 150, 4], [167, 12, 150, 8], [167, 13, 150, 9, "rect"], [167, 17, 150, 13], [167, 19, 150, 15], [168, 10, 151, 6], [168, 17, 151, 13], [168, 21, 151, 17, "DOMRectReadOnly"], [168, 36, 151, 32], [168, 37, 151, 33], [168, 38, 151, 34], [169, 8, 152, 4], [170, 8, 154, 4], [170, 15, 154, 11], [170, 19, 154, 15, "DOMRectReadOnly"], [170, 34, 154, 30], [170, 35, 154, 31, "rect"], [170, 39, 154, 35], [170, 40, 154, 36, "x"], [170, 41, 154, 37], [170, 43, 154, 39, "rect"], [170, 47, 154, 43], [170, 48, 154, 44, "y"], [170, 49, 154, 45], [170, 51, 154, 47, "rect"], [170, 55, 154, 51], [170, 56, 154, 52, "width"], [170, 61, 154, 57], [170, 63, 154, 59, "rect"], [170, 67, 154, 63], [170, 68, 154, 64, "height"], [170, 74, 154, 70], [170, 75, 154, 71], [171, 6, 155, 2], [172, 4, 155, 3], [173, 2, 155, 3], [174, 0, 155, 3], [174, 3]], "functionMap": {"names": ["<global>", "castToNumber", "DOMRectReadOnly", "constructor", "get__x", "get__y", "get__width", "get__height", "get__top", "get__right", "get__bottom", "get__left", "toJSON", "fromRect", "__getInternalX", "__getInternalY", "__getInternalWidth", "__getInternalHeight", "__setInternalX", "__setInternalY", "__setInternalWidth", "__setInternalHeight"], "mappings": "AAA;ACyB;CDE;eEO;ECM;GDK;EEK;GFE;EGK;GHE;EIK;GJE;EKK;GLE;EMK;GNS;EOK;GPS;EQK;GRS;ESK;GTS;EUE;GVY;EWK;GXM;EYE;GZE;EaE;GbE;EcE;GdE;EeE;GfE;EgBE;GhBE;EiBE;GjBE;EkBE;GlBE;EmBE;GnBE"}}, "type": "js/module"}]}