{"dependencies": [{"name": "./animationManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 6, "column": 28, "index": 103}}], "key": "GhohGaY570owURBZ/FmQ5kfBgps=", "exportNames": ["*"]}}, {"name": "./presets", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 213}, "end": {"line": 12, "column": 45, "index": 258}}], "key": "wl+i0si2QZP77xy/or20GqIHbBg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"ScreenTransition\", {\n    enumerable: true,\n    get: function () {\n      return _presets.ScreenTransition;\n    }\n  });\n  Object.defineProperty(exports, \"finishScreenTransition\", {\n    enumerable: true,\n    get: function () {\n      return _animationManager.finishScreenTransition;\n    }\n  });\n  Object.defineProperty(exports, \"startScreenTransition\", {\n    enumerable: true,\n    get: function () {\n      return _animationManager.startScreenTransition;\n    }\n  });\n  var _animationManager = require(_dependencyMap[0], \"./animationManager\");\n  var _presets = require(_dependencyMap[1], \"./presets\");\n});", "lineCount": 27, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "Object"], [7, 8, 1, 13], [7, 9, 1, 13, "defineProperty"], [7, 23, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [8, 4, 1, 13, "enumerable"], [8, 14, 1, 13], [9, 4, 1, 13, "get"], [9, 7, 1, 13], [9, 18, 1, 13, "get"], [9, 19, 1, 13], [10, 6, 1, 13], [10, 13, 1, 13, "_presets"], [10, 21, 1, 13], [10, 22, 1, 13, "ScreenTransition"], [10, 38, 1, 13], [11, 4, 1, 13], [12, 2, 1, 13], [13, 2, 1, 13, "Object"], [13, 8, 1, 13], [13, 9, 1, 13, "defineProperty"], [13, 23, 1, 13], [13, 24, 1, 13, "exports"], [13, 31, 1, 13], [14, 4, 1, 13, "enumerable"], [14, 14, 1, 13], [15, 4, 1, 13, "get"], [15, 7, 1, 13], [15, 18, 1, 13, "get"], [15, 19, 1, 13], [16, 6, 1, 13], [16, 13, 1, 13, "_animationManager"], [16, 30, 1, 13], [16, 31, 1, 13, "finishScreenTransition"], [16, 53, 1, 13], [17, 4, 1, 13], [18, 2, 1, 13], [19, 2, 1, 13, "Object"], [19, 8, 1, 13], [19, 9, 1, 13, "defineProperty"], [19, 23, 1, 13], [19, 24, 1, 13, "exports"], [19, 31, 1, 13], [20, 4, 1, 13, "enumerable"], [20, 14, 1, 13], [21, 4, 1, 13, "get"], [21, 7, 1, 13], [21, 18, 1, 13, "get"], [21, 19, 1, 13], [22, 6, 1, 13], [22, 13, 1, 13, "_animationManager"], [22, 30, 1, 13], [22, 31, 1, 13, "startScreenTransition"], [22, 52, 1, 13], [23, 4, 1, 13], [24, 2, 1, 13], [25, 2, 3, 0], [25, 6, 3, 0, "_animationManager"], [25, 23, 3, 0], [25, 26, 3, 0, "require"], [25, 33, 3, 0], [25, 34, 3, 0, "_dependencyMap"], [25, 48, 3, 0], [26, 2, 12, 0], [26, 6, 12, 0, "_presets"], [26, 14, 12, 0], [26, 17, 12, 0, "require"], [26, 24, 12, 0], [26, 25, 12, 0, "_dependencyMap"], [26, 39, 12, 0], [27, 0, 12, 45], [27, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}