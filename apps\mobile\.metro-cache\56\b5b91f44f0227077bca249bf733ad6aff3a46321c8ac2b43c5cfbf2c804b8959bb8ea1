{"dependencies": [{"name": "./errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 60}, "end": {"line": 3, "column": 43, "index": 103}}], "key": "rEld05quROH+iA6QLT6kkvqJ/qc=", "exportNames": ["*"]}}, {"name": "./logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 104}, "end": {"line": 4, "column": 34, "index": 138}}], "key": "RJYKXaUuTbTmL7MuVmczbacEgjY=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 139}, "end": {"line": 5, "column": 51, "index": 190}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}, {"name": "./reactUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 191}, "end": {"line": 6, "column": 68, "index": 259}}], "key": "vEqJ/agEzqswVNsXxMLPQl8WTJQ=", "exportNames": ["*"]}}, {"name": "./shareableMappingCache", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 260}, "end": {"line": 7, "column": 64, "index": 324}}], "key": "hjy7PrQZwaViSHKgqgET7267USw=", "exportNames": ["*"]}}, {"name": "./shareables", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 325}, "end": {"line": 8, "column": 59, "index": 384}}], "key": "V8GJV/2wCfEKa73+4dIdiUi/ZbE=", "exportNames": ["*"]}}, {"name": "./threads", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 385}, "end": {"line": 9, "column": 60, "index": 445}}], "key": "ZuB0ICrjKM3htfPQkuonl9kPByQ=", "exportNames": ["*"]}}, {"name": "./valueSetter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 446}, "end": {"line": 10, "column": 44, "index": 490}}], "key": "dp0b2eNbdjzPb/KovROtf9u6MKQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.makeMutableUI = exports.makeMutable = void 0;\n  var _errors = require(_dependencyMap[0], \"./errors\");\n  var _logger = require(_dependencyMap[1], \"./logger\");\n  var _PlatformChecker = require(_dependencyMap[2], \"./PlatformChecker\");\n  var _reactUtils = require(_dependencyMap[3], \"./reactUtils\");\n  var _shareableMappingCache = require(_dependencyMap[4], \"./shareableMappingCache\");\n  var _shareables = require(_dependencyMap[5], \"./shareables\");\n  var _threads = require(_dependencyMap[6], \"./threads\");\n  var _valueSetter = require(_dependencyMap[7], \"./valueSetter\");\n  var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n  function shouldWarnAboutAccessDuringRender() {\n    return __DEV__ && (0, _reactUtils.isReactRendering)() && !(0, _reactUtils.isFirstReactRender)();\n  }\n  function checkInvalidReadDuringRender() {\n    if (shouldWarnAboutAccessDuringRender()) {\n      _logger.logger.warn(\"Reading from `value` during component render. Please ensure that you don't access the `value` property nor use `get` method of a shared value while React is rendering a component.\", {\n        strict: true\n      });\n    }\n  }\n  function checkInvalidWriteDuringRender() {\n    if (shouldWarnAboutAccessDuringRender()) {\n      _logger.logger.warn(\"Writing to `value` during component render. Please ensure that you don't access the `value` property nor use `set` method of a shared value while React is rendering a component.\", {\n        strict: true\n      });\n    }\n  }\n  var _worklet_7577286896107_init_data = {\n    code: \"function addCompilerSafeGetAndSet_mutablesTs1(mutable){Object.defineProperties(mutable,{get:{value:function(){return mutable.value;},configurable:false,enumerable:false},set:{value:function(newValue){if(typeof newValue==='function'&&!newValue.__isAnimationDefinition){mutable.value=newValue(mutable.value);}else{mutable.value=newValue;}},configurable:false,enumerable:false}});}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\mutables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"addCompilerSafeGetAndSet_mutablesTs1\\\",\\\"mutable\\\",\\\"Object\\\",\\\"defineProperties\\\",\\\"get\\\",\\\"value\\\",\\\"configurable\\\",\\\"enumerable\\\",\\\"set\\\",\\\"newValue\\\",\\\"__isAnimationDefinition\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/mutables.ts\\\"],\\\"mappings\\\":\\\"AAuCA,SAAAA,qCAAAC,OAAA,EAAAC,MAAA,CAAAC,gBAAA,CAAAF,OAAA,EACAG,GAAA,EACAC,KAAA,SAAAA,CAAA,EACA,OAAAJ,OAAA,CAAAI,KAAA,CACA,EACAC,YAAA,OACAC,UAAA,MACA,EACAC,GAAA,EACAH,KAAS,SAAAA,CAAAI,QAAA,CAAwB,CAEzB,GAAC,OAAAA,QAAiB,aAAS,EAE7B,CAAAA,QAAK,CAAAC,uBAAG,EACNT,OAAO,CAAAI,KAAQ,CAAAI,QAAK,CAAAR,OAAA,CAAAI,KAAA,EACrB,MACDJ,OAAA,CAAYI,KAAE,CAAKI,QAAA,CACnB,CACD,EACDH,YAAK,OACHC,UAAK,M\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  /**\n   * Adds `get` and `set` methods to the mutable object to handle access to\n   * `value` property.\n   *\n   * React Compiler disallows modifying return values of hooks. Even though\n   * assignment to `value` is a setter invocation, Compiler's static analysis\n   * doesn't detect it. That's why we provide a second API for users using the\n   * Compiler.\n   */\n  var addCompilerSafeGetAndSet = function () {\n    var _e = [new global.Error(), 1, -27];\n    var addCompilerSafeGetAndSet = function (mutable) {\n      Object.defineProperties(mutable, {\n        get: {\n          value() {\n            return mutable.value;\n          },\n          configurable: false,\n          enumerable: false\n        },\n        set: {\n          value(newValue) {\n            if (typeof newValue === 'function' &&\n            // If we have an animation definition, we don't want to call it here.\n            !newValue.__isAnimationDefinition) {\n              mutable.value = newValue(mutable.value);\n            } else {\n              mutable.value = newValue;\n            }\n          },\n          configurable: false,\n          enumerable: false\n        }\n      });\n    };\n    addCompilerSafeGetAndSet.__closure = {};\n    addCompilerSafeGetAndSet.__workletHash = 7577286896107;\n    addCompilerSafeGetAndSet.__initData = _worklet_7577286896107_init_data;\n    addCompilerSafeGetAndSet.__stackDetails = _e;\n    return addCompilerSafeGetAndSet;\n  }();\n  /**\n   * Hides the internal `_value` property of a mutable. It won't be visible to:\n   *\n   * - `Object.keys`,\n   * - `const prop in obj`,\n   * - Etc.\n   *\n   * This way when the user accidentally sends the SharedValue to React, he won't\n   * get an obscure error message.\n   *\n   * We hide for both _React runtime_ and _Worklet runtime_ mutables for\n   * uniformity of behavior.\n   */\n  var _worklet_12520546980376_init_data = {\n    code: \"function hideInternalValueProp_mutablesTs2(mutable){Object.defineProperty(mutable,'_value',{configurable:false,enumerable:false});}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\mutables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"hideInternalValueProp_mutablesTs2\\\",\\\"mutable\\\",\\\"Object\\\",\\\"defineProperty\\\",\\\"configurable\\\",\\\"enumerable\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/mutables.ts\\\"],\\\"mappings\\\":\\\"AA2EA,SAAAA,kCAAAC,OAAA,EAAAC,MAAA,CAAAC,cAAA,CAAAF,OAAA,WACAG,YAAA,OACAC,UAAA,MACA,GACA\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var hideInternalValueProp = function () {\n    var _e = [new global.Error(), 1, -27];\n    var hideInternalValueProp = function (mutable) {\n      Object.defineProperty(mutable, '_value', {\n        configurable: false,\n        enumerable: false\n      });\n    };\n    hideInternalValueProp.__closure = {};\n    hideInternalValueProp.__workletHash = 12520546980376;\n    hideInternalValueProp.__initData = _worklet_12520546980376_init_data;\n    hideInternalValueProp.__stackDetails = _e;\n    return hideInternalValueProp;\n  }();\n  var _worklet_3490228900074_init_data = {\n    code: \"function makeMutableUI_mutablesTs3(initial){const{valueSetter,hideInternalValueProp,addCompilerSafeGetAndSet}=this.__closure;const listeners=new Map();let value=initial;const mutable={get value(){return value;},set value(newValue){valueSetter(mutable,newValue);},get _value(){return value;},set _value(newValue){value=newValue;listeners.forEach(function(listener){listener(newValue);});},modify:function(modifier,forceUpdate=true){valueSetter(mutable,modifier!==undefined?modifier(value):value,forceUpdate);},addListener:function(id,listener){listeners.set(id,listener);},removeListener:function(id){listeners.delete(id);},_animation:null,_isReanimatedSharedValue:true};hideInternalValueProp(mutable);addCompilerSafeGetAndSet(mutable);return mutable;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\mutables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"makeMutableUI_mutablesTs3\\\",\\\"initial\\\",\\\"valueSetter\\\",\\\"hideInternalValueProp\\\",\\\"addCompilerSafeGetAndSet\\\",\\\"__closure\\\",\\\"listeners\\\",\\\"Map\\\",\\\"value\\\",\\\"mutable\\\",\\\"newValue\\\",\\\"_value\\\",\\\"forEach\\\",\\\"listener\\\",\\\"modify\\\",\\\"modifier\\\",\\\"forceUpdate\\\",\\\"undefined\\\",\\\"addListener\\\",\\\"id\\\",\\\"set\\\",\\\"removeListener\\\",\\\"delete\\\",\\\"_animation\\\",\\\"_isReanimatedSharedValue\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/mutables.ts\\\"],\\\"mappings\\\":\\\"AAgGO,SAAAA,yBAA8DA,CAAAC,OAAA,QAAAC,WAAA,CAAAC,qBAAA,CAAAC,wBAAA,OAAAC,SAAA,CAEnE,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAC,GAAG,CAA0B,CAAC,CACpD,GAAI,CAAAC,KAAK,CAAGP,OAAO,CAEnB,KAAM,CAAAQ,OAA8B,CAAG,CACrC,GAAI,CAAAD,KAAKA,CAAA,CAAG,CACV,MAAO,CAAAA,KAAK,CACd,CAAC,CACD,GAAI,CAAAA,KAAKA,CAACE,QAAQ,CAAE,CAClBR,WAAW,CAACO,OAAO,CAAoBC,QAAQ,CAAC,CAClD,CAAC,CACD,GAAI,CAAAC,MAAMA,CAAA,CAAU,CAClB,MAAO,CAAAH,KAAK,CACd,CAAC,CACD,GAAI,CAAAG,MAAMA,CAACD,QAAe,CAAE,CAC1BF,KAAK,CAAGE,QAAQ,CAChBJ,SAAS,CAACM,OAAO,CAAE,SAAAC,QAAQ,CAAK,CAC9BA,QAAQ,CAACH,QAAQ,CAAC,CACpB,CAAC,CAAC,CACJ,CAAC,CACDI,MAAM,CAAE,QAAAA,CAACC,QAAQ,CAAEC,WAAW,CAAG,IAAI,CAAK,CACxCd,WAAW,CACTO,OAAO,CACPM,QAAQ,GAAKE,SAAS,CAAGF,QAAQ,CAACP,KAAK,CAAC,CAAGA,KAAK,CAChDQ,WACF,CAAC,CACH,CAAC,CACDE,WAAW,CAAE,QAAAA,CAACC,EAAU,CAAEN,QAAyB,CAAK,CACtDP,SAAS,CAACc,GAAG,CAACD,EAAE,CAAEN,QAAQ,CAAC,CAC7B,CAAC,CACDQ,cAAc,CAAE,QAAAA,CAACF,EAAU,CAAK,CAC9Bb,SAAS,CAACgB,MAAM,CAACH,EAAE,CAAC,CACtB,CAAC,CAEDI,UAAU,CAAE,IAAI,CAChBC,wBAAwB,CAAE,IAC5B,CAAC,CAEDrB,qBAAqB,CAACM,OAAO,CAAC,CAC9BL,wBAAwB,CAACK,OAAO,CAAC,CAEjC,MAAO,CAAAA,OAAO,CAChB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var makeMutableUI = exports.makeMutableUI = function () {\n    var _e = [new global.Error(), -4, -27];\n    var makeMutableUI = function (initial) {\n      var listeners = new Map();\n      var value = initial;\n      var mutable = {\n        get value() {\n          return value;\n        },\n        set value(newValue) {\n          (0, _valueSetter.valueSetter)(mutable, newValue);\n        },\n        get _value() {\n          return value;\n        },\n        set _value(newValue) {\n          value = newValue;\n          listeners.forEach(listener => {\n            listener(newValue);\n          });\n        },\n        modify: function (modifier) {\n          var forceUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n          (0, _valueSetter.valueSetter)(mutable, modifier !== undefined ? modifier(value) : value, forceUpdate);\n        },\n        addListener: (id, listener) => {\n          listeners.set(id, listener);\n        },\n        removeListener: id => {\n          listeners.delete(id);\n        },\n        _animation: null,\n        _isReanimatedSharedValue: true\n      };\n      hideInternalValueProp(mutable);\n      addCompilerSafeGetAndSet(mutable);\n      return mutable;\n    };\n    makeMutableUI.__closure = {\n      valueSetter: _valueSetter.valueSetter,\n      hideInternalValueProp,\n      addCompilerSafeGetAndSet\n    };\n    makeMutableUI.__workletHash = 3490228900074;\n    makeMutableUI.__initData = _worklet_3490228900074_init_data;\n    makeMutableUI.__stackDetails = _e;\n    return makeMutableUI;\n  }();\n  var _worklet_2662348200644_init_data = {\n    code: \"function mutablesTs4(){const{makeMutableUI,initial}=this.__closure;return makeMutableUI(initial);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\mutables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"mutablesTs4\\\",\\\"makeMutableUI\\\",\\\"initial\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/mutables.ts\\\"],\\\"mappings\\\":\\\"AA+IY,SAAAA,WAAMA,CAAA,QAAAC,aAAA,CAAAC,OAAA,OAAAC,SAAA,CAEZ,MAAO,CAAAF,aAAa,CAACC,OAAO,CAAC,CAC/B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_9616099880145_init_data = {\n    code: \"function mutablesTs5(sv){return sv.value;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\mutables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"mutablesTs5\\\",\\\"sv\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/mutables.ts\\\"],\\\"mappings\\\":\\\"AAwJmD,QAAC,CAAAA,WAAuBA,CAAAC,EAAA,EACnE,MAAO,CAAAA,EAAE,CAACC,KAAK,CACjB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_16360772742581_init_data = {\n    code: \"function mutablesTs6(){const{mutable,newValue}=this.__closure;mutable.value=newValue;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\mutables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"mutablesTs6\\\",\\\"mutable\\\",\\\"newValue\\\",\\\"__closure\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/mutables.ts\\\"],\\\"mappings\\\":\\\"AA+Jc,SAAAA,WAAMA,CAAA,QAAAC,OAAA,CAAAC,QAAA,OAAAC,SAAA,CACZF,OAAO,CAACG,KAAK,CAAGF,QAAQ,CAC1B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_1667173883347_init_data = {\n    code: \"function mutablesTs7(){const{mutable,modifier,forceUpdate}=this.__closure;mutable.modify(modifier,forceUpdate);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\mutables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"mutablesTs7\\\",\\\"mutable\\\",\\\"modifier\\\",\\\"forceUpdate\\\",\\\"__closure\\\",\\\"modify\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/mutables.ts\\\"],\\\"mappings\\\":\\\"AAgLc,SAAAA,WAAMA,CAAA,QAAAC,OAAA,CAAAC,QAAA,CAAAC,WAAA,OAAAC,SAAA,CACZH,OAAO,CAACI,MAAM,CAACH,QAAQ,CAAEC,WAAW,CAAC,CACvC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  function makeMutableNative(initial) {\n    var handle = (0, _shareables.makeShareableCloneRecursive)({\n      __init: function () {\n        var _e = [new global.Error(), -3, -27];\n        var mutablesTs4 = function () {\n          return makeMutableUI(initial);\n        };\n        mutablesTs4.__closure = {\n          makeMutableUI,\n          initial\n        };\n        mutablesTs4.__workletHash = 2662348200644;\n        mutablesTs4.__initData = _worklet_2662348200644_init_data;\n        mutablesTs4.__stackDetails = _e;\n        return mutablesTs4;\n      }()\n    });\n    var mutable = {\n      get value() {\n        checkInvalidReadDuringRender();\n        var uiValueGetter = (0, _threads.executeOnUIRuntimeSync)(function () {\n          var _e = [new global.Error(), 1, -27];\n          var mutablesTs5 = function (sv) {\n            return sv.value;\n          };\n          mutablesTs5.__closure = {};\n          mutablesTs5.__workletHash = 9616099880145;\n          mutablesTs5.__initData = _worklet_9616099880145_init_data;\n          mutablesTs5.__stackDetails = _e;\n          return mutablesTs5;\n        }());\n        return uiValueGetter(mutable);\n      },\n      set value(newValue) {\n        checkInvalidWriteDuringRender();\n        (0, _threads.runOnUI)(function () {\n          var _e = [new global.Error(), -3, -27];\n          var mutablesTs6 = function () {\n            mutable.value = newValue;\n          };\n          mutablesTs6.__closure = {\n            mutable,\n            newValue\n          };\n          mutablesTs6.__workletHash = 16360772742581;\n          mutablesTs6.__initData = _worklet_16360772742581_init_data;\n          mutablesTs6.__stackDetails = _e;\n          return mutablesTs6;\n        }())();\n      },\n      get _value() {\n        throw new _errors.ReanimatedError('Reading from `_value` directly is only possible on the UI runtime. Perhaps you passed an Animated Style to a non-animated component?');\n      },\n      set _value(_newValue) {\n        throw new _errors.ReanimatedError('Setting `_value` directly is only possible on the UI runtime. Perhaps you want to assign to `value` instead?');\n      },\n      modify: function (modifier) {\n        var forceUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n        (0, _threads.runOnUI)(function () {\n          var _e = [new global.Error(), -4, -27];\n          var mutablesTs7 = function () {\n            mutable.modify(modifier, forceUpdate);\n          };\n          mutablesTs7.__closure = {\n            mutable,\n            modifier,\n            forceUpdate\n          };\n          mutablesTs7.__workletHash = 1667173883347;\n          mutablesTs7.__initData = _worklet_1667173883347_init_data;\n          mutablesTs7.__stackDetails = _e;\n          return mutablesTs7;\n        }())();\n      },\n      addListener: () => {\n        throw new _errors.ReanimatedError('Adding listeners is only possible on the UI runtime.');\n      },\n      removeListener: () => {\n        throw new _errors.ReanimatedError('Removing listeners is only possible on the UI runtime.');\n      },\n      _isReanimatedSharedValue: true\n    };\n    hideInternalValueProp(mutable);\n    addCompilerSafeGetAndSet(mutable);\n    _shareableMappingCache.shareableMappingCache.set(mutable, handle);\n    return mutable;\n  }\n  function makeMutableWeb(initial) {\n    var value = initial;\n    var listeners = new Map();\n    var mutable = {\n      get value() {\n        checkInvalidReadDuringRender();\n        return value;\n      },\n      set value(newValue) {\n        checkInvalidWriteDuringRender();\n        (0, _valueSetter.valueSetter)(mutable, newValue);\n      },\n      get _value() {\n        return value;\n      },\n      set _value(newValue) {\n        value = newValue;\n        listeners.forEach(listener => {\n          listener(newValue);\n        });\n      },\n      modify: function (modifier) {\n        var forceUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n        (0, _valueSetter.valueSetter)(mutable, modifier !== undefined ? modifier(mutable.value) : mutable.value, forceUpdate);\n      },\n      addListener: (id, listener) => {\n        listeners.set(id, listener);\n      },\n      removeListener: id => {\n        listeners.delete(id);\n      },\n      _isReanimatedSharedValue: true\n    };\n    hideInternalValueProp(mutable);\n    addCompilerSafeGetAndSet(mutable);\n    return mutable;\n  }\n  var makeMutable = exports.makeMutable = SHOULD_BE_USE_WEB ? makeMutableWeb : makeMutableNative;\n});", "lineCount": 317, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "makeMutableUI"], [7, 23, 1, 13], [7, 26, 1, 13, "exports"], [7, 33, 1, 13], [7, 34, 1, 13, "makeMutable"], [7, 45, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_errors"], [8, 13, 3, 0], [8, 16, 3, 0, "require"], [8, 23, 3, 0], [8, 24, 3, 0, "_dependencyMap"], [8, 38, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_logger"], [9, 13, 4, 0], [9, 16, 4, 0, "require"], [9, 23, 4, 0], [9, 24, 4, 0, "_dependencyMap"], [9, 38, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_PlatformChecker"], [10, 22, 5, 0], [10, 25, 5, 0, "require"], [10, 32, 5, 0], [10, 33, 5, 0, "_dependencyMap"], [10, 47, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_reactUtils"], [11, 17, 6, 0], [11, 20, 6, 0, "require"], [11, 27, 6, 0], [11, 28, 6, 0, "_dependencyMap"], [11, 42, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_shareableMappingCache"], [12, 28, 7, 0], [12, 31, 7, 0, "require"], [12, 38, 7, 0], [12, 39, 7, 0, "_dependencyMap"], [12, 53, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_shareables"], [13, 17, 8, 0], [13, 20, 8, 0, "require"], [13, 27, 8, 0], [13, 28, 8, 0, "_dependencyMap"], [13, 42, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_threads"], [14, 14, 9, 0], [14, 17, 9, 0, "require"], [14, 24, 9, 0], [14, 25, 9, 0, "_dependencyMap"], [14, 39, 9, 0], [15, 2, 10, 0], [15, 6, 10, 0, "_valueSetter"], [15, 18, 10, 0], [15, 21, 10, 0, "require"], [15, 28, 10, 0], [15, 29, 10, 0, "_dependencyMap"], [15, 43, 10, 0], [16, 2, 12, 0], [16, 6, 12, 6, "SHOULD_BE_USE_WEB"], [16, 23, 12, 23], [16, 26, 12, 26], [16, 30, 12, 26, "shouldBeUseWeb"], [16, 61, 12, 40], [16, 63, 12, 41], [16, 64, 12, 42], [17, 2, 14, 0], [17, 11, 14, 9, "shouldWarnAboutAccessDuringRender"], [17, 44, 14, 42, "shouldWarnAboutAccessDuringRender"], [17, 45, 14, 42], [17, 47, 14, 45], [18, 4, 15, 2], [18, 11, 15, 9, "__DEV__"], [18, 18, 15, 16], [18, 22, 15, 20], [18, 26, 15, 20, "isReactRendering"], [18, 54, 15, 36], [18, 56, 15, 37], [18, 57, 15, 38], [18, 61, 15, 42], [18, 62, 15, 43], [18, 66, 15, 43, "isFirstReactRender"], [18, 96, 15, 61], [18, 98, 15, 62], [18, 99, 15, 63], [19, 2, 16, 0], [20, 2, 18, 0], [20, 11, 18, 9, "checkInvalidReadDuringRender"], [20, 39, 18, 37, "checkInvalidReadDuringRender"], [20, 40, 18, 37], [20, 42, 18, 40], [21, 4, 19, 2], [21, 8, 19, 6, "shouldWarnAboutAccessDuringRender"], [21, 41, 19, 39], [21, 42, 19, 40], [21, 43, 19, 41], [21, 45, 19, 43], [22, 6, 20, 4, "logger"], [22, 20, 20, 10], [22, 21, 20, 11, "warn"], [22, 25, 20, 15], [22, 26, 21, 6], [22, 207, 21, 187], [22, 209, 22, 6], [23, 8, 22, 8, "strict"], [23, 14, 22, 14], [23, 16, 22, 16], [24, 6, 22, 21], [24, 7, 23, 4], [24, 8, 23, 5], [25, 4, 24, 2], [26, 2, 25, 0], [27, 2, 27, 0], [27, 11, 27, 9, "checkInvalidWriteDuringRender"], [27, 40, 27, 38, "checkInvalidWriteDuringRender"], [27, 41, 27, 38], [27, 43, 27, 41], [28, 4, 28, 2], [28, 8, 28, 6, "shouldWarnAboutAccessDuringRender"], [28, 41, 28, 39], [28, 42, 28, 40], [28, 43, 28, 41], [28, 45, 28, 43], [29, 6, 29, 4, "logger"], [29, 20, 29, 10], [29, 21, 29, 11, "warn"], [29, 25, 29, 15], [29, 26, 30, 6], [29, 205, 30, 185], [29, 207, 31, 6], [30, 8, 31, 8, "strict"], [30, 14, 31, 14], [30, 16, 31, 16], [31, 6, 31, 21], [31, 7, 32, 4], [31, 8, 32, 5], [32, 4, 33, 2], [33, 2, 34, 0], [34, 2, 34, 1], [34, 6, 34, 1, "_worklet_7577286896107_init_data"], [34, 38, 34, 1], [35, 4, 34, 1, "code"], [35, 8, 34, 1], [36, 4, 34, 1, "location"], [36, 12, 34, 1], [37, 4, 34, 1, "sourceMap"], [37, 13, 34, 1], [38, 4, 34, 1, "version"], [38, 11, 34, 1], [39, 2, 34, 1], [40, 2, 40, 0], [41, 0, 41, 0], [42, 0, 42, 0], [43, 0, 43, 0], [44, 0, 44, 0], [45, 0, 45, 0], [46, 0, 46, 0], [47, 0, 47, 0], [48, 0, 48, 0], [49, 2, 40, 0], [49, 6, 40, 0, "addCompilerSafeGetAndSet"], [49, 30, 40, 0], [49, 33, 49, 0], [50, 4, 49, 0], [50, 8, 49, 0, "_e"], [50, 10, 49, 0], [50, 18, 49, 0, "global"], [50, 24, 49, 0], [50, 25, 49, 0, "Error"], [50, 30, 49, 0], [51, 4, 49, 0], [51, 8, 49, 0, "addCompilerSafeGetAndSet"], [51, 32, 49, 0], [51, 44, 49, 0, "addCompilerSafeGetAndSet"], [51, 45, 49, 41, "mutable"], [51, 52, 49, 71], [51, 54, 49, 79], [52, 6, 51, 2, "Object"], [52, 12, 51, 8], [52, 13, 51, 9, "defineProperties"], [52, 29, 51, 25], [52, 30, 51, 26, "mutable"], [52, 37, 51, 33], [52, 39, 51, 35], [53, 8, 52, 4, "get"], [53, 11, 52, 7], [53, 13, 52, 9], [54, 10, 53, 6, "value"], [54, 15, 53, 11, "value"], [54, 16, 53, 11], [54, 18, 53, 14], [55, 12, 54, 8], [55, 19, 54, 15, "mutable"], [55, 26, 54, 22], [55, 27, 54, 23, "value"], [55, 32, 54, 28], [56, 10, 55, 6], [56, 11, 55, 7], [57, 10, 56, 6, "configurable"], [57, 22, 56, 18], [57, 24, 56, 20], [57, 29, 56, 25], [58, 10, 57, 6, "enumerable"], [58, 20, 57, 16], [58, 22, 57, 18], [59, 8, 58, 4], [59, 9, 58, 5], [60, 8, 59, 4, "set"], [60, 11, 59, 7], [60, 13, 59, 9], [61, 10, 60, 6, "value"], [61, 15, 60, 11, "value"], [61, 16, 60, 12, "newValue"], [61, 24, 60, 55], [61, 26, 60, 57], [62, 12, 61, 8], [62, 16, 62, 10], [62, 23, 62, 17, "newValue"], [62, 31, 62, 25], [62, 36, 62, 30], [62, 46, 62, 40], [63, 12, 63, 10], [64, 12, 64, 10], [64, 13, 64, 12, "newValue"], [64, 21, 64, 20], [64, 22, 64, 49, "__isAnimationDefinition"], [64, 45, 64, 72], [64, 47, 65, 10], [65, 14, 66, 10, "mutable"], [65, 21, 66, 17], [65, 22, 66, 18, "value"], [65, 27, 66, 23], [65, 30, 66, 27, "newValue"], [65, 38, 66, 35], [65, 39, 66, 64, "mutable"], [65, 46, 66, 71], [65, 47, 66, 72, "value"], [65, 52, 66, 77], [65, 53, 66, 78], [66, 12, 67, 8], [66, 13, 67, 9], [66, 19, 67, 15], [67, 14, 68, 10, "mutable"], [67, 21, 68, 17], [67, 22, 68, 18, "value"], [67, 27, 68, 23], [67, 30, 68, 26, "newValue"], [67, 38, 68, 43], [68, 12, 69, 8], [69, 10, 70, 6], [69, 11, 70, 7], [70, 10, 71, 6, "configurable"], [70, 22, 71, 18], [70, 24, 71, 20], [70, 29, 71, 25], [71, 10, 72, 6, "enumerable"], [71, 20, 72, 16], [71, 22, 72, 18], [72, 8, 73, 4], [73, 6, 74, 2], [73, 7, 74, 3], [73, 8, 74, 4], [74, 4, 75, 0], [74, 5, 75, 1], [75, 4, 75, 1, "addCompilerSafeGetAndSet"], [75, 28, 75, 1], [75, 29, 75, 1, "__closure"], [75, 38, 75, 1], [76, 4, 75, 1, "addCompilerSafeGetAndSet"], [76, 28, 75, 1], [76, 29, 75, 1, "__workletHash"], [76, 42, 75, 1], [77, 4, 75, 1, "addCompilerSafeGetAndSet"], [77, 28, 75, 1], [77, 29, 75, 1, "__initData"], [77, 39, 75, 1], [77, 42, 75, 1, "_worklet_7577286896107_init_data"], [77, 74, 75, 1], [78, 4, 75, 1, "addCompilerSafeGetAndSet"], [78, 28, 75, 1], [78, 29, 75, 1, "__stackDetails"], [78, 43, 75, 1], [78, 46, 75, 1, "_e"], [78, 48, 75, 1], [79, 4, 75, 1], [79, 11, 75, 1, "addCompilerSafeGetAndSet"], [79, 35, 75, 1], [80, 2, 75, 1], [80, 3, 49, 0], [81, 2, 76, 0], [82, 0, 77, 0], [83, 0, 78, 0], [84, 0, 79, 0], [85, 0, 80, 0], [86, 0, 81, 0], [87, 0, 82, 0], [88, 0, 83, 0], [89, 0, 84, 0], [90, 0, 85, 0], [91, 0, 86, 0], [92, 0, 87, 0], [93, 0, 88, 0], [94, 2, 76, 0], [94, 6, 76, 0, "_worklet_12520546980376_init_data"], [94, 39, 76, 0], [95, 4, 76, 0, "code"], [95, 8, 76, 0], [96, 4, 76, 0, "location"], [96, 12, 76, 0], [97, 4, 76, 0, "sourceMap"], [97, 13, 76, 0], [98, 4, 76, 0, "version"], [98, 11, 76, 0], [99, 2, 76, 0], [100, 2, 76, 0], [100, 6, 76, 0, "hideInternalValueProp"], [100, 27, 76, 0], [100, 30, 89, 0], [101, 4, 89, 0], [101, 8, 89, 0, "_e"], [101, 10, 89, 0], [101, 18, 89, 0, "global"], [101, 24, 89, 0], [101, 25, 89, 0, "Error"], [101, 30, 89, 0], [102, 4, 89, 0], [102, 8, 89, 0, "hideInternalValueProp"], [102, 29, 89, 0], [102, 41, 89, 0, "hideInternalValueProp"], [102, 42, 89, 38, "mutable"], [102, 49, 89, 68], [102, 51, 89, 70], [103, 6, 91, 2, "Object"], [103, 12, 91, 8], [103, 13, 91, 9, "defineProperty"], [103, 27, 91, 23], [103, 28, 91, 24, "mutable"], [103, 35, 91, 31], [103, 37, 91, 33], [103, 45, 91, 41], [103, 47, 91, 43], [104, 8, 92, 4, "configurable"], [104, 20, 92, 16], [104, 22, 92, 18], [104, 27, 92, 23], [105, 8, 93, 4, "enumerable"], [105, 18, 93, 14], [105, 20, 93, 16], [106, 6, 94, 2], [106, 7, 94, 3], [106, 8, 94, 4], [107, 4, 95, 0], [107, 5, 95, 1], [108, 4, 95, 1, "hideInternalValueProp"], [108, 25, 95, 1], [108, 26, 95, 1, "__closure"], [108, 35, 95, 1], [109, 4, 95, 1, "hideInternalValueProp"], [109, 25, 95, 1], [109, 26, 95, 1, "__workletHash"], [109, 39, 95, 1], [110, 4, 95, 1, "hideInternalValueProp"], [110, 25, 95, 1], [110, 26, 95, 1, "__initData"], [110, 36, 95, 1], [110, 39, 95, 1, "_worklet_12520546980376_init_data"], [110, 72, 95, 1], [111, 4, 95, 1, "hideInternalValueProp"], [111, 25, 95, 1], [111, 26, 95, 1, "__stackDetails"], [111, 40, 95, 1], [111, 43, 95, 1, "_e"], [111, 45, 95, 1], [112, 4, 95, 1], [112, 11, 95, 1, "hideInternalValueProp"], [112, 32, 95, 1], [113, 2, 95, 1], [113, 3, 89, 0], [114, 2, 89, 0], [114, 6, 89, 0, "_worklet_3490228900074_init_data"], [114, 38, 89, 0], [115, 4, 89, 0, "code"], [115, 8, 89, 0], [116, 4, 89, 0, "location"], [116, 12, 89, 0], [117, 4, 89, 0, "sourceMap"], [117, 13, 89, 0], [118, 4, 89, 0, "version"], [118, 11, 89, 0], [119, 2, 89, 0], [120, 2, 89, 0], [120, 6, 89, 0, "makeMutableUI"], [120, 19, 89, 0], [120, 22, 89, 0, "exports"], [120, 29, 89, 0], [120, 30, 89, 0, "makeMutableUI"], [120, 43, 89, 0], [120, 46, 97, 7], [121, 4, 97, 7], [121, 8, 97, 7, "_e"], [121, 10, 97, 7], [121, 18, 97, 7, "global"], [121, 24, 97, 7], [121, 25, 97, 7, "Error"], [121, 30, 97, 7], [122, 4, 97, 7], [122, 8, 97, 7, "makeMutableUI"], [122, 21, 97, 7], [122, 33, 97, 7, "makeMutableUI"], [122, 34, 97, 37, "initial"], [122, 41, 97, 51], [122, 43, 97, 69], [123, 6, 99, 2], [123, 10, 99, 8, "listeners"], [123, 19, 99, 17], [123, 22, 99, 20], [123, 26, 99, 24, "Map"], [123, 29, 99, 27], [123, 30, 99, 53], [123, 31, 99, 54], [124, 6, 100, 2], [124, 10, 100, 6, "value"], [124, 15, 100, 11], [124, 18, 100, 14, "initial"], [124, 25, 100, 21], [125, 6, 102, 2], [125, 10, 102, 8, "mutable"], [125, 17, 102, 38], [125, 20, 102, 41], [126, 8, 103, 4], [126, 12, 103, 8, "value"], [126, 17, 103, 13, "value"], [126, 18, 103, 13], [126, 20, 103, 16], [127, 10, 104, 6], [127, 17, 104, 13, "value"], [127, 22, 104, 18], [128, 8, 105, 4], [128, 9, 105, 5], [129, 8, 106, 4], [129, 12, 106, 8, "value"], [129, 17, 106, 13, "value"], [129, 18, 106, 14, "newValue"], [129, 26, 106, 22], [129, 28, 106, 24], [130, 10, 107, 6], [130, 14, 107, 6, "valueSetter"], [130, 38, 107, 17], [130, 40, 107, 18, "mutable"], [130, 47, 107, 25], [130, 49, 107, 45, "newValue"], [130, 57, 107, 53], [130, 58, 107, 54], [131, 8, 108, 4], [131, 9, 108, 5], [132, 8, 109, 4], [132, 12, 109, 8, "_value"], [132, 18, 109, 14, "_value"], [132, 19, 109, 14], [132, 21, 109, 24], [133, 10, 110, 6], [133, 17, 110, 13, "value"], [133, 22, 110, 18], [134, 8, 111, 4], [134, 9, 111, 5], [135, 8, 112, 4], [135, 12, 112, 8, "_value"], [135, 18, 112, 14, "_value"], [135, 19, 112, 15, "newValue"], [135, 27, 112, 30], [135, 29, 112, 32], [136, 10, 113, 6, "value"], [136, 15, 113, 11], [136, 18, 113, 14, "newValue"], [136, 26, 113, 22], [137, 10, 114, 6, "listeners"], [137, 19, 114, 15], [137, 20, 114, 16, "for<PERSON>ach"], [137, 27, 114, 23], [137, 28, 114, 25, "listener"], [137, 36, 114, 33], [137, 40, 114, 38], [138, 12, 115, 8, "listener"], [138, 20, 115, 16], [138, 21, 115, 17, "newValue"], [138, 29, 115, 25], [138, 30, 115, 26], [139, 10, 116, 6], [139, 11, 116, 7], [139, 12, 116, 8], [140, 8, 117, 4], [140, 9, 117, 5], [141, 8, 118, 4, "modify"], [141, 14, 118, 10], [141, 16, 118, 12], [141, 25, 118, 12, "modify"], [141, 26, 118, 13, "modifier"], [141, 34, 118, 21], [141, 36, 118, 46], [142, 10, 118, 46], [142, 14, 118, 23, "forceUpdate"], [142, 25, 118, 34], [142, 28, 118, 34, "arguments"], [142, 37, 118, 34], [142, 38, 118, 34, "length"], [142, 44, 118, 34], [142, 52, 118, 34, "arguments"], [142, 61, 118, 34], [142, 69, 118, 34, "undefined"], [142, 78, 118, 34], [142, 81, 118, 34, "arguments"], [142, 90, 118, 34], [142, 96, 118, 37], [142, 100, 118, 41], [143, 10, 119, 6], [143, 14, 119, 6, "valueSetter"], [143, 38, 119, 17], [143, 40, 120, 8, "mutable"], [143, 47, 120, 15], [143, 49, 121, 8, "modifier"], [143, 57, 121, 16], [143, 62, 121, 21, "undefined"], [143, 71, 121, 30], [143, 74, 121, 33, "modifier"], [143, 82, 121, 41], [143, 83, 121, 42, "value"], [143, 88, 121, 47], [143, 89, 121, 48], [143, 92, 121, 51, "value"], [143, 97, 121, 56], [143, 99, 122, 8, "forceUpdate"], [143, 110, 123, 6], [143, 111, 123, 7], [144, 8, 124, 4], [144, 9, 124, 5], [145, 8, 125, 4, "addListener"], [145, 19, 125, 15], [145, 21, 125, 17, "addListener"], [145, 22, 125, 18, "id"], [145, 24, 125, 28], [145, 26, 125, 30, "listener"], [145, 34, 125, 55], [145, 39, 125, 60], [146, 10, 126, 6, "listeners"], [146, 19, 126, 15], [146, 20, 126, 16, "set"], [146, 23, 126, 19], [146, 24, 126, 20, "id"], [146, 26, 126, 22], [146, 28, 126, 24, "listener"], [146, 36, 126, 32], [146, 37, 126, 33], [147, 8, 127, 4], [147, 9, 127, 5], [148, 8, 128, 4, "removeListener"], [148, 22, 128, 18], [148, 24, 128, 21, "id"], [148, 26, 128, 31], [148, 30, 128, 36], [149, 10, 129, 6, "listeners"], [149, 19, 129, 15], [149, 20, 129, 16, "delete"], [149, 26, 129, 22], [149, 27, 129, 23, "id"], [149, 29, 129, 25], [149, 30, 129, 26], [150, 8, 130, 4], [150, 9, 130, 5], [151, 8, 132, 4, "_animation"], [151, 18, 132, 14], [151, 20, 132, 16], [151, 24, 132, 20], [152, 8, 133, 4, "_isReanimatedSharedValue"], [152, 32, 133, 28], [152, 34, 133, 30], [153, 6, 134, 2], [153, 7, 134, 3], [154, 6, 136, 2, "hideInternalValueProp"], [154, 27, 136, 23], [154, 28, 136, 24, "mutable"], [154, 35, 136, 31], [154, 36, 136, 32], [155, 6, 137, 2, "addCompilerSafeGetAndSet"], [155, 30, 137, 26], [155, 31, 137, 27, "mutable"], [155, 38, 137, 34], [155, 39, 137, 35], [156, 6, 139, 2], [156, 13, 139, 9, "mutable"], [156, 20, 139, 16], [157, 4, 140, 0], [157, 5, 140, 1], [158, 4, 140, 1, "makeMutableUI"], [158, 17, 140, 1], [158, 18, 140, 1, "__closure"], [158, 27, 140, 1], [159, 6, 140, 1, "valueSetter"], [159, 17, 140, 1], [159, 19, 107, 6, "valueSetter"], [159, 43, 107, 17], [160, 6, 107, 17, "hideInternalValueProp"], [160, 27, 107, 17], [161, 6, 107, 17, "addCompilerSafeGetAndSet"], [162, 4, 107, 17], [163, 4, 107, 17, "makeMutableUI"], [163, 17, 107, 17], [163, 18, 107, 17, "__workletHash"], [163, 31, 107, 17], [164, 4, 107, 17, "makeMutableUI"], [164, 17, 107, 17], [164, 18, 107, 17, "__initData"], [164, 28, 107, 17], [164, 31, 107, 17, "_worklet_3490228900074_init_data"], [164, 63, 107, 17], [165, 4, 107, 17, "makeMutableUI"], [165, 17, 107, 17], [165, 18, 107, 17, "__stackDetails"], [165, 32, 107, 17], [165, 35, 107, 17, "_e"], [165, 37, 107, 17], [166, 4, 107, 17], [166, 11, 107, 17, "makeMutableUI"], [166, 24, 107, 17], [167, 2, 107, 17], [167, 3, 97, 7], [168, 2, 97, 7], [168, 6, 97, 7, "_worklet_2662348200644_init_data"], [168, 38, 97, 7], [169, 4, 97, 7, "code"], [169, 8, 97, 7], [170, 4, 97, 7, "location"], [170, 12, 97, 7], [171, 4, 97, 7, "sourceMap"], [171, 13, 97, 7], [172, 4, 97, 7, "version"], [172, 11, 97, 7], [173, 2, 97, 7], [174, 2, 97, 7], [174, 6, 97, 7, "_worklet_9616099880145_init_data"], [174, 38, 97, 7], [175, 4, 97, 7, "code"], [175, 8, 97, 7], [176, 4, 97, 7, "location"], [176, 12, 97, 7], [177, 4, 97, 7, "sourceMap"], [177, 13, 97, 7], [178, 4, 97, 7, "version"], [178, 11, 97, 7], [179, 2, 97, 7], [180, 2, 97, 7], [180, 6, 97, 7, "_worklet_16360772742581_init_data"], [180, 39, 97, 7], [181, 4, 97, 7, "code"], [181, 8, 97, 7], [182, 4, 97, 7, "location"], [182, 12, 97, 7], [183, 4, 97, 7, "sourceMap"], [183, 13, 97, 7], [184, 4, 97, 7, "version"], [184, 11, 97, 7], [185, 2, 97, 7], [186, 2, 97, 7], [186, 6, 97, 7, "_worklet_1667173883347_init_data"], [186, 38, 97, 7], [187, 4, 97, 7, "code"], [187, 8, 97, 7], [188, 4, 97, 7, "location"], [188, 12, 97, 7], [189, 4, 97, 7, "sourceMap"], [189, 13, 97, 7], [190, 4, 97, 7, "version"], [190, 11, 97, 7], [191, 2, 97, 7], [192, 2, 142, 0], [192, 11, 142, 9, "makeMutableNative"], [192, 28, 142, 26, "makeMutableNative"], [192, 29, 142, 34, "initial"], [192, 36, 142, 48], [192, 38, 142, 66], [193, 4, 143, 2], [193, 8, 143, 8, "handle"], [193, 14, 143, 14], [193, 17, 143, 17], [193, 21, 143, 17, "makeShareableCloneRecursive"], [193, 60, 143, 44], [193, 62, 143, 45], [194, 6, 144, 4, "__init"], [194, 12, 144, 10], [194, 14, 144, 12], [195, 8, 144, 12], [195, 12, 144, 12, "_e"], [195, 14, 144, 12], [195, 22, 144, 12, "global"], [195, 28, 144, 12], [195, 29, 144, 12, "Error"], [195, 34, 144, 12], [196, 8, 144, 12], [196, 12, 144, 12, "mutablesTs4"], [196, 23, 144, 12], [196, 35, 144, 12, "mutablesTs4"], [196, 36, 144, 12], [196, 38, 144, 18], [197, 10, 146, 6], [197, 17, 146, 13, "makeMutableUI"], [197, 30, 146, 26], [197, 31, 146, 27, "initial"], [197, 38, 146, 34], [197, 39, 146, 35], [198, 8, 147, 4], [198, 9, 147, 5], [199, 8, 147, 5, "mutablesTs4"], [199, 19, 147, 5], [199, 20, 147, 5, "__closure"], [199, 29, 147, 5], [200, 10, 147, 5, "makeMutableUI"], [200, 23, 147, 5], [201, 10, 147, 5, "initial"], [202, 8, 147, 5], [203, 8, 147, 5, "mutablesTs4"], [203, 19, 147, 5], [203, 20, 147, 5, "__workletHash"], [203, 33, 147, 5], [204, 8, 147, 5, "mutablesTs4"], [204, 19, 147, 5], [204, 20, 147, 5, "__initData"], [204, 30, 147, 5], [204, 33, 147, 5, "_worklet_2662348200644_init_data"], [204, 65, 147, 5], [205, 8, 147, 5, "mutablesTs4"], [205, 19, 147, 5], [205, 20, 147, 5, "__stackDetails"], [205, 34, 147, 5], [205, 37, 147, 5, "_e"], [205, 39, 147, 5], [206, 8, 147, 5], [206, 15, 147, 5, "mutablesTs4"], [206, 26, 147, 5], [207, 6, 147, 5], [207, 7, 144, 12], [208, 4, 148, 2], [208, 5, 148, 3], [208, 6, 148, 4], [209, 4, 150, 2], [209, 8, 150, 8, "mutable"], [209, 15, 150, 38], [209, 18, 150, 41], [210, 6, 151, 4], [210, 10, 151, 8, "value"], [210, 15, 151, 13, "value"], [210, 16, 151, 13], [210, 18, 151, 23], [211, 8, 152, 6, "checkInvalidReadDuringRender"], [211, 36, 152, 34], [211, 37, 152, 35], [211, 38, 152, 36], [212, 8, 153, 6], [212, 12, 153, 12, "uiValueGetter"], [212, 25, 153, 25], [212, 28, 153, 28], [212, 32, 153, 28, "executeOnUIRuntimeSync"], [212, 63, 153, 50], [212, 65, 153, 51], [213, 10, 153, 51], [213, 14, 153, 51, "_e"], [213, 16, 153, 51], [213, 24, 153, 51, "global"], [213, 30, 153, 51], [213, 31, 153, 51, "Error"], [213, 36, 153, 51], [214, 10, 153, 51], [214, 14, 153, 51, "mutablesTs5"], [214, 25, 153, 51], [214, 37, 153, 51, "mutablesTs5"], [214, 38, 153, 52, "sv"], [214, 40, 153, 70], [214, 42, 153, 75], [215, 12, 154, 8], [215, 19, 154, 15, "sv"], [215, 21, 154, 17], [215, 22, 154, 18, "value"], [215, 27, 154, 23], [216, 10, 155, 6], [216, 11, 155, 7], [217, 10, 155, 7, "mutablesTs5"], [217, 21, 155, 7], [217, 22, 155, 7, "__closure"], [217, 31, 155, 7], [218, 10, 155, 7, "mutablesTs5"], [218, 21, 155, 7], [218, 22, 155, 7, "__workletHash"], [218, 35, 155, 7], [219, 10, 155, 7, "mutablesTs5"], [219, 21, 155, 7], [219, 22, 155, 7, "__initData"], [219, 32, 155, 7], [219, 35, 155, 7, "_worklet_9616099880145_init_data"], [219, 67, 155, 7], [220, 10, 155, 7, "mutablesTs5"], [220, 21, 155, 7], [220, 22, 155, 7, "__stackDetails"], [220, 36, 155, 7], [220, 39, 155, 7, "_e"], [220, 41, 155, 7], [221, 10, 155, 7], [221, 17, 155, 7, "mutablesTs5"], [221, 28, 155, 7], [222, 8, 155, 7], [222, 9, 153, 51], [222, 11, 155, 7], [222, 12, 155, 8], [223, 8, 156, 6], [223, 15, 156, 13, "uiValueGetter"], [223, 28, 156, 26], [223, 29, 156, 27, "mutable"], [223, 36, 156, 52], [223, 37, 156, 53], [224, 6, 157, 4], [224, 7, 157, 5], [225, 6, 158, 4], [225, 10, 158, 8, "value"], [225, 15, 158, 13, "value"], [225, 16, 158, 14, "newValue"], [225, 24, 158, 22], [225, 26, 158, 24], [226, 8, 159, 6, "checkInvalidWriteDuringRender"], [226, 37, 159, 35], [226, 38, 159, 36], [226, 39, 159, 37], [227, 8, 160, 6], [227, 12, 160, 6, "runOnUI"], [227, 28, 160, 13], [227, 30, 160, 14], [228, 10, 160, 14], [228, 14, 160, 14, "_e"], [228, 16, 160, 14], [228, 24, 160, 14, "global"], [228, 30, 160, 14], [228, 31, 160, 14, "Error"], [228, 36, 160, 14], [229, 10, 160, 14], [229, 14, 160, 14, "mutablesTs6"], [229, 25, 160, 14], [229, 37, 160, 14, "mutablesTs6"], [229, 38, 160, 14], [229, 40, 160, 20], [230, 12, 161, 8, "mutable"], [230, 19, 161, 15], [230, 20, 161, 16, "value"], [230, 25, 161, 21], [230, 28, 161, 24, "newValue"], [230, 36, 161, 32], [231, 10, 162, 6], [231, 11, 162, 7], [232, 10, 162, 7, "mutablesTs6"], [232, 21, 162, 7], [232, 22, 162, 7, "__closure"], [232, 31, 162, 7], [233, 12, 162, 7, "mutable"], [233, 19, 162, 7], [234, 12, 162, 7, "newValue"], [235, 10, 162, 7], [236, 10, 162, 7, "mutablesTs6"], [236, 21, 162, 7], [236, 22, 162, 7, "__workletHash"], [236, 35, 162, 7], [237, 10, 162, 7, "mutablesTs6"], [237, 21, 162, 7], [237, 22, 162, 7, "__initData"], [237, 32, 162, 7], [237, 35, 162, 7, "_worklet_16360772742581_init_data"], [237, 68, 162, 7], [238, 10, 162, 7, "mutablesTs6"], [238, 21, 162, 7], [238, 22, 162, 7, "__stackDetails"], [238, 36, 162, 7], [238, 39, 162, 7, "_e"], [238, 41, 162, 7], [239, 10, 162, 7], [239, 17, 162, 7, "mutablesTs6"], [239, 28, 162, 7], [240, 8, 162, 7], [240, 9, 160, 14], [240, 11, 162, 7], [240, 12, 162, 8], [240, 13, 162, 9], [240, 14, 162, 10], [241, 6, 163, 4], [241, 7, 163, 5], [242, 6, 165, 4], [242, 10, 165, 8, "_value"], [242, 16, 165, 14, "_value"], [242, 17, 165, 14], [242, 19, 165, 24], [243, 8, 166, 6], [243, 14, 166, 12], [243, 18, 166, 16, "ReanimatedError"], [243, 41, 166, 31], [243, 42, 167, 8], [243, 176, 168, 6], [243, 177, 168, 7], [244, 6, 169, 4], [244, 7, 169, 5], [245, 6, 170, 4], [245, 10, 170, 8, "_value"], [245, 16, 170, 14, "_value"], [245, 17, 170, 15, "_newValue"], [245, 26, 170, 31], [245, 28, 170, 33], [246, 8, 171, 6], [246, 14, 171, 12], [246, 18, 171, 16, "ReanimatedError"], [246, 41, 171, 31], [246, 42, 172, 8], [246, 152, 173, 6], [246, 153, 173, 7], [247, 6, 174, 4], [247, 7, 174, 5], [248, 6, 176, 4, "modify"], [248, 12, 176, 10], [248, 14, 176, 12], [248, 23, 176, 12, "modify"], [248, 24, 176, 13, "modifier"], [248, 32, 176, 21], [248, 34, 176, 46], [249, 8, 176, 46], [249, 12, 176, 23, "forceUpdate"], [249, 23, 176, 34], [249, 26, 176, 34, "arguments"], [249, 35, 176, 34], [249, 36, 176, 34, "length"], [249, 42, 176, 34], [249, 50, 176, 34, "arguments"], [249, 59, 176, 34], [249, 67, 176, 34, "undefined"], [249, 76, 176, 34], [249, 79, 176, 34, "arguments"], [249, 88, 176, 34], [249, 94, 176, 37], [249, 98, 176, 41], [250, 8, 177, 6], [250, 12, 177, 6, "runOnUI"], [250, 28, 177, 13], [250, 30, 177, 14], [251, 10, 177, 14], [251, 14, 177, 14, "_e"], [251, 16, 177, 14], [251, 24, 177, 14, "global"], [251, 30, 177, 14], [251, 31, 177, 14, "Error"], [251, 36, 177, 14], [252, 10, 177, 14], [252, 14, 177, 14, "mutablesTs7"], [252, 25, 177, 14], [252, 37, 177, 14, "mutablesTs7"], [252, 38, 177, 14], [252, 40, 177, 20], [253, 12, 178, 8, "mutable"], [253, 19, 178, 15], [253, 20, 178, 16, "modify"], [253, 26, 178, 22], [253, 27, 178, 23, "modifier"], [253, 35, 178, 31], [253, 37, 178, 33, "forceUpdate"], [253, 48, 178, 44], [253, 49, 178, 45], [254, 10, 179, 6], [254, 11, 179, 7], [255, 10, 179, 7, "mutablesTs7"], [255, 21, 179, 7], [255, 22, 179, 7, "__closure"], [255, 31, 179, 7], [256, 12, 179, 7, "mutable"], [256, 19, 179, 7], [257, 12, 179, 7, "modifier"], [257, 20, 179, 7], [258, 12, 179, 7, "forceUpdate"], [259, 10, 179, 7], [260, 10, 179, 7, "mutablesTs7"], [260, 21, 179, 7], [260, 22, 179, 7, "__workletHash"], [260, 35, 179, 7], [261, 10, 179, 7, "mutablesTs7"], [261, 21, 179, 7], [261, 22, 179, 7, "__initData"], [261, 32, 179, 7], [261, 35, 179, 7, "_worklet_1667173883347_init_data"], [261, 67, 179, 7], [262, 10, 179, 7, "mutablesTs7"], [262, 21, 179, 7], [262, 22, 179, 7, "__stackDetails"], [262, 36, 179, 7], [262, 39, 179, 7, "_e"], [262, 41, 179, 7], [263, 10, 179, 7], [263, 17, 179, 7, "mutablesTs7"], [263, 28, 179, 7], [264, 8, 179, 7], [264, 9, 177, 14], [264, 11, 179, 7], [264, 12, 179, 8], [264, 13, 179, 9], [264, 14, 179, 10], [265, 6, 180, 4], [265, 7, 180, 5], [266, 6, 181, 4, "addListener"], [266, 17, 181, 15], [266, 19, 181, 17, "addListener"], [266, 20, 181, 17], [266, 25, 181, 23], [267, 8, 182, 6], [267, 14, 182, 12], [267, 18, 182, 16, "ReanimatedError"], [267, 41, 182, 31], [267, 42, 183, 8], [267, 96, 184, 6], [267, 97, 184, 7], [268, 6, 185, 4], [268, 7, 185, 5], [269, 6, 186, 4, "removeListener"], [269, 20, 186, 18], [269, 22, 186, 20, "removeListener"], [269, 23, 186, 20], [269, 28, 186, 26], [270, 8, 187, 6], [270, 14, 187, 12], [270, 18, 187, 16, "ReanimatedError"], [270, 41, 187, 31], [270, 42, 188, 8], [270, 98, 189, 6], [270, 99, 189, 7], [271, 6, 190, 4], [271, 7, 190, 5], [272, 6, 192, 4, "_isReanimatedSharedValue"], [272, 30, 192, 28], [272, 32, 192, 30], [273, 4, 193, 2], [273, 5, 193, 3], [274, 4, 195, 2, "hideInternalValueProp"], [274, 25, 195, 23], [274, 26, 195, 24, "mutable"], [274, 33, 195, 31], [274, 34, 195, 32], [275, 4, 196, 2, "addCompilerSafeGetAndSet"], [275, 28, 196, 26], [275, 29, 196, 27, "mutable"], [275, 36, 196, 34], [275, 37, 196, 35], [276, 4, 198, 2, "shareableMappingCache"], [276, 48, 198, 23], [276, 49, 198, 24, "set"], [276, 52, 198, 27], [276, 53, 198, 28, "mutable"], [276, 60, 198, 35], [276, 62, 198, 37, "handle"], [276, 68, 198, 43], [276, 69, 198, 44], [277, 4, 199, 2], [277, 11, 199, 9, "mutable"], [277, 18, 199, 16], [278, 2, 200, 0], [279, 2, 202, 0], [279, 11, 202, 9, "makeMutableWeb"], [279, 25, 202, 23, "makeMutableWeb"], [279, 26, 202, 31, "initial"], [279, 33, 202, 45], [279, 35, 202, 63], [280, 4, 203, 2], [280, 8, 203, 6, "value"], [280, 13, 203, 18], [280, 16, 203, 21, "initial"], [280, 23, 203, 28], [281, 4, 204, 2], [281, 8, 204, 8, "listeners"], [281, 17, 204, 17], [281, 20, 204, 20], [281, 24, 204, 24, "Map"], [281, 27, 204, 27], [281, 28, 204, 53], [281, 29, 204, 54], [282, 4, 206, 2], [282, 8, 206, 8, "mutable"], [282, 15, 206, 38], [282, 18, 206, 41], [283, 6, 207, 4], [283, 10, 207, 8, "value"], [283, 15, 207, 13, "value"], [283, 16, 207, 13], [283, 18, 207, 23], [284, 8, 208, 6, "checkInvalidReadDuringRender"], [284, 36, 208, 34], [284, 37, 208, 35], [284, 38, 208, 36], [285, 8, 209, 6], [285, 15, 209, 13, "value"], [285, 20, 209, 18], [286, 6, 210, 4], [286, 7, 210, 5], [287, 6, 211, 4], [287, 10, 211, 8, "value"], [287, 15, 211, 13, "value"], [287, 16, 211, 14, "newValue"], [287, 24, 211, 22], [287, 26, 211, 24], [288, 8, 212, 6, "checkInvalidWriteDuringRender"], [288, 37, 212, 35], [288, 38, 212, 36], [288, 39, 212, 37], [289, 8, 213, 6], [289, 12, 213, 6, "valueSetter"], [289, 36, 213, 17], [289, 38, 213, 18, "mutable"], [289, 45, 213, 25], [289, 47, 213, 45, "newValue"], [289, 55, 213, 53], [289, 56, 213, 54], [290, 6, 214, 4], [290, 7, 214, 5], [291, 6, 216, 4], [291, 10, 216, 8, "_value"], [291, 16, 216, 14, "_value"], [291, 17, 216, 14], [291, 19, 216, 24], [292, 8, 217, 6], [292, 15, 217, 13, "value"], [292, 20, 217, 18], [293, 6, 218, 4], [293, 7, 218, 5], [294, 6, 219, 4], [294, 10, 219, 8, "_value"], [294, 16, 219, 14, "_value"], [294, 17, 219, 15, "newValue"], [294, 25, 219, 30], [294, 27, 219, 32], [295, 8, 220, 6, "value"], [295, 13, 220, 11], [295, 16, 220, 14, "newValue"], [295, 24, 220, 22], [296, 8, 221, 6, "listeners"], [296, 17, 221, 15], [296, 18, 221, 16, "for<PERSON>ach"], [296, 25, 221, 23], [296, 26, 221, 25, "listener"], [296, 34, 221, 33], [296, 38, 221, 38], [297, 10, 222, 8, "listener"], [297, 18, 222, 16], [297, 19, 222, 17, "newValue"], [297, 27, 222, 25], [297, 28, 222, 26], [298, 8, 223, 6], [298, 9, 223, 7], [298, 10, 223, 8], [299, 6, 224, 4], [299, 7, 224, 5], [300, 6, 226, 4, "modify"], [300, 12, 226, 10], [300, 14, 226, 12], [300, 23, 226, 12, "modify"], [300, 24, 226, 13, "modifier"], [300, 32, 226, 21], [300, 34, 226, 46], [301, 8, 226, 46], [301, 12, 226, 23, "forceUpdate"], [301, 23, 226, 34], [301, 26, 226, 34, "arguments"], [301, 35, 226, 34], [301, 36, 226, 34, "length"], [301, 42, 226, 34], [301, 50, 226, 34, "arguments"], [301, 59, 226, 34], [301, 67, 226, 34, "undefined"], [301, 76, 226, 34], [301, 79, 226, 34, "arguments"], [301, 88, 226, 34], [301, 94, 226, 37], [301, 98, 226, 41], [302, 8, 227, 6], [302, 12, 227, 6, "valueSetter"], [302, 36, 227, 17], [302, 38, 228, 8, "mutable"], [302, 45, 228, 15], [302, 47, 229, 8, "modifier"], [302, 55, 229, 16], [302, 60, 229, 21, "undefined"], [302, 69, 229, 30], [302, 72, 229, 33, "modifier"], [302, 80, 229, 41], [302, 81, 229, 42, "mutable"], [302, 88, 229, 49], [302, 89, 229, 50, "value"], [302, 94, 229, 55], [302, 95, 229, 56], [302, 98, 229, 59, "mutable"], [302, 105, 229, 66], [302, 106, 229, 67, "value"], [302, 111, 229, 72], [302, 113, 230, 8, "forceUpdate"], [302, 124, 231, 6], [302, 125, 231, 7], [303, 6, 232, 4], [303, 7, 232, 5], [304, 6, 233, 4, "addListener"], [304, 17, 233, 15], [304, 19, 233, 17, "addListener"], [304, 20, 233, 18, "id"], [304, 22, 233, 28], [304, 24, 233, 30, "listener"], [304, 32, 233, 55], [304, 37, 233, 60], [305, 8, 234, 6, "listeners"], [305, 17, 234, 15], [305, 18, 234, 16, "set"], [305, 21, 234, 19], [305, 22, 234, 20, "id"], [305, 24, 234, 22], [305, 26, 234, 24, "listener"], [305, 34, 234, 32], [305, 35, 234, 33], [306, 6, 235, 4], [306, 7, 235, 5], [307, 6, 236, 4, "removeListener"], [307, 20, 236, 18], [307, 22, 236, 21, "id"], [307, 24, 236, 31], [307, 28, 236, 36], [308, 8, 237, 6, "listeners"], [308, 17, 237, 15], [308, 18, 237, 16, "delete"], [308, 24, 237, 22], [308, 25, 237, 23, "id"], [308, 27, 237, 25], [308, 28, 237, 26], [309, 6, 238, 4], [309, 7, 238, 5], [310, 6, 240, 4, "_isReanimatedSharedValue"], [310, 30, 240, 28], [310, 32, 240, 30], [311, 4, 241, 2], [311, 5, 241, 3], [312, 4, 243, 2, "hideInternalValueProp"], [312, 25, 243, 23], [312, 26, 243, 24, "mutable"], [312, 33, 243, 31], [312, 34, 243, 32], [313, 4, 244, 2, "addCompilerSafeGetAndSet"], [313, 28, 244, 26], [313, 29, 244, 27, "mutable"], [313, 36, 244, 34], [313, 37, 244, 35], [314, 4, 246, 2], [314, 11, 246, 9, "mutable"], [314, 18, 246, 16], [315, 2, 247, 0], [316, 2, 249, 7], [316, 6, 249, 13, "makeMutable"], [316, 17, 249, 24], [316, 20, 249, 24, "exports"], [316, 27, 249, 24], [316, 28, 249, 24, "makeMutable"], [316, 39, 249, 24], [316, 42, 249, 27, "SHOULD_BE_USE_WEB"], [316, 59, 249, 44], [316, 62, 250, 4, "makeMutableWeb"], [316, 76, 250, 18], [316, 79, 251, 4, "makeMutableNative"], [316, 96, 251, 21], [317, 0, 251, 22], [317, 3]], "functionMap": {"names": ["<global>", "shouldWarnAboutAccessDuringRender", "checkInvalidReadDuringRender", "checkInvalidWriteDuringRender", "addCompilerSafeGetAndSet", "Object.defineProperties$argument_1.get.value", "Object.defineProperties$argument_1.set.value", "hideInternalValueProp", "makeMutableUI", "mutable.get__value", "mutable.set__value", "mutable.get___value", "mutable.set___value", "listeners.forEach$argument_0", "mutable.modify", "mutable.addListener", "mutable.removeListener", "makeMutableNative", "makeShareableCloneRecursive$argument_0.__init", "executeOnUIRuntimeSync$argument_0", "runOnUI$argument_0", "makeMutableWeb"], "mappings": "AAA;ACa;CDE;AEE;CFO;AGE;CHO;AIe;MCI;ODE;MEK;OFU;CJK;AOc;CPM;OQE;ICM;KDE;IEC;KFE;IGC;KHE;IIC;wBCE;ODE;KJC;YMC;KNM;iBOC;KPE;oBQC;KRE;CRU;AiBE;YCE;KDG;IRI;mDUE;OVE;KQE;IPC;cUE;OVE;KOC;INE;KMI;ILC;KKI;YHE;cMC;ONE;KGC;iBFC;KEI;oBDC;KCI;CjBU;AqBE;IZK;KYG;IXC;KWG;IVE;KUE;ITC;wBCE;ODE;KSC;YPE;KOM;iBNC;KME;oBLC;KKE;CrBS"}}, "type": "js/module"}]}