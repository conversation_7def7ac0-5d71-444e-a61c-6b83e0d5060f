{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Components/ScrollView/ScrollView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 64}}], "key": "Z6KoJgAYym4YFhr1Vnk2ReqruYk=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../Core/Devtools/openFileInEditor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 68}}], "key": "3vH1y5vrRpr8TQTBUBKQt4U0X5I=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "../../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 48}}], "key": "/m0HqCpVZ4yItbJJaw+YeR/qFWU=", "exportNames": ["*"]}}, {"name": "../Data/LogBoxData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 49}}], "key": "A5Z1ymCQl9OoAWyeygTXLGmRNuU=", "exportNames": ["*"]}}, {"name": "./<PERSON><PERSON><PERSON><PERSON><PERSON>", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 44}}], "key": "b5S9ymGzNnfKvL/8q6rOPQ3F4JA=", "exportNames": ["*"]}}, {"name": "./LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 42}}], "key": "M6ofQu070ZUTf+Oq+Zz+7FQEgjs=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorSection", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 62}}], "key": "psfwCNco8+nKb+3u3V4A+OhHW2E=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[1], \"../../Components/ScrollView/ScrollView\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../../Components/View/View\"));\n  var _openFileInEditor = _interopRequireDefault(require(_dependencyMap[3], \"../../Core/Devtools/openFileInEditor\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[5], \"../../Text/Text\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[6], \"../../Utilities/Platform\"));\n  var LogBoxData = _interopRequireWildcard(require(_dependencyMap[7], \"../Data/LogBoxData\"));\n  var _AnsiHighlight = _interopRequireDefault(require(_dependencyMap[8], \"./AnsiHighlight\"));\n  var _LogBoxButton = _interopRequireDefault(require(_dependencyMap[9], \"./LogBoxButton\"));\n  var _LogBoxInspectorSection = _interopRequireDefault(require(_dependencyMap[10], \"./LogBoxInspectorSection\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[11], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[12], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[13], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxInspectorCodeFrame.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function CodeFrameDisplay(_ref) {\n    var codeFrame = _ref.codeFrame;\n    function getFileName() {\n      var matches = /[^/]*$/.exec(codeFrame.fileName);\n      if (matches && matches.length > 0) {\n        return matches[0];\n      }\n      return codeFrame.fileName;\n    }\n    function getLocation() {\n      var location = codeFrame.location;\n      if (location != null) {\n        return ` (${location.row}:${location.column + 1})`;\n      }\n      return null;\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.box,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.frame,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          horizontal: true,\n          contentContainerStyle: styles.contentContainer,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_AnsiHighlight.default, {\n            style: styles.content,\n            text: codeFrame.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxButton.default, {\n        backgroundColor: {\n          default: 'transparent',\n          pressed: LogBoxStyle.getBackgroundDarkColor(1)\n        },\n        style: styles.button,\n        onPress: () => {\n          (0, _openFileInEditor.default)(codeFrame.fileName, codeFrame.location?.row ?? 0);\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.fileText,\n          children: [getFileName(), getLocation()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 5\n    }, this);\n  }\n  function LogBoxInspectorCodeFrame(props) {\n    var codeFrame = props.codeFrame,\n      componentCodeFrame = props.componentCodeFrame;\n    var sources = [];\n    if (codeFrame != null) {\n      sources.push(codeFrame);\n    }\n    if (componentCodeFrame != null && componentCodeFrame?.content !== codeFrame?.content) {\n      sources.push(componentCodeFrame);\n    }\n    if (sources.length === 0) {\n      return null;\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorSection.default, {\n      heading: sources.length > 1 ? 'Sources' : 'Source',\n      action: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(AppInfo, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 15\n      }, this),\n      children: sources.map((frame, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(CodeFrameDisplay, {\n        codeFrame: frame\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 5\n    }, this);\n  }\n  function AppInfo() {\n    var appInfo = LogBoxData.getAppInfo();\n    if (appInfo == null) {\n      return null;\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxButton.default, {\n      backgroundColor: {\n        default: 'transparent',\n        pressed: appInfo.onPress ? LogBoxStyle.getBackgroundColor(1) : 'transparent'\n      },\n      style: appInfoStyles.buildButton,\n      onPress: appInfo.onPress,\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n        style: appInfoStyles.text,\n        children: [appInfo.appVersion, \" (\", appInfo.engine, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 5\n    }, this);\n  }\n  var appInfoStyles = _StyleSheet.default.create({\n    text: {\n      color: LogBoxStyle.getTextColor(0.4),\n      fontSize: 12,\n      lineHeight: 12\n    },\n    buildButton: {\n      flex: 0,\n      flexGrow: 0,\n      paddingVertical: 4,\n      paddingHorizontal: 5,\n      borderRadius: 5,\n      marginRight: -8\n    }\n  });\n  var styles = _StyleSheet.default.create({\n    box: {\n      backgroundColor: LogBoxStyle.getBackgroundColor(),\n      marginLeft: 10,\n      marginRight: 10,\n      marginTop: 5,\n      borderRadius: 3\n    },\n    frame: {\n      padding: 10,\n      borderBottomColor: LogBoxStyle.getTextColor(0.1),\n      borderBottomWidth: 1\n    },\n    button: {\n      paddingTop: 10,\n      paddingBottom: 10\n    },\n    contentContainer: {\n      minWidth: '100%'\n    },\n    content: {\n      color: LogBoxStyle.getTextColor(1),\n      fontSize: 12,\n      includeFontPadding: false,\n      lineHeight: 20,\n      fontFamily: _Platform.default.select({\n        android: 'monospace',\n        ios: 'Menlo'\n      })\n    },\n    fileText: {\n      color: LogBoxStyle.getTextColor(0.5),\n      textAlign: 'center',\n      flex: 1,\n      fontSize: 12,\n      includeFontPadding: false,\n      lineHeight: 16,\n      fontFamily: _Platform.default.select({\n        android: 'monospace',\n        ios: 'Menlo'\n      })\n    }\n  });\n  var _default = exports.default = LogBoxInspectorCodeFrame;\n});", "lineCount": 208, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [7, 17, 13, 0], [7, 20, 13, 0, "_interopRequireDefault"], [7, 42, 13, 0], [7, 43, 13, 0, "require"], [7, 50, 13, 0], [7, 51, 13, 0, "_dependencyMap"], [7, 65, 13, 0], [8, 2, 14, 0], [8, 6, 14, 0, "_View"], [8, 11, 14, 0], [8, 14, 14, 0, "_interopRequireDefault"], [8, 36, 14, 0], [8, 37, 14, 0, "require"], [8, 44, 14, 0], [8, 45, 14, 0, "_dependencyMap"], [8, 59, 14, 0], [9, 2, 15, 0], [9, 6, 15, 0, "_openFileInEditor"], [9, 23, 15, 0], [9, 26, 15, 0, "_interopRequireDefault"], [9, 48, 15, 0], [9, 49, 15, 0, "require"], [9, 56, 15, 0], [9, 57, 15, 0, "_dependencyMap"], [9, 71, 15, 0], [10, 2, 16, 0], [10, 6, 16, 0, "_StyleSheet"], [10, 17, 16, 0], [10, 20, 16, 0, "_interopRequireDefault"], [10, 42, 16, 0], [10, 43, 16, 0, "require"], [10, 50, 16, 0], [10, 51, 16, 0, "_dependencyMap"], [10, 65, 16, 0], [11, 2, 17, 0], [11, 6, 17, 0, "_Text"], [11, 11, 17, 0], [11, 14, 17, 0, "_interopRequireDefault"], [11, 36, 17, 0], [11, 37, 17, 0, "require"], [11, 44, 17, 0], [11, 45, 17, 0, "_dependencyMap"], [11, 59, 17, 0], [12, 2, 18, 0], [12, 6, 18, 0, "_Platform"], [12, 15, 18, 0], [12, 18, 18, 0, "_interopRequireDefault"], [12, 40, 18, 0], [12, 41, 18, 0, "require"], [12, 48, 18, 0], [12, 49, 18, 0, "_dependencyMap"], [12, 63, 18, 0], [13, 2, 19, 0], [13, 6, 19, 0, "LogBoxData"], [13, 16, 19, 0], [13, 19, 19, 0, "_interopRequireWildcard"], [13, 42, 19, 0], [13, 43, 19, 0, "require"], [13, 50, 19, 0], [13, 51, 19, 0, "_dependencyMap"], [13, 65, 19, 0], [14, 2, 20, 0], [14, 6, 20, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [14, 20, 20, 0], [14, 23, 20, 0, "_interopRequireDefault"], [14, 45, 20, 0], [14, 46, 20, 0, "require"], [14, 53, 20, 0], [14, 54, 20, 0, "_dependencyMap"], [14, 68, 20, 0], [15, 2, 21, 0], [15, 6, 21, 0, "_LogBoxButton"], [15, 19, 21, 0], [15, 22, 21, 0, "_interopRequireDefault"], [15, 44, 21, 0], [15, 45, 21, 0, "require"], [15, 52, 21, 0], [15, 53, 21, 0, "_dependencyMap"], [15, 67, 21, 0], [16, 2, 22, 0], [16, 6, 22, 0, "_LogBoxInspectorSection"], [16, 29, 22, 0], [16, 32, 22, 0, "_interopRequireDefault"], [16, 54, 22, 0], [16, 55, 22, 0, "require"], [16, 62, 22, 0], [16, 63, 22, 0, "_dependencyMap"], [16, 77, 22, 0], [17, 2, 23, 0], [17, 6, 23, 0, "LogBoxStyle"], [17, 17, 23, 0], [17, 20, 23, 0, "_interopRequireWildcard"], [17, 43, 23, 0], [17, 44, 23, 0, "require"], [17, 51, 23, 0], [17, 52, 23, 0, "_dependencyMap"], [17, 66, 23, 0], [18, 2, 24, 0], [18, 6, 24, 0, "React"], [18, 11, 24, 0], [18, 14, 24, 0, "_interopRequireWildcard"], [18, 37, 24, 0], [18, 38, 24, 0, "require"], [18, 45, 24, 0], [18, 46, 24, 0, "_dependencyMap"], [18, 60, 24, 0], [19, 2, 24, 31], [19, 6, 24, 31, "_jsxDevRuntime"], [19, 20, 24, 31], [19, 23, 24, 31, "require"], [19, 30, 24, 31], [19, 31, 24, 31, "_dependencyMap"], [19, 45, 24, 31], [20, 2, 24, 31], [20, 6, 24, 31, "_jsxFileName"], [20, 18, 24, 31], [21, 2, 24, 31], [21, 11, 24, 31, "_interopRequireWildcard"], [21, 35, 24, 31, "e"], [21, 36, 24, 31], [21, 38, 24, 31, "t"], [21, 39, 24, 31], [21, 68, 24, 31, "WeakMap"], [21, 75, 24, 31], [21, 81, 24, 31, "r"], [21, 82, 24, 31], [21, 89, 24, 31, "WeakMap"], [21, 96, 24, 31], [21, 100, 24, 31, "n"], [21, 101, 24, 31], [21, 108, 24, 31, "WeakMap"], [21, 115, 24, 31], [21, 127, 24, 31, "_interopRequireWildcard"], [21, 150, 24, 31], [21, 162, 24, 31, "_interopRequireWildcard"], [21, 163, 24, 31, "e"], [21, 164, 24, 31], [21, 166, 24, 31, "t"], [21, 167, 24, 31], [21, 176, 24, 31, "t"], [21, 177, 24, 31], [21, 181, 24, 31, "e"], [21, 182, 24, 31], [21, 186, 24, 31, "e"], [21, 187, 24, 31], [21, 188, 24, 31, "__esModule"], [21, 198, 24, 31], [21, 207, 24, 31, "e"], [21, 208, 24, 31], [21, 214, 24, 31, "o"], [21, 215, 24, 31], [21, 217, 24, 31, "i"], [21, 218, 24, 31], [21, 220, 24, 31, "f"], [21, 221, 24, 31], [21, 226, 24, 31, "__proto__"], [21, 235, 24, 31], [21, 243, 24, 31, "default"], [21, 250, 24, 31], [21, 252, 24, 31, "e"], [21, 253, 24, 31], [21, 270, 24, 31, "e"], [21, 271, 24, 31], [21, 294, 24, 31, "e"], [21, 295, 24, 31], [21, 320, 24, 31, "e"], [21, 321, 24, 31], [21, 330, 24, 31, "f"], [21, 331, 24, 31], [21, 337, 24, 31, "o"], [21, 338, 24, 31], [21, 341, 24, 31, "t"], [21, 342, 24, 31], [21, 345, 24, 31, "n"], [21, 346, 24, 31], [21, 349, 24, 31, "r"], [21, 350, 24, 31], [21, 358, 24, 31, "o"], [21, 359, 24, 31], [21, 360, 24, 31, "has"], [21, 363, 24, 31], [21, 364, 24, 31, "e"], [21, 365, 24, 31], [21, 375, 24, 31, "o"], [21, 376, 24, 31], [21, 377, 24, 31, "get"], [21, 380, 24, 31], [21, 381, 24, 31, "e"], [21, 382, 24, 31], [21, 385, 24, 31, "o"], [21, 386, 24, 31], [21, 387, 24, 31, "set"], [21, 390, 24, 31], [21, 391, 24, 31, "e"], [21, 392, 24, 31], [21, 394, 24, 31, "f"], [21, 395, 24, 31], [21, 409, 24, 31, "_t"], [21, 411, 24, 31], [21, 415, 24, 31, "e"], [21, 416, 24, 31], [21, 432, 24, 31, "_t"], [21, 434, 24, 31], [21, 441, 24, 31, "hasOwnProperty"], [21, 455, 24, 31], [21, 456, 24, 31, "call"], [21, 460, 24, 31], [21, 461, 24, 31, "e"], [21, 462, 24, 31], [21, 464, 24, 31, "_t"], [21, 466, 24, 31], [21, 473, 24, 31, "i"], [21, 474, 24, 31], [21, 478, 24, 31, "o"], [21, 479, 24, 31], [21, 482, 24, 31, "Object"], [21, 488, 24, 31], [21, 489, 24, 31, "defineProperty"], [21, 503, 24, 31], [21, 508, 24, 31, "Object"], [21, 514, 24, 31], [21, 515, 24, 31, "getOwnPropertyDescriptor"], [21, 539, 24, 31], [21, 540, 24, 31, "e"], [21, 541, 24, 31], [21, 543, 24, 31, "_t"], [21, 545, 24, 31], [21, 552, 24, 31, "i"], [21, 553, 24, 31], [21, 554, 24, 31, "get"], [21, 557, 24, 31], [21, 561, 24, 31, "i"], [21, 562, 24, 31], [21, 563, 24, 31, "set"], [21, 566, 24, 31], [21, 570, 24, 31, "o"], [21, 571, 24, 31], [21, 572, 24, 31, "f"], [21, 573, 24, 31], [21, 575, 24, 31, "_t"], [21, 577, 24, 31], [21, 579, 24, 31, "i"], [21, 580, 24, 31], [21, 584, 24, 31, "f"], [21, 585, 24, 31], [21, 586, 24, 31, "_t"], [21, 588, 24, 31], [21, 592, 24, 31, "e"], [21, 593, 24, 31], [21, 594, 24, 31, "_t"], [21, 596, 24, 31], [21, 607, 24, 31, "f"], [21, 608, 24, 31], [21, 613, 24, 31, "e"], [21, 614, 24, 31], [21, 616, 24, 31, "t"], [21, 617, 24, 31], [22, 2, 31, 0], [22, 11, 31, 9, "CodeFrameDisplay"], [22, 27, 31, 25, "CodeFrameDisplay"], [22, 28, 31, 25, "_ref"], [22, 32, 31, 25], [22, 34, 31, 75], [23, 4, 31, 75], [23, 8, 31, 27, "codeFrame"], [23, 17, 31, 36], [23, 20, 31, 36, "_ref"], [23, 24, 31, 36], [23, 25, 31, 27, "codeFrame"], [23, 34, 31, 36], [24, 4, 32, 2], [24, 13, 32, 11, "getFileName"], [24, 24, 32, 22, "getFileName"], [24, 25, 32, 22], [24, 27, 32, 25], [25, 6, 34, 4], [25, 10, 34, 10, "matches"], [25, 17, 34, 17], [25, 20, 34, 20], [25, 28, 34, 28], [25, 29, 34, 29, "exec"], [25, 33, 34, 33], [25, 34, 34, 34, "codeFrame"], [25, 43, 34, 43], [25, 44, 34, 44, "fileName"], [25, 52, 34, 52], [25, 53, 34, 53], [26, 6, 35, 4], [26, 10, 35, 8, "matches"], [26, 17, 35, 15], [26, 21, 35, 19, "matches"], [26, 28, 35, 26], [26, 29, 35, 27, "length"], [26, 35, 35, 33], [26, 38, 35, 36], [26, 39, 35, 37], [26, 41, 35, 39], [27, 8, 36, 6], [27, 15, 36, 13, "matches"], [27, 22, 36, 20], [27, 23, 36, 21], [27, 24, 36, 22], [27, 25, 36, 23], [28, 6, 37, 4], [29, 6, 40, 4], [29, 13, 40, 11, "codeFrame"], [29, 22, 40, 20], [29, 23, 40, 21, "fileName"], [29, 31, 40, 29], [30, 4, 41, 2], [31, 4, 43, 2], [31, 13, 43, 11, "getLocation"], [31, 24, 43, 22, "getLocation"], [31, 25, 43, 22], [31, 27, 43, 25], [32, 6, 45, 4], [32, 10, 45, 10, "location"], [32, 18, 45, 18], [32, 21, 45, 21, "codeFrame"], [32, 30, 45, 30], [32, 31, 45, 31, "location"], [32, 39, 45, 39], [33, 6, 46, 4], [33, 10, 46, 8, "location"], [33, 18, 46, 16], [33, 22, 46, 20], [33, 26, 46, 24], [33, 28, 46, 26], [34, 8, 47, 6], [34, 15, 47, 13], [34, 20, 47, 18, "location"], [34, 28, 47, 26], [34, 29, 47, 27, "row"], [34, 32, 47, 30], [34, 36, 48, 8, "location"], [34, 44, 48, 16], [34, 45, 48, 17, "column"], [34, 51, 48, 23], [34, 54, 48, 26], [34, 55, 48, 27], [34, 58, 49, 9], [35, 6, 50, 4], [36, 6, 52, 4], [36, 13, 52, 11], [36, 17, 52, 15], [37, 4, 53, 2], [38, 4, 55, 2], [38, 24, 56, 4], [38, 28, 56, 4, "_jsxDevRuntime"], [38, 42, 56, 4], [38, 43, 56, 4, "jsxDEV"], [38, 49, 56, 4], [38, 51, 56, 5, "_View"], [38, 56, 56, 5], [38, 57, 56, 5, "default"], [38, 64, 56, 9], [39, 6, 56, 10, "style"], [39, 11, 56, 15], [39, 13, 56, 17, "styles"], [39, 19, 56, 23], [39, 20, 56, 24, "box"], [39, 23, 56, 28], [40, 6, 56, 28, "children"], [40, 14, 56, 28], [40, 30, 57, 6], [40, 34, 57, 6, "_jsxDevRuntime"], [40, 48, 57, 6], [40, 49, 57, 6, "jsxDEV"], [40, 55, 57, 6], [40, 57, 57, 7, "_View"], [40, 62, 57, 7], [40, 63, 57, 7, "default"], [40, 70, 57, 11], [41, 8, 57, 12, "style"], [41, 13, 57, 17], [41, 15, 57, 19, "styles"], [41, 21, 57, 25], [41, 22, 57, 26, "frame"], [41, 27, 57, 32], [42, 8, 57, 32, "children"], [42, 16, 57, 32], [42, 31, 58, 8], [42, 35, 58, 8, "_jsxDevRuntime"], [42, 49, 58, 8], [42, 50, 58, 8, "jsxDEV"], [42, 56, 58, 8], [42, 58, 58, 9, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [42, 69, 58, 9], [42, 70, 58, 9, "default"], [42, 77, 58, 19], [43, 10, 58, 20, "horizontal"], [43, 20, 58, 30], [44, 10, 58, 31, "contentContainerStyle"], [44, 31, 58, 52], [44, 33, 58, 54, "styles"], [44, 39, 58, 60], [44, 40, 58, 61, "contentContainer"], [44, 56, 58, 78], [45, 10, 58, 78, "children"], [45, 18, 58, 78], [45, 33, 59, 10], [45, 37, 59, 10, "_jsxDevRuntime"], [45, 51, 59, 10], [45, 52, 59, 10, "jsxDEV"], [45, 58, 59, 10], [45, 60, 59, 11, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [45, 74, 59, 11], [45, 75, 59, 11, "default"], [45, 82, 59, 24], [46, 12, 59, 25, "style"], [46, 17, 59, 30], [46, 19, 59, 32, "styles"], [46, 25, 59, 38], [46, 26, 59, 39, "content"], [46, 33, 59, 47], [47, 12, 59, 48, "text"], [47, 16, 59, 52], [47, 18, 59, 54, "codeFrame"], [47, 27, 59, 63], [47, 28, 59, 64, "content"], [48, 10, 59, 72], [49, 12, 59, 72, "fileName"], [49, 20, 59, 72], [49, 22, 59, 72, "_jsxFileName"], [49, 34, 59, 72], [50, 12, 59, 72, "lineNumber"], [50, 22, 59, 72], [51, 12, 59, 72, "columnNumber"], [51, 24, 59, 72], [52, 10, 59, 72], [52, 17, 59, 74], [53, 8, 59, 75], [54, 10, 59, 75, "fileName"], [54, 18, 59, 75], [54, 20, 59, 75, "_jsxFileName"], [54, 32, 59, 75], [55, 10, 59, 75, "lineNumber"], [55, 20, 59, 75], [56, 10, 59, 75, "columnNumber"], [56, 22, 59, 75], [57, 8, 59, 75], [57, 15, 60, 20], [58, 6, 60, 21], [59, 8, 60, 21, "fileName"], [59, 16, 60, 21], [59, 18, 60, 21, "_jsxFileName"], [59, 30, 60, 21], [60, 8, 60, 21, "lineNumber"], [60, 18, 60, 21], [61, 8, 60, 21, "columnNumber"], [61, 20, 60, 21], [62, 6, 60, 21], [62, 13, 61, 12], [62, 14, 61, 13], [62, 29, 62, 6], [62, 33, 62, 6, "_jsxDevRuntime"], [62, 47, 62, 6], [62, 48, 62, 6, "jsxDEV"], [62, 54, 62, 6], [62, 56, 62, 7, "_LogBoxButton"], [62, 69, 62, 7], [62, 70, 62, 7, "default"], [62, 77, 62, 19], [63, 8, 63, 8, "backgroundColor"], [63, 23, 63, 23], [63, 25, 63, 25], [64, 10, 64, 10, "default"], [64, 17, 64, 17], [64, 19, 64, 19], [64, 32, 64, 32], [65, 10, 65, 10, "pressed"], [65, 17, 65, 17], [65, 19, 65, 19, "LogBoxStyle"], [65, 30, 65, 30], [65, 31, 65, 31, "getBackgroundDarkColor"], [65, 53, 65, 53], [65, 54, 65, 54], [65, 55, 65, 55], [66, 8, 66, 8], [66, 9, 66, 10], [67, 8, 67, 8, "style"], [67, 13, 67, 13], [67, 15, 67, 15, "styles"], [67, 21, 67, 21], [67, 22, 67, 22, "button"], [67, 28, 67, 29], [68, 8, 68, 8, "onPress"], [68, 15, 68, 15], [68, 17, 68, 17, "onPress"], [68, 18, 68, 17], [68, 23, 68, 23], [69, 10, 69, 10], [69, 14, 69, 10, "openFileInEditor"], [69, 39, 69, 26], [69, 41, 69, 27, "codeFrame"], [69, 50, 69, 36], [69, 51, 69, 37, "fileName"], [69, 59, 69, 45], [69, 61, 69, 47, "codeFrame"], [69, 70, 69, 56], [69, 71, 69, 57, "location"], [69, 79, 69, 65], [69, 81, 69, 67, "row"], [69, 84, 69, 70], [69, 88, 69, 74], [69, 89, 69, 75], [69, 90, 69, 76], [70, 8, 70, 8], [70, 9, 70, 10], [71, 8, 70, 10, "children"], [71, 16, 70, 10], [71, 31, 71, 8], [71, 35, 71, 8, "_jsxDevRuntime"], [71, 49, 71, 8], [71, 50, 71, 8, "jsxDEV"], [71, 56, 71, 8], [71, 58, 71, 9, "_Text"], [71, 63, 71, 9], [71, 64, 71, 9, "default"], [71, 71, 71, 13], [72, 10, 71, 14, "style"], [72, 15, 71, 19], [72, 17, 71, 21, "styles"], [72, 23, 71, 27], [72, 24, 71, 28, "fileText"], [72, 32, 71, 37], [73, 10, 71, 37, "children"], [73, 18, 71, 37], [73, 21, 72, 11, "getFileName"], [73, 32, 72, 22], [73, 33, 72, 23], [73, 34, 72, 24], [73, 36, 73, 11, "getLocation"], [73, 47, 73, 22], [73, 48, 73, 23], [73, 49, 73, 24], [74, 8, 73, 24], [75, 10, 73, 24, "fileName"], [75, 18, 73, 24], [75, 20, 73, 24, "_jsxFileName"], [75, 32, 73, 24], [76, 10, 73, 24, "lineNumber"], [76, 20, 73, 24], [77, 10, 73, 24, "columnNumber"], [77, 22, 73, 24], [78, 8, 73, 24], [78, 15, 74, 14], [79, 6, 74, 15], [80, 8, 74, 15, "fileName"], [80, 16, 74, 15], [80, 18, 74, 15, "_jsxFileName"], [80, 30, 74, 15], [81, 8, 74, 15, "lineNumber"], [81, 18, 74, 15], [82, 8, 74, 15, "columnNumber"], [82, 20, 74, 15], [83, 6, 74, 15], [83, 13, 75, 20], [83, 14, 75, 21], [84, 4, 75, 21], [85, 6, 75, 21, "fileName"], [85, 14, 75, 21], [85, 16, 75, 21, "_jsxFileName"], [85, 28, 75, 21], [86, 6, 75, 21, "lineNumber"], [86, 16, 75, 21], [87, 6, 75, 21, "columnNumber"], [87, 18, 75, 21], [88, 4, 75, 21], [88, 11, 76, 10], [88, 12, 76, 11], [89, 2, 78, 0], [90, 2, 80, 0], [90, 11, 80, 9, "LogBoxInspectorCodeFrame"], [90, 35, 80, 33, "LogBoxInspectorCodeFrame"], [90, 36, 80, 34, "props"], [90, 41, 80, 46], [90, 43, 80, 60], [91, 4, 81, 2], [91, 8, 81, 9, "codeFrame"], [91, 17, 81, 18], [91, 20, 81, 42, "props"], [91, 25, 81, 47], [91, 26, 81, 9, "codeFrame"], [91, 35, 81, 18], [92, 6, 81, 20, "componentCodeFrame"], [92, 24, 81, 38], [92, 27, 81, 42, "props"], [92, 32, 81, 47], [92, 33, 81, 20, "componentCodeFrame"], [92, 51, 81, 38], [93, 4, 82, 2], [93, 8, 82, 6, "sources"], [93, 15, 82, 13], [93, 18, 82, 16], [93, 20, 82, 18], [94, 4, 83, 2], [94, 8, 83, 6, "codeFrame"], [94, 17, 83, 15], [94, 21, 83, 19], [94, 25, 83, 23], [94, 27, 83, 25], [95, 6, 84, 4, "sources"], [95, 13, 84, 11], [95, 14, 84, 12, "push"], [95, 18, 84, 16], [95, 19, 84, 17, "codeFrame"], [95, 28, 84, 26], [95, 29, 84, 27], [96, 4, 85, 2], [97, 4, 86, 2], [97, 8, 87, 4, "componentCodeFrame"], [97, 26, 87, 22], [97, 30, 87, 26], [97, 34, 87, 30], [97, 38, 88, 4, "componentCodeFrame"], [97, 56, 88, 22], [97, 58, 88, 24, "content"], [97, 65, 88, 31], [97, 70, 88, 36, "codeFrame"], [97, 79, 88, 45], [97, 81, 88, 47, "content"], [97, 88, 88, 54], [97, 90, 89, 4], [98, 6, 90, 4, "sources"], [98, 13, 90, 11], [98, 14, 90, 12, "push"], [98, 18, 90, 16], [98, 19, 90, 17, "componentCodeFrame"], [98, 37, 90, 35], [98, 38, 90, 36], [99, 4, 91, 2], [100, 4, 92, 2], [100, 8, 92, 6, "sources"], [100, 15, 92, 13], [100, 16, 92, 14, "length"], [100, 22, 92, 20], [100, 27, 92, 25], [100, 28, 92, 26], [100, 30, 92, 28], [101, 6, 93, 4], [101, 13, 93, 11], [101, 17, 93, 15], [102, 4, 94, 2], [103, 4, 95, 2], [103, 24, 96, 4], [103, 28, 96, 4, "_jsxDevRuntime"], [103, 42, 96, 4], [103, 43, 96, 4, "jsxDEV"], [103, 49, 96, 4], [103, 51, 96, 5, "_LogBoxInspectorSection"], [103, 74, 96, 5], [103, 75, 96, 5, "default"], [103, 82, 96, 27], [104, 6, 97, 6, "heading"], [104, 13, 97, 13], [104, 15, 97, 15, "sources"], [104, 22, 97, 22], [104, 23, 97, 23, "length"], [104, 29, 97, 29], [104, 32, 97, 32], [104, 33, 97, 33], [104, 36, 97, 36], [104, 45, 97, 45], [104, 48, 97, 48], [104, 56, 97, 57], [105, 6, 98, 6, "action"], [105, 12, 98, 12], [105, 27, 98, 14], [105, 31, 98, 14, "_jsxDevRuntime"], [105, 45, 98, 14], [105, 46, 98, 14, "jsxDEV"], [105, 52, 98, 14], [105, 54, 98, 15, "AppInfo"], [105, 61, 98, 22], [106, 8, 98, 22, "fileName"], [106, 16, 98, 22], [106, 18, 98, 22, "_jsxFileName"], [106, 30, 98, 22], [107, 8, 98, 22, "lineNumber"], [107, 18, 98, 22], [108, 8, 98, 22, "columnNumber"], [108, 20, 98, 22], [109, 6, 98, 22], [109, 13, 98, 24], [109, 14, 98, 26], [110, 6, 98, 26, "children"], [110, 14, 98, 26], [110, 16, 99, 7, "sources"], [110, 23, 99, 14], [110, 24, 99, 15, "map"], [110, 27, 99, 18], [110, 28, 99, 19], [110, 29, 99, 20, "frame"], [110, 34, 99, 25], [110, 36, 99, 27, "index"], [110, 41, 99, 32], [110, 59, 100, 8], [110, 63, 100, 8, "_jsxDevRuntime"], [110, 77, 100, 8], [110, 78, 100, 8, "jsxDEV"], [110, 84, 100, 8], [110, 86, 100, 9, "CodeFrameDisplay"], [110, 102, 100, 25], [111, 8, 100, 38, "codeFrame"], [111, 17, 100, 47], [111, 19, 100, 49, "frame"], [112, 6, 100, 55], [112, 9, 100, 31, "index"], [112, 14, 100, 36], [113, 8, 100, 36, "fileName"], [113, 16, 100, 36], [113, 18, 100, 36, "_jsxFileName"], [113, 30, 100, 36], [114, 8, 100, 36, "lineNumber"], [114, 18, 100, 36], [115, 8, 100, 36, "columnNumber"], [115, 20, 100, 36], [116, 6, 100, 36], [116, 13, 100, 57], [116, 14, 101, 7], [117, 4, 101, 8], [118, 6, 101, 8, "fileName"], [118, 14, 101, 8], [118, 16, 101, 8, "_jsxFileName"], [118, 28, 101, 8], [119, 6, 101, 8, "lineNumber"], [119, 16, 101, 8], [120, 6, 101, 8, "columnNumber"], [120, 18, 101, 8], [121, 4, 101, 8], [121, 11, 102, 28], [121, 12, 102, 29], [122, 2, 104, 0], [123, 2, 106, 0], [123, 11, 106, 9, "AppInfo"], [123, 18, 106, 16, "AppInfo"], [123, 19, 106, 16], [123, 21, 106, 19], [124, 4, 107, 2], [124, 8, 107, 8, "appInfo"], [124, 15, 107, 15], [124, 18, 107, 18, "LogBoxData"], [124, 28, 107, 28], [124, 29, 107, 29, "getAppInfo"], [124, 39, 107, 39], [124, 40, 107, 40], [124, 41, 107, 41], [125, 4, 108, 2], [125, 8, 108, 6, "appInfo"], [125, 15, 108, 13], [125, 19, 108, 17], [125, 23, 108, 21], [125, 25, 108, 23], [126, 6, 109, 4], [126, 13, 109, 11], [126, 17, 109, 15], [127, 4, 110, 2], [128, 4, 112, 2], [128, 24, 113, 4], [128, 28, 113, 4, "_jsxDevRuntime"], [128, 42, 113, 4], [128, 43, 113, 4, "jsxDEV"], [128, 49, 113, 4], [128, 51, 113, 5, "_LogBoxButton"], [128, 64, 113, 5], [128, 65, 113, 5, "default"], [128, 72, 113, 17], [129, 6, 114, 6, "backgroundColor"], [129, 21, 114, 21], [129, 23, 114, 23], [130, 8, 115, 8, "default"], [130, 15, 115, 15], [130, 17, 115, 17], [130, 30, 115, 30], [131, 8, 116, 8, "pressed"], [131, 15, 116, 15], [131, 17, 116, 17, "appInfo"], [131, 24, 116, 24], [131, 25, 116, 25, "onPress"], [131, 32, 116, 32], [131, 35, 117, 12, "LogBoxStyle"], [131, 46, 117, 23], [131, 47, 117, 24, "getBackgroundColor"], [131, 65, 117, 42], [131, 66, 117, 43], [131, 67, 117, 44], [131, 68, 117, 45], [131, 71, 118, 12], [132, 6, 119, 6], [132, 7, 119, 8], [133, 6, 120, 6, "style"], [133, 11, 120, 11], [133, 13, 120, 13, "appInfoStyles"], [133, 26, 120, 26], [133, 27, 120, 27, "buildButton"], [133, 38, 120, 39], [134, 6, 121, 6, "onPress"], [134, 13, 121, 13], [134, 15, 121, 15, "appInfo"], [134, 22, 121, 22], [134, 23, 121, 23, "onPress"], [134, 30, 121, 31], [135, 6, 121, 31, "children"], [135, 14, 121, 31], [135, 29, 122, 6], [135, 33, 122, 6, "_jsxDevRuntime"], [135, 47, 122, 6], [135, 48, 122, 6, "jsxDEV"], [135, 54, 122, 6], [135, 56, 122, 7, "_Text"], [135, 61, 122, 7], [135, 62, 122, 7, "default"], [135, 69, 122, 11], [136, 8, 122, 12, "style"], [136, 13, 122, 17], [136, 15, 122, 19, "appInfoStyles"], [136, 28, 122, 32], [136, 29, 122, 33, "text"], [136, 33, 122, 38], [137, 8, 122, 38, "children"], [137, 16, 122, 38], [137, 19, 123, 9, "appInfo"], [137, 26, 123, 16], [137, 27, 123, 17, "appVersion"], [137, 37, 123, 27], [137, 39, 123, 28], [137, 43, 123, 30], [137, 45, 123, 31, "appInfo"], [137, 52, 123, 38], [137, 53, 123, 39, "engine"], [137, 59, 123, 45], [137, 61, 123, 46], [137, 64, 124, 6], [138, 6, 124, 6], [139, 8, 124, 6, "fileName"], [139, 16, 124, 6], [139, 18, 124, 6, "_jsxFileName"], [139, 30, 124, 6], [140, 8, 124, 6, "lineNumber"], [140, 18, 124, 6], [141, 8, 124, 6, "columnNumber"], [141, 20, 124, 6], [142, 6, 124, 6], [142, 13, 124, 12], [143, 4, 124, 13], [144, 6, 124, 13, "fileName"], [144, 14, 124, 13], [144, 16, 124, 13, "_jsxFileName"], [144, 28, 124, 13], [145, 6, 124, 13, "lineNumber"], [145, 16, 124, 13], [146, 6, 124, 13, "columnNumber"], [146, 18, 124, 13], [147, 4, 124, 13], [147, 11, 125, 18], [147, 12, 125, 19], [148, 2, 127, 0], [149, 2, 129, 0], [149, 6, 129, 6, "appInfoStyles"], [149, 19, 129, 19], [149, 22, 129, 22, "StyleSheet"], [149, 41, 129, 32], [149, 42, 129, 33, "create"], [149, 48, 129, 39], [149, 49, 129, 40], [150, 4, 130, 2, "text"], [150, 8, 130, 6], [150, 10, 130, 8], [151, 6, 131, 4, "color"], [151, 11, 131, 9], [151, 13, 131, 11, "LogBoxStyle"], [151, 24, 131, 22], [151, 25, 131, 23, "getTextColor"], [151, 37, 131, 35], [151, 38, 131, 36], [151, 41, 131, 39], [151, 42, 131, 40], [152, 6, 132, 4, "fontSize"], [152, 14, 132, 12], [152, 16, 132, 14], [152, 18, 132, 16], [153, 6, 133, 4, "lineHeight"], [153, 16, 133, 14], [153, 18, 133, 16], [154, 4, 134, 2], [154, 5, 134, 3], [155, 4, 135, 2, "buildButton"], [155, 15, 135, 13], [155, 17, 135, 15], [156, 6, 136, 4, "flex"], [156, 10, 136, 8], [156, 12, 136, 10], [156, 13, 136, 11], [157, 6, 137, 4, "flexGrow"], [157, 14, 137, 12], [157, 16, 137, 14], [157, 17, 137, 15], [158, 6, 138, 4, "paddingVertical"], [158, 21, 138, 19], [158, 23, 138, 21], [158, 24, 138, 22], [159, 6, 139, 4, "paddingHorizontal"], [159, 23, 139, 21], [159, 25, 139, 23], [159, 26, 139, 24], [160, 6, 140, 4, "borderRadius"], [160, 18, 140, 16], [160, 20, 140, 18], [160, 21, 140, 19], [161, 6, 141, 4, "marginRight"], [161, 17, 141, 15], [161, 19, 141, 17], [161, 20, 141, 18], [162, 4, 142, 2], [163, 2, 143, 0], [163, 3, 143, 1], [163, 4, 143, 2], [164, 2, 145, 0], [164, 6, 145, 6, "styles"], [164, 12, 145, 12], [164, 15, 145, 15, "StyleSheet"], [164, 34, 145, 25], [164, 35, 145, 26, "create"], [164, 41, 145, 32], [164, 42, 145, 33], [165, 4, 146, 2, "box"], [165, 7, 146, 5], [165, 9, 146, 7], [166, 6, 147, 4, "backgroundColor"], [166, 21, 147, 19], [166, 23, 147, 21, "LogBoxStyle"], [166, 34, 147, 32], [166, 35, 147, 33, "getBackgroundColor"], [166, 53, 147, 51], [166, 54, 147, 52], [166, 55, 147, 53], [167, 6, 148, 4, "marginLeft"], [167, 16, 148, 14], [167, 18, 148, 16], [167, 20, 148, 18], [168, 6, 149, 4, "marginRight"], [168, 17, 149, 15], [168, 19, 149, 17], [168, 21, 149, 19], [169, 6, 150, 4, "marginTop"], [169, 15, 150, 13], [169, 17, 150, 15], [169, 18, 150, 16], [170, 6, 151, 4, "borderRadius"], [170, 18, 151, 16], [170, 20, 151, 18], [171, 4, 152, 2], [171, 5, 152, 3], [172, 4, 153, 2, "frame"], [172, 9, 153, 7], [172, 11, 153, 9], [173, 6, 154, 4, "padding"], [173, 13, 154, 11], [173, 15, 154, 13], [173, 17, 154, 15], [174, 6, 155, 4, "borderBottomColor"], [174, 23, 155, 21], [174, 25, 155, 23, "LogBoxStyle"], [174, 36, 155, 34], [174, 37, 155, 35, "getTextColor"], [174, 49, 155, 47], [174, 50, 155, 48], [174, 53, 155, 51], [174, 54, 155, 52], [175, 6, 156, 4, "borderBottomWidth"], [175, 23, 156, 21], [175, 25, 156, 23], [176, 4, 157, 2], [176, 5, 157, 3], [177, 4, 158, 2, "button"], [177, 10, 158, 8], [177, 12, 158, 10], [178, 6, 159, 4, "paddingTop"], [178, 16, 159, 14], [178, 18, 159, 16], [178, 20, 159, 18], [179, 6, 160, 4, "paddingBottom"], [179, 19, 160, 17], [179, 21, 160, 19], [180, 4, 161, 2], [180, 5, 161, 3], [181, 4, 162, 2, "contentContainer"], [181, 20, 162, 18], [181, 22, 162, 20], [182, 6, 163, 4, "min<PERSON><PERSON><PERSON>"], [182, 14, 163, 12], [182, 16, 163, 14], [183, 4, 164, 2], [183, 5, 164, 3], [184, 4, 165, 2, "content"], [184, 11, 165, 9], [184, 13, 165, 11], [185, 6, 166, 4, "color"], [185, 11, 166, 9], [185, 13, 166, 11, "LogBoxStyle"], [185, 24, 166, 22], [185, 25, 166, 23, "getTextColor"], [185, 37, 166, 35], [185, 38, 166, 36], [185, 39, 166, 37], [185, 40, 166, 38], [186, 6, 167, 4, "fontSize"], [186, 14, 167, 12], [186, 16, 167, 14], [186, 18, 167, 16], [187, 6, 168, 4, "includeFontPadding"], [187, 24, 168, 22], [187, 26, 168, 24], [187, 31, 168, 29], [188, 6, 169, 4, "lineHeight"], [188, 16, 169, 14], [188, 18, 169, 16], [188, 20, 169, 18], [189, 6, 170, 4, "fontFamily"], [189, 16, 170, 14], [189, 18, 170, 16, "Platform"], [189, 35, 170, 24], [189, 36, 170, 25, "select"], [189, 42, 170, 31], [189, 43, 170, 32], [190, 8, 170, 33, "android"], [190, 15, 170, 40], [190, 17, 170, 42], [190, 28, 170, 53], [191, 8, 170, 55, "ios"], [191, 11, 170, 58], [191, 13, 170, 60], [192, 6, 170, 67], [192, 7, 170, 68], [193, 4, 171, 2], [193, 5, 171, 3], [194, 4, 172, 2, "fileText"], [194, 12, 172, 10], [194, 14, 172, 12], [195, 6, 173, 4, "color"], [195, 11, 173, 9], [195, 13, 173, 11, "LogBoxStyle"], [195, 24, 173, 22], [195, 25, 173, 23, "getTextColor"], [195, 37, 173, 35], [195, 38, 173, 36], [195, 41, 173, 39], [195, 42, 173, 40], [196, 6, 174, 4, "textAlign"], [196, 15, 174, 13], [196, 17, 174, 15], [196, 25, 174, 23], [197, 6, 175, 4, "flex"], [197, 10, 175, 8], [197, 12, 175, 10], [197, 13, 175, 11], [198, 6, 176, 4, "fontSize"], [198, 14, 176, 12], [198, 16, 176, 14], [198, 18, 176, 16], [199, 6, 177, 4, "includeFontPadding"], [199, 24, 177, 22], [199, 26, 177, 24], [199, 31, 177, 29], [200, 6, 178, 4, "lineHeight"], [200, 16, 178, 14], [200, 18, 178, 16], [200, 20, 178, 18], [201, 6, 179, 4, "fontFamily"], [201, 16, 179, 14], [201, 18, 179, 16, "Platform"], [201, 35, 179, 24], [201, 36, 179, 25, "select"], [201, 42, 179, 31], [201, 43, 179, 32], [202, 8, 179, 33, "android"], [202, 15, 179, 40], [202, 17, 179, 42], [202, 28, 179, 53], [203, 8, 179, 55, "ios"], [203, 11, 179, 58], [203, 13, 179, 60], [204, 6, 179, 67], [204, 7, 179, 68], [205, 4, 180, 2], [206, 2, 181, 0], [206, 3, 181, 1], [206, 4, 181, 2], [207, 2, 181, 3], [207, 6, 181, 3, "_default"], [207, 14, 181, 3], [207, 17, 181, 3, "exports"], [207, 24, 181, 3], [207, 25, 181, 3, "default"], [207, 32, 181, 3], [207, 35, 183, 15, "LogBoxInspectorCodeFrame"], [207, 59, 183, 39], [208, 0, 183, 39], [208, 3]], "functionMap": {"names": ["<global>", "CodeFrameDisplay", "getFileName", "getLocation", "LogBoxButton.props.onPress", "LogBoxInspectorCodeFrame", "sources.map$argument_0", "AppInfo"], "mappings": "AAA;AC8B;ECC;GDS;EEE;GFU;iBGe;SHE;CDQ;AKE;mBCmB;ODE;CLG;AOE;CPqB"}}, "type": "js/module"}]}