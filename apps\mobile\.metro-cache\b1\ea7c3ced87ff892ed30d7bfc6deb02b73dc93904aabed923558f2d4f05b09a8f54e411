{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 45}}], "key": "WyqnBhspP5BAR0xvCwqfBv/v4uA=", "exportNames": ["*"]}}, {"name": "../Utilities/HMRClient", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 22}, "end": {"line": 21, "column": 55}}], "key": "/3znl6kSaPctipowrDe5leVrLgM=", "exportNames": ["*"]}}, {"name": "./setUpReactRefresh", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 32}}], "key": "U1rFZ9xmT6g91jAoB75uDz6Jaeg=", "exportNames": ["*"]}}, {"name": "./Devtools/loadBundleFromServer", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 46}}], "key": "nqutn0rinKc+PFHeXMU4BlmvGxQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[1], \"../Utilities/Platform\"));\n  if (__DEV__) {\n    if (!_Platform.default.isTesting) {\n      var HMRClient = require(_dependencyMap[2], \"../Utilities/HMRClient\").default;\n      if (console._isPolyfilled) {\n        ['trace', 'info', 'warn', 'error', 'log', 'group', 'groupCollapsed', 'groupEnd', 'debug'].forEach(level => {\n          var originalFunction = console[level];\n          console[level] = function () {\n            for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n              args[_key] = arguments[_key];\n            }\n            HMRClient.log(level, args);\n            originalFunction.apply(console, args);\n          };\n        });\n      }\n    }\n    require(_dependencyMap[3], \"./setUpReactRefresh\");\n    global[`${global.__METRO_GLOBAL_PREFIX__ ?? ''}__loadBundleAsync`] = require(_dependencyMap[4], \"./Devtools/loadBundleFromServer\").default;\n  }\n});", "lineCount": 23, "map": [[3, 2, 11, 0], [3, 6, 11, 0, "_Platform"], [3, 15, 11, 0], [3, 18, 11, 0, "_interopRequireDefault"], [3, 40, 11, 0], [3, 41, 11, 0, "require"], [3, 48, 11, 0], [3, 49, 11, 0, "_dependencyMap"], [3, 63, 11, 0], [4, 2, 19, 0], [4, 6, 19, 4, "__DEV__"], [4, 13, 19, 11], [4, 15, 19, 13], [5, 4, 20, 2], [5, 8, 20, 6], [5, 9, 20, 7, "Platform"], [5, 26, 20, 15], [5, 27, 20, 16, "isTesting"], [5, 36, 20, 25], [5, 38, 20, 27], [6, 6, 21, 4], [6, 10, 21, 10, "HMRClient"], [6, 19, 21, 19], [6, 22, 21, 22, "require"], [6, 29, 21, 29], [6, 30, 21, 29, "_dependencyMap"], [6, 44, 21, 29], [6, 73, 21, 54], [6, 74, 21, 55], [6, 75, 21, 56, "default"], [6, 82, 21, 63], [7, 6, 24, 4], [7, 10, 24, 8, "console"], [7, 17, 24, 15], [7, 18, 24, 16, "_isPolyfilled"], [7, 31, 24, 29], [7, 33, 24, 31], [8, 8, 26, 6], [8, 9, 27, 8], [8, 16, 27, 15], [8, 18, 28, 8], [8, 24, 28, 14], [8, 26, 29, 8], [8, 32, 29, 14], [8, 34, 30, 8], [8, 41, 30, 15], [8, 43, 31, 8], [8, 48, 31, 13], [8, 50, 32, 8], [8, 57, 32, 15], [8, 59, 33, 8], [8, 75, 33, 24], [8, 77, 34, 8], [8, 87, 34, 18], [8, 89, 35, 8], [8, 96, 35, 15], [8, 97, 36, 7], [8, 98, 36, 8, "for<PERSON>ach"], [8, 105, 36, 15], [8, 106, 36, 16, "level"], [8, 111, 36, 21], [8, 115, 36, 25], [9, 10, 37, 8], [9, 14, 37, 14, "originalFunction"], [9, 30, 37, 30], [9, 33, 37, 33, "console"], [9, 40, 37, 40], [9, 41, 37, 41, "level"], [9, 46, 37, 46], [9, 47, 37, 47], [10, 10, 38, 8, "console"], [10, 17, 38, 15], [10, 18, 38, 16, "level"], [10, 23, 38, 21], [10, 24, 38, 22], [10, 27, 38, 25], [10, 39, 38, 67], [11, 12, 38, 67], [11, 21, 38, 67, "_len"], [11, 25, 38, 67], [11, 28, 38, 67, "arguments"], [11, 37, 38, 67], [11, 38, 38, 67, "length"], [11, 44, 38, 67], [11, 46, 38, 38, "args"], [11, 50, 38, 42], [11, 57, 38, 42, "Array"], [11, 62, 38, 42], [11, 63, 38, 42, "_len"], [11, 67, 38, 42], [11, 70, 38, 42, "_key"], [11, 74, 38, 42], [11, 80, 38, 42, "_key"], [11, 84, 38, 42], [11, 87, 38, 42, "_len"], [11, 91, 38, 42], [11, 93, 38, 42, "_key"], [11, 97, 38, 42], [12, 14, 38, 38, "args"], [12, 18, 38, 42], [12, 19, 38, 42, "_key"], [12, 23, 38, 42], [12, 27, 38, 42, "arguments"], [12, 36, 38, 42], [12, 37, 38, 42, "_key"], [12, 41, 38, 42], [13, 12, 38, 42], [14, 12, 39, 10, "HMRClient"], [14, 21, 39, 19], [14, 22, 39, 20, "log"], [14, 25, 39, 23], [14, 26, 39, 24, "level"], [14, 31, 39, 29], [14, 33, 39, 31, "args"], [14, 37, 39, 35], [14, 38, 39, 36], [15, 12, 40, 10, "originalFunction"], [15, 28, 40, 26], [15, 29, 40, 27, "apply"], [15, 34, 40, 32], [15, 35, 40, 33, "console"], [15, 42, 40, 40], [15, 44, 40, 42, "args"], [15, 48, 40, 46], [15, 49, 40, 47], [16, 10, 41, 8], [16, 11, 41, 9], [17, 8, 42, 6], [17, 9, 42, 7], [17, 10, 42, 8], [18, 6, 43, 4], [19, 4, 44, 2], [20, 4, 46, 2, "require"], [20, 11, 46, 9], [20, 12, 46, 9, "_dependencyMap"], [20, 26, 46, 9], [20, 52, 46, 31], [20, 53, 46, 32], [21, 4, 48, 2, "global"], [21, 10, 48, 8], [21, 11, 48, 9], [21, 14, 48, 12, "global"], [21, 20, 48, 18], [21, 21, 48, 19, "__METRO_GLOBAL_PREFIX__"], [21, 44, 48, 42], [21, 48, 48, 46], [21, 50, 48, 48], [21, 69, 48, 67], [21, 70, 48, 68], [21, 73, 49, 4, "require"], [21, 80, 49, 11], [21, 81, 49, 11, "_dependencyMap"], [21, 95, 49, 11], [21, 133, 49, 45], [21, 134, 49, 46], [21, 135, 49, 47, "default"], [21, 142, 49, 54], [22, 2, 50, 0], [23, 0, 50, 1], [23, 3]], "functionMap": {"names": ["<global>", "forEach$argument_0", "console.level"], "mappings": "AAA;gBCmC;yBCE;SDG;ODC"}}, "type": "js/module"}]}