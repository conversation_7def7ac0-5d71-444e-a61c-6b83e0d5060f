{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../handlersRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 87}, "end": {"line": 3, "column": 57, "index": 144}}], "key": "Q8MtNj8/mrt1iN8Kay94o881ERE=", "exportNames": ["*"]}}, {"name": "../../../RNGestureHandlerModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 145}, "end": {"line": 4, "column": 69, "index": 214}}], "key": "2BYIjnTRSFId8SRJ7sJFxLD1BD4=", "exportNames": ["*"]}}, {"name": "../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 215}, "end": {"line": 5, "column": 68, "index": 283}}], "key": "ByXat9lt9duIJLDmSeH0V+tRq1s=", "exportNames": ["*"]}}, {"name": "../../../ActionType", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 341}, "end": {"line": 7, "column": 49, "index": 390}}], "key": "HJ6ReZDVLtCQDFQShsuSOLiCtL0=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 391}, "end": {"line": 8, "column": 40, "index": 431}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../../../ghQueueMicrotask", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 514}, "end": {"line": 10, "column": 61, "index": 575}}], "key": "6QYiO8x9sAoDBctSRJ19A8sqUNk=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 641}, "end": {"line": 16, "column": 17, "index": 747}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}, {"name": "../../../mountRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 748}, "end": {"line": 17, "column": 55, "index": 803}}], "key": "ZDu7aL2iuT3Od7iyX13y9sY9XZQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.attachHandlers = attachHandlers;\n  var _handlersRegistry = require(_dependencyMap[1], \"../../handlersRegistry\");\n  var _RNGestureHandlerModule = _interopRequireDefault(require(_dependencyMap[2], \"../../../RNGestureHandlerModule\"));\n  var _utils = require(_dependencyMap[3], \"../../utils\");\n  var _ActionType = require(_dependencyMap[4], \"../../../ActionType\");\n  var _reactNative = require(_dependencyMap[5], \"react-native\");\n  var _ghQueueMicrotask = require(_dependencyMap[6], \"../../../ghQueueMicrotask\");\n  var _utils2 = require(_dependencyMap[7], \"./utils\");\n  var _mountRegistry = require(_dependencyMap[8], \"../../../mountRegistry\");\n  function attachHandlers(_ref) {\n    var preparedGesture = _ref.preparedGesture,\n      gestureConfig = _ref.gestureConfig,\n      gesturesToAttach = _ref.gesturesToAttach,\n      viewTag = _ref.viewTag,\n      webEventHandlersRef = _ref.webEventHandlersRef;\n    gestureConfig.initialize();\n\n    // Use queueMicrotask to extract handlerTags, because all refs should be initialized\n    // when it's ran\n    (0, _ghQueueMicrotask.ghQueueMicrotask)(() => {\n      if (!preparedGesture.isMounted) {\n        return;\n      }\n      gestureConfig.prepare();\n    });\n    for (var handler of gesturesToAttach) {\n      (0, _utils2.checkGestureCallbacksForWorklets)(handler);\n      _RNGestureHandlerModule.default.createGestureHandler(handler.handlerName, handler.handlerTag, (0, _utils.filterConfig)(handler.config, _utils2.ALLOWED_PROPS));\n      (0, _handlersRegistry.registerHandler)(handler.handlerTag, handler, handler.config.testId);\n    }\n\n    // Use queueMicrotask to extract handlerTags, because all refs should be initialized\n    // when it's ran\n    (0, _ghQueueMicrotask.ghQueueMicrotask)(() => {\n      if (!preparedGesture.isMounted) {\n        return;\n      }\n      for (var _handler of gesturesToAttach) {\n        _RNGestureHandlerModule.default.updateGestureHandler(_handler.handlerTag, (0, _utils.filterConfig)(_handler.config, _utils2.ALLOWED_PROPS, (0, _utils2.extractGestureRelations)(_handler)));\n      }\n      (0, _utils.scheduleFlushOperations)();\n    });\n    for (var gesture of gesturesToAttach) {\n      var actionType = gesture.shouldUseReanimated ? _ActionType.ActionType.REANIMATED_WORKLET : _ActionType.ActionType.JS_FUNCTION_NEW_API;\n      if (_reactNative.Platform.OS === 'web') {\n        _RNGestureHandlerModule.default.attachGestureHandler(gesture.handlerTag, viewTag, _ActionType.ActionType.JS_FUNCTION_OLD_API,\n        // Ignored on web\n        webEventHandlersRef);\n      } else {\n        _RNGestureHandlerModule.default.attachGestureHandler(gesture.handlerTag, viewTag, actionType);\n      }\n      _mountRegistry.MountRegistry.gestureWillMount(gesture);\n    }\n    preparedGesture.attachedGestures = gesturesToAttach;\n    if (preparedGesture.animatedHandlers) {\n      var isAnimatedGesture = g => g.shouldUseReanimated;\n      preparedGesture.animatedHandlers.value = gesturesToAttach.filter(isAnimatedGesture).map(g => g.handlers);\n    }\n  }\n});", "lineCount": 65, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_handlersRegistry"], [7, 23, 3, 0], [7, 26, 3, 0, "require"], [7, 33, 3, 0], [7, 34, 3, 0, "_dependencyMap"], [7, 48, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_RNGestureHandlerModule"], [8, 29, 4, 0], [8, 32, 4, 0, "_interopRequireDefault"], [8, 54, 4, 0], [8, 55, 4, 0, "require"], [8, 62, 4, 0], [8, 63, 4, 0, "_dependencyMap"], [8, 77, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_utils"], [9, 12, 5, 0], [9, 15, 5, 0, "require"], [9, 22, 5, 0], [9, 23, 5, 0, "_dependencyMap"], [9, 37, 5, 0], [10, 2, 7, 0], [10, 6, 7, 0, "_ActionType"], [10, 17, 7, 0], [10, 20, 7, 0, "require"], [10, 27, 7, 0], [10, 28, 7, 0, "_dependencyMap"], [10, 42, 7, 0], [11, 2, 8, 0], [11, 6, 8, 0, "_reactNative"], [11, 18, 8, 0], [11, 21, 8, 0, "require"], [11, 28, 8, 0], [11, 29, 8, 0, "_dependencyMap"], [11, 43, 8, 0], [12, 2, 10, 0], [12, 6, 10, 0, "_ghQueueMicrotask"], [12, 23, 10, 0], [12, 26, 10, 0, "require"], [12, 33, 10, 0], [12, 34, 10, 0, "_dependencyMap"], [12, 48, 10, 0], [13, 2, 12, 0], [13, 6, 12, 0, "_utils2"], [13, 13, 12, 0], [13, 16, 12, 0, "require"], [13, 23, 12, 0], [13, 24, 12, 0, "_dependencyMap"], [13, 38, 12, 0], [14, 2, 17, 0], [14, 6, 17, 0, "_mountRegistry"], [14, 20, 17, 0], [14, 23, 17, 0, "require"], [14, 30, 17, 0], [14, 31, 17, 0, "_dependencyMap"], [14, 45, 17, 0], [15, 2, 27, 7], [15, 11, 27, 16, "attachHandlers"], [15, 25, 27, 30, "attachHandlers"], [15, 26, 27, 30, "_ref"], [15, 30, 27, 30], [15, 32, 33, 25], [16, 4, 33, 25], [16, 8, 28, 2, "preparedGesture"], [16, 23, 28, 17], [16, 26, 28, 17, "_ref"], [16, 30, 28, 17], [16, 31, 28, 2, "preparedGesture"], [16, 46, 28, 17], [17, 6, 29, 2, "gestureConfig"], [17, 19, 29, 15], [17, 22, 29, 15, "_ref"], [17, 26, 29, 15], [17, 27, 29, 2, "gestureConfig"], [17, 40, 29, 15], [18, 6, 30, 2, "gestures<PERSON>oAtta<PERSON>"], [18, 22, 30, 18], [18, 25, 30, 18, "_ref"], [18, 29, 30, 18], [18, 30, 30, 2, "gestures<PERSON>oAtta<PERSON>"], [18, 46, 30, 18], [19, 6, 31, 2, "viewTag"], [19, 13, 31, 9], [19, 16, 31, 9, "_ref"], [19, 20, 31, 9], [19, 21, 31, 2, "viewTag"], [19, 28, 31, 9], [20, 6, 32, 2, "webEventHandlersRef"], [20, 25, 32, 21], [20, 28, 32, 21, "_ref"], [20, 32, 32, 21], [20, 33, 32, 2, "webEventHandlersRef"], [20, 52, 32, 21], [21, 4, 34, 2, "gestureConfig"], [21, 17, 34, 15], [21, 18, 34, 16, "initialize"], [21, 28, 34, 26], [21, 29, 34, 27], [21, 30, 34, 28], [23, 4, 36, 2], [24, 4, 37, 2], [25, 4, 38, 2], [25, 8, 38, 2, "ghQueueMicrotask"], [25, 42, 38, 18], [25, 44, 38, 19], [25, 50, 38, 25], [26, 6, 39, 4], [26, 10, 39, 8], [26, 11, 39, 9, "preparedGesture"], [26, 26, 39, 24], [26, 27, 39, 25, "isMounted"], [26, 36, 39, 34], [26, 38, 39, 36], [27, 8, 40, 6], [28, 6, 41, 4], [29, 6, 42, 4, "gestureConfig"], [29, 19, 42, 17], [29, 20, 42, 18, "prepare"], [29, 27, 42, 25], [29, 28, 42, 26], [29, 29, 42, 27], [30, 4, 43, 2], [30, 5, 43, 3], [30, 6, 43, 4], [31, 4, 45, 2], [31, 9, 45, 7], [31, 13, 45, 13, "handler"], [31, 20, 45, 20], [31, 24, 45, 24, "gestures<PERSON>oAtta<PERSON>"], [31, 40, 45, 40], [31, 42, 45, 42], [32, 6, 46, 4], [32, 10, 46, 4, "checkGestureCallbacksForWorklets"], [32, 50, 46, 36], [32, 52, 46, 37, "handler"], [32, 59, 46, 44], [32, 60, 46, 45], [33, 6, 47, 4, "RNGestureHandlerModule"], [33, 37, 47, 26], [33, 38, 47, 27, "createGestureHandler"], [33, 58, 47, 47], [33, 59, 48, 6, "handler"], [33, 66, 48, 13], [33, 67, 48, 14, "handler<PERSON>ame"], [33, 78, 48, 25], [33, 80, 49, 6, "handler"], [33, 87, 49, 13], [33, 88, 49, 14, "handlerTag"], [33, 98, 49, 24], [33, 100, 50, 6], [33, 104, 50, 6, "filterConfig"], [33, 123, 50, 18], [33, 125, 50, 19, "handler"], [33, 132, 50, 26], [33, 133, 50, 27, "config"], [33, 139, 50, 33], [33, 141, 50, 35, "ALLOWED_PROPS"], [33, 162, 50, 48], [33, 163, 51, 4], [33, 164, 51, 5], [34, 6, 53, 4], [34, 10, 53, 4, "registerHandler"], [34, 43, 53, 19], [34, 45, 53, 20, "handler"], [34, 52, 53, 27], [34, 53, 53, 28, "handlerTag"], [34, 63, 53, 38], [34, 65, 53, 40, "handler"], [34, 72, 53, 47], [34, 74, 53, 49, "handler"], [34, 81, 53, 56], [34, 82, 53, 57, "config"], [34, 88, 53, 63], [34, 89, 53, 64, "testId"], [34, 95, 53, 70], [34, 96, 53, 71], [35, 4, 54, 2], [37, 4, 56, 2], [38, 4, 57, 2], [39, 4, 58, 2], [39, 8, 58, 2, "ghQueueMicrotask"], [39, 42, 58, 18], [39, 44, 58, 19], [39, 50, 58, 25], [40, 6, 59, 4], [40, 10, 59, 8], [40, 11, 59, 9, "preparedGesture"], [40, 26, 59, 24], [40, 27, 59, 25, "isMounted"], [40, 36, 59, 34], [40, 38, 59, 36], [41, 8, 60, 6], [42, 6, 61, 4], [43, 6, 62, 4], [43, 11, 62, 9], [43, 15, 62, 15, "handler"], [43, 23, 62, 22], [43, 27, 62, 26, "gestures<PERSON>oAtta<PERSON>"], [43, 43, 62, 42], [43, 45, 62, 44], [44, 8, 63, 6, "RNGestureHandlerModule"], [44, 39, 63, 28], [44, 40, 63, 29, "updateGestureHandler"], [44, 60, 63, 49], [44, 61, 64, 8, "handler"], [44, 69, 64, 15], [44, 70, 64, 16, "handlerTag"], [44, 80, 64, 26], [44, 82, 65, 8], [44, 86, 65, 8, "filterConfig"], [44, 105, 65, 20], [44, 107, 66, 10, "handler"], [44, 115, 66, 17], [44, 116, 66, 18, "config"], [44, 122, 66, 24], [44, 124, 67, 10, "ALLOWED_PROPS"], [44, 145, 67, 23], [44, 147, 68, 10], [44, 151, 68, 10, "extractGestureRelations"], [44, 182, 68, 33], [44, 184, 68, 34, "handler"], [44, 192, 68, 41], [44, 193, 69, 8], [44, 194, 70, 6], [44, 195, 70, 7], [45, 6, 71, 4], [46, 6, 73, 4], [46, 10, 73, 4, "scheduleFlushOperations"], [46, 40, 73, 27], [46, 42, 73, 28], [46, 43, 73, 29], [47, 4, 74, 2], [47, 5, 74, 3], [47, 6, 74, 4], [48, 4, 76, 2], [48, 9, 76, 7], [48, 13, 76, 13, "gesture"], [48, 20, 76, 20], [48, 24, 76, 24, "gestures<PERSON>oAtta<PERSON>"], [48, 40, 76, 40], [48, 42, 76, 42], [49, 6, 77, 4], [49, 10, 77, 10, "actionType"], [49, 20, 77, 20], [49, 23, 77, 23, "gesture"], [49, 30, 77, 30], [49, 31, 77, 31, "shouldUseReanimated"], [49, 50, 77, 50], [49, 53, 78, 8, "ActionType"], [49, 75, 78, 18], [49, 76, 78, 19, "REANIMATED_WORKLET"], [49, 94, 78, 37], [49, 97, 79, 8, "ActionType"], [49, 119, 79, 18], [49, 120, 79, 19, "JS_FUNCTION_NEW_API"], [49, 139, 79, 38], [50, 6, 81, 4], [50, 10, 81, 8, "Platform"], [50, 31, 81, 16], [50, 32, 81, 17, "OS"], [50, 34, 81, 19], [50, 39, 81, 24], [50, 44, 81, 29], [50, 46, 81, 31], [51, 8, 83, 8, "RNGestureHandlerModule"], [51, 39, 83, 30], [51, 40, 83, 31, "attachGestureHandler"], [51, 60, 83, 51], [51, 61, 85, 8, "gesture"], [51, 68, 85, 15], [51, 69, 85, 16, "handlerTag"], [51, 79, 85, 26], [51, 81, 86, 8, "viewTag"], [51, 88, 86, 15], [51, 90, 87, 8, "ActionType"], [51, 112, 87, 18], [51, 113, 87, 19, "JS_FUNCTION_OLD_API"], [51, 132, 87, 38], [52, 8, 87, 40], [53, 8, 88, 8, "webEventHandlersRef"], [53, 27, 89, 6], [53, 28, 89, 7], [54, 6, 90, 4], [54, 7, 90, 5], [54, 13, 90, 11], [55, 8, 91, 6, "RNGestureHandlerModule"], [55, 39, 91, 28], [55, 40, 91, 29, "attachGestureHandler"], [55, 60, 91, 49], [55, 61, 92, 8, "gesture"], [55, 68, 92, 15], [55, 69, 92, 16, "handlerTag"], [55, 79, 92, 26], [55, 81, 93, 8, "viewTag"], [55, 88, 93, 15], [55, 90, 94, 8, "actionType"], [55, 100, 95, 6], [55, 101, 95, 7], [56, 6, 96, 4], [57, 6, 98, 4, "MountRegistry"], [57, 34, 98, 17], [57, 35, 98, 18, "gestureWillMount"], [57, 51, 98, 34], [57, 52, 98, 35, "gesture"], [57, 59, 98, 42], [57, 60, 98, 43], [58, 4, 99, 2], [59, 4, 101, 2, "preparedGesture"], [59, 19, 101, 17], [59, 20, 101, 18, "attachedGestures"], [59, 36, 101, 34], [59, 39, 101, 37, "gestures<PERSON>oAtta<PERSON>"], [59, 55, 101, 53], [60, 4, 103, 2], [60, 8, 103, 6, "preparedGesture"], [60, 23, 103, 21], [60, 24, 103, 22, "animatedHandlers"], [60, 40, 103, 38], [60, 42, 103, 40], [61, 6, 104, 4], [61, 10, 104, 10, "isAnimatedGesture"], [61, 27, 104, 27], [61, 30, 104, 31, "g"], [61, 31, 104, 45], [61, 35, 104, 50, "g"], [61, 36, 104, 51], [61, 37, 104, 52, "shouldUseReanimated"], [61, 56, 104, 71], [62, 6, 106, 4, "preparedGesture"], [62, 21, 106, 19], [62, 22, 106, 20, "animatedHandlers"], [62, 38, 106, 36], [62, 39, 106, 37, "value"], [62, 44, 106, 42], [62, 47, 106, 45, "gestures<PERSON>oAtta<PERSON>"], [62, 63, 106, 61], [62, 64, 107, 7, "filter"], [62, 70, 107, 13], [62, 71, 107, 14, "isAnimatedGesture"], [62, 88, 107, 31], [62, 89, 107, 32], [62, 90, 108, 7, "map"], [62, 93, 108, 10], [62, 94, 108, 12, "g"], [62, 95, 108, 13], [62, 99, 108, 18, "g"], [62, 100, 108, 19], [62, 101, 108, 20, "handlers"], [62, 109, 108, 28], [62, 110, 110, 7], [63, 4, 111, 2], [64, 2, 112, 0], [65, 0, 112, 1], [65, 3]], "functionMap": {"names": ["<global>", "attachHandlers", "ghQueueMicrotask$argument_0", "isAnimatedGesture", "gesturesToAttach.filter.map$argument_0"], "mappings": "AAA;OC0B;mBCW;GDK;mBCe;GDgB;8BE8B,yCF;WGI,iBH;CDI"}}, "type": "js/module"}]}