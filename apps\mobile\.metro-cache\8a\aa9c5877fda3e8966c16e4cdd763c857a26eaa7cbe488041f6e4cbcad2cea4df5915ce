{"dependencies": [{"name": "../ReactNative/RendererProxy", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 11, "column": 26}, "end": {"line": 11, "column": 65}}], "key": "nxWoOE97N4smVxbyFcw2AtVeOu0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _require = require(_dependencyMap[0], \"../ReactNative/RendererProxy\"),\n    dispatchCommand = _require.dispatchCommand;\n  function codegenNativeCommands(options) {\n    var commandObj = {};\n    options.supportedCommands.forEach(command => {\n      commandObj[command] = function (ref) {\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        dispatchCommand(ref, command, args);\n      };\n    });\n    return commandObj;\n  }\n  var _default = exports.default = codegenNativeCommands;\n});", "lineCount": 21, "map": [[6, 2, 11, 0], [6, 6, 11, 0, "_require"], [6, 14, 11, 0], [6, 17, 11, 26, "require"], [6, 24, 11, 33], [6, 25, 11, 33, "_dependencyMap"], [6, 39, 11, 33], [6, 74, 11, 64], [6, 75, 11, 65], [7, 4, 11, 7, "dispatchCommand"], [7, 19, 11, 22], [7, 22, 11, 22, "_require"], [7, 30, 11, 22], [7, 31, 11, 7, "dispatchCommand"], [7, 46, 11, 22], [8, 2, 17, 0], [8, 11, 17, 9, "codegenNativeCommands"], [8, 32, 17, 30, "codegenNativeCommands"], [8, 33, 17, 48, "options"], [8, 40, 17, 74], [8, 42, 17, 79], [9, 4, 18, 2], [9, 8, 18, 8, "commandObj"], [9, 18, 18, 68], [9, 21, 18, 71], [9, 22, 18, 72], [9, 23, 18, 73], [10, 4, 20, 2, "options"], [10, 11, 20, 9], [10, 12, 20, 10, "supportedCommands"], [10, 29, 20, 27], [10, 30, 20, 28, "for<PERSON>ach"], [10, 37, 20, 35], [10, 38, 20, 36, "command"], [10, 45, 20, 43], [10, 49, 20, 47], [11, 6, 22, 4, "commandObj"], [11, 16, 22, 14], [11, 17, 22, 15, "command"], [11, 24, 22, 22], [11, 25, 22, 23], [11, 28, 22, 26], [11, 38, 22, 27, "ref"], [11, 41, 22, 30], [11, 43, 22, 44], [12, 8, 22, 44], [12, 17, 22, 44, "_len"], [12, 21, 22, 44], [12, 24, 22, 44, "arguments"], [12, 33, 22, 44], [12, 34, 22, 44, "length"], [12, 40, 22, 44], [12, 42, 22, 35, "args"], [12, 46, 22, 39], [12, 53, 22, 39, "Array"], [12, 58, 22, 39], [12, 59, 22, 39, "_len"], [12, 63, 22, 39], [12, 70, 22, 39, "_len"], [12, 74, 22, 39], [12, 85, 22, 39, "_key"], [12, 89, 22, 39], [12, 95, 22, 39, "_key"], [12, 99, 22, 39], [12, 102, 22, 39, "_len"], [12, 106, 22, 39], [12, 108, 22, 39, "_key"], [12, 112, 22, 39], [13, 10, 22, 35, "args"], [13, 14, 22, 39], [13, 15, 22, 39, "_key"], [13, 19, 22, 39], [13, 27, 22, 39, "arguments"], [13, 36, 22, 39], [13, 37, 22, 39, "_key"], [13, 41, 22, 39], [14, 8, 22, 39], [15, 8, 24, 6, "dispatchCommand"], [15, 23, 24, 21], [15, 24, 24, 22, "ref"], [15, 27, 24, 25], [15, 29, 24, 27, "command"], [15, 36, 24, 34], [15, 38, 24, 36, "args"], [15, 42, 24, 40], [15, 43, 24, 41], [16, 6, 25, 4], [16, 7, 25, 5], [17, 4, 26, 2], [17, 5, 26, 3], [17, 6, 26, 4], [18, 4, 28, 2], [18, 11, 28, 11, "commandObj"], [18, 21, 28, 21], [19, 2, 29, 0], [20, 2, 29, 1], [20, 6, 29, 1, "_default"], [20, 14, 29, 1], [20, 17, 29, 1, "exports"], [20, 24, 29, 1], [20, 25, 29, 1, "default"], [20, 32, 29, 1], [20, 35, 31, 15, "codegenNativeCommands"], [20, 56, 31, 36], [21, 0, 31, 36], [21, 3]], "functionMap": {"names": ["<global>", "codegenNativeCommands", "options.supportedCommands.forEach$argument_0", "commandObj.command"], "mappings": "AAA;ACgB;oCCG;0BCE;KDG;GDC;CDG"}}, "type": "js/module"}]}