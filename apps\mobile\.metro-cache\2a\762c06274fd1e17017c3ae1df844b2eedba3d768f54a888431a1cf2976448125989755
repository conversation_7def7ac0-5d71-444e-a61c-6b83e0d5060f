{"dependencies": [{"name": "../<PERSON><PERSON>/<PERSON>ert", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 29}}], "key": "K6W8+ghxcNJeV7RLvGHrYbvOSBc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  if (!global.alert) {\n    global.alert = function (text) {\n      require(_dependencyMap[0], \"../Alert/Alert\").default.alert('Alert', '' + text);\n    };\n  }\n});", "lineCount": 9, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 17, 0], [4, 6, 17, 4], [4, 7, 17, 5, "global"], [4, 13, 17, 11], [4, 14, 17, 12, "alert"], [4, 19, 17, 17], [4, 21, 17, 19], [5, 4, 18, 2, "global"], [5, 10, 18, 8], [5, 11, 18, 9, "alert"], [5, 16, 18, 14], [5, 19, 18, 17], [5, 29, 18, 27, "text"], [5, 33, 18, 39], [5, 35, 18, 41], [6, 6, 21, 4, "require"], [6, 13, 21, 11], [6, 14, 21, 11, "_dependencyMap"], [6, 28, 21, 11], [6, 49, 21, 28], [6, 50, 21, 29], [6, 51, 21, 30, "default"], [6, 58, 21, 37], [6, 59, 21, 38, "alert"], [6, 64, 21, 43], [6, 65, 21, 44], [6, 72, 21, 51], [6, 74, 21, 53], [6, 76, 21, 55], [6, 79, 21, 58, "text"], [6, 83, 21, 62], [6, 84, 21, 63], [7, 4, 22, 2], [7, 5, 22, 3], [8, 2, 23, 0], [9, 0, 23, 1], [9, 3]], "functionMap": {"names": ["<global>", "global.alert"], "mappings": "AAA;iBCiB;GDI"}}, "type": "js/module"}]}