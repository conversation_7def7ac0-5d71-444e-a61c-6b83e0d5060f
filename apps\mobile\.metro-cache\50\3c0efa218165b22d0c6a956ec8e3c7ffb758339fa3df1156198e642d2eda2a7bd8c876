{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 63}, "end": {"line": 3, "column": 51, "index": 114}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  // This file would ideally be in `worklets/specs` but\n  // codegen is pretty stupid and stops looking after first `spec` directory found.\n  var _default = exports.default = _reactNative.TurboModuleRegistry.get('WorkletsModule');\n});", "lineCount": 12, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "default"], [7, 17, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_reactNative"], [8, 18, 3, 0], [8, 21, 3, 0, "require"], [8, 28, 3, 0], [8, 29, 3, 0, "_dependencyMap"], [8, 43, 3, 0], [9, 2, 5, 0], [10, 2, 6, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_default"], [11, 14, 6, 0], [11, 17, 6, 0, "exports"], [11, 24, 6, 0], [11, 25, 6, 0, "default"], [11, 32, 6, 0], [11, 35, 12, 15, "TurboModuleRegistry"], [11, 67, 12, 34], [11, 68, 12, 35, "get"], [11, 71, 12, 38], [11, 72, 12, 45], [11, 88, 12, 61], [11, 89, 12, 62], [12, 0, 12, 62], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}