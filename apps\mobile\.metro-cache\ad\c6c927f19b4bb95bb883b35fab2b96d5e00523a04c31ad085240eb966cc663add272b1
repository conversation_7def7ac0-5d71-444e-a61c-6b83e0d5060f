{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../../../Libraries/Utilities/warnOnce", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 67}}], "key": "J/hlnQgNiiFWG+47XopOhrbx2JA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.warnNoNativePerformance = warnNoNativePerformance;\n  var _warnOnce = _interopRequireDefault(require(_dependencyMap[1], \"../../../../../Libraries/Utilities/warnOnce\"));\n  function warnNoNativePerformance() {\n    (0, _warnOnce.default)('missing-native-performance', 'Missing native implementation of Performance');\n  }\n});", "lineCount": 11, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_warnOnce"], [7, 15, 11, 0], [7, 18, 11, 0, "_interopRequireDefault"], [7, 40, 11, 0], [7, 41, 11, 0, "require"], [7, 48, 11, 0], [7, 49, 11, 0, "_dependencyMap"], [7, 63, 11, 0], [8, 2, 13, 7], [8, 11, 13, 16, "warnNoNativePerformance"], [8, 34, 13, 39, "warnNoNativePerformance"], [8, 35, 13, 39], [8, 37, 13, 42], [9, 4, 14, 2], [9, 8, 14, 2, "warnOnce"], [9, 25, 14, 10], [9, 27, 15, 4], [9, 55, 15, 32], [9, 57, 16, 4], [9, 103, 17, 2], [9, 104, 17, 3], [10, 2, 18, 0], [11, 0, 18, 1], [11, 3]], "functionMap": {"names": ["<global>", "warnNoNativePerformance"], "mappings": "AAA;OCY"}}, "type": "js/module"}]}