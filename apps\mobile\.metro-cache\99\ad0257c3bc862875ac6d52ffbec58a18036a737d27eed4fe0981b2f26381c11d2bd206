{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 49}, "end": {"line": 2, "column": 73, "index": 122}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var NativeModule = _reactNative.Platform.OS === \"android\" ? _reactNative.TurboModuleRegistry.getEnforcing(\"RNEdgeToEdge\") : null;\n  if (NativeModule != null) {\n    _reactNative.Appearance.addChangeListener(() => {\n      NativeModule.onColorSchemeChange();\n    });\n  }\n  var _default = exports.default = NativeModule;\n});", "lineCount": 14, "map": [[6, 2, 2, 0], [6, 6, 2, 0, "_reactNative"], [6, 18, 2, 0], [6, 21, 2, 0, "require"], [6, 28, 2, 0], [6, 29, 2, 0, "_dependencyMap"], [6, 43, 2, 0], [7, 2, 12, 0], [7, 6, 12, 6, "NativeModule"], [7, 18, 12, 18], [7, 21, 13, 2, "Platform"], [7, 42, 13, 10], [7, 43, 13, 11, "OS"], [7, 45, 13, 13], [7, 50, 13, 18], [7, 59, 13, 27], [7, 62, 14, 6, "TurboModuleRegistry"], [7, 94, 14, 25], [7, 95, 14, 26, "getEnforcing"], [7, 107, 14, 38], [7, 108, 14, 45], [7, 122, 14, 59], [7, 123, 14, 60], [7, 126, 15, 6], [7, 130, 15, 10], [8, 2, 17, 0], [8, 6, 17, 4, "NativeModule"], [8, 18, 17, 16], [8, 22, 17, 20], [8, 26, 17, 24], [8, 28, 17, 26], [9, 4, 18, 2, "Appearance"], [9, 27, 18, 12], [9, 28, 18, 13, "addChangeListener"], [9, 45, 18, 30], [9, 46, 18, 31], [9, 52, 18, 37], [10, 6, 19, 4, "NativeModule"], [10, 18, 19, 16], [10, 19, 19, 17, "onColorSchemeChange"], [10, 38, 19, 36], [10, 39, 19, 37], [10, 40, 19, 38], [11, 4, 20, 2], [11, 5, 20, 3], [11, 6, 20, 4], [12, 2, 21, 0], [13, 2, 21, 1], [13, 6, 21, 1, "_default"], [13, 14, 21, 1], [13, 17, 21, 1, "exports"], [13, 24, 21, 1], [13, 25, 21, 1, "default"], [13, 32, 21, 1], [13, 35, 23, 15, "NativeModule"], [13, 47, 23, 27], [14, 0, 23, 27], [14, 3]], "functionMap": {"names": ["<global>", "Appearance.addChangeListener$argument_0"], "mappings": "AAA;+BCiB;GDE"}}, "type": "js/module"}]}