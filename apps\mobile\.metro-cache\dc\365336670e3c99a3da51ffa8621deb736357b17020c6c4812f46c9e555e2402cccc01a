{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/toArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yxbT34yjmkVZuhOKwnPlwW2nTdA=", "exportNames": ["*"]}}, {"name": "escape-string-regexp", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 54, "index": 69}}], "key": "Opxn8Ttfh7QNGeF0y+BQ6rRbDGo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.extractPathFromURL = extractPathFromURL;\n  var _toArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/toArray\"));\n  var _escapeStringRegexp = _interopRequireDefault(require(_dependencyMap[2], \"escape-string-regexp\"));\n  function extractPathFromURL(prefixes, url) {\n    for (var prefix of prefixes) {\n      var protocol = prefix.match(/^[^:]+:/)?.[0] ?? '';\n      var host = prefix.replace(new RegExp(`^${(0, _escapeStringRegexp.default)(protocol)}`), '').replace(/\\/+/g, '/') // Replace multiple slash (//) with single ones\n      .replace(/^\\//, ''); // Remove extra leading slash\n\n      var prefixRegex = new RegExp(`^${(0, _escapeStringRegexp.default)(protocol)}(/)*${host.split('.').map(it => it === '*' ? '[^/]+' : (0, _escapeStringRegexp.default)(it)).join('\\\\.')}`);\n      var _url$split = url.split('?'),\n        _url$split2 = (0, _toArray2.default)(_url$split),\n        originAndPath = _url$split2[0],\n        searchParams = _url$split2.slice(1);\n      var normalizedURL = originAndPath.replace(/\\/+/g, '/').concat(searchParams.length ? `?${searchParams.join('?')}` : '');\n      if (prefixRegex.test(normalizedURL)) {\n        return normalizedURL.replace(prefixRegex, '');\n      }\n    }\n    return undefined;\n  }\n});", "lineCount": 29, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "extractPathFromURL"], [8, 28, 1, 13], [8, 31, 1, 13, "extractPathFromURL"], [8, 49, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_toArray2"], [9, 15, 1, 13], [9, 18, 1, 13, "_interopRequireDefault"], [9, 40, 1, 13], [9, 41, 1, 13, "require"], [9, 48, 1, 13], [9, 49, 1, 13, "_dependencyMap"], [9, 63, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_escapeStringRegexp"], [10, 25, 3, 0], [10, 28, 3, 0, "_interopRequireDefault"], [10, 50, 3, 0], [10, 51, 3, 0, "require"], [10, 58, 3, 0], [10, 59, 3, 0, "_dependencyMap"], [10, 73, 3, 0], [11, 2, 4, 7], [11, 11, 4, 16, "extractPathFromURL"], [11, 29, 4, 34, "extractPathFromURL"], [11, 30, 4, 35, "prefixes"], [11, 38, 4, 43], [11, 40, 4, 45, "url"], [11, 43, 4, 48], [11, 45, 4, 50], [12, 4, 5, 2], [12, 9, 5, 7], [12, 13, 5, 13, "prefix"], [12, 19, 5, 19], [12, 23, 5, 23, "prefixes"], [12, 31, 5, 31], [12, 33, 5, 33], [13, 6, 6, 4], [13, 10, 6, 10, "protocol"], [13, 18, 6, 18], [13, 21, 6, 21, "prefix"], [13, 27, 6, 27], [13, 28, 6, 28, "match"], [13, 33, 6, 33], [13, 34, 6, 34], [13, 43, 6, 43], [13, 44, 6, 44], [13, 47, 6, 47], [13, 48, 6, 48], [13, 49, 6, 49], [13, 53, 6, 53], [13, 55, 6, 55], [14, 6, 7, 4], [14, 10, 7, 10, "host"], [14, 14, 7, 14], [14, 17, 7, 17, "prefix"], [14, 23, 7, 23], [14, 24, 7, 24, "replace"], [14, 31, 7, 31], [14, 32, 7, 32], [14, 36, 7, 36, "RegExp"], [14, 42, 7, 42], [14, 43, 7, 43], [14, 47, 7, 47], [14, 51, 7, 47, "escapeStringRegexp"], [14, 78, 7, 65], [14, 80, 7, 66, "protocol"], [14, 88, 7, 74], [14, 89, 7, 75], [14, 91, 7, 77], [14, 92, 7, 78], [14, 94, 7, 80], [14, 96, 7, 82], [14, 97, 7, 83], [14, 98, 7, 84, "replace"], [14, 105, 7, 91], [14, 106, 7, 92], [14, 112, 7, 98], [14, 114, 7, 100], [14, 117, 7, 103], [14, 118, 7, 104], [14, 119, 7, 105], [15, 6, 7, 105], [15, 7, 8, 5, "replace"], [15, 14, 8, 12], [15, 15, 8, 13], [15, 20, 8, 18], [15, 22, 8, 20], [15, 24, 8, 22], [15, 25, 8, 23], [15, 26, 8, 24], [15, 27, 8, 25], [17, 6, 10, 4], [17, 10, 10, 10, "prefixRegex"], [17, 21, 10, 21], [17, 24, 10, 24], [17, 28, 10, 28, "RegExp"], [17, 34, 10, 34], [17, 35, 10, 35], [17, 39, 10, 39], [17, 43, 10, 39, "escapeStringRegexp"], [17, 70, 10, 57], [17, 72, 10, 58, "protocol"], [17, 80, 10, 66], [17, 81, 10, 67], [17, 88, 10, 74, "host"], [17, 92, 10, 78], [17, 93, 10, 79, "split"], [17, 98, 10, 84], [17, 99, 10, 85], [17, 102, 10, 88], [17, 103, 10, 89], [17, 104, 10, 90, "map"], [17, 107, 10, 93], [17, 108, 10, 94, "it"], [17, 110, 10, 96], [17, 114, 10, 100, "it"], [17, 116, 10, 102], [17, 121, 10, 107], [17, 124, 10, 110], [17, 127, 10, 113], [17, 134, 10, 120], [17, 137, 10, 123], [17, 141, 10, 123, "escapeStringRegexp"], [17, 168, 10, 141], [17, 170, 10, 142, "it"], [17, 172, 10, 144], [17, 173, 10, 145], [17, 174, 10, 146], [17, 175, 10, 147, "join"], [17, 179, 10, 151], [17, 180, 10, 152], [17, 185, 10, 157], [17, 186, 10, 158], [17, 188, 10, 160], [17, 189, 10, 161], [18, 6, 11, 4], [18, 10, 11, 4, "_url$split"], [18, 20, 11, 4], [18, 23, 11, 45, "url"], [18, 26, 11, 48], [18, 27, 11, 49, "split"], [18, 32, 11, 54], [18, 33, 11, 55], [18, 36, 11, 58], [18, 37, 11, 59], [19, 8, 11, 59, "_url$split2"], [19, 19, 11, 59], [19, 26, 11, 59, "_toArray2"], [19, 35, 11, 59], [19, 36, 11, 59, "default"], [19, 43, 11, 59], [19, 45, 11, 59, "_url$split"], [19, 55, 11, 59], [20, 8, 11, 11, "originAndPath"], [20, 21, 11, 24], [20, 24, 11, 24, "_url$split2"], [20, 35, 11, 24], [21, 8, 11, 29, "searchParams"], [21, 20, 11, 41], [21, 23, 11, 41, "_url$split2"], [21, 34, 11, 41], [21, 35, 11, 41, "slice"], [21, 40, 11, 41], [22, 6, 12, 4], [22, 10, 12, 10, "normalizedURL"], [22, 23, 12, 23], [22, 26, 12, 26, "originAndPath"], [22, 39, 12, 39], [22, 40, 12, 40, "replace"], [22, 47, 12, 47], [22, 48, 12, 48], [22, 54, 12, 54], [22, 56, 12, 56], [22, 59, 12, 59], [22, 60, 12, 60], [22, 61, 12, 61, "concat"], [22, 67, 12, 67], [22, 68, 12, 68, "searchParams"], [22, 80, 12, 80], [22, 81, 12, 81, "length"], [22, 87, 12, 87], [22, 90, 12, 90], [22, 94, 12, 94, "searchParams"], [22, 106, 12, 106], [22, 107, 12, 107, "join"], [22, 111, 12, 111], [22, 112, 12, 112], [22, 115, 12, 115], [22, 116, 12, 116], [22, 118, 12, 118], [22, 121, 12, 121], [22, 123, 12, 123], [22, 124, 12, 124], [23, 6, 13, 4], [23, 10, 13, 8, "prefixRegex"], [23, 21, 13, 19], [23, 22, 13, 20, "test"], [23, 26, 13, 24], [23, 27, 13, 25, "normalizedURL"], [23, 40, 13, 38], [23, 41, 13, 39], [23, 43, 13, 41], [24, 8, 14, 6], [24, 15, 14, 13, "normalizedURL"], [24, 28, 14, 26], [24, 29, 14, 27, "replace"], [24, 36, 14, 34], [24, 37, 14, 35, "prefixRegex"], [24, 48, 14, 46], [24, 50, 14, 48], [24, 52, 14, 50], [24, 53, 14, 51], [25, 6, 15, 4], [26, 4, 16, 2], [27, 4, 17, 2], [27, 11, 17, 9, "undefined"], [27, 20, 17, 18], [28, 2, 18, 0], [29, 0, 18, 1], [29, 3]], "functionMap": {"names": ["<global>", "extractPathFromURL", "host.split.map$argument_0"], "mappings": "AAA;OCG;8FCM,mDD;CDQ"}}, "type": "js/module"}]}