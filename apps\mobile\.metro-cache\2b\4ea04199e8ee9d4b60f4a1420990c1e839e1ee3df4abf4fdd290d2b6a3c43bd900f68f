{"dependencies": [{"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "./js-reanimated", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 68}, "end": {"line": 4, "column": 59, "index": 127}}], "key": "CCCkQMZZsw5Kf1kql0D2xuX8LFs=", "exportNames": ["*"]}}, {"name": "./NativeReanimated", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 128}, "end": {"line": 5, "column": 66, "index": 194}}], "key": "LWuCRoHOHWl9wUovEEZCI/sh4uQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ReanimatedModule = void 0;\n  var _PlatformChecker = require(_dependencyMap[0], \"../PlatformChecker\");\n  var _jsReanimated = require(_dependencyMap[1], \"./js-reanimated\");\n  var _NativeReanimated = require(_dependencyMap[2], \"./NativeReanimated\");\n  var ReanimatedModule = exports.ReanimatedModule = (0, _PlatformChecker.shouldBeUseWeb)() ? (0, _jsReanimated.createJSReanimatedModule)() : (0, _NativeReanimated.createNativeReanimatedModule)();\n});", "lineCount": 12, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "ReanimatedModule"], [7, 26, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_PlatformChecker"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_js<PERSON>ean<PERSON>"], [9, 19, 4, 0], [9, 22, 4, 0, "require"], [9, 29, 4, 0], [9, 30, 4, 0, "_dependencyMap"], [9, 44, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_NativeReanimated"], [10, 23, 5, 0], [10, 26, 5, 0, "require"], [10, 33, 5, 0], [10, 34, 5, 0, "_dependencyMap"], [10, 48, 5, 0], [11, 2, 7, 7], [11, 6, 7, 13, "ReanimatedModule"], [11, 22, 7, 29], [11, 25, 7, 29, "exports"], [11, 32, 7, 29], [11, 33, 7, 29, "ReanimatedModule"], [11, 49, 7, 29], [11, 52, 7, 32], [11, 56, 7, 32, "shouldBeUseWeb"], [11, 87, 7, 46], [11, 89, 7, 47], [11, 90, 7, 48], [11, 93, 8, 4], [11, 97, 8, 4, "createJSReanimatedModule"], [11, 135, 8, 28], [11, 137, 8, 29], [11, 138, 8, 30], [11, 141, 9, 4], [11, 145, 9, 4, "createNativeReanimatedModule"], [11, 191, 9, 32], [11, 193, 9, 33], [11, 194, 9, 34], [12, 0, 9, 35], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}