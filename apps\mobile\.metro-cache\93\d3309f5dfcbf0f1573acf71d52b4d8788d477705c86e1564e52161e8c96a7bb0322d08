{"dependencies": [{"name": "@react-navigation/core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 115, "index": 130}}], "key": "Wm75LgE4xYscVWo0KoLFlflJQCo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 131}, "end": {"line": 4, "column": 31, "index": 162}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 163}, "end": {"line": 5, "column": 40, "index": 203}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./LinkingContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 204}, "end": {"line": 6, "column": 53, "index": 257}}], "key": "r/0Yvi+HouDAqn4vN4m4I6AMfEI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useLinkProps = useLinkProps;\n  var _core = require(_dependencyMap[0], \"@react-navigation/core\");\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _LinkingContext = require(_dependencyMap[3], \"./LinkingContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var getStateFromParams = params => {\n    if (params?.state) {\n      return params.state;\n    }\n    if (params?.screen) {\n      return {\n        routes: [{\n          name: params.screen,\n          params: params.params,\n          // @ts-expect-error this is fine 🔥\n          state: params.screen ? getStateFromParams(params.params) : undefined\n        }]\n      };\n    }\n    return undefined;\n  };\n\n  /**\n   * Hook to get props for an anchor tag so it can work with in page navigation.\n   *\n   * @param props.screen Name of the screen to navigate to (e.g. `'Feeds'`).\n   * @param props.params Params to pass to the screen to navigate to (e.g. `{ sort: 'hot' }`).\n   * @param props.href Optional absolute path to use for the href (e.g. `/feeds/hot`).\n   * @param props.action Optional action to use for in-page navigation. By default, the path is parsed to an action based on linking config.\n   */\n  function useLinkProps(_ref) {\n    var screen = _ref.screen,\n      params = _ref.params,\n      href = _ref.href,\n      action = _ref.action;\n    var root = React.useContext(_core.NavigationContainerRefContext);\n    var navigation = React.useContext(_core.NavigationHelpersContext);\n    var _React$useContext = React.useContext(_LinkingContext.LinkingContext),\n      options = _React$useContext.options;\n    var onPress = e => {\n      var shouldHandle = false;\n      if (_reactNative.Platform.OS !== 'web' || !e) {\n        e?.preventDefault?.();\n        shouldHandle = true;\n      } else {\n        // ignore clicks with modifier keys\n        var hasModifierKey = 'metaKey' in e && e.metaKey || 'altKey' in e && e.altKey || 'ctrlKey' in e && e.ctrlKey || 'shiftKey' in e && e.shiftKey;\n\n        // only handle left clicks\n        var isLeftClick = 'button' in e ? e.button == null || e.button === 0 : true;\n\n        // let browser handle \"target=_blank\" etc.\n        var isSelfTarget = e.currentTarget && 'target' in e.currentTarget ? [undefined, null, '', 'self'].includes(e.currentTarget.target) : true;\n        if (!hasModifierKey && isLeftClick && isSelfTarget) {\n          e.preventDefault?.();\n          shouldHandle = true;\n        }\n      }\n      if (shouldHandle) {\n        if (action) {\n          if (navigation) {\n            navigation.dispatch(action);\n          } else if (root) {\n            root.dispatch(action);\n          } else {\n            throw new Error(\"Couldn't find a navigation object. Is your component inside NavigationContainer?\");\n          }\n        } else {\n          // @ts-expect-error This is already type-checked by the prop types\n          navigation?.navigate(screen, params);\n        }\n      }\n    };\n    var getPathFromStateHelper = options?.getPathFromState ?? _core.getPathFromState;\n    return {\n      href: href ?? (_reactNative.Platform.OS === 'web' && screen != null ? getPathFromStateHelper({\n        routes: [{\n          // @ts-expect-error this is fine 🔥\n          name: screen,\n          // @ts-expect-error this is fine 🔥\n          params: params,\n          // @ts-expect-error this is fine 🔥\n          state: getStateFromParams(params)\n        }]\n      }, options?.config) : undefined),\n      role: 'link',\n      onPress\n    };\n  }\n});", "lineCount": 97, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useLinkProps"], [7, 22, 1, 13], [7, 25, 1, 13, "useLinkProps"], [7, 37, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_reactNative"], [10, 18, 5, 0], [10, 21, 5, 0, "require"], [10, 28, 5, 0], [10, 29, 5, 0, "_dependencyMap"], [10, 43, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_LinkingContext"], [11, 21, 6, 0], [11, 24, 6, 0, "require"], [11, 31, 6, 0], [11, 32, 6, 0, "_dependencyMap"], [11, 46, 6, 0], [12, 2, 6, 53], [12, 11, 6, 53, "_interopRequireWildcard"], [12, 35, 6, 53, "e"], [12, 36, 6, 53], [12, 38, 6, 53, "t"], [12, 39, 6, 53], [12, 68, 6, 53, "WeakMap"], [12, 75, 6, 53], [12, 81, 6, 53, "r"], [12, 82, 6, 53], [12, 89, 6, 53, "WeakMap"], [12, 96, 6, 53], [12, 100, 6, 53, "n"], [12, 101, 6, 53], [12, 108, 6, 53, "WeakMap"], [12, 115, 6, 53], [12, 127, 6, 53, "_interopRequireWildcard"], [12, 150, 6, 53], [12, 162, 6, 53, "_interopRequireWildcard"], [12, 163, 6, 53, "e"], [12, 164, 6, 53], [12, 166, 6, 53, "t"], [12, 167, 6, 53], [12, 176, 6, 53, "t"], [12, 177, 6, 53], [12, 181, 6, 53, "e"], [12, 182, 6, 53], [12, 186, 6, 53, "e"], [12, 187, 6, 53], [12, 188, 6, 53, "__esModule"], [12, 198, 6, 53], [12, 207, 6, 53, "e"], [12, 208, 6, 53], [12, 214, 6, 53, "o"], [12, 215, 6, 53], [12, 217, 6, 53, "i"], [12, 218, 6, 53], [12, 220, 6, 53, "f"], [12, 221, 6, 53], [12, 226, 6, 53, "__proto__"], [12, 235, 6, 53], [12, 243, 6, 53, "default"], [12, 250, 6, 53], [12, 252, 6, 53, "e"], [12, 253, 6, 53], [12, 270, 6, 53, "e"], [12, 271, 6, 53], [12, 294, 6, 53, "e"], [12, 295, 6, 53], [12, 320, 6, 53, "e"], [12, 321, 6, 53], [12, 330, 6, 53, "f"], [12, 331, 6, 53], [12, 337, 6, 53, "o"], [12, 338, 6, 53], [12, 341, 6, 53, "t"], [12, 342, 6, 53], [12, 345, 6, 53, "n"], [12, 346, 6, 53], [12, 349, 6, 53, "r"], [12, 350, 6, 53], [12, 358, 6, 53, "o"], [12, 359, 6, 53], [12, 360, 6, 53, "has"], [12, 363, 6, 53], [12, 364, 6, 53, "e"], [12, 365, 6, 53], [12, 375, 6, 53, "o"], [12, 376, 6, 53], [12, 377, 6, 53, "get"], [12, 380, 6, 53], [12, 381, 6, 53, "e"], [12, 382, 6, 53], [12, 385, 6, 53, "o"], [12, 386, 6, 53], [12, 387, 6, 53, "set"], [12, 390, 6, 53], [12, 391, 6, 53, "e"], [12, 392, 6, 53], [12, 394, 6, 53, "f"], [12, 395, 6, 53], [12, 409, 6, 53, "_t"], [12, 411, 6, 53], [12, 415, 6, 53, "e"], [12, 416, 6, 53], [12, 432, 6, 53, "_t"], [12, 434, 6, 53], [12, 441, 6, 53, "hasOwnProperty"], [12, 455, 6, 53], [12, 456, 6, 53, "call"], [12, 460, 6, 53], [12, 461, 6, 53, "e"], [12, 462, 6, 53], [12, 464, 6, 53, "_t"], [12, 466, 6, 53], [12, 473, 6, 53, "i"], [12, 474, 6, 53], [12, 478, 6, 53, "o"], [12, 479, 6, 53], [12, 482, 6, 53, "Object"], [12, 488, 6, 53], [12, 489, 6, 53, "defineProperty"], [12, 503, 6, 53], [12, 508, 6, 53, "Object"], [12, 514, 6, 53], [12, 515, 6, 53, "getOwnPropertyDescriptor"], [12, 539, 6, 53], [12, 540, 6, 53, "e"], [12, 541, 6, 53], [12, 543, 6, 53, "_t"], [12, 545, 6, 53], [12, 552, 6, 53, "i"], [12, 553, 6, 53], [12, 554, 6, 53, "get"], [12, 557, 6, 53], [12, 561, 6, 53, "i"], [12, 562, 6, 53], [12, 563, 6, 53, "set"], [12, 566, 6, 53], [12, 570, 6, 53, "o"], [12, 571, 6, 53], [12, 572, 6, 53, "f"], [12, 573, 6, 53], [12, 575, 6, 53, "_t"], [12, 577, 6, 53], [12, 579, 6, 53, "i"], [12, 580, 6, 53], [12, 584, 6, 53, "f"], [12, 585, 6, 53], [12, 586, 6, 53, "_t"], [12, 588, 6, 53], [12, 592, 6, 53, "e"], [12, 593, 6, 53], [12, 594, 6, 53, "_t"], [12, 596, 6, 53], [12, 607, 6, 53, "f"], [12, 608, 6, 53], [12, 613, 6, 53, "e"], [12, 614, 6, 53], [12, 616, 6, 53, "t"], [12, 617, 6, 53], [13, 2, 7, 0], [13, 6, 7, 6, "getStateFromParams"], [13, 24, 7, 24], [13, 27, 7, 27, "params"], [13, 33, 7, 33], [13, 37, 7, 37], [14, 4, 8, 2], [14, 8, 8, 6, "params"], [14, 14, 8, 12], [14, 16, 8, 14, "state"], [14, 21, 8, 19], [14, 23, 8, 21], [15, 6, 9, 4], [15, 13, 9, 11, "params"], [15, 19, 9, 17], [15, 20, 9, 18, "state"], [15, 25, 9, 23], [16, 4, 10, 2], [17, 4, 11, 2], [17, 8, 11, 6, "params"], [17, 14, 11, 12], [17, 16, 11, 14, "screen"], [17, 22, 11, 20], [17, 24, 11, 22], [18, 6, 12, 4], [18, 13, 12, 11], [19, 8, 13, 6, "routes"], [19, 14, 13, 12], [19, 16, 13, 14], [19, 17, 13, 15], [20, 10, 14, 8, "name"], [20, 14, 14, 12], [20, 16, 14, 14, "params"], [20, 22, 14, 20], [20, 23, 14, 21, "screen"], [20, 29, 14, 27], [21, 10, 15, 8, "params"], [21, 16, 15, 14], [21, 18, 15, 16, "params"], [21, 24, 15, 22], [21, 25, 15, 23, "params"], [21, 31, 15, 29], [22, 10, 16, 8], [23, 10, 17, 8, "state"], [23, 15, 17, 13], [23, 17, 17, 15, "params"], [23, 23, 17, 21], [23, 24, 17, 22, "screen"], [23, 30, 17, 28], [23, 33, 17, 31, "getStateFromParams"], [23, 51, 17, 49], [23, 52, 17, 50, "params"], [23, 58, 17, 56], [23, 59, 17, 57, "params"], [23, 65, 17, 63], [23, 66, 17, 64], [23, 69, 17, 67, "undefined"], [24, 8, 18, 6], [24, 9, 18, 7], [25, 6, 19, 4], [25, 7, 19, 5], [26, 4, 20, 2], [27, 4, 21, 2], [27, 11, 21, 9, "undefined"], [27, 20, 21, 18], [28, 2, 22, 0], [28, 3, 22, 1], [30, 2, 24, 0], [31, 0, 25, 0], [32, 0, 26, 0], [33, 0, 27, 0], [34, 0, 28, 0], [35, 0, 29, 0], [36, 0, 30, 0], [37, 0, 31, 0], [38, 2, 32, 7], [38, 11, 32, 16, "useLinkProps"], [38, 23, 32, 28, "useLinkProps"], [38, 24, 32, 28, "_ref"], [38, 28, 32, 28], [38, 30, 37, 3], [39, 4, 37, 3], [39, 8, 33, 2, "screen"], [39, 14, 33, 8], [39, 17, 33, 8, "_ref"], [39, 21, 33, 8], [39, 22, 33, 2, "screen"], [39, 28, 33, 8], [40, 6, 34, 2, "params"], [40, 12, 34, 8], [40, 15, 34, 8, "_ref"], [40, 19, 34, 8], [40, 20, 34, 2, "params"], [40, 26, 34, 8], [41, 6, 35, 2, "href"], [41, 10, 35, 6], [41, 13, 35, 6, "_ref"], [41, 17, 35, 6], [41, 18, 35, 2, "href"], [41, 22, 35, 6], [42, 6, 36, 2, "action"], [42, 12, 36, 8], [42, 15, 36, 8, "_ref"], [42, 19, 36, 8], [42, 20, 36, 2, "action"], [42, 26, 36, 8], [43, 4, 38, 2], [43, 8, 38, 8, "root"], [43, 12, 38, 12], [43, 15, 38, 15, "React"], [43, 20, 38, 20], [43, 21, 38, 21, "useContext"], [43, 31, 38, 31], [43, 32, 38, 32, "NavigationContainerRefContext"], [43, 67, 38, 61], [43, 68, 38, 62], [44, 4, 39, 2], [44, 8, 39, 8, "navigation"], [44, 18, 39, 18], [44, 21, 39, 21, "React"], [44, 26, 39, 26], [44, 27, 39, 27, "useContext"], [44, 37, 39, 37], [44, 38, 39, 38, "NavigationHelpersContext"], [44, 68, 39, 62], [44, 69, 39, 63], [45, 4, 40, 2], [45, 8, 40, 2, "_React$useContext"], [45, 25, 40, 2], [45, 28, 42, 6, "React"], [45, 33, 42, 11], [45, 34, 42, 12, "useContext"], [45, 44, 42, 22], [45, 45, 42, 23, "LinkingContext"], [45, 75, 42, 37], [45, 76, 42, 38], [46, 6, 41, 4, "options"], [46, 13, 41, 11], [46, 16, 41, 11, "_React$useContext"], [46, 33, 41, 11], [46, 34, 41, 4, "options"], [46, 41, 41, 11], [47, 4, 43, 2], [47, 8, 43, 8, "onPress"], [47, 15, 43, 15], [47, 18, 43, 18, "e"], [47, 19, 43, 19], [47, 23, 43, 23], [48, 6, 44, 4], [48, 10, 44, 8, "<PERSON><PERSON><PERSON><PERSON>"], [48, 22, 44, 20], [48, 25, 44, 23], [48, 30, 44, 28], [49, 6, 45, 4], [49, 10, 45, 8, "Platform"], [49, 31, 45, 16], [49, 32, 45, 17, "OS"], [49, 34, 45, 19], [49, 39, 45, 24], [49, 44, 45, 29], [49, 48, 45, 33], [49, 49, 45, 34, "e"], [49, 50, 45, 35], [49, 52, 45, 37], [50, 8, 46, 6, "e"], [50, 9, 46, 7], [50, 11, 46, 9, "preventDefault"], [50, 25, 46, 23], [50, 28, 46, 26], [50, 29, 46, 27], [51, 8, 47, 6, "<PERSON><PERSON><PERSON><PERSON>"], [51, 20, 47, 18], [51, 23, 47, 21], [51, 27, 47, 25], [52, 6, 48, 4], [52, 7, 48, 5], [52, 13, 48, 11], [53, 8, 49, 6], [54, 8, 50, 6], [54, 12, 50, 12, "hasModifierKey"], [54, 26, 50, 26], [54, 29, 50, 29], [54, 38, 50, 38], [54, 42, 50, 42, "e"], [54, 43, 50, 43], [54, 47, 50, 47, "e"], [54, 48, 50, 48], [54, 49, 50, 49, "metaKey"], [54, 56, 50, 56], [54, 60, 50, 60], [54, 68, 50, 68], [54, 72, 50, 72, "e"], [54, 73, 50, 73], [54, 77, 50, 77, "e"], [54, 78, 50, 78], [54, 79, 50, 79, "altKey"], [54, 85, 50, 85], [54, 89, 50, 89], [54, 98, 50, 98], [54, 102, 50, 102, "e"], [54, 103, 50, 103], [54, 107, 50, 107, "e"], [54, 108, 50, 108], [54, 109, 50, 109, "ctrl<PERSON>ey"], [54, 116, 50, 116], [54, 120, 50, 120], [54, 130, 50, 130], [54, 134, 50, 134, "e"], [54, 135, 50, 135], [54, 139, 50, 139, "e"], [54, 140, 50, 140], [54, 141, 50, 141, "shift<PERSON>ey"], [54, 149, 50, 149], [56, 8, 52, 6], [57, 8, 53, 6], [57, 12, 53, 12, "isLeftClick"], [57, 23, 53, 23], [57, 26, 53, 26], [57, 34, 53, 34], [57, 38, 53, 38, "e"], [57, 39, 53, 39], [57, 42, 53, 42, "e"], [57, 43, 53, 43], [57, 44, 53, 44, "button"], [57, 50, 53, 50], [57, 54, 53, 54], [57, 58, 53, 58], [57, 62, 53, 62, "e"], [57, 63, 53, 63], [57, 64, 53, 64, "button"], [57, 70, 53, 70], [57, 75, 53, 75], [57, 76, 53, 76], [57, 79, 53, 79], [57, 83, 53, 83], [59, 8, 55, 6], [60, 8, 56, 6], [60, 12, 56, 12, "isSelfTarget"], [60, 24, 56, 24], [60, 27, 56, 27, "e"], [60, 28, 56, 28], [60, 29, 56, 29, "currentTarget"], [60, 42, 56, 42], [60, 46, 56, 46], [60, 54, 56, 54], [60, 58, 56, 58, "e"], [60, 59, 56, 59], [60, 60, 56, 60, "currentTarget"], [60, 73, 56, 73], [60, 76, 56, 76], [60, 77, 56, 77, "undefined"], [60, 86, 56, 86], [60, 88, 56, 88], [60, 92, 56, 92], [60, 94, 56, 94], [60, 96, 56, 96], [60, 98, 56, 98], [60, 104, 56, 104], [60, 105, 56, 105], [60, 106, 56, 106, "includes"], [60, 114, 56, 114], [60, 115, 56, 115, "e"], [60, 116, 56, 116], [60, 117, 56, 117, "currentTarget"], [60, 130, 56, 130], [60, 131, 56, 131, "target"], [60, 137, 56, 137], [60, 138, 56, 138], [60, 141, 56, 141], [60, 145, 56, 145], [61, 8, 57, 6], [61, 12, 57, 10], [61, 13, 57, 11, "hasModifierKey"], [61, 27, 57, 25], [61, 31, 57, 29, "isLeftClick"], [61, 42, 57, 40], [61, 46, 57, 44, "isSelfTarget"], [61, 58, 57, 56], [61, 60, 57, 58], [62, 10, 58, 8, "e"], [62, 11, 58, 9], [62, 12, 58, 10, "preventDefault"], [62, 26, 58, 24], [62, 29, 58, 27], [62, 30, 58, 28], [63, 10, 59, 8, "<PERSON><PERSON><PERSON><PERSON>"], [63, 22, 59, 20], [63, 25, 59, 23], [63, 29, 59, 27], [64, 8, 60, 6], [65, 6, 61, 4], [66, 6, 62, 4], [66, 10, 62, 8, "<PERSON><PERSON><PERSON><PERSON>"], [66, 22, 62, 20], [66, 24, 62, 22], [67, 8, 63, 6], [67, 12, 63, 10, "action"], [67, 18, 63, 16], [67, 20, 63, 18], [68, 10, 64, 8], [68, 14, 64, 12, "navigation"], [68, 24, 64, 22], [68, 26, 64, 24], [69, 12, 65, 10, "navigation"], [69, 22, 65, 20], [69, 23, 65, 21, "dispatch"], [69, 31, 65, 29], [69, 32, 65, 30, "action"], [69, 38, 65, 36], [69, 39, 65, 37], [70, 10, 66, 8], [70, 11, 66, 9], [70, 17, 66, 15], [70, 21, 66, 19, "root"], [70, 25, 66, 23], [70, 27, 66, 25], [71, 12, 67, 10, "root"], [71, 16, 67, 14], [71, 17, 67, 15, "dispatch"], [71, 25, 67, 23], [71, 26, 67, 24, "action"], [71, 32, 67, 30], [71, 33, 67, 31], [72, 10, 68, 8], [72, 11, 68, 9], [72, 17, 68, 15], [73, 12, 69, 10], [73, 18, 69, 16], [73, 22, 69, 20, "Error"], [73, 27, 69, 25], [73, 28, 69, 26], [73, 110, 69, 108], [73, 111, 69, 109], [74, 10, 70, 8], [75, 8, 71, 6], [75, 9, 71, 7], [75, 15, 71, 13], [76, 10, 72, 8], [77, 10, 73, 8, "navigation"], [77, 20, 73, 18], [77, 22, 73, 20, "navigate"], [77, 30, 73, 28], [77, 31, 73, 29, "screen"], [77, 37, 73, 35], [77, 39, 73, 37, "params"], [77, 45, 73, 43], [77, 46, 73, 44], [78, 8, 74, 6], [79, 6, 75, 4], [80, 4, 76, 2], [80, 5, 76, 3], [81, 4, 77, 2], [81, 8, 77, 8, "getPathFromStateHelper"], [81, 30, 77, 30], [81, 33, 77, 33, "options"], [81, 40, 77, 40], [81, 42, 77, 42, "getPathFromState"], [81, 58, 77, 58], [81, 62, 77, 62, "getPathFromState"], [81, 84, 77, 78], [82, 4, 78, 2], [82, 11, 78, 9], [83, 6, 79, 4, "href"], [83, 10, 79, 8], [83, 12, 79, 10, "href"], [83, 16, 79, 14], [83, 21, 79, 19, "Platform"], [83, 42, 79, 27], [83, 43, 79, 28, "OS"], [83, 45, 79, 30], [83, 50, 79, 35], [83, 55, 79, 40], [83, 59, 79, 44, "screen"], [83, 65, 79, 50], [83, 69, 79, 54], [83, 73, 79, 58], [83, 76, 79, 61, "getPathFromStateHelper"], [83, 98, 79, 83], [83, 99, 79, 84], [84, 8, 80, 6, "routes"], [84, 14, 80, 12], [84, 16, 80, 14], [84, 17, 80, 15], [85, 10, 81, 8], [86, 10, 82, 8, "name"], [86, 14, 82, 12], [86, 16, 82, 14, "screen"], [86, 22, 82, 20], [87, 10, 83, 8], [88, 10, 84, 8, "params"], [88, 16, 84, 14], [88, 18, 84, 16, "params"], [88, 24, 84, 22], [89, 10, 85, 8], [90, 10, 86, 8, "state"], [90, 15, 86, 13], [90, 17, 86, 15, "getStateFromParams"], [90, 35, 86, 33], [90, 36, 86, 34, "params"], [90, 42, 86, 40], [91, 8, 87, 6], [91, 9, 87, 7], [92, 6, 88, 4], [92, 7, 88, 5], [92, 9, 88, 7, "options"], [92, 16, 88, 14], [92, 18, 88, 16, "config"], [92, 24, 88, 22], [92, 25, 88, 23], [92, 28, 88, 26, "undefined"], [92, 37, 88, 35], [92, 38, 88, 36], [93, 6, 89, 4, "role"], [93, 10, 89, 8], [93, 12, 89, 10], [93, 18, 89, 16], [94, 6, 90, 4, "onPress"], [95, 4, 91, 2], [95, 5, 91, 3], [96, 2, 92, 0], [97, 0, 92, 1], [97, 3]], "functionMap": {"names": ["<global>", "getStateFromParams", "useLinkProps", "onPress"], "mappings": "AAA;2BCM;CDe;OEU;kBCW;GDiC;CFgB"}}, "type": "js/module"}]}