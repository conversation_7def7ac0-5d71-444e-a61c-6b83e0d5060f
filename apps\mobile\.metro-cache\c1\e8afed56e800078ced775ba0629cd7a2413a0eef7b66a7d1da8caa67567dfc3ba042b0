{"dependencies": [{"name": "./typeof.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 14, "index": 14}, "end": {"line": 1, "column": 36, "index": 36}}], "key": "3Tw2gscGXiWiW1gdVz1cVllPjgA=", "exportNames": ["*"]}}, {"name": "./toPrimitive.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 2, "column": 18, "index": 67}, "end": {"line": 2, "column": 45, "index": 94}}], "key": "6ufrlwJUHHLLvES1ZZf7kiK39Jo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _typeof = require(_dependencyMap[0], \"./typeof.js\")[\"default\"];\n  var toPrimitive = require(_dependencyMap[1], \"./toPrimitive.js\");\n  function toPropertyKey(t) {\n    var i = toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n  }\n  module.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 9, "map": [[2, 2, 1, 0], [2, 6, 1, 4, "_typeof"], [2, 13, 1, 11], [2, 16, 1, 14, "require"], [2, 23, 1, 21], [2, 24, 1, 21, "_dependencyMap"], [2, 38, 1, 21], [2, 56, 1, 35], [2, 57, 1, 36], [2, 58, 1, 37], [2, 67, 1, 46], [2, 68, 1, 47], [3, 2, 2, 0], [3, 6, 2, 4, "toPrimitive"], [3, 17, 2, 15], [3, 20, 2, 18, "require"], [3, 27, 2, 25], [3, 28, 2, 25, "_dependencyMap"], [3, 42, 2, 25], [3, 65, 2, 44], [3, 66, 2, 45], [4, 2, 3, 0], [4, 11, 3, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [4, 24, 3, 22, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [4, 25, 3, 23, "t"], [4, 26, 3, 24], [4, 28, 3, 26], [5, 4, 4, 2], [5, 8, 4, 6, "i"], [5, 9, 4, 7], [5, 12, 4, 10, "toPrimitive"], [5, 23, 4, 21], [5, 24, 4, 22, "t"], [5, 25, 4, 23], [5, 27, 4, 25], [5, 35, 4, 33], [5, 36, 4, 34], [6, 4, 5, 2], [6, 11, 5, 9], [6, 19, 5, 17], [6, 23, 5, 21, "_typeof"], [6, 30, 5, 28], [6, 31, 5, 29, "i"], [6, 32, 5, 30], [6, 33, 5, 31], [6, 36, 5, 34, "i"], [6, 37, 5, 35], [6, 40, 5, 38, "i"], [6, 41, 5, 39], [6, 44, 5, 42], [6, 46, 5, 44], [7, 2, 6, 0], [8, 2, 7, 0, "module"], [8, 8, 7, 6], [8, 9, 7, 7, "exports"], [8, 16, 7, 14], [8, 19, 7, 17, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 32, 7, 30], [8, 34, 7, 32, "module"], [8, 40, 7, 38], [8, 41, 7, 39, "exports"], [8, 48, 7, 46], [8, 49, 7, 47, "__esModule"], [8, 59, 7, 57], [8, 62, 7, 60], [8, 66, 7, 64], [8, 68, 7, 66, "module"], [8, 74, 7, 72], [8, 75, 7, 73, "exports"], [8, 82, 7, 80], [8, 83, 7, 81], [8, 92, 7, 90], [8, 93, 7, 91], [8, 96, 7, 94, "module"], [8, 102, 7, 100], [8, 103, 7, 101, "exports"], [8, 110, 7, 108], [9, 0, 7, 109], [9, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;ACE;CDG"}}, "type": "js/module"}]}