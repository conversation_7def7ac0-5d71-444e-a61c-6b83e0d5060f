{"dependencies": [{"name": "./getInvertedMultiplier.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 67, "index": 82}}], "key": "V7zj8wNC7g6lb7YqJtSiJnVPzHw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getDistanceForDirection = getDistanceForDirection;\n  var _getInvertedMultiplier = require(_dependencyMap[0], \"./getInvertedMultiplier.js\");\n  function getDistanceForDirection(layout, gestureDirection, isRTL) {\n    var multiplier = (0, _getInvertedMultiplier.getInvertedMultiplier)(gestureDirection, isRTL);\n    switch (gestureDirection) {\n      case 'vertical':\n      case 'vertical-inverted':\n        return layout.height * multiplier;\n      case 'horizontal':\n      case 'horizontal-inverted':\n        return layout.width * multiplier;\n    }\n  }\n});", "lineCount": 20, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getDistanceForDirection"], [7, 33, 1, 13], [7, 36, 1, 13, "getDistanceForDirection"], [7, 59, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_getInvertedMultiplier"], [8, 28, 3, 0], [8, 31, 3, 0, "require"], [8, 38, 3, 0], [8, 39, 3, 0, "_dependencyMap"], [8, 53, 3, 0], [9, 2, 4, 7], [9, 11, 4, 16, "getDistanceForDirection"], [9, 34, 4, 39, "getDistanceForDirection"], [9, 35, 4, 40, "layout"], [9, 41, 4, 46], [9, 43, 4, 48, "gestureDirection"], [9, 59, 4, 64], [9, 61, 4, 66, "isRTL"], [9, 66, 4, 71], [9, 68, 4, 73], [10, 4, 5, 2], [10, 8, 5, 8, "multiplier"], [10, 18, 5, 18], [10, 21, 5, 21], [10, 25, 5, 21, "getInvertedMultiplier"], [10, 69, 5, 42], [10, 71, 5, 43, "gestureDirection"], [10, 87, 5, 59], [10, 89, 5, 61, "isRTL"], [10, 94, 5, 66], [10, 95, 5, 67], [11, 4, 6, 2], [11, 12, 6, 10, "gestureDirection"], [11, 28, 6, 26], [12, 6, 7, 4], [12, 11, 7, 9], [12, 21, 7, 19], [13, 6, 8, 4], [13, 11, 8, 9], [13, 30, 8, 28], [14, 8, 9, 6], [14, 15, 9, 13, "layout"], [14, 21, 9, 19], [14, 22, 9, 20, "height"], [14, 28, 9, 26], [14, 31, 9, 29, "multiplier"], [14, 41, 9, 39], [15, 6, 10, 4], [15, 11, 10, 9], [15, 23, 10, 21], [16, 6, 11, 4], [16, 11, 11, 9], [16, 32, 11, 30], [17, 8, 12, 6], [17, 15, 12, 13, "layout"], [17, 21, 12, 19], [17, 22, 12, 20, "width"], [17, 27, 12, 25], [17, 30, 12, 28, "multiplier"], [17, 40, 12, 38], [18, 4, 13, 2], [19, 2, 14, 0], [20, 0, 14, 1], [20, 3]], "functionMap": {"names": ["<global>", "getDistanceForDirection"], "mappings": "AAA;OCG;CDU"}}, "type": "js/module"}]}