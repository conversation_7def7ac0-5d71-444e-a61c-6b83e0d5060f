{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 49, "index": 64}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../contexts", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 200}, "end": {"line": 11, "column": 62, "index": 262}}], "key": "wd0eGeDAyqj2GVxPDras6etTTUQ=", "exportNames": ["*"]}}, {"name": "warn-once", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 263}, "end": {"line": 12, "column": 33, "index": 296}}], "key": "vWcOfkIsCMxiS31CEQqA0rEMOUM=", "exportNames": ["*"]}}, {"name": "../fabric/ScreenStackNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 319}, "end": {"line": 17, "column": 46, "index": 417}}], "key": "RKL/RiEYDlNnlv2CmLtBjwndJh4=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[2], \"react\"));\n  var _contexts = require(_dependencyMap[3], \"../contexts\");\n  var _warnOnce = _interopRequireDefault(require(_dependencyMap[4], \"warn-once\"));\n  var _ScreenStackNativeComponent = _interopRequireDefault(require(_dependencyMap[5], \"../fabric/ScreenStackNativeComponent\"));\n  var _jsxDevRuntime = require(_dependencyMap[6], \"react/jsx-dev-runtime\");\n  var _excluded = [\"goBackGesture\", \"screensRefs\", \"currentScreenId\", \"transitionAnimation\", \"screenEdgeGesture\", \"onFinishTransitioning\", \"children\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-screens\\\\src\\\\components\\\\ScreenStack.tsx\"; // Native components\n  var assertGHProvider = (ScreenGestureDetector, goBackGesture) => {\n    var isGestureDetectorProviderNotDetected = ScreenGestureDetector.name !== 'GHWrapper' && goBackGesture !== undefined;\n    (0, _warnOnce.default)(isGestureDetectorProviderNotDetected, 'Cannot detect GestureDetectorProvider in a screen that uses `goBackGesture`. Make sure your navigator is wrapped in GestureDetectorProvider.');\n  };\n  var assertCustomScreenTransitionsProps = (screensRefs, currentScreenId, goBackGesture) => {\n    var isGestureDetectorNotConfiguredProperly = goBackGesture !== undefined && screensRefs === null && currentScreenId === undefined;\n    (0, _warnOnce.default)(isGestureDetectorNotConfiguredProperly, 'Custom Screen Transition require screensRefs and currentScreenId to be provided.');\n  };\n  function ScreenStack(props) {\n    var goBackGesture = props.goBackGesture,\n      passedScreenRefs = props.screensRefs,\n      currentScreenId = props.currentScreenId,\n      transitionAnimation = props.transitionAnimation,\n      screenEdgeGesture = props.screenEdgeGesture,\n      onFinishTransitioning = props.onFinishTransitioning,\n      children = props.children,\n      rest = (0, _objectWithoutProperties2.default)(props, _excluded);\n    var screensRefs = _react.default.useRef(passedScreenRefs?.current ?? {});\n    var ref = _react.default.useRef(null);\n    var ScreenGestureDetector = _react.default.useContext(_contexts.GHContext);\n    var gestureDetectorBridge = _react.default.useRef({\n      stackUseEffectCallback: _stackRef => {\n        // this method will be overriden in GestureDetector\n      }\n    });\n    _react.default.useEffect(() => {\n      gestureDetectorBridge.current.stackUseEffectCallback(ref);\n    });\n    assertGHProvider(ScreenGestureDetector, goBackGesture);\n    assertCustomScreenTransitionsProps(screensRefs, currentScreenId, goBackGesture);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_contexts.RNSScreensRefContext.Provider, {\n      value: screensRefs,\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(ScreenGestureDetector, {\n        gestureDetectorBridge: gestureDetectorBridge,\n        goBackGesture: goBackGesture,\n        transitionAnimation: transitionAnimation,\n        screenEdgeGesture: screenEdgeGesture ?? false,\n        screensRefs: screensRefs,\n        currentScreenId: currentScreenId,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScreenStackNativeComponent.default, {\n          ...rest,\n          /**\n           * This messy override is to conform NativeProps used by codegen and\n           * our Public API. To see reasoning go to this PR:\n           * https://github.com/software-mansion/react-native-screens/pull/2423#discussion_r1810616995\n           */\n          onFinishTransitioning: onFinishTransitioning,\n          ref: ref,\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 5\n    }, this);\n  }\n  var _default = exports.default = ScreenStack;\n});", "lineCount": 83, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_react"], [10, 12, 3, 0], [10, 15, 3, 0, "_interopRequireDefault"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 11, 0], [11, 6, 11, 0, "_contexts"], [11, 15, 11, 0], [11, 18, 11, 0, "require"], [11, 25, 11, 0], [11, 26, 11, 0, "_dependencyMap"], [11, 40, 11, 0], [12, 2, 12, 0], [12, 6, 12, 0, "_warnOnce"], [12, 15, 12, 0], [12, 18, 12, 0, "_interopRequireDefault"], [12, 40, 12, 0], [12, 41, 12, 0, "require"], [12, 48, 12, 0], [12, 49, 12, 0, "_dependencyMap"], [12, 63, 12, 0], [13, 2, 15, 0], [13, 6, 15, 0, "_ScreenStackNativeComponent"], [13, 33, 15, 0], [13, 36, 15, 0, "_interopRequireDefault"], [13, 58, 15, 0], [13, 59, 15, 0, "require"], [13, 66, 15, 0], [13, 67, 15, 0, "_dependencyMap"], [13, 81, 15, 0], [14, 2, 17, 46], [14, 6, 17, 46, "_jsxDevRuntime"], [14, 20, 17, 46], [14, 23, 17, 46, "require"], [14, 30, 17, 46], [14, 31, 17, 46, "_dependencyMap"], [14, 45, 17, 46], [15, 2, 17, 46], [15, 6, 17, 46, "_excluded"], [15, 15, 17, 46], [16, 2, 17, 46], [16, 6, 17, 46, "_jsxFileName"], [16, 18, 17, 46], [16, 127, 14, 0], [17, 2, 19, 0], [17, 6, 19, 6, "assertGHProvider"], [17, 22, 19, 22], [17, 25, 19, 25, "assertGHProvider"], [17, 26, 20, 2, "ScreenGestureDetector"], [17, 47, 22, 24], [17, 49, 23, 2, "goBackGesture"], [17, 62, 23, 42], [17, 67, 24, 5], [18, 4, 25, 2], [18, 8, 25, 8, "isGestureDetectorProviderNotDetected"], [18, 44, 25, 44], [18, 47, 26, 4, "ScreenGestureDetector"], [18, 68, 26, 25], [18, 69, 26, 26, "name"], [18, 73, 26, 30], [18, 78, 26, 35], [18, 89, 26, 46], [18, 93, 26, 50, "goBackGesture"], [18, 106, 26, 63], [18, 111, 26, 68, "undefined"], [18, 120, 26, 77], [19, 4, 28, 2], [19, 8, 28, 2, "warnOnce"], [19, 25, 28, 10], [19, 27, 29, 4, "isGestureDetectorProviderNotDetected"], [19, 63, 29, 40], [19, 65, 30, 4], [19, 207, 31, 2], [19, 208, 31, 3], [20, 2, 32, 0], [20, 3, 32, 1], [21, 2, 34, 0], [21, 6, 34, 6, "assertCustomScreenTransitionsProps"], [21, 40, 34, 40], [21, 43, 34, 43, "assertCustomScreenTransitionsProps"], [21, 44, 35, 2, "screensRefs"], [21, 55, 35, 46], [21, 57, 36, 2, "currentScreenId"], [21, 72, 36, 54], [21, 74, 37, 2, "goBackGesture"], [21, 87, 37, 50], [21, 92, 38, 5], [22, 4, 39, 2], [22, 8, 39, 8, "isGestureDetectorNotConfiguredProperly"], [22, 46, 39, 46], [22, 49, 40, 4, "goBackGesture"], [22, 62, 40, 17], [22, 67, 40, 22, "undefined"], [22, 76, 40, 31], [22, 80, 41, 4, "screensRefs"], [22, 91, 41, 15], [22, 96, 41, 20], [22, 100, 41, 24], [22, 104, 42, 4, "currentScreenId"], [22, 119, 42, 19], [22, 124, 42, 24, "undefined"], [22, 133, 42, 33], [23, 4, 44, 2], [23, 8, 44, 2, "warnOnce"], [23, 25, 44, 10], [23, 27, 45, 4, "isGestureDetectorNotConfiguredProperly"], [23, 65, 45, 42], [23, 67, 46, 4], [23, 149, 47, 2], [23, 150, 47, 3], [24, 2, 48, 0], [24, 3, 48, 1], [25, 2, 50, 0], [25, 11, 50, 9, "ScreenStack"], [25, 22, 50, 20, "ScreenStack"], [25, 23, 50, 21, "props"], [25, 28, 50, 44], [25, 30, 50, 46], [26, 4, 51, 2], [26, 8, 52, 4, "goBackGesture"], [26, 21, 52, 17], [26, 24, 60, 6, "props"], [26, 29, 60, 11], [26, 30, 52, 4, "goBackGesture"], [26, 43, 52, 17], [27, 6, 53, 17, "passedScreenRefs"], [27, 22, 53, 33], [27, 25, 60, 6, "props"], [27, 30, 60, 11], [27, 31, 53, 4, "screensRefs"], [27, 42, 53, 15], [28, 6, 54, 4, "currentScreenId"], [28, 21, 54, 19], [28, 24, 60, 6, "props"], [28, 29, 60, 11], [28, 30, 54, 4, "currentScreenId"], [28, 45, 54, 19], [29, 6, 55, 4, "transitionAnimation"], [29, 25, 55, 23], [29, 28, 60, 6, "props"], [29, 33, 60, 11], [29, 34, 55, 4, "transitionAnimation"], [29, 53, 55, 23], [30, 6, 56, 4, "screenEdgeGesture"], [30, 23, 56, 21], [30, 26, 60, 6, "props"], [30, 31, 60, 11], [30, 32, 56, 4, "screenEdgeGesture"], [30, 49, 56, 21], [31, 6, 57, 4, "onFinishTransitioning"], [31, 27, 57, 25], [31, 30, 60, 6, "props"], [31, 35, 60, 11], [31, 36, 57, 4, "onFinishTransitioning"], [31, 57, 57, 25], [32, 6, 58, 4, "children"], [32, 14, 58, 12], [32, 17, 60, 6, "props"], [32, 22, 60, 11], [32, 23, 58, 4, "children"], [32, 31, 58, 12], [33, 6, 59, 7, "rest"], [33, 10, 59, 11], [33, 17, 59, 11, "_objectWithoutProperties2"], [33, 42, 59, 11], [33, 43, 59, 11, "default"], [33, 50, 59, 11], [33, 52, 60, 6, "props"], [33, 57, 60, 11], [33, 59, 60, 11, "_excluded"], [33, 68, 60, 11], [34, 4, 62, 2], [34, 8, 62, 8, "screensRefs"], [34, 19, 62, 19], [34, 22, 62, 22, "React"], [34, 36, 62, 27], [34, 37, 62, 28, "useRef"], [34, 43, 62, 34], [34, 44, 63, 4, "passedScreenRefs"], [34, 60, 63, 20], [34, 62, 63, 22, "current"], [34, 69, 63, 29], [34, 73, 63, 33], [34, 74, 63, 34], [34, 75, 64, 2], [34, 76, 64, 3], [35, 4, 65, 2], [35, 8, 65, 8, "ref"], [35, 11, 65, 11], [35, 14, 65, 14, "React"], [35, 28, 65, 19], [35, 29, 65, 20, "useRef"], [35, 35, 65, 26], [35, 36, 65, 27], [35, 40, 65, 31], [35, 41, 65, 32], [36, 4, 66, 2], [36, 8, 66, 8, "ScreenGestureDetector"], [36, 29, 66, 29], [36, 32, 66, 32, "React"], [36, 46, 66, 37], [36, 47, 66, 38, "useContext"], [36, 57, 66, 48], [36, 58, 66, 49, "GHContext"], [36, 77, 66, 58], [36, 78, 66, 59], [37, 4, 67, 2], [37, 8, 67, 8, "gestureDetectorBridge"], [37, 29, 67, 29], [37, 32, 67, 32, "React"], [37, 46, 67, 37], [37, 47, 67, 38, "useRef"], [37, 53, 67, 44], [37, 54, 67, 68], [38, 6, 68, 4, "stackUseEffectCallback"], [38, 28, 68, 26], [38, 30, 68, 28, "_stackRef"], [38, 39, 68, 37], [38, 43, 68, 41], [39, 8, 69, 6], [40, 6, 69, 6], [41, 4, 71, 2], [41, 5, 71, 3], [41, 6, 71, 4], [42, 4, 73, 2, "React"], [42, 18, 73, 7], [42, 19, 73, 8, "useEffect"], [42, 28, 73, 17], [42, 29, 73, 18], [42, 35, 73, 24], [43, 6, 74, 4, "gestureDetectorBridge"], [43, 27, 74, 25], [43, 28, 74, 26, "current"], [43, 35, 74, 33], [43, 36, 74, 34, "stackUseEffectCallback"], [43, 58, 74, 56], [43, 59, 74, 57, "ref"], [43, 62, 74, 60], [43, 63, 74, 61], [44, 4, 75, 2], [44, 5, 75, 3], [44, 6, 75, 4], [45, 4, 77, 2, "assertGHProvider"], [45, 20, 77, 18], [45, 21, 77, 19, "ScreenGestureDetector"], [45, 42, 77, 40], [45, 44, 77, 42, "goBackGesture"], [45, 57, 77, 55], [45, 58, 77, 56], [46, 4, 79, 2, "assertCustomScreenTransitionsProps"], [46, 38, 79, 36], [46, 39, 80, 4, "screensRefs"], [46, 50, 80, 15], [46, 52, 81, 4, "currentScreenId"], [46, 67, 81, 19], [46, 69, 82, 4, "goBackGesture"], [46, 82, 83, 2], [46, 83, 83, 3], [47, 4, 85, 2], [47, 24, 86, 4], [47, 28, 86, 4, "_jsxDevRuntime"], [47, 42, 86, 4], [47, 43, 86, 4, "jsxDEV"], [47, 49, 86, 4], [47, 51, 86, 5, "_contexts"], [47, 60, 86, 5], [47, 61, 86, 5, "RNSScreensRefContext"], [47, 81, 86, 25], [47, 82, 86, 26, "Provider"], [47, 90, 86, 34], [48, 6, 86, 35, "value"], [48, 11, 86, 40], [48, 13, 86, 42, "screensRefs"], [48, 24, 86, 54], [49, 6, 86, 54, "children"], [49, 14, 86, 54], [49, 29, 87, 6], [49, 33, 87, 6, "_jsxDevRuntime"], [49, 47, 87, 6], [49, 48, 87, 6, "jsxDEV"], [49, 54, 87, 6], [49, 56, 87, 7, "ScreenGestureDetector"], [49, 77, 87, 28], [50, 8, 88, 8, "gestureDetectorBridge"], [50, 29, 88, 29], [50, 31, 88, 31, "gestureDetectorBridge"], [50, 52, 88, 53], [51, 8, 89, 8, "goBackGesture"], [51, 21, 89, 21], [51, 23, 89, 23, "goBackGesture"], [51, 36, 89, 37], [52, 8, 90, 8, "transitionAnimation"], [52, 27, 90, 27], [52, 29, 90, 29, "transitionAnimation"], [52, 48, 90, 49], [53, 8, 91, 8, "screenEdgeGesture"], [53, 25, 91, 25], [53, 27, 91, 27, "screenEdgeGesture"], [53, 44, 91, 44], [53, 48, 91, 48], [53, 53, 91, 54], [54, 8, 92, 8, "screensRefs"], [54, 19, 92, 19], [54, 21, 92, 21, "screensRefs"], [54, 32, 92, 33], [55, 8, 93, 8, "currentScreenId"], [55, 23, 93, 23], [55, 25, 93, 25, "currentScreenId"], [55, 40, 93, 41], [56, 8, 93, 41, "children"], [56, 16, 93, 41], [56, 31, 94, 8], [56, 35, 94, 8, "_jsxDevRuntime"], [56, 49, 94, 8], [56, 50, 94, 8, "jsxDEV"], [56, 56, 94, 8], [56, 58, 94, 9, "_ScreenStackNativeComponent"], [56, 85, 94, 9], [56, 86, 94, 9, "default"], [56, 93, 94, 35], [57, 10, 94, 35], [57, 13, 95, 14, "rest"], [57, 17, 95, 18], [58, 10, 96, 10], [59, 0, 97, 0], [60, 0, 98, 0], [61, 0, 99, 0], [62, 0, 100, 0], [63, 10, 101, 10, "onFinishTransitioning"], [63, 31, 101, 31], [63, 33, 102, 12, "onFinishTransitioning"], [63, 54, 103, 11], [64, 10, 104, 10, "ref"], [64, 13, 104, 13], [64, 15, 104, 15, "ref"], [64, 18, 104, 19], [65, 10, 104, 19, "children"], [65, 18, 104, 19], [65, 20, 105, 11, "children"], [66, 8, 105, 19], [67, 10, 105, 19, "fileName"], [67, 18, 105, 19], [67, 20, 105, 19, "_jsxFileName"], [67, 32, 105, 19], [68, 10, 105, 19, "lineNumber"], [68, 20, 105, 19], [69, 10, 105, 19, "columnNumber"], [69, 22, 105, 19], [70, 8, 105, 19], [70, 15, 106, 36], [71, 6, 106, 37], [72, 8, 106, 37, "fileName"], [72, 16, 106, 37], [72, 18, 106, 37, "_jsxFileName"], [72, 30, 106, 37], [73, 8, 106, 37, "lineNumber"], [73, 18, 106, 37], [74, 8, 106, 37, "columnNumber"], [74, 20, 106, 37], [75, 6, 106, 37], [75, 13, 107, 29], [76, 4, 107, 30], [77, 6, 107, 30, "fileName"], [77, 14, 107, 30], [77, 16, 107, 30, "_jsxFileName"], [77, 28, 107, 30], [78, 6, 107, 30, "lineNumber"], [78, 16, 107, 30], [79, 6, 107, 30, "columnNumber"], [79, 18, 107, 30], [80, 4, 107, 30], [80, 11, 108, 35], [80, 12, 108, 36], [81, 2, 110, 0], [82, 2, 110, 1], [82, 6, 110, 1, "_default"], [82, 14, 110, 1], [82, 17, 110, 1, "exports"], [82, 24, 110, 1], [82, 25, 110, 1, "default"], [82, 32, 110, 1], [82, 35, 112, 15, "ScreenStack"], [82, 46, 112, 26], [83, 0, 112, 26], [83, 3]], "functionMap": {"names": ["<global>", "assertGHProvider", "assertCustomScreenTransitionsProps", "ScreenStack", "React.useRef$argument_0.stackUseEffectCallback", "React.useEffect$argument_0"], "mappings": "AAA;yBCkB;CDa;2CEE;CFc;AGE;4BCkB;KDE;kBEG;GFE;CHmC"}}, "type": "js/module"}]}