{"dependencies": [{"name": "./platform-specific/findHostInstance", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 92}, "end": {"line": 8, "column": 46, "index": 183}}], "key": "B1Jj6e+RiKw449q6uclHv+35k4E=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 8, "index": 549}, "end": {"line": 21, "column": 105, "index": 646}}], "key": "GHOwPKsHyqKPAI7qNK0GYb/HcIc=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  /* eslint-disable */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getShadowNodeWrapperFromRef = getShadowNodeWrapperFromRef;\n  var _findHostInstance = require(_dependencyMap[0], \"./platform-specific/findHostInstance\");\n  var getInternalInstanceHandleFromPublicInstance;\n  function getShadowNodeWrapperFromRef(ref, hostInstance) {\n    if (getInternalInstanceHandleFromPublicInstance === undefined) {\n      try {\n        getInternalInstanceHandleFromPublicInstance = require(_dependencyMap[1], \"react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance\").getInternalInstanceHandleFromPublicInstance ?? (_ref => _ref._internalInstanceHandle);\n      } catch (e) {\n        getInternalInstanceHandleFromPublicInstance = _ref => _ref._internalInstanceHandle;\n      }\n    }\n\n    // TODO: Clean this up since 0.74 is the minimum supported version now.\n    // taken from https://github.com/facebook/react-native/commit/803bb16531697233686efd475f004c1643e03617#diff-d8172256c6d63b5d32db10e54d7b10f37a26b337d5280d89f5bfd7bcea778292R196\n    // @ts-ignore some weird stuff on RN 0.74 - see examples with scrollView\n    var scrollViewRef = ref?.getScrollResponder?.()?.getNativeScrollRef?.();\n    // @ts-ignore some weird stuff on RN 0.74  - see examples with scrollView\n    var otherScrollViewRef = ref?.getNativeScrollRef?.();\n    // @ts-ignore some weird stuff on RN 0.74 - see setNativeProps example\n    var textInputRef = ref?.__internalInstanceHandle?.stateNode?.node;\n    var resolvedRef;\n    if (scrollViewRef) {\n      resolvedRef = scrollViewRef.__internalInstanceHandle.stateNode.node;\n    } else if (otherScrollViewRef) {\n      resolvedRef = otherScrollViewRef.__internalInstanceHandle.stateNode.node;\n    } else if (textInputRef) {\n      resolvedRef = textInputRef;\n    } else {\n      var instance = hostInstance ?? (0, _findHostInstance.findHostInstance)(ref);\n      resolvedRef = getInternalInstanceHandleFromPublicInstance(instance).stateNode.node;\n    }\n    return resolvedRef;\n  }\n});", "lineCount": 41, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [5, 2, 2, 0, "Object"], [5, 8, 2, 0], [5, 9, 2, 0, "defineProperty"], [5, 23, 2, 0], [5, 24, 2, 0, "exports"], [5, 31, 2, 0], [6, 4, 2, 0, "value"], [6, 9, 2, 0], [7, 2, 2, 0], [8, 2, 2, 0, "exports"], [8, 9, 2, 0], [8, 10, 2, 0, "getShadowNodeWrapperFromRef"], [8, 37, 2, 0], [8, 40, 2, 0, "getShadowNodeWrapperFromRef"], [8, 67, 2, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_findHostInstance"], [9, 23, 5, 0], [9, 26, 5, 0, "require"], [9, 33, 5, 0], [9, 34, 5, 0, "_dependencyMap"], [9, 48, 5, 0], [10, 2, 10, 0], [10, 6, 10, 4, "getInternalInstanceHandleFromPublicInstance"], [10, 49, 12, 1], [11, 2, 14, 7], [11, 11, 14, 16, "getShadowNodeWrapperFromRef"], [11, 38, 14, 43, "getShadowNodeWrapperFromRef"], [11, 39, 15, 2, "ref"], [11, 42, 15, 22], [11, 44, 16, 2, "hostInstance"], [11, 56, 16, 29], [11, 58, 17, 21], [12, 4, 18, 2], [12, 8, 18, 6, "getInternalInstanceHandleFromPublicInstance"], [12, 51, 18, 49], [12, 56, 18, 54, "undefined"], [12, 65, 18, 63], [12, 67, 18, 65], [13, 6, 19, 4], [13, 10, 19, 8], [14, 8, 20, 6, "getInternalInstanceHandleFromPublicInstance"], [14, 51, 20, 49], [14, 54, 21, 8, "require"], [14, 61, 21, 15], [14, 62, 21, 15, "_dependencyMap"], [14, 76, 21, 15], [14, 169, 21, 104], [14, 170, 21, 105], [14, 171, 22, 11, "getInternalInstanceHandleFromPublicInstance"], [14, 214, 22, 54], [14, 219, 23, 10, "_ref"], [14, 223, 23, 19], [14, 227, 23, 24, "_ref"], [14, 231, 23, 28], [14, 232, 23, 29, "_internalInstanceHandle"], [14, 255, 23, 52], [14, 256, 23, 53], [15, 6, 24, 4], [15, 7, 24, 5], [15, 8, 24, 6], [15, 15, 24, 13, "e"], [15, 16, 24, 14], [15, 18, 24, 16], [16, 8, 25, 6, "getInternalInstanceHandleFromPublicInstance"], [16, 51, 25, 49], [16, 54, 25, 53, "_ref"], [16, 58, 25, 62], [16, 62, 26, 8, "_ref"], [16, 66, 26, 12], [16, 67, 26, 13, "_internalInstanceHandle"], [16, 90, 26, 36], [17, 6, 27, 4], [18, 4, 28, 2], [20, 4, 30, 2], [21, 4, 31, 2], [22, 4, 32, 2], [23, 4, 33, 2], [23, 8, 33, 8, "scrollViewRef"], [23, 21, 33, 21], [23, 24, 33, 24, "ref"], [23, 27, 33, 27], [23, 29, 33, 29, "getScrollResponder"], [23, 47, 33, 47], [23, 50, 33, 50], [23, 51, 33, 51], [23, 53, 33, 53, "getNativeScrollRef"], [23, 71, 33, 71], [23, 74, 33, 74], [23, 75, 33, 75], [24, 4, 34, 2], [25, 4, 35, 2], [25, 8, 35, 8, "otherScrollViewRef"], [25, 26, 35, 26], [25, 29, 35, 29, "ref"], [25, 32, 35, 32], [25, 34, 35, 34, "getNativeScrollRef"], [25, 52, 35, 52], [25, 55, 35, 55], [25, 56, 35, 56], [26, 4, 36, 2], [27, 4, 37, 2], [27, 8, 37, 8, "textInputRef"], [27, 20, 37, 20], [27, 23, 37, 23, "ref"], [27, 26, 37, 26], [27, 28, 37, 28, "__internalInstanceHandle"], [27, 52, 37, 52], [27, 54, 37, 54, "stateNode"], [27, 63, 37, 63], [27, 65, 37, 65, "node"], [27, 69, 37, 69], [28, 4, 39, 2], [28, 8, 39, 6, "resolvedRef"], [28, 19, 39, 17], [29, 4, 40, 2], [29, 8, 40, 6, "scrollViewRef"], [29, 21, 40, 19], [29, 23, 40, 21], [30, 6, 41, 4, "resolvedRef"], [30, 17, 41, 15], [30, 20, 41, 18, "scrollViewRef"], [30, 33, 41, 31], [30, 34, 41, 32, "__internalInstanceHandle"], [30, 58, 41, 56], [30, 59, 41, 57, "stateNode"], [30, 68, 41, 66], [30, 69, 41, 67, "node"], [30, 73, 41, 71], [31, 4, 42, 2], [31, 5, 42, 3], [31, 11, 42, 9], [31, 15, 42, 13, "otherScrollViewRef"], [31, 33, 42, 31], [31, 35, 42, 33], [32, 6, 43, 4, "resolvedRef"], [32, 17, 43, 15], [32, 20, 43, 18, "otherScrollViewRef"], [32, 38, 43, 36], [32, 39, 43, 37, "__internalInstanceHandle"], [32, 63, 43, 61], [32, 64, 43, 62, "stateNode"], [32, 73, 43, 71], [32, 74, 43, 72, "node"], [32, 78, 43, 76], [33, 4, 44, 2], [33, 5, 44, 3], [33, 11, 44, 9], [33, 15, 44, 13, "textInputRef"], [33, 27, 44, 25], [33, 29, 44, 27], [34, 6, 45, 4, "resolvedRef"], [34, 17, 45, 15], [34, 20, 45, 18, "textInputRef"], [34, 32, 45, 30], [35, 4, 46, 2], [35, 5, 46, 3], [35, 11, 46, 9], [36, 6, 47, 4], [36, 10, 47, 10, "instance"], [36, 18, 47, 18], [36, 21, 47, 21, "hostInstance"], [36, 33, 47, 33], [36, 37, 47, 37], [36, 41, 47, 37, "findHostInstance"], [36, 75, 47, 53], [36, 77, 47, 54, "ref"], [36, 80, 47, 57], [36, 81, 47, 58], [37, 6, 48, 4, "resolvedRef"], [37, 17, 48, 15], [37, 20, 49, 6, "getInternalInstanceHandleFromPublicInstance"], [37, 63, 49, 49], [37, 64, 49, 50, "instance"], [37, 72, 49, 58], [37, 73, 49, 59], [37, 74, 49, 60, "stateNode"], [37, 83, 49, 69], [37, 84, 49, 70, "node"], [37, 88, 49, 74], [38, 4, 50, 2], [39, 4, 52, 2], [39, 11, 52, 9, "resolvedRef"], [39, 22, 52, 20], [40, 2, 53, 0], [41, 0, 53, 1], [41, 3]], "functionMap": {"names": ["<global>", "getShadowNodeWrapperFromRef", "<anonymous>", "getInternalInstanceHandleFromPublicInstance"], "mappings": "AAA;OCa;SCS,2CD;oDEE;oCFC;CD2B"}}, "type": "js/module"}]}