{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 8, "column": 22, "index": 133}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "warn-once", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 134}, "end": {"line": 9, "column": 33, "index": 167}}], "key": "vWcOfkIsCMxiS31CEQqA0rEMOUM=", "exportNames": ["*"]}}, {"name": "./DebugContainer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 169}, "end": {"line": 11, "column": 46, "index": 215}}], "key": "00nR495UyKRlwAAkSGQ6ISyX2ng=", "exportNames": ["*"]}}, {"name": "./ScreenStackHeaderConfig", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 286}, "end": {"line": 13, "column": 68, "index": 354}}], "key": "ODmM8Zo4+r0I2znLfxVk65uhabc=", "exportNames": ["*"]}}, {"name": "./Screen", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 355}, "end": {"line": 14, "column": 30, "index": 385}}], "key": "Ddkhns95vV7IG/j2ilBD9xB8a68=", "exportNames": ["*"]}}, {"name": "./ScreenStack", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 386}, "end": {"line": 15, "column": 40, "index": 426}}], "key": "0/etDhxVbASMuuXiHzxxRmW0HzY=", "exportNames": ["*"]}}, {"name": "../contexts", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 427}, "end": {"line": 16, "column": 51, "index": 478}}], "key": "wd0eGeDAyqj2GVxPDras6etTTUQ=", "exportNames": ["*"]}}, {"name": "./ScreenFooter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 479}, "end": {"line": 17, "column": 49, "index": 528}}], "key": "SG8niKWihz3sf8/QHPXl74IHs08=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _warnOnce = _interopRequireDefault(require(_dependencyMap[4], \"warn-once\"));\n  var _DebugContainer = _interopRequireDefault(require(_dependencyMap[5], \"./DebugContainer\"));\n  var _ScreenStackHeaderConfig = require(_dependencyMap[6], \"./ScreenStackHeaderConfig\");\n  var _Screen = _interopRequireDefault(require(_dependencyMap[7], \"./Screen\"));\n  var _ScreenStack = _interopRequireDefault(require(_dependencyMap[8], \"./ScreenStack\"));\n  var _contexts = require(_dependencyMap[9], \"../contexts\");\n  var _ScreenFooter = require(_dependencyMap[10], \"./ScreenFooter\");\n  var _jsxDevRuntime = require(_dependencyMap[11], \"react/jsx-dev-runtime\");\n  var _excluded = [\"children\", \"headerConfig\", \"activityState\", \"shouldFreeze\", \"stackPresentation\", \"sheetAllowedDetents\", \"contentStyle\", \"style\", \"screenId\", \"unstable_sheetFooter\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-screens\\\\src\\\\components\\\\ScreenStackItem.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function ScreenStackItem(_ref, ref) {\n    var children = _ref.children,\n      headerConfig = _ref.headerConfig,\n      activityState = _ref.activityState,\n      shouldFreeze = _ref.shouldFreeze,\n      stackPresentation = _ref.stackPresentation,\n      sheetAllowedDetents = _ref.sheetAllowedDetents,\n      contentStyle = _ref.contentStyle,\n      style = _ref.style,\n      screenId = _ref.screenId,\n      unstable_sheetFooter = _ref.unstable_sheetFooter,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var currentScreenRef = React.useRef(null);\n    var screenRefs = React.useContext(_contexts.RNSScreensRefContext);\n    React.useImperativeHandle(ref, () => currentScreenRef.current);\n    var isHeaderInModal = _reactNative.Platform.OS === 'android' ? false : stackPresentation !== 'push' && headerConfig?.hidden === false;\n    var headerHiddenPreviousRef = React.useRef(headerConfig?.hidden);\n    React.useEffect(() => {\n      (0, _warnOnce.default)(_reactNative.Platform.OS !== 'android' && stackPresentation !== 'push' && headerHiddenPreviousRef.current !== headerConfig?.hidden, `Dynamically changing header's visibility in modals will result in remounting the screen and losing all local state.`);\n      headerHiddenPreviousRef.current = headerConfig?.hidden;\n    }, [headerConfig?.hidden, stackPresentation]);\n    var content = /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_DebugContainer.default, {\n        style: [stackPresentation === 'formSheet' ? _reactNative.Platform.OS === 'ios' ? styles.absolute : sheetAllowedDetents === 'fitToContents' ? null : styles.container : styles.container, contentStyle],\n        stackPresentation: stackPresentation ?? 'push',\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScreenStackHeaderConfig.ScreenStackHeaderConfig, {\n        ...headerConfig\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 7\n      }, this), stackPresentation === 'formSheet' && unstable_sheetFooter && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScreenFooter.FooterComponent, {\n        children: unstable_sheetFooter()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n\n    // We take backgroundColor from contentStyle and apply it on Screen.\n    // This allows to workaround one issue with truncated\n    // content with formSheet presentation.\n    var internalScreenStyle;\n    if (stackPresentation === 'formSheet' && contentStyle) {\n      var flattenContentStyles = _reactNative.StyleSheet.flatten(contentStyle);\n      internalScreenStyle = {\n        backgroundColor: flattenContentStyles?.backgroundColor\n      };\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Screen.default, {\n      ref: node => {\n        currentScreenRef.current = node;\n        if (screenRefs === null) {\n          console.warn('Looks like RNSScreensRefContext is missing. Make sure the ScreenStack component is wrapped in it');\n          return;\n        }\n        var currentRefs = screenRefs.current;\n        if (node === null) {\n          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n          delete currentRefs[screenId];\n        } else {\n          currentRefs[screenId] = {\n            current: node\n          };\n        }\n      },\n      enabled: true,\n      isNativeStack: true,\n      activityState: activityState,\n      shouldFreeze: shouldFreeze,\n      stackPresentation: stackPresentation,\n      hasLargeHeader: headerConfig?.largeTitle ?? false,\n      sheetAllowedDetents: sheetAllowedDetents,\n      style: [style, internalScreenStyle],\n      ...rest,\n      children: isHeaderInModal ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScreenStack.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Screen.default, {\n          enabled: true,\n          isNativeStack: true,\n          activityState: activityState,\n          shouldFreeze: shouldFreeze,\n          hasLargeHeader: headerConfig?.largeTitle ?? false,\n          style: _reactNative.StyleSheet.absoluteFill,\n          children: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this) : content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 5\n    }, this);\n  }\n  var _default = exports.default = /*#__PURE__*/React.forwardRef(ScreenStackItem);\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flex: 1\n    },\n    absolute: {\n      position: 'absolute',\n      top: 0,\n      start: 0,\n      end: 0\n    }\n  });\n});", "lineCount": 140, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "React"], [8, 11, 1, 0], [8, 14, 1, 0, "_interopRequireWildcard"], [8, 37, 1, 0], [8, 38, 1, 0, "require"], [8, 45, 1, 0], [8, 46, 1, 0, "_dependencyMap"], [8, 60, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 9, 0], [10, 6, 9, 0, "_warnOnce"], [10, 15, 9, 0], [10, 18, 9, 0, "_interopRequireDefault"], [10, 40, 9, 0], [10, 41, 9, 0, "require"], [10, 48, 9, 0], [10, 49, 9, 0, "_dependencyMap"], [10, 63, 9, 0], [11, 2, 11, 0], [11, 6, 11, 0, "_DebugContainer"], [11, 21, 11, 0], [11, 24, 11, 0, "_interopRequireDefault"], [11, 46, 11, 0], [11, 47, 11, 0, "require"], [11, 54, 11, 0], [11, 55, 11, 0, "_dependencyMap"], [11, 69, 11, 0], [12, 2, 13, 0], [12, 6, 13, 0, "_ScreenStackHeaderConfig"], [12, 30, 13, 0], [12, 33, 13, 0, "require"], [12, 40, 13, 0], [12, 41, 13, 0, "_dependencyMap"], [12, 55, 13, 0], [13, 2, 14, 0], [13, 6, 14, 0, "_Screen"], [13, 13, 14, 0], [13, 16, 14, 0, "_interopRequireDefault"], [13, 38, 14, 0], [13, 39, 14, 0, "require"], [13, 46, 14, 0], [13, 47, 14, 0, "_dependencyMap"], [13, 61, 14, 0], [14, 2, 15, 0], [14, 6, 15, 0, "_ScreenStack"], [14, 18, 15, 0], [14, 21, 15, 0, "_interopRequireDefault"], [14, 43, 15, 0], [14, 44, 15, 0, "require"], [14, 51, 15, 0], [14, 52, 15, 0, "_dependencyMap"], [14, 66, 15, 0], [15, 2, 16, 0], [15, 6, 16, 0, "_contexts"], [15, 15, 16, 0], [15, 18, 16, 0, "require"], [15, 25, 16, 0], [15, 26, 16, 0, "_dependencyMap"], [15, 40, 16, 0], [16, 2, 17, 0], [16, 6, 17, 0, "_ScreenFooter"], [16, 19, 17, 0], [16, 22, 17, 0, "require"], [16, 29, 17, 0], [16, 30, 17, 0, "_dependencyMap"], [16, 44, 17, 0], [17, 2, 17, 49], [17, 6, 17, 49, "_jsxDevRuntime"], [17, 20, 17, 49], [17, 23, 17, 49, "require"], [17, 30, 17, 49], [17, 31, 17, 49, "_dependencyMap"], [17, 45, 17, 49], [18, 2, 17, 49], [18, 6, 17, 49, "_excluded"], [18, 15, 17, 49], [19, 2, 17, 49], [19, 6, 17, 49, "_jsxFileName"], [19, 18, 17, 49], [20, 2, 17, 49], [20, 11, 17, 49, "_interopRequireWildcard"], [20, 35, 17, 49, "e"], [20, 36, 17, 49], [20, 38, 17, 49, "t"], [20, 39, 17, 49], [20, 68, 17, 49, "WeakMap"], [20, 75, 17, 49], [20, 81, 17, 49, "r"], [20, 82, 17, 49], [20, 89, 17, 49, "WeakMap"], [20, 96, 17, 49], [20, 100, 17, 49, "n"], [20, 101, 17, 49], [20, 108, 17, 49, "WeakMap"], [20, 115, 17, 49], [20, 127, 17, 49, "_interopRequireWildcard"], [20, 150, 17, 49], [20, 162, 17, 49, "_interopRequireWildcard"], [20, 163, 17, 49, "e"], [20, 164, 17, 49], [20, 166, 17, 49, "t"], [20, 167, 17, 49], [20, 176, 17, 49, "t"], [20, 177, 17, 49], [20, 181, 17, 49, "e"], [20, 182, 17, 49], [20, 186, 17, 49, "e"], [20, 187, 17, 49], [20, 188, 17, 49, "__esModule"], [20, 198, 17, 49], [20, 207, 17, 49, "e"], [20, 208, 17, 49], [20, 214, 17, 49, "o"], [20, 215, 17, 49], [20, 217, 17, 49, "i"], [20, 218, 17, 49], [20, 220, 17, 49, "f"], [20, 221, 17, 49], [20, 226, 17, 49, "__proto__"], [20, 235, 17, 49], [20, 243, 17, 49, "default"], [20, 250, 17, 49], [20, 252, 17, 49, "e"], [20, 253, 17, 49], [20, 270, 17, 49, "e"], [20, 271, 17, 49], [20, 294, 17, 49, "e"], [20, 295, 17, 49], [20, 320, 17, 49, "e"], [20, 321, 17, 49], [20, 330, 17, 49, "f"], [20, 331, 17, 49], [20, 337, 17, 49, "o"], [20, 338, 17, 49], [20, 341, 17, 49, "t"], [20, 342, 17, 49], [20, 345, 17, 49, "n"], [20, 346, 17, 49], [20, 349, 17, 49, "r"], [20, 350, 17, 49], [20, 358, 17, 49, "o"], [20, 359, 17, 49], [20, 360, 17, 49, "has"], [20, 363, 17, 49], [20, 364, 17, 49, "e"], [20, 365, 17, 49], [20, 375, 17, 49, "o"], [20, 376, 17, 49], [20, 377, 17, 49, "get"], [20, 380, 17, 49], [20, 381, 17, 49, "e"], [20, 382, 17, 49], [20, 385, 17, 49, "o"], [20, 386, 17, 49], [20, 387, 17, 49, "set"], [20, 390, 17, 49], [20, 391, 17, 49, "e"], [20, 392, 17, 49], [20, 394, 17, 49, "f"], [20, 395, 17, 49], [20, 409, 17, 49, "_t"], [20, 411, 17, 49], [20, 415, 17, 49, "e"], [20, 416, 17, 49], [20, 432, 17, 49, "_t"], [20, 434, 17, 49], [20, 441, 17, 49, "hasOwnProperty"], [20, 455, 17, 49], [20, 456, 17, 49, "call"], [20, 460, 17, 49], [20, 461, 17, 49, "e"], [20, 462, 17, 49], [20, 464, 17, 49, "_t"], [20, 466, 17, 49], [20, 473, 17, 49, "i"], [20, 474, 17, 49], [20, 478, 17, 49, "o"], [20, 479, 17, 49], [20, 482, 17, 49, "Object"], [20, 488, 17, 49], [20, 489, 17, 49, "defineProperty"], [20, 503, 17, 49], [20, 508, 17, 49, "Object"], [20, 514, 17, 49], [20, 515, 17, 49, "getOwnPropertyDescriptor"], [20, 539, 17, 49], [20, 540, 17, 49, "e"], [20, 541, 17, 49], [20, 543, 17, 49, "_t"], [20, 545, 17, 49], [20, 552, 17, 49, "i"], [20, 553, 17, 49], [20, 554, 17, 49, "get"], [20, 557, 17, 49], [20, 561, 17, 49, "i"], [20, 562, 17, 49], [20, 563, 17, 49, "set"], [20, 566, 17, 49], [20, 570, 17, 49, "o"], [20, 571, 17, 49], [20, 572, 17, 49, "f"], [20, 573, 17, 49], [20, 575, 17, 49, "_t"], [20, 577, 17, 49], [20, 579, 17, 49, "i"], [20, 580, 17, 49], [20, 584, 17, 49, "f"], [20, 585, 17, 49], [20, 586, 17, 49, "_t"], [20, 588, 17, 49], [20, 592, 17, 49, "e"], [20, 593, 17, 49], [20, 594, 17, 49, "_t"], [20, 596, 17, 49], [20, 607, 17, 49, "f"], [20, 608, 17, 49], [20, 613, 17, 49, "e"], [20, 614, 17, 49], [20, 616, 17, 49, "t"], [20, 617, 17, 49], [21, 2, 28, 0], [21, 11, 28, 9, "ScreenStackItem"], [21, 26, 28, 24, "ScreenStackItem"], [21, 27, 28, 24, "_ref"], [21, 31, 28, 24], [21, 33, 43, 2, "ref"], [21, 36, 43, 31], [21, 38, 44, 2], [22, 4, 44, 2], [22, 8, 30, 4, "children"], [22, 16, 30, 12], [22, 19, 30, 12, "_ref"], [22, 23, 30, 12], [22, 24, 30, 4, "children"], [22, 32, 30, 12], [23, 6, 31, 4, "headerConfig"], [23, 18, 31, 16], [23, 21, 31, 16, "_ref"], [23, 25, 31, 16], [23, 26, 31, 4, "headerConfig"], [23, 38, 31, 16], [24, 6, 32, 4, "activityState"], [24, 19, 32, 17], [24, 22, 32, 17, "_ref"], [24, 26, 32, 17], [24, 27, 32, 4, "activityState"], [24, 40, 32, 17], [25, 6, 33, 4, "shouldFreeze"], [25, 18, 33, 16], [25, 21, 33, 16, "_ref"], [25, 25, 33, 16], [25, 26, 33, 4, "shouldFreeze"], [25, 38, 33, 16], [26, 6, 34, 4, "stackPresentation"], [26, 23, 34, 21], [26, 26, 34, 21, "_ref"], [26, 30, 34, 21], [26, 31, 34, 4, "stackPresentation"], [26, 48, 34, 21], [27, 6, 35, 4, "sheetAllowedDetents"], [27, 25, 35, 23], [27, 28, 35, 23, "_ref"], [27, 32, 35, 23], [27, 33, 35, 4, "sheetAllowedDetents"], [27, 52, 35, 23], [28, 6, 36, 4, "contentStyle"], [28, 18, 36, 16], [28, 21, 36, 16, "_ref"], [28, 25, 36, 16], [28, 26, 36, 4, "contentStyle"], [28, 38, 36, 16], [29, 6, 37, 4, "style"], [29, 11, 37, 9], [29, 14, 37, 9, "_ref"], [29, 18, 37, 9], [29, 19, 37, 4, "style"], [29, 24, 37, 9], [30, 6, 38, 4, "screenId"], [30, 14, 38, 12], [30, 17, 38, 12, "_ref"], [30, 21, 38, 12], [30, 22, 38, 4, "screenId"], [30, 30, 38, 12], [31, 6, 40, 4, "unstable_sheetFooter"], [31, 26, 40, 24], [31, 29, 40, 24, "_ref"], [31, 33, 40, 24], [31, 34, 40, 4, "unstable_sheetFooter"], [31, 54, 40, 24], [32, 6, 41, 7, "rest"], [32, 10, 41, 11], [32, 17, 41, 11, "_objectWithoutProperties2"], [32, 42, 41, 11], [32, 43, 41, 11, "default"], [32, 50, 41, 11], [32, 52, 41, 11, "_ref"], [32, 56, 41, 11], [32, 58, 41, 11, "_excluded"], [32, 67, 41, 11], [33, 4, 45, 2], [33, 8, 45, 8, "currentScreenRef"], [33, 24, 45, 24], [33, 27, 45, 27, "React"], [33, 32, 45, 32], [33, 33, 45, 33, "useRef"], [33, 39, 45, 39], [33, 40, 45, 53], [33, 44, 45, 57], [33, 45, 45, 58], [34, 4, 46, 2], [34, 8, 46, 8, "screenRefs"], [34, 18, 46, 18], [34, 21, 46, 21, "React"], [34, 26, 46, 26], [34, 27, 46, 27, "useContext"], [34, 37, 46, 37], [34, 38, 46, 38, "RNSScreensRefContext"], [34, 68, 46, 58], [34, 69, 46, 59], [35, 4, 48, 2, "React"], [35, 9, 48, 7], [35, 10, 48, 8, "useImperativeHandle"], [35, 29, 48, 27], [35, 30, 48, 28, "ref"], [35, 33, 48, 31], [35, 35, 48, 33], [35, 41, 48, 39, "currentScreenRef"], [35, 57, 48, 55], [35, 58, 48, 56, "current"], [35, 65, 48, 64], [35, 66, 48, 65], [36, 4, 50, 2], [36, 8, 50, 8, "isHeaderInModal"], [36, 23, 50, 23], [36, 26, 51, 4, "Platform"], [36, 47, 51, 12], [36, 48, 51, 13, "OS"], [36, 50, 51, 15], [36, 55, 51, 20], [36, 64, 51, 29], [36, 67, 52, 8], [36, 72, 52, 13], [36, 75, 53, 8, "stackPresentation"], [36, 92, 53, 25], [36, 97, 53, 30], [36, 103, 53, 36], [36, 107, 53, 40, "headerConfig"], [36, 119, 53, 52], [36, 121, 53, 54, "hidden"], [36, 127, 53, 60], [36, 132, 53, 65], [36, 137, 53, 70], [37, 4, 55, 2], [37, 8, 55, 8, "headerHiddenPreviousRef"], [37, 31, 55, 31], [37, 34, 55, 34, "React"], [37, 39, 55, 39], [37, 40, 55, 40, "useRef"], [37, 46, 55, 46], [37, 47, 55, 47, "headerConfig"], [37, 59, 55, 59], [37, 61, 55, 61, "hidden"], [37, 67, 55, 67], [37, 68, 55, 68], [38, 4, 57, 2, "React"], [38, 9, 57, 7], [38, 10, 57, 8, "useEffect"], [38, 19, 57, 17], [38, 20, 57, 18], [38, 26, 57, 24], [39, 6, 58, 4], [39, 10, 58, 4, "warnOnce"], [39, 27, 58, 12], [39, 29, 59, 6, "Platform"], [39, 50, 59, 14], [39, 51, 59, 15, "OS"], [39, 53, 59, 17], [39, 58, 59, 22], [39, 67, 59, 31], [39, 71, 60, 8, "stackPresentation"], [39, 88, 60, 25], [39, 93, 60, 30], [39, 99, 60, 36], [39, 103, 61, 8, "headerHiddenPreviousRef"], [39, 126, 61, 31], [39, 127, 61, 32, "current"], [39, 134, 61, 39], [39, 139, 61, 44, "headerConfig"], [39, 151, 61, 56], [39, 153, 61, 58, "hidden"], [39, 159, 61, 64], [39, 161, 62, 6], [39, 278, 63, 4], [39, 279, 63, 5], [40, 6, 65, 4, "headerHiddenPreviousRef"], [40, 29, 65, 27], [40, 30, 65, 28, "current"], [40, 37, 65, 35], [40, 40, 65, 38, "headerConfig"], [40, 52, 65, 50], [40, 54, 65, 52, "hidden"], [40, 60, 65, 58], [41, 4, 66, 2], [41, 5, 66, 3], [41, 7, 66, 5], [41, 8, 66, 6, "headerConfig"], [41, 20, 66, 18], [41, 22, 66, 20, "hidden"], [41, 28, 66, 26], [41, 30, 66, 28, "stackPresentation"], [41, 47, 66, 45], [41, 48, 66, 46], [41, 49, 66, 47], [42, 4, 68, 2], [42, 8, 68, 8, "content"], [42, 15, 68, 15], [42, 31, 69, 4], [42, 35, 69, 4, "_jsxDevRuntime"], [42, 49, 69, 4], [42, 50, 69, 4, "jsxDEV"], [42, 56, 69, 4], [42, 58, 69, 4, "_jsxDevRuntime"], [42, 72, 69, 4], [42, 73, 69, 4, "Fragment"], [42, 81, 69, 4], [43, 6, 69, 4, "children"], [43, 14, 69, 4], [43, 30, 70, 6], [43, 34, 70, 6, "_jsxDevRuntime"], [43, 48, 70, 6], [43, 49, 70, 6, "jsxDEV"], [43, 55, 70, 6], [43, 57, 70, 7, "_DebugContainer"], [43, 72, 70, 7], [43, 73, 70, 7, "default"], [43, 80, 70, 21], [44, 8, 71, 8, "style"], [44, 13, 71, 13], [44, 15, 71, 15], [44, 16, 72, 10, "stackPresentation"], [44, 33, 72, 27], [44, 38, 72, 32], [44, 49, 72, 43], [44, 52, 73, 14, "Platform"], [44, 73, 73, 22], [44, 74, 73, 23, "OS"], [44, 76, 73, 25], [44, 81, 73, 30], [44, 86, 73, 35], [44, 89, 74, 16, "styles"], [44, 95, 74, 22], [44, 96, 74, 23, "absolute"], [44, 104, 74, 31], [44, 107, 75, 16, "sheetAllowedDetents"], [44, 126, 75, 35], [44, 131, 75, 40], [44, 146, 75, 55], [44, 149, 76, 16], [44, 153, 76, 20], [44, 156, 77, 16, "styles"], [44, 162, 77, 22], [44, 163, 77, 23, "container"], [44, 172, 77, 32], [44, 175, 78, 14, "styles"], [44, 181, 78, 20], [44, 182, 78, 21, "container"], [44, 191, 78, 30], [44, 193, 79, 10, "contentStyle"], [44, 205, 79, 22], [44, 206, 80, 10], [45, 8, 81, 8, "stackPresentation"], [45, 25, 81, 25], [45, 27, 81, 27, "stackPresentation"], [45, 44, 81, 44], [45, 48, 81, 48], [45, 54, 81, 55], [46, 8, 81, 55, "children"], [46, 16, 81, 55], [46, 18, 82, 9, "children"], [47, 6, 82, 17], [48, 8, 82, 17, "fileName"], [48, 16, 82, 17], [48, 18, 82, 17, "_jsxFileName"], [48, 30, 82, 17], [49, 8, 82, 17, "lineNumber"], [49, 18, 82, 17], [50, 8, 82, 17, "columnNumber"], [50, 20, 82, 17], [51, 6, 82, 17], [51, 13, 83, 22], [51, 14, 83, 23], [51, 29, 95, 6], [51, 33, 95, 6, "_jsxDevRuntime"], [51, 47, 95, 6], [51, 48, 95, 6, "jsxDEV"], [51, 54, 95, 6], [51, 56, 95, 7, "_ScreenStackHeaderConfig"], [51, 80, 95, 7], [51, 81, 95, 7, "ScreenStackHeaderConfig"], [51, 104, 95, 30], [52, 8, 95, 30], [52, 11, 95, 35, "headerConfig"], [53, 6, 95, 47], [54, 8, 95, 47, "fileName"], [54, 16, 95, 47], [54, 18, 95, 47, "_jsxFileName"], [54, 30, 95, 47], [55, 8, 95, 47, "lineNumber"], [55, 18, 95, 47], [56, 8, 95, 47, "columnNumber"], [56, 20, 95, 47], [57, 6, 95, 47], [57, 13, 95, 50], [57, 14, 95, 51], [57, 16, 97, 7, "stackPresentation"], [57, 33, 97, 24], [57, 38, 97, 29], [57, 49, 97, 40], [57, 53, 97, 44, "unstable_sheetFooter"], [57, 73, 97, 64], [57, 90, 98, 8], [57, 94, 98, 8, "_jsxDevRuntime"], [57, 108, 98, 8], [57, 109, 98, 8, "jsxDEV"], [57, 115, 98, 8], [57, 117, 98, 9, "_ScreenFooter"], [57, 130, 98, 9], [57, 131, 98, 9, "FooterComponent"], [57, 146, 98, 24], [58, 8, 98, 24, "children"], [58, 16, 98, 24], [58, 18, 98, 26, "unstable_sheetFooter"], [58, 38, 98, 46], [58, 39, 98, 47], [59, 6, 98, 48], [60, 8, 98, 48, "fileName"], [60, 16, 98, 48], [60, 18, 98, 48, "_jsxFileName"], [60, 30, 98, 48], [61, 8, 98, 48, "lineNumber"], [61, 18, 98, 48], [62, 8, 98, 48, "columnNumber"], [62, 20, 98, 48], [63, 6, 98, 48], [63, 13, 98, 66], [63, 14, 99, 7], [64, 4, 99, 7], [64, 19, 100, 6], [64, 20, 101, 3], [66, 4, 103, 2], [67, 4, 104, 2], [68, 4, 105, 2], [69, 4, 106, 2], [69, 8, 106, 6, "internalScreenStyle"], [69, 27, 106, 25], [70, 4, 108, 2], [70, 8, 108, 6, "stackPresentation"], [70, 25, 108, 23], [70, 30, 108, 28], [70, 41, 108, 39], [70, 45, 108, 43, "contentStyle"], [70, 57, 108, 55], [70, 59, 108, 57], [71, 6, 109, 4], [71, 10, 109, 10, "flattenContentStyles"], [71, 30, 109, 30], [71, 33, 109, 33, "StyleSheet"], [71, 56, 109, 43], [71, 57, 109, 44, "flatten"], [71, 64, 109, 51], [71, 65, 109, 52, "contentStyle"], [71, 77, 109, 64], [71, 78, 109, 65], [72, 6, 110, 4, "internalScreenStyle"], [72, 25, 110, 23], [72, 28, 110, 26], [73, 8, 111, 6, "backgroundColor"], [73, 23, 111, 21], [73, 25, 111, 23, "flattenContentStyles"], [73, 45, 111, 43], [73, 47, 111, 45, "backgroundColor"], [74, 6, 112, 4], [74, 7, 112, 5], [75, 4, 113, 2], [76, 4, 115, 2], [76, 24, 116, 4], [76, 28, 116, 4, "_jsxDevRuntime"], [76, 42, 116, 4], [76, 43, 116, 4, "jsxDEV"], [76, 49, 116, 4], [76, 51, 116, 5, "_Screen"], [76, 58, 116, 5], [76, 59, 116, 5, "default"], [76, 66, 116, 11], [77, 6, 117, 6, "ref"], [77, 9, 117, 9], [77, 11, 117, 11, "node"], [77, 15, 117, 15], [77, 19, 117, 19], [78, 8, 118, 8, "currentScreenRef"], [78, 24, 118, 24], [78, 25, 118, 25, "current"], [78, 32, 118, 32], [78, 35, 118, 35, "node"], [78, 39, 118, 39], [79, 8, 120, 8], [79, 12, 120, 12, "screenRefs"], [79, 22, 120, 22], [79, 27, 120, 27], [79, 31, 120, 31], [79, 33, 120, 33], [80, 10, 121, 10, "console"], [80, 17, 121, 17], [80, 18, 121, 18, "warn"], [80, 22, 121, 22], [80, 23, 122, 12], [80, 121, 123, 10], [80, 122, 123, 11], [81, 10, 124, 10], [82, 8, 125, 8], [83, 8, 127, 8], [83, 12, 127, 14, "currentRefs"], [83, 23, 127, 25], [83, 26, 127, 28, "screenRefs"], [83, 36, 127, 38], [83, 37, 127, 39, "current"], [83, 44, 127, 46], [84, 8, 129, 8], [84, 12, 129, 12, "node"], [84, 16, 129, 16], [84, 21, 129, 21], [84, 25, 129, 25], [84, 27, 129, 27], [85, 10, 130, 10], [86, 10, 131, 10], [86, 17, 131, 17, "currentRefs"], [86, 28, 131, 28], [86, 29, 131, 29, "screenId"], [86, 37, 131, 37], [86, 38, 131, 38], [87, 8, 132, 8], [87, 9, 132, 9], [87, 15, 132, 15], [88, 10, 133, 10, "currentRefs"], [88, 21, 133, 21], [88, 22, 133, 22, "screenId"], [88, 30, 133, 30], [88, 31, 133, 31], [88, 34, 133, 34], [89, 12, 133, 36, "current"], [89, 19, 133, 43], [89, 21, 133, 45, "node"], [90, 10, 133, 50], [90, 11, 133, 51], [91, 8, 134, 8], [92, 6, 135, 6], [92, 7, 135, 8], [93, 6, 136, 6, "enabled"], [93, 13, 136, 13], [94, 6, 137, 6, "isNativeStack"], [94, 19, 137, 19], [95, 6, 138, 6, "activityState"], [95, 19, 138, 19], [95, 21, 138, 21, "activityState"], [95, 34, 138, 35], [96, 6, 139, 6, "shouldFreeze"], [96, 18, 139, 18], [96, 20, 139, 20, "shouldFreeze"], [96, 32, 139, 33], [97, 6, 140, 6, "stackPresentation"], [97, 23, 140, 23], [97, 25, 140, 25, "stackPresentation"], [97, 42, 140, 43], [98, 6, 141, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [98, 20, 141, 20], [98, 22, 141, 22, "headerConfig"], [98, 34, 141, 34], [98, 36, 141, 36, "largeTitle"], [98, 46, 141, 46], [98, 50, 141, 50], [98, 55, 141, 56], [99, 6, 142, 6, "sheetAllowedDetents"], [99, 25, 142, 25], [99, 27, 142, 27, "sheetAllowedDetents"], [99, 46, 142, 47], [100, 6, 143, 6, "style"], [100, 11, 143, 11], [100, 13, 143, 13], [100, 14, 143, 14, "style"], [100, 19, 143, 19], [100, 21, 143, 21, "internalScreenStyle"], [100, 40, 143, 40], [100, 41, 143, 42], [101, 6, 143, 42], [101, 9, 144, 10, "rest"], [101, 13, 144, 14], [102, 6, 144, 14, "children"], [102, 14, 144, 14], [102, 16, 145, 7, "isHeaderInModal"], [102, 31, 145, 22], [102, 47, 146, 8], [102, 51, 146, 8, "_jsxDevRuntime"], [102, 65, 146, 8], [102, 66, 146, 8, "jsxDEV"], [102, 72, 146, 8], [102, 74, 146, 9, "_ScreenStack"], [102, 86, 146, 9], [102, 87, 146, 9, "default"], [102, 94, 146, 20], [103, 8, 146, 21, "style"], [103, 13, 146, 26], [103, 15, 146, 28, "styles"], [103, 21, 146, 34], [103, 22, 146, 35, "container"], [103, 31, 146, 45], [104, 8, 146, 45, "children"], [104, 16, 146, 45], [104, 31, 147, 10], [104, 35, 147, 10, "_jsxDevRuntime"], [104, 49, 147, 10], [104, 50, 147, 10, "jsxDEV"], [104, 56, 147, 10], [104, 58, 147, 11, "_Screen"], [104, 65, 147, 11], [104, 66, 147, 11, "default"], [104, 73, 147, 17], [105, 10, 148, 12, "enabled"], [105, 17, 148, 19], [106, 10, 149, 12, "isNativeStack"], [106, 23, 149, 25], [107, 10, 150, 12, "activityState"], [107, 23, 150, 25], [107, 25, 150, 27, "activityState"], [107, 38, 150, 41], [108, 10, 151, 12, "shouldFreeze"], [108, 22, 151, 24], [108, 24, 151, 26, "shouldFreeze"], [108, 36, 151, 39], [109, 10, 152, 12, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [109, 24, 152, 26], [109, 26, 152, 28, "headerConfig"], [109, 38, 152, 40], [109, 40, 152, 42, "largeTitle"], [109, 50, 152, 52], [109, 54, 152, 56], [109, 59, 152, 62], [110, 10, 153, 12, "style"], [110, 15, 153, 17], [110, 17, 153, 19, "StyleSheet"], [110, 40, 153, 29], [110, 41, 153, 30, "absoluteFill"], [110, 53, 153, 43], [111, 10, 153, 43, "children"], [111, 18, 153, 43], [111, 20, 154, 13, "content"], [112, 8, 154, 20], [113, 10, 154, 20, "fileName"], [113, 18, 154, 20], [113, 20, 154, 20, "_jsxFileName"], [113, 32, 154, 20], [114, 10, 154, 20, "lineNumber"], [114, 20, 154, 20], [115, 10, 154, 20, "columnNumber"], [115, 22, 154, 20], [116, 8, 154, 20], [116, 15, 155, 18], [117, 6, 155, 19], [118, 8, 155, 19, "fileName"], [118, 16, 155, 19], [118, 18, 155, 19, "_jsxFileName"], [118, 30, 155, 19], [119, 8, 155, 19, "lineNumber"], [119, 18, 155, 19], [120, 8, 155, 19, "columnNumber"], [120, 20, 155, 19], [121, 6, 155, 19], [121, 13, 156, 21], [121, 14, 156, 22], [121, 17, 158, 8, "content"], [122, 4, 159, 7], [123, 6, 159, 7, "fileName"], [123, 14, 159, 7], [123, 16, 159, 7, "_jsxFileName"], [123, 28, 159, 7], [124, 6, 159, 7, "lineNumber"], [124, 16, 159, 7], [125, 6, 159, 7, "columnNumber"], [125, 18, 159, 7], [126, 4, 159, 7], [126, 11, 160, 12], [126, 12, 160, 13], [127, 2, 162, 0], [128, 2, 162, 1], [128, 6, 162, 1, "_default"], [128, 14, 162, 1], [128, 17, 162, 1, "exports"], [128, 24, 162, 1], [128, 25, 162, 1, "default"], [128, 32, 162, 1], [128, 48, 164, 15, "React"], [128, 53, 164, 20], [128, 54, 164, 21, "forwardRef"], [128, 64, 164, 31], [128, 65, 164, 32, "ScreenStackItem"], [128, 80, 164, 47], [128, 81, 164, 48], [129, 2, 166, 0], [129, 6, 166, 6, "styles"], [129, 12, 166, 12], [129, 15, 166, 15, "StyleSheet"], [129, 38, 166, 25], [129, 39, 166, 26, "create"], [129, 45, 166, 32], [129, 46, 166, 33], [130, 4, 167, 2, "container"], [130, 13, 167, 11], [130, 15, 167, 13], [131, 6, 168, 4, "flex"], [131, 10, 168, 8], [131, 12, 168, 10], [132, 4, 169, 2], [132, 5, 169, 3], [133, 4, 170, 2, "absolute"], [133, 12, 170, 10], [133, 14, 170, 12], [134, 6, 171, 4, "position"], [134, 14, 171, 12], [134, 16, 171, 14], [134, 26, 171, 24], [135, 6, 172, 4, "top"], [135, 9, 172, 7], [135, 11, 172, 9], [135, 12, 172, 10], [136, 6, 173, 4, "start"], [136, 11, 173, 9], [136, 13, 173, 11], [136, 14, 173, 12], [137, 6, 174, 4, "end"], [137, 9, 174, 7], [137, 11, 174, 9], [138, 4, 175, 2], [139, 2, 176, 0], [139, 3, 176, 1], [139, 4, 176, 2], [140, 0, 176, 3], [140, 3]], "functionMap": {"names": ["<global>", "ScreenStackItem", "React.useImperativeHandle$argument_1", "React.useEffect$argument_0", "Screen.props.ref"], "mappings": "AAA;AC2B;iCCoB,+BD;kBES;GFS;WGmD;OHkB;CD2B"}}, "type": "js/module"}]}