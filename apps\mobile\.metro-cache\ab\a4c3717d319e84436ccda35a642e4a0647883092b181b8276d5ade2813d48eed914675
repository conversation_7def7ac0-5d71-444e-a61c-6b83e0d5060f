{"dependencies": [{"name": "./ReactNativeDocumentElementInstanceHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0}, "end": {"line": 28, "column": 52}}], "key": "cR1MnFfwj6lnrLlF0BsotwYXLZQ=", "exportNames": ["*"]}}, {"name": "./ReactNativeDocumentInstanceHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0}, "end": {"line": 33, "column": 45}}], "key": "ViwXxrJ429i51O+FTMelBciwf1I=", "exportNames": ["*"]}}, {"name": "../../../../../../Libraries/ReactNative/RendererProxy", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 45, "column": 20}, "end": {"line": 45, "column": 84}}], "key": "7wbyXYKHXYDgpg+z13ds3OwbSvo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getInstanceHandle = getInstanceHandle;\n  exports.getNativeElementReference = getNativeElementReference;\n  exports.getNativeNodeReference = getNativeNodeReference;\n  exports.getNativeTextReference = getNativeTextReference;\n  exports.getOwnerDocument = getOwnerDocument;\n  exports.getPublicInstanceFromInstanceHandle = getPublicInstanceFromInstanceHandle;\n  exports.setInstanceHandle = setInstanceHandle;\n  exports.setOwnerDocument = setOwnerDocument;\n  var _ReactNativeDocumentElementInstanceHandle = require(_dependencyMap[0], \"./ReactNativeDocumentElementInstanceHandle\");\n  var _ReactNativeDocumentInstanceHandle = require(_dependencyMap[1], \"./ReactNativeDocumentInstanceHandle\");\n  var RendererProxy;\n  function getRendererProxy() {\n    if (RendererProxy == null) {\n      RendererProxy = require(_dependencyMap[2], \"../../../../../../Libraries/ReactNative/RendererProxy\");\n    }\n    return RendererProxy;\n  }\n  var INSTANCE_HANDLE_KEY = Symbol('internalInstanceHandle');\n  var OWNER_DOCUMENT_KEY = Symbol('ownerDocument');\n  function getInstanceHandle(node) {\n    return node[INSTANCE_HANDLE_KEY];\n  }\n  function setInstanceHandle(node, instanceHandle) {\n    node[INSTANCE_HANDLE_KEY] = instanceHandle;\n  }\n  function getOwnerDocument(node) {\n    return node[OWNER_DOCUMENT_KEY] ?? null;\n  }\n  function setOwnerDocument(node, ownerDocument) {\n    node[OWNER_DOCUMENT_KEY] = ownerDocument;\n  }\n  function getPublicInstanceFromInstanceHandle(instanceHandle) {\n    if ((0, _ReactNativeDocumentInstanceHandle.isReactNativeDocumentInstanceHandle)(instanceHandle)) {\n      return (0, _ReactNativeDocumentInstanceHandle.getPublicInstanceFromReactNativeDocumentInstanceHandle)(instanceHandle);\n    }\n    if ((0, _ReactNativeDocumentElementInstanceHandle.isReactNativeDocumentElementInstanceHandle)(instanceHandle)) {\n      return (0, _ReactNativeDocumentElementInstanceHandle.getPublicInstanceFromReactNativeDocumentElementInstanceHandle)(instanceHandle);\n    }\n    var mixedPublicInstance = getRendererProxy().getPublicInstanceFromInternalInstanceHandle(instanceHandle);\n    return mixedPublicInstance;\n  }\n  function getNativeNodeReference(node) {\n    var instanceHandle = getInstanceHandle(node);\n    if ((0, _ReactNativeDocumentInstanceHandle.isReactNativeDocumentInstanceHandle)(instanceHandle)) {\n      return (0, _ReactNativeDocumentInstanceHandle.getNativeNodeReferenceFromReactNativeDocumentInstanceHandle)(instanceHandle);\n    }\n    if ((0, _ReactNativeDocumentElementInstanceHandle.isReactNativeDocumentElementInstanceHandle)(instanceHandle)) {\n      return (0, _ReactNativeDocumentElementInstanceHandle.getNativeElementReferenceFromReactNativeDocumentElementInstanceHandle)(instanceHandle);\n    }\n    return getRendererProxy().getNodeFromInternalInstanceHandle(instanceHandle);\n  }\n  function getNativeElementReference(node) {\n    var instanceHandle = getInstanceHandle(node);\n    return getRendererProxy().getNodeFromInternalInstanceHandle(instanceHandle);\n  }\n  function getNativeTextReference(node) {\n    var instanceHandle = getInstanceHandle(node);\n    return getRendererProxy().getNodeFromInternalInstanceHandle(instanceHandle);\n  }\n});", "lineCount": 64, "map": [[13, 2, 24, 0], [13, 6, 24, 0, "_ReactNativeDocumentElementInstanceHandle"], [13, 47, 24, 0], [13, 50, 24, 0, "require"], [13, 57, 24, 0], [13, 58, 24, 0, "_dependencyMap"], [13, 72, 24, 0], [14, 2, 29, 0], [14, 6, 29, 0, "_ReactNativeDocumentInstanceHandle"], [14, 40, 29, 0], [14, 43, 29, 0, "require"], [14, 50, 29, 0], [14, 51, 29, 0, "_dependencyMap"], [14, 65, 29, 0], [15, 2, 40, 0], [15, 6, 40, 4, "RendererProxy"], [15, 19, 40, 17], [16, 2, 41, 0], [16, 11, 41, 9, "getRendererProxy"], [16, 27, 41, 25, "getRendererProxy"], [16, 28, 41, 25], [16, 30, 41, 28], [17, 4, 42, 2], [17, 8, 42, 6, "RendererProxy"], [17, 21, 42, 19], [17, 25, 42, 23], [17, 29, 42, 27], [17, 31, 42, 29], [18, 6, 45, 4, "RendererProxy"], [18, 19, 45, 17], [18, 22, 45, 20, "require"], [18, 29, 45, 27], [18, 30, 45, 27, "_dependencyMap"], [18, 44, 45, 27], [18, 104, 45, 83], [18, 105, 45, 84], [19, 4, 46, 2], [20, 4, 47, 2], [20, 11, 47, 9, "RendererProxy"], [20, 24, 47, 22], [21, 2, 48, 0], [22, 2, 50, 0], [22, 6, 50, 6, "INSTANCE_HANDLE_KEY"], [22, 25, 50, 25], [22, 28, 50, 28, "Symbol"], [22, 34, 50, 34], [22, 35, 50, 35], [22, 59, 50, 59], [22, 60, 50, 60], [23, 2, 51, 0], [23, 6, 51, 6, "OWNER_DOCUMENT_KEY"], [23, 24, 51, 24], [23, 27, 51, 27, "Symbol"], [23, 33, 51, 33], [23, 34, 51, 34], [23, 49, 51, 49], [23, 50, 51, 50], [24, 2, 53, 7], [24, 11, 53, 16, "getInstanceHandle"], [24, 28, 53, 33, "getInstanceHandle"], [24, 29, 53, 34, "node"], [24, 33, 53, 52], [24, 35, 53, 70], [25, 4, 55, 2], [25, 11, 55, 9, "node"], [25, 15, 55, 13], [25, 16, 55, 14, "INSTANCE_HANDLE_KEY"], [25, 35, 55, 33], [25, 36, 55, 34], [26, 2, 56, 0], [27, 2, 58, 7], [27, 11, 58, 16, "setInstanceHandle"], [27, 28, 58, 33, "setInstanceHandle"], [27, 29, 59, 2, "node"], [27, 33, 59, 20], [27, 35, 60, 2, "instanceHandle"], [27, 49, 60, 32], [27, 51, 61, 8], [28, 4, 63, 2, "node"], [28, 8, 63, 6], [28, 9, 63, 7, "INSTANCE_HANDLE_KEY"], [28, 28, 63, 26], [28, 29, 63, 27], [28, 32, 63, 30, "instanceHandle"], [28, 46, 63, 44], [29, 2, 64, 0], [30, 2, 66, 7], [30, 11, 66, 16, "getOwnerDocument"], [30, 27, 66, 32, "getOwnerDocument"], [30, 28, 67, 2, "node"], [30, 32, 67, 20], [30, 34, 68, 30], [31, 4, 70, 2], [31, 11, 70, 9, "node"], [31, 15, 70, 13], [31, 16, 70, 14, "OWNER_DOCUMENT_KEY"], [31, 34, 70, 32], [31, 35, 70, 33], [31, 39, 70, 37], [31, 43, 70, 41], [32, 2, 71, 0], [33, 2, 73, 7], [33, 11, 73, 16, "setOwnerDocument"], [33, 27, 73, 32, "setOwnerDocument"], [33, 28, 74, 2, "node"], [33, 32, 74, 20], [33, 34, 75, 2, "ownerDocument"], [33, 47, 75, 43], [33, 49, 76, 8], [34, 4, 78, 2, "node"], [34, 8, 78, 6], [34, 9, 78, 7, "OWNER_DOCUMENT_KEY"], [34, 27, 78, 25], [34, 28, 78, 26], [34, 31, 78, 29, "ownerDocument"], [34, 44, 78, 42], [35, 2, 79, 0], [36, 2, 81, 7], [36, 11, 81, 16, "getPublicInstanceFromInstanceHandle"], [36, 46, 81, 51, "getPublicInstanceFromInstanceHandle"], [36, 47, 82, 2, "instanceHandle"], [36, 61, 82, 32], [36, 63, 83, 17], [37, 4, 84, 2], [37, 8, 84, 6], [37, 12, 84, 6, "isReactNativeDocumentInstanceHandle"], [37, 82, 84, 41], [37, 84, 84, 42, "instanceHandle"], [37, 98, 84, 56], [37, 99, 84, 57], [37, 101, 84, 59], [38, 6, 85, 4], [38, 13, 85, 11], [38, 17, 85, 11, "getPublicInstanceFromReactNativeDocumentInstanceHandle"], [38, 106, 85, 65], [38, 108, 86, 6, "instanceHandle"], [38, 122, 87, 4], [38, 123, 87, 5], [39, 4, 88, 2], [40, 4, 90, 2], [40, 8, 90, 6], [40, 12, 90, 6, "isReactNativeDocumentElementInstanceHandle"], [40, 96, 90, 48], [40, 98, 90, 49, "instanceHandle"], [40, 112, 90, 63], [40, 113, 90, 64], [40, 115, 90, 66], [41, 6, 91, 4], [41, 13, 91, 11], [41, 17, 91, 11, "getPublicInstanceFromReactNativeDocumentElementInstanceHandle"], [41, 120, 91, 72], [41, 122, 92, 6, "instanceHandle"], [41, 136, 93, 4], [41, 137, 93, 5], [42, 4, 94, 2], [43, 4, 96, 2], [43, 8, 96, 8, "mixedPublicInstance"], [43, 27, 96, 27], [43, 30, 97, 4, "getRendererProxy"], [43, 46, 97, 20], [43, 47, 97, 21], [43, 48, 97, 22], [43, 49, 97, 23, "getPublicInstanceFromInternalInstanceHandle"], [43, 92, 97, 66], [43, 93, 98, 6, "instanceHandle"], [43, 107, 99, 4], [43, 108, 99, 5], [44, 4, 102, 2], [44, 11, 102, 9, "mixedPublicInstance"], [44, 30, 102, 28], [45, 2, 103, 0], [46, 2, 105, 7], [46, 11, 105, 16, "getNativeNodeReference"], [46, 33, 105, 38, "getNativeNodeReference"], [46, 34, 106, 2, "node"], [46, 38, 106, 20], [46, 40, 107, 24], [47, 4, 108, 2], [47, 8, 108, 8, "instanceHandle"], [47, 22, 108, 22], [47, 25, 108, 25, "getInstanceHandle"], [47, 42, 108, 42], [47, 43, 108, 43, "node"], [47, 47, 108, 47], [47, 48, 108, 48], [48, 4, 110, 2], [48, 8, 110, 6], [48, 12, 110, 6, "isReactNativeDocumentInstanceHandle"], [48, 82, 110, 41], [48, 84, 110, 42, "instanceHandle"], [48, 98, 110, 56], [48, 99, 110, 57], [48, 101, 110, 59], [49, 6, 111, 4], [49, 13, 111, 11], [49, 17, 111, 11, "getNativeNodeReferenceFromReactNativeDocumentInstanceHandle"], [49, 111, 111, 70], [49, 113, 112, 6, "instanceHandle"], [49, 127, 113, 4], [49, 128, 113, 5], [50, 4, 114, 2], [51, 4, 116, 2], [51, 8, 116, 6], [51, 12, 116, 6, "isReactNativeDocumentElementInstanceHandle"], [51, 96, 116, 48], [51, 98, 116, 49, "instanceHandle"], [51, 112, 116, 63], [51, 113, 116, 64], [51, 115, 116, 66], [52, 6, 117, 4], [52, 13, 117, 11], [52, 17, 117, 11, "getNativeElementReferenceFromReactNativeDocumentElementInstanceHandle"], [52, 128, 117, 80], [52, 130, 118, 6, "instanceHandle"], [52, 144, 119, 4], [52, 145, 119, 5], [53, 4, 120, 2], [54, 4, 123, 2], [54, 11, 123, 9, "getRendererProxy"], [54, 27, 123, 25], [54, 28, 123, 26], [54, 29, 123, 27], [54, 30, 123, 28, "getNodeFromInternalInstanceHandle"], [54, 63, 123, 61], [54, 64, 123, 62, "instanceHandle"], [54, 78, 123, 76], [54, 79, 123, 77], [55, 2, 124, 0], [56, 2, 126, 7], [56, 11, 126, 16, "getNativeElementReference"], [56, 36, 126, 41, "getNativeElementReference"], [56, 37, 127, 2, "node"], [56, 41, 127, 23], [56, 43, 128, 27], [57, 4, 130, 2], [57, 8, 130, 8, "instanceHandle"], [57, 22, 130, 22], [57, 25, 130, 25, "getInstanceHandle"], [57, 42, 130, 42], [57, 43, 130, 43, "node"], [57, 47, 130, 47], [57, 48, 130, 74], [58, 4, 133, 2], [58, 11, 133, 9, "getRendererProxy"], [58, 27, 133, 25], [58, 28, 133, 26], [58, 29, 133, 27], [58, 30, 133, 28, "getNodeFromInternalInstanceHandle"], [58, 63, 133, 61], [58, 64, 133, 62, "instanceHandle"], [58, 78, 133, 76], [58, 79, 133, 77], [59, 2, 134, 0], [60, 2, 136, 7], [60, 11, 136, 16, "getNativeTextReference"], [60, 33, 136, 38, "getNativeTextReference"], [60, 34, 137, 2, "node"], [60, 38, 137, 29], [60, 40, 138, 24], [61, 4, 140, 2], [61, 8, 140, 8, "instanceHandle"], [61, 22, 140, 22], [61, 25, 140, 25, "getInstanceHandle"], [61, 42, 140, 42], [61, 43, 140, 43, "node"], [61, 47, 140, 47], [61, 48, 140, 74], [62, 4, 143, 2], [62, 11, 143, 9, "getRendererProxy"], [62, 27, 143, 25], [62, 28, 143, 26], [62, 29, 143, 27], [62, 30, 143, 28, "getNodeFromInternalInstanceHandle"], [62, 63, 143, 61], [62, 64, 143, 62, "instanceHandle"], [62, 78, 143, 76], [62, 79, 143, 77], [63, 2, 144, 0], [64, 0, 144, 1], [64, 3]], "functionMap": {"names": ["<global>", "getRendererProxy", "getInstanceHandle", "setInstanceHandle", "getOwnerDocument", "setOwnerDocument", "getPublicInstanceFromInstanceHandle", "getNativeNodeReference", "getNativeElementReference", "getNativeTextReference"], "mappings": "AAA;ACwC;CDO;OEK;CFG;OGE;CHM;OIE;CJK;OKE;CLM;OME;CNsB;OOE;CPmB;OQE;CRQ;OSE"}}, "type": "js/module"}]}