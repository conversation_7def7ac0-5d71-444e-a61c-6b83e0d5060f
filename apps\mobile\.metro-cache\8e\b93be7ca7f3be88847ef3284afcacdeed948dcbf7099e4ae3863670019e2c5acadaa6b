{"dependencies": [{"name": "./core.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 14, "index": 98}, "end": {"line": 5, "column": 34, "index": 118}}], "key": "ud7OA+9V36/ALBXnU4BYy09opnw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  //This file contains the ES6 extensions to the core Promises/A+ API\n  var Promise = require(_dependencyMap[0], \"./core.js\");\n  module.exports = Promise;\n\n  /* Static Functions */\n\n  var TRUE = valuePromise(true);\n  var FALSE = valuePromise(false);\n  var NULL = valuePromise(null);\n  var UNDEFINED = valuePromise(undefined);\n  var ZERO = valuePromise(0);\n  var EMPTYSTRING = valuePromise('');\n  function valuePromise(value) {\n    var p = new Promise(Promise._D);\n    p._y = 1;\n    p._z = value;\n    return p;\n  }\n  Promise.resolve = function (value) {\n    if (value instanceof Promise) return value;\n    if (value === null) return NULL;\n    if (value === undefined) return UNDEFINED;\n    if (value === true) return TRUE;\n    if (value === false) return FALSE;\n    if (value === 0) return ZERO;\n    if (value === '') return EMPTYSTRING;\n    if (typeof value === 'object' || typeof value === 'function') {\n      try {\n        var then = value.then;\n        if (typeof then === 'function') {\n          return new Promise(then.bind(value));\n        }\n      } catch (ex) {\n        return new Promise(function (resolve, reject) {\n          reject(ex);\n        });\n      }\n    }\n    return valuePromise(value);\n  };\n  var iterableToArray = function (iterable) {\n    if (typeof Array.from === 'function') {\n      // ES2015+, iterables exist\n      iterableToArray = Array.from;\n      return Array.from(iterable);\n    }\n\n    // ES5, only arrays and array-likes exist\n    iterableToArray = function (x) {\n      return Array.prototype.slice.call(x);\n    };\n    return Array.prototype.slice.call(iterable);\n  };\n  Promise.all = function (arr) {\n    var args = iterableToArray(arr);\n    return new Promise(function (resolve, reject) {\n      if (args.length === 0) return resolve([]);\n      var remaining = args.length;\n      function res(i, val) {\n        if (val && (typeof val === 'object' || typeof val === 'function')) {\n          if (val instanceof Promise && val.then === Promise.prototype.then) {\n            while (val._y === 3) {\n              val = val._z;\n            }\n            if (val._y === 1) return res(i, val._z);\n            if (val._y === 2) reject(val._z);\n            val.then(function (val) {\n              res(i, val);\n            }, reject);\n            return;\n          } else {\n            var then = val.then;\n            if (typeof then === 'function') {\n              var p = new Promise(then.bind(val));\n              p.then(function (val) {\n                res(i, val);\n              }, reject);\n              return;\n            }\n          }\n        }\n        args[i] = val;\n        if (--remaining === 0) {\n          resolve(args);\n        }\n      }\n      for (var i = 0; i < args.length; i++) {\n        res(i, args[i]);\n      }\n    });\n  };\n  function onSettledFulfill(value) {\n    return {\n      status: 'fulfilled',\n      value: value\n    };\n  }\n  function onSettledReject(reason) {\n    return {\n      status: 'rejected',\n      reason: reason\n    };\n  }\n  function mapAllSettled(item) {\n    if (item && (typeof item === 'object' || typeof item === 'function')) {\n      if (item instanceof Promise && item.then === Promise.prototype.then) {\n        return item.then(onSettledFulfill, onSettledReject);\n      }\n      var then = item.then;\n      if (typeof then === 'function') {\n        return new Promise(then.bind(item)).then(onSettledFulfill, onSettledReject);\n      }\n    }\n    return onSettledFulfill(item);\n  }\n  Promise.allSettled = function (iterable) {\n    return Promise.all(iterableToArray(iterable).map(mapAllSettled));\n  };\n  Promise.reject = function (value) {\n    return new Promise(function (resolve, reject) {\n      reject(value);\n    });\n  };\n  Promise.race = function (values) {\n    return new Promise(function (resolve, reject) {\n      iterableToArray(values).forEach(function (value) {\n        Promise.resolve(value).then(resolve, reject);\n      });\n    });\n  };\n\n  /* Prototype Methods */\n\n  Promise.prototype['catch'] = function (onRejected) {\n    return this.then(null, onRejected);\n  };\n  function getAggregateError(errors) {\n    if (typeof AggregateError === 'function') {\n      return new AggregateError(errors, 'All promises were rejected');\n    }\n    var error = new Error('All promises were rejected');\n    error.name = 'AggregateError';\n    error.errors = errors;\n    return error;\n  }\n  Promise.any = function promiseAny(values) {\n    return new Promise(function (resolve, reject) {\n      var promises = iterableToArray(values);\n      var hasResolved = false;\n      var rejectionReasons = [];\n      function resolveOnce(value) {\n        if (!hasResolved) {\n          hasResolved = true;\n          resolve(value);\n        }\n      }\n      function rejectionCheck(reason) {\n        rejectionReasons.push(reason);\n        if (rejectionReasons.length === promises.length) {\n          reject(getAggregateError(rejectionReasons));\n        }\n      }\n      if (promises.length === 0) {\n        reject(getAggregateError(rejectionReasons));\n      } else {\n        promises.forEach(function (value) {\n          Promise.resolve(value).then(resolveOnce, rejectionCheck);\n        });\n      }\n    });\n  };\n});", "lineCount": 175, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 5, 0], [5, 6, 5, 4, "Promise"], [5, 13, 5, 11], [5, 16, 5, 14, "require"], [5, 23, 5, 21], [5, 24, 5, 21, "_dependencyMap"], [5, 38, 5, 21], [5, 54, 5, 33], [5, 55, 5, 34], [6, 2, 7, 0, "module"], [6, 8, 7, 6], [6, 9, 7, 7, "exports"], [6, 16, 7, 14], [6, 19, 7, 17, "Promise"], [6, 26, 7, 24], [8, 2, 9, 0], [10, 2, 11, 0], [10, 6, 11, 4, "TRUE"], [10, 10, 11, 8], [10, 13, 11, 11, "valuePromise"], [10, 25, 11, 23], [10, 26, 11, 24], [10, 30, 11, 28], [10, 31, 11, 29], [11, 2, 12, 0], [11, 6, 12, 4, "FALSE"], [11, 11, 12, 9], [11, 14, 12, 12, "valuePromise"], [11, 26, 12, 24], [11, 27, 12, 25], [11, 32, 12, 30], [11, 33, 12, 31], [12, 2, 13, 0], [12, 6, 13, 4, "NULL"], [12, 10, 13, 8], [12, 13, 13, 11, "valuePromise"], [12, 25, 13, 23], [12, 26, 13, 24], [12, 30, 13, 28], [12, 31, 13, 29], [13, 2, 14, 0], [13, 6, 14, 4, "UNDEFINED"], [13, 15, 14, 13], [13, 18, 14, 16, "valuePromise"], [13, 30, 14, 28], [13, 31, 14, 29, "undefined"], [13, 40, 14, 38], [13, 41, 14, 39], [14, 2, 15, 0], [14, 6, 15, 4, "ZERO"], [14, 10, 15, 8], [14, 13, 15, 11, "valuePromise"], [14, 25, 15, 23], [14, 26, 15, 24], [14, 27, 15, 25], [14, 28, 15, 26], [15, 2, 16, 0], [15, 6, 16, 4, "EMPTYSTRING"], [15, 17, 16, 15], [15, 20, 16, 18, "valuePromise"], [15, 32, 16, 30], [15, 33, 16, 31], [15, 35, 16, 33], [15, 36, 16, 34], [16, 2, 18, 0], [16, 11, 18, 9, "valuePromise"], [16, 23, 18, 21, "valuePromise"], [16, 24, 18, 22, "value"], [16, 29, 18, 27], [16, 31, 18, 29], [17, 4, 19, 2], [17, 8, 19, 6, "p"], [17, 9, 19, 7], [17, 12, 19, 10], [17, 16, 19, 14, "Promise"], [17, 23, 19, 21], [17, 24, 19, 22, "Promise"], [17, 31, 19, 29], [17, 32, 19, 30, "_D"], [17, 34, 19, 32], [17, 35, 19, 33], [18, 4, 20, 2, "p"], [18, 5, 20, 3], [18, 6, 20, 4, "_y"], [18, 8, 20, 6], [18, 11, 20, 9], [18, 12, 20, 10], [19, 4, 21, 2, "p"], [19, 5, 21, 3], [19, 6, 21, 4, "_z"], [19, 8, 21, 6], [19, 11, 21, 9, "value"], [19, 16, 21, 14], [20, 4, 22, 2], [20, 11, 22, 9, "p"], [20, 12, 22, 10], [21, 2, 23, 0], [22, 2, 24, 0, "Promise"], [22, 9, 24, 7], [22, 10, 24, 8, "resolve"], [22, 17, 24, 15], [22, 20, 24, 18], [22, 30, 24, 28, "value"], [22, 35, 24, 33], [22, 37, 24, 35], [23, 4, 25, 2], [23, 8, 25, 6, "value"], [23, 13, 25, 11], [23, 25, 25, 23, "Promise"], [23, 32, 25, 30], [23, 34, 25, 32], [23, 41, 25, 39, "value"], [23, 46, 25, 44], [24, 4, 27, 2], [24, 8, 27, 6, "value"], [24, 13, 27, 11], [24, 18, 27, 16], [24, 22, 27, 20], [24, 24, 27, 22], [24, 31, 27, 29, "NULL"], [24, 35, 27, 33], [25, 4, 28, 2], [25, 8, 28, 6, "value"], [25, 13, 28, 11], [25, 18, 28, 16, "undefined"], [25, 27, 28, 25], [25, 29, 28, 27], [25, 36, 28, 34, "UNDEFINED"], [25, 45, 28, 43], [26, 4, 29, 2], [26, 8, 29, 6, "value"], [26, 13, 29, 11], [26, 18, 29, 16], [26, 22, 29, 20], [26, 24, 29, 22], [26, 31, 29, 29, "TRUE"], [26, 35, 29, 33], [27, 4, 30, 2], [27, 8, 30, 6, "value"], [27, 13, 30, 11], [27, 18, 30, 16], [27, 23, 30, 21], [27, 25, 30, 23], [27, 32, 30, 30, "FALSE"], [27, 37, 30, 35], [28, 4, 31, 2], [28, 8, 31, 6, "value"], [28, 13, 31, 11], [28, 18, 31, 16], [28, 19, 31, 17], [28, 21, 31, 19], [28, 28, 31, 26, "ZERO"], [28, 32, 31, 30], [29, 4, 32, 2], [29, 8, 32, 6, "value"], [29, 13, 32, 11], [29, 18, 32, 16], [29, 20, 32, 18], [29, 22, 32, 20], [29, 29, 32, 27, "EMPTYSTRING"], [29, 40, 32, 38], [30, 4, 34, 2], [30, 8, 34, 6], [30, 15, 34, 13, "value"], [30, 20, 34, 18], [30, 25, 34, 23], [30, 33, 34, 31], [30, 37, 34, 35], [30, 44, 34, 42, "value"], [30, 49, 34, 47], [30, 54, 34, 52], [30, 64, 34, 62], [30, 66, 34, 64], [31, 6, 35, 4], [31, 10, 35, 8], [32, 8, 36, 6], [32, 12, 36, 10, "then"], [32, 16, 36, 14], [32, 19, 36, 17, "value"], [32, 24, 36, 22], [32, 25, 36, 23, "then"], [32, 29, 36, 27], [33, 8, 37, 6], [33, 12, 37, 10], [33, 19, 37, 17, "then"], [33, 23, 37, 21], [33, 28, 37, 26], [33, 38, 37, 36], [33, 40, 37, 38], [34, 10, 38, 8], [34, 17, 38, 15], [34, 21, 38, 19, "Promise"], [34, 28, 38, 26], [34, 29, 38, 27, "then"], [34, 33, 38, 31], [34, 34, 38, 32, "bind"], [34, 38, 38, 36], [34, 39, 38, 37, "value"], [34, 44, 38, 42], [34, 45, 38, 43], [34, 46, 38, 44], [35, 8, 39, 6], [36, 6, 40, 4], [36, 7, 40, 5], [36, 8, 40, 6], [36, 15, 40, 13, "ex"], [36, 17, 40, 15], [36, 19, 40, 17], [37, 8, 41, 6], [37, 15, 41, 13], [37, 19, 41, 17, "Promise"], [37, 26, 41, 24], [37, 27, 41, 25], [37, 37, 41, 35, "resolve"], [37, 44, 41, 42], [37, 46, 41, 44, "reject"], [37, 52, 41, 50], [37, 54, 41, 52], [38, 10, 42, 8, "reject"], [38, 16, 42, 14], [38, 17, 42, 15, "ex"], [38, 19, 42, 17], [38, 20, 42, 18], [39, 8, 43, 6], [39, 9, 43, 7], [39, 10, 43, 8], [40, 6, 44, 4], [41, 4, 45, 2], [42, 4, 46, 2], [42, 11, 46, 9, "valuePromise"], [42, 23, 46, 21], [42, 24, 46, 22, "value"], [42, 29, 46, 27], [42, 30, 46, 28], [43, 2, 47, 0], [43, 3, 47, 1], [44, 2, 49, 0], [44, 6, 49, 4, "iterableToArray"], [44, 21, 49, 19], [44, 24, 49, 22], [44, 33, 49, 22, "iterableToArray"], [44, 34, 49, 32, "iterable"], [44, 42, 49, 40], [44, 44, 49, 42], [45, 4, 50, 2], [45, 8, 50, 6], [45, 15, 50, 13, "Array"], [45, 20, 50, 18], [45, 21, 50, 19, "from"], [45, 25, 50, 23], [45, 30, 50, 28], [45, 40, 50, 38], [45, 42, 50, 40], [46, 6, 51, 4], [47, 6, 52, 4, "iterableToArray"], [47, 21, 52, 19], [47, 24, 52, 22, "Array"], [47, 29, 52, 27], [47, 30, 52, 28, "from"], [47, 34, 52, 32], [48, 6, 53, 4], [48, 13, 53, 11, "Array"], [48, 18, 53, 16], [48, 19, 53, 17, "from"], [48, 23, 53, 21], [48, 24, 53, 22, "iterable"], [48, 32, 53, 30], [48, 33, 53, 31], [49, 4, 54, 2], [51, 4, 56, 2], [52, 4, 57, 2, "iterableToArray"], [52, 19, 57, 17], [52, 22, 57, 20], [52, 31, 57, 20, "iterableToArray"], [52, 32, 57, 30, "x"], [52, 33, 57, 31], [52, 35, 57, 33], [53, 6, 57, 35], [53, 13, 57, 42, "Array"], [53, 18, 57, 47], [53, 19, 57, 48, "prototype"], [53, 28, 57, 57], [53, 29, 57, 58, "slice"], [53, 34, 57, 63], [53, 35, 57, 64, "call"], [53, 39, 57, 68], [53, 40, 57, 69, "x"], [53, 41, 57, 70], [53, 42, 57, 71], [54, 4, 57, 73], [54, 5, 57, 74], [55, 4, 58, 2], [55, 11, 58, 9, "Array"], [55, 16, 58, 14], [55, 17, 58, 15, "prototype"], [55, 26, 58, 24], [55, 27, 58, 25, "slice"], [55, 32, 58, 30], [55, 33, 58, 31, "call"], [55, 37, 58, 35], [55, 38, 58, 36, "iterable"], [55, 46, 58, 44], [55, 47, 58, 45], [56, 2, 59, 0], [56, 3, 59, 1], [57, 2, 61, 0, "Promise"], [57, 9, 61, 7], [57, 10, 61, 8, "all"], [57, 13, 61, 11], [57, 16, 61, 14], [57, 26, 61, 24, "arr"], [57, 29, 61, 27], [57, 31, 61, 29], [58, 4, 62, 2], [58, 8, 62, 6, "args"], [58, 12, 62, 10], [58, 15, 62, 13, "iterableToArray"], [58, 30, 62, 28], [58, 31, 62, 29, "arr"], [58, 34, 62, 32], [58, 35, 62, 33], [59, 4, 64, 2], [59, 11, 64, 9], [59, 15, 64, 13, "Promise"], [59, 22, 64, 20], [59, 23, 64, 21], [59, 33, 64, 31, "resolve"], [59, 40, 64, 38], [59, 42, 64, 40, "reject"], [59, 48, 64, 46], [59, 50, 64, 48], [60, 6, 65, 4], [60, 10, 65, 8, "args"], [60, 14, 65, 12], [60, 15, 65, 13, "length"], [60, 21, 65, 19], [60, 26, 65, 24], [60, 27, 65, 25], [60, 29, 65, 27], [60, 36, 65, 34, "resolve"], [60, 43, 65, 41], [60, 44, 65, 42], [60, 46, 65, 44], [60, 47, 65, 45], [61, 6, 66, 4], [61, 10, 66, 8, "remaining"], [61, 19, 66, 17], [61, 22, 66, 20, "args"], [61, 26, 66, 24], [61, 27, 66, 25, "length"], [61, 33, 66, 31], [62, 6, 67, 4], [62, 15, 67, 13, "res"], [62, 18, 67, 16, "res"], [62, 19, 67, 17, "i"], [62, 20, 67, 18], [62, 22, 67, 20, "val"], [62, 25, 67, 23], [62, 27, 67, 25], [63, 8, 68, 6], [63, 12, 68, 10, "val"], [63, 15, 68, 13], [63, 20, 68, 18], [63, 27, 68, 25, "val"], [63, 30, 68, 28], [63, 35, 68, 33], [63, 43, 68, 41], [63, 47, 68, 45], [63, 54, 68, 52, "val"], [63, 57, 68, 55], [63, 62, 68, 60], [63, 72, 68, 70], [63, 73, 68, 71], [63, 75, 68, 73], [64, 10, 69, 8], [64, 14, 69, 12, "val"], [64, 17, 69, 15], [64, 29, 69, 27, "Promise"], [64, 36, 69, 34], [64, 40, 69, 38, "val"], [64, 43, 69, 41], [64, 44, 69, 42, "then"], [64, 48, 69, 46], [64, 53, 69, 51, "Promise"], [64, 60, 69, 58], [64, 61, 69, 59, "prototype"], [64, 70, 69, 68], [64, 71, 69, 69, "then"], [64, 75, 69, 73], [64, 77, 69, 75], [65, 12, 70, 10], [65, 19, 70, 17, "val"], [65, 22, 70, 20], [65, 23, 70, 21, "_y"], [65, 25, 70, 23], [65, 30, 70, 28], [65, 31, 70, 29], [65, 33, 70, 31], [66, 14, 71, 12, "val"], [66, 17, 71, 15], [66, 20, 71, 18, "val"], [66, 23, 71, 21], [66, 24, 71, 22, "_z"], [66, 26, 71, 24], [67, 12, 72, 10], [68, 12, 73, 10], [68, 16, 73, 14, "val"], [68, 19, 73, 17], [68, 20, 73, 18, "_y"], [68, 22, 73, 20], [68, 27, 73, 25], [68, 28, 73, 26], [68, 30, 73, 28], [68, 37, 73, 35, "res"], [68, 40, 73, 38], [68, 41, 73, 39, "i"], [68, 42, 73, 40], [68, 44, 73, 42, "val"], [68, 47, 73, 45], [68, 48, 73, 46, "_z"], [68, 50, 73, 48], [68, 51, 73, 49], [69, 12, 74, 10], [69, 16, 74, 14, "val"], [69, 19, 74, 17], [69, 20, 74, 18, "_y"], [69, 22, 74, 20], [69, 27, 74, 25], [69, 28, 74, 26], [69, 30, 74, 28, "reject"], [69, 36, 74, 34], [69, 37, 74, 35, "val"], [69, 40, 74, 38], [69, 41, 74, 39, "_z"], [69, 43, 74, 41], [69, 44, 74, 42], [70, 12, 75, 10, "val"], [70, 15, 75, 13], [70, 16, 75, 14, "then"], [70, 20, 75, 18], [70, 21, 75, 19], [70, 31, 75, 29, "val"], [70, 34, 75, 32], [70, 36, 75, 34], [71, 14, 76, 12, "res"], [71, 17, 76, 15], [71, 18, 76, 16, "i"], [71, 19, 76, 17], [71, 21, 76, 19, "val"], [71, 24, 76, 22], [71, 25, 76, 23], [72, 12, 77, 10], [72, 13, 77, 11], [72, 15, 77, 13, "reject"], [72, 21, 77, 19], [72, 22, 77, 20], [73, 12, 78, 10], [74, 10, 79, 8], [74, 11, 79, 9], [74, 17, 79, 15], [75, 12, 80, 10], [75, 16, 80, 14, "then"], [75, 20, 80, 18], [75, 23, 80, 21, "val"], [75, 26, 80, 24], [75, 27, 80, 25, "then"], [75, 31, 80, 29], [76, 12, 81, 10], [76, 16, 81, 14], [76, 23, 81, 21, "then"], [76, 27, 81, 25], [76, 32, 81, 30], [76, 42, 81, 40], [76, 44, 81, 42], [77, 14, 82, 12], [77, 18, 82, 16, "p"], [77, 19, 82, 17], [77, 22, 82, 20], [77, 26, 82, 24, "Promise"], [77, 33, 82, 31], [77, 34, 82, 32, "then"], [77, 38, 82, 36], [77, 39, 82, 37, "bind"], [77, 43, 82, 41], [77, 44, 82, 42, "val"], [77, 47, 82, 45], [77, 48, 82, 46], [77, 49, 82, 47], [78, 14, 83, 12, "p"], [78, 15, 83, 13], [78, 16, 83, 14, "then"], [78, 20, 83, 18], [78, 21, 83, 19], [78, 31, 83, 29, "val"], [78, 34, 83, 32], [78, 36, 83, 34], [79, 16, 84, 14, "res"], [79, 19, 84, 17], [79, 20, 84, 18, "i"], [79, 21, 84, 19], [79, 23, 84, 21, "val"], [79, 26, 84, 24], [79, 27, 84, 25], [80, 14, 85, 12], [80, 15, 85, 13], [80, 17, 85, 15, "reject"], [80, 23, 85, 21], [80, 24, 85, 22], [81, 14, 86, 12], [82, 12, 87, 10], [83, 10, 88, 8], [84, 8, 89, 6], [85, 8, 90, 6, "args"], [85, 12, 90, 10], [85, 13, 90, 11, "i"], [85, 14, 90, 12], [85, 15, 90, 13], [85, 18, 90, 16, "val"], [85, 21, 90, 19], [86, 8, 91, 6], [86, 12, 91, 10], [86, 14, 91, 12, "remaining"], [86, 23, 91, 21], [86, 28, 91, 26], [86, 29, 91, 27], [86, 31, 91, 29], [87, 10, 92, 8, "resolve"], [87, 17, 92, 15], [87, 18, 92, 16, "args"], [87, 22, 92, 20], [87, 23, 92, 21], [88, 8, 93, 6], [89, 6, 94, 4], [90, 6, 95, 4], [90, 11, 95, 9], [90, 15, 95, 13, "i"], [90, 16, 95, 14], [90, 19, 95, 17], [90, 20, 95, 18], [90, 22, 95, 20, "i"], [90, 23, 95, 21], [90, 26, 95, 24, "args"], [90, 30, 95, 28], [90, 31, 95, 29, "length"], [90, 37, 95, 35], [90, 39, 95, 37, "i"], [90, 40, 95, 38], [90, 42, 95, 40], [90, 44, 95, 42], [91, 8, 96, 6, "res"], [91, 11, 96, 9], [91, 12, 96, 10, "i"], [91, 13, 96, 11], [91, 15, 96, 13, "args"], [91, 19, 96, 17], [91, 20, 96, 18, "i"], [91, 21, 96, 19], [91, 22, 96, 20], [91, 23, 96, 21], [92, 6, 97, 4], [93, 4, 98, 2], [93, 5, 98, 3], [93, 6, 98, 4], [94, 2, 99, 0], [94, 3, 99, 1], [95, 2, 101, 0], [95, 11, 101, 9, "onSettledFulfill"], [95, 27, 101, 25, "onSettledFulfill"], [95, 28, 101, 26, "value"], [95, 33, 101, 31], [95, 35, 101, 33], [96, 4, 102, 2], [96, 11, 102, 9], [97, 6, 102, 11, "status"], [97, 12, 102, 17], [97, 14, 102, 19], [97, 25, 102, 30], [98, 6, 102, 32, "value"], [98, 11, 102, 37], [98, 13, 102, 39, "value"], [99, 4, 102, 45], [99, 5, 102, 46], [100, 2, 103, 0], [101, 2, 104, 0], [101, 11, 104, 9, "onSettledReject"], [101, 26, 104, 24, "onSettledReject"], [101, 27, 104, 25, "reason"], [101, 33, 104, 31], [101, 35, 104, 33], [102, 4, 105, 2], [102, 11, 105, 9], [103, 6, 105, 11, "status"], [103, 12, 105, 17], [103, 14, 105, 19], [103, 24, 105, 29], [104, 6, 105, 31, "reason"], [104, 12, 105, 37], [104, 14, 105, 39, "reason"], [105, 4, 105, 46], [105, 5, 105, 47], [106, 2, 106, 0], [107, 2, 107, 0], [107, 11, 107, 9, "mapAllSettled"], [107, 24, 107, 22, "mapAllSettled"], [107, 25, 107, 23, "item"], [107, 29, 107, 27], [107, 31, 107, 29], [108, 4, 108, 2], [108, 8, 108, 5, "item"], [108, 12, 108, 9], [108, 17, 108, 14], [108, 24, 108, 21, "item"], [108, 28, 108, 25], [108, 33, 108, 30], [108, 41, 108, 38], [108, 45, 108, 42], [108, 52, 108, 49, "item"], [108, 56, 108, 53], [108, 61, 108, 58], [108, 71, 108, 68], [108, 72, 108, 69], [108, 74, 108, 70], [109, 6, 109, 4], [109, 10, 109, 7, "item"], [109, 14, 109, 11], [109, 26, 109, 23, "Promise"], [109, 33, 109, 30], [109, 37, 109, 34, "item"], [109, 41, 109, 38], [109, 42, 109, 39, "then"], [109, 46, 109, 43], [109, 51, 109, 48, "Promise"], [109, 58, 109, 55], [109, 59, 109, 56, "prototype"], [109, 68, 109, 65], [109, 69, 109, 66, "then"], [109, 73, 109, 70], [109, 75, 109, 71], [110, 8, 110, 6], [110, 15, 110, 13, "item"], [110, 19, 110, 17], [110, 20, 110, 18, "then"], [110, 24, 110, 22], [110, 25, 110, 23, "onSettledFulfill"], [110, 41, 110, 39], [110, 43, 110, 41, "onSettledReject"], [110, 58, 110, 56], [110, 59, 110, 57], [111, 6, 111, 4], [112, 6, 112, 4], [112, 10, 112, 8, "then"], [112, 14, 112, 12], [112, 17, 112, 15, "item"], [112, 21, 112, 19], [112, 22, 112, 20, "then"], [112, 26, 112, 24], [113, 6, 113, 4], [113, 10, 113, 8], [113, 17, 113, 15, "then"], [113, 21, 113, 19], [113, 26, 113, 24], [113, 36, 113, 34], [113, 38, 113, 36], [114, 8, 114, 6], [114, 15, 114, 13], [114, 19, 114, 17, "Promise"], [114, 26, 114, 24], [114, 27, 114, 25, "then"], [114, 31, 114, 29], [114, 32, 114, 30, "bind"], [114, 36, 114, 34], [114, 37, 114, 35, "item"], [114, 41, 114, 39], [114, 42, 114, 40], [114, 43, 114, 41], [114, 44, 114, 42, "then"], [114, 48, 114, 46], [114, 49, 114, 47, "onSettledFulfill"], [114, 65, 114, 63], [114, 67, 114, 65, "onSettledReject"], [114, 82, 114, 80], [114, 83, 114, 81], [115, 6, 115, 4], [116, 4, 116, 2], [117, 4, 118, 2], [117, 11, 118, 9, "onSettledFulfill"], [117, 27, 118, 25], [117, 28, 118, 26, "item"], [117, 32, 118, 30], [117, 33, 118, 31], [118, 2, 119, 0], [119, 2, 120, 0, "Promise"], [119, 9, 120, 7], [119, 10, 120, 8, "allSettled"], [119, 20, 120, 18], [119, 23, 120, 21], [119, 33, 120, 31, "iterable"], [119, 41, 120, 39], [119, 43, 120, 41], [120, 4, 121, 2], [120, 11, 121, 9, "Promise"], [120, 18, 121, 16], [120, 19, 121, 17, "all"], [120, 22, 121, 20], [120, 23, 121, 21, "iterableToArray"], [120, 38, 121, 36], [120, 39, 121, 37, "iterable"], [120, 47, 121, 45], [120, 48, 121, 46], [120, 49, 121, 47, "map"], [120, 52, 121, 50], [120, 53, 121, 51, "mapAllSettled"], [120, 66, 121, 64], [120, 67, 121, 65], [120, 68, 121, 66], [121, 2, 122, 0], [121, 3, 122, 1], [122, 2, 124, 0, "Promise"], [122, 9, 124, 7], [122, 10, 124, 8, "reject"], [122, 16, 124, 14], [122, 19, 124, 17], [122, 29, 124, 27, "value"], [122, 34, 124, 32], [122, 36, 124, 34], [123, 4, 125, 2], [123, 11, 125, 9], [123, 15, 125, 13, "Promise"], [123, 22, 125, 20], [123, 23, 125, 21], [123, 33, 125, 31, "resolve"], [123, 40, 125, 38], [123, 42, 125, 40, "reject"], [123, 48, 125, 46], [123, 50, 125, 48], [124, 6, 126, 4, "reject"], [124, 12, 126, 10], [124, 13, 126, 11, "value"], [124, 18, 126, 16], [124, 19, 126, 17], [125, 4, 127, 2], [125, 5, 127, 3], [125, 6, 127, 4], [126, 2, 128, 0], [126, 3, 128, 1], [127, 2, 130, 0, "Promise"], [127, 9, 130, 7], [127, 10, 130, 8, "race"], [127, 14, 130, 12], [127, 17, 130, 15], [127, 27, 130, 25, "values"], [127, 33, 130, 31], [127, 35, 130, 33], [128, 4, 131, 2], [128, 11, 131, 9], [128, 15, 131, 13, "Promise"], [128, 22, 131, 20], [128, 23, 131, 21], [128, 33, 131, 31, "resolve"], [128, 40, 131, 38], [128, 42, 131, 40, "reject"], [128, 48, 131, 46], [128, 50, 131, 48], [129, 6, 132, 4, "iterableToArray"], [129, 21, 132, 19], [129, 22, 132, 20, "values"], [129, 28, 132, 26], [129, 29, 132, 27], [129, 30, 132, 28, "for<PERSON>ach"], [129, 37, 132, 35], [129, 38, 132, 36], [129, 48, 132, 45, "value"], [129, 53, 132, 50], [129, 55, 132, 51], [130, 8, 133, 6, "Promise"], [130, 15, 133, 13], [130, 16, 133, 14, "resolve"], [130, 23, 133, 21], [130, 24, 133, 22, "value"], [130, 29, 133, 27], [130, 30, 133, 28], [130, 31, 133, 29, "then"], [130, 35, 133, 33], [130, 36, 133, 34, "resolve"], [130, 43, 133, 41], [130, 45, 133, 43, "reject"], [130, 51, 133, 49], [130, 52, 133, 50], [131, 6, 134, 4], [131, 7, 134, 5], [131, 8, 134, 6], [132, 4, 135, 2], [132, 5, 135, 3], [132, 6, 135, 4], [133, 2, 136, 0], [133, 3, 136, 1], [135, 2, 138, 0], [137, 2, 140, 0, "Promise"], [137, 9, 140, 7], [137, 10, 140, 8, "prototype"], [137, 19, 140, 17], [137, 20, 140, 18], [137, 27, 140, 25], [137, 28, 140, 26], [137, 31, 140, 29], [137, 41, 140, 39, "onRejected"], [137, 51, 140, 49], [137, 53, 140, 51], [138, 4, 141, 2], [138, 11, 141, 9], [138, 15, 141, 13], [138, 16, 141, 14, "then"], [138, 20, 141, 18], [138, 21, 141, 19], [138, 25, 141, 23], [138, 27, 141, 25, "onRejected"], [138, 37, 141, 35], [138, 38, 141, 36], [139, 2, 142, 0], [139, 3, 142, 1], [140, 2, 144, 0], [140, 11, 144, 9, "getAggregateError"], [140, 28, 144, 26, "getAggregateError"], [140, 29, 144, 27, "errors"], [140, 35, 144, 33], [140, 37, 144, 34], [141, 4, 145, 2], [141, 8, 145, 5], [141, 15, 145, 12, "AggregateError"], [141, 29, 145, 26], [141, 34, 145, 31], [141, 44, 145, 41], [141, 46, 145, 42], [142, 6, 146, 4], [142, 13, 146, 11], [142, 17, 146, 15, "AggregateError"], [142, 31, 146, 29], [142, 32, 146, 30, "errors"], [142, 38, 146, 36], [142, 40, 146, 37], [142, 68, 146, 65], [142, 69, 146, 66], [143, 4, 147, 2], [144, 4, 149, 2], [144, 8, 149, 6, "error"], [144, 13, 149, 11], [144, 16, 149, 14], [144, 20, 149, 18, "Error"], [144, 25, 149, 23], [144, 26, 149, 24], [144, 54, 149, 52], [144, 55, 149, 53], [145, 4, 151, 2, "error"], [145, 9, 151, 7], [145, 10, 151, 8, "name"], [145, 14, 151, 12], [145, 17, 151, 15], [145, 33, 151, 31], [146, 4, 152, 2, "error"], [146, 9, 152, 7], [146, 10, 152, 8, "errors"], [146, 16, 152, 14], [146, 19, 152, 17, "errors"], [146, 25, 152, 23], [147, 4, 154, 2], [147, 11, 154, 9, "error"], [147, 16, 154, 14], [148, 2, 155, 0], [149, 2, 157, 0, "Promise"], [149, 9, 157, 7], [149, 10, 157, 8, "any"], [149, 13, 157, 11], [149, 16, 157, 14], [149, 25, 157, 23, "promiseAny"], [149, 35, 157, 33, "promiseAny"], [149, 36, 157, 34, "values"], [149, 42, 157, 40], [149, 44, 157, 42], [150, 4, 158, 2], [150, 11, 158, 9], [150, 15, 158, 13, "Promise"], [150, 22, 158, 20], [150, 23, 158, 21], [150, 33, 158, 30, "resolve"], [150, 40, 158, 37], [150, 42, 158, 39, "reject"], [150, 48, 158, 45], [150, 50, 158, 47], [151, 6, 159, 4], [151, 10, 159, 8, "promises"], [151, 18, 159, 16], [151, 21, 159, 19, "iterableToArray"], [151, 36, 159, 34], [151, 37, 159, 35, "values"], [151, 43, 159, 41], [151, 44, 159, 42], [152, 6, 160, 4], [152, 10, 160, 8, "hasResolved"], [152, 21, 160, 19], [152, 24, 160, 22], [152, 29, 160, 27], [153, 6, 161, 4], [153, 10, 161, 8, "rejectionReasons"], [153, 26, 161, 24], [153, 29, 161, 27], [153, 31, 161, 29], [154, 6, 163, 4], [154, 15, 163, 13, "resolveOnce"], [154, 26, 163, 24, "resolveOnce"], [154, 27, 163, 25, "value"], [154, 32, 163, 30], [154, 34, 163, 32], [155, 8, 164, 6], [155, 12, 164, 10], [155, 13, 164, 11, "hasResolved"], [155, 24, 164, 22], [155, 26, 164, 24], [156, 10, 165, 8, "hasResolved"], [156, 21, 165, 19], [156, 24, 165, 22], [156, 28, 165, 26], [157, 10, 166, 8, "resolve"], [157, 17, 166, 15], [157, 18, 166, 16, "value"], [157, 23, 166, 21], [157, 24, 166, 22], [158, 8, 167, 6], [159, 6, 168, 4], [160, 6, 170, 4], [160, 15, 170, 13, "<PERSON><PERSON><PERSON><PERSON>"], [160, 29, 170, 27, "<PERSON><PERSON><PERSON><PERSON>"], [160, 30, 170, 28, "reason"], [160, 36, 170, 34], [160, 38, 170, 36], [161, 8, 171, 6, "rejectionReasons"], [161, 24, 171, 22], [161, 25, 171, 23, "push"], [161, 29, 171, 27], [161, 30, 171, 28, "reason"], [161, 36, 171, 34], [161, 37, 171, 35], [162, 8, 173, 6], [162, 12, 173, 10, "rejectionReasons"], [162, 28, 173, 26], [162, 29, 173, 27, "length"], [162, 35, 173, 33], [162, 40, 173, 38, "promises"], [162, 48, 173, 46], [162, 49, 173, 47, "length"], [162, 55, 173, 53], [162, 57, 173, 55], [163, 10, 174, 8, "reject"], [163, 16, 174, 14], [163, 17, 174, 15, "getAggregateError"], [163, 34, 174, 32], [163, 35, 174, 33, "rejectionReasons"], [163, 51, 174, 49], [163, 52, 174, 50], [163, 53, 174, 51], [164, 8, 175, 6], [165, 6, 176, 4], [166, 6, 178, 4], [166, 10, 178, 7, "promises"], [166, 18, 178, 15], [166, 19, 178, 16, "length"], [166, 25, 178, 22], [166, 30, 178, 27], [166, 31, 178, 28], [166, 33, 178, 29], [167, 8, 179, 6, "reject"], [167, 14, 179, 12], [167, 15, 179, 13, "getAggregateError"], [167, 32, 179, 30], [167, 33, 179, 31, "rejectionReasons"], [167, 49, 179, 47], [167, 50, 179, 48], [167, 51, 179, 49], [168, 6, 180, 4], [168, 7, 180, 5], [168, 13, 180, 11], [169, 8, 181, 6, "promises"], [169, 16, 181, 14], [169, 17, 181, 15, "for<PERSON>ach"], [169, 24, 181, 22], [169, 25, 181, 23], [169, 35, 181, 32, "value"], [169, 40, 181, 37], [169, 42, 181, 38], [170, 10, 182, 8, "Promise"], [170, 17, 182, 15], [170, 18, 182, 16, "resolve"], [170, 25, 182, 23], [170, 26, 182, 24, "value"], [170, 31, 182, 29], [170, 32, 182, 30], [170, 33, 182, 31, "then"], [170, 37, 182, 35], [170, 38, 182, 36, "resolveOnce"], [170, 49, 182, 47], [170, 51, 182, 49, "<PERSON><PERSON><PERSON><PERSON>"], [170, 65, 182, 63], [170, 66, 182, 64], [171, 8, 183, 6], [171, 9, 183, 7], [171, 10, 183, 8], [172, 6, 184, 4], [173, 4, 185, 2], [173, 5, 185, 3], [173, 6, 185, 4], [174, 2, 186, 0], [174, 3, 186, 1], [175, 0, 186, 2], [175, 3]], "functionMap": {"names": ["<global>", "valuePromise", "Promise.resolve", "Promise$argument_0", "iterableToArray", "Promise.all", "res", "val.then$argument_0", "p.then$argument_0", "onSettledFulfill", "onSettledReject", "mapAllSettled", "Promise.allSettled", "Promise.reject", "Promise.race", "iterableToArray.forEach$argument_0", "Promise.prototype._catch", "getAggregateError", "promiseAny", "resolveOnce", "<PERSON><PERSON><PERSON><PERSON>", "promises.forEach$argument_0"], "mappings": "AAA;ACiB;CDK;kBEC;yBCiB;ODE;CFI;sBIE;CJU;cKE;qBFG;IGG;mBCQ;WDE;mBEM;aFE;KHS;GEI;CLC;ASE;CTE;AUC;CVE;AWC;CXY;qBYC;CZE;iBaE;qBVC;GUE;CbC;ecE;qBXC;oCYC;KZE;GWC;CdC;6BgBI;ChBE;AiBE;CjBW;ckBE;qBfC;IgBK;KhBK;IiBE;KjBM;uBkBK;OlBE;GeE;ClBC"}}, "type": "js/module"}]}