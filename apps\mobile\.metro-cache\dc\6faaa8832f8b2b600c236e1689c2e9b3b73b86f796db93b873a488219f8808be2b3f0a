{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PermissionStatus = void 0;\n  var PermissionStatus = exports.PermissionStatus = /*#__PURE__*/function (PermissionStatus) {\n    /**\n     * User has granted the permission.\n     */\n    PermissionStatus[\"GRANTED\"] = \"granted\";\n    /**\n     * User hasn't granted or denied the permission yet.\n     */\n    PermissionStatus[\"UNDETERMINED\"] = \"undetermined\";\n    /**\n     * User has denied the permission.\n     */\n    PermissionStatus[\"DENIED\"] = \"denied\";\n    return PermissionStatus;\n  }({});\n  /**\n   * Permission expiration time. Currently, all permissions are granted permanently.\n   */\n  /**\n   * An object obtained by permissions get and request functions.\n   */\n});", "lineCount": 27, "map": [[6, 6, 1, 12, "PermissionStatus"], [6, 22, 1, 28], [6, 25, 1, 28, "exports"], [6, 32, 1, 28], [6, 33, 1, 28, "PermissionStatus"], [6, 49, 1, 28], [6, 75, 1, 12, "PermissionStatus"], [6, 91, 1, 28], [7, 4, 2, 2], [8, 0, 3, 0], [9, 0, 4, 0], [10, 4, 1, 12, "PermissionStatus"], [10, 20, 1, 28], [11, 4, 6, 2], [12, 0, 7, 0], [13, 0, 8, 0], [14, 4, 1, 12, "PermissionStatus"], [14, 20, 1, 28], [15, 4, 10, 2], [16, 0, 11, 0], [17, 0, 12, 0], [18, 4, 1, 12, "PermissionStatus"], [18, 20, 1, 28], [19, 4, 1, 28], [19, 11, 1, 12, "PermissionStatus"], [19, 27, 1, 28], [20, 2, 1, 28], [21, 2, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 2, 21, 0], [25, 0, 22, 0], [26, 0, 23, 0], [27, 0, 21, 0], [27, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}