{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorFooterButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 72}}], "key": "bpbbcHLLyqst4g4JkZSLcz3emI0=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = LogBoxInspectorFooter;\n  var _View = _interopRequireDefault(require(_dependencyMap[1], \"../../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[2], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"../../Text/Text\"));\n  var _LogBoxInspectorFooterButton = _interopRequireDefault(require(_dependencyMap[4], \"./LogBoxInspectorFooterButton\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[5], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxInspectorFooter.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function LogBoxInspectorFooter(props) {\n    if (props.level === 'syntax') {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.root,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.button,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            id: \"logbox_dismissable_text\",\n            style: styles.syntaxErrorText,\n            children: \"This error cannot be dismissed.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 7\n      }, this);\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.root,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorFooterButton.default, {\n        id: \"logbox_footer_button_dismiss\",\n        text: \"Dismiss\",\n        onPress: props.onDismiss\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorFooterButton.default, {\n        id: \"logbox_footer_button_minimize\",\n        text: \"Minimize\",\n        onPress: props.onMinimize\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 5\n    }, this);\n  }\n  var styles = _StyleSheet.default.create({\n    root: {\n      backgroundColor: LogBoxStyle.getBackgroundColor(1),\n      shadowColor: '#000',\n      shadowOffset: {\n        width: 0,\n        height: -2\n      },\n      shadowRadius: 2,\n      shadowOpacity: 0.5,\n      flexDirection: 'row'\n    },\n    button: {\n      flex: 1\n    },\n    syntaxErrorText: {\n      textAlign: 'center',\n      width: '100%',\n      height: 48,\n      fontSize: 14,\n      lineHeight: 20,\n      paddingTop: 20,\n      paddingBottom: 50,\n      fontStyle: 'italic',\n      color: LogBoxStyle.getTextColor(0.6)\n    }\n  });\n});", "lineCount": 94, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_View"], [7, 11, 13, 0], [7, 14, 13, 0, "_interopRequireDefault"], [7, 36, 13, 0], [7, 37, 13, 0, "require"], [7, 44, 13, 0], [7, 45, 13, 0, "_dependencyMap"], [7, 59, 13, 0], [8, 2, 14, 0], [8, 6, 14, 0, "_StyleSheet"], [8, 17, 14, 0], [8, 20, 14, 0, "_interopRequireDefault"], [8, 42, 14, 0], [8, 43, 14, 0, "require"], [8, 50, 14, 0], [8, 51, 14, 0, "_dependencyMap"], [8, 65, 14, 0], [9, 2, 15, 0], [9, 6, 15, 0, "_Text"], [9, 11, 15, 0], [9, 14, 15, 0, "_interopRequireDefault"], [9, 36, 15, 0], [9, 37, 15, 0, "require"], [9, 44, 15, 0], [9, 45, 15, 0, "_dependencyMap"], [9, 59, 15, 0], [10, 2, 16, 0], [10, 6, 16, 0, "_LogBoxInspectorFooterButton"], [10, 34, 16, 0], [10, 37, 16, 0, "_interopRequireDefault"], [10, 59, 16, 0], [10, 60, 16, 0, "require"], [10, 67, 16, 0], [10, 68, 16, 0, "_dependencyMap"], [10, 82, 16, 0], [11, 2, 17, 0], [11, 6, 17, 0, "LogBoxStyle"], [11, 17, 17, 0], [11, 20, 17, 0, "_interopRequireWildcard"], [11, 43, 17, 0], [11, 44, 17, 0, "require"], [11, 51, 17, 0], [11, 52, 17, 0, "_dependencyMap"], [11, 66, 17, 0], [12, 2, 18, 0], [12, 6, 18, 0, "React"], [12, 11, 18, 0], [12, 14, 18, 0, "_interopRequireWildcard"], [12, 37, 18, 0], [12, 38, 18, 0, "require"], [12, 45, 18, 0], [12, 46, 18, 0, "_dependencyMap"], [12, 60, 18, 0], [13, 2, 18, 31], [13, 6, 18, 31, "_jsxDevRuntime"], [13, 20, 18, 31], [13, 23, 18, 31, "require"], [13, 30, 18, 31], [13, 31, 18, 31, "_dependencyMap"], [13, 45, 18, 31], [14, 2, 18, 31], [14, 6, 18, 31, "_jsxFileName"], [14, 18, 18, 31], [15, 2, 18, 31], [15, 11, 18, 31, "_interopRequireWildcard"], [15, 35, 18, 31, "e"], [15, 36, 18, 31], [15, 38, 18, 31, "t"], [15, 39, 18, 31], [15, 68, 18, 31, "WeakMap"], [15, 75, 18, 31], [15, 81, 18, 31, "r"], [15, 82, 18, 31], [15, 89, 18, 31, "WeakMap"], [15, 96, 18, 31], [15, 100, 18, 31, "n"], [15, 101, 18, 31], [15, 108, 18, 31, "WeakMap"], [15, 115, 18, 31], [15, 127, 18, 31, "_interopRequireWildcard"], [15, 150, 18, 31], [15, 162, 18, 31, "_interopRequireWildcard"], [15, 163, 18, 31, "e"], [15, 164, 18, 31], [15, 166, 18, 31, "t"], [15, 167, 18, 31], [15, 176, 18, 31, "t"], [15, 177, 18, 31], [15, 181, 18, 31, "e"], [15, 182, 18, 31], [15, 186, 18, 31, "e"], [15, 187, 18, 31], [15, 188, 18, 31, "__esModule"], [15, 198, 18, 31], [15, 207, 18, 31, "e"], [15, 208, 18, 31], [15, 214, 18, 31, "o"], [15, 215, 18, 31], [15, 217, 18, 31, "i"], [15, 218, 18, 31], [15, 220, 18, 31, "f"], [15, 221, 18, 31], [15, 226, 18, 31, "__proto__"], [15, 235, 18, 31], [15, 243, 18, 31, "default"], [15, 250, 18, 31], [15, 252, 18, 31, "e"], [15, 253, 18, 31], [15, 270, 18, 31, "e"], [15, 271, 18, 31], [15, 294, 18, 31, "e"], [15, 295, 18, 31], [15, 320, 18, 31, "e"], [15, 321, 18, 31], [15, 330, 18, 31, "f"], [15, 331, 18, 31], [15, 337, 18, 31, "o"], [15, 338, 18, 31], [15, 341, 18, 31, "t"], [15, 342, 18, 31], [15, 345, 18, 31, "n"], [15, 346, 18, 31], [15, 349, 18, 31, "r"], [15, 350, 18, 31], [15, 358, 18, 31, "o"], [15, 359, 18, 31], [15, 360, 18, 31, "has"], [15, 363, 18, 31], [15, 364, 18, 31, "e"], [15, 365, 18, 31], [15, 375, 18, 31, "o"], [15, 376, 18, 31], [15, 377, 18, 31, "get"], [15, 380, 18, 31], [15, 381, 18, 31, "e"], [15, 382, 18, 31], [15, 385, 18, 31, "o"], [15, 386, 18, 31], [15, 387, 18, 31, "set"], [15, 390, 18, 31], [15, 391, 18, 31, "e"], [15, 392, 18, 31], [15, 394, 18, 31, "f"], [15, 395, 18, 31], [15, 409, 18, 31, "_t"], [15, 411, 18, 31], [15, 415, 18, 31, "e"], [15, 416, 18, 31], [15, 432, 18, 31, "_t"], [15, 434, 18, 31], [15, 441, 18, 31, "hasOwnProperty"], [15, 455, 18, 31], [15, 456, 18, 31, "call"], [15, 460, 18, 31], [15, 461, 18, 31, "e"], [15, 462, 18, 31], [15, 464, 18, 31, "_t"], [15, 466, 18, 31], [15, 473, 18, 31, "i"], [15, 474, 18, 31], [15, 478, 18, 31, "o"], [15, 479, 18, 31], [15, 482, 18, 31, "Object"], [15, 488, 18, 31], [15, 489, 18, 31, "defineProperty"], [15, 503, 18, 31], [15, 508, 18, 31, "Object"], [15, 514, 18, 31], [15, 515, 18, 31, "getOwnPropertyDescriptor"], [15, 539, 18, 31], [15, 540, 18, 31, "e"], [15, 541, 18, 31], [15, 543, 18, 31, "_t"], [15, 545, 18, 31], [15, 552, 18, 31, "i"], [15, 553, 18, 31], [15, 554, 18, 31, "get"], [15, 557, 18, 31], [15, 561, 18, 31, "i"], [15, 562, 18, 31], [15, 563, 18, 31, "set"], [15, 566, 18, 31], [15, 570, 18, 31, "o"], [15, 571, 18, 31], [15, 572, 18, 31, "f"], [15, 573, 18, 31], [15, 575, 18, 31, "_t"], [15, 577, 18, 31], [15, 579, 18, 31, "i"], [15, 580, 18, 31], [15, 584, 18, 31, "f"], [15, 585, 18, 31], [15, 586, 18, 31, "_t"], [15, 588, 18, 31], [15, 592, 18, 31, "e"], [15, 593, 18, 31], [15, 594, 18, 31, "_t"], [15, 596, 18, 31], [15, 607, 18, 31, "f"], [15, 608, 18, 31], [15, 613, 18, 31, "e"], [15, 614, 18, 31], [15, 616, 18, 31, "t"], [15, 617, 18, 31], [16, 2, 26, 15], [16, 11, 26, 24, "LogBoxInspectorFooter"], [16, 32, 26, 45, "LogBoxInspectorFooter"], [16, 33, 26, 46, "props"], [16, 38, 26, 58], [16, 40, 26, 72], [17, 4, 27, 2], [17, 8, 27, 6, "props"], [17, 13, 27, 11], [17, 14, 27, 12, "level"], [17, 19, 27, 17], [17, 24, 27, 22], [17, 32, 27, 30], [17, 34, 27, 32], [18, 6, 28, 4], [18, 26, 29, 6], [18, 30, 29, 6, "_jsxDevRuntime"], [18, 44, 29, 6], [18, 45, 29, 6, "jsxDEV"], [18, 51, 29, 6], [18, 53, 29, 7, "_View"], [18, 58, 29, 7], [18, 59, 29, 7, "default"], [18, 66, 29, 11], [19, 8, 29, 12, "style"], [19, 13, 29, 17], [19, 15, 29, 19, "styles"], [19, 21, 29, 25], [19, 22, 29, 26, "root"], [19, 26, 29, 31], [20, 8, 29, 31, "children"], [20, 16, 29, 31], [20, 31, 30, 8], [20, 35, 30, 8, "_jsxDevRuntime"], [20, 49, 30, 8], [20, 50, 30, 8, "jsxDEV"], [20, 56, 30, 8], [20, 58, 30, 9, "_View"], [20, 63, 30, 9], [20, 64, 30, 9, "default"], [20, 71, 30, 13], [21, 10, 30, 14, "style"], [21, 15, 30, 19], [21, 17, 30, 21, "styles"], [21, 23, 30, 27], [21, 24, 30, 28, "button"], [21, 30, 30, 35], [22, 10, 30, 35, "children"], [22, 18, 30, 35], [22, 33, 31, 10], [22, 37, 31, 10, "_jsxDevRuntime"], [22, 51, 31, 10], [22, 52, 31, 10, "jsxDEV"], [22, 58, 31, 10], [22, 60, 31, 11, "_Text"], [22, 65, 31, 11], [22, 66, 31, 11, "default"], [22, 73, 31, 15], [23, 12, 31, 16, "id"], [23, 14, 31, 18], [23, 16, 31, 19], [23, 41, 31, 44], [24, 12, 31, 45, "style"], [24, 17, 31, 50], [24, 19, 31, 52, "styles"], [24, 25, 31, 58], [24, 26, 31, 59, "syntaxErrorText"], [24, 41, 31, 75], [25, 12, 31, 75, "children"], [25, 20, 31, 75], [25, 22, 31, 76], [26, 10, 33, 10], [27, 12, 33, 10, "fileName"], [27, 20, 33, 10], [27, 22, 33, 10, "_jsxFileName"], [27, 34, 33, 10], [28, 12, 33, 10, "lineNumber"], [28, 22, 33, 10], [29, 12, 33, 10, "columnNumber"], [29, 24, 33, 10], [30, 10, 33, 10], [30, 17, 33, 16], [31, 8, 33, 17], [32, 10, 33, 17, "fileName"], [32, 18, 33, 17], [32, 20, 33, 17, "_jsxFileName"], [32, 32, 33, 17], [33, 10, 33, 17, "lineNumber"], [33, 20, 33, 17], [34, 10, 33, 17, "columnNumber"], [34, 22, 33, 17], [35, 8, 33, 17], [35, 15, 34, 14], [36, 6, 34, 15], [37, 8, 34, 15, "fileName"], [37, 16, 34, 15], [37, 18, 34, 15, "_jsxFileName"], [37, 30, 34, 15], [38, 8, 34, 15, "lineNumber"], [38, 18, 34, 15], [39, 8, 34, 15, "columnNumber"], [39, 20, 34, 15], [40, 6, 34, 15], [40, 13, 35, 12], [40, 14, 35, 13], [41, 4, 37, 2], [42, 4, 39, 2], [42, 24, 40, 4], [42, 28, 40, 4, "_jsxDevRuntime"], [42, 42, 40, 4], [42, 43, 40, 4, "jsxDEV"], [42, 49, 40, 4], [42, 51, 40, 5, "_View"], [42, 56, 40, 5], [42, 57, 40, 5, "default"], [42, 64, 40, 9], [43, 6, 40, 10, "style"], [43, 11, 40, 15], [43, 13, 40, 17, "styles"], [43, 19, 40, 23], [43, 20, 40, 24, "root"], [43, 24, 40, 29], [44, 6, 40, 29, "children"], [44, 14, 40, 29], [44, 30, 41, 6], [44, 34, 41, 6, "_jsxDevRuntime"], [44, 48, 41, 6], [44, 49, 41, 6, "jsxDEV"], [44, 55, 41, 6], [44, 57, 41, 7, "_LogBoxInspectorFooterButton"], [44, 85, 41, 7], [44, 86, 41, 7, "default"], [44, 93, 41, 34], [45, 8, 42, 8, "id"], [45, 10, 42, 10], [45, 12, 42, 11], [45, 42, 42, 41], [46, 8, 43, 8, "text"], [46, 12, 43, 12], [46, 14, 43, 13], [46, 23, 43, 22], [47, 8, 44, 8, "onPress"], [47, 15, 44, 15], [47, 17, 44, 17, "props"], [47, 22, 44, 22], [47, 23, 44, 23, "on<PERSON><PERSON><PERSON>"], [48, 6, 44, 33], [49, 8, 44, 33, "fileName"], [49, 16, 44, 33], [49, 18, 44, 33, "_jsxFileName"], [49, 30, 44, 33], [50, 8, 44, 33, "lineNumber"], [50, 18, 44, 33], [51, 8, 44, 33, "columnNumber"], [51, 20, 44, 33], [52, 6, 44, 33], [52, 13, 45, 7], [52, 14, 45, 8], [52, 29, 46, 6], [52, 33, 46, 6, "_jsxDevRuntime"], [52, 47, 46, 6], [52, 48, 46, 6, "jsxDEV"], [52, 54, 46, 6], [52, 56, 46, 7, "_LogBoxInspectorFooterButton"], [52, 84, 46, 7], [52, 85, 46, 7, "default"], [52, 92, 46, 34], [53, 8, 47, 8, "id"], [53, 10, 47, 10], [53, 12, 47, 11], [53, 43, 47, 42], [54, 8, 48, 8, "text"], [54, 12, 48, 12], [54, 14, 48, 13], [54, 24, 48, 23], [55, 8, 49, 8, "onPress"], [55, 15, 49, 15], [55, 17, 49, 17, "props"], [55, 22, 49, 22], [55, 23, 49, 23, "onMinimize"], [56, 6, 49, 34], [57, 8, 49, 34, "fileName"], [57, 16, 49, 34], [57, 18, 49, 34, "_jsxFileName"], [57, 30, 49, 34], [58, 8, 49, 34, "lineNumber"], [58, 18, 49, 34], [59, 8, 49, 34, "columnNumber"], [59, 20, 49, 34], [60, 6, 49, 34], [60, 13, 50, 7], [60, 14, 50, 8], [61, 4, 50, 8], [62, 6, 50, 8, "fileName"], [62, 14, 50, 8], [62, 16, 50, 8, "_jsxFileName"], [62, 28, 50, 8], [63, 6, 50, 8, "lineNumber"], [63, 16, 50, 8], [64, 6, 50, 8, "columnNumber"], [64, 18, 50, 8], [65, 4, 50, 8], [65, 11, 51, 10], [65, 12, 51, 11], [66, 2, 53, 0], [67, 2, 55, 0], [67, 6, 55, 6, "styles"], [67, 12, 55, 12], [67, 15, 55, 15, "StyleSheet"], [67, 34, 55, 25], [67, 35, 55, 26, "create"], [67, 41, 55, 32], [67, 42, 55, 33], [68, 4, 56, 2, "root"], [68, 8, 56, 6], [68, 10, 56, 8], [69, 6, 57, 4, "backgroundColor"], [69, 21, 57, 19], [69, 23, 57, 21, "LogBoxStyle"], [69, 34, 57, 32], [69, 35, 57, 33, "getBackgroundColor"], [69, 53, 57, 51], [69, 54, 57, 52], [69, 55, 57, 53], [69, 56, 57, 54], [70, 6, 58, 4, "shadowColor"], [70, 17, 58, 15], [70, 19, 58, 17], [70, 25, 58, 23], [71, 6, 59, 4, "shadowOffset"], [71, 18, 59, 16], [71, 20, 59, 18], [72, 8, 59, 19, "width"], [72, 13, 59, 24], [72, 15, 59, 26], [72, 16, 59, 27], [73, 8, 59, 29, "height"], [73, 14, 59, 35], [73, 16, 59, 37], [73, 17, 59, 38], [74, 6, 59, 39], [74, 7, 59, 40], [75, 6, 60, 4, "shadowRadius"], [75, 18, 60, 16], [75, 20, 60, 18], [75, 21, 60, 19], [76, 6, 61, 4, "shadowOpacity"], [76, 19, 61, 17], [76, 21, 61, 19], [76, 24, 61, 22], [77, 6, 62, 4, "flexDirection"], [77, 19, 62, 17], [77, 21, 62, 19], [78, 4, 63, 2], [78, 5, 63, 3], [79, 4, 64, 2, "button"], [79, 10, 64, 8], [79, 12, 64, 10], [80, 6, 65, 4, "flex"], [80, 10, 65, 8], [80, 12, 65, 10], [81, 4, 66, 2], [81, 5, 66, 3], [82, 4, 67, 2, "syntaxErrorText"], [82, 19, 67, 17], [82, 21, 67, 19], [83, 6, 68, 4, "textAlign"], [83, 15, 68, 13], [83, 17, 68, 15], [83, 25, 68, 23], [84, 6, 69, 4, "width"], [84, 11, 69, 9], [84, 13, 69, 11], [84, 19, 69, 17], [85, 6, 70, 4, "height"], [85, 12, 70, 10], [85, 14, 70, 12], [85, 16, 70, 14], [86, 6, 71, 4, "fontSize"], [86, 14, 71, 12], [86, 16, 71, 14], [86, 18, 71, 16], [87, 6, 72, 4, "lineHeight"], [87, 16, 72, 14], [87, 18, 72, 16], [87, 20, 72, 18], [88, 6, 73, 4, "paddingTop"], [88, 16, 73, 14], [88, 18, 73, 16], [88, 20, 73, 18], [89, 6, 74, 4, "paddingBottom"], [89, 19, 74, 17], [89, 21, 74, 19], [89, 23, 74, 21], [90, 6, 75, 4, "fontStyle"], [90, 15, 75, 13], [90, 17, 75, 15], [90, 25, 75, 23], [91, 6, 76, 4, "color"], [91, 11, 76, 9], [91, 13, 76, 11, "LogBoxStyle"], [91, 24, 76, 22], [91, 25, 76, 23, "getTextColor"], [91, 37, 76, 35], [91, 38, 76, 36], [91, 41, 76, 39], [92, 4, 77, 2], [93, 2, 78, 0], [93, 3, 78, 1], [93, 4, 78, 2], [94, 0, 78, 3], [94, 3]], "functionMap": {"names": ["<global>", "LogBoxInspectorFooter"], "mappings": "AAA;eCyB;CD2B"}}, "type": "js/module"}]}