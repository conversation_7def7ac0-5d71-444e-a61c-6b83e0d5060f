{"dependencies": [{"name": "../../../../Libraries/TurboModule/TurboModuleRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 93}}], "key": "H+9Pk6sLVUPsBv6YXnwcNYMfH5g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var TurboModuleRegistry = _interopRequireWildcard(require(_dependencyMap[0], \"../../../../Libraries/TurboModule/TurboModuleRegistry\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var NativeModule = TurboModuleRegistry.get('BlobModule');\n  var constants = null;\n  var NativeBlobModule = null;\n  if (NativeModule != null) {\n    NativeBlobModule = {\n      getConstants() {\n        if (constants == null) {\n          constants = NativeModule.getConstants();\n        }\n        return constants;\n      },\n      addNetworkingHandler() {\n        NativeModule.addNetworkingHandler();\n      },\n      addWebSocketHandler(id) {\n        NativeModule.addWebSocketHandler(id);\n      },\n      removeWebSocketHandler(id) {\n        NativeModule.removeWebSocketHandler(id);\n      },\n      sendOverSocket(blob, socketID) {\n        NativeModule.sendOverSocket(blob, socketID);\n      },\n      createFromParts(parts, withId) {\n        NativeModule.createFromParts(parts, withId);\n      },\n      release(blobId) {\n        NativeModule.release(blobId);\n      }\n    };\n  }\n  var _default = exports.default = NativeBlobModule;\n});", "lineCount": 40, "map": [[6, 2, 13, 0], [6, 6, 13, 0, "TurboModuleRegistry"], [6, 25, 13, 0], [6, 28, 13, 0, "_interopRequireWildcard"], [6, 51, 13, 0], [6, 52, 13, 0, "require"], [6, 59, 13, 0], [6, 60, 13, 0, "_dependencyMap"], [6, 74, 13, 0], [7, 2, 13, 93], [7, 11, 13, 93, "_interopRequireWildcard"], [7, 35, 13, 93, "e"], [7, 36, 13, 93], [7, 38, 13, 93, "t"], [7, 39, 13, 93], [7, 68, 13, 93, "WeakMap"], [7, 75, 13, 93], [7, 81, 13, 93, "r"], [7, 82, 13, 93], [7, 89, 13, 93, "WeakMap"], [7, 96, 13, 93], [7, 100, 13, 93, "n"], [7, 101, 13, 93], [7, 108, 13, 93, "WeakMap"], [7, 115, 13, 93], [7, 127, 13, 93, "_interopRequireWildcard"], [7, 150, 13, 93], [7, 162, 13, 93, "_interopRequireWildcard"], [7, 163, 13, 93, "e"], [7, 164, 13, 93], [7, 166, 13, 93, "t"], [7, 167, 13, 93], [7, 176, 13, 93, "t"], [7, 177, 13, 93], [7, 181, 13, 93, "e"], [7, 182, 13, 93], [7, 186, 13, 93, "e"], [7, 187, 13, 93], [7, 188, 13, 93, "__esModule"], [7, 198, 13, 93], [7, 207, 13, 93, "e"], [7, 208, 13, 93], [7, 214, 13, 93, "o"], [7, 215, 13, 93], [7, 217, 13, 93, "i"], [7, 218, 13, 93], [7, 220, 13, 93, "f"], [7, 221, 13, 93], [7, 226, 13, 93, "__proto__"], [7, 235, 13, 93], [7, 243, 13, 93, "default"], [7, 250, 13, 93], [7, 252, 13, 93, "e"], [7, 253, 13, 93], [7, 270, 13, 93, "e"], [7, 271, 13, 93], [7, 294, 13, 93, "e"], [7, 295, 13, 93], [7, 320, 13, 93, "e"], [7, 321, 13, 93], [7, 330, 13, 93, "f"], [7, 331, 13, 93], [7, 337, 13, 93, "o"], [7, 338, 13, 93], [7, 341, 13, 93, "t"], [7, 342, 13, 93], [7, 345, 13, 93, "n"], [7, 346, 13, 93], [7, 349, 13, 93, "r"], [7, 350, 13, 93], [7, 358, 13, 93, "o"], [7, 359, 13, 93], [7, 360, 13, 93, "has"], [7, 363, 13, 93], [7, 364, 13, 93, "e"], [7, 365, 13, 93], [7, 375, 13, 93, "o"], [7, 376, 13, 93], [7, 377, 13, 93, "get"], [7, 380, 13, 93], [7, 381, 13, 93, "e"], [7, 382, 13, 93], [7, 385, 13, 93, "o"], [7, 386, 13, 93], [7, 387, 13, 93, "set"], [7, 390, 13, 93], [7, 391, 13, 93, "e"], [7, 392, 13, 93], [7, 394, 13, 93, "f"], [7, 395, 13, 93], [7, 409, 13, 93, "_t"], [7, 411, 13, 93], [7, 415, 13, 93, "e"], [7, 416, 13, 93], [7, 432, 13, 93, "_t"], [7, 434, 13, 93], [7, 441, 13, 93, "hasOwnProperty"], [7, 455, 13, 93], [7, 456, 13, 93, "call"], [7, 460, 13, 93], [7, 461, 13, 93, "e"], [7, 462, 13, 93], [7, 464, 13, 93, "_t"], [7, 466, 13, 93], [7, 473, 13, 93, "i"], [7, 474, 13, 93], [7, 478, 13, 93, "o"], [7, 479, 13, 93], [7, 482, 13, 93, "Object"], [7, 488, 13, 93], [7, 489, 13, 93, "defineProperty"], [7, 503, 13, 93], [7, 508, 13, 93, "Object"], [7, 514, 13, 93], [7, 515, 13, 93, "getOwnPropertyDescriptor"], [7, 539, 13, 93], [7, 540, 13, 93, "e"], [7, 541, 13, 93], [7, 543, 13, 93, "_t"], [7, 545, 13, 93], [7, 552, 13, 93, "i"], [7, 553, 13, 93], [7, 554, 13, 93, "get"], [7, 557, 13, 93], [7, 561, 13, 93, "i"], [7, 562, 13, 93], [7, 563, 13, 93, "set"], [7, 566, 13, 93], [7, 570, 13, 93, "o"], [7, 571, 13, 93], [7, 572, 13, 93, "f"], [7, 573, 13, 93], [7, 575, 13, 93, "_t"], [7, 577, 13, 93], [7, 579, 13, 93, "i"], [7, 580, 13, 93], [7, 584, 13, 93, "f"], [7, 585, 13, 93], [7, 586, 13, 93, "_t"], [7, 588, 13, 93], [7, 592, 13, 93, "e"], [7, 593, 13, 93], [7, 594, 13, 93, "_t"], [7, 596, 13, 93], [7, 607, 13, 93, "f"], [7, 608, 13, 93], [7, 613, 13, 93, "e"], [7, 614, 13, 93], [7, 616, 13, 93, "t"], [7, 617, 13, 93], [8, 2, 27, 0], [8, 6, 27, 6, "NativeModule"], [8, 18, 27, 18], [8, 21, 27, 21, "TurboModuleRegistry"], [8, 40, 27, 40], [8, 41, 27, 41, "get"], [8, 44, 27, 44], [8, 45, 27, 51], [8, 57, 27, 63], [8, 58, 27, 64], [9, 2, 29, 0], [9, 6, 29, 4, "constants"], [9, 15, 29, 13], [9, 18, 29, 16], [9, 22, 29, 20], [10, 2, 30, 0], [10, 6, 30, 4, "NativeBlobModule"], [10, 22, 30, 20], [10, 25, 30, 23], [10, 29, 30, 27], [11, 2, 32, 0], [11, 6, 32, 4, "NativeModule"], [11, 18, 32, 16], [11, 22, 32, 20], [11, 26, 32, 24], [11, 28, 32, 26], [12, 4, 33, 2, "NativeBlobModule"], [12, 20, 33, 18], [12, 23, 33, 21], [13, 6, 34, 4, "getConstants"], [13, 18, 34, 16, "getConstants"], [13, 19, 34, 16], [13, 21, 34, 30], [14, 8, 35, 6], [14, 12, 35, 10, "constants"], [14, 21, 35, 19], [14, 25, 35, 23], [14, 29, 35, 27], [14, 31, 35, 29], [15, 10, 36, 8, "constants"], [15, 19, 36, 17], [15, 22, 36, 20, "NativeModule"], [15, 34, 36, 32], [15, 35, 36, 33, "getConstants"], [15, 47, 36, 45], [15, 48, 36, 46], [15, 49, 36, 47], [16, 8, 37, 6], [17, 8, 38, 6], [17, 15, 38, 13, "constants"], [17, 24, 38, 22], [18, 6, 39, 4], [18, 7, 39, 5], [19, 6, 40, 4, "addNetworkingHandler"], [19, 26, 40, 24, "addNetworkingHandler"], [19, 27, 40, 24], [19, 29, 40, 33], [20, 8, 41, 6, "NativeModule"], [20, 20, 41, 18], [20, 21, 41, 19, "addNetworkingHandler"], [20, 41, 41, 39], [20, 42, 41, 40], [20, 43, 41, 41], [21, 6, 42, 4], [21, 7, 42, 5], [22, 6, 43, 4, "addWebSocketHandler"], [22, 25, 43, 23, "addWebSocketHandler"], [22, 26, 43, 24, "id"], [22, 28, 43, 34], [22, 30, 43, 42], [23, 8, 44, 6, "NativeModule"], [23, 20, 44, 18], [23, 21, 44, 19, "addWebSocketHandler"], [23, 40, 44, 38], [23, 41, 44, 39, "id"], [23, 43, 44, 41], [23, 44, 44, 42], [24, 6, 45, 4], [24, 7, 45, 5], [25, 6, 46, 4, "removeWebSocketHandler"], [25, 28, 46, 26, "removeWebSocketHandler"], [25, 29, 46, 27, "id"], [25, 31, 46, 37], [25, 33, 46, 45], [26, 8, 47, 6, "NativeModule"], [26, 20, 47, 18], [26, 21, 47, 19, "removeWebSocketHandler"], [26, 43, 47, 41], [26, 44, 47, 42, "id"], [26, 46, 47, 44], [26, 47, 47, 45], [27, 6, 48, 4], [27, 7, 48, 5], [28, 6, 49, 4, "sendOverSocket"], [28, 20, 49, 18, "sendOverSocket"], [28, 21, 49, 19, "blob"], [28, 25, 49, 31], [28, 27, 49, 33, "socketID"], [28, 35, 49, 49], [28, 37, 49, 57], [29, 8, 50, 6, "NativeModule"], [29, 20, 50, 18], [29, 21, 50, 19, "sendOverSocket"], [29, 35, 50, 33], [29, 36, 50, 34, "blob"], [29, 40, 50, 38], [29, 42, 50, 40, "socketID"], [29, 50, 50, 48], [29, 51, 50, 49], [30, 6, 51, 4], [30, 7, 51, 5], [31, 6, 52, 4, "createFromParts"], [31, 21, 52, 19, "createFromParts"], [31, 22, 52, 20, "parts"], [31, 27, 52, 40], [31, 29, 52, 42, "withId"], [31, 35, 52, 56], [31, 37, 52, 64], [32, 8, 53, 6, "NativeModule"], [32, 20, 53, 18], [32, 21, 53, 19, "createFromParts"], [32, 36, 53, 34], [32, 37, 53, 35, "parts"], [32, 42, 53, 40], [32, 44, 53, 42, "withId"], [32, 50, 53, 48], [32, 51, 53, 49], [33, 6, 54, 4], [33, 7, 54, 5], [34, 6, 55, 4, "release"], [34, 13, 55, 11, "release"], [34, 14, 55, 12, "blobId"], [34, 20, 55, 26], [34, 22, 55, 34], [35, 8, 56, 6, "NativeModule"], [35, 20, 56, 18], [35, 21, 56, 19, "release"], [35, 28, 56, 26], [35, 29, 56, 27, "blobId"], [35, 35, 56, 33], [35, 36, 56, 34], [36, 6, 57, 4], [37, 4, 58, 2], [37, 5, 58, 3], [38, 2, 59, 0], [39, 2, 59, 1], [39, 6, 59, 1, "_default"], [39, 14, 59, 1], [39, 17, 59, 1, "exports"], [39, 24, 59, 1], [39, 25, 59, 1, "default"], [39, 32, 59, 1], [39, 35, 61, 16, "NativeBlobModule"], [39, 51, 61, 32], [40, 0, 61, 32], [40, 3]], "functionMap": {"names": ["<global>", "getConstants", "addNetworkingHandler", "addWebSocketHandler", "removeWebSocketHandler", "sendOverSocket", "createFromParts", "release"], "mappings": "AAA;ICiC;KDK;IEC;KFE;IGC;KHE;IIC;KJE;IKC;KLE;IMC;KNE;IOC;KPE"}}, "type": "js/module"}]}