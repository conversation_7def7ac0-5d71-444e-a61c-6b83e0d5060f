{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../Core/Devtools/openFileInEditor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 68}}], "key": "3vH1y5vrRpr8TQTBUBKQt4U0X5I=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "./LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 42}}], "key": "M6ofQu070ZUTf+Oq+Zz+7FQEgjs=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorSection", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 62}}], "key": "psfwCNco8+nKb+3u3V4A+OhHW2E=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorSourceMapStatus", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 78}}], "key": "8vwWm4m1IX/DlbOBlKQxLJRMibc=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorStackFrame", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 68}}], "key": "SkZLu5MzYdhQB4fazEvDhLWt4B8=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  exports.getCollapseMessage = getCollapseMessage;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../../Components/View/View\"));\n  var _openFileInEditor = _interopRequireDefault(require(_dependencyMap[3], \"../../Core/Devtools/openFileInEditor\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[5], \"../../Text/Text\"));\n  var _LogBoxButton = _interopRequireDefault(require(_dependencyMap[6], \"./LogBoxButton\"));\n  var _LogBoxInspectorSection = _interopRequireDefault(require(_dependencyMap[7], \"./LogBoxInspectorSection\"));\n  var _LogBoxInspectorSourceMapStatus = _interopRequireDefault(require(_dependencyMap[8], \"./LogBoxInspectorSourceMapStatus\"));\n  var _LogBoxInspectorStackFrame = _interopRequireDefault(require(_dependencyMap[9], \"./LogBoxInspectorStackFrame\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[10], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[11], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[12], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native\\\\Libraries\\\\LogBox\\\\UI\\\\LogBoxInspectorStackFrames.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function getCollapseMessage(stackFrames, collapsed) {\n    if (stackFrames.length === 0) {\n      return 'No frames to show';\n    }\n    var collapsedCount = stackFrames.reduce((count, _ref) => {\n      var collapse = _ref.collapse;\n      if (collapse === true) {\n        return count + 1;\n      }\n      return count;\n    }, 0);\n    if (collapsedCount === 0) {\n      return 'Showing all frames';\n    }\n    var framePlural = `frame${collapsedCount > 1 ? 's' : ''}`;\n    if (collapsedCount === stackFrames.length) {\n      return collapsed ? `See${collapsedCount > 1 ? ' all ' : ' '}${collapsedCount} collapsed ${framePlural}` : `Collapse${collapsedCount > 1 ? ' all ' : ' '}${collapsedCount} ${framePlural}`;\n    } else {\n      return collapsed ? `See ${collapsedCount} more ${framePlural}` : `Collapse ${collapsedCount} ${framePlural}`;\n    }\n  }\n  function LogBoxInspectorStackFrames(props) {\n    var _React$useState = React.useState(() => {\n        return props.log.getAvailableStack().some(_ref2 => {\n          var collapse = _ref2.collapse;\n          return !collapse;\n        });\n      }),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      collapsed = _React$useState2[0],\n      setCollapsed = _React$useState2[1];\n    function getStackList() {\n      if (collapsed === true) {\n        return props.log.getAvailableStack().filter(_ref3 => {\n          var collapse = _ref3.collapse;\n          return !collapse;\n        });\n      } else {\n        return props.log.getAvailableStack();\n      }\n    }\n    if (props.log.getAvailableStack().length === 0) {\n      return null;\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorSection.default, {\n      heading: \"Call Stack\",\n      action: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorSourceMapStatus.default, {\n        onPress: props.log.symbolicated.status === 'FAILED' ? props.onRetry : null,\n        status: props.log.symbolicated.status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this),\n      children: [props.log.symbolicated.status !== 'COMPLETE' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: stackStyles.hintBox,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: stackStyles.hintText,\n          children: \"This call stack is not symbolicated. Some features are unavailable such as viewing the function name or tapping to open files.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(StackFrameList, {\n        list: getStackList(),\n        status: props.log.symbolicated.status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(StackFrameFooter, {\n        onPress: () => setCollapsed(!collapsed),\n        message: getCollapseMessage(props.log.getAvailableStack(), collapsed)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 5\n    }, this);\n  }\n  function StackFrameList(props) {\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: props.list.map((frame, index) => {\n        var file = frame.file,\n          lineNumber = frame.lineNumber;\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxInspectorStackFrame.default, {\n          frame: frame,\n          onPress: props.status === 'COMPLETE' && file != null && lineNumber != null ? () => (0, _openFileInEditor.default)(file, lineNumber) : null\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false);\n  }\n  function StackFrameFooter(props) {\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: stackStyles.collapseContainer,\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LogBoxButton.default, {\n        backgroundColor: {\n          default: 'transparent',\n          pressed: LogBoxStyle.getBackgroundColor(1)\n        },\n        onPress: props.onPress,\n        style: stackStyles.collapseButton,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: stackStyles.collapse,\n          children: props.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 5\n    }, this);\n  }\n  var stackStyles = _StyleSheet.default.create({\n    section: {\n      marginTop: 15\n    },\n    heading: {\n      alignItems: 'center',\n      flexDirection: 'row',\n      paddingHorizontal: 12,\n      marginBottom: 10\n    },\n    headingText: {\n      color: LogBoxStyle.getTextColor(1),\n      flex: 1,\n      fontSize: 20,\n      fontWeight: '600',\n      includeFontPadding: false,\n      lineHeight: 20\n    },\n    body: {\n      paddingBottom: 10\n    },\n    bodyText: {\n      color: LogBoxStyle.getTextColor(1),\n      fontSize: 14,\n      includeFontPadding: false,\n      lineHeight: 18,\n      fontWeight: '500',\n      paddingHorizontal: 27\n    },\n    hintText: {\n      color: LogBoxStyle.getTextColor(0.7),\n      fontSize: 13,\n      includeFontPadding: false,\n      lineHeight: 18,\n      fontWeight: '400',\n      marginHorizontal: 10\n    },\n    hintBox: {\n      backgroundColor: LogBoxStyle.getBackgroundColor(),\n      marginHorizontal: 10,\n      paddingHorizontal: 5,\n      paddingVertical: 10,\n      borderRadius: 5,\n      marginBottom: 5\n    },\n    collapseContainer: {\n      marginLeft: 15,\n      flexDirection: 'row'\n    },\n    collapseButton: {\n      borderRadius: 5\n    },\n    collapse: {\n      color: LogBoxStyle.getTextColor(0.7),\n      fontSize: 12,\n      fontWeight: '300',\n      lineHeight: 20,\n      marginTop: 0,\n      paddingHorizontal: 10,\n      paddingVertical: 5\n    }\n  });\n  var _default = exports.default = LogBoxInspectorStackFrames;\n});", "lineCount": 219, "map": [[9, 2, 15, 0], [9, 6, 15, 0, "_View"], [9, 11, 15, 0], [9, 14, 15, 0, "_interopRequireDefault"], [9, 36, 15, 0], [9, 37, 15, 0, "require"], [9, 44, 15, 0], [9, 45, 15, 0, "_dependencyMap"], [9, 59, 15, 0], [10, 2, 16, 0], [10, 6, 16, 0, "_openFileInEditor"], [10, 23, 16, 0], [10, 26, 16, 0, "_interopRequireDefault"], [10, 48, 16, 0], [10, 49, 16, 0, "require"], [10, 56, 16, 0], [10, 57, 16, 0, "_dependencyMap"], [10, 71, 16, 0], [11, 2, 17, 0], [11, 6, 17, 0, "_StyleSheet"], [11, 17, 17, 0], [11, 20, 17, 0, "_interopRequireDefault"], [11, 42, 17, 0], [11, 43, 17, 0, "require"], [11, 50, 17, 0], [11, 51, 17, 0, "_dependencyMap"], [11, 65, 17, 0], [12, 2, 18, 0], [12, 6, 18, 0, "_Text"], [12, 11, 18, 0], [12, 14, 18, 0, "_interopRequireDefault"], [12, 36, 18, 0], [12, 37, 18, 0, "require"], [12, 44, 18, 0], [12, 45, 18, 0, "_dependencyMap"], [12, 59, 18, 0], [13, 2, 19, 0], [13, 6, 19, 0, "_LogBoxButton"], [13, 19, 19, 0], [13, 22, 19, 0, "_interopRequireDefault"], [13, 44, 19, 0], [13, 45, 19, 0, "require"], [13, 52, 19, 0], [13, 53, 19, 0, "_dependencyMap"], [13, 67, 19, 0], [14, 2, 20, 0], [14, 6, 20, 0, "_LogBoxInspectorSection"], [14, 29, 20, 0], [14, 32, 20, 0, "_interopRequireDefault"], [14, 54, 20, 0], [14, 55, 20, 0, "require"], [14, 62, 20, 0], [14, 63, 20, 0, "_dependencyMap"], [14, 77, 20, 0], [15, 2, 21, 0], [15, 6, 21, 0, "_LogBoxInspectorSourceMapStatus"], [15, 37, 21, 0], [15, 40, 21, 0, "_interopRequireDefault"], [15, 62, 21, 0], [15, 63, 21, 0, "require"], [15, 70, 21, 0], [15, 71, 21, 0, "_dependencyMap"], [15, 85, 21, 0], [16, 2, 22, 0], [16, 6, 22, 0, "_LogBoxInspectorStackFrame"], [16, 32, 22, 0], [16, 35, 22, 0, "_interopRequireDefault"], [16, 57, 22, 0], [16, 58, 22, 0, "require"], [16, 65, 22, 0], [16, 66, 22, 0, "_dependencyMap"], [16, 80, 22, 0], [17, 2, 23, 0], [17, 6, 23, 0, "LogBoxStyle"], [17, 17, 23, 0], [17, 20, 23, 0, "_interopRequireWildcard"], [17, 43, 23, 0], [17, 44, 23, 0, "require"], [17, 51, 23, 0], [17, 52, 23, 0, "_dependencyMap"], [17, 66, 23, 0], [18, 2, 24, 0], [18, 6, 24, 0, "React"], [18, 11, 24, 0], [18, 14, 24, 0, "_interopRequireWildcard"], [18, 37, 24, 0], [18, 38, 24, 0, "require"], [18, 45, 24, 0], [18, 46, 24, 0, "_dependencyMap"], [18, 60, 24, 0], [19, 2, 24, 31], [19, 6, 24, 31, "_jsxDevRuntime"], [19, 20, 24, 31], [19, 23, 24, 31, "require"], [19, 30, 24, 31], [19, 31, 24, 31, "_dependencyMap"], [19, 45, 24, 31], [20, 2, 24, 31], [20, 6, 24, 31, "_jsxFileName"], [20, 18, 24, 31], [21, 2, 24, 31], [21, 11, 24, 31, "_interopRequireWildcard"], [21, 35, 24, 31, "e"], [21, 36, 24, 31], [21, 38, 24, 31, "t"], [21, 39, 24, 31], [21, 68, 24, 31, "WeakMap"], [21, 75, 24, 31], [21, 81, 24, 31, "r"], [21, 82, 24, 31], [21, 89, 24, 31, "WeakMap"], [21, 96, 24, 31], [21, 100, 24, 31, "n"], [21, 101, 24, 31], [21, 108, 24, 31, "WeakMap"], [21, 115, 24, 31], [21, 127, 24, 31, "_interopRequireWildcard"], [21, 150, 24, 31], [21, 162, 24, 31, "_interopRequireWildcard"], [21, 163, 24, 31, "e"], [21, 164, 24, 31], [21, 166, 24, 31, "t"], [21, 167, 24, 31], [21, 176, 24, 31, "t"], [21, 177, 24, 31], [21, 181, 24, 31, "e"], [21, 182, 24, 31], [21, 186, 24, 31, "e"], [21, 187, 24, 31], [21, 188, 24, 31, "__esModule"], [21, 198, 24, 31], [21, 207, 24, 31, "e"], [21, 208, 24, 31], [21, 214, 24, 31, "o"], [21, 215, 24, 31], [21, 217, 24, 31, "i"], [21, 218, 24, 31], [21, 220, 24, 31, "f"], [21, 221, 24, 31], [21, 226, 24, 31, "__proto__"], [21, 235, 24, 31], [21, 243, 24, 31, "default"], [21, 250, 24, 31], [21, 252, 24, 31, "e"], [21, 253, 24, 31], [21, 270, 24, 31, "e"], [21, 271, 24, 31], [21, 294, 24, 31, "e"], [21, 295, 24, 31], [21, 320, 24, 31, "e"], [21, 321, 24, 31], [21, 330, 24, 31, "f"], [21, 331, 24, 31], [21, 337, 24, 31, "o"], [21, 338, 24, 31], [21, 341, 24, 31, "t"], [21, 342, 24, 31], [21, 345, 24, 31, "n"], [21, 346, 24, 31], [21, 349, 24, 31, "r"], [21, 350, 24, 31], [21, 358, 24, 31, "o"], [21, 359, 24, 31], [21, 360, 24, 31, "has"], [21, 363, 24, 31], [21, 364, 24, 31, "e"], [21, 365, 24, 31], [21, 375, 24, 31, "o"], [21, 376, 24, 31], [21, 377, 24, 31, "get"], [21, 380, 24, 31], [21, 381, 24, 31, "e"], [21, 382, 24, 31], [21, 385, 24, 31, "o"], [21, 386, 24, 31], [21, 387, 24, 31, "set"], [21, 390, 24, 31], [21, 391, 24, 31, "e"], [21, 392, 24, 31], [21, 394, 24, 31, "f"], [21, 395, 24, 31], [21, 409, 24, 31, "_t"], [21, 411, 24, 31], [21, 415, 24, 31, "e"], [21, 416, 24, 31], [21, 432, 24, 31, "_t"], [21, 434, 24, 31], [21, 441, 24, 31, "hasOwnProperty"], [21, 455, 24, 31], [21, 456, 24, 31, "call"], [21, 460, 24, 31], [21, 461, 24, 31, "e"], [21, 462, 24, 31], [21, 464, 24, 31, "_t"], [21, 466, 24, 31], [21, 473, 24, 31, "i"], [21, 474, 24, 31], [21, 478, 24, 31, "o"], [21, 479, 24, 31], [21, 482, 24, 31, "Object"], [21, 488, 24, 31], [21, 489, 24, 31, "defineProperty"], [21, 503, 24, 31], [21, 508, 24, 31, "Object"], [21, 514, 24, 31], [21, 515, 24, 31, "getOwnPropertyDescriptor"], [21, 539, 24, 31], [21, 540, 24, 31, "e"], [21, 541, 24, 31], [21, 543, 24, 31, "_t"], [21, 545, 24, 31], [21, 552, 24, 31, "i"], [21, 553, 24, 31], [21, 554, 24, 31, "get"], [21, 557, 24, 31], [21, 561, 24, 31, "i"], [21, 562, 24, 31], [21, 563, 24, 31, "set"], [21, 566, 24, 31], [21, 570, 24, 31, "o"], [21, 571, 24, 31], [21, 572, 24, 31, "f"], [21, 573, 24, 31], [21, 575, 24, 31, "_t"], [21, 577, 24, 31], [21, 579, 24, 31, "i"], [21, 580, 24, 31], [21, 584, 24, 31, "f"], [21, 585, 24, 31], [21, 586, 24, 31, "_t"], [21, 588, 24, 31], [21, 592, 24, 31, "e"], [21, 593, 24, 31], [21, 594, 24, 31, "_t"], [21, 596, 24, 31], [21, 607, 24, 31, "f"], [21, 608, 24, 31], [21, 613, 24, 31, "e"], [21, 614, 24, 31], [21, 616, 24, 31, "t"], [21, 617, 24, 31], [22, 2, 31, 7], [22, 11, 31, 16, "getCollapseMessage"], [22, 29, 31, 34, "getCollapseMessage"], [22, 30, 32, 2, "stackFrames"], [22, 41, 32, 20], [22, 43, 33, 2, "collapsed"], [22, 52, 33, 20], [22, 54, 34, 10], [23, 4, 35, 2], [23, 8, 35, 6, "stackFrames"], [23, 19, 35, 17], [23, 20, 35, 18, "length"], [23, 26, 35, 24], [23, 31, 35, 29], [23, 32, 35, 30], [23, 34, 35, 32], [24, 6, 36, 4], [24, 13, 36, 11], [24, 32, 36, 30], [25, 4, 37, 2], [26, 4, 39, 2], [26, 8, 39, 8, "collapsedCount"], [26, 22, 39, 22], [26, 25, 39, 25, "stackFrames"], [26, 36, 39, 36], [26, 37, 39, 37, "reduce"], [26, 43, 39, 43], [26, 44, 39, 44], [26, 45, 39, 45, "count"], [26, 50, 39, 50], [26, 52, 39, 50, "_ref"], [26, 56, 39, 50], [26, 61, 39, 67], [27, 6, 39, 67], [27, 10, 39, 53, "collapse"], [27, 18, 39, 61], [27, 21, 39, 61, "_ref"], [27, 25, 39, 61], [27, 26, 39, 53, "collapse"], [27, 34, 39, 61], [28, 6, 40, 4], [28, 10, 40, 8, "collapse"], [28, 18, 40, 16], [28, 23, 40, 21], [28, 27, 40, 25], [28, 29, 40, 27], [29, 8, 41, 6], [29, 15, 41, 13, "count"], [29, 20, 41, 18], [29, 23, 41, 21], [29, 24, 41, 22], [30, 6, 42, 4], [31, 6, 44, 4], [31, 13, 44, 11, "count"], [31, 18, 44, 16], [32, 4, 45, 2], [32, 5, 45, 3], [32, 7, 45, 5], [32, 8, 45, 6], [32, 9, 45, 7], [33, 4, 47, 2], [33, 8, 47, 6, "collapsedCount"], [33, 22, 47, 20], [33, 27, 47, 25], [33, 28, 47, 26], [33, 30, 47, 28], [34, 6, 48, 4], [34, 13, 48, 11], [34, 33, 48, 31], [35, 4, 49, 2], [36, 4, 51, 2], [36, 8, 51, 8, "framePlural"], [36, 19, 51, 19], [36, 22, 51, 22], [36, 30, 51, 30, "collapsedCount"], [36, 44, 51, 44], [36, 47, 51, 47], [36, 48, 51, 48], [36, 51, 51, 51], [36, 54, 51, 54], [36, 57, 51, 57], [36, 59, 51, 59], [36, 61, 51, 61], [37, 4, 52, 2], [37, 8, 52, 6, "collapsedCount"], [37, 22, 52, 20], [37, 27, 52, 25, "stackFrames"], [37, 38, 52, 36], [37, 39, 52, 37, "length"], [37, 45, 52, 43], [37, 47, 52, 45], [38, 6, 53, 4], [38, 13, 53, 11, "collapsed"], [38, 22, 53, 20], [38, 25, 54, 8], [38, 31, 55, 10, "collapsedCount"], [38, 45, 55, 24], [38, 48, 55, 27], [38, 49, 55, 28], [38, 52, 55, 31], [38, 59, 55, 38], [38, 62, 55, 41], [38, 65, 55, 44], [38, 68, 56, 11, "collapsedCount"], [38, 82, 56, 25], [38, 96, 56, 39, "framePlural"], [38, 107, 56, 50], [38, 109, 56, 52], [38, 112, 57, 8], [38, 123, 58, 10, "collapsedCount"], [38, 137, 58, 24], [38, 140, 58, 27], [38, 141, 58, 28], [38, 144, 58, 31], [38, 151, 58, 38], [38, 154, 58, 41], [38, 157, 58, 44], [38, 160, 59, 11, "collapsedCount"], [38, 174, 59, 25], [38, 178, 59, 29, "framePlural"], [38, 189, 59, 40], [38, 191, 59, 42], [39, 4, 60, 2], [39, 5, 60, 3], [39, 11, 60, 9], [40, 6, 61, 4], [40, 13, 61, 11, "collapsed"], [40, 22, 61, 20], [40, 25, 62, 8], [40, 32, 62, 15, "collapsedCount"], [40, 46, 62, 29], [40, 55, 62, 38, "framePlural"], [40, 66, 62, 49], [40, 68, 62, 51], [40, 71, 63, 8], [40, 83, 63, 20, "collapsedCount"], [40, 97, 63, 34], [40, 101, 63, 38, "framePlural"], [40, 112, 63, 49], [40, 114, 63, 51], [41, 4, 64, 2], [42, 2, 65, 0], [43, 2, 67, 0], [43, 11, 67, 9, "LogBoxInspectorStackFrames"], [43, 37, 67, 35, "LogBoxInspectorStackFrames"], [43, 38, 67, 36, "props"], [43, 43, 67, 48], [43, 45, 67, 62], [44, 4, 68, 2], [44, 8, 68, 2, "_React$useState"], [44, 23, 68, 2], [44, 26, 68, 36, "React"], [44, 31, 68, 41], [44, 32, 68, 42, "useState"], [44, 40, 68, 50], [44, 41, 68, 51], [44, 47, 68, 57], [45, 8, 70, 4], [45, 15, 70, 11, "props"], [45, 20, 70, 16], [45, 21, 70, 17, "log"], [45, 24, 70, 20], [45, 25, 70, 21, "getAvailableStack"], [45, 42, 70, 38], [45, 43, 70, 39], [45, 44, 70, 40], [45, 45, 70, 41, "some"], [45, 49, 70, 45], [45, 50, 70, 46, "_ref2"], [45, 55, 70, 46], [46, 10, 70, 46], [46, 14, 70, 48, "collapse"], [46, 22, 70, 56], [46, 25, 70, 56, "_ref2"], [46, 30, 70, 56], [46, 31, 70, 48, "collapse"], [46, 39, 70, 56], [47, 10, 70, 56], [47, 17, 70, 62], [47, 18, 70, 63, "collapse"], [47, 26, 70, 71], [48, 8, 70, 71], [48, 10, 70, 72], [49, 6, 71, 2], [49, 7, 71, 3], [49, 8, 71, 4], [50, 6, 71, 4, "_React$useState2"], [50, 22, 71, 4], [50, 29, 71, 4, "_slicedToArray2"], [50, 44, 71, 4], [50, 45, 71, 4, "default"], [50, 52, 71, 4], [50, 54, 71, 4, "_React$useState"], [50, 69, 71, 4], [51, 6, 68, 9, "collapsed"], [51, 15, 68, 18], [51, 18, 68, 18, "_React$useState2"], [51, 34, 68, 18], [52, 6, 68, 20, "setCollapsed"], [52, 18, 68, 32], [52, 21, 68, 32, "_React$useState2"], [52, 37, 68, 32], [53, 4, 73, 2], [53, 13, 73, 11, "getStackList"], [53, 25, 73, 23, "getStackList"], [53, 26, 73, 23], [53, 28, 73, 26], [54, 6, 74, 4], [54, 10, 74, 8, "collapsed"], [54, 19, 74, 17], [54, 24, 74, 22], [54, 28, 74, 26], [54, 30, 74, 28], [55, 8, 75, 6], [55, 15, 75, 13, "props"], [55, 20, 75, 18], [55, 21, 75, 19, "log"], [55, 24, 75, 22], [55, 25, 75, 23, "getAvailableStack"], [55, 42, 75, 40], [55, 43, 75, 41], [55, 44, 75, 42], [55, 45, 75, 43, "filter"], [55, 51, 75, 49], [55, 52, 75, 50, "_ref3"], [55, 57, 75, 50], [56, 10, 75, 50], [56, 14, 75, 52, "collapse"], [56, 22, 75, 60], [56, 25, 75, 60, "_ref3"], [56, 30, 75, 60], [56, 31, 75, 52, "collapse"], [56, 39, 75, 60], [57, 10, 75, 60], [57, 17, 75, 66], [57, 18, 75, 67, "collapse"], [57, 26, 75, 75], [58, 8, 75, 75], [58, 10, 75, 76], [59, 6, 76, 4], [59, 7, 76, 5], [59, 13, 76, 11], [60, 8, 77, 6], [60, 15, 77, 13, "props"], [60, 20, 77, 18], [60, 21, 77, 19, "log"], [60, 24, 77, 22], [60, 25, 77, 23, "getAvailableStack"], [60, 42, 77, 40], [60, 43, 77, 41], [60, 44, 77, 42], [61, 6, 78, 4], [62, 4, 79, 2], [63, 4, 81, 2], [63, 8, 81, 6, "props"], [63, 13, 81, 11], [63, 14, 81, 12, "log"], [63, 17, 81, 15], [63, 18, 81, 16, "getAvailableStack"], [63, 35, 81, 33], [63, 36, 81, 34], [63, 37, 81, 35], [63, 38, 81, 36, "length"], [63, 44, 81, 42], [63, 49, 81, 47], [63, 50, 81, 48], [63, 52, 81, 50], [64, 6, 82, 4], [64, 13, 82, 11], [64, 17, 82, 15], [65, 4, 83, 2], [66, 4, 85, 2], [66, 24, 86, 4], [66, 28, 86, 4, "_jsxDevRuntime"], [66, 42, 86, 4], [66, 43, 86, 4, "jsxDEV"], [66, 49, 86, 4], [66, 51, 86, 5, "_LogBoxInspectorSection"], [66, 74, 86, 5], [66, 75, 86, 5, "default"], [66, 82, 86, 27], [67, 6, 87, 6, "heading"], [67, 13, 87, 13], [67, 15, 87, 14], [67, 27, 87, 26], [68, 6, 88, 6, "action"], [68, 12, 88, 12], [68, 27, 89, 8], [68, 31, 89, 8, "_jsxDevRuntime"], [68, 45, 89, 8], [68, 46, 89, 8, "jsxDEV"], [68, 52, 89, 8], [68, 54, 89, 9, "_LogBoxInspectorSourceMapStatus"], [68, 85, 89, 9], [68, 86, 89, 9, "default"], [68, 93, 89, 39], [69, 8, 90, 10, "onPress"], [69, 15, 90, 17], [69, 17, 91, 12, "props"], [69, 22, 91, 17], [69, 23, 91, 18, "log"], [69, 26, 91, 21], [69, 27, 91, 22, "symbolicated"], [69, 39, 91, 34], [69, 40, 91, 35, "status"], [69, 46, 91, 41], [69, 51, 91, 46], [69, 59, 91, 54], [69, 62, 91, 57, "props"], [69, 67, 91, 62], [69, 68, 91, 63, "onRetry"], [69, 75, 91, 70], [69, 78, 91, 73], [69, 82, 92, 11], [70, 8, 93, 10, "status"], [70, 14, 93, 16], [70, 16, 93, 18, "props"], [70, 21, 93, 23], [70, 22, 93, 24, "log"], [70, 25, 93, 27], [70, 26, 93, 28, "symbolicated"], [70, 38, 93, 40], [70, 39, 93, 41, "status"], [71, 6, 93, 48], [72, 8, 93, 48, "fileName"], [72, 16, 93, 48], [72, 18, 93, 48, "_jsxFileName"], [72, 30, 93, 48], [73, 8, 93, 48, "lineNumber"], [73, 18, 93, 48], [74, 8, 93, 48, "columnNumber"], [74, 20, 93, 48], [75, 6, 93, 48], [75, 13, 94, 9], [75, 14, 95, 7], [76, 6, 95, 7, "children"], [76, 14, 95, 7], [76, 17, 96, 7, "props"], [76, 22, 96, 12], [76, 23, 96, 13, "log"], [76, 26, 96, 16], [76, 27, 96, 17, "symbolicated"], [76, 39, 96, 29], [76, 40, 96, 30, "status"], [76, 46, 96, 36], [76, 51, 96, 41], [76, 61, 96, 51], [76, 78, 97, 8], [76, 82, 97, 8, "_jsxDevRuntime"], [76, 96, 97, 8], [76, 97, 97, 8, "jsxDEV"], [76, 103, 97, 8], [76, 105, 97, 9, "_View"], [76, 110, 97, 9], [76, 111, 97, 9, "default"], [76, 118, 97, 13], [77, 8, 97, 14, "style"], [77, 13, 97, 19], [77, 15, 97, 21, "stackStyles"], [77, 26, 97, 32], [77, 27, 97, 33, "hintBox"], [77, 34, 97, 41], [78, 8, 97, 41, "children"], [78, 16, 97, 41], [78, 31, 98, 10], [78, 35, 98, 10, "_jsxDevRuntime"], [78, 49, 98, 10], [78, 50, 98, 10, "jsxDEV"], [78, 56, 98, 10], [78, 58, 98, 11, "_Text"], [78, 63, 98, 11], [78, 64, 98, 11, "default"], [78, 71, 98, 15], [79, 10, 98, 16, "style"], [79, 15, 98, 21], [79, 17, 98, 23, "stackStyles"], [79, 28, 98, 34], [79, 29, 98, 35, "hintText"], [79, 37, 98, 44], [80, 10, 98, 44, "children"], [80, 18, 98, 44], [80, 20, 98, 45], [81, 8, 101, 10], [82, 10, 101, 10, "fileName"], [82, 18, 101, 10], [82, 20, 101, 10, "_jsxFileName"], [82, 32, 101, 10], [83, 10, 101, 10, "lineNumber"], [83, 20, 101, 10], [84, 10, 101, 10, "columnNumber"], [84, 22, 101, 10], [85, 8, 101, 10], [85, 15, 101, 16], [86, 6, 101, 17], [87, 8, 101, 17, "fileName"], [87, 16, 101, 17], [87, 18, 101, 17, "_jsxFileName"], [87, 30, 101, 17], [88, 8, 101, 17, "lineNumber"], [88, 18, 101, 17], [89, 8, 101, 17, "columnNumber"], [89, 20, 101, 17], [90, 6, 101, 17], [90, 13, 102, 14], [90, 14, 103, 7], [90, 29, 104, 6], [90, 33, 104, 6, "_jsxDevRuntime"], [90, 47, 104, 6], [90, 48, 104, 6, "jsxDEV"], [90, 54, 104, 6], [90, 56, 104, 7, "StackFrameList"], [90, 70, 104, 21], [91, 8, 105, 8, "list"], [91, 12, 105, 12], [91, 14, 105, 14, "getStackList"], [91, 26, 105, 26], [91, 27, 105, 27], [91, 28, 105, 29], [92, 8, 106, 8, "status"], [92, 14, 106, 14], [92, 16, 106, 16, "props"], [92, 21, 106, 21], [92, 22, 106, 22, "log"], [92, 25, 106, 25], [92, 26, 106, 26, "symbolicated"], [92, 38, 106, 38], [92, 39, 106, 39, "status"], [93, 6, 106, 46], [94, 8, 106, 46, "fileName"], [94, 16, 106, 46], [94, 18, 106, 46, "_jsxFileName"], [94, 30, 106, 46], [95, 8, 106, 46, "lineNumber"], [95, 18, 106, 46], [96, 8, 106, 46, "columnNumber"], [96, 20, 106, 46], [97, 6, 106, 46], [97, 13, 107, 7], [97, 14, 107, 8], [97, 29, 108, 6], [97, 33, 108, 6, "_jsxDevRuntime"], [97, 47, 108, 6], [97, 48, 108, 6, "jsxDEV"], [97, 54, 108, 6], [97, 56, 108, 7, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [97, 72, 108, 23], [98, 8, 109, 8, "onPress"], [98, 15, 109, 15], [98, 17, 109, 17, "onPress"], [98, 18, 109, 17], [98, 23, 109, 23, "setCollapsed"], [98, 35, 109, 35], [98, 36, 109, 36], [98, 37, 109, 37, "collapsed"], [98, 46, 109, 46], [98, 47, 109, 48], [99, 8, 110, 8, "message"], [99, 15, 110, 15], [99, 17, 110, 17, "getCollapseMessage"], [99, 35, 110, 35], [99, 36, 110, 36, "props"], [99, 41, 110, 41], [99, 42, 110, 42, "log"], [99, 45, 110, 45], [99, 46, 110, 46, "getAvailableStack"], [99, 63, 110, 63], [99, 64, 110, 64], [99, 65, 110, 65], [99, 67, 110, 67, "collapsed"], [99, 76, 110, 76], [100, 6, 110, 78], [101, 8, 110, 78, "fileName"], [101, 16, 110, 78], [101, 18, 110, 78, "_jsxFileName"], [101, 30, 110, 78], [102, 8, 110, 78, "lineNumber"], [102, 18, 110, 78], [103, 8, 110, 78, "columnNumber"], [103, 20, 110, 78], [104, 6, 110, 78], [104, 13, 111, 7], [104, 14, 111, 8], [105, 4, 111, 8], [106, 6, 111, 8, "fileName"], [106, 14, 111, 8], [106, 16, 111, 8, "_jsxFileName"], [106, 28, 111, 8], [107, 6, 111, 8, "lineNumber"], [107, 16, 111, 8], [108, 6, 111, 8, "columnNumber"], [108, 18, 111, 8], [109, 4, 111, 8], [109, 11, 112, 28], [109, 12, 112, 29], [110, 2, 114, 0], [111, 2, 116, 0], [111, 11, 116, 9, "StackFrameList"], [111, 25, 116, 23, "StackFrameList"], [111, 26, 116, 24, "props"], [111, 31, 119, 1], [111, 33, 119, 3], [112, 4, 120, 2], [112, 24, 121, 4], [112, 28, 121, 4, "_jsxDevRuntime"], [112, 42, 121, 4], [112, 43, 121, 4, "jsxDEV"], [112, 49, 121, 4], [112, 51, 121, 4, "_jsxDevRuntime"], [112, 65, 121, 4], [112, 66, 121, 4, "Fragment"], [112, 74, 121, 4], [113, 6, 121, 4, "children"], [113, 14, 121, 4], [113, 16, 122, 7, "props"], [113, 21, 122, 12], [113, 22, 122, 13, "list"], [113, 26, 122, 17], [113, 27, 122, 18, "map"], [113, 30, 122, 21], [113, 31, 122, 22], [113, 32, 122, 23, "frame"], [113, 37, 122, 28], [113, 39, 122, 30, "index"], [113, 44, 122, 35], [113, 49, 122, 40], [114, 8, 123, 8], [114, 12, 123, 15, "file"], [114, 16, 123, 19], [114, 19, 123, 35, "frame"], [114, 24, 123, 40], [114, 25, 123, 15, "file"], [114, 29, 123, 19], [115, 10, 123, 21, "lineNumber"], [115, 20, 123, 31], [115, 23, 123, 35, "frame"], [115, 28, 123, 40], [115, 29, 123, 21, "lineNumber"], [115, 39, 123, 31], [116, 8, 124, 8], [116, 28, 125, 10], [116, 32, 125, 10, "_jsxDevRuntime"], [116, 46, 125, 10], [116, 47, 125, 10, "jsxDEV"], [116, 53, 125, 10], [116, 55, 125, 11, "_LogBoxInspectorStackFrame"], [116, 81, 125, 11], [116, 82, 125, 11, "default"], [116, 89, 125, 36], [117, 10, 127, 12, "frame"], [117, 15, 127, 17], [117, 17, 127, 19, "frame"], [117, 22, 127, 25], [118, 10, 128, 12, "onPress"], [118, 17, 128, 19], [118, 19, 129, 14, "props"], [118, 24, 129, 19], [118, 25, 129, 20, "status"], [118, 31, 129, 26], [118, 36, 129, 31], [118, 46, 129, 41], [118, 50, 129, 45, "file"], [118, 54, 129, 49], [118, 58, 129, 53], [118, 62, 129, 57], [118, 66, 129, 61, "lineNumber"], [118, 76, 129, 71], [118, 80, 129, 75], [118, 84, 129, 79], [118, 87, 130, 18], [118, 93, 130, 24], [118, 97, 130, 24, "openFileInEditor"], [118, 122, 130, 40], [118, 124, 130, 41, "file"], [118, 128, 130, 45], [118, 130, 130, 47, "lineNumber"], [118, 140, 130, 57], [118, 141, 130, 58], [118, 144, 131, 18], [119, 8, 132, 13], [119, 11, 126, 17, "index"], [119, 16, 126, 22], [120, 10, 126, 22, "fileName"], [120, 18, 126, 22], [120, 20, 126, 22, "_jsxFileName"], [120, 32, 126, 22], [121, 10, 126, 22, "lineNumber"], [121, 20, 126, 22], [122, 10, 126, 22, "columnNumber"], [122, 22, 126, 22], [123, 8, 126, 22], [123, 15, 133, 11], [123, 16, 133, 12], [124, 6, 135, 6], [124, 7, 135, 7], [125, 4, 135, 8], [125, 20, 136, 6], [125, 21, 136, 7], [126, 2, 138, 0], [127, 2, 140, 0], [127, 11, 140, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [127, 27, 140, 25, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [127, 28, 141, 2, "props"], [127, 33, 141, 58], [127, 35, 142, 2], [128, 4, 143, 2], [128, 24, 144, 4], [128, 28, 144, 4, "_jsxDevRuntime"], [128, 42, 144, 4], [128, 43, 144, 4, "jsxDEV"], [128, 49, 144, 4], [128, 51, 144, 5, "_View"], [128, 56, 144, 5], [128, 57, 144, 5, "default"], [128, 64, 144, 9], [129, 6, 144, 10, "style"], [129, 11, 144, 15], [129, 13, 144, 17, "stackStyles"], [129, 24, 144, 28], [129, 25, 144, 29, "collapseContainer"], [129, 42, 144, 47], [130, 6, 144, 47, "children"], [130, 14, 144, 47], [130, 29, 145, 6], [130, 33, 145, 6, "_jsxDevRuntime"], [130, 47, 145, 6], [130, 48, 145, 6, "jsxDEV"], [130, 54, 145, 6], [130, 56, 145, 7, "_LogBoxButton"], [130, 69, 145, 7], [130, 70, 145, 7, "default"], [130, 77, 145, 19], [131, 8, 146, 8, "backgroundColor"], [131, 23, 146, 23], [131, 25, 146, 25], [132, 10, 147, 10, "default"], [132, 17, 147, 17], [132, 19, 147, 19], [132, 32, 147, 32], [133, 10, 148, 10, "pressed"], [133, 17, 148, 17], [133, 19, 148, 19, "LogBoxStyle"], [133, 30, 148, 30], [133, 31, 148, 31, "getBackgroundColor"], [133, 49, 148, 49], [133, 50, 148, 50], [133, 51, 148, 51], [134, 8, 149, 8], [134, 9, 149, 10], [135, 8, 150, 8, "onPress"], [135, 15, 150, 15], [135, 17, 150, 17, "props"], [135, 22, 150, 22], [135, 23, 150, 23, "onPress"], [135, 30, 150, 31], [136, 8, 151, 8, "style"], [136, 13, 151, 13], [136, 15, 151, 15, "stackStyles"], [136, 26, 151, 26], [136, 27, 151, 27, "collapseButton"], [136, 41, 151, 42], [137, 8, 151, 42, "children"], [137, 16, 151, 42], [137, 31, 152, 8], [137, 35, 152, 8, "_jsxDevRuntime"], [137, 49, 152, 8], [137, 50, 152, 8, "jsxDEV"], [137, 56, 152, 8], [137, 58, 152, 9, "_Text"], [137, 63, 152, 9], [137, 64, 152, 9, "default"], [137, 71, 152, 13], [138, 10, 152, 14, "style"], [138, 15, 152, 19], [138, 17, 152, 21, "stackStyles"], [138, 28, 152, 32], [138, 29, 152, 33, "collapse"], [138, 37, 152, 42], [139, 10, 152, 42, "children"], [139, 18, 152, 42], [139, 20, 152, 44, "props"], [139, 25, 152, 49], [139, 26, 152, 50, "message"], [140, 8, 152, 57], [141, 10, 152, 57, "fileName"], [141, 18, 152, 57], [141, 20, 152, 57, "_jsxFileName"], [141, 32, 152, 57], [142, 10, 152, 57, "lineNumber"], [142, 20, 152, 57], [143, 10, 152, 57, "columnNumber"], [143, 22, 152, 57], [144, 8, 152, 57], [144, 15, 152, 64], [145, 6, 152, 65], [146, 8, 152, 65, "fileName"], [146, 16, 152, 65], [146, 18, 152, 65, "_jsxFileName"], [146, 30, 152, 65], [147, 8, 152, 65, "lineNumber"], [147, 18, 152, 65], [148, 8, 152, 65, "columnNumber"], [148, 20, 152, 65], [149, 6, 152, 65], [149, 13, 153, 20], [150, 4, 153, 21], [151, 6, 153, 21, "fileName"], [151, 14, 153, 21], [151, 16, 153, 21, "_jsxFileName"], [151, 28, 153, 21], [152, 6, 153, 21, "lineNumber"], [152, 16, 153, 21], [153, 6, 153, 21, "columnNumber"], [153, 18, 153, 21], [154, 4, 153, 21], [154, 11, 154, 10], [154, 12, 154, 11], [155, 2, 156, 0], [156, 2, 158, 0], [156, 6, 158, 6, "stackStyles"], [156, 17, 158, 17], [156, 20, 158, 20, "StyleSheet"], [156, 39, 158, 30], [156, 40, 158, 31, "create"], [156, 46, 158, 37], [156, 47, 158, 38], [157, 4, 159, 2, "section"], [157, 11, 159, 9], [157, 13, 159, 11], [158, 6, 160, 4, "marginTop"], [158, 15, 160, 13], [158, 17, 160, 15], [159, 4, 161, 2], [159, 5, 161, 3], [160, 4, 162, 2, "heading"], [160, 11, 162, 9], [160, 13, 162, 11], [161, 6, 163, 4, "alignItems"], [161, 16, 163, 14], [161, 18, 163, 16], [161, 26, 163, 24], [162, 6, 164, 4, "flexDirection"], [162, 19, 164, 17], [162, 21, 164, 19], [162, 26, 164, 24], [163, 6, 165, 4, "paddingHorizontal"], [163, 23, 165, 21], [163, 25, 165, 23], [163, 27, 165, 25], [164, 6, 166, 4, "marginBottom"], [164, 18, 166, 16], [164, 20, 166, 18], [165, 4, 167, 2], [165, 5, 167, 3], [166, 4, 168, 2, "headingText"], [166, 15, 168, 13], [166, 17, 168, 15], [167, 6, 169, 4, "color"], [167, 11, 169, 9], [167, 13, 169, 11, "LogBoxStyle"], [167, 24, 169, 22], [167, 25, 169, 23, "getTextColor"], [167, 37, 169, 35], [167, 38, 169, 36], [167, 39, 169, 37], [167, 40, 169, 38], [168, 6, 170, 4, "flex"], [168, 10, 170, 8], [168, 12, 170, 10], [168, 13, 170, 11], [169, 6, 171, 4, "fontSize"], [169, 14, 171, 12], [169, 16, 171, 14], [169, 18, 171, 16], [170, 6, 172, 4, "fontWeight"], [170, 16, 172, 14], [170, 18, 172, 16], [170, 23, 172, 21], [171, 6, 173, 4, "includeFontPadding"], [171, 24, 173, 22], [171, 26, 173, 24], [171, 31, 173, 29], [172, 6, 174, 4, "lineHeight"], [172, 16, 174, 14], [172, 18, 174, 16], [173, 4, 175, 2], [173, 5, 175, 3], [174, 4, 176, 2, "body"], [174, 8, 176, 6], [174, 10, 176, 8], [175, 6, 177, 4, "paddingBottom"], [175, 19, 177, 17], [175, 21, 177, 19], [176, 4, 178, 2], [176, 5, 178, 3], [177, 4, 179, 2, "bodyText"], [177, 12, 179, 10], [177, 14, 179, 12], [178, 6, 180, 4, "color"], [178, 11, 180, 9], [178, 13, 180, 11, "LogBoxStyle"], [178, 24, 180, 22], [178, 25, 180, 23, "getTextColor"], [178, 37, 180, 35], [178, 38, 180, 36], [178, 39, 180, 37], [178, 40, 180, 38], [179, 6, 181, 4, "fontSize"], [179, 14, 181, 12], [179, 16, 181, 14], [179, 18, 181, 16], [180, 6, 182, 4, "includeFontPadding"], [180, 24, 182, 22], [180, 26, 182, 24], [180, 31, 182, 29], [181, 6, 183, 4, "lineHeight"], [181, 16, 183, 14], [181, 18, 183, 16], [181, 20, 183, 18], [182, 6, 184, 4, "fontWeight"], [182, 16, 184, 14], [182, 18, 184, 16], [182, 23, 184, 21], [183, 6, 185, 4, "paddingHorizontal"], [183, 23, 185, 21], [183, 25, 185, 23], [184, 4, 186, 2], [184, 5, 186, 3], [185, 4, 187, 2, "hintText"], [185, 12, 187, 10], [185, 14, 187, 12], [186, 6, 188, 4, "color"], [186, 11, 188, 9], [186, 13, 188, 11, "LogBoxStyle"], [186, 24, 188, 22], [186, 25, 188, 23, "getTextColor"], [186, 37, 188, 35], [186, 38, 188, 36], [186, 41, 188, 39], [186, 42, 188, 40], [187, 6, 189, 4, "fontSize"], [187, 14, 189, 12], [187, 16, 189, 14], [187, 18, 189, 16], [188, 6, 190, 4, "includeFontPadding"], [188, 24, 190, 22], [188, 26, 190, 24], [188, 31, 190, 29], [189, 6, 191, 4, "lineHeight"], [189, 16, 191, 14], [189, 18, 191, 16], [189, 20, 191, 18], [190, 6, 192, 4, "fontWeight"], [190, 16, 192, 14], [190, 18, 192, 16], [190, 23, 192, 21], [191, 6, 193, 4, "marginHorizontal"], [191, 22, 193, 20], [191, 24, 193, 22], [192, 4, 194, 2], [192, 5, 194, 3], [193, 4, 195, 2, "hintBox"], [193, 11, 195, 9], [193, 13, 195, 11], [194, 6, 196, 4, "backgroundColor"], [194, 21, 196, 19], [194, 23, 196, 21, "LogBoxStyle"], [194, 34, 196, 32], [194, 35, 196, 33, "getBackgroundColor"], [194, 53, 196, 51], [194, 54, 196, 52], [194, 55, 196, 53], [195, 6, 197, 4, "marginHorizontal"], [195, 22, 197, 20], [195, 24, 197, 22], [195, 26, 197, 24], [196, 6, 198, 4, "paddingHorizontal"], [196, 23, 198, 21], [196, 25, 198, 23], [196, 26, 198, 24], [197, 6, 199, 4, "paddingVertical"], [197, 21, 199, 19], [197, 23, 199, 21], [197, 25, 199, 23], [198, 6, 200, 4, "borderRadius"], [198, 18, 200, 16], [198, 20, 200, 18], [198, 21, 200, 19], [199, 6, 201, 4, "marginBottom"], [199, 18, 201, 16], [199, 20, 201, 18], [200, 4, 202, 2], [200, 5, 202, 3], [201, 4, 203, 2, "collapseContainer"], [201, 21, 203, 19], [201, 23, 203, 21], [202, 6, 204, 4, "marginLeft"], [202, 16, 204, 14], [202, 18, 204, 16], [202, 20, 204, 18], [203, 6, 205, 4, "flexDirection"], [203, 19, 205, 17], [203, 21, 205, 19], [204, 4, 206, 2], [204, 5, 206, 3], [205, 4, 207, 2, "collapseButton"], [205, 18, 207, 16], [205, 20, 207, 18], [206, 6, 208, 4, "borderRadius"], [206, 18, 208, 16], [206, 20, 208, 18], [207, 4, 209, 2], [207, 5, 209, 3], [208, 4, 210, 2, "collapse"], [208, 12, 210, 10], [208, 14, 210, 12], [209, 6, 211, 4, "color"], [209, 11, 211, 9], [209, 13, 211, 11, "LogBoxStyle"], [209, 24, 211, 22], [209, 25, 211, 23, "getTextColor"], [209, 37, 211, 35], [209, 38, 211, 36], [209, 41, 211, 39], [209, 42, 211, 40], [210, 6, 212, 4, "fontSize"], [210, 14, 212, 12], [210, 16, 212, 14], [210, 18, 212, 16], [211, 6, 213, 4, "fontWeight"], [211, 16, 213, 14], [211, 18, 213, 16], [211, 23, 213, 21], [212, 6, 214, 4, "lineHeight"], [212, 16, 214, 14], [212, 18, 214, 16], [212, 20, 214, 18], [213, 6, 215, 4, "marginTop"], [213, 15, 215, 13], [213, 17, 215, 15], [213, 18, 215, 16], [214, 6, 216, 4, "paddingHorizontal"], [214, 23, 216, 21], [214, 25, 216, 23], [214, 27, 216, 25], [215, 6, 217, 4, "paddingVertical"], [215, 21, 217, 19], [215, 23, 217, 21], [216, 4, 218, 2], [217, 2, 219, 0], [217, 3, 219, 1], [217, 4, 219, 2], [218, 2, 219, 3], [218, 6, 219, 3, "_default"], [218, 14, 219, 3], [218, 17, 219, 3, "exports"], [218, 24, 219, 3], [218, 25, 219, 3, "default"], [218, 32, 219, 3], [218, 35, 221, 15, "LogBoxInspectorStackFrames"], [218, 61, 221, 41], [219, 0, 221, 41], [219, 3]], "functionMap": {"names": ["<global>", "getCollapseMessage", "stackFrames.reduce$argument_0", "LogBoxInspectorStackFrames", "React.useState$argument_0", "props.log.getAvailableStack.some$argument_0", "getStackList", "props.log.getAvailableStack.filter$argument_0", "StackFrameFooter.props.onPress", "StackFrameList", "props.list.map$argument_0", "<anonymous>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;OC8B;4CCQ;GDM;CDoB;AGE;mDCC;8CCE,yBD;GDC;EGE;kDCE,yBD;GHI;iBK8B,8BL;CHK;ASE;sBCM;kBCQ,wCD;ODK;CTG;AYE;CZgB"}}, "type": "js/module"}]}