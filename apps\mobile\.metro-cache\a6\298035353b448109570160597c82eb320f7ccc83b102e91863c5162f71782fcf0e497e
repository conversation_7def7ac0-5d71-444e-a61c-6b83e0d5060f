{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../reanimated<PERSON><PERSON>per", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 27}, "end": {"line": 2, "column": 50, "index": 77}}], "key": "55E8XkeO+dzLnJh3bfoVrgRwD58=", "exportNames": ["*"]}}, {"name": "../../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 78}, "end": {"line": 3, "column": 44, "index": 122}}], "key": "4wo4OYT4MSo2InL8kiWmZxvepwE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Wrap = exports.AnimatedWrap = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[6], \"react\"));\n  var _reanimatedWrapper = require(_dependencyMap[7], \"../reanimatedWrapper\");\n  var _utils = require(_dependencyMap[8], \"../../../utils\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Wrap = exports.Wrap = /*#__PURE__*/function (_React$Component) {\n    function Wrap() {\n      (0, _classCallCheck2.default)(this, Wrap);\n      return _callSuper(this, Wrap, arguments);\n    }\n    (0, _inherits2.default)(Wrap, _React$Component);\n    return (0, _createClass2.default)(Wrap, [{\n      key: \"render\",\n      value: function render() {\n        try {\n          // I don't think that fighting with types over such a simple function is worth it\n          // The only thing it does is add 'collapsable: false' to the child component\n          // to make sure it is in the native view hierarchy so the detector can find\n          // correct viewTag to attach to.\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          var child = _react.default.Children.only(this.props.children);\n          return /*#__PURE__*/_react.default.cloneElement(child, {\n            collapsable: false\n          },\n          // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n          child.props.children);\n        } catch (e) {\n          throw new Error((0, _utils.tagMessage)(`GestureDetector got more than one view as a child. If you want the gesture to work on multiple views, wrap them with a common parent and attach the gesture to that view.`));\n        }\n      }\n    }]);\n  }(_react.default.Component);\n  var AnimatedWrap = exports.AnimatedWrap = _reanimatedWrapper.Reanimated?.default?.createAnimatedComponent(Wrap) ?? Wrap;\n});", "lineCount": 45, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_react"], [12, 12, 1, 0], [12, 15, 1, 0, "_interopRequireDefault"], [12, 37, 1, 0], [12, 38, 1, 0, "require"], [12, 45, 1, 0], [12, 46, 1, 0, "_dependencyMap"], [12, 60, 1, 0], [13, 2, 2, 0], [13, 6, 2, 0, "_reanimated<PERSON><PERSON>per"], [13, 24, 2, 0], [13, 27, 2, 0, "require"], [13, 34, 2, 0], [13, 35, 2, 0, "_dependencyMap"], [13, 49, 2, 0], [14, 2, 3, 0], [14, 6, 3, 0, "_utils"], [14, 12, 3, 0], [14, 15, 3, 0, "require"], [14, 22, 3, 0], [14, 23, 3, 0, "_dependencyMap"], [14, 37, 3, 0], [15, 2, 3, 44], [15, 11, 3, 44, "_callSuper"], [15, 22, 3, 44, "t"], [15, 23, 3, 44], [15, 25, 3, 44, "o"], [15, 26, 3, 44], [15, 28, 3, 44, "e"], [15, 29, 3, 44], [15, 40, 3, 44, "o"], [15, 41, 3, 44], [15, 48, 3, 44, "_getPrototypeOf2"], [15, 64, 3, 44], [15, 65, 3, 44, "default"], [15, 72, 3, 44], [15, 74, 3, 44, "o"], [15, 75, 3, 44], [15, 82, 3, 44, "_possibleConstructorReturn2"], [15, 109, 3, 44], [15, 110, 3, 44, "default"], [15, 117, 3, 44], [15, 119, 3, 44, "t"], [15, 120, 3, 44], [15, 122, 3, 44, "_isNativeReflectConstruct"], [15, 147, 3, 44], [15, 152, 3, 44, "Reflect"], [15, 159, 3, 44], [15, 160, 3, 44, "construct"], [15, 169, 3, 44], [15, 170, 3, 44, "o"], [15, 171, 3, 44], [15, 173, 3, 44, "e"], [15, 174, 3, 44], [15, 186, 3, 44, "_getPrototypeOf2"], [15, 202, 3, 44], [15, 203, 3, 44, "default"], [15, 210, 3, 44], [15, 212, 3, 44, "t"], [15, 213, 3, 44], [15, 215, 3, 44, "constructor"], [15, 226, 3, 44], [15, 230, 3, 44, "o"], [15, 231, 3, 44], [15, 232, 3, 44, "apply"], [15, 237, 3, 44], [15, 238, 3, 44, "t"], [15, 239, 3, 44], [15, 241, 3, 44, "e"], [15, 242, 3, 44], [16, 2, 3, 44], [16, 11, 3, 44, "_isNativeReflectConstruct"], [16, 37, 3, 44], [16, 51, 3, 44, "t"], [16, 52, 3, 44], [16, 56, 3, 44, "Boolean"], [16, 63, 3, 44], [16, 64, 3, 44, "prototype"], [16, 73, 3, 44], [16, 74, 3, 44, "valueOf"], [16, 81, 3, 44], [16, 82, 3, 44, "call"], [16, 86, 3, 44], [16, 87, 3, 44, "Reflect"], [16, 94, 3, 44], [16, 95, 3, 44, "construct"], [16, 104, 3, 44], [16, 105, 3, 44, "Boolean"], [16, 112, 3, 44], [16, 145, 3, 44, "t"], [16, 146, 3, 44], [16, 159, 3, 44, "_isNativeReflectConstruct"], [16, 184, 3, 44], [16, 196, 3, 44, "_isNativeReflectConstruct"], [16, 197, 3, 44], [16, 210, 3, 44, "t"], [16, 211, 3, 44], [17, 2, 3, 44], [17, 6, 5, 13, "Wrap"], [17, 10, 5, 17], [17, 13, 5, 17, "exports"], [17, 20, 5, 17], [17, 21, 5, 17, "Wrap"], [17, 25, 5, 17], [17, 51, 5, 17, "_React$Component"], [17, 67, 5, 17], [18, 4, 5, 17], [18, 13, 5, 17, "Wrap"], [18, 18, 5, 17], [19, 6, 5, 17], [19, 10, 5, 17, "_classCallCheck2"], [19, 26, 5, 17], [19, 27, 5, 17, "default"], [19, 34, 5, 17], [19, 42, 5, 17, "Wrap"], [19, 46, 5, 17], [20, 6, 5, 17], [20, 13, 5, 17, "_callSuper"], [20, 23, 5, 17], [20, 30, 5, 17, "Wrap"], [20, 34, 5, 17], [20, 36, 5, 17, "arguments"], [20, 45, 5, 17], [21, 4, 5, 17], [22, 4, 5, 17], [22, 8, 5, 17, "_inherits2"], [22, 18, 5, 17], [22, 19, 5, 17, "default"], [22, 26, 5, 17], [22, 28, 5, 17, "Wrap"], [22, 32, 5, 17], [22, 34, 5, 17, "_React$Component"], [22, 50, 5, 17], [23, 4, 5, 17], [23, 15, 5, 17, "_createClass2"], [23, 28, 5, 17], [23, 29, 5, 17, "default"], [23, 36, 5, 17], [23, 38, 5, 17, "Wrap"], [23, 42, 5, 17], [24, 6, 5, 17, "key"], [24, 9, 5, 17], [25, 6, 5, 17, "value"], [25, 11, 5, 17], [25, 13, 10, 2], [25, 22, 10, 2, "render"], [25, 28, 10, 8, "render"], [25, 29, 10, 8], [25, 31, 10, 11], [26, 8, 11, 4], [26, 12, 11, 8], [27, 10, 12, 6], [28, 10, 13, 6], [29, 10, 14, 6], [30, 10, 15, 6], [31, 10, 16, 6], [32, 10, 17, 6], [32, 14, 17, 12, "child"], [32, 19, 17, 22], [32, 22, 17, 25, "React"], [32, 36, 17, 30], [32, 37, 17, 31, "Children"], [32, 45, 17, 39], [32, 46, 17, 40, "only"], [32, 50, 17, 44], [32, 51, 17, 45], [32, 55, 17, 49], [32, 56, 17, 50, "props"], [32, 61, 17, 55], [32, 62, 17, 56, "children"], [32, 70, 17, 64], [32, 71, 17, 65], [33, 10, 18, 6], [33, 30, 18, 13, "React"], [33, 44, 18, 18], [33, 45, 18, 19, "cloneElement"], [33, 57, 18, 31], [33, 58, 19, 8, "child"], [33, 63, 19, 13], [33, 65, 20, 8], [34, 12, 20, 10, "collapsable"], [34, 23, 20, 21], [34, 25, 20, 23], [35, 10, 20, 29], [35, 11, 20, 30], [36, 10, 21, 8], [37, 10, 22, 8, "child"], [37, 15, 22, 13], [37, 16, 22, 14, "props"], [37, 21, 22, 19], [37, 22, 22, 20, "children"], [37, 30, 23, 6], [37, 31, 23, 7], [38, 8, 24, 4], [38, 9, 24, 5], [38, 10, 24, 6], [38, 17, 24, 13, "e"], [38, 18, 24, 14], [38, 20, 24, 16], [39, 10, 25, 6], [39, 16, 25, 12], [39, 20, 25, 16, "Error"], [39, 25, 25, 21], [39, 26, 26, 8], [39, 30, 26, 8, "tagMessage"], [39, 47, 26, 18], [39, 49, 27, 10], [39, 220, 28, 8], [39, 221, 29, 6], [39, 222, 29, 7], [40, 8, 30, 4], [41, 6, 31, 2], [42, 4, 31, 3], [43, 2, 31, 3], [43, 4, 5, 26, "React"], [43, 18, 5, 31], [43, 19, 5, 32, "Component"], [43, 28, 5, 41], [44, 2, 34, 7], [44, 6, 34, 13, "AnimatedWrap"], [44, 18, 34, 25], [44, 21, 34, 25, "exports"], [44, 28, 34, 25], [44, 29, 34, 25, "AnimatedWrap"], [44, 41, 34, 25], [44, 44, 35, 2, "Reanimated"], [44, 73, 35, 12], [44, 75, 35, 14, "default"], [44, 82, 35, 21], [44, 84, 35, 23, "createAnimatedComponent"], [44, 107, 35, 46], [44, 108, 35, 47, "Wrap"], [44, 112, 35, 51], [44, 113, 35, 52], [44, 117, 35, 56, "Wrap"], [44, 121, 35, 60], [45, 0, 35, 61], [45, 3]], "functionMap": {"names": ["<global>", "Wrap", "render"], "mappings": "AAA;OCI;ECK;GDqB;CDC"}}, "type": "js/module"}]}