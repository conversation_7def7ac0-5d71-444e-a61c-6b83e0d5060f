{"dependencies": [{"name": "../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 69}, "end": {"line": 3, "column": 44, "index": 113}}], "key": "ioSJ9iLOtXMo2uBjbVE14/NC9RQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.areDependenciesEqual = areDependenciesEqual;\n  exports.buildDependencies = buildDependencies;\n  exports.buildWorkletsHash = buildWorkletsHash;\n  exports.validateAnimatedStyles = exports.shallowEqual = exports.isAnimated = void 0;\n  var _errors = require(_dependencyMap[0], \"../errors\");\n  // Builds one big hash from multiple worklets' hashes.\n  function buildWorkletsHash(worklets) {\n    // For arrays `Object.values` returns the array itself.\n    return Object.values(worklets).reduce((acc, worklet) => acc + worklet.__workletHash.toString(), '');\n  }\n\n  // Builds dependencies array for useEvent handlers.\n  function buildDependencies(dependencies, handlers) {\n    var handlersList = Object.values(handlers).filter(handler => handler !== undefined);\n    if (!dependencies) {\n      dependencies = handlersList.map(handler => {\n        return {\n          workletHash: handler.__workletHash,\n          closure: handler.__closure\n        };\n      });\n    } else {\n      dependencies.push(buildWorkletsHash(handlersList));\n    }\n    return dependencies;\n  }\n\n  // This is supposed to work as useEffect comparison.\n  function areDependenciesEqual(nextDependencies, prevDependencies) {\n    function is(x, y) {\n      return x === y && (x !== 0 || 1 / x === 1 / y) || Number.isNaN(x) && Number.isNaN(y);\n    }\n    var objectIs = typeof Object.is === 'function' ? Object.is : is;\n    function areHookInputsEqual(nextDeps, prevDeps) {\n      if (!nextDeps || !prevDeps || prevDeps.length !== nextDeps.length) {\n        return false;\n      }\n      for (var i = 0; i < prevDeps.length; ++i) {\n        if (!objectIs(nextDeps[i], prevDeps[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    return areHookInputsEqual(nextDependencies, prevDependencies);\n  }\n  var _worklet_16812618548896_init_data = {\n    code: \"function isAnimated_utilsTs1(prop){const isAnimated_utilsTs1=this._recur;if(Array.isArray(prop)){return prop.some(isAnimated_utilsTs1);}else if(typeof prop==='object'&&prop!==null){if(prop.onFrame!==undefined){return true;}else{return Object.values(prop).some(isAnimated_utilsTs1);}}return false;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\hook\\\\utils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"isAnimated_utilsTs1\\\",\\\"prop\\\",\\\"_recur\\\",\\\"Array\\\",\\\"isArray\\\",\\\"some\\\",\\\"onFrame\\\",\\\"undefined\\\",\\\"Object\\\",\\\"values\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/hook/utils.ts\\\"],\\\"mappings\\\":\\\"AA0EO,SAAAA,mBAAmCA,CAAAC,IAAA,QAAAD,mBAAA,MAAAE,MAAA,CAExC,GAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,CAAE,CACvB,MAAO,CAAAA,IAAI,CAACI,IAAI,CAACL,mBAAW,EAC9B,CAAC,IAAM,IAAI,MAAO,CAAAC,IAAI,GAAK,QAAQ,EAAIA,IAAI,GAAK,IAAI,CAAE,CACpD,GAAKA,IAAI,CAA6BK,OAAO,GAAKC,SAAS,CAAE,CAC3D,MAAO,KAAI,CACb,CAAC,IAAM,CACL,MAAO,CAAAC,MAAM,CAACC,MAAM,CAACR,IAAI,CAAC,CAACI,IAAI,CAACL,mBAAW,EAC7C,CACF,CACA,MAAO,MAAK,CACd\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var isAnimated = exports.isAnimated = function () {\n    var _e = [new global.Error(), 1, -27];\n    var isAnimated = function (prop) {\n      if (Array.isArray(prop)) {\n        return prop.some(isAnimated);\n      } else if (typeof prop === 'object' && prop !== null) {\n        if (prop.onFrame !== undefined) {\n          return true;\n        } else {\n          return Object.values(prop).some(isAnimated);\n        }\n      }\n      return false;\n    };\n    isAnimated.__closure = {};\n    isAnimated.__workletHash = 16812618548896;\n    isAnimated.__initData = _worklet_16812618548896_init_data;\n    isAnimated.__stackDetails = _e;\n    return isAnimated;\n  }(); // This function works because `Object.keys`\n  // return empty array of primitives and on arrays\n  // it returns array of its indices.\n  var _worklet_2158856734967_init_data = {\n    code: \"function shallowEqual_utilsTs2(a,b){const aKeys=Object.keys(a);const bKeys=Object.keys(b);if(aKeys.length!==bKeys.length){return false;}for(let i=0;i<aKeys.length;i++){if(a[aKeys[i]]!==b[aKeys[i]]){return false;}}return true;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\hook\\\\utils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shallowEqual_utilsTs2\\\",\\\"a\\\",\\\"b\\\",\\\"aKeys\\\",\\\"Object\\\",\\\"keys\\\",\\\"bKeys\\\",\\\"length\\\",\\\"i\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/hook/utils.ts\\\"],\\\"mappings\\\":\\\"AA2FO,SAAAA,qBAEOA,CAAAC,CAAA,CAAAC,CAAA,EAEZ,KAAM,CAAAC,KAAK,CAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC,CAC5B,KAAM,CAAAK,KAAK,CAAGF,MAAM,CAACC,IAAI,CAACH,CAAC,CAAC,CAC5B,GAAIC,KAAK,CAACI,MAAM,GAAKD,KAAK,CAACC,MAAM,CAAE,CACjC,MAAO,MAAK,CACd,CACA,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGL,KAAK,CAACI,MAAM,CAAEC,CAAC,EAAE,CAAE,CACrC,GAAIP,CAAC,CAACE,KAAK,CAACK,CAAC,CAAC,CAAC,GAAKN,CAAC,CAACC,KAAK,CAACK,CAAC,CAAC,CAAC,CAAE,CAC/B,MAAO,MAAK,CACd,CACF,CACA,MAAO,KAAI,CACb\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var shallowEqual = exports.shallowEqual = function () {\n    var _e = [new global.Error(), 1, -27];\n    var shallowEqual = function (a, b) {\n      var aKeys = Object.keys(a);\n      var bKeys = Object.keys(b);\n      if (aKeys.length !== bKeys.length) {\n        return false;\n      }\n      for (var i = 0; i < aKeys.length; i++) {\n        if (a[aKeys[i]] !== b[aKeys[i]]) {\n          return false;\n        }\n      }\n      return true;\n    };\n    shallowEqual.__closure = {};\n    shallowEqual.__workletHash = 2158856734967;\n    shallowEqual.__initData = _worklet_2158856734967_init_data;\n    shallowEqual.__stackDetails = _e;\n    return shallowEqual;\n  }();\n  var _worklet_11676986094821_init_data = {\n    code: \"function validateAnimatedStyles_utilsTs3(styles){if(typeof styles!=='object'){throw new ReanimatedError(\\\"`useAnimatedStyle` has to return an object, found \\\"+typeof styles+\\\" instead.\\\");}else if(Array.isArray(styles)){throw new ReanimatedError('`useAnimatedStyle` has to return an object and cannot return static styles combined with dynamic ones. Please do merging where a component receives props.');}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\hook\\\\utils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"validateAnimatedStyles_utilsTs3\\\",\\\"styles\\\",\\\"ReanimatedError\\\",\\\"Array\\\",\\\"isArray\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/hook/utils.ts\\\"],\\\"mappings\\\":\\\"AA4GO,SAAAA,+BAA0DA,CAAEC,MAAA,EAEjE,GAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CAC9B,KAAM,IAAI,CAAAC,eAAe,sDACgC,MAAO,CAAAD,MAAM,YACtE,CAAC,CACH,CAAC,IAAM,IAAIE,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,CAAE,CAChC,KAAM,IAAI,CAAAC,eAAe,CACvB,4JACF,CAAC,CACH,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var validateAnimatedStyles = exports.validateAnimatedStyles = function () {\n    var _e = [new global.Error(), 1, -27];\n    var validateAnimatedStyles = function (styles) {\n      if (typeof styles !== 'object') {\n        throw new _errors.ReanimatedError(`\\`useAnimatedStyle\\` has to return an object, found ${typeof styles} instead.`);\n      } else if (Array.isArray(styles)) {\n        throw new _errors.ReanimatedError('`useAnimatedStyle` has to return an object and cannot return static styles combined with dynamic ones. Please do merging where a component receives props.');\n      }\n    };\n    validateAnimatedStyles.__closure = {};\n    validateAnimatedStyles.__workletHash = 11676986094821;\n    validateAnimatedStyles.__initData = _worklet_11676986094821_init_data;\n    validateAnimatedStyles.__stackDetails = _e;\n    return validateAnimatedStyles;\n  }();\n});", "lineCount": 129, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "areDependenciesEqual"], [7, 30, 1, 13], [7, 33, 1, 13, "areDependenciesEqual"], [7, 53, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "buildDependencies"], [8, 27, 1, 13], [8, 30, 1, 13, "buildDependencies"], [8, 47, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "buildWorkletsHash"], [9, 27, 1, 13], [9, 30, 1, 13, "buildWorkletsHash"], [9, 47, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "validateAnimatedStyles"], [10, 32, 1, 13], [10, 35, 1, 13, "exports"], [10, 42, 1, 13], [10, 43, 1, 13, "shallowEqual"], [10, 55, 1, 13], [10, 58, 1, 13, "exports"], [10, 65, 1, 13], [10, 66, 1, 13, "isAnimated"], [10, 76, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "_errors"], [11, 13, 3, 0], [11, 16, 3, 0, "require"], [11, 23, 3, 0], [11, 24, 3, 0, "_dependencyMap"], [11, 38, 3, 0], [12, 2, 6, 0], [13, 2, 7, 7], [13, 11, 7, 16, "buildWorkletsHash"], [13, 28, 7, 33, "buildWorkletsHash"], [13, 29, 8, 2, "worklets"], [13, 37, 10, 42], [13, 39, 11, 2], [14, 4, 12, 2], [15, 4, 13, 2], [15, 11, 13, 9, "Object"], [15, 17, 13, 15], [15, 18, 13, 16, "values"], [15, 24, 13, 22], [15, 25, 13, 23, "worklets"], [15, 33, 13, 31], [15, 34, 13, 32], [15, 35, 13, 33, "reduce"], [15, 41, 13, 39], [15, 42, 14, 4], [15, 43, 14, 5, "acc"], [15, 46, 14, 8], [15, 48, 14, 10, "worklet"], [15, 55, 14, 53], [15, 60, 15, 6, "acc"], [15, 63, 15, 9], [15, 66, 15, 12, "worklet"], [15, 73, 15, 19], [15, 74, 15, 20, "__workletHash"], [15, 87, 15, 33], [15, 88, 15, 34, "toString"], [15, 96, 15, 42], [15, 97, 15, 43], [15, 98, 15, 44], [15, 100, 16, 4], [15, 102, 17, 2], [15, 103, 17, 3], [16, 2, 18, 0], [18, 2, 20, 0], [19, 2, 21, 7], [19, 11, 21, 16, "buildDependencies"], [19, 28, 21, 33, "buildDependencies"], [19, 29, 22, 2, "dependencies"], [19, 41, 22, 30], [19, 43, 23, 2, "handlers"], [19, 51, 23, 55], [19, 53, 24, 2], [20, 4, 26, 2], [20, 8, 26, 8, "handlersList"], [20, 20, 26, 20], [20, 23, 26, 23, "Object"], [20, 29, 26, 29], [20, 30, 26, 30, "values"], [20, 36, 26, 36], [20, 37, 26, 37, "handlers"], [20, 45, 26, 45], [20, 46, 26, 46], [20, 47, 26, 47, "filter"], [20, 53, 26, 53], [20, 54, 27, 5, "handler"], [20, 61, 27, 12], [20, 65, 27, 17, "handler"], [20, 72, 27, 24], [20, 77, 27, 29, "undefined"], [20, 86, 28, 2], [20, 87, 28, 29], [21, 4, 29, 2], [21, 8, 29, 6], [21, 9, 29, 7, "dependencies"], [21, 21, 29, 19], [21, 23, 29, 21], [22, 6, 30, 4, "dependencies"], [22, 18, 30, 16], [22, 21, 30, 19, "handlersList"], [22, 33, 30, 31], [22, 34, 30, 32, "map"], [22, 37, 30, 35], [22, 38, 30, 37, "handler"], [22, 45, 30, 44], [22, 49, 30, 49], [23, 8, 31, 6], [23, 15, 31, 13], [24, 10, 32, 8, "workletHash"], [24, 21, 32, 19], [24, 23, 32, 21, "handler"], [24, 30, 32, 28], [24, 31, 32, 29, "__workletHash"], [24, 44, 32, 42], [25, 10, 33, 8, "closure"], [25, 17, 33, 15], [25, 19, 33, 17, "handler"], [25, 26, 33, 24], [25, 27, 33, 25, "__closure"], [26, 8, 34, 6], [26, 9, 34, 7], [27, 6, 35, 4], [27, 7, 35, 5], [27, 8, 35, 6], [28, 4, 36, 2], [28, 5, 36, 3], [28, 11, 36, 9], [29, 6, 37, 4, "dependencies"], [29, 18, 37, 16], [29, 19, 37, 17, "push"], [29, 23, 37, 21], [29, 24, 37, 22, "buildWorkletsHash"], [29, 41, 37, 39], [29, 42, 37, 40, "handlersList"], [29, 54, 37, 52], [29, 55, 37, 53], [29, 56, 37, 54], [30, 4, 38, 2], [31, 4, 40, 2], [31, 11, 40, 9, "dependencies"], [31, 23, 40, 21], [32, 2, 41, 0], [34, 2, 43, 0], [35, 2, 44, 7], [35, 11, 44, 16, "areDependenciesEqual"], [35, 31, 44, 36, "areDependenciesEqual"], [35, 32, 45, 2, "nextDependencies"], [35, 48, 45, 34], [35, 50, 46, 2, "prevDependencies"], [35, 66, 46, 34], [35, 68, 47, 2], [36, 4, 48, 2], [36, 13, 48, 11, "is"], [36, 15, 48, 13, "is"], [36, 16, 48, 14, "x"], [36, 17, 48, 23], [36, 19, 48, 25, "y"], [36, 20, 48, 34], [36, 22, 48, 36], [37, 6, 49, 4], [37, 13, 50, 7, "x"], [37, 14, 50, 8], [37, 19, 50, 13, "y"], [37, 20, 50, 14], [37, 25, 50, 19, "x"], [37, 26, 50, 20], [37, 31, 50, 25], [37, 32, 50, 26], [37, 36, 50, 30], [37, 37, 50, 31], [37, 40, 50, 34, "x"], [37, 41, 50, 35], [37, 46, 50, 40], [37, 47, 50, 41], [37, 50, 50, 44, "y"], [37, 51, 50, 45], [37, 52, 50, 46], [37, 56, 51, 7, "Number"], [37, 62, 51, 13], [37, 63, 51, 14, "isNaN"], [37, 68, 51, 19], [37, 69, 51, 20, "x"], [37, 70, 51, 21], [37, 71, 51, 22], [37, 75, 51, 26, "Number"], [37, 81, 51, 32], [37, 82, 51, 33, "isNaN"], [37, 87, 51, 38], [37, 88, 51, 39, "y"], [37, 89, 51, 40], [37, 90, 51, 42], [38, 4, 53, 2], [39, 4, 54, 2], [39, 8, 54, 8, "objectIs"], [39, 16, 54, 67], [39, 19, 55, 4], [39, 26, 55, 11, "Object"], [39, 32, 55, 17], [39, 33, 55, 18, "is"], [39, 35, 55, 20], [39, 40, 55, 25], [39, 50, 55, 35], [39, 53, 55, 38, "Object"], [39, 59, 55, 44], [39, 60, 55, 45, "is"], [39, 62, 55, 47], [39, 65, 55, 50, "is"], [39, 67, 55, 52], [40, 4, 57, 2], [40, 13, 57, 11, "areHookInputsEqual"], [40, 31, 57, 29, "areHookInputsEqual"], [40, 32, 58, 4, "nextDeps"], [40, 40, 58, 28], [40, 42, 59, 4, "prevDeps"], [40, 50, 59, 28], [40, 52, 60, 4], [41, 6, 61, 4], [41, 10, 61, 8], [41, 11, 61, 9, "nextDeps"], [41, 19, 61, 17], [41, 23, 61, 21], [41, 24, 61, 22, "prevDeps"], [41, 32, 61, 30], [41, 36, 61, 34, "prevDeps"], [41, 44, 61, 42], [41, 45, 61, 43, "length"], [41, 51, 61, 49], [41, 56, 61, 54, "nextDeps"], [41, 64, 61, 62], [41, 65, 61, 63, "length"], [41, 71, 61, 69], [41, 73, 61, 71], [42, 8, 62, 6], [42, 15, 62, 13], [42, 20, 62, 18], [43, 6, 63, 4], [44, 6, 64, 4], [44, 11, 64, 9], [44, 15, 64, 13, "i"], [44, 16, 64, 14], [44, 19, 64, 17], [44, 20, 64, 18], [44, 22, 64, 20, "i"], [44, 23, 64, 21], [44, 26, 64, 24, "prevDeps"], [44, 34, 64, 32], [44, 35, 64, 33, "length"], [44, 41, 64, 39], [44, 43, 64, 41], [44, 45, 64, 43, "i"], [44, 46, 64, 44], [44, 48, 64, 46], [45, 8, 65, 6], [45, 12, 65, 10], [45, 13, 65, 11, "objectIs"], [45, 21, 65, 19], [45, 22, 65, 20, "nextDeps"], [45, 30, 65, 28], [45, 31, 65, 29, "i"], [45, 32, 65, 30], [45, 33, 65, 31], [45, 35, 65, 33, "prevDeps"], [45, 43, 65, 41], [45, 44, 65, 42, "i"], [45, 45, 65, 43], [45, 46, 65, 44], [45, 47, 65, 45], [45, 49, 65, 47], [46, 10, 66, 8], [46, 17, 66, 15], [46, 22, 66, 20], [47, 8, 67, 6], [48, 6, 68, 4], [49, 6, 69, 4], [49, 13, 69, 11], [49, 17, 69, 15], [50, 4, 70, 2], [51, 4, 72, 2], [51, 11, 72, 9, "areHookInputsEqual"], [51, 29, 72, 27], [51, 30, 72, 28, "nextDependencies"], [51, 46, 72, 44], [51, 48, 72, 46, "prevDependencies"], [51, 64, 72, 62], [51, 65, 72, 63], [52, 2, 73, 0], [53, 2, 73, 1], [53, 6, 73, 1, "_worklet_16812618548896_init_data"], [53, 39, 73, 1], [54, 4, 73, 1, "code"], [54, 8, 73, 1], [55, 4, 73, 1, "location"], [55, 12, 73, 1], [56, 4, 73, 1, "sourceMap"], [56, 13, 73, 1], [57, 4, 73, 1, "version"], [57, 11, 73, 1], [58, 2, 73, 1], [59, 2, 73, 1], [59, 6, 73, 1, "isAnimated"], [59, 16, 73, 1], [59, 19, 73, 1, "exports"], [59, 26, 73, 1], [59, 27, 73, 1, "isAnimated"], [59, 37, 73, 1], [59, 40, 75, 7], [60, 4, 75, 7], [60, 8, 75, 7, "_e"], [60, 10, 75, 7], [60, 18, 75, 7, "global"], [60, 24, 75, 7], [60, 25, 75, 7, "Error"], [60, 30, 75, 7], [61, 4, 75, 7], [61, 8, 75, 7, "isAnimated"], [61, 18, 75, 7], [61, 30, 75, 7, "isAnimated"], [61, 31, 75, 27, "prop"], [61, 35, 75, 40], [61, 37, 75, 42], [62, 6, 77, 2], [62, 10, 77, 6, "Array"], [62, 15, 77, 11], [62, 16, 77, 12, "isArray"], [62, 23, 77, 19], [62, 24, 77, 20, "prop"], [62, 28, 77, 24], [62, 29, 77, 25], [62, 31, 77, 27], [63, 8, 78, 4], [63, 15, 78, 11, "prop"], [63, 19, 78, 15], [63, 20, 78, 16, "some"], [63, 24, 78, 20], [63, 25, 78, 21, "isAnimated"], [63, 35, 78, 31], [63, 36, 78, 32], [64, 6, 79, 2], [64, 7, 79, 3], [64, 13, 79, 9], [64, 17, 79, 13], [64, 24, 79, 20, "prop"], [64, 28, 79, 24], [64, 33, 79, 29], [64, 41, 79, 37], [64, 45, 79, 41, "prop"], [64, 49, 79, 45], [64, 54, 79, 50], [64, 58, 79, 54], [64, 60, 79, 56], [65, 8, 80, 4], [65, 12, 80, 9, "prop"], [65, 16, 80, 13], [65, 17, 80, 42, "onFrame"], [65, 24, 80, 49], [65, 29, 80, 54, "undefined"], [65, 38, 80, 63], [65, 40, 80, 65], [66, 10, 81, 6], [66, 17, 81, 13], [66, 21, 81, 17], [67, 8, 82, 4], [67, 9, 82, 5], [67, 15, 82, 11], [68, 10, 83, 6], [68, 17, 83, 13, "Object"], [68, 23, 83, 19], [68, 24, 83, 20, "values"], [68, 30, 83, 26], [68, 31, 83, 27, "prop"], [68, 35, 83, 31], [68, 36, 83, 32], [68, 37, 83, 33, "some"], [68, 41, 83, 37], [68, 42, 83, 38, "isAnimated"], [68, 52, 83, 48], [68, 53, 83, 49], [69, 8, 84, 4], [70, 6, 85, 2], [71, 6, 86, 2], [71, 13, 86, 9], [71, 18, 86, 14], [72, 4, 87, 0], [72, 5, 87, 1], [73, 4, 87, 1, "isAnimated"], [73, 14, 87, 1], [73, 15, 87, 1, "__closure"], [73, 24, 87, 1], [74, 4, 87, 1, "isAnimated"], [74, 14, 87, 1], [74, 15, 87, 1, "__workletHash"], [74, 28, 87, 1], [75, 4, 87, 1, "isAnimated"], [75, 14, 87, 1], [75, 15, 87, 1, "__initData"], [75, 25, 87, 1], [75, 28, 87, 1, "_worklet_16812618548896_init_data"], [75, 61, 87, 1], [76, 4, 87, 1, "isAnimated"], [76, 14, 87, 1], [76, 15, 87, 1, "__stackDetails"], [76, 29, 87, 1], [76, 32, 87, 1, "_e"], [76, 34, 87, 1], [77, 4, 87, 1], [77, 11, 87, 1, "isAnimated"], [77, 21, 87, 1], [78, 2, 87, 1], [78, 3, 75, 7], [78, 7, 89, 0], [79, 2, 90, 0], [80, 2, 91, 0], [81, 2, 91, 0], [81, 6, 91, 0, "_worklet_2158856734967_init_data"], [81, 38, 91, 0], [82, 4, 91, 0, "code"], [82, 8, 91, 0], [83, 4, 91, 0, "location"], [83, 12, 91, 0], [84, 4, 91, 0, "sourceMap"], [84, 13, 91, 0], [85, 4, 91, 0, "version"], [85, 11, 91, 0], [86, 2, 91, 0], [87, 2, 91, 0], [87, 6, 91, 0, "shallowEqual"], [87, 18, 91, 0], [87, 21, 91, 0, "exports"], [87, 28, 91, 0], [87, 29, 91, 0, "shallowEqual"], [87, 41, 91, 0], [87, 44, 92, 7], [88, 4, 92, 7], [88, 8, 92, 7, "_e"], [88, 10, 92, 7], [88, 18, 92, 7, "global"], [88, 24, 92, 7], [88, 25, 92, 7, "Error"], [88, 30, 92, 7], [89, 4, 92, 7], [89, 8, 92, 7, "shallowEqual"], [89, 20, 92, 7], [89, 32, 92, 7, "shallowEqual"], [89, 33, 94, 2, "a"], [89, 34, 94, 6], [89, 36, 94, 8, "b"], [89, 37, 94, 12], [89, 39, 94, 14], [90, 6, 96, 2], [90, 10, 96, 8, "a<PERSON><PERSON><PERSON>"], [90, 15, 96, 13], [90, 18, 96, 16, "Object"], [90, 24, 96, 22], [90, 25, 96, 23, "keys"], [90, 29, 96, 27], [90, 30, 96, 28, "a"], [90, 31, 96, 29], [90, 32, 96, 30], [91, 6, 97, 2], [91, 10, 97, 8, "b<PERSON><PERSON><PERSON>"], [91, 15, 97, 13], [91, 18, 97, 16, "Object"], [91, 24, 97, 22], [91, 25, 97, 23, "keys"], [91, 29, 97, 27], [91, 30, 97, 28, "b"], [91, 31, 97, 29], [91, 32, 97, 30], [92, 6, 98, 2], [92, 10, 98, 6, "a<PERSON><PERSON><PERSON>"], [92, 15, 98, 11], [92, 16, 98, 12, "length"], [92, 22, 98, 18], [92, 27, 98, 23, "b<PERSON><PERSON><PERSON>"], [92, 32, 98, 28], [92, 33, 98, 29, "length"], [92, 39, 98, 35], [92, 41, 98, 37], [93, 8, 99, 4], [93, 15, 99, 11], [93, 20, 99, 16], [94, 6, 100, 2], [95, 6, 101, 2], [95, 11, 101, 7], [95, 15, 101, 11, "i"], [95, 16, 101, 12], [95, 19, 101, 15], [95, 20, 101, 16], [95, 22, 101, 18, "i"], [95, 23, 101, 19], [95, 26, 101, 22, "a<PERSON><PERSON><PERSON>"], [95, 31, 101, 27], [95, 32, 101, 28, "length"], [95, 38, 101, 34], [95, 40, 101, 36, "i"], [95, 41, 101, 37], [95, 43, 101, 39], [95, 45, 101, 41], [96, 8, 102, 4], [96, 12, 102, 8, "a"], [96, 13, 102, 9], [96, 14, 102, 10, "a<PERSON><PERSON><PERSON>"], [96, 19, 102, 15], [96, 20, 102, 16, "i"], [96, 21, 102, 17], [96, 22, 102, 18], [96, 23, 102, 19], [96, 28, 102, 24, "b"], [96, 29, 102, 25], [96, 30, 102, 26, "a<PERSON><PERSON><PERSON>"], [96, 35, 102, 31], [96, 36, 102, 32, "i"], [96, 37, 102, 33], [96, 38, 102, 34], [96, 39, 102, 35], [96, 41, 102, 37], [97, 10, 103, 6], [97, 17, 103, 13], [97, 22, 103, 18], [98, 8, 104, 4], [99, 6, 105, 2], [100, 6, 106, 2], [100, 13, 106, 9], [100, 17, 106, 13], [101, 4, 107, 0], [101, 5, 107, 1], [102, 4, 107, 1, "shallowEqual"], [102, 16, 107, 1], [102, 17, 107, 1, "__closure"], [102, 26, 107, 1], [103, 4, 107, 1, "shallowEqual"], [103, 16, 107, 1], [103, 17, 107, 1, "__workletHash"], [103, 30, 107, 1], [104, 4, 107, 1, "shallowEqual"], [104, 16, 107, 1], [104, 17, 107, 1, "__initData"], [104, 27, 107, 1], [104, 30, 107, 1, "_worklet_2158856734967_init_data"], [104, 62, 107, 1], [105, 4, 107, 1, "shallowEqual"], [105, 16, 107, 1], [105, 17, 107, 1, "__stackDetails"], [105, 31, 107, 1], [105, 34, 107, 1, "_e"], [105, 36, 107, 1], [106, 4, 107, 1], [106, 11, 107, 1, "shallowEqual"], [106, 23, 107, 1], [107, 2, 107, 1], [107, 3, 92, 7], [108, 2, 92, 7], [108, 6, 92, 7, "_worklet_11676986094821_init_data"], [108, 39, 92, 7], [109, 4, 92, 7, "code"], [109, 8, 92, 7], [110, 4, 92, 7, "location"], [110, 12, 92, 7], [111, 4, 92, 7, "sourceMap"], [111, 13, 92, 7], [112, 4, 92, 7, "version"], [112, 11, 92, 7], [113, 2, 92, 7], [114, 2, 92, 7], [114, 6, 92, 7, "validateAnimatedStyles"], [114, 28, 92, 7], [114, 31, 92, 7, "exports"], [114, 38, 92, 7], [114, 39, 92, 7, "validateAnimatedStyles"], [114, 61, 92, 7], [114, 64, 109, 7], [115, 4, 109, 7], [115, 8, 109, 7, "_e"], [115, 10, 109, 7], [115, 18, 109, 7, "global"], [115, 24, 109, 7], [115, 25, 109, 7, "Error"], [115, 30, 109, 7], [116, 4, 109, 7], [116, 8, 109, 7, "validateAnimatedStyles"], [116, 30, 109, 7], [116, 42, 109, 7, "validateAnimatedStyles"], [116, 43, 109, 39, "styles"], [116, 49, 109, 65], [116, 51, 109, 67], [117, 6, 111, 2], [117, 10, 111, 6], [117, 17, 111, 13, "styles"], [117, 23, 111, 19], [117, 28, 111, 24], [117, 36, 111, 32], [117, 38, 111, 34], [118, 8, 112, 4], [118, 14, 112, 10], [118, 18, 112, 14, "ReanimatedError"], [118, 41, 112, 29], [118, 42, 113, 6], [118, 97, 113, 61], [118, 104, 113, 68, "styles"], [118, 110, 113, 74], [118, 121, 114, 4], [118, 122, 114, 5], [119, 6, 115, 2], [119, 7, 115, 3], [119, 13, 115, 9], [119, 17, 115, 13, "Array"], [119, 22, 115, 18], [119, 23, 115, 19, "isArray"], [119, 30, 115, 26], [119, 31, 115, 27, "styles"], [119, 37, 115, 33], [119, 38, 115, 34], [119, 40, 115, 36], [120, 8, 116, 4], [120, 14, 116, 10], [120, 18, 116, 14, "ReanimatedError"], [120, 41, 116, 29], [120, 42, 117, 6], [120, 198, 118, 4], [120, 199, 118, 5], [121, 6, 119, 2], [122, 4, 120, 0], [122, 5, 120, 1], [123, 4, 120, 1, "validateAnimatedStyles"], [123, 26, 120, 1], [123, 27, 120, 1, "__closure"], [123, 36, 120, 1], [124, 4, 120, 1, "validateAnimatedStyles"], [124, 26, 120, 1], [124, 27, 120, 1, "__workletHash"], [124, 40, 120, 1], [125, 4, 120, 1, "validateAnimatedStyles"], [125, 26, 120, 1], [125, 27, 120, 1, "__initData"], [125, 37, 120, 1], [125, 40, 120, 1, "_worklet_11676986094821_init_data"], [125, 73, 120, 1], [126, 4, 120, 1, "validateAnimatedStyles"], [126, 26, 120, 1], [126, 27, 120, 1, "__stackDetails"], [126, 41, 120, 1], [126, 44, 120, 1, "_e"], [126, 46, 120, 1], [127, 4, 120, 1], [127, 11, 120, 1, "validateAnimatedStyles"], [127, 33, 120, 1], [128, 2, 120, 1], [128, 3, 109, 7], [129, 0, 109, 7], [129, 3]], "functionMap": {"names": ["<global>", "buildWorkletsHash", "Object.values.reduce$argument_0", "buildDependencies", "Object.values.filter$argument_0", "handlersList.map$argument_0", "areDependenciesEqual", "is", "areHookInputsEqual", "isAnimated", "shallowEqual", "validateAnimatedStyles"], "mappings": "AAA;OCM;ICO;4CDC;CDG;OGG;ICM,kCD;oCEG;KFK;CHM;OMG;ECI;GDK;EEI;GFa;CNG;OSE;CTY;OUK;CVe;OWE;CXW"}}, "type": "js/module"}]}