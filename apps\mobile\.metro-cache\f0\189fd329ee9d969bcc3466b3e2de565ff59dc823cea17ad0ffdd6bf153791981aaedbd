{"dependencies": [{"name": "react-is", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 3, "column": 14, "index": 29}, "end": {"line": 3, "column": 33, "index": 48}}], "key": "hoZupclAije+HbYquo78nDVN6Z8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var reactIs = require(_dependencyMap[0], \"react-is\");\n\n  /**\n   * Copyright 2015, Yahoo! Inc.\n   * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n   */\n  var REACT_STATICS = {\n    childContextTypes: true,\n    contextType: true,\n    contextTypes: true,\n    defaultProps: true,\n    displayName: true,\n    getDefaultProps: true,\n    getDerivedStateFromError: true,\n    getDerivedStateFromProps: true,\n    mixins: true,\n    propTypes: true,\n    type: true\n  };\n  var KNOWN_STATICS = {\n    name: true,\n    length: true,\n    prototype: true,\n    caller: true,\n    callee: true,\n    arguments: true,\n    arity: true\n  };\n  var FORWARD_REF_STATICS = {\n    '$$typeof': true,\n    render: true,\n    defaultProps: true,\n    displayName: true,\n    propTypes: true\n  };\n  var MEMO_STATICS = {\n    '$$typeof': true,\n    compare: true,\n    defaultProps: true,\n    displayName: true,\n    propTypes: true,\n    type: true\n  };\n  var TYPE_STATICS = {};\n  TYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;\n  TYPE_STATICS[reactIs.Memo] = MEMO_STATICS;\n  function getStatics(component) {\n    // React v16.11 and below\n    if (reactIs.isMemo(component)) {\n      return MEMO_STATICS;\n    } // React v16.12 and above\n\n    return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n  }\n  var defineProperty = Object.defineProperty;\n  var getOwnPropertyNames = Object.getOwnPropertyNames;\n  var getOwnPropertySymbols = Object.getOwnPropertySymbols;\n  var getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n  var getPrototypeOf = Object.getPrototypeOf;\n  var objectPrototype = Object.prototype;\n  function hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n    if (typeof sourceComponent !== 'string') {\n      // don't hoist over string (html) components\n      if (objectPrototype) {\n        var inheritedComponent = getPrototypeOf(sourceComponent);\n        if (inheritedComponent && inheritedComponent !== objectPrototype) {\n          hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n        }\n      }\n      var keys = getOwnPropertyNames(sourceComponent);\n      if (getOwnPropertySymbols) {\n        keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n      }\n      var targetStatics = getStatics(targetComponent);\n      var sourceStatics = getStatics(sourceComponent);\n      for (var i = 0; i < keys.length; ++i) {\n        var key = keys[i];\n        if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n          var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n          try {\n            // Avoid failures from read-only properties\n            defineProperty(targetComponent, key, descriptor);\n          } catch (e) {}\n        }\n      }\n    }\n    return targetComponent;\n  }\n  module.exports = hoistNonReactStatics;\n});", "lineCount": 93, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [4, 6, 3, 4, "reactIs"], [4, 13, 3, 11], [4, 16, 3, 14, "require"], [4, 23, 3, 21], [4, 24, 3, 21, "_dependencyMap"], [4, 38, 3, 21], [4, 53, 3, 32], [4, 54, 3, 33], [6, 2, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 2, 9, 0], [10, 6, 9, 4, "REACT_STATICS"], [10, 19, 9, 17], [10, 22, 9, 20], [11, 4, 10, 2, "childContextTypes"], [11, 21, 10, 19], [11, 23, 10, 21], [11, 27, 10, 25], [12, 4, 11, 2, "contextType"], [12, 15, 11, 13], [12, 17, 11, 15], [12, 21, 11, 19], [13, 4, 12, 2, "contextTypes"], [13, 16, 12, 14], [13, 18, 12, 16], [13, 22, 12, 20], [14, 4, 13, 2, "defaultProps"], [14, 16, 13, 14], [14, 18, 13, 16], [14, 22, 13, 20], [15, 4, 14, 2, "displayName"], [15, 15, 14, 13], [15, 17, 14, 15], [15, 21, 14, 19], [16, 4, 15, 2, "getDefaultProps"], [16, 19, 15, 17], [16, 21, 15, 19], [16, 25, 15, 23], [17, 4, 16, 2, "getDerivedStateFromError"], [17, 28, 16, 26], [17, 30, 16, 28], [17, 34, 16, 32], [18, 4, 17, 2, "getDerivedStateFromProps"], [18, 28, 17, 26], [18, 30, 17, 28], [18, 34, 17, 32], [19, 4, 18, 2, "mixins"], [19, 10, 18, 8], [19, 12, 18, 10], [19, 16, 18, 14], [20, 4, 19, 2, "propTypes"], [20, 13, 19, 11], [20, 15, 19, 13], [20, 19, 19, 17], [21, 4, 20, 2, "type"], [21, 8, 20, 6], [21, 10, 20, 8], [22, 2, 21, 0], [22, 3, 21, 1], [23, 2, 22, 0], [23, 6, 22, 4, "KNOWN_STATICS"], [23, 19, 22, 17], [23, 22, 22, 20], [24, 4, 23, 2, "name"], [24, 8, 23, 6], [24, 10, 23, 8], [24, 14, 23, 12], [25, 4, 24, 2, "length"], [25, 10, 24, 8], [25, 12, 24, 10], [25, 16, 24, 14], [26, 4, 25, 2, "prototype"], [26, 13, 25, 11], [26, 15, 25, 13], [26, 19, 25, 17], [27, 4, 26, 2, "caller"], [27, 10, 26, 8], [27, 12, 26, 10], [27, 16, 26, 14], [28, 4, 27, 2, "callee"], [28, 10, 27, 8], [28, 12, 27, 10], [28, 16, 27, 14], [29, 4, 28, 2, "arguments"], [29, 13, 28, 11], [29, 15, 28, 13], [29, 19, 28, 17], [30, 4, 29, 2, "arity"], [30, 9, 29, 7], [30, 11, 29, 9], [31, 2, 30, 0], [31, 3, 30, 1], [32, 2, 31, 0], [32, 6, 31, 4, "FORWARD_REF_STATICS"], [32, 25, 31, 23], [32, 28, 31, 26], [33, 4, 32, 2], [33, 14, 32, 12], [33, 16, 32, 14], [33, 20, 32, 18], [34, 4, 33, 2, "render"], [34, 10, 33, 8], [34, 12, 33, 10], [34, 16, 33, 14], [35, 4, 34, 2, "defaultProps"], [35, 16, 34, 14], [35, 18, 34, 16], [35, 22, 34, 20], [36, 4, 35, 2, "displayName"], [36, 15, 35, 13], [36, 17, 35, 15], [36, 21, 35, 19], [37, 4, 36, 2, "propTypes"], [37, 13, 36, 11], [37, 15, 36, 13], [38, 2, 37, 0], [38, 3, 37, 1], [39, 2, 38, 0], [39, 6, 38, 4, "MEMO_STATICS"], [39, 18, 38, 16], [39, 21, 38, 19], [40, 4, 39, 2], [40, 14, 39, 12], [40, 16, 39, 14], [40, 20, 39, 18], [41, 4, 40, 2, "compare"], [41, 11, 40, 9], [41, 13, 40, 11], [41, 17, 40, 15], [42, 4, 41, 2, "defaultProps"], [42, 16, 41, 14], [42, 18, 41, 16], [42, 22, 41, 20], [43, 4, 42, 2, "displayName"], [43, 15, 42, 13], [43, 17, 42, 15], [43, 21, 42, 19], [44, 4, 43, 2, "propTypes"], [44, 13, 43, 11], [44, 15, 43, 13], [44, 19, 43, 17], [45, 4, 44, 2, "type"], [45, 8, 44, 6], [45, 10, 44, 8], [46, 2, 45, 0], [46, 3, 45, 1], [47, 2, 46, 0], [47, 6, 46, 4, "TYPE_STATICS"], [47, 18, 46, 16], [47, 21, 46, 19], [47, 22, 46, 20], [47, 23, 46, 21], [48, 2, 47, 0, "TYPE_STATICS"], [48, 14, 47, 12], [48, 15, 47, 13, "reactIs"], [48, 22, 47, 20], [48, 23, 47, 21, "ForwardRef"], [48, 33, 47, 31], [48, 34, 47, 32], [48, 37, 47, 35, "FORWARD_REF_STATICS"], [48, 56, 47, 54], [49, 2, 48, 0, "TYPE_STATICS"], [49, 14, 48, 12], [49, 15, 48, 13, "reactIs"], [49, 22, 48, 20], [49, 23, 48, 21, "Memo"], [49, 27, 48, 25], [49, 28, 48, 26], [49, 31, 48, 29, "MEMO_STATICS"], [49, 43, 48, 41], [50, 2, 50, 0], [50, 11, 50, 9, "getStatics"], [50, 21, 50, 19, "getStatics"], [50, 22, 50, 20, "component"], [50, 31, 50, 29], [50, 33, 50, 31], [51, 4, 51, 2], [52, 4, 52, 2], [52, 8, 52, 6, "reactIs"], [52, 15, 52, 13], [52, 16, 52, 14, "isMemo"], [52, 22, 52, 20], [52, 23, 52, 21, "component"], [52, 32, 52, 30], [52, 33, 52, 31], [52, 35, 52, 33], [53, 6, 53, 4], [53, 13, 53, 11, "MEMO_STATICS"], [53, 25, 53, 23], [54, 4, 54, 2], [54, 5, 54, 3], [54, 6, 54, 4], [56, 4, 57, 2], [56, 11, 57, 9, "TYPE_STATICS"], [56, 23, 57, 21], [56, 24, 57, 22, "component"], [56, 33, 57, 31], [56, 34, 57, 32], [56, 44, 57, 42], [56, 45, 57, 43], [56, 46, 57, 44], [56, 50, 57, 48, "REACT_STATICS"], [56, 63, 57, 61], [57, 2, 58, 0], [58, 2, 60, 0], [58, 6, 60, 4, "defineProperty"], [58, 20, 60, 18], [58, 23, 60, 21, "Object"], [58, 29, 60, 27], [58, 30, 60, 28, "defineProperty"], [58, 44, 60, 42], [59, 2, 61, 0], [59, 6, 61, 4, "getOwnPropertyNames"], [59, 25, 61, 23], [59, 28, 61, 26, "Object"], [59, 34, 61, 32], [59, 35, 61, 33, "getOwnPropertyNames"], [59, 54, 61, 52], [60, 2, 62, 0], [60, 6, 62, 4, "getOwnPropertySymbols"], [60, 27, 62, 25], [60, 30, 62, 28, "Object"], [60, 36, 62, 34], [60, 37, 62, 35, "getOwnPropertySymbols"], [60, 58, 62, 56], [61, 2, 63, 0], [61, 6, 63, 4, "getOwnPropertyDescriptor"], [61, 30, 63, 28], [61, 33, 63, 31, "Object"], [61, 39, 63, 37], [61, 40, 63, 38, "getOwnPropertyDescriptor"], [61, 64, 63, 62], [62, 2, 64, 0], [62, 6, 64, 4, "getPrototypeOf"], [62, 20, 64, 18], [62, 23, 64, 21, "Object"], [62, 29, 64, 27], [62, 30, 64, 28, "getPrototypeOf"], [62, 44, 64, 42], [63, 2, 65, 0], [63, 6, 65, 4, "objectPrototype"], [63, 21, 65, 19], [63, 24, 65, 22, "Object"], [63, 30, 65, 28], [63, 31, 65, 29, "prototype"], [63, 40, 65, 38], [64, 2, 66, 0], [64, 11, 66, 9, "hoistNonReactStatics"], [64, 31, 66, 29, "hoistNonReactStatics"], [64, 32, 66, 30, "targetComponent"], [64, 47, 66, 45], [64, 49, 66, 47, "sourceComponent"], [64, 64, 66, 62], [64, 66, 66, 64, "blacklist"], [64, 75, 66, 73], [64, 77, 66, 75], [65, 4, 67, 2], [65, 8, 67, 6], [65, 15, 67, 13, "sourceComponent"], [65, 30, 67, 28], [65, 35, 67, 33], [65, 43, 67, 41], [65, 45, 67, 43], [66, 6, 68, 4], [67, 6, 69, 4], [67, 10, 69, 8, "objectPrototype"], [67, 25, 69, 23], [67, 27, 69, 25], [68, 8, 70, 6], [68, 12, 70, 10, "inheritedComponent"], [68, 30, 70, 28], [68, 33, 70, 31, "getPrototypeOf"], [68, 47, 70, 45], [68, 48, 70, 46, "sourceComponent"], [68, 63, 70, 61], [68, 64, 70, 62], [69, 8, 72, 6], [69, 12, 72, 10, "inheritedComponent"], [69, 30, 72, 28], [69, 34, 72, 32, "inheritedComponent"], [69, 52, 72, 50], [69, 57, 72, 55, "objectPrototype"], [69, 72, 72, 70], [69, 74, 72, 72], [70, 10, 73, 8, "hoistNonReactStatics"], [70, 30, 73, 28], [70, 31, 73, 29, "targetComponent"], [70, 46, 73, 44], [70, 48, 73, 46, "inheritedComponent"], [70, 66, 73, 64], [70, 68, 73, 66, "blacklist"], [70, 77, 73, 75], [70, 78, 73, 76], [71, 8, 74, 6], [72, 6, 75, 4], [73, 6, 77, 4], [73, 10, 77, 8, "keys"], [73, 14, 77, 12], [73, 17, 77, 15, "getOwnPropertyNames"], [73, 36, 77, 34], [73, 37, 77, 35, "sourceComponent"], [73, 52, 77, 50], [73, 53, 77, 51], [74, 6, 79, 4], [74, 10, 79, 8, "getOwnPropertySymbols"], [74, 31, 79, 29], [74, 33, 79, 31], [75, 8, 80, 6, "keys"], [75, 12, 80, 10], [75, 15, 80, 13, "keys"], [75, 19, 80, 17], [75, 20, 80, 18, "concat"], [75, 26, 80, 24], [75, 27, 80, 25, "getOwnPropertySymbols"], [75, 48, 80, 46], [75, 49, 80, 47, "sourceComponent"], [75, 64, 80, 62], [75, 65, 80, 63], [75, 66, 80, 64], [76, 6, 81, 4], [77, 6, 83, 4], [77, 10, 83, 8, "targetStatics"], [77, 23, 83, 21], [77, 26, 83, 24, "getStatics"], [77, 36, 83, 34], [77, 37, 83, 35, "targetComponent"], [77, 52, 83, 50], [77, 53, 83, 51], [78, 6, 84, 4], [78, 10, 84, 8, "sourceStatics"], [78, 23, 84, 21], [78, 26, 84, 24, "getStatics"], [78, 36, 84, 34], [78, 37, 84, 35, "sourceComponent"], [78, 52, 84, 50], [78, 53, 84, 51], [79, 6, 86, 4], [79, 11, 86, 9], [79, 15, 86, 13, "i"], [79, 16, 86, 14], [79, 19, 86, 17], [79, 20, 86, 18], [79, 22, 86, 20, "i"], [79, 23, 86, 21], [79, 26, 86, 24, "keys"], [79, 30, 86, 28], [79, 31, 86, 29, "length"], [79, 37, 86, 35], [79, 39, 86, 37], [79, 41, 86, 39, "i"], [79, 42, 86, 40], [79, 44, 86, 42], [80, 8, 87, 6], [80, 12, 87, 10, "key"], [80, 15, 87, 13], [80, 18, 87, 16, "keys"], [80, 22, 87, 20], [80, 23, 87, 21, "i"], [80, 24, 87, 22], [80, 25, 87, 23], [81, 8, 89, 6], [81, 12, 89, 10], [81, 13, 89, 11, "KNOWN_STATICS"], [81, 26, 89, 24], [81, 27, 89, 25, "key"], [81, 30, 89, 28], [81, 31, 89, 29], [81, 35, 89, 33], [81, 37, 89, 35, "blacklist"], [81, 46, 89, 44], [81, 50, 89, 48, "blacklist"], [81, 59, 89, 57], [81, 60, 89, 58, "key"], [81, 63, 89, 61], [81, 64, 89, 62], [81, 65, 89, 63], [81, 69, 89, 67], [81, 71, 89, 69, "sourceStatics"], [81, 84, 89, 82], [81, 88, 89, 86, "sourceStatics"], [81, 101, 89, 99], [81, 102, 89, 100, "key"], [81, 105, 89, 103], [81, 106, 89, 104], [81, 107, 89, 105], [81, 111, 89, 109], [81, 113, 89, 111, "targetStatics"], [81, 126, 89, 124], [81, 130, 89, 128, "targetStatics"], [81, 143, 89, 141], [81, 144, 89, 142, "key"], [81, 147, 89, 145], [81, 148, 89, 146], [81, 149, 89, 147], [81, 151, 89, 149], [82, 10, 90, 8], [82, 14, 90, 12, "descriptor"], [82, 24, 90, 22], [82, 27, 90, 25, "getOwnPropertyDescriptor"], [82, 51, 90, 49], [82, 52, 90, 50, "sourceComponent"], [82, 67, 90, 65], [82, 69, 90, 67, "key"], [82, 72, 90, 70], [82, 73, 90, 71], [83, 10, 92, 8], [83, 14, 92, 12], [84, 12, 93, 10], [85, 12, 94, 10, "defineProperty"], [85, 26, 94, 24], [85, 27, 94, 25, "targetComponent"], [85, 42, 94, 40], [85, 44, 94, 42, "key"], [85, 47, 94, 45], [85, 49, 94, 47, "descriptor"], [85, 59, 94, 57], [85, 60, 94, 58], [86, 10, 95, 8], [86, 11, 95, 9], [86, 12, 95, 10], [86, 19, 95, 17, "e"], [86, 20, 95, 18], [86, 22, 95, 20], [86, 23, 95, 21], [87, 8, 96, 6], [88, 6, 97, 4], [89, 4, 98, 2], [90, 4, 100, 2], [90, 11, 100, 9, "targetComponent"], [90, 26, 100, 24], [91, 2, 101, 0], [92, 2, 103, 0, "module"], [92, 8, 103, 6], [92, 9, 103, 7, "exports"], [92, 16, 103, 14], [92, 19, 103, 17, "hoistNonReactStatics"], [92, 39, 103, 37], [93, 0, 103, 38], [93, 3]], "functionMap": {"names": ["<global>", "getStatics", "hoistNonReactStatics"], "mappings": "AAA;ACiD;CDQ;AEQ;CFmC"}}, "type": "js/module"}]}