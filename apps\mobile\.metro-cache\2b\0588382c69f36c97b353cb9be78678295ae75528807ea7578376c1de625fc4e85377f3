{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./GenericTouchable", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 75}, "end": {"line": 3, "column": 50, "index": 125}}], "key": "b7B+HFZ7s4hNhosUwPttECYmJ2U=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _GenericTouchable = _interopRequireDefault(require(_dependencyMap[3], \"./GenericTouchable\"));\n  var _jsxDevRuntime = require(_dependencyMap[4], \"react/jsx-dev-runtime\");\n  var _excluded = [\"delayLongPress\", \"extraButtonProps\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\components\\\\touchables\\\\TouchableWithoutFeedback.tsx\";\n  /**\n   * @deprecated TouchableWithoutFeedback will be removed in the future version of Gesture Handler. Use Pressable instead.\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * @deprecated TouchableWithoutFeedback will be removed in the future version of Gesture Handler. Use Pressable instead.\n   */\n  var TouchableWithoutFeedback = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n    var _ref$delayLongPress = _ref.delayLongPress,\n      delayLongPress = _ref$delayLongPress === void 0 ? 600 : _ref$delayLongPress,\n      _ref$extraButtonProps = _ref.extraButtonProps,\n      extraButtonProps = _ref$extraButtonProps === void 0 ? {\n        rippleColor: 'transparent',\n        exclusive: true\n      } : _ref$extraButtonProps,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_GenericTouchable.default, {\n      ref: ref,\n      delayLongPress: delayLongPress,\n      extraButtonProps: extraButtonProps,\n      ...rest\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 5\n    }, this);\n  });\n  var _default = exports.default = TouchableWithoutFeedback;\n});", "lineCount": 41, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "React"], [8, 11, 1, 0], [8, 14, 1, 0, "_interopRequireWildcard"], [8, 37, 1, 0], [8, 38, 1, 0, "require"], [8, 45, 1, 0], [8, 46, 1, 0, "_dependencyMap"], [8, 60, 1, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_GenericTouchable"], [9, 23, 3, 0], [9, 26, 3, 0, "_interopRequireDefault"], [9, 48, 3, 0], [9, 49, 3, 0, "require"], [9, 56, 3, 0], [9, 57, 3, 0, "_dependencyMap"], [9, 71, 3, 0], [10, 2, 3, 50], [10, 6, 3, 50, "_jsxDevRuntime"], [10, 20, 3, 50], [10, 23, 3, 50, "require"], [10, 30, 3, 50], [10, 31, 3, 50, "_dependencyMap"], [10, 45, 3, 50], [11, 2, 3, 50], [11, 6, 3, 50, "_excluded"], [11, 15, 3, 50], [12, 2, 3, 50], [12, 6, 3, 50, "_jsxFileName"], [12, 18, 3, 50], [13, 2, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [16, 2, 6, 0], [16, 11, 6, 0, "_interopRequireWildcard"], [16, 35, 6, 0, "e"], [16, 36, 6, 0], [16, 38, 6, 0, "t"], [16, 39, 6, 0], [16, 68, 6, 0, "WeakMap"], [16, 75, 6, 0], [16, 81, 6, 0, "r"], [16, 82, 6, 0], [16, 89, 6, 0, "WeakMap"], [16, 96, 6, 0], [16, 100, 6, 0, "n"], [16, 101, 6, 0], [16, 108, 6, 0, "WeakMap"], [16, 115, 6, 0], [16, 127, 6, 0, "_interopRequireWildcard"], [16, 150, 6, 0], [16, 162, 6, 0, "_interopRequireWildcard"], [16, 163, 6, 0, "e"], [16, 164, 6, 0], [16, 166, 6, 0, "t"], [16, 167, 6, 0], [16, 176, 6, 0, "t"], [16, 177, 6, 0], [16, 181, 6, 0, "e"], [16, 182, 6, 0], [16, 186, 6, 0, "e"], [16, 187, 6, 0], [16, 188, 6, 0, "__esModule"], [16, 198, 6, 0], [16, 207, 6, 0, "e"], [16, 208, 6, 0], [16, 214, 6, 0, "o"], [16, 215, 6, 0], [16, 217, 6, 0, "i"], [16, 218, 6, 0], [16, 220, 6, 0, "f"], [16, 221, 6, 0], [16, 226, 6, 0, "__proto__"], [16, 235, 6, 0], [16, 243, 6, 0, "default"], [16, 250, 6, 0], [16, 252, 6, 0, "e"], [16, 253, 6, 0], [16, 270, 6, 0, "e"], [16, 271, 6, 0], [16, 294, 6, 0, "e"], [16, 295, 6, 0], [16, 320, 6, 0, "e"], [16, 321, 6, 0], [16, 330, 6, 0, "f"], [16, 331, 6, 0], [16, 337, 6, 0, "o"], [16, 338, 6, 0], [16, 341, 6, 0, "t"], [16, 342, 6, 0], [16, 345, 6, 0, "n"], [16, 346, 6, 0], [16, 349, 6, 0, "r"], [16, 350, 6, 0], [16, 358, 6, 0, "o"], [16, 359, 6, 0], [16, 360, 6, 0, "has"], [16, 363, 6, 0], [16, 364, 6, 0, "e"], [16, 365, 6, 0], [16, 375, 6, 0, "o"], [16, 376, 6, 0], [16, 377, 6, 0, "get"], [16, 380, 6, 0], [16, 381, 6, 0, "e"], [16, 382, 6, 0], [16, 385, 6, 0, "o"], [16, 386, 6, 0], [16, 387, 6, 0, "set"], [16, 390, 6, 0], [16, 391, 6, 0, "e"], [16, 392, 6, 0], [16, 394, 6, 0, "f"], [16, 395, 6, 0], [16, 409, 6, 0, "_t"], [16, 411, 6, 0], [16, 415, 6, 0, "e"], [16, 416, 6, 0], [16, 432, 6, 0, "_t"], [16, 434, 6, 0], [16, 441, 6, 0, "hasOwnProperty"], [16, 455, 6, 0], [16, 456, 6, 0, "call"], [16, 460, 6, 0], [16, 461, 6, 0, "e"], [16, 462, 6, 0], [16, 464, 6, 0, "_t"], [16, 466, 6, 0], [16, 473, 6, 0, "i"], [16, 474, 6, 0], [16, 478, 6, 0, "o"], [16, 479, 6, 0], [16, 482, 6, 0, "Object"], [16, 488, 6, 0], [16, 489, 6, 0, "defineProperty"], [16, 503, 6, 0], [16, 508, 6, 0, "Object"], [16, 514, 6, 0], [16, 515, 6, 0, "getOwnPropertyDescriptor"], [16, 539, 6, 0], [16, 540, 6, 0, "e"], [16, 541, 6, 0], [16, 543, 6, 0, "_t"], [16, 545, 6, 0], [16, 552, 6, 0, "i"], [16, 553, 6, 0], [16, 554, 6, 0, "get"], [16, 557, 6, 0], [16, 561, 6, 0, "i"], [16, 562, 6, 0], [16, 563, 6, 0, "set"], [16, 566, 6, 0], [16, 570, 6, 0, "o"], [16, 571, 6, 0], [16, 572, 6, 0, "f"], [16, 573, 6, 0], [16, 575, 6, 0, "_t"], [16, 577, 6, 0], [16, 579, 6, 0, "i"], [16, 580, 6, 0], [16, 584, 6, 0, "f"], [16, 585, 6, 0], [16, 586, 6, 0, "_t"], [16, 588, 6, 0], [16, 592, 6, 0, "e"], [16, 593, 6, 0], [16, 594, 6, 0, "_t"], [16, 596, 6, 0], [16, 607, 6, 0, "f"], [16, 608, 6, 0], [16, 613, 6, 0, "e"], [16, 614, 6, 0], [16, 616, 6, 0, "t"], [16, 617, 6, 0], [17, 2, 11, 0], [18, 0, 12, 0], [19, 0, 13, 0], [20, 2, 14, 0], [20, 6, 14, 6, "TouchableWithoutFeedback"], [20, 30, 14, 30], [20, 46, 14, 33, "React"], [20, 51, 14, 38], [20, 52, 14, 39, "forwardRef"], [20, 62, 14, 49], [20, 63, 18, 2], [20, 64, 18, 2, "_ref"], [20, 68, 18, 2], [20, 70, 28, 4, "ref"], [20, 73, 28, 7], [21, 4, 28, 7], [21, 8, 28, 7, "_ref$delayLongPress"], [21, 27, 28, 7], [21, 30, 28, 7, "_ref"], [21, 34, 28, 7], [21, 35, 20, 6, "delayLongPress"], [21, 49, 20, 20], [22, 6, 20, 6, "delayLongPress"], [22, 20, 20, 20], [22, 23, 20, 20, "_ref$delayLongPress"], [22, 42, 20, 20], [22, 56, 20, 23], [22, 59, 20, 26], [22, 62, 20, 26, "_ref$delayLongPress"], [22, 81, 20, 26], [23, 6, 20, 26, "_ref$extraButtonProps"], [23, 27, 20, 26], [23, 30, 20, 26, "_ref"], [23, 34, 20, 26], [23, 35, 21, 6, "extraButtonProps"], [23, 51, 21, 22], [24, 6, 21, 6, "extraButtonProps"], [24, 22, 21, 22], [24, 25, 21, 22, "_ref$extraButtonProps"], [24, 46, 21, 22], [24, 60, 21, 25], [25, 8, 22, 8, "rippleColor"], [25, 19, 22, 19], [25, 21, 22, 21], [25, 34, 22, 34], [26, 8, 23, 8, "exclusive"], [26, 17, 23, 17], [26, 19, 23, 19], [27, 6, 24, 6], [27, 7, 24, 7], [27, 10, 24, 7, "_ref$extraButtonProps"], [27, 31, 24, 7], [28, 6, 25, 9, "rest"], [28, 10, 25, 13], [28, 17, 25, 13, "_objectWithoutProperties2"], [28, 42, 25, 13], [28, 43, 25, 13, "default"], [28, 50, 25, 13], [28, 52, 25, 13, "_ref"], [28, 56, 25, 13], [28, 58, 25, 13, "_excluded"], [28, 67, 25, 13], [29, 4, 25, 13], [29, 24, 30, 4], [29, 28, 30, 4, "_jsxDevRuntime"], [29, 42, 30, 4], [29, 43, 30, 4, "jsxDEV"], [29, 49, 30, 4], [29, 51, 30, 5, "_GenericTouchable"], [29, 68, 30, 5], [29, 69, 30, 5, "default"], [29, 76, 30, 21], [30, 6, 31, 6, "ref"], [30, 9, 31, 9], [30, 11, 31, 11, "ref"], [30, 14, 31, 15], [31, 6, 32, 6, "delayLongPress"], [31, 20, 32, 20], [31, 22, 32, 22, "delayLongPress"], [31, 36, 32, 37], [32, 6, 33, 6, "extraButtonProps"], [32, 22, 33, 22], [32, 24, 33, 24, "extraButtonProps"], [32, 40, 33, 41], [33, 6, 33, 41], [33, 9, 34, 10, "rest"], [34, 4, 34, 14], [35, 6, 34, 14, "fileName"], [35, 14, 34, 14], [35, 16, 34, 14, "_jsxFileName"], [35, 28, 34, 14], [36, 6, 34, 14, "lineNumber"], [36, 16, 34, 14], [37, 6, 34, 14, "columnNumber"], [37, 18, 34, 14], [38, 4, 34, 14], [38, 11, 35, 5], [38, 12, 35, 6], [39, 2, 35, 6], [39, 3, 37, 0], [39, 4, 37, 1], [40, 2, 37, 2], [40, 6, 37, 2, "_default"], [40, 14, 37, 2], [40, 17, 37, 2, "exports"], [40, 24, 37, 2], [40, 25, 37, 2, "default"], [40, 32, 37, 2], [40, 35, 39, 15, "TouchableWithoutFeedback"], [40, 59, 39, 39], [41, 0, 39, 39], [41, 3]], "functionMap": {"names": ["<global>", "React.forwardRef$argument_0"], "mappings": "AAA;ECiB;GDkB"}}, "type": "js/module"}]}