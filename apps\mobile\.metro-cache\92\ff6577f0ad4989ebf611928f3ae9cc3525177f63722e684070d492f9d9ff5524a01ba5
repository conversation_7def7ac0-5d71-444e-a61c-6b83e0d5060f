{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 73, "index": 120}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}, {"name": "./NavigationContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 121}, "end": {"line": 5, "column": 59, "index": 180}}], "key": "RM0XoJ1uy5+hqq85ZlLNt6FYuco=", "exportNames": ["*"]}}, {"name": "./NavigationRouteContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 181}, "end": {"line": 6, "column": 69, "index": 250}}], "key": "AWXnpGNA5UkH1qQUM7hLv2L9KzI=", "exportNames": ["*"]}}, {"name": "./SceneView.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 251}, "end": {"line": 7, "column": 43, "index": 294}}], "key": "FvyKlHSheioqPkFrfVVDYQbCJP4=", "exportNames": ["*"]}}, {"name": "./theming/ThemeContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 295}, "end": {"line": 8, "column": 57, "index": 352}}], "key": "qlk5yrcKdN2V0KhKMRE4Vd3Zk/8=", "exportNames": ["*"]}}, {"name": "./useNavigationCache.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 353}, "end": {"line": 9, "column": 61, "index": 414}}], "key": "BIFS8wPXcRN7UboMxphJ4rGKLe4=", "exportNames": ["*"]}}, {"name": "./useRouteCache.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 415}, "end": {"line": 10, "column": 51, "index": 466}}], "key": "7Wwgyr1YruLrWKgk0bZ/DNlglOg=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 467}, "end": {"line": 11, "column": 48, "index": 515}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDescriptors = useDescriptors;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/slicedToArray\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _NavigationBuilderContext = require(_dependencyMap[4], \"./NavigationBuilderContext.js\");\n  var _NavigationContext = require(_dependencyMap[5], \"./NavigationContext.js\");\n  var _NavigationRouteContext = require(_dependencyMap[6], \"./NavigationRouteContext.js\");\n  var _SceneView = require(_dependencyMap[7], \"./SceneView.js\");\n  var _ThemeContext = require(_dependencyMap[8], \"./theming/ThemeContext.js\");\n  var _useNavigationCache2 = require(_dependencyMap[9], \"./useNavigationCache.js\");\n  var _useRouteCache = require(_dependencyMap[10], \"./useRouteCache.js\");\n  var _jsxRuntime = require(_dependencyMap[11], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\n  function _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n  /**\n   * Hook to create descriptor objects for the child routes.\n   *\n   * A descriptor object provides 3 things:\n   * - Helper method to render a screen\n   * - Options specified by the screen for the navigator\n   * - Navigation object intended for the route\n   */\n  function useDescriptors(_ref) {\n    var state = _ref.state,\n      screens = _ref.screens,\n      navigation = _ref.navigation,\n      screenOptions = _ref.screenOptions,\n      screenLayout = _ref.screenLayout,\n      onAction = _ref.onAction,\n      getState = _ref.getState,\n      setState = _ref.setState,\n      addListener = _ref.addListener,\n      addKeyedListener = _ref.addKeyedListener,\n      onRouteFocus = _ref.onRouteFocus,\n      router = _ref.router,\n      emitter = _ref.emitter;\n    var theme = React.useContext(_ThemeContext.ThemeContext);\n    var _React$useState = React.useState({}),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      options = _React$useState2[0],\n      setOptions = _React$useState2[1];\n    var _React$useContext = React.useContext(_NavigationBuilderContext.NavigationBuilderContext),\n      onDispatchAction = _React$useContext.onDispatchAction,\n      onOptionsChange = _React$useContext.onOptionsChange,\n      scheduleUpdate = _React$useContext.scheduleUpdate,\n      flushUpdates = _React$useContext.flushUpdates,\n      stackRef = _React$useContext.stackRef;\n    var context = React.useMemo(() => ({\n      navigation,\n      onAction,\n      addListener,\n      addKeyedListener,\n      onRouteFocus,\n      onDispatchAction,\n      onOptionsChange,\n      scheduleUpdate,\n      flushUpdates,\n      stackRef\n    }), [navigation, onAction, addListener, addKeyedListener, onRouteFocus, onDispatchAction, onOptionsChange, scheduleUpdate, flushUpdates, stackRef]);\n    var _useNavigationCache = (0, _useNavigationCache2.useNavigationCache)({\n        state,\n        getState,\n        navigation,\n        setOptions,\n        router,\n        emitter\n      }),\n      base = _useNavigationCache.base,\n      navigations = _useNavigationCache.navigations;\n    var routes = (0, _useRouteCache.useRouteCache)(state.routes);\n    var getOptions = (route, navigation, overrides) => {\n      var config = screens[route.name];\n      var screen = config.props;\n      var optionsList = [\n      // The default `screenOptions` passed to the navigator\n      screenOptions,\n      // The `screenOptions` props passed to `Group` elements\n      ...(config.options ? config.options.filter(Boolean) : []),\n      // The `options` prop passed to `Screen` elements,\n      screen.options,\n      // The options set via `navigation.setOptions`\n      overrides];\n      return optionsList.reduce((acc, curr) => Object.assign(acc,\n      // @ts-expect-error: we check for function but TS still complains\n      typeof curr !== 'function' ? curr : curr({\n        route,\n        navigation,\n        theme\n      })), {});\n    };\n    var render = (route, navigation, customOptions, routeState) => {\n      var config = screens[route.name];\n      var screen = config.props;\n      var clearOptions = () => setOptions(o => {\n        if (route.key in o) {\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          var _route$key = route.key,\n            _ = o[_route$key],\n            rest = (0, _objectWithoutProperties2.default)(o, [_route$key].map(_toPropertyKey));\n          return rest;\n        }\n        return o;\n      });\n      var layout =\n      // The `layout` prop passed to `Screen` elements,\n      screen.layout ??\n      // The `screenLayout` props passed to `Group` elements\n      config.layout ??\n      // The default `screenLayout` passed to the navigator\n      screenLayout;\n      var element = /*#__PURE__*/(0, _jsxRuntime.jsx)(_SceneView.SceneView, {\n        navigation: navigation,\n        route: route,\n        screen: screen,\n        routeState: routeState,\n        getState: getState,\n        setState: setState,\n        options: customOptions,\n        clearOptions: clearOptions\n      });\n      if (layout != null) {\n        element = layout({\n          route,\n          navigation,\n          options: customOptions,\n          // @ts-expect-error: in practice `theme` will be defined\n          theme,\n          children: element\n        });\n      }\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationBuilderContext.NavigationBuilderContext.Provider, {\n        value: context,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationContext.NavigationContext.Provider, {\n          value: navigation,\n          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationRouteContext.NavigationRouteContext.Provider, {\n            value: route,\n            children: element\n          })\n        })\n      }, route.key);\n    };\n    var descriptors = routes.reduce((acc, route, i) => {\n      var navigation = navigations[route.key];\n      var customOptions = getOptions(route, navigation, options[route.key]);\n      var element = render(route, navigation, customOptions, state.routes[i].state);\n      acc[route.key] = {\n        route,\n        // @ts-expect-error: it's missing action helpers, fix later\n        navigation,\n        render() {\n          return element;\n        },\n        options: customOptions\n      };\n      return acc;\n    }, {});\n\n    /**\n     * Create a descriptor object for a route.\n     *\n     * @param route Route object for which the descriptor should be created\n     * @param placeholder Whether the descriptor should be a placeholder, e.g. for a route not yet in the state\n     * @returns Descriptor object\n     */\n    var describe = (route, placeholder) => {\n      if (!placeholder) {\n        if (!(route.key in descriptors)) {\n          throw new Error(`Couldn't find a route with the key ${route.key}.`);\n        }\n        return descriptors[route.key];\n      }\n      var navigation = base;\n      var customOptions = getOptions(route, navigation, {});\n      var element = render(route, navigation, customOptions, undefined);\n      return {\n        route,\n        navigation,\n        render() {\n          return element;\n        },\n        options: customOptions\n      };\n    };\n    return {\n      describe,\n      descriptors\n    };\n  }\n});", "lineCount": 197, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "useDescriptors"], [8, 24, 1, 13], [8, 27, 1, 13, "useDescriptors"], [8, 41, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_slicedToArray2"], [10, 21, 1, 13], [10, 24, 1, 13, "_interopRequireDefault"], [10, 46, 1, 13], [10, 47, 1, 13, "require"], [10, 54, 1, 13], [10, 55, 1, 13, "_dependencyMap"], [10, 69, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "React"], [11, 11, 3, 0], [11, 14, 3, 0, "_interopRequireWildcard"], [11, 37, 3, 0], [11, 38, 3, 0, "require"], [11, 45, 3, 0], [11, 46, 3, 0, "_dependencyMap"], [11, 60, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_NavigationBuilderContext"], [12, 31, 4, 0], [12, 34, 4, 0, "require"], [12, 41, 4, 0], [12, 42, 4, 0, "_dependencyMap"], [12, 56, 4, 0], [13, 2, 5, 0], [13, 6, 5, 0, "_NavigationContext"], [13, 24, 5, 0], [13, 27, 5, 0, "require"], [13, 34, 5, 0], [13, 35, 5, 0, "_dependencyMap"], [13, 49, 5, 0], [14, 2, 6, 0], [14, 6, 6, 0, "_NavigationRouteContext"], [14, 29, 6, 0], [14, 32, 6, 0, "require"], [14, 39, 6, 0], [14, 40, 6, 0, "_dependencyMap"], [14, 54, 6, 0], [15, 2, 7, 0], [15, 6, 7, 0, "_SceneView"], [15, 16, 7, 0], [15, 19, 7, 0, "require"], [15, 26, 7, 0], [15, 27, 7, 0, "_dependencyMap"], [15, 41, 7, 0], [16, 2, 8, 0], [16, 6, 8, 0, "_ThemeContext"], [16, 19, 8, 0], [16, 22, 8, 0, "require"], [16, 29, 8, 0], [16, 30, 8, 0, "_dependencyMap"], [16, 44, 8, 0], [17, 2, 9, 0], [17, 6, 9, 0, "_useNavigationCache2"], [17, 26, 9, 0], [17, 29, 9, 0, "require"], [17, 36, 9, 0], [17, 37, 9, 0, "_dependencyMap"], [17, 51, 9, 0], [18, 2, 10, 0], [18, 6, 10, 0, "_useRouteCache"], [18, 20, 10, 0], [18, 23, 10, 0, "require"], [18, 30, 10, 0], [18, 31, 10, 0, "_dependencyMap"], [18, 45, 10, 0], [19, 2, 11, 0], [19, 6, 11, 0, "_jsxRuntime"], [19, 17, 11, 0], [19, 20, 11, 0, "require"], [19, 27, 11, 0], [19, 28, 11, 0, "_dependencyMap"], [19, 42, 11, 0], [20, 2, 11, 48], [20, 11, 11, 48, "_interopRequireWildcard"], [20, 35, 11, 48, "e"], [20, 36, 11, 48], [20, 38, 11, 48, "t"], [20, 39, 11, 48], [20, 68, 11, 48, "WeakMap"], [20, 75, 11, 48], [20, 81, 11, 48, "r"], [20, 82, 11, 48], [20, 89, 11, 48, "WeakMap"], [20, 96, 11, 48], [20, 100, 11, 48, "n"], [20, 101, 11, 48], [20, 108, 11, 48, "WeakMap"], [20, 115, 11, 48], [20, 127, 11, 48, "_interopRequireWildcard"], [20, 150, 11, 48], [20, 162, 11, 48, "_interopRequireWildcard"], [20, 163, 11, 48, "e"], [20, 164, 11, 48], [20, 166, 11, 48, "t"], [20, 167, 11, 48], [20, 176, 11, 48, "t"], [20, 177, 11, 48], [20, 181, 11, 48, "e"], [20, 182, 11, 48], [20, 186, 11, 48, "e"], [20, 187, 11, 48], [20, 188, 11, 48, "__esModule"], [20, 198, 11, 48], [20, 207, 11, 48, "e"], [20, 208, 11, 48], [20, 214, 11, 48, "o"], [20, 215, 11, 48], [20, 217, 11, 48, "i"], [20, 218, 11, 48], [20, 220, 11, 48, "f"], [20, 221, 11, 48], [20, 226, 11, 48, "__proto__"], [20, 235, 11, 48], [20, 243, 11, 48, "default"], [20, 250, 11, 48], [20, 252, 11, 48, "e"], [20, 253, 11, 48], [20, 270, 11, 48, "e"], [20, 271, 11, 48], [20, 294, 11, 48, "e"], [20, 295, 11, 48], [20, 320, 11, 48, "e"], [20, 321, 11, 48], [20, 330, 11, 48, "f"], [20, 331, 11, 48], [20, 337, 11, 48, "o"], [20, 338, 11, 48], [20, 341, 11, 48, "t"], [20, 342, 11, 48], [20, 345, 11, 48, "n"], [20, 346, 11, 48], [20, 349, 11, 48, "r"], [20, 350, 11, 48], [20, 358, 11, 48, "o"], [20, 359, 11, 48], [20, 360, 11, 48, "has"], [20, 363, 11, 48], [20, 364, 11, 48, "e"], [20, 365, 11, 48], [20, 375, 11, 48, "o"], [20, 376, 11, 48], [20, 377, 11, 48, "get"], [20, 380, 11, 48], [20, 381, 11, 48, "e"], [20, 382, 11, 48], [20, 385, 11, 48, "o"], [20, 386, 11, 48], [20, 387, 11, 48, "set"], [20, 390, 11, 48], [20, 391, 11, 48, "e"], [20, 392, 11, 48], [20, 394, 11, 48, "f"], [20, 395, 11, 48], [20, 409, 11, 48, "_t"], [20, 411, 11, 48], [20, 415, 11, 48, "e"], [20, 416, 11, 48], [20, 432, 11, 48, "_t"], [20, 434, 11, 48], [20, 441, 11, 48, "hasOwnProperty"], [20, 455, 11, 48], [20, 456, 11, 48, "call"], [20, 460, 11, 48], [20, 461, 11, 48, "e"], [20, 462, 11, 48], [20, 464, 11, 48, "_t"], [20, 466, 11, 48], [20, 473, 11, 48, "i"], [20, 474, 11, 48], [20, 478, 11, 48, "o"], [20, 479, 11, 48], [20, 482, 11, 48, "Object"], [20, 488, 11, 48], [20, 489, 11, 48, "defineProperty"], [20, 503, 11, 48], [20, 508, 11, 48, "Object"], [20, 514, 11, 48], [20, 515, 11, 48, "getOwnPropertyDescriptor"], [20, 539, 11, 48], [20, 540, 11, 48, "e"], [20, 541, 11, 48], [20, 543, 11, 48, "_t"], [20, 545, 11, 48], [20, 552, 11, 48, "i"], [20, 553, 11, 48], [20, 554, 11, 48, "get"], [20, 557, 11, 48], [20, 561, 11, 48, "i"], [20, 562, 11, 48], [20, 563, 11, 48, "set"], [20, 566, 11, 48], [20, 570, 11, 48, "o"], [20, 571, 11, 48], [20, 572, 11, 48, "f"], [20, 573, 11, 48], [20, 575, 11, 48, "_t"], [20, 577, 11, 48], [20, 579, 11, 48, "i"], [20, 580, 11, 48], [20, 584, 11, 48, "f"], [20, 585, 11, 48], [20, 586, 11, 48, "_t"], [20, 588, 11, 48], [20, 592, 11, 48, "e"], [20, 593, 11, 48], [20, 594, 11, 48, "_t"], [20, 596, 11, 48], [20, 607, 11, 48, "f"], [20, 608, 11, 48], [20, 613, 11, 48, "e"], [20, 614, 11, 48], [20, 616, 11, 48, "t"], [20, 617, 11, 48], [21, 2, 11, 48], [21, 11, 11, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [21, 26, 11, 48, "t"], [21, 27, 11, 48], [21, 35, 11, 48, "i"], [21, 36, 11, 48], [21, 39, 11, 48, "_toPrimitive"], [21, 51, 11, 48], [21, 52, 11, 48, "t"], [21, 53, 11, 48], [21, 92, 11, 48, "i"], [21, 93, 11, 48], [21, 96, 11, 48, "i"], [21, 97, 11, 48], [21, 100, 11, 48, "i"], [21, 101, 11, 48], [22, 2, 11, 48], [22, 11, 11, 48, "_toPrimitive"], [22, 24, 11, 48, "t"], [22, 25, 11, 48], [22, 27, 11, 48, "r"], [22, 28, 11, 48], [22, 55, 11, 48, "t"], [22, 56, 11, 48], [22, 61, 11, 48, "t"], [22, 62, 11, 48], [22, 71, 11, 48, "t"], [22, 72, 11, 48], [22, 78, 11, 48, "e"], [22, 79, 11, 48], [22, 82, 11, 48, "t"], [22, 83, 11, 48], [22, 84, 11, 48, "Symbol"], [22, 90, 11, 48], [22, 91, 11, 48, "toPrimitive"], [22, 102, 11, 48], [22, 120, 11, 48, "e"], [22, 121, 11, 48], [22, 129, 11, 48, "i"], [22, 130, 11, 48], [22, 133, 11, 48, "e"], [22, 134, 11, 48], [22, 135, 11, 48, "call"], [22, 139, 11, 48], [22, 140, 11, 48, "t"], [22, 141, 11, 48], [22, 143, 11, 48, "r"], [22, 144, 11, 48], [22, 183, 11, 48, "i"], [22, 184, 11, 48], [22, 193, 11, 48, "i"], [22, 194, 11, 48], [22, 206, 11, 48, "TypeError"], [22, 215, 11, 48], [22, 288, 11, 48, "r"], [22, 289, 11, 48], [22, 292, 11, 48, "String"], [22, 298, 11, 48], [22, 301, 11, 48, "Number"], [22, 307, 11, 48], [22, 309, 11, 48, "t"], [22, 310, 11, 48], [23, 2, 12, 0], [24, 0, 13, 0], [25, 0, 14, 0], [26, 0, 15, 0], [27, 0, 16, 0], [28, 0, 17, 0], [29, 0, 18, 0], [30, 0, 19, 0], [31, 2, 20, 7], [31, 11, 20, 16, "useDescriptors"], [31, 25, 20, 30, "useDescriptors"], [31, 26, 20, 30, "_ref"], [31, 30, 20, 30], [31, 32, 34, 3], [32, 4, 34, 3], [32, 8, 21, 2, "state"], [32, 13, 21, 7], [32, 16, 21, 7, "_ref"], [32, 20, 21, 7], [32, 21, 21, 2, "state"], [32, 26, 21, 7], [33, 6, 22, 2, "screens"], [33, 13, 22, 9], [33, 16, 22, 9, "_ref"], [33, 20, 22, 9], [33, 21, 22, 2, "screens"], [33, 28, 22, 9], [34, 6, 23, 2, "navigation"], [34, 16, 23, 12], [34, 19, 23, 12, "_ref"], [34, 23, 23, 12], [34, 24, 23, 2, "navigation"], [34, 34, 23, 12], [35, 6, 24, 2, "screenOptions"], [35, 19, 24, 15], [35, 22, 24, 15, "_ref"], [35, 26, 24, 15], [35, 27, 24, 2, "screenOptions"], [35, 40, 24, 15], [36, 6, 25, 2, "screenLayout"], [36, 18, 25, 14], [36, 21, 25, 14, "_ref"], [36, 25, 25, 14], [36, 26, 25, 2, "screenLayout"], [36, 38, 25, 14], [37, 6, 26, 2, "onAction"], [37, 14, 26, 10], [37, 17, 26, 10, "_ref"], [37, 21, 26, 10], [37, 22, 26, 2, "onAction"], [37, 30, 26, 10], [38, 6, 27, 2, "getState"], [38, 14, 27, 10], [38, 17, 27, 10, "_ref"], [38, 21, 27, 10], [38, 22, 27, 2, "getState"], [38, 30, 27, 10], [39, 6, 28, 2, "setState"], [39, 14, 28, 10], [39, 17, 28, 10, "_ref"], [39, 21, 28, 10], [39, 22, 28, 2, "setState"], [39, 30, 28, 10], [40, 6, 29, 2, "addListener"], [40, 17, 29, 13], [40, 20, 29, 13, "_ref"], [40, 24, 29, 13], [40, 25, 29, 2, "addListener"], [40, 36, 29, 13], [41, 6, 30, 2, "addKeyedListener"], [41, 22, 30, 18], [41, 25, 30, 18, "_ref"], [41, 29, 30, 18], [41, 30, 30, 2, "addKeyedListener"], [41, 46, 30, 18], [42, 6, 31, 2, "onRouteFocus"], [42, 18, 31, 14], [42, 21, 31, 14, "_ref"], [42, 25, 31, 14], [42, 26, 31, 2, "onRouteFocus"], [42, 38, 31, 14], [43, 6, 32, 2, "router"], [43, 12, 32, 8], [43, 15, 32, 8, "_ref"], [43, 19, 32, 8], [43, 20, 32, 2, "router"], [43, 26, 32, 8], [44, 6, 33, 2, "emitter"], [44, 13, 33, 9], [44, 16, 33, 9, "_ref"], [44, 20, 33, 9], [44, 21, 33, 2, "emitter"], [44, 28, 33, 9], [45, 4, 35, 2], [45, 8, 35, 8, "theme"], [45, 13, 35, 13], [45, 16, 35, 16, "React"], [45, 21, 35, 21], [45, 22, 35, 22, "useContext"], [45, 32, 35, 32], [45, 33, 35, 33, "ThemeContext"], [45, 59, 35, 45], [45, 60, 35, 46], [46, 4, 36, 2], [46, 8, 36, 2, "_React$useState"], [46, 23, 36, 2], [46, 26, 36, 32, "React"], [46, 31, 36, 37], [46, 32, 36, 38, "useState"], [46, 40, 36, 46], [46, 41, 36, 47], [46, 42, 36, 48], [46, 43, 36, 49], [46, 44, 36, 50], [47, 6, 36, 50, "_React$useState2"], [47, 22, 36, 50], [47, 29, 36, 50, "_slicedToArray2"], [47, 44, 36, 50], [47, 45, 36, 50, "default"], [47, 52, 36, 50], [47, 54, 36, 50, "_React$useState"], [47, 69, 36, 50], [48, 6, 36, 9, "options"], [48, 13, 36, 16], [48, 16, 36, 16, "_React$useState2"], [48, 32, 36, 16], [49, 6, 36, 18, "setOptions"], [49, 16, 36, 28], [49, 19, 36, 28, "_React$useState2"], [49, 35, 36, 28], [50, 4, 37, 2], [50, 8, 37, 2, "_React$useContext"], [50, 25, 37, 2], [50, 28, 43, 6, "React"], [50, 33, 43, 11], [50, 34, 43, 12, "useContext"], [50, 44, 43, 22], [50, 45, 43, 23, "NavigationBuilderContext"], [50, 95, 43, 47], [50, 96, 43, 48], [51, 6, 38, 4, "onDispatchAction"], [51, 22, 38, 20], [51, 25, 38, 20, "_React$useContext"], [51, 42, 38, 20], [51, 43, 38, 4, "onDispatchAction"], [51, 59, 38, 20], [52, 6, 39, 4, "onOptionsChange"], [52, 21, 39, 19], [52, 24, 39, 19, "_React$useContext"], [52, 41, 39, 19], [52, 42, 39, 4, "onOptionsChange"], [52, 57, 39, 19], [53, 6, 40, 4, "scheduleUpdate"], [53, 20, 40, 18], [53, 23, 40, 18, "_React$useContext"], [53, 40, 40, 18], [53, 41, 40, 4, "scheduleUpdate"], [53, 55, 40, 18], [54, 6, 41, 4, "flushUpdates"], [54, 18, 41, 16], [54, 21, 41, 16, "_React$useContext"], [54, 38, 41, 16], [54, 39, 41, 4, "flushUpdates"], [54, 51, 41, 16], [55, 6, 42, 4, "stackRef"], [55, 14, 42, 12], [55, 17, 42, 12, "_React$useContext"], [55, 34, 42, 12], [55, 35, 42, 4, "stackRef"], [55, 43, 42, 12], [56, 4, 44, 2], [56, 8, 44, 8, "context"], [56, 15, 44, 15], [56, 18, 44, 18, "React"], [56, 23, 44, 23], [56, 24, 44, 24, "useMemo"], [56, 31, 44, 31], [56, 32, 44, 32], [56, 39, 44, 39], [57, 6, 45, 4, "navigation"], [57, 16, 45, 14], [58, 6, 46, 4, "onAction"], [58, 14, 46, 12], [59, 6, 47, 4, "addListener"], [59, 17, 47, 15], [60, 6, 48, 4, "addKeyedListener"], [60, 22, 48, 20], [61, 6, 49, 4, "onRouteFocus"], [61, 18, 49, 16], [62, 6, 50, 4, "onDispatchAction"], [62, 22, 50, 20], [63, 6, 51, 4, "onOptionsChange"], [63, 21, 51, 19], [64, 6, 52, 4, "scheduleUpdate"], [64, 20, 52, 18], [65, 6, 53, 4, "flushUpdates"], [65, 18, 53, 16], [66, 6, 54, 4, "stackRef"], [67, 4, 55, 2], [67, 5, 55, 3], [67, 6, 55, 4], [67, 8, 55, 6], [67, 9, 55, 7, "navigation"], [67, 19, 55, 17], [67, 21, 55, 19, "onAction"], [67, 29, 55, 27], [67, 31, 55, 29, "addListener"], [67, 42, 55, 40], [67, 44, 55, 42, "addKeyedListener"], [67, 60, 55, 58], [67, 62, 55, 60, "onRouteFocus"], [67, 74, 55, 72], [67, 76, 55, 74, "onDispatchAction"], [67, 92, 55, 90], [67, 94, 55, 92, "onOptionsChange"], [67, 109, 55, 107], [67, 111, 55, 109, "scheduleUpdate"], [67, 125, 55, 123], [67, 127, 55, 125, "flushUpdates"], [67, 139, 55, 137], [67, 141, 55, 139, "stackRef"], [67, 149, 55, 147], [67, 150, 55, 148], [67, 151, 55, 149], [68, 4, 56, 2], [68, 8, 56, 2, "_useNavigationCache"], [68, 27, 56, 2], [68, 30, 59, 6], [68, 34, 59, 6, "useNavigationCache"], [68, 73, 59, 24], [68, 75, 59, 25], [69, 8, 60, 4, "state"], [69, 13, 60, 9], [70, 8, 61, 4, "getState"], [70, 16, 61, 12], [71, 8, 62, 4, "navigation"], [71, 18, 62, 14], [72, 8, 63, 4, "setOptions"], [72, 18, 63, 14], [73, 8, 64, 4, "router"], [73, 14, 64, 10], [74, 8, 65, 4, "emitter"], [75, 6, 66, 2], [75, 7, 66, 3], [75, 8, 66, 4], [76, 6, 57, 4, "base"], [76, 10, 57, 8], [76, 13, 57, 8, "_useNavigationCache"], [76, 32, 57, 8], [76, 33, 57, 4, "base"], [76, 37, 57, 8], [77, 6, 58, 4, "navigations"], [77, 17, 58, 15], [77, 20, 58, 15, "_useNavigationCache"], [77, 39, 58, 15], [77, 40, 58, 4, "navigations"], [77, 51, 58, 15], [78, 4, 67, 2], [78, 8, 67, 8, "routes"], [78, 14, 67, 14], [78, 17, 67, 17], [78, 21, 67, 17, "useRouteCache"], [78, 49, 67, 30], [78, 51, 67, 31, "state"], [78, 56, 67, 36], [78, 57, 67, 37, "routes"], [78, 63, 67, 43], [78, 64, 67, 44], [79, 4, 68, 2], [79, 8, 68, 8, "getOptions"], [79, 18, 68, 18], [79, 21, 68, 21, "getOptions"], [79, 22, 68, 22, "route"], [79, 27, 68, 27], [79, 29, 68, 29, "navigation"], [79, 39, 68, 39], [79, 41, 68, 41, "overrides"], [79, 50, 68, 50], [79, 55, 68, 55], [80, 6, 69, 4], [80, 10, 69, 10, "config"], [80, 16, 69, 16], [80, 19, 69, 19, "screens"], [80, 26, 69, 26], [80, 27, 69, 27, "route"], [80, 32, 69, 32], [80, 33, 69, 33, "name"], [80, 37, 69, 37], [80, 38, 69, 38], [81, 6, 70, 4], [81, 10, 70, 10, "screen"], [81, 16, 70, 16], [81, 19, 70, 19, "config"], [81, 25, 70, 25], [81, 26, 70, 26, "props"], [81, 31, 70, 31], [82, 6, 71, 4], [82, 10, 71, 10, "optionsList"], [82, 21, 71, 21], [82, 24, 71, 24], [83, 6, 72, 4], [84, 6, 73, 4, "screenOptions"], [84, 19, 73, 17], [85, 6, 74, 4], [86, 6, 75, 4], [86, 10, 75, 8, "config"], [86, 16, 75, 14], [86, 17, 75, 15, "options"], [86, 24, 75, 22], [86, 27, 75, 25, "config"], [86, 33, 75, 31], [86, 34, 75, 32, "options"], [86, 41, 75, 39], [86, 42, 75, 40, "filter"], [86, 48, 75, 46], [86, 49, 75, 47, "Boolean"], [86, 56, 75, 54], [86, 57, 75, 55], [86, 60, 75, 58], [86, 62, 75, 60], [86, 63, 75, 61], [87, 6, 76, 4], [88, 6, 77, 4, "screen"], [88, 12, 77, 10], [88, 13, 77, 11, "options"], [88, 20, 77, 18], [89, 6, 78, 4], [90, 6, 79, 4, "overrides"], [90, 15, 79, 13], [90, 16, 79, 14], [91, 6, 80, 4], [91, 13, 80, 11, "optionsList"], [91, 24, 80, 22], [91, 25, 80, 23, "reduce"], [91, 31, 80, 29], [91, 32, 80, 30], [91, 33, 80, 31, "acc"], [91, 36, 80, 34], [91, 38, 80, 36, "curr"], [91, 42, 80, 40], [91, 47, 80, 45, "Object"], [91, 53, 80, 51], [91, 54, 80, 52, "assign"], [91, 60, 80, 58], [91, 61, 80, 59, "acc"], [91, 64, 80, 62], [92, 6, 81, 4], [93, 6, 82, 4], [93, 13, 82, 11, "curr"], [93, 17, 82, 15], [93, 22, 82, 20], [93, 32, 82, 30], [93, 35, 82, 33, "curr"], [93, 39, 82, 37], [93, 42, 82, 40, "curr"], [93, 46, 82, 44], [93, 47, 82, 45], [94, 8, 83, 6, "route"], [94, 13, 83, 11], [95, 8, 84, 6, "navigation"], [95, 18, 84, 16], [96, 8, 85, 6, "theme"], [97, 6, 86, 4], [97, 7, 86, 5], [97, 8, 86, 6], [97, 9, 86, 7], [97, 11, 86, 9], [97, 12, 86, 10], [97, 13, 86, 11], [97, 14, 86, 12], [98, 4, 87, 2], [98, 5, 87, 3], [99, 4, 88, 2], [99, 8, 88, 8, "render"], [99, 14, 88, 14], [99, 17, 88, 17, "render"], [99, 18, 88, 18, "route"], [99, 23, 88, 23], [99, 25, 88, 25, "navigation"], [99, 35, 88, 35], [99, 37, 88, 37, "customOptions"], [99, 50, 88, 50], [99, 52, 88, 52, "routeState"], [99, 62, 88, 62], [99, 67, 88, 67], [100, 6, 89, 4], [100, 10, 89, 10, "config"], [100, 16, 89, 16], [100, 19, 89, 19, "screens"], [100, 26, 89, 26], [100, 27, 89, 27, "route"], [100, 32, 89, 32], [100, 33, 89, 33, "name"], [100, 37, 89, 37], [100, 38, 89, 38], [101, 6, 90, 4], [101, 10, 90, 10, "screen"], [101, 16, 90, 16], [101, 19, 90, 19, "config"], [101, 25, 90, 25], [101, 26, 90, 26, "props"], [101, 31, 90, 31], [102, 6, 91, 4], [102, 10, 91, 10, "clearOptions"], [102, 22, 91, 22], [102, 25, 91, 25, "clearOptions"], [102, 26, 91, 25], [102, 31, 91, 31, "setOptions"], [102, 41, 91, 41], [102, 42, 91, 42, "o"], [102, 43, 91, 43], [102, 47, 91, 47], [103, 8, 92, 6], [103, 12, 92, 10, "route"], [103, 17, 92, 15], [103, 18, 92, 16, "key"], [103, 21, 92, 19], [103, 25, 92, 23, "o"], [103, 26, 92, 24], [103, 28, 92, 26], [104, 10, 93, 8], [105, 10, 94, 8], [105, 14, 94, 8, "_route$key"], [105, 24, 94, 8], [105, 27, 95, 11, "route"], [105, 32, 95, 16], [105, 33, 95, 17, "key"], [105, 36, 95, 20], [106, 12, 95, 23, "_"], [106, 13, 95, 24], [106, 16, 97, 12, "o"], [106, 17, 97, 13], [106, 18, 97, 13, "_route$key"], [106, 28, 97, 13], [107, 12, 96, 13, "rest"], [107, 16, 96, 17], [107, 23, 96, 17, "_objectWithoutProperties2"], [107, 48, 96, 17], [107, 49, 96, 17, "default"], [107, 56, 96, 17], [107, 58, 97, 12, "o"], [107, 59, 97, 13], [107, 62, 97, 13, "_route$key"], [107, 72, 97, 13], [107, 74, 97, 13, "map"], [107, 77, 97, 13], [107, 78, 97, 13, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [107, 92, 97, 13], [108, 10, 98, 8], [108, 17, 98, 15, "rest"], [108, 21, 98, 19], [109, 8, 99, 6], [110, 8, 100, 6], [110, 15, 100, 13, "o"], [110, 16, 100, 14], [111, 6, 101, 4], [111, 7, 101, 5], [111, 8, 101, 6], [112, 6, 102, 4], [112, 10, 102, 10, "layout"], [112, 16, 102, 16], [113, 6, 103, 4], [114, 6, 104, 4, "screen"], [114, 12, 104, 10], [114, 13, 104, 11, "layout"], [114, 19, 104, 17], [115, 6, 105, 4], [116, 6, 106, 4, "config"], [116, 12, 106, 10], [116, 13, 106, 11, "layout"], [116, 19, 106, 17], [117, 6, 107, 4], [118, 6, 108, 4, "screenLayout"], [118, 18, 108, 16], [119, 6, 109, 4], [119, 10, 109, 8, "element"], [119, 17, 109, 15], [119, 20, 109, 18], [119, 33, 109, 31], [119, 37, 109, 31, "_jsx"], [119, 52, 109, 35], [119, 54, 109, 36, "SceneView"], [119, 74, 109, 45], [119, 76, 109, 47], [120, 8, 110, 6, "navigation"], [120, 18, 110, 16], [120, 20, 110, 18, "navigation"], [120, 30, 110, 28], [121, 8, 111, 6, "route"], [121, 13, 111, 11], [121, 15, 111, 13, "route"], [121, 20, 111, 18], [122, 8, 112, 6, "screen"], [122, 14, 112, 12], [122, 16, 112, 14, "screen"], [122, 22, 112, 20], [123, 8, 113, 6, "routeState"], [123, 18, 113, 16], [123, 20, 113, 18, "routeState"], [123, 30, 113, 28], [124, 8, 114, 6, "getState"], [124, 16, 114, 14], [124, 18, 114, 16, "getState"], [124, 26, 114, 24], [125, 8, 115, 6, "setState"], [125, 16, 115, 14], [125, 18, 115, 16, "setState"], [125, 26, 115, 24], [126, 8, 116, 6, "options"], [126, 15, 116, 13], [126, 17, 116, 15, "customOptions"], [126, 30, 116, 28], [127, 8, 117, 6, "clearOptions"], [127, 20, 117, 18], [127, 22, 117, 20, "clearOptions"], [128, 6, 118, 4], [128, 7, 118, 5], [128, 8, 118, 6], [129, 6, 119, 4], [129, 10, 119, 8, "layout"], [129, 16, 119, 14], [129, 20, 119, 18], [129, 24, 119, 22], [129, 26, 119, 24], [130, 8, 120, 6, "element"], [130, 15, 120, 13], [130, 18, 120, 16, "layout"], [130, 24, 120, 22], [130, 25, 120, 23], [131, 10, 121, 8, "route"], [131, 15, 121, 13], [132, 10, 122, 8, "navigation"], [132, 20, 122, 18], [133, 10, 123, 8, "options"], [133, 17, 123, 15], [133, 19, 123, 17, "customOptions"], [133, 32, 123, 30], [134, 10, 124, 8], [135, 10, 125, 8, "theme"], [135, 15, 125, 13], [136, 10, 126, 8, "children"], [136, 18, 126, 16], [136, 20, 126, 18, "element"], [137, 8, 127, 6], [137, 9, 127, 7], [137, 10, 127, 8], [138, 6, 128, 4], [139, 6, 129, 4], [139, 13, 129, 11], [139, 26, 129, 24], [139, 30, 129, 24, "_jsx"], [139, 45, 129, 28], [139, 47, 129, 29, "NavigationBuilderContext"], [139, 97, 129, 53], [139, 98, 129, 54, "Provider"], [139, 106, 129, 62], [139, 108, 129, 64], [140, 8, 130, 6, "value"], [140, 13, 130, 11], [140, 15, 130, 13, "context"], [140, 22, 130, 20], [141, 8, 131, 6, "children"], [141, 16, 131, 14], [141, 18, 131, 16], [141, 31, 131, 29], [141, 35, 131, 29, "_jsx"], [141, 50, 131, 33], [141, 52, 131, 34, "NavigationContext"], [141, 88, 131, 51], [141, 89, 131, 52, "Provider"], [141, 97, 131, 60], [141, 99, 131, 62], [142, 10, 132, 8, "value"], [142, 15, 132, 13], [142, 17, 132, 15, "navigation"], [142, 27, 132, 25], [143, 10, 133, 8, "children"], [143, 18, 133, 16], [143, 20, 133, 18], [143, 33, 133, 31], [143, 37, 133, 31, "_jsx"], [143, 52, 133, 35], [143, 54, 133, 36, "NavigationRouteContext"], [143, 100, 133, 58], [143, 101, 133, 59, "Provider"], [143, 109, 133, 67], [143, 111, 133, 69], [144, 12, 134, 10, "value"], [144, 17, 134, 15], [144, 19, 134, 17, "route"], [144, 24, 134, 22], [145, 12, 135, 10, "children"], [145, 20, 135, 18], [145, 22, 135, 20, "element"], [146, 10, 136, 8], [146, 11, 136, 9], [147, 8, 137, 6], [147, 9, 137, 7], [148, 6, 138, 4], [148, 7, 138, 5], [148, 9, 138, 7, "route"], [148, 14, 138, 12], [148, 15, 138, 13, "key"], [148, 18, 138, 16], [148, 19, 138, 17], [149, 4, 139, 2], [149, 5, 139, 3], [150, 4, 140, 2], [150, 8, 140, 8, "descriptors"], [150, 19, 140, 19], [150, 22, 140, 22, "routes"], [150, 28, 140, 28], [150, 29, 140, 29, "reduce"], [150, 35, 140, 35], [150, 36, 140, 36], [150, 37, 140, 37, "acc"], [150, 40, 140, 40], [150, 42, 140, 42, "route"], [150, 47, 140, 47], [150, 49, 140, 49, "i"], [150, 50, 140, 50], [150, 55, 140, 55], [151, 6, 141, 4], [151, 10, 141, 10, "navigation"], [151, 20, 141, 20], [151, 23, 141, 23, "navigations"], [151, 34, 141, 34], [151, 35, 141, 35, "route"], [151, 40, 141, 40], [151, 41, 141, 41, "key"], [151, 44, 141, 44], [151, 45, 141, 45], [152, 6, 142, 4], [152, 10, 142, 10, "customOptions"], [152, 23, 142, 23], [152, 26, 142, 26, "getOptions"], [152, 36, 142, 36], [152, 37, 142, 37, "route"], [152, 42, 142, 42], [152, 44, 142, 44, "navigation"], [152, 54, 142, 54], [152, 56, 142, 56, "options"], [152, 63, 142, 63], [152, 64, 142, 64, "route"], [152, 69, 142, 69], [152, 70, 142, 70, "key"], [152, 73, 142, 73], [152, 74, 142, 74], [152, 75, 142, 75], [153, 6, 143, 4], [153, 10, 143, 10, "element"], [153, 17, 143, 17], [153, 20, 143, 20, "render"], [153, 26, 143, 26], [153, 27, 143, 27, "route"], [153, 32, 143, 32], [153, 34, 143, 34, "navigation"], [153, 44, 143, 44], [153, 46, 143, 46, "customOptions"], [153, 59, 143, 59], [153, 61, 143, 61, "state"], [153, 66, 143, 66], [153, 67, 143, 67, "routes"], [153, 73, 143, 73], [153, 74, 143, 74, "i"], [153, 75, 143, 75], [153, 76, 143, 76], [153, 77, 143, 77, "state"], [153, 82, 143, 82], [153, 83, 143, 83], [154, 6, 144, 4, "acc"], [154, 9, 144, 7], [154, 10, 144, 8, "route"], [154, 15, 144, 13], [154, 16, 144, 14, "key"], [154, 19, 144, 17], [154, 20, 144, 18], [154, 23, 144, 21], [155, 8, 145, 6, "route"], [155, 13, 145, 11], [156, 8, 146, 6], [157, 8, 147, 6, "navigation"], [157, 18, 147, 16], [158, 8, 148, 6, "render"], [158, 14, 148, 12, "render"], [158, 15, 148, 12], [158, 17, 148, 15], [159, 10, 149, 8], [159, 17, 149, 15, "element"], [159, 24, 149, 22], [160, 8, 150, 6], [160, 9, 150, 7], [161, 8, 151, 6, "options"], [161, 15, 151, 13], [161, 17, 151, 15, "customOptions"], [162, 6, 152, 4], [162, 7, 152, 5], [163, 6, 153, 4], [163, 13, 153, 11, "acc"], [163, 16, 153, 14], [164, 4, 154, 2], [164, 5, 154, 3], [164, 7, 154, 5], [164, 8, 154, 6], [164, 9, 154, 7], [164, 10, 154, 8], [166, 4, 156, 2], [167, 0, 157, 0], [168, 0, 158, 0], [169, 0, 159, 0], [170, 0, 160, 0], [171, 0, 161, 0], [172, 0, 162, 0], [173, 4, 163, 2], [173, 8, 163, 8, "describe"], [173, 16, 163, 16], [173, 19, 163, 19, "describe"], [173, 20, 163, 20, "route"], [173, 25, 163, 25], [173, 27, 163, 27, "placeholder"], [173, 38, 163, 38], [173, 43, 163, 43], [174, 6, 164, 4], [174, 10, 164, 8], [174, 11, 164, 9, "placeholder"], [174, 22, 164, 20], [174, 24, 164, 22], [175, 8, 165, 6], [175, 12, 165, 10], [175, 14, 165, 12, "route"], [175, 19, 165, 17], [175, 20, 165, 18, "key"], [175, 23, 165, 21], [175, 27, 165, 25, "descriptors"], [175, 38, 165, 36], [175, 39, 165, 37], [175, 41, 165, 39], [176, 10, 166, 8], [176, 16, 166, 14], [176, 20, 166, 18, "Error"], [176, 25, 166, 23], [176, 26, 166, 24], [176, 64, 166, 62, "route"], [176, 69, 166, 67], [176, 70, 166, 68, "key"], [176, 73, 166, 71], [176, 76, 166, 74], [176, 77, 166, 75], [177, 8, 167, 6], [178, 8, 168, 6], [178, 15, 168, 13, "descriptors"], [178, 26, 168, 24], [178, 27, 168, 25, "route"], [178, 32, 168, 30], [178, 33, 168, 31, "key"], [178, 36, 168, 34], [178, 37, 168, 35], [179, 6, 169, 4], [180, 6, 170, 4], [180, 10, 170, 10, "navigation"], [180, 20, 170, 20], [180, 23, 170, 23, "base"], [180, 27, 170, 27], [181, 6, 171, 4], [181, 10, 171, 10, "customOptions"], [181, 23, 171, 23], [181, 26, 171, 26, "getOptions"], [181, 36, 171, 36], [181, 37, 171, 37, "route"], [181, 42, 171, 42], [181, 44, 171, 44, "navigation"], [181, 54, 171, 54], [181, 56, 171, 56], [181, 57, 171, 57], [181, 58, 171, 58], [181, 59, 171, 59], [182, 6, 172, 4], [182, 10, 172, 10, "element"], [182, 17, 172, 17], [182, 20, 172, 20, "render"], [182, 26, 172, 26], [182, 27, 172, 27, "route"], [182, 32, 172, 32], [182, 34, 172, 34, "navigation"], [182, 44, 172, 44], [182, 46, 172, 46, "customOptions"], [182, 59, 172, 59], [182, 61, 172, 61, "undefined"], [182, 70, 172, 70], [182, 71, 172, 71], [183, 6, 173, 4], [183, 13, 173, 11], [184, 8, 174, 6, "route"], [184, 13, 174, 11], [185, 8, 175, 6, "navigation"], [185, 18, 175, 16], [186, 8, 176, 6, "render"], [186, 14, 176, 12, "render"], [186, 15, 176, 12], [186, 17, 176, 15], [187, 10, 177, 8], [187, 17, 177, 15, "element"], [187, 24, 177, 22], [188, 8, 178, 6], [188, 9, 178, 7], [189, 8, 179, 6, "options"], [189, 15, 179, 13], [189, 17, 179, 15, "customOptions"], [190, 6, 180, 4], [190, 7, 180, 5], [191, 4, 181, 2], [191, 5, 181, 3], [192, 4, 182, 2], [192, 11, 182, 9], [193, 6, 183, 4, "describe"], [193, 14, 183, 12], [194, 6, 184, 4, "descriptors"], [195, 4, 185, 2], [195, 5, 185, 3], [196, 2, 186, 0], [197, 0, 186, 1], [197, 3]], "functionMap": {"names": ["<global>", "useDescriptors", "React.useMemo$argument_0", "getOptions", "optionsList.reduce$argument_0", "render", "clearOptions", "setOptions$argument_0", "routes.reduce$argument_0", "acc.route.key.render", "describe"], "mappings": "AAA;OCmB;gCCwB;IDW;qBEa;8BCY;ODM;GFC;iBIC;yBCG,iBC;KDU,CD;GJsC;oCOC;MCQ;ODE;GPI;mBSS;MLa;OKE;GTG;CDK"}}, "type": "js/module"}]}