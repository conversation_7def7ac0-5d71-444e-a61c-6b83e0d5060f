{"dependencies": [{"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 82}, "end": {"line": 3, "column": 52, "index": 134}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "./useAnimatedStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 214}, "end": {"line": 5, "column": 54, "index": 268}}], "key": "XO0Zo6ry7e/lELMxcbu6bNFK6ew=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedProps = void 0;\n  var _PlatformChecker = require(_dependencyMap[0], \"../PlatformChecker\");\n  var _useAnimatedStyle = require(_dependencyMap[1], \"./useAnimatedStyle\");\n  // TODO: we should make sure that when useAP is used we are not assigning styles\n\n  function useAnimatedPropsJS(updater, deps, adapters) {\n    return (0, _useAnimatedStyle.useAnimatedStyle)(updater, deps, adapters, true);\n  }\n  var useAnimatedPropsNative = _useAnimatedStyle.useAnimatedStyle;\n\n  /**\n   * Lets you create an animated props object which can be animated using shared\n   * values.\n   *\n   * @param updater - A function returning an object with properties you want to\n   *   animate.\n   * @param dependencies - An optional array of dependencies. Only relevant when\n   *   using Reanimated without the Babel plugin on the Web.\n   * @param adapters - An optional function or array of functions allowing to\n   *   adopt prop naming between JS and the native side.\n   * @returns An animated props object which has to be passed to `animatedProps`\n   *   property of an Animated component that you want to animate.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedProps\n   */\n  var useAnimatedProps = exports.useAnimatedProps = (0, _PlatformChecker.shouldBeUseWeb)() ? useAnimatedPropsJS : useAnimatedPropsNative;\n});", "lineCount": 32, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedProps"], [7, 26, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_PlatformChecker"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_useAnimatedStyle"], [9, 23, 5, 0], [9, 26, 5, 0, "require"], [9, 33, 5, 0], [9, 34, 5, 0, "_dependencyMap"], [9, 48, 5, 0], [10, 2, 7, 0], [12, 2, 19, 0], [12, 11, 19, 9, "useAnimatedPropsJS"], [12, 29, 19, 27, "useAnimatedPropsJS"], [12, 30, 20, 2, "updater"], [12, 37, 20, 22], [12, 39, 21, 2, "deps"], [12, 43, 21, 30], [12, 45, 22, 2, "adapters"], [12, 53, 25, 10], [12, 55, 26, 2], [13, 4, 27, 2], [13, 11, 27, 9], [13, 15, 27, 10, "useAnimatedStyle"], [13, 49, 27, 26], [13, 51, 28, 4, "updater"], [13, 58, 28, 11], [13, 60, 29, 4, "deps"], [13, 64, 29, 8], [13, 66, 30, 4, "adapters"], [13, 74, 30, 12], [13, 76, 31, 4], [13, 80, 32, 2], [13, 81, 32, 3], [14, 2, 33, 0], [15, 2, 35, 0], [15, 6, 35, 6, "useAnimatedPropsNative"], [15, 28, 35, 28], [15, 31, 35, 31, "useAnimatedStyle"], [15, 65, 35, 47], [17, 2, 37, 0], [18, 0, 38, 0], [19, 0, 39, 0], [20, 0, 40, 0], [21, 0, 41, 0], [22, 0, 42, 0], [23, 0, 43, 0], [24, 0, 44, 0], [25, 0, 45, 0], [26, 0, 46, 0], [27, 0, 47, 0], [28, 0, 48, 0], [29, 0, 49, 0], [30, 0, 50, 0], [31, 2, 51, 7], [31, 6, 51, 13, "useAnimatedProps"], [31, 22, 51, 47], [31, 25, 51, 47, "exports"], [31, 32, 51, 47], [31, 33, 51, 47, "useAnimatedProps"], [31, 49, 51, 47], [31, 52, 51, 50], [31, 56, 51, 50, "shouldBeUseWeb"], [31, 87, 51, 64], [31, 89, 51, 65], [31, 90, 51, 66], [31, 93, 52, 5, "useAnimatedPropsJS"], [31, 111, 52, 23], [31, 114, 53, 4, "useAnimatedPropsNative"], [31, 136, 53, 26], [32, 0, 53, 27], [32, 3]], "functionMap": {"names": ["<global>", "useAnimatedPropsJS"], "mappings": "AAA;ACkB;CDc"}}, "type": "js/module"}]}