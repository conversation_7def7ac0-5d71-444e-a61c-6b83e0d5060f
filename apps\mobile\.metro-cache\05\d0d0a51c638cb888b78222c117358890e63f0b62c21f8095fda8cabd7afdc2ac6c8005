{"dependencies": [{"name": "./Font", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 23, "index": 23}}], "key": "uhjJvb2CC+i2amHkDH4+UF8lIHQ=", "exportNames": ["*"]}}, {"name": "./FontUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 24}, "end": {"line": 2, "column": 28, "index": 52}}], "key": "2mxH25PLwcMTW2Y2QoRkxA/CNFs=", "exportNames": ["*"]}}, {"name": "./FontHooks", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 53}, "end": {"line": 3, "column": 39, "index": 92}}], "key": "wjUVrFzY9F2HUfzibDTayNImJy4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {\n    useFonts: true\n  };\n  Object.defineProperty(exports, \"useFonts\", {\n    enumerable: true,\n    get: function () {\n      return _FontHooks.useFonts;\n    }\n  });\n  var _Font = require(_dependencyMap[0], \"./Font\");\n  Object.keys(_Font).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _Font[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _Font[key];\n      }\n    });\n  });\n  var _FontUtils = require(_dependencyMap[1], \"./FontUtils\");\n  Object.keys(_FontUtils).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _FontUtils[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _FontUtils[key];\n      }\n    });\n  });\n  var _FontHooks = require(_dependencyMap[2], \"./FontHooks\");\n});", "lineCount": 39, "map": [[14, 2, 1, 0], [14, 6, 1, 0, "_Font"], [14, 11, 1, 0], [14, 14, 1, 0, "require"], [14, 21, 1, 0], [14, 22, 1, 0, "_dependencyMap"], [14, 36, 1, 0], [15, 2, 1, 0, "Object"], [15, 8, 1, 0], [15, 9, 1, 0, "keys"], [15, 13, 1, 0], [15, 14, 1, 0, "_Font"], [15, 19, 1, 0], [15, 21, 1, 0, "for<PERSON>ach"], [15, 28, 1, 0], [15, 39, 1, 0, "key"], [15, 42, 1, 0], [16, 4, 1, 0], [16, 8, 1, 0, "key"], [16, 11, 1, 0], [16, 29, 1, 0, "key"], [16, 32, 1, 0], [17, 4, 1, 0], [17, 8, 1, 0, "Object"], [17, 14, 1, 0], [17, 15, 1, 0, "prototype"], [17, 24, 1, 0], [17, 25, 1, 0, "hasOwnProperty"], [17, 39, 1, 0], [17, 40, 1, 0, "call"], [17, 44, 1, 0], [17, 45, 1, 0, "_exportNames"], [17, 57, 1, 0], [17, 59, 1, 0, "key"], [17, 62, 1, 0], [18, 4, 1, 0], [18, 8, 1, 0, "key"], [18, 11, 1, 0], [18, 15, 1, 0, "exports"], [18, 22, 1, 0], [18, 26, 1, 0, "exports"], [18, 33, 1, 0], [18, 34, 1, 0, "key"], [18, 37, 1, 0], [18, 43, 1, 0, "_Font"], [18, 48, 1, 0], [18, 49, 1, 0, "key"], [18, 52, 1, 0], [19, 4, 1, 0, "Object"], [19, 10, 1, 0], [19, 11, 1, 0, "defineProperty"], [19, 25, 1, 0], [19, 26, 1, 0, "exports"], [19, 33, 1, 0], [19, 35, 1, 0, "key"], [19, 38, 1, 0], [20, 6, 1, 0, "enumerable"], [20, 16, 1, 0], [21, 6, 1, 0, "get"], [21, 9, 1, 0], [21, 20, 1, 0, "get"], [21, 21, 1, 0], [22, 8, 1, 0], [22, 15, 1, 0, "_Font"], [22, 20, 1, 0], [22, 21, 1, 0, "key"], [22, 24, 1, 0], [23, 6, 1, 0], [24, 4, 1, 0], [25, 2, 1, 0], [26, 2, 2, 0], [26, 6, 2, 0, "_FontUtils"], [26, 16, 2, 0], [26, 19, 2, 0, "require"], [26, 26, 2, 0], [26, 27, 2, 0, "_dependencyMap"], [26, 41, 2, 0], [27, 2, 2, 0, "Object"], [27, 8, 2, 0], [27, 9, 2, 0, "keys"], [27, 13, 2, 0], [27, 14, 2, 0, "_FontUtils"], [27, 24, 2, 0], [27, 26, 2, 0, "for<PERSON>ach"], [27, 33, 2, 0], [27, 44, 2, 0, "key"], [27, 47, 2, 0], [28, 4, 2, 0], [28, 8, 2, 0, "key"], [28, 11, 2, 0], [28, 29, 2, 0, "key"], [28, 32, 2, 0], [29, 4, 2, 0], [29, 8, 2, 0, "Object"], [29, 14, 2, 0], [29, 15, 2, 0, "prototype"], [29, 24, 2, 0], [29, 25, 2, 0, "hasOwnProperty"], [29, 39, 2, 0], [29, 40, 2, 0, "call"], [29, 44, 2, 0], [29, 45, 2, 0, "_exportNames"], [29, 57, 2, 0], [29, 59, 2, 0, "key"], [29, 62, 2, 0], [30, 4, 2, 0], [30, 8, 2, 0, "key"], [30, 11, 2, 0], [30, 15, 2, 0, "exports"], [30, 22, 2, 0], [30, 26, 2, 0, "exports"], [30, 33, 2, 0], [30, 34, 2, 0, "key"], [30, 37, 2, 0], [30, 43, 2, 0, "_FontUtils"], [30, 53, 2, 0], [30, 54, 2, 0, "key"], [30, 57, 2, 0], [31, 4, 2, 0, "Object"], [31, 10, 2, 0], [31, 11, 2, 0, "defineProperty"], [31, 25, 2, 0], [31, 26, 2, 0, "exports"], [31, 33, 2, 0], [31, 35, 2, 0, "key"], [31, 38, 2, 0], [32, 6, 2, 0, "enumerable"], [32, 16, 2, 0], [33, 6, 2, 0, "get"], [33, 9, 2, 0], [33, 20, 2, 0, "get"], [33, 21, 2, 0], [34, 8, 2, 0], [34, 15, 2, 0, "_FontUtils"], [34, 25, 2, 0], [34, 26, 2, 0, "key"], [34, 29, 2, 0], [35, 6, 2, 0], [36, 4, 2, 0], [37, 2, 2, 0], [38, 2, 3, 0], [38, 6, 3, 0, "_<PERSON><PERSON><PERSON><PERSON>s"], [38, 16, 3, 0], [38, 19, 3, 0, "require"], [38, 26, 3, 0], [38, 27, 3, 0, "_dependencyMap"], [38, 41, 3, 0], [39, 0, 3, 39], [39, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}