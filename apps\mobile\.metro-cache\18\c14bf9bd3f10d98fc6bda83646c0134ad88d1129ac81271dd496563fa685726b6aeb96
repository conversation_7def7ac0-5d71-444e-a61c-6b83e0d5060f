{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../handlersRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "Q8MtNj8/mrt1iN8Kay94o881ERE=", "exportNames": ["*"]}}, {"name": "../../../RNGestureHandlerModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 60}, "end": {"line": 2, "column": 69, "index": 129}}], "key": "2BYIjnTRSFId8SRJ7sJFxLD1BD4=", "exportNames": ["*"]}}, {"name": "../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 130}, "end": {"line": 3, "column": 54, "index": 184}}], "key": "ByXat9lt9duIJLDmSeH0V+tRq1s=", "exportNames": ["*"]}}, {"name": "../../../mountRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 233}, "end": {"line": 5, "column": 55, "index": 288}}], "key": "ZDu7aL2iuT3Od7iyX13y9sY9XZQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.dropHandlers = dropHandlers;\n  var _handlersRegistry = require(_dependencyMap[1], \"../../handlersRegistry\");\n  var _RNGestureHandlerModule = _interopRequireDefault(require(_dependencyMap[2], \"../../../RNGestureHandlerModule\"));\n  var _utils = require(_dependencyMap[3], \"../../utils\");\n  var _mountRegistry = require(_dependencyMap[4], \"../../../mountRegistry\");\n  function dropHandlers(preparedGesture) {\n    for (var handler of preparedGesture.attachedGestures) {\n      _RNGestureHandlerModule.default.dropGestureHandler(handler.handlerTag);\n      (0, _handlersRegistry.unregisterHandler)(handler.handlerTag, handler.config.testId);\n      _mountRegistry.MountRegistry.gestureWillUnmount(handler);\n    }\n    (0, _utils.scheduleFlushOperations)();\n  }\n});", "lineCount": 19, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_handlersRegistry"], [7, 23, 1, 0], [7, 26, 1, 0, "require"], [7, 33, 1, 0], [7, 34, 1, 0, "_dependencyMap"], [7, 48, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_RNGestureHandlerModule"], [8, 29, 2, 0], [8, 32, 2, 0, "_interopRequireDefault"], [8, 54, 2, 0], [8, 55, 2, 0, "require"], [8, 62, 2, 0], [8, 63, 2, 0, "_dependencyMap"], [8, 77, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_utils"], [9, 12, 3, 0], [9, 15, 3, 0, "require"], [9, 22, 3, 0], [9, 23, 3, 0, "_dependencyMap"], [9, 37, 3, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_mountRegistry"], [10, 20, 5, 0], [10, 23, 5, 0, "require"], [10, 30, 5, 0], [10, 31, 5, 0, "_dependencyMap"], [10, 45, 5, 0], [11, 2, 7, 7], [11, 11, 7, 16, "dropHandlers"], [11, 23, 7, 28, "dropHandlers"], [11, 24, 7, 29, "preparedGesture"], [11, 39, 7, 66], [11, 41, 7, 68], [12, 4, 8, 2], [12, 9, 8, 7], [12, 13, 8, 13, "handler"], [12, 20, 8, 20], [12, 24, 8, 24, "preparedGesture"], [12, 39, 8, 39], [12, 40, 8, 40, "attachedGestures"], [12, 56, 8, 56], [12, 58, 8, 58], [13, 6, 9, 4, "RNGestureHandlerModule"], [13, 37, 9, 26], [13, 38, 9, 27, "dropGestureHandler"], [13, 56, 9, 45], [13, 57, 9, 46, "handler"], [13, 64, 9, 53], [13, 65, 9, 54, "handlerTag"], [13, 75, 9, 64], [13, 76, 9, 65], [14, 6, 11, 4], [14, 10, 11, 4, "unregister<PERSON><PERSON><PERSON>"], [14, 45, 11, 21], [14, 47, 11, 22, "handler"], [14, 54, 11, 29], [14, 55, 11, 30, "handlerTag"], [14, 65, 11, 40], [14, 67, 11, 42, "handler"], [14, 74, 11, 49], [14, 75, 11, 50, "config"], [14, 81, 11, 56], [14, 82, 11, 57, "testId"], [14, 88, 11, 63], [14, 89, 11, 64], [15, 6, 13, 4, "MountRegistry"], [15, 34, 13, 17], [15, 35, 13, 18, "gestureWillUnmount"], [15, 53, 13, 36], [15, 54, 13, 37, "handler"], [15, 61, 13, 44], [15, 62, 13, 45], [16, 4, 14, 2], [17, 4, 16, 2], [17, 8, 16, 2, "scheduleFlushOperations"], [17, 38, 16, 25], [17, 40, 16, 26], [17, 41, 16, 27], [18, 2, 17, 0], [19, 0, 17, 1], [19, 3]], "functionMap": {"names": ["<global>", "dropHandlers"], "mappings": "AAA;OCM;CDU"}}, "type": "js/module"}]}