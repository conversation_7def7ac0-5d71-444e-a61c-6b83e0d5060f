{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../specs_DEPRECATED/modules/NativeReactDevToolsSettingsManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 112}}], "key": "tBgZwqTh0Eh222KPhbxK1Rel6sA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  var _NativeReactDevToolsSettingsManager = _interopRequireDefault(require(_dependencyMap[1], \"../specs_DEPRECATED/modules/NativeReactDevToolsSettingsManager\"));\n  module.exports = {\n    setGlobalHookSettings(settings) {\n      _NativeReactDevToolsSettingsManager.default?.setGlobalHookSettings(settings);\n    },\n    getGlobalHookSettings() {\n      return _NativeReactDevToolsSettingsManager.default?.getGlobalHookSettings();\n    }\n  };\n});", "lineCount": 12, "map": [[3, 2, 11, 0], [3, 6, 11, 0, "_NativeReactDevToolsSettingsManager"], [3, 41, 11, 0], [3, 44, 11, 0, "_interopRequireDefault"], [3, 66, 11, 0], [3, 67, 11, 0, "require"], [3, 74, 11, 0], [3, 75, 11, 0, "_dependencyMap"], [3, 89, 11, 0], [4, 2, 13, 0, "module"], [4, 8, 13, 6], [4, 9, 13, 7, "exports"], [4, 16, 13, 14], [4, 19, 13, 17], [5, 4, 14, 2, "setGlobalHookSettings"], [5, 25, 14, 23, "setGlobalHookSettings"], [5, 26, 14, 24, "settings"], [5, 34, 14, 40], [5, 36, 14, 42], [6, 6, 15, 4, "NativeReactDevToolsSettingsManager"], [6, 49, 15, 38], [6, 51, 15, 40, "setGlobalHookSettings"], [6, 72, 15, 61], [6, 73, 15, 62, "settings"], [6, 81, 15, 70], [6, 82, 15, 71], [7, 4, 16, 2], [7, 5, 16, 3], [8, 4, 17, 2, "getGlobalHookSettings"], [8, 25, 17, 23, "getGlobalHookSettings"], [8, 26, 17, 23], [8, 28, 17, 35], [9, 6, 18, 4], [9, 13, 18, 11, "NativeReactDevToolsSettingsManager"], [9, 56, 18, 45], [9, 58, 18, 47, "getGlobalHookSettings"], [9, 79, 18, 68], [9, 80, 18, 69], [9, 81, 18, 70], [10, 4, 19, 2], [11, 2, 20, 0], [11, 3, 20, 1], [12, 0, 20, 2], [12, 3]], "functionMap": {"names": ["<global>", "module.exports.setGlobalHookSettings", "module.exports.getGlobalHookSettings"], "mappings": "AAA;ECa;GDE;EEC;GFE"}}, "type": "js/module"}]}