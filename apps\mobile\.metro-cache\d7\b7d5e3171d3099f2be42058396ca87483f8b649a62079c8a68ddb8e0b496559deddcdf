{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 66, "index": 66}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./shouldFallbackToLegacyNativeModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 67}, "end": {"line": 2, "column": 90, "index": 157}}], "key": "ZWpp/EH+LAbu02DJouYnhA0ujUc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _shouldFallbackToLegacyNativeModule = require(_dependencyMap[1], \"./shouldFallbackToLegacyNativeModule\");\n  // TurboModuleRegistry falls back to NativeModules so we don't have to try go\n  // assign NativeModules' counterparts if TurboModuleRegistry would resolve\n  // with undefined.\n  var RCTAsyncStorage = _reactNative.TurboModuleRegistry ? _reactNative.TurboModuleRegistry.get(\"PlatformLocalStorage\") ||\n  // Support for external modules, like react-native-windows\n  _reactNative.TurboModuleRegistry.get(\"RNC_AsyncSQLiteDBStorage\") || _reactNative.TurboModuleRegistry.get(\"RNCAsyncStorage\") : _reactNative.NativeModules[\"PlatformLocalStorage\"] ||\n  // Support for external modules, like react-native-windows\n  _reactNative.NativeModules[\"RNC_AsyncSQLiteDBStorage\"] || _reactNative.NativeModules[\"RNCAsyncStorage\"];\n  if (!RCTAsyncStorage && (0, _shouldFallbackToLegacyNativeModule.shouldFallbackToLegacyNativeModule)()) {\n    if (_reactNative.TurboModuleRegistry) {\n      RCTAsyncStorage = _reactNative.TurboModuleRegistry.get(\"AsyncSQLiteDBStorage\") || _reactNative.TurboModuleRegistry.get(\"AsyncLocalStorage\");\n    } else {\n      RCTAsyncStorage = _reactNative.NativeModules[\"AsyncSQLiteDBStorage\"] || _reactNative.NativeModules[\"AsyncLocalStorage\"];\n    }\n  }\n  var _default = exports.default = RCTAsyncStorage;\n});", "lineCount": 24, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_reactNative"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_shouldFallbackToLegacyNativeModule"], [7, 41, 2, 0], [7, 44, 2, 0, "require"], [7, 51, 2, 0], [7, 52, 2, 0, "_dependencyMap"], [7, 66, 2, 0], [8, 2, 4, 0], [9, 2, 5, 0], [10, 2, 6, 0], [11, 2, 7, 0], [11, 6, 7, 4, "RCTAsyncStorage"], [11, 21, 7, 19], [11, 24, 7, 22, "TurboModuleRegistry"], [11, 56, 7, 41], [11, 59, 8, 4, "TurboModuleRegistry"], [11, 91, 8, 23], [11, 92, 8, 24, "get"], [11, 95, 8, 27], [11, 96, 8, 28], [11, 118, 8, 50], [11, 119, 8, 51], [12, 2, 8, 55], [13, 2, 9, 4, "TurboModuleRegistry"], [13, 34, 9, 23], [13, 35, 9, 24, "get"], [13, 38, 9, 27], [13, 39, 9, 28], [13, 65, 9, 54], [13, 66, 9, 55], [13, 70, 10, 4, "TurboModuleRegistry"], [13, 102, 10, 23], [13, 103, 10, 24, "get"], [13, 106, 10, 27], [13, 107, 10, 28], [13, 124, 10, 45], [13, 125, 10, 46], [13, 128, 11, 4, "NativeModules"], [13, 154, 11, 17], [13, 155, 11, 18], [13, 177, 11, 40], [13, 178, 11, 41], [14, 2, 11, 45], [15, 2, 12, 4, "NativeModules"], [15, 28, 12, 17], [15, 29, 12, 18], [15, 55, 12, 44], [15, 56, 12, 45], [15, 60, 13, 4, "NativeModules"], [15, 86, 13, 17], [15, 87, 13, 18], [15, 104, 13, 35], [15, 105, 13, 36], [16, 2, 15, 0], [16, 6, 15, 4], [16, 7, 15, 5, "RCTAsyncStorage"], [16, 22, 15, 20], [16, 26, 15, 24], [16, 30, 15, 24, "shouldFallbackToLegacyNativeModule"], [16, 100, 15, 58], [16, 102, 15, 59], [16, 103, 15, 60], [16, 105, 15, 62], [17, 4, 16, 2], [17, 8, 16, 6, "TurboModuleRegistry"], [17, 40, 16, 25], [17, 42, 16, 27], [18, 6, 17, 4, "RCTAsyncStorage"], [18, 21, 17, 19], [18, 24, 18, 6, "TurboModuleRegistry"], [18, 56, 18, 25], [18, 57, 18, 26, "get"], [18, 60, 18, 29], [18, 61, 18, 30], [18, 83, 18, 52], [18, 84, 18, 53], [18, 88, 19, 6, "TurboModuleRegistry"], [18, 120, 19, 25], [18, 121, 19, 26, "get"], [18, 124, 19, 29], [18, 125, 19, 30], [18, 144, 19, 49], [18, 145, 19, 50], [19, 4, 20, 2], [19, 5, 20, 3], [19, 11, 20, 9], [20, 6, 21, 4, "RCTAsyncStorage"], [20, 21, 21, 19], [20, 24, 22, 6, "NativeModules"], [20, 50, 22, 19], [20, 51, 22, 20], [20, 73, 22, 42], [20, 74, 22, 43], [20, 78, 23, 6, "NativeModules"], [20, 104, 23, 19], [20, 105, 23, 20], [20, 124, 23, 39], [20, 125, 23, 40], [21, 4, 24, 2], [22, 2, 25, 0], [23, 2, 25, 1], [23, 6, 25, 1, "_default"], [23, 14, 25, 1], [23, 17, 25, 1, "exports"], [23, 24, 25, 1], [23, 25, 25, 1, "default"], [23, 32, 25, 1], [23, 35, 27, 15, "RCTAsyncStorage"], [23, 50, 27, 30], [24, 0, 27, 30], [24, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}