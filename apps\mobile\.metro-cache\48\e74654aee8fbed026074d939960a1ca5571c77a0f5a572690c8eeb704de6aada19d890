{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 7, "column": 15, "index": 97}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 98}, "end": {"line": 12, "column": 22, "index": 187}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../handlers/gestures/gestureObjects", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 189}, "end": {"line": 14, "column": 80, "index": 269}}], "key": "X4nQ5kAWXqGrNUjxr0fY33k4BYA=", "exportNames": ["*"]}}, {"name": "../handlers/gestures/GestureDetector", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 270}, "end": {"line": 15, "column": 71, "index": 341}}], "key": "vyL1g9JGFtGIJQswwuFZEiJvFNw=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Text = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _gestureObjects = require(_dependencyMap[4], \"../handlers/gestures/gestureObjects\");\n  var _GestureDetector = require(_dependencyMap[5], \"../handlers/gestures/GestureDetector\");\n  var _jsxDevRuntime = require(_dependencyMap[6], \"react/jsx-dev-runtime\");\n  var _excluded = [\"onPress\", \"onLongPress\"];\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\components\\\\Text.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var Text = exports.Text = /*#__PURE__*/(0, _react.forwardRef)((props, ref) => {\n    var onPress = props.onPress,\n      onLongPress = props.onLongPress,\n      rest = (0, _objectWithoutProperties2.default)(props, _excluded);\n    var textRef = (0, _react.useRef)(null);\n    var native = _gestureObjects.GestureObjects.Native().runOnJS(true);\n    var refHandler = node => {\n      textRef.current = node;\n      if (ref === null) {\n        return;\n      }\n      if (typeof ref === 'function') {\n        ref(node);\n      } else {\n        ref.current = node;\n      }\n    };\n\n    // This is a special case for `Text` component. After https://github.com/software-mansion/react-native-gesture-handler/pull/3379 we check for\n    // `displayName` field. However, `Text` from RN has this field set to `Text`, but is also present in `RNSVGElements` set.\n    // We don't want to treat our `Text` as the one from `SVG`, therefore we add special field to ref.\n    refHandler.rngh = true;\n    (0, _react.useEffect)(() => {\n      if (_reactNative.Platform.OS !== 'web') {\n        return;\n      }\n      var textElement = ref ? ref.current : textRef.current;\n\n      // At this point we are sure that textElement is div in HTML tree\n      textElement?.setAttribute('rnghtext', 'true');\n    }, []);\n    return onPress || onLongPress ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_GestureDetector.GestureDetector, {\n      gesture: native,\n      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n        onPress: onPress,\n        onLongPress: onLongPress,\n        ref: refHandler,\n        ...rest\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n      ref: ref,\n      ...rest\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this);\n  });\n  // eslint-disable-next-line @typescript-eslint/no-redeclare\n});", "lineCount": 73, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireWildcard"], [8, 38, 1, 0], [8, 39, 1, 0, "require"], [8, 46, 1, 0], [8, 47, 1, 0, "_dependencyMap"], [8, 61, 1, 0], [9, 2, 8, 0], [9, 6, 8, 0, "_reactNative"], [9, 18, 8, 0], [9, 21, 8, 0, "require"], [9, 28, 8, 0], [9, 29, 8, 0, "_dependencyMap"], [9, 43, 8, 0], [10, 2, 14, 0], [10, 6, 14, 0, "_gestureObjects"], [10, 21, 14, 0], [10, 24, 14, 0, "require"], [10, 31, 14, 0], [10, 32, 14, 0, "_dependencyMap"], [10, 46, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "_GestureDetector"], [11, 22, 15, 0], [11, 25, 15, 0, "require"], [11, 32, 15, 0], [11, 33, 15, 0, "_dependencyMap"], [11, 47, 15, 0], [12, 2, 15, 71], [12, 6, 15, 71, "_jsxDevRuntime"], [12, 20, 15, 71], [12, 23, 15, 71, "require"], [12, 30, 15, 71], [12, 31, 15, 71, "_dependencyMap"], [12, 45, 15, 71], [13, 2, 15, 71], [13, 6, 15, 71, "_excluded"], [13, 15, 15, 71], [14, 2, 15, 71], [14, 6, 15, 71, "_jsxFileName"], [14, 18, 15, 71], [15, 2, 15, 71], [15, 11, 15, 71, "_interopRequireWildcard"], [15, 35, 15, 71, "e"], [15, 36, 15, 71], [15, 38, 15, 71, "t"], [15, 39, 15, 71], [15, 68, 15, 71, "WeakMap"], [15, 75, 15, 71], [15, 81, 15, 71, "r"], [15, 82, 15, 71], [15, 89, 15, 71, "WeakMap"], [15, 96, 15, 71], [15, 100, 15, 71, "n"], [15, 101, 15, 71], [15, 108, 15, 71, "WeakMap"], [15, 115, 15, 71], [15, 127, 15, 71, "_interopRequireWildcard"], [15, 150, 15, 71], [15, 162, 15, 71, "_interopRequireWildcard"], [15, 163, 15, 71, "e"], [15, 164, 15, 71], [15, 166, 15, 71, "t"], [15, 167, 15, 71], [15, 176, 15, 71, "t"], [15, 177, 15, 71], [15, 181, 15, 71, "e"], [15, 182, 15, 71], [15, 186, 15, 71, "e"], [15, 187, 15, 71], [15, 188, 15, 71, "__esModule"], [15, 198, 15, 71], [15, 207, 15, 71, "e"], [15, 208, 15, 71], [15, 214, 15, 71, "o"], [15, 215, 15, 71], [15, 217, 15, 71, "i"], [15, 218, 15, 71], [15, 220, 15, 71, "f"], [15, 221, 15, 71], [15, 226, 15, 71, "__proto__"], [15, 235, 15, 71], [15, 243, 15, 71, "default"], [15, 250, 15, 71], [15, 252, 15, 71, "e"], [15, 253, 15, 71], [15, 270, 15, 71, "e"], [15, 271, 15, 71], [15, 294, 15, 71, "e"], [15, 295, 15, 71], [15, 320, 15, 71, "e"], [15, 321, 15, 71], [15, 330, 15, 71, "f"], [15, 331, 15, 71], [15, 337, 15, 71, "o"], [15, 338, 15, 71], [15, 341, 15, 71, "t"], [15, 342, 15, 71], [15, 345, 15, 71, "n"], [15, 346, 15, 71], [15, 349, 15, 71, "r"], [15, 350, 15, 71], [15, 358, 15, 71, "o"], [15, 359, 15, 71], [15, 360, 15, 71, "has"], [15, 363, 15, 71], [15, 364, 15, 71, "e"], [15, 365, 15, 71], [15, 375, 15, 71, "o"], [15, 376, 15, 71], [15, 377, 15, 71, "get"], [15, 380, 15, 71], [15, 381, 15, 71, "e"], [15, 382, 15, 71], [15, 385, 15, 71, "o"], [15, 386, 15, 71], [15, 387, 15, 71, "set"], [15, 390, 15, 71], [15, 391, 15, 71, "e"], [15, 392, 15, 71], [15, 394, 15, 71, "f"], [15, 395, 15, 71], [15, 409, 15, 71, "_t"], [15, 411, 15, 71], [15, 415, 15, 71, "e"], [15, 416, 15, 71], [15, 432, 15, 71, "_t"], [15, 434, 15, 71], [15, 441, 15, 71, "hasOwnProperty"], [15, 455, 15, 71], [15, 456, 15, 71, "call"], [15, 460, 15, 71], [15, 461, 15, 71, "e"], [15, 462, 15, 71], [15, 464, 15, 71, "_t"], [15, 466, 15, 71], [15, 473, 15, 71, "i"], [15, 474, 15, 71], [15, 478, 15, 71, "o"], [15, 479, 15, 71], [15, 482, 15, 71, "Object"], [15, 488, 15, 71], [15, 489, 15, 71, "defineProperty"], [15, 503, 15, 71], [15, 508, 15, 71, "Object"], [15, 514, 15, 71], [15, 515, 15, 71, "getOwnPropertyDescriptor"], [15, 539, 15, 71], [15, 540, 15, 71, "e"], [15, 541, 15, 71], [15, 543, 15, 71, "_t"], [15, 545, 15, 71], [15, 552, 15, 71, "i"], [15, 553, 15, 71], [15, 554, 15, 71, "get"], [15, 557, 15, 71], [15, 561, 15, 71, "i"], [15, 562, 15, 71], [15, 563, 15, 71, "set"], [15, 566, 15, 71], [15, 570, 15, 71, "o"], [15, 571, 15, 71], [15, 572, 15, 71, "f"], [15, 573, 15, 71], [15, 575, 15, 71, "_t"], [15, 577, 15, 71], [15, 579, 15, 71, "i"], [15, 580, 15, 71], [15, 584, 15, 71, "f"], [15, 585, 15, 71], [15, 586, 15, 71, "_t"], [15, 588, 15, 71], [15, 592, 15, 71, "e"], [15, 593, 15, 71], [15, 594, 15, 71, "_t"], [15, 596, 15, 71], [15, 607, 15, 71, "f"], [15, 608, 15, 71], [15, 613, 15, 71, "e"], [15, 614, 15, 71], [15, 616, 15, 71, "t"], [15, 617, 15, 71], [16, 2, 17, 7], [16, 6, 17, 13, "Text"], [16, 10, 17, 17], [16, 13, 17, 17, "exports"], [16, 20, 17, 17], [16, 21, 17, 17, "Text"], [16, 25, 17, 17], [16, 41, 17, 20], [16, 45, 17, 20, "forwardRef"], [16, 62, 17, 30], [16, 64, 18, 2], [16, 65, 19, 4, "props"], [16, 70, 19, 22], [16, 72, 20, 4, "ref"], [16, 75, 20, 56], [16, 80, 21, 7], [17, 4, 22, 4], [17, 8, 22, 12, "onPress"], [17, 15, 22, 19], [17, 18, 22, 46, "props"], [17, 23, 22, 51], [17, 24, 22, 12, "onPress"], [17, 31, 22, 19], [18, 6, 22, 21, "onLongPress"], [18, 17, 22, 32], [18, 20, 22, 46, "props"], [18, 25, 22, 51], [18, 26, 22, 21, "onLongPress"], [18, 37, 22, 32], [19, 6, 22, 37, "rest"], [19, 10, 22, 41], [19, 17, 22, 41, "_objectWithoutProperties2"], [19, 42, 22, 41], [19, 43, 22, 41, "default"], [19, 50, 22, 41], [19, 52, 22, 46, "props"], [19, 57, 22, 51], [19, 59, 22, 51, "_excluded"], [19, 68, 22, 51], [20, 4, 24, 4], [20, 8, 24, 10, "textRef"], [20, 15, 24, 17], [20, 18, 24, 20], [20, 22, 24, 20, "useRef"], [20, 35, 24, 26], [20, 37, 24, 42], [20, 41, 24, 46], [20, 42, 24, 47], [21, 4, 25, 4], [21, 8, 25, 10, "native"], [21, 14, 25, 16], [21, 17, 25, 19, "Gesture"], [21, 47, 25, 26], [21, 48, 25, 27, "Native"], [21, 54, 25, 33], [21, 55, 25, 34], [21, 56, 25, 35], [21, 57, 25, 36, "runOnJS"], [21, 64, 25, 43], [21, 65, 25, 44], [21, 69, 25, 48], [21, 70, 25, 49], [22, 4, 27, 4], [22, 8, 27, 10, "ref<PERSON><PERSON><PERSON>"], [22, 18, 27, 20], [22, 21, 27, 24, "node"], [22, 25, 27, 33], [22, 29, 27, 38], [23, 6, 28, 6, "textRef"], [23, 13, 28, 13], [23, 14, 28, 14, "current"], [23, 21, 28, 21], [23, 24, 28, 24, "node"], [23, 28, 28, 28], [24, 6, 30, 6], [24, 10, 30, 10, "ref"], [24, 13, 30, 13], [24, 18, 30, 18], [24, 22, 30, 22], [24, 24, 30, 24], [25, 8, 31, 8], [26, 6, 32, 6], [27, 6, 34, 6], [27, 10, 34, 10], [27, 17, 34, 17, "ref"], [27, 20, 34, 20], [27, 25, 34, 25], [27, 35, 34, 35], [27, 37, 34, 37], [28, 8, 35, 8, "ref"], [28, 11, 35, 11], [28, 12, 35, 12, "node"], [28, 16, 35, 16], [28, 17, 35, 17], [29, 6, 36, 6], [29, 7, 36, 7], [29, 13, 36, 13], [30, 8, 37, 8, "ref"], [30, 11, 37, 11], [30, 12, 37, 12, "current"], [30, 19, 37, 19], [30, 22, 37, 22, "node"], [30, 26, 37, 26], [31, 6, 38, 6], [32, 4, 39, 4], [32, 5, 39, 5], [34, 4, 41, 4], [35, 4, 42, 4], [36, 4, 43, 4], [37, 4, 44, 4, "ref<PERSON><PERSON><PERSON>"], [37, 14, 44, 14], [37, 15, 44, 15, "rngh"], [37, 19, 44, 19], [37, 22, 44, 22], [37, 26, 44, 26], [38, 4, 46, 4], [38, 8, 46, 4, "useEffect"], [38, 24, 46, 13], [38, 26, 46, 14], [38, 32, 46, 20], [39, 6, 47, 6], [39, 10, 47, 10, "Platform"], [39, 31, 47, 18], [39, 32, 47, 19, "OS"], [39, 34, 47, 21], [39, 39, 47, 26], [39, 44, 47, 31], [39, 46, 47, 33], [40, 8, 48, 8], [41, 6, 49, 6], [42, 6, 51, 6], [42, 10, 51, 12, "textElement"], [42, 21, 51, 23], [42, 24, 51, 26, "ref"], [42, 27, 51, 29], [42, 30, 52, 11, "ref"], [42, 33, 52, 14], [42, 34, 52, 64, "current"], [42, 41, 52, 71], [42, 44, 53, 10, "textRef"], [42, 51, 53, 17], [42, 52, 53, 18, "current"], [42, 59, 53, 25], [44, 6, 55, 6], [45, 6, 56, 7, "textElement"], [45, 17, 56, 18], [45, 19, 56, 50, "setAttribute"], [45, 31, 56, 62], [45, 32, 57, 8], [45, 42, 57, 18], [45, 44, 58, 8], [45, 50, 59, 6], [45, 51, 59, 7], [46, 4, 60, 4], [46, 5, 60, 5], [46, 7, 60, 7], [46, 9, 60, 9], [46, 10, 60, 10], [47, 4, 62, 4], [47, 11, 62, 11, "onPress"], [47, 18, 62, 18], [47, 22, 62, 22, "onLongPress"], [47, 33, 62, 33], [47, 49, 63, 6], [47, 53, 63, 6, "_jsxDevRuntime"], [47, 67, 63, 6], [47, 68, 63, 6, "jsxDEV"], [47, 74, 63, 6], [47, 76, 63, 7, "_GestureDetector"], [47, 92, 63, 7], [47, 93, 63, 7, "GestureDetector"], [47, 108, 63, 22], [48, 6, 63, 23, "gesture"], [48, 13, 63, 30], [48, 15, 63, 32, "native"], [48, 21, 63, 39], [49, 6, 63, 39, "children"], [49, 14, 63, 39], [49, 29, 64, 8], [49, 33, 64, 8, "_jsxDevRuntime"], [49, 47, 64, 8], [49, 48, 64, 8, "jsxDEV"], [49, 54, 64, 8], [49, 56, 64, 9, "_reactNative"], [49, 68, 64, 9], [49, 69, 64, 9, "Text"], [49, 73, 64, 15], [50, 8, 65, 10, "onPress"], [50, 15, 65, 17], [50, 17, 65, 19, "onPress"], [50, 24, 65, 27], [51, 8, 66, 10, "onLongPress"], [51, 19, 66, 21], [51, 21, 66, 23, "onLongPress"], [51, 32, 66, 35], [52, 8, 67, 10, "ref"], [52, 11, 67, 13], [52, 13, 67, 15, "ref<PERSON><PERSON><PERSON>"], [52, 23, 67, 26], [53, 8, 67, 26], [53, 11, 68, 14, "rest"], [54, 6, 68, 18], [55, 8, 68, 18, "fileName"], [55, 16, 68, 18], [55, 18, 68, 18, "_jsxFileName"], [55, 30, 68, 18], [56, 8, 68, 18, "lineNumber"], [56, 18, 68, 18], [57, 8, 68, 18, "columnNumber"], [57, 20, 68, 18], [58, 6, 68, 18], [58, 13, 69, 9], [59, 4, 69, 10], [60, 6, 69, 10, "fileName"], [60, 14, 69, 10], [60, 16, 69, 10, "_jsxFileName"], [60, 28, 69, 10], [61, 6, 69, 10, "lineNumber"], [61, 16, 69, 10], [62, 6, 69, 10, "columnNumber"], [62, 18, 69, 10], [63, 4, 69, 10], [63, 11, 70, 23], [63, 12, 70, 24], [63, 28, 72, 6], [63, 32, 72, 6, "_jsxDevRuntime"], [63, 46, 72, 6], [63, 47, 72, 6, "jsxDEV"], [63, 53, 72, 6], [63, 55, 72, 7, "_reactNative"], [63, 67, 72, 7], [63, 68, 72, 7, "Text"], [63, 72, 72, 13], [64, 6, 72, 14, "ref"], [64, 9, 72, 17], [64, 11, 72, 19, "ref"], [64, 14, 72, 23], [65, 6, 72, 23], [65, 9, 72, 28, "rest"], [66, 4, 72, 32], [67, 6, 72, 32, "fileName"], [67, 14, 72, 32], [67, 16, 72, 32, "_jsxFileName"], [67, 28, 72, 32], [68, 6, 72, 32, "lineNumber"], [68, 16, 72, 32], [69, 6, 72, 32, "columnNumber"], [69, 18, 72, 32], [70, 4, 72, 32], [70, 11, 72, 35], [70, 12, 73, 5], [71, 2, 74, 2], [71, 3, 75, 0], [71, 4, 75, 1], [72, 2, 76, 0], [73, 0, 76, 0], [73, 3]], "functionMap": {"names": ["<global>", "forwardRef$argument_0", "ref<PERSON><PERSON><PERSON>", "useEffect$argument_0"], "mappings": "AAA;ECiB;uBCS;KDY;cEO;KFc;GDc"}}, "type": "js/module"}]}