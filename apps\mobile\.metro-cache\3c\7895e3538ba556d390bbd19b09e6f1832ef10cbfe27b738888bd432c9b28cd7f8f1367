{"dependencies": [{"name": "../Core/InitializeCore", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 32}}], "key": "eZBKq8f6mvv+BuBCc9iR8CcOr+k=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  require(_dependencyMap[0], \"../Core/InitializeCore\");\n});", "lineCount": 3, "map": [[2, 2, 11, 0, "require"], [2, 9, 11, 0], [2, 10, 11, 0, "_dependencyMap"], [2, 24, 11, 0], [3, 0, 11, 32], [3, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}