{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  function _getPrototypeOf(t) {\n    return module.exports = _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n      return t.__proto__ || Object.getPrototypeOf(t);\n    }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _getPrototypeOf(t);\n  }\n  module.exports = _getPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 8, "map": [[2, 2, 1, 0], [2, 11, 1, 9, "_getPrototypeOf"], [2, 26, 1, 24, "_getPrototypeOf"], [2, 27, 1, 25, "t"], [2, 28, 1, 26], [2, 30, 1, 28], [3, 4, 2, 2], [3, 11, 2, 9, "module"], [3, 17, 2, 15], [3, 18, 2, 16, "exports"], [3, 25, 2, 23], [3, 28, 2, 26, "_getPrototypeOf"], [3, 43, 2, 41], [3, 46, 2, 44, "Object"], [3, 52, 2, 50], [3, 53, 2, 51, "setPrototypeOf"], [3, 67, 2, 65], [3, 70, 2, 68, "Object"], [3, 76, 2, 74], [3, 77, 2, 75, "getPrototypeOf"], [3, 91, 2, 89], [3, 92, 2, 90, "bind"], [3, 96, 2, 94], [3, 97, 2, 95], [3, 98, 2, 96], [3, 101, 2, 99], [3, 111, 2, 109, "t"], [3, 112, 2, 110], [3, 114, 2, 112], [4, 6, 3, 4], [4, 13, 3, 11, "t"], [4, 14, 3, 12], [4, 15, 3, 13, "__proto__"], [4, 24, 3, 22], [4, 28, 3, 26, "Object"], [4, 34, 3, 32], [4, 35, 3, 33, "getPrototypeOf"], [4, 49, 3, 47], [4, 50, 3, 48, "t"], [4, 51, 3, 49], [4, 52, 3, 50], [5, 4, 4, 2], [5, 5, 4, 3], [5, 7, 4, 5, "module"], [5, 13, 4, 11], [5, 14, 4, 12, "exports"], [5, 21, 4, 19], [5, 22, 4, 20, "__esModule"], [5, 32, 4, 30], [5, 35, 4, 33], [5, 39, 4, 37], [5, 41, 4, 39, "module"], [5, 47, 4, 45], [5, 48, 4, 46, "exports"], [5, 55, 4, 53], [5, 56, 4, 54], [5, 65, 4, 63], [5, 66, 4, 64], [5, 69, 4, 67, "module"], [5, 75, 4, 73], [5, 76, 4, 74, "exports"], [5, 83, 4, 81], [5, 85, 4, 83, "_getPrototypeOf"], [5, 100, 4, 98], [5, 101, 4, 99, "t"], [5, 102, 4, 100], [5, 103, 4, 101], [6, 2, 5, 0], [7, 2, 6, 0, "module"], [7, 8, 6, 6], [7, 9, 6, 7, "exports"], [7, 16, 6, 14], [7, 19, 6, 17, "_getPrototypeOf"], [7, 34, 6, 32], [7, 36, 6, 34, "module"], [7, 42, 6, 40], [7, 43, 6, 41, "exports"], [7, 50, 6, 48], [7, 51, 6, 49, "__esModule"], [7, 61, 6, 59], [7, 64, 6, 62], [7, 68, 6, 66], [7, 70, 6, 68, "module"], [7, 76, 6, 74], [7, 77, 6, 75, "exports"], [7, 84, 6, 82], [7, 85, 6, 83], [7, 94, 6, 92], [7, 95, 6, 93], [7, 98, 6, 96, "module"], [7, 104, 6, 102], [7, 105, 6, 103, "exports"], [7, 112, 6, 110], [8, 0, 6, 111], [8, 3]], "functionMap": {"names": ["_getPrototypeOf", "<anonymous>", "<global>"], "mappings": "AAA;mGCC;GDE;CEC"}}, "type": "js/module"}]}