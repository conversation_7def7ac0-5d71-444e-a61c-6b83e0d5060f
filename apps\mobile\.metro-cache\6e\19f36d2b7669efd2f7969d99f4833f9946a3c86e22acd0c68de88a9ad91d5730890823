{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 42, "index": 57}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../Text.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 58}, "end": {"line": 4, "column": 34, "index": 92}}], "key": "UfNR+WZdGHHR+kk13ETrBegm38s=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 93}, "end": {"line": 5, "column": 48, "index": 141}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Label = Label;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _Text = require(_dependencyMap[3], \"../Text.js\");\n  var _jsxRuntime = require(_dependencyMap[4], \"react/jsx-runtime\");\n  var _excluded = [\"tintColor\", \"style\"];\n  function Label(_ref) {\n    var tintColor = _ref.tintColor,\n      style = _ref.style,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Text.Text, {\n      numberOfLines: 1,\n      ...rest,\n      style: [styles.label, tintColor != null && {\n        color: tintColor\n      }, style]\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    label: {\n      textAlign: 'center',\n      backgroundColor: 'transparent'\n    }\n  });\n});", "lineCount": 32, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Label"], [8, 15, 1, 13], [8, 18, 1, 13, "Label"], [8, 23, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_reactNative"], [10, 18, 3, 0], [10, 21, 3, 0, "require"], [10, 28, 3, 0], [10, 29, 3, 0, "_dependencyMap"], [10, 43, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_Text"], [11, 11, 4, 0], [11, 14, 4, 0, "require"], [11, 21, 4, 0], [11, 22, 4, 0, "_dependencyMap"], [11, 36, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_jsxRuntime"], [12, 17, 5, 0], [12, 20, 5, 0, "require"], [12, 27, 5, 0], [12, 28, 5, 0, "_dependencyMap"], [12, 42, 5, 0], [13, 2, 5, 48], [13, 6, 5, 48, "_excluded"], [13, 15, 5, 48], [14, 2, 6, 7], [14, 11, 6, 16, "Label"], [14, 16, 6, 21, "Label"], [14, 17, 6, 21, "_ref"], [14, 21, 6, 21], [14, 23, 10, 3], [15, 4, 10, 3], [15, 8, 7, 2, "tintColor"], [15, 17, 7, 11], [15, 20, 7, 11, "_ref"], [15, 24, 7, 11], [15, 25, 7, 2, "tintColor"], [15, 34, 7, 11], [16, 6, 8, 2, "style"], [16, 11, 8, 7], [16, 14, 8, 7, "_ref"], [16, 18, 8, 7], [16, 19, 8, 2, "style"], [16, 24, 8, 7], [17, 6, 9, 5, "rest"], [17, 10, 9, 9], [17, 17, 9, 9, "_objectWithoutProperties2"], [17, 42, 9, 9], [17, 43, 9, 9, "default"], [17, 50, 9, 9], [17, 52, 9, 9, "_ref"], [17, 56, 9, 9], [17, 58, 9, 9, "_excluded"], [17, 67, 9, 9], [18, 4, 11, 2], [18, 11, 11, 9], [18, 24, 11, 22], [18, 28, 11, 22, "_jsx"], [18, 43, 11, 26], [18, 45, 11, 27, "Text"], [18, 55, 11, 31], [18, 57, 11, 33], [19, 6, 12, 4, "numberOfLines"], [19, 19, 12, 17], [19, 21, 12, 19], [19, 22, 12, 20], [20, 6, 13, 4], [20, 9, 13, 7, "rest"], [20, 13, 13, 11], [21, 6, 14, 4, "style"], [21, 11, 14, 9], [21, 13, 14, 11], [21, 14, 14, 12, "styles"], [21, 20, 14, 18], [21, 21, 14, 19, "label"], [21, 26, 14, 24], [21, 28, 14, 26, "tintColor"], [21, 37, 14, 35], [21, 41, 14, 39], [21, 45, 14, 43], [21, 49, 14, 47], [22, 8, 15, 6, "color"], [22, 13, 15, 11], [22, 15, 15, 13, "tintColor"], [23, 6, 16, 4], [23, 7, 16, 5], [23, 9, 16, 7, "style"], [23, 14, 16, 12], [24, 4, 17, 2], [24, 5, 17, 3], [24, 6, 17, 4], [25, 2, 18, 0], [26, 2, 19, 0], [26, 6, 19, 6, "styles"], [26, 12, 19, 12], [26, 15, 19, 15, "StyleSheet"], [26, 38, 19, 25], [26, 39, 19, 26, "create"], [26, 45, 19, 32], [26, 46, 19, 33], [27, 4, 20, 2, "label"], [27, 9, 20, 7], [27, 11, 20, 9], [28, 6, 21, 4, "textAlign"], [28, 15, 21, 13], [28, 17, 21, 15], [28, 25, 21, 23], [29, 6, 22, 4, "backgroundColor"], [29, 21, 22, 19], [29, 23, 22, 21], [30, 4, 23, 2], [31, 2, 24, 0], [31, 3, 24, 1], [31, 4, 24, 2], [32, 0, 24, 3], [32, 3]], "functionMap": {"names": ["<global>", "Label"], "mappings": "AAA;OCK;CDY"}}, "type": "js/module"}]}