# ENTERPRISE PNPM CONFIGURATION FOR METRO BUNDLER COMPATIBILITY
# This configuration is REQUIRED for React Native/Expo apps in monorepos

# CRITICAL: Use hoisted node_modules structure for Metro bundler compatibility
# Metro bundler cannot resolve dependencies through pnpm's symlink structure
node-linker=hoisted

# Package manager specification
package-manager=pnpm

# Optimize for monorepo performance
prefer-workspace-packages=true
link-workspace-packages=true

# Prevent phantom dependencies while maintaining compatibility
hoist-pattern[]=*

# React Native specific optimizations
# Ensure React Native dependencies are hoisted to avoid resolution issues
public-hoist-pattern[]=*react-native*
public-hoist-pattern[]=*expo*
public-hoist-pattern[]=*@expo*
public-hoist-pattern[]=*@react-native*
public-hoist-pattern[]=*@babel*

# Performance optimizations for large monorepos
registry=https://registry.npmjs.org/
network-timeout=60000
fetch-retries=3
fetch-retry-factor=2
fetch-retry-mintimeout=10000
fetch-retry-maxtimeout=60000

# Cache optimizations
store-dir=~/.pnpm-store
cache-dir=~/.pnpm-cache

# Strict peer dependencies for enterprise reliability
strict-peer-dependencies=false
auto-install-peers=true

# Workspace optimizations
shared-workspace-lockfile=true
save-workspace-protocol=rolling
