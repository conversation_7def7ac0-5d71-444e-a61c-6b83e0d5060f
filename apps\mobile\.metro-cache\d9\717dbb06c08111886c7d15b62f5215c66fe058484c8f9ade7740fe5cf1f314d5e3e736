{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.FadingTransition = FadingTransition;\n  function FadingTransition(name, transitionData) {\n    var translateX = transitionData.translateX,\n      translateY = transitionData.translateY,\n      scaleX = transitionData.scaleX,\n      scaleY = transitionData.scaleY;\n    var fadingTransition = {\n      name,\n      style: {\n        0: {\n          opacity: 1,\n          transform: [{\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }]\n        },\n        20: {\n          opacity: 0,\n          transform: [{\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }]\n        },\n        60: {\n          opacity: 0,\n          transform: [{\n            translateX: '0px',\n            translateY: '0px',\n            scale: `1,1`\n          }]\n        },\n        100: {\n          opacity: 1,\n          transform: [{\n            translateX: '0px',\n            translateY: '0px',\n            scale: `1,1`\n          }]\n        }\n      },\n      duration: 300\n    };\n    return fadingTransition;\n  }\n});", "lineCount": 53, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "FadingTransition"], [7, 26, 1, 13], [7, 29, 1, 13, "FadingTransition"], [7, 45, 1, 13], [8, 2, 4, 7], [8, 11, 4, 16, "FadingTransition"], [8, 27, 4, 32, "FadingTransition"], [8, 28, 4, 33, "name"], [8, 32, 4, 45], [8, 34, 4, 47, "transitionData"], [8, 48, 4, 77], [8, 50, 4, 79], [9, 4, 5, 2], [9, 8, 5, 10, "translateX"], [9, 18, 5, 20], [9, 21, 5, 53, "transitionData"], [9, 35, 5, 67], [9, 36, 5, 10, "translateX"], [9, 46, 5, 20], [10, 6, 5, 22, "translateY"], [10, 16, 5, 32], [10, 19, 5, 53, "transitionData"], [10, 33, 5, 67], [10, 34, 5, 22, "translateY"], [10, 44, 5, 32], [11, 6, 5, 34, "scaleX"], [11, 12, 5, 40], [11, 15, 5, 53, "transitionData"], [11, 29, 5, 67], [11, 30, 5, 34, "scaleX"], [11, 36, 5, 40], [12, 6, 5, 42, "scaleY"], [12, 12, 5, 48], [12, 15, 5, 53, "transitionData"], [12, 29, 5, 67], [12, 30, 5, 42, "scaleY"], [12, 36, 5, 48], [13, 4, 7, 2], [13, 8, 7, 8, "fadingTransition"], [13, 24, 7, 24], [13, 27, 7, 27], [14, 6, 8, 4, "name"], [14, 10, 8, 8], [15, 6, 9, 4, "style"], [15, 11, 9, 9], [15, 13, 9, 11], [16, 8, 10, 6], [16, 9, 10, 7], [16, 11, 10, 9], [17, 10, 11, 8, "opacity"], [17, 17, 11, 15], [17, 19, 11, 17], [17, 20, 11, 18], [18, 10, 12, 8, "transform"], [18, 19, 12, 17], [18, 21, 12, 19], [18, 22, 13, 10], [19, 12, 14, 12, "translateX"], [19, 22, 14, 22], [19, 24, 14, 24], [19, 27, 14, 27, "translateX"], [19, 37, 14, 37], [19, 41, 14, 41], [20, 12, 15, 12, "translateY"], [20, 22, 15, 22], [20, 24, 15, 24], [20, 27, 15, 27, "translateY"], [20, 37, 15, 37], [20, 41, 15, 41], [21, 12, 16, 12, "scale"], [21, 17, 16, 17], [21, 19, 16, 19], [21, 22, 16, 22, "scaleX"], [21, 28, 16, 28], [21, 32, 16, 32, "scaleY"], [21, 38, 16, 38], [22, 10, 17, 10], [22, 11, 17, 11], [23, 8, 19, 6], [23, 9, 19, 7], [24, 8, 20, 6], [24, 10, 20, 8], [24, 12, 20, 10], [25, 10, 21, 8, "opacity"], [25, 17, 21, 15], [25, 19, 21, 17], [25, 20, 21, 18], [26, 10, 22, 8, "transform"], [26, 19, 22, 17], [26, 21, 22, 19], [26, 22, 23, 10], [27, 12, 24, 12, "translateX"], [27, 22, 24, 22], [27, 24, 24, 24], [27, 27, 24, 27, "translateX"], [27, 37, 24, 37], [27, 41, 24, 41], [28, 12, 25, 12, "translateY"], [28, 22, 25, 22], [28, 24, 25, 24], [28, 27, 25, 27, "translateY"], [28, 37, 25, 37], [28, 41, 25, 41], [29, 12, 26, 12, "scale"], [29, 17, 26, 17], [29, 19, 26, 19], [29, 22, 26, 22, "scaleX"], [29, 28, 26, 28], [29, 32, 26, 32, "scaleY"], [29, 38, 26, 38], [30, 10, 27, 10], [30, 11, 27, 11], [31, 8, 29, 6], [31, 9, 29, 7], [32, 8, 30, 6], [32, 10, 30, 8], [32, 12, 30, 10], [33, 10, 31, 8, "opacity"], [33, 17, 31, 15], [33, 19, 31, 17], [33, 20, 31, 18], [34, 10, 32, 8, "transform"], [34, 19, 32, 17], [34, 21, 32, 19], [34, 22, 33, 10], [35, 12, 34, 12, "translateX"], [35, 22, 34, 22], [35, 24, 34, 24], [35, 29, 34, 29], [36, 12, 35, 12, "translateY"], [36, 22, 35, 22], [36, 24, 35, 24], [36, 29, 35, 29], [37, 12, 36, 12, "scale"], [37, 17, 36, 17], [37, 19, 36, 19], [38, 10, 37, 10], [38, 11, 37, 11], [39, 8, 39, 6], [39, 9, 39, 7], [40, 8, 40, 6], [40, 11, 40, 9], [40, 13, 40, 11], [41, 10, 41, 8, "opacity"], [41, 17, 41, 15], [41, 19, 41, 17], [41, 20, 41, 18], [42, 10, 42, 8, "transform"], [42, 19, 42, 17], [42, 21, 42, 19], [42, 22, 43, 10], [43, 12, 44, 12, "translateX"], [43, 22, 44, 22], [43, 24, 44, 24], [43, 29, 44, 29], [44, 12, 45, 12, "translateY"], [44, 22, 45, 22], [44, 24, 45, 24], [44, 29, 45, 29], [45, 12, 46, 12, "scale"], [45, 17, 46, 17], [45, 19, 46, 19], [46, 10, 47, 10], [46, 11, 47, 11], [47, 8, 49, 6], [48, 6, 50, 4], [48, 7, 50, 5], [49, 6, 51, 4, "duration"], [49, 14, 51, 12], [49, 16, 51, 14], [50, 4, 52, 2], [50, 5, 52, 3], [51, 4, 54, 2], [51, 11, 54, 9, "fadingTransition"], [51, 27, 54, 25], [52, 2, 55, 0], [53, 0, 55, 1], [53, 3]], "functionMap": {"names": ["<global>", "FadingTransition"], "mappings": "AAA;OCG;CDmD"}}, "type": "js/module"}]}