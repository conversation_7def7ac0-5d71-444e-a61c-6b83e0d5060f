{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 194}, "end": {"line": 8, "column": 50, "index": 244}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  /**\n   * Copied from: react-native/Libraries/LogBox/Data/LogBoxData.js\n   * react-native/Libraries/LogBox/Data/parseLogBoxLog.js\n   */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.addLogBoxLog = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var LogBox = _reactNative.LogBox;\n  var noop = () => {\n    // do nothing\n  };\n\n  // Do nothing when addLogBoxLog is called if LogBox is not available\n  var addLogBoxLog = exports.addLogBoxLog = LogBox?.addLog?.bind(LogBox) ?? noop;\n});", "lineCount": 20, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [5, 0, 3, 0], [6, 0, 4, 0], [7, 0, 5, 0], [8, 2, 2, 0, "Object"], [8, 8, 2, 0], [8, 9, 2, 0, "defineProperty"], [8, 23, 2, 0], [8, 24, 2, 0, "exports"], [8, 31, 2, 0], [9, 4, 2, 0, "value"], [9, 9, 2, 0], [10, 2, 2, 0], [11, 2, 2, 0, "exports"], [11, 9, 2, 0], [11, 10, 2, 0, "addLogBoxLog"], [11, 22, 2, 0], [12, 2, 8, 0], [12, 6, 8, 0, "_reactNative"], [12, 18, 8, 0], [12, 21, 8, 0, "require"], [12, 28, 8, 0], [12, 29, 8, 0, "_dependencyMap"], [12, 43, 8, 0], [13, 2, 48, 0], [13, 6, 48, 6, "LogBox"], [13, 12, 48, 12], [13, 15, 48, 15, "RNLogBox"], [13, 34, 48, 41], [14, 2, 50, 0], [14, 6, 50, 6, "noop"], [14, 10, 50, 10], [14, 13, 50, 13, "noop"], [14, 14, 50, 13], [14, 19, 50, 19], [15, 4, 51, 2], [16, 2, 51, 2], [16, 3, 52, 1], [18, 2, 54, 0], [19, 2, 55, 7], [19, 6, 55, 13, "addLogBoxLog"], [19, 18, 55, 25], [19, 21, 55, 25, "exports"], [19, 28, 55, 25], [19, 29, 55, 25, "addLogBoxLog"], [19, 41, 55, 25], [19, 44, 55, 28, "LogBox"], [19, 50, 55, 34], [19, 52, 55, 36, "addLog"], [19, 58, 55, 42], [19, 60, 55, 44, "bind"], [19, 64, 55, 48], [19, 65, 55, 49, "LogBox"], [19, 71, 55, 55], [19, 72, 55, 56], [19, 76, 55, 60, "noop"], [19, 80, 55, 64], [20, 0, 55, 65], [20, 3]], "functionMap": {"names": ["<global>", "noop"], "mappings": "AAA;aCiD;CDE"}}, "type": "js/module"}]}