{"dependencies": [{"name": "../../src/private/specs_DEPRECATED/modules/NativeBlobModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 76}}], "key": "uLWkuuBpj6rtZpGesTrLGcTLQcg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {};\n  exports.default = void 0;\n  var _NativeBlobModule = _interopRequireWildcard(require(_dependencyMap[0], \"../../src/private/specs_DEPRECATED/modules/NativeBlobModule\"));\n  Object.keys(_NativeBlobModule).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _NativeBlobModule[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _NativeBlobModule[key];\n      }\n    });\n  });\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var _default = exports.default = _NativeBlobModule.default;\n});", "lineCount": 21, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeBlobModule"], [7, 23, 11, 0], [7, 26, 11, 0, "_interopRequireWildcard"], [7, 49, 11, 0], [7, 50, 11, 0, "require"], [7, 57, 11, 0], [7, 58, 11, 0, "_dependencyMap"], [7, 72, 11, 0], [8, 2, 11, 0, "Object"], [8, 8, 11, 0], [8, 9, 11, 0, "keys"], [8, 13, 11, 0], [8, 14, 11, 0, "_NativeBlobModule"], [8, 31, 11, 0], [8, 33, 11, 0, "for<PERSON>ach"], [8, 40, 11, 0], [8, 51, 11, 0, "key"], [8, 54, 11, 0], [9, 4, 11, 0], [9, 8, 11, 0, "key"], [9, 11, 11, 0], [9, 29, 11, 0, "key"], [9, 32, 11, 0], [10, 4, 11, 0], [10, 8, 11, 0, "Object"], [10, 14, 11, 0], [10, 15, 11, 0, "prototype"], [10, 24, 11, 0], [10, 25, 11, 0, "hasOwnProperty"], [10, 39, 11, 0], [10, 40, 11, 0, "call"], [10, 44, 11, 0], [10, 45, 11, 0, "_exportNames"], [10, 57, 11, 0], [10, 59, 11, 0, "key"], [10, 62, 11, 0], [11, 4, 11, 0], [11, 8, 11, 0, "key"], [11, 11, 11, 0], [11, 15, 11, 0, "exports"], [11, 22, 11, 0], [11, 26, 11, 0, "exports"], [11, 33, 11, 0], [11, 34, 11, 0, "key"], [11, 37, 11, 0], [11, 43, 11, 0, "_NativeBlobModule"], [11, 60, 11, 0], [11, 61, 11, 0, "key"], [11, 64, 11, 0], [12, 4, 11, 0, "Object"], [12, 10, 11, 0], [12, 11, 11, 0, "defineProperty"], [12, 25, 11, 0], [12, 26, 11, 0, "exports"], [12, 33, 11, 0], [12, 35, 11, 0, "key"], [12, 38, 11, 0], [13, 6, 11, 0, "enumerable"], [13, 16, 11, 0], [14, 6, 11, 0, "get"], [14, 9, 11, 0], [14, 20, 11, 0, "get"], [14, 21, 11, 0], [15, 8, 11, 0], [15, 15, 11, 0, "_NativeBlobModule"], [15, 32, 11, 0], [15, 33, 11, 0, "key"], [15, 36, 11, 0], [16, 6, 11, 0], [17, 4, 11, 0], [18, 2, 11, 0], [19, 2, 11, 76], [19, 11, 11, 76, "_interopRequireWildcard"], [19, 35, 11, 76, "e"], [19, 36, 11, 76], [19, 38, 11, 76, "t"], [19, 39, 11, 76], [19, 68, 11, 76, "WeakMap"], [19, 75, 11, 76], [19, 81, 11, 76, "r"], [19, 82, 11, 76], [19, 89, 11, 76, "WeakMap"], [19, 96, 11, 76], [19, 100, 11, 76, "n"], [19, 101, 11, 76], [19, 108, 11, 76, "WeakMap"], [19, 115, 11, 76], [19, 127, 11, 76, "_interopRequireWildcard"], [19, 150, 11, 76], [19, 162, 11, 76, "_interopRequireWildcard"], [19, 163, 11, 76, "e"], [19, 164, 11, 76], [19, 166, 11, 76, "t"], [19, 167, 11, 76], [19, 176, 11, 76, "t"], [19, 177, 11, 76], [19, 181, 11, 76, "e"], [19, 182, 11, 76], [19, 186, 11, 76, "e"], [19, 187, 11, 76], [19, 188, 11, 76, "__esModule"], [19, 198, 11, 76], [19, 207, 11, 76, "e"], [19, 208, 11, 76], [19, 214, 11, 76, "o"], [19, 215, 11, 76], [19, 217, 11, 76, "i"], [19, 218, 11, 76], [19, 220, 11, 76, "f"], [19, 221, 11, 76], [19, 226, 11, 76, "__proto__"], [19, 235, 11, 76], [19, 243, 11, 76, "default"], [19, 250, 11, 76], [19, 252, 11, 76, "e"], [19, 253, 11, 76], [19, 270, 11, 76, "e"], [19, 271, 11, 76], [19, 294, 11, 76, "e"], [19, 295, 11, 76], [19, 320, 11, 76, "e"], [19, 321, 11, 76], [19, 330, 11, 76, "f"], [19, 331, 11, 76], [19, 337, 11, 76, "o"], [19, 338, 11, 76], [19, 341, 11, 76, "t"], [19, 342, 11, 76], [19, 345, 11, 76, "n"], [19, 346, 11, 76], [19, 349, 11, 76, "r"], [19, 350, 11, 76], [19, 358, 11, 76, "o"], [19, 359, 11, 76], [19, 360, 11, 76, "has"], [19, 363, 11, 76], [19, 364, 11, 76, "e"], [19, 365, 11, 76], [19, 375, 11, 76, "o"], [19, 376, 11, 76], [19, 377, 11, 76, "get"], [19, 380, 11, 76], [19, 381, 11, 76, "e"], [19, 382, 11, 76], [19, 385, 11, 76, "o"], [19, 386, 11, 76], [19, 387, 11, 76, "set"], [19, 390, 11, 76], [19, 391, 11, 76, "e"], [19, 392, 11, 76], [19, 394, 11, 76, "f"], [19, 395, 11, 76], [19, 409, 11, 76, "_t"], [19, 411, 11, 76], [19, 415, 11, 76, "e"], [19, 416, 11, 76], [19, 432, 11, 76, "_t"], [19, 434, 11, 76], [19, 441, 11, 76, "hasOwnProperty"], [19, 455, 11, 76], [19, 456, 11, 76, "call"], [19, 460, 11, 76], [19, 461, 11, 76, "e"], [19, 462, 11, 76], [19, 464, 11, 76, "_t"], [19, 466, 11, 76], [19, 473, 11, 76, "i"], [19, 474, 11, 76], [19, 478, 11, 76, "o"], [19, 479, 11, 76], [19, 482, 11, 76, "Object"], [19, 488, 11, 76], [19, 489, 11, 76, "defineProperty"], [19, 503, 11, 76], [19, 508, 11, 76, "Object"], [19, 514, 11, 76], [19, 515, 11, 76, "getOwnPropertyDescriptor"], [19, 539, 11, 76], [19, 540, 11, 76, "e"], [19, 541, 11, 76], [19, 543, 11, 76, "_t"], [19, 545, 11, 76], [19, 552, 11, 76, "i"], [19, 553, 11, 76], [19, 554, 11, 76, "get"], [19, 557, 11, 76], [19, 561, 11, 76, "i"], [19, 562, 11, 76], [19, 563, 11, 76, "set"], [19, 566, 11, 76], [19, 570, 11, 76, "o"], [19, 571, 11, 76], [19, 572, 11, 76, "f"], [19, 573, 11, 76], [19, 575, 11, 76, "_t"], [19, 577, 11, 76], [19, 579, 11, 76, "i"], [19, 580, 11, 76], [19, 584, 11, 76, "f"], [19, 585, 11, 76], [19, 586, 11, 76, "_t"], [19, 588, 11, 76], [19, 592, 11, 76, "e"], [19, 593, 11, 76], [19, 594, 11, 76, "_t"], [19, 596, 11, 76], [19, 607, 11, 76, "f"], [19, 608, 11, 76], [19, 613, 11, 76, "e"], [19, 614, 11, 76], [19, 616, 11, 76, "t"], [19, 617, 11, 76], [20, 2, 11, 76], [20, 6, 11, 76, "_default"], [20, 14, 11, 76], [20, 17, 11, 76, "exports"], [20, 24, 11, 76], [20, 25, 11, 76, "default"], [20, 32, 11, 76], [20, 35, 13, 15, "NativeBlobModule"], [20, 60, 13, 31], [21, 0, 13, 31], [21, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}