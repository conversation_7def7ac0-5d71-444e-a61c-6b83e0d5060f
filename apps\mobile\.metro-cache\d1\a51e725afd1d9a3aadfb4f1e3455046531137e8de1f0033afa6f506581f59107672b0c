{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "../animation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 49, "index": 64}}], "key": "a6n75g9KQy+KnMEjz15YzADQ7Hw=", "exportNames": ["*"]}}, {"name": "../isSharedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 179}, "end": {"line": 6, "column": 49, "index": 228}}], "key": "+wsZ4vw3n7k5+siJvCyq73LAhFk=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 229}, "end": {"line": 7, "column": 54, "index": 283}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "../WorkletEventHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 284}, "end": {"line": 8, "column": 61, "index": 345}}], "key": "588C2ttWmFfH+Cx2zV7Dtb/CLj8=", "exportNames": ["*"]}}, {"name": "./InlinePropManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 498}, "end": {"line": 16, "column": 70, "index": 568}}], "key": "VvCec8CoZixmXg+r1z4csYmUpDk=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 569}, "end": {"line": 17, "column": 44, "index": 613}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PropsFilter = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _animation = require(_dependencyMap[3], \"../animation\");\n  var _isSharedValue = require(_dependencyMap[4], \"../isSharedValue\");\n  var _PlatformChecker = require(_dependencyMap[5], \"../PlatformChecker\");\n  var _WorkletEventHandler = require(_dependencyMap[6], \"../WorkletEventHandler\");\n  var _InlinePropManager = require(_dependencyMap[7], \"./InlinePropManager\");\n  var _utils = require(_dependencyMap[8], \"./utils\");\n  function dummyListener() {\n    // empty listener we use to assign to listener properties for which animated\n    // event is used.\n  }\n  var PropsFilter = exports.PropsFilter = /*#__PURE__*/function () {\n    function PropsFilter() {\n      (0, _classCallCheck2.default)(this, PropsFilter);\n      this._initialPropsMap = new Map();\n    }\n    return (0, _createClass2.default)(PropsFilter, [{\n      key: \"filterNonAnimatedProps\",\n      value: function filterNonAnimatedProps(component) {\n        var _this = this;\n        var inputProps = component.props;\n        var props = {};\n        var _loop = function () {\n          var value = inputProps[key];\n          if (key === 'style') {\n            var styleProp = inputProps.style;\n            var styles = (0, _utils.flattenArray)(styleProp ?? []);\n            var processedStyle = styles.map(style => {\n              if (style && style.viewDescriptors) {\n                var handle = style;\n                if (component._isFirstRender) {\n                  _this._initialPropsMap.set(handle, {\n                    ...handle.initial.value,\n                    ...(0, _animation.initialUpdaterRun)(handle.initial.updater)\n                  });\n                }\n                return _this._initialPropsMap.get(handle) ?? {};\n              } else if ((0, _InlinePropManager.hasInlineStyles)(style)) {\n                return (0, _InlinePropManager.getInlineStyle)(style, component._isFirstRender);\n              } else {\n                return style;\n              }\n            });\n            // keep styles as they were passed by the user\n            // it will help other libs to interpret styles correctly\n            props[key] = processedStyle;\n          } else if (key === 'animatedProps') {\n            var animatedProp = inputProps.animatedProps;\n            if (animatedProp.initial !== undefined) {\n              Object.keys(animatedProp.initial.value).forEach(initialValueKey => {\n                props[initialValueKey] = animatedProp.initial?.value[initialValueKey];\n              });\n            }\n          } else if ((0, _utils.has)('workletEventHandler', value) && value.workletEventHandler instanceof _WorkletEventHandler.WorkletEventHandler) {\n            if (value.workletEventHandler.eventNames.length > 0) {\n              value.workletEventHandler.eventNames.forEach(eventName => {\n                props[eventName] = (0, _utils.has)('listeners', value.workletEventHandler) ? value.workletEventHandler.listeners[eventName] : dummyListener;\n              });\n            } else {\n              props[key] = dummyListener;\n            }\n          } else if ((0, _isSharedValue.isSharedValue)(value)) {\n            if (component._isFirstRender) {\n              props[key] = value.value;\n            }\n          } else if (key !== 'onGestureHandlerStateChange' || !(0, _PlatformChecker.isChromeDebugger)()) {\n            props[key] = value;\n          }\n        };\n        for (var key in inputProps) {\n          _loop();\n        }\n        return props;\n      }\n    }]);\n  }();\n});", "lineCount": 86, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 21, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_classCallCheck2"], [9, 22, 1, 13], [9, 25, 1, 13, "_interopRequireDefault"], [9, 47, 1, 13], [9, 48, 1, 13, "require"], [9, 55, 1, 13], [9, 56, 1, 13, "_dependencyMap"], [9, 70, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_createClass2"], [10, 19, 1, 13], [10, 22, 1, 13, "_interopRequireDefault"], [10, 44, 1, 13], [10, 45, 1, 13, "require"], [10, 52, 1, 13], [10, 53, 1, 13, "_dependencyMap"], [10, 67, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "_animation"], [11, 16, 3, 0], [11, 19, 3, 0, "require"], [11, 26, 3, 0], [11, 27, 3, 0, "_dependencyMap"], [11, 41, 3, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_isSharedValue"], [12, 20, 6, 0], [12, 23, 6, 0, "require"], [12, 30, 6, 0], [12, 31, 6, 0, "_dependencyMap"], [12, 45, 6, 0], [13, 2, 7, 0], [13, 6, 7, 0, "_PlatformChecker"], [13, 22, 7, 0], [13, 25, 7, 0, "require"], [13, 32, 7, 0], [13, 33, 7, 0, "_dependencyMap"], [13, 47, 7, 0], [14, 2, 8, 0], [14, 6, 8, 0, "_WorkletEventHandler"], [14, 26, 8, 0], [14, 29, 8, 0, "require"], [14, 36, 8, 0], [14, 37, 8, 0, "_dependencyMap"], [14, 51, 8, 0], [15, 2, 16, 0], [15, 6, 16, 0, "_InlinePropManager"], [15, 24, 16, 0], [15, 27, 16, 0, "require"], [15, 34, 16, 0], [15, 35, 16, 0, "_dependencyMap"], [15, 49, 16, 0], [16, 2, 17, 0], [16, 6, 17, 0, "_utils"], [16, 12, 17, 0], [16, 15, 17, 0, "require"], [16, 22, 17, 0], [16, 23, 17, 0, "_dependencyMap"], [16, 37, 17, 0], [17, 2, 19, 0], [17, 11, 19, 9, "dummyListener"], [17, 24, 19, 22, "dummyListener"], [17, 25, 19, 22], [17, 27, 19, 25], [18, 4, 20, 2], [19, 4, 21, 2], [20, 2, 21, 2], [21, 2, 22, 1], [21, 6, 24, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [21, 17, 24, 24], [21, 20, 24, 24, "exports"], [21, 27, 24, 24], [21, 28, 24, 24, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [21, 39, 24, 24], [22, 4, 24, 24], [22, 13, 24, 24, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [22, 25, 24, 24], [23, 6, 24, 24], [23, 10, 24, 24, "_classCallCheck2"], [23, 26, 24, 24], [23, 27, 24, 24, "default"], [23, 34, 24, 24], [23, 42, 24, 24, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 53, 24, 24], [24, 6, 24, 24], [24, 11, 25, 10, "_initialPropsMap"], [24, 27, 25, 26], [24, 30, 25, 29], [24, 34, 25, 33, "Map"], [24, 37, 25, 36], [24, 38, 25, 70], [24, 39, 25, 71], [25, 4, 25, 71], [26, 4, 25, 71], [26, 15, 25, 71, "_createClass2"], [26, 28, 25, 71], [26, 29, 25, 71, "default"], [26, 36, 25, 71], [26, 38, 25, 71, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [26, 49, 25, 71], [27, 6, 25, 71, "key"], [27, 9, 25, 71], [28, 6, 25, 71, "value"], [28, 11, 25, 71], [28, 13, 27, 2], [28, 22, 27, 9, "filterNonAnimatedProps"], [28, 44, 27, 31, "filterNonAnimatedProps"], [28, 45, 28, 4, "component"], [28, 54, 28, 77], [28, 56, 29, 29], [29, 8, 29, 29], [29, 12, 29, 29, "_this"], [29, 17, 29, 29], [30, 8, 30, 4], [30, 12, 30, 10, "inputProps"], [30, 22, 30, 20], [30, 25, 31, 6, "component"], [30, 34, 31, 15], [30, 35, 31, 16, "props"], [30, 40, 31, 70], [31, 8, 32, 4], [31, 12, 32, 10, "props"], [31, 17, 32, 40], [31, 20, 32, 43], [31, 21, 32, 44], [31, 22, 32, 45], [32, 8, 32, 46], [32, 12, 32, 46, "_loop"], [32, 17, 32, 46], [32, 29, 32, 46, "_loop"], [32, 30, 32, 46], [32, 32, 34, 34], [33, 10, 35, 6], [33, 14, 35, 12, "value"], [33, 19, 35, 17], [33, 22, 35, 20, "inputProps"], [33, 32, 35, 30], [33, 33, 35, 31, "key"], [33, 36, 35, 34], [33, 37, 35, 35], [34, 10, 36, 6], [34, 14, 36, 10, "key"], [34, 17, 36, 13], [34, 22, 36, 18], [34, 29, 36, 25], [34, 31, 36, 27], [35, 12, 37, 8], [35, 16, 37, 14, "styleProp"], [35, 25, 37, 23], [35, 28, 37, 26, "inputProps"], [35, 38, 37, 36], [35, 39, 37, 37, "style"], [35, 44, 37, 42], [36, 12, 38, 8], [36, 16, 38, 14, "styles"], [36, 22, 38, 20], [36, 25, 38, 23], [36, 29, 38, 23, "flattenArray"], [36, 48, 38, 35], [36, 50, 38, 48, "styleProp"], [36, 59, 38, 57], [36, 63, 38, 61], [36, 65, 38, 63], [36, 66, 38, 64], [37, 12, 40, 8], [37, 16, 40, 14, "processedStyle"], [37, 30, 40, 42], [37, 33, 40, 45, "styles"], [37, 39, 40, 51], [37, 40, 40, 52, "map"], [37, 43, 40, 55], [37, 44, 40, 57, "style"], [37, 49, 40, 62], [37, 53, 40, 67], [38, 14, 41, 10], [38, 18, 41, 14, "style"], [38, 23, 41, 19], [38, 27, 41, 23, "style"], [38, 32, 41, 28], [38, 33, 41, 29, "viewDescriptors"], [38, 48, 41, 44], [38, 50, 41, 46], [39, 16, 42, 12], [39, 20, 42, 18, "handle"], [39, 26, 42, 24], [39, 29, 42, 27, "style"], [39, 34, 42, 55], [40, 16, 44, 12], [40, 20, 44, 16, "component"], [40, 29, 44, 25], [40, 30, 44, 26, "_isFirstRender"], [40, 44, 44, 40], [40, 46, 44, 42], [41, 18, 45, 14, "_this"], [41, 23, 45, 18], [41, 24, 45, 19, "_initialPropsMap"], [41, 40, 45, 35], [41, 41, 45, 36, "set"], [41, 44, 45, 39], [41, 45, 45, 40, "handle"], [41, 51, 45, 46], [41, 53, 45, 48], [42, 20, 46, 16], [42, 23, 46, 19, "handle"], [42, 29, 46, 25], [42, 30, 46, 26, "initial"], [42, 37, 46, 33], [42, 38, 46, 34, "value"], [42, 43, 46, 39], [43, 20, 47, 16], [43, 23, 47, 19], [43, 27, 47, 19, "initialUpdaterRun"], [43, 55, 47, 36], [43, 57, 47, 37, "handle"], [43, 63, 47, 43], [43, 64, 47, 44, "initial"], [43, 71, 47, 51], [43, 72, 47, 52, "updater"], [43, 79, 47, 59], [44, 18, 48, 14], [44, 19, 48, 29], [44, 20, 48, 30], [45, 16, 49, 12], [46, 16, 51, 12], [46, 23, 51, 19, "_this"], [46, 28, 51, 23], [46, 29, 51, 24, "_initialPropsMap"], [46, 45, 51, 40], [46, 46, 51, 41, "get"], [46, 49, 51, 44], [46, 50, 51, 45, "handle"], [46, 56, 51, 51], [46, 57, 51, 52], [46, 61, 51, 56], [46, 62, 51, 57], [46, 63, 51, 58], [47, 14, 52, 10], [47, 15, 52, 11], [47, 21, 52, 17], [47, 25, 52, 21], [47, 29, 52, 21, "hasInlineStyles"], [47, 63, 52, 36], [47, 65, 52, 37, "style"], [47, 70, 52, 42], [47, 71, 52, 43], [47, 73, 52, 45], [48, 16, 53, 12], [48, 23, 53, 19], [48, 27, 53, 19, "getInlineStyle"], [48, 60, 53, 33], [48, 62, 53, 34, "style"], [48, 67, 53, 39], [48, 69, 53, 41, "component"], [48, 78, 53, 50], [48, 79, 53, 51, "_isFirstRender"], [48, 93, 53, 65], [48, 94, 53, 66], [49, 14, 54, 10], [49, 15, 54, 11], [49, 21, 54, 17], [50, 16, 55, 12], [50, 23, 55, 19, "style"], [50, 28, 55, 24], [51, 14, 56, 10], [52, 12, 57, 8], [52, 13, 57, 9], [52, 14, 57, 10], [53, 12, 58, 8], [54, 12, 59, 8], [55, 12, 60, 8, "props"], [55, 17, 60, 13], [55, 18, 60, 14, "key"], [55, 21, 60, 17], [55, 22, 60, 18], [55, 25, 60, 21, "processedStyle"], [55, 39, 60, 35], [56, 10, 61, 6], [56, 11, 61, 7], [56, 17, 61, 13], [56, 21, 61, 17, "key"], [56, 24, 61, 20], [56, 29, 61, 25], [56, 44, 61, 40], [56, 46, 61, 42], [57, 12, 62, 8], [57, 16, 62, 14, "animatedProp"], [57, 28, 62, 26], [57, 31, 62, 29, "inputProps"], [57, 41, 62, 39], [57, 42, 62, 40, "animatedProps"], [57, 55, 64, 9], [58, 12, 65, 8], [58, 16, 65, 12, "animatedProp"], [58, 28, 65, 24], [58, 29, 65, 25, "initial"], [58, 36, 65, 32], [58, 41, 65, 37, "undefined"], [58, 50, 65, 46], [58, 52, 65, 48], [59, 14, 66, 10, "Object"], [59, 20, 66, 16], [59, 21, 66, 17, "keys"], [59, 25, 66, 21], [59, 26, 66, 22, "animatedProp"], [59, 38, 66, 34], [59, 39, 66, 35, "initial"], [59, 46, 66, 42], [59, 47, 66, 43, "value"], [59, 52, 66, 48], [59, 53, 66, 49], [59, 54, 66, 50, "for<PERSON>ach"], [59, 61, 66, 57], [59, 62, 66, 59, "initialValueKey"], [59, 77, 66, 74], [59, 81, 66, 79], [60, 16, 67, 12, "props"], [60, 21, 67, 17], [60, 22, 67, 18, "initialValueKey"], [60, 37, 67, 33], [60, 38, 67, 34], [60, 41, 68, 14, "animatedProp"], [60, 53, 68, 26], [60, 54, 68, 27, "initial"], [60, 61, 68, 34], [60, 63, 68, 36, "value"], [60, 68, 68, 41], [60, 69, 68, 42, "initialValueKey"], [60, 84, 68, 57], [60, 85, 68, 58], [61, 14, 69, 10], [61, 15, 69, 11], [61, 16, 69, 12], [62, 12, 70, 8], [63, 10, 71, 6], [63, 11, 71, 7], [63, 17, 71, 13], [63, 21, 72, 8], [63, 25, 72, 8, "has"], [63, 35, 72, 11], [63, 37, 72, 12], [63, 58, 72, 33], [63, 60, 72, 35, "value"], [63, 65, 72, 40], [63, 66, 72, 41], [63, 70, 73, 8, "value"], [63, 75, 73, 13], [63, 76, 73, 14, "workletEventHandler"], [63, 95, 73, 33], [63, 107, 73, 45, "WorkletEventHandler"], [63, 147, 73, 64], [63, 149, 74, 8], [64, 12, 75, 8], [64, 16, 75, 12, "value"], [64, 21, 75, 17], [64, 22, 75, 18, "workletEventHandler"], [64, 41, 75, 37], [64, 42, 75, 38, "eventNames"], [64, 52, 75, 48], [64, 53, 75, 49, "length"], [64, 59, 75, 55], [64, 62, 75, 58], [64, 63, 75, 59], [64, 65, 75, 61], [65, 14, 76, 10, "value"], [65, 19, 76, 15], [65, 20, 76, 16, "workletEventHandler"], [65, 39, 76, 35], [65, 40, 76, 36, "eventNames"], [65, 50, 76, 46], [65, 51, 76, 47, "for<PERSON>ach"], [65, 58, 76, 54], [65, 59, 76, 56, "eventName"], [65, 68, 76, 65], [65, 72, 76, 70], [66, 16, 77, 12, "props"], [66, 21, 77, 17], [66, 22, 77, 18, "eventName"], [66, 31, 77, 27], [66, 32, 77, 28], [66, 35, 77, 31], [66, 39, 77, 31, "has"], [66, 49, 77, 34], [66, 51, 77, 35], [66, 62, 77, 46], [66, 64, 77, 48, "value"], [66, 69, 77, 53], [66, 70, 77, 54, "workletEventHandler"], [66, 89, 77, 73], [66, 90, 77, 74], [66, 93, 79, 18, "value"], [66, 98, 79, 23], [66, 99, 79, 24, "workletEventHandler"], [66, 118, 79, 43], [66, 119, 79, 44, "listeners"], [66, 128, 79, 53], [66, 129, 80, 18, "eventName"], [66, 138, 80, 27], [66, 139, 80, 28], [66, 142, 81, 16, "dummyListener"], [66, 155, 81, 29], [67, 14, 82, 10], [67, 15, 82, 11], [67, 16, 82, 12], [68, 12, 83, 8], [68, 13, 83, 9], [68, 19, 83, 15], [69, 14, 84, 10, "props"], [69, 19, 84, 15], [69, 20, 84, 16, "key"], [69, 23, 84, 19], [69, 24, 84, 20], [69, 27, 84, 23, "dummyListener"], [69, 40, 84, 36], [70, 12, 85, 8], [71, 10, 86, 6], [71, 11, 86, 7], [71, 17, 86, 13], [71, 21, 86, 17], [71, 25, 86, 17, "isSharedValue"], [71, 53, 86, 30], [71, 55, 86, 31, "value"], [71, 60, 86, 36], [71, 61, 86, 37], [71, 63, 86, 39], [72, 12, 87, 8], [72, 16, 87, 12, "component"], [72, 25, 87, 21], [72, 26, 87, 22, "_isFirstRender"], [72, 40, 87, 36], [72, 42, 87, 38], [73, 14, 88, 10, "props"], [73, 19, 88, 15], [73, 20, 88, 16, "key"], [73, 23, 88, 19], [73, 24, 88, 20], [73, 27, 88, 23, "value"], [73, 32, 88, 28], [73, 33, 88, 29, "value"], [73, 38, 88, 34], [74, 12, 89, 8], [75, 10, 90, 6], [75, 11, 90, 7], [75, 17, 90, 13], [75, 21, 90, 17, "key"], [75, 24, 90, 20], [75, 29, 90, 25], [75, 58, 90, 54], [75, 62, 90, 58], [75, 63, 90, 59], [75, 67, 90, 59, "isChromeDebugger"], [75, 100, 90, 75], [75, 102, 90, 76], [75, 103, 90, 77], [75, 105, 90, 79], [76, 12, 91, 8, "props"], [76, 17, 91, 13], [76, 18, 91, 14, "key"], [76, 21, 91, 17], [76, 22, 91, 18], [76, 25, 91, 21, "value"], [76, 30, 91, 26], [77, 10, 92, 6], [78, 8, 93, 4], [78, 9, 93, 5], [79, 8, 34, 4], [79, 13, 34, 9], [79, 17, 34, 15, "key"], [79, 20, 34, 18], [79, 24, 34, 22, "inputProps"], [79, 34, 34, 32], [80, 10, 34, 32, "_loop"], [80, 15, 34, 32], [81, 8, 34, 32], [82, 8, 94, 4], [82, 15, 94, 11, "props"], [82, 20, 94, 16], [83, 6, 95, 2], [84, 4, 95, 3], [85, 2, 95, 3], [86, 0, 95, 3], [86, 3]], "functionMap": {"names": ["<global>", "dummyListener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filterNonAnimatedProps", "styles.map$argument_0", "Object.keys.forEach$argument_0", "value.workletEventHandler.eventNames.forEach$argument_0"], "mappings": "AAA;ACkB;CDG;OEE;ECG;wDCa;SDiB;0DES;WFG;uDGO;WHM;GDa;CFC"}}, "type": "js/module"}]}