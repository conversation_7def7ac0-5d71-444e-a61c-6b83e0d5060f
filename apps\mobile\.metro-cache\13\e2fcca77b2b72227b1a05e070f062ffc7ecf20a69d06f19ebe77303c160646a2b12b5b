{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 64, "index": 111}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 162}, "end": {"line": 6, "column": 66, "index": 228}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "use-latest-callback", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 229}, "end": {"line": 7, "column": 52, "index": 281}}], "key": "2ER/r3Agt+5SFwaFR8HXg24Rpu4=", "exportNames": ["*"]}}, {"name": "use-sync-external-store/with-selector", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 282}, "end": {"line": 8, "column": 89, "index": 371}}], "key": "eWOvQ07XtQMBjXiY0qREKFi+uR8=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 372}, "end": {"line": 9, "column": 63, "index": 435}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.FrameSizeProvider = FrameSizeProvider;\n  exports.useFrameSize = useFrameSize;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[3], \"react-native-safe-area-context\");\n  var _useLatestCallback = _interopRequireDefault(require(_dependencyMap[4], \"use-latest-callback\"));\n  var _withSelector = require(_dependencyMap[5], \"use-sync-external-store/with-selector\");\n  var _jsxRuntime = require(_dependencyMap[6], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  // eslint-disable-next-line no-restricted-imports\n\n  var FrameContext = /*#__PURE__*/React.createContext(undefined);\n  function useFrameSize(selector, debounce) {\n    var context = React.useContext(FrameContext);\n    if (context == null) {\n      throw new Error('useFrameSize must be used within a FrameSizeProvider');\n    }\n    var value = (0, _withSelector.useSyncExternalStoreWithSelector)(debounce ? context.subscribeDebounced : context.subscribe, context.getCurrent, context.getCurrent, selector);\n    return value;\n  }\n  function FrameSizeProvider(_ref) {\n    var children = _ref.children;\n    var context = React.useContext(FrameContext);\n    if (context != null) {\n      // If the context is already present, don't wrap again\n      return children;\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(FrameSizeProviderInner, {\n      children: children\n    });\n  }\n  function FrameSizeProviderInner(_ref2) {\n    var children = _ref2.children;\n    var listeners = React.useRef(new Set());\n    var _useResizeListener = useResizeListener(size => {\n        listeners.current.forEach(listener => listener(size));\n      }),\n      element = _useResizeListener.element,\n      get = _useResizeListener.get;\n    var getCurrent = (0, _useLatestCallback.default)(get);\n    var subscribe = (0, _useLatestCallback.default)(listener => {\n      listeners.current.add(listener);\n      return () => {\n        listeners.current.delete(listener);\n      };\n    });\n    var subscribeDebounced = (0, _useLatestCallback.default)(listener => {\n      var timer;\n      var debouncedListener = size => {\n        clearTimeout(timer);\n        timer = setTimeout(() => {\n          listener(size);\n        }, 100);\n      };\n      listeners.current.add(debouncedListener);\n      return () => {\n        clearTimeout(timer);\n        listeners.current.delete(debouncedListener);\n      };\n    });\n    var context = React.useMemo(() => ({\n      getCurrent,\n      subscribe,\n      subscribeDebounced\n    }), [subscribe, subscribeDebounced, getCurrent]);\n    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(FrameContext.Provider, {\n      value: context,\n      children: [element, children]\n    });\n  }\n  var useResizeListener = _reactNative.Platform.OS === 'web' ? useResizeListenerWeb : useResizeListenerNative;\n  function useResizeListenerNative(onChange) {\n    var frame = (0, _reactNativeSafeAreaContext.useSafeAreaFrame)();\n    React.useLayoutEffect(() => {\n      onChange(frame);\n    }, [frame, onChange]);\n    return {\n      element: null,\n      get: () => frame\n    };\n  }\n  var _Dimensions$get = _reactNative.Dimensions.get('window'),\n    _Dimensions$get$width = _Dimensions$get.width,\n    width = _Dimensions$get$width === void 0 ? 0 : _Dimensions$get$width,\n    _Dimensions$get$heigh = _Dimensions$get.height,\n    height = _Dimensions$get$heigh === void 0 ? 0 : _Dimensions$get$heigh;\n\n  // FIXME: On the Web, the safe area frame value doesn't update on resize\n  // So we workaround this by measuring the frame on resize\n  function useResizeListenerWeb(onChange) {\n    var frameRef = React.useRef({\n      width,\n      height\n    });\n    var elementRef = React.useRef(null);\n    React.useEffect(() => {\n      if (elementRef.current == null) {\n        return;\n      }\n      var update = size => {\n        if (frameRef.current.width === size.width && frameRef.current.height === size.height) {\n          return;\n        }\n        frameRef.current = size;\n        onChange(size);\n      };\n      var rect = elementRef.current.getBoundingClientRect();\n      update({\n        width: rect.width,\n        height: rect.height\n      });\n      var observer = new ResizeObserver(entries => {\n        var entry = entries[0];\n        if (entry) {\n          var _entry$contentRect = entry.contentRect,\n            _width = _entry$contentRect.width,\n            _height = _entry$contentRect.height;\n          update({\n            width: _width,\n            height: _height\n          });\n        }\n      });\n      observer.observe(elementRef.current);\n      return () => {\n        observer.disconnect();\n      };\n    }, [onChange]);\n    var element = /*#__PURE__*/(0, _jsxRuntime.jsx)(\"div\", {\n      ref: elementRef,\n      style: {\n        ..._reactNative.StyleSheet.absoluteFillObject,\n        pointerEvents: 'none',\n        visibility: 'hidden'\n      }\n    });\n    return {\n      element,\n      get: () => frameRef.current\n    };\n  }\n});", "lineCount": 149, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "FrameSizeProvider"], [8, 27, 1, 13], [8, 30, 1, 13, "FrameSizeProvider"], [8, 47, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "useFrameSize"], [9, 22, 1, 13], [9, 25, 1, 13, "useFrameSize"], [9, 37, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "React"], [10, 11, 3, 0], [10, 14, 3, 0, "_interopRequireWildcard"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_reactNative"], [11, 18, 4, 0], [11, 21, 4, 0, "require"], [11, 28, 4, 0], [11, 29, 4, 0, "_dependencyMap"], [11, 43, 4, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_reactNativeSafeAreaContext"], [12, 33, 6, 0], [12, 36, 6, 0, "require"], [12, 43, 6, 0], [12, 44, 6, 0, "_dependencyMap"], [12, 58, 6, 0], [13, 2, 7, 0], [13, 6, 7, 0, "_useLatestCallback"], [13, 24, 7, 0], [13, 27, 7, 0, "_interopRequireDefault"], [13, 49, 7, 0], [13, 50, 7, 0, "require"], [13, 57, 7, 0], [13, 58, 7, 0, "_dependencyMap"], [13, 72, 7, 0], [14, 2, 8, 0], [14, 6, 8, 0, "_withSelector"], [14, 19, 8, 0], [14, 22, 8, 0, "require"], [14, 29, 8, 0], [14, 30, 8, 0, "_dependencyMap"], [14, 44, 8, 0], [15, 2, 9, 0], [15, 6, 9, 0, "_jsxRuntime"], [15, 17, 9, 0], [15, 20, 9, 0, "require"], [15, 27, 9, 0], [15, 28, 9, 0, "_dependencyMap"], [15, 42, 9, 0], [16, 2, 9, 63], [16, 11, 9, 63, "_interopRequireWildcard"], [16, 35, 9, 63, "e"], [16, 36, 9, 63], [16, 38, 9, 63, "t"], [16, 39, 9, 63], [16, 68, 9, 63, "WeakMap"], [16, 75, 9, 63], [16, 81, 9, 63, "r"], [16, 82, 9, 63], [16, 89, 9, 63, "WeakMap"], [16, 96, 9, 63], [16, 100, 9, 63, "n"], [16, 101, 9, 63], [16, 108, 9, 63, "WeakMap"], [16, 115, 9, 63], [16, 127, 9, 63, "_interopRequireWildcard"], [16, 150, 9, 63], [16, 162, 9, 63, "_interopRequireWildcard"], [16, 163, 9, 63, "e"], [16, 164, 9, 63], [16, 166, 9, 63, "t"], [16, 167, 9, 63], [16, 176, 9, 63, "t"], [16, 177, 9, 63], [16, 181, 9, 63, "e"], [16, 182, 9, 63], [16, 186, 9, 63, "e"], [16, 187, 9, 63], [16, 188, 9, 63, "__esModule"], [16, 198, 9, 63], [16, 207, 9, 63, "e"], [16, 208, 9, 63], [16, 214, 9, 63, "o"], [16, 215, 9, 63], [16, 217, 9, 63, "i"], [16, 218, 9, 63], [16, 220, 9, 63, "f"], [16, 221, 9, 63], [16, 226, 9, 63, "__proto__"], [16, 235, 9, 63], [16, 243, 9, 63, "default"], [16, 250, 9, 63], [16, 252, 9, 63, "e"], [16, 253, 9, 63], [16, 270, 9, 63, "e"], [16, 271, 9, 63], [16, 294, 9, 63, "e"], [16, 295, 9, 63], [16, 320, 9, 63, "e"], [16, 321, 9, 63], [16, 330, 9, 63, "f"], [16, 331, 9, 63], [16, 337, 9, 63, "o"], [16, 338, 9, 63], [16, 341, 9, 63, "t"], [16, 342, 9, 63], [16, 345, 9, 63, "n"], [16, 346, 9, 63], [16, 349, 9, 63, "r"], [16, 350, 9, 63], [16, 358, 9, 63, "o"], [16, 359, 9, 63], [16, 360, 9, 63, "has"], [16, 363, 9, 63], [16, 364, 9, 63, "e"], [16, 365, 9, 63], [16, 375, 9, 63, "o"], [16, 376, 9, 63], [16, 377, 9, 63, "get"], [16, 380, 9, 63], [16, 381, 9, 63, "e"], [16, 382, 9, 63], [16, 385, 9, 63, "o"], [16, 386, 9, 63], [16, 387, 9, 63, "set"], [16, 390, 9, 63], [16, 391, 9, 63, "e"], [16, 392, 9, 63], [16, 394, 9, 63, "f"], [16, 395, 9, 63], [16, 409, 9, 63, "_t"], [16, 411, 9, 63], [16, 415, 9, 63, "e"], [16, 416, 9, 63], [16, 432, 9, 63, "_t"], [16, 434, 9, 63], [16, 441, 9, 63, "hasOwnProperty"], [16, 455, 9, 63], [16, 456, 9, 63, "call"], [16, 460, 9, 63], [16, 461, 9, 63, "e"], [16, 462, 9, 63], [16, 464, 9, 63, "_t"], [16, 466, 9, 63], [16, 473, 9, 63, "i"], [16, 474, 9, 63], [16, 478, 9, 63, "o"], [16, 479, 9, 63], [16, 482, 9, 63, "Object"], [16, 488, 9, 63], [16, 489, 9, 63, "defineProperty"], [16, 503, 9, 63], [16, 508, 9, 63, "Object"], [16, 514, 9, 63], [16, 515, 9, 63, "getOwnPropertyDescriptor"], [16, 539, 9, 63], [16, 540, 9, 63, "e"], [16, 541, 9, 63], [16, 543, 9, 63, "_t"], [16, 545, 9, 63], [16, 552, 9, 63, "i"], [16, 553, 9, 63], [16, 554, 9, 63, "get"], [16, 557, 9, 63], [16, 561, 9, 63, "i"], [16, 562, 9, 63], [16, 563, 9, 63, "set"], [16, 566, 9, 63], [16, 570, 9, 63, "o"], [16, 571, 9, 63], [16, 572, 9, 63, "f"], [16, 573, 9, 63], [16, 575, 9, 63, "_t"], [16, 577, 9, 63], [16, 579, 9, 63, "i"], [16, 580, 9, 63], [16, 584, 9, 63, "f"], [16, 585, 9, 63], [16, 586, 9, 63, "_t"], [16, 588, 9, 63], [16, 592, 9, 63, "e"], [16, 593, 9, 63], [16, 594, 9, 63, "_t"], [16, 596, 9, 63], [16, 607, 9, 63, "f"], [16, 608, 9, 63], [16, 613, 9, 63, "e"], [16, 614, 9, 63], [16, 616, 9, 63, "t"], [16, 617, 9, 63], [17, 2, 5, 0], [19, 2, 10, 0], [19, 6, 10, 6, "FrameContext"], [19, 18, 10, 18], [19, 21, 10, 21], [19, 34, 10, 34, "React"], [19, 39, 10, 39], [19, 40, 10, 40, "createContext"], [19, 53, 10, 53], [19, 54, 10, 54, "undefined"], [19, 63, 10, 63], [19, 64, 10, 64], [20, 2, 11, 7], [20, 11, 11, 16, "useFrameSize"], [20, 23, 11, 28, "useFrameSize"], [20, 24, 11, 29, "selector"], [20, 32, 11, 37], [20, 34, 11, 39, "debounce"], [20, 42, 11, 47], [20, 44, 11, 49], [21, 4, 12, 2], [21, 8, 12, 8, "context"], [21, 15, 12, 15], [21, 18, 12, 18, "React"], [21, 23, 12, 23], [21, 24, 12, 24, "useContext"], [21, 34, 12, 34], [21, 35, 12, 35, "FrameContext"], [21, 47, 12, 47], [21, 48, 12, 48], [22, 4, 13, 2], [22, 8, 13, 6, "context"], [22, 15, 13, 13], [22, 19, 13, 17], [22, 23, 13, 21], [22, 25, 13, 23], [23, 6, 14, 4], [23, 12, 14, 10], [23, 16, 14, 14, "Error"], [23, 21, 14, 19], [23, 22, 14, 20], [23, 76, 14, 74], [23, 77, 14, 75], [24, 4, 15, 2], [25, 4, 16, 2], [25, 8, 16, 8, "value"], [25, 13, 16, 13], [25, 16, 16, 16], [25, 20, 16, 16, "useSyncExternalStoreWithSelector"], [25, 66, 16, 48], [25, 68, 16, 49, "debounce"], [25, 76, 16, 57], [25, 79, 16, 60, "context"], [25, 86, 16, 67], [25, 87, 16, 68, "subscribeDebounced"], [25, 105, 16, 86], [25, 108, 16, 89, "context"], [25, 115, 16, 96], [25, 116, 16, 97, "subscribe"], [25, 125, 16, 106], [25, 127, 16, 108, "context"], [25, 134, 16, 115], [25, 135, 16, 116, "get<PERSON>urrent"], [25, 145, 16, 126], [25, 147, 16, 128, "context"], [25, 154, 16, 135], [25, 155, 16, 136, "get<PERSON>urrent"], [25, 165, 16, 146], [25, 167, 16, 148, "selector"], [25, 175, 16, 156], [25, 176, 16, 157], [26, 4, 17, 2], [26, 11, 17, 9, "value"], [26, 16, 17, 14], [27, 2, 18, 0], [28, 2, 19, 7], [28, 11, 19, 16, "FrameSizeProvider"], [28, 28, 19, 33, "FrameSizeProvider"], [28, 29, 19, 33, "_ref"], [28, 33, 19, 33], [28, 35, 21, 3], [29, 4, 21, 3], [29, 8, 20, 2, "children"], [29, 16, 20, 10], [29, 19, 20, 10, "_ref"], [29, 23, 20, 10], [29, 24, 20, 2, "children"], [29, 32, 20, 10], [30, 4, 22, 2], [30, 8, 22, 8, "context"], [30, 15, 22, 15], [30, 18, 22, 18, "React"], [30, 23, 22, 23], [30, 24, 22, 24, "useContext"], [30, 34, 22, 34], [30, 35, 22, 35, "FrameContext"], [30, 47, 22, 47], [30, 48, 22, 48], [31, 4, 23, 2], [31, 8, 23, 6, "context"], [31, 15, 23, 13], [31, 19, 23, 17], [31, 23, 23, 21], [31, 25, 23, 23], [32, 6, 24, 4], [33, 6, 25, 4], [33, 13, 25, 11, "children"], [33, 21, 25, 19], [34, 4, 26, 2], [35, 4, 27, 2], [35, 11, 27, 9], [35, 24, 27, 22], [35, 28, 27, 22, "_jsx"], [35, 43, 27, 26], [35, 45, 27, 27, "FrameSizeProviderInner"], [35, 67, 27, 49], [35, 69, 27, 51], [36, 6, 28, 4, "children"], [36, 14, 28, 12], [36, 16, 28, 14, "children"], [37, 4, 29, 2], [37, 5, 29, 3], [37, 6, 29, 4], [38, 2, 30, 0], [39, 2, 31, 0], [39, 11, 31, 9, "FrameSizeProviderInner"], [39, 33, 31, 31, "FrameSizeProviderInner"], [39, 34, 31, 31, "_ref2"], [39, 39, 31, 31], [39, 41, 33, 3], [40, 4, 33, 3], [40, 8, 32, 2, "children"], [40, 16, 32, 10], [40, 19, 32, 10, "_ref2"], [40, 24, 32, 10], [40, 25, 32, 2, "children"], [40, 33, 32, 10], [41, 4, 34, 2], [41, 8, 34, 8, "listeners"], [41, 17, 34, 17], [41, 20, 34, 20, "React"], [41, 25, 34, 25], [41, 26, 34, 26, "useRef"], [41, 32, 34, 32], [41, 33, 34, 33], [41, 37, 34, 37, "Set"], [41, 40, 34, 40], [41, 41, 34, 41], [41, 42, 34, 42], [41, 43, 34, 43], [42, 4, 35, 2], [42, 8, 35, 2, "_useResizeListener"], [42, 26, 35, 2], [42, 29, 38, 6, "useResizeListener"], [42, 46, 38, 23], [42, 47, 38, 24, "size"], [42, 51, 38, 28], [42, 55, 38, 32], [43, 8, 39, 4, "listeners"], [43, 17, 39, 13], [43, 18, 39, 14, "current"], [43, 25, 39, 21], [43, 26, 39, 22, "for<PERSON>ach"], [43, 33, 39, 29], [43, 34, 39, 30, "listener"], [43, 42, 39, 38], [43, 46, 39, 42, "listener"], [43, 54, 39, 50], [43, 55, 39, 51, "size"], [43, 59, 39, 55], [43, 60, 39, 56], [43, 61, 39, 57], [44, 6, 40, 2], [44, 7, 40, 3], [44, 8, 40, 4], [45, 6, 36, 4, "element"], [45, 13, 36, 11], [45, 16, 36, 11, "_useResizeListener"], [45, 34, 36, 11], [45, 35, 36, 4, "element"], [45, 42, 36, 11], [46, 6, 37, 4, "get"], [46, 9, 37, 7], [46, 12, 37, 7, "_useResizeListener"], [46, 30, 37, 7], [46, 31, 37, 4, "get"], [46, 34, 37, 7], [47, 4, 41, 2], [47, 8, 41, 8, "get<PERSON>urrent"], [47, 18, 41, 18], [47, 21, 41, 21], [47, 25, 41, 21, "useLatestCallback"], [47, 51, 41, 38], [47, 53, 41, 39, "get"], [47, 56, 41, 42], [47, 57, 41, 43], [48, 4, 42, 2], [48, 8, 42, 8, "subscribe"], [48, 17, 42, 17], [48, 20, 42, 20], [48, 24, 42, 20, "useLatestCallback"], [48, 50, 42, 37], [48, 52, 42, 38, "listener"], [48, 60, 42, 46], [48, 64, 42, 50], [49, 6, 43, 4, "listeners"], [49, 15, 43, 13], [49, 16, 43, 14, "current"], [49, 23, 43, 21], [49, 24, 43, 22, "add"], [49, 27, 43, 25], [49, 28, 43, 26, "listener"], [49, 36, 43, 34], [49, 37, 43, 35], [50, 6, 44, 4], [50, 13, 44, 11], [50, 19, 44, 17], [51, 8, 45, 6, "listeners"], [51, 17, 45, 15], [51, 18, 45, 16, "current"], [51, 25, 45, 23], [51, 26, 45, 24, "delete"], [51, 32, 45, 30], [51, 33, 45, 31, "listener"], [51, 41, 45, 39], [51, 42, 45, 40], [52, 6, 46, 4], [52, 7, 46, 5], [53, 4, 47, 2], [53, 5, 47, 3], [53, 6, 47, 4], [54, 4, 48, 2], [54, 8, 48, 8, "subscribeDebounced"], [54, 26, 48, 26], [54, 29, 48, 29], [54, 33, 48, 29, "useLatestCallback"], [54, 59, 48, 46], [54, 61, 48, 47, "listener"], [54, 69, 48, 55], [54, 73, 48, 59], [55, 6, 49, 4], [55, 10, 49, 8, "timer"], [55, 15, 49, 13], [56, 6, 50, 4], [56, 10, 50, 10, "debouncedListener"], [56, 27, 50, 27], [56, 30, 50, 30, "size"], [56, 34, 50, 34], [56, 38, 50, 38], [57, 8, 51, 6, "clearTimeout"], [57, 20, 51, 18], [57, 21, 51, 19, "timer"], [57, 26, 51, 24], [57, 27, 51, 25], [58, 8, 52, 6, "timer"], [58, 13, 52, 11], [58, 16, 52, 14, "setTimeout"], [58, 26, 52, 24], [58, 27, 52, 25], [58, 33, 52, 31], [59, 10, 53, 8, "listener"], [59, 18, 53, 16], [59, 19, 53, 17, "size"], [59, 23, 53, 21], [59, 24, 53, 22], [60, 8, 54, 6], [60, 9, 54, 7], [60, 11, 54, 9], [60, 14, 54, 12], [60, 15, 54, 13], [61, 6, 55, 4], [61, 7, 55, 5], [62, 6, 56, 4, "listeners"], [62, 15, 56, 13], [62, 16, 56, 14, "current"], [62, 23, 56, 21], [62, 24, 56, 22, "add"], [62, 27, 56, 25], [62, 28, 56, 26, "debouncedListener"], [62, 45, 56, 43], [62, 46, 56, 44], [63, 6, 57, 4], [63, 13, 57, 11], [63, 19, 57, 17], [64, 8, 58, 6, "clearTimeout"], [64, 20, 58, 18], [64, 21, 58, 19, "timer"], [64, 26, 58, 24], [64, 27, 58, 25], [65, 8, 59, 6, "listeners"], [65, 17, 59, 15], [65, 18, 59, 16, "current"], [65, 25, 59, 23], [65, 26, 59, 24, "delete"], [65, 32, 59, 30], [65, 33, 59, 31, "debouncedListener"], [65, 50, 59, 48], [65, 51, 59, 49], [66, 6, 60, 4], [66, 7, 60, 5], [67, 4, 61, 2], [67, 5, 61, 3], [67, 6, 61, 4], [68, 4, 62, 2], [68, 8, 62, 8, "context"], [68, 15, 62, 15], [68, 18, 62, 18, "React"], [68, 23, 62, 23], [68, 24, 62, 24, "useMemo"], [68, 31, 62, 31], [68, 32, 62, 32], [68, 39, 62, 39], [69, 6, 63, 4, "get<PERSON>urrent"], [69, 16, 63, 14], [70, 6, 64, 4, "subscribe"], [70, 15, 64, 13], [71, 6, 65, 4, "subscribeDebounced"], [72, 4, 66, 2], [72, 5, 66, 3], [72, 6, 66, 4], [72, 8, 66, 6], [72, 9, 66, 7, "subscribe"], [72, 18, 66, 16], [72, 20, 66, 18, "subscribeDebounced"], [72, 38, 66, 36], [72, 40, 66, 38, "get<PERSON>urrent"], [72, 50, 66, 48], [72, 51, 66, 49], [72, 52, 66, 50], [73, 4, 67, 2], [73, 11, 67, 9], [73, 24, 67, 22], [73, 28, 67, 22, "_jsxs"], [73, 44, 67, 27], [73, 46, 67, 28, "FrameContext"], [73, 58, 67, 40], [73, 59, 67, 41, "Provider"], [73, 67, 67, 49], [73, 69, 67, 51], [74, 6, 68, 4, "value"], [74, 11, 68, 9], [74, 13, 68, 11, "context"], [74, 20, 68, 18], [75, 6, 69, 4, "children"], [75, 14, 69, 12], [75, 16, 69, 14], [75, 17, 69, 15, "element"], [75, 24, 69, 22], [75, 26, 69, 24, "children"], [75, 34, 69, 32], [76, 4, 70, 2], [76, 5, 70, 3], [76, 6, 70, 4], [77, 2, 71, 0], [78, 2, 72, 0], [78, 6, 72, 6, "useResizeListener"], [78, 23, 72, 23], [78, 26, 72, 26, "Platform"], [78, 47, 72, 34], [78, 48, 72, 35, "OS"], [78, 50, 72, 37], [78, 55, 72, 42], [78, 60, 72, 47], [78, 63, 72, 50, "useResizeListenerWeb"], [78, 83, 72, 70], [78, 86, 72, 73, "useResizeListenerNative"], [78, 109, 72, 96], [79, 2, 73, 0], [79, 11, 73, 9, "useResizeListenerNative"], [79, 34, 73, 32, "useResizeListenerNative"], [79, 35, 73, 33, "onChange"], [79, 43, 73, 41], [79, 45, 73, 43], [80, 4, 74, 2], [80, 8, 74, 8, "frame"], [80, 13, 74, 13], [80, 16, 74, 16], [80, 20, 74, 16, "useSafeAreaFrame"], [80, 64, 74, 32], [80, 66, 74, 33], [80, 67, 74, 34], [81, 4, 75, 2, "React"], [81, 9, 75, 7], [81, 10, 75, 8, "useLayoutEffect"], [81, 25, 75, 23], [81, 26, 75, 24], [81, 32, 75, 30], [82, 6, 76, 4, "onChange"], [82, 14, 76, 12], [82, 15, 76, 13, "frame"], [82, 20, 76, 18], [82, 21, 76, 19], [83, 4, 77, 2], [83, 5, 77, 3], [83, 7, 77, 5], [83, 8, 77, 6, "frame"], [83, 13, 77, 11], [83, 15, 77, 13, "onChange"], [83, 23, 77, 21], [83, 24, 77, 22], [83, 25, 77, 23], [84, 4, 78, 2], [84, 11, 78, 9], [85, 6, 79, 4, "element"], [85, 13, 79, 11], [85, 15, 79, 13], [85, 19, 79, 17], [86, 6, 80, 4, "get"], [86, 9, 80, 7], [86, 11, 80, 9, "get"], [86, 12, 80, 9], [86, 17, 80, 15, "frame"], [87, 4, 81, 2], [87, 5, 81, 3], [88, 2, 82, 0], [89, 2, 83, 0], [89, 6, 83, 0, "_Dimensions$get"], [89, 21, 83, 0], [89, 24, 86, 4, "Dimensions"], [89, 47, 86, 14], [89, 48, 86, 15, "get"], [89, 51, 86, 18], [89, 52, 86, 19], [89, 60, 86, 27], [89, 61, 86, 28], [90, 4, 86, 28, "_Dimensions$get$width"], [90, 25, 86, 28], [90, 28, 86, 28, "_Dimensions$get"], [90, 43, 86, 28], [90, 44, 84, 2, "width"], [90, 49, 84, 7], [91, 4, 84, 2, "width"], [91, 9, 84, 7], [91, 12, 84, 7, "_Dimensions$get$width"], [91, 33, 84, 7], [91, 47, 84, 10], [91, 48, 84, 11], [91, 51, 84, 11, "_Dimensions$get$width"], [91, 72, 84, 11], [92, 4, 84, 11, "_Dimensions$get$heigh"], [92, 25, 84, 11], [92, 28, 84, 11, "_Dimensions$get"], [92, 43, 84, 11], [92, 44, 85, 2, "height"], [92, 50, 85, 8], [93, 4, 85, 2, "height"], [93, 10, 85, 8], [93, 13, 85, 8, "_Dimensions$get$heigh"], [93, 34, 85, 8], [93, 48, 85, 11], [93, 49, 85, 12], [93, 52, 85, 12, "_Dimensions$get$heigh"], [93, 73, 85, 12], [95, 2, 88, 0], [96, 2, 89, 0], [97, 2, 90, 0], [97, 11, 90, 9, "useResizeListenerWeb"], [97, 31, 90, 29, "useResizeListenerWeb"], [97, 32, 90, 30, "onChange"], [97, 40, 90, 38], [97, 42, 90, 40], [98, 4, 91, 2], [98, 8, 91, 8, "frameRef"], [98, 16, 91, 16], [98, 19, 91, 19, "React"], [98, 24, 91, 24], [98, 25, 91, 25, "useRef"], [98, 31, 91, 31], [98, 32, 91, 32], [99, 6, 92, 4, "width"], [99, 11, 92, 9], [100, 6, 93, 4, "height"], [101, 4, 94, 2], [101, 5, 94, 3], [101, 6, 94, 4], [102, 4, 95, 2], [102, 8, 95, 8, "elementRef"], [102, 18, 95, 18], [102, 21, 95, 21, "React"], [102, 26, 95, 26], [102, 27, 95, 27, "useRef"], [102, 33, 95, 33], [102, 34, 95, 34], [102, 38, 95, 38], [102, 39, 95, 39], [103, 4, 96, 2, "React"], [103, 9, 96, 7], [103, 10, 96, 8, "useEffect"], [103, 19, 96, 17], [103, 20, 96, 18], [103, 26, 96, 24], [104, 6, 97, 4], [104, 10, 97, 8, "elementRef"], [104, 20, 97, 18], [104, 21, 97, 19, "current"], [104, 28, 97, 26], [104, 32, 97, 30], [104, 36, 97, 34], [104, 38, 97, 36], [105, 8, 98, 6], [106, 6, 99, 4], [107, 6, 100, 4], [107, 10, 100, 10, "update"], [107, 16, 100, 16], [107, 19, 100, 19, "size"], [107, 23, 100, 23], [107, 27, 100, 27], [108, 8, 101, 6], [108, 12, 101, 10, "frameRef"], [108, 20, 101, 18], [108, 21, 101, 19, "current"], [108, 28, 101, 26], [108, 29, 101, 27, "width"], [108, 34, 101, 32], [108, 39, 101, 37, "size"], [108, 43, 101, 41], [108, 44, 101, 42, "width"], [108, 49, 101, 47], [108, 53, 101, 51, "frameRef"], [108, 61, 101, 59], [108, 62, 101, 60, "current"], [108, 69, 101, 67], [108, 70, 101, 68, "height"], [108, 76, 101, 74], [108, 81, 101, 79, "size"], [108, 85, 101, 83], [108, 86, 101, 84, "height"], [108, 92, 101, 90], [108, 94, 101, 92], [109, 10, 102, 8], [110, 8, 103, 6], [111, 8, 104, 6, "frameRef"], [111, 16, 104, 14], [111, 17, 104, 15, "current"], [111, 24, 104, 22], [111, 27, 104, 25, "size"], [111, 31, 104, 29], [112, 8, 105, 6, "onChange"], [112, 16, 105, 14], [112, 17, 105, 15, "size"], [112, 21, 105, 19], [112, 22, 105, 20], [113, 6, 106, 4], [113, 7, 106, 5], [114, 6, 107, 4], [114, 10, 107, 10, "rect"], [114, 14, 107, 14], [114, 17, 107, 17, "elementRef"], [114, 27, 107, 27], [114, 28, 107, 28, "current"], [114, 35, 107, 35], [114, 36, 107, 36, "getBoundingClientRect"], [114, 57, 107, 57], [114, 58, 107, 58], [114, 59, 107, 59], [115, 6, 108, 4, "update"], [115, 12, 108, 10], [115, 13, 108, 11], [116, 8, 109, 6, "width"], [116, 13, 109, 11], [116, 15, 109, 13, "rect"], [116, 19, 109, 17], [116, 20, 109, 18, "width"], [116, 25, 109, 23], [117, 8, 110, 6, "height"], [117, 14, 110, 12], [117, 16, 110, 14, "rect"], [117, 20, 110, 18], [117, 21, 110, 19, "height"], [118, 6, 111, 4], [118, 7, 111, 5], [118, 8, 111, 6], [119, 6, 112, 4], [119, 10, 112, 10, "observer"], [119, 18, 112, 18], [119, 21, 112, 21], [119, 25, 112, 25, "ResizeObserver"], [119, 39, 112, 39], [119, 40, 112, 40, "entries"], [119, 47, 112, 47], [119, 51, 112, 51], [120, 8, 113, 6], [120, 12, 113, 12, "entry"], [120, 17, 113, 17], [120, 20, 113, 20, "entries"], [120, 27, 113, 27], [120, 28, 113, 28], [120, 29, 113, 29], [120, 30, 113, 30], [121, 8, 114, 6], [121, 12, 114, 10, "entry"], [121, 17, 114, 15], [121, 19, 114, 17], [122, 10, 115, 8], [122, 14, 115, 8, "_entry$contentRect"], [122, 32, 115, 8], [122, 35, 118, 12, "entry"], [122, 40, 118, 17], [122, 41, 118, 18, "contentRect"], [122, 52, 118, 29], [123, 12, 116, 10, "width"], [123, 18, 116, 15], [123, 21, 116, 15, "_entry$contentRect"], [123, 39, 116, 15], [123, 40, 116, 10, "width"], [123, 45, 116, 15], [124, 12, 117, 10, "height"], [124, 19, 117, 16], [124, 22, 117, 16, "_entry$contentRect"], [124, 40, 117, 16], [124, 41, 117, 10, "height"], [124, 47, 117, 16], [125, 10, 119, 8, "update"], [125, 16, 119, 14], [125, 17, 119, 15], [126, 12, 120, 10, "width"], [126, 17, 120, 15], [126, 19, 120, 10, "width"], [126, 25, 120, 15], [127, 12, 121, 10, "height"], [127, 18, 121, 16], [127, 20, 121, 10, "height"], [128, 10, 122, 8], [128, 11, 122, 9], [128, 12, 122, 10], [129, 8, 123, 6], [130, 6, 124, 4], [130, 7, 124, 5], [130, 8, 124, 6], [131, 6, 125, 4, "observer"], [131, 14, 125, 12], [131, 15, 125, 13, "observe"], [131, 22, 125, 20], [131, 23, 125, 21, "elementRef"], [131, 33, 125, 31], [131, 34, 125, 32, "current"], [131, 41, 125, 39], [131, 42, 125, 40], [132, 6, 126, 4], [132, 13, 126, 11], [132, 19, 126, 17], [133, 8, 127, 6, "observer"], [133, 16, 127, 14], [133, 17, 127, 15, "disconnect"], [133, 27, 127, 25], [133, 28, 127, 26], [133, 29, 127, 27], [134, 6, 128, 4], [134, 7, 128, 5], [135, 4, 129, 2], [135, 5, 129, 3], [135, 7, 129, 5], [135, 8, 129, 6, "onChange"], [135, 16, 129, 14], [135, 17, 129, 15], [135, 18, 129, 16], [136, 4, 130, 2], [136, 8, 130, 8, "element"], [136, 15, 130, 15], [136, 18, 130, 18], [136, 31, 130, 31], [136, 35, 130, 31, "_jsx"], [136, 50, 130, 35], [136, 52, 130, 36], [136, 57, 130, 41], [136, 59, 130, 43], [137, 6, 131, 4, "ref"], [137, 9, 131, 7], [137, 11, 131, 9, "elementRef"], [137, 21, 131, 19], [138, 6, 132, 4, "style"], [138, 11, 132, 9], [138, 13, 132, 11], [139, 8, 133, 6], [139, 11, 133, 9, "StyleSheet"], [139, 34, 133, 19], [139, 35, 133, 20, "absoluteFillObject"], [139, 53, 133, 38], [140, 8, 134, 6, "pointerEvents"], [140, 21, 134, 19], [140, 23, 134, 21], [140, 29, 134, 27], [141, 8, 135, 6, "visibility"], [141, 18, 135, 16], [141, 20, 135, 18], [142, 6, 136, 4], [143, 4, 137, 2], [143, 5, 137, 3], [143, 6, 137, 4], [144, 4, 138, 2], [144, 11, 138, 9], [145, 6, 139, 4, "element"], [145, 13, 139, 11], [146, 6, 140, 4, "get"], [146, 9, 140, 7], [146, 11, 140, 9, "get"], [146, 12, 140, 9], [146, 17, 140, 15, "frameRef"], [146, 25, 140, 23], [146, 26, 140, 24, "current"], [147, 4, 141, 2], [147, 5, 141, 3], [148, 2, 142, 0], [149, 0, 142, 1], [149, 3]], "functionMap": {"names": ["<global>", "useFrameSize", "FrameSizeProvider", "FrameSizeProviderInner", "useResizeListener$argument_0", "listeners.current.forEach$argument_0", "useLatestCallback$argument_0", "<anonymous>", "debouncedListener", "setTimeout$argument_0", "React.useMemo$argument_0", "useResizeListenerNative", "React.useLayoutEffect$argument_0", "get", "useResizeListenerWeb", "React.useEffect$argument_0", "update", "ResizeObserver$argument_0"], "mappings": "AAA;OCU;CDO;OEC;CFW;AGC;wBCO;8BCC,0BD;GDC;sCGE;WCE;KDE;GHC;+CGC;8BEE;yBCE;ODE;KFC;WCE;KDG;GHC;gCOC;IPI;CHK;AWE;wBCE;GDE;SEG,WF;CXE;AcQ;kBCM;mBCI;KDM;wCEM;KFY;WRE;KQE;GDC;SDW,sBC;CdE"}}, "type": "js/module"}]}