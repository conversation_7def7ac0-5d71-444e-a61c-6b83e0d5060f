{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "./BlobManager", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 61, "column": 24}, "end": {"line": 61, "column": 48}}, {"start": {"line": 85, "column": 24}, "end": {"line": 85, "column": 48}}, {"start": {"line": 135, "column": 24}, "end": {"line": 135, "column": 48}}], "key": "Fpxa1gBsnj4MBlPbNG6wQnDyrT4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var Blob = /*#__PURE__*/function () {\n    function Blob() {\n      var parts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n      var options = arguments.length > 1 ? arguments[1] : undefined;\n      (0, _classCallCheck2.default)(this, Blob);\n      var BlobManager = require(_dependencyMap[3], \"./BlobManager\").default;\n      this.data = BlobManager.createFromParts(parts, options).data;\n    }\n    return (0, _createClass2.default)(Blob, [{\n      key: \"data\",\n      get: function () {\n        if (!this._data) {\n          throw new Error('Blob has been closed and is no longer available');\n        }\n        return this._data;\n      },\n      set: function (data) {\n        this._data = data;\n      }\n    }, {\n      key: \"slice\",\n      value: function slice(start, end) {\n        var contentType = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '';\n        var BlobManager = require(_dependencyMap[3], \"./BlobManager\").default;\n        var _this$data = this.data,\n          offset = _this$data.offset,\n          size = _this$data.size;\n        if (typeof start === 'number') {\n          if (start > size) {\n            start = size;\n          }\n          offset += start;\n          size -= start;\n          if (typeof end === 'number') {\n            if (end < 0) {\n              end = this.size + end;\n            }\n            if (end > this.size) {\n              end = this.size;\n            }\n            size = end - start;\n          }\n        }\n        return BlobManager.createFromOptions({\n          blobId: this.data.blobId,\n          offset,\n          size,\n          type: contentType,\n          __collector: this.data.__collector\n        });\n      }\n    }, {\n      key: \"close\",\n      value: function close() {\n        var BlobManager = require(_dependencyMap[3], \"./BlobManager\").default;\n        BlobManager.release(this.data.blobId);\n        this.data = null;\n      }\n    }, {\n      key: \"size\",\n      get: function () {\n        return this.data.size;\n      }\n    }, {\n      key: \"type\",\n      get: function () {\n        return this.data.type || '';\n      }\n    }]);\n  }();\n  var _default = exports.default = Blob;\n});", "lineCount": 82, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_classCallCheck2"], [9, 22, 11, 13], [9, 25, 11, 13, "_interopRequireDefault"], [9, 47, 11, 13], [9, 48, 11, 13, "require"], [9, 55, 11, 13], [9, 56, 11, 13, "_dependencyMap"], [9, 70, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_createClass2"], [10, 19, 11, 13], [10, 22, 11, 13, "_interopRequireDefault"], [10, 44, 11, 13], [10, 45, 11, 13, "require"], [10, 52, 11, 13], [10, 53, 11, 13, "_dependencyMap"], [10, 67, 11, 13], [11, 2, 11, 13], [11, 6, 52, 6, "Blob"], [11, 10, 52, 10], [12, 4, 60, 2], [12, 13, 60, 2, "Blob"], [12, 18, 60, 2], [12, 20, 60, 71], [13, 6, 60, 71], [13, 10, 60, 14, "parts"], [13, 15, 60, 41], [13, 18, 60, 41, "arguments"], [13, 27, 60, 41], [13, 28, 60, 41, "length"], [13, 34, 60, 41], [13, 42, 60, 41, "arguments"], [13, 51, 60, 41], [13, 59, 60, 41, "undefined"], [13, 68, 60, 41], [13, 71, 60, 41, "arguments"], [13, 80, 60, 41], [13, 86, 60, 44], [13, 88, 60, 46], [14, 6, 60, 46], [14, 10, 60, 48, "options"], [14, 17, 60, 69], [14, 20, 60, 69, "arguments"], [14, 29, 60, 69], [14, 30, 60, 69, "length"], [14, 36, 60, 69], [14, 43, 60, 69, "arguments"], [14, 52, 60, 69], [14, 58, 60, 69, "undefined"], [14, 67, 60, 69], [15, 6, 60, 69], [15, 10, 60, 69, "_classCallCheck2"], [15, 26, 60, 69], [15, 27, 60, 69, "default"], [15, 34, 60, 69], [15, 42, 60, 69, "Blob"], [15, 46, 60, 69], [16, 6, 61, 4], [16, 10, 61, 10, "BlobManager"], [16, 21, 61, 21], [16, 24, 61, 24, "require"], [16, 31, 61, 31], [16, 32, 61, 31, "_dependencyMap"], [16, 46, 61, 31], [16, 66, 61, 47], [16, 67, 61, 48], [16, 68, 61, 49, "default"], [16, 75, 61, 56], [17, 6, 62, 4], [17, 10, 62, 8], [17, 11, 62, 9, "data"], [17, 15, 62, 13], [17, 18, 62, 16, "BlobManager"], [17, 29, 62, 27], [17, 30, 62, 28, "createFromParts"], [17, 45, 62, 43], [17, 46, 62, 44, "parts"], [17, 51, 62, 49], [17, 53, 62, 51, "options"], [17, 60, 62, 58], [17, 61, 62, 59], [17, 62, 62, 60, "data"], [17, 66, 62, 64], [18, 4, 63, 2], [19, 4, 63, 3], [19, 15, 63, 3, "_createClass2"], [19, 28, 63, 3], [19, 29, 63, 3, "default"], [19, 36, 63, 3], [19, 38, 63, 3, "Blob"], [19, 42, 63, 3], [20, 6, 63, 3, "key"], [20, 9, 63, 3], [21, 6, 63, 3, "get"], [21, 9, 63, 3], [21, 11, 76, 2], [21, 20, 76, 2, "get"], [21, 21, 76, 2], [21, 23, 76, 23], [22, 8, 77, 4], [22, 12, 77, 8], [22, 13, 77, 9], [22, 17, 77, 13], [22, 18, 77, 14, "_data"], [22, 23, 77, 19], [22, 25, 77, 21], [23, 10, 78, 6], [23, 16, 78, 12], [23, 20, 78, 16, "Error"], [23, 25, 78, 21], [23, 26, 78, 22], [23, 75, 78, 71], [23, 76, 78, 72], [24, 8, 79, 4], [25, 8, 81, 4], [25, 15, 81, 11], [25, 19, 81, 15], [25, 20, 81, 16, "_data"], [25, 25, 81, 21], [26, 6, 82, 2], [26, 7, 82, 3], [27, 6, 82, 3, "set"], [27, 9, 82, 3], [27, 11, 71, 2], [27, 20, 71, 2, "set"], [27, 21, 71, 11, "data"], [27, 25, 71, 26], [27, 27, 71, 28], [28, 8, 72, 4], [28, 12, 72, 8], [28, 13, 72, 9, "_data"], [28, 18, 72, 14], [28, 21, 72, 17, "data"], [28, 25, 72, 21], [29, 6, 73, 2], [30, 4, 73, 3], [31, 6, 73, 3, "key"], [31, 9, 73, 3], [32, 6, 73, 3, "value"], [32, 11, 73, 3], [32, 13, 84, 2], [32, 22, 84, 2, "slice"], [32, 27, 84, 7, "slice"], [32, 28, 84, 8, "start"], [32, 33, 84, 22], [32, 35, 84, 24, "end"], [32, 38, 84, 36], [32, 40, 84, 71], [33, 8, 84, 71], [33, 12, 84, 38, "contentType"], [33, 23, 84, 58], [33, 26, 84, 58, "arguments"], [33, 35, 84, 58], [33, 36, 84, 58, "length"], [33, 42, 84, 58], [33, 50, 84, 58, "arguments"], [33, 59, 84, 58], [33, 67, 84, 58, "undefined"], [33, 76, 84, 58], [33, 79, 84, 58, "arguments"], [33, 88, 84, 58], [33, 94, 84, 61], [33, 96, 84, 63], [34, 8, 85, 4], [34, 12, 85, 10, "BlobManager"], [34, 23, 85, 21], [34, 26, 85, 24, "require"], [34, 33, 85, 31], [34, 34, 85, 31, "_dependencyMap"], [34, 48, 85, 31], [34, 68, 85, 47], [34, 69, 85, 48], [34, 70, 85, 49, "default"], [34, 77, 85, 56], [35, 8, 86, 4], [35, 12, 86, 4, "_this$data"], [35, 22, 86, 4], [35, 25, 86, 25], [35, 29, 86, 29], [35, 30, 86, 30, "data"], [35, 34, 86, 34], [36, 10, 86, 9, "offset"], [36, 16, 86, 15], [36, 19, 86, 15, "_this$data"], [36, 29, 86, 15], [36, 30, 86, 9, "offset"], [36, 36, 86, 15], [37, 10, 86, 17, "size"], [37, 14, 86, 21], [37, 17, 86, 21, "_this$data"], [37, 27, 86, 21], [37, 28, 86, 17, "size"], [37, 32, 86, 21], [38, 8, 88, 4], [38, 12, 88, 8], [38, 19, 88, 15, "start"], [38, 24, 88, 20], [38, 29, 88, 25], [38, 37, 88, 33], [38, 39, 88, 35], [39, 10, 89, 6], [39, 14, 89, 10, "start"], [39, 19, 89, 15], [39, 22, 89, 18, "size"], [39, 26, 89, 22], [39, 28, 89, 24], [40, 12, 91, 8, "start"], [40, 17, 91, 13], [40, 20, 91, 16, "size"], [40, 24, 91, 20], [41, 10, 92, 6], [42, 10, 93, 6, "offset"], [42, 16, 93, 12], [42, 20, 93, 16, "start"], [42, 25, 93, 21], [43, 10, 94, 6, "size"], [43, 14, 94, 10], [43, 18, 94, 14, "start"], [43, 23, 94, 19], [44, 10, 96, 6], [44, 14, 96, 10], [44, 21, 96, 17, "end"], [44, 24, 96, 20], [44, 29, 96, 25], [44, 37, 96, 33], [44, 39, 96, 35], [45, 12, 97, 8], [45, 16, 97, 12, "end"], [45, 19, 97, 15], [45, 22, 97, 18], [45, 23, 97, 19], [45, 25, 97, 21], [46, 14, 99, 10, "end"], [46, 17, 99, 13], [46, 20, 99, 16], [46, 24, 99, 20], [46, 25, 99, 21, "size"], [46, 29, 99, 25], [46, 32, 99, 28, "end"], [46, 35, 99, 31], [47, 12, 100, 8], [48, 12, 101, 8], [48, 16, 101, 12, "end"], [48, 19, 101, 15], [48, 22, 101, 18], [48, 26, 101, 22], [48, 27, 101, 23, "size"], [48, 31, 101, 27], [48, 33, 101, 29], [49, 14, 103, 10, "end"], [49, 17, 103, 13], [49, 20, 103, 16], [49, 24, 103, 20], [49, 25, 103, 21, "size"], [49, 29, 103, 25], [50, 12, 104, 8], [51, 12, 105, 8, "size"], [51, 16, 105, 12], [51, 19, 105, 15, "end"], [51, 22, 105, 18], [51, 25, 105, 21, "start"], [51, 30, 105, 26], [52, 10, 106, 6], [53, 8, 107, 4], [54, 8, 108, 4], [54, 15, 108, 11, "BlobManager"], [54, 26, 108, 22], [54, 27, 108, 23, "createFromOptions"], [54, 44, 108, 40], [54, 45, 108, 41], [55, 10, 109, 6, "blobId"], [55, 16, 109, 12], [55, 18, 109, 14], [55, 22, 109, 18], [55, 23, 109, 19, "data"], [55, 27, 109, 23], [55, 28, 109, 24, "blobId"], [55, 34, 109, 30], [56, 10, 110, 6, "offset"], [56, 16, 110, 12], [57, 10, 111, 6, "size"], [57, 14, 111, 10], [58, 10, 112, 6, "type"], [58, 14, 112, 10], [58, 16, 112, 12, "contentType"], [58, 27, 112, 23], [59, 10, 118, 6, "__collector"], [59, 21, 118, 17], [59, 23, 118, 19], [59, 27, 118, 23], [59, 28, 118, 24, "data"], [59, 32, 118, 28], [59, 33, 118, 29, "__collector"], [60, 8, 119, 4], [60, 9, 119, 5], [60, 10, 119, 6], [61, 6, 120, 2], [62, 4, 120, 3], [63, 6, 120, 3, "key"], [63, 9, 120, 3], [64, 6, 120, 3, "value"], [64, 11, 120, 3], [64, 13, 134, 2], [64, 22, 134, 2, "close"], [64, 27, 134, 7, "close"], [64, 28, 134, 7], [64, 30, 134, 10], [65, 8, 135, 4], [65, 12, 135, 10, "BlobManager"], [65, 23, 135, 21], [65, 26, 135, 24, "require"], [65, 33, 135, 31], [65, 34, 135, 31, "_dependencyMap"], [65, 48, 135, 31], [65, 68, 135, 47], [65, 69, 135, 48], [65, 70, 135, 49, "default"], [65, 77, 135, 56], [66, 8, 136, 4, "BlobManager"], [66, 19, 136, 15], [66, 20, 136, 16, "release"], [66, 27, 136, 23], [66, 28, 136, 24], [66, 32, 136, 28], [66, 33, 136, 29, "data"], [66, 37, 136, 33], [66, 38, 136, 34, "blobId"], [66, 44, 136, 40], [66, 45, 136, 41], [67, 8, 137, 4], [67, 12, 137, 8], [67, 13, 137, 9, "data"], [67, 17, 137, 13], [67, 20, 137, 16], [67, 24, 137, 20], [68, 6, 138, 2], [69, 4, 138, 3], [70, 6, 138, 3, "key"], [70, 9, 138, 3], [71, 6, 138, 3, "get"], [71, 9, 138, 3], [71, 11, 144, 2], [71, 20, 144, 2, "get"], [71, 21, 144, 2], [71, 23, 144, 21], [72, 8, 145, 4], [72, 15, 145, 11], [72, 19, 145, 15], [72, 20, 145, 16, "data"], [72, 24, 145, 20], [72, 25, 145, 21, "size"], [72, 29, 145, 25], [73, 6, 146, 2], [74, 4, 146, 3], [75, 6, 146, 3, "key"], [75, 9, 146, 3], [76, 6, 146, 3, "get"], [76, 9, 146, 3], [76, 11, 153, 2], [76, 20, 153, 2, "get"], [76, 21, 153, 2], [76, 23, 153, 21], [77, 8, 154, 4], [77, 15, 154, 11], [77, 19, 154, 15], [77, 20, 154, 16, "data"], [77, 24, 154, 20], [77, 25, 154, 21, "type"], [77, 29, 154, 25], [77, 33, 154, 29], [77, 35, 154, 31], [78, 6, 155, 2], [79, 4, 155, 3], [80, 2, 155, 3], [81, 2, 155, 3], [81, 6, 155, 3, "_default"], [81, 14, 155, 3], [81, 17, 155, 3, "exports"], [81, 24, 155, 3], [81, 25, 155, 3, "default"], [81, 32, 155, 3], [81, 35, 158, 15, "Blob"], [81, 39, 158, 19], [82, 0, 158, 19], [82, 3]], "functionMap": {"names": ["<global>", "Blob", "constructor", "set__data", "get__data", "slice", "close", "get__size", "get__type"], "mappings": "AAA;ACmD;ECQ;GDG;EEQ;GFE;EGG;GHM;EIE;GJoC;EKc;GLI;EMM;GNE;EOO;GPE;CDC"}}, "type": "js/module"}]}