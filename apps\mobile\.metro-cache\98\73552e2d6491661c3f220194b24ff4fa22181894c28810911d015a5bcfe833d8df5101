{"dependencies": [{"name": "../../../Libraries/Utilities/PolyfillFunctions", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 78}}], "key": "ByHaTzm/R0dqOywOK45GP8FPtt0=", "exportNames": ["*"]}}, {"name": "../webapis/geometry/DOMRect", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 48}}], "key": "LpDFe4ScJzHQffGmYCylw7ZVUS4=", "exportNames": ["*"]}}, {"name": "../webapis/geometry/DOMRectReadOnly", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 29, "column": 10}, "end": {"line": 29, "column": 56}}], "key": "Fy2MMp6mmVB573tvnrxMP4ax//w=", "exportNames": ["*"]}}, {"name": "../webapis/geometry/DOMRectList", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 34, "column": 10}, "end": {"line": 34, "column": 52}}], "key": "MqQHGmRbUMsJgro83/33ahIiIJI=", "exportNames": ["*"]}}, {"name": "../webapis/dom/oldstylecollections/HTMLCollection", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 39, "column": 10}, "end": {"line": 39, "column": 70}}], "key": "3ebEx5OqqVlBeo8j3FUhBmPKgZc=", "exportNames": ["*"]}}, {"name": "../webapis/dom/oldstylecollections/NodeList", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 44, "column": 10}, "end": {"line": 44, "column": 64}}], "key": "iFZqKM5EXvI0hiaQo/R+RxPRIps=", "exportNames": ["*"]}}, {"name": "../webapis/dom/nodes/ReadOnlyNode", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 49, "column": 10}, "end": {"line": 49, "column": 54}}], "key": "obAI/ztw+JJf4qyXACzzlIS4wGY=", "exportNames": ["*"]}}, {"name": "../webapis/dom/nodes/ReactNativeDocument", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 54, "column": 10}, "end": {"line": 54, "column": 61}}], "key": "Ny3M233iKjWVsidpx/SP8JM16Zo=", "exportNames": ["*"]}}, {"name": "../webapis/dom/nodes/ReadOnlyCharacterData", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 59, "column": 10}, "end": {"line": 59, "column": 63}}], "key": "qJGZ8fZzmdp/xGVp3YubXMFZBXA=", "exportNames": ["*"]}}, {"name": "../webapis/dom/nodes/ReadOnlyText", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 64, "column": 10}, "end": {"line": 64, "column": 54}}], "key": "X2K74sILbFUmgFjoDDFDaVLojTk=", "exportNames": ["*"]}}, {"name": "../webapis/dom/nodes/ReadOnlyElement", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 69, "column": 10}, "end": {"line": 69, "column": 57}}], "key": "7XvnMkvYga8ffRHh3GbwrvJdk0Y=", "exportNames": ["*"]}}, {"name": "../webapis/dom/nodes/ReactNativeElement", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 74, "column": 10}, "end": {"line": 74, "column": 60}}], "key": "sjRTQ87oh9ejUtxgZpqK5WpCSoU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = setUpDOM;\n  var _PolyfillFunctions = require(_dependencyMap[0], \"../../../Libraries/Utilities/PolyfillFunctions\");\n  var initialized = false;\n  function setUpDOM() {\n    if (initialized) {\n      return;\n    }\n    initialized = true;\n    (0, _PolyfillFunctions.polyfillGlobal)('DOMRect', () => require(_dependencyMap[1], \"../webapis/geometry/DOMRect\").default);\n    (0, _PolyfillFunctions.polyfillGlobal)('DOMRectReadOnly', () => require(_dependencyMap[2], \"../webapis/geometry/DOMRectReadOnly\").default);\n    (0, _PolyfillFunctions.polyfillGlobal)('DOMRectList', () => require(_dependencyMap[3], \"../webapis/geometry/DOMRectList\").default);\n    (0, _PolyfillFunctions.polyfillGlobal)('HTMLCollection', () => require(_dependencyMap[4], \"../webapis/dom/oldstylecollections/HTMLCollection\").default);\n    (0, _PolyfillFunctions.polyfillGlobal)('NodeList', () => require(_dependencyMap[5], \"../webapis/dom/oldstylecollections/NodeList\").default);\n    (0, _PolyfillFunctions.polyfillGlobal)('Node', () => require(_dependencyMap[6], \"../webapis/dom/nodes/ReadOnlyNode\").default);\n    (0, _PolyfillFunctions.polyfillGlobal)('Document', () => require(_dependencyMap[7], \"../webapis/dom/nodes/ReactNativeDocument\").default);\n    (0, _PolyfillFunctions.polyfillGlobal)('CharacterData', () => require(_dependencyMap[8], \"../webapis/dom/nodes/ReadOnlyCharacterData\").default);\n    (0, _PolyfillFunctions.polyfillGlobal)('Text', () => require(_dependencyMap[9], \"../webapis/dom/nodes/ReadOnlyText\").default);\n    (0, _PolyfillFunctions.polyfillGlobal)('Element', () => require(_dependencyMap[10], \"../webapis/dom/nodes/ReadOnlyElement\").default);\n    (0, _PolyfillFunctions.polyfillGlobal)('HTMLElement', () => require(_dependencyMap[11], \"../webapis/dom/nodes/ReactNativeElement\").default);\n  }\n});", "lineCount": 25, "map": [[6, 2, 11, 0], [6, 6, 11, 0, "_PolyfillFunctions"], [6, 24, 11, 0], [6, 27, 11, 0, "require"], [6, 34, 11, 0], [6, 35, 11, 0, "_dependencyMap"], [6, 49, 11, 0], [7, 2, 13, 0], [7, 6, 13, 4, "initialized"], [7, 17, 13, 15], [7, 20, 13, 18], [7, 25, 13, 23], [8, 2, 15, 15], [8, 11, 15, 24, "setUpDOM"], [8, 19, 15, 32, "setUpDOM"], [8, 20, 15, 32], [8, 22, 15, 35], [9, 4, 16, 2], [9, 8, 16, 6, "initialized"], [9, 19, 16, 17], [9, 21, 16, 19], [10, 6, 17, 4], [11, 4, 18, 2], [12, 4, 20, 2, "initialized"], [12, 15, 20, 13], [12, 18, 20, 16], [12, 22, 20, 20], [13, 4, 22, 2], [13, 8, 22, 2, "polyfillGlobal"], [13, 41, 22, 16], [13, 43, 23, 4], [13, 52, 23, 13], [13, 54, 24, 4], [13, 60, 24, 10, "require"], [13, 67, 24, 17], [13, 68, 24, 17, "_dependencyMap"], [13, 82, 24, 17], [13, 116, 24, 47], [13, 117, 24, 48], [13, 118, 24, 49, "default"], [13, 125, 25, 2], [13, 126, 25, 3], [14, 4, 27, 2], [14, 8, 27, 2, "polyfillGlobal"], [14, 41, 27, 16], [14, 43, 28, 4], [14, 60, 28, 21], [14, 62, 29, 4], [14, 68, 29, 10, "require"], [14, 75, 29, 17], [14, 76, 29, 17, "_dependencyMap"], [14, 90, 29, 17], [14, 132, 29, 55], [14, 133, 29, 56], [14, 134, 29, 57, "default"], [14, 141, 30, 2], [14, 142, 30, 3], [15, 4, 32, 2], [15, 8, 32, 2, "polyfillGlobal"], [15, 41, 32, 16], [15, 43, 33, 4], [15, 56, 33, 17], [15, 58, 34, 4], [15, 64, 34, 10, "require"], [15, 71, 34, 17], [15, 72, 34, 17, "_dependencyMap"], [15, 86, 34, 17], [15, 124, 34, 51], [15, 125, 34, 52], [15, 126, 34, 53, "default"], [15, 133, 35, 2], [15, 134, 35, 3], [16, 4, 37, 2], [16, 8, 37, 2, "polyfillGlobal"], [16, 41, 37, 16], [16, 43, 38, 4], [16, 59, 38, 20], [16, 61, 39, 4], [16, 67, 39, 10, "require"], [16, 74, 39, 17], [16, 75, 39, 17, "_dependencyMap"], [16, 89, 39, 17], [16, 145, 39, 69], [16, 146, 39, 70], [16, 147, 39, 71, "default"], [16, 154, 40, 2], [16, 155, 40, 3], [17, 4, 42, 2], [17, 8, 42, 2, "polyfillGlobal"], [17, 41, 42, 16], [17, 43, 43, 4], [17, 53, 43, 14], [17, 55, 44, 4], [17, 61, 44, 10, "require"], [17, 68, 44, 17], [17, 69, 44, 17, "_dependencyMap"], [17, 83, 44, 17], [17, 133, 44, 63], [17, 134, 44, 64], [17, 135, 44, 65, "default"], [17, 142, 45, 2], [17, 143, 45, 3], [18, 4, 47, 2], [18, 8, 47, 2, "polyfillGlobal"], [18, 41, 47, 16], [18, 43, 48, 4], [18, 49, 48, 10], [18, 51, 49, 4], [18, 57, 49, 10, "require"], [18, 64, 49, 17], [18, 65, 49, 17, "_dependencyMap"], [18, 79, 49, 17], [18, 119, 49, 53], [18, 120, 49, 54], [18, 121, 49, 55, "default"], [18, 128, 50, 2], [18, 129, 50, 3], [19, 4, 52, 2], [19, 8, 52, 2, "polyfillGlobal"], [19, 41, 52, 16], [19, 43, 53, 4], [19, 53, 53, 14], [19, 55, 54, 4], [19, 61, 54, 10, "require"], [19, 68, 54, 17], [19, 69, 54, 17, "_dependencyMap"], [19, 83, 54, 17], [19, 130, 54, 60], [19, 131, 54, 61], [19, 132, 54, 62, "default"], [19, 139, 55, 2], [19, 140, 55, 3], [20, 4, 57, 2], [20, 8, 57, 2, "polyfillGlobal"], [20, 41, 57, 16], [20, 43, 58, 4], [20, 58, 58, 19], [20, 60, 59, 4], [20, 66, 59, 10, "require"], [20, 73, 59, 17], [20, 74, 59, 17, "_dependencyMap"], [20, 88, 59, 17], [20, 137, 59, 62], [20, 138, 59, 63], [20, 139, 59, 64, "default"], [20, 146, 60, 2], [20, 147, 60, 3], [21, 4, 62, 2], [21, 8, 62, 2, "polyfillGlobal"], [21, 41, 62, 16], [21, 43, 63, 4], [21, 49, 63, 10], [21, 51, 64, 4], [21, 57, 64, 10, "require"], [21, 64, 64, 17], [21, 65, 64, 17, "_dependencyMap"], [21, 79, 64, 17], [21, 119, 64, 53], [21, 120, 64, 54], [21, 121, 64, 55, "default"], [21, 128, 65, 2], [21, 129, 65, 3], [22, 4, 67, 2], [22, 8, 67, 2, "polyfillGlobal"], [22, 41, 67, 16], [22, 43, 68, 4], [22, 52, 68, 13], [22, 54, 69, 4], [22, 60, 69, 10, "require"], [22, 67, 69, 17], [22, 68, 69, 17, "_dependencyMap"], [22, 82, 69, 17], [22, 126, 69, 56], [22, 127, 69, 57], [22, 128, 69, 58, "default"], [22, 135, 70, 2], [22, 136, 70, 3], [23, 4, 72, 2], [23, 8, 72, 2, "polyfillGlobal"], [23, 41, 72, 16], [23, 43, 73, 4], [23, 56, 73, 17], [23, 58, 74, 4], [23, 64, 74, 10, "require"], [23, 71, 74, 17], [23, 72, 74, 17, "_dependencyMap"], [23, 86, 74, 17], [23, 133, 74, 59], [23, 134, 74, 60], [23, 135, 74, 61, "default"], [23, 142, 75, 2], [23, 143, 75, 3], [24, 2, 76, 0], [25, 0, 76, 1], [25, 3]], "functionMap": {"names": ["<global>", "setUpDOM", "polyfillGlobal$argument_1"], "mappings": "AAA;eCc;ICS,oDD;ICK,4DD;ICK,wDD;ICK,0ED;ICK,oED;ICK,0DD;ICK,iED;ICK,mED;ICK,0DD;ICK,6DD;ICK,gED"}}, "type": "js/module"}]}