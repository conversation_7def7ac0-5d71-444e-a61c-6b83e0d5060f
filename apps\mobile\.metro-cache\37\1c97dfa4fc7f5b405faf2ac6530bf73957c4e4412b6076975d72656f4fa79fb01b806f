{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 16, "index": 126}, "end": {"line": 4, "column": 32, "index": 142}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "use-sync-external-store/shim", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 15, "index": 159}, "end": {"line": 5, "column": 54, "index": 198}}], "key": "aKGLQ73LwOEJPCL0G86fBp+zBKQ=", "exportNames": ["*"]}}, {"name": "./style-sheet", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 22, "index": 222}, "end": {"line": 6, "column": 46, "index": 246}}], "key": "/bYi8u7M4Q4+2ZvRGwVsQ8kBQ3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useColorScheme = void 0;\n  var react_1 = require(_dependencyMap[0], \"react\");\n  var shim_1 = require(_dependencyMap[1], \"use-sync-external-store/shim\");\n  var style_sheet_1 = require(_dependencyMap[2], \"./style-sheet\");\n  function useColorScheme() {\n    var store = (0, react_1.useContext)(style_sheet_1.StoreContext);\n    var colorScheme = (0, shim_1.useSyncExternalStore)(store.subscribeColorScheme, store.getColorScheme);\n    return {\n      colorScheme,\n      setColorScheme: store.setColorScheme,\n      toggleColorScheme: store.toggleColorScheme\n    };\n  }\n  exports.useColorScheme = useColorScheme;\n});", "lineCount": 21, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "useColorScheme"], [7, 24, 3, 22], [7, 27, 3, 25], [7, 32, 3, 30], [7, 33, 3, 31], [8, 2, 4, 0], [8, 6, 4, 6, "react_1"], [8, 13, 4, 13], [8, 16, 4, 16, "require"], [8, 23, 4, 23], [8, 24, 4, 23, "_dependencyMap"], [8, 38, 4, 23], [8, 50, 4, 31], [8, 51, 4, 32], [9, 2, 5, 0], [9, 6, 5, 6, "shim_1"], [9, 12, 5, 12], [9, 15, 5, 15, "require"], [9, 22, 5, 22], [9, 23, 5, 22, "_dependencyMap"], [9, 37, 5, 22], [9, 72, 5, 53], [9, 73, 5, 54], [10, 2, 6, 0], [10, 6, 6, 6, "style_sheet_1"], [10, 19, 6, 19], [10, 22, 6, 22, "require"], [10, 29, 6, 29], [10, 30, 6, 29, "_dependencyMap"], [10, 44, 6, 29], [10, 64, 6, 45], [10, 65, 6, 46], [11, 2, 7, 0], [11, 11, 7, 9, "useColorScheme"], [11, 25, 7, 23, "useColorScheme"], [11, 26, 7, 23], [11, 28, 7, 26], [12, 4, 8, 4], [12, 8, 8, 10, "store"], [12, 13, 8, 15], [12, 16, 8, 18], [12, 17, 8, 19], [12, 18, 8, 20], [12, 20, 8, 22, "react_1"], [12, 27, 8, 29], [12, 28, 8, 30, "useContext"], [12, 38, 8, 40], [12, 40, 8, 42, "style_sheet_1"], [12, 53, 8, 55], [12, 54, 8, 56, "StoreContext"], [12, 66, 8, 68], [12, 67, 8, 69], [13, 4, 9, 4], [13, 8, 9, 10, "colorScheme"], [13, 19, 9, 21], [13, 22, 9, 24], [13, 23, 9, 25], [13, 24, 9, 26], [13, 26, 9, 28, "shim_1"], [13, 32, 9, 34], [13, 33, 9, 35, "useSyncExternalStore"], [13, 53, 9, 55], [13, 55, 9, 57, "store"], [13, 60, 9, 62], [13, 61, 9, 63, "subscribeColorScheme"], [13, 81, 9, 83], [13, 83, 9, 85, "store"], [13, 88, 9, 90], [13, 89, 9, 91, "getColorScheme"], [13, 103, 9, 105], [13, 104, 9, 106], [14, 4, 10, 4], [14, 11, 10, 11], [15, 6, 11, 8, "colorScheme"], [15, 17, 11, 19], [16, 6, 12, 8, "setColorScheme"], [16, 20, 12, 22], [16, 22, 12, 24, "store"], [16, 27, 12, 29], [16, 28, 12, 30, "setColorScheme"], [16, 42, 12, 44], [17, 6, 13, 8, "toggleColorScheme"], [17, 23, 13, 25], [17, 25, 13, 27, "store"], [17, 30, 13, 32], [17, 31, 13, 33, "toggleColorScheme"], [18, 4, 14, 4], [18, 5, 14, 5], [19, 2, 15, 0], [20, 2, 16, 0, "exports"], [20, 9, 16, 7], [20, 10, 16, 8, "useColorScheme"], [20, 24, 16, 22], [20, 27, 16, 25, "useColorScheme"], [20, 41, 16, 39], [21, 0, 16, 40], [21, 3]], "functionMap": {"names": ["<global>", "useColorScheme"], "mappings": "AAA;ACM;CDQ"}}, "type": "js/module"}]}