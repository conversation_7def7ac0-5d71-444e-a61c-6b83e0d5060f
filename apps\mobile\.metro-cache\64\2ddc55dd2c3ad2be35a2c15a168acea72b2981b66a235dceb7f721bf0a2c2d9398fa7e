{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.clearImmediate = clearImmediate;\n  exports.setImmediate = setImmediate;\n  var GUIID = 1;\n  var clearedImmediates = new Set();\n  function setImmediate(callback) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (arguments.length < 1) {\n      throw new TypeError('setImmediate must be called with at least one argument (a function to call)');\n    }\n    if (typeof callback !== 'function') {\n      throw new TypeError('The first argument to setImmediate must be a function.');\n    }\n    var id = GUIID++;\n    if (clearedImmediates.has(id)) {\n      clearedImmediates.delete(id);\n    }\n    global.queueMicrotask(() => {\n      if (!clearedImmediates.has(id)) {\n        callback.apply(undefined, args);\n      } else {\n        clearedImmediates.delete(id);\n      }\n    });\n    return id;\n  }\n  function clearImmediate(immediateID) {\n    clearedImmediates.add(immediateID);\n  }\n});", "lineCount": 37, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "clearImmediate"], [7, 24, 11, 13], [7, 27, 11, 13, "clearImmediate"], [7, 41, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "setImmediate"], [8, 22, 11, 13], [8, 25, 11, 13, "setImmediate"], [8, 37, 11, 13], [9, 2, 14, 0], [9, 6, 14, 4, "GUIID"], [9, 11, 14, 9], [9, 14, 14, 12], [9, 15, 14, 13], [10, 2, 17, 0], [10, 6, 17, 6, "clearedImmediates"], [10, 23, 17, 36], [10, 26, 17, 39], [10, 30, 17, 43, "Set"], [10, 33, 17, 46], [10, 34, 17, 47], [10, 35, 17, 48], [11, 2, 24, 7], [11, 11, 24, 16, "setImmediate"], [11, 23, 24, 28, "setImmediate"], [11, 24, 24, 29, "callback"], [11, 32, 24, 47], [11, 34, 24, 71], [12, 4, 24, 71], [12, 13, 24, 71, "_len"], [12, 17, 24, 71], [12, 20, 24, 71, "arguments"], [12, 29, 24, 71], [12, 30, 24, 71, "length"], [12, 36, 24, 71], [12, 38, 24, 52, "args"], [12, 42, 24, 56], [12, 49, 24, 56, "Array"], [12, 54, 24, 56], [12, 55, 24, 56, "_len"], [12, 59, 24, 56], [12, 66, 24, 56, "_len"], [12, 70, 24, 56], [12, 81, 24, 56, "_key"], [12, 85, 24, 56], [12, 91, 24, 56, "_key"], [12, 95, 24, 56], [12, 98, 24, 56, "_len"], [12, 102, 24, 56], [12, 104, 24, 56, "_key"], [12, 108, 24, 56], [13, 6, 24, 52, "args"], [13, 10, 24, 56], [13, 11, 24, 56, "_key"], [13, 15, 24, 56], [13, 23, 24, 56, "arguments"], [13, 32, 24, 56], [13, 33, 24, 56, "_key"], [13, 37, 24, 56], [14, 4, 24, 56], [15, 4, 25, 2], [15, 8, 25, 6, "arguments"], [15, 17, 25, 15], [15, 18, 25, 16, "length"], [15, 24, 25, 22], [15, 27, 25, 25], [15, 28, 25, 26], [15, 30, 25, 28], [16, 6, 26, 4], [16, 12, 26, 10], [16, 16, 26, 14, "TypeError"], [16, 25, 26, 23], [16, 26, 27, 6], [16, 103, 28, 4], [16, 104, 28, 5], [17, 4, 29, 2], [18, 4, 30, 2], [18, 8, 30, 6], [18, 15, 30, 13, "callback"], [18, 23, 30, 21], [18, 28, 30, 26], [18, 38, 30, 36], [18, 40, 30, 38], [19, 6, 31, 4], [19, 12, 31, 10], [19, 16, 31, 14, "TypeError"], [19, 25, 31, 23], [19, 26, 32, 6], [19, 82, 33, 4], [19, 83, 33, 5], [20, 4, 34, 2], [21, 4, 36, 2], [21, 8, 36, 8, "id"], [21, 10, 36, 10], [21, 13, 36, 13, "GUIID"], [21, 18, 36, 18], [21, 20, 36, 20], [22, 4, 39, 2], [22, 8, 39, 6, "clearedImmediates"], [22, 25, 39, 23], [22, 26, 39, 24, "has"], [22, 29, 39, 27], [22, 30, 39, 28, "id"], [22, 32, 39, 30], [22, 33, 39, 31], [22, 35, 39, 33], [23, 6, 40, 4, "clearedImmediates"], [23, 23, 40, 21], [23, 24, 40, 22, "delete"], [23, 30, 40, 28], [23, 31, 40, 29, "id"], [23, 33, 40, 31], [23, 34, 40, 32], [24, 4, 41, 2], [25, 4, 44, 2, "global"], [25, 10, 44, 8], [25, 11, 44, 9, "queueMicrotask"], [25, 25, 44, 23], [25, 26, 44, 24], [25, 32, 44, 30], [26, 6, 45, 4], [26, 10, 45, 8], [26, 11, 45, 9, "clearedImmediates"], [26, 28, 45, 26], [26, 29, 45, 27, "has"], [26, 32, 45, 30], [26, 33, 45, 31, "id"], [26, 35, 45, 33], [26, 36, 45, 34], [26, 38, 45, 36], [27, 8, 46, 6, "callback"], [27, 16, 46, 14], [27, 17, 46, 15, "apply"], [27, 22, 46, 20], [27, 23, 46, 21, "undefined"], [27, 32, 46, 30], [27, 34, 46, 32, "args"], [27, 38, 46, 36], [27, 39, 46, 37], [28, 6, 47, 4], [28, 7, 47, 5], [28, 13, 47, 11], [29, 8, 49, 6, "clearedImmediates"], [29, 25, 49, 23], [29, 26, 49, 24, "delete"], [29, 32, 49, 30], [29, 33, 49, 31, "id"], [29, 35, 49, 33], [29, 36, 49, 34], [30, 6, 50, 4], [31, 4, 51, 2], [31, 5, 51, 3], [31, 6, 51, 4], [32, 4, 53, 2], [32, 11, 53, 9, "id"], [32, 13, 53, 11], [33, 2, 54, 0], [34, 2, 59, 7], [34, 11, 59, 16, "clearImmediate"], [34, 25, 59, 30, "clearImmediate"], [34, 26, 59, 31, "immediateID"], [34, 37, 59, 50], [34, 39, 59, 52], [35, 4, 60, 2, "clearedImmediates"], [35, 21, 60, 19], [35, 22, 60, 20, "add"], [35, 25, 60, 23], [35, 26, 60, 24, "immediateID"], [35, 37, 60, 35], [35, 38, 60, 36], [36, 2, 61, 0], [37, 0, 61, 1], [37, 3]], "functionMap": {"names": ["<global>", "setImmediate", "global.queueMicrotask$argument_0", "clearImmediate"], "mappings": "AAA;OCuB;wBCoB;GDO;CDG;OGK"}}, "type": "js/module"}]}