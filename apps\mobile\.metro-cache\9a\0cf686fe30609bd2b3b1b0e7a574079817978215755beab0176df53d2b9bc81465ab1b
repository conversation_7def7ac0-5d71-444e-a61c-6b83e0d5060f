{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "../../dom/events/Event", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 43}}], "key": "DDe09b6G/xKMPeTxmtlFrrA0ORw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _Event2 = _interopRequireDefault(require(_dependencyMap[6], \"../../dom/events/Event\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var MessageEvent = exports.default = /*#__PURE__*/function (_Event) {\n    function MessageEvent(type, options) {\n      var _this;\n      (0, _classCallCheck2.default)(this, MessageEvent);\n      _this = _callSuper(this, MessageEvent, [type, options]);\n      _this._data = options?.data;\n      _this._origin = String(options?.origin ?? '');\n      _this._lastEventId = String(options?.lastEventId ?? '');\n      return _this;\n    }\n    (0, _inherits2.default)(MessageEvent, _Event);\n    return (0, _createClass2.default)(MessageEvent, [{\n      key: \"data\",\n      get: function () {\n        return this._data;\n      }\n    }, {\n      key: \"origin\",\n      get: function () {\n        return this._origin;\n      }\n    }, {\n      key: \"lastEventId\",\n      get: function () {\n        return this._lastEventId;\n      }\n    }]);\n  }(_Event2.default);\n});", "lineCount": 43, "map": [[12, 2, 20, 0], [12, 6, 20, 0, "_Event2"], [12, 13, 20, 0], [12, 16, 20, 0, "_interopRequireDefault"], [12, 38, 20, 0], [12, 39, 20, 0, "require"], [12, 46, 20, 0], [12, 47, 20, 0, "_dependencyMap"], [12, 61, 20, 0], [13, 2, 20, 43], [13, 11, 20, 43, "_callSuper"], [13, 22, 20, 43, "t"], [13, 23, 20, 43], [13, 25, 20, 43, "o"], [13, 26, 20, 43], [13, 28, 20, 43, "e"], [13, 29, 20, 43], [13, 40, 20, 43, "o"], [13, 41, 20, 43], [13, 48, 20, 43, "_getPrototypeOf2"], [13, 64, 20, 43], [13, 65, 20, 43, "default"], [13, 72, 20, 43], [13, 74, 20, 43, "o"], [13, 75, 20, 43], [13, 82, 20, 43, "_possibleConstructorReturn2"], [13, 109, 20, 43], [13, 110, 20, 43, "default"], [13, 117, 20, 43], [13, 119, 20, 43, "t"], [13, 120, 20, 43], [13, 122, 20, 43, "_isNativeReflectConstruct"], [13, 147, 20, 43], [13, 152, 20, 43, "Reflect"], [13, 159, 20, 43], [13, 160, 20, 43, "construct"], [13, 169, 20, 43], [13, 170, 20, 43, "o"], [13, 171, 20, 43], [13, 173, 20, 43, "e"], [13, 174, 20, 43], [13, 186, 20, 43, "_getPrototypeOf2"], [13, 202, 20, 43], [13, 203, 20, 43, "default"], [13, 210, 20, 43], [13, 212, 20, 43, "t"], [13, 213, 20, 43], [13, 215, 20, 43, "constructor"], [13, 226, 20, 43], [13, 230, 20, 43, "o"], [13, 231, 20, 43], [13, 232, 20, 43, "apply"], [13, 237, 20, 43], [13, 238, 20, 43, "t"], [13, 239, 20, 43], [13, 241, 20, 43, "e"], [13, 242, 20, 43], [14, 2, 20, 43], [14, 11, 20, 43, "_isNativeReflectConstruct"], [14, 37, 20, 43], [14, 51, 20, 43, "t"], [14, 52, 20, 43], [14, 56, 20, 43, "Boolean"], [14, 63, 20, 43], [14, 64, 20, 43, "prototype"], [14, 73, 20, 43], [14, 74, 20, 43, "valueOf"], [14, 81, 20, 43], [14, 82, 20, 43, "call"], [14, 86, 20, 43], [14, 87, 20, 43, "Reflect"], [14, 94, 20, 43], [14, 95, 20, 43, "construct"], [14, 104, 20, 43], [14, 105, 20, 43, "Boolean"], [14, 112, 20, 43], [14, 145, 20, 43, "t"], [14, 146, 20, 43], [14, 159, 20, 43, "_isNativeReflectConstruct"], [14, 184, 20, 43], [14, 196, 20, 43, "_isNativeReflectConstruct"], [14, 197, 20, 43], [14, 210, 20, 43, "t"], [14, 211, 20, 43], [15, 2, 20, 43], [15, 6, 32, 21, "MessageEvent"], [15, 18, 32, 33], [15, 21, 32, 33, "exports"], [15, 28, 32, 33], [15, 29, 32, 33, "default"], [15, 36, 32, 33], [15, 62, 32, 33, "_Event"], [15, 68, 32, 33], [16, 4, 37, 2], [16, 13, 37, 2, "MessageEvent"], [16, 26, 37, 14, "type"], [16, 30, 37, 26], [16, 32, 37, 28, "options"], [16, 39, 37, 55], [16, 41, 37, 57], [17, 6, 37, 57], [17, 10, 37, 57, "_this"], [17, 15, 37, 57], [18, 6, 37, 57], [18, 10, 37, 57, "_classCallCheck2"], [18, 26, 37, 57], [18, 27, 37, 57, "default"], [18, 34, 37, 57], [18, 42, 37, 57, "MessageEvent"], [18, 54, 37, 57], [19, 6, 38, 4, "_this"], [19, 11, 38, 4], [19, 14, 38, 4, "_callSuper"], [19, 24, 38, 4], [19, 31, 38, 4, "MessageEvent"], [19, 43, 38, 4], [19, 46, 38, 10, "type"], [19, 50, 38, 14], [19, 52, 38, 16, "options"], [19, 59, 38, 23], [20, 6, 40, 4, "_this"], [20, 11, 40, 4], [20, 12, 40, 9, "_data"], [20, 17, 40, 14], [20, 20, 40, 17, "options"], [20, 27, 40, 24], [20, 29, 40, 26, "data"], [20, 33, 40, 30], [21, 6, 41, 4, "_this"], [21, 11, 41, 4], [21, 12, 41, 9, "_origin"], [21, 19, 41, 16], [21, 22, 41, 19, "String"], [21, 28, 41, 25], [21, 29, 41, 26, "options"], [21, 36, 41, 33], [21, 38, 41, 35, "origin"], [21, 44, 41, 41], [21, 48, 41, 45], [21, 50, 41, 47], [21, 51, 41, 48], [22, 6, 42, 4, "_this"], [22, 11, 42, 4], [22, 12, 42, 9, "_lastEventId"], [22, 24, 42, 21], [22, 27, 42, 24, "String"], [22, 33, 42, 30], [22, 34, 42, 31, "options"], [22, 41, 42, 38], [22, 43, 42, 40, "lastEventId"], [22, 54, 42, 51], [22, 58, 42, 55], [22, 60, 42, 57], [22, 61, 42, 58], [23, 6, 42, 59], [23, 13, 42, 59, "_this"], [23, 18, 42, 59], [24, 4, 43, 2], [25, 4, 43, 3], [25, 8, 43, 3, "_inherits2"], [25, 18, 43, 3], [25, 19, 43, 3, "default"], [25, 26, 43, 3], [25, 28, 43, 3, "MessageEvent"], [25, 40, 43, 3], [25, 42, 43, 3, "_Event"], [25, 48, 43, 3], [26, 4, 43, 3], [26, 15, 43, 3, "_createClass2"], [26, 28, 43, 3], [26, 29, 43, 3, "default"], [26, 36, 43, 3], [26, 38, 43, 3, "MessageEvent"], [26, 50, 43, 3], [27, 6, 43, 3, "key"], [27, 9, 43, 3], [28, 6, 43, 3, "get"], [28, 9, 43, 3], [28, 11, 45, 2], [28, 20, 45, 2, "get"], [28, 21, 45, 2], [28, 23, 45, 20], [29, 8, 46, 4], [29, 15, 46, 11], [29, 19, 46, 15], [29, 20, 46, 16, "_data"], [29, 25, 46, 21], [30, 6, 47, 2], [31, 4, 47, 3], [32, 6, 47, 3, "key"], [32, 9, 47, 3], [33, 6, 47, 3, "get"], [33, 9, 47, 3], [33, 11, 49, 2], [33, 20, 49, 2, "get"], [33, 21, 49, 2], [33, 23, 49, 23], [34, 8, 50, 4], [34, 15, 50, 11], [34, 19, 50, 15], [34, 20, 50, 16, "_origin"], [34, 27, 50, 23], [35, 6, 51, 2], [36, 4, 51, 3], [37, 6, 51, 3, "key"], [37, 9, 51, 3], [38, 6, 51, 3, "get"], [38, 9, 51, 3], [38, 11, 53, 2], [38, 20, 53, 2, "get"], [38, 21, 53, 2], [38, 23, 53, 28], [39, 8, 54, 4], [39, 15, 54, 11], [39, 19, 54, 15], [39, 20, 54, 16, "_lastEventId"], [39, 32, 54, 28], [40, 6, 55, 2], [41, 4, 55, 3], [42, 2, 55, 3], [42, 4, 32, 42, "Event"], [42, 19, 32, 47], [43, 0, 32, 47], [43, 3]], "functionMap": {"names": ["<global>", "MessageEvent", "constructor", "get__data", "get__origin", "get__lastEventId"], "mappings": "AAA;eC+B;ECK;GDM;EEE;GFE;EGE;GHE;EIE;GJE"}}, "type": "js/module"}]}