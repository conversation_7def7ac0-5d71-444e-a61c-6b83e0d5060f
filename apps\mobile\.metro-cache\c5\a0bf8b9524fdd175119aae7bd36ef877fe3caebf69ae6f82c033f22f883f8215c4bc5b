{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 52}, "end": {"line": 5, "column": 65, "index": 117}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  // Copyright © 2024 650 Industries.\n\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createPermissionHook = createPermissionHook;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = require(_dependencyMap[4], \"react\");\n  var _excluded = [\"get\", \"request\"];\n  // These types are identical, but improves the readability for suggestions in editors\n\n  /**\n   * Get or request permission for protected functionality within the app.\n   * It uses separate permission requesters to interact with a single permission.\n   * By default, the hook will only retrieve the permission status.\n   */\n  function usePermission(methods, options) {\n    var isMounted = (0, _react.useRef)(true);\n    var _useState = (0, _react.useState)(null),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      status = _useState2[0],\n      setStatus = _useState2[1];\n    var _ref = options || {},\n      _ref$get = _ref.get,\n      get = _ref$get === void 0 ? true : _ref$get,\n      _ref$request = _ref.request,\n      request = _ref$request === void 0 ? false : _ref$request,\n      permissionOptions = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var getPermission = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {\n      var response = yield methods.getMethod(Object.keys(permissionOptions).length > 0 ? permissionOptions : undefined);\n      if (isMounted.current) setStatus(response);\n      return response;\n    }), [methods.getMethod]);\n    var requestPermission = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {\n      var response = yield methods.requestMethod(Object.keys(permissionOptions).length > 0 ? permissionOptions : undefined);\n      if (isMounted.current) setStatus(response);\n      return response;\n    }), [methods.requestMethod]);\n    (0, _react.useEffect)(function runMethods() {\n      if (request) requestPermission();\n      if (!request && get) getPermission();\n    }, [get, request, requestPermission, getPermission]);\n\n    // Workaround for unmounting components receiving state updates\n    (0, _react.useEffect)(function didMount() {\n      isMounted.current = true;\n      return () => {\n        isMounted.current = false;\n      };\n    }, []);\n    return [status, requestPermission, getPermission];\n  }\n\n  /**\n   * Create a new permission hook with the permission methods built-in.\n   * This can be used to quickly create specific permission hooks in every module.\n   */\n  function createPermissionHook(methods) {\n    return options => usePermission(methods, options);\n  }\n});", "lineCount": 67, "map": [[2, 2, 1, 0], [4, 2, 3, 0], [4, 14, 3, 12], [6, 2, 3, 13], [6, 6, 3, 13, "_interopRequireDefault"], [6, 28, 3, 13], [6, 31, 3, 13, "require"], [6, 38, 3, 13], [6, 39, 3, 13, "_dependencyMap"], [6, 53, 3, 13], [7, 2, 3, 13, "Object"], [7, 8, 3, 13], [7, 9, 3, 13, "defineProperty"], [7, 23, 3, 13], [7, 24, 3, 13, "exports"], [7, 31, 3, 13], [8, 4, 3, 13, "value"], [8, 9, 3, 13], [9, 2, 3, 13], [10, 2, 3, 13, "exports"], [10, 9, 3, 13], [10, 10, 3, 13, "createPermissionHook"], [10, 30, 3, 13], [10, 33, 3, 13, "createPermissionHook"], [10, 53, 3, 13], [11, 2, 3, 13], [11, 6, 3, 13, "_asyncToGenerator2"], [11, 24, 3, 13], [11, 27, 3, 13, "_interopRequireDefault"], [11, 49, 3, 13], [11, 50, 3, 13, "require"], [11, 57, 3, 13], [11, 58, 3, 13, "_dependencyMap"], [11, 72, 3, 13], [12, 2, 3, 13], [12, 6, 3, 13, "_objectWithoutProperties2"], [12, 31, 3, 13], [12, 34, 3, 13, "_interopRequireDefault"], [12, 56, 3, 13], [12, 57, 3, 13, "require"], [12, 64, 3, 13], [12, 65, 3, 13, "_dependencyMap"], [12, 79, 3, 13], [13, 2, 3, 13], [13, 6, 3, 13, "_slicedToArray2"], [13, 21, 3, 13], [13, 24, 3, 13, "_interopRequireDefault"], [13, 46, 3, 13], [13, 47, 3, 13, "require"], [13, 54, 3, 13], [13, 55, 3, 13, "_dependencyMap"], [13, 69, 3, 13], [14, 2, 5, 0], [14, 6, 5, 0, "_react"], [14, 12, 5, 0], [14, 15, 5, 0, "require"], [14, 22, 5, 0], [14, 23, 5, 0, "_dependencyMap"], [14, 37, 5, 0], [15, 2, 5, 65], [15, 6, 5, 65, "_excluded"], [15, 15, 5, 65], [16, 2, 9, 0], [18, 2, 29, 0], [19, 0, 30, 0], [20, 0, 31, 0], [21, 0, 32, 0], [22, 0, 33, 0], [23, 2, 34, 0], [23, 11, 34, 9, "usePermission"], [23, 24, 34, 22, "usePermission"], [23, 25, 35, 2, "methods"], [23, 32, 35, 53], [23, 34, 36, 2, "options"], [23, 41, 36, 42], [23, 43, 37, 93], [24, 4, 38, 2], [24, 8, 38, 8, "isMounted"], [24, 17, 38, 17], [24, 20, 38, 20], [24, 24, 38, 20, "useRef"], [24, 37, 38, 26], [24, 39, 38, 27], [24, 43, 38, 31], [24, 44, 38, 32], [25, 4, 39, 2], [25, 8, 39, 2, "_useState"], [25, 17, 39, 2], [25, 20, 39, 30], [25, 24, 39, 30, "useState"], [25, 39, 39, 38], [25, 41, 39, 58], [25, 45, 39, 62], [25, 46, 39, 63], [26, 6, 39, 63, "_useState2"], [26, 16, 39, 63], [26, 23, 39, 63, "_slicedToArray2"], [26, 38, 39, 63], [26, 39, 39, 63, "default"], [26, 46, 39, 63], [26, 48, 39, 63, "_useState"], [26, 57, 39, 63], [27, 6, 39, 9, "status"], [27, 12, 39, 15], [27, 15, 39, 15, "_useState2"], [27, 25, 39, 15], [28, 6, 39, 17, "setStatus"], [28, 15, 39, 26], [28, 18, 39, 26, "_useState2"], [28, 28, 39, 26], [29, 4, 40, 2], [29, 8, 40, 2, "_ref"], [29, 12, 40, 2], [29, 15, 40, 64, "options"], [29, 22, 40, 71], [29, 26, 40, 75], [29, 27, 40, 76], [29, 28, 40, 77], [30, 6, 40, 77, "_ref$get"], [30, 14, 40, 77], [30, 17, 40, 77, "_ref"], [30, 21, 40, 77], [30, 22, 40, 10, "get"], [30, 25, 40, 13], [31, 6, 40, 10, "get"], [31, 9, 40, 13], [31, 12, 40, 13, "_ref$get"], [31, 20, 40, 13], [31, 34, 40, 16], [31, 38, 40, 20], [31, 41, 40, 20, "_ref$get"], [31, 49, 40, 20], [32, 6, 40, 20, "_ref$request"], [32, 18, 40, 20], [32, 21, 40, 20, "_ref"], [32, 25, 40, 20], [32, 26, 40, 22, "request"], [32, 33, 40, 29], [33, 6, 40, 22, "request"], [33, 13, 40, 29], [33, 16, 40, 29, "_ref$request"], [33, 28, 40, 29], [33, 42, 40, 32], [33, 47, 40, 37], [33, 50, 40, 37, "_ref$request"], [33, 62, 40, 37], [34, 6, 40, 42, "permissionOptions"], [34, 23, 40, 59], [34, 30, 40, 59, "_objectWithoutProperties2"], [34, 55, 40, 59], [34, 56, 40, 59, "default"], [34, 63, 40, 59], [34, 65, 40, 59, "_ref"], [34, 69, 40, 59], [34, 71, 40, 59, "_excluded"], [34, 80, 40, 59], [35, 4, 42, 2], [35, 8, 42, 8, "getPermission"], [35, 21, 42, 21], [35, 24, 42, 24], [35, 28, 42, 24, "useCallback"], [35, 46, 42, 35], [35, 65, 42, 35, "_asyncToGenerator2"], [35, 83, 42, 35], [35, 84, 42, 35, "default"], [35, 91, 42, 35], [35, 93, 42, 36], [35, 106, 42, 48], [36, 6, 43, 4], [36, 10, 43, 10, "response"], [36, 18, 43, 18], [36, 27, 43, 27, "methods"], [36, 34, 43, 34], [36, 35, 43, 35, "getMethod"], [36, 44, 43, 44], [36, 45, 44, 6, "Object"], [36, 51, 44, 12], [36, 52, 44, 13, "keys"], [36, 56, 44, 17], [36, 57, 44, 18, "permissionOptions"], [36, 74, 44, 35], [36, 75, 44, 36], [36, 76, 44, 37, "length"], [36, 82, 44, 43], [36, 85, 44, 46], [36, 86, 44, 47], [36, 89, 44, 51, "permissionOptions"], [36, 106, 44, 68], [36, 109, 44, 83, "undefined"], [36, 118, 45, 4], [36, 119, 45, 5], [37, 6, 46, 4], [37, 10, 46, 8, "isMounted"], [37, 19, 46, 17], [37, 20, 46, 18, "current"], [37, 27, 46, 25], [37, 29, 46, 27, "setStatus"], [37, 38, 46, 36], [37, 39, 46, 37, "response"], [37, 47, 46, 45], [37, 48, 46, 46], [38, 6, 47, 4], [38, 13, 47, 11, "response"], [38, 21, 47, 19], [39, 4, 48, 2], [39, 5, 48, 3], [39, 8, 48, 5], [39, 9, 48, 6, "methods"], [39, 16, 48, 13], [39, 17, 48, 14, "getMethod"], [39, 26, 48, 23], [39, 27, 48, 24], [39, 28, 48, 25], [40, 4, 50, 2], [40, 8, 50, 8, "requestPermission"], [40, 25, 50, 25], [40, 28, 50, 28], [40, 32, 50, 28, "useCallback"], [40, 50, 50, 39], [40, 69, 50, 39, "_asyncToGenerator2"], [40, 87, 50, 39], [40, 88, 50, 39, "default"], [40, 95, 50, 39], [40, 97, 50, 40], [40, 110, 50, 52], [41, 6, 51, 4], [41, 10, 51, 10, "response"], [41, 18, 51, 18], [41, 27, 51, 27, "methods"], [41, 34, 51, 34], [41, 35, 51, 35, "requestMethod"], [41, 48, 51, 48], [41, 49, 52, 6, "Object"], [41, 55, 52, 12], [41, 56, 52, 13, "keys"], [41, 60, 52, 17], [41, 61, 52, 18, "permissionOptions"], [41, 78, 52, 35], [41, 79, 52, 36], [41, 80, 52, 37, "length"], [41, 86, 52, 43], [41, 89, 52, 46], [41, 90, 52, 47], [41, 93, 52, 51, "permissionOptions"], [41, 110, 52, 68], [41, 113, 52, 83, "undefined"], [41, 122, 53, 4], [41, 123, 53, 5], [42, 6, 54, 4], [42, 10, 54, 8, "isMounted"], [42, 19, 54, 17], [42, 20, 54, 18, "current"], [42, 27, 54, 25], [42, 29, 54, 27, "setStatus"], [42, 38, 54, 36], [42, 39, 54, 37, "response"], [42, 47, 54, 45], [42, 48, 54, 46], [43, 6, 55, 4], [43, 13, 55, 11, "response"], [43, 21, 55, 19], [44, 4, 56, 2], [44, 5, 56, 3], [44, 8, 56, 5], [44, 9, 56, 6, "methods"], [44, 16, 56, 13], [44, 17, 56, 14, "requestMethod"], [44, 30, 56, 27], [44, 31, 56, 28], [44, 32, 56, 29], [45, 4, 58, 2], [45, 8, 58, 2, "useEffect"], [45, 24, 58, 11], [45, 26, 59, 4], [45, 35, 59, 13, "runMethods"], [45, 45, 59, 23, "runMethods"], [45, 46, 59, 23], [45, 48, 59, 26], [46, 6, 60, 6], [46, 10, 60, 10, "request"], [46, 17, 60, 17], [46, 19, 60, 19, "requestPermission"], [46, 36, 60, 36], [46, 37, 60, 37], [46, 38, 60, 38], [47, 6, 61, 6], [47, 10, 61, 10], [47, 11, 61, 11, "request"], [47, 18, 61, 18], [47, 22, 61, 22, "get"], [47, 25, 61, 25], [47, 27, 61, 27, "getPermission"], [47, 40, 61, 40], [47, 41, 61, 41], [47, 42, 61, 42], [48, 4, 62, 4], [48, 5, 62, 5], [48, 7, 63, 4], [48, 8, 63, 5, "get"], [48, 11, 63, 8], [48, 13, 63, 10, "request"], [48, 20, 63, 17], [48, 22, 63, 19, "requestPermission"], [48, 39, 63, 36], [48, 41, 63, 38, "getPermission"], [48, 54, 63, 51], [48, 55, 64, 2], [48, 56, 64, 3], [50, 4, 66, 2], [51, 4, 67, 2], [51, 8, 67, 2, "useEffect"], [51, 24, 67, 11], [51, 26, 67, 12], [51, 35, 67, 21, "didMount"], [51, 43, 67, 29, "didMount"], [51, 44, 67, 29], [51, 46, 67, 32], [52, 6, 68, 4, "isMounted"], [52, 15, 68, 13], [52, 16, 68, 14, "current"], [52, 23, 68, 21], [52, 26, 68, 24], [52, 30, 68, 28], [53, 6, 69, 4], [53, 13, 69, 11], [53, 19, 69, 17], [54, 8, 70, 6, "isMounted"], [54, 17, 70, 15], [54, 18, 70, 16, "current"], [54, 25, 70, 23], [54, 28, 70, 26], [54, 33, 70, 31], [55, 6, 71, 4], [55, 7, 71, 5], [56, 4, 72, 2], [56, 5, 72, 3], [56, 7, 72, 5], [56, 9, 72, 7], [56, 10, 72, 8], [57, 4, 74, 2], [57, 11, 74, 9], [57, 12, 74, 10, "status"], [57, 18, 74, 16], [57, 20, 74, 18, "requestPermission"], [57, 37, 74, 35], [57, 39, 74, 37, "getPermission"], [57, 52, 74, 50], [57, 53, 74, 51], [58, 2, 75, 0], [60, 2, 77, 0], [61, 0, 78, 0], [62, 0, 79, 0], [63, 0, 80, 0], [64, 2, 81, 7], [64, 11, 81, 16, "createPermissionHook"], [64, 31, 81, 36, "createPermissionHook"], [64, 32, 82, 2, "methods"], [64, 39, 82, 53], [64, 41, 83, 2], [65, 4, 84, 2], [65, 11, 84, 10, "options"], [65, 18, 84, 50], [65, 22, 85, 4, "usePermission"], [65, 35, 85, 17], [65, 36, 85, 39, "methods"], [65, 43, 85, 46], [65, 45, 85, 48, "options"], [65, 52, 85, 55], [65, 53, 85, 56], [66, 2, 86, 0], [67, 0, 86, 1], [67, 3]], "functionMap": {"names": ["<global>", "usePermission", "getPermission", "requestPermission", "runMethods", "didMount", "<anonymous>", "createPermissionHook"], "mappings": "AAA;ACiC;oCCQ;GDM;wCEE;GFM;IGG;KHG;YIK;WCE;KDE;GJC;CDG;OOM;SDG;wDCC;CPC"}}, "type": "js/module"}]}