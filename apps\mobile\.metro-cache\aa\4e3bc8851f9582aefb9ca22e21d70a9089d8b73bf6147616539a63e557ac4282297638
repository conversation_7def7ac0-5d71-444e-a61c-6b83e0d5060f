{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ScreenTransition = void 0;\n  var _worklet_5397481334330_init_data = {\n    code: \"function presetsTs1(event){return{transform:[{translateX:event.translationX}]};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs1\\\",\\\"event\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AAKmB,SAAAA,UAAUA,CAAAC,KAAA,EAEzB,MAAO,CACLC,SAAS,CAAE,CAAC,CAAEC,UAAU,CAAEF,KAAK,CAACG,YAAa,CAAC,CAChD,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_12552265402966_init_data = {\n    code: \"function presetsTs2(event,screenSize){return{transform:[{translateX:(event.translationX-screenSize.width)*0.3}]};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs2\\\",\\\"event\\\",\\\"screenSize\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\",\\\"width\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AAWuB,QAAC,CAAAA,UAAOA,CAAAC,KAAA,CAAAC,UAAe,EAE1C,MAAO,CACLC,SAAS,CAAE,CACT,CAAEC,UAAU,CAAE,CAACH,KAAK,CAACI,YAAY,CAAGH,UAAU,CAACI,KAAK,EAAI,GAAI,CAAC,CAEjE,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var SwipeRight = {\n    topScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs1 = function (event) {\n        return {\n          transform: [{\n            translateX: event.translationX\n          }]\n        };\n      };\n      presetsTs1.__closure = {};\n      presetsTs1.__workletHash = 5397481334330;\n      presetsTs1.__initData = _worklet_5397481334330_init_data;\n      presetsTs1.__stackDetails = _e;\n      return presetsTs1;\n    }(),\n    belowTopScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs2 = function (event, screenSize) {\n        return {\n          transform: [{\n            translateX: (event.translationX - screenSize.width) * 0.3\n          }]\n        };\n      };\n      presetsTs2.__closure = {};\n      presetsTs2.__workletHash = 12552265402966;\n      presetsTs2.__initData = _worklet_12552265402966_init_data;\n      presetsTs2.__stackDetails = _e;\n      return presetsTs2;\n    }()\n  };\n  var _worklet_2133058739768_init_data = {\n    code: \"function presetsTs3(event){return{transform:[{translateX:event.translationX}]};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs3\\\",\\\"event\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AAsBmB,SAAAA,UAAUA,CAAAC,KAAA,EAEzB,MAAO,CACLC,SAAS,CAAE,CAAC,CAAEC,UAAU,CAAEF,KAAK,CAACG,YAAa,CAAC,CAChD,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_3461802174358_init_data = {\n    code: \"function presetsTs4(event,screenSize){return{transform:[{translateX:(event.translationX+screenSize.width)*0.3}]};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs4\\\",\\\"event\\\",\\\"screenSize\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\",\\\"width\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AA4BuB,QAAC,CAAAA,UAAOA,CAAAC,KAAA,CAAAC,UAAe,EAE1C,MAAO,CACLC,SAAS,CAAE,CACT,CAAEC,UAAU,CAAE,CAACH,KAAK,CAACI,YAAY,CAAGH,UAAU,CAACI,KAAK,EAAI,GAAI,CAAC,CAEjE,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var SwipeLeft = {\n    topScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs3 = function (event) {\n        return {\n          transform: [{\n            translateX: event.translationX\n          }]\n        };\n      };\n      presetsTs3.__closure = {};\n      presetsTs3.__workletHash = 2133058739768;\n      presetsTs3.__initData = _worklet_2133058739768_init_data;\n      presetsTs3.__stackDetails = _e;\n      return presetsTs3;\n    }(),\n    belowTopScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs4 = function (event, screenSize) {\n        return {\n          transform: [{\n            translateX: (event.translationX + screenSize.width) * 0.3\n          }]\n        };\n      };\n      presetsTs4.__closure = {};\n      presetsTs4.__workletHash = 3461802174358;\n      presetsTs4.__initData = _worklet_3461802174358_init_data;\n      presetsTs4.__stackDetails = _e;\n      return presetsTs4;\n    }()\n  };\n  var _worklet_12609633126238_init_data = {\n    code: \"function presetsTs5(event){return{transform:[{translateY:event.translationY}]};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs5\\\",\\\"event\\\",\\\"transform\\\",\\\"translateY\\\",\\\"translationY\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AAuCmB,SAAAA,UAAUA,CAAAC,KAAA,EAEzB,MAAO,CACLC,SAAS,CAAE,CAAC,CAAEC,UAAU,CAAEF,KAAK,CAACG,YAAa,CAAC,CAChD,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_16693751064747_init_data = {\n    code: \"function presetsTs6(event,screenSize){return{transform:[{translateY:(event.translationY-screenSize.height)*0.3}]};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs6\\\",\\\"event\\\",\\\"screenSize\\\",\\\"transform\\\",\\\"translateY\\\",\\\"translationY\\\",\\\"height\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AA6CuB,QAAC,CAAAA,UAAOA,CAAAC,KAAA,CAAAC,UAAe,EAE1C,MAAO,CACLC,SAAS,CAAE,CACT,CAAEC,UAAU,CAAE,CAACH,KAAK,CAACI,YAAY,CAAGH,UAAU,CAACI,MAAM,EAAI,GAAI,CAAC,CAElE,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var SwipeDown = {\n    topScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs5 = function (event) {\n        return {\n          transform: [{\n            translateY: event.translationY\n          }]\n        };\n      };\n      presetsTs5.__closure = {};\n      presetsTs5.__workletHash = 12609633126238;\n      presetsTs5.__initData = _worklet_12609633126238_init_data;\n      presetsTs5.__stackDetails = _e;\n      return presetsTs5;\n    }(),\n    belowTopScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs6 = function (event, screenSize) {\n        return {\n          transform: [{\n            translateY: (event.translationY - screenSize.height) * 0.3\n          }]\n        };\n      };\n      presetsTs6.__closure = {};\n      presetsTs6.__workletHash = 16693751064747;\n      presetsTs6.__initData = _worklet_16693751064747_init_data;\n      presetsTs6.__stackDetails = _e;\n      return presetsTs6;\n    }()\n  };\n  var _worklet_12279878844764_init_data = {\n    code: \"function presetsTs7(event){return{transform:[{translateY:event.translationY}]};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs7\\\",\\\"event\\\",\\\"transform\\\",\\\"translateY\\\",\\\"translationY\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AAwDmB,SAAAA,UAAUA,CAAAC,KAAA,EAEzB,MAAO,CACLC,SAAS,CAAE,CAAC,CAAEC,UAAU,CAAEF,KAAK,CAACG,YAAa,CAAC,CAChD,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_4265744767203_init_data = {\n    code: \"function presetsTs8(event,screenSize){return{transform:[{translateY:(event.translationY+screenSize.height)*0.3}]};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs8\\\",\\\"event\\\",\\\"screenSize\\\",\\\"transform\\\",\\\"translateY\\\",\\\"translationY\\\",\\\"height\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AA8DuB,QAAC,CAAAA,UAAOA,CAAAC,KAAA,CAAAC,UAAe,EAE1C,MAAO,CACLC,SAAS,CAAE,CACT,CAAEC,UAAU,CAAE,CAACH,KAAK,CAACI,YAAY,CAAGH,UAAU,CAACI,MAAM,EAAI,GAAI,CAAC,CAElE,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var SwipeUp = {\n    topScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs7 = function (event) {\n        return {\n          transform: [{\n            translateY: event.translationY\n          }]\n        };\n      };\n      presetsTs7.__closure = {};\n      presetsTs7.__workletHash = 12279878844764;\n      presetsTs7.__initData = _worklet_12279878844764_init_data;\n      presetsTs7.__stackDetails = _e;\n      return presetsTs7;\n    }(),\n    belowTopScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs8 = function (event, screenSize) {\n        return {\n          transform: [{\n            translateY: (event.translationY + screenSize.height) * 0.3\n          }]\n        };\n      };\n      presetsTs8.__closure = {};\n      presetsTs8.__workletHash = 4265744767203;\n      presetsTs8.__initData = _worklet_4265744767203_init_data;\n      presetsTs8.__stackDetails = _e;\n      return presetsTs8;\n    }()\n  };\n  var _worklet_7218801101943_init_data = {\n    code: \"function presetsTs9(event,_screenSize){return{transform:[{translateX:event.translationX},{translateY:event.translationY}]};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs9\\\",\\\"event\\\",\\\"_screenSize\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\",\\\"translateY\\\",\\\"translationY\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AAyEkB,QAAC,CAAAA,UAAOA,CAAAC,KAAA,CAAAC,WAAgB,EAEtC,MAAO,CACLC,SAAS,CAAE,CACT,CAAEC,UAAU,CAAEH,KAAK,CAACI,YAAa,CAAC,CAClC,CAAEC,UAAU,CAAEL,KAAK,CAACM,YAAa,CAAC,CAEtC,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_8426911925292_init_data = {\n    code: \"function presetsTs10(_event,_screenSize){return{};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs10\\\",\\\"_event\\\",\\\"_screenSize\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AAkFuB,QAAC,CAAAA,WAAQA,CAAAC,MAAA,CAAAC,WAAgB,EAE5C,MAAO,CAAC,CAAC,CACX\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var TwoDimensional = {\n    topScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs9 = function (event, _screenSize) {\n        return {\n          transform: [{\n            translateX: event.translationX\n          }, {\n            translateY: event.translationY\n          }]\n        };\n      };\n      presetsTs9.__closure = {};\n      presetsTs9.__workletHash = 7218801101943;\n      presetsTs9.__initData = _worklet_7218801101943_init_data;\n      presetsTs9.__stackDetails = _e;\n      return presetsTs9;\n    }(),\n    belowTopScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs10 = function (_event, _screenSize) {\n        return {};\n      };\n      presetsTs10.__closure = {};\n      presetsTs10.__workletHash = 8426911925292;\n      presetsTs10.__initData = _worklet_8426911925292_init_data;\n      presetsTs10.__stackDetails = _e;\n      return presetsTs10;\n    }()\n  };\n  var _worklet_9103787439185_init_data = {\n    code: \"function presetsTs11(event,_screenSize){return{transform:[{translateX:event.translationX}]};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs11\\\",\\\"event\\\",\\\"_screenSize\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AAyFkB,QAAC,CAAAA,WAAOA,CAAAC,KAAA,CAAAC,WAAgB,EAEtC,MAAO,CACLC,SAAS,CAAE,CAAC,CAAEC,UAAU,CAAEH,KAAK,CAACI,YAAa,CAAC,CAChD,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_2542131628654_init_data = {\n    code: \"function presetsTs12(_event,_screenSize){return{};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs12\\\",\\\"_event\\\",\\\"_screenSize\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AA+FuB,QAAC,CAAAA,WAAQA,CAAAC,MAAA,CAAAC,WAAgB,EAE5C,MAAO,CAAC,CAAC,CACX\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var Horizontal = {\n    topScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs11 = function (event, _screenSize) {\n        return {\n          transform: [{\n            translateX: event.translationX\n          }]\n        };\n      };\n      presetsTs11.__closure = {};\n      presetsTs11.__workletHash = 9103787439185;\n      presetsTs11.__initData = _worklet_9103787439185_init_data;\n      presetsTs11.__stackDetails = _e;\n      return presetsTs11;\n    }(),\n    belowTopScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs12 = function (_event, _screenSize) {\n        return {};\n      };\n      presetsTs12.__closure = {};\n      presetsTs12.__workletHash = 2542131628654;\n      presetsTs12.__initData = _worklet_2542131628654_init_data;\n      presetsTs12.__stackDetails = _e;\n      return presetsTs12;\n    }()\n  };\n  var _worklet_6606829014899_init_data = {\n    code: \"function presetsTs13(event,_screenSize){return{transform:[{translateY:event.translationY}]};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs13\\\",\\\"event\\\",\\\"_screenSize\\\",\\\"transform\\\",\\\"translateY\\\",\\\"translationY\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AAsGkB,QAAC,CAAAA,WAAOA,CAAAC,KAAA,CAAAC,WAAgB,EAEtC,MAAO,CACLC,SAAS,CAAE,CAAC,CAAEC,UAAU,CAAEH,KAAK,CAACI,YAAa,CAAC,CAChD,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_1358672065192_init_data = {\n    code: \"function presetsTs14(_event,_screenSize){return{};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs14\\\",\\\"_event\\\",\\\"_screenSize\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AA4GuB,QAAC,CAAAA,WAAQA,CAAAC,MAAA,CAAAC,WAAgB,EAE5C,MAAO,CAAC,CAAC,CACX\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var Vertical = {\n    topScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs13 = function (event, _screenSize) {\n        return {\n          transform: [{\n            translateY: event.translationY\n          }]\n        };\n      };\n      presetsTs13.__closure = {};\n      presetsTs13.__workletHash = 6606829014899;\n      presetsTs13.__initData = _worklet_6606829014899_init_data;\n      presetsTs13.__stackDetails = _e;\n      return presetsTs13;\n    }(),\n    belowTopScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs14 = function (_event, _screenSize) {\n        return {};\n      };\n      presetsTs14.__closure = {};\n      presetsTs14.__workletHash = 1358672065192;\n      presetsTs14.__initData = _worklet_1358672065192_init_data;\n      presetsTs14.__stackDetails = _e;\n      return presetsTs14;\n    }()\n  };\n  var _worklet_2303728454086_init_data = {\n    code: \"function presetsTs15(event,screenSize){return{opacity:1-Math.abs(event.translationX/screenSize.width)};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs15\\\",\\\"event\\\",\\\"screenSize\\\",\\\"opacity\\\",\\\"Math\\\",\\\"abs\\\",\\\"translationX\\\",\\\"width\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AAmHkB,QAAC,CAAAA,WAAOA,CAAAC,KAAA,CAAUC,UAAK,EAErC,MAAO,CACLC,OAAO,CAAE,CAAC,CAAGC,IAAI,CAACC,GAAG,CAACJ,KAAK,CAACK,YAAY,CAAGJ,UAAU,CAACK,KAAK,CAC7D,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var _worklet_13066077812970_init_data = {\n    code: \"function presetsTs16(_event,_screenSize){return{};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-reanimated\\\\src\\\\screenTransition\\\\presets.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"presetsTs16\\\",\\\"_event\\\",\\\"_screenSize\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/tap2go/node_modules/react-native-reanimated/src/screenTransition/presets.ts\\\"],\\\"mappings\\\":\\\"AAyHuB,QAAC,CAAAA,WAAQA,CAAAC,MAAA,CAAAC,WAAgB,EAE5C,MAAO,CAAC,CAAC,CACX\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.4\"\n  };\n  var SwipeRightFade = {\n    topScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs15 = function (event, screenSize) {\n        return {\n          opacity: 1 - Math.abs(event.translationX / screenSize.width)\n        };\n      };\n      presetsTs15.__closure = {};\n      presetsTs15.__workletHash = 2303728454086;\n      presetsTs15.__initData = _worklet_2303728454086_init_data;\n      presetsTs15.__stackDetails = _e;\n      return presetsTs15;\n    }(),\n    belowTopScreenStyle: function () {\n      var _e = [new global.Error(), 1, -27];\n      var presetsTs16 = function (_event, _screenSize) {\n        return {};\n      };\n      presetsTs16.__closure = {};\n      presetsTs16.__workletHash = 13066077812970;\n      presetsTs16.__initData = _worklet_13066077812970_init_data;\n      presetsTs16.__stackDetails = _e;\n      return presetsTs16;\n    }()\n  };\n  var ScreenTransition = exports.ScreenTransition = {\n    SwipeRight,\n    SwipeLeft,\n    SwipeDown,\n    SwipeUp,\n    Horizontal,\n    Vertical,\n    TwoDimensional,\n    SwipeRightFade\n  };\n});", "lineCount": 354, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "ScreenTransition"], [7, 26, 1, 13], [8, 2, 1, 13], [8, 6, 1, 13, "_worklet_5397481334330_init_data"], [8, 38, 1, 13], [9, 4, 1, 13, "code"], [9, 8, 1, 13], [10, 4, 1, 13, "location"], [10, 12, 1, 13], [11, 4, 1, 13, "sourceMap"], [11, 13, 1, 13], [12, 4, 1, 13, "version"], [12, 11, 1, 13], [13, 2, 1, 13], [14, 2, 1, 13], [14, 6, 1, 13, "_worklet_12552265402966_init_data"], [14, 39, 1, 13], [15, 4, 1, 13, "code"], [15, 8, 1, 13], [16, 4, 1, 13, "location"], [16, 12, 1, 13], [17, 4, 1, 13, "sourceMap"], [17, 13, 1, 13], [18, 4, 1, 13, "version"], [18, 11, 1, 13], [19, 2, 1, 13], [20, 2, 5, 0], [20, 6, 5, 6, "SwipeRight"], [20, 16, 5, 42], [20, 19, 5, 45], [21, 4, 6, 2, "topScreenStyle"], [21, 18, 6, 16], [21, 20, 6, 18], [22, 6, 6, 18], [22, 10, 6, 18, "_e"], [22, 12, 6, 18], [22, 20, 6, 18, "global"], [22, 26, 6, 18], [22, 27, 6, 18, "Error"], [22, 32, 6, 18], [23, 6, 6, 18], [23, 10, 6, 18, "presetsTs1"], [23, 20, 6, 18], [23, 32, 6, 18, "presetsTs1"], [23, 33, 6, 19, "event"], [23, 38, 6, 24], [23, 40, 6, 29], [24, 8, 8, 4], [24, 15, 8, 11], [25, 10, 9, 6, "transform"], [25, 19, 9, 15], [25, 21, 9, 17], [25, 22, 9, 18], [26, 12, 9, 20, "translateX"], [26, 22, 9, 30], [26, 24, 9, 32, "event"], [26, 29, 9, 37], [26, 30, 9, 38, "translationX"], [27, 10, 9, 51], [27, 11, 9, 52], [28, 8, 10, 4], [28, 9, 10, 5], [29, 6, 11, 2], [29, 7, 11, 3], [30, 6, 11, 3, "presetsTs1"], [30, 16, 11, 3], [30, 17, 11, 3, "__closure"], [30, 26, 11, 3], [31, 6, 11, 3, "presetsTs1"], [31, 16, 11, 3], [31, 17, 11, 3, "__workletHash"], [31, 30, 11, 3], [32, 6, 11, 3, "presetsTs1"], [32, 16, 11, 3], [32, 17, 11, 3, "__initData"], [32, 27, 11, 3], [32, 30, 11, 3, "_worklet_5397481334330_init_data"], [32, 62, 11, 3], [33, 6, 11, 3, "presetsTs1"], [33, 16, 11, 3], [33, 17, 11, 3, "__stackDetails"], [33, 31, 11, 3], [33, 34, 11, 3, "_e"], [33, 36, 11, 3], [34, 6, 11, 3], [34, 13, 11, 3, "presetsTs1"], [34, 23, 11, 3], [35, 4, 11, 3], [35, 5, 6, 18], [35, 7, 11, 3], [36, 4, 12, 2, "belowTopScreenStyle"], [36, 23, 12, 21], [36, 25, 12, 23], [37, 6, 12, 23], [37, 10, 12, 23, "_e"], [37, 12, 12, 23], [37, 20, 12, 23, "global"], [37, 26, 12, 23], [37, 27, 12, 23, "Error"], [37, 32, 12, 23], [38, 6, 12, 23], [38, 10, 12, 23, "presetsTs2"], [38, 20, 12, 23], [38, 32, 12, 23, "presetsTs2"], [38, 33, 12, 24, "event"], [38, 38, 12, 29], [38, 40, 12, 31, "screenSize"], [38, 50, 12, 41], [38, 52, 12, 46], [39, 8, 14, 4], [39, 15, 14, 11], [40, 10, 15, 6, "transform"], [40, 19, 15, 15], [40, 21, 15, 17], [40, 22, 16, 8], [41, 12, 16, 10, "translateX"], [41, 22, 16, 20], [41, 24, 16, 22], [41, 25, 16, 23, "event"], [41, 30, 16, 28], [41, 31, 16, 29, "translationX"], [41, 43, 16, 41], [41, 46, 16, 44, "screenSize"], [41, 56, 16, 54], [41, 57, 16, 55, "width"], [41, 62, 16, 60], [41, 66, 16, 64], [42, 10, 16, 68], [42, 11, 16, 69], [43, 8, 18, 4], [43, 9, 18, 5], [44, 6, 19, 2], [44, 7, 19, 3], [45, 6, 19, 3, "presetsTs2"], [45, 16, 19, 3], [45, 17, 19, 3, "__closure"], [45, 26, 19, 3], [46, 6, 19, 3, "presetsTs2"], [46, 16, 19, 3], [46, 17, 19, 3, "__workletHash"], [46, 30, 19, 3], [47, 6, 19, 3, "presetsTs2"], [47, 16, 19, 3], [47, 17, 19, 3, "__initData"], [47, 27, 19, 3], [47, 30, 19, 3, "_worklet_12552265402966_init_data"], [47, 63, 19, 3], [48, 6, 19, 3, "presetsTs2"], [48, 16, 19, 3], [48, 17, 19, 3, "__stackDetails"], [48, 31, 19, 3], [48, 34, 19, 3, "_e"], [48, 36, 19, 3], [49, 6, 19, 3], [49, 13, 19, 3, "presetsTs2"], [49, 23, 19, 3], [50, 4, 19, 3], [50, 5, 12, 23], [51, 2, 20, 0], [51, 3, 20, 1], [52, 2, 20, 2], [52, 6, 20, 2, "_worklet_2133058739768_init_data"], [52, 38, 20, 2], [53, 4, 20, 2, "code"], [53, 8, 20, 2], [54, 4, 20, 2, "location"], [54, 12, 20, 2], [55, 4, 20, 2, "sourceMap"], [55, 13, 20, 2], [56, 4, 20, 2, "version"], [56, 11, 20, 2], [57, 2, 20, 2], [58, 2, 20, 2], [58, 6, 20, 2, "_worklet_3461802174358_init_data"], [58, 38, 20, 2], [59, 4, 20, 2, "code"], [59, 8, 20, 2], [60, 4, 20, 2, "location"], [60, 12, 20, 2], [61, 4, 20, 2, "sourceMap"], [61, 13, 20, 2], [62, 4, 20, 2, "version"], [62, 11, 20, 2], [63, 2, 20, 2], [64, 2, 22, 0], [64, 6, 22, 6, "SwipeLeft"], [64, 15, 22, 41], [64, 18, 22, 44], [65, 4, 23, 2, "topScreenStyle"], [65, 18, 23, 16], [65, 20, 23, 18], [66, 6, 23, 18], [66, 10, 23, 18, "_e"], [66, 12, 23, 18], [66, 20, 23, 18, "global"], [66, 26, 23, 18], [66, 27, 23, 18, "Error"], [66, 32, 23, 18], [67, 6, 23, 18], [67, 10, 23, 18, "presetsTs3"], [67, 20, 23, 18], [67, 32, 23, 18, "presetsTs3"], [67, 33, 23, 19, "event"], [67, 38, 23, 24], [67, 40, 23, 29], [68, 8, 25, 4], [68, 15, 25, 11], [69, 10, 26, 6, "transform"], [69, 19, 26, 15], [69, 21, 26, 17], [69, 22, 26, 18], [70, 12, 26, 20, "translateX"], [70, 22, 26, 30], [70, 24, 26, 32, "event"], [70, 29, 26, 37], [70, 30, 26, 38, "translationX"], [71, 10, 26, 51], [71, 11, 26, 52], [72, 8, 27, 4], [72, 9, 27, 5], [73, 6, 28, 2], [73, 7, 28, 3], [74, 6, 28, 3, "presetsTs3"], [74, 16, 28, 3], [74, 17, 28, 3, "__closure"], [74, 26, 28, 3], [75, 6, 28, 3, "presetsTs3"], [75, 16, 28, 3], [75, 17, 28, 3, "__workletHash"], [75, 30, 28, 3], [76, 6, 28, 3, "presetsTs3"], [76, 16, 28, 3], [76, 17, 28, 3, "__initData"], [76, 27, 28, 3], [76, 30, 28, 3, "_worklet_2133058739768_init_data"], [76, 62, 28, 3], [77, 6, 28, 3, "presetsTs3"], [77, 16, 28, 3], [77, 17, 28, 3, "__stackDetails"], [77, 31, 28, 3], [77, 34, 28, 3, "_e"], [77, 36, 28, 3], [78, 6, 28, 3], [78, 13, 28, 3, "presetsTs3"], [78, 23, 28, 3], [79, 4, 28, 3], [79, 5, 23, 18], [79, 7, 28, 3], [80, 4, 29, 2, "belowTopScreenStyle"], [80, 23, 29, 21], [80, 25, 29, 23], [81, 6, 29, 23], [81, 10, 29, 23, "_e"], [81, 12, 29, 23], [81, 20, 29, 23, "global"], [81, 26, 29, 23], [81, 27, 29, 23, "Error"], [81, 32, 29, 23], [82, 6, 29, 23], [82, 10, 29, 23, "presetsTs4"], [82, 20, 29, 23], [82, 32, 29, 23, "presetsTs4"], [82, 33, 29, 24, "event"], [82, 38, 29, 29], [82, 40, 29, 31, "screenSize"], [82, 50, 29, 41], [82, 52, 29, 46], [83, 8, 31, 4], [83, 15, 31, 11], [84, 10, 32, 6, "transform"], [84, 19, 32, 15], [84, 21, 32, 17], [84, 22, 33, 8], [85, 12, 33, 10, "translateX"], [85, 22, 33, 20], [85, 24, 33, 22], [85, 25, 33, 23, "event"], [85, 30, 33, 28], [85, 31, 33, 29, "translationX"], [85, 43, 33, 41], [85, 46, 33, 44, "screenSize"], [85, 56, 33, 54], [85, 57, 33, 55, "width"], [85, 62, 33, 60], [85, 66, 33, 64], [86, 10, 33, 68], [86, 11, 33, 69], [87, 8, 35, 4], [87, 9, 35, 5], [88, 6, 36, 2], [88, 7, 36, 3], [89, 6, 36, 3, "presetsTs4"], [89, 16, 36, 3], [89, 17, 36, 3, "__closure"], [89, 26, 36, 3], [90, 6, 36, 3, "presetsTs4"], [90, 16, 36, 3], [90, 17, 36, 3, "__workletHash"], [90, 30, 36, 3], [91, 6, 36, 3, "presetsTs4"], [91, 16, 36, 3], [91, 17, 36, 3, "__initData"], [91, 27, 36, 3], [91, 30, 36, 3, "_worklet_3461802174358_init_data"], [91, 62, 36, 3], [92, 6, 36, 3, "presetsTs4"], [92, 16, 36, 3], [92, 17, 36, 3, "__stackDetails"], [92, 31, 36, 3], [92, 34, 36, 3, "_e"], [92, 36, 36, 3], [93, 6, 36, 3], [93, 13, 36, 3, "presetsTs4"], [93, 23, 36, 3], [94, 4, 36, 3], [94, 5, 29, 23], [95, 2, 37, 0], [95, 3, 37, 1], [96, 2, 37, 2], [96, 6, 37, 2, "_worklet_12609633126238_init_data"], [96, 39, 37, 2], [97, 4, 37, 2, "code"], [97, 8, 37, 2], [98, 4, 37, 2, "location"], [98, 12, 37, 2], [99, 4, 37, 2, "sourceMap"], [99, 13, 37, 2], [100, 4, 37, 2, "version"], [100, 11, 37, 2], [101, 2, 37, 2], [102, 2, 37, 2], [102, 6, 37, 2, "_worklet_16693751064747_init_data"], [102, 39, 37, 2], [103, 4, 37, 2, "code"], [103, 8, 37, 2], [104, 4, 37, 2, "location"], [104, 12, 37, 2], [105, 4, 37, 2, "sourceMap"], [105, 13, 37, 2], [106, 4, 37, 2, "version"], [106, 11, 37, 2], [107, 2, 37, 2], [108, 2, 39, 0], [108, 6, 39, 6, "SwipeDown"], [108, 15, 39, 41], [108, 18, 39, 44], [109, 4, 40, 2, "topScreenStyle"], [109, 18, 40, 16], [109, 20, 40, 18], [110, 6, 40, 18], [110, 10, 40, 18, "_e"], [110, 12, 40, 18], [110, 20, 40, 18, "global"], [110, 26, 40, 18], [110, 27, 40, 18, "Error"], [110, 32, 40, 18], [111, 6, 40, 18], [111, 10, 40, 18, "presetsTs5"], [111, 20, 40, 18], [111, 32, 40, 18, "presetsTs5"], [111, 33, 40, 19, "event"], [111, 38, 40, 24], [111, 40, 40, 29], [112, 8, 42, 4], [112, 15, 42, 11], [113, 10, 43, 6, "transform"], [113, 19, 43, 15], [113, 21, 43, 17], [113, 22, 43, 18], [114, 12, 43, 20, "translateY"], [114, 22, 43, 30], [114, 24, 43, 32, "event"], [114, 29, 43, 37], [114, 30, 43, 38, "translationY"], [115, 10, 43, 51], [115, 11, 43, 52], [116, 8, 44, 4], [116, 9, 44, 5], [117, 6, 45, 2], [117, 7, 45, 3], [118, 6, 45, 3, "presetsTs5"], [118, 16, 45, 3], [118, 17, 45, 3, "__closure"], [118, 26, 45, 3], [119, 6, 45, 3, "presetsTs5"], [119, 16, 45, 3], [119, 17, 45, 3, "__workletHash"], [119, 30, 45, 3], [120, 6, 45, 3, "presetsTs5"], [120, 16, 45, 3], [120, 17, 45, 3, "__initData"], [120, 27, 45, 3], [120, 30, 45, 3, "_worklet_12609633126238_init_data"], [120, 63, 45, 3], [121, 6, 45, 3, "presetsTs5"], [121, 16, 45, 3], [121, 17, 45, 3, "__stackDetails"], [121, 31, 45, 3], [121, 34, 45, 3, "_e"], [121, 36, 45, 3], [122, 6, 45, 3], [122, 13, 45, 3, "presetsTs5"], [122, 23, 45, 3], [123, 4, 45, 3], [123, 5, 40, 18], [123, 7, 45, 3], [124, 4, 46, 2, "belowTopScreenStyle"], [124, 23, 46, 21], [124, 25, 46, 23], [125, 6, 46, 23], [125, 10, 46, 23, "_e"], [125, 12, 46, 23], [125, 20, 46, 23, "global"], [125, 26, 46, 23], [125, 27, 46, 23, "Error"], [125, 32, 46, 23], [126, 6, 46, 23], [126, 10, 46, 23, "presetsTs6"], [126, 20, 46, 23], [126, 32, 46, 23, "presetsTs6"], [126, 33, 46, 24, "event"], [126, 38, 46, 29], [126, 40, 46, 31, "screenSize"], [126, 50, 46, 41], [126, 52, 46, 46], [127, 8, 48, 4], [127, 15, 48, 11], [128, 10, 49, 6, "transform"], [128, 19, 49, 15], [128, 21, 49, 17], [128, 22, 50, 8], [129, 12, 50, 10, "translateY"], [129, 22, 50, 20], [129, 24, 50, 22], [129, 25, 50, 23, "event"], [129, 30, 50, 28], [129, 31, 50, 29, "translationY"], [129, 43, 50, 41], [129, 46, 50, 44, "screenSize"], [129, 56, 50, 54], [129, 57, 50, 55, "height"], [129, 63, 50, 61], [129, 67, 50, 65], [130, 10, 50, 69], [130, 11, 50, 70], [131, 8, 52, 4], [131, 9, 52, 5], [132, 6, 53, 2], [132, 7, 53, 3], [133, 6, 53, 3, "presetsTs6"], [133, 16, 53, 3], [133, 17, 53, 3, "__closure"], [133, 26, 53, 3], [134, 6, 53, 3, "presetsTs6"], [134, 16, 53, 3], [134, 17, 53, 3, "__workletHash"], [134, 30, 53, 3], [135, 6, 53, 3, "presetsTs6"], [135, 16, 53, 3], [135, 17, 53, 3, "__initData"], [135, 27, 53, 3], [135, 30, 53, 3, "_worklet_16693751064747_init_data"], [135, 63, 53, 3], [136, 6, 53, 3, "presetsTs6"], [136, 16, 53, 3], [136, 17, 53, 3, "__stackDetails"], [136, 31, 53, 3], [136, 34, 53, 3, "_e"], [136, 36, 53, 3], [137, 6, 53, 3], [137, 13, 53, 3, "presetsTs6"], [137, 23, 53, 3], [138, 4, 53, 3], [138, 5, 46, 23], [139, 2, 54, 0], [139, 3, 54, 1], [140, 2, 54, 2], [140, 6, 54, 2, "_worklet_12279878844764_init_data"], [140, 39, 54, 2], [141, 4, 54, 2, "code"], [141, 8, 54, 2], [142, 4, 54, 2, "location"], [142, 12, 54, 2], [143, 4, 54, 2, "sourceMap"], [143, 13, 54, 2], [144, 4, 54, 2, "version"], [144, 11, 54, 2], [145, 2, 54, 2], [146, 2, 54, 2], [146, 6, 54, 2, "_worklet_4265744767203_init_data"], [146, 38, 54, 2], [147, 4, 54, 2, "code"], [147, 8, 54, 2], [148, 4, 54, 2, "location"], [148, 12, 54, 2], [149, 4, 54, 2, "sourceMap"], [149, 13, 54, 2], [150, 4, 54, 2, "version"], [150, 11, 54, 2], [151, 2, 54, 2], [152, 2, 56, 0], [152, 6, 56, 6, "SwipeUp"], [152, 13, 56, 39], [152, 16, 56, 42], [153, 4, 57, 2, "topScreenStyle"], [153, 18, 57, 16], [153, 20, 57, 18], [154, 6, 57, 18], [154, 10, 57, 18, "_e"], [154, 12, 57, 18], [154, 20, 57, 18, "global"], [154, 26, 57, 18], [154, 27, 57, 18, "Error"], [154, 32, 57, 18], [155, 6, 57, 18], [155, 10, 57, 18, "presetsTs7"], [155, 20, 57, 18], [155, 32, 57, 18, "presetsTs7"], [155, 33, 57, 19, "event"], [155, 38, 57, 24], [155, 40, 57, 29], [156, 8, 59, 4], [156, 15, 59, 11], [157, 10, 60, 6, "transform"], [157, 19, 60, 15], [157, 21, 60, 17], [157, 22, 60, 18], [158, 12, 60, 20, "translateY"], [158, 22, 60, 30], [158, 24, 60, 32, "event"], [158, 29, 60, 37], [158, 30, 60, 38, "translationY"], [159, 10, 60, 51], [159, 11, 60, 52], [160, 8, 61, 4], [160, 9, 61, 5], [161, 6, 62, 2], [161, 7, 62, 3], [162, 6, 62, 3, "presetsTs7"], [162, 16, 62, 3], [162, 17, 62, 3, "__closure"], [162, 26, 62, 3], [163, 6, 62, 3, "presetsTs7"], [163, 16, 62, 3], [163, 17, 62, 3, "__workletHash"], [163, 30, 62, 3], [164, 6, 62, 3, "presetsTs7"], [164, 16, 62, 3], [164, 17, 62, 3, "__initData"], [164, 27, 62, 3], [164, 30, 62, 3, "_worklet_12279878844764_init_data"], [164, 63, 62, 3], [165, 6, 62, 3, "presetsTs7"], [165, 16, 62, 3], [165, 17, 62, 3, "__stackDetails"], [165, 31, 62, 3], [165, 34, 62, 3, "_e"], [165, 36, 62, 3], [166, 6, 62, 3], [166, 13, 62, 3, "presetsTs7"], [166, 23, 62, 3], [167, 4, 62, 3], [167, 5, 57, 18], [167, 7, 62, 3], [168, 4, 63, 2, "belowTopScreenStyle"], [168, 23, 63, 21], [168, 25, 63, 23], [169, 6, 63, 23], [169, 10, 63, 23, "_e"], [169, 12, 63, 23], [169, 20, 63, 23, "global"], [169, 26, 63, 23], [169, 27, 63, 23, "Error"], [169, 32, 63, 23], [170, 6, 63, 23], [170, 10, 63, 23, "presetsTs8"], [170, 20, 63, 23], [170, 32, 63, 23, "presetsTs8"], [170, 33, 63, 24, "event"], [170, 38, 63, 29], [170, 40, 63, 31, "screenSize"], [170, 50, 63, 41], [170, 52, 63, 46], [171, 8, 65, 4], [171, 15, 65, 11], [172, 10, 66, 6, "transform"], [172, 19, 66, 15], [172, 21, 66, 17], [172, 22, 67, 8], [173, 12, 67, 10, "translateY"], [173, 22, 67, 20], [173, 24, 67, 22], [173, 25, 67, 23, "event"], [173, 30, 67, 28], [173, 31, 67, 29, "translationY"], [173, 43, 67, 41], [173, 46, 67, 44, "screenSize"], [173, 56, 67, 54], [173, 57, 67, 55, "height"], [173, 63, 67, 61], [173, 67, 67, 65], [174, 10, 67, 69], [174, 11, 67, 70], [175, 8, 69, 4], [175, 9, 69, 5], [176, 6, 70, 2], [176, 7, 70, 3], [177, 6, 70, 3, "presetsTs8"], [177, 16, 70, 3], [177, 17, 70, 3, "__closure"], [177, 26, 70, 3], [178, 6, 70, 3, "presetsTs8"], [178, 16, 70, 3], [178, 17, 70, 3, "__workletHash"], [178, 30, 70, 3], [179, 6, 70, 3, "presetsTs8"], [179, 16, 70, 3], [179, 17, 70, 3, "__initData"], [179, 27, 70, 3], [179, 30, 70, 3, "_worklet_4265744767203_init_data"], [179, 62, 70, 3], [180, 6, 70, 3, "presetsTs8"], [180, 16, 70, 3], [180, 17, 70, 3, "__stackDetails"], [180, 31, 70, 3], [180, 34, 70, 3, "_e"], [180, 36, 70, 3], [181, 6, 70, 3], [181, 13, 70, 3, "presetsTs8"], [181, 23, 70, 3], [182, 4, 70, 3], [182, 5, 63, 23], [183, 2, 71, 0], [183, 3, 71, 1], [184, 2, 71, 2], [184, 6, 71, 2, "_worklet_7218801101943_init_data"], [184, 38, 71, 2], [185, 4, 71, 2, "code"], [185, 8, 71, 2], [186, 4, 71, 2, "location"], [186, 12, 71, 2], [187, 4, 71, 2, "sourceMap"], [187, 13, 71, 2], [188, 4, 71, 2, "version"], [188, 11, 71, 2], [189, 2, 71, 2], [190, 2, 71, 2], [190, 6, 71, 2, "_worklet_8426911925292_init_data"], [190, 38, 71, 2], [191, 4, 71, 2, "code"], [191, 8, 71, 2], [192, 4, 71, 2, "location"], [192, 12, 71, 2], [193, 4, 71, 2, "sourceMap"], [193, 13, 71, 2], [194, 4, 71, 2, "version"], [194, 11, 71, 2], [195, 2, 71, 2], [196, 2, 73, 0], [196, 6, 73, 6, "TwoDimensional"], [196, 20, 73, 46], [196, 23, 73, 49], [197, 4, 74, 2, "topScreenStyle"], [197, 18, 74, 16], [197, 20, 74, 18], [198, 6, 74, 18], [198, 10, 74, 18, "_e"], [198, 12, 74, 18], [198, 20, 74, 18, "global"], [198, 26, 74, 18], [198, 27, 74, 18, "Error"], [198, 32, 74, 18], [199, 6, 74, 18], [199, 10, 74, 18, "presetsTs9"], [199, 20, 74, 18], [199, 32, 74, 18, "presetsTs9"], [199, 33, 74, 19, "event"], [199, 38, 74, 24], [199, 40, 74, 26, "_screenSize"], [199, 51, 74, 37], [199, 53, 74, 42], [200, 8, 76, 4], [200, 15, 76, 11], [201, 10, 77, 6, "transform"], [201, 19, 77, 15], [201, 21, 77, 17], [201, 22, 78, 8], [202, 12, 78, 10, "translateX"], [202, 22, 78, 20], [202, 24, 78, 22, "event"], [202, 29, 78, 27], [202, 30, 78, 28, "translationX"], [203, 10, 78, 41], [203, 11, 78, 42], [203, 13, 79, 8], [204, 12, 79, 10, "translateY"], [204, 22, 79, 20], [204, 24, 79, 22, "event"], [204, 29, 79, 27], [204, 30, 79, 28, "translationY"], [205, 10, 79, 41], [205, 11, 79, 42], [206, 8, 81, 4], [206, 9, 81, 5], [207, 6, 82, 2], [207, 7, 82, 3], [208, 6, 82, 3, "presetsTs9"], [208, 16, 82, 3], [208, 17, 82, 3, "__closure"], [208, 26, 82, 3], [209, 6, 82, 3, "presetsTs9"], [209, 16, 82, 3], [209, 17, 82, 3, "__workletHash"], [209, 30, 82, 3], [210, 6, 82, 3, "presetsTs9"], [210, 16, 82, 3], [210, 17, 82, 3, "__initData"], [210, 27, 82, 3], [210, 30, 82, 3, "_worklet_7218801101943_init_data"], [210, 62, 82, 3], [211, 6, 82, 3, "presetsTs9"], [211, 16, 82, 3], [211, 17, 82, 3, "__stackDetails"], [211, 31, 82, 3], [211, 34, 82, 3, "_e"], [211, 36, 82, 3], [212, 6, 82, 3], [212, 13, 82, 3, "presetsTs9"], [212, 23, 82, 3], [213, 4, 82, 3], [213, 5, 74, 18], [213, 7, 82, 3], [214, 4, 83, 2, "belowTopScreenStyle"], [214, 23, 83, 21], [214, 25, 83, 23], [215, 6, 83, 23], [215, 10, 83, 23, "_e"], [215, 12, 83, 23], [215, 20, 83, 23, "global"], [215, 26, 83, 23], [215, 27, 83, 23, "Error"], [215, 32, 83, 23], [216, 6, 83, 23], [216, 10, 83, 23, "presetsTs10"], [216, 21, 83, 23], [216, 33, 83, 23, "presetsTs10"], [216, 34, 83, 24, "_event"], [216, 40, 83, 30], [216, 42, 83, 32, "_screenSize"], [216, 53, 83, 43], [216, 55, 83, 48], [217, 8, 85, 4], [217, 15, 85, 11], [217, 16, 85, 12], [217, 17, 85, 13], [218, 6, 86, 2], [218, 7, 86, 3], [219, 6, 86, 3, "presetsTs10"], [219, 17, 86, 3], [219, 18, 86, 3, "__closure"], [219, 27, 86, 3], [220, 6, 86, 3, "presetsTs10"], [220, 17, 86, 3], [220, 18, 86, 3, "__workletHash"], [220, 31, 86, 3], [221, 6, 86, 3, "presetsTs10"], [221, 17, 86, 3], [221, 18, 86, 3, "__initData"], [221, 28, 86, 3], [221, 31, 86, 3, "_worklet_8426911925292_init_data"], [221, 63, 86, 3], [222, 6, 86, 3, "presetsTs10"], [222, 17, 86, 3], [222, 18, 86, 3, "__stackDetails"], [222, 32, 86, 3], [222, 35, 86, 3, "_e"], [222, 37, 86, 3], [223, 6, 86, 3], [223, 13, 86, 3, "presetsTs10"], [223, 24, 86, 3], [224, 4, 86, 3], [224, 5, 83, 23], [225, 2, 87, 0], [225, 3, 87, 1], [226, 2, 87, 2], [226, 6, 87, 2, "_worklet_9103787439185_init_data"], [226, 38, 87, 2], [227, 4, 87, 2, "code"], [227, 8, 87, 2], [228, 4, 87, 2, "location"], [228, 12, 87, 2], [229, 4, 87, 2, "sourceMap"], [229, 13, 87, 2], [230, 4, 87, 2, "version"], [230, 11, 87, 2], [231, 2, 87, 2], [232, 2, 87, 2], [232, 6, 87, 2, "_worklet_2542131628654_init_data"], [232, 38, 87, 2], [233, 4, 87, 2, "code"], [233, 8, 87, 2], [234, 4, 87, 2, "location"], [234, 12, 87, 2], [235, 4, 87, 2, "sourceMap"], [235, 13, 87, 2], [236, 4, 87, 2, "version"], [236, 11, 87, 2], [237, 2, 87, 2], [238, 2, 89, 0], [238, 6, 89, 6, "Horizontal"], [238, 16, 89, 42], [238, 19, 89, 45], [239, 4, 90, 2, "topScreenStyle"], [239, 18, 90, 16], [239, 20, 90, 18], [240, 6, 90, 18], [240, 10, 90, 18, "_e"], [240, 12, 90, 18], [240, 20, 90, 18, "global"], [240, 26, 90, 18], [240, 27, 90, 18, "Error"], [240, 32, 90, 18], [241, 6, 90, 18], [241, 10, 90, 18, "presetsTs11"], [241, 21, 90, 18], [241, 33, 90, 18, "presetsTs11"], [241, 34, 90, 19, "event"], [241, 39, 90, 24], [241, 41, 90, 26, "_screenSize"], [241, 52, 90, 37], [241, 54, 90, 42], [242, 8, 92, 4], [242, 15, 92, 11], [243, 10, 93, 6, "transform"], [243, 19, 93, 15], [243, 21, 93, 17], [243, 22, 93, 18], [244, 12, 93, 20, "translateX"], [244, 22, 93, 30], [244, 24, 93, 32, "event"], [244, 29, 93, 37], [244, 30, 93, 38, "translationX"], [245, 10, 93, 51], [245, 11, 93, 52], [246, 8, 94, 4], [246, 9, 94, 5], [247, 6, 95, 2], [247, 7, 95, 3], [248, 6, 95, 3, "presetsTs11"], [248, 17, 95, 3], [248, 18, 95, 3, "__closure"], [248, 27, 95, 3], [249, 6, 95, 3, "presetsTs11"], [249, 17, 95, 3], [249, 18, 95, 3, "__workletHash"], [249, 31, 95, 3], [250, 6, 95, 3, "presetsTs11"], [250, 17, 95, 3], [250, 18, 95, 3, "__initData"], [250, 28, 95, 3], [250, 31, 95, 3, "_worklet_9103787439185_init_data"], [250, 63, 95, 3], [251, 6, 95, 3, "presetsTs11"], [251, 17, 95, 3], [251, 18, 95, 3, "__stackDetails"], [251, 32, 95, 3], [251, 35, 95, 3, "_e"], [251, 37, 95, 3], [252, 6, 95, 3], [252, 13, 95, 3, "presetsTs11"], [252, 24, 95, 3], [253, 4, 95, 3], [253, 5, 90, 18], [253, 7, 95, 3], [254, 4, 96, 2, "belowTopScreenStyle"], [254, 23, 96, 21], [254, 25, 96, 23], [255, 6, 96, 23], [255, 10, 96, 23, "_e"], [255, 12, 96, 23], [255, 20, 96, 23, "global"], [255, 26, 96, 23], [255, 27, 96, 23, "Error"], [255, 32, 96, 23], [256, 6, 96, 23], [256, 10, 96, 23, "presetsTs12"], [256, 21, 96, 23], [256, 33, 96, 23, "presetsTs12"], [256, 34, 96, 24, "_event"], [256, 40, 96, 30], [256, 42, 96, 32, "_screenSize"], [256, 53, 96, 43], [256, 55, 96, 48], [257, 8, 98, 4], [257, 15, 98, 11], [257, 16, 98, 12], [257, 17, 98, 13], [258, 6, 99, 2], [258, 7, 99, 3], [259, 6, 99, 3, "presetsTs12"], [259, 17, 99, 3], [259, 18, 99, 3, "__closure"], [259, 27, 99, 3], [260, 6, 99, 3, "presetsTs12"], [260, 17, 99, 3], [260, 18, 99, 3, "__workletHash"], [260, 31, 99, 3], [261, 6, 99, 3, "presetsTs12"], [261, 17, 99, 3], [261, 18, 99, 3, "__initData"], [261, 28, 99, 3], [261, 31, 99, 3, "_worklet_2542131628654_init_data"], [261, 63, 99, 3], [262, 6, 99, 3, "presetsTs12"], [262, 17, 99, 3], [262, 18, 99, 3, "__stackDetails"], [262, 32, 99, 3], [262, 35, 99, 3, "_e"], [262, 37, 99, 3], [263, 6, 99, 3], [263, 13, 99, 3, "presetsTs12"], [263, 24, 99, 3], [264, 4, 99, 3], [264, 5, 96, 23], [265, 2, 100, 0], [265, 3, 100, 1], [266, 2, 100, 2], [266, 6, 100, 2, "_worklet_6606829014899_init_data"], [266, 38, 100, 2], [267, 4, 100, 2, "code"], [267, 8, 100, 2], [268, 4, 100, 2, "location"], [268, 12, 100, 2], [269, 4, 100, 2, "sourceMap"], [269, 13, 100, 2], [270, 4, 100, 2, "version"], [270, 11, 100, 2], [271, 2, 100, 2], [272, 2, 100, 2], [272, 6, 100, 2, "_worklet_1358672065192_init_data"], [272, 38, 100, 2], [273, 4, 100, 2, "code"], [273, 8, 100, 2], [274, 4, 100, 2, "location"], [274, 12, 100, 2], [275, 4, 100, 2, "sourceMap"], [275, 13, 100, 2], [276, 4, 100, 2, "version"], [276, 11, 100, 2], [277, 2, 100, 2], [278, 2, 102, 0], [278, 6, 102, 6, "Vertical"], [278, 14, 102, 40], [278, 17, 102, 43], [279, 4, 103, 2, "topScreenStyle"], [279, 18, 103, 16], [279, 20, 103, 18], [280, 6, 103, 18], [280, 10, 103, 18, "_e"], [280, 12, 103, 18], [280, 20, 103, 18, "global"], [280, 26, 103, 18], [280, 27, 103, 18, "Error"], [280, 32, 103, 18], [281, 6, 103, 18], [281, 10, 103, 18, "presetsTs13"], [281, 21, 103, 18], [281, 33, 103, 18, "presetsTs13"], [281, 34, 103, 19, "event"], [281, 39, 103, 24], [281, 41, 103, 26, "_screenSize"], [281, 52, 103, 37], [281, 54, 103, 42], [282, 8, 105, 4], [282, 15, 105, 11], [283, 10, 106, 6, "transform"], [283, 19, 106, 15], [283, 21, 106, 17], [283, 22, 106, 18], [284, 12, 106, 20, "translateY"], [284, 22, 106, 30], [284, 24, 106, 32, "event"], [284, 29, 106, 37], [284, 30, 106, 38, "translationY"], [285, 10, 106, 51], [285, 11, 106, 52], [286, 8, 107, 4], [286, 9, 107, 5], [287, 6, 108, 2], [287, 7, 108, 3], [288, 6, 108, 3, "presetsTs13"], [288, 17, 108, 3], [288, 18, 108, 3, "__closure"], [288, 27, 108, 3], [289, 6, 108, 3, "presetsTs13"], [289, 17, 108, 3], [289, 18, 108, 3, "__workletHash"], [289, 31, 108, 3], [290, 6, 108, 3, "presetsTs13"], [290, 17, 108, 3], [290, 18, 108, 3, "__initData"], [290, 28, 108, 3], [290, 31, 108, 3, "_worklet_6606829014899_init_data"], [290, 63, 108, 3], [291, 6, 108, 3, "presetsTs13"], [291, 17, 108, 3], [291, 18, 108, 3, "__stackDetails"], [291, 32, 108, 3], [291, 35, 108, 3, "_e"], [291, 37, 108, 3], [292, 6, 108, 3], [292, 13, 108, 3, "presetsTs13"], [292, 24, 108, 3], [293, 4, 108, 3], [293, 5, 103, 18], [293, 7, 108, 3], [294, 4, 109, 2, "belowTopScreenStyle"], [294, 23, 109, 21], [294, 25, 109, 23], [295, 6, 109, 23], [295, 10, 109, 23, "_e"], [295, 12, 109, 23], [295, 20, 109, 23, "global"], [295, 26, 109, 23], [295, 27, 109, 23, "Error"], [295, 32, 109, 23], [296, 6, 109, 23], [296, 10, 109, 23, "presetsTs14"], [296, 21, 109, 23], [296, 33, 109, 23, "presetsTs14"], [296, 34, 109, 24, "_event"], [296, 40, 109, 30], [296, 42, 109, 32, "_screenSize"], [296, 53, 109, 43], [296, 55, 109, 48], [297, 8, 111, 4], [297, 15, 111, 11], [297, 16, 111, 12], [297, 17, 111, 13], [298, 6, 112, 2], [298, 7, 112, 3], [299, 6, 112, 3, "presetsTs14"], [299, 17, 112, 3], [299, 18, 112, 3, "__closure"], [299, 27, 112, 3], [300, 6, 112, 3, "presetsTs14"], [300, 17, 112, 3], [300, 18, 112, 3, "__workletHash"], [300, 31, 112, 3], [301, 6, 112, 3, "presetsTs14"], [301, 17, 112, 3], [301, 18, 112, 3, "__initData"], [301, 28, 112, 3], [301, 31, 112, 3, "_worklet_1358672065192_init_data"], [301, 63, 112, 3], [302, 6, 112, 3, "presetsTs14"], [302, 17, 112, 3], [302, 18, 112, 3, "__stackDetails"], [302, 32, 112, 3], [302, 35, 112, 3, "_e"], [302, 37, 112, 3], [303, 6, 112, 3], [303, 13, 112, 3, "presetsTs14"], [303, 24, 112, 3], [304, 4, 112, 3], [304, 5, 109, 23], [305, 2, 113, 0], [305, 3, 113, 1], [306, 2, 113, 2], [306, 6, 113, 2, "_worklet_2303728454086_init_data"], [306, 38, 113, 2], [307, 4, 113, 2, "code"], [307, 8, 113, 2], [308, 4, 113, 2, "location"], [308, 12, 113, 2], [309, 4, 113, 2, "sourceMap"], [309, 13, 113, 2], [310, 4, 113, 2, "version"], [310, 11, 113, 2], [311, 2, 113, 2], [312, 2, 113, 2], [312, 6, 113, 2, "_worklet_13066077812970_init_data"], [312, 39, 113, 2], [313, 4, 113, 2, "code"], [313, 8, 113, 2], [314, 4, 113, 2, "location"], [314, 12, 113, 2], [315, 4, 113, 2, "sourceMap"], [315, 13, 113, 2], [316, 4, 113, 2, "version"], [316, 11, 113, 2], [317, 2, 113, 2], [318, 2, 115, 0], [318, 6, 115, 6, "SwipeRightFade"], [318, 20, 115, 46], [318, 23, 115, 49], [319, 4, 116, 2, "topScreenStyle"], [319, 18, 116, 16], [319, 20, 116, 18], [320, 6, 116, 18], [320, 10, 116, 18, "_e"], [320, 12, 116, 18], [320, 20, 116, 18, "global"], [320, 26, 116, 18], [320, 27, 116, 18, "Error"], [320, 32, 116, 18], [321, 6, 116, 18], [321, 10, 116, 18, "presetsTs15"], [321, 21, 116, 18], [321, 33, 116, 18, "presetsTs15"], [321, 34, 116, 19, "event"], [321, 39, 116, 24], [321, 41, 116, 26, "screenSize"], [321, 51, 116, 36], [321, 53, 116, 41], [322, 8, 118, 4], [322, 15, 118, 11], [323, 10, 119, 6, "opacity"], [323, 17, 119, 13], [323, 19, 119, 15], [323, 20, 119, 16], [323, 23, 119, 19, "Math"], [323, 27, 119, 23], [323, 28, 119, 24, "abs"], [323, 31, 119, 27], [323, 32, 119, 28, "event"], [323, 37, 119, 33], [323, 38, 119, 34, "translationX"], [323, 50, 119, 46], [323, 53, 119, 49, "screenSize"], [323, 63, 119, 59], [323, 64, 119, 60, "width"], [323, 69, 119, 65], [324, 8, 120, 4], [324, 9, 120, 5], [325, 6, 121, 2], [325, 7, 121, 3], [326, 6, 121, 3, "presetsTs15"], [326, 17, 121, 3], [326, 18, 121, 3, "__closure"], [326, 27, 121, 3], [327, 6, 121, 3, "presetsTs15"], [327, 17, 121, 3], [327, 18, 121, 3, "__workletHash"], [327, 31, 121, 3], [328, 6, 121, 3, "presetsTs15"], [328, 17, 121, 3], [328, 18, 121, 3, "__initData"], [328, 28, 121, 3], [328, 31, 121, 3, "_worklet_2303728454086_init_data"], [328, 63, 121, 3], [329, 6, 121, 3, "presetsTs15"], [329, 17, 121, 3], [329, 18, 121, 3, "__stackDetails"], [329, 32, 121, 3], [329, 35, 121, 3, "_e"], [329, 37, 121, 3], [330, 6, 121, 3], [330, 13, 121, 3, "presetsTs15"], [330, 24, 121, 3], [331, 4, 121, 3], [331, 5, 116, 18], [331, 7, 121, 3], [332, 4, 122, 2, "belowTopScreenStyle"], [332, 23, 122, 21], [332, 25, 122, 23], [333, 6, 122, 23], [333, 10, 122, 23, "_e"], [333, 12, 122, 23], [333, 20, 122, 23, "global"], [333, 26, 122, 23], [333, 27, 122, 23, "Error"], [333, 32, 122, 23], [334, 6, 122, 23], [334, 10, 122, 23, "presetsTs16"], [334, 21, 122, 23], [334, 33, 122, 23, "presetsTs16"], [334, 34, 122, 24, "_event"], [334, 40, 122, 30], [334, 42, 122, 32, "_screenSize"], [334, 53, 122, 43], [334, 55, 122, 48], [335, 8, 124, 4], [335, 15, 124, 11], [335, 16, 124, 12], [335, 17, 124, 13], [336, 6, 125, 2], [336, 7, 125, 3], [337, 6, 125, 3, "presetsTs16"], [337, 17, 125, 3], [337, 18, 125, 3, "__closure"], [337, 27, 125, 3], [338, 6, 125, 3, "presetsTs16"], [338, 17, 125, 3], [338, 18, 125, 3, "__workletHash"], [338, 31, 125, 3], [339, 6, 125, 3, "presetsTs16"], [339, 17, 125, 3], [339, 18, 125, 3, "__initData"], [339, 28, 125, 3], [339, 31, 125, 3, "_worklet_13066077812970_init_data"], [339, 64, 125, 3], [340, 6, 125, 3, "presetsTs16"], [340, 17, 125, 3], [340, 18, 125, 3, "__stackDetails"], [340, 32, 125, 3], [340, 35, 125, 3, "_e"], [340, 37, 125, 3], [341, 6, 125, 3], [341, 13, 125, 3, "presetsTs16"], [341, 24, 125, 3], [342, 4, 125, 3], [342, 5, 122, 23], [343, 2, 126, 0], [343, 3, 126, 1], [344, 2, 128, 7], [344, 6, 128, 13, "ScreenTransition"], [344, 22, 128, 29], [344, 25, 128, 29, "exports"], [344, 32, 128, 29], [344, 33, 128, 29, "ScreenTransition"], [344, 49, 128, 29], [344, 52, 128, 32], [345, 4, 129, 2, "SwipeRight"], [345, 14, 129, 12], [346, 4, 130, 2, "SwipeLeft"], [346, 13, 130, 11], [347, 4, 131, 2, "SwipeDown"], [347, 13, 131, 11], [348, 4, 132, 2, "SwipeUp"], [348, 11, 132, 9], [349, 4, 133, 2, "Horizontal"], [349, 14, 133, 12], [350, 4, 134, 2, "Vertical"], [350, 12, 134, 10], [351, 4, 135, 2, "TwoDimensional"], [351, 18, 135, 16], [352, 4, 136, 2, "SwipeRightFade"], [353, 2, 137, 0], [353, 3, 137, 1], [354, 0, 137, 2], [354, 3]], "functionMap": {"names": ["<global>", "SwipeRight.topScreenStyle", "SwipeRight.belowTopScreenStyle", "SwipeLeft.topScreenStyle", "SwipeLeft.belowTopScreenStyle", "SwipeDown.topScreenStyle", "SwipeDown.belowTopScreenStyle", "SwipeUp.topScreenStyle", "SwipeUp.belowTopScreenStyle", "TwoDimensional.topScreenStyle", "TwoDimensional.belowTopScreenStyle", "Horizontal.topScreenStyle", "Horizontal.belowTopScreenStyle", "Vertical.topScreenStyle", "Vertical.belowTopScreenStyle", "SwipeRightFade.topScreenStyle", "SwipeRightFade.belowTopScreenStyle"], "mappings": "AAA;kBCK;GDK;uBEC;GFO;kBGI;GHK;uBIC;GJO;kBKI;GLK;uBMC;GNO;kBOI;GPK;uBQC;GRO;kBSI;GTQ;uBUC;GVG;kBWI;GXK;uBYC;GZG;kBaI;GbK;uBcC;GdG;kBeI;GfK;uBgBC;GhBG"}}, "type": "js/module"}]}