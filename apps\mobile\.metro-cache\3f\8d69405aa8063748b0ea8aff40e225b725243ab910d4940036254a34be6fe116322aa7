{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  function noop() {}\n\n  // States:\n  //\n  // 0 - pending\n  // 1 - fulfilled with _value\n  // 2 - rejected with _value\n  // 3 - adopted the state of another promise, _value\n  //\n  // once the state is no longer pending (0) it is immutable\n\n  // All `_` prefixed properties will be reduced to `_{random number}`\n  // at build time to obfuscate them and discourage their use.\n  // We don't use symbols or Object.defineProperty to fully hide them\n  // because the performance isn't good enough.\n\n  // to avoid using try/catch inside critical functions, we\n  // extract them to here.\n  var LAST_ERROR = null;\n  var IS_ERROR = {};\n  function getThen(obj) {\n    try {\n      return obj.then;\n    } catch (ex) {\n      LAST_ERROR = ex;\n      return IS_ERROR;\n    }\n  }\n  function tryCallOne(fn, a) {\n    try {\n      return fn(a);\n    } catch (ex) {\n      LAST_ERROR = ex;\n      return IS_ERROR;\n    }\n  }\n  function tryCallTwo(fn, a, b) {\n    try {\n      fn(a, b);\n    } catch (ex) {\n      LAST_ERROR = ex;\n      return IS_ERROR;\n    }\n  }\n  module.exports = Promise;\n  function Promise(fn) {\n    if (typeof this !== 'object') {\n      throw new TypeError('Promises must be constructed via new');\n    }\n    if (typeof fn !== 'function') {\n      throw new TypeError('Promise constructor\\'s argument is not a function');\n    }\n    this._x = 0;\n    this._y = 0;\n    this._z = null;\n    this._A = null;\n    if (fn === noop) return;\n    doResolve(fn, this);\n  }\n  Promise._B = null;\n  Promise._C = null;\n  Promise._D = noop;\n  Promise.prototype.then = function (onFulfilled, onRejected) {\n    if (this.constructor !== Promise) {\n      return safeThen(this, onFulfilled, onRejected);\n    }\n    var res = new Promise(noop);\n    handle(this, new Handler(onFulfilled, onRejected, res));\n    return res;\n  };\n  function safeThen(self, onFulfilled, onRejected) {\n    return new self.constructor(function (resolve, reject) {\n      var res = new Promise(noop);\n      res.then(resolve, reject);\n      handle(self, new Handler(onFulfilled, onRejected, res));\n    });\n  }\n  function handle(self, deferred) {\n    while (self._y === 3) {\n      self = self._z;\n    }\n    if (Promise._B) {\n      Promise._B(self);\n    }\n    if (self._y === 0) {\n      if (self._x === 0) {\n        self._x = 1;\n        self._A = deferred;\n        return;\n      }\n      if (self._x === 1) {\n        self._x = 2;\n        self._A = [self._A, deferred];\n        return;\n      }\n      self._A.push(deferred);\n      return;\n    }\n    handleResolved(self, deferred);\n  }\n  function handleResolved(self, deferred) {\n    setImmediate(function () {\n      var cb = self._y === 1 ? deferred.onFulfilled : deferred.onRejected;\n      if (cb === null) {\n        if (self._y === 1) {\n          resolve(deferred.promise, self._z);\n        } else {\n          reject(deferred.promise, self._z);\n        }\n        return;\n      }\n      var ret = tryCallOne(cb, self._z);\n      if (ret === IS_ERROR) {\n        reject(deferred.promise, LAST_ERROR);\n      } else {\n        resolve(deferred.promise, ret);\n      }\n    });\n  }\n  function resolve(self, newValue) {\n    // Promise Resolution Procedure: https://github.com/promises-aplus/promises-spec#the-promise-resolution-procedure\n    if (newValue === self) {\n      return reject(self, new TypeError('A promise cannot be resolved with itself.'));\n    }\n    if (newValue && (typeof newValue === 'object' || typeof newValue === 'function')) {\n      var then = getThen(newValue);\n      if (then === IS_ERROR) {\n        return reject(self, LAST_ERROR);\n      }\n      if (then === self.then && newValue instanceof Promise) {\n        self._y = 3;\n        self._z = newValue;\n        finale(self);\n        return;\n      } else if (typeof then === 'function') {\n        doResolve(then.bind(newValue), self);\n        return;\n      }\n    }\n    self._y = 1;\n    self._z = newValue;\n    finale(self);\n  }\n  function reject(self, newValue) {\n    self._y = 2;\n    self._z = newValue;\n    if (Promise._C) {\n      Promise._C(self, newValue);\n    }\n    finale(self);\n  }\n  function finale(self) {\n    if (self._x === 1) {\n      handle(self, self._A);\n      self._A = null;\n    }\n    if (self._x === 2) {\n      for (var i = 0; i < self._A.length; i++) {\n        handle(self, self._A[i]);\n      }\n      self._A = null;\n    }\n  }\n  function Handler(onFulfilled, onRejected, promise) {\n    this.onFulfilled = typeof onFulfilled === 'function' ? onFulfilled : null;\n    this.onRejected = typeof onRejected === 'function' ? onRejected : null;\n    this.promise = promise;\n  }\n\n  /**\n   * Take a potentially misbehaving resolver function and make sure\n   * onFulfilled and onRejected are only called once.\n   *\n   * Makes no guarantees about asynchrony.\n   */\n  function doResolve(fn, promise) {\n    var done = false;\n    var res = tryCallTwo(fn, function (value) {\n      if (done) return;\n      done = true;\n      resolve(promise, value);\n    }, function (reason) {\n      if (done) return;\n      done = true;\n      reject(promise, reason);\n    });\n    if (!done && res === IS_ERROR) {\n      done = true;\n      reject(promise, LAST_ERROR);\n    }\n  }\n});", "lineCount": 195, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 5, 0], [4, 11, 5, 9, "noop"], [4, 15, 5, 13, "noop"], [4, 16, 5, 13], [4, 18, 5, 16], [4, 19, 5, 17], [6, 2, 7, 0], [7, 2, 8, 0], [8, 2, 9, 0], [9, 2, 10, 0], [10, 2, 11, 0], [11, 2, 12, 0], [12, 2, 13, 0], [13, 2, 14, 0], [15, 2, 16, 0], [16, 2, 17, 0], [17, 2, 18, 0], [18, 2, 19, 0], [20, 2, 22, 0], [21, 2, 23, 0], [22, 2, 24, 0], [22, 6, 24, 4, "LAST_ERROR"], [22, 16, 24, 14], [22, 19, 24, 17], [22, 23, 24, 21], [23, 2, 25, 0], [23, 6, 25, 4, "IS_ERROR"], [23, 14, 25, 12], [23, 17, 25, 15], [23, 18, 25, 16], [23, 19, 25, 17], [24, 2, 26, 0], [24, 11, 26, 9, "getThen"], [24, 18, 26, 16, "getThen"], [24, 19, 26, 17, "obj"], [24, 22, 26, 20], [24, 24, 26, 22], [25, 4, 27, 2], [25, 8, 27, 6], [26, 6, 28, 4], [26, 13, 28, 11, "obj"], [26, 16, 28, 14], [26, 17, 28, 15, "then"], [26, 21, 28, 19], [27, 4, 29, 2], [27, 5, 29, 3], [27, 6, 29, 4], [27, 13, 29, 11, "ex"], [27, 15, 29, 13], [27, 17, 29, 15], [28, 6, 30, 4, "LAST_ERROR"], [28, 16, 30, 14], [28, 19, 30, 17, "ex"], [28, 21, 30, 19], [29, 6, 31, 4], [29, 13, 31, 11, "IS_ERROR"], [29, 21, 31, 19], [30, 4, 32, 2], [31, 2, 33, 0], [32, 2, 35, 0], [32, 11, 35, 9, "tryCallOne"], [32, 21, 35, 19, "tryCallOne"], [32, 22, 35, 20, "fn"], [32, 24, 35, 22], [32, 26, 35, 24, "a"], [32, 27, 35, 25], [32, 29, 35, 27], [33, 4, 36, 2], [33, 8, 36, 6], [34, 6, 37, 4], [34, 13, 37, 11, "fn"], [34, 15, 37, 13], [34, 16, 37, 14, "a"], [34, 17, 37, 15], [34, 18, 37, 16], [35, 4, 38, 2], [35, 5, 38, 3], [35, 6, 38, 4], [35, 13, 38, 11, "ex"], [35, 15, 38, 13], [35, 17, 38, 15], [36, 6, 39, 4, "LAST_ERROR"], [36, 16, 39, 14], [36, 19, 39, 17, "ex"], [36, 21, 39, 19], [37, 6, 40, 4], [37, 13, 40, 11, "IS_ERROR"], [37, 21, 40, 19], [38, 4, 41, 2], [39, 2, 42, 0], [40, 2, 43, 0], [40, 11, 43, 9, "tryCallTwo"], [40, 21, 43, 19, "tryCallTwo"], [40, 22, 43, 20, "fn"], [40, 24, 43, 22], [40, 26, 43, 24, "a"], [40, 27, 43, 25], [40, 29, 43, 27, "b"], [40, 30, 43, 28], [40, 32, 43, 30], [41, 4, 44, 2], [41, 8, 44, 6], [42, 6, 45, 4, "fn"], [42, 8, 45, 6], [42, 9, 45, 7, "a"], [42, 10, 45, 8], [42, 12, 45, 10, "b"], [42, 13, 45, 11], [42, 14, 45, 12], [43, 4, 46, 2], [43, 5, 46, 3], [43, 6, 46, 4], [43, 13, 46, 11, "ex"], [43, 15, 46, 13], [43, 17, 46, 15], [44, 6, 47, 4, "LAST_ERROR"], [44, 16, 47, 14], [44, 19, 47, 17, "ex"], [44, 21, 47, 19], [45, 6, 48, 4], [45, 13, 48, 11, "IS_ERROR"], [45, 21, 48, 19], [46, 4, 49, 2], [47, 2, 50, 0], [48, 2, 52, 0, "module"], [48, 8, 52, 6], [48, 9, 52, 7, "exports"], [48, 16, 52, 14], [48, 19, 52, 17, "Promise"], [48, 26, 52, 24], [49, 2, 54, 0], [49, 11, 54, 9, "Promise"], [49, 18, 54, 16, "Promise"], [49, 19, 54, 17, "fn"], [49, 21, 54, 19], [49, 23, 54, 21], [50, 4, 55, 2], [50, 8, 55, 6], [50, 15, 55, 13], [50, 19, 55, 17], [50, 24, 55, 22], [50, 32, 55, 30], [50, 34, 55, 32], [51, 6, 56, 4], [51, 12, 56, 10], [51, 16, 56, 14, "TypeError"], [51, 25, 56, 23], [51, 26, 56, 24], [51, 64, 56, 62], [51, 65, 56, 63], [52, 4, 57, 2], [53, 4, 58, 2], [53, 8, 58, 6], [53, 15, 58, 13, "fn"], [53, 17, 58, 15], [53, 22, 58, 20], [53, 32, 58, 30], [53, 34, 58, 32], [54, 6, 59, 4], [54, 12, 59, 10], [54, 16, 59, 14, "TypeError"], [54, 25, 59, 23], [54, 26, 59, 24], [54, 77, 59, 75], [54, 78, 59, 76], [55, 4, 60, 2], [56, 4, 61, 2], [56, 8, 61, 6], [56, 9, 61, 7, "_x"], [56, 11, 61, 9], [56, 14, 61, 12], [56, 15, 61, 13], [57, 4, 62, 2], [57, 8, 62, 6], [57, 9, 62, 7, "_y"], [57, 11, 62, 9], [57, 14, 62, 12], [57, 15, 62, 13], [58, 4, 63, 2], [58, 8, 63, 6], [58, 9, 63, 7, "_z"], [58, 11, 63, 9], [58, 14, 63, 12], [58, 18, 63, 16], [59, 4, 64, 2], [59, 8, 64, 6], [59, 9, 64, 7, "_A"], [59, 11, 64, 9], [59, 14, 64, 12], [59, 18, 64, 16], [60, 4, 65, 2], [60, 8, 65, 6, "fn"], [60, 10, 65, 8], [60, 15, 65, 13, "noop"], [60, 19, 65, 17], [60, 21, 65, 19], [61, 4, 66, 2, "doResolve"], [61, 13, 66, 11], [61, 14, 66, 12, "fn"], [61, 16, 66, 14], [61, 18, 66, 16], [61, 22, 66, 20], [61, 23, 66, 21], [62, 2, 67, 0], [63, 2, 68, 0, "Promise"], [63, 9, 68, 7], [63, 10, 68, 8, "_B"], [63, 12, 68, 10], [63, 15, 68, 13], [63, 19, 68, 17], [64, 2, 69, 0, "Promise"], [64, 9, 69, 7], [64, 10, 69, 8, "_C"], [64, 12, 69, 10], [64, 15, 69, 13], [64, 19, 69, 17], [65, 2, 70, 0, "Promise"], [65, 9, 70, 7], [65, 10, 70, 8, "_D"], [65, 12, 70, 10], [65, 15, 70, 13, "noop"], [65, 19, 70, 17], [66, 2, 72, 0, "Promise"], [66, 9, 72, 7], [66, 10, 72, 8, "prototype"], [66, 19, 72, 17], [66, 20, 72, 18, "then"], [66, 24, 72, 22], [66, 27, 72, 25], [66, 37, 72, 34, "onFulfilled"], [66, 48, 72, 45], [66, 50, 72, 47, "onRejected"], [66, 60, 72, 57], [66, 62, 72, 59], [67, 4, 73, 2], [67, 8, 73, 6], [67, 12, 73, 10], [67, 13, 73, 11, "constructor"], [67, 24, 73, 22], [67, 29, 73, 27, "Promise"], [67, 36, 73, 34], [67, 38, 73, 36], [68, 6, 74, 4], [68, 13, 74, 11, "safeThen"], [68, 21, 74, 19], [68, 22, 74, 20], [68, 26, 74, 24], [68, 28, 74, 26, "onFulfilled"], [68, 39, 74, 37], [68, 41, 74, 39, "onRejected"], [68, 51, 74, 49], [68, 52, 74, 50], [69, 4, 75, 2], [70, 4, 76, 2], [70, 8, 76, 6, "res"], [70, 11, 76, 9], [70, 14, 76, 12], [70, 18, 76, 16, "Promise"], [70, 25, 76, 23], [70, 26, 76, 24, "noop"], [70, 30, 76, 28], [70, 31, 76, 29], [71, 4, 77, 2, "handle"], [71, 10, 77, 8], [71, 11, 77, 9], [71, 15, 77, 13], [71, 17, 77, 15], [71, 21, 77, 19, "Handler"], [71, 28, 77, 26], [71, 29, 77, 27, "onFulfilled"], [71, 40, 77, 38], [71, 42, 77, 40, "onRejected"], [71, 52, 77, 50], [71, 54, 77, 52, "res"], [71, 57, 77, 55], [71, 58, 77, 56], [71, 59, 77, 57], [72, 4, 78, 2], [72, 11, 78, 9, "res"], [72, 14, 78, 12], [73, 2, 79, 0], [73, 3, 79, 1], [74, 2, 81, 0], [74, 11, 81, 9, "safeThen"], [74, 19, 81, 17, "safeThen"], [74, 20, 81, 18, "self"], [74, 24, 81, 22], [74, 26, 81, 24, "onFulfilled"], [74, 37, 81, 35], [74, 39, 81, 37, "onRejected"], [74, 49, 81, 47], [74, 51, 81, 49], [75, 4, 82, 2], [75, 11, 82, 9], [75, 15, 82, 13, "self"], [75, 19, 82, 17], [75, 20, 82, 18, "constructor"], [75, 31, 82, 29], [75, 32, 82, 30], [75, 42, 82, 40, "resolve"], [75, 49, 82, 47], [75, 51, 82, 49, "reject"], [75, 57, 82, 55], [75, 59, 82, 57], [76, 6, 83, 4], [76, 10, 83, 8, "res"], [76, 13, 83, 11], [76, 16, 83, 14], [76, 20, 83, 18, "Promise"], [76, 27, 83, 25], [76, 28, 83, 26, "noop"], [76, 32, 83, 30], [76, 33, 83, 31], [77, 6, 84, 4, "res"], [77, 9, 84, 7], [77, 10, 84, 8, "then"], [77, 14, 84, 12], [77, 15, 84, 13, "resolve"], [77, 22, 84, 20], [77, 24, 84, 22, "reject"], [77, 30, 84, 28], [77, 31, 84, 29], [78, 6, 85, 4, "handle"], [78, 12, 85, 10], [78, 13, 85, 11, "self"], [78, 17, 85, 15], [78, 19, 85, 17], [78, 23, 85, 21, "Handler"], [78, 30, 85, 28], [78, 31, 85, 29, "onFulfilled"], [78, 42, 85, 40], [78, 44, 85, 42, "onRejected"], [78, 54, 85, 52], [78, 56, 85, 54, "res"], [78, 59, 85, 57], [78, 60, 85, 58], [78, 61, 85, 59], [79, 4, 86, 2], [79, 5, 86, 3], [79, 6, 86, 4], [80, 2, 87, 0], [81, 2, 88, 0], [81, 11, 88, 9, "handle"], [81, 17, 88, 15, "handle"], [81, 18, 88, 16, "self"], [81, 22, 88, 20], [81, 24, 88, 22, "deferred"], [81, 32, 88, 30], [81, 34, 88, 32], [82, 4, 89, 2], [82, 11, 89, 9, "self"], [82, 15, 89, 13], [82, 16, 89, 14, "_y"], [82, 18, 89, 16], [82, 23, 89, 21], [82, 24, 89, 22], [82, 26, 89, 24], [83, 6, 90, 4, "self"], [83, 10, 90, 8], [83, 13, 90, 11, "self"], [83, 17, 90, 15], [83, 18, 90, 16, "_z"], [83, 20, 90, 18], [84, 4, 91, 2], [85, 4, 92, 2], [85, 8, 92, 6, "Promise"], [85, 15, 92, 13], [85, 16, 92, 14, "_B"], [85, 18, 92, 16], [85, 20, 92, 18], [86, 6, 93, 4, "Promise"], [86, 13, 93, 11], [86, 14, 93, 12, "_B"], [86, 16, 93, 14], [86, 17, 93, 15, "self"], [86, 21, 93, 19], [86, 22, 93, 20], [87, 4, 94, 2], [88, 4, 95, 2], [88, 8, 95, 6, "self"], [88, 12, 95, 10], [88, 13, 95, 11, "_y"], [88, 15, 95, 13], [88, 20, 95, 18], [88, 21, 95, 19], [88, 23, 95, 21], [89, 6, 96, 4], [89, 10, 96, 8, "self"], [89, 14, 96, 12], [89, 15, 96, 13, "_x"], [89, 17, 96, 15], [89, 22, 96, 20], [89, 23, 96, 21], [89, 25, 96, 23], [90, 8, 97, 6, "self"], [90, 12, 97, 10], [90, 13, 97, 11, "_x"], [90, 15, 97, 13], [90, 18, 97, 16], [90, 19, 97, 17], [91, 8, 98, 6, "self"], [91, 12, 98, 10], [91, 13, 98, 11, "_A"], [91, 15, 98, 13], [91, 18, 98, 16, "deferred"], [91, 26, 98, 24], [92, 8, 99, 6], [93, 6, 100, 4], [94, 6, 101, 4], [94, 10, 101, 8, "self"], [94, 14, 101, 12], [94, 15, 101, 13, "_x"], [94, 17, 101, 15], [94, 22, 101, 20], [94, 23, 101, 21], [94, 25, 101, 23], [95, 8, 102, 6, "self"], [95, 12, 102, 10], [95, 13, 102, 11, "_x"], [95, 15, 102, 13], [95, 18, 102, 16], [95, 19, 102, 17], [96, 8, 103, 6, "self"], [96, 12, 103, 10], [96, 13, 103, 11, "_A"], [96, 15, 103, 13], [96, 18, 103, 16], [96, 19, 103, 17, "self"], [96, 23, 103, 21], [96, 24, 103, 22, "_A"], [96, 26, 103, 24], [96, 28, 103, 26, "deferred"], [96, 36, 103, 34], [96, 37, 103, 35], [97, 8, 104, 6], [98, 6, 105, 4], [99, 6, 106, 4, "self"], [99, 10, 106, 8], [99, 11, 106, 9, "_A"], [99, 13, 106, 11], [99, 14, 106, 12, "push"], [99, 18, 106, 16], [99, 19, 106, 17, "deferred"], [99, 27, 106, 25], [99, 28, 106, 26], [100, 6, 107, 4], [101, 4, 108, 2], [102, 4, 109, 2, "handleResolved"], [102, 18, 109, 16], [102, 19, 109, 17, "self"], [102, 23, 109, 21], [102, 25, 109, 23, "deferred"], [102, 33, 109, 31], [102, 34, 109, 32], [103, 2, 110, 0], [104, 2, 112, 0], [104, 11, 112, 9, "handleResolved"], [104, 25, 112, 23, "handleResolved"], [104, 26, 112, 24, "self"], [104, 30, 112, 28], [104, 32, 112, 30, "deferred"], [104, 40, 112, 38], [104, 42, 112, 40], [105, 4, 113, 2, "setImmediate"], [105, 16, 113, 14], [105, 17, 113, 15], [105, 29, 113, 26], [106, 6, 114, 4], [106, 10, 114, 8, "cb"], [106, 12, 114, 10], [106, 15, 114, 13, "self"], [106, 19, 114, 17], [106, 20, 114, 18, "_y"], [106, 22, 114, 20], [106, 27, 114, 25], [106, 28, 114, 26], [106, 31, 114, 29, "deferred"], [106, 39, 114, 37], [106, 40, 114, 38, "onFulfilled"], [106, 51, 114, 49], [106, 54, 114, 52, "deferred"], [106, 62, 114, 60], [106, 63, 114, 61, "onRejected"], [106, 73, 114, 71], [107, 6, 115, 4], [107, 10, 115, 8, "cb"], [107, 12, 115, 10], [107, 17, 115, 15], [107, 21, 115, 19], [107, 23, 115, 21], [108, 8, 116, 6], [108, 12, 116, 10, "self"], [108, 16, 116, 14], [108, 17, 116, 15, "_y"], [108, 19, 116, 17], [108, 24, 116, 22], [108, 25, 116, 23], [108, 27, 116, 25], [109, 10, 117, 8, "resolve"], [109, 17, 117, 15], [109, 18, 117, 16, "deferred"], [109, 26, 117, 24], [109, 27, 117, 25, "promise"], [109, 34, 117, 32], [109, 36, 117, 34, "self"], [109, 40, 117, 38], [109, 41, 117, 39, "_z"], [109, 43, 117, 41], [109, 44, 117, 42], [110, 8, 118, 6], [110, 9, 118, 7], [110, 15, 118, 13], [111, 10, 119, 8, "reject"], [111, 16, 119, 14], [111, 17, 119, 15, "deferred"], [111, 25, 119, 23], [111, 26, 119, 24, "promise"], [111, 33, 119, 31], [111, 35, 119, 33, "self"], [111, 39, 119, 37], [111, 40, 119, 38, "_z"], [111, 42, 119, 40], [111, 43, 119, 41], [112, 8, 120, 6], [113, 8, 121, 6], [114, 6, 122, 4], [115, 6, 123, 4], [115, 10, 123, 8, "ret"], [115, 13, 123, 11], [115, 16, 123, 14, "tryCallOne"], [115, 26, 123, 24], [115, 27, 123, 25, "cb"], [115, 29, 123, 27], [115, 31, 123, 29, "self"], [115, 35, 123, 33], [115, 36, 123, 34, "_z"], [115, 38, 123, 36], [115, 39, 123, 37], [116, 6, 124, 4], [116, 10, 124, 8, "ret"], [116, 13, 124, 11], [116, 18, 124, 16, "IS_ERROR"], [116, 26, 124, 24], [116, 28, 124, 26], [117, 8, 125, 6, "reject"], [117, 14, 125, 12], [117, 15, 125, 13, "deferred"], [117, 23, 125, 21], [117, 24, 125, 22, "promise"], [117, 31, 125, 29], [117, 33, 125, 31, "LAST_ERROR"], [117, 43, 125, 41], [117, 44, 125, 42], [118, 6, 126, 4], [118, 7, 126, 5], [118, 13, 126, 11], [119, 8, 127, 6, "resolve"], [119, 15, 127, 13], [119, 16, 127, 14, "deferred"], [119, 24, 127, 22], [119, 25, 127, 23, "promise"], [119, 32, 127, 30], [119, 34, 127, 32, "ret"], [119, 37, 127, 35], [119, 38, 127, 36], [120, 6, 128, 4], [121, 4, 129, 2], [121, 5, 129, 3], [121, 6, 129, 4], [122, 2, 130, 0], [123, 2, 131, 0], [123, 11, 131, 9, "resolve"], [123, 18, 131, 16, "resolve"], [123, 19, 131, 17, "self"], [123, 23, 131, 21], [123, 25, 131, 23, "newValue"], [123, 33, 131, 31], [123, 35, 131, 33], [124, 4, 132, 2], [125, 4, 133, 2], [125, 8, 133, 6, "newValue"], [125, 16, 133, 14], [125, 21, 133, 19, "self"], [125, 25, 133, 23], [125, 27, 133, 25], [126, 6, 134, 4], [126, 13, 134, 11, "reject"], [126, 19, 134, 17], [126, 20, 135, 6, "self"], [126, 24, 135, 10], [126, 26, 136, 6], [126, 30, 136, 10, "TypeError"], [126, 39, 136, 19], [126, 40, 136, 20], [126, 83, 136, 63], [126, 84, 137, 4], [126, 85, 137, 5], [127, 4, 138, 2], [128, 4, 139, 2], [128, 8, 140, 4, "newValue"], [128, 16, 140, 12], [128, 21, 141, 5], [128, 28, 141, 12, "newValue"], [128, 36, 141, 20], [128, 41, 141, 25], [128, 49, 141, 33], [128, 53, 141, 37], [128, 60, 141, 44, "newValue"], [128, 68, 141, 52], [128, 73, 141, 57], [128, 83, 141, 67], [128, 84, 141, 68], [128, 86, 142, 4], [129, 6, 143, 4], [129, 10, 143, 8, "then"], [129, 14, 143, 12], [129, 17, 143, 15, "getThen"], [129, 24, 143, 22], [129, 25, 143, 23, "newValue"], [129, 33, 143, 31], [129, 34, 143, 32], [130, 6, 144, 4], [130, 10, 144, 8, "then"], [130, 14, 144, 12], [130, 19, 144, 17, "IS_ERROR"], [130, 27, 144, 25], [130, 29, 144, 27], [131, 8, 145, 6], [131, 15, 145, 13, "reject"], [131, 21, 145, 19], [131, 22, 145, 20, "self"], [131, 26, 145, 24], [131, 28, 145, 26, "LAST_ERROR"], [131, 38, 145, 36], [131, 39, 145, 37], [132, 6, 146, 4], [133, 6, 147, 4], [133, 10, 148, 6, "then"], [133, 14, 148, 10], [133, 19, 148, 15, "self"], [133, 23, 148, 19], [133, 24, 148, 20, "then"], [133, 28, 148, 24], [133, 32, 149, 6, "newValue"], [133, 40, 149, 14], [133, 52, 149, 26, "Promise"], [133, 59, 149, 33], [133, 61, 150, 6], [134, 8, 151, 6, "self"], [134, 12, 151, 10], [134, 13, 151, 11, "_y"], [134, 15, 151, 13], [134, 18, 151, 16], [134, 19, 151, 17], [135, 8, 152, 6, "self"], [135, 12, 152, 10], [135, 13, 152, 11, "_z"], [135, 15, 152, 13], [135, 18, 152, 16, "newValue"], [135, 26, 152, 24], [136, 8, 153, 6, "finale"], [136, 14, 153, 12], [136, 15, 153, 13, "self"], [136, 19, 153, 17], [136, 20, 153, 18], [137, 8, 154, 6], [138, 6, 155, 4], [138, 7, 155, 5], [138, 13, 155, 11], [138, 17, 155, 15], [138, 24, 155, 22, "then"], [138, 28, 155, 26], [138, 33, 155, 31], [138, 43, 155, 41], [138, 45, 155, 43], [139, 8, 156, 6, "doResolve"], [139, 17, 156, 15], [139, 18, 156, 16, "then"], [139, 22, 156, 20], [139, 23, 156, 21, "bind"], [139, 27, 156, 25], [139, 28, 156, 26, "newValue"], [139, 36, 156, 34], [139, 37, 156, 35], [139, 39, 156, 37, "self"], [139, 43, 156, 41], [139, 44, 156, 42], [140, 8, 157, 6], [141, 6, 158, 4], [142, 4, 159, 2], [143, 4, 160, 2, "self"], [143, 8, 160, 6], [143, 9, 160, 7, "_y"], [143, 11, 160, 9], [143, 14, 160, 12], [143, 15, 160, 13], [144, 4, 161, 2, "self"], [144, 8, 161, 6], [144, 9, 161, 7, "_z"], [144, 11, 161, 9], [144, 14, 161, 12, "newValue"], [144, 22, 161, 20], [145, 4, 162, 2, "finale"], [145, 10, 162, 8], [145, 11, 162, 9, "self"], [145, 15, 162, 13], [145, 16, 162, 14], [146, 2, 163, 0], [147, 2, 165, 0], [147, 11, 165, 9, "reject"], [147, 17, 165, 15, "reject"], [147, 18, 165, 16, "self"], [147, 22, 165, 20], [147, 24, 165, 22, "newValue"], [147, 32, 165, 30], [147, 34, 165, 32], [148, 4, 166, 2, "self"], [148, 8, 166, 6], [148, 9, 166, 7, "_y"], [148, 11, 166, 9], [148, 14, 166, 12], [148, 15, 166, 13], [149, 4, 167, 2, "self"], [149, 8, 167, 6], [149, 9, 167, 7, "_z"], [149, 11, 167, 9], [149, 14, 167, 12, "newValue"], [149, 22, 167, 20], [150, 4, 168, 2], [150, 8, 168, 6, "Promise"], [150, 15, 168, 13], [150, 16, 168, 14, "_C"], [150, 18, 168, 16], [150, 20, 168, 18], [151, 6, 169, 4, "Promise"], [151, 13, 169, 11], [151, 14, 169, 12, "_C"], [151, 16, 169, 14], [151, 17, 169, 15, "self"], [151, 21, 169, 19], [151, 23, 169, 21, "newValue"], [151, 31, 169, 29], [151, 32, 169, 30], [152, 4, 170, 2], [153, 4, 171, 2, "finale"], [153, 10, 171, 8], [153, 11, 171, 9, "self"], [153, 15, 171, 13], [153, 16, 171, 14], [154, 2, 172, 0], [155, 2, 173, 0], [155, 11, 173, 9, "finale"], [155, 17, 173, 15, "finale"], [155, 18, 173, 16, "self"], [155, 22, 173, 20], [155, 24, 173, 22], [156, 4, 174, 2], [156, 8, 174, 6, "self"], [156, 12, 174, 10], [156, 13, 174, 11, "_x"], [156, 15, 174, 13], [156, 20, 174, 18], [156, 21, 174, 19], [156, 23, 174, 21], [157, 6, 175, 4, "handle"], [157, 12, 175, 10], [157, 13, 175, 11, "self"], [157, 17, 175, 15], [157, 19, 175, 17, "self"], [157, 23, 175, 21], [157, 24, 175, 22, "_A"], [157, 26, 175, 24], [157, 27, 175, 25], [158, 6, 176, 4, "self"], [158, 10, 176, 8], [158, 11, 176, 9, "_A"], [158, 13, 176, 11], [158, 16, 176, 14], [158, 20, 176, 18], [159, 4, 177, 2], [160, 4, 178, 2], [160, 8, 178, 6, "self"], [160, 12, 178, 10], [160, 13, 178, 11, "_x"], [160, 15, 178, 13], [160, 20, 178, 18], [160, 21, 178, 19], [160, 23, 178, 21], [161, 6, 179, 4], [161, 11, 179, 9], [161, 15, 179, 13, "i"], [161, 16, 179, 14], [161, 19, 179, 17], [161, 20, 179, 18], [161, 22, 179, 20, "i"], [161, 23, 179, 21], [161, 26, 179, 24, "self"], [161, 30, 179, 28], [161, 31, 179, 29, "_A"], [161, 33, 179, 31], [161, 34, 179, 32, "length"], [161, 40, 179, 38], [161, 42, 179, 40, "i"], [161, 43, 179, 41], [161, 45, 179, 43], [161, 47, 179, 45], [162, 8, 180, 6, "handle"], [162, 14, 180, 12], [162, 15, 180, 13, "self"], [162, 19, 180, 17], [162, 21, 180, 19, "self"], [162, 25, 180, 23], [162, 26, 180, 24, "_A"], [162, 28, 180, 26], [162, 29, 180, 27, "i"], [162, 30, 180, 28], [162, 31, 180, 29], [162, 32, 180, 30], [163, 6, 181, 4], [164, 6, 182, 4, "self"], [164, 10, 182, 8], [164, 11, 182, 9, "_A"], [164, 13, 182, 11], [164, 16, 182, 14], [164, 20, 182, 18], [165, 4, 183, 2], [166, 2, 184, 0], [167, 2, 186, 0], [167, 11, 186, 9, "Handler"], [167, 18, 186, 16, "Handler"], [167, 19, 186, 17, "onFulfilled"], [167, 30, 186, 28], [167, 32, 186, 30, "onRejected"], [167, 42, 186, 40], [167, 44, 186, 42, "promise"], [167, 51, 186, 49], [167, 53, 186, 50], [168, 4, 187, 2], [168, 8, 187, 6], [168, 9, 187, 7, "onFulfilled"], [168, 20, 187, 18], [168, 23, 187, 21], [168, 30, 187, 28, "onFulfilled"], [168, 41, 187, 39], [168, 46, 187, 44], [168, 56, 187, 54], [168, 59, 187, 57, "onFulfilled"], [168, 70, 187, 68], [168, 73, 187, 71], [168, 77, 187, 75], [169, 4, 188, 2], [169, 8, 188, 6], [169, 9, 188, 7, "onRejected"], [169, 19, 188, 17], [169, 22, 188, 20], [169, 29, 188, 27, "onRejected"], [169, 39, 188, 37], [169, 44, 188, 42], [169, 54, 188, 52], [169, 57, 188, 55, "onRejected"], [169, 67, 188, 65], [169, 70, 188, 68], [169, 74, 188, 72], [170, 4, 189, 2], [170, 8, 189, 6], [170, 9, 189, 7, "promise"], [170, 16, 189, 14], [170, 19, 189, 17, "promise"], [170, 26, 189, 24], [171, 2, 190, 0], [173, 2, 192, 0], [174, 0, 193, 0], [175, 0, 194, 0], [176, 0, 195, 0], [177, 0, 196, 0], [178, 0, 197, 0], [179, 2, 198, 0], [179, 11, 198, 9, "doResolve"], [179, 20, 198, 18, "doResolve"], [179, 21, 198, 19, "fn"], [179, 23, 198, 21], [179, 25, 198, 23, "promise"], [179, 32, 198, 30], [179, 34, 198, 32], [180, 4, 199, 2], [180, 8, 199, 6, "done"], [180, 12, 199, 10], [180, 15, 199, 13], [180, 20, 199, 18], [181, 4, 200, 2], [181, 8, 200, 6, "res"], [181, 11, 200, 9], [181, 14, 200, 12, "tryCallTwo"], [181, 24, 200, 22], [181, 25, 200, 23, "fn"], [181, 27, 200, 25], [181, 29, 200, 27], [181, 39, 200, 37, "value"], [181, 44, 200, 42], [181, 46, 200, 44], [182, 6, 201, 4], [182, 10, 201, 8, "done"], [182, 14, 201, 12], [182, 16, 201, 14], [183, 6, 202, 4, "done"], [183, 10, 202, 8], [183, 13, 202, 11], [183, 17, 202, 15], [184, 6, 203, 4, "resolve"], [184, 13, 203, 11], [184, 14, 203, 12, "promise"], [184, 21, 203, 19], [184, 23, 203, 21, "value"], [184, 28, 203, 26], [184, 29, 203, 27], [185, 4, 204, 2], [185, 5, 204, 3], [185, 7, 204, 5], [185, 17, 204, 15, "reason"], [185, 23, 204, 21], [185, 25, 204, 23], [186, 6, 205, 4], [186, 10, 205, 8, "done"], [186, 14, 205, 12], [186, 16, 205, 14], [187, 6, 206, 4, "done"], [187, 10, 206, 8], [187, 13, 206, 11], [187, 17, 206, 15], [188, 6, 207, 4, "reject"], [188, 12, 207, 10], [188, 13, 207, 11, "promise"], [188, 20, 207, 18], [188, 22, 207, 20, "reason"], [188, 28, 207, 26], [188, 29, 207, 27], [189, 4, 208, 2], [189, 5, 208, 3], [189, 6, 208, 4], [190, 4, 209, 2], [190, 8, 209, 6], [190, 9, 209, 7, "done"], [190, 13, 209, 11], [190, 17, 209, 15, "res"], [190, 20, 209, 18], [190, 25, 209, 23, "IS_ERROR"], [190, 33, 209, 31], [190, 35, 209, 33], [191, 6, 210, 4, "done"], [191, 10, 210, 8], [191, 13, 210, 11], [191, 17, 210, 15], [192, 6, 211, 4, "reject"], [192, 12, 211, 10], [192, 13, 211, 11, "promise"], [192, 20, 211, 18], [192, 22, 211, 20, "LAST_ERROR"], [192, 32, 211, 30], [192, 33, 211, 31], [193, 4, 212, 2], [194, 2, 213, 0], [195, 0, 213, 1], [195, 3]], "functionMap": {"names": ["<global>", "noop", "getThen", "tryCallOne", "tryCallTwo", "Promise", "Promise.prototype.then", "safeThen", "self.constructor$argument_0", "handle", "handleResolved", "setImmediate$argument_0", "resolve", "reject", "finale", "Handler", "doResolve", "tryCallTwo$argument_1", "tryCallTwo$argument_2"], "mappings": "AAA;ACI,kBD;AEqB;CFO;AGE;CHO;AIC;CJO;AKI;CLa;yBMK;CNO;AOE;8BCC;GDI;CPC;ASC;CTsB;AUE;eCC;GDgB;CVC;AYC;CZgC;AaE;CbO;AcC;CdW;AeE;CfI;AgBQ;2BCE;GDI,EE;GFI;ChBK"}}, "type": "js/module"}]}