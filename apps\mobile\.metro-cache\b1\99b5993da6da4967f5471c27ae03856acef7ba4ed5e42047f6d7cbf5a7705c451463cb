{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 67}, "end": {"line": 3, "column": 50, "index": 117}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 119}, "end": {"line": 5, "column": 36, "index": 155}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "../GestureButtons", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 156}, "end": {"line": 6, "column": 47, "index": 203}}], "key": "v8c+tPX/dHOXwLqylweYjN0bYwE=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.TOUCHABLE_STATE = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var React = _react;\n  var _reactNative = require(_dependencyMap[7], \"react-native\");\n  var _State = require(_dependencyMap[8], \"../../State\");\n  var _GestureButtons = require(_dependencyMap[9], \"../GestureButtons\");\n  var _jsxDevRuntime = require(_dependencyMap[10], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\tap2go\\\\node_modules\\\\react-native-gesture-handler\\\\src\\\\components\\\\touchables\\\\GenericTouchable.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  /**\n   * Each touchable is a states' machine which preforms transitions.\n   * On very beginning (and on the very end or recognition) touchable is\n   * UNDETERMINED. Then it moves to BEGAN. If touchable recognizes that finger\n   * travel outside it transits to special MOVED_OUTSIDE state. Gesture recognition\n   * finishes in UNDETERMINED state.\n   */\n  var TOUCHABLE_STATE = exports.TOUCHABLE_STATE = {\n    UNDETERMINED: 0,\n    BEGAN: 1,\n    MOVED_OUTSIDE: 2\n  };\n\n  // TODO: maybe can be better\n  // TODO: all clearTimeout have ! added, maybe they shouldn't ?\n  /**\n   * GenericTouchable is not intented to be used as it is.\n   * Should be treated as a source for the rest of touchables\n   */\n  var GenericTouchable = exports.default = /*#__PURE__*/function (_Component) {\n    function GenericTouchable() {\n      var _this;\n      (0, _classCallCheck2.default)(this, GenericTouchable);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, GenericTouchable, [...args]);\n      // This flag is required since recognition of longPress implies not-invoking onPress\n      _this.longPressDetected = false;\n      _this.pointerInside = true;\n      // State of touchable\n      _this.STATE = TOUCHABLE_STATE.UNDETERMINED;\n      _this.onGestureEvent = _ref => {\n        var pointerInside = _ref.nativeEvent.pointerInside;\n        if (_this.pointerInside !== pointerInside) {\n          if (pointerInside) {\n            _this.onMoveIn();\n          } else {\n            _this.onMoveOut();\n          }\n        }\n        _this.pointerInside = pointerInside;\n      };\n      _this.onHandlerStateChange = _ref2 => {\n        var nativeEvent = _ref2.nativeEvent;\n        var state = nativeEvent.state;\n        if (state === _State.State.CANCELLED || state === _State.State.FAILED) {\n          // Need to handle case with external cancellation (e.g. by ScrollView)\n          _this.moveToState(TOUCHABLE_STATE.UNDETERMINED);\n        } else if (\n        // This platform check is an implication of slightly different behavior of handlers on different platform.\n        // And Android \"Active\" state is achieving on first move of a finger, not on press in.\n        // On iOS event on \"Began\" is not delivered.\n        state === (_reactNative.Platform.OS !== 'android' ? _State.State.ACTIVE : _State.State.BEGAN) && _this.STATE === TOUCHABLE_STATE.UNDETERMINED) {\n          // Moving inside requires\n          _this.handlePressIn();\n        } else if (state === _State.State.END) {\n          var shouldCallOnPress = !_this.longPressDetected && _this.STATE !== TOUCHABLE_STATE.MOVED_OUTSIDE && _this.pressOutTimeout === null;\n          _this.handleGoToUndetermined();\n          if (shouldCallOnPress) {\n            // Calls only inside component whether no long press was called previously\n            _this.props.onPress?.();\n          }\n        }\n      };\n      _this.onLongPressDetected = () => {\n        _this.longPressDetected = true;\n        // Checked for in the caller of `onLongPressDetected`, but better to check twice\n        _this.props.onLongPress?.();\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(GenericTouchable, _Component);\n    return (0, _createClass2.default)(GenericTouchable, [{\n      key: \"handlePressIn\",\n      value:\n      // handlePressIn in called on first touch on traveling inside component.\n      // Handles state transition with delay.\n      function handlePressIn() {\n        if (this.props.delayPressIn) {\n          this.pressInTimeout = setTimeout(() => {\n            this.moveToState(TOUCHABLE_STATE.BEGAN);\n            this.pressInTimeout = null;\n          }, this.props.delayPressIn);\n        } else {\n          this.moveToState(TOUCHABLE_STATE.BEGAN);\n        }\n        if (this.props.onLongPress) {\n          var time = (this.props.delayPressIn || 0) + (this.props.delayLongPress || 0);\n          this.longPressTimeout = setTimeout(this.onLongPressDetected, time);\n        }\n      }\n      // handleMoveOutside in called on traveling outside component.\n      // Handles state transition with delay.\n    }, {\n      key: \"handleMoveOutside\",\n      value: function handleMoveOutside() {\n        if (this.props.delayPressOut) {\n          this.pressOutTimeout = this.pressOutTimeout || setTimeout(() => {\n            this.moveToState(TOUCHABLE_STATE.MOVED_OUTSIDE);\n            this.pressOutTimeout = null;\n          }, this.props.delayPressOut);\n        } else {\n          this.moveToState(TOUCHABLE_STATE.MOVED_OUTSIDE);\n        }\n      }\n\n      // handleGoToUndetermined transits to UNDETERMINED state with proper delay\n    }, {\n      key: \"handleGoToUndetermined\",\n      value: function handleGoToUndetermined() {\n        clearTimeout(this.pressOutTimeout); // TODO: maybe it can be undefined\n        if (this.props.delayPressOut) {\n          this.pressOutTimeout = setTimeout(() => {\n            if (this.STATE === TOUCHABLE_STATE.UNDETERMINED) {\n              this.moveToState(TOUCHABLE_STATE.BEGAN);\n            }\n            this.moveToState(TOUCHABLE_STATE.UNDETERMINED);\n            this.pressOutTimeout = null;\n          }, this.props.delayPressOut);\n        } else {\n          if (this.STATE === TOUCHABLE_STATE.UNDETERMINED) {\n            this.moveToState(TOUCHABLE_STATE.BEGAN);\n          }\n          this.moveToState(TOUCHABLE_STATE.UNDETERMINED);\n        }\n      }\n    }, {\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        this.reset();\n      }\n      // Reset timeout to prevent memory leaks.\n    }, {\n      key: \"reset\",\n      value: function reset() {\n        this.longPressDetected = false;\n        this.pointerInside = true;\n        clearTimeout(this.pressInTimeout);\n        clearTimeout(this.pressOutTimeout);\n        clearTimeout(this.longPressTimeout);\n        this.pressOutTimeout = null;\n        this.longPressTimeout = null;\n        this.pressInTimeout = null;\n      }\n\n      // All states' transitions are defined here.\n    }, {\n      key: \"moveToState\",\n      value: function moveToState(newState) {\n        if (newState === this.STATE) {\n          // Ignore dummy transitions\n          return;\n        }\n        if (newState === TOUCHABLE_STATE.BEGAN) {\n          // First touch and moving inside\n          this.props.onPressIn?.();\n        } else if (newState === TOUCHABLE_STATE.MOVED_OUTSIDE) {\n          // Moving outside\n          this.props.onPressOut?.();\n        } else if (newState === TOUCHABLE_STATE.UNDETERMINED) {\n          // Need to reset each time on transition to UNDETERMINED\n          this.reset();\n          if (this.STATE === TOUCHABLE_STATE.BEGAN) {\n            // ... and if it happens inside button.\n            this.props.onPressOut?.();\n          }\n        }\n        // Finally call lister (used by subclasses)\n        this.props.onStateChange?.(this.STATE, newState);\n        // ... and make transition.\n        this.STATE = newState;\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        // To prevent memory leaks\n        this.reset();\n      }\n    }, {\n      key: \"onMoveIn\",\n      value: function onMoveIn() {\n        if (this.STATE === TOUCHABLE_STATE.MOVED_OUTSIDE) {\n          // This call is not throttled with delays (like in RN's implementation).\n          this.moveToState(TOUCHABLE_STATE.BEGAN);\n        }\n      }\n    }, {\n      key: \"onMoveOut\",\n      value: function onMoveOut() {\n        // Long press should no longer be detected\n        clearTimeout(this.longPressTimeout);\n        this.longPressTimeout = null;\n        if (this.STATE === TOUCHABLE_STATE.BEGAN) {\n          this.handleMoveOutside();\n        }\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var hitSlop = (typeof this.props.hitSlop === 'number' ? {\n          top: this.props.hitSlop,\n          left: this.props.hitSlop,\n          bottom: this.props.hitSlop,\n          right: this.props.hitSlop\n        } : this.props.hitSlop) ?? undefined;\n        var coreProps = {\n          accessible: this.props.accessible !== false,\n          accessibilityLabel: this.props.accessibilityLabel,\n          accessibilityHint: this.props.accessibilityHint,\n          accessibilityRole: this.props.accessibilityRole,\n          // TODO: check if changed to no 's' correctly, also removed 2 props that are no longer available: `accessibilityComponentType` and `accessibilityTraits`,\n          // would be good to check if it is ok for sure, see: https://github.com/facebook/react-native/issues/24016\n          accessibilityState: this.props.accessibilityState,\n          accessibilityActions: this.props.accessibilityActions,\n          onAccessibilityAction: this.props.onAccessibilityAction,\n          nativeID: this.props.nativeID,\n          onLayout: this.props.onLayout\n        };\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_GestureButtons.BaseButton, {\n          style: this.props.containerStyle,\n          onHandlerStateChange:\n          // TODO: not sure if it can be undefined instead of null\n          this.props.disabled ? undefined : this.onHandlerStateChange,\n          onGestureEvent: this.onGestureEvent,\n          hitSlop: hitSlop,\n          userSelect: this.props.userSelect,\n          shouldActivateOnStart: this.props.shouldActivateOnStart,\n          disallowInterruption: this.props.disallowInterruption,\n          testID: this.props.testID,\n          touchSoundDisabled: this.props.touchSoundDisabled ?? false,\n          enabled: !this.props.disabled,\n          ...this.props.extraButtonProps,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Animated.View, {\n            ...coreProps,\n            style: this.props.style,\n            children: this.props.children\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 7\n        }, this);\n      }\n    }]);\n  }(_react.Component);\n  GenericTouchable.defaultProps = {\n    delayLongPress: 600,\n    extraButtonProps: {\n      rippleColor: 'transparent',\n      exclusive: true\n    }\n  };\n});", "lineCount": 279, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_react"], [12, 12, 1, 0], [12, 15, 1, 0, "_interopRequireWildcard"], [12, 38, 1, 0], [12, 39, 1, 0, "require"], [12, 46, 1, 0], [12, 47, 1, 0, "_dependencyMap"], [12, 61, 1, 0], [13, 2, 1, 31], [13, 6, 1, 31, "React"], [13, 11, 1, 31], [13, 14, 1, 31, "_react"], [13, 20, 1, 31], [14, 2, 3, 0], [14, 6, 3, 0, "_reactNative"], [14, 18, 3, 0], [14, 21, 3, 0, "require"], [14, 28, 3, 0], [14, 29, 3, 0, "_dependencyMap"], [14, 43, 3, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_State"], [15, 12, 5, 0], [15, 15, 5, 0, "require"], [15, 22, 5, 0], [15, 23, 5, 0, "_dependencyMap"], [15, 37, 5, 0], [16, 2, 6, 0], [16, 6, 6, 0, "_GestureButtons"], [16, 21, 6, 0], [16, 24, 6, 0, "require"], [16, 31, 6, 0], [16, 32, 6, 0, "_dependencyMap"], [16, 46, 6, 0], [17, 2, 6, 47], [17, 6, 6, 47, "_jsxDevRuntime"], [17, 20, 6, 47], [17, 23, 6, 47, "require"], [17, 30, 6, 47], [17, 31, 6, 47, "_dependencyMap"], [17, 45, 6, 47], [18, 2, 6, 47], [18, 6, 6, 47, "_jsxFileName"], [18, 18, 6, 47], [19, 2, 6, 47], [19, 11, 6, 47, "_interopRequireWildcard"], [19, 35, 6, 47, "e"], [19, 36, 6, 47], [19, 38, 6, 47, "t"], [19, 39, 6, 47], [19, 68, 6, 47, "WeakMap"], [19, 75, 6, 47], [19, 81, 6, 47, "r"], [19, 82, 6, 47], [19, 89, 6, 47, "WeakMap"], [19, 96, 6, 47], [19, 100, 6, 47, "n"], [19, 101, 6, 47], [19, 108, 6, 47, "WeakMap"], [19, 115, 6, 47], [19, 127, 6, 47, "_interopRequireWildcard"], [19, 150, 6, 47], [19, 162, 6, 47, "_interopRequireWildcard"], [19, 163, 6, 47, "e"], [19, 164, 6, 47], [19, 166, 6, 47, "t"], [19, 167, 6, 47], [19, 176, 6, 47, "t"], [19, 177, 6, 47], [19, 181, 6, 47, "e"], [19, 182, 6, 47], [19, 186, 6, 47, "e"], [19, 187, 6, 47], [19, 188, 6, 47, "__esModule"], [19, 198, 6, 47], [19, 207, 6, 47, "e"], [19, 208, 6, 47], [19, 214, 6, 47, "o"], [19, 215, 6, 47], [19, 217, 6, 47, "i"], [19, 218, 6, 47], [19, 220, 6, 47, "f"], [19, 221, 6, 47], [19, 226, 6, 47, "__proto__"], [19, 235, 6, 47], [19, 243, 6, 47, "default"], [19, 250, 6, 47], [19, 252, 6, 47, "e"], [19, 253, 6, 47], [19, 270, 6, 47, "e"], [19, 271, 6, 47], [19, 294, 6, 47, "e"], [19, 295, 6, 47], [19, 320, 6, 47, "e"], [19, 321, 6, 47], [19, 330, 6, 47, "f"], [19, 331, 6, 47], [19, 337, 6, 47, "o"], [19, 338, 6, 47], [19, 341, 6, 47, "t"], [19, 342, 6, 47], [19, 345, 6, 47, "n"], [19, 346, 6, 47], [19, 349, 6, 47, "r"], [19, 350, 6, 47], [19, 358, 6, 47, "o"], [19, 359, 6, 47], [19, 360, 6, 47, "has"], [19, 363, 6, 47], [19, 364, 6, 47, "e"], [19, 365, 6, 47], [19, 375, 6, 47, "o"], [19, 376, 6, 47], [19, 377, 6, 47, "get"], [19, 380, 6, 47], [19, 381, 6, 47, "e"], [19, 382, 6, 47], [19, 385, 6, 47, "o"], [19, 386, 6, 47], [19, 387, 6, 47, "set"], [19, 390, 6, 47], [19, 391, 6, 47, "e"], [19, 392, 6, 47], [19, 394, 6, 47, "f"], [19, 395, 6, 47], [19, 409, 6, 47, "_t"], [19, 411, 6, 47], [19, 415, 6, 47, "e"], [19, 416, 6, 47], [19, 432, 6, 47, "_t"], [19, 434, 6, 47], [19, 441, 6, 47, "hasOwnProperty"], [19, 455, 6, 47], [19, 456, 6, 47, "call"], [19, 460, 6, 47], [19, 461, 6, 47, "e"], [19, 462, 6, 47], [19, 464, 6, 47, "_t"], [19, 466, 6, 47], [19, 473, 6, 47, "i"], [19, 474, 6, 47], [19, 478, 6, 47, "o"], [19, 479, 6, 47], [19, 482, 6, 47, "Object"], [19, 488, 6, 47], [19, 489, 6, 47, "defineProperty"], [19, 503, 6, 47], [19, 508, 6, 47, "Object"], [19, 514, 6, 47], [19, 515, 6, 47, "getOwnPropertyDescriptor"], [19, 539, 6, 47], [19, 540, 6, 47, "e"], [19, 541, 6, 47], [19, 543, 6, 47, "_t"], [19, 545, 6, 47], [19, 552, 6, 47, "i"], [19, 553, 6, 47], [19, 554, 6, 47, "get"], [19, 557, 6, 47], [19, 561, 6, 47, "i"], [19, 562, 6, 47], [19, 563, 6, 47, "set"], [19, 566, 6, 47], [19, 570, 6, 47, "o"], [19, 571, 6, 47], [19, 572, 6, 47, "f"], [19, 573, 6, 47], [19, 575, 6, 47, "_t"], [19, 577, 6, 47], [19, 579, 6, 47, "i"], [19, 580, 6, 47], [19, 584, 6, 47, "f"], [19, 585, 6, 47], [19, 586, 6, 47, "_t"], [19, 588, 6, 47], [19, 592, 6, 47, "e"], [19, 593, 6, 47], [19, 594, 6, 47, "_t"], [19, 596, 6, 47], [19, 607, 6, 47, "f"], [19, 608, 6, 47], [19, 613, 6, 47, "e"], [19, 614, 6, 47], [19, 616, 6, 47, "t"], [19, 617, 6, 47], [20, 2, 6, 47], [20, 11, 6, 47, "_callSuper"], [20, 22, 6, 47, "t"], [20, 23, 6, 47], [20, 25, 6, 47, "o"], [20, 26, 6, 47], [20, 28, 6, 47, "e"], [20, 29, 6, 47], [20, 40, 6, 47, "o"], [20, 41, 6, 47], [20, 48, 6, 47, "_getPrototypeOf2"], [20, 64, 6, 47], [20, 65, 6, 47, "default"], [20, 72, 6, 47], [20, 74, 6, 47, "o"], [20, 75, 6, 47], [20, 82, 6, 47, "_possibleConstructorReturn2"], [20, 109, 6, 47], [20, 110, 6, 47, "default"], [20, 117, 6, 47], [20, 119, 6, 47, "t"], [20, 120, 6, 47], [20, 122, 6, 47, "_isNativeReflectConstruct"], [20, 147, 6, 47], [20, 152, 6, 47, "Reflect"], [20, 159, 6, 47], [20, 160, 6, 47, "construct"], [20, 169, 6, 47], [20, 170, 6, 47, "o"], [20, 171, 6, 47], [20, 173, 6, 47, "e"], [20, 174, 6, 47], [20, 186, 6, 47, "_getPrototypeOf2"], [20, 202, 6, 47], [20, 203, 6, 47, "default"], [20, 210, 6, 47], [20, 212, 6, 47, "t"], [20, 213, 6, 47], [20, 215, 6, 47, "constructor"], [20, 226, 6, 47], [20, 230, 6, 47, "o"], [20, 231, 6, 47], [20, 232, 6, 47, "apply"], [20, 237, 6, 47], [20, 238, 6, 47, "t"], [20, 239, 6, 47], [20, 241, 6, 47, "e"], [20, 242, 6, 47], [21, 2, 6, 47], [21, 11, 6, 47, "_isNativeReflectConstruct"], [21, 37, 6, 47], [21, 51, 6, 47, "t"], [21, 52, 6, 47], [21, 56, 6, 47, "Boolean"], [21, 63, 6, 47], [21, 64, 6, 47, "prototype"], [21, 73, 6, 47], [21, 74, 6, 47, "valueOf"], [21, 81, 6, 47], [21, 82, 6, 47, "call"], [21, 86, 6, 47], [21, 87, 6, 47, "Reflect"], [21, 94, 6, 47], [21, 95, 6, 47, "construct"], [21, 104, 6, 47], [21, 105, 6, 47, "Boolean"], [21, 112, 6, 47], [21, 145, 6, 47, "t"], [21, 146, 6, 47], [21, 159, 6, 47, "_isNativeReflectConstruct"], [21, 184, 6, 47], [21, 196, 6, 47, "_isNativeReflectConstruct"], [21, 197, 6, 47], [21, 210, 6, 47, "t"], [21, 211, 6, 47], [22, 2, 15, 0], [23, 0, 16, 0], [24, 0, 17, 0], [25, 0, 18, 0], [26, 0, 19, 0], [27, 0, 20, 0], [28, 0, 21, 0], [29, 2, 22, 7], [29, 6, 22, 13, "TOUCHABLE_STATE"], [29, 21, 22, 28], [29, 24, 22, 28, "exports"], [29, 31, 22, 28], [29, 32, 22, 28, "TOUCHABLE_STATE"], [29, 47, 22, 28], [29, 50, 22, 31], [30, 4, 23, 2, "UNDETERMINED"], [30, 16, 23, 14], [30, 18, 23, 16], [30, 19, 23, 17], [31, 4, 24, 2, "BEGAN"], [31, 9, 24, 7], [31, 11, 24, 9], [31, 12, 24, 10], [32, 4, 25, 2, "MOVED_OUTSIDE"], [32, 17, 25, 15], [32, 19, 25, 17], [33, 2, 26, 0], [33, 3, 26, 10], [35, 2, 34, 0], [36, 2, 35, 0], [37, 2, 38, 0], [38, 0, 39, 0], [39, 0, 40, 0], [40, 0, 41, 0], [41, 2, 38, 0], [41, 6, 43, 21, "GenericTouchable"], [41, 22, 43, 37], [41, 25, 43, 37, "exports"], [41, 32, 43, 37], [41, 33, 43, 37, "default"], [41, 40, 43, 37], [41, 66, 43, 37, "_Component"], [41, 76, 43, 37], [42, 4, 43, 37], [42, 13, 43, 37, "GenericTouchable"], [42, 30, 43, 37], [43, 6, 43, 37], [43, 10, 43, 37, "_this"], [43, 15, 43, 37], [44, 6, 43, 37], [44, 10, 43, 37, "_classCallCheck2"], [44, 26, 43, 37], [44, 27, 43, 37, "default"], [44, 34, 43, 37], [44, 42, 43, 37, "GenericTouchable"], [44, 58, 43, 37], [45, 6, 43, 37], [45, 15, 43, 37, "_len"], [45, 19, 43, 37], [45, 22, 43, 37, "arguments"], [45, 31, 43, 37], [45, 32, 43, 37, "length"], [45, 38, 43, 37], [45, 40, 43, 37, "args"], [45, 44, 43, 37], [45, 51, 43, 37, "Array"], [45, 56, 43, 37], [45, 57, 43, 37, "_len"], [45, 61, 43, 37], [45, 64, 43, 37, "_key"], [45, 68, 43, 37], [45, 74, 43, 37, "_key"], [45, 78, 43, 37], [45, 81, 43, 37, "_len"], [45, 85, 43, 37], [45, 87, 43, 37, "_key"], [45, 91, 43, 37], [46, 8, 43, 37, "args"], [46, 12, 43, 37], [46, 13, 43, 37, "_key"], [46, 17, 43, 37], [46, 21, 43, 37, "arguments"], [46, 30, 43, 37], [46, 31, 43, 37, "_key"], [46, 35, 43, 37], [47, 6, 43, 37], [48, 6, 43, 37, "_this"], [48, 11, 43, 37], [48, 14, 43, 37, "_callSuper"], [48, 24, 43, 37], [48, 31, 43, 37, "GenericTouchable"], [48, 47, 43, 37], [48, 53, 43, 37, "args"], [48, 57, 43, 37], [49, 6, 59, 2], [50, 6, 59, 2, "_this"], [50, 11, 59, 2], [50, 12, 60, 2, "longPressDetected"], [50, 29, 60, 19], [50, 32, 60, 22], [50, 37, 60, 27], [51, 6, 60, 27, "_this"], [51, 11, 60, 27], [51, 12, 62, 2, "pointerInside"], [51, 25, 62, 15], [51, 28, 62, 18], [51, 32, 62, 22], [52, 6, 64, 2], [53, 6, 64, 2, "_this"], [53, 11, 64, 2], [53, 12, 65, 2, "STATE"], [53, 17, 65, 7], [53, 20, 65, 26, "TOUCHABLE_STATE"], [53, 35, 65, 41], [53, 36, 65, 42, "UNDETERMINED"], [53, 48, 65, 54], [54, 6, 65, 54, "_this"], [54, 11, 65, 54], [54, 12, 159, 2, "onGestureEvent"], [54, 26, 159, 16], [54, 29, 159, 19, "_ref"], [54, 33, 159, 19], [54, 37, 161, 55], [55, 8, 161, 55], [55, 12, 160, 19, "pointerInside"], [55, 25, 160, 32], [55, 28, 160, 32, "_ref"], [55, 32, 160, 32], [55, 33, 160, 4, "nativeEvent"], [55, 44, 160, 15], [55, 45, 160, 19, "pointerInside"], [55, 58, 160, 32], [56, 8, 162, 4], [56, 12, 162, 8, "_this"], [56, 17, 162, 8], [56, 18, 162, 13, "pointerInside"], [56, 31, 162, 26], [56, 36, 162, 31, "pointerInside"], [56, 49, 162, 44], [56, 51, 162, 46], [57, 10, 163, 6], [57, 14, 163, 10, "pointerInside"], [57, 27, 163, 23], [57, 29, 163, 25], [58, 12, 164, 8, "_this"], [58, 17, 164, 8], [58, 18, 164, 13, "onMoveIn"], [58, 26, 164, 21], [58, 27, 164, 22], [58, 28, 164, 23], [59, 10, 165, 6], [59, 11, 165, 7], [59, 17, 165, 13], [60, 12, 166, 8, "_this"], [60, 17, 166, 8], [60, 18, 166, 13, "onMoveOut"], [60, 27, 166, 22], [60, 28, 166, 23], [60, 29, 166, 24], [61, 10, 167, 6], [62, 8, 168, 4], [63, 8, 169, 4, "_this"], [63, 13, 169, 4], [63, 14, 169, 9, "pointerInside"], [63, 27, 169, 22], [63, 30, 169, 25, "pointerInside"], [63, 43, 169, 38], [64, 6, 170, 2], [64, 7, 170, 3], [65, 6, 170, 3, "_this"], [65, 11, 170, 3], [65, 12, 172, 2, "onHandlerStateChange"], [65, 32, 172, 22], [65, 35, 172, 25, "_ref2"], [65, 40, 172, 25], [65, 44, 174, 66], [66, 8, 174, 66], [66, 12, 173, 4, "nativeEvent"], [66, 23, 173, 15], [66, 26, 173, 15, "_ref2"], [66, 31, 173, 15], [66, 32, 173, 4, "nativeEvent"], [66, 43, 173, 15], [67, 8, 175, 4], [67, 12, 175, 12, "state"], [67, 17, 175, 17], [67, 20, 175, 22, "nativeEvent"], [67, 31, 175, 33], [67, 32, 175, 12, "state"], [67, 37, 175, 17], [68, 8, 176, 4], [68, 12, 176, 8, "state"], [68, 17, 176, 13], [68, 22, 176, 18, "State"], [68, 34, 176, 23], [68, 35, 176, 24, "CANCELLED"], [68, 44, 176, 33], [68, 48, 176, 37, "state"], [68, 53, 176, 42], [68, 58, 176, 47, "State"], [68, 70, 176, 52], [68, 71, 176, 53, "FAILED"], [68, 77, 176, 59], [68, 79, 176, 61], [69, 10, 177, 6], [70, 10, 178, 6, "_this"], [70, 15, 178, 6], [70, 16, 178, 11, "moveToState"], [70, 27, 178, 22], [70, 28, 178, 23, "TOUCHABLE_STATE"], [70, 43, 178, 38], [70, 44, 178, 39, "UNDETERMINED"], [70, 56, 178, 51], [70, 57, 178, 52], [71, 8, 179, 4], [71, 9, 179, 5], [71, 15, 179, 11], [72, 8, 180, 6], [73, 8, 181, 6], [74, 8, 182, 6], [75, 8, 183, 6, "state"], [75, 13, 183, 11], [75, 19, 183, 17, "Platform"], [75, 40, 183, 25], [75, 41, 183, 26, "OS"], [75, 43, 183, 28], [75, 48, 183, 33], [75, 57, 183, 42], [75, 60, 183, 45, "State"], [75, 72, 183, 50], [75, 73, 183, 51, "ACTIVE"], [75, 79, 183, 57], [75, 82, 183, 60, "State"], [75, 94, 183, 65], [75, 95, 183, 66, "BEGAN"], [75, 100, 183, 71], [75, 101, 183, 72], [75, 105, 184, 6, "_this"], [75, 110, 184, 6], [75, 111, 184, 11, "STATE"], [75, 116, 184, 16], [75, 121, 184, 21, "TOUCHABLE_STATE"], [75, 136, 184, 36], [75, 137, 184, 37, "UNDETERMINED"], [75, 149, 184, 49], [75, 151, 185, 6], [76, 10, 186, 6], [77, 10, 187, 6, "_this"], [77, 15, 187, 6], [77, 16, 187, 11, "handlePressIn"], [77, 29, 187, 24], [77, 30, 187, 25], [77, 31, 187, 26], [78, 8, 188, 4], [78, 9, 188, 5], [78, 15, 188, 11], [78, 19, 188, 15, "state"], [78, 24, 188, 20], [78, 29, 188, 25, "State"], [78, 41, 188, 30], [78, 42, 188, 31, "END"], [78, 45, 188, 34], [78, 47, 188, 36], [79, 10, 189, 6], [79, 14, 189, 12, "shouldCallOnPress"], [79, 31, 189, 29], [79, 34, 190, 8], [79, 35, 190, 9, "_this"], [79, 40, 190, 9], [79, 41, 190, 14, "longPressDetected"], [79, 58, 190, 31], [79, 62, 191, 8, "_this"], [79, 67, 191, 8], [79, 68, 191, 13, "STATE"], [79, 73, 191, 18], [79, 78, 191, 23, "TOUCHABLE_STATE"], [79, 93, 191, 38], [79, 94, 191, 39, "MOVED_OUTSIDE"], [79, 107, 191, 52], [79, 111, 192, 8, "_this"], [79, 116, 192, 8], [79, 117, 192, 13, "pressOutTimeout"], [79, 132, 192, 28], [79, 137, 192, 33], [79, 141, 192, 37], [80, 10, 193, 6, "_this"], [80, 15, 193, 6], [80, 16, 193, 11, "handleGoToUndetermined"], [80, 38, 193, 33], [80, 39, 193, 34], [80, 40, 193, 35], [81, 10, 194, 6], [81, 14, 194, 10, "shouldCallOnPress"], [81, 31, 194, 27], [81, 33, 194, 29], [82, 12, 195, 8], [83, 12, 196, 8, "_this"], [83, 17, 196, 8], [83, 18, 196, 13, "props"], [83, 23, 196, 18], [83, 24, 196, 19, "onPress"], [83, 31, 196, 26], [83, 34, 196, 29], [83, 35, 196, 30], [84, 10, 197, 6], [85, 8, 198, 4], [86, 6, 199, 2], [86, 7, 199, 3], [87, 6, 199, 3, "_this"], [87, 11, 199, 3], [87, 12, 201, 2, "onLongPressDetected"], [87, 31, 201, 21], [87, 34, 201, 24], [87, 40, 201, 30], [88, 8, 202, 4, "_this"], [88, 13, 202, 4], [88, 14, 202, 9, "longPressDetected"], [88, 31, 202, 26], [88, 34, 202, 29], [88, 38, 202, 33], [89, 8, 203, 4], [90, 8, 204, 4, "_this"], [90, 13, 204, 4], [90, 14, 204, 9, "props"], [90, 19, 204, 14], [90, 20, 204, 15, "onLongPress"], [90, 31, 204, 26], [90, 34, 204, 29], [90, 35, 204, 30], [91, 6, 205, 2], [91, 7, 205, 3], [92, 6, 205, 3], [92, 13, 205, 3, "_this"], [92, 18, 205, 3], [93, 4, 205, 3], [94, 4, 205, 3], [94, 8, 205, 3, "_inherits2"], [94, 18, 205, 3], [94, 19, 205, 3, "default"], [94, 26, 205, 3], [94, 28, 205, 3, "GenericTouchable"], [94, 44, 205, 3], [94, 46, 205, 3, "_Component"], [94, 56, 205, 3], [95, 4, 205, 3], [95, 15, 205, 3, "_createClass2"], [95, 28, 205, 3], [95, 29, 205, 3, "default"], [95, 36, 205, 3], [95, 38, 205, 3, "GenericTouchable"], [95, 54, 205, 3], [96, 6, 205, 3, "key"], [96, 9, 205, 3], [97, 6, 205, 3, "value"], [97, 11, 205, 3], [98, 6, 67, 2], [99, 6, 68, 2], [100, 6, 69, 2], [100, 15, 69, 2, "handlePressIn"], [100, 28, 69, 15, "handlePressIn"], [100, 29, 69, 15], [100, 31, 69, 18], [101, 8, 70, 4], [101, 12, 70, 8], [101, 16, 70, 12], [101, 17, 70, 13, "props"], [101, 22, 70, 18], [101, 23, 70, 19, "delayPressIn"], [101, 35, 70, 31], [101, 37, 70, 33], [102, 10, 71, 6], [102, 14, 71, 10], [102, 15, 71, 11, "pressInTimeout"], [102, 29, 71, 25], [102, 32, 71, 28, "setTimeout"], [102, 42, 71, 38], [102, 43, 71, 39], [102, 49, 71, 45], [103, 12, 72, 8], [103, 16, 72, 12], [103, 17, 72, 13, "moveToState"], [103, 28, 72, 24], [103, 29, 72, 25, "TOUCHABLE_STATE"], [103, 44, 72, 40], [103, 45, 72, 41, "BEGAN"], [103, 50, 72, 46], [103, 51, 72, 47], [104, 12, 73, 8], [104, 16, 73, 12], [104, 17, 73, 13, "pressInTimeout"], [104, 31, 73, 27], [104, 34, 73, 30], [104, 38, 73, 34], [105, 10, 74, 6], [105, 11, 74, 7], [105, 13, 74, 9], [105, 17, 74, 13], [105, 18, 74, 14, "props"], [105, 23, 74, 19], [105, 24, 74, 20, "delayPressIn"], [105, 36, 74, 32], [105, 37, 74, 33], [106, 8, 75, 4], [106, 9, 75, 5], [106, 15, 75, 11], [107, 10, 76, 6], [107, 14, 76, 10], [107, 15, 76, 11, "moveToState"], [107, 26, 76, 22], [107, 27, 76, 23, "TOUCHABLE_STATE"], [107, 42, 76, 38], [107, 43, 76, 39, "BEGAN"], [107, 48, 76, 44], [107, 49, 76, 45], [108, 8, 77, 4], [109, 8, 78, 4], [109, 12, 78, 8], [109, 16, 78, 12], [109, 17, 78, 13, "props"], [109, 22, 78, 18], [109, 23, 78, 19, "onLongPress"], [109, 34, 78, 30], [109, 36, 78, 32], [110, 10, 79, 6], [110, 14, 79, 12, "time"], [110, 18, 79, 16], [110, 21, 80, 8], [110, 22, 80, 9], [110, 26, 80, 13], [110, 27, 80, 14, "props"], [110, 32, 80, 19], [110, 33, 80, 20, "delayPressIn"], [110, 45, 80, 32], [110, 49, 80, 36], [110, 50, 80, 37], [110, 55, 80, 42], [110, 59, 80, 46], [110, 60, 80, 47, "props"], [110, 65, 80, 52], [110, 66, 80, 53, "delayLongPress"], [110, 80, 80, 67], [110, 84, 80, 71], [110, 85, 80, 72], [110, 86, 80, 73], [111, 10, 81, 6], [111, 14, 81, 10], [111, 15, 81, 11, "longPressTimeout"], [111, 31, 81, 27], [111, 34, 81, 30, "setTimeout"], [111, 44, 81, 40], [111, 45, 81, 41], [111, 49, 81, 45], [111, 50, 81, 46, "onLongPressDetected"], [111, 69, 81, 65], [111, 71, 81, 67, "time"], [111, 75, 81, 71], [111, 76, 81, 72], [112, 8, 82, 4], [113, 6, 83, 2], [114, 6, 84, 2], [115, 6, 85, 2], [116, 4, 85, 2], [117, 6, 85, 2, "key"], [117, 9, 85, 2], [118, 6, 85, 2, "value"], [118, 11, 85, 2], [118, 13, 86, 2], [118, 22, 86, 2, "handleMoveOutside"], [118, 39, 86, 19, "handleMoveOutside"], [118, 40, 86, 19], [118, 42, 86, 22], [119, 8, 87, 4], [119, 12, 87, 8], [119, 16, 87, 12], [119, 17, 87, 13, "props"], [119, 22, 87, 18], [119, 23, 87, 19, "delayPressOut"], [119, 36, 87, 32], [119, 38, 87, 34], [120, 10, 88, 6], [120, 14, 88, 10], [120, 15, 88, 11, "pressOutTimeout"], [120, 30, 88, 26], [120, 33, 89, 8], [120, 37, 89, 12], [120, 38, 89, 13, "pressOutTimeout"], [120, 53, 89, 28], [120, 57, 90, 8, "setTimeout"], [120, 67, 90, 18], [120, 68, 90, 19], [120, 74, 90, 25], [121, 12, 91, 10], [121, 16, 91, 14], [121, 17, 91, 15, "moveToState"], [121, 28, 91, 26], [121, 29, 91, 27, "TOUCHABLE_STATE"], [121, 44, 91, 42], [121, 45, 91, 43, "MOVED_OUTSIDE"], [121, 58, 91, 56], [121, 59, 91, 57], [122, 12, 92, 10], [122, 16, 92, 14], [122, 17, 92, 15, "pressOutTimeout"], [122, 32, 92, 30], [122, 35, 92, 33], [122, 39, 92, 37], [123, 10, 93, 8], [123, 11, 93, 9], [123, 13, 93, 11], [123, 17, 93, 15], [123, 18, 93, 16, "props"], [123, 23, 93, 21], [123, 24, 93, 22, "delayPressOut"], [123, 37, 93, 35], [123, 38, 93, 36], [124, 8, 94, 4], [124, 9, 94, 5], [124, 15, 94, 11], [125, 10, 95, 6], [125, 14, 95, 10], [125, 15, 95, 11, "moveToState"], [125, 26, 95, 22], [125, 27, 95, 23, "TOUCHABLE_STATE"], [125, 42, 95, 38], [125, 43, 95, 39, "MOVED_OUTSIDE"], [125, 56, 95, 52], [125, 57, 95, 53], [126, 8, 96, 4], [127, 6, 97, 2], [129, 6, 99, 2], [130, 4, 99, 2], [131, 6, 99, 2, "key"], [131, 9, 99, 2], [132, 6, 99, 2, "value"], [132, 11, 99, 2], [132, 13, 100, 2], [132, 22, 100, 2, "handleGoToUndetermined"], [132, 44, 100, 24, "handleGoToUndetermined"], [132, 45, 100, 24], [132, 47, 100, 27], [133, 8, 101, 4, "clearTimeout"], [133, 20, 101, 16], [133, 21, 101, 17], [133, 25, 101, 21], [133, 26, 101, 22, "pressOutTimeout"], [133, 41, 101, 38], [133, 42, 101, 39], [133, 43, 101, 40], [133, 44, 101, 41], [134, 8, 102, 4], [134, 12, 102, 8], [134, 16, 102, 12], [134, 17, 102, 13, "props"], [134, 22, 102, 18], [134, 23, 102, 19, "delayPressOut"], [134, 36, 102, 32], [134, 38, 102, 34], [135, 10, 103, 6], [135, 14, 103, 10], [135, 15, 103, 11, "pressOutTimeout"], [135, 30, 103, 26], [135, 33, 103, 29, "setTimeout"], [135, 43, 103, 39], [135, 44, 103, 40], [135, 50, 103, 46], [136, 12, 104, 8], [136, 16, 104, 12], [136, 20, 104, 16], [136, 21, 104, 17, "STATE"], [136, 26, 104, 22], [136, 31, 104, 27, "TOUCHABLE_STATE"], [136, 46, 104, 42], [136, 47, 104, 43, "UNDETERMINED"], [136, 59, 104, 55], [136, 61, 104, 57], [137, 14, 105, 10], [137, 18, 105, 14], [137, 19, 105, 15, "moveToState"], [137, 30, 105, 26], [137, 31, 105, 27, "TOUCHABLE_STATE"], [137, 46, 105, 42], [137, 47, 105, 43, "BEGAN"], [137, 52, 105, 48], [137, 53, 105, 49], [138, 12, 106, 8], [139, 12, 107, 8], [139, 16, 107, 12], [139, 17, 107, 13, "moveToState"], [139, 28, 107, 24], [139, 29, 107, 25, "TOUCHABLE_STATE"], [139, 44, 107, 40], [139, 45, 107, 41, "UNDETERMINED"], [139, 57, 107, 53], [139, 58, 107, 54], [140, 12, 108, 8], [140, 16, 108, 12], [140, 17, 108, 13, "pressOutTimeout"], [140, 32, 108, 28], [140, 35, 108, 31], [140, 39, 108, 35], [141, 10, 109, 6], [141, 11, 109, 7], [141, 13, 109, 9], [141, 17, 109, 13], [141, 18, 109, 14, "props"], [141, 23, 109, 19], [141, 24, 109, 20, "delayPressOut"], [141, 37, 109, 33], [141, 38, 109, 34], [142, 8, 110, 4], [142, 9, 110, 5], [142, 15, 110, 11], [143, 10, 111, 6], [143, 14, 111, 10], [143, 18, 111, 14], [143, 19, 111, 15, "STATE"], [143, 24, 111, 20], [143, 29, 111, 25, "TOUCHABLE_STATE"], [143, 44, 111, 40], [143, 45, 111, 41, "UNDETERMINED"], [143, 57, 111, 53], [143, 59, 111, 55], [144, 12, 112, 8], [144, 16, 112, 12], [144, 17, 112, 13, "moveToState"], [144, 28, 112, 24], [144, 29, 112, 25, "TOUCHABLE_STATE"], [144, 44, 112, 40], [144, 45, 112, 41, "BEGAN"], [144, 50, 112, 46], [144, 51, 112, 47], [145, 10, 113, 6], [146, 10, 114, 6], [146, 14, 114, 10], [146, 15, 114, 11, "moveToState"], [146, 26, 114, 22], [146, 27, 114, 23, "TOUCHABLE_STATE"], [146, 42, 114, 38], [146, 43, 114, 39, "UNDETERMINED"], [146, 55, 114, 51], [146, 56, 114, 52], [147, 8, 115, 4], [148, 6, 116, 2], [149, 4, 116, 3], [150, 6, 116, 3, "key"], [150, 9, 116, 3], [151, 6, 116, 3, "value"], [151, 11, 116, 3], [151, 13, 118, 2], [151, 22, 118, 2, "componentDidMount"], [151, 39, 118, 19, "componentDidMount"], [151, 40, 118, 19], [151, 42, 118, 22], [152, 8, 119, 4], [152, 12, 119, 8], [152, 13, 119, 9, "reset"], [152, 18, 119, 14], [152, 19, 119, 15], [152, 20, 119, 16], [153, 6, 120, 2], [154, 6, 121, 2], [155, 4, 121, 2], [156, 6, 121, 2, "key"], [156, 9, 121, 2], [157, 6, 121, 2, "value"], [157, 11, 121, 2], [157, 13, 122, 2], [157, 22, 122, 2, "reset"], [157, 27, 122, 7, "reset"], [157, 28, 122, 7], [157, 30, 122, 10], [158, 8, 123, 4], [158, 12, 123, 8], [158, 13, 123, 9, "longPressDetected"], [158, 30, 123, 26], [158, 33, 123, 29], [158, 38, 123, 34], [159, 8, 124, 4], [159, 12, 124, 8], [159, 13, 124, 9, "pointerInside"], [159, 26, 124, 22], [159, 29, 124, 25], [159, 33, 124, 29], [160, 8, 125, 4, "clearTimeout"], [160, 20, 125, 16], [160, 21, 125, 17], [160, 25, 125, 21], [160, 26, 125, 22, "pressInTimeout"], [160, 40, 125, 37], [160, 41, 125, 38], [161, 8, 126, 4, "clearTimeout"], [161, 20, 126, 16], [161, 21, 126, 17], [161, 25, 126, 21], [161, 26, 126, 22, "pressOutTimeout"], [161, 41, 126, 38], [161, 42, 126, 39], [162, 8, 127, 4, "clearTimeout"], [162, 20, 127, 16], [162, 21, 127, 17], [162, 25, 127, 21], [162, 26, 127, 22, "longPressTimeout"], [162, 42, 127, 39], [162, 43, 127, 40], [163, 8, 128, 4], [163, 12, 128, 8], [163, 13, 128, 9, "pressOutTimeout"], [163, 28, 128, 24], [163, 31, 128, 27], [163, 35, 128, 31], [164, 8, 129, 4], [164, 12, 129, 8], [164, 13, 129, 9, "longPressTimeout"], [164, 29, 129, 25], [164, 32, 129, 28], [164, 36, 129, 32], [165, 8, 130, 4], [165, 12, 130, 8], [165, 13, 130, 9, "pressInTimeout"], [165, 27, 130, 23], [165, 30, 130, 26], [165, 34, 130, 30], [166, 6, 131, 2], [168, 6, 133, 2], [169, 4, 133, 2], [170, 6, 133, 2, "key"], [170, 9, 133, 2], [171, 6, 133, 2, "value"], [171, 11, 133, 2], [171, 13, 134, 2], [171, 22, 134, 2, "moveToState"], [171, 33, 134, 13, "moveToState"], [171, 34, 134, 14, "newState"], [171, 42, 134, 38], [171, 44, 134, 40], [172, 8, 135, 4], [172, 12, 135, 8, "newState"], [172, 20, 135, 16], [172, 25, 135, 21], [172, 29, 135, 25], [172, 30, 135, 26, "STATE"], [172, 35, 135, 31], [172, 37, 135, 33], [173, 10, 136, 6], [174, 10, 137, 6], [175, 8, 138, 4], [176, 8, 139, 4], [176, 12, 139, 8, "newState"], [176, 20, 139, 16], [176, 25, 139, 21, "TOUCHABLE_STATE"], [176, 40, 139, 36], [176, 41, 139, 37, "BEGAN"], [176, 46, 139, 42], [176, 48, 139, 44], [177, 10, 140, 6], [178, 10, 141, 6], [178, 14, 141, 10], [178, 15, 141, 11, "props"], [178, 20, 141, 16], [178, 21, 141, 17, "onPressIn"], [178, 30, 141, 26], [178, 33, 141, 29], [178, 34, 141, 30], [179, 8, 142, 4], [179, 9, 142, 5], [179, 15, 142, 11], [179, 19, 142, 15, "newState"], [179, 27, 142, 23], [179, 32, 142, 28, "TOUCHABLE_STATE"], [179, 47, 142, 43], [179, 48, 142, 44, "MOVED_OUTSIDE"], [179, 61, 142, 57], [179, 63, 142, 59], [180, 10, 143, 6], [181, 10, 144, 6], [181, 14, 144, 10], [181, 15, 144, 11, "props"], [181, 20, 144, 16], [181, 21, 144, 17, "onPressOut"], [181, 31, 144, 27], [181, 34, 144, 30], [181, 35, 144, 31], [182, 8, 145, 4], [182, 9, 145, 5], [182, 15, 145, 11], [182, 19, 145, 15, "newState"], [182, 27, 145, 23], [182, 32, 145, 28, "TOUCHABLE_STATE"], [182, 47, 145, 43], [182, 48, 145, 44, "UNDETERMINED"], [182, 60, 145, 56], [182, 62, 145, 58], [183, 10, 146, 6], [184, 10, 147, 6], [184, 14, 147, 10], [184, 15, 147, 11, "reset"], [184, 20, 147, 16], [184, 21, 147, 17], [184, 22, 147, 18], [185, 10, 148, 6], [185, 14, 148, 10], [185, 18, 148, 14], [185, 19, 148, 15, "STATE"], [185, 24, 148, 20], [185, 29, 148, 25, "TOUCHABLE_STATE"], [185, 44, 148, 40], [185, 45, 148, 41, "BEGAN"], [185, 50, 148, 46], [185, 52, 148, 48], [186, 12, 149, 8], [187, 12, 150, 8], [187, 16, 150, 12], [187, 17, 150, 13, "props"], [187, 22, 150, 18], [187, 23, 150, 19, "onPressOut"], [187, 33, 150, 29], [187, 36, 150, 32], [187, 37, 150, 33], [188, 10, 151, 6], [189, 8, 152, 4], [190, 8, 153, 4], [191, 8, 154, 4], [191, 12, 154, 8], [191, 13, 154, 9, "props"], [191, 18, 154, 14], [191, 19, 154, 15, "onStateChange"], [191, 32, 154, 28], [191, 35, 154, 31], [191, 39, 154, 35], [191, 40, 154, 36, "STATE"], [191, 45, 154, 41], [191, 47, 154, 43, "newState"], [191, 55, 154, 51], [191, 56, 154, 52], [192, 8, 155, 4], [193, 8, 156, 4], [193, 12, 156, 8], [193, 13, 156, 9, "STATE"], [193, 18, 156, 14], [193, 21, 156, 17, "newState"], [193, 29, 156, 25], [194, 6, 157, 2], [195, 4, 157, 3], [196, 6, 157, 3, "key"], [196, 9, 157, 3], [197, 6, 157, 3, "value"], [197, 11, 157, 3], [197, 13, 207, 2], [197, 22, 207, 2, "componentWillUnmount"], [197, 42, 207, 22, "componentWillUnmount"], [197, 43, 207, 22], [197, 45, 207, 25], [198, 8, 208, 4], [199, 8, 209, 4], [199, 12, 209, 8], [199, 13, 209, 9, "reset"], [199, 18, 209, 14], [199, 19, 209, 15], [199, 20, 209, 16], [200, 6, 210, 2], [201, 4, 210, 3], [202, 6, 210, 3, "key"], [202, 9, 210, 3], [203, 6, 210, 3, "value"], [203, 11, 210, 3], [203, 13, 212, 2], [203, 22, 212, 2, "onMoveIn"], [203, 30, 212, 10, "onMoveIn"], [203, 31, 212, 10], [203, 33, 212, 13], [204, 8, 213, 4], [204, 12, 213, 8], [204, 16, 213, 12], [204, 17, 213, 13, "STATE"], [204, 22, 213, 18], [204, 27, 213, 23, "TOUCHABLE_STATE"], [204, 42, 213, 38], [204, 43, 213, 39, "MOVED_OUTSIDE"], [204, 56, 213, 52], [204, 58, 213, 54], [205, 10, 214, 6], [206, 10, 215, 6], [206, 14, 215, 10], [206, 15, 215, 11, "moveToState"], [206, 26, 215, 22], [206, 27, 215, 23, "TOUCHABLE_STATE"], [206, 42, 215, 38], [206, 43, 215, 39, "BEGAN"], [206, 48, 215, 44], [206, 49, 215, 45], [207, 8, 216, 4], [208, 6, 217, 2], [209, 4, 217, 3], [210, 6, 217, 3, "key"], [210, 9, 217, 3], [211, 6, 217, 3, "value"], [211, 11, 217, 3], [211, 13, 219, 2], [211, 22, 219, 2, "onMoveOut"], [211, 31, 219, 11, "onMoveOut"], [211, 32, 219, 11], [211, 34, 219, 14], [212, 8, 220, 4], [213, 8, 221, 4, "clearTimeout"], [213, 20, 221, 16], [213, 21, 221, 17], [213, 25, 221, 21], [213, 26, 221, 22, "longPressTimeout"], [213, 42, 221, 39], [213, 43, 221, 40], [214, 8, 222, 4], [214, 12, 222, 8], [214, 13, 222, 9, "longPressTimeout"], [214, 29, 222, 25], [214, 32, 222, 28], [214, 36, 222, 32], [215, 8, 223, 4], [215, 12, 223, 8], [215, 16, 223, 12], [215, 17, 223, 13, "STATE"], [215, 22, 223, 18], [215, 27, 223, 23, "TOUCHABLE_STATE"], [215, 42, 223, 38], [215, 43, 223, 39, "BEGAN"], [215, 48, 223, 44], [215, 50, 223, 46], [216, 10, 224, 6], [216, 14, 224, 10], [216, 15, 224, 11, "handleMoveOutside"], [216, 32, 224, 28], [216, 33, 224, 29], [216, 34, 224, 30], [217, 8, 225, 4], [218, 6, 226, 2], [219, 4, 226, 3], [220, 6, 226, 3, "key"], [220, 9, 226, 3], [221, 6, 226, 3, "value"], [221, 11, 226, 3], [221, 13, 228, 2], [221, 22, 228, 2, "render"], [221, 28, 228, 8, "render"], [221, 29, 228, 8], [221, 31, 228, 11], [222, 8, 229, 4], [222, 12, 229, 10, "hitSlop"], [222, 19, 229, 17], [222, 22, 230, 6], [222, 23, 230, 7], [222, 30, 230, 14], [222, 34, 230, 18], [222, 35, 230, 19, "props"], [222, 40, 230, 24], [222, 41, 230, 25, "hitSlop"], [222, 48, 230, 32], [222, 53, 230, 37], [222, 61, 230, 45], [222, 64, 231, 10], [223, 10, 232, 12, "top"], [223, 13, 232, 15], [223, 15, 232, 17], [223, 19, 232, 21], [223, 20, 232, 22, "props"], [223, 25, 232, 27], [223, 26, 232, 28, "hitSlop"], [223, 33, 232, 35], [224, 10, 233, 12, "left"], [224, 14, 233, 16], [224, 16, 233, 18], [224, 20, 233, 22], [224, 21, 233, 23, "props"], [224, 26, 233, 28], [224, 27, 233, 29, "hitSlop"], [224, 34, 233, 36], [225, 10, 234, 12, "bottom"], [225, 16, 234, 18], [225, 18, 234, 20], [225, 22, 234, 24], [225, 23, 234, 25, "props"], [225, 28, 234, 30], [225, 29, 234, 31, "hitSlop"], [225, 36, 234, 38], [226, 10, 235, 12, "right"], [226, 15, 235, 17], [226, 17, 235, 19], [226, 21, 235, 23], [226, 22, 235, 24, "props"], [226, 27, 235, 29], [226, 28, 235, 30, "hitSlop"], [227, 8, 236, 10], [227, 9, 236, 11], [227, 12, 237, 10], [227, 16, 237, 14], [227, 17, 237, 15, "props"], [227, 22, 237, 20], [227, 23, 237, 21, "hitSlop"], [227, 30, 237, 28], [227, 35, 237, 33, "undefined"], [227, 44, 237, 42], [228, 8, 239, 4], [228, 12, 239, 10, "coreProps"], [228, 21, 239, 19], [228, 24, 239, 22], [229, 10, 240, 6, "accessible"], [229, 20, 240, 16], [229, 22, 240, 18], [229, 26, 240, 22], [229, 27, 240, 23, "props"], [229, 32, 240, 28], [229, 33, 240, 29, "accessible"], [229, 43, 240, 39], [229, 48, 240, 44], [229, 53, 240, 49], [230, 10, 241, 6, "accessibilityLabel"], [230, 28, 241, 24], [230, 30, 241, 26], [230, 34, 241, 30], [230, 35, 241, 31, "props"], [230, 40, 241, 36], [230, 41, 241, 37, "accessibilityLabel"], [230, 59, 241, 55], [231, 10, 242, 6, "accessibilityHint"], [231, 27, 242, 23], [231, 29, 242, 25], [231, 33, 242, 29], [231, 34, 242, 30, "props"], [231, 39, 242, 35], [231, 40, 242, 36, "accessibilityHint"], [231, 57, 242, 53], [232, 10, 243, 6, "accessibilityRole"], [232, 27, 243, 23], [232, 29, 243, 25], [232, 33, 243, 29], [232, 34, 243, 30, "props"], [232, 39, 243, 35], [232, 40, 243, 36, "accessibilityRole"], [232, 57, 243, 53], [233, 10, 244, 6], [234, 10, 245, 6], [235, 10, 246, 6, "accessibilityState"], [235, 28, 246, 24], [235, 30, 246, 26], [235, 34, 246, 30], [235, 35, 246, 31, "props"], [235, 40, 246, 36], [235, 41, 246, 37, "accessibilityState"], [235, 59, 246, 55], [236, 10, 247, 6, "accessibilityActions"], [236, 30, 247, 26], [236, 32, 247, 28], [236, 36, 247, 32], [236, 37, 247, 33, "props"], [236, 42, 247, 38], [236, 43, 247, 39, "accessibilityActions"], [236, 63, 247, 59], [237, 10, 248, 6, "onAccessibilityAction"], [237, 31, 248, 27], [237, 33, 248, 29], [237, 37, 248, 33], [237, 38, 248, 34, "props"], [237, 43, 248, 39], [237, 44, 248, 40, "onAccessibilityAction"], [237, 65, 248, 61], [238, 10, 249, 6, "nativeID"], [238, 18, 249, 14], [238, 20, 249, 16], [238, 24, 249, 20], [238, 25, 249, 21, "props"], [238, 30, 249, 26], [238, 31, 249, 27, "nativeID"], [238, 39, 249, 35], [239, 10, 250, 6, "onLayout"], [239, 18, 250, 14], [239, 20, 250, 16], [239, 24, 250, 20], [239, 25, 250, 21, "props"], [239, 30, 250, 26], [239, 31, 250, 27, "onLayout"], [240, 8, 251, 4], [240, 9, 251, 5], [241, 8, 253, 4], [241, 28, 254, 6], [241, 32, 254, 6, "_jsxDevRuntime"], [241, 46, 254, 6], [241, 47, 254, 6, "jsxDEV"], [241, 53, 254, 6], [241, 55, 254, 7, "_GestureButtons"], [241, 70, 254, 7], [241, 71, 254, 7, "BaseButton"], [241, 81, 254, 17], [242, 10, 255, 8, "style"], [242, 15, 255, 13], [242, 17, 255, 15], [242, 21, 255, 19], [242, 22, 255, 20, "props"], [242, 27, 255, 25], [242, 28, 255, 26, "containerStyle"], [242, 42, 255, 41], [243, 10, 256, 8, "onHandlerStateChange"], [243, 30, 256, 28], [244, 10, 257, 10], [245, 10, 258, 10], [245, 14, 258, 14], [245, 15, 258, 15, "props"], [245, 20, 258, 20], [245, 21, 258, 21, "disabled"], [245, 29, 258, 29], [245, 32, 258, 32, "undefined"], [245, 41, 258, 41], [245, 44, 258, 44], [245, 48, 258, 48], [245, 49, 258, 49, "onHandlerStateChange"], [245, 69, 259, 9], [246, 10, 260, 8, "onGestureEvent"], [246, 24, 260, 22], [246, 26, 260, 24], [246, 30, 260, 28], [246, 31, 260, 29, "onGestureEvent"], [246, 45, 260, 44], [247, 10, 261, 8, "hitSlop"], [247, 17, 261, 15], [247, 19, 261, 17, "hitSlop"], [247, 26, 261, 25], [248, 10, 262, 8, "userSelect"], [248, 20, 262, 18], [248, 22, 262, 20], [248, 26, 262, 24], [248, 27, 262, 25, "props"], [248, 32, 262, 30], [248, 33, 262, 31, "userSelect"], [248, 43, 262, 42], [249, 10, 263, 8, "shouldActivateOnStart"], [249, 31, 263, 29], [249, 33, 263, 31], [249, 37, 263, 35], [249, 38, 263, 36, "props"], [249, 43, 263, 41], [249, 44, 263, 42, "shouldActivateOnStart"], [249, 65, 263, 64], [250, 10, 264, 8, "disallowInterruption"], [250, 30, 264, 28], [250, 32, 264, 30], [250, 36, 264, 34], [250, 37, 264, 35, "props"], [250, 42, 264, 40], [250, 43, 264, 41, "disallowInterruption"], [250, 63, 264, 62], [251, 10, 265, 8, "testID"], [251, 16, 265, 14], [251, 18, 265, 16], [251, 22, 265, 20], [251, 23, 265, 21, "props"], [251, 28, 265, 26], [251, 29, 265, 27, "testID"], [251, 35, 265, 34], [252, 10, 266, 8, "touchSoundDisabled"], [252, 28, 266, 26], [252, 30, 266, 28], [252, 34, 266, 32], [252, 35, 266, 33, "props"], [252, 40, 266, 38], [252, 41, 266, 39, "touchSoundDisabled"], [252, 59, 266, 57], [252, 63, 266, 61], [252, 68, 266, 67], [253, 10, 267, 8, "enabled"], [253, 17, 267, 15], [253, 19, 267, 17], [253, 20, 267, 18], [253, 24, 267, 22], [253, 25, 267, 23, "props"], [253, 30, 267, 28], [253, 31, 267, 29, "disabled"], [253, 39, 267, 38], [254, 10, 267, 38], [254, 13, 268, 12], [254, 17, 268, 16], [254, 18, 268, 17, "props"], [254, 23, 268, 22], [254, 24, 268, 23, "extraButtonProps"], [254, 40, 268, 39], [255, 10, 268, 39, "children"], [255, 18, 268, 39], [255, 33, 269, 8], [255, 37, 269, 8, "_jsxDevRuntime"], [255, 51, 269, 8], [255, 52, 269, 8, "jsxDEV"], [255, 58, 269, 8], [255, 60, 269, 9, "_reactNative"], [255, 72, 269, 9], [255, 73, 269, 9, "Animated"], [255, 81, 269, 17], [255, 82, 269, 18, "View"], [255, 86, 269, 22], [256, 12, 269, 22], [256, 15, 269, 27, "coreProps"], [256, 24, 269, 36], [257, 12, 269, 38, "style"], [257, 17, 269, 43], [257, 19, 269, 45], [257, 23, 269, 49], [257, 24, 269, 50, "props"], [257, 29, 269, 55], [257, 30, 269, 56, "style"], [257, 35, 269, 62], [258, 12, 269, 62, "children"], [258, 20, 269, 62], [258, 22, 270, 11], [258, 26, 270, 15], [258, 27, 270, 16, "props"], [258, 32, 270, 21], [258, 33, 270, 22, "children"], [259, 10, 270, 30], [260, 12, 270, 30, "fileName"], [260, 20, 270, 30], [260, 22, 270, 30, "_jsxFileName"], [260, 34, 270, 30], [261, 12, 270, 30, "lineNumber"], [261, 22, 270, 30], [262, 12, 270, 30, "columnNumber"], [262, 24, 270, 30], [263, 10, 270, 30], [263, 17, 271, 23], [264, 8, 271, 24], [265, 10, 271, 24, "fileName"], [265, 18, 271, 24], [265, 20, 271, 24, "_jsxFileName"], [265, 32, 271, 24], [266, 10, 271, 24, "lineNumber"], [266, 20, 271, 24], [267, 10, 271, 24, "columnNumber"], [267, 22, 271, 24], [268, 8, 271, 24], [268, 15, 272, 18], [268, 16, 272, 19], [269, 6, 274, 2], [270, 4, 274, 3], [271, 2, 274, 3], [271, 4, 43, 46, "Component"], [271, 20, 43, 55], [272, 2, 43, 21, "GenericTouchable"], [272, 18, 43, 37], [272, 19, 46, 9, "defaultProps"], [272, 31, 46, 21], [272, 34, 46, 24], [273, 4, 47, 4, "delayLongPress"], [273, 18, 47, 18], [273, 20, 47, 20], [273, 23, 47, 23], [274, 4, 48, 4, "extraButtonProps"], [274, 20, 48, 20], [274, 22, 48, 22], [275, 6, 49, 6, "rippleColor"], [275, 17, 49, 17], [275, 19, 49, 19], [275, 32, 49, 32], [276, 6, 50, 6, "exclusive"], [276, 15, 50, 15], [276, 17, 50, 17], [277, 4, 51, 4], [278, 2, 52, 2], [278, 3, 52, 3], [279, 0, 52, 3], [279, 3]], "functionMap": {"names": ["<global>", "GenericTouchable", "handlePressIn", "setTimeout$argument_0", "handleMoveOutside", "handleGoToUndetermined", "componentDidMount", "reset", "moveToState", "onGestureEvent", "onHandlerStateChange", "onLongPressDetected", "componentWillUnmount", "onMoveIn", "onMoveOut", "render"], "mappings": "AAA;eC0C;EC0B;uCCE;ODG;GDS;EGG;mBDI;SCG;GHI;EIG;wCFG;OEM;GJO;EKE;GLE;EME;GNS;EOG;GPuB;mBQE;GRW;yBSE;GT2B;wBUE;GVI;EWE;GXG;EYE;GZK;EaE;GbO;EcE;Gd8C;CDC"}}, "type": "js/module"}]}