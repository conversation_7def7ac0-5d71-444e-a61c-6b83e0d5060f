{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.roundToNearestPixel = exports.getPixelSizeForLayoutSize = exports.fontScale = exports.pixelRatio = exports.hairlineWidth = exports.platformColor = exports.platformSelect = void 0;\n  function create(name) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    var json = JSON.stringify({\n      name,\n      args\n    });\n    return `__${json}`;\n  }\n  var platformSelect = value => create(\"platformSelect\", value);\n  exports.platformSelect = platformSelect;\n  var platformColor = color => create(\"platformColor\", color);\n  exports.platformColor = platformColor;\n  var hairlineWidth = () => create(\"hairlineWidth\");\n  exports.hairlineWidth = hairlineWidth;\n  var pixelRatio = v => create(\"pixelRatio\", v);\n  exports.pixelRatio = pixelRatio;\n  var fontScale = v => create(\"fontScale\", v);\n  exports.fontScale = fontScale;\n  var getPixelSizeForLayoutSize = n => create(\"getPixelSizeForLayoutSize\", n);\n  exports.getPixelSizeForLayoutSize = getPixelSizeForLayoutSize;\n  var roundToNearestPixel = n => create(\"roundToNearestPixel\", n);\n  exports.roundToNearestPixel = roundToNearestPixel;\n});", "lineCount": 32, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "roundToNearestPixel"], [7, 29, 3, 27], [7, 32, 3, 30, "exports"], [7, 39, 3, 37], [7, 40, 3, 38, "getPixelSizeForLayoutSize"], [7, 65, 3, 63], [7, 68, 3, 66, "exports"], [7, 75, 3, 73], [7, 76, 3, 74, "fontScale"], [7, 85, 3, 83], [7, 88, 3, 86, "exports"], [7, 95, 3, 93], [7, 96, 3, 94, "pixelRatio"], [7, 106, 3, 104], [7, 109, 3, 107, "exports"], [7, 116, 3, 114], [7, 117, 3, 115, "hairlineWidth"], [7, 130, 3, 128], [7, 133, 3, 131, "exports"], [7, 140, 3, 138], [7, 141, 3, 139, "platformColor"], [7, 154, 3, 152], [7, 157, 3, 155, "exports"], [7, 164, 3, 162], [7, 165, 3, 163, "platformSelect"], [7, 179, 3, 177], [7, 182, 3, 180], [7, 187, 3, 185], [7, 188, 3, 186], [8, 2, 4, 0], [8, 11, 4, 9, "create"], [8, 17, 4, 15, "create"], [8, 18, 4, 16, "name"], [8, 22, 4, 20], [8, 24, 4, 31], [9, 4, 4, 31], [9, 13, 4, 31, "_len"], [9, 17, 4, 31], [9, 20, 4, 31, "arguments"], [9, 29, 4, 31], [9, 30, 4, 31, "length"], [9, 36, 4, 31], [9, 38, 4, 25, "args"], [9, 42, 4, 29], [9, 49, 4, 29, "Array"], [9, 54, 4, 29], [9, 55, 4, 29, "_len"], [9, 59, 4, 29], [9, 66, 4, 29, "_len"], [9, 70, 4, 29], [9, 81, 4, 29, "_key"], [9, 85, 4, 29], [9, 91, 4, 29, "_key"], [9, 95, 4, 29], [9, 98, 4, 29, "_len"], [9, 102, 4, 29], [9, 104, 4, 29, "_key"], [9, 108, 4, 29], [10, 6, 4, 25, "args"], [10, 10, 4, 29], [10, 11, 4, 29, "_key"], [10, 15, 4, 29], [10, 23, 4, 29, "arguments"], [10, 32, 4, 29], [10, 33, 4, 29, "_key"], [10, 37, 4, 29], [11, 4, 4, 29], [12, 4, 5, 4], [12, 8, 5, 10, "json"], [12, 12, 5, 14], [12, 15, 5, 17, "JSON"], [12, 19, 5, 21], [12, 20, 5, 22, "stringify"], [12, 29, 5, 31], [12, 30, 5, 32], [13, 6, 5, 34, "name"], [13, 10, 5, 38], [14, 6, 5, 40, "args"], [15, 4, 5, 45], [15, 5, 5, 46], [15, 6, 5, 47], [16, 4, 6, 4], [16, 11, 6, 11], [16, 16, 6, 16, "json"], [16, 20, 6, 20], [16, 22, 6, 22], [17, 2, 7, 0], [18, 2, 8, 0], [18, 6, 8, 6, "platformSelect"], [18, 20, 8, 20], [18, 23, 8, 24, "value"], [18, 28, 8, 29], [18, 32, 8, 34, "create"], [18, 38, 8, 40], [18, 39, 8, 41], [18, 55, 8, 57], [18, 57, 8, 59, "value"], [18, 62, 8, 64], [18, 63, 8, 65], [19, 2, 9, 0, "exports"], [19, 9, 9, 7], [19, 10, 9, 8, "platformSelect"], [19, 24, 9, 22], [19, 27, 9, 25, "platformSelect"], [19, 41, 9, 39], [20, 2, 10, 0], [20, 6, 10, 6, "platformColor"], [20, 19, 10, 19], [20, 22, 10, 23, "color"], [20, 27, 10, 28], [20, 31, 10, 33, "create"], [20, 37, 10, 39], [20, 38, 10, 40], [20, 53, 10, 55], [20, 55, 10, 57, "color"], [20, 60, 10, 62], [20, 61, 10, 63], [21, 2, 11, 0, "exports"], [21, 9, 11, 7], [21, 10, 11, 8, "platformColor"], [21, 23, 11, 21], [21, 26, 11, 24, "platformColor"], [21, 39, 11, 37], [22, 2, 12, 0], [22, 6, 12, 6, "hairlineWidth"], [22, 19, 12, 19], [22, 22, 12, 22, "hairlineWidth"], [22, 23, 12, 22], [22, 28, 12, 28, "create"], [22, 34, 12, 34], [22, 35, 12, 35], [22, 50, 12, 50], [22, 51, 12, 51], [23, 2, 13, 0, "exports"], [23, 9, 13, 7], [23, 10, 13, 8, "hairlineWidth"], [23, 23, 13, 21], [23, 26, 13, 24, "hairlineWidth"], [23, 39, 13, 37], [24, 2, 14, 0], [24, 6, 14, 6, "pixelRatio"], [24, 16, 14, 16], [24, 19, 14, 20, "v"], [24, 20, 14, 21], [24, 24, 14, 26, "create"], [24, 30, 14, 32], [24, 31, 14, 33], [24, 43, 14, 45], [24, 45, 14, 47, "v"], [24, 46, 14, 48], [24, 47, 14, 49], [25, 2, 15, 0, "exports"], [25, 9, 15, 7], [25, 10, 15, 8, "pixelRatio"], [25, 20, 15, 18], [25, 23, 15, 21, "pixelRatio"], [25, 33, 15, 31], [26, 2, 16, 0], [26, 6, 16, 6, "fontScale"], [26, 15, 16, 15], [26, 18, 16, 19, "v"], [26, 19, 16, 20], [26, 23, 16, 25, "create"], [26, 29, 16, 31], [26, 30, 16, 32], [26, 41, 16, 43], [26, 43, 16, 45, "v"], [26, 44, 16, 46], [26, 45, 16, 47], [27, 2, 17, 0, "exports"], [27, 9, 17, 7], [27, 10, 17, 8, "fontScale"], [27, 19, 17, 17], [27, 22, 17, 20, "fontScale"], [27, 31, 17, 29], [28, 2, 18, 0], [28, 6, 18, 6, "getPixelSizeForLayoutSize"], [28, 31, 18, 31], [28, 34, 18, 35, "n"], [28, 35, 18, 36], [28, 39, 18, 41, "create"], [28, 45, 18, 47], [28, 46, 18, 48], [28, 73, 18, 75], [28, 75, 18, 77, "n"], [28, 76, 18, 78], [28, 77, 18, 79], [29, 2, 19, 0, "exports"], [29, 9, 19, 7], [29, 10, 19, 8, "getPixelSizeForLayoutSize"], [29, 35, 19, 33], [29, 38, 19, 36, "getPixelSizeForLayoutSize"], [29, 63, 19, 61], [30, 2, 20, 0], [30, 6, 20, 6, "roundToNearestPixel"], [30, 25, 20, 25], [30, 28, 20, 29, "n"], [30, 29, 20, 30], [30, 33, 20, 35, "create"], [30, 39, 20, 41], [30, 40, 20, 42], [30, 61, 20, 63], [30, 63, 20, 65, "n"], [30, 64, 20, 66], [30, 65, 20, 67], [31, 2, 21, 0, "exports"], [31, 9, 21, 7], [31, 10, 21, 8, "roundToNearestPixel"], [31, 29, 21, 27], [31, 32, 21, 30, "roundToNearestPixel"], [31, 51, 21, 49], [32, 0, 21, 50], [32, 3]], "functionMap": {"names": ["<global>", "create", "platformSelect", "platformColor", "hairlineWidth", "pixelRatio", "fontScale", "getPixelSizeForLayoutSize", "roundToNearestPixel"], "mappings": "AAA;ACG;CDG;uBEC,0CF;sBGE,yCH;sBIE,6BJ;mBKE,8BL;kBME,6BN;kCOE,6CP;4BQE,uCR"}}, "type": "js/module"}]}