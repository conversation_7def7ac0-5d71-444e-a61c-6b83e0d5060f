{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 0, "index": 186}, "end": {"line": 6, "column": 79, "index": 265}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var nativeComponentName = 'RNGestureHandlerRootView';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNGestureHandlerRootView\",\n    validAttributes: {}\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 15, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 6, 0], [8, 6, 6, 0, "NativeComponentRegistry"], [8, 29, 6, 79], [8, 32, 6, 0, "require"], [8, 39, 6, 79], [8, 40, 6, 79, "_dependencyMap"], [8, 54, 6, 79], [8, 123, 6, 78], [8, 124, 6, 79], [9, 2, 6, 0], [9, 6, 6, 0, "nativeComponentName"], [9, 25, 6, 79], [9, 28, 6, 0], [9, 54, 6, 79], [10, 2, 6, 0], [10, 6, 6, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 6, 79], [10, 31, 6, 79, "exports"], [10, 38, 6, 79], [10, 39, 6, 79, "__INTERNAL_VIEW_CONFIG"], [10, 61, 6, 79], [10, 64, 6, 0], [11, 4, 6, 0, "uiViewClassName"], [11, 19, 6, 79], [11, 21, 6, 0], [11, 47, 6, 79], [12, 4, 6, 0, "validAttributes"], [12, 19, 6, 79], [12, 21, 6, 0], [12, 22, 6, 78], [13, 2, 6, 78], [13, 3, 6, 79], [14, 2, 6, 79], [14, 6, 6, 79, "_default"], [14, 14, 6, 79], [14, 17, 6, 79, "exports"], [14, 24, 6, 79], [14, 25, 6, 79, "default"], [14, 32, 6, 79], [14, 35, 6, 0, "NativeComponentRegistry"], [14, 58, 6, 79], [14, 59, 6, 0, "get"], [14, 62, 6, 79], [14, 63, 6, 0, "nativeComponentName"], [14, 82, 6, 79], [14, 84, 6, 0], [14, 90, 6, 0, "__INTERNAL_VIEW_CONFIG"], [14, 112, 6, 78], [14, 113, 6, 79], [15, 0, 6, 79], [15, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}