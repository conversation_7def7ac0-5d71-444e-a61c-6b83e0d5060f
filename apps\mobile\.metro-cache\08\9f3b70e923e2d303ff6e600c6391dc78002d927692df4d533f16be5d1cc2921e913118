{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "use-latest-callback", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 52, "index": 99}}], "key": "2ER/r3Agt+5SFwaFR8HXg24Rpu4=", "exportNames": ["*"]}}, {"name": "./deepFreeze.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 100}, "end": {"line": 5, "column": 45, "index": 145}}], "key": "rNxrvQaXjK4/vUvPksJpbMJ5u+4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useSyncState = useSyncState;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _useLatestCallback = _interopRequireDefault(require(_dependencyMap[2], \"use-latest-callback\"));\n  var _deepFreeze = require(_dependencyMap[3], \"./deepFreeze.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var createStore = getInitialState => {\n    var listeners = [];\n    var initialized = false;\n    var state;\n    var getState = () => {\n      if (initialized) {\n        return state;\n      }\n      initialized = true;\n      state = (0, _deepFreeze.deepFreeze)(getInitialState());\n      return state;\n    };\n    var isBatching = false;\n    var didUpdate = false;\n    var setState = newState => {\n      state = (0, _deepFreeze.deepFreeze)(newState);\n      didUpdate = true;\n      if (!isBatching) {\n        listeners.forEach(listener => listener());\n      }\n    };\n    var subscribe = callback => {\n      listeners.push(callback);\n      return () => {\n        var index = listeners.indexOf(callback);\n        if (index > -1) {\n          listeners.splice(index, 1);\n        }\n      };\n    };\n    var batchUpdates = callback => {\n      isBatching = true;\n      callback();\n      isBatching = false;\n      if (didUpdate) {\n        didUpdate = false;\n        listeners.forEach(listener => listener());\n      }\n    };\n    return {\n      getState,\n      setState,\n      batchUpdates,\n      subscribe\n    };\n  };\n  function useSyncState(getInitialState) {\n    var store = React.useRef(createStore(getInitialState)).current;\n    var state = React.useSyncExternalStore(store.subscribe, store.getState, store.getState);\n    React.useDebugValue(state);\n    var pendingUpdatesRef = React.useRef([]);\n    var scheduleUpdate = (0, _useLatestCallback.default)(callback => {\n      pendingUpdatesRef.current.push(callback);\n    });\n    var flushUpdates = (0, _useLatestCallback.default)(() => {\n      var pendingUpdates = pendingUpdatesRef.current;\n      pendingUpdatesRef.current = [];\n      if (pendingUpdates.length !== 0) {\n        store.batchUpdates(() => {\n          // Flush all the pending updates\n          for (var update of pendingUpdates) {\n            update();\n          }\n        });\n      }\n    });\n    return {\n      state,\n      getState: store.getState,\n      setState: store.setState,\n      scheduleUpdate,\n      flushUpdates\n    };\n  }\n});", "lineCount": 87, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "useSyncState"], [8, 22, 1, 13], [8, 25, 1, 13, "useSyncState"], [8, 37, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "React"], [9, 11, 3, 0], [9, 14, 3, 0, "_interopRequireWildcard"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_useLatestCallback"], [10, 24, 4, 0], [10, 27, 4, 0, "_interopRequireDefault"], [10, 49, 4, 0], [10, 50, 4, 0, "require"], [10, 57, 4, 0], [10, 58, 4, 0, "_dependencyMap"], [10, 72, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_deepFreeze"], [11, 17, 5, 0], [11, 20, 5, 0, "require"], [11, 27, 5, 0], [11, 28, 5, 0, "_dependencyMap"], [11, 42, 5, 0], [12, 2, 5, 45], [12, 11, 5, 45, "_interopRequireWildcard"], [12, 35, 5, 45, "e"], [12, 36, 5, 45], [12, 38, 5, 45, "t"], [12, 39, 5, 45], [12, 68, 5, 45, "WeakMap"], [12, 75, 5, 45], [12, 81, 5, 45, "r"], [12, 82, 5, 45], [12, 89, 5, 45, "WeakMap"], [12, 96, 5, 45], [12, 100, 5, 45, "n"], [12, 101, 5, 45], [12, 108, 5, 45, "WeakMap"], [12, 115, 5, 45], [12, 127, 5, 45, "_interopRequireWildcard"], [12, 150, 5, 45], [12, 162, 5, 45, "_interopRequireWildcard"], [12, 163, 5, 45, "e"], [12, 164, 5, 45], [12, 166, 5, 45, "t"], [12, 167, 5, 45], [12, 176, 5, 45, "t"], [12, 177, 5, 45], [12, 181, 5, 45, "e"], [12, 182, 5, 45], [12, 186, 5, 45, "e"], [12, 187, 5, 45], [12, 188, 5, 45, "__esModule"], [12, 198, 5, 45], [12, 207, 5, 45, "e"], [12, 208, 5, 45], [12, 214, 5, 45, "o"], [12, 215, 5, 45], [12, 217, 5, 45, "i"], [12, 218, 5, 45], [12, 220, 5, 45, "f"], [12, 221, 5, 45], [12, 226, 5, 45, "__proto__"], [12, 235, 5, 45], [12, 243, 5, 45, "default"], [12, 250, 5, 45], [12, 252, 5, 45, "e"], [12, 253, 5, 45], [12, 270, 5, 45, "e"], [12, 271, 5, 45], [12, 294, 5, 45, "e"], [12, 295, 5, 45], [12, 320, 5, 45, "e"], [12, 321, 5, 45], [12, 330, 5, 45, "f"], [12, 331, 5, 45], [12, 337, 5, 45, "o"], [12, 338, 5, 45], [12, 341, 5, 45, "t"], [12, 342, 5, 45], [12, 345, 5, 45, "n"], [12, 346, 5, 45], [12, 349, 5, 45, "r"], [12, 350, 5, 45], [12, 358, 5, 45, "o"], [12, 359, 5, 45], [12, 360, 5, 45, "has"], [12, 363, 5, 45], [12, 364, 5, 45, "e"], [12, 365, 5, 45], [12, 375, 5, 45, "o"], [12, 376, 5, 45], [12, 377, 5, 45, "get"], [12, 380, 5, 45], [12, 381, 5, 45, "e"], [12, 382, 5, 45], [12, 385, 5, 45, "o"], [12, 386, 5, 45], [12, 387, 5, 45, "set"], [12, 390, 5, 45], [12, 391, 5, 45, "e"], [12, 392, 5, 45], [12, 394, 5, 45, "f"], [12, 395, 5, 45], [12, 409, 5, 45, "_t"], [12, 411, 5, 45], [12, 415, 5, 45, "e"], [12, 416, 5, 45], [12, 432, 5, 45, "_t"], [12, 434, 5, 45], [12, 441, 5, 45, "hasOwnProperty"], [12, 455, 5, 45], [12, 456, 5, 45, "call"], [12, 460, 5, 45], [12, 461, 5, 45, "e"], [12, 462, 5, 45], [12, 464, 5, 45, "_t"], [12, 466, 5, 45], [12, 473, 5, 45, "i"], [12, 474, 5, 45], [12, 478, 5, 45, "o"], [12, 479, 5, 45], [12, 482, 5, 45, "Object"], [12, 488, 5, 45], [12, 489, 5, 45, "defineProperty"], [12, 503, 5, 45], [12, 508, 5, 45, "Object"], [12, 514, 5, 45], [12, 515, 5, 45, "getOwnPropertyDescriptor"], [12, 539, 5, 45], [12, 540, 5, 45, "e"], [12, 541, 5, 45], [12, 543, 5, 45, "_t"], [12, 545, 5, 45], [12, 552, 5, 45, "i"], [12, 553, 5, 45], [12, 554, 5, 45, "get"], [12, 557, 5, 45], [12, 561, 5, 45, "i"], [12, 562, 5, 45], [12, 563, 5, 45, "set"], [12, 566, 5, 45], [12, 570, 5, 45, "o"], [12, 571, 5, 45], [12, 572, 5, 45, "f"], [12, 573, 5, 45], [12, 575, 5, 45, "_t"], [12, 577, 5, 45], [12, 579, 5, 45, "i"], [12, 580, 5, 45], [12, 584, 5, 45, "f"], [12, 585, 5, 45], [12, 586, 5, 45, "_t"], [12, 588, 5, 45], [12, 592, 5, 45, "e"], [12, 593, 5, 45], [12, 594, 5, 45, "_t"], [12, 596, 5, 45], [12, 607, 5, 45, "f"], [12, 608, 5, 45], [12, 613, 5, 45, "e"], [12, 614, 5, 45], [12, 616, 5, 45, "t"], [12, 617, 5, 45], [13, 2, 6, 0], [13, 6, 6, 6, "createStore"], [13, 17, 6, 17], [13, 20, 6, 20, "getInitialState"], [13, 35, 6, 35], [13, 39, 6, 39], [14, 4, 7, 2], [14, 8, 7, 8, "listeners"], [14, 17, 7, 17], [14, 20, 7, 20], [14, 22, 7, 22], [15, 4, 8, 2], [15, 8, 8, 6, "initialized"], [15, 19, 8, 17], [15, 22, 8, 20], [15, 27, 8, 25], [16, 4, 9, 2], [16, 8, 9, 6, "state"], [16, 13, 9, 11], [17, 4, 10, 2], [17, 8, 10, 8, "getState"], [17, 16, 10, 16], [17, 19, 10, 19, "getState"], [17, 20, 10, 19], [17, 25, 10, 25], [18, 6, 11, 4], [18, 10, 11, 8, "initialized"], [18, 21, 11, 19], [18, 23, 11, 21], [19, 8, 12, 6], [19, 15, 12, 13, "state"], [19, 20, 12, 18], [20, 6, 13, 4], [21, 6, 14, 4, "initialized"], [21, 17, 14, 15], [21, 20, 14, 18], [21, 24, 14, 22], [22, 6, 15, 4, "state"], [22, 11, 15, 9], [22, 14, 15, 12], [22, 18, 15, 12, "deepFreeze"], [22, 40, 15, 22], [22, 42, 15, 23, "getInitialState"], [22, 57, 15, 38], [22, 58, 15, 39], [22, 59, 15, 40], [22, 60, 15, 41], [23, 6, 16, 4], [23, 13, 16, 11, "state"], [23, 18, 16, 16], [24, 4, 17, 2], [24, 5, 17, 3], [25, 4, 18, 2], [25, 8, 18, 6, "isBatching"], [25, 18, 18, 16], [25, 21, 18, 19], [25, 26, 18, 24], [26, 4, 19, 2], [26, 8, 19, 6, "didUpdate"], [26, 17, 19, 15], [26, 20, 19, 18], [26, 25, 19, 23], [27, 4, 20, 2], [27, 8, 20, 8, "setState"], [27, 16, 20, 16], [27, 19, 20, 19, "newState"], [27, 27, 20, 27], [27, 31, 20, 31], [28, 6, 21, 4, "state"], [28, 11, 21, 9], [28, 14, 21, 12], [28, 18, 21, 12, "deepFreeze"], [28, 40, 21, 22], [28, 42, 21, 23, "newState"], [28, 50, 21, 31], [28, 51, 21, 32], [29, 6, 22, 4, "didUpdate"], [29, 15, 22, 13], [29, 18, 22, 16], [29, 22, 22, 20], [30, 6, 23, 4], [30, 10, 23, 8], [30, 11, 23, 9, "isBatching"], [30, 21, 23, 19], [30, 23, 23, 21], [31, 8, 24, 6, "listeners"], [31, 17, 24, 15], [31, 18, 24, 16, "for<PERSON>ach"], [31, 25, 24, 23], [31, 26, 24, 24, "listener"], [31, 34, 24, 32], [31, 38, 24, 36, "listener"], [31, 46, 24, 44], [31, 47, 24, 45], [31, 48, 24, 46], [31, 49, 24, 47], [32, 6, 25, 4], [33, 4, 26, 2], [33, 5, 26, 3], [34, 4, 27, 2], [34, 8, 27, 8, "subscribe"], [34, 17, 27, 17], [34, 20, 27, 20, "callback"], [34, 28, 27, 28], [34, 32, 27, 32], [35, 6, 28, 4, "listeners"], [35, 15, 28, 13], [35, 16, 28, 14, "push"], [35, 20, 28, 18], [35, 21, 28, 19, "callback"], [35, 29, 28, 27], [35, 30, 28, 28], [36, 6, 29, 4], [36, 13, 29, 11], [36, 19, 29, 17], [37, 8, 30, 6], [37, 12, 30, 12, "index"], [37, 17, 30, 17], [37, 20, 30, 20, "listeners"], [37, 29, 30, 29], [37, 30, 30, 30, "indexOf"], [37, 37, 30, 37], [37, 38, 30, 38, "callback"], [37, 46, 30, 46], [37, 47, 30, 47], [38, 8, 31, 6], [38, 12, 31, 10, "index"], [38, 17, 31, 15], [38, 20, 31, 18], [38, 21, 31, 19], [38, 22, 31, 20], [38, 24, 31, 22], [39, 10, 32, 8, "listeners"], [39, 19, 32, 17], [39, 20, 32, 18, "splice"], [39, 26, 32, 24], [39, 27, 32, 25, "index"], [39, 32, 32, 30], [39, 34, 32, 32], [39, 35, 32, 33], [39, 36, 32, 34], [40, 8, 33, 6], [41, 6, 34, 4], [41, 7, 34, 5], [42, 4, 35, 2], [42, 5, 35, 3], [43, 4, 36, 2], [43, 8, 36, 8, "batchUpdates"], [43, 20, 36, 20], [43, 23, 36, 23, "callback"], [43, 31, 36, 31], [43, 35, 36, 35], [44, 6, 37, 4, "isBatching"], [44, 16, 37, 14], [44, 19, 37, 17], [44, 23, 37, 21], [45, 6, 38, 4, "callback"], [45, 14, 38, 12], [45, 15, 38, 13], [45, 16, 38, 14], [46, 6, 39, 4, "isBatching"], [46, 16, 39, 14], [46, 19, 39, 17], [46, 24, 39, 22], [47, 6, 40, 4], [47, 10, 40, 8, "didUpdate"], [47, 19, 40, 17], [47, 21, 40, 19], [48, 8, 41, 6, "didUpdate"], [48, 17, 41, 15], [48, 20, 41, 18], [48, 25, 41, 23], [49, 8, 42, 6, "listeners"], [49, 17, 42, 15], [49, 18, 42, 16, "for<PERSON>ach"], [49, 25, 42, 23], [49, 26, 42, 24, "listener"], [49, 34, 42, 32], [49, 38, 42, 36, "listener"], [49, 46, 42, 44], [49, 47, 42, 45], [49, 48, 42, 46], [49, 49, 42, 47], [50, 6, 43, 4], [51, 4, 44, 2], [51, 5, 44, 3], [52, 4, 45, 2], [52, 11, 45, 9], [53, 6, 46, 4, "getState"], [53, 14, 46, 12], [54, 6, 47, 4, "setState"], [54, 14, 47, 12], [55, 6, 48, 4, "batchUpdates"], [55, 18, 48, 16], [56, 6, 49, 4, "subscribe"], [57, 4, 50, 2], [57, 5, 50, 3], [58, 2, 51, 0], [58, 3, 51, 1], [59, 2, 52, 7], [59, 11, 52, 16, "useSyncState"], [59, 23, 52, 28, "useSyncState"], [59, 24, 52, 29, "getInitialState"], [59, 39, 52, 44], [59, 41, 52, 46], [60, 4, 53, 2], [60, 8, 53, 8, "store"], [60, 13, 53, 13], [60, 16, 53, 16, "React"], [60, 21, 53, 21], [60, 22, 53, 22, "useRef"], [60, 28, 53, 28], [60, 29, 53, 29, "createStore"], [60, 40, 53, 40], [60, 41, 53, 41, "getInitialState"], [60, 56, 53, 56], [60, 57, 53, 57], [60, 58, 53, 58], [60, 59, 53, 59, "current"], [60, 66, 53, 66], [61, 4, 54, 2], [61, 8, 54, 8, "state"], [61, 13, 54, 13], [61, 16, 54, 16, "React"], [61, 21, 54, 21], [61, 22, 54, 22, "useSyncExternalStore"], [61, 42, 54, 42], [61, 43, 54, 43, "store"], [61, 48, 54, 48], [61, 49, 54, 49, "subscribe"], [61, 58, 54, 58], [61, 60, 54, 60, "store"], [61, 65, 54, 65], [61, 66, 54, 66, "getState"], [61, 74, 54, 74], [61, 76, 54, 76, "store"], [61, 81, 54, 81], [61, 82, 54, 82, "getState"], [61, 90, 54, 90], [61, 91, 54, 91], [62, 4, 55, 2, "React"], [62, 9, 55, 7], [62, 10, 55, 8, "useDebugValue"], [62, 23, 55, 21], [62, 24, 55, 22, "state"], [62, 29, 55, 27], [62, 30, 55, 28], [63, 4, 56, 2], [63, 8, 56, 8, "pendingUpdatesRef"], [63, 25, 56, 25], [63, 28, 56, 28, "React"], [63, 33, 56, 33], [63, 34, 56, 34, "useRef"], [63, 40, 56, 40], [63, 41, 56, 41], [63, 43, 56, 43], [63, 44, 56, 44], [64, 4, 57, 2], [64, 8, 57, 8, "scheduleUpdate"], [64, 22, 57, 22], [64, 25, 57, 25], [64, 29, 57, 25, "useLatestCallback"], [64, 55, 57, 42], [64, 57, 57, 43, "callback"], [64, 65, 57, 51], [64, 69, 57, 55], [65, 6, 58, 4, "pendingUpdatesRef"], [65, 23, 58, 21], [65, 24, 58, 22, "current"], [65, 31, 58, 29], [65, 32, 58, 30, "push"], [65, 36, 58, 34], [65, 37, 58, 35, "callback"], [65, 45, 58, 43], [65, 46, 58, 44], [66, 4, 59, 2], [66, 5, 59, 3], [66, 6, 59, 4], [67, 4, 60, 2], [67, 8, 60, 8, "flushUpdates"], [67, 20, 60, 20], [67, 23, 60, 23], [67, 27, 60, 23, "useLatestCallback"], [67, 53, 60, 40], [67, 55, 60, 41], [67, 61, 60, 47], [68, 6, 61, 4], [68, 10, 61, 10, "pendingUpdates"], [68, 24, 61, 24], [68, 27, 61, 27, "pendingUpdatesRef"], [68, 44, 61, 44], [68, 45, 61, 45, "current"], [68, 52, 61, 52], [69, 6, 62, 4, "pendingUpdatesRef"], [69, 23, 62, 21], [69, 24, 62, 22, "current"], [69, 31, 62, 29], [69, 34, 62, 32], [69, 36, 62, 34], [70, 6, 63, 4], [70, 10, 63, 8, "pendingUpdates"], [70, 24, 63, 22], [70, 25, 63, 23, "length"], [70, 31, 63, 29], [70, 36, 63, 34], [70, 37, 63, 35], [70, 39, 63, 37], [71, 8, 64, 6, "store"], [71, 13, 64, 11], [71, 14, 64, 12, "batchUpdates"], [71, 26, 64, 24], [71, 27, 64, 25], [71, 33, 64, 31], [72, 10, 65, 8], [73, 10, 66, 8], [73, 15, 66, 13], [73, 19, 66, 19, "update"], [73, 25, 66, 25], [73, 29, 66, 29, "pendingUpdates"], [73, 43, 66, 43], [73, 45, 66, 45], [74, 12, 67, 10, "update"], [74, 18, 67, 16], [74, 19, 67, 17], [74, 20, 67, 18], [75, 10, 68, 8], [76, 8, 69, 6], [76, 9, 69, 7], [76, 10, 69, 8], [77, 6, 70, 4], [78, 4, 71, 2], [78, 5, 71, 3], [78, 6, 71, 4], [79, 4, 72, 2], [79, 11, 72, 9], [80, 6, 73, 4, "state"], [80, 11, 73, 9], [81, 6, 74, 4, "getState"], [81, 14, 74, 12], [81, 16, 74, 14, "store"], [81, 21, 74, 19], [81, 22, 74, 20, "getState"], [81, 30, 74, 28], [82, 6, 75, 4, "setState"], [82, 14, 75, 12], [82, 16, 75, 14, "store"], [82, 21, 75, 19], [82, 22, 75, 20, "setState"], [82, 30, 75, 28], [83, 6, 76, 4, "scheduleUpdate"], [83, 20, 76, 18], [84, 6, 77, 4, "flushUpdates"], [85, 4, 78, 2], [85, 5, 78, 3], [86, 2, 79, 0], [87, 0, 79, 1], [87, 3]], "functionMap": {"names": ["<global>", "createStore", "getState", "setState", "listeners.forEach$argument_0", "subscribe", "<anonymous>", "batchUpdates", "useSyncState", "useLatestCallback$argument_0", "store.batchUpdates$argument_0"], "mappings": "AAA;oBCK;mBCI;GDO;mBEG;wBCI,sBD;GFE;oBIC;WCE;KDK;GJC;uBMC;wBHM,sBG;GNE;CDO;OQC;2CCK;GDE;yCCC;yBCI;ODK;GDE;CRQ"}}, "type": "js/module"}]}